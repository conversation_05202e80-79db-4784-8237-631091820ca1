apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-koala-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-koala-dev
  template:
    metadata:
      labels:
        app: ldc-navigator-koala-dev
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-koala-dev:#{Build.BuildId}#
        name: ldc-navigator-koala-dev
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "test" 
---
apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-koala-dev
  namespace: dev
spec:
  type: ClusterIP
  ports:
  - port: 9120
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-koala-dev