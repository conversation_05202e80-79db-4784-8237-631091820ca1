spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        server-addr: http://192.168.0.241:8848
        namespace: navigator_cloud_dev
      config:
        server-addr: http://192.168.0.241:8848
        file-extension: yaml
        namespace: navigator_cloud_dev
        group: SERVICE_GROUP
        shared-configs:
          - data-id: navigator-commons.yaml
            group: DEFAULT_GROUP
            refresh: true
