package com.navigator.koala.domain.model;

import com.navigator.koala.pojo.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 */
@Data
@Accessors(chain = true)
public class WarrantDO {

    /**
     * 仓单主表
     */
    WarrantEntity warrantEntity;

    /**
     * 仓单修改表
     */
    WarrantModifyEntity warrantModifyEntity;

    /**
     * 仓单注销记录表
     */
    WarrantCancellationEntity warrantCancellationEntity;

    /**
     * 仓单数量变更表
     */
    WarrantCountUpdateLogEntity warrantCountUpdateLogEntity;
    /**
     * 仓单分配/转让
     */
    WarrantAllocateEntity warrantAllocateEntity;

    /**
     * 仓单注销关联
     */
    List<CancellationRelationEntity> cancellationRelationEntities;

}
