package com.navigator.koala.domain.service;

import com.navigator.koala.domain.model.WarrantDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
public interface WarrantDomainServer {

    /**
     * 保存
     * @param warrantDO
     * @return
     */
    Integer saveWarrantDO(WarrantDO warrantDO);

    /**
     * 更新
     * @param warrantDO
     * @return
     */
    Integer updateWarrantDO(WarrantDO warrantDO);


    /**
     * 仓单合同注销
     *
     * @param warrantDO
     * @return
     */
    boolean cancelledWarrantContract(WarrantDO warrantDO);
}
