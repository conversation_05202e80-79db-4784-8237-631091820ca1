package com.navigator.koala.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.koala.mapper.CancellationRelationMapper;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */
@Dao
public class CancellationRelationDao extends BaseDaoImpl<CancellationRelationMapper, CancellationRelationEntity> {
    public List<CancellationRelationEntity> queryCancellationRelationByWarrantCancellationId(Integer warrantCancellationId) {
        return this.list(Wrappers.<CancellationRelationEntity>lambdaQuery()
                .eq(CancellationRelationEntity::getWarrantCancellationId, warrantCancellationId)
        );
    }

    public List<CancellationRelationEntity> queryCancellationRelationByContractCode(String referContractCode) {
        return this.list(Wrappers.<CancellationRelationEntity>lambdaQuery()
                .eq(CancellationRelationEntity::getReferContractCode, referContractCode)
        );
    }
}
