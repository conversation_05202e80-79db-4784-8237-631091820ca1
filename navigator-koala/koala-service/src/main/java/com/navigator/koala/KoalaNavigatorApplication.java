package com.navigator.koala;

import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12
 */
@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.koala.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class KoalaNavigatorApplication {

    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }

    public static void main(String[] args) {
        SpringApplication.run(KoalaNavigatorApplication.class, args);
    }

}
