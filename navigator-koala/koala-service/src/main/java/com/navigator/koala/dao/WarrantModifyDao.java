package com.navigator.koala.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.koala.mapper.WarrantModifyMapper;
import com.navigator.koala.pojo.entity.WarrantModifyEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */
@Dao
public class WarrantModifyDao extends BaseDaoImpl<WarrantModifyMapper, WarrantModifyEntity> {

    public List<WarrantModifyEntity> queryWarrantModifyByReceiptCode(String receiptCode) {
        return this.list(Wrappers.<WarrantModifyEntity>lambdaQuery()
                .eq(WarrantModifyEntity::getWarrantCode, receiptCode)
        );
    }

    public List<WarrantModifyEntity> queryWarrantModifyByReceiptCode(String receiptCode,List<Integer> modifyType) {
        return this.list(Wrappers.<WarrantModifyEntity>lambdaQuery()
                .eq(WarrantModifyEntity::getWarrantCode, receiptCode)
                .in(WarrantModifyEntity::getModifyType, modifyType)
        );
    }
}
