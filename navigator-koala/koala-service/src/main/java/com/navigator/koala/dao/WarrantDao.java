package com.navigator.koala.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.koala.mapper.WarrantMapper;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantStatusEnum;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */
@Dao
public class WarrantDao extends BaseDaoImpl<WarrantMapper, WarrantEntity> {

    public IPage<WarrantEntity> queryWarrant(QueryDTO<QueryWarrantBO> queryDTO) {

        QueryWarrantBO queryWarrantBO = queryDTO.getCondition();
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), new LambdaQueryWrapper<WarrantEntity>()
                .eq(null != queryWarrantBO.getRegisteId(), WarrantEntity::getRegisteId, queryWarrantBO.getRegisteId())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getExchangeCode()), WarrantEntity::getExchangeCode, queryWarrantBO.getExchangeCode())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getFutureCode()), WarrantEntity::getFutureCode, queryWarrantBO.getFutureCode())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getDomainCode()), WarrantEntity::getDomainCode, queryWarrantBO.getDomainCode())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getCategory2()), WarrantEntity::getCategory2, queryWarrantBO.getCategory2())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getWarehouseCode()), WarrantEntity::getWarehouseCode, queryWarrantBO.getWarehouseCode())
                .eq(StringUtils.isNotBlank(queryWarrantBO.getSiteCode()), WarrantEntity::getSiteCode, queryWarrantBO.getSiteCode())
                .like(StringUtils.isNotBlank(queryWarrantBO.getCode()), WarrantEntity::getCode, queryWarrantBO.getCode())
                .gt(null != queryWarrantBO.getAllocateNumType() && queryWarrantBO.getAllocateNumType() == 1, WarrantEntity::getHoldCount, BigDecimal.ZERO)
                .eq(null != queryWarrantBO.getAllocateNumType() && queryWarrantBO.getAllocateNumType() == 0, WarrantEntity::getHoldCount, BigDecimal.ZERO)
                .eq(null != queryWarrantBO.getCategory(), WarrantEntity::getCategory, queryWarrantBO.getCategory())
                .eq(null != queryWarrantBO.getStatus(), WarrantEntity::getStatus, queryWarrantBO.getStatus())
                .eq(WarrantEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(WarrantEntity::getSiteCode, queryWarrantBO.getSiteCodeList())
                .between(StringUtils.isNotBlank(queryWarrantBO.getCreateStartTime()) && StringUtils.isNotBlank(queryWarrantBO.getCreateEndTime()),
                        WarrantEntity::getCreatedAt,
                        DateTimeUtil.parseTimeStamp0000(queryWarrantBO.getCreateStartTime()), DateTimeUtil.parseTimeStamp2359(queryWarrantBO.getCreateEndTime()))
                .orderByDesc(WarrantEntity::getCreatedAt)
        );
    }

    public List<WarrantEntity> queryWarrantSum(QueryWarrantBO queryWarrantBO, Integer status) {
        return this.list(
                new LambdaQueryWrapper<WarrantEntity>()
                        .eq(null != queryWarrantBO.getRegisteId(), WarrantEntity::getRegisteId, queryWarrantBO.getRegisteId())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getExchangeCode()), WarrantEntity::getExchangeCode, queryWarrantBO.getExchangeCode())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getFutureCode()), WarrantEntity::getFutureCode, queryWarrantBO.getFutureCode())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getDomainCode()), WarrantEntity::getDomainCode, queryWarrantBO.getDomainCode())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getCategory2()), WarrantEntity::getCategory2, queryWarrantBO.getCategory2())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getWarehouseCode()), WarrantEntity::getWarehouseCode, queryWarrantBO.getWarehouseCode())
                        .eq(StringUtils.isNotBlank(queryWarrantBO.getSiteCode()), WarrantEntity::getSiteCode, queryWarrantBO.getSiteCode())
                        .eq(null != queryWarrantBO.getCategory(), WarrantEntity::getCategory, queryWarrantBO.getCategory())
                        .eq(null != queryWarrantBO.getStatus(), WarrantEntity::getStatus, queryWarrantBO.getStatus())
                        .notIn(null != status, WarrantEntity::getStatus, Arrays.asList(status))
                        .eq(WarrantEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .between(StringUtils.isNotBlank(queryWarrantBO.getCreateStartTime()) && StringUtils.isNotBlank(queryWarrantBO.getCreateEndTime()),
                                WarrantEntity::getCreatedAt,
                                DateTimeUtil.parseTimeStamp0000(queryWarrantBO.getCreateStartTime()), DateTimeUtil.parseTimeStamp2359(queryWarrantBO.getCreateEndTime()))
                        .orderByDesc(WarrantEntity::getCreatedAt)
        );
    }

    public List<WarrantEntity> queryWarrantSumByCode(String code) {
        return this.list(
                new LambdaQueryWrapper<WarrantEntity>()
                        .eq(WarrantEntity::getCode, code)
                        .eq(WarrantEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByDesc(WarrantEntity::getCreatedAt)
        );
    }


    public String getMaxWarrantCode(String code) {
        List<WarrantEntity> warrantEntities = this.list(
                new LambdaQueryWrapper<WarrantEntity>()
                        .like(WarrantEntity::getCode, code)
                        .orderByDesc(WarrantEntity::getCreatedAt)
        );
        return warrantEntities.isEmpty() ? null : warrantEntities.get(0).getCode();

    }
}
