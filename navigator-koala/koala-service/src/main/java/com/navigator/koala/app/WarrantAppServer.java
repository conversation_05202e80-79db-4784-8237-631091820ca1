package com.navigator.koala.app;

import com.navigator.common.dto.Result;
import com.navigator.koala.pojo.dto.AllocateAssignWarrantDTO;
import com.navigator.koala.pojo.dto.CancelledWarrantDTO;
import com.navigator.koala.pojo.dto.UpdateWarrantNumDTO;
import com.navigator.koala.pojo.dto.WarrantDTO;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.vo.VerifyWarrantFileReturnVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
public interface WarrantAppServer {

    /**
     * 提交仓单
     *
     * @param warrantEntity
     * @return
     */
    WarrantEntity submitWarrant(WarrantEntity warrantEntity);

    /**
     * 保存仓单
     *
     * @param warrantEntity
     * @return
     */
    WarrantEntity saveWarrant(WarrantEntity warrantEntity);

    /**
     * 编辑仓单
     *
     * @param warrantEntity
     * @return
     */
    WarrantEntity updateWarrant(WarrantEntity warrantEntity);

    /**
     * LDC注销仓单
     *
     * @param warrantDTO
     * @return
     */
    boolean LDCCancelledWarrant(WarrantDTO warrantDTO);

    /**
     * 仓单分配/转让
     *
     * @param allocateAssignWarrantDTO
     * @return
     */
    Result allocateWarrant(AllocateAssignWarrantDTO allocateAssignWarrantDTO);

    /**
     * 更新仓单量
     *
     * @param updateWarrantNumDTO
     * @return
     */
    boolean updateWarrantNum(UpdateWarrantNumDTO updateWarrantNumDTO);

    /**
     * 仓单合同注销
     *
     * @param cancelledWarrantDTO
     * @return
     */
    boolean cancelledWarrantContract(CancelledWarrantDTO cancelledWarrantDTO);

    /**
     * 仓单上传
     *
     * @param file
     */
    VerifyWarrantFileReturnVO uploadingFileWarrant(MultipartFile file);

    /**
     * 仓单上传校验
     *
     */
    VerifyWarrantFileReturnVO verifyFileWarrant(MultipartFile file);


    /**
     * 修改仓单状态
     *
     * @param warrantDTO
     * @return
     */
    boolean updateWarrantStatus(WarrantDTO warrantDTO);
    /**
     * 根据仓单id删除仓单
     *
     * @param id
     * @return
     */
    boolean deleteWarrant(Integer id);

    /**
     * 根据合同编码获取注销记录信息
     * @param contractCode
     * @return
     */
    WarrantCancellationEntity queryWarrantCancellation(String contractCode);
}
