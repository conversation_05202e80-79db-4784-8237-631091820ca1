package com.navigator.koala.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.koala.app.WarrantAppServer;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.dto.*;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.vo.WarrantCancellationVO;
import com.navigator.koala.service.WarrantQueryService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
@RestController
public class WarrantFacadeImpl implements WarrantFacade {

    @Resource
    private WarrantAppServer warrantAppServer;
    @Resource
    private WarrantQueryService warrantQueryService;

    @Override
    public Result submitWarrant(WarrantEntity warrantEntity) {
        return Result.success(warrantAppServer.submitWarrant(warrantEntity));
    }

    @Override
    public Result updateWarrant(WarrantEntity warrantEntity) {
        return Result.success(warrantAppServer.updateWarrant(warrantEntity));
    }

    @Override
    public Result saveWarrant(WarrantEntity warrantEntity) {
        return Result.success(warrantAppServer.saveWarrant(warrantEntity));
    }

    @Override
    public Result LDCCancelledWarrant(WarrantDTO warrantDTO) {
        return Result.success(warrantAppServer.LDCCancelledWarrant(warrantDTO));
    }

    @Override
    public Result allocateWarrant(AllocateAssignWarrantDTO allocateAssignWarrantDTO) {
        return warrantAppServer.allocateWarrant(allocateAssignWarrantDTO);
    }

    @Override
    public Result updateWarrantStatus(WarrantDTO warrantDTO) {
        return Result.success(warrantAppServer.updateWarrantStatus(warrantDTO));
    }

    @Override
    public Result updateWarrantNum(UpdateWarrantNumDTO updateWarrantNumDTO) {
        return Result.success(warrantAppServer.updateWarrantNum(updateWarrantNumDTO));
    }

    @Override
    public Result cancelledWarrantContract(CancelledWarrantDTO cancelledWarrantDTO) {
        return Result.success(warrantAppServer.cancelledWarrantContract(cancelledWarrantDTO));
    }

    @Override
    public Result uploadingFileWarrant(MultipartFile file) {
        return Result.success(warrantAppServer.uploadingFileWarrant(file));
    }

    @Override
    public Result verifyFileWarrant(MultipartFile file) {
        return Result.success(warrantAppServer.verifyFileWarrant(file));
    }

    @Override
    public Result queryWarrant(QueryDTO<QueryWarrantBO> queryDTO) {
        return warrantQueryService.queryWarrant(queryDTO);
    }

    @Override
    public Result queryWarrantByID(Integer id) {
        return Result.success(warrantQueryService.queryWarrantByID(id));
    }

    @Override
    public Result queryWarrantVOByID(Integer id) {
        return Result.success(warrantQueryService.queryWarrantVOByID(id));
    }


    @Override
    public Result queryWarrantCancellationByContractCode(String contractCode) {
        return Result.success(warrantQueryService.queryWarrantCancellationByContractCode(contractCode));
    }

    @Override
    public Result queryWarrantCancellationByWarrantCode(String warrantCode) {
        return Result.success(warrantQueryService.queryWarrantCancellationByWarrantCode(warrantCode));
    }

    @Override
    public Result queryWarrantSum(QueryWarrantBO queryWarrantBO) {
        return Result.success(warrantQueryService.queryWarrantSum(queryWarrantBO));
    }

    @Override
    public List<DownloadWarrantExcelDTO> queryWarrantExcel(QueryWarrantBO queryWarrantBO) {
        return warrantQueryService.queryWarrantExcel(queryWarrantBO);
    }

    @Override
    public Result queryWarrantByCode(String code) {
        return Result.success(warrantQueryService.queryWarrantByCode(code));
    }

    @Override
    public Result deleteWarrant(Integer id) {
        return Result.success(warrantAppServer.deleteWarrant(id));
    }

    @Override
    public List<CancellationRelationEntity> queryCancellationRelationByCancellId(Integer cancellationId) {
        return warrantQueryService.queryCancellationRelationByCancellId(cancellationId);
    }

    @Override
    public WarrantCancellationEntity queryWarrantCancellation(String contractCode) {
        return warrantAppServer.queryWarrantCancellation(contractCode);
    }

}
