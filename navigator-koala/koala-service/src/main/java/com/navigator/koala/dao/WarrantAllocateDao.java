package com.navigator.koala.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.koala.mapper.WarrantAllocateMapper;
import com.navigator.koala.pojo.entity.WarrantAllocateEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@Dao
public class WarrantAllocateDao extends BaseDaoImpl<WarrantAllocateMapper, WarrantAllocateEntity> {

    public List<WarrantAllocateEntity> queryWarrantAllocateByReceiptCode(String warrantCode) {
        return this.baseMapper.selectList(Wrappers.<WarrantAllocateEntity>lambdaQuery()
                .eq(WarrantAllocateEntity::getWarrantCode, warrantCode)
        );
    }

    public List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCode(String warrantCode, String contractCode) {
        return this.baseMapper.selectList(Wrappers.<WarrantAllocateEntity>lambdaQuery()
                .eq(WarrantAllocateEntity::getContractCode, contractCode)
                .eq(WarrantAllocateEntity::getWarrantCode, warrantCode)
        );
    }

    /**
     * 根据仓单编码andTT编码查询数据
     *
     * @param warrantCode
     * @param ttCode
     * @return
     */
    public List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCodeAndTtCode(String warrantCode, String ttCode) {
        return this.baseMapper.selectList(Wrappers.<WarrantAllocateEntity>lambdaQuery()
                .eq(WarrantAllocateEntity::getWarrantCode, warrantCode)
                .eq(WarrantAllocateEntity::getTtCode, ttCode)
        );
    }
}
