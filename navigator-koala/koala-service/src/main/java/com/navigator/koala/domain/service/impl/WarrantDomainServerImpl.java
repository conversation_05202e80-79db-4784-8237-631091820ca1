package com.navigator.koala.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.koala.dao.*;
import com.navigator.koala.domain.model.WarrantDO;
import com.navigator.koala.domain.service.WarrantDomainServer;
import com.navigator.koala.pojo.entity.*;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
@Service
public class WarrantDomainServerImpl implements WarrantDomainServer {

    @Resource
    private WarrantDao warrantDao;
    @Resource
    private WarrantModifyDao warrantModifyDao;
    @Resource
    private WarrantCancellationDao warrantCancellationDao;
    @Resource
    private WarrantCountUpdateLogDao warrantCountUpdateLogDao;
    @Resource
    private CancellationRelationDao cancellationRelationDao;
    @Resource
    private WarrantAllocateDao warrantAllocateDao;
    @Resource
    private RedisUtil redisUtil;

    Random random = new Random();

    @Override
    public Integer saveWarrantDO(WarrantDO warrantDO) {

        return saveWarrant(warrantDO.getWarrantEntity());
    }

    @Override
    public Integer updateWarrantDO(WarrantDO warrantDO) {
        //更新主表信息
        if (null != warrantDO.getWarrantEntity()) {
            updateWarrant(warrantDO.getWarrantEntity());
        }
        //仓单变更更信息
        if (null != warrantDO.getWarrantModifyEntity()) {
            WarrantModifyEntity warrantModifyEntity = warrantDO.getWarrantModifyEntity();
            warrantModifyDao.saveOrUpdate(warrantModifyEntity);

        }

        //仓单分配/转让
        if (null != warrantDO.getWarrantAllocateEntity()) {
            WarrantAllocateEntity warrantAllocateEntity = warrantDO.getWarrantAllocateEntity();
            warrantAllocateDao.saveOrUpdate(warrantAllocateEntity);

        }

        //仓单注销信息
        if (null != warrantDO.getWarrantCancellationEntity()) {
            WarrantCancellationEntity warrantCancellationEntity = warrantDO.getWarrantCancellationEntity();

            warrantCancellationDao.saveOrUpdate(warrantCancellationEntity);

            if(CollectionUtils.isNotEmpty(warrantDO.getCancellationRelationEntities())){
                for (CancellationRelationEntity cancellationRelationEntity : warrantDO.getCancellationRelationEntities()) {
                    cancellationRelationEntity
                            .setWarrantCode(warrantCancellationEntity.getReceiptCode())
                            .setWarrantCancellationId(warrantCancellationEntity.getId())
                    ;
                    cancellationRelationDao.saveOrUpdate(cancellationRelationEntity);
                }
            }


        }
        //仓单数量变更信息
        if (null != warrantDO.getWarrantCountUpdateLogEntity()) {
            WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = warrantDO.getWarrantCountUpdateLogEntity();
            warrantCountUpdateLogDao.saveOrUpdate(warrantCountUpdateLogEntity);

        }
        return 0;
    }

    @Override
    public boolean cancelledWarrantContract(WarrantDO warrantDO) {
        if (null != warrantDO.getWarrantEntity()) {
            updateWarrant(warrantDO.getWarrantEntity());
        }

        //仓单注销信息
        if (null != warrantDO.getWarrantCancellationEntity()) {
            WarrantCancellationEntity warrantCancellationEntity = warrantDO.getWarrantCancellationEntity();
            if (null == warrantCancellationEntity.getId()) {
                warrantCancellationDao.saveOrUpdate(warrantCancellationEntity);
            }


        }

        return true;
    }


    private Integer saveWarrant(WarrantEntity warrantEntity) {
        String warrantCode = "warrant:code";
        warrantCode = warrantCode + ":" + warrantEntity.getExchangeCode() + warrantEntity.getFutureCode() + DateTimeUtil.formatDateCN1(new Date());
        if (null == redisUtil.get(warrantCode)) {
            String code = warrantEntity.getExchangeCode() + warrantEntity.getFutureCode() + DateTimeUtil.formatDateCN1(new Date());
            String maxWarrantCode = warrantDao.getMaxWarrantCode(code);
            if (StringUtil.isNotEmpty(maxWarrantCode)) {
                long value = Long.parseLong(maxWarrantCode.substring(maxWarrantCode.length() - 4));
                redisUtil.set(warrantCode, value);
            }
        }
        // 获取递增码
        long value = redisUtil.incr(warrantCode, 1L);

        //生成仓单编码
        String code = warrantEntity.getExchangeCode() + warrantEntity.getFutureCode() + DateTimeUtil.formatDateCN1(new Date()) + String.format("%04d", value);

        warrantEntity.setCode(code);
        warrantDao.saveOrUpdate(warrantEntity);
        return warrantEntity.getId();
    }


    private Integer updateWarrant(WarrantEntity warrantEntity) {
        warrantDao.saveOrUpdate(warrantEntity);
        return warrantEntity.getId();
    }

}
