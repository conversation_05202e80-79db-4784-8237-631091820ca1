package com.navigator.koala.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.dto.DownloadWarrantExcelDTO;
import com.navigator.koala.pojo.dto.WarrantExcelDTO;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.entity.WarrantAllocateEntity;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.vo.WarrantCancellationVO;
import com.navigator.koala.pojo.vo.WarrantSumVO;
import com.navigator.koala.pojo.vo.WarrantVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
public interface WarrantQueryService {

    /**
     * 仓单列表
     *
     * @param queryDTO
     * @return
     */
    Result queryWarrant(QueryDTO<QueryWarrantBO> queryDTO);

    /**
     * 根据仓单id查询仓单信息
     *
     * @param id
     * @return
     */
    WarrantEntity queryWarrantByID(Integer id);

    /**
     * 仓单详情
     *
     * @param id
     * @return
     */
    WarrantVO queryWarrantVOByID(Integer id);

    /**
     * 总计
     *
     * @param queryWarrantBO
     * @return
     */
    List<WarrantSumVO> queryWarrantSum(QueryWarrantBO queryWarrantBO);

    /**
     * 下载仓单
     */
    List<DownloadWarrantExcelDTO> queryWarrantExcel(QueryWarrantBO queryWarrantBO);
    /**
     * 根据仓单code查询仓单信息
     *
     * @param code
     * @return
     */
    WarrantEntity queryWarrantByCode(String code);

    /**
     * 根据仓编号和合同编号查询仓单分配信息
     *
     * @param warrantCode
     * @param contractCode
     * @return
     */
    List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCode(String warrantCode, String contractCode);

    /**
     * 根据合同编号查询合同注销记录
     *
     * @param contractCode
     * @return
     */
    List<WarrantCancellationVO> queryWarrantCancellationByContractCode(String contractCode);

    /**
     * 根据仓单编号查询注销记录
     *
     * @param warrantCode
     * @return
     */
    List<WarrantCancellationVO> queryWarrantCancellationByWarrantCode(String warrantCode);


    /**
     * 根据仓单code和提货合同code查询分配注销记录
     */
    List<WarrantCancellationEntity> queryWarrantCancellationByWarrantCodeAndDeliveryContractCode(String warrantCode, String deliveryContractCode);

    WarrantCancellationEntity queryWarrantCancellationById(Integer id);
    /**
     * 根据仓单code和ttCode查询分配注销记录
     *
     * @param warrantCode
     * @param ttCode
     * @return
     */
    List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCodeAndTtCode(String warrantCode, String ttCode);

    /**
     * 根据合同编码查询注销记录信息
     * @param contractCode
     * @return
     */
    WarrantCancellationEntity queryWarrantCancellation(String contractCode);

    /**
     * 根据注销记录ID获取注销合同明细
     */
    List<CancellationRelationEntity> queryCancellationRelationByCancellId(Integer cancellationId);
}
