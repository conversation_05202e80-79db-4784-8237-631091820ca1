package com.navigator.koala.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.koala.dao.*;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.dto.DownloadWarrantExcelDTO;
import com.navigator.koala.pojo.entity.*;
import com.navigator.koala.pojo.enums.*;
import com.navigator.koala.pojo.vo.CancellationRelationVO;
import com.navigator.koala.pojo.vo.WarrantCancellationVO;
import com.navigator.koala.pojo.vo.WarrantSumVO;
import com.navigator.koala.pojo.vo.WarrantVO;
import com.navigator.koala.service.WarrantQueryService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
@Service
public class WarrantQueryServiceImpl implements WarrantQueryService {

    @Resource
    private WarrantDao warrantDao;
    @Resource
    private WarrantModifyDao warrantModifyDao;
    @Resource
    private WarrantCancellationDao warrantCancellationDao;
    @Resource
    private WarrantAllocateDao warrantAllocateDao;
    @Resource
    private CancellationRelationDao cancellationRelationDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private ContractFacade contractFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private PayConditionFacade payConditionFacade;
    @Resource
    private WarehouseFacade warehouseFacade;
    @Resource
    private EmployFacade employFacade;


    @Override
    public Result queryWarrant(QueryDTO<QueryWarrantBO> queryDTO) {
        QueryWarrantBO queryWarrantBO = queryDTO.getCondition();

        // 权限列表
        PermissionBO permissionBO = employFacade.querySitePermission(JwtUtils.getCurrentUserId(), Integer.parseInt(queryWarrantBO.getCategory2()));
        queryWarrantBO.setSiteCodeList(permissionBO.getSiteCodeList());
        queryDTO.setCondition(queryWarrantBO);
        IPage<WarrantEntity> iPage = warrantDao.queryWarrant(queryDTO);
        List<WarrantVO> warrantVOS = iPage.getRecords().stream().map(warrantEntity -> {
            WarrantVO warrantVO = BeanConvertUtils.convert(WarrantVO.class, warrantEntity);
            warrantVO.setAllocatedTransferCount(warrantEntity.getRegisteCount().subtract(warrantEntity.getHoldCount()));
            return warrantVO;
        }).collect(Collectors.toList());

        return Result.page(iPage, warrantVOS);
    }

    @Override
    public WarrantEntity queryWarrantByID(Integer id) {
        return warrantDao.getById(id);
    }

    @Override
    public WarrantVO queryWarrantVOByID(Integer id) {
        WarrantEntity warrantEntity = warrantDao.getById(id);
        WarrantVO warrantVO = BeanConvertUtils.convert(WarrantVO.class, warrantEntity);
        //LDC注销记录
        List<WarrantModifyEntity> warrantLdcCancelList = warrantModifyDao.queryWarrantModifyByReceiptCode(warrantEntity.getCode(), Arrays.asList(WarrantModifyTypeEnum.LDC_CANCEL.getValue()));
        warrantVO.setWarrantLdcCancelList(warrantLdcCancelList);
        //分配记录
        List<WarrantAllocateEntity> warrantAllocateList = warrantAllocateDao.queryWarrantAllocateByReceiptCode(warrantEntity.getCode());

        for (WarrantAllocateEntity warrantAllocateEntity : warrantAllocateList) {
            ContractEntity contractEntity = contractFacade.getBasicContractById(warrantAllocateEntity.getContractId());

            if (null != contractEntity) {
                warrantAllocateEntity
                        .setSiteName(contractEntity.getSiteName())
                        .setWarrantTradeType(contractEntity.getWarrantTradeType());
            }
        }

        warrantVO.setWarrantAllocateEntities(warrantAllocateList);
        //仓单合同注销记录
        /*List<WarrantCancellationEntity> warrantContractCancelList = warrantCancellationDao.queryWarrantCancellationByReceiptCode(warrantEntity.getCode());
        warrantVO.setWarrantCancellationVOS(createWarrantCancellation(warrantContractCancelList));*/
        //查询仓单操作记录
        Result result = operationLogFacade.queryOperationDetailByReferBizCode(warrantEntity.getCode(), SystemEnum.MAGELLAN.getValue());
        if ((result != null && result.getCode() == ResultCodeEnum.OK.getCode() && result.isSuccess())) {
            List<OperationDetailEntity> operationDetailEntities = JSON.parseArray(JSON.toJSONString(result.getData()), OperationDetailEntity.class);
            warrantVO.setOperationDetailEntities(operationDetailEntities);
        }
        return warrantVO;
    }


    private List<WarrantCancellationVO> createWarrantCancellation(List<WarrantCancellationEntity> warrantContractCancelList) {
        List<WarrantCancellationVO> warrantCancellationVOS = new ArrayList<>();
        warrantCancellationVOS = warrantContractCancelList.stream().map(warrantCancellationEntity -> {

            WarrantCancellationVO warrantCancellationVO = BeanConvertUtils.convert(WarrantCancellationVO.class, warrantCancellationEntity);
            //查询仓单关联表
            List<CancellationRelationEntity> cancellationRelationEntities = cancellationRelationDao.queryCancellationRelationByWarrantCancellationId(warrantCancellationEntity.getId());
            List<CancellationRelationVO> cancellationRelationVOList = new ArrayList<>();
            //合同集合
            for (CancellationRelationEntity cancellationRelationEntity : cancellationRelationEntities) {
                ContractEntity contractEntity = contractFacade.getBasicContractById(cancellationRelationEntity.getReferContractId());
                CancellationRelationVO cancellationRelationVO = BeanConvertUtils.convert(CancellationRelationVO.class, cancellationRelationEntity);
                //查询付款条件代码
                Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(contractEntity.getPayConditionId());
                if (payCondition.isSuccess()) {
                    PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
                    cancellationRelationVO.setPayConditionCode(payConditionEntity.getCode());
                }
                cancellationRelationVO
                        .setCommodityName(contractEntity.getCommodityName())
                        .setDeliveryWarehouse(this.getFactoryWarehouse(contractEntity.getShipWarehouseId()))
                        .setUnitPrice(contractEntity.getUnitPrice())
                        .setDomainCode(contractEntity.getDomainCode())
                        .setCancelCount(contractEntity.getOrderNum())
                        .setDeliveryType(String.valueOf(contractEntity.getDeliveryTypeValue()))
                        .setDestination(contractEntity.getDestinationValue())
                        .setWeightCriteria(contractEntity.getWeightCheckValue())
                        .setFutureCode(contractEntity.getFutureCode())
                ;
                cancellationRelationVOList.add(cancellationRelationVO);
            }
            WarrantEntity warrantEntity = warrantDao.queryWarrantSumByCode(warrantCancellationEntity.getReceiptCode()).get(0);

            if(null == warrantEntity){
                throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
            }

            if (!cancellationRelationEntities.isEmpty()) {
                Integer contractId = cancellationRelationEntities.get(0).getReferContractId();
                ContractEntity contractEntity = contractFacade.getBasicContractById(contractId);
                if(null != contractEntity){
                    //查询合同信息
                    warrantCancellationVO
                            .setWarrantTradeType(WarrantTradeTypeEnum.getByValue(warrantEntity.getTradeType()).getDesc())
                            .setDomainCode(contractEntity.getDomainCode())
                            .setSignDate(contractEntity.getSignDate())
                            .setSiteCode(contractEntity.getSiteCode())
                            .setSiteName(contractEntity.getSiteName())
                            .setMemo(contractEntity.getMemo())
                            .setDeliveryStartTime(contractEntity.getDeliveryStartTime())
                            .setDeliveryEndTime(contractEntity.getDeliveryEndTime())
                            .setFutureCode(contractEntity.getFutureCode())
                    ;
                }
            }

            warrantCancellationVO.setCancellationRelationVOList(cancellationRelationVOList);

            return warrantCancellationVO;
        }).collect(Collectors.toList());

        return warrantCancellationVOS;
    }

    private String getFactoryWarehouse(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }

    @Override
    public List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCode(String warrantCode, String contractCode) {
        return warrantAllocateDao.queryWarrantAllocateByWarrantCode(warrantCode, contractCode);
    }

    @Override
    public List<WarrantCancellationVO> queryWarrantCancellationByContractCode(String contractCode) {

        ContractEntity contractEntity = contractFacade.getBasicContractByCode(contractCode);

        List<WarrantCancellationEntity> warrantCancellationEntities = new ArrayList<>();

        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        List<CancellationRelationEntity> cancellationRelationEntities = cancellationRelationDao.queryCancellationRelationByContractCode(contractCode);
        if (!CollectionUtils.isEmpty(cancellationRelationEntities)) {
            warrantCancellationEntities.add(warrantCancellationDao.getById(cancellationRelationEntities.get(0).getWarrantCancellationId()));
        } else {
            warrantCancellationEntities = warrantCancellationDao.queryWarrantCancellationByContractCode(contractCode);
        }
        if (CollectionUtils.isEmpty(warrantCancellationEntities)) {

            return null;
        }
        return createWarrantCancellation(warrantCancellationEntities);
    }

    @Override
    public List<WarrantCancellationEntity> queryWarrantCancellationByWarrantCodeAndDeliveryContractCode(String warrantCode, String deliveryContractCode) {
        List<WarrantCancellationEntity> warrantCancellationEntities = warrantCancellationDao.queryWarrantCancellationByWarrantCodeAndDeliveryContractCode(warrantCode, deliveryContractCode);

        return warrantCancellationEntities;
    }

    @Override
    public WarrantCancellationEntity queryWarrantCancellationById(Integer id) {
        return warrantCancellationDao.getById(id);
    }

    @Override
    public List<WarrantCancellationVO> queryWarrantCancellationByWarrantCode(String warrantCode) {
        List<WarrantCancellationEntity> warrantCancellationEntities = warrantCancellationDao.queryWarrantCancellationByReceiptCode(warrantCode);
        if (warrantCancellationEntities.isEmpty()) {
            return null;
        }
        return createWarrantCancellation(warrantCancellationEntities);
    }

    @Override
    public List<WarrantAllocateEntity> queryWarrantAllocateByWarrantCodeAndTtCode(String warrantCode, String ttCode) {
        return warrantAllocateDao.queryWarrantAllocateByWarrantCodeAndTtCode(warrantCode, ttCode);
    }

    @Override
    public List<CancellationRelationEntity> queryCancellationRelationByCancellId(Integer cancellationId) {
        return cancellationRelationDao.queryCancellationRelationByWarrantCancellationId(cancellationId);
    }

    @Override
    public WarrantCancellationEntity queryWarrantCancellation(String contractCode) {
        // 查询注销记录信息
        List<CancellationRelationEntity> cancellationRelationEntities = cancellationRelationDao.queryCancellationRelationByContractCode(contractCode);
        if (ObjectUtil.isNotEmpty(cancellationRelationEntities) && cancellationRelationEntities.size() > 0) {
          return  warrantCancellationDao.getById(cancellationRelationEntities.get(0).getWarrantCancellationId());
        }
        return null;
    }

    @Override
    public List<WarrantSumVO> queryWarrantSum(QueryWarrantBO queryWarrantBO) {

        CategoryQO categoryQO = new CategoryQO();
        categoryQO
                .setParentSerialNo(Integer.parseInt(queryWarrantBO.getCategory2()))
                .setFutureCode(queryWarrantBO.getFutureCode())
        ;
        List<CategoryEntity> categoryEntities = categoryFacade.queryCategoryList(categoryQO);


        categoryEntities = new ArrayList<>(categoryEntities.stream()
                .filter(entity -> entity.getFutureCode() != null) // 过滤掉null的futureCode
                .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CategoryEntity::getFutureCode)))));


        List<WarrantSumVO> warrantSumVOS = new ArrayList<>();
        for (CategoryEntity categoryEntity : categoryEntities) {
            //品种代码不能为空
            if (StringUtil.isNotEmpty(categoryEntity.getFutureCode())) {
                //取出品种编码
                String futureCode = categoryEntity.getFutureCode();
                //取出品种名称
                String categoryName = categoryEntity.getName();
                WarrantSumVO warrantSumVO = new WarrantSumVO();
                queryWarrantBO.setFutureCode(futureCode);
                List<WarrantEntity> warrantEntities = warrantDao.queryWarrantSum(queryWarrantBO, WarrantStatusEnum.INVALID.getValue());
                if (warrantEntities.isEmpty()) {
                    warrantSumVO
                            .setCategoryName(categoryName)
                            .setFutureCode(futureCode)
                            .setOwnHoldHandCount(BigDecimal.ZERO)
                            .setWarrantNum(BigDecimal.ZERO)
                            .setHoldHandCount(BigDecimal.ZERO)
                            .setAllocateNum(BigDecimal.ZERO)
                    ;
                } else {

                    //仓单总量
                    BigDecimal warrantNum = BigDecimal.ZERO;
                    //自有仓单持有量
                    BigDecimal ownHoldHandCount = BigDecimal.ZERO;
                    //仓单持有量
                    BigDecimal holdHandCount = BigDecimal.ZERO;
                    //可分配/转让量
                    BigDecimal allocateNum = BigDecimal.ZERO;

                    for (WarrantEntity warrantEntity : warrantEntities) {
                        warrantNum = warrantNum.add(warrantEntity.getRegisteHandCount());
                        if (WarrantPropertyEnum.OWN_WARRANT.getValue().equals(warrantEntity.getProperty())) {
                            //自有仓单数量
                            ownHoldHandCount = ownHoldHandCount.add(warrantEntity.getHoldHandCount());
                        } else {
                            //仓单数量
                            holdHandCount = holdHandCount.add(warrantEntity.getHoldHandCount());
                        }
                        allocateNum = allocateNum.add(warrantEntity.getHoldCount());
                    }
                    warrantSumVO
                            .setCategoryName(categoryName)
                            .setFutureCode(futureCode)
                            .setWarrantNum(warrantNum)
                            .setOwnHoldHandCount(ownHoldHandCount)
                            .setHoldHandCount(holdHandCount)
                            .setAllocateNum(allocateNum)
                    ;
                }

                warrantSumVOS.add(warrantSumVO);
            }
        }

        return warrantSumVOS;
    }

    @Override
    public List<DownloadWarrantExcelDTO> queryWarrantExcel(QueryWarrantBO queryWarrantBO) {
        List<WarrantEntity> warrantEntities = warrantDao.queryWarrantSum(queryWarrantBO, WarrantStatusEnum.DRAFT.getValue());
        if (warrantEntities.isEmpty()) {
            return null;
        }

        List<DownloadWarrantExcelDTO> warrantExcelDTOS = new ArrayList<>();
        for (WarrantEntity warrantEntity : warrantEntities) {
            DownloadWarrantExcelDTO downloadWarrantExcelDTO = BeanConvertUtils.convert(DownloadWarrantExcelDTO.class, warrantEntity);
            downloadWarrantExcelDTO
                    .setStatus(WarrantStatusEnum.getValue(warrantEntity.getStatus()).getDesc())
                    .setProperty(WarrantPropertyEnum.getValue(warrantEntity.getProperty()).getDesc())
                    .setWarrantCode(warrantEntity.getCode())
                    .setSiteName(warrantEntity.getSiteName())
                    .setRegisteHandCount(warrantEntity.getRegisteHandCount().toString())
                    .setDepositAmount(warrantEntity.getDepositAmount().toString())
                    .setHoldCount(warrantEntity.getHoldCount().toString())
                    .setCategory(WarrantCategoryEnum.getValue(warrantEntity.getCategory()).getDesc())
                    .setWarehouseName(warrantEntity.getWarehouseName())
                    .setDepositPaymentType(WarrantPaymentTypeEnum.getValue(warrantEntity.getDepositPaymentType()).getDesc())
            ;
            warrantExcelDTOS.add(downloadWarrantExcelDTO);

            List<ContractEntity> contractEntities = contractFacade.getContractByWarrantCode(warrantEntity.getCode());
            for (ContractEntity contractEntity : contractEntities) {
                DownloadWarrantExcelDTO downloadWarrantExcelDTO1 = BeanConvertUtils.convert(DownloadWarrantExcelDTO.class, contractEntity);
                downloadWarrantExcelDTO1
                        .setSiteName(contractEntity.getSiteName())
                        .setSalesType(ContractSalesTypeEnum.getByValue(contractEntity.getSalesType()).getDescription())
                        .setSource(ContractActionEnum.getByType(contractEntity.getContractSource()).getDesc())
                        .setContractCount(contractEntity.getOrderNum().toPlainString())
                ;
                warrantExcelDTOS.add(downloadWarrantExcelDTO1);
            }


        }
        return warrantExcelDTOS;
    }


    @Override
    public WarrantEntity queryWarrantByCode(String code) {
        List<WarrantEntity> warrantEntities = warrantDao.queryWarrantSumByCode(code);

        return !warrantEntities.isEmpty() ? warrantEntities.get(0) : null;
    }

}
