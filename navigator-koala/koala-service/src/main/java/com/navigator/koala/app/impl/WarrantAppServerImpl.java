package com.navigator.koala.app.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ExchangeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.koala.app.WarrantAppServer;
import com.navigator.koala.dao.WarrantDao;
import com.navigator.koala.domain.model.WarrantDO;
import com.navigator.koala.domain.service.WarrantDomainServer;
import com.navigator.koala.pojo.dto.*;
import com.navigator.koala.pojo.entity.*;
import com.navigator.koala.pojo.enums.*;
import com.navigator.koala.pojo.vo.VerifyWarrantFileReturnVO;
import com.navigator.koala.service.WarrantQueryService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/15
 */
@Service
@Slf4j
public class WarrantAppServerImpl implements WarrantAppServer {

    @Resource
    private WarrantDao warrantDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private WarrantDomainServer warrantDomainServer;
    @Resource
    private WarrantQueryService warrantQueryService;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private TradeTicketFacade tradeTicketFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private TradingConfigFacade tradingConfigFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private WarehouseFacade warehouseFacade;
    @Autowired
    private ContractFacade contractFacade;
    @Autowired
    private SiteFacade siteFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WarrantEntity submitWarrant(WarrantEntity warrantEntity) {

        if (null == warrantEntity.getId()) {
            return saveSubmitWarrantEntity(warrantEntity);
        }
        WarrantEntity warrant = warrantQueryService.queryWarrantByID(warrantEntity.getId());
        if (WarrantStatusEnum.DRAFT.getValue().equals(warrant.getStatus())) {
            warrantEntity.setCode(warrant.getCode());
            return saveSubmitWarrantEntity(warrantEntity);
        } else {
            return updateWarrant(warrantEntity);
        }
    }

    private WarrantEntity saveSubmitWarrantEntity(WarrantEntity warrantEntity) {

        WarrantDO warrantDO = new WarrantDO();
        //校验仓单数据完整新
        if (StringUtils.isEmpty(warrantEntity.getFutureCode())) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        if (WarrantSourceEnum.PURCHASE_CONTRACT.getValue().equals(warrantEntity.getSource())) {
            warrantEntity.setProperty(WarrantPropertyEnum.WARRANT.getValue());
        } else {
            warrantEntity.setProperty(WarrantPropertyEnum.OWN_WARRANT.getValue());
        }


        if (null == warrantEntity.getRegisteHandCount()) {
            BigDecimal registeHandCount = warrantEntity.getRegisteCount().divide(new BigDecimal(HandRateEnum.WARRANT.getRate()));
            warrantEntity.setRegisteHandCount(registeHandCount);
        }

        //库点编码为空时且库点id不为空时查询库点信息
        if (StringUtils.isEmpty(warrantEntity.getWarehouseCode())) {
            String warehouseCode = factoryConvertValue(warrantEntity.getWarehouseName());
            warrantEntity.setWarehouseCode(warehouseCode);
        }


        TradingConfigVO tradingConfigVO = tradingConfigFacade.getDomainTypeByCategoryCode(warrantEntity.getFutureCode());
        warrantEntity
                .setExchangeCode(tradingConfigVO.getExchange())
                .setExchangeName(ExchangeEnum.getDescByValue(tradingConfigVO.getExchange()))
                .setHoldHandCount(warrantEntity.getRegisteHandCount())
                .setHoldCount(warrantEntity.getRegisteHandCount().multiply(new BigDecimal(HandRateEnum.WARRANT.getRate())))
                .setStatus(WarrantStatusEnum.EFFECTIVE.getValue())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setUpdatedAt(DateTime.now().toTimestamp())
        ;

        if (null != warrantEntity.getRegisteHandCount()) {
            warrantEntity = createWarrantEntity(warrantEntity);
        }
        warrantDO.setWarrantEntity(warrantEntity);
        if (null == warrantEntity.getId()) {
            warrantEntity
                    .setCreatedAt(DateTime.now().toTimestamp())
                    .setCreatedBy(employFacade.getEmployById(userId).getName());
            warrantDomainServer.saveWarrantDO(warrantDO);
        } else {
            warrantDomainServer.updateWarrantDO(warrantDO);
        }

        try {
            WarrantLogDTO warrantLogDTO = new WarrantLogDTO()
                    .setData(JSON.toJSONString(warrantEntity))
                    .setLogBizCodeEnum(LogBizCodeEnum.SUBMIT_WAREHOUSE_APPLY)
                    .setOperatorId(userId)
                    .setOperatorName(employFacade.getEmployById(userId).getName())
                    .setWarrantCode(warrantEntity.getCode())
                    .setWarrantEntity(warrantEntity)
                    .setWarrantId(warrantEntity.getId());
            createWarrantLog(warrantLogDTO);
        } catch (Exception e) {
            log.debug("createWarrantLog error:{}", e);
        }

        return warrantEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WarrantEntity saveWarrant(WarrantEntity warrantEntity) {
        WarrantDO warrantDO = new WarrantDO();
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        //校验仓单数据完整新
        if (StringUtils.isEmpty(warrantEntity.getFutureCode())) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }

        TradingConfigVO tradingConfigVO = tradingConfigFacade.getDomainTypeByCategoryCode(warrantEntity.getFutureCode());


        warrantEntity
                .setExchangeCode(tradingConfigVO.getExchange())
                .setExchangeName(ExchangeEnum.getDescByValue(tradingConfigVO.getExchange()))
                .setStatus(WarrantStatusEnum.DRAFT.getValue())
                .setHoldHandCount(warrantEntity.getRegisteHandCount());

        if (null != warrantEntity.getRegisteHandCount()) {
            warrantEntity = createWarrantEntity(warrantEntity);
            warrantEntity.setHoldHandCount(warrantEntity.getRegisteHandCount())
                    .setHoldCount(warrantEntity.getRegisteHandCount().multiply(new BigDecimal(HandRateEnum.WARRANT.getRate())));
        }

        warrantEntity
                .setProperty(WarrantPropertyEnum.OWN_WARRANT.getValue())
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;
        warrantDO.setWarrantEntity(warrantEntity);
        warrantDomainServer.saveWarrantDO(warrantDO);
        try {
            WarrantLogDTO warrantLogDTO = new WarrantLogDTO()
                    .setData(JSON.toJSONString(warrantEntity))
                    .setLogBizCodeEnum(LogBizCodeEnum.SAVE_WAREHOUSE_APPLY)
                    .setOperatorId(userId)
                    .setOperatorName(employFacade.getEmployById(userId).getName())
                    .setWarrantCode(warrantEntity.getCode())
                    .setWarrantEntity(warrantEntity)
                    .setWarrantId(warrantEntity.getId());
            createWarrantLog(warrantLogDTO);
        } catch (Exception e) {
            log.debug("createWarrantLog error:{}", e);
        }

        return warrantEntity;
    }

    @Override
    public WarrantEntity updateWarrant(WarrantEntity warrantEntity) {

        if (null == warrantEntity.getId()) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }

        WarrantEntity warrant = warrantQueryService.queryWarrantByID(warrantEntity.getId());
        if (!WarrantStatusEnum.EFFECTIVE.getValue().equals(warrant.getStatus())) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EDIT);
        }

        if (warrant.getRegisteCount().subtract(warrant.getHoldCount()).compareTo(BigDecimal.ZERO) != 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EDIT);
        }
        warrantDomainServer.updateWarrantDO(new WarrantDO().setWarrantEntity(warrantEntity));
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        try {


            WarrantLogDTO warrantLogDTO = new WarrantLogDTO()
                    .setData(JSON.toJSONString(warrantEntity))
                    .setLogBizCodeEnum(LogBizCodeEnum.UPDATE_WAREHOUSE_APPLY)
                    .setOperatorId(userId)
                    .setOperatorName(employFacade.getEmployById(userId).getName())
                    .setWarrantCode(warrantEntity.getCode())
                    .setWarrantEntity(warrantEntity)
                    .setWarrantId(warrantEntity.getId());
            createWarrantLog(warrantLogDTO);
        } catch (Exception e) {
            log.debug("createWarrantLog error:{}", e);
        }
        return null;
    }

    private WarrantEntity createWarrantEntity(WarrantEntity warrantEntity) {

        //换算仓单手数量
        //注册数量
        BigDecimal registeCount = warrantEntity.getRegisteHandCount().multiply(new BigDecimal(HandRateEnum.WARRANT.getRate()));
        //持有量
        BigDecimal holdCount = warrantEntity.getHoldHandCount().multiply(new BigDecimal(HandRateEnum.WARRANT.getRate()));


        return warrantEntity
                .setRegisteCount(registeCount)
                .setHoldCount(holdCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean LDCCancelledWarrant(WarrantDTO warrantDTO) {
        //根据仓单code查询查询仓单信息
        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByID(warrantDTO.getId());
        WarrantEntity warrant = warrantEntity;
        //校验仓单数据是否存在
        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }

        if (!warrantEntity.getStatus().equals(WarrantStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_CANCEL);
        }
        Integer status = warrantEntity.getStatus();
        BigDecimal changeWarrantCount = warrantDTO.getChangeWarrantCount();
        //注册手数
        BigDecimal registeHandCount = warrantEntity.getRegisteHandCount().subtract(changeWarrantCount);
        //注册数量
        BigDecimal registeCount = registeHandCount.multiply(new BigDecimal(HandRateEnum.WARRANT.getRate()));
        BigDecimal holdHandCount = warrantEntity.getHoldHandCount().subtract(changeWarrantCount);
        BigDecimal holdCount = holdHandCount.multiply(new BigDecimal(HandRateEnum.WARRANT.getRate()));


        //校验数量
        if (holdHandCount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ENOUGH);
        } else if (holdHandCount.compareTo(BigDecimal.ZERO) == 0) {
            status = WarrantStatusEnum.ALLOCATE_COMPLETED.getValue();
        }

        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        //修改仓单注销量

        warrantEntity
                .setStatus(status)
                .setRegisteCount(registeCount)
                .setRegisteHandCount(registeHandCount)
                .setHoldCount(holdCount)
                .setHoldHandCount(holdHandCount);
        //记录仓单变更信息
        WarrantModifyEntity warrantModify = new WarrantModifyEntity();
        warrantModify
                .setWarrantCode(warrantEntity.getCode())
                .setModifyType(WarrantModifyTypeEnum.LDC_CANCEL.getValue())
                .setModifyCount(warrantDTO.getChangeWarrantCount())
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;

        //记录仓单变更数量
        WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = createWarrantCountUpdateLogEntity(warrant, warrantEntity, changeWarrantCount);

        //记录操作日志
        WarrantDO warrantDO = new WarrantDO();
        warrantDO
                .setWarrantEntity(warrantEntity)
                .setWarrantModifyEntity(warrantModify)
                .setWarrantCountUpdateLogEntity(warrantCountUpdateLogEntity)
        ;
        warrantDomainServer.updateWarrantDO(warrantDO);
        try {
            WarrantLogDTO warrantLogDTO = new WarrantLogDTO()
                    .setData(JSON.toJSONString(warrantEntity))
                    .setLogBizCodeEnum(LogBizCodeEnum.LDC_CANCEL_WAREHOUSE_APPLY)
                    .setOperatorId(userId)
                    .setOperatorName(employFacade.getEmployById(userId).getName())
                    .setWarrantCode(warrantEntity.getCode())
                    .setWarrantEntity(warrantEntity)
                    .setWarrantId(warrantEntity.getId());
            createWarrantLog(warrantLogDTO);
        } catch (Exception e) {
            log.debug("createWarrantLog error:{}", e);
        }

        return true;
    }

    private WarrantCountUpdateLogEntity createWarrantCountUpdateLogEntity(WarrantEntity warrant, WarrantEntity warrantEntity, BigDecimal changeWarrantCount) {

        Map<String, String> operationCountMap = new HashMap<>();
        Map<String, String> AfterWarrantCountMap = new HashMap<>();
        //仓单持有量
        operationCountMap.put("holdCount", String.valueOf(warrant.getHoldCount()));
        AfterWarrantCountMap.put("holdCount", String.valueOf(warrantEntity.getHoldCount()));
        operationCountMap.put("holdHandCount", String.valueOf(warrant.getHoldHandCount()));
        AfterWarrantCountMap.put("holdHandCount", String.valueOf(warrantEntity.getHoldHandCount()));
        //仓单变更量
        operationCountMap.put("getAlteredCount", String.valueOf(warrant.getAlteredCount()));
        AfterWarrantCountMap.put("getAlteredCount", String.valueOf(warrantEntity.getAlteredCount()));
        //仓单注销量
        operationCountMap.put("cancelledCount", String.valueOf(warrant.getCancelledCount()));
        AfterWarrantCountMap.put("cancelledCount", String.valueOf(warrantEntity.getCancelledCount()));

        WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = new WarrantCountUpdateLogEntity();
        warrantCountUpdateLogEntity
                .setWarrantCode(warrantEntity.getCode())
                .setOperationCount(JSONObject.toJSONString(operationCountMap))
                .setAfterWarrantCount(JSONObject.toJSONString(AfterWarrantCountMap))
                .setChangeWarrantCount(changeWarrantCount);
        return warrantCountUpdateLogEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result allocateWarrant(AllocateAssignWarrantDTO allocateAssignWarrantDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        CreateWarrantContractDTO createWarrantContractDTO = allocateAssignWarrantDTO.getCreateWarrantContractDTO();

        //根据仓单code查询查询仓单信息
        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByCode(createWarrantContractDTO.getWarrantCode());
        WarrantEntity warrant = warrantEntity;
        //校验仓单数据是否存在
        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }

        if (!warrantEntity.getStatus().equals(WarrantStatusEnum.EFFECTIVE.getValue()) && !warrantEntity.getStatus().equals(WarrantStatusEnum.ALLOCATE_UNDERWAY.getValue())) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ALLOCATE);
        }

        warrantCountCheck(createWarrantContractDTO, warrantEntity, createWarrantContractDTO.getChangeWarrantCount());

        //todo 调用trade服务
        TTDTO ttdto = createWarrantContractAddTTDTO(allocateAssignWarrantDTO, warrantEntity);
        //组装TT参数
        Result result = tradeTicketFacade.createWarrantSalesContract(ttdto);
        if (result == null || result.getCode() != ResultCodeEnum.OK.getCode() || result.isSuccess() == false) {
            log.error("result1:{}", JSON.toJSONString(result));
            throw new BusinessException(result.getMessage());
        }

        List<TTQueryVO> ttQueryVOList = JSON.parseArray(JSON.toJSONString(result.getData()), TTQueryVO.class);

        if (ttQueryVOList.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
        }
        TTQueryVO ttQueryVO = ttQueryVOList.get(0);

        //记录仓单分配转让信息
        WarrantDO warrantDO = allocateWarrantModify(warrantEntity, warrant, createWarrantContractDTO, userId, ttQueryVO);
        warrantDomainServer.updateWarrantDO(warrantDO);
        try {
            LogBizCodeEnum logBizCodeEnum = LogBizCodeEnum.ALLOCATE_WAREHOUSE_APPLY;
            if (WarrantOperationTypeEnum.TRANSFER.getValue().equals(createWarrantContractDTO.getOperationType())) {
                logBizCodeEnum = LogBizCodeEnum.TRANSFER_WAREHOUSE_APPLY;
            }

            WarrantLogDTO warrantLogDTO = new WarrantLogDTO()
                    .setData(JSON.toJSONString(warrantEntity))
                    .setLogBizCodeEnum(logBizCodeEnum)
                    .setOperatorId(userId)
                    .setOperatorName(employFacade.getEmployById(userId).getName())
                    .setWarrantCode(warrantEntity.getCode())
                    .setWarrantEntity(warrantEntity)
                    .setWarrantId(warrantEntity.getId());
            createWarrantLog(warrantLogDTO);
        } catch (Exception e) {
            log.debug("createWarrantLog error:{}", e);
        }
        return result;
    }


    /**
     * 校验仓单数量是否充足
     *
     * @param createWarrantContractDTO
     * @param warrantEntity
     * @param changeWarrantCount
     */
    private boolean warrantCountCheck(CreateWarrantContractDTO createWarrantContractDTO, WarrantEntity warrantEntity, BigDecimal changeWarrantCount) {

        BigDecimal holdCount = warrantEntity.getHoldCount();
        if (!StringUtils.isEmpty(createWarrantContractDTO.getTtCode())) {
            List<WarrantAllocateEntity> warrantAllocateEntities = warrantQueryService.queryWarrantAllocateByWarrantCodeAndTtCode(warrantEntity.getCode(), createWarrantContractDTO.getTtCode());
            if (!warrantAllocateEntities.isEmpty()) {
                WarrantAllocateEntity warrantAllocateEntity = warrantAllocateEntities.get(0);

                holdCount = holdCount.add(warrantAllocateEntity.getOperationCount());
            }
        }


        holdCount = holdCount.subtract(changeWarrantCount);
        if (holdCount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ENOUGH);
        }
        return true;
    }


    private WarrantDO allocateWarrantModify(WarrantEntity warrantEntity,
                                            WarrantEntity warrant,
                                            CreateWarrantContractDTO createWarrantContractDTO,
                                            Integer userId,
                                            TTQueryVO ttQueryVO) {
        WarrantDO warrantDO = new WarrantDO();

        BigDecimal changeWarrantCount = createWarrantContractDTO.getChangeWarrantCount();

        BigDecimal holdCount = warrantEntity.getHoldCount().subtract(changeWarrantCount);

        //申请手数
        BigDecimal holdHandCount = warrantEntity.getHoldHandCount().subtract(changeWarrantCount.divide(new BigDecimal(HandRateEnum.WARRANT.getRate())));

        if (holdHandCount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ENOUGH);
        }
        Integer status = warrantEntity.getStatus();
        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(createWarrantContractDTO.getWarrantTradeType())) {
            status = WarrantStatusEnum.ALLOCATE_UNDERWAY.getValue();
        } else if (holdHandCount.compareTo(BigDecimal.ZERO) == 0) {
            status = WarrantStatusEnum.ALLOCATE_COMPLETED.getValue();
        }

        warrantEntity
                .setHoldCount(holdCount)
                .setHoldHandCount(holdHandCount)
                .setStatus(status)
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
        ;

        //记录仓单分配转让信息
        WarrantAllocateEntity warrantAllocateEntity = new WarrantAllocateEntity();
        warrantAllocateEntity
                .setCustomerName(ttQueryVO.getCustomerName())
                .setCustomerId(ttQueryVO.getCustomerId())
                .setSupplierName(ttQueryVO.getSupplierName())
                .setSupplierId(ttQueryVO.getSupplierId())
                .setTtCode(ttQueryVO.getCode())
                .setTtId(ttQueryVO.getTtId())
                .setContractCode(ttQueryVO.getContractCode())
                .setContractId(ttQueryVO.getContractId())
                .setWarrantCode(warrantEntity.getCode())
                .setOperationType(createWarrantContractDTO.getOperationType())
                .setOperationCount(changeWarrantCount)
                .setStatus(WarrantAllocateEnum.ALLOCATED.getValue())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;
        //记录仓单变更信息
        WarrantModifyEntity warrantModify = new WarrantModifyEntity();
        warrantModify
                .setWarrantCode(warrantEntity.getCode())
                .setModifyType(WarrantOperationTypeEnum.ALLOCATE.getValue().equals(createWarrantContractDTO.getOperationType()) ?
                        WarrantModifyTypeEnum.ALLOCATE.getValue() : WarrantModifyTypeEnum.TRANSFER.getValue())
                .setModifyCount(changeWarrantCount)
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;

        //记录仓单变更数量
        WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = createWarrantCountUpdateLogEntity(warrant, warrantEntity, changeWarrantCount);
        return warrantDO
                .setWarrantModifyEntity(warrantModify)
                .setWarrantAllocateEntity(warrantAllocateEntity)
                .setWarrantEntity(warrantEntity)
                .setWarrantCountUpdateLogEntity(warrantCountUpdateLogEntity);
    }

    //组装TT参数
    private TTDTO createWarrantContractAddTTDTO(AllocateAssignWarrantDTO allocateAssignWarrantDTO, WarrantEntity warrantEntity) {
        TTDTO ttdto = new TTDTO();
        SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();

        //仓单创建TT参数实体
        CreateWarrantContractDTO createWarrantContractDTO = allocateAssignWarrantDTO.getCreateWarrantContractDTO();

        TTTypeEnum ttTypeEnum = WarrantOperationTypeEnum.ALLOCATE.getValue().equals(createWarrantContractDTO.getOperationType()) ? TTTypeEnum.ALLOCATE : TTTypeEnum.ASSIGN;

        //塞入参数
        salesContractAddTTDTO
                .setContractType(String.valueOf(createWarrantContractDTO.getContractType()))
                .setGoodsSpecId(createWarrantContractDTO.getGoodsSpecId())
                .setGoodsPackageId(createWarrantContractDTO.getGoodsPackageId())
                .setType(ttTypeEnum.getType())
                .setCreditDays(createWarrantContractDTO.getCreditDays())
                .setSignDate(createWarrantContractDTO.getSignDate())
                .setPackageWeight(createWarrantContractDTO.getPackageWeight())
                .setContractNum(String.valueOf(createWarrantContractDTO.getChangeWarrantCount()))
                .setDepositRate(createWarrantContractDTO.getDepositRate())
                .setDepositAmount(String.valueOf(createWarrantContractDTO.getDepositAmount()))
                .setDeliveryType(createWarrantContractDTO.getDeliveryType())
                .setDestination(createWarrantContractDTO.getDestination())
                .setUnitPrice(createWarrantContractDTO.getUnitPrice())
                .setWeightCheck(createWarrantContractDTO.getWeightCheck())
                .setShipWarehouseId(createWarrantContractDTO.getShipWarehouseId())
                .setShipWarehouseCode(createWarrantContractDTO.getShipWarehouseCode())
                .setShipWarehouseName(createWarrantContractDTO.getShipWarehouseName())
                .setOem(createWarrantContractDTO.getOem())
                .setMemo(createWarrantContractDTO.getMemo())
                .setIsSoybean2(createWarrantContractDTO.getIsSoybean2())
                .setWarrantTradeType(warrantEntity.getTradeType())
                .setSettleType(createWarrantContractDTO.getSettleType())
                .setAddedDepositRate(createWarrantContractDTO.getAddedDepositRate())
                .setPriceEndType(createWarrantContractDTO.getPriceEndType())
                .setPriceEndTime(createWarrantContractDTO.getPriceEndTime())
                .setWeightTolerance(createWarrantContractDTO.getIsStf())
                .setOwnerId(createWarrantContractDTO.getOwnerId())
                .setWarrantId(warrantEntity.getId())
                .setWarrantTradeType(createWarrantContractDTO.getWarrantTradeType())
                .setDepositUseRule(createWarrantContractDTO.getDepositPaymentType())
                .setWriteOffStartTime(createWarrantContractDTO.getWriteOffStartTime())
                .setWriteOffEndTime(createWarrantContractDTO.getWriteOffEndTime())
                .setDeliveryStartTime(null != createWarrantContractDTO.getDeliveryStartTime() ? createWarrantContractDTO.getDeliveryStartTime() : createWarrantContractDTO.getWriteOffStartTime())
                .setDeliveryEndTime(null != createWarrantContractDTO.getDeliveryEndTime() ? createWarrantContractDTO.getDeliveryEndTime() : createWarrantContractDTO.getWriteOffEndTime())
                .setAddedDepositRate2(createWarrantContractDTO.getAddedDepositRate2())
                .setWeightTolerance(createWarrantContractDTO.getWeightTolerance())
                .setNeedPackageWeight(createWarrantContractDTO.getNeedPackageWeight())
                .setExchangeCode(warrantEntity.getExchangeCode())
                .setWarrantCategory(warrantEntity.getCategory())
                .setWarrantCode(createWarrantContractDTO.getWarrantCode())
                .setCommodityName(createWarrantContractDTO.getCommodityName())
                .setSiteCode(createWarrantContractDTO.getSiteCode())
                .setSiteName(createWarrantContractDTO.getSiteName())
                .setGoodsId(createWarrantContractDTO.getGoodsId())
                .setGoodsName(createWarrantContractDTO.getGoodsName())
                .setCommodityName(createWarrantContractDTO.getCommodityName())
                .setPayConditionId(createWarrantContractDTO.getPayConditionId())
                .setPayConditionCode(createWarrantContractDTO.getPayConditionCode())
                .setCustomerId(createWarrantContractDTO.getCustomerId())
                .setCustomerName(createWarrantContractDTO.getCustomerName())
                .setSupplierId(createWarrantContractDTO.getSupplierId())
                .setSupplierName(createWarrantContractDTO.getSupplierName())
                .setGoodsCategoryId(createWarrantContractDTO.getGoodsCategoryId())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setBuCode(BuCodeEnum.WT.getValue())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setDomainCode(createWarrantContractDTO.getDomainCode())
                .setInvoicePaymentRate(createWarrantContractDTO.getInvoicePaymentRate())
                .setPayConditionId(createWarrantContractDTO.getPayConditionId())
                .setUsage(createWarrantContractDTO.getUsage())
                .setCategory1(createWarrantContractDTO.getCategory1())
                .setCategory2(createWarrantContractDTO.getCategory2())
                .setCategory3(createWarrantContractDTO.getCategory3())
                .setFutureCode(createWarrantContractDTO.getFutureCode())

        ;
        PriceDetailDTO priceDetailDTO = allocateAssignWarrantDTO.getPriceDetailDTO();
        PriceDetailBO priceDetailBO = new PriceDetailBO();
        priceDetailBO
                .setBusinessPrice(new BigDecimal(priceDetailDTO.getBusinessPrice()))
                .setBuyBackPrice(new BigDecimal(priceDetailDTO.getBuyBackPrice()))
                .setComplaintDiscountPrice(new BigDecimal(priceDetailDTO.getComplaintDiscountPrice()))
                .setDelayPrice(new BigDecimal(priceDetailDTO.getDelayPrice()))
                .setExtraPrice(new BigDecimal(priceDetailDTO.getExtraPrice()))
                .setFee(new BigDecimal(priceDetailDTO.getFee()))
                .setForwardPrice(new BigDecimal(priceDetailDTO.getForwardPrice()))
                .setLiftingPrice(new BigDecimal(priceDetailDTO.getLiftingPrice()))
                .setOptionPrice(new BigDecimal(priceDetailDTO.getOptionPrice()))
                .setOtherDeliveryPrice(new BigDecimal(priceDetailDTO.getOtherDeliveryPrice()))
                .setOtherPrice(new BigDecimal(priceDetailDTO.getOtherPrice()))
                .setProteinDiffPrice(new BigDecimal(priceDetailDTO.getProteinDiffPrice()))
                .setRefineDiffPrice(new BigDecimal(priceDetailDTO.getRefineDiffPrice()))
                .setRefineFracDiffPrice(new BigDecimal(priceDetailDTO.getRefineFracDiffPrice()))
                .setShippingFeePrice(new BigDecimal(priceDetailDTO.getShippingFeePrice()))
                .setSurveyFees(new BigDecimal(priceDetailDTO.getSurveyFees()))
                .setTemperaturePrice(new BigDecimal(priceDetailDTO.getTemperaturePrice()))
                .setTransferFactoryPrice(new BigDecimal(priceDetailDTO.getTransferFactoryPrice()))
                .setTransportPrice(new BigDecimal(priceDetailDTO.getTransportPrice()))
                .setCompensationPrice(new BigDecimal(priceDetailDTO.getCompensationPrice()))
        ;
        ttdto.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        ttdto.setTtType(ttTypeEnum.getType());
        ttdto.setBuCode(BuCodeEnum.WT.getValue());
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setSubmitType(SubmitTypeEnum.SUBMIT.getValue());

        return ttdto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWarrantNum(UpdateWarrantNumDTO updateWarrantNumDTO) {

        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        WarrantDO warrantDO = new WarrantDO();
        //根据仓单code查询查询仓单信息
        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByCode(updateWarrantNumDTO.getWarrantCode());
        WarrantEntity warrant = warrantEntity;
        //校验仓单数据是否存在
        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }
        //仓单操作量
        BigDecimal changeWarrantCount = updateWarrantNumDTO.getChangeWarrantCount();

        //数量计算
        WarrantDO warrantDO1 = disposeWarrantCount(updateWarrantNumDTO, warrantEntity, warrantDO);

        //记录仓单变更信息
        WarrantModifyEntity warrantModify = new WarrantModifyEntity();
        warrantModify
                .setWarrantCode(warrantEntity.getCode())
                .setModifyType(updateWarrantNumDTO.getModifyType())
                .setModifyCount(changeWarrantCount)
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;

        //记录仓单变更数量
        WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = createWarrantCountUpdateLogEntity(warrant, warrantEntity, changeWarrantCount);
        warrantDO.setWarrantEntity(warrantEntity)
                .setWarrantModifyEntity(warrantModify)
                .setWarrantCountUpdateLogEntity(warrantCountUpdateLogEntity)
        ;
        warrantDomainServer.updateWarrantDO(warrantDO);


        if (WarrantModifyTypeEnum.CONTRACT_BUYBACK.getValue().equals(updateWarrantNumDTO.getModifyType())) {
            updateWarrantStatus(new WarrantDTO()
                    .setCode(updateWarrantNumDTO.getWarrantCode())
            );
        }
        return true;
    }

    @Override
    public boolean cancelledWarrantContract(CancelledWarrantDTO cancelledWarrantDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByCode(cancelledWarrantDTO.getWarrantCode());
        WarrantEntity warrant = warrantEntity;
        WarrantDO warrantDO = new WarrantDO();

        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }
        //合同注销
        contractCancel(cancelledWarrantDTO, warrantEntity, warrantDO, userId);

        //仓单合同注销量
        BigDecimal changeWarrantCount = cancelledWarrantDTO.getChangeWarrantCount();
        //记录仓单变更信息
        WarrantModifyEntity warrantModify = new WarrantModifyEntity();
        warrantModify
                .setWarrantCode(warrantEntity.getCode())
                .setModifyType(cancelledWarrantDTO.getModifyType())
                .setModifyCount(changeWarrantCount)
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;
        //记录仓单变更数量
        WarrantCountUpdateLogEntity warrantCountUpdateLogEntity = createWarrantCountUpdateLogEntity(warrant, warrantEntity, changeWarrantCount);
        warrantDO.setWarrantEntity(warrantEntity)
                .setWarrantModifyEntity(warrantModify)
                .setWarrantCountUpdateLogEntity(warrantCountUpdateLogEntity)
        ;
        warrantDomainServer.updateWarrantDO(warrantDO);

        return true;
    }

    /**
     * 仓单合同注销
     *
     * @param cancelledWarrantDTO
     * @param warrantEntity
     * @return
     */
    private WarrantDO contractCancel(CancelledWarrantDTO cancelledWarrantDTO, WarrantEntity warrantEntity, WarrantDO warrantDO, Integer userId) {

        //ContractEntity contractEntity = contractFacade.getBasicContractById(cancelledWarrantDTO.getContractId());
        /*if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }*/

        if (ContractSalesTypeEnum.PURCHASE.getValue() == cancelledWarrantDTO.getSalesType()) {

            BigDecimal holdCount = warrantEntity.getHoldCount().subtract(cancelledWarrantDTO.getChangeWarrantCount());
            BigDecimal holdHandCount = holdCount.divide(new BigDecimal(HandRateEnum.WARRANT.getRate()));
            Integer status = warrantEntity.getStatus();
            if (holdHandCount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ENOUGH);
            } else if (holdHandCount.compareTo(BigDecimal.ZERO) == 0) {
                status = WarrantStatusEnum.ALLOCATE_COMPLETED.getValue();
            }
            warrantEntity
                    .setStatus(status)
                    .setHoldCount(holdCount)
                    .setHoldHandCount(holdHandCount)
            ;
        }

        //仓单合同注销
        BigDecimal warrantCancelledCount = warrantEntity.getCancelledCount().add(cancelledWarrantDTO.getChangeWarrantCount());
        warrantEntity.setCancelledCount(warrantCancelledCount);
        //记录仓单合同注销量
        WarrantCancellationEntity warrantCancellationEntity = createWarrantCancellationEntity(cancelledWarrantDTO, userId);

        return warrantDO
                .setCancellationRelationEntities(cancelledWarrantDTO.getCancellationRelationEntities())
                .setWarrantEntity(warrantEntity)
                .setWarrantCancellationEntity(warrantCancellationEntity)
                ;
    }

    /**
     * 仓单合同作废
     *
     * @param updateWarrantNumDTO
     * @param warrantEntity
     * @return
     */
    private WarrantDO disposeWarrantCount(UpdateWarrantNumDTO updateWarrantNumDTO, WarrantEntity warrantEntity, WarrantDO warrantDO) {


        if (WarrantModifyTypeEnum.CONTRACT_CANCEL_REVOKE.getValue().equals(updateWarrantNumDTO.getModifyType())) {
            //仓单合同注销撤回
            BigDecimal warrantCancelledCount = warrantEntity.getCancelledCount().subtract(updateWarrantNumDTO.getChangeWarrantCount());
            warrantEntity.setCancelledCount(warrantCancelledCount);
            if (null != updateWarrantNumDTO.getSalesType() && ContractSalesTypeEnum.PURCHASE.getValue() == updateWarrantNumDTO.getSalesType()) {
                //采购合同注销作废合仓单持有量增加
                BigDecimal holdCount = warrantEntity.getHoldCount().add(updateWarrantNumDTO.getChangeWarrantCount());
                BigDecimal holdHandCount = holdCount.divide(new BigDecimal(HandRateEnum.WARRANT.getRate()));
                warrantEntity
                        .setHoldCount(holdCount)
                        .setHoldHandCount(holdHandCount)
                        .setStatus(WarrantStatusEnum.ALLOCATE_COMPLETED.getValue().equals(warrantEntity.getStatus()) ? WarrantStatusEnum.EFFECTIVE.getValue() : warrantEntity.getStatus())
                ;
            }

            //根据仓单号和合同编号查询出仓单合同注销记录
            //List<WarrantCancellationEntity> warrantCancellationEntities = warrantQueryService.queryWarrantCancellationByWarrantCodeAndDeliveryContractCode(updateWarrantNumDTO.getWarrantCode(), updateWarrantNumDTO.getContractCode());

            WarrantCancellationEntity warrantCancellationEntity = warrantQueryService.queryWarrantCancellationById(updateWarrantNumDTO.getWarrantCancellationId());

            if (null != warrantCancellationEntity) {
                warrantCancellationEntity.setStatus(WarrantCancellationEnum.INVALID.getValue());
                warrantDO.setWarrantCancellationEntity(warrantCancellationEntity);
            }

        } else if (WarrantModifyTypeEnum.CONTRACT_INVALID.getValue().equals(updateWarrantNumDTO.getModifyType())
                || WarrantModifyTypeEnum.CONTRACT_BUYBACK.getValue().equals(updateWarrantNumDTO.getModifyType())
        ) {
            if (null != updateWarrantNumDTO.getSalesType() && ContractSalesTypeEnum.PURCHASE.getValue() == updateWarrantNumDTO.getSalesType()
                    && WarrantModifyTypeEnum.CONTRACT_INVALID.getValue().equals(updateWarrantNumDTO.getModifyType())) {
                //计算仓单数量
                BigDecimal holdCount = warrantEntity.getHoldCount().subtract(updateWarrantNumDTO.getChangeWarrantCount());
                if (holdCount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException(ResultCodeEnum.WARRANT_NOT_ENOUGH);
                }
                BigDecimal holdHandCount = holdCount.divide(new BigDecimal(HandRateEnum.WARRANT.getRate()));

                warrantEntity
                        .setHoldHandCount(holdHandCount)
                        .setHoldCount(holdCount)
                        .setStatus(holdCount.compareTo(BigDecimal.ZERO) == 0 ? WarrantStatusEnum.INVALID.getValue() : warrantEntity.getStatus());
            } else {
                log.info("======================================================================updateWarrantNumDTO.getModifyType():{}", updateWarrantNumDTO.getModifyType());
                //仓单合同回购,仓单合同作废 仓单持有数量增加
                BigDecimal holdCount = warrantEntity.getHoldCount().add(updateWarrantNumDTO.getChangeWarrantCount());
                BigDecimal holdHandCount = holdCount.divide(new BigDecimal(HandRateEnum.WARRANT.getRate()));
                warrantEntity
                        .setHoldCount(holdCount)
                        .setHoldHandCount(holdHandCount)
                ;
                if (WarrantModifyTypeEnum.CONTRACT_INVALID.getValue().equals(updateWarrantNumDTO.getModifyType())) {
                    WarrantStatusEnum warrantStatusEnum = getContractByWarrantCode(updateWarrantNumDTO);
                    log.info("======================================================================warrantStatusEnum:{}", warrantStatusEnum.getValue());
                    warrantEntity.setStatus(warrantStatusEnum.getValue());
                }
            }
        }


        List<WarrantAllocateEntity> warrantAllocateEntities = warrantQueryService.queryWarrantAllocateByWarrantCode(updateWarrantNumDTO.getWarrantCode(), updateWarrantNumDTO.getContractCode());
        if (!warrantAllocateEntities.isEmpty()) {
            WarrantAllocateEntity warrantAllocateEntity = warrantAllocateEntities.get(0);
            warrantAllocateEntity.setStatus(WarrantAllocateEnum.CANCELLED.getValue());
            warrantDO
                    .setWarrantAllocateEntity(warrantAllocateEntity);
        }

        return warrantDO
                .setWarrantEntity(warrantEntity);
    }


    private WarrantStatusEnum getContractByWarrantCode(UpdateWarrantNumDTO updateWarrantNumDTO) {
        log.info("==================================================updateWarrantNumDTO:{}", JSONObject.toJSONString(updateWarrantNumDTO));
        List<ContractEntity> contractEntities = contractFacade.getContractByWarrantCode(updateWarrantNumDTO.getWarrantCode());
        log.info("==================================================contractEntities:{}", JSONObject.toJSONString(contractEntities));
        if (CollectionUtils.isEmpty(contractEntities)) {
            return WarrantStatusEnum.EFFECTIVE;
        }
        for (ContractEntity contractEntity : contractEntities) {

            if (contractEntity.getContractCode().equals(updateWarrantNumDTO.getContractCode())
                    || ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                continue;
            }

            if (ContractStatusEnum.EFFECTIVE.getValue() == contractEntity.getStatus() || ContractStatusEnum.INEFFECTIVE.getValue() == contractEntity.getStatus()) {
                return WarrantStatusEnum.ALLOCATE_UNDERWAY;
            }
        }

        return WarrantStatusEnum.EFFECTIVE;
    }


    private WarrantCancellationEntity createWarrantCancellationEntity(CancelledWarrantDTO cancelledWarrantDTO, Integer userId) {
        WarrantCancellationEntity warrantCancellationEntity = new WarrantCancellationEntity();
        warrantCancellationEntity
                .setIsModifyAll(cancelledWarrantDTO.getIsModifyAll())
                .setCancelCount(cancelledWarrantDTO.getChangeWarrantCount())
                .setSourceContractCode(cancelledWarrantDTO.getContractCode())
                .setSourceContractId(cancelledWarrantDTO.getContractId())
                .setCustomerId(cancelledWarrantDTO.getCustomerId())
                .setCustomerName(cancelledWarrantDTO.getCustomerName())
                .setDeliveryCustomerId(cancelledWarrantDTO.getDeliveryCustomerId())
                .setDeliveryCustomerName(cancelledWarrantDTO.getDeliveryCustomerName())
                .setFutureCode(cancelledWarrantDTO.getFutureCode())
                .setWriteOffDate(cancelledWarrantDTO.getWriteOffDate())
                .setWriteOffAction(cancelledWarrantDTO.getWriteOffAction())
                .setDeliveryGoodsName(cancelledWarrantDTO.getGoodsName())
                .setDeliveryPassword(cancelledWarrantDTO.getDeliveryPassword())
                .setReceiptCode(cancelledWarrantDTO.getWarrantCode())
                .setUpdatedAt(DateTime.now().toTimestamp())
                .setUpdatedBy(employFacade.getEmployById(userId).getName())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setCreatedBy(employFacade.getEmployById(userId).getName())
        ;
        return warrantCancellationEntity;
    }

    @Override
    public VerifyWarrantFileReturnVO uploadingFileWarrant(MultipartFile file) {
        List<WarrantExcelDTO> warrantExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, WarrantExcelDTO.class);

        VerifyWarrantFileReturnVO verifyWarrantFileReturnVO = new VerifyWarrantFileReturnVO();

        Integer successNum = 0;
        Integer failNum = 0;
        Integer totalNum = 0;

        List<WarrantExcelDTO> warrantExcels = new ArrayList<>();

        List<CategoryEntity> categoryEntities = categoryFacade.getAllCategoryList(2);

        for (WarrantExcelDTO warrantExcelDTO : warrantExcelDTOS) {
            //判断warrantExcelDTO对象中的值是否全部为空
            if (warrantExcelDTO.isAllEqual()) {
                continue;
            }
            totalNum++;

            WarrantExcelDTO warrantExcel = warrantDataIntegrityCheck(warrantExcelDTO, categoryEntities);

            if (null != warrantExcel) {
                failNum++;
                warrantExcels.add(warrantExcelDTO);
            } else {
                //整合warrantEntity数据
                WarrantEntity warrantEntity = prepareWarrantFileInfo(warrantExcelDTO, categoryEntities);

                submitWarrant(warrantEntity);
                successNum++;
            }
        }
        verifyWarrantFileReturnVO
                .setWarrantExcelDTOS(warrantExcels)
                .setTotalNum(totalNum)
                .setFailNum(failNum)
                .setSuccessNum(successNum);

        return verifyWarrantFileReturnVO;
    }

    //整合warrantEntity数据
    private WarrantEntity prepareWarrantFileInfo(WarrantExcelDTO warrantExcelDTO, List<CategoryEntity> categoryEntities) {
        WarrantEntity warrantEntity = new WarrantEntity();
        //根据注册放查询注册方信息
        CompanyEntity companyEntity = companyFacade.queryCompanyByName(warrantExcelDTO.getRegisteName());
        //根据二级品类名称查询品类信息
        CategoryEntity categoryEntity = categoryEntities.stream().filter(categoryEntity1 -> categoryEntity1.getName().equals(warrantExcelDTO.getCategory2())).findFirst().orElse(null);
        //根据价格库名称查询交割库信息
        if (StringUtils.isEmpty(warrantEntity.getWarehouseCode())) {
            String warehouseCode = factoryConvertValue(warrantEntity.getWarehouseName());
            warrantEntity.setWarehouseCode(warehouseCode);
        }

        //根据账套编码账套信息
        SiteEntity siteEntity = siteFacade.getSiteDetailByCode(warrantExcelDTO.getSiteCode());
        warrantEntity
                .setCategory1(null == categoryEntity ? "" : String.valueOf(categoryEntity.getParentId()))
                .setCategory2(null == categoryEntity ? "" : String.valueOf(categoryEntity.getId()))
                .setProperty(WarrantPropertyEnum.OWN_WARRANT.getValue())
                .setWarehouseName(warrantExcelDTO.getWarehouseName())
                .setSiteCode(warrantExcelDTO.getSiteCode())
                .setSiteName(siteEntity.getName())
                .setRegisteId(companyEntity.getId())
                .setRegisteName(warrantExcelDTO.getRegisteName())
                .setFutureCode(warrantExcelDTO.getCategoryCode())
                .setRegisteHandCount(new BigDecimal(warrantExcelDTO.getRegisteHandCount()))
                .setCategory(WarrantCategoryEnum.getDesc(warrantExcelDTO.getCategory()).getValue())
                .setDepositPaymentType(WarrantPaymentTypeEnum.getDesc(warrantExcelDTO.getDepositPaymentType()).getValue())
                .setDepositAmount(new BigDecimal(warrantExcelDTO.getDepositAmount()))
                .setMemo(warrantExcelDTO.getMemo())
        ;


        return warrantEntity;
    }

    @Override
    public VerifyWarrantFileReturnVO verifyFileWarrant(MultipartFile file) {
        List<WarrantExcelDTO> warrantExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, WarrantExcelDTO.class);
        VerifyWarrantFileReturnVO verifyWarrantFileReturnVO = new VerifyWarrantFileReturnVO();

        Integer successNum = 0;
        Integer failNum = 0;
        Integer totalNum = 0;

        List<WarrantExcelDTO> warrantExcelDTOList = new ArrayList<>();
        List<CategoryEntity> categoryEntities = categoryFacade.getAllCategoryList(2);
        for (WarrantExcelDTO warrantExcelDTO : warrantExcelDTOS) {
            //判断warrantExcelDTO对象中的值是否全部为空
            if (warrantExcelDTO.isAllEqual()) {
                continue;
            }
            totalNum++;
            WarrantExcelDTO warrantExcel = warrantDataIntegrityCheck(warrantExcelDTO, categoryEntities);
            if (null != warrantExcel) {
                warrantExcelDTOList.add(warrantDataIntegrityCheck(warrantExcelDTO, categoryEntities));
                failNum++;
            } else {
                successNum++;

            }
        }

        verifyWarrantFileReturnVO
                .setSuccessNum(successNum)
                .setFailNum(failNum)
                .setTotalNum(totalNum)
                .setWarrantExcelDTOS(warrantExcelDTOList);

        return verifyWarrantFileReturnVO;
    }

    @Override
    public boolean updateWarrantStatus(WarrantDTO warrantDTO) {
        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByCode(warrantDTO.getCode());
        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }
        Integer status = WarrantStatusEnum.EFFECTIVE.getValue();
        List<ContractEntity> contractEntities = contractFacade.getContractByWarrantCode(warrantEntity.getCode());
        for (ContractEntity contractEntity : contractEntities) {
            if (ContractStatusEnum.INEFFECTIVE.getValue() == contractEntity.getStatus()
                    && ContractActionEnum.ALLOCATE.getActionValue() == contractEntity.getContractSource()
                    && ContractActionEnum.ASSIGN.getActionValue() == contractEntity.getContractSource()
            ) {
                status = WarrantStatusEnum.ALLOCATE_UNDERWAY.getValue();
            }
        }

        if (warrantEntity.getHoldCount().compareTo(BigDecimal.ZERO) == 0) {
            status = WarrantStatusEnum.ALLOCATE_COMPLETED.getValue();
        }

        warrantEntity.setStatus(status);
        WarrantDO warrantDO = new WarrantDO().setWarrantEntity(warrantEntity);
        warrantDomainServer.updateWarrantDO(warrantDO);
        return true;
    }


    @Override
    public boolean deleteWarrant(Integer id) {
        WarrantEntity warrantEntity = warrantQueryService.queryWarrantByID(id);
        if (null == warrantEntity) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_EXIST);
        }
        if (!WarrantStatusEnum.DRAFT.getValue().equals(warrantEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.WARRANT_NOT_DRAFT);
        }
        warrantEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
        warrantDomainServer.updateWarrantDO(new WarrantDO().setWarrantEntity(warrantEntity));

        return true;
    }

    @Override
    public WarrantCancellationEntity queryWarrantCancellation(String contractCode) {
        return warrantQueryService.queryWarrantCancellation(contractCode);
    }


    /**
     * 仓单完整性校验
     *
     * @param warrantExcelDTO
     */
    private WarrantExcelDTO warrantDataIntegrityCheck(WarrantExcelDTO warrantExcelDTO, List<CategoryEntity> categoryEntities) {

        //todo 添加账套+主体+二级品类校验 wan

        if (StringUtils.isEmpty(warrantExcelDTO.getSiteCode())) {
            warrantExcelDTO.setFailReason("账套编码为空");
            return warrantExcelDTO;
        }
        //根据账套编码账套信息
        SiteEntity siteEntity = siteFacade.getSiteDetailByCode(warrantExcelDTO.getSiteCode());
        if (null == siteEntity) {
            warrantExcelDTO.setFailReason("未匹配到账套编码");
            return warrantExcelDTO;
        }

        //注册方名称为空
        if (StringUtils.isEmpty(warrantExcelDTO.getRegisteName())) {
            warrantExcelDTO.setFailReason("注册方名称为空");
            return warrantExcelDTO;
        }

        //二级品类为空
        if (StringUtils.isEmpty(warrantExcelDTO.getCategory2())) {
            warrantExcelDTO.setFailReason("二级品类为空");
            return warrantExcelDTO;
        }
        CategoryEntity categoryEntity = categoryEntities.stream().filter(categoryEntity1 -> categoryEntity1.getName().equals(warrantExcelDTO.getCategory2())).findFirst().orElse(null);
        if (null == categoryEntity) {
            warrantExcelDTO.setFailReason("二级品类不正确");
            return warrantExcelDTO;
        }
        //品种代码为空
        if (StringUtils.isEmpty(warrantExcelDTO.getCategoryCode())) {
            warrantExcelDTO.setFailReason("品种代码为空");
            return warrantExcelDTO;
        }
        //注册手数为空
        if (StringUtils.isEmpty(warrantExcelDTO.getRegisteHandCount())) {
            warrantExcelDTO.setFailReason("注册手数为空");
            return warrantExcelDTO;
        }
        if (!warrantExcelDTO.getRegisteHandCount().matches("^-?\\d+$")) {
            warrantExcelDTO.setFailReason("申请数量不是整数");
            return warrantExcelDTO;
        }
        //交割库为空
        if (StringUtils.isEmpty(warrantExcelDTO.getWarehouseName())) {
            warrantExcelDTO.setFailReason("交割库为空");
            return warrantExcelDTO;
        }

        String warehouseCode = factoryConvertValue(warrantExcelDTO.getWarehouseName());
        if (StringUtils.isEmpty(warehouseCode)) {
            warrantExcelDTO.setFailReason("根据名称未查询到交割库信息!");
            return warrantExcelDTO;
        }

        //仓单类型为空
        if (StringUtils.isEmpty(warrantExcelDTO.getCategory())) {
            warrantExcelDTO.setFailReason("仓单类型为空");
            return warrantExcelDTO;
        }
        if (null == WarrantCategoryEnum.getDesc(warrantExcelDTO.getCategory())) {
            warrantExcelDTO.setFailReason("仓单类型不正确");
            return warrantExcelDTO;
        }
        //交割保证金付款方式为空
        if (StringUtils.isEmpty(warrantExcelDTO.getDepositPaymentType())) {
            warrantExcelDTO.setFailReason("交割保证金付款方式为空");
            return warrantExcelDTO;
        }
        if (null == WarrantPaymentTypeEnum.getDesc(warrantExcelDTO.getDepositPaymentType())) {
            warrantExcelDTO.setFailReason("交割保证金付款方输入不正确");
            return warrantExcelDTO;
        }
        //交割保证金金额为空
        if (StringUtils.isEmpty(warrantExcelDTO.getDepositAmount())) {
            warrantExcelDTO.setFailReason("交割保证金金额为空");
            return warrantExcelDTO;
        }

        if (!warrantExcelDTO.getDepositAmount().matches("^([-+])?\\d+(\\.\\d+)?$")) {
            warrantExcelDTO.setFailReason("交割保证金金额非整数或小数类型");
            return warrantExcelDTO;
        }

        CompanyEntity companyEntity = companyFacade.queryCompanyByName(warrantExcelDTO.getRegisteName());
        if (null == companyEntity) {
            warrantExcelDTO.setFailReason("注方不存在");
            return warrantExcelDTO;
        }


        if (!siteEntity.getCompanyId().equals(companyEntity.getId())) {
            warrantExcelDTO.setFailReason("账套和注册放未绑定");
            return warrantExcelDTO;
        }

        List<Integer> category2List = Arrays.stream(siteEntity.getCategory2().split(",")).map(category2 -> Integer.parseInt(category2.trim())).collect(Collectors.toList());
        if (!category2List.contains(categoryEntity.getId())) {
            warrantExcelDTO.setFailReason("账套和二级品类未绑定");
            return warrantExcelDTO;
        }
        WarehouseEntity warehouseEntity = new WarehouseEntity();
        Result<WarehouseEntity> result = warehouseFacade.getDceWarehouseByName(warrantExcelDTO.getWarehouseName());
        if (result.isSuccess()) {
            warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
        }
        if (ObjectUtil.isNotEmpty(warehouseEntity)) {
            List<String> list = Arrays.stream(warehouseEntity.getSiteCodes().split(",")).map(String::trim).collect(Collectors.toList());
            if (!list.contains(siteEntity.getCode())) {
                warrantExcelDTO.setFailReason("交割库和账套未绑定");
                return warrantExcelDTO;
            }
        }
        //查询二级品种下的三级品类
        List<CategoryEntity> categoryEntity3List = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(categoryEntity.getId()));
        List<String> futureCodeList = categoryEntity3List.stream().map(CategoryEntity::getFutureCode).collect(Collectors.toList());
        //校验warrantExcelDTO.getFutureCode是否在futureCodeList
        if (!futureCodeList.contains(warrantExcelDTO.getCategoryCode())) {
            warrantExcelDTO.setFailReason("该品种下没未使用'" + warrantExcelDTO.getCategoryCode() + "'代码");
            return warrantExcelDTO;
        }
        return null;
    }

    /**
     * 仓单日志
     */
    private void createWarrantLog(WarrantLogDTO warrantLogDTO) {
        // 记录日志
        try {
            operationLogFacade.recordOperationLog(
                    new OperationDetailDTO()
                            .setBizCode(warrantLogDTO.getLogBizCodeEnum().getBizCode())
                            .setBizModule(ModuleTypeEnum.PRICE.getModule())
                            .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                            .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                            .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                            .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                            .setOperationName(warrantLogDTO.getLogBizCodeEnum().getMsg())
                            .setReferBizId(warrantLogDTO.getWarrantId())
                            .setReferBizCode(warrantLogDTO.getWarrantCode())
                            .setMetaData(JSON.toJSONString(warrantLogDTO.getWarrantEntity()))
                            .setData(JSON.toJSONString(warrantLogDTO.getData()))
                            .setTriggerSys(SystemEnum.MAGELLAN.getDescription())
            );
        } catch (Exception e) {
            log.debug(e.getMessage());
        }
    }

    public String factoryConvertValue(String name) {
        if (ObjectUtil.isNotEmpty(name)) {
            Result<WarehouseEntity> result = warehouseFacade.getDceWarehouseByName(name);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getCode() : "";
            }
        }
        return "";
    }
}
