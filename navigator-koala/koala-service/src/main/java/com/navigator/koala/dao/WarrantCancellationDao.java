package com.navigator.koala.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.koala.mapper.WarrantCancellationMapper;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;

import java.sql.Wrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */
@Dao
public class WarrantCancellationDao extends BaseDaoImpl<WarrantCancellationMapper, WarrantCancellationEntity> {

    public List<WarrantCancellationEntity> queryWarrantCancellationByReceiptCode(String receiptCode) {
        return this.list(Wrappers.<WarrantCancellationEntity>lambdaQuery()
                .eq(WarrantCancellationEntity::getReceiptCode, receiptCode)
        );
    }

    public List<WarrantCancellationEntity> queryWarrantCancellationByContractCode(String contractCode) {
        return this.list(Wrappers.<WarrantCancellationEntity>lambdaQuery()
                .eq(WarrantCancellationEntity::getSourceContractCode, contractCode)
        );
    }

    public List<WarrantCancellationEntity> queryWarrantCancellationByWarrantCodeAndDeliveryContractCode(String warrantCode, String deliveryContractCode){
        return this.list(Wrappers.<WarrantCancellationEntity>lambdaQuery()
                .eq(WarrantCancellationEntity::getReceiptCode, warrantCode)
                .eq(WarrantCancellationEntity::getDeliveryContractCode, deliveryContractCode)
        );
    }
}
