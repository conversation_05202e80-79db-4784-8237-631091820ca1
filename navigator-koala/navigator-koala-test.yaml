apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-koala-test
  namespace: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-koala-test
  template:
    metadata:
      labels:
        app: ldc-navigator-koala-test
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-koala-test:#{Build.BuildId}#
        name: ldc-navigator-koala-test
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "uat" 

---
apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-koala-test
  namespace: test
spec:
  type: ClusterIP
  ports:
  - port: 9020
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-koala-test

