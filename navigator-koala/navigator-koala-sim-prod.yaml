apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-koala-sim-prod
  namespace: sim-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-koala-sim-prod
  template:
    metadata:
      labels:
        app: ldc-navigator-koala-sim-prod
    spec:
      containers:
      - image: csm4pnvgacr001.azurecr.cn/navigator-koala-sim-prod:#{Build.BuildId}#
        name: ldc-navigator-koala-sim-prod
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "sim-prod"
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4pnvgsto002-secret  # required
            shareName: logs-sim-prod  # required
            server: csm4pnvgsto002.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional

---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-koala-sim-prod
  namespace: sim-prod
spec:
  type: ClusterIP
  ports:
  - port: 9120
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-koala-sim-prod