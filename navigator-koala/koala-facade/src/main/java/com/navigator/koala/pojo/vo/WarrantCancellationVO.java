package com.navigator.koala.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/25
 */
@Data
@Accessors(chain = true)
public class WarrantCancellationVO extends WarrantCancellationEntity {

    //交割库
    private String deliveryWarehouse;
    //含税单价
    private BigDecimal unitPrice;
    //期货编号
    private String futureCode;
    //期货合约
    private String domainCode;
    //提货方式
    private String deliveryType;
    //目的港
    private String destination;
    //仓单交易类型
    private String warrantTradeType;
    //签订日期
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;
    //注销说明
    private String memo;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    //销售合同信息集合
    List<CancellationRelationVO> cancellationRelationVOList;


}
