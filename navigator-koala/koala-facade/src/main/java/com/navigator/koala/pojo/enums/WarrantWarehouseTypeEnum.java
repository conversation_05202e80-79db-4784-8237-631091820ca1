package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */

@AllArgsConstructor
@Getter
public enum WarrantWarehouseTypeEnum {

    FACTORY_WAREHOUSE(1, "厂库"),
    ASSIGN_WAREHOUSE(2, "指定交割仓库"),
    TRANSFER_WAREHOUSE(3, "中转交割库"),
    TAX_WAREHOUSE(4, "报税交割库"),
    OTHER_WAREHOUSE(5, "其他非标交割库"),
    ;

    private Integer value;
    private String desc;
}
