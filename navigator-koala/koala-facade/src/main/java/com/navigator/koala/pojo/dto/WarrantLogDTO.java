package com.navigator.koala.pojo.dto;

import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.koala.pojo.entity.WarrantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */

@Data
@Accessors(chain = true)
public class WarrantLogDTO {

    //仓单id
    private Integer warrantId;
    //仓单code
    private String warrantCode;
    //操作类型
    LogBizCodeEnum logBizCodeEnum;
    //操作人id
    private Integer operatorId;
    //操作人名称
    private String operatorName;
    @ApiModelProperty(value = "原数据Json")
    private WarrantEntity warrantEntity;
    @ApiModelProperty(value = "记录的数据，一般为接口参数")
    private String data;


}
