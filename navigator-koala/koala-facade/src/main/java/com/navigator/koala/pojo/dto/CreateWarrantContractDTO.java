package com.navigator.koala.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@Data
@Accessors(chain = true)
public class CreateWarrantContractDTO {

    @ApiModelProperty(value = "TTId")
    private Integer ttId;

    @ApiModelProperty(value = "TTCode")
    private String ttCode;


    @ApiModelProperty(value = "仓单id")
    private Integer warrantId;

    @ApiModelProperty(value = "仓单code")
    private String warrantCode;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "买方主体id")
    private Integer customerId;

    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @ApiModelProperty(value = "卖方主体id")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    @Min(0)
    private String goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    @Min(0)
    private String goodsSpecId;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重名称")
    private String packageWeightName;

    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;


    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "注销开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "提货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "提货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "点价截止日期类型（1时间 2文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;
    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    private Integer isStf;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交易所code")
    private String exchangeCode;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    private String ownerId;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal changeWarrantCount;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "保证金比例id")
    private Integer depositRateId;

    @ApiModelProperty(value = "保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "应付履约保证金")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "交货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "目的地/港")
    private String destination;

    @ApiModelProperty(value = "含税单价（系统默认由以下字段输入数字相加而来： 基差价 期货价格 蛋白价差 散粕补贴 期权费 其他物流费用 运费。起吊费用；达孚待付，客户自付。滞期费。高温费 回购折价 客诉折价 转厂补贴 其他补贴 商务补贴 手续费 DCE交割：只有交割结算价）")
    private String unitPrice;

    @ApiModelProperty(value = "重量检验")
    private String weightCheck;

    @ApiModelProperty(value = "重量检验标准名称")
    private String weightCheckName;

    @ApiModelProperty(value = "交割库id")
    private String shipWarehouseId;

    @ApiModelProperty(value = "交割库编码")
    private String shipWarehouseCode;

    @ApiModelProperty(value = "交割库名称")
    private String shipWarehouseName;

    @ApiModelProperty(value = "代加工，默认否（0、否 1、是）")
    private Integer oem;

    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "备注")
    private String memo;


}
