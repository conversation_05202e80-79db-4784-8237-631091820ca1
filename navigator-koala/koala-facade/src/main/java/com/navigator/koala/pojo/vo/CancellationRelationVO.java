package com.navigator.koala.pojo.vo;

import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/21
 */
@Data
@Accessors(chain = true)
public class CancellationRelationVO extends CancellationRelationEntity {


    @ApiModelProperty(value = "注销货品名称")
    private String commodityName;

    @ApiModelProperty(value = "交割库")
    private String deliveryWarehouse;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "期货编号")
    private String futureCode;

    @ApiModelProperty(value = "交割合约")
    private String domainCode;

    @ApiModelProperty(value = "注销数量")
    private BigDecimal cancelCount;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "提货方式")
    private String deliveryType;

    @ApiModelProperty(value = "目的港")
    private String destination;

    @ApiModelProperty(value = "重量验收标准")
    private String weightCriteria;


}
