package com.navigator.koala.pojo.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */

@Data
@Accessors(chain = true)
public class QueryWarrantBO {

    @ApiModelProperty(value = "注册方id")
    private Integer registeId;

    @ApiModelProperty(value = "创建开始日期")
    private String createStartTime;

    @ApiModelProperty(value = "创建结束日期")
    private String createEndTime;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "交割库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "交割合约")
    private String domainCode;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "仓单类型")
    private Integer category;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "品种id")
    private String goodsCategoryId;

    @ApiModelProperty(value = "仓单编号")
    private String code;

    @ApiModelProperty(value = "分配/转让量>0")
    private Integer allocateNumType;

    private List<String> siteCodeList;
}
