package com.navigator.koala.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbk_warrant_allocate")
@ApiModel(value = "WarrantAllocateEntity对象", description = "仓单分配转让记录")
public class WarrantAllocateEntity implements Serializable {

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal operationCount;

    @ApiModelProperty(value = "ttId")
    private Integer ttId;

    @ApiModelProperty(value = "tt编号")
    private String ttCode;

    @ApiModelProperty(value = "仓单合同Id")
    private Integer contractId;

    @ApiModelProperty(value = "仓单合同编号")
    private String contractCode;

    @ApiModelProperty(value = "卖方主体Id")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;

    @ApiModelProperty(value = "买方主体Id")
    private Integer customerId;

    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    //仓单类型
    @TableField(exist = false)
    private Integer warrantTradeType;

    //账套名称
    @TableField(exist = false)
    private String siteName;
}
