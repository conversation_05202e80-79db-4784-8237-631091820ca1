package com.navigator.koala.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */
@Data
@Accessors(chain = true)
public class WarrantDTO {

    @ApiModelProperty(value = "仓单id")
    private Integer id;

    //仓单code
    private String code;

    //注销数量
    private BigDecimal changeWarrantCount;

    //注销类型
    private Integer cancelType;

    //仓单状态
    /**
     * {@link com.navigator.koala.pojo.enums.WarrantStatusEnum}
     */
    private Integer status;
}
