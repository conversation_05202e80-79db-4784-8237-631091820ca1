package com.navigator.koala.pojo.vo;

import com.navigator.koala.pojo.dto.WarrantExcelDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
@Data
@Accessors(chain = true)
public class VerifyWarrantFileReturnVO {

    //成功数量
    private Integer successNum;
    //失败数量
    private Integer failNum;
    //总数
    private Integer totalNum;

    List<WarrantExcelDTO> warrantExcelDTOS;
}
