package com.navigator.koala.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/18
 */

@Data
@Accessors(chain = true)
public class CancelledWarrantDTO {

    /**
     * {@link WarrantModifyTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private Integer modifyType;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal changeWarrantCount;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "是否修改货品&提货方")
    private Integer isModifyAll;

    @ApiModelProperty(value = "仓单合同id")
    private Integer contractId;

    @ApiModelProperty(value = "仓单合同号")
    private String contractCode;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "提货合同客户编码")
    private Integer deliveryCustomerId;

    @ApiModelProperty(value = "提货合同客户编码")
    private String deliveryCustomerCode;

    @ApiModelProperty(value = "提货合同客户名称")
    private String deliveryCustomerName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "提货密码")
    private String deliveryPassword;

    @ApiModelProperty(value = "注销说明")
    private String memo;

    // zengshl 补充
    @ApiModelProperty(value = "注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同; 豆二注销：不修改提货方，修改提货方")
    private int writeOffAction;

    @ApiModelProperty(value = "注销日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDate;

    @ApiModelProperty(value = "仓单注销记录关联表")
    List<CancellationRelationEntity> cancellationRelationEntities;

}
