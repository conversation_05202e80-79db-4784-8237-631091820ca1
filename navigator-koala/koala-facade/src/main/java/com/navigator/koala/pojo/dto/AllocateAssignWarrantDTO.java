package com.navigator.koala.pojo.dto;

import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.ContractPriceDTO;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@Data
@Accessors(chain = true)
public class AllocateAssignWarrantDTO {

    //创建仓单合同信息实体
    private CreateWarrantContractDTO createWarrantContractDTO;
    //价格实体
    private PriceDetailDTO priceDetailDTO;

}
