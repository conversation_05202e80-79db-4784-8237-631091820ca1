package com.navigator.koala.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbk_warrant_cancellation")
@ApiModel(value = "WarrantCancellationEntity对象", description = "仓单注销记录表")
public class WarrantCancellationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "仓单编号")
    private String receiptCode;

    @ApiModelProperty(value = "合同编号")
    private String sourceContractCode;

    @ApiModelProperty(value = "合同编号Id")
    private Integer sourceContractId;

    @ApiModelProperty(value = "所属提货合同")
    private String deliveryContractCode;

    @ApiModelProperty(value = "所属提货合同Id")
    private Integer deliveryContractId;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购合同编号Id")
    private Integer purchaseContractId;

    @ApiModelProperty(value = "注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同 4. 豆二注销不修改提货方 5. 豆二注销修改提货方")
    private int writeOffAction;

    @ApiModelProperty(value = "是否同时修改货品和提货方")
    private Integer isModifyAll;

    @ApiModelProperty(value = "注销数量")
    private BigDecimal cancelCount;

    @ApiModelProperty(value = "提货密码")
    private String deliveryPassword;

    @ApiModelProperty(value = "所属客户code")
    private String customerCode;

    @ApiModelProperty(value = "所属客户Id")
    private Integer customerId;

    @ApiModelProperty(value = "所属客户名称")
    private String customerName;

    @ApiModelProperty(value = "提货客户code")
    private String deliveryCustomerCode;

    @ApiModelProperty(value = "提货客户Id")
    private Integer deliveryCustomerId;

    @ApiModelProperty(value = "提货客户名称")
    private String deliveryCustomerName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "注销合约")
    private String domainCode;

    @ApiModelProperty(value = "提货商品名称")
    private String deliveryGoodsName;

    @ApiModelProperty(value = "提货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "提货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "注销日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDate;

    @ApiModelProperty(value = "提货类型")
    private String deliveryType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
