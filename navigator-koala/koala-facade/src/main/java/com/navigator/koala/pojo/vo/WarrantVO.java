package com.navigator.koala.pojo.vo;

import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.koala.pojo.entity.WarrantAllocateEntity;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.entity.WarrantModifyEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17
 */

@Data
@Accessors(chain = true)
public class WarrantVO extends WarrantEntity {

    //已分配转让数量
    private BigDecimal allocatedTransferCount;

    //仓单LDC注销记录
    List<WarrantModifyEntity> warrantLdcCancelList;
    //仓单分配记录
    List<WarrantAllocateEntity> warrantAllocateEntities;
    //仓单合同注销记录
    List<WarrantCancellationVO> warrantCancellationVOS;
    //仓单操作记录
    List<OperationDetailEntity> operationDetailEntities;

}
