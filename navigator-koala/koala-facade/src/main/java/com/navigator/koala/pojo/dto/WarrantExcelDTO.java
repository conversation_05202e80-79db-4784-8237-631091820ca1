package com.navigator.koala.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */

@Data
@Accessors(chain = true)
public class WarrantExcelDTO {

    //账套编码
    @Excel(name = "账套编码", orderNum = "1", width = 15)
    private String siteCode;

    @Excel(name = "账套名称", orderNum = "2", width = 15)
    private String siteName;

    @Excel(name = "注册方", orderNum = "3", width = 15)
    private String registeName;

    @Excel(name = "二级品类", orderNum = "4", width = 15)
    private String category2;

    @Excel(name = "品种代码", orderNum = "5", width = 15)
    private String categoryCode;

    @Excel(name = "区域", orderNum = "6", width = 15)
    private String area;

    @Excel(name = "注册数量（手）", orderNum = "7", width = 15)
    private String registeHandCount;

    @Excel(name = "交割库", orderNum = "8", width = 15)
    private String warehouseName;

    @Excel(name = "仓单类型", orderNum = "9", width = 15)
    private String category;

    @Excel(name = "交割保证金付款方式", orderNum = "10", width = 15)
    private String depositPaymentType;

    @Excel(name = "交割保证金金额", orderNum = "11", width = 15)
    private String depositAmount;

    @Excel(name = "备注", orderNum = "12", width = 15)
    private String memo;

    //失败原因
    private String failReason;

    public boolean isAllEqual() {
        return StringUtils.isEmpty(siteCode)
                //&& StringUtils.isEmpty(siteName)
                && StringUtils.isEmpty(registeName)
                && StringUtils.isEmpty(category2)
                && StringUtils.isEmpty(categoryCode)
                && StringUtils.isEmpty(area)
                && StringUtils.isEmpty(registeHandCount)
                && StringUtils.isEmpty(warehouseName)
                && StringUtils.isEmpty(category)
                && StringUtils.isEmpty(depositPaymentType)
                && StringUtils.isEmpty(depositAmount);
    }

}
