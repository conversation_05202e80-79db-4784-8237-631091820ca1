package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */

@AllArgsConstructor
@Getter
public enum WarrantPropertyEnum {

    //    OTHER(0,"其他"),
    OWN_WARRANT(1, "自有仓单"),
    WARRANT(2, "仓单"),
    ;

    private Integer value;
    private String desc;

    public static WarrantPropertyEnum getDesc(String desc) {
        for (WarrantPropertyEnum value : WarrantPropertyEnum.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return OWN_WARRANT;
    }

    public static WarrantPropertyEnum getValue(Integer value) {
        for (WarrantPropertyEnum value1 : WarrantPropertyEnum.values()) {
            if (value1.value.equals(value)) {
                return value1;
            }
        }
        return OWN_WARRANT;
    }

    public static String getDescByValue(Integer value) {
        return getValue(value).getDesc();
    }
}
