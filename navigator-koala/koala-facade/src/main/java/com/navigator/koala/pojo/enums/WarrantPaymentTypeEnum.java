package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */

@AllArgsConstructor
@Getter
public enum WarrantPaymentTypeEnum {

    OTHER(0, "其他"),
    BANK_GUARANTEE(1, "保函"),
    CASH(2, "现金"),
    ;

    private Integer value;
    private String desc;


    public static WarrantPaymentTypeEnum getDesc(String desc) {
        for (WarrantPaymentTypeEnum warrantPaymentTypeEnum : WarrantPaymentTypeEnum.values()) {
            if (warrantPaymentTypeEnum.getDesc().equals(desc)) {
                return warrantPaymentTypeEnum;
            }
        }
        return OTHER;
    }

    public static WarrantPaymentTypeEnum getValue(Integer value) {
        for (WarrantPaymentTypeEnum warrantPaymentTypeEnum : WarrantPaymentTypeEnum.values()) {
            if (warrantPaymentTypeEnum.getValue().equals(value)) {
                return warrantPaymentTypeEnum;
            }
        }
        return OTHER;
    }
}
