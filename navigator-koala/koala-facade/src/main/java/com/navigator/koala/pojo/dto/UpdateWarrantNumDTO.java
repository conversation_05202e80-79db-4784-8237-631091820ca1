package com.navigator.koala.pojo.dto;

import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/29
 */

@Data
@Accessors(chain = true)
public class UpdateWarrantNumDTO {

    /**
     * {@link WarrantModifyTypeEnum}
     */
    private Integer modifyType;
    //仓单编号
    private String warrantCode;
    //操作数量
    private BigDecimal changeWarrantCount;
    //合同Id
    private Integer contractId;
    //合同编号
    private String contractCode;
    //注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同
    private String writeOffAction;
    //注销记录id
    private Integer warrantCancellationId;
    //合同销售类型（1.采购 2.销售）
    private Integer salesType;
}
