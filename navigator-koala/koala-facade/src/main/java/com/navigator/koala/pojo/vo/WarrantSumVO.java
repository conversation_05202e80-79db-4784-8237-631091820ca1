package com.navigator.koala.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20
 */

@Data
@Accessors(chain = true)
public class WarrantSumVO {

    @ApiModelProperty(value = "品种名称")
    private String categoryName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "仓单数量(手)")
    private BigDecimal warrantNum;

    @ApiModelProperty(value = "自有仓单持有量")
    private BigDecimal ownHoldHandCount;

    @ApiModelProperty(value = "仓单持有量")
    private BigDecimal holdHandCount;

    @ApiModelProperty(value = "可分配/转让量")
    private BigDecimal allocateNum;

}
