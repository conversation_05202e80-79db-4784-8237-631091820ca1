package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */

@AllArgsConstructor
@Getter
public enum WarrantCategoryEnum {
    //仓单类型 1，工厂仓单 2，仓库仓单
//    OTHER(0, "其他", "OTHER"),
    FACTORY(1, "厂库仓单", "FACTORY"),
    WAREHOUSE(2, "仓库仓单", "WAREHOUSE");

    private Integer value;
    private String desc;
    private String code;


    public static WarrantCategoryEnum getValue(Integer value) {
        for (WarrantCategoryEnum e : WarrantCategoryEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return FACTORY;
    }

    public static String getDescByValue(Integer value) {
        return getValue(value).getDesc();
    }


    public static WarrantCategoryEnum getDesc(String desc) {
        for (WarrantCategoryEnum e : WarrantCategoryEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e;
            }
        }
        return FACTORY;
    }


}
