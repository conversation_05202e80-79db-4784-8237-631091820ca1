package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20
 */

@AllArgsConstructor
@Getter
public enum WarrantStatusEnum {

    DRAFT(0, "草稿"),
    EFFECTIVE(1, "生效"),
    ALLOCATE_UNDERWAY(2, "分配中/转让中"),
    ALLOCATE_COMPLETED(3, "已完成"),
    INEFFECTIVE(4, "未生效"),
    INVALID(15, "已作废"),
    ;

    private Integer value;
    private String desc;

    public static WarrantStatusEnum getDesc(String desc) {
        for (WarrantStatusEnum value : WarrantStatusEnum.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return DRAFT;
    }

    public static WarrantStatusEnum getValue(Integer value) {
        for (WarrantStatusEnum value1 : WarrantStatusEnum.values()) {
            if (value1.value.equals(value)) {
                return value1;
            }
        }
        return DRAFT;
    }
}
