package com.navigator.koala.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/29
 */

@Data
@Accessors(chain = true)
public class DownloadWarrantExcelDTO {

    @Excel(name = "注册/采购方", orderNum = "1", width = 15)
    private String registeName;

    @Excel(name = "注册号", orderNum = "2", width = 15)
    private String warrantCode;

    @Excel(name = "注册数量(手)", orderNum = "3", width = 15)
    private String registeHandCount;

    @Excel(name = "品种代码", orderNum = "4", width = 15)
    private String futureCode;

    @Excel(name = "交易所", orderNum = "5", width = 15)
    private String exchangeCode;

    @Excel(name = "账套", orderNum = "6", width = 15)
    private String siteName;

    @Excel(name = "交割库", orderNum = "7", width = 15)
    private String warehouseName;

    @Excel(name = "仓单类型", orderNum = "8", width = 15)
    private String category;

    @Excel(name = "交割保证金付款方式", orderNum = "9", width = 15)
    private String depositPaymentType;

    @Excel(name = "交割保证金金额", orderNum = "10", width = 15)
    private String depositAmount;

    @Excel(name = "可分配/转让量（吨）", orderNum = "11", width = 15)
    private String holdCount;

    @Excel(name = "仓单属性", orderNum = "12", width = 15)
    private String property;

    @Excel(name = "状态", orderNum = "13", width = 15)
    private String status;

    @Excel(name = "合同编号", orderNum = "14", width = 15)
    private String contractCode;

    @Excel(name = "销售/采购", orderNum = "15", width = 15)
    private String salesType;

    @Excel(name = "合同来源", orderNum = "16", width = 15)
    private String source;

    @Excel(name = "对方主体", orderNum = "17", width = 15)
    private String customerName;

    @Excel(name = "合同数量(吨)", orderNum = "18", width = 15)
    private String contractCount;
}
