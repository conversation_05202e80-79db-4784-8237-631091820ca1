package com.navigator.koala.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.koala.pojo.dto.*;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.vo.WarrantCancellationVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
@FeignClient(name = "navigator-koala-service")
public interface WarrantFacade {


    /**
     * 提交仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/submitWarrant")
    Result submitWarrant(@RequestBody WarrantEntity warrantEntity);

    /**
     * 更新仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/updateWarrant")
    Result updateWarrant(@RequestBody WarrantEntity warrantEntity);

    /**
     * 保存仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/saveWarrant")
    Result saveWarrant(@RequestBody WarrantEntity warrantEntity);

    /**
     * LDC注销仓单
     *
     * @param warrantDTO
     * @return
     */
    @PostMapping("/LDCCancelledWarrant")
    Result LDCCancelledWarrant(@RequestBody WarrantDTO warrantDTO);

    /**
     * 仓单分配/转让
     *
     * @param allocateAssignWarrantDTO
     * @return
     */
    @PostMapping("/allocateWarrant")
    Result allocateWarrant(@RequestBody AllocateAssignWarrantDTO allocateAssignWarrantDTO);

    /**
     * 更新仓单状态
     *
     * @param warrantDTO
     * @return
     */
    @PostMapping("/updateWarrantStatus")
    Result updateWarrantStatus(@RequestBody WarrantDTO warrantDTO);

    /**
     * 更新仓单注销量
     *
     * @param updateWarrantNumDTO
     * @return
     */
    @PostMapping("/updateWarrantNum")
    Result updateWarrantNum(@RequestBody UpdateWarrantNumDTO updateWarrantNumDTO);

    /**
     * 根据合同编号查询合同注销记录
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/queryWarrantCancellationByContractCode")
    Result queryWarrantCancellationByContractCode(@RequestParam("contractCode") String contractCode);

    /**
     * 根据仓单编号查询注销记录
     *
     * @param warrantCode
     * @return
     */
    @GetMapping("/queryWarrantCancellationByWarrantCode")
    Result queryWarrantCancellationByWarrantCode(@RequestParam("warrantCode")String warrantCode);

    /**
     * 仓单合同注销
     *
     * @param cancelledWarrantDTO
     * @return
     */
    @PostMapping("/cancelledWarrantContract")
    Result cancelledWarrantContract(@RequestBody CancelledWarrantDTO cancelledWarrantDTO);

    /**
     * 仓单上传
     *
     * @param file
     */
    @PostMapping(value = "/uploadingFileWarrant", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result uploadingFileWarrant(@RequestPart("file") MultipartFile file);

    /**
     * 仓单上传校验
     *
     * @param file
     */
    @PostMapping(value = "/verifyFileWarrant", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result verifyFileWarrant(@RequestPart("file") MultipartFile file);

    /**
     * 仓单列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryWarrant")
    Result queryWarrant(@RequestBody QueryDTO<QueryWarrantBO> queryDTO);

    /**
     * 根据仓单id查询仓单信息
     *
     * @param id
     * @return
     */
    @GetMapping("/queryWarrantByID")
    Result queryWarrantByID(@RequestParam("id") Integer id);

    /**
     * 仓单详情
     *
     * @param id
     * @return
     */
    @GetMapping("/queryWarrantVOByID")
    Result queryWarrantVOByID(@RequestParam("id") Integer id);

    /**
     * 总计
     *
     * @param queryWarrantBO
     * @return
     */
    @PostMapping("/queryWarrantSum")
    Result queryWarrantSum(@RequestBody QueryWarrantBO queryWarrantBO);

    /**
     * 下载仓单
     */
    @PostMapping("/queryWarrantExcel")
    List<DownloadWarrantExcelDTO> queryWarrantExcel(@RequestBody QueryWarrantBO queryWarrantBO);

    /**
     * 根据仓单code查询仓单信息
     *
     * @param code
     * @return
     */
    @GetMapping("/queryWarrantByCode")
    Result queryWarrantByCode(@RequestParam("code") String code);

    @GetMapping("/deleteWarrant")
    Result deleteWarrant(@RequestParam("id") Integer id);

    /**
     * 根据注销记录获取注销合同信息
     * @param cancellationId
     * @return
     */
    @GetMapping("/queryCancellationRelationByCancellId")
    List<CancellationRelationEntity> queryCancellationRelationByCancellId(@RequestParam("cancellationId") Integer cancellationId);

    /**
     * 根据合同编码查询注销记录信息
     * @param contractCode
     * @return
     */
    @GetMapping("/queryWarrantCancellation")
    WarrantCancellationEntity queryWarrantCancellation(@RequestParam("contractCode") String contractCode);

}
