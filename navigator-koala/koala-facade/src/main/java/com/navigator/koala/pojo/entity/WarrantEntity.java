package com.navigator.koala.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.koala.pojo.enums.WarrantCategoryEnum;
import com.navigator.koala.pojo.enums.WarrantPaymentTypeEnum;
import com.navigator.koala.pojo.enums.WarrantPropertyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbk_warrant")
@ApiModel(value = "WarrantEntity对象", description = "仓单信息表")
public class WarrantEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "仓单名称")
    private String name;

    @ApiModelProperty(value = "仓单编号")
    private String code;

    /**
     * {@link WarrantCategoryEnum}
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer category;

    /**
     * {@link WarrantPropertyEnum}
     */
    @ApiModelProperty(value = "仓单属性")
    private Integer property;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer tradeType;

    @ApiModelProperty(value = "仓单来源")
    private Integer source;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交易所名称（简称）")
    private String exchangeName;

    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "品种名称")
    private String categoryName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "注册数量")
    private BigDecimal registeCount;

    @ApiModelProperty(value = "注册数量手数")
    private BigDecimal registeHandCount;

    @ApiModelProperty(value = "持有量")
    private BigDecimal holdCount;

    @ApiModelProperty(value = "持有量手数")
    private BigDecimal holdHandCount;

    @ApiModelProperty(value = "注册方id")
    private Integer registeId;

    @ApiModelProperty(value = "注册方名称")
    private String registeName;

    @ApiModelProperty(value = "所属主体ID")
    private Integer companyId;

    @ApiModelProperty(value = "所属主体Name")
    private String companyName;

    @ApiModelProperty(value = "注册商品货值")
    private BigDecimal registeAmount;

    @ApiModelProperty(value = "交割库类型")
    private Integer warehouseType;

    @ApiModelProperty(value = "交割库编号")
    private String warehouseCode;

    @ApiModelProperty(value = "交割库名称")
    private String warehouseName;

    @ApiModelProperty(value = "交割合约")
    private String domainCode;

    @ApiModelProperty(value = "区域")
    private String area;

    /**
     * {@link WarrantPaymentTypeEnum}
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;

    @ApiModelProperty(value = "保证金比例")
    private String depositRate;

    @ApiModelProperty(value = "保证金金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "已变更数量")
    private BigDecimal alteredCount;

    @ApiModelProperty(value = "已注销数量")
    private BigDecimal cancelledCount;

    @ApiModelProperty(value = "合同注销数量")
    private BigDecimal contractCancelledCount;

    /**
     * {@link com.navigator.koala.pojo.enums.WarrantStatusEnum}
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "删除信息")
    private Integer isDeleted;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
