package com.navigator.koala.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13
 */

@AllArgsConstructor
@Getter
public enum WarrantModifyTypeEnum {

    LDC_CANCEL(1, "LDC注销", "LDC_CANCEL"),
    ALLOCATE(2, "分配", "ALLOCATE"),
    TRANSFER(3, "转让", "TRANSFER"),
    CONTRACT_CANCEL(4, "合同注销", "CONTRACT_CANCEL"),
    CONTRACT_INVALID(5, "仓单合同作废", "CONTRACT_INVALID"),
    CONTRACT_CANCEL_REVOKE(6, "仓单合同注销撤回", "CONTRACT_CANCEL_REVOKE"),
    CONTRACT_BUYBACK(7, "仓单合同回购", "CONTRACT_BUYBACK"),
    ;

    private Integer value;
    private String desc;
    private String code;

    public static WarrantModifyTypeEnum getByValue(Integer value) {
        for (WarrantModifyTypeEnum typeEnum : WarrantModifyTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }


    public static WarrantModifyTypeEnum getByCode(String code) {
        for (WarrantModifyTypeEnum typeEnum : WarrantModifyTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
