package com.navigator.open.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.util.StringUtil;
import com.navigator.open.pojo.qo.OpenApiLogQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * OPEN API 日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbo_open_api_log")
@ApiModel(value = "openApiLog对象", description = "OPEN API 日志")
public class OpenApiLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日志ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "应用ID")
    private Integer openAppId;

    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    @ApiModelProperty(value = "请求数据")
    private String requestData;

    @ApiModelProperty(value = "响应数据")
    private String responseData;

    @ApiModelProperty(value = "请求时间")
    private Date requestTime;

    @ApiModelProperty(value = "响应时间")
    private Date responseTime;

    @ApiModelProperty(value = "结果编码")
    private String code;

    @ApiModelProperty(value = "结果消息")
    private String msg;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<OpenApiLogEntity> lqw(OpenApiLogQO condition) {
        LambdaQueryWrapper<OpenApiLogEntity> lqw = new LambdaQueryWrapper<OpenApiLogEntity>();
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), OpenApiLogEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getOpenAppId()), OpenApiLogEntity::getOpenAppId, condition.getOpenAppId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRequestMethod()), OpenApiLogEntity::getRequestMethod, condition.getRequestMethod());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRequestUrl()), OpenApiLogEntity::getRequestUrl, condition.getRequestUrl());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCode()), OpenApiLogEntity::getCode, condition.getCode());
        }
        lqw.orderByDesc(OpenApiLogEntity::getId);
        return lqw;
    }
}
