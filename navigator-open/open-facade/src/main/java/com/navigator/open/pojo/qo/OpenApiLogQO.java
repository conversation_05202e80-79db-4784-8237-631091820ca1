package com.navigator.open.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * OPEN API 日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@Accessors(chain = true)
public class OpenApiLogQO {
    @ApiModelProperty(value = "日志ID")
    private Integer id;
    @ApiModelProperty(value = "应用ID")
    private Integer openAppId;
    @ApiModelProperty(value = "请求方法")
    private String requestMethod;
    @ApiModelProperty(value = "请求URL")
    private String requestUrl;
    @ApiModelProperty(value = "结果编码")
    private String code;
}
