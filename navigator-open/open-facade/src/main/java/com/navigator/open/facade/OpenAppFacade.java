package com.navigator.open.facade;

import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 应用 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@FeignClient(value = "navigator-open-service")
public interface OpenAppFacade {
    /**
     * 根据条件：获取应用列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取应用列表")
    @PostMapping("/openApp/queryOpenAppList")
    List<OpenAppEntity> queryOpenAppList(@RequestBody OpenAppQO condition);

    /**
     * 根据ID：获取应用
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取应用")
    @GetMapping("/openApp/getOpenAppById")
    OpenAppEntity getOpenAppById(@RequestParam(value = "id") Integer id);
}