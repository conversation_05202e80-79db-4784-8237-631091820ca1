package com.navigator.open.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * OPEN API 日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@Accessors(chain = true)
public class OpenApiLogAddDTO {
    @ApiModelProperty(value = "应用ID")
    private Integer openAppId;
    @ApiModelProperty(value = "请求方法")
    private String requestMethod;
    @ApiModelProperty(value = "请求URL")
    private String requestUrl;
    @ApiModelProperty(value = "请求数据")
    private String requestData;
    @ApiModelProperty(value = "响应数据")
    private String responseData;
    @ApiModelProperty(value = "请求时间")
    private Date requestTime;
    @ApiModelProperty(value = "响应时间")
    private Date responseTime;
    @ApiModelProperty(value = "结果编码")
    private Integer code;
    @ApiModelProperty(value = "结果消息")
    private String msg;

}
