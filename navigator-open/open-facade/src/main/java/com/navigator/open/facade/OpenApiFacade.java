package com.navigator.open.facade;

import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * Open API Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@FeignClient(value = "navigator-open-service")
public interface OpenApiFacade {
    /**
     * 根据条件：获取Open API列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取Open API列表")
    @PostMapping("/openApi/queryOpenApiList")
    List<OpenApiEntity> queryOpenApiList(@RequestBody OpenApiQO condition);

    /**
     * 根据ID：获取Open API
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取Open API")
    @GetMapping("/openApi/getOpenApiById")
    OpenApiEntity getOpenApiById(@RequestParam(value = "id") Integer id);
}