package com.navigator.open.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenApiLogEntity;
import com.navigator.open.pojo.qo.OpenApiLogQO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * OPEN API 日志 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@FeignClient(value = "navigator-open-service")
public interface OpenApiLogFacade {
    /**
     * 根据条件：获取OPEN API 日志分页
     *
     * @param queryDTO
     * @return
     */
    @ApiOperation(value = "根据条件：获取OPEN API 日志分页")
    @PostMapping("/openApiLog/queryOpenApiLogPage")
    Page<OpenApiLogEntity> queryOpenApiLogPage(@RequestBody QueryDTO<OpenApiLogQO> queryDTO);

    /**
     * 根据ID：获取OPEN API 日志
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取OPEN API 日志")
    @GetMapping("/openApiLog/getOpenApiLogById")
    OpenApiLogEntity getOpenApiLogById(@RequestParam(value = "id") Integer id);

    /**
     * 新增：OPEN API 日志
     *
     * @param addDTO
     * @return
     */
    @ApiOperation(value = "新增：OPEN API 日志")
    @PostMapping("/openApiLog/addOpenApiLog")
    Result addOpenApiLog(@RequestBody OpenApiLogAddDTO addDTO);
}