package com.navigator.open.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.open.pojo.qo.OpenApiQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * Open API
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbo_open_api")
@ApiModel(value = "openApi对象", description = "Open API")
public class OpenApiEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "删除标志")
    private Integer isDeleted;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<OpenApiEntity> lqw(OpenApiQO condition) {
        LambdaQueryWrapper<OpenApiEntity> lqw = new LambdaQueryWrapper<OpenApiEntity>().eq(OpenApiEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), OpenApiEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getName()), OpenApiEntity::getName, condition.getName());
        }
        lqw.orderByDesc(OpenApiEntity::getId);
        return lqw;
    }
}
