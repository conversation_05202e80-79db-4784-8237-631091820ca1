package com.navigator.open.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <p>
 * 应用 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@FeignClient(value = "navigator-open-service")
public interface TradeTicketApiFacade {

    /**
     * 创建合同
     *
     * @param omContractAddTTDTO
     * @return
     */
    @ApiOperation(value = "根据条件：创建合同")
    @PostMapping("/openApp/createContract")
    Result saveCreate(OMContractAddTTDTO omContractAddTTDTO);
}