package com.navigator.open.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@Accessors(chain = true)
public class OpenAppQO {
    @ApiModelProperty(value = "应用ID")
    private Integer id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "证书编号")
    private String certCode;
}
