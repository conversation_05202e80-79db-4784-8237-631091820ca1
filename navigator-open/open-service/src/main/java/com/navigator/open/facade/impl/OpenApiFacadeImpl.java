package com.navigator.open.facade.impl;

import com.navigator.open.facade.OpenApiFacade;
import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;
import com.navigator.open.service.IOpenApiService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Open API Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@RestController
@Api(tags = "Open API")
public class OpenApiFacadeImpl implements OpenApiFacade {

    @Resource
    private IOpenApiService openApiService;

    @Override
    public List<OpenApiEntity> queryOpenApiList(OpenApiQO condition) {
        return openApiService.queryOpenApiList(condition);
    }

    @Override
    public OpenApiEntity getOpenApiById(Integer id) {
        return openApiService.getOpenApiById(id);
    }
}