package com.navigator.open.facade.impl;

import com.navigator.open.facade.OpenAppFacade;
import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;
import com.navigator.open.service.IOpenAppService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 应用 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@RestController
@Api(tags = "应用")
public class OpenAppFacadeImpl implements OpenAppFacade {

    @Resource
    private IOpenAppService openAppService;

    @Override
    public List<OpenAppEntity> queryOpenAppList(OpenAppQO condition) {
        return openAppService.queryOpenAppList(condition);
    }

    @Override
    public OpenAppEntity getOpenAppById(Integer id) {
        return openAppService.getOpenAppById(id);
    }
}