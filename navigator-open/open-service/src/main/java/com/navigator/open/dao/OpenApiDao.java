package com.navigator.open.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.open.mapper.OpenApiMapper;
import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;

import java.util.List;

/**
 * <p>
 * Open API DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Dao
public class OpenApiDao extends BaseDaoImpl<OpenApiMapper, OpenApiEntity> {
    /**
     * 根据条件：获取Open API列表
     *
     * @param condition
     * @return
     */
    public List<OpenApiEntity> queryOpenApiList(OpenApiQO condition) {
        return this.list(OpenApiEntity.lqw(condition));
    }

    /**
     * 根据ID：获取Open API
     *
     * @param id
     * @return
     */
    public OpenApiEntity getOpenApiById(Integer id) {
        return this.getById(id);
    }
}
