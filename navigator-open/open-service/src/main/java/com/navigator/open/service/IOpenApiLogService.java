package com.navigator.open.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenApiLogEntity;
import com.navigator.open.pojo.qo.OpenApiLogQO;

/**
 * <p>
 * OPEN API 日志 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IOpenApiLogService extends IService<OpenApiLogEntity> {
    /**
     * 根据条件：获取OPEN API 日志分页
     *
     * @param queryDTO
     * @return
     */
    Page<OpenApiLogEntity> queryOpenApiLogPage(QueryDTO<OpenApiLogQO> queryDTO);

    /**
     * 根据ID：获取OPEN API 日志
     *
     * @param id
     * @return
     */
    OpenApiLogEntity getOpenApiLogById(Integer id);

    /**
     * 新增：OPEN API 日志
     *
     * @param addDTO
     * @return
     */
    OpenApiLogEntity addOpenApiLog(OpenApiLogAddDTO addDTO);
}
