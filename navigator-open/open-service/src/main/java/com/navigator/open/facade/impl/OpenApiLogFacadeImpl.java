package com.navigator.open.facade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.open.facade.OpenApiLogFacade;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenApiLogEntity;
import com.navigator.open.pojo.qo.OpenApiLogQO;
import com.navigator.open.service.IOpenApiLogService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * OPEN API 日志 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@RestController
@Api(tags = "OPEN API 日志")
public class OpenApiLogFacadeImpl implements OpenApiLogFacade {

    @Resource
    private IOpenApiLogService openApiLogService;

    @Override
    public Page<OpenApiLogEntity> queryOpenApiLogPage(QueryDTO<OpenApiLogQO> queryDTO) {
        return openApiLogService.queryOpenApiLogPage(queryDTO);
    }

    @Override
    public OpenApiLogEntity getOpenApiLogById(Integer id) {
        return openApiLogService.getOpenApiLogById(id);
    }

    @Override
    public Result addOpenApiLog(OpenApiLogAddDTO addDTO) {
        openApiLogService.addOpenApiLog(addDTO);
        return Result.success();
    }
}