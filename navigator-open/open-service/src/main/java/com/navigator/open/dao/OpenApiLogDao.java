package com.navigator.open.dao;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.PageUtil;
import com.navigator.open.mapper.OpenApiLogMapper;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenApiLogEntity;
import com.navigator.open.pojo.qo.OpenApiLogQO;

/**
 * <p>
 * OPEN API 日志 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Dao
public class OpenApiLogDao extends BaseDaoImpl<OpenApiLogMapper, OpenApiLogEntity> {
    /**
     * 根据条件：获取OPEN API 日志分页
     *
     * @param queryDTO
     * @return
     */
    public Page<OpenApiLogEntity> queryOpenApiLogPage(QueryDTO<OpenApiLogQO> queryDTO) {
        return PageUtil.convertPage(this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), OpenApiLogEntity.lqw(queryDTO.getCondition())));
    }

    /**
     * 根据ID：获取OPEN API 日志
     *
     * @param id
     * @return
     */
    public OpenApiLogEntity getOpenApiLogById(Integer id) {
        return this.getById(id);
    }

    /**
     * 新增：OPEN API 日志
     *
     * @param addDTO
     * @return
     */
    public OpenApiLogEntity addOpenApiLog(OpenApiLogAddDTO addDTO) {
        // 复制
        OpenApiLogEntity entity = BeanUtil.copyProperties(addDTO, OpenApiLogEntity.class);
        // 新增
        boolean flag = save(entity);
        if (flag) {
            return entity;
        }
        throw new BusinessException("新增失败！");
    }
}
