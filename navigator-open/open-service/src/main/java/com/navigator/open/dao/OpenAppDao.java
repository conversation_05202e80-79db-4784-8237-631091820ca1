package com.navigator.open.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.open.mapper.OpenAppMapper;
import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;

import java.util.List;

/**
 * <p>
 * 应用 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Dao
public class OpenAppDao extends BaseDaoImpl<OpenAppMapper, OpenAppEntity> {
    /**
     * 根据条件：获取应用列表
     *
     * @param condition
     * @return
     */
    public List<OpenAppEntity> queryOpenAppList(OpenAppQO condition) {
        return this.list(OpenAppEntity.lqw(condition));
    }

    /**
     * 根据ID：获取应用
     *
     * @param id
     * @return
     */
    public OpenAppEntity getOpenAppById(Integer id) {
        return this.getById(id);
    }
}
