package com.navigator.open.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.open.dao.OpenApiDao;
import com.navigator.open.mapper.OpenApiMapper;
import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;
import com.navigator.open.service.IOpenApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Open API Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Service

public class OpenApiServiceImpl extends ServiceImpl<OpenApiMapper, OpenApiEntity> implements IOpenApiService {

    @Resource
    private OpenApiDao openApiDao;

    @Override
    public List<OpenApiEntity> queryOpenApiList(OpenApiQO condition) {
        return openApiDao.queryOpenApiList(condition);
    }

    @Override
    public OpenApiEntity getOpenApiById(Integer id) {
        return openApiDao.getOpenApiById(id);
    }
}