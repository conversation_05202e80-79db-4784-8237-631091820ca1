package com.navigator.open.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;

import java.util.List;

/**
 * <p>
 * 应用 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IOpenAppService extends IService<OpenAppEntity> {
    /**
     * 根据条件：获取应用列表
     *
     * @param condition
     * @return
     */
    List<OpenAppEntity> queryOpenAppList(OpenAppQO condition);

    /**
     * 根据ID：获取应用
     *
     * @param id
     * @return
     */
    OpenAppEntity getOpenAppById(Integer id);
}
