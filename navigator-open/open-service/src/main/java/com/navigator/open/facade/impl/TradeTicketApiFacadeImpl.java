package com.navigator.open.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.open.facade.TradeTicketApiFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.KeyTradeInfoTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Open API Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@RestController
@Api(tags = "TT API")
public class TradeTicketApiFacadeImpl implements TradeTicketApiFacade {

    @Autowired
    private TradeTicketFacade tradeTicketFacade;

    @Override
    public Result saveCreate(OMContractAddTTDTO omContractAddTTDTO) {
        List<TTQueryVO> list = getTtQueryVOS(omContractAddTTDTO, true);
        return Result.success(list);
    }


    private List<TTQueryVO> getTtQueryVOS(OMContractAddTTDTO omContractAddTTDTO, boolean saveStatus) {
        List<TTQueryVO> list = new ArrayList<>();
        if (null != omContractAddTTDTO.getSalesType()) {
            for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {
                SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();
                BeanUtils.copyProperties(keyTradeInfoTTDTO, salesContractAddTTDTO);
                BeanUtils.copyProperties(omContractAddTTDTO, salesContractAddTTDTO);
                salesContractAddTTDTO.setDomainCode(keyTradeInfoTTDTO.getDomainCode());
                salesContractAddTTDTO.setFutureCode(keyTradeInfoTTDTO.getFutureCode());
                salesContractAddTTDTO.setContractNum(keyTradeInfoTTDTO.getContractNum());
                salesContractAddTTDTO.setWriteOffEndTime(keyTradeInfoTTDTO.getWriteOffEndTime());
                salesContractAddTTDTO.setWriteOffStartTime(keyTradeInfoTTDTO.getWriteOffStartTime());
                salesContractAddTTDTO.setSupplierId(StringUtils.isBlank(omContractAddTTDTO.getSupplierId()) ? null : Integer.parseInt(omContractAddTTDTO.getSupplierId()));
                salesContractAddTTDTO.setCustomerId(StringUtils.isBlank(omContractAddTTDTO.getCustomerId()) ? null : Integer.parseInt(omContractAddTTDTO.getCustomerId()));
                salesContractAddTTDTO.setGoodsCategoryId(StringUtils.isBlank(omContractAddTTDTO.getGoodsCategoryId()) ? null : Integer.parseInt(omContractAddTTDTO.getGoodsCategoryId()));
                salesContractAddTTDTO.setAddedDepositRate(StringUtils.isBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? 0 : Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()));
                salesContractAddTTDTO.setPayConditionId(StringUtils.isBlank(keyTradeInfoTTDTO.getPayConditionId()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getPayConditionId()));
                salesContractAddTTDTO.setUsage(omContractAddTTDTO.getUsage());
                salesContractAddTTDTO.setPriceEndTime(keyTradeInfoTTDTO.getPriceEndTime());
                salesContractAddTTDTO.setPriceEndType(keyTradeInfoTTDTO.getPriceEndType());
                //转换价格数据
                PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, keyTradeInfoTTDTO.getPriceDetailDTO());

                //处理
                Result result = getResult(omContractAddTTDTO, salesContractAddTTDTO, priceDetailBO, saveStatus);

                if (result.getCode() == ResultCodeEnum.OK.code()) {
                    List<TTQueryVO> ttQueryVOList = JSON.parseArray(JSON.toJSONString(result.getData()), TTQueryVO.class);
                    list.addAll(ttQueryVOList);
                } else {
                    // 处理异常
                    if (result.getCode() == ResultCodeEnum.TT_MODIFY_NOT_EQUAL.getCode()) {
                        throw new BusinessException(ResultCodeEnum.TT_MODIFY_NOT_EQUAL);
                    }
                    throw new BusinessException(ResultCodeEnum.TT_FAIL, result.getMessage());
                }
            }
        }
        return list;
    }

    private Result getResult(OMContractAddTTDTO omContractAddTTDTO, SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO, boolean saveStatus) {
        TTDTO ttdto = new TTDTO();
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setBuCode(salesContractAddTTDTO.getBuCode());
        ttdto.setSalesType(salesContractAddTTDTO.getSalesType());
        // 设置TT类型
        ttdto.setTtType(omContractAddTTDTO.getType() == null ? TTTypeEnum.NEW.getType() : omContractAddTTDTO.getType());
        // 设置提交类型:保存
        ttdto.setSubmitType(omContractAddTTDTO.getSubmitType());
        return saveStatus ? tradeTicketFacade.saveTT(ttdto) : tradeTicketFacade.updateTT(ttdto);
    }
}