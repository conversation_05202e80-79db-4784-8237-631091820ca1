package com.navigator.open.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;

import java.util.List;

/**
 * <p>
 * Open API Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
public interface IOpenApiService extends IService<OpenApiEntity> {
    /**
     * 根据条件：获取Open API列表
     *
     * @param condition
     * @return
     */
    List<OpenApiEntity> queryOpenApiList(OpenApiQO condition);

    /**
     * 根据ID：获取Open API
     *
     * @param id
     * @return
     */
    OpenApiEntity getOpenApiById(Integer id);
}
