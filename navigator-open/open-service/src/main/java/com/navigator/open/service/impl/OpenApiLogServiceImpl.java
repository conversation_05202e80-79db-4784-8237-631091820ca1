package com.navigator.open.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.dto.QueryDTO;
import com.navigator.open.dao.OpenApiLogDao;
import com.navigator.open.mapper.OpenApiLogMapper;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenApiLogEntity;
import com.navigator.open.pojo.qo.OpenApiLogQO;
import com.navigator.open.service.IOpenApiLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <p>
 * OPEN API 日志 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service

public class OpenApiLogServiceImpl extends ServiceImpl<OpenApiLogMapper, OpenApiLogEntity> implements IOpenApiLogService {

    @Resource
    private OpenApiLogDao openApiLogDao;

    @Override
    public Page<OpenApiLogEntity> queryOpenApiLogPage(QueryDTO<OpenApiLogQO> queryDTO) {
        return openApiLogDao.queryOpenApiLogPage(queryDTO);
    }

    @Override
    public OpenApiLogEntity getOpenApiLogById(Integer id) {
        return openApiLogDao.getOpenApiLogById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OpenApiLogEntity addOpenApiLog(OpenApiLogAddDTO addDTO) {
        return openApiLogDao.addOpenApiLog(addDTO);
    }
}