package com.navigator.open.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.open.dao.OpenAppDao;
import com.navigator.open.mapper.OpenAppMapper;
import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;
import com.navigator.open.service.IOpenAppService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 应用 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service

public class OpenAppServiceImpl extends ServiceImpl<OpenAppMapper, OpenAppEntity> implements IOpenAppService {

    @Resource
    private OpenAppDao openAppDao;

    @Override
    public List<OpenAppEntity> queryOpenAppList(OpenAppQO condition) {
        return openAppDao.queryOpenAppList(condition);
    }

    @Override
    public OpenAppEntity getOpenAppById(Integer id) {
        return openAppDao.getOpenAppById(id);
    }
}