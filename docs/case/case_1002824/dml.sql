update ACT_HI_PROCINST set TT_TYPE_= t.type from ACT_HI_PROCINST p join dbt_trade_ticket t ON t.code = p.BUSINESS_KEY_;
select p.TT_TYPE_,t.type from ACT_HI_PROCINST p join dbt_trade_ticket t ON t.code = p.BUSINESS_KEY_ ;

update ACT_HI_TASKINST set TT_TYPE_= p.TT_TYPE_ from ACT_HI_TASKINST a join ACT_HI_PROCINST p ON p.ID_ = a.PROC_INST_ID_;
select a.TT_TYPE_,p.TT_TYPE_ from ACT_HI_TASKINST a join ACT_HI_PROCINST p ON p.ID_ = a.PROC_INST_ID_;

update ACT_HI_ACTINST set TT_TYPE_= p.TT_TYPE_ from ACT_HI_ACTINST t join ACT_HI_PROCINST p ON p.ID_ = T.PROC_INST_ID_;
select t.TT_TYPE_,p.TT_TYPE_ from ACT_HI_ACTINST t join ACT_HI_PROCINST p ON p.ID_ = T.PROC_INST_ID_;

update ACT_HI_PROCINST set TT_TYPE_= 14 where TRADE_TYPE_NAME_ like '%权益变更%';

update ACT_HI_TASKINST set TT_TYPE_= p.TT_TYPE_ from ACT_HI_TASKINST a join ACT_HI_PROCINST p ON p.ID_ = a.PROC_INST_ID_ where p.TRADE_TYPE_NAME_ like '%权益变更%';

update ACT_HI_ACTINST set TT_TYPE_= p.TT_TYPE_ from ACT_HI_ACTINST t join ACT_HI_PROCINST p ON p.ID_ = T.PROC_INST_ID_ where p.TRADE_TYPE_NAME_ like '%权益变更%';