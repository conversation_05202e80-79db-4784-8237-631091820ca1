-- 合同增加尾量关闭字段
ALTER TABLE [dbo].[dbt_contract] ADD [close_tail_num] decimal(25,6)  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'尾量关闭的数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'close_tail_num'
    GO

-- 合同视图增加尾量关闭字段-contract.close_tail_num
ALTER VIEW [dbo].[v_contract] AS
SELECT
    contract.id,
    contract.contract_code,
    contract.status,
    contract.sign_date,
    contract.customer_id,
    contract.customer_name,
    contract.supplier_id,
    contract.supplier_name,
    contract.trade_type,
    contract.contract_type,
    contract.goods_category_id,
    contract.goods_package_id,
    contract.goods_spec_id,
    contract.domain_code,
    contract.goods_name,
    contract.contract_num,
    contract.total_amount,
    contract.payment_type,
    contract.credit_days,
    contract.delivery_factory_code,
    contract.weight_tolerance,
    contract.delivery_start_time,
    contract.delivery_end_time,
    contract.price_end_time,
    contract.total_price_num,
    contract.deposit_rate,
    contract.deposit_amount,
    contract.deposit_release_type,
    contract.added_deposit,
    contract.oem,
    contract.tax_rate,
    contract.invoice_type,
    contract.is_stf,
    contract.customer_contract_code,
    contract.tag_config_ids,
    contract.memo,
    contract.sales_type,
    contract.created_at,
    contract.created_by,
    contract.updated_at,
    contract.updated_by,
    contract.is_deleted,
    contract.unit_price,
    contract.fob_unit_price,
    contract.cif_unit_price,
    contract.delivery_type,
    contract.price_end_type,
    contract.belong_customer_id,
    contract.able_reverse_price_times,
    contract.company_id,
    contract.company_name,
    contract.close_tail_num,
    package_weight.rule_key AS package_weight_name,
    weight_check.rule_key AS weight_check_name,
    destination.rule_key AS destination_name,
    delivery_type.name AS delivery_type_name,
    warehouse.name AS ship_warehouse_name,
    customer.enterprise_name AS enterprise_name,
    supplier.enterprise_name AS supplier_enterprise_name,
    business_person.name AS business_person_name,
    create_by.name AS create_by_name,
    p1.forward_price AS forward_price,
    p1.extra_price AS extra_price,
    p1.factory_price AS factory_price,
    p1.protein_diff_price AS protein_diff_price,
    p1.compensation_price AS compensation_price,
    p1.option_price AS option_price,
    p1.transport_price AS transport_price,
    p1.lifting_price AS lifting_price,
    p1.delay_price AS delay_price,
    p1.temperature_price AS temperature_price,
    p1.other_delivery_price AS other_delivery_price,
    p1.buy_back_price AS buy_back_price,
    p1.complaint_discount_price AS complaint_discount_price,
    p1.transfer_factory_price AS transfer_factory_price,
    p1.other_price AS other_price,
    p1.business_price AS business_price,
    p1.fee AS fee,
    p1.shipping_fee_price AS shipping_fee_price,
    p1.refine_diff_price AS refine_diff_price
FROM
    dbt_contract contract
        LEFT JOIN dbz_system_rule_item package_weight ON contract.package_weight = package_weight.id
        LEFT JOIN dbz_system_rule_item weight_check ON contract.weight_check = weight_check.id
        LEFT JOIN dbz_system_rule_item destination ON contract.destination = destination.id
        LEFT JOIN dbt_delivery_type delivery_type ON contract.delivery_type = delivery_type.id
        LEFT JOIN dba_factory_warehouse warehouse ON contract.ship_warehouse_id = warehouse.id
        LEFT JOIN dbo.dba_customer customer ON contract.customer_id = customer.id
        LEFT JOIN dbo.dba_customer supplier ON contract.supplier_id = supplier.id
        LEFT JOIN dba_employ business_person ON contract.owner_id = business_person.id
        LEFT JOIN dba_employ create_by ON contract.created_by = create_by.id
        LEFT JOIN dbt_contract_price p1 ON contract.id = p1.contract_id
WHERE
        ( SELECT COUNT ( * ) FROM dbt_contract_price AS p2 WHERE p1.contract_id = p2.contract_id AND p2.id >= p1.id ) <= 1

    GO

-- 权益变更视图增加尾量关闭字段-contract.close_tail_num
ALTER VIEW [dbo].[v_contract_equity] AS
SELECT
    ROW_NUMBER ( ) OVER ( ORDER BY contract.id ) AS id,
        contract.id AS contract_id,
    contract.delivery_factory_code,
    contract.supplier_id,
    contract.supplier_name,
    contract.contract_code,
    contract.customer_id,
    contract.customer_name,
    customer.enterprise_name AS enterprise_name,
    contract.goods_category_id,
    contract.contract_type,
    contract.delivery_start_time,
    contract.delivery_end_time,
    contract.able_transfer_times,
    contract.transferred_times,
    contract.able_reverse_price_times,
    contract.reversed_price_times,
    contract.close_tail_num,
    equity.apply_code AS last_apply_code,
    equity.approve_status AS last_approve_status,
    equity.updated_by AS last_updated_by,
    equity.updated_at AS last_updated_at
FROM
    dbt_contract contract
        LEFT JOIN dbo.dba_customer customer ON contract.customer_id = customer.id
        LEFT JOIN dbt_contract_change_equity equity ON contract.id = equity.contract_id
WHERE
        contract.status = 2
  AND contract.contract_type IN ( 1, 2 )
  AND contract.total_buy_back_num = 0
  AND contract.contract_num > 0
  AND contract.is_deleted = 0
  AND contract.sales_type = 2
  AND ( SELECT COUNT ( * ) FROM dbt_contract_change_equity AS equity2 WHERE equity.contract_id = equity2.contract_id AND equity2.id >= equity.id ) <= 1