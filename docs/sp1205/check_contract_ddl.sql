CREATE TABLE [dbo].[dbi_check_contract] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [contract_id] int  NULL,
  [contract_code] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [check_batch] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [start_check_date] datetime  NULL,
  [end_check_date] datetime  NULL,
  [type] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [status] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [base_info_result] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [lkg_record_result] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [lkg_info_result] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [order_num] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_order_num] decimal(25,6) DEFAULT ((0)) NULL,
  [total_price_num] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_total_price_num] decimal(25,6) DEFAULT ((0)) NULL,
  [unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [cif_unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_cif_unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [fob_unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_fob_unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [total_amount] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_total_amount] decimal(25,6) DEFAULT ((0)) NULL,
  [deposit_amount] decimal(25,6) DEFAULT ((0)) NULL,
  [calc_deposit_amount] decimal(25,6) DEFAULT ((0)) NULL,
  [total_transfer_times] int  NULL,
  [calc_total_transfer_times] int  NULL,
  [total_reverse_price_times] int  NULL,
  [calc_total_reverse_price_times] int  NULL,
  [contract_num] decimal(25,6) DEFAULT ((0)) NULL,
  [lkg_contract_num] decimal(25,6) DEFAULT ((0)) NULL,
  [lkg_delivery_num] decimal(25,6) DEFAULT ((0)) NULL,
  [lkg_bill_num] decimal(25,6) DEFAULT ((0)) NULL,
  [nav_status] int  NULL,
  [lkg_status] int  NULL,
  [created_by] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_by] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL,
  CONSTRAINT [PK__dbi_chec__3213E83F749FB190] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
)  
ON [PRIMARY]
GO

ALTER TABLE [dbo].[dbi_check_contract] SET (LOCK_ESCALATION = TABLE)