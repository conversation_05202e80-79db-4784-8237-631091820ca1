
-- dba_c_employ
ALTER TABLE [dbo].[dba_c_employ] ADD [enterprise_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户集团名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_employ',
'COLUMN', N'enterprise_name'


ALTER TABLE [dbo].[dba_c_employ] ADD [customer_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户简称',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_employ',
'COLUMN', N'customer_name'


ALTER TABLE [dbo].[dba_c_employ] ADD [customer_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户编号',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_employ',
'COLUMN', N'customer_code'

UPDATE  dba_c_employ set enterprise_name =b.enterprise_name ,customer_name = b.name  ,customer_code = b.linkage_customer_code  from dba_c_employ a LEFT JOIN dba_customer b on  a.customer_id = b.id ;



--dbt_tt_structure
ALTER TABLE [dbo].[dbt_tt_structure] ADD [delivery_factory_id] int NULL
GO

ALTER TABLE [dbo].[dbt_tt_structure] ADD [delivery_factory_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'交货工厂id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'delivery_factory_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交货工厂编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'delivery_factory_code'


--dbt_contract_structure
ALTER TABLE [dbo].[dbt_contract_structure] ADD [delivery_factory_id] int  NULL
GO

ALTER TABLE [dbo].[dbt_contract_structure] ADD [delivery_factory_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'交货工厂id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'delivery_factory_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交货工厂编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'delivery_factory_code'


-- dbz_operation_detail
ALTER TABLE [dbo].[dbz_operation_detail] ADD [after_data] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改后数据',
'SCHEMA', N'dbo',
'TABLE', N'dbz_operation_detail',
'COLUMN', N'after_data'



-- dbz_operation_config
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'updateDefRoleStatus', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户角色列表--角色禁用/启用');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveCustomerConfig', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--批量上传');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'replenishCustomerMessage', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--补充基础数据');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'updateSystemAndCustomer', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--系统及账号更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_updateTemplateContactFactory', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕合同模版更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_redactCustomerDepositRate', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕履约保证金新增');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_updateCustomerDepositRateStatus', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕履约保证金启用/禁用');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_updateCustomerDetail', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕白名单/发票类型更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_redactCustomerBank', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕账户信息更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbm_addCustomerCreditPayment', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆粕赊销&预付更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_updateTemplateContactFactory', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油合同模版更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_redactCustomerDepositRate', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油履约保证金新增');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_updateCustomerDepositRateStatus', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油履约保证金启用/禁用');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_updateCustomerDetail', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油白名单/发票类型更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_redactCustomerBank', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油账户信息更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'sbo_addCustomerCreditPayment', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户配置--豆油赊销&预付更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveOrUpdateRole', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户角色列表--新建角色');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveRoleMenu', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户角色管理--菜单权限更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveOrUpdatePower', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '客户角色管理--操作权限更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveOrUpdateFactory', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '通用配置--工厂设置更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'setNotTrade', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '通用配置--非交易日定义更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'saveOrUpdateSystemRule', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '通用配置--CLB密码过期时间更新');
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'invalidStatus', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '通用配置--易企签服务状态变更');



-- dba_menu
INSERT INTO [dbo].[dba_menu]([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES (NULL, N'N071', N' Columbus用户列表', 1, 81, N'/customer-manage/customerOperationLog', 1, 0, '2022-10-11 03:09:40.030', '2022-10-11 03:09:40.030', 0, 1, 0, N'N019');

