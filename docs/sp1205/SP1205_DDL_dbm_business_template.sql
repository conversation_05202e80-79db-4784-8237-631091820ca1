ALTER TABLE [dbo].[dbm_business_template] ADD [auto] int DEFAULT 1 NULL
GO

ALTER TABLE [dbo].[dbm_business_template] ADD [system] int DEFAULT 1 NULL
GO

ALTER TABLE [dbo].[dbm_business_template] ADD [status] int DEFAULT 1 NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'0:手动 1：自动',
'SCHEMA', N'dbo',
'TABLE', N'dbm_business_template',
'COLUMN', N'auto'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1：magellan 2：columbus',
'SCHEMA', N'dbo',
'TABLE', N'dbm_business_template',
'COLUMN', N'system'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0：禁用 1：启用',
'SCHEMA', N'dbo',
'TABLE', N'dbm_business_template',
'COLUMN', N'status'


update  [dbo].[dbm_business_template]  set  receiver_type = 'customer_contact'  where business_code = 'CUSTOMER_SIGN_NOTICE'

update  [dbo].[dbm_business_template]  set  receiver_type = 'columbus_user'  where business_code = 'UpdateAllocate_Sms'