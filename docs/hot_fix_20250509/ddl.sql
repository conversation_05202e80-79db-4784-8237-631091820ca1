ALTER VIEW [dbo].[v_contract] AS SELECT
contract.id,
contract.contract_code,
contract.status,
contract.sign_date,
contract.customer_id,
contract.customer_name,
contract.supplier_id,
contract.supplier_name,
contract.trade_type,
contract.contract_type,
contract.goods_category_id,
contract.category1,
contract.category2,
contract.category3,
contract.goods_package_id,
contract.goods_spec_id,
contract.future_code,
contract.domain_code,
contract.goods_name,
contract.goods_id,
contract.commodity_name,
contract.contract_num,
contract.total_amount,
contract.payment_type,
contract.pay_condition_id,
contract.credit_days,
contract.delivery_factory_code,
contract.ship_warehouse_id,
contract.weight_tolerance,
contract.delivery_start_time,
contract.delivery_end_time,
contract.price_end_time,
contract.total_price_num,
contract.deposit_rate,
contract.deposit_amount,
contract.deposit_release_type,
contract.added_deposit,
contract.oem,
contract.tax_rate,
contract.invoice_type,
contract.is_stf,
contract.customer_contract_code,
contract.tag_config_ids,
contract.memo,
contract.sales_type,
contract.created_at,
contract.created_by,
contract.updated_at,
contract.updated_by,
contract.is_deleted,
contract.unit_price,
contract.fob_unit_price,
contract.cif_unit_price,
contract.delivery_type,
contract.price_end_type,
contract.belong_customer_id,
contract.able_reverse_price_times,
contract.company_id,
contract.company_name,
contract.close_tail_num,
contract.bu_code,
contract.warrant_cancel_count,
contract.contract_num - contract.warrant_cancel_count - contract.total_buy_back_num as can_cancel_count,
contract.write_off_status,
contract.warrant_trade_type,
contract.warrant_code,
contract.contract_nature,
contract.site_code,
contract.site_name,
contract.settle_type,
contract.is_soybean2,
contract.package_weight_value AS package_weight_name,
contract.weight_check_value AS weight_check_name,
contract.destination_value AS destination_name,
contract.delivery_type_value AS delivery_type_name,
contract.ship_warehouse_value AS ship_warehouse_name,
contract.write_off_start_time,
contract.write_off_end_time,
contract.warrant_id,
contract.exchange_code,
contract.standard_type,
contract.standard_file_id,
contract.standard_remark,
warrant.hold_count,
customer.enterprise_name AS enterprise_name,
supplier.enterprise_name AS supplier_enterprise_name,
business_person.name AS business_person_name,
create_by.name AS create_by_name,
p1.forward_price AS forward_price,
p1.extra_price AS extra_price,
p1.factory_price AS factory_price,
p1.protein_diff_price AS protein_diff_price,
p1.compensation_price AS compensation_price,
p1.option_price AS option_price,
p1.transport_price AS transport_price,
p1.lifting_price AS lifting_price,
p1.delay_price AS delay_price,
p1.temperature_price AS temperature_price,
p1.other_delivery_price AS other_delivery_price,
p1.buy_back_price AS buy_back_price,
p1.complaint_discount_price AS complaint_discount_price,
p1.transfer_factory_price AS transfer_factory_price,
p1.other_price AS other_price,
p1.business_price AS business_price,
p1.fee AS fee,
p1.shipping_fee_price AS shipping_fee_price,
p1.refine_diff_price AS refine_diff_price
FROM
dbt_contract contract
LEFT JOIN dbo.dba_customer customer ON contract.customer_id = customer.id
LEFT JOIN dbo.dba_customer supplier ON contract.supplier_id = supplier.id
LEFT JOIN dba_employ business_person ON contract.owner_id = business_person.id
LEFT JOIN dba_employ create_by ON contract.created_by = create_by.id
LEFT JOIN dbt_contract_price p1 ON contract.id = p1.contract_id AND p1.is_deleted = 0
LEFT JOIN dbk_warrant warrant on warrant.id = contract.warrant_id
WHERE
( SELECT COUNT ( * ) FROM dbt_contract_price AS p2 WHERE p1.contract_id = p2.contract_id AND p2.is_deleted = 0 AND p2.id >= p1.id ) <= 1;

GO
-- 刷新视图
sp_refreshview 'v_contract';