# PRD_1003312-界面优化——报表中心

## **1. 需求概述**
将合同报表页面的合同状态筛选控件从单选改为多选，以提升用户体验和查询效率。为避免影响现有功能，采用新建接口的方案。

## **2. 前端修改完成情况**

### **2.1 已完成的前端修改**
- ✅ **数据结构修改**: 将 `status` 从字符串改为数组
- ✅ **UI控件升级**: 添加 `isMultiple: true` 和 `tags: true` 支持多选
- ✅ **API调用更新**: 新增 `exportContractExcelMultiStatus` 接口调用
- ✅ **参数处理**: 使用 `statusList` 参数名传递多选状态数组

### **2.2 修改的文件**
1. `src/views/report-center/contractReport.vue` - 主要报表页面
2. `src/ajax/apiUrl/reportCenter.js` - API接口定义

## **3. 后端需要新建的接口**

### **3.1 新接口设计**
- **原接口**: `POST /magellan/contract/exportContractExcel` (保持不变)
- **新接口**: `POST /magellan/contract/exportContractExcelMultiStatus`
- **功能**: 支持多选合同状态的合同报表导出

### **3.2 接口参数对比**

**原接口参数:**
```java
public class ContractExportRequest {
    private String goodsCategoryId;
    private String salesType;
    private String signStartDate;
    private String signEndDate;
    private String deliveryStartTime;
    private String deliveryEndTime;
    private String deliveryFactoryName;
    private String status; // 单个状态
}
```

**新接口参数:**
```java
public class ContractExportMultiStatusRequest {
    private String goodsCategoryId;
    private String salesType;
    private String signStartDate;
    private String signEndDate;
    private String deliveryStartTime;
    private String deliveryEndTime;
    private String deliveryFactoryName;
    private List<String> statusList; // 多个状态列表
}
```

## **4. Controller层实现**

```java
@RestController
@RequestMapping("/magellan/contract")
public class ContractController {
    
    // 保持原接口不变
    @PostMapping("/exportContractExcel")
    public ResponseEntity<byte[]> exportContractExcel(@RequestBody ContractExportRequest request) {
        // 原有逻辑保持不变
        return contractService.exportContractExcel(request);
    }
    
    // 新增多状态支持接口
    @PostMapping("/exportContractExcelMultiStatus")
    public ResponseEntity<byte[]> exportContractExcelMultiStatus(@RequestBody ContractExportMultiStatusRequest request) {
        // 参数验证
        validateMultiStatusRequest(request);
        
        // 调用新的服务方法
        byte[] excelData = contractService.exportContractExcelWithMultiStatus(request);
        
        // 返回Excel文件
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "合同报表.xls");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(excelData);
    }
    
    private void validateMultiStatusRequest(ContractExportMultiStatusRequest request) {
        // 验证状态值是否有效
        if (request.getStatusList() != null) {
            for (String status : request.getStatusList()) {
                if (!ContractStatus.isValidStatus(status)) {
                    throw new InvalidParameterException("无效的合同状态: " + status);
                }
            }
        }
    }
}
```

## **5. Service层实现**

```java
@Service
public class ContractService {
    
    // 保持原方法不变
    public byte[] exportContractExcel(ContractExportRequest request) {
        // 原有逻辑保持不变
        return generateExcelReport(buildSingleStatusQuery(request));
    }
    
    // 新增多状态支持方法
    public byte[] exportContractExcelWithMultiStatus(ContractExportMultiStatusRequest request) {
        // 构建查询参数
        Map<String, Object> queryParams = buildMultiStatusQuery(request);
        
        // 查询数据
        List<ContractReportData> reportData = contractMapper.selectContractReportDataMultiStatus(queryParams);
        
        // 生成Excel
        return generateExcelReport(reportData);
    }
    
    private Map<String, Object> buildMultiStatusQuery(ContractExportMultiStatusRequest request) {
        Map<String, Object> params = new HashMap<>();
        
        // 复制原有参数逻辑
        params.put("goodsCategoryId", request.getGoodsCategoryId());
        params.put("salesType", request.getSalesType());
        params.put("signStartDate", request.getSignStartDate());
        params.put("signEndDate", request.getSignEndDate());
        params.put("deliveryStartTime", request.getDeliveryStartTime());
        params.put("deliveryEndTime", request.getDeliveryEndTime());
        params.put("deliveryFactoryName", request.getDeliveryFactoryName());
        
        // 新增多状态处理逻辑
        if (request.getStatusList() != null && !request.getStatusList().isEmpty()) {
            params.put("statusList", request.getStatusList());
            params.put("hasStatusFilter", true);
        } else {
            params.put("hasStatusFilter", false);
        }
        
        return params;
    }
    
    // 复用原有Excel生成逻辑
    private byte[] generateExcelReport(List<ContractReportData> reportData) {
        // 与原接口完全相同的Excel生成逻辑
        // ...
    }
}
```

## **6. Mapper层实现**

```java
@Mapper
public interface ContractMapper {
    
    // 保持原方法不变
    List<ContractReportData> selectContractReportData(Map<String, Object> params);
    
    // 新增多状态查询方法
    List<ContractReportData> selectContractReportDataMultiStatus(Map<String, Object> params);
}
```

## **7. MyBatis XML查询语句**

```xml
<!-- 保持原查询不变 -->
<select id="selectContractReportData" parameterType="map" resultType="ContractReportData">
    SELECT 
        contract_id,
        contract_code,
        contract_status,
        sign_date,
        delivery_time,
        factory_code
        -- 其他字段...
    FROM contract_table 
    WHERE 1=1
    
    <!-- 原有查询条件保持不变 -->
    <if test="goodsCategoryId != null and goodsCategoryId != ''">
        AND goods_category_id = #{goodsCategoryId}
    </if>
    
    <if test="salesType != null and salesType != ''">
        AND sales_type = #{salesType}
    </if>
    
    <if test="signStartDate != null and signStartDate != ''">
        AND sign_date >= #{signStartDate}
    </if>
    
    <if test="signEndDate != null and signEndDate != ''">
        AND sign_date <= #{signEndDate}
    </if>
    
    <if test="deliveryStartTime != null and deliveryStartTime != ''">
        AND delivery_time >= #{deliveryStartTime}
    </if>
    
    <if test="deliveryEndTime != null and deliveryEndTime != ''">
        AND delivery_time <= #{deliveryEndTime}
    </if>
    
    <if test="deliveryFactoryName != null and deliveryFactoryName != ''">
        AND delivery_factory_name = #{deliveryFactoryName}
    </if>
    
    <!-- 原有单状态过滤 -->
    <if test="status != null and status != ''">
        AND contract_status = #{status}
    </if>
    
    ORDER BY sign_date DESC
</select>

<!-- 新增多状态查询 -->
<select id="selectContractReportDataMultiStatus" parameterType="map" resultType="ContractReportData">
    SELECT 
        contract_id,
        contract_code,
        contract_status,
        sign_date,
        delivery_time,
        factory_code
        -- 其他字段...
    FROM contract_table 
    WHERE 1=1
    
    <!-- 复制所有原有查询条件 -->
    <if test="goodsCategoryId != null and goodsCategoryId != ''">
        AND goods_category_id = #{goodsCategoryId}
    </if>
    
    <if test="salesType != null and salesType != ''">
        AND sales_type = #{salesType}
    </if>
    
    <if test="signStartDate != null and signStartDate != ''">
        AND sign_date >= #{signStartDate}
    </if>
    
    <if test="signEndDate != null and signEndDate != ''">
        AND sign_date <= #{signEndDate}
    </if>
    
    <if test="deliveryStartTime != null and deliveryStartTime != ''">
        AND delivery_time >= #{deliveryStartTime}
    </if>
    
    <if test="deliveryEndTime != null and deliveryEndTime != ''">
        AND delivery_time <= #{deliveryEndTime}
    </if>
    
    <if test="deliveryFactoryName != null and deliveryFactoryName != ''">
        AND delivery_factory_name = #{deliveryFactoryName}
    </if>
    
    <!-- 新增多状态过滤逻辑 -->
    <if test="hasStatusFilter">
        AND contract_status IN
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </if>
    
    ORDER BY sign_date DESC
</select>
```

## **8. 数据传输对象 (DTO)**

```java
// 新增多状态请求DTO
public class ContractExportMultiStatusRequest {
    private String goodsCategoryId;
    private String salesType;
    private String signStartDate;
    private String signEndDate;
    private String deliveryStartTime;
    private String deliveryEndTime;
    private String deliveryFactoryName;
    private List<String> statusList;

    // 构造函数
    public ContractExportMultiStatusRequest() {}

    // Getter和Setter方法
    public String getGoodsCategoryId() { return goodsCategoryId; }
    public void setGoodsCategoryId(String goodsCategoryId) { this.goodsCategoryId = goodsCategoryId; }

    public String getSalesType() { return salesType; }
    public void setSalesType(String salesType) { this.salesType = salesType; }

    public String getSignStartDate() { return signStartDate; }
    public void setSignStartDate(String signStartDate) { this.signStartDate = signStartDate; }

    public String getSignEndDate() { return signEndDate; }
    public void setSignEndDate(String signEndDate) { this.signEndDate = signEndDate; }

    public String getDeliveryStartTime() { return deliveryStartTime; }
    public void setDeliveryStartTime(String deliveryStartTime) { this.deliveryStartTime = deliveryStartTime; }

    public String getDeliveryEndTime() { return deliveryEndTime; }
    public void setDeliveryEndTime(String deliveryEndTime) { this.deliveryEndTime = deliveryEndTime; }

    public String getDeliveryFactoryName() { return deliveryFactoryName; }
    public void setDeliveryFactoryName(String deliveryFactoryName) { this.deliveryFactoryName = deliveryFactoryName; }

    public List<String> getStatusList() { return statusList; }
    public void setStatusList(List<String> statusList) { this.statusList = statusList; }
}
```

## **9. 合同状态枚举验证**

```java
public enum ContractStatus {
    NOT_EFFECTIVE("1", "未生效"),
    EFFECTIVE("2", "生效中"),
    MODIFYING("3", "修改中"),
    COMPLETED("4", "已完成"),
    CANCELLED("5", "已作废"),
    CLOSING("6", "关闭中"),
    CLOSED("7", "已关闭"),
    SPLITTING("8", "拆分中"),
    LKG_EXCEPTION("9", "lkg异常");

    private final String code;
    private final String description;

    ContractStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getDescription() { return description; }

    // 验证状态是否有效
    public static boolean isValidStatus(String statusCode) {
        if (statusCode == null || statusCode.trim().isEmpty()) {
            return false;
        }

        for (ContractStatus status : ContractStatus.values()) {
            if (status.getCode().equals(statusCode)) {
                return true;
            }
        }
        return false;
    }

    // 获取所有有效状态代码
    public static List<String> getAllValidCodes() {
        return Arrays.stream(ContractStatus.values())
                .map(ContractStatus::getCode)
                .collect(Collectors.toList());
    }
}
```

## **10. 测试用例**

### **10.1 单元测试**
```java
@Test
public void testExportContractExcelMultiStatus_WithMultipleStatuses() {
    // 准备测试数据
    ContractExportMultiStatusRequest request = new ContractExportMultiStatusRequest();
    request.setGoodsCategoryId("1");
    request.setSalesType("2");
    request.setStatusList(Arrays.asList("1", "2", "4")); // 未生效、生效中、已完成

    // 执行测试
    byte[] result = contractService.exportContractExcelWithMultiStatus(request);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.length > 0);
}

@Test
public void testExportContractExcelMultiStatus_WithEmptyStatusList() {
    // 测试空状态列表（应该返回所有状态的数据）
    ContractExportMultiStatusRequest request = new ContractExportMultiStatusRequest();
    request.setGoodsCategoryId("1");
    request.setSalesType("2");
    request.setStatusList(new ArrayList<>());

    byte[] result = contractService.exportContractExcelWithMultiStatus(request);

    assertNotNull(result);
}

@Test(expected = InvalidParameterException.class)
public void testExportContractExcelMultiStatus_WithInvalidStatus() {
    // 测试无效状态值
    ContractExportMultiStatusRequest request = new ContractExportMultiStatusRequest();
    request.setStatusList(Arrays.asList("1", "INVALID_STATUS"));

    contractService.exportContractExcelWithMultiStatus(request);
}
```

### **10.2 集成测试**
```java
@Test
public void testExportContractExcelMultiStatusAPI() throws Exception {
    ContractExportMultiStatusRequest request = new ContractExportMultiStatusRequest();
    request.setGoodsCategoryId("1");
    request.setSalesType("2");
    request.setStatusList(Arrays.asList("1", "2"));

    mockMvc.perform(post("/magellan/contract/exportContractExcelMultiStatus")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(header().string("Content-Type", "application/octet-stream"))
            .andExpect(header().string("Content-Disposition", "attachment; filename=\"合同报表.xls\""));
}
```

## **11. 性能考虑**

### **11.1 数据库索引**
确保合同状态字段有适当的索引以支持IN查询：
```sql
-- 如果不存在，创建合同状态索引
CREATE INDEX idx_contract_status ON contract_table(contract_status);

-- 复合索引（如果经常同时按品类和状态查询）
CREATE INDEX idx_contract_category_status ON contract_table(goods_category_id, contract_status);
```

### **11.2 查询优化**
- IN查询通常比多个OR条件更高效
- 当状态列表很长时，考虑分批查询
- 监控查询执行计划，确保使用了正确的索引

## **12. 部署和回滚计划**

### **12.1 部署步骤**
1. **后端部署**：
   - 部署新的Controller、Service、Mapper方法
   - 新增数据库查询语句
   - 不影响现有功能

2. **前端部署**：
   - 前端代码已完成修改
   - 测试多选功能

3. **验证**：
   - 确认新接口正常工作
   - 确认原接口未受影响

### **12.2 回滚方案**
- 如果新接口有问题，可以快速回滚前端调用原接口
- 后端新增的代码不会影响现有功能
- 数据库无结构变更，无需回滚

## **13. API文档**

```yaml
/magellan/contract/exportContractExcelMultiStatus:
  post:
    summary: 导出合同报表（支持多选状态）
    description: 支持多个合同状态同时筛选的合同报表导出功能
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              goodsCategoryId:
                type: string
                description: 商品品类ID
              salesType:
                type: string
                description: 销售类型
              signStartDate:
                type: string
                format: date
                description: 签订开始日期
              signEndDate:
                type: string
                format: date
                description: 签订结束日期
              deliveryStartTime:
                type: string
                description: 交提货开始时间
              deliveryEndTime:
                type: string
                description: 交提货结束时间
              deliveryFactoryName:
                type: string
                description: 交货工厂名称
              statusList:
                type: array
                items:
                  type: string
                description: 合同状态列表
                example: ["1", "2", "4"]
    responses:
      200:
        description: Excel文件
        content:
          application/octet-stream:
            schema:
              type: string
              format: binary
```

## **14. 总结**

### **14.1 前端完成情况**
- ✅ 合同状态控件已改为多选
- ✅ UI使用collapse tags优化显示
- ✅ API调用已更新为新接口
- ✅ 代码注释已按规范添加

### **14.2 后端实现要点**
1. **保持现有系统稳定**：原接口完全不变，不影响其他功能
2. **实现多选功能**：新接口专门支持多选合同状态
3. **代码复用**：新接口复制原有逻辑，只修改状态处理部分
4. **数据库优化**：使用IN条件进行高效查询
5. **易于维护**：新旧接口并存，便于测试和回滚

### **14.3 实现优势**
- **零风险**：不影响现有功能
- **高效率**：用户可同时选择多个状态进行筛选
- **易维护**：代码结构清晰，便于后续维护
- **可扩展**：为后续类似需求提供了实现模式

### **14.4 前端修改文件清单**
1. `src/views/report-center/contractReport.vue` - 主要报表页面
2. `src/ajax/apiUrl/reportCenter.js` - API接口定义

### **14.5 后端需要新增的文件/方法**
1. **Controller**: `ContractController.exportContractExcelMultiStatus()`
2. **Service**: `ContractService.exportContractExcelWithMultiStatus()`
3. **Mapper**: `ContractMapper.selectContractReportDataMultiStatus()`
4. **DTO**: `ContractExportMultiStatusRequest`
5. **XML**: 新增多状态查询SQL语句

前端代码已完成并添加了规范的注释，后端按照此PRD实现即可完成整个功能。
