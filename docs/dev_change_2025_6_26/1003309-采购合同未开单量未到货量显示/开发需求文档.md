# 采购合同未开单量和未到货量显示优化开发需求文档

## 1. 需求概述

### 1.1 基本信息
- **Case编号**: 1003309
- **Case名称**: 采购合同未开单量未到货量显示优化
- **关联TAPD**: 1003227
- **优先级**: 高
- **开发人员**: <PERSON> Shi
- **开发日期**: 2025-06-27

### 1.2 需求描述
优化现货采购合同详情页面中"未开单量"和"未到货量"字段的显示逻辑，根据交货方式采用不同的计算方式，提高数据准确性。

### 1.3 适用范围
- **业务类型**: 现货
- **合同类型**: 采购 (sales_type = 1)
- **影响系统**: Columbus（内部用户）、Magellan（外部用户）

## 2. 需求分析

### 2.1 现状分析
根据代码分析，当前系统中：

1. **未开单量计算**：
   - 通过ATLAS Open quantity_contract接口获取
   - 代码位置：`ContractQueryLogicServiceImpl.getContractDetail()`
   - 计算公式：`合同数量 - 已开单数量`

2. **未到货量计算**：
   - 当前使用：`合同数量 - totalDeliveryNum`
   - totalDeliveryNum来源：`AtlasMappingContractEntity.executedNum`

3. **交货方式判断**：
   - 存储在：`dbt_contract.delivery_type`字段
   - 关联表：`dbt_delivery_type`
   - "仓单转让"对应name='仓单转让'

### 2.2 问题识别
1. `dbi_atlas_contract_execute.executed_num`字段当前缺乏赋值逻辑
2. 不同交货方式下未到货量计算逻辑需要区分
3. 需要建立ATLAS AC-96报文数据处理机制

### 2.3 业务规则
**当交货方式 ≠ "仓单转让"时：**
- 未开单量 = Open quantity from ATLAS
- 未到货量 = 合同总数量 - executed_num字段值

**当交货方式 = "仓单转让"时：**
- 未开单量 = Open quantity from ATLAS
- 未到货量 = Open quantity from ATLAS

## 3. 技术方案设计

### 3.1 数据库变更

#### 3.1.1 executed_num字段赋值逻辑
**数据来源**：
- 接口：`/DR/ackContractExeUpdate`回调接口的传入参数（JSON格式）
- 条件：`operation_type = 'DR_CONTRACT_EXE_UPDATE'` and 当交货方式不等于“仓单转让”
- 处理逻辑：当`creationStatus=3`时，累加`plannedQuantity`到对应合同的`executed_num`

#### 3.1.2 相关表结构
```sql
-- dbi_atlas_contract_execute表
executed_num decimal(15,6) -- 已执行数量，需要建立赋值逻辑

-- dbi_atlas_operation_log表  
operation_type varchar -- 操作类型，筛选'DR_CONTRACT_EXE_UPDATE'
response_info text -- JSON格式，包含creationStatus和plannedQuantity
```

### 3.2 后端API修改

#### 3.2.1 核心修改文件
1. **ContractQueryLogicServiceImpl.java**
   - 方法：`getContractDetail()`
   - 位置：第1537-1558行
   - 修改：增加交货方式判断逻辑

2. **AtlasSyncCallbackServiceImpl.java**
   - 新增：DR_CONTRACT_EXE_UPDATE数据处理逻辑
   - 功能：解析AC-96报文，更新executed_num（AC-96报文是一个atlas回调Navigator的接口， 更新给航海家执行的量， 他会设计多个表的插入和更新，dbi_atlas_operation_log就是其中的一个表 ）

#### 3.2.2 新增服务方法
```java
// 新增方法：处理ATLAS执行数据更新
public void processContractExecuteUpdate(AtlasOperationLogEntity logEntity) {
   // 解析response_info中的JSON数据
   // 当creationStatus=3时，累加plannedQuantity到executed_num
}

// 修改方法：根据交货方式计算未到货量
private BigDecimal calculateNotDeliveryNum(ContractEntity contract, BigDecimal openQuantity) {
   if ("仓单转让".equals(contract.getDeliveryTypeValue())) {
      return openQuantity; // 使用ATLAS开放量
   } else {
      return contract.getContractNum().subtract(getExecutedNum(contract));
   }
}
```

### 3.3 前端界面修改

#### 3.3.1 影响页面
1. **Columbus系统**：
   - 文件：合同详情页面组件
   - 接口：`/contract/getContractByContractId`

2. **Magellan系统**：
   - 文件：合同详情页面组件
   - 接口：`/contract/getContractByContractId`

#### 3.3.2 显示逻辑
- 前端无需修改，后端API返回正确计算结果即可
- 确保字段名称保持一致：`notBillNum`、`notDeliveryNum`

## 4. 实施计划

### 4.1 开发步骤
1. **第一阶段**：数据处理逻辑开发
   - 实现executed_num字段赋值逻辑
   - 开发AC-96报文解析功能

2. **第二阶段**：业务逻辑修改
   - 修改未到货量计算逻辑
   - 增加交货方式判断

3. **第三阶段**：测试验证
   - 单元测试
   - 集成测试
   - 用户验收测试

### 4.2 关键文件清单
```
后端修改：
├── ContractQueryLogicServiceImpl.java (核心逻辑修改)
├── AtlasSyncCallbackServiceImpl.java (新增数据处理)
├── AtlasMappingContractEntity.java (确认字段定义)
└── 相关测试文件

数据库：
├── executed_num字段数据修复脚本
└── 历史数据处理脚本
```

## 5. 测试用例设计

### 5.1 功能测试用例

#### 5.1.1 交货方式为"仓单转让"
- **输入**：现货采购合同，交货方式="仓单转让"
- **预期**：未到货量 = ATLAS Open quantity
- **验证**：检查API返回值

#### 5.1.2 交货方式为其他方式
- **输入**：现货采购合同，交货方式≠"仓单转让"
- **预期**：未到货量 = 合同数量 - executed_num
- **验证**：检查计算逻辑

#### 5.1.3 AC-96报文处理
- **输入**：creationStatus=3的DR_CONTRACT_EXE_UPDATE报文
- **预期**：executed_num字段正确累加
- **验证**：数据库字段值变化

### 5.2 边界测试用例
1. executed_num为null的情况
2. ATLAS接口返回异常的情况
3. 交货方式字段为空的情况

## 6. 风险评估

### 6.1 技术风险
- **风险**：ATLAS接口调用失败
- **影响**：未开单量显示异常
- **缓解**：增加异常处理和默认值逻辑

### 6.2 数据风险
- **风险**：历史数据executed_num字段为空
- **影响**：未到货量计算错误
- **缓解**：编写数据修复脚本

### 6.3 业务风险
- **风险**：交货方式判断逻辑错误
- **影响**：业务数据显示不准确
- **缓解**：充分测试各种交货方式场景

## 7. 上线计划

### 7.1 部署顺序
1. 数据库脚本执行
2. 后端服务部署
3. 前端页面更新（如需要）
4. 功能验证

### 7.2 回滚方案
- 保留原有计算逻辑作为备用
- 准备数据回滚脚本
- 监控关键业务指标

## 8. 验收标准

### 8.1 功能验收
- [ ] 仓单转让合同未到货量使用ATLAS开放量
- [ ] 非仓单转让合同未到货量使用executed_num计算
- [ ] AC-96报文正确更新executed_num字段
- [ ] Columbus和Magellan系统显示一致

### 8.2 性能验收
- [ ] 合同详情页面加载时间无明显增加
- [ ] ATLAS接口调用响应时间在可接受范围内

### 8.3 稳定性验收
- [ ] 异常情况下系统正常运行
- [ ] 数据一致性得到保证
