# 采购合同未开单量未到货量显示优化项目

## 项目概述

本项目旨在优化现货采购合同详情页面中"未开单量"和"未到货量"字段的显示逻辑，根据不同的交货方式采用相应的计算方式，提高数据准确性和业务适用性。

## 项目信息

- **Case编号**: 1003309
- **关联TAPD**: 1003227
- **开发人员**: <PERSON> Shi
- **开发日期**: 2025-06-27
- **优先级**: 高

## 核心需求

### 业务逻辑优化

**当交货方式 ≠ "仓单转让"时：**
- 未开单量 = Open quantity from ATLAS
- 未到货量 = 合同总数量 - executed_num字段值

**当交货方式 = "仓单转让"时：**
- 未开单量 = Open quantity from ATLAS  
- 未到货量 = Open quantity from ATLAS

### 数据处理增强

- 建立`dbi_atlas_contract_execute.executed_num`字段的赋值逻辑
- 处理ATLAS AC-96报文数据（operation_type = 'DR_CONTRACT_EXE_UPDATE'）
- 当creationStatus=3时，累加plannedQuantity到对应合同的executed_num字段

## 技术架构

```
前端层 (Columbus/Magellan)
    ↓ HTTP请求
业务层 (ContractQueryLogicServiceImpl)
    ↓ 数据查询  
数据层 (dbi_atlas_contract_execute, dbi_atlas_operation_log)
    ↓ 外部接口
ATLAS系统 (Open quantity接口, AC-96报文)
```

## 主要修改点

### 后端代码修改

1. **ContractQueryLogicServiceImpl.java**
   - 位置：`getContractDetail()`方法（第1537-1558行）
   - 功能：增加交货方式判断逻辑，根据不同交货方式计算未到货量

2. **AtlasSyncCallbackServiceImpl.java**
   - 功能：新增AC-96报文处理逻辑
   - 作用：解析报文数据，更新executed_num字段

### 数据库变更

1. **历史数据修复**
   - 从`dbi_atlas_operation_log`表解析历史AC-96报文
   - 更新`dbi_atlas_contract_execute.executed_num`字段

2. **性能优化**
   - 创建相关索引提高查询性能
   - 建立监控视图便于数据观察

### 前端影响

- Columbus和Magellan系统的合同详情页面
- 无需修改前端代码，后端API返回正确计算结果
- 保持现有字段名称：`notBillNum`、`notDeliveryNum`

## 文档结构

```
docs/dev_change_2025_6_26/1003309-采购合同未开单量未到货量显示/
├── README.md                    # 项目总览（本文件）
├── 开发需求文档.md              # 详细需求分析和业务规则
├── 技术实现方案.md              # 技术架构和实现细节
├── 数据库变更脚本.sql           # 数据库修改和历史数据处理
└── 测试用例.md                  # 完整的测试用例和验收标准
```

## 关键技术点

### 1. 交货方式判断逻辑

```java
private boolean isWarrantTransfer(ContractEntity contractEntity) {
    return "仓单转让".equals(contractEntity.getDeliveryTypeValue());
}
```

### 2. 未到货量计算逻辑

```java
private BigDecimal calculateNotDeliveryNum(ContractEntity contractEntity, BigDecimal openQuantity) {
    if ("仓单转让".equals(contractEntity.getDeliveryTypeValue())) {
        return openQuantity; // 使用ATLAS开放量
    } else {
        return contractEntity.getContractNum().subtract(getExecutedNum(contractEntity));
    }
}
```

### 3. AC-96报文处理

```java
public void processContractExecuteUpdate(AtlasOperationLogEntity operationLogEntity) {
    // 解析JSON数据
    // 检查creationStatus=3
    // 累加plannedQuantity到executed_num
}
```

## 数据表关系

```
dbt_contract (合同主表)
    ├── contract_code (合同编号)
    ├── delivery_type_value (交货方式)
    └── contract_num (合同数量)

dbi_atlas_contract_execute (ATLAS合同执行表)
    ├── nav_contract_code (关联合同编号)
    ├── executed_num (已执行数量) ← 核心字段
    └── allocated_num (已分配数量)

dbi_atlas_operation_log (ATLAS操作日志表)
    ├── operation_type (操作类型)
    ├── biz_code (业务编码)
    └── response_info (响应信息JSON) ← AC-96报文数据
```

## 实施步骤

### 第一阶段：数据处理
1. 执行数据库变更脚本
2. 修复历史executed_num数据
3. 建立AC-96报文处理逻辑

### 第二阶段：业务逻辑
1. 修改合同详情查询逻辑
2. 实现交货方式判断
3. 优化未到货量计算

### 第三阶段：测试验证
1. 单元测试和集成测试
2. 功能测试和性能测试
3. 用户验收测试

## 质量保证

### 测试覆盖
- **功能测试**: 覆盖所有交货方式和业务场景
- **性能测试**: 确保页面加载性能无明显下降
- **异常测试**: 验证ATLAS接口异常时的处理逻辑
- **兼容性测试**: 确保Columbus和Magellan系统一致性

### 数据安全
- 执行前备份关键数据
- 提供完整的回滚方案
- 建立数据质量检查机制

### 监控机制
- 创建监控视图观察数据变化
- 记录关键操作日志
- 建立异常告警机制

## 风险控制

### 技术风险
- **ATLAS接口依赖**: 增加异常处理和缓存机制
- **数据一致性**: 建立事务处理和数据校验
- **性能影响**: 优化查询逻辑和创建索引

### 业务风险
- **计算逻辑错误**: 充分测试各种业务场景
- **历史数据问题**: 编写数据修复和验证脚本
- **用户体验**: 保持界面一致性和响应速度

## 上线计划

### 部署顺序
1. 数据库脚本执行（维护窗口期）
2. 后端服务灰度发布
3. 功能验证和监控
4. 全量发布

### 验收标准
- [ ] 仓单转让合同未到货量使用ATLAS开放量
- [ ] 非仓单转让合同未到货量使用executed_num计算  
- [ ] AC-96报文正确更新executed_num字段
- [ ] Columbus和Magellan系统显示一致
- [ ] 页面加载性能无明显下降
- [ ] 异常情况处理正常

## 后续维护

### 监控指标
- ATLAS接口调用成功率
- executed_num字段更新频率
- 合同详情页面响应时间
- 数据计算准确性

### 优化方向
- 缓存策略优化
- 批量数据处理
- 实时数据同步
- 用户体验提升

## 联系信息

- **开发负责人**: Jason Shi (<EMAIL>)
- **项目经理**: [待补充]
- **测试负责人**: [待补充]
- **运维负责人**: [待补充]

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-27 | 初始版本，完成需求分析和技术方案 | Jason Shi |

---

**注意**: 本项目涉及核心业务逻辑修改，请严格按照文档执行，确保充分测试后再上线。
