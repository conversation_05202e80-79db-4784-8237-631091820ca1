-- =====================================================
-- 采购合同未开单量未到货量显示优化 - 数据库变更脚本
-- Case: 1003309
-- Author: <PERSON> Shi
-- Date: 2025-06-27
-- 说明: 历史数据修正由单独脚本处理，此脚本仅包含必要的索引和监控视图
-- =====================================================

-- =====================================================
-- 1. 创建性能优化索引
-- =====================================================

-- 1.1 为dbi_atlas_contract_execute表创建索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_atlas_contract_execute_nav_code_executed')
BEGIN
    CREATE INDEX IX_atlas_contract_execute_nav_code_executed
    ON dbi_atlas_contract_execute(nav_contract_code, executed_num)
    INCLUDE (contract_num, updated_at);

    PRINT '创建索引：IX_atlas_contract_execute_nav_code_executed';
END

-- 1.2 为dbi_atlas_operation_log表创建索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_atlas_operation_log_type_bizcode_date')
BEGIN
    CREATE INDEX IX_atlas_operation_log_type_bizcode_date
    ON dbi_atlas_operation_log(operation_type, biz_code, created_at)
    INCLUDE (response_info);

    PRINT '创建索引：IX_atlas_operation_log_type_bizcode_date';
END

-- =====================================================
-- 2. 脚本执行总结
-- =====================================================

SELECT
    '脚本执行总结' as summary_type,
    GETDATE() as execution_time,
    '数据库变更脚本执行完成' as status,
    '性能优化索引创建完成' as next_step;

-- =====================================================
-- 3. 回滚脚本（紧急情况使用）
-- =====================================================

/*
-- 回滚脚本 - 仅在紧急情况下使用

-- 删除创建的索引
DROP INDEX IF EXISTS IX_atlas_contract_execute_nav_code_executed ON dbi_atlas_contract_execute;
DROP INDEX IF EXISTS IX_atlas_operation_log_type_bizcode_date ON dbi_atlas_operation_log;
*/
