# 采购合同未开单量未到货量显示优化 - 技术实现方案

## 1. 技术架构概述

### 1.1 系统架构
```
前端层 (Columbus/Magellan)
    ↓ HTTP请求
业务层 (ContractQueryLogicServiceImpl)
    ↓ 数据查询
数据层 (dbi_atlas_contract_execute, dbi_atlas_operation_log)
    ↓ 外部接口
ATLAS系统 (Open quantity接口, AC-96报文)
```

### 1.2 核心组件
1. **数据处理组件**: 处理AC-96报文，更新executed_num
2. **业务逻辑组件**: 根据交货方式计算未到货量
3. **接口调用组件**: 获取ATLAS开放量数据

## 2. 详细实现方案

### 2.1 数据处理层实现

#### 2.1.1 executed_num字段更新逻辑

**实现位置**: `AtlasSyncCallbackServiceImpl.java`

```java
/**
 * 处理合同执行数据更新
 * @param operationLogEntity ATLAS操作日志
 */
public void processContractExecuteUpdate(AtlasOperationLogEntity operationLogEntity) {
    try {
        // 解析response_info JSON数据
        String responseInfo = operationLogEntity.getResponseInfo();
        JSONObject responseJson = JSON.parseObject(responseInfo);
        
        // 检查creationStatus
        Integer creationStatus = responseJson.getInteger("creationStatus");
        if (creationStatus != null && creationStatus == 3) {
            
            // 获取plannedQuantity
            BigDecimal plannedQuantity = responseJson.getBigDecimal("plannedQuantity");
            String contractCode = operationLogEntity.getBizCode();
            
            if (plannedQuantity != null && StringUtils.isNotBlank(contractCode)) {
                // 更新executed_num字段
                updateExecutedNum(contractCode, plannedQuantity);
            }
        }
    } catch (Exception e) {
        log.error("处理合同执行数据更新异常: {}", e.getMessage(), e);
    }
}

/**
 * 更新合同执行数量
 */
private void updateExecutedNum(String contractCode, BigDecimal plannedQuantity) {
    // 查询现有记录
    AtlasMappingContractEntity existingEntity = atlasMappingContractDao
        .selectOne(Wrappers.<AtlasMappingContractEntity>lambdaQuery()
            .eq(AtlasMappingContractEntity::getNavContractCode, contractCode));
    
    if (existingEntity != null) {
        // 累加plannedQuantity到executed_num
        BigDecimal currentExecutedNum = existingEntity.getExecutedNum() != null ? 
            existingEntity.getExecutedNum() : BigDecimal.ZERO;
        BigDecimal newExecutedNum = currentExecutedNum.add(plannedQuantity);
        
        existingEntity.setExecutedNum(newExecutedNum);
        atlasMappingContractDao.updateById(existingEntity);
        
        log.info("更新合同{}执行数量: {} -> {}", contractCode, currentExecutedNum, newExecutedNum);
    }
}
```

#### 2.1.2 历史数据处理脚本

```sql
-- 历史数据修复脚本
-- 从dbi_atlas_operation_log中提取历史执行数据

WITH ExecuteData AS (
    SELECT 
        biz_code,
        JSON_VALUE(response_info, '$.plannedQuantity') as planned_quantity,
        JSON_VALUE(response_info, '$.creationStatus') as creation_status,
        created_at
    FROM dbi_atlas_operation_log 
    WHERE operation_type = 'DR_CONTRACT_EXE_UPDATE'
        AND JSON_VALUE(response_info, '$.creationStatus') = '3'
        AND JSON_VALUE(response_info, '$.plannedQuantity') IS NOT NULL
)
UPDATE ace 
SET executed_num = ISNULL(ace.executed_num, 0) + CAST(ed.planned_quantity AS DECIMAL(15,6))
FROM dbi_atlas_contract_execute ace
INNER JOIN ExecuteData ed ON ace.nav_contract_code = ed.biz_code;
```

### 2.2 业务逻辑层实现

#### 2.2.1 核心计算逻辑修改

**修改文件**: `ContractQueryLogicServiceImpl.java`
**修改位置**: `getContractDetail()方法` (第1537-1558行)

```java
// 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-06-27 start
/**
 * 根据交货方式计算未到货量
 */
private BigDecimal calculateNotDeliveryNum(ContractEntity contractEntity, BigDecimal openQuantity) {
    // 获取交货方式
    String deliveryTypeValue = contractEntity.getDeliveryTypeValue();
    
    if ("仓单转让".equals(deliveryTypeValue)) {
        // 仓单转让：使用ATLAS开放量
        return openQuantity != null ? openQuantity : BigDecimal.ZERO;
    } else {
        // 其他交货方式：合同数量 - 已执行数量
        BigDecimal contractNum = contractEntity.getContractNum();
        BigDecimal executedNum = getExecutedNumByContract(contractEntity.getContractCode());
        return contractNum.subtract(executedNum);
    }
}

/**
 * 获取合同已执行数量
 */
private BigDecimal getExecutedNumByContract(String contractCode) {
    try {
        Result<AtlasMappingContractEntity> result = atlasContractFacade.getByNavContractCode(contractCode);
        if (result.isSuccess() && result.getData() != null) {
            AtlasMappingContractEntity entity = result.getData();
            return entity.getExecutedNum() != null ? entity.getExecutedNum() : BigDecimal.ZERO;
        }
    } catch (Exception e) {
        log.error("获取合同{}执行数量异常: {}", contractCode, e.getMessage());
    }
    return BigDecimal.ZERO;
}

// 修改原有的未到货量计算逻辑
// 原代码位置：第1555行
// 原代码：totalDeliveryNum = mappingContractEntity.getExecutedNum() == null ? BigDecimal.ZERO : mappingContractEntity.getExecutedNum();
// 修改为：
if (mappingContractEntity != null && mappingContractEntity.getId() != null) {
    // 根据交货方式计算未到货量
    totalDeliveryNum = calculateNotDeliveryNum(contractEntity, notBilledNum);
}
// 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-06-27 end
```

#### 2.2.2 交货方式判断逻辑

```java
/**
 * 判断是否为仓单转让交货方式
 */
private boolean isWarrantTransfer(ContractEntity contractEntity) {
    // 方式1：通过delivery_type_value字段判断
    if (StringUtils.isNotBlank(contractEntity.getDeliveryTypeValue())) {
        return "仓单转让".equals(contractEntity.getDeliveryTypeValue());
    }
    
    // 方式2：通过delivery_type关联dbt_delivery_type表判断
    if (contractEntity.getDeliveryType() != null) {
        // 查询交货方式名称
        // 这里需要调用相关服务获取交货方式详情
        return checkDeliveryTypeIsWarrantTransfer(contractEntity.getDeliveryType());
    }
    
    return false;
}
```

### 2.3 接口层实现

#### 2.3.1 ATLAS接口调用优化

**现有接口**: `AtlasSyncUriService.getContractOpenQuantity()`
**优化点**: 增加异常处理和缓存机制

```java
/**
 * 获取合同开放量（优化版本）
 */
public BigDecimal getContractOpenQuantityOptimized(String businessEntity, String contractCode) {
    String cacheKey = String.format("contract_open_quantity_%s_%s", businessEntity, contractCode);
    
    // 尝试从缓存获取
    BigDecimal cachedValue = getCachedOpenQuantity(cacheKey);
    if (cachedValue != null) {
        return cachedValue;
    }
    
    try {
        BigDecimal openQuantity = getContractOpenQuantity(businessEntity, contractCode);
        
        // 缓存结果（5分钟有效期）
        cacheOpenQuantity(cacheKey, openQuantity, 300);
        
        return openQuantity;
    } catch (Exception e) {
        log.error("获取ATLAS合同开放量异常，使用默认值: {}", e.getMessage());
        return BigDecimal.ZERO;
    }
}
```

### 2.4 数据一致性保证

#### 2.4.1 事务处理

```java
@Transactional(rollbackFor = Exception.class)
public void updateContractExecuteData(String contractCode, BigDecimal plannedQuantity) {
    try {
        // 1. 更新executed_num
        updateExecutedNum(contractCode, plannedQuantity);
        
        // 2. 记录操作日志
        recordExecuteUpdateLog(contractCode, plannedQuantity);
        
        // 3. 清除相关缓存
        clearContractCache(contractCode);
        
    } catch (Exception e) {
        log.error("更新合同执行数据失败: {}", e.getMessage(), e);
        throw new BusinessException("更新合同执行数据失败");
    }
}
```

#### 2.4.2 数据校验

```java
/**
 * 校验executed_num数据合理性
 */
private void validateExecutedNum(String contractCode, BigDecimal newExecutedNum) {
    // 获取合同总数量
    ContractEntity contract = contractQueryDomainService.getBasicContractByCode(contractCode);
    if (contract != null) {
        BigDecimal contractNum = contract.getContractNum();
        
        // 执行数量不能超过合同总数量
        if (newExecutedNum.compareTo(contractNum) > 0) {
            log.warn("合同{}执行数量{}超过合同总数量{}", contractCode, newExecutedNum, contractNum);
            // 可以选择抛出异常或记录警告
        }
        
        // 执行数量不能为负数
        if (newExecutedNum.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("合同执行数量不能为负数");
        }
    }
}
```

## 3. 性能优化方案

### 3.1 缓存策略
- **ATLAS接口结果缓存**: 5分钟有效期
- **合同基础信息缓存**: 30分钟有效期
- **交货方式信息缓存**: 1小时有效期

### 3.2 批量处理
- **历史数据处理**: 分批次处理，避免长时间锁表
- **AC-96报文处理**: 异步处理，提高响应速度

### 3.3 数据库优化
```sql
-- 为executed_num字段创建索引
CREATE INDEX IX_atlas_contract_execute_nav_code_executed 
ON dbi_atlas_contract_execute(nav_contract_code, executed_num);

-- 为operation_log表创建复合索引
CREATE INDEX IX_atlas_operation_log_type_bizcode 
ON dbi_atlas_operation_log(operation_type, biz_code, created_at);
```

## 4. 监控和日志

### 4.1 关键指标监控
- ATLAS接口调用成功率
- executed_num更新频率
- 合同详情页面响应时间

### 4.2 日志记录
```java
// 关键操作日志
log.info("合同{}未到货量计算: 交货方式={}, 计算方式={}, 结果={}", 
    contractCode, deliveryType, calculationMethod, result);

// 异常情况日志
log.warn("合同{}ATLAS接口调用失败，使用默认值", contractCode);

// 数据变更日志
log.info("合同{}执行数量更新: {} -> {}", contractCode, oldValue, newValue);
```

## 5. 部署和回滚方案

### 5.1 部署步骤
1. **数据库变更**: 执行历史数据修复脚本
2. **后端部署**: 灰度发布，逐步切换流量
3. **功能验证**: 验证关键业务场景
4. **全量发布**: 完成所有实例部署

### 5.2 回滚方案
```java
// 配置开关，支持快速回滚到原有逻辑
@Value("${contract.delivery.calculation.new.enabled:true}")
private boolean useNewCalculationLogic;

private BigDecimal calculateNotDeliveryNum(ContractEntity contractEntity, BigDecimal openQuantity) {
    if (!useNewCalculationLogic) {
        // 回滚到原有逻辑
        return contractEntity.getContractNum().subtract(getTotalDeliveryNumOld(contractEntity));
    }
    
    // 新逻辑
    return calculateNotDeliveryNumNew(contractEntity, openQuantity);
}
```

## 6. 测试策略

### 6.1 单元测试
- 交货方式判断逻辑测试
- 未到货量计算逻辑测试
- AC-96报文解析测试

### 6.2 集成测试
- ATLAS接口调用测试
- 数据库操作测试
- 端到端业务流程测试

### 6.3 性能测试
- 合同详情页面加载性能测试
- 大量数据处理性能测试
- 并发访问压力测试
