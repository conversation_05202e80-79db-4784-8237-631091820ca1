# 采购合同未开单量未到货量显示优化 - 测试用例

## 1. 测试概述

### 1.1 测试目标
验证采购合同详情页面中"未开单量"和"未到货量"字段根据交货方式正确计算和显示。

### 1.2 测试范围
- 现货采购合同（sales_type = 1, contract_nature = 1）
- Columbus和Magellan系统
- 不同交货方式的计算逻辑
- ATLAS接口集成
- 数据处理逻辑

### 1.3 测试环境
- 开发环境：用于单元测试和集成测试
- 测试环境：用于系统测试和用户验收测试
- 预生产环境：用于性能测试和最终验证

## 2. 功能测试用例

### 2.1 交货方式为"仓单转让"的测试用例

#### 测试用例 TC001：仓单转让合同未到货量计算
**测试目标**：验证仓单转让合同的未到货量使用ATLAS开放量

**前置条件**：
- 存在现货采购合同，交货方式为"仓单转让"
- ATLAS系统返回正常的开放量数据

**测试步骤**：
1. 登录Columbus/Magellan系统
2. 查询现货采购合同列表
3. 选择交货方式为"仓单转让"的合同
4. 点击查看合同详情
5. 检查"未开单量"和"未到货量"字段

**预期结果**：
- 未开单量 = ATLAS返回的开放量
- 未到货量 = ATLAS返回的开放量
- 两个字段值相等

**测试数据**：
```
合同编号：TEST_CONTRACT_001
合同数量：1000吨
交货方式：仓单转让
ATLAS开放量：800吨
executed_num：200吨
```

**预期显示**：
- 未开单量：800吨
- 未到货量：800吨

---

#### 测试用例 TC002：仓单转让合同ATLAS接口异常处理
**测试目标**：验证ATLAS接口异常时的处理逻辑

**前置条件**：
- 存在现货采购合同，交货方式为"仓单转让"
- 模拟ATLAS接口调用失败

**测试步骤**：
1. 模拟ATLAS接口返回异常
2. 访问合同详情页面
3. 检查字段显示和错误处理

**预期结果**：
- 系统不崩溃
- 未开单量和未到货量显示为0或默认值
- 记录错误日志

### 2.2 交货方式为非"仓单转让"的测试用例

#### 测试用例 TC003：非仓单转让合同未到货量计算
**测试目标**：验证非仓单转让合同的未到货量计算逻辑

**前置条件**：
- 存在现货采购合同，交货方式为"工厂自提"
- dbi_atlas_contract_execute表中有对应的executed_num数据

**测试步骤**：
1. 登录系统查看合同详情
2. 检查"未开单量"和"未到货量"字段

**测试数据**：
```
合同编号：TEST_CONTRACT_002
合同数量：1000吨
交货方式：工厂自提
ATLAS开放量：800吨
executed_num：150吨
```

**预期显示**：
- 未开单量：800吨（来自ATLAS）
- 未到货量：850吨（1000 - 150）

---

#### 测试用例 TC004：executed_num为NULL的处理
**测试目标**：验证executed_num字段为NULL时的处理逻辑

**前置条件**：
- 合同的executed_num字段为NULL

**测试步骤**：
1. 查看合同详情
2. 检查未到货量计算

**预期结果**：
- 未到货量 = 合同数量 - 0（将NULL视为0）
- 系统正常运行，不报错

### 2.3 数据处理测试用例

#### 测试用例 TC005：AC-96报文处理
**测试目标**：验证AC-96报文正确更新executed_num字段

**前置条件**：
- 系统接收到AC-96报文
- 报文中creationStatus=3，plannedQuantity有值

**测试步骤**：
1. 模拟发送AC-96报文
2. 检查dbi_atlas_contract_execute表的executed_num字段
3. 验证累加逻辑

**测试数据**：
```json
{
  "creationStatus": 3,
  "plannedQuantity": "100.000000",
  "contractNo": "TEST_CONTRACT_003"
}
```

**预期结果**：
- executed_num字段正确累加100
- 操作日志记录处理过程

---

#### 测试用例 TC006：重复报文处理
**测试目标**：验证重复AC-96报文的处理逻辑

**前置条件**：
- 同一合同收到多条AC-96报文

**测试步骤**：
1. 发送第一条报文，plannedQuantity=100
2. 发送第二条报文，plannedQuantity=50
3. 检查executed_num累加结果

**预期结果**：
- executed_num = 原值 + 100 + 50
- 每条报文都被正确处理

## 3. 界面测试用例

### 3.1 Columbus系统界面测试

#### 测试用例 TC007：Columbus合同详情页面显示
**测试目标**：验证Columbus系统中合同详情页面字段显示

**测试步骤**：
1. 登录Columbus系统
2. 进入合同管理模块
3. 查看不同交货方式的合同详情
4. 检查"未开单量"和"未到货量"字段位置和数值

**预期结果**：
- 字段位置正确
- 数值计算准确
- 界面显示正常

### 3.2 Magellan系统界面测试

#### 测试用例 TC008：Magellan合同详情页面显示
**测试目标**：验证Magellan系统中合同详情页面字段显示

**测试步骤**：
1. 登录Magellan系统
2. 查看合同详情页面
3. 对比Columbus系统显示结果

**预期结果**：
- 两个系统显示结果一致
- 字段格式正确

## 4. 性能测试用例

### 4.1 响应时间测试

#### 测试用例 TC009：合同详情页面加载性能
**测试目标**：验证修改后页面加载性能无明显下降

**测试步骤**：
1. 记录修改前页面加载时间
2. 部署新版本
3. 测试修改后页面加载时间
4. 对比性能差异

**性能指标**：
- 页面加载时间 < 3秒
- 性能下降 < 10%

### 4.2 并发测试

#### 测试用例 TC010：并发访问测试
**测试目标**：验证多用户同时访问合同详情的性能

**测试步骤**：
1. 模拟50个用户同时访问不同合同详情
2. 监控系统响应时间和资源使用
3. 检查数据一致性

**预期结果**：
- 系统稳定运行
- 响应时间在可接受范围内
- 无数据错误

## 5. 异常测试用例

### 5.1 网络异常测试

#### 测试用例 TC011：ATLAS接口超时处理
**测试目标**：验证ATLAS接口超时时的处理逻辑

**测试步骤**：
1. 模拟ATLAS接口响应超时
2. 访问合同详情页面
3. 检查系统行为

**预期结果**：
- 页面正常显示
- 使用默认值或缓存值
- 记录超时日志

### 5.2 数据异常测试

#### 测试用例 TC012：异常数据处理
**测试目标**：验证异常数据的处理能力

**测试数据**：
- executed_num为负数
- 合同数量为0
- 交货方式字段为空

**预期结果**：
- 系统不崩溃
- 给出合理的默认值
- 记录异常日志

## 6. 兼容性测试用例

### 6.1 浏览器兼容性

#### 测试用例 TC013：多浏览器兼容性测试
**测试目标**：验证不同浏览器下的显示效果

**测试环境**：
- Chrome 最新版
- Firefox 最新版
- Edge 最新版
- Safari（如适用）

**预期结果**：
- 所有浏览器显示一致
- 功能正常运行

## 7. 回归测试用例

### 7.1 原有功能验证

#### 测试用例 TC014：原有功能回归测试
**测试目标**：确保修改不影响原有功能

**测试范围**：
- 合同创建功能
- 合同修改功能
- 合同查询功能
- 其他相关业务流程

**预期结果**：
- 所有原有功能正常
- 无新增缺陷

## 8. 用户验收测试用例

### 8.1 业务场景测试

#### 测试用例 TC015：完整业务流程测试
**测试目标**：验证完整的业务流程

**测试步骤**：
1. 创建现货采购合同
2. 设置不同交货方式
3. 模拟ATLAS数据更新
4. 查看合同详情页面
5. 验证数据准确性

**验收标准**：
- 业务流程顺畅
- 数据计算准确
- 用户体验良好

## 9. 测试数据准备

### 9.1 测试合同数据
```sql
-- 仓单转让合同
INSERT INTO dbt_contract (contract_code, contract_num, sales_type, contract_nature, delivery_type_value, status)
VALUES ('TEST_WT_001', 1000.000000, 1, 1, '仓单转让', 2);

-- 工厂自提合同
INSERT INTO dbt_contract (contract_code, contract_num, sales_type, contract_nature, delivery_type_value, status)
VALUES ('TEST_ZT_001', 1500.000000, 1, 1, '工厂自提', 2);

-- 对应的executed_num数据
INSERT INTO dbi_atlas_contract_execute (nav_contract_code, executed_num)
VALUES ('TEST_ZT_001', 200.000000);
```

### 9.2 ATLAS模拟数据
```json
{
  "TEST_WT_001": {"openQuantity": "800.000000"},
  "TEST_ZT_001": {"openQuantity": "1200.000000"}
}
```

## 10. 测试执行计划

### 10.1 测试阶段
1. **单元测试**：开发完成后立即执行
2. **集成测试**：单元测试通过后执行
3. **系统测试**：集成测试通过后执行
4. **用户验收测试**：系统测试通过后执行

### 10.2 测试时间安排
- 单元测试：1天
- 集成测试：2天
- 系统测试：3天
- 用户验收测试：2天
- 总计：8天

### 10.3 测试通过标准
- 所有测试用例执行通过率 ≥ 95%
- 无严重缺陷
- 性能指标满足要求
- 用户验收通过
