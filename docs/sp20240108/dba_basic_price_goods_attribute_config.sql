/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 18/01/2024 14:44:01
*/


-- ----------------------------
-- Table structure for dba_basic_price_goods_attribute_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_basic_price_goods_attribute_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_basic_price_goods_attribute_config]
GO

CREATE TABLE [dbo].[dba_basic_price_goods_attribute_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [price_goods_config_id] int  NULL,
  [attribute_value_id] int  NULL,
  [created_at] datetime  NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dba_basic_price_goods_attribute_config] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'基差基准价货品id',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_attribute_config',
'COLUMN', N'price_goods_config_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'货品规格属性id',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_attribute_config',
'COLUMN', N'attribute_value_id'
GO


-- ----------------------------
-- Records of dba_basic_price_goods_attribute_config
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_basic_price_goods_attribute_config] ON
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'1', N'1', N'16', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'2', N'1', N'17', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'3', N'1', N'18', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'4', N'1', N'19', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'5', N'1', N'20', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'6', N'2', N'21', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'7', N'3', N'22', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'8', N'2', N'23', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'9', N'1', N'24', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'10', N'1', N'25', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'11', N'1', N'26', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'12', N'1', N'27', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'13', N'1', N'28', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'14', N'1', N'29', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'15', N'1', N'30', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'16', N'1', N'31', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'17', N'1', N'32', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'18', N'1', N'33', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'19', N'6', N'37', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'20', N'4', N'38', N'2024-01-17 13:42:49.000', N'1')
GO

INSERT INTO [dbo].[dba_basic_price_goods_attribute_config] ([id], [price_goods_config_id], [attribute_value_id], [created_at], [created_by]) VALUES (N'21', N'5', N'39', N'2024-01-17 13:42:49.000', N'1')
GO

SET IDENTITY_INSERT [dbo].[dba_basic_price_goods_attribute_config] OFF
GO


-- ----------------------------
-- Auto increment value for dba_basic_price_goods_attribute_config
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_basic_price_goods_attribute_config]', RESEED, 21)
GO


-- ----------------------------
-- Primary Key structure for table dba_basic_price_goods_attribute_config
-- ----------------------------
ALTER TABLE [dbo].[dba_basic_price_goods_attribute_config] ADD CONSTRAINT [PK__dba_basi__3213E83FBE9E322F] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

