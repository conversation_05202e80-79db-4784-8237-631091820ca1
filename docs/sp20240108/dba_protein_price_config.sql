/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 19/01/2024 14:05:46
*/


-- ----------------------------
-- Table structure for dba_protein_price_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_protein_price_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_protein_price_config]
GO

CREATE TABLE [dbo].[dba_protein_price_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [rule_item_id] int  NULL,
  [company_id] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [rule_id] int  NULL,
  [category_id] int  NULL,
  [factory_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [delivery_begin_date] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_deleted] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL,
  [attribute_value_id] int NULL
)
GO

ALTER TABLE [dbo].[dba_protein_price_config] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for dba_protein_price_config
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_protein_price_config]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dba_protein_price_config
-- ----------------------------
ALTER TABLE [dbo].[dba_protein_price_config] ADD CONSTRAINT [PK__dba_prot__3213E83F7894AE51] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

