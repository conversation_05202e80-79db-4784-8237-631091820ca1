/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 18/01/2024 14:43:52
*/


-- ----------------------------
-- Table structure for dba_basic_price_goods_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_basic_price_goods_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_basic_price_goods_config]
GO

CREATE TABLE [dbo].[dba_basic_price_goods_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category_id] int  NULL
)
GO

ALTER TABLE [dbo].[dba_basic_price_goods_config] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_config',
'COLUMN', N'name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_config',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_config',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类Id',
'SCHEMA', N'dbo',
'TABLE', N'dba_basic_price_goods_config',
'COLUMN', N'category_id'
GO


-- ----------------------------
-- Records of dba_basic_price_goods_config
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_basic_price_goods_config] ON
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'1', N'脱胶毛豆油', NULL, NULL, N'12')
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'2', N'一级大豆油', NULL, NULL, N'12')
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'3', N'三级大豆油', NULL, NULL, N'12')
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'4', N'一级大豆油,RZ', NULL, NULL, N'12')
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'5', N'三级大豆油,RZ', NULL, NULL, N'12')
GO

INSERT INTO [dbo].[dba_basic_price_goods_config] ([id], [name], [created_at], [created_by], [category_id]) VALUES (N'6', N'脱胶毛豆油,RZ', NULL, NULL, N'12')
GO

SET IDENTITY_INSERT [dbo].[dba_basic_price_goods_config] OFF
GO


-- ----------------------------
-- Auto increment value for dba_basic_price_goods_config
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_basic_price_goods_config]', RESEED, 6)
GO


-- ----------------------------
-- Primary Key structure for table dba_basic_price_goods_config
-- ----------------------------
ALTER TABLE [dbo].[dba_basic_price_goods_config] ADD CONSTRAINT [PK__dba_basi__3213E83F2432C094] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

