--新增基差基准价配置
INSERT INTO [dbo].[dbz_system_rule]([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (11, 0, 'S0015', '蛋白价差配置', 0, 1, '2024-01-11 05:57:54.600', '2024-01-11 05:57:54.600');
INSERT INTO [dbo].[dbz_system_rule]([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (12, 0, 'S0015', '蛋白价差配置', 0, 1, '2024-01-11 05:58:00.987', '2024-01-11 05:58:00.987');

--新增用户按钮权限
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (564, NULL, NULL, N'业务配置-蛋白价差', NULL, 1, 1, 1, 0, '2024-01-25 02:15:58.267', '2024-01-25 02:15:58.267');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (565, N'SBM_S_106', N'SBM_S_106', N'新增蛋白价差', NULL, 564, 2, 1, 0, '2024-01-25 02:15:58.323', '2024-01-25 02:15:58.323');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (566, N'SBM_S_107', N'SBM_S_107', N'编辑蛋白价差', NULL, 564, 2, 1, 0, '2024-01-25 02:15:58.360', '2024-01-25 02:15:58.360');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (567, N'SBM_S_108', N'SBM_S_108', N'启用/禁用蛋白价差', NULL, 564, 2, 1, 0, '2024-01-25 02:19:29.683', '2024-01-25 02:19:29.683');



