update dbt_delivery_type set atlas_code = type;
update dbt_delivery_type set atlas_code = 2 where atlas_code = '3';
update dbt_delivery_type set bu_code = 'ST';
update dbt_delivery_type set bu_code = 'WT';

-- 初始化提货委托文件配置
INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( 0, 0, 'S0022', '提货委托文件配置', 0, 1, GETDATE(), GETDATE());
DECLARE @ruleId int = ( select id from dbz_system_rule where code = 'S0022');
INSERT INTO [dbo].[dbz_system_rule_item] ([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id], [mdm_code]) VALUES (@ruleId, '提货委托文件配置', 2, 0, 0, 1, GETDATE(), GETDATE(), '', 1, 1, N'MGL', 'https://csm4nnvgsto001.blob.core.chinacloudapi.cn/dev/dev/fileRecord/upload/file/magellan/2024-10-09/4f18335b15b8462d9348aa0ccd0f9df1_MGL提货委托申请下载模版.xlsx', N'', N'admin1', N'admin1', NULL, NULL, N'');
INSERT INTO [dbo].[dbz_system_rule_item] ([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id], [mdm_code]) VALUES (@ruleId, '提货委托文件配置', 2, 0, 0, 1, GETDATE(), GETDATE(), '', 1, 1, N'CLB', 'https://csm4nnvgsto001.blob.core.chinacloudapi.cn/dev/dev/fileRecord/upload/file/magellan/2024-10-09/4c8a8def986a486fb1a9f15784d1e735_CLB提货委托申请下载模版.xlsx', N'', N'admin1', N'admin1', NULL, NULL, N'');

