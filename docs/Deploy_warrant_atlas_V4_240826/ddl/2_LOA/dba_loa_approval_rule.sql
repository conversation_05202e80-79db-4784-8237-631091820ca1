/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 03/09/2024 10:12:44
*/


-- ----------------------------
-- Table structure for dba_loa_approval_rule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_loa_approval_rule]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_loa_approval_rule]
GO

CREATE TABLE [dbo].[dba_loa_approval_rule] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [category2] int  NULL,
  [sales_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [bu_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [tt_type] int  NULL,
  [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL
)
GO

ALTER TABLE [dbo].[dba_loa_approval_rule] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务线',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'bu_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'tt_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则类容',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'rule_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'created_at'
GO


-- ----------------------------
-- Records of dba_loa_approval_rule
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_loa_approval_rule] ON
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'1', N'11', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'2', N'11', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_BUYBACK_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_BUYBACK_ABC2");
end

rule SBM_A_BUYBACK_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_BUYBACK_ABC1");
        update($approveRule);
end

rule SBM_A_BUYBACK_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_BUYBACK_AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'3', N'11', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_CLOSED_A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("SBM_A_CLOSED_A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'4', N'11', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_REVISE_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_REVISE_ABC2");
end

rule SBM_A_REVISE_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_ABC1");
        update($approveRule);
end

rule SBM_A_REVISE_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_AB");
        update($approveRule);
end

rule SBM_A_REVISE_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_REVISE_A1");
        update($approveRule);
end

rule SBM_A_REVISE_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("SBM_A_REVISE_A2");
        update($approveRule);
end

rule SBM_A_REVISE_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("SBM_A_REVISE_A3");
        update($approveRule);
end

rule SBM_A_REVISE_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("SBM_A_REVISE_A4");
        update($approveRule);
end

rule SBM_A_REVISE_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("SBM_A_REVISE_A5");
        update($approveRule);
end

rule SBM_A_REVISE_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("SBM_A_REVISE_A6");
        update($approveRule);
end

rule SBM_A_REVISE_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("SBM_A_REVISE_A7");
        update($approveRule);
end

rule SBM_A_REVISE_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A8");
        update($approveRule);
end

rule SBM_A_REVISE_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'5', N'11', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_SPLIT_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_SPLIT_ABC2");
end

rule SBM_A_SPLIT_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额超过#MAX_AMOUNT#;"));
        System.out.println("SBM_A_SPLIT_ABC1");
        update($approveRule);
end

rule SBM_A_SPLIT_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_SPLIT_AB");
        update($approveRule);
end

rule SBM_A_SPLIT_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_SPLIT_A1");
        update($approveRule);
end

rule SBM_A_SPLIT_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("SBM_A_SPLIT_A2");
        update($approveRule);
end

rule SBM_A_SPLIT_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("SBM_A_SPLIT_A3");
        update($approveRule);
end

rule SBM_A_SPLIT_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("SBM_A_SPLIT_A4");
        update($approveRule);
end

rule SBM_A_SPLIT_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("SBM_A_SPLIT_A5");
        update($approveRule);
end

rule SBM_A_SPLIT_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("SBM_A_SPLIT_A6");
        update($approveRule);
end

rule SBM_A_SPLIT_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("SBM_A_SPLIT_A7");
        update($approveRule);
end

rule SBM_A_SPLIT_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_SPLIT_A8");
        update($approveRule);
end

rule SBM_A_SPLIT_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_SPLIT_A9");
        update($approveRule);
end


rule SBM_A_SPLIT_A10
//    no-loop true
//    lock-on-active true
//    salience -1
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("SBM_A_SPLIT_A10");
        update($approveRule);
end
/*
rule SBM_A_SPLIT_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println("SBM_A_SPLIT_A7");
        update($approveRule);
end

rule SBM_A_SPLIT_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_SPLIT_A8");
        update($approveRule);
end

rule SBM_A_SPLIT_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_SPLIT_A9");
        update($approveRule);
end

rule SBM_A_SPLIT_A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_SPLIT_A10");
        update($approveRule);
end

rule SBM_A_SPLIT_A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_SPLIT_A11");
        update($approveRule);
end

rule SBM_A_SPLIT_A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_SPLIT_A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'6', N'11', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule SBM_A_WASHOUT_INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule SBM_A_WASHOUT_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_WASHOUT_ABC2");
end

rule SBM_A_WASHOUT_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_ABC1");
        update($approveRule);
end

rule SBM_A_WASHOUT_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'7', N'12', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'8', N'12', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_BUYBACK_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_BUYBACK_ABC2");
end

rule SBM_A_BUYBACK_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_BUYBACK_ABC1");
        update($approveRule);
end

rule SBM_A_BUYBACK_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_BUYBACK_AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'9', N'12', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_CLOSED_A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("SBM_A_CLOSED_A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'10', N'12', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_REVISE_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_REVISE_ABC2");
end

rule SBM_A_REVISE_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_ABC1");
        update($approveRule);
end

rule SBM_A_REVISE_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_AB");
        update($approveRule);
end

rule SBM_A_REVISE_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_REVISE_A1");
        update($approveRule);
end

rule SBM_A_REVISE_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("SBM_A_REVISE_A2");
        update($approveRule);
end

rule SBM_A_REVISE_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("SBM_A_REVISE_A3");
        update($approveRule);
end

rule SBM_A_REVISE_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("SBM_A_REVISE_A4");
        update($approveRule);
end

rule SBM_A_REVISE_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("SBM_A_REVISE_A5");
        update($approveRule);
end

rule SBM_A_REVISE_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("SBM_A_REVISE_A6");
        update($approveRule);
end

rule SBM_A_REVISE_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("SBM_A_REVISE_A7");
        update($approveRule);
end

rule SBM_A_REVISE_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A8");
        update($approveRule);
end

rule SBM_A_REVISE_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'11', N'12', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_SPLIT_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_SPLIT_ABC2");
end

rule SBM_A_SPLIT_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额超过#MAX_AMOUNT#;"));
        System.out.println("SBM_A_SPLIT_ABC1");
        update($approveRule);
end

rule SBM_A_SPLIT_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_SPLIT_AB");
        update($approveRule);
end

rule SBM_A_SPLIT_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_SPLIT_A1");
        update($approveRule);
end

rule SBM_A_SPLIT_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("SBM_A_SPLIT_A2");
        update($approveRule);
end

rule SBM_A_SPLIT_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("SBM_A_SPLIT_A3");
        update($approveRule);
end

rule SBM_A_SPLIT_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("SBM_A_SPLIT_A4");
        update($approveRule);
end

rule SBM_A_SPLIT_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("SBM_A_SPLIT_A5");
        update($approveRule);
end

rule SBM_A_SPLIT_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("SBM_A_SPLIT_A6");
        update($approveRule);
end

rule SBM_A_SPLIT_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("SBM_A_SPLIT_A7");
        update($approveRule);
end

rule SBM_A_SPLIT_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_SPLIT_A8");
        update($approveRule);
end

rule SBM_A_SPLIT_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_SPLIT_A9");
        update($approveRule);
end


rule SBM_A_SPLIT_A10
//    no-loop true
//    lock-on-active true
//    salience -1
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("SBM_A_SPLIT_A10");
        update($approveRule);
end
/*
rule SBM_A_SPLIT_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println("SBM_A_SPLIT_A7");
        update($approveRule);
end

rule SBM_A_SPLIT_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_SPLIT_A8");
        update($approveRule);
end

rule SBM_A_SPLIT_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_SPLIT_A9");
        update($approveRule);
end

rule SBM_A_SPLIT_A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_SPLIT_A10");
        update($approveRule);
end

rule SBM_A_SPLIT_A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_SPLIT_A11");
        update($approveRule);
end

rule SBM_A_SPLIT_A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_SPLIT_A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'12', N'12', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule SBM_A_WASHOUT_INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule SBM_A_WASHOUT_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_WASHOUT_ABC2");
end

rule SBM_A_WASHOUT_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_ABC1");
        update($approveRule);
end

rule SBM_A_WASHOUT_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'13', N'11', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at]) VALUES (N'14', N'12', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000')
GO

SET IDENTITY_INSERT [dbo].[dba_loa_approval_rule] OFF
GO


-- ----------------------------
-- Auto increment value for dba_loa_approval_rule
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_loa_approval_rule]', RESEED, 14)
GO


-- ----------------------------
-- Primary Key structure for table dba_loa_approval_rule
-- ----------------------------
ALTER TABLE [dbo].[dba_loa_approval_rule] ADD CONSTRAINT [PK__dba_loa___3213E83F9ED6061B] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

