--dba_customer_original_paper
CREATE TABLE [dbo].[dba_customer_original_paper] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [customer_id] int DEFAULT ((0)) NULL,
  [category_id] int DEFAULT ((0)) NULL,
  [company_id] int DEFAULT ((0)) NULL,
  [ldc_frame] int DEFAULT ((0)) NULL,
  [sale_type] int DEFAULT ((0)) NULL,
  [original_paper] int DEFAULT ((0)) NULL,
  [status] int DEFAULT ((1)) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [created_by] int  NULL,
  [updated_by] int  NULL,
  [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  CONSTRAINT [PK__dba_customer_original_p_3213E83F71TG2F43] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
)
ON [PRIMARY]
GO

ALTER TABLE [dbo].[dba_customer_original_paper] SET (LOCK_ESCALATION = TABLE)
GO


--dba_customer_protocol
CREATE TABLE [dbo].[dba_customer_protocol] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [protocol_no] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [customer_id] int DEFAULT ((0)) NULL,
    [category_id] int DEFAULT ((0)) NULL,
    [company_id] int DEFAULT ((0)) NULL,
    [sale_type] int DEFAULT ((0)) NULL,
    [frame_protocol] int DEFAULT ((0)) NULL,
    [protocol_start_date] datetime  NULL,
    [protocol_end_date] datetime  NULL,
    [status] int DEFAULT ((1)) NULL,
    [is_deleted] int DEFAULT ((0)) NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    [created_by] int  NULL,
    [updated_by] int  NULL,
    [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    CONSTRAINT [PK__dba_customer_protoc_3213Q53F71TG2F43] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dba_customer_protocol] SET (LOCK_ESCALATION = TABLE)