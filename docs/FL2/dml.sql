ALTER TABLE [dbo].[dbg_goods] ADD [tax_rate] decimal(25,6) DEFAULT ((0)) NULL;

GO
ALTER TABLE [dbo].[dbt_contract_sign] ADD [invoke_no] nvarchar(255) DEFAULT '' NULL;

GO
ALTER TABLE [dbo].[dbt_contract_sign] ADD [confirm_status] int DEFAULT 0 NULL;
GO

update dbt_contract_sign set confirm_status = 0;

update dbg_goods set tax_rate = 9;

update dbz_system_rule_item set memo = '增值税专用发票,纸质' where id = 1;
update dbz_system_rule_item set memo = '增值税普通发票,纸质' where id = 2;
update dbz_system_rule_item set memo = '增值税普通发票,电子' where id = 3;
update dbz_system_rule_item set memo = '增值税普通发票,电子' where id = 4;
update dbz_system_rule_item set memo = '增值税普通发票,纸质' where id = 5;
update dbz_system_rule_item set memo = '增值税专用发票,纸质' where id = 6;
update dbz_system_rule_item set memo = '增值税专用发票,电子' where id = 469;
update dbz_system_rule_item set memo = '增值税专用发票,电子' where id = 468;
update dba_customer_detail set invoice_name = '增值税专用发票,纸质' where invoice_id = 1;
update dba_customer_detail set invoice_name = '增值税普通发票,纸质' where invoice_id = 2;
update dba_customer_detail set invoice_name = '增值税普通发票,电子' where invoice_id = 3;
update dba_customer_detail set invoice_name = '增值税普通发票,电子' where invoice_id = 4;
update dba_customer_detail set invoice_name = '增值税普通发票,纸质' where invoice_id = 5;
update dba_customer_detail set invoice_name = '增值税专用发票,纸质' where invoice_id = 6;
update dba_customer_detail set invoice_name = '增值税专用发票,电子' where invoice_id = 469;
update dba_customer_detail set invoice_name = '增值税专用发票,电子' where invoice_id = 468;



INSERT INTO [dbo].[dbz_operation_config]([id], [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (44, N'sbm_updateNF', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '豆粕 NF&正本更新');
INSERT INTO [dbo].[dbz_operation_config]([id], [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (45, N'sbo_updateNF', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '豆油 NF&正本更新');
INSERT INTO [dbo].[dbz_operation_config]([id], [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (46, N'sbm_updateCustomerProtocol', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '豆粕框架协议更新');
INSERT INTO [dbo].[dbz_operation_config]([id], [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (47, N'sbo_updateCustomerProtocol', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '豆油框架协议更新');

update [dbo].[dbz_operation_config] set memo = '客户配置--豆油通知人更新' where id = 30;
update [dbo].[dbz_operation_config] set memo = '客户配置--豆粕通知人更新' where id = 24;

ALTER TABLE [dbo].[dbt_contract_price] ADD [previous_record] text COLLATE Chinese_PRC_CI_AS  NULL;
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [customer_non_frame] int DEFAULT 0 NULL;
GO

update dbt_contract_sign set customer_non_frame = ldc_frame;