ALTER TABLE [dbo].[dbf_position]
    ADD [latest_message] ntext DEFAULT '' NULL
GO

ALTER TABLE [dbo].[dbf_position]
    ADD [transaction_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL

ALTER TABLE [dbo].[dbf_position]
    ADD [transaction_transfer_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL

ALTER TABLE [dbo].[dbf_position]
    ADD [change_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL

ALTER TABLE [dbo].[dbf_position]
    ADD [change_transfer_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL

ALTER TABLE [dbo].[dbf_position] ADD [modify_type] int  NULL

ALTER TABLE [dbo].[dbf_position] ADD [change_price] decimal(15,6) NULL

ALTER TABLE [dbo].[dbf_position] ADD [change_diff_price] decimal(15,6)  NULL

ALTER TABLE [dbo].[dbf_position] ADD [change_num] decimal(15,6) NULL
    GO
ALTER TABLE [dbo].[dbf_position] ADD [change_hand_num] decimal(15,6) NULL