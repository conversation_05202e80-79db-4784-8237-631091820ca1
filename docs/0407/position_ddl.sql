CREATE TABLE [dbo].[dbf_position_log] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [position_id] int DEFAULT ((0)) NULL,
    [position_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [type] int DEFAULT ((0)) NULL,
    [pending_type] int DEFAULT ((0)) NULL,
    [modify_type] int DEFAULT ((0)) NULL,
    [apply_hand_num] decimal(15,6) DEFAULT ((0)) NULL,
    [apply_num] decimal(15,6) DEFAULT ((0)) NULL,
    [apply_price] decimal(15,6) DEFAULT ((0)) NULL,
    [change_price] decimal(15,6) DEFAULT ((0)) NULL,
    [change_num] decimal(15,6) DEFAULT ((0)) NULL,
    [change_hand_num] decimal(15,6) DEFAULT ((0)) NULL,
    [audit_status] int DEFAULT ((0)) NULL,
    [memo] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [change_diff_price] decimal(15,6)  NULL,
    [apply_diff_price] decimal(15,6) DEFAULT ((0)) NULL,
    [apply_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [change_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [apply_transfer_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [change_transfer_domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dbf_posi__3213E83FCAA4599M] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbf_position_log] SET (LOCK_ESCALATION = TABLE)