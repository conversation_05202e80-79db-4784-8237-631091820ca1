IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[dbf_position]') AND type in (N'U'))
DROP TABLE [dbo].[dbf_position];
CREATE TABLE [dbo].[dbf_position](
    id int NOT NULL IDENTITY(1,1),
    category_id int,
    type int,
    code nvarchar(64),
    domain_code nvarchar(64),
    transfer_domain_code nvarchar(64),
    status int,
    pending_type int,
    close_type int,
    apply_hand_num decimal(25,6),
    apply_num decimal(25,6),
    apply_price decimal(25,6),
    apply_diff_price decimal(25,6),
    deal_num decimal(25,6),
    transaction_price decimal(25,6),
    transaction_diff_price decimal(15,6),
    not_deal_num decimal(15,6),
    orientation nvarchar(64),
    created_by_name nvarchar(255),
    cancel_reason nvarchar(255),
    created_by int,
    updated_by int,
    created_at DATETIME DEFAULT  (getdate()),
    updated_at DATETIME DEFAULT  (getdate()),
    is_deleted int DEFAULT  ((0)),
    PRIMARY KEY (id)
);

EXEC sp_addextendedproperty 'MS_Description', '持仓记录表', 'SCHEMA', dbo, 'table', dbf_position, null, null;
EXEC sp_addextendedproperty 'MS_Description', '自增ID', 'SCHEMA', dbo, 'table', dbf_position, 'column', id;
EXEC sp_addextendedproperty 'MS_Description', '品类id', 'SCHEMA', dbo, 'table', dbf_position, 'column', category_id;
EXEC sp_addextendedproperty 'MS_Description', '操作类型;1.点价 2.转月', 'SCHEMA', dbo, 'table', dbf_position, 'column', type;
EXEC sp_addextendedproperty 'MS_Description', '申请编号', 'SCHEMA', dbo, 'table', dbf_position, 'column', code;
EXEC sp_addextendedproperty 'MS_Description', '期货合约', 'SCHEMA', dbo, 'table', dbf_position, 'column', domain_code;
EXEC sp_addextendedproperty 'MS_Description', '转入合约', 'SCHEMA', dbo, 'table', dbf_position, 'column', transfer_domain_code;
EXEC sp_addextendedproperty 'MS_Description', '状态;1.待挂单 2.待成交 3.已成交  4.未成交', 'SCHEMA', dbo, 'table', dbf_position, 'column', status;
EXEC sp_addextendedproperty 'MS_Description', '挂单类型;1随盘 2挂单', 'SCHEMA', dbo, 'table', dbf_position, 'column', pending_type;
EXEC sp_addextendedproperty 'MS_Description', '平仓类型;1一口价合同平仓 2采购套保 3榨利头寸套保 4其他', 'SCHEMA', dbo, 'table', dbf_position, 'column', close_type;
EXEC sp_addextendedproperty 'MS_Description', '申请手数', 'SCHEMA', dbo, 'table', dbf_position, 'column', apply_hand_num;
EXEC sp_addextendedproperty 'MS_Description', '申请数量', 'SCHEMA', dbo, 'table', dbf_position, 'column', apply_num;
EXEC sp_addextendedproperty 'MS_Description', '申请价格', 'SCHEMA', dbo, 'table', dbf_position, 'column', apply_price;
EXEC sp_addextendedproperty 'MS_Description', '申请挂单价差', 'SCHEMA', dbo, 'table', dbf_position, 'column', apply_diff_price;
EXEC sp_addextendedproperty 'MS_Description', '成交数量', 'SCHEMA', dbo, 'table', dbf_position, 'column', deal_num;
EXEC sp_addextendedproperty 'MS_Description', '成交价格', 'SCHEMA', dbo, 'table', dbf_position, 'column', transaction_price;
EXEC sp_addextendedproperty 'MS_Description', '成交价差', 'SCHEMA', dbo, 'table', dbf_position, 'column', transaction_diff_price;
EXEC sp_addextendedproperty 'MS_Description', '未成交数量', 'SCHEMA', dbo, 'table', dbf_position, 'column', not_deal_num;
EXEC sp_addextendedproperty 'MS_Description', '买卖方向', 'SCHEMA', dbo, 'table', dbf_position, 'column', orientation;
EXEC sp_addextendedproperty 'MS_Description', '创建人姓名', 'SCHEMA', dbo, 'table', dbf_position, 'column', created_by_name;
EXEC sp_addextendedproperty 'MS_Description', '未成交原因', 'SCHEMA', dbo, 'table', dbf_position, 'column', cancel_reason;
EXEC sp_addextendedproperty 'MS_Description', '创建人', 'SCHEMA', dbo, 'table', dbf_position, 'column', created_by;
EXEC sp_addextendedproperty 'MS_Description', '更新人', 'SCHEMA', dbo, 'table', dbf_position, 'column', updated_by;
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', dbo, 'table', dbf_position, 'column', created_at;
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', dbo, 'table', dbf_position, 'column', updated_at;
EXEC sp_addextendedproperty 'MS_Description', '逻辑删除;（0未被删除 1已被删除）', 'SCHEMA', dbo, 'table', dbf_position, 'column', is_deleted;
