ALTER TABLE [dbo].[dbt_contract] ADD [is_over_forward] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否是超远月合同（0:否 1:是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'is_over_forward'

-- 超远月
UPDATE dbt_contract set is_over_forward = 1
WHERE
        sales_type = 2
  AND DATEDIFF( [month], concat ( '20', domain_code, '01' ), delivery_end_time ) >= 4;

-- 子合同超远月
UPDATE dbt_contract set is_over_forward = 1
WHERE
        sales_type = 2
  AND parent_id IN ( SELECT id FROM dbt_contract WHERE sales_type = 2 AND DATEDIFF( [month], concat ( '20', domain_code, '01' ), delivery_end_time ) >= 4 )
  AND contract_source IN ( 122, 123 );

UPDATE dbt_contract set is_over_forward = 0 WHERE is_over_forward IS NULL

-- 转月次数
UPDATE dbt_contract SET total_transfer_times = 2,able_transfer_times = total_transfer_times-transferred_times WHERE is_over_forward = 1 AND total_transfer_times != 4
