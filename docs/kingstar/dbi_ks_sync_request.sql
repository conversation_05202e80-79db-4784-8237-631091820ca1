-- ----------------------------
-- Table structure for dbi_ks_sync_request
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_ks_sync_request]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_ks_sync_request]
    GO

CREATE TABLE [dbo].[dbi_ks_sync_request] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [instruct_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [biz_id] int  NULL,
    [biz_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [biz_type] int  NULL,
    [operation_type] int  NULL,
    [sync_time] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [sync_status] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [user_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [try_times] int  NULL,
    [request_url] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [token] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [request_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [response_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [remark] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主键ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'指令id-申请单号,订单唯一（uuid）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'instruct_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'biz_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据号，本次为申请单号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'biz_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务类型:1.点价 2.转月 3.反点价',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'biz_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'处理类型:1.点价 2.点价移仓 3.撤单 4.改单 5.反点价 6.反点价移仓',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'operation_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'sync_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'sync_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口同步的userCode',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'user_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'try_times'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求的url',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'request_url'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口同步的token',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'token'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'传输原始信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'request_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'返回信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'response_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息传输用户',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息最后修改用户',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'修改时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_request',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dbi_ks_sync_request
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_ks_sync_request]', RESEED, 1)
    GO


-- ----------------------------
-- Primary Key structure for table dbi_ks_sync_request
-- ----------------------------
ALTER TABLE [dbo].[dbi_ks_sync_request] ADD CONSTRAINT [PK__dbi_atla__3213E83FF728E164] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

