-- ----------------------------
-- Table structure for dbi_ks_sync_callback
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_ks_sync_callback]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_ks_sync_callback]
    GO

CREATE TABLE [dbo].[dbi_ks_sync_callback] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [uuid] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [instruct_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [instruct_status] int  NULL,
    [ack_data] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [return_msg] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [status] nvarchar(32) COLLATE Chinese_PRC_CI_AS DEFAULT 'pending' NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'uuid',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'uuid'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'指令id-申请单号,订单唯一',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'instruct_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'指令状态 1=待挂单、2=待成交、3=未成交、4=成交待分配、5=部分成交',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'instruct_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'KingStar系统回传信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'ack_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执信息（成功/失败+描述）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'return_msg'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回调处理状态（pending/success/fail）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_ks_sync_callback',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dbi_ks_sync_callback
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_ks_sync_callback]', RESEED, 1)
    GO


-- ----------------------------
-- Indexes structure for table dbi_ks_sync_callback
-- ----------------------------
CREATE NONCLUSTERED INDEX [IDX_dbi_ks_sync_callback_instruct_id]
ON [dbo].[dbi_ks_sync_callback] (
  [instruct_id] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dbi_ks_sync_callback
-- ----------------------------
ALTER TABLE [dbo].[dbi_ks_sync_callback] ADD CONSTRAINT [PK__dbi_atla__3213E83FBF93659C] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

