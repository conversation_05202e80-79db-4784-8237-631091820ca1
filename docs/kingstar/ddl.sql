-- =============================================
-- 修改表结构：dbf_price_apply 增加字段及注释
-- =============================================

ALTER TABLE [dbo].[dbf_price_apply] ADD [position_action] NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE [dbo].[dbf_price_apply] ADD [interface_status] INT NULL;
ALTER TABLE [dbo].[dbf_price_apply] ADD [pending_result] NVARCHAR(255) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE [dbo].[dbf_price_apply] ADD [change_result] NVARCHAR(255) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE [dbo].[dbf_price_apply] ADD [hms_instruct] NVARCHAR(64) COLLATE Chinese_PRC_CI_AS NULL;

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'开平方向(0.开仓 1.平仓 2.平今仓 3.自动开平)', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbf_price_apply',
    @level2type = N'COLUMN', @level2name = N'position_action';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'接口状态（1.挂单中 2.改单中 3.撤单中 4.已完成 5.不调用接口-失败后不调用）', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbf_price_apply',
    @level2type = N'COLUMN', @level2name = N'interface_status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'挂单结果', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbf_price_apply',
    @level2type = N'COLUMN', @level2name = N'pending_result';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'改撤单结果', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbf_price_apply',
    @level2type = N'COLUMN', @level2name = N'change_result';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'最新的hms系统指令ID', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbf_price_apply',
    @level2type = N'COLUMN', @level2name = N'hms_instruct';

-- =============================================
-- 修改表结构：dbt_contract 增加字段及注释
-- =============================================

ALTER TABLE [dbo].[dbt_contract] ADD [is_reverse_price] INT NULL;

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'是否发起反点价（0.否 1.是）', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbt_contract',
    @level2type = N'COLUMN', @level2name = N'is_reverse_price';

-- =============================================
-- 修改表结构：dbt_contract_history 增加字段及注释
-- =============================================

ALTER TABLE [dbo].[dbt_contract_history] ADD [is_reverse_price] INT NULL;

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'是否发起反点价（0.否 1.是）', 
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE',  @level1name = N'dbt_contract_history',
    @level2type = N'COLUMN', @level2name = N'is_reverse_price';
