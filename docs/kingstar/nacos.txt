-- 1.navigator-commons.yaml 增加 kingstar 配置(具体配置视环境而定)
messageQueue:
  kingstar:
    syncQueueName: nva-int-queue-01
    syncDeadLetterName: nva-int-queue-01/$deadletterqueue

-- 2.navigator-future.yaml 增加 kingstar 配置(具体配置视环境而定)
kingstar:
  syncStatus: 1
  useMessageQueue: 0
  # ESB_to_Navigator
  esbToNavigator:
    authentication:
      api-key: x-Gateway-APIKey
      api-value: 8dc5fd69-60a2-4814-a145-9956c1d54f9d
      api-matchers: /kingstar/callback
  # Navigator_to_ESB
  navigatorToEsb:
    authentication:
      api-key: x-Gateway-APIKey
      api-value: a0ebe09d-64e5-4ba0-aec2-1d38bea186c1
  endpoint:
    # url: https://di-api-gw-cn-val.d1.ad.local:8443/gateway/ProxyKingstar/1.0/gw
    # url: https://doc-tst-internal.ldcchina.cn/gw
    url: http://127.0.0.1:4523/m1/6551660-6255785-default
  notice:
    # email: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    email: <EMAIL>,<EMAIL>
servicebus:
  jms:
    enabled: true

-- 3.navigator-cuckoo.yaml 增加 serviceBus 配置
servicebus:
  jms:
    enabled: true

-- 4.增加KingStar时间条件单配置
Data ID:	kingstar-sync-rules.json
Group:	    BUSINESS_SYNC_GROUP
Format:	    json
Content:
{
    "syncControlRules": [
        {
            "exchange": "DCE",
            "startTime": "00:00:00",
            "endTime": "08:54:59",
            "businessTypes": [
                {
                    "type": "点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "反点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "套保平仓-点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "转月",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                },
                {
                    "type": "套保平仓-转月",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                },
                {
                    "type": "套利",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                }
            ]
        },
        {
            "exchange": "DCE",
            "startTime": "23:00:00",
            "endTime": "23:59:59",
            "businessTypes": [
                {
                    "type": "点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "反点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "套保平仓-点价",
                    "syncEnable": false,
                    "syncReleaseTime": "08:55:00"
                },
                {
                    "type": "转月",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                },
                {
                    "type": "套保平仓-转月",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                },
                {
                    "type": "套利",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                }
            ]
        },
        {
            "exchange": "DCE",
            "startTime": "10:15:00",
            "endTime": "10:29:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "10:30:00"
                }
            ]
        },
        {
            "exchange": "DCE",
            "startTime": "11:31:00",
            "endTime": "13:29:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "13:30:00"
                }
            ]
        },
        {
            "exchange": "DCE",
            "startTime": "15:01:00",
            "endTime": "20:54:59",
            "businessTypes": [
                {
                    "type": "点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "反点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "套保平仓-点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "转月",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                },
                {
                    "type": "套保平仓-转月",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                },
                {
                    "type": "套利",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                }
            ]
        },
        {
            "exchange": "ZCE",
            "startTime": "00:00:00",
            "endTime": "08:54:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                }
            ]
        },
        {
            "exchange": "ZCE",
            "startTime": "23:00:00",
            "endTime": "23:59:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "09:00:00"
                }
            ]
        },
        {
            "exchange": "ZCE",
            "startTime": "10:15:00",
            "endTime": "10:29:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "10:30:00"
                }
            ]
        },
        {
            "exchange": "ZCE",
            "startTime": "11:31:00",
            "endTime": "13:29:59",
            "businessTypes": [
                {
                    "type": "全部业务",
                    "syncEnable": false,
                    "syncReleaseTime": "13:30:00"
                }
            ]
        },
        {
            "exchange": "ZCE",
            "startTime": "15:01:00",
            "endTime": "20:54:59",
            "businessTypes": [
                {
                    "type": "点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "反点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "套保平仓-点价",
                    "syncEnable": false,
                    "syncReleaseTime": "20:55:00"
                },
                {
                    "type": "转月",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                },
                {
                    "type": "套保平仓-转月",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                },
                {
                    "type": "套利",
                    "syncEnable": false,
                    "syncReleaseTime": "21:00:00"
                }
            ]
        }
    ]
}