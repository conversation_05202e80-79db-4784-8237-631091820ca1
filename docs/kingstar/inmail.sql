-- 插入dbm_template
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ('', N'期货站内信通知', N'inmail', N'${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}', N'${auditStatus!} ${priceType!} ${orientation!} ${dealDirection!} ${dominantCode!} ${applyHandNum!}手 ${applyPrice!} ${pendingType!} 编号:${applyCode!}', N'normal', NULL, GETDATE(), NULL,  GETDATE(), NULL, N'POSITION_ADD_REVOCATION_APPLY_FOR_INMAIL', NULL, NULL, 0);
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ('', N'期货站内信通知（改单）', N'inmail', N'${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}', N'改单 ${priceType!};
${priceType!} ${orientation!} ${dealDirection!} ${dominantCode!} ${applyHandNum!}手 ${applyPrice!} ${pendingType!} 编号:${applyCode!};
${ChangePendingDominantCode!} ${ChangeTranferDominantCode!} ${changeHandNum!} ${changePrice!} ${changeDiffPrice!}', N'normal', NULL,  GETDATE(), NULL,  GETDATE(), NULL, N'POSITION_UPDATE_APPLY_FOR_INMAIL', NULL, NULL, 0);

-- 插入dbm_business_template
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category],  [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency],[template_id]) SELECT N'POSITION_ADD_REVOCATION_APPLY_FOR_INMAIL', '', NULL, N'inmail', N'NOTICE', N'system', '', N'role', '', GETDATE(), GETDATE(), NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, N'单条实时发送',id FROM dbm_template WHERE template_code = 'POSITION_ADD_REVOCATION_APPLY_FOR_INMAIL';

INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category],  [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency],[template_id]) SELECT N'POSITION_UPDATE_APPLY_FOR_INMAIL', '', NULL, N'inmail', N'NOTICE', N'system', '', N'role', '', GETDATE(), GETDATE(), NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, N'单条实时发送',id FROM dbm_template WHERE template_code = 'POSITION_UPDATE_APPLY_FOR_INMAIL';