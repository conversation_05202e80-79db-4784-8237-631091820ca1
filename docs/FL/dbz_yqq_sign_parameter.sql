/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 06/07/2023 09:45:02
*/


-- ----------------------------
-- Table structure for dbz_yqq_sign_parameter
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_yqq_sign_parameter]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_yqq_sign_parameter]
GO

CREATE TABLE [dbo].[dbz_yqq_sign_parameter] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [company_id] int  NULL,
  [enterprise_name] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [app_secret_key] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [app_id] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [seal_name] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_by] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [update_by] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [context] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbz_yqq_sign_parameter] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'company_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'企业名称',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'enterprise_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'易企签应用appSecretKey',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'app_secret_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'易企签应用appId',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'app_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'印章名称',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'seal_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改人',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'update_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'环境',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_sign_parameter',
'COLUMN', N'context'
GO


-- ----------------------------
-- Records of dbz_yqq_sign_parameter
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dbz_yqq_sign_parameter] ON
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'1', N'1', N'上海甜头菜电子商务有限公司', N'skb1a457138c28175d6445c6489d96dd67', N'1816b061bdace98a4118e30ac81', N'test', N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'dev')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'2', N'2', N'上海甜头菜电子商务有限公司', N'skb1a457138c28175d6445c6489d96dd67', N'1816b061bdace98a4118e30ac81', N'test', N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'dev')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'3', N'1', N'路易达孚天津国际贸易有限公司', NULL, NULL, NULL, N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'test')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'4', N'2', N'广州富凌食品科技有限公司', NULL, NULL, NULL, N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'test')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'5', N'1', N'路易达孚天津国际贸易有限公司', N'sk01ab341ddff3c856de88ef2b57554fc9', N'18160e08f5422aac68e38ec5c51', N'uat测试用章', N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'uat')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'6', N'2', N'广州富凌食品科技有限公司', N'	
sk92b0dc84b417d6ff72888d0e0af33944', N'	
18900a793476698ff64e4eb9412', NULL, N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'uat')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'7', N'1', N'路易达孚天津国际贸易有限公司', N'ske2b9982a05cec86df9cb5139947effc4', N'1831bcd9dd726bd28c138375531', N'uat测试用章', N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'preuat')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'8', N'2', N'广州富凌食品科技有限公司', N'sk095217b046b102ccb36490a4d985ddcb', N'189009620416698ff64e4eb9412', NULL, N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'preuat')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'9', N'1', N'路易达孚天津国际贸易有限公司', N'sk4c915504ca83401932a826deff11d255', N'181f101f91a82706a72ca254e63', N'', N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'prod')
GO

INSERT INTO [dbo].[dbz_yqq_sign_parameter] ([id], [company_id], [enterprise_name], [app_secret_key], [app_id], [seal_name], [created_by], [update_by], [created_at], [updated_at], [context]) VALUES (N'10', N'2', N'广州富凌食品科技有限公司', NULL, NULL, NULL, N'1', N'1', N'2023-07-04 00:00:00.000', N'2023-07-04 00:00:00.000', N'prod')
GO

SET IDENTITY_INSERT [dbo].[dbz_yqq_sign_parameter] OFF
GO


-- ----------------------------
-- Auto increment value for dbz_yqq_sign_parameter
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbz_yqq_sign_parameter]', RESEED, 10)
GO


-- ----------------------------
-- Primary Key structure for table dbz_yqq_sign_parameter
-- ----------------------------
ALTER TABLE [dbo].[dbz_yqq_sign_parameter] ADD CONSTRAINT [PK__dbz_yqq___3213E83F4E98E744] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

