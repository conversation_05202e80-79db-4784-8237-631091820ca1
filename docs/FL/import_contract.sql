ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [invoice_payment_rate] varchar(64) COLLATE Chinese_PRC_CI_AS NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [payment_term_code] varchar(255) COLLATE Chinese_PRC_CI_AS NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票后补缴货款比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'invoice_payment_rate'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'付款条件编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'payment_term_code'


ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [invoice_payment_rate] varchar(64) COLLATE Chinese_PRC_CI_AS NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [payment_term_code] varchar(255) COLLATE Chinese_PRC_CI_AS NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票后补缴货款比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'invoice_payment_rate'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'付款条件编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'payment_term_code'

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [invoice_payment_rate] int  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [payment_term_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票后补缴货款比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'invoice_payment_rate'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'付款条件编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'payment_term_code'