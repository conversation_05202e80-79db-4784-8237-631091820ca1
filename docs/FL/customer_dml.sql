SET IDENTITY_INSERT  [dbo].[dba_customer]  ON;

INSERT INTO [dbo].[dba_customer] ([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10006, N'********', N'', 0, 0, 1, 1, 0, N'2', N'1', N'广州富凌食品科技有限公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:37.330', '2023-06-26 01:40:37.330', 1, 1, N'', N'FL', N'', 1, 2, N'', N'', 0, N'', N'', NULL, N'05', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 6);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10005, N'854951', N'', 0, 0, 1, 1, 0, N'2', N'1', N'FL广州植之元油脂实业有限公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:37.260', '2023-06-26 01:40:37.260', 1, 0, N'', N'', N'', 0, 2, N'', N'', 0, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 4);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10004, N'1218761', N'', 0, 0, 1, 1, 0, N'2', N'1', N'FL东莞路易达孚饲料蛋白有限公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:37.177', '2023-06-26 01:40:37.177', 1, 0, N'', N'', N'', 0, 2, N'', N'', 0, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 3);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10003, N'********', N'', 0, 0, 1, 1, 0, N'2', N'1', N'FL路易达孚（天津）食品科技有限责任公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:31.690', '2023-06-26 01:40:31.690', 1, 0, N'', N'', N'', 0, 2, N'', N'', 0, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 2);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10002, N'********', N'', 0, 0, 1, 1, 0, N'2', N'1', N'FL路易达孚（天津）国际贸易有限公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:31.610', '2023-06-26 01:40:31.610', 1, 0, N'', N'', N'', 0, 2, N'', N'', 0, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 5);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10001, N'********', N'', 0, 0, 1, 1, 0, N'2', N'1', N'FL路易达孚（张家港）饲料蛋白有限公司', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:27.023', '2023-06-26 01:40:27.023', 1, 0, N'', N'', N'', 0, 2, N'', N'', 0, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 1);
INSERT INTO [dbo].[dba_customer]([id], [linkage_customer_code], [mdm_customer_code], [parent_id], [use_yqq], [frame_expired], [template_expired], [original_paper], [type], [grade], [name], [contact], [largest_advance], [credit_days], [payment_type], [invoice_type], [paper_need], [phone], [address], [moving], [cnf_selling], [credit_selling], [enterprise], [lng], [lat], [ax_code], [trade_type], [status], [is_deleted], [created_at], [updated_at], [is_customer], [is_supplier], [code], [short_name], [sign_place], [is_ldc], [company_id], [enterprise_name], [supplier_code], [is_columbus], [invoice_name], [invoice_address], [tax_no], [site_id], [full_name], [active], [customer_indexes_name], [k_indexes], [customer_type], [district], [full_name_english], [fax], [postcode], [invoice_id_no], [level], [terminal_customer], [dealer], [business_customer], [the_limit], [share_enterprise_credit], [trading_limit], [default_port], [default_transport_time], [arrive_contact], [arrive_phone], [bank_name], [bank_account_no], [invoice_require], [invoice_phone], [email], [homepage], [electronic_invoice_phone], [electronic_invoice_email], [category_list], [biz_employ], [created_by], [mone], [updated_by], [created_by_name], [updated_by_name], [factory_id]) VALUES (10000, N'********', N'', 0, 1, 1, 1, 0, N'2', N'1', N'FL', N'', .000000, 0, 1, 1, N'', N'', N'', 0, 0, 0, 0, N'', N'', N'', 0, 1, 0, '2023-06-26 01:40:24.833', '2023-06-26 01:40:24.833', 1, 0, N'', N'', N'', 0, 1, N'FL', N'', 1, N'', N'', NULL, N'', N'', 1, N'', N'', 0, N'', N'', N'', N'', 0, 0, 0, 0, 0, .000000, 0, .000000, NULL, NULL, N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'', N'1', N'', N'1', NULL, NULL, 6);

SET IDENTITY_INSERT  [dbo].[dba_customer]  OFF;

DBCC CHECKIDENT ('[dbo].[dba_customer]', RESEED, 11000)
GO


INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10006, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.293', '2023-07-03 08:33:55.293', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10005, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.330', '2023-07-03 08:33:55.330', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10004, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.357', '2023-07-03 08:33:55.357', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10003, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.407', '2023-07-03 08:33:55.407', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10002, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.443', '2023-07-03 08:33:55.443', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10001, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.520', '2023-07-03 08:33:55.520', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10000, 11, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:55.547', '2023-07-03 08:33:55.547', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10006, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.560', '2023-07-03 08:33:58.560', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10005, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.613', '2023-07-03 08:33:58.613', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10004, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.627', '2023-07-03 08:33:58.627', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10003, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.640', '2023-07-03 08:33:58.640', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10002, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.650', '2023-07-03 08:33:58.650', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10001, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.700', '2023-07-03 08:33:58.700', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);
INSERT INTO [dbo].[dba_customer_detail]([customer_id], [category_id], [quality_check_rule], [quality_check_name], [quality_check_content], [frame_expired], [ldc_frame], [frame_protocol], [protocol_start_date], [protocol_end_date], [protocol_no], [delivery_delay_fine], [demurrage_fine], [created_at], [updated_at], [is_white_list], [is_reverse_price], [is_structure], [invoice_id], [invoice_name], [created_by], [updated_by], [created_by_name], [updated_by_name], [grade_score]) VALUES (10000, 12, '', '', '', 0, 1, 0, NULL, NULL, N'', 2.000000, 1.000000, '2023-07-03 08:33:58.713', '2023-07-03 08:33:58.713', 0, 0, 0, 0, N'', NULL, NULL, N'1', N'1', 0);




update dba_customer_deposit_rate SET company_id = 1 where company_id is null;

update dba_customer_credit_payment SET company_id = 1 where company_id is null;

update dba_customer_bank SET company_id = 1 where company_id is null;

update dba_customer_detail SET company_id = 1,company_name = 'TJIB' where company_id is null;

update dba_customer set factory_id = 1,company_id = 1 where id = 5;
update dba_customer set factory_id = 2,company_id = 1 where id = 176;
update dba_customer set factory_id = 3,company_id = 1 where id = 3218;
update dba_customer set factory_id = 4,company_id = 1 where id = 4587;
update dba_customer set factory_id = 5,company_id = 1 where id = 6;