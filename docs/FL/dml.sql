
-- dbt_trade_ticket
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [company_id] int  NULL
    GO

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [company_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'company_name'
    GO
UPDATE dbt_trade_ticket SET company_id = 1 ,company_name = 'TJIB';

--dbt_contract
ALTER TABLE [dbo].[dbt_contract] ADD [company_id] int  NULL
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [company_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'company_name'
    GO

UPDATE dbt_contract SET company_id = 1 ,company_name = 'TJIB';

--dbt_contract_sign
ALTER TABLE [dbo].[dbt_contract_sign] ADD [company_id] int  NULL
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [company_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'company_name'
    GO

UPDATE dbt_contract_sign SET company_id = 1 ,company_name = 'TJIB';

--dba_role_def
ALTER TABLE [dbo].[dba_role_def] ADD [is_base_factory] int NULL
GO

update dba_role_def set is_base_factory = is_base_company
GO

update dba_role_def set is_base_company = 0;

update dba_role set company_id=1 where (factory_id!=0 and category_id!=0 and company_id=0);

--dba_employ_role
ALTER TABLE [dbo].[dba_employ_role] ADD [company_id] int NULL
GO

UPDATE dba_employ_role SET company_id = b.company_id FROM dba_employ_role a LEFT JOIN dba_role b ON a.role_id = b.id
GO


--dba_customer_deposit_rate保证金比例
ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [company_id] nvarchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体Id',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'company_id';
GO

--dba_customer_credit_payment赊销预付
ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [company_id] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_credit_payment',
'COLUMN', N'company_id'
GO

--dba_customer_bank客户账户信息
ALTER TABLE [dbo].[dba_customer_bank] ADD [company_id] nvarchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_bank',
'COLUMN', N'company_id'
GO

--dba_customer
ALTER TABLE [dbo].[dba_customer] ADD [factory_id] int  NULL;
GO

--dbf_price_allocate
ALTER TABLE [dbo].[dbf_price_allocate] ADD [company_id] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_allocate',
'COLUMN', N'company_id'
GO
-----dbf_price_apply
ALTER TABLE [dbo].[dbf_price_apply] ADD [company_id] int DEFAULT 0 NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_apply',
'COLUMN', N'company_id'
GO

--主体id
ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [company_id] int DEFAULT 0 NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'company_id'
GO
--dbf_position
ALTER TABLE [dbo].[dbf_position] ADD [company_id] int NULL
GO

update dbf_position set company_id =1 where company_id is null
    GO

--dbz_system_rule
SET IDENTITY_INSERT [dbo].[dbz_system_rule] ON;
INSERT INTO [dbo].[dbz_system_rule]([id], [category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (100, 11, 0, 'S0011', 'FL合同审批阈值配置', 0, 1, '2022-11-02 09:37:24.090', '2022-11-02 09:37:24.090');
INSERT INTO [dbo].[dbz_system_rule]([id], [category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (101, 12, 0, 'S0011', 'FL合同审批阈值配置', 0, 1, '2022-11-02 09:37:24.090', '2022-11-02 09:37:24.090');
SET IDENTITY_INSERT [dbo].[dbz_system_rule] OFF ;


INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (100, '豆粕总金额下限阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2023-06-30 10:27:13.337', NULL, 1, 1, N'MIN_AMOUNT', '25000000', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (100, '豆粕总金额上限阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'MAX_AMOUNT', '50000000', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (100, '豆粕交期阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'DELIVERY_DUE_MONTH', '12', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (100, '豆粕可提量阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'REMAIN_CONTRACT_NUMBER', '32', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (101, '豆油总金额下限阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'MIN_AMOUNT', '26000000', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (101, '豆油总金额上限阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'MAX_AMOUNT', '51000000', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (101, '豆油交期阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'DELIVERY_DUE_MONTH', '6', NULL, N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES (101, '豆油可提量阈值', 2, 0, 0, 1, '2022-12-23 03:36:27.037', '2022-12-23 03:36:27.037', NULL, 1, 1, N'REMAIN_CONTRACT_NUMBER', '20', NULL, N'admin1', N'admin1');
update dbz_system_rule_item set memo = '2' where rule_id in(100,101);
update dbz_system_rule_item set memo = '1' where rule_id in(96,97);

--ACT_HI_PROCINST
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [COMPANY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [COMPANY_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [FACTORY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [FACTORY_CODE_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [SUPPLIER_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [SUPPLIER_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_PROCINST] ADD [CUSTOMER_ID_] int NULL
GO

--ACT_HI_ACTINST
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [COMPANY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [COMPANY_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [FACTORY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [FACTORY_CODE_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [SUPPLIER_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [SUPPLIER_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_ACTINST] ADD [CUSTOMER_ID_] int NULL
GO

--ACT_HI_TASKINST
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [COMPANY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [COMPANY_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [FACTORY_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [FACTORY_CODE_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [SUPPLIER_ID_] int NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [SUPPLIER_NAME_] nvarchar(255) NULL
GO
ALTER TABLE [dbo].[ACT_HI_TASKINST] ADD [CUSTOMER_ID_] int NULL
GO



ALTER TABLE [dbo].[dbf_price_apply] ADD [company_name] nvarchar(255) NULL
GO
EXEC sp_addextendedproperty
'MS_Description', N'主体名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_apply',
'COLUMN', N'company_name'
GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [company_name] nvarchar(255) NULL
GO


ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [company_name] nvarchar(255) NULL
GO


ALTER TABLE [dbo].[dbt_contract_sign] ADD [voluntarily_sign_type] int NULL
GO


ALTER TABLE [dbo].[dba_customer_detail] ADD [grade_score] int NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ALTER COLUMN [sign_error_message] nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL
GO

ALTER TABLE [dbo].[dba_company] ADD [short_name] nvarchar(255) NULL
GO


TRUNCATE TABLE [dbo].[dba_company];
INSERT INTO [dbo].[dba_company]([parent_id], [name], [address], [created_by], [updated_by], [status], [created_at], [updated_at], [is_deleted], [is_customer], [customer_id], [short_name]) VALUES (0, N'路易达孚（天津）国际贸易有限公司', N'天津', N'1', N'1', 1, '2023-06-14 18:28:55.000', '2023-06-14 18:28:55.000', 0, 0, 0, 'TJIB');
INSERT INTO [dbo].[dba_company]( [parent_id], [name], [address], [created_by], [updated_by], [status], [created_at], [updated_at], [is_deleted], [is_customer], [customer_id], [short_name]) VALUES (0, N'广州富凌食品科技有限公司', NULL, N'1', N'1', 1, '2023-06-14 01:46:27.670', '2023-06-14 01:46:27.670', 0, 0, 0, 'FL');



TRUNCATE TABLE [dbo].[dba_factory_company];
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (1, 1, 1, 0, '2023-06-28 17:38:18.133', '2023-06-28 17:38:18.133', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (6, 2, 1, 0, '2023-06-28 17:51:58.980', '2023-06-28 17:51:58.980', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (6, 1, 1, 0, '2023-06-28 17:51:58.990', '2023-06-28 17:51:58.990', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (3, 1, 1, 0, '2023-07-03 16:19:58.303', '2023-07-03 16:19:58.303', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (2, 1, 1, 0, '2023-07-03 16:20:07.160', '2023-07-03 16:20:07.160', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (4, 1, 1, 0, '2023-07-03 16:22:22.337', '2023-07-03 16:22:22.337', '1', '1');
INSERT INTO [dbo].[dba_factory_company]([factory_id], [company_id], [status], [is_deleted], [created_at], [updated_at], [created_by], [updated_by]) VALUES (5, 1, 1, 0, '2023-07-03 16:22:26.070', '2023-07-03 16:22:26.070', '1', '1');


update dbm_template set title = '【路易达孚电子商务平台】密码重置' ,content ='您在路易达孚电子商务平台 Columbus系统的密码已重置为${password!}，请重新登陆系统并修改密码，谢谢'  where name = 'Columbus用户重置密码';


update dbf_price_apply SET company_id = 1,company_name = '路易达孚（天津）国际贸易有限公司' where company_id is null;

update dbf_price_allocate SET company_id = 1,company_name = '路易达孚（天津）国际贸易有限公司' where company_id is null;

update dbf_price_deal_detail SET company_id = 1,company_name = '路易达孚（天津）国际贸易有限公司' where company_id is null;


update dbt_contract SET company_id = 1,company_name = '路易达孚（天津）国际贸易有限公司' where company_id is null;


GO
ALTER TABLE [dbo].[dba_customer_detail] ADD [company_id] int NULL
GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [company_name] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dba_customer_detail_update_record] ADD [company_id] int NULL
GO

update dba_customer_credit_payment SET company_id = 1 where company_id is null;

update dba_customer_deposit_rate SET company_id = 1 where company_id is null;

update dba_customer_bank SET company_id = 1 where company_id is null;

update dba_customer_detail_update_record SET company_id = 1 where company_id is null;

UPDATE dba_customer_detail
SET is_structure = 1,
    is_white_list = 1,
    is_reverse_price = 1
WHERE
        category_id = 11
  AND customer_id IN ( SELECT id FROM dba_customer WHERE linkage_customer_code IN ( '1409713', '1166645', '1364029' ) );


INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'completeSignVoluntarilySign', N'T_CONTRACT_COMMON_OP', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '(静默签)协议LDC签署完成');


ALTER TABLE [dbo].[dbt_contract_structure] ADD [company_id] int DEFAULT 1 NULL
GO

update [dbo].[dbt_contract_structure] set company_id = 1;
