/*
 Navicat Premium Data Transfer

 Source Server         : ldc_navigator_test-外网
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : *************:65431
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 12/10/2024 17:20:40
*/


-- ----------------------------
-- Table structure for dba_sequence
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_sequence]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_sequence]
GO

CREATE TABLE [dbo].[dba_sequence] (
  [id] int IDENTITY(1,1) NOT NULL,
  [redis_key] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [redis_value] int  NULL,
  [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [is_deleted] int DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[dba_sequence] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序列号Key',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'redis_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序列号值',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'redis_value'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'序列号',
'SCHEMA', N'dbo',
'TABLE', N'dba_sequence'
GO


-- ----------------------------
-- Primary Key structure for table dba_sequence
-- ----------------------------
ALTER TABLE [dbo].[dba_sequence] ADD CONSTRAINT [PK__dba_sequ__3213E83F69710E33] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

