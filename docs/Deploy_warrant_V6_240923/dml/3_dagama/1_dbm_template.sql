INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather])
VALUES (N'', N'客户锁定', N'inmail', N'提货委托-锁定状态提醒', N'您的提货委托${applyCode!}因${atlasSubStatus!}已被锁定，请注意查收。', N'normal', NULL, GETDATE(), NULL, GETDATE(), NULL, N'COLUMBUS_DELIVERY_APPLY_BLOCKED', NULL, NULL, 0);

INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency])
select  N'COLUMBUS_DELIVERY_APPLY_BLOCKED', N'', N'', N'inmail', N'NOTICE', id, N'system', N'', N'customer_role', N'', GETDATE(), GETDATE(), NULL, NULL, 1, 2, 1, 0, NULL, NULL, 1, '单条实时发送'
FROM [dbo].[dbm_template] where template_code = 'COLUMBUS_DELIVERY_APPLY_BLOCKED';