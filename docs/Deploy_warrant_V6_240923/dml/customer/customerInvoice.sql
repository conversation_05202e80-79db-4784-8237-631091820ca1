update dba_customer_invoice set invoice_type = item.rule_key
    from dba_customer_invoice invoice
INNER JOIN dbz_system_rule_item item ON invoice.invoice_id = item.id;


--发票
update dba_customer_invoice set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--白名单
update dba_customer_detail set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--银行
update dba_customer_bank set category1 = ','+category1+',',category2 = ','+category2+',';
--联系人
update dba_contact set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--提货白名单
update dba_customer_delivery_white set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--保证金比例
update dba_customer_deposit_rate set category1 = ','+category1+',',category2 = ','+category2+',';
--客户评级表
update dba_customer_grade_score set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--客户框架协议
update dba_customer_protocol set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--客户正本
update dba_customer_original_paper set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';
--赊销&预付
update dba_customer_credit_payment set category1 = ','+category1+',',category2 = ','+category2+',',category3 = ','+category3+',';