ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [invoice_payment_rate] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'invoice_payment_rate'


ALTER TABLE [dbo].[dbt_tt_add] ADD [invoice_payment_rate] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_add',
'COLUMN', N'invoice_payment_rate'

ALTER TABLE [dbo].[dbt_tt_modify] ADD [invoice_payment_rate] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_modify',
'COLUMN', N'invoice_payment_rate'

ALTER TABLE [dbo].[dbt_contract] ADD [invoice_payment_rate] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract',
'COLUMN', N'invoice_payment_rate'

ALTER TABLE [dbo].[dbt_contract_history] ADD [invoice_payment_rate] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_history',
'COLUMN', N'invoice_payment_rate'