ALTER TABLE [dbo].[dba_pay_condition] ADD [mdm_pay_condition_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

ALTER TABLE [dbo].[dba_pay_condition] ADD [deposit_rate] int NULL
GO

ALTER TABLE [dbo].[dba_pay_condition] ADD [added_deposit_rate] int NULL
GO

ALTER TABLE [dbo].[dba_pay_condition] ADD [invoice_payment_rate] int NULL
GO

ALTER TABLE [dbo].[dba_pay_condition] ADD [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'ATLAS条件代码
',
'SCHEMA', N'dbo',
'TABLE', N'dba_pay_condition',
'COLUMN', N'mdm_pay_condition_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'履约保证金比例
',
'SCHEMA', N'dbo',
'TABLE', N'dba_pay_condition',
'COLUMN', N'deposit_rate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'履约保证金比例后补缴
',
'SCHEMA', N'dbo',
'TABLE', N'dba_pay_condition',
'COLUMN', N'added_deposit_rate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票后补缴货款比例
',
'SCHEMA', N'dbo',
'TABLE', N'dba_pay_condition',
'COLUMN', N'invoice_payment_rate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注
',
'SCHEMA', N'dbo',
'TABLE', N'dba_pay_condition',
'COLUMN', N'remark'

ALTER TABLE [dbo].[dba_pay_condition] ALTER COLUMN [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL
GO

ALTER TABLE [dbo].[dba_pay_condition] ALTER COLUMN [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL

ALTER TABLE [dbo].[dba_pay_condition] ADD [is_deleted] int NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'逻辑删除  0:启用 1:禁用',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_pay_condition',
    'COLUMN', N'is_deleted'