IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_yqq_task_record_proc]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_yqq_task_record_proc]
GO

CREATE TABLE [dbo].[dbz_yqq_task_record_proc] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [task_record_id] int DEFAULT ((0)) NULL,
  [dispose_node] int DEFAULT ((1)) NULL,
  [data] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbz_yqq_task_record_proc] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'task_record表Id',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record_proc',
'COLUMN', N'task_record_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record_proc',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record_proc',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbz_yqq_task_record_proc
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbz_yqq_task_record_proc]', RESEED, 4)
GO


-- ----------------------------
-- Primary Key structure for table dbz_yqq_task_record_proc
-- ----------------------------
ALTER TABLE [dbo].[dbz_yqq_task_record_proc] ADD CONSTRAINT [PK__dbz_yqq___3213E83FA6754D5D] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO