IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_yqq_task_record]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_yqq_task_record]
GO

CREATE TABLE [dbo].[dbz_yqq_task_record] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [biz_id] int DEFAULT ((0)) NULL,
  [biz_uu_code] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [invoke_no] nvarchar(128) COLLATE Chinese_PRC_CI_AS  NULL,
  [type] int DEFAULT ((0)) NULL,
  [data] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [webhook_event_type] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [dispose_status] int DEFAULT ((1)) NULL,
  [dispose_node] int DEFAULT ((1)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [sign_file_type] int DEFAULT ((1002)) NULL
)
GO

ALTER TABLE [dbo].[dbz_yqq_task_record] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'易企签回调回调唯一编码invokeNo',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'invoke_no'
GO

EXEC sp_addextendedproperty
'MS_Description', N'数据类型 1请求 2:接收',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'json参数',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'回调类型 对应易企签(WebhookEventType)类型',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'webhook_event_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'处理状态 1:未处理 2:处理中 3:处理完成',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'dispose_status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'当前处理到的节点 接收数据并保存.....邮件发送成功',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'dispose_node'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'协议文件类型',
'SCHEMA', N'dbo',
'TABLE', N'dbz_yqq_task_record',
'COLUMN', N'sign_file_type'
GO


-- ----------------------------
-- Auto increment value for dbz_yqq_task_record
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbz_yqq_task_record]', RESEED, 11)
GO


-- ----------------------------
-- Primary Key structure for table dbz_yqq_task_record
-- ----------------------------
ALTER TABLE [dbo].[dbz_yqq_task_record] ADD CONSTRAINT [PK__dbz_yqq___3213E83F0C17944B] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO