/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 23/09/2024 16:52:40
*/


-- ----------------------------
-- Table structure for dba_customer_delivery_white
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_delivery_white]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_customer_delivery_white]
GO

CREATE TABLE [dbo].[dba_customer_delivery_white] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [customer_id] int  NULL,
  [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category3] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
  [status] int  NULL,
  [created_at] datetime  NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_at] datetime  NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dba_customer_delivery_white] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户id',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'三级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_delivery_white',
'COLUMN', N'updated_by'
GO


-- ----------------------------
-- Auto increment value for dba_customer_delivery_white
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_customer_delivery_white]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dba_customer_delivery_white
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_delivery_white] ADD CONSTRAINT [PK__dba_cust__3213E83FE30C4EFF] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

