/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 25/09/2024 17:31:08
*/


-- ----------------------------
-- Table structure for dba_customer_mdm_counterparty_record
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_mdm_counterparty_record]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_customer_mdm_counterparty_record]
GO

CREATE TABLE [dbo].[dba_customer_mdm_counterparty_record] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [counterparty_id] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [request_data] text COLLATE Chinese_PRC_CI_AS  NULL,
  [address_list] text COLLATE Chinese_PRC_CI_AS  NULL,
  [bank_details_list] text COLLATE Chinese_PRC_CI_AS  NULL,
  [category_list] text COLLATE Chinese_PRC_CI_AS  NULL,
  [before_data] text COLLATE Chinese_PRC_CI_AS  NULL,
  [after_data] text COLLATE Chinese_PRC_CI_AS  NULL,
  [content] text COLLATE Chinese_PRC_CI_AS  NULL,
  [response_data] text COLLATE Chinese_PRC_CI_AS  NULL,
  [request_time] datetime  NULL,
  [operation_type] int  NULL
)
GO

ALTER TABLE [dbo].[dba_customer_mdm_counterparty_record] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户编码 ',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'counterparty_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求参数',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'request_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改前客户主数据',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'before_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改后客户主数据',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'after_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变化的字段',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'相应参数',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'response_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'request_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0:update;1:insert',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_mdm_counterparty_record',
'COLUMN', N'operation_type'
GO


-- ----------------------------
-- Auto increment value for dba_customer_mdm_counterparty_record
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_customer_mdm_counterparty_record]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dba_customer_mdm_counterparty_record
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_mdm_counterparty_record] ADD CONSTRAINT [PK__dba_cust__3213E83FFD13242A] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

