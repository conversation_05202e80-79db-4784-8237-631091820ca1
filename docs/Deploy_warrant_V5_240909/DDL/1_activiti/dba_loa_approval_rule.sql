/*
 Navicat Premium Data Transfer

 Source Server         : int
 Source Server Type    : SQL Server
 Source Server Version : 12005688 (12.00.5688)
 Source Host           : csm4invgsqs001.privatelink.database.chinacloudapi.cn:1433
 Source Catalog        : CSM4INVGSQL001
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 12005688 (12.00.5688)
 File Encoding         : 65001

 Date: 19/11/2024 09:45:26
*/


-- ----------------------------
-- Table structure for dba_loa_approval_rule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_loa_approval_rule]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_loa_approval_rule]
GO

CREATE TABLE [dbo].[dba_loa_approval_rule] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [category2] int  NULL,
  [sales_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [bu_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [tt_type] int  NULL,
  [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [status] int  NULL,
  [is_deleted] int  NULL
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务线',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'bu_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'tt_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则类容',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'rule_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1:启用 2:禁用',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0:未删除 1:已删除',
'SCHEMA', N'dbo',
'TABLE', N'dba_loa_approval_rule',
'COLUMN', N'is_deleted'
GO


-- ----------------------------
-- Records of dba_loa_approval_rule
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_loa_approval_rule] ON
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'1', N'11', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'2', N'11', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'3', N'11', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'4', N'11', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println(" AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'5', N'11', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule A10
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println(" A9");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'6', N'11', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'7', N'12', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false && salesType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'8', N'12', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'9', N'12', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'10', N'12', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false && salesType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'11', N'12', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false && salesType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule A10
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println(" A9");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'12', N'12', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'13', N'11', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end


rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'14', N'12', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false && salesType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'15', N'27', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(transportPrice !=0)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(businessPrice !=0)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(otherPrice !=0)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'16', N'27', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_BUYBACK
		when
        $approveRule:ContractApproveBizInfoDTO(ttType == 8)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'17', N'27', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'18', N'27', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(transportPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end


rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(businessPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(otherPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(customerGroupChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryEndTimeChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'19', N'27', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(transportPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end


rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(businessPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(otherPriceChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(customerGroupChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryEndTimeChanged ==true && salesType == 1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'20', N'27', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(ttType == 9)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'21', N'22', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'22', N'22', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println(" ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'23', N'22', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'24', N'22', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'25', N'22', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println(" ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule A10
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println(" A9");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'26', N'22', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'27', N'22', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'28', N'27', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'29', N'25', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'30', N'25', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'31', N'25', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule  A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println(" A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'32', N'25', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule  A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println(" A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'33', N'25', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule  A10
//    no-loop true
//    lock-on-active true
//    salience -1
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println(" A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println(" A9");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'34', N'25', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'35', N'25', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'43', N'28', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(category2 == 28)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("豆二发起审批;"));
        System.out.println("A1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'44', N'28', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(category2 == 28)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("豆二发起审批;"));
        System.out.println("A1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong == true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#且交期大于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#且交期小于#DELIVERY_DUE_MONTH#;"));
        System.out.println("AB");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'45', N'28', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'46', N'28', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println(" ABC2");
        update($approveRule);
end

rule  AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println(" AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'47', N'28', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule A10
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println(" A9");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'48', N'28', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'49', N'28', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(category2 == 28)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("豆二发起审批;"));
        System.out.println("A1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'50', N'26', N'1,2', N'ST', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'51', N'26', N'1,2', N'ST', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'52', N'26', N'1,2', N'ST', N'11', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A
    when
        $approveRule:ContractApproveBizInfoDTO(remainMuch ==true)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("尾量大于等于#REMAIN_CONTRACT_NUMBER#;"));
        System.out.println("A");
        update($approveRule);
end


', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'53', N'26', N'1,2', N'ST', N'2', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule  A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println(" A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'54', N'26', N'1,2', N'ST', N'3', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("A2");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("A6");
        update($approveRule);
end
/*
rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("A7");
        update($approveRule);
end
*/
rule A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A8");
        update($approveRule);
end

rule A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("A9");
        update($approveRule);
end


rule A10
    when
        $approveRule:ContractApproveBizInfoDTO(addedSignatureType==1)
    then
        $approveRule.setRuleResult(1011);
        $approveRule.setRuleMemo(new StringBuilder().append("原合同审批免签;"));
        System.out.println("A10");
        update($approveRule);
end
/*
rule  A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        System.out.println(" A7");
        update($approveRule);
end

rule  A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println(" A8");
        update($approveRule);
end

rule  A10
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println(" A10");
        update($approveRule);
end

rule  A11
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println(" A11");
        update($approveRule);
end

rule  A12
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println(" A12");
        update($approveRule);
end*/
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'55', N'26', N'1,2', N'ST', N'9', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println(" ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'56', N'26', N'1,2', N'WT', N'1', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'65', N'28', N'1,2', N'WT', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(category2 == 28)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("豆二发起审批;"));
        System.out.println("A1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong == true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#且交期大于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#且交期小于#DELIVERY_DUE_MONTH#;"));
        System.out.println("AB");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'66', N'11', N'1,2', N'WT', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'67', N'12', N'1,2', N'WT', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'69', N'25', N'1,2', N'WT', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'70', N'26', N'1,2', N'WT', N'8', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("ABC1");
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule SBM_A_BUYBACK
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("回购业务;"));
        System.out.println("SBM_A_BUYBACK");
        update($approveRule);
end
', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'71', N'26', N'1,2', N'WT', N'16', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'72', N'28', N'1,2', N'WT', N'16', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(category2 == 28)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("豆二发起审批;"));
        System.out.println("A1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong == true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#且交期大于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#且交期小于#DELIVERY_DUE_MONTH#;"));
        System.out.println("AB");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'73', N'25', N'1,2', N'WT', N'16', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'74', N'12', N'1,2', N'WT', N'16', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false && salesType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("A3");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_loa_approval_rule] ([id], [category2], [sales_type], [bu_code], [tt_type], [rule_info], [created_at], [status], [is_deleted]) VALUES (N'75', N'11', N'1,2', N'WT', N'16', N'package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("ABC2");
        update($approveRule);
end

rule ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("ABC1");
        update($approveRule);
end

rule AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("AB");
        update($approveRule);
end

rule A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("A1");
        update($approveRule);
end

rule A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("A2");
        update($approveRule);
end

rule A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("A4");
        update($approveRule);
end

rule A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("A5");
        update($approveRule);
end

rule A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("A6");
        update($approveRule);
end

rule A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("A7");
        update($approveRule);
end

', N'2024-08-27 14:54:08.000', N'1', N'0')
GO

SET IDENTITY_INSERT [dbo].[dba_loa_approval_rule] OFF
GO


-- ----------------------------
-- Auto increment value for dba_loa_approval_rule
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_loa_approval_rule]', RESEED, 75)
GO


-- ----------------------------
-- Primary Key structure for table dba_loa_approval_rule
-- ----------------------------
ALTER TABLE [dbo].[dba_loa_approval_rule] ADD CONSTRAINT [PK__dba_loa___3213E83F9ED6061B] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

