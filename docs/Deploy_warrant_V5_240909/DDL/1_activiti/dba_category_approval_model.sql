/*
 Navicat Premium Data Transfer

 Source Server         : test
 Source Server Type    : SQL Server
 Source Server Version : 12005688 (12.00.5688)
 Source Host           : csm4dnvgsqs001.database.chinacloudapi.cn:1433
 Source Catalog        : CSM4DNVGSQL001
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 12005688 (12.00.5688)
 File Encoding         : 65001

 Date: 19/11/2024 09:21:52
*/


-- ----------------------------
-- Table structure for dba_category_approval_model
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_category_approval_model]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_category_approval_model]
GO

CREATE TABLE [dbo].[dba_category_approval_model] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [model_key] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [model_id] int  NULL,
  [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [status] int  NULL,
  [is_deleted] int  NULL
)
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程图编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'model_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程图id',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'model_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1:启用 2:禁用',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0:未删除 1:已删除',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'is_deleted'
GO


-- ----------------------------
-- Records of dba_category_approval_model
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_category_approval_model] ON
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'1', N'PROC_SBM_APPROVING', N'16067503', N'11', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'2', N'PROC_SOB_APPROVING', N'16067501', N'12', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'3', N'PROC_BP_APPROVING', N'15977501', N'27', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'4', N'PROC_BCO_APPROVING', N'15977504', N'22', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'5', N'PROC_SO_APPROVING', N'15977507', N'25', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'6', N'PROC_SP_DEF_APPROVE', N'15977510', N'26', N'2024-09-09 10:18:33.000', N'0', N'1')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'7', N'PROC_SBNB_APPROVING', N'15977513', N'28', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at], [status], [is_deleted]) VALUES (N'8', N'PROC_SP_APPROVING', N'15977516', N'26', N'2024-09-09 10:18:33.000', N'1', N'0')
GO

SET IDENTITY_INSERT [dbo].[dba_category_approval_model] OFF
GO


-- ----------------------------
-- Auto increment value for dba_category_approval_model
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_category_approval_model]', RESEED, 8)
GO


-- ----------------------------
-- Primary Key structure for table dba_category_approval_model
-- ----------------------------
ALTER TABLE [dbo].[dba_category_approval_model] ADD CONSTRAINT [PK__dba_cate__3213E83F116C6DF8] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

