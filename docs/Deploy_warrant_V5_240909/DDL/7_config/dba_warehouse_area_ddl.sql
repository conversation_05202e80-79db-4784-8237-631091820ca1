/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 27/09/2024 18:00:55
*/


-- ----------------------------
-- Table structure for dba_warehouse_area
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_warehouse_area]') AND type IN ('U'))
DROP TABLE [dbo].[dba_warehouse_area]
    GO

CREATE TABLE [dbo].[dba_warehouse_area] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [market_zone] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_deleted] int  NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL
    )
    GO

ALTER TABLE [dbo].[dba_warehouse_area] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主键ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'地理区域名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'地理区域编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'market_zone'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0.未删除 1.已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse_area',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dba_warehouse_area
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_warehouse_area]', RESEED, 1)
    GO

