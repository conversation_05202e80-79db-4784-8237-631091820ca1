ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] ADD [transport_way] int  NULL
GO

ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] ADD [freeboard] int  NULL
GO

ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] ADD [mmsi] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'运输方式',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'transport_way'
GO

EXEC sp_addextendedproperty
'MS_Description', N'核定干舷',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'freeboard'
GO

EXEC sp_addextendedproperty
'MS_Description', N'MMSI',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'mmsi'