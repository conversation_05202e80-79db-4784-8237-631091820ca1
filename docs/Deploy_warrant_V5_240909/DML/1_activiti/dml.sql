update ACT_HI_PROCINST
set CATEGORY_1_ = t.category1,
    CATEGORY_2_ = t.category2,
    CATEGORY_3_ = t.category3,
    SITE_CODE_ = t.site_code,
    SITE_NAME_ = t.site_name,
    BU_CODE_ = t.bu_code,
    FUTURE_CODE_ = t.future_code
    from ACT_HI_PROCINST a
JOIN dbt_trade_ticket t ON a.BUSINESS_KEY_ = t.code;

update ACT_HI_PROCINST
set CATEGORY_1_ = t.category1,
    CATEGORY_2_ = t.category2,
    CATEGORY_3_ = t.category3,
    SITE_CODE_ = t.site_code,
    SITE_NAME_ = t.site_name,
    BU_CODE_ = t.bu_code,
    FUTURE_CODE_ = t.future_code
    from ACT_HI_PROCINST a
JOIN dbt_contract t ON a.REFER_BIZ_CODE_ = t.contract_code
where TRADE_TYPE_VALUE_ = 201;

update ACT_HI_TASKINST
set CATEGORY_1_ = p.CATEGORY_1_,
    CATEGORY_2_ = p.CATEGORY_2_,
    CATEGORY_3_ = p.CATEGORY_3_,
    SITE_CODE_ = p.SITE_CODE_,
    SITE_NAME_ = p.SITE_NAME_,
    BU_CODE_ = p.BU_CODE_,
    FUTURE_CODE_ = p.FUTURE_CODE_
    from ACT_HI_TASKINST a
JOIN ACT_HI_PROCINST p ON p.ID_ = a.PROC_INST_ID_;


update ACT_HI_ACTINST
set CATEGORY_1_ = p.CATEGORY_1_,
    CATEGORY_2_ = p.CATEGORY_2_,
    CATEGORY_3_ = p.CATEGORY_3_,
    SITE_CODE_ = p.SITE_CODE_,
    SITE_NAME_ = p.SITE_NAME_,
    BU_CODE_ = p.BU_CODE_,
    FUTURE_CODE_ = p.FUTURE_CODE_
    from ACT_HI_ACTINST a
JOIN ACT_HI_PROCINST p ON p.ID_ = a.PROC_INST_ID_;
