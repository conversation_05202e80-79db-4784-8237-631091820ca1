ALTER TABLE [dbo].[dbt_delivery_type] ADD [created_by] int NULL

ALTER TABLE [dbo].[dbt_delivery_type] ADD [updated_by] int NULL


CREATE TABLE [dbo].[dba_business_detail_update_record] (
  [id] int IDENTITY(1,1) NOT NULL,
  [business_id] int NULL,
  [detail_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [category_id] int NULL,
  [raw_data] text COLLATE Chinese_PRC_CI_AS NULL,
  [data] text COLLATE Chinese_PRC_CI_AS NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [created_at] datetime NULL,
  CONSTRAINT [PK__dba_cust__3213E83F743E563D] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
)
ON [PRIMARY]
TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[dba_business_detail_update_record] SET (LOCK_ESCALATION = TABLE)



CREATE TABLE [dbo].[dba_customer_detail_update_record] (
  [id] int IDENTITY(1,1) NOT NULL,
  [customer_id] int NULL,
  [detail_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [category_id] int NULL,
  [raw_data] text COLLATE Chinese_PRC_CI_AS NULL,
  [data] text COLLATE Chinese_PRC_CI_AS NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [created_at] datetime NULL
)
ON [PRIMARY]
TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[dba_customer_detail_update_record] SET (LOCK_ESCALATION = TABLE)