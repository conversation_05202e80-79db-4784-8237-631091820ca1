ALTER TABLE [dbo].[dbt_tt_price] ADD [contrary_status] int DEFAULT 0 NULL
GO
ALTER TABLE [dbo].[dbt_tt_tranfer] ADD [contrary_status] int DEFAULT 0 NULL
GO
ALTER TABLE [dbo].[dbf_price_apply] ADD [sales_type] int DEFAULT 2 NULL;
GO
ALTER TABLE [dbo].[dbf_price_allocate] ADD [sales_type] int DEFAULT 2 NULL;
GO
ALTER TABLE [dbo].[dbt_tt_tranfer] ADD [price_apply_id] int DEFAULT 0 NULL;
GO
ALTER TABLE [dbo].[dbf_price_apply] ADD [deal_contrary_num] int DEFAULT 0 NULL;
GO
ALTER TABLE [dbo].[dbt_tt_tranfer] ADD [price_allocate_id] int DEFAULT 0 NULL;
GO
ALTER TABLE [dbo].[dbt_contract_sign] ADD [cancel_reason] nvarchar(255) DEFAULT '' NULL;
GO

update dbt_tt_tranfer set price_apply_id = 0;

update dbt_tt_tranfer set price_allocate_id = 0;

update dbt_tt_price set contrary_status = 1;

update dbt_tt_tranfer set contrary_status = 1;

update dbf_price_apply set deal_contrary_num = 0 where deal_contrary_num is null;

update dbf_price_apply set sales_type = 2 where sales_type is null;

update dbf_price_allocate set sales_type = 2 where sales_type is null;

INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (503, N'SBM_S_097', N'SBM_S_097', N'豆粕销售撤回', NULL, 1, 2, 1, 0, '2023-10-08 04:59:50.117', '2023-10-08 04:59:50.117');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (504, N'SBM_P_047', N'SBM_P_047', N'豆粕采购撤回', NULL, 2, 2, 1, 0, '2023-10-08 04:59:55.383', '2023-10-08 04:59:55.383');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (505, N'SBO_S_097', N'SBO_S_097', N'都有销售撤回', NULL, 3, 2, 1, 0, '2023-10-08 04:59:58.697', '2023-10-08 04:59:58.697');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (506, N'SBO_P_047', N'SBO_P_047', N'豆油采购撤回', NULL, 4, 2, 1, 0, '2023-10-08 05:00:02.180', '2023-10-08 05:00:02.180');