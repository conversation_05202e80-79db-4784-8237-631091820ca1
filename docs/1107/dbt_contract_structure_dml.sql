/*
 Navicat Premium Data Transfer

 Source Server         : navigator-admin-dev
 Source Server Type    : SQL Server
 Source Server Version : 14003411
 Source Host           : *************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003411
 File Encoding         : 65001

 Date: 14/11/2022 19:54:35
*/


-- ----------------------------
-- Table structure for dbt_contract_structure
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbt_contract_structure]') AND type IN ('U'))
	DROP TABLE [dbo].[dbt_contract_structure]
GO

CREATE TABLE [dbo].[dbt_contract_structure] (
  [id] int IDENTITY(1,1) NOT NULL,
  [contract_id] int DEFAULT ((0)) NULL,
  [contract_code] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [tt_id] int DEFAULT ((0)) NULL,
  [tt_code] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [price_apply_id] int NULL,
  [structure_type] int DEFAULT ((0)) NULL,
  [domain_code] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [total_num] decimal(18,6) DEFAULT ((0)) NULL,
  [unit_num] decimal(18,6) DEFAULT ((0)) NULL,
  [start_time] datetime NULL,
  [end_time] datetime NULL,
  [total_day] int NULL,
  [deal_num] decimal(18,6) DEFAULT ((0)) NULL,
  [not_deal_num] decimal(18,6) NULL,
  [assigned_num] decimal(18,6) NULL,
  [price_status] int DEFAULT ((-1)) NULL,
  [created_by] int DEFAULT ((0)) NULL,
  [updated_by] int DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [goods_category_id] int DEFAULT ((0)) NULL,
  [customer_id] int DEFAULT ((0)) NULL,
  [customer_name] nvarchar(32) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [unit_increment] decimal(18,6) NULL,
  [cash_return] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [cumulative_price] varchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
  [trigger_price] varchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
  [cumulative_release] decimal(18,6) NULL,
  [structure_unit_num] decimal(18,6) NULL,
  [sign_date] datetime NULL,
  [supplier_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dbt_contract_structure] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TT ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'tt_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TT编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'tt_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'点价申请ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'price_apply_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构类型，1:A结构 2:B结构 3:C结构',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'structure_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货合约',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'domain_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'total_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1单位数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'unit_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化定价起始时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'start_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化定价结束时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'end_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总交易日',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'total_day'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'未成交数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'not_deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'已分配数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'assigned_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'定价状态 -1:合同未生效 0：未开始定价 1：定价中 2：定价完成',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'price_status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发起人ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作人ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'goods_category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户名称',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'customer_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'单位增量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'unit_increment'
GO

EXEC sp_addextendedproperty
'MS_Description', N'现金返还量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cash_return'
GO

EXEC sp_addextendedproperty
'MS_Description', N'累积价格',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'触发价格',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'trigger_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'累计释放量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_release'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化单位数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'structure_unit_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'签订日期',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'sign_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'卖方主体',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'supplier_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化定价合同表',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure'
GO


-- ----------------------------
-- Primary Key structure for table dbt_contract_structure
-- ----------------------------
ALTER TABLE [dbo].[dbt_contract_structure] ADD CONSTRAINT [PK__dbt_cont__3213E83F99FB9068] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

