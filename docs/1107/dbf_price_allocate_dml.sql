alter table dbf_price_allocate add  price_deal_id int DEFAULT ((0)) NULL;
alter table dbf_price_allocate add  supplier_id int NULL;
alter table dbf_price_allocate add  supplier_name varchar(255) COLLATE Chinese_PRC_CI_AS NULL;

EXEC sp_addextendedproperty
'MS_Description', N'�ɽ���id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_allocate',
'COLUMN', N'price_deal_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'�ɽ���id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_allocate',
'COLUMN', N'price_deal_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'����ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_allocate',
'COLUMN', N'supplier_id'
GO