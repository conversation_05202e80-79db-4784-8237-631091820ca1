/*
 Navicat Premium Data Transfer

 Source Server         : navigator-admin-dev
 Source Server Type    : SQL Server
 Source Server Version : 14003411
 Source Host           : *************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003411
 File Encoding         : 65001

 Date: 14/11/2022 20:36:49
*/


-- ----------------------------
-- Table structure for dbz_trace_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_trace_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_trace_log]
GO

CREATE TABLE [dbo].[dbz_trace_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [refer_biz_id] int NULL,
  [refer_biz_code] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [biz_module] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [data] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [log_info] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime NULL,
  [updated_at] datetime NULL
)
GO

ALTER TABLE [dbo].[dbz_trace_log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务记录ID',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'refer_biz_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务编码',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'refer_biz_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务模块',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'biz_module'
GO

EXEC sp_addextendedproperty
'MS_Description', N'Json数据',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'日志内容',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log',
'COLUMN', N'log_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作日志表',
'SCHEMA', N'dbo',
'TABLE', N'dbz_trace_log'
GO


-- ----------------------------
-- Primary Key structure for table dbz_trace_log
-- ----------------------------
ALTER TABLE [dbo].[dbz_trace_log] ADD CONSTRAINT [PK__dbm_oper__3213E83F83261FA0_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

