/*
 Navicat Premium Data Transfer

 Source Server         : navigator-admin-dev
 Source Server Type    : SQL Server
 Source Server Version : 14003411
 Source Host           : *************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003411
 File Encoding         : 65001

 Date: 14/11/2022 20:11:30
*/


-- ----------------------------
-- Table structure for dbf_price_deal_detail
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbf_price_deal_detail]') AND type IN ('U'))
	DROP TABLE [dbo].[dbf_price_deal_detail]
GO

CREATE TABLE [dbo].[dbf_price_deal_detail] (
  [id] int IDENTITY(1,1) NOT NULL,
  [price_apply_id] int DEFAULT ((0)) NULL,
  [type] int DEFAULT ((0)) NULL,
  [apply_num] decimal(18,6) DEFAULT ((0)) NULL,
  [deal_num] decimal(18,6) DEFAULT ((0)) NULL,
  [not_deal_num] decimal(18,6) DEFAULT ((0)) NULL,
  [total_day] int DEFAULT ((0)) NULL,
  [unit_num] decimal(18,6) DEFAULT ((0)) NULL,
  [closing_price] decimal(18,6) DEFAULT ((0)) NULL,
  [deal_date] varchar(32) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [deal_price] decimal(18,6) NULL,
  [memo] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [deal_hand_num] int DEFAULT ((0)) NULL,
  [status] int NULL,
  [transaction_diff_price] decimal(15,6) DEFAULT ((0)) NULL,
  [allocate_num] decimal(15,6) DEFAULT ((0)) NULL,
  [allocate_status] int DEFAULT ((0)) NULL,
  [not_allocate_num] decimal(15,6) DEFAULT ((0)) NULL,
  [category_id] int DEFAULT ((0)) NULL,
  [category_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [not_deal_hand_num] int DEFAULT ((0)) NULL,
  [apply_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [dominant_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [tranfer_dominant_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL,
  [customer_id] int DEFAULT ((0)) NULL,
  [customer_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [structure_contract_id] int DEFAULT ((0)) NULL,
  [cancel_reason] nvarchar(1000) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [day_total_cash_return] decimal(15,6) NULL,
  [supplier_id] int NULL,
  [supplier_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dbf_price_deal_detail] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'点价申请ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'price_apply_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型（1.点价 2.转月 3.反点价 4结构化定价）',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请数量',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'apply_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交数量',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'释放数量（不成交数量）',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'not_deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总交易日',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'total_day'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1单位数量',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'unit_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'收盘价',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'closing_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交日',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'deal_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交价',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'deal_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交手数',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'deal_hand_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态（ 2.待成交 3.成交待分配  4.未成交 ）',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'成交价差',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'transaction_diff_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'分配量',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'allocate_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'分配状态（1 未分配 2 部分分配 3 全部分配）',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'allocate_status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'未分配量',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'not_allocate_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类id',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'category_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'未成交手数',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'not_deal_hand_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请单code',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'apply_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货合约',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'dominant_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'转入合约',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'tranfer_dominant_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'customer_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化定价合同ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'structure_contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'未成交原因',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'cancel_reason'
GO

EXEC sp_addextendedproperty
'MS_Description', N'当日返现总金额',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'day_total_cash_return'
GO

EXEC sp_addextendedproperty
'MS_Description', N'卖方ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'supplier_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'卖方名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail',
'COLUMN', N'supplier_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化处理详情表',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_deal_detail'
GO


-- ----------------------------
-- Primary Key structure for table dbf_price_deal_detail
-- ----------------------------
ALTER TABLE [dbo].[dbf_price_deal_detail] ADD CONSTRAINT [PK__dbf_pric__3213E83F44E96172] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

