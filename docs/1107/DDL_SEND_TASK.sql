DROP TABLE [dbo].[dbm_send_task] 
GO
CREATE TABLE [dbo].[dbm_send_task] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [business_template_id] int DEFAULT ((0)) NULL,
  [plan_send_time] datetime  NULL,
  [sender] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [sender_name] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [receiver] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
  [receiver_type] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [copyer] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
  [copyer_type] nvarchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [title] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
  [content] text COLLATE Chinese_PRC_CI_AS  NULL,
  [message_type] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [status] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [created_by] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [updated_by] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [business_code] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [refer_id] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [replay_to] varchar(1000) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [try_times] int DEFAULT ((0)) NULL
)  
ON [PRIMARY]
TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[dbm_send_task] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务模板id',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'business_template_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'计划发送时间',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'plan_send_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发送者',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'sender'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发送者姓名',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'sender_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接收者',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'receiver'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接收者类型，用户/角色',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'receiver_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'抄送人',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'copyer'
GO

EXEC sp_addextendedproperty
'MS_Description', N'抄送人类型',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'copyer_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'标题',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'title'
GO

EXEC sp_addextendedproperty
'MS_Description', N'拼接好的消息内容',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'消息类型（短信:sms,邮箱:mail,站内信:inmail)',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'message_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发送状态',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务创建者',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务修改时间',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务修改者',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务场景',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'business_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联记录Id',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'refer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'回复邮箱地址',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'replay_to'
GO

EXEC sp_addextendedproperty
'MS_Description', N'重试次数',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task',
'COLUMN', N'try_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发送任务表',
'SCHEMA', N'dbo',
'TABLE', N'dbm_send_task'