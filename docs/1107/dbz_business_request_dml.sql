/*
 Navicat Premium Data Transfer

 Source Server         : navigator-admin-dev
 Source Server Type    : SQL Server
 Source Server Version : 14003411
 Source Host           : *************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003411
 File Encoding         : 65001

 Date: 14/11/2022 20:37:30
*/


-- ----------------------------
-- Table structure for dbz_business_request
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_business_request]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_business_request]
GO

CREATE TABLE [dbo].[dbz_business_request] (
  [id] int IDENTITY(1,1) NOT NULL,
  [biz_code] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [biz_module] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [data] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime NULL,
  [updated_at] datetime NULL
)
GO

ALTER TABLE [dbo].[dbz_business_request] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务编码',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request',
'COLUMN', N'biz_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务模块',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request',
'COLUMN', N'biz_module'
GO

EXEC sp_addextendedproperty
'MS_Description', N'Json数据',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request',
'COLUMN', N'data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注信息',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作日志表',
'SCHEMA', N'dbo',
'TABLE', N'dbz_business_request'
GO


-- ----------------------------
-- Primary Key structure for table dbz_business_request
-- ----------------------------
ALTER TABLE [dbo].[dbz_business_request] ADD CONSTRAINT [PK__dbm_oper__3213E83F83261FA0_copy1_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

