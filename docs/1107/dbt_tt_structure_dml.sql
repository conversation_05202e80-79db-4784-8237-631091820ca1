/*
 Navicat Premium Data Transfer

 Source Server         : navigator-admin-dev
 Source Server Type    : SQL Server
 Source Server Version : 14003411
 Source Host           : *************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003411
 File Encoding         : 65001

 Date: 14/11/2022 20:11:04
*/


-- ----------------------------
-- Table structure for dbt_tt_structure
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbt_tt_structure]') AND type IN ('U'))
	DROP TABLE [dbo].[dbt_tt_structure]
GO

CREATE TABLE [dbo].[dbt_tt_structure] (
  [id] int IDENTITY(1,1) NOT NULL,
  [tt_id] int DEFAULT ((0)) NULL,
  [contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [contract_id] int DEFAULT ((0)) NULL,
  [structure_type] int DEFAULT ((0)) NULL,
  [total_num] decimal(18) DEFAULT ((0)) NULL,
  [total_day] int NULL,
  [unit_num] decimal(18) DEFAULT ((0)) NULL,
  [domain_code] nvarchar(32) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [memo] nvarchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [start_time] datetime NULL,
  [end_time] datetime NULL,
  [created_by] int DEFAULT ((0)) NULL,
  [updated_by] int DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [sign_date] datetime DEFAULT (getdate()) NULL,
  [unit_increment] decimal(18,6) NULL,
  [cash_return] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [cumulative_price] varchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
  [trigger_price] varchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
  [customer_id] int NULL,
  [customer_name] varchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
  [structure_unit_num] decimal(18,6) NULL,
  [price_apply_id] int NULL,
  [price_rule] int NULL
)
GO

ALTER TABLE [dbo].[dbt_tt_structure] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'ttid',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'tt_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同ID',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1:A,2:B,3:C',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'structure_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构总量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'total_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'总交易日',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'total_day'
GO

EXEC sp_addextendedproperty
'MS_Description', N'1单位量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'unit_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货合约',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'domain_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'定价开始日期',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'start_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'定价结束日期',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'end_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'签订日期',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'sign_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'单位增量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'unit_increment'
GO

EXEC sp_addextendedproperty
'MS_Description', N'现金返还量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'cash_return'
GO

EXEC sp_addextendedproperty
'MS_Description', N'累积价格',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'cumulative_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'触发价格',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'trigger_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属商务ID ',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属商务名称',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'customer_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化单位数量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure',
'COLUMN', N'structure_unit_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结构化定价申请单表',
'SCHEMA', N'dbo',
'TABLE', N'dbt_tt_structure'
GO


-- ----------------------------
-- Primary Key structure for table dbt_tt_structure
-- ----------------------------
ALTER TABLE [dbo].[dbt_tt_structure] ADD CONSTRAINT [PK__dbt_tt_s__3213E83F20E01573] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

