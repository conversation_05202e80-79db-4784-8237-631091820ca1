alter table dba_customer_detail add grade_score varchar(128) COLLATE Chinese_PRC_CI_AS  NULL;



INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (433, N'TY_052', N'TY_052', N'导出数据', NULL, 379, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (444, N'TY_053', N'TY_053', N'下载评级模板', NULL, 379, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (445, N'TY_054', N'TY_054', N'批量上传评级', NULL, 379, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (446, N'TY_055', N'TY_055', N'挂单区间新增', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (447, N'TY_056', N'TY_056', N'挂单区间编辑', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (448, N'TY_057', N'TY_057', N'挂单区间删除', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (449, N'TY_058', N'TY_058', N'挂单区间导出', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (450, N'TY_059', N'TY_059', N'白名单客户评级编辑', NULL, 60, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (451, N'TY_060', N'TY_060', N'白名单客户评级编辑', NULL, 61, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (452, N'TY_061', N'TY_061', N'豆粕客户评级编辑', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (453, N'TY_062', N'TY_062', N'豆油客户评级编辑', NULL, 64, 2, 1, 0, '2023-07-07 14:41:37.000', '2023-07-07 14:41:37.000');



CREATE TABLE [dbo].[dbf_price_power_time] (
  [id] int IDENTITY(1,1) NOT NULL,
  [start_time] time(7) NOT NULL,
  [end_time] time(7) NOT NULL,
  [magellan_price_power_type] tinyint NOT NULL,
  [colum_price_power_type] tinyint NOT NULL,
  [category_id_list] varchar(256) COLLATE Chinese_PRC_CI_AS NULL,
  [category_name_list] varchar(512) COLLATE Chinese_PRC_CI_AS NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [created_by] int NULL,
  [updated_by] int NULL,
  [created_by_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [updated_by_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [created_date] varchar(128) COLLATE Chinese_PRC_CI_AS NULL,
  [updated_date] varchar(128) COLLATE Chinese_PRC_CI_AS NULL,
  [add_update_state] int DEFAULT ((0)) NULL,
  CONSTRAINT [PK__dbf_pric__3213E83F753F8EE2] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]
)
ON [PRIMARY]
GO

ALTER TABLE [dbo].[dbf_price_power_time] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'开始时间',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'start_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结束时间',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'end_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'麦哲伦定价特权类型',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'magellan_price_power_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'哥伦布定价特权类型',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'colum_price_power_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种ID列表',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'category_id_list'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种名称列表',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'category_name_list'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间所在日期字符串',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'created_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间所在日期字符串',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'updated_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'新增更新状态',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_power_time',
'COLUMN', N'add_update_state'






CREATE TABLE [dbo].[dbf_price_grade] (
  [id] int IDENTITY(1,1) NOT NULL,
  [business_code] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [business_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [category_id] int NULL,
  [grade_start] int NULL,
  [grade_end] int NULL,
  [category_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [created_by] int NULL,
  [updated_by] int NULL,
  [created_by_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  [updated_by_name] varchar(255) COLLATE Chinese_PRC_CI_AS NULL,
  CONSTRAINT [PK__dba_pric__3213E83F598EE5C4] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]
)
ON [PRIMARY]
GO

ALTER TABLE [dbo].[dbf_price_grade] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务Code',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'business_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'business_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种ID',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'评级低位数',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'grade_start'
GO

EXEC sp_addextendedproperty
'MS_Description', N'评级高位数',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'grade_end'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种名称',
'SCHEMA', N'dbo',
'TABLE', N'dbf_price_grade',
'COLUMN', N'category_name'