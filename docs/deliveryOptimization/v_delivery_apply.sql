ALTER VIEW [dbo].[v_delivery_apply] AS SELECT
	ROW_NUMBER ( ) OVER ( ORDER BY apply.id ) AS row_id,
	apply.id,
	apply.code,
	apply.apply_num,
	apply.total_allocate_num,
	apply.apply_status,
	apply.bill_status,
	apply.customer_id,
	apply.supplier_id,
	apply.goods_id,
	apply.goods_category_id,
	apply.warehouse_type,
	apply.delivery_warehouse_id,
	apply.delivery_type,
	apply.plan_delivery_time,
	apply.delivery_factory_code,
	apply.plate_number,
	apply.trailer_number,
	apply.driver_name,
	apply.driver_id_number,
	apply.onboard_phone,
	apply.is_carpool,
	apply.carpool_info,
	apply.carpool_priority,
	apply.ship_delivery_destination,
	apply.approval_info,
	apply.invalid_reason,
	apply.remark,
	apply.created_by,
	apply.updated_by,
	apply.created_at,
	apply.updated_at,
	apply.approval_by,
	apply.approval_at,
	apply.apply_at,
	apply.car_type,
    car_type.rule_key AS car_type_name,
	customer.name AS customer_name,
	supplier.name AS supplier_name,
	goods.name AS goods_name,
	goods.linkage_goods_code AS linkage_goods_code,
	package.name AS goods_package,
	spec.name AS goods_spec,
	delivery_type.name AS delivery_type_name,
	delivery_warehouse.name AS delivery_warehouse_name,
	warehouse.name AS ship_warehouse_name,
	apply_contract.contract_code AS contract_code,
	apply_contract.allocate_num AS allocate_num,
	apply_contract.unit_price AS unit_price,
	apply_contract.allocate_amount AS allocate_amount,
	apply_contract.contract_type AS contract_type,
	apply_contract.contract_sign_date AS contract_sign_date,
	apply_contract.delivery_start_time AS delivery_start_time,
	apply_contract.delivery_end_time AS delivery_end_time
FROM
	dbd_delivery_apply apply
	LEFT JOIN dbd_delivery_apply_contract apply_contract ON apply.id = apply_contract.apply_id
	LEFT JOIN dba_factory_warehouse warehouse ON apply_contract.ship_warehouse_id = warehouse.id
	LEFT JOIN dbo.dba_customer customer ON apply.customer_id = customer.id
	LEFT JOIN dbo.dba_customer supplier ON apply.supplier_id = supplier.id
	LEFT JOIN dbg_goods goods ON apply.goods_id = goods.id
	LEFT JOIN dbt_delivery_type delivery_type ON apply_contract.delivery_type = delivery_type.id
	LEFT JOIN dba_delivery_warehouse delivery_warehouse ON apply.delivery_warehouse_id = delivery_warehouse.id
	LEFT JOIN dbg_attribute_value package ON package.id = goods.package_id
	LEFT JOIN dbg_attribute_value spec ON spec.id = goods.spec_id
    LEFT JOIN dbz_system_rule_item car_type ON apply.car_type = car_type.id

-- 刷新视图(执行完修改脚本后执行)
sp_refreshview 'v_delivery_apply'
