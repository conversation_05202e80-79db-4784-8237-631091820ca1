ALTER TABLE [dbo].[dbd_delivery_apply] ADD [car_type] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'车辆类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'car_type'

ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] ADD [car_type] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'车辆类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_driver_log',
    'COLUMN', N'car_type'


ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [payment_type] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'付款方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'payment_type'

DROP INDEX [unique_driver] ON [dbo].[dbd_delivery_apply_driver_log]
    GO
