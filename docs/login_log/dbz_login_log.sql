/*
 登录日志表DDL
 用于记录用户登录日志信息
 
 Date: 2025-06-26
*/

-- ----------------------------
-- Table structure for dbz_login_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_login_log]') AND type IN ('U'))
DROP TABLE [dbo].[dbz_login_log]
    GO

CREATE TABLE [dbo].[dbz_login_log] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [user_id] int  NULL,
    [username] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [email] nvarchar(128) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [phone] nvarchar(32) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [login_system] int DEFAULT 1 NULL,
    [login_type] int DEFAULT 1 NULL,
    [login_status] int DEFAULT 0 NULL,
    [ip_address] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [user_agent] nvarchar(512) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [login_time] datetime  NULL,
    [failure_reason] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [request_params] ntext COLLATE Chinese_PRC_CI_AS  NULL,
    [response_result] ntext COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbz_login_log] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'user_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'username'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户邮箱',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'email'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户手机号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'phone'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'登录系统(1:Magellan 2:Columbus)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'login_system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'登录方式(1:邮箱密码 2:手机验证码 3:AAD登录)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'login_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'登录状态(1:成功 0:失败)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'login_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'登录IP地址',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'ip_address'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户代理(浏览器信息)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'user_agent'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'登录时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'login_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'失败原因',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'failure_reason'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求参数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'request_params'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'响应结果',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'response_result'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_login_log',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dbz_login_log
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbz_login_log]', RESEED, 1)
    GO


-- ----------------------------
-- Indexes structure for table dbz_login_log
-- ----------------------------
CREATE NONCLUSTERED INDEX [idx_user_id]
ON [dbo].[dbz_login_log] (
  [user_id] ASC
)
GO

CREATE NONCLUSTERED INDEX [idx_email]
ON [dbo].[dbz_login_log] (
  [email] ASC
)
GO

CREATE NONCLUSTERED INDEX [idx_phone]
ON [dbo].[dbz_login_log] (
  [phone] ASC
)
GO

CREATE NONCLUSTERED INDEX [idx_login_time]
ON [dbo].[dbz_login_log] (
  [login_time] DESC
)
GO

CREATE NONCLUSTERED INDEX [idx_login_system_status]
ON [dbo].[dbz_login_log] (
  [login_system] ASC,
  [login_status] ASC
)
GO

CREATE NONCLUSTERED INDEX [idx_ip_address]
ON [dbo].[dbz_login_log] (
  [ip_address] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dbz_login_log
-- ----------------------------
ALTER TABLE [dbo].[dbz_login_log] ADD CONSTRAINT [PK__dbz_logi__3213E83F] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

