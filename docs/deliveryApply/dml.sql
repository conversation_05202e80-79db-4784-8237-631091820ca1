-- 商品表添加字段
ALTER TABLE [dbo].[dbg_goods] ADD [is_delivery] int DEFAULT ((1)) NULL
    GO

ALTER TABLE [dbo].[dbg_goods] ADD [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否可提货',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'is_delivery'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'updated_by'
-- 初始化数据
update dbg_goods set is_delivery = 1 where is_delivery is null

-- 合同表添加字段
ALTER TABLE [dbo].[dbt_contract] ADD [apply_delivery_num] decimal (25,6) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'申请提货数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'apply_delivery_num'