/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_0214
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 06/09/2023 14:50:35
*/


-- ----------------------------
-- Table structure for dbd_delivery_apply_driver_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbd_delivery_apply_driver_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dbd_delivery_apply_driver_log]
GO

CREATE TABLE [dbo].[dbd_delivery_apply_driver_log] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [customer_id] int DEFAULT ((0)) NULL,
  [plate_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [trailer_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [driver_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [driver_id_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [onboard_phone] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [goods_category_id] int  NULL,
  [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'车船号',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'plate_number'
GO

EXEC sp_addextendedproperty
'MS_Description', N'挂车号',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'trailer_number'
GO

EXEC sp_addextendedproperty
'MS_Description', N'司机姓名',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'driver_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'司机身份证号码',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'driver_id_number'
GO

EXEC sp_addextendedproperty
'MS_Description', N'随车电话',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'onboard_phone'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'goods_category_id'
GO

    EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'dbd_delivery_apply_driver_log',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_driver_log'
GO


-- ----------------------------
-- Auto increment value for dbd_delivery_apply_driver_log
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbd_delivery_apply_driver_log]', RESEED, 1)
GO


-- ----------------------------
-- Indexes structure for table dbd_delivery_apply_driver_log
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [unique_driver]
ON [dbo].[dbd_delivery_apply_driver_log] (
  [plate_number] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dbd_delivery_apply_driver_log
-- ----------------------------
ALTER TABLE [dbo].[dbd_delivery_apply_driver_log] ADD CONSTRAINT [PK__dbd_deli__3213E83FB866DFBB] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

