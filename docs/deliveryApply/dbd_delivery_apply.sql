/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 25/09/2023 15:13:40
*/


-- ----------------------------
-- Table structure for dbd_delivery_apply
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbd_delivery_apply]') AND type IN ('U'))
DROP TABLE [dbo].[dbd_delivery_apply]
    GO

CREATE TABLE [dbo].[dbd_delivery_apply] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [apply_num] decimal(25,6) DEFAULT ((0)) NULL,
    [total_allocate_num] decimal(25,6) DEFAULT ((0)) NULL,
    [apply_status] int DEFAULT ((0)) NULL,
    [bill_status] int DEFAULT ((0)) NULL,
    [customer_id] int DEFAULT ((0)) NULL,
    [supplier_id] int DEFAULT ((0)) NULL,
    [goods_id] int DEFAULT ((0)) NULL,
    [goods_category_id] int DEFAULT ((0)) NULL,
    [warehouse_type] int DEFAULT ((0)) NULL,
    [delivery_warehouse_id] int DEFAULT ((0)) NULL,
    [delivery_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [plan_delivery_time] datetime DEFAULT (getdate()) NULL,
    [delivery_factory_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [plate_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [trailer_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [driver_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [driver_id_number] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [onboard_phone] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_carpool] int DEFAULT ((0)) NULL,
    [carpool_info] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [carpool_priority] int DEFAULT ((0)) NULL,
    [ship_delivery_destination] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [approval_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [invalid_reason] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [remark] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    [approval_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [approval_at] datetime  NULL,
    [apply_at] datetime  NULL
    )
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'apply_num'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已分配合同数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'total_allocate_num'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请状态（0.所有 1.新录入 2.待审批 3.审批通过 4.审批驳回 5.作废待审核 6.已作废）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'apply_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'开单状态（1.未开单 2.已开单）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'bill_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'买方主体ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'卖方主体ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'supplier_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'goods_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品类id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'goods_category_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'LDC库/外库 1.LDC库 2.外库',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'warehouse_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货库点id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'delivery_warehouse_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'delivery_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'计划提货日期',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'plan_delivery_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货工厂',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'delivery_factory_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'车/船号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'plate_number'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'挂车号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'trailer_number'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'司机姓名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'driver_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'司机身份证号码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'driver_id_number'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'随车电话',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'onboard_phone'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否拼车/船',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'is_carpool'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'拼车/船信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'carpool_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'拼车/船优先执行',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'carpool_priority'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'船提目的港',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'ship_delivery_destination'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审批信息json',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'approval_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'作废原因',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'invalid_reason'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审批人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'approval_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审批时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'approval_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'apply_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货申请表',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply'
    GO


    -- ----------------------------
-- Auto increment value for dbd_delivery_apply
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbd_delivery_apply]', RESEED, 1)
    GO


-- ----------------------------
-- Primary Key structure for table dbd_delivery_apply
-- ----------------------------
ALTER TABLE [dbo].[dbd_delivery_apply] ADD CONSTRAINT [PK__dbd_deli__3213E83F4442D62E] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

