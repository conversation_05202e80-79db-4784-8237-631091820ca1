/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_0214
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 06/09/2023 14:50:20
*/


-- ----------------------------
-- Table structure for dbd_delivery_apply_contract
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbd_delivery_apply_contract]') AND type IN ('U'))
	DROP TABLE [dbo].[dbd_delivery_apply_contract]
GO

CREATE TABLE [dbo].[dbd_delivery_apply_contract] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [apply_id] int DEFAULT ((0)) NULL,
  [contract_id] int DEFAULT ((0)) NULL,
  [contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [allocate_num] decimal(25,6) DEFAULT ((0)) NULL,
  [contract_num] decimal(25,6) DEFAULT ((0)) NULL,
  [contract_type] int DEFAULT ((0)) NULL,
  [unit_price] decimal(25,6) DEFAULT ((0)) NULL,
  [allocate_amount] decimal(25,6) DEFAULT ((0)) NULL,
  [delivery_type] int DEFAULT ((0)) NULL,
  [ship_warehouse_id] int DEFAULT ((0)) NULL,
  [contract_sign_date] datetime DEFAULT (getdate()) NULL,
  [delivery_start_time] datetime DEFAULT (getdate()) NULL,
  [delivery_end_time] datetime DEFAULT (getdate()) NULL,
  [remark] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [is_deleted] int  NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请单id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'apply_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'分配数量',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'allocate_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同数量',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'contract_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同类型',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'contract_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'含税单价',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'unit_price'
GO

EXEC sp_addextendedproperty
'MS_Description', N'分配金额',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'allocate_amount'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交提货方式',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'delivery_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点id',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'ship_warehouse_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'签订日期',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'contract_sign_date'
GO

EXEC sp_addextendedproperty
'MS_Description', N'提货开始日期',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'delivery_start_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'提货截止日期',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'delivery_end_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否删除',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbd_delivery_apply_contract',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbd_delivery_apply_contract
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbd_delivery_apply_contract]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbd_delivery_apply_contract
-- ----------------------------
ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD CONSTRAINT [PK__dbd_deli__3213E83F066E7A38] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

