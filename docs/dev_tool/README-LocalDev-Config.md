# Navigator Cloud Local Development Configuration

这些脚本用于自动配置Navigator Cloud项目的所有微服务，使其能够在本地开发环境中运行。

## 文件说明

- `configure-local-dev.ps1` - PowerShell主脚本
- `configure-local-dev.bat` - 批处理启动脚本
- `README-LocalDev-Config.md` - 本说明文件

## 功能

脚本会自动修改所有微服务的配置文件：

### 1. Bootstrap.yml文件
- 确保 `active: dev` 配置正确

### 2. Bootstrap-dev.yml文件
- 将 `server-addr` 改为 `http://localhost:8848`
- 将 `namespace` 改为 `navigator_cloud_int`
- 删除 `username` 和 `password` 配置（本地无需密码）

## 使用方法

### 方法一：直接运行批处理文件
```bash
# 在Navigator_cloud根目录下运行
configure-local-dev.bat
```

### 方法二：直接运行PowerShell脚本
```powershell
# 在Navigator_cloud根目录下运行
powershell.exe -ExecutionPolicy Bypass -File configure-local-dev.ps1
```

### 方法三：在PowerShell中运行
```powershell
# 打开PowerShell，切换到Navigator_cloud根目录
cd "C:\path\to\Navigator_cloud"
.\configure-local-dev.ps1
```

## 涉及的微服务

脚本会自动处理以下19个微服务的配置文件：

**普通微服务（16个）：**
- navigator-activiti
- navigator-admin
- navigator-cuckoo
- navigator-customer
- navigator-dagama
- navigator-delivery
- navigator-future
- navigator-gateway
- navigator-goods
- navigator-husky
- navigator-koala
- navigator-open
- navigator-pay
- navigator-pigeon
- navigator-sparrow
- navigator-trade

**Web服务（3个）：**
- navigator-columbus-web
- navigator-magellan-web
- navigator-open-api

## 前置条件

1. 确保本地已安装并启动Nacos服务器（端口8848）
2. 在Nacos中创建 `navigator_cloud_int` 命名空间
3. 确保PowerShell执行策略允许运行脚本

## 验证

脚本运行完成后会自动验证配置结果，显示成功配置的文件数量。

## 注意事项

1. 脚本必须在Navigator_cloud项目根目录下运行
2. 建议在运行脚本前备份原始配置文件
3. 如果某些文件不存在，脚本会跳过并显示警告
4. 脚本使用相对路径，适用于任何下载的分支

## 故障排除

如果遇到PowerShell执行策略限制，可以临时设置：
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

或者使用管理员权限运行：
```powershell
powershell.exe -ExecutionPolicy Bypass -File configure-local-dev.ps1
```
