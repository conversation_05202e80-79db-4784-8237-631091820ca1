@echo off
REM Navigator Cloud Local Development Configuration Script
REM This script configures all microservices to use local Nacos (localhost:8848) with navigator_cloud_int namespace
REM Author: <PERSON> Shi
REM Date: 2025-01-06

echo === Navigator Cloud Local Development Configuration Script ===
echo Configuring all microservices for local development...
echo.

REM Use PowerShell to perform the configuration
powershell.exe -ExecutionPolicy Bypass -File "%~dp0configure-local-dev.ps1"

echo.
echo Press any key to exit...
pause >nul
