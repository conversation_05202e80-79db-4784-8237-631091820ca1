# Navigator项目服务依赖关系图

## 项目概述

Navigator是一个基于Spring Cloud的微服务架构项目，包含多个业务服务和Web应用。本文档详细描述了各服务之间的依赖关系，特别是facade模块的依赖关系。

## 服务架构图

```mermaid
graph TB
    %% Web应用层
    subgraph "Web应用层"
        MW[navigator-magellan-web]
        CW[navigator-columbus-web]
        OA[navigator-open-api]
    end
    
    %% 网关层
    GW[navigator-gateway]
    
    %% 业务服务层
    subgraph "核心业务服务"
        AS[navigator-admin-service]
        TS[navigator-trade-service]
        DS[navigator-delivery-service]
        CS[navigator-customer-service]
        GS[navigator-goods-service]
        FS[navigator-future-service]
    end
    
    subgraph "扩展业务服务"
        PS[navigator-pigeon-service]
        SS[navigator-sparrow-service]
        HS[navigator-husky-service]
        KS[navigator-koala-service]
        CKS[navigator-cuckoo-service]
        DGS[navigator-dagama-service]
        ACS[navigator-activiti-service]
    end
    
    %% Facade层
    subgraph "Facade接口层"
        AF[admin-facade]
        TF[trade-facade]
        DF[delivery-facade]
        CF[customer-facade]
        GF[goods-facade]
        FF[future-facade]
        PF[pigeon-facade]
        SF[sparrow-facade]
        HF[husky-facade]
        KF[koala-facade]
        CKF[cuckoo-facade]
        DGF[dagama-facade]
        ACF[activiti-facade]
    end
    
    %% 公共模块
    NC[navigator-commons]
    
    %% Web应用依赖关系
    MW --> AF
    MW --> TF
    MW --> DF
    MW --> CF
    MW --> GF
    MW --> FF
    MW --> PF
    MW --> SF
    MW --> HF
    MW --> KF
    MW --> CKF
    MW --> DGF
    MW --> NC
    
    CW --> AF
    CW --> TF
    CW --> DF
    CW --> CF
    CW --> GF
    CW --> FF
    CW --> PF
    CW --> SF
    CW --> HF
    CW --> KF
    CW --> CKF
    CW --> DGF
    CW --> NC
    
    %% 服务到Facade的关系
    AS --> AF
    TS --> TF
    DS --> DF
    CS --> CF
    GS --> GF
    FS --> FF
    PS --> PF
    SS --> SF
    HS --> HF
    KS --> KF
    CKS --> CKF
    DGS --> DGF
    ACS --> ACF
    
    %% Facade之间的依赖关系
    TF --> DF
    TF --> GF
    TF --> ACF
    TF --> HF
    
    FF --> TF
    
    PF --> CF
    PF --> GF
    PF --> TF
    
    CKF --> CF
    CKF --> GF
    CKF --> TF
    
    GF --> AF
    
    %% 服务间facade依赖
    DS --> CF
    DS --> GF
    DS --> TF
    DS --> CKF
    
    TS --> AF
    TS --> GF
    TS --> SF
    TS --> FF
    TS --> PF
    TS --> CKF
    
    FS --> CF
    FS --> SF
    FS --> GF
    FS --> KF
    
    CS --> SF
    CS --> AF
    CS --> TF
    
    GS --> FF
    
    %% 所有模块都依赖commons
    AF --> NC
    TF --> NC
    DF --> NC
    CF --> NC
    GF --> NC
    FF --> NC
    PF --> NC
    SF --> NC
    HF --> NC
    KF --> NC
    CKF --> NC
    DGF --> NC
    ACF --> NC
```

## 关键依赖关系分析

### 1. Web应用层依赖

#### navigator-magellan-web 依赖的facade：
- admin-facade
- trade-facade
- **delivery-facade** ⚠️
- customer-facade
- goods-facade
- future-facade
- pigeon-facade
- sparrow-facade
- husky-facade
- koala-facade
- cuckoo-facade
- dagama-facade

#### navigator-columbus-web 依赖的facade：
- admin-facade
- trade-facade
- **delivery-facade** ⚠️
- customer-facade
- goods-facade
- future-facade
- pigeon-facade
- sparrow-facade
- husky-facade
- koala-facade
- cuckoo-facade
- dagama-facade

### 2. 关键发现：delivery-facade的影响范围

当修改 `delivery-facade` 中的DTO类时，以下服务需要重新构建：

#### 直接依赖delivery-facade的服务：
1. **navigator-magellan-web** ⚠️ (你遇到的问题)
2. **navigator-columbus-web** ⚠️
3. **trade-facade** (facade间依赖)
4. **navigator-delivery-service** (自身服务)

#### 间接依赖delivery-facade的服务：
通过trade-facade间接依赖：
- 所有依赖trade-facade的服务

### 3. Facade间依赖关系

```mermaid
graph LR
    TF[trade-facade] --> DF[delivery-facade]
    TF --> GF[goods-facade]
    TF --> ACF[activiti-facade]
    TF --> HF[husky-facade]
    
    FF[future-facade] --> TF[trade-facade]
    
    PF[pigeon-facade] --> CF[customer-facade]
    PF --> GF[goods-facade]
    PF --> TF[trade-facade]
    
    CKF[cuckoo-facade] --> CF[customer-facade]
    CKF --> GF[goods-facade]
    CKF --> TF[trade-facade]
    
    GF[goods-facade] --> AF[admin-facade]
```

## 部署影响分析

### 修改delivery-facade时需要重新构建的服务：

#### 必须重新构建（直接依赖）：
1. `navigator-magellan-web`
2. `navigator-columbus-web`
3. `navigator-delivery-service`

#### 可能需要重新构建（间接依赖）：
1. `trade-facade` → 影响所有依赖trade-facade的服务
2. 所有依赖trade-facade的服务：
   - `navigator-trade-service`
   - `future-facade` → `navigator-future-service`
   - `pigeon-facade` → `navigator-pigeon-service`
   - `cuckoo-facade` → `navigator-cuckoo-service`

### 变更类型判断：

#### 需要重新构建的变更（Breaking Changes）：
- 新增/删除字段
- 修改字段类型
- 修改字段名称
- 修改方法签名

#### 不需要重新构建的变更（Non-Breaking Changes）：
- 字段计算逻辑变更
- 注解属性变更（如 `@Excel(name="审核人")` → `@Excel(name="审核")`）
- 业务逻辑实现变更

## 实用解决方案

### 1. 智能依赖检测脚本

创建 `check-facade-impact.sh` 脚本：

```bash
#!/bin/bash
# Navigator项目facade变更影响检测脚本

# 定义依赖关系映射
declare -A FACADE_DEPENDENCIES
FACADE_DEPENDENCIES["delivery-facade"]="navigator-magellan-web navigator-columbus-web navigator-delivery-service"
FACADE_DEPENDENCIES["trade-facade"]="navigator-magellan-web navigator-columbus-web navigator-trade-service navigator-future-service navigator-pigeon-service navigator-cuckoo-service"
FACADE_DEPENDENCIES["admin-facade"]="navigator-magellan-web navigator-columbus-web navigator-admin-service navigator-goods-service navigator-customer-service navigator-trade-service"
FACADE_DEPENDENCIES["customer-facade"]="navigator-magellan-web navigator-columbus-web navigator-customer-service navigator-delivery-service navigator-future-service navigator-pigeon-service navigator-cuckoo-service"
FACADE_DEPENDENCIES["goods-facade"]="navigator-magellan-web navigator-columbus-web navigator-goods-service navigator-delivery-service navigator-trade-service navigator-future-service navigator-pigeon-service navigator-cuckoo-service"

# 检测变更的facade模块
check_facade_changes() {
    local prev_commit=${1:-HEAD~1}
    local changed_files=$(git diff --name-only $prev_commit)
    local affected_services=""

    echo "🔍 检测facade变更..."

    for facade in "${!FACADE_DEPENDENCIES[@]}"; do
        if echo "$changed_files" | grep -q "$facade"; then
            echo "📦 检测到 $facade 变更"

            # 检查变更类型
            if check_breaking_change "$facade" "$prev_commit"; then
                echo "🔴 破坏性变更 - 需要重新构建依赖服务"
                affected_services="$affected_services ${FACADE_DEPENDENCIES[$facade]}"
            else
                echo "🟢 兼容性变更 - 无需重新构建依赖服务"
            fi
        fi
    done

    if [ -n "$affected_services" ]; then
        echo ""
        echo "⚠️  需要重新构建的服务："
        echo "$affected_services" | tr ' ' '\n' | sort -u | sed 's/^/  - /'
    else
        echo ""
        echo "✅ 无需重新构建任何服务"
    fi
}

# 检查是否为破坏性变更
check_breaking_change() {
    local facade=$1
    local prev_commit=$2

    # 查找facade目录下的Java文件
    local facade_files=$(find . -path "*/$facade/src/main/java/**/*.java" -type f)

    for file in $facade_files; do
        if git diff --name-only $prev_commit | grep -q "$file"; then
            # 检查字段定义变化（忽略注解内容）
            local current_fields=$(grep -E "private.*;" "$file" 2>/dev/null | sed 's/@[^;]*//g' | sort)
            local previous_fields=$(git show "$prev_commit:$file" 2>/dev/null | grep -E "private.*;" | sed 's/@[^;]*//g' | sort)

            if [ "$current_fields" != "$previous_fields" ]; then
                echo "    🔍 $file: 字段定义发生变化"
                return 0  # 破坏性变更
            fi

            # 检查方法签名变化
            local current_methods=$(grep -E "public.*\(" "$file" 2>/dev/null | sed 's/{.*//g' | sort)
            local previous_methods=$(git show "$prev_commit:$file" 2>/dev/null | grep -E "public.*\(" | sed 's/{.*//g' | sort)

            if [ "$current_methods" != "$previous_methods" ]; then
                echo "    🔍 $file: 方法签名发生变化"
                return 0  # 破坏性变更
            fi

            echo "    ✅ $file: 仅实现或注解变更"
        fi
    done

    return 1  # 非破坏性变更
}

# 生成构建命令
generate_build_commands() {
    local services="$1"
    echo ""
    echo "🛠️  建议的构建命令："
    echo "mvn clean package -pl $(echo $services | tr ' ' ',') -am"
}

# 主函数
main() {
    echo "Navigator项目facade变更影响检测"
    echo "=================================="

    if [ $# -eq 0 ]; then
        check_facade_changes
    else
        check_facade_changes "$1"
    fi
}

# 运行主函数
main "$@"
```

### 2. Azure DevOps智能构建流水线

创建 `azure-pipelines-smart-build.yml`：

```yaml
# Navigator项目智能构建流水线
trigger:
  branches:
    include:
    - dev_changes_*
    - master
  paths:
    include:
    - navigator-*/

variables:
  buildMagellan: false
  buildColumbus: false
  buildDelivery: false
  buildTrade: false
  buildAdmin: false
  buildCustomer: false
  buildGoods: false

stages:
- stage: AnalyzeDependencies
  displayName: '分析依赖关系'
  jobs:
  - job: DetectChanges
    displayName: '检测变更影响'
    steps:
    - script: |
        echo "检测facade变更..."

        # 检测delivery-facade变更
        if git diff --name-only HEAD~1 | grep -q "delivery-facade"; then
          echo "检测到delivery-facade变更"
          echo "##vso[task.setvariable variable=buildMagellan;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildColumbus;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildDelivery;isOutput=true]true"
        fi

        # 检测trade-facade变更
        if git diff --name-only HEAD~1 | grep -q "trade-facade"; then
          echo "检测到trade-facade变更"
          echo "##vso[task.setvariable variable=buildMagellan;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildColumbus;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildTrade;isOutput=true]true"
        fi

        # 检测admin-facade变更
        if git diff --name-only HEAD~1 | grep -q "admin-facade"; then
          echo "检测到admin-facade变更"
          echo "##vso[task.setvariable variable=buildMagellan;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildColumbus;isOutput=true]true"
          echo "##vso[task.setvariable variable=buildAdmin;isOutput=true]true"
        fi

      name: detectChanges
      displayName: '检测facade变更'

- stage: BuildServices
  displayName: '构建受影响的服务'
  dependsOn: AnalyzeDependencies
  variables:
    buildMagellan: $[ stageDependencies.AnalyzeDependencies.DetectChanges.outputs['detectChanges.buildMagellan'] ]
    buildColumbus: $[ stageDependencies.AnalyzeDependencies.DetectChanges.outputs['detectChanges.buildColumbus'] ]
    buildDelivery: $[ stageDependencies.AnalyzeDependencies.DetectChanges.outputs['detectChanges.buildDelivery'] ]
    buildTrade: $[ stageDependencies.AnalyzeDependencies.DetectChanges.outputs['detectChanges.buildTrade'] ]
    buildAdmin: $[ stageDependencies.AnalyzeDependencies.DetectChanges.outputs['detectChanges.buildAdmin'] ]
  jobs:
  - job: BuildMagellan
    condition: eq(variables.buildMagellan, 'true')
    displayName: '构建Magellan Web'
    steps:
    - task: Maven@3
      inputs:
        mavenPomFile: 'pom.xml'
        goals: 'clean package'
        options: '-pl navigator-web/navigator-magellan-web -am'

  - job: BuildColumbus
    condition: eq(variables.buildColumbus, 'true')
    displayName: '构建Columbus Web'
    steps:
    - task: Maven@3
      inputs:
        mavenPomFile: 'pom.xml'
        goals: 'clean package'
        options: '-pl navigator-web/navigator-columbus-web -am'

  - job: BuildDelivery
    condition: eq(variables.buildDelivery, 'true')
    displayName: '构建Delivery Service'
    steps:
    - task: Maven@3
      inputs:
        mavenPomFile: 'pom.xml'
        goals: 'clean package'
        options: '-pl navigator-delivery -am'
```

### 3. 变更类型检测工具

创建 `check-api-compatibility.java`：

```java
/**
 * API兼容性检测工具
 */
@Component
public class ApiCompatibilityChecker {

    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("用法: java ApiCompatibilityChecker <facade-module> <previous-commit>");
            return;
        }

        String facadeModule = args[0];
        String previousCommit = args[1];

        boolean isBreaking = checkApiCompatibility(facadeModule, previousCommit);

        if (isBreaking) {
            System.out.println("🔴 检测到破坏性变更，需要重新构建依赖服务");
            System.exit(1);
        } else {
            System.out.println("🟢 兼容性变更，无需重新构建依赖服务");
            System.exit(0);
        }
    }

    private static boolean checkApiCompatibility(String facadeModule, String previousCommit) {
        // 实现API兼容性检测逻辑
        // 1. 比较类的public方法签名
        // 2. 比较DTO字段定义
        // 3. 比较接口定义
        return false; // 简化实现
    }
}
```

### 4. 版本管理策略

实施语义化版本管理：

```xml
<!-- 在facade模块的pom.xml中 -->
<version>1.2.3-SNAPSHOT</version>
<!--
  1.x.x - 主版本号：破坏性变更
  x.2.x - 次版本号：新功能，向后兼容
  x.x.3 - 修订号：bug修复，向后兼容
-->
```

## 快速使用指南

### 立即可用的解决方案

1. **保存检测脚本**：
   ```bash
   # 将上面的check-facade-impact.sh保存到项目根目录
   chmod +x check-facade-impact.sh
   ```

2. **检测当前变更影响**：
   ```bash
   # 检测当前提交相对于上一个提交的变更
   ./check-facade-impact.sh

   # 检测相对于特定提交的变更
   ./check-facade-impact.sh HEAD~3
   ```

3. **手动构建受影响的服务**：
   ```bash
   # 如果检测到delivery-facade变更，运行：
   mvn clean package -pl navigator-web/navigator-magellan-web,navigator-web/navigator-columbus-web,navigator-delivery -am
   ```

### 你的具体问题解决方案

针对你遇到的 `delivery-facade` DTO修改问题：

1. **确认变更类型**：
   - ✅ 如果只是修改了 `@Excel(name = "审核人")` → `@Excel(name = "审核")`
   - ✅ 如果只是修改了字段计算逻辑（如 `unExecutedNum` 的计算方式）
   - ❌ 如果新增/删除了字段
   - ❌ 如果修改了字段类型或名称

2. **对于兼容性变更**（如你的情况）：
   - 理论上不需要重新构建 magellan-web 和 columbus-web
   - 但由于当前缺乏自动检测机制，建议还是重新构建以确保一致性

3. **立即解决当前问题**：
   ```bash
   # 重新构建受影响的服务
   mvn clean package -pl navigator-web/navigator-magellan-web,navigator-web/navigator-columbus-web -am

   # 重新部署到AKS
   kubectl rollout restart deployment ldc-navigator-magellan-web-dev -n dev
   kubectl rollout restart deployment ldc-navigator-columbus-web-dev -n dev
   ```

### 长期改进建议

1. **集成到CI/CD流水线**：将检测脚本集成到Azure DevOps流水线中
2. **建立变更规范**：在团队中建立facade变更的规范和检查清单
3. **实施版本管理**：对facade模块进行语义化版本管理

## 总结

你遇到的问题是典型的微服务依赖管理问题。当修改 `delivery-facade` 中的DTO时，所有直接依赖它的Web应用（magellan-web、columbus-web）都需要重新构建和部署，即使变更只是字段计算逻辑或显示名称的修改。

**关键发现**：
- magellan-web 和 columbus-web 都直接依赖 delivery-facade
- 即使是兼容性变更，在缺乏自动检测的情况下，建议重新构建以确保一致性
- 需要建立正式的依赖管理和变更检测机制

**立即行动**：
1. 使用提供的检测脚本分析变更影响
2. 重新构建和部署受影响的服务
3. 考虑实施自动化的依赖检测和构建策略
