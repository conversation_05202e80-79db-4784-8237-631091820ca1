# Navigator Cloud Local Development Configuration Script
# This script configures all microservices to use local Nacos (localhost:8848) with navigator_cloud_int namespace
# Author: <PERSON>
# Date: 2025-01-06

Write-Host "=== Navigator Cloud Local Development Configuration Script ===" -ForegroundColor Green
Write-Host "Configuring all microservices for local development..." -ForegroundColor Yellow

# Function to update bootstrap-dev.yml files
function Update-BootstrapDevFile {
    param(
        [string]$FilePath
    )

    if (Test-Path $FilePath) {
        Write-Host "Processing: $FilePath" -ForegroundColor Cyan

        # Read the file content
        $content = Get-Content $FilePath -Raw

        # Remove username and password lines
        $content = $content -replace "(?m)^\s*username:\s*nacos\s*$", ""
        $content = $content -replace "(?m)^\s*password:\s*nacos\s*$", ""

        # Update server-addr to localhost:8848
        $content = $content -replace "server-addr:\s*http://[^:]+:8848", "server-addr: http://localhost:8848"

        # Update namespace to navigator_cloud_int
        $content = $content -replace "namespace:\s*[^\s]+", "namespace: navigator_cloud_int"

        # Clean up extra empty lines
        $content = $content -replace "(?m)^\s*$\n\s*$", "`n"

        # Write back to file
        $content | Set-Content $FilePath -NoNewline

        Write-Host "  ✓ Updated successfully" -ForegroundColor Green
    } else {
        Write-Host "  ✗ File not found: $FilePath" -ForegroundColor Red
    }
}

# Function to ensure bootstrap.yml has active: dev
function Update-BootstrapFile {
    param(
        [string]$FilePath
    )

    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw

        # Check if active is already set to dev
        if ($content -match "active:\s*dev") {
            Write-Host "  ✓ Already configured for dev" -ForegroundColor Gray
        } else {
            # Update active to dev
            $content = $content -replace "active:\s*[^\s]+", "active: dev"
            $content | Set-Content $FilePath -NoNewline
            Write-Host "  ✓ Updated active to dev" -ForegroundColor Green
        }
    }
}

# Define all microservice paths
$microservices = @(
    "navigator-activiti/activiti-service/src/main/resources",
    "navigator-admin/admin-service/src/main/resources",
    "navigator-cuckoo/cuckoo-service/src/main/resources",
    "navigator-customer/customer-service/src/main/resources",
    "navigator-dagama/dagama-service/src/main/resources",
    "navigator-delivery/delivery-service/src/main/resources",
    "navigator-future/future-service/src/main/resources",
    "navigator-gateway/src/main/resources",
    "navigator-goods/goods-service/src/main/resources",
    "navigator-husky/husky-service/src/main/resources",
    "navigator-koala/koala-service/src/main/resources",
    "navigator-open/open-service/src/main/resources",
    "navigator-pay/pay-service/src/main/resources",
    "navigator-pigeon/pigeon-service/src/main/resources",
    "navigator-sparrow/sparrow-service/src/main/resources",
    "navigator-trade/trade-service/src/main/resources"
)

# Define web services (special path structure)
$webServices = @(
    "navigator-web/navigator-columbus-web/src/main/resources",
    "navigator-web/navigator-magellan-web/src/main/resources",
    "navigator-web/navigator-open-api/src/main/resources"
)

# Combine all services
$allServices = $microservices + $webServices

Write-Host "`n=== Step 1: Updating bootstrap.yml files ===" -ForegroundColor Yellow

foreach ($service in $allServices) {
    $bootstrapFile = Join-Path $service "bootstrap.yml"
    if (Test-Path $bootstrapFile) {
        Write-Host "Processing: $bootstrapFile" -ForegroundColor Cyan
        Update-BootstrapFile -FilePath $bootstrapFile
    }
}

Write-Host "`n=== Step 2: Updating bootstrap-dev.yml files ===" -ForegroundColor Yellow

foreach ($service in $allServices) {
    $bootstrapDevFile = Join-Path $service "bootstrap-dev.yml"
    Update-BootstrapDevFile -FilePath $bootstrapDevFile
}

Write-Host "`n=== Configuration Summary ===" -ForegroundColor Green
Write-Host "✓ All bootstrap.yml files configured with active: dev" -ForegroundColor Green
Write-Host "✓ All bootstrap-dev.yml files configured with:" -ForegroundColor Green
Write-Host "  - server-addr: http://localhost:8848" -ForegroundColor Green
Write-Host "  - namespace: navigator_cloud_int" -ForegroundColor Green
Write-Host "  - Removed username/password authentication" -ForegroundColor Green

Write-Host "`n=== Verification ===" -ForegroundColor Yellow
Write-Host "Verifying configuration..." -ForegroundColor Cyan

$totalFiles = 0
$configuredFiles = 0

foreach ($service in $allServices) {
    $bootstrapDevFile = Join-Path $service "bootstrap-dev.yml"
    if (Test-Path $bootstrapDevFile) {
        $totalFiles++
        $content = Get-Content $bootstrapDevFile -Raw
        if ($content -match "server-addr:\s*http://localhost:8848" -and $content -match "namespace:\s*navigator_cloud_int") {
            $configuredFiles++
        }
    }
}

Write-Host "Successfully configured $configuredFiles out of $totalFiles bootstrap-dev.yml files" -ForegroundColor Green

if ($configuredFiles -eq $totalFiles) {
    Write-Host " All files configured successfully for local development!" -ForegroundColor Green
    Write-Host "You can now start your local Nacos server and run the microservices." -ForegroundColor Green
} else {
    Write-Host " Some files may need manual review." -ForegroundColor Yellow
}

Write-Host "`n=== Script completed ===" -ForegroundColor Green
