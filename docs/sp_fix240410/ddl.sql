--tt query

CREATE INDEX IX_dbt_trade_ticket_updated_at ON dbt_trade_ticket (updated_at desc);

ALTER INDEX IX_dbt_trade_ticket_updated_at ON dbt_trade_ticket REBUILD;

CREATE INDEX IX_dbt_trade_ticket_vtt ON dbt_trade_ticket (belong_customer_id);

ALTER INDEX IX_dbt_trade_ticket_vtt ON dbt_trade_ticket REBUILD;

--contract_sign

CREATE INDEX IX_dbt_contract_sign_tt_id ON dbt_contract_sign (tt_id desc);

ALTER INDEX IX_dbt_contract_sign_tt_id ON dbt_contract_sign REBUILD;

--contract_sign query

CREATE INDEX IX_dbt_contract_sign_updated_at ON dbt_contract_sign (updated_at desc);

ALTER INDEX IX_dbt_contract_sign_updated_at ON dbt_contract_sign REBUILD;

--contract query

CREATE INDEX IX_dbt_contract_updated_at ON dbt_contract (updated_at desc);

ALTER INDEX IX_dbt_contract_updated_at ON dbt_contract REBUILD;