INSERT INTO [dbo].[dba_menu]([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES (NULL, N'N072', N'站内信', 1, 90, N'/message/mailList', 1, 0, '2023-01-04 00:00:00.000', '2023-01-04 00:00:00.000', 0, 1, 0, N'N031');


INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (415, NULL, NULL, N'消息中心', NULL, 0, 0, 1, 0, '2023-01-11 03:10:17.850', '2023-01-11 03:10:17.850');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (416, NULL, NULL, N'消息列表', NULL, 415, 1, 1, 0, '2023-01-11 03:15:54.080', '2023-01-11 03:15:54.080');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (417, N'TY_043', N'TY_043', N'编辑消息模板', NULL, 416, 2, 1, 0, '2023-01-11 03:16:02.187', '2023-01-11 03:16:02.187');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (418, N'TY_044', N'TY_044', N'启用/禁用消息模板 ', NULL, 416, 2, 1, 0, '2023-01-11 03:24:04.120', '2023-01-11 03:24:04.120');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (419, NULL, NULL, N'业务配置-LOA阈值配置', NULL, 1, 1, 1, 0, '2023-01-11 03:27:44.167', '2023-01-11 03:27:44.167');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (420, N'SBM_S_082', N'SBM_S_082', N'编辑', NULL, 419, 2, 1, 0, '2023-01-11 03:27:49.260', '2023-01-11 03:27:49.260');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (421, NULL, NULL, N'业务配置-LOA阈值配置', NULL, 3, 1, 1, 0, '2023-01-11 03:28:56.387', '2023-01-11 03:28:56.387');
INSERT INTO [dbo].[dba_power]([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (422, N'SBO_S_082', N'SBO_S_082', N'编辑', NULL, 421, 2, 1, 0, '2023-01-11 03:28:59.647', '2023-01-11 03:28:59.647');





update  [dbo].[dbz_system_rule_item] set code ='豆粕总金额下限阈值' where rule_key = 'MIN_AMOUNT' and rule_id =96;
update  [dbo].[dbz_system_rule_item] set code ='豆粕总金额上限阈值' where rule_key = 'MAX_AMOUNT'and rule_id =96;
update  [dbo].[dbz_system_rule_item] set code ='豆粕交期阈值' where rule_key = 'DELIVERY_DUE_MONTH'and rule_id =96;
update  [dbo].[dbz_system_rule_item] set code ='豆粕可提量阈值' where rule_key = 'REMAIN_CONTRACT_NUMBER'and rule_id =96;
update  [dbo].[dbz_system_rule_item] set code ='豆油总金额下限阈值' where rule_key = 'MIN_AMOUNT' and rule_id =97;
update  [dbo].[dbz_system_rule_item] set code ='豆油总金额上限阈值' where rule_key = 'MAX_AMOUNT'and rule_id =97;
update  [dbo].[dbz_system_rule_item] set code ='豆油交期阈值' where rule_key = 'DELIVERY_DUE_MONTH'and rule_id =97;
update  [dbo].[dbz_system_rule_item] set code ='豆油可提量阈值' where rule_key = 'REMAIN_CONTRACT_NUMBER'and rule_id =97;




INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'-1', N'Columbus用户重置密码', N'email', N'【LDC客户端】密码重置', N'您在LDC Columbus系统的密码已重置为${password!}，请重新登陆系统并修改密码，谢谢', N'normal', NULL, '2022-11-11 06:41:45.487', NULL, '2022-11-11 06:41:45.487', NULL, N'CUSTOMER_COLUMBUS_RESET_PWD_MAIL');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'-1', N'Columbus用户重置密码', N'sms', N'【LDC客户端】密码重置', N'您在LDC Columbus系统的密码已重置为${password!}，请重新登陆系统并修改密码，谢谢', N'normal', NULL, '2022-11-11 06:41:45.487', NULL, '2022-11-11 06:41:45.487', NULL, N'CUSTOMER_COLUMBUS_RESET_PWD_SMS');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'审批任务提醒', N'inmail', N'合同${contractCode!}_【${nodeName!}】审批提醒', N'合同${contractCode!} 【${nodeName!}】待审批，请尽快处理。', N'normal', NULL, '2022-11-29 02:42:36.560', NULL, '2022-11-29 02:42:36.560', NULL, N'APPROVE_NOTICE');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'审批任务提醒', N'email', N'合同${contractCode!}_【${nodeName!}】审批提醒', N'合同${contractCode!} 【${nodeName!}】待审批，请尽快处理。', N'normal', NULL, '2022-11-29 02:47:57.587', NULL, '2022-11-29 02:47:57.587', NULL, N'APPROVE_NOTICE');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'节点审批通过CC', N'inmail', N'合同${contractCode!}_【${nodeName!}】已审批通过。', N'合同${contractCode!} 【${nodeName!}】已审批通过，请知悉。', N'normal', NULL, '2022-11-29 02:48:38.460', NULL, '2022-11-29 02:48:38.460', NULL, N'APPROVING_FOLLOW');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'节点审批通过CC', N'email', N'合同${contractCode!}_【${nodeName!}】已审批通过。', N'合同${contractCode!} 【${nodeName!}】已审批通过，请知悉。', N'normal', NULL, '2022-11-29 02:48:49.287', NULL, '2022-11-29 02:48:49.287', NULL, N'APPROVING_FOLLOW');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'流程审批通过CC', N'inmail', N'合同${contractCode!}已审批通过。', N'合同${contractCode!}已审批通过，请知悉。
', N'normal', NULL, '2022-11-29 02:51:13.527', NULL, '2022-11-29 02:51:13.527', NULL, N'APPROVED_FOLLOW');
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code]) VALUES (N'', N'流程审批通过CC', N'email', N'合同${contractCode!}已审批通过。1', N'合同${contractCode!}已审批通过，请知悉。', N'normal', NULL, '2022-11-29 02:51:14.127', NULL, '2022-11-29 02:51:14.127', NULL, N'APPROVED_FOLLOW');



INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'CUSTOMER_COLUMBUS_RESET_PWD', N'<EMAIL>', N'email_list', N'email', N'NOTICE', 4, N'', N'', N'personal', N'', '2022-11-11 06:40:50.883', '2022-11-11 06:40:50.883', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'CUSTOMER_COLUMBUS_RESET_PWD', NULL, NULL, N'sms', N'NOTICE', 5, N'', N'', N'personal', N'', '2022-11-11 06:40:50.883', '2022-11-11 06:40:50.883', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_TASK_APPROVE_NOTICE', NULL, NULL, N'inmail', N'NOTICE', 6, N'system', NULL, N'personal', N'', '2022-11-29 02:53:40.413', '2022-11-29 02:53:40.413', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_TASK_APPROVE_NOTICE', N'', NULL, N'email', N'NOTICE', 7, N'', N'', N'personal', N'', '2022-11-29 02:57:20.747', '2022-11-29 02:57:20.747', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_TASK_APPROVED_FOLLOW', N'', NULL, N'inmail', N'NOTICE', 8, N'system', NULL, N'personal', N'', '2022-11-29 03:04:56.397', '2022-11-29 03:04:56.397', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_TASK_APPROVED_FOLLOW1', N'', N'email_list', N'email', N'NOTICE', 9, N'', N'', N'personal', N'', '2022-11-29 03:05:08.853', '2022-11-29 03:05:08.853', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_PROCINST_APPROVED_FOLLOW', N'', NULL, N'inmail', N'NOTICE', 10, N'system', NULL, N'personal', N'', '2022-11-29 03:06:59.937', '2022-11-29 03:06:59.937', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'LDC_PROCINST_APPROVED_FOLLOW', N'', N'email_list', N'email', N'NOTICE', 11, N'', N'', N'personal', N'', '2022-11-29 03:07:00.470', '2022-11-29 03:07:00.470', NULL, NULL, 1, 1, 1, 0);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'CLB_USER_MANUAL_NOTICE', N'', N'', N'inmail', N'NOTICE', 0, N'', N'', N'personal', N'', '2022-12-01 10:12:29.277', '2022-12-01 10:12:29.277', NULL, NULL, 1, 1, 1, 1);
INSERT INTO [dbo].[dbm_business_template] ([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted]) VALUES (N'CLB_USER_MANUAL_NOTICE', N'', N'', N'email', N'NOTICE', 0, N'', N'', N'personal', N'', '2022-12-01 10:12:30.480', '2022-12-01 10:12:30.480', NULL, NULL, 1, 1, 1, 1);
