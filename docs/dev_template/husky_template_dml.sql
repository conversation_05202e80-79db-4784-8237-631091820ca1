/*
 Navicat Premium Data Transfer

 Source Server         : Ldc_test
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 10/10/2023 11:26:27
*/


-- ----------------------------
-- Table structure for dbh_quality
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_quality]') AND type IN ('U'))
	DROP TABLE [dbo].[dbh_quality]
GO

CREATE TABLE [dbo].[dbh_quality] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [goods_category_id] int DEFAULT 0 NULL,
  [factory_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [warehouse_ids] text COLLATE Chinese_PRC_CI_AS  NULL,
  [usage] int  NULL,
  [spec_id] int  NULL,
  [spec_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [sales_type] int  NULL,
  [has_customer] int  NULL,
  [enterprise] int  NULL,
  [content] text COLLATE Chinese_PRC_CI_AS  NULL,
  [customer_ids] text COLLATE Chinese_PRC_CI_AS  NULL,
  [customer_names] text COLLATE Chinese_PRC_CI_AS  NULL,
  [status] int DEFAULT 1 NULL,
  [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbh_quality] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'goods_category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交货工厂
编码',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'factory_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点ID逗号隔开',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'warehouse_ids'
GO

EXEC sp_addextendedproperty
'MS_Description', N'用途',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'usage'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'spec_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格信息',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'spec_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型（1采购 2销售）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否有客户(0 无 1 有)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'has_customer'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否为集团客户  0:否 1:是',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'enterprise'
GO

EXEC sp_addextendedproperty
'MS_Description', N'质量指标条款内容',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'集团/专属客户ID逗号隔开',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'customer_ids'
GO

EXEC sp_addextendedproperty
'MS_Description', N'集团/专属客户名称逗号隔开',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'customer_names'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态（0禁用 1启用）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality',
'COLUMN', N'created_by'
GO


-- ----------------------------
-- Table structure for dbh_quality_rule
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_quality_rule]') AND type IN ('U'))
	DROP TABLE [dbo].[dbh_quality_rule]
GO

CREATE TABLE [dbo].[dbh_quality_rule] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [quality_id] int  NULL,
  [quality_rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [refer_id] int  NULL,
  [refer_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [refer_value] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_deleted] int DEFAULT 1 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbh_quality_rule] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'质量指标ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'quality_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'warehouse/customer/enterprise(VariableBizCodeEnum)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'quality_rule_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点/客户/集团客户ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'refer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点/客户/集团客户编码',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'refer_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点/客户/集团客户名称',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'refer_value'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态（0未删除 1删除）
',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发货库点/客户/集团客户编码',
'SCHEMA', N'dbo',
'TABLE', N'dbh_quality_rule',
'COLUMN', N'created_by'
GO


-- ----------------------------
-- Auto increment value for dbh_quality
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbh_quality]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbh_quality
-- ----------------------------
ALTER TABLE [dbo].[dbh_quality] ADD CONSTRAINT [PK__dbh_qual__3213E83F93EE9AFF] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO


-- ----------------------------
-- Auto increment value for dbh_quality_rule
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbh_quality_rule]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbh_quality_rule
-- ----------------------------
ALTER TABLE [dbo].[dbh_quality_rule] ADD CONSTRAINT [PK__dbh_qual__3213E83FAEE2785D] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

/*
 Navicat Premium Data Transfer

 Source Server         : Ldc_test
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 11/10/2023 10:45:39
*/


-- ----------------------------
-- Table structure for dbh_template_history_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_template_history_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dbh_template_history_log]
GO

CREATE TABLE [dbo].[dbh_template_history_log] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [refer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [refer_type] int  NULL,
  [refer_id] int  NULL,
  [operation_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [template_group_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [content] text COLLATE Chinese_PRC_CI_AS  NULL,
  [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
  [rule_json] text COLLATE Chinese_PRC_CI_AS  NULL,
  [rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [bu_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [company_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category_id] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [sales_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [protocol_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [contract_action_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [customer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [title] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [need_num] int  NULL,
  [sort] int  NULL,
  [can_modify] int  NULL,
  [layout] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS  NULL,
  [version] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [status] int DEFAULT 1 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [customer_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [bind_relation_info] text COLLATE Chinese_PRC_CI_AS  NULL,
  [condition_variable_info] text COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbh_template_history_log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板编号',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'refer_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条款类型（1 条款 2 条款组）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'refer_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板/条款组/条款ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'refer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型（1新增 2修改）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'operation_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属条款组编号',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'template_group_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条款内容',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则drools脚本内容',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'rule_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则信息',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'rule_json'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规则编号',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'rule_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务线（现货ST、期货FT）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'bu_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属主体（TJIB/FLIB）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'company_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务品类(11 豆粕M;12 豆油O)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型(1 采购P；2 销售S)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'protocol_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'contract_action_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属客户（空则为通用 非空为某个客户）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'customer_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'标题',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'title'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否需要序号(0不需要 1需要序号)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'need_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'顺序',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'sort'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否可修改（0不可修改 1可以修改）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'can_modify'
GO

EXEC sp_addextendedproperty
'MS_Description', N'布局格式',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'layout'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'版本号（时间戳格式：yyyyMMddHHmmss）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'version'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态（0禁用 1启用）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'客户名称',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'customer_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板绑定信息',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'bind_relation_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'条件变量信息',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_history_log',
'COLUMN', N'condition_variable_info'
GO


-- ----------------------------
-- Table structure for dbh_template_load
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_template_load]') AND type IN ('U'))
	DROP TABLE [dbo].[dbh_template_load]
GO

CREATE TABLE [dbo].[dbh_template_load] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [type] tinyint  NULL,
  [contract_sign_id] int  NULL,
  [tt_id] int  NULL,
  [tt_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [contract_id] int  NULL,
  [contract_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [content] text COLLATE Chinese_PRC_CI_AS  NULL,
  [biz_data] text COLLATE Chinese_PRC_CI_AS  NULL,
  [template_id] int  NULL,
  [template_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [template_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [bu_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [company_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category_id] int  NULL,
  [sales_type] int  NULL,
  [protocol_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [contract_action_type] int  NULL,
  [customer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [error_info] text COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [updated_at] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[dbh_template_load] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板自增ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'加载类型（1、条款组规则过滤 2、条款规则过滤 3、出具信息组装渲染  4、用户提交 5、提交组装）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'协议ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'contract_sign_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TT的ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'tt_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TT编号',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'tt_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同号',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'处理的内容',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'content'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务数据',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'biz_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板ID',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'template_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板名称',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'template_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'模板名称',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'template_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务线（现货ST、期货FT）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'bu_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属主体（TJIB/FLIB）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'company_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务品类(11 豆粕M;12 豆油O)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型(1 采购P；2 销售S)',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'protocol_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'contract_action_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'所属客户（空则为通用 非空为某个客户）',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'customer_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'异常信息',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'error_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbh_template_load',
'COLUMN', N'memo'
GO


-- ----------------------------
-- Auto increment value for dbh_template_history_log
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbh_template_history_log]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbh_template_history_log
-- ----------------------------
ALTER TABLE [dbo].[dbh_template_history_log] ADD CONSTRAINT [PK__dbh_temp__3213E83F836D0D8A] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO


-- ----------------------------
-- Auto increment value for dbh_template_load
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbh_template_load]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbh_template_load
-- ----------------------------
ALTER TABLE [dbo].[dbh_template_load] ADD CONSTRAINT [PK__dbh_temp__B9F7A638A2FA0DB8] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO



    /*
     Navicat Premium Data Transfer

     Source Server         : Ldc_test
     Source Server Type    : SQL Server
     Source Server Version : 14003456
     Source Host           : ************:1433
     Source Catalog        : ldc_navigator_test
     Source Schema         : dbo

     Target Server Type    : SQL Server
     Target Server Version : 14003456
     File Encoding         : 65001

     Date: 13/10/2023 18:16:08
    */


-- ----------------------------
-- Table structure for dbh_a_modify_matrix
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_a_modify_matrix]') AND type IN ('U'))
DROP TABLE [dbo].[dbh_a_modify_matrix]
    GO

CREATE TABLE [dbo].[dbh_a_modify_matrix] (
    [id] int  NOT NULL,
    [tcode] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [mcode] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rcode] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [cat] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [act] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [a] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [b] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [c] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [d] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [e] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [f] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [g] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [h] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [i] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [j] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [k] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [l] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [m] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [n] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [o] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [p] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [q] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [r] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [s] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [t] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [u] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [v] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [w] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [x] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [y] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [content] varchar(2000) COLLATE Chinese_PRC_CI_AS  NULL,
    [ruleinfo] varchar(2000) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbh_a_modify_matrix] SET (LOCK_ESCALATION = TABLE)
    GO


-- ----------------------------
-- Primary Key structure for table dbh_a_modify_matrix
-- ----------------------------
ALTER TABLE [dbo].[dbh_a_modify_matrix] ADD CONSTRAINT [PK__dbh_a__3213E83FD6687742] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    /*
     Navicat Premium Data Transfer

     Source Server         : Ldc_test
     Source Server Type    : SQL Server
     Source Server Version : 14003456
     Source Host           : ************:1433
     Source Catalog        : ldc_navigator_test
     Source Schema         : dbo

     Target Server Type    : SQL Server
     Target Server Version : 14003456
     File Encoding         : 65001

     Date: 13/10/2023 18:16:37
    */


-- ----------------------------
-- Table structure for dbh_check_base_tme
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_check_base_tme]') AND type IN ('U'))
DROP TABLE [dbo].[dbh_check_base_tme]
    GO

CREATE TABLE [dbo].[dbh_check_base_tme] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [t] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [g] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rg] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [srct] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [ecount] int  NULL,
    [modify] int  NULL,
    [gtype] int  NULL,
    [gsort] int  NULL,
    [gno] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [tname] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [gname] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [action] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbh_check_base_tme] SET (LOCK_ESCALATION = TABLE)
    GO


    -- ----------------------------
-- Auto increment value for dbh_check_base_tme
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbh_check_base_tme]', RESEED, 1)
    GO


-- ----------------------------
-- Primary Key structure for table dbh_check_base_tme
-- ----------------------------
ALTER TABLE [dbo].[dbh_check_base_tme] ADD CONSTRAINT [PK__dbh_chec__3213E83F075E63AB] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    /*
     Navicat Premium Data Transfer

     Source Server         : Ldc_test
     Source Server Type    : SQL Server
     Source Server Version : 14003456
     Source Host           : ************:1433
     Source Catalog        : ldc_navigator_test
     Source Schema         : dbo

     Target Server Type    : SQL Server
     Target Server Version : 14003456
     File Encoding         : 65001

     Date: 13/10/2023 18:23:59
    */


-- ----------------------------
-- Table structure for dbh_variable
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbh_variable]') AND type IN ('U'))
DROP TABLE [dbo].[dbh_variable]
    GO

CREATE TABLE [dbo].[dbh_variable] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [display_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [value_type] tinyint DEFAULT 3 NULL,
    [is_condition] tinyint DEFAULT 0 NULL,
    [is_key] tinyint DEFAULT 0 NULL,
    [typical_value] varchar(1024) COLLATE Chinese_PRC_CI_AS  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] tinyint DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [has_dict] tinyint DEFAULT 0 NULL,
    [is_enum] tinyint DEFAULT 0 NULL,
    [is_logic] tinyint DEFAULT 0 NULL,
    [enum_path] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [pattern_relations] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [input_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [original_field] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbh_variable] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量英文名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'display_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字段类型（1整数、2字符串）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'value_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否条件变量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'is_condition'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否关键变量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'is_key'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'典型值(逗号隔开)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'typical_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0未删除 1已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否有字典值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'has_dict'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为枚举',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'is_enum'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为逻辑变量（比如option1、option2）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'is_logic'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'枚举取值路径',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'enum_path'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'运算关系逗号隔开(“==”、“!=”、“>”、“>=”、“<”、“<=”、“contains”)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'pattern_relations'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'输入框类型（select、input、boolean）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'input_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'原字段内容',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_variable',
    'COLUMN', N'original_field'
    GO


    -- ----------------------------
-- Records of dbh_variable
-- ----------------------------
    SET IDENTITY_INSERT [dbo].[dbh_variable] ON
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'1', N'buCode', N'业务线', N'业务线', N'2', N'0', N'1', N'45,454', N'期货/现货，现有合同相关业务均为现货', N'0', N'2023-07-19 06:50:37.673', N'2023-07-27 17:38:43.633', N'admin1', NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.BuCodeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'2', N'companyCode', N'所属主体', N'所属主体', N'2', N'1', N'1', N'TJIB,FL', N'签约主体', N'0', N'2023-07-19 06:52:14.303', N'2023-08-17 13:36:38.257', N'admin1', NULL, N'0', N'0', N'0', NULL, N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'3', N'categoryId', N'业务品类', N'业务品类', N'1', N'1', N'1', NULL, N'豆粕/豆油', N'0', N'2023-07-19 06:52:40.510', N'2023-07-19 06:52:40.510', NULL, NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.GoodsCategoryEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'4', N'salesType', N'采销类型', N'采销类型', N'1', N'1', N'1', NULL, N'1采购2销售', N'0', N'2023-07-19 06:53:04.147', N'2023-07-19 06:53:04.147', NULL, NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.ContractSalesTypeEnum', N'==,!=', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'5', N'protocolType', N'协议类型', N'协议类型', N'2', N'0', N'1', NULL, N'大合同/订单/补充协议', N'0', N'2023-07-19 06:53:50.907', N'2023-07-19 06:53:50.907', NULL, NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.ProtocolTypeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'6', N'contractActionType', N'操作类型', N'操作类型', N'1', N'1', N'1', NULL, N'TT中的操作类型', N'0', N'2023-07-19 06:54:17.100', N'2023-07-19 06:54:17.100', NULL, NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.ContractTradeTypeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'7', N'enterpriseCode', N'所属集团客户', N'所属集团客户', N'2', N'1', N'1', NULL, NULL, N'0', N'2023-07-19 06:54:51.760', N'2023-10-07 18:54:03.253', N'admin1', NULL, N'1', N'0', N'0', N'com.navigator.bisiness.enums.SpecialCustomerGroupEnum', N'==,!=', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'8', N'contractType', N'合同类型', N'合同类型', N'1', N'1', N'0', NULL, N'合同类型，如一口价/基差/暂定价/基差暂定价', N'0', N'2023-07-19 06:55:30.853', N'2023-07-19 06:55:30.853', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.ContractTypeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'9', N'deliveryType', N'提货类型', N'提货类型', N'1', N'1', N'0', NULL, N'自提/送货/转货权', N'0', N'2023-07-19 06:55:59.930', N'2023-07-19 06:55:59.930', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.DeliveryModeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'10', N'paymentType', N'付款方式', N'付款方式', N'1', N'1', N'0', NULL, N'赊销/预付', N'0', N'2023-07-19 06:58:00.697', N'2023-07-19 06:58:00.697', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.PaymentTypeEnum', N'==,!=', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'11', N'deliveryFactoryCode', N'交货工厂编码', N'交货工厂编码', N'2', N'1', N'0', NULL, NULL, N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'12', N'priceEndType', N'点价截止日期类型', N'点价截止日期类型', N'1', N'1', N'0', NULL, N'日期/文本', N'0', N'2023-07-19 06:59:15.780', N'2023-07-19 06:59:15.780', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum', N'==,!=', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'13', N'transferFactory', N'是否涉及转厂', N'是否涉及转厂', N'1', N'1', N'0', NULL, N'交货工厂是否变化', N'0', N'2023-07-19 07:00:01.333', N'2023-07-19 07:00:01.333', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.TransferFactoryEnum', N'==,!=', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'14', N'layOut', N'布局样式', N'布局样式', N'2', N'0', N'1', NULL, N'两种样式，新增/修改', N'0', N'2023-07-26 08:23:42.010', N'2023-07-26 08:23:42.010', NULL, NULL, N'1', N'0', N'0', NULL, NULL, N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'15', N'goodsCategorySymbol', N'品类简称(M/Y)', N'品类简称(M/Y)', N'2', N'0', N'0', NULL, N'品类期货前缀，如豆粕是M豆油是Y', N'0', N'2023-08-01 05:33:27.163', N'2023-08-01 05:33:27.163', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'vrjc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'16', N'goodsCategoryCode', N'品类全称(SBM/SBO)', N'品类全称(SBM/SBO)', N'2', N'0', N'0', NULL, N'如SBM/SBO', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'vrqc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'17', N'contractCode', N'合同编号', N'合同编号', N'2', N'0', N'0', NULL, N'Nav系统中的合同编号', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'no')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'18', N'contractTypeInfo', N'合同类型描述', N'合同类型描述', N'2', N'0', N'0', NULL, N'合同类型描述', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ty')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'19', N'signDate', N'签约日期（yyyy年MM月dd日）', N'签约日期（yyyy年MM月dd日）', N'2', N'0', N'0', NULL, N'签约日期（yyyy年MM月dd日）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'doc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'20', N'originalSignDate', N'原合同签约日期（yyyy年MM月dd日）', N'原合同签约日期（yyyy年MM月dd日）', N'2', N'0', N'0', NULL, N'原合同签约日期（yyyy年MM月dd日）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ydoc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'21', N'originalContractCode', N'父合同编号', N'父合同编号', N'2', N'0', N'0', NULL, N'父合同编号', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'noy')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'22', N'contractTitle', N'合同标题（销售/采购-合同/订单）', N'合同标题（销售/采购-合同/订单）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xhd或chd')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'23', N'goodsCategoryName', N'品类描述(豆粕/豆油)', N'品类描述(豆粕/豆油)', N'2', N'0', N'0', NULL, N'品类名称', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'vr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'24', N'deliveryTypeInfo', N'交提货方式描述（自提/配送/仓单）', N'交提货方式描述（自提/配送/仓单）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'dg')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'25', N'paymentTypeInfo', N'合同付款方式描述(赊销/预付款)', N'合同付款方式描述(赊销/预付款)', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'pm')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'26', N'symbolFactory', N'国标值/企标值', N'国标值/企标值', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'gqbz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'27', N'customerName', N'买方主体名称(客户)', N'客户主体名称(采购卖方销售买方)', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户的主体名称', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'na')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'28', N'customerContactName', N'买方收件人（客户）', N'客户客户收件人（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户的收件人信息', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'fox')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'29', N'customerContactAddresses', N'买方地址（客户）', N'客户地址（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户的地址', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ads')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'30', N'customerContactEmails', N'买方电子邮箱（客户）', N'客户电子邮箱（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户的邮箱', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ema')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'31', N'customerContactPhones', N'买方电话号码（客户）', N'客户电话号码（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户的联系电话', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mbo')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'32', N'customerBankName', N'买方开户行（客户）', N'客户开户行（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户银行开户行', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'khfk')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'33', N'customerBankAccountNo', N'买方银行账号（客户）', N'客户银行账号（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户开户银行', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zhfk')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'34', N'customerBankAccountName', N'买方开户名（客户）', N'客户开户名（采购卖方销售买方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为客户开户户名', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'khmc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'35', N'ldcName', N'卖方主体名称/开户名（LDC）', N'LDC主体名称/开户名（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的主体名称', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'me')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'36', N'ldcContactAddresses', N'卖方地址（LDC）', N'LDC地址（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的地址', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mads')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'37', N'ldcSignPlace', N'卖方主体所在地址简称-签订地（LDC）', N'LDC主体所在地址简称-签订地（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的主体所在地简称', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ad')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'38', N'ldcContactNames', N'卖方收件人（LDC）', N'LDC收件人（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的收件人信息', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mfox')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'39', N'ldcContactEmails', N'卖方电子邮箱（LDC）', N'LDC电子邮箱（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的邮箱', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mema')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'40', N'ldcContactPhones', N'卖方电话号码（LDC）', N'LDC电话号码（LDC采购买方销售卖方）', N'2', N'0', N'0', NULL, N'无论采销，这个值均为LDC的联系电话', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mmbo')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'41', N'ldcAddress', N'采购合同.ldc买方地址', N'采购合同.ldc买方地址', N'2', N'0', N'0', NULL, N'采购合同.ldc买方地址', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'bscd')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'42', N'supplierAddress', N'采购合同.卖方供应商地址', N'采购合同.卖方供应商地址', N'2', N'0', N'0', NULL, N'采购合同.卖方供应商地址', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'sscd')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'43', N'contractNum', N'合同数量', N'合同数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'44', N'sourceContractNum', N'原合同数量', N'原合同数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'cfsl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'45', N'originalContractNum', N'原合同剩余数量(合同总量-拆分数量)', N'原合同剩余数量(合同总量-拆分数量)', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'sysl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'46', N'weightTolerance', N'溢短装(格式：5%)', N'溢短装(格式：5%)', N'2', N'0', N'0', NULL, N'溢短装，带%', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'os')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'47', N'originalWeightTolerance', N'原合同溢短装', N'原合同溢短装(格式：5%)', N'2', N'0', N'0', NULL, N'溢短装，带%', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yos')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'48', N'goodsSpecId', N'蛋白含量', N'蛋白含量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'eg')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'49', N'packageWeight', N'包装计算重量(袋皮扣重/包装)', N'包装计算重量(袋皮扣重/包装)', N'2', N'0', N'0', NULL, N'包装及扣重信息描述', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ag')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'51', N'originalPackageWeight', N'原包装计算重量(袋皮扣重/包装)', N'原包装计算重量(袋皮扣重/包装)', N'2', N'0', N'0', NULL, N'包装及扣重信息描述', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yag')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'52', N'totalAmount', N'含税总金额', N'含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'prt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'53', N'noTaxTotalAmount', N'不含税总金额', N'不含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'nprt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'54', N'addedTaxTotalAmount', N'增值税总金额', N'增值税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'sz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'55', N'unitPriceDetail', N'含税单价明细', N'含税单价明细', N'2', N'0', N'0', NULL, N'不包含基差基准信息，基差合同的格式为“+期权费20元/吨+商务补贴10元/吨+手续费1元/吨”;非基差合同的格式为“；单价包含期权费10元/吨,运费30元/吨”。可为空。', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'prx')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'56', N'unitPriceDiffDetail', N'含税单价明细(含基差价)', N'含税单价明细(含基差价)', N'2', N'0', N'0', NULL, N'包含基差基准价信息，基差合同的格式为“+基准基差10元/吨+手续费1元/吨”;非基差合同的格式为“+基准基差17元/吨+期权费20元/吨+商务补贴10元/吨+手续费1元/吨”。可为空。', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'prxy')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'57', N'unitPrice', N'含税单价价格', N'含税单价价格', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'pr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'58', N'originalUnitPrice', N'原合同.含税单价', N'原合同.含税单价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ypr或xpr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'59', N'originalTTUnitPrice', N'修改前含税单价价格', N'修改前含税单价价格', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xpr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'60', N'extraPrice', N'含税单价.基差价', N'含税单价.基差价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jcj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'61', N'thisTimeFee', N'含税单价.手续费', N'含税单价.手续费', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'sxf')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'62', N'transportPrice', N'含税单价.运费', N'含税单价.运费', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yf')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'63', N'delayPrice', N'含税单价.滞期费', N'含税单价.滞期费', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zqf')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'64', N'complaintDiscountPrice', N'含税单价.客诉折价', N'含税单价.客诉折价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'kszj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'65', N'businessPrice', N'含税单价.商务补贴', N'含税单价.商务补贴', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'sxlx')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'66', N'delayAmount', N'滞期费总金额', N'滞期费总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zqfz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'67', N'complaintDiscountAmount', N'客诉折价总金额', N'客诉折价总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'kszjz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'68', N'originalTransportPrice', N'原合同.含税单价.运费', N'原合同.含税单价.运费', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yyf')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'69', N'warehouseAddress', N'发货库点.地址', N'发货库点.地址', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ds')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'70', N'warehouseDeliveryPoint', N'发货库点.交货地点', N'发货库点.交货地点', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'dd')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'71', N'deliveryTime', N'交货周期（开始交货日至截止交货日）', N'交货周期（开始交货日至截止交货日）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'po')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'72', N'destination', N'目的港(拼"送到"+（指定地点）)', N'目的港(拼"送到"+（指定地点）)', N'2', N'0', N'0', NULL, N'已经拼了"送到"+（指定地点）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'py')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'73', N'originalDestination', N'目的港', N'目的港', N'2', N'0', N'0', NULL, N'已经拼了"送到"+（指定地点）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'pye')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'74', N'deliveryFactoryName', N'交货工厂名称', N'交货工厂名称', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jhgc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'75', N'originalDeliveryFactoryName', N'原交货工厂名称', N'原交货工厂名称', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yjhgc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'76', N'weightCheck', N'重量验收/重量检验协议文本', N'重量验收/重量检验协议文本', N'2', N'0', N'0', NULL, N'系统配置的重量/质量检验文本', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'pe')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'77', N'creditDays', N'账期', N'账期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mes')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'78', N'ldcBankName', N'LDC开户行', N'LDC开户行', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'kh')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'79', N'ldcBankAccountNo', N'LDC银行账号', N'LDC银行账号', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zh')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'80', N'depositPayEndDay', N'付款截止日期(合同.签订日期+1个自然日)', N'付款截止日期(合同.签订日期+1个自然日)', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jzfk')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'81', N'depositRateInfo', N'保证金比例(x%)', N'保证金比例(x%)', N'2', N'0', N'0', NULL, N'保证金比例(x%)', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'mr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'82', N'addedDepositRateInfo', N'点价后补缴比例(x%)', N'点价后补缴比例(x%)', N'2', N'0', N'0', NULL, N'点价后补缴比例(x%)', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'dmr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'83', N'jointAddedDepositRate', N'保证金追加比例（区分品类、一口价基差,5%<履约保证金比例≤10%，显示7%）', N'规则_保证金追加比例（区分品类、一口价基差）', N'2', N'0', N'0', NULL, N'保证金追加比例（区分品类、一口价基差）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'bzjzj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'84', N'domainCodeInfo', N'期货合约（yyyy年MM月）', N'期货合约（yyyy年MM月）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'hy')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'85', N'domainCode', N'期货合约(例：yyMM)', N'期货合约(例：yyMM)', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'hyj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'86', N'originalDomainCode', N'原期货合约（yyyy年MM月）', N'原期货合约（yyyy年MM月）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'yhy')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'87', N'priceEndTime', N'点价截止日期（区分文本/日期yyyy年MM月dd日）', N'点价/作价截止日期（区分文本/日期yyyy年MM月dd日）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'88', N'protocolStartDate', N'框架合同签约日期', N'框架合同签约日期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'kjr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'89', N'protocolNo', N'框架合同号', N'框架合同号', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'kjh')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'90', N'signProtocolCode', N'协议编号', N'协议编号', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xyb')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'91', N'tradeTicketTime', N'TT.创建日期', N'TT.创建日期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ttxr')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'92', N'originalPriceNum', N'合同定价TT.定价数量', N'合同定价TT.定价数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'htdj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'93', N'priceApplyTime', N'合同定价TT.点价日期', N'合同定价TT.点价日期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djs')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'94', N'transactionPrice', N'定价单.价格', N'定价单.价格', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djjg')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'95', N'priceOrderTaxAmount', N'定价单.含税总金额', N'定价单.含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djprt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'96', N'priceOrderNoTaxAmount', N'定价单.不含税总金额', N'定价单.不含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djnprt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'97', N'priceOrderAddedTaxAmount', N'定价单.增值税总金额', N'定价单.增值税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'djsz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'98', N'weightedAverageUnitPrice', N'定价单.加权平均价', N'定价单.加权平均价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ldjjg')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'99', N'weightedAverageTaxPrice', N'定价单.加权平均含税价', N'定价单.加权平均含税价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ldjjghs')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'100', N'weightedAverageTaxAmount', N'定价单.加权含税总金额', N'定价单.加权含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'lprt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'101', N'weightedAverageNoTaxAmount', N'定价单.加权不含税总金额', N'定价单.加权不含税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'lnprt')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'102', N'weightedAverageAddedTaxAmount', N'定价单.加权增值税总金额', N'定价单.加权增值税总金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'lsz')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'103', N'remainPriceNum', N'合同未定价量', N'合同未定价量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'wdjl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'105', N'transferNum', N'转月数量', N'转月数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zysl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'106', N'transferMonthTime', N'转月时间', N'转月时间', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jzlyfk')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'107', N'transferTTPrice', N'转月价差', N'转月价差', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'zyjc')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'108', N'remainBillNum', N'未开单量', N'未开单量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'wkdl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'109', N'remainDeliveryNum', N'未提货量', N'未提货量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'wthl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'110', N'washoutNum', N'解约定赔数量', N'解约定赔数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xdsl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'111', N'washoutPrice', N'解约定赔市场价', N'解约定赔市场价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xdscj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'112', N'washoutDiffPrice', N'解约定赔总差价', N'解约定赔总差价', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xdcj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'113', N'washoutDiffAmount', N'解约定赔差价总额', N'解约定赔差价总额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'xdze')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'114', N'structurePriceTotalNum', N'结构化定价总数量', N'结构化定价总数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghzsl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'115', N'structurePriceStartTime', N'结构化定价开始日期', N'结构化定价开始日期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghkrq')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'116', N'structurePriceUnitNum', N'结构化定价.单位量', N'结构化定价.单位量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghdwl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'117', N'structurePriceEndTime', N'结构化定价.结束日期', N'结构化定价.结束日期', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghjrq')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'118', N'structureTriggerPrice', N'结构化定价.触发价格', N'结构化定价.触发价格', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghcf')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'119', N'structureCumulativePrice', N'结构化定价.累积价格', N'结构化定价.累积价格', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghlj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'120', N'structureUnitNum', N'结构化定价.单位数量', N'结构化定价.单位数量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghdwsl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'121', N'structureUnitIncrement', N'结构化定价.单位量增量', N'结构化定价.单位量增量', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghdwzl')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'122', N'structureCashReturn', N'结构化定价.单吨现金返还金额', N'结构化定价.单吨现金返还金额', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'jghddxjfh')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'124', N'depositText', N'履约保证金文本.特殊处理（当日卖方基差报价150/300元以上）', N'规则_履约保证金文本.特殊处理（当日卖方基差报价150/300元以上）', N'2', N'0', N'0', NULL, N'', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'lytext')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'125', N'qrCodeImage', N'二维码', N'二维码', N'2', N'0', N'0', NULL, N'二维码URL', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'ewm')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'126', N'barCodeImage', N'条形码', N'条形码', N'2', N'0', N'0', NULL, N'条形码URL', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'txm')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'127', N'depositAmount', N'履约保证金比例', N'履约保证金比例', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,>,>=,<,<=', N'input', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'128', N'addedDepositRate', N'点价后履约保证金补缴', N'点价后履约保证金补缴', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,>,>=,<,<=', N'input', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'129', N'splitType', N'拆分类型', N'拆分类型', N'1', N'1', N'0', NULL, N'1部分拆分/2全部拆分', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.SplitTypeEnum', N'==,!=', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'131', N'specId', N'货品规格', N'货品规格', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,contains,not contains', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'132', N'structureType', N'结构化定价类型', N'结构化定价类型', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,contains,not contains', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'133', N'washOutDiffUnitPriceType', N'解约定赔.付款方式', N'解约定赔.付款方式', N'1', N'1', N'0', NULL, N'LDC打款/客户打款/互补打款', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'0', N'1', N'0', N'com.navigator.trade.pojo.enums.CompareUnitPriceEnum', N'==,!=', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'134', N'signType', N'合同终止方式', N'合同终止方式', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'135', N'notPriceNum', N'未定价量', N'未定价量', N'1', N'1', N'0', NULL, N'', N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'1', N'0', N'0', NULL, N'==,!=,>,>=,<,<=', N'input', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'136', N'depositUseRuleName', N'保证金使用规则描述(（option1/option2文本)', N'规则_保证金使用规则描述(（option1/option2文本)', N'2', N'0', N'0', NULL, N'新增字段（option1、option2,保证金释放方式）', N'0', N'2023-08-01 05:34:22.877', N'2023-08-01 05:34:22.877', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'新增字段（option1、option2,保证金释放方式）')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'137', N'modifyList', N'合同修改字段', N'合同修改字段', N'2', N'1', N'0', NULL, NULL, N'0', N'2023-07-19 06:58:50.970', N'2023-07-19 06:58:50.970', NULL, NULL, N'0', N'1', N'0', N'com.navigator.husky.pojo.enums.TemplateFieldModifyEnum', N'contains', N'select', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'138', N'aboveDeadlineInfo', N'以上期限条款文本(交货截止日期小于合同签约日期）', N'规则_以上期限条款文本(交货截止日期小于合同签约日期）', N'2', N'0', N'0', NULL, N'新增字段', N'0', N'2023-08-29 05:49:10.833', N'2023-08-29 05:49:10.833', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'新增字段')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'141', N'priceDeadlineText', N'基差暂定价和暂定价-作价期限', N'规则_基差暂定价和暂定价-点价/作价期限描述', N'2', N'0', N'0', NULL, N'新增字段（option1、option2，点价截止日期类型）', N'0', N'2023-08-31 09:00:11.250', N'2023-08-31 09:00:11.250', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'新增字段（option1、option2，点价截止日期类型）')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'142', N'invoicePaymentRateInfo', N'发票后补缴货款比例取值', N'发票后补缴货款比例取值(如15%)', N'2', N'0', N'0', NULL, N'票后付款比例描述，例如15%', N'0', N'2023-09-07 06:18:09.480', N'2023-09-07 06:18:09.480', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'新增字段(15,85)')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'143', N'invoicePaymentRateInTurnInfo', N'货款比例(100-补缴)', N'货款比例(100-补缴比例，如85%)', N'2', N'0', N'0', NULL, N'票款比例描述，例如85%', N'0', N'2023-09-07 06:19:18.387', N'2023-09-07 06:19:18.387', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'新增字段(100-发票后补缴货款比例)')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'144', N'qualityInfo', N'质量指标条款', N'质量指标条款', N'2', N'0', N'0', NULL, N'系统配置的货物质量指标文本', N'0', N'2023-09-07 06:19:47.017', N'2023-09-07 06:19:47.017', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'新增字段(质量指标条款)')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'145', N'categoryDomainCode', N'期货合约(例：M2309/Y2309)', N'期货合约(例：M2309/Y2309)', N'2', N'0', N'0', NULL, N'如M2309/Y2309', N'0', N'2023-09-12 08:13:39.953', N'2023-09-12 08:13:39.953', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, N'vrjc+hyj')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'148', N'invoicePaymentRate', N'发票后补缴货款比例', N'发票后补缴货款比例(如15)', N'1', N'1', N'0', NULL, N'发票后补缴货款比例(如15)', N'0', N'2023-09-13 05:30:47.843', N'2023-09-13 05:30:47.843', NULL, NULL, N'0', N'0', N'0', NULL, N'==,!=,>,>=,<,<=', N'input', NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'149', N'logo', N'公司主体-LOGO', N'公司主体-LOGO', N'2', N'0', N'0', NULL, N'LDC公司主体LogoURL', N'0', N'2023-09-14 10:22:50.427', N'2023-09-14 10:22:50.427', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'151', N'protocolTypeCondition', N'协议类型条件', N'协议类型条件', N'2', N'1', N'1', NULL, N'条件变量，协议类型', N'0', N'2023-07-19 06:53:50.907', N'2023-07-19 06:53:50.907', NULL, NULL, N'0', N'1', N'0', N'com.navigator.bisiness.enums.ProtocolTypeEnum', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'152', N'salesTypeInfo', N'采销描述(采购/销售)', N'采销描述(采购/销售)', N'2', N'0', N'0', NULL, NULL, N'0', N'2023-09-14 10:22:50.427', N'2023-09-14 10:22:50.427', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'153', N'protocolTypeInfo', N'协议类型描述(订单/合同)', N'协议类型描述(订单/合同)', N'2', N'0', N'0', NULL, NULL, N'0', N'2023-09-14 10:22:50.427', N'2023-09-14 10:22:50.427', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, NULL)
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'154', N'priceEndTimeContains', N'点价截止日期(含)', N'点价截止日期(含)', N'2', N'0', N'0', NULL, N'新增字段（点价截止日期，日期拼（含））', N'0', N'2023-09-20 01:56:55.470', N'2023-09-20 01:56:55.470', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'新增字段（点价截止日期，日期拼（含））')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'155', N'customerSplitName', N'新合同客户名称(拆分)', N'新合同客户名称(拆分)', N'2', N'0', N'0', NULL, N'新合同客户名称(拆分)', N'0', N'2023-09-20 05:56:31.240', N'2023-09-20 05:56:31.240', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'新合同客户名称(拆分)')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'156', N'totalAmountInfoModify', N'原合同_变更后_含税总金额', N'原合同_变更后_含税总金额', N'2', N'0', N'0', NULL, N'原合同_变更后_含税总金额', N'0', N'2023-09-20 05:56:31.240', N'2023-09-20 05:56:31.240', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'157', N'noTaxTotalAmountInfoModify', N'原合同_变更后_不含税总金额', N'原合同_变更后_不含税总金额', N'2', N'0', N'0', NULL, N'原合同_变更后_不含税总金额', N'0', N'2023-09-20 05:56:31.240', N'2023-09-20 05:56:31.240', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'158', N'addedTaxTotalAmountInfoModify', N'原合同_变更后_增值税总金额', N'原合同_变更后_增值税总金额', N'2', N'0', N'0', NULL, N'原合同_变更后_增值税总金额', N'0', N'2023-09-20 05:56:31.240', N'2023-09-20 05:56:31.240', NULL, NULL, N'0', N'0', N'1', NULL, NULL, NULL, N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'160', N'destinationCode', N'目的港/目的地', N'目的港/目的地', N'2', N'1', N'0', NULL, NULL, N'0', N'2023-07-19 06:54:51.760', N'2023-07-19 06:54:51.760', NULL, NULL, N'1', N'0', N'0', N'', N'==,!=,contains,not contains', N'select', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'161', N'isZDSpecialDeliveryCustomer', N'是否为正大集团特殊交货地点客户', N'是否为正大集团特殊交货地点客户', N'1', N'1', N'0', NULL, N'是否为正大集团特殊交货地点客户', N'0', N'2023-07-19 07:00:01.333', N'2023-07-19 07:00:01.333', NULL, NULL, N'0', N'0', N'0', N'', N'==', N'boolean', N'')
    GO

    INSERT INTO [dbo].[dbh_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [typical_value], [memo], [is_deleted], [created_at], [updated_at], [updated_by], [created_by], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [original_field]) VALUES (N'162', N'specialCustomerDeliveryInfo', N'正大集团-TJ-交货地点-备注', N'正大集团-TJ-交货地点-备注', N'2', N'0', N'0', NULL, N'正大集团-TJ-交货地点-备注', N'0', N'2023-09-14 10:22:50.427', N'2023-09-14 10:22:50.427', NULL, NULL, N'0', N'0', N'0', NULL, NULL, NULL, NULL)
    GO

    SET IDENTITY_INSERT [dbo].[dbh_variable] OFF
    GO


    -- ----------------------------
-- Auto increment value for dbh_variable
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbh_variable]', RESEED, 162)
    GO


-- ----------------------------
-- Uniques structure for table dbh_variable
-- ----------------------------
ALTER TABLE [dbo].[dbh_variable] ADD CONSTRAINT [unique_code] UNIQUE NONCLUSTERED ([code] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbh_variable] ADD CONSTRAINT [unique_name] UNIQUE NONCLUSTERED ([name] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO


-- ----------------------------
-- Primary Key structure for table dbh_variable
-- ----------------------------
ALTER TABLE [dbo].[dbh_variable] ADD CONSTRAINT [PK__dbh_vari__3213E83F5B15015D] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    /*
     Navicat Premium Data Transfer

     Source Server         : Ldc_test
     Source Server Type    : SQL Server
     Source Server Version : 14003456
     Source Host           : ************:1433
     Source Catalog        : ldc_navigator_test
     Source Schema         : dbo

     Target Server Type    : SQL Server
     Target Server Version : 14003456
     File Encoding         : 65001

     Date: 13/10/2023 18:24:34
    */


-- ----------------------------
-- Table structure for dbz_dict_item
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_dict_item]') AND type IN ('U'))
DROP TABLE [dbo].[dbz_dict_item]
    GO

CREATE TABLE [dbo].[dbz_dict_item] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [dict_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [dict_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_value] int  NULL,
    [item_description] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_sort] int  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbz_dict_item] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'dict_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'dict_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'item_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'item_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'item_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项展示信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'item_description'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'item_sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0未删除 1已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'is_deleted'
    GO


    -- ----------------------------
-- Records of dbz_dict_item
-- ----------------------------
    SET IDENTITY_INSERT [dbo].[dbz_dict_item] ON
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'1', N'contractType', N'合同类型', N'一口价', N'1', N'1', N'一口价合同', N'1', NULL, N'1', N'2023-07-20 01:54:54.503', N'2023-07-20 01:54:54.503')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'2', N'contractType', N'合同类型', N'基差', N'2', N'2', N'基差合同', N'2', NULL, N'1', N'2023-07-20 02:28:27.753', N'2023-07-20 02:28:27.753')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'3', N'contractType', N'合同类型', N'暂定价', N'3', N'3', N'暂定价合同', N'3', NULL, N'1', N'2023-07-20 02:29:06.123', N'2023-07-20 02:29:06.123')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'4', N'contractType', N'合同类型', N'基差暂定价', N'4', N'4', N'基差暂定价合同', N'4', NULL, N'1', N'2023-07-20 02:29:32.540', N'2023-07-20 02:29:32.540')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'5', N'contractType', N'合同类型', N'结构化定价', N'5', N'5', N'结构化定价价合同', N'5', NULL, N'1', N'2023-07-20 02:30:04.710', N'2023-07-20 02:30:04.710')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'6', N'deliveryType', N'提货类型', N'自提', N'1', N'1', N'自提', N'1', NULL, N'1', N'2023-07-20 02:30:55.297', N'2023-07-20 02:30:55.297')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'7', N'deliveryType', N'提货类型', N'配送', N'2', N'2', N'配送', N'2', NULL, N'1', N'2023-07-20 02:31:17.330', N'2023-07-20 02:31:17.330')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'8', N'deliveryType', N'提货类型', N'转货权', N'3', N'3', N'转货权', N'3', NULL, N'1', N'2023-07-20 02:31:55.940', N'2023-07-20 02:31:55.940')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'9', N'paymentType', N'付款方式', N'赊销', N'1', N'1', N'赊销', N'1', NULL, N'1', N'2023-07-20 04:12:29.343', N'2023-07-20 04:12:29.343')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'10', N'paymentType', N'付款方式', N'预付款', N'2', N'2', N'预付款', N'2', NULL, N'1', N'2023-07-20 04:13:06.677', N'2023-07-20 04:13:06.677')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'11', N'deliveryFactoryCode', N'交货工厂编码', N'路易达孚（张家港）饲料蛋白有限公司', N'ZJG', NULL, N'路易达孚（张家港）饲料蛋白有限公司', N'1', NULL, N'1', N'2023-07-21 05:16:30.317', N'2023-07-21 05:16:30.317')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'12', N'deliveryFactoryCode', N'交货工厂编码', N'路易达孚（天津）食品科技有限责任公司', N'TJ', NULL, N'路易达孚（天津）食品科技有限责任公司', N'2', NULL, N'1', N'2023-07-21 05:16:48.493', N'2023-07-21 05:16:48.493')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'13', N'deliveryFactoryCode', N'交货工厂编码', N'东莞路易达孚饲料蛋白有限公司', N'DG', NULL, N'东莞路易达孚饲料蛋白有限公司', N'3', NULL, N'1', N'2023-07-21 05:16:52.920', N'2023-07-21 05:16:52.920')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'14', N'deliveryFactoryCode', N'交货工厂编码', N'广州植之元油脂实业有限公司', N'ZZY', NULL, N'广州植之元油脂实业有限公司', N'4', NULL, N'1', N'2023-07-21 05:16:57.473', N'2023-07-21 05:16:57.473')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'15', N'deliveryFactoryCode', N'交货工厂编码', N'路易达孚（天津）国际贸易有限公司', N'TJIB', NULL, N'路易达孚（天津）国际贸易有限公司', N'5', NULL, N'1', N'2023-07-21 05:17:02.240', N'2023-07-21 05:17:02.240')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'16', N'deliveryFactoryCode', N'交货工厂编码', N'富凌', N'FL', NULL, N'富凌', N'6', NULL, N'1', N'2023-07-21 05:17:33.380', N'2023-07-21 05:17:33.380')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'18', N'layOut', N'布局样式', N'新增', N'1', N'1', N'新增', N'1', NULL, N'0', N'2023-07-26 08:24:35.470', N'2023-07-26 08:24:35.470')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'19', N'layOut', N'布局样式', N'修改', N'2', N'2', N'修改', N'2', NULL, N'0', N'2023-07-26 08:25:12.827', N'2023-07-26 08:25:12.827')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'20', N'layOut', N'布局样式', N'样式三', N'3', N'3', N'样式三', N'3', NULL, N'1', N'2023-07-26 08:25:35.113', N'2023-07-26 08:25:35.113')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'21', N'depositAmount', N'履约保证金金额', N'0', N'0', N'0', N'0', N'1', NULL, N'1', N'2023-08-11 01:20:21.090', N'2023-08-11 01:20:21.090')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'22', N'addedDepositRate', N'点价后履约保证金补缴', N'0', N'0', N'0', N'0', N'1', NULL, N'1', N'2023-08-11 01:20:21.090', N'2023-08-11 01:20:21.090')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'23', N'notPriceNum', N'未定价量', N'0', N'0', N'0', N'0', N'1', NULL, N'1', N'2023-08-11 01:20:21.090', N'2023-08-11 01:20:21.090')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'24', N'signType', N'合同终止方式', N'补充协议', N'0', N'0', N'补充协议', N'1', NULL, N'0', N'2023-08-11 09:03:30.260', N'2023-08-11 09:03:30.260')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'25', N'signType', N'合同终止方式', N'尾量协议', N'1', N'1', N'尾量协议', N'2', NULL, N'0', N'2023-08-11 09:03:59.410', N'2023-08-11 09:03:59.410')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'26', N'enterpriseCode', N'特殊集团客户', N'正大集团', N'ZDJT', NULL, N'正大集团', N'1', NULL, N'0', N'2023-09-23 03:52:48.363', N'2023-09-23 03:52:48.363')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'27', N'enterpriseCode', N'特殊集团客户', N'河南牧原粮食贸易有限公司', N'C_HNMY', NULL, N'河南牧原粮食贸易有限公司', N'3', NULL, N'0', N'2023-09-23 03:54:23.287', N'2023-09-23 03:54:23.287')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'28', N'enterpriseCode', N'特殊集团客户', N'象屿集团', N'C_XYJT', NULL, N'象屿集团', N'2', NULL, N'0', N'2023-09-23 03:54:23.983', N'2023-09-23 03:54:23.983')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'34', N'destinationCode', N'目的港/目的地', N'辽宁省营口港-牧原', N'SBM00394', N'274', N'辽宁省营口港-牧原', N'1', NULL, N'0', N'2023-09-25 08:34:32.890', N'2023-09-25 08:34:32.890')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'35', N'destinationCode', N'目的港/目的地', N'辽宁省锦州港-牧原', N'SBM00395', N'274', N'辽宁省锦州港-牧原', N'2', NULL, N'0', N'2023-09-25 08:37:01.690', N'2023-09-25 08:37:01.690')
    GO

    INSERT INTO [dbo].[dbz_dict_item] ([id], [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at]) VALUES (N'36', N'enterpriseCode', N'特殊集团客户', N'通威集团', N'C_TWJT', NULL, N'通威集团', N'4', NULL, N'0', N'2023-10-08 00:27:00.047', N'2023-10-08 00:27:00.047')
    GO

    SET IDENTITY_INSERT [dbo].[dbz_dict_item] OFF
    GO


    -- ----------------------------
-- Auto increment value for dbz_dict_item
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbz_dict_item]', RESEED, 36)
    GO


-- ----------------------------
-- Primary Key structure for table dbz_dict_item
-- ----------------------------
ALTER TABLE [dbo].[dbz_dict_item] ADD CONSTRAINT [PK__dbz_dict__3213E83FE6F06813] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO



