ALTER TABLE [dbo].[dbt_trade_ticket] ADD [usage] int NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'usage'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [usage] int  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'usage'
    GO

ALTER TABLE [dbo].[dba_customer] ADD [enterprise_customer_code] varchar(255) DEFAULT '' NULL
GO

    EXEC sp_addextendedproperty
    'MS_Description', N'客户集团编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer',
    'COLUMN', N'enterprise_customer_code'
    GO

ALTER TABLE [dbo].[dba_customer] ADD [template_vip_code] varchar(255) DEFAULT '' NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'模板特殊客户组编码（取集团客户编码）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer',
    'COLUMN', N'template_vip_code'
    GO

--	追加履约保证金比例
ALTER TABLE [dbo].[dbt_tt_add] ADD [added_deposit_rate2] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	追加履约保证金比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'added_deposit_rate2'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [added_deposit_rate2] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	追加履约保证金比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'added_deposit_rate2'

ALTER TABLE [dbo].[dbt_contract] ADD [added_deposit_rate2] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	追加履约保证金比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'added_deposit_rate2'
GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [usage] int  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'usage'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [added_deposit_rate2] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	追加履约保证金比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'added_deposit_rate2'
GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [usage] int  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [added_deposit_rate2] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'usage'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	追加履约保证金比例',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'added_deposit_rate2'
GO

