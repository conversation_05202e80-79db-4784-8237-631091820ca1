-- todo:FileProcessFacadeImpl： 151 文件
-- todo:FileBusinessServiceImpl：149 文件
-- todo: ContractSignBuildProcessor: 429 二维码
--todo: ContractValueObjectService: 93 合同lkg开单量
-- todo:TemplateSignServiceImpl: 300 Clause_0001
ALTER TABLE [dbo].[dba_customer] ADD [enterprise_customer_code] nvarchar(255) NULL;
GO

update dba_customer set parent_id = id where name = enterprise_name;

UPDATE dba_customer
SET dba_customer.enterprise_customer_code = (
    SELECT b.linkage_customer_code
    FROM dba_customer b
    WHERE b.id = dba_customer.parent_id
      AND b.is_deleted = 0)
WHERE enterprise_name <> '';

update dba_customer set parent_id = id where name = enterprise_name;
-- 特殊客户编码
UPDATE dba_customer SET template_vip_code = 'C_XYJT'  WHERE linkage_customer_code IN ('1201551','1387308','1186392','1359374','1358704');
UPDATE dba_customer SET template_vip_code = 'ZDJT'  WHERE linkage_customer_code IN ('79752','1344154','34945');
UPDATE dba_customer SET template_vip_code = 'C_HNMY'  WHERE linkage_customer_code IN ('1175278','1261023');
UPDATE dba_customer SET template_vip_code = 'C_TWJT'  WHERE linkage_customer_code IN ('1206600','1356925','1356926','1346930','1346689','36651','27618','20816','80301','84317','84057','93701','61654','1355805','1352308','1204538','1202056','1200980','1180709','1180704','1180705','1180707','1181238','1183569','1183571','1183015','1183017','1183019','1183021','1183022','116776','1173374','1147864','1138935','1138958','1138960','1206737','1229283','1227925','1221231','1261041','1261042','1261025','1261226','1261227','1261229','1261230','1261231','1360597','1360598','1261040','1261228','1248188','1365167','1365170','1365181','1365182','1366768','1363426','1365705','1368943','1368944','1370475','1318171','1359453') AND id not in (4098,4092,4087);

-- update [dbo].[dba_customer]
-- set template_vip_code = enterprise_customer_code;

INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at])
VALUES ( 0, 0, 'S0014', '数字合同出具开关', 0, 1, GETDATE(), GETDATE());

INSERT INTO [dbo].[dbz_system_rule_item] ([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name])
SELECT id,'S0014',1, 0,0,1,GETDATE(),GETDATE(),'数字合同出具开关',1,1,N'数字合同出具开关','数字合同出具开关', NULL, N'1',N'1'
FROM dbz_system_rule
WHERE code = 'S0014'
  AND status = 1;
-- 刷新LOGO
UPDATE dba_company set logo ='https://csm4pnvgsto001.blob.core.chinacloudapi.cn/prod/prod/upload/magellan/ldclogo.png?sp=r&st=2022-07-12T02:29:35Z&se=2122-07-12T10:29:35Z&sv=2021-06-08&sr=b&sig=SvqklQpMI6HowhhKnNpMYSTwc9Lw0KHFWM6MSVca74k%3D' where id =1;
UPDATE dba_company set logo ='https://csm4pnvgsto001.blob.core.chinacloudapi.cn/prod/prod/upload/magellan/fl_logo.png?sp=r&st=2023-10-12T10:00:29Z&se=2083-10-12T18:00:29Z&spr=https&sv=2022-11-02&sr=b&sig=tTVDGzYGI8U37nW3ZiGBGakf%2F6%2BNnWROlxbBo4S%2BmUE%3D' where id =2;

--数字合同菜单配置同步
INSERT INTO [dbo].[dba_menu] ([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES ( NULL, N'N076', N'条款管理', 1, 88, N'/contract/termList', 1, 1, getDate(), getDate(), 0, 1, 0, N'N027');
INSERT INTO [dbo].[dba_menu] ([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES ( NULL, N'N077', N'条款组管理', 1, 88, N'/contract/termGroupList', 1, 1, getDate(), getDate(), 0, 1, 0, N'N027');
INSERT INTO [dbo].[dba_menu] ([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES ( NULL, N'N078', N'模板管理', 1, 88, N'/templateManage/list', 1, 1, getDate(), getDate(), 0, 1, 0, N'N027');
INSERT INTO [dbo].[dba_menu] ([icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES ( NULL, N'N079', N'变量管理', 1, 88, N'/variate/list', 1, 1, getDate(), getDate(), 0, 1, 0, N'N027');

update [dbo].[dba_menu] set is_deleted = 1WHERE code in ('N028', 'N029')


INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (503, N'SBM_S_100', N'SBM_S_100', N'豆粕销售撤回', NULL, 13, 2, 1, 1, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (504, N'SBM_P_047', N'SBM_P_047', N'豆粕采购撤回', NULL, 25, 2, 1, 1, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (505, N'SBO_S_100', N'SBO_S_100', N'豆油销售撤回', NULL, 37, 2, 1, 1, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (506, N'SBO_P_047', N'SBO_P_047', N'豆油采购撤回', NULL, 49, 2, 1, 1, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES ( 507, NULL, NULL, N'合同模板管理', NULL, 0, 0, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES ( 508, NULL, NULL, N'条款管理', NULL, 507, 1, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES ( 509, NULL, NULL, N'条款组管理', NULL, 507, 1, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES ( 510, NULL, NULL, N'模板管理', NULL, 507, 1, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (511, NULL, NULL, N'业务配置-质量指标', NULL, 1, 1, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (512, NULL, NULL, N'业务配置-质量指标', NULL, 2, 1, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (513, NULL, NULL, N'业务配置-质量指标', NULL, 3, 1, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (514, NULL, NULL, N'业务配置-质量指标', NULL, 4, 1, 1, 0, GETDATE(), GETDATE());


INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (515, N'SBM_S_097', N'SBM_S_097', N'新增-质量指标', NULL, 511, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (516, N'SBM_S_098', N'SBM_S_098', N'编辑-质量指标', NULL, 511, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (517, N'SBM_S_099', N'SBM_S_099', N'启用/禁用质量指标', NULL, 511, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (518, N'SBO_S_097', N'SBO_S_097', N'新增-质量指标', NULL, 513, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (519, N'SBO_S_098', N'SBO_S_098', N'编辑-质量指标', NULL, 513, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (520, N'SBO_S_099', N'SBO_S_099', N'启用/禁用质量指标', NULL, 513, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (521, N'TY_083', N'TY_083', N'新增条款', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (522, N'TY_084', N'TY_084', N'编辑条款', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (523, N'TY_085', N'TY_085', N'关联模板', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (531, N'TY_092', N'TY_092', N'移除关联模板', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (524, N'TY_086', N'TY_086', N'新增条款组', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (525, N'TY_087', N'TY_087', N'编辑条款组', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (526, N'TY_088', N'TY_088', N'新增模板', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (527, N'TY_089', N'TY_089', N'编辑模板', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (528, N'TY_090', N'TY_090', N'关联条款', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (529, N'TY_091', N'TY_091', N'禁用/启用模板', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (530, N'TY_093', N'TY_093', N'移除关联条款', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());