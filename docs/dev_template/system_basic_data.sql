-- 袋皮扣重
--  SELECT ri.id,case WHEN sr.category_id = 11 then 'SBM' else 'SBO' END '品类',sr.name as '包装',
-- ri.rule_key as '袋皮扣重',ri.memo as '对应协议文本',case when ri.status = 0 then '禁用' else '有效' end '状态'
-- FROM [dbo].[dbz_system_rule] sr
-- left join dbz_system_rule_item ri on sr.id = ri.rule_id
--  where sr.status = 1 and sr.parent_id in (14,33);

UPDATE dbz_system_rule_item SET memo = '标包50KG，卖方提供编织袋，包装袋不计价，不返还。每条编织袋扣重100克。'  WHERE id = 24 AND  rule_key = '0.10KG';
UPDATE dbz_system_rule_item SET memo = '标包70KG，卖方提供编织袋，包装袋不计价，不返还。每条编织袋扣重120克。'  WHERE id = 25 AND  rule_key = '0.12KG';
UPDATE dbz_system_rule_item SET memo = '散装。'  WHERE id = 26 AND  rule_key = '不扣皮';
UPDATE dbz_system_rule_item SET memo = '标袋800KG-1000KG之间，卖方提供吨袋，买方收货后负责将吨袋返还给卖方。每条吨袋平均扣重3400克。由于买方原因造成吨袋破损或遗失需要按每条85元进行赔偿。客户吨袋使用完毕返还回工厂确认质量数量无误后扣除吨袋补贴10元/吨。'  WHERE id = 27 AND  rule_key = '3.4KG';
UPDATE dbz_system_rule_item SET memo = '买方自备吨袋。'  WHERE id = 28 AND  rule_key = '不扣皮';
UPDATE dbz_system_rule_item SET memo = '标包50KG，卖方提供编织袋，包装袋不计价，不返还。不扣皮重。'  WHERE id = 29 AND  rule_key = '不扣皮';
UPDATE dbz_system_rule_item SET memo = '标包70KG，卖方提供编织袋，包装袋不计价，不返还。不扣皮重。'  WHERE id = 30 AND  rule_key = '不扣皮';
UPDATE dbz_system_rule_item SET memo = '散装。'  WHERE id = 207 AND  rule_key = '不扣皮';
UPDATE dbz_system_rule_item SET memo = '标包70KG，卖方提供编织袋，包装袋不计价，不返还。每条编织袋扣重125克。'  WHERE id = 382 AND  rule_key = '0.125KG';
UPDATE dbz_system_rule_item SET memo = '标包70KG，卖方提供编织袋，包装袋不计价，不返还。每条编织袋扣重110克。'  WHERE id = 433 AND  rule_key = '0.11KG';

-- 重量检验

select ri.id,case WHEN s.category_id = 11 then 'SBM' else 'SBO' END '品类',ri.rule_id as 'ruleId',s.code as '编码',
        ri.lkg_code as 'LKG编码',ri.rule_key as '名称',ri.rule_value as '条款',ri.rule_key as 'memo'
from dbz_system_rule_item ri
         join dbz_system_rule s on ri.rule_id = s.id
WHERE ri.rule_id in (61,63);
--禁用/删除重量检验
UPDATE dbz_system_rule_item SET status = 0 WHERE rule_id in (61,63) AND lkg_code IN ('21','22','23','24','25','26','27');
-- 更新重量检验内容
UPDATE dbz_system_rule_item SET rule_key = 'M-S-自提-卖方计量', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由双方认可的第三方检验机构进行复验。该复验结果为重量检验的最终结果。复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。' WHERE rule_id in (61,63) AND lkg_code = '10';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-目的港过磅', rule_value = '重量以#PYE#过磅计量为准。买方对重量不再有任何异议。' WHERE rule_id in (61,63) AND lkg_code = '11';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-买方过磅', rule_value = '重量以货物送至买方工厂的地磅计量为准。买方对重量不再有任何异议。' WHERE rule_id in (61,63) AND lkg_code = '12';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-整包-0.15%买方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。
0.15%以内的损耗由买方承担，超出0.15%损耗的由卖方派外管员前往买方处抽包核实，抽包结果大于0.15%的并且双方对抽包结果没有异议的，扣除0.15%后赔付。' WHERE rule_id in (61,63) AND lkg_code = '13';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-整包-0.3%买方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。
0.3%以内的损耗由买方承担，超出0.3%损耗的由卖方派外管员前往买方处抽包核实，抽包结果大于0.3%的并且双方对抽包结果没有异议的，扣除0.3%后赔付。' WHERE rule_id in (61,63) AND lkg_code = '14';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-整包-0.3%卖方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。
0.3%以内的损耗由卖方承担并已经在合同单价中扣除，0.3%到0.6%的损耗由买方承担，超出0.6%损耗的由卖方派外管员前往买方处抽包核实，抽包结果大于0.6%的并且双方对抽包结果没有异议的，扣除0.6%后赔付。' WHERE rule_id in (61,63) AND lkg_code = '15';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-整包-0.2%卖方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。
0.2%以内的损耗由卖方承担并已经在合同单价中扣除，0.2%到0.4%的损耗由买方承担，超出0.4%损耗的由卖方派外管员前往买方处抽包核实，抽包结果大于0.4%的并且双方对抽包结果没有异议的，扣除0.4%后赔付。' WHERE rule_id in (61,63) AND lkg_code = '16';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-散包/吨袋-0.15%买方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方和买方协商解决，0.15%以内的损耗由买方承担。' WHERE rule_id in (61,63) AND lkg_code = '17';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-散包/吨袋-0.3%买方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方和买方协商解决，0.3%以内的损耗由买方承担。' WHERE rule_id in (61,63) AND lkg_code = '18';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-散包/吨袋-0.3%卖方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，0.3%以内的损耗由卖方承担并已经在合同单价中扣除，0.3%到0.6%的损耗由买方承担，超出0.6%损耗的由卖方和买方协商解决。' WHERE rule_id in (61,63) AND lkg_code = '19';
UPDATE dbz_system_rule_item SET rule_key = 'M-S-送货-散包/吨袋-0.2%卖方', rule_value = '重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，0.2%以内的损耗由卖方承担并已经在合同单价中扣除，0.2%到0.4%的损耗由买方承担，超出0.4%损耗的由卖方和买方协商解决。' WHERE rule_id in (61,63) AND lkg_code = '20';
-- 新增重量检验
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:01:31.450', '2023-10-13 17:01:31.447', NULL, 1, 1, N'M-S-转货权', '<p>重量以货权转移证明所载重量为准。</p>', N'100', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:03:47.977', '2023-10-13 17:03:47.977', NULL, 1, 1, N'O-S-自提-交货工厂=ZJG', '<p>1）重量：如果买方以卡车提货，重量以交货仓库地磅为准；如果买方以船舶或驳船或管道提货，重量以指定交货储罐打尺为准。</p><p>2）质量：质量检验地点在交货地点，质量检验以交货地点的检验为准。每次交货时由双方对当批货物大罐/管线样品签字封样。</p>', N'101', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:04:10.667', '2023-10-13 17:04:10.667', NULL, 1, 1, N'O-S-自提-交货工厂=ZZY/TJIB/TJ/DG/FL', '<p>1）重量：如果买方以卡车提货，重量以交货仓库地磅为准；如果买方以船舶或驳船或管道提货，重量以指定交货储罐打尺为准。</p><p>2）质量：质量检验地点在交货地点，质量检验以交货地点的检验为准。每次交货时由双方对当批货物装货管线样品签字封样。</p>', N'102', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:04:28.193', '2023-10-13 17:04:28.193', NULL, 1, 1, N'O-S-转货权-交货工厂=ZJG', '<p>1）重量：以货权转移证明所载重量为准。</p><p>2）质量：质量检验地点在交货地点，质量检验以交货地点的检验为准。每次交货时由双方对当批货物大罐/管线样品签字封样。</p>', N'103', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:04:49.723', '2023-10-13 17:04:49.723', NULL, 1, 1, N'O-S-转货权-交货工厂=ZZY/TJIB/TJ/DG/FL', '<p>1）重量：以货权转移证明所载重量为准。</p><p>2）质量：质量检验地点在交货地点，质量检验以交货地点的检验为准。每次交货时由双方对当批货物装货管线样品签字封样。</p>', N'104', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:05:11.187', '2023-10-13 17:05:11.187', NULL, 1, 1, N'O-S-送货-交货工厂=ZJG/ZZY/TJIB/TJ/DG/FL-重量质量以卖方为准', '<p>1）重量：以卖方发货仓库地磅/发货储罐打尺为准。</p><p>2）质量：以卖方工厂检验为准。每次送货时由双方对当批货物样品签字封样。</p>', N'105', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:05:29.480', '2023-10-13 17:05:29.480', NULL, 1, 1, N'O-S-送货-交货工厂=ZJG/ZZY/TJIB/TJ/DG/FL-重量质量以买方为准', '<p>1）重量：以买方收货仓库地磅/收货储罐打尺为准。</p><p>2）质量：质量检验地点在交货地点，质量检验以交货地点的检验为准。每次送货时由双方对当批货物样品签字封样。</p>', N'106', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:06:20.490', '2023-10-13 17:06:20.490', NULL, 1, 1, N'M-P-自提', '<p>1）重量检验：以交货地点/仓库的地磅数量为准。</p><p>2）质量检验：质量检验以交货地点的检验为准;每次提货时由双方对当批货物分别封样。</p>', N'107', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:06:40.533', '2023-10-13 17:06:40.530', NULL, 1, 1, N'M-P-送货', '<p>1）重量检验：以交货地点/仓库的地磅数量为准。</p><p>2）质量检验：质量检验以交货地点的检验为准;每次提货时由双方对当批货物分别封样。</p>', N'108', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:06:56.997', '2023-10-13 17:06:56.997', NULL, 1, 1, N'M-P-转货权', '<p>1）重量检验：以货权转让证明载明数量为准。</p><p>2）质量检验：质量检验以交货地点的检验为准;每次提货时由双方对当批货物分别封样。</p>', N'109', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:07:51.197', '2023-10-13 17:07:51.197', NULL, 1, 1, N'O-P-自提', '<p>1）	重量检验：以交货地点/仓库的地磅数量为准。</p><p>2）	质量检验：卖方于买方提货前提交指定交货仓库出具的当批货物的检验报告，经买方确认以上检验结果符合合同要求后开始提货。每次提货时由双方对当批货物共同封样。</p>', N'110', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:08:23.807', '2023-10-13 17:08:23.807', NULL, 1, 1, N'O-P-送货', '<p>1）重量检验：以交货地点/仓库的地磅数量为准。</p><p>2）质量检验：质量检验以交货地点的检验为准;每次提货时由双方对当批货物分别封样。</p>', N'111', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 63, '重量检验配置', 1, 0, 0, 1, '2023-10-13 17:08:41.343', '2023-10-13 17:08:41.343', NULL, 1, 1, N'O-P-转货权', '<p>1）重量检验：以货权转让证明载明数量为准。</p><p>2）质量检验：卖方于买方提货前提交指定交货仓库出具的当批货物的检验报告，经买方确认以上检验结果符合合同要求后开始提货。每次提货时由双方对当批货物共同封样。</p>', N'112', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:09:17.337', '2023-10-13 17:09:17.333', NULL, 1, 1, N'M-S-送货-河南牧原&河南谷安-辽宁省营口港', '<p>重量以辽宁省营口港过磅计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。</p>', N'113', N'admin1', N'admin1');
INSERT INTO [dbo].[dbz_system_rule_item] ( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name]) VALUES ( 61, '重量检验配置', 2, 0, 0, 1, '2023-10-13 17:09:32.610', '2023-10-13 17:09:32.610', NULL, 1, 1, N'M-S-送货-河南牧原&河南谷安-辽宁省锦州港', '<p>重量以辽宁省锦州港过磅计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由卖方派外管员前往买方处抽取完整包核实，以双方认可的抽包平均重量确定损耗处理争议，复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担。</p>', N'114', N'admin1', N'admin1');
