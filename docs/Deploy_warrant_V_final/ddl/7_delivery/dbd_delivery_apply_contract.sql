ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [atlas_update_result] int  NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [atlas_update_times] int  NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [executed_num] decimal(25,6) NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [apply_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'根据ATLAS的数据反馈更新结果的记录1.true 0.false',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'atlas_update_result'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接收ATLAS反馈信息更新表的次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'atlas_update_times'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已执行数量(已提货数量)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'executed_num'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'apply_code'
