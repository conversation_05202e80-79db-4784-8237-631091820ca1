-- ----------------------------
-- Table structure for dbd_delivery_apply_allocate
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbd_delivery_apply_allocate]') AND type IN ('U'))
DROP TABLE [dbo].[dbd_delivery_apply_allocate]
    GO

CREATE TABLE [dbo].[dbd_delivery_apply_allocate] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [apply_id] int  NULL,
    [apply_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [goods_id] int  NULL,
    [customer_id] int  NULL,
    [supplier_id] int  NULL,
    [warrant_number] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_exchange_delivery] int  NULL,
    [is_soybean2] int  NULL,
    [allocation_qty] decimal(15,6)  NULL,
    [remark] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [sub_apply_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [dce_contract_no] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_allocate] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请单id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'apply_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'申请单Code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'apply_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'商品id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'goods_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'买方主体ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'卖方主体ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'supplier_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单号码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'warrant_number'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否是DCE',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'is_exchange_delivery'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否是豆二',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'is_soybean2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'分配数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'allocation_qty'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'DR子编号(传输ATLAS)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'sub_apply_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'DCE合同编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate',
    'COLUMN', N'dce_contract_no'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货申请预分配',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_allocate'
    GO


    -- ----------------------------
-- Auto increment value for dbd_delivery_apply_allocate
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbd_delivery_apply_allocate]', RESEED, 1)
    GO


-- ----------------------------
-- Primary Key structure for table dbd_delivery_apply_allocate
-- ----------------------------
ALTER TABLE [dbo].[dbd_delivery_apply_allocate] ADD CONSTRAINT [PK__dbd_deli__3213E83F211B4BEF] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

