--dbd_delivery_apply
--运输方式 transport_way int
--车站 station nvarchar
--核定干舷 freeboard int
--MMSI mmsi nvarchar
--拼车/船方 carpool_count int
--拼车/船客户 carpool_customer nvarchar
--装货优先级 loading_priority int
--操作方 operator_name nvarchar

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [transport_way] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [station] nvarchar(255) NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [freeboard] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [mmsi] nvarchar(255) NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [carpool_count] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [carpool_customer] nvarchar(255) NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [loading_priority] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [trigger_sys] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [third_sys] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [atlas_apply_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [atlas_sub_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [approval_comments] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [is_final] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [queue_status] nvarchar(64) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'运输方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'transport_way'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'车站',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'station'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'核定干舷',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'freeboard'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'MMSI',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'mmsi'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'拼车/船方',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'carpool_count'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'拼车/船客户',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'carpool_customer'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'装货优先级',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'loading_priority'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'操作方',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'trigger_sys'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'第三方系统',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'third_sys'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'Atlas接口返回的申请状态 Rejected Blocked Awaiting confirmation Confirmed',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'atlas_apply_status'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'Atlas接口返回的sub_status',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'atlas_sub_status'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS接口返回的审批备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'approval_comments'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'拼车人数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'is_final'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'车辆状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'queue_status'
