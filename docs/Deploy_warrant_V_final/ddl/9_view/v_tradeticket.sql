ALTER VIEW [dbo].[v_tradeticket] AS
SELECT
    tt.id,
    tt.bu_code,
    tt.site_code,
    tt.site_name,
    tt.contract_code,
    tt.contract_id,
    tt.code,
    tt.type,
    tt.contract_type,
    tt.status,
    tt.approval_status,
    tt.approval_type,
    tt.contract_status,
    tt.contract_signature_status,
    tt.operation_source,
    tt.contract_source,
    tt.trade_type,
    tt.owner_id,
    tt.sales_type,
    tt.invalid_reason,
    tt.is_deleted,
    tt.created_by,
    tt.updated_by,
    tt.created_at,
    tt.updated_at,
    tt.protocol_code,
    tt.sign_id,
    tt.goods_category_id,
    tt.sub_goods_category_id,
    tt.customer_id,
    tt.customer_code,
    tt.customer_name,
    tt.customer_type,
    tt.supplier_id,
    tt.supplier_name,
    tt.supplier_type,
    tt.bank_id,
    tt.supplier_code,
    tt.future_code,
    tt.domain_code,
    tt.belong_customer_id,
    tt.group_id,
    tt.source_contract_id,
    tt.before_contract_num,
    tt.change_contract_num,
    tt.after_contract_num,
    tt.pay_condition_id,
    tt.occupy_status,
    tt.company_id,
    tt.company_name,
    tt.usage,
    tt.cancel_reason,
    tt.source_type,
    tt.confirm_price_info,
    tt.category1,
    tt.category2,
    tt.category3,
    customer.enterprise_name AS enterprise_name,
    supplier.enterprise_name AS supplier_enterprise_name,
    tt.goods_name,
    tt.commodity_name,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_start_time
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_start_time
          ELSE contract.delivery_start_time END) AS delivery_start_time,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_end_time
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_end_time
          ELSE contract.delivery_end_time END) AS delivery_end_time,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.goods_package_id
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.goods_package_id
          ELSE contract.goods_package_id END) AS goods_package_id,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.goods_spec_id
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.goods_spec_id
          ELSE contract.goods_spec_id END) AS goods_spec_id,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.ship_warehouse_id
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.ship_warehouse_id
          ELSE contract.ship_warehouse_id END) AS ship_warehouse_id,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_factory_code
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_factory_code
          ELSE contract.delivery_factory_code END) AS delivery_factory_code,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.extra_price
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.extra_price
          ELSE contract.extra_price END) AS extra_price,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.unit_price
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.unit_price
          ELSE contract.unit_price END) AS unit_price,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_type
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_type
          ELSE contract.delivery_type END) AS delivery_type,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.package_weight
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.package_weight
          ELSE contract.package_weight END) AS package_weight,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.price_end_time
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.price_end_time
          ELSE contract.price_end_time END) AS price_end_time,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.deposit_rate
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.deposit_rate
          ELSE contract.deposit_rate END) AS deposit_rate,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.added_deposit_rate
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.added_deposit_rate
          ELSE contract.added_deposit_rate END) AS added_deposit_rate,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.added_deposit_rate2
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.added_deposit_rate2
          ELSE contract.added_deposit_rate2 END) AS added_deposit_rate2,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.credit_days
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.credit_days
          ELSE contract.credit_days END) AS credit_days,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.memo
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.memo
          ELSE contract.memo END) AS memo,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.warrant_code
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.warrant_code
          ELSE contract.warrant_code END) AS warrant_code,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.warrant_id
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.warrant_id
          ELSE contract.warrant_id END) AS warrant_id,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.warrant_trade_type
          WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.warrant_trade_type
          ELSE contract.warrant_trade_type END) AS warrant_trade_type,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.write_off_start_time
          ELSE contract.write_off_start_time END) AS write_off_start_time,
    (CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.write_off_end_time
          ELSE contract.write_off_end_time END) AS write_off_end_time,
    (CASE WHEN tt.type =2 THEN tt_modify.goods_id
          WHEN tt.type =3 THEN tt_modify.goods_id
          WHEN tt.type =18 THEN tt_modify.goods_id
          WHEN tt.type =6 THEN tt_price.goods_id
          WHEN tt.type =12 THEN tt_price.goods_id
          WHEN tt.type =4 THEN tt_tranfer.goods_id
          WHEN tt.type =5 THEN tt_tranfer.goods_id
          ELSE tt_add.goods_id END) AS goods_id
FROM
    dbo.dbt_trade_ticket tt
        LEFT JOIN dbo.dba_customer customer ON tt.customer_id = customer.id
        LEFT JOIN dbo.dba_customer supplier ON tt.supplier_id = supplier.id
        LEFT JOIN dbo.dbt_tt_add tt_add ON tt_add.tt_id = tt.id
        LEFT JOIN dbo.dbt_tt_modify tt_modify ON tt_modify.tt_id = tt.id
        LEFT JOIN dbo.dbt_contract contract ON contract.id = tt.contract_id
        LEFT JOIN dbo.dbt_tt_tranfer tt_tranfer ON tt_tranfer.tt_id = tt.id
        LEFT JOIN dbo.dbt_tt_price tt_price ON tt_price.tt_id = tt.id;

-- 刷新视图(执行完修改脚本后执行)
sp_refreshview 'v_tradeticket';