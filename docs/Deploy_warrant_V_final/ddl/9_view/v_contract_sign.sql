ALTER VIEW [dbo].[v_contract_sign] AS
SELECT
    a.*,
    b.use_yqq,
    b.is_columbus,
    (select frame_protocol from dba_customer_protocol c where  c.customer_id = a.use_customer_id and c.sale_type = a.sales_type and c.company_id = a.company_id and c.category_id = a.goods_category_id and c.is_deleted = 0) as 'frame_protocol',
        (select protocol_start_date from dba_customer_protocol c where  c.customer_id = a.use_customer_id and c.sale_type = a.sales_type and c.company_id = a.company_id and c.category_id = a.goods_category_id and c.is_deleted = 0) as 'protocol_start_date',
        (select protocol_end_date from dba_customer_protocol c where  c.customer_id = a.use_customer_id and c.sale_type = a.sales_type and c.company_id = a.company_id and c.category_id = a.goods_category_id and c.is_deleted = 0) as 'protocol_end_date',
        (select ldc_frame from dba_customer_original_paper d where  d.customer_id = a.use_customer_id and d.sale_type = a.sales_type and d.company_id = a.company_id and d.category_id = a.goods_category_id and d.is_deleted = 0) as 'original_ldc_frame'
FROM
    ( SELECT ( CASE WHEN sales_type = 2 THEN customer_id WHEN sales_type = 1 THEN supplier_id END ) AS use_customer_id,* FROM dbt_contract_sign ) a
        LEFT JOIN dba_customer b ON a.use_customer_id = b.id;

-- 刷新视图(执行完修改脚本后执行)
sp_refreshview 'v_contract_sign';