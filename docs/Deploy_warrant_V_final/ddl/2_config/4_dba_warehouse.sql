-- 新增库点表
-- Table structure for dba_warehouse
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_warehouse]') AND type IN ('U'))
DROP TABLE [dbo].[dba_warehouse]
    GO

CREATE TABLE [dbo].[dba_warehouse] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [unique_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [type] int  NULL,
    [name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [geographic_area_id] int  NULL,
    [geographic_city_id] int  NULL,
    [is_dce] int  NULL,
    [warehouse_type] int  NULL,
    [is_unset] int  NULL,
    [delivery_point] nvarchar(1000) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [address] nvarchar(1000) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [site_codes] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [atlas_warehouse_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [atlas_terminal_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [status] int  NULL,
    [source_id] int  NULL,
    [remark] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_deleted] int  NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'唯一码(暂定，后续根据唯一码查询配置)',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'unique_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'类型（新增字段）1：发货，2：提货',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'库点名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'库点编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'地理区域id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'geographic_area_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'地理工厂id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'geographic_city_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否交割 1：是 0：否',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'is_dce'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'LDC库/外库 1.LDC库 2.外库',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'warehouse_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否不定库 1：是 0：否',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'is_unset'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交货地点',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'delivery_point'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'地址',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'address'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'关联帐套(可以多个)',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'site_codes'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS warehose',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'atlas_warehouse_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS terminal',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'atlas_terminal_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'原数据的id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'source_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0未删除 1已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'dba_warehouse',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_warehouse'
    GO


    -- ----------------------------
-- Auto increment value for dba_warehouse
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_warehouse]', RESEED, 1000)
    GO


-- ----------------------------
-- Primary Key structure for table dba_warehouse
-- ----------------------------
ALTER TABLE [dbo].[dba_warehouse] ADD CONSTRAINT [PK__dba_ware__3213E83FA0AB5299] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

