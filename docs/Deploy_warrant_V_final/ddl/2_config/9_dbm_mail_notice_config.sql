-- ----------------------------
-- Table structure for dbm_mail_notice_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbm_mail_notice_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dbm_mail_notice_config]
GO

CREATE TABLE [dbo].[dbm_mail_notice_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category2_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [factory_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [from_mail] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [cc] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [reply_to] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [pwd] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_deleted] tinyint DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[dbm_mail_notice_config] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类主编码',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类主名称',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'category2_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'工厂编码',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'factory_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'发邮件账号',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'from_mail'
GO

EXEC sp_addextendedproperty
'MS_Description', N'抄送',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'cc'
GO

EXEC sp_addextendedproperty
'MS_Description', N'回复人',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'reply_to'
GO

EXEC sp_addextendedproperty
'MS_Description', N'密码',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'pwd'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否被删除（0否;1是）',
'SCHEMA', N'dbo',
'TABLE', N'dbm_mail_notice_config',
'COLUMN', N'is_deleted'
GO


-- ----------------------------
-- Auto increment value for dbm_mail_notice_config
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbm_mail_notice_config]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbm_mail_notice_config
-- ----------------------------
ALTER TABLE [dbo].[dbm_mail_notice_config] ADD CONSTRAINT [PK__dbm_mail__3213E83F0A0CC87C] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

