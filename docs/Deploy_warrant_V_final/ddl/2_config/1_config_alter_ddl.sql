ALTER TABLE [dbo].[dbz_system_rule] ADD [memo] nvarchar(256) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_system_rule',
    'COLUMN', N'memo'
-- dbz_system_rule_item
ALTER TABLE [dbo].[dbz_system_rule_item] ADD [mdm_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'Mdm编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_system_rule_item',
    'COLUMN', N'mdm_code'
    GO
-- 易企签
ALTER TABLE [dbo].[dbz_yqq_sign_parameter] ADD [status] int DEFAULT 1 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'易企签配置',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_yqq_sign_parameter',
    'COLUMN', N'status'
    GO
-- 交提货方式表，加4个字段（业务类型、Atlas传输值、是否Magellan可提、是否Columbus可提）
ALTER TABLE [dbo].[dbt_delivery_type] ADD [bu_code] nvarchar(255) DEFAULT 'ST' NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'业务类型（ST-现货，WT-仓单）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_delivery_type',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_delivery_type] ADD [atlas_code] nvarchar(255) DEFAULT '' NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'Atlas传输值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_delivery_type',
    'COLUMN', N'atlas_code'
    GO

ALTER TABLE [dbo].[dbt_delivery_type] ADD [able_delivery_magellan] int DEFAULT 1 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'magellan可提(0-不可提，1-可提)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_delivery_type',
    'COLUMN', N'able_delivery_magellan'
    GO

ALTER TABLE [dbo].[dbt_delivery_type] ADD [able_delivery_columbus] int DEFAULT 1 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'columbus可提(0-不可提，1-可提)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_delivery_type',
    'COLUMN', N'able_delivery_columbus'
    GO

-- dba_pay_condition 付款条件代码
ALTER TABLE [dbo].[dba_pay_condition] ADD [bu_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_pay_condition',
    'COLUMN', N'bu_code'
    GO

-- dbh_quality 质量指标表加3个字段：货品ID,货品名称，企标类型
ALTER TABLE [dbo].[dbh_quality] ADD [sku_ids] text NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品ID列表',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_quality',
    'COLUMN', N'sku_ids'

ALTER TABLE [dbo].[dbh_quality] ADD [sku_names] text NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称列表',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_quality',
    'COLUMN', N'sku_names'

ALTER TABLE [dbo].[dbh_quality] ADD [standard_type] varchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'国企标准',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_quality',
    'COLUMN', N'standard_type'
