-- 通用关联关系记录表
-- Table structure for dbz_common_relation
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbz_common_relation]') AND type IN ('U'))
	DROP TABLE [dbo].[dbz_common_relation]
GO

CREATE TABLE [dbo].[dbz_common_relation] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [relation_type] int  NULL,
  [bind_code1] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [bind_code2] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_deleted] tinyint DEFAULT ((0)) NULL,
  [created_by] int DEFAULT ((0)) NULL,
  [updated_by] int DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbz_common_relation] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联类型表 1.库点与账套',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'relation_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联编码1',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'bind_code1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联编码2',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'bind_code2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否删除（0未删除 1已删除）',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbz_common_relation',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbz_common_relation
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbz_common_relation]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbz_common_relation
-- ----------------------------
ALTER TABLE [dbo].[dbz_common_relation] ADD CONSTRAINT [PK__dbz_comm__3213E83F317ABA84] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

