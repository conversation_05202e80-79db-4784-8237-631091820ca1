-- 新增品种代码的交易所及期货月份配置
-- Table structure for dbf_trading_config

IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbf_trading_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dbf_trading_config]
GO

CREATE TABLE [dbo].[dbf_trading_config] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [exchange] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [domain_month] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_deleted] int DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[dbf_trading_config] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货代码',
'SCHEMA', N'dbo',
'TABLE', N'dbf_trading_config',
'COLUMN', N'future_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交易所',
'SCHEMA', N'dbo',
'TABLE', N'dbf_trading_config',
'COLUMN', N'exchange'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货月份',
'SCHEMA', N'dbo',
'TABLE', N'dbf_trading_config',
'COLUMN', N'domain_month'
GO

EXEC sp_addextendedproperty
'MS_Description', N'逻辑删除',
'SCHEMA', N'dbo',
'TABLE', N'dbf_trading_config',
'COLUMN', N'is_deleted'
GO


-- ----------------------------
-- Auto increment value for dbf_trading_config
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbf_trading_config]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbf_trading_config
-- ----------------------------
ALTER TABLE [dbo].[dbf_trading_config] ADD CONSTRAINT [PK__dbf_trad__3213E83FEDD6F091] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

