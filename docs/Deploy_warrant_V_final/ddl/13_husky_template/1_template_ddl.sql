--合同模板配置
ALTER TABLE [dbo].[dbh_template] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [category1_name] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category1_name'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [category2_name] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category2_name'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [category3_name] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'category3_name'
    GO

--条款配置
ALTER TABLE [dbo].[dbh_template_item] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbh_template_item] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbh_template_item] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbh_template_item] ADD [category1_name] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category1_name'
    GO

ALTER TABLE [dbo].[dbh_template_item] ADD [category2_name] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category2_name'
    GO

ALTER TABLE [dbo].[dbh_template_item] ADD [category3_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'category3_name'
    GO
--模板出具标识表
ALTER TABLE [dbo].[dbh_template_check] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbh_template_check] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbh_template_check] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'category3'
    GO