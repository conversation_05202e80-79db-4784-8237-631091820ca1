-- 新增字段
ALTER TABLE [dbo].[dba_customer] ADD [is_authorization] int NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否有授权书',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer',
    'COLUMN', N'is_authorization'

ALTER TABLE [dbo].[dba_customer] ADD [trade_status] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer',
    'COLUMN', N'trade_status'
    GO

--客户模板配置
ALTER TABLE [dbo].[dba_customer_protocol] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_protocol] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_protocol] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category3'
    GO

---客户赊销预付
ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [bu_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'bu_code'
    GO

---客户履约保证金比例
ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [bu_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'bu_code'
    GO

---客户正本配置
ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category3'
    GO

---客户配置
ALTER TABLE [dbo].[dba_customer_detail] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [is_deleted] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0 未删除 1删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'is_deleted'

    GO

----客户通知人
ALTER TABLE [dbo].[dba_contact] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_contact] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_contact] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category3'
    GO

---通知人工厂编码
ALTER TABLE [dbo].[dba_contact_factory] ADD [factory_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'工厂编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact_factory',
    'COLUMN', N'factory_code'
    GO

--客户账户板置
ALTER TABLE [dbo].[dba_customer_bank] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category3'
    GO