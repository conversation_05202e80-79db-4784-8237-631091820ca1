-- 新增表（客户发票 + 客户评级 + 客户提货白名单 + 客户Mdm对接请求记录表）
---- 新增客户发票表
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_invoice]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_invoice]
    GO

CREATE TABLE [dbo].[dba_customer_invoice] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [customer_id] int  NULL,
    [invoice_id] int  NULL,
    [invoice_type] int DEFAULT ((1)) NULL,
    [invoice_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
    [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
    [company_id] int  NULL,
    [company_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT ((0)) NULL,
    [status] int DEFAULT ((1)) NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_at] datetime  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_invoice] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'客户id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'invoice_id'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'invoice_type'
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'发票描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'invoice_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'所属主体id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'所属主体简称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'company_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_invoice
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_invoice]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_invoice
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_invoice] ADD CONSTRAINT [PK__dba_cust__3213E83F6805FCDD] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO


----新增客户评级表
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_grade_score]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_grade_score]
    GO

CREATE TABLE [dbo].[dba_customer_grade_score] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [customer_id] int  NULL,
    [grade_score] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int  NULL,
    [status] int  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_at] datetime  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_grade_score] SET (LOCK_ESCALATION = TABLE)
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_grade_score
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_grade_score]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_grade_score
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_grade_score] ADD CONSTRAINT [PK__dba_cust__3213E83F1A821287] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

-- 新增客户提货白名单表
/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 23/09/2024 16:52:40
*/


-- ----------------------------
-- Table structure for dba_customer_delivery_white
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_delivery_white]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_delivery_white]
    GO

CREATE TABLE [dbo].[dba_customer_delivery_white] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [customer_id] int  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category3] nvarchar(1000) COLLATE Chinese_PRC_CI_AS  NULL,
    [status] int  NULL,
    [created_at] datetime  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_at] datetime  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_delivery_white] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'客户id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_delivery_white',
    'COLUMN', N'updated_by'
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_delivery_white
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_delivery_white]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_delivery_white
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_delivery_white] ADD CONSTRAINT [PK__dba_cust__3213E83FE30C4EFF] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

-- 新增客户Mdm对接表

/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 25/09/2024 17:31:08
*/


-- ----------------------------
-- Table structure for dba_customer_mdm_counterparty_record
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_mdm_counterparty_record]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_mdm_counterparty_record]
    GO

CREATE TABLE [dbo].[dba_customer_mdm_counterparty_record] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [counterparty_id] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [request_data] text COLLATE Chinese_PRC_CI_AS  NULL,
    [address_list] text COLLATE Chinese_PRC_CI_AS  NULL,
    [bank_details_list] text COLLATE Chinese_PRC_CI_AS  NULL,
    [category_list] text COLLATE Chinese_PRC_CI_AS  NULL,
    [before_data] text COLLATE Chinese_PRC_CI_AS  NULL,
    [after_data] text COLLATE Chinese_PRC_CI_AS  NULL,
    [content] text COLLATE Chinese_PRC_CI_AS  NULL,
    [response_data] text COLLATE Chinese_PRC_CI_AS  NULL,
    [request_time] datetime  NULL,
    [operation_type] int  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_mdm_counterparty_record] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'客户编码 ',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'counterparty_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求参数',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'request_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'修改前客户主数据',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'before_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'修改后客户主数据',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'after_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变化的字段',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'content'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'相应参数',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'response_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'request_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'0:update;1:insert',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_mdm_counterparty_record',
    'COLUMN', N'operation_type'
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_mdm_counterparty_record
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_mdm_counterparty_record]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_mdm_counterparty_record
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_mdm_counterparty_record] ADD CONSTRAINT [PK__dba_cust__3213E83FFD13242A] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

