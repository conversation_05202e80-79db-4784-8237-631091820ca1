-- Table structure for dbr_business_rule
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_business_rule]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_business_rule]
    GO

CREATE TABLE [dbo].[dbr_business_rule] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_variable] varchar(1024) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_info] varchar(1024) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] int  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbr_business_rule] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'rule_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务编号（某模块编号唯一，例：business_process_node表的node_code字段）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'refer_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务类型（1 LOA-A签汇签 2 LOA阈值-B签汇签 3 LOA阈值-C签单签 4 LOA阈值-C签汇签）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'refer_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件变量(conditionType、deliveryType)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'condition_variable'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'condition_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载规则信息（drools脚本）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'rule_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA_APROVAL：LOA审批CUSTOMER:客户关系)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源（例：Magellan、Columbus）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule',
    'COLUMN', N'system_id'
    GO
    -- ----------------------------
-- Auto increment value for dbr_business_rule
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_business_rule]', RESEED, 0)
    GO
-- ----------------------------
-- Primary Key structure for table dbr_business_rule
-- ----------------------------
ALTER TABLE [dbo].[dbr_business_rule] ADD CONSTRAINT [PK__dbr_busi__3213E83F1F54B5C8] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    -- ----------------------------
-- Table structure for dbr_business_rule_condition
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_business_rule_condition]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_business_rule_condition]
    GO

CREATE TABLE [dbo].[dbr_business_rule_condition] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [condition_variable_id] int  NULL,
    [condition_variable] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [pattern_relation] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_value] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_value_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_desc] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] int  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbr_business_rule_condition] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量ID(dbr_dict_item)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'condition_variable_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件变量英文名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'condition_variable'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'pattern_relation'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'condition_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载规则脚本（drools脚本）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'rule_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件值描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'condition_value_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载规则信息描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'rule_desc'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA阈值、权益变更、客户关系等)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'system_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_condition',
    'COLUMN', N'memo'
    GO


    -- ----------------------------
-- Auto increment value for dbr_business_rule_condition
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_business_rule_condition]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbr_business_rule_condition
-- ----------------------------
ALTER TABLE [dbo].[dbr_business_rule_condition] ADD CONSTRAINT [PK__dbr_na__3213E83FDE3ADAE4] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO
    -- ----------------------------
-- Table structure for dbr_business_rule_detail
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_business_rule_detail]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_business_rule_detail]
    GO

CREATE TABLE [dbo].[dbr_business_rule_detail] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_condition_id] int  NULL,
    [logic_relation] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
    [level] int  NULL,
    [rule_desc] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_value_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] int  NULL,
    [sort] int  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] tinyint DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbr_business_rule_detail] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'rule_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务编号（某模块编号唯一，例：business_process_node表的node_code字段）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'refer_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务类型（1 LOA-A签汇签 2 LOA阈值-B签汇签 3 LOA阈值-C签单签 4 LOA阈值-C签汇签）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'refer_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件值ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'rule_condition_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'逻辑关系(&&、||)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'logic_relation'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载规则脚本（drools脚本）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'rule_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'优先级',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'level'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'加载规则描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'rule_desc'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'条件值描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'condition_value_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA_APROVAL：LOA审批CUSTOMER:客户关系)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源（例：Magellan、Columbus）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'system_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0未删除 1已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'修改时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_business_rule_detail',
    'COLUMN', N'updated_at'
    GO

    -- ----------------------------
-- Auto increment value for dbr_business_rule_detail
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_business_rule_detail]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbr_business_rule_detail
-- ----------------------------
ALTER TABLE [dbo].[dbr_business_rule_detail] ADD CONSTRAINT [PK__dbr_busi__3213E83F1E2F67DB] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO
    -- ----------------------------
-- Table structure for dbr_common_config
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_common_config]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_common_config]
    GO

CREATE TABLE [dbo].[dbr_common_config] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [group_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [parent_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] int DEFAULT 0 NULL,
    [category2] int DEFAULT 0 NULL,
    [category3] int DEFAULT 0 NULL,
    [sales_type] int DEFAULT 0 NULL,
    [rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_content] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [status] tinyint  NOT NULL,
    [description] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] int  NULL,
    [sort] int  NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] tinyint DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbr_common_config] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'配置组编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'group_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'本记录的唯一编码，groupCode+id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'父留的编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'parent_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'0：采销 1：采购 2：销售',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'sales_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'rule_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则脚本',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'rule_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'rule_content'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态（0禁用 1启用）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'description'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA_APROVAL：LOA审批CUSTOMER:客户关系)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源（例：Magellan、Columbus）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'system_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'删除状态（0未删除 1已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_common_config',
    'COLUMN', N'updated_by'
    GO

    -- ----------------------------
-- Auto increment value for dbr_common_config
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_common_config]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbr_common_config
-- ----------------------------
ALTER TABLE [dbo].[dbr_common_config] ADD CONSTRAINT [PK__dbr_comm__3213E83FEF01A980] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    -- ----------------------------
-- Table structure for dbr_dict_item
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_dict_item]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_dict_item]
    GO

CREATE TABLE [dbo].[dbr_dict_item] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [dict_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [dict_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_value] int  NULL,
    [item_description] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [item_sort] int  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] int  NULL,
    [status] int DEFAULT 1 NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    )
    GO

ALTER TABLE [dbo].[dbr_dict_item] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典自增ID
',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典编码
',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'dict_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'dict_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'item_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'item_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'item_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项展示信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'item_description'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'字典项排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'item_sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA阈值、权益变更、客户关系等)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源（例：Magellan、Columbus）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'system_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_dict_item',
    'COLUMN', N'updated_by'
    GO

    -- ----------------------------
-- Auto increment value for dbr_dict_item
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_dict_item]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbr_dict_item
-- ----------------------------
ALTER TABLE [dbo].[dbr_dict_item] ADD CONSTRAINT [PK__dbz_syst__3213E83F023F3A2F_copy1] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

    -- ----------------------------
-- Table structure for dbr_variable
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbr_variable]') AND type IN ('U'))
DROP TABLE [dbo].[dbr_variable]
    GO

CREATE TABLE [dbo].[dbr_variable] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [display_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [value_type] tinyint DEFAULT 3 NULL,
    [is_condition] tinyint DEFAULT 0 NULL,
    [is_key] tinyint DEFAULT 0 NULL,
    [is_constant] tinyint  NULL,
    [has_dict] tinyint DEFAULT 0 NULL,
    [is_enum] tinyint DEFAULT 0 NULL,
    [is_logic] tinyint DEFAULT 0 NULL,
    [enum_path] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [pattern_relations] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [input_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [module_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [system_id] tinyint DEFAULT 0 NULL,
    [sort] int DEFAULT 0 NULL,
    [is_deleted] tinyint DEFAULT 0 NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbr_variable] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'变量展示名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'display_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'值类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'value_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为条件变量（0否 1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'is_condition'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为关键变量（0否 1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'is_key'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为常量（0否 1是)阈值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'is_constant'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否有字典值（0否 1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'has_dict'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为枚举（0否 1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'is_enum'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为逻辑变量（0否 1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'is_logic'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'枚举代码路径',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'enum_path'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'pattern_relations'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'输入框类型（select、input、boolean）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'input_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'功能模块(LOA_APROVAL：LOA审批CUSTOMER:客户关系)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'module_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源（例：Magellan、Columbus）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'system_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbr_variable',
    'COLUMN', N'sort'
    GO


    -- ----------------------------
-- Auto increment value for dbr_variable
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbr_variable]', RESEED, 0)
    GO
-- ----------------------------
-- Primary Key structure for table dbr_variable
-- ----------------------------
ALTER TABLE [dbo].[dbr_variable] ADD CONSTRAINT [PK__dbz_syst__3213E83F345F723C] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

