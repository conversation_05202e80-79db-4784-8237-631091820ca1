-- ----------------------------
-- Table structure for dba_category_approval_model
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_category_approval_model]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_category_approval_model]
GO

CREATE TABLE [dbo].[dba_category_approval_model] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [model_key] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [model_id] int  NULL,
  [category2] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL
)
GO

ALTER TABLE [dbo].[dba_category_approval_model] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程图编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'model_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'流程图id',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'model_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_category_approval_model',
'COLUMN', N'created_at'
GO


-- ----------------------------
-- Records of dba_category_approval_model
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_category_approval_model] ON
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at]) VALUES (N'1', N'PROC_DEF_SC_ADD', N'2501', N'11', NULL)
GO

INSERT INTO [dbo].[dba_category_approval_model] ([id], [model_key], [model_id], [category2], [created_at]) VALUES (N'2', N'PROC_DEF_SC_ADD', N'2501', N'12', NULL)
GO

SET IDENTITY_INSERT [dbo].[dba_category_approval_model] OFF
GO


-- ----------------------------
-- Auto increment value for dba_category_approval_model
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_category_approval_model]', RESEED, 2)
GO


-- ----------------------------
-- Primary Key structure for table dba_category_approval_model
-- ----------------------------
ALTER TABLE [dbo].[dba_category_approval_model] ADD CONSTRAINT [PK__dba_cate__3213E83F116C6DF8] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

