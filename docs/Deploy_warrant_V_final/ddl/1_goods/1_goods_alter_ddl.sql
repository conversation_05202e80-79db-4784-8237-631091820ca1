-- =================== dbg_category =================== start.
ALTER TABLE [dbo].[dbg_category] ADD [serial_no] int NULL
GO
EXEC sp_addextendedproperty
     'MS_Description', N'主编码',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_category',
     'COLUMN', N'serial_no';
ALTER TABLE [dbo].[dbg_category] ADD [is_dce] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否交割',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'is_dce'
    GO
ALTER TABLE [dbo].[dbg_category] ADD [is_soybean2] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否豆二',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'is_soybean2'
    GO
ALTER TABLE [dbo].[dbg_category] ADD [future_code] varchar (255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'期货代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'future_code'
ALTER TABLE [dbo].[dbg_category] ADD [nav_id] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'NAV品种ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'nav_id'
ALTER TABLE [dbo].[dbg_category] ADD [is_split_contract] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否拆分合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'is_split_contract'
ALTER TABLE [dbo].[dbg_category] ADD [created_by] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'created_by'
    GO
ALTER TABLE [dbo].[dbg_category] ADD [updated_by] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category',
    'COLUMN', N'updated_by'
-- =================== dbg_category =================== end.

-- =================== dbg_attribute =================== start.
ALTER TABLE [dbo].[dbg_attribute] ADD [display_name] nvarchar(64) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'规格/包装名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_attribute',
    'COLUMN', N'display_name'
    GO
-- dbg_attribute

ALTER TABLE [dbo].[dbg_attribute] ADD [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbg_attribute] ADD [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_attribute',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_attribute',
    'COLUMN', N'updated_by'
-- =================== dbg_attribute =================== end.

-- =================== dbg_goods =================== start.
ALTER TABLE [dbo].[dbg_goods] ADD [spu_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'SPU商品ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'spu_id'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [full_name] varchar (255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'系统生成的sku货品全称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'full_name'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [sku_no] varchar (255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'品种code+多个关键+销售规格+包装值ID
下划线隔开（排序）:_11_222_12_',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'sku_no'
    GO

ALTER TABLE [dbo].[dbg_goods] ADD [key_attribute_values] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'多个关键规格值+销售规格信息(Json,例：
[{"attributeId":"20","attributeValueId":"20","attributeValueName":"43%"}])',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'key_attribute_values'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [category1] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [category2] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [category3] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'category3'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [nav_sku_id] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'NAV SKU ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'nav_sku_id'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [nick_name] varchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'商品昵称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'nick_name'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [is_warrant] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否仓单标品(0否 1是)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'is_warrant'
    GO
ALTER TABLE [dbo].[dbg_goods] ADD [is_tt_attribute] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否TT默认规格(新增字段)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'is_tt_attribute'
ALTER TABLE [dbo].[dbg_goods] ADD [is_global_oil] int NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否Global Oil(0否 1是)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_goods',
    'COLUMN', N'is_global_oil'
-- =================== dbg_goods =================== end.

-- =================== dbg_attribute_value =================== start.
ALTER TABLE [dbo].[dbg_attribute_value] ADD [mdm_package_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'包装mdm编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_attribute_value',
    'COLUMN', N'mdm_package_code'
-- =================== dbg_attribute_value =================== end.

-- =================== dbg_category =================== start.
-- =================== dbg_category =================== end.
