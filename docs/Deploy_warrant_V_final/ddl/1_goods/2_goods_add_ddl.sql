
--新增品类规格表
-- ----------------------------
-- ----------------------------
-- Table structure for dbg_category_attribute
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_category_attribute]') AND type IN ('U'))
DROP TABLE [dbo].[dbg_category_attribute]
    GO

CREATE TABLE [dbo].[dbg_category_attribute] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [category1] int  NULL,
    [category2] int  NULL,
    [category3] int  NULL,
    [attribute_id] int  NULL,
    [attribute_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [attribute_type] int  NULL,
    [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_by] varchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_by] varchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [sort] int  NULL
    )
    GO

ALTER TABLE [dbo].[dbg_category_attribute] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种ID(3级品类)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规格ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'attribute_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规格名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'attribute_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规格类型（1包装规格;2关键规格;3销售规格）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'attribute_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'删除状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_category_attribute',
    'COLUMN', N'sort'
    GO


    -- ----------------------------
-- Auto increment value for dbg_category_attribute
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbg_category_attribute]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbg_category_attribute
-- ----------------------------
ALTER TABLE [dbo].[dbg_category_attribute] ADD CONSTRAINT [PK__dbg_cate__3213E83F38571FB6] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO




    -- ----------------------------
-- Table structure for dbg_sku_attribute_value
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_sku_attribute_value]') AND type IN ('U'))
	DROP TABLE [dbo].[dbg_sku_attribute_value]
GO

CREATE TABLE [dbo].[dbg_sku_attribute_value] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [sku_id] int DEFAULT 0 NULL,
  [category1] int  NULL,
  [category2] int  NULL,
  [category3] int  NULL,
  [attribute_id] int DEFAULT 0 NULL,
  [attribute_value_id] int DEFAULT 0 NULL,
  [attribute_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [attribute_value_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [sort] int DEFAULT 0 NULL,
  [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [is_deleted] int DEFAULT 0 NULL,
  [attribute_type] int  NULL
)
GO

ALTER TABLE [dbo].[dbg_sku_attribute_value] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SKU ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'sku_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'三级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'attribute_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格值ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'attribute_value_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格名称',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'attribute_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格值名称',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'attribute_value_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'sort'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否删除',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'规格类型',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_attribute_value',
'COLUMN', N'attribute_type'
GO


-- ----------------------------
-- Table structure for dbg_sku_mdm
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_sku_mdm]') AND type IN ('U'))
	DROP TABLE [dbo].[dbg_sku_mdm]
GO

CREATE TABLE [dbo].[dbg_sku_mdm] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [type] int  NULL,
  [sku_id] int  NULL,
  [mdm_id] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [is_deleted] int DEFAULT 0 NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [updated_at] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[dbg_sku_mdm] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'类型',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SKU ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'sku_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'MDM编码',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'mdm_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SKU-MDM',
'SCHEMA', N'dbo',
'TABLE', N'dbg_sku_mdm'
GO


-- ----------------------------
-- Table structure for dbg_spu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_spu]') AND type IN ('U'))
	DROP TABLE [dbo].[dbg_spu]
GO

CREATE TABLE [dbo].[dbg_spu] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [category1] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category2] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category3] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [spu_no] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [spu_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [key_attribute_values] text COLLATE Chinese_PRC_CI_AS  NULL,
  [status] int  NULL,
  [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [is_deleted] int DEFAULT 0 NULL,
  [nav_spu_id] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbg_spu] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种ID(3级品类)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
'MS_Description', N'spu编码（品种code+多个关键销售规格+包装值ID 下划线隔开（排序）:_11_222_12_）',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'spu_no'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种+关键规格（豆粕,43%;）',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'spu_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关键规格信息多个关键规格值信息(Json)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'key_attribute_values'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SPU商品状态(0禁用;1启用)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'NAV SPU ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu',
'COLUMN', N'nav_spu_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SPU商品',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu'
GO


-- ----------------------------
-- Table structure for dbg_spu_attribute_value
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_spu_attribute_value]') AND type IN ('U'))
	DROP TABLE [dbo].[dbg_spu_attribute_value]
GO

CREATE TABLE [dbo].[dbg_spu_attribute_value] (
  [ID] int  IDENTITY(1,1) NOT NULL,
  [spu_id] int  NULL,
  [category1] int  NULL,
  [category2] int  NULL,
  [category3] int  NULL,
  [attribute_id] int DEFAULT 0 NULL,
  [attribute_value_id] int DEFAULT 0 NULL,
  [attribute_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [attribute_value_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [sort] int DEFAULT 0 NULL,
  [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [is_deleted] int DEFAULT 0 NULL
)
GO

ALTER TABLE [dbo].[dbg_spu_attribute_value] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'ID'
GO

EXEC sp_addextendedproperty
'MS_Description', N'spuId',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'spu_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'三级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关键规格ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'attribute_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关键规格值ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'attribute_value_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关键规格值名称',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'attribute_value_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'sort'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'SPU的关键规格信息',
'SCHEMA', N'dbo',
'TABLE', N'dbg_spu_attribute_value'
GO

-- ----------------------------
-- Auto increment value for dbg_sku_attribute_value
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbg_sku_attribute_value]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbg_sku_attribute_value
-- ----------------------------
ALTER TABLE [dbo].[dbg_sku_attribute_value] ADD CONSTRAINT [PK__dbg_attr__3213E83F5A8F1EC3_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Auto increment value for dbg_sku_mdm
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbg_sku_mdm]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbg_sku_mdm
-- ----------------------------
ALTER TABLE [dbo].[dbg_sku_mdm] ADD CONSTRAINT [PK__dbg_sku___3213E83F5F9BED64] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Auto increment value for dbg_spu
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbg_spu]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbg_spu
-- ----------------------------
ALTER TABLE [dbo].[dbg_spu] ADD CONSTRAINT [PK__dbg_spu__3214EC27E646D2FA] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO


-- ----------------------------
-- Auto increment value for dbg_spu_attribute_value
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbg_spu_attribute_value]', RESEED, 0)
GO


-- ----------------------------
-- Primary Key structure for table dbg_spu_attribute_value
-- ----------------------------
ALTER TABLE [dbo].[dbg_spu_attribute_value] ADD CONSTRAINT [PK__dbg_spu___3214EC278F0003F4] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

    /*
     Navicat Premium Data Transfer

     Source Server         : ldc_navigator_test-外网
     Source Server Type    : SQL Server
     Source Server Version : 14003456
     Source Host           : *************:65431
     Source Catalog        : ldc_navigator_test
     Source Schema         : dbo

     Target Server Type    : SQL Server
     Target Server Version : 14003456
     File Encoding         : 65001

     Date: 12/10/2024 17:20:40
    */


-- ----------------------------
-- Table structure for dba_sequence
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_sequence]') AND type IN ('U'))
DROP TABLE [dbo].[dba_sequence]
    GO

CREATE TABLE [dbo].[dba_sequence] (
    [id] int IDENTITY(1,1) NOT NULL,
    [redis_key] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [redis_value] int  NULL,
    [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [is_deleted] int DEFAULT 0 NULL
    )
    GO

ALTER TABLE [dbo].[dba_sequence] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'序列号Key',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'redis_key'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'序列号值',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'redis_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'删除状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'序列号',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_sequence'
    GO


-- ----------------------------
-- Primary Key structure for table dba_sequence
-- ----------------------------
ALTER TABLE [dbo].[dba_sequence] ADD CONSTRAINT [PK__dba_sequ__3213E83F69710E33] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

-- ----------------------------
-- Table structure for dbg_sku_mdm
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbg_sku_mdm]') AND type IN ('U'))
DROP TABLE [dbo].[dbg_sku_mdm]
    GO

CREATE TABLE [dbo].[dbg_sku_mdm] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [type] int  NULL,
    [sku_id] int  NULL,
    [mdm_id] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [tax_rate] decimal(25,6)  NULL,
    [bu_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [sale_tax_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [purchase_tax_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [memo] varchar(256) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbg_sku_mdm] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主键',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'SKU ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'sku_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'MDM编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'mdm_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'税率',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'tax_rate'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线,现货/仓单',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'bu_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'销售的税率编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'sale_tax_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'采购的税率编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'purchase_tax_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'删除状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'SKU-MDM',
    'SCHEMA', N'dbo',
    'TABLE', N'dbg_sku_mdm'
    GO


    -- ----------------------------
-- Auto increment value for dbg_sku_mdm
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbg_sku_mdm]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dbg_sku_mdm
-- ----------------------------
ALTER TABLE [dbo].[dbg_sku_mdm] ADD CONSTRAINT [PK__dbg_sku___3213E83F5F9BED64] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

