CREATE TABLE [dbo].[dba_c_powerV2] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [pre_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [describe] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [parent_id] int  NULL,
  [level] int  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [is_category] int  NULL,
  [original_id] int  NULL
)
GO

ALTER TABLE [dbo].[dba_c_powerV2] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否区分品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_powerV2',
'COLUMN', N'is_category'
GO

EXEC sp_addextendedproperty
'MS_Description', N'原始ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_powerV2',
'COLUMN', N'original_id'
GO