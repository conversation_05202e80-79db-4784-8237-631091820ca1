CREATE TABLE [dbo].[dba_c_menuV2] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [icon] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [category_id] int  NULL,
  [code] nvarchar(32) COLLATE Chinese_PRC_CI_AS  NULL,
  [parent_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [name] nvarchar(32) COLLATE Chinese_PRC_CI_AS  NULL,
  [level] int  NULL,
  [parent_id] int  NULL,
  [url] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [sort] int  NULL,
  [status] int DEFAULT ((1)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [is_category] int  NULL,
  [original_id] int  NULL
)
GO

ALTER TABLE [dbo].[dba_c_menuV2] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否区分品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_menuV2',
'COLUMN', N'is_category'
GO

EXEC sp_addextendedproperty
'MS_Description', N'原始ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_c_menuV2',
'COLUMN', N'original_id'
GO