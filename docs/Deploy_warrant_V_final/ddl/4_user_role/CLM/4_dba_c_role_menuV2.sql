CREATE TABLE [dbo].[dba_c_role_menuV2] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [menu_id] int  NULL,
  [role_id] int  NULL,
  [customer_id] int  NULL,
  [created_by] int  NULL,
  [updated_by] int  NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL,
  [is_deleted] int DEFAULT ((0)) NULL
)
GO

ALTER TABLE [dbo].[dba_c_role_menuV2] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Indexes structure for table dba_c_role_menuV2
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [PK__dba_role__3213E83FE3FD2412_copy2]
ON [dbo].[dba_c_role_menuV2] (
  [id] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dba_c_role_menuV2
-- ----------------------------
ALTER TABLE [dbo].[dba_c_role_menuV2] ADD CONSTRAINT [PK__dba_c_ro__3213E83F4D0E3F51_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

