CREATE TABLE [dbo].[dba_menuV2] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [icon] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [code] nvarchar(32) COLLATE Chinese_PRC_CI_AS  NULL,
  [name] nvarchar(32) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [level] int  NULL,
  [parent_id] int  NULL,
  [parent_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [url] nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [sort] int  NULL,
  [status] tinyint DEFAULT ((1)) NULL,
  [original_code] nvarchar(32) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] tinyint DEFAULT ((0)) NULL,
  [system] tinyint  NULL,
  [category_id] int  NULL,
  [is_category] int  NULL
)
GO

ALTER TABLE [dbo].[dba_menuV2] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'图标',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'icon'
GO

EXEC sp_addextendedproperty
'MS_Description', N'编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'级别',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'level'
GO

EXEC sp_addextendedproperty
'MS_Description', N'上级ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'parent_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'上级编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'parent_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'地址',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'url'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'sort'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'原始编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'original_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标志',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'系统标志',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'system'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品类ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'category_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否区分品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2',
'COLUMN', N'is_category'
GO

EXEC sp_addextendedproperty
'MS_Description', N'菜单',
'SCHEMA', N'dbo',
'TABLE', N'dba_menuV2'
GO


-- ----------------------------
-- Primary Key structure for table dba_menuV2
-- ----------------------------
ALTER TABLE [dbo].[dba_menuV2] ADD CONSTRAINT [PK__dba_menuV2__3213E83F92AC8435_copy1_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

