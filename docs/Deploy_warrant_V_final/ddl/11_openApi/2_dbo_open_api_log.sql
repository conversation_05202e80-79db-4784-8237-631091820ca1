CREATE TABLE [dbo].[dbo_open_api_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [open_app_id] int  NULL,
  [request_id] int  NULL,
  [request_url] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [request_data] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [response_data] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [request_time] datetime2(7)  NULL,
  [response_time] datetime2(7)  NULL,
  [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [msg] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [created_at] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[dbo_open_api_log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'日志ID',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'应用ID',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'open_app_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求ID',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'request_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求URL',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'request_url'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求数据',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'request_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'响应数据',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'response_data'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求时间',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'request_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'响应时间',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'response_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结果编码',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结果消息',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'msg'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'OPEN API 日志',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_api_log'
GO


-- ----------------------------
-- Primary Key structure for table dbo_open_api_log
-- ----------------------------
ALTER TABLE [dbo].[dbo_open_api_log] ADD CONSTRAINT [PK__dbo_open__3213E83F3947DD15] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

