CREATE TABLE [dbo].[dbo_open_app] (
  [id] int IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [cert_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [private_key] text COLLATE Chinese_PRC_CI_AS  NULL,
  [public_key] text COLLATE Chinese_PRC_CI_AS  NULL,
  [memo] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [is_deleted] int DEFAULT 0 NULL,
  [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT 0 NULL,
  [updated_at] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[dbo_open_app] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'应用ID',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'名称',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证书编号',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'cert_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'私钥',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'private_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'公钥',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'public_key'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'memo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除状态',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'应用',
'SCHEMA', N'dbo',
'TABLE', N'dbo_open_app'
GO


-- ----------------------------
-- Primary Key structure for table dbo_open_app
-- ----------------------------
ALTER TABLE [dbo].[dbo_open_app] ADD CONSTRAINT [PK__dbo_open__3213E83F3986FC55] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

