/*
 Navicat Premium Data Transfer

 Source Server         : INT
 Source Server Type    : SQL Server
 Source Server Version : 12005688 (12.00.5688)
 Source Host           : csm4invgsqs001.privatelink.database.chinacloudapi.cn:1433
 Source Catalog        : CSM4INVGSQL001
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 12005688 (12.00.5688)
 File Encoding         : 65001

 Date: 16/10/2024 06:33:27
*/


-- ----------------------------
-- Table structure for dbi_atlas_sync_record
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_sync_record]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_atlas_sync_record]
    GO

CREATE TABLE [dbo].[dbi_atlas_sync_record] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [request_id] int  NULL,
    [uuid] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [biz_id] int  NULL,
    [biz_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [object_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [operation_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [trade_type] int  NULL,
    [try_times] int  NULL,
    [sync_type] int  NULL,
    [business_doc_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [business_entity] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [atlas_request_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [atlas_results_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [sync_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [reprocessed] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [target_system] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [ack_time] datetime  NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [is_deleted] int DEFAULT '' NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [sub_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'关联的requestId',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'request_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'唯一编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'uuid'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'biz_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'biz_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'传输对象，区分本次接口传输的是合同、定价单',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'object_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'处理类型，区分本次接口传输的是新增、修改',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'operation_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求场景',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'trade_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'try_times'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步类型：1.同步 2.异步',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'sync_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'business_doc_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'所属公司/实体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'business_entity'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求atlas传输的信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'atlas_request_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'atlas返回的信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'atlas_results_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'sync_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否已经被重新传输或放弃 ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'reprocessed'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目标系统',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'target_system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接收到回执的时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'ack_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0:未删除 1:已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息传输用户',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息最后修改用户',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息传输时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口信息最后修改时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货委托在ATLAS CN的初步存在状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货委托在ATLAS CN的处理状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'sub_status'
    GO


    -- ----------------------------
-- Auto increment value for dbi_atlas_sync_record
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_atlas_sync_record]', RESEED, 1)
    GO

-- ----------------------------
-- Indexes structure for table dbi_atlas_sync_record
-- ----------------------------
    CREATE NONCLUSTERED INDEX [IX_BizCode]
    ON [dbo].[dbi_atlas_sync_record] (
      [biz_code] ASC
    )
    GO

-- ----------------------------
-- Primary Key structure for table dbi_atlas_sync_record
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_sync_record] ADD CONSTRAINT [PK__TableNam__3213E83F0A12D660] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

