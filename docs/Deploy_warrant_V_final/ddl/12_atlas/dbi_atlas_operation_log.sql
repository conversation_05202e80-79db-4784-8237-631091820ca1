/*
 Navicat Premium Data Transfer

 Source Server         : INT
 Source Server Type    : SQL Server
 Source Server Version : 12005688 (12.00.5688)
 Source Host           : csm4invgsqs001.privatelink.database.chinacloudapi.cn:1433
 Source Catalog        : CSM4INVGSQL001
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 12005688 (12.00.5688)
 File Encoding         : 65001

 Date: 16/10/2024 06:23:14
*/


-- ----------------------------
-- Table structure for dbi_atlas_operation_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_operation_log]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_atlas_operation_log]
    GO

CREATE TABLE [dbo].[dbi_atlas_operation_log] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [refer_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [biz_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [request_system] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [operation_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [operation_source] varchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [target_system] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [request_url] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [request_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [response_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [created_by] varchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'关联记录Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'refer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'biz_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'调用者系统',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'request_system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操怍类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'operation_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操怍来源',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'operation_source'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目标系统',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'target_system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求地址',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'request_url'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'request_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'response_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_operation_log',
    'COLUMN', N'created_by'
    GO


    -- ----------------------------
-- Auto increment value for dbi_atlas_operation_log
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_atlas_operation_log]', RESEED, 1)
    GO

-- ----------------------------
-- Indexes structure for table dbi_atlas_operation_log
-- ----------------------------
    CREATE NONCLUSTERED INDEX [IX_BizCode]
    ON [dbo].[dbi_atlas_operation_log] (
      [biz_code] ASC
    )
    GO

-- ----------------------------
-- Primary Key structure for table dbi_atlas_operation_log
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_operation_log] ADD CONSTRAINT [PK__dbi_atla__3213E83F4D6610C9] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

