/*
 Navicat Premium Data Transfer

 Source Server         : INT
 Source Server Type    : SQL Server
 Source Server Version : 12005688 (12.00.5688)
 Source Host           : csm4invgsqs001.privatelink.database.chinacloudapi.cn:1433
 Source Catalog        : CSM4INVGSQL001
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 12005688 (12.00.5688)
 File Encoding         : 65001

 Date: 16/10/2024 06:27:49
*/


-- ----------------------------
-- Table structure for dbi_atlas_sync_callback
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_sync_callback]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_atlas_sync_callback]
    GO

CREATE TABLE [dbo].[dbi_atlas_sync_callback] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [uuid] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [biz_id] int  NULL,
    [biz_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [try_times] int  NULL,
    [sync_type] int  NULL,
    [business_doc_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [ack_business_doc_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [ack_data] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [return_msg] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [ack_time] datetime  NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执消息里面的origMessageId',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'uuid'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'biz_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务单据号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'biz_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'try_times'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步类型：1.同步 2.异步',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'sync_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执消息里面的businessDocID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'business_doc_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执消息里面的ackBusinessDocId',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'ack_business_doc_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS系统回传信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'ack_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执信息（成功/失败+描述）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'return_msg'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接收到回执的时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_callback',
    'COLUMN', N'ack_time'
    GO


    -- ----------------------------
-- Auto increment value for dbi_atlas_sync_callback
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_atlas_sync_callback]', RESEED, 1)
    GO

-- ----------------------------
-- Indexes structure for table dbi_atlas_sync_callback
-- ----------------------------
    CREATE NONCLUSTERED INDEX [IX_BizCode]
    ON [dbo].[dbi_atlas_sync_callback] (
      [biz_code] ASC
    )
    GO

-- ----------------------------
-- Primary Key structure for table dbi_atlas_sync_callback
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_sync_callback] ADD CONSTRAINT [PK__dbi_atla__3213E83F7B2EB2ED] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO

