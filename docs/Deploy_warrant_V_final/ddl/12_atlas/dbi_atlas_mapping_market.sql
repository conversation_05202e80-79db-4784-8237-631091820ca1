/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 16/10/2024 14:01:04
*/


-- ----------------------------
-- Table structure for dbi_atlas_mapping_market
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_mapping_market]') AND type IN ('U'))
	DROP TABLE [dbo].[dbi_atlas_mapping_market]
GO

CREATE TABLE [dbo].[dbi_atlas_mapping_market] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [future_letters] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [fno_market] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbi_atlas_mapping_market] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'id主键 自增长',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货合约首字母',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'future_letters'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交易所名称',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'fno_market'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_market',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbi_atlas_mapping_market
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbi_atlas_mapping_market]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_mapping_market
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_mapping_market] ADD CONSTRAINT [PK__dbi_atla__3213E83FA257DAA4] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

