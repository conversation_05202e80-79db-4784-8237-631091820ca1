-- ----------------------------
-- Table structure for dbi_atlas_mapping_second_cost
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_mapping_second_cost]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_atlas_mapping_second_cost]
    GO

CREATE TABLE [dbo].[dbi_atlas_mapping_second_cost] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [delivery_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [delivery_type_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [price_detail_field] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [sync_action] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [bu_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [status] tinyint DEFAULT 1 NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'次级费用code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'次级费用名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'delivery_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'delivery_type_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'单价明细字段',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'price_detail_field'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步场景（所有场景-ALL，新增-CREATE，更新-Amendment，拆分-Splitting）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'sync_action'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线（所有-ALL，现货-ST，仓单-WT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'bu_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态（0.禁用 1.启用）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_second_cost',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dbi_atlas_mapping_second_cost
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_atlas_mapping_second_cost]', RESEED, 1)
    GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_mapping_second_cost
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_mapping_second_cost] ADD CONSTRAINT [PK__dbi_atla__3213E83F18DC7862] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    GO
