-- ----------------------------
-- Table structure for dbi_atlas_mapping_invoice
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_mapping_invoice]') AND type IN ('U'))
	DROP TABLE [dbo].[dbi_atlas_mapping_invoice]
GO

CREATE TABLE [dbo].[dbi_atlas_mapping_invoice] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [invoice_mdm_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [tax_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [tax_rate] decimal(25,6) DEFAULT ((0)) NULL,
  [contract_category_type] int  NULL,
  [status] tinyint DEFAULT ((1)) NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL
)
GO

ALTER TABLE [dbo].[dbi_atlas_mapping_invoice] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'发票类型的mdm编码',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'invoice_mdm_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'税率编码',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'tax_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'税率',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'tax_rate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同类型：1.现货 2.仓单 3.豆二',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'contract_category_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态（0.禁用 1.启用）',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_invoice',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbi_atlas_mapping_invoice
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbi_atlas_mapping_invoice]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_mapping_invoice
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_mapping_invoice] ADD CONSTRAINT [PK__dbi_atla__3213E83FDBA7C9AA] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

