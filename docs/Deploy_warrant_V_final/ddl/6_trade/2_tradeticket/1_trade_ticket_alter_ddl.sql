-- 新增脚本 TT  dbt_trade_ticket dbt_tt_add dbt_tt_modify
-- dbt_trade_ticket

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category3'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [goods_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'goods_id'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [goods_name] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'goods_name'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'contract_nature'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [site_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [site_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'site_name'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'commodity_name'
    GO

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'is_soybean2'
    GO
-- dbt_tt_add
ALTER TABLE [dbo].[dbt_tt_add] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'ship_warehouse_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [settle_type] varchar(255)  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'结算方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'settle_type'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [write_off_start_time]  datetime NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销周期开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'write_off_start_time'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [write_off_end_time]  datetime  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销周期结束时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'write_off_end_time'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_id'
    GO


ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单Code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_code'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'期货代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_type] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_file_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_remark] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_remark'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [deposit_payment_type] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'交割保证金付款方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'deposit_payment_type'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [delivery_margin_amount] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'交割保证金金额',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'delivery_margin_amount'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'category'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_category'
    GO

-- dbt_tt_modify

ALTER TABLE [dbo].[dbt_tt_modify] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'ship_warehouse_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [price_detail_info] ntext DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'价格详情Json',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'price_detail_info'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [is_modify_all] int DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否同时修改货品和提货方',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'is_modify_all'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'提货主体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'delivery_id'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_password] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'提货密码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'delivery_password'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_date]  datetime NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销日期',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_date'
    GO


ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_delivery_start_time]  datetime NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销交提货开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_delivery_start_time'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_delivery_end_time]  datetime  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销交提货结束时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_delivery_end_time'
    GO


ALTER TABLE [dbo].[dbt_tt_modify] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'期货编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'future_code'
    GO


ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_trade_type] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_num] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_num'
    GO



ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'warrant_id'
    GO


ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单Code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'warrant_code'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'category'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'warrant_category'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [settle_type] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'结算方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'settle_type'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [survey_fees] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'survey_fees'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [refine_frac_diff_price] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'refine_frac_diff_price'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [ve_price] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'VE单价',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N've_price'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [ve_content] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'VE含量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N've_content'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [standard_type] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [standard_file_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_tt_modify] ADD [standard_remark] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'standard_remark'
    GO
--dbt_tt_tranfer

ALTER TABLE [dbo].[dbt_tt_tranfer] ADD [this_fee]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'本次手续费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_tranfer',
    'COLUMN', N'this_fee'
    GO

--dbt_contract_price
ALTER TABLE [dbo].[dbt_contract_price] ADD [survey_fees] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N'survey_fees'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [refine_frac_diff_price] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N'refine_frac_diff_price'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [ve_price]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'VE单价',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N've_price'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [ve_content]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'VE含量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N've_content'
    GO
