配置命名空间名称：navigator-koala.yaml

server:
  port: 9101
spring:
  application:
    name: navigator-koala-service
  datasource:
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: **************************************************************
    username: SA
    password: 211aAT518
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  config: classpath:log/logback.xml
  level:
    com.ideaProject.mapper : DEBUG
    # 去除nacos心跳日志
    com.alibaba.nacos.client.config.impl: WARN