
-- 销售合同导入表
ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [warehouse_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [refine_frac_diff_price] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [survey_fees] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [commodity_full_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [site_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [standard_type] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [standard_file_no] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [standard_remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [usage] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_sales_contract] ADD [bu_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'warehouse_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'refine_frac_diff_price'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'survey_fees'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'future_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品全称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'commodity_full_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'site_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'standard_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'standard_file_no'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'standard_remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'usage'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_sales_contract',
    'COLUMN', N'bu_code'
    GO

-- 采购合同导入表
ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [warehouse_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [refine_frac_diff_price] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [survey_fees] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [commodity_full_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [site_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [standard_type] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [standard_file_no] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [standard_remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [usage] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_lkg_purchase_contract] ADD [bu_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'warehouse_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'refine_frac_diff_price'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'survey_fees'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'future_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品全称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'commodity_full_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'site_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'standard_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'standard_file_no'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'standard_remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用途',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'usage'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_lkg_purchase_contract',
    'COLUMN', N'bu_code'
    GO

-- 预导入合同表
ALTER TABLE [dbo].[dbi_prepare_contract] ADD [pay_condition_id] int  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [company_id] int  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [company_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [site_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [site_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [survey_fees] decimal(15,6) DEFAULT 0 NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [refine_frac_diff_price] decimal(15,6) DEFAULT 0 NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [commodity_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [destination_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [package_weight_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [delivery_type_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [weight_check_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [invoice_type_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [ship_warehouse_value] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [bu_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [standard_type] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [standard_file_id] int DEFAULT 0 NULL
    GO

ALTER TABLE [dbo].[dbi_prepare_contract] ADD [standard_remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'付款条件id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'pay_condition_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体Id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'company_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'site_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'site_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'survey_fees'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'refine_frac_diff_price'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'future_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'commodity_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'destination_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'package_weight_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'delivery_type_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'weight_check_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'invoice_type_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'ship_warehouse_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'bu_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'standard_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'standard_file_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_prepare_contract',
    'COLUMN', N'standard_remark'
    GO
