
--TODO dbt_contract

ALTER TABLE [dbo].[dbt_contract] ADD [origin_contract_type] int  NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'原合同类型 ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'origin_contract_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [contract_close_type] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [warrant_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_cancel_count'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'is_soybean2'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单标识ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_status] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销状态(1.未注销 2.注销中 3.已注销)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_status'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_start_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_start_time'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_end_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销截止时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_end_time'
    GO


ALTER TABLE [dbo].[dbt_contract] ADD [settle_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'结算类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'settle_type'
    GO


ALTER TABLE [dbo].[dbt_contract] ADD [exchange_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'exchange_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_file_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_name'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [delivery_mode] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'delivery_mode'
    GO


ALTER TABLE [dbo].[dbt_contract] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'commodity_name'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_remark] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_remark'
    GO



-- todo dbt_contract_history

ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_close_type] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_cancel_count'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'is_soybean2'
    GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单标识ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_status] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销状态(1.未注销 2.注销中 3.已注销)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_status'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_start_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_start_time'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_end_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销截止时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_end_time'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [settle_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'结算类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'settle_type'
    GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [exchange_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'exchange_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_file_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_remark] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_remark'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_name'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_mode] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_mode'
    GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'commodity_name'
    GO

