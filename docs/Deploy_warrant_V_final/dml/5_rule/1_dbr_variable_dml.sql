--初始化规则正本变量信息
TRUNCATE TABLE dbr_variable;
SET IDENTITY_INSERT [dbo].[dbr_variable] ON
GO
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (27, 'category1', '一级品类', '一级品类', 1, 1, 1, 0, 0, 0, 0, '', '==,!=,contains,not contains', 'select', 'SIGN_PAPER', 1, 0, 0, '豆粕/豆油', '2024-09-13 07:54:31.510', '2024-09-13 07:54:31.510', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (28, 'category2', '二级品类', '二级品类', 1, 1, 1, 0, 0, 0, 0, '', '==,!=,contains,not contains', 'select', 'SIGN_PAPER', 1, 0, 0, '豆粕/豆油', '2024-09-13 07:54:31.537', '2024-09-13 07:54:31.537', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (29, 'category3', '三级品类', '三级品类', 1, 1, 1, 0, 0, 0, 0, '', '==,!=,contains,not contains', 'select', 'SIGN_PAPER', 1, 0, 0, '豆粕/豆油', '2024-09-13 07:54:31.553', '2024-09-13 07:54:31.553', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (30, 'buCode', '业务线', '业务线', 2, 1, 1, 0, 0, 1, 0, 'com.navigator.bisiness.enums.BuCodeEnum', '==,!=,contains,not contains', 'select', 'SIGN_PAPER', 1, 0, 0, '期货/现货，现有合同相关业务均为现货', '2024-04-16 06:30:01.360', '2024-04-16 06:30:01.360', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (31, 'companyCode', '所属主体', '所属主体', 2, 1, 1, 0, 0, 0, 0, '', '==,!=,contains,not contains', 'select', 'SIGN_PAPER', 1, 0, 0, '签约主体', '2024-04-16 06:30:01.427', '2024-04-16 06:30:01.427', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (32, 'salesType', '采销类型', '采销类型', 1, 1, 1, 0, 0, 1, 0, 'com.navigator.bisiness.enums.ContractSalesTypeEnum', '==,!=', 'select', 'SIGN_PAPER', 1, 0, 0, '1采购2销售', '2024-04-16 06:30:01.510', '2024-04-16 06:30:01.510', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (33, 'diffdiveryEndMonth', '交货截止日期-签订日期月份', '交货截止日期-签订日期月份', 1, 1, 1, 0, 0, 0, 0, '', '==,!=,>,>=,<,<=', 'input', 'SIGN_PAPER', 1, 0, 0, '交货截止日期-签订日期', '2024-04-16 06:30:01.427', '2024-04-16 06:30:01.427', N'', NULL);
INSERT INTO [dbo].[dbr_variable] ([id], [code], [name], [display_name], [value_type], [is_condition], [is_key], [is_constant], [has_dict], [is_enum], [is_logic], [enum_path], [pattern_relations], [input_type], [module_type], [system_id], [sort], [is_deleted], [memo], [created_at], [updated_at], [updated_by], [created_by]) VALUES (34, 'contractNum', '总数量', '总数量 ', 1, 1, 1, 0, 0, 0, 0, '', '==,!=,>,>=,<,<=', 'input', 'SIGN_PAPER', 1, 0, 0, '总数量', '2024-04-16 06:30:01.427', '2024-04-16 06:30:01.427', N'', NULL);
SET IDENTITY_INSERT [dbo].[dbr_variable] OFF
GO