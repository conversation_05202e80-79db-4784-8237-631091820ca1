-- 插入品种数据：豆粕、豆油
SET IDENTITY_INSERT [dbo].[dbg_category] ON
GO
INSERT INTO [dbo].[dbg_category]( [id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no]) VALUES (N'23',11, N'豆粕', N'SBM', N'1', 3, 0, N'1', 0, getdate(), getdate(), 0.55, 20, 0, 0, 1, 'Link-SBM', 'Link-豆粕', '油豆',  '110');
GO
INSERT INTO [dbo].[dbg_category]( [id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no]) VALUES (N'24',12, N'豆油', N'SBO', N'1', 3, 0, N'1', 0, getdate(), getdate(), 0.22, 30, 0, 0, 1, 'Link-SBO', 'Link-豆油', '油豆', '120');
GO
SET IDENTITY_INSERT [dbo].[dbg_category] OFF
GO

-- 更新品类主表serial_no为id
UPDATE dbg_category SET serial_no = id;

-- 插入品种数据：特种油脂、特种蛋白、副产品、豆二
SET IDENTITY_INSERT [dbo].[dbg_category] ON
GO

INSERT INTO [dbo].[dbg_category] ([id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no], [is_dce], [is_soybean2], [future_code], [created_by], [updated_by], [nav_id], [is_split_contract]) VALUES (N'25', N'10', N'特种油脂', N'SBNB', N'1', N'2', N'1', N'1', N'0', N'2021-12-02 19:44:22.000', N'2024-09-04 14:46:43.083', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'25', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
GO

INSERT INTO [dbo].[dbg_category] ([id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no], [is_dce], [is_soybean2], [future_code], [created_by], [updated_by], [nav_id], [is_split_contract]) VALUES (N'26', N'10', N'特种蛋白', N'SP', N'1', N'2', N'0', N'1', N'0', N'2024-09-04 08:05:14.000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'26', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
GO

INSERT INTO [dbo].[dbg_category] ([id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no], [is_dce], [is_soybean2], [future_code], [created_by], [updated_by], [nav_id], [is_split_contract]) VALUES (N'27', N'10', N'副产品', N'BP', N'1', N'2', N'0', N'1', N'0', N'2024-09-04 08:05:17.000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, N'27', NULL, NULL, NULL, NULL, NULL, NULL, NULL)
GO

INSERT INTO [dbo].[dbg_category] ([id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no], [is_dce], [is_soybean2], [future_code], [created_by], [updated_by], [nav_id], [is_split_contract]) VALUES (N'28', N'10', N'豆二', N'SBNB', N'1', N'2', N'0', N'1', N'0', N'2024-09-04 08:05:20.000', NULL, N'.55', N'20', N'.000000', N'.000000', N'1', N'Link-SBNB', N'Link-豆二', N'豆二', N'28', N'0', N'1', N'B', NULL, N'1', NULL, N'1')
GO

SET IDENTITY_INSERT [dbo].[dbg_category] OFF
GO


-- ----------------------------
-- Auto increment value for dbg_category
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbg_category]', RESEED, 28)
GO

-- 二级品类改名称
UPDATE dbg_category set name = '油脂油料' WHERE id = 10;
