update dba_customer set mdm_customer_code = linkage_customer_code;

update dba_contact_factory set factory_code = (select code FROM dba_factory where id = dba_contact_factory.factory_id) where factory_code  is null;

update dba_bank_factory set factory_code = (select code FROM dba_factory where id = dba_bank_factory.factory_id) where factory_code  is null;

-- 客户的TradeStatus初始化
UPDATE c set c.trade_status = cs.TRADE_STATUS
    FROM dba_customer c
JOIN BI.CRIS cs on c.linkage_customer_code = cs.CP_ID;


--客户银行账号
UPDATE dba_customer_bank
SET category1 = ',10,', category2 = ',11,' where category_id = 11;
UPDATE dba_customer_bank
SET category1 = ',10,', category2 = ',12,' where category_id = 12;

--客户-通知人
update dba_contact set category1 = ',10,',category2 = ',11,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 11;
update dba_contact set category1 = ',10,',category2 = ',12,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 12;

-- 客户-赊销&预付
update dba_customer_credit_payment set category1 = ',10,',category2 = ',11,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 11;
update dba_customer_credit_payment set category1 = ',10,',category2 = ',12,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 12;
update dba_customer_credit_payment set  bu_code = 'ST';

---客户赊销预付/履约保证金更新业务线
UPDATE dba_customer_deposit_rate
SET category1 = ',10,', category2 = ',11,' where category_id = 11;
UPDATE dba_customer_deposit_rate
SET category1 = ',10,', category2 = ',12,' where category_id = 12;
update dba_customer_deposit_rate set bu_code = 'ST';

--客户配置
update dba_customer_detail set category1 = ',10,',category2 = ',11,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 11;
update dba_customer_detail set category1 = ',10,',category2 = ',12,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 12;

---- 客户正本
update dba_customer_original_paper set category1 = ',10,',category2 = ',11,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 11;
update dba_customer_original_paper set category1 = ',10,',category2 = ',12,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 12;

---- 客户框架协议配置
update dba_customer_protocol  set category1 = ',10,',category2 = ',11,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 11;
update dba_customer_protocol  set category1 = ',10,',category2 = ',12,',category3 = ',' + (SELECT STRING_AGG(id, ',') AS ids FROM dbg_category WHERE parent_id = category_id) + ',' where category_id = 12;

----迁移客户发票配置
INSERT INTO dba_customer_invoice (customer_id,invoice_id,invoice_name,category1,category2,category3,company_id,company_name,created_by,created_at,updated_by,updated_at)
SELECT customer_id,invoice_id,invoice_name,category1,category2,category3,company_id,company_name,created_by_name,created_at,updated_by_name,updated_at
from dba_customer_detail
where invoice_id <> 0;


UPDATE dba_customer_invoice
SET company_name = c.short_name
    from dba_customer_invoice i
join dba_company c ON i.company_id = c.id;

---迁移客户评级配置
INSERT INTO dba_customer_grade_score (customer_id,grade_score,category1,category2,category3,created_by,created_at,updated_by,updated_at)
SELECT customer_id,grade_score,category1,category2,category3,created_by_name,created_at,updated_by_name,updated_at
from dba_customer_detail
where grade_score is not null;

---账户信息工厂code
update dba_bank_factory
set factory_code = f.code
    from dba_bank_factory b
INNER JOIN dba_factory f on b.factory_id = f.id;

update dba_customer_deposit_rate set bu_code = 'ST';

update dba_customer_credit_payment set  bu_code = 'ST';



