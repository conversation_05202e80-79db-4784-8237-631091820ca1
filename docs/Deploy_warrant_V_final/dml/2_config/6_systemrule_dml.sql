-- 1、初始化提货委托文件配置
INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( 0, 0, 'S0022', '提货委托文件配置', 0, 1, GETDATE(), GETDATE());
DECLARE @ruleId int = ( select id from dbz_system_rule where code = 'S0022');
INSERT INTO [dbo].[dbz_system_rule_item] ([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id], [mdm_code]) VALUES (@ruleId, '提货委托文件配置', 2, 0, 0, 1, GETDATE(), GETDATE(), '', 1, 1, N'MGL', 'https://csm4nnvgsto001.blob.core.chinacloudapi.cn/dev/dev/fileRecord/upload/file/magellan/2024-10-09/4f18335b15b8462d9348aa0ccd0f9df1_MGL提货委托申请下载模版.xlsx', N'', N'admin1', N'admin1', NULL, NULL, N'');
INSERT INTO [dbo].[dbz_system_rule_item] ([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id], [mdm_code]) VALUES (@ruleId, '提货委托文件配置', 2, 0, 0, 1, GETDATE(), GETDATE(), '', 1, 1, N'CLB', 'https://csm4nnvgsto001.blob.core.chinacloudapi.cn/dev/dev/fileRecord/upload/file/magellan/2024-10-09/4c8a8def986a486fb1a9f15784d1e735_CLB提货委托申请下载模版.xlsx', N'', N'admin1', N'admin1', NULL, NULL, N'');

-- 2、豆二注销比例
DECLARE @LastInsertedID INT;
DECLARE @DouErID INT;
DECLARE @DouPoID INT;
DECLARE @DouYouID INT;
-- 豆二ID
SET @DouErID=28;
-- 豆粕ID
SET @DouPoID=23;
-- 豆油ID
SET @DouYouID=24;
-- 豆二注销比例主表-S1005
INSERT INTO [dbo].[dbz_system_rule]([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( @DouErID, 0, 'S1005', '豆二注销比例', 0, 1, getdate(), getdate());
SET @LastInsertedID = (select id from dbz_system_rule where code = 'S1005' and category_id = @DouErID);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id]) VALUES ( @LastInsertedID, 'S1005', 3, 0, 0, 1, getdate(), getdate(), '豆粕', 1, 1, @DouPoID, '0.785', NULL, N'admin1', N'admin1', N'0', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id]) VALUES ( @LastInsertedID, 'S1005', 3, 0, 0, 1, getdate(), getdate(), '豆油', 1, 1, @DouYouID, '0.185', NULL, N'admin1', N'admin1', N'0', NULL);

-- 3、变更袋皮扣重的包装名
UPDATE dbz_system_rule SET name = '袋装-50KG' WHERE name = '50KG' AND parent_id = 14;
UPDATE dbz_system_rule SET name = '袋装-70KG' WHERE name = '70KG' AND parent_id = 14;