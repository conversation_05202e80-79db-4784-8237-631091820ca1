
-- TODO dbt_contract
-- 合同的数据割接操作  contract_close_type = b.contract_close_type,
UPDATE dbt_contract
SET
    category1 = b.category1,
    category2 = b.category2,
    category3 = b.category3,
    bu_code = 'ST',
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_contract a,
	dbg_goods b
WHERE
    a.goods_id = b.id;

-- 合同值对象
-- 目的地
update dbt_contract set destination_value = b.rule_key from dbt_contract a , dbz_system_rule_item b where a.destination = b.id ;
-- 袋皮扣重
update dbt_contract set package_weight_value = b.rule_key from dbt_contract a , dbz_system_rule_item b where a.package_weight = b.id ;
-- 交提货方式
update dbt_contract set delivery_type_value = b.name from dbt_contract a , dbt_delivery_type b where a.delivery_type = b.id ;
-- 发票类型
update dbt_contract set invoice_type_value = b.rule_key from dbt_contract a , dbz_system_rule_item b where a.invoice_type = b.id ;
update dbt_contract set invoice_type_value = '增值税普通发票,电子'  where invoice_type_value = '1';
update dbt_contract set invoice_type_value = '增值税普通发票,纸质'  where invoice_type_value = '2';
update dbt_contract set invoice_type_value = '增值税专用发票,电子'  where invoice_type_value = '3';
update dbt_contract set invoice_type_value = '增值税专用发票,纸质'  where invoice_type_value = '4';
update dbt_contract set invoice_type_value = '增值税普通发票,全电'  where invoice_type_value = '5';
update dbt_contract set invoice_type_value = '增值税专用发票,全电'  where invoice_type_value = '6';
-- 重量检验
update dbt_contract set weight_check_value = b.rule_key from dbt_contract a , dbz_system_rule_item b where a.weight_check = b.id ;
-- 发货库点
update dbt_contract set ship_warehouse_value = b.name from dbt_contract a , dba_factory_warehouse b where a.ship_warehouse_id = b.id ;

--更新合同表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新合同表 品种代码信息
update dbt_contract set future_code = 'M' where category2 = 11;
update dbt_contract set future_code = 'Y' where category2 = 12;
--更新合同表 货品信息
update dbt_contract set commodity_name = goods_name WHERE commodity_name is null;

-- TODO dbt_contract_history
-- todo:fix 合同历史表，关联商品
UPDATE dbt_contract_history
SET category1 = b.category1, category2 = b.category2, category3 = b.category3 ,bu_code = 'ST',
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_contract_history a,
	dbg_goods b
WHERE
    a.goods_id = b.id;

-- 合同值对象
-- 目的地
update dbt_contract_history set destination_value = b.rule_key from dbt_contract_history a , dbz_system_rule_item b where a.destination = b.id ;
-- 袋皮扣重
update dbt_contract_history set package_weight_value = b.rule_key from dbt_contract_history a , dbz_system_rule_item b where a.package_weight = b.id ;
-- 交提货方式
update dbt_contract_history set delivery_type_value = b.name from dbt_contract_history a , dbt_delivery_type b where a.delivery_type = b.id ;
-- 发票类型
update dbt_contract_history set invoice_type_value = b.rule_key from dbt_contract_history a , dbz_system_rule_item b where a.invoice_type = b.id ;
update dbt_contract_history set invoice_type_value = '增值税普通发票,电子'  where invoice_type_value = '1';
update dbt_contract_history set invoice_type_value = '增值税普通发票,纸质'  where invoice_type_value = '2';
update dbt_contract_history set invoice_type_value = '增值税专用发票,电子'  where invoice_type_value = '3';
update dbt_contract_history set invoice_type_value = '增值税专用发票,纸质'  where invoice_type_value = '4';
update dbt_contract_history set invoice_type_value = '增值税普通发票,全电'  where invoice_type_value = '5';
update dbt_contract_history set invoice_type_value = '增值税专用发票,全电'  where invoice_type_value = '6';
-- 重量检验
update dbt_contract_history set weight_check_value = b.rule_key from dbt_contract_history a , dbz_system_rule_item b where a.weight_check = b.id ;
-- 发货库点
update dbt_contract_history set ship_warehouse_value = b.name from dbt_contract_history a , dba_factory_warehouse b where a.ship_warehouse_id = b.id ;


--更新合同表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract_history tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新合同表 品种代码信息
update dbt_contract_history set future_code = 'M' where category2 = 11;
update dbt_contract_history set future_code = 'Y' where category2 = 12;
--更新合同表 货品信息
update dbt_contract_history set commodity_name = goods_name WHERE commodity_name is null;