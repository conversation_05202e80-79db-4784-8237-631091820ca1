-- 数据脚本割接更新
-- todo dbt_tt_add

-- 目的地
update dbt_tt_add set destination_value = b.rule_key from dbt_tt_add a , dbz_system_rule_item b where a.destination = b.id;
-- 袋皮扣重
update dbt_tt_add set package_weight_value = b.rule_key from dbt_tt_add a , dbz_system_rule_item b where a.package_weight = b.id ;
-- 交提货方式
update dbt_tt_add set delivery_type_value = b.name from dbt_tt_add a , dbt_delivery_type b where a.delivery_type = b.id ;
-- 发票类型
update dbt_tt_add set invoice_type_value = b.rule_key from dbt_tt_add a , dbz_system_rule_item b where a.invoice_type = b.id ;
update dbt_tt_add set invoice_type_value = '增值税普通发票,电子'  where invoice_type_value = '1';
update dbt_tt_add set invoice_type_value = '增值税普通发票,纸质'  where invoice_type_value = '2';
update dbt_tt_add set invoice_type_value = '增值税专用发票,电子'  where invoice_type_value = '3';
update dbt_tt_add set invoice_type_value = '增值税专用发票,纸质'  where invoice_type_value = '4';
update dbt_tt_add set invoice_type_value = '增值税普通发票,全电'  where invoice_type_value = '5';
update dbt_tt_add set invoice_type_value = '增值税专用发票,全电'  where invoice_type_value = '6';
-- 重量检验
update dbt_tt_add set weight_check_value = b.rule_key from dbt_tt_add a , dbz_system_rule_item b where a.weight_check = b.id ;
-- 发货库点
update dbt_tt_add set ship_warehouse_value = b.name from dbt_tt_add a , dba_factory_warehouse b where a.ship_warehouse_id = b.id ;
-- 品种代码信息
update dbt_trade_ticket set future_code = 'M' where goods_category_id = 11;
update dbt_trade_ticket set future_code = 'Y' where goods_category_id = 12;

-- todo dbt_tt_modify

-- 目的地
update dbt_tt_modify set destination_value = b.rule_key from dbt_tt_modify a , dbz_system_rule_item b where a.destination = b.id ;
-- 袋皮扣重
update dbt_tt_modify set package_weight_value = b.rule_key from dbt_tt_modify a , dbz_system_rule_item b where a.package_weight = b.id ;
-- 交提货方式
update dbt_tt_modify set delivery_type_value = b.name from dbt_tt_modify a , dbt_delivery_type b where a.delivery_type = b.id ;
-- 发票类型
update dbt_tt_modify set invoice_type_value = b.rule_key from dbt_tt_modify a , dbz_system_rule_item b where a.invoice_type = b.id ;
update dbt_tt_modify set invoice_type_value = '增值税普通发票,电子'  where invoice_type_value = '1';
update dbt_tt_modify set invoice_type_value = '增值税普通发票,纸质'  where invoice_type_value = '2';
update dbt_tt_modify set invoice_type_value = '增值税专用发票,电子'  where invoice_type_value = '3';
update dbt_tt_modify set invoice_type_value = '增值税专用发票,纸质'  where invoice_type_value = '4';
update dbt_tt_modify set invoice_type_value = '增值税普通发票,全电'  where invoice_type_value = '5';
update dbt_tt_modify set invoice_type_value = '增值税专用发票,全电'  where invoice_type_value = '6';
-- 重量检验
update dbt_tt_modify set weight_check_value = b.rule_key from dbt_tt_modify a , dbz_system_rule_item b where a.weight_check = b.id ;
-- 发货库点
update dbt_tt_modify set ship_warehouse_value = b.name from dbt_tt_modify a , dba_factory_warehouse b where a.ship_warehouse_id = b.id ;
-- 价格详情Json
update dbt_tt_modify set price_detail_info = (select * from dbt_contract_price b where dbt_tt_modify.tt_id = b.tt_id  FOR JSON AUTO);
-- 品种代码信息
update dbt_trade_ticket set future_code = 'M' where goods_category_id = 11;
update dbt_trade_ticket set future_code = 'Y' where goods_category_id = 12;

-- todo dbt_tt_tranfer 本次手续费 新增字段（协议表迁移过来的）转月

UPDATE dbt_tt_tranfer
SET this_fee = b.this_time_fee FROM
	dbt_tt_tranfer a,
	dbt_contract_sign b
WHERE
    a.tt_id = b.tt_id
  and b.tt_type in (4,5);

-- todo: dbt_trade_ticket 根据TT类型，从子表的goods_id 进行关联 7,10,14

-- 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_trade_ticket tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;

-- 货品信息和默认字段信息,需要确认商品的信息ID是对的
UPDATE dbt_trade_ticket
SET bu_code = 'ST',
    category1 = b.category1,
    category2 = b.category2,
    category3 = b.category3,
    goods_id = b.id,
    goods_name = b.NAME,
    commodity_name = b.NAME,
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_trade_ticket a,
	dbt_tt_add c,
	dbg_goods b
WHERE
    a.id = c.tt_id
  and a.type in (1,8,9,11,13)
  AND c.goods_id = b.id;

UPDATE dbt_trade_ticket
SET bu_code = 'ST',
    category1 = b.category1,
    category2 = b.category2,
    category3 = b.category3,
    goods_id = b.id,
    goods_name = b.NAME,
    commodity_name = b.NAME,
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_trade_ticket a,
	dbt_tt_modify c,
	dbg_goods b
WHERE
    a.id = c.tt_id
  and a.type in (2,3)
  AND c.goods_id = b.id;

UPDATE dbt_trade_ticket
SET bu_code = 'ST',
    category1 = b.category1,
    category2 = b.category2,
    category3 = b.category3,
    goods_id = b.id,
    goods_name = b.NAME,
    commodity_name = b.NAME,
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_trade_ticket a,
	dbt_tt_price c,
	dbg_goods b
WHERE
    a.id = c.tt_id
  and a.type in (6,12)
  AND c.goods_id = b.id;

UPDATE dbt_trade_ticket
SET bu_code = 'ST',
    category1 = b.category1,
    category2 = b.category2,
    category3 = b.category3,
    goods_id = b.id,
    goods_name = b.NAME,
    commodity_name = b.NAME,
    contract_nature = 1,
    is_soybean2 = 0
    FROM
	dbt_trade_ticket a,
	dbt_tt_tranfer c,
	dbg_goods b
WHERE
    a.id = c.tt_id
  and a.type in (4,5)
  AND c.goods_id = b.id;


--更新TT主表 品种代码信息
update dbt_trade_ticket set future_code = 'M' where category2 = 11;
update dbt_trade_ticket set future_code = 'Y' where category2 = 12;