--修改协议数据
UPDATE dbt_contract_sign
SET provide_type = ( CASE dbt_contract_sign.ldc_frame WHEN '0' THEN 'FILE_UPLOAD' WHEN '1' THEN 'HUSKY_CONTRACT' ELSE 'NOT_PROVIDE' END ),
    need_review = 1,
    ldc_signature_type = ( CASE dbt_contract_sign.ldc_frame WHEN '0' THEN 'NORMAL' WHEN '1' THEN 'SILENCE' ELSE 'OFFLINE_SIGN' END ),
    contract_sign_type = ( CASE dbt_contract_sign.trade_type WHEN '103' THEN '101' WHEN '105' THEN '104' WHEN '112' THEN '111' WHEN '151' THEN '101' ELSE dbt_contract_sign.trade_type END ),
    termine_type = dbt_contract_sign.sign_type,
    is_soybean2 = 0,
    bu_code = 'ST';

--品类商品参数
UPDATE dbt_contract_sign
SET dbt_contract_sign.category1 = dbg_goods.category1,
    dbt_contract_sign.category2 = dbg_goods.category2,
    dbt_contract_sign.category3 = dbg_goods.category3,
    dbt_contract_sign.commodity_name = dbg_goods.name
    from dbt_contract_sign
JOIN dbg_goods ON dbt_contract_sign.goods_id = dbg_goods.id;

--更新协议主表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract_sign tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;

--更新协议表 品种代码信息
update dbt_contract_sign set future_code = 'M' where category2 = 11 and future_code is null;
update dbt_contract_sign set future_code = 'Y' where category2 = 12 and future_code is null;

--ws参数塞入
UPDATE sign
SET sign.ws_type = (CASE
                        WHEN c.is_columbus = '0' AND c.use_yqq = '0' THEN '2'
                        WHEN c.is_columbus = '1' AND c.use_yqq = '0' THEN '1'
                        WHEN c.is_columbus = '1' AND c.use_yqq = '1' THEN '0'
    END)
    from ( SELECT ( CASE WHEN sales_type = 2 THEN customer_id WHEN sales_type = 1 THEN supplier_id END ) AS use_customer_id,* FROM dbt_contract_sign ) sign
JOIN dba_customer c ON sign.use_customer_id = c.id;


--客户开通易企签，customer_signature_type 1：易企签 2：线下签署 0：不签署 现在没有不签署的
UPDATE sign
SET sign.customer_signature_type = (CASE c.use_yqq WHEN '1' THEN 'YQQ_SIGN' ELSE 'OFFLINE_SIGN' END)
    from ( SELECT ( CASE WHEN sales_type = 2 THEN customer_id WHEN sales_type = 1 THEN supplier_id END ) AS use_customer_id,* FROM dbt_contract_sign ) sign
JOIN dba_customer c ON sign.use_customer_id = c.id;