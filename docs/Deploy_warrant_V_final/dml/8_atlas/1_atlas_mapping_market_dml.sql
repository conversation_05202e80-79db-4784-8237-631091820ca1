-- ----------------------------
-- Table structure for dbi_atlas_mapping_market
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_mapping_market]') AND type IN ('U'))
DROP TABLE [dbo].[dbi_atlas_mapping_market]
    GO

CREATE TABLE [dbo].[dbi_atlas_mapping_market] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [future_letters] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [fno_market] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL
    )
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_market] SET (LOCK_ESCALATION = TABLE)
    GO


    -- ----------------------------
-- Records of dbi_atlas_mapping_market
-- ----------------------------
    SET IDENTITY_INSERT [dbo].[dbi_atlas_mapping_market] ON
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'1', N'M', N'DCEMEMT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'2', N'Y', N'DCEBOMT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'3', N'B', N'DCESOY2MT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'4', N'P', N'DCEPOMT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'5', N'RM', N'ZCERMEMT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'6', N'OI', N'ZCEROILMT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    INSERT INTO [dbo].[dbi_atlas_mapping_market] ([id], [future_letters], [fno_market], [remark], [created_at], [updated_at]) VALUES (N'7', N'A', N'DCESOY1MT', N'', N'2025-03-01 07:42:16.353', N'2025-03-01 07:42:16.353')
    GO

    SET IDENTITY_INSERT [dbo].[dbi_atlas_mapping_market] OFF
    GO


    -- ----------------------------
-- Auto increment value for dbi_atlas_mapping_market
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbi_atlas_mapping_market]', RESEED, 7)
    GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_mapping_market
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_mapping_market] ADD CONSTRAINT [PK__dbi_atla__3213E83FB1B36138] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

