-- 更新发票类型配置
UPDATE dbz_system_rule_item
SET
    mdm_code = CASE
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税专用发票,纸质' THEN '100'
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税普通发票,纸质' THEN '101'
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税普通发票,电子' THEN '102'
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税专用发票,电子' THEN '103'
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税专用发票,全电' THEN '104'
                   WHEN CAST(memo AS VARCHAR(MAX)) = '增值税普通发票,全电' THEN '105'
                   ELSE mdm_code
        END,
    status = CASE
                 WHEN CAST(memo AS VARCHAR(MAX)) = '增值税普通发票,电子' THEN 0
                 WHEN CAST(memo AS VARCHAR(MAX)) = '增值税专用发票,电子' THEN 0
                 ELSE status
        END
WHERE code = 'S0009';

-- 增加atlas发票关联表
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'100', N'E2', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'101', N'E1', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'104', N'E3', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'105', N'E4', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'104', N'F3', 1, 1, GETDATE(), GETDATE(), 0.13);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'105', N'F4', 1, 1, GETDATE(), GETDATE(), 0.13);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'100', N'F2', 1, 1, GETDATE(), GETDATE(), 0.13);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'101', N'F1', 1, 1, GETDATE(), GETDATE(), 0.13);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'100', N'E9', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'101', N'E9', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'104', N'E9', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'105', N'E9', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'100', N'E9', 3, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'101', N'E9', 3, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'104', N'E9', 3, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'105', N'E9', 3, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'100', N'A1', 1, 1, GETDATE(), GETDATE(), 0);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'101', N'A1', 1, 1, GETDATE(), GETDATE(), 0);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'104', N'A1', 1, 1, GETDATE(), GETDATE(), 0);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'105', N'A1', 1, 1, GETDATE(), GETDATE(), 0);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'102', N'E1', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'103', N'E2', 1, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'102', N'E1', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'103', N'E2', 2, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'102', N'E1', 3, 1, GETDATE(), GETDATE(), 0.09);
INSERT INTO [dbo].[dbi_atlas_mapping_invoice] ( [invoice_mdm_code], [tax_code], [contract_category_type], [status], [created_at], [updated_at], [tax_rate]) VALUES ( N'103', N'E2', 3, 1, GETDATE(), GETDATE(), 0.09);
