-- 初始化数据
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'TRSPTRUS', N'内贸卡车', NULL, N'卖方车板交货', N'transportPrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'TRSPMARS', N'内贸船运', NULL, N'码头卖方船板自提', N'transportPrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'DEMURRAS', N'滞期船运', NULL, N'ALL', N'delayPrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'TRSPRAIS', N'内贸铁运', NULL, N'火车站火车板自提', N'transportPrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'PORTLIFS', N'目的港起吊费', NULL, N'ALL', N'liftingPrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
INSERT INTO [dbo].[dbi_atlas_mapping_second_cost] ([code], [name], [delivery_type], [delivery_type_name], [price_detail_field], [sync_action], [bu_code], [remark], [status], [created_at], [updated_at]) VALUES (N'PORTHETS', N'目的港高温费', NULL, N'ALL', N'temperaturePrice', N'ALL', N'ALL', N'', N'1', GETDATE(), GETDATE());
