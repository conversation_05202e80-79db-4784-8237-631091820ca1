--数字合同模板优化
UPDATE dbh_template
SET category1 = ',10,',
    category2 = ( CASE category_id WHEN 11 THEN ',11,' WHEN 12 THEN ',12,' ELSE '0' END ),
    category3 = ( CASE category_id WHEN 11 THEN ',23,' WHEN 12 THEN ',24,' ELSE '0' END ),
    category1_name = '油脂油料',
    category2_name = ( CASE category_id WHEN 11 THEN '豆粕' WHEN 12 THEN '豆油' ELSE '0' END ),
    category3_name = ( CASE category_id WHEN 11 THEN '豆粕' WHEN 12 THEN '豆油' ELSE '0' END ),
    bu_code = CONCAT(',',bu_code,','),
    company_code = CONCAT(',',company_code);

--数字合同条款优化
UPDATE dbh_template_item SET customer_code = 'ALL,' WHERE customer_code = '';
UPDATE dbh_template_item SET customer_code = CONCAT(',',customer_code);

UPDATE dbh_template_item SET category_id = '11,12,' WHERE category_id = '11,10,12,' OR category_id = '10,11,12,';
UPDATE dbh_template_item SET category_id = '11,' WHERE category_id = '10,11,' OR category_id = '11,10,';
UPDATE dbh_template_item
SET category1 = ',10,',
    category2 = CONCAT(',',category_id),
    category3 = (CASE category_id WHEN '11,' THEN ',23,' WHEN '12,' THEN ',24,' WHEN '11,12,' THEN ',23,24,' ELSE '0' END ),
    category1_name = '油脂油料',
    category2_name = (CASE category_id WHEN '11,' THEN '豆粕' WHEN '12,' THEN ',豆油,' WHEN '11,12,' THEN '豆粕,豆油' ELSE '0' END ),
    category3_name = ( CASE category_id WHEN '11,' THEN '豆粕' WHEN '12,' THEN ',豆油,' WHEN '11,12,' THEN '豆粕,豆油' ELSE '0' END ),
    bu_code = CONCAT(',',bu_code),
    sales_type = CONCAT(',',sales_type),
    protocol_type = CONCAT(',',protocol_type),
    contract_action_type = CONCAT(',',contract_action_type),
    company_code = CONCAT(',',company_code);


--数字合同-模板出具标识优化
UPDATE dbh_template_check
SET category1 = 10,
    category2 = category_id,
    category3 = ( CASE category_id WHEN 11 THEN 23 WHEN 12 THEN 24 ELSE 0 END );