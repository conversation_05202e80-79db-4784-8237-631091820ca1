--申请单
update dbf_price_apply set future_code = 'M' where category2 = 11;
update dbf_price_apply set future_code = 'Y' where category2 = 12;

update dbf_price_apply set tranfer_future_code = 'M' where category2 = 11 and type in(2,3);
update dbf_price_apply set tranfer_future_code = 'Y' where category2 = 12 and type in(2,3);

--成交单
update dbf_price_deal_detail set future_code = 'M' where category2 = 11;
update dbf_price_deal_detail set future_code = 'Y' where category2 = 12;

update dbf_price_deal_detail set tranfer_future_code = 'M' where category2 = 11 and type in(2,3);
update dbf_price_deal_detail set tranfer_future_code = 'Y' where category2 = 12 and type in(2,3);


--分配单
update dbf_price_allocate set future_code = 'M' where category2 = 11;
update dbf_price_allocate set future_code = 'Y' where category2 = 12;

update dbf_price_allocate set raw_future_code = 'M' where category2 = 11 and price_apply_type in(2,3);
update dbf_price_allocate set raw_future_code = 'Y' where category2 = 12 and price_apply_type in(2,3);

--刷分配单数据
update dbf_price_allocate set site_code = c.site_code,site_name = c.site_name from dbf_price_allocate f JOIN dbt_contract c ON f.contract_id = c.id;