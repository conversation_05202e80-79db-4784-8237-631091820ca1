--
CREATE TABLE [dbz_announcement] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [system_id] int  NULL,
    [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [content] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [status] int  NULL,
    [publish_time] datetime  NULL,
    [expiration_date] datetime  NULL,
    [is_deleted] tinyint DEFAULT ((0)) NULL,
    [created_by] int  NULL,
    [updated_by] int  NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dbz_anno__3213E83F136666FD] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbz_announcement] SET (LOCK_ESCALATION = TABLE)









--
CREATE TABLE [dbz_file_record] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [system_id] int  NULL,
    [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [url] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [status] int  NULL,
    [is_deleted] tinyint DEFAULT ((0)) NULL,
    [created_by] int  NULL,
    [updated_by] int  NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dbz_file_r__3213E83F136666FD] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbz_file_record] SET (LOCK_ESCALATION = TABLE)