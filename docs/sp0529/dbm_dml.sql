INSERT INTO [dbo].[dbm_template]( [third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES (N'', N'成交待分配通知', N'inmail', N'【挂单成交可分配】${priceType!}，${dominantCode!}，${applyNum!}，${applyPrice!}', N'${customerName!}于${LDCName!}，${categoryName!}，${dominantCode!}，${priceType!}，${applyNum!}，${transactionPrice!}已成交，${priceCode!}成交分配请点击分配合同。
', N'normal', NULL, '2023-05-29 01:23:06.893', NULL, '2023-06-14 11:10:47.057', N'5', N'COLUMBUS_DEAL_ALLOCATE', NULL, N'admin5', 0);
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES (N'COLUMBUS_DEAL_ALLOCATE', N'', N'', N'inmail', N'NOTICE', (select top(1) id from dbm_template ORDER BY id desc), N'system', N'', N'customer_role', N'', '2023-05-29 01:42:40.440', '2023-05-29 01:42:40.440', NULL, NULL, 1, 2, 1, 0, NULL, NULL, 1, '单条实时发送');



INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ( N'', N'电子文档签章通知', N'inmail', N'电子文档签章通知', N'您有一份电子文档待签署，合同编号：${contractCode!}，TT编号${TTCode!}，请及时签章回传
', N'normal', NULL, '2023-05-29 01:27:34.923', NULL, '2023-05-29 01:27:34.923', NULL, N'COLUMBUS_SIGN_INMAIL', NULL, NULL, 0);
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES ( N'COLUMBUS_SIGN_INMAIL', N'', N'', N'inmail', N'NOTICE', (select top(1) id from dbm_template ORDER BY id desc), N'system', N'', N'customer_role', N'', '2023-05-29 01:42:40.463', '2023-05-29 01:42:40.463', NULL, NULL, 1, 2, 1, 0, NULL, NULL, 1, '单条实时发送');

INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES (N'', N'合同正本通知', N'inmail', N'合同正本通知', N'合同编号：${contractCode!}，${TTCode!}，我司已于${mailGoTime!}	寄出，邮寄单号：${ldcDeliverySn!}。请贵司于收到后5个工作日内盖章回寄。
', N'normal', NULL, '2023-05-29 01:30:30.510', NULL, '2023-06-14 11:24:03.697', N'5', N'COLUMBUS_ORIGINAL_PAPER', NULL, N'admin5', 1);
INSERT INTO [dbo].[dbm_business_template]( [business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES (N'COLUMBUS_ORIGINAL_PAPER', N'', N'', N'inmail', N'NOTICE', (select top(1) id from dbm_template ORDER BY id desc), N'system', N'', N'customer_role', N'', '2023-05-29 01:42:40.490', '2023-06-14 11:18:44.290', NULL, N'5', 1, 2, 1, 0, NULL, N'admin5', 1, '发送频率：每天17:30:00发送一次（不区分工作日）
发送内容：前一日17:30:00-当日17:29:59需要录入正本信息的合同');


INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES (N'', N'未成交单据通知', N'inmail', N'挂单未成交已撤销', N'${customerName!}于${LDCName!}，${dominantCode!}，${priceType!}，${applyNum!}吨，${applyPrice!}元/吨，${priceCode!}，挂单未成。
', N'normal', NULL, '2023-06-26 11:15:23.997', NULL, '2023-06-28 17:33:19.440', N'1', N'COLUMBUS_NOT_DEAL', NULL, N'万家霖', 1);
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES ( N'COLUMBUS_NOT_DEAL', N'', N'', N'inmail', N'NOTICE', (select top(1) id from dbm_template ORDER BY id desc), N'system', N'', N'customer_role', N'', '2023-05-29 01:42:40.513', '2023-06-14 11:18:32.193', NULL, N'5', 1, 2, 1, 0, NULL, N'admin5', 1, '交易日
8:30:00合并发送：
【前一个交易日17:00:00-8:29:59】
9:00:00合并发送：
【8:30:00-8:59:59】
15:30:00合并发送：
【9:00:00-15:29:59】
17:00:00合并发送：
【15:30:00-16:59:59】');





INSERT INTO [dbo].[dba_c_role_def]( [code], [name], [description], [parent_id], [level], [sort], [is_base_category], [is_sales_type], [related_category_id], [related_sales_type], [type], [status], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (NULL, N'正本', NULL, NULL, 2, NULL, 1, 1, N'[11,12]', N'[1,2]', 1, 1, N'1', N'1', '2023-05-30 12:03:59.520', '2023-05-30 12:03:59.520', 0);
INSERT INTO [dbo].[dba_c_role_def]([code], [name], [description], [parent_id], [level], [sort], [is_base_category], [is_sales_type], [related_category_id], [related_sales_type], [type], [status], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (NULL, N'IT', NULL, NULL, 2, NULL, 0, 0, N'[]', N'[]', 0, 1, N'100172', N'100172', '2023-06-15 11:23:53.157', '2023-06-15 11:23:53.157', 0);
INSERT INTO [dbo].[dba_c_role_def]([code], [name], [description], [parent_id], [level], [sort], [is_base_category], [is_sales_type], [related_category_id], [related_sales_type], [type], [status], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (NULL, N'业务管理员', NULL, NULL, 2, NULL, 0, 0, N'[]', N'[]', 0, 1, N'100172', N'100172', '2023-06-15 11:24:49.807', '2023-06-15 11:24:49.807', 0);

INSERT INTO [dbo].[dba_c_role]([name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'正本', NULL, 0, 2, NULL, NULL, NULL, 210, NULL, NULL, 11, 1, 1, 1, '2023-05-30 12:03:59.557', '2023-05-30 12:03:59.557', 0);
INSERT INTO [dbo].[dba_c_role]([name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'正本', NULL, 0, 2, NULL, NULL, NULL, 210, NULL, NULL, 11, 2, 1, 1, '2023-05-30 12:03:59.573', '2023-05-30 12:03:59.573', 0);
INSERT INTO [dbo].[dba_c_role]([name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'正本', NULL, 0, 2, NULL, NULL, NULL, 210, NULL, NULL, 12, 1, 1, 1, '2023-05-30 12:03:59.577', '2023-05-30 12:03:59.577', 0);
INSERT INTO [dbo].[dba_c_role]([name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'正本', NULL, 0, 2, NULL, NULL, NULL, 210, NULL, NULL, 12, 2, 1, 1, '2023-05-30 12:03:59.580', '2023-05-30 12:03:59.580', 0);
INSERT INTO [dbo].[dba_c_role]( [name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'IT', NULL, NULL, 2, NULL, NULL, NULL, 211, NULL, NULL, 0, 0, 100172, 100172, '2023-06-15 11:23:53.683', '2023-06-15 11:23:53.683', 0);
INSERT INTO [dbo].[dba_c_role]([name], [description], [parent_id], [level], [sort], [admin_id], [code], [role_def_id], [role_def_code], [real_name], [category_id], [sales_type], [created_by], [updated_by], [created_at], [updated_at], [is_deleted]) VALUES (N'业务管理员', NULL, NULL, 2, NULL, NULL, NULL, 212, NULL, NULL, 0, 0, 100172, 100172, '2023-06-15 11:24:49.827', '2023-06-15 11:24:49.827', 0);

