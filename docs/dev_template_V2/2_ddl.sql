update [dbo].[dbh_template_group] set main_version_status = 1,is_deleted = 0;
update [dbo].[dbh_template_group] set is_fixed = 0;
update [dbo].[dbh_template_group] set is_fixed = 1 where contract_action_type = -1;

update dbh_template_item set main_version_status = 1,is_deleted = 0;
update dbh_template_item set is_fixed = 0;
update dbh_template_item set is_fixed = 1 where template_group_code in
(select code from dbh_template_group where  is_fixed = 1);

update dbz_dict_item set status = 1 where is_deleted = 0;
update dbz_dict_item set status = 0 where is_deleted = 1;
update dbh_template set is_deleted = 0,company_code = 'FL,TJIB,',main_version_status = 1,main_version = '2024000',duplicate_times = 1;
-- 生产环境为禁用状态的模板，数据清空
delete from dbh_template where status = 0;
-- UAT用-质量指标同步，客户数据联表更新
-- update dbh_quality_rule
-- set dbh_quality_rule.refer_id = c.id
--     from dbh_quality_rule
-- join dba_customer c on dbh_quality_rule.refer_code = c.linkage_customer_code
-- where dbh_quality_rule.quality_rule_code in ('enterprise','customer');
-- UAT用-客户主数据模板特殊集团客户清空
-- SELECT id,name,template_vip_code from dba_customer where template_vip_code not in (
--     select item_code from dbz_dict_item where dict_code = 'enterpriseCode'
-- )
-- 条款绑定，条款组未绑定的数据清除
update  dbh_template_item_relation set is_deleted = 1
where id in (
    select ir.id from dbh_template_item_relation ir
                          join dbh_template h on ir.template_code = h.code and status = 1
                          join dbh_template_item i on ir.template_item_code = i.code
                          join dbh_template_group g on i.template_group_code = g.code
                          LEFT JOIN dbh_template_group_relation gr on h.code = gr.template_code and gr.template_group_code = g.code and gr.is_deleted = 0
    where ir.is_deleted = 0 and gr.id is null and h.contract_action_type != '102');

-- 模板关联的条款组未绑定条款,清空条款组。不包含牧原的模板
-- select * from dbh_template_group_relation
-- where is_deleted = 0
-- and template_code not like '%HNMY%'
-- and id not in
-- (select gr.id from dbh_template_group_relation gr
-- join dbh_template_group g on gr.template_group_code = g.code
-- join dbh_template_item ti on g.real_group_code = ti.template_group_code
-- join dbh_template_item_relation ir on gr.template_code = ir.template_code and ti.code = ir.template_item_code
-- where gr.is_deleted = 0) ;
update dbh_template_group_relation set is_deleted = 1
where is_deleted = 0
  and template_code not like '%HNMY%'
  and id not in
      (select gr.id from dbh_template_group_relation gr
                             join dbh_template_group g on gr.template_group_code = g.code
                             join dbh_template_item ti on g.real_group_code = ti.template_group_code
                             join dbh_template_item_relation ir on gr.template_code = ir.template_code and ti.code = ir.template_item_code
       where gr.is_deleted = 0) ;
-- 牧原的条款加载条件丢失问题
-- select * from dbh_template_rule where refer_code in ('Clause_0277','Clause_0280','Clause_0281','Clause_0282','Clause_0283','Clause_0284','Clause_0288','Clause_0290','Clause_0291','Clause_0292','Clause_0294','Clause_0295','Clause_0296','Clause_0300','Clause_0304','Clause_0305','Clause_0306','Clause_0307','Clause_0308','Clause_0309','Clause_0310','Clause_0311','Clause_0312','Clause_0313','Clause_0314','Clause_0315','Clause_0316','Clause_0317','Clause_0318','Clause_0319','Clause_0320','Clause_0321','Clause_0324','Clause_0325','Clause_0326','Clause_0327','Clause_0328','Clause_0329','Clause_0331','Clause_0333','Clause_0341');
update dbh_template_rule set is_deleted = 0 where  refer_code in ('Clause_0277','Clause_0280','Clause_0281','Clause_0282','Clause_0283','Clause_0284','Clause_0288','Clause_0290','Clause_0291','Clause_0292','Clause_0294','Clause_0295','Clause_0296','Clause_0300','Clause_0304','Clause_0305','Clause_0306','Clause_0307','Clause_0308','Clause_0309','Clause_0310','Clause_0311','Clause_0312','Clause_0313','Clause_0314','Clause_0315','Clause_0316','Clause_0317','Clause_0318','Clause_0319','Clause_0320','Clause_0321','Clause_0324','Clause_0325','Clause_0326','Clause_0327','Clause_0328','Clause_0329','Clause_0331','Clause_0333','Clause_0341');
-- 邮件通知：协议出具内容修改通知
INSERT INTO [dbo].[dbm_template] ([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ( N'', N'协议出具内容修改通知', N'email', N'协议出具内容修改通知', N'<p>合同编号：${contractCode!}，</p>
<p>TT编号：${ttCode!}，</p>
<p>协议类型：${tradeType!}，</p>
<p>模版编号：${templateCode!}，</p>
<p>模版名称：${templateName!}，</p>
<p>协议出具修改了条款组${modifyGroupCodes!}，增加了${addGroupNum!}个自定义条款，删除了条款组${deleteGroupCodes!}</p>', N'normal', NULL, GETDATE(), NULL,  GETDATE(), N'1', N'MAGELLAN_HUSKY_TEMPLATE_CHANGE', NULL, N'admin1', 0);
INSERT INTO [dbo].[dbm_business_template] ( [business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency])
SELECT N'MAGELLAN_HUSKY_TEMPLATE_CHANGE', N'', N'', N'email', N'NOTICE', id, N'system', N'135;136;140;8430;', N'role', N'', GETDATE(), GETDATE(), NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, '单条实时发送'
FROM dbm_template WHERE template_code = 'MAGELLAN_HUSKY_TEMPLATE_CHANGE';
-- 品类
UPDATE dbh_variable set is_enum = 0,has_dict = 1 where code = 'categoryId';
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('categoryId', '业务品类', '豆粕', '11', 11, '豆粕', 1, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('categoryId', '业务品类', '豆油', '12', 12, '豆油', 2, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
-- 初始化操作类型
update dbh_variable set is_enum = 0,has_dict = 1 where code = 'contractActionType';

INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '无', '-1', -1, '无', 1, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '新增合同', '101', 101, '新增合同', 2, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '修改合同', '102', 102, '修改合同', 3, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '拆分合同', '104', 104, '拆分合同', 5, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同点价', '111', 111, '合同点价', 7, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同转月（部分）', '121', 121, '合同转月（部分）', 9, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同转月（全部）', '122', 122, '合同转月（全部）', 10, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同反点价（部分）', '131', 131, '合同反点价（部分）', 11, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同反点价（全部）', '132', 132, '合同反点价（全部）', 12, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '新增结构化定价合同', '141', 141, '新增结构化定价合同', 13, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同回购', '151', 151, '合同回购', 14, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '解约定赔', '161', 161, '解约定赔', 15, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
INSERT INTO [dbo].[dbz_dict_item] ( [dict_code], [dict_name], [item_name], [item_code], [item_value], [item_description], [item_sort], [memo], [is_deleted], [created_at], [updated_at], [status], [created_by], [updated_by]) VALUES ('contractActionType', '操作类型', '合同关闭', '191', 191, '合同关闭', 15, NULL, 0, GETDATE(), GETDATE(), 0, NULL, NULL);
-- 菜单、权限
INSERT INTO [dbo].[dba_menu] ( [icon], [code], [name], [level], [parent_id], [url], [sort], [status], [created_at], [updated_at], [is_deleted], [system], [category_id], [parent_code]) VALUES ( NULL, N'N089', N'历史记录', 1, 88, N'/contract/versionLog', 1, 1, GETDATE(),GETDATE(), 0, 1, 0, N'N027');


INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (538, N'TY_094', N'TY_094', N'条款-导出文件', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (539, N'TY_095', N'TY_095', N'条款-导出脚本', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (540, N'TY_096', N'TY_096', N'条款-导入脚本', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (541, N'TY_097', N'TY_097', N'编辑固定条款', NULL, 508, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (542, N'TY_098', N'TY_098', N'条款组-导出文件', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (543, N'TY_099', N'TY_099', N'条款组-导出脚本', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (544, N'TY_100', N'TY_100', N'条款组-导入脚本', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (545, N'TY_101', N'TY_101', N'编辑固定条款组', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());


INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (546, N'TY_102', N'TY_102', N'复制模板', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (547, N'TY_103', N'TY_103', N'模板导出文件', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (548, N'TY_104', N'TY_104', N'模板导出脚本', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (549, N'TY_105', N'TY_105', N'模板导入脚本', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (550, N'TY_106', N'TY_106', N'模板保存正式版本', NULL, 510, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (551, N'SBM_S_103', N'SBM_S_103', N'质量指标-导出', NULL, 511, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (552, N'SBO_S_103', N'SBO_S_103', N'质量指标-导出', NULL, 513, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (553, N'TY_107', N'TY_107', N'特殊模板客户-新增', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (554, N'TY_108', N'TY_108', N'特殊模板客户-编辑', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (555, N'TY_109', N'TY_109', N'出具模板标识-新增', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (556, N'TY_110', N'TY_110', N'出具模板标识-编辑', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (557, N'TY_111', N'TY_111', N'出具模板标识-导出', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());
INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (558, N'TY_112', N'TY_112', N'出具模板标识-启用/禁用', NULL, 64, 2, 1, 0, GETDATE(), GETDATE());

INSERT INTO [dbo].[dba_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [system], [is_deleted], [created_at], [updated_at]) VALUES (559, N'TY_113', N'TY_113', N'条款组-启用/禁用', NULL, 509, 2, 1, 0, GETDATE(), GETDATE());


