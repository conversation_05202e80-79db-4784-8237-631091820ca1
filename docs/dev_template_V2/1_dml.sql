

ALTER TABLE [dbo].[dbz_dict_item] ADD [status] int DEFAULT ((1)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态(0禁用 1启用)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'status'
    GO
ALTER TABLE [dbo].[dbz_dict_item] ADD [created_by] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'created_by'
    GO
ALTER TABLE [dbo].[dbz_dict_item] ADD [updated_by] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dbz_dict_item',
    'COLUMN', N'updated_by'
    GO

ALTER TABLE [dbo].[dbh_template] ADD [main_version] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本号（例：2023001,按年份递增,统计一年正式改了几次）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'main_version'
    GO
ALTER TABLE [dbo].[dbh_template] ADD [main_version_status] int DEFAULT ((1)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本状态（0 非正式版本 1 正式版本）保存、更新、绑定关系变更，则更新为非正式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'main_version_status'
    GO
ALTER TABLE [dbo].[dbh_template] ADD [main_version_desc] text DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'main_version_desc'
    GO
ALTER TABLE [dbo].[dbh_template] ADD [duplicate_times] int DEFAULT ((1)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'编号重复版本次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'duplicate_times'
    GO
ALTER TABLE [dbo].[dbh_template] ADD [is_deleted] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0 未删除 1删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template',
    'COLUMN', N'is_deleted'

ALTER TABLE [dbo].[dbh_template_group] ADD [main_version_status] int DEFAULT ((1)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本状态（0 非正式版本 1 正式版本）保存、更新、绑定关系变更，则更新为非正式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_group',
    'COLUMN', N'main_version_status'
    GO
ALTER TABLE [dbo].[dbh_template_group] ADD [is_fixed] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为固定条款组（0 非固定 1 固定）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_group',
    'COLUMN', N'is_fixed'
    GO
ALTER TABLE [dbo].[dbh_template_group] ADD [is_deleted] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0 未删除 1删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_group',
    'COLUMN', N'is_deleted'

ALTER TABLE [dbo].[dbh_template_item] ADD [main_version_status] int DEFAULT ((1)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本状态（0 非正式版本 1 正式版本）保存、更新、绑定关系变更，则更新为非正式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'main_version_status'
    GO
ALTER TABLE [dbo].[dbh_template_item] ADD [is_fixed] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为固定条款（0 非固定 1 固定）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'is_fixed'
    GO
ALTER TABLE [dbo].[dbh_template_item] ADD [is_deleted] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0 未删除 1删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_item',
    'COLUMN', N'is_deleted'


CREATE TABLE [dbo].[dbh_template_check] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [category_id] int  NULL,
    [sales_type] int  NULL,
    [contract_action_type] int  NULL,
    [template_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [template_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_info] text COLLATE Chinese_PRC_CI_AS  NULL,
    [rule_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [condition_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [status] int DEFAULT 1 NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT 0 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    CONSTRAINT [PK__dbh_temp__3213E83F49E81D9D] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    TEXTIMAGE_ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbh_template_check] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'配置自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务品类(11 豆粕M;12 豆油Y)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'category_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'采销类型(1 采购P；2 销售S)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'sales_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作类型(无-1;新增合同101;修改合同102;拆分合同104;合同点价111;合同转月（部分）121;
    合同转月（全部）122;合同反点价（部分）131;合同反点价（全部）132;新增结构化定价合同141;合同回购151;解约定赔161;合同关闭191)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'contract_action_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'模板编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'template_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'模板名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'template_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则drools脚本内容(关联记录到)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'rule_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'rule_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'规则描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'condition_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态（0禁用 1启用）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_check',
    'COLUMN', N'memo'


CREATE TABLE [dbo].[dbh_template_history] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [refer_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [refer_type] int  NULL,
    [refer_id] int  NULL,
    [operation_type] int  NULL,
    [operation_type_info] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [request_info] text COLLATE Chinese_PRC_CI_AS  NULL,
    [category_id] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [sales_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [contract_action_type] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [title] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [content] text COLLATE Chinese_PRC_CI_AS  NULL,
    [version_type] int  NULL,
    [version] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [main_version] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [main_version_status] int  NULL,
    [main_version_desc] text COLLATE Chinese_PRC_CI_AS  NULL,
    [memo] text COLLATE Chinese_PRC_CI_AS  NULL,
    [status] int DEFAULT 1 NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    CONSTRAINT [PK__dbh_temp__3213E83F878DA8AA] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    TEXTIMAGE_ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbh_template_history] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自增ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'模板/条款组/条款编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'refer_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'模板/条款组/条款名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'refer_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'类型（1 条款 2 条款组 3模板）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'refer_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'模板/条款组/条款ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'refer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作类型数值（1新增 2修改 3启用/禁用 5复制模板 6模板-条款绑定关系-绑定 7模板-条款绑定关系-解除 8标记为正式版本 9脚本导入 10脚本导出）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'operation_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作类型描述（1新增 2修改 3启用/禁用 5复制模板 6模板-条款绑定关系-绑定 7模板-条款绑定关系-解除 8标记为正式版本 9脚本导入 10脚本导出）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'operation_type_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接口请求内容',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'request_info'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务品类(11 豆粕M;12 豆油Y)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'category_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'采销类型(1 采购P；2 销售S)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'sales_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'标题-条款组',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'title'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'具体内容信息（Json格式）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'content'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'版本类型(1 临时版本 2 正式版本)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'version_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'版本号（时间戳格式：yyyyMMddHHmmss）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'version'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本号（例：2023001,按年份递增,统计一年正式改了几次）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'main_version'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'正式版本状态（0 非正式版本 1 正式版本）保存、更新、绑定关系变更，则更新为非正式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'main_version_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'标记为主版本备注信息',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'main_version_desc'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态（0禁用 1启用）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbh_template_history',
    'COLUMN', N'status'