create view  v_husky_template AS
SELECT tp.id AS template_id,tp.code AS template_code,tp.name AS template_name,tp.bu_code AS template_bu_code,tp.company_code AS template_company_code,tp.category_id AS template_category_id,tp.sales_type AS template_sales_type,tp.protocol_type AS template_protocol_type,tp.contract_action_type AS template_contract_action_type,tp.enterprise_code AS template_enterprise_code,tp.enterprise_name AS template_enterprise_name,tp.layout AS template_layout,tp.status AS template_status,tp.memo AS template_memo,tp.main_version_status AS template_main_version_status,tp.main_version_desc AS template_main_version_desc,tp.bind_template AS bind_template,tp.updated_at AS template_updated_at,tp.updated_by AS template_updated_by,
       tbd.bind_template_code,tbd.bind_template_name,tbd.sort AS bind_sort,

       tgr.sort AS group_sort,tg.id AS group_id,tg.code AS group_code,tg.name AS group_name,tg.title AS group_title,tg.real_group_code AS real_group_code,tg.contract_action_type AS group_contract_action_type,tg.status AS group_status,tg.memo AS group_memo,tg.rule_info AS group_rule_info,gr.condition_info AS group_condition_info,tg.is_fixed AS group_is_fixed,tg.main_version_status AS group_main_version_status,tg.updated_at AS group_updated_at,tg.updated_by AS group_updated_by,

       teg.eId AS item_id,teg.ecode AS item_code,teg.ename AS item_name,teg.template_group_code AS item_group_code,teg.eContent AS item_content,teg.eSalesType AS item_sales_type,teg.eCategoryId AS item_category_id,teg.eEnterpriseCode AS item_enterprise_code,teg.eEnterpriseName AS item_enterprise_name,teg.eMemo AS item_memo,teg.eStatus AS item_status,teg.erule AS item_condition_info,teg.eRuleInfo AS item_rule_info,teg.eMainVersionStatus AS item_main_version_status,teg.eUpdatedAt AS item_updated_at,teg.eUpdatedBy AS item_updated_by
FROM dbh_template tp
         JOIN(
    SELECT t.id,t.code AS template_code,t.bind_template,
           CASE WHEN tb.bind_template_code IS NOT NULL THEN tb.bind_template_code ELSE t.code END AS bind_template_code,
           b.name as bind_template_name,tb.sort
    FROM  dbh_template t
              LEFT JOIN dbh_template_bind tb ON t.code = tb.template_code AND tb.is_deleted = 0
              LEFT JOIN dbh_template b ON tb.bind_template_code = b.code
) tbd ON tp.code = tbd.template_code
         LEFT JOIN dbh_template_group_relation tgr ON tbd.bind_template_code = tgr.template_code AND tgr.is_deleted = 0
         JOIN dbh_template_group tg ON tgr.template_group_code = tg.code
         LEFT JOIN dbh_template_rule gr ON tg.code = gr.refer_code AND gr.is_deleted = 0 AND gr.refer_type = 2
         LEFT JOIN (
    SELECT te.template_code,te.template_item_code ecode,e.template_group_code,e.id eId,e.name ename,e.content eContent,e.title etitle,e.rule_info eRuleInfo,r.condition_info erule,e.sales_type eSalesType,e.category_id eCategoryId,e.enterprise_code eEnterpriseCode,e.enterprise_name eEnterpriseName,e.memo eMemo,e.status eStatus,e.is_fixed eisfixed,e.main_version_status eMainVersionStatus,e.updated_at eUpdatedAt,e.updated_by eUpdatedBy
    FROM dbh_template_item_relation te
             JOIN dbh_template_item e ON e.code = te.template_item_code AND te.is_deleted = 0
             LEFT JOIN dbh_template_rule r ON e.code=r.refer_code AND r.is_deleted=0
) teg ON teg.template_group_code =tg.real_group_code AND teg.template_code = tbd.bind_template_code