/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 02/09/2024 14:50:31
*/


-- ----------------------------
-- Table structure for dbi_atlas_mapping_department
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_mapping_department]') AND type IN ('U'))
	DROP TABLE [dbo].[dbi_atlas_mapping_department]
GO

CREATE TABLE [dbo].[dbi_atlas_mapping_department] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [mdm_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [business_entity] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [contract_category_type] int  NULL,
  [commodity_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [market_zone] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [status] tinyint DEFAULT ((1)) NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL
)
GO

ALTER TABLE [dbo].[dbi_atlas_mapping_department] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键ID',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'部门编码',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'mdm_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'ATLAS账套',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'business_entity'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同类型：1.现货 2.仓单 3.豆二',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'contract_category_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'商品编码',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'commodity_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'区域编码（如果为空则适配全部）',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'market_zone'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态（0.禁用 1.启用）',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_mapping_department',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbi_atlas_mapping_department
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbi_atlas_mapping_department]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_mapping_department
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_mapping_department] ADD CONSTRAINT [PK__dbi_atla__3213E83FF43CA2B4] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

