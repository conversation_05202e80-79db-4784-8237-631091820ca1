-- dbd_delivery_apply
ALTER TABLE [dbo].[dbd_delivery_apply] ADD [atlas_apply_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [atlas_sub_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply] ADD [approval_comments] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'Atlas接口返回的申请状态 Rejected Blocked Awaiting confirmation Confirmed',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'atlas_apply_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'Atlas接口返回的sub_status',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'atlas_sub_status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS接口返回的审批备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply',
    'COLUMN', N'approval_comments'
    GO

-- dbd_delivery_apply_contract
ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [atlas_update_result] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [atlas_update_times] int NULL
    GO

ALTER TABLE [dbo].[dbd_delivery_apply_contract] ADD [split_no] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'根据ATLAS的数据反馈更新结果的记录1.true 0.false',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'atlas_update_result'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'接收ATLAS反馈信息更新表的次数',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'atlas_update_times'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS拆分的子合同编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbd_delivery_apply_contract',
    'COLUMN', N'split_no'
    GO

-- dbi_atlas_mapping_contract
ALTER TABLE [dbo].[dbi_atlas_mapping_contract] ADD [executed_num] decimal(15,6) NULL
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_contract] ADD [allocated_num] decimal(15,6) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已执行数量（ATLAS返回子合同）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'executed_num'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已分配数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'allocated_num'
