-- ----------------------------
-- Table structure for dbi_atlas_payment_term_changes
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_payment_term_changes]') AND type IN ('U'))
	DROP TABLE [dbo].[dbi_atlas_payment_term_changes]
GO

CREATE TABLE [dbo].[dbi_atlas_payment_term_changes] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [trade_type] int  NULL,
  [credit_days] int  NULL,
  [pay_condition_id] int  NULL,
  [old_payment_term_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [new_payment_term_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT getdate() NULL,
  [updated_at] datetime DEFAULT getdate() NULL
)
GO

ALTER TABLE [dbo].[dbi_atlas_payment_term_changes] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键ID',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交易类型',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'trade_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'赊销天数',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'credit_days'
GO

EXEC sp_addextendedproperty
'MS_Description', N'付款条件代码ID',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'pay_condition_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更前的 payment term code',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'old_payment_term_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更后的 payment term code',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'new_payment_term_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_payment_term_changes',
'COLUMN', N'updated_at'
GO


-- ----------------------------
-- Auto increment value for dbi_atlas_payment_term_changes
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbi_atlas_payment_term_changes]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_payment_term_changes
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_payment_term_changes] ADD CONSTRAINT [PK__dbi_atla__3213E83F6BF769A1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

