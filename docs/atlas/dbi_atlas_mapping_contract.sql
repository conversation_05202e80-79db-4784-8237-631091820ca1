CREATE TABLE [dbo].[dbi_atlas_mapping_contract] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [uuid] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [company_business_entity] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [atlas_business_entity] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [contract_business_entity] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [sales_type] int DEFAULT ((0)) NULL,
    [nav_contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [atlas_contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [split_flag] int DEFAULT ((0)) NULL,
    [contract_num] decimal(15,6) DEFAULT ((0)) NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [call_back_time] datetime  NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime  NULL,
    CONSTRAINT [PK__dbi_atla__3213E83FF39D624C] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY],
    CONSTRAINT [uk_uuid] UNIQUE NONCLUSTERED ([uuid] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_contract] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'公司主体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'company_business_entity'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS主体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'atlas_business_entity'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同主体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'contract_business_entity'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同采销类型（1.采购 2.销售） ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'sales_type'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'航海家合同编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'nav_contract_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS合同编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'atlas_contract_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'Split from ATLAS(当子合同为在ATLAS拆分建立的) ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'split_flag'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'contract_num'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'回执接收时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'call_back_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_contract',
    'COLUMN', N'updated_at'