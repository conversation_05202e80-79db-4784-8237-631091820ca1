CREATE TABLE [dbo].[dbi_atlas_mapping_config] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [parent_id] int DEFAULT ((0)) NULL,
    [key_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [key_value] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [refer_id] int DEFAULT ((0)) NULL,
    [refer_table_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [refer_table_field] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [sort] int DEFAULT ((0)) NULL,
    [status] tinyint DEFAULT ((0)) NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dbi_atla__3213E83FF6E276C5] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_config] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'	自增长主键',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'父id(暂不做层级)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'parent_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'键名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'key_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'键值',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'key_value'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'与key关联id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'refer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'与key关联表名',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'refer_table_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'与key关联字段',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'refer_table_field'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'updated_at'