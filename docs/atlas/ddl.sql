-- 包装代码mdm编码
ALTER TABLE [dbo].[dbg_attribute_value] ADD [mdm_package_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL

-- 配置的mdm编码
ALTER TABLE [dbo].[dbz_system_rule_item] ADD [mdm_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL

-- 发货库点的ATLAS编码
ALTER TABLE [dbo].[dba_factory_warehouse] ADD [atlas_warehouse_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL

-- 付款代码的mdm编码
ALTER TABLE [dbo].[dba_pay_condition] ADD [mdm_pay_condition_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL

ALTER TABLE [dbo].[dbt_contract] ADD [origin_contract_type] int  NULL
