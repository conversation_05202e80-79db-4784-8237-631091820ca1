CREATE TABLE [dbo].[dbi_atlas_mapping_market] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [future_letters] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [fno_market] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [remark] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dbi_atla__3213E83FA257DAA4] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_market] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'id主键 自增长',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约首字母',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'future_letters'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'fno_market'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'remark'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_market',
    'COLUMN', N'updated_at'