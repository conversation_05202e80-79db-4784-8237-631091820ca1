/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_0214
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 31/05/2023 10:02:32
*/


-- ----------------------------
-- Table structure for dbi_atlas_sync_request
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbi_atlas_sync_request]') AND type IN ('U'))
	DROP TABLE [dbo].[dbi_atlas_sync_request]
GO

CREATE TABLE [dbo].[dbi_atlas_sync_request] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [tt_id] int  NULL,
  [biz_id] int  NULL,
  [biz_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [object_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [operation_type] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [sales_type] int  NULL,
  [trade_type] int  NULL,
  [sync_time] datetime  NULL,
  [sync_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [request_info] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [remark] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbi_atlas_sync_request] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'TTid',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'tt_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务单据id',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'biz_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'业务单据号，本次为合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'biz_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'传输对象，区分本次接口传输的是合同、定价单',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'object_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'处理类型，区分本次接口传输',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'operation_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'请求场景',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'trade_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'同步时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'sync_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'同步状态',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'sync_status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'传输原始信息',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'request_info'
GO

EXEC sp_addextendedproperty
'MS_Description', N'备注',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口信息传输用户',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口信息最后修改用户',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口信息传输时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口信息最后修改时间',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'ATLAS请求记录表',
'SCHEMA', N'dbo',
'TABLE', N'dbi_atlas_sync_request'
GO


-- ----------------------------
-- Auto increment value for dbi_atlas_sync_request
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbi_atlas_sync_request]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dbi_atlas_sync_request
-- ----------------------------
ALTER TABLE [dbo].[dbi_atlas_sync_request] ADD CONSTRAINT [PK__dbi_atla__3213E83FB6E55895] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

