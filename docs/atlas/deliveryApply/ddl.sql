ALTER TABLE [dbo].[dbi_atlas_mapping_config] DROP CONSTRAINT [DF__dbi_atlas__refer__1705CEC5]
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_config] ALTER COLUMN [refer_id] nvarchar(64) COLLATE Chinese_PRC_CI_AS NULL
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_config] ADD CONSTRAINT [DF__dbi_atlas__refer__1705CEC5] DEFAULT '' FOR [refer_id]
    GO

ALTER TABLE [dbo].[dbi_atlas_mapping_config] ADD [is_refered_other_tables ] tinyint NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'有没有关联其他数据表 0:没有 ; 1:有 ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_mapping_config',
    'COLUMN', N'is_refered_other_tables '

    GO


ALTER TABLE [dbo].[dbi_atlas_sync_record] ADD [status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

ALTER TABLE [dbo].[dbi_atlas_sync_record] ADD [sub_status] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货委托在ATLAS CN的初步存在状态: AWAITING CONFIRMATION ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货委托在ATLAS CN的处理状态: Insufficient cash ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbi_atlas_sync_record',
    'COLUMN', N'sub_status'