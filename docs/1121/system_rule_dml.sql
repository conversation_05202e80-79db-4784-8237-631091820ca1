--dbz_system_rule
set IDENTITY_INSERT dbz_system_rule on
INSERT INTO [dbo].[dbz_system_rule]([id], [category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (96, 11, 0, 'S0011', '合同审批阈值配置', 0, 1, '2022-11-02 09:37:24.090', '2022-11-02 09:37:24.090');
INSERT INTO [dbo].[dbz_system_rule]([id], [category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (97, 12, 0, 'S0011', '合同审批阈值配置', 0, 1, '2022-11-02 09:37:24.090', '2022-11-02 09:37:24.090');
set IDENTITY_INSERT dbz_system_rule off

--dbz_system_rule_item
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (96, '合同总金额A签阈值', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'MIN_AMOUNT', '25000000', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (96, '合同总金额C签阈值', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'MAX_AMOUNT', '50000000', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (96, '提货交期阈值(月)', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'DELIVERY_DUE_MONTH', '12', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (96, '未提数量(吨)', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'REMAIN_CONTRACT_NUMBER', '32', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (97, '合同总金额A签阈值', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'MIN_AMOUNT', '20000000', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (97, '合同总金额C签阈值', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'MAX_AMOUNT', '50000000', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (97, '提货交期阈值(月)', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'DELIVERY_DUE_MONTH', '12', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code]) VALUES (97, '未提数量(吨)', 2, 0, 0, 1, '2022-11-02 09:40:37.910', '2022-11-02 09:40:37.910', NULL, 1, 1, N'REMAIN_CONTRACT_NUMBER', '32', NULL);
