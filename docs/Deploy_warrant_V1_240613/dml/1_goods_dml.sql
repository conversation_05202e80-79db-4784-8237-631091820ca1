
-- 品类主表插入新数据
SET IDENTITY_INSERT [dbo].[dbg_category] ON
GO
INSERT INTO [dbo].[dbg_category]( [id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no]) VALUES (N'23',11, N'豆粕', N'SBM', N'1', 3, 0, N'1', 0, getdate(), getdate(), 0.55, 20, 0, 0, 1, 'Link-SBM', 'Link-豆粕', '油豆',  '110');
GO
INSERT INTO [dbo].[dbg_category]( [id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate], [deposit_rate], [added_deposit_amount], [delay_pay_fine], [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no]) VALUES (N'24',12, N'豆油', N'SBO', N'1', 3, 0, N'1', 0, getdate(), getdate(), 0.22, 30, 0, 0, 1, 'Link-SBO', 'Link-豆油', '油豆', '120');
GO
SET IDENTITY_INSERT [dbo].[dbg_category] OFF
GO

-- 品类主表serial_no为id
UPDATE dbg_category SET serial_no = id;

-- 商品品类
UPDATE dbg_goods
SET
    category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
FROM dbg_goods f
         JOIN dbg_category g2 ON f.category_id = g2.id
         JOIN dbg_category g1 ON g2.parent_id = g1.id
         JOIN dbg_category g3 ON g2.id = g3.parent_id;

-- 规格改名称
UPDATE dbg_attribute SET display_name = '豆粕包装',name = '豆粕包装' WHERE id = 1;
UPDATE dbg_attribute SET display_name = '蛋白含量',name = '蛋白含量' WHERE id = 2;
UPDATE dbg_attribute SET display_name = '豆油包装',name = '豆油包装' WHERE id = 3;
-- UPDATE dbg_attribute SET display_name = '弃用' WHERE id = 4; -- V2新增两个豆油规格再弃用

--规格值改名称
UPDATE dbg_attribute_value set name = '袋装-50KG' where id = 1 and name = '50KG';
UPDATE dbg_attribute_value set name = '袋装-70KG' where id = 2 and name = '70KG';

-- 豆粕：品种规格 蛋白含量、豆粕包装
INSERT INTO [dbo].[dbg_category_attribute] ([category1], [category2], [category3], [attribute_id], [attribute_name], [attribute_type], [MEMO], [IS_DELETE], [CREATED_BY], [CREATED_AT], [UPDATED_BY], [UPDATED_AT], [sort])
VALUES ('10', '11', '23', 1, '豆粕包装', 1, '', 0, 1, getdate(), 1, getdate(), 200);
INSERT INTO [dbo].[dbg_category_attribute] ([category1], [category2], [category3], [attribute_id], [attribute_name], [attribute_type], [MEMO], [IS_DELETE], [CREATED_BY], [CREATED_AT], [UPDATED_BY], [UPDATED_AT], [sort])
VALUES ('10', '11', '23', 2, '蛋白含量', 2, '', 0, 1, getdate(), 1, getdate(), 1);

-- 豆油：品种规格 豆油包装
INSERT INTO [dbo].[dbg_category_attribute] ([category1], [category2], [category3], [attribute_id], [attribute_name], [attribute_type], [MEMO], [IS_DELETE], [CREATED_BY], [CREATED_AT], [UPDATED_BY], [UPDATED_AT], [sort])
VALUES ('10', '12', '24', 4, '豆油包装', 1, '', 0, 1, getdate(), 1, getdate(), 200);
