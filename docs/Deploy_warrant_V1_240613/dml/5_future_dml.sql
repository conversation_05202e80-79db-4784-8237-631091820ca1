--修改头寸申请单参数
UPDATE dbf_price_apply
SET
    category1 = g1.serial_no,
    category2 = g2.serial_no
    FROM dbf_price_apply f
JOIN dbg_category g2 ON f.category_id = g2.id
    JOIN dbg_category g1 ON g2.parent_id = g1.id;

--头寸成交单
UPDATE dbf_price_deal_detail
SET
    category1 = g1.serial_no,
    category2 = g2.serial_no
    FROM dbf_price_deal_detail f
JOIN dbg_category g2 ON f.category_id = g2.id
    JOIN dbg_category g1 ON g2.parent_id = g1.id;

--头寸分配单
UPDATE dbf_price_allocate
SET
    category1 = g1.serial_no,
    category2 = g2.serial_no
    FROM dbf_price_allocate f
JOIN dbg_category g2 ON f.category_id = g2.id
    JOIN dbg_category g1 ON g2.parent_id = g1.id;

--套保品仓
UPDATE dbf_position
SET
    category1 = g1.serial_no,
    category2 = g2.serial_no
    FROM dbf_position f
JOIN dbg_category g2 ON f.category_id = g2.id
    JOIN dbg_category g1 ON g2.parent_id = g1.id;