1、字符串用nvarchar
2、协议表新增字段加注释
3、规格表：豆油规格暂不弃用，V2新增两个豆油规格再弃用
4、郝先胜：确认tradeTicket主表的三个变更量字段的使用范围，是否涉及变更场景



-----V2

1、<PERSON><PERSON>文本用text
2、协议详情表sign_detail表结构脚本
3、货品表，字段是否global_oil先去掉（脚本及代码）
4、dml: dbt_trade_ticket表生产无goods_id
   tt_add: 1,8,9,11
   tt_modify: 2,3
   CASE WHEN tt.type =2 THEN tt_modify.goods_id
      WHEN tt.type =3 THEN tt_modify.goods_id
   	 WHEN tt.type =6 THEN tt_price.goods_id
   	 WHEN tt.type =12 THEN tt_price.goods_id
   	 	 WHEN tt.type =4 THEN tt_tranfer.goods_id
   	 	 WHEN tt.type =5 THEN tt_tranfer.goods_id
   	 	 else tt_add.goods_id
5、协议枚举优化