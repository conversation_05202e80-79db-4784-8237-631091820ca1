-- 品类主表新增字段
ALTER TABLE [dbo].[dbg_category] ADD [serial_no] int NULL
GO

EXEC sp_addextendedproperty
     'MS_Description', N'主编码',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_category',
     'COLUMN', N'serial_no';

-- 商品规格表新增字段
ALTER TABLE [dbo].[dbg_attribute] ADD [display_name] nvarchar(64) NULL

-- 商品表新增字段

ALTER TABLE [dbo].[dbg_goods] ADD [spu_id] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [full_name] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [sku_no] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [key_attribute_values] text NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category1] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category2] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category3] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [is_warrant] int NULL
GO

EXEC sp_addextendedproperty
     'MS_Description', N'SPU商品ID',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'spu_id'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'系统生成的sku货品全称',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'full_name'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'品种code+多个关键+销售规格+包装值ID
下划线隔开（排序）:_11_222_12_',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'sku_no'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'多个关键规格值+销售规格信息(Json,例：
[{"attributeId":"20","attributeValueId":"20","attributeValueName":"43%"}])',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'key_attribute_values'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'一级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'二级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'三级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'是否仓单标品(0否 1是)',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_goods',
     'COLUMN', N'is_warrant'
GO

CREATE TABLE [dbo].[dbg_category_attribute]
(
    ID             INT NOT NULL IDENTITY (1,1),
    category1      VARCHAR(255),
    category2      VARCHAR(255),
    category3      VARCHAR(255),
    attribute_id   INT,
    attribute_name VARCHAR(255),
    attribute_type INT,
    sort   INT,
    MEMO           VARCHAR(256) DEFAULT '''',
    IS_DELETE      INT          DEFAULT 0,
    CREATED_BY     INT          DEFAULT 0,
    CREATED_AT     DATETIME     DEFAULT getdate(),
    UPDATED_BY     INT          DEFAULT 0,
    UPDATED_AT     DATETIME     DEFAULT getdate(),
    PRIMARY KEY (ID)
);

EXEC sp_addextendedproperty ''MS_Description'', ''品类规格关联表(新增表)'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, null, null;
EXEC sp_addextendedproperty ''MS_Description'', ''ID'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', ID;
EXEC sp_addextendedproperty ''MS_Description'', ''一级品类'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', category1;
EXEC sp_addextendedproperty ''MS_Description'', ''二级品类'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', category2;
EXEC sp_addextendedproperty ''MS_Description'', ''品种ID(3级品类)'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', category3;
EXEC sp_addextendedproperty ''MS_Description'', ''规格ID'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', attribute_id;
EXEC sp_addextendedproperty ''MS_Description'', ''规格名'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', attribute_name;
EXEC sp_addextendedproperty ''MS_Description'', ''规格类型（1包装规格;2关键规格;3销售规格）'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', attribute_type;
EXEC sp_addextendedproperty ''MS_Description'', ''排序'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', sort;
EXEC sp_addextendedproperty ''MS_Description'', ''备注'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', MEMO;
EXEC sp_addextendedproperty ''MS_Description'', ''删除状态'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', IS_DELETE;
EXEC sp_addextendedproperty ''MS_Description'', ''创建人'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', CREATED_BY;
EXEC sp_addextendedproperty ''MS_Description'', ''创建时间'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', CREATED_AT;
EXEC sp_addextendedproperty ''MS_Description'', ''更新人'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', UPDATED_BY;
EXEC sp_addextendedproperty ''MS_Description'', ''更新时间'', ''SCHEMA'', dbo, ''table'', dbg_category_attribute, ''column'', UPDATED_AT;

CREATE TABLE [dbo].[dbg_spu](
                                ID VARCHAR(255),
                                category1 VARCHAR(255),
                                category2 VARCHAR(255),
                                category3 VARCHAR(255),
                                spu_no VARCHAR(255),
                                spu_name VARCHAR(255),
                                key_attribute_values VARCHAR(255),
                                status INT,
                                MEMO VARCHAR(256) DEFAULT  '',
                                IS_DELETE INT DEFAULT  0,
                                CREATED_BY INT DEFAULT  0,
                                CREATED_AT DATETIME DEFAULT  getdate(),
                                UPDATED_BY INT DEFAULT  0,
                                UPDATED_AT DATETIME DEFAULT  getdate()
);

EXEC sp_addextendedproperty 'MS_Description', 'SPU商品表(新增表)', 'SCHEMA', dbo, 'table', dbg_spu, null, null;
EXEC sp_addextendedproperty 'MS_Description', 'ID', 'SCHEMA', dbo, 'table', dbg_spu, 'column', ID;
EXEC sp_addextendedproperty 'MS_Description', '一级品类', 'SCHEMA', dbo, 'table', dbg_spu, 'column', category1;
EXEC sp_addextendedproperty 'MS_Description', '二级品类', 'SCHEMA', dbo, 'table', dbg_spu, 'column', category2;
EXEC sp_addextendedproperty 'MS_Description', '品种ID(3级品类)', 'SCHEMA', dbo, 'table', dbg_spu, 'column', category3;
EXEC sp_addextendedproperty 'MS_Description', 'spu编码（品种code+多个关键销售规格+包装值ID 下划线隔开（排序）:_11_222_12_）', 'SCHEMA', dbo, 'table', dbg_spu, 'column', spu_no;
EXEC sp_addextendedproperty 'MS_Description', '品种+关键规格（豆粕,43%;）', 'SCHEMA', dbo, 'table', dbg_spu, 'column', spu_name;
EXEC sp_addextendedproperty 'MS_Description', '关键规格信息多个关键规格值信息(Json,例： [{"attributeId":"20","attributeValueId":"20","attributeValueName":"43%"}]);(Json,例：[{""specValueId"":""20"",""specValueName"":""43%""}])")', 'SCHEMA', dbo, 'table', dbg_spu, 'column', key_attribute_values;
EXEC sp_addextendedproperty 'MS_Description', 'SPU商品状态(0禁用;1启用)', 'SCHEMA', dbo, 'table', dbg_spu, 'column', status;
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', dbo, 'table', dbg_spu, 'column', MEMO;
EXEC sp_addextendedproperty 'MS_Description', '删除状态', 'SCHEMA', dbo, 'table', dbg_spu, 'column', IS_DELETE;
EXEC sp_addextendedproperty 'MS_Description', '创建人', 'SCHEMA', dbo, 'table', dbg_spu, 'column', CREATED_BY;
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', dbo, 'table', dbg_spu, 'column', CREATED_AT;
EXEC sp_addextendedproperty 'MS_Description', '更新人', 'SCHEMA', dbo, 'table', dbg_spu, 'column', UPDATED_BY;
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', dbo, 'table', dbg_spu, 'column', UPDATED_AT;

CREATE TABLE [dbo].[dbg_spu_attribute_value](
                                                ID INT NOT NULL IDENTITY(1,1),
                                                MEMO VARCHAR(256) DEFAULT  '',
                                                IS_DELETE INT DEFAULT  0,
                                                CREATED_BY INT DEFAULT  0,
                                                CREATED_AT DATETIME DEFAULT  getdate(),
                                                UPDATED_BY INT DEFAULT  0,
                                                UPDATED_AT DATETIME DEFAULT  getdate(),
                                                spu_id VARCHAR(256) null,
                                                category1 VARCHAR(256) null,
                                                category2 VARCHAR(256) null,
                                                category3 VARCHAR(256) null,
                                                attribute_id INT null,
                                                attribute_value_id INT null,
                                                attribute_value_name VARCHAR(256) null,
                                                sort INT null,
                                                PRIMARY KEY (ID)
);

EXEC sp_addextendedproperty 'MS_Description', 'SPU的关键规格信息', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, null, null;
EXEC sp_addextendedproperty 'MS_Description', 'ID', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', ID;
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', MEMO;
EXEC sp_addextendedproperty 'MS_Description', '删除状态', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', IS_DELETE;
EXEC sp_addextendedproperty 'MS_Description', '创建人', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', CREATED_BY;
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', CREATED_AT;
EXEC sp_addextendedproperty 'MS_Description', '更新人', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', UPDATED_BY;
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', UPDATED_AT;
EXEC sp_addextendedproperty 'MS_Description', 'spuId', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', spu_id;
EXEC sp_addextendedproperty 'MS_Description', '一级品类', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', category1;
EXEC sp_addextendedproperty 'MS_Description', '二级品类', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', category2;
EXEC sp_addextendedproperty 'MS_Description', '三级品类', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', category3;
EXEC sp_addextendedproperty 'MS_Description', '关键规格ID', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', attribute_id;
EXEC sp_addextendedproperty 'MS_Description', '关键规格值ID', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', attribute_value_id;
EXEC sp_addextendedproperty 'MS_Description', '关键规格值名称', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', attribute_value_name;
EXEC sp_addextendedproperty 'MS_Description', '排序', 'SCHEMA', dbo, 'table', dbg_spu_attribute_value, 'column', sort;


CREATE TABLE [dbo].[dbg_sku_attribute_value] (
                                                 [id] int  IDENTITY(1,1) NOT NULL,
                                                 [goods_id] int DEFAULT ((0)) NULL,
                                                 [attribute_id] int DEFAULT ((0)) NULL,
                                                 [attribute_value_id] int DEFAULT ((0)) NULL,
                                                 [attribute_name] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
                                                 [attribute_value] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
                                                 [sort] int DEFAULT ((0)) NULL,
                                                 [is_deleted] tinyint DEFAULT ((0)) NULL,
                                                 [created_at] datetime DEFAULT (getdate()) NULL,
                                                 [updated_at] datetime DEFAULT (getdate()) NULL,
                                                 [category1] varchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
                                                 [category2] varchar(256) COLLATE Chinese_PRC_CI_AS  NULL,
                                                 [category3] varchar(256) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbg_sku_attribute_value] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
     'MS_Description', N'一级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_sku_attribute_value',
     'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'二级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_sku_attribute_value',
     'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
     'MS_Description', N'三级品类',
     'SCHEMA', N'dbo',
     'TABLE', N'dbg_sku_attribute_value',
     'COLUMN', N'category3'
GO


-- ----------------------------
-- Primary Key structure for table dbg_sku_attribute_value
-- ----------------------------
ALTER TABLE [dbo].[dbg_sku_attribute_value] ADD CONSTRAINT [PK__dbg_attr__3213E83F5A8F1EC3_copy1] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
GO
