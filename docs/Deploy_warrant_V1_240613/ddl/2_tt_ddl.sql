-- 新增脚本 TT 服务 dbt_tt_add dbt_tt_modify dbt_trade_ticket
ALTER TABLE [dbo].[dbt_tt_add] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_add] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'ship_warehouse_value'
    GO

-- dbt_tt_modify

ALTER TABLE [dbo].[dbt_tt_modify] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'ship_warehouse_value'
    GO
ALTER TABLE [dbo].[dbt_tt_modify] ADD [price_detail_info] ntext DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'价格详情Json',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'price_detail_info'
    GO

--dbt_tt_tranfer

ALTER TABLE [dbo].[dbt_tt_tranfer] ADD [this_fee]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'本次手续费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_tranfer',
    'COLUMN', N'this_fee'
    GO

-- dbt_trade_ticket


ALTER TABLE [dbo].[dbt_trade_ticket] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类，品类编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'category3'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [goods_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'goods_id'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [goods_name] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'goods_name'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'contract_nature'
    GO
