
-- dbt_contract

ALTER TABLE [dbo].[dbt_contract] ADD [contract_close_type] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [warrant_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_cancel_count'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'is_soybean2'
    GO

-- dbt_contract_history


ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_close_type] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [destination_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [package_weight_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [weight_check_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [invoice_type_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [ship_warehouse_value] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [category1] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category3] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [bu_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_code] nvarchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_cancel_count'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'is_soybean2'
    GO
