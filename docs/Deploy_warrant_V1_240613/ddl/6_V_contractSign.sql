SELECT        contract_sign.id, contract_sign.contract_id, contract_sign.contract_code, contract_sign.tt_id, contract_sign.tt_code, contract_sign.uuid, contract_sign.protocol_code, contract_sign.tt_type, contract_sign.status,
              contract_sign.contract_type, contract_sign.need_original_paper, contract_sign.sales_type, contract_sign.customer_id, contract_sign.customer_name, contract_sign.customer_code, contract_sign.original_customer_id,
              contract_sign.supplier_id, contract_sign.supplier_name, contract_sign.supplier_account, contract_sign.signature_url, contract_sign.signature_type, contract_sign.signature_way, contract_sign.signature_status,
              contract_sign.domain_code, contract_sign.goods_id, contract_sign.goods_name, contract_sign.owner_id, contract_sign.memo, contract_sign.extra_price, contract_sign.ldc_need_original_paper, contract_sign.ldc_frame,
              contract_sign.created_at, contract_sign.updated_at, contract_sign.is_deleted, contract_sign.reject_reason, contract_sign.invalid_reason, contract_sign.goods_category_id, contract_sign.delivery_factory_code,
              contract_sign.delivery_factory_name, contract_sign.qr_code_image, contract_sign.bar_code_image, contract_sign.sign_error_code, contract_sign.sign_error_message, contract_sign.is_on_line_sign,
              contract_sign.belong_customer_id, contract_sign.frame_protocol_type, contract_sign.sign_type, contract_sign.trade_type, contract_sign.contract_source, contract_sign.is_customer_signature, contract_sign.this_time_fee,
              contract_sign.company_id, contract_sign.company_name, contract_sign.voluntarily_sign_type, contract_sign.invoke_no, contract_sign.confirm_status, contract_sign.customer_non_frame, contract_sign.cancel_reason,
              customer.use_yqq, customer.is_columbus, sign.use_customer_id, protocol.frame_protocol, protocol.protocol_start_date, protocol.protocol_end_date, paper.ldc_frame AS original_ldc_frame, tt.approval_status,
              tt.status AS tt_status, contract.status AS contract_status, contract_sign.category3, contract_sign.category2, contract_sign.category1, contract_sign.bu_code, contract_sign.contract_sign_type, contract_sign.ws_type,
              contract_sign.termine_type, contract_sign.customer_signature_type, contract_sign.provide_type, contract_sign.ldc_signature_type, contract_sign.need_review
FROM            dbo.dbt_contract_sign AS contract_sign LEFT OUTER JOIN
                dbo.dbt_trade_ticket AS tt ON tt.id = contract_sign.tt_id LEFT OUTER JOIN
                dbo.dbt_contract AS contract ON contract.id = contract_sign.contract_id LEFT OUTER JOIN
                (SELECT        id, sales_type, company_id, goods_category_id, (CASE WHEN sales_type = 2 THEN customer_id WHEN sales_type = 1 THEN supplier_id END) AS use_customer_id
                 FROM            dbo.dbt_contract_sign) AS sign ON contract_sign.id = sign.id LEFT OUTER JOIN
                dbo.dba_customer AS customer ON sign.use_customer_id = customer.id LEFT OUTER JOIN
                dbo.dba_customer_protocol AS protocol ON protocol.customer_id = sign.use_customer_id AND protocol.sale_type = sign.sales_type AND protocol.company_id = sign.company_id AND
                                                         protocol.category_id = sign.goods_category_id AND protocol.is_deleted = 0 LEFT OUTER JOIN
                dbo.dba_customer_original_paper AS paper ON paper.customer_id = sign.use_customer_id AND paper.sale_type = sign.sales_type AND paper.company_id = sign.company_id AND
                                                            paper.category_id = sign.goods_category_id AND paper.is_deleted = 0