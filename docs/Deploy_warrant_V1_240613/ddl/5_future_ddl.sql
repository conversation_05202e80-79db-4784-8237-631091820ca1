--套保平仓
ALTER TABLE [dbo].[dbf_position] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbf_position] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbf_position] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'category3'
    GO

--头寸申请
ALTER TABLE [dbo].[dbf_price_allocate] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'category3'
    GO

--头寸申请单
ALTER TABLE [dbo].[dbf_price_apply] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_apply',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbf_price_apply] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_apply',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbf_price_apply] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_apply',
    'COLUMN', N'category3'
    GO

--头寸成交单
ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [category1] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_deal_detail',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [category2] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_deal_detail',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [category3] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_deal_detail',
    'COLUMN', N'category3'
    GO