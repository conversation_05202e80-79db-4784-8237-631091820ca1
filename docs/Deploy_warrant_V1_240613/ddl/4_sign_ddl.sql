ALTER TABLE [dbo].[dbt_contract_sign] ADD [provide_type] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'出具方式(1不出具;2文件上传;3数字合同）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'provide_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD need_review nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'审核方式（需审核 不审核）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'need_review'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [ldc_signature_type] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'ldc签章方式（不签章 线下签 易企签-标准 易企签-静默 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'ldc_signature_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [customer_signature_type] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'客户签章方式（不签章 线下签 易企签）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'customer_signature_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [termine_type] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'终止方式:0.补充协议 1.尾量终止',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'termine_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [ws_type] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'ws类型(0使用ws;1非ws但系统;非系统2)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'ws_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [contract_sign_type] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'协议类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'contract_sign_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [bu_code] nvarchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'bu_code'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category1] int DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category2] int  DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category3] int  DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'category3'
    GO


IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbt_contract_sign_detail]') AND type IN ('U'))
DROP TABLE [dbo].[dbt_contract_sign_detail]
GO

CREATE TABLE [dbo].[dbt_contract_sign_detail] (
    [ID] int  IDENTITY(1,1) NOT NULL,
    [contract_sign_id] int  NULL,
    [contract_id] int  NULL,
    [contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [tt_id] int  NULL,
    [tt_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [tt_type] int  NULL,
    [trade_type] int  NULL,
    [new_contract_content] text COLLATE Chinese_PRC_CI_AS  NULL,
    [source_contract_content] text COLLATE Chinese_PRC_CI_AS  NULL,
    [modify_content] text COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dbt_contract_sign_detail] SET (LOCK_ESCALATION = TABLE)
    GO


    -- ----------------------------
-- Auto increment value for dbt_contract_sign_detail
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dbt_contract_sign_detail]', RESEED, 9)
    GO


-- ----------------------------
-- Primary Key structure for table dbt_contract_sign_detail
-- ----------------------------
ALTER TABLE [dbo].[dbt_contract_sign_detail] ADD CONSTRAINT [PK__dbt_cont__3214EC27D9E9966D] PRIMARY KEY CLUSTERED ([ID])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO