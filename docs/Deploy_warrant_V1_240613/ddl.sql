
-- dbt_contract

ALTER TABLE [dbo].[dbt_contract] ADD [contract_close_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [destination_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [package_weight_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [delivery_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [weight_check_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [invoice_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [ship_warehouse_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [category1] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category2] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [category3] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [bu_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [warrant_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_cancel_count'
    GO
ALTER TABLE [dbo].[dbt_contract] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'is_soybean2'
    GO

-- dbt_contract_history


ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_close_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_close_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [destination_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [package_weight_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [weight_check_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [invoice_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [ship_warehouse_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [category1] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category2] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category3] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [bu_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_nature'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_cancel_count'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'is_soybean2'
    GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [destination_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'目的地名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'destination_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [package_weight_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'袋皮扣重名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'package_weight_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交提货方式名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [weight_check_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'重量检验名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'weight_check_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [invoice_type_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'invoice_type_value'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [ship_warehouse_value] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发货库点',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'ship_warehouse_value'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [bu_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线编号（现货-ST、DCE仓单-FT）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'bu_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category1] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category1'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category2] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category2'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [category3] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category3'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_nature] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同性质 1. 现货贸易合同 2.仓单贸易合同 3.仓单提货合同 4.仓单提货密码/货权合同',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_nature'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_code] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_cancel_count] decimal(15,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'已注销数量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_cancel_count'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套id',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_id'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [contract_close_type] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'合同关闭的类型（1.协议关闭 2.执行关闭 3.尾量关闭 ）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'contract_close_type'
    GO
ALTER TABLE [dbo].[dbt_contract_history] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'is_soybean2'
    GO

-- 品类主表新增字段
ALTER TABLE [dbo].[dbg_category] ADD [serial_no] varchar NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'主编码',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'serial_no';

-- 商品规格表新增字段
ALTER TABLE [dbo].[dbg_attribute] ADD [display_name] nvarchar(64) NULL

-- 商品表新增字段

ALTER TABLE [dbo].[dbg_goods] ADD [spu_id] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [full_name] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [sku_no] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [key_attribute_values] text NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category1] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category2] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [category3] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [is_warrant] int NULL
GO

ALTER TABLE [dbo].[dbg_goods] ADD [is_global_oil] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'SPU商品ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'spu_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'系统生成的sku货品全称',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'full_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'品种code+多个关键+销售规格+包装值ID
下划线隔开（排序）:_11_222_12_',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'sku_no'
GO

EXEC sp_addextendedproperty
'MS_Description', N'多个关键规格值+销售规格信息(Json,例：
[{"attributeId":"20","attributeValueId":"20","attributeValueName":"43%"}])',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'key_attribute_values'
GO

EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'category1'
GO

EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'category2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'三级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'category3'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否仓单标品(0否 1是)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'is_warrant'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否global_oil(0否 1是)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'is_global_oil'
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [provide_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [provide_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [ldc_signature_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [customer_signature_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [termine_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [ws_type] int NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [contract_sign_type] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [bu_code] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category1] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category2] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [category3] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [contract_content] text NULL
GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [modify_content] text COLLATE Chinese_PRC_CI_AS_KS NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'出具方式(1不出具;2文件上传;3数字合同）',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'provide_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'审核方式（需审核 不审核）',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'provide_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'ldc签章方式（不签章 线下签 易企签-标准 易企签-静默 ）',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'ldc_signature_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'客户签章方式（不签章 线下签 易企签）',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'customer_signature_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'终止方式:0.补充协议 1.尾量终止',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'termine_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'ws类型(0使用ws;1非ws但系统;非系统2)',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'ws_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'协议类型',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'contract_sign_type'
GO
EXEC sp_addextendedproperty
'MS_Description', N'业务线',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'bu_code'
GO
EXEC sp_addextendedproperty
'MS_Description', N'一级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'category1'
GO
EXEC sp_addextendedproperty
'MS_Description', N'二级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'category2'
GO
EXEC sp_addextendedproperty
'MS_Description', N'三级品类',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'category3'
GO
EXEC sp_addextendedproperty
'MS_Description', N'合同基础数据信息',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'contract_content'
GO
EXEC sp_addextendedproperty
'MS_Description', N'TT变更信息',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_sign',
'COLUMN', N'modify_content'
GO
