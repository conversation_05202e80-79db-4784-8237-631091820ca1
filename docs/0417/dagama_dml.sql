INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name]) VALUES (N'', N'期货站内信通知', N'inmail', N'${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}', N'${auditStatus!} ${priceType!} ${orientation!} ${dealDirection!} ${dominantCode!} ${applyHandNum!}手 ${applyPrice!} ${pendingType!} 编号:${applyCode!}', N'normal', NULL, '2023-04-17 01:33:55.737', N'', '2023-04-17 01:33:55.737', NULL, N'ADD_REVOCATION_APPLY_FOR_INMAIL', NULL, NULL);


INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name]) VALUES ( N'ADD_REVOCATION_APPLY_FOR_INMAIL', N'', N'', N'inmail', N'NOTICE', (select top 1 id from dbm_template ORDER BY id desc), N'system', N'', N'role', N'', '2023-04-17 01:41:48.693', '2023-04-17 01:41:48.693', NULL, NULL, 1, 1, 1, 0, NULL, NULL);



INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name]) VALUES (N'', N'期货站内信通知（改单）', N'inmail', N'${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}', N'改单 ${priceType!};
${priceType!} ${orientation!} ${dealDirection!} ${dominantCode!} ${applyHandNum!}手 ${applyPrice!} ${pendingType!} 编号:${applyCode!};
${ChangePendingDominantCode!} ${ChangeTranferDominantCode!} ${changeHandNum!} ${changePrice!} ${changeDiffPrice!}', N'normal', NULL, '2023-04-17 01:49:32.520', NULL, '2023-04-17 01:49:32.520', NULL, N'UPDATE_APPLY_FOR_INMAIL', NULL, NULL);


INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name]) VALUES ( N'UPDATE_APPLY_FOR_INMAIL', N'', N'', N'inmail', N'NOTICE', (select top 1 id from dbm_template ORDER BY id desc), N'system', N'', N'role', N'', '2023-04-17 01:50:47.370', '2023-04-21 15:24:35.877', NULL, N'1', 1, 1, 1, 0, NULL, N'admin1');
