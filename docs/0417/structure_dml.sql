SET IDENTITY_INSERT dba_structure_rule ON;
INSERT INTO [dbo].[dba_structure_rule] ( [id], [structure_name], [status], [is_deleted], [created_by], [updated_by], [created_by_name], [updated_by_name], [created_at], [updated_at] )
VALUES
    ( 1, N'每日敲出每日加倍', 1, 0, N'', N'', N'', N'', '2023-04-18 08:16:05.980', '2023-04-18 08:16:05.980' );
INSERT INTO [dbo].[dba_structure_rule] ( [id], [structure_name], [status], [is_deleted], [created_by], [updated_by], [created_by_name], [updated_by_name], [created_at], [updated_at] )
VALUES
    ( 2, N'保证买入每日加倍', 1, 0, N'', N'', N'', N'', '2023-04-18 08:16:10.407', '2023-04-18 08:16:10.407' );
INSERT INTO [dbo].[dba_structure_rule] ( [id], [structure_name], [status], [is_deleted], [created_by], [updated_by], [created_by_name], [updated_by_name], [created_at], [updated_at] )
VALUES
    ( 3, N'每日敲出到期日加倍', 1, 0, N'', N'', N'', N'', '2023-04-18 08:16:11.620', '2023-04-18 08:16:11.620' );

SET IDENTITY_INSERT dba_structure_rule OFF;


ALTER TABLE [dbo].[dbt_contract_structure] ADD [structure_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL


update dbt_tt_structure set structure_name = b.structure_name  from dbt_tt_structure a LEFT JOIN dba_structure_rule b on a.structure_type = b.id;
update dbt_contract_structure set structure_name = b.structure_name  from dbt_contract_structure a LEFT JOIN dba_structure_rule b on a.structure_type = b.id;