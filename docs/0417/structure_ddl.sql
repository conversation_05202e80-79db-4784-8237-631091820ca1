CREATE TABLE [dbo].[dba_structure_rule] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [structure_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [status] int DEFAULT ((1)) NULL,
    [is_deleted] int DEFAULT ((0)) NULL,
    [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [updated_at] datetime DEFAULT (getdate()) NULL,
    CONSTRAINT [PK__dba_stru__3213E83FCAA4554D] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO


ALTER TABLE [dbo].[dbt_tt_structure] ADD [structure_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL