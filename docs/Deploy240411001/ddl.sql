ALTER TABLE [dbo].[dbt_tt_add] ADD [residual_risk_limit] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'剩余风险限额',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'residual_risk_limit'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [residual_risk_usage] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'剩余风险使用',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'residual_risk_usage'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [residual_risk_residue] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'剩余风险剩余',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'residual_risk_residue'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [residual_risk_trade_status] varchar(255) DEFAULT '' NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易状态(NoTrade;CBT-ConsultBeforeTrade; Active;Inactive;pending) ',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'residual_risk_trade_status'
    GO


-- 修改质量指标表的refer_code的注释
exec sp_updateextendedproperty N'MS_Description', N'发货库点code/客户code/集团客户code（ 外键：warehouse ： dba_factory_warehouse的lkg_warehouse_code字段； customer/enterprise ：dba_customer表的linkage_customer_code）', N'SCHEMA', N'dbo',N'table',N'dbh_quality_rule',N'column', N'refer_code';
