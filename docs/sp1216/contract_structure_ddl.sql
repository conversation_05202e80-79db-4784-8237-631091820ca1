ALTER TABLE [dbo].[dbt_contract_structure] ADD [cumulative_deal_num] decimal(18,6) DEFAULT ((0)) NULL
GO

ALTER TABLE [dbo].[dbt_contract_structure] ADD [cumulative_allocate_num] decimal(18,6) DEFAULT ((0)) NULL
GO

ALTER TABLE [dbo].[dbt_contract_structure] ADD [cumulative_cash_return] decimal(18,6) DEFAULT ((0)) NULL
GO

ALTER TABLE [dbo].[dbt_contract_structure] ADD [cumulative_not_deal_num] decimal(18,6) DEFAULT ((0)) NULL
GO


ALTER TABLE [dbo].[dbt_contract_structure] ADD  [is_deleted]  int DEFAULT 0  NOT NULL
GO




EXEC sp_addextendedproperty
'MS_Description', N'累计成交量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N'累计分配量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_allocate_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N',累积返还金额',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_cash_return'
GO

EXEC sp_addextendedproperty
'MS_Description', N',累积释放量',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'cumulative_not_deal_num'
GO

EXEC sp_addextendedproperty
'MS_Description', N',逻辑删除（0未被删除，1已被删除）',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_structure',
'COLUMN', N'is_deleted'
GO

