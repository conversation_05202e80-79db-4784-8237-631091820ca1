--视图
SELECT
tt.*,
customer.enterprise_name AS enterprise_name,
supplier.enterprise_name AS supplier_enterprise_name,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_start_time
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_start_time
   ELSE contract.delivery_start_time END) AS delivery_start_time,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_end_time
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_end_time
   ELSE contract.delivery_end_time END) AS delivery_end_time,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.goods_package_id
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.goods_package_id
   ELSE contract.goods_package_id END) AS goods_package_id,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.goods_spec_id
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.goods_spec_id
   ELSE contract.goods_spec_id END) AS goods_spec_id,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.ship_warehouse_id
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.ship_warehouse_id
   ELSE contract.ship_warehouse_id END) AS ship_warehouse_id,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_factory_code
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_factory_code
   ELSE contract.delivery_factory_code END) AS delivery_factory_code,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.extra_price
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.extra_price
   ELSE contract.extra_price END) AS extra_price,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.unit_price
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.unit_price
   ELSE contract.unit_price END) AS unit_price,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.delivery_type
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.delivery_type
   ELSE contract.delivery_type END) AS delivery_type,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.package_weight
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.package_weight
   ELSE contract.package_weight END) AS package_weight,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.price_end_time
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.price_end_time
   ELSE contract.price_end_time END) AS price_end_time,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.deposit_rate
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.deposit_rate
   ELSE contract.deposit_rate END) AS deposit_rate,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.added_deposit_rate
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.added_deposit_rate
   ELSE contract.added_deposit_rate END) AS added_deposit_rate,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.credit_days
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.credit_days
   ELSE contract.credit_days END) AS credit_days,
(CASE WHEN tt_add.tt_id IS NOT NULL AND tt_add.tt_id != '' THEN tt_add.memo
   WHEN tt_modify.tt_id IS NOT NULL AND tt_modify.tt_id != '' THEN tt_modify.memo
   ELSE contract.memo END) AS memo,
	 (CASE WHEN tt.type =2 THEN tt_modify.goods_id
   WHEN tt.type =3 THEN tt_modify.goods_id
	 WHEN tt.type =6 THEN tt_price.goods_id
	 WHEN tt.type =12 THEN tt_price.goods_id
	 	 WHEN tt.type =4 THEN tt_tranfer.goods_id
	 	 WHEN tt.type =5 THEN tt_tranfer.goods_id
   ELSE tt_add.goods_id END) AS goods_id
FROM
dbo.dbt_trade_ticket tt
LEFT JOIN dbo.dba_customer customer ON tt.customer_id = customer.id
LEFT JOIN dbo.dba_customer supplier ON tt.supplier_id = supplier.id
LEFT JOIN dbo.dbt_tt_add tt_add ON tt_add.tt_id = tt.id
LEFT JOIN dbo.dbt_tt_modify tt_modify ON tt_modify.tt_id = tt.id
LEFT JOIN dbo.dbt_contract contract ON contract.id = tt.contract_id
LEFT JOIN dbo.dbt_tt_tranfer tt_tranfer ON tt_tranfer.tt_id = tt.id
LEFT JOIN dbo.dbt_tt_price tt_price ON tt_price.tt_id = tt.id




-- createName
ALTER TABLE [dbo].[dba_customer] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_customer] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer',
'COLUMN', N'updated_by_name'


ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [created_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [updated_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_deposit_rate',
'COLUMN', N'updated_by_name'


ALTER TABLE [dbo].[dba_contact] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_contact] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_contact',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_contact',
'COLUMN', N'updated_by_name'



ALTER TABLE [dbo].[dba_customer_detail] ADD [created_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [updated_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_detail',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_detail',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_detail',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_detail',
'COLUMN', N'updated_by_name'



ALTER TABLE [dbo].[dba_customer_bank] ADD [created_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [updated_by] int  NULL
GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_bank',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_bank',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_bank',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_bank',
'COLUMN', N'updated_by_name'

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_credit_payment',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_customer_credit_payment',
'COLUMN', N'updated_by_name'


ALTER TABLE [dbo].[dba_factory] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_factory] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_factory',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_factory',
'COLUMN', N'updated_by_name'

ALTER TABLE [dbo].[dba_trade_day] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dba_trade_day] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_trade_day',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_trade_day',
'COLUMN', N'updated_by_name'


ALTER TABLE [dbo].[dbz_system_rule_item] ADD [created_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

ALTER TABLE [dbo].[dbz_system_rule_item] ADD [updated_by_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT ((1)) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dbz_system_rule_item',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人名称',
'SCHEMA', N'dbo',
'TABLE', N'dbz_system_rule_item',
'COLUMN', N'updated_by_name'


ALTER TABLE [dbo].[dbm_business_template] ADD [created_by_name] nvarchar(64) NULL
GO

ALTER TABLE [dbo].[dbm_business_template] ADD [updated_by_name] nvarchar(64) NULL




ALTER TABLE [dbo].[dbm_template] ADD [created_by_name] nvarchar(64) DEFAULT '' NULL
GO

ALTER TABLE [dbo].[dbm_template] ADD [updated_by_name] nvarchar(64) DEFAULT '' NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人名称',
'SCHEMA', N'dbo',
'TABLE', N'dbm_template',
'COLUMN', N'created_by_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'修改人名称',
'SCHEMA', N'dbo',
'TABLE', N'dbm_template',
'COLUMN', N'updated_by_name'
