UPDATE  [dbo].[dba_customer]  set created_by_name = b.name  FROM [dbo].[dba_customer] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_customer]  set updated_by_name = b.name  FROM [dbo].[dba_customer] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_customer_deposit_rate]  set created_by_name = b.name  FROM [dbo].[dba_customer_deposit_rate] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_customer_deposit_rate]  set updated_by_name = b.name  FROM [dbo].[dba_customer_deposit_rate] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_contact]  set created_by_name = b.name  FROM [dbo].[dba_contact] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_contact]  set updated_by_name = b.name  FROM [dbo].[dba_contact] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_customer_detail]  set created_by_name = b.name  FROM [dbo].[dba_customer_detail] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_customer_detail]  set updated_by_name = b.name  FROM [dbo].[dba_customer_detail] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_customer_bank]  set created_by_name = b.name  FROM [dbo].[dba_customer_bank] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_customer_bank]  set updated_by_name = b.name  FROM [dbo].[dba_customer_bank] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_customer_credit_payment]  set created_by_name = b.name  FROM [dbo].[dba_customer_credit_payment] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_customer_credit_payment]  set updated_by_name = b.name  FROM [dbo].[dba_customer_credit_payment] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_factory]  set created_by_name = b.name  FROM [dbo].[dba_factory] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_factory]  set updated_by_name = b.name  FROM [dbo].[dba_factory] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dba_trade_day]  set created_by_name = b.name  FROM [dbo].[dba_trade_day] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dba_trade_day]  set updated_by_name = b.name  FROM [dbo].[dba_trade_day] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dbz_system_rule_item]  set created_by_name = b.name  FROM [dbo].[dbz_system_rule_item] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dbz_system_rule_item]  set updated_by_name = b.name  FROM [dbo].[dbz_system_rule_item] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dbm_business_template]  set created_by_name = b.name  FROM [dbo].[dbm_business_template] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dbm_business_template]  set updated_by_name = b.name  FROM [dbo].[dbm_business_template] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;

UPDATE  [dbo].[dbm_template]  set created_by_name = b.name  FROM [dbo].[dbm_template] a LEFT JOIN [dbo]. dba_employ b  on  a.created_by = b.id;
UPDATE  [dbo].[dbm_template]  set updated_by_name = b.name  FROM [dbo].[dbm_template] a LEFT JOIN [dbo]. dba_employ b  on  a.updated_by = b.id;