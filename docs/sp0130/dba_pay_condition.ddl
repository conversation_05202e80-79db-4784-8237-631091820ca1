ALTER TABLE [dbo].[dbt_contract] ADD [pay_condition_id] int  NULL;
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [pay_condition_id] int  NULL;
ALTER TABLE [dbo].[dbt_contract_history] ADD [pay_condition_id] int  NULL;

/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 02/03/2023 11:25:44
*/


-- ----------------------------
-- Table structure for dba_pay_condition
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_pay_condition]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_pay_condition]
GO

CREATE TABLE [dbo].[dba_pay_condition] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [sort] int  NULL,
  [status] int  NULL,
  [sales_type] int  NULL,
  [contract_type] varchar(64) COLLATE Chinese_PRC_CI_AS  NULL,
  [payment_type] int  NULL,
  [credit_days] int  NULL,
  [goods_category_id] int  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [created_by] int DEFAULT ((0)) NULL,
  [updated_by] int DEFAULT ((0)) NULL
)
GO

ALTER TABLE [dbo].[dba_pay_condition] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of dba_pay_condition
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_pay_condition] ON
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'1', N'10% after ctr date within 1 working day, 90% after delivery within 15days', N'10% after ctr date within 1 working day, 90% after delivery within 15days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'2', N'10% after ctr date within 1 working day, 90% after delivery within 30days', N'10% after ctr date within 1 working day, 90% after delivery within 30days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'3', N'10% after ctr date within 1 working day, 90% before delivery', N'10% after ctr date within 1 working day, 90% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'4', N'10% after pricing, 95% before delivery  ', N'10% after pricing, 95% before delivery  ', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'5', N'100% after delivery within 1 day', N'100% after delivery within 1 day', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'6', N'100% after delivery within 15 days', N'100% after delivery within 15 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'7', N'100% after delivery within 2 days', N'100% after delivery within 2 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'8', N'100% after delivery within 20 days', N'100% after delivery within 20 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'9', N'100% after delivery within 3 days', N'100% after delivery within 3 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'10', N'100% after delivery within 30 days', N'100% after delivery within 30 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'11', N'100% after delivery within 365 days', N'100% after delivery within 365 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'12', N'100% after delivery within 45 days', N'100% after delivery within 45 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'13', N'100% after delivery within 5 days', N'100% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'14', N'100% after delivery within 7 days ', N'100% after delivery within 7 days ', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'15', N'100% after delivery within 90 days', N'100% after delivery within 90 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'16', N'100% prepayment', N'100% prepayment', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'17', N'110% prepayment before pricing', N'110% prepayment before pricing', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'18', N'15% after ctr date within 1 working day, 85% before delivery', N'15% after ctr date within 1 working day, 85% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'19', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 10 days', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 10 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'20', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 15 days', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 15 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'21', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 30 days', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'22', N'2% after ctr date within 1 working day,3% after pricing within 1 working day, 95% before delivery ', N'2% after ctr date within 1 working day,3% after pricing within 1 working day, 95% before delivery ', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'23', N'20% after ctr date within 1 working day, 80% before delivery', N'20% after ctr date within 1 working day, 80% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'24', N'3% after ctr date within 1 working day, 97% after delivery within 15days', N'3% after ctr date within 1 working day, 97% after delivery within 15days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'25', N'3% after ctr date within 1 working day, 97% after delivery within 30days', N'3% after ctr date within 1 working day, 97% after delivery within 30days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'26', N'3% after ctr date within 1 working day, 97% before delivery ', N'3% after ctr date within 1 working day, 97% before delivery ', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'27', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 10 days', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 10 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'28', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 15 days', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 15 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'29', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 30 days', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'30', N'3% after ctr date within 1 working day,2% after pricing within 1 working day, 95% before delivery', N'3% after ctr date within 1 working day,2% after pricing within 1 working day, 95% before delivery', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'31', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 10 days', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 10 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'32', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 15 days', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 15 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'33', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 30 days', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'34', N'3% after ctr date within 1 working day,3% after pricing within 1 working day, 94% before delivery', N'3% after ctr date within 1 working day,3% after pricing within 1 working day, 94% before delivery', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'35', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 10 days', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 10 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'36', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 15 days', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 15 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'37', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 30 days', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'38', N'3% after ctr date within 1 working day,5% after pricing within 1 working day, 92% before delivery', N'3% after ctr date within 1 working day,5% after pricing within 1 working day, 92% before delivery', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'39', N'5% after ctr date within 1 working day, 95% after delivery within 15days', N'5% after ctr date within 1 working day, 95% after delivery within 15days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'40', N'5% after ctr date within 1 working day, 95% after delivery within 30 days', N'5% after ctr date within 1 working day, 95% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'41', N'5% after ctr date within 1 working day, 95% before delivery', N'5% after ctr date within 1 working day, 95% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'42', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 10days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 10days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'43', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 15 days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 15 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'44', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 30 days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 30 days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'45', N'5% after ctr date within 1 working day,5% after pricing within 1 working day, 90% before delivery', N'5% after ctr date within 1 working day,5% after pricing within 1 working day, 90% before delivery', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'46', N'5% after pricing within 1 working day, 95% after delivery within 10 days', N'5% after pricing within 1 working day, 95% after delivery within 10 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'47', N'5% after pricing within 1 working day, 95% after delivery within 15 days', N'5% after pricing within 1 working day, 95% after delivery within 15 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'48', N'5% after pricing within 1 working day, 95% after delivery within 30 days', N'5% after pricing within 1 working day, 95% after delivery within 30 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'49', N'5% after pricing within 1 working day, 95% before delivery', N'5% after pricing within 1 working day, 95% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'50', N'8% after ctr date within 1 working day, 92% after delivery within 15days', N'8% after ctr date within 1 working day, 92% after delivery within 15days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'51', N'8% after ctr date within 1 working day, 92% after delivery within 30days', N'8% after ctr date within 1 working day, 92% after delivery within 30days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'52', N'8% after ctr date within 1 working day, 92% before delivery', N'8% after ctr date within 1 working day, 92% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'53', N'85% after delivery, 15% after invoice', N'85% after delivery, 15% after invoice', NULL, N'1', N'1', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'54', N'85% before delivery, 15% after invoice', N'85% before delivery, 15% after invoice', NULL, N'1', N'1', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'55', N'7% after ctr date within 1 working day, 93% before delivery', N'7% after ctr date within 1 working day, 93% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'56', N'10% after ctr date within 1 working day, 90% after delivery within 2days', N'10% after ctr date within 1 working day, 90% after delivery within 2days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'57', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 3days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 3days', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'58', N'3% after pricing within 1 working day, 97% before delivery  ', N'3% after pricing within 1 working day, 97% before delivery  ', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'59', N'6% after ctr date within 1 working day, 94% before delivery', N'6% after ctr date within 1 working day, 94% before delivery', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'60', N'3% after ctr date within 1 working day,7% after pricing within 1 working day, 90% before delivery', N'3% after ctr date within 1 working day,7% after pricing within 1 working day, 90% before delivery', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'61', N'110% prepayment before pricing （7 days）', N'110% prepayment before pricing （7 days）', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'62', N'110% prepayment before pricing （10 days）', N'110% prepayment before pricing （10 days）', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'63', N'110% prepayment before pricing （15 days）', N'110% prepayment before pricing （15 days）', NULL, N'1', N'2', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'64', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 7 days', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'65', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 7 days', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'66', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 7 days', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'67', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 7 days', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'68', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 7 days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'69', N'5% after pricing within 1 working day, 95% after delivery within 7 days', N'5% after pricing within 1 working day, 95% after delivery within 7 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'70', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 5 days', N'2% after ctr date within 1 working day,3% after pricing within 1 working date, 95% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'71', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 5 days', N'3% after ctr date within 1 working day,2% after pricing within 1 working date, 95% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'72', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 5 days', N'3% after ctr date within 1 working day,3% after pricing within 1 working date, 94% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'73', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 5 days', N'3% after ctr date within 1 working day,5% after pricing within 1 working date, 92% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'74', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 5 days', N'5% after ctr date within 1 working day,5% after pricing within 1 working date, 90% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

INSERT INTO [dbo].[dba_pay_condition] ([id], [name], [code], [sort], [status], [sales_type], [contract_type], [payment_type], [credit_days], [goods_category_id], [created_at], [updated_at], [created_by], [updated_by]) VALUES (N'75', N'5% after pricing within 1 working day, 95% after delivery within 5 days', N'5% after pricing within 1 working day, 95% after delivery within 5 days', NULL, N'1', N'0', NULL, NULL, NULL, NULL, N'2023-02-19 07:13:42.637', N'2023-02-19 07:13:42.637', N'0', N'0')
GO

SET IDENTITY_INSERT [dbo].[dba_pay_condition] OFF
GO


-- ----------------------------
-- Auto increment value for dba_pay_condition
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_pay_condition]', RESEED, 75)
GO

