INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ( N'', N'哥伦布主管理员账户信息变更通知', N'email', N'欢迎使用路易达孚电子商务平台', N'<p>尊敬的${customerName!}，</p>

<p> </p>

<p> 欢迎您代表${companyName!}使用路易达孚电子商务平台！</p>

<p> 平台登录地址如下，建议使用主流浏览器Edge或者Chrome: 路易达孚电子商务平台，请在平台首页处获取操作手册和录屏文件。</p>

<p> 系统登陆账号为您提供的手机号，默认密码“111111”，身份验证还需要结合短信验证码，登陆后请务必修改默认密码。</p>

<p> 后续使用过程中，如果您忘记密码，请在登录页面自行重置。</p>

<p> </p>

<p>路易达孚电子商务平台支持团队</p>

<p>${sendDate!}</p>', N'normal', NULL, '2023-08-14 08:15:18.693', NULL, '2023-08-14 17:53:41.240', N'1', N'COLUMBUS_SYSTEM_CHANGE_NOTICE_1', NULL, N'admin1', 0)
    GO
declare @id int

set @id=@@identity
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES (N'COLUMBUS_SYSTEM_CHANGE_NOTICE_1', N'', N'', N'email', N'NOTICE',  @id , N'system', N'', N'columbus_user', N'',getDate(),getDate(), NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, '单条实时发送');




INSERT INTO [dbo].[dbm_template]( [third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES ( N'', N'哥伦布主管理员账户信息变更通知', N'email', N'您已成为路易达孚电子商务平台主管理员', N'
<p> 欢迎您代表${companyName!}使用路易达孚电子商务平台！您已拥有贵公司的主管理员角色。</p>

<p> 平台登录地址如下，建议使用主流浏览器Edge或者Chrome: 路易达孚电子商务平台，请在平台首页处获取操作手册和录屏文件。</p>

<p> 系统登陆账号为您的手机号，后续使用过程中，如果您忘记密码，请在登录页面自行重置。</p>

<p> </p>

<p>路易达孚电子商务平台支持团队</p>

<p>${sendDate!}</p>', N'normal', NULL, '2023-08-14 08:19:34.127', NULL, '2023-08-14 17:53:02.547', N'1', N'COLUMBUS_SYSTEM_CHANGE_NOTICE_2', NULL, N'admin1', 0);
GO
declare @id1 int

set @id1=@@identity


INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [template_id], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency]) VALUES (N'COLUMBUS_SYSTEM_CHANGE_NOTICE_2', N'', N'', N'email', N'NOTICE', @id1 , N'system', N'', N'personal', N'',getDate(),getDate(), NULL, NULL, 1, 2, 1, 0, NULL, NULL, 1, '单条实时发送');
