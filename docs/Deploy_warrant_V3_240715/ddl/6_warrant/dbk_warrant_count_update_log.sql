/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 17/10/2024 14:48:51
*/


-- ----------------------------
-- Table structure for dbk_warrant_count_update_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_warrant_count_update_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_warrant_count_update_log]
GO

CREATE TABLE [dbo].[dbk_warrant_count_update_log] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [warrant_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [operation_type] int  NULL,
  [operation_count] text COLLATE Chinese_PRC_CI_AS  NULL,
  [after_warrant_count] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [change_warrant_count] decimal(18,6)  NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbk_warrant_count_update_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for dbk_warrant_count_update_log
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_warrant_count_update_log]', RESEED, 358)
GO


-- ----------------------------
-- Primary Key structure for table dbk_warrant_count_update_log
-- ----------------------------
ALTER TABLE [dbo].[dbk_warrant_count_update_log] ADD CONSTRAINT [PK__dbk_warr__3213E83F5236983F] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

