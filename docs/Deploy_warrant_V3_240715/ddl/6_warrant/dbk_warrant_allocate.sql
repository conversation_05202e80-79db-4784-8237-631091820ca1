/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 17/10/2024 14:47:57
*/


-- ----------------------------
-- Table structure for dbk_warrant_allocate
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_warrant_allocate]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_warrant_allocate]
GO

CREATE TABLE [dbo].[dbk_warrant_allocate] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [warrant_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [operation_count] decimal(18,6)  NULL,
  [contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [contract_id] int  NULL,
  [supplier_id] int  NULL,
  [supplier_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [customer_id] int  NULL,
  [customer_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [operation_type] int  NULL,
  [status] int DEFAULT ((1)) NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime  NULL,
  [tt_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [tt_id] int  NULL
)
GO

ALTER TABLE [dbo].[dbk_warrant_allocate] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单编号',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'warrant_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作数量',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'operation_count'
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单合同id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'卖方主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'supplier_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'卖方主体名称',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'supplier_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'买方主体id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'customer_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'买方主体名称',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'customer_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'操作类型',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'operation_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TTcode',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'tt_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'TTid',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_allocate',
'COLUMN', N'tt_id'
GO


-- ----------------------------
-- Auto increment value for dbk_warrant_allocate
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_warrant_allocate]', RESEED, 204)
GO


-- ----------------------------
-- Primary Key structure for table dbk_warrant_allocate
-- ----------------------------
ALTER TABLE [dbo].[dbk_warrant_allocate] ADD CONSTRAINT [PK__dbk_warr__3213E83FFD890DC3] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

