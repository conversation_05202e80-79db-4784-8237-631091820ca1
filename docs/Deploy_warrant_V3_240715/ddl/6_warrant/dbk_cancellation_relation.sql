/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 03/09/2024 10:00:57
*/


-- ----------------------------
-- Table structure for dbk_cancellation_relation
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_cancellation_relation]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_cancellation_relation]
GO

CREATE TABLE [dbo].[dbk_cancellation_relation] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [warrant_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [sales_type] int  NULL,
  [contract_nature] int  NULL,
  [warrant_cancellation_id] int  NULL,
  [refer_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [refer_contract_id] int  NULL,
  [status] int  NULL
)
GO

ALTER TABLE [dbo].[dbk_cancellation_relation] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单编号',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'warrant_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'采销类型',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'sales_type'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同性质',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'contract_nature'
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单注销记录表id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'warrant_cancellation_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联业务数据编号',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'refer_contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联业务数据Id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'refer_contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'状态',
'SCHEMA', N'dbo',
'TABLE', N'dbk_cancellation_relation',
'COLUMN', N'status'
GO


-- ----------------------------
-- Auto increment value for dbk_cancellation_relation
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_cancellation_relation]', RESEED, 65)
GO


-- ----------------------------
-- Primary Key structure for table dbk_cancellation_relation
-- ----------------------------
ALTER TABLE [dbo].[dbk_cancellation_relation] ADD CONSTRAINT [PK__dbk_canc__3213E83FFDDB7975] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

