/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 03/09/2024 10:01:12
*/


-- ----------------------------
-- Table structure for dbk_warrant
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_warrant]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_warrant]
GO

CREATE TABLE [dbo].[dbk_warrant] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [tp_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category] int DEFAULT ((0)) NULL,
  [trade_type] int DEFAULT ((0)) NULL,
  [source] int DEFAULT ((0)) NULL,
  [exchange_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [exchange_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [category_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [material] int DEFAULT ((0)) NULL,
  [price_mode] int DEFAULT ((0)) NULL,
  [registe_date] datetime DEFAULT ((0)) NULL,
  [registe_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [registe_count] decimal(18,6) DEFAULT ((0)) NULL,
  [registe_unit_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [registe_unit_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [goods_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [goods_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [registe_price] decimal(18,6) DEFAULT NULL NULL,
  [registe_amount] decimal(18,6) DEFAULT ((0)) NULL,
  [warehouse_type] int DEFAULT ((0)) NULL,
  [warehouse_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [warehouse_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [domain_category_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [domain_code_full] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [deposit_rate] int DEFAULT ((0)) NULL,
  [deposit_amount] decimal(18,6) DEFAULT ((0)) NULL,
  [delivery_type] int DEFAULT ((0)) NULL,
  [min_daily_delivery_count] decimal(18,6) DEFAULT ((0)) NULL,
  [max_daily_delivery_count] decimal(18,6) DEFAULT ((0)) NULL,
  [delivery_unit_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_unit_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_memo] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [altered_count] decimal(18,6) DEFAULT ((0)) NULL,
  [cancelled_count] decimal(18,6) DEFAULT ((0)) NULL,
  [status] int DEFAULT ((1)) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [delivery_factory_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [delivery_factory_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [registe_id] int  NULL,
  [company_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [deposit_payment_type] int  NULL,
  [registe_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [hold_hand_count] decimal(18,6) DEFAULT ((0)) NULL,
  [hold_count] decimal(18,6) DEFAULT ((0)) NULL,
  [registe_hand_count] decimal(18,6) DEFAULT ((0)) NULL,
  [company_id] int  NULL,
  [contract_cancelled_count] decimal(18,6) DEFAULT ((0)) NULL,
  [property] int  NULL,
  [area] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [site_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [site_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [goods_category_id] int  NULL,
  [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [is_soybean2] int  NULL
)
GO

ALTER TABLE [dbo].[dbk_warrant] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'仓单属性',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant',
'COLUMN', N'property'
GO

EXEC sp_addextendedproperty
'MS_Description', N'区域',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant',
'COLUMN', N'area'
GO

EXEC sp_addextendedproperty
'MS_Description', N'账套id',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant',
'COLUMN', N'site_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'账套名称',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant',
'COLUMN', N'site_name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否为豆二（0否;1是）',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant',
'COLUMN', N'is_soybean2'
GO


-- ----------------------------
-- Auto increment value for dbk_warrant
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_warrant]', RESEED, 65)
GO


-- ----------------------------
-- Indexes structure for table dbk_warrant
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [WarrantCode]
ON [dbo].[dbk_warrant] (
  [code] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dbk_warrant
-- ----------------------------
ALTER TABLE [dbo].[dbk_warrant] ADD CONSTRAINT [PK__dbk_warr__3213E83FEB619E47] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

