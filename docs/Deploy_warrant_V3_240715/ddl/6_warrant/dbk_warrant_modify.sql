/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 03/09/2024 10:01:40
*/


-- ----------------------------
-- Table structure for dbk_warrant_modify
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_warrant_modify]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_warrant_modify]
GO

CREATE TABLE [dbo].[dbk_warrant_modify] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [warrant_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [modify_type] int DEFAULT ((0)) NULL,
  [modify_count] decimal(18,6) DEFAULT ((0)) NULL,
  [refer_tt_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [refer_contract_type] int DEFAULT '' NULL,
  [refer_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [status] int DEFAULT ((0)) NULL
)
GO

ALTER TABLE [dbo].[dbk_warrant_modify] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for dbk_warrant_modify
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_warrant_modify]', RESEED, 273)
GO


-- ----------------------------
-- Primary Key structure for table dbk_warrant_modify
-- ----------------------------
ALTER TABLE [dbo].[dbk_warrant_modify] ADD CONSTRAINT [PK__dbk_warr__3213E83FDA3296F8] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

