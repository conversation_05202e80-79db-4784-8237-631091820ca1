/*
 Navicat Premium Data Transfer

 Source Server         : 新LDC
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 03/09/2024 10:01:25
*/


-- ----------------------------
-- Table structure for dbk_warrant_cancellation
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbk_warrant_cancellation]') AND type IN ('U'))
	DROP TABLE [dbo].[dbk_warrant_cancellation]
GO

CREATE TABLE [dbo].[dbk_warrant_cancellation] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [memo] text COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [receipt_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [source_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [refer_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [refer_contract_category] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [cancel_type] int DEFAULT ((0)) NULL,
  [cancel_count] decimal(18,6) DEFAULT ((0)) NULL,
  [delivery_password] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [customer_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [customer_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [goods_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [goods_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_customer_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_customer_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [future_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [delivery_goods_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [change_customer] int DEFAULT ((0)) NULL,
  [change_goods] int DEFAULT ((0)) NULL,
  [status] int DEFAULT ((0)) NULL,
  [is_modify_all] int  NULL,
  [delivery_contract_id] int  NULL,
  [delivery_start_time] datetime  NULL,
  [delivery_end_time] datetime  NULL,
  [delivery_type] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [source_contract_id] int  NULL,
  [purchase_contract_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [purchase_contract_id] int  NULL,
  [delivery_customer_id] int  NULL,
  [customer_id] int  NULL,
  [write_off_date] datetime  NULL,
  [write_off_action] int  NULL,
  [domain_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[dbk_warrant_cancellation] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同 4. 豆二注销不修改提货方 5. 豆二注销修改提货方',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_cancellation',
'COLUMN', N'write_off_action'
GO

EXEC sp_addextendedproperty
'MS_Description', N'注销合约',
'SCHEMA', N'dbo',
'TABLE', N'dbk_warrant_cancellation',
'COLUMN', N'domain_code'
GO


-- ----------------------------
-- Auto increment value for dbk_warrant_cancellation
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbk_warrant_cancellation]', RESEED, 87)
GO


-- ----------------------------
-- Primary Key structure for table dbk_warrant_cancellation
-- ----------------------------
ALTER TABLE [dbo].[dbk_warrant_cancellation] ADD CONSTRAINT [PK__dbk_warr__3213E83FC14025E5] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

