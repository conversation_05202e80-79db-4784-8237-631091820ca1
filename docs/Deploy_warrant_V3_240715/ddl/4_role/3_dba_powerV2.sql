CREATE TABLE [dbo].[dba_powerV2] (
  [id] int  NOT NULL,
  [pre_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [describe] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [parent_id] int  NULL,
  [level] int  NULL,
  [system] int  NULL,
  [is_deleted] tinyint DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [sort] int  NULL,
  [original_id] int  NULL,
  [is_category] int  NULL
)
GO

ALTER TABLE [dbo].[dba_powerV2] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'权限类编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'pre_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'编码',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'名称',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'描述',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'describe'
GO

EXEC sp_addextendedproperty
'MS_Description', N'上级ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'parent_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'级别',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'level'
GO

EXEC sp_addextendedproperty
'MS_Description', N'系统标志',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'system'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标志',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'排序',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'sort'
GO

EXEC sp_addextendedproperty
'MS_Description', N'关联原ID(刷历史数据关联处理用)',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'original_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否区分品类',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2',
'COLUMN', N'is_category'
GO

EXEC sp_addextendedproperty
'MS_Description', N'权限',
'SCHEMA', N'dbo',
'TABLE', N'dba_powerV2'
GO


-- ----------------------------
-- Primary Key structure for table dba_powerV2
-- ----------------------------
ALTER TABLE [dbo].[dba_powerV2] ADD CONSTRAINT [PK__dba_powe__3213E83FACCCD893_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

