CREATE TABLE [dbo].[dba_role_menuV2] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [menu_id] int  NULL,
  [role_id] int  NULL,
  [created_at] datetime  NULL,
  [updated_at] datetime  NULL,
  [is_deleted] tinyint DEFAULT ((0)) NULL
)
GO

ALTER TABLE [dbo].[dba_role_menuV2] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'主键',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'菜单ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'menu_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'角色ID',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'role_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'删除标志',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2',
'COLUMN', N'is_deleted'
GO

EXEC sp_addextendedproperty
'MS_Description', N'角色菜单关系',
'SCHEMA', N'dbo',
'TABLE', N'dba_role_menuV2'
GO


-- ----------------------------
-- Primary Key structure for table dba_role_menuV2
-- ----------------------------
ALTER TABLE [dbo].[dba_role_menuV2] ADD CONSTRAINT [PK__dba_role__3213E83FE3FD2412_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

