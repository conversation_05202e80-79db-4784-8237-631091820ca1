/*
 Navicat Premium Data Transfer

 Source Server         : Ldc_test
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 14/10/2024 15:28:45
*/


-- ----------------------------
-- Table structure for dba_site
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_site]') AND type IN ('U'))
DROP TABLE [dbo].[dba_site]
    GO

CREATE TABLE [dbo].[dba_site] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [lkg_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [atlas_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [company_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [company_id] int  NULL,
    [factory_id] int  NULL,
    [factory_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [status] int  NULL,
    [sync_system] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [belong_customer_id] int  NULL,
    [created_at] datetime  NULL,
    [updated_at] datetime  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL
    )
    GO

ALTER TABLE [dbo].[dba_site] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'LKG帐套编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'lkg_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'ATLAS帐套编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'atlas_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'company_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主体ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交货工厂ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'factory_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交货工厂编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'factory_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类（单选）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类（多选）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类（多选）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态(0禁用 1启用)',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'同步系统（LKG；ATLAS）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'sync_system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'LDC的客户ID（兼容销售的supplierID，采购的customerId）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'belong_customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'updated_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_site',
    'COLUMN', N'updated_by'
    GO


    -- ----------------------------
-- Auto increment value for dba_site
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_site]', RESEED, 13)
    GO


-- ----------------------------
-- Primary Key structure for table dba_site
-- ----------------------------
ALTER TABLE [dbo].[dba_site] ADD CONSTRAINT [PK__dba_site__3213E83F802877DB] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

