ALTER TABLE [dbo].[dba_customer] ADD [is_authorization] int NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否有授权书',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer',
    'COLUMN', N'is_authorization'

--客户模板配置
ALTER TABLE [dbo].[dba_customer_protocol] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_protocol] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_protocol] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_protocol',
    'COLUMN', N'category3'
    GO

---客户赊销预付
ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_credit_payment] ADD [bu_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_credit_payment',
    'COLUMN', N'bu_code'
    GO

---客户履约保证金比例
ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_deposit_rate] ADD [bu_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'业务线',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_deposit_rate',
    'COLUMN', N'bu_code'
    GO

---客户正本配置
ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_original_paper] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_original_paper',
    'COLUMN', N'category3'
    GO

---客户配置
ALTER TABLE [dbo].[dba_customer_detail] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'category3'
    GO

ALTER TABLE [dbo].[dba_customer_detail] ADD [is_deleted] int DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0 未删除 1删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_detail',
    'COLUMN', N'is_deleted'

    GO

----客户通知人
ALTER TABLE [dbo].[dba_contact] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_contact] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_contact] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact',
    'COLUMN', N'category3'
    GO

---通知人工厂编码
ALTER TABLE [dbo].[dba_contact_factory] ADD [factory_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'工厂编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_contact_factory',
    'COLUMN', N'factory_code'
    GO

--客户账户板置
ALTER TABLE [dbo].[dba_customer_bank] ADD [category1] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category1'
    GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [category2] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category2'
    GO

ALTER TABLE [dbo].[dba_customer_bank] ADD [category3] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_bank',
    'COLUMN', N'category3'
    GO

----客户发票
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_invoice]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_invoice]
    GO

CREATE TABLE [dbo].[dba_customer_invoice] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [customer_id] int  NULL,
    [invoice_id] int  NULL,
    [invoice_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
    [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
    [company_id] int  NULL,
    [company_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int DEFAULT ((0)) NULL,
    [status] int DEFAULT ((1)) NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_at] datetime  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_invoice] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'客户id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'invoice_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'发票描述',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'invoice_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'一级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category1'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'二级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category2'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'三级品类',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'category3'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'所属主体id',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'company_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'所属主体简称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'company_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_customer_invoice',
    'COLUMN', N'updated_at'
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_invoice
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_invoice]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_invoice
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_invoice] ADD CONSTRAINT [PK__dba_cust__3213E83F6805FCDD] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO


----客户评级
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_customer_grade_score]') AND type IN ('U'))
DROP TABLE [dbo].[dba_customer_grade_score]
    GO

CREATE TABLE [dbo].[dba_customer_grade_score] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [customer_id] int  NULL,
    [grade_score] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category1] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category2] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category3] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] int  NULL,
    [status] int  NULL,
    [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime  NULL,
    [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_at] datetime  NULL
    )
    GO

ALTER TABLE [dbo].[dba_customer_grade_score] SET (LOCK_ESCALATION = TABLE)
    GO


    -- ----------------------------
-- Auto increment value for dba_customer_grade_score
-- ----------------------------
    DBCC CHECKIDENT ('[dbo].[dba_customer_grade_score]', RESEED, 0)
    GO


-- ----------------------------
-- Primary Key structure for table dba_customer_grade_score
-- ----------------------------
ALTER TABLE [dbo].[dba_customer_grade_score] ADD CONSTRAINT [PK__dba_cust__3213E83F1A821287] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    GO

