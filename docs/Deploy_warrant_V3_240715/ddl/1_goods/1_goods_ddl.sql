-- dbg_goods

ALTER TABLE [dbo].[dbg_goods] ADD [is_tt_attribute] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否TT默认规格(新增字段)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'is_tt_attribute'

-- dbg_category

ALTER TABLE [dbo].[dbg_category] ADD [is_dce] int NULL
GO

ALTER TABLE [dbo].[dbg_category] ADD [is_soybean2] int NULL
GO

ALTER TABLE [dbo].[dbg_category] ADD [future_code] varchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否交割',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'is_dce'
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否豆二',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'is_soybean2'
GO

EXEC sp_addextendedproperty
'MS_Description', N'期货代码',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'future_code'

ALTER TABLE [dbo].[dbg_category] ADD [created_by] varchar(255) NULL
GO

ALTER TABLE [dbo].[dbg_category] ADD [updated_by] varchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'updated_by'

ALTER TABLE [dbo].[dbg_category] ADD [nav_id] varchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'NAV品种ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'nav_id'

ALTER TABLE [dbo].[dbg_category] ADD [is_split_contract] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否拆分合同',
'SCHEMA', N'dbo',
'TABLE', N'dbg_category',
'COLUMN', N'is_split_contract'


-- dbg_attribute

ALTER TABLE [dbo].[dbg_attribute] ADD [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
GO

ALTER TABLE [dbo].[dbg_attribute] ADD [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_attribute',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新人',
'SCHEMA', N'dbo',
'TABLE', N'dbg_attribute',
'COLUMN', N'updated_by'

ALTER TABLE [dbo].[dbg_goods] ADD [nav_sku_id] varchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'NAV SKU ID',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'nav_sku_id'

ALTER TABLE [dbo].[dbg_goods] ADD [nick_name] varchar(255) NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'商品昵称',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'nick_name'

ALTER TABLE [dbo].[dbg_goods] ADD [is_global_oil] int NULL
GO

EXEC sp_addextendedproperty
'MS_Description', N'是否Global Oil(0否 1是)',
'SCHEMA', N'dbo',
'TABLE', N'dbg_goods',
'COLUMN', N'is_global_oil'