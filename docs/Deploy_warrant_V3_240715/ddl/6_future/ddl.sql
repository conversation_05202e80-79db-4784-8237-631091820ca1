ALTER TABLE [dbo].[dbf_price_allocate] ADD [bu_code] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [bu_code] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbf_price_apply] ADD [bu_code] nvarchar(255) NULL
GO

ALTER TABLE [dbo].[dbf_position] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbf_position] ADD [transfer_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'转入合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'transfer_future_code'
    GO

ALTER TABLE [dbo].[dbf_price_apply] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_apply',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbf_price_apply] ADD [tranfer_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'转入合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_apply',
    'COLUMN', N'tranfer_future_code'
    GO

ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_deal_detail',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbf_price_deal_detail] ADD [tranfer_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'转入合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_deal_detail',
    'COLUMN', N'tranfer_future_code'
    GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [raw_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'转入合约',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'raw_future_code'
    GO

EXEC sp_rename '[dbo].[dbf_price_apply].[category_name]', 'category3_name', 'COLUMN'
GO

EXEC sp_rename '[dbo].[dbf_price_deal_detail].[category_name]', 'category3_name', 'COLUMN'
GO

EXEC sp_rename '[dbo].[dbf_price_allocate].[category_name]', 'category3_name', 'COLUMN'
GO

ALTER TABLE [dbo].[dbf_position] ADD [commodity_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'commodity_name'

ALTER TABLE [dbo].[dbf_position] ADD [transaction_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'成交合约编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'transaction_future_code'
    GO

ALTER TABLE [dbo].[dbf_position] ADD [transaction_transfer_domain_future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'成交转入合约编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_position',
    'COLUMN', N'transaction_transfer_domain_future_code'
    GO


ALTER TABLE [dbo].[dbf_price_allocate] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbf_price_allocate] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbf_price_allocate',
    'COLUMN', N'site_name'
    GO

