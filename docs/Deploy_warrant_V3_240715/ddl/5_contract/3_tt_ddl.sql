ALTER TABLE [dbo].[dbt_trade_ticket] ADD [site_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'site_code'
    GO

    ALTER TABLE [dbo].[dbt_trade_ticket] ADD [site_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'site_name'
    GO
ALTER TABLE [dbo].[dbt_trade_ticket] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_trade_ticket] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'commodity_name'
    GO

    ALTER TABLE [dbo].[dbt_trade_ticket] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_trade_ticket',
    'COLUMN', N'is_soybean2'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_trade_type'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [settle_type] varchar(255)  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'结算方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'settle_type'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [write_off_start_time]  datetime NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销周期开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'write_off_start_time'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [write_off_end_time]  datetime  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销周期结束时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'write_off_end_time'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_id'
    GO


    ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单Code',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_code'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'期货代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'future_code'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_type] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_type'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_file_id] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_file_id'
    GO

    ALTER TABLE [dbo].[dbt_tt_add] ADD [standard_remark] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'standard_remark'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [survey_fees] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'检验费',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N'survey_fees'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [refine_frac_diff_price] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'精炼/分提价差',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N'refine_frac_diff_price'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [deposit_payment_type] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'交割保证金付款方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'deposit_payment_type'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [delivery_margin_amount] decimal(15,6) DEFAULT ((0)) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'交割保证金金额',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'delivery_margin_amount'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'category'
    GO

ALTER TABLE [dbo].[dbt_tt_add] ADD [warrant_category] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'仓单类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_add',
    'COLUMN', N'warrant_category'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [ve_price]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'VE单价',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N've_price'
    GO

ALTER TABLE [dbo].[dbt_contract_price] ADD [ve_content]  decimal(25,6) DEFAULT ((0)) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'VE含量',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_price',
    'COLUMN', N've_content'
    GO




