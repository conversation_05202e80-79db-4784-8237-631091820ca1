
-- dbt_contract
ALTER TABLE [dbo].[dbt_contract] ADD [warrant_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单标识ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_status] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销状态(1.未注销 2.注销中 3.已注销)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_status'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_start_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_start_time'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [write_off_end_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销截止时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'write_off_end_time'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [settle_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'settle_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [category_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'category_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [exchange_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'exchange_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_file_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'site_name'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [delivery_mode] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'delivery_mode'
    GO


ALTER TABLE [dbo].[dbt_contract] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'commodity_name'
    GO

ALTER TABLE [dbo].[dbt_contract] ADD [standard_remark] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract',
    'COLUMN', N'standard_remark'
    GO



-- dbt_contract_history
ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单标识ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_status] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销状态(1.未注销 2.注销中 3.已注销)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_status'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_start_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_start_time'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [write_off_end_time] datetime NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'注销截止时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'write_off_end_time'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [settle_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'结算类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'settle_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [category_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'category_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [exchange_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'exchange_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [future_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'期货合约代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_type] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标|国标类型',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_type'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_file_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'企标文件ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_file_id'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'site_name'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [delivery_mode] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'提货方式',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'delivery_mode'
    GO


ALTER TABLE [dbo].[dbt_contract_history] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'commodity_name'
    GO

ALTER TABLE [dbo].[dbt_contract_history] ADD [standard_remark] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_history',
    'COLUMN', N'standard_remark'
    GO
