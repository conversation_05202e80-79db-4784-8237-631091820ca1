
    ALTER TABLE [dbo].[dbt_tt_modify] ADD [is_modify_all] int DEFAULT 0 NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'是否同时修改货品和提货方',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'is_modify_all'
    GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_id] int NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'提货主体',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'delivery_id'
    GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [delivery_password] varchar(255) NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'提货密码',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'delivery_password'
        GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_date]  datetime NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'注销日期',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'write_off_date'
        GO


    ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_delivery_start_time]  datetime NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销交提货开始时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_delivery_start_time'
    GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_delivery_end_time]  datetime  NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'注销交提货结束时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'write_off_delivery_end_time'
    GO


    ALTER TABLE [dbo].[dbt_tt_modify] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'期货编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_tt_modify',
    'COLUMN', N'future_code'
    GO


    ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_trade_type] int NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'仓单交易类型',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'warrant_trade_type'
        GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [write_off_num] decimal(15,6) DEFAULT ((0)) NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'注销数量',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'write_off_num'
        GO



    ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_id] int NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'仓单ID',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'warrant_id'
        GO


    ALTER TABLE [dbo].[dbt_tt_modify] ADD [warrant_code] varchar(255) NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'仓单Code',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'warrant_code'
        GO

    ALTER TABLE [dbo].[dbt_tt_modify] ADD [category] int NULL
        GO
        EXEC sp_addextendedproperty
        'MS_Description', N'仓单类型',
        'SCHEMA', N'dbo',
        'TABLE', N'dbt_tt_modify',
        'COLUMN', N'category'
        GO




