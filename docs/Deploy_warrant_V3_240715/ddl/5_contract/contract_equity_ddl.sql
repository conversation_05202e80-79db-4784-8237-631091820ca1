ALTER VIEW [dbo].[v_contract_equity] AS SELECT
    ROW_NUMBER ( ) OVER ( ORDER BY contract.id ) AS id,
        contract.id AS contract_id,
    contract.delivery_factory_code,
    contract.supplier_id,
    contract.supplier_name,
    contract.contract_code,
    contract.customer_id,
    contract.customer_name,
    customer.enterprise_name AS enterprise_name,
    contract.goods_category_id,
    contract.contract_type,
    contract.delivery_start_time,
    contract.delivery_end_time,
    contract.able_transfer_times,
    contract.transferred_times,
    contract.able_reverse_price_times,
    contract.reversed_price_times,
    contract.close_tail_num,
    contract.bu_code,
    equity.apply_code AS last_apply_code,
    equity.approve_status AS last_approve_status,
    equity.updated_by AS last_updated_by,
    equity.updated_at AS last_updated_at
FROM
    dbt_contract contract
        LEFT JOIN dbo.dba_customer customer ON contract.customer_id = customer.id
        LEFT JOIN dbt_contract_change_equity equity ON contract.id = equity.contract_id
WHERE
        contract.status = 2
  AND contract.contract_type IN ( 1, 2 )
  AND contract.total_buy_back_num = 0
  AND contract.contract_num > 0
  AND contract.is_deleted = 0
  AND contract.sales_type = 2
  AND ( SELECT COUNT ( * ) FROM dbt_contract_change_equity AS equity2 WHERE equity.contract_id = equity2.contract_id AND equity2.id >= equity.id ) <= 1

  -- 刷新视图(执行完修改脚本后执行)
sp_refreshview 'v_contract_equity';
