
-- dbt_contract_sign
ALTER TABLE [dbo].[dbt_contract_sign] ADD [future_code] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'品种代码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'future_code'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [is_soybean2] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否为豆二注销生成（0否;1是）',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'is_soybean2'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [warrant_trade_type] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单交易类型(1.交易所交割仓单 2.线下交易所仓单)',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'warrant_trade_type'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [exchange_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'交易所编号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'exchange_code'
    GO


ALTER TABLE [dbo].[dbt_contract_sign] ADD [site_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'site_code'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [site_name] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'账套名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'site_name'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [warrant_id] int DEFAULT 0 NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'warrant_id'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [warrant_code] nvarchar(255) NULL
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'仓单注册号',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'warrant_code'
    GO
ALTER TABLE [dbo].[dbt_contract_sign] ADD [commodity_name] varchar(255) NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'货品名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'commodity_name'
    GO

ALTER TABLE [dbo].[dbt_contract_sign] ADD [standard_remark] text NULL
    GO
    EXEC sp_addextendedproperty
    'MS_Description', N'指标备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dbt_contract_sign',
    'COLUMN', N'standard_remark'
    GO


