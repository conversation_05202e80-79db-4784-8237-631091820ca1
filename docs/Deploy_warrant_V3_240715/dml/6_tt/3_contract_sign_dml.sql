--更新协议主表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract_sign tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新协议表 品种代码信息
update dbt_contract_sign set future_code = 'M' where category2 = 11 and future_code is null;
update dbt_contract_sign set future_code = 'Y' where category2 = 12 and future_code is null;
--更新协议表 货品信息
update dbt_contract_sign set commodity_name = goods_name WHERE commodity_name is null;