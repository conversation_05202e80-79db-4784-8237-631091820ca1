--更新TT主表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_trade_ticket tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新TT主表 品种代码信息
update dbt_trade_ticket set future_code = 'M' where category2 = 11;
update dbt_trade_ticket set future_code = 'Y' where category2 = 12;
--更新TT主表 货品信息
update dbt_trade_ticket set commodity_name = goods_name WHERE commodity_name is null;
