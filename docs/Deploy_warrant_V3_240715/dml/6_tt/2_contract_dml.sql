--更新合同表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新合同表 品种代码信息
update dbt_contract set future_code = 'M' where category2 = 11;
update dbt_contract set future_code = 'Y' where category2 = 12;
--更新合同表 货品信息
update dbt_contract set commodity_name = goods_name WHERE commodity_name is null;

--更新合同表 账套信息
UPDATE tt set tt.site_code = s.code,tt.site_name = s.name
    FROM dbt_contract_history tt
JOIN dba_site s ON tt.belong_customer_id = s.belong_customer_id;
--更新合同表 品种代码信息
update dbt_contract_history set future_code = 'M' where category2 = 11;
update dbt_contract_history set future_code = 'Y' where category2 = 12;
--更新合同表 货品信息
update dbt_contract_history set commodity_name = goods_name WHERE commodity_name is null;



-- 日志脚本
INSERT INTO [dbo].[dbz_operation_config]([biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES (N'newWarrantPurchaseContract', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '新增仓单采购合同');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'newWarrantSalesContract', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '新增仓单销售合同');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'warrantContractWriteOff', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '仓单合同注销');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'warrantContractInvalid', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '仓单合同作废');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'warrantContractBuyBack', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '仓单合同回购');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'salesContractBuyBack', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '销售合同回购');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'newWarrantContract', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '新建仓单合同');
INSERT INTO [dbo].[dbz_operation_config]( [biz_code], [template_code], [created_at], [updated_at], [memo]) VALUES ( N'writeOffWithDraw', N'M_OPERATION_LOG', '2022-12-09 16:02:34.000', '2022-12-09 16:02:34.000', '仓单合同注销撤回');