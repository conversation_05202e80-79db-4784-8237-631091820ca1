EXEC sp_rename 'dba_c_power', 'dba_c_power_old';

CREATE TABLE [dbo].[dba_c_power] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [pre_code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [code] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [describe] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [parent_id] int  NULL,
  [level] int  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL,
  [is_deleted] int DEFAULT ((0)) NULL
)
GO

ALTER TABLE [dbo].[dba_c_power] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of dba_c_power
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dba_c_power] ON
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'1', NULL, NULL, N'系统管理', NULL, N'0', N'0', N'2022-09-22 06:54:53.860', N'2022-09-22 06:54:53.860', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'2', NULL, NULL, N'豆粕销售', NULL, N'0', N'0', N'2022-09-22 06:56:08.583', N'2022-09-22 06:56:08.583', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'3', NULL, NULL, N'豆粕采购', NULL, N'0', N'0', N'2022-09-22 06:56:25.380', N'2022-09-22 06:56:25.380', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'4', NULL, NULL, N'豆油销售', NULL, N'0', N'0', N'2022-09-22 06:56:39.090', N'2022-09-22 06:56:39.090', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'5', NULL, NULL, N'豆油采购', NULL, N'0', N'0', N'2022-09-22 06:56:52.790', N'2022-09-22 06:56:52.790', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'6', NULL, NULL, N'角色管理列表', NULL, N'1', N'1', N'2022-09-22 06:57:24.897', N'2022-09-22 06:57:24.897', N'1')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'7', NULL, NULL, N'账户管理列表', NULL, N'1', N'1', N'2022-09-22 06:57:59.133', N'2022-09-22 06:57:59.133', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'8', NULL, NULL, N'待签署合同管理', NULL, N'2', N'1', N'2022-09-22 06:58:16.657', N'2022-09-22 06:58:16.657', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'9', NULL, NULL, N'待签署合同管理', NULL, N'3', N'1', N'2022-10-13 05:48:44.427', N'2022-10-13 05:48:44.427', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'10', NULL, NULL, N'合同管理', NULL, N'3', N'1', N'2022-10-13 05:49:20.570', N'2022-10-13 05:49:20.570', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'11', NULL, NULL, N'待签署合同管理', NULL, N'4', N'1', N'2022-10-13 05:49:43.150', N'2022-10-13 05:49:43.150', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'12', NULL, NULL, N'待签署合同管理', NULL, N'5', N'1', N'2022-10-13 05:49:44.890', N'2022-10-13 05:49:44.890', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'13', NULL, NULL, N'合同管理', NULL, N'5', N'1', N'2022-10-13 05:50:58.103', N'2022-10-13 05:50:58.103', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'14', N'CTY_001', N'CTY_001', N'菜单权限', NULL, N'6', N'2', N'2022-09-22 07:00:18.017', N'2022-09-22 07:00:18.017', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'15', N'CTY_002', N'CTY_002', N'操作权限', NULL, N'6', N'2', N'2022-09-22 07:00:19.360', N'2022-09-22 07:00:19.360', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'16', N'CTY_003', N'CTY_003', N'新建', NULL, N'7', N'2', N'2022-09-22 07:00:20.780', N'2022-09-22 07:00:20.780', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'17', N'CTY_004', N'CTY_004', N'编辑', NULL, N'7', N'2', N'2022-09-22 07:00:22.060', N'2022-09-22 07:00:22.060', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'18', N'CTY_005', N'CTY_005', N'启用/禁用', NULL, N'7', N'2', N'2022-09-22 07:00:24.853', N'2022-09-22 07:00:24.853', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'19', N'CSBM_S_001', N'CSBM_S_001', N'签章', NULL, N'8', N'2', N'2022-09-22 07:01:26.230', N'2022-09-22 07:01:26.230', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'20', N'CSBM_S_002', N'CSBM_S_002', N'正本编辑', NULL, N'8', N'2', N'2022-09-22 07:01:28.910', N'2022-09-22 07:01:28.910', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'21', N'CSBM_P_001', N'CSBM_P_001', N'签章', NULL, N'9', N'2', N'2022-10-13 05:54:04.327', N'2022-10-13 05:54:04.327', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'22', N'CSBM_P_007', N'CSBM_P_007', N'正本编辑', NULL, N'9', N'2', N'2022-10-13 05:54:05.420', N'2022-10-13 05:54:05.420', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'23', N'CSBM_P_002', N'CSBM_P_002', N'点价', NULL, N'10', N'2', N'2022-10-13 05:54:06.580', N'2022-10-13 05:54:06.580', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'24', N'CSBM_P_003', N'CSBM_P_003', N'转月', NULL, N'10', N'2', N'2022-10-13 05:54:08.217', N'2022-10-13 05:54:08.217', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'25', N'CSBM_P_004', N'CSBM_P_004', N'改单', NULL, N'10', N'2', N'2022-10-13 05:54:09.290', N'2022-10-13 05:54:09.290', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'26', N'CSBM_P_005', N'CSBM_P_005', N'撤单', NULL, N'10', N'2', N'2022-10-13 05:54:12.330', N'2022-10-13 05:54:12.330', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'27', N'CSBM_P_006', N'CSBM_P_006', N'分配合同', NULL, N'10', N'2', N'2022-10-13 05:54:14.973', N'2022-10-13 05:54:14.973', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'28', N'CSBO_S_001', N'CSBO_S_001', N'签章', NULL, N'11', N'2', N'2022-10-13 05:55:36.553', N'2022-10-13 05:55:36.553', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'29', N'CSBO_S_002', N'CSBO_S_002', N'正本编辑', NULL, N'11', N'2', N'2022-10-13 05:55:37.913', N'2022-10-13 05:55:37.913', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'30', N'CSBO_P_001', N'CSBO_P_001', N'签章', NULL, N'12', N'2', N'2022-10-13 05:56:17.900', N'2022-10-13 05:56:17.900', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'31', N'CSBO_P_007', N'CSBO_P_007', N'正本编辑', NULL, N'12', N'2', N'2022-10-13 05:56:19.140', N'2022-10-13 05:56:19.140', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'32', N'CSBO_P_002', N'CSBO_P_002', N'点价', NULL, N'13', N'2', N'2022-10-13 05:56:20.483', N'2022-10-13 05:56:20.483', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'33', N'CSBO_P_003', N'CSBO_P_003', N'转月', NULL, N'13', N'2', N'2022-10-13 05:56:21.630', N'2022-10-13 05:56:21.630', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'34', N'CSBO_P_004', N'CSBO_P_004', N'改单', NULL, N'13', N'2', N'2022-10-13 05:56:22.753', N'2022-10-13 05:56:22.753', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'35', N'CSBO_P_005', N'CSBO_P_005', N'撤单', NULL, N'13', N'2', N'2022-10-13 05:56:23.933', N'2022-10-13 05:56:23.933', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'36', N'CSBO_P_006', N'CSBO_P_006', N'分配合同', NULL, N'13', N'2', N'2022-10-13 05:56:25.353', N'2022-10-13 05:56:25.353', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'37', NULL, NULL, N'合同管理', NULL, N'2', N'1', N'2022-10-20 02:22:33.963', N'2022-10-20 02:22:33.963', N'1')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'38', N'CSBM_S_003', N'CSBM_S_003', N'反点价', NULL, N'37', N'2', N'2022-10-20 02:25:44.717', N'2022-10-20 02:25:44.717', N'1')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'39', N'CSBM_P_008', N'CSBM_P_008', N'反点价', NULL, N'10', N'2', N'2022-10-20 02:27:44.473', N'2022-10-20 02:27:44.473', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'40', NULL, NULL, N'合同管理', NULL, N'4', N'1', N'2022-10-20 02:31:17.670', N'2022-10-20 02:31:17.670', N'1')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'41', N'CSBO_S_003', N'CSBO_S_003', N'反点价', NULL, N'40', N'2', N'2022-10-20 02:32:01.550', N'2022-10-20 02:32:01.550', N'1')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'42', N'CSBO_P_008', N'CSBO_P_008', N'反点价', NULL, N'13', N'2', N'2022-10-20 02:32:22.257', N'2022-10-20 02:32:22.257', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'43', NULL, NULL, N'提货委托', NULL, N'2', N'1', N'2023-09-12 02:16:17.380', N'2023-09-12 02:16:17.380', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'44', NULL, NULL, N'提货委托', NULL, N'4', N'1', N'2023-09-12 02:18:07.820', N'2023-09-12 02:18:07.820', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'45', N'CSBM_S_009', N'CSBM_S_009', N'提货-新增申请', NULL, N'43', N'2', N'2023-09-12 02:18:47.173', N'2023-09-12 02:18:47.173', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'46', N'CSBM_S_010', N'CSBM_S_010', N'提货-下载申请', NULL, N'43', N'2', N'2023-09-12 02:18:48.297', N'2023-09-12 02:18:48.297', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'47', N'CSBM_S_011', N'CSBM_S_011', N'提货-批量提交', NULL, N'43', N'2', N'2023-09-12 02:18:49.737', N'2023-09-12 02:18:49.737', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'48', N'CSBM_S_012', N'CSBM_S_012', N'提货-复制', NULL, N'43', N'2', N'2023-09-12 02:18:51.120', N'2023-09-12 02:18:51.120', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'49', N'CSBM_S_013', N'CSBM_S_013', N'提货-编辑', NULL, N'43', N'2', N'2023-09-12 02:18:52.453', N'2023-09-12 02:18:52.453', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'50', N'CSBM_S_014', N'CSBM_S_014', N'提货-作废', NULL, N'43', N'2', N'2023-09-12 02:19:00.920', N'2023-09-12 02:19:00.920', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'51', N'CSBM_S_015', N'CSBM_S_015', N'提货-提交', NULL, N'43', N'2', N'2023-09-12 02:20:08.153', N'2023-09-12 02:20:08.153', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'52', N'CSBM_S_016', N'CSBM_S_016', N'提货-撤回', NULL, N'43', N'2', N'2023-09-12 02:20:25.513', N'2023-09-12 02:20:25.513', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'53', N'CSBM_S_017', N'CSBM_S_017', N'提货-申请提货', NULL, N'43', N'2', N'2023-09-12 02:20:27.800', N'2023-09-12 02:20:27.800', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'54', N'CSBO_S_009', N'CSBO_S_009', N'提货-新增申请', NULL, N'44', N'2', N'2023-09-12 02:23:17.253', N'2023-09-12 02:23:17.253', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'55', N'CSBO_S_010', N'CSBO_S_010', N'提货-下载申请', NULL, N'44', N'2', N'2023-09-12 02:23:18.823', N'2023-09-12 02:23:18.823', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'56', N'CSBO_S_011', N'CSBO_S_011', N'提货-批量提交', NULL, N'44', N'2', N'2023-09-12 02:23:20.237', N'2023-09-12 02:23:20.237', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'57', N'CSBO_S_012', N'CSBO_S_012', N'提货-复制', NULL, N'44', N'2', N'2023-09-12 02:23:21.510', N'2023-09-12 02:23:21.510', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'58', N'CSBO_S_013', N'CSBO_S_013', N'提货-编辑', NULL, N'44', N'2', N'2023-09-12 02:23:22.660', N'2023-09-12 02:23:22.660', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'59', N'CSBO_S_014', N'CSBO_S_014', N'提货-作废', NULL, N'44', N'2', N'2023-09-12 02:23:24.193', N'2023-09-12 02:23:24.193', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'60', N'CSBO_S_015', N'CSBO_S_015', N'提货-提交', NULL, N'44', N'2', N'2023-09-12 02:23:25.497', N'2023-09-12 02:23:25.497', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'61', N'CSBO_S_016', N'CSBO_S_016', N'提货-撤回', NULL, N'44', N'2', N'2023-09-12 02:23:26.763', N'2023-09-12 02:23:26.763', N'0')
GO

INSERT INTO [dbo].[dba_c_power] ([id], [pre_code], [code], [name], [describe], [parent_id], [level], [created_at], [updated_at], [is_deleted]) VALUES (N'62', N'CSBO_S_017', N'CSBO_S_017', N'提货-申请提货', NULL, N'44', N'2', N'2023-09-12 02:23:28.160', N'2023-09-12 02:23:28.160', N'0')
GO

SET IDENTITY_INSERT [dbo].[dba_c_power] OFF
GO


-- ----------------------------
-- Indexes structure for table dba_c_power
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [PK__dba_powe__3213E83FACCCD893]
ON [dbo].[dba_c_power] (
  [id] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table dba_c_power
-- ----------------------------
ALTER TABLE [dbo].[dba_c_power] ADD CONSTRAINT [PK__dba_c_po__3213E83F351A737D] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

