--客户银行账号
UPDATE dba_customer_bank
SET category1 = g1.serial_no,
    category2 = g2.serial_no
    from dba_customer_bank c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id;

--客户通知人
UPDATE dba_contact
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_contact c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;

--赊销预付
UPDATE dba_customer_credit_payment
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_customer_credit_payment c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;

---保证金
UPDATE dba_customer_deposit_rate
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_customer_deposit_rate c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;

--客户配置
UPDATE dba_customer_detail
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_customer_detail c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;
---
update dba_customer_detail set is_deleted = 0 where company_id = 1;
update dba_customer_detail set is_deleted = 1 where company_id <> 1;

----
UPDATE dba_customer_original_paper
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_customer_original_paper c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;

----
UPDATE dba_customer_protocol
SET category1 = g1.serial_no,
    category2 = g2.serial_no,
    category3 = g3.serial_no
    from dba_customer_protocol c
join dbg_category g2 ON c.category_id = g2.id
    join dbg_category g1 ON g2.parent_id = g1.id
    join dbg_category g3 ON g2.id = g3.parent_id
;

----迁移客户发票配置
INSERT INTO dba_customer_invoice (customer_id,invoice_id,invoice_name,category1,category2,category3,company_id,company_name,created_by,created_at,updated_by,updated_at)
SELECT customer_id,invoice_id,invoice_name,category1,category2,category3,company_id,company_name,created_by_name,created_at,updated_by_name,updated_at
from dba_customer_detail
where invoice_id <> 0;


UPDATE dba_customer_invoice
SET company_name = c.short_name
    from dba_customer_invoice i
join dba_company c ON i.company_id = c.id;


---迁移客户评级配置
INSERT INTO dba_customer_grade_score (customer_id,grade_score,category1,category2,category3,created_by,created_at,updated_by,updated_at)
SELECT customer_id,grade_score,category1,category2,category3,created_by_name,created_at,updated_by_name,updated_at
from dba_customer_detail
where grade_score is not null;

---客户赊销预付/履约保证金更新业务线
update dba_customer_deposit_rate set bu_code = 'ST';

update dba_customer_credit_payment set bu_code = 'ST';


---账户信息工厂code
update dba_bank_factory
set factory_code = f.code
    from dba_bank_factory b
INNER JOIN dba_factory f on b.factory_id = f.id

