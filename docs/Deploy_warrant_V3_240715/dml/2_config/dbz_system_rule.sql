-- 豆二注销比例
DECLARE @LastInsertedID INT;
DECLARE @DouErID INT;
DECLARE @DouPoID INT;
DECLARE @DouYouID INT;
-- 豆二ID
SET @DouErID=28;
-- 豆粕ID
SET @DouPoID=23;
-- 豆油ID
SET @DouYouID=24;
-- 豆二注销比例主表-S1005
INSERT INTO [dbo].[dbz_system_rule]([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( @DouErID, 0, 'S1005', '豆二注销比例', 0, 1, getdate(), getdate());
SET @LastInsertedID = (select id from dbz_system_rule where code = 'S1005' and category_id = @DouErID);
INSERT INTO [dbo].[dbz_system_rule_item]([rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id]) VALUES ( @LastInsertedID, 'S1005', 3, 0, 0, 1, getdate(), getdate(), '豆粕', 1, 1, @DouPoID, '0.785', NULL, N'admin1', N'admin1', N'0', NULL);
INSERT INTO [dbo].[dbz_system_rule_item]( [rule_id], [code], [value_type], [sort], [is_system], [status], [created_at], [updated_at], [memo], [created_by], [updated_by], [rule_key], [rule_value], [lkg_code], [created_by_name], [updated_by_name], [company_id], [goods_id]) VALUES ( @LastInsertedID, 'S1005', 3, 0, 0, 1, getdate(), getdate(), '豆油', 1, 1, @DouYouID, '0.185', NULL, N'admin1', N'admin1', N'0', NULL);

-- 发票类型
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( 21, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES ( 22, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (25, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (26, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (27, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());
-- INSERT INTO [dbo].[dbz_system_rule] ([category_id], [parent_id], [code], [name], [sort], [status], [created_at], [updated_at]) VALUES (28, 0, 'S0009', '发票类型', 0, 1, getdate(), getdate());