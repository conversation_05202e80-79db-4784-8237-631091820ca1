INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (1, N'', '2024-07-19 01:30:02.260', N'', '2024-07-19 01:30:02.260', N'M', N'DCE', N'1$3$5$7$8$9$11$12', 0);
INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (2, N'', '2024-07-19 01:30:08.737', N'', '2024-07-19 01:30:08.737', N'Y', N'DCE', N'1$3$5$7$8$9$11$12', 0);
INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (3, N'', '2024-07-19 01:30:14.460', N'', '2024-07-19 01:30:14.460', N'P', N'DCE', N'1$2$3$4$5$6$7$8$9$10$11$12', 0);
INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (4, N'', '2024-07-19 01:30:20.160', N'', '2024-07-19 01:30:20.160', N'RM', N'ZCE', N'1$3$5$7$8$9$11', 0);
INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (5, N'', '2024-07-19 01:30:26.487', N'', '2024-07-19 01:30:26.487', N'OI', N'ZCE', N'1$3$5$7$9$11', 0);
INSERT INTO [dbo].[dbf_trading_config] ([id], [created_by], [created_at], [updated_by], [updated_at], [future_code], [exchange], [domain_month], [is_deleted]) VALUES (6, N'', '2024-07-19 01:30:33.337', N'', '2024-07-19 01:30:33.337', N'B', N'DCE', N'1$2$3$4$5$6$7$8$9$10$11$12', 0);


--申请单
update dbf_price_apply set future_code = 'M' where category2 = 11;
update dbf_price_apply set future_code = 'Y' where category2 = 12;

update dbf_price_apply set tranfer_future_code = 'M' where category2 = 11 and type in(2,3);
update dbf_price_apply set tranfer_future_code = 'Y' where category2 = 12 and type in(2,3);

--成交单
update dbf_price_deal_detail set future_code = 'M' where category2 = 11;
update dbf_price_deal_detail set future_code = 'Y' where category2 = 12;

update dbf_price_deal_detail set tranfer_future_code = 'M' where category2 = 11 and type in(2,3);
update dbf_price_deal_detail set tranfer_future_code = 'Y' where category2 = 12 and type in(2,3);


--分配单
update dbf_price_allocate set future_code = 'M' where category2 = 11;
update dbf_price_allocate set future_code = 'Y' where category2 = 12;

update dbf_price_allocate set raw_future_code = 'M' where category2 = 11 and price_apply_type in(2,3);
update dbf_price_allocate set raw_future_code = 'Y' where category2 = 12 and price_apply_type in(2,3);

--刷分配单数据
update dbf_price_allocate set site_code = c.site_code,site_name = c.site_name from dbf_price_allocate f JOIN dbt_contract c ON f.contract_id = c.id;