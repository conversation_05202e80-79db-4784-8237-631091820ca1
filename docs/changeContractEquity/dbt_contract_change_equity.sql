/*
 Navicat Premium Data Transfer

 Source Server         : 达孚测试环境
 Source Server Type    : SQL Server
 Source Server Version : 14003456
 Source Host           : ************:1433
 Source Catalog        : ldc_navigator_test
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14003456
 File Encoding         : 65001

 Date: 20/10/2023 14:25:26
*/


-- ----------------------------
-- Table structure for dbt_contract_change_equity
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dbt_contract_change_equity]') AND type IN ('U'))
	DROP TABLE [dbo].[dbt_contract_change_equity]
GO

CREATE TABLE [dbo].[dbt_contract_change_equity] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [apply_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [approve_status] int DEFAULT ((0)) NULL,
  [contract_id] int DEFAULT ((0)) NULL,
  [contract_code] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [before_able_transfer_times] int DEFAULT ((0)) NULL,
  [after_able_transfer_times] int DEFAULT ((0)) NULL,
  [before_transferred_times] int DEFAULT ((0)) NULL,
  [after_transferred_times] int DEFAULT ((0)) NULL,
  [before_able_reverse_price_times] int DEFAULT ((0)) NULL,
  [after_able_reverse_price_times] int DEFAULT ((0)) NULL,
  [before_reversed_price_times] int DEFAULT ((0)) NULL,
  [after_reversed_price_times] int DEFAULT ((0)) NULL,
  [remark] ntext COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [created_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [updated_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT '' NULL,
  [apply_by] int DEFAULT ((0)) NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dbt_contract_change_equity] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'自增id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'apply_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'审批状态',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'approve_status'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同id',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'contract_id'
GO

EXEC sp_addextendedproperty
'MS_Description', N'合同编号',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'contract_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更前可转月次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'before_able_transfer_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更后可转月次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'after_able_transfer_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更前已转月次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'before_transferred_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更后已转月次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'after_transferred_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更前可反点价次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'before_able_reverse_price_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更后可反点价次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'after_able_reverse_price_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更前已反点价次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'before_reversed_price_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'变更后已反点价次数',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'after_reversed_price_times'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请说明',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请人',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'created_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'updated_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'申请发起人',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'apply_by'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'created_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity',
'COLUMN', N'updated_at'
GO

EXEC sp_addextendedproperty
'MS_Description', N'dbt_contract_change_equity',
'SCHEMA', N'dbo',
'TABLE', N'dbt_contract_change_equity'
GO


-- ----------------------------
-- Auto increment value for dbt_contract_change_equity
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dbt_contract_change_equity]', RESEED, 1)
GO

-- ----------------------------
-- Indexes structure for table dbt_contract_change_equity
-- ----------------------------
CREATE NONCLUSTERED INDEX [index_apply_code]
ON [dbo].[dbt_contract_change_equity] (
  [apply_code] ASC
)
GO

CREATE NONCLUSTERED INDEX [index_contract_code]
ON [dbo].[dbt_contract_change_equity] (
  [contract_code] ASC
)
GO

CREATE NONCLUSTERED INDEX [index_contract_id]
ON [dbo].[dbt_contract_change_equity] (
  [contract_id] ASC
)
GO

-- ----------------------------
-- Primary Key structure for table dbt_contract_change_equity
-- ----------------------------
ALTER TABLE [dbo].[dbt_contract_change_equity] ADD CONSTRAINT [PK__dbt_cont__3213E83F2B8C9B70] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

