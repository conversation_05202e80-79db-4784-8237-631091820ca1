-- 插入dbm_template
INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES (N'', N'审批任务提醒', N'inmail', N'白名单权益变更审批', N'合同${contractCode!}的转月/反点价次数发起变更，【${nodeName!}】待审批，请尽快处理。', N'normal', NULL, '2023-10-24 05:39:25.890', NULL, '2023-10-24 05:39:25.890', NULL, N'CONTRACT_EQUITY_APPROVE_NOTICE', NULL, NULL, 0);
INSERT INTO [dbo].[dbm_template]([third_party_id], [name], [message_type], [title], [content], [content_type], [memo], [created_at], [created_by], [updated_at], [updated_by], [template_code], [created_by_name], [updated_by_name], [is_gather]) VALUES (N'', N'审批结果提醒', N'inmail', N'白名单权益变更审批结果', N'合同${contractCode!}的转月/反点价次数变更审批${approvalResult!}，请查询。', N'normal', NULL, '2023-10-24 05:39:42.457', NULL, '2023-10-24 05:39:42.457', NULL, N'CONTRACT_EQUITY_APPROVE_RESULT_NOTICE', NULL, NULL, 0);

-- 插入dbm_business_template
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency], [template_id]) SELECT N'CONTRACT_EQUITY_APPROVE_NOTICE', N'', N'', N'inmail', N'NOTICE', N'system', N'', N'personal', N'', '2023-10-24 05:45:57.753', '2023-10-24 05:45:57.753', NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, '单条实时发送', id FROM dbm_template WHERE template_code = 'CONTRACT_EQUITY_APPROVE_NOTICE';
INSERT INTO [dbo].[dbm_business_template]([business_code], [copyer], [copyer_type], [message_type], [message_category], [sender], [receiver], [receiver_type], [next_action], [created_at], [updated_at], [created_by], [updated_by], [auto], [system], [status], [is_deleted], [created_by_name], [updated_by_name], [automatic_send], [send_frequency], [template_id]) SELECT N'CONTRACT_EQUITY_APPROVE_RESULT_NOTICE', N'', N'', N'inmail', N'NOTICE', N'system', N'', N'personal', N'', '2023-10-24 05:45:59.780', '2023-10-24 05:45:59.780', NULL, NULL, 1, 1, 1, 0, NULL, NULL, 1, '单条实时发送', id FROM dbm_template WHERE template_code = 'CONTRACT_EQUITY_APPROVE_RESULT_NOTICE';