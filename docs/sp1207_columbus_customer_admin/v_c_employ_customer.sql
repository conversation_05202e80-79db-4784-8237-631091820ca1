--名称:v_c_employ_customer
SELECT
	cec.*,
	c.linkage_customer_code AS 'linkage_customer_code',
	c.name AS 'customer_name',
	ce.name 'employ_name',
	ce.phone 'phone',
	ce.email 'email',
	c1.name 'enterprise_name'
FROM
	dba_c_employ_customer cec
	LEFT JOIN dba_customer c ON cec.customer_id = c.id
	LEFT JOIN dba_c_employ ce ON cec.c_employ_id	= ce.id
	LEFT JOIN dba_customer c1 ON cec.parent_customer_id = c1.id
	;
