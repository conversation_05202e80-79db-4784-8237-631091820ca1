--查询哥伦布端系统主管理员未添加系统管理员角色查询
SELECT
    a.id,
    a.c_employ_id,
    a.customer_id,
    a.type,
    r.id,
    r.employ_id,
    r.customer_id,
    r.role_def_id
FROM
    dba_c_employ_customer a
        LEFT JOIN (select * from dba_c_employ_role where role_id = 1) r ON a.c_employ_id = r.employ_id and a.customer_id = r.customer_id
where a.id > 10
  and a.is_deleted = 0
  and type = 0
  and role_def_id is null;

--新增管理员角色
INSERT INTO [dbo].[dba_c_employ_role] ([employ_id], [role_id], [role_def_id], [customer_id])
SELECT
    a.c_employ_id,
    1,
    1,
    a.customer_id
FROM
    dba_c_employ_customer a
        LEFT JOIN (select * from dba_c_employ_role where role_id = 1) r ON a.c_employ_id = r.employ_id and a.customer_id = r.customer_id
where a.id > 10
  and a.is_deleted = 0
  and type = 0
  and role_def_id is null;