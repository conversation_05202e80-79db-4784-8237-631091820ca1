IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dba_c_employ_customer]') AND type IN ('U'))
	DROP TABLE [dbo].[dba_c_employ_customer]
GO

CREATE TABLE [dbo].[dba_c_employ_customer] (
  [id] int  IDENTITY(1,1) NOT NULL,
  [c_employ_id] int DEFAULT ((0)) NULL,
  [customer_id] int DEFAULT ((0)) NULL,
  [parent_customer_id] int DEFAULT ((0)) NULL,
  [type] int DEFAULT ((0)) NULL,
  [signature_status] int  NULL,
  [signature_time] datetime  NULL,
  [signature_content] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [visit_time] datetime  NULL,
  [status] int DEFAULT ((1)) NULL,
  [is_deleted] int DEFAULT ((0)) NULL,
  [created_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [updated_by] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
  [created_at] datetime DEFAULT (getdate()) NULL,
  [updated_at] datetime DEFAULT (getdate()) NULL
)
GO

ALTER TABLE [dbo].[dba_c_employ_customer] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Auto increment value for dba_c_employ_customer
-- ----------------------------
DBCC CHECKIDENT ('[dbo].[dba_c_employ_customer]', RESEED, 1)
GO


-- ----------------------------
-- Primary Key structure for table dba_c_employ_customer
-- ----------------------------
ALTER TABLE [dbo].[dba_c_employ_customer] ADD CONSTRAINT [PK__c_employ__3213E83F0509B036] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

