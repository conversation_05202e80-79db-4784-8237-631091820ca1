CREATE TABLE [dbo].[dba_user_menu_collect] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [user_id] int DEFAULT 0 NULL,
    [system] tinyint DEFAULT 1 NULL,
    [name] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [new_menu_name] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [category_id] int DEFAULT 11 NULL,
    [menu_id] int DEFAULT 0 NULL,
    [menu_code] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [menu_url] nvarchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [sort] tinyint DEFAULT 0 NULL,
    [memo] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [is_deleted] tinyint DEFAULT 0 NULL,
    [created_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [updated_by] varchar(255) COLLATE Chinese_PRC_CI_AS  NULL,
    [created_at] datetime DEFAULT getdate() NULL,
    [updated_at] datetime DEFAULT getdate() NULL,
    [customer_id] int  NULL,
    CONSTRAINT [PK__dba_user__3213E83F9D2C6A86] PRIMARY KEY CLUSTERED ([id])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    ON [PRIMARY]
    )
    ON [PRIMARY]
    GO

ALTER TABLE [dbo].[dba_user_menu_collect] SET (LOCK_ESCALATION = TABLE)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户收藏夹表-ID自增',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户ID(来源于dba_employ或dba_c_employ表)',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'user_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'系统来源(1 Magellan/2Columbus)',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'system'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'收藏的名称（可修改）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'自定义的菜单名称',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'new_menu_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'品类（11 豆粕 12 豆油）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'category_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'菜单ID',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'menu_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'菜单编码',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'menu_code'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'收藏夹的路径',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'menu_url'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'Columbus区分主体，Magellan默认为0',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'customer_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'排序',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'sort'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'备注',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'memo'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否删除（0:未删除 1:已删除）',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'created_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新人',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'updated_by'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'创建时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'created_at'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'更新时间',
    'SCHEMA', N'dbo',
    'TABLE', N'dba_user_menu_collect',
    'COLUMN', N'updated_at'