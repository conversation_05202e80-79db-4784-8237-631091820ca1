-- adding batch TT creation group_id field by <PERSON> at 2025-01-06 start
-- Create sequence for group_id generation to fix race condition issues

-- Check if sequence already exists and drop it if needed
IF EXISTS (SELECT * FROM sys.sequences WHERE name = 'group_id_seq' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DROP SEQUENCE dbo.group_id_seq;
    PRINT 'Existing sequence dbo.group_id_seq dropped';
END

-- Create the sequence for group_id generation
CREATE SEQUENCE dbo.group_id_seq
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 2147483647  -- Max value for INT type
    NO CYCLE
    CACHE 10;  -- Cache 10 values for better performance

PRINT 'Sequence dbo.group_id_seq created successfully';

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT ON dbo.group_id_seq TO [your_application_user];

-- Test the sequence
SELECT NEXT VALUE FOR dbo.group_id_seq AS first_group_id;
SELECT NEXT VALUE FOR dbo.group_id_seq AS second_group_id;

PRINT 'Sequence test completed. You should see values 1 and 2 above.';

-- Optional: If you have existing group_id data, set the sequence to start after the max value
-- Uncomment and run the following if needed:
/*
DECLARE @max_group_id INT;
SELECT @max_group_id = ISNULL(MAX(group_id), 0) FROM dbt_contract WHERE group_id IS NOT NULL;

IF @max_group_id > 0
BEGIN
    DECLARE @sql NVARCHAR(100);
    SET @sql = 'ALTER SEQUENCE dbo.group_id_seq RESTART WITH ' + CAST(@max_group_id + 1 AS NVARCHAR(10));
    EXEC sp_executesql @sql;
    PRINT 'Sequence restarted with value: ' + CAST(@max_group_id + 1 AS NVARCHAR(10));
END
*/

-- adding batch TT creation group_id field by Jason Shi at 2025-01-06 end
