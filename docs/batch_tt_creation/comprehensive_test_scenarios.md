# 批量TT创建 Group ID 功能 - 综合测试用例

**Case Number:** 1003270 - batch TT creation group_id field  
**测试范围:** 点价、反点价、合同修改、回购、拆分等业务场景  
**前端系统:** 麦哲伦 (Magellan)  
**测试日期:** 2025-06-17  

## 📊 数据库当前状态

- **总合同数:** 12,239
- **有 group_id 的合同:** 4 个
- **无 group_id 的合同:** 12,235 个
- **当前序列值:** 8 (下一个可用值)

## 🎯 测试场景一：批量TT创建 - 新增合同

### 业务背景/目标
验证批量创建多个TT时，生成的合同能够正确分配相同的 group_id，实现批次追踪功能。

### 功能描述
用户在麦哲伦系统中一次性创建多个TT，系统应为同一批次创建的所有合同分配相同的 group_id。

### 输入/输出

**输入:**
- **接口:** `POST /tradeTicket/submitTTBatch`
- **参数:** 
```json
{
  "ttIdList": [19248, 19249, 19250],
  "submitStatus": "1",
  "ttProcessor": "SPOT_SALES",
  "salesType": 2,
  "type": 1,
  "residualRiskForceSubmit": false
}
```

**预期输出:**
- 返回成功状态
- 生成3个合同，都具有相同的 group_id (应为 8)
- 合同状态为有效 (status=1 或 2)

### 边界条件
1. **最小批次:** 1个TT (应生成 group_id)
2. **最大批次:** 系统支持的最大TT数量
3. **序列溢出:** group_id 接近 INTEGER 最大值
4. **并发提交:** 多用户同时批量提交

### 验证点
1. ✅ **Group ID 生成:** 所有合同具有相同的 group_id
2. ✅ **序列递增:** group_id 使用数据库序列，值为 8
3. ✅ **数据一致性:** dbt_contract 和 dbt_contract_history 都有 group_id
4. ✅ **业务逻辑:** 不影响现有单个TT创建流程

### 是否可自动化
**是** - 可通过 API 自动化测试

### 自动化测试脚本
```sql
-- 验证查询
SELECT contract_code, group_id, created_at 
FROM dbt_contract 
WHERE group_id = 8
ORDER BY created_at;

-- 验证历史表
SELECT contract_code, group_id, created_at 
FROM dbt_contract_history 
WHERE group_id = 8;
```

---

## 🎯 测试场景二：合同拆分 - Group ID 继承

### 业务背景/目标
验证合同拆分操作时，子合同能够正确继承父合同的 group_id，保持批次关联性。

### 功能描述
用户对已有合同进行拆分操作，生成的子合同应继承父合同的 group_id。

### 输入/输出

**输入:**
- **接口:** `POST /contract/split`
- **父合同:** TJIBSBMS2500967 (group_id = 7)
- **拆分参数:**
```json
{
  "contractId": 12255,
  "splitDetails": [
    {"contractNum": "500.00", "deliveryStartTime": "2025-07-01"},
    {"contractNum": "300.00", "deliveryStartTime": "2025-08-01"}
  ]
}
```

**预期输出:**
- 生成2个子合同
- 子合同 group_id = 7 (继承父合同)
- 父合同保持原有 group_id = 7

### 边界条件
1. **父合同无 group_id:** 子合同也应为 null
2. **父合同有 group_id:** 子合同必须继承
3. **多级拆分:** 子合同再拆分时继续继承

### 验证点
1. ✅ **继承逻辑:** 子合同 group_id = 父合同 group_id
2. ✅ **空值处理:** 父合同 group_id 为 null 时，子合同也为 null
3. ✅ **数据完整性:** 拆分后所有合同保持批次关联

### 是否可自动化
**是** - 可通过 API 自动化测试

---

## 🎯 测试场景三：合同回购 - Group ID 继承

### 业务背景/目标
验证销售合同回购操作时，回购合同能够正确继承原合同的 group_id。

### 功能描述
用户对销售合同执行回购操作，生成的回购合同应继承原合同的 group_id。

### 输入/输出

**输入:**
- **接口:** `POST /contract/applyBuyBack`
- **原合同:** TJIBSBMS2500966 (group_id = 7)
- **回购参数:**
```json
{
  "contractId": 12254,
  "buyBackNum": "800.00",
  "buyBackPrice": "3250.00",
  "reason": "市场价格变动"
}
```

**预期输出:**
- 生成回购合同
- 回购合同 group_id = 7 (继承原合同)
- 原合同状态更新，group_id 保持不变

### 验证点
1. ✅ **继承逻辑:** 回购合同继承原合同 group_id
2. ✅ **合同关联:** parent_id 正确设置
3. ✅ **业务状态:** 原合同和回购合同状态正确

### 是否可自动化
**是** - 可通过 API 自动化测试

---

## 🎯 测试场景四：点价/反点价 - Group ID 继承

### 业务背景/目标
验证点价和反点价操作时，新生成的合同能够正确继承原合同的 group_id。

### 功能描述
用户对基差合同进行点价或反点价操作，生成的新合同应继承原合同的 group_id。

### 输入/输出

**输入:**
- **接口:** `POST /future/priceApply` (点价申请)
- **原合同:** 具有 group_id 的基差合同
- **点价参数:**
```json
{
  "contractId": 12241,
  "priceType": 1,
  "targetPrice": "3300.00",
  "priceNum": "1000.00"
}
```

**预期输出:**
- 点价成功后生成新合同
- 新合同 group_id = 原合同 group_id
- 价格状态更新为已点价

### 验证点
1. ✅ **继承逻辑:** 点价后合同继承 group_id
2. ✅ **价格处理:** 点价逻辑正确执行
3. ✅ **状态管理:** 合同状态正确更新

### 是否可自动化
**是** - 可通过 API 自动化测试

---

## 🎯 测试场景五：合同修改 - Group ID 保持

### 业务背景/目标
验证合同修改操作时，group_id 保持不变，不会丢失批次信息。

### 功能描述
用户修改合同的业务信息（如交货期、数量等），group_id 应保持不变。

### 输入/输出

**输入:**
- **接口:** `POST /contract/revise`
- **目标合同:** TJIBSBMS2500965 (group_id = 6)
- **修改参数:**
```json
{
  "contractId": 12242,
  "deliveryEndTime": "2025-08-31",
  "memo": "延期交货"
}
```

**预期输出:**
- 合同修改成功
- group_id 保持为 6
- 修改记录正确保存

### 验证点
1. ✅ **Group ID 保持:** 修改前后 group_id 不变
2. ✅ **历史记录:** dbt_contract_history 正确记录
3. ✅ **业务逻辑:** 修改功能正常工作

### 是否可自动化
**是** - 可通过 API 自动化测试

---

## 🎯 测试场景六：并发批量提交

### 业务背景/目标
验证多用户同时进行批量TT提交时，group_id 生成的线程安全性。

### 功能描述
模拟多个用户同时提交批量TT，验证每个批次都能获得唯一的 group_id。

### 输入/输出

**输入:**
- 用户A: 提交 ttIdList: [19251, 19252]
- 用户B: 提交 ttIdList: [19253, 19254]
- 同时执行

**预期输出:**
- 用户A的合同: group_id = 8
- 用户B的合同: group_id = 9
- 无重复 group_id

### 验证点
1. ✅ **线程安全:** 无 group_id 重复
2. ✅ **序列正确:** 序列值正确递增
3. ✅ **数据一致性:** 每个批次内 group_id 相同

### 是否可自动化
**是** - 可通过并发测试工具

---

## 📋 测试执行计划

### 阶段一：基础功能测试 (1-2天)
- 场景一：批量TT创建
- 场景二：合同拆分
- 场景三：合同回购

### 阶段二：高级功能测试 (1天)
- 场景四：点价/反点价
- 场景五：合同修改

### 阶段三：性能和并发测试 (1天)
- 场景六：并发批量提交
- 压力测试：大批量TT创建

### 阶段四：回归测试 (0.5天)
- 验证现有功能未受影响
- 端到端业务流程测试

## 🔧 测试环境要求

- **数据库:** Azure SQL Server INT 环境
- **后端:** Navigator Cloud 微服务
- **前端:** 麦哲伦系统
- **测试数据:** 现有合同数据 + 新建测试数据

## 📊 成功标准

- ✅ 所有测试场景通过
- ✅ Group ID 正确生成和继承
- ✅ 现有功能无回归问题
- ✅ 性能指标满足要求
- ✅ 数据一致性验证通过

## 🤖 自动化测试脚本

### PowerShell 批量测试脚本

```powershell
# 批量TT创建自动化测试
$baseUri = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer YOUR_TOKEN"
}

# 测试场景一：批量TT创建
function Test-BatchTTCreation {
    $payload = @{
        ttIdList = @(19248, 19249, 19250)
        submitStatus = "1"
        ttProcessor = "SPOT_SALES"
        salesType = 2
        type = 1
        residualRiskForceSubmit = $false
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUri/tradeTicket/submitTTBatch" -Method POST -Headers $headers -Body $payload

    # 验证响应
    if ($response.success) {
        Write-Host "✅ 批量TT创建成功" -ForegroundColor Green
        return $response.data
    } else {
        Write-Host "❌ 批量TT创建失败: $($response.message)" -ForegroundColor Red
        return $null
    }
}

# 测试场景二：合同拆分
function Test-ContractSplit {
    param($contractId)

    $payload = @{
        contractId = $contractId
        splitDetails = @(
            @{ contractNum = "500.00"; deliveryStartTime = "2025-07-01" },
            @{ contractNum = "300.00"; deliveryStartTime = "2025-08-01" }
        )
    } | ConvertTo-Json -Depth 3

    $response = Invoke-RestMethod -Uri "$baseUri/contract/split" -Method POST -Headers $headers -Body $payload

    if ($response.success) {
        Write-Host "✅ 合同拆分成功" -ForegroundColor Green
        return $response.data
    } else {
        Write-Host "❌ 合同拆分失败: $($response.message)" -ForegroundColor Red
        return $null
    }
}

# 执行完整测试套件
function Run-ComprehensiveTests {
    Write-Host "🚀 开始执行 Group ID 功能测试..." -ForegroundColor Cyan

    # 记录测试开始时间
    $startTime = Get-Date

    # 执行各个测试场景
    $batchResult = Test-BatchTTCreation
    $splitResult = Test-ContractSplit -contractId 12255

    # 记录测试结束时间
    $endTime = Get-Date
    $duration = $endTime - $startTime

    Write-Host "⏱️ 测试完成，耗时: $($duration.TotalSeconds) 秒" -ForegroundColor Cyan
}
```

### SQL 验证查询集

```sql
-- 验证脚本集合

-- 1. 验证批量创建的合同具有相同 group_id
SELECT
    contract_code,
    group_id,
    created_at,
    CASE
        WHEN group_id IS NOT NULL THEN '✅ 有 Group ID'
        ELSE '❌ 缺少 Group ID'
    END as group_id_status
FROM dbt_contract
WHERE created_at >= DATEADD(minute, -10, GETDATE())
ORDER BY created_at DESC;

-- 2. 验证 group_id 继承 - 拆分场景
SELECT
    parent.contract_code as parent_contract,
    parent.group_id as parent_group_id,
    child.contract_code as child_contract,
    child.group_id as child_group_id,
    CASE
        WHEN parent.group_id = child.group_id THEN '✅ 继承正确'
        WHEN parent.group_id IS NULL AND child.group_id IS NULL THEN '✅ 空值处理正确'
        ELSE '❌ 继承错误'
    END as inheritance_status
FROM dbt_contract parent
INNER JOIN dbt_contract child ON child.parent_id = parent.id
WHERE parent.created_at >= DATEADD(day, -1, GETDATE());

-- 3. 验证序列使用情况
SELECT
    MAX(group_id) as max_group_id,
    COUNT(DISTINCT group_id) as unique_group_ids,
    COUNT(*) as total_contracts_with_group_id
FROM dbt_contract
WHERE group_id IS NOT NULL;

-- 4. 验证历史表数据一致性
SELECT
    c.contract_code,
    c.group_id as contract_group_id,
    h.group_id as history_group_id,
    CASE
        WHEN c.group_id = h.group_id OR (c.group_id IS NULL AND h.group_id IS NULL)
        THEN '✅ 一致'
        ELSE '❌ 不一致'
    END as consistency_status
FROM dbt_contract c
LEFT JOIN dbt_contract_history h ON c.id = h.contract_id
WHERE c.created_at >= DATEADD(day, -1, GETDATE());

-- 5. 验证并发安全性 - 检查重复 group_id
SELECT
    group_id,
    COUNT(*) as contract_count,
    MIN(created_at) as first_created,
    MAX(created_at) as last_created,
    DATEDIFF(second, MIN(created_at), MAX(created_at)) as time_span_seconds
FROM dbt_contract
WHERE group_id IS NOT NULL
GROUP BY group_id
HAVING COUNT(*) > 1
ORDER BY group_id;
```

## 📈 性能基准测试

### 响应时间要求
- **批量TT创建 (3个TT):** < 5秒
- **合同拆分:** < 3秒
- **合同回购:** < 3秒
- **点价操作:** < 2秒
- **合同修改:** < 2秒

### 并发测试要求
- **并发用户数:** 10个用户同时操作
- **成功率:** > 99%
- **数据一致性:** 100%
- **Group ID 唯一性:** 100%

## 🔍 故障排查指南

### 常见问题及解决方案

1. **Group ID 为 NULL**
   - 检查 GroupIdGenerationService 是否正确注入
   - 验证数据库序列是否存在
   - 查看应用日志中的错误信息

2. **Group ID 重复**
   - 检查数据库序列配置
   - 验证并发控制是否正确
   - 检查事务隔离级别

3. **继承逻辑失败**
   - 验证父合同是否存在 group_id
   - 检查继承逻辑的空值处理
   - 确认业务逻辑执行顺序

### 日志监控关键字
- `Generated new group ID from sequence`
- `Inherit group_id from parent contract`
- `Set group_id for batch contract creation`
- `Error generating group ID from sequence`

## 📋 测试报告模板

### 测试执行记录
```
测试场景: [场景名称]
执行时间: [YYYY-MM-DD HH:mm:ss]
执行人: [测试人员]
测试环境: [环境信息]

输入数据:
[具体输入参数]

预期结果:
[预期的输出和状态]

实际结果:
[实际的输出和状态]

验证查询:
[SQL查询结果]

测试结果: ✅ PASS / ❌ FAIL
备注: [其他说明]
```

## 🎯 验收标准

### 功能验收
- [ ] 批量TT创建生成正确的 group_id
- [ ] 所有继承场景正确传递 group_id
- [ ] 现有功能无回归问题
- [ ] 数据库数据一致性验证通过

### 性能验收
- [ ] 响应时间满足要求
- [ ] 并发测试通过
- [ ] 系统稳定性良好

### 安全验收
- [ ] 数据库序列线程安全
- [ ] 事务处理正确
- [ ] 异常处理完善

---

**测试完成标志:** 所有验收标准通过 ✅
