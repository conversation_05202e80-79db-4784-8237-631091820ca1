-- adding batch TT creation group_id field by <PERSON> at 2025-01-06 start
-- Add group_id column to contract tables for batch TT creation tracking

-- 1. Add group_id column to main contract table
ALTER TABLE dbt_contract
ADD group_id int NULL;

-- Add index for performance on main table
CREATE INDEX idx_dbt_contract_group_id ON dbt_contract(group_id);

-- Add column description for main table
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'批次组ID，用于标识同一批次创建的合同',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'dbt_contract',
    @level2type = N'COLUMN', @level2name = N'group_id';

-- 2. Add group_id column to contract history table
ALTER TABLE dbt_contract_history
ADD group_id int NULL;

-- Add index for performance on history table
CREATE INDEX idx_dbt_contract_history_group_id ON dbt_contract_history(group_id);

-- Add column description for history table
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'批次组ID，用于标识同一批次创建的合同（历史记录）',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'dbt_contract_history',
    @level2type = N'COLUMN', @level2name = N'group_id';

-- Verification queries to check the columns were added successfully
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_NAME IN ('dbt_contract', 'dbt_contract_history') AND COLUMN_NAME = 'group_id'
-- ORDER BY TABLE_NAME;

-- adding batch TT creation group_id field by Jason Shi at 2025-01-06 end
