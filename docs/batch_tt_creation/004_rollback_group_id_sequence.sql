-- adding batch TT creation group_id field by <PERSON> at 2025-01-06 start
-- Rollback script for group_id sequence

-- WARNING: This script will permanently remove the group_id sequence
-- Only run this script if you need to rollback the sequence implementation

-- Check if sequence exists and drop it
IF EXISTS (SELECT * FROM sys.sequences WHERE name = 'group_id_seq' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    DROP SEQUENCE dbo.group_id_seq;
    PRINT 'Sequence dbo.group_id_seq dropped successfully';
END
ELSE
BEGIN
    PRINT 'Sequence dbo.group_id_seq does not exist';
END

-- Verification query to confirm the sequence was removed
-- SELECT * FROM sys.sequences WHERE name = 'group_id_seq' AND schema_id = SCHEMA_ID('dbo');
-- This should return no rows if rollback was successful

PRINT 'Sequence rollback completed. Please verify that dbo.group_id_seq sequence has been removed.';

-- adding batch TT creation group_id field by <PERSON> at 2025-01-06 end
