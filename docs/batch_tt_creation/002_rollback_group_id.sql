-- adding batch TT creation group_id field by <PERSON> at 2025-01-06 start
-- Rollback script for group_id column addition to contract tables

-- WARNING: This script will permanently remove the group_id columns and all their data
-- Only run this script if you need to rollback the group_id implementation

-- 1. Rollback dbt_contract table
-- Remove the index first
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_dbt_contract_group_id' AND object_id = OBJECT_ID('dbt_contract'))
BEGIN
    DROP INDEX idx_dbt_contract_group_id ON dbt_contract;
    PRINT 'Index idx_dbt_contract_group_id dropped successfully';
END
ELSE
BEGIN
    PRINT 'Index idx_dbt_contract_group_id does not exist';
END

-- Remove the column description
IF EXISTS (
    SELECT * FROM sys.extended_properties
    WHERE major_id = OBJECT_ID('dbt_contract')
    AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('dbt_contract') AND name = 'group_id')
    AND name = 'MS_Description'
)
BEGIN
    EXEC sp_dropextendedproperty
        @name = N'MS_Description',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'dbt_contract',
        @level2type = N'COLUMN', @level2name = N'group_id';
    PRINT 'Column description for dbt_contract.group_id removed successfully';
END
ELSE
BEGIN
    PRINT 'Column description for dbt_contract.group_id does not exist';
END

-- Remove the group_id column from main table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbt_contract') AND name = 'group_id')
BEGIN
    ALTER TABLE dbt_contract DROP COLUMN group_id;
    PRINT 'Column group_id dropped from dbt_contract successfully';
END
ELSE
BEGIN
    PRINT 'Column group_id does not exist in dbt_contract';
END

-- 2. Rollback dbt_contract_history table
-- Remove the index first
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_dbt_contract_history_group_id' AND object_id = OBJECT_ID('dbt_contract_history'))
BEGIN
    DROP INDEX idx_dbt_contract_history_group_id ON dbt_contract_history;
    PRINT 'Index idx_dbt_contract_history_group_id dropped successfully';
END
ELSE
BEGIN
    PRINT 'Index idx_dbt_contract_history_group_id does not exist';
END

-- Remove the column description
IF EXISTS (
    SELECT * FROM sys.extended_properties
    WHERE major_id = OBJECT_ID('dbt_contract_history')
    AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('dbt_contract_history') AND name = 'group_id')
    AND name = 'MS_Description'
)
BEGIN
    EXEC sp_dropextendedproperty
        @name = N'MS_Description',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'dbt_contract_history',
        @level2type = N'COLUMN', @level2name = N'group_id';
    PRINT 'Column description for dbt_contract_history.group_id removed successfully';
END
ELSE
BEGIN
    PRINT 'Column description for dbt_contract_history.group_id does not exist';
END

-- Remove the group_id column from history table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbt_contract_history') AND name = 'group_id')
BEGIN
    ALTER TABLE dbt_contract_history DROP COLUMN group_id;
    PRINT 'Column group_id dropped from dbt_contract_history successfully';
END
ELSE
BEGIN
    PRINT 'Column group_id does not exist in dbt_contract_history';
END

-- Verification query to confirm the columns were removed
-- SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE
-- FROM INFORMATION_SCHEMA.COLUMNS
-- WHERE TABLE_NAME IN ('dbt_contract', 'dbt_contract_history') AND COLUMN_NAME = 'group_id'
-- ORDER BY TABLE_NAME;
-- This should return no rows if rollback was successful

PRINT 'Rollback completed. Please verify that group_id columns have been removed from both contract tables.';

-- adding batch TT creation group_id field by Jason Shi at 2025-01-06 end
