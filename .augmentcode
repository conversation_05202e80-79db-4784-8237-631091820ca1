# Augment Code 项目配置

## 项目概述
Navigator_cloud 是一个基于 Spring Cloud 的大型微服务项目，用于商品贸易管理系统。

## 快速理解指南

### 项目结构
- **后端**: 13个Spring Cloud微服务
- **前端**: Columbus_web（内部）+ Magellan_web（外部）
- **数据**: SQL Server + Redis + Activiti工作流
- **集成**: ATLAS系统（通过CUCKOO微服务）

### 核心业务
1. **合同管理** - 固定价格和基差临时定价合同
2. **提货申请** - 与ATLAS系统集成的提货流程
3. **审批流程** - 基于Activiti的LOA审批
4. **交易处理** - 完整的贸易业务流程

### 开发规范
- 使用Java 11和Maven
- 代码修改需要添加案例号注释
- 最小化修改原则
- 优先使用现有接口

### 重要提醒
- 跨工作空间编辑注意编码问题
- 使用PowerShell而非cmd
- 数据库ID使用序列生成
- 共享功能修改需谨慎

## 文件组织
- `.augment/memories.md` - 项目记忆配置
- `.augment/rules.md` - 开发规则规范
- `.augment/architecture.md` - 架构详细说明
- `docs/dev_tool/` - 开发工具和文档

## 常用路径
- 微服务配置: `service/src/main/resources/bootstrap.yml`
- 前端项目: `../Columbus_web` 和 `../Magellan_web`
- 开发文档: `docs/dev_tool/`

## 关键业务规则
- contractType=1或4显示回购按钮
- group_id用于批次管理，子合同继承父合同
- executedNum由ATLAS回调更新
- 未提货量 = allocateNum - executedNum（最小值0）

## Repository management
- Using Azure DevOps for version control
- Regularly pull and push changes
- Code review base on current branch's commit and PRD(requirement), SQL script

请参考 `.augment/` 目录下的详细配置文件获取更多信息。
