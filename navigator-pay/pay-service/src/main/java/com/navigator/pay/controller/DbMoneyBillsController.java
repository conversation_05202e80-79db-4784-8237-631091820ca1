package com.navigator.pigeon.controller;


import com.navigator.common.dto.Result;
import com.navigator.pigeon.pojo.entity.DbMoneyBills;
import com.navigator.pigeon.service.IDbMoneyBillsService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@RestController
@RequestMapping("/dbMoneyBills")
public class DbMoneyBillsController {

    @Resource
    IDbMoneyBillsService dbMoneyBillsService;

    @GetMapping("/findBillsById")
    @ApiOperation(value = "根据id获取账单", notes = "根据id获取账单", httpMethod = "GET")
    public Result<DbMoneyBills> findBillsById(@ApiParam(value = "账单id")
                                              @RequestParam Integer billsId) {
        return Result.success(dbMoneyBillsService.getById(billsId));
    }

}
