package com.navigator.pigeon.facade.impl;


import com.navigator.pigeon.pojo.entity.DbMoneyBills;
import com.navigator.pigeon.facade.MoneyBillsFacade;
import com.navigator.pigeon.service.IDbMoneyBillsService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class DbMoneyBillsFacadeImpl implements MoneyBillsFacade {

    @Resource
    IDbMoneyBillsService dbMoneyBillsService;

    @Override
    public DbMoneyBills findOne() {
        return dbMoneyBillsService.getById(1501);
    }
}
