<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.pigeon.mapper.DbMoneyBillsMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.pigeon.pojo.entity.DbMoneyBills">
                    <result column="id" property="id"/>
                    <result column="account_business_id" property="accountBusinessId"/>
                    <result column="type" property="type"/>
                    <result column="pay_type" property="payType"/>
                    <result column="bill_type" property="billType"/>
                    <result column="status" property="status"/>
                    <result column="code" property="code"/>
                    <result column="expire_time" property="expireTime"/>
                    <result column="customer_id" property="customerId"/>
                    <result column="customer_name" property="customerName"/>
                    <result column="money" property="money"/>
                    <result column="deduction_money" property="deductionMoney"/>
                    <result column="practical_money" property="practicalMoney"/>
                    <result column="money_account_id" property="moneyAccountId"/>
                    <result column="money_account_name" property="moneyAccountName"/>
                    <result column="bank_name" property="bankName"/>
                    <result column="account_num" property="accountNum"/>
                    <result column="customer_money_account_id" property="customerMoneyAccountId"/>
                    <result column="customer_money_account_name" property="customerMoneyAccountName"/>
                    <result column="customer_bank_name" property="customerBankName"/>
                    <result column="customer_account_num" property="customerAccountNum"/>
                    <result column="source" property="source"/>
                    <result column="source_id" property="sourceId"/>
                    <result column="create_type" property="createType"/>
                    <result column="account_employees_id" property="accountEmployeesId"/>
                    <result column="is_finishing" property="isFinishing"/>
                    <result column="is_invalid" property="isInvalid"/>
                    <result column="is_edit" property="isEdit"/>
                    <result column="is_delete" property="isDelete"/>
                    <result column="action_accessory" property="actionAccessory"/>
                    <result column="action_remark" property="actionRemark"/>
                    <result column="remark" property="remark"/>
                    <result column="logs" property="logs"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="contract_id" property="contractId"/>
                    <result column="delivery_id" property="deliveryId"/>
                    <result column="trading_time" property="tradingTime"/>
                    <result column="group_code" property="groupCode"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, account_business_id, type, pay_type, bill_type, status, code, expire_time, customer_id, customer_name, money, deduction_money, practical_money, money_account_id, money_account_name, bank_name, account_num, customer_money_account_id, customer_money_account_name, customer_bank_name, customer_account_num, source, source_id, create_type, account_employees_id, is_finishing, is_invalid, is_edit, is_delete, action_accessory, action_remark, remark, logs, created_at, updated_at, contract_id, delivery_id, trading_time, group_code
        </sql>
</mapper>
