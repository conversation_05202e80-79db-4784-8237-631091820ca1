package com.navigator.pigeon.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value="DbMoneyBills对象", description="")
public class DbMoneyBills implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "所属企业")
    private Integer accountBusinessId;

    @ApiModelProperty(value = "类型：0应收1应付")
    private Integer type;

    @ApiModelProperty(value = "付款核销方式：0直接支付1退至余额")
    private Integer payType;

    @ApiModelProperty(value = "款项类别：0其他 1开户保证金 2预付款 3合同保证金 4合同保证金追加 5货款 6销售合同短溢款 7运费 8补偿款 9销售合同提货款 10采购合同提货款 11采购合同短溢款 12采购商退款 13转出保证金 14转入保证金 15转出预付款 16转入预付款 17销售合同结算退款 18 提货抵消保证金 19 提货抵消预付款 20 提货使用优惠券 21 客户余额转账转出 22 客户余额转账转入 23 向信用账户借款 24 向信用账户还款")
    private Integer billType;

    @ApiModelProperty(value = "操作状态：0待核销1已核销2已作废3已申请付款")
    private Integer status;

    @ApiModelProperty(value = "账单编号")
    private String code;

    @ApiModelProperty(value = "账单到期日")
    private Date expireTime;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "合计金额")
    private Double money;

    @ApiModelProperty(value = "转移支付金额-扣减")
    private Double deductionMoney;

    @ApiModelProperty(value = "实际金额-（合计减扣减）")
    private Double practicalMoney;

    @ApiModelProperty(value = "银行卡ID")
    private Integer moneyAccountId;

    @ApiModelProperty(value = "开户名")
    private String moneyAccountName;

    @ApiModelProperty(value = "开户行")
    private String bankName;

    @ApiModelProperty(value = "账户")
    private String accountNum;

    @ApiModelProperty(value = "客户银行卡ID")
    private Integer customerMoneyAccountId;

    @ApiModelProperty(value = "客户开户名")
    private String customerMoneyAccountName;

    @ApiModelProperty(value = "客户开户行")
    private String customerBankName;

    @ApiModelProperty(value = "客户账户")
    private String customerAccountNum;

    @ApiModelProperty(value = "来源：0合同1提单2客户")
    private Integer source;

    @ApiModelProperty(value = "来源ID：0合同1提单2客户")
    private Integer sourceId;

    @ApiModelProperty(value = "创建方式：0系统生成1手动添加")
    private Integer createType;

    @ApiModelProperty(value = "员工ID")
    private Integer accountEmployeesId;

    @ApiModelProperty(value = "是否可核销:0是1否")
    private Integer isFinishing;

    @ApiModelProperty(value = "是否可作废：0是1否")
    private Integer isInvalid;

    @ApiModelProperty(value = "是否可编辑：0是1否")
    private Integer isEdit;

    @ApiModelProperty(value = "是否可删除：0是1否")
    private Integer isDelete;

    @ApiModelProperty(value = "操作附件")
    private String actionAccessory;

    @ApiModelProperty(value = "操作备注")
    private String actionRemark;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "账单日志")
    private String logs;

    private Date createdAt;

    private Date updatedAt;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "提货id")
    private Integer deliveryId;

    @ApiModelProperty(value = "交易时间")
    private Date tradingTime;

    @ApiModelProperty(value = "组合code")
    private String groupCode;


}
