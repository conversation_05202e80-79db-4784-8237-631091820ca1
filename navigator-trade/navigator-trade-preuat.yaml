apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-trade-preuat
  namespace: preuat
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ldc-navigator-trade-preuat
  template:
    metadata:
      labels:
        app: ldc-navigator-trade-preuat
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-trade-preuat:#{Build.BuildId}#
        name: ldc-navigator-trade-preuat
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "preuat" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4nnvgsto001-secret  # required
            shareName: logs-preuat  # required
            server: csm4nnvgsto001.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional
