package com.navigator.trade.pojo.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合同类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Getter
public enum ContractTypeEnum {
    /**
     * 合同类型
     */
    YI_KOU_JIA(1, "一口价"),
    JI_CHA(2, "基差"),
    ZAN_DING_JIA(3, "暂定价"),
    JI_CHA_ZAN_DING_JIA(4, "基差暂定价"),
    STRUCTURE(5, "结构化定价"),
    ;
    int value;
    String desc;

    ContractTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractTypeEnum getByValue(int value) {
        for (ContractTypeEnum typeEnum : ContractTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return ContractTypeEnum.YI_KOU_JIA;
    }

    public static ContractTypeEnum getByDesc(String desc) {
        for (ContractTypeEnum typeEnum : ContractTypeEnum.values()) {
            if (typeEnum.getDesc().equals(desc)) {
                return typeEnum;
            }
        }
        return ContractTypeEnum.YI_KOU_JIA;
    }

    public static String getDescByValue(int value) {
        for (ContractTypeEnum typeEnum : ContractTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum.getDesc();
            }
        }
        return ContractTypeEnum.YI_KOU_JIA.getDesc();
    }

    /**
     * 基差合同类型
     *
     * @return
     */
    public static List<Integer> getBasicTypeList() {
        return Arrays.asList(JI_CHA.getValue(), JI_CHA_ZAN_DING_JIA.getValue());
    }

    /**
     * 基差合同类型
     *
     * @return
     */
    public static List<Integer> getBasicList() {
        return Arrays.asList(JI_CHA.getValue());
    }

}
