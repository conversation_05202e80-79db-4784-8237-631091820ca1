package com.navigator.trade.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 合同含税单价的VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@Accessors(chain = true)
public class ContractUnitPriceVO {

    /**
     * 含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 含税单价-物流相关费用
     */
    private BigDecimal fobUnitPrice;

    /**
     * 含税单价/（税率+1）
     */
    private BigDecimal cifUnitPrice;


}
