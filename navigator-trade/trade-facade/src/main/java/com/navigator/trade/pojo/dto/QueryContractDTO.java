package com.navigator.trade.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/29 13:53
 */
@Data
@Accessors(chain = true)
public class QueryContractDTO {

    private String status;

    private Integer ldcFrame;           // 是否是ldc模板 0：nonFrame 1:是

    private Integer originalPaperStatus; // 正本状态

    private String contractCode;        // 合同编号

    private String customerName;        // 客户

    private String deliveryFactoryCode;     // 交货工厂code

    private BigDecimal contractNum;     // 合同吨数

    private String deliveryFactoryName;     //交货工厂名称

    private BigDecimal unitPrice;       // 含税单价

    private Integer contractType;       // 合同类型（1 一口价合同 2 基差合同 3 点价合同）")

    private Integer warningType;        // 预警类型

    private Integer goodsCategoryId;    // 品种Id

    private Integer goodsId;    // 货品id

    private Integer category2;    //二级品类

    private Integer category3;    //三级品类

    private Integer goodsPackageId;     // 包装Id

    private Integer goodsSpecId;        // 规格Id

    private String signDate;            // 签订日期

    private String priceEndTime;        // 点价截止日期

    private String startTime;   //开始交货日期开始时间

    private String endTime;     //开始交货日期结束时间


    private String deliveryType;        // 交提货方式

    private String destination;         // 目的港

    private String domainCode;          // 期货合约

    private String futureCode;          //期货代码

    private String siteCode;           //账套code

    private String customerId;          // 客户Id

    private String companyId;          // 主体id

    private String type;                // 操作类型（1.点价 2.转月 3.反点价）

    private Integer priceAllocateId;    //分配单id

    private Integer salesType;          //1,采购 2,销售

    private String buCode;              //业务线

    private String shipWarehouseId;     //交割库Id

}
