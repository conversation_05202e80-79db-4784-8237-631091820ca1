package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2
 */
@Getter
@AllArgsConstructor
public enum CustomerSignatureTypeEnum {

    /**
     * 客户签章方式（不签章 易企签 线下签）
     */
    NO_SIGN(0, "不签章", "NO_SIGN"),
    YQQ_SIGN(1, "易企签", "YQQ_SIGN"),
    OFFLINE_SIGN(2, "线下签章", "OFFLINE_SIGN")
    ;
    Integer value;
    String desc;
    String code;
    public static CustomerSignatureTypeEnum getByType(Integer value) {
        for (CustomerSignatureTypeEnum customerSignatureTypeEnum : CustomerSignatureTypeEnum.values()) {
            if (customerSignatureTypeEnum.value.equals(value)) {
                return customerSignatureTypeEnum;
            }
        }
        return null;
    }
}
