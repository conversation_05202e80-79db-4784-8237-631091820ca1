package com.navigator.trade.pojo.dto.contract;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 转月/反点价次数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-23
 */
@Data
@Accessors(chain = true)
public class ContractTransferCountDTO {

    /**
     * 总转月次数
     */
    private Integer totalTransferTimes;

    /**
     * 可转月次数
     */
    private Integer ableTransferTimes;

    /**
     * 总反点价次数
     */
    private Integer totalReversePriceTimes;

    /**
     * 可反点价次数
     */
    private Integer ableReversePriceTimes;

    /**
     * 已转月次数
     */
    private Integer transferredTimes;

    /**
     * 已反点价次数
     */
    private Integer reversedPriceTimes;

    /**
     * 是否是超远月
     */
    private Integer isOverForward;

}
