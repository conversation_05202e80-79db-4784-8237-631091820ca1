package com.navigator.trade.pojo.dto.contractsign;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/25
 */

@Data
@Accessors(chain = true)
public class ContractSignYQQCallbackDTO {

    //合同唯一编码
    private String uuid;
    //返回的签署地址
    private String signatureUrl;
    //签章状态 1:发起成功 2:待LDC签章(参与者正在处理信封<回调签章链接>) 3:LDC签章完成(参与者确认) 4:待客户签章(参与者正在处理信封<回调签章链接>) 5:客户确认签章(参与者确认) 6、文件上传完成
    private Integer signatureStatus;
    //协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）
    private Integer status;
    //自动签状态
    private Integer voluntarilySignType;
    private Integer backStatus;

}
