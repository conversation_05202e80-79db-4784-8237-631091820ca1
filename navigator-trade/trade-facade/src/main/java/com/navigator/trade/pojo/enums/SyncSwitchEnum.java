package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SyncSwitchEnum {
    /**
     * 同步系统
     */
    NONE(0, "不同步"),
    LKG(1, "只同步LKG"),
    ATLAS(2, "只同步ATLAS"),
    BOTH(3, "同步LKG和ATLAS");


    final int value;
    final String desc;

    public static SyncSwitchEnum getByValue(int value) {
        for (SyncSwitchEnum syncSwitchEnum : SyncSwitchEnum.values()) {
            if (value == syncSwitchEnum.getValue()) {
                return syncSwitchEnum;
            }
        }
        return SyncSwitchEnum.NONE;
    }

}
