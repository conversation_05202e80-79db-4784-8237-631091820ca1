package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-06 17:59
 */
@FeignClient(name = "navigator-trade-service")
@Component
public interface DeliveryTypeFacade {

    /**
     * 获取所有交提货方式
     *
     * @return 交提货方式集合
     */
    @GetMapping("/getAllDeliveryTypeList")
    List<DeliveryTypeEntity> getAllDeliveryTypeList(@RequestParam(value = "status", required = false) Integer status,
                                                    @RequestParam(value = "categoryId", required = false) Integer categoryId,
                                                    @RequestParam(value = "siteCode", required = false) String siteCode,
                                                    @RequestParam(value = "buCode", required = false) String buCode,
                                                    @RequestParam(value = "type", required = false) Integer type);

    /**
     * 根据品类-获取提货类型（分组）
     *
     * @param categoryId 品类ID
     * @return 分组的提货类型信息
     */
    @GetMapping("/getDeliveryTypeByCategoryId")
    List<DeliveryTypeVO> getDeliveryTypeByCategoryId(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                                     @RequestParam(value = "siteCode", required = false) String siteCode,
                                                     @RequestParam(value = "buCode", required = false) String buCode);


    @GetMapping("/getAllDeliveryByAddressType")
    List<DeliveryTypeEntity> getAllDeliveryByAddressType(@RequestParam(value = "status", required = false) Integer status,
                                                         @RequestParam(value = "addressType", required = false) Integer addressType);

    /**
     * 根据Id获取交提货方式信息
     *
     * @param id 交提货方式ID
     * @return 提货方式信息
     */
    @GetMapping("/getDeliveryTypeById")
    DeliveryTypeEntity getDeliveryTypeById(@RequestParam(value = "id") Integer id);


    /**
     * 根据lkgCode获取交提货方式信息
     *
     * @param lkgCode 交提货方式ID
     * @return 提货方式信息
     */
    @GetMapping("/getDeliveryTypeByLkgCode")
    DeliveryTypeEntity getDeliveryTypeByLkgCode(@RequestParam(value = "lkgCode") String lkgCode);

    /**
     * 新增或编辑交提货方式
     *
     * @param deliveryTypeEntity
     * @return
     */
    @PostMapping("/saveOrUpdateDeliveryType")
    Result saveOrUpdateDeliveryType(@RequestBody DeliveryTypeEntity deliveryTypeEntity);

    /**
     * 启用禁用
     *
     * @param deliveryTypeId
     * @return
     */
    @GetMapping("/invalidStatus")
    Result invalidStatus(@RequestParam(value = "deliveryTypeId") Integer deliveryTypeId);

    /**
     * 导入提货类型
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/importDeliveryType")
    Result importDeliveryType(@RequestParam("file") MultipartFile uploadFile);

    /**
     * 获取提货类型为送货的集合
     *
     * @return
     */
    @GetMapping("/getSendDeliveryTypeIdList")
    List<Integer> getSendDeliveryTypeIdList();
}
