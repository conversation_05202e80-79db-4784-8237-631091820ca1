package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@Accessors(chain = true)
public class TTQueryStructureVO implements Serializable {
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "买方")
    private String customerName;

    @ApiModelProperty(value = "卖方")
    private String supplierName;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "品类名称")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "品类名称")
    private String goodsCategoryName;

    @ApiModelProperty(value = "结构总量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "1:A,2:B,3:C")
    private Integer structureType;

    @ApiModelProperty(value = "1:A,2:B,3:C")
    private String structureTypeName;

    @ApiModelProperty(value = "定价开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "定价结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "敲出价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "增强价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "总交易日")
    private Integer totalDay;

    @ApiModelProperty(value = "1单位量")
    private BigDecimal unitNum;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;
    @ApiModelProperty(value = "创建人")
    private String createdByName;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "规则说明")
    private String priceRule;
//------
    @ApiModelProperty(value = "交易类型")
    private Integer tradeType;
    @ApiModelProperty(value = "交易类型")
    private String tradeTypeName;
    @ApiModelProperty(value = "所属商务")
    private Integer ownerId;
    @ApiModelProperty(value = "所属商务")
    private String ownerName;
    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "累积价格")
    private String cumulativePrice;
    @ApiModelProperty(value = "触发价格")
    private String triggerPrice;
    @ApiModelProperty(value = "结构化单位数量")
    private BigDecimal structureUnitNum;
    @ApiModelProperty(value = "单位增量")
    private BigDecimal unitIncrement;
    @ApiModelProperty(value = "现金返还量")
    private String cashReturn;
    @ApiModelProperty(value = "ttId")
    private Integer ttId;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "买方客户编号")
    private String customerCode;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂id")
    private Integer deliveryFactoryId;

    @ApiModelProperty(value = "交货工厂名称")
    private Integer deliveryFactoryName;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

}
