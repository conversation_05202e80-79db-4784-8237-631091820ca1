package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 待办审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
@Data
@Accessors(chain = true)
@TableName("dbz_approval")
@ApiModel(value = "ApprovalEntity对象", description = "待办审批表")
public class ApprovalEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "审批编号")
    private String approvalKey;

    @ApiModelProperty(value = "审批事件类型（1001 TT审批 1002 合同审批）")
    private Integer approvalEventType;

    @ApiModelProperty(value = "功能模块类型")
    private Integer moduleType;

    @ApiModelProperty(value = "业务编码")
    private String businessCode;

    @ApiModelProperty(value = "（合同号/提货号）")
    private String businessNum;

    @ApiModelProperty(value = "业务状态")
    private Integer status;

    @ApiModelProperty(value = "审批流程状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "发起人ID")
    private Integer sponsorId;

    @ApiModelProperty(value = "发起人姓名")
    private String sponsorName;

    @ApiModelProperty(value = "审批起始时间")
    private Date startTime;

    @ApiModelProperty(value = "审批结束时间")
    private Date endTime;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;


}
