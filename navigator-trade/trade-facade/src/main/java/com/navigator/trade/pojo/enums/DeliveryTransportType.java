package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-04-02 13:25
 */
@Deprecated
@Getter
@AllArgsConstructor
public enum DeliveryTransportType {
    /**
     * 交提货-运输方式
     */

    TRUCK(1, "卡车"),
    SHIPPING(2, "船舶/驳船"),
    PIPELINE(3, "管道提货"),

    ;
    int value;
    String desc;

    public static DeliveryTransportType getByDesc(String desc) {
        return Arrays.stream(values())
                .filter(deliveryTransportType -> Objects.equals(desc, deliveryTransportType.getDesc()))
                .findFirst()
                .orElse(TRUCK);
    }

}
