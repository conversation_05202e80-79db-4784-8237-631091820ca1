package com.navigator.trade.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ReportVO {
    private String factoryCode;
    private Integer contractType;
    private Integer status;
    private String contractTypeName;
    private String deliveryStartTime;
    private BigDecimal unitPrice;
    private String averagePrice;
    private BigDecimal unsubmitSize;
    private BigDecimal approvingSize;
    private BigDecimal doneSize;
    private BigDecimal invalidSize;
    private BigDecimal totalSize;
    @ApiModelProperty(value = "变更合同量")
    private BigDecimal changeContractNum;

}
