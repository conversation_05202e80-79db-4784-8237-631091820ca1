package com.navigator.trade.facade;

import com.navigator.trade.pojo.entity.TTTranferEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/21 14:47
 */
@FeignClient(name = "navigator-trade-service")
public interface TtTranferFacade {

    /**
     * 新增 ttTranferEntity
     *
     * @param ttTranferEntity
     * @return
     */
    @PostMapping("/saveTtTranfer")
    boolean saveTtTranfer(@RequestBody TTTranferEntity ttTranferEntity);

    @GetMapping("/getTransferByTtId")
    TTTranferEntity getTransferByTtId(@RequestParam("ttId") Integer ttId);

    @GetMapping("/getTTTranferByPriceApplyId")
    List<TTTranferEntity> getTTTranferByPriceApplyId(@RequestParam(value = "priceApplyId") Integer priceApplyId);

    @GetMapping("/selectTTTranferByTTId")
    TTTranferEntity selectTTTranferByTTId(@RequestParam(value = "TTId") Integer TTId);

    @GetMapping("/selectTTTranferByPriceAllocateId")
    List<TTTranferEntity> selectTTTranferByPriceAllocateId(@RequestParam(value = "priceAllocateId") Integer priceAllocateId);
}
