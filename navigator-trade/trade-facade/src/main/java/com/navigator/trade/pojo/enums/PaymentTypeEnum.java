package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaymentTypeEnum {
    /**
     * 交易类型
     *
     * @Link
     */
    CREDIT(1, "赊销"),
    IMPREST(2, "预付"),

    ;

    private int type;
    private String desc;

    public static PaymentTypeEnum getByType(Integer type) {
        if (null == type) return PaymentTypeEnum.CREDIT;
        for (PaymentTypeEnum typeEnum : PaymentTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return PaymentTypeEnum.CREDIT;
    }

    public static PaymentTypeEnum getByDesc(String desc) {
        if (null == desc) return PaymentTypeEnum.CREDIT;
        for (PaymentTypeEnum typeEnum : PaymentTypeEnum.values()) {
            if (desc.equals(typeEnum.getDesc())) {
                return typeEnum;
            }
        }
        return PaymentTypeEnum.CREDIT;
    }

    public Integer getValue() {
        return this.getType();
    }

    public static String getDescByValue(Integer value) {
        return getByType(value).desc;
    }
}
