package com.navigator.trade.pojo.dto.contract;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 解约定赔的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@Accessors(chain = true)
public class ContractWashOutDTO {
    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 采销类型
     */
    private Integer salesType;

    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 合同状态
     */
    private Integer contractStatus;

    /**
     * 解约定赔数量
     */
    private BigDecimal washOutNum;

    /**
     * 期货金额
     */
    private BigDecimal forwardPrice;

    /**
     * 基差价格
     */
    private BigDecimal extraPrice;

    /**
     * 期货合约
     */
    private String domainCode;

}