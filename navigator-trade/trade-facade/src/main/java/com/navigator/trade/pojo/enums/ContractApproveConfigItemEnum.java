package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContractApproveConfigItemEnum {

    /**
     * 审批规则
     */
    MIN_AMOUNT(1, "20000000","#MIN_AMOUNT#","总金额下限阈值","万元"),
    MAX_AMOUNT(1, "50000000","#MAX_AMOUNT#","总金额上限阈值","万元"),
    DELIVERY_DUE_MONTH(1, "12","#DELIVERY_DUE_MONTH#","交期阈值","个月"),
    REMAIN_CONTRACT_NUMBER(1, "32","#REMAIN_CONTRACT_NUMBER#","可提量阈值","吨"),
/*    P_MIN_AMOUNT(1, "20000000","#MIN_AMOUNT#"),
    P_MAX_AMOUNT(1, "50000000","#MAX_AMOUNT#"),
    P_DELIVERY_DUE_MONTH(1, "12","#DELIVERY_DUE_MONTH#"),
    P_REMAIN_CONTRACT_NUMBER(1, "32","#REMAIN_CONTRACT_NUMBER#"),*/
    ;

    private Integer valueType;
    private String defaultValue;
    private String placeholder;
    private String code;
    private String unit;


    public static ContractApproveConfigItemEnum getByName(String name) {
        for (ContractApproveConfigItemEnum e : ContractApproveConfigItemEnum.values()) {
            if (name.equals(e.name())) {
                return e;
            }
        }
        return null;
    }
}
