package com.navigator.trade.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.util.BigDecimalSerializer;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/2 15:54
 */
@Data
@Accessors(chain = true)
public class ContractVO {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "合同状态（1、待提交审批 2、待出具 3、待审核 4、待盖章 5、待回传 6、待确认合规 8、执行中 15、已取消）")
    private Integer status;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Split（变更）3、Transfer（变更包含转厂执行） 4、resale（回购再重售）5、washout（解约定赔）")
    private Integer tradeType;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @Excel(name = "交割库Id")
    private String shipWarehouseId;

    @Excel(name = "交割库")
    private String shipWarehouseName;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "品种名称")
    private String categoryName;

    @ApiModelProperty(value = "包装规格")
    private String goodsSpecId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 点价合同）")
    private Integer contractType;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "基差价（元）")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "是否定价完成")
    private Integer priceComplete;

    @ApiModelProperty(value = "可以变更数量")
    private BigDecimal canAllocateOfContract;

    @ApiModelProperty(value = "合同已经分配数量")
    private BigDecimal sumPriceAllocateOfContract;

    @ApiModelProperty(value = "含税单价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "应付履约保证金")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "是否需要正本（0 不需要 1 需要）")
    private Integer needOriginalPaper;

    @ApiModelProperty(value = "履约保证金比例 " +
            "(系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
    private Integer depositRate;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "合同负责人")
    private Integer ownerId;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "作价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）")
    private Integer approvalStatus;

    @ApiModelProperty(value = "签章状态 1:发起成功 2:待LDC签章(参与者正在处理信封<回调签章链接>) 3:待LDC签章完成(参与者确认)" +
            "4:待客户签章(参与者正在处理信封<回调签章链接>) 5:客户确认签章(参与者确认)")
    private Integer signatureStatus;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "待签章链接Url")
    private String signatureUrl;

    // 重量质检
    @ApiModelProperty(value = "重量质检")
    private String weightCheckName;

    // 交提货方式
    @ApiModelProperty(value = "交提货方式")
    private String deliveryTypeName;

    // 发货库点
    @ApiModelProperty(value = "发货库点")
    private String factoryWarehouseName;

    // 目的地名
    @ApiModelProperty(value = "目的地名")
    private String destinationName;

    //品类编码
    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    // 合同所属商务姓名
    @ApiModelProperty(value = "合同所属商务姓名")
    private String businessPersonName;

    // 合同模板url地址
    @ApiModelProperty(value = "合同模板url地址")
    private String contractPdfOriginalUrl;

    // ldc合同
    @ApiModelProperty(value = "ldc合同")
    private String ldcContractUrl;

    // 客户合同
    @ApiModelProperty(value = "客户合同")
    private List<FileInfoEntity> customerContractUrl;

    // 审核组员工id集合
    @ApiModelProperty(value = "审核组员工id集合")
    private List<Integer> approveIds;

    private CustomerDTO customerDTO;

    private EmployEntity createdEntity;

    private ContractStructureEntity contractStructureEntity;

    @ApiModelProperty(value = "未开单量")
    private BigDecimal notBillNum;

    @ApiModelProperty(value = "未提货量")
    private BigDecimal notDeliveryNum;

    @ApiModelProperty(value = "已定价量")
    private BigDecimal priceNum;

    @ApiModelProperty(value = "未定价量")
    private BigDecimal notPriceNum;

    @ApiModelProperty(value = "最新一条tt")
    private Integer latestTtId;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private String paymentTypeName;

    @ApiModelProperty(value = "付款条件代码ID")
    private Integer payConditionId;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    private ContractStructureDTO contractStructureVO;

    @ApiModelProperty(value = "操作人Id")
    private String operatorId;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date operatorTime;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee = BigDecimal.ZERO;

    @ApiModelProperty(value = "已反点价次数")
    private Integer reversedPriceTimes;

    @ApiModelProperty(value = "蛋白价差")
    private BigDecimal proteinDiffPrice;

    // 是否展示反点价
    private Integer isShowReversePrice;

    // 是否展示定价完成
    private Integer isShowPriceComplete;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体简称")
    private String companyName;

    @ApiModelProperty(value = "是否关闭尾量")
    private Integer isCloseTailNum;

    /**
     * 仓单的字段信息
     */

    @ApiModelProperty(value = "合同性质")
    private Integer contractNature;

    @ApiModelProperty(value = "仓单ID/列表跳转会用到")
    private String warrantId;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "注销数量")
    private BigDecimal warrantCancelCount;

    @ApiModelProperty(value = "可注销量")
    private BigDecimal canCancelCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "合同注销状态（1、未注销 2.注销中 3.已注销）")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "注销开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

    @ApiModelProperty(value = "备注|合同注销备注")
    private String memo;

    @ApiModelProperty(value = "提货密码")
    private String deliveryPassword;

    @ApiModelProperty(value = "注销日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDate;

    @ApiModelProperty(value = "提货客户名称")
    private String deliveryCustomerName;

}
