package com.navigator.trade.pojo.dto.contractsign;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.navigator.bisiness.enums.TTTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同出具
 *
 * <AUTHOR>
 * @since 2022-01-21 18:00
 */
@Data
@Accessors(chain = true)
public class ContractSignCreateDTO {

    /**
     * 协议ID
     */
    private Integer contractSignId;
    /**
     * 合同ID
     */
    private Integer contractId;
    /**
     * 合同编码
     */
    private String contractCode;
    /**
     * ttID
     */
    private Integer ttId;
    /**
     * TT编号
     */
    private String ttCode;
    /**
     * {@link TTTypeEnum}
     * TT类型（1、销售合同新增；2、销售合同修改；3、销售合同拆分 4、转月5、反点价  6、点转反定价单 7、主体变化）
     */
    private Integer ttType;
    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer tradeType;
    /**
     * belongCustomerId
     */
    private Integer belongCustomerId;
    /**
     * 合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）
     */
    private Integer contractType;
    /**
     * 是否需要正本（0 不需要 1 需要）
     */
    private Integer needOriginalPaper;
    /**
     * 合同销售类型（1.采购 2.销售）
     */
    private Integer salesType;
    /**
     * 买方客户ID
     */
    private Integer customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 原始买方客户ID（非0则为变更客户主体）
     */
    private Integer originalCustomerId;
    /**
     * 卖方客户ID
     */
    private String supplierId;
    /**
     * 卖方客户名称
     */
    private String supplierName;
    /**
     * 卖方主体收款账号信息
     */
    private Integer supplierAccount;
    /**
     * 签章类型（1线下、2 客户单签、3、双签）
     */
    private String signatureType;

    /**
     * 本次转月手续费
     */
    private BigDecimal thisTimeFee;
    /**
     * 签章类型（1易企签、2 文件上传）
     */
    private String signatureWay;
    /**
     * 签章状态 1:发起成功 2:待LDC签章(参与者正在处理信封【回调签章链接】) 3:LDC签章完成(参与者确认) 4:待客户签章(参与者正在处理信封【回调签章链接】) 5:客户确认签章(参与者确认) 6、文件上传完成
     */
    private Integer signatureStatus;
    /**
     * 期货合约
     */
    private String domainCode;
    /**
     * 货品ID
     */
    private Integer goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 品种
     */
    private Integer goodsCategoryId;
    /**
     * 合同负责人
     */
    private Integer ownerId;
    /**
     * 备注
     */
    private String memo;
    /**
     * 基差价格
     */
    private BigDecimal extraPrice;
    /**
     * ldc是否需要正本（0.不需要 1.需要）
     */
    private Integer ldcNeedOriginalPaper;
    /**
     * 是否是ldc模板（0.nonframe 1.是）
     */
    private Integer ldcFrame;

    /**
     * 交货工厂编码
     */
    private String deliveryFactoryCode;

    /**
     * 交货工厂名称
     */
    private String deliveryFactoryName;


    @ApiModelProperty(value = "协议类型:0.补充协议 1.尾量终止")
    private Integer signType;

    private Integer companyId;

    private String companyName;

    @ApiModelProperty(value = "变更合同量")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "开始交货时间")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "签订日期")
    private Date signDate;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    /**
     * 很多状态现在从数据转换进行处理
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "出具方式(1不出具;2文件上传;3数字合同）")
    private String provideType;

    @ApiModelProperty(value = "审核方式（需审核 不审核）")
    private String needReview;

    @ApiModelProperty(value = "ldc签章方式（不签章 线下签 易企签-标准 易企签-静默 ）")
    private String ldcSignatureType;

    @ApiModelProperty(value = "客户签章方式（不签章 线下签 易企签）")
    private String customerSignatureType;

    @ApiModelProperty(value = "终止方式:0.补充协议 1.尾量终止")
    private String termineType;

    @ApiModelProperty(value = "ws类型(0使用ws;1非ws但系统;非系统2)")
    private String wsType;

    @ApiModelProperty(value = "协议类型")
    private String contractSignType;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    /**
     * V3新增的字段信息
     */
    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "品种代码")
    private String categoryCode;
    //品类编码
    @ApiModelProperty(value = "品类编码")
    private String futureCode;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "是否注销生成协议")
    private Integer isWriteOff;

    @ApiModelProperty(value = "货品昵称")
    private String commodityName;
}
