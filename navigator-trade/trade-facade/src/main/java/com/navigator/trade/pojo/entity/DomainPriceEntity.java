package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.AuditStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 期货价格表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
@Accessors(chain = true)
@TableName("dbt_domain_price")
@ApiModel(value = "DomainPriceEntity对象", description = "期货价格表")
public class DomainPriceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    @ApiModelProperty(value = "交易日期")
    private Date tradeDate;

    @ApiModelProperty(value = "交易价格")
    private BigDecimal price;

    @ApiModelProperty("主力合约")
    private String domainCode;

    /**
     * {@link AuditStatusEnum}
     */
    @ApiModelProperty(value = "审核状态")
    private Integer status;

    @ApiModelProperty(value = "来源附件")
    private String sourceFileName;

    @ApiModelProperty(value = "来源附件路径")
    private String sourceFilePath;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "是否删除")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;
}
