package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractStructurePricingStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ContractStructureDTO extends ContractStructureEntity {

    private String structureTypeName;
    private String priceStatusName;
    /**
     * totalNum-dealNum-notDealNum
     */
    private BigDecimal remainNum;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    public String getPriceStatusName() {
        return ContractStructurePricingStatusEnum.getByValue(getPriceStatus()).getDesc();
    }

    public BigDecimal getRemainNum() {
        return getTotalNum();
    }
}
