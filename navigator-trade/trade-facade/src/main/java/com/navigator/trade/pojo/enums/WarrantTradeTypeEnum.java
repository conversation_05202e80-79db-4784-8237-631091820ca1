package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 仓单交易类型 枚举1.交易所交割仓单 2.线下交易所仓单 3.交易所仓单交易平台
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */

@Getter
@AllArgsConstructor
public enum WarrantTradeTypeEnum {
    /**
     * 仓单交易类型
     */
    ONLINE_TRADE(1, "交易所交割仓单"),
    OFFLINE_TRADE(2, "线下交易所仓单合同"),
    ONLINE_TRADE_PLATFORM(3, "交易所仓单交易平台");

    Integer value;
    String desc;

    public static WarrantTradeTypeEnum getByValue(Integer value) {
        for (WarrantTradeTypeEnum statusEnum : WarrantTradeTypeEnum.values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return WarrantTradeTypeEnum.ONLINE_TRADE;
    }

}
