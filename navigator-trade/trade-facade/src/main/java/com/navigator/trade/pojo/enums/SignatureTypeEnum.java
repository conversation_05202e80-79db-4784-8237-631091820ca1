package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 17:14
 */
@Getter
@AllArgsConstructor
public enum SignatureTypeEnum {

    /**
     * 签章类型
     */
    OFFLINE(1,"线下"),
    ONE_SIGNATURE(2,"客户单签"),
    BOTH_SIGNATURE(3,"双签"),
    LDC_ONE_SIGNATURE(4,"LDC单签"),
    OPEN_SIGN_GROUP(5,"开启签章组"),
    ;

    private int value;
    private String desc;

    public static SignatureTypeEnum getByValue(int value) {
        for (SignatureTypeEnum typeEnum : SignatureTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return SignatureTypeEnum.BOTH_SIGNATURE;
    }

}
