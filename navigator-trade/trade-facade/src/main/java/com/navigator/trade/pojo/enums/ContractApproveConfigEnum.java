package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ContractApproveConfigEnum {
    /**
     * 审批规则
     */
    BEAN_A(11,"A","1000"),
    OIL_A(12,"A","500"),
    BEAN_B (11,"B","25000000"),
    OIL_B (12,"B","20000000"),
    BEAN_C(11,"C","50000000"),
    OIL_C(12,"C","50000000"),

    ;
    /**
     * type 品类 11 豆粕 12 豆油
     */
    private Integer type;
    private String approvalType;
    private String value;


    public static BigDecimal getValueByType(Integer type,String approvalType) {
        for (ContractApproveConfigEnum contractApproveConfigEnum : ContractApproveConfigEnum.values()) {
            if (Objects.equals(contractApproveConfigEnum.getApprovalType(), approvalType)&&Objects.equals(contractApproveConfigEnum.getType(), type)) {
                return new BigDecimal(contractApproveConfigEnum.getValue());
            }
        }
        return null;
    }
}
