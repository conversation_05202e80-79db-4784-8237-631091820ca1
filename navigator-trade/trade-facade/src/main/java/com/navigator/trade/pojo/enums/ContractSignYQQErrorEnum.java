package com.navigator.trade.pojo.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContractSignYQQErrorEnum {

    SUCCEED("100550001","系统提示：易企签请求处理中,请刷新后再试"),
    A_SUCCESSFUL("100600000","签章发起成功"),
    LACK_PHONE("100550236","缺少发送方联系方式"),
    INVALID_PHONE("100550251","无效的中国手机号格式"),
    LACK_NAME("100550241","接收方名字长度不合法，名字不能为空且最大为32"),
    LACK_ENTERPRISE("100550100","缺少企业名字"),
    LACK_SIGN_PERSON("100550240","缺少签章人"),
    CREATE_FAILURE("100550341","创建信封失败"),
    SIGN_FAILED("100550100", "易企签调用失败"),
    NOT_AUTH("200000001", "客户未实名或未开通易企签服务"),
    NOT_CUSTOMER("300000001", "客户没有签章账号"),
    ACCOMPLISH("300000002", "签章流程完成"),
    MAIL_SEND_SUCCEED("300000003", "邮件已发送"),
    ;


    private String code;
    private String Message;
}
