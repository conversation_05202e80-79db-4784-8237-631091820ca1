package com.navigator.trade.pojo.dto.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-06 17:14
 **/
@Data
@Accessors(chain = true)
public class ContractRelativeDTO {
    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private Integer contractId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
    /**
     * 交货工厂
     */
    @ApiModelProperty(value = "交货工厂")
    private String factoryCode;
    @ApiModelProperty(value = "账套")
    private String siteName;
    /**
     * 合同数量
     */
    @ApiModelProperty(value = "创建时间")
    private String contractNum;

    /**
     * 父合同ID
     */
    @ApiModelProperty(value = "父合同ID")
    private Integer parentId;
    /**
     * 父合同编号
     */
    @ApiModelProperty(value = "父合同编号")
    private String parentContractCode;
    /**
     * 父合同数量
     */
    @ApiModelProperty(value = "父合同数量")
    private String parentContractNum;
    /**
     * 父合同交货工厂
     */
    @ApiModelProperty(value = "父合同交货工厂")
    private String parentFactoryCode;

    @ApiModelProperty(value = "父合同账套")
    private String parentSiteName;
    /**
     * 合同来源
     */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String contractSourceInfo;
    /**
     * 交易类型
     */
    @ApiModelProperty(value = "创建时间")
    private Integer tradeType;
    /**
     * 交易类型描述
     */
    @ApiModelProperty(value = "交易类型描述")
    private String tradeTypeInfo;
    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    private String modifyFieldInfo = "";
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "TT内容")
    private TradeTicketEntity tradeTicketEntity;

    @ApiModelProperty(value = "原合同ID")
    private Integer originalContractTtId;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;
}
