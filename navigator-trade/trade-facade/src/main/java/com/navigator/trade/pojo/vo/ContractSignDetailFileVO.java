package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-01-26 18:32
 */
@Data
@Accessors(chain = true)
public class ContractSignDetailFileVO {
    /**
     * 协议ID
     */
    private Integer contractSignId;
    /**
     * 协议编号
     */
    private String protocolCode;
    /**
     * 合同id
     */
    private String contractId;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * TT_ID
     */
    private Integer ttId;
    /**
     * TT编号
     */
    private String ttCode;
    /**
     * 协议附件地址
     */
    private String fileUrl;
    /**
     * 附件状态
     */
    private Integer fileStatus;
    /**
     * 文件类型
     */
    private Integer fileType;
    /**
     * 文件后缀（.png）
     */
    private String fileExtension;
    /**
     * 签章状态
     */
    private String signatureStatus;
    /**
     * 失效原因
     */
    private String invalidReason;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    private Integer colNotShow =0;
}
