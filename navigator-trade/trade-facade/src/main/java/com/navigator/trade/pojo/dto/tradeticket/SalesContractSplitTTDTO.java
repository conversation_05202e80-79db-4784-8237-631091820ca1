package com.navigator.trade.pojo.dto.tradeticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.trade.pojo.enums.ContractSignTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SalesContractSplitTTDTO extends TTCommonDTO {
    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "类型：2、Revise（修改）3、Split（拆分）")
    private Integer type;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "子合同ID")
    private Integer sonContractId;

    @ApiModelProperty(value = "根合同ID")
    private Integer rootContractId;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;

    @ApiModelProperty(value = "客户看到的合同状态（待定）")
    private Integer customerStatus;

    @ApiModelProperty(value = "是否需要正本（0 不需要 1 需要）")
    private Integer needOriginalPaper;

    @ApiModelProperty(value = "正本状态")
    private Integer originalPaperStatus;

    @ApiModelProperty(value = "待签章链接Url")
    private String signatureUrl;

    @ApiModelProperty(value = "签章类型（客户：线下和易企签）")
    private String signatureType;

    @ApiModelProperty(value = "签订地（卖方主体）")
    private String signPlace;

    @ApiModelProperty(value = "签章状态 1:发起成功 2:待LDC签章(参与者正在处理信封<回调签章链接>) 3:待LDC签章完成(参与者确认)" + "4:待客户签章(参与者正在处理信封<回调签章链接>) 5:客户确认签章(参与者确认)")
    private Integer signatureStatus;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Split（变更）3、Transfer（变更包含转厂执行） 4、resale（回购再重售）5、washout（解约定赔）")
    private Integer tradeType;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "计量单位（TT只有吨，涉及物流会有袋件数等）")
    private String weightUnit;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "质量检验（根据客户属性定）")
    private String qualityCheck;

    @ApiModelProperty(value = "币种（系统默认为CNY）")
    private String currencyType;

    @ApiModelProperty(value = "含税单价（系统默认由以下字段输入数字相加而来： 基差价 期货价格 蛋白价差 散粕补贴 期权费 其他物流费用 运费。起吊费用；达孚待付，客户自付。滞期费。高温费 回购折价 客诉折价 转厂补贴 其他补贴 商务补贴 手续费 DCE交割：只有交割结算价）")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal baseDiffPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "税率（1.增值税专用发票9% 2.增值税专用发票10% 3.增值税专用发票12%） 税率根据品种自动带出（需单独模块维护）发票类型，可选")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "暂定价")
    private BigDecimal temporaryPrice;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "已提总量（吨）")
    private BigDecimal totalDeliveryNum;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "应付履约保证金")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "追加履约保证金 一口价默认是5%【下跌】基差150 【下跌】 一口价暂定价5%【上涨也会收】延期定价 默认>0【上涨收】")
    private BigDecimal addedDeposit;

    @ApiModelProperty(value = "履约保证金比例 (系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositReleaseType;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    private BigDecimal delayPayFine;

    @ApiModelProperty(value = "是否代加工，默认否（0:否 1:是）")
    private Integer oem;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;


    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "是否安排运输")
    private Integer isArrangeTransport;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    private Integer weightCheck;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "作价开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date priceStartTime;

    @ApiModelProperty(value = "作价截止时间")
    //@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String priceEndTime;

    @ApiModelProperty(value = "合同负责人")
    private Integer ownerId;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否是stf合同（0.否 1.是）")
    private Integer isStf;

    @ApiModelProperty(value = "stf合同号")
    private String customerContractCode;

    @ApiModelProperty(value = "ldc是否需要正本（0.不需要 1.需要）")
    private Integer ldcNeedOriginalPaper;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "是否是ldc模板（0.nonframe 1.是）")
    private Integer ldcFrame;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "修改内容Json")
    private String modifyContent;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    private String userId;

    @ApiModelProperty(value = "tt类型")
    private Integer ttType;

    private Integer supplierAccountId;

    @ApiModelProperty(value = "补充协议类型 -1:原合同TT不审批不生成协议   0: 普通拆分(新合同) 1: 数量变更补充协议(原合同)")
    private Integer addedSignatureType = 0;
    /**
     * {@link  com.navigator.trade.pojo.enums.ContractSignTypeEnum}
     */
    private Integer signType = ContractSignTypeEnum.ALL_SPLIT.getValue();
}
