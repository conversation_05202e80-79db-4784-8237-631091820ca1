package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/3
 */

@Getter
@AllArgsConstructor
public enum VoluntarilySignTypeEnum {
    /**
     * 易企签自动签署状态
     */
    NOT_START(0, "未签署"),
    NODE_START(1, "自动签署启动"),
    VOLUNTARILY_SIGN_FAILED(2, "自动签署发起失败"),
    SIGN_FAILED(3, "调用失败"),
    SUCCEED(4, "自动签署成功"),
    ;

    Integer value;
    String desc;
}
