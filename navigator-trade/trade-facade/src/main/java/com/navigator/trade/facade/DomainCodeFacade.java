package com.navigator.trade.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceAuditDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceLeadDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/20 11:37
 */
@FeignClient(name = "navigator-trade-service")
public interface DomainCodeFacade {

    /**
     * 获取当天收盘价
     *
     * @param categoryId
     * @param name
     * @param categoryCode
     * @return
     */
    @GetMapping("/getClosingPrice")
    Result getClosingPrice(@RequestParam(value = "categoryId", required = false) Integer categoryId, @RequestParam("name") String name, @RequestParam(value = "categoryCode", required = false) String categoryCode);

    /**
     * 预览，并分析收盘价Excel数据
     *
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping(value = "/previewDomainPrice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    List<DomainPriceLeadDTO> previewDomainPrice(@RequestPart("file") MultipartFile file);

    /**
     * 上传收盘价
     *
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping(value = "/uploadDomainPrice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result uploadDomainPrice(@RequestPart("file") MultipartFile file);

    /**
     * 查询主力合约价格列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/queryDomainPrice")
    Result queryDomainPrice(@RequestBody QueryDTO<QueryDomainPriceBO> queryDTO);

    /**
     * 审核收盘价信息
     *
     * @param domainPriceAuditDTO 审核信息
     * @return 审核结果
     */
    @PostMapping("/audit")
    Result audit(@RequestBody DomainPriceAuditDTO domainPriceAuditDTO);


    /**
     * 结构化定价收盘价列表
     *
     * @param domainPriceTodayDTO
     * @return 审核结果
     */
    @PostMapping("/queryDomainPriceToday")
    Result queryDomainPriceToday(@RequestBody DomainPriceTodayDTO domainPriceTodayDTO);

    /**
     * 获取最近的收盘价
     *
     * @param
     * @param
     * @param categoryCode
     * @return
     */
    @GetMapping("/getLastestClosingPrice")
    Result getLastestClosingPrice(@RequestParam(value = "categoryId", required = false) Integer categoryId, @RequestParam("domainCode") String domainCode, @RequestParam("signDate") Date signDate, @RequestParam("categoryCode") String categoryCode);
}
