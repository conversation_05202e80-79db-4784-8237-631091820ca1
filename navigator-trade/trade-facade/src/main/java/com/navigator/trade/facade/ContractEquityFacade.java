package com.navigator.trade.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contractEquity.ContractChangeEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 合同权益变更的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractEquityFacade {

    @PostMapping("/contract/getChangeContractEquityList")
    Result getChangeContractEquityList(@RequestBody QueryDTO<ContractEquityQueryDTO> queryDTO);

    @PostMapping("/contract/changeContractEquity")
    Result changeContractEquity(@RequestBody ContractChangeEquityDTO changeEquityDTO);

    @GetMapping("/contract/getChangeEquityDetailByApplyCode")
    Result getChangeEquityDetailByApplyCode(@RequestParam("applyCode") String applyCode);

    @GetMapping("/contract/getChangeEquityByContractCode")
    Result getChangeEquityByContractCode(@RequestParam("contractCode") String contractCode);

    @GetMapping("/contract/getChangeContractEquityRecord")
    Result getChangeContractEquityRecord(@RequestParam("contractCode") String contractCode);

    @GetMapping("/contract/getChangeContractEquityDetailByNotApprove")
    List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(@RequestParam("contractId") Integer contractId);


}
