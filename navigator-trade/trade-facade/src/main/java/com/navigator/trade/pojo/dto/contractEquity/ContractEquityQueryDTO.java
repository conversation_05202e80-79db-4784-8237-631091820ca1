package com.navigator.trade.pojo.dto.contractEquity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContractEquityQueryDTO {

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体ID")
    private Integer supplierId;

    @ApiModelProperty(value = "买方集团名称")
    private String customerGroupName;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "开始提货日期")
    private String deliveryStartDate;

    @ApiModelProperty(value = "结束提货日期")
    private String deliveryEndDate;

    @ApiModelProperty(value = "可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "可反点价次数")
    private Integer ableReversePriceTimes;

    @ApiModelProperty(value = "已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private Integer reversedPriceTimes;

    @ApiModelProperty(value = "最近审批状态")
    private Integer lastApprovalStatus;

    @ApiModelProperty(value = "最近变更人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最近变更时间")
    private String lastUpdatedAt;

    @ApiModelProperty(value = "最近变更申请（申请变更的编号）")
    private String lastApplyCode;

}
