package com.navigator.trade.pojo.dto;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import lombok.Data;

@Data
public class TradeSupportDTO {
    SiteEntity siteEntity;
    CustomerDTO customerDTO;
    CustomerDTO supplierDTO;
    SkuEntity skuEntity;
    WarehouseEntity warehouseEntity;
    CustomerDetailEntity customerDetailEntity;
}
