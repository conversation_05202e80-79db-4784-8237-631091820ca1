package com.navigator.trade.pojo.dto.contractEquity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ContractChangeEquityDTO {

    @ApiModelProperty(value = "权益变更合同集合")
    private List<ContractEquityDTO> contractEquityDTOList;

    @ApiModelProperty(value = "申请说明")
    private String applyRemark;


}
