package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class TTDetailVO {

    // 对应的是TradeTicketEntity 和 TT子表
    private TTQueryDetailVO ttQueryDetailVO;

    private List<CompareObjectDTO> compareObjectDTOList;
    // 对应ContractPriceEntity
    private TTPriceDetailVO ttPriceDetailVO;

    private TTQueryStructureVO ttQueryStructureVO;
    // TT主表
    private TradeTicketEntity tradeTicketEntity;

    /**
     * 后端返回类型标识
     * 0:ttQueryDetailVO 1:compareObjectDTO 2:点价实体 3:解约定赔详情 4:合同关闭详情 5:合同作废详情 6:结构化定价详情
     * 7: 拆分新增类型 8: 拆分list类型 9:修改新增类型 10: 修改list类型 11:全部转月
     */
    private String detailType;


    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "协议状态")
    private String protocolStatus;

    @ApiModelProperty(value = "tt状态")
    private String ttStatus;

    @ApiModelProperty(value = "修改时间")
    private String updateTime;

    @ApiModelProperty(value = "tt编号")
    private String ttCode;

    @ApiModelProperty(value = "ttId")
    private Integer ttId;

    @ApiModelProperty(value = "ttType")
    private Integer ttType;

    @ApiModelProperty(value = "tradeType")
    private Integer tradeType;

    @ApiModelProperty(value = "ttTypeInfo")
    private String tradeTypeInfo;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同Id")
    private Integer contractId;

    @ApiModelProperty(value = "协议编号")
    private String contractSignCode;

    @ApiModelProperty(value = "协议Id")
    private Integer contractSignId;

    @ApiModelProperty(value = "协议编号")
    private String protocolCode;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "申请单号")
    private String priceApplyCode;

    @ApiModelProperty(value = "申请单号Id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "定价时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date priceApplyTime;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "原合同类型")
    private Integer originContractType;

    @ApiModelProperty(value = "原合同数量")
    private BigDecimal originContractNum;

    @ApiModelProperty(value = "来源类型: 1.保存 2.提交（默认）")
    private Integer sourceType;

    @ApiModelProperty(value = "二级品种名")
    private String category2Name;

    @ApiModelProperty(value = "三级品种名")
    private String category3Name;
}
