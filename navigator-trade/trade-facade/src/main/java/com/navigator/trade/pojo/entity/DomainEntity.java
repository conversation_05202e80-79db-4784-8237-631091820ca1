package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 期货合约表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
@Accessors(chain = true)
@TableName("dbt_domain")
@ApiModel(value="DomainEntity对象", description="期货合约表")
public class DomainEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "期货合约名称")
    private String name;

    @ApiModelProperty(value = "主力合约日期")
    private Date dateCode;

    @ApiModelProperty(value = "品种id")
    private Integer categoryId;

    @ApiModelProperty(value = "品种名称")
    private String categoryName;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;


}
