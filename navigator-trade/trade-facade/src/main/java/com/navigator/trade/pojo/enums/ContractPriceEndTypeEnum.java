package com.navigator.trade.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 点价截止日期类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-2
 */
@Getter
public enum ContractPriceEndTypeEnum {
    /**
     * 点价截止日期类型
     */
    DATE(1, "时间"),
    TEXT(2, "文本");
    int value;
    String desc;

    ContractPriceEndTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractPriceEndTypeEnum getByValue(Integer value) {

        for (ContractPriceEndTypeEnum priceEndTypeEnum : ContractPriceEndTypeEnum.values()) {
            if (priceEndTypeEnum.getValue() == value) {
                return priceEndTypeEnum;
            }
        }
        return ContractPriceEndTypeEnum.DATE;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
