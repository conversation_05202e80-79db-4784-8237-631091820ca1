package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TTStatusEnum {
    /**
     * TT状态
     */
    NEW(1, "新录入"),
    APPROVING(2, "审批&协议"),
    DONE(3, "已完成"),
    WAITING(4, "待修改提交"),
    INVALID(5, "已作废"),
    ;

    int type;
    String desc;

    public static TTStatusEnum getByValue(int value) {
        for (TTStatusEnum statusEnum : TTStatusEnum.values()) {
            if (value == statusEnum.getType()) {
                return statusEnum;
            }
        }
        return TTStatusEnum.NEW;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
