package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 待办审批记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
@Data
@Accessors(chain = true)
@TableName("dbz_approval_record")
@ApiModel(value = "ApprovalRecordEntity对象", description = "待办审批记录表")
public class ApprovalRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "db_approval表ID")
    private String approvalId;

    @ApiModelProperty(value = "审批编号（1001 TT审批 1002 合同审批）")
    private String approvalKey;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "发起人ID")
    private Integer sponsorId;

    @ApiModelProperty(value = "发起人姓名")
    private String sponsorName;

    @ApiModelProperty(value = "审批人ID")
    private Integer approverId;

    @ApiModelProperty(value = "审批人姓名")
    private String approverName;

    @ApiModelProperty(value = "业务状态")
    private Integer status;

    @ApiModelProperty(value = "审批结果状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批备注/留言")
    private String memo;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;


}
