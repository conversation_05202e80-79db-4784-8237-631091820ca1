package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 定价单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("dbt_tt_price")
@ApiModel(value = "TtPriceEntity对象", description = "定价单表")
public class TTPriceEntity extends TTSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /*
    * DELETE @20240807 NEO FOR:MOVE TO PARENT CLASS
    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    */

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "申请id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "分配单id(收盘价记录ID)")
    private Integer allocateId;

    @ApiModelProperty(value = "源合同ID(合同变更、点价申请等来源ID)")
    private Integer sourceContractId;

    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价 4.定价 5.结构化定价）")
    private Integer type;

    @ApiModelProperty(value = "是否被驳回 0:否 1:是")
    private Integer contraryStatus;

    @ApiModelProperty(value = "定价量")
    private BigDecimal num;

    @ApiModelProperty(value = "剩余未点价量")
    private BigDecimal remainPriceNum;

    @ApiModelProperty(value = "原点价量")
    private BigDecimal originalPriceNum;

    @ApiModelProperty(value = "已定价量")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "原合同暂定价")
    private BigDecimal tempPrice;

    @ApiModelProperty(value = "原合同含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "原合同基差价")
    private BigDecimal diffPrice;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "定价价格/价差")
    private BigDecimal price;

    @ApiModelProperty(value = "定价时间")
    private Date priceTime;

    @ApiModelProperty(value = "备注（原暂定价-基差）")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "货品")
    private Integer goodsId;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "定价单变更源ID")
    private Integer sourceId;

    @ApiModelProperty(value = "加权平均价")
    private BigDecimal avePrice;

    @ApiModelProperty(value = "最终合同价格")
    private BigDecimal endContractPrice;

    @ApiModelProperty(value = "最终全额货款")
    private BigDecimal endAllPrice;

    @ApiModelProperty(value = "价格明细")
    private String contractPriceDetail;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "作价截止时间")
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String priceEndTime;

    @ApiModelProperty(value = "当前合同数量")
    private BigDecimal thisContractNum;
}
