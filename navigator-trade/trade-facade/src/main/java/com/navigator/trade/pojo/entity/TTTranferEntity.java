package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * TT-转月、反点价
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_tt_tranfer")
@ApiModel(value = "TtTranferEntity对象", description = "TT-转月、反点价")
public class TTTranferEntity extends TTSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /*
    * DELETE @20240807 NEO FOR:MOVE TO PARENT CLASS
    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    */

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "源合同ID(合同变更来源ID)")
    private Integer sourceContractId;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "变更类型（1、转月 2 部分转月 3、反点价4、部分反点价）")
    private Integer type;

    @ApiModelProperty(value = "是否被驳回 0:否 1:是")
    private Integer contraryStatus;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "原合约")
    private String originalDomainCode;

    @ApiModelProperty(value = "当前合约")
    private String domainCode;

    @ApiModelProperty(value = "原合同暂定价")
    private BigDecimal tempPrice;

    @ApiModelProperty(value = "原合同基差价")
    private BigDecimal diffPrice;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "定价价格/价差")
    private BigDecimal price;

    @ApiModelProperty(value = "备注（原暂定价-基差）")
    private String memo;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "修改内容Json")
    private String modifyContent;

    @ApiModelProperty(value = "品种Id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "货品")
    private Integer goodsId;

    @ApiModelProperty(value = "买方客户Id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "修改前后字段")
    private String content;

    @ApiModelProperty(value = "申请单Id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "分配单id")
    private Integer priceAllocateId;

    // V1 新增本次手续费，价格JSON Author:zengshl 2024-06-18 start

    @ApiModelProperty(value = "本次手续费")
    private BigDecimal thisFee;
    // V1 新增本次手续费，价格JSON Author:zengshl 2024-06-18 end

}
