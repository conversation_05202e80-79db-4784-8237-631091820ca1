package com.navigator.trade.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 合同审核状态 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Getter
public enum ContractReviewStatusEnum {
    /**
     * 合同审核状态
     */
    PASS(0, "通过"),
    REJECT(1, "驳回");
    int value;
    String desc;

    ContractReviewStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractReviewStatusEnum getByValue(Integer value) {

        for (ContractReviewStatusEnum reviewStatusEnum : ContractReviewStatusEnum.values()) {
            if (reviewStatusEnum.getValue() == value) {
                return reviewStatusEnum;
            }
        }
        return ContractReviewStatusEnum.PASS;
    }
}
