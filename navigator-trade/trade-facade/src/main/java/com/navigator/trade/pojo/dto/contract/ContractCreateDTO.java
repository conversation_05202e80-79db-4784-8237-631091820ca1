package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 创建合同的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ContractCreateDTO extends ContractEntity {
    /**
     * 合同创建的来源【现货TT新增】【仓单分配 | 仓单转让】【仓单采购TT新增】
     * <p>
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同创建的来源【现货TT新增】【仓单分配 | 仓单转让】【仓单采购TT新增】")
    private Integer actionSource;

    @ApiModelProperty(value = "当前ttId")
    private Integer currentTtId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "TT交易类型")
    private Integer ttTradeType;

    @ApiModelProperty(value = "结构化DTO")
    private ContractStructureDTO contractStructureDTO;
}
