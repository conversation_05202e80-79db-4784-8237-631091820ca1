package com.navigator.trade.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_contract")
@ApiModel(value = "contract视图", description = "合同表")
public class ContractVOEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    // ===================基础信息====================
    @ApiModelProperty(value = "ID")
    private Integer id;

    @Excel(name = "合同编号", width = 25)
    private String contractCode;

    @Excel(name = "合同状态")
    private String status;

    @Excel(name = "签订日期", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd")
    private String signDate;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @Excel(name = "买方主体简称")
    private String customerName;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @Excel(name = "卖方主体简称")
    private String supplierName;

    @Excel(name = "交易类型")
    private String tradeType;

    @Excel(name = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "包装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格")
    private Integer goodsSpecId;

    @Excel(name = "期货合约")
    private String domainCode;

    @Excel(name = "货物Id")
    private Integer goodsId;

    @Excel(name = "货物")
    private String goodsName;

    @Excel(name = "总数量", numFormat = "0.000")
    private BigDecimal contractNum;

    @Excel(name = "袋皮扣重")
    private String packageWeightName;

    @Excel(name = "合同总金额", numFormat = "0.000")
    private BigDecimal totalAmount;

    @Excel(name = "付款方式")
    private String paymentType;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @Excel(name = "赊销账期", suffix = "天")
    private String creditDays;

    @Excel(name = "交货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty("交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "发货库点ID")
    private Integer shipWarehouseId;

    @Excel(name = "交货方式")
    private String deliveryTypeName;

    @Excel(name = "发货库点")
    private String shipWarehouseName;

    @Excel(name = "目的地")
    private String destinationName;

    @Excel(name = "溢短装", suffix = "%")
    private String weightTolerance;

    @Excel(name = "开始交货日", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd")
    private String deliveryStartTime;

    @Excel(name = "截止交货日", databaseFormat = "yyyy-MM-dd", format = "yyyy-MM-dd")
    private String deliveryEndTime;

    @Excel(name = "点价截止日期")
    private String priceEndTime;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @Excel(name = "已定价量", numFormat = "0.000")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    // ===================价格明细=========================

    @Excel(name = "不含税单价", numFormat = "0.000")
    private BigDecimal cifUnitPrice;

    @Excel(name = "Fob单价", numFormat = "0.000")
    private BigDecimal fobUnitPrice;

    @Excel(name = "含税单价", numFormat = "0.000")
    private BigDecimal unitPrice;

    @Excel(name = "基差价", numFormat = "0.000")
    private BigDecimal extraPrice;

    @Excel(name = "期货价格", numFormat = "0.000")
    private BigDecimal forwardPrice;

    @Excel(name = "出厂价", numFormat = "0.000")
    private BigDecimal factoryPrice;

    @Excel(name = "蛋白价差", numFormat = "0.000")
    private BigDecimal proteinDiffPrice;

    @Excel(name = "散粕补贴", numFormat = "0.000")
    private BigDecimal compensationPrice;

    @Excel(name = "期权费", numFormat = "0.000")
    private BigDecimal optionPrice;

    @Excel(name = "运费", numFormat = "0.000")
    private BigDecimal transportPrice;

    @Excel(name = "起吊费", numFormat = "0.000")
    private BigDecimal liftingPrice;

    @Excel(name = "滞期费", numFormat = "0.000")
    private BigDecimal delayPrice;

    @Excel(name = " 高温费", numFormat = "0.000")
    private BigDecimal temperaturePrice;

    @Excel(name = " 其他物流费", numFormat = "0.000")
    private BigDecimal otherDeliveryPrice;

    @Excel(name = " 回购折价", numFormat = "0.000")
    private BigDecimal buyBackPrice;

    @Excel(name = "客诉折价", numFormat = "0.000")
    private BigDecimal complaintDiscountPrice;

    @Excel(name = " 转厂补贴", numFormat = "0.000")
    private BigDecimal transferFactoryPrice;

    @Excel(name = "其他补贴", numFormat = "0.000")
    private BigDecimal otherPrice;

    @Excel(name = "商务补贴", numFormat = "0.000")
    private BigDecimal businessPrice;

    @Excel(name = "手续费", numFormat = "0.000")
    private BigDecimal fee;

    @Excel(name = "装运费单价", numFormat = "0.000")
    private BigDecimal shippingFeePrice;

    @Excel(name = "精炼价差", numFormat = "0.000")
    private BigDecimal refineDiffPrice;

    // ===================履约保证金=========================

    @Excel(name = "履约保证金比例", suffix = "%")
    private Integer depositRate;

    @Excel(name = "应付履约保证金金额", numFormat = "0.000")
    private BigDecimal depositAmount;

    @Excel(name = "履约保证金释放方式")
    private String depositReleaseType;

    @ApiModelProperty(value = "追加履约保证金(跌破)金额")
    private String addedDeposit;

    // ===================其他=========================
    @Excel(name = "代加工")
    private String oem;

    @Excel(name = "重量检验")
    private String weightCheckName;

    @Excel(name = "集团客户")
    private String enterpriseName;

    @ApiModelProperty(value = "供应商客户集团名称")
    private String supplierEnterpriseName;

    @Excel(name = "税率", numFormat = "0.00")
    private String taxRate;

    @Excel(name = "客户开票类型")
    private String invoiceType;

    @Excel(name = "STF")
    private String isStf;

    @Excel(name = "所属商务")
    private String businessPersonName;

    @Excel(name = "创建人")
    private String createByName;

    @Excel(name = "客户合同号")
    private String customerContractCode;

    @Excel(name = "创建时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @Excel(name = "标签")
    private String tagConfigIds;

    @Excel(name = "备注")
    private String memo;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "逻辑删除  0:未删除 1:删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "可反点价次数")
    private Integer ableReversePriceTimes;

    private Integer companyId;

    private String companyName;

    @ApiModelProperty("尾量关闭数量")
    private BigDecimal closeTailNum;

    // add zengshl 新增仓单的查询字段信息
    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

    @ApiModelProperty(value = "合同性质")
    private Integer contractNature;

    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;

    @ApiModelProperty(value = "合同注销状态（1、未注销 2.注销中 3.已注销）")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "仓单ID/列表跳转会用到")
    private String warrantId;

    @ApiModelProperty(value = "仓单编号/注册号")
    private String warrantCode;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "已注销量")
    private BigDecimal warrantCancelCount;

    @ApiModelProperty(value = "可注销量")
    private BigDecimal canCancelCount;

    @ApiModelProperty(value = "仓单持有量")
    private BigDecimal holdCount;

    @ApiModelProperty(value = "注销开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;
}
