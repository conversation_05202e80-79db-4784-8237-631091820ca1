package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.trade.pojo.enums.ContractReviewStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 合同协议审核
 *
 * <AUTHOR>
 * @since 2022-01-21 18:35
 */
@Data
@Accessors(chain = true)
public class ContractSignReviewDTO {
    /**
     * 协议ID
     */
    private Integer contractSignId;
    /**
     * 审核状态
     * {@link ContractReviewStatusEnum}
     */
    private Integer reviewStatus;

    /**
     * 审核备注
     */
    private String reviewRemark;
    /**
     * 协议ID
     */
    private Integer ttId;

    /**
     * 合同ID
     */
    private Integer contractId;
}
