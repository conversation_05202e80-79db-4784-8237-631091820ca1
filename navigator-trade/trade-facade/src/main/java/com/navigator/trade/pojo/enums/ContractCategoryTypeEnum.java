package com.navigator.trade.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 合同的品种类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Getter
public enum ContractCategoryTypeEnum {
    /**
     * 品种类型 1.现货 2.仓单 3.豆二
     */
    REGULAR(1, "现货"),
    DCE(2, "仓单"),
    SOYBEAN2(3, "豆二"),
    ;
    final int value;
    final String desc;

    ContractCategoryTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractCategoryTypeEnum getByValue(int value) {
        for (ContractCategoryTypeEnum typeEnum : ContractCategoryTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return ContractCategoryTypeEnum.REGULAR;
    }

}
