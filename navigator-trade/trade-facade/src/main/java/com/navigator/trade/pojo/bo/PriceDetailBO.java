package com.navigator.trade.pojo.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PriceDetailBO {

    //TODO NEO JASON BO是查询用还是业务处理用？建议调整
    //业务处理用 ContractPriceBaseEntity

    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货价")
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "蛋白价差")
    private BigDecimal proteinDiffPrice;

    @ApiModelProperty(value = "散粕补贴")
    private BigDecimal compensationPrice;

    @ApiModelProperty(value = "期权费")
    private BigDecimal optionPrice;

    @ApiModelProperty(value = "运费")
    private BigDecimal transportPrice;

    @ApiModelProperty(value = "起吊费")
    private BigDecimal liftingPrice;

    @ApiModelProperty(value = "滞期费")
    private BigDecimal delayPrice;

    @ApiModelProperty(value = " 高温费")
    private BigDecimal temperaturePrice;

    @ApiModelProperty(value = " 其他物流费")
    private BigDecimal otherDeliveryPrice;

    @ApiModelProperty(value = " 回购折价")
    private BigDecimal buyBackPrice;

    @ApiModelProperty(value = "客诉折价")
    private BigDecimal complaintDiscountPrice;

    @ApiModelProperty(value = " 转厂补贴")
    private BigDecimal transferFactoryPrice;

    @ApiModelProperty(value = "其他补贴")
    private BigDecimal otherPrice;

    @ApiModelProperty(value = "商务补贴")
    private BigDecimal businessPrice;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(value = "装运费单价")
    private BigDecimal shippingFeePrice;

    @ApiModelProperty(value = "精炼价差")
    private BigDecimal refineDiffPrice;

    @ApiModelProperty(value = "精炼/分提价差")
    private BigDecimal refineFracDiffPrice;

    @ApiModelProperty(value = "检验费")
    private BigDecimal surveyFees;

    @ApiModelProperty(value = "出厂费")
    private BigDecimal factoryPrice;

    @ApiModelProperty(value = "VE单价")
    private BigDecimal vePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "VE含量")
    private BigDecimal veContent = BigDecimal.ZERO;
}
