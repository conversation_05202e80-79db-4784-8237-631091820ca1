package com.navigator.trade.pojo.dto.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 变更合同的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class ContractModifyDTO {

    /**
     * 处理类型
     * {@link ProcessorTypeEnum}
     */
    @ApiModelProperty(value = "处理类型")
    private String processorType;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "TT类型")
    private Integer ttType;

    @ApiModelProperty(value = "TT交易类型")
    private Integer ttTradeType;

    @ApiModelProperty(value = "协议ID")
    private Integer signId;

    /**
     * 合同来源
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    @ApiModelProperty(value = "tt审批类型")
    private Integer approvalType;

    @ApiModelProperty(value = "定价单列表")
    private List<ConfirmPriceDTO> confirmPriceDTOList;

    @ApiModelProperty(value = "父合同实体")
    private ContractEntity parentContractEntity;

    // ===============可变更字段===============

    // ==================采购==================
    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方主体收款账号Id")
    private Integer supplierAccountId;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    private BigDecimal delayPayFine;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate = 0;

    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositReleaseType;

    // ==================销售==================

    @ApiModelProperty(value = "合同类型")
    private Integer sonContractType;

    @ApiModelProperty(value = "子合同id")
    private Integer sonContractId;

    @ApiModelProperty(value = "变更数量")
    private BigDecimal modifyNum;

    @ApiModelProperty(value = "点价截止日期")
    private String priceEndTime;

    @ApiModelProperty(value = "开始交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "买方主体")
    private Integer customerId;

    @ApiModelProperty(value = "包装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "品种：二级品类")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate = 0;

    @ApiModelProperty(value = "应付履约保证金金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式")
    private Integer paymentType;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "重量检验")
    private String weightCheck;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "包装计算重量")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "发货库点(发货库点ID，默认为工厂豆粕库，可选)")
    private String shipWarehouseId;

    @ApiModelProperty(value = "溢短装溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;


    @ApiModelProperty(value = "代加工(是否代加工，默认否（0、否 1、是）)")
    private Integer oem;

    @ApiModelProperty(value = "变更备注")
    private String remark;

    @ApiModelProperty(value = "含税单价")
    private PriceDetailBO priceDetailDTO;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "修改接口的类型：1.普通修改 2.定价完成")
    private Integer reviseType = 1;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "提交类型 1.保存 2.提交(默认)")
    private Integer submitType = SubmitTypeEnum.SUBMIT.getValue();

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "提货方式")
    private Integer deliveryMode;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "所属商务")
    private Integer ownerId;

    @ApiModelProperty(value = "所属商务名称")
    private String ownerName;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品昵称")
    private String commodityName;

    /**
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

}
