package com.navigator.trade.pojo.dto.contractEquity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractEquityDTO {
    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "变更前可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "变更前可反点价次数")
    private Integer ableReversePriceTimes;

    @ApiModelProperty(value = "变更前已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "变更前已反点价次数")
    private Integer reversedPriceTimes;

    @ApiModelProperty(value = "可转月次数")
    private Integer changeAbleTransferTimes;

    @ApiModelProperty(value = "可反点价次数")
    private Integer changeAbleReversePriceTimes;

    @ApiModelProperty(value = "已转月次数")
    private Integer changeTransferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private Integer changeReversedPriceTimes;
}