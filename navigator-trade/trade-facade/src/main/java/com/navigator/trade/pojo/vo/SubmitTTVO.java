package com.navigator.trade.pojo.vo;

import com.navigator.common.enums.ResultCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SubmitTTVO {

    private List<Integer> successList;
    private List<Integer> failedList;

    @ApiModelProperty(value = "提交成功个数")
    private Integer successListSize;

    @ApiModelProperty(value = "提交失败个数")
    private Integer failedListSize;

    @ApiModelProperty(value = "信息完整度校验")
    private List<Integer> failedByCompleteList;

    @ApiModelProperty(value = "TT审批校验")
    private List<Integer> failedByApproveList;

    @ApiModelProperty(value = "商品信息校验")
    private List<Integer> failedByGoodList;

    @ApiModelProperty(value = "客户信息校验")
    private List<Integer> failedByCustomerList;

    @ApiModelProperty(value = "皮带扣是否有效校验")
    private List<Integer> failedByPackageList;

    @ApiModelProperty(value = "质量指标校验")
    private List<Integer> failedByQualityList;

    @ApiModelProperty(value = "付款条件代码是否有效校验")
    private List<Integer> failedByPayConditionList;

    @ApiModelProperty(value = "交货是否有效校验")
    private List<Integer> failedByDeliveryList;

    @ApiModelProperty(value = "重量验收是否有效校验")
    private List<Integer> failedByWeightList;

    @ApiModelProperty(value = "客户赊销赊购配置校验")
    private List<Integer> failedByCreditPaymentList;

    @ApiModelProperty(value = "客户履约保证金配置校验")
    private List<Integer> failedByDepositRateList;

    @ApiModelProperty(value = "基差价或者蛋白价超出校验")
    private List<Integer>  failedByLOAList;

    @ApiModelProperty(value = "客户发票信息校验")
    private List<Integer>  failedByInvoiceList;

    @ApiModelProperty(value = "库点配置校验")
    private List<Integer>  failedByWarehouseList;

    @ApiModelProperty(value = "采购客户账户")
    private List<Integer>  failedByCustomerAccountList;
    /**
     * 剩余风险控制提示信息
     */
    @ApiModelProperty(value = "剩余风险控制提示信息")
    private List<Integer> failedByResidualRiskList;
    private ResultCodeEnum failedByResidualRiskErrorMsg;
    private List<TTQueryVO> list;

    // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18
    @ApiModelProperty(value = "履约保证金释放方式")
    private List<Integer>  failedByDepositReleaseTypeList;

    @ApiModelProperty(value = "期货合约+月份")
    private List<Integer>  failedByDomainCodeList;

}
