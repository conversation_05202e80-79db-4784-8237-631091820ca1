package com.navigator.trade.facade;

import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.*;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import com.navigator.trade.pojo.vo.ContractSignDetailFileVO;
import com.navigator.trade.pojo.vo.ContractSignFileVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 15:21
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractSignFacade {

    /**
     * 协议列表高级分页搜索
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/contractSign/queryContContractSigns")
    Result queryContContractSigns(@RequestBody QueryDTO<ContractSignQO> queryDTO);

    /**
     * 统计协议个数
     *
     * @param signBO
     * @return
     */
    @PostMapping("/contractSign/getContractSignStat")
    Result getContractSignStat(@RequestBody QueryContractSignBO signBO);

    /**
     * 根据协议ID，查询协议详情
     *
     * @param contractSignId 协议Id
     * @return 协议详情信息
     */
    @GetMapping("/getContractSignDetailById")
    ContractSignEntity getContractSignDetailById(@RequestParam(name = "contractSignId") Integer contractSignId);

    /**
     * 根据ttId，查询协议详情
     *
     * @param ttId ttId
     * @return 协议详情信息
     */
    @GetMapping("/getContractSignDetailByTtId")
    ContractSignEntity getContractSignDetailByTtId(@RequestParam(name = "ttId") Integer ttId);

    /**
     * 查询各个状态下的合同数量
     *
     * @return 结果
     */
    @GetMapping("/getCustomerSignStat")
    ContractSignAllStatusNumVO getCustomerSignStat(@RequestParam(value = "salesType", required = false) Integer salesType);

    /**
     * 查询各个状态下的合同数量
     */
    @GetMapping("/getMagellanSignStat")
    ContractSignAllStatusNumVO getMagellanSignStat(
            @RequestParam(value = "ldcFrame", required = false) Integer ldcFrame,
            @RequestParam(value = "salesType", required = false) Integer salesType,
            @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId);

    /**
     * 根据合同ID，查询所有已签署的协议信息
     *
     * @param contractId 合同Id
     * @return 合同的所有的协议信息（文件只有双签）
     */
    @GetMapping("/contractSign/getSignFileListByContractId")
    List<ContractSignFileVO> getSignFileListByContractId(@RequestParam(name = "contractId") Integer contractId, @RequestParam(name = "system") Integer system);

    /**
     * 根据协议ID，查询所有的协议文件信息
     *
     * @param contractSignId 协议Id
     * @param system         系统(1麦哲伦 2哥伦布)
     *                       {@link SystemEnum}
     * @return 协议的所有文件信息
     */
    @GetMapping("/contractSign/getAllSignFileListById")
    List<ContractSignDetailFileVO> getAllSignFileListById(@RequestParam(name = "contractSignId") Integer contractSignId,
                                                          @RequestParam(name = "system", required = false) Integer system);


    /**
     * 签章发起时错误回调
     *
     * @param contractSignErrorDTO
     * @return
     */
    @PostMapping("/contractSign/UpdateContractSignError")
    Result UpdateContractSignError(@RequestBody ContractSignErrorDTO contractSignErrorDTO);

    /**
     * 易企签待签章地址更改
     *
     * @param contractSignYQQUrlDTO
     * @return
     */
    @PostMapping("/contractSign/updateContractSignYQQUrl")
    boolean updateContractSignYQQUrl(@RequestBody ContractSignYQQUrlDTO contractSignYQQUrlDTO);

    /**
     * 处理签章回调信息
     *
     * @param contractSignYQQCallbackDTO
     * @return
     */
    @PostMapping("/updateContractSignYQQCallback")
    boolean updateContractSignYQQCallback(@RequestBody ContractSignYQQCallbackDTO contractSignYQQCallbackDTO);

    /**
     * 根据唯一编号查询协议
     *
     * @param uuid
     * @return
     */
    @GetMapping("/contractSign/queryByUUId")
    ContractSignEntity queryByUUId(@RequestParam(value = "uuid") String uuid);


    /**
     * 保存快递信息
     *
     * @param contractPaperDTO
     * @return
     */
    @PostMapping("/contractSign/saveContractPaperSign")
    Result saveContractPaperSign(@RequestBody ContractPaperDTO contractPaperDTO);


    /**
     * 回传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    @PostMapping("/contractSign/postBackContractSign")
    Result postBackContractSign(@RequestBody ContractBaseSignDTO contractBaseSignDTO);


    /**
     * 上传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    @PostMapping("/contractSign/uploadingBackContractSign")
    Result uploadingBackContractSign(@RequestBody ContractBaseSignDTO contractBaseSignDTO);

    /**
     * 生成协议PDF信息
     *
     * @param contractSignId 协议ID
     * @return 协议模版+组装数据信息
     */
    @GetMapping("/contractSign/generateSignPdf")
    Result generateSignTemplate(@RequestParam(name = "contractSignId") Integer contractSignId);
    /**
     * 构建合同模板的 条件参数 + 业务参数
     *
     * @param contractSignId 协议ID
     * @return
     */
    @GetMapping("/contractSign/buildHuskySignTemplateDTOV2")
    Result buildHuskySignTemplateDTOV2(@RequestParam(name = "contractSignId") Integer contractSignId);
    /**
     * 构建合同模板的 条件参数 + 业务参数
     *
     * @param contractSignId 协议ID
     * @return
     */
    @GetMapping("/contractSign/buildHuskySignTemplateDTO")
    Result buildHuskySignTemplateDTO(@RequestParam(name = "contractSignId") Integer contractSignId);

    /**
     * 出具协议
     *
     * @param contractSignProvideDTO 出具协议修改数据
     * @return 出具结果
     */
    @PostMapping("/contractSign/provide")
    Result provideContractSign(@RequestBody ContractSignProvideDTO contractSignProvideDTO);

    /**
     * 预览PDF文件
     * @param templateEntity
     * @return
     */
    @PostMapping("/previewHuskyContractPdf")
    Result previewHuskyContractPdf(@RequestBody TemplateEntity templateEntity);

    /**
     * 数字合同出局
     *
     * @param templateEntity
     * @return
     */
    @PostMapping("/provideHuskyContractSign")
    Result provideHuskyContractSign(@RequestBody TemplateEntity templateEntity);

    @PostMapping("/contractSign/generateSignPdfTest")
    Result generateSignPdfTest(@RequestBody ContractSignProvideDTO signProvideDTO);

    /**
     * 审核协议
     *
     * @param contractSignReviewDTO 协议审核参数
     * @return 审核协议结果
     */
    @PostMapping("/contractSign/review")
    Result reviewContractSign(@RequestBody ContractSignReviewDTO contractSignReviewDTO);


    /**
     * 发起签章
     *
     * @param signReviewDTO
     * @return
     */
    @PostMapping("/contractSign/contractSignStartSignature")
    Result contractSignStartSignature(@RequestBody ContractSignReviewDTO signReviewDTO);


    /**
     * 删除协议
     *
     * @param ttId TTID
     * @return 删除协议结果
     */
    @PostMapping("/contractSign/delete")
    Result deleteContractSign(@RequestParam(name = "ttId") Integer ttId);

    @GetMapping("/contractSign/sendCustomerNoticeEmail")
    Result sendCustomerNoticeEmail(@RequestParam(value = "signId") Integer signId);

    @GetMapping("/contractSign/sendContractSignOriginalPaper")
    void sendContractSignOriginalPaper();

    @PostMapping("/contractSign/updateById")
    Boolean updateById(@RequestBody ContractSignEntity signEntity);
}
