package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同确认结果DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Data
@Accessors(chain = true)
public class ContractConfirmResultDTO {

    @ApiModelProperty("合同的同步动作")
    private String contractAction;

    @ApiModelProperty(value = "原始合同实体")
    private ContractEntity originalContractEntity;

    @ApiModelProperty(value = "父合同实体")
    private ContractEntity parentContractEntity;

    @ApiModelProperty(value = "不同步原因")
    private String notSyncReason;

}
