package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum  InvalidTypeEnum {

    /**
     * 作废类型
     *
     * @Link
     */
    CANCEL(1, "撤销作废"),
    REJECT(2, "驳回作废"),

    ;

    private int type;
    private String desc;

    public static InvalidTypeEnum getByType(Integer type) {
        if (null == type) return InvalidTypeEnum.CANCEL;
        for (InvalidTypeEnum typeEnum : InvalidTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return InvalidTypeEnum.CANCEL;
    }

    public static String getDescByValue(Integer value) {
        return getByType(value).desc;
    }
}
