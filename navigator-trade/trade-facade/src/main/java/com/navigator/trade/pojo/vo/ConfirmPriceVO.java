package com.navigator.trade.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 定价单的VO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ConfirmPriceVO {
    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 定价单id
     */
    private Integer ttPriceId;

    /**
     * TTId
     */
    private Integer ttId;

    /**
     * 定价价格
     */
    private BigDecimal confirmPrice;

    /**
     * 定价数量
     */
    private BigDecimal confirmNum;

}
