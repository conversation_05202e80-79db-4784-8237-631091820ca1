package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import com.navigator.trade.pojo.enums.DeliveryTransportType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021-12-06 17:54
 */
@Accessors(chain = true)
@TableName("dbt_delivery_type")
@ApiModel(value = "DeliveryTypeEntity对象", description = "交提货方式表")
@Data
public class DeliveryTypeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品类ID,0通用")
    private Integer categoryId;

    @ApiModelProperty(value = "业务类型")
    private String buCode = BuCodeEnum.ST.getValue();

    @ApiModelProperty(value = "交提货方式名称")
    private String name;

    /**
     * {@link DeliveryModeEnum}
     */
    @ApiModelProperty(value = "交提货类型")
    private Integer type;

    @ApiModelProperty(value = "提货地点：（1、工厂 2、码头 3、达孚外租库 4、客户仓库 5交割库）")
    private Integer addressType;

    @ApiModelProperty(value = "lkg编码")
    private String lkgCode;

    @ApiModelProperty(value = "Atlas编码")
    private String atlasCode;

    @ApiModelProperty(value = "Mgl可提")
    private Integer ableDeliveryMagellan = 1;

    @ApiModelProperty(value = "Columbus可提")
    private Integer ableDeliveryColumbus = 1;

    /**
     * {@link DeliveryTransportType}
     */
    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

    @ApiModelProperty(value = "合同条款")
    private String contractTerms;

    @ApiModelProperty(value = "状态")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "模板修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "修改人")
    private Integer updatedBy;

    @TableField(exist = false)
    private String updator;

}
