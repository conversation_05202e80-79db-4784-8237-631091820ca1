package com.navigator.trade.pojo.dto.contract;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.trade.pojo.dto.tradeticket.ContractPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.DeliveryTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import lombok.Data;

import static com.navigator.common.constant.ContractSignConstant.*;

@Data
public class ContractDetailInfoDTO extends ContractEntity {
    Integer contractId;
    //====================枚举值====================

    String contractTypeName;
    String salesTypeName;
    String contractSourceName;
    String tradeTypeName;
    String goodsCategoryName;
    String deliveryModeName;
    String paymentTypeName;

    //====================直接拼接区====================


    //====================需逻辑处理区====================

    //====================需逻辑处理并落地区====================


    String createEmployName;
    String ownerEmployName;


    ContractPriceDTO contractPriceDTO;

    public Integer getContractId() {
        return getId();
    }

    public String getContractTypeName() {
        try {
            return ContractTypeEnum.getDescByValue(getContractType());
        } catch (Exception e) {
            return WHEN_ERROR_CONTRACT_TYPE;
        }
    }

    public String getSalesTypeName() {
        try {
            return ContractSalesTypeEnum.getDescByValue(getSalesType());
        } catch (Exception e) {
            return ContractSalesTypeEnum.SALES.getDescription();
        }
    }

    public String getContractSourceName() {
        return ContractTradeTypeEnum.getDescByValue(getContractSource());
    }

    public String getTradeTypeName() {
        return ContractTradeTypeEnum.getDescByValue(getTradeType());
    }

    public String getGoodsCategoryName() {
        return GoodsCategoryEnum.getDesc(getGoodsCategoryId());
    }

    public String getDeliveryModeName() {
        try {
            return DeliveryTypeEnum.getModeNameByValue(getDeliveryType());
        } catch (Exception e) {
            return WHEN_ERROR_DELIVERY_MODE;
        }
    }

    public String getPaymentTypeName() {
        try {
            return PaymentTypeEnum.getDescByValue(getPaymentType());
        } catch (Exception e) {
            return WHEN_ERROR_PAYMENT_TYPE;
        }
    }


}
