package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 价格明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@Accessors(chain = true)
@TableName("dbt_contract_price")
@ApiModel(value = "ContractPriceEntity对象", description = "价格明细表")
public class ContractPriceEntity extends ContractPriceBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    /**
     * 价格信息都在ContractPriceBaseEntity中
     */

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;


}
