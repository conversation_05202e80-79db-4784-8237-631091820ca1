package com.navigator.trade.pojo.dto.future;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/13
 */

@Data
@Accessors(chain = true)
public class ContraryPriceDTO {
    //分配单id
    private Integer allocateId;
    //品类
    private Integer categoryId;
    //采销类型
    private Integer salesType;
    //定价单类型
    private Integer priceApplyType;
    //撤回原因
    private String contraryCause;
}
