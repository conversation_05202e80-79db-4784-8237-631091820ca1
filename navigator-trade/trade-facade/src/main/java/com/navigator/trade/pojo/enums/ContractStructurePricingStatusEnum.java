package com.navigator.trade.pojo.enums;

import lombok.Getter;

@Getter
public enum ContractStructurePricingStatusEnum {

    INVALID(0, "未开始"),
    PRICING(1, "定价中"),
    CANCELED(2, "已终止"),
    COMPLATE(9, "已完成");

    private int value;
    private String desc;

    ContractStructurePricingStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ContractStructurePricingStatusEnum getByValue(int value) {
        for (ContractStructurePricingStatusEnum en : ContractStructurePricingStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ContractStructurePricingStatusEnum.INVALID;
    }
}
