package com.navigator.trade.pojo.dto.tradeticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SalesContractAddTTDTO extends TTCommonDTO {
    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 点价合同）")
    private String contractType;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private String goodsPackageId;


    @ApiModelProperty(value = "规格（默认43%），可选")
    private String goodsSpecId;

    /**
     * {@link com.navigator.trade.pojo.enums.PaymentTypeEnum}
     */
    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "迟付款罚金")
    private String delayPayFine;

    @ApiModelProperty(value = "签订地")
    private String signPlace;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "是否代加工，默认否（0、否 1、是）")
    private Integer oem;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositUseRule;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;

    @ApiModelProperty(value = "目的地/目的港")
    private String destination;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    private String weightCheck;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private String shipWarehouseId;

    @ApiModelProperty(value = "发货库点Code")
    private String shipWarehouseCode;

    @ApiModelProperty(value = "发货库点名称")
    private String shipWarehouseName;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    private String ownerId;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    private Integer isStf;

    @ApiModelProperty(value = "点价截止日期类型（1时间 2文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT类型（1、合同新增；2、合同变更；3、合同回购；4、点价申请）")
    public Integer type;

    @ApiModelProperty(value = "完成度状态")
    private Integer completedStatus;

    @ApiModelProperty(value = "点价开始时间")
    private String priceStartTime;

    private Boolean createStatus;

    private String userId;

    private Integer supplierAccountId;


    /**
     * ************************核心字段集
     *****************************************/

    @ApiModelProperty(value = "含税单价（系统默认由以下字段输入数字相加而来： 基差价 期货价格 蛋白价差 散粕补贴 期权费 其他物流费用 运费。起吊费用；达孚待付，客户自付。滞期费。高温费 回购折价 客诉折价 转厂补贴 其他补贴 商务补贴 手续费 DCE交割：只有交割结算价）")
    private String unitPrice;

    @ApiModelProperty(value = "合同总量（吨）")
    private String contractNum;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "履约保证金比例 (系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
    private Integer depositRate;

    @ApiModelProperty(value = "应付履约保证金")
    private String depositAmount;

    @ApiModelProperty(value = "协议类型:0.补充协议 1.尾量终止")
    private Integer signType;

    @ApiModelProperty(value = "当前解约定赔含税单价")
    private BigDecimal washoutUnitPrice;

    @ApiModelProperty(value = "解约定赔价格详情")
    private String washoutPriceDetailBO;

    @ApiModelProperty(value = "补充协议类型 -1:原合同TT不审批不生成协议   0: 普通拆分(新合同) 1: 数量变更补充协议(原合同)")
    private Integer addedSignatureType = 0;

    @ApiModelProperty(value = "期货价")
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "仓单交易类型 1.交易所交割仓单 2.线下交易所仓单 3.交易所仓单交易平台")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "付款方式(1:赊销 2:预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "仓单ID")
    private Integer warrantId;

    @ApiModelProperty(value = "仓单Code")
    private String warrantCode;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "品种代码")
    private String categoryCode;
    //品类编码
    @ApiModelProperty(value = "品类编码")
    private String futureCode;

    /**
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;

    /**
     * 1保函 2现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;

    @ApiModelProperty(value = "交割保证金金额")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String deliveryMarginAmount;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

    @ApiModelProperty(value = "是否为豆二（0否;1是）")
    private Integer isSoybean2;

    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
    @ApiModelProperty(value = "批次组ID，用于标识同一批次创建的合同")
    private Integer groupId;
    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

}
