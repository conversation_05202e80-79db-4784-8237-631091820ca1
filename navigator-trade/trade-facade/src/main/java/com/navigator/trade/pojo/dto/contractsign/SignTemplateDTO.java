package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.common.constant.TemplateConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022-02-23 16:49
 */
@Data
@Accessors(chain = true)
public class SignTemplateDTO {

    /**
     * M条款、E子模版的展示条件封装
     */
    private TemplateConditionDTO templateCondition;
    /**
     * 关键变量
     */
    private KeyVariableDTO keyVariableDTO;
    /**
     * 品种简称(豆粕：M; 豆油：Y)
     */
    private String vrjc = "";

    /**
     * 品种全称(豆粕：SBM;豆油：SBO)
     */
    private String vrqc;
    /**
     * 合同编号
     */
    private String no = "";
    /**
     * 二维码
     */
    private String ewm = "";
    /**
     * 条形码
     */
    private String txm = "";
    /**
     * 签约日期
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String doc = "";
    /**
     * 原合同签约日期
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String ydoc = "";
    /**
     * 交提货方式
     */
    private String dg = "";
    /**
     * 合同类型
     */
    private String ty = "";
    /**
     * 合同付款方式
     */
    private String pm = "";
    /**
     * 卖方主体所在地址简称
     */
    private String ad = "";
    /**
     * 买方主体
     */
    private String na = "";
    /**
     * 买方主体
     */
    private String nna = "";
    /**
     * 卖方主体
     */
    private String me = "";
    /**
     * 采购合同.买方地址
     */
    private String bscd;
    /**
     * 采购合同.卖方地址
     */
    private String sscd;
    /**
     * 品种
     */
    private String vr = "";
    /**
     * 吨数
     */
    private String mt = "0";
    /**
     * 溢短装
     */
    private String os = "";
    /**
     * 蛋白含量
     */
    private String eg = "";
    /**
     * 包装计算重量
     */
    private String ag = "";

    /**
     * 原包装计算重量
     */
    private String yag = "";
    /**
     * 含税单价
     */
    private String pr = "0";
    /**
     * 原合同含税单价
     */
    private String ypr = "0";
    /**
     * 含税单价明细
     * 期货价格3200；基差价格+20；运费20
     */
    private String prx = "";
    /**
     * 含税单价明细(含基差价)
     * 期货价格3200；基差价格+20；运费20
     */
    private String prxy = "";
    /**
     * 含税单价中的运费
     */
    private String yf = "0";
    /**
     * 原合同含税单价中的运费
     */
    private String yyf = "0";
    /**
     * 地址
     */
    private String ds = "";
    /**
     * 交货地点
     */
    private String dd = "";
    /**
     * 交货周期
     * 合同.开始交货日及截止交货日
     * 需转化成中文日期格式（2018年5月18日至2018年5月22日）
     */
    private String po = "";
    /**
     * 目的港("送到"+（指定地点）)
     */
    private String py = "";
    /**
     * 目的港
     */
    private String pye = "";
    /**
     * 重量验收
     */
    private String pe = "";
    /**
     * 账期
     */
    private Integer mes = 0;
    /**
     * 开户行
     */
    private String kh = "";
    /**
     * 银行账号
     */
    private String zh = "";
    /**
     * 付款截止日期
     * 合同.签订日期+1个自然日
     * 需转化成中文日期格式(例如2022年2月10日)
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String jzfk = "";
    /**
     * 保证金比例:5%
     */
    private String mr = "0";
    /**
     * 点价后补缴比例:5%
     */
    private String dmr = "0";
    /**
     * 买方收件人,多个值，使用；隔开
     */
    private String fox = "";
    /**
     * 期货合约
     * 需转化成中文日期格式,示例文本：2022年05月
     */
    private String hy = "";
    /**
     * 期货合约,示例文本：2205
     */
    private String hyj = "";
    /**
     * todo:(未确定的取值逻辑)
     */
    private String smx = "";
    /**
     * 基差价
     * 合同.含税单价.基差价
     */
    private String jcj = "0";
    /**
     * 点价截止日期
     */
    private String djj = "";
    /**
     * 框架合同签约日期
     */
    private String kjr = "";
    /**
     * 框架合同号
     */
    private String kjh = "";
    /**
     * 协议编号
     */
    private String xyb = "";
    /**
     * 定价单TT数量
     * 合同定价TT.定价数量
     */
    private String htdj = "0";
    /**
     * 点价日期
     * 合同定价TT.点价日期
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String djs = "";
    /**
     * 定价单价格
     * 合同定价TT.定价价格
     */
    private String djjg = "0";
    /**
     * 合同未定价量
     */
    private String wdjl = "0";
    /**
     * TT修改日期
     * TT.签订日期,需转换成中文格式
     */
    private String ttxr = "";
    /**
     * 父合同编号
     */
    private String noy = "";
    /**
     * 转月数量
     */
    private String zysl = "0";
    /**
     * 原期货合约
     */
    //@JsonFormat(pattern = "yyyy年MM月", timezone = "GMT+8")
    private String yhy = "";
    /**
     * 手续费
     */
    private String sxf = "0";
    /**
     * 交货工厂
     */
    private String jhgc = "";
    /**
     * 原合同数量
     */
    private String cfsl = "0";
    /**
     * 原合同交货工厂
     */
    private String yjhgc = "";
    /**
     * 未开单量
     */
    private String wkdl = "0";
    /**
     * 未提货量
     */
    private String wthl = "0";

    //todo:===============新增字段=================
    /**
     * 加权平均价
     * TODO:逻辑修改
     * 合同.所有定价单（定价价格+基差价）.平均价
     */
    private String jqpj = "0";
    /**
     * 解约定赔数量
     */
    private String xdsl = "0";
    /**
     * 合同剩余未开单量
     * #wkdl#-#XDSL#
     */
    private String xwkdl = "0";
    /**
     * 解约定赔总差价
     */
    private String xdcj = "0";
    /**
     * 解约定赔市场价
     */
    private String xdscj = "0";
    /**
     * 转月价差
     */
    private String zyjc = "0";
    /**
     * 原合同溢短装
     */
    private String yos = "";
    /**
     * 修改前含税单价(示例：3201.01)
     */
    private String xpr = "";
    /**
     * 原合同剩余数量(合同总量-拆分数量)
     */
    private String sysl = "";
    /**
     * 解约定赔差价总额,（#pr#-#xdscj#）*#xdsl#
     */
    private String xdze = "";
    /**
     * 保证金追加比例
     * 履约保证金=除5、10、15、20外，显示5%
     */
    private String bzjzj = "";
    /**
     * 投诉单号
     */
    private String tsdh = "";
    /**
     * 投诉原因
     */
    private String tsyy = "";
    /**
     * 客诉折价: 合同.含税单价.客诉折价
     * -20.00
     */
    private String kszj = "";
    /**
     * 客诉折价总金额
     * kszj * mt,客诉折价*吨数
     * -200.00
     */
    private String kszjz = "";
    /**
     * 滞期费: 合同.含税单价.滞期费
     */
    private String zqf = "";
    /**
     * 滞期费总金额
     * zqf*wkdl(滞期费*未开单量)
     */
    private String zqfz = "";
    /**
     * 赊销利息: 合同.含税单价.商务补贴
     */
    private String sxlx = "";
    /**
     * 赊销日期
     */
    private String sxrq = "";
    /**
     * ldc盖章
     * yqq.盖章
     */
    private String lgz = "";
    /**
     * 客户盖章
     * 客户.盖章
     */
    private String kgz = "";
    /**
     * 国标值/企标值
     * 交货工厂=ZJG、YZ、ZS、ZZY、DG、TJ 显示：企标值,其他显示：国标值
     */
    private String gqbz = "";
    /**
     * 买方地址
     */
    private String ads = "";
    /**
     * 买方电子邮箱
     * 多个值，使用；隔开
     * 示例文本：<EMAIL>；<EMAIL>
     */
    private String ema = "";
    /**
     * 买方电话号码
     * 多个值，使用；隔开
     */
    private String mbo = "";
    /**
     * 卖方地址
     */
    private String mads = "";
    /**
     * 卖方收件人
     * 合同.品种.买方主体.交货工厂.收件人 (逗号隔开)
     */
    private String mfox = "";
    /**
     * 卖方电子邮箱
     * 合同.品种.买方主体.交货工厂.收件人（分号隔开）
     */
    private String mema = "";
    /**
     * 卖方电话号码
     */
    private String mmbo = "";
    /**
     * 含税总金额
     */
    private String prt = "0";
    /**
     * 不含税总金额
     */
    private String nprt = "0";
    /**
     * 增值税总金额
     */
    private String sz = "0";
    /**
     * 原合同_变更后_税率
     */
    private BigDecimal taxRateModify;
    /**
     * 原合同_变更后_含税总金额
     */
    private BigDecimal totalAmountModify;
    /**
     * 原合同_变更后_含税总金额
     */
    private String totalAmountInfoModify = "0";
    /**
     * 原合同_变更后_不含税总金额
     */
    private String noTaxTotalAmountInfoModify = "0";
    /**
     * 原合同_变更后_增值税总金额
     */
    private String addedTaxTotalAmountInfoModify = "0";
    /**
     * 销售合同/订单
     */
    private String xhd = "";
    /**
     * 采购合同/订单
     */
    private String chd = "";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：开户行
     */
    private String khfk = "";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：银行账号
     */
    private String zhfk = "";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：开户名
     */
    private String khmc = "";
    /**
     * 结构化定价总数量
     */
    private String jghzsl = "0";
    /**
     * 结构化定价开始日期
     */
    private String jghkrq = "";
    /**
     * 定价单含税总金额
     */
    private String djprt = "0";

    /**
     * 加权平均含税价
     */
    private String ldjjghs = "0";

    /**
     * 定价单不含税总金额
     */
    private String djnprt = "0";

    /**
     * 定价单增值税总金额
     */
    private String djsz = "0";
    /**
     * 加权平均价
     */
    private String ldjjg = "0";
    /**
     * 加权含税总金额
     */
    private String lprt = "0";
    /**
     * 加权不含税总金额
     */
    private String lnprt = "0";
    /**
     * 加权增值税总金额
     */
    private String lsz = "0";

    /**
     * 结构化定价单位量
     */
    private String jghdwl = "0";

    private String sdze = "";

    private String cash_money = "";
    /**
     * 转月时间
     */
    private String jzlyfk = "";

    /**
     * 转月含税单价明细
     */
    private String prxzy = "";
    /**
     * 结构化定价结束日期
     */
    private String jghjrq = "";
    /**
     * 结构化定价敲出价格
     */
    private String jghqc = "0";
    /**
     * 结构化定价增强价格
     */
    private String jghzq = "0";
    /**
     * 结构化定价触发价格
     * 点价申请.结构化定价.触发价格
     */
    private String jghcf = "";
    /**
     * 结构化定价累积价格
     * 点价申请.结构化定价.累积价格
     */
    private String jghlj = "";
    /**
     * 结构化定价单位数量
     * 点价申请.结构化定价.单位量数量
     */
    private String jghdwsl = "";
    /**
     * 结构化定价单位增量
     * 点价申请.结构化定价.单位量增量
     */
    private String jghdwzl = "";
    /**
     * 履约保证金文本特殊处理（当日卖方基差报价${lytext!}元以上）
     * 豆油-销售-基差（付款条款变更，取300）
     */
    private Integer lytext = 150;

    /**
     * 单吨现金返还金额
     */
    private String jghddxjfh;
    /**
     * 保证金使用规则描述（option1/option2）
     */
    private String depositUseRuleName;

    /**
     * 以上期限文本(交货截止日期 < 合同签约日期,拆分）
     * {@link TemplateConstant.DePOSIT_USE_RULE_RATIO}
     */
    private String aboveDeadlineInfo;
    /**
     * 点价截止日期(含)
     * djj+(含)，日期时候拼（含）
     */
    private String priceEndTimeContains = "";

    /**
     * 作价期限文本(日期/文本)
     * 基差暂定价和暂定价作价期限
     * {@link TemplateConstant.PRICE_DEADLINE_DATE}
     */
    private String priceDeadlineText;
    /**
     * 发票后补缴货款比例
     */
    @ApiModelProperty(value = "发票后补缴货款比例")
    private String invoicePaymentRateInfo;
    /**
     * 100-发票后补缴货款比例
     */
    @ApiModelProperty(value = "100-发票后补缴货款比例")
    private String invoicePaymentRateInTurnInfo;
    //正大“交货地点”特殊条款触发逻辑：客户=正大集团（配置的3个） 并且  有特殊备注  并且 交货工厂=TJ,
    // TT的备注信息中，包含被“@#￥”括起来的信息
    private String  specialCustomerDeliveryInfo;
    /**
     * 主体-logo
     */
    private String logo;
}
