package com.navigator.trade.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.ContractVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 合同对外暴露的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/30
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractFacade {

    /**
     * 根据合同id查询合同
     *
     * @param id
     * @return
     */
    @GetMapping("/getContractById")
    ContractEntity getContractById(@RequestParam(value = "id") Integer id);

    /**
     * 根据合同id查询基本合同信息(不调用lkg)
     *
     * @param id
     * @return
     */
    @GetMapping("/getBasicContractById")
    ContractEntity getBasicContractById(@RequestParam(value = "id") Integer id);

    /**
     * 根据编号获取合同基本信息
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getBasicContractByCode")
    ContractEntity getBasicContractByCode(@RequestParam(value = "contractCode") String contractCode);

    /**
     * 根据仓单编号获取合同基本信息
     *
     * @param warrantCode
     * @return
     */
    @GetMapping("/getContractByWarrantCode")
    List<ContractEntity> getContractByWarrantCode(@RequestParam(value = "warrantCode") String warrantCode);

    /**
     * 修改合同
     *
     * @param contractEntity
     * @return
     */
    @PostMapping("/updateContract")
    Integer updateContract(@RequestBody ContractEntity contractEntity);


    /**
     * 更新备份合同数据信息
     *
     * @param contractBackUpDTO
     * @return
     */
    @PostMapping("/updateAndBackUpContract")
    Integer updateAndBackUpContract(@RequestBody ContractBackUpDTO contractBackUpDTO);

    /**
     * 根据合同编号关闭合同状态
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/updateContractByCode")
    Integer updateContractByCode(@RequestParam(value = "contractCode") String contractCode,
                                 @RequestParam(value = "contractStatus") Integer contractStatus);


    @PostMapping("/updateStructureContractPricingStatus")
    boolean updateStructureContractPricingStatus(@RequestBody ContractStructureDTO contractStructureDTO);


    /**
     * 根据条件查询结构化定价合同
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractStructure")
    Result queryContractStructure(@RequestBody QueryDTO<ContractBO> queryDTO);

    /**
     * 确认合规
     *
     * @param contractBaseDTO
     * @return
     */
    @PostMapping("/contract/confirm")
    Result confirmContract(@RequestBody ContractBaseDTO contractBaseDTO);

    /**
     * 补充合同信息
     *
     * @param contractBaseDTO
     * @return
     */
    @PostMapping("/contract/fill")
    Result fillContract(@RequestBody ContractBaseDTO contractBaseDTO);

    /**
     * 合同分页列表高级检索 | 调整到新结构
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContract")
    Result queryContract(@RequestBody QueryDTO<ContractQO> queryDTO);

    /**
     * 根据合约号获取当前客户可以分配的合同(分页查询)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/pageContractsByDomainCode")
    Result pageContractsByDomainCode(@RequestBody QueryDTO<QueryContractDTO> queryDTO);


    /**
     * 根据合约号获取当前客户可以分配的合同(不分页)
     *
     * @param queryContractDTO
     * @return
     */
    @PostMapping("/queryContractsByDomainCodeList")
    List<ContractEntity> queryContractsByDomainCodeList(@RequestBody QueryContractDTO queryContractDTO);


    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractsColumbus")
    Result queryContractsColumbus(@RequestBody QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 根据合同Id查询合同明细 仓单 | 现货
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractByContractId")
    Result getContractByContractId(@RequestParam("contractId") String contractId);

    /**
     * 根据合同Id查询合同明细(不调用lkg)
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getBasicContractByContractId")
    Result getBasicContractByContractId(@RequestParam("contractId") String contractId);

    /**
     * 根据合同Id查询合同明细
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractByContractCode")
    Result getContractByContractCode(@RequestParam("contractCode") String contractCode);

    /**
     * 根据合同Id查询合同Id
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractIdByCode")
    Result getContractIdByCode(@RequestParam("contractCode") String contractCode);

    @PostMapping("/getValidStructureContract")
    List<ContractStructureEntity> getValidStructureContract(@RequestBody List<Integer> contractIds);

    /**
     * 根据合同Id获取电子合同
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractPdfs")
    Result getContractPdfs(@RequestParam("contractId") String contractId);

    /**
     * 查询合同是否可签章
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/contract/canSign/{contractId}")
    Result canSign(@PathVariable("contractId") String contractId);

    /**
     * 获取合同含税单价详细
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractUnitPriceDetail")
    Result getContractUnitPriceDetail(@RequestParam("contractId") String contractId);

    /**
     * 查询客户待点价及转月数量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/queryContractsFuturesNum")
    List<ContractEntity> queryContractsFuturesNum(@RequestBody ContractFuturesDTO contractFuturesDTO);


    /**
     * 分组查询期货合约
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/queryContractsFutures")
    List<ContractEntity> queryContractsFutures(@RequestBody ContractFuturesDTO contractFuturesDTO);

    /**
     * 暂定价合同定价（生成定价单）
     *
     * @param confirmPriceDTO
     * @return
     */
    @PostMapping("/contract/createTtPrice")
    Result createTtPrice(@RequestBody ConfirmPriceDTO confirmPriceDTO);


    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/contract/futureContracts")
    Result futureContracts(@RequestBody QueryDTO<ContractFuturesDTO> queryDTO);

    /**
     * 查询合同的拆分和修改的记录
     *
     * @param modifyDTO
     * @return
     */
    @PostMapping("/contract/getModifyLog")
    Result getContractModifyLog(@RequestBody ContractModifyDTO modifyDTO);

    /**
     * 获取合同变更所需要的数量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/contract/getModifyNumInfo")
    Result getContractModifyNumInfo(@RequestParam("contractId") Integer contractId);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return List<ConfirmPriceVO>
     */
    @GetMapping("/contract/getConfirmPriceList")
    Result getConfirmPriceList(@RequestParam("contractId") Integer contractId);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return List<ConfirmPriceDTO>
     */
    @GetMapping("/contract/getConfirmPricedList")
    Result getConfirmPricedList(@RequestParam("contractId") Integer contractId);

    @GetMapping("/contract/getTTPriceByContractId")
    Result getTTPriceByContractId(@RequestParam("contractId") Integer contractId);

    /**
     * 获取合同定价单信息
     *
     * @param contractId
     * @return ConfirmPriceDTO
     */
    @GetMapping("/contract/getConfirmPricedInfo")
    Result getConfirmPricedInfo(@RequestParam("contractId") Integer contractId, @RequestParam("ttPriceId") Integer ttPriceId);

    /**
     * 保存定价单
     *
     * @param ttPriceEntity
     * @return
     */
    @PostMapping("/contract/saveConfirmPrice")
    Result saveConfirmPrice(@RequestBody TTPriceEntity ttPriceEntity);

    /**
     * 修改合同
     *
     * @param contractModifyDTO
     * @return
     */
    @PostMapping("/contract/revise")
    Result reviseContract(@RequestBody ContractModifyDTO contractModifyDTO);

    /**
     * 拆分合同
     *
     * @param contractModifyDTO
     * @return
     */
    @PostMapping("/contract/split")
    Result splitContract(@RequestBody ContractModifyDTO contractModifyDTO);

    /**
     * 点价操作
     *
     * @param salesContractTTPriceDTO
     * @return
     */
    @PostMapping("/contract/price")
    Result priceContract(@RequestBody SalesContractTTPriceDTO salesContractTTPriceDTO);

    /**
     * 转月操作
     *
     * @param contractTransferDTO
     * @return
     */
    @PostMapping("/contract/transferMonth")
    Result transferMonthContract(@RequestBody ContractTransferDTO contractTransferDTO);

    /**
     * 反点价操作
     *
     * @param contractTransferDTO
     * @return
     */
    @PostMapping("/contract/reversePrice")
    Result reversePriceContract(@RequestBody ContractTransferDTO contractTransferDTO);

    /**
     * 转月生成子合同
     *
     * @param contractTransferDTO
     * @return
     */
    @PostMapping("/contract/createSonContract")
    Result createSonContract(@RequestBody ContractTransferDTO contractTransferDTO);

    /**
     * 销售合同回购
     *
     * @param contractBuyBackDTO
     * @return
     */
    @PostMapping("/contract/applyBuyBack")
    Result applyBuyBack(@RequestBody ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 解约定赔
     *
     * @param contractWashOutDTO
     * @return
     */
    @PostMapping("/contract/applyWashOut")
    Result applyWashOut(@RequestBody ContractWashOutDTO contractWashOutDTO);

    /**
     * 仓单合同注销
     *
     * @param contractWriteOffDTO
     * @return
     */
    @PostMapping("/contract/applyWriteOff")
    Result applyWriteOff(@RequestBody ContractWriteOffDTO contractWriteOffDTO);

    /**
     * 仓单合同豆二注销
     *
     * @param contractWriteOffOMDTO
     * @return
     */
    @PostMapping("/contract/applySoyBean2WriteOff")
    Result applySoyBean2WriteOff(@RequestBody ContractWriteOffOMDTO contractWriteOffOMDTO);

    /**
     * 仓单合同注销撤回
     *
     * @param contractWriteOffWithDrawDTO
     * @return
     */
    @PostMapping("/contract/applyWriteOffWithDraw")
    Result applyWriteOffWithDraw(@RequestBody ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * 仓单合同作废
     *
     * @param contractId
     * @return
     */
    @GetMapping("/contract/applyWarrantContractInvalid")
    Result applyWarrantContractInvalid(@RequestParam("contractId") Integer contractId);

    /**
     * 合同关闭
     *
     * @param contractId
     * @return
     */
    @GetMapping("/contract/applyClosed")
    Result applyClosed(@RequestParam("contractId") Integer contractId);

    /**
     * Atlas 仓单合同关闭请求
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/contract/atlasClosed")
    Result applyAtlasClosed(@RequestParam("contractCode") String contractCode);

    /**
     * 合同关闭
     *
     * @param contractId
     * @return
     */
    @GetMapping("/contract/applyInvalid")
    Result applyInvalid(@RequestParam("contractId") Integer contractId);

    /**
     * 判断是否能编辑货品
     *
     * @param contractId
     * @return
     */
    @GetMapping("/contract/judgeCanModifyGoods")
    Result judgeCanModifyGoods(@RequestParam("contractId") Integer contractId);


    /**
     * 同步数据到contract
     *
     * @param contractEntity
     * @return
     */
    @PostMapping("/contract/prePareToContract")
    boolean prePareToContract(@RequestBody ContractEntity contractEntity);


    @GetMapping("/contract/getContractTransferNum")
    ContractTransferCountDTO getContractTransferNum(@RequestParam("customerId") Integer customerId,
                                                    @RequestParam("goodsCategoryId") Integer goodsCategoryId,
                                                    @RequestParam("domainCode") String domainCode,
                                                    @RequestParam("deliveryEndTime") Date deliveryEndTime,
                                                    @RequestParam("category2") Integer category2);


    @PostMapping("/changeCustomerWhiteList")
    Result changeCustomerWhiteList(@RequestBody CustomerDetailDTO customerDetailDTO);

    @GetMapping("/contract/addStructureRelease")
    boolean addStructureRelease(@RequestParam("contractId") Integer contractId, @RequestParam("notDealNum") BigDecimal notDealNum);

    @GetMapping("/contract/getApplyIdByStructureContractId")
    Integer getApplyIdByStructureContractId(@RequestParam("contractId") Integer contractId);

    @GetMapping("/contract/getApplyIdByStructureContractCode")
    Integer getApplyIdByStructureContractCode(@RequestParam("contractCode") String contractCode);

    @GetMapping("/contract/getContractStructureById")
    ContractStructureEntity getContractStructureById(@RequestParam("contractId") Integer contractId);

    @GetMapping("/contract/getContractStructureVOById")
    ContractStructureEntity getContractStructureVOById(@RequestParam("contractId") Integer contractId);

    //更新含税单价以及其他contract关联数据
    @GetMapping("/updateUnitPrice")
    Result updateUnitPrice(@RequestParam(value = "contractCode") String contractCode, @RequestParam("unitPrice") BigDecimal unitPrice);

    @PostMapping("/updateStructureContract")
    boolean updateStructureContract(@RequestBody ContractStructureEntity contractStructureEntity);

    /**
     * 默认合同关闭，传状态按传的合同号批量更新合同状态
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/updateContractStatus")
    Result updateContractStatus(@RequestParam(value = "file") MultipartFile uploadFile, @RequestParam(value = "status", required = false) Integer status);


    /**
     * 查询客户对应可提货申请的合同
     *
     * @param deliveryApplyContractQO
     * @return
     */
    @PostMapping("/getDeliveryApplyContractGroup")
    Result getDeliveryApplyContractGroup(@RequestBody DeliveryApplyContractQO deliveryApplyContractQO);

    @PostMapping("/getDeliveryApplyContractList")
    Result getDeliveryApplyContractList(@RequestBody DeliveryApplyContractQO deliveryApplyContractQO);

    /**
     * atlas:提货申请获取合同列表信息
     *
     * @param deliveryApplyContractQO
     * @return
     */
    @PostMapping("/getDeliveryApplyContractListForAtlas")
    Result getDeliveryApplyContractListForAtlas(@RequestBody DeliveryApplyContractQO deliveryApplyContractQO);

    @GetMapping("/getContractListByIds")
    Result getContractListByIds(@RequestParam("contractIdList") List<Integer> contractIdList);

    @GetMapping("/getContractTraceList")
    Result getContractTraceList(@RequestParam("contractId") Integer contractId);

    /**
     * 根据仓单合同ID获取提货权合同
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getCargoRightsContractById")
    Result<List<ContractVO>> getCargoRightsContractById(@RequestParam("contractId") Integer contractId);

    /**
     * 根据申请单id查询定价单
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/getTTPriceByApplyId")
    List<TTPriceEntity> getTTPriceByApplyId(@RequestParam(value = "priceApplyId") Integer priceApplyId);

    /**
     * 根据分配单id查询定价单
     *
     * @param allocateId
     * @return
     */
    @GetMapping("/getTTPriceByAllocateId")
    TTPriceEntity getTTPriceByAllocateId(@RequestParam(value = "allocateId") Integer allocateId);

    /**
     * 根据申请单id查询定价单
     *
     * @param sourceId
     * @return
     */
    @GetMapping("/getTTPriceBySourceId")
    List<TTPriceEntity> getTTPriceBySourceId(@RequestParam(value = "sourceId") Integer sourceId);

    /**
     * 修改定价单
     *
     * @param ttPriceEntity
     * @return
     */
    @PostMapping("/updateTTPriceById")
    Boolean updateTTPriceById(@RequestBody TTPriceEntity ttPriceEntity);

    /**
     * 根据合同id关闭合同的尾量
     *
     * @param contractId 合同id
     * @param triggerSys 触发系统
     * @return
     */
    @GetMapping("/closeTailNumByContractId")
    Result closeTailNumByContractId(@RequestParam(value = "contractId") Integer contractId, @RequestParam("triggerSys") Integer triggerSys);


    /**
     * 根据多选合同关闭合同的尾量
     *
     * @param contractIds 合同id集合
     * @param triggerSys  触发系统
     * @return
     */
    @GetMapping("/batchCloseTailNum")
    Result batchCloseTailNum(@RequestParam(value = "contractIds") List<Integer> contractIds, @RequestParam("triggerSys") Integer triggerSys);

    /**
     * 取消尾量关闭
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/cancelCloseTailNumByContractId")
    Result cancelCloseTailNumByContractId(@RequestParam(value = "contractId") Integer contractId);

    /**
     * 批量取消关闭尾数
     *
     * @param contractIds 合同id集合
     * @return
     */
    @GetMapping("/batchCancelCloseTailNum")
    Result batchCancelCloseTailNum(@RequestParam(value = "contractIds") List<Integer> contractIds);

    /**
     * 根据合同id查询合同的尾量关闭记录
     *
     * @param contractCode 合同id
     * @return
     */
    @GetMapping("/getCloseTailNumRecord")
    Result getCloseTailNumRecord(@RequestParam(value = "contractCode") String contractCode);

    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态 Author: Mr 2024-05-27 Start
    @GetMapping("/closedBySiteCodeAndContractCode")
    Result closedBySiteCodeAndContractCode(@RequestParam(value = "siteCode") String siteCode, @RequestParam(value = "contractCode") String contractCode);

    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态 Author: Mr 2024-05-27 End

    /**
     * 根据合同Id获取提货合同<主要是仓单合同进行注销产生的>
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getDeliveryContractByContractId")
    Result getDeliveryContractByContractId(@RequestParam(value = "contractId") Integer contractId);

    /**
     * 校验某品种下是否存在过程中的合同（根据品种）
     *
     * @param category3List
     * @return
     */
    @GetMapping("/judgeCategory3InProcessContractForSite")
    List<String> judgeCategory3InProcessContractForSite(@RequestParam(value = "category3List") List<Integer> category3List,
                                                        @RequestParam(value = "siteCode") String siteCode);


    @GetMapping("/getContractBlockedNum")
    Result<BigDecimal> getContractBlockedNum(@RequestParam(value = "contractCode") String contractCode);

    @GetMapping("/getContractHistoryEntity")
    Result<ContractHistoryEntity> getContractHistoryEntity(@RequestParam(value = "contractId") Integer contractId, @RequestParam(value = "mainVersion") Integer mainVersion);

    @GetMapping("/getContractMdmInfo")
    Result<ContractMdmInfoDTO> getContractMdmInfo(@RequestParam(value = "contractCode") String contractCode);

    @GetMapping("/getOriginalContractType")
    Result<Integer> getOriginalContractType(@RequestParam(value = "contractId") Integer contractId);
}
