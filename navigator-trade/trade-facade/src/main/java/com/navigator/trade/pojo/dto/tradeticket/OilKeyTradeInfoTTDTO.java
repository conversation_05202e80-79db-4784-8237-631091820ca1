package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.trade.pojo.dto.future.OilPriceDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
@Data
@Accessors(chain = true)
public class OilKeyTradeInfoTTDTO extends CommonTradeInfoDTO{

    @ApiModelProperty(value = "价格详情")
    @Valid
    private OilPriceDetailDTO oilPriceDetailDTO;
}
