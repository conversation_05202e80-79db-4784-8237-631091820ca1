package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同关闭类型
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/27
 */

@Getter
@AllArgsConstructor
public enum ContractCloseTypeEnum {

    SIGN_CLOSE(0, "协议关闭","SIGN_CLOSE"),
    EXECUTE_CLOSE(1, "执行关闭","EXECUTE_CLOSE"),
    TAIL_CLOSE(2, "尾量关闭","TAIL_CLOSE");


    int type;
    String desc;
    String code;
}
