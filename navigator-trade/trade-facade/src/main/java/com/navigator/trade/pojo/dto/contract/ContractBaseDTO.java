package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.enums.ContractReviewStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 合同基础的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ContractBaseDTO {
    /**
     * 补充信息-标签ID集合
     */
    private List<Integer> tagConfigIdList;
    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 协议id
     */
    private Integer contractSignId;
    /**
     * 合同code
     */
    private String contractCode;

    /**
     * 合同UuId
     */
    private String contractUuId;

    /**
     * 合同状态
     */
    private Integer contractStatus;

    /**
     * 审核状态
     * {@link ContractReviewStatusEnum}
     */
    private Integer reviewStatus;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * TT 编码
     */
    private String ttCode;

    /**
     * TT 审批类型
     */
    private Integer ttApprovalType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 作废原因
     */
    private String invalidReason;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 是否是NonFrame 0:不是 1:是
     */
    private Boolean nonFrame;

    /**
     * 文件id列表
     */
    private Integer[] fileIdList;

    /**
     * 客户合同号
     */
    private String customerContractCode;

    /**
     * 是否是stf
     */
    private Boolean stf;

    /**
     * 请求系统 1:magellan 2:columbus
     */
    private Integer system;
}
