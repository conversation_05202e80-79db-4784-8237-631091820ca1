package com.navigator.trade.pojo.dto.contract;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class VerifyContractStructureNumDTO {

    //客户id
    private Integer customerId;
    //品类
    private Integer goodsCategoryId;
    //合约
    private String domainCode;
    //合同数量
    private BigDecimal contractNum;
    //主体
    private Integer companyId;
}
