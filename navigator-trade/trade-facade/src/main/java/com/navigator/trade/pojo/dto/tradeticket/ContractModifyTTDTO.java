package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.common.enums.ModifyTypeEnum;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.DeliveryTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;

public class ContractModifyTTDTO extends TTModifyEntity {
    String typeName;                // 变更类型
    String contractTypeName;        // 合同类型
    String paymentTypeName;         // 支付方式
    String deliveryTypeName;        // 运输方式
    String priceEndTypeName;        // 定价结束时间类型

    public String getTypeName() {
        return null == getType() ? "" : ModifyTypeEnum.getDescByValue(getType());
    }

    public String getContractTypeName() {
        return null == getContractType() ? "" : ContractTypeEnum.getDescByValue(getContractType());
    }

    public String getPaymentTypeName() {
        return null == getPaymentType() ? "" : PaymentTypeEnum.getDescByValue(getPaymentType());
    }

    public String getDeliveryTypeName() {
        return null == getDeliveryType() ? "" : DeliveryTypeEnum.getDescByValue(getDeliveryType());
    }

    public String getPriceEndTypeName() {
        return null == getPriceEndType() ? "" : ContractPriceEndTypeEnum.getDescByValue(getPriceEndType());
    }

}
