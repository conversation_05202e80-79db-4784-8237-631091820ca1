package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
@Deprecated
public enum DeliveryTypeEnum {
    /**
     * 交提货方式
     */
    FACTORY_TAKE(1, 1, "自提", "工厂自提"),
    SHIP_SPORT_TAKE(2, 1, "自提", "码头卖方船板自提(车船连运送到仓库)"),
    TRUCK_SEND(3, 2, "送货", "卡车送货至客户仓库"),
    PORT_TRUCK_TAKE(4, 1, "自提", "码头买方汽车板交货（我司代收代付起吊费）（买方去码头提货）"),
    SHIP_CPORT_TAKE(5, 1, "自提", "码头买方船板自提（达孚送货至码头，客户自己负责所有的码头费用）"),
    WAREHOUSE_RENT_TAKE(6, 1, "自提", "外租库买方汽车板交货（外租库自提）"),
    WAREHOUSE_DELIVERY_TAKE(7, 1, "自提", "交割库自提"),
    WAREHOUSE_DELIVERY_SEND(8, 2, "送货", "交割库送货"),
    WAREHOUSE_RENT_SEND(9, 2, "送货", "外租库买房汽车板 交货"),
    DONGGUAN_TRUCK_TKAE(11, 1, "自提", "东莞工厂汽车板 交货"),
    TRAIN_STATION_TAKE(12, 1, "自提", "火车站火车版自提"),
    KAGNDIHUAMEI_TRUCK_TKAE(13, 1, "自提", "康地华美工厂交货"),
    SHANGGAO_TRUCK_TKAE(14, 1, "自提", "江西上高双胞胎实业工厂买方汽车板 交货"),
    WANNIAN_TRUCK_TKAE(15, 1, "自提", "江西万年双胞胎牧业工厂买方汽车板 交货"),
    TANGRENSHEN_TRUCK_TKAE(16, 1, "自提", "江西万年双胞胎牧业工厂买方汽车板 交货"),


    ;

    private int value;
    /**
     * {@link DeliveryModeEnum}
     */
    private int mode;  //1：自提  2：送货
    private String modeName;

    private String desc;

    public static DeliveryTypeEnum getByValue(int value) {
        return Arrays.stream(values())
                .filter(deliveryTypeEnum -> Objects.equals(value, deliveryTypeEnum.getValue()))
                .findFirst()
                .orElse(FACTORY_TAKE);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static String getModeNameByValue(Integer value) {
        return getByValue(value).getModeName();
    }

}
