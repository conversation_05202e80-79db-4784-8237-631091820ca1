package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ContractModifyLogVO {
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     * */
    @ApiModelProperty(value = "合同来源")
    private String contractSource;

    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
}
