package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbt_contract_change_equity
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_change_equity")
@ApiModel(value = "ContractChangeEquityEntity对象", description = "dbt_contract_change_equity")
public class ContractChangeEquityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "申请编号")
    private String applyCode;

    @ApiModelProperty(value = "审批状态")
    private Integer approveStatus;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "变更前可转月次数")
    private Integer beforeAbleTransferTimes;

    @ApiModelProperty(value = "变更后可转月次数")
    private Integer afterAbleTransferTimes;

    @ApiModelProperty(value = "变更前已转月次数")
    private Integer beforeTransferredTimes;

    @ApiModelProperty(value = "变更后已转月次数")
    private Integer afterTransferredTimes;

    @ApiModelProperty(value = "变更前可反点价次数")
    private Integer beforeAbleReversePriceTimes;

    @ApiModelProperty(value = "变更后可反点价次数")
    private Integer afterAbleReversePriceTimes;

    @ApiModelProperty(value = "变更前已反点价次数")
    private Integer beforeReversedPriceTimes;

    @ApiModelProperty(value = "变更后已反点价次数")
    private Integer afterReversedPriceTimes;

    @ApiModelProperty(value = "申请说明")
    private String remark;

    @ApiModelProperty(value = "申请人id")
    private Integer applyBy;

    @ApiModelProperty(value = "申请人")
    private String createdBy;

    @ApiModelProperty(value = "申请时间")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "客户id")
    @TableField(exist = false)
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    @TableField(exist = false)
    private String customerName;

}
