package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-26 17:38
 */
@Data
@Accessors(chain = true)
public class ContractSignFileVO {
    /**
     * 协议ID
     */
    @ApiModelProperty(value = "协议ID")
    private Integer contractSignId;
    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议编号")
    private String protocolCode;
    /**
     * TT类型
     */
    @ApiModelProperty(value = "TT类型")
    private Integer ttType;
    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型")
    private String ttTypeInfo;
    /**
     * 双签文件地址
     */
    @ApiModelProperty(value = "双签文件地址")
    private String signaturePdfUrl;
    /**
     * 是否完成双签，未完成则为待出具
     */
    @ApiModelProperty(value = "是否完成双签，未完成则为待出具")
    private Boolean signatureFinish;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 文件类型
     * {@link com.navigator.common.enums.FileCategoryType}
     */
    @ApiModelProperty(value = "文件类型")
    private Integer fileCategoryType;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件详情")
    List<ContractSignDetailFileVO> contractSignDetailFileVOS;
    @ApiModelProperty(value = "是否显示行")
    private Integer colNotShow = 0;
    @ApiModelProperty(value = "路径")
    private String path;
}
