package com.navigator.trade.pojo.dto.future;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class DomainPriceQueryDTO {
    private Integer categoryId;
    private String categoryCode;
    private String domainCode;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

}
