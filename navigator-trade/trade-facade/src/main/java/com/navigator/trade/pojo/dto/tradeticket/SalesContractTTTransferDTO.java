package com.navigator.trade.pojo.dto.tradeticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.BuCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/24 19:02
 */
@Data
@Accessors(chain = true)
public class SalesContractTTTransferDTO extends TTCommonDTO {


    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    @ApiModelProperty(value = "合同编号")
    private String sonContractCode;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同ID")
    private Integer sonContractId;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "变更类型（1、转月 2 部分转月 3、反点价4、部分反点价）")
    private Integer type;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "原合约")
    private String originalDomainCode;

    @ApiModelProperty(value = "原合同暂定价")
    private BigDecimal tempPrice;

    @ApiModelProperty(value = "原合同基差价")
    private BigDecimal diffPrice;

    @ApiModelProperty(value = "本次转月手续费")
    private BigDecimal thisTimeFee;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "定价价格/价差")
    private BigDecimal price;

    @ApiModelProperty(value = "备注（原暂定价-基差）")
    private String memo;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

    private String userId;

    @ApiModelProperty(value = "修改内容Json")
    private String modifyContent;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "品种id")
    private Integer categoryId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "合同负责人")
    private Integer ownerId;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "卖方主体收款账号Id")
    private Integer supplierAccountId;


    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "补充协议类型 -1:原合同TT不审批不生成协议   0: 普通拆分(新合同) 1: 数量变更补充协议(原合同)")
    private Integer addedSignatureType = 0;

    @ApiModelProperty(value = "修改前后字段")
    private String content;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "申请单Id")
    private Integer PriceApplyId;

    @ApiModelProperty(value = "分配单id")
    private Integer priceAllocateId;

    @ApiModelProperty(value = "仓单交易类型 1.交易所交割仓单 2.线下交易所仓单 3.交易所仓单交易平台")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "作价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;
    //多品类V1 转月生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;
    //多品类V1 转月生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end

    /**添加两个仓单的属性**/
    @ApiModelProperty(value = "业务类型")
    private String buCode;
}
