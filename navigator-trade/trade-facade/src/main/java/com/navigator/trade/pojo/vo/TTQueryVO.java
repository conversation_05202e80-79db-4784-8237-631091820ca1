package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class TTQueryVO {
    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "协议编号")
    private String protocolCode;

    @ApiModelProperty(value = "协议状态")
    private String protocolStatus;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Split（变更）3、Transfer（变更包含转厂执行） 4、resale（回购再重售）5、washout（解约定赔）")
    private Integer tradeType;

    @ApiModelProperty(value = "TT类型（1、销售合同新增；2、销售合同变更；3、销售合同回购；4、点价申请）")
    private Integer type;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）")
    private Integer approvalStatus;

    @ApiModelProperty(value = "失效原因")
    private String invalidReason;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "是否可作废判断条件(0:不可作废 1: 可作废)")
    private Integer invalidStatus;

    @ApiModelProperty(value = "是否可撤回判断条件(0:不可撤回 1: 可撤回)")
    private Integer cancelStatus;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "来源标识：1原合同 2新合同")
    private Integer sourceFlag = 2;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private String goodsPackageName;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private String goodsSpecName;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private String shipWarehouseName;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private String deliveryType;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "总数量")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;

    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "来源类型: 1.保存 2.提交（默认）")
    private Integer sourceType;

    // V3新增字段
    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date WriteOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date WriteOffEndTime;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value ="仓单ID")
    private Integer warrantId;

    @ApiModelProperty(value ="仓单Code")
    private String warrantCode;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "是否豆二")
    private Integer isSoybean2;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购合同ID")
    private Integer purchaseContractId;


}
