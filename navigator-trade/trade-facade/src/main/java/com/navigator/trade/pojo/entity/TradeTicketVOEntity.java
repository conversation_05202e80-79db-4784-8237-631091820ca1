package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * tt视图
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_tradeticket")
@ApiModel(value = "TradeTicketEntity对象", description = "TT申请表")
public class TradeTicketVOEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "TT类型")
    private Integer type;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "tt状态（1、新录入 2、审批中 3、待修改提交 4、已取消 5 已完成）")
    private Integer status;

    @ApiModelProperty(value = "审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批类型")
    private Integer approvalType;

    @ApiModelProperty(value = "合同状态（1、未生效 2、生效中  3、修改中 8、已完成 9、已作废）")
    private Integer contractStatus;

    @ApiModelProperty(value = "协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）")
    private Integer contractSignatureStatus;

    @ApiModelProperty(value = "操作来源（0系统 1用户）")
    private Integer operationSource;

    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    private Integer tradeType;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    private Integer ownerId;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "商品种类")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "商品子类")
    private Integer subGoodsCategoryId;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "买方客户编号")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户编号")
    private String supplierCode;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "银行Id")
    private Integer bankId;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "关联id")
    private String groupId;

    @ApiModelProperty(value = "源合同信息")
    private Integer sourceContractId;

    @ApiModelProperty(value = "变更前合同量")
    private BigDecimal beforeContractNum;

    @ApiModelProperty(value = "变更合同量")
    private BigDecimal changeContractNum;

    @ApiModelProperty(value = "变更后合同量")
    private BigDecimal afterContractNum;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "供应商客户集团名称")
    private String supplierEnterpriseName;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "商品Id")
    private Integer goodsId;

    @ApiModelProperty(value = "货品全称")
    private String goodsName;

    @ApiModelProperty(value = "货品昵称")
    private String commodityName;

    @ApiModelProperty(value = "主体Id")
    private Integer companyId;

    @ApiModelProperty(value = "来源类型: 1.保存 2.提交（默认）")
    private Integer sourceType;

    // V3新增字段
    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;

    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value ="仓单ID")
    private Integer warrantId;

    @ApiModelProperty(value ="仓单Code")
    private String warrantCode;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
    @ApiModelProperty(value = "公司名称")
    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(value = "是否豆二")
    @TableField(exist = false)
    private Integer isSoybean2;

    @ApiModelProperty(value = "发货库点名称")
    @TableField(exist = false)
    private String shipWarehouseName;

    @ApiModelProperty(value = "袋皮扣重名称")
    @TableField(exist = false)
    private String packageWeightName;

    @ApiModelProperty(value = "交提货方式")
    @TableField(exist = false)
    private String deliveryTypeName;
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end

}
