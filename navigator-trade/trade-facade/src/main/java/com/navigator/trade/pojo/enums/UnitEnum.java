package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UnitEnum {
    /**
     * 单位
     */
    TON(1, "T", "吨", "001", 1),
    KG(2, "KG", "千克", "001", 1),
    POUND(3, "LB", "磅", "001", 1),
    OUNCE(4, "OZ", "盎司", "001", 1),
    BAG(5, "B", "袋", "001", 0),
    ;

    int unitTpe;
    String symbol;
    String desc;
    String lkgCode;
    int isWeight;


    private static UnitEnum getByValue(Integer value) {
        if (null == value) return UnitEnum.TON;
        for (UnitEnum en : UnitEnum.values()) {
            if (value == en.getUnitTpe()) {
                return en;
            }
        }
        return UnitEnum.TON;
    }

    public static UnitEnum getByName(String unit) {
        if (null == unit) return UnitEnum.TON;
        for (UnitEnum en : UnitEnum.values()) {
            if (unit.equals(en.name())) {
                return en;
            }
        }
        return UnitEnum.TON;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static String getDescByName(String unit) {
        return getByName(unit).getDesc();
    }
}
