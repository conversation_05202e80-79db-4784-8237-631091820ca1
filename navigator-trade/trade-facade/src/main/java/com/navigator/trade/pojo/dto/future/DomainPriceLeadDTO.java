package com.navigator.trade.pojo.dto.future;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-03-21 23:19
 */
@Data
@Accessors(chain = true)
public class DomainPriceLeadDTO {
    private Integer id;

//    @Excel(name = "品种", orderNum = "1")
    private String categoryName;

    @Excel(name = "期货代码", orderNum = "2")
    private String categoryCode;

    private Integer categoryId;

    @Excel(name = "合约年月", orderNum = "3")
    private String domainCode;

    @Excel(name = "收盘价", orderNum = "4")
    private BigDecimal price;

//    @Excel(name = "日期", orderNum = "5")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private Date tradeDate;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    /**
     * 上传人
     */
    private String uploadBy;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;
    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    private String sourceFileName;

    private String sourceFilePath;

    /**
     * 异常描述
     */
    private String abnormalDesc;

    private Boolean abnormalJudge;
}
