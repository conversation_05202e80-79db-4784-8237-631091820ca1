package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "navigator-trade-service")
public interface RedisFacade {
    @GetMapping("/saveRedis")
    Result saveRedis(@RequestParam("version") String version);

    @GetMapping("/reloadRedis")
    Result reloadRedis(@RequestParam("version") String version);

    @GetMapping("/updateRedis")
    Result updateRedis(@RequestParam("key") String key, @RequestParam("value") String value);

    @GetMapping("/getLockValue")
    Result getLockValue(@RequestParam("key") String key);

    @GetMapping("/insertRedis")
    Result insertRedis(@RequestParam("key") String key);

    @GetMapping("/rollBackRedis")
    Result rollBackRedis(@RequestParam("key") String key);

    @GetMapping("/getByRedisKey")
    Result getByRedisKey(@RequestParam("key") String key);
}
