package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.TTPriceEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 定价单的VO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ConfirmPriceDTO extends TTPriceEntity {

    String fixPriceNo = "";

    public String getFixPriceNo() {
        //FP-20220101101112-10001
        //return "FP-" + DateTimeUtil.formatDateTimeValue(getCreatedAt()) + "-" + getId().toString();
        return getTtId().toString();
    }
}
