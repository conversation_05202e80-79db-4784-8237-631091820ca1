package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.bisiness.enums.TTTranferTypeEnum;
import com.navigator.trade.pojo.entity.TTTranferEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;

public class ContractTransferTTDTO extends TTTranferEntity {
    String contractTypeName;        // 合同类型
    String typeName;                // 转月类型


    public String getContractTypeName() {
        return null == getContractType() ? "" : ContractTypeEnum.getDescByValue(getContractType());
    }

    public String getTypeName() {
        return null == getType() ? "" : TTTranferTypeEnum.getDescByValue(getType());
    }

}
