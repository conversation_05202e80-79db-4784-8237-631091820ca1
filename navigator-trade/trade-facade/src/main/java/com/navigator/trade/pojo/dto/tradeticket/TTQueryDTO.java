package com.navigator.trade.pojo.dto.tradeticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TTQueryDTO {
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "品种")
    private String goodsCategoryId;

    @ApiModelProperty(value = "3级品种")
    private String category3;

    @ApiModelProperty(value = "tt状态")
    private String status;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "TT类型")
    private String type;

    private String createStartTime;

    private String createEndTime;

    @ApiModelProperty(value = "合同销售类型（1：采购 2：销售）")
    private Integer salesType;

    @ApiModelProperty(value = "交易类型")
    private List<String> tradeType;

    private String userId;

    @ApiModelProperty(value = "开始交货时间")
    private String deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    private String deliveryEndTime;

    @ApiModelProperty(value = "业务编号")
    private String bizCode;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "卖方名称")
    private String supplierName;

    @ApiModelProperty(value = "交货开始月份")
    private String deliveryStartDate;

    @ApiModelProperty(value = "交货截止月份")
    private String deliveryEndDate;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private String goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private String goodsSpecId;

    @ApiModelProperty(value = "交货工厂编码")
    private List<String> deliveryFactoryCode;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private String shipWarehouseId;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "主体Id")
    private String companyId;

    private String supplierId;

    private String customerId;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "账套id")
    private String siteCode;

    @ApiModelProperty(value = "货品名称，模糊匹配")
    private String goodsName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "合约")
    private String domainCode;

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
    @ApiModelProperty(value = "分页开始行")
    private Integer startRow;

    @ApiModelProperty(value = "分页结束行")
    private Integer pageSize;

    @ApiModelProperty(value = "买方集团用户id集合")
    private List<Integer> customerEnterpriseIds;

    @ApiModelProperty(value = "卖方集团用户id集合")
    private List<Integer> supplierEnterpriseIds;

    @ApiModelProperty(value = "账套集合")
    private List<String> siteCodeList;
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end
}
