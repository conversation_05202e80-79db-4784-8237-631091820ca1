package com.navigator.trade.pojo.qo;

import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 合同域查询相关的QO对象
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
public class ContractQO implements Serializable {

    @ApiModelProperty("采销类型")
    private Integer salesType;

    @ApiModelProperty("合同编号")
    private String contractCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty("交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "卖方主体")
    private String supplierName;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "合约")
    private String domainCode;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "二级品类")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "品种")
    private Integer category3;

    @ApiModelProperty(value = "合同状态（1、未生效 2、生效中  3、修改中 8、已完成 9、已作废）")
    private Integer status;

    @ApiModelProperty(value = "签订日期")
    private String signDate;

    @ApiModelProperty(value = "签订开始日期")
    private String signStartDate;

    @ApiModelProperty(value = "签订结束日期")
    private String signEndDate;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "开始交货时间")
    private String deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    private String deliveryEndTime;

    @ApiModelProperty(value = "注销开始时间")
    private String writeOffStartTime;

    @ApiModelProperty(value = "注销结束时间")
    private String writeOffEndTime;

    private ContractStructureDTO contractStructureDTO;

    @ApiModelProperty(value = "触发系统")
    private String triggerSys;

    @ApiModelProperty(value = "操作人Id")
    private String operatorId;

    @ApiModelProperty(value = "是否已定价")
    private Integer priceComplete;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "可反点价次数")
    private Integer ableReversePriceTimes;

    private String companyId;

    @ApiModelProperty(value = "卖方主体")
    private String supplierId;

    @ApiModelProperty(value = "卖方主体")
    private String customerId;

    @ApiModelProperty(value = "是否尾量关闭 0否 1是")
    private Integer tailClosing;

    @ApiModelProperty(value = "哥伦布主体id")
    private Integer columbusCustomerId;

    @ApiModelProperty(value = "是否可反点价 0.否 1.是")
    private Integer isReversePrice;

    @ApiModelProperty(value = "权限信息")
    private Map<Integer, List<Integer>> companyCustomerIdMap;

    private List<String> siteCodeList;

    /**
     * 主要的现货和仓单的区分字段
     */
    @ApiModelProperty(value = "业务线,默认是现货合同：仓单参数 WT , 现货参数 ST")
    private String buCode;

    @ApiModelProperty(value = "合同性质")
    /**
     * {@link com.navigator.bisiness.enums.ContractNatureEnum}
     */
    private Integer contractNature;

    @ApiModelProperty(value = "合同注销状态（1、未注销 2.注销中 3.已注销）")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "仓单编号/注册号")
    private String warrantCode;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "可注销量>0,选择是、否")
    private Integer isWriteOffNum;
    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;
    @ApiModelProperty(value = "货品名称，模糊匹配")
    private String goodsName;
    @ApiModelProperty(value = "发货库点ID")
    private Integer shipWarehouseId;
}
