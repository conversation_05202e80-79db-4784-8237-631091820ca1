package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.trade.pojo.enums.ContractSignTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-02-25 15:24
 */
@Data
@Accessors(chain = true)
public class TemplateConditionDTO {
    // ----------M条款是否展示的判断条件（合同模版配置）
    /**
     * 变更的字段（M条款是否展示）
     */
    private List<String> modifyList;
    /**
     * 原合同类型
     */
    private Integer originalContractType;
    // ----------E展示的判断条件（M条款配置）
    /**
     * {@link com.navigator.trade.pojo.enums.ContractTypeEnum}
     * 合同类型（1一口价 2基差 3暂定价 4基差暂定价 5 结构化定价）
     */
    private Integer contractType;
    /**
     * 采销方式
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;
    /**
     * 合同提货类型 1：自提  2：送货
     * {@link com.navigator.trade.pojo.enums.DeliveryModeEnum}
     */
    private Integer deliveryType;
    /**
     * 付款方式(有赊销天数则为赊销；1赊销2预付款)
     * {@link PaymentTypeEnum}
     */
    private Integer paymentType;
    /**
     * 交货工厂编码(ZJG、YZ、ZS、LY、DG、ZZY)
     */
    private String deliveryFactoryCode;
    /**
     * 点价截止日期类型（1时间 2文本）
     */
    private Integer priceEndType;
    /**
     * 履约保证金
     */
    private Integer depositAmount = 0;
    /**
     * 点价后履约保证金补缴
     */
    private Integer addedDepositRate = 5;
    /**
     * 未定价量
     */
    private BigDecimal notPriceNum = BigDecimal.ZERO;
    /**
     * 全部/部分拆分
     */
    private Integer actionType = 1;
    /**
     * 1 部分 2 全部
     * {@link com.navigator.trade.pojo.enums.SplitTypeEnum}
     * 合同拆分数量<合同未开单量
     */
    private Integer splitType = 1;
    /**
     * 品类
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */

    private Integer goodsCategoryId;
    /**
     * 规格id
     */
    private Integer specId;
    /**
     * 1: ==0 2: >0  3: <0
     * <p>
     * {@link com.navigator.trade.pojo.enums.CompareUnitPriceEnum}
     * 解约定赔.含税单价-解约定赔.新含税单价
     */
    private Integer washOutDiffUnitPriceType = 0;

    /**
     * 1:涉及 2:不涉及
     * {@link com.navigator.trade.pojo.enums.TransferFactoryEnum}
     * 是否涉及转场
     */
    private Integer transferFactory = 2;
    /**
     * 解约定赔
     * 解约定赔.含税单价-解约定赔.新含税单价
     */
    private BigDecimal washOutDiffUnitPrice = BigDecimal.ZERO;
    /**
     * 1 尾量 0 补充协议
     * {@link ContractSignTypeEnum}
     */
    private Integer signType;
    /**
     * 履约保证金释放方式
     * {@link DepositUseRuleEnum}
     */
    private Integer depositReleaseType;
    /**
     * 结构化定价类型
     */
    private Integer structureType;
    /**
     * 发票后补缴货款比例
     */
    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单）")
    private String protocolTypeCondition;

    /**
     * 所属集团客户编码
     */
    private String enterpriseCode;

    /**
     * 目的港编码
     */
    private String destinationCode;

    /**
     * 是否为正大集团特殊交货地点客户
     */
    private Integer isZDSpecialDeliveryCustomer;

    private Integer category1;

    private Integer category2;

    private Integer category3;

    /**
     * {@link  com.navigator.bisiness.enums.SettleType}
     * 1,交易所结算  2,自行结算
     */
    @ApiModelProperty(value = "结算方式")
    private Integer settleType;

    /**
     * {@link  com.navigator.bisiness.enums.DepositPaymentType}
     * 1,保函  2,现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;
    /**
     * {@link WarrantCategoryEnum}
     * 仓单类型 1，厂库仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;
    /**
     * 仓单属性: 1自由仓单；2仓单
     * {@link WarrantPropertyEnum}
     */
    @ApiModelProperty(value = "仓单属性")
    private Integer warrantProperty;
    /**
     * 货品SPU名称
     */
    private String spuName;
    /**
     * 包装名称
     */
    private String packageName;
}
