package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/11/29 13:39
 */
@Getter
@AllArgsConstructor
public enum TTApproveStatusEnum {

    /**
     * TT审批状态
     */
    WITHOUT_APPROVE(0, "无需审批"),
    WAIT_A_SIGN(1, "待A签"),
    WAIT_B_SIGN(2, "待B签"),
    WAIT_C_SIGN(3, "待C签"),
    REJECT(4, "审批驳回"),
    APPROVE(5, "审批通过"),
    ;

    int value;
    String desc;

    public static TTApproveStatusEnum getByValue(int value) {
        for (TTApproveStatusEnum statusEnum : TTApproveStatusEnum.values()) {
            if (value == statusEnum.getValue()) {
                return statusEnum;
            }
        }
        return TTApproveStatusEnum.WAIT_A_SIGN;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
