package com.navigator.trade.pojo.dto.contractsign;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContractSignErrorDTO {

    @ApiModelProperty(value = "协议唯一编号")
    private String uuid;

    @ApiModelProperty(value = "签章错误编码")
    private String signErrorCode;

    @ApiModelProperty(value = "签章错误信息")
    private String signErrorMessage;


}
