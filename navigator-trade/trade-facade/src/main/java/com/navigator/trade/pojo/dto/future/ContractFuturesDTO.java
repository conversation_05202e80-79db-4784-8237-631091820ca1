package com.navigator.trade.pojo.dto.future;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 11:12
 */
@Data
@Accessors(chain = true)
public class ContractFuturesDTO {

    //客户id
    private String customerId;
    //品种id
    private Integer goodsCategoryId;
    //二级品类
    private Integer category2;
    //三级品类
    private Integer category3;
    //可点价量
    private BigDecimal priceNum;
    //期货编号
    private String futureCode;
    //期货合约
    private String domainCode;
    //合同类型（1.采购 2.销售）
    private Integer salesType;
    //操作系统
    private Integer system;
    //点价类型
    private Integer priceType;
    //买方id
    private String supplierId;
    //申请单id
    private String applyId;
    //员工id
    private String employId;
    //合同类型
    private List<Integer> contractType;
    //查询类型
    private Integer status = 0;
    //结构化定价开始日
    private Timestamp priceBeginTime;
    //主体id
    private Integer companyId;
    //主体ids
    private List<Integer> companyIds;
    //业务线
    private String buCode;

}
