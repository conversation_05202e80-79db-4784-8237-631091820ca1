package com.navigator.trade.pojo.dto.tradeticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SubmitTTBatchDTO {
    private List<Integer> ttIdList;
    /**
     * 提交状态 1:新增时的提交 2. 待修改区的提交
     */
    private String submitStatus;


    private String ttProcessor;
    private String userId;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "tt类型")
    private Integer type;

    @ApiModelProperty(value = "品种")
    private String goodsCategoryId;

    /**
     * 剩余风险控制：是否强制提交（客户交易状态为CBT/INACTIV/ACTIVE，且RR Residue < 0 && RR Usage ≤ 150 kUSD，可强制提交 ）
     */
    private Boolean residualRiskForceSubmit;

}
