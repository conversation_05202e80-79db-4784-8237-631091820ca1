package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IsOnLineSignEnum {

    /**
     * 是否线上签章
     *
     */

    OFFLINE(0, "线下"),
    ON_LINE(1, "线上"),

    ;

    private int value;
    private String desc;

    public static IsOnLineSignEnum getByType(Integer value) {
        if (null == value) return IsOnLineSignEnum.OFFLINE;
        for (IsOnLineSignEnum typeEnum : IsOnLineSignEnum.values()) {
            if (typeEnum.getValue() == value) {
                return typeEnum;
            }
        }
        return IsOnLineSignEnum.OFFLINE;
    }

    public static String getDescByValue(Integer value) {
        return getByType(value).desc;
    }
}
