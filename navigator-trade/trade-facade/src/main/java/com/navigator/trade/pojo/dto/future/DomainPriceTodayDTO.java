package com.navigator.trade.pojo.dto.future;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/7 10:04
 */

@Data
@Accessors(chain = true)
public class DomainPriceTodayDTO {

    //申请单id
    private Integer priceDealDetailId;
    //收盘价状态
    private Integer auditStatus;
    //品类id
    private Integer categoryId;
    // 期货代码
    private Integer categoryCode;
}
