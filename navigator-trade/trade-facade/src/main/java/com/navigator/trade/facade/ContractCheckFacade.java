package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contract.ContractCheckDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 合同校验的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/25
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractCheckFacade {
    /**
     * 校验合同数据
     *
     * @param contractCheckDTO
     */
    @PostMapping("/contract/checkContract")
    Result checkContract(@RequestBody ContractCheckDTO contractCheckDTO);

    /**
     * 校验lkg数据
     *
     * @param contractCheckDTO
     */
    @PostMapping("/contract/checkLkgContract")
    Result checkLkgContract(@RequestBody ContractCheckDTO contractCheckDTO);

    /**
     * 校验重试
     *
     * @param contractCheckDTO
     */
    @PostMapping("/contract/retryCheckContractByBatch")
    Result retryCheckContractByBatch(@RequestBody ContractCheckDTO contractCheckDTO);

    /**
     * 每日校验
     *
     * @return
     */
    @GetMapping("/contract/checkDailyContract")
    Result checkDailyContract();
}
