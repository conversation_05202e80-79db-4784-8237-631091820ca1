package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 正本快递信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
@TableName("dbt_contract_paper")
@ApiModel(value = "ContractPaperEntity对象", description = "正本快递信息表")
public class ContractPaperEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "协议id")
    private Integer contractSignId;

    @ApiModelProperty(value = "协议id")
    private Integer contractSignCode;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "客户正本要求")
    private String customerRequirement;

    @ApiModelProperty(value = "LDC物流公司名称")
    private String ldcDeliveryCompany;

    @ApiModelProperty(value = "LDC物流单单号")
    private String ldcDeliverySn;

    @ApiModelProperty(value = "LDC寄出份数")
    private Integer ldcPaperNum;

    @ApiModelProperty(value = "LDC寄出时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mailGoTime;

    @ApiModelProperty(value = "客户物流公司名称")
    private String customerDeliveryCompany;

    @ApiModelProperty(value = "客户物流单单号")
    private String customerDeliverySn;

    @ApiModelProperty(value = "客户寄出份数")
    private Integer customerPaperNum;

    @ApiModelProperty(value = "客户寄回时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mailReturnTime;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;


}
