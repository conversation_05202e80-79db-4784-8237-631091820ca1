package com.navigator.trade.pojo.dto.future;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@Accessors(chain = true)
public class OilPriceDetailDTO {
    @ApiModelProperty(value = "基差价（元）")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String extraPrice;

    @ApiModelProperty(value = "期货价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String forwardPrice;

    @ApiModelProperty(value = "精炼价差")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String refineDiffPrice;

    @ApiModelProperty(value = "期权费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String optionPrice;

    @ApiModelProperty(value = " 其他物流费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String otherDeliveryPrice;

    @ApiModelProperty(value = "运费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String transportPrice;


    @ApiModelProperty(value = "滞期费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String delayPrice;

    @ApiModelProperty(value = " 回购折价/解约定赔折价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String buyBackPrice;

    @ApiModelProperty(value = "客诉折价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String complaintDiscountPrice;

    @ApiModelProperty(value = " 转厂补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String transferFactoryPrice;

    @ApiModelProperty(value = "其他补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String otherPrice;

    @ApiModelProperty(value = "手续费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String fee;

}
