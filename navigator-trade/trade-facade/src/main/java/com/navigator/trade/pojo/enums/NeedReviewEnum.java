package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/27
 */
@Getter
@AllArgsConstructor
public enum NeedReviewEnum {
    NO_REVIEW(0, "不需要审核"),
    REVIEW(1, "需要审核");

    Integer value;
    String desc;

    public static NeedReviewEnum getByType(Integer value) {
        for (NeedReviewEnum type : NeedReviewEnum.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        for (NeedReviewEnum type : NeedReviewEnum.values()) {
            if (type.value == value) {
                return type.desc;
            }
        }
        return null;
    }
}
