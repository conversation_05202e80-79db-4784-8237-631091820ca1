package com.navigator.trade.pojo.dto.tradeticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: No Description
 * Created by <PERSON><PERSON>ong on 2022/1/25 16:44
 */
@Data
@Accessors(chain = true)
public class SalesContractTTPriceDTO extends TTCommonDTO {

    @ApiModelProperty(value = "ttid")
    private Integer ttId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "申请id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "分配单id(收盘价记录ID)")
    private Integer allocateId;

    @ApiModelProperty(value = "操作类型（1.点价  2.定价 3结构化定价 4暂定价-基差暂定价）")
    private Integer type;

    @ApiModelProperty(value = "定价量")
    private BigDecimal num;

    @ApiModelProperty(value = "原合同暂定价")
    private BigDecimal tempPrice;

    @ApiModelProperty(value = "原合同基差价")
    private BigDecimal diffPrice;

    @ApiModelProperty(value = "价格明细")
    private String contractPriceDetail;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "定价价格/价差")
    private BigDecimal price;

    @ApiModelProperty(value = "定价时间")
    private Date priceTime;

    @ApiModelProperty(value = "备注（原暂定价-基差）")
    private String memo;

    @ApiModelProperty(value = "仓单交易类型 1.交易所交割仓单 2.线下交易所仓单 3.交易所仓单交易平台")
    private Integer warrantTradeType;

    //仓单编码
    private String warrantCode;
    //仓单id
    private Integer warrantId;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    // 合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）)
    private Integer contractType;

    private String userId;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "卖方主体收款账号Id")
    private Integer supplierAccountId;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "剩余未点价量")
    private BigDecimal remainPriceNum;

    @ApiModelProperty(value = "已定价量")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "原点价量")
    private BigDecimal originalPriceNum;

    @ApiModelProperty(value = "加权平均价")
    private BigDecimal avePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "最终合同价格")
    private BigDecimal endContractPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "最终全额货款")
    private BigDecimal endAllPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "本次转月手续费")
    private BigDecimal thisTimeFee;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    private Integer ownerId;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "作价截止时间")
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String priceEndTime;

    @ApiModelProperty(value = "当前合同数量")
    private BigDecimal thisContractNum;
    //多品类V1 转月生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;
    //多品类V1 转月生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "业务类型")
    private String buCode;



}
