package com.navigator.trade.pojo.dto.tradeticket;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.trade.pojo.dto.validator.TTDTOGroupSequenceProvider;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.group.GroupSequenceProvider;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: Neo
 * @date: 2024-08-13
 * @description:新增TT的信息
 */
@Data
@Accessors(chain = true)
@GroupSequenceProvider(TTDTOGroupSequenceProvider.class)
public class OMContractAddTTDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 点价合同）")
    @NotBlank
    private String contractType;

    /**
    * {@link com.navigator.bisiness.enums.ContractNatureEnum}
    */
    @ApiModelProperty(value = "合同性质（1 一口价合同 2 基差合同 3 点价合同）")
    private Integer contractNature;

    @ApiModelProperty(value = "卖方客户ID")
    @NotBlank
    private String supplierId;

    @ApiModelProperty(value = "客户名称")
    @NotBlank
    private String customerName;

    @ApiModelProperty(value = "买方客户编号")
    private String customerCode;

    @ApiModelProperty(value = "买方客户ID")
    @NotBlank
    private String customerId;

    @ApiModelProperty(value = "品种")
    private String goodsCategoryId;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    @Min(0)
    private String goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    @Min(0)
    private String goodsSpecId;

    /**
     * {@link com.navigator.trade.pojo.enums.PaymentTypeEnum}
     */
    @ApiModelProperty(value = "赊销账期")
    @Min(0)
    @NotNull
    private Integer creditDays;

    /**
     * 在账套里面了不区分采销
     */
    @ApiModelProperty(value = "交货工厂编码")
    @NotBlank
    private String deliveryFactoryCode;

    /**
     * TODO 代码实现，采购特有的
     */
    @ApiModelProperty(value = "迟付款罚金")
    private String delayPayFine;

    /**
     * TODO 代码实现，采购特有的
     */
    @ApiModelProperty(value = "签订地")
    private String signPlace;

    /**
     * 在账套里面了不区分采销
     */
    @ApiModelProperty(value = "交货工厂名称")
    @NotBlank
    private String deliveryFactoryName;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @NotNull
    private Date signDate;

    @ApiModelProperty(value = "是否代加工，默认否（0、否 1、是）")
    @Min(0)
    private Integer oem;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    @Min(0)
    private Integer depositUseRule;

    @ApiModelProperty(value = "核心字段集-支撑多品类字段")
    @NotEmpty
    private List<@Valid KeyTradeInfoTTDTO> ttKernelDTOList;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    @NotNull
    private Integer deliveryType;

    @ApiModelProperty(value = "目的地/港")
    @NotBlank
    private String destination;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    @NotBlank
    private String weightCheck;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    @Min(0)
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    /**
     * TODO 账套带出来的必填不区分
     */
    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    @NotBlank
    private String shipWarehouseId;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    @Min(0)
    private Integer weightTolerance;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    @NotBlank
    private String ownerId;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    private Integer isStf;

    @ApiModelProperty(value = "用途")
    @NotNull
    private Integer usage;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT类型（1、合同新增；2、合同变更；3、合同回购；4、点价申请）")
    public Integer type;

    @ApiModelProperty(value = "完成度状态")
    private Integer completedStatus;

    @ApiModelProperty(value = "点价开始时间")
    private String priceStartTime;

    private Boolean createStatus;

    private String userId;

    /**
     * TODO 代码实现，采购必填
     */
    @ApiModelProperty(value = "采购卖方主体收款账号信息")
    private Integer supplierAccountId;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    private Integer tradeType;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    @ApiModelProperty(value = "协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）")
    private Integer contractSignatureStatus;

    @ApiModelProperty(value = "tt状态（1、新录入 2、审批中 3、待修改提交 4、已取消 5 已完成）")
    private Integer status;

    private Integer approvalStatus;

    private Integer approvalType;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "原合同ID")
    private Integer rootContractId;



    //****************************************************
    //******************兼容结构定价合同新增****************
    private BigDecimal structureTotalNum = BigDecimal.ZERO;
    private BigDecimal minPrice = BigDecimal.ZERO;
    private BigDecimal maxPrice = BigDecimal.ZERO;
    private Integer structureType = 1;
    private Integer totalDay = 1;
    private Integer unitNum = 0;
    private Date structurePriceStartTime;
    private Date structurePriceEndTime;
    private String domainCode;
    //****************************************************

    @ApiModelProperty(value = "提交类型 1.保存 2.提交(默认)")
    private Integer submitType;

    // V3 ==========================
    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "账套编码")
    @NotBlank
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    @NotBlank
    private String siteName;

    @ApiModelProperty(value = "一级品类")
    @NotNull
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    @NotNull
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    @NotNull
    private Integer category3;

    @ApiModelProperty(value = "商品Id")
    @NotNull
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    @NotBlank
    private String goodsName;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    /**
     * 付款方式(有赊销天数则为赊销；1赊销2预付款)
     * {@link PaymentTypeEnum}
     */
    private Integer paymentType;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    /**
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;

    /**
     * 1保函 2现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;

    @ApiModelProperty(value = "交割保证金金额")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String deliveryMarginAmount;

    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffEndTime;

    /**
     * TODO 代码实现 特种油脂必填
     */
    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    /**
     * TODO 代码实现 特种油脂必填
     */
    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

    @ApiModelProperty(value = "是否为豆二（0否;1是）")
    private Integer isSoybean2;


}
