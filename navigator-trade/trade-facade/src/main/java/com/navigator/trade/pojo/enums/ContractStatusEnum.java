package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 合同状态 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */

@Getter
@AllArgsConstructor
public enum ContractStatusEnum {
    /**
     * 合同状态
     */
    DRAFT(0, "草稿"),
    INEFFECTIVE(1, "未生效"),
    EFFECTIVE(2, "生效中"),
    MODIFYING(3, "修改中"),
    COMPLETED(4, "已完成"),
    INVALID(5, "已作废"),
    CLOSING(6, "关闭中"),
    CLOSED(7, "已关闭"),
    SPLITTING(8, "拆分中"),
    LKG_EXCEPTION(9, "lkg异常"),
    ;

    int value;
    String desc;

    public static ContractStatusEnum getByValue(Integer value) {
        if (null == value) return INEFFECTIVE;
        for (ContractStatusEnum statusEnum : ContractStatusEnum.values()) {
            if (value == statusEnum.getValue()) {
                return statusEnum;
            }
        }
        return ContractStatusEnum.INEFFECTIVE;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static List<Integer> getFinalContractStatus() {
        return Arrays.asList(COMPLETED.getValue(), INVALID.getValue(), CLOSED.getValue());
    }
}
