package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 合同定价状态 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */

@Getter
@AllArgsConstructor
public enum ContractPricedStatusEnum {
    /**
     * 合同定价状态
     */
    NOT_PRICED(1, "整体未定价"),
    PART_PRICED(2, "部分定价"),
    PRICED(3, "全部定价"),
    ;

    int value;
    String desc;

    public static ContractPricedStatusEnum getByValue(int value) {
        for (ContractPricedStatusEnum statusEnum : ContractPricedStatusEnum.values()) {
            if (value == statusEnum.getValue()) {
                return statusEnum;
            }
        }
        return ContractPricedStatusEnum.NOT_PRICED;
    }

}
