package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.navigator.common.util.PriceSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PriceDetailVO {

    @ApiModelProperty(value = "基差价（元）")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货价")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "蛋白价差")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal proteinDiffPrice;

    @ApiModelProperty(value = "散粕补贴")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal compensationPrice;

    @ApiModelProperty(value = "期权费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal optionPrice;

    @ApiModelProperty(value = "运费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal transportPrice;

    @ApiModelProperty(value = "起吊费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal liftingPrice;

    @ApiModelProperty(value = "滞期费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal delayPrice;

    @ApiModelProperty(value = " 高温费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal temperaturePrice;

    @ApiModelProperty(value = " 其他物流费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal otherDeliveryPrice;

    @ApiModelProperty(value = " 回购折价")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal buyBackPrice;

    @ApiModelProperty(value = "客诉折价")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal complaintDiscountPrice;

    @ApiModelProperty(value = " 转厂补贴")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal transferFactoryPrice;

    @ApiModelProperty(value = "其他补贴")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal otherPrice;

    @ApiModelProperty(value = "商务补贴")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal businessPrice;

    @ApiModelProperty(value = "手续费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal fee;

    @ApiModelProperty(value = "装运费单价")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal shippingFeePrice;

    @ApiModelProperty(value = "精炼价差")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal refineDiffPrice;

    @ApiModelProperty(value = "精炼/分提价差")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal refineFracDiffPrice;

    @ApiModelProperty(value = "检验费")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal surveyFees;

    @ApiModelProperty(value = "精炼价差")
    @JsonSerialize(using = PriceSerializer.class)
    private String previousRecord;

    @ApiModelProperty(value = "VE单价")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal vePrice;

    @ApiModelProperty(value = "VE含量")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal veContent;

}
