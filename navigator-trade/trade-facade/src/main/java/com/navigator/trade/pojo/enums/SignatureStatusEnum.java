package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/30 17:07
 */
@Getter
@AllArgsConstructor
public enum SignatureStatusEnum {

    SUCCEED(1,"发起成功"),
    LDC_AWAIT_SIGNATURE(2,"LDC等签章"),
    CUSTOMER_AWAIT_SIGNATURE(3,"客户待签章"),
    ALREADY_SIGNATURE(4,"签章完成"),
    STORAGE_FILE_BE_DEFEATED(5,"存储文件失败"),
    LDC_ALREADY_SIGNATURE(6,"LDC签章完成"),
    CUSTOMER_ALREADY_SIGNATURE(7,"客户签章完成"),
    ;

    private int type;
    private String desc;
}
