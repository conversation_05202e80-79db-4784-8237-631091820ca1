package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2022-06-23 14:50
 **/
@Getter
@AllArgsConstructor
public enum ContractSignTypeEnum {
    /**
     * 协议模版组装类型
     */
    ADDED_PROTOCOL(0, "补充协议"),
    REMAIN_NUM(1, "尾量协议"),
    PARTLY_SPLIT(2, "部分拆分"),
    ALL_SPLIT(3, "全量拆分"),
    ;

    private int value;
    private String desc;

    public static ContractSignTypeEnum getEnumByValue(Integer value) {
        return Arrays.stream(values())
                .filter(contractSignTypeEnum -> value == contractSignTypeEnum.getValue())
                .findFirst()
                .orElse(REMAIN_NUM);
    }
}
