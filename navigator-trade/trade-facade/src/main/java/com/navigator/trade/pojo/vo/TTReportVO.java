package com.navigator.trade.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class TTReportVO {
    private List<ReportVO> yiReportVOList;
    private List<ReportVO> jiReportVOList;
    private List<ReportVO> zanReportVOList;
    private BigDecimal unsubmitTotalSize;
    private BigDecimal approvingTotalSize;
    private BigDecimal doneTotalSize;
    private BigDecimal invalidTotalSize;
    private BigDecimal totalSize;
    private List<ReportVO> reportVOList;


}
