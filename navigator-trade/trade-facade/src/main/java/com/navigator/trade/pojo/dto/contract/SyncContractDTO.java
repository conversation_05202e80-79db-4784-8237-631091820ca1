package com.navigator.trade.pojo.dto.contract;

import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 同步合同的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class SyncContractDTO {
    /**
     * ttId
     */
    private Integer ttId;

    /**
     * 协议Id
     */
    private Integer signId;

    /**
     * 同步类型
     */
    private Integer syncType;

    /**
     * 新增：子合同 修改：父合同
     */
    private ContractEntity contractEntity;

    /**
     * 定价单合同id
     */
    private Integer priceContractId;

    /**
     * 更新子合同id
     */
    private Integer referContractId;

    /**
     * 交易场景
     */
    private Integer tradeType;

    /**
     * 定价单id
     */
    private Integer confirmPriceId;

    /**
     * 同步的系统
     */
    private String targetSyncSystem = SystemEnum.LKG.getName();

    /**
     * 操作人
     */
    private String operatorName;

}
