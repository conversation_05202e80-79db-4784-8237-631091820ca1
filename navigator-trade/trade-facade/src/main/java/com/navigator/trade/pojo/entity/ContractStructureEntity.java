package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_structure")
@ApiModel(value = "ContractStructureEntity对象", description = "")
public class ContractStructureEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "TT ID")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "点价申请单ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "卖方主体")
    private String supplierName;

    @ApiModelProperty(value = "结构类型，1:A结构 2:B结构 3:C结构")
    private Integer structureType = -1;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "总数量")
    private BigDecimal totalNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "1单位数量")
    private BigDecimal unitNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "结构化定价起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结构化定价结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "总交易日")
    private Integer totalDay = 0;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "已分配数量")
    private BigDecimal assignedNum = BigDecimal.ZERO;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractStructurePricingStatusEnum}
     */
    @ApiModelProperty(value = "定价状态 -1:合同未生效 0：未生效 1：定价中 2：定价完成")
    private Integer priceStatus = -1;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "签订日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date signDate;

    @ApiModelProperty(value = "单位增量")
    private BigDecimal unitIncrement;

    @ApiModelProperty(value = "现金返还量")
    private String cashReturn;

    @ApiModelProperty(value = "累积价格")
    private String cumulativePrice;

    @ApiModelProperty(value = "累计成交量")
    private BigDecimal cumulativeDealNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "累计成交量")
    private BigDecimal cumulativeNotDealNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "累计分配量")
    private BigDecimal cumulativeAllocateNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "累积返还金额")
    private BigDecimal cumulativeCashReturn = BigDecimal.ZERO;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "触发价格")
    private String triggerPrice;

    @ApiModelProperty(value = "触发价格")
    private BigDecimal cumulativeRelease;

    @ApiModelProperty(value = "结构化单位数量")
    private BigDecimal structureUnitNum;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂id")
    private Integer deliveryFactoryId;

    @ApiModelProperty(value = "定价开始日期")
    @TableField(exist = false)
    private Date priceStartDate;

    @ApiModelProperty(value = "定价到期日")
    @TableField(exist = false)
    private Date priceEndDate;

    @ApiModelProperty(value = "结构化名称")
    private String structureName;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "期货代码")
    @TableField(exist = false)
    private String futureCode;

}
