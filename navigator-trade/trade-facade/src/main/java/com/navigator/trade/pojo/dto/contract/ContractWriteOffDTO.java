package com.navigator.trade.pojo.dto.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同注销DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class ContractWriteOffDTO {


    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "父合同实体")
    private ContractEntity parentContractEntity;

    @ApiModelProperty(value = "合同类型")
    private Integer sonContractType;

    @ApiModelProperty(value = "子合同id")
    private Integer sonContractId;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "TT交易类型")
    private Integer ttTradeType;

    @ApiModelProperty(value = "TT类型")
    private Integer ttType;
    @ApiModelProperty(value = "SignId")
    private Integer signId;

    /**
     * 采销类型
     * {@link ContractSalesTypeEnum}
     */
    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    /**
     * 合同来源 【仓单合同注销】
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    private Integer contractSource;

    /**
     * 仓单注销修改产生的新字段 可变更字段
     **/

    @ApiModelProperty(value = "注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同")
    private int writeOffAction;

    @ApiModelProperty(value = "是否同时修改货品和提货方")
    private Integer isModifyAll;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    /**
     * 采购：客户是卖方；销售：主体是卖方
     */
    @ApiModelProperty(value = "卖方主体")
    private Integer supplierId;

    /**
     * 采购：主体是买方；销售：客户是买方
     */
    @ApiModelProperty(value = "买方主体|提货主体")
    private Integer customerId;

    @ApiModelProperty(value = "注销数量")
    private BigDecimal writeOffNum;

    @ApiModelProperty(value = "提货密码")
    private String deliveryPassword;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "注销货品")
    private Integer goodsId;

    @ApiModelProperty(value = "注销货品名称")
    private String goodsName;

    @ApiModelProperty(value = "注销日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDate;

    @ApiModelProperty(value = "注销交提货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDeliveryStartTime;

    @ApiModelProperty(value = "注销交提货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date writeOffDeliveryEndTime;

    @ApiModelProperty(value = "发货库点【交割库】")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "发货库点名称")
    private String shipWarehouseName;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价明细")
    private PriceDetailBO priceDetailDTO;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "目的地/港")
    private String destination;

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "注销说明")
    private String memo;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    /**** 线下交易所仓单合同  *****/

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer addedDepositRate;

    @ApiModelProperty(value = "应付履约保证金金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;


    /*************注销采购信息录入****************/
    @ApiModelProperty(value = "注销合同采购信息")
    private ContractWriteOffPurchaseDTO contractWriteOffPurchaseDTO;

    @ApiModelProperty(value = "提交类型 1.保存 2.提交(默认)")
    private Integer submitType = SubmitTypeEnum.SUBMIT.getValue();

    @ApiModelProperty(value = "协议是否可见，后端使用属性")
    private Integer isDelete;

    @ApiModelProperty(value = "修改内容Json")
    private String modifyContent;

    @ApiModelProperty(value = "修改前后字段")
    private String content;
}
