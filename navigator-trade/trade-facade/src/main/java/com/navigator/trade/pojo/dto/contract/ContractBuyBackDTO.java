package com.navigator.trade.pojo.dto.contract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 合同回购的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ContractBuyBackDTO {

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;

    @ApiModelProperty(value = "回购数量")
    private BigDecimal buyBackNum;

    @ApiModelProperty(value = "期货金额")
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "业务类型")
    private String buCode;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

}
