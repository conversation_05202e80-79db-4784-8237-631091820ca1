package com.navigator.trade.pojo.dto.tradeticket;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.navigator.bisiness.enums.BuCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TTCommonDTO {
    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer tradeType;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer salesType;

    /**
     * {@link com.navigator.bisiness.enums.ContractNatureEnum}
     */
    private Integer contractNature;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer contractSource;

    @ApiModelProperty(value = "协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer contractSignatureStatus;

    @ApiModelProperty(value = "tt状态（1、新录入 2、审批中 3、待修改提交 4、已取消 5 已完成）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer status;

    private Integer approvalStatus;

    private Integer approvalType;

    @ApiModelProperty(value = "TT编号")
    private String code;

    @ApiModelProperty(value = "原合同ID")
    private Integer rootContractId;

    @ApiModelProperty(value = "源合同ID(合同变更来源ID)")
    private Integer sourceContractId;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "买方客户编号")
    private String customerCode;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;
    @ApiModelProperty(value = "用途")
    private Integer usage;

    /**
     * 业务类型 仓单/现货
     * {@link BuCodeEnum}
     */
    private String buCode;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

}
