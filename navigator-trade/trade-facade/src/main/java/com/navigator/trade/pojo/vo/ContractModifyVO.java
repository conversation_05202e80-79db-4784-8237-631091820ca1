package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 变更合同的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class ContractModifyVO {
    @ApiModelProperty(value = "操作类型")
    private Integer ttType;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    private Integer tradeType;

    @ApiModelProperty(value = "合同量")
    private String contractNum;

    @ApiModelProperty(value = "定价量")
    private String priceNum;

    @ApiModelProperty(value = "TTId")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "tt状态（1、新录入 2、审批中 3、待修改提交 4、已取消 5 已完成）")
    private Integer ttStatus;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @ApiModelProperty(value = "子合同Id")
    private Integer sonContractId;

    @ApiModelProperty(value = "子合同编号")
    private String sonContractCode;

    @ApiModelProperty(value = "发起人ID")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "买方主体")
    private String customerName;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    private String contractSource;

    @ApiModelProperty(value = "父合同Id")
    private Integer sourceContractId;

    @ApiModelProperty(value = "父合同编号")
    private Integer sourceContractCode;

    private Integer isDeleted;

    @ApiModelProperty(value = "合同变化量")
    private BigDecimal changeContractNum;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "协议状态")
    private String protocolStatus;

    @ApiModelProperty(value = "审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）")
    private String approvalStatus;

    @ApiModelProperty(value = "撤回原因")
    private String cancelReason;
}
