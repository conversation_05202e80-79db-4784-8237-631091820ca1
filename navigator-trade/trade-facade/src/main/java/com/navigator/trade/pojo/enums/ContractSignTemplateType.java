package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2022-03-04 10:21
 */
@Getter
@AllArgsConstructor
public enum ContractSignTemplateType {
    /**
     * 协议模版组装类型
     */
    SYSTEM_BASIC_TEMPLATE_HTML(1, "系统-组装基础模版"),
    SYSTEM_COMPLETE_TEMPLATE_HTML(2, "系统-渲染数据模版"),
    USER_COMMIT_TEMPLATE_HTML(3, "用户-提交后模版"),
    COMPARE_TEST(4, ""),
    ;

    private int value;
    private String desc;

    public static ContractSignTemplateType getEnumByValue(Integer value) {
        return Arrays.stream(values())
                .filter(signTemplateType -> value == signTemplateType.getValue())
                .findFirst()
                .orElse(SYSTEM_BASIC_TEMPLATE_HTML);
    }
}
