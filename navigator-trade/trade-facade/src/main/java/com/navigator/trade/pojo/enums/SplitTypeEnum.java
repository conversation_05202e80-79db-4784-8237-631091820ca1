package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022-03-24 02:05
 */
@Getter
@AllArgsConstructor
public enum SplitTypeEnum {
    /**
     * 拆分类型
     */
    PART_SPLIT(1, "部分"),
    ALL_SPLIT(2, "全部");

    int value;
    String desc;

    public static SplitTypeEnum getByValue(int value) {
        for (SplitTypeEnum splitTypeEnum : SplitTypeEnum.values()) {
            if (value == splitTypeEnum.getValue()) {
                return splitTypeEnum;
            }
        }
        return SplitTypeEnum.PART_SPLIT;
    }

}
