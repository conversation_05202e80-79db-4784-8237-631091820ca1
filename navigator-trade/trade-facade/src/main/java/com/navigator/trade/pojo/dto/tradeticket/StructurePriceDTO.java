package com.navigator.trade.pojo.dto.tradeticket;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class StructurePriceDTO extends  TTCommonDTO{

    @ApiModelProperty(value = "1:A,2:B,3:C")
    private Integer structureType;
    @ApiModelProperty(value = "结构总量")
    private BigDecimal totalNum;
    @ApiModelProperty(value = "累积价格")
    private String cumulativePrice;
    @ApiModelProperty(value = "触发价格")
    private String triggerPrice;
    @ApiModelProperty(value = "总交易日")
    private Integer totalDay;
    @ApiModelProperty(value = "结构化单位数量")
    private BigDecimal structureUnitNum;
    @ApiModelProperty(value = "1单位量")
    private BigDecimal unitNum;
    @ApiModelProperty(value = "单位增量")
    private BigDecimal unitIncrement;
    @ApiModelProperty(value = "定价开始日期")
    private Date startTime;
    @ApiModelProperty(value = "定价结束日期")
    private Date endTime;
    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;
    @ApiModelProperty(value = "现金返还量")
    private String cashReturn;
    @ApiModelProperty(value = "品类")
    private Integer goodsCategoryId;


    @ApiModelProperty(value = "ttId")
    private Integer ttId;
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
    @ApiModelProperty(value = "合同ID")
    private Integer contractId;
    @ApiModelProperty(value = "申请id")
    private Integer priceApplyId;
    @ApiModelProperty(value = "创建人")
    private Integer createdBy;
    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
