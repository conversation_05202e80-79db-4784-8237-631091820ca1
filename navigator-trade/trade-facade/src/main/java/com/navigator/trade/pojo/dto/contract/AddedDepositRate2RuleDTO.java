package com.navigator.trade.pojo.dto.contract;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-14 09:35
 **/
@Data
@Accessors(chain = true)
public class AddedDepositRate2RuleDTO {
    /**
     * 品类
     */
    private Integer goodsCategoryId;
    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 履约保证金比例
     */
    private Integer depositRate = 0;

    /**
     * 履约保证金点价后补缴
     */
    private Integer addedDepositRate = 0;

}
