package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.bisiness.enums.BuCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-13 15:25
 **/
@Data
@Accessors(chain = true)
public class KeyVariableDTO {
    /**
     * {@link BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private Integer categoryId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private Integer salesType;

    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     * 修改-变更主体（2，103 --> 2,101）
     */
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    /**
     * 对应TT实际的 contractActionType;
     */
    private Integer originalContractActionType;


    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    /**
     * 所属集团客户编码
     */
    private String enterpriseCode;


    /**
     * 模板条件匹配使用(模板新增、修改、复制模板匹配等场景用)
     */
    @ApiModelProperty(value = "一级品类")
    private String templateCategory1;

    @ApiModelProperty(value = "二级品类")
    private String templateCategory2;

    @ApiModelProperty(value = "三级品类")
    private String templateCategory3;

}
