package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 合同量表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_detail")
@ApiModel(value="ContractDetailEntity对象", description="合同量表")
public class ContractDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "总转月次数")
    private Integer totalTransferTimes;

    @ApiModelProperty(value = "可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "合同签订总量（吨）")
    private Double orderNum;

    @ApiModelProperty(value = "合同总量（吨）")
    private Double contractNum;

    @ApiModelProperty(value = "已提总量（吨）")
    private Double totalDeliveryNum;

    @ApiModelProperty(value = "已点总量（吨）")
    private Double totalPriceNum;

    @ApiModelProperty(value = "已转月量")
    private Double totalTransferNum;

    @ApiModelProperty(value = "已变更量")
    private Double totalModifyNum;


}
