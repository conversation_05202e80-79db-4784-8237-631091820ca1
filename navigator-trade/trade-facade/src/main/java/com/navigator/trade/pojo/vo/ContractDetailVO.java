package com.navigator.trade.pojo.vo;

import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2021/12/3 17:23
 */

@Data
@Accessors(chain = true)
public class ContractDetailVO extends ContractEntity {

    @ApiModelProperty(value = "ttId")
    private Integer ttId;

    @ApiModelProperty(value = "ttCode")
    private String ttCode;

    @ApiModelProperty(value = "tt创建时间")
    private Date ttCreatedAt;

    @ApiModelProperty(value = "协议id")
    private Integer signId;

    @ApiModelProperty(value = "协议编号")
    private String protocolCode;

    //客户发票类型名
    private String invoiceTypeName;

    //是否为集团客户  0:否 1:是
    private Integer enterprise;

    @ApiModelProperty(value = "lkg-客户编码")
    private String lkgCustomerCode;

    @ApiModelProperty(value = "发货库点")
    private String deliveryTypeName;

    @ApiModelProperty(value = "目的地名")
    private String destinationName;
    @ApiModelProperty(value = "发货库点名称")
    private String shipWarehouseName;
    @ApiModelProperty(value = "发货库点lkg编码")
    private String lkgWarehouseCode;
    @ApiModelProperty(value = "发货库点-交货地点")
    private String shipWarehouseAddress;

    @ApiModelProperty(value = "合同模板url地址")
    private String contractPdfOriginalUrl;

    @ApiModelProperty(value = "质检名称")
    private String weightCheckName;

    @ApiModelProperty(value = "袋屁扣重名称")
    private String packageWeightName;

    @ApiModelProperty(value = "品种名")
    private String goodsCategoryName;
    //补充信息-标签集合
    private List<String> tagConfigList;

    @ApiModelProperty(value = "客户合同")
    private List<FileInfoEntity> customerContractUrl;

    //客户银行详情
    private List<CustomerBankDTO> customerBankDTOS;

    private ContractStructureDTO contractStructureVO;

    @ApiModelProperty(value = "合同未执行量")
    private BigDecimal notExecutedNum;

    @ApiModelProperty(value = "合同可回购量")
    private BigDecimal buyBackNum;

    @ApiModelProperty(value = "是否展示合同作废")
    private Integer isShowInvalid;

    @ApiModelProperty(value = "是否展示反点价")
    private Integer isShowReversePrice;

    @ApiModelProperty(value = "是否展示定价完成")
    private Integer isShowPriceComplete;

    @ApiModelProperty(value = "是否展示暂定价定价")
    private Integer isShowCreateTtPrice;

    @ApiModelProperty(value = "拆分和修改操作是否展示保存按钮 0.不展示 1.展示")
    private Integer isShowSaveButton;

    @ApiModelProperty(value = "合同所属商务姓名")
    private String businessPersonName;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "价格详情")
    private PriceDetailVO priceDetailVO;

    @ApiModelProperty(value = "未开单量")
    private BigDecimal notBillNum;

    @ApiModelProperty(value = "未提货量")
    private BigDecimal notDeliveryNum;

    @ApiModelProperty(value = "未定价量")
    private BigDecimal notPricedNum;

    @ApiModelProperty(value = "已定价量")
    private BigDecimal pricedNum;

    @ApiModelProperty(value = "可关闭数量")
    private BigDecimal closeNum;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private Integer reversedPriceTimes;

    @ApiModelProperty(value = "可注销量")
    private BigDecimal canCancelCount = BigDecimal.ZERO;

    /************************** 兼容处理 **************************/
    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "保证金释放方式")
    private Integer depositUseRule;

    @ApiModelProperty(value = "追加履约保证金")
    private BigDecimal addedDepositAmount;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;
    @ApiModelProperty(value = "lkg- 商品编码")
    private String lkgGoodsCode;
    @ApiModelProperty(value = "交提货方式")
    private DeliveryTypeEntity deliveryTypeEntity;
    @ApiModelProperty(value = "lkg-带皮扣重编码")
    private String lkgWeightCheckCode;
    @ApiModelProperty(value = "lkg-目的港编码")
    private String lkgDestinationCode;

    @ApiModelProperty(value = "回购合同编号")
    private String buyBackContractCode;
    @ApiModelProperty(value = "回购合同id")
    private Integer buyBackContractId;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "是否定价完成")
    private Integer priceCompleteStatus;

    @ApiModelProperty(value = "仓单类型")
    private Integer warrantType;

    @ApiModelProperty(value = "结算方式")
    private String settleType;
    /**
     * 1保函 2现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;
    /**
     * 用途
     */
    private String usageString;

    @ApiModelProperty(value = "二级品种名")
    private String category2Name;

    @ApiModelProperty(value = "三级品种名")
    private String category3Name;
    @ApiModelProperty(value = "企标文件编码")
    private String standardFileCode;

    @ApiModelProperty(value = "账套同步系统，LKG/ATLAS")
    private String syncSystem;

    public BigDecimal getAddDepositAmount() {
        return this.getAddedDeposit();
    }

    public Integer getDepositUseRule() {
        return this.getDepositReleaseType();
    }

    public Integer getContractId() {
        return this.getId();
    }

    public BigDecimal getNotExecutedNum() {
        try {
            // 基差、基差暂定价取得是未点价量
            if (getContractType() == ContractTypeEnum.JI_CHA.getValue()
                    || getContractType() == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
                return getContractNum().subtract(getTotalPriceNum());
            }
            return getContractNum();
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

}
