package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.common.constant.TemplateConstant;
import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022-02-23 16:49
 */
@Data
@Accessors(chain = true)
public class SignHuskyTemplateDTO {

    /**
     * M条款、E子模版的展示条件封装
     */
    private TemplateConditionDTO templateCondition;
    /**
     * 关键变量基本信息
     */
    private KeyVariableDTO keyVariableDTO;
    /**
     * 合同已修改字段
     */
//    private TemplateFieldModifyDTO fieldModifyDTO;

    private TemplateLoadEntity templateLoadEntity;
    /**
     * 一级品类名称
     */
    private String categoryName1;
    /**
     * 二级品类名称
     */
    private String categoryName2;
    /**
     * 三级品类名称
     */
    private String categoryName3;

    /**
     * 品种简称(豆粕：M; 豆油：Y)
     * vrjc
     */
    private String goodsCategorySymbol = "";

    /**
     * 品种全称(豆粕：SBM;豆油：SBO)
     * vrqc
     */
    private String goodsCategoryCode;
    /**
     * 采销描述(采购/销售)
     */
    private String salesTypeInfo = "";
    /**
     * 协议类型描述(订单/合同)
     */
    private String protocolTypeInfo = "";
    /**
     * 合同编号
     * no
     */
    private String contractCode = "";
    /**
     * 二维码
     * ewm
     */
    private String qrCodeImage = "";
    /**
     * 条形码
     * txm
     */
    private String barCodeImage = "";
    /**
     * 签约日期
     * doc
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String signDate = "";
    /**
     * 原合同签约日期
     * ydoc
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String originalSignDate = "";
    /**
     * 交提货方式
     * dg
     */
    private String deliveryTypeInfo = "";
    /**
     * 合同类型
     * ty
     */
    private String contractTypeInfo = "";
    /**
     * 合同付款方式
     * pm
     */
    private String paymentTypeInfo = "";
    /**
     * 卖方主体所在地址简称-签订地
     * ad
     */
    private String ldcSignPlace = "";
    /**
     * 买方主体
     * na
     */
    private String customerName = "";
    /**
     * 买方主体（=customerName,当拆分，取新合同客户名称）
     * nna
     */
    private String customerSplitName = "";
    /**
     * 卖方主体
     * me
     */
    private String ldcName = "";
    /**
     * 采购合同.买方地址
     * bscd
     */
    private String ldcAddress;
    /**
     * 采购合同.卖方地址（供应商地址）
     * sscd
     */
    private String supplierAddress;
    /**
     * 品种
     * vr
     */
    private String goodsCategoryName = "";
    /**
     * 吨数
     * mt
     */
    private String contractNum = "0";
    /**
     * 溢短装
     * os
     */
    private String weightTolerance = "";
    /**
     * 蛋白含量
     * eg
     */
    private String goodsSpecId = "";
    /**
     * 货物全称
     */
    private String goodsFullName = "";
    /**
     * 货物名称
     */
    private String commodityName;
    /**
     * 货物包装名
     */
    private String goodsPackageName;
    /**
     * 包装计算重量
     * ag
     */
    private String packageWeight = "";

    /**
     * 原包装计算重量
     * yag
     */
    private String originalPackageWeight = "";
    /**
     * 含税单价
     * pr
     */
    private String unitPrice = "0";
    /**
     * 含税单价明细
     * 期货价格3200；基差价格+20；运费20
     * prx
     */
    private String unitPriceDetail = "";
    /**
     * 含税单价明细(含基差价)
     * 期货价格3200；基差价格+20；运费20
     * prxy
     */
    private String unitPriceDiffDetail = "";
    /**
     * 含税单价中的运费
     * yf
     */
    private String transportPrice = "0";
    /**
     * 原合同含税单价中的运费
     * yyf
     */
    private String originalTransportPrice = "0";
    /**
     * VE单价
     */
    private String vePrice = "0";
    /**
     * VE含量
     */
    private String veContent = "0";
    /**
     * 库点名称
     */
    private String warehouseName = "";
    /**
     * 地址 发货库点.地址
     * ds
     */
    private String warehouseAddress = "";
    /**
     * 交货地点 发货库点.交货地点
     * dd
     */
    private String warehouseDeliveryPoint = "";
    /**
     * 交货周期
     * 合同.开始交货日及截止交货日
     * 需转化成中文日期格式（2018年5月18日至2018年5月22日）
     * po
     */
    private String deliveryTime = "";
    /**
     * 目的港("送到"+（指定地点）)
     * py
     */
    private String destination = "";
    /**
     * 原目的港
     * pye
     */
    private String originalDestination = "";
    /**
     * 重量验收
     * pe
     */
    private String weightCheck = "";
    /**
     * 账期
     * mes
     */
    private Integer creditDays = 0;
    /**
     * 开户行
     * kh
     */
    private String ldcBankName = "";
    /**
     * 银行账号
     * zh
     */
    private String ldcBankAccountNo = "";
    /**
     * 付款截止日期
     * 合同.签订日期+1个自然日
     * 需转化成中文日期格式(例如2022年2月10日)
     * jzfk
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String depositPayEndDay = "";
    /**
     * 保证金比例:5%
     * mr
     */
    private String depositRateInfo = "0";
    /**
     * 点价后补缴比例:5%
     * dmr
     */
    private String addedDepositRateInfo = "0";
    /**
     * 买方收件人,多个值，使用；隔开
     * fox
     */
    private String customerContactName = "";
    /**
     * 期货合约
     * 需转化成中文日期格式,示例文本：2022年05月
     * hy
     */
    private String domainCodeInfo = "";
    /**
     * 期货合约,示例文本：2205
     * hyj
     */
    private String domainCode = "";
    /**
     * 期货合约,示例文本：M2205
     * hyj
     */
    private String categoryDomainCode = "";
    /**
     * 基差价
     * 合同.含税单价.基差价
     * jcj
     */
    private String extraPrice = "0";
    /**
     * 点价截止日期
     * djj
     */
    private String priceEndTime = "";
    /**
     * 框架合同签约日期
     * kjr
     */
    private String protocolStartDate = "";
    /**
     * 框架合同号
     * kjh
     */
    private String protocolNo = "";
    /**
     * 协议编号
     * xyb
     */
    private String signProtocolCode = "";
    /**
     * 定价单TT数量
     * 合同定价TT.定价数量
     * htdj
     */
    private String originalPriceNum = "0";
    /**
     * 点价日期
     * 合同定价TT.点价日期
     * djs
     */
//    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private String priceApplyTime = "";
    /**
     * 定价单价格
     * 合同定价TT.定价价格
     * djjg
     */
    private String transactionPrice = "0";
    /**
     * 合同未定价量
     * wdjl
     */
    private String remainPriceNum = "0";
    /**
     * TT修改日期
     * TT.签订日期,需转换成中文格式
     * ttxr
     */
    private String tradeTicketTime = "";
    /**
     * 父合同编号
     * noy
     */
    private String originalContractCode = "";
    /**
     * 转月数量
     * zysl
     */
    private String transferNum = "0";
    /**
     * 原期货合约
     * yhy
     */
    //@JsonFormat(pattern = "yyyy年MM月", timezone = "GMT+8")
    private String originalDomainCode = "";
    /**
     * 手续费
     * sxf
     */
    private String thisTimeFee = "0";
    /**
     * 交货工厂
     * jhgc
     */
    private String deliveryFactoryName = "";
    /**
     * 原合同数量
     * cfsl
     */
    private String sourceContractNum = "0";
    /**
     * 原合同交货工厂
     * yjhgc
     */
    private String originalDeliveryFactoryName = "";
    /**
     * 未开单量
     * wkdl
     */
    private String remainBillNum = "0";
    /**
     * 未提货量
     * wthl
     */
    private String remainDeliveryNum = "0";

    //todo:===============新增字段=================
    /**
     * 解约定赔数量
     * xdsl
     */
    private String washoutNum = "0";
    /**
     * 解约定赔总差价
     * xdcj
     */
    private String washoutDiffPrice = "0";
    /**
     * 解约定赔市场价
     * xdscj
     */
    private String washoutPrice = "0";
    /**
     * 转月价差
     * zyjc
     */
    private String transferTTPrice = "0";
    /**
     * 原合同溢短装
     * yos
     */
    private String originalWeightTolerance = "";
    /**
     * 修改前含税单价(示例：3201.01)
     * xpr
     */
    private String originalTTUnitPrice = "";
    /**
     * 原合同剩余数量(合同总量-拆分数量)
     * sysl
     */
    private String originalContractNum = "";
    /**
     * 解约定赔差价总额,（#pr#-#xdscj#）*#xdsl#
     * xdze
     */
    private String washoutDiffAmount = "";
    /**
     * 保证金追加比例/追加履约保证金取值
     * 履约保证金=除5、10、15、20外，显示5%
     * bzjzj
     */
    private String jointAddedDepositRate = "";

    /**
     * 客诉折价: 合同.含税单价.客诉折价
     * -20.00
     * kszj
     */
    private String complaintDiscountPrice = "";
    /**
     * 客诉折价总金额
     * kszj * mt,客诉折价*吨数
     * -200.00
     * kszjz
     */
    private String complaintDiscountAmount = "";
    /**
     * 滞期费: 合同.含税单价.滞期费
     * zqf
     */
    private String delayPrice = "";
    /**
     * 滞期费总金额
     * zqf*wkdl(滞期费*未开单量)
     * zqfz
     */
    private String delayAmount = "";
    /**
     * 赊销利息: 合同.含税单价.商务补贴
     * sxlx
     */
    private String businessPrice = "";
    /**
     * 国标值/企标值
     * 交货工厂=ZJG、YZ、ZS、ZZY、DG、TJ 显示：企标值,其他显示：国标值
     * gqbz
     */
    private String symbolFactory = "";
    /**
     * 买方地址
     * ads
     */
    private String customerContactAddresses = "";
    /**
     * 买方电子邮箱
     * 多个值，使用；隔开
     * 示例文本：<EMAIL>；<EMAIL>
     * ema
     */
    private String customerContactEmails = "";
    /**
     * 买方电话号码
     * 多个值，使用；隔开
     * mbo
     */
    private String customerContactPhones = "";
    /**
     * 卖方地址
     * mads
     */
    private String ldcContactAddresses = "";
    /**
     * 卖方收件人
     * 合同.品种.买方主体.交货工厂.收件人 (逗号隔开)
     * mfox
     */
    private String ldcContactNames = "";
    /**
     * 卖方电子邮箱
     * 合同.品种.买方主体.交货工厂.收件人（分号隔开）
     * mema
     */
    private String ldcContactEmails = "";
    /**
     * 卖方电话号码
     * mmbo
     */
    private String ldcContactPhones = "";
    /**
     * 含税总金额
     * prt
     */
    private String totalAmount = "0";
    /**
     * 不含税总金额
     * nprt
     */
    private String noTaxTotalAmount = "0";
    /**
     * 增值税总金额
     * sz
     */
    private String addedTaxTotalAmount = "0";
    /**
     * 原合同_变更后_税率
     */
    private BigDecimal taxRateModify;
    /**
     * 原合同_变更后_含税总金额
     */
    private BigDecimal totalAmountModify;
    /**
     * 原合同_变更后_含税总金额
     */
    private String totalAmountInfoModify = "0";
    /**
     * 原合同_变更后_不含税总金额
     */
    private String noTaxTotalAmountInfoModify = "0";
    /**
     * 原合同_变更后_增值税总金额
     */
    private String addedTaxTotalAmountInfoModify = "0";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：开户行
     * khfk
     */
    private String customerBankName = "";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：银行账号
     * zhfk
     */
    private String customerBankAccountNo = "";
    /**
     * 系统客户配置.配置品种.适用工厂.付款属性（收款）：开户名
     * khmc
     */
    private String customerBankAccountName = "";
    /**
     * 定价单含税总金额
     * djprt
     */
    private String priceOrderTaxAmount = "0";

    /**
     * 加权平均含税价
     * ldjjghs
     */
    private String weightedAverageTaxPrice = "0";

    /**
     * 定价单不含税总金额
     * djnprt
     */
    private String priceOrderNoTaxAmount = "0";

    /**
     * 定价单增值税总金额
     * djsz
     */
    private String priceOrderAddedTaxAmount = "0";
    /**
     * 加权平均价
     * ldjjg
     */
    private String weightedAverageUnitPrice = "0";
    /**
     * 加权含税总金额
     * lprt
     */
    private String weightedAverageTaxAmount = "0";
    /**
     * 加权不含税总金额
     * lnprt
     */
    private String weightedAverageNoTaxAmount = "0";
    /**
     * 加权增值税总金额
     * lsz
     */
    private String weightedAverageAddedTaxAmount = "0";
    /**
     * 结构化定价总数量
     * jghzsl
     */
    private String structurePriceTotalNum = "0";
    /**
     * 结构化定价开始日期
     * jghkrq
     */
    private String structurePriceStartTime = "";

    /**
     * 结构化定价单位量
     * jghdwl
     */
    private String structurePriceUnitNum = "0";

    /**
     * 转月时间
     * jzlyfk
     */
    private String transferMonthTime = "";

    /**
     * 结构化定价结束日期
     * jghjrq
     */
    private String structurePriceEndTime = "";
    /**
     * 结构化定价触发价格
     * 点价申请.结构化定价.触发价格
     * jghcf
     */
    private String structureTriggerPrice = "";
    /**
     * 结构化定价累积价格
     * 点价申请.结构化定价.累积价格
     * jghlj
     */
    private String structureCumulativePrice = "";
    /**
     * 结构化定价单位数量
     * 点价申请.结构化定价.单位量数量
     * jghdwsl
     */
    private String structureUnitNum = "";
    /**
     * 结构化定价单位增量
     * 点价申请.结构化定价.单位量增量
     * jghdwzl
     */
    private String structureUnitIncrement = "";
    /**
     * 履约保证金文本特殊处理（当日卖方基差报价${lytext!}元以上）
     * 豆油-销售-基差（付款条款变更，取300）
     * lytext
     */
    private Integer depositText = 100;

    /**
     * 单吨现金返还金额
     * jghddxjfh
     */
    private String structureCashReturn;
    //==============================================================
    /**
     * 原合同含税单价
     * ypr或xpr(todo:待整理)
     */
    private String originalUnitPrice = "0";
    /**
     * 销售合同/订单
     * xhd chd
     */
    private String contractTitle = "";

    /**
     * 保证金使用规则描述
     */
    private String depositUseRuleName;
    /**
     * 以上期限文本(交货截止日期 < 合同签约日期,拆分）
     * {@link TemplateConstant.DePOSIT_USE_RULE_RATIO}
     */
    private String aboveDeadlineInfo;

    /**
     * 作价期限文本(日期/文本)
     * 基差暂定价和暂定价作价期限
     * {@link TemplateConstant.PRICE_DEADLINE_DATE}
     */
    private String priceDeadlineText;
    /**
     * 点价截止日期(含)
     * djj+(含)，日期时候拼（含）
     */
    private String priceEndTimeContains = "";
    /**
     * 发票后补缴货款比例
     */
    @ApiModelProperty(value = "发票后补缴货款比例")
    private String invoicePaymentRateInfo;
    /**
     * 100-发票后补缴货款比例
     */
    @ApiModelProperty(value = "100-发票后补缴货款比例")
    private String invoicePaymentRateInTurnInfo;
    /**
     * 质量指标条款
     */
    @ApiModelProperty(value = "质量指标条款")
    private String qualityInfo;
    /**
     * 质量指标ID（页面不显示，开发看）
     */
    private Integer qualityId;
    //正大“交货地点”特殊条款触发逻辑：客户=正大集团（配置的3个） 并且  有特殊备注  并且 交货工厂=TJ,
    // TT的备注信息中，包含被“@#￥”括起来的信息
    private String specialCustomerDeliveryInfo;
    /**
     * 主体-logo
     */
    private String logo;

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "仓单ID")
    private Integer warrantId;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;
    /**
     * {@link  com.navigator.bisiness.enums.SettleType}
     * 1,交易所结算  2,自行结算
     */
    @ApiModelProperty(value = "结算方式描述")
    private String settleTypeInfo;
    /**
     * {@link  com.navigator.bisiness.enums.DepositPaymentType}
     * 1,保函  2,现金
     */
    @ApiModelProperty(value = "交割保证金付款方式描述")
    private String depositPaymentTypeInfo;
    /**
     * {@link WarrantCategoryEnum}
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型描述")
    private String warrantCategoryInfo;
    /**
     * 仓单属性
     * {@link WarrantPropertyEnum}
     * 仓单属性:1自由仓单；2仓单
     */
    @ApiModelProperty(value = "仓单属性描述")
    private String warrantPropertyInfo;
    /**
     * 仓单交易所编码
     */
    private String exchangeCode = "DCE";
    /**
     * 仓单交易所名称
     */
    private String exchangeName = "大连商品交易所";

    @ApiModelProperty(value = "注销开始时间,yyyy年MM月dd日")
    private String writeOffStartTime;

    @ApiModelProperty(value = "注销截止时间.yyyy年MM月dd日")
    private String writeOffEndTime;
}
