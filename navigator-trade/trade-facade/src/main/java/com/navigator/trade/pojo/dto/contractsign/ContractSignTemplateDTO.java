package com.navigator.trade.pojo.dto.contractsign;

import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import lombok.Data;

@Data
public class ContractSignTemplateDTO {
    Integer contractId;
    String contractCode;

    TradeTicketEntity tradeTicketEntity;

    ContractDetailInfoDTO contractDetailInfoDTO;

    ContractSignEntity contractSignDTO;

    ContractDetailInfoDTO sourceContractDetailInfoDTO;

    TradeTicketDTO tradeTicketDTO;

}
