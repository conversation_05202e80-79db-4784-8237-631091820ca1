package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "TradeTicket值对象", description = "TT申请表")
public class TradeTicketDTO extends TradeTicketEntity {


    String salesTypeName;           //采销类型
    String typeName;                //TT类型名称
    String contractTypeName;        //合同类型名称
    String statusName;              //状态名称
    String approvalStatusName;      //审批状态
    String approvalTypeName;        //审批规则名称
    String contractStatusName;      //合同状态
    String operationSourceName;     //操作来源
    String contractSourceName;      //合同来源
    String tradeTypeName;           //交易类型
    String goodsCategoryName;       //父品类
    String subGoodsCategoryName;    //子品类

    ContractAddTTDTO contractAddTTDTO;
    ContractModifyTTDTO contractModifyTTDTO;
    TTPriceEntity contractTTPriceDTO;
    ContractTransferTTDTO contractTransferTTDTO;
    ContractStructurePriceAddDTO contractStructurePriceAddDTO;

    public String getSalesTypeName() {
        return null == getSalesType() ? "" : ContractSalesTypeEnum.getDescByValue(getSalesType());
    }

    public String getTypeName() {
        return null == getType() ? "" : TTTypeEnum.getDescByValue((getType()));

    }

    public String getContractTypeName() {
        return null == getContractType() ? "" : ContractTypeEnum.getDescByValue(getContractType());
    }

    public String getStatusName() {
        return null == getStatus() ? "" : TTStatusEnum.getDescByValue(getStatus());
    }

    public String getApprovalStatusName() {
        return null == getApprovalStatus() ? "" : TTApproveStatusEnum.getDescByValue(getApprovalStatus());
    }

    public String getContractStatusName() {
        return null == getContractStatus() ? "" : ContractStatusEnum.getDescByValue(getContractStatus());
    }

    public String getOperationSourceName() {
        return null == getOperationSource() ? "" : OperationSourceEnum.getDescByValue(getOperationSource());
    }

    public String getContractSourceName() {
        return null == getContractSource() ? "" : ContractActionEnum.getDescByValue(getContractSource());
    }

    public String getTradeTypeName() {
        return null == getTradeType() ? "" : ContractTradeTypeEnum.getDescByValue(getTradeType());
    }

    public String getGoodsCategoryName() {
        return null == getGoodsCategoryId() ? "" : GoodsCategoryEnum.getDesc(getGoodsCategoryId());
    }

    public String getSubGoodsCategoryName() {
        return null == getSubGoodsCategoryId() ? "" : GoodsCategoryEnum.getDesc(getCategory2());
    }
}
