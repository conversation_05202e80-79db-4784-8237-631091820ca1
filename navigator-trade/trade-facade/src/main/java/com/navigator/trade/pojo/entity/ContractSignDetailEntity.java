package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/26
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_sign_detail")
@ApiModel(value = "ContractSignDetailEntity对象", description = "合同签署表关联表")
public class ContractSignDetailEntity implements Serializable {

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "协议id")
    private Integer contractSignId;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "tt的id")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "TT类型")
    private Integer ttType;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    private Integer tradeType;

    @ApiModelProperty(value = "新/当前合同基础信息")
    private String newContractContent;

    @ApiModelProperty(value = "原合同信息")
    private String sourceContractContent;

    @ApiModelProperty(value = "TT变更信息")
    private String modifyContent;
}
