package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * dbi_check_contract
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_check_contract")
@ApiModel(value = "CheckContractEntity对象", description = "dbi_check_contract")
public class CheckContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "nav合同编号")
    private String contractCode;

    @ApiModelProperty(value = "校验批次")
    private String checkBatch;

    @ApiModelProperty(value = "开始校验时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startCheckDate;

    @ApiModelProperty(value = "结束校验时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCheckDate;

    @ApiModelProperty(value = "校验状态")
    private String type;

    @ApiModelProperty(value = "校验状态")
    private String status;

    @ApiModelProperty(value = "基础信息校验结果")
    private String baseInfoResult;

    @ApiModelProperty(value = "lkg接口校验结果")
    private String lkgRecordResult;

    @ApiModelProperty(value = "lkg接口校验结果")
    private String lkgInfoResult;

    @ApiModelProperty(value = "备注")
    private String memo;

    //=========合同信息字段=========
    @ApiModelProperty(value = "nav合同状态")
    private Integer navStatus;

    @ApiModelProperty(value = "计算的合同状态")
    private Integer calcStatus;

    @ApiModelProperty(value = "lkg合同状态")
    private Integer lkgStatus;

    @ApiModelProperty(value = "合同签订总量（吨）")
    private BigDecimal orderNum;

    @ApiModelProperty(value = "计算合同签订总量（吨）")
    private BigDecimal calcOrderNum;

    @ApiModelProperty(value = "已点总量（吨）")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "计算已点总量（吨）")
    private BigDecimal calcTotalPriceNum;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "计算含税单价")
    private BigDecimal calcUnitPrice;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "计算含税单价/（税率+1）")
    private BigDecimal calcCifUnitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "计算含税单价-物流相关费用")
    private BigDecimal calcFobUnitPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "计算总价")
    private BigDecimal calcTotalAmount;

    @ApiModelProperty(value = "应付履约保证金")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "计算应付履约保证金")
    private BigDecimal calcDepositAmount;

    @ApiModelProperty(value = "总转月次数")
    private Integer totalTransferTimes;

    @ApiModelProperty(value = "计算总转月次数")
    private Integer calcTotalTransferTimes;

    @ApiModelProperty(value = "总反点价次数")
    private Integer totalReversePriceTimes;

    @ApiModelProperty(value = "计算总反点价次数")
    private Integer calcTotalReversePriceTimes;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "lkg合同总量（吨）")
    private BigDecimal lkgContractNum;

    @ApiModelProperty(value = "lkg合同提货总量（吨）")
    private BigDecimal lkgDeliveryNum;

    @ApiModelProperty(value = "lkg合同开单总量（吨）")
    private BigDecimal lkgBillNum;

    @ApiModelProperty(value = "lkg接口返回的信息")
    private String lkgResultInfo;

    @ApiModelProperty(value = "发起人ID")
    private String createdBy;

    @ApiModelProperty(value = "操作人ID")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
