package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 14:20
 */
@Data
@Accessors(chain = true)
public class ContractSignVO {

    //协议id
    private Integer id;
    //合同id
    private String contractId;
    //合同编号
    private String contractCode;
    //合同状态
    private Integer contractStatus;
    //TTid
    private Integer ttId;
    //TT编号
    private String ttCode;
    //TT状态
    private Integer ttStatus;
    //客户Id
    private Integer customerId;
    //客户名称
    private String customerName;
    //货品ID
    private Integer goodsId;
    //商品名称
    private String goodsName;
    //协议类型
    private Integer ttType;

    private String ttTypeInfo;

    // 合同模板url地址
    private String contractPdfOriginalUrl;
    private String originalContractPdfDownUrl;

    private String contractPdfDownUrl;

    @ApiModelProperty(value = "卖方客户ID")
    private String supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;

    // ldc合同
    private String ldcContractUrl;

    private String ldcContractDownUrl;

    //交货工厂编码
    private String deliveryFactoryCode;

    //交货工厂名称
    private String deliveryFactoryName;

    // LDC合同
    private List<FileInfoEntity> ldcContractUrls;

    @ApiModelProperty(value = "签章错误编码")
    private String signErrorCode;

    @ApiModelProperty(value = "签章错误信息")
    private String signErrorMessage;

    @ApiModelProperty(value = "自动签署状态")
    private Integer voluntarilySignType;

    @ApiModelProperty(value = "invokeNo编码")
    private String invokeNo;

    @ApiModelProperty(value = "是否线上签章 0:否 1:是")
    private String isOnLineSign;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "仓单Id")
    private String warrantId;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "品种代码")
    private String categoryCode;

    @ApiModelProperty(value = "品类编码")
    private String futureCode;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    // 客户合同
    private List<FileInfoEntity> customerContractUrl;
    //协议编号
    private String protocolCode;
    //是否需要正本信息是否需要正本 0 不需要 1 需要
    private Integer needOriginalPaper;
    //LDC需要正本
    private Integer ldcNeedOriginalPaper;
    //状态
    private Integer status;
    //预警类
    private Integer warningType;
    //预警类
    private String warningName;
    //待签章链接Url
    private String signatureUrl;
    //驳回原因
    private String rejectReason;
    //作废原因
    private String invalidReason;
    //审批状态
    private Integer approvalStatus;
    //签章类型（1易企签、2 文件上传）
    private Integer signatureWay;
    //是否是nonFrame
    private String nonFrame;
    //创建日期
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdAt;
    //修改时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "审核状态")
    private Integer confirmStatus;

    @ApiModelProperty(value = "合同模板配置主体")
    private Integer originalLdcFrame;

    /**
     * 组合逻辑：
     * 0，	非WS但系统：不使用易企签，但使用Columbus系统
     * 1，	使用WS：使用易企签，且使用Columbus系统
     * 2，	非系统：不使用易企签，且不使用Columbus系统
     */
    @ApiModelProperty(value = "WS属性（0非WS但系统；1使用WS；2非系统）")
    private Integer useWs;

    private String tradeTypeInfo;

    private Integer tradeType;
}
