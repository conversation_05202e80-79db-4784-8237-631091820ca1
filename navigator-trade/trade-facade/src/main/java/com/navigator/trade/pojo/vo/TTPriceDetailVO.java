package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class TTPriceDetailVO {
    @ApiModelProperty(value = "tt编号")
    private String code;

    @ApiModelProperty(value = "*取消数量")
    private BigDecimal num;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty(value = "点价申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date priceApplyTime;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "品种")
    private String categoryName;
    @ApiModelProperty(value = "二级品种名")
    private String category2Name;
    @ApiModelProperty(value = "三级品种名")
    private String category3Name;
    @ApiModelProperty(value = "商品名称")
    private String goodsId;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "*差价")
    private BigDecimal buyBackPrice;

    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货价")
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "*价差总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "已定价量")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "本次定价量")
    private BigDecimal thisTimePriceNum;

    @ApiModelProperty(value = "未定价量")
    private BigDecimal remainPriceNum;

    @ApiModelProperty(value = "作价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "是否定价完成")
    private Integer priceComplete;

    @ApiModelProperty(value = "*市场价格")
    private String washoutPrice;

    @ApiModelProperty(value = "*合同价格")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "合同总数量")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "定价单含税单价")
    private BigDecimal applyUnitPrice;

    @ApiModelProperty(value = "定价单含税总额")
    private BigDecimal applyTaxUnitPrice;

    @ApiModelProperty(value = "定价单不含税总额")
    private BigDecimal applyNotTaxUnitPrice;

    @ApiModelProperty(value = "定价单增值税总额")
    private BigDecimal applyAddedTaxAllPrice;

    @ApiModelProperty(value = "加权平均价")
    private BigDecimal avePrice;

    @ApiModelProperty(value = "最终合同价格")
    private BigDecimal endContractPrice;

    @ApiModelProperty(value = "最终全额货款")
    private BigDecimal endAllPrice;

    @ApiModelProperty(value = "最终不含税总金额")
    private BigDecimal endNotTaxAllPrice;

    @ApiModelProperty(value = "最终增值税总金额")
    private BigDecimal endAddedTaxAllPrice;

    @ApiModelProperty(value = "价格明")
    private ContractPriceEntity contractPriceEntity;

    @ApiModelProperty(value = "*合同价格")
    private String unitPriceString;

}
