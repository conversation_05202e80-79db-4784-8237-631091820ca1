package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * TT申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_trade_ticket")
@ApiModel(value = "TradeTicketEntity对象", description = "TT申请表")
public class TradeTicketEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "TT编号")
    private String code;
    /**
     * {@link TTTypeEnum}
     */
    @ApiModelProperty(value = "TT类型")
    private Integer type;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "tt状态（1、新录入 2、审批中 3、待修改提交 4、已取消 5 已完成）")
    private Integer status;

    @ApiModelProperty(value = "审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）")
    private Integer approvalStatus;

    /**
     * {@link com.navigator.activiti.pojo.enums.ContractApproveRuleEnum}
     */
    @ApiModelProperty(value = "审批类型")
    private Integer approvalType;

    @ApiModelProperty(value = "合同状态（1、未生效 2、生效中  3、修改中 8、已完成 9、已作废）")
    private Integer contractStatus;

    @ApiModelProperty(value = "协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）")
    private Integer contractSignatureStatus;

    @ApiModelProperty(value = "操作来源（0系统 1用户）")
    private Integer operationSource;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer tradeType;

    @ApiModelProperty(value = "TT所属商务(默认当前账号人,可选择当前工厂、品种的所属商务)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer ownerId;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "作废原因")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String invalidReason;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    /**
     * {@link GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "商品种类")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "商品子类")
    private Integer subGoodsCategoryId;

    @ApiModelProperty(value = "买方客户ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer customerId;

    @ApiModelProperty(value = "买方客户编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName;

    @ApiModelProperty(value = "卖方客户ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supplierCode;

    @ApiModelProperty(value = "卖方客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supplierName;

    @ApiModelProperty(value = "银行Id")
    private Integer bankId;

    @ApiModelProperty(value = "品种代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String domainCode;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "关联id")
    private String groupId;

    @ApiModelProperty(value = "源合同信息")
    private Integer sourceContractId;

    @ApiModelProperty(value = "变更前合同量")
    private BigDecimal beforeContractNum;

    @ApiModelProperty(value = "变更合同量")
    private BigDecimal changeContractNum;

    @ApiModelProperty(value = "变更后合同量")
    private BigDecimal afterContractNum;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "占用状态")
    private Integer occupyStatus;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "撤回原因")
    private String cancelReason;

    @ApiModelProperty(value = "来源类型: 1.保存 2.提交（默认）")
    private Integer sourceType;

    @ApiModelProperty(value = "定价单信息")
    private String confirmPriceInfo;

    // V1 业务线编码 品类 合同性质 商品信息，价格JSON Author:zengshl 2024-06-18 start

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;

    @ApiModelProperty(value = "合同性质")
    private Integer contractNature;

    //SkuId
    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    //SkuFullName
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    //SkuNickName
    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    // V1 业务线编码 品类 合同性质 商品信息，价格JSON Author:zengshl 2024-06-18 end

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;


}
