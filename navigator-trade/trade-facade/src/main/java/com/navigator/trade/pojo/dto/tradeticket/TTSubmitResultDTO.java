package com.navigator.trade.pojo.dto.tradeticket;

import lombok.Data;

@Data
public class TTSubmitResultDTO {
    private Integer ttId;
    private String ttCode;
    private Integer contractId;
    private String contractCode;
    private Integer signId;
    private String protocolCode;
    private Boolean checkResult = true;
    private Boolean checkDataIntegrity = true;
    private Boolean checkGoodsInfo = true;
    private Boolean checkGoodsQualityConfig = true;
    private Boolean checkPayConditionConfig = true;
    private Boolean checkApprove = true;
    private StringBuilder errorMsg = new StringBuilder();
}
