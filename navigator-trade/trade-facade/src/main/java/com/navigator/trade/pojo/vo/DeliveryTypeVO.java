package com.navigator.trade.pojo.vo;

import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-02 13:34
 */
@Data
@Accessors(chain = true)
public class DeliveryTypeVO {
    /**
     * 交提货方式类型
     */
    private Integer deliveryType;
    /**
     * 交提货类型名称
     */
    private String deliveryTypeName;

    private List<DeliveryTypeEntity> deliveryTypeList;
}
