package com.navigator.trade.pojo.entity;

import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ContractPriceBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal extraPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "期货价")
    private BigDecimal forwardPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "出厂价")
    private BigDecimal factoryPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "蛋白价格")
    private BigDecimal proteinDiffPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "散粕补贴")
    private BigDecimal compensationPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "期权费")
    private BigDecimal optionPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "运费")
    private BigDecimal transportPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "起吊费")
    private BigDecimal liftingPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "滞期费")
    private BigDecimal delayPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = " 高温费")
    private BigDecimal temperaturePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = " 其他物流费")
    private BigDecimal otherDeliveryPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = " 和解款折价")
    private BigDecimal buyBackPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "客诉折价")
    private BigDecimal complaintDiscountPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = " 转厂补贴")
    private BigDecimal transferFactoryPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "其他补贴")
    private BigDecimal otherPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "商务补贴")
    private BigDecimal businessPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee = BigDecimal.ZERO;

    @ApiModelProperty(value = "装运费单价")
    private BigDecimal shippingFeePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "精炼价差")
    private BigDecimal refineDiffPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "精炼/分提价差")
    private BigDecimal refineFracDiffPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "检验费")
    private BigDecimal surveyFees = BigDecimal.ZERO;

    @ApiModelProperty(value = "精炼价差")
    private String previousRecord;

    @ApiModelProperty(value = "VE单价")
    private BigDecimal vePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "VE含量")
    private BigDecimal veContent = BigDecimal.ZERO;

    public ContractPriceBaseEntity(PriceDetailDTO priceDetailDTO) {
        setExtraPrice(BigDecimalUtil.parseZero(priceDetailDTO.getExtraPrice()));
        setForwardPrice(BigDecimalUtil.parseZero(priceDetailDTO.getForwardPrice()));
        setFactoryPrice(BigDecimalUtil.parseZero(priceDetailDTO.getFactoryPrice()));
        setProteinDiffPrice(BigDecimalUtil.parseZero(priceDetailDTO.getProteinDiffPrice()));
        setCompensationPrice(BigDecimalUtil.parseZero(priceDetailDTO.getCompensationPrice()));
        setOptionPrice(BigDecimalUtil.parseZero(priceDetailDTO.getOptionPrice()));
        setTransportPrice(BigDecimalUtil.parseZero(priceDetailDTO.getTransportPrice()));
        setLiftingPrice(BigDecimalUtil.parseZero(priceDetailDTO.getLiftingPrice()));
        setDelayPrice(BigDecimalUtil.parseZero(priceDetailDTO.getDelayPrice()));
        setTemperaturePrice(BigDecimalUtil.parseZero(priceDetailDTO.getTemperaturePrice()));
        setOtherDeliveryPrice(BigDecimalUtil.parseZero(priceDetailDTO.getOtherDeliveryPrice()));
        setBuyBackPrice(BigDecimalUtil.parseZero(priceDetailDTO.getBuyBackPrice()));
        setComplaintDiscountPrice(BigDecimalUtil.parseZero(priceDetailDTO.getComplaintDiscountPrice()));
        setTransferFactoryPrice(BigDecimalUtil.parseZero(priceDetailDTO.getTransferFactoryPrice()));
        setOtherPrice(BigDecimalUtil.parseZero(priceDetailDTO.getOtherPrice()));
        setBusinessPrice(BigDecimalUtil.parseZero(priceDetailDTO.getBusinessPrice()));
        setFee(BigDecimalUtil.parseZero(priceDetailDTO.getFee()));
        setShippingFeePrice(BigDecimalUtil.parseZero(priceDetailDTO.getShippingFeePrice()));
        setRefineDiffPrice(BigDecimalUtil.parseZero(priceDetailDTO.getRefineDiffPrice()));
        setVePrice(BigDecimalUtil.parseZero(priceDetailDTO.getVePrice()));
        setVeContent(BigDecimalUtil.parseZero(priceDetailDTO.getVeContent()));
    }

    public ContractPriceBaseEntity() {

    }
}
