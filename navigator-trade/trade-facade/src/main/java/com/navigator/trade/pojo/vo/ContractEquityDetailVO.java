package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.activiti.pojo.dto.ApproveTaskActInfoDTO;
import com.navigator.activiti.pojo.dto.EquityChangeInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ContractEquityDetailVO {

    @ApiModelProperty(value = "申请编号")
    private String applyCode;

    @ApiModelProperty(value = "业务信息")
    private List<EquityChangeInfoDTO> equityChangeInfoDTOList;

    @ApiModelProperty(value = "审批规则")
    private String approveRuleName;

    @ApiModelProperty(value = "审批原因")
    private String approveCause;

    @ApiModelProperty(value = "审批发起人")
    private String startUserName;

    @ApiModelProperty(value = "审批创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "审批信息")
    private List<ApproveTaskActInfoDTO> approveTaskInfoList;
}
