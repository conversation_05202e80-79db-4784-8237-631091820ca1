package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-25 17:23
 */
@Getter
@AllArgsConstructor
public enum ContractSignStatusEnum {
    /**
     * 原合同状态
     */
    WAIT_PROVIDE(1, "待出具"),
    WAIT_REVIEW(2, "待审核"),
    WAIT_STAMP(3, "待签章"),
    WAIT_BACK(4, "待回签"),
    WAIT_CONFIRM(5, "待确认合规"),
    PAPER(6, "正本"),
    PROCESSING(7, "已完成"),
    ABNORMAL(9, "异常"),
    INVALID(15, "已作废");

    private int value;
    private String desc;

    public static ContractSignStatusEnum getEnumByValue(Integer value) {
        return Arrays.stream(values())
                .filter(signStatusEnum -> value == signStatusEnum.getValue())
                .findFirst()
                .orElse(WAIT_PROVIDE);
    }

    public static List<Integer> getInvalidSignStatusList() {
        return Arrays.asList(ABNORMAL.getValue(), INVALID.getValue());
    }

}
