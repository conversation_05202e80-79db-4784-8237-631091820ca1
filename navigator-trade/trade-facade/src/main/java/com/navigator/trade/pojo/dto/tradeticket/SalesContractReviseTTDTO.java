package com.navigator.trade.pojo.dto.tradeticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SalesContractReviseTTDTO extends TTCommonDTO {
    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "履约保证金比例 (系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
    private Integer depositRate;

    @ApiModelProperty(value = "应付履约保证金")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;//

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    private Integer weightCheck;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "是否代加工，默认否（0:否 1:是）")
    private Integer oem;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "追加履约保证金 一口价默认是5%【下跌】基差150 【下跌】 一口价暂定价5%【上涨也会收】延期定价 默认>0【上涨收】")
    private BigDecimal addedDeposit;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;

    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "修改内容Json")
    private String modifyContent;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "子合同ID")
    private Integer sonContractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    private String userId;

    @ApiModelProperty(value = "TT所属商务")
    private String ownerId;

    @ApiModelProperty(value = "tt类型")
    private Integer ttType;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "税率（1.增值税专用发票9% 2.增值税专用发票10% 3.增值税专用发票12%） 税率根据品种自动带出（需单独模块维护）发票类型，可选")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "作价截止时间")
    //@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String priceEndTime;

    private Integer supplierAccountId;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    private BigDecimal delayPayFine;

//    @ApiModelProperty(value = "迟付款罚金规则")
//    private String delayPayFineRule;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositReleaseType;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "修改前后字段")
    private String content;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    /**
     * 修改接口的类型：1.普通修改 2.定价完成
     */
    private Integer reviseType = 1;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;

    @ApiModelProperty(value = "补充协议类型 -1:原合同TT不审批不生成协议   0: 新合同 ")
    private Integer addedSignatureType = 0;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    private Integer isStf;

    /**
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;
}
