package com.navigator.trade.pojo.enums;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SubmitTypeEnum {
    /**
     * 提交类型
     */
    SAVE(1, "保存"),
    SUBMIT(2, "提交"),
    SAVE_SUBMIT(3, "保存并提交"),
    COPY_SAVE(4, "复制保存");

    final int value;
    final String desc;

    public static SubmitTypeEnum getByValue(int value) {
        for (SubmitTypeEnum splitTypeEnum : SubmitTypeEnum.values()) {
            if (value == splitTypeEnum.getValue()) {
                return splitTypeEnum;
            }
        }
        return SubmitTypeEnum.SUBMIT;
    }

}
