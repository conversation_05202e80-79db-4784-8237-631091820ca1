package com.navigator.trade.pojo.dto.contract;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.entity.ContractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 注销子合同信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Data
@Accessors(chain = true)
public class WriteOffContractDTO {

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "合同性质")
    private Integer contractNature;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "仓单注销记录表id")
    private Integer warrantCancellationId;

    @ApiModelProperty(value = "关联业务数据编号")
    private String referContractCode;

    @ApiModelProperty(value = "关联业务数据Id")
    private Integer referContractId;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;

}
