package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.vo.TtPriceEntityVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(name = "navigator-trade-service")
public interface ContractPriceFacade {
    /**
     * @description: 计算合同明细值
     * @param: [priceDetailDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/calculatePrice")
    Result calculatePrice(@RequestBody PriceDetailBO priceDetailBO);

    @PostMapping("/tradeTicket/calculatePriceBo")
    BigDecimal calculatePriceBo(@RequestBody PriceDetailBO priceDetailBO);

    /**
     * 保存ttPrice
     *
     * @param ttPriceEntity
     * @return
     */
    @PostMapping("/saveTtPrice")
    boolean saveTtPrice(@RequestBody TTPriceEntity ttPriceEntity);

    /**
     * 根据合同编号获取TtPrice
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getTtPrice")
    TtPriceEntityVO getTtPrice(@RequestParam("contractCode") String contractCode);

    /**
     * 根据合同id获取ContractPriceEntity
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractPriceEntityContractId")
    ContractPriceEntity getContractPriceEntityContractId(@RequestParam("contractId") Integer contractId);


    /**
     * 根据合同id获取ContractPriceEntity
     *
     * @param ttId
     * @return
     */
    @GetMapping("/getContractPriceEntityByTTId")
    ContractPriceEntity getContractPriceEntityByTTId(@RequestParam("ttId") Integer ttId);

    /**
     * 根据合同id更新ContractPriceEntity
     *
     * @param contractPriceEntity
     * @return
     */
    @PostMapping("/updatePriceByContractId")
    boolean updatePriceByContractId(@RequestBody ContractPriceEntity contractPriceEntity);

    /**
     * 更新合同的期货价格
     *
     * @param contractEntity 合同实体
     * @param forwardPrice   期货价格
     * @return
     */
    @PostMapping("/updateContractForwardPrice")
    Result updateContractForwardPrice(@RequestBody ContractEntity contractEntity, @RequestParam("forwardPrice") BigDecimal forwardPrice);


    /**
     * 同步数据到contractPrice
     * @param contractPriceEntity
     * @return
     */
    @PostMapping("/syncContractPrice")
    boolean syncContractPrice(@RequestBody ContractPriceEntity contractPriceEntity);

}
