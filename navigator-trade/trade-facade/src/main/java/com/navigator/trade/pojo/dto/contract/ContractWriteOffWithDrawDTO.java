package com.navigator.trade.pojo.dto.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 合同注销撤回DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Accessors(chain = true)
public class ContractWriteOffWithDrawDTO {

    @ApiModelProperty(value = "注销记录ID")
    private Integer cancellationId;
    /**
     * 非豆二的注销撤回字段信息
     */
    @ApiModelProperty(value = "仓单仓单合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "注销的动作 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同 4. 豆二注销不修改提货方 5. 豆二注销修改提货方")
    private int writeOffAction;

    @ApiModelProperty(value = "注销撤回数量")
    private BigDecimal withDrawNum;

    @ApiModelProperty(value = "注销撤回的合同列表")
    List<WriteOffContractDTO>  writeOffContractDTOS;
    /**
     * 下面合同字段信息，根据前端接口进行转换
     */

    /**
     * 非豆二的注销撤回字段信息
     */

    @ApiModelProperty(value = "货权合同ID")
    private Integer cargoRightsContractId;

    @ApiModelProperty(value = "提货合同ID")
    private Integer deliveryContractId;

    @ApiModelProperty(value = "采购合同ID")
    private Integer purchaseContractId;


}
