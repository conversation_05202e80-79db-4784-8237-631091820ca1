package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-25 17:23
 */
@Getter
@AllArgsConstructor
public enum ContractWriteOffStatusEnum {
    /**
     * 原合同状态
     */
    NOT_WRITEOFF(1, "未注销"),
    WRITEOFFING(2, "注销中"),
    COMPLATE_WRITEOFF(3, "已注销");

    private int value;
    private String desc;

}
