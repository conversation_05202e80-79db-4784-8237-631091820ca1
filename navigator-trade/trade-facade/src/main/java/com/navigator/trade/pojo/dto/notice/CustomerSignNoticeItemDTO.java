package com.navigator.trade.pojo.dto.notice;

import com.navigator.common.util.time.DateTimeUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CustomerSignNoticeItemDTO implements Serializable {
    String businessCode;
    Integer signId;
    Integer categoryId;
    String categoryCode;
    Integer salesType;
    Integer customerId;
    String customerName;
    Integer supplierName;
    String factoryCode;
    String factoryName;
    String contractCode;
    String contractFilePath;

    //String sender;
    //String senderName;
    //String replayTo;
    //List<ReceiverContactVO> receiver;
    //List<String> ccList;

    public String getCurrentTime() {
        return DateTimeUtil.formatDateString();
    }

    String currentTime;


}
