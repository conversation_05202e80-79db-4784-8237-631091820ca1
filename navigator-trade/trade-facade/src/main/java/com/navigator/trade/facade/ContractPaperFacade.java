package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/26 10:56
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractPaperFacade {

    @GetMapping("/getContractPaper")
    Result getContractPaper(@RequestParam("contractSignId") Integer contractSignId);

    @PostMapping("/saveContractPaper")
    boolean saveContractPaper(@RequestBody ContractPaperDTO contractPaperDTO);

}
