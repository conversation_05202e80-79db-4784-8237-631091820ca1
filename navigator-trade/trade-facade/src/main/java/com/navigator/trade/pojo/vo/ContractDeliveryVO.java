package com.navigator.trade.pojo.vo;

import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 仓单详情提货合同数据
 * <AUTHOR>
 * @date 2024-07-17
 */

@Data
@Accessors(chain = true)
public class ContractDeliveryVO extends ContractEntity {

    @ApiModelProperty(value = "货物：仓单合同的货物信息")
    private String warrantGoodsName;


}
