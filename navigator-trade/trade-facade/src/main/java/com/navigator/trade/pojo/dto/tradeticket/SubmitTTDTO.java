package com.navigator.trade.pojo.dto.tradeticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

@Data
public class SubmitTTDTO {
    @ApiModelProperty(value = "提交状态 1:新增时的提交 2. 待修改区的提交 3.修改/拆分的提交")
    private String submitStatus;
    @Valid
    @ApiModelProperty(value = "信息实体")
    private OMContractAddTTDTO createTradeTicketDTO;
    @ApiModelProperty(value = "ttProcessor类型")
    private String ttProcessor;
    private Integer ttId;
    private String userId;
    /**
     * 剩余风险控制：是否强制提交（客户交易状态为CBT/INACTIV/ACTIVE，且RR Residue < 0 && RR Usage ≤ 150 kUSD，可强制提交 ）
     */
    private Boolean residualRiskForceSubmit;
}
