package com.navigator.trade.facade;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.ContractBO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合同导出的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022/07/16
 */
@FeignClient(name = "navigator-trade-service")
public interface ContractExportFacade {

    /**
     * 每日合同导出
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return
     */
    @GetMapping("/contract/exportDailyContract")
    Result exportDailyContract(@RequestParam(value = "startDateTime", required = false) String startDateTime, @RequestParam(value = "endDateTime", required = false) String endDateTime);

    /**
     * 合同报表导出
     *
     * @param contractBO
     */
    @PostMapping("/contract/exportContractExcel")
    Result exportContractExcel(@RequestBody ContractBO contractBO);

    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 start
    /**
     * 合同报表导出（支持多选状态）
     *
     * @param contractBO
     */
    @PostMapping("/contract/exportContractExcelMultiStatus")
    Result exportContractExcelMultiStatus(@RequestBody ContractBO contractBO);
    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 end

    /**
     * Data migration by Jason
     *
     * @param status
     */
    @PostMapping("/contract/contractMigrationJason")
    Result dataMigration(@RequestParam(value = "status", required = false) String status, @RequestBody(required = false) Map<String, List<String>> contractCodes);

    @PostMapping("/contract/priceMigrationJason")
    Result priceMigration(@RequestParam(value = "status", required = false) String status, @RequestBody(required = false) Map<String, List<String>> contractCodes);
}
