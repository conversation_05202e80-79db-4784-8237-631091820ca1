package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_contract_sign")
public class ContractSignVOEntity extends ContractSignEntity {
    @ApiModelProperty(value = "是否使用易企签")
    private Integer useYqq;

    @ApiModelProperty(value = "是否使用 Columbus 系统")
    private Integer isColumbus;

    @ApiModelProperty(value = "对应客户")
    private Integer useCustomerId;

    @ApiModelProperty(value = "模板协议(0 无,订单 1有,大合同)")
    private Integer frameProtocol;

    @ApiModelProperty(value = "协议生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolStartDate;

    @ApiModelProperty(value = "协议过期日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolEndDate;

    @ApiModelProperty(value = "合同模板配置主体")
    private Integer originalLdcFrame;
}
