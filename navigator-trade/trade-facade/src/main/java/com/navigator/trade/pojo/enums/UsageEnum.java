package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum UsageEnum {

    /**
     * TT/合同-用途
     */

    DEFAULT(0, "无"),
    SELF_PRODUCED(1, "自产"),
    TRADE(2, "贸易"),
    PACKAGE(3, "包装"),
    SELF_USE(4, "自用"),
    DELIVERY(5, "交割"),
    ;
    int value;
    String desc;

    public static UsageEnum getByValue(int type) {
        return Arrays.stream(values())
                .filter(usageEnum -> Objects.equals(type, usageEnum.getValue()))
                .findFirst()
                .orElse(SELF_PRODUCED);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static Integer getValueByDesc(String desc) {
        return Arrays.stream(values())
                .filter(usageEnum -> desc.equals(usageEnum.getDesc()))
                .findFirst()
                .orElse(SELF_PRODUCED).getValue();
    }
}
