package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.bo.PriceDetailBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 注销采购回购的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Accessors(chain = true)
public class ContractWriteOffItemDTO {

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @ApiModelProperty(value = "发货库点【交割库】")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "发货库点名称")
    private String shipWarehouseName;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价明细")
    private PriceDetailBO priceDetailDTO;

    @ApiModelProperty(value = "期货合约代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "注销数量")
    private BigDecimal writeOffNum;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "应付履约保证金金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "目的地/港")
    private String destination;

    @ApiModelProperty(value = "重量检验")
    private String weightCheck;

}
