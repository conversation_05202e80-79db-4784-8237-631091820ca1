package com.navigator.trade.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.navigator.common.util.BigDecimalSerializer;
import com.navigator.common.util.PriceSerializer;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class TTQueryDetailVO {

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 点价合同）")
    private String contractType;

    @ApiModelProperty(value = "卖方客户ID")
    private String supplierId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "买方客户编号")
    private String customerCode;

    @ApiModelProperty(value = "买方客户ID")
    private String customerId;

    @ApiModelProperty(value = "品种")
    private String goodsCategoryId;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private String goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private String goodsSpecId;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    @ApiModelProperty(value = "是否代加工，默认否（0、否 1、是）")
    private Integer oem;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositUseRule;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;
    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private String deliveryTypeName;
    @ApiModelProperty(value = "目的地Id")
    private String destination;

    @ApiModelProperty(value = "目的地名称")
    private String destinationName;
    @ApiModelProperty(value = "重量检验Id（可选，Magellan维护）")
    private String weightCheck;

    @ApiModelProperty(value = "重量检验名称（可选，Magellan维护）")
    private String weightCheckName;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight;

    @ApiModelProperty(value = "袋皮扣重名称")
    private String packageWeightName;

    @ApiModelProperty(value = "发货库点ID")
    private String shipWarehouseId;

    @ApiModelProperty(value = "发货库点Code")
    private String shipWarehouseCode;

    @ApiModelProperty(value = "发货库点名称")
    private String shipWarehouseName;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "TT所属商务Id")
    private Integer ownerId;

    @ApiModelProperty(value = "TT所属商务")
    private String ownerName;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    private Integer isStf;

    @ApiModelProperty(value = "点价截止日期类型（1时间 2文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    @ApiModelProperty(value = "履约保证金点价后补缴比例")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    @ApiModelProperty(value = "TT编号")
    private String code;
    @ApiModelProperty(value = "TT类型（1、销售合同新增；2、销售合同变更；3、销售合同回购；4、点价申请）")
    public Integer type;
    @ApiModelProperty(value = "完成度状态")
    private Integer completedStatus;
    private Integer approvalStatus;
    private Integer approvalType;
    @ApiModelProperty(value = "点价开始时间")
    private String priceStartTime;
    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;
    @ApiModelProperty(value = "卖方主体收款账号id")
    private Integer supplierAccountId;
    @ApiModelProperty(value = "签订地（卖方主体）")
    private String signPlace;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Split（变更）3、Transfer（变更包含转厂执行） 4、resale（回购再重售）5、washout（解约定赔）")
    private Integer tradeType;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "税率（1.增值税专用发票9% 2.增值税专用发票10% 3.增值税专用发票12%） 税率根据品种自动带出（需单独模块维护）发票类型，可选")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "合同总量（吨）")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal contractNum;

    @ApiModelProperty(value = "计量单位（TT只有吨，涉及物流会有袋件数等）")
    private String unit;

    @ApiModelProperty(value = "总价")
    //@JsonSerialize(using= BigDecimalSerializer.class)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "币种（系统默认为CNY）")
    private String currencyType;

    @ApiModelProperty(value = "是否为集团客户  0:否 1:是")
    private Integer enterprise;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "tbd 税率根据品种自动带出（需单独模块维护）发票类型，可选")
    private Integer invoiceType;

    @ApiModelProperty(value = "tbd 税率根据品种自动带出（需单独模块维护）发票类型，可选")
    private String invoiceTypeName;

    @ApiModelProperty(value = "含税单价（系统默认由以下字段输入数字相加而来： 基差价 期货价格 蛋白价差 散粕补贴 期权费 其他物流费用 运费。起吊费用；达孚待付，客户自付。滞期费。高温费 回购折价 客诉折价 转厂补贴 其他补贴 商务补贴 手续费 DCE交割：只有交割结算价）")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "含税单价/（税率+1）")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "应付履约保证金")
    @JsonSerialize(using = PriceSerializer.class)
    private BigDecimal depositAmount;

    private Integer depositAmountStatus;

    @ApiModelProperty(value = "追加履约保证金 一口价默认是5%【下跌】基差150 【下跌】 一口价暂定价5%【上涨也会收】延期定价 默认>0【上涨收】")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal addedDepositAmount;

    private Integer addedDepositAmountStatus;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销 1 ；无赊销天数为预付款 2)")
    private Integer paymentType;

    @ApiModelProperty(value = "质量检验（根据客户属性定）")
    private String qualityCheck;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal delayPayFine;

    @ApiModelProperty(value = "履约保证金比例Id")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRateValue;

    @ApiModelProperty(value = "价格详情")
    private PriceDetailVO priceDetailVO;

    @ApiModelProperty(value = "定价量")
    private BigDecimal num;

    @ApiModelProperty(value = "定价价格/价差")
    private BigDecimal price;

    @ApiModelProperty(value = "定价时间")
    private Date priceTime;

    @ApiModelProperty(value = "已提总量（吨）")
    private BigDecimal totalDeliveryNum;

    @ApiModelProperty(value = "客户银行详情")
    private List<CustomerBankDTO> customerBankDTOS;

    @ApiModelProperty(value = "原合同编号")
    private String rootContractCode;

    @ApiModelProperty(value = "原合同id")
    private Integer rootContractId;

    @ApiModelProperty(value = "发起人")
    private String createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    @ApiModelProperty(value = "协议Id")
    private Integer signId;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "用途")
    private String usageString;

    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "剩余风险限额")
    private String residualRiskLimit;
    @ApiModelProperty(value = "剩余风险使用")
    private String residualRiskUsage;
    @ApiModelProperty(value = "剩余风险剩余")
    private String residualRiskResidue;
    /**
     * 交易状态
     * (NoTrade;
     * CBT-ConsultBeforeTrade;
     * Active;
     * Inactive）
     */
    @ApiModelProperty(value = "交易状态")
    private String residualRiskTradeStatus;

    // v3新增字段
    @ApiModelProperty(value = "仓单Code")
    private String warrantCode;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套")
    private String siteName;

    @ApiModelProperty(value = "账套同步系统")
    private String syncSystem;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "仓单交易类型 1.交易所交割仓单 2.线下交易所仓单 3.交易所仓单交易平台")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffEndTime;

    /**
     * 仓单类型 1，工厂仓单 2，仓库仓单
     */
    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;

    /**
     * 1保函 2现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;

    @ApiModelProperty(value = "交割保证金金额")
    private BigDecimal deliveryMarginAmount;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "企标文件编号")
    private String standardFileCode;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

    @ApiModelProperty(value = "付款条件代码")
    private String payConditionCode;

    @ApiModelProperty(value = "是否豆二")
    private Integer isSoybean2;

}
