package com.navigator.trade.pojo.dto.contract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 合同校验的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24
 */
@Data
@Accessors(chain = true)
public class ContractCheckDTO {

    @ApiModelProperty(value = "合同号List")
    private List<String> contractCodeList;

    @ApiModelProperty(value = "是否开启获取lkg")
    private Integer isOpenSync;

    @ApiModelProperty(value = "开始校验时间")
    private String startDateTime;

    @ApiModelProperty(value = "结束校验时间")
    private String endDateTime;

    @ApiModelProperty(value = "结束校验时间")
    private String checkBatch;
}
