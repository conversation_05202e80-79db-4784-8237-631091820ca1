package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.bisiness.enums.*;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffOMDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TTDTO {

    /**
     * 新增实体(解约定赔,回购,关闭)
     */
    private SalesContractAddTTDTO salesContractAddTTDTO;
    /**
     * 修改实体
     */
    private SalesContractReviseTTDTO salesContractReviseTTDTO;
    /**
     * 拆分实体
     */
    private SalesContractSplitTTDTO salesContractSplitTTDTO;
    /**
     * 转月实体
     */
    private SalesContractTTTransferDTO salesContractTTTransferDTO;

    /**
     * 点价实体
     */
    private SalesContractTTPriceDTO salesContractTTPriceDTO;

    /**
     * 结构化定价实体
     */
    private SalesStructurePriceTTDTO salesStructurePriceTTDTO;

    /**
     * 仓单注销实体
     */
    private ContractWriteOffDTO contractWriteOffDTO;

    /**
     * 仓单豆二注销实体
     */
    private ContractWriteOffOMDTO contractWriteOffOMDTO;

    /**
     * 价格实体(修改)
     */
    private PriceDetailBO priceDetailBO;

    /**
     * 回购DTO
     */
    private ContractBuyBackDTO contractBuyBackDTO;
    /**
     * 解约定赔
     */
    private ContractWashOutDTO contractWashOutDTO;

    /**
     * 关闭/点价动作给TT传参数
     */
    private ContractEntity contractEntity;
    /**
     * tt类型
     * {@link ProcessorTypeEnum}
     */
    private String processorType;

    @ApiModelProperty(value = "业务关联id")
    private String groupId;

    @ApiModelProperty(value = "是否修改主体")
    private Boolean reviseCustomerType;

    @ApiModelProperty(value = "提交类型 1.保存（前端不传） 2.提交(默认 前端不传) 4.复制保存（前端传值）")
    private Integer submitType = SubmitTypeEnum.SUBMIT.getValue();

    @ApiModelProperty(value = "是否修改主体(保存修改主体使用)")
    private Boolean changeCustomerFlag;

    @ApiModelProperty(value = "修改主体的Id")
    private Integer originCustomerId;

    @ApiModelProperty(value = "定价单信息")
    private String confirmPriceInfo;

    /**
     * 业务类型
     * {@link BuCodeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private String buCode;

    /**
     * 采销类型
     * {@link ContractSalesTypeEnum}
     */
    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    /**
     * tt类型
     * {@link TTTypeEnum}
     */
    @ApiModelProperty(value = "tt类型")
    private Integer ttType;

    /**
     * trade类型
     * {@link ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "trade类型")
    private Integer contractTradeType;


    /**
     * 仓单注销时tt的动作
     * 1 修改TT 2 销售提货合同新增TT 3 仓单采购合同新增TT
     */
    private TTWriteOffActionEnum WriteOffTTAction;


    public TTTypeEnum getTTTypeEnum() {
        return TTTypeEnum.getByType(this.ttType);
    }
}
