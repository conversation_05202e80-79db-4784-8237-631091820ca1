package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.Data;

/**
 * <p>
 * 每日合同数量
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
public class ContractExportDTO {
    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 关联合同编号
     */
    private String refContractCode;

    /**
     * 定价方式
     */
    private String modeOfPrice;

    /**
     * 货品代码
     */
    private String commodityCode;

    /**
     * 合同数量
     */
    private String contractNum;

    /**
     * 定价状态
     */
    private String fixStatus;

    /**
     * 合同状态
     */
    private String contractStatus;

    /**
     * 备注
     */
    private String memo;

    // ==========================销售=================================
    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 合同开单数量
     */
    private String contractOutCount;

    /**
     * 合同未开单结余数量
     */
    private String contractNoOutCount;

    /**
     * 合同实发数量
     */
    private String contractFactOutCount;

    /**
     * 合同未发货数量
     */
    private String contractNoSendCount;

    /**
     * 总结价数量
     */

    private String tolSettleCount;

    /**
     * 未结价数量
     */
    private String tolNoSettleCount;

    // ==========================采购=================================
    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 合同出库数量
     */
    private String inCount;

    /**
     * 合同结余数量
     */
    private String noIntCount;


    public ContractExportDTO(ContractEntity contractEntity) {
        this.contractCode = contractEntity.getContractCode();
        this.contractNum = String.valueOf(contractEntity.getContractNum());
        this.contractFactOutCount = String.valueOf(contractEntity.getTotalDeliveryNum());
        this.contractNoSendCount = String.valueOf(contractEntity.getContractNum().subtract(contractEntity.getTotalDeliveryNum()));
        this.tolSettleCount = String.valueOf(contractEntity.getTotalPriceNum());
        this.tolNoSettleCount = String.valueOf(contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()));
        this.contractStatus = String.valueOf(contractEntity.getStatus());
        this.memo = contractEntity.getMemo();
    }


    /**
     * 对象转换成Csv输出标题头
     * 确保 CsvTitle 与 StringArray 顺序一直
     */
    public static String[] getSalesCsvTitle() {
        return new String[]{
                "合同编号",
                "关联合同号",
                "定价方式",
                "客户代码",
                "货品代码",
                "合同数量",
                "合同开单数量",
                "合同未开单结余数量",
                "合同实发数量",
                "合同未发货数量",
                "总结价数量",
                "未结价数量",
                "定价状态",
                "合同状态",
                "备注",
        };
    }

    public static String[] getPurchaseCsvTitle() {
        return new String[]{
                "合同编号",
                "关联合同号",
                "定价方式",
                "供应商编码",
                "货品代码",
                "合同数量",
                "合同出库数量",
                "合同结余数量",
                "定价状态",
                "合同状态",
                "备注",
        };
    }

    public String[] toSalesStringArray() {
        return new String[]{
                contractCode,
                refContractCode,
                modeOfPrice,
                customerCode,
                commodityCode,
                contractNum,
                contractOutCount,
                contractNoOutCount,
                contractFactOutCount,
                contractNoSendCount,
                tolSettleCount,
                tolNoSettleCount,
                fixStatus,
                contractStatus,
                memo,
        };
    }

    public String[] toPurchaseStringArray() {
        return new String[]{
                contractCode,
                refContractCode,
                modeOfPrice,
                supplierCode,
                commodityCode,
                contractNum,
                inCount,
                noIntCount,
                fixStatus,
                contractStatus,
                memo,
        };
    }
}
