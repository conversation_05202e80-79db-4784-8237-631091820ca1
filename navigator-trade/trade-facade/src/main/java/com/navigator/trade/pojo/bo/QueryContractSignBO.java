package com.navigator.trade.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-20 17:48
 */
@Data
@Accessors(chain = true)
public class QueryContractSignBO {
    /**
     * 协议状态
     */
    private String status;
    /**
     * 是否是ldc模板 0：nonFrame 1:是
     */
    private Integer ldcFrame;

    /**
     * 是否是ldc模板 0：nonFrame 1:是
     */
    private Integer nonFrame;
    /**
     * 客户登录账号id
     */
    private Integer employId;

    /**
     * 系统(1麦哲伦 2哥伦布)
     */
    private Integer system;

    /**
     * 合同编号->业务编号（TT编码/合同编码）
     */
    private String contractCode;

    /**
     * 货品ID
     */
    private Integer goodsId;

    /**
     * 品种id
     */
    private Integer goodsCategoryId;
    /**
     * 协议号
     */
    private String protocolCode;
    /**
     * 客户Id
     */
    private Integer customerId;
    /**
     * 哥伦布主体id
     */
    private Integer columbusCustomerId;
    /**
     * 供应商Id
     */
    private Integer supplierId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 协议类型
     */
    private Integer ttType;
    /**
     * 预警类型(1框架协议过期 2使用WS 3非WS但系统)
     */
    private String warningType;

    private String signDate;

    /**
     * 开始创建时间
     */
    private String startSignDate;

    /**
     * 结束创建时间
     */
    private String endSignDate;

    /**
     * 合同销售类型（1.采购 2.销售）
     */
    private Integer salesType;

    /**
     * 交货工厂编码
     */
    private List<String> deliveryFactoryCode;

    /**
     * 交货工厂编码
     */
    private String deliveryFactoryId;

    /**
     * 0:不需要正本 正本 1:客户需要正本 2LDC需要正本
     */
    private Integer needOriginalPaper;

    /**
     * 0:非WS但系统,1:使用WS,2:非系统
     */
    private Integer useWS;

    /**
     * 客户idList
     */
    private List<Integer> customerIdList;

    private List<String> siteCodeList;

    /**
     * 主体
     */
    private String companyId;

    /**
     * 主体
     */
    private String createStartTime;

    private String createEndTime;

    @ApiModelProperty(value = "是否使用易企签")
    private String useYqq;

    @ApiModelProperty(value = "是否使用 Columbus 系统")
    private String isColumbus;

    @ApiModelProperty(value = "对应客户")
    private String useCustomerId;

    @ApiModelProperty(value = "模板协议(0 无,订单 1有,大合同)")
    private String frameProtocol;

    @ApiModelProperty(value = "协议生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String protocolStartDate;

    @ApiModelProperty(value = "协议过期日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String protocolEndDate;


}
