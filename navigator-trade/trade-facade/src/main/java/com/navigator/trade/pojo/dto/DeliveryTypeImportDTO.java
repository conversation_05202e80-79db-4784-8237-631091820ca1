package com.navigator.trade.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import com.navigator.trade.pojo.enums.DeliveryTransportType;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-02-26 18:54
 */
@Data
public class DeliveryTypeImportDTO {
    @Excel(name = "lkg编码")
    private String lkgCode;

    @Excel(name = "品种")
    private String categoryCode;

    @Excel(name = "交提货方式名称")
    private String name;

    /**
     * {@link DeliveryModeEnum}
     */
    @Excel(name = "提货类型")
    private String type;

    /**
     * 提货地点：（1、工厂 2、码头 3、达孚外租库 4、客户仓库 5交割库）
     */
    @Excel(name = "提货地点")
    private String addressType;

    /**
     * {@link DeliveryTransportType}
     */
    @Excel(name = "运输方式")
    private String transportWay;

    @Excel(name = "合同条款")
    private String contractTerms;
}
