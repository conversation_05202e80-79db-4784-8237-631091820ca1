package com.navigator.trade.pojo.dto.contractsign;

import lombok.Data;

import java.util.List;

/**
 * 合同出具
 *
 * <AUTHOR>
 * @since 2022-01-21 18:00
 */
@Data
public class ContractSignProvideDTO {

    /**
     * 协议ID
     */
    private Integer contractSignId;
    /**
     * 修改后的，电子合同HTML内容
     */
    private String htmlContent;
    /**
     * 当合同协议为non-frame时，上传PDF文件ID
     */
    private List<Integer> fileIdList;
    /**
     * 当合同协议为non-frame时，客户是否已签章
     */
    private Integer isCustomerSignature;
}
