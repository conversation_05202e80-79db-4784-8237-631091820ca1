package com.navigator.trade.pojo.enums;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
@AllArgsConstructor
public enum CompareUnitPriceEnum {

    EQ(1, "=0", "互不打款"),
    GT(2, ">0", "LDC打款"),
    LT(3, "<0", "客户打款"),
    ;

    private int value;
    private String code;
    private String desc;

    public static int getCompareUnitPrice(BigDecimal washOutDiffUnitPrice, Integer salesType) {
        if (washOutDiffUnitPrice.compareTo(BigDecimal.ZERO) == 0) {
            return EQ.getValue();
        } else if ((washOutDiffUnitPrice.compareTo(BigDecimal.ZERO) < 0 && ContractSalesTypeEnum.SALES.getValue() == salesType) ||
                (washOutDiffUnitPrice.compareTo(BigDecimal.ZERO) > 0 && ContractSalesTypeEnum.PURCHASE.getValue() == salesType)) {
            return GT.getValue();
        } else {
            return LT.getValue();
        }
    }

}
