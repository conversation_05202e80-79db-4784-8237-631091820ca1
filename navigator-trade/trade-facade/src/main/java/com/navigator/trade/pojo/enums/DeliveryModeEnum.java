package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DeliveryModeEnum {

    /**
     * 交提货-类型
     */

    TAKE(1, "自提"),
    SEND(2, "送货"),
    TRANSFER(3, "转货权"),
    ;
    int value;
    String desc;

    public static DeliveryModeEnum getByValue(int type) {
        return Arrays.stream(values())
                .filter(deliveryModeEnum -> Objects.equals(type, deliveryModeEnum.getValue()))
                .findFirst()
                .orElse(TAKE);
    }

    public static DeliveryModeEnum getByDesc(String desc) {
        return Arrays.stream(values())
                .filter(deliveryModeEnum -> Objects.equals(desc, deliveryModeEnum.getDesc()))
                .findFirst()
                .orElse(TAKE);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
