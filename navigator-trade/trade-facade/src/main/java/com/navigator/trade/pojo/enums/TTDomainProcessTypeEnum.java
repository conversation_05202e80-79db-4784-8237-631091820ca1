package com.navigator.trade.pojo.enums;

import com.navigator.bisiness.enums.TTTypeEnum;
import lombok.Getter;

@Getter
public enum TTDomainProcessTypeEnum {
    ADD(1, "UNKNOWN/NEW/BUYBACK/PUT_BACK/CLOSED/INVALID/WASHOUT/ALLOCATE/ASSIGN/WARRANT_PURCHASE"),
    MODIFY(2, "REVISE/SPLIT/WRITE_OFF"),
    PRICE(3, "PRICE/FIXED"),
    TRANSFER(4, "TRANSFER/REVERSE_PRICE"),
    STRUCTURE(5, "EQUITY_CHANGE/STRUCTURE_PRICE"),
    ;


    int value;
    String desc;

    TTDomainProcessTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static TTDomainProcessTypeEnum getByTTType(TTTypeEnum ttType) {
        System.out.println("=========="+ttType.name());
        for (TTDomainProcessTypeEnum ttDomainProcessTypeEnum : TTDomainProcessTypeEnum.values()) {
            if (ttDomainProcessTypeEnum.getDesc().contains(ttType.name())) {
                return ttDomainProcessTypeEnum;
            }
        }
        return null;
    }
}
