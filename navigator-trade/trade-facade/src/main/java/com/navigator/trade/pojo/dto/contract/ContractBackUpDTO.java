package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同备份的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ContractBackUpDTO {

    /**
     * 合同实体
     */
    private ContractEntity contractEntity;

    /**
     * 备份类型
     */
    private String backTradeType;

    /**
     * 对应业务编码
     */
    private String referCode;
}
