package com.navigator.trade.pojo.dto.tradeticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

@Data
@Accessors(chain = true)
public class CommonTradeInfoDTO {
    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号(批量新增)")
    private String code;

    @ApiModelProperty(value = "含税单价（系统默认由以下字段输入数字相加而来： 基差价 期货价格 蛋白价差 散粕补贴 期权费 其他物流费用 运费。起吊费用；达孚待付，客户自付。滞期费。高温费 回购折价 客诉折价 转厂补贴 其他补贴 商务补贴 手续费 DCE交割：只有交割结算价）")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String unitPrice;

    @ApiModelProperty(value = "合同总量（吨）")
    @NotBlank
    @Pattern(regexp = "([-+])?\\d+(\\.\\d+)?$")
    private String contractNum;

    // TODO 仓单没这个属性
    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "交易所编码")
    private String exchangeCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "履约保证金比例 ")
    @NotNull
    private Integer depositRate;

    @ApiModelProperty(value = "应付履约保证金金额")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String depositAmount;

    @ApiModelProperty(value = "履约保证金点价后补缴比例")
    private String addedDepositRate;

    @ApiModelProperty(value = "付款条件Id")
    @NotBlank
    private String payConditionId;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "点价截止日期类型（1时间 2文本）")
    private Integer priceEndType;

    /**
     * TODO 代码实现，一口价不校验
     */
    @ApiModelProperty(value = "点价截止时间")
    private String priceEndTime;

    /**
     * 仓单的字段信息
     */
    @ApiModelProperty(value = "注销周期开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "备注")
    private String memo;
}
