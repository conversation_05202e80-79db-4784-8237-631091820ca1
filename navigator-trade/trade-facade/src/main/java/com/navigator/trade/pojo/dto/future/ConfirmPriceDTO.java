package com.navigator.trade.pojo.dto.future;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 定价单的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@Accessors(chain = true)
public class ConfirmPriceDTO {
    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 定价单id
     */
    private Integer ttPriceId;

    /**
     * 定价价格
     */
    private BigDecimal confirmPrice;

    /**
     * 定价数量
     */
    private BigDecimal confirmNum;

    /**
     * VE单价
     */
    private BigDecimal vePrice = BigDecimal.ZERO;

    /**
     * veContent
     */
    private BigDecimal veContent = BigDecimal.ZERO;

}
