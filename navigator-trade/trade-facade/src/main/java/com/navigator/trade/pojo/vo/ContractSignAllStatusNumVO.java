package com.navigator.trade.pojo.vo;


import lombok.Data;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/6 14:32
 */
@Data
public class ContractSignAllStatusNumVO {
    // 待出具
    private int waitProvide = 0;
    // 待审核
    private int waitReview = 0;
    // 待签章
    private int waitStamp = 0;
    // 待回传
    private int waitBack = 0;
    // 待确认合规
    private int waitConfirm = 0;
    // 正本
    private int originalPaper = 0;
    // 执行中
    private int processing = 0;
    // 异常
    private int abnormal = 0;
    // 已作废
    private int invalid = 0;
    // 已取消（todo：待废弃）
    private int canceled = 0;

}
