package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 合同签署表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_sign")
@ApiModel(value = "ContractSignEntity对象", description = "合同签署表")
public class ContractSignEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "协议唯一编号")
    private String uuid;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "tt的id")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "协议号")
    private String protocolCode;

    /**
     * {@link TTTypeEnum}
     */
    @ApiModelProperty(value = "TT类型（1、销售合同新增；2、销售合同修改；3、销售合同拆分 4、转月5、反点价  6、点转反定价单 7、主体变化）")
    private Integer ttType;

    /**
     * {@link ContractSignStatusEnum}
     */
    @ApiModelProperty(value = "协议出具状态（1、待出具 2、待审核 3、待盖章 4、待回签 6、待确认合规  7、正本 8、已完成 9、异常 10、已作废 ）")
    private Integer status;


    @ApiModelProperty(value = "回签状态（1YQQ 2,上传)")
    private Integer backStatus;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractTypeEnum}
     */
    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同  3暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     * */
    @ApiModelProperty(value = "合同来源")
    private Integer contractSource;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "交易类型：101、New（新增）102、Revise（修改）104、Split（拆分）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer tradeType;

    @ApiModelProperty(value = "是否需要正本（0 不需要 1 需要）")
    private Integer needOriginalPaper;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private Integer customerCode;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "原始买方客户ID（非0则为变更客户主体）")
    private Integer originalCustomerId;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "卖方客户ID")
    private String supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;

    @ApiModelProperty(value = "待签章链接Url")
    private String signatureUrl;

    @ApiModelProperty(value = "签章类型（1线下、2 客户单签、3、双签）")
    private Integer signatureType;

    @ApiModelProperty(value = "签章类型（1易企签、2文件上传）")
    private Integer signatureWay;

    @ApiModelProperty(value = "签章状态 1:发起成功 2:待LDC签章(参与者正在处理信封<回调签章链接>) 3:LDC签章完成(参与者确认) 4:待客户签章(参与者正在处理信封<回调签章链接>) 5:客户确认签章(参与者确认) 6、文件上传完成")
    private Integer signatureStatus;

    @ApiModelProperty(value = "签章错误编码")
    private String signErrorCode;

    @ApiModelProperty(value = "签章错误信息")
    private String signErrorMessage;

    @ApiModelProperty(value = "自动签署状态")
    private Integer voluntarilySignType;

    @ApiModelProperty(value = "是否线上签章")
    private Integer isOnLineSign;

    @ApiModelProperty(value = "品种代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "合同负责人")
    private Integer ownerId;

    @ApiModelProperty(value = "二维码")
    private String qrCodeImage;

    @ApiModelProperty(value = "条形码")
    private String barCodeImage;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "基差价格")
    private Double extraPrice;

    @ApiModelProperty(value = "本次转月手续费")
    private BigDecimal thisTimeFee;

    @ApiModelProperty(value = "ldc是否需要正本（0.不需要 1.需要）")
    private Integer ldcNeedOriginalPaper;

    @ApiModelProperty(value = "是否是ldc模板（0.nonframe 1.是）")
    private Integer ldcFrame;

    @ApiModelProperty(value = "是否是ldc模板（0.nonframe 1.是）")
    private Integer customerNonFrame;

    @ApiModelProperty(value = "是否有框架协议 0: 无 大合同 1:有 订单")
    private Integer frameProtocolType;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "invokeNo")
    private String invokeNo;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    @ApiModelProperty(value = "协议类型:0.补充协议 1.尾量终止")
    /**
     * {@link com.navigator.trade.pojo.enums.ContractSignTypeEnum}
     */
    private Integer signType;

    @ApiModelProperty(value = "当合同协议为non-frame时，客户是否已签章")
    private Integer isCustomerSignature;

    @ApiModelProperty(value = "确认合规驳回")
    private Integer confirmStatus;

    //多品类V1 协议多品类字段添加 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "出具方式(1不出具;2文件上传;3数字合同）")
    private String provideType;

    @ApiModelProperty(value = "审核方式（需审核 不审核）")
    private String needReview;

    @ApiModelProperty(value = "ldc签章方式（不签章 线下签 易企签-标准 易企签-静默 ）")
    private String ldcSignatureType;

    @ApiModelProperty(value = "客户签章方式（不签章 线下签 易企签）")
    private String customerSignatureType;

    @ApiModelProperty(value = "终止方式:0.补充协议 1.尾量终止")
    private String termineType;

    @ApiModelProperty(value = "ws类型(0使用ws;1非ws但系统;非系统2)")
    private String wsType;

    @ApiModelProperty(value = "协议类型")
    private String contractSignType;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;
    //多品类V1 协议多品类字段添加 Author:Wan 2024-07-01 end

    /**
     * V3新增的字段信息
     */
    @ApiModelProperty(value = "仓单Id")
    private String warrantId;

    @ApiModelProperty(value = "仓单编号")
    private String warrantCode;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "是否为豆二注销生成（0否;1是）")
    private Integer isSoybean2;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "交易所编号")
    private String exchangeCode;

    @ApiModelProperty(value = "货品昵称")
    private String commodityName;

    @ApiModelProperty(value = "正本原因")
    private String paperReason;

}
