package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * TT新增申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@Accessors(chain = true)
@TableName("dbt_tt_add")
@ApiModel(value = "TTAddEntity对象", description = "TT新增申请表")
public class TTAddEntity extends TTSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /*
    * DELETE @20240807 NEO FOR:MOVE TO PARENT CLASS
    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    */

    /**
     * 主表已存在，子表冗余
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "根合同ID")
    private Integer rootContractId;

    @ApiModelProperty(value = "合同状态（1、待提交审批 2、待出具 3、待审核 4、待盖章 5、待回传 6、待确认合规 8、执行中15、已取消）")
    private Integer status;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;


    //*******************计划删除*******************
    @ApiModelProperty(value = "买方客户编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerCode;
    @ApiModelProperty(value = "买方客户ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer customerId;
    @ApiModelProperty(value = "客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName;
    @ApiModelProperty(value = "卖方客户ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer supplierId;
    @ApiModelProperty(value = "卖方客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supplierName;

    @ApiModelProperty(value = "品种")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer goodsCategoryId;
    //*******************计划删除*******************

    @ApiModelProperty(value = "卖方主体收款账号信息")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supplierAccount;

    @ApiModelProperty(value = "签订地（卖方主体）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String signPlace;

    @ApiModelProperty(value = "期货合约")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String domainCode;

    @ApiModelProperty(value = "货品ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String goodsName;

    @ApiModelProperty(value = "计量单位（TT只有吨，涉及物流会有袋件数等）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String unit;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer goodsSpecId;

    /**
     * 带皮扣重、返回给前端、TT详情中查看
     */
    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String packageWeight;

    @Deprecated
    @ApiModelProperty(value = "质量检验（根据客户属性定）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String qualityCheck;

    @ApiModelProperty(value = "币种（系统默认为CNY）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String currencyType;

    @ApiModelProperty(value = "含税单价,用json字段保存，或者存入价格详情表中")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价-物流相关费用,用json字段保存，或者存入价格详情表中")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "税率")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "发票类型")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer invoiceType;

    @ApiModelProperty(value = "含税单价/（税率+1）用json字段保存，或者存入价格详情表中")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @Deprecated
    @ApiModelProperty(value = "暂定价")
    private BigDecimal temporaryPrice;

    @Deprecated
    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "总价,用json字段保存，或者存入价格详情表中")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "基差价（元）,用json字段保存，或者存入价格详情表中")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "赊销账期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销 1 ；无赊销天数为预付款 2)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer paymentType;


    @ApiModelProperty(value = "应付履约保证金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "追加履约保证金")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal addedDepositAmount;

    @ApiModelProperty(value = "履约保证金比例")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer depositUseRule;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal delayPayFine;

//    @ApiModelProperty(value = "迟付款罚金规则")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
//    private String delayPayFineRule;

    @ApiModelProperty(value = "是否代加工，默认否（0、否 1、是）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer oem;

    @ApiModelProperty(value = "开始交货时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date deliveryEndTime;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer deliveryType;

    @ApiModelProperty(value = "交货工厂编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deliveryFactoryName;

    @ApiModelProperty(value = "目的地Id/港")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String destination;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "是否安排运输")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer isArrangeTransport;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String weightCheck;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer weightTolerance;

    @ApiModelProperty(value = "作价开始时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date priceStartTime;

    @ApiModelProperty(value = "作价截止时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String priceEndTime;

    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String memo;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "签订日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date signDate;

    @ApiModelProperty(value = "是否是stf合同（0:否 1:是）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer isStf;

    @ApiModelProperty(value = "点价截止日期类型（1时间 2文本）")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer priceEndType;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer addedDepositRate;

    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "是否为集团客户  0:否 1:是")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer enterprise;

    @ApiModelProperty(value = "完整度状态(0:未完整 1: 已完整)")
    private Integer completedStatus;

    @ApiModelProperty(value = "当前解约定赔含税单价")
    private BigDecimal washoutUnitPrice;

    @ApiModelProperty(value = "生成时原合同量")
    private BigDecimal sourceContractNum;

    @ApiModelProperty(value = "解约定赔价格详情")
    private String washoutPriceDetail;

    @ApiModelProperty(value = "修改前后字段")
    private String content;

    @ApiModelProperty(value = "期货价")
    private BigDecimal forwardPrice;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "剩余风险限额")
    private String residualRiskLimit = "";
    @ApiModelProperty(value = "剩余风险使用")
    private String residualRiskUsage = "";
    @ApiModelProperty(value = "剩余风险剩余")
    private String residualRiskResidue = "";
    /**
     * 交易状态
     * (NoTrade;
     * CBT-ConsultBeforeTrade;
     * Active;
     * Inactive）
     */
    @ApiModelProperty(value = "交易状态")
    private String residualRiskTradeStatus = "";

    // V1 TT新增 添加值对象，合同量变化字段 Author:zengshl 2024-06-18 start
    /**
     * 增加 值对象
     */
    @ApiModelProperty(value = "目的地,值对象新增字段")
    private String destinationValue;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）,值对象新增字段")
    private String deliveryTypeValue;

    @ApiModelProperty(value = "袋皮扣重,值对象新增字段")
    private String packageWeightValue;

    @ApiModelProperty(value = "发票类型,值对象新增字段")
    private String invoiceTypeValue;

    @ApiModelProperty(value = "重量检验,值对象新增字段")
    private String weightCheckValue;

    @ApiModelProperty(value = "发货库点,值对象新增字段")
    private String shipWarehouseValue;
    

    // V1 TT 添加值对象，合同量变化字段 Author:zengshl 2024-06-18 end

    @ApiModelProperty(value = "仓单交易类型")
    private Integer warrantTradeType;

    @ApiModelProperty(value = "结算方式")
    private String settleType;

    @ApiModelProperty(value = "注销周期开始时间")
    private Date writeOffStartTime;

    @ApiModelProperty(value = "注销周期结束时间")
    private Date writeOffEndTime;

    @ApiModelProperty(value = "仓单ID")
    private Integer warrantId;

    @ApiModelProperty(value = "仓单Code")
    private String warrantCode;

    @ApiModelProperty(value = "期货编码")
    private String futureCode;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

    /**
     * 1保函 2现金
     */
    @ApiModelProperty(value = "交割保证金付款方式")
    private Integer depositPaymentType;

    @ApiModelProperty(value = "交割保证金金额")
    private BigDecimal deliveryMarginAmount;

    @ApiModelProperty(value = "仓单类型")
    private Integer warrantCategory;



}
