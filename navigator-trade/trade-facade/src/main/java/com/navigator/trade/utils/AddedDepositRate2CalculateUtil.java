package com.navigator.trade.utils;

import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.enums.ContractTypeEnum;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-14 09:39
 **/
public class AddedDepositRate2CalculateUtil {

    private final static Integer RATE_RULE_0 = 0;
    private final static Integer RATE_RULE_5 = 5;
    private final static Integer RATE_RULE_10 = 10;

    private final static Integer ADDED_DEPOSIT_RATE2_VALUE_3 = 3;
    private final static Integer ADDED_DEPOSIT_RATE2_VALUE_5 = 5;
    private final static Integer ADDED_DEPOSIT_RATE2_VALUE_7 = 7;
    private final static Integer ADDED_DEPOSIT_RATE2_VALUE_10 = 10;

    /**
     * TODO 需要重新计算保证金规则-多品种
     * * 豆粕#BZJZJ#
     * 基差合同
     * * 0<=履约保证金比例+履约保证金补缴比例≤5%，显示3%
     * * 5%<履约保证金比例+履约保证金补缴比例≤10%，显示5%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 0<=履约保证金比例≤5%，显示3%
     * * 5%<履约保证金比例≤10%，显示5%
     * * 履约保证金比例>10%，显示10%
     * * 豆油#BZJZJ#
     * 基差合同
     * * 0<=履约保证金比例+履约保证金补缴比例≤5%，显示3%
     * * 5%<履约保证金比例+履约保证金补缴比例≤10%，显示7%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 0<=履约保证金比例≤5%，显示3%
     * * 5%<履约保证金比例≤10%，显示5%
     * * 履约保证金比例>10%，显示10%
     */
    public static Integer getAddedDepositRate2(AddedDepositRate2RuleDTO depositRate2RuleDTO) {
        // BUGFIX：case-1003003 追加履约保证金比例错误 Author: NaNa 2025-02-26 START
        if (null == depositRate2RuleDTO.getDepositRate()) {
            depositRate2RuleDTO.setDepositRate(0);
        }
        if (null == depositRate2RuleDTO.getAddedDepositRate()) {
            depositRate2RuleDTO.setAddedDepositRate(0);
        }
        // BUGFIX：case-1003003 追加履约保证金比例错误 Author: NaNa 2025-02-26 END
        int addedDepositRate2;
        int addedDepositRateValue = ContractTypeEnum.JI_CHA.getValue() == depositRate2RuleDTO.getContractType() ?
                (depositRate2RuleDTO.getDepositRate() + depositRate2RuleDTO.getAddedDepositRate()) :
                depositRate2RuleDTO.getDepositRate();
        if (addedDepositRateValue >= RATE_RULE_0 && addedDepositRateValue <= RATE_RULE_5) {
            addedDepositRate2 = ADDED_DEPOSIT_RATE2_VALUE_3;
        } else if (addedDepositRateValue > RATE_RULE_5 && addedDepositRateValue <= RATE_RULE_10) {
            addedDepositRate2 = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.SPECIFIC_PROTEIN.getValue()).contains(depositRate2RuleDTO.getGoodsCategoryId()) ?
                    ADDED_DEPOSIT_RATE2_VALUE_5 : ADDED_DEPOSIT_RATE2_VALUE_7;
        } else if (addedDepositRateValue > RATE_RULE_10) {
            addedDepositRate2 = ADDED_DEPOSIT_RATE2_VALUE_10;
        } else {
            addedDepositRate2 = 0;
        }
        return addedDepositRate2;
    }

    public static void main(String[] args) {
        AddedDepositRate2RuleDTO ruleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(GoodsCategoryEnum.OSM_OIL.getValue())
                .setContractType(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())
                .setDepositRate(5)
                .setAddedDepositRate(4);
        System.out.println(getAddedDepositRate2(ruleDTO));
    }

}
