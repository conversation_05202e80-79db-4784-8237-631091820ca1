package com.navigator.trade.pojo.dto.contractsign;

import lombok.Data;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-15 14:47
 **/
@Data
public class TemplateFieldModifyDTO {
    /**
     * 合同类型-修改（修改为1 未修改为0）
     */
    private Integer contractTypeModify = 0;
    /**
     * 开始交货时间-修改
     */
    private Integer deliveryStartTimeModify = 0;
    /**
     * 截止交货时间秀-修改
     */
    private Integer deliveryEndTimeModify = 0;
    /**
     * 买方客户-修改
     */
    private Integer customerCodeModify = 0;
    /**
     * 赊销账期-修改
     */
    private Integer creditDaysModify = 0;
    /**
     * 袋皮扣重-修改
     */
    private Integer packageWeightModify = 0;
    /**
     * 发货库点-修改
     */
    private Integer shipWarehouseIdModify = 0;
    /**
     * 目的地-修改
     */
    private Integer destinationModify = 0;
    /**
     * 溢短装-修改
     */
    private Integer weightToleranceModify = 0;
    /**
     * 重量检验-修改
     */
    private Integer weightCheckModify = 0;
    /**
     * 含税单价-修改
     */
    private Integer unitPriceModify = 0;
    /**
     * 应付履约保证金比例-修改
     */
    private Integer depositAmountModify = 0;
    /**
     * 交提货方式-修改
     */
    private Integer deliveryTypeModify = 0;
    /**
     * 付款方式-修改
     */
    private Integer paymentTypeModify = 0;
    /**
     * 交货工厂-修改
     */
    private Integer deliveryFactoryCodeModify = 0;
    /**
     * 点价截止日期类型-修改
     */
    private Integer priceEndTypeModify = 0;
    /**
     * 履约保证金点价后补缴-修改
     */
    private Integer addedDepositRateModify = 0;
    /**
     * 货品包装-修改
     */
    private Integer goodsPackageIdModify = 0;
    /**
     * 货品规格-修改
     */
    private Integer goodsSpecIdModify = 0;
}
