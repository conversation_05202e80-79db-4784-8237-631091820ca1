package com.navigator.trade.facade;


import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "navigator-trade-service")
public interface TradeTicketFacade {

    /**
     * 创建仓单合同 （仓单DTO-动作  <-（转让或者分配），active : 提交或者保存）
     * 转让 -> 交易类型是线下的
     *
     * @param ttdto
     * @return
     */
    @PostMapping("/tradeTicket/createWarrantContract")
    Result createWarrantSalesContract(@RequestBody TTDTO ttdto);

    /**
     * @description: 保存TT
     * @param: [salesContractAddTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/saveTT")
    Result saveTT(@RequestBody TTDTO ttdto);

    /**
     * @description: 提交TT
     * @param: [submitTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/submitTT")
    Result submitTT(@RequestBody SubmitTTDTO submitTTDTO);

    /**
     * @description: 批量提交TT
     * @param: [submitTTBatchDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/submitTTBatch")
    Result submitTTBatch(@RequestBody SubmitTTBatchDTO submitTTBatchDTO);


    /**
     * @description: 删除TT
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/tradeTicket/deleteTT/{ttId}")
    Result deleteTT(@PathVariable("ttId") Integer ttId);


    /**
     * @description: 修改TT
     * @param: [createTradeTicketDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/updateTT")
    Result updateTT(@RequestBody TTDTO ttdto);

    @GetMapping("/tradeTicket/getTTDetailInfo")
    TradeTicketDTO getTTDetailInfo(@RequestParam(value = "ttCode") String ttCode);

    /**
     * @description: 获取TT列表
     * @param: [queryDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/queryTTList")
    Result queryTTList(@RequestBody QueryDTO<TTQueryDTO> queryDTO);

    /**
     * @description: 获取TT详情
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/tradeTicket/queryTTDetail/{ttId}")
    Result queryTTDetail(@PathVariable("ttId") Integer ttId);


    /**
     * @description: TT审批
     * @param: [approvalDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/approveTT")
    Result approveTT(@RequestBody ApprovalDTO approvalDTO);

    @PostMapping("/tradeTicket/batchApproveTT")
    Result batchApproveTT(@RequestBody List<ApprovalDTO> approvalDTOList);

    /**
     * @description: TT撤回
     * @param: [operateTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/cancelTT")
    Result cancelTT(@RequestBody OperateTTDTO operateTTDTO);

    /**
     * 点价单/转月/反点价撤回
     *
     * @param contraryPriceDTO
     * @return
     */
    @PostMapping("/tradeTicket/contraryPrice")
    Result contraryPrice(@RequestBody ContraryPriceDTO contraryPriceDTO);

    /**
     * @description: TT作废
     * @param: [operateTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/invalidTT")
    Result invalidTT(@RequestBody OperateTTDTO operateTTDTO);

    /**
     * @description: 查询TT记录数量
     * @param: [operateTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/tradeTicket/getTTStat")
    Result getTTStat(@RequestBody StatQueryDTO statQueryDTO);

    /**
     * 根据合同号查询修改记录
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/tradeTicket/queryModifyLog")
    Result queryModifyLog(@RequestParam("contractCode") String contractCode);

    /**
     * @description: 获取目的地
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/tradeTicket/getDestination")
    Result getDestination();

    @GetMapping("/tradeTicket/getSignTemplateInfo")
    SignTemplateDTO getSignTemplateInfo(@RequestParam("ttId") Integer ttId);

    @GetMapping("/tradeTicket/complete")
    Result complete(@RequestParam("contractId") Integer contractId, @RequestParam("ttId") Integer ttId);

    @PostMapping("/tradeTicket/queryTTReport")
    Result queryTTReport(@RequestBody ReportDTO reportDTO);

    @PostMapping("/tradeTicket/queryTotalReport")
    Result queryTotalReport(@RequestBody ReportDTO reportDTO);

    @GetMapping("/generateTTByContractId")
    Result generateTTByContractId(@RequestParam("idList") List<Integer> idList);

    @GetMapping("/generateTT")
    Result generateTT(@RequestParam("idStart") Integer idStart, @RequestParam("idEnd") Integer idEnd);

    /**
     * 根据合同号查询TT
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/tradeTicket/queryListByContractCode")
    Result queryListByContractCode(@RequestParam("contractCode") String contractCode);


    @GetMapping("/tradeTicket/mockModify")
    Result mockModify(@RequestParam("t") Integer t, @RequestParam("v") String v);

    @GetMapping("/tradeTicket/getTradeTicketById")
    Result getTradeTicketById(@RequestParam("ttId") Integer ttId);

    @GetMapping("/tradeTicket/getTtAddByTtId")
    Result getTtAddByTtId(@RequestParam("ttId") Integer ttId);

    @GetMapping("/tradeTicket/getTtModifyByTtId")
    Result getTtModifyByTtId(@RequestParam("ttId") Integer ttId);

    @GetMapping("/tradeTicket/updatePriceAllocateId")
    Result updatePriceAllocateId(@RequestParam("contractId") Integer contractId, @RequestParam("allocateId") Integer allocateId);

}
