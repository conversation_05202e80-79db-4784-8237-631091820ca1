package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * v_contract_equity
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_contract_equity")
@ApiModel(value = "权益变更视图", description = "v_contract_equity")
public class ContractEquityVOEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "集团客户")
    private String enterpriseName;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "可反点价次数")
    private Integer ableReversePriceTimes;

    @ApiModelProperty(value = "已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private Integer reversedPriceTimes;

    @ApiModelProperty(value = "申请编号")
    private String lastApplyCode;

    @ApiModelProperty(value = "审批状态")
    private Integer lastApproveStatus;

    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    @ApiModelProperty(value = "尾量关闭数量")
    private BigDecimal closeTailNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;


}
