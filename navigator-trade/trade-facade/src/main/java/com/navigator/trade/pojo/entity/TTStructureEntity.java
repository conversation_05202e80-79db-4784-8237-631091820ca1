package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_tt_structure")
@ApiModel(value = "TtStructureEntity对象", description = "")
public class TTStructureEntity extends TTSubEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /*
    * DELETE @20240807 NEO FOR:MOVE TO PARENT CLASS
    @ApiModelProperty(value = "TTid")
    private Integer ttId;
    */

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "1:A,2:B,3:C")
    private Integer structureType;

    @ApiModelProperty(value = "结构总量")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "总交易日")
    private Integer totalDay;

    @ApiModelProperty(value = "1单位量")
    private BigDecimal unitNum;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "定价开始日期")
    private Date startTime;

    @ApiModelProperty(value = "定价结束日期")
    private Date endTime;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "所属商务ID")
    private Integer customerId;

    @ApiModelProperty(value = "所属商务名称")
    private String customerName;

    @ApiModelProperty(value = "品类")
    @TableField(exist = false)
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "签订日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date signDate;

    @ApiModelProperty(value = "单位增量")
    private BigDecimal unitIncrement;

    @ApiModelProperty(value = "现金返还量")
    private String cashReturn;

    @ApiModelProperty(value = "累积价格")
    private String cumulativePrice;

    @ApiModelProperty(value = "触发价格")
    private String triggerPrice;

    @ApiModelProperty(value = "结构化单位数量")
    private BigDecimal structureUnitNum;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂id")
    private Integer deliveryFactoryId;

    @ApiModelProperty(value = "结构化名称")
    private String structureName;
}
