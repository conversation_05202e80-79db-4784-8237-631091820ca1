package com.navigator.trade.pojo.dto.future;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PriceDetailDTO {
    @ApiModelProperty(value = "基差价（元）")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String extraPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "期货价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String forwardPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "蛋白价差")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String proteinDiffPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "散粕补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String compensationPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "期权费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String optionPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "运费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String transportPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "起吊费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String liftingPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "滞期费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String delayPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = " 高温费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String temperaturePrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = " 其他物流费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String otherDeliveryPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = " 回购折价/解约定赔折价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String buyBackPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "客诉折价")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String complaintDiscountPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = " 转厂补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String transferFactoryPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "其他补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String otherPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "商务补贴")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String businessPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "手续费")
    @NotBlank
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String fee = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "装运费单价")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String shippingFeePrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "出厂价")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String factoryPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "精炼价差")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String refineDiffPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "精炼/分提价差")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String refineFracDiffPrice = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "检验费")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String surveyFees = BigDecimal.ZERO.toString();

    @ApiModelProperty(value = "VE单价")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String vePrice;

    @ApiModelProperty(value = "VE含量")
    @Pattern(regexp = "^([-+])?\\d+(\\.\\d+)?$")
    private String veContent;

}
