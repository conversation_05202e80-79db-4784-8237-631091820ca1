package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.pojo.entity.ContractPriceBaseEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.Data;

@Data
public class ContractAddTTDTO extends TTAddEntity {

    //TODO NEO JASON：原本的类改为 【OMContractAddTTDTO】 了，谨慎使用！

    String contractTypeName;        // 合同类型名称
    String unitName;                // 单位
    String invoiceTypeName;         // 发票类型
    String paymentTypeName;         // 支付类型
    String depositUseRuleName;      // 保证金使用规则
    String deliveryTypeName;        // 运输类型
    String priceEndTypeName;        // 点价截止日期类型


    GoodsInfoVO goodsInfoVO;        //商品信息
    ContractPriceBaseEntity contractPriceInfo;

    public String getContractTypeName() {
        return null == getContractType() ? "" : ContractTypeEnum.getDescByValue(getContractType());
    }

    public String getUnitName() {
        return null == getUnit() ? "" : UnitEnum.getDescByName(getUnit());
    }

    public String getInvoiceTypeName() {
        return null == getInvoiceType() ? "" : InvoiceTypeEnum.getDescByValue(getInvoiceType());
    }

    public String getPaymentTypeName() {
        return null == getPaymentType() ? "" : PaymentTypeEnum.getDescByValue(getPaymentType());
    }

    public String getDepositUseRuleName() {
        return null == getDepositUseRule() ? "" : DepositUseRuleEnum.getDescByValue(getDepositUseRule());
    }

    public String getDeliveryTypeName() {
        return null == getDeliveryType() ? "" : DeliveryTypeEnum.getDescByValue(getDeliveryType());
    }

    public String getPriceEndTypeName() {
        return null == getPriceEndType() ? "" : ContractPriceEndTypeEnum.getDescByValue(getPriceEndType());
    }
}
