package com.navigator.trade.pojo.dto.tradeticket;

import com.navigator.bisiness.enums.BuCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class StatQueryDTO {

    @ApiModelProperty(value = "合同销售类型（1：采购 2：销售）")
    private Integer salesType;

    @ApiModelProperty(value = "品种")
    private String goodsCategoryId;

    private String userId;

    private List<String> siteCodeList;

    private List<Integer> customerIdList;

    private Map<Integer, List<Integer>> companyCustomerIdMap;

    private String buCode = BuCodeEnum.ST.getValue();
}
