package com.navigator.trade.pojo.vo;

import com.navigator.trade.pojo.entity.TTPriceEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON>ong on 2022/2/14 17:32
 */
@Data
@Accessors(chain = true)
public class TtPriceEntityVO {

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    // 已定价量
    private BigDecimal SumPriceNum;

    // 定价价格
    private BigDecimal avgPrice;

    List<TTPriceEntity> TTPriceEntityList;


}
