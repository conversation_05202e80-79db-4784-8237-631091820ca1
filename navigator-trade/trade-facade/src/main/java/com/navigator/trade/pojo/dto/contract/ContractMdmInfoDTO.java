package com.navigator.trade.pojo.dto.contract;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 合同MDM信息的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Accessors(chain = true)
public class ContractMdmInfoDTO {

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "主体名称")
    private String businessEntity;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "配送方式")
    private String contractTerms;

}
