package com.navigator.trade.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-03-03 18:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbt_contract_sign_template_log")
@ApiModel(value = "ContractSignTemplateLogEntity", description = "合同协议模版生成Html记录")
public class ContractSignTemplateLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "协议id")
    private Integer contractSignId;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "ttId")
    private Integer ttId;

    @ApiModelProperty(value = "模版内容")
    private String content;

    @ApiModelProperty(value = "业务数据")
    private String bizData;

    /**
     * 1 模版底板 2 渲染数据后的模版
     */
    @ApiModelProperty(value = "模版类型")
    private Integer type;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
