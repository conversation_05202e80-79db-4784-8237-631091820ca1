package com.navigator.trade.pojo.dto.contract;

import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 变更合同的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class ContractTransferDTO {

    /**
     * 父合同实体
     */
    private ContractEntity contractEntity;

    /**
     * 转月数量
     */
    private BigDecimal transferNum;

    /**
     * 反点价数量
     */
    private BigDecimal reversePricingNum;

    /**
     * 转月合约
     */
    private String domainCode;

    /**
     * 类型
     */
    public Integer ttTranferType;

    /**
     * 基差价
     */
    private BigDecimal extraPrice;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 转入合约
     */
    private String transferDominantCode;


    /**
     * 成交价格
     */
    private BigDecimal transactionPrice;

    /**
     * 最新期货价格
     */
    private BigDecimal latestForwardPrice;

    /**
     * 申请单Id
     */
    private Integer priceApplyId;

    /**
     * 分配单id
     */
    private Integer priceAllocateId;

    /**
     * 分配数量
     */
    private BigDecimal allocateNum;

    /**
     * 成交价差
     */
    private BigDecimal transactionDiffPrice;

    /**
     * 期货代码前缀
     */
    private String transferFutureCode;

}
