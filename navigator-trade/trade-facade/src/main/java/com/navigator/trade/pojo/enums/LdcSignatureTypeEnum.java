package com.navigator.trade.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/27
 */
@Getter
@AllArgsConstructor
public enum LdcSignatureTypeEnum {
    /**
     * 0.不签章 1.线下签章 2.标准签署 3.静默签署
     */
    NO_SIGN(0, "不签章", "NO_SIGN"),
    OFFLINE_SIGN(1, "线下签章","OFFLINE_SIGN"),
    NORMAL(2, "标准签署","NORMAL"),
    SILENCE(3, "静默签署","SILENCE"),
    ;

    ;
    Integer value;
    String desc;
    String code;

    public static LdcSignatureTypeEnum getByType(Integer value) {
        for (LdcSignatureTypeEnum type : LdcSignatureTypeEnum.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}
