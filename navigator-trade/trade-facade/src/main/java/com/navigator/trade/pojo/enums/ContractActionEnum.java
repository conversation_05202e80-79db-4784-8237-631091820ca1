package com.navigator.trade.pojo.enums;

import com.navigator.bisiness.enums.TTTypeEnum;
import lombok.Getter;

@Getter
public enum ContractActionEnum {
    /**
     *
     */
    UNKNOWN(-1, "未知", "UNKNOWN", "未知", "UNKNOWN"),
    NEW(1, "新增", "New", "新增", "ADD"),
    REVISE(2, "修改", "Revise", "修改", "REVISE"),
    REVISE_CUSTOMER(4, "合同主体修改", "CustomerRevise", "合同主体修改", "REVISE"),
    SPLIT(3, "拆分", "Split", "拆分", "SPLIT"),
    SPLIT_CUSTOMER(5, "合同主体拆分", "CustomerSplit", "合同主体拆分", "SPLIT"),
    WARRANT_WRITEOFF(6, "仓单合同注销", "warrantWriteOff", "仓单合同注销", "WRITEOFF"),
    PRICING(110, "点价申请", "Pricing", "点价申请", "PRICE"),
    PRICE_CONFIRM(111, "点价", "PriceConfirm", "点价结果", "PRICE"),
    STRUCTURE_PRICING(112, "结构化定价申请", "StructuredPricing", "结构化点价申请", "STRUCTURE"),
    STRUCTURE_PRICE_CONFIRM(113, "结构化定价", "StructuredPriceConfirm", "结构化定价结果", "PRICE"),
    PRICE_RESULT(114, "点价", "PriceResult", "点价结果录入", "PRICE"),
    PRICE_FIXING(115, "确价申请", "PriceFixing", "确价申请", "PRICE"),
    PRICE_FIXED(116, "确价", "PriceFixed", "确价结果", "FIXED"),
    TRANSFERRING(121, "转月申请", "Transferring", "转月申请", "TRANSFER"),
    TRANSFER_CONFIRM(122, "部分转月", "TransferConfirm", "转月结果", "TRANSFER"),
    TRANSFER_ALL_CONFIRM(123, "全部转月", "TransferResult", "转月结果录入", "TRANSFER"),
    TRANSFER_RESULT(124, "转月", "TransferResult", "转月结果录入", "TRANSFER"),
    REVERSE_PRICING(130, "反点价申请", "ReversePricing", "反点价申请", "REVERSE_PRICE"),
    REVERSE_PRICE_CONFIRM(131, "反点价", "ReversePrice", "反点价", "REVERSE_PRICE"),
    REVERSE_PRICE_ALL_CONFIRM(132, "全部反点价", "AllReversePrice", "全部反点价", "REVERSE_PRICE"),
    WASHOUT(211, "解约定赔", "Washout", "解约定赔", "WASHOUT"),
    BUYBACK(221, "回购", "BuyBack", "回购", "BUYBACK"),
    PUT_BACK(231, "回售", "PutBack", "回售", "PUT_BACK"),
    CLOSED(241, "关闭", "Closed", "关闭", "CLOSED"),
    INVALID(251, "作废", "Invalid", "作废", "INVALID"),
    CONTRACT_CANCEL(261,"合同取消", "contractCancel", "合同取消", "CONTRACT_CANCEL"),
    ALLOCATE(202, "分配","ALLOCATE", "分配", "ALLOCATE"),
    ASSIGN(203, "转让","ASSIGN", "转让", "ASSIGN"),
    WRITE_OFF_A(205, "注销（不修改货品和含税单价）", "writeOffA", "注销（不修改货品和含税单价）","WRITE_OFF_A"),
    WRITE_OFF_B(206, "注销不修改提货方且修改（货品或含税单价）", "writeOffB", "注销不修改提货方且修改（货品或含税单价）", "WRITE_OFF_B"),
    WRITE_OFF_C(207, "注销同时修改提货方，修改（货品或含税单价）", "writeOffC", "注销同时修改提货方，修改（货品或含税单价）","WRITE_OFF_C"),
    WRITE_OFF_OM_A(208, "豆二注销不修改提货方", "writeOffOMA", "豆二注销不修改提货方","WRITE_OFF_OM_A"),
    WRITE_OFF_OM_B(209, "豆二注销修改提货方", "writeOffOMB", "豆二注销修改提货方", "WRITE_OFF_OM_B"),


    ;

    /*
    1、ContractSource = TradeType.value
    2、TT & Contract & Sign 交互 SalesType & SubGoodsCategory & TradeType
    3、Handler内聚，
    */
    int actionValue;
    String desc;
    String actionCode;
    String memo;
    String processorCode;

    ContractActionEnum(int actionValue, String desc, String actionCode, String memo, String processorCode) {
        this.actionValue = actionValue;
        this.desc = desc;
        this.actionCode = actionCode;
        this.memo = memo;
        this.processorCode = processorCode;
    }

    public static ContractActionEnum getByType(Integer actionValue) {
        for (ContractActionEnum contractActionEnum : ContractActionEnum.values()) {
            if (contractActionEnum.getActionValue() == actionValue) {
                return contractActionEnum;
            }
        }
        return ContractActionEnum.NEW;
    }

    public static String getDescByValue(Integer value) {
        return getByType(value).getDesc();
    }

    public int getTradeType() {
        //兼容TradeType
        return actionValue;
    }

    public static String getDescByType(String typeName) {
        for (ContractActionEnum contractActionEnum : ContractActionEnum.values()) {
            if (contractActionEnum.name().equals(typeName)) {
                return contractActionEnum.getDesc();
            }
        }
        return ContractActionEnum.NEW.name();
    }

}
