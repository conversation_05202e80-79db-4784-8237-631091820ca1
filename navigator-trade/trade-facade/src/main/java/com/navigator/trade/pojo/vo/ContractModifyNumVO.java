package com.navigator.trade.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 变更合同数量的VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Data
@Accessors(chain = true)
public class ContractModifyNumVO {

    /**
     * 可变更量
     */
    private BigDecimal canModifyNum;

    /**
     * 未定价量
     */
    private BigDecimal notPricedNum;

    /**
     * 已定价量
     */
    private BigDecimal pricedNum;

    /**
     * 未开单量
     */
    private BigDecimal notBillNum;

    /**
     * 已开单量
     */
    private BigDecimal totalBillNum;

    /**
     * 未开提货量
     */
    private BigDecimal notDeliveryNum;

    /**
     * 已提货量
     */
    private BigDecimal totalDeliveryNum;

    /**
     * 调拨量
     */
    private BigDecimal allocateNum;

    /**
     * 已提货量
     */
    private BigDecimal deliveryNum;

    /**
     * 合同定价状态
     */
    private Integer pricedStatus;
    /**
     * 可转月数量
     */
    private BigDecimal transferNum;


    private BigDecimal ablePriceNum;

    /**
     * 合同数量
     */
    private BigDecimal contractNum;

    /**
     * 提货申请数量
     */
    private BigDecimal applyDeliveryNum;

    // BUGFIX：Case-1003256-合同已全量开单，仍可继续拆分导致合同超开 Author: Mr 2025-06-10 Start
    /**
     * 已拆分未合规数量
     */
    private BigDecimal splitNotCompliantNum;
    // BUGFIX：Case-1003256-合同已全量开单，仍可继续拆分导致合同超开 Author: Mr 2025-06-10 End

}
