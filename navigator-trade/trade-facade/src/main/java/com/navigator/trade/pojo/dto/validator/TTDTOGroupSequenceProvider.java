package com.navigator.trade.pojo.dto.validator;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import java.util.ArrayList;
import java.util.List;

public class TTDTOGroupSequenceProvider implements DefaultGroupSequenceProvider<OMContractAddTTDTO> {
    @Override
    public List<Class<?>> getValidationGroups(OMContractAddTTDTO OMContractAddTTDTO) {
        List<Class<?>> defaultGroupSequence = new ArrayList<>();
        defaultGroupSequence.add(OMContractAddTTDTO.class);
        if (OMContractAddTTDTO != null) {
        }
        return defaultGroupSequence;
    }
}