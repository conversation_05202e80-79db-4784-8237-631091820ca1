package com.navigator.trade.app.trade;

import cn.hutool.core.util.RandomUtil;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.utdata.DataCheckMocker;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

//@SpringBootTest
//@Transactional
//@Rollback
class TradeHandlerSimpleTest {


    @Test
    void testClassValue() {
        List<TTDTO> ttdtoList = new ArrayList<>();
        TTDTO ttdto = new TTDTO();
        ttdto.setBuCode("111");
        ttdtoList.add(ttdto);

        TTDTO ttdto2 = new TTDTO();
        ttdto2.setBuCode("222");
        ttdtoList.add(ttdto2);

        for (TTDTO tto : ttdtoList) {
            testV(tto);
        }

        System.out.println(ttdtoList);

    }

    void testV(TTDTO ttdto) {
        ttdto.setBuCode(ttdto.getBuCode() + "333");
    }

    @Test
    void getStrategy() {


        System.out.println("111111111");

        /*for (Field declaredField : OMContractAddTTDTO.class.getDeclaredFields()) {
            System.out.println(declaredField.getName());
        }*/


        List<String> listM = new ArrayList<>();

        OMContractAddTTDTO o1 = new OMContractAddTTDTO();
        listM.addAll(getMethodNameList(o1));
        listM.addAll(getMethodNameList(o1));

        Collections.sort(listM);

        for (String s : listM) {
            System.out.println(s);
        }

    }

    private List<String> getMethodNameList(Object objects) {
        List<String> listM = new ArrayList<>();
        for (Method declaredMethod : objects.getClass().getDeclaredMethods()) {
            String methodName = declaredMethod.getName();
            if (methodName.contains("set")) {
                String setMethodName = "";
                setMethodName = "." + methodName + "(";
                String parameterType = declaredMethod.getParameters()[0].getType().getName();
                if (parameterType.contains("String")) {
                    setMethodName = setMethodName + "\"" + RandomUtil.randomString(10) + "\"";
                } else if (parameterType.contains("Integer")) {
                    if (methodName.contains("Status") || methodName.contains("Type")) {
                        setMethodName = setMethodName + RandomUtil.randomInt(1, 9);
                    } else {
                        setMethodName = setMethodName + RandomUtil.randomInt(1, 9999);
                    }

                } else if (parameterType.contains("Decimal")) {
                    setMethodName = setMethodName + "BigDecimal.valueOf(" + RandomUtil.randomInt(1, 9999) + ")";
                } else if (parameterType.contains("Date")) {
                    setMethodName = setMethodName + "new Date()";
                } else {
                    setMethodName = setMethodName + "null";
                }
                setMethodName = setMethodName + ")";
                listM.add(setMethodName);
            }
        }

        return listM;
    }

    @Test
    void testCompareDTO() {
        Map<String, Map> mapDTOs = DataCheckMocker.intDTOFiledsMap();
        Map<String, String> mapOmc = mapDTOs.get("Omc");
        Map<String, String> mapSca = mapDTOs.get("Sca");
        Map<String, String> mapOMContractAddTTDTO = mapDTOs.get("OMContractAddTTDTO");
        Map<String, String> mapCommonTradeInfoDTO = mapDTOs.get("CommonTradeInfoDTO");
        Map<String, String> mapPriceDetailDTO = mapDTOs.get("PriceDetailDTO");
        Map<String, String> mapPriceDetailBO = mapDTOs.get("PriceDetailBO");
        Map<String, String> mapSalesContractAddTTDTO = mapDTOs.get("SalesContractAddTTDTO");
        Map<String, String> mapTTCommonDTO = mapDTOs.get("TTCommonDTO");
        Map<String, String> mapSalesContractReviseTTDTO = mapDTOs.get("SalesContractReviseTTDTO");
        Map<String, String> mapSalesContractSplitTTDTO = mapDTOs.get("SalesContractSplitTTDTO");
        Map<String, String> mapSalesContractTTPriceDTO = mapDTOs.get("SalesContractTTPriceDTO");
        Map<String, String> mapSalesContractTTTransferDTO = mapDTOs.get("SalesContractTTTransferDTO");
        Map<String, String> mapSalesStructurePriceTTDTO = mapDTOs.get("SalesStructurePriceTTDTO");
        Map<String, String> mapTradeTicketEntity = mapDTOs.get("TradeTicketEntity");
        Map<String, String> mapTTAddEntity = mapDTOs.get("TTAddEntity");
        Map<String, String> mapTTModifyEntity = mapDTOs.get("TTModifyEntity");
        Map<String, String> mapTTPriceEntity = mapDTOs.get("TTPriceEntity");
        Map<String, String> mapTTStructureEntity = mapDTOs.get("TTStructureEntity");
        Map<String, String> mapTTTranferEntity = mapDTOs.get("TTTranferEntity");
        Map<String, String> mapContractPriceBaseEntity = mapDTOs.get("ContractPriceBaseEntity");

        Map<String, String> mapLogicFields = DataCheckMocker.initLogicInfoMap();

        StringBuilder sb = new StringBuilder();

        Map<String, String> m1 = BeanConvertUtils.getObjectFieldMap(new TTAddEntity(),true);
        Map<String, String> m2 = BeanConvertUtils.getObjectFieldMap(new TTAddEntity(),true);

        for (String s : mapTTAddEntity.keySet()) {
            if (m1.keySet().contains(s)) {
                //sb.append(s).append("\t").append("\n");
            } else {
                sb.append(s).append("\t0").append("\n");
            }
        }
        System.out.println(sb.toString());

    }

    @Test
    void testCompareDTO2() {

        String filePath = "D:\\test.xlsx";
        List<Map<String, Object>> list = EasyPoiUtils.importExcel(filePath);
        for (Map<String, Object> map : list) {
            System.out.println("=================");
            for (String s : map.keySet()) {
                Object o = map.get(s);
                String k = String.valueOf(o);

                System.out.println(s + ":" +o.getClass().getName()+"="+ k);
            }
        }

        List<CompareObjectDTO> ll = BeanCompareUtils.compareMapFields(list.get(0), list.get(1), null);
        System.out.println(ll);




    }

    @Test
    void testGetDtoFields() {
        Map<String, String> m1 = BeanConvertUtils.getObjectFieldMap(new OMContractAddTTDTO(),true);
        m1.forEach((k, v) -> System.out.println(k + "\t" + v ));
    }


}