package com.navigator.trade.utdata;

import cn.hutool.core.util.RandomUtil;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerDataMocker {

    public static CustomerEntity genCustomer() {
        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.setId(10001)
                .setLinkageCustomerCode("LKGC_" + RandomUtil.randomNumbers(6))
                .setMdmCustomerCode("LDCC_" + RandomUtil.randomNumbers(8))
                .setParentId(0)
                .setUseYqq(1)
                .setFrameExpired(0)
                .setTemplateExpired(0)
                .setOriginalPaper(1)
                .setPaperNeed("要求邮寄")
                .setType(1)
                .setGrade("VIP")
                .setName("上海甜甜圈商贸有限公司")
                .setShortName("上海甜甜圈")
                .setContact("唐大明")
                .setLargestAdvance(BigDecimal.valueOf(1000000))
                .setCreditDays(10)
                .setPaymentType(PaymentTypeEnum.CREDIT.getType())
                .setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue())
                .setPhone("15800001111")
                .setAddress("上海市浦东新区世纪大道1008号")
                .setMoving(1000)
                .setCnfSelling(1000)
                .setEnterprise(0)
                .setLng("116.397128")
                .setLat("39.916527")
                .setAxCode("AX" + RandomUtil.randomNumbers(4))
                .setTradeType("active")
                .setStatus(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setIsCustomer(1)
                .setIsSupplier(0)
                .setSignPlace("上海市浦东新区")
                .setCode("NC" + RandomUtil.randomNumbers(6));
        return customerEntity;
    }

    public static CustomerEntity genSupplier() {
        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.setId(20001)
                .setLinkageCustomerCode("LKGS_" + RandomUtil.randomNumbers(6))
                .setMdmCustomerCode("LDCS_" + RandomUtil.randomNumbers(6))
                .setParentId(0)
                .setUseYqq(1)
                .setFrameExpired(0)
                .setTemplateExpired(0)
                .setOriginalPaper(1)
                .setPaperNeed("要求邮寄")
                .setType(1)
                .setGrade("VIP")
                .setName("苏州达孚贸易有限公司")
                .setShortName("SZ")
                .setContact("苏大强")
                .setLargestAdvance(BigDecimal.valueOf(1000000))
                .setCreditDays(10)
                .setPaymentType(PaymentTypeEnum.CREDIT.getType())
                .setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue())
                .setPhone("13112332123")
                .setAddress("苏州市金鸡湖大道1024号")
                .setMoving(1000)
                .setCnfSelling(1000)
                .setEnterprise(0)
                .setLng("258.397128")
                .setLat("369.916527")
                .setAxCode("AX" + RandomUtil.randomNumbers(4))
                .setTradeType("active")
                .setStatus(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setIsCustomer(0)
                .setIsSupplier(1)
                .setSignPlace("苏州市金鸡湖大道")
                .setCode("NS" + RandomUtil.randomNumbers(6));
        return customerEntity;
    }

    public static CustomerDTO genSalesCustomer() {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setName("上海甜头菜");
        customerDTO.setShortName("甜头菜");
        customerDTO.setAddress("上海市浦东新区甜头菜");

        return customerDTO;
    }

    public static CustomerDTO genSalesSupplier() {
        CustomerDTO supplierDTO = new CustomerDTO();
        supplierDTO.setName("上海路易达孚国际贸易有限公司");
        supplierDTO.setShortName("上海达孚");
        supplierDTO.setAddress("上海市浦东新区晶耀前滩");

        List<CustomerBankDTO> bankDTOList = new ArrayList<>();
        CustomerBankDTO customerBankDTO = new CustomerBankDTO();
        customerBankDTO.setBankName("中国农业银行上海分行（销售-LDC）");
        customerBankDTO.setBankAccountNo("*****************");
        bankDTOList.add(customerBankDTO);

        supplierDTO.setCustomerBankDTOS(bankDTOList);

        return supplierDTO;
    }

    public static CustomerDTO genPurchaseSupplier() {
        CustomerDTO supplierDTO = new CustomerDTO();
        supplierDTO.setName("嘉吉中国国际贸易有限公司");
        supplierDTO.setShortName("嘉吉中国");
        supplierDTO.setAddress("上海市浦东新区嘉吉科技园");

        List<CustomerBankDTO> bankDTOList = new ArrayList<>();
        CustomerBankDTO customerBankDTO = new CustomerBankDTO();
        customerBankDTO.setBankName("中国农业银行上海分行（采购-客户）");
        customerBankDTO.setBankAccountNo("3033303330333033303");
        bankDTOList.add(customerBankDTO);

        supplierDTO.setCustomerBankDTOS(bankDTOList);

        return supplierDTO;
    }

    public static CustomerDTO genPurchaseCustomer() {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setName("天津路易达孚国际贸易有限公司");
        customerDTO.setShortName("天津IB");
        customerDTO.setAddress("天津市路易达孚物流园");
        return customerDTO;
    }


}
