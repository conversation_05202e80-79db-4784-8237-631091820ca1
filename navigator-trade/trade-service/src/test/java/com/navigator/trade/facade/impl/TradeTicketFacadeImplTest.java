package com.navigator.trade.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
@Transactional
@Rollback
class TradeTicketFacadeImplTest {

    @Resource
    TradeTicketFacadeImpl tradeTicketFacade;


    @Test
    public void testqueryTTDetail() {
        tradeTicketFacade.queryTTDetail(4458);
    }

    @Test
    public void testConvertTradeTicketDTO() {
        OMContractAddTTDTO omContractAddTTDTO = new OMContractAddTTDTO();
        omContractAddTTDTO.setContractType("1")
                .setSupplierId("1")
                .setCustomerId("1")
                .setCustomerCode("TTC")
                .setCustomerName("上海甜头菜v")
                .setGoodsCategoryId("11")
                .setGoodsPackageId("1")
                .setGoodsSpecId("1")
                .setCreditDays(0)
                .setDeliveryFactoryCode("TJ")
                .setDeliveryFactoryName("天津达孚")
                .setDelayPayFine("100")
                .setSignPlace("天津")
                .setSignDate(new Date())
                .setOem(0)
                .setDepositUseRule(1)
                .setDeliveryType(1)
                .setDestination("上海")
                .setWeightCheck("")
                .setNeedPackageWeight(0)
                .setPackageWeight("")
                .setShipWarehouseId("1")
                .setWeightTolerance(1)
                .setOwnerId("695")
//                .setMemo("test add")
                .setIsStf(0)
                .setPriceStartTime("2022-05-01")
//                .setPriceEndTime("三个月后")
//                .setPriceEndType(2)
                .setContractId(null)
                .setContractCode(null)
                .setTtId(null)
                .setType(TTTypeEnum.NEW.getType())
                .setCompletedStatus(1)
                .setUserId("695")
                .setSupplierAccountId(200002);

        List<KeyTradeInfoTTDTO> keyTradeInfoTTDTOList = new ArrayList<>();


        KeyTradeInfoTTDTO keyTradeInfoTTDTO = (KeyTradeInfoTTDTO) new KeyTradeInfoTTDTO()
                .setTtId(null)
                .setCode(null)
                .setUnitPrice("3050")
                .setContractNum("1000")
                .setDeliveryStartTime(new Date())
                .setDeliveryEndTime(new Date())
                .setDomainCode("2205")
                .setDepositRate(10)
                .setAddedDepositRate("0")
                .setDepositAmount("300000");
        keyTradeInfoTTDTO.setPriceDetailDTO(new PriceDetailDTO()
                .setExtraPrice("15")
                .setForwardPrice("17")
                .setFactoryPrice("3030")
                .setProteinDiffPrice("22")
                .setCompensationPrice("6")
                .setOptionPrice("71")
                .setTransportPrice("72")
                .setLiftingPrice("73")
                .setDelayPrice("74")
                .setTemperaturePrice("75")
                .setOtherDeliveryPrice("76")
                .setBuyBackPrice("77")
                .setComplaintDiscountPrice("78")
                .setTransferFactoryPrice("79")
                .setOtherPrice("80")
                .setBusinessPrice("81")
                .setFee("82")
                .setShippingFeePrice("83")
                .setRefineDiffPrice("84")
        );
        keyTradeInfoTTDTOList.add(keyTradeInfoTTDTO);


        keyTradeInfoTTDTO = (KeyTradeInfoTTDTO) new KeyTradeInfoTTDTO()
                .setTtId(null)
                .setCode(null)
                .setUnitPrice("3055")
                .setContractNum("2000")
                .setDeliveryStartTime(new Date())
                .setDeliveryEndTime(new Date())
                .setDomainCode("2201")
                .setDepositRate(10)
                .setAddedDepositRate("0")
                .setDepositAmount("350000");
        keyTradeInfoTTDTO.setPriceDetailDTO(new PriceDetailDTO()
                .setExtraPrice("15")
                .setForwardPrice("17")
                .setFactoryPrice("3050")
                .setProteinDiffPrice("22")
                .setCompensationPrice("23")
                .setOptionPrice("24")
                .setTransportPrice("25")
                .setLiftingPrice("26")
                .setDelayPrice("27")
                .setTemperaturePrice("28")
                .setOtherDeliveryPrice("29")
                .setBuyBackPrice("30")
                .setComplaintDiscountPrice("31")
                .setTransferFactoryPrice("32")
                .setOtherPrice("33")
                .setBusinessPrice("34")
                .setFee("35")
                .setShippingFeePrice("36")
                .setRefineDiffPrice("37")
        );
        keyTradeInfoTTDTOList.add(keyTradeInfoTTDTO);

        keyTradeInfoTTDTO = (KeyTradeInfoTTDTO) new KeyTradeInfoTTDTO()
                .setTtId(null)
                .setCode(null)
                .setUnitPrice("3060")
                .setContractNum("3000")
                .setDeliveryStartTime(new Date())
                .setDeliveryEndTime(new Date())
                .setDomainCode("2209")
                .setDepositRate(10)
                .setAddedDepositRate("0")
                .setDepositAmount("360000");
        keyTradeInfoTTDTO.setPriceDetailDTO(new PriceDetailDTO()
                .setExtraPrice("15")
                .setForwardPrice("17")
                .setFactoryPrice("3051")
                .setProteinDiffPrice("51")
                .setCompensationPrice("52")
                .setOptionPrice("53")
                .setTransportPrice("54")
                .setLiftingPrice("55")
                .setDelayPrice("56")
                .setTemperaturePrice("57")
                .setOtherDeliveryPrice("58")
                .setBuyBackPrice("59")
                .setComplaintDiscountPrice("60")
                .setTransferFactoryPrice("61")
                .setOtherPrice("62")
                .setBusinessPrice("63")
                .setFee("64")
                .setShippingFeePrice("65")
                .setRefineDiffPrice("66")
        );
        keyTradeInfoTTDTOList.add(keyTradeInfoTTDTO);

        omContractAddTTDTO.setTtKernelDTOList(keyTradeInfoTTDTOList);



    }

    @Test
    void approveTT() {

        List<ApprovalDTO> approvalDTOList=new ArrayList<>();
        ApprovalDTO approvalDTO=new ApprovalDTO();
        approvalDTO.setTaskId("14485020");
        approvalDTO.setApproveStatus("1");
        approvalDTO.setMemo("22222222");
        approvalDTO.setTtCode("SC2022040814293085");

        approvalDTOList.add(approvalDTO);

        approvalDTO=new ApprovalDTO();
        approvalDTO.setTaskId("14485099");
        approvalDTO.setApproveStatus("1");
        approvalDTO.setMemo("22222222");
        approvalDTO.setTtCode("SC20220408142930999");
        approvalDTOList.add(approvalDTO);

        System.out.println(JSON.toJSONString(approvalDTOList));

        tradeTicketFacade.approveTT(approvalDTO);
    }

    @Test
    public void testStructureTradeTicketDTO() {
        TTDTO ttdto = new TTDTO();
        SalesStructurePriceTTDTO structureTTDTO = new SalesStructurePriceTTDTO();
        structureTTDTO.setStructureType(1);
        structureTTDTO.setTotalNum(new BigDecimal(5000));
        structureTTDTO.setCumulativePrice("22222");
        structureTTDTO.setStructureUnitNum(new BigDecimal(5000));
        structureTTDTO.setStartTime(new Date());
        structureTTDTO.setEndTime(new Date());
        structureTTDTO.setSignDate(new Date());
        structureTTDTO.setCashReturn("1111");
        structureTTDTO.setGoodsCategoryId(11);
        structureTTDTO.setOwnerId("0");
        structureTTDTO.setSalesType(1);
        structureTTDTO.setSupplierId(20);
        structureTTDTO.setSupplierName("test");
        structureTTDTO.setCustomerId(22);
        structureTTDTO.setCustomerName("customerName");
        structureTTDTO.setDomainCode("2301");

        ttdto.setSalesStructurePriceTTDTO(structureTTDTO);
        ttdto.setProcessorType(ProcessorTypeEnum.SBM_S_STRUCTURE_PRICE.getTtValue());
        tradeTicketFacade.saveTT(ttdto);
    }
}
