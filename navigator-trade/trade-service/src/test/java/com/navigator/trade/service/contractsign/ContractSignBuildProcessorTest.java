package com.navigator.trade.service.contractsign;

import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.customer.pojo.vo.CustomerTemplateVO;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.trade.dao.ContractPriceDao;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.TemplateDao;
import com.navigator.trade.dao.TtAddDao;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.TemplateConditionDTO;
import com.navigator.trade.pojo.dto.tradeticket.ContractPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.utdata.CustomerDataMocker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class ContractSignBuildProcessorTest {
    @MockBean
    protected AttributeFacade attributeFacade;
    @MockBean
    protected SystemRuleFacade systemRuleFacade;
    @MockBean
    protected CustomerFacade customerFacade;
    @MockBean
    protected CustomerDetailFacade customerDetailFacade;
    @MockBean
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @MockBean
    private FileProcessFacade fileProcessFacade;
    @MockBean
    private AzureBlobUtil azureBlobUtil;

    @MockBean
    private LkgContractFacade lkgContractFacade;

    @Resource
    IContractQueryService contractQueryService;
    @Resource
    ITradeTicketQueryService ttQueryService;

    @Resource
    private ContractSignDao contractSignDao;

    @Resource
    ContractSignBuildProcessor contractSignBuildProcessor;
    @Resource
    ContractSignRobot contractSignRobot;

    @Resource
    ContractPriceDao contractPriceDao;
    @Resource
    TemplateDao templateDao;

    @Resource
    TtAddDao ttAddDao;

    @BeforeEach
    void setUp() {

        AttributeValueEntity dd = new AttributeValueEntity();
        dd.setName("43%");
        Mockito.when(attributeFacade.getAttributeValueById(Mockito.anyInt())).thenReturn(dd);

        SystemRuleItemEntity rule1 = new SystemRuleItemEntity()
                .setRuleKey("M-S-自提-卖方计量")
                .setRuleValue("重量以卖方计量为准。如买方对重量有异议，应在收货后下一个工作日的下午5点前书面提出，逾期视为货物符合要求。买方提出异议后，由双方认可的第三方检验机构进行复验。该复验结果为重量检验的最终结果。复检费用由过错方（即复检结果对其主张（更为）不利的一方）承担");
        Mockito.when(systemRuleFacade.getRuleItemById(7)).thenReturn(rule1);
        Mockito.when(systemRuleFacade.getRuleItemById(8)).thenReturn(rule1);

        SystemRuleItemEntity rule2 = new SystemRuleItemEntity()
                .setRuleKey("上海浦东新区")
                .setRuleValue("上海浦东新区碧波路园区");
        Mockito.when(systemRuleFacade.getRuleItemById(39)).thenReturn(rule2);
        SystemRuleItemEntity rule3 = new SystemRuleItemEntity()
                .setMemo("标包50KG，卖方提供编织袋，包装袋不计价，不返还。每条编织袋扣重100克");
        Mockito.when(systemRuleFacade.getRuleItemById(24)).thenReturn(rule3);
        Mockito.when(systemRuleFacade.getRuleItemById(29)).thenReturn(rule3);

        //销售 5-->6
        //采购 7-->138
        CustomerDTO s5 = CustomerDataMocker.genSalesSupplier();
        CustomerDTO c6 = CustomerDataMocker.genSalesCustomer();
        CustomerDTO c138 = CustomerDataMocker.genPurchaseCustomer();
        CustomerDTO s7 = CustomerDataMocker.genPurchaseSupplier();

        Mockito.when(customerFacade.getCustomerById(5)).thenReturn(s5);
        Mockito.when(customerFacade.getCustomerById(6)).thenReturn(c6);
        Mockito.when(customerFacade.getCustomerById(7)).thenReturn(s7);
        Mockito.when(customerFacade.getCustomerById(138)).thenReturn(c138);

        CustomerTemplateVO ct5 = new CustomerTemplateVO();
        CustomerTemplateVO.ContactFactory cf5 = new CustomerTemplateVO.ContactFactory()
                .setEmail("<EMAIL>")
                .setContactPhone("13800138000")
                .setContactName("（销售）达孚王大川");
        List<CustomerTemplateVO.ContactFactory> list5 = new ArrayList<>();
        list5.add(cf5);
        ct5.setContactFactories(list5);

        CustomerTemplateVO ct6 = new CustomerTemplateVO();
        CustomerTemplateVO.ContactFactory cf6 = new CustomerTemplateVO.ContactFactory()
                .setEmail("<EMAIL>")
                .setContactPhone("18888888888")
                .setContactName("（销售）富农李庆国");
        List<CustomerTemplateVO.ContactFactory> list6 = new ArrayList<>();
        list6.add(cf6);
        ct6.setContactFactories(list6);

        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(5, 11)).thenReturn(ct5);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(6, 11)).thenReturn(ct6);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(5, 12)).thenReturn(ct5);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(6, 12)).thenReturn(ct6);

        CustomerTemplateVO ct7 = new CustomerTemplateVO();
        CustomerTemplateVO.ContactFactory cf7 = new CustomerTemplateVO.ContactFactory()
                .setEmail("<EMAIL>")
                .setContactPhone("16666666666")
                .setContactName("（采购）嘉吉销售张三丰");
        List<CustomerTemplateVO.ContactFactory> list7 = new ArrayList<>();
        list7.add(cf7);
        ct7.setContactFactories(list7);

        CustomerTemplateVO ct138 = new CustomerTemplateVO();
        CustomerTemplateVO.ContactFactory cf138 = new CustomerTemplateVO.ContactFactory()
                .setEmail("<EMAIL>")
                .setContactPhone("13811111111")
                .setContactName("（采购）达孚采购王小米");
        List<CustomerTemplateVO.ContactFactory> list138 = new ArrayList<>();
        list138.add(cf138);
        ct138.setContactFactories(list138);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(7, 11)).thenReturn(ct7);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(138, 11)).thenReturn(ct138);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(7, 12)).thenReturn(ct7);
        Mockito.when(customerFacade.queryTemplateContactFactoryByCustomerId(138, 12)).thenReturn(ct138);



        FactoryWarehouseEntity warehouseEntity = new FactoryWarehouseEntity()
                .setAddress("江苏省张家港保税区宝岛路1号(UT)")
                .setDeliveryPoint("UT江海码头船板/车板交货（“指定地点”）");

        Mockito.when(factoryWarehouseFacade.queryFactoryWarehouseById(Mockito.anyInt())).thenReturn(warehouseEntity);

        Mockito.when(lkgContractFacade.getLkgContract(Mockito.anyString())).thenReturn(null);
    }

    @Test
    public void testCreatePdfByTTCode() {
        String ttcode = "PC202206171350488365";
        TradeTicketDTO tt = ttQueryService.getTTDetailInfo(ttcode);
        if (null != tt) {
            ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();


            ContractSignEntity contractSignEntity = contractSignDao.getSignDetailByTtId(tt.getId());

            int contractId = contractSignEntity.getContractId();
            ContractEntity contractEntity = contractQueryService.getContractById(contractId);
            if (contractEntity.getSalesType() == 2) {
                contractEntity.setCustomerId(6);
                contractEntity.setSupplierId(5);
            } else {
                contractEntity.setCustomerId(138);
                contractEntity.setSupplierId(7);
            }
            ContractDetailInfoDTO contractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, contractEntity);

            ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
            contractPriceEntity = contractPriceDao.getContractPriceEntityContractId(contractId);
            contractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity));

            contractSignTemplateDTO.setTradeTicketDTO(tt);

            Integer srcContractId = tt.getSourceContractId();
            if (null != srcContractId && srcContractId > 0) {
                ContractEntity srcContractEntity = contractQueryService.getContractById(srcContractId);
                srcContractEntity.setCustomerId(6);
                srcContractEntity.setSupplierId(5);
                ContractDetailInfoDTO srcContractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, srcContractEntity);

                ContractPriceEntity contractPriceEntity2 = new ContractPriceEntity();
                contractPriceEntity2 = contractPriceDao.getContractPriceEntityContractId(srcContractId);
                srcContractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity2));

                contractSignTemplateDTO.setSourceContractDetailInfoDTO(srcContractDetailInfoDTO);
            }

            contractSignTemplateDTO.setContractDetailInfoDTO(contractDetailInfoDTO);
            contractSignTemplateDTO.setContractSignDTO(contractSignEntity);
            contractSignTemplateDTO.setContractId(contractDetailInfoDTO.getId());

//            contractSignBuildProcessor.createPdf(contractSignTemplateDTO);
        }


    }


    @Test
    public void testBuildDTO() {


        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();

        //新增797
        //转月682
        //反点价712
        //点价798
        //回购  583
        //解约定赔581
        //关闭695


        Integer signId = 1349;
        ContractSignEntity contractSignEntity = contractSignDao.getById(signId);

        int contractId = contractSignEntity.getContractId();
        ContractEntity contractEntity = contractQueryService.getContractById(contractId);
        contractEntity.setCustomerId(6);
        contractEntity.setSupplierId(5);
        ContractDetailInfoDTO contractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, contractEntity);

        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        contractPriceEntity = contractPriceDao.getContractPriceEntityContractId(contractId);
        contractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity));

        Integer ttid = contractSignEntity.getTtId();
        TradeTicketDTO tt = ttQueryService.getTTDetailInfo(ttid);

        contractSignTemplateDTO.setTradeTicketDTO(tt);

        Integer srcContractId = tt.getSourceContractId();
        if (null != srcContractId && srcContractId > 0) {
            ContractEntity srcContractEntity = contractQueryService.getContractById(srcContractId);
            srcContractEntity.setCustomerId(6);
            srcContractEntity.setSupplierId(5);
            ContractDetailInfoDTO srcContractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, srcContractEntity);

            ContractPriceEntity contractPriceEntity2 = new ContractPriceEntity();
            contractPriceEntity2 = contractPriceDao.getContractPriceEntityContractId(srcContractId);
            srcContractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity2));

            contractSignTemplateDTO.setSourceContractDetailInfoDTO(srcContractDetailInfoDTO);
        }

        contractSignTemplateDTO.setContractDetailInfoDTO(contractDetailInfoDTO);
        contractSignTemplateDTO.setContractSignDTO(contractSignEntity);
        contractSignTemplateDTO.setContractId(contractDetailInfoDTO.getId());

        contractSignRobot.createPdf(contractSignTemplateDTO);


    }

    @Test
    public void testCreatePdf() {

        Integer logId = 1793;

        contractSignRobot.createPdf(logId);


    }

    @Test
    public void testCreateWhitePdf() {

        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();

        ContractSignEntity contractSignEntity = new ContractSignEntity();
        contractSignEntity.setGoodsCategoryId(11)
                .setSalesType(2)
                .setContractType(1)
                .setFrameProtocolType(0)
                .setSignType(0)
                .setTradeType(0)
                .setTtType(TTTypeEnum.NEW.getType());

        TemplateConditionDTO templateCondition = new TemplateConditionDTO();
        templateCondition.setContractType(1)
                .setSalesType(2)
                .setDeliveryType(1)
                .setPaymentType(1)
                .setDeliveryFactoryCode("ZJG")
                .setPriceEndType(1)
                .setDepositAmount(100)
                .setAddedDepositRate(5)
                .setNotPriceNum(BigDecimal.ZERO)
                .setActionType(1)
                .setSplitType(1)
                .setSpecId(11)
                .setTransferFactory(2)
                .setSignType(0);


        contractSignRobot.createPdf(signTemplateDTO, contractSignEntity);


    }

    @Test
    public void getPatten() {
        String sp = "============================\r\n";

        String start = "<#if";

        String content = "";

        List<Integer> typeList = new ArrayList<>();
        typeList.add(3);

        List<TemplateEntity> templateList = templateDao.findAllTemplate(typeList, null);

        StringBuilder sbLog = new StringBuilder();

        for (TemplateEntity templateEntity : templateList) {

            content = contractSignRobot.assembleTemplateInfo(templateEntity.getId());

            int s = content.indexOf(start);

            if (s >= 0) {
                sbLog.append("\r\n").append(templateEntity.getId()).append("：").append(templateEntity.getCode()).append("\r\n").append(sp);

                buildSbLog(sbLog, templateEntity.getCode(), content);

            }
        }

        System.out.println(sbLog.toString());

    }

    private void buildSbLog(StringBuilder sbLog, String code, String content) {
        boolean loop = true;
        String start1 = "<#if";
        String start2 = "<#elseif";
        String end = ">";

        int s = 0;
        while (loop) {

            int s1 = content.indexOf(start1, s);
            int s2 = content.indexOf(start2, s);
            if (s1 < 0 && s2 < 0) {
                loop = false;
                continue;
            }

            if (s1 >= 0) {
                if (s2 > s1 || s2 < 0) {
                    s = s1;
                } else {
                    s = s2;
                }
            } else {
                if (s2 >= 0) {
                    s = s2;
                } else {
                    loop = false;
                    continue;
                }
            }

            int e = content.indexOf(end, s);

            String c = "";

            c = content.substring(s, e + 1);

            s = e + 1;

            sbLog.append(c).append("\r\n");


        }
    }
}
