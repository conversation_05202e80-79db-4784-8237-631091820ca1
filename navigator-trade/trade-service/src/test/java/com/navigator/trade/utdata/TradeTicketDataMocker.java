package com.navigator.trade.utdata;

import cn.hutool.core.util.RandomUtil;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;

import java.math.BigDecimal;
import java.util.Date;


public class TradeTicketDataMocker {

    public static TradeTicketEntity genTradeTicketEntity(ContractActionEnum actionEnum) {
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();

        ContractTypeEnum contractTypeEnum = ContractTypeEnum.JI_CHA;
        if (ContractTypeEnum.STRUCTURE.equals(actionEnum)) {
            contractTypeEnum = ContractTypeEnum.STRUCTURE;
        }

        tradeTicketEntity.setContractCode("HT_NEO_" + RandomUtil.randomNumbers(8))
                .setContractId(0)
                .setCode("TT_NEO_" + RandomUtil.randomNumbers(8))
                .setType(actionEnum.getActionValue())
                .setContractType(contractTypeEnum.getValue())
                .setStatus(1)
                .setApprovalStatus(0)
                .setApprovalType(ContractApproveRuleEnum.NONE.getValue())
                .setContractStatus(1)
                .setContractSignatureStatus(1)
                .setOperationSource(0)
                .setContractSource(actionEnum.getActionValue())
                .setTradeType(actionEnum.getActionValue())
                .setOwnerId(999)
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setInvalidReason("")
                .setIsDeleted(0)
                .setCreatedBy(999)
                .setUpdatedBy(999)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setProtocolCode("")
                .setSignId(0)
                .setGoodsCategoryId(GoodsCategoryEnum.OSM.getValue())
                .setSubGoodsCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue());
        return tradeTicketEntity;
    }

    public static TTAddEntity genTTAddEntity() {
        TTAddEntity ttAddEntity = new TTAddEntity();

        TradeTicketEntity tradeTicketEntity = genTradeTicketEntity(ContractActionEnum.NEW);
        CustomerEntity supplier = CustomerDataMocker.genSupplier();
        GoodsEntity goodsEntity = GoodsDataMocker.genGoodsEntity();
        CustomerEntity customer = CustomerDataMocker.genCustomer();

        ttAddEntity.setId(300000001)
                .setContractCode("HT" + RandomUtil.randomNumbers(8))
                .setContractId(801)
                .setRootContractId(0)
                .setStatus(1)
                .setCustomerCode(customer.getCode())
                .setCustomerId(customer.getId())
                .setContractType(tradeTicketEntity.getContractType())
                .setCustomerName(customer.getName())
                .setSupplierId(supplier.getId())
                .setSupplierName(supplier.getName())
                .setSupplierAccount(RandomUtil.randomNumbers(12))
                .setSignPlace(supplier.getSignPlace())
                .setDomainCode("M2503")
                .setGoodsId(goodsEntity.getId())
                .setGoodsName(goodsEntity.getName())
                .setUnit(UnitEnum.TON.name())
                .setGoodsCategoryId(goodsEntity.getCategoryId())
                .setGoodsPackageId(goodsEntity.getPackageId())
                .setGoodsSpecId(goodsEntity.getSpecId())
                .setNeedPackageWeight(0)
                .setPackageWeight("")
                .setQualityCheck("以LDC为准")
                .setCurrencyType(CurrencyTypeEnum.CNY.getDesc())
                .setUnitPrice(BigDecimal.valueOf(3600))
                .setFobUnitPrice(BigDecimal.valueOf(3510))
                .setTaxRate(BigDecimal.valueOf(0.07))
                .setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue())
                .setCifUnitPrice(BigDecimal.valueOf(3520))
                .setContractNum(BigDecimal.valueOf(1500))
                .setTemporaryPrice(BigDecimal.valueOf(3530))
                .setTransactionPrice(BigDecimal.valueOf(3540))
                .setTotalAmount(BigDecimal.valueOf(4560000))
                .setExtraPrice(BigDecimal.valueOf(200))
                .setCreditDays(0)
                .setPaymentType(PaymentTypeEnum.IMPREST.getType())
                .setDepositAmount(BigDecimal.valueOf(456000))
                .setAddedDepositAmount(BigDecimal.ZERO)
                .setDepositRate(10)
                .setDelayPayFine(new BigDecimal("2"))
                .setOem(0)
                .setDeliveryStartTime(DateTimeUtil.addDays(30))
                .setDeliveryEndTime(DateTimeUtil.addDays(120))
                .setDeliveryType(DeliveryTypeEnum.WAREHOUSE_DELIVERY_TAKE.getValue())
                .setDeliveryFactoryCode("ZJG")
                .setDeliveryFactoryName("张家港达孚")
                .setDestination(customer.getAddress())
                .setShipWarehouseId(0)
                .setIsArrangeTransport(0)
                .setWeightCheck("")
                .setWeightTolerance(5)
                .setPriceStartTime(DateTimeUtil.addDays(10))
                .setPriceStartTime(DateTimeUtil.addDays(20))
                .setMemo("NEO UT DATA")
                .setSignDate(new Date())
                .setIsStf(0)
                .setPriceEndType(1)
                .setAddedDepositRate(5)
                .setEnterprise(0)
                .setCompletedStatus(1)
                .setTtId(tradeTicketEntity.getId())
        ;

        return ttAddEntity;
    }
}
