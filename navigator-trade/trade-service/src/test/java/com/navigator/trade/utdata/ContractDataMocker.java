package com.navigator.trade.utdata;

import cn.hutool.core.util.RandomUtil;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.UUID;

public class ContractDataMocker {

    public static ContractEntity genContract4Structure() {
        Timestamp t = new Timestamp(System.currentTimeMillis());
        String ts = DateTimeUtil.formatDateTimeString(t);

        CustomerEntity customerEntity = CustomerDataMocker.genCustomer();
        CustomerEntity supplierEntity = CustomerDataMocker.genSupplier();


        ContractEntity contractEntity = new ContractEntity();
        contractEntity.setId(1)
                .setUuid(UUID.randomUUID().toString())
                .setLinkinageCode("LHT" + RandomUtil.randomNumbers(8))
                .setContractCode("HT" + ts)
                .setContractType(ContractTypeEnum.STRUCTURE.getValue())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setRootId(0)
                .setParentId(0)
                .setContractSource(ContractActionEnum.NEW.getActionValue())
                .setCustomerId(customerEntity.getId())
                .setCustomerName(customerEntity.getName())
                .setCustomerCode(customerEntity.getMdmCustomerCode())
                .setOriginalCustomerId(0)
                .setSupplierId(supplierEntity.getId())
                .setSupplierName(supplierEntity.getName())
                .setSupplierAccount(RandomUtil.randomNumbers(12))
                .setStatus(2)
                .setCustomerStatus(2)
                .setNeedOriginalPaper(1)
                .setOriginalPaperStatus(1)
                .setSignatureType("易企签")
                .setSignPlace(supplierEntity.getSignPlace())
                .setTradeType(1)
                .setDomainCode("2205")
                .setGoodsCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue())
//                .setGoodsId()
//                .setGoodsName()
                .setWeightUnit(UnitEnum.TON.name())
//                .setGoodsPackageId()
//                .setGoodsSpecId()
//                .setNeedPackageWeight()
//                .setPackageWeight()
//                .setQualityCheck()
                .setCurrencyType(CurrencyTypeEnum.CNY.getDesc())
                .setUnitPrice(BigDecimal.valueOf(3300))
                .setBaseDiffPrice(BigDecimal.valueOf(200))
                .setFobUnitPrice(BigDecimal.valueOf(350))
                .setTaxRate(BigDecimal.valueOf(0.07))
                .setCifUnitPrice(BigDecimal.valueOf(3175))
                .setOrderNum(BigDecimal.valueOf(1000))
                .setContractNum(BigDecimal.valueOf(1000))
                .setTotalTransferTimes(1)
                .setAbleTransferTimes(1)
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setTotalPriceNum(BigDecimal.ZERO)
                .setTotalTransferNum(BigDecimal.ZERO)
                .setTotalModifyNum(BigDecimal.ZERO)
                .setTemporaryPrice(BigDecimal.valueOf(3300))
                .setTransactionPrice(BigDecimal.valueOf(3280))
                .setTotalAmount(BigDecimal.valueOf(3300000))
                .setCreditDays(0)
                .setPaymentType(PaymentTypeEnum.IMPREST.getType())
                .setDepositAmount(BigDecimal.valueOf(330000))
                .setAddedDeposit(BigDecimal.ZERO)
                .setDepositRate(10)
                .setDepositReleaseType(1)
                .setDelayPayFine(new BigDecimal("2"))
                .setOem(0)
                .setDeliveryStartTime(DateTimeUtil.addDays(30))
                .setDeliveryEndTime(DateTimeUtil.addDays(120))
                .setDeliveryType(DeliveryTypeEnum.WAREHOUSE_DELIVERY_TAKE.getValue())
                .setDeliveryFactory("张家港达孚")
                .setDestination(customerEntity.getAddress())
                .setShipWarehouseId(202)
                .setIsArrangeTransport(0)
//                .setWeightCheck()
//                .setWeightTolerance()
                .setPriceStartTime(new Date())
                .setPriceEndTime("")
                .setOwnerId(301)
                .setRejectReason("")
                .setInvalidReason("")
                .setMemo("neo测试数据")
                .setExtraPrice(BigDecimal.valueOf(200))
                .setIsStf(0)
                .setCustomerContractCode("")
                .setLdcNeedOriginalPaper(1)
                .setSignDate(new Date())
                .setPriceEndType(1)
                .setLdcFrame(1)
                .setDeliveryFactoryCode("ZJG")
                .setDeliveryFactoryName("张家港达孚")
                .setVersion(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCreatedBy(301)
                .setUpdatedBy(0)
                .setAddedDepositRate(5);
        return contractEntity;
    }

    public static ContractStructureEntity genContractStructure(ContractEntity contractEntity) {
        ContractStructureEntity contractStructureEntity = new ContractStructureEntity();
        contractStructureEntity.setId(200001)
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setTtId(300000001)
                .setTtCode("TT" + DateTimeUtil.formatDateTimeString(new Timestamp(System.currentTimeMillis())))
                .setStructureType(3)
                .setDomainCode(contractEntity.getDomainCode())
                .setTotalNum(contractEntity.getContractNum())
                //.setMaxPrice(BigDecimal.valueOf(3100))
                //.setMinPrice(BigDecimal.valueOf(3000))
                .setUnitNum(BigDecimal.valueOf(50))
                .setStartTime(DateTimeUtil.parseDateString("2022-03-04"))
                .setEndTime(DateTimeUtil.parseDateString("2022-03-13"))
                .setTotalDay(10)
                //.setPriceRule("")
                .setDealNum(BigDecimal.ZERO)
                .setPriceStatus(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCreatedBy(301)
                .setUpdatedBy(0);
        return contractStructureEntity;
    }
}
