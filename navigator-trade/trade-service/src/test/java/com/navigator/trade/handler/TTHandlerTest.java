package com.navigator.trade.handler;

import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class TTHandlerTest {

    @Autowired
    TTHandler TTHandler;

    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void getStrategy() {

        boolean result = true;

        ITradeTicketService tradeTicketService = null;
        try {
            tradeTicketService = TTHandler.getStrategy("SBM_S_TT_ADD");
            if (!tradeTicketService.getClass().getName().contains("SBMSalesTTAddServiceImpl")) {
                result &= false;
            }
            tradeTicketService = TTHandler.getStrategy("TEST");
            if (null != tradeTicketService) {
                result &= false;
            }

        } catch (Exception e) {
            result = false;
        }

        Assert.assertTrue(result);
    }


    @Test
    public void get() {

      //  ProcessorTypeEnum processorTypeEnum = TTHandler.get(TTTypeEnum.CREATE);

        //Assert.assertFalse(null == processorTypeEnum);

    }
}