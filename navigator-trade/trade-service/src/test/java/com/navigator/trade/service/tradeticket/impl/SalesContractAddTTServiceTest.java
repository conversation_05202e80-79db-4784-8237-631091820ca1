package com.navigator.trade.service.tradeticket.impl;

import com.navigator.admin.facade.DepositRuleFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.SupplierFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.utdata.TTUTDataGenerator;
import org.junit.After;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
@Transactional
@Rollback
class SalesContractAddTTServiceTest {

    @Autowired
    @Qualifier("SBM_S_TT_ADD,SBO_S_TT_ADD")
    //@MockBean
    ITradeTicketService tradeTicketService;

    @MockBean
    SupplierFacade supplierFacade;
    @MockBean
    CustomerFacade customerFacade;
    @MockBean
    CustomerDetailFacade customerDetailFacade;
    @MockBean
    SystemRuleFacade systemRuleFacade;
    @MockBean
    DepositRuleFacade depositRuleFacade;
    @MockBean
    GoodsFacade goodsFacade;
    @MockBean
    OperationLogFacade operationLogFacade;


    @BeforeEach
    void setUp() {
        SupplierDTO supplier = TTUTDataGenerator.genSupplierDTO();
        Mockito.when(supplierFacade.getSupplierById(Mockito.anyInt())).thenReturn(supplier);

        CustomerDTO customerDTO = TTUTDataGenerator.genCustomerDTO();
        CustomerDetailEntity customerDetailEntity = TTUTDataGenerator.genCustomerDetailEntity();
        Mockito.when(customerFacade.getCustomerById(Mockito.any())).thenReturn(customerDTO);
        Mockito.when(customerDetailFacade.queryCustomerDetailList(Mockito.any(), Mockito.any())).thenReturn(customerDetailEntity);

        GoodsInfoVO goodsInfoVO=TTUTDataGenerator.genGoodsInfoVO();
        Mockito.when(goodsFacade.acquireGoodsInfo(Mockito.any())).thenReturn(goodsInfoVO);

        Mockito.when(systemRuleFacade.getSystemRule(Mockito.any())).thenReturn(null);

        //Mockito.when(depositRuleFacade.calcContractUseDeposit(Mockito.any())).thenReturn(BigDecimal.valueOf(Mockito.anyDouble()));



    }

    @After
    void tearDown() {

    }

    @Test
    void saveTT() {


        TTDTO ttdto = TTUTDataGenerator.genTTAddData();

        tradeTicketService.saveTT(ttdto);

    }

    @Test
    void startApprove(){
        tradeTicketService.startTTApprove(7195,null,null);
    }

}