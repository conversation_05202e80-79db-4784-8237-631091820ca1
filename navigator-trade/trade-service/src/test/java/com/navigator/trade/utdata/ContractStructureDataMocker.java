package com.navigator.trade.utdata;

import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.TTStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;

import java.math.BigDecimal;
import java.util.Date;

public class ContractStructureDataMocker {

    public static TTStructureEntity genTTStructureEntity() {

        TTStructureEntity ttStructureEntity = new TTStructureEntity();

        TradeTicketEntity tradeTicketEntity = TradeTicketDataMocker.genTradeTicketEntity(ContractActionEnum.STRUCTURE_PRICING);
        tradeTicketEntity.setContractCode("");
        ttStructureEntity.setId(5566)
                .setContractCode("")
                .setContractId(0)
                //.setPriceApplyId(0)
                .setStructureType(1)
                .setTotalNum(BigDecimal.valueOf(1500))
                //.setMaxPrice(BigDecimal.valueOf(3100))
                //.setMinPrice(BigDecimal.valueOf(3000))
                .setTotalDay(10)
                .setUnitNum(BigDecimal.valueOf(50))
                .setStartTime(new Date())
                .setEndTime(DateTimeUtil.addDays(10))
                .setDomainCode("M2503")
                //.setPriceRule("规则说明")
                .setCustomerName("Customer")
                .setGoodsCategoryId(tradeTicketEntity.getSubGoodsCategoryId())
                .setTtId(ttStructureEntity.getTtId())
        ;
        return ttStructureEntity;
    }


    public static TTDTO genContractStructureTTDTO() {
        TTDTO ttdto = new TTDTO();

        TradeTicketEntity tradeTicketEntity = TradeTicketDataMocker.genTradeTicketEntity(ContractActionEnum.STRUCTURE_PRICING);
        TTStructureEntity ttStructureEntity = genTTStructureEntity();

        SalesStructurePriceTTDTO salesStructurePriceTTDTO = new SalesStructurePriceTTDTO();
        salesStructurePriceTTDTO = BeanConvertUtils.convert(SalesStructurePriceTTDTO.class, ttStructureEntity);

        salesStructurePriceTTDTO.setTradeType(tradeTicketEntity.getTradeType());
        salesStructurePriceTTDTO.setSalesType(tradeTicketEntity.getSalesType());
        salesStructurePriceTTDTO.setContractSource(tradeTicketEntity.getContractSource());
        salesStructurePriceTTDTO.setContractSignatureStatus(tradeTicketEntity.getContractSignatureStatus());
        salesStructurePriceTTDTO.setStatus(tradeTicketEntity.getStatus());
        salesStructurePriceTTDTO.setApprovalStatus(tradeTicketEntity.getApprovalStatus());
        salesStructurePriceTTDTO.setApprovalType(tradeTicketEntity.getApprovalType());
        salesStructurePriceTTDTO.setCode(tradeTicketEntity.getCode());

        CustomerEntity customer = CustomerDataMocker.genCustomer();
        CustomerEntity supplier = CustomerDataMocker.genSupplier();
        salesStructurePriceTTDTO.setCustomerId(customer.getId());
        salesStructurePriceTTDTO.setSupplierId(supplier.getId());

        salesStructurePriceTTDTO.setOwnerId(JwtUtils.getCurrentUserId());

        ttdto.setProcessorType(ProcessorTypeEnum.SBM_S_STRUCTURE_PRICE.getTtValue());
        ttdto.setSalesStructurePriceTTDTO(salesStructurePriceTTDTO);

        return ttdto;
    }
}
