package com.navigator.trade.utdata;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataCheckMocker {

    public static Map<String, Map> initTableFiledsMap() {
        Map<String, String> mapTT = new HashMap<>();
        Map<String, String> mapTT_ADD = new HashMap<>();
        Map<String, String> mapTT_Modify = new HashMap<>();
        Map<String, String> mapTT_Price = new HashMap<>();
        Map<String, String> mapTT_Structure = new HashMap<>();
        Map<String, String> mapTT_Transfer = new HashMap<>();
        Map<String, String> mapContract_Price = new HashMap<>();
        Map<String, String> mapContract_Main = new HashMap<>();

        mapTT.put("id", "TT");
        mapTT.put("contractCode", "TT");
        mapTT.put("contractId", "TT");
        mapTT.put("code", "TT");
        mapTT.put("type", "TT");
        mapTT.put("contractType", "TT");
        mapTT.put("status", "TT");
        mapTT.put("approvalStatus", "TT");
        mapTT.put("approvalType", "TT");
        mapTT.put("contractStatus", "TT");
        mapTT.put("contractSignatureStatus", "TT");
        mapTT.put("operationSource", "TT");
        mapTT.put("contractSource", "TT");
        mapTT.put("tradeType", "TT");
        mapTT.put("ownerId", "TT");
        mapTT.put("salesType", "TT");
        mapTT.put("invalidReason", "TT");
        mapTT.put("isDeleted", "TT");
        mapTT.put("createdBy", "TT");
        mapTT.put("updatedBy", "TT");
        mapTT.put("createdAt", "TT");
        mapTT.put("updatedAt", "TT");
        mapTT.put("protocolCode", "TT");
        mapTT.put("signId", "TT");
        mapTT.put("goodsCategoryId", "TT");
        mapTT.put("subGoodsCategoryId", "TT");
        mapTT.put("customerId", "TT");
        mapTT.put("customerCode", "TT");
        mapTT.put("customerName", "TT");
        mapTT.put("customerType", "TT");
        mapTT.put("supplierId", "TT");
        mapTT.put("supplierName", "TT");
        mapTT.put("supplierType", "TT");
        mapTT.put("bankId", "TT");
        mapTT.put("supplierCode", "TT");
        mapTT.put("domainCode", "TT");
        mapTT.put("belongCustomerId", "TT");
        mapTT.put("groupId", "TT");
        mapTT.put("sourceContractId", "TT");
        mapTT.put("beforeContractNum", "TT");
        mapTT.put("changeContractNum", "TT");
        mapTT.put("afterContractNum", "TT");
        mapTT.put("payConditionId", "TT");
        mapTT.put("occupyStatus", "TT");
        mapTT.put("companyId", "TT");
        mapTT.put("companyName", "TT");
        mapTT.put("usage", "TT");
        mapTT.put("cancelReason", "TT");
        mapTT.put("sourceType", "TT");
        mapTT.put("confirmPriceInfo", "TT");
        mapTT.put("buCode", "TT");
        mapTT.put("category1", "TT");
        mapTT.put("category2", "TT");
        mapTT.put("category3", "TT");
        mapTT.put("goodsId", "TT");
        mapTT.put("goodsName", "TT");
        mapTT.put("contractNature", "TT");
        mapTT.put("siteId", "TT");
        mapTT.put("siteCode", "TT");
        mapTT.put("siteName", "TT");
        mapTT.put("commodityName", "TT");
        mapTT.put("isSoybean2", "TT");
        mapTT.put("futureCode", "TT");
        mapTT_ADD.put("id", "TT_ADD");
        mapTT_ADD.put("ttId", "TT_ADD");
        mapTT_ADD.put("contractCode", "TT_ADD");
        mapTT_ADD.put("contractId", "TT_ADD");
        mapTT_ADD.put("rootContractId", "TT_ADD");
        mapTT_ADD.put("status", "TT_ADD");
        mapTT_ADD.put("customerCode", "TT_ADD");
        mapTT_ADD.put("customerId", "TT_ADD");
        mapTT_ADD.put("contractType", "TT_ADD");
        mapTT_ADD.put("customerName", "TT_ADD");
        mapTT_ADD.put("supplierId", "TT_ADD");
        mapTT_ADD.put("supplierName", "TT_ADD");
        mapTT_ADD.put("supplierAccount", "TT_ADD");
        mapTT_ADD.put("signPlace", "TT_ADD");
        mapTT_ADD.put("domainCode", "TT_ADD");
        mapTT_ADD.put("goodsId", "TT_ADD");
        mapTT_ADD.put("goodsName", "TT_ADD");
        mapTT_ADD.put("unit", "TT_ADD");
        mapTT_ADD.put("goodsCategoryId", "TT_ADD");
        mapTT_ADD.put("goodsPackageId", "TT_ADD");
        mapTT_ADD.put("goodsSpecId", "TT_ADD");
        mapTT_ADD.put("needPackageWeight", "TT_ADD");
        mapTT_ADD.put("packageWeight", "TT_ADD");
        mapTT_ADD.put("qualityCheck", "TT_ADD");
        mapTT_ADD.put("currencyType", "TT_ADD");
        mapTT_ADD.put("unitPrice", "TT_ADD");
        mapTT_ADD.put("fobUnitPrice", "TT_ADD");
        mapTT_ADD.put("taxRate", "TT_ADD");
        mapTT_ADD.put("invoiceType", "TT_ADD");
        mapTT_ADD.put("cifUnitPrice", "TT_ADD");
        mapTT_ADD.put("contractNum", "TT_ADD");
        mapTT_ADD.put("temporaryPrice", "TT_ADD");
        mapTT_ADD.put("transactionPrice", "TT_ADD");
        mapTT_ADD.put("totalAmount", "TT_ADD");
        mapTT_ADD.put("extraPrice", "TT_ADD");
        mapTT_ADD.put("creditDays", "TT_ADD");
        mapTT_ADD.put("paymentType", "TT_ADD");
        mapTT_ADD.put("depositAmount", "TT_ADD");
        mapTT_ADD.put("addedDepositAmount", "TT_ADD");
        mapTT_ADD.put("depositRate", "TT_ADD");
        mapTT_ADD.put("depositUseRule", "TT_ADD");
        mapTT_ADD.put("delayPayFine", "TT_ADD");
        mapTT_ADD.put("oem", "TT_ADD");
        mapTT_ADD.put("deliveryStartTime", "TT_ADD");
        mapTT_ADD.put("deliveryEndTime", "TT_ADD");
        mapTT_ADD.put("deliveryType", "TT_ADD");
        mapTT_ADD.put("deliveryFactoryCode", "TT_ADD");
        mapTT_ADD.put("deliveryFactoryName", "TT_ADD");
        mapTT_ADD.put("destination", "TT_ADD");
        mapTT_ADD.put("shipWarehouseId", "TT_ADD");
        mapTT_ADD.put("isArrangeTransport", "TT_ADD");
        mapTT_ADD.put("weightCheck", "TT_ADD");
        mapTT_ADD.put("weightTolerance", "TT_ADD");
        mapTT_ADD.put("priceStartTime", "TT_ADD");
        mapTT_ADD.put("priceEndTime", "TT_ADD");
        mapTT_ADD.put("priceEndType", "TT_ADD");
        mapTT_ADD.put("memo", "TT_ADD");
        mapTT_ADD.put("isStf", "TT_ADD");
        mapTT_ADD.put("signDate", "TT_ADD");
        mapTT_ADD.put("addedDepositRate", "TT_ADD");
        mapTT_ADD.put("enterprise", "TT_ADD");
        mapTT_ADD.put("completedStatus", "TT_ADD");
        mapTT_ADD.put("createdBy", "TT_ADD");
        mapTT_ADD.put("updatedBy", "TT_ADD");
        mapTT_ADD.put("createdAt", "TT_ADD");
        mapTT_ADD.put("updatedAt", "TT_ADD");
        mapTT_ADD.put("bankId", "TT_ADD");
        mapTT_ADD.put("washoutUnitPrice", "TT_ADD");
        mapTT_ADD.put("sourceContractNum", "TT_ADD");
        mapTT_ADD.put("washoutPriceDetail", "TT_ADD");
        mapTT_ADD.put("content", "TT_ADD");
        mapTT_ADD.put("forwardPrice", "TT_ADD");
        mapTT_ADD.put("invoicePaymentRate", "TT_ADD");
        mapTT_ADD.put("addedDepositRate2", "TT_ADD");
        mapTT_ADD.put("residualRiskLimit", "TT_ADD");
        mapTT_ADD.put("residualRiskUsage", "TT_ADD");
        mapTT_ADD.put("residualRiskResidue", "TT_ADD");
        mapTT_ADD.put("residualRiskTradeStatus", "TT_ADD");
        mapTT_ADD.put("destinationValue", "TT_ADD");
        mapTT_ADD.put("packageWeightValue", "TT_ADD");
        mapTT_ADD.put("deliveryTypeValue", "TT_ADD");
        mapTT_ADD.put("weightCheckValue", "TT_ADD");
        mapTT_ADD.put("invoiceTypeValue", "TT_ADD");
        mapTT_ADD.put("shipWarehouseValue", "TT_ADD");
        mapTT_ADD.put("warrantTradeType", "TT_ADD");
        mapTT_ADD.put("settleType", "TT_ADD");
        mapTT_ADD.put("writeOffStartTime", "TT_ADD");
        mapTT_ADD.put("writeOffEndTime", "TT_ADD");
        mapTT_ADD.put("warrantId", "TT_ADD");
        mapTT_ADD.put("warrantCode", "TT_ADD");
        mapTT_ADD.put("futureCode", "TT_ADD");
        mapTT_ADD.put("standardType", "TT_ADD");
        mapTT_ADD.put("standardFileId", "1");
        mapTT_ADD.put("standardRemark", "TT_ADD");
        mapTT_ADD.put("depositPaymentType", "TT_ADD");
        mapTT_ADD.put("deliveryMarginAmount", "TT_ADD");
        mapTT_ADD.put("category", "TT_ADD");
        mapTT_ADD.put("warrantCategory", "TT_ADD");
        mapTT_Modify.put("id", "TT_Modify");
        mapTT_Modify.put("ttId", "TT_Modify");
        mapTT_Modify.put("contractCode", "TT_Modify");
        mapTT_Modify.put("type", "TT_Modify");
        mapTT_Modify.put("contractId", "TT_Modify");
        mapTT_Modify.put("sourceContractId", "TT_Modify");
        mapTT_Modify.put("rootContractId", "TT_Modify");
        mapTT_Modify.put("contractType", "TT_Modify");
        mapTT_Modify.put("customerId", "TT_Modify");
        mapTT_Modify.put("customerName", "TT_Modify");
        mapTT_Modify.put("customerCode", "TT_Modify");
        mapTT_Modify.put("supplierId", "TT_Modify");
        mapTT_Modify.put("supplierName", "TT_Modify");
        mapTT_Modify.put("supplierAccount", "TT_Modify");
        mapTT_Modify.put("customerStatus", "TT_Modify");
        mapTT_Modify.put("needOriginalPaper", "TT_Modify");
        mapTT_Modify.put("originalPaperStatus", "TT_Modify");
        mapTT_Modify.put("signatureUrl", "TT_Modify");
        mapTT_Modify.put("signatureType", "TT_Modify");
        mapTT_Modify.put("signPlace", "TT_Modify");
        mapTT_Modify.put("signatureStatus", "TT_Modify");
        mapTT_Modify.put("tradeType", "TT_Modify");
        mapTT_Modify.put("domainCode", "TT_Modify");
        mapTT_Modify.put("goodsId", "TT_Modify");
        mapTT_Modify.put("goodsName", "TT_Modify");
        mapTT_Modify.put("weightUnit", "TT_Modify");
        mapTT_Modify.put("goodsCategoryId", "TT_Modify");
        mapTT_Modify.put("goodsPackageId", "TT_Modify");
        mapTT_Modify.put("goodsSpecId", "TT_Modify");
        mapTT_Modify.put("needPackageWeight", "TT_Modify");
        mapTT_Modify.put("packageWeight", "TT_Modify");
        mapTT_Modify.put("qualityCheck", "TT_Modify");
        mapTT_Modify.put("currencyType", "TT_Modify");
        mapTT_Modify.put("unitPrice", "TT_Modify");
        mapTT_Modify.put("baseDiffPrice", "TT_Modify");
        mapTT_Modify.put("fobUnitPrice", "TT_Modify");
        mapTT_Modify.put("taxRate", "TT_Modify");
        mapTT_Modify.put("cifUnitPrice", "TT_Modify");
        mapTT_Modify.put("contractNum", "TT_Modify");
        mapTT_Modify.put("temporaryPrice", "TT_Modify");
        mapTT_Modify.put("transactionPrice", "TT_Modify");
        mapTT_Modify.put("totalAmount", "TT_Modify");
        mapTT_Modify.put("totalDeliveryNum", "TT_Modify");
        mapTT_Modify.put("creditDays", "TT_Modify");
        mapTT_Modify.put("paymentType", "TT_Modify");
        mapTT_Modify.put("depositAmount", "TT_Modify");
        mapTT_Modify.put("addedDeposit", "TT_Modify");
        mapTT_Modify.put("depositRate", "TT_Modify");
        mapTT_Modify.put("depositReleaseType", "TT_Modify");
        mapTT_Modify.put("delayPayFine", "TT_Modify");
        mapTT_Modify.put("oem", "TT_Modify");
        mapTT_Modify.put("deliveryStartTime", "TT_Modify");
        mapTT_Modify.put("deliveryEndTime", "TT_Modify");
        mapTT_Modify.put("deliveryType", "TT_Modify");
        mapTT_Modify.put("deliveryFactory", "TT_Modify");
        mapTT_Modify.put("destination", "TT_Modify");
        mapTT_Modify.put("shipWarehouseId", "TT_Modify");
        mapTT_Modify.put("isArrangeTransport", "TT_Modify");
        mapTT_Modify.put("weightCheck", "TT_Modify");
        mapTT_Modify.put("weightTolerance", "TT_Modify");
        mapTT_Modify.put("priceStartTime", "TT_Modify");
        mapTT_Modify.put("priceEndTime", "TT_Modify");
        mapTT_Modify.put("ownerId", "TT_Modify");
        mapTT_Modify.put("rejectReason", "TT_Modify");
        mapTT_Modify.put("invalidReason", "TT_Modify");
        mapTT_Modify.put("memo", "TT_Modify");
        mapTT_Modify.put("isStf", "TT_Modify");
        mapTT_Modify.put("customerContractCode", "TT_Modify");
        mapTT_Modify.put("ldcNeedOriginalPaper", "TT_Modify");
        mapTT_Modify.put("signDate", "TT_Modify");
        mapTT_Modify.put("priceEndType", "TT_Modify");
        mapTT_Modify.put("ldcFrame", "TT_Modify");
        mapTT_Modify.put("deliveryFactoryCode", "TT_Modify");
        mapTT_Modify.put("deliveryFactoryName", "TT_Modify");
        mapTT_Modify.put("extraPrice", "TT_Modify");
        mapTT_Modify.put("forwardPrice", "TT_Modify");
        mapTT_Modify.put("factoryPrice", "TT_Modify");
        mapTT_Modify.put("proteinDiffPrice", "TT_Modify");
        mapTT_Modify.put("compensationPrice", "TT_Modify");
        mapTT_Modify.put("optionPrice", "TT_Modify");
        mapTT_Modify.put("transportPrice", "TT_Modify");
        mapTT_Modify.put("liftingPrice", "TT_Modify");
        mapTT_Modify.put("delayPrice", "TT_Modify");
        mapTT_Modify.put("temperaturePrice", "TT_Modify");
        mapTT_Modify.put("otherDeliveryPrice", "TT_Modify");
        mapTT_Modify.put("buyBackPrice", "TT_Modify");
        mapTT_Modify.put("complaintDiscountPrice", "TT_Modify");
        mapTT_Modify.put("transferFactoryPrice", "TT_Modify");
        mapTT_Modify.put("otherPrice", "TT_Modify");
        mapTT_Modify.put("businessPrice", "TT_Modify");
        mapTT_Modify.put("fee", "TT_Modify");
        mapTT_Modify.put("shippingFeePrice", "TT_Modify");
        mapTT_Modify.put("modifyContent", "TT_Modify");
        mapTT_Modify.put("createdBy", "TT_Modify");
        mapTT_Modify.put("updatedBy", "TT_Modify");
        mapTT_Modify.put("createdAt", "TT_Modify");
        mapTT_Modify.put("updatedAt", "TT_Modify");
        mapTT_Modify.put("addedDepositRate", "TT_Modify");
        mapTT_Modify.put("addedDepositAmount", "TT_Modify");
        mapTT_Modify.put("refineDiffPrice", "TT_Modify");
        mapTT_Modify.put("relationId", "TT_Modify");
        mapTT_Modify.put("newContractNum", "TT_Modify");
        mapTT_Modify.put("content", "TT_Modify");
        mapTT_Modify.put("invoiceType", "TT_Modify");
        mapTT_Modify.put("invoicePaymentRate", "TT_Modify");
        mapTT_Modify.put("addedDepositRate2", "TT_Modify");
        mapTT_Modify.put("destinationValue", "TT_Modify");
        mapTT_Modify.put("packageWeightValue", "TT_Modify");
        mapTT_Modify.put("deliveryTypeValue", "TT_Modify");
        mapTT_Modify.put("weightCheckValue", "TT_Modify");
        mapTT_Modify.put("invoiceTypeValue", "TT_Modify");
        mapTT_Modify.put("shipWarehouseValue", "TT_Modify");
        mapTT_Modify.put("priceDetailInfo", "TT_Modify");
        mapTT_Modify.put("isModifyAll", "TT_Modify");
        mapTT_Modify.put("deliveryId", "TT_Modify");
        mapTT_Modify.put("deliveryPassword", "TT_Modify");
        mapTT_Modify.put("writeOffDate", "TT_Modify");
        mapTT_Modify.put("writeOffDeliveryStartTime", "TT_Modify");
        mapTT_Modify.put("writeOffDeliveryEndTime", "TT_Modify");
        mapTT_Modify.put("futureCode", "TT_Modify");
        mapTT_Modify.put("warrantTradeType", "TT_Modify");
        mapTT_Modify.put("writeOffNum", "TT_Modify");
        mapTT_Modify.put("warrantId", "TT_Modify");
        mapTT_Modify.put("warrantCode", "TT_Modify");
        mapTT_Modify.put("category", "TT_Modify");
        mapTT_Price.put("id", "TT_Price");
        mapTT_Price.put("ttId", "TT_Price");
        mapTT_Price.put("contractCode", "TT_Price");
        mapTT_Price.put("contractId", "TT_Price");
        mapTT_Price.put("priceApplyId", "TT_Price");
        mapTT_Price.put("allocateId", "TT_Price");
        mapTT_Price.put("sourceContractId", "TT_Price");
        mapTT_Price.put("type", "TT_Price");
        mapTT_Price.put("num", "TT_Price");
        mapTT_Price.put("tempPrice", "TT_Price");
        mapTT_Price.put("diffPrice", "TT_Price");
        mapTT_Price.put("transactionPrice", "TT_Price");
        mapTT_Price.put("price", "TT_Price");
        mapTT_Price.put("priceTime", "TT_Price");
        mapTT_Price.put("memo", "TT_Price");
        mapTT_Price.put("createdBy", "TT_Price");
        mapTT_Price.put("updatedBy", "TT_Price");
        mapTT_Price.put("createdAt", "TT_Price");
        mapTT_Price.put("updatedAt", "TT_Price");
        mapTT_Price.put("customerId", "TT_Price");
        mapTT_Price.put("customerName", "TT_Price");
        mapTT_Price.put("customerCode", "TT_Price");
        mapTT_Price.put("supplierId", "TT_Price");
        mapTT_Price.put("supplierName", "TT_Price");
        mapTT_Price.put("goodsId", "TT_Price");
        mapTT_Price.put("goodsCategoryId", "TT_Price");
        mapTT_Price.put("sourceId", "TT_Price");
        mapTT_Price.put("remainPriceNum", "TT_Price");
        mapTT_Price.put("originalPriceNum", "TT_Price");
        mapTT_Price.put("avePrice", "TT_Price");
        mapTT_Price.put("endContractPrice", "TT_Price");
        mapTT_Price.put("endAllPrice", "TT_Price");
        mapTT_Price.put("totalPriceNum", "TT_Price");
        mapTT_Price.put("contractPriceDetail", "TT_Price");
        mapTT_Price.put("priceEndType", "TT_Price");
        mapTT_Price.put("priceEndTime", "TT_Price");
        mapTT_Price.put("unitPrice", "TT_Price");
        mapTT_Price.put("thisContractNum", "TT_Price");
        mapTT_Price.put("contraryStatus", "TT_Price");
        mapTT_Structure.put("id", "TT_Structure");
        mapTT_Structure.put("ttId", "TT_Structure");
        mapTT_Structure.put("contractCode", "TT_Structure");
        mapTT_Structure.put("contractId", "TT_Structure");
        mapTT_Structure.put("structureType", "TT_Structure");
        mapTT_Structure.put("totalNum", "TT_Structure");
        mapTT_Structure.put("totalDay", "TT_Structure");
        mapTT_Structure.put("unitNum", "TT_Structure");
        mapTT_Structure.put("domainCode", "TT_Structure");
        mapTT_Structure.put("memo", "TT_Structure");
        mapTT_Structure.put("startTime", "TT_Structure");
        mapTT_Structure.put("endTime", "TT_Structure");
        mapTT_Structure.put("createdBy", "TT_Structure");
        mapTT_Structure.put("updatedBy", "TT_Structure");
        mapTT_Structure.put("createdAt", "TT_Structure");
        mapTT_Structure.put("updatedAt", "TT_Structure");
        mapTT_Structure.put("signDate", "TT_Structure");
        mapTT_Structure.put("unitIncrement", "TT_Structure");
        mapTT_Structure.put("cashReturn", "TT_Structure");
        mapTT_Structure.put("cumulativePrice", "TT_Structure");
        mapTT_Structure.put("triggerPrice", "TT_Structure");
        mapTT_Structure.put("customerId", "TT_Structure");
        mapTT_Structure.put("customerName", "TT_Structure");
        mapTT_Structure.put("structureUnitNum", "TT_Structure");
        mapTT_Structure.put("priceApplyId", "TT_Structure");
        mapTT_Structure.put("priceRule", "TT_Structure");
        mapTT_Structure.put("deliveryFactoryId", "TT_Structure");
        mapTT_Structure.put("deliveryFactoryCode", "TT_Structure");
        mapTT_Structure.put("structureName", "TT_Structure");
        mapTT_Transfer.put("id", "TT_Transfer");
        mapTT_Transfer.put("ttId", "TT_Transfer");
        mapTT_Transfer.put("contractCode", "TT_Transfer");
        mapTT_Transfer.put("contractId", "TT_Transfer");
        mapTT_Transfer.put("sourceContractId", "TT_Transfer");
        mapTT_Transfer.put("contractType", "TT_Transfer");
        mapTT_Transfer.put("type", "TT_Transfer");
        mapTT_Transfer.put("num", "TT_Transfer");
        mapTT_Transfer.put("originalDomainCode", "TT_Transfer");
        mapTT_Transfer.put("domainCode", "TT_Transfer");
        mapTT_Transfer.put("tempPrice", "TT_Transfer");
        mapTT_Transfer.put("diffPrice", "TT_Transfer");
        mapTT_Transfer.put("transactionPrice", "TT_Transfer");
        mapTT_Transfer.put("price", "TT_Transfer");
        mapTT_Transfer.put("memo", "TT_Transfer");
        mapTT_Transfer.put("createdBy", "TT_Transfer");
        mapTT_Transfer.put("updatedBy", "TT_Transfer");
        mapTT_Transfer.put("createdAt", "TT_Transfer");
        mapTT_Transfer.put("updatedAt", "TT_Transfer");
        mapTT_Transfer.put("modifyContent", "TT_Transfer");
        mapTT_Transfer.put("goodsCategoryId", "TT_Transfer");
        mapTT_Transfer.put("customerId", "TT_Transfer");
        mapTT_Transfer.put("customerName", "TT_Transfer");
        mapTT_Transfer.put("customerCode", "TT_Transfer");
        mapTT_Transfer.put("supplierId", "TT_Transfer");
        mapTT_Transfer.put("supplierName", "TT_Transfer");
        mapTT_Transfer.put("goodsId", "TT_Transfer");
        mapTT_Transfer.put("content", "TT_Transfer");
        mapTT_Transfer.put("contraryStatus", "TT_Transfer");
        mapTT_Transfer.put("priceApplyId", "TT_Transfer");
        mapTT_Transfer.put("priceAllocateId", "TT_Transfer");
        mapTT_Transfer.put("thisFee", "TT_Transfer");
        mapContract_Price.put("id", "Contract_Price");
        mapContract_Price.put("ttId", "Contract_Price");
        mapContract_Price.put("ttCode", "Contract_Price");
        mapContract_Price.put("contractCode", "Contract_Price");
        mapContract_Price.put("contractId", "Contract_Price");
        mapContract_Price.put("extraPrice", "Contract_Price");
        mapContract_Price.put("forwardPrice", "Contract_Price");
        mapContract_Price.put("factoryPrice", "Contract_Price");
        mapContract_Price.put("proteinDiffPrice", "Contract_Price");
        mapContract_Price.put("compensationPrice", "Contract_Price");
        mapContract_Price.put("optionPrice", "Contract_Price");
        mapContract_Price.put("transportPrice", "Contract_Price");
        mapContract_Price.put("liftingPrice", "Contract_Price");
        mapContract_Price.put("delayPrice", "Contract_Price");
        mapContract_Price.put("temperaturePrice", "Contract_Price");
        mapContract_Price.put("otherDeliveryPrice", "Contract_Price");
        mapContract_Price.put("buyBackPrice", "Contract_Price");
        mapContract_Price.put("complaintDiscountPrice", "Contract_Price");
        mapContract_Price.put("transferFactoryPrice", "Contract_Price");
        mapContract_Price.put("otherPrice", "Contract_Price");
        mapContract_Price.put("businessPrice", "Contract_Price");
        mapContract_Price.put("fee", "Contract_Price");
        mapContract_Price.put("shippingFeePrice", "Contract_Price");
        mapContract_Price.put("isDeleted", "Contract_Price");
        mapContract_Price.put("createdAt", "Contract_Price");
        mapContract_Price.put("updatedAt", "Contract_Price");
        mapContract_Price.put("refineDiffPrice", "Contract_Price");
        mapContract_Price.put("previousRecord", "Contract_Price");
        mapContract_Price.put("surveyFees", "Contract_Price");
        mapContract_Price.put("refineFracDiffPrice", "Contract_Price");
        mapContract_Price.put("vePrice", "Contract_Price");
        mapContract_Price.put("veContent", "Contract_Price");
        mapContract_Main.put("id", "Contract_Main");
        mapContract_Main.put("uuid", "Contract_Main");
        mapContract_Main.put("linkinageCode", "Contract_Main");
        mapContract_Main.put("contractCode", "Contract_Main");
        mapContract_Main.put("contractType", "Contract_Main");
        mapContract_Main.put("salesType", "Contract_Main");
        mapContract_Main.put("rootId", "Contract_Main");
        mapContract_Main.put("parentId", "Contract_Main");
        mapContract_Main.put("contractSource", "Contract_Main");
        mapContract_Main.put("customerId", "Contract_Main");
        mapContract_Main.put("customerName", "Contract_Main");
        mapContract_Main.put("customerCode", "Contract_Main");
        mapContract_Main.put("originalCustomerId", "Contract_Main");
        mapContract_Main.put("supplierId", "Contract_Main");
        mapContract_Main.put("supplierName", "Contract_Main");
        mapContract_Main.put("supplierAccount", "Contract_Main");
        mapContract_Main.put("status", "Contract_Main");
        mapContract_Main.put("customerStatus", "Contract_Main");
        mapContract_Main.put("needOriginalPaper", "Contract_Main");
        mapContract_Main.put("originalPaperStatus", "Contract_Main");
        mapContract_Main.put("signatureType", "Contract_Main");
        mapContract_Main.put("signPlace", "Contract_Main");
        mapContract_Main.put("tradeType", "Contract_Main");
        mapContract_Main.put("domainCode", "Contract_Main");
        mapContract_Main.put("goodsId", "Contract_Main");
        mapContract_Main.put("goodsName", "Contract_Main");
        mapContract_Main.put("weightUnit", "Contract_Main");
        mapContract_Main.put("goodsCategoryId", "Contract_Main");
        mapContract_Main.put("goodsPackageId", "Contract_Main");
        mapContract_Main.put("goodsSpecId", "Contract_Main");
        mapContract_Main.put("needPackageWeight", "Contract_Main");
        mapContract_Main.put("packageWeight", "Contract_Main");
        mapContract_Main.put("qualityCheck", "Contract_Main");
        mapContract_Main.put("currencyType", "Contract_Main");
        mapContract_Main.put("unitPrice", "Contract_Main");
        mapContract_Main.put("baseDiffPrice", "Contract_Main");
        mapContract_Main.put("fobUnitPrice", "Contract_Main");
        mapContract_Main.put("taxRate", "Contract_Main");
        mapContract_Main.put("cifUnitPrice", "Contract_Main");
        mapContract_Main.put("orderNum", "Contract_Main");
        mapContract_Main.put("contractNum", "Contract_Main");
        mapContract_Main.put("totalTransferTimes", "Contract_Main");
        mapContract_Main.put("ableTransferTimes", "Contract_Main");
        mapContract_Main.put("totalDeliveryNum", "Contract_Main");
        mapContract_Main.put("totalPriceNum", "Contract_Main");
        mapContract_Main.put("totalTransferNum", "Contract_Main");
        mapContract_Main.put("totalModifyNum", "Contract_Main");
        mapContract_Main.put("temporaryPrice", "Contract_Main");
        mapContract_Main.put("transactionPrice", "Contract_Main");
        mapContract_Main.put("totalAmount", "Contract_Main");
        mapContract_Main.put("creditDays", "Contract_Main");
        mapContract_Main.put("paymentType", "Contract_Main");
        mapContract_Main.put("depositAmount", "Contract_Main");
        mapContract_Main.put("addedDeposit", "Contract_Main");
        mapContract_Main.put("depositRate", "Contract_Main");
        mapContract_Main.put("depositReleaseType", "Contract_Main");
        mapContract_Main.put("delayPayFine", "Contract_Main");
        mapContract_Main.put("oem", "Contract_Main");
        mapContract_Main.put("deliveryStartTime", "Contract_Main");
        mapContract_Main.put("deliveryEndTime", "Contract_Main");
        mapContract_Main.put("deliveryType", "Contract_Main");
        mapContract_Main.put("deliveryFactory", "Contract_Main");
        mapContract_Main.put("destination", "Contract_Main");
        mapContract_Main.put("shipWarehouseId", "Contract_Main");
        mapContract_Main.put("isArrangeTransport", "Contract_Main");
        mapContract_Main.put("weightCheck", "Contract_Main");
        mapContract_Main.put("weightTolerance", "Contract_Main");
        mapContract_Main.put("priceStartTime", "Contract_Main");
        mapContract_Main.put("priceEndTime", "Contract_Main");
        mapContract_Main.put("ownerId", "Contract_Main");
        mapContract_Main.put("rejectReason", "Contract_Main");
        mapContract_Main.put("invalidReason", "Contract_Main");
        mapContract_Main.put("memo", "Contract_Main");
        mapContract_Main.put("extraPrice", "Contract_Main");
        mapContract_Main.put("isStf", "Contract_Main");
        mapContract_Main.put("customerContractCode", "Contract_Main");
        mapContract_Main.put("ldcNeedOriginalPaper", "Contract_Main");
        mapContract_Main.put("signDate", "Contract_Main");
        mapContract_Main.put("priceEndType", "Contract_Main");
        mapContract_Main.put("ldcFrame", "Contract_Main");
        mapContract_Main.put("deliveryFactoryCode", "Contract_Main");
        mapContract_Main.put("deliveryFactoryName", "Contract_Main");
        mapContract_Main.put("version", "Contract_Main");
        mapContract_Main.put("isDeleted", "Contract_Main");
        mapContract_Main.put("createdBy", "Contract_Main");
        mapContract_Main.put("updatedBy", "Contract_Main");
        mapContract_Main.put("createdAt", "Contract_Main");
        mapContract_Main.put("updatedAt", "Contract_Main");
        mapContract_Main.put("addedDepositRate", "Contract_Main");
        mapContract_Main.put("supplierAccountId", "Contract_Main");
        mapContract_Main.put("totalReversePriceTimes", "Contract_Main");
        mapContract_Main.put("ableReversePriceTimes", "Contract_Main");
        mapContract_Main.put("tagConfigIds", "Contract_Main");
        mapContract_Main.put("isChangeFactory", "Contract_Main");
        mapContract_Main.put("belongCustomerId", "Contract_Main");
        mapContract_Main.put("invoiceType", "Contract_Main");
        mapContract_Main.put("mainVersion", "Contract_Main");
        mapContract_Main.put("totalBuyBackNum", "Contract_Main");
        mapContract_Main.put("orderAmount", "Contract_Main");
        mapContract_Main.put("createBatch", "Contract_Main");
        mapContract_Main.put("createSource", "Contract_Main");
        mapContract_Main.put("transferredTimes", "Contract_Main");
        mapContract_Main.put("reversedPriceTimes", "Contract_Main");
        mapContract_Main.put("isOverForward", "Contract_Main");
        mapContract_Main.put("payConditionId", "Contract_Main");
        mapContract_Main.put("repeatContractCode", "Contract_Main");
        mapContract_Main.put("companyId", "Contract_Main");
        mapContract_Main.put("companyName", "Contract_Main");
        mapContract_Main.put("invoicePaymentRate", "Contract_Main");
        mapContract_Main.put("originContractType", "Contract_Main");
        mapContract_Main.put("applyDeliveryNum", "Contract_Main");
        mapContract_Main.put("usage", "Contract_Main");
        mapContract_Main.put("addedDepositRate2", "Contract_Main");
        mapContract_Main.put("closeTailNum", "Contract_Main");
        mapContract_Main.put("destinationValue", "Contract_Main");
        mapContract_Main.put("packageWeightValue", "Contract_Main");
        mapContract_Main.put("deliveryTypeValue", "Contract_Main");
        mapContract_Main.put("weightCheckValue", "Contract_Main");
        mapContract_Main.put("invoiceTypeValue", "Contract_Main");
        mapContract_Main.put("shipWarehouseValue", "Contract_Main");
        mapContract_Main.put("buCode", "Contract_Main");
        mapContract_Main.put("category1", "Contract_Main");
        mapContract_Main.put("category2", "Contract_Main");
        mapContract_Main.put("category3", "Contract_Main");
        mapContract_Main.put("contractNature", "Contract_Main");
        mapContract_Main.put("warrantCode", "Contract_Main");
        mapContract_Main.put("warrantCancelCount", "Contract_Main");
        mapContract_Main.put("siteCode", "Contract_Main");
        mapContract_Main.put("contractCloseType", "Contract_Main");
        mapContract_Main.put("isSoybean2", "Contract_Main");
        mapContract_Main.put("writeOffStatus", "Contract_Main");
        mapContract_Main.put("warrantTradeType", "Contract_Main");
        mapContract_Main.put("writeOffStartTime", "Contract_Main");
        mapContract_Main.put("writeOffEndTime", "Contract_Main");
        mapContract_Main.put("warrantId", "Contract_Main");
        mapContract_Main.put("categoryCode", "Contract_Main");
        mapContract_Main.put("exchangeCode", "Contract_Main");
        mapContract_Main.put("futureCode", "Contract_Main");
        mapContract_Main.put("standardType", "1");
        mapContract_Main.put("standardFileId", "1");
        mapContract_Main.put("siteName", "Contract_Main");
        mapContract_Main.put("deliveryMode", "Contract_Main");
        mapContract_Main.put("commodityName", "Contract_Main");
        mapContract_Main.put("standardRemark", "Contract_Main");
        mapContract_Main.put("settleType", "Contract_Main");


        Map<String, Map> map = new HashMap<>();
        map.put("TT", mapTT);
        map.put("TT_ADD", mapTT_ADD);
        map.put("TT_Modify", mapTT_Modify);
        map.put("TT_Price", mapTT_Price);
        map.put("TT_Structure", mapTT_Structure);
        map.put("TT_Transfer", mapTT_Transfer);
        map.put("Contract_Price", mapContract_Price);
        map.put("Contract_Main", mapContract_Main);
        return map;

    }

    public static Map<String, Map> intDTOFiledsMap() {
        Map<String, String> mapOMContractAddTTDTO = new HashMap<>();
        Map<String, String> mapCommonTradeInfoDTO = new HashMap<>();
        Map<String, String> mapPriceDetailDTO = new HashMap<>();
        Map<String, String> mapPriceDetailBO = new HashMap<>();
        Map<String, String> mapSalesContractAddTTDTO = new HashMap<>();
        Map<String, String> mapTTCommonDTO = new HashMap<>();
        Map<String, String> mapSalesContractReviseTTDTO = new HashMap<>();
        Map<String, String> mapSalesContractSplitTTDTO = new HashMap<>();
        Map<String, String> mapSalesContractTTPriceDTO = new HashMap<>();
        Map<String, String> mapSalesContractTTTransferDTO = new HashMap<>();
        Map<String, String> mapSalesStructurePriceTTDTO = new HashMap<>();
        Map<String, String> mapTradeTicketEntity = new HashMap<>();
        Map<String, String> mapTTAddEntity = new HashMap<>();
        Map<String, String> mapTTModifyEntity = new HashMap<>();
        Map<String, String> mapTTPriceEntity = new HashMap<>();
        Map<String, String> mapTTStructureEntity = new HashMap<>();
        Map<String, String> mapTTTranferEntity = new HashMap<>();
        Map<String, String> mapOmc = new HashMap<>();
        Map<String, String> mapSca = new HashMap<>();
        Map<String, String> mapContractPriceBaseEntity = new HashMap<>();


        mapOMContractAddTTDTO.put("contractType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractNature", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("supplierId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("customerName", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("customerCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("customerId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("goodsCategoryId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("goodsPackageId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("goodsSpecId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("creditDays", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryFactoryCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("delayPayFine", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("signPlace", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryFactoryName", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("signDate", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("oem", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("depositUseRule", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractNum", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("ttKernelDTOList", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("destination", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("weightCheck", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("needPackageWeight", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("packageWeight", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("shipWarehouseId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("weightTolerance", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("ownerId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("ttId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("completedStatus", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("priceStartTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("createStatus", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("userId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("supplierAccountId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("tradeType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("salesType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractSource", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractSignatureStatus", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("status", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("approvalStatus", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("approvalType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("code", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("rootContractId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("usage", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("structureTotalNum", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("minPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("maxPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("structureType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("totalDay", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("unitNum", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("structurePriceStartTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("structurePriceEndTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("domainCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("submitType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("buCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("warrantTradeType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("siteCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("siteName", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("category1", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("category2", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("category3", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("goodsId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("goodsName", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("commodityName", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("paymentType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("settleType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("warrantCategory", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("depositPaymentType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryMarginAmount", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("writeOffStartTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("writeOffEndTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("standardType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("standardFileId", "1");
        mapOMContractAddTTDTO.put("standardRemark", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("isSoybean2", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("ttId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("code", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("unitPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("contractNum", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryStartTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("deliveryEndTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("futureCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("exchangeCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("domainCode", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("depositRate", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("depositAmount", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("addedDepositRate", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("payConditionId", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("invoicePaymentRate", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("priceEndType", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("priceEndTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("WriteOffStartTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("WriteOffEndTime", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("memo", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("extraPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("forwardPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("proteinDiffPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("compensationPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("optionPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("transportPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("liftingPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("delayPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("temperaturePrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("otherDeliveryPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("buyBackPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("complaintDiscountPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("transferFactoryPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("otherPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("businessPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("fee", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("shippingFeePrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("factoryPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("refineDiffPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("refineFracDiffPrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("surveyFees", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("vePrice", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("veContent", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("type", "OMContractAddTTDTO");
        mapOMContractAddTTDTO.put("isStf", "OMContractAddTTDTO");

        mapCommonTradeInfoDTO.put("ttId", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("code", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("unitPrice", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("contractNum", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("deliveryStartTime", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("deliveryEndTime", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("futureCode", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("exchangeCode", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("domainCode", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("depositRate", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("depositAmount", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("addedDepositRate", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("payConditionId", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("invoicePaymentRate", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("priceEndType", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("priceEndTime", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("WriteOffStartTime", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("WriteOffEndTime", "CommonTradeInfoDTO");
        mapCommonTradeInfoDTO.put("memo", "CommonTradeInfoDTO");
        mapPriceDetailDTO.put("extraPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("forwardPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("proteinDiffPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("compensationPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("optionPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("transportPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("liftingPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("delayPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("temperaturePrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("otherDeliveryPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("buyBackPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("complaintDiscountPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("transferFactoryPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("otherPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("businessPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("fee", "PriceDetailDTO");
        mapPriceDetailDTO.put("shippingFeePrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("factoryPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("refineDiffPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("refineFracDiffPrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("surveyFees", "PriceDetailDTO");
        mapPriceDetailDTO.put("vePrice", "PriceDetailDTO");
        mapPriceDetailDTO.put("veContent", "PriceDetailDTO");
        mapPriceDetailBO.put("extraPrice", "PriceDetailBO");
        mapPriceDetailBO.put("forwardPrice", "PriceDetailBO");
        mapPriceDetailBO.put("proteinDiffPrice", "PriceDetailBO");
        mapPriceDetailBO.put("compensationPrice", "PriceDetailBO");
        mapPriceDetailBO.put("optionPrice", "PriceDetailBO");
        mapPriceDetailBO.put("transportPrice", "PriceDetailBO");
        mapPriceDetailBO.put("liftingPrice", "PriceDetailBO");
        mapPriceDetailBO.put("delayPrice", "PriceDetailBO");
        mapPriceDetailBO.put("temperaturePrice", "PriceDetailBO");
        mapPriceDetailBO.put("otherDeliveryPrice", "PriceDetailBO");
        mapPriceDetailBO.put("buyBackPrice", "PriceDetailBO");
        mapPriceDetailBO.put("complaintDiscountPrice", "PriceDetailBO");
        mapPriceDetailBO.put("transferFactoryPrice", "PriceDetailBO");
        mapPriceDetailBO.put("otherPrice", "PriceDetailBO");
        mapPriceDetailBO.put("businessPrice", "PriceDetailBO");
        mapPriceDetailBO.put("fee", "PriceDetailBO");
        mapPriceDetailBO.put("shippingFeePrice", "PriceDetailBO");
        mapPriceDetailBO.put("refineDiffPrice", "PriceDetailBO");
        mapPriceDetailBO.put("refineFracDiffPrice", "PriceDetailBO");
        mapPriceDetailBO.put("surveyFees", "PriceDetailBO");
        mapPriceDetailBO.put("factoryPrice", "PriceDetailBO");
        mapPriceDetailBO.put("vePrice", "PriceDetailBO");
        mapPriceDetailBO.put("veContent", "PriceDetailBO");
        mapSalesContractAddTTDTO.put("contractType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("goodsPackageId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("goodsSpecId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("creditDays", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryFactoryCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("delayPayFine", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("signPlace", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryFactoryName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("signDate", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("oem", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("depositUseRule", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("destination", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("weightCheck", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("needPackageWeight", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("packageWeight", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("shipWarehouseId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("shipWarehouseCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("shipWarehouseName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("weightTolerance", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("ownerId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("memo", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("isStf", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("priceEndType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("priceEndTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("addedDepositRate", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("addedDepositRate2", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("ttId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("completedStatus", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("priceStartTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("createStatus", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("userId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("supplierAccountId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("unitPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractNum", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryStartTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryEndTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("depositRate", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("depositAmount", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("signType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("washoutUnitPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("washoutPriceDetailBO", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("addedSignatureType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("forwardPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("warrantTradeType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("paymentType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("settleType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("writeOffStartTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("writeOffEndTime", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("warrantId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("warrantCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("exchangeCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("categoryCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("futureCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("warrantCategory", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("depositPaymentType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("deliveryMarginAmount", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("standardType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("standardFileId", "1");
        mapSalesContractAddTTDTO.put("standardRemark", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("isSoybean2", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("tradeType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("salesType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractNature", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractSource", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("contractSignatureStatus", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("status", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("approvalStatus", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("approvalType", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("code", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("rootContractId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("sourceContractId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("supplierId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("supplierName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("customerName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("customerCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("customerId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("goodsCategoryId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("domainCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("futureCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("belongCustomerId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("payConditionId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("payConditionCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("companyId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("companyName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("invoicePaymentRate", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("usage", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("buCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("siteCode", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("siteName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("category1", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("category2", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("category3", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("goodsId", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("goodsName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("commodityName", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("extraPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("forwardPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("proteinDiffPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("compensationPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("optionPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("transportPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("liftingPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("delayPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("temperaturePrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("otherDeliveryPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("buyBackPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("complaintDiscountPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("transferFactoryPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("otherPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("businessPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("fee", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("shippingFeePrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("refineDiffPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("refineFracDiffPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("surveyFees", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("factoryPrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("vePrice", "SalesContractAddTTDTO");
        mapSalesContractAddTTDTO.put("veContent", "SalesContractAddTTDTO");


        mapTTCommonDTO.put("tradeType", "TTCommonDTO");
        mapTTCommonDTO.put("salesType", "TTCommonDTO");
        mapTTCommonDTO.put("contractNature", "TTCommonDTO");
        mapTTCommonDTO.put("contractSource", "TTCommonDTO");
        mapTTCommonDTO.put("contractSignatureStatus", "TTCommonDTO");
        mapTTCommonDTO.put("status", "TTCommonDTO");
        mapTTCommonDTO.put("approvalStatus", "TTCommonDTO");
        mapTTCommonDTO.put("approvalType", "TTCommonDTO");
        mapTTCommonDTO.put("code", "TTCommonDTO");
        mapTTCommonDTO.put("rootContractId", "TTCommonDTO");
        mapTTCommonDTO.put("sourceContractId", "TTCommonDTO");
        mapTTCommonDTO.put("supplierId", "TTCommonDTO");
        mapTTCommonDTO.put("supplierName", "TTCommonDTO");
        mapTTCommonDTO.put("customerName", "TTCommonDTO");
        mapTTCommonDTO.put("customerCode", "TTCommonDTO");
        mapTTCommonDTO.put("customerId", "TTCommonDTO");
        mapTTCommonDTO.put("goodsCategoryId", "TTCommonDTO");
        mapTTCommonDTO.put("domainCode", "TTCommonDTO");
        mapTTCommonDTO.put("futureCode", "TTCommonDTO");
        mapTTCommonDTO.put("belongCustomerId", "TTCommonDTO");
        mapTTCommonDTO.put("payConditionId", "TTCommonDTO");
        mapTTCommonDTO.put("payConditionCode", "TTCommonDTO");
        mapTTCommonDTO.put("companyId", "TTCommonDTO");
        mapTTCommonDTO.put("companyName", "TTCommonDTO");
        mapTTCommonDTO.put("invoicePaymentRate", "TTCommonDTO");
        mapTTCommonDTO.put("usage", "TTCommonDTO");
        mapTTCommonDTO.put("buCode", "TTCommonDTO");
        mapTTCommonDTO.put("siteCode", "TTCommonDTO");
        mapTTCommonDTO.put("siteName", "TTCommonDTO");
        mapTTCommonDTO.put("category1", "TTCommonDTO");
        mapTTCommonDTO.put("category2", "TTCommonDTO");
        mapTTCommonDTO.put("category3", "TTCommonDTO");
        mapTTCommonDTO.put("goodsId", "TTCommonDTO");
        mapTTCommonDTO.put("goodsName", "TTCommonDTO");
        mapTTCommonDTO.put("commodityName", "TTCommonDTO");
        mapSalesContractReviseTTDTO.put("ttId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractNum", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("deliveryStartTime", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("deliveryEndTime", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsPackageId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsSpecId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("unitPrice", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("depositRate", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("depositAmount", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("creditDays", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("paymentType//", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("deliveryType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("deliveryFactoryCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("destination", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("weightCheck", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("needPackageWeight", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("packageWeight", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("shipWarehouseId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("weightTolerance", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("oem", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("memo", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("priceEndType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("addedDeposit", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("addedDepositRate", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("addedDepositRate2", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("modifyContent", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("sonContractId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("userId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("ownerId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("ttType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("fobUnitPrice", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("taxRate", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("cifUnitPrice", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("totalAmount", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("priceEndTime", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("supplierAccountId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("delayPayFine", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("delayPayFineRule", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("depositReleaseType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("deliveryFactoryName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("belongCustomerId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("content", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("signDate", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("protocolCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("reviseType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("supplierAccount", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("addedSignatureType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("isStf", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("tradeType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("salesType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractNature", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractSource", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("contractSignatureStatus", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("status", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("approvalStatus", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("approvalType", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("code", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("rootContractId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("sourceContractId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("supplierId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("supplierName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("customerName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("customerCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("customerId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsCategoryId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("domainCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("futureCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("belongCustomerId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("payConditionId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("payConditionCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("companyId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("companyName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("invoicePaymentRate", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("usage", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("buCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("siteCode", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("siteName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("category1", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("category2", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("category3", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsId", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("goodsName", "SalesContractReviseTTDTO");
        mapSalesContractReviseTTDTO.put("commodityName", "SalesContractReviseTTDTO");
        mapSalesContractSplitTTDTO.put("ttId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("type", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("sonContractId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("rootContractId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("supplierAccount", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("customerStatus", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("needOriginalPaper", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("originalPaperStatus", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signatureUrl", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signatureType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signPlace", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signatureStatus", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("tradeType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("weightUnit", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsPackageId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsSpecId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("needPackageWeight", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("packageWeight", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("qualityCheck", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("currencyType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("unitPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("baseDiffPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("fobUnitPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("taxRate", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("cifUnitPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractNum", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("temporaryPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("transactionPrice", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("totalAmount", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("totalDeliveryNum", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("creditDays", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("paymentType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("depositAmount", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("addedDeposit", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("depositRate", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("depositReleaseType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("delayPayFine", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("oem", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryStartTime", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryEndTime", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryFactory", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("destination", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("shipWarehouseId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("isArrangeTransport", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("weightCheck", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("weightTolerance", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("priceStartTime", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("priceEndTime", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("ownerId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("rejectReason", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("invalidReason", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("memo", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("isStf", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("customerContractCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("ldcNeedOriginalPaper", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signDate", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("priceEndType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("ldcFrame", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryFactoryCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("deliveryFactoryName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("modifyContent", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("addedDepositRate", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("addedDepositRate2", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("userId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("ttType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("supplierAccountId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("addedSignatureType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("signType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("tradeType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("salesType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractNature", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractSource", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("contractSignatureStatus", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("status", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("approvalStatus", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("approvalType", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("code", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("rootContractId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("sourceContractId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("supplierId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("supplierName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("customerName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("customerCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("customerId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsCategoryId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("domainCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("futureCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("belongCustomerId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("payConditionId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("payConditionCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("companyId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("companyName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("invoicePaymentRate", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("usage", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("buCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("siteCode", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("siteName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("category1", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("category2", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("category3", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsId", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("goodsName", "SalesContractSplitTTDTO");
        mapSalesContractSplitTTDTO.put("commodityName", "SalesContractSplitTTDTO");
        mapSalesContractTTPriceDTO.put("ttId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("priceApplyId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("allocateId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("type", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("num", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("tempPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("diffPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractPriceDetail", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("transactionPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("price", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("priceTime", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("memo", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("createdBy", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("updatedBy", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractType", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("userId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsPackageId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsSpecId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("unitPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("fobUnitPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("cifUnitPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("totalAmount", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("supplierAccountId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("deliveryFactoryCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("deliveryFactoryName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("remainPriceNum", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("totalPriceNum", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("originalPriceNum", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("avePrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("endContractPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("endAllPrice", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("thisTimeFee", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("ownerId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("priceEndType", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("priceEndTime", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("thisContractNum", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category1", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category2", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category3", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("allocateNum", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("tradeType", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("salesType", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractNature", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractSource", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("contractSignatureStatus", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("status", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("approvalStatus", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("approvalType", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("code", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("rootContractId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("sourceContractId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("supplierId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("supplierName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("customerName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("customerCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("customerId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsCategoryId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("domainCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("futureCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("belongCustomerId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("payConditionId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("payConditionCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("companyId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("companyName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("invoicePaymentRate", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("usage", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("buCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("siteCode", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("siteName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category1", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category2", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("category3", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsId", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("goodsName", "SalesContractTTPriceDTO");
        mapSalesContractTTPriceDTO.put("commodityName", "SalesContractTTPriceDTO");
        mapSalesContractTTTransferDTO.put("ttId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("sonContractId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("type", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("num", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("originalDomainCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("tempPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("diffPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("thisTimeFee", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("transactionPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("price", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("memo", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("createdBy", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("updatedBy", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("createdAt", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("updatedAt", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("userId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("modifyContent", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("goodsPackageId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("categoryId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("goodsSpecId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("ownerId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("unitPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("fobUnitPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("cifUnitPrice", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("totalAmount", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("supplierAccountId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("deliveryFactoryCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("deliveryFactoryName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("signDate", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("addedSignatureType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("content", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("priceEndType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("PriceApplyId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("priceAllocateId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("priceEndTime", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("addedDepositRate2", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category1", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category2", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category3", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("tradeType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("salesType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractNature", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractSource", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("contractSignatureStatus", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("status", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("approvalStatus", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("approvalType", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("code", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("rootContractId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("sourceContractId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("supplierId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("supplierName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("customerName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("customerCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("customerId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("goodsCategoryId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("domainCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("futureCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("belongCustomerId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("payConditionId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("payConditionCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("companyId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("companyName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("invoicePaymentRate", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("usage", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("buCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("siteCode", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("siteName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category1", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category2", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("category3", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("goodsId", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("goodsName", "SalesContractTTTransferDTO");
        mapSalesContractTTTransferDTO.put("commodityName", "SalesContractTTTransferDTO");
        mapSalesStructurePriceTTDTO.put("structureType", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("totalNum", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("cumulativePrice", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("triggerPrice", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("totalDay", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("structureUnitNum", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("unitNum", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("unitIncrement", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("startTime", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("endTime", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("signDate", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("cashReturn", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("goodsCategoryId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("ownerId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("ttId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("contractCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("contractId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("priceApplyId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("createdBy", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("updatedBy", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("createdAt", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("updatedAt", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("userId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("createStatus", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("deliveryFactoryCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("deliveryFactoryId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("structureName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("tradeType", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("salesType", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("contractNature", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("contractSource", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("contractSignatureStatus", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("status", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("approvalStatus", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("approvalType", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("code", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("rootContractId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("sourceContractId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("supplierId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("supplierName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("customerName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("customerCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("customerId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("goodsCategoryId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("domainCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("futureCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("belongCustomerId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("payConditionId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("payConditionCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("companyId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("companyName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("invoicePaymentRate", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("usage", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("buCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("siteCode", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("siteName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("category1", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("category2", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("category3", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("goodsId", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("goodsName", "SalesStructurePriceTTDTO");
        mapSalesStructurePriceTTDTO.put("commodityName", "SalesStructurePriceTTDTO");
        mapTradeTicketEntity.put("id", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractId", "TradeTicketEntity");
        mapTradeTicketEntity.put("code", "TradeTicketEntity");
        mapTradeTicketEntity.put("type", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractType", "TradeTicketEntity");
        mapTradeTicketEntity.put("status", "TradeTicketEntity");
        mapTradeTicketEntity.put("approvalStatus", "TradeTicketEntity");
        mapTradeTicketEntity.put("approvalType", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractStatus", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractSignatureStatus", "TradeTicketEntity");
        mapTradeTicketEntity.put("operationSource", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractSource", "TradeTicketEntity");
        mapTradeTicketEntity.put("tradeType", "TradeTicketEntity");
        mapTradeTicketEntity.put("ownerId", "TradeTicketEntity");
        mapTradeTicketEntity.put("salesType", "TradeTicketEntity");
        mapTradeTicketEntity.put("invalidReason", "TradeTicketEntity");
        mapTradeTicketEntity.put("isDeleted", "TradeTicketEntity");
        mapTradeTicketEntity.put("createdBy", "TradeTicketEntity");
        mapTradeTicketEntity.put("updatedBy", "TradeTicketEntity");
        mapTradeTicketEntity.put("createdAt", "TradeTicketEntity");
        mapTradeTicketEntity.put("updatedAt", "TradeTicketEntity");
        mapTradeTicketEntity.put("protocolCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("signId", "TradeTicketEntity");
        mapTradeTicketEntity.put("goodsCategoryId", "TradeTicketEntity");
        mapTradeTicketEntity.put("subGoodsCategoryId", "TradeTicketEntity");
        mapTradeTicketEntity.put("customerId", "TradeTicketEntity");
        mapTradeTicketEntity.put("customerCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("customerName", "TradeTicketEntity");
        mapTradeTicketEntity.put("supplierId", "TradeTicketEntity");
        mapTradeTicketEntity.put("supplierCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("supplierName", "TradeTicketEntity");
        mapTradeTicketEntity.put("bankId", "TradeTicketEntity");
        mapTradeTicketEntity.put("futureCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("domainCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("belongCustomerId", "TradeTicketEntity");
        mapTradeTicketEntity.put("groupId", "TradeTicketEntity");
        mapTradeTicketEntity.put("sourceContractId", "TradeTicketEntity");
        mapTradeTicketEntity.put("beforeContractNum", "TradeTicketEntity");
        mapTradeTicketEntity.put("changeContractNum", "TradeTicketEntity");
        mapTradeTicketEntity.put("afterContractNum", "TradeTicketEntity");
        mapTradeTicketEntity.put("payConditionId", "TradeTicketEntity");
        mapTradeTicketEntity.put("occupyStatus", "TradeTicketEntity");
        mapTradeTicketEntity.put("companyId", "TradeTicketEntity");
        mapTradeTicketEntity.put("companyName", "TradeTicketEntity");
        mapTradeTicketEntity.put("usage", "TradeTicketEntity");
        mapTradeTicketEntity.put("cancelReason", "TradeTicketEntity");
        mapTradeTicketEntity.put("sourceType", "TradeTicketEntity");
        mapTradeTicketEntity.put("confirmPriceInfo", "TradeTicketEntity");
        mapTradeTicketEntity.put("buCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("category1", "TradeTicketEntity");
        mapTradeTicketEntity.put("category2", "TradeTicketEntity");
        mapTradeTicketEntity.put("category3", "TradeTicketEntity");
        mapTradeTicketEntity.put("contractNature", "TradeTicketEntity");
        mapTradeTicketEntity.put("goodsId", "TradeTicketEntity");
        mapTradeTicketEntity.put("goodsName", "TradeTicketEntity");
        mapTradeTicketEntity.put("commodityName", "TradeTicketEntity");
        mapTradeTicketEntity.put("siteCode", "TradeTicketEntity");
        mapTradeTicketEntity.put("siteName", "TradeTicketEntity");
        mapTradeTicketEntity.put("isSoybean2", "TradeTicketEntity");
        mapTTAddEntity.put("id", "TTAddEntity");
        mapTTAddEntity.put("contractCode", "TTAddEntity");
        mapTTAddEntity.put("contractId", "TTAddEntity");
        mapTTAddEntity.put("rootContractId", "TTAddEntity");
        mapTTAddEntity.put("status", "TTAddEntity");
        mapTTAddEntity.put("contractType", "TTAddEntity");
        mapTTAddEntity.put("customerCode", "TTAddEntity");
        mapTTAddEntity.put("customerId", "TTAddEntity");
        mapTTAddEntity.put("customerName", "TTAddEntity");
        mapTTAddEntity.put("supplierId", "TTAddEntity");
        mapTTAddEntity.put("supplierName", "TTAddEntity");
        mapTTAddEntity.put("goodsCategoryId", "TTAddEntity");
        mapTTAddEntity.put("supplierAccount", "TTAddEntity");
        mapTTAddEntity.put("signPlace", "TTAddEntity");
        mapTTAddEntity.put("domainCode", "TTAddEntity");
        mapTTAddEntity.put("goodsId", "TTAddEntity");
        mapTTAddEntity.put("goodsName", "TTAddEntity");
        mapTTAddEntity.put("unit", "TTAddEntity");
        mapTTAddEntity.put("goodsPackageId", "TTAddEntity");
        mapTTAddEntity.put("goodsSpecId", "TTAddEntity");
        mapTTAddEntity.put("needPackageWeight", "TTAddEntity");
        mapTTAddEntity.put("packageWeight", "TTAddEntity");
        mapTTAddEntity.put("qualityCheck", "TTAddEntity");
        mapTTAddEntity.put("currencyType", "TTAddEntity");
        mapTTAddEntity.put("unitPrice", "TTAddEntity");
        mapTTAddEntity.put("fobUnitPrice", "TTAddEntity");
        mapTTAddEntity.put("taxRate", "TTAddEntity");
        mapTTAddEntity.put("invoiceType", "TTAddEntity");
        mapTTAddEntity.put("cifUnitPrice", "TTAddEntity");
        mapTTAddEntity.put("contractNum", "TTAddEntity");
        mapTTAddEntity.put("temporaryPrice", "TTAddEntity");
        mapTTAddEntity.put("transactionPrice", "TTAddEntity");
        mapTTAddEntity.put("totalAmount", "TTAddEntity");
        mapTTAddEntity.put("extraPrice", "TTAddEntity");
        mapTTAddEntity.put("creditDays", "TTAddEntity");
        mapTTAddEntity.put("paymentType", "TTAddEntity");
        mapTTAddEntity.put("depositAmount", "TTAddEntity");
        mapTTAddEntity.put("addedDepositAmount", "TTAddEntity");
        mapTTAddEntity.put("depositRate", "TTAddEntity");
        mapTTAddEntity.put("depositUseRule", "TTAddEntity");
        mapTTAddEntity.put("delayPayFine", "TTAddEntity");
        mapTTAddEntity.put("oem", "TTAddEntity");
        mapTTAddEntity.put("deliveryStartTime", "TTAddEntity");
        mapTTAddEntity.put("deliveryEndTime", "TTAddEntity");
        mapTTAddEntity.put("deliveryType", "TTAddEntity");
        mapTTAddEntity.put("deliveryFactoryCode", "TTAddEntity");
        mapTTAddEntity.put("deliveryFactoryName", "TTAddEntity");
        mapTTAddEntity.put("destination", "TTAddEntity");
        mapTTAddEntity.put("shipWarehouseId", "TTAddEntity");
        mapTTAddEntity.put("isArrangeTransport", "TTAddEntity");
        mapTTAddEntity.put("weightCheck", "TTAddEntity");
        mapTTAddEntity.put("weightTolerance", "TTAddEntity");
        mapTTAddEntity.put("priceStartTime", "TTAddEntity");
        mapTTAddEntity.put("priceEndTime", "TTAddEntity");
        mapTTAddEntity.put("memo", "TTAddEntity");
        mapTTAddEntity.put("createdBy", "TTAddEntity");
        mapTTAddEntity.put("updatedBy", "TTAddEntity");
        mapTTAddEntity.put("createdAt", "TTAddEntity");
        mapTTAddEntity.put("updatedAt", "TTAddEntity");
        mapTTAddEntity.put("signDate", "TTAddEntity");
        mapTTAddEntity.put("isStf", "TTAddEntity");
        mapTTAddEntity.put("priceEndType", "TTAddEntity");
        mapTTAddEntity.put("addedDepositRate", "TTAddEntity");
        mapTTAddEntity.put("addedDepositRate2", "TTAddEntity");
        mapTTAddEntity.put("enterprise", "TTAddEntity");
        mapTTAddEntity.put("completedStatus", "TTAddEntity");
        mapTTAddEntity.put("washoutUnitPrice", "TTAddEntity");
        mapTTAddEntity.put("sourceContractNum", "TTAddEntity");
        mapTTAddEntity.put("washoutPriceDetail", "TTAddEntity");
        mapTTAddEntity.put("content", "TTAddEntity");
        mapTTAddEntity.put("forwardPrice", "TTAddEntity");
        mapTTAddEntity.put("invoicePaymentRate", "TTAddEntity");
        mapTTAddEntity.put("residualRiskLimit", "TTAddEntity");
        mapTTAddEntity.put("residualRiskUsage", "TTAddEntity");
        mapTTAddEntity.put("residualRiskResidue", "TTAddEntity");
        mapTTAddEntity.put("residualRiskTradeStatus", "TTAddEntity");
        mapTTAddEntity.put("destinationValue", "TTAddEntity");
        mapTTAddEntity.put("deliveryTypeValue", "TTAddEntity");
        mapTTAddEntity.put("packageWeightValue", "TTAddEntity");
        mapTTAddEntity.put("invoiceTypeValue", "TTAddEntity");
        mapTTAddEntity.put("weightCheckValue", "TTAddEntity");
        mapTTAddEntity.put("shipWarehouseValue", "TTAddEntity");
        mapTTAddEntity.put("warrantTradeType", "TTAddEntity");
        mapTTAddEntity.put("settleType", "TTAddEntity");
        mapTTAddEntity.put("writeOffStartTime", "TTAddEntity");
        mapTTAddEntity.put("writeOffEndTime", "TTAddEntity");
        mapTTAddEntity.put("warrantId", "TTAddEntity");
        mapTTAddEntity.put("warrantCode", "TTAddEntity");
        mapTTAddEntity.put("futureCode", "TTAddEntity");
        mapTTAddEntity.put("standardType", "TTAddEntity");
        mapTTAddEntity.put("standardFileId", "1");
        mapTTAddEntity.put("standardRemark", "TTAddEntity");
        mapTTAddEntity.put("depositPaymentType", "TTAddEntity");
        mapTTAddEntity.put("deliveryMarginAmount", "TTAddEntity");
        mapTTAddEntity.put("warrantCategory", "TTAddEntity");
        mapTTAddEntity.put("ttId", "TTAddEntity");
        mapTTModifyEntity.put("id", "TTModifyEntity");
        mapTTModifyEntity.put("contractCode", "TTModifyEntity");
        mapTTModifyEntity.put("type", "TTModifyEntity");
        mapTTModifyEntity.put("contractId", "TTModifyEntity");
        mapTTModifyEntity.put("sourceContractId", "TTModifyEntity");
        mapTTModifyEntity.put("rootContractId", "TTModifyEntity");
        mapTTModifyEntity.put("contractType", "TTModifyEntity");
        mapTTModifyEntity.put("customerId", "TTModifyEntity");
        mapTTModifyEntity.put("customerName", "TTModifyEntity");
        mapTTModifyEntity.put("customerCode", "TTModifyEntity");
        mapTTModifyEntity.put("supplierId", "TTModifyEntity");
        mapTTModifyEntity.put("supplierName", "TTModifyEntity");
        mapTTModifyEntity.put("supplierAccount", "TTModifyEntity");
        mapTTModifyEntity.put("customerStatus", "TTModifyEntity");
        mapTTModifyEntity.put("needOriginalPaper", "TTModifyEntity");
        mapTTModifyEntity.put("originalPaperStatus", "TTModifyEntity");
        mapTTModifyEntity.put("signatureUrl", "TTModifyEntity");
        mapTTModifyEntity.put("signatureType", "TTModifyEntity");
        mapTTModifyEntity.put("signPlace", "TTModifyEntity");
        mapTTModifyEntity.put("signatureStatus", "TTModifyEntity");
        mapTTModifyEntity.put("tradeType", "TTModifyEntity");
        mapTTModifyEntity.put("domainCode", "TTModifyEntity");
        mapTTModifyEntity.put("goodsId", "TTModifyEntity");
        mapTTModifyEntity.put("goodsName", "TTModifyEntity");
        mapTTModifyEntity.put("weightUnit", "TTModifyEntity");
        mapTTModifyEntity.put("goodsCategoryId", "TTModifyEntity");
        mapTTModifyEntity.put("goodsPackageId", "TTModifyEntity");
        mapTTModifyEntity.put("goodsSpecId", "TTModifyEntity");
        mapTTModifyEntity.put("needPackageWeight", "TTModifyEntity");
        mapTTModifyEntity.put("packageWeight", "TTModifyEntity");
        mapTTModifyEntity.put("qualityCheck", "TTModifyEntity");
        mapTTModifyEntity.put("currencyType", "TTModifyEntity");
        mapTTModifyEntity.put("unitPrice", "TTModifyEntity");
        mapTTModifyEntity.put("baseDiffPrice", "TTModifyEntity");
        mapTTModifyEntity.put("fobUnitPrice", "TTModifyEntity");
        mapTTModifyEntity.put("taxRate", "TTModifyEntity");
        mapTTModifyEntity.put("cifUnitPrice", "TTModifyEntity");
        mapTTModifyEntity.put("contractNum", "TTModifyEntity");
        mapTTModifyEntity.put("temporaryPrice", "TTModifyEntity");
        mapTTModifyEntity.put("transactionPrice", "TTModifyEntity");
        mapTTModifyEntity.put("totalAmount", "TTModifyEntity");
        mapTTModifyEntity.put("totalDeliveryNum", "TTModifyEntity");
        mapTTModifyEntity.put("creditDays", "TTModifyEntity");
        mapTTModifyEntity.put("paymentType", "TTModifyEntity");
        mapTTModifyEntity.put("depositAmount", "TTModifyEntity");
        mapTTModifyEntity.put("addedDeposit", "TTModifyEntity");
        mapTTModifyEntity.put("depositRate", "TTModifyEntity");
        mapTTModifyEntity.put("depositReleaseType", "TTModifyEntity");
        mapTTModifyEntity.put("delayPayFine", "TTModifyEntity");
        mapTTModifyEntity.put("oem", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryStartTime", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryEndTime", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryType", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryFactory", "TTModifyEntity");
        mapTTModifyEntity.put("destination", "TTModifyEntity");
        mapTTModifyEntity.put("shipWarehouseId", "TTModifyEntity");
        mapTTModifyEntity.put("isArrangeTransport", "TTModifyEntity");
        mapTTModifyEntity.put("weightCheck", "TTModifyEntity");
        mapTTModifyEntity.put("weightTolerance", "TTModifyEntity");
        mapTTModifyEntity.put("priceStartTime", "TTModifyEntity");
        mapTTModifyEntity.put("priceEndTime", "TTModifyEntity");
        mapTTModifyEntity.put("ownerId", "TTModifyEntity");
        mapTTModifyEntity.put("rejectReason", "TTModifyEntity");
        mapTTModifyEntity.put("invalidReason", "TTModifyEntity");
        mapTTModifyEntity.put("memo", "TTModifyEntity");
        mapTTModifyEntity.put("isStf", "TTModifyEntity");
        mapTTModifyEntity.put("customerContractCode", "TTModifyEntity");
        mapTTModifyEntity.put("ldcNeedOriginalPaper", "TTModifyEntity");
        mapTTModifyEntity.put("signDate", "TTModifyEntity");
        mapTTModifyEntity.put("priceEndType", "TTModifyEntity");
        mapTTModifyEntity.put("ldcFrame", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryFactoryCode", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryFactoryName", "TTModifyEntity");
        mapTTModifyEntity.put("extraPrice", "TTModifyEntity");
        mapTTModifyEntity.put("forwardPrice", "TTModifyEntity");
        mapTTModifyEntity.put("factoryPrice", "TTModifyEntity");
        mapTTModifyEntity.put("proteinDiffPrice", "TTModifyEntity");
        mapTTModifyEntity.put("compensationPrice", "TTModifyEntity");
        mapTTModifyEntity.put("optionPrice", "TTModifyEntity");
        mapTTModifyEntity.put("transportPrice", "TTModifyEntity");
        mapTTModifyEntity.put("liftingPrice", "TTModifyEntity");
        mapTTModifyEntity.put("delayPrice", "TTModifyEntity");
        mapTTModifyEntity.put("temperaturePrice", "TTModifyEntity");
        mapTTModifyEntity.put("otherDeliveryPrice", "TTModifyEntity");
        mapTTModifyEntity.put("buyBackPrice", "TTModifyEntity");
        mapTTModifyEntity.put("complaintDiscountPrice", "TTModifyEntity");
        mapTTModifyEntity.put("transferFactoryPrice", "TTModifyEntity");
        mapTTModifyEntity.put("otherPrice", "TTModifyEntity");
        mapTTModifyEntity.put("businessPrice", "TTModifyEntity");
        mapTTModifyEntity.put("fee", "TTModifyEntity");
        mapTTModifyEntity.put("shippingFeePrice", "TTModifyEntity");
        mapTTModifyEntity.put("refineDiffPrice", "TTModifyEntity");
        mapTTModifyEntity.put("modifyContent", "TTModifyEntity");
        mapTTModifyEntity.put("createdBy", "TTModifyEntity");
        mapTTModifyEntity.put("updatedBy", "TTModifyEntity");
        mapTTModifyEntity.put("createdAt", "TTModifyEntity");
        mapTTModifyEntity.put("updatedAt", "TTModifyEntity");
        mapTTModifyEntity.put("addedDepositRate", "TTModifyEntity");
        mapTTModifyEntity.put("addedDepositRate2", "TTModifyEntity");
        mapTTModifyEntity.put("addedDepositAmount", "TTModifyEntity");
        mapTTModifyEntity.put("relationId", "TTModifyEntity");
        mapTTModifyEntity.put("newContractNum", "TTModifyEntity");
        mapTTModifyEntity.put("content", "TTModifyEntity");
        mapTTModifyEntity.put("invoiceType", "TTModifyEntity");
        mapTTModifyEntity.put("invoicePaymentRate", "TTModifyEntity");
        mapTTModifyEntity.put("destinationValue", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryTypeValue", "TTModifyEntity");
        mapTTModifyEntity.put("packageWeightValue", "TTModifyEntity");
        mapTTModifyEntity.put("invoiceTypeValue", "TTModifyEntity");
        mapTTModifyEntity.put("weightCheckValue", "TTModifyEntity");
        mapTTModifyEntity.put("shipWarehouseValue", "TTModifyEntity");
        mapTTModifyEntity.put("priceDetailInfo", "TTModifyEntity");
        mapTTModifyEntity.put("isModifyAll", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryId", "TTModifyEntity");
        mapTTModifyEntity.put("deliveryPassword", "TTModifyEntity");
        mapTTModifyEntity.put("writeOffDate", "TTModifyEntity");
        mapTTModifyEntity.put("writeOffDeliveryStartTime", "TTModifyEntity");
        mapTTModifyEntity.put("writeOffDeliveryEndTime", "TTModifyEntity");
        mapTTModifyEntity.put("futureCode", "TTModifyEntity");
        mapTTModifyEntity.put("warrantTradeType", "TTModifyEntity");
        mapTTModifyEntity.put("writeOffNum", "TTModifyEntity");
        mapTTModifyEntity.put("warrantId", "TTModifyEntity");
        mapTTModifyEntity.put("warrantCode", "TTModifyEntity");
        mapTTModifyEntity.put("category", "TTModifyEntity");
        mapTTModifyEntity.put("ttId", "TTModifyEntity");
        mapTTPriceEntity.put("id", "TTPriceEntity");
        mapTTPriceEntity.put("ttId", "TTPriceEntity");
        mapTTPriceEntity.put("contractCode", "TTPriceEntity");
        mapTTPriceEntity.put("contractId", "TTPriceEntity");
        mapTTPriceEntity.put("priceApplyId", "TTPriceEntity");
        mapTTPriceEntity.put("allocateId", "TTPriceEntity");
        mapTTPriceEntity.put("sourceContractId", "TTPriceEntity");
        mapTTPriceEntity.put("type", "TTPriceEntity");
        mapTTPriceEntity.put("contraryStatus", "TTPriceEntity");
        mapTTPriceEntity.put("num", "TTPriceEntity");
        mapTTPriceEntity.put("remainPriceNum", "TTPriceEntity");
        mapTTPriceEntity.put("originalPriceNum", "TTPriceEntity");
        mapTTPriceEntity.put("totalPriceNum", "TTPriceEntity");
        mapTTPriceEntity.put("tempPrice", "TTPriceEntity");
        mapTTPriceEntity.put("unitPrice", "TTPriceEntity");
        mapTTPriceEntity.put("diffPrice", "TTPriceEntity");
        mapTTPriceEntity.put("transactionPrice", "TTPriceEntity");
        mapTTPriceEntity.put("price", "TTPriceEntity");
        mapTTPriceEntity.put("priceTime", "TTPriceEntity");
        mapTTPriceEntity.put("memo", "TTPriceEntity");
        mapTTPriceEntity.put("createdBy", "TTPriceEntity");
        mapTTPriceEntity.put("updatedBy", "TTPriceEntity");
        mapTTPriceEntity.put("createdAt", "TTPriceEntity");
        mapTTPriceEntity.put("updatedAt", "TTPriceEntity");
        mapTTPriceEntity.put("goodsCategoryId", "TTPriceEntity");
        mapTTPriceEntity.put("goodsId", "TTPriceEntity");
        mapTTPriceEntity.put("customerId", "TTPriceEntity");
        mapTTPriceEntity.put("customerName", "TTPriceEntity");
        mapTTPriceEntity.put("customerCode", "TTPriceEntity");
        mapTTPriceEntity.put("supplierId", "TTPriceEntity");
        mapTTPriceEntity.put("supplierName", "TTPriceEntity");
        mapTTPriceEntity.put("sourceId", "TTPriceEntity");
        mapTTPriceEntity.put("avePrice", "TTPriceEntity");
        mapTTPriceEntity.put("endContractPrice", "TTPriceEntity");
        mapTTPriceEntity.put("endAllPrice", "TTPriceEntity");
        mapTTPriceEntity.put("contractPriceDetail", "TTPriceEntity");
        mapTTPriceEntity.put("priceEndType", "TTPriceEntity");
        mapTTPriceEntity.put("priceEndTime", "TTPriceEntity");
        mapTTPriceEntity.put("thisContractNum", "TTPriceEntity");
        mapTTStructureEntity.put("id", "TTStructureEntity");
        mapTTStructureEntity.put("ttId", "TTStructureEntity");
        mapTTStructureEntity.put("contractCode", "TTStructureEntity");
        mapTTStructureEntity.put("contractId", "TTStructureEntity");
        mapTTStructureEntity.put("structureType", "TTStructureEntity");
        mapTTStructureEntity.put("totalNum", "TTStructureEntity");
        mapTTStructureEntity.put("totalDay", "TTStructureEntity");
        mapTTStructureEntity.put("unitNum", "TTStructureEntity");
        mapTTStructureEntity.put("domainCode", "TTStructureEntity");
        mapTTStructureEntity.put("memo", "TTStructureEntity");
        mapTTStructureEntity.put("startTime", "TTStructureEntity");
        mapTTStructureEntity.put("endTime", "TTStructureEntity");
        mapTTStructureEntity.put("createdBy", "TTStructureEntity");
        mapTTStructureEntity.put("updatedBy", "TTStructureEntity");
        mapTTStructureEntity.put("createdAt", "TTStructureEntity");
        mapTTStructureEntity.put("updatedAt", "TTStructureEntity");
        mapTTStructureEntity.put("customerId", "TTStructureEntity");
        mapTTStructureEntity.put("customerName", "TTStructureEntity");
        mapTTStructureEntity.put("goodsCategoryId", "TTStructureEntity");
        mapTTStructureEntity.put("signDate", "TTStructureEntity");
        mapTTStructureEntity.put("unitIncrement", "TTStructureEntity");
        mapTTStructureEntity.put("cashReturn", "TTStructureEntity");
        mapTTStructureEntity.put("cumulativePrice", "TTStructureEntity");
        mapTTStructureEntity.put("triggerPrice", "TTStructureEntity");
        mapTTStructureEntity.put("structureUnitNum", "TTStructureEntity");
        mapTTStructureEntity.put("deliveryFactoryCode", "TTStructureEntity");
        mapTTStructureEntity.put("deliveryFactoryId", "TTStructureEntity");
        mapTTStructureEntity.put("structureName", "TTStructureEntity");
        mapTTTranferEntity.put("id", "TTTranferEntity");
        mapTTTranferEntity.put("ttId", "TTTranferEntity");
        mapTTTranferEntity.put("contractCode", "TTTranferEntity");
        mapTTTranferEntity.put("contractId", "TTTranferEntity");
        mapTTTranferEntity.put("sourceContractId", "TTTranferEntity");
        mapTTTranferEntity.put("contractType", "TTTranferEntity");
        mapTTTranferEntity.put("type", "TTTranferEntity");
        mapTTTranferEntity.put("contraryStatus", "TTTranferEntity");
        mapTTTranferEntity.put("num", "TTTranferEntity");
        mapTTTranferEntity.put("originalDomainCode", "TTTranferEntity");
        mapTTTranferEntity.put("domainCode", "TTTranferEntity");
        mapTTTranferEntity.put("tempPrice", "TTTranferEntity");
        mapTTTranferEntity.put("diffPrice", "TTTranferEntity");
        mapTTTranferEntity.put("transactionPrice", "TTTranferEntity");
        mapTTTranferEntity.put("price", "TTTranferEntity");
        mapTTTranferEntity.put("memo", "TTTranferEntity");
        mapTTTranferEntity.put("createdBy", "TTTranferEntity");
        mapTTTranferEntity.put("updatedBy", "TTTranferEntity");
        mapTTTranferEntity.put("createdAt", "TTTranferEntity");
        mapTTTranferEntity.put("updatedAt", "TTTranferEntity");
        mapTTTranferEntity.put("modifyContent", "TTTranferEntity");
        mapTTTranferEntity.put("goodsCategoryId", "TTTranferEntity");
        mapTTTranferEntity.put("goodsId", "TTTranferEntity");
        mapTTTranferEntity.put("customerId", "TTTranferEntity");
        mapTTTranferEntity.put("customerName", "TTTranferEntity");
        mapTTTranferEntity.put("customerCode", "TTTranferEntity");
        mapTTTranferEntity.put("supplierId", "TTTranferEntity");
        mapTTTranferEntity.put("supplierName", "TTTranferEntity");
        mapTTTranferEntity.put("content", "TTTranferEntity");
        mapTTTranferEntity.put("priceApplyId", "TTTranferEntity");
        mapTTTranferEntity.put("priceAllocateId", "TTTranferEntity");
        mapTTTranferEntity.put("thisFee", "TTTranferEntity");

        mapOmc.put("addedDepositRate", "Omc");
        mapOmc.put("approvalStatus", "Omc");
        mapOmc.put("approvalType", "Omc");
        mapOmc.put("buCode", "Omc");
        mapOmc.put("businessPrice", "Omc");
        mapOmc.put("buyBackPrice", "Omc");
        mapOmc.put("category1", "Omc");
        mapOmc.put("category2", "Omc");
        mapOmc.put("category3", "Omc");
        mapOmc.put("code", "Omc");
        mapOmc.put("commodityName", "Omc");
        mapOmc.put("compensationPrice", "Omc");
        mapOmc.put("complaintDiscountPrice", "Omc");
        mapOmc.put("completedStatus", "Omc");
        mapOmc.put("contractCode", "Omc");
        mapOmc.put("contractId", "Omc");
        mapOmc.put("contractNature", "Omc");
        mapOmc.put("contractNum", "Omc");
        mapOmc.put("contractSignatureStatus", "Omc");
        mapOmc.put("contractSource", "Omc");
        mapOmc.put("contractType", "Omc");
        mapOmc.put("createStatus", "Omc");
        mapOmc.put("creditDays", "Omc");
        mapOmc.put("customerCode", "Omc");
        mapOmc.put("customerId", "Omc");
        mapOmc.put("customerName", "Omc");
        mapOmc.put("delayPayFine", "Omc");
        mapOmc.put("delayPrice", "Omc");
        mapOmc.put("deliveryEndTime", "Omc");
        mapOmc.put("deliveryFactoryCode", "Omc");
        mapOmc.put("deliveryFactoryName", "Omc");
        mapOmc.put("deliveryMarginAmount", "Omc");
        mapOmc.put("deliveryStartTime", "Omc");
        mapOmc.put("deliveryType", "Omc");
        mapOmc.put("depositAmount", "Omc");
        mapOmc.put("depositPaymentType", "Omc");
        mapOmc.put("depositRate", "Omc");
        mapOmc.put("depositUseRule", "Omc");
        mapOmc.put("destination", "Omc");
        mapOmc.put("domainCode", "Omc");
        mapOmc.put("exchangeCode", "Omc");
        mapOmc.put("extraPrice", "Omc");
        mapOmc.put("factoryPrice", "Omc");
        mapOmc.put("fee", "Omc");
        mapOmc.put("forwardPrice", "Omc");
        mapOmc.put("futureCode", "Omc");
        mapOmc.put("goodsCategoryId", "Omc");
        mapOmc.put("goodsId", "Omc");
        mapOmc.put("goodsName", "Omc");
        mapOmc.put("goodsPackageId", "Omc");
        mapOmc.put("goodsSpecId", "Omc");
        mapOmc.put("invoicePaymentRate", "Omc");
        mapOmc.put("isStf", "Omc");
        mapOmc.put("liftingPrice", "Omc");
        mapOmc.put("maxPrice", "Omc");
        mapOmc.put("memo", "Omc");
        mapOmc.put("minPrice", "Omc");
        mapOmc.put("needPackageWeight", "Omc");
        mapOmc.put("oem", "Omc");
        mapOmc.put("optionPrice", "Omc");
        mapOmc.put("otherDeliveryPrice", "Omc");
        mapOmc.put("otherPrice", "Omc");
        mapOmc.put("ownerId", "Omc");
        mapOmc.put("packageWeight", "Omc");
        mapOmc.put("payConditionId", "Omc");
        mapOmc.put("paymentType", "Omc");
        mapOmc.put("priceEndTime", "Omc");
        mapOmc.put("priceEndType", "Omc");
        mapOmc.put("priceStartTime", "Omc");
        mapOmc.put("proteinDiffPrice", "Omc");
        mapOmc.put("refineDiffPrice", "Omc");
        mapOmc.put("refineFracDiffPrice", "Omc");
        mapOmc.put("rootContractId", "Omc");
        mapOmc.put("salesType", "Omc");
        mapOmc.put("settleType", "Omc");
        mapOmc.put("shippingFeePrice", "Omc");
        mapOmc.put("shipWarehouseId", "Omc");
        mapOmc.put("signDate", "Omc");
        mapOmc.put("signPlace", "Omc");
        mapOmc.put("siteCode", "Omc");
        mapOmc.put("siteName", "Omc");
        mapOmc.put("standardFileId", "1");
        mapOmc.put("standardRemark", "Omc");
        mapOmc.put("standardType", "Omc");
        mapOmc.put("status", "Omc");
        mapOmc.put("structurePriceEndTime", "Omc");
        mapOmc.put("structurePriceStartTime", "Omc");
        mapOmc.put("structureTotalNum", "Omc");
        mapOmc.put("structureType", "Omc");
        mapOmc.put("submitType", "Omc");
        mapOmc.put("supplierAccountId", "Omc");
        mapOmc.put("supplierId", "Omc");
        mapOmc.put("surveyFees", "Omc");
        mapOmc.put("temperaturePrice", "Omc");
        mapOmc.put("totalDay", "Omc");
        mapOmc.put("tradeType", "Omc");
        mapOmc.put("transferFactoryPrice", "Omc");
        mapOmc.put("transportPrice", "Omc");
        mapOmc.put("ttId", "Omc");
        mapOmc.put("ttKernelDTOList", "Omc");
        mapOmc.put("type", "Omc");
        mapOmc.put("unitNum", "Omc");
        mapOmc.put("unitPrice", "Omc");
        mapOmc.put("usage", "Omc");
        mapOmc.put("userId", "Omc");
        mapOmc.put("warrantCategory", "Omc");
        mapOmc.put("warrantTradeType", "Omc");
        mapOmc.put("weightCheck", "Omc");
        mapOmc.put("weightTolerance", "Omc");
        mapOmc.put("writeOffEndTime", "Omc");
        mapOmc.put("writeOffStartTime", "Omc");
        mapSca.put("addedDepositRate", "Sca");
        mapSca.put("addedDepositRate2", "Sca");
        mapSca.put("addedSignatureType", "Sca");
        mapSca.put("categoryCode", "Sca");
        mapSca.put("completedStatus", "Sca");
        mapSca.put("approvalStatus", "Sca");
        mapSca.put("approvalType", "Sca");
        mapSca.put("belongCustomerId", "Sca");
        mapSca.put("buCode", "Sca");
        mapSca.put("businessPrice", "Sca");
        mapSca.put("buyBackPrice", "Sca");
        mapSca.put("category1", "Sca");
        mapSca.put("category2", "Sca");
        mapSca.put("category3", "Sca");
        mapSca.put("code", "Sca");
        mapSca.put("commodityName", "Sca");
        mapSca.put("companyId", "Sca");
        mapSca.put("companyName", "Sca");
        mapSca.put("compensationPrice", "Sca");
        mapSca.put("complaintDiscountPrice", "Sca");
        mapSca.put("contractCode", "Sca");
        mapSca.put("contractId", "Sca");
        mapSca.put("contractNature", "Sca");
        mapSca.put("contractNum", "Sca");
        mapSca.put("contractSignatureStatus", "Sca");
        mapSca.put("contractSource", "Sca");
        mapSca.put("contractType", "Sca");
        mapSca.put("createStatus", "Sca");
        mapSca.put("creditDays", "Sca");
        mapSca.put("customerCode", "Sca");
        mapSca.put("customerId", "Sca");
        mapSca.put("customerName", "Sca");
        mapSca.put("delayPayFine", "Sca");
        mapSca.put("delayPrice", "Sca");
        mapSca.put("deliveryEndTime", "Sca");
        mapSca.put("deliveryFactoryCode", "Sca");
        mapSca.put("deliveryFactoryName", "Sca");
        mapSca.put("deliveryMarginAmount", "Sca");
        mapSca.put("deliveryStartTime", "Sca");
        mapSca.put("deliveryType", "Sca");
        mapSca.put("depositAmount", "Sca");
        mapSca.put("depositPaymentType", "Sca");
        mapSca.put("depositRate", "Sca");
        mapSca.put("depositUseRule", "Sca");
        mapSca.put("destination", "Sca");
        mapSca.put("domainCode", "Sca");
        mapSca.put("exchangeCode", "Sca");
        mapSca.put("extraPrice", "Sca");
        mapSca.put("factoryPrice", "Sca");
        mapSca.put("fee", "Sca");
        mapSca.put("forwardPrice", "Sca");
        mapSca.put("futureCode", "Sca");
        mapSca.put("goodsCategoryId", "Sca");
        mapSca.put("goodsId", "Sca");
        mapSca.put("goodsName", "Sca");
        mapSca.put("goodsPackageId", "Sca");
        mapSca.put("goodsSpecId", "Sca");
        mapSca.put("invoicePaymentRate", "Sca");
        mapSca.put("isStf", "Sca");
        mapSca.put("liftingPrice", "Sca");
        mapSca.put("memo", "Sca");
        mapSca.put("needPackageWeight", "Sca");
        mapSca.put("oem", "Sca");
        mapSca.put("optionPrice", "Sca");
        mapSca.put("otherDeliveryPrice", "Sca");
        mapSca.put("otherPrice", "Sca");
        mapSca.put("ownerId", "Sca");
        mapSca.put("packageWeight", "Sca");
        mapSca.put("payConditionId", "Sca");
        mapSca.put("payConditionCode", "Sca");
        mapSca.put("paymentType", "Sca");
        mapSca.put("priceEndTime", "Sca");
        mapSca.put("priceEndType", "Sca");
        mapSca.put("priceStartTime", "Sca");
        mapSca.put("proteinDiffPrice", "Sca");
        mapSca.put("refineDiffPrice", "Sca");
        mapSca.put("refineFracDiffPrice", "Sca");
        mapSca.put("rootContractId", "Sca");
        mapSca.put("salesType", "Sca");
        mapSca.put("settleType", "Sca");
        mapSca.put("shippingFeePrice", "Sca");
        mapSca.put("shipWarehouseCode", "Sca");
        mapSca.put("shipWarehouseId", "Sca");
        mapSca.put("shipWarehouseName", "Sca");
        mapSca.put("signDate", "Sca");
        mapSca.put("signPlace", "Sca");
        mapSca.put("signType", "Sca");
        mapSca.put("siteCode", "Sca");
        mapSca.put("siteName", "Sca");
        mapSca.put("sourceContractId", "Sca");
        mapSca.put("standardFileId", "1");
        mapSca.put("standardRemark", "Sca");
        mapSca.put("standardType", "Sca");
        mapSca.put("supplierAccountId", "Sca");
        mapSca.put("supplierId", "Sca");
        mapSca.put("supplierName", "Sca");
        mapSca.put("surveyFees", "Sca");
        mapSca.put("temperaturePrice", "Sca");
        mapSca.put("tradeType", "Sca");
        mapSca.put("transferFactoryPrice", "Sca");
        mapSca.put("transportPrice", "Sca");
        mapSca.put("ttId", "Sca");
        mapSca.put("type", "Sca");
        mapSca.put("unitPrice", "Sca");
        mapSca.put("usage", "Sca");
        mapSca.put("userId", "Sca");
        mapSca.put("warrantCode", "Sca");
        mapSca.put("warrantId", "Sca");
        mapSca.put("warrantCategory", "Sca");
        mapSca.put("warrantTradeType", "Sca");
        mapSca.put("washoutPriceDetailBO", "Sca");
        mapSca.put("washoutUnitPrice", "Sca");
        mapSca.put("weightCheck", "Sca");
        mapSca.put("weightTolerance", "Sca");
        mapSca.put("writeOffEndTime", "Sca");
        mapSca.put("writeOffStartTime", "Sca");

        mapContractPriceBaseEntity.put("extraPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("forwardPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("factoryPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("proteinDiffPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("compensationPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("optionPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("transportPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("liftingPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("delayPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("temperaturePrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("otherDeliveryPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("buyBackPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("complaintDiscountPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("transferFactoryPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("otherPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("businessPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("fee", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("shippingFeePrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("refineDiffPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("refineFracDiffPrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("surveyFees", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("previousRecord", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("vePrice", "ContractPriceBaseEntity");
        mapContractPriceBaseEntity.put("veContent", "ContractPriceBaseEntity");


        Map<String, Map> map = new HashMap<>();
        map.put("Omc", mapOmc);
        map.put("Sca", mapSca);
        map.put("OMContractAddTTDTO", mapOMContractAddTTDTO);
        map.put("CommonTradeInfoDTO", mapCommonTradeInfoDTO);
        map.put("PriceDetailDTO", mapPriceDetailDTO);
        map.put("PriceDetailBO", mapPriceDetailBO);
        map.put("SalesContractAddTTDTO", mapSalesContractAddTTDTO);
        map.put("TTCommonDTO", mapTTCommonDTO);
        map.put("SalesContractReviseTTDTO", mapSalesContractReviseTTDTO);
        map.put("SalesContractSplitTTDTO", mapSalesContractSplitTTDTO);
        map.put("SalesContractTTPriceDTO", mapSalesContractTTPriceDTO);
        map.put("SalesContractTTTransferDTO", mapSalesContractTTTransferDTO);
        map.put("SalesStructurePriceTTDTO", mapSalesStructurePriceTTDTO);
        map.put("TradeTicketEntity", mapTradeTicketEntity);
        map.put("TTAddEntity", mapTTAddEntity);
        map.put("TTModifyEntity", mapTTModifyEntity);
        map.put("TTPriceEntity", mapTTPriceEntity);
        map.put("TTStructureEntity", mapTTStructureEntity);
        map.put("TTTranferEntity", mapTTTranferEntity);
        map.put("ContractPriceBaseEntity", mapContractPriceBaseEntity);

        return map;
    }

    public static Map<String, String> initLogicInfoMap() {
        Map<String, String> map = new HashMap<>();
        map.put("ttId", "00-TT");
        map.put("code", "00-TT");
        map.put("type", "00-TT");
        map.put("tradeType", "00-TT");
        map.put("salesType", "00-TT");
        map.put("buCode", "00-TT");
        map.put("submitType", "00-TT");
        map.put("status", "00-TT");
        map.put("completedStatus", "00-TT");
        map.put("createStatus", "00-TT");
        map.put("userId", "00-TT");
        map.put("ownerId", "00-TT");
        map.put("memo", "00-TT");
        map.put("approvalStatus", "00-TT");
        map.put("approvalType", "00-TT");
        map.put("contractId", "01-合同协议");
        map.put("contractCode", "01-合同协议");
        map.put("contractSource", "01-合同协议");
        map.put("contractType", "01-合同协议");
        map.put("rootContractId", "01-合同协议");
        map.put("contractNature", "01-合同协议");
        map.put("signPlace", "01-合同协议");
        map.put("signDate", "01-合同协议");
        map.put("isStf", "01-合同协议");
        map.put("contractSignatureStatus", "01-合同协议");
        map.put("signId", "01-合同协议");
        map.put("protocolCode", "01-合同协议");
        map.put("siteCode", "02-主体");
        map.put("siteName", "02-主体");
        map.put("customerId", "03-买卖双方");
        map.put("customerCode", "03-买卖双方");
        map.put("customerName", "03-买卖双方");
        map.put("supplierId", "03-买卖双方");
        map.put("category1", "04-商品");
        map.put("category2", "04-商品");
        map.put("category3", "04-商品");
        map.put("goodsCategoryId", "04-商品");
        map.put("goodsPackageId", "04-商品");
        map.put("goodsSpecId", "04-商品");
        map.put("goodsId", "04-商品");
        map.put("goodsName", "04-商品");
        map.put("commodityName", "04-商品");
        map.put("oem", "04-商品");
        map.put("weightCheck", "04-商品");
        map.put("needPackageWeight", "04-商品");
        map.put("packageWeight", "04-商品");
        map.put("weightTolerance", "04-商品");
        map.put("usage", "04-商品");
        map.put("domainCode", "04-商品");
        map.put("standardType", "04-商品");
        map.put("standardFileId", "1");
        map.put("standardRemark", "04-商品");
        map.put("futureCode", "04-商品");
        map.put("warrantDomainCode", "04-商品");
        map.put("exchangeCode", "04-商品");
        map.put("contractNum", "05-量价钱");
        map.put("unitPrice", "05-量价钱");
        map.put("delayPayFine", "05-量价钱");
        map.put("depositUseRule", "05-量价钱");
        map.put("depositRate", "05-量价钱");
        map.put("depositAmount", "05-量价钱");
        map.put("addedDepositRate", "05-量价钱");
        map.put("invoicePaymentRate", "05-量价钱");
        map.put("priceEndType", "05-量价钱");
        map.put("priceEndTime", "05-量价钱");
        map.put("priceStartTime", "05-量价钱");
        map.put("businessPrice", "06-价格");
        map.put("buyBackPrice", "06-价格");
        map.put("compensationPrice", "06-价格");
        map.put("complaintDiscountPrice", "06-价格");
        map.put("delayPrice", "06-价格");
        map.put("extraPrice", "06-价格");
        map.put("factoryPrice", "06-价格");
        map.put("fee", "06-价格");
        map.put("forwardPrice", "06-价格");
        map.put("liftingPrice", "06-价格");
        map.put("optionPrice", "06-价格");
        map.put("otherDeliveryPrice", "06-价格");
        map.put("otherPrice", "06-价格");
        map.put("proteinDiffPrice", "06-价格");
        map.put("refineDiffPrice", "06-价格");
        map.put("refineFracDiffPrice", "06-价格");
        map.put("shippingFeePrice", "06-价格");
        map.put("surveyFees", "06-价格");
        map.put("temperaturePrice", "06-价格");
        map.put("transferFactoryPrice", "06-价格");
        map.put("transportPrice", "06-价格");
        map.put("paymentType", "07-财税票");
        map.put("settleType", "07-财税票");
        map.put("payConditionId", "07-财税票");
        map.put("creditDays", "07-财税票");
        map.put("supplierAccountId", "07-财税票");
        map.put("deliveryType", "08-提货");
        map.put("deliveryStartTime", "08-提货");
        map.put("deliveryEndTime", "08-提货");
        map.put("deliveryFactoryCode", "08-提货");
        map.put("deliveryFactoryName", "08-提货");
        map.put("destination", "08-提货");
        map.put("shipWarehouseId", "08-提货");
        map.put("warrantTradeType", "09-仓单");
        map.put("warrantCategory", "09-仓单");
        map.put("warrantDeliveryDepositPaymentType", "09-仓单");
        map.put("warrantDeliveryDepositAmount", "09-仓单");
        map.put("WriteOffStartTime", "09-仓单");
        map.put("WriteOffEndTime", "09-仓单");
        map.put("structureType", "10-结构化");
        map.put("minPrice", "10-结构化");
        map.put("maxPrice", "10-结构化");
        map.put("totalDay", "10-结构化");
        map.put("unitNum", "10-结构化");
        map.put("structurePriceStartTime", "10-结构化");
        map.put("structurePriceEndTime", "10-结构化");
        map.put("structureTotalNum", "10-结构化");

        return map;

    }
}
