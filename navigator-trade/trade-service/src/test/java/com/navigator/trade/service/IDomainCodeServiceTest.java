package com.navigator.trade.service;

import com.navigator.trade.pojo.entity.DomainPriceEntity;
import com.navigator.trade.service.futrue.IDomainCodeService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class IDomainCodeServiceTest {
    @Resource
    IDomainCodeService domainService;

    @Test
    void getClosingPrice() {
        DomainPriceEntity domainPriceEntity = domainService.getClosingPrice(1, "M2201", new Date(),null);
        System.out.println(domainPriceEntity.getPrice());

//        cp = domainService.getClosingPrice(1, "M2205");
//        System.out.println(cp);
    }

    @Test
    void testGetClosingPrice() {
    }
}
