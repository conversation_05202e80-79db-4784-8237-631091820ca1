package com.navigator.trade.utdata;

import com.navigator.admin.pojo.entity.EmployEntity;

public class EmployDataMocker {

    public static EmployEntity genEmploy() {
        EmployEntity employEntity = new EmployEntity();
        employEntity.setId(1001)
                .setCompanyId(1)
                .setDepartmentId(11)
                .setName("sudaqiang")
                .setPassword("123456")
                .setRealName("苏大强")
                .setNickName("Tony")
                .setWorkNo("LDC00088")
                .setEmail("<EMAIL>")
                .setSex(1)
                .setBirthday("")
                .setType(1)
                .setLock(0)
                .setStatus(1);

        return employEntity;
    }

}


