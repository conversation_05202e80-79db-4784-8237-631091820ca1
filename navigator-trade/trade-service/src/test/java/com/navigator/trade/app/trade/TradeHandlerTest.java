package com.navigator.trade.app.trade;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.tt.TTHandleFactory;
import com.navigator.trade.app.tt.domain.service.impl.TTDomainServiceImpl;
import com.navigator.trade.app.tt.domain.service.processor.AbstractTTDomainProcessor;
import com.navigator.trade.app.tt.logic.service.handler.ITTSceneHandler;
import com.navigator.trade.facade.impl.TradeTicketFacadeImpl;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.KeyTradeInfoTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SubmitTTDTO;
import com.navigator.trade.utdata.TTUTDataGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
//@Transactional
//@Rollback
class TradeHandlerTest {
    @Autowired
    TTDomainServiceImpl ttDomainService;

    @Autowired
    private TTHandleFactory ttHandleFactory;

    @Autowired
    TradeTicketFacadeImpl tradeTicketFacade;

    @MockBean
    CustomerFacade customerFacade;
    @MockBean
    CompanyFacade companyFacade;
    @MockBean
    SkuFacade skuFacade;
    @MockBean
    SiteFacade siteFacade;
    @MockBean
    CustomerDetailFacade customerDetailFacade;
    @MockBean
    CustomerInvoiceFacade customerInvoiceFacade;
    @MockBean
    SystemRuleFacade systemRuleFacade;
    @MockBean
    WarehouseFacade warehouseFacade;

    @BeforeEach
    void setUp() {

        CustomerDTO customerEntity = new CustomerDTO();
        customerEntity.setId(142);
        customerEntity.setName("上海甜头菜天正商务有限公司");
        customerEntity.setLinkageCustomerCode("TTC");
        customerEntity.setCompanyId(6);
        customerEntity.setStatus(1);
        customerEntity.setInvoiceType(1);

        CustomerDTO supplier = new CustomerDTO();
        supplier.setId(6);
        supplier.setName("路易达孚（天津）国际贸易有限公司");
        supplier.setLinkageCustomerCode("TJIB");
        supplier.setCompanyId(695);

        CompanyEntity companyEntity=new CompanyEntity();
        companyEntity.setId(695);
        companyEntity.setShortName("天津国贸");

        SkuEntity skuEntity=new SkuEntity();
        skuEntity.setCategory1(10);
        skuEntity.setCategory2(11);
        skuEntity.setCategory3(23);
        skuEntity.setId(10);
        skuEntity.setFullName("北京豆粕,浸出;50KG");

        SiteEntity siteEntity=new SiteEntity();
        siteEntity.setBelongCustomerId(695);
        siteEntity.setFactoryCode("ZJGG");
        siteEntity.setFactoryName("张家港港口");

        CustomerDetailEntity customerDetailEntity=new CustomerDetailEntity();
        customerDetailEntity.setCustomerId(977);

        List<CustomerInvoiceEntity> customerInvoiceEntities=new ArrayList<>();

        SystemRuleItemEntity systemRuleItemEntity=new SystemRuleItemEntity();
        systemRuleItemEntity.setId(1);
        systemRuleItemEntity.setRuleKey("30");

        WarehouseEntity warehouseEntity=new WarehouseEntity();
        warehouseEntity.setName("甜头菜仓库");
        Result wher = Result.success();
        wher.setData(warehouseEntity);

        Mockito.when(customerFacade.getCustomerById(142)).thenReturn(customerEntity);
        Mockito.when(customerFacade.getCustomerById(6)).thenReturn(supplier);
        Mockito.when(companyFacade.queryCompanyById(Mockito.anyInt())).thenReturn(companyEntity);
        Mockito.when(siteFacade.getSiteDetailByCode(Mockito.anyString())).thenReturn(siteEntity);
        Mockito.when(skuFacade.getSkuById(Mockito.anyInt())).thenReturn(skuEntity);
        Mockito.when(customerDetailFacade.queryCustomerDetailEntity(Mockito.anyInt(), Mockito.anyInt())).thenReturn(customerDetailEntity);
        Mockito.when(customerInvoiceFacade.queryCustomerInvoiceList(Mockito.any())).thenReturn(customerInvoiceEntities);
        Mockito.when(systemRuleFacade.getRuleItemById(Mockito.anyInt())).thenReturn(systemRuleItemEntity);
        Mockito.when(warehouseFacade.getWarehouseById(Mockito.anyInt())).thenReturn(wher);


    }

    @Test
    void testPurchaseWT(){
        OMContractAddTTDTO omContractAddTTDTO= TTUTDataGenerator.genOmContractAddTTDTO();


        SubmitTTDTO submitTTDTO=new SubmitTTDTO();

        submitTTDTO.setCreateTradeTicketDTO(omContractAddTTDTO);
        submitTTDTO.setSubmitStatus("1");
        submitTTDTO.setUserId("695");

        tradeTicketFacade.submitTT(submitTTDTO);
    }

    @Test
    void getStrategy() {
        /*TradeTicketDO tradeTicketDO = TTDataMocker.genTradeTicketDO(TTTypeEnum.PRICE);
        ttDomainService.createTradeTicketDO(tradeTicketDO);*/
        int i = 4946;
        /*for (TTTypeEnum ttTypeEnum : TTTypeEnum.values()) {
            if(ttTypeEnum==TTTypeEnum.UNKNOWN || ttTypeEnum==TTTypeEnum.EQUITY_CHANGE){
                continue;
            }
            TradeTicketDO tradeTicketDO = TTDataMocker.genTradeTicketDO(ttTypeEnum);
            TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
            tradeTicketEntity.setId(i);
            tradeTicketEntity.setType(ttTypeEnum.getType());
            tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
            ttDomainService.createTradeTicketDO(tradeTicketDO);
            i = i + 1;
        }*/


        AbstractTTDomainProcessor h1 = ttHandleFactory.getTTDomainProcessor(TTTypeEnum.PRICE);
        ITTSceneHandler h2 = ttHandleFactory.getTTSceneHandler(TTTypeEnum.PRICE);


        System.out.println("111111");


    }

    @Test
    void testGetStrategy() {

        List<String> listField = new ArrayList<>();

        OMContractAddTTDTO omContractAddTTDTO = new OMContractAddTTDTO();
        SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();
        KeyTradeInfoTTDTO keyTradeInfoTTDTO = new KeyTradeInfoTTDTO();

        System.out.println("=============");
        for (Field field : PriceDetailBO.class.getDeclaredFields()) {
            listField.add(field.getName());
        }

        listField = new ArrayList<>();
        for (Field field : PriceDetailDTO.class.getDeclaredFields()) {
            listField.add(field.getName());
        }

        Collections.sort(listField);

        listField.forEach(s -> {
            System.out.println(s);
        });
        //System.out.println(listField);

        System.out.println("================");
        PriceDetailDTO priceDetailDTO=new PriceDetailDTO();
        priceDetailDTO.setBusinessPrice("1")
                .setFee("2")
                .setBuyBackPrice("3")
                .setDelayPrice("4")
                .setExtraPrice("5")
                .setCompensationPrice("6")
                .setComplaintDiscountPrice("7")
                .setFactoryPrice("8")
                .setForwardPrice("9")
                .setLiftingPrice("10")
                .setOptionPrice("11")
                .setOtherPrice("12")
                .setOtherDeliveryPrice("13")
                .setProteinDiffPrice("14")
                .setRefineDiffPrice("15")
                .setRefineFracDiffPrice("16")
                .setShippingFeePrice("17")
                .setSurveyFees("18")
                .setTemperaturePrice("19")
                .setTransportPrice("20")
                .setTransferFactoryPrice("21")
                ;
        PriceDetailBO p = BeanConvertUtils.map(PriceDetailBO.class, priceDetailDTO);
        System.out.println(p);
    }
}