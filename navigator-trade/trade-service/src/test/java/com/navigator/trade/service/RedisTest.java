package com.navigator.trade.service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisTest {

    private static final int CONCURRENCY_LEVEL = 100;
    private static final CountDownLatch cdl = new CountDownLatch(CONCURRENCY_LEVEL);
    private static final ThreadFactory threadFactory = new ThreadFactoryBuilder()
            .setNameFormat("RedisTest-pool-%d").build();

    @Autowired
    private IContractQueryService contractService;

    private static final ExecutorService executorService = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() + 1,
            10, 0, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000), threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 测试递增发号器
     */
    @Test
    public void testSequence0() throws Exception {
        generateSequence0();
    }

    @Test
    public void testSequence1() {
        String s = contractService.genNewContractCode("DF", 2,11);
        System.out.println(Thread.currentThread().getName() + "generate seq=" + s);
    }

    private void generateSequence0() throws Exception {
        long start = System.currentTimeMillis();
        for (int i = 0; i < CONCURRENCY_LEVEL; i++) {
            // 使用 线程池+CountDownLatch 模拟多线程并发请求
            executorService.submit(() -> {
                try {
                    String seq = contractService.genNewContractCode("TJDF", 2,11);
                    System.out.println(Thread.currentThread().getName() + "generate seq=" + seq);
                } catch (Exception e) {
                    System.out.println(Thread.currentThread().getName() + "执行异常：" + e.getMessage());
                } finally {
                    // 线程启动后，倒计数器-1，表示有一个线程就绪了
                    cdl.countDown();
                }
            });
        }

        // 主线程一直等待 所有获取序列的线程执行完毕
        cdl.await();
        System.out.println("发号结束，耗时：" + (System.currentTimeMillis() - start) / 100 + "s");
        executorService.shutdown();
        System.out.println("=========================================================");
    }
}
