package com.navigator.trade.utdata;

import com.navigator.bisiness.enums.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.koala.pojo.enums.WarrantPaymentTypeEnum;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.KeyTradeInfoTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TTUTDataGenerator {

    //TODO NEO 需整理出可用的完整基础数据

    public static TTDTO genTTAddData() {
        TTDTO ttdto = new TTDTO();

        ttdto.setProcessorType(ProcessorTypeEnum.SBM_S_ADD.getTtValue());

        SalesContractAddTTDTO scAddTTDTO = new SalesContractAddTTDTO();

        scAddTTDTO.setContractType("1");
        scAddTTDTO.setSupplierId(1);
        scAddTTDTO.setCustomerName("UTTEST:天门粤海饲料有限公司");
        scAddTTDTO.setCustomerCode("lc0000001");
        scAddTTDTO.setCustomerId(1);
        scAddTTDTO.setGoodsCategoryId(1);
        scAddTTDTO.setGoodsSpecId("11");
        scAddTTDTO.setGoodsPackageId("3");
        scAddTTDTO.setCreditDays(0);
        scAddTTDTO.setDeliveryFactoryName("天津");
        scAddTTDTO.setDeliveryFactoryCode("KH11111111");
        scAddTTDTO.setSignDate(DateTimeUtil.parseDateString("2022-02-16"));
        scAddTTDTO.setOem(0);
        scAddTTDTO.setDepositUseRule(1);
        scAddTTDTO.setPriceEndTime("");
        scAddTTDTO.setPriceEndType(1);
        scAddTTDTO.setAddedDepositRate(5);

        List<KeyTradeInfoTTDTO> keyInfoList = new ArrayList<>();
        KeyTradeInfoTTDTO keyInfoDTO = new KeyTradeInfoTTDTO();

        keyInfoDTO.setUnitPrice("16");
        keyInfoDTO.setContractNum("10");
        keyInfoDTO.setDeliveryStartTime(DateTimeUtil.parseDateString("2022-02-22"));
        keyInfoDTO.setDeliveryEndTime(DateTimeUtil.parseDateString("2022-03-14"));
        keyInfoDTO.setDomainCode("2205");
        keyInfoDTO.setDepositRate(10);
        keyInfoDTO.setDepositAmount("16");

        PriceDetailDTO priceDetailDTO = new PriceDetailDTO();
        priceDetailDTO.setExtraPrice("1");
        priceDetailDTO.setForwardPrice("1");
        priceDetailDTO.setProteinDiffPrice("1");
        priceDetailDTO.setCompensationPrice("1");
        priceDetailDTO.setOptionPrice("1");
        priceDetailDTO.setTransportPrice("1");
        priceDetailDTO.setLiftingPrice("1");
        priceDetailDTO.setDelayPrice("1");
        priceDetailDTO.setTemperaturePrice("1");
        priceDetailDTO.setOtherDeliveryPrice("1");
        priceDetailDTO.setBuyBackPrice("1");
        priceDetailDTO.setComplaintDiscountPrice("1");
        priceDetailDTO.setTransferFactoryPrice("1");
        priceDetailDTO.setOtherPrice("1");
        priceDetailDTO.setBusinessPrice("1");
        priceDetailDTO.setFee("1");

        keyInfoDTO.setPriceDetailDTO(priceDetailDTO);

        keyInfoList.add(keyInfoDTO);

        scAddTTDTO.setDeliveryType(1);
        scAddTTDTO.setDestination("17");
        scAddTTDTO.setWeightCheck("10");
        scAddTTDTO.setPackageWeight("");
        scAddTTDTO.setShipWarehouseId("1");
        scAddTTDTO.setWeightTolerance(1);
        scAddTTDTO.setNeedPackageWeight(0);
        scAddTTDTO.setOwnerId("1");
        scAddTTDTO.setIsStf(0);
        scAddTTDTO.setMemo("123");
        scAddTTDTO.setUserId("-9");

        scAddTTDTO.setGoodsCategoryId(1);

        //scAddTTDTO.setTtKernelDTOList(keyInfoList);

        ttdto.setSalesContractAddTTDTO(scAddTTDTO);

        return ttdto;

    }


    public static SupplierDTO genSupplierDTO() {
        SupplierDTO supplierDTO = new SupplierDTO();
        supplierDTO.setId(695);
        supplierDTO.setName("UT:张家港达孚生产厂");
        supplierDTO.setSignPlace("上海");
        List<CustomerBankDTO> listBankDTO = new ArrayList<>();
        listBankDTO.add(genCustomerBankDTO());
        return supplierDTO;
    }

    public static CustomerBankDTO genCustomerBankDTO() {
        CustomerBankDTO customerBankDTO = new CustomerBankDTO();
        customerBankDTO.setId(666);
        customerBankDTO.setBankName("中国银行");
        customerBankDTO.setBankAccountName("上海天茂");
        customerBankDTO.setBankAccountNo("************");
        customerBankDTO.setIsDefault(1);
        return customerBankDTO;
    }

    public static CustomerDTO genCustomerDTO() {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setLinkageCustomerCode("UT_C00001");
        customerDTO.setId(999);
        customerDTO.setName("单元测试用户");
        customerDTO.setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue());
        customerDTO.setEnterprise(0);

        return customerDTO;
    }

    public static CustomerDetailEntity genCustomerDetailEntity() {
        CustomerDetailEntity customerDetailEntity = new CustomerDetailEntity();
        customerDetailEntity.setQualityCheckContent("ut");
        customerDetailEntity.setDeliveryDelayFine(BigDecimal.valueOf(100));
        return customerDetailEntity;
    }

    public static GoodsInfoVO genGoodsInfoVO() {
        GoodsInfoVO goodsInfoVO = new GoodsInfoVO();
        goodsInfoVO.setGoodsId(777);
        goodsInfoVO.setGoodsName("张家港达孚43%50KG");
        goodsInfoVO.setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue());
        goodsInfoVO.setGoodsCode("ZJG4350");

        return goodsInfoVO;
    }

    public static OMContractAddTTDTO genOmContractAddTTDTO() {
        OMContractAddTTDTO omContractAddTTDTO=new OMContractAddTTDTO();

        omContractAddTTDTO.setType(TTTypeEnum.NEW.getType());
        omContractAddTTDTO.setContractType(ContractTypeEnum.YI_KOU_JIA.getValue()+"");
        omContractAddTTDTO.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue());
        omContractAddTTDTO.setBuCode(BuCodeEnum.WT.getValue());

        omContractAddTTDTO.setTradeType(ContractTradeTypeEnum.NEW.getValue());
        omContractAddTTDTO.setSubmitType(SubmitTypeEnum.SUBMIT.getValue());
        omContractAddTTDTO.setOwnerId("100050");

        omContractAddTTDTO.setSignPlace("TJ");
        omContractAddTTDTO.setSignDate(new Date());
        omContractAddTTDTO.setIsStf(1);

        omContractAddTTDTO.setSiteCode("TJIB");
        omContractAddTTDTO.setSiteName("TJIB GM");

        omContractAddTTDTO.setCustomerId("142");
        omContractAddTTDTO.setCustomerCode("C_142");
        omContractAddTTDTO.setCustomerName("上海甜头菜电子商务有限公司");
        omContractAddTTDTO.setSupplierId("6");
        omContractAddTTDTO.setDomainCode("2501");
        omContractAddTTDTO.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        omContractAddTTDTO.setDepositPaymentType(WarrantPaymentTypeEnum.BANK_GUARANTEE.getValue());
        omContractAddTTDTO.setUsage(5);
        omContractAddTTDTO.setCategory1(10);
        omContractAddTTDTO.setCategory2(11);
        omContractAddTTDTO.setCategory3(23);
        omContractAddTTDTO.setGoodsId(55);
        omContractAddTTDTO.setGoodsName("豆粕-47%,48%");
        omContractAddTTDTO.setCommodityName("豆粕-47%,48%");

        omContractAddTTDTO.setWarrantTradeType(WarrantTradeTypeEnum.ONLINE_TRADE.getValue());
        omContractAddTTDTO.setDepositPaymentType(WarrantPaymentTypeEnum.BANK_GUARANTEE.getValue());
        omContractAddTTDTO.setDeliveryMarginAmount("35000");

        PriceDetailDTO priceDetailDTO=new PriceDetailDTO();


        KeyTradeInfoTTDTO keyTradeInfoTTDTO=new KeyTradeInfoTTDTO();
        keyTradeInfoTTDTO.setUnitPrice("3300");
        keyTradeInfoTTDTO.setUnitPrice("3250");
        keyTradeInfoTTDTO.setContractNum("100");
        keyTradeInfoTTDTO.setDeliveryEndTime(new Date());
        keyTradeInfoTTDTO.setDeliveryEndTime(new Date());
        keyTradeInfoTTDTO.setFutureCode("2501");
        keyTradeInfoTTDTO.setExchangeCode("M");
        keyTradeInfoTTDTO.setDomainCode("M2501");
        keyTradeInfoTTDTO.setDepositRate(10);
        keyTradeInfoTTDTO.setDepositAmount("33000");
        keyTradeInfoTTDTO.setAddedDepositRate("5");
        keyTradeInfoTTDTO.setPayConditionId("99");
        keyTradeInfoTTDTO.setInvoicePaymentRate(10);
        keyTradeInfoTTDTO.setMemo("测试仓单采购新增");




        keyTradeInfoTTDTO.setPriceDetailDTO(priceDetailDTO);

        List<KeyTradeInfoTTDTO> keyTradeInfoTTDTOS= new ArrayList<>();
        keyTradeInfoTTDTOS.add(keyTradeInfoTTDTO);
        omContractAddTTDTO.setTtKernelDTOList(keyTradeInfoTTDTOS);

        return omContractAddTTDTO;
    }
}


