package com.navigator.trade.service.contract;

import cn.hutool.core.date.DateUtil;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.pojo.entity.CheckContractEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.check.ICheckContractService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 合同表 测试类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class ContractBaseTest {
    @Resource
    IContractQueryService contractService;
    @Resource
    IContractOperationNewService salesContractOperationService;
    @Autowired
    ICheckContractService checkContractService;
    @Resource
    IContractExportService contractImportService;

    /**
     * 生成合同编码00
     */
    @Test
    public void genContractCodeTest() {
        log.info("生成的code{}", contractService.genNewContractCode("DF", 1, 11));
    }

    /**
     * 合同是否是超远期合同
     */
    @Test
    public void isOverForwardContractTest() {

        ContractEntity contractEntity = contractService.getContractById(2395);
        // 提货结束日期
        Date deliveryEndTime = contractEntity.getDeliveryEndTime();
        // 主力合约
        String domainCode = contractEntity.getDomainCode();

        /*if (!Arrays.asList(1, 5, 9).contains(DateUtil.month(deliveryEndTime) + 1)) {
            log.info("不是超远期合同");
            return;
        }*/

        if (DateUtil.betweenMonth(DateTimeUtil.parseDateValue("20" + domainCode + "01"), deliveryEndTime, false) >= 4) {
            log.info("是超远期合同");
        } else {
            log.info("不是超远期合同");
        }
    }

    @Test
    public void importDailyContract() {
        List<FileBaseInfoDTO> fileBaseInfoDTOS = contractImportService.exportDailyContract("2022-07-01", "2022-07-02");
        System.out.println(fileBaseInfoDTOS);
    }

    @Test
    public void toCamelCaseTest() {
        // 定义属性/方法集合
        List<Field> fieldList = new ArrayList<>();
        List<Method> methodList = new ArrayList<>();

        Class tempClass = CheckContractEntity.class;
        while (tempClass != null) {
            //当父类为null的时候说明到达了最上层的父类(Object类)
            fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
            methodList.addAll(Arrays.asList(tempClass.getDeclaredMethods()));
            //得到父类,然后赋给自己
            tempClass = tempClass.getSuperclass();
        }
        // 遍历属性，找到属性对应的方法
        for (Field f : fieldList) {
            String fieldName = "get" + f.getName();
            for (Method method : methodList) {
                if (fieldName.equalsIgnoreCase(method.getName())) {
                    String type = "";
                    if (f.getType().getName().equals("java.math.BigDecimal")) {
                        type = "decimal(25,6) DEFAULT ((0)) NULL";
                    } else if (f.getType().getName().equals("java.lang.String")) {
                        type = "varchar(64) NULL";
                    } else if (f.getType().getName().equals("java.lang.Integer")) {
                        type = "int NULL";
                    } else if (f.getType().getName().equals("java.util.Date")) {
                        type = "datetime NULL";
                    }
                    System.out.println("ALTER TABLE [dbo].[dbi_check_contract] ADD [" + StringUtil.toLine(f.getName()) + "] " + type + "\nGO");
                }
            }
        }
    }

    @Test
    public void testCheckContract() {
        checkContractService.checkContract(null, null,"2022-10-01 00:00:00", "2022-11-29 23:59:59");
//        checkContractService.checkContract(Arrays.asList("TJIBSBMS2205396"), null, null, null);
//        checkContractService.retryCheckContractByBatch(Arrays.asList("TJIBSBMS2205396"), "BATCH20221202164643");
    }
}
