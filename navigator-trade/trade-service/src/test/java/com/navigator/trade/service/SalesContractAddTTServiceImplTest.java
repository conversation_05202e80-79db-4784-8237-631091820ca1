package com.navigator.trade.service;

import com.navigator.trade.TradeNavigatorApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TradeNavigatorApplication.class)
@Transactional
@Rollback
public class SalesContractAddTTServiceImplTest {


    @Test
    public void testCreateTradeTicket() {
    }
}
