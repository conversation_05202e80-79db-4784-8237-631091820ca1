package com.navigator.trade.utdata;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.entity.*;
import cn.hutool.core.util.RandomUtil;
import com.navigator.trade.pojo.enums.TTDomainProcessTypeEnum;

import java.lang.reflect.Field;
import java.util.Date;

public class TTDataMocker {

    public static TradeTicketDO genTradeTicketDO(TTTypeEnum ttTypeEnum) {
        TradeTicketDO tradeTicketDO = new TradeTicketDO();

        TTDomainProcessTypeEnum ttDomainProcessTypeEnum = TTDomainProcessTypeEnum.getByTTType(ttTypeEnum);

        if (null == ttDomainProcessTypeEnum) {
            return null;
        }
        TradeTicketEntity tradeTicketEntity = genTradeTicketEntity();
        tradeTicketEntity.setStatus(999);
        tradeTicketEntity.setType(ttTypeEnum.getType());

        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        switch (ttDomainProcessTypeEnum) {
            case ADD:
                tradeTicketDO.setTtSubEntity(genTTAddEntity());
                break;
            case MODIFY:
                tradeTicketDO.setTtSubEntity(genTTModifyEntity());
                break;
            case PRICE:
                tradeTicketDO.setTtSubEntity(genTTPriceEntity());
                break;
            case TRANSFER:
                tradeTicketDO.setTtSubEntity(getTTTranferEntity());
                break;
            case STRUCTURE:
                tradeTicketDO.setTtSubEntity(getTTStructureEntity());
                break;
        }

        tradeTicketDO.setContractPriceEntity(genContractPriceEntity());

        return tradeTicketDO;
    }

    public static TradeTicketEntity genTradeTicketEntity() {
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();

        tradeTicketEntity.setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setCode(RandomUtil.randomString(10))
                .setType(RandomUtil.randomInt())
                .setContractType(RandomUtil.randomInt())
                .setStatus(1)
                .setApprovalStatus(RandomUtil.randomInt())
                .setApprovalType(RandomUtil.randomInt())
                .setContractStatus(RandomUtil.randomInt())
                .setContractSignatureStatus(1)
                .setOperationSource(RandomUtil.randomInt())
                .setContractSource(RandomUtil.randomInt())
                .setTradeType(RandomUtil.randomInt())
                .setOwnerId(RandomUtil.randomInt())
                .setSalesType(RandomUtil.randomInt())
                .setInvalidReason(RandomUtil.randomString(10))
                .setIsDeleted(0)
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedBy(RandomUtil.randomInt())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setProtocolCode(RandomUtil.randomString(10))
                .setSignId(RandomUtil.randomInt())
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setSubGoodsCategoryId(RandomUtil.randomInt())
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerCode(RandomUtil.randomString(10))
                .setCustomerName(RandomUtil.randomString(10))
                .setSupplierId(RandomUtil.randomInt())
                .setSupplierCode(RandomUtil.randomString(10))
                .setSupplierName(RandomUtil.randomString(10))
                .setBankId(RandomUtil.randomInt())
                .setDomainCode("M2501")
                .setBelongCustomerId(RandomUtil.randomInt())
                .setGroupId(RandomUtil.randomString(10))
                .setSourceContractId(RandomUtil.randomInt())
                .setBeforeContractNum(RandomUtil.randomBigDecimal())
                .setChangeContractNum(RandomUtil.randomBigDecimal())
                .setAfterContractNum(RandomUtil.randomBigDecimal())
                .setPayConditionId(RandomUtil.randomInt())
                .setOccupyStatus(RandomUtil.randomInt())
                .setCompanyId(RandomUtil.randomInt())
                .setCompanyName(RandomUtil.randomString(10))
                .setUsage(RandomUtil.randomInt())
                .setCancelReason(RandomUtil.randomString(10))
                .setSourceType(RandomUtil.randomInt())
                .setConfirmPriceInfo(RandomUtil.randomString(10))
                .setBuCode(RandomUtil.randomString(10))
                .setCategory1(RandomUtil.randomInt())
                .setCategory2(RandomUtil.randomInt())
                .setCategory3(RandomUtil.randomInt())
                .setContractNature(RandomUtil.randomInt())
                .setGoodsId(RandomUtil.randomInt())
                .setGoodsName(RandomUtil.randomString(10))
                .setCommodityName(RandomUtil.randomString(10))
                .setSiteCode(RandomUtil.randomString(10))
                .setSiteName(RandomUtil.randomString(10))
        ;
        return tradeTicketEntity;
    }

    public static TTAddEntity genTTAddEntity() {
        TTAddEntity ttAddEntity = new TTAddEntity();

        ttAddEntity.setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setRootContractId(RandomUtil.randomInt())
                .setStatus(1)
                .setContractType(RandomUtil.randomInt())
                .setCustomerCode(RandomUtil.randomString(10))
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerName(RandomUtil.randomString(10))
                .setSupplierId(RandomUtil.randomInt())
                .setSupplierName(RandomUtil.randomString(10))
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setSupplierAccount(RandomUtil.randomString(10))
                .setSignPlace(RandomUtil.randomString(10))
                .setDomainCode("M2501")
                .setGoodsId(RandomUtil.randomInt())
                .setGoodsName(RandomUtil.randomString(10))
                .setUnit(RandomUtil.randomString(10))
                .setGoodsPackageId(RandomUtil.randomInt())
                .setGoodsSpecId(RandomUtil.randomInt())
                .setNeedPackageWeight(RandomUtil.randomInt())
                .setPackageWeight(RandomUtil.randomString(10))
                .setQualityCheck(RandomUtil.randomString(10))
                .setCurrencyType(RandomUtil.randomString(10))
                .setUnitPrice(RandomUtil.randomBigDecimal())
                .setFobUnitPrice(RandomUtil.randomBigDecimal())
                .setTaxRate(RandomUtil.randomBigDecimal())
                .setInvoiceType(1)
                .setCifUnitPrice(RandomUtil.randomBigDecimal())
                .setContractNum(RandomUtil.randomBigDecimal())
                .setTemporaryPrice(RandomUtil.randomBigDecimal())
                .setTransactionPrice(RandomUtil.randomBigDecimal())
                .setTotalAmount(RandomUtil.randomBigDecimal())
                .setExtraPrice(RandomUtil.randomBigDecimal())
                .setCreditDays(RandomUtil.randomInt())
                .setPaymentType(RandomUtil.randomInt())
                .setDepositAmount(RandomUtil.randomBigDecimal())
                .setAddedDepositAmount(RandomUtil.randomBigDecimal())
                .setDepositRate(RandomUtil.randomInt())
                .setDepositUseRule(RandomUtil.randomInt())
                .setDelayPayFine(RandomUtil.randomBigDecimal())
                .setOem(1)
                .setDeliveryStartTime((new Date()))
                .setDeliveryEndTime((new Date()))
                .setDeliveryType(2)
                .setDeliveryFactoryCode(RandomUtil.randomString(10))
                .setDeliveryFactoryName(RandomUtil.randomString(10))
                .setDestination(RandomUtil.randomString(10))
                .setShipWarehouseId(RandomUtil.randomInt())
                .setIsArrangeTransport(1)
                .setWeightCheck(RandomUtil.randomString(10))
                .setWeightTolerance(RandomUtil.randomInt())
                .setPriceStartTime((new Date()))
                .setPriceEndTime(RandomUtil.randomString(10))
                .setMemo(RandomUtil.randomString(10))
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedAt((new Date()))
                .setCreatedAt((new Date()))
                .setSignDate(new Date())
                .setIsStf(RandomUtil.randomInt())
                .setPriceEndType(1)
                .setAddedDepositRate(RandomUtil.randomInt())
                .setAddedDepositRate2(RandomUtil.randomInt())
                .setEnterprise(RandomUtil.randomInt())
                .setCompletedStatus(RandomUtil.randomInt())
                .setWashoutUnitPrice(RandomUtil.randomBigDecimal())
                .setSourceContractNum(RandomUtil.randomBigDecimal())
                .setWashoutPriceDetail(RandomUtil.randomString(10))
                .setContent(RandomUtil.randomString(10))
                .setForwardPrice(RandomUtil.randomBigDecimal())
                .setInvoicePaymentRate(RandomUtil.randomInt())
                .setResidualRiskLimit(RandomUtil.randomString(10))
                .setResidualRiskUsage(RandomUtil.randomString(10))
                .setResidualRiskResidue(RandomUtil.randomString(10))
                .setResidualRiskTradeStatus(RandomUtil.randomString(10))
                .setDestinationValue(RandomUtil.randomString(10))
                .setDeliveryTypeValue(RandomUtil.randomString(10))
                .setPackageWeightValue(RandomUtil.randomString(10))
                .setInvoiceTypeValue(RandomUtil.randomString(10))
                .setWeightCheckValue(RandomUtil.randomString(10))
                .setShipWarehouseValue(RandomUtil.randomString(10))
                .setWarrantTradeType(RandomUtil.randomInt())
                .setSettleType(RandomUtil.randomString(10))
                .setWriteOffStartTime((new Date()))
                .setWriteOffEndTime((new Date()))
                .setWarrantId(RandomUtil.randomInt())
                .setWarrantCode(RandomUtil.randomString(10))
                .setFutureCode(RandomUtil.randomString(10))
                .setStandardType(RandomUtil.randomString(10))
                .setStandardFileId(RandomUtil.randomInt(10))
                .setStandardRemark(RandomUtil.randomString(10))
                .setDepositPaymentType(RandomUtil.randomInt())
                .setDeliveryMarginAmount(RandomUtil.randomBigDecimal())
                .setWarrantCategory(RandomUtil.randomInt())

        ;

        return ttAddEntity;
    }

    public static TTModifyEntity genTTModifyEntity() {
        TTModifyEntity ttModifyEntity = new TTModifyEntity();

        ttModifyEntity.setContractCode(RandomUtil.randomString(10))
                .setType(RandomUtil.randomInt())
                .setContractId(RandomUtil.randomInt())
                .setSourceContractId(RandomUtil.randomInt())
                .setRootContractId(RandomUtil.randomInt())
                .setContractType(RandomUtil.randomInt())
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerName(RandomUtil.randomString(10))
                .setCustomerCode(RandomUtil.randomString(10))
                .setSupplierId(RandomUtil.randomInt())
                .setSupplierName(RandomUtil.randomString(10))
                .setSupplierAccount(RandomUtil.randomString(10))
                .setCustomerStatus(RandomUtil.randomInt())
                .setNeedOriginalPaper(RandomUtil.randomInt())
                .setOriginalPaperStatus(0)
                .setSignatureUrl(RandomUtil.randomString(10))
                .setSignatureType(RandomUtil.randomString(10))
                .setSignPlace(RandomUtil.randomString(10))
                .setSignatureStatus(RandomUtil.randomInt())
                .setTradeType(RandomUtil.randomInt())
                .setDomainCode("M2501")
                .setGoodsId(RandomUtil.randomInt())
                .setGoodsName(RandomUtil.randomString(10))
                .setWeightUnit(RandomUtil.randomString(10))
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setGoodsPackageId(RandomUtil.randomInt())
                .setGoodsSpecId(RandomUtil.randomInt())
                .setNeedPackageWeight(RandomUtil.randomInt())
                .setPackageWeight(RandomUtil.randomString(10))
                .setQualityCheck(RandomUtil.randomString(10))
                .setCurrencyType(RandomUtil.randomString(10))
                .setUnitPrice(RandomUtil.randomBigDecimal())
                .setBaseDiffPrice(RandomUtil.randomBigDecimal())
                .setFobUnitPrice(RandomUtil.randomBigDecimal())
                .setTaxRate(RandomUtil.randomBigDecimal())
                .setCifUnitPrice(RandomUtil.randomBigDecimal())
                .setContractNum(RandomUtil.randomBigDecimal())
                .setTemporaryPrice(RandomUtil.randomBigDecimal())
                .setTransactionPrice(RandomUtil.randomBigDecimal())
                .setTotalAmount(RandomUtil.randomBigDecimal())
                .setTotalDeliveryNum(RandomUtil.randomBigDecimal())
                .setCreditDays(RandomUtil.randomInt())
                .setPaymentType(RandomUtil.randomInt())
                .setDepositAmount(RandomUtil.randomBigDecimal())
                .setAddedDeposit(RandomUtil.randomBigDecimal())
                .setDepositRate(RandomUtil.randomInt())
                .setDepositReleaseType(RandomUtil.randomInt())
                .setDelayPayFine(RandomUtil.randomBigDecimal())
                .setOem(1)
                .setDeliveryStartTime(new Date())
                .setDeliveryEndTime(new Date())
                .setDeliveryType(2)
                .setDeliveryFactory(RandomUtil.randomString(10))
                .setDestination(RandomUtil.randomString(10))
                .setShipWarehouseId(RandomUtil.randomInt())
                .setIsArrangeTransport(1)
                .setWeightCheck(RandomUtil.randomString(10))
                .setWeightTolerance(RandomUtil.randomInt())
                .setPriceStartTime(new Date())
                .setPriceEndTime(RandomUtil.randomString(10))
                .setOwnerId(RandomUtil.randomInt())
                .setRejectReason(RandomUtil.randomString(10))
                .setInvalidReason(RandomUtil.randomString(10))
                .setMemo(RandomUtil.randomString(10))
                .setIsStf(RandomUtil.randomInt())
                .setCustomerContractCode(RandomUtil.randomString(10))
                .setLdcNeedOriginalPaper(1)
                .setSignDate(new Date())
                .setPriceEndType(1)
                .setLdcFrame(1)
                .setDeliveryFactoryCode(RandomUtil.randomString(10))
                .setDeliveryFactoryName(RandomUtil.randomString(10))
                .setExtraPrice(RandomUtil.randomBigDecimal())
                .setForwardPrice(RandomUtil.randomBigDecimal())
                .setFactoryPrice(RandomUtil.randomBigDecimal())
                .setProteinDiffPrice(RandomUtil.randomBigDecimal())
                .setCompensationPrice(RandomUtil.randomBigDecimal())
                .setOptionPrice(RandomUtil.randomBigDecimal())
                .setTransportPrice(RandomUtil.randomBigDecimal())
                .setLiftingPrice(RandomUtil.randomBigDecimal())
                .setDelayPrice(RandomUtil.randomBigDecimal())
                .setTemperaturePrice(RandomUtil.randomBigDecimal())
                .setOtherDeliveryPrice(RandomUtil.randomBigDecimal())
                .setBuyBackPrice(RandomUtil.randomBigDecimal())
                .setComplaintDiscountPrice(RandomUtil.randomBigDecimal())
                .setTransferFactoryPrice(RandomUtil.randomBigDecimal())
                .setOtherPrice(RandomUtil.randomBigDecimal())
                .setBusinessPrice(RandomUtil.randomBigDecimal())
                .setFee(RandomUtil.randomBigDecimal())
                .setShippingFeePrice(RandomUtil.randomBigDecimal())
                .setRefineDiffPrice(RandomUtil.randomBigDecimal())
                .setModifyContent(RandomUtil.randomString(10))
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedBy(RandomUtil.randomInt())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setAddedDepositRate(RandomUtil.randomInt())
                .setAddedDepositRate2(RandomUtil.randomInt())
                .setAddedDepositAmount(RandomUtil.randomBigDecimal())
                .setRelationId(RandomUtil.randomString(10))
                .setNewContractNum(RandomUtil.randomBigDecimal())
                .setContent(RandomUtil.randomString(10))
                .setInvoiceType(RandomUtil.randomInt())
                .setInvoicePaymentRate(RandomUtil.randomInt())
                .setDestinationValue(RandomUtil.randomString(10))
                .setDeliveryTypeValue(RandomUtil.randomString(10))
                .setPackageWeightValue(RandomUtil.randomString(10))
                .setInvoiceTypeValue(RandomUtil.randomString(10))
                .setWeightCheckValue(RandomUtil.randomString(10))
                .setShipWarehouseValue(RandomUtil.randomString(10))
                .setPriceDetailInfo(RandomUtil.randomString(10))
                .setIsModifyAll(RandomUtil.randomInt())
                .setDeliveryId(RandomUtil.randomInt())
                .setDeliveryPassword(RandomUtil.randomString(10))
                .setWriteOffDate(new Date())
                .setWriteOffDeliveryStartTime(new Date())
                .setWriteOffDeliveryEndTime(new Date())
                .setFutureCode(RandomUtil.randomString(10))
                .setWarrantTradeType(RandomUtil.randomInt())
                .setWriteOffNum(RandomUtil.randomBigDecimal())
        ;


        return ttModifyEntity;
    }

    public static TTPriceEntity genTTPriceEntity() {

        TTPriceEntity ttPriceEntity = new TTPriceEntity();

        ttPriceEntity.setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setPriceApplyId(RandomUtil.randomInt())
                .setAllocateId(RandomUtil.randomInt())
                .setSourceContractId(RandomUtil.randomInt())
                .setType(RandomUtil.randomInt())
                .setContraryStatus(RandomUtil.randomInt())
                .setNum(RandomUtil.randomBigDecimal())
                .setRemainPriceNum(RandomUtil.randomBigDecimal())
                .setOriginalPriceNum(RandomUtil.randomBigDecimal())
                .setTotalPriceNum(RandomUtil.randomBigDecimal())
                .setTempPrice(RandomUtil.randomBigDecimal())
                .setUnitPrice(RandomUtil.randomBigDecimal())
                .setDiffPrice(RandomUtil.randomBigDecimal())
                .setTransactionPrice(RandomUtil.randomBigDecimal())
                .setPrice(RandomUtil.randomBigDecimal())
                .setPriceTime(new Date())
                .setMemo(RandomUtil.randomString(10))
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedBy(RandomUtil.randomInt())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setGoodsId(RandomUtil.randomInt())
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerName(RandomUtil.randomString(10))
                .setCustomerCode(RandomUtil.randomString(10))
                .setSupplierId(RandomUtil.randomInt())
                .setSupplierName(RandomUtil.randomString(10))
                .setSourceId(RandomUtil.randomInt())
                .setAvePrice(RandomUtil.randomBigDecimal())
                .setEndContractPrice(RandomUtil.randomBigDecimal())
                .setEndAllPrice(RandomUtil.randomBigDecimal())
                .setContractPriceDetail(RandomUtil.randomString(10))
                .setPriceEndType(1)
                .setPriceEndTime(RandomUtil.randomString(10))
                .setThisContractNum(RandomUtil.randomBigDecimal());

        return ttPriceEntity;
    }

    public static TTTranferEntity getTTTranferEntity() {
        TTTranferEntity ttTranferEntity = new TTTranferEntity();

        ttTranferEntity.setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setSourceContractId(RandomUtil.randomInt())
                .setContractType(RandomUtil.randomInt())
                .setType(RandomUtil.randomInt())
                .setContraryStatus(RandomUtil.randomInt())
                .setNum(RandomUtil.randomBigDecimal())
                .setOriginalDomainCode(RandomUtil.randomString(10))
                .setDomainCode("M2501")
                .setTempPrice(RandomUtil.randomBigDecimal())
                .setDiffPrice(RandomUtil.randomBigDecimal())
                .setTransactionPrice(RandomUtil.randomBigDecimal())
                .setPrice(RandomUtil.randomBigDecimal())
                .setMemo(RandomUtil.randomString(10))
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedBy(RandomUtil.randomInt())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setModifyContent(RandomUtil.randomString(10))
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setGoodsId(RandomUtil.randomInt())
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerName(RandomUtil.randomString(10))
                .setCustomerCode(RandomUtil.randomString(10))
                .setSupplierId(RandomUtil.randomInt())
                .setSupplierName(RandomUtil.randomString(10))
                .setContent(RandomUtil.randomString(10))
                .setPriceApplyId(RandomUtil.randomInt())
                .setPriceAllocateId(RandomUtil.randomInt())
                .setThisFee(RandomUtil.randomBigDecimal())
        ;
        return ttTranferEntity;
    }

    public static TTStructureEntity getTTStructureEntity() {
        TTStructureEntity ttStructureEntity = new TTStructureEntity();

        ttStructureEntity.setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setStructureType(RandomUtil.randomInt())
                .setTotalNum(RandomUtil.randomBigDecimal())
                .setTotalDay(RandomUtil.randomInt())
                .setUnitNum(RandomUtil.randomBigDecimal())
                .setDomainCode("M2501")
                .setMemo(RandomUtil.randomString(10))
                .setStartTime(new Date())
                .setEndTime(new Date())
                .setCreatedBy(RandomUtil.randomInt())
                .setUpdatedBy(RandomUtil.randomInt())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCustomerId(RandomUtil.randomInt())
                .setCustomerName(RandomUtil.randomString(10))
                .setGoodsCategoryId(RandomUtil.randomInt())
                .setSignDate(new Date())
                .setUnitIncrement(RandomUtil.randomBigDecimal())
                .setCashReturn(RandomUtil.randomString(10))
                .setCumulativePrice(RandomUtil.randomString(10))
                .setTriggerPrice(RandomUtil.randomString(10))
                .setStructureUnitNum(RandomUtil.randomBigDecimal())
                .setDeliveryFactoryCode(RandomUtil.randomString(10))
                .setDeliveryFactoryId(RandomUtil.randomInt())
                .setStructureName(RandomUtil.randomString(10))
        ;
        return ttStructureEntity;
    }

    public static ContractPriceEntity genContractPriceEntity() {
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();

        contractPriceEntity.setTtId(RandomUtil.randomInt())
                .setTtCode(RandomUtil.randomString(10))
                .setContractCode(RandomUtil.randomString(10))
                .setContractId(RandomUtil.randomInt())
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setExtraPrice(RandomUtil.randomBigDecimal())
                .setForwardPrice(RandomUtil.randomBigDecimal())
                .setFactoryPrice(RandomUtil.randomBigDecimal())
                .setProteinDiffPrice(RandomUtil.randomBigDecimal())
                .setCompensationPrice(RandomUtil.randomBigDecimal())
                .setOptionPrice(RandomUtil.randomBigDecimal())
                .setTransportPrice(RandomUtil.randomBigDecimal())
                .setLiftingPrice(RandomUtil.randomBigDecimal())
                .setDelayPrice(RandomUtil.randomBigDecimal())
                .setTemperaturePrice(RandomUtil.randomBigDecimal())
                .setOtherDeliveryPrice(RandomUtil.randomBigDecimal())
                .setBuyBackPrice(RandomUtil.randomBigDecimal())
                .setComplaintDiscountPrice(RandomUtil.randomBigDecimal())
                .setTransferFactoryPrice(RandomUtil.randomBigDecimal())
                .setOtherPrice(RandomUtil.randomBigDecimal())
                .setBusinessPrice(RandomUtil.randomBigDecimal())
                .setFee(RandomUtil.randomBigDecimal())
                .setShippingFeePrice(RandomUtil.randomBigDecimal())
                .setRefineDiffPrice(RandomUtil.randomBigDecimal())
                .setRefineFracDiffPrice(RandomUtil.randomBigDecimal())
                .setSurveyFees(RandomUtil.randomBigDecimal())
                .setPreviousRecord(RandomUtil.randomString(10))
        ;
        return contractPriceEntity;
    }

    public static OMContractAddTTDTO genOMContractAddTTDTO() {
        OMContractAddTTDTO omContractAddTTDTO = new OMContractAddTTDTO();

        for (Field declaredField : OMContractAddTTDTO.class.getDeclaredFields()) {
            System.out.println(declaredField.getName());
        }
        return omContractAddTTDTO;
    }
}
