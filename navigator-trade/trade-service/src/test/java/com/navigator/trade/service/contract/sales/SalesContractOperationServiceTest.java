package com.navigator.trade.service.contract.sales;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.pojo.entity.ContractEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
//@Transactional
//@Rollback
class SalesContractOperationServiceTest {

    @Resource
    ContractDao contractDao;

    @Resource
    SalesContractOperationService salesContractOperationService;

    @MockBean
    PriceApplyFacade priceApplyFacade;

    @BeforeEach
    void setUp() {
        Result<Integer> rtn=Result.success(695929);
        Mockito.when(priceApplyFacade.priceApply(Mockito.any())).thenReturn(rtn);
    }

    @Test
    public void testContractValid() {
        ContractEntity contractEntity = contractDao.getContractById(3266);
        salesContractOperationService.afterContractProcess(contractEntity);
    }

}