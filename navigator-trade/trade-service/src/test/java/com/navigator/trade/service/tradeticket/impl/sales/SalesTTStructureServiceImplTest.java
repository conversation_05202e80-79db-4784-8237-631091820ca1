package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.admin.facade.DepositRuleFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.utdata.ContractDataMocker;
import com.navigator.trade.utdata.ContractStructureDataMocker;
import com.navigator.trade.utdata.CustomerDataMocker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
@Transactional
@Rollback
class SalesTTStructureServiceImplTest {

    @Resource
    @Qualifier("SBM_S_TT_STRUCTURE_PRICE")
    ITradeTicketService tradeTicketService;

    @MockBean
    FactoryWarehouseFacade factoryWarehouseFacade;
    @MockBean
    OperationLogFacade operationLogFacade;
    @MockBean
    ApproveFacade approveFacade;
    @MockBean
    CustomerFacade customerFacade;
    @MockBean
    CustomerDetailFacade customerDetailFacade;
    @MockBean
    SystemRuleFacade systemRuleFacade;
    @MockBean
    DepositRuleFacade depositRuleFacade;
    @MockBean
    PriceAllocateFacade priceAllocateFacade;
    @Resource
    TradeTicketDao tradeTicketDao;
    @Resource
    private com.navigator.trade.handler.TTHandler TTHandler;

    @BeforeEach
    void setUp() {
//        Mockito.when(JwtUtils.getCurrentUserId()).thenReturn(SystemUserEnum.UT_TESTER.getUserId());

        CustomerEntity customer = CustomerDataMocker.genCustomer();
        CustomerEntity supplier = CustomerDataMocker.genSupplier();
        CustomerDTO supplierDTO = new CustomerDTO();
        CustomerDTO customerDTO = new CustomerDTO();

        customerDTO.setId(customer.getId());
        customerDTO.setShortName(customer.getShortName());
        customerDTO.setName(customer.getName());
        customerDTO.setLinkageCustomerCode("LG00001");

        supplierDTO.setId(supplier.getId());
        supplierDTO.setShortName(supplier.getShortName());
        supplierDTO.setName(supplier.getName());
        customerDTO.setLinkageCustomerCode("LG00002");

        Mockito.when(customerFacade.getCustomerById(20001)).thenReturn(supplierDTO);
        Mockito.when(customerFacade.getCustomerById(10001)).thenReturn(customerDTO);
    }


    @Test
    public void testAddContractStructure() {

        SalesStructurePriceTTDTO salesStructurePriceTTDTO = new SalesStructurePriceTTDTO();
        salesStructurePriceTTDTO.setTtId(111)
                // .setCompletedStatus(0)
                .setCustomerId(10001)
                .setCustomerName("天门粤海饲料有限公司")
                .setSupplierId(20001)
                .setDomainCode("2205")
                .setGoodsCategoryId(11)
        //.setContractNum(BigDecimal.valueOf(3333))
        //.setStartTime(DateTimeUtil.parseDateString("2022-03-28"))
        //.setEndTime(DateTimeUtil.parseDateString("2022-03-31"))
        //.setStructureType(1)
        //.setMinPrice(BigDecimal.valueOf(2222))
        //.setMaxPrice(BigDecimal.valueOf(3333))
        //.setSignDate(DateTimeUtil.parseDateString("2022-03-28"))
        // .setOwnerId("1")
        // .setTotalDay(10)
        //.setUnitNum(BigDecimal.valueOf(50))
        //.setPriceRule("")
        ;

        System.out.println(JSON.toJSONString(salesStructurePriceTTDTO));


        TTDTO ttdto = new TTDTO();
        ttdto.setSalesStructurePriceTTDTO(salesStructurePriceTTDTO);
        ttdto.setProcessorType(ProcessorTypeEnum.SBM_S_STRUCTURE_PRICE.getTtValue());
        ITradeTicketService tradeTicketService = TTHandler.getStrategy(ttdto.getProcessorType());
        tradeTicketService.saveTT(ttdto);
        /*TTDTO ttdto = ContractStructureDataMocker.genContractStructureTTDTO();

        tradeTicketService.saveTT(ttdto);*/
    }

    @Test
    public void testContractStructureValid() {
        TTDTO ttdto = ContractStructureDataMocker.genContractStructureTTDTO();

        tradeTicketService.saveTT(ttdto);
    }

    @Test
    public void testDate() {
        ContractStructureEntity cc = ContractDataMocker.genContractStructure(ContractDataMocker.genContract4Structure());

        ContractStructureDTO dto = BeanConvertUtils.convert(ContractStructureDTO.class, cc);

        System.out.println(JSON.toJSONString(dto));

    }

}