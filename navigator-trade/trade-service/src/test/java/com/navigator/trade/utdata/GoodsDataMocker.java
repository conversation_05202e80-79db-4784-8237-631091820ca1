package com.navigator.trade.utdata;

import cn.hutool.core.util.RandomUtil;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.trade.pojo.enums.UnitEnum;

import java.util.Date;

public class GoodsDataMocker {


    public static GoodsEntity genGoodsEntity() {
        GoodsEntity goodsEntity = new GoodsEntity();
        CustomerEntity supplier=CustomerDataMocker.genSupplier();

        goodsEntity.setId(707)
                .setLinkageGoodsCode("LKG_G_701")
                .setMdmGoodsCode("LDC_G_701")
                .setCode("G"+ RandomUtil.randomNumbers(6))
                .setName("张家港,豆粕,55%,80KG")
                .setShortName("ZJG43%80")
                .setPackageId(3)
                .setSpecId(11)
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue())
                .setPicture("")
                .setSupplierId(supplier.getId())
                .setSettleType(1)
                .setBarCode("")
                .setDescription("这是NEO的测试商品")
                .setMeasureUnit(UnitEnum.TON.getUnitTpe())
                .setStatus(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date());
        return goodsEntity;
    }
}
