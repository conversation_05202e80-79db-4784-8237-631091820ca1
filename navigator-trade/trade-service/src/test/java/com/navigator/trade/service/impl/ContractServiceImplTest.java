package com.navigator.trade.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.service.IContractHistoryService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.utdata.GoodsDataMocker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
//@Transactional
//@Rollback
class ContractServiceImplTest {

    @Resource
    ContractDao contractDao;
    @Resource
    IContractQueryService contractService;

    @MockBean
    GoodsFacade goodsFacade;
    @Resource
    private IContractValueObjectService contractValueObjectService;
    @Resource
    private IContractHistoryService contractHistoryService;

    @BeforeEach
    void setUp() {

        GoodsInfoVO goodsDto = BeanConvertUtils.convert(GoodsInfoVO.class, GoodsDataMocker.genGoodsEntity());
        Mockito.when(goodsFacade.findGoodsById(Mockito.anyInt())).thenReturn(goodsDto);

    }


    @Test
    public void testQuery() {
        QueryDTO<ContractQO> queryDto = new QueryDTO<>();
        ContractQO contractBO = new ContractQO();
        contractBO.setContractType(5);
        contractBO.setCustomerName(null);
        queryDto.setCondition(contractBO);

        Result list = contractService.queryContract(queryDto);

        System.out.println(JSON.toJSONString(list));


    }

    @Test
    public void testUpdateContract() {
        ContractEntity contractEntity = contractDao.getContractById(186);
        System.out.println(JSON.toJSONString(contractEntity));
        contractValueObjectService.updateContractById(contractEntity);
        contractEntity = contractDao.getContractById(186);
        System.out.println(JSON.toJSONString(contractEntity));
        contractValueObjectService.updateContractById(contractEntity, "test", "T" + DateTimeUtil.formatDateTimeString());
        contractEntity = contractDao.getContractById(186);

        ContractEntity cc = contractHistoryService.getContractEntity(186, 2);
        System.out.println("------------------------------");
        System.out.println(JSON.toJSONString(contractEntity));
        System.out.println(JSON.toJSONString(cc));
    }


}