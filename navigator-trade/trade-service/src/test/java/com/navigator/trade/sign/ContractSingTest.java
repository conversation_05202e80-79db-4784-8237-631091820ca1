package com.navigator.trade.sign;

import com.navigator.trade.handler.SalesContractSignHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 14:16
 */
@Slf4j
public class ContractSingTest {
    @Autowired
    private SalesContractSignHandler salesContractSignHandler;

    @Test
    public void generateContractSignPdf() {

        String s = String.format("envelopesStart.{%s}，参数{%s}|{%s}|{%s|{%s}", "uudi","appUrl", "envelopesStart.getAppId()", "envelopesStart.getAppSecretKey()", "request");

        System.out.println(s);

//        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
//        log.info(FastJsonUtils.getBeanToJson(contractSignService.generateSignTemplate(1244)));
    }
}
