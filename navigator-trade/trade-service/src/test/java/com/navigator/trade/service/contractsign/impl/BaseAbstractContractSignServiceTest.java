package com.navigator.trade.service.contractsign.impl;

import com.navigator.trade.service.contractsign.impl.sales.SBMSalesSignCreateService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class BaseAbstractContractSignServiceTest {



    @Resource
    SBMSalesSignCreateService contractSignService;


    @Test
    public void test(){
        try {
            contractSignService.sendCustomerNoticeEmail(3052);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}