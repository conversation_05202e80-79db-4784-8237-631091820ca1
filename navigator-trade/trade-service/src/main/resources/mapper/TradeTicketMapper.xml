<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.trade.mapper.TradeTicketMapper">

    <!-- 汇总TT状态数量 -->
    <select id="getTTStat" parameterType="com.navigator.trade.pojo.dto.tradeticket.StatQueryDTO"
            resultType="com.navigator.trade.pojo.vo.TTAllStatusNumVO">
        SELECT
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS newTT,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS approving,
        SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) AS done,
        SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) AS waiting,
        SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) AS canceled
        FROM dbt_trade_ticket
        WHERE is_deleted = 0
        AND sales_type = #{salesType}
        AND category2 = #{goodsCategoryId}
        AND bu_code = #{buCode}
        <if test="siteCodeList != null and siteCodeList.size() > 0">
            AND site_code IN
            <foreach item="siteCode" collection="siteCodeList" open="(" separator="," close=")">
                #{siteCode}
            </foreach>
        </if>
    </select>

    <!-- 通用查询条件 -->
    <sql id="commonWhereClause">
        WHERE tt.is_deleted = 0
        <!-- siteCode: 集合条件 -->
        <if test="siteCodeList != null and siteCodeList.size() > 0">
            AND (tt.site_code IN
            <foreach collection="siteCodeList" item="siteCode" open="(" separator="," close=")">
                #{siteCode}
            </foreach>
            OR tt.site_code IS NULL)
        </if>

        <!-- status: 精确匹配 -->
        <if test="status != null and status != ''">
            AND tt.status = #{status}
        </if>

        <!-- contractCode: 精确匹配 -->
        <if test="contractCode != null and contractCode != ''">
            AND tt.contract_code LIKE CONCAT('%',#{contractCode},'%')
        </if>

        <!-- code: 模糊匹配 -->
        <if test="code != null and code != ''">
            AND tt.code LIKE CONCAT('%', #{code}, '%')
        </if>

        <!-- type: 精确匹配 -->
        <if test="type != null and type != ''">
            AND tt.type = #{type}
        </if>

        <!-- 创建时间起始：大于等于 -->
        <if test="createStartTime != null and createStartTime != ''">
            AND tt.created_at &gt;= CONCAT(#{createStartTime}, ' 00:00:00')
        </if>

        <!-- 创建时间结束：小于等于 -->
        <if test="createEndTime != null and createEndTime != ''">
            AND tt.created_at &lt;= CONCAT(#{createEndTime}, ' 23:59:59')
        </if>

        <!-- salesType -->
        <if test="salesType != null">
            AND tt.sales_type = #{salesType}
        </if>

        <!-- createBy -->
        <if test="createBy != null and createBy != ''">
            AND tt.created_by = #{createBy}
        </if>

        <!-- companyId -->
        <if test="companyId != null and companyId != ''">
            AND tt.company_id = #{companyId}
        </if>

        <!-- customerName: 模糊匹配 -->
        <if test="customerName != null and customerName != ''">
            AND tt.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>

        <!-- supplierName: 模糊匹配 -->
        <if test="supplierName != null and supplierName != ''">
            AND tt.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>

        <!-- supplierId -->
        <if test="supplierId != null and supplierId != ''">
            AND tt.supplier_id = #{supplierId}
        </if>

        <!-- customerId -->
        <if test="customerId != null and customerId != ''">
            AND tt.customer_id = #{customerId}
        </if>

        <!-- goodsSpecId -->
        <if test="goodsSpecId != null and goodsSpecId != ''">
            AND COALESCE(tt_add.goods_spec_id, tt_modify.goods_spec_id, contract.goods_spec_id) = #{goodsSpecId}
        </if>

        <!-- goodsPackageId -->
        <if test="goodsPackageId != null and goodsPackageId != ''">
            AND COALESCE(tt_add.goods_package_id, tt_modify.goods_package_id, contract.goods_package_id) =
            #{goodsPackageId}
        </if>

        <!-- goodsCategoryId 对应 category2 -->
        <if test="goodsCategoryId != null and goodsCategoryId != ''">
            AND tt.category2 = #{goodsCategoryId}
        </if>

        <!-- category3 -->
        <if test="category3 != null and category3 != ''">
            AND tt.category3 = #{category3}
        </if>

        <!-- tradeType: 集合条件 -->
        <if test="tradeType != null and tradeType.size() > 0">
            AND tt.trade_type IN
            <foreach collection="tradeType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- enterpriseName：模糊查询 -->
        <if test="customerEnterpriseIds != null and customerEnterpriseIds.size() > 0">
            AND tt.customer_id IN
            <foreach collection="customerEnterpriseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="supplierEnterpriseIds != null and supplierEnterpriseIds.size() > 0">
            AND tt.supplier_id IN
            <foreach collection="supplierEnterpriseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- deliveryFactoryCode: 集合条件 -->
        <if test="deliveryFactoryCode != null and deliveryFactoryCode.size() > 0">
            AND COALESCE(tt_add.delivery_factory_code, tt_modify.delivery_factory_code, contract.delivery_factory_code)
            IN
            <foreach collection="deliveryFactoryCode" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- shipWarehouseId -->
        <if test="shipWarehouseId != null and shipWarehouseId != ''">
            AND COALESCE(tt_add.ship_warehouse_id, tt_modify.ship_warehouse_id, contract.ship_warehouse_id) =
            #{shipWarehouseId}
        </if>

        <!-- contractType -->
        <if test="contractType != null">
            AND tt.contract_type = #{contractType}
        </if>

        <!-- deliveryStartDate: 区间 -->
        <if test="deliveryStartDate != null and deliveryStartDate != ''">
            AND COALESCE(tt_add.delivery_start_time, tt_modify.delivery_start_time, contract.delivery_start_time)
            BETWEEN
            CAST(#{deliveryStartDate} + '-01 00:00:00' AS DATETIME)
            AND
            DATEADD(MONTH, 1, CAST(#{deliveryStartDate} + '-01 00:00:00' AS DATETIME))
        </if>

        <!-- deliveryStartTime: 大于等于 -->
        <if test="deliveryStartTime != null and deliveryStartTime != ''">
            AND COALESCE(tt_add.delivery_start_time, tt_modify.delivery_start_time, contract.delivery_start_time) &gt;=
            CONCAT(#{deliveryStartTime}, ' 00:00:00')
        </if>

        <!-- deliveryEndTime: 小于 -->
        <if test="deliveryEndTime != null and deliveryEndTime != ''">
            AND COALESCE(tt_add.delivery_end_time, tt_modify.delivery_end_time, contract.delivery_end_time) &lt;
            CONCAT(#{deliveryEndTime}, ' 23:59:59')
        </if>

        <!-- buCode -->
        <if test="buCode != null and buCode != ''">
            AND tt.bu_code = #{buCode}
        </if>

        <!-- warrantTradeType -->
        <if test="warrantTradeType != null">
            AND COALESCE(tt_add.warrant_trade_type, tt_modify.warrant_trade_type, contract.warrant_trade_type) =
            #{warrantTradeType}
        </if>

        <!-- goodsId -->
        <if test="goodsId != null">
            AND COALESCE(tt_add.goods_id, tt_modify.goods_id, contract.goods_id) = #{goodsId}
        </if>

        <!-- siteCode -->
        <if test="siteCode != null and siteCode != ''">
            AND tt.site_code = #{siteCode}
        </if>

        <!-- goodsName: 模糊匹配 -->
        <if test="goodsName != null and goodsName != ''">
            <trim prefix="AND (" suffix=")" suffixOverrides="OR">
                tt.goods_name LIKE CONCAT('%', #{goodsName}, '%') OR
                tt.commodity_name LIKE CONCAT('%', #{goodsName}, '%')
            </trim>
        </if>

        <!-- bizCode: 模糊匹配 -->
        <if test="bizCode != null and bizCode != ''">
            <trim prefix="AND (" suffix=")" suffixOverrides="OR">
                tt.contract_code LIKE CONCAT('%', #{bizCode}, '%') OR
                tt.code LIKE CONCAT('%', #{bizCode}, '%') OR
                contract.warrant_code LIKE CONCAT('%', #{bizCode}, '%')
            </trim>
        </if>
    </sql>

    <!-- 分页查询语句 -->
    <select id="queryTTPagedList" parameterType="com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO"
            resultType="com.navigator.trade.pojo.entity.TradeTicketVOEntity">
        SELECT
        tt.id,
        tt.bu_code,
        tt.site_code,
        tt.site_name,
        tt.contract_code,
        tt.contract_id,
        tt.code,
        tt.type,
        tt.contract_type,
        tt.status,
        tt.approval_status,
        tt.approval_type,
        tt.contract_signature_status,
        tt.operation_source,
        tt.contract_source,
        tt.trade_type,
        tt.owner_id,
        tt.sales_type,
        tt.invalid_reason,
        tt.is_deleted,
        tt.created_by,
        tt.updated_by,
        tt.created_at,
        tt.updated_at,
        tt.protocol_code,
        tt.sign_id,
        tt.goods_category_id,
        tt.sub_goods_category_id,
        tt.customer_id,
        tt.customer_code,
        tt.customer_name,
        tt.customer_type,
        tt.supplier_id,
        tt.supplier_name,
        tt.supplier_type,
        tt.bank_id,
        tt.supplier_code,
        tt.future_code,
        tt.belong_customer_id,
        tt.group_id,
        tt.source_contract_id,
        tt.before_contract_num,
        tt.change_contract_num,
        tt.after_contract_num,
        tt.pay_condition_id,
        tt.occupy_status,
        tt.company_id,
        tt.company_name,
        tt.usage,
        tt.cancel_reason,
        tt.source_type,
        tt.confirm_price_info,
        tt.category1,
        tt.category2,
        tt.category3,
        tt.goods_name,
        tt.commodity_name,
        contract.status AS contract_status,
        contract.is_soybean2 AS is_soybean2,
        COALESCE(tt.domain_code, contract.domain_code) AS domain_code,
        COALESCE(tt_add.delivery_start_time, tt_modify.delivery_start_time, contract.delivery_start_time) AS
        delivery_start_time,
        COALESCE(tt_add.delivery_end_time, tt_modify.delivery_end_time, contract.delivery_end_time) AS
        delivery_end_time,
        COALESCE(tt_add.goods_package_id, tt_modify.goods_package_id, contract.goods_package_id) AS goods_package_id,
        COALESCE(tt_add.goods_spec_id, tt_modify.goods_spec_id, contract.goods_spec_id) AS goods_spec_id,
        COALESCE(tt_add.ship_warehouse_id, tt_modify.ship_warehouse_id, contract.ship_warehouse_id) AS
        ship_warehouse_id,
        COALESCE(tt_add.ship_warehouse_value, tt_modify.ship_warehouse_value, contract.ship_warehouse_value) AS
        ship_warehouse_name,
        COALESCE(tt_add.delivery_factory_code, tt_modify.delivery_factory_code, contract.delivery_factory_code) AS
        delivery_factory_code,
        COALESCE(tt_add.extra_price, tt_modify.extra_price, contract.extra_price) AS extra_price,
        COALESCE(tt_add.unit_price, tt_modify.unit_price, contract.unit_price) AS unit_price,
        COALESCE(tt_add.delivery_type, tt_modify.delivery_type, contract.delivery_type) AS delivery_type,
        COALESCE(tt_add.delivery_type_value, tt_modify.delivery_type_value, contract.delivery_type_value) AS
        delivery_type_name,
        COALESCE(tt_add.package_weight, tt_modify.package_weight, contract.package_weight) AS package_weight,
        COALESCE(tt_add.package_weight_value, tt_modify.package_weight_value, contract.package_weight_value) AS
        package_weight_name,
        COALESCE(tt_add.price_end_time, tt_modify.price_end_time, contract.price_end_time) AS price_end_time,
        COALESCE(tt_add.deposit_rate, tt_modify.deposit_rate, contract.deposit_rate) AS deposit_rate,
        COALESCE(tt_add.added_deposit_rate, tt_modify.added_deposit_rate, contract.added_deposit_rate) AS
        added_deposit_rate,
        COALESCE(tt_add.added_deposit_rate2, tt_modify.added_deposit_rate2, contract.added_deposit_rate2) AS
        added_deposit_rate2,
        COALESCE(tt_add.credit_days, tt_modify.credit_days, contract.credit_days) AS credit_days,
        COALESCE(tt_add.memo, tt_modify.memo, contract.memo) AS memo,
        COALESCE(tt_add.warrant_code, tt_modify.warrant_code, contract.warrant_code) AS warrant_code,
        COALESCE(tt_add.warrant_id, tt_modify.warrant_id, contract.warrant_id) AS warrant_id,
        COALESCE(tt_add.warrant_trade_type, tt_modify.warrant_trade_type, contract.warrant_trade_type) AS
        warrant_trade_type,
        COALESCE(tt_add.write_off_start_time, contract.write_off_start_time) AS write_off_start_time,
        COALESCE(tt_add.write_off_end_time, contract.write_off_end_time) AS write_off_end_time,
        COALESCE(tt_add.goods_id, tt_modify.goods_id, contract.goods_id) AS goods_id
        FROM
        dbo.dbt_trade_ticket tt
        LEFT JOIN dbo.dbt_tt_add tt_add ON tt_add.tt_id = tt.id
        LEFT JOIN dbo.dbt_tt_modify tt_modify ON tt_modify.tt_id = tt.id
        LEFT JOIN dbo.dbt_contract contract ON contract.id = tt.contract_id
        LEFT JOIN dbo.dbt_tt_tranfer tt_tranfer ON tt_tranfer.tt_id = tt.id
        <include refid="commonWhereClause"/>
        ORDER BY tt.updated_at DESC
        OFFSET #{startRow} ROWS
        FETCH NEXT #{pageSize} ROWS ONLY;
    </select>

    <!-- 总记录数查询 -->
    <select id="queryTTTotalCount" resultType="int" parameterType="com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO">
        SELECT COUNT(1)
        FROM dbo.dbt_trade_ticket tt
        LEFT JOIN dbo.dbt_tt_add tt_add ON tt_add.tt_id = tt.id
        LEFT JOIN dbo.dbt_tt_modify tt_modify ON tt_modify.tt_id = tt.id
        LEFT JOIN dbo.dbt_contract contract ON contract.id = tt.contract_id
        LEFT JOIN dbo.dbt_tt_tranfer tt_tranfer ON tt_tranfer.tt_id = tt.id
        <include refid="commonWhereClause"/>
    </select>

</mapper>
