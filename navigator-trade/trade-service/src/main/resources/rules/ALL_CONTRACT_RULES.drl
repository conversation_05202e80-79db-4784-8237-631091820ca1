package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule r0

    when
        $approveRule:ContractApproveBizInfoDTO(maxTotalAmount >= 0)
    then
        //$approveRule.setRuleMemo($approveRule.getRuleMemo().append("有参数了;"));
        System.out.println("r0");
        //update($approveRule);
end

rule r1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >= maxTotalAmount)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#maxTotalAmount#;"));
        System.out.println("金额大于#maxTotalAmount#;");
        update($approveRule);
end

rule r2
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong == true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期超过#DELIVERY_DUE_MONTH#;"));
        System.out.println("交期超过阈值;");
        update($approveRule);

end

rule r3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < maxTotalAmount && totalAmount >= minTotalAmount)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额介于AC审批阈值;"));
        System.out.println("金额介于AC审批阈值;");
        update($approveRule);
end

rule r4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && proteinDiffPrice != 0 )
    then
        System.out.println("蛋白价差不为0;");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差不为0;"));
        update($approveRule);
end

rule r5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && transportPrice != 0 )
    then
        System.out.println("运费不为0");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        update($approveRule);
end

rule r6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && refineDiffPrice != 0 )
    then
        System.out.println("精炼价差不为0");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        update($approveRule);
end
rule r7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && businessPrice != 0 )
    then
        System.out.println("商务补贴不为0");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        update($approveRule);
end
rule r8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && otherPrice != 0 )
    then
        System.out.println("其他补贴不为0");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        update($approveRule);
end

rule r9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount < minTotalAmount && lowExtraPrice == true)
    then
        System.out.println("基差价格字段低于阈值");
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        update($approveRule);
end
rule r10
    when
        $approveRule:ContractApproveBizInfoDTO(proteinDiffPriceChanged ==true)
    then
        System.out.println("蛋白价差变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差变化;"));
        update($approveRule);
end
rule r11
    when
        $approveRule:ContractApproveBizInfoDTO(transportPriceChanged ==true)
    then
        System.out.println("运费变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变化;"));
        update($approveRule);
end
rule r12
    when
        $approveRule:ContractApproveBizInfoDTO(refineDiffPriceChanged ==true)
    then
        System.out.println("精炼价差变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变化;"));
        update($approveRule);
end
rule r13
    when
        $approveRule:ContractApproveBizInfoDTO(businessPriceChanged ==true)
    then
        System.out.println("商务补贴变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变化;"));
        update($approveRule);
end
rule r14
    when
        $approveRule:ContractApproveBizInfoDTO(otherPriceChanged ==true)
    then
        System.out.println("其他补贴变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变化;"));
        update($approveRule);
end
rule r15
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryEndTimeChanged ==true)
    then
        System.out.println("交提货日期变化");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        update($approveRule);
end
rule r16
    when
        $approveRule:ContractApproveBizInfoDTO(customerChanged ==true)
    then
        System.out.println("客户主体变化（同集团）;");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化（同集团）;"));
        update($approveRule);
end
rule r17
    when
        $approveRule:ContractApproveBizInfoDTO(customerGroupChanged ==true)
    then
        System.out.println("客户主体变化（不同集团）;");
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化（不同集团）;"));
        update($approveRule);
end
