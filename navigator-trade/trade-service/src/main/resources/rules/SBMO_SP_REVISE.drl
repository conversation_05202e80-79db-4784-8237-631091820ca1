package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_REVISE_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_REVISE_ABC2");
end

rule SBM_A_REVISE_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_ABC1");
        update($approveRule);
end

rule SBM_A_REVISE_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_REVISE_AB");
        update($approveRule);
end

rule SBM_A_REVISE_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_REVISE_A1");
        update($approveRule);
end

rule SBM_A_REVISE_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费变更;"));
        System.out.println("SBM_A_REVISE_A2");
        update($approveRule);
end

rule SBM_A_REVISE_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差变更;"));
        System.out.println("SBM_A_REVISE_A3");
        update($approveRule);
end

rule SBM_A_REVISE_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴变更;"));
        System.out.println("SBM_A_REVISE_A4");
        update($approveRule);
end

rule SBM_A_REVISE_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPriceChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴变更;"));
        System.out.println("SBM_A_REVISE_A5");
        update($approveRule);
end

rule SBM_A_REVISE_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && contract2To4 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差合同变更为基差暂定价合同;"));
        System.out.println("SBM_A_REVISE_A6");
        update($approveRule);
end

rule SBM_A_REVISE_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && customerGroupChanged ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("客户主体变化;"));
        System.out.println("SBM_A_REVISE_A7");
        update($approveRule);
end

rule SBM_A_REVISE_A8
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==1)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A8");
        update($approveRule);
end

rule SBM_A_REVISE_A9
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && deliveryEndTimeChanged ==true && deliveryLong ==false &&contractType ==2)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交提货日期变化;"));
        System.out.println("SBM_A_REVISE_A9");
        update($approveRule);
end