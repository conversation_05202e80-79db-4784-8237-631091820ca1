package rules

import com.navigator.activiti.drools.ApproveRule

lock-on-active true

//规则一：当合同类型为1，买方类型为1,contractPrice小于600000
rule approve_rule_11
    when
        $approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice < 600000)
        //$approveRule:ApproveRule(contractType == 1 && buyerType == 1)
    then
        $approveRule.setApproveType(1);
        System.out.println("规则1匹配");
end

//规则二：当合同类型为2，买房类型为1
rule approve_rule_21
    when
        $approveRule:ApproveRule(contractType == 1 && buyerType == 2)
    then
        $approveRule.setApproveType(111111);
        System.out.println("R2:规则approve_rule_2匹配");
end

//规则三：当合同类型为1，买房类型为2
rule approve_rule_31
    when
        $approveRule:ApproveRule(contractType == 2 && buyerType == 1)
    then
        $approveRule.setApproveType(3);
        //System.out.println("规则3匹配");
end

//规则四：当合同类型为2，买房类型为2
rule approve_rule_41
    when
        $approveRule:ApproveRule(contractType == 2 && buyerType == 2)
    then
        $approveRule.setApproveType(4);
        System.out.println("R2:规则approve_rule_4匹配");
end

//规则五：当合同价格大于600000并且合同价格小于900000
rule approve_rule_51
    when
        $approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice >= 600000 && contractPrice < 900000)
    then
        $approveRule.setApproveType(5);
        System.out.println("规则5匹配");
end

rule approve_rule_61
    when
        $approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice >= 900000)
    then
        $approveRule.setApproveType(6);
        System.out.println("规则6匹配");
end



