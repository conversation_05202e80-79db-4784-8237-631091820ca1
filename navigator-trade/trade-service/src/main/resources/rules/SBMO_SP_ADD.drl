package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true

rule SBM_A_ADD_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleResult(1015);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        System.out.println("SBM_A_ADD_ABC2");
        update($approveRule);
end

rule SBM_A_ADD_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_ABC1");
        update($approveRule);
end

rule SBM_A_ADD_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_ADD_AB");
        update($approveRule);
end

rule SBM_A_ADD_A1
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && proteinDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差高于触发阈值;"));
        }else{
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("蛋白价差低于触发阈值;"));
        }
        System.out.println("SBM_A_ADD_A1");
        update($approveRule);
end

rule SBM_A_ADD_A2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && transportPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("运费不为0;"));
        System.out.println("SBM_A_ADD_A2");
        update($approveRule);
end

rule SBM_A_ADD_A3
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && refineDiffPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("精炼价差不为0;"));
        System.out.println("SBM_A_ADD_A3");
        update($approveRule);
end

rule SBM_A_ADD_A4
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && businessPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("商务补贴不为0;"));
        System.out.println("SBM_A_ADD_A4");
        update($approveRule);
end

rule SBM_A_ADD_A5
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && otherPrice !=0 && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("其他补贴不为0;"));
        System.out.println("SBM_A_ADD_A5");
        update($approveRule);
end

rule SBM_A_ADD_A6
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && lowExtraPrice ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        if($approveRule.getSalesType() == 1){
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段高于阈值;"));
        }else {
            $approveRule.setRuleMemo($approveRule.getRuleMemo().append("基差价格字段低于阈值;"));
        }
        System.out.println("SBM_A_ADD_A6");
        update($approveRule);
end

rule SBM_A_ADD_A7
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount
        && newContract3 ==true && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同类型为暂定价合同;"));
        System.out.println("SBM_A_ADD_A7");
        update($approveRule);
end

