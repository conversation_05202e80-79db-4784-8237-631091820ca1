package rules

import com.navigator.common.dto.ContractApproveBizInfoDTO;

lock-on-active true
rule SBM_A_WASHOUT_INIT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount >0)
    then
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println("========================================");
        System.out.println($approveRule.getTotalAmount());
        System.out.println($approveRule.isDeliveryLong());
        update($approveRule);
end

rule SBM_A_WASHOUT_ABC1
    when
        $approveRule:ContractApproveBizInfoDTO(deliveryLong ==true)
    then
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("交期大于等于#DELIVERY_DUE_MONTH#;"));
        $approveRule.setRuleResult(1015);
        update($approveRule);
        System.out.println("SBM_A_WASHOUT_ABC2");
end

rule SBM_A_WASHOUT_ABC2
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount > maxTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1014);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("合同金额大于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_ABC1");
        update($approveRule);
end

rule SBM_A_WASHOUT_AB
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= maxTotalAmount && totalAmount > minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1013);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("金额大于#MIN_AMOUNT#且小于等于#MAX_AMOUNT#;"));
        System.out.println("SBM_A_WASHOUT_AB");
        update($approveRule);
end

rule SBM_A_WASHOUT
    when
        $approveRule:ContractApproveBizInfoDTO(totalAmount <= minTotalAmount && deliveryLong ==false)
    then
        $approveRule.setRuleResult(1012);
        $approveRule.setRuleMemo($approveRule.getRuleMemo().append("业务类型为解约定赔;"));
        System.out.println("SBM_A_WASHOUT");
        update($approveRule);
end