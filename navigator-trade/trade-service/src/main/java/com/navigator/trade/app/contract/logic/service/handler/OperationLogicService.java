package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <p>
 * 合同基本操作的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
public interface OperationLogicService {


    /**
     * 暂定价合同定价（生成定价单）
     *
     * @param confirmPriceDTO
     * @return
     */
    boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO);

    /**
     * 作废合同
     *
     * @param contractBaseDTO
     * @return
     */
    List<TTQueryVO> invalidContract(ContractBaseDTO contractBaseDTO);

    /**
     * tt作废合同
     *
     * @param contractBaseDTO
     * @return
     */
    boolean invalidContractByTT(ContractBaseDTO contractBaseDTO);


    /**
     * 补充合同信息
     *
     * @param contractBaseDTO
     * @return
     */
    boolean fillContract(ContractBaseDTO contractBaseDTO);

    /**
     * 审批通过更新合同 → 更新合同的同步
     *
     * @param contractModifyDTO
     */
    void updateContractByApproved(ContractModifyDTO contractModifyDTO);

    /**
     * 根据合同id关闭尾数
     *
     * @param contractId 合同id
     * @param triggerSys 触发系统
     * @return
     */
    Boolean closeTailNumByContractId(Integer contractId, Integer triggerSys);

    /**
     * 批量关闭尾数
     *
     * @param contractIds 合同id集合
     * @param triggerSys  触发系统
     * @return
     */
    String batchCloseTailNum(List<Integer> contractIds, Integer triggerSys);

    /**
     * 取消尾量关闭
     *
     * @param contractId 合同id
     * @return
     */
    Boolean cancelCloseTailNumByContractId(Integer contractId);

    /**
     * 批量取消关闭尾数
     *
     * @param contractIds 合同id集合
     * @return
     */
    String batchCancelCloseTailNum(List<Integer> contractIds);

    List<OperationDetailEntity> getCloseTailNumRecord(String contractCode);

    /**
     * 根据账套ID和合同编号关闭合同
     *
     * @param siteId       账套ID
     * @param contractCode 合同编号
     * @return
     */
    Boolean closeContractBySiteIdAndContractCode(Integer siteId, String contractCode);
}
