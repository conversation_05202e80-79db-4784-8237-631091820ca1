package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ExchangeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BusinessUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CreateLogicService;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓单注销的Logic 逻辑处理|直接处理仓单合同创建|现货合同创建|采销日志差异
 *
 * <AUTHOR>
 * @date 20240715
 */
@Slf4j
@Service
public class CreateLogicServiceImpl implements CreateLogicService {

    /**
     * 生成合同编号
     */
    @Resource
    private SequenceUtil sequenceUtil;
    /**
     * 跨域获取配置-TODO 抽出来到remote里面去 ContractLogicRemote
     */
    @Autowired
    private CustomerFacade customerFacade;

    @Autowired
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;

    @Autowired
    private CustomerDetailFacade customerDetailFacade;

    @Autowired
    private SiteFacade siteFacade;

    @Autowired
    private SkuFacade skuFacade;

    @Autowired
    private TradingConfigFacade tradingConfigFacade;
    /**
     * 合同域写服务处理
     */
    @Autowired
    private ContractDomainService contractDomainService;
    /**
     * 合同域读服务处理
     */
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    /**
     * 合同域通用逻辑处理
     */
    @Autowired
    private CommonLogicService commonLogicService;

    /**
     * 需要查询一个TT用来写日志
     */
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    @Autowired
    private TradeDomainRemoteService tradeDomainRemoteService;


    /**
     * 补充合同的基本信息 现货|仓单|采销
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildBaseInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 拷贝基本信息
        BeanUtils.copyProperties(contractCreateDTO, contractEntity);
        // 合同性质
        if (BuCodeEnum.WT.getValue().equals(contractCreateDTO.getBuCode())) {
            contractEntity.setContractNature(ContractNatureEnum.WAREHOUSE_TRADE.getValue());
        } else {
            contractEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        }
        // 仓单合同的编号特殊处理 迁移到TT实现
        // 补充基本信息 -- 合同编号
        contractEntity.setUuid(IdUtil.simpleUUID())
                .setLinkinageCode(contractCreateDTO.getContractCode())
                .setRepeatContractCode(contractCreateDTO.getContractCode())
                .setContractType(contractCreateDTO.getContractType())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setWriteOffStatus(ContractWriteOffStatusEnum.NOT_WRITEOFF.getValue())
                .setPriceEndTime(contractCreateDTO.getPriceEndTime())
                .setPriceEndType(contractCreateDTO.getPriceEndType())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setCreateBatch(null);

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Set group_id if provided in contractCreateDTO
        if (contractCreateDTO.getGroupId() != null) {
            contractEntity.setGroupId(contractCreateDTO.getGroupId());
        }
        // For buy-back operations, inherit group_id from parent contract
        else if (contractCreateDTO.getParentId() != null && contractCreateDTO.getParentId() > 0) {
            ContractEntity parentContract = contractQueryDomainService.getBasicContractById(contractCreateDTO.getParentId());
            if (parentContract != null && parentContract.getGroupId() != null) {
                contractEntity.setGroupId(parentContract.getGroupId());
            }
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
        // 销售仓单特有的 交易类型是线上的|交易平台的，并且 仓单销售|采购合同合同有直接生效的
        if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())
                && (WarrantTradeTypeEnum.ONLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())
                || WarrantTradeTypeEnum.ONLINE_TRADE_PLATFORM.getValue().equals(contractEntity.getWarrantTradeType()))) {
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
        }
    }

    /**
     * 补充业务信息，主要是从客户配置域获取合同创建的条件数据
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildBizInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {

        int isLdcFrame = DisableStatusEnum.ENABLE.getValue();
        int signatureType = DisableStatusEnum.DISABLE.getValue();
        int needOriginalPaper = DisableStatusEnum.DISABLE.getValue();
        // 主体id
        Integer customerId = contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue() ? contractCreateDTO.getCustomerId() : contractCreateDTO.getSupplierId();
        // 客户信息
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        commonLogicService.updateCustomerInfo(contractEntity.getCustomerId(),
                contractEntity.getSupplierId(),
                contractEntity.getCustomerId(),
                contractEntity.getSupplierId(),
                contractEntity);
        if (ObjectUtil.isNotEmpty(customerDTO)) {
            // 判断是否需要正本
            needOriginalPaper = customerDTO.getOriginalPaper();
            // 签章类型:是否使用易企签
            signatureType = customerDTO.getUseYqq();
        }
        // add by zengshl
        if (ObjectUtil.isNotEmpty(contractCreateDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(contractCreateDTO.getSiteCode());
            contractEntity.setBelongCustomerId(siteEntity.getBelongCustomerId());
            contractEntity.setDeliveryFactoryCode(siteEntity.getFactoryCode());
            contractEntity.setDeliveryFactory(siteEntity.getFactoryCode());
            contractEntity.setDeliveryFactoryName(siteEntity.getFactoryName());
            contractEntity.setCompanyId(siteEntity.getCompanyId());
            contractEntity.setCompanyName(siteEntity.getCompanyName());
        }
        if (ObjectUtil.isNotEmpty(contractCreateDTO.getGoodsId())) {
            SkuEntity skuEntity = skuFacade.getSkuById(contractCreateDTO.getGoodsId());
            contractEntity.setGoodsCategoryId(skuEntity.getCategory2());
            contractEntity.setGoodsPackageId(skuEntity.getPackageId());
            contractEntity.setGoodsSpecId(skuEntity.getSpecId());
            contractEntity.setGoodsName(skuEntity.getFullName());
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 start
            if (StringUtils.isNotBlank(contractCreateDTO.getCommodityName())) {
                contractEntity.setCommodityName(contractCreateDTO.getCommodityName());
            } else {
                List<String> nickNameList = StringUtils.isNotBlank(skuEntity.getNickName()) ? StringUtil.split(skuEntity.getNickName(), "\\$\\$") : new ArrayList<>();
                String nickName = CollectionUtils.isEmpty(nickNameList) ? skuEntity.getFullName() : nickNameList.get(0);
                contractEntity.setCommodityName(nickName);
            }
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 end
            contractCreateDTO.setGoodsCategoryId(skuEntity.getCategory2());
        }

        //根据客户和品类查询客户配置 二级品类
        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
        customerOriginalPaperDTO
                .setCustomerId(contractCreateDTO.getCustomerId())
                .setCategory2(String.valueOf(contractCreateDTO.getCategory2()))
                .setCategory3(String.valueOf(contractCreateDTO.getCategory3()))
                .setCompanyId(contractCreateDTO.getCompanyId())
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setSaleType(contractCreateDTO.getSalesType());
        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
        if (customerOriginalPaperEntity != null) {
            // 模板类型
            isLdcFrame = customerOriginalPaperEntity.getLdcFrame();
        }

        // 初始化合同转月、反点价次数
        processorCreateTransferTimes(contractCreateDTO, contractEntity);

        contractEntity
                .setNeedOriginalPaper(needOriginalPaper)
                .setLdcFrame(isLdcFrame)
                .setSignatureType(String.valueOf(signatureType));

        // 合同数量处理
        if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            contractEntity.setTotalPriceNum(contractEntity.getContractNum());
        }
        // 签订总金额
        contractEntity.setOrderAmount(contractEntity.getTotalAmount());
        // 可提数量
        contractEntity.setApplyDeliveryNum(BigDecimal.ZERO);
        // 已注销量
        contractEntity.setWarrantCancelCount(BigDecimal.ZERO);
        // TODO 交易所编码
        TradingConfigVO tradingConfigVO = tradingConfigFacade.getDomainTypeByCategoryCode(contractEntity.getFutureCode());
        contractEntity.setExchangeCode(null == tradingConfigVO ? ExchangeEnum.DCE.getValue() : tradingConfigVO.getExchange());

    }

    /**
     * 创建合同
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 查询是否存在相同的合同编号
        List<ContractEntity> oldList = contractQueryDomainService.getByContractCode(contractEntity.getContractCode());
        oldList.forEach(oldContractEntity -> {
            try {
                contractDomainService.updateContractById(oldContractEntity
                        .setRepeatContractCode(oldContractEntity.getRepeatContractCode() + "-" + oldContractEntity.getId())
                        .setIsDeleted(IsDeletedEnum.DELETED.getValue()));
            } catch (Exception e) {
                throw new BusinessException(ResultCodeEnum.FAILURE);
            }
        });
        try {
            contractDomainService.saveContract(contractEntity);
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
    }

    /**
     * 创建结构化合同
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAdditionalInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 合同类型 = 结构化合同 并且 采销类型 = 销售
        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue() &&
                contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            ContractStructureEntity contractStructureEntity = BeanConvertUtils.convert(ContractStructureEntity.class, contractCreateDTO.getContractStructureDTO());
            contractStructureEntity.setContractId(contractEntity.getId())
                    .setContractCode(contractEntity.getContractCode())
                    .setPriceStatus(ContractStructurePricingStatusEnum.INVALID.getValue())
                    .setTotalNum(contractEntity.getContractNum())
                    .setCreatedBy(contractEntity.getCreatedBy())
                    .setUpdatedBy(contractEntity.getUpdatedBy());
            contractDomainService.saveContractStructure(contractStructureEntity);
        }
    }

    /**
     * 创建后操作
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterCreateProcess(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {

        // 有些仓单合同是直接生效，那么需要进行二次备份下
        if (ContractStatusEnum.EFFECTIVE.getValue() == contractEntity.getStatus()) {
            contractEntity.setVersion(0);
            contractDomainService.updateContractById(contractEntity, contractCreateDTO.getTtTradeType().toString(), contractCreateDTO.getTtCode());
        }
        // 记录日志
        LogBizCodeEnum bizCodeEnum = null;
        // 现货销售的日志类型
        if (contractCreateDTO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            if (contractCreateDTO.getActionSource().equals(ContractActionEnum.NEW.getActionValue())) {
                bizCodeEnum = LogBizCodeEnum.NEW_SALES_CONTRACT;
            } else if (contractCreateDTO.getActionSource().equals(ContractActionEnum.STRUCTURE_PRICING.getActionValue())) {
                bizCodeEnum = LogBizCodeEnum.NEW_SALES_STRUCTURE_PRICING;
            } else {
                bizCodeEnum = LogBizCodeEnum.CHANGE_SALES_CONTRACT;
            }
            // 仓单
            if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
                bizCodeEnum = LogBizCodeEnum.NEW_WARRANT_SALES_CONTRACT;
            }
        }
        // 仓单|现货采购的日志类型
        if (contractCreateDTO.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            if (contractCreateDTO.getActionSource().equals(ContractActionEnum.NEW.getActionValue())) {
                bizCodeEnum = LogBizCodeEnum.NEW_PURCHASE_CONTRACT;
            } else if (contractCreateDTO.getActionSource().equals(ContractActionEnum.BUYBACK.getActionValue())) {
                bizCodeEnum = LogBizCodeEnum.SALES_CONTRACT_BUYBACK;
            } else {
                bizCodeEnum = LogBizCodeEnum.CHANGE_PURCHASE_CONTRACT;
            }
            // 仓单
            if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
                bizCodeEnum = LogBizCodeEnum.NEW_WARRANT_PURCHASE_CONTRACT;
            }
        }
        TradeTicketQO qo = new TradeTicketQO();
        qo.setTtId(contractCreateDTO.getCurrentTtId());
        TradeTicketEntity tradeTicketEntity = ttQueryLogicService.fetchTradeTicketEntity(qo);
        tradeTicketEntity.setContractId(contractEntity.getId());
        String jsonData = JSONUtil.toJsonStr(contractEntity);
        tradeDomainRemoteService.addContractOperationLog(tradeTicketEntity, bizCodeEnum, jsonData, SystemEnum.MAGELLAN.getValue());
    }

    /**
     * 处理转月次数
     *
     * @param contractCreateDTO 合同创建DTO
     * @param contractEntity    合同实体
     */
    private void processorCreateTransferTimes(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        int totalTransferTimes = 1;
        int totalReversePriceTimes = 0;

        // 是否是超远期合同
        int isOverForward = ObjectUtil.isNotEmpty(contractEntity.getDeliveryEndTime()) &&
                BusinessUtil.isOverForwardContract(contractEntity.getDomainCode(), contractEntity.getDeliveryEndTime())
                && contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? 1 : 0;

        //根据客户和品类查询客户配置
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO
                .setCategory2(String.valueOf(contractCreateDTO.getCategory2()))
                .setCategory3(String.valueOf(contractCreateDTO.getCategory3()))
                .setCustomerId(contractCreateDTO.getCustomerId());
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

        if (CollectionUtils.isNotEmpty(customerDetailEntityList)) {
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            // 转月次数
            totalTransferTimes = customerDetailEntity.getIsWhiteList() == 1 ? 4 : (isOverForward == 1 ? 2 : 1);

            if (customerDetailEntity.getIsReversePrice() == 1) {
                totalReversePriceTimes = 1;
            }
        } else {
            // 没有客户配置，默认转月次数
            totalTransferTimes = isOverForward == 1 ? 2 : 1;
        }

        // 初始化合同转月、反点价次数
        contractEntity.setTotalTransferTimes(totalTransferTimes)
                .setAbleTransferTimes(totalTransferTimes)
                .setIsOverForward(isOverForward)
                .setTransferredTimes(0)
                .setTotalReversePriceTimes(totalReversePriceTimes)
                .setAbleReversePriceTimes(totalReversePriceTimes)
                .setReversedPriceTimes(0);
    }


}
