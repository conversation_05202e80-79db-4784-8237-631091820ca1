package com.navigator.trade.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.ContractBackUpDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractRelativeDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 合同表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IContractQueryService {
    /**
     * 新增合同生成合同编号
     *
     * @param salesName       买/卖方主体简称
     * @param salesType       销售合同类型
     * @param goodsCategoryId 商品品类
     * @return
     */
    String genNewContractCode(String salesName, Integer salesType, Integer goodsCategoryId);

    /**
     * 新增结构化定价生成合同编号
     *
     * @return
     */
    String genStructureContractCode(String companyName);

    /**
     * 生成子合同编号
     *
     * @param contractCode 父合同编号
     * @param salesType    销售合同类型
     * @return
     */
    String genSonContractCode(String contractCode, Integer salesType);

    /**
     * 根据条件获取合同
     *
     * @param queryDTO
     * @return
     */
    Result queryContract(QueryDTO<ContractQO> queryDTO);

    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @return
     */
    Result queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 根据唯一编号查询合同
     *
     * @param uuid
     * @return
     */
    ContractEntity queryContractByUuid(String uuid);

    /**
     * 根据id查询合同
     *
     * @param id
     * @return
     */
    ContractEntity getContractById(Integer id);

    /**
     * 根据合同id查询基本合同信息(不调用lkg)
     *
     * @param id
     * @return
     */
    ContractEntity getBasicContractById(Integer id);

    ContractEntity getBasicContractByCode(String code);

    ContractEntity getContractEntityByCodeLike(String code);

    ContractDetailInfoDTO getContractDetailInfoDTO(Integer id);

    /**
     * 根据id更新合同
     *
     * @param contractEntity
     * @return
     */
    Integer updateContract(ContractEntity contractEntity);

    /**
     * 根据合同Id查询合同明细
     *
     * @param contractId
     * @return
     */
    Result<ContractDetailVO> getContractDetailById(String contractId);

    /**
     * 根据合同Id查询合同明细(不调用lkg)
     *
     * @param contractId
     * @return
     */
    Result<ContractDetailVO> getBasicContractDetailById(String contractId);

    /**
     * 根据合同Code查询合同明细
     *
     * @param contractCode
     * @return
     */
    Result<ContractDetailVO> getContractDetailByCode(String contractCode);

    /**
     * 根据合同Code查询合同Id
     *
     * @param contractCode
     * @return
     */
    Integer getContractIdByCode(String contractCode);

    /**
     * 根据合同Code查询合同信息
     *
     * @param contractCode
     * @return
     */
    ContractEntity getContractByCode(String contractCode);

    /**
     * 根据合同Id获取电子合同
     *
     * @param contractId
     * @return
     */
    Result getContractPdfs(String contractId);

    // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 Start

    /**
     * 根据合同id 查询合同加权品均价
     *
     * @param contractId
     * @return
     */
    BigDecimal contractAvgPrice(Integer contractId);
    // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 end

    /**
     * 查询合同是否可签章
     *
     * @param contractId 合同id
     * @return
     */
    boolean canSign(String contractId);

    /**
     * 根据合同Id获取合同含税单价详情
     *
     * @param contractId
     * @return
     */
    Result getContractUnitPriceDetail(String contractId);


    /**
     * 查询客户的期货合约数据
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO);


    /**
     * 分组查询期货合约
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO);

    /**
     * 根据合约号获取当前客户可以分配的合同(分页查询)
     *
     * @param queryDTO
     * @return
     */
    Result pageContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 根据合约号获取当前客户可以分配的合同(不分页)
     *
     * @param queryDTO
     * @return
     */
    List<ContractVO> listContractsByDomainCode(QueryContractDTO queryDTO);

    List<ContractEntity> queryContractsByDomainCodeList(@RequestBody QueryContractDTO queryContractDTO);

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    Result futureContracts(QueryDTO<ContractFuturesDTO> queryDTO);

    /**
     * 查询合同拆分记录
     *
     * @param contractModifyDTO
     * @return
     */
    List<ContractModifyVO> getContractModifyLog(ContractModifyDTO contractModifyDTO);

    /**
     * 获取合同变更所需要的数量
     *
     * @param contractId
     * @return
     */
    ContractModifyNumVO getContractModifyNumInfo(Integer contractId);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return ConfirmPriceVO
     */
    List<ConfirmPriceVO> getConfirmPriceList(Integer contractId);

    List<PriceDealDetailVO> getContractStructurePriceList(Integer contractId);

    /**
     * 根据条件查询合同
     *
     * @param customerId       客户id
     * @param goodsCategoryId  商品品类id
     * @param domainCode       主力合约
     * @param contractTypeList 合同类型
     * @return
     */
    List<ContractEntity> getContractList(Integer customerId, Integer goodsCategoryId, String domainCode, List<Integer> contractTypeList);

    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 Start
    List<ContractEntity> getContractList(Integer customerId, List<Integer> category3List, List<Integer> contractStatusList, List<Integer> contractTypeList);
    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 End

    List<ContractEntity> getContractList(List<String> contractCodeList, String startDateTime, String endDateTime);

    boolean judgeCanModifyGoods(Integer contractId);

    /**
     * 获取每日合同数据
     *
     * @param salesType     销售类型
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return
     */
    List<ContractEntity> getDailyContractList(Integer salesType, String startDateTime, String endDateTime);

    boolean prePareToContract(ContractEntity contractEntity);

    /**
     * 根据父合同id获取合同信息
     *
     * @param pid
     * @return
     */
    List<ContractEntity> getContractByPid(Integer pid);

    Result updateUnitPrice(String contractCode, BigDecimal unitPrice);

    Integer updateAndBackUpContract(ContractBackUpDTO contractBackUpDTO);

    Result updateContractStatus(MultipartFile uploadFile, Integer status);

    List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO);

    List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO);

    List<ContractEntity> getContractListByIds(List<Integer> contractIdList);

    List<ContractRelativeDTO> getContractTraceList(Integer contractId);
}
