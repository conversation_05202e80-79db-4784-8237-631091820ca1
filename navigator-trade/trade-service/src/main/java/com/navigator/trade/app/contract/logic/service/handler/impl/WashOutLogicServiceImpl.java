package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.future.enums.AllocateStatusEnum;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.app.contract.logic.service.handler.WashOutLogicService;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同解约索赔业务逻辑处理 逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WashOutLogicServiceImpl implements WashOutLogicService {

    @Autowired
    private RedisUtil redisUtil;
    /**
     * 合同域处理
     */
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private CommonLogicService commonLogicService;
    /**
     * 域内服务调用
     */
    @Autowired
    private StructureLogicService structureLogicService;
    @Autowired
    private PriceAllocateFacade priceAllocateFacade;

    /**
     * 校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 解约定赔dto
     */
    @Override
    public void washOutContractCheck(ContractEntity contractEntity, ContractWashOutDTO contractBuyBackDTO) {
        // 校验合同是否存在
        if (contractEntity == null) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 校验客户状态是否可用
        if (!commonLogicService.isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 校验合同状态是否为生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }

        // 校验是否已洗单
        List<ContractEntity> washOutList = contractQueryDomainService.getWashOutList(contractEntity.getId());
        if (CollectionUtil.isNotEmpty(washOutList)) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_WASH_OUT_EXCEPTION);
        }

        // 校验头寸是否已分配
        if (priceAllocateFacade.getNotAllocateByContractId(contractEntity.getId())) {
            throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_4);
        }

        // 根据合同类型处理不同逻辑
        switch (ContractTypeEnum.getByValue(contractEntity.getContractType())) {
            case YI_KOU_JIA:
                // 校验洗单数量是否大于未开单量
                if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum().subtract(contractEntity.getTotalBillNum()))) {
                    throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_6);
                }
                break;
            case JI_CHA:
                // 校验解约定赔数量是否大于未定价量
                if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()))) {
                    throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_5);
                }

                // 校验合同结构
                VerifyContractStructureNumDTO verifyDTO = new VerifyContractStructureNumDTO()
                        .setCustomerId(contractEntity.getCustomerId())
                        .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                        .setDomainCode(contractEntity.getDomainCode())
                        .setContractNum(contractEntity.getContractNum())
                        .setCompanyId(contractEntity.getCompanyId());
                Boolean flag = structureLogicService.verifyContractStructureNum(verifyDTO);
                if (flag) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
                }
                break;
            case JI_CHA_ZAN_DING_JIA:
                BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();

                // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 Start
                // BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
                // String key = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
                // Object object = redisUtil.get(key);
                // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 End

                // 校验待审的分配
                List<PriceAllocateEntity> priceAllocateEntityList = priceAllocateFacade.getByContractId(contractEntity.getId());
                List<PriceAllocateEntity> waitingAuditList = priceAllocateEntityList.stream()
                        .filter(i -> AllocateStatusEnum.WAIT_AUDIT.getValue() == i.getStatus())
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(waitingAuditList)) {
                    throw new BusinessException(ResultCodeEnum.ALLOCATE_EXIST);
                }

                // 校验定价量和提货量的关系
                if (BigDecimalUtil.isEqual(totalPriceNum, contractEntity.getContractNum())) {
                    //  1）当定价完成，可解约定赔量=总量-已开单；
                    if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum().subtract(contractEntity.getTotalBillNum()))) {
                        throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_6);
                    }
                } else {
                    //  2）未定价完成，可解约定赔量=总量- MAX（已定价量；已开单）
                    BigDecimal maxNum = BigDecimalUtil.max(totalPriceNum, contractEntity.getTotalBillNum());
                    if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum().subtract(maxNum))) {
                        if (BigDecimalUtil.isEqual(maxNum, totalPriceNum)) {
                            throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_5);
                        }

                        if (BigDecimalUtil.isEqual(maxNum, contractEntity.getTotalBillNum())) {
                            throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_6);
                        }
                    }
                }
                break;
            case ZAN_DING_JIA:
                // 暂定价合同不能洗单
                throw new BusinessException(ResultCodeEnum.ZAN_DING_JIA_CANNOT);
            default:
                // 处理其他合同类型或无类型的默认情况
                break;
        }

        // 校验是否可提货数量
        if (contractEntity.getApplyDeliveryNum() != null && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 校验尾量关闭
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }
    }

    /**
     * 处理父合同信息
     *
     * @param contractEntity     父合同
     * @param contractWashOutDTO 回购dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateFatherContract(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO) {
        // 解约定赔数量
        BigDecimal contractNum = contractEntity.getContractNum().subtract(contractWashOutDTO.getWashOutNum());
        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
        // 解约定赔导致的原合同全部定价 -- 在TT域处理或者调用
        if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && BigDecimalUtil.isEqual(contractNum, contractEntity.getTotalPriceNum())
                && BigDecimalUtil.isGreaterThanZero(contractNum)) {
            // 原合同需要自动计算加权平均价 TODO TT 域提供查询 TTQueryLogic TTLogic
//            List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
//            BigDecimal totalPrice = BigDecimal.ZERO;
//            BigDecimal totalNum = BigDecimal.ZERO;
//            for (TTPriceEntity ttPriceEntity : confirmPriceList) {
//                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
//                totalNum = totalNum.add(ttPriceEntity.getNum());
//            }
//
//            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
//                // 加权平均价
//                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);
//                // 新期货价格  TT 域提供更新接口
//                 contractPriceService.updateContractForwardPrice(contractEntity, averagePrice);
//            }
        }
        // 合同状态改为修改中
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        // 更新合同信息
        contractDomainService.updateContractById(contractEntity);

    }

    @Override
    public void recordOperationLog(ContractWashOutDTO contractWashOutDTO, ContractEntity contractEntity) {
        // 记录操作记录 采购|销售
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            commonLogicService.addContractOperationLog(contractEntity,
                    LogBizCodeEnum.PURCHASE_CONTRACT_WASH_OUT, JSONUtil.toJsonStr(contractWashOutDTO),
                    SystemEnum.MAGELLAN.getValue());
        } else {
            commonLogicService.addContractOperationLog(contractEntity,
                    LogBizCodeEnum.SALES_CONTRACT_WASH_OUT, JSONUtil.toJsonStr(contractWashOutDTO),
                    SystemEnum.MAGELLAN.getValue());
        }
    }

}
