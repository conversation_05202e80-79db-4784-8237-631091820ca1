package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.TradeTicketMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.entity.TradeTicketVOEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Dao
public class TradeTicketDao extends BaseDaoImpl<TradeTicketMapper, TradeTicketEntity> {
    /**
     * 根据ttCode查询TT
     *
     * @return TradeTicketEntity
     */
    public TradeTicketEntity getTradeTicketEntityByCode(String code) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getCode, code)
                .orderByDesc(TradeTicketEntity::getId)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 根据ttId查询TT
     *
     * @return TradeTicketEntity
     */
    public TradeTicketEntity getTradeTicketEntityById(Integer id) {
        return getOne(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getId, id));
    }

    /**
     * 根据ttId,userId查询TT
     *
     * @return TradeTicketEntity
     */
    public TradeTicketEntity getTradeTicketEntity(Integer ttId, String userId) {
        return getOne(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getId, ttId));
        //.eq(TradeTicketEntity::getCreatedBy, userId)
        //.eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getName()));
    }


    /**
     * 根据code修改tt状态
     */
    public int updateStatusByCode(int status, String code) {
        return update(Wrappers.<TradeTicketEntity>lambdaUpdate()
                .set(TradeTicketEntity::getStatus, status)
                .eq(TradeTicketEntity::getCode, code)) ? 1 : 0;
    }


    /**
     * 根据ttId 删除
     */
    public void deleteById(Integer ttId) {
        log.info("check_code_question ttId:{} deleteById  ", ttId);
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate()
                .eq(TradeTicketEntity::getId, ttId)
                .set(TradeTicketEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));
    }


    /**
     * 根据TTQueryDTO分页查询TradeTicketEntity
     *
     * @return IPage<TradeTicketEntity>
     */
    public IPage<TradeTicketEntity> queryPageByTTQueryDTO(Page<TradeTicketEntity> page, TTQueryDTO ttQueryDTO) {
        ttQueryDTO.setContractCode(StringUtils.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode())
                .setBizCode(StringUtils.isNotBlank(ttQueryDTO.getBizCode()) ? ttQueryDTO.getBizCode().trim() : ttQueryDTO.getBizCode());
        LambdaQueryWrapper<TradeTicketEntity> wrapper = Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(ttQueryDTO.getStatus()), TradeTicketEntity::getStatus, ttQueryDTO.getStatus())
                .like(StringUtils.isNotBlank(ttQueryDTO.getContractCode()), TradeTicketEntity::getContractCode, "%" + (StrUtil.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode()) + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getCode()), TradeTicketEntity::getCode, "%" + ttQueryDTO.getCode() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getType()), TradeTicketEntity::getType, ttQueryDTO.getType())
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(StringUtils.isNotBlank(ttQueryDTO.getCreateStartTime()), TradeTicketEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(ttQueryDTO.getCreateStartTime()))
                .le(StringUtils.isNotBlank(ttQueryDTO.getCreateEndTime()), TradeTicketEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(ttQueryDTO.getCreateEndTime()))
                .eq(ttQueryDTO.getSalesType() != null, TradeTicketEntity::getSalesType, ttQueryDTO.getSalesType())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCreateBy()), TradeTicketEntity::getCreatedBy, ttQueryDTO.getCreateBy())
//                        .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()) && ContractSalesTypeEnum.SALES.getValue() == ttQueryDTO.getSalesType(), TradeTicketEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
//                        .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()) && ContractSalesTypeEnum.PURCHASE.getValue() == ttQueryDTO.getSalesType(), TradeTicketEntity::getSupplierName, "%" + ttQueryDTO.getCustomerName() + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TradeTicketEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getSupplierName()), TradeTicketEntity::getSupplierName, "%" + ttQueryDTO.getSupplierName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TradeTicketEntity::getCategory2, ttQueryDTO.getGoodsCategoryId())
                .in(CollectionUtils.isNotEmpty(ttQueryDTO.getTradeType()), TradeTicketEntity::getTradeType, ttQueryDTO.getTradeType())
//                        .in(CollectionUtils.isNotEmpty(customerIdList), TradeTicketEntity::getBelongCustomerId, customerIdList)

                .orderByDesc(TradeTicketEntity::getUpdatedAt);
        if (StringUtils.isNotBlank(ttQueryDTO.getBizCode())) {
            wrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketEntity::getContractCode, "%" + ttQueryDTO.getBizCode().trim() + "%")
                    .or(StringUtils.isNotBlank(ttQueryDTO.getBizCode()))
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketEntity::getCode, "%" + ttQueryDTO.getBizCode().trim() + "%"));
        }
        IPage<TradeTicketEntity> iPage = page(page, wrapper);
        return iPage;


    }

    /**
     * 根据id修改tt状态
     */
    public void updateStatusById(Integer status, Integer approvalStatus, Integer ttId) {
        log.info("check_code_question ttId:{} updateStatusById   ", ttId);
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getId, ttId)
                .set(status != null, TradeTicketEntity::getStatus, status)
                .set(approvalStatus != null, TradeTicketEntity::getApprovalStatus, approvalStatus)
        );
    }

    public void updateOccupyStatusById(Integer ttId, boolean isOccupy) {
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getId, ttId)
                .set(TradeTicketEntity::getOccupyStatus, isOccupy ? 1 : 0)
        );
    }

    /**
     * 根据id修改tt状态
     */
    public void invalidTTById(int status, String invalidReason, Integer ttId) {
        log.info("check_code_question ttId:{} invalidTTById  ", ttId);
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getId, ttId)
                .set(TradeTicketEntity::getStatus, status)
                .set(TradeTicketEntity::getInvalidReason, invalidReason));
    }

    /**
     * 根据code修改tt状态
     */
    public void updateApprovalStatusByCode(Integer status, Integer approvalStatus, String ttCode) {
        log.info("check_code_question ttCode:{} invalidTTById   ", ttCode);
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getCode, ttCode)
                .set(status != null, TradeTicketEntity::getStatus, status)
                .set(approvalStatus != null, TradeTicketEntity::getApprovalStatus, approvalStatus)

        );
    }

    /**
     * 根据code修改tt状态
     */
    public void updateTTInfoByCode(Integer status, Integer approvalStatus, String ttCode, Integer approvalType) {
        log.info("check_code_question2 ttCode:{}  updateTTInfoByCode  approvalStatus:{},approvalType:{}", ttCode, approvalStatus, approvalType);
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getCode, ttCode)
                .set(status != null, TradeTicketEntity::getStatus, status)
                .set(approvalStatus != null, TradeTicketEntity::getApprovalStatus, approvalStatus)
                .set(approvalType != null, TradeTicketEntity::getApprovalType, approvalType)
        );
    }

    /**
     * 更新tt中的合同 Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    public int updateContractId(Integer ttId, Integer contractId) {
        int result = update(Wrappers.<TradeTicketEntity>lambdaUpdate()
                .set(TradeTicketEntity::getContractId, contractId)
                .eq(TradeTicketEntity::getId, ttId)) ? 1 : 0;
        log.info("check_code_question  updateTTInfoByCode  ttId:{} result:{}", ttId, result);
        return result;
    }

    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        int result = update(Wrappers.<TradeTicketEntity>lambdaUpdate()
                .set(TradeTicketEntity::getContractId, contractEntity.getId())
                .set(TradeTicketEntity::getContractCode, contractEntity.getContractCode())
                .set(TradeTicketEntity::getContractType, contractEntity.getContractType())
                .set(TradeTicketEntity::getSourceContractId, contractEntity.getParentId())
                .eq(TradeTicketEntity::getId, ttId)) ? 1 : 0;
        log.info("check_code_question  updateTTInfoByCode  ttId:{} result:{}", ttId, result);
        return result;
    }

    public List<TradeTicketEntity> getByContractId(Integer contractId) {
        return list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractId, contractId)
                .orderByDesc(TradeTicketEntity::getId));
    }

    public List<TradeTicketEntity> getCanModifyByContractId(Integer contractId) {
        return list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractId, contractId)
                .notIn(TradeTicketEntity::getType, Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()))
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ne(TradeTicketEntity::getStatus, TTStatusEnum.INVALID.getType())
                // BUGFIX：case-1002581 定价单含税单价错误-界面优化BUG合同详情展示保存TT Author: Mr 2024-05-13 Start
                .and(wrapper -> wrapper.isNull(TradeTicketEntity::getSourceType).or().ne(TradeTicketEntity::getSourceType, SubmitTypeEnum.SAVE.getValue()))
                // BUGFIX：case-1002581 定价单含税单价错误-界面优化BUG合同详情展示保存TT Author: Mr 2024-05-13 End
                .orderByDesc(TradeTicketEntity::getId));
    }

    public List<TradeTicketEntity> getBySignId(String signId) {
        return list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getSignId, signId)
                .orderByDesc(TradeTicketEntity::getId));
    }


    public List<TradeTicketEntity> getList(List<Integer> ttIdList, Integer salesType) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != salesType, TradeTicketEntity::getSalesType, salesType)
                .in(CollectionUtils.isNotEmpty(ttIdList), TradeTicketEntity::getId, ttIdList)
        );
        return list;
    }

    public TradeTicketEntity getTradeTicketEntityByContractId(Integer contractId) {
        return getOne(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractId, contractId));
    }

    public int updateSignInfo(Integer id, ContractSignEntity contractSign) {
        log.info("check_code_question2 ttCode:{}  updateSignInfo  id:{} ", contractSign.getTtCode(), id);
        int result = update(Wrappers.<TradeTicketEntity>lambdaUpdate()
                .set(TradeTicketEntity::getSignId, contractSign.getId())
                .set(TradeTicketEntity::getProtocolCode, contractSign.getProtocolCode())
//                .set(TradeTicketEntity::getContractId, contractSign.getContractId())
//                .set(TradeTicketEntity::getContractCode, contractSign.getContractCode())
                .eq(TradeTicketEntity::getId, id)) ? 1 : 0;
        return result;
    }

    public List<TradeTicketEntity> getTradeTicketEntityByContractCode(String contractCode) {
        return list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractCode, contractCode)
                .orderByAsc(TradeTicketEntity::getCreatedAt));
    }

    public List<TradeTicketEntity> getSplitList(String contractCode) {
        return this.list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .like(TradeTicketEntity::getContractCode, contractCode + "%")
                .in(TradeTicketEntity::getContractSource, Arrays.asList(ContractActionEnum.NEW.getActionValue(),
                        ContractActionEnum.REVISE_CUSTOMER.getActionValue(), ContractActionEnum.SPLIT.getActionValue(),
                        ContractActionEnum.SPLIT_CUSTOMER.getActionValue())));
    }

    public List<TradeTicketEntity> getReviseList(String contractCode) {
        return this.list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .like(TradeTicketEntity::getContractCode, contractCode + "%")
                .in(TradeTicketEntity::getContractSource, Arrays.asList(ContractActionEnum.REVISE.getActionValue()))
        );
    }

    public void hideTT(Integer ttId) {
        boolean update = update(Wrappers.<TradeTicketEntity>lambdaUpdate().eq(TradeTicketEntity::getId, ttId)
                .set(TradeTicketEntity::getStatus, TTStatusEnum.DONE.getType())
                .set(TradeTicketEntity::getApprovalStatus, TTApproveStatusEnum.WITHOUT_APPROVE.getValue())
                .set(TradeTicketEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
        );
        log.info("check_code_question  updateTTInfoByCode  ttId:{} update:{}", ttId, update);
    }

    public List<TradeTicketEntity> queryIncompleteTTByContractId(Integer contractId, String groupId) {
        LambdaQueryWrapper<TradeTicketEntity> wrapper = Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractId, contractId)
                .notIn(TradeTicketEntity::getType, Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()))
                .eq(TradeTicketEntity::getStatus, TTStatusEnum.APPROVING.getType())
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (StringUtils.isNotBlank(groupId)) {
            wrapper.or(i -> i
                    .ne(TradeTicketEntity::getStatus, TTStatusEnum.DONE.getType())
                    .eq(TradeTicketEntity::getGroupId, groupId)
            );
        }
        return this.list(wrapper);
    }

    public List<TradeTicketEntity> queryIncompleteTTBySourceContractId(Integer sourceContractId, List<String> groupIdList) {
        LambdaQueryWrapper<TradeTicketEntity> wrapper = Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getSourceContractId, sourceContractId)
                .eq(TradeTicketEntity::getStatus, TTStatusEnum.APPROVING.getType())
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            wrapper.or(i -> i
                    .ne(TradeTicketEntity::getStatus, TTStatusEnum.DONE.getType())
                    .in(CollectionUtils.isNotEmpty(groupIdList), TradeTicketEntity::getGroupId, groupIdList)
            );
        }
        return this.list(wrapper);
    }

    public List<TradeTicketEntity> queryTTBySourceContractId(Integer sourceContractId) {
        return this.list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getSourceContractId, sourceContractId)
        );
    }

    public TradeTicketEntity queryTTByTradeTypeAndContractId(Integer contractId, Integer tradeType) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(null != contractId, TradeTicketEntity::getContractId, contractId)
                .eq(null != tradeType, TradeTicketEntity::getTradeType, tradeType)
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    public TradeTicketEntity getByGroupId(String groupId, Integer id) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getGroupId, groupId)
                .ne(TradeTicketEntity::getId, id)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public TradeTicketEntity getWarrantByGroupId(String groupId, Integer id) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getGroupId, groupId)
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ne(TradeTicketEntity::getId, id)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<TradeTicketEntity> queryChangeLogByContractId(Integer contractId, String contractCode) {

        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractId, contractId)
                .orderByAsc(TradeTicketEntity::getId)
        );


        List<TradeTicketEntity> list1 = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getContractCode, contractCode)
                .eq(TradeTicketEntity::getType, TTTypeEnum.BUYBACK.getType())
                .orderByDesc(TradeTicketEntity::getId)
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));

        if (CollectionUtil.isNotEmpty(list1)) {
            list.addAll(list1);
        }

        List<TradeTicketEntity> list2 = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(TradeTicketEntity::getSourceContractId, contractId)
                .eq(TradeTicketEntity::getTradeType, ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue())
                .eq(TradeTicketEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        if (CollectionUtil.isNotEmpty(list2)) {
            list.addAll(list2);
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    public TradeTicketEntity getByContractId(Integer contractId, String groupId) {
        List<TradeTicketEntity> list = list(Wrappers.<TradeTicketEntity>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(groupId), TradeTicketEntity::getGroupId, groupId)
                .eq(ObjectUtil.isNotEmpty(contractId), TradeTicketEntity::getContractId, contractId)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
    /**
     * TT分页查询
     *
     * @param queryDTO 查询条件
     * @return TT列表
     */
    public List<TradeTicketVOEntity> queryTTPagedList(TTQueryDTO queryDTO) {
        return baseMapper.queryTTPagedList(queryDTO);
    }

    /**
     * TT总数查询
     *
     * @param queryDTO 查询条件
     * @return 记录总数
     */
    public Integer queryTTTotalCount(TTQueryDTO queryDTO) {
        return baseMapper.queryTTTotalCount(queryDTO);
    }
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end

}
