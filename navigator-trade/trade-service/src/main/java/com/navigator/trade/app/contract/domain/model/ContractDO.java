package com.navigator.trade.app.contract.domain.model;

import com.navigator.trade.pojo.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 合同域领域对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractDO {
    /**
     * 主合同
     */
    private ContractEntity contractEntity;

    /**
     * 子合同
     */
    private List<ContractEntity> childContractEntity;

    /**
     * 合同历史信息
     */
    private ContractHistoryEntity contractHistoryEntity;

    /**
     * 合同价格信息
     */
    private ContractPriceEntity contractPriceEntity;

    /**
     * 合同结构化信息
     */
    private ContractStructureEntity contractStructureEntity;

    /**
     * 合同权益变更信息
     */
    private ContractChangeEquityEntity contractChangeEquityEntity;
}
