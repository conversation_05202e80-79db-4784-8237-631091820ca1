package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.navigator.bisiness.enums.TTWriteOffActionEnum;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/20
 * @Version 1.0
 */

@Component
@Slf4j
public class TradeTicketDOConvert {
    @Autowired
    TradeTicketEntityConvert tradeTicketEntityConvert;

    @Autowired
    TTAddEntityConvert ttAddEntityConvert;

    @Autowired
    TTModifyEntityConvert ttModifyEntityConvert;

    @Autowired
    ContractPriceEntityConvert contractPriceEntityConvert;

    @Autowired
    TTPriceEntityConvert ttPriceEntityConvert;

    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;

    public TradeTicketDO convert2TradeTicketDO(TTDTO ttdto) {
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        //TODO NEO 202408
        //各个场景待细化

        switch (ttdto.getTTTypeEnum()) {
            case NEW:
            case ALLOCATE:
            case ASSIGN:
            case BUYBACK:
                tradeTicketDO = add2TradeTicketDO(ttdto);
                break;
            case REVISE:
            case SPLIT:
                tradeTicketDO = revise2TradeTicketDO(ttdto);
                break;
            case TRANSFER:
            case REVERSE_PRICE:
                tradeTicketDO = transfer2TradeTicketDO(ttdto);
                break;
            case PRICE:
            case FIXED:
                tradeTicketDO = price2TradeTicketDO(ttdto);
                break;
            case STRUCTURE_PRICE:
                //TODO NEO 202408
                //tradeTicketDO = structurePrice2TradeTicketDO(ttdto);
                break;
            default:
                break;
        }

        return tradeTicketDO;
    }

    /**
     * 新增场景
     *
     * @param ttdto
     * @returnsaveTT
     */
    public TradeTicketDO add2TradeTicketDO(TTDTO ttdto) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.add2TradeTicketEntity(ttdto);
        // 子表convert
        TTAddEntity ttAddEntity = ttAddEntityConvert.add2TTAddEntity(ttdto);
        // 合同价格convert
        ContractPriceEntity contractPriceEntity = contractPriceEntityConvert.add2ContractPriceEntity(ttdto.getPriceDetailBO(), tradeTicketEntity.getCode(), tradeTicketEntity.getContractCode());
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttAddEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    /**
     * 销售合同结构化定价DO
     *
     * @param ttdto
     * @returnsaveTT
     */
    public TradeTicketDO structureTradeTicketDO(TTDTO ttdto) {
        // 主表convert TODO需要优化处理
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getStructureTradeTicketEntity(ttdto);
        // 子表convert
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        TTStructureEntity ttStructureEntity = new TTStructureEntity();
        BeanUtil.copyProperties(salesStructurePriceTTDTO, ttStructureEntity);
        ttStructureEntity
                .setTotalNum(salesStructurePriceTTDTO.getTotalNum())
                .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setSignDate(salesStructurePriceTTDTO.getSignDate())
                .setCustomerName(salesStructurePriceTTDTO.getCustomerName())
                .setDeliveryFactoryCode(salesStructurePriceTTDTO.getDeliveryFactoryCode())
                .setDeliveryFactoryId(salesStructurePriceTTDTO.getDeliveryFactoryId())
                .setStructureName(salesStructurePriceTTDTO.getStructureName())
                .setTtId(tradeTicketEntity.getId());
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttStructureEntity);
        return tradeTicketDO;
    }

    /**
     * 仓单注销场景
     *
     * @param ttdto
     * @return
     */
    public TradeTicketDO writeOff2TradeTicketDO(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.writeOff2TradeTicketEntity(ttdto, arrangeContext);
        // 子表convert
        TTModifyEntity ttModifyEntity = ttModifyEntityConvert.writeOff2TTModifyEntity(ttdto, arrangeContext);
        // 合同价格convert
        PriceDetailBO priceDetailDTO = null;
        ContractPriceEntity contractPriceEntity = null;
        // 仓单生成提货合同
        if (TTWriteOffActionEnum.DELIVERY_ADD.equals(ttdto.getWriteOffTTAction())) {
            priceDetailDTO = ttdto.getContractWriteOffDTO().getPriceDetailDTO();
        }
        // 仓单注销生成的采购合同
        if (TTWriteOffActionEnum.PURCHASE_ADD.equals(ttdto.getWriteOffTTAction())) {
            priceDetailDTO = ttdto.getContractWriteOffDTO().getContractWriteOffPurchaseDTO().getPriceDetailDTO();
        }
        if (ObjectUtil.isNotEmpty(priceDetailDTO)) {
            contractPriceEntity = contractPriceEntityConvert.writeOff2ContractPriceEntity(priceDetailDTO, tradeTicketEntity);
        }
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttModifyEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    /**
     * 仓单豆二注销场景
     *
     * @param ttdto
     * @return
     */
    public TradeTicketDO writeOff2OMTradeTicketDO(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.writeOff2OMTradeTicketEntity(ttdto, arrangeContext);
        // 子表convert
        TTModifyEntity ttModifyEntity = ttModifyEntityConvert.writeOff2OMTTModifyEntity(ttdto, arrangeContext);
        // 合同价格convert
        PriceDetailBO priceDetailDTO = null;
        ContractPriceEntity contractPriceEntity = null;
        // 获取价格信息
        priceDetailDTO = ttdto.getPriceDetailBO();
        if (ObjectUtil.isNotEmpty(priceDetailDTO)) {
            contractPriceEntity = contractPriceEntityConvert.writeOff2ContractPriceEntity(priceDetailDTO, tradeTicketEntity);
        }
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttModifyEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }


    /**
     * 仓单合同作废
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    public TradeTicketDO invalidTradeTicketDO(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.invalidTradeTicketEntity(ttdto);
        // 子表convert
        TTAddEntity ttAddEntity = ttAddEntityConvert.invalidTTAddEntity(ttdto);
        // 合同价格convert
        PriceDetailBO priceDetailDTO = null;
        ContractPriceEntity contractPriceEntity = null;
        // 获取价格信息
        priceDetailDTO = ttdto.getPriceDetailBO();
        if (ObjectUtil.isNotEmpty(priceDetailDTO)) {
            contractPriceEntity = contractPriceEntityConvert.writeOff2ContractPriceEntity(priceDetailDTO, tradeTicketEntity);
        }
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttAddEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    /**
     * 取消声明TT
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    public TradeTicketDO cancelTradeTicketDO(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.cancelTradeTicketEntity(ttdto);
        // 子表convert
        TTAddEntity ttAddEntity = ttAddEntityConvert.cancelTTAddEntity(ttdto);
        // 合同价格convert
        PriceDetailBO priceDetailDTO = null;
        ContractPriceEntity contractPriceEntity = null;
        // 获取价格信息
        priceDetailDTO = ttdto.getPriceDetailBO();
        if (ObjectUtil.isNotEmpty(priceDetailDTO)) {
            contractPriceEntity = contractPriceEntityConvert.writeOff2ContractPriceEntity(priceDetailDTO, tradeTicketEntity);
        }
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttAddEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    /**
     * 分配场景
     *
     * @param ttdto
     * @return
     */
    public TradeTicketDO allocate2TradeTicketDO(TTDTO ttdto) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.allocate2TradeTicketEntity(ttdto);
        // 子表convert
        TTAddEntity ttAddEntity = ttAddEntityConvert.allocate2TTAddEntity(ttdto);
        // 合同价格convert
        ContractPriceEntity contractPriceEntity = contractPriceEntityConvert.add2ContractPriceEntity(ttdto.getPriceDetailBO(), tradeTicketEntity.getCode(), tradeTicketEntity.getContractCode());
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttAddEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }


    public TradeTicketDO split2TradeTicketDO(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.split2TradeTicketEntity(ttdto, arrangeContext);
        // 子表convert
        TTModifyEntity ttModifyEntity = ttModifyEntityConvert.split2TTModifyEntity(ttdto, tradeTicketEntity.getId(), tradeTicketEntity.getType());

        // 合同价格convert
        ContractPriceEntity contractPriceEntity = contractPriceEntityConvert.split2ContractPriceEntity(ttdto, ttModifyEntity);
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttModifyEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    public TradeTicketDO revise2TradeTicketDO(TTDTO ttdto) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.revise2TradeTicketEntity(ttdto);
        // 子表convert
        TTModifyEntity ttModifyEntity = ttModifyEntityConvert.revise2TTModifyEntity(ttdto, tradeTicketEntity.getId(), tradeTicketEntity.getType());

        // 合同价格convert
        ContractPriceEntity contractPriceEntity = contractPriceEntityConvert.revise2ContractPriceEntity(ttdto, ttModifyEntity);
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttModifyEntity);
        tradeTicketDO.setContractPriceEntity(contractPriceEntity);
        return tradeTicketDO;
    }

    public TradeTicketDO price2TradeTicketDO(TTDTO ttdto) {
        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.price2TradeTicketEntity(ttdto);
        // 【仓单】【交易所】【交易平台】 平台TT是直接完成的
        if (TTStatusEnum.DONE.getType() == ttdto.getSalesContractTTPriceDTO().getStatus()) {
            tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setContractStatus(ContractStatusEnum.EFFECTIVE.getValue());
            tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.PROCESSING.getValue());
        }
        // 子表convert
        TTPriceEntity ttPriceEntity = ttPriceEntityConvert.price2TTPriceEntity(ttdto, tradeTicketEntity);

        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttPriceEntity);
        return tradeTicketDO;
    }

    public TradeTicketDO transfer2TradeTicketDO(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();

        // 主表convert
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityConvert.transfer2TradeTicketEntity(ttdto);
        // 【仓单】【交易所】【交易平台】 平台TT是直接完成的
        if (TTStatusEnum.DONE.getType() == salesContractTTTransferDTO.getStatus()) {
            tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setContractStatus(ContractStatusEnum.EFFECTIVE.getValue());
            tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.PROCESSING.getValue());
        }

        // 子表convert
        TTTranferEntity ttTranferEntity = tradeTicketConvertUtil.convertToTTTransfer(salesContractTTTransferDTO, tradeTicketEntity.getId());

        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(ttTranferEntity);
        log.info("反点价内容1，价格==============================："+FastJsonUtils.getBeanToJson(ttdto.getPriceDetailBO()));
        log.info("反点价内容2，ttdto==============================："+FastJsonUtils.getBeanToJson(ttdto));
        // 合同价格convert
        if (ttdto.getSalesContractTTTransferDTO().getAddedSignatureType() != -1) {
            ContractPriceEntity sonContractPriceEntity = tradeTicketConvertUtil.saveTransferContractPrice(ttdto, tradeTicketEntity.getId());
            tradeTicketDO.setContractPriceEntity(sonContractPriceEntity);
        }
        return tradeTicketDO;
    }

}
