package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.ContractPriceMapper;
import com.navigator.trade.pojo.entity.ContractPriceEntity;

import java.util.List;

@Dao
public class ContractPriceDao extends BaseDaoImpl<ContractPriceMapper, ContractPriceEntity> {
    /**
     * 根据ttId查询价格
     *
     * @return ContractPriceEntity
     */
    public ContractPriceEntity getContractPriceEntityByTTId(Integer ttId) {
        return getOne(Wrappers.<ContractPriceEntity>lambdaUpdate().eq(ContractPriceEntity::getTtId, ttId));
    }

    /**
     * 更新price中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    public int updateContractId(Integer ttId, Integer contractId) {
        return update(Wrappers.<ContractPriceEntity>lambdaUpdate()
                .set(ContractPriceEntity::getContractId, contractId)
                .eq(ContractPriceEntity::getTtId, ttId)) ? 1 : 0;
    }

    /**
     * 根据合同Id查询价格
     *
     * @return ContractPriceEntity
     */
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        List<ContractPriceEntity> priceEntityList = list(Wrappers.<ContractPriceEntity>lambdaUpdate()
                .eq(ContractPriceEntity::getContractId, contractId)
                .eq(ContractPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(ContractPriceEntity::getUpdatedAt));

        return CollectionUtil.isNotEmpty(priceEntityList) ? priceEntityList.get(0) : null;
    }

    public List<ContractPriceEntity> getContractPriceListContractId(Integer contractId) {

        return list(Wrappers.<ContractPriceEntity>lambdaUpdate()
                .eq(ContractPriceEntity::getContractId, contractId)
                .eq(ContractPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(ContractPriceEntity::getCreatedAt));
    }

    public boolean updatePriceByTtId(ContractPriceEntity contractPriceEntity) {
        return update(contractPriceEntity, Wrappers.<ContractPriceEntity>lambdaUpdate()
                .eq(ContractPriceEntity::getTtId, contractPriceEntity.getTtId()));
    }
}
