package com.navigator.trade.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.ContractStructureMapper;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractStructureEntity;

import java.util.List;

@Dao
public class ContractStructureDao extends BaseDaoImpl<ContractStructureMapper, ContractStructureEntity> {
    /**
     * 根据ttId查询结构化定价详情
     *
     * @return ContractStructureEntity
     */
    public ContractStructureEntity getContractStructureByTTId(Integer ttId) {
        return getOne(Wrappers.<ContractStructureEntity>lambdaUpdate()
                .eq(ContractStructureEntity::getTtId, ttId));
    }

    public ContractStructureEntity getContractStructure(Integer ContractId) {
        return getOne(Wrappers.<ContractStructureEntity>lambdaUpdate()
                .eq(ContractStructureEntity::getContractId, ContractId));
    }

    public IPage<ContractStructureEntity> queryContractStructure(QueryDTO<ContractBO> queryDTO) {

        ObjectMapper mapper = new ObjectMapper();
        ContractBO contractBO = mapper.convertValue(queryDTO.getCondition(), ContractBO.class);

        LambdaQueryWrapper<ContractStructureEntity> wrapper = new LambdaQueryWrapper<ContractStructureEntity>()
                .like(StrUtil.isNotBlank(contractBO.getCustomerName()), ContractStructureEntity::getCustomerName, contractBO.getCustomerName())
                .like(StrUtil.isNotBlank(contractBO.getSupplierName()), ContractStructureEntity::getSupplierName, contractBO.getSupplierName())
                .eq(null != contractBO.getGoodsCategoryId(), ContractStructureEntity::getGoodsCategoryId, contractBO.getGoodsCategoryId())
                .eq(StrUtil.isNotBlank(contractBO.getDomainCode()),ContractStructureEntity::getDomainCode, contractBO.getDomainCode())
                .eq(null != contractBO.getPriceStatus(),ContractStructureEntity::getPriceStatus, contractBO.getPriceStatus())
                .eq(StrUtil.isNotBlank(contractBO.getContractCode()),ContractStructureEntity::getContractCode, contractBO.getContractCode())
                .eq(null!=contractBO.getSignStartDate(),ContractStructureEntity::getStartTime,contractBO.getSignStartDate())
                .orderByAsc(ContractStructureEntity::getCreatedAt);

        IPage<ContractStructureEntity> iPage = this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
        return iPage;
    }

    public List<ContractStructureEntity> getByContractCode(String contractCode) {
        LambdaQueryWrapper<ContractStructureEntity> wrapper = new LambdaQueryWrapper<ContractStructureEntity>()
                .eq(ContractStructureEntity::getContractCode,contractCode)
                .eq(ContractStructureEntity::getIsDeleted,0);
        return list(wrapper);
    }

    public void updateContractById(ContractStructureEntity contractStructureEntity) {
        updateById(contractStructureEntity);
    }


    public List<ContractStructureEntity> verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO) {

        return this.list(Wrappers.<ContractStructureEntity>lambdaUpdate()
                .eq(ContractStructureEntity::getCustomerId,verifyContractStructureNumDTO.getCustomerId())
                .eq(ContractStructureEntity::getCompanyId,verifyContractStructureNumDTO.getCompanyId())
                .eq(ContractStructureEntity::getGoodsCategoryId,verifyContractStructureNumDTO.getGoodsCategoryId())
                .eq(ContractStructureEntity::getDomainCode,verifyContractStructureNumDTO.getDomainCode())
        );
    }
}
