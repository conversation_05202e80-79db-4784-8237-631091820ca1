package com.navigator.trade.service.contract.purchase;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDepositRateFacade;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerDepositRateVO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.Impl.BaseContractReviseAbstractService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 豆粕合同拆分的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_P_CONTRACT_REVISE,SBO_P_CONTRACT_REVISE")
public class PurChaseContractReviseService extends BaseContractReviseAbstractService {
    @Resource
    public IContractOperationNewService purchaseContractOperationService;
    @Resource
    public CustomerDepositRateFacade customerDepositRateFacade;

    @Override
    protected void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 记录操作记录
        purchaseContractOperationService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.SPLIT_PURCHASE_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                SystemEnum.MAGELLAN.getValue());
    }

    @Override
    protected ContractEntity operateSonContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 判断合同主体是否发生修改
        if (!contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            contractModifyDTO.setContractSource(ContractActionEnum.REVISE_CUSTOMER.getActionValue());
            contractModifyDTO.setParentContractEntity(contractEntity);
            // 生成子合同
            IContractService createService = salesContractHandler.getStrategy(
                    contractEntity.getSalesType(),
                    ContractTradeTypeEnum.NEW.getValue(),
                    contractEntity.getGoodsCategoryId());
            return createService.createContractByModify(contractModifyDTO);
        }
        return null;
    }

    @Override
    protected List<TTQueryVO> operateTradeTicket(ContractEntity sonContractEntity, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        TTDTO ttdto = new TTDTO();
        SalesContractReviseTTDTO salesContractReviseTTDTO = new SalesContractReviseTTDTO();

        boolean changeCustomerFlag = false;
        int originCustomerId = contractModifyDTO.getCustomerId();

        // 保存接口不走修改主体的逻辑，走修改主体的逻辑，然后最后修改TT的主体信息
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue() && !contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            contractModifyDTO.setSupplierId(contractEntity.getSupplierId());
            changeCustomerFlag = true;
        }

        // 处理变更的数据
        if (!contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            BeanUtils.copyProperties(sonContractEntity, salesContractReviseTTDTO);
            salesContractReviseTTDTO.setSonContractId(sonContractEntity.getId());
            salesContractReviseTTDTO.setSourceContractId(contractEntity.getId());
            salesContractReviseTTDTO.setRootContractId(contractEntity.getRootId());
            salesContractReviseTTDTO.setSignDate(new Date());
            ttdto.setReviseCustomerType(true);
        } else {
            ttdto.setReviseCustomerType(false);
            BeanUtils.copyProperties(contractEntity, salesContractReviseTTDTO);
            BeanUtils.copyProperties(contractModifyDTO, salesContractReviseTTDTO);

            // 价格处理
            PriceDetailBO priceDetailDTO = contractModifyDTO.getPriceDetailDTO();
            BigDecimal unitPrice = contractModifyDTO.getUnitPrice();

            // 运输费
            BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailDTO.getTransportPrice())
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getLiftingPrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getDelayPrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getTemperaturePrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getOtherDeliveryPrice()));

            // 计算fobUnitPrice价格
            BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
            salesContractReviseTTDTO.setFobUnitPrice(fobUnitPrice);

            // 计算总金额
            salesContractReviseTTDTO.setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), unitPrice));

            // 基差修改一口价||暂定价||基差暂定价 履约保证金更新
            if (contractModifyDTO.getReviseType() == 2) {
                // NAV系统的履约保证金更新=NAV变更前履约保证金+NAV变更前履约保证金点价后补缴
                int depositRate = contractEntity.getDepositRate();
                int addedDepositRate = contractEntity.getAddedDepositRate();
                int newDepositRate = depositRate + addedDepositRate;
                salesContractReviseTTDTO.setDepositRate(newDepositRate)
                        .setAddedDepositRate(0)
                        .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, salesContractReviseTTDTO.getTotalAmount(), BigDecimal.valueOf(salesContractReviseTTDTO.getDepositRate() * 0.01)));

                // 查询客户的保证金配置是否存在
                Result result = customerDepositRateFacade.getCustomerDepositRateAddTT(new CustomerDepositRateBO()
                        .setCustomerId(contractEntity.getSupplierId())
                        .setContractType(contractModifyDTO.getSonContractType())
                        .setStatus(DisableStatusEnum.ENABLE.getValue())
                        .setCategoryId(contractEntity.getGoodsCategoryId())
                        .setCategory1(String.valueOf(contractEntity.getCategory1()))
                        .setCategory2(String.valueOf(contractEntity.getCategory2()))
                        .setCategory3(String.valueOf(contractEntity.getCategory3()))
                        .setBuCode(contractEntity.getBuCode())
                );
                if (result.getCode() == ResultCodeEnum.OK.code()) {
                    List<CustomerDepositRateVO> customerDepositRateVOS = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerDepositRateVO.class);
                    boolean exist = customerDepositRateVOS.stream().anyMatch(depositRateVO ->
                            depositRateVO.getDepositRate() == newDepositRate
                                    && depositRateVO.getPricingDepositRate() == 0
                                    && depositRateVO.getIsProcurement().equals(GeneralEnum.YES.getValue()));
                    // 不存在则增加
                    if (!exist) {
                        CustomerDepositRateDTO customerDepositRateDTO = new CustomerDepositRateDTO();
                        customerDepositRateDTO.setCustomerId(contractEntity.getCustomerId())
                                .setStatus(DisableStatusEnum.ENABLE.getValue())
                                .setCategoryId(contractEntity.getGoodsCategoryId())
                                .setIsProcurement(GeneralEnum.YES.getValue())
                                .setDepositRate(newDepositRate)
                                .setContractType(contractModifyDTO.getSonContractType())
                                .setPricingDepositRate(0);
                        customerDepositRateFacade.saveCustomerDepositRate(customerDepositRateDTO);
                    }
                }
            }

            // 税率
            SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
            if (null != skuEntity.getTaxRate()) {
                BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                salesContractReviseTTDTO.setTaxRate(taxRate);
                //cifUnitPrice
                BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
                salesContractReviseTTDTO.setCifUnitPrice(cifUnitPrice);
            }

            salesContractReviseTTDTO.setSignDate(contractEntity.getSignDate());
        }

        // 处理点价截止日期
        String priceEndTime = contractModifyDTO.getPriceEndTime();

        if (DateTimeUtil.isDate(priceEndTime)) {
            salesContractReviseTTDTO.setPriceEndType(ContractPriceEndTypeEnum.DATE.getValue());
        } else {
            salesContractReviseTTDTO.setPriceEndType(ContractPriceEndTypeEnum.TEXT.getValue());
        }

        salesContractReviseTTDTO.setContractType(contractModifyDTO.getSonContractType());
        salesContractReviseTTDTO.setShipWarehouseId(Integer.parseInt(contractModifyDTO.getShipWarehouseId()));
        salesContractReviseTTDTO.setWeightCheck(Integer.parseInt(contractModifyDTO.getWeightCheck()));
        salesContractReviseTTDTO.setMemo(contractModifyDTO.getRemark());
        salesContractReviseTTDTO.setAddedDepositRate(contractModifyDTO.getAddedDepositRate() == null ? contractEntity.getAddedDepositRate() : contractModifyDTO.getAddedDepositRate());
        salesContractReviseTTDTO.setDelayPayFine(contractModifyDTO.getDelayPayFine() == null ? contractEntity.getDelayPayFine() : contractModifyDTO.getDelayPayFine());
        salesContractReviseTTDTO.setDepositReleaseType(contractModifyDTO.getDepositReleaseType() == null ? contractEntity.getDepositReleaseType() : contractModifyDTO.getDepositReleaseType());
        salesContractReviseTTDTO.setNeedPackageWeight(contractModifyDTO.getNeedPackageWeight() == null ? contractEntity.getNeedPackageWeight() : contractModifyDTO.getNeedPackageWeight());
        salesContractReviseTTDTO.setPackageWeight(contractModifyDTO.getPackageWeight() == null ? contractEntity.getPackageWeight() : contractModifyDTO.getPackageWeight());
        salesContractReviseTTDTO.setSupplierAccountId(contractModifyDTO.getSupplierAccountId() == null ? contractEntity.getSupplierAccountId() : contractModifyDTO.getSupplierAccountId());
        salesContractReviseTTDTO.setSourceContractId(contractEntity.getId());
        salesContractReviseTTDTO.setRootContractId(contractEntity.getRootId());
        salesContractReviseTTDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        salesContractReviseTTDTO.setReviseType(contractModifyDTO.getReviseType());
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setContractType(contractModifyDTO.getSonContractType())
                .setDepositRate(contractModifyDTO.getDepositRate())
                .setAddedDepositRate(contractModifyDTO.getAddedDepositRate());
        salesContractReviseTTDTO.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));
        ttdto.setSalesContractReviseTTDTO(salesContractReviseTTDTO);

        // 价格修改
        ttdto.setPriceDetailBO(contractModifyDTO.getPriceDetailDTO());

        ttdto.setSubmitType(contractModifyDTO.getSubmitType());
        ttdto.setChangeCustomerFlag(changeCustomerFlag);
        ttdto.setOriginCustomerId(originCustomerId);

        // 获取处理TT接口
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                contractEntity.getSalesType(),
                TTTypeEnum.REVISE.getType(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(ttProcessorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
        List<TTQueryVO> ttQueryVOS = tradeTicketService.saveTT(ttdto).getData();

        // 更新approvalType
        contractModifyDTO.setApprovalType(ttdto.getSalesContractReviseTTDTO().getApprovalType());

        // 前端合同编号展示
        // 普通修改 原合同 新tt
        // 主体修改 原合同
        //         新合同 新tt
        TTQueryVO ttQueryVO = new TTQueryVO();
        if (contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            ttQueryVOS.forEach(queryVO -> {
                queryVO.setContractCode(null);
                contractModifyDTO.setTtId(queryVO.getTtId());
            });
        }

        ttQueryVO.setSourceFlag(1).setContractCode(contractEntity.getContractCode());
        ttQueryVOS.add(ttQueryVO);
        return ttQueryVOS;
    }

    @Override
    protected void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {

        // 判断合同主体是否发生修改
        if (!contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            contractEntity.setContractNum(BigDecimal.ZERO)
                    .setTotalAmount(BigDecimal.ZERO)
                    .setDepositAmount(BigDecimal.ZERO);
        } else {
            // 普通修改
            TTModifyEntity modifyEntity = ttModifyService.getTTModifyEntityByTTId(contractModifyDTO.getTtId());

            int isChangeFactory = 0;

            if (null != modifyEntity) {
                // 交货工厂是否变更
                if (StringUtils.isNotBlank(modifyEntity.getDeliveryFactoryName()) && !contractEntity.getDeliveryFactoryName().equals(modifyEntity.getDeliveryFactoryName())) {
                    isChangeFactory = 1;
                }

                BeanUtils.copyProperties(modifyEntity, contractEntity);
                contractEntity.setId(contractModifyDTO.getContractId())
                        .setPayConditionId(contractModifyDTO.getPayConditionId());
            }

            // belongCustomerId变更
            if (StringUtils.isNotBlank(contractEntity.getDeliveryFactoryCode())) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(contractEntity.getDeliveryFactoryCode());
                if (factoryEntity != null) {
                    CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), contractEntity.getCompanyId());
                    contractEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                }
            }

            contractEntity.setIsChangeFactory(isChangeFactory);
        }

        // 基差修改一口价直接进入生效中
        if (contractModifyDTO.getReviseType() == 2) {
            // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 Start
            PriceDetailBO priceDetailBO = contractModifyDTO.getPriceDetailDTO();
            BigDecimal avgPrice = contractService.contractAvgPrice(contractModifyDTO.getContractId());
            log.info("==========================================avgPrice:{}",avgPrice);
            if (!BigDecimalUtil.isEqual(avgPrice, priceDetailBO.getForwardPrice())) {
                priceDetailBO.setForwardPrice(avgPrice);
                log.info("==========================================priceDetailBO.getForwardPrice():{}",priceDetailBO.getForwardPrice());
                contractPriceService.updateContractForwardPrice(contractEntity, priceDetailBO.getForwardPrice());
            }
            // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 end
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());

            // 记录定价完成
            String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
            redisUtil.set(priceStatus, 1);

            // 基差修改一口价直接记录版本
            contractValueObjectService.updateContractById(contractEntity, contractModifyDTO.getTtId());
            return;
        }

        // 合同进入修改中
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        contractEntity.setUsage(contractModifyDTO.getUsage());
        contractValueObjectService.updateContractById(contractEntity);
    }

}
