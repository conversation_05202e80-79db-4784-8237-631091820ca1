package com.navigator.trade.app.contract.domain.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;

/**
 * 合同值对象基础服务
 * 包括合同、合同历史、合同价格、合同结构化定价详情
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface ContractDomainService {

    /**
     * 保存创建合同
     *
     * @param contractEntity
     * @return
     */
    boolean saveContract(ContractEntity contractEntity);

    /**
     * 更新合同
     *
     * @param contractEntity
     * @return
     */
    boolean updateContractById(ContractEntity contractEntity);

    /**
     * 更新合同，并记录合同历史
     *
     * @param contractEntity 合同实体
     * @param backTradeType  备份类型
     * @param referCode      关联编码
     * @return
     */
    boolean updateContractById(ContractEntity contractEntity, String backTradeType, String referCode);


    /**
     * 回滚合同动作
     *
     * @param contractId
     * @return
     */
    ContractEntity rollBackContract(Integer contractId);


    /**
     * 创建结构化合同
     *
     * @param contractStructureEntity
     * @return
     */
    boolean saveContractStructure(ContractStructureEntity contractStructureEntity);


    /**
     * 更新结构化定价状态
     *
     * @param contractStructureDTO
     * @return
     */
    boolean updateStructureContractPricingStatus(ContractStructureDTO contractStructureDTO);


    /**
     * 根据合同ID作废合同
     *
     * @param contractId
     * @param status
     * @return
     */
    boolean updateContractStatusById(Integer contractId, Integer status);

    /**
     * 上传文件更新合同状态
     *
     * @param uploadFile
     * @param status
     * @return
     */
    Result updateContractStatus(MultipartFile uploadFile, Integer status);

    /**
     * 更新合同结构化
     *
     * @param contractStructureEntity
     * @return
     */
    boolean updateStructureContract(ContractStructureEntity contractStructureEntity);

    /**
     * 更新合同的期货价格
     *
     * @param contractEntity 合同实体
     * @param forwardPrice   期货价格
     * @return
     */
    ContractEntity updateContractForwardPrice(ContractEntity contractEntity, BigDecimal forwardPrice);

    /**
     * 计算价格详情
     *
     * @param priceDetailBO
     * @return
     */
    BigDecimal calculatePriceDetailBO(PriceDetailBO priceDetailBO);

    /**
     * 更新结构化合同
     * @param contractStructureEntity
     */
    void updateById(ContractStructureEntity contractStructureEntity);

    /**
     * 创建结构化合同
     * @param contractStructure
     * @return
     */
    boolean saveStructureContract(ContractStructureEntity contractStructure);

    /**
     * 根据siteCode与contractCode关闭合同
     *
     * @param siteCode     siteCode
     * @param contractCode contractCode
     * @return 是否成功
     */
    boolean closedBySiteCodeAndContractCode(String siteCode, String contractCode);
}
