package com.navigator.trade.app.contract.logic.service.handler;


import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;

import java.util.List;

/**
 * 合同拆分子类业务逻辑操作 逻辑处理
 *
 * <AUTHOR>
 * @Date 2024-07-15
 */
public interface SplitLogicService {

    /**
     * 校验合同信息 -- 拆分校验
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void splitContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);


    /**
     * 修改补充补充基本信息-创建子合同
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    void buildBaseInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);

    /**
     * 处理变更的业务信息
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    void buildBizInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);

    /**
     * 创建合同
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    void createChildContract(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);

    /**
     * 更新父子合同数据
     *
     * @param contractEntity
     * @param contractModifyDTO
     */
    void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, List<TTPriceEntity> ttPriceEntityList);

}
