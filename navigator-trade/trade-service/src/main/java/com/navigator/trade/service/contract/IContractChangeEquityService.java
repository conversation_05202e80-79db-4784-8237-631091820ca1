package com.navigator.trade.service.contract;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractChangeEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import com.navigator.trade.pojo.dto.tradeticket.ApprovalDTO;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.pojo.entity.ContractEquityVOEntity;
import com.navigator.trade.pojo.vo.ContractEquityDetailVO;
import com.navigator.trade.pojo.vo.ContractEquityOperationVO;

import java.util.List;

/**
 * <p>
 * dbt_contract_change_equity 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface IContractChangeEquityService {

    IPage<ContractEquityVOEntity> getChangeContractEquityList(QueryDTO<ContractEquityQueryDTO> queryDTO);

    boolean changeContractEquity(ContractChangeEquityDTO changeEquityDTO);

    ContractEquityDetailVO getChangeEquityDetailByApplyCode(String applyCode);

    List<ContractChangeEquityEntity> getChangeEquityByContractCode(String contractCode);

    boolean approveEquityChange(ApprovalDTO approvalDTO);

    List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId);

    List<ContractChangeEquityEntity> getChangeContractEquityDetailByContractIds(List<Integer> contractIds);

    List<ContractEquityOperationVO> getChangeContractEquityRecord(String contractCode);
}
