package com.navigator.trade.service.contract.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.future.enums.PendingTypeEnum;
import com.navigator.future.enums.PriceApplyOperationTypeEnum;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.contract.Impl.BaseContractOperationAbstractService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 * 销售合同基本操作的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Service
public class SalesContractOperationService extends BaseContractOperationAbstractService {

    @Resource
    PriceApplyFacade priceApplyFacade;
    @Resource
    ContractStructureDao contractStructureDao;

    @Override
    public void afterContractProcess(ContractEntity contractEntity) {
        //豆粕\豆粕销售结构化定价合同
        if ((contractEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_MEAL.getValue())
                || contractEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue()))
                && contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()
                && contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {

            ContractStructureEntity contractStructureEntity = contractStructureDao.getContractStructure(contractEntity.getId());
            if (null == contractStructureEntity) {
                //TODO NEO 数据异常
                return;
            }

            PriceApplyEntity priceApplyEntity = new PriceApplyDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setCustomerName(contractEntity.getCustomerName())
                    .setType(PriceTypeEnum.STRUCTURE_PRICING.getValue())
                    .setDominantCode(contractEntity.getDomainCode())
                    .setTranferDominantCode(contractEntity.getDomainCode())
                    .setApplyDiffPrice(BigDecimal.ZERO)
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setPendingType(PendingTypeEnum.FOLLOW_LARGE_CAP.getValue())
                    .setStructureType(contractStructureEntity.getStructureType())
                    .setApplyNum(contractEntity.getContractNum())
                    .setApplyPrice(BigDecimal.ZERO)
                    .setTransactionPrice(BigDecimal.ZERO)
                    .setTransactionDiffPrice(BigDecimal.ZERO)
                    .setDealHandNum(0)
                    .setContractId(contractEntity.getId())
//                    .setMaxPrice(contractStructureEntity.getMaxPrice())
//                    .setMinPrice(contractStructureEntity.getMinPrice())
                    .setUnitNum(contractStructureEntity.getUnitNum())
                    .setStartTime(contractStructureEntity.getStartTime())
                    .setEndTime(contractStructureEntity.getEndTime())
                    .setTotalDay(contractStructureEntity.getTotalDay());
//                    .setTriggerSys(SystemEnum.MAGELLAN.getDesc());

            PriceApplyDTO priceApplyDTO = (PriceApplyDTO) priceApplyEntity;
            priceApplyDTO.setModifyType(PriceApplyOperationTypeEnum.NORMAL_APPLY.getValue());
            priceApplyDTO.setTriggerSys(SystemEnum.MAGELLAN.getDescription());


            System.out.println(JSON.toJSONString(priceApplyDTO));

            Result<Integer> priceApplyResult = priceApplyFacade.priceApply(priceApplyDTO);
            if (priceApplyResult.isSuccess()) {
                int priceApplyId = priceApplyResult.getData();
                contractStructureEntity.setPriceApplyId(priceApplyId);
                contractStructureDao.updateById(contractStructureEntity);
            }


        }
    }
}
