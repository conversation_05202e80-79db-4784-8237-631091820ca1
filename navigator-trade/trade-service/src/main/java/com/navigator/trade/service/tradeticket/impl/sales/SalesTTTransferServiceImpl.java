package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.future.enums.AllocateTypeEnum;
import com.navigator.future.enums.ContraryStatusEnum;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.OperateTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;


@Slf4j
@Component("SBM_S_TT_TRANSFER,SBO_S_TT_TRANSFER")
public class SalesTTTransferServiceImpl extends BaseTradeTicketAbstractService {
    public SalesTTTransferServiceImpl() {
        ttTypeEnum = TTTypeEnum.TRANSFER;
        contractTradeTypeEnum = ContractTradeTypeEnum.TRANSFER_ALL;
        processorType = ProcessorTypeEnum.SBM_S_TRANSFER.getTtValue();
        logBizCodeEnum = LogBizCodeEnum.SUBMIT_SALES_TT;
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.SALES;
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_TRANSFER.getTtValue().equalsIgnoreCase(processorType) ||
                ProcessorTypeEnum.SBO_S_REVERSE_PRICE.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getTransferTradeTicketEntity(ttdto.getSalesContractTTTransferDTO());
        injectionProperty(tradeTicketEntity);
        tradeTicketEntity.setContractSource(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) ? ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue() : ContractActionEnum.TRANSFER_CONFIRM.getActionValue());
        tradeTicketEntity.setTradeType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) ? ContractTradeTypeEnum.TRANSFER_ALL.getValue() : ContractTradeTypeEnum.TRANSFER_PART.getValue());

        return tradeTicketEntity;
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) {
        saveTTTransfer(ttDto, ttId);
        if ((ttDto.getProcessorType().contains("REVERSE_PRICE")
                || (ttDto.getProcessorType().contains("TRANSFER")))
                && ttDto.getSalesContractTTTransferDTO().getAddedSignatureType() == -1) {
            return;
        }
        tradeTicketConvertUtil.saveTransferContractPrice(ttDto, ttId);
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = contractSignConvertUtil.getTransferContractSignCreateDTO(ttId, ttDto);
        return contractSignCreateDTO;
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {

        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        Integer contractId = salesContractTTTransferDTO.getContractId();
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        List<CompareObjectDTO> compareObjectDTOList = getCompareObjectDTOList(salesContractTTTransferDTO, contractEntity, contractPriceEntity);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractTTTransferDTO.setModifyContent(modifyContent);

        //获取前后字段
        PriceDetailBO priceDetailBO = ttdto.getPriceDetailBO();
        List<CompareObjectDTO> contentDTOList = getContentList(salesContractTTTransferDTO, contractEntity, contractPriceEntity, priceDetailBO);
        String content = JSON.toJSONString(contentDTOList);
        salesContractTTTransferDTO.setContent(content);
        //初始化交易、销售类型、合同来源
        Integer tradeType = null;
        String code = CodeGeneratorUtil.genSalesTTNewCode();
        switch (TTTranferTypeEnum.getByValue(salesContractTTTransferDTO.getType())) {
            case TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_ALL.getValue();
                contractSource = ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue();
                code = getSalesTTCode(contractId);
                break;
            case PART_TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_PART.getValue();
                contractSource = ContractActionEnum.TRANSFER_CONFIRM.getActionValue();
                break;
            case REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue();
                break;
            case PART_REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue();
                break;
            default:
                break;
        }
        salesContractTTTransferDTO.setTradeType(tradeType);

        salesContractTTTransferDTO.setSalesType(salesType.getValue());
        salesContractTTTransferDTO.setStatus(status);
        salesContractTTTransferDTO.setContractSource(contractSource);
        //协议签署状态
        salesContractTTTransferDTO.setContractSignatureStatus(contractSignatureStatus);
        salesContractTTTransferDTO.setUserId(JwtUtils.getCurrentUserId());

        //生成TT编号
        salesContractTTTransferDTO.setCode(code);

        ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
        return ttdto;
    }

    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        return null;
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        return "";
    }

    private List<CompareObjectDTO> getCompareObjectDTOList(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity) {
        return tradeTicketConvertUtil.getTransferCompareObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity);
    }

    private List<CompareObjectDTO> getContentList(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        return tradeTicketConvertUtil.getTransferContentObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity, priceDetailBO);
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTTranferEntity ttTranferEntity = ttTranferDao.getTTTransferEntityByTTId(ttId);

        if (ttTranferEntity.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())
//                || ttTranferEntity.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())
        ) {
            String modifyContent = ttTranferEntity.getContent();
            List<CompareObjectDTO> list = tradeTicketConvertUtil.getReviseCompareList(modifyContent, tradeTicketEntity, ttTranferEntity);
            ttDetailVO.setDetailType("11");
            ttDetailVO.setCompareObjectDTOList(list);
        } else {
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
            ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getContractId());

            TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
            PriceDetailVO priceDetailVO = new PriceDetailVO();
            BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
            BeanUtils.copyProperties(ttTranferEntity, ttQueryDetailVO);
            BeanUtils.copyProperties(contractEntity, ttQueryDetailVO);
            if (contractPriceEntity != null) {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
            ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

            //合同类型
            if (null != tradeTicketEntity.getContractType()) {
                ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
            }

            //卖家
            if (null != ttTranferEntity.getSupplierId()) {
                ttQueryDetailVO.setSupplierId(String.valueOf(ttTranferEntity.getSupplierId()));
            }

            //买家
            if (null != ttTranferEntity.getCustomerId()) {
                ttQueryDetailVO.setCustomerId(String.valueOf(ttTranferEntity.getCustomerId()));
            }
            ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttTranferEntity.getCustomerId());
            if (null != customerDTO) {
                ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
                ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
                ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS().isEmpty() ? null : customerDTO.getCustomerBankDTOS());
            }

            //商品信息
            if (null != ttTranferEntity.getGoodsCategoryId()) {
                ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttTranferEntity.getGoodsCategoryId()));
            }
            if (null != contractEntity.getGoodsPackageId()) {
                ttQueryDetailVO.setGoodsPackageId(String.valueOf(contractEntity.getGoodsPackageId()));
            }
            if (null != contractEntity.getGoodsSpecId()) {
                ttQueryDetailVO.setGoodsSpecId(String.valueOf(contractEntity.getGoodsSpecId()));
            }

            //商务
            if (null != tradeTicketEntity.getOwnerId()) {
                ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
                EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
                if (null != employEntity) {
                    ttQueryDetailVO.setOwnerName(employEntity.getName());
                }
            }

            //创建人
            if (null != tradeTicketEntity.getCreatedBy()) {
                EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
                if (null != employEntity) {
                    ttQueryDetailVO.setCreatedBy(employEntity.getName());
                }
            }
            //应付履约保证金状态
            if (null != contractEntity.getDepositAmount()) {
                int depositAmountStatus = contractEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
                ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
            }

            // 履约保证金释放方式
            ttQueryDetailVO.setDepositUseRule(contractEntity.getDepositReleaseType());
            // 点价截止日期
            ttQueryDetailVO.setPriceEndTime(contractEntity.getPriceEndTime());

            if (null != contractEntity.getInvoiceType()) {
                ttQueryDetailVO.setInvoiceType(contractEntity.getInvoiceType());
                ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
            }
            if (null != ttQueryDetailVO.getDeliveryType()) {
                DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
                if (null != deliveryTypeEntity) {
                    ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
                }
            }

            //查询工厂信息
            ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, contractEntity.getShipWarehouseId());

            //查询配置名称
            //目的地
            String destinationName = ttQueryDetailVO.getDestination();
            if (StringUtils.isNumeric(destinationName)) {
                SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
                destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
            }
            ttQueryDetailVO.setDestinationName(destinationName);

            //重量检测
            if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
                if (systemRuleItemEntity != null) {
                    ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
                }
            }
            //袋皮扣重
            if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
                if (systemRuleItemEntity != null) {
                    ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
                }
            }

            String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
            recordTTQuery(data, LogBizCodeEnum.QUERY_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

            ttDetailVO.setDetailType("0");
            ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        }
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(tradeTicketEntity.getContractCode())
                .setContractId(tradeTicketEntity.getContractId())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));

        return ttDetailVO;
    }

    public void saveTTTransfer(TTDTO ttdto, Integer ttId) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        TTTranferEntity ttTranferEntity = tradeTicketConvertUtil.convertToTTTransfer(salesContractTTTransferDTO, ttId);
        ttTranferDao.save(ttTranferEntity);

    }

    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());

            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }

        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {

    }

    @Override
    public void cancel(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        //取消工作流审批
        cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
        log.info("check_code_question  cancel ");
        //修改tt状态为待修改提交
        tradeTicketDao.updateStatusById(TTStatusEnum.WAITING.getType(), null, operateTTDTO.getTtId());
        //取消协议
        cancelContractSign(tradeTicketEntity, operateTTDTO.getMemo());
    }

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Result contraryPrice(ContraryPriceDTO contraryPriceDTO) {
        PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(String.valueOf(contraryPriceDTO.getAllocateId()));

        /*if (AllocateTypeEnum.ALL.getValue() == priceAllocateEntity.getType()) {
            return priceAllocateContraryAll(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        } else {
            return priceAllocateContraryPortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        }*/

        return priceAllocateContraryPortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());
    }

    /**
     * 部分撤回
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result priceAllocateContraryPortion(PriceAllocateEntity priceAllocateEntity, String contraryCause) {
        String userId = JwtUtils.getCurrentUserId();
        //撤回反点价
        //部分反点价,部分转月
        //根据申请单查询priceApplyId查询TTTranferEntity;
        List<TTTranferEntity> ttTranferEntities = ttTranferService.selectTTTranferByPriceAllocateId(priceAllocateEntity.getId());
        for (TTTranferEntity ttTranferEntity : ttTranferEntities) {
            //校验协议是否已经签署
            ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttTranferEntity.getTtId());
            //已签署不予撤回
            /*if (ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
            }*/
            //1,撤回转月,反点价单
            BigDecimal priceApplyNum = ttTranferEntity.getNum();

            ttTranferEntity
                    .setNum(BigDecimal.ZERO)
                    .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                    .setMemo(contraryCause);
            ttTranferService.updateTTTranferById(ttTranferEntity);
            //2,撤回TT
            //修改tt状态为待修改提交
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttTranferEntity.getTtId());
            tradeTicketEntity.setCancelReason(contraryCause)
                    .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                    .setStatus(TTStatusEnum.INVALID.getType());

            tradeTicketDao.updateById(tradeTicketEntity);
            //取消工作流审批
            cancelActiviti(contraryCause, userId, tradeTicketEntity);
            //3,撤回协议
            this.invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, contraryCause, contractSignEntity.getId());
            if (AllocateTypeEnum.ALL.getValue() == priceAllocateEntity.getType()) {
                priceAllocateContraryAll(ttTranferEntity, priceAllocateEntity);
            } else {
                //4,修改合同数量
                ContractEntity contractEntity = contractService.getContractById(ttTranferEntity.getSourceContractId());
                //合同数量
                BigDecimal contractNum = contractEntity.getContractNum().add(priceApplyNum);
                //拆分数量
                boolean b = contraryTTTranfer(contractEntity.getId(), ttTranferEntity.getId());
                contractEntity
                        .setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus())
                        .setContractNum(contractNum);
                if (TTTypeEnum.TRANSFER.getType().equals(tradeTicketEntity.getType())) {
                    BigDecimal totalTransferNum = contractEntity.getTotalTransferNum().subtract(priceApplyNum);
                    contractEntity.setTotalTransferNum(totalTransferNum);
                }
                //BigDecimal contractNum = contractEntity.getContractNum().add(priceApplyNum);
                List<CompareObjectDTO> compareObjectDTOS = JSON.parseArray(ttTranferEntity.getContent(), CompareObjectDTO.class);
                /*//合同总价
                BigDecimal totalAmount = contractNum.multiply(contractEntity.getUnitPrice());
                contractEntity.setTotalAmount(totalAmount);
                //应付履约保证金金额
                BigDecimal depositAmount = totalAmount.multiply(BigDecimal.valueOf((contractEntity.getDepositRate() * 0.01)));
                contractEntity.setDepositAmount(depositAmount);*/

                contraryContract(compareObjectDTOS, contractEntity, AllocateTypeEnum.PART.getValue());
                /*compareObjectDTOS.forEach(i -> {
                    //合同总价
                    if (i.getName().equals("totalAmount")) {
                        if (StringUtil.isNotEmpty(i.getBefore())) {
                            contractEntity.setTotalAmount(new BigDecimal(i.getBefore()));
                        }
                    }
                });*/

                //contractService.updateContract(contractEntity);

                log.info("Sale.AllocateType:{}", priceAllocateEntity.getType());
                log.info("Sale.ContractId:{}", ttTranferEntity.getContractId());
                ContractEntity contract = contractService.getBasicContractById(ttTranferEntity.getContractId());
                contract.setStatus(ContractStatusEnum.INVALID.getValue());
                contractService.updateContract(contract);
            }
        }
        return Result.success();
    }

    private boolean contraryTTTranfer(Integer contractId, Integer ttTranferId) {
        List<TTTranferEntity> ttTranferEntities = ttTranferService.getTTTranferByContractId(contractId, ttTranferId);

        for (TTTranferEntity ttTranferEntity : ttTranferEntities) {
            ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttTranferEntity.getTtId());

            if (null != contractSignEntity && ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSignEntity.getStatus()) {
                return false;
            }

        }
        return true;
    }

    /**
     * 全部撤回
     *
     * @param ttTranferEntity
     * @param priceAllocateEntity
     */
    private void priceAllocateContraryAll(TTTranferEntity ttTranferEntity, PriceAllocateEntity priceAllocateEntity) {
        //5,修改合同
        ContractEntity contractEntity = contractService.getContractById(ttTranferEntity.getSourceContractId());
        //转月,反点价次数修改
        if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
            if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()) {
                //转月次数
                contractEntity.setAbleTransferTimes(contractEntity.getAbleTransferTimes() + 1)
                        .setTransferredTimes(contractEntity.getTransferredTimes() - 1);
            } else {
                //反点价次数
                contractEntity.setAbleReversePriceTimes(contractEntity.getAbleReversePriceTimes() + 1)
                        .setReversedPriceTimes(contractEntity.getReversedPriceTimes() - 1);
            }
        }
        boolean b = contraryTTTranfer(contractEntity.getId(), ttTranferEntity.getId());

        contractEntity
                .setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus());
        //修改合同价格
        List<CompareObjectDTO> compareObjectDTOS = JSON.parseArray(ttTranferEntity.getContent(), CompareObjectDTO.class);
        //查询合同price
        contraryContract(compareObjectDTOS, contractEntity, AllocateTypeEnum.ALL.getValue());
    }

    private void contraryContract(List<CompareObjectDTO> compareObjectDTOS, ContractEntity contractEntity, Integer allocateType) {
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        compareObjectDTOS.forEach(i -> {

            //含税单价
            if (i.getName().equals("cifUnitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setCifUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //含税单价
            if (i.getName().equals("unitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //合同总价
            if (i.getName().equals("totalAmount")) {
                if(AllocateTypeEnum.ALL.getValue() == allocateType){
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setTotalAmount(new BigDecimal(i.getBefore()));
                    }
                }else if(AllocateTypeEnum.PART.getValue() == allocateType){
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        //合同总价
                        BigDecimal totalAmount = contractEntity.getContractNum().multiply(contractEntity.getUnitPrice());
                        contractEntity.setTotalAmount(totalAmount);
                    }

                }

            }
            //应付履约保证金金额
            BigDecimal depositAmount = contractEntity.getTotalAmount().multiply(BigDecimal.valueOf((contractEntity.getDepositRate() * 0.01)));
            contractEntity.setDepositAmount(depositAmount);

            //基差价格
            if (i.getName().equals("extraPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                    contractPriceEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                }
            }
            //手续费
            if (i.getName().equals("fee")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractPriceEntity.setFee(new BigDecimal(i.getBefore()));
                }
            }
            //期货价
            if (i.getName().equals("forwardPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractPriceEntity.setForwardPrice(new BigDecimal(i.getBefore()));
                }
            }
            //含税单价-物流相关费用
            if (i.getName().equals("fobUnitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setFobUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //期货合约
            if (i.getName().equals("domainCode")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setDomainCode(i.getBefore());
                }
            }
        });
        contractPriceService.updatePriceByContractId(contractPriceEntity);
        contractService.updateContract(contractEntity);
    }

    /**
     * 全部撤回
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result priceAllocateContraryAll(PriceAllocateEntity priceAllocateEntity, String contraryCause) {

        //撤回点价
        //部分点价
        List<TTTranferEntity> ttTranferEntities = ttTranferService.selectTTTranferByPriceAllocateId(priceAllocateEntity.getId());
        //遍历定价单
        for (TTTranferEntity ttTranferEntity : ttTranferEntities) {
            Integer ttId = ttTranferEntity.getTtId();
            //校验协议是否已经签署
            ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttId);
            //已签署不予撤回
            if (ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
            }
            String userId = JwtUtils.getCurrentUserId();
            //1,撤回定价单
            ttTranferEntity.setNum(BigDecimal.ZERO)
                    .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                    .setMemo(contraryCause);
            ttTranferService.updateTTTranferById(ttTranferEntity);
            //2,撤回TT
            //修改tt状态为待修改提交
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttTranferEntity.getTtId());
            tradeTicketEntity.setCancelReason(contraryCause)
                    .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                    .setStatus(TTStatusEnum.INVALID.getType());

            tradeTicketDao.updateById(tradeTicketEntity);
            //取消工作流审批
            cancelActiviti(contraryCause, userId, tradeTicketEntity);
            //3,撤回协议
            this.invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, contraryCause, contractSignEntity.getId());
            //5,修改合同
            //修改定价数量
            ContractEntity contractEntity = contractService.getContractById(ttTranferEntity.getSourceContractId());
            //转月,反点价次数修改
            if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()) {
                //转月次数
                contractEntity.setAbleTransferTimes(contractEntity.getAbleTransferTimes() - 1);
            } else {
                //反点价次数
                contractEntity.setAbleReversePriceTimes(contractEntity.getAbleReversePriceTimes() - 1);
            }
            contractEntity
                    .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
            ;
            //修改合同价格
            List<CompareObjectDTO> compareObjectDTOS = JSON.parseArray(ttTranferEntity.getContent(), CompareObjectDTO.class);
            //查询合同price
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            compareObjectDTOS.forEach(i -> {
                //合同总价
                if (i.getName().equals("totalAmount")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setTotalAmount(new BigDecimal(i.getBefore()));
                    }
                }
                //含税单价
                if (i.getName().equals("cifUnitPrice")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setCifUnitPrice(new BigDecimal(i.getBefore()));
                    }
                }
                //含税单价
                if (i.getName().equals("unitPrice")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setUnitPrice(new BigDecimal(i.getBefore()));
                    }
                }
                //基差价格
                if (i.getName().equals("extraPrice")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                        contractPriceEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                    }
                }
                //手续费
                if (i.getName().equals("fee")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractPriceEntity.setFee(new BigDecimal(i.getBefore()));
                    }
                }
                //期货价
                if (i.getName().equals("forwardPrice")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractPriceEntity.setForwardPrice(new BigDecimal(i.getBefore()));
                    }
                }
                //含税单价-物流相关费用
                if (i.getName().equals("fobUnitPrice")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setFobUnitPrice(new BigDecimal(i.getBefore()));
                    }
                }
                //期货合约
                if (i.getName().equals("domainCode")) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setDomainCode(i.getBefore());
                    }
                }
            });
            contractService.updateContract(contractEntity);
        }
        return Result.success();
    }
}
