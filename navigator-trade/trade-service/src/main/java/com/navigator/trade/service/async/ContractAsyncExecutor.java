package com.navigator.trade.service.async;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同异步执行器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractAsyncExecutor {

    private final OperationLogFacade operationLogFacade;
    private final MessageFacade messageFacade;
    private final EmployFacade employFacade;

    /**
     * 保存权益变更操作记录
     */
    @Async
    public void recordChangeEquityLog(ContractChangeEquityEntity contractChangeEquity, Integer operatorId, LogBizCodeEnum logBizCodeEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(logBizCodeEnum.getBizCode())
                .setOperationName(logBizCodeEnum.getMsg())
                .setReferBizId(contractChangeEquity.getContractId())
                .setReferBizCode(contractChangeEquity.getContractCode())
                .setTtCode(contractChangeEquity.getApplyCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_EQUITY.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setData(JSON.toJSONString(contractChangeEquity))
                .setOperatorId(operatorId)
                .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    @Async
    public void sendInMailMessage(String contractCode, String approvalResult, String applyBy) {

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        Map<String, Object> dataMap = new HashMap<>();
        List<String> receivers = new ArrayList<>();
        receivers.add(applyBy);

        dataMap.put("contractCode", contractCode);
        dataMap.put("approvalResult", approvalResult);

        messageInfoDTO.setDataMap(dataMap)
                .setReceivers(receivers)
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setMenuCode(String.valueOf(MenuCodeEnum.ACTIVITI.getValue()))
                .setBusinessCode(MessageBusinessCodeEnum.CONTRACT_EQUITY_APPROVE_RESULT_NOTICE.name())
                .setBusinessSceneCode(BusinessSceneEnum.CONTRACT_EQUITY_APPROVE_RESULT_NOTICE.getDesc());

        messageFacade.sendMessage(messageInfoDTO);
    }

    @Async
    public void recordContractConfirmLog(TradeTicketEntity tradeTicketEntity, String beforeData, String afterData) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(LogBizCodeEnum.CONFIRM_LKG_CONTRACT_LOG.getBizCode())
                .setOperationName(LogBizCodeEnum.CONFIRM_LKG_CONTRACT_LOG.getMsg())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode())
                .setTtCode(tradeTicketEntity.getCode())
                .setTradeTypeName(ContractActionEnum.getByType(tradeTicketEntity.getContractSource()).getDesc())
                .setMetaData(beforeData)
                .setData(afterData)
                .setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
        operationLogFacade.recordOperationLog(operationDetailDTO);
    }
}