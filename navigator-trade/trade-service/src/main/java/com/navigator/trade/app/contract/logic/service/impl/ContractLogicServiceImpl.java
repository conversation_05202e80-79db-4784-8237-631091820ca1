package com.navigator.trade.app.contract.logic.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.contract.logic.service.handler.*;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContractLogicServiceImpl implements ContractLogicService {

    /**
     * 创建合同子类业务处理
     */
    @Autowired
    private CreateLogicService createLogicService;
    /**
     * 创建结构化合同子类业务处理
     */
    @Autowired
    private CreateStructureLogicService createStructureLogicService;
    /**
     * 合同变更子类业务处理
     */
    @Autowired
    private ReviseLogicService reviseLogicService;
    /**
     * 合同拆分子类业务处理
     */
    @Autowired
    private SplitLogicService splitLogicService;
    /**
     * 合同解约索赔子类业务处理
     */
    @Autowired
    private WashOutLogicService washOutLogicService;
    /**
     * 合同回购子类业务处理
     */
    @Autowired
    private BuyBackLogicService buyBackLogicService;
    /**
     * 合同关闭子类业务处理
     */
    @Autowired
    private CloseLogicService closeLogicService;
    /**
     * 取消合同子类操作
     */
    @Autowired
    private CancelLogicService cancelLogicService;
    /**
     * 合同确认合规生效子类业务处理
     */
    @Autowired
    private ConfirmLogicService confirmLogicService;
    /**
     * 合同仓单注销子类业务处理
     */
    @Autowired
    private WriteOffLogicService writeOffLogicService;
    /**
     * 合同仓单豆二注销子类业务处理
     */
    @Autowired
    private Soybean2WriteOffLogicService soybean2WriteOffLogicService;
    /**
     * 合同本身处理的一些动作单元比如补充合同信息，定价，作废等
     */
    @Autowired
    private OperationLogicService operationLogicService;

    @Autowired
    private CommonLogicService commonLogicService;

    @Autowired
    private ContractDomainService contractDomainService;

    @Autowired
    private ContractQueryDomainService contractQueryDomainService;

    @Autowired
    private TransferLogicService transferLogicService;

    @Value("${sync.atlas.openQuery:0}")
    private Integer openAtlasQuery;  // 是否开启atlas查询


    /**
     * 现货采购合同创建|现货销售合同，仓单销售合同分配|转让,仓单采购合同创建
     *
     * @param contractCreateDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createContract(ContractCreateDTO contractCreateDTO) {
        ContractEntity contractEntity = new ContractEntity();
        //1. 补充基本信息
        createLogicService.buildBaseInfo(contractCreateDTO, contractEntity);
        //2. 补充业务信息
        createLogicService.buildBizInfo(contractCreateDTO, contractEntity);
        //3. 创建合同
        createLogicService.create(contractCreateDTO, contractEntity);
        //4. 合同创建结构化 - 销售合同
        createLogicService.createAdditionalInfo(contractCreateDTO, contractEntity);
        //5. 后续操作 记录操作日志 TODO 这个记录日志目前无法再前端查询
        createLogicService.afterCreateProcess(contractCreateDTO, contractEntity);
        // 仓单合同需要推送到A
        return contractEntity;
    }

    /**
     * 创建结构化合同
     *
     * @param tradeTicketEntity
     * @param ttdto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createStructureContract(TradeTicketEntity tradeTicketEntity, TTDTO ttdto) {
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        salesStructurePriceTTDTO.setTtId(tradeTicketEntity.getId());
        ContractEntity contractEntity = createStructureLogicService.createStructureContract(salesStructurePriceTTDTO);
        return contractEntity;
    }

    /***************************合同修改的业务逻辑处理 start***********************************/

    /**
     * 校验合同信息-修改校验
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    @Override
    public void modifyContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        reviseLogicService.modifyContractCheck(contractEntity, contractModifyDTO);
    }

    /**
     * 变更创建合同 - 创建子合同
     *
     * @param contractModifyDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity modifyContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        if (isCustomerChanged(contractEntity, contractModifyDTO)) {
            contractModifyDTO.setContractSource(ContractActionEnum.REVISE_CUSTOMER.getActionValue());
            // 保存提交不生成子合同
            if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
                return null;
            }
            return splitContract(contractEntity, contractModifyDTO);
        } else {
            contractModifyDTO.setContractSource(ContractActionEnum.REVISE.getActionValue());
            return null;
        }
    }

    @Override
    public void modifyPriceComplete(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        reviseLogicService.modifyPriceComplete(contractEntity, contractModifyDTO);
    }

    @Override
    public void modifyFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 修改父合同信息
        reviseLogicService.operateFatherContract(contractEntity, contractModifyDTO);
    }

    /********************************合同修改的业务逻辑处理 END****************************************/


    /********************************合同拆分的业务逻辑处理 strat****************************************/

    /**
     * 校验合同信息-修改校验
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    @Override
    public void splitContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        splitLogicService.splitContractCheck(contractEntity, contractModifyDTO);
    }

    /**
     * 变更创建合同 - 创建子合同
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity splitContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        contractModifyDTO.setParentContractEntity(contractEntity);

        // 生成子合同
        ContractEntity childContractEntity = new ContractEntity();
        // 补充基本信息
        splitLogicService.buildBaseInfo(contractModifyDTO, childContractEntity);
        // 补充业务信息
        splitLogicService.buildBizInfo(contractModifyDTO, childContractEntity);
        // 创建合同
        splitLogicService.createChildContract(contractModifyDTO, childContractEntity);

        return childContractEntity;
    }

    @Override
    public void splitFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, List<TTPriceEntity> ttPriceEntityList) {
        splitLogicService.operateFatherContract(contractEntity, contractModifyDTO, ttPriceEntityList);
    }
    /********************************合同拆分的业务逻辑处理 END****************************************/


    /********************************解约索赔的业务逻辑处理 strat****************************************/

    /**
     * 校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractWashOutDTO 解约定赔dto
     */
    @Override
    public void washOutContractCheck(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO) {
        washOutLogicService.washOutContractCheck(contractEntity, contractWashOutDTO);
    }

    /**
     * 处理父合同信息-处理父合同数据
     *
     * @param contractEntity     合同实体
     * @param contractWashOutDTO 解约索赔
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void washOutContract(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO) {
        // 更新父合同
        washOutLogicService.operateFatherContract(contractEntity, contractWashOutDTO);
        // 记录日志信息
        washOutLogicService.recordOperationLog(contractWashOutDTO, contractEntity);
    }


    /********************************解约索赔合的业务逻辑处理 END****************************************/

    /********************************销售合同回购 START***********************************************/

    @Override
    public void buyBackContractCheck(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        buyBackLogicService.buyBackContractCheck(contractEntity, contractBuyBackDTO);

    }

    @Override
    public void buyBackContract(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        // 1.更新父合同回购信息
        buyBackLogicService.operateFatherContract(contractEntity, contractBuyBackDTO);
        // 2.记录回购日志
        buyBackLogicService.recordOperationLog(contractBuyBackDTO, contractEntity);
        // 仓单合同生效的情况下，需要推送仓单进行变更记录信息,可以进行多次回购
    }

    /********************************销售合同回购 END ***********************************************/

    /********************************合同关闭的业务逻辑处理 START****************************************/

    @Override
    public void closeContractCheck(ContractEntity contractEntity) {
        closeLogicService.closeContractCheck(contractEntity);
    }

    /**
     * 关闭合同
     *
     * @param contractEntity 合同实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeContract(ContractEntity contractEntity) {
        closeLogicService.closeContract(contractEntity);
    }


    /********************************合同关闭的业务逻辑处理 END****************************************/


    /********************************合同注销的业务逻辑处理 Start****************************************/

    @Override
    public void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO) {
        writeOffLogicService.writeOffContractCheck(contractEntity, contractWriteOffDTO);
    }

    /**
     * （非豆二场景一，不修改货品）创建子合同 （货权合同即可）（不可见）
     * 注销不修改提货方且不修改其他字段；
     * 注销修改提货方且不修改其他字段；
     * 以上场景注销，不产生新仓单合同，仓单、仓单合同生成一条注销记录（即提货密码），并
     * 更新仓单的已注销量；
     * 更新仓单合同的已注销量；
     *
     * @param contractEntity
     * @param contractWriteOffDTO 注销仓单数据信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createCargoRights(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO) {
        // 1.处理货权合同信息
        contractWriteOffDTO.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        contractWriteOffDTO.setParentContractEntity(contractEntity);
        ContractEntity sonContractEntity = new ContractEntity();
        // 2.处理基本信息
        writeOffLogicService.buildBaseInfo(contractWriteOffDTO, sonContractEntity, false);
        writeOffLogicService.buildBizInfo(contractWriteOffDTO, sonContractEntity);
        // 提货权合同编码比较特殊
        String sonContractCode = contractQueryDomainService.genCargoRightsContractCode(contractEntity.getContractCode(), contractEntity.getSalesType());
        sonContractEntity.setTradeType(ContractTradeTypeEnum.WRITE_OFF_A.getValue())
                .setContractNature(ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue())
                .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum());
        // 3.创建合同
        writeOffLogicService.createContract(contractWriteOffDTO, sonContractEntity);
        // 4.处理父合同信息 - 【TODO修改合同信息-写历史表】
        contractEntity.setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum().add(contractEntity.getWarrantCancelCount()));
        if (contractEntity.getContractNum().subtract(contractEntity.getTotalBuyBackNum())
                .compareTo(contractEntity.getWarrantCancelCount()) == 0) {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue());
        } else {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        }
        // 设置仓单合同状态【直接生效的情况-记录历史】
        contractDomainService.updateContractById(contractEntity);
        return sonContractEntity;

    }

    /**
     * 场景：
     * d)注销不修改提货方且修改其他字段；
     * 1）以上场景注销，仓单、仓单合同生成一条注销记录（即提货密码），系统拆出一个新仓单销售合同并
     * 更新仓单的已注销量；
     * 原仓单合同的总数量；
     * 新合同总数量=已注销量；
     * 2）系统自动生成新仓单销售合同TT/协议/合同（TT类型=合同新增、业务类型=仓单），合同信息=注销录入信息+原合同信息
     * 3）新仓单销售合同的协议确认合规后触发ATLAS接口（新增接口），并更新合同状态至生效中
     * 创建提货合同  contractWriteOffDTO  |  contractModifyDTO 合并信息
     * tradeType : 处理2|3的提货合同
     *
     * @param contractEntity
     * @param contractWriteOffDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createDeliveryContract(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO, int tradeType) {
        // 创建提货合同 注销数量
        contractWriteOffDTO.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        contractWriteOffDTO.setParentContractEntity(contractEntity);
        ContractEntity sonContractEntity = new ContractEntity();
        // 1.构建基础信息
        writeOffLogicService.buildBaseInfo(contractWriteOffDTO, sonContractEntity, false);
        sonContractEntity.setTradeType(tradeType)
                .setContractNature(ContractNatureEnum.WAREHOUSE_DELIVERY.getValue())
                .setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum());
        // 2.构建提货合同的业务信息
        writeOffLogicService.buildBizInfo(contractWriteOffDTO, sonContractEntity);
        // 3.创建合同
        writeOffLogicService.createContract(contractWriteOffDTO, sonContractEntity);

        // 场景二 需要更新父合同数据信息
        if (ContractTradeTypeEnum.WRITE_OFF_B.getValue() == tradeType) {
            // 4.处理合同数据变更
            writeOffLogicService.operateFatherContract(contractWriteOffDTO, contractEntity);
            // 5.记录操作日志
            LogBizCodeEnum bizCodeEnum = LogBizCodeEnum.WARRANT_CONTRACT_WRITE_OFF;
            commonLogicService.addContractOperationLog(contractEntity, bizCodeEnum, JSONUtil.toJsonStr(contractWriteOffDTO), SystemEnum.MAGELLAN.getValue());
        }
        return sonContractEntity;
    }

    /**
     * 场景：
     * e)注销同时修改货品、修改提货方；
     * 1）以上场景注销；原仓单销售合同生成一个注销记录、一个新仓单采购合同、一个新仓单销售合同【上方出力 】；新仓单销售合同、新仓单采购合同同步注销记录，并
     * 更新仓单已注销量；
     * 原合同总数量、可注销/回购数量；
     * 新采购合同总数量=已注销量；
     * 新销售合同总数量=已注销量
     * 2）系统自动生成新仓单销售合同TT/协议/合同（TT类型=合同新增、业务类型=仓单），合同信息=注销录入信息+原合同信息
     * 3）系统自动生成新仓单采购合同TT/协议/合同（TT类型=合同新增、业务类型=仓单），合同信息=注销录入采购合同信息+原合同信息
     *
     * @param contractEntity
     * @param contractWriteOffDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createPurchaseContract(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO) {
        // 创建提货合同 注销数量
        contractWriteOffDTO.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        contractWriteOffDTO.setParentContractEntity(contractEntity);
        ContractEntity sonContractEntity = new ContractEntity();
        // 1.构建基础信息
        writeOffLogicService.buildBaseInfo(contractWriteOffDTO, sonContractEntity, true);
        sonContractEntity.setTradeType(ContractTradeTypeEnum.WRITE_OFF_C.getValue())
                .setContractNature(ContractNatureEnum.WAREHOUSE_DELIVERY.getValue())
                .setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum());
        // 2.构建修改的业务信息-有差异
        writeOffLogicService.buildBizInfoPurchase(contractWriteOffDTO, sonContractEntity);
        // 3.创建合同-计算有差异
        writeOffLogicService.createContract(contractWriteOffDTO, sonContractEntity);
        // 4.处理合同数据变更
        writeOffLogicService.operateFatherContract(contractWriteOffDTO, contractEntity);
        // 5.记录操作日志
        commonLogicService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.WARRANT_CONTRACT_WRITE_OFF, JSONUtil.toJsonStr(contractWriteOffDTO),
                SystemEnum.MAGELLAN.getValue());
        return sonContractEntity;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ContractEntity> createOMContract(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO, Integer contractNatrue) {
        List<ContractEntity> contractEntities = new ArrayList<>();
        // 1.创建豆粕合同
        ContractEntity omsContract = createOMSonContract(contractEntity, contractWriteOffOMDTO, "SBM", contractNatrue);
        // 2.创建豆油合同
        ContractEntity oliContract = createOMSonContract(contractEntity, contractWriteOffOMDTO, "SBO", contractNatrue);
        contractEntities.add(omsContract);
        contractEntities.add(oliContract);
        // 如果是场景二那么需要生成仓单贸易合同以及提货权合同
        if (ContractNatureEnum.WAREHOUSE_TRADE.getValue().equals(contractNatrue)) {
            contractEntities.add(createOMCargoRights(omsContract, contractWriteOffOMDTO));
            contractEntities.add(createOMCargoRights(oliContract, contractWriteOffOMDTO));
        }
        // 3.处理合同数据变更
        soybean2WriteOffLogicService.operateFatherContract(contractWriteOffOMDTO, contractEntity);
        // 4.记录操作日志
        commonLogicService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.WARRANT_CONTRACT_WRITE_OFF, JSONUtil.toJsonStr(contractWriteOffOMDTO),
                SystemEnum.MAGELLAN.getValue());
        return contractEntities;
    }

    @Override
    public void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO) {
        soybean2WriteOffLogicService.writeOffContractCheck(contractEntity, contractWriteOffOMDTO);
    }

    /**
     * 豆二注销生成提货合同|贸易合同
     *
     * @param contractEntity
     * @param contractWriteOffOMDTO
     * @param type
     * @param contractNatrue
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createOMSonContract(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO, String type, Integer contractNatrue) {
        // 创建提货合同 注销数量
        contractWriteOffOMDTO.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        contractWriteOffOMDTO.setParentContractEntity(contractEntity);
        ContractEntity sonContractEntity = new ContractEntity();
        // 1.构建基础信息
        soybean2WriteOffLogicService.buildBaseInfo(contractWriteOffOMDTO, sonContractEntity, type);
        if (ContractNatureEnum.WAREHOUSE_TRADE.getValue().equals(contractNatrue)) {
            sonContractEntity.setTradeType(ContractTradeTypeEnum.WRITE_OFF_OM_B.getValue());
        } else {
            sonContractEntity.setTradeType(ContractTradeTypeEnum.WRITE_OFF_OM_A.getValue());
        }
        sonContractEntity.setContractNature(contractNatrue);
        // 2.构建提货合同的业务信息
        soybean2WriteOffLogicService.buildBizInfo(contractWriteOffOMDTO, sonContractEntity, type);
        // 3.创建合同
        soybean2WriteOffLogicService.createContract(contractWriteOffOMDTO, sonContractEntity);
        return sonContractEntity;
    }

    /**
     * 豆二注销生成提货权合同
     *
     * @param contractEntity
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createOMCargoRights(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO) {
        ContractEntity cargoRights = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, cargoRights);
        String sonContractCode = contractQueryDomainService.genCargoRightsContractCode(contractEntity.getContractCode(), contractEntity.getSalesType());
        // 提货方的TT
        commonLogicService.updateCustomerInfo(contractEntity.getCustomerId(), contractEntity.getSupplierId(),
                contractWriteOffOMDTO.getCustomerId(), contractWriteOffOMDTO.getSupplierId(), cargoRights);
        cargoRights.setId(null)
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setContractNature(ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue())
                .setRepeatContractCode(sonContractCode)
                .setParentId(contractEntity.getId())
                .setRootId(contractEntity.getParentId() == 0 ? contractEntity.getId() : contractEntity.getParentId())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setCreateBatch(null);
        contractDomainService.saveContract(cargoRights);
        return cargoRights;
    }

    @Override
    public void writeOffWithDrawCheck(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        writeOffLogicService.writeOffWithDrawCheck(contractEntity, contractWriteOffWithDrawDTO);
    }

    @Override
    public void writeOffWithDraw(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        writeOffLogicService.writeOffWithDraw(contractEntity, contractWriteOffWithDrawDTO);
    }

    @Override
    public void warrantContractInvalid(ContractEntity contractEntity) {
        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        // 合同已注销量 =0
        if (BigDecimal.ZERO.compareTo(contractEntity.getWarrantCancelCount()) != 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_WARRANTNUM);
        }
        // 合同已回购量 =0
//        if (BigDecimal.ZERO.compareTo(contractEntity.getTotalBuyBackNum()) != 0) {
//            throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_BUYBACKNUM);
//        }
        // 如果是采购仓单合同作废,那么对应的仓单已经分配或者转让生成了销售合同是不能进行作废的
        if (ContractTradeTypeEnum.BUYBACK.getValue() != contractEntity.getTradeType()
                && ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
           List<ContractEntity> contractEntities = contractQueryDomainService.getContractByPurchase(contractEntity);
           if (ObjectUtil.isNotEmpty(contractEntities) && contractEntities.size() > 0) {
               throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_PURCHASE);
           }
        }


        // 校验Atlas的可提量 该合同可提量不等于合同作废量，无法进行合同作废 作废量就是合同数量
        if (openAtlasQuery == 1) {
            Result<BigDecimal> result = commonLogicService.getContractOpenQuantity(contractEntity);
            if (result.isSuccess()) {
                BigDecimal deliveredNum = result.getData().setScale(6, RoundingMode.HALF_UP);
                // 需要扣减下回购量在进行比较
                BigDecimal contractNum = contractEntity.getContractNum().subtract(contractEntity.getTotalBuyBackNum());
                if (contractNum.compareTo(deliveredNum) != 0) {
                    throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_ATLAS);
                }
            }
        }
        // 校验如果是定价中带审批需要提示盘面数据
        if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {
            boolean status = commonLogicService.getNotAllocateByContractId(contractEntity.getId());
            if (status) {
                throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_PRICE);
            }
        }
        // 执行作废
        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())) {
            contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
            contractDomainService.updateContractById(contractEntity);
        } else {
            // 更新仓单的数量【作废】
            commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_INVALID.getValue(), contractEntity.getContractNum(), null);
            contractEntity.setTotalPriceNum(BigDecimal.ZERO);
            contractEntity.setStatus(ContractStatusEnum.INVALID.getValue());
            contractDomainService.updateContractById(contractEntity);
        }
        // 记录操作日志
        LogBizCodeEnum code = LogBizCodeEnum.INVALID_SALES_CONTRACT;
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            code = LogBizCodeEnum.INVALID_PURCHASE_CONTRACT;
        }
        commonLogicService.addContractOperationLog(contractEntity, code, "", SystemEnum.MAGELLAN.getValue());
    }


    /********************************合同注销的业务逻辑处理 END****************************************/

    /**
     * 仓单合同关闭 | Atlas的调用
     * 合同状态更新为已关闭
     * NAV需校验，剩余可注销量=0
     * 符合条件需锁定此合同的已注销量及提货权，不计入提货委托量的计算，同步合同的状态为已关闭，可提货数量为；
     *
     * @param contractCode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity warrantContractClose(String contractCode) {
        return cancelLogicService.warrantContractClose(contractCode);
    }

    /**
     * 取消合同操作
     *
     * @param contractModifyDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelContractModify(ContractModifyDTO contractModifyDTO) {
        cancelLogicService.cancelContractModify(contractModifyDTO);
    }


    /**
     * 确认合规后合同操作
     *
     * @param contractModifyDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractConfirmResultDTO confirmContractModify(ContractModifyDTO contractModifyDTO) {
        return confirmLogicService.confirmContractModify(contractModifyDTO);
    }


    /**********合同本身的一些处理动作比如合同尾量关闭，合同定价，作废合同，补充信息等  Start    ******/

    @Override
    public boolean fillContract(ContractBaseDTO contractBaseDTO) {
        return operationLogicService.fillContract(contractBaseDTO);
    }

    @Override
    public boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO) {
        return operationLogicService.createTtPrice(confirmPriceDTO);
    }

    @Override
    public Boolean closeTailNumByContractId(Integer contractId, Integer triggerSys) {
        return operationLogicService.closeTailNumByContractId(contractId, triggerSys);
    }

    @Override
    public String batchCloseTailNum(List<Integer> contractIds, Integer triggerSys) {
        return operationLogicService.batchCloseTailNum(contractIds, triggerSys);
    }

    @Override
    public Boolean cancelCloseTailNumByContractId(Integer contractId) {
        return operationLogicService.cancelCloseTailNumByContractId(contractId);
    }

    @Override
    public String batchCancelCloseTailNum(List<Integer> contractIds) {
        return operationLogicService.batchCancelCloseTailNum(contractIds);
    }

    @Override
    public void closeContractStructureTT(ContractEntity contractEntity) {
        commonLogicService.closeContractStructureTT(contractEntity);
    }


    @Override
    public boolean updateContract(ContractEntity contractEntity) {
        return contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void transferMonthCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        transferLogicService.transferMonthCheck(contractEntity, contractTransferDTO);
    }

    @Override
    public ContractEntity createTransferContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext) {
        // 生成子合同
        ContractEntity childContractEntity = new ContractEntity();
        // 补充基本信息
        transferLogicService.buildBaseInfo(contractEntity, childContractEntity, contractTransferDTO);
        // 补充业务信息
        transferLogicService.buildBizInfo(contractTransferDTO, contractEntity, childContractEntity, arrangeContext);
        // 创建合同
        transferLogicService.createChildContract(contractTransferDTO, childContractEntity);

        return childContractEntity;
    }

    @Override
    public void updateTransferFatherContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, List<TTPriceEntity> ttPriceEntityList) {
        transferLogicService.updateTransferFatherContract(contractEntity, contractTransferDTO, ttPriceEntityList);
    }

    @Override
    public void reversePriceCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        transferLogicService.reversePriceCheck(contractEntity, contractTransferDTO);
    }

    @Override
    public void updatePriceFactorContract(BigDecimal newTotalPriceNum, ContractEntity contractEntity, List<TTPriceEntity> ttPriceEntityList) {
        // 全部定价
        if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), newTotalPriceNum)) {
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : ttPriceEntityList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                // 更新期货价格
                contractDomainService.updateContractForwardPrice(contractEntity, averagePrice);

                // 更新合同
                contractDomainService.updateContractById(contractEntity, String.valueOf(ContractTradeTypeEnum.PRICE.getValue()), "");
            }
            return;
        }
        contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void updateContractStatus(Integer contractId, TradeTicketEntity tradeTicketEntity) {
        // 原合同进入生效中
        ContractEntity buyBackParentContract = contractQueryDomainService.getBasicContractById(contractId);
        if (null != buyBackParentContract && buyBackParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
            int buyBackContractStatus = ContractStatusEnum.EFFECTIVE.getValue();
            // 如果是仓单客户进行回购的动作时候需要计算下注销量
            BigDecimal buyBackNum = buyBackParentContract.getTotalBuyBackNum();
            if (BuCodeEnum.WT.getValue().equals(buyBackParentContract.getBuCode())) {
                buyBackNum = buyBackNum.add(buyBackParentContract.getWarrantCancelCount());
            }
            if (BigDecimalUtil.isEqual(buyBackParentContract.getOrderNum(), buyBackNum)) {
                buyBackContractStatus = ContractStatusEnum.COMPLETED.getValue();
            }
            // 合同进入生效，备份合同信息
            contractDomainService.updateContractById(buyBackParentContract.setStatus(buyBackContractStatus)
                    , String.valueOf(tradeTicketEntity.getTradeType()), tradeTicketEntity.getCode());
            //  同步到Atlas | LKG 更新的动作
            commonLogicService.syncContractInfo(buyBackParentContract, tradeTicketEntity.getId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
        }
    }


    /**********合同本身的一些处理动作比如合同定价，作废合同，补充信息等  end    ******/

    /**
     * 是否变更主体
     *
     * @param contractEntity    合同数据
     * @param contractModifyDTO 修改数据
     * @return
     */
    protected boolean isCustomerChanged(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        return isSales ?
                !contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId()) :
                !contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId());
    }

    @Override
    public void updateBuyBackContract(Integer contractId, BigDecimal buyBackNum) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractId);
        contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
        contractEntity.setTotalBuyBackNum(contractEntity.getTotalBuyBackNum().subtract(buyBackNum));
        contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void invalidContractByTT(ContractBaseDTO contractBaseDTO) {
        // 校验合同
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractBaseDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 更新状态
        contractDomainService.updateContractById(contractEntity
                .setStatus(ContractStatusEnum.INVALID.getValue())
                .setInvalidReason(contractBaseDTO.getInvalidReason()));
        // 记录操作日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
    }

    @Override
    public void processTransferMonthPrice(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext) {
        // 处理转让月份的价格
        transferLogicService.processTransferMonthPrice(contractEntity, contractTransferDTO, arrangeContext);
    }

}
