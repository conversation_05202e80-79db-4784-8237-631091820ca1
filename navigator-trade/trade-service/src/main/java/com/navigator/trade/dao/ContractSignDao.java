package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.enums.EarlyWarningEnum;
import com.navigator.trade.mapper.ContractSignMapper;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:46
 */
@Dao
public class ContractSignDao extends BaseDaoImpl<ContractSignMapper, ContractSignEntity> {

    public IPage<ContractSignEntity> queryContractSign(QueryDTO<QueryContractSignBO> queryDTO,
                                                       List<Integer> customerIds, List<Integer> belongCustomerIdList) {

        ObjectMapper mapper = new ObjectMapper();
        QueryContractSignBO contractSignBo = mapper.convertValue(queryDTO.getCondition(), QueryContractSignBO.class);
        String warningType = contractSignBo.getWarningType();
        LambdaQueryWrapper<ContractSignEntity> wrapper = new LambdaQueryWrapper<ContractSignEntity>();
        wrapper.eq(contractSignBo.getLdcFrame() != null, ContractSignEntity::getLdcFrame, contractSignBo.getLdcFrame());
        wrapper.eq(contractSignBo.getTtType() != null, ContractSignEntity::getTtType, contractSignBo.getTtType());
        wrapper.and(StrUtil.isNotBlank(contractSignBo.getContractCode()),
                bizWrapper -> bizWrapper.like(ContractSignEntity::getContractCode, "%" + (StrUtil.isNotBlank(contractSignBo.getContractCode()) ? contractSignBo.getContractCode().trim() : contractSignBo.getContractCode()) + "%")
                        .or().like(ContractSignEntity::getTtCode, "%" + (StrUtil.isNotBlank(contractSignBo.getContractCode()) ? contractSignBo.getContractCode().trim() : contractSignBo.getContractCode()) + "%"));
        wrapper.eq(contractSignBo.getGoodsId() != null, ContractSignEntity::getGoodsId, contractSignBo.getGoodsId());
        wrapper.eq(StringUtils.isNotBlank(warningType) && warningType.equals(EarlyWarningEnum.ORIGINAL_PAPER.getValue().toString()), ContractSignEntity::getNeedOriginalPaper, warningType);
        wrapper.eq(StringUtils.isNotBlank(warningType) && warningType.equals(EarlyWarningEnum.LDC_ORIGINAL_PAPER.getValue().toString()), ContractSignEntity::getLdcNeedOriginalPaper, warningType);
        wrapper.eq(null != contractSignBo.getSalesType(), ContractSignEntity::getSalesType, contractSignBo.getSalesType());
        wrapper.eq(null != contractSignBo.getGoodsCategoryId(), ContractSignEntity::getGoodsCategoryId, contractSignBo.getGoodsCategoryId());
        wrapper.eq(StrUtil.isNotBlank(contractSignBo.getProtocolCode()), ContractSignEntity::getProtocolCode, contractSignBo.getProtocolCode());
        wrapper.eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        wrapper.between(StrUtil.isNotBlank(contractSignBo.getSignDate()), ContractSignEntity::getCreatedAt, contractSignBo.getSignDate() + " 00:00:00.000", contractSignBo.getSignDate() + " 23:59:59.000");
        wrapper.between(StrUtil.isNotBlank(contractSignBo.getStartSignDate()) && StrUtil.isNotBlank(contractSignBo.getEndSignDate()), ContractSignEntity::getCreatedAt, contractSignBo.getStartSignDate() + " 00:00:00.000", contractSignBo.getEndSignDate() + " 23:59:59.000");
        wrapper.in(!contractSignBo.getDeliveryFactoryCode().isEmpty(), ContractSignEntity::getDeliveryFactoryCode, contractSignBo.getDeliveryFactoryCode());//                .like(StrUtil.isNotBlank(contractSignBo.getContractCode()), ContractSignEntity::getContractCode, "%"+contractSignBo.getContractCode()+"%")
//.like(StrUtil.isNotBlank(contractSignBo.getCustomerName()), ContractSignEntity::getCustomerName, "%" + contractSignBo.getCustomerName() + "%")

        // 哥伦布
        if (SystemEnum.COLUMBUS.getValue() == contractSignBo.getSystem()) {

            // 按照状态查询
            if (StringUtil.isNotEmpty(contractSignBo.getStatus())) {
                // (【已完成】状态对应Magellan【正本】&【已完成】2个状态)
                if (Integer.parseInt(contractSignBo.getStatus()) == ContractSignStatusEnum.PROCESSING.getValue()) {
                    wrapper.in(ContractSignEntity::getStatus, Arrays.asList(ContractSignStatusEnum.PROCESSING.getValue(), ContractSignStatusEnum.PAPER.getValue()));
                } else {
                    wrapper.eq(ContractSignEntity::getStatus, contractSignBo.getStatus());
                }
            } else {
                wrapper.in(ContractSignEntity::getStatus, Arrays.asList(ContractSignStatusEnum.WAIT_BACK.getValue(),
                        ContractSignStatusEnum.WAIT_CONFIRM.getValue(), ContractSignStatusEnum.PROCESSING.getValue(),
                        ContractSignStatusEnum.PAPER.getValue(), ContractSignStatusEnum.INVALID.getValue()));
            }
        } else {
            // 麦哲伦
            if (StringUtil.isNotEmpty(contractSignBo.getStatus())) {
                wrapper.eq(ContractSignEntity::getStatus, contractSignBo.getStatus());
            } else {
                wrapper.notIn(ContractSignEntity::getStatus, Arrays.asList(ContractSignStatusEnum.INVALID.getValue()));
            }
            wrapper.in(ContractSignEntity::getBelongCustomerId, belongCustomerIdList);
        }

        // 客户id
        if (ContractSalesTypeEnum.SALES.getValue() == contractSignBo.getSalesType()) {
            wrapper.in(CollectionUtil.isNotEmpty(customerIds), ContractSignEntity::getCustomerId, customerIds);
            wrapper.eq(null != contractSignBo.getCustomerId(), ContractSignEntity::getCustomerId, contractSignBo.getCustomerId());
        } else {
            wrapper.in(CollectionUtil.isNotEmpty(customerIds), ContractSignEntity::getSupplierId, customerIds);
            wrapper.eq(null != contractSignBo.getCustomerId(), ContractSignEntity::getSupplierId, contractSignBo.getCustomerId());
        }

        if (StrUtil.isNotBlank(contractSignBo.getStatus()) && Arrays.asList(ContractSignStatusEnum.WAIT_PROVIDE.getValue(), ContractSignStatusEnum.WAIT_BACK.getValue()).contains(Integer.parseInt(contractSignBo.getStatus()))) {
            wrapper.orderByDesc(ContractSignEntity::getUpdatedAt, ContractSignEntity::getId);
        } else {
            wrapper.orderByDesc(ContractSignEntity::getId, ContractSignEntity::getUpdatedAt);
        }
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
    }

    /**
     * 根据唯一编号查询协议信息
     *
     * @param uuid
     * @returnz
     */
    public ContractSignEntity queryByUUId(String uuid) {
        return this.getOne(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getUuid, uuid)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public ContractSignEntity queryByTTCode(String ttCode) {
        return this.getOne(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getTtCode, ttCode)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据合同编号，查询协议信息
     *
     * @param contractCode 合同编号
     * @return
     */
    public List<ContractSignEntity> queryByContractCode(String contractCode) {
        return this.list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getContractCode, contractCode)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(ContractSignEntity::getId)
        );
    }

    /**
     * 根据合同编号，查询协议信息
     *
     * @param contractId 合同编号
     * @return
     */
    public List<ContractSignEntity> queryByContractId(Integer contractId) {
        return this.list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getContractId, contractId)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())

        );
    }

    /**
     * 根据ttId，查询协议详情
     *
     * @param ttId ttId
     * @return 协议详情信息
     */
    public ContractSignEntity getSignDetailByTtId(Integer ttId) {
        List<ContractSignEntity> signEntityList = list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getTtId, ttId)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

        return signEntityList.isEmpty() ? null : signEntityList.get(0);
    }

    /**
     * 统计数量
     *
     * @param status     协议状态
     * @param ldcFrame   是否为non-frame模版
     * @param customerId 客户ID
     * @return
     */
    public List<ContractSignEntity> getContractSignList(Integer status, Integer ldcFrame, Integer customerId, Integer salesType,
                                                        Integer goodsCategoryId, List<Integer> belongCustomerIdList) {
        return this.list(new LambdaQueryWrapper<ContractSignEntity>()
                .eq(null != customerId, ContractSignEntity::getCustomerId, customerId)
                .eq(null != ldcFrame, ContractSignEntity::getLdcFrame, ldcFrame)
                .eq(null != salesType, ContractSignEntity::getSalesType, salesType)
                .eq(null != goodsCategoryId, ContractSignEntity::getCategory2, goodsCategoryId)
                .eq(null != status, ContractSignEntity::getStatus, status)
                .in(!belongCustomerIdList.isEmpty(), ContractSignEntity::getBelongCustomerId, belongCustomerIdList)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 统计数量
     *
     * @param status     协议状态
     * @param ldcFrame   是否为non-frame模版
     * @param customerId 客户ID
     * @return
     */
    public Integer getContractSignStat(Integer status, Integer ldcFrame, Integer customerId, Integer salesType,
                                       Integer goodsCategoryId, List<String> siteCodeList) {
        if (CollectionUtils.isEmpty(siteCodeList)) {
            return 0;
        }
        return this.count(new LambdaQueryWrapper<ContractSignEntity>()
                .eq(null != customerId, ContractSignEntity::getCustomerId, customerId)
                .eq(null != ldcFrame, ContractSignEntity::getLdcFrame, ldcFrame)
                .eq(null != salesType, ContractSignEntity::getSalesType, salesType)
                .eq(null != goodsCategoryId, ContractSignEntity::getCategory2, goodsCategoryId)
                .eq(null != status, ContractSignEntity::getStatus, status)
                .in(!siteCodeList.isEmpty(), ContractSignEntity::getSiteCode, siteCodeList)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 更新sign中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    public int updateContractId(Integer ttId, Integer contractId) {
        return update(Wrappers.<ContractSignEntity>lambdaUpdate()
                .set(ContractSignEntity::getContractId, contractId)
                .eq(ContractSignEntity::getTtId, ttId)) ? 1 : 0;
    }

    public ContractSignAllStatusNumVO getColumbusContractSignStat(QueryContractSignBO signBO) {
        return baseMapper.getColumbusContractSignStat(signBO);
    }

    public ContractSignAllStatusNumVO getMagellanContractSignStat(QueryContractSignBO signBO) {
        return baseMapper.getMagellanContractSignStat(signBO);
    }

    public List<ContractSignEntity> queryIncompleteByContractId(List<Integer> ids, List<Integer> ttTypeList) {
        LambdaQueryWrapper<ContractSignEntity> wrapper = Wrappers.<ContractSignEntity>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(ttTypeList), ContractSignEntity::getTtType, ttTypeList)
                .notIn(ContractSignEntity::getTtType, Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()))
                .in(ContractSignEntity::getContractId, ids)
                .in(ContractSignEntity::getStatus, Arrays.asList(ContractSignStatusEnum.WAIT_PROVIDE.getValue(),
                        ContractSignStatusEnum.WAIT_REVIEW.getValue(), ContractSignStatusEnum.WAIT_STAMP.getValue(),
                        ContractSignStatusEnum.WAIT_BACK.getValue(), ContractSignStatusEnum.WAIT_CONFIRM.getValue()))
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(wrapper);
    }

    public List<ContractSignEntity> querySignListByContractId(Integer contractId, List<Integer> ttStatusList) {
        LambdaQueryWrapper<ContractSignEntity> wrapper = Wrappers.<ContractSignEntity>lambdaQuery()
                .notIn(ContractSignEntity::getTtType, Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()))
                .eq(ContractSignEntity::getContractId, contractId)
                .in(CollectionUtil.isNotEmpty(ttStatusList), ContractSignEntity::getStatus, ttStatusList)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(wrapper);
    }

    public List<ContractSignEntity> querySonContractSplitIncomplete(List<Integer> ids) {
        LambdaQueryWrapper<ContractSignEntity> wrapper = Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getTtType, TTTypeEnum.SPLIT.getType())
                .in(ContractSignEntity::getContractId, ids)
                .in(ContractSignEntity::getStatus, Arrays.asList(ContractSignStatusEnum.WAIT_PROVIDE.getValue(),
                        ContractSignStatusEnum.WAIT_REVIEW.getValue(), ContractSignStatusEnum.WAIT_STAMP.getValue(),
                        ContractSignStatusEnum.WAIT_BACK.getValue(), ContractSignStatusEnum.WAIT_CONFIRM.getValue()))
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(ContractSignEntity::getTradeType, ContractTradeTypeEnum.NEW.getValue());
        return this.list(wrapper);
    }

    public List<ContractSignEntity> sendContractSignOriginalPaper(List<Integer> contractSingId) {

        return this.list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getStatus, ContractSignStatusEnum.PAPER.getValue())
                .in(ContractSignEntity::getId, contractSingId)
                .groupBy(ContractSignEntity::getCustomerId, ContractSignEntity::getSupplierId, ContractSignEntity::getSalesType, ContractSignEntity::getGoodsCategoryId)
                .select(ContractSignEntity::getCustomerId, ContractSignEntity::getSupplierId, ContractSignEntity::getSalesType, ContractSignEntity::getGoodsCategoryId)
        );

    }

    public List<ContractSignEntity> queryContractSignByIdSCustomerIdSalesType(List<Integer> contractSingId, Integer customerId, String supplierId, Integer goodsCategoryId) {

        return this.list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getStatus, ContractSignStatusEnum.PAPER.getValue())
                .in(ContractSignEntity::getId, contractSingId)
                .eq(null != customerId, ContractSignEntity::getCustomerId, customerId)
                .eq(StrUtil.isNotEmpty(supplierId), ContractSignEntity::getSupplierId, supplierId)
                .eq(ContractSignEntity::getGoodsCategoryId, goodsCategoryId)
        );

    }

    public ContractSignEntity queryContractSignByContractCode(String contractCode) {
        List<ContractSignEntity> contractSignEntities = this.list(Wrappers.<ContractSignEntity>lambdaQuery()
                .eq(ContractSignEntity::getContractCode, contractCode)
                .eq(ContractSignEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(ContractSignEntity::getId)
        );
        return contractSignEntities.isEmpty() ? null : contractSignEntities.get(0);
    }

    public boolean updateSignWarrantId(Integer signId, Integer warrantId, String warrantCode) {
        return update(Wrappers.<ContractSignEntity>lambdaUpdate()
                .set(ContractSignEntity::getWarrantId, warrantId)
                .set(ContractSignEntity::getWarrantCode, warrantCode)
                .eq(ContractSignEntity::getId, signId));
    }
}
