package com.navigator.trade.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 价格明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface IContractPriceService {

    Result calculatePrice(PriceDetailBO priceDetailBO);

    BigDecimal calculatePriceBo(PriceDetailBO priceDetailBO);
    /**
     * 计算合同的含税单价
     *
     * @param priceDetailDTO
     * @param taxRate
     * @return
     */
    ContractUnitPriceVO calcContractUnitPrice(PriceDetailBO priceDetailDTO, BigDecimal taxRate);

    BigDecimal getValue(PriceDetailDTO priceDetailDTO);

    /**
     * 更新ContractPrice中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    int updateContractId(Integer ttId, Integer contractId);

    /**
     * 获取ContractPrice
     *
     * @param contractId
     * @return
     */
    ContractPriceEntity getContractPriceEntityContractId(Integer contractId);

    List<ContractPriceEntity> getContractPriceListContractId(Integer contractId);

    ContractPriceEntity getContractPriceEntityByTTId(Integer ttId);

    void saveOrUpdate(ContractPriceEntity contractPriceEntity);

    void save(ContractPriceEntity contractPriceEntity);

    /**
     * 根据合同id修改price
     *
     * @param contractPriceEntity
     * @return
     */
    boolean updatePriceByContractId(ContractPriceEntity contractPriceEntity);

    /**
     * 更新合同的期货价格
     *
     * @param contractEntity 合同实体
     * @param forwardPrice   期货价格
     * @return
     */
    ContractEntity updateContractForwardPrice(ContractEntity contractEntity, BigDecimal forwardPrice);


    /**
     * 同步合同单价数据
     * @param contractPriceEntity
     * @return
     */
    boolean syncContractPrice(ContractPriceEntity contractPriceEntity);
}
