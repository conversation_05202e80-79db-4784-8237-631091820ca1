package com.navigator.trade.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.trade.facade.ContractCheckFacade;
import com.navigator.trade.pojo.dto.contract.ContractCheckDTO;
import com.navigator.trade.service.check.ICheckContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 合同校验的接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/25
 */
@RestController
public class ContractCheckFacadeImpl implements ContractCheckFacade {


    @Autowired
    private ICheckContractService checkContractService;

    @Override
    public Result checkContract(ContractCheckDTO contractCheckDTO) {
        checkContractService.checkContract(contractCheckDTO.getContractCodeList(),
                contractCheckDTO.getIsOpenSync(), contractCheckDTO.getStartDateTime(), contractCheckDTO.getEndDateTime());
        return Result.success();
    }

    @Override
    public Result checkLkgContract(ContractCheckDTO contractCheckDTO) {
        checkContractService.checkLkgContractByCodeList(contractCheckDTO.getContractCodeList());
        return Result.success();
    }

    @Override
    public Result retryCheckContractByBatch(ContractCheckDTO contractCheckDTO) {
        checkContractService.retryCheckContractByBatch(contractCheckDTO.getContractCodeList(),
                contractCheckDTO.getIsOpenSync(),
                contractCheckDTO.getCheckBatch());
        return Result.success();
    }

    @Override
    public Result checkDailyContract() {
        checkContractService.checkDailyContract();
        return Result.success();
    }
}
