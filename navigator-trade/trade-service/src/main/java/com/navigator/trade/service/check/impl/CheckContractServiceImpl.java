package com.navigator.trade.service.check.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import com.navigator.pigeon.pojo.enums.LkgPriceFixStatusEnum;
import com.navigator.trade.dao.CheckContractDao;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.check.ICheckContractService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.futrue.ICustomerFutureContractService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 合同校验
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/18
 */
@Slf4j
@Service
public class CheckContractServiceImpl implements ICheckContractService {
    @Autowired
    private IContractQueryService contractQueryService;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private ICustomerFutureContractService customerFutureContractService;
    @Autowired
    private LkgContractFacade lkgContractFacade;
    @Autowired
    private ITtPriceService ttPriceService;
    @Autowired
    private IContractSignQueryService contractSignQueryService;
    @Autowired
    private CheckContractDao checkContractDao;
    @Autowired
    private ITradeTicketQueryService tradeTicketQueryService;

    @Override
    public void checkContract(List<String> contractCodeList, Integer isOpenSync, String startDateTime, String endDateTime) {
        // 校验批次号
        String checkBatch = "BATCH" + DateTimeUtil.formatDateTimeValue();

        // 获取特定时间的合同
        List<ContractEntity> contractEntityList = contractQueryService.getContractList(contractCodeList, startDateTime, endDateTime);

        for (ContractEntity contractEntity : contractEntityList) {

            CheckContractEntity checkContractEntity = getCheckContractEntity(isOpenSync, startDateTime, endDateTime, checkBatch, contractEntity);

            // 保存校验信息
            saveCheckInfo(checkContractEntity);

        }
    }

    private CheckContractEntity getCheckContractEntity(Integer isOpenSync, String startDateTime, String endDateTime, String checkBatch, ContractEntity contractEntity) {
        // 初始化校验信息
        CheckContractEntity checkContractEntity = initCheckContract(contractEntity, checkBatch, startDateTime, endDateTime);

        // 校验基础信息
        checkBaseInfo(contractEntity, checkContractEntity);

        // 校验接口调用情况
        checkLkgRecord(contractEntity, checkContractEntity);

        // 校验lkg信息
        checkLkgInfo(contractEntity, isOpenSync, checkContractEntity);

        return checkContractEntity;
    }

    @Override
    public void retryCheckContractByBatch(List<String> contractCodeList, Integer isOpenSync, String batch) {
        List<CheckContractEntity> checkContractEntityList = checkContractDao.getByBatchAndContractCode(contractCodeList, batch);
        for (CheckContractEntity checkContractEntity : checkContractEntityList) {
            ContractEntity contractEntity = contractQueryService.getContractById(checkContractEntity.getContractId());
            CheckContractEntity checkContract = getCheckContractEntity(isOpenSync, null, null, checkContractEntity.getCheckBatch(), contractEntity);

            String status;
            if (checkContract.getBaseInfoResult().equals("NORMAL") &&
                    checkContract.getLkgRecordResult().equals("NORMAL")) {
                status = "NORMAL";
            } else {
                status = "EXCEPTION";
            }
            checkContract.setStatus(status);

            checkContractDao.updateById(checkContract.setId(checkContractEntity.getId()).setStatus(status));
        }
    }

    @Override
    public void checkLkgContractByCodeList(List<String> contractCodeList) {
        // 校验批次号
        String checkBatch = "BATCH" + DateTimeUtil.formatDateTimeValue();

        for (String contractCode : contractCodeList) {
            ContractEntity contractEntity = contractQueryService.getBasicContractByCode(contractCode);

            CheckContractEntity checkContractEntity = initCheckLkgContract(contractEntity, checkBatch);

            // 校验lkg信息
            checkLkgInfo(contractEntity, DisableStatusEnum.ENABLE.getValue(), checkContractEntity);

            // 保存校验信息
            saveCheckInfo(checkContractEntity);
        }
    }

    @Override
    public void checkDailyContract() {
        checkContract(null, 1,
                DateTimeUtil.formatDateTimeString00(DateTimeUtil.now()),
                DateTimeUtil.formatDateTimeString24(DateTimeUtil.now()));
    }

    /**
     * 校核合同基础信息
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkBaseInfo(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        // 校验数量
        checkContractNum(contractEntity, checkContractEntity);

        // 校验价格
        checkContractPrice(contractEntity, checkContractEntity);

        // 校验合同状态
        checkContractStatus(contractEntity, checkContractEntity);

        // 校验合同转月次数
        checkContractTransferTimes(contractEntity, checkContractEntity);

        // 校验其他基本信息
        chekOtherInfo(contractEntity, checkContractEntity);

        String baseInfoResult = StringUtils.isNotBlank(checkContractEntity.getBaseInfoResult())
                ? String.valueOf(checkContractEntity.getBaseInfoResult()) : "NORMAL";
        checkContractEntity.setBaseInfoResult(baseInfoResult);

        log.info("合同编号:{},其他基础信息校验完毕", contractEntity.getContractCode());

    }

    /**
     * 校验其他基本信息
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void chekOtherInfo(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        // 税率为0
        if (BigDecimalUtil.isEqual(contractEntity.getTaxRate(), new BigDecimal("0"))) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "taxRate为0；\n");
            log.error("合同编号:{},taxRate为0", contractEntity.getContractCode());
        }

        // 一口价合同追加履约保证金不为0
        if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue()) && contractEntity.getAddedDepositRate() != 0) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "追加履约保证金比例异常；\n");
            log.error("合同编号:{},追加履约保证金比例异常", contractEntity.getContractCode());
        }

        // 履约保证金超过150
        if (contractEntity.getDepositRate() > 150) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "履约保证金比例异常；\n");
            log.error("合同编号:{},履约保证金比例异常", contractEntity.getContractCode());
        }
    }

    /**
     * 校验接口调用情况
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkLkgRecord(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        // 合同的同步：合同状态 → 协议确认 → 触发lkg
        // 获取所有确认合规的协议
        List<ContractSignEntity> signEntityList = contractSignQueryService
                .querySignListByContractId(contractEntity.getId(),
                        Arrays.asList(ContractSignStatusEnum.PAPER.getValue(), ContractSignStatusEnum.PROCESSING.getValue()));

        // 是否触发lkg接口
        for (ContractSignEntity contractSignEntity : signEntityList) {
            // 解约定赔不同步接口
            if (!contractSignEntity.getTtType().equals(TTTypeEnum.WASHOUT.getType())) {
                checkRequestInfo(contractSignEntity.getContractCode(), contractSignEntity.getTtId(), checkContractEntity);
            }
        }

        // 定价单的同步 1.定价更新 2.拆分定价单 FIXME 拆分定价单只更新了原合同的定价单为0，新合同的定价单未同步
        /*List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            Integer ttId = ttPriceEntity.getTtId();
            if (null != ttId && 0 != ttId) {
                checkRequestInfo(contractEntity.getContractCode(), ttId, checkContractEntity);
            }
        }*/
        String lkgRecordResult = StringUtils.isNotBlank(checkContractEntity.getLkgRecordResult())
                ? checkContractEntity.getLkgRecordResult() : "NORMAL";
        checkContractEntity.setLkgRecordResult(lkgRecordResult);
    }

    /**
     * 校验LKG接口数据是否同步
     *
     * @param contractCode
     * @param ttId
     * @param checkContractEntity
     */
    private void checkRequestInfo(String contractCode, Integer ttId, CheckContractEntity checkContractEntity) {

        // 查询request是否存在记录 FIXME 线上存在一批更新父合同的定价单传的是子合同的tt
        Result priceRequestResult = lkgContractFacade.getLkgRequestByTtId(ttId);
        List<LkgSyncRequestEntity> requestEntityList = JSON.parseArray(JSON.toJSONString(priceRequestResult.getData()), LkgSyncRequestEntity.class);
        if (CollectionUtil.isEmpty(requestEntityList)) {
            checkContractEntity.setLkgRecordResult(StringUtils.isNotBlank(checkContractEntity.getLkgRecordResult()) ? checkContractEntity.getLkgRecordResult() : ""
                    + "Request同步异常,ttId:" + ttId + "；\n");
            log.error("合同编号:{},Request同步异常,ttId:{}", contractCode, ttId);
            return;
        }

        // 根据requestId查询record记录的状态
        for (LkgSyncRequestEntity lkgSyncRequestEntity : requestEntityList) {
            Result priceRecordResult = lkgContractFacade.getLkgRecordByRequestId(lkgSyncRequestEntity.getId());

            List<LkgSyncRecordEntity> recordEntityList = JSON.parseArray(JSON.toJSONString(priceRecordResult.getData()), LkgSyncRecordEntity.class);
            if (CollectionUtil.isEmpty(recordEntityList)) {
                checkContractEntity.setLkgRecordResult(StringUtils.isNotBlank(checkContractEntity.getLkgRecordResult()) ? checkContractEntity.getLkgRecordResult() : ""
                        + "Record同步异常,requestId:" + lkgSyncRequestEntity.getId() + "；\n");
                log.error("合同编号:{},Record同步异常,requestId:{}", contractCode, lkgSyncRequestEntity.getId());
                return;
            }
        }
    }

    /**
     * 校验LKG接口数据：数量、状态
     *
     * @param contractEntity
     * @param isOpenSync          是否开启lkg查询
     * @param checkContractEntity
     */
    private void checkLkgInfo(ContractEntity contractEntity, Integer isOpenSync, CheckContractEntity checkContractEntity) {
        if (null != isOpenSync && isOpenSync.equals(DisableStatusEnum.ENABLE.getValue())) {
            // 已提数量
            BigDecimal lkgDeliveryNum = BigDecimal.ZERO;
            // 已开单数量
            BigDecimal lkgBillNum = BigDecimal.ZERO;

            Result result = lkgContractFacade.getLkgContract(contractEntity.getContractCode());
            if (null != result && result.getCode() == ResultCodeEnum.OK.code()) {
                if (null != result.getData()) {
                    StringBuilder lkgInfo = new StringBuilder();

                    LkgContractDTO lkgContractDTO = JSON.parseObject(JSON.toJSONString(result.getData()), LkgContractDTO.class);
                    if (null != lkgContractDTO && null != lkgContractDTO.getStatus() && lkgContractDTO.getStatus() != -1) {

                        // 合同数量的校核
                        if (null != lkgContractDTO.getCount() &&
                                !BigDecimalUtil.isEqual(contractEntity.getContractNum(), BigDecimal.valueOf(lkgContractDTO.getCount()))) {
                            lkgInfo.append("LKG合同数量不一致；\n");
                            log.error("合同编号:{},与LKG合同数量不一致,nav:{},lkg:{}", contractEntity.getContractCode(),
                                    contractEntity.getContractNum(), BigDecimal.valueOf(lkgContractDTO.getCount()));
                        }

                        // lkg数据
                        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                            lkgDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getContractFactOutCount());
                            lkgBillNum = BigDecimal.valueOf(lkgContractDTO.getContractOutCount());
                        } else {
                            lkgDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getInCount());
                            lkgBillNum = BigDecimal.valueOf(lkgContractDTO.getOrderCount());
                        }

                        // 开单量
                        if (BigDecimalUtil.isGreater(lkgBillNum, contractEntity.getContractNum())) {
                            lkgInfo.append("LKG开单量异常；\n");
                            log.error("合同编号:{},LKG开单量异常", contractEntity.getContractCode());
                        }

                        // 提货量
                        if (BigDecimalUtil.isGreater(lkgDeliveryNum, contractEntity.getContractNum())) {
                            lkgInfo.append("LKG提货量异常；\n");
                            log.error("合同编号:{},LKG提货量异常", contractEntity.getContractCode());
                        }

                        // 定价状态
                        int navFixStatus = LkgPriceFixStatusEnum.getByContractNum(contractEntity.getContractType(), contractEntity.getContractNum(), contractEntity.getTotalPriceNum()).getValue();
                        if (lkgContractDTO.getFixStatus() != navFixStatus) {
                            lkgInfo.append("LKG定价状态异常；\n");
                            log.error("合同编号:{},LKG定价状态异常", contractEntity.getContractCode());
                        }

                        // 合同状态-仅关闭
                        if (ContractStatusEnum.CLOSED.getValue() == lkgContractDTO.getStatus() && !lkgContractDTO.getStatus().equals(contractEntity.getStatus())) {
                            lkgInfo.append("LKG关闭状态异常；\n");
                            log.error("合同编号:{},LKG关闭状态异常", contractEntity.getContractCode());
                        }

                        checkContractEntity.setContractNum(contractEntity.getContractNum())
                                .setLkgContractNum(BigDecimal.valueOf(lkgContractDTO.getCount()))
                                .setLkgDeliveryNum(lkgDeliveryNum)
                                .setLkgBillNum(lkgBillNum)
                                .setNavStatus(contractEntity.getStatus())
                                .setLkgStatus(lkgContractDTO.getStatus())
                                .setLkgInfoResult(String.valueOf(lkgInfo))
                                .setLkgResultInfo(JSON.toJSONString(lkgContractDTO));

                        log.info("合同编号:{},LKG数据校验完毕", contractEntity.getContractCode());
                    } else {
                        log.error("合同编号:{},获取LKG信息异常", contractEntity.getContractCode());
                    }
                }
            }
        }

        checkContractEntity.setLkgInfoResult(StringUtils.isNotBlank(checkContractEntity.getLkgInfoResult())
                ? checkContractEntity.getLkgInfoResult() : "INIT");
    }

    /**
     * 校验合同状态
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkContractStatus(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        // 1.nav合同状态校验

        // 原合同的状态
        checkContractEntity.setNavStatus(contractEntity.getStatus());

        // 校验合同状态
        int newStatus = contractEntity.getStatus();
        List<ContractSignEntity> signEntityList = contractSignQueryService
                .queryIncompleteByContractId(Collections.singletonList(contractEntity.getId()), null);

        if (CollectionUtil.isEmpty(signEntityList)) {
            if (BigDecimalUtil.isZero(contractEntity.getContractNum())) {
                newStatus = ContractStatusEnum.COMPLETED.getValue();
            } else {
                newStatus = ContractStatusEnum.EFFECTIVE.getValue();
            }
        } else {
            for (ContractSignEntity contractSignEntity : signEntityList) {
                switch (TTTypeEnum.getByType(contractSignEntity.getTtType())) {
                    case NEW:
                        newStatus = ContractStatusEnum.INEFFECTIVE.getValue();
                        break;
                    case REVISE:
                        // 目前在修改中，待定
                    case TRANSFER:
                    case WASHOUT:
                    case BUYBACK:
                        newStatus = ContractStatusEnum.MODIFYING.getValue();
                        break;
                    case SPLIT:
                        if (contractSignEntity.getTradeType() == ContractTradeTypeEnum.NEW.getValue()) {
                            newStatus = ContractStatusEnum.INEFFECTIVE.getValue();
                        } else {
                            newStatus = ContractStatusEnum.SPLITTING.getValue();
                        }
                        break;
                    case CLOSED:
                        newStatus = ContractStatusEnum.CLOSING.getValue();
                        break;
                    default:
                        break;
                }
            }
        }
        if (contractEntity.getStatus() != newStatus) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "合同状态异常；\n");
            log.error("合同编号:{},合同状态异常", contractEntity.getContractCode());
        }

        checkContractEntity.setCalcStatus(newStatus);

        // 2.lkg合同状态校验
        log.info("合同编号:{},合同状态校验完毕", contractEntity.getContractCode());
    }

    /**
     * 校核合同数量: 1.合同拆分的汇集 2.lkg获取的数据
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkContractNum(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        StringBuilder baseInfo = new StringBuilder();

        // 1.合同拆分的汇集(涉及合同拆分) 拆分+主体修改+部分转月+反点价
        List<ContractEntity> contractEntityList = contractQueryService.getContractByPid(contractEntity.getId());

        BigDecimal splitContractNum = contractEntityList.stream()
                .filter(entity -> entity.getIsDeleted().equals(IsDeletedEnum.NOT_DELETED.getValue())
                        && ContractStatusEnum.INVALID.getValue() != entity.getStatus())
                .map(ContractEntity::getOrderNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 解约定赔
        List<TradeTicketEntity> ticketEntityList = tradeTicketQueryService.getByContractId(contractEntity.getId());
        BigDecimal washOutContractNum = ticketEntityList.stream()
                .filter(entity -> entity.getIsDeleted().equals(IsDeletedEnum.NOT_DELETED.getValue())
                        && TTTypeEnum.WASHOUT.getType().equals(entity.getType()))
                .map(TradeTicketEntity::getChangeContractNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        BigDecimal newContractNum = splitContractNum.add(washOutContractNum).add(contractEntity.getContractNum());
        if (!BigDecimalUtil.isEqual(contractEntity.getOrderNum(), newContractNum)) {
            baseInfo.append("OrderNum不一致；\n");
            log.error("合同编号:{},OrderNum不一致", contractEntity.getContractCode());
        }

        // 2.定价数量的校验 - 一口价不做校验（基差转一口价）
        BigDecimal newTotalPriceNum = contractEntity.getTotalPriceNum();
        if (contractEntity.getContractType() != ContractTypeEnum.YI_KOU_JIA.getValue()) {
            List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
            newTotalPriceNum = confirmPriceList.stream()
                    .map(TTPriceEntity::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!BigDecimalUtil.isEqual(contractEntity.getTotalPriceNum(), newTotalPriceNum)) {
                baseInfo.append("TotalPriceNum不一致；\n");
                log.error("合同编号:{},TotalPriceNum不一致", contractEntity.getContractCode());
            }
        }

        checkContractEntity.setOrderNum(contractEntity.getOrderNum())
                .setCalcOrderNum(newContractNum)
                .setTotalPriceNum(contractEntity.getTotalPriceNum())
                .setCalcTotalPriceNum(newTotalPriceNum)
                .setBaseInfoResult(String.valueOf(baseInfo));

        // 3.lkg合同数量与nav数量对比
        log.info("合同编号:{},数量校验完毕", contractEntity.getContractCode());
    }

    /**
     * 校验合同转月次数
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkContractTransferTimes(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            // 获取客户初始次数
            ContractTransferCountDTO contractTransferCountDTO = customerFutureContractService.getContractTransferNum(contractEntity.getCustomerId(),
                    contractEntity.getGoodsCategoryId(), contractEntity.getIsOverForward(), contractEntity.getCategory2());

            // 计算转月次数
            contractTransferCountDTO.setTransferredTimes(contractEntity.getTransferredTimes())
                    .setAbleTransferTimes(contractTransferCountDTO.getTotalTransferTimes() - contractEntity.getTransferredTimes())
                    .setReversedPriceTimes(contractEntity.getReversedPriceTimes())
                    .setAbleReversePriceTimes(contractTransferCountDTO.getTotalReversePriceTimes() - contractEntity.getReversedPriceTimes());

            // 根据contractTransferCountDTO对比contract字段
            compareContractProperties(contractEntity, checkContractEntity, contractTransferCountDTO);

            checkContractEntity.setTotalTransferTimes(contractEntity.getTotalTransferTimes())
                    .setCalcTotalTransferTimes(contractTransferCountDTO.getTotalTransferTimes())
                    .setTotalReversePriceTimes(contractEntity.getTotalReversePriceTimes())
                    .setCalcTotalReversePriceTimes(contractTransferCountDTO.getTotalReversePriceTimes());

            log.info("合同编号:{},转月次数校验完毕", contractEntity.getContractCode());
        }
    }

    /**
     * 校核合同价格
     *
     * @param contractEntity
     * @param checkContractEntity
     */
    private void checkContractPrice(ContractEntity contractEntity, CheckContractEntity checkContractEntity) {
        ContractPriceEntity priceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        if (null == priceEntity) {
            log.error("合同编号:{},null == priceEntity", contractEntity.getContractCode());
        }

        // 含税单价是否正确
        PriceDetailBO priceDetailBO = BeanConvertUtils.convert(PriceDetailBO.class, priceEntity);
        ContractUnitPriceVO contractUnitPriceVO = contractPriceService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());
        // 根据contractUnitPriceVO对比contract字段
        compareContractProperties(contractEntity, checkContractEntity, contractUnitPriceVO);

        // 合同总金额是否正确
        BigDecimal newTotalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), contractUnitPriceVO.getUnitPrice());
        if (!BigDecimalUtil.isEqual(contractEntity.getTotalAmount(), newTotalAmount)) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "TotalAmount不一致；\n");
            log.error("合同编号:{},TotalAmount不一致", contractEntity.getContractCode());
        }

        // 合同保证金是否正确
        BigDecimal newDepositAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, newTotalAmount, BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));
        if (!BigDecimalUtil.isEqual(contractEntity.getDepositAmount(), newDepositAmount)) {
            checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + "DepositAmount不一致；\n");
            log.error("合同编号:{},DepositAmount不一致", contractEntity.getContractCode());
        }

        checkContractEntity.setUnitPrice(contractEntity.getUnitPrice())
                .setCalcUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setCifUnitPrice(contractEntity.getCifUnitPrice())
                .setCalcCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setFobUnitPrice(contractEntity.getFobUnitPrice())
                .setCalcFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setTotalAmount(contractEntity.getTotalAmount())
                .setCalcTotalAmount(newTotalAmount)
                .setDepositAmount(contractEntity.getDepositAmount())
                .setCalcDepositAmount(newDepositAmount);

        log.info("合同编号:{},价格校验完毕", contractEntity.getContractCode());
    }

    // =================================================INIT============================================================
    private CheckContractEntity initCheckContract(ContractEntity contractEntity, String checkBatch, String
            startDateTime, String endDateTime) {
        return new CheckContractEntity()
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setCheckBatch(checkBatch)
                .setStartCheckDate(StringUtils.isNoneBlank(startDateTime) ? DateTimeUtil.parseDateTimeString(startDateTime) : null)
                .setEndCheckDate(StringUtils.isNoneBlank(endDateTime) ? DateTimeUtil.parseDateTimeString(endDateTime) : null)
                .setType("CONTRACT")
                .setStatus("INIT")
                .setCreatedBy("SYSTEM")
                .setUpdatedBy("SYSTEM");
    }

    private CheckContractEntity initCheckLkgContract(ContractEntity contractEntity, String checkBatch) {
        return new CheckContractEntity()
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setCheckBatch(checkBatch)
                .setType("LKG")
                .setStatus("INIT")
                .setCreatedBy("SYSTEM")
                .setUpdatedBy("SYSTEM");
    }

    // ================================================PROCESSOR========================================================

    /**
     * 根据changeObject比较合同的字段
     *
     * @param contractEntity
     * @param checkContractEntity
     * @param changeObject
     */
    private void compareContractProperties(ContractEntity contractEntity, CheckContractEntity
            checkContractEntity, Object changeObject) {
        Object originalObject = BeanConvertUtils.convert(changeObject.getClass(), contractEntity);

        Class clazz = changeObject.getClass();
        try {
            PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz, Object.class).getPropertyDescriptors();
            for (PropertyDescriptor pd : pds) {
                // get方法
                Method readMethod = pd.getReadMethod();
                // 在obj1上调用get方法等同于获得obj1的属性值
                Object objBefore = readMethod.invoke(originalObject);
                // 在obj2上调用get方法等同于获得obj2的属性值
                Object objAfter = readMethod.invoke(changeObject);

                // 比较这两个值是否相等
                if (!ObjectUtil.equals(objBefore, objAfter)) {
                    checkContractEntity.setBaseInfoResult(checkContractEntity.getBaseInfoResult() + pd.getName() + "不一致；\n");
                    log.error("合同编号:{},{}不一致", contractEntity.getContractCode(), pd.getName());
                }
            }
        } catch (Exception e) {
            log.error("[{}]compareContractProperties exception:{}", contractEntity.getContractCode(), e.getMessage());
        }
    }

    // =================================================RESULT==========================================================
    private void saveCheckInfo(CheckContractEntity checkContractEntity) {
        String status;
        if (checkContractEntity.getBaseInfoResult().equals("NORMAL") &&
                checkContractEntity.getLkgRecordResult().equals("NORMAL")) {
            status = "NORMAL";
        } else {
            status = "EXCEPTION";
        }
        checkContractEntity.setStatus(status);
        checkContractDao.save(checkContractEntity);
    }
}
