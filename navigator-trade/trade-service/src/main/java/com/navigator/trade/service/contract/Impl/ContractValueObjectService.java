package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.dto.LkgQueryRecordDTO;
import com.navigator.pigeon.pojo.entity.LkgContractInfoEntity;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.service.IContractHistoryService;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
@Service
public class ContractValueObjectService implements IContractValueObjectService {

    @Autowired
    ContractDao contractDao;
    @Autowired
    IContractHistoryService contractHistoryService;
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private LkgContractFacade lkgContractFacade;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private ITtPriceService ttPriceService;

    @Override
    public boolean updateContractById(ContractEntity contractEntity) {
        contractEntity.setVersion(contractEntity.getVersion() + 1);
        return contractDao.updateById(contractEntity);
    }

    @Override
    public boolean updateContractById(ContractEntity contractEntity, String backTradeType, String referCode) {
        contractEntity.setVersion(contractEntity.getVersion() + 1);
        contractEntity.setMainVersion(contractEntity.getMainVersion() + 1);

        boolean result = contractDao.updateById(contractEntity);

        try {
            contractHistoryService.backupContract(contractEntity, backTradeType, referCode);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    @Override
    public boolean updateContractById(ContractEntity contractEntity, Integer ttId) {
        String backTradeType = "";
        String referCode = "";

        // 从tt获取业务信息
        TradeTicketEntity ticketEntity = tradeTickService.getByTtId(ttId);
        if (null != ticketEntity) {
            backTradeType = String.valueOf(ticketEntity.getTradeType());
            referCode = ticketEntity.getCode();
        }
        return this.updateContractById(contractEntity, backTradeType, referCode);
    }

    @Override
    public ContractEntity getContractById(Integer contractId) {
        ContractEntity contractEntity = contractDao.getContractById(contractId);
        if (contractEntity == null) {
            return null;
        }
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();// 获取lkg信息
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;
        // todo:===========nana: 数字合同注释掉此行
        totalBillNum = totalDeliveryNum;
        // 获取lkg信息
        try {
            if (!contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                Result result = null;
//                if (!SpringContextUtil.getEnv().contains(GlobalConstant.DEV_PROFILE)) {
                result = lkgContractFacade.getLkgContract(contractEntity.getContractCode());
//                }

                log.info("获取lkg信息：" + JSON.toJSONString(result));
                if (null != result && result.getCode() == ResultCodeEnum.OK.code()) {
                    if (null != result.getData()) {
                        LkgContractDTO lkgContractDTO = JSON.parseObject(JSON.toJSONString(result.getData()), LkgContractDTO.class);
                        if (null != lkgContractDTO && null != lkgContractDTO.getStatus() && lkgContractDTO.getStatus() != -1) {
                            if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                                totalDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getContractFactOutCount());
                                totalBillNum = BigDecimal.valueOf(lkgContractDTO.getContractOutCount());
                                allocateNum = BigDecimal.valueOf(lkgContractDTO.getDbOrderCount());
                            } else {
                                totalDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getInCount());
                                totalBillNum = BigDecimal.valueOf(lkgContractDTO.getOrderCount());
                            }

                            log.info("合同数量变更==[{}],已提数量：{}→{},已开单数量：{}→{},数据来源：{}",
                                    contractEntity.getContractCode(),
                                    contractEntity.getTotalDeliveryNum(), totalDeliveryNum,
                                    contractEntity.getTotalBillNum(), totalBillNum,
                                    JSON.toJSONString(lkgContractDTO));
                        }
                    }
                } else {
                    throw new BusinessException(ResultCodeEnum.GET_LKG_CONTRACT_EXCEPTION);
                }
            }
        } catch (Exception e) {
            log.error("获取LKG合同信息异常:{}", e.getMessage());
            contractEntity.setLkgContractException("获取LKG合同信息异常");
            throw new BusinessException(ResultCodeEnum.GET_LKG_CONTRACT_EXCEPTION);
        }

        // 处理精度丢失
        totalDeliveryNum = totalDeliveryNum.setScale(6, RoundingMode.HALF_UP);
        totalBillNum = totalBillNum.setScale(6, RoundingMode.HALF_UP);
        allocateNum = allocateNum.setScale(6, RoundingMode.HALF_UP);

        contractEntity
                .setTotalDeliveryNum(totalDeliveryNum)
                .setTotalBillNum(totalBillNum)
                .setAllocateNum(allocateNum);

        return contractEntity;
    }

    @Override
    public ContractEntity rollBackContract(Integer contractId) {
        ContractEntity contractEntity = contractDao.getById(contractId);
        if (null != contractEntity) {
            // 获取合同历史记录
            ContractHistoryEntity historyContract = contractHistoryService.getContractHistoryEntity(contractId, contractEntity.getMainVersion());
            if (null != historyContract) {
                contractEntity = BeanConvertUtils.convert(ContractEntity.class, historyContract);
                contractEntity.setId(historyContract.getContractId());

                // 更新contractPrice
                List<ContractPriceEntity> priceEntityList = contractPriceService.getContractPriceListContractId(contractId);
                if (priceEntityList.size() > 1) {
                    contractPriceService.updatePriceByContractId(priceEntityList.get(0).setIsDeleted(IsDeletedEnum.DELETED.getValue()));
                } else {
                    ContractPriceEntity contractPriceEntity = JSON.parseObject(historyContract.getPriceInfo(), ContractPriceEntity.class);
                    contractPriceEntity.setUpdatedAt(DateTimeUtil.now());
                    contractPriceService.updatePriceByContractId(contractPriceEntity);
                }

                // 是否存在点价单
                List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractId);
                if (CollectionUtil.isNotEmpty(confirmPriceList)) {
                    BigDecimal totalPriceNum = BigDecimal.ZERO;
                    totalPriceNum = totalPriceNum.add(confirmPriceList.stream()
                            .map(TTPriceEntity::getNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    contractEntity.setTotalPriceNum(totalPriceNum);
                }

                contractDao.updateById(contractEntity);
            }
        }
        return contractEntity;
    }

    @Override
    public ContractEntity getLocalLkgContractByContractId(Integer contractId) {

        ContractEntity contractEntity = contractDao.getContractById(contractId);
        if (null == contractEntity) {
            return null;
        }

        // 已提数量
        BigDecimal totalDeliveryNum = BigDecimal.ZERO;
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;

        Result result = lkgContractFacade.getLocalLkgContractByContractCode(contractEntity.getContractCode());
        log.info("获取localLkg信息：" + JSON.toJSONString(result));
        if (result.isSuccess()) {
            LkgContractInfoEntity lkgContractInfo = JSON.parseObject(JSON.toJSONString(result.getData()), LkgContractInfoEntity.class);
            if (null != lkgContractInfo) {

                // 排除已关闭的合同
                if (lkgContractInfo.getStatus() == 7) {
                    return null;
                }
                totalDeliveryNum = lkgContractInfo.getContractFactOutCount();
                totalBillNum = lkgContractInfo.getContractOutCount();
                allocateNum = lkgContractInfo.getDbOrderCount();
            }
        }

        return contractEntity.setTotalDeliveryNum(totalDeliveryNum)
                .setTotalBillNum(totalBillNum)
                .setAllocateNum(allocateNum);
    }

    @Override
    public List<LkgContractInfoEntity> getLocalLkgContractByContractCodeList(List<String> contractCodeList) {
        Result result = lkgContractFacade.getLocalLkgContractByContractCodeList(new LkgQueryRecordDTO().setContractCodeList(contractCodeList));
        if (result.isSuccess()) {
            return JSON.parseArray(JSON.toJSONString(result.getData()), LkgContractInfoEntity.class);
        }
        return null;
    }
}
