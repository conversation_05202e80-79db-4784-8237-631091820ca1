package com.navigator.trade.service.impl;

import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.dao.ContractPriceDao;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.service.IContractPriceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 价格明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
public class ContractPriceServiceImpl implements IContractPriceService {
    @Autowired
    private ContractPriceDao contractPriceDao;

    @Override
    public Result calculatePrice(PriceDetailBO priceDetailBO) {
        BigDecimal value = getValue(priceDetailBO);
        return Result.success(value);
    }

    @Override
    public BigDecimal calculatePriceBo(PriceDetailBO priceDetailBO) {
        return getValue(priceDetailBO);
    }

    @Override
    public ContractUnitPriceVO calcContractUnitPrice(PriceDetailBO priceDetailBO, BigDecimal taxRate) {
        ContractUnitPriceVO contractUnitPriceVO = new ContractUnitPriceVO();

        // 含税单价
        BigDecimal unitPrice = getValue(priceDetailBO);

        // 运输费
        BigDecimal deliveryPrice = BigDecimal.ZERO.add(priceDetailBO.getTransportPrice() != null ? priceDetailBO.getTransportPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getLiftingPrice() != null ? priceDetailBO.getLiftingPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getDelayPrice() != null ? priceDetailBO.getDelayPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getTemperaturePrice() != null ? priceDetailBO.getTemperaturePrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getOtherDeliveryPrice() != null ? priceDetailBO.getOtherDeliveryPrice() : BigDecimal.ZERO);

        // fob含税单价
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);

        // cif含税单价
        if (null != taxRate) {
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
            contractUnitPriceVO.setCifUnitPrice(cifUnitPrice);
        }

        return contractUnitPriceVO
                .setUnitPrice(unitPrice)
                .setFobUnitPrice(fobUnitPrice);
    }

    @Override
    public BigDecimal getValue(PriceDetailDTO priceDetailDTO) {
        Class clazz = priceDetailDTO.getClass();
        Field[] fields = clazz.getDeclaredFields();
        BigDecimal a = BigDecimal.ZERO;
        for (Field f : fields) {
            f.setAccessible(true);
            try {
                String s = StringUtils.isBlank((String) f.get(priceDetailDTO)) ? "0" : (String) f.get(priceDetailDTO);
                String regex = "^([-+])?\\d+(\\.\\d+)?$";
                Pattern p = Pattern.compile(regex);
                Matcher m = p.matcher(s);
                if (!m.matches() || f.getName().equalsIgnoreCase("goodsCategoryId")) {
                    a = a.add(BigDecimal.ZERO);
                } else {
                    a = a.add(new BigDecimal(s));
                }

            } catch (IllegalAccessException e1) {
                e1.printStackTrace();
            }
        }
        return a;
    }


//    public BigDecimal getValue(PriceDetailBO priceDetailBO) {
//        Class clazz = priceDetailBO.getClass();
//        Field[] fields = clazz.getDeclaredFields();
//        BigDecimal a = BigDecimal.ZERO;
//        for (Field f : fields) {
//            f.setAccessible(true);
//            try {
//                BigDecimal s = f.get(priceDetailBO) == null ? BigDecimal.ZERO : (BigDecimal) f.get(priceDetailBO);
//                a = a.add(s);
//            } catch (IllegalAccessException e1) {
//                e1.printStackTrace();
//            }
//        }
//        return a;
//    }

    /**
     * 计算价格详情的总值 （其他价格+vePrice*veContent）
     *
     * @param priceDetailBO 价格详情对象
     * @return 总价值，类型为BigDecimal
     */
    public BigDecimal getValue(PriceDetailBO priceDetailBO) {
        BigDecimal totalValue = BigDecimal.ZERO;
        Class<?> clazz = priceDetailBO.getClass();
        Field[] fields = clazz.getDeclaredFields();

        // 记录需要排除的字段名称
        String[] excludeFields = {"vePrice", "veContent"};

        for (Field field : fields) {
            // 检查字段是否需要排除
            if (isExcluded(field, excludeFields)) {
                continue;
            }

            // 设置字段可访问
            field.setAccessible(true);

            try {
                Object fieldValue = field.get(priceDetailBO);
                if (fieldValue instanceof BigDecimal) {
                    BigDecimal value = (BigDecimal) fieldValue;
                    totalValue = totalValue.add(value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        // 添加 vePrice 和 veContent 的乘积
        BigDecimal vePrice = getBigDecimalField(priceDetailBO, "vePrice");
        BigDecimal veContent = getBigDecimalField(priceDetailBO, "veContent");

        if (vePrice != null && veContent != null) {
            totalValue = totalValue.add(vePrice.multiply(veContent));
        }

        return totalValue;
    }

    private boolean isExcluded(Field field, String[] excludeFields) {
        for (String fieldName : excludeFields) {
            if (fieldName.equals(field.getName())) {
                return true;
            }
        }
        return false;
    }

    private BigDecimal getBigDecimalField(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object fieldValue = field.get(obj);
            if (fieldValue instanceof BigDecimal) {
                return (BigDecimal) fieldValue;
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return contractPriceDao.updateContractId(ttId, contractId);
    }

    @Override
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        return contractPriceDao.getContractPriceEntityContractId(contractId);
    }

    @Override
    public List<ContractPriceEntity> getContractPriceListContractId(Integer contractId) {
        return contractPriceDao.getContractPriceListContractId(contractId);
    }

    @Override
    public ContractPriceEntity getContractPriceEntityByTTId(Integer ttId) {
        return contractPriceDao.getContractPriceEntityByTTId(ttId);
    }

    @Override
    public void saveOrUpdate(ContractPriceEntity contractPriceEntity) {
        if (Objects.nonNull(contractPriceEntity)) {
            ContractPriceEntity oldPriceEntity = getContractPriceEntityByTTId(contractPriceEntity.getTtId());
            if (Objects.nonNull(oldPriceEntity)) {
                contractPriceEntity.setId(oldPriceEntity.getId());
            }
            contractPriceDao.saveOrUpdate(contractPriceEntity);
        }
    }

    @Override
    public void save(ContractPriceEntity contractPriceEntity) {
        contractPriceDao.save(contractPriceEntity);
    }

    @Override
    public boolean updatePriceByContractId(ContractPriceEntity contractPriceEntity) {
        return contractPriceDao.updatePriceByTtId(contractPriceEntity);
    }

    @Override
    public ContractEntity updateContractForwardPrice(ContractEntity contractEntity, BigDecimal averagePrice) {
        ContractPriceEntity contractPriceEntity = getContractPriceEntityContractId(contractEntity.getId());

        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        priceDetailBO.setForwardPrice(averagePrice);
        contractPriceEntity.setForwardPrice(averagePrice);

        BigDecimal deliveryPrice = contractPriceEntity.getTransportPrice()
                .add(contractPriceEntity.getLiftingPrice())
                .add(contractPriceEntity.getDelayPrice())
                .add(contractPriceEntity.getTemperaturePrice())
                .add(contractPriceEntity.getOtherDeliveryPrice());
        BigDecimal taxRate = contractEntity.getTaxRate();
        BigDecimal unitPrice = calculatePriceBo(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        BigDecimal totalAmount = unitPrice.multiply(contractEntity.getContractNum());
        BigDecimal depositAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, totalAmount, BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));

        contractEntity.setTotalAmount(totalAmount)
                .setDepositAmount(depositAmount)
                .setCifUnitPrice(cifUnitPrice)
                .setFobUnitPrice(fobUnitPrice)
                .setUnitPrice(unitPrice);

        // 更新contractPrice
        this.updatePriceByContractId(contractPriceEntity);

        return contractEntity;
    }

    /**
     * 同步合同含税单价明细
     *
     * @param contractPriceEntity
     */
    @Override
    public boolean syncContractPrice(ContractPriceEntity contractPriceEntity) {
        return contractPriceDao.save(contractPriceEntity);
    }
}
