package com.navigator.trade.app.tt.logic.service.handler.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractSplitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("SPLIT_HANDLER")
public class TTSplitSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        // 商品信息
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();

        Integer goodsId = salesContractSplitTTDTO.getGoodsId();

        SkuEntity skuEntity = skuFacade.getSkuById(goodsId);
        if (skuEntity != null) {
            salesContractSplitTTDTO.setGoodsName(skuEntity.getFullName())
                    .setCategory1(skuEntity.getCategory1())
                    .setCategory2(skuEntity.getCategory2())
                    .setCategory3(skuEntity.getCategory3())
                    .setGoodsCategoryId(skuEntity.getCategory2());
        }

        Integer contractId = salesContractSplitTTDTO.getSourceContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        PriceDetailBO priceDetailBo = ttdto.getPriceDetailBO();

        List<CompareObjectDTO> compareObjectDTOList = getSplitCompareObjectDTO(salesContractSplitTTDTO, priceDetailBo, contractId);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractSplitTTDTO.setModifyContent(modifyContent);

        // 判断是否是销售合同
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();

        boolean isCustomerChanged = isSales ?
                !contractEntity.getCustomerId().equals(salesContractSplitTTDTO.getCustomerId()) :
                !contractEntity.getSupplierId().equals(salesContractSplitTTDTO.getSupplierId());

        int contractSource = isCustomerChanged ? ContractActionEnum.SPLIT_CUSTOMER.getActionValue() : ContractActionEnum.SPLIT.getActionValue();

        // 交易类型
        int tradeType = isCustomerChanged ? ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() : ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        if (salesContractSplitTTDTO.getAddedSignatureType() == 0 && ttdto.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            tradeType = ContractTradeTypeEnum.NEW.getValue();
        }

        // TT编号
        String code = getSplitTTCode(isSales, contractId, ttdto.getSubmitType(), salesContractSplitTTDTO.getAddedSignatureType());

        salesContractSplitTTDTO
                .setTtType(TTTypeEnum.SPLIT.getType())
                .setSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setUserId(JwtUtils.getCurrentUserId())
                .setStatus(TTStatusEnum.NEW.getType())
                .setSalesType(contractEntity.getSalesType())
                .setCode(code)
                .setContractSource(contractSource)
                .setTradeType(tradeType);

        ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
    }

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.SPLIT.equals(ttTypeEnum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 1. 初始化
        initDTO(ttdto);

        // 2. 转换
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.split2TradeTicketDO(ttdto, arrangeContext);

        arrangeContext.setTradeTicketDO(tradeTicketDO);

        // 3. 保存至数据库
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);
        TradeTicketEntity ticketEntity = tradeTicketDO.getTradeTicketEntity();

        // 4. 处理拆分保存的数据
        ttDomainService.updateSaveModifyTTInfo(ttdto, ticketEntity);

        // 5. 提交审批
        if (ttdto.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            ttApproveHandler.startTTApprove(ticketEntity.getId(), ttdto, null);
        }

        SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();
        if (salesContractSplitTTDTO != null) {
            // 6. 隐藏TT
            ttDomainService.dealGroupTT(salesContractSplitTTDTO.getTtId(),
                    salesContractSplitTTDTO.getTtType(),
                    salesContractSplitTTDTO.getAddedSignatureType());

            // 7. 返回结果 - 前端展示
            TTQueryVO ttQueryVO = new TTQueryVO();
            ttQueryVO
                    .setSourceFlag(salesContractSplitTTDTO.getAddedSignatureType() != 0 ? 1 : 2)
                    .setContractCode(ticketEntity.getContractCode())
                    .setCode(ticketEntity.getCode())
                    .setTtId(ticketEntity.getId());
            ttQueryVOS.add(ttQueryVO);
            salesContractSplitTTDTO.setTtId(ticketEntity.getId());
        }
        return ttQueryVOS;
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTModifyEntity ttModifyEntity = (TTModifyEntity) tradeTicketDO.getTtSubEntity();

        TTDetailVO ttDetailVO = new TTDetailVO();
        String modifyContent = ttModifyEntity.getContent();
        List<CompareObjectDTO> compareList = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        List<CompareObjectDTO> list = tradeTicketConvertUtil.getSplitCompareList(compareList, tradeTicketEntity, ttModifyEntity);
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, priceDetailVO);
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        ttQueryDetailVO.setTradeType(tradeTicketEntity.getTradeType());
        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(ttModifyEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(ttModifyEntity.getPriceEndTime());

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttModifyEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttModifyEntity.getSupplierId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());

        //买家
        if (null != ttModifyEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttModifyEntity.getCustomerId()));
        }
        if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttModifyEntity.getCustomerId());
            if (null != customerDTO) {
                ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
                ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
                ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
            }
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttModifyEntity.getDepositAmount()) {
            int depositAmountStatus = ttModifyEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttModifyEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttModifyEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttQueryDetailVO.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        if (null != ttModifyEntity.getDepositRate()) {
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateFacade.getCustomerDepositRateById(ttModifyEntity.getDepositRate());
            if (null != customerDepositRateEntity) {
                ttQueryDetailVO.setDepositRateValue(customerDepositRateEntity.getDepositRate());
            }
        }
        //查询库点信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttModifyEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttModifyEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttModifyEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        if (ttModifyEntity.getNewContractNum() != null) {
            ttQueryDetailVO.setContractNum(ttModifyEntity.getNewContractNum());
            BigDecimal totalAmount = ttModifyEntity.getNewContractNum().multiply(ttModifyEntity.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);
            ttQueryDetailVO.setTotalAmount(totalAmount);
        }
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ttDetailVO.setCompareObjectDTOList(list);
        if (CollectionUtils.isEmpty(compareList)) {
            ttDetailVO.setDetailType("7");
        } else {
            ttDetailVO.setDetailType("8");
        }
        ContractEntity originalContractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setSourceType(tradeTicketEntity.getSourceType())
                .setOriginContractNum(originalContractEntity.getContractNum())
                .setOriginContractType(originalContractEntity.getContractType())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        return ttDetailVO;
    }

    /**
     * 获取拆分保存的TTCode
     *
     * @param isSales            是否是销售合同
     * @param contractId         合同ID
     * @param addedSignatureType 补充协议类型
     * @return
     */
    private String getSplitTTCode(boolean isSales, Integer contractId, Integer submitType, Integer addedSignatureType) {

        // 新合同的TT编号
        String code = isSales ? sequenceUtil.generateSpotSalesTTCode() : sequenceUtil.generateSpotPurchaseTTCode();

        // 普通拆分子TT编号
        if (addedSignatureType == 0) {
            return code;
        }

        // 原合同的TT编号
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);

        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                // 合同基差转一口价外的TT
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        // 保存的TT
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());

        // 不存在TT记录
        if (tradeTicketEntities.isEmpty()) {
            return code;
        }

        // 保存拆分新生成-001 保存提交则使用最新的一条保存的记录
        if (submitType == SubmitTypeEnum.SAVE_SUBMIT.getValue()) {
            return tradeTicketEntities.get(0).getCode();
        }

        return sequenceUtil.generateSpotChildTTCode(tradeTicketEntities.get(0).getCode());
    }

    protected List<CompareObjectDTO> getSplitCompareObjectDTO(SalesContractSplitTTDTO salesContractSplitTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        //salesContractSplitTTDTO.setSupplierId(contractEntity.getSupplierId());
//        GoodsInfoVO goodsInfoVO = getGoodsInfoVO(salesContractSplitTTDTO);
//        salesContractSplitTTDTO.setGoodsId(goodsInfoVO.getGoodsId());
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.compareFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO newDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(salesContractSplitTTDTO, newDTO);
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(newDTO.getSignDate());
        newDTO.setSignDate(signDate);
        newDTO.setUsage(salesContractSplitTTDTO.getUsage());
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(originalDTO, newDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }
}
