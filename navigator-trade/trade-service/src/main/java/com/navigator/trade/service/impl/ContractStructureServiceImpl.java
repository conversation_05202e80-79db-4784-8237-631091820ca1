package com.navigator.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.activiti.pojo.enums.ApproveProcessEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.approval.CategoryApprovalModelFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StructureCodeUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.mapper.ContractStructureMapper;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractVO;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IContractStructureService;
import com.navigator.trade.service.ITTApproveService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.contractsign.IContractSignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Slf4j
@Service
public class ContractStructureServiceImpl extends ServiceImpl<ContractStructureMapper, ContractStructureEntity> implements IContractStructureService {

    @Resource
    ContractStructureDao contractStructureDao;
    @Resource
    ContractDao contractDao;
    @Resource
    PriceApplyFacade priceApplyFacade;
    @Resource
    private FuturesDomainFacade futuresDomainFacade;
    @Autowired
    protected TradeTicketDao tradeTicketDao;

    @Autowired
    protected IContractValueObjectService contractValueObjectService;

    @Autowired
    protected OperationLogFacade operationLogFacade;

    @Autowired
    private ApproveFacade approveFacade;

    @Resource
    private IContractQueryService contractService;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private FileBusinessFacade fileBusinessFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private PriceAllocateFacade priceAllocateFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CategoryApprovalModelFacade categoryApprovalModelFacade;

    @Autowired
    @Qualifier("TTApproveServiceImpl")
    protected ITTApproveService approveService;

    @Autowired
    protected SalesContractSignHandler salesContractSignHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractStructureEntity submitStructureContract(SalesStructurePriceTTDTO structurePriceTTDTO) {
        if (structurePriceTTDTO == null) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }
        structurePriceTTDTO.setSignDate(DateTimeUtil.parseTimeStamp2359(structurePriceTTDTO.getSignDate()));
        Integer ttId = structurePriceTTDTO.getTtId();
        //校验可操作量
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO
                .setCustomerId(structurePriceTTDTO.getCustomerId().toString())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setSupplierId(structurePriceTTDTO.getSupplierId().toString())
                .setStatus(PriceTypeEnum.STRUCTURE_PRICING.getValue())
                .setDomainCode(structurePriceTTDTO.getDomainCode())
                .setGoodsCategoryId(structurePriceTTDTO.getGoodsCategoryId());

        Result result = futuresDomainFacade.mayTransferNum(contractFuturesDTO);
        CustomerFuturesDTO customerFuturesDTO = new CustomerFuturesDTO();
        if (result.isSuccess()) {
            customerFuturesDTO = JSON.parseObject(JSON.toJSONString(result.getData()), CustomerFuturesDTO.class);
        } else {
            throw new BusinessException(ResultCodeEnum.ADD_PRICE_APPLY_BE_DEFEATED);
        }
        //this.checkPriceApplyNum(customerFuturesDTO.getMayTransferNum(), structurePriceTTDTO.getTotalNum());

//        TTDTO ttdto = new TTDTO();
//        ttdto.setSalesStructurePriceTTDTO(structurePriceTTDTO);


        //确定审批
//        RecordBizOperationDTO recordBizOperationDTO = startTTApprove(structurePriceTTDTO.getTtId(), ttdto);
//
//        if(null == recordBizOperationDTO)
//            throw new BusinessException(ResultCodeEnum.APPROVE_FAIL);

        //创建合同
        ContractStructureEntity contractStructureEntity = createContract(structurePriceTTDTO);

        //创建合同的签署
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        BeanUtils.copyProperties(structurePriceTTDTO, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtCode(structurePriceTTDTO.getCode())
                .setCompanyName(structurePriceTTDTO.getCompanyName())
                .setTtType(TTTypeEnum.STRUCTURE_PRICE.getType())
                .setSalesType(2)
                .setSupplierId(String.valueOf(structurePriceTTDTO.getSupplierId()))
                .setSupplierName(structurePriceTTDTO.getSupplierName())
                .setOwnerId(Integer.valueOf(structurePriceTTDTO.getOwnerId()))
                .setTtId(ttId)
                .setContractId(contractStructureEntity.getContractId());

        ContractSignEntity contractSign = createContractSign(contractSignCreateDTO, ttId);

        //做新增结构化定价的记录
        saveStructureTTOperationDetail(structurePriceTTDTO);

//        approveFacade.recordBizOperation(recordBizOperationDTO);

        //创建点价单
        Result<Integer> priceResult = structurePriceApply(structurePriceTTDTO, contractStructureEntity);

        if (priceResult == Result.failure()) {
            throw new BusinessException(ResultCodeEnum.ADD_PRICE_APPLY_BE_DEFEATED);
        }

        //创建好点价单后，将点价单号赋值给结构化定价
        contractStructureEntity.setPriceApplyId(priceResult.getData());
        contractStructureDao.updateById(contractStructureEntity);

        return contractStructureEntity;
    }

    private Result<Integer> structurePriceApply(SalesStructurePriceTTDTO tt, ContractStructureEntity contract) {
        PriceApplyDTO priceApplyDTO = new PriceApplyDTO();

        CompanyEntity companyEntity = companyFacade.queryCompanyById(tt.getCompanyId());

        priceApplyDTO
                .setCustomerId(tt.getCustomerId())
                .setCode(contract.getContractCode())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setCustomerId(tt.getCustomerId())
                .setCustomerName(tt.getCustomerName())
                .setCategoryId(tt.getGoodsCategoryId())
                .setType(PriceTypeEnum.STRUCTURE_PRICING.getValue()).setDominantCode(tt.getDomainCode())
                .setContractId(contract.getId())
                .setApplyNum(tt.getTotalNum())
                .setContractId(contract.getContractId())
                .setStartTime(tt.getStartTime())
                .setEndTime(tt.getEndTime())
                .setStructureType(1)
                .setCompanyId(tt.getCompanyId())
                .setCompanyName(companyEntity.getName())
                .setTotalDay(contract.getTotalDay());
        return priceApplyFacade.priceApply(priceApplyDTO);
    }

    /**
     * 校验申请数量 与可点数量
     *
     * @param canPriceNum
     * @param applyNum
     */
    private void checkPriceApplyNum(BigDecimal canPriceNum, BigDecimal applyNum) {
        if (BigDecimalUtil.isLess(canPriceNum, applyNum)) {
            throw new BusinessException(ResultCodeEnum.APPLY_NUM_MORE_THAN_CAN_PRICE_NUM);
        }
        /*if (BigDecimalUtil.isLess(canPriceNum.subtract(applyNum), BigDecimal.TEN) && !BigDecimalUtil.isEqual(canPriceNum.subtract(applyNum), BigDecimal.ZERO)) {
            throw new BusinessException(ResultCodeEnum.SURPLUS_NUM_LESS_THAN_TEN);
        }
        if (applyNum.intValue() < 10 && BigDecimalUtil.isGreater(canPriceNum.subtract(applyNum), BigDecimal.TEN)) {
            throw new BusinessException(ResultCodeEnum.APPLY_NUM_MUST_MULTIPLE_OF_TEN);
        }*/
    }


    @Override
    public boolean updateStructureContractPricingStatus(ContractStructureDTO contractStructureDTO) {
        boolean rtn = false;
        ContractStructureEntity contractStructureEntity = contractStructureDao.getContractStructure(contractStructureDTO.getContractId());
        if (null != contractStructureEntity) {
            contractStructureEntity.setPriceStatus(contractStructureDTO.getPriceStatus());
            rtn = contractStructureDao.updateById(contractStructureEntity);
        }
        return rtn;
    }

    @Override
    public ContractStructureEntity getContractStructure(Integer contractId) {
        return contractStructureDao.getContractStructure(contractId);
    }

    @Override
    public List<PriceDealDetailVO> getContractStructurePriceList(Integer priceApplyId) {
        List<PriceDealDetailVO> structureEntityList = new ArrayList<>();
        Result<List<PriceDealDetailVO>> result = priceApplyFacade.queryPriceDealDetail(priceApplyId);
        return result.getData();
    }


    @Override
    public Result queryContractStructure(QueryDTO<ContractBO> queryDTO) {
        IPage<ContractStructureEntity> iPage = contractStructureDao.queryContractStructure(queryDTO);

        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractStructureEntity -> {

            ContractVO contractVO = new ContractVO();

            ContractEntity contractEntity = contractService.getBasicContractById(contractStructureEntity.getContractId());

            // 获取合同所属商务
            EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());

            BeanUtil.copyProperties(contractEntity, contractVO);
            // 获取客户合同url列表
            List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setCustomerContractUrl(customerContractUrls);

            // 获取合同模板url地址
            List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);

            contractVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);

            // 获取品类编码
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
            contractVO.setCategoryCode(categoryEntity != null ? categoryEntity.getCode() : null);
            // 获取LDC合同url地址
            List<FileInfoEntity> ldcContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setLdcContractUrl(ldcContractUrls != null && ldcContractUrls.size() > 0 ? ldcContractUrls.get(0).getFileUrl() : null);

            // 交提货方式
            contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());

            // 重量质检
            if (StringUtils.isNotBlank(contractEntity.getWeightCheck())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getWeightCheck()));
                contractVO.setWeightCheckName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null);
            }
            // 目的地名
            if (StringUtils.isNotBlank(contractEntity.getDestination())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getDestination()));
                contractVO.setDestinationName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null);
            }

            // 根据createdBy获取employ信息
            EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
            contractVO.setCreatedEntity(employEntity);

            ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);

            contractVO.setContractStructureVO(contractStructureDTO);

            return contractVO;
        }).collect(Collectors.toList());

        return Result.page(iPage, contractVOList);
    }

    @Override
    public boolean addStructureRelease(Integer contractId, BigDecimal notDealNum) {
        ContractStructureEntity contractStructure = contractStructureDao.getContractStructure(contractId);
        if (contractStructure == null) return false;
        contractStructure.setCumulativeRelease(contractStructure.getCumulativeRelease().add(notDealNum));
        return contractStructureDao.updateById(contractStructure);
    }

    @Override
    public Integer getApplyIdByStructureContractId(Integer contractId) {
        ContractStructureEntity one = getOne(Wrappers.<ContractStructureEntity>lambdaQuery().eq(ContractStructureEntity::getContractId, contractId));
        return one.getPriceApplyId();
    }

    @Override
    public Integer getApplyIdByStructureContractCode(String contractCode) {
        ContractStructureEntity one = getOne(Wrappers.<ContractStructureEntity>lambdaQuery().eq(ContractStructureEntity::getContractCode, contractCode).eq(ContractStructureEntity::getIsDeleted, 0));
        return one.getPriceApplyId();
    }

    @Override
    public boolean updateStructureContract(ContractStructureEntity contractStructureEntity) {
        return contractStructureDao.updateById(contractStructureEntity);
    }

    @Override
    public Boolean verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO) {

        //根据客户,合约,品类查询是否存在结构化定价
        List<ContractStructureEntity> contractStructureEntities = contractStructureDao.verifyContractStructureNum(verifyContractStructureNumDTO);
        if (contractStructureEntities.isEmpty()) {
            return false;
        }

        //如果存在结构化定价校验数量
        BigDecimal num = BigDecimal.ZERO;

        for (ContractStructureEntity contractStructureEntity : contractStructureEntities) {
            //结构化定价总量
            BigDecimal totalNum = contractStructureEntity.getTotalNum();
            //总量加上累积成交量
            totalNum = totalNum.add(contractStructureEntity.getCumulativeDealNum());

            //查询分配通过的分配单
            List<PriceAllocateEntity> priceAllocateEntities = priceAllocateFacade.getAllocateByContractStructureId(contractStructureEntity.getContractId());
            //分配单为空时 结束本次循环
            if (priceAllocateEntities.isEmpty()) {
                num = num.add(totalNum);
                continue;
            }
            //查询
            for (PriceAllocateEntity priceAllocateEntity : priceAllocateEntities) {
                totalNum = totalNum.subtract(priceAllocateEntity.getAllocateNum());
            }

            num = num.add(totalNum);
        }

        //根据客户,合约,品类查询合同
        BigDecimal contractNums = BigDecimal.ZERO;
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO
                .setCustomerId(verifyContractStructureNumDTO.getCustomerId().toString())
                .setCompanyId(verifyContractStructureNumDTO.getCompanyId())
                .setGoodsCategoryId(verifyContractStructureNumDTO.getGoodsCategoryId())
                .setDomainCode(verifyContractStructureNumDTO.getDomainCode())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setContractType(Arrays.asList(ContractTypeEnum.JI_CHA.getValue()))
                .setPriceType(PriceTypeEnum.TRANSFER_MONTH.getValue());
        List<ContractEntity> contractEntities = contractService.queryContractsFuturesNum(contractFuturesDTO);

        for (ContractEntity contractEntity : contractEntities) {
            BigDecimal contractNum = contractEntity.getContractNum();
            contractNum = contractNum.subtract(contractEntity.getTotalPriceNum());

            contractNums = contractNums.add(contractNum);
        }

        //合同可操作数量减去结构化定定价量
        BigDecimal operationNum = contractNums.subtract(num);
        //减去给的合同数量
        operationNum = operationNum.subtract(verifyContractStructureNumDTO.getContractNum());
        //小于0
        if (operationNum.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }

        return false;
    }

    @Override
    public ContractStructureEntity getContractStructureById(Integer contractId) {
        ContractStructureEntity one = getOne(Wrappers.<ContractStructureEntity>lambdaQuery().eq(ContractStructureEntity::getContractId, contractId));

        ContractEntity contractEntity = contractService.getBasicContractById(contractId);
        one.setFutureCode(contractEntity.getFutureCode());

        return one;
    }

    @Override
    public ContractStructureEntity getContractStructureVOById(Integer contractId) {
        ContractStructureEntity one = getOne(Wrappers.<ContractStructureEntity>lambdaQuery().eq(ContractStructureEntity::getContractId, contractId));
        one.setStructureName(StructureCodeUtil.numToCode(one.getStructureType()) + "-" + one.getStructureName());
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);
        one.setFutureCode(contractEntity.getFutureCode());
        return one;
    }

    @Override
    public List<ContractStructureEntity> getValidStructureContract(List<Integer> contractIds) {
        List<ContractStructureEntity> structureList = new ArrayList<>();
        if (contractIds != null && contractIds.size() > 0) {
            LambdaQueryWrapper<ContractStructureEntity> structureWrapper = new LambdaQueryWrapper<>();
            structureWrapper.in(ContractStructureEntity::getContractId, contractIds).orderBy(true, true, ContractStructureEntity::getContractId);
            structureList = contractStructureDao.list(structureWrapper);
            for (int i = 0; i < structureList.size(); i++) {
                structureList.get(i).setPriceStartDate(structureList.get(i).getStartTime());
                structureList.get(i).setPriceEndDate(structureList.get(i).getEndTime());
            }
        }
        return structureList;
    }

    private ContractStructureEntity createContract(SalesStructurePriceTTDTO tt) {
        List<ContractEntity> oldList = contractDao.getByContractCode(tt.getContractCode());
        oldList.forEach(oldContractEntity -> {
            try {
                contractValueObjectService.updateContractById(oldContractEntity.setRepeatContractCode(oldContractEntity.getRepeatContractCode() + "-" + oldContractEntity.getId()).setIsDeleted(IsDeletedEnum.DELETED.getValue()));
            } catch (Exception e) {
                log.error("contractCode: {} update fail cause by: {}", oldContractEntity.getContractCode(), e.getMessage());
                throw new BusinessException(ResultCodeEnum.FAILURE);
            }
        });

        if (StringUtils.isBlank(tt.getCustomerName())) {
            CustomerEntity customerEntity = customerFacade.getCustomerById(tt.getCustomerId());
            tt.setCustomerName(customerEntity.getName()).setCustomerCode(customerEntity.getLinkageCustomerCode());

        }

        ContractEntity contractEntity = new ContractEntity();
        contractEntity.setUuid(UUID.randomUUID().toString())
                .setContractCode(tt.getContractCode())
                .setRepeatContractCode(tt.getContractCode())
                .setContractType(ContractTypeEnum.STRUCTURE.getValue())
                .setSalesType(tt.getSalesType())
                .setContractSource(1)
                .setCustomerId(tt.getCustomerId())
                .setCustomerName(tt.getCustomerName())
                .setCustomerCode(tt.getCustomerCode())
                .setSupplierId(tt.getSupplierId())
                .setSupplierName(tt.getSupplierName())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setNeedOriginalPaper(1)//需要正本
                .setSignatureType(String.valueOf(SignatureTypeEnum.BOTH_SIGNATURE.getValue()))
                .setTradeType(ContractTradeTypeEnum.NEW.getValue())
                .setDomainCode(tt.getDomainCode())
                .setGoodsId(tt.getGoodsCategoryId())
                .setGoodsCategoryId(tt.getGoodsCategoryId())
                .setContractNum(tt.getTotalNum())
                .setPriceStartTime(tt.getStartTime())
                .setPriceEndTime(DateTimeUtil.formatDateTimeString(tt.getEndTime()))
                .setOwnerId(Integer.valueOf(tt.getOwnerId()))
                .setSignDate(tt.getSignDate())
                .setBelongCustomerId(tt.getBelongCustomerId())
                .setPriceEndType(ContractPriceEndTypeEnum.DATE.ordinal())
                .setIsDeleted(0)
                .setCompanyId(tt.getCompanyId())
                .setCompanyName(tt.getCompanyName())
                .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setCreatedAt(DateTimeUtil.now())
                .setCategory1(tt.getCategory1())
                .setCategory2(tt.getCategory2())
                .setCategory3(tt.getCategory3())
                .setSiteCode(tt.getSiteCode())
                .setSiteName(tt.getSiteName())
        ;
        try {
            contractDao.save(contractEntity);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }


        tt.setContractId(contractEntity.getId());
        tradeTicketDao.updateContractId(tt.getTtId(), contractEntity.getId());

        List<ContractStructureEntity> oldStructureList = contractStructureDao.getByContractCode(tt.getContractCode());
        oldStructureList.forEach(oldStructure -> contractStructureDao.updateContractById(oldStructure.setIsDeleted(IsDeletedEnum.DELETED.getValue())));

        ContractStructureEntity contractStructure = new ContractStructureEntity();
        BeanUtils.copyProperties(tt, contractStructure);
        contractStructure.setContractId(contractEntity.getId());
        contractStructure.setSupplierName(contractEntity.getSupplierName());
        contractStructure.setCumulativeRelease(new BigDecimal(0));
        contractStructure.setStructureName(tt.getStructureName());

        boolean save = contractStructureDao.save(contractStructure);

        if (save) return contractStructure;
        else return null;
    }

    private void saveStructureTTOperationDetail(SalesStructurePriceTTDTO structurePriceTTDTO) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO().setBizCode("addStructure")
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(1).setSource(9)
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setMetaData(null).setCreatedAt(DateTimeUtil.now())
                .setReferBizId(structurePriceTTDTO.getTtId())
                .setReferBizCode(structurePriceTTDTO.getCode())
                .setTtCode(structurePriceTTDTO.getCode());

        operationLogFacade.recordOperationLogDetail(operationDetailDTO);
    }

    public RecordBizOperationDTO startTTApprove(Integer ttId, TTDTO ttDto) {

        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        Integer salesType = tradeTicketEntity.getSalesType();
        int value = ContractApproveRuleEnum.NONE.getValue();
        ApproveDTO approveDTO = getApproveDTO(ttId, ttDto, tradeTicketEntity, salesType, value);

        log.info("check_code_question2 ttId:{} ContractStructureServiceImpl  startTTApprove1   ", ttId);
        Result result = approveService.startTTApprove(approveDTO);
        if (!result.isSuccess()) {
            log.info("发起审批失败,提交失败,result:{}", JSON.toJSONString(result));
            throw new BusinessException(ResultCodeEnum.START_APPROVAL_ERROR);
        }
        log.info("startTTApprove.result:{}", JSON.toJSONString(result));

        // 更改tt状态,更新审批规则
        if (Objects.equals(ContractApproveRuleEnum.NONE.getValue(), value)) {
            //免签

            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WITHOUT_APPROVE.getValue(), tradeTicketEntity.getCode(), value);

            //更新合同状态
            if (ContractActionEnum.NEW.getActionValue() != tradeTicketEntity.getContractSource()) {
                ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
                contractModifyDTO.setTtId(tradeTicketEntity.getId());
                contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
                contractModifyDTO.setTtType(tradeTicketEntity.getType());
                contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
                if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                    contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
                }

                if (ttDto.getSalesContractReviseTTDTO() != null) {
                    ttDto.getSalesContractReviseTTDTO().setApprovalType(ContractApproveRuleEnum.NONE.getValue());
                }
            }
        } else {
            //审批中
            log.info("check_code_question2 ttId:{} ContractStructureServiceImpl  startTTApprove2   ", ttId);
            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_A_SIGN.getValue(), tradeTicketEntity.getCode(), value);

        }

        ApproveResultDTO approveResultDTO = JSON.parseObject(JSON.toJSONString(result.getData()), ApproveResultDTO.class);
        RecordBizOperationDTO recordBizOperationDTO = new RecordBizOperationDTO();
        recordBizOperationDTO.setApproveDTO(approveDTO);
        recordBizOperationDTO.setApproveResultDTO(approveResultDTO);
        return recordBizOperationDTO;
    }

    private ApproveDTO getApproveDTO(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity, Integer salesType, int value) {
        //获得流程定义
        //ApproveProcessEnum approveProcessEnum = ApproveProcessEnum.getByTradeInfo(salesType, GoodsCategoryEnum.getByValue(tradeTicketEntity.getGoodsCategoryId()).name(), ContractTradeTypeEnum.STRUCTURE_PRICE.name());

        String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

        log.info("============ tt.ttId:{}", JSON.toJSONString(ttId));
        log.info("============ tt.contractCode:{}", JSON.toJSONString(tradeTicketEntity.getContractCode()));
        log.info("============ tt.subCategoryId:{}", JSON.toJSONString(tradeTicketEntity.getGoodsCategoryId()));
        log.info("============ tt.categoryId:{}", JSON.toJSONString(tradeTicketEntity.getSubGoodsCategoryId()));
        log.info("============ tt.subCategoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getGoodsCategoryId())));
        log.info("============ tt.categoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getSubGoodsCategoryId())));
        CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory1());
        CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
        CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());

        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO
                .setCategory1Name(category1Entity.getName())
                .setCategory2Name(category2Entity.getName())
                .setCategory3Name(category3Entity.getName())
                .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtTypeEnum(TTTypeEnum.getByType(tradeTicketEntity.getType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setBuCode(tradeTicketEntity.getBuCode())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(salesType))
                .setUserId(JwtUtils.getCurrentUserId())
                .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                //.setRoleId("")
                .setCustomerName(ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? tradeTicketEntity.getCustomerName() : tradeTicketEntity.getSupplierName())
                .setProcessKey(processKey)
                .setBizModule("TT").setBizId(ttId)
                .setBizCode(tradeTicketEntity.getCode())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode())
                .setApproveRuleValue(value)
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSiteCode(tradeTicketEntity.getSiteCode())
                .setSiteName(tradeTicketEntity.getSiteName())
        ;


        buildApproveBizData(approveDTO, ttDto, tradeTicketEntity);

        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_ALL)
                    .setTtTypeEnum(TTTypeEnum.TRANSFER)
            ;
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_PART)
                    .setTtTypeEnum(TTTypeEnum.TRANSFER);
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER)
                    .setTtTypeEnum(TTTypeEnum.SPLIT);
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_NORMAL)
                    .setTtTypeEnum(TTTypeEnum.SPLIT);
        }
        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

        return approveDTO;
    }

    protected void buildApproveBizData(ApproveDTO approveDTO, TTDTO ttdto, TradeTicketEntity tradeTicketEntity) {
        List<ApproveBizInfoDTO> approveBizInfoDTOList = new ArrayList<>();

        approveBizInfoDTOList.add(new ApproveBizInfoDTO().setIndex(1).setName("customerName").setDisplayName("客户名称").setValue(approveDTO.getCustomerName()));

        Object data = JSON.toJSONString(approveBizInfoDTOList);
        approveDTO.setBizData(data);
    }

    public ContractSignEntity createContractSign(ContractSignCreateDTO contractSignCreateDTO, Integer ttId) {
        //创建合同协议
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        contractSignCreateDTO.setTradeType(tradeTicketEntity.getTradeType()).setBelongCustomerId(tradeTicketEntity.getBelongCustomerId());

        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        contractSignCreateDTO
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3());

        ContractSignEntity contractSign = contractSignService.createOrUpdateContractSign(contractSignCreateDTO);
        //回填协议信息到TT
        tradeTicketDao.updateSignInfo(ttId, contractSign);
        return contractSign;
    }
}
