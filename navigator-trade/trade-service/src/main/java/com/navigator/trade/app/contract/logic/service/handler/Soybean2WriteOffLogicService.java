package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractWriteOffOMDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 */
public interface Soybean2WriteOffLogicService {


    /**
     * 校验合同注销
     *
     * @param contractEntity     合同实体
     * @param contractWriteOffOMDTO 解约定赔dto
     */
    void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO);

    /**
     * 修改补充补充基本信息-创建子合同
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    void buildBaseInfo(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity, String type);

    /**
     * 处理变更的业务信息
     * 修改货品，交割库（库点），交货方式，目的港，注销说明备注，赊销账期，交货周期，短装溢，所属商务，履约保证金
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    void buildBizInfo(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity, String type);

    /**
     * 创建合同
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    void createContract(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity);

    /**
     * 处理仓单销售合同信息
     *
     * @param contractWriteOffOMDTO 注销信息
     * @param contractEntity    仓单合同
     */
    void operateFatherContract(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity);

}
