package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 合同关闭子类业务逻辑处理 逻辑处理
 *
 * <AUTHOR>
 */
public interface CloseLogicService {

    /**
     * 校验关闭合同的条件
     *
     * @param contractEntity
     */
    void closeContractCheck(ContractEntity contractEntity);


    /**
     * 关闭合同
     *
     * @param contractEntity
     */
    void closeContract(ContractEntity contractEntity);

}
