package com.navigator.trade.service.tradeticket.impl.purchase;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component("SBM_P_TT_WASHOUT,SBO_P_TT_WASHOUT")
public class PurchaseTTWashoutServiceImpl extends PurchaseTTAddServiceImpl {
    public PurchaseTTWashoutServiceImpl() {
        ttTypeEnum = TTTypeEnum.WASHOUT;
        contractTradeTypeEnum = ContractTradeTypeEnum.WASHOUT;
        contractSource = ContractActionEnum.WASHOUT.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.PURCHASE;
        processorType = ProcessorTypeEnum.SBM_P_WASHOUT.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_P_WASHOUT.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = tradeTicketConvertUtil.getWashoutTTDetailVO(ttId, tradeTicketEntity);
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_PURCHASE_TT, ttId, OperationSourceEnum.SYSTEM.getValue());
        return ttDetailVO;
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        return initPurchaseDTOCommon(ttdto);
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }

    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        TTAddEntity ttAdd = ttAddDao.getTTAddEntityByTTId(ttId);
        return contractSignConvertUtil.getWashoutSignTemplateDTO(ttId, ttAdd, ttAdd.getSupplierId());
    }

    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());

            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }

        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {
        tradeTicketConvertUtil.updateAddContent(originalContractEntity, contractEntity, ttId, ttType);
    }
}

