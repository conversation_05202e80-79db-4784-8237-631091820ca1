package com.navigator.trade.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.ContractEquityVOMapper;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractEquityVOEntity;

import java.math.BigDecimal;

/**
 * <p>
 * v_contract_equity Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Dao
public class ContractEquityVODao extends BaseDaoImpl<ContractEquityVOMapper, ContractEquityVOEntity> {
    public IPage<ContractEquityVOEntity> getChangeContractEquityList(QueryDTO<ContractEquityQueryDTO> queryDTO) {
        LambdaQueryWrapper<ContractEquityVOEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (null != queryDTO && null != queryDTO.getCondition()) {
            ContractEquityQueryDTO equityQueryDTO = queryDTO.getCondition();
            queryWrapper
                    .eq(null != equityQueryDTO.getCustomerId(), ContractEquityVOEntity::getCustomerId, equityQueryDTO.getCustomerId())
                    .eq(null != equityQueryDTO.getSupplierId(), ContractEquityVOEntity::getSupplierId, equityQueryDTO.getSupplierId())
                    .like(StrUtil.isNotBlank(equityQueryDTO.getContractCode()), ContractEquityVOEntity::getContractCode, StrUtil.isNotBlank(equityQueryDTO.getContractCode()) ? equityQueryDTO.getContractCode().trim() : null)
                    .like(StrUtil.isNotBlank(equityQueryDTO.getDeliveryFactoryCode()), ContractEquityVOEntity::getDeliveryFactoryCode, equityQueryDTO.getDeliveryFactoryCode())
                    .eq(null != equityQueryDTO.getGoodsCategoryId(), ContractEquityVOEntity::getGoodsCategoryId, equityQueryDTO.getGoodsCategoryId())
                    .eq(null != equityQueryDTO.getContractType(), ContractEquityVOEntity::getContractType, equityQueryDTO.getContractType())
                    .ge(StrUtil.isNotBlank(equityQueryDTO.getDeliveryStartDate()), ContractEquityVOEntity::getDeliveryStartTime, StrUtil.isNotBlank(equityQueryDTO.getDeliveryStartDate()) ? DateTimeUtil.parseTimeStamp0000(equityQueryDTO.getDeliveryStartDate().split(" ")[0]) : "")
                    .le(StrUtil.isNotBlank(equityQueryDTO.getDeliveryEndDate()), ContractEquityVOEntity::getDeliveryEndTime, StrUtil.isNotBlank(equityQueryDTO.getDeliveryEndDate()) ? DateTimeUtil.parseTimeStamp2359(equityQueryDTO.getDeliveryEndDate().split(" ")[0]) : "")
                    // 权益变更
                    .eq(null != equityQueryDTO.getLastApprovalStatus(), ContractEquityVOEntity::getLastApproveStatus, equityQueryDTO.getLastApprovalStatus())
                    .like(StrUtil.isNotBlank(equityQueryDTO.getLastApplyCode()), ContractEquityVOEntity::getLastApplyCode, StrUtil.isNotBlank(equityQueryDTO.getLastApplyCode()) ? equityQueryDTO.getLastApplyCode().trim() : null)
                    .like(StrUtil.isNotBlank(equityQueryDTO.getLastUpdatedBy()), ContractEquityVOEntity::getLastUpdatedBy, equityQueryDTO.getLastUpdatedBy())
                    .between(StrUtil.isNotBlank(equityQueryDTO.getLastUpdatedAt()), ContractEquityVOEntity::getLastUpdatedAt, StrUtil.isNotBlank(equityQueryDTO.getLastUpdatedAt()) ? DateTimeUtil.parseTimeStamp0000(equityQueryDTO.getLastUpdatedAt().split(" ")[0]) : "", StrUtil.isNotBlank(equityQueryDTO.getLastUpdatedAt()) ? DateTimeUtil.parseTimeStamp2359(equityQueryDTO.getLastUpdatedAt().split(" ")[0]) : "")
                    // 转月次数
                    .eq(null != equityQueryDTO.getAbleTransferTimes(), ContractEquityVOEntity::getAbleTransferTimes, equityQueryDTO.getAbleTransferTimes())
                    .eq(null != equityQueryDTO.getTransferredTimes(), ContractEquityVOEntity::getTransferredTimes, equityQueryDTO.getTransferredTimes())
                    .eq(null != equityQueryDTO.getAbleReversePriceTimes(), ContractEquityVOEntity::getAbleReversePriceTimes, equityQueryDTO.getAbleReversePriceTimes())
                    .eq(null != equityQueryDTO.getReversedPriceTimes(), ContractEquityVOEntity::getReversedPriceTimes, equityQueryDTO.getReversedPriceTimes())
                    .and(wrapper -> wrapper.isNull(ContractEquityVOEntity::getCloseTailNum).or().eq(ContractEquityVOEntity::getCloseTailNum, BigDecimal.ZERO))
                    .orderByDesc(ContractEquityVOEntity::getLastUpdatedAt)
                    .orderByDesc(ContractEquityVOEntity::getContractId);
        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }
}
