package com.navigator.trade.service.contract.purchase;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.Impl.BaseContractSplitAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 豆粕合同拆分的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_P_CONTRACT_SPLIT,SBO_P_CONTRACT_SPLIT")
public class PurchaseContractSplitService extends BaseContractSplitAbstractService {
    @Resource
    public IContractOperationNewService purchaseContractOperationService;

    @Override
    protected void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 记录操作记录
        purchaseContractOperationService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.SPLIT_PURCHASE_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                SystemEnum.MAGELLAN.getValue());
    }

    @Override
    protected ContractEntity operateSonContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 判断主题是否发生变更
        if (!contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            contractModifyDTO.setContractSource(ContractActionEnum.SPLIT_CUSTOMER.getActionValue());
        } else {
            contractModifyDTO.setContractSource(ContractActionEnum.SPLIT.getActionValue());
        }
        contractModifyDTO.setParentContractEntity(contractEntity);

        // 生成子合同
        IContractService createService = salesContractHandler.getStrategy(
                contractEntity.getSalesType(),
                ContractTradeTypeEnum.NEW.getValue(),
                contractEntity.getGoodsCategoryId());

        return createService.createContractByModify(contractModifyDTO);
    }
}
