package com.navigator.trade.facade.impl;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.common.dto.Result;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import com.navigator.trade.service.IDeliveryTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-06 18:01
 */
@RestController
public class DeliveryTypeFacadeImpl implements DeliveryTypeFacade {
    @Autowired
    private IDeliveryTypeService iDeliveryTypeService;
    @Resource
    private EmployFacade employFacade;

    @Override
    public List<DeliveryTypeEntity> getAllDeliveryTypeList(Integer status, Integer categoryId, String siteCode, String buCode, Integer type) {
        List<DeliveryTypeEntity> list = iDeliveryTypeService.getAllDeliveryTypeList(status, categoryId, siteCode, buCode, type);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(it -> {
                if (null != it.getUpdatedBy() && 0 != it.getUpdatedBy()) {
                    EmployEntity employEntity = employFacade.getEmployById(it.getUpdatedBy());
                    it.setUpdator(null == employEntity ? "" : employEntity.getName());
                }
            });
        }
        return list;
    }

    @Override
    public List<DeliveryTypeVO> getDeliveryTypeByCategoryId(Integer categoryId, String siteCode, String buCode) {
        return iDeliveryTypeService.getDeliveryTypeByCategoryId(categoryId, siteCode, buCode);
    }

    @Override
    public List<DeliveryTypeEntity> getAllDeliveryByAddressType(Integer status, Integer addressType) {
        return iDeliveryTypeService.getAllDeliveryByAddressType(status, addressType);
    }


    @Override
    public DeliveryTypeEntity getDeliveryTypeById(Integer id) {
        return iDeliveryTypeService.getDeliveryTypeById(id);
    }

    @Override
    public DeliveryTypeEntity getDeliveryTypeByLkgCode(String lkgCode) {
        return iDeliveryTypeService.getDeliveryTypeByLkgCode(lkgCode);
    }

    @Override
    public Result saveOrUpdateDeliveryType(DeliveryTypeEntity deliveryTypeEntity) {
        return Result.judge(iDeliveryTypeService.saveOrUpdateDeliveryType(deliveryTypeEntity));
    }

    @Override
    public Result invalidStatus(Integer deliveryTypeId) {
        return Result.judge(iDeliveryTypeService.invalidStatus(deliveryTypeId));
    }

    @Override
    public Result importDeliveryType(MultipartFile uploadFile) {
        return iDeliveryTypeService.importDeliveryType(uploadFile);
    }

    @Override
    public List<Integer> getSendDeliveryTypeIdList() {
        return iDeliveryTypeService.getSendDeliveryTypeIdList();
    }

}
