package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.CheckContractMapper;
import com.navigator.trade.pojo.entity.CheckContractEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-25
 */
@Dao
public class CheckContractDao extends BaseDaoImpl<CheckContractMapper, CheckContractEntity> {

    public List<CheckContractEntity> getByBatchAndContractCode(List<String> contractCodeList, String batch) {
        return list(Wrappers.<CheckContractEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(contractCodeList), CheckContractEntity::getContractCode, contractCodeList)
                .eq(CheckContractEntity::getCheckBatch, batch));
    }
}
