package com.navigator.trade.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.facade.DomainCodeFacade;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceAuditDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceLeadDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import com.navigator.trade.service.futrue.IDomainCodeService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/20 11:57
 */
@RestController
public class DomainCodeFacadeImpl implements DomainCodeFacade {

    @Resource
    IDomainCodeService domainService;

    @Override
    public Result getClosingPrice(Integer categoryId, String domainCode, String categoryCode) {
        return Result.success(domainService.getClosingPrice(categoryId, domainCode, categoryCode));
    }

    @Override
    public List<DomainPriceLeadDTO> previewDomainPrice(MultipartFile file) {
        return domainService.previewDomainPrice(file);
    }

    @Override
    public Result uploadDomainPrice(MultipartFile file) {
        return domainService.uploadDomainPrice(file);
    }

    @Override
    public Result queryDomainPrice(QueryDTO<QueryDomainPriceBO> queryDTO) {
        return domainService.queryDomainPrice(queryDTO);
    }

    @Override
    public Result audit(DomainPriceAuditDTO domainPriceAuditDTO) {
        return Result.judge(domainService.audit(domainPriceAuditDTO));
    }

    @Override
    public Result queryDomainPriceToday(DomainPriceTodayDTO domainPriceTodayDTO) {
        return Result.success(domainService.queryDomainPriceToday(domainPriceTodayDTO));
    }

    @Override
    public Result getLastestClosingPrice(Integer categoryId, String domainCode, Date signDate, String categoryCode) {
        return Result.success(domainService.getLastestClosingPrice(categoryId, domainCode, signDate, categoryCode));
    }
}
