package com.navigator.trade.service.contract;

import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <p>
 * 合同的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
public interface IContractService {

    /**
     * 创建合同
     *
     * @param contractCreateDTO
     * @return
     */
    ContractEntity createContract(ContractCreateDTO contractCreateDTO);

    /**
     * 变更创建子合同
     *
     * @param contractModifyDTO
     * @return
     */
    ContractEntity createContractByModify(ContractModifyDTO contractModifyDTO);

    /**
     * 部分转月、反点价生成子合同
     *
     * @param contractTransferDTO
     * @return
     */
    ContractEntity createSonContract(ContractTransferDTO contractTransferDTO);

    /**
     * 合同拆分
     *
     * @param contractModifyDTO
     */
    List<TTQueryVO> splitContract(ContractModifyDTO contractModifyDTO);

    /**
     * 合同修改
     *
     * @param contractModifyDTO
     */
    List<TTQueryVO> reviseContract(ContractModifyDTO contractModifyDTO);

    /**
     * 申请回购
     *
     * @param contractBuyBackDTO
     */
    List<TTQueryVO> applyBuyBack(ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 申请解约定赔
     *
     * @param contractWashOutDTO
     */
    List<TTQueryVO> applyWashOut(ContractWashOutDTO contractWashOutDTO);

    /**
     * 申请关闭
     *
     * @param contractId
     */
    List<TTQueryVO> applyClosed(Integer contractId);
}
