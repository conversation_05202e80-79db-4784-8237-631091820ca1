package com.navigator.trade.service.impl;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.trade.dao.ContractPaperDao;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.service.IContractPaperService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.contractsign.IContractSignService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.Lint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 正本快递信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Service
@Slf4j
public class ContractPaperServiceImpl implements IContractPaperService {

    @Autowired
    private ContractPaperDao contractPaperDao;
    @Resource
    private IContractSignQueryService iContractSignQueryService;
    @Resource
    private MessageFacade messageFacade;

    @Override
    public ContractPaperEntity getContractPaper(Integer contractSignId) {
        ContractPaperEntity contractPaper = contractPaperDao.getContractPaper(contractSignId);
        return contractPaper;
    }

    @Override
    public boolean saveContractPaper(ContractPaperDTO contractPaperDTO) {
        ContractPaperEntity contractPaperEntity = BeanConvertUtils.convert(ContractPaperEntity.class, contractPaperDTO);
        ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailById(contractPaperEntity.getContractSignId());

        if (ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType()) {
            contractPaperEntity.setCustomerId(contractSignEntity.getCustomerId());
        } else {
            contractPaperEntity.setCustomerId(Integer.parseInt(contractSignEntity.getSupplierId()));
        }

        contractPaperEntity.setCreatedAt(new Date())
                .setUpdatedAt(new Date());
        if (null == contractPaperDTO.getId()) {
            return contractPaperDao.save(contractPaperEntity);
        }

        return contractPaperDao.updateById(contractPaperEntity);
    }

    /*@Override
    public void sendContractSignOriginalPaper() {
        //根据客户id分组查询信息
        List<ContractPaperEntity> paperGroups = contractPaperDao.sendContractSignOriginalPaper(null);

        for (ContractPaperEntity paperGroup : paperGroups) {
            //查询正本快递信息
            List<ContractPaperEntity> contractPaperEntities = contractPaperDao.sendContractSignOriginalPaper(paperGroup.getCustomerId());
            //塞入发送消息参数
            MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
            messageInfoDTO.setReferBizId(String.valueOf(contractPaperEntities.stream().map(ContractPaperEntity::getContractSignId).distinct().collect(Collectors.toList())));
            messageInfoDTO.setBusinessSceneCode(MessageBusinessCodeEnum.COLUMBUS_ORIGINAL_PAPER.name());
            messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
            List<Map<String, Object>> dataMaps = new ArrayList<>();
            //遍历查询正本快递信息
            for (ContractPaperEntity contractPaperEntity : contractPaperEntities) {
                //根据id查询协议信息
                ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailById(contractPaperEntity.getContractSignId());
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("contractCode", contractSignEntity.getContractCode());
                dataMap.put("TTCode", contractSignEntity.getTtCode());
                dataMap.put("mailGoTime", DateTimeUtil.formatDateStringCN(contractPaperEntity.getMailGoTime()));
                dataMap.put("ldcDeliverySn", contractPaperEntity.getLdcDeliverySn());

            }
            messageInfoDTO.setDataMaps(dataMaps);
            //发送消息
            try {
                messageFacade.sendMessage(messageInfoDTO);
            } catch (Exception e) {
                log.debug("消息服务异常:" + e.getMessage());
            }
        }
    }*/

    @Override
    public List<Integer> sendContractSignOriginalPaper() {
        List<ContractPaperEntity> paperGroups = contractPaperDao.sendContractSignOriginalPaper();
        return paperGroups.stream().map(ContractPaperEntity::getContractSignId).distinct().collect(Collectors.toList());
    }
}
