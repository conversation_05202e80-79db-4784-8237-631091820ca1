package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.enums.systemrule.DepositSceneEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.tradeticket.impl.purchase.PurchaseTTAddServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

import static com.navigator.admin.pojo.enums.LogBizCodeEnum.SUBMIT_BUYBACK_TT;

@Slf4j
@Component("SBM_P_TT_BUYBACK,SBO_P_TT_BUYBACK")
public class SalesTTBuybackServiceImpl extends PurchaseTTAddServiceImpl {
    public SalesTTBuybackServiceImpl() {
        ttTypeEnum = TTTypeEnum.BUYBACK;
        contractTradeTypeEnum = ContractTradeTypeEnum.BUYBACK;
        contractSource = ContractActionEnum.BUYBACK.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.NEW.getType();
        salesType = ContractSalesTypeEnum.PURCHASE;
        processorType = ProcessorTypeEnum.SBM_P_BUYBACK.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
        logBizCodeEnum = SUBMIT_BUYBACK_TT;
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_P_BUYBACK.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TTAddEntity convertToTTAdd(SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO) {
        TTAddEntity ttAddEntity = new TTAddEntity();
        BeanUtils.copyProperties(salesContractAddTTDTO, ttAddEntity);
        ttAddEntity.setRootContractId(salesContractAddTTDTO.getRootContractId());
        //查询客户信息
        CustomerDTO customer = null;
        CustomerDetailEntity customerDetailEntities = null;
        if (null != salesContractAddTTDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
        }
        if (customer != null) {
            ttAddEntity.setCustomerCode(customer.getLinkageCustomerCode());
            ttAddEntity.setCustomerId(customer.getId());
            ttAddEntity.setCustomerName(customer.getName());
            ttAddEntity.setEnterprise(customer.getEnterprise());
        }
        //迟付款罚金
        if (StringUtil.isNumeric(salesContractAddTTDTO.getDelayPayFine())) {
            ttAddEntity.setDelayPayFine(new BigDecimal(salesContractAddTTDTO.getDelayPayFine()));
        }
        //查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractAddTTDTO.getSupplierId()) {
            //supplier = customerFacade.getCustomerById(salesContractAddTTDTO.getSupplierId());
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(salesContractAddTTDTO.getSupplierId())
                    .setCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                    .setFactoryCode(salesContractAddTTDTO.getDeliveryFactoryCode())
                    .setSalesType(salesContractAddTTDTO.getSalesType())
                    .setCompanyId(salesContractAddTTDTO.getCompanyId())
                    .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                    .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
            ;
            supplier = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        }
        if (supplier != null) {
            ttAddEntity.setSupplierId(supplier.getId());
            ttAddEntity.setSupplierName(supplier.getName());
            //ttAddEntity.setSignPlace(supplier.getSignPlace());
            List<CustomerBankDTO> customerBankList = supplier.getCustomerBankDTOS();
            if (customerBankList != null && customerBankList.size() > 0) {
                ttAddEntity.setSupplierAccount(customerBankList.get(0).getBankAccountNo());
            }
        }
        if (salesContractAddTTDTO.getSupplierAccountId() != null) {
            CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(salesContractAddTTDTO.getSupplierAccountId());
            if (customerBankEntity != null) {
                ttAddEntity.setSupplierAccount(customerBankEntity.getBankAccountNo());
            }
        }
        //签订地
        ttAddEntity.setSignPlace(salesContractAddTTDTO.getSignPlace());

        //单位
        ttAddEntity.setUnit(UnitEnum.TON.name());

        //袋皮扣重【采销同适用】
        //      a.豆粕：当【袋皮扣重】选择不扣袋皮时，【包装计算重量】默认为否；当选择具体的值时，【包装计算重量】默认为是
        //      b.豆油：默认【包装计算重量】为否，袋皮扣重【不扣皮】
        ttAddEntity.setPackageWeight(salesContractAddTTDTO.getPackageWeight());
        Integer needPackageWeight = 0;
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(salesContractAddTTDTO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                String ruleKey = systemRuleItemEntity.getRuleKey();
                if (!"不扣皮".equalsIgnoreCase(ruleKey)) {
                    needPackageWeight = 1;
                }
            }
        }
        ttAddEntity.setNeedPackageWeight(needPackageWeight);

        //币种
        ttAddEntity.setCurrencyType(CurrencyTypeEnum.CNY.getDesc());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);

        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));

        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);
        //获取商品信息
        SkuEntity skuEntity = skuFacade.getSkuById(salesContractAddTTDTO.getGoodsId());
        if (null != skuEntity.getTaxRate()) {
            //税率
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            ttAddEntity.setTaxRate(taxRate);
            //cifUnitPrice
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
            ttAddEntity.setCifUnitPrice(cifUnitPrice);
        }
        if (null != salesContractAddTTDTO.getGoodsCategoryId()) {
            ttAddEntity.setGoodsCategoryId(salesContractAddTTDTO.getGoodsCategoryId());
        }
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getGoodsPackageId())) {
            ttAddEntity.setGoodsPackageId(Integer.parseInt(salesContractAddTTDTO.getGoodsPackageId()));
        }
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getGoodsSpecId())) {
            ttAddEntity.setGoodsSpecId(Integer.parseInt(salesContractAddTTDTO.getGoodsSpecId()));
        }

        if (null != salesContractAddTTDTO.getSupplierId() && null != salesContractAddTTDTO.getGoodsCategoryId()) {
            customerDetailEntities = customerDetailFacade.queryCustomerDetailEntity(salesContractAddTTDTO.getSupplierId(), salesContractAddTTDTO.getCategory3());
        }
        if (null != customerDetailEntities) {
            ttAddEntity.setQualityCheck(customerDetailEntities.getQualityCheckContent());
        }

        //查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(salesContractAddTTDTO.getCompanyId())
                .setCustomerId(salesContractAddTTDTO.getSupplierId())
                .setCategory1(String.valueOf(salesContractAddTTDTO.getCategory1()))
                .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);

        if (!customerInvoiceEntities.isEmpty()) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(customerInvoiceEntities.get(0).getInvoiceId());
            if (systemRuleItemEntity != null) {
                //发票类型
                ttAddEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
//                //税率
//                BigDecimal taxRate = new BigDecimal(systemRuleItemEntity.getRuleValue()).divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
//                ttAddEntity.setTaxRate(taxRate);
//                //cifUnitPrice
//                BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
//                ttAddEntity.setCifUnitPrice(cifUnitPrice);
            }
        }

        //合同总量
        BigDecimal contractNum = BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getContractNum());
        ttAddEntity.setContractNum(contractNum);

        //计算总价(确认无 temporaryPrice  transactionPrice)
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, unitPrice);
        ttAddEntity.setTotalAmount(totalAmount);
        //迟付款罚金
        //BigDecimal delayPayFine = totalAmount.multiply(new BigDecimal("0.0008"));
        if (StringUtil.isNumeric(salesContractAddTTDTO.getDelayPayFine())) {
            ttAddEntity.setDelayPayFine(new BigDecimal(salesContractAddTTDTO.getDelayPayFine()));
        }
        //基差价
        ttAddEntity.setExtraPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getExtraPrice()));
        ttAddEntity.setForwardPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getForwardPrice()));

        //付款方式
        if (salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() != 0) {
            ttAddEntity.setPaymentType(PaymentTypeEnum.CREDIT.getType());
        } else {
            ttAddEntity.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        }
        //应付履约保证金
        ttAddEntity.setDepositAmount(BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getDepositAmount()));

        //追加履约保证金
        // 客户Id
        Integer customerId = salesContractAddTTDTO.getCustomerId();
        // 品类Id
        Integer categoryId = salesContractAddTTDTO.getGoodsCategoryId();
        // 合同类型
        String contractType = salesContractAddTTDTO.getContractType();

        if (null != customerId && null != categoryId && StringUtils.isNotEmpty(contractType)) {
            DepositRuleDTO depositRuleDTO = new DepositRuleDTO()
                    .setContractType(customerId)
                    .setCustomerId(customerId)
                    .setCategoryId(categoryId)
                    .setRuleType(DepositSceneEnum.FALL.getType())
                    .setContractNum(contractNum);
            BigDecimal addedDepositAmount = depositRuleFacade.calcContractUseDeposit(depositRuleDTO);
            ttAddEntity.setAddedDepositAmount(addedDepositAmount);
        }

        //交货时间
        ttAddEntity.setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime());
        ttAddEntity.setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime());

        //点价截止时间
        ttAddEntity.setPriceEndTime(salesContractAddTTDTO.getPriceEndTime());

        //履约保证金比例id
        ttAddEntity.setDepositRate(salesContractAddTTDTO.getDepositRate());

        //期货合约
        ttAddEntity.setDomainCode(salesContractAddTTDTO.getDomainCode());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getShipWarehouseId())) {
            ttAddEntity.setShipWarehouseId(Integer.parseInt(salesContractAddTTDTO.getShipWarehouseId()));
        }
        ttAddEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttAddEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        // BUGFIX：case-1003162 回购的，采购协议带出了M-S-转货权 Author: Mr 2025-04-29 Start
        ttAddEntity.setDestinationValue(systemRuleConvertValue(ttAddEntity.getDestination()));
        ttAddEntity.setPackageWeightValue(systemRuleConvertValue(ttAddEntity.getPackageWeight()));
        ttAddEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttAddEntity.getDeliveryType()));
        ttAddEntity.setWeightCheckValue(systemRuleConvertValue(ttAddEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        if (Objects.nonNull(ttAddEntity.getInvoiceType())) {
            ttAddEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(ttAddEntity.getInvoiceType()));
        }
        ttAddEntity.setShipWarehouseValue(factoryConvertValue(ttAddEntity.getShipWarehouseId()));
        // BUGFIX：case-1003162 回购的，采购协议带出了M-S-转货权 Author: Mr 2025-04-29 End
        return ttAddEntity;
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }

    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());

            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }

        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }

}

