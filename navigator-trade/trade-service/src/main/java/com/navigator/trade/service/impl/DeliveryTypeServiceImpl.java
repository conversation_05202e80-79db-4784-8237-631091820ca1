package com.navigator.trade.service.impl;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.dao.DeliveryTypeDao;
import com.navigator.trade.pojo.dto.DeliveryTypeImportDTO;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import com.navigator.trade.pojo.enums.DeliveryTransportType;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeliveryTypeServiceImpl implements IDeliveryTypeService {
    @Resource
    private DeliveryTypeDao deliveryTypeDao;
    @Resource
    private SiteFacade siteFacade;

    @Override
    public List<DeliveryTypeEntity> getAllDeliveryTypeList(Integer status, Integer categoryId, String siteCode, String buCode, Integer type) {
        //只有同步LKG且为豆粕豆油，取豆粕豆油配置，其他的取通用
        if (null != categoryId) {
            categoryId = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue()).contains(categoryId) ? categoryId : 0;
            if (StringUtils.isNotBlank(siteCode)) {
                SiteEntity siteEntity = siteFacade.getSiteByCode(siteCode);
                if (null != siteEntity && SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem())) {
                    categoryId = 0;
                }
            }
        }
        return deliveryTypeDao.getAllDeliveryTypeList(status, categoryId, buCode, type);
    }

    @Override
    public List<DeliveryTypeVO> getDeliveryTypeByCategoryId(Integer categoryId, String siteCode, String buCode) {
        categoryId = null == categoryId ? 0 : categoryId;
        List<DeliveryTypeEntity> deliveryTypeEntities = this.getAllDeliveryTypeList(DisableStatusEnum.ENABLE.getValue(), categoryId, siteCode, buCode, null);
        Map<Integer, List<DeliveryTypeEntity>> deliveryTypeMap = deliveryTypeEntities.stream().collect(Collectors.groupingBy(DeliveryTypeEntity::getType));
        return deliveryTypeMap.keySet().stream()
                .map(deliveryType -> {
                    return new DeliveryTypeVO().setDeliveryType(deliveryType)
                            .setDeliveryTypeName(DeliveryModeEnum.getByValue(deliveryType).getDesc())
                            .setDeliveryTypeList(deliveryTypeMap.get(deliveryType));
                }).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryTypeEntity> getAllDeliveryByAddressType(Integer status, Integer addressType) {
        return deliveryTypeDao.getAllDeliveryByAddressType(status, addressType);
    }

    @Override
    public DeliveryTypeEntity getDeliveryTypeById(Integer id) {
        return deliveryTypeDao.getById(id);
    }

    @Override
    public DeliveryTypeEntity getDeliveryTypeByLkgCode(String lkgCode) {
        return deliveryTypeDao.getDeliveryTypeByLkgCode(lkgCode);
    }

    @Override
    public boolean saveOrUpdateDeliveryType(DeliveryTypeEntity deliveryTypeEntity) {
        if (StringUtils.isNotBlank(deliveryTypeEntity.getLkgCode()) && 0 != deliveryTypeEntity.getCategoryId()) {
            deliveryTypeEntity.setLkgCode(deliveryTypeEntity.getLkgCode().trim());
            DeliveryTypeEntity LkgCodeDeliveryType = deliveryTypeDao.getDeliveryTypeByLkgCodeNotId(deliveryTypeEntity.getLkgCode(), deliveryTypeEntity.getId());
            if (null != LkgCodeDeliveryType) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_TYPE_LKG_CODE_EXIST);
            }
        }
        if (null == deliveryTypeEntity.getId()) {
            deliveryTypeEntity.setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCategoryId(null == deliveryTypeEntity.getCategoryId() ? 0 : deliveryTypeEntity.getCategoryId())
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()));
            return deliveryTypeDao.save(deliveryTypeEntity);
        } else {
            deliveryTypeEntity.setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedAt(DateTimeUtil.now());
            return deliveryTypeDao.updateById(deliveryTypeEntity);
        }
    }

    @Override
    public boolean invalidStatus(Integer deliveryTypeId) {
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeDao.getById(deliveryTypeId);
        if (null == deliveryTypeEntity) {
            return false;
        }
        // Start: Case-1002964: 交提货方式更新后未更新“更新人” by nana Date:20250219
        deliveryTypeEntity.setStatus(DisableStatusEnum.ENABLE.getValue().equals(deliveryTypeEntity.getStatus()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue())
                .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setUpdatedAt(DateTimeUtil.now());
        // END: Case-1002964: 交提货方式更新后未更新“更新人” by nana Date:20250219
        return deliveryTypeDao.updateById(deliveryTypeEntity);
    }

    @Override
    public Result importDeliveryType(MultipartFile uploadFile) {
        try {
            List<DeliveryTypeImportDTO> testImportDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, DeliveryTypeImportDTO.class);
            log.info(FastJsonUtils.getBeanToJson(testImportDTOList));
            if (!CollectionUtils.isEmpty(testImportDTOList)) {
                for (DeliveryTypeImportDTO testImportDTO : testImportDTOList) {
                    DeliveryTypeEntity deliveryTypeEntity = new DeliveryTypeEntity()
                            .setCategoryId(GoodsCategoryEnum.getByCode(testImportDTO.getCategoryCode().trim()).getValue())
                            .setType(DeliveryModeEnum.getByDesc(testImportDTO.getType().trim()).getValue())
                            .setName(StringUtils.isBlank(testImportDTO.getName()) ? "" : testImportDTO.getName())
                            .setContractTerms(StringUtils.isBlank(testImportDTO.getContractTerms()) ? "" : testImportDTO.getContractTerms())
                            .setLkgCode(StringUtils.isBlank(testImportDTO.getLkgCode()) ? "" : testImportDTO.getLkgCode())
                            .setStatus(DisableStatusEnum.ENABLE.getValue())
                            .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                            .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                            .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                    if (StringUtils.isBlank(testImportDTO.getTransportWay())) {
                        deliveryTypeEntity.setTransportWay(DeliveryTransportType.getByDesc(testImportDTO.getTransportWay()).getValue());
                    }
                    deliveryTypeDao.save(deliveryTypeEntity);
                }
            }
            return Result.success(testImportDTOList);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
    }

    @Override
    public List<Integer> getSendDeliveryTypeIdList() {
        return deliveryTypeDao.getSendDeliveryTypeIdList();
    }


}
