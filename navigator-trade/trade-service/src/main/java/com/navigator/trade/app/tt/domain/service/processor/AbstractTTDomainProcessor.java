package com.navigator.trade.app.tt.domain.service.processor;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.ContractPriceDao;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.service.IContractPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public abstract class AbstractTTDomainProcessor {
    @Autowired
    TradeTicketDao tradeTicketDao;

    @Autowired
    ContractPriceDao contractPriceDao;

    @Autowired
    IContractPriceService contractPriceService;

    // 创建记录
    public TradeTicketDO createTradeTicket(TradeTicketDO tradeTicketDO) {
        /**
         * 1、前处理
         * 2、新增主表记录
         * 3、新增副表记录
         * 4、后处理
         */

        // 前处理
        beforeCreateProcess();

        //保存数据
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        tradeTicketDao.saveOrUpdate(tradeTicketEntity);
        //更新DO信息
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);

        //新增副表记录
        addTTSubEntity(tradeTicketDO);

        //保存价格信息
        ContractPriceEntity priceEntity = tradeTicketDO.getContractPriceEntity();
        if (Objects.nonNull(priceEntity)) {
            priceEntity.setTtId(tradeTicketEntity.getId());
            contractPriceService.saveOrUpdate(priceEntity);
            //更新DO信息
            tradeTicketDO.setContractPriceEntity(priceEntity);
        }

        //后处理
        afterCreateProcess();


        return tradeTicketDO;


    }

    public boolean updateTTContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {

        // 更新tradeTicket的合同关系
        tradeTicketDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        // 更新TT与协议关系
        if (ObjectUtil.isNotEmpty(tradeTicketEntity.getSignId())) {
            tradeTicketDao.updateSignInfo(tradeTicketEntity.getId(), new ContractSignEntity()
                    .setId(tradeTicketEntity.getSignId())
                    .setProtocolCode(tradeTicketEntity.getProtocolCode()));
        }

        //更新子表的合同信息
        updateTTSubEntityContractInfo(tradeTicketEntity, contractEntity);

        // 更新TT-price的合同关系
        contractPriceService.updateContractId(tradeTicketEntity.getId(), contractEntity.getId());
        return true;
    }

    public boolean updateTTSignInfo(TradeTicketEntity tradeTicketEntity, ContractSignEntity contractSignEntity) {

        // 更新tradeTicket的合同关系
        int rtn = tradeTicketDao.updateSignInfo(tradeTicketEntity.getId(), contractSignEntity);

        return rtn > 0;
    }


    // 更新记录

    // 查询记录

    abstract void addTTSubEntity(TradeTicketDO tradeTicketDO);

    abstract boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity);


    private void updateTTSubEntity() {
        //TO BE OVERRIDED
    }

    private void beforeCreateProcess() {
        //TO BE OVERRIDED
    }

    private void afterCreateProcess() {
        //TO BE OVERRIDED
    }


}
