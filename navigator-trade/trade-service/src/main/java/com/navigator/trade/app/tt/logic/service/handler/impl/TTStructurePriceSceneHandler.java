package com.navigator.trade.app.tt.logic.service.handler.impl;

import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StructureCodeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryStructureVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("STRUCTURE_PRICE_HANDLER")
public class TTStructurePriceSceneHandler extends AbstractTTSceneHandler {
    @Autowired
    private StructureRuleFacade structureRuleFacade;

    @Override
    public void initDTO(TTDTO ttdto) {
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        //初始化交易、销售类型、合同来源
        salesStructurePriceTTDTO.setStatus(TTStatusEnum.NEW.getType());
        salesStructurePriceTTDTO.setTradeType(ContractTradeTypeEnum.STRUCTURE_PRICE.getValue());
        salesStructurePriceTTDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        salesStructurePriceTTDTO.setContractSource(ContractActionEnum.STRUCTURE_PRICE_CONFIRM.getActionValue());
        //协议签署状态
        salesStructurePriceTTDTO.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        String userId = JwtUtils.getCurrentUserId();
        salesStructurePriceTTDTO.setUserId(userId);
        String code = salesStructurePriceTTDTO.getCode();

        //根据code存在与否, 判断是否为新增
        if (StringUtils.isBlank(code)) {
            salesStructurePriceTTDTO.setCreateStatus(true);
            //生成TT编号
            code = CodeGeneratorUtil.genSalesTTNewCode();
            salesStructurePriceTTDTO.setCode(code);
            //生成合同编号
            if (null != salesStructurePriceTTDTO.getSupplierId()) {
                CustomerDTO supplier = customerFacade.getCustomerById(salesStructurePriceTTDTO.getSupplierId());
                if (null != supplier) {
                    CompanyEntity companyEntity = companyFacade.queryCompanyById(supplier.getCompanyId());
                    String contractCode = contractService.genStructureContractCode(companyEntity.getShortName());
                    salesStructurePriceTTDTO.setContractCode(contractCode);
                }
            }
        } else {
            // TODD 直接提交的，改代码要去掉
            salesStructurePriceTTDTO.setCreateStatus(false);
            //设置ttId
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(salesStructurePriceTTDTO.getCode());
            if (tradeTicketEntity == null) {
                throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
            }
            Integer ttId = tradeTicketEntity.getId();
            salesStructurePriceTTDTO.setTtId(ttId);
            salesStructurePriceTTDTO.setContractCode(tradeTicketEntity.getContractCode());
        }
        ttdto.setSalesStructurePriceTTDTO(salesStructurePriceTTDTO);
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {

        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        // 1、初始化
        initDTO(ttdto);
        // 2、convert  TODO 转换需要调整
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.structureTradeTicketDO(ttdto);
        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        // 3、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        // 4、提交审批【TT完成也是要调用-出审批记录】
        Integer ttId = tradeTicketEntity.getId();
        //发起审批
        ttApproveHandler.startTTApprove(ttId, ttdto, null);
        // 5、返回结果
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode()).setCode(tradeTicketEntity.getCode()).setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        ResultCodeEnum submitResult = ResultCodeEnum.OK;
        if (ResultCodeEnum.OK.equals(submitResult)) {
            return list;
        } else {
            throw new BusinessException(submitResult);
        }
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTStructureEntity ttStructureEntity = (TTStructureEntity) tradeTicketDO.getTtSubEntity();

        TTQueryStructureVO ttQueryStructureVO = new TTQueryStructureVO();
        TTDetailVO ttDetailVO = new TTDetailVO();
        BeanUtils.copyProperties(ttStructureEntity, ttQueryStructureVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryStructureVO);
        ttQueryStructureVO
                .setTtId(tradeTicketEntity.getId())
                .setGoodsCategoryId(tradeTicketEntity.getSubGoodsCategoryId())
                .setGoodsCategoryName(GoodsCategoryEnum.getDescByValue(tradeTicketEntity.getSubGoodsCategoryId()))
                .setTradeTypeName(ContractTradeTypeEnum.getDescByValue(tradeTicketEntity.getTradeType()));
        if (StringUtils.isBlank(ttStructureEntity.getStructureName())) {
            if (null != ttStructureEntity.getStructureType()) {
                String structureName = structureRuleFacade.getNameById(ttStructureEntity.getStructureType());
                ttQueryStructureVO.setStructureTypeName(structureName);
            }
        } else {
            ttQueryStructureVO.setStructureTypeName(StructureCodeUtil.numToCode(ttStructureEntity.getStructureType()) + "-" + ttStructureEntity.getStructureName());
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryStructureVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryStructureVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryStructureVO.setCreatedByName(employEntity.getName());
            }
        }
        ttDetailVO.setTtQueryStructureVO(ttQueryStructureVO);
        ttDetailVO.setDetailType("6");
        return ttDetailVO;
    }
}
