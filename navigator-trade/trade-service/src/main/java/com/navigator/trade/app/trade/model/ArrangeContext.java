package com.navigator.trade.app.trade.model;

import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 服务编排上下文信息
 * @Date 2024/7/15
 * @Version 1.0
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArrangeContext {
    /**
     * TT领域模型
     */
    TradeTicketDO tradeTicketDO;

    /**
     * 操作日志
     */
    RecordBizOperationDTO recordBizOperationDTO;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同编号
     */
    private Integer contractId;

    /**
     * 拆分保存的定价单信息
     */
    String confirmPriceInfo;

    /**
     * 多TT时，TT分组id
     */
    String groupId;

    /**
     * 批量TT创建时的合同分组ID
     */
    private Integer contractGroupId;

    /**
     * 转月 - 价格详情
     */
    PriceDetailBO priceDetailBO;

    /**
     * 转月 - 本次手续费
     */
    private BigDecimal thisTimeFee;
}
