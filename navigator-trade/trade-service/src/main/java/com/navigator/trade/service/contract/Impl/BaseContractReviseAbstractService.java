package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 变更合同的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
public abstract class BaseContractReviseAbstractService extends BaseContractAbstractService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> reviseContract(ContractModifyDTO contractModifyDTO) {

        ContractEntity contractEntity = contractValueObjectService.getContractById(contractModifyDTO.getContractId());

        // 区分是保存还是提交
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            // 一个合同仅支持保存一个变更TT！
            String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
            if (StringUtils.isNotBlank(saveTimes)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_SAVE_TT);
            }

            // 保存tt到新录入状态
            List<TTQueryVO> ttQueryVOS = operateTradeTicket(null, contractEntity, contractModifyDTO);

            // 一个合同仅支持保存一个变更TT！
            redisUtil.set(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode(), "1");

            return ttQueryVOS;
        }

        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SUBMIT.getValue() ||
                contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE_SUBMIT.getValue()) {

            // 1.校验合同数据
            checkContractInfo(contractEntity, contractModifyDTO);

            // 2.处理子合同
            ContractEntity sonContractEntity = operateSonContract(contractEntity, contractModifyDTO);

            // 3.处理TT
            List<TTQueryVO> ttQueryVOS = operateTradeTicket(sonContractEntity, contractEntity, contractModifyDTO);

            // 4.记录拆分日志
            recordOperationLog(contractModifyDTO, contractEntity);

            // 5.更新父合同
            operateFatherContract(contractEntity, contractModifyDTO);

            return ttQueryVOS;
        }
        return null;
    }

    /**
     * 日志处理
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected abstract void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);

    @Override
    public List<TTQueryVO> splitContract(ContractModifyDTO contractModifyDTO) {
        // 不处理
        return null;
    }

    //====抽象方法：子类必须实现和替换的方法====

    /**
     * 校验合同信息
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    protected void checkContractInfo(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 判断客户是否可用
        if (!isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }

        // 校验付款代码是否禁用
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(contractModifyDTO.getPayConditionId());
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
        }

        //校验质量指标
        QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        Integer customerId = contractModifyDTO.getCustomerId();
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            customerId = contractModifyDTO.getSupplierId();
        }
        qualityInfoDTO
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                .setWarehouseId(Integer.parseInt(contractModifyDTO.getShipWarehouseId()))
                .setUsage(contractModifyDTO.getUsage())
                .setGoodsId(contractModifyDTO.getGoodsId())
                .setSalesType(contractEntity.getSalesType())
                .setCustomerId(customerId);
        // 特种油脂
        if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(contractEntity.getCategory2())) {
            qualityInfoDTO.setStandardType(contractEntity.getStandardType());
        }
        Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            throw new BusinessException(ResultCodeEnum.QUALITY_NOT_COMPLETED);
        }

        // 校验客户主数据赊销账期/预付款
        if (contractModifyDTO.getPaymentType().equals(PaymentTypeEnum.CREDIT.getType())) {
            // 获取主数据
            CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
            creditPaymentDTO.setCustomerId(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractModifyDTO.getCustomerId() : contractModifyDTO.getSupplierId())
                    .setCategoryId(contractModifyDTO.getGoodsCategoryId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setCategory1(String.valueOf(contractEntity.getCategory1()))
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
                    .setBuCode(contractEntity.getBuCode())
                    .setIsSales(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                    .setIsProcurement(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
            Result result = customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
            if (result.isSuccess()) {
                List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
                if (CollectionUtil.isNotEmpty(creditPaymentVOList)) {
                    CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                    // 客户主数据赊销改为预付,系统提醒"“客户主数据配置，付款方式已更新为：预付”。不允许提交。
                    if (customerCreditPaymentVO.getPaymentType().equals(PaymentTypeEnum.IMPREST.getType())) {
                        throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_TYPE_HAS_CHANGE);
                    }

                    // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                    if (contractModifyDTO.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                        String message = "最多可赊销{" + customerCreditPaymentVO.getCreditDays() + "}天";
                        throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_CREDIT_HAS_CHANGE, message);
                    }
                }
            }
        }

        // 校验合同类型
        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        switch (typeEnum) {
            case YI_KOU_JIA:
                // 一口价不支持修改类型
                if (!contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_MODIFY);
                }

                // 数量校验
                if (BigDecimalUtil.isGreaterThanZero(contractEntity.getTotalBillNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_BILL_NUM_EXCEPTION);
                }

                break;
            case JI_CHA:
                // 全部定价不支持修改基差暂定价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                    if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_CONFIRMED_ALL);
                    }
                }
                // 未全部定价不能修改一口价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_CONFIRMED_ALL);
                    }
                }
                break;
            case JI_CHA_ZAN_DING_JIA:
                // 数量校验
                if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                    if (BigDecimalUtil.isGreaterThanZero(contractEntity.getTotalBillNum()) || BigDecimalUtil.isGreaterThanZero(contractEntity.getAllocateNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_BILL_NUM_EXCEPTION);
                    }
                }

                // 未全部定价不能修改一口价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                    /*if (!contractEntity.getContractNum().equals(contractEntity.getTotalPriceNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_CONFIRMED_ALL);
                    }*/

                        // 修改一口价校验
                        BigDecimal minNum = BigDecimalUtil.min(contractEntity.getTotalPriceNum(),
                                contractEntity.getContractNum().subtract(contractEntity.getTotalBillNum()),
                                contractEntity.getContractNum().subtract(contractEntity.getAllocateNum()));
                        if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), minNum)) {
                            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
                        }
                        break;
                    }
                }
                break;
            default:
                break;
        }

        if (contractModifyDTO.getReviseType() == 2) {
            // 一个合同只能定价完成一次
            if (null != redisUtil.get(RedisConstants.CONTRACT_CONFIRM_COMPLETE_CODE + contractEntity.getContractCode())) {
                // 防止异常导致无法定价完成
                if (BigDecimalUtil.isEqual(contractEntity.getTotalPriceNum(), contractEntity.getContractNum())) {
                    throw new BusinessException(ResultCodeEnum.REPEATED_SUBMIT);
                }
            } else {
                redisUtil.set(RedisConstants.CONTRACT_CONFIRM_COMPLETE_CODE + contractEntity.getContractCode(), contractEntity.getContractCode());
            }
        }

        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum()
                && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())
                && contractModifyDTO.getReviseType() == 1) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

    }

    /**
     * 处理子合同信息
     *
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     * @return
     */
    protected abstract ContractEntity operateSonContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 处理TT信息
     *
     * @param sonContractEntity 子合同
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     */
    protected abstract List<TTQueryVO> operateTradeTicket(ContractEntity sonContractEntity, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 处理父合同信息
     *
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     */
    protected abstract void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);
}
