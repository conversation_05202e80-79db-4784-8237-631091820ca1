package com.navigator.trade.service.tradeticket.impl.convert;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTranferTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.TemplateConditionDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import com.navigator.trade.pojo.enums.DeliveryTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class ContractSignConvertUtil {
    /***********************DAO***********************/
    @Autowired
    protected TradeTicketDao tradeTicketDao;
    @Autowired
    protected TtAddDao ttAddDao;
    @Autowired
    protected TtPriceDao ttPriceDao;
    @Autowired
    protected TtModifyDao ttModifyDao;
    @Autowired
    protected TtTranferDao ttTranferDao;
    @Autowired
    protected IContractQueryService contractService;


    /***********************Facade***********************/
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected CategoryFacade categoryFacade;
    @Autowired
    protected AttributeFacade attributeFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    /***********************Service & Handler***********************/

    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;

    /***********************ContractSignCreateDTO***********************/
    /**
     * 创建协议记录
     *
     * @param ttId
     * @param ttDto
     * @param ttTypeEnum
     * @return
     */
    public ContractSignCreateDTO getContractSignCreateDTO(Integer ttId, TTDTO ttDto, TTTypeEnum ttTypeEnum) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        SalesContractAddTTDTO salesContractAddTTDTO = ttDto.getSalesContractAddTTDTO();
        BeanUtils.copyProperties(salesContractAddTTDTO, contractSignCreateDTO);
        BeanUtils.copyProperties(ttAddEntity, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtId(ttId)
                .setTtCode(salesContractAddTTDTO.getCode())
                .setSignatureStatus(salesContractAddTTDTO.getContractSignatureStatus())
                .setGoodsCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                .setOwnerId(Integer.parseInt(salesContractAddTTDTO.getOwnerId()))
                .setTtType(ttTypeEnum.getType())
                .setSalesType(salesContractAddTTDTO.getSalesType())
                .setSupplierId(String.valueOf(salesContractAddTTDTO.getSupplierId()))
                .setBelongCustomerId(salesContractAddTTDTO.getBelongCustomerId())
                .setTradeType(salesContractAddTTDTO.getBelongCustomerId())
                .setSignType(salesContractAddTTDTO.getSignType())
                .setSignDate(salesContractAddTTDTO.getSignDate())
                .setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime())
                .setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime())
                .setContractNum(new BigDecimal(salesContractAddTTDTO.getContractNum()))
        //.setContractId(salesContractAddTTDTO.getContractId())
        ;
        /*GoodsInfoVO goodsInfoVO = getGoodsInfoVO(ttAddEntity.getGoodsCategoryId(), ttAddEntity.getGoodsPackageId(), ttAddEntity.getGoodsSpecId(), ttAddEntity.getSupplierId());
        if (goodsInfoVO != null) {
            contractSignCreateDTO.setGoodsId(goodsInfoVO.getGoodsId());
        }*/
        return contractSignCreateDTO;
    }

    /**
     * 定价时，创建协议记录之前的处理
     *
     * @param ttId
     * @param ttDto
     * @param ttTypeEnum
     * @return
     */
    public ContractSignCreateDTO getPriceContractSignCreateDTO(Integer ttId, TTDTO ttDto, TTTypeEnum ttTypeEnum) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttDto.getSalesContractTTPriceDTO();
        BeanUtils.copyProperties(salesContractTTPriceDTO, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtCode(salesContractTTPriceDTO.getCode())
                .setTtId(ttId)
                .setSupplierId(String.valueOf(salesContractTTPriceDTO.getSupplierId()))
                .setTtType(ttTypeEnum.getType())
                .setBelongCustomerId(salesContractTTPriceDTO.getBelongCustomerId())
                .setTradeType(salesContractTTPriceDTO.getBelongCustomerId())
        //.setContractId(salesContractTTPriceDTO.getContractId())
        ;

        // 补充信息
        ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTPriceDTO.getContractId());
        contractSignCreateDTO.setSupplierId(String.valueOf(contractEntity.getSupplierId()))
                .setSupplierName(contractEntity.getSupplierName())
                .setGoodsId(contractEntity.getGoodsId());

        return contractSignCreateDTO;
    }


    /**
     * 回购时，创建协议记录之前的处理
     *
     * @param ttId
     * @param ttDto
     * @param ttTypeEnum
     * @return
     */
    public ContractSignCreateDTO getReviseContractSignCreateDTO(Integer ttId, TTDTO ttDto, TTTypeEnum ttTypeEnum) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        BeanUtils.copyProperties(salesContractReviseTTDTO, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtId(ttId)
                .setTtCode(salesContractReviseTTDTO.getCode())
                .setSignatureStatus(salesContractReviseTTDTO.getContractSignatureStatus())
                .setGoodsCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                .setGoodsId(salesContractReviseTTDTO.getGoodsId())
                .setOwnerId(Integer.parseInt(salesContractReviseTTDTO.getOwnerId()))
                .setTtType(ttTypeEnum.getType())
                .setSupplierId(String.valueOf(salesContractReviseTTDTO.getSupplierId()))
                .setBelongCustomerId(salesContractReviseTTDTO.getBelongCustomerId())
                .setTradeType(salesContractReviseTTDTO.getBelongCustomerId())
                .setSignDate(salesContractReviseTTDTO.getSignDate())
                .setDeliveryStartTime(salesContractReviseTTDTO.getDeliveryStartTime())
                .setDeliveryEndTime(salesContractReviseTTDTO.getDeliveryEndTime())
                .setContractNum(salesContractReviseTTDTO.getContractNum())
        //.setContractId(salesContractReviseTTDTO.getContractId())

        ;
        return contractSignCreateDTO;
    }

    /**
     * 拆分时，创建协议记录之前的处理
     *
     * @param ttId
     * @param ttDto
     * @param ttTypeEnum
     * @return
     */
    public ContractSignCreateDTO getSplitContractSignCreateDTO(Integer ttId, TTDTO ttDto, TTTypeEnum ttTypeEnum) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttDto.getSalesContractSplitTTDTO();
        if (salesContractSplitTTDTO.getAddedSignatureType() == 1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            BeanUtils.copyProperties(salesContractSplitTTDTO, contractSignCreateDTO);
            BeanUtils.copyProperties(contractEntity, contractSignCreateDTO);
            contractSignCreateDTO
                    .setTtId(ttId)
                    .setContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setTtCode(salesContractSplitTTDTO.getCode())
                    .setSignatureStatus(salesContractSplitTTDTO.getContractSignatureStatus())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setGoodsId(contractEntity.getGoodsId())
                    .setOwnerId(contractEntity.getOwnerId())
                    .setTtType(ttTypeEnum.getType())
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setBelongCustomerId(contractEntity.getBelongCustomerId())
                    .setCustomerId(contractEntity.getCustomerId())
                    .setSupplierId(String.valueOf(contractEntity.getSupplierId()))
                    .setSignType(salesContractSplitTTDTO.getSignType())
                    .setSignDate(salesContractSplitTTDTO.getSignDate())
                    .setDeliveryStartTime(salesContractSplitTTDTO.getDeliveryStartTime())
                    .setDeliveryEndTime(salesContractSplitTTDTO.getDeliveryEndTime())
                    .setContractNum(salesContractSplitTTDTO.getContractNum())
            //.setContractId(salesContractSplitTTDTO.getContractId())
            ;
        } else {
            BeanUtils.copyProperties(salesContractSplitTTDTO, contractSignCreateDTO);
            contractSignCreateDTO
                    .setTtId(ttId)
                    .setContractId(salesContractSplitTTDTO.getSonContractId())
                    .setTtCode(salesContractSplitTTDTO.getCode())
                    .setSignatureStatus(salesContractSplitTTDTO.getContractSignatureStatus())
                    .setGoodsCategoryId(salesContractSplitTTDTO.getGoodsCategoryId())
                    .setGoodsId(salesContractSplitTTDTO.getGoodsId())
                    .setOwnerId(salesContractSplitTTDTO.getOwnerId())
                    .setTtType(ttTypeEnum.getType())
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setCustomerId(salesContractSplitTTDTO.getCustomerId())
                    .setSupplierId(String.valueOf(salesContractSplitTTDTO.getSupplierId()))
                    .setBelongCustomerId(salesContractSplitTTDTO.getBelongCustomerId())
                    .setSignType(salesContractSplitTTDTO.getSignType())
                    .setSignDate(salesContractSplitTTDTO.getSignDate())
                    .setDeliveryStartTime(salesContractSplitTTDTO.getDeliveryStartTime())
                    .setDeliveryEndTime(salesContractSplitTTDTO.getDeliveryEndTime())
                    .setContractNum(salesContractSplitTTDTO.getContractNum())
            //.setContractId(salesContractSplitTTDTO.getContractId())
            ;
            if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == salesContractSplitTTDTO.getTradeType()) {
                contractSignCreateDTO.setTradeType(ContractTradeTypeEnum.NEW.getValue());
            }
        }

        return contractSignCreateDTO;
    }

    /**
     * 转月时，创建协议记录之前的处理
     *
     * @param ttId
     * @param ttDto
     * @return
     */
    public ContractSignCreateDTO getTransferContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttDto.getSalesContractTTTransferDTO();
        log.info("==============================================salesContractTTTransferDTO():{}", JSON.toJSONString(salesContractTTTransferDTO));
        //todo 11,07 salesContractTTTransferDTO中的合同id字段丢失
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttId);
        ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getContractId());
        log.info("==============================================salesContractTTTransferDTO.getContractId()():{}", salesContractTTTransferDTO.getContractId());
        BeanUtils.copyProperties(salesContractTTTransferDTO, contractSignCreateDTO);
        contractSignCreateDTO.setTtId(ttId)
                .setContractId(salesContractTTTransferDTO.getSonContractId() != null ? salesContractTTTransferDTO.getSonContractId() : salesContractTTTransferDTO.getContractId())
                .setTtCode(salesContractTTTransferDTO.getCode())
                .setTtType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()) ? TTTypeEnum.TRANSFER.getType() : TTTypeEnum.REVERSE_PRICE.getType())
                .setSignatureStatus(salesContractTTTransferDTO.getContractSignatureStatus())
                .setGoodsCategoryId(salesContractTTTransferDTO.getGoodsCategoryId())
                .setOwnerId(salesContractTTTransferDTO.getOwnerId())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                .setSupplierId(String.valueOf(salesContractTTTransferDTO.getSupplierId()))
                .setBelongCustomerId(salesContractTTTransferDTO.getBelongCustomerId())
                .setTradeType(salesContractTTTransferDTO.getTradeType())
        //.setContractId(salesContractTTTransferDTO.getContractId())

        ;
        contractSignCreateDTO.setGoodsId(salesContractTTTransferDTO.getGoodsId());
        return contractSignCreateDTO;
    }

    /**
     * 新增结构化定价时，创建协议记录之前的处理
     *
     * @param ttId
     * @param ttDto
     * @param ttTypeEnum
     * @return
     */
    public ContractSignCreateDTO getStructureContractSignCreateDTO(Integer ttId, TTDTO ttDto, TTTypeEnum ttTypeEnum) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttDto.getSalesStructurePriceTTDTO();
        BeanUtils.copyProperties(salesStructurePriceTTDTO, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtCode(salesStructurePriceTTDTO.getCode())
                .setTtId(ttId)
                .setTtType(ttTypeEnum.getType())
                .setSupplierId(salesStructurePriceTTDTO.getSupplierId().toString())
                .setContractType(ContractTypeEnum.STRUCTURE.getValue())
                .setSignatureStatus(salesStructurePriceTTDTO.getContractSignatureStatus())
                .setOwnerId(Integer.parseInt(salesStructurePriceTTDTO.getOwnerId()))
                .setTradeType(salesStructurePriceTTDTO.getTradeType())
                .setBelongCustomerId(salesStructurePriceTTDTO.getBelongCustomerId())
                .setSignDate(salesStructurePriceTTDTO.getSignDate())
        //.setContractId(salesStructurePriceTTDTO.getContractId())
        ;
        return contractSignCreateDTO;
    }

    /***********************SignTemplateDTO***********************/
    public SignTemplateDTO getPriceSignTemplateDTO(Integer ttId) {
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();
        TemplateConditionDTO templateConditionDTO = new TemplateConditionDTO();

        TradeTicketEntity tt = tradeTicketDao.getTradeTicketEntityById(ttId);
        TTPriceEntity tp = ttPriceDao.getTTPriceEntityByTTId(ttId);
        ContractEntity cp = contractService.getBasicContractById(tt.getContractId());
        ContractPriceEntity cPrice = contractPriceService.getContractPriceEntityContractId(tt.getContractId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(cp.getCustomerId());
        FactoryWarehouseEntity warehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(cp.getShipWarehouseId());
        DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(cp.getDeliveryType());

        templateConditionDTO
                .setContractType(tt.getContractType())
                .setDeliveryType(deliveryTypeEntity.getType())
                .setPaymentType(cp.getPaymentType())
                .setDeliveryFactoryCode(cp.getDeliveryFactoryCode())
                .setPriceEndType(cp.getPriceEndType())
                .setDepositAmount(cp.getDepositRate())
                .setAddedDepositRate(null == cp.getAddedDepositRate() ? 0 : cp.getAddedDepositRate())
                .setNotPriceNum(cp.getOrderNum().subtract(tp.getNum()))
                .setActionType(tt.getContractSource())
        ;

        //目的港
        if (StringUtils.isNumeric(cp.getDestination())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(cp.getDestination()));
            signTemplateDTO.setPy(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : cp.getDestination());
        } else {
            signTemplateDTO.setPy(cp.getDestination());
        }
        //袋皮扣重
        if (StringUtils.isNumeric(cp.getPackageWeight())) {
            SystemRuleItemEntity packageWeightItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(cp.getWeightCheck()));
            signTemplateDTO.setAg(packageWeightItemEntity != null ? packageWeightItemEntity.getMemo() : cp.getWeightCheck());
        } else {
            signTemplateDTO.setAg(cp.getWeightCheck());
        }

        signTemplateDTO
                .setAd(null == customerDTO ? "暂无卖方主体地址" : customerDTO.getAddress())
                .setNo(tt.getContractCode())
                .setTy(ContractTypeEnum.getByValue(tt.getContractType()).getDesc())
                .setXyb(tt.getProtocolCode())
                .setDoc(DateTimeUtil.formatDateStringCN(cp.getSignDate()))
                .setYdoc(DateTimeUtil.formatDateStringCN(cp.getSignDate()))
                .setDg(DeliveryModeEnum.getByValue(deliveryTypeEntity.getType()).getDesc())
                .setPm(PaymentTypeEnum.getByType(cp.getPaymentType()).getDesc())
                //卖方主体所在地址简称
                // .setAd(ttAdd.g)
                .setNa(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getCustomerName() : tt.getSupplierName())
                .setMe(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getSupplierName() : tt.getCustomerName())
                .setVr(categoryFacade.getBasicCategoryBySerialNo(cp.getGoodsCategoryId()).getName())
                .setMt(cp.getContractNum().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setOs(cp.getWeightTolerance() + "%")
                .setEg(attributeFacade.getAttributeValueById(cp.getGoodsSpecId()).getName())
                .setAg(cp.getPackageWeight())
                //.setPrx(cp.getLiftingPrice())
                .setYf(cPrice.getTransportPrice().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setDs(null != warehouseEntity ? warehouseEntity.getAddress() : "交货工厂地址暂无")
                //todo：交货地点/目的港？
                .setDd(null != warehouseEntity ? warehouseEntity.getDeliveryPoint() : "交货工厂地址暂无")
                .setPo(DateTimeUtil.formatDateStringCN(cp.getDeliveryStartTime(), cp.getDeliveryEndTime()))
                .setPe(systemRuleFacade.getRuleItemById(Integer.valueOf(cp.getWeightCheck())).getRuleKey())
                .setZh(cp.getSupplierAccount())
                // todo:合同签订日+2个自然日
                .setJzfk(DateTimeUtil.formatDateStringCN(DateTimeUtil.addDays(cp.getSignDate(), 1, false)))
                .setMr(cp.getDepositRate() + (0 == cp.getDepositRate() ? "" : "%"))
                .setDmr(cp.getAddedDepositRate() + (0 == cp.getAddedDepositRate() ? "" : "%"))
                .setFox(cp.getCustomerName())
                .setJcj(cp.getExtraPrice().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setDjj(cp.getPriceEndTime())
                .setKjr(DateTimeUtil.formatDateStringCN(cp.getSignDate()))
                .setKjh(cp.getContractCode())
                .setTtxr(DateTimeUtil.formatDateStringCN(tt.getCreatedAt()))
                .setJhgc(cp.getDeliveryFactoryName())
                .setTemplateCondition(templateConditionDTO)
                .setJqpj(tp.getTransactionPrice().toPlainString())
                .setWdjl(cp.getContractNum().subtract(cp.getTotalPriceNum()).toPlainString())
                .setWkdl(cp.getContractNum().subtract(cp.getTotalDeliveryNum()).toPlainString())
                .setHtdj(tp.getNum().toPlainString())
                .setMes(null != customerDTO ? customerDTO.getCreditDays() : 0)
                .setKh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankName())
                .setZh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankAccountNo())
                .setDjs(DateTimeUtil.formatDateStringCN(tp.getCreatedAt()))
                .setHtdj(tp.getNum().toPlainString())
                .setHy(DateTimeUtil.parseDayByDomainCode(cp.getDomainCode()))
                .setHyj(cp.getDomainCode())
                .setDjjg(tp.getPrice().toPlainString())
        ;
        return signTemplateDTO;
    }


    public void initOriginalParameter(List<CompareObjectDTO> compareObjectDTOList, List<String> modifyList, SignTemplateDTO signTemplateDTO, TTModifyEntity tm) {
        //签约日期
        if (modifyList.contains("signDate")) {
            compareObjectDTOList.forEach(i -> {
                if ("signDate".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setDoc(DateTimeUtil.formatString(i.getAfter()));
                    signTemplateDTO.setYdoc(DateTimeUtil.formatString(i.getAfter()));
                }
            });

        } else {
            signTemplateDTO.setDoc(DateTimeUtil.formatDateStringCN(tm.getSignDate()));
            signTemplateDTO.setYdoc(DateTimeUtil.formatDateStringCN(tm.getSignDate()));
        }

        //包装计算重量

        //含税单价
        if (modifyList.contains("unitPrice")) {
            compareObjectDTOList.forEach(i -> {
                if ("unitPrice".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setPr(new BigDecimal(i.getAfter()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                    signTemplateDTO.setYpr(new BigDecimal(i.getBefore()).setScale(2, RoundingMode.HALF_UP).toPlainString());
                }
            });
        } else {
            signTemplateDTO.setPr(tm.getUnitPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
            signTemplateDTO.setYpr(tm.getUnitPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
        }
        //含税单价中的运费
        if (modifyList.contains("transportPrice")) {
            compareObjectDTOList.forEach(i -> {
                if ("transportPrice".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setYf(new BigDecimal(i.getAfter()).toPlainString());
                    signTemplateDTO.setYyf(new BigDecimal(i.getBefore()).toPlainString());
                }
            });
        } else {
            signTemplateDTO.setYf(tm.getTransportPrice().toPlainString());
            signTemplateDTO.setYyf(tm.getTransportPrice().toPlainString());
        }
        //期货合约
        if (modifyList.contains("domainCode")) {
            compareObjectDTOList.forEach(i -> {
                if ("domainCode".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setHy(i.getAfter());
                    signTemplateDTO.setYhy(i.getBefore());
                }
            });
        } else {
            signTemplateDTO.setHy(tm.getDomainCode());
            signTemplateDTO.setYhy(tm.getDomainCode());
        }
        //合同数量
        if (modifyList.contains("contractNum")) {
            compareObjectDTOList.forEach(i -> {
                if ("contractNum".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setCfsl(new BigDecimal(i.getAfter()).toPlainString());
                }
            });
        }
        //交货工厂
        if (modifyList.contains("deliveryFactoryName")) {
            compareObjectDTOList.forEach(i -> {
                if ("deliveryFactoryName".equalsIgnoreCase(i.getName())) {
                    signTemplateDTO.setJhgc(i.getAfter());
                    signTemplateDTO.setYjhgc(i.getBefore());
                }
            });
        } else {
            signTemplateDTO.setJhgc(tm.getDeliveryFactoryName());
            signTemplateDTO.setYjhgc(tm.getDeliveryFactoryName());
        }
        //发货库点
        if (tm.getShipWarehouseId() != null) {
            signTemplateDTO.setDd(String.valueOf(tm.getShipWarehouseId()));
            FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(tm.getShipWarehouseId());
            if (null != factoryWarehouseEntity) {
                signTemplateDTO.setDd(factoryWarehouseEntity.getName());
            }
        }
    }


    public BigDecimal avePrice(Integer contractId) {

        List<TTPriceEntity> ttPriceEntities = ttPriceDao.getConfirmPriceList(contractId);
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalNum = BigDecimal.ZERO;
        for (TTPriceEntity ttPriceEntity : ttPriceEntities) {
            totalPrice = totalPrice.add(ttPriceEntity.getPrice().setScale(2, RoundingMode.HALF_UP).multiply(ttPriceEntity.getNum()));
            totalNum = totalNum.add(ttPriceEntity.getNum());
        }
        // 加权平均价

        BigDecimal ldjjg = BigDecimalUtil.isGreaterThanZero(totalNum) ? BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum) : BigDecimal.ZERO;

        return ldjjg;
    }


    public void saveTTPrice(TTDTO ttDto, Integer ttId) {
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttDto.getSalesContractTTPriceDTO();
        // 保存 ttPrice
        TTPriceEntity ttPriceEntity = BeanConvertUtils.convert(TTPriceEntity.class, salesContractTTPriceDTO);
//        ttPriceEntity.setGoodsId(salesContractTTPriceDTO.get)
        //查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractTTPriceDTO.getSupplierId()) {
            supplier = customerFacade.getCustomerById(salesContractTTPriceDTO.getSupplierId());
        }
        if (supplier != null) {
            ttPriceEntity.setSupplierId(supplier.getId());
            ttPriceEntity.setSupplierName(supplier.getName());

        }
        //获取商品信息
        ttPriceEntity.setGoodsId(salesContractTTPriceDTO.getGoodsId());

        ttPriceEntity.setTtId(ttId);
        ttPriceEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttPriceEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttPriceDao.save(ttPriceEntity);
        //在那定价定价，生成TT价格
        ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTPriceDTO.getContractId());
        ContractPriceEntity oldContractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractTTPriceDTO.getContractId());
        //set定价时合同价格明细
//        if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
//            ttPriceEntity.setContractPriceDetail(JSON.toJSONString(newContractPrice));
//        } else {
//            ttPriceEntity.setContractPriceDetail(salesContractTTPriceDTO.getContractPriceDetail());
//        }
        //加权品均价
        BigDecimal avePrice = this.avePrice(contractEntity.getId());
        ttPriceEntity.setAvePrice(avePrice);
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory3());
        if (categoryEntity != null && Arrays.asList("脂肪酸", "豆油脂肪酸", "特油脂肪酸").contains(categoryEntity.getName())) {
            //最终合同价格
            BigDecimal endContractPrice = salesContractTTPriceDTO.getUnitPrice();
            ttPriceEntity.setUnitPrice(salesContractTTPriceDTO.getUnitPrice().subtract(salesContractTTPriceDTO.getPrice()))
                    .setEndContractPrice(endContractPrice)
                    .setEndAllPrice(endContractPrice.multiply(contractEntity.getContractNum()));
        } else {
            if (BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO)) {
                //定价完成
                BigDecimal sourcePrice = contractEntity.getUnitPrice().subtract(oldContractPriceEntity.getForwardPrice());
                //最终合同价格
                BigDecimal endContractPrice = avePrice.add(sourcePrice);
                ttPriceEntity.setEndContractPrice(endContractPrice);
                //最终全额货款
                ttPriceEntity.setEndAllPrice(endContractPrice.multiply(contractEntity.getContractNum()));
            }
        }
        PriceDetailBO priceDetailBO = ttDto.getPriceDetailBO();
        ContractPriceEntity newContractPrice = BeanConvertUtils.convert(ContractPriceEntity.class, priceDetailBO);
        TradeTicketEntity ticketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        newContractPrice.setId(null)
                .setTtId(ttId)
                .setTtCode(null != ticketEntity ? ticketEntity.getCode() : "")
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode());
        contractPriceService.saveOrUpdate(newContractPrice);
        ttPriceEntity.setContractPriceDetail(JSON.toJSONString(newContractPrice));
        ttPriceDao.updateById(ttPriceEntity);
    }

    public SignTemplateDTO getAddSignTemplateDTO(Integer ttId, TTAddEntity ttAdd, Integer customerId) {
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();
        TemplateConditionDTO templateConditionDTO = new TemplateConditionDTO();
        log.info("=========tradeTicketId:" + ttId.toString());
        TradeTicketEntity tt = tradeTicketDao.getTradeTicketEntityById(ttId);
        ContractPriceEntity cp = contractPriceService.getContractPriceEntityByTTId(ttId);
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttAdd.getDeliveryType());
        FactoryWarehouseEntity warehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(ttAdd.getShipWarehouseId());
        templateConditionDTO
                .setContractType(tt.getContractType())
                .setDeliveryType(DeliveryTypeEnum.getByValue(ttAdd.getDeliveryType()).getMode())
                .setPaymentType(ttAdd.getPaymentType())
                .setDeliveryFactoryCode(ttAdd.getDeliveryFactoryCode())
                .setPriceEndType(ttAdd.getPriceEndType())
                .setDepositAmount(ttAdd.getDepositRate())
                .setAddedDepositRate(null == ttAdd.getAddedDepositRate() ? 0 : ttAdd.getAddedDepositRate())
                .setActionType(tt.getContractSource())
        ;
        //目的港
        if (StringUtils.isNumeric(ttAdd.getDestination())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getDestination()));
            signTemplateDTO.setPy(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : ttAdd.getDestination());
        } else {
            signTemplateDTO.setPy(ttAdd.getDestination());
        }
        //袋皮扣重
        if (StringUtils.isNumeric(ttAdd.getPackageWeight())) {
            SystemRuleItemEntity packageWeightItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getWeightCheck()));
            signTemplateDTO.setAg(packageWeightItemEntity != null ? packageWeightItemEntity.getMemo() : ttAdd.getWeightCheck());
        } else {
            signTemplateDTO.setAg(ttAdd.getWeightCheck());
        }

        signTemplateDTO
                .setYag(signTemplateDTO.getAg())
                .setAd(null == customerDTO ? "暂无卖方主体地址" : customerDTO.getAddress())
                .setNo(tt.getContractCode())
                .setTy(ContractTypeEnum.getByValue(tt.getContractType()).getDesc())
                .setXyb(tt.getProtocolCode())
                .setDg(DeliveryModeEnum.getByValue(deliveryTypeEntity.getType()).getDesc())
                .setDoc(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setYdoc(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setPm(PaymentTypeEnum.getByType(ttAdd.getPaymentType()).getDesc())
                //卖方主体所在地址简称
                // .setAd(ttAdd.g)
                .setNa(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getCustomerName() : tt.getSupplierName())
                .setMe(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getSupplierName() : tt.getCustomerName())
                .setVr(categoryFacade.getCategoryDTOById(ttAdd.getGoodsCategoryId()).getName())
                .setMt(ttAdd.getContractNum().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setOs(ttAdd.getWeightTolerance() + "%")
                .setEg(attributeFacade.getAttributeValueById(ttAdd.getGoodsSpecId()).getName())
                // 袋皮扣重协议文本
                .setAg(ttAdd.getPackageWeight())
                //.setPrx(cp.getLiftingPrice())
                .setYf(cp.getTransportPrice().toPlainString())
                .setDs(null != warehouseEntity ? warehouseEntity.getAddress() : "交货工厂地址暂无")
                //todo：交货地点/目的港？
                .setDd(null != warehouseEntity ? warehouseEntity.getDeliveryPoint() : "交货工厂地址暂无")
                .setPo(DateTimeUtil.formatDateStringCN(ttAdd.getDeliveryStartTime(), ttAdd.getDeliveryEndTime()))
                .setPe(systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getWeightCheck())).getRuleKey())
                .setZh(ttAdd.getSupplierAccount())
                // todo:合同签订日+2个自然日
                .setJzfk(DateTimeUtil.formatDateStringCN(DateTimeUtil.addDays(ttAdd.getSignDate(), 2, false)))
                .setMr(ttAdd.getDepositRate() + (0 == ttAdd.getDepositRate() ? "" : "%"))
                .setDmr(String.valueOf(ttAdd.getAddedDepositRate()))
//        .setAds(ttAdd.getCus)
                .setFox(ttAdd.getCustomerName())
                .setJcj(cp.getExtraPrice().toPlainString())
                .setDjj(ttAdd.getPriceEndTime())
                .setKjr(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setKjh(ttAdd.getContractCode())
                .setTtxr(DateTimeUtil.formatDateStringCN(ttAdd.getUpdatedAt()))
                .setJhgc(ttAdd.getDeliveryFactoryName())
                .setMes(null == customerDTO ? 0 : customerDTO.getCreditDays())
                .setKh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankName())
                .setZh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankAccountNo())
                .setHy(DateTimeUtil.parseDayByDomainCode(ttAdd.getDomainCode()))
                .setHyj(ttAdd.getDomainCode())
                .setTemplateCondition(templateConditionDTO)
        ;
        return signTemplateDTO;
    }


    public SignTemplateDTO getWashoutSignTemplateDTO(Integer ttId, TTAddEntity ttAdd, Integer customerId) {
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();
        TemplateConditionDTO templateConditionDTO = new TemplateConditionDTO();
        log.info("=========tradeTicketId:" + ttId.toString());
        TradeTicketEntity tt = tradeTicketDao.getTradeTicketEntityById(ttId);
        ContractPriceEntity cp = contractPriceService.getContractPriceEntityByTTId(ttId);
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttAdd.getDeliveryType());
        FactoryWarehouseEntity warehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(ttAdd.getShipWarehouseId());

        BigDecimal washOutDiffUnitPrice = BigDecimal.ZERO;
        BigDecimal unitPrice = BigDecimal.ZERO;
        BigDecimal totalPriceDiff = BigDecimal.ZERO;
        log.info("RootContractId:{}", ttAdd.getRootContractId());
        if (null != ttAdd.getRootContractId()) {
            ContractEntity contractEntity = contractService.getBasicContractById(ttAdd.getRootContractId());
            unitPrice = contractEntity.getUnitPrice();
            washOutDiffUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, ttAdd.getUnitPrice());
            totalPriceDiff = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, washOutDiffUnitPrice, ttAdd.getContractNum());
        }

        templateConditionDTO
                .setContractType(tt.getContractType())
                .setDeliveryType(DeliveryTypeEnum.getByValue(ttAdd.getDeliveryType()).getMode())
                .setPaymentType(ttAdd.getPaymentType())
                .setDeliveryFactoryCode(ttAdd.getDeliveryFactoryCode())
                .setPriceEndType(ttAdd.getPriceEndType())
                .setDepositAmount(ttAdd.getDepositRate())
                .setAddedDepositRate(null == ttAdd.getAddedDepositRate() ? 0 : ttAdd.getAddedDepositRate())
                .setActionType(tt.getContractSource())
                .setWashOutDiffUnitPrice(washOutDiffUnitPrice)
        ;
        //目的港
        if (StringUtils.isNumeric(ttAdd.getDestination())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getDestination()));
            signTemplateDTO.setPy(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : ttAdd.getDestination());
        } else {
            signTemplateDTO.setPy(ttAdd.getDestination());
        }
        //袋皮扣重
        if (StringUtils.isNumeric(ttAdd.getPackageWeight())) {
            SystemRuleItemEntity packageWeightItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getWeightCheck()));
            signTemplateDTO.setAg(packageWeightItemEntity != null ? packageWeightItemEntity.getMemo() : ttAdd.getWeightCheck());
        } else {
            signTemplateDTO.setAg(ttAdd.getWeightCheck());
        }

        signTemplateDTO
                .setYag(signTemplateDTO.getAg())
                .setAd(null == customerDTO ? "暂无卖方主体地址" : customerDTO.getAddress())
                .setNo(tt.getContractCode())
                .setTy(ContractTypeEnum.getByValue(tt.getContractType()).getDesc())
                .setXyb(tt.getProtocolCode())
                .setDg(DeliveryModeEnum.getByValue(deliveryTypeEntity.getType()).getDesc())
                .setDoc(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setYdoc(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setPm(PaymentTypeEnum.getByType(ttAdd.getPaymentType()).getDesc())
                //卖方主体所在地址简称
                // .setAd(ttAdd.g)
                .setNa(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getCustomerName() : tt.getSupplierName())
                .setMe(ContractSalesTypeEnum.SALES.getValue() == tt.getSalesType() ? tt.getSupplierName() : tt.getCustomerName())
                .setVr(categoryFacade.getCategoryDTOById(ttAdd.getGoodsCategoryId()).getName())
                .setMt(ttAdd.getContractNum().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setOs(ttAdd.getWeightTolerance() + "%")
                .setEg(attributeFacade.getAttributeValueById(ttAdd.getGoodsSpecId()).getName())
                // 袋皮扣重协议文本
                .setAg(ttAdd.getPackageWeight())
                //.setPrx(cp.getLiftingPrice())
                .setYf(cp.getTransportPrice().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .setDs(null != warehouseEntity ? warehouseEntity.getAddress() : "交货工厂地址暂无")
                //todo：交货地点/目的港？
                .setDd(null != warehouseEntity ? warehouseEntity.getDeliveryPoint() : "交货工厂地址暂无")
                .setPo(DateTimeUtil.formatDateStringCN(ttAdd.getDeliveryStartTime(), ttAdd.getDeliveryEndTime()))
                .setPe(systemRuleFacade.getRuleItemById(Integer.valueOf(ttAdd.getWeightCheck())).getRuleKey())
                .setZh(ttAdd.getSupplierAccount())
                // todo:合同签订日+2个自然日
                .setJzfk(DateTimeUtil.formatDateStringCN(DateTimeUtil.addDays(ttAdd.getSignDate(), 2, false)))
                .setMr(ttAdd.getDepositRate() + (0 == ttAdd.getDepositRate() ? "" : "%"))
                .setDmr(String.valueOf(ttAdd.getAddedDepositRate()))
//        .setAds(ttAdd.getCus)
                .setFox(ttAdd.getCustomerName())
                .setJcj(cp.getExtraPrice().toPlainString())
                .setDjj(ttAdd.getPriceEndTime())
                .setKjr(DateTimeUtil.formatDateStringCN(ttAdd.getSignDate()))
                .setKjh(ttAdd.getContractCode())
                .setTtxr(DateTimeUtil.formatDateStringCN(ttAdd.getUpdatedAt()))
                .setJhgc(ttAdd.getDeliveryFactoryName())
                .setMes(null == customerDTO ? 0 : customerDTO.getCreditDays())
                .setKh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankName())
                .setZh(customerDTO == null || CollectionUtils.isEmpty(customerDTO.getCustomerBankDTOS()) ? "暂无" : customerDTO.getCustomerBankDTOS().get(0).getBankAccountNo())
                .setHy(DateTimeUtil.parseDayByDomainCode(ttAdd.getDomainCode()))
                .setHyj(ttAdd.getDomainCode())
                .setTemplateCondition(templateConditionDTO)
                //解约定赔
                .setXdsl(ttAdd.getContractNum().toPlainString())
                .setPr(unitPrice.toPlainString())
                .setXdscj(ttAdd.getUnitPrice().toPlainString())
                .setXdcj(totalPriceDiff.toPlainString())
                .setXdze(washOutDiffUnitPrice.setScale(2, RoundingMode.HALF_UP).toPlainString())
        ;
        return signTemplateDTO;
    }
}
