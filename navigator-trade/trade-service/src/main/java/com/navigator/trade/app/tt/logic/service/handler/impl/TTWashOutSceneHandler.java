package com.navigator.trade.app.tt.logic.service.handler.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 解约定赔
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("WASHOUT_HANDLER")
public class TTWashOutSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        ContractEntity contractEntity = ttdto.getContractEntity();
        ContractWashOutDTO contractWashOutDTO = ttdto.getContractWashOutDTO();
        // 处理回购的数据
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
        salesContractAddTTDTO.setContractId(contractEntity.getId());
        // 价格处理
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        // 解约定陪不改变合同价格
        ttdto.setPriceDetailBO(priceDetailBO);
        PriceDetailBO washOutPriceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())
                || (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())
                && totalPriceNum.compareTo(BigDecimal.ZERO) == 0)
        ) {
            washOutPriceDetailBO.setForwardPrice(BigDecimal.ZERO);
        }
        if (contractWashOutDTO.getExtraPrice() != null) {
            washOutPriceDetailBO.setExtraPrice(contractWashOutDTO.getExtraPrice());
        }
        if (contractWashOutDTO.getForwardPrice() != null) {
            washOutPriceDetailBO.setForwardPrice(contractWashOutDTO.getForwardPrice());
        }
        // 解约定赔含税单价
        BigDecimal washoutUnitPrice = contractPriceFacade.calculatePriceBo(washOutPriceDetailBO);
        salesContractAddTTDTO
                .setWashoutUnitPrice(washoutUnitPrice)
                .setWashoutPriceDetailBO(JSON.toJSONString(washOutPriceDetailBO))
                .setUnitPrice(String.valueOf(contractEntity.getUnitPrice()))
                .setContractNum(String.valueOf(contractWashOutDTO.getWashOutNum()))
                .setSourceContractId(contractEntity.getId());
        // 期货合约
        if (StringUtils.isNotBlank(contractWashOutDTO.getDomainCode())) {
            salesContractAddTTDTO.setDomainCode(contractWashOutDTO.getDomainCode());
        }
        //初始化交易、销售类型、合同来源 TT 自身的一些设置
        salesContractAddTTDTO.setTradeType(ContractTradeTypeEnum.WASHOUT.getValue());
        salesContractAddTTDTO.setSalesType(salesContractAddTTDTO.getSalesType());
        salesContractAddTTDTO.setContractSource(ContractActionEnum.WASHOUT.getActionValue());
        salesContractAddTTDTO.setStatus(TTStatusEnum.APPROVING.getType());
        salesContractAddTTDTO.setType(TTTypeEnum.WASHOUT.getType());
        //协议签署状态
        salesContractAddTTDTO.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);
        //生成TT编号
        ContractSalesTypeEnum salesTypeEnum = ContractSalesTypeEnum.getByValue(salesContractAddTTDTO.getSalesType());
        String code = CodeGeneratorUtil.genTTNewCodeBySaleType(salesTypeEnum);
        salesContractAddTTDTO.setCode(code);
        salesContractAddTTDTO.setCreateStatus(true);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        // 待审批的状态
        ttdto.setSubmitType(SubmitTypeEnum.SUBMIT.getValue());
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        //1.初值化解约定赔TT的
        initDTO(ttdto);
        // 2、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.add2TradeTicketDO(ttdto);
        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        // 3、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        //生成主合同TT+Sub
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        // 4、提交审批
        Integer ttId = tradeTicketEntity.getId();
        ResultCodeEnum submitResult = ttApproveHandler.submit(ttId, arrangeContext);
        // 5、返回结果
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        if (ResultCodeEnum.OK.equals(submitResult)) {
            return list;
        } else {
            throw new BusinessException(submitResult);
        }
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();

        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();
        Integer contractId = ttAddEntity.getContractId();
        ContractEntity contractEntity = null;
        ContractPriceEntity originalContractPriceEntity = null;
        if (null != contractId) {
            contractEntity = contractQueryLogicService.getBasicContractById(contractId);
            originalContractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        }
        // 合同查询详情
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        //合同编号
        BigDecimal washoutUnitPrice = ttAddEntity.getWashoutUnitPrice();
        BigDecimal unitPrice = contractEntity.getUnitPrice();
        String domainCode = contractEntity.getDomainCode();
        ttPriceDetailVO.setDomainCode(domainCode);
        BigDecimal washoutCalPrice = BigDecimal.ZERO;
        StringBuilder washoutPrice = new StringBuilder();
        String pre = "";
        String unitPriceString = "";
        //取消数量:  ttPriceDetailVO.num
        //合同价格: ttPriceDetailVO.unitPrice
        //市场价格: ttPriceDetailVO.washoutPrice
        //总金额 totalPrice
        //差价: ttPriceDetailVO.buyBackPrice
        //定价量
        if (washoutUnitPrice != null) {
            BigDecimal totalPriceNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
            BigDecimal sourceContractNum = ttAddEntity.getSourceContractNum() == null ? BigDecimal.ZERO : ttAddEntity.getSourceContractNum();
            if (ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType()
                    || (sourceContractNum.compareTo(totalPriceNum) <= 0)) {
                washoutPrice.append(washoutUnitPrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                washoutCalPrice = washoutUnitPrice;
            } else {
                //BigDecimal tempPrice = washoutUnitPrice.subtract(contractPriceEntity.getForwardPrice()).setScale(2, RoundingMode.HALF_UP);
                //String symbol = GoodsCategoryEnum.getByValue(contractEntity.getGoodsCategoryId()).getLkgFutureSymbol();
                //washoutPrice = symbol + contractEntity.getDomainCode() + tempPrice;
                //washoutCalPrice = tempPrice;
                String symbol = contractEntity.getFutureCode();
                pre = symbol + domainCode;
                washoutPrice.append(pre);
                if (washoutUnitPrice.compareTo(BigDecimal.ZERO) >= 0) {
                    washoutPrice.append("+");
                }
                washoutPrice.append(washoutUnitPrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                washoutCalPrice = washoutUnitPrice;
            }
        }

        ttPriceDetailVO.setWashoutPrice(String.valueOf(washoutPrice));
        if ((unitPrice != null
                && contractEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()
                && originalContractPriceEntity != null)
                || (unitPrice != null
                && contractEntity.getContractType() == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()
                && contractEntity.getTotalPriceNum().compareTo(BigDecimal.ZERO) == 0)) {
            unitPrice = unitPrice.subtract(originalContractPriceEntity.getForwardPrice());
        }
        if (StringUtils.isNotBlank(pre)) {
            if (unitPrice.compareTo(BigDecimal.ZERO) >= 0) {
                unitPriceString = pre + "+" + unitPrice.stripTrailingZeros().toPlainString();
            } else {
                unitPriceString = pre + unitPrice.stripTrailingZeros().toPlainString();
            }
        } else {
            unitPriceString = unitPrice.stripTrailingZeros().toPlainString();
        }
        ttPriceDetailVO.setUnitPriceString(unitPriceString);
        ttPriceDetailVO.setUnitPrice(unitPrice);
        if (null != unitPrice && null != washoutUnitPrice) {
            //解约定赔
            BigDecimal buyBackPrice = unitPrice.subtract(washoutCalPrice).abs().setScale(2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setBuyBackPrice(buyBackPrice);
            BigDecimal totalPrice = buyBackPrice.multiply(ttAddEntity.getContractNum()).setScale(2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setTotalPrice(totalPrice);
        }
        if (StringUtils.isNotBlank(ttAddEntity.getWashoutPriceDetail())) {
            PriceDetailBO priceDetailBO = JSON.parseObject(ttAddEntity.getWashoutPriceDetail(), PriceDetailBO.class);
            ttPriceDetailVO.setForwardPrice(priceDetailBO.getForwardPrice());
            ttPriceDetailVO.setExtraPrice(priceDetailBO.getExtraPrice());
        }
        if (null != ttAddEntity.getSupplierId()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
            ttPriceDetailVO.setSupplierName(null != customerDTO ? customerDTO.getName() : null);
        }
        if (null != ttAddEntity.getCustomerId()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getCustomerId());
            ttPriceDetailVO.setCustomerName(null != customerDTO ? customerDTO.getName() : null);
        }
        CategoryEntity category2 = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());

        ttPriceDetailVO
                //合同编号
                .setContractCode(tradeTicketEntity.getContractCode())
                //合同id
                .setContractId(tradeTicketEntity.getContractId())
                //TT编号
                .setCode(tradeTicketEntity.getCode())
                //品种
//                .setCategoryName(GoodsCategoryEnum.getByValue(ttAddEntity.getGoodsCategoryId()).getDesc())
                .setCategoryName(null == category2 ? "" : category2.getName())
                //合同类型
                .setContractType(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
                //含税单价
//                .setPrice(contractEntity.getUnitPrice())
                .setPrice(washoutUnitPrice)
                //解约定赔数量
                .setNum(ttAddEntity.getContractNum())
                //创建时间
                .setCreateTime(ttAddEntity.getCreatedAt())
                //协议id
                .setSignId(tradeTicketEntity.getSignId())
                //协议编号
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setSignDate(ttAddEntity.getSignDate())
        ;

        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);

        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttAddEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttAddEntity.getSupplierId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        //买家
        if (null != ttAddEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttAddEntity.getCustomerId()));
        }

        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttAddEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttAddEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);


        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        //原合同信息
        ttQueryDetailVO.setRootContractId(ttAddEntity.getContractId());
        String contractCode = contractEntity.getContractCode();
        ttQueryDetailVO.setRootContractCode(contractCode);
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ttDetailVO.setDetailType("3");
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);

        ContractEntity originalContractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));

        String modifyContent = ttAddEntity.getContent();
        List<CompareObjectDTO> list = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        ttDetailVO.setCompareObjectDTOList(list);

        return ttDetailVO;
    }
}
