package com.navigator.trade.service.tradeticket;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.tradeticket.ReportDTO;
import com.navigator.trade.pojo.dto.tradeticket.StatQueryDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.entity.TTTranferEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.ContractModifyLogVO;
import com.navigator.trade.pojo.vo.ReportVO;
import com.navigator.trade.pojo.vo.TTAllStatusNumVO;

import java.util.List;

/**
 * <p>
 * TT申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface ITradeTicketQueryService {
    /**
     * @param ttQueryDTO
     * @description: 获取TT列表
     * @return: com.navigator.common.dto.Result
     */
    Result queryTTList(QueryDTO<TTQueryDTO> ttQueryDTO);

    /**
     * 查询各个状态下的tt数量
     *
     * @return
     */
    TTAllStatusNumVO getTTStat(StatQueryDTO statQueryDTO);

    /**
     * 根据合同id查询TT
     *
     * @param contractId 合同id
     * @return
     */
    TradeTicketEntity getCanModifyByContractId(Integer contractId);

    /**
     * 根据合协议id查询TT
     *
     * @param signId 协议id
     * @return
     */
    TradeTicketEntity getBySignId(String signId);

    /**
     * 根据ttid查询TT
     *
     * @param ttId 协议id
     * @return
     */
    TradeTicketEntity getByTtId(Integer ttId);

    TradeTicketDTO getTTDetailInfo(Integer ttId);

    TradeTicketDTO getTTDetailInfo(String ttCode);

    List<ContractModifyLogVO> queryModifyLog(String contractCode);

    List<String> getDestination();

    /**
     * 根据合同编号查询拆分TT
     *
     * @param contractCode
     * @return
     */
    List<TradeTicketEntity> getSplitList(String contractCode);

    /**
     * 跟据合同编号查询修改TT
     *
     * @param contractCode
     * @return
     */
    List<TradeTicketEntity> getReviseList(String contractCode);

    /**
     * 根据合同id查询tt记录
     *
     * @param contractId
     * @return
     */
    List<TradeTicketEntity> getByContractId(Integer contractId);

    /**
     * 根据relationId获取关联的另一条数据
     *
     * @param
     * @return
     */
    TTModifyEntity getModifyByRelationId(String relationId, Integer id);

    /**
     * 根据groupId获取关联的另一条数据
     *
     * @param
     * @return
     */
    TradeTicketEntity getByGroupId(String groupId, Integer id);

    /**
     * 根据ttId获取关联的另一条数据
     *
     * @param
     * @return
     */
    TTTranferEntity getTransferById(Integer ttId);

    List<TradeTicketEntity> queryChangeLogByContractId(Integer contractId, String contractCode);

    List<ReportVO> queryTTReport(ReportDTO reportDTO);

    Result generateTTByContractId(List<Integer> idList);

    List<TradeTicketEntity> queryListByContractCode(String contractCode);

    List<TradeTicketEntity> queryIncompleteTTByContractId(Integer contractId, String groupId);

    void updateTTStatusById(Integer ttId, TTStatusEnum ttStatusEnum);

    boolean isOccupy(Integer ttId);

    void setOccupyStatus(Integer ttId, boolean isOccupy);

    TTAddEntity getLkgTTAddByTTId(Integer ttId);

    TTModifyEntity getLkgTTModifyByTTId(Integer ttId);

}
