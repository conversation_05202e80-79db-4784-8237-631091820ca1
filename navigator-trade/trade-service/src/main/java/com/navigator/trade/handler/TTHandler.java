package com.navigator.trade.handler;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.service.tradeticket.IContractTradeTicketService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TT处理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TTHandler {

    @Autowired
    @Lazy
    private Map<String, ITradeTicketService> tradeTicketServiceMap;



    @Autowired
    private Map<String, IContractTradeTicketService> contractTradeTicketServiceMap;

    private final Map<TTTypeEnum, ProcessorTypeEnum> cacheMap = new ConcurrentHashMap<>();

    public ITradeTicketService getStrategy(String processKey) {
        log.info("TTHandler.tradeTicketServiceMap:{}", JSON.toJSONString(tradeTicketServiceMap));
        for (String s : tradeTicketServiceMap.keySet()) {
            if (s.contains(processKey)) {
                return tradeTicketServiceMap.get(s);
            }
        }
        return null;
    }

    public ITradeTicketService getStrategy(Integer salesType, Integer ttType, Integer subGoodsCategoryId) {
        String processKey = TTHandlerUtil.getTTProcessor(salesType, ttType, subGoodsCategoryId);
        return getStrategy(processKey);
    }

    public ITradeTicketService getStrategy(TradeTicketEntity tradeTicketEntity) {
        String processKey = TTHandlerUtil.getTTProcessor(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        return getStrategy(processKey);
    }

    public ITradeTicketService getStrategyByTradeType(Integer salesType, Integer tradeType, Integer subGoodsCategoryId) {
        String processKey = TTHandlerUtil.getTTProcessorByTradeType(salesType, tradeType, subGoodsCategoryId);
        return getStrategy(processKey);

    }

    public IContractTradeTicketService getContractTTStrategy(Integer salesType, Integer ttType, Integer subGoodsCategoryId) {
        /*String processKey = getTTProcessor(salesType, ttProcessor, subGoodsCategoryId);
        for (String s : contractTradeTicketServiceMap.keySet()) {
            if (s.contains(processKey)) {
                return contractTradeTicketServiceMap.get(s);
            }
        }
        return null;*/
        return null;
    }


}
