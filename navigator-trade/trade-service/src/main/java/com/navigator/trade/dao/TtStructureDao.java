package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.TtStructureMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.entity.TTStructureEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/2 10:48
 */
@Dao
public class TtStructureDao extends BaseDaoImpl<TtStructureMapper, TTStructureEntity> {
    public List<TTStructureEntity> queryListByTTQueryDTO(TTQueryDTO ttQueryDTO) {
        List<TTStructureEntity> ttPriceEntityList = list(Wrappers.<TTStructureEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TTStructureEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TTStructureEntity::getGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
        );
        return ttPriceEntityList;
    }

    public TTStructureEntity getByTTId(Integer ttId) {
        List<TTStructureEntity> structureEntityList = list(Wrappers.<TTStructureEntity>lambdaUpdate().eq(TTStructureEntity::getTtId, ttId).orderByDesc(TTStructureEntity::getId));
        return CollectionUtils.isEmpty(structureEntityList) ? null : structureEntityList.get(0);
    }


    public Integer updateContractId(Integer ttId, Integer contractId) {
        return update(Wrappers.<TTStructureEntity>lambdaUpdate()
                .set(TTStructureEntity::getContractId, contractId)
                .eq(TTStructureEntity::getTtId, ttId)) ? 1 : 0;

    }

    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        return update(Wrappers.<TTStructureEntity>lambdaUpdate()
                .set(TTStructureEntity::getContractId, contractEntity.getId())
                .set(TTStructureEntity::getContractCode, contractEntity.getContractCode())
                .eq(TTStructureEntity::getTtId, ttId)) ? 1 : 0;
    }
}
