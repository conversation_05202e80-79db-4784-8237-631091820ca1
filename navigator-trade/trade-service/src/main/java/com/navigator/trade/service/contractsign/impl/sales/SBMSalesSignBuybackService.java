package com.navigator.trade.service.contractsign.impl.sales;

import com.navigator.trade.pojo.dto.contractsign.ContractSignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.service.contractsign.impl.BaseAbstractContractSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component("SBM_P_SIGN_BUYBACK,SBO_P_SIGN_BUYBACK")
public class SBMSalesSignBuybackService extends BaseAbstractContractSignService {
    @Override
    protected SignTemplateDTO fillSignTemplateDTO(SignTemplateDTO signTemplateDTO, ContractSignTemplateDTO contractSignTemplateDTO) {
        //TODO 可以更新该场景特有的参数
        return signTemplateDTO;
    }
}
