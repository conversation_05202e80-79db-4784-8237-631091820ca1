package com.navigator.trade.service.contract.purchase;

import com.navigator.trade.service.contract.Impl.BaseContractClosedAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 豆粕合同关闭的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_P_CONTRACT_CLOSED,SBO_P_CONTRACT_CLOSED")
public class PurchaseContractClosedService extends BaseContractClosedAbstractService {

}
