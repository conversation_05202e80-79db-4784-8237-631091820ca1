package com.navigator.trade.service.impl;

import com.navigator.trade.dao.TtModifyDao;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.service.ITtModifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ttAdd表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class TtModifyServiceImpl implements ITtModifyService {

    @Autowired
    private TtModifyDao ttModifyDao;

    @Override
    public TTModifyEntity getTTModifyEntityByTTId(Integer ttId) {
        return ttModifyDao.getTTModifyEntityByTTId(ttId);
    }
}
