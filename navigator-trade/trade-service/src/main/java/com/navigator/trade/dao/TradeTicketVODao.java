package com.navigator.trade.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.TradeTicketVOMapper;
import com.navigator.trade.pojo.dto.tradeticket.ReportDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.TradeTicketVOEntity;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Dao
public class TradeTicketVODao extends BaseDaoImpl<TradeTicketVOMapper, TradeTicketVOEntity> {

    public IPage<TradeTicketVOEntity> queryVOPageByTTQueryDTO(Page<TradeTicketVOEntity> page, TTQueryDTO ttQueryDTO, List<Integer> customerIdList, List<Integer> companyIdList) {
        ttQueryDTO.setContractCode(StringUtils.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode())
                .setBizCode(StringUtils.isNotBlank(ttQueryDTO.getBizCode()) ? ttQueryDTO.getBizCode().trim() : ttQueryDTO.getBizCode());

        // 交货开始月份
        Date startDeliveryDate = new Date();
        Timestamp endDeliveryDate = DateTimeUtil.now();
        if (StrUtil.isNotBlank(ttQueryDTO.getDeliveryStartDate())) {
            startDeliveryDate = DateTimeUtil.parseDateString(ttQueryDTO.getDeliveryStartDate() + "-01");
            endDeliveryDate = DateTimeUtil.addMonth(DateTimeUtil.parseDateString(ttQueryDTO.getDeliveryStartDate() + "-01"), 1);
        }

        LambdaQueryWrapper<TradeTicketVOEntity> wrapper = Wrappers.<TradeTicketVOEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(ttQueryDTO.getStatus()), TradeTicketVOEntity::getStatus, ttQueryDTO.getStatus())
                .like(StringUtils.isNotBlank(ttQueryDTO.getContractCode()), TradeTicketVOEntity::getContractCode, "%" + (StrUtil.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode()) + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getCode()), TradeTicketVOEntity::getCode, "%" + ttQueryDTO.getCode() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getType()), TradeTicketVOEntity::getType, ttQueryDTO.getType())
                .eq(TradeTicketVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(StringUtils.isNotBlank(ttQueryDTO.getCreateStartTime()), TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(ttQueryDTO.getCreateStartTime()))
                .le(StringUtils.isNotBlank(ttQueryDTO.getCreateEndTime()), TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(ttQueryDTO.getCreateEndTime()))
                .eq(ttQueryDTO.getSalesType() != null, TradeTicketVOEntity::getSalesType, ttQueryDTO.getSalesType())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCreateBy()), TradeTicketVOEntity::getCreatedBy, ttQueryDTO.getCreateBy())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCompanyId()), TradeTicketVOEntity::getCompanyId, ttQueryDTO.getCompanyId())
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TradeTicketVOEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getSupplierName()), TradeTicketVOEntity::getSupplierName, "%" + ttQueryDTO.getSupplierName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TradeTicketVOEntity::getSubGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
                .in(CollectionUtils.isNotEmpty(ttQueryDTO.getTradeType()), TradeTicketVOEntity::getTradeType, ttQueryDTO.getTradeType())
                .like(StringUtils.isNotBlank(ttQueryDTO.getEnterpriseName()), ttQueryDTO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? TradeTicketVOEntity::getEnterpriseName : TradeTicketVOEntity::getSupplierEnterpriseName, "%" + ttQueryDTO.getEnterpriseName() + "%")
                .in(CollectionUtils.isNotEmpty(ttQueryDTO.getDeliveryFactoryCode()), TradeTicketVOEntity::getDeliveryFactoryCode, ttQueryDTO.getDeliveryFactoryCode())
                .eq(StrUtil.isNotBlank(ttQueryDTO.getShipWarehouseId()), TradeTicketVOEntity::getShipWarehouseId, ttQueryDTO.getShipWarehouseId())
                .eq(ttQueryDTO.getContractType() != null, TradeTicketVOEntity::getContractType, ttQueryDTO.getContractType())
                .between(StrUtil.isNotBlank(ttQueryDTO.getDeliveryStartDate()), TradeTicketVOEntity::getDeliveryStartTime, startDeliveryDate, endDeliveryDate)
                .ge(StrUtil.isNotBlank(ttQueryDTO.getDeliveryStartTime()), TradeTicketVOEntity::getDeliveryStartTime, StrUtil.isNotBlank(ttQueryDTO.getDeliveryStartTime()) ? DateTimeUtil.parseTimeStamp0000(ttQueryDTO.getDeliveryStartTime().split(" ")[0]) : "")
                .le(StrUtil.isNotBlank(ttQueryDTO.getDeliveryEndTime()), TradeTicketVOEntity::getDeliveryEndTime, StrUtil.isNotBlank(ttQueryDTO.getDeliveryEndTime()) ? DateTimeUtil.parseTimeStamp2359(ttQueryDTO.getDeliveryEndTime().split(" ")[0]) : "")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getDomainCode()), TradeTicketVOEntity::getDomainCode, ttQueryDTO.getDomainCode())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getFutureCode()), TradeTicketVOEntity::getFutureCode, ttQueryDTO.getFutureCode())
                .in(TradeTicketVOEntity::getBelongCustomerId, customerIdList)
                .in(TradeTicketVOEntity::getCompanyId, companyIdList)
                .orderByDesc(TradeTicketVOEntity::getUpdatedAt);
        if (StringUtils.isNotBlank(ttQueryDTO.getBizCode())) {
            wrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketVOEntity::getContractCode, "%" + ttQueryDTO.getBizCode().trim() + "%")
                    .or(StringUtils.isNotBlank(ttQueryDTO.getBizCode()))
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketVOEntity::getCode, "%" + ttQueryDTO.getBizCode().trim() + "%"));
        }
        IPage<TradeTicketVOEntity> iPage = page(page, wrapper);
        return iPage;
    }

    public IPage<TradeTicketVOEntity> queryVOPageByTTQueryDTO(Page<TradeTicketVOEntity> page,
                                                              TTQueryDTO ttQueryDTO, List<String> siteCodeList) {
        ttQueryDTO.setContractCode(StringUtils.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode())
                .setBizCode(StringUtils.isNotBlank(ttQueryDTO.getBizCode()) ? ttQueryDTO.getBizCode().trim() : ttQueryDTO.getBizCode());

        // 交货开始月份
        Date startDeliveryDate = null;
        Date endDeliveryDate = null;
        if (CollectionUtils.isEmpty(siteCodeList)) {
            return this.page(new Page<>(0, 0, 0));
        }
        if (StrUtil.isNotBlank(ttQueryDTO.getDeliveryStartDate())) {
            startDeliveryDate = DateTimeUtil.parseDateString(ttQueryDTO.getDeliveryStartDate() + "-01");
        }
        if (StrUtil.isNotBlank(ttQueryDTO.getDeliveryEndDate())) {
            endDeliveryDate = DateTimeUtil.addMonth(DateTimeUtil.parseDateString(ttQueryDTO.getDeliveryEndDate() + "-01"), 1);
        }
        LambdaQueryWrapper<TradeTicketVOEntity> wrapper = Wrappers.<TradeTicketVOEntity>lambdaQuery()
                .and(k -> {
                    k.in(CollectionUtils.isNotEmpty(siteCodeList), TradeTicketVOEntity::getSiteCode, siteCodeList)
                            .or().isNull(TradeTicketVOEntity::getSiteCode);
                })
//                .in(CollectionUtils.isNotEmpty(siteCodeList), TradeTicketVOEntity::getSiteCode, siteCodeList)
                .eq(StringUtils.isNotBlank(ttQueryDTO.getStatus()), TradeTicketVOEntity::getStatus, ttQueryDTO.getStatus())
                .like(StringUtils.isNotBlank(ttQueryDTO.getContractCode()), TradeTicketVOEntity::getContractCode, "%" + (StrUtil.isNotBlank(ttQueryDTO.getContractCode()) ? ttQueryDTO.getContractCode().trim() : ttQueryDTO.getContractCode()) + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getCode()), TradeTicketVOEntity::getCode, "%" + ttQueryDTO.getCode() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getType()), TradeTicketVOEntity::getType, ttQueryDTO.getType())
                .eq(TradeTicketVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(StringUtils.isNotBlank(ttQueryDTO.getCreateStartTime()), TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(ttQueryDTO.getCreateStartTime()))
                .le(StringUtils.isNotBlank(ttQueryDTO.getCreateEndTime()), TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(ttQueryDTO.getCreateEndTime()))
                .eq(ttQueryDTO.getSalesType() != null, TradeTicketVOEntity::getSalesType, ttQueryDTO.getSalesType())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCreateBy()), TradeTicketVOEntity::getCreatedBy, ttQueryDTO.getCreateBy())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCompanyId()), TradeTicketVOEntity::getCompanyId, ttQueryDTO.getCompanyId())
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TradeTicketVOEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .like(StringUtils.isNotBlank(ttQueryDTO.getSupplierName()), TradeTicketVOEntity::getSupplierName, "%" + ttQueryDTO.getSupplierName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getSupplierId()), TradeTicketVOEntity::getSupplierId, ttQueryDTO.getSupplierId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCustomerId()), TradeTicketVOEntity::getCustomerId, ttQueryDTO.getCustomerId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsSpecId()), TradeTicketVOEntity::getGoodsSpecId, ttQueryDTO.getGoodsSpecId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsPackageId()), TradeTicketVOEntity::getGoodsPackageId, ttQueryDTO.getGoodsPackageId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TradeTicketVOEntity::getCategory2, ttQueryDTO.getGoodsCategoryId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getCategory3()), TradeTicketVOEntity::getCategory3, ttQueryDTO.getCategory3())
                .in(CollectionUtils.isNotEmpty(ttQueryDTO.getTradeType()), TradeTicketVOEntity::getTradeType, ttQueryDTO.getTradeType())
                .like(StringUtils.isNotBlank(ttQueryDTO.getEnterpriseName()), ttQueryDTO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? TradeTicketVOEntity::getEnterpriseName : TradeTicketVOEntity::getSupplierEnterpriseName, "%" + ttQueryDTO.getEnterpriseName() + "%")
                .in(CollectionUtils.isNotEmpty(ttQueryDTO.getDeliveryFactoryCode()), TradeTicketVOEntity::getDeliveryFactoryCode, ttQueryDTO.getDeliveryFactoryCode())
                .eq(StrUtil.isNotBlank(ttQueryDTO.getShipWarehouseId()), TradeTicketVOEntity::getShipWarehouseId, ttQueryDTO.getShipWarehouseId())
                .eq(StrUtil.isNotBlank(ttQueryDTO.getCompanyId()), TradeTicketVOEntity::getCompanyId, ttQueryDTO.getCompanyId())
                .eq(ttQueryDTO.getContractType() != null, TradeTicketVOEntity::getContractType, ttQueryDTO.getContractType())
                .ge(Objects.nonNull(startDeliveryDate), TradeTicketVOEntity::getDeliveryStartTime, startDeliveryDate)
                .lt(Objects.nonNull(endDeliveryDate), TradeTicketVOEntity::getDeliveryEndTime, endDeliveryDate)
                .eq(StringUtils.isNotBlank(ttQueryDTO.getBuCode()), TradeTicketVOEntity::getBuCode, ttQueryDTO.getBuCode())
                .eq(Objects.nonNull(ttQueryDTO.getWarrantTradeType()), TradeTicketVOEntity::getWarrantTradeType, ttQueryDTO.getWarrantTradeType())
                .eq(Objects.nonNull(ttQueryDTO.getGoodsId()), TradeTicketVOEntity::getGoodsId, ttQueryDTO.getGoodsId())
                .eq(StringUtils.isNotBlank(ttQueryDTO.getSiteCode()), TradeTicketVOEntity::getSiteCode, ttQueryDTO.getSiteCode())
                // 货品名称模糊查询
                .and(StrUtil.isNotBlank(ttQueryDTO.getGoodsName()),
                        bizWrapper -> bizWrapper.like(TradeTicketVOEntity::getGoodsName, "%" + (StrUtil.isNotBlank(ttQueryDTO.getGoodsName()) ? ttQueryDTO.getGoodsName().trim() : ttQueryDTO.getGoodsName()) + "%")
                                .or().like(TradeTicketVOEntity::getCommodityName, "%" + (StrUtil.isNotBlank(ttQueryDTO.getGoodsName()) ? ttQueryDTO.getGoodsName().trim() : ttQueryDTO.getGoodsName()) + "%"))
                .orderByDesc(TradeTicketVOEntity::getUpdatedAt);
        if (StringUtils.isNotBlank(ttQueryDTO.getBizCode())) {
            wrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketVOEntity::getContractCode, "%" + ttQueryDTO.getBizCode().trim() + "%")
                    .or(StringUtils.isNotBlank(ttQueryDTO.getBizCode()))
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketVOEntity::getCode, "%" + ttQueryDTO.getBizCode().trim() + "%")
                    .or(StringUtils.isNotBlank(ttQueryDTO.getBizCode()))
                    .like(StringUtils.isNotBlank(ttQueryDTO.getBizCode()), TradeTicketVOEntity::getWarrantCode, "%" + ttQueryDTO.getBizCode().trim() + "%"));
        }

//  权限重构
//        wrapper.and(k -> {
//            companyCustomerIdMap.entrySet().forEach(i -> {
//                k .or(v->v
//                        .eq(TradeTicketVOEntity::getCompanyId, i.getKey())
//                        .in(TradeTicketVOEntity::getBelongCustomerId, i.getValue()))
//                ;
//            });
//        });

        IPage<TradeTicketVOEntity> iPage = page(page, wrapper);
        return iPage;
    }

    public List<TradeTicketVOEntity> queryTTReport(ReportDTO reportDTO) {
        return list(Wrappers.<TradeTicketVOEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(reportDTO.getFactoryCode()), TradeTicketVOEntity::getDeliveryFactoryCode, reportDTO.getFactoryCode())
                .eq(null != reportDTO.getCategoryId(), TradeTicketVOEntity::getSubGoodsCategoryId, reportDTO.getCategoryId())
                .eq(null != reportDTO.getSaleType(), TradeTicketVOEntity::getSalesType, reportDTO.getSaleType())
                .eq(TradeTicketVOEntity::getType, TTTypeEnum.NEW.getType())
                .eq(TradeTicketVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(new Date()))
                .lt(TradeTicketVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(new Date()))
        );
    }
}
