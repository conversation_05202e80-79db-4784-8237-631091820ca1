package com.navigator.trade.app.adpater.filler;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.TradeSupportDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.TTDomainProcessTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TradeTieckFiller {

    @Autowired
    TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    TTAddEntityFiller ttAddEntityFiller;
    @Autowired
    TTModifyEntityFiller ttModifyEntityFiller;
    @Autowired
    TTPriceEntityFiller ttPriceEntityFiller;
    @Autowired
    TTTransferEntityFiller ttTransferEntityFiller;
    @Autowired
    TTStructureEntityFiller ttStructureEntityFiller;


    public TradeTicketDO fillTradeTicketDO(TradeTicketDO tradeTicketDO) {

        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTAddEntity ttAddEntity = null;
        TTModifyEntity ttModifyEntity = null;
        TTPriceEntity ttPriceEntity = null;
        TTTranferEntity ttTranferEntity = null;
        TTStructureEntity ttStructureEntity = null;

        TradeSupportDTO tradeSupportDTO = tradeDomainRemoteService.getTradeSupportDTO(tradeTicketEntity);
        CustomerDTO customerDTO = tradeSupportDTO.getCustomerDTO();
        CustomerDTO supplierDTO = tradeSupportDTO.getSupplierDTO();
        SiteEntity siteEntity = tradeSupportDTO.getSiteEntity();
        WarehouseEntity warehouseEntity = tradeSupportDTO.getWarehouseEntity();
        SkuEntity skuEntity = tradeSupportDTO.getSkuEntity();

        TTDomainProcessTypeEnum ttDomainProcessTypeEnum = TTDomainProcessTypeEnum.getByTTType(tradeTicketDO.getTTType());
        switch (ttDomainProcessTypeEnum) {
            case ADD:
                ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
                ttAddEntityFiller.fillEntity(ttAddEntity, tradeTicketEntity, tradeSupportDTO);
                tradeTicketDO.setTtSubEntity(ttAddEntity);
                break;
            case MODIFY:
                ttModifyEntity = (TTModifyEntity) tradeTicketDO.getTtSubEntity();
                ttModifyEntityFiller.fillEntity(ttModifyEntity, tradeTicketEntity, tradeSupportDTO);
                tradeTicketDO.setTtSubEntity(ttModifyEntity);
                break;
            case PRICE:
                ttPriceEntity = (TTPriceEntity) tradeTicketDO.getTtSubEntity();
                ttPriceEntityFiller.fillEntity(ttPriceEntity, tradeTicketEntity, tradeSupportDTO);
                tradeTicketDO.setTtSubEntity(ttPriceEntity);
                break;
            case TRANSFER:
                ttTranferEntity = (TTTranferEntity) tradeTicketDO.getTtSubEntity();
                ttTransferEntityFiller.fillEntity(ttTranferEntity, tradeTicketEntity, tradeSupportDTO);
                tradeTicketDO.setTtSubEntity(ttTranferEntity);
                break;
            case STRUCTURE:
                ttStructureEntity = (TTStructureEntity) tradeTicketDO.getTtSubEntity();
                ttStructureEntityFiller.fillEntity(ttStructureEntity, tradeTicketEntity, tradeSupportDTO);
                tradeTicketDO.setTtSubEntity(ttStructureEntity);
                break;
            default:
                break;
        }

        return tradeTicketDO;
    }

    public void fillEntity(TradeTicketEntity tradeTicketEntity, TradeSupportDTO tradeSupportDTO) {
        CustomerDTO customerDTO = tradeSupportDTO.getCustomerDTO();
        CustomerDTO supplierDTO = tradeSupportDTO.getSupplierDTO();
        SiteEntity siteEntity = tradeSupportDTO.getSiteEntity();
        WarehouseEntity warehouseEntity = tradeSupportDTO.getWarehouseEntity();
        SkuEntity skuEntity = tradeSupportDTO.getSkuEntity();

        /**
         * ================ 1、填充基本信息 ================
         * 主体、所属商务、创建人等
         * =============================================
         * */
        if (null != siteEntity) {
            if (StringUtil.isEmpty(tradeTicketEntity.getSiteName())) {
                tradeTicketEntity.setSiteName(siteEntity.getName());
                tradeTicketEntity.setBelongCustomerId(siteEntity.getBelongCustomerId());
                tradeTicketEntity.setCompanyId(siteEntity.getCompanyId());
                tradeTicketEntity.setCompanyName(siteEntity.getCompanyName());
            }
        }

        /**
         * ================ 2、填充合同信息 ================
         * TBD
         * =============================================
         * */

        /**
         * ================ 3、填充协议信息 ================
         * TBD
         * =============================================
         * */

        /**
         * ================ 4、填充客户/供应商信息 ================
         * customerDTO、supplierDTO
         * =============================================
         * */
        if (null != customerDTO) {
            if (StringUtil.isEmpty(tradeTicketEntity.getCustomerCode())) {
                tradeTicketEntity.setCustomerCode(customerDTO.getCode());
                tradeTicketEntity.setCustomerName(customerDTO.getName());
            }
        }
        if (null != supplierDTO) {
            if (StringUtil.isEmpty(tradeTicketEntity.getSupplierCode())) {
                tradeTicketEntity.setSupplierCode(supplierDTO.getCode());
                tradeTicketEntity.setSupplierName(supplierDTO.getName());
            }
        }

        /**
         * ================ 5、填充货品信息 ================
         * Category、Code、Name等、质量检验、重量检验等
         * =============================================
         * */
        if (null != skuEntity) {
            tradeTicketEntity.setCategory1(skuEntity.getCategory1());
            tradeTicketEntity.setCategory2(skuEntity.getCategory2());
            tradeTicketEntity.setCategory3(skuEntity.getCategory3());
            tradeTicketEntity.setGoodsCategoryId(skuEntity.getCategory1());
            tradeTicketEntity.setSubGoodsCategoryId(skuEntity.getCategory2());
            tradeTicketEntity.setGoodsName(skuEntity.getFullName());
            if (StringUtil.isEmpty(tradeTicketEntity.getCommodityName())) {
                tradeTicketEntity.setCommodityName(skuEntity.getNickName());
            }
        }

        /**
         * ================ 6、填充价格信息 ================
         * 各种价格
         * =============================================
         * */

        /**
         * ================ 7、填充金额信息 ================
         * 量、比例、金额等，含点价和提货的金额
         * =============================================
         * */

        /**
         * ================ 8、填充财务信息 ================
         * 付款类型、使用规则、结算、付款代码、发票等
         * =============================================
         * */

        /**
         * ================ 9、填充期货、期权、点转反、交易所等信息 ================
         * FutureCode等、Price、Transfer等
         * =============================================
         * */


        /**
         * ================ 10、填充提货信息 ================
         * 工厂、目的地、仓库等
         * =============================================
         * */



        /**
         * ================ 11、填充结构化定价信息 ================
         * 结构化定价的属性等
         * =============================================
         * */


        /**
         * ================ 12、填充仓单信息 ================
         * 仓单的属性等
         * =============================================
         * */

    }

    private void fill1_main(TradeTicketEntity tradeTicketEntity,
                            SiteEntity siteEntity) {


    }

    private void fill2_contract(TradeTicketEntity tradeTicketEntity,
                                TTAddEntity ttAddEntity,
                                TTModifyEntity ttModifyEntity,
                                TTPriceEntity ttPriceEntity,
                                TTTranferEntity ttTranferEntity,
                                TTStructureEntity ttStructureEntity) {


        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill3_sign(TradeTicketEntity tradeTicketEntity,
                            TTAddEntity ttAddEntity,
                            TTModifyEntity ttModifyEntity,
                            TTPriceEntity ttPriceEntity,
                            TTTranferEntity ttTranferEntity,
                            TTStructureEntity ttStructureEntity,
                            CustomerDTO customerDTO,
                            CustomerDTO supplierDTO) {


        if (null != ttAddEntity) {
            if (null != customerDTO) {
                if (StringUtil.isEmpty(tradeTicketEntity.getCustomerCode())) {
                    ttAddEntity.setCustomerCode(customerDTO.getCode());
                    ttAddEntity.setCustomerName(customerDTO.getName());
                }
            }
            if (null != supplierDTO) {
                if (StringUtil.isEmpty(tradeTicketEntity.getSupplierCode())) {
                    ttAddEntity.setSupplierAccount(supplierDTO.getBankAccountNo());
                    ttAddEntity.setSupplierName(supplierDTO.getName());
                }
            }
        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill4_customer(TradeTicketEntity tradeTicketEntity,
                                TTAddEntity ttAddEntity,
                                TTModifyEntity ttModifyEntity,
                                TTPriceEntity ttPriceEntity,
                                TTTranferEntity ttTranferEntity,
                                TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill5_goods(TradeTicketEntity tradeTicketEntity,
                             TTAddEntity ttAddEntity,
                             TTModifyEntity ttModifyEntity,
                             TTPriceEntity ttPriceEntity,
                             TTTranferEntity ttTranferEntity,
                             TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill6_price(TradeTicketEntity tradeTicketEntity,
                             TTAddEntity ttAddEntity,
                             TTModifyEntity ttModifyEntity,
                             TTPriceEntity ttPriceEntity,
                             TTTranferEntity ttTranferEntity,
                             TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill7_amount(TradeTicketEntity tradeTicketEntity,
                              TTAddEntity ttAddEntity,
                              TTModifyEntity ttModifyEntity,
                              TTPriceEntity ttPriceEntity,
                              TTTranferEntity ttTranferEntity,
                              TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill8_finance(TradeTicketEntity tradeTicketEntity,
                               TTAddEntity ttAddEntity,
                               TTModifyEntity ttModifyEntity,
                               TTPriceEntity ttPriceEntity,
                               TTTranferEntity ttTranferEntity,
                               TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill9_future(TradeTicketEntity tradeTicketEntity,
                              TTAddEntity ttAddEntity,
                              TTModifyEntity ttModifyEntity,
                              TTPriceEntity ttPriceEntity,
                              TTTranferEntity ttTranferEntity,
                              TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill10_delivery(TradeTicketEntity tradeTicketEntity,
                                 TTAddEntity ttAddEntity,
                                 TTModifyEntity ttModifyEntity,
                                 TTPriceEntity ttPriceEntity,
                                 TTTranferEntity ttTranferEntity,
                                 TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill11_structure(TradeTicketEntity tradeTicketEntity,
                                  TTAddEntity ttAddEntity,
                                  TTModifyEntity ttModifyEntity,
                                  TTPriceEntity ttPriceEntity,
                                  TTTranferEntity ttTranferEntity,
                                  TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }

    private void fill12_warrant(TradeTicketEntity tradeTicketEntity,
                                TTAddEntity ttAddEntity,
                                TTModifyEntity ttModifyEntity,
                                TTPriceEntity ttPriceEntity,
                                TTTranferEntity ttTranferEntity,
                                TTStructureEntity ttStructureEntity) {
        if (null != ttAddEntity) {

        }
        if (null != ttModifyEntity) {

        }
        if (null != ttPriceEntity) {

        }
        if (null != ttTranferEntity) {

        }
        if (null != ttStructureEntity) {

        }
    }
}
