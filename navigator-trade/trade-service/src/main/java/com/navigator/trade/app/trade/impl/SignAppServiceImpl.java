package com.navigator.trade.app.trade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.trade.SignAppService;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.pojo.dto.contract.ContractConfirmResultDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractReviewStatusEnum;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.service.async.ContractAsyncExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 协议内容操作
 * <AUTHOR>
 * @Description
 * @Date 2024/7/19 22:34
 * @Version 1.0
 */
@Service
public class SignAppServiceImpl implements SignAppService {

    @Resource
    private ContractSignLogicService contractSignLogicService;

    @Resource
    private ContractLogicService contractLogicService;

    /**
     * TODO 待TT域提供了在调整
     */
    @Resource
    private TradeTicketDao tradeTicketDao;

    @Resource
    private TTQueryLogicService ttQueryLogicService;

    /**
     * TODO 待优化
     */
    @Autowired
    private ContractAsyncExecutor contractAsyncExecutor;

    /**
     * 协议确认合规
     *
     * @param contractBaseDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmContract(ContractBaseDTO contractBaseDTO) {
        // 1.合规是否通过
        ContractSignEntity contractSignEntity = contractSignLogicService.confirmContract(contractBaseDTO);
        if (contractBaseDTO.getReviewStatus().equals(ContractReviewStatusEnum.PASS.getValue())) {
            //2.修改TT状态
            TradeTicketQO ticketQO = new TradeTicketQO();
            ticketQO.setTtId(contractSignEntity.getTtId());
            TradeTicketEntity tradeTicketEntity = ttQueryLogicService.fetchTradeTicketEntity(ticketQO);
            tradeTicketDao.updateStatusById(TTStatusEnum.DONE.getType(), TTApproveStatusEnum.APPROVE.getValue(), contractSignEntity.getTtId());
            // 3. 处理合同
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId())
                    .setContractSource(tradeTicketEntity.getContractSource())
                    .setTtTradeType(tradeTicketEntity.getTradeType())
                    .setTtCode(tradeTicketEntity.getCode())
                    .setTtId(tradeTicketEntity.getId());
            ContractConfirmResultDTO contractConfirmResultDTO = contractLogicService.confirmContractModify(contractModifyDTO);
            // TODO 调整记录同步结果
            contractAsyncExecutor.recordContractConfirmLog(tradeTicketEntity, JSON.toJSONString(contractModifyDTO), JSON.toJSONString(contractConfirmResultDTO));
        }
        return true;
    }
}
