package com.navigator.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.trade.pojo.dto.tradeticket.StatQueryDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.entity.TradeTicketVOEntity;
import com.navigator.trade.pojo.vo.TTAllStatusNumVO;

import java.util.List;

/**
 * <p>
 * TT申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
public interface TradeTicketMapper extends BaseMapper<TradeTicketEntity> {

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
    TTAllStatusNumVO getTTStat(StatQueryDTO statQueryDTO);

    List<TradeTicketVOEntity> queryTTPagedList(TTQueryDTO ttQueryDTO);

    Integer queryTTTotalCount(TTQueryDTO ttQueryDTO);
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end
}
