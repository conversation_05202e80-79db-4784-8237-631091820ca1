package com.navigator.trade.service.contract.sales;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.Impl.BaseContractBuyBackAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 合同回购的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-17
 */
@Slf4j
@Service("SBM_P_CONTRACT_BUYBACK,SBO_P_CONTRACT_BUYBACK")
public class SalesContractBuyBackService extends BaseContractBuyBackAbstractService {
    @Resource
    public IContractOperationNewService salesContractOperationService;

    @Override
    protected void recordOperationLog(ContractBuyBackDTO contractBuyBackDTO, ContractEntity contractEntity) {
        // 记录操作记录
        salesContractOperationService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.SALES_CONTRACT_BUY_BACK, JSONUtil.toJsonStr(contractBuyBackDTO),
                SystemEnum.MAGELLAN.getValue());
    }
}
