package com.navigator.trade.service.contract.Impl;

import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.service.contract.GroupIdGenerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 1003270 batch TT creation group_id field changed by <PERSON> at 2025-06-17 start
 * Group ID generation service implementation
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
public class GroupIdGenerationServiceImpl implements GroupIdGenerationService {

    @Autowired
    private ContractDao contractDao;

    /**
     * Generate a new group ID using database sequence
     * This approach is thread-safe and prevents race conditions
     *
     * @return new group ID from sequence
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateGroupId() {
        try {
            // Get next value from database sequence - thread-safe
            Integer newGroupId = contractDao.getNextGroupId();

            log.info("Generated new group ID from sequence: {}", newGroupId);
            return newGroupId;

        } catch (Exception e) {
            log.error("Error generating group ID from sequence", e);
            throw new RuntimeException("Failed to generate group ID from sequence", e);
        }
    }
}
// 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
