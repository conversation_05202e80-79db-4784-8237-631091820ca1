package com.navigator.trade.app.contract.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.dao.ContractHistoryDao;
import com.navigator.trade.dao.ContractPriceDao;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.ITtPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 合同值对象基础服务
 * 合同域相关内容处理
 * 包括合同、合同历史、合同价格、合同结构化定价详情
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class ContractDomainServiceImpl implements ContractDomainService {

    @Autowired
    ContractDao contractDao;
    /**
     * TODO 待优化
     */
    @Autowired
    private ITtPriceService ttPriceService;
    /**
     * 合同历史DAO
     */
    @Autowired
    ContractHistoryDao contractHistoryDao;
    /**
     * 合同价格信息
     */
    @Autowired
    ContractPriceDao contractPriceDao;
    /**
     * 结构化定价合同
     */
    @Autowired
    ContractStructureDao contractStructureDao;

    @Override
    public boolean saveContract(ContractEntity contractEntity) {
        return contractDao.save(contractEntity);
    }

    @Override
    public boolean saveContractStructure(ContractStructureEntity contractStructureEntity) {
        return contractStructureDao.save(contractStructureEntity);
    }

    @Override
    public boolean updateContractById(ContractEntity contractEntity) {
        if (ObjectUtil.isEmpty(contractEntity.getVersion())) {
            contractEntity.setVersion(0);
        } else {
            contractEntity.setVersion(contractEntity.getVersion() + 1);
        }
        return contractDao.updateById(contractEntity);
    }


    @Override
    public boolean updateContractById(ContractEntity contractEntity, String backTradeType, String referCode) {
        contractEntity.setVersion(contractEntity.getVersion() + 1);
        contractEntity.setMainVersion(contractEntity.getMainVersion() + 1);
        boolean result = contractDao.updateById(contractEntity);
        try {
            // 记录合同历史
            backupContract(contractEntity, backTradeType, referCode);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }


    @Override
    public ContractEntity rollBackContract(Integer contractId) {
        ContractEntity contractEntity = contractDao.getById(contractId);
        if (null != contractEntity) {
            // 获取合同历史记录
            ContractHistoryEntity historyContract = contractHistoryDao.getContractHistoryEntity(contractId, contractEntity.getMainVersion());
            if (null != historyContract) {
                contractEntity = BeanConvertUtils.convert(ContractEntity.class, historyContract);
                contractEntity.setId(historyContract.getContractId());

                // 更新contractPrice
                List<ContractPriceEntity> priceEntityList = contractPriceDao.getContractPriceListContractId(contractId);
                if (priceEntityList.size() > 1) {
                    contractPriceDao.updatePriceByTtId(priceEntityList.get(0).setIsDeleted(IsDeletedEnum.DELETED.getValue()));
                } else {
                    ContractPriceEntity contractPriceEntity = JSON.parseObject(historyContract.getPriceInfo(), ContractPriceEntity.class);
                    contractPriceEntity.setUpdatedAt(DateTimeUtil.now());
                    contractPriceDao.updatePriceByTtId(contractPriceEntity);
                }

                // TODO：这块需要储存到合同内数据结构 是否存在点价单- 待完善
                List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractId);
                if (CollectionUtil.isNotEmpty(confirmPriceList)) {
                    BigDecimal totalPriceNum = BigDecimal.ZERO;
                    totalPriceNum = totalPriceNum.add(confirmPriceList.stream()
                            .map(TTPriceEntity::getNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    contractEntity.setTotalPriceNum(totalPriceNum);
                }

                contractDao.updateById(contractEntity);
            }
        }
        return contractEntity;
    }

    /**
     * 合同历史备份
     *
     * @param contractEntity
     * @param backTradeType
     * @param referCode
     */
    public void backupContract(ContractEntity contractEntity, String backTradeType, String referCode) {
        // 创建合同历史信息
        ContractHistoryEntity contractHistoryEntity;
        contractHistoryEntity = BeanConvertUtils.convert(ContractHistoryEntity.class, contractEntity);

        Integer contractId = contractEntity.getId();

        ContractStructureEntity contractStructureEntity = null;
        ContractPriceEntity contractPriceEntity = null;

        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {
            contractStructureEntity = contractStructureDao.getContractStructure(contractId);
        } else {
            contractPriceEntity = contractPriceDao.getContractPriceEntityContractId(contractId);
        }

        contractHistoryEntity.setId(null)
                .setContractId(contractId)
                .setBackTradeType(backTradeType)
                .setBackBizCode(referCode)
                .setBackTime(new Date())
                .setPriceInfo(JSON.toJSONString(contractPriceEntity))
                .setStructureInfo(JSON.toJSONString(contractStructureEntity));

        contractHistoryDao.save(contractHistoryEntity);
    }


    @Override
    public boolean updateStructureContractPricingStatus(ContractStructureDTO contractStructureDTO) {
        boolean rtn = false;
        ContractStructureEntity contractStructureEntity = contractStructureDao.getContractStructure(contractStructureDTO.getContractId());
        if (null != contractStructureEntity) {
            contractStructureEntity.setPriceStatus(contractStructureDTO.getPriceStatus());
            rtn = contractStructureDao.updateById(contractStructureEntity);
        }
        return rtn;
    }

    @Override
    public boolean updateContractStatusById(Integer contractId, Integer status) {
        ContractEntity contractEntity = contractDao.getContractById(contractId);
        contractEntity.setStatus(status);
        if (ContractStatusEnum.INVALID.getValue() == status) {
            contractEntity.setWarrantCancelCount(BigDecimal.ZERO);
            contractEntity.setContractNum(BigDecimal.ZERO);
        }
        return contractDao.updateById(contractEntity);
    }

    @Override
    public Result updateContractStatus(MultipartFile uploadFile, Integer status) {
        List<ContractEntity> contractList = EasyPoiUtils.importExcel(uploadFile, 0, 1, ContractEntity.class);
        if (CollectionUtils.isEmpty(contractList)) {
            return Result.success("无数据信息！");
        }
        status = null == status ? ContractStatusEnum.CLOSED.getValue() : status;
        List<String> contractCodeList = new ArrayList<>();
        for (ContractEntity contractInfo : contractList) {
            ContractEntity contractEntity = contractDao.getBasicContractByCode(contractInfo.getContractCode().trim());
            contractDao.updateById(contractEntity.setStatus(status));
            contractCodeList.add(contractInfo.getContractCode().trim());
        }
        return Result.success("合同更新成功" + FastJsonUtils.getBeanToJson(contractCodeList));
    }

    @Override
    public boolean updateStructureContract(ContractStructureEntity contractStructureEntity) {
        return contractStructureDao.updateById(contractStructureEntity);
    }

    @Override
    public ContractEntity updateContractForwardPrice(ContractEntity contractEntity, BigDecimal averagePrice) {
        ContractPriceEntity contractPriceEntity = contractPriceDao.getContractPriceEntityContractId(contractEntity.getId());

        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        priceDetailBO.setForwardPrice(averagePrice);
        contractPriceEntity.setForwardPrice(averagePrice);

        BigDecimal deliveryPrice = contractPriceEntity.getTransportPrice()
                .add(contractPriceEntity.getLiftingPrice())
                .add(contractPriceEntity.getDelayPrice())
                .add(contractPriceEntity.getTemperaturePrice())
                .add(contractPriceEntity.getOtherDeliveryPrice());
        BigDecimal taxRate = contractEntity.getTaxRate();
        BigDecimal unitPrice = calculatePriceDetailBO(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        BigDecimal totalAmount = unitPrice.multiply(contractEntity.getContractNum());
        BigDecimal depositAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, totalAmount, BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));

        contractEntity.setTotalAmount(totalAmount)
                .setDepositAmount(depositAmount)
                .setCifUnitPrice(cifUnitPrice)
                .setFobUnitPrice(fobUnitPrice)
                .setUnitPrice(unitPrice);

        // 更新contractPrice
        contractPriceDao.updatePriceByTtId(contractPriceEntity);

        return contractEntity;
    }

    /**
     * 计算价格详情的总值 （其他价格+vePrice*veContent）
     *
     * @param priceDetailBO 价格详情对象
     * @return 总价值，类型为BigDecimal
     */
    public BigDecimal calculatePriceDetailBO(PriceDetailBO priceDetailBO) {
        BigDecimal totalValue = BigDecimal.ZERO;
        Class<?> clazz = priceDetailBO.getClass();
        Field[] fields = clazz.getDeclaredFields();

        // 记录需要排除的字段名称
        String[] excludeFields = {"vePrice", "veContent"};

        for (Field field : fields) {
            // 检查字段是否需要排除
            if (isExcluded(field, excludeFields)) {
                continue;
            }

            // 设置字段可访问
            field.setAccessible(true);

            try {
                Object fieldValue = field.get(priceDetailBO);
                if (fieldValue instanceof BigDecimal) {
                    BigDecimal value = (BigDecimal) fieldValue;
                    totalValue = totalValue.add(value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        // 添加 vePrice 和 veContent 的乘积
        BigDecimal vePrice = getBigDecimalField(priceDetailBO, "vePrice");
        BigDecimal veContent = getBigDecimalField(priceDetailBO, "veContent");

        if (vePrice != null && veContent != null) {
            totalValue = totalValue.add(vePrice.multiply(veContent));
        }

        return totalValue;
    }

    @Override
    public void updateById(ContractStructureEntity contractStructureEntity) {
        contractStructureDao.updateById(contractStructureEntity);
    }

    @Override
    public boolean saveStructureContract(ContractStructureEntity contractStructure) {
        return contractStructureDao.save(contractStructure);
    }

    @Override
    public boolean closedBySiteCodeAndContractCode(String siteCode, String contractCode) {
        return contractDao.closedBySiteCodeAndContractCode(siteCode, contractCode);
    }

    private boolean isExcluded(Field field, String[] excludeFields) {
        for (String fieldName : excludeFields) {
            if (fieldName.equals(field.getName())) {
                return true;
            }
        }
        return false;
    }

    private BigDecimal getBigDecimalField(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object fieldValue = field.get(obj);
            if (fieldValue instanceof BigDecimal) {
                return (BigDecimal) fieldValue;
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

}
