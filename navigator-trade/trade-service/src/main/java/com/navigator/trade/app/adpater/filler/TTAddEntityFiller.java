package com.navigator.trade.app.adpater.filler;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.TradeSupportDTO;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TTAddEntityFiller {

    @Autowired
    private WarrantFacade warrantFacade;

    public void fillEntity(TTAddEntity targetEntity, TradeTicketEntity tradeTicketEntity, TradeSupportDTO tradeSupportDTO) {
        CustomerDTO customerDTO = tradeSupportDTO.getCustomerDTO();
        CustomerDTO supplierDTO = tradeSupportDTO.getSupplierDTO();
        SiteEntity siteEntity = tradeSupportDTO.getSiteEntity();
        WarehouseEntity warehouseEntity = tradeSupportDTO.getWarehouseEntity();
        SkuEntity skuEntity = tradeSupportDTO.getSkuEntity();

        /**
         * ================ 1、填充基本信息 ================
         * 主体、所属商务、创建人等
         * =============================================
         * */

        /**
         * ================ 2、填充合同信息 ================
         * Code、Type等
         * =============================================
         * */
        if (StringUtil.isEmpty(targetEntity.getContractCode())) {
            targetEntity.setContractCode(tradeTicketEntity.getContractCode());
            targetEntity.setContractType(tradeTicketEntity.getContractType());
        }

        /**
         * ================ 3、填充协议信息 ================
         * Code、Type等
         * =============================================
         * */


        /**
         * ================ 4、填充客户/供应商信息 ================
         * customerDTO、supplierDTO、账户、联系人等
         * =============================================
         * */
        if (StringUtil.isEmpty(targetEntity.getCustomerCode())) {
            targetEntity.setCustomerCode(tradeTicketEntity.getCustomerCode());
            targetEntity.setCustomerName(tradeTicketEntity.getCustomerName());
            targetEntity.setCustomerId(tradeTicketEntity.getCustomerId());
        }
        if (StringUtil.isEmpty(targetEntity.getSupplierName())) {
            targetEntity.setSupplierId(tradeTicketEntity.getSupplierId());
            targetEntity.setSupplierName(tradeTicketEntity.getSupplierName());
            if (null != supplierDTO) {
                targetEntity.setSupplierAccount(supplierDTO.getBankAccountNo());
            }
        }

        /**
         * ================ 5、填充货品信息 ================
         * Category、Code、Name等、质量检验、重量检验等、用途等
         * =============================================
         * */
        if (StringUtil.isEmpty(targetEntity.getGoodsName())) {
            targetEntity.setGoodsName(tradeTicketEntity.getGoodsName());
            targetEntity.setGoodsCategoryId(tradeTicketEntity.getCategory2());
            targetEntity.setGoodsPackageId(skuEntity.getPackageId());
            targetEntity.setGoodsSpecId(skuEntity.getSpecId());
        }
        //TODO NEO SystemRule
        //targetEntity.setWeightCheck();
        targetEntity.setQualityCheck(tradeSupportDTO.getCustomerDetailEntity().getQualityCheckContent());


        /**
         * ================ 6、填充价格信息 ================
         * 各种价格
         * =============================================
         * */

        /**
         * ================ 7、填充金额信息 ================
         * 量、比例、金额等，含点价和提货的金额
         * =============================================
         * */

        /**
         * ================ 8、填充财务信息 ================
         * 付款类型、使用规则、结算、付款代码、发票等
         * =============================================
         * */

        /**
         * ================ 9、填充期货、期权、点转反、交易所等信息 ================
         * FutureCode等、Price、Transfer等
         * =============================================
         * */

        /**
         * ================ 10、填充提货信息 ================
         * 工厂、目的地、仓库等
         * =============================================
         * */
        if (StringUtil.isEmpty(targetEntity.getShipWarehouseValue())) {
            targetEntity.setShipWarehouseValue(warehouseEntity.getName());
        }
        //TODO NEO SystemRule
        //targetEntity.setDestination()
        //targetEntity.setDestinationValue()

        /**
         * ================ 11、填充结构化定价信息 ================
         * 结构化定价的属性等
         * =============================================
         * */


        /**
         * ================ 12、填充仓单信息 ================
         * 仓单的属性等
         * =============================================
         * */
        if (StringUtil.isEmpty(targetEntity.getWarrantCode())) {
            Result result = warrantFacade.queryWarrantByID(targetEntity.getWarrantId());
            if (result.isSuccess()) {
                WarrantEntity warrantEntity = (WarrantEntity) result.getData();
                targetEntity.setWarrantCode(warrantEntity.getCode());
                targetEntity.setWarrantCategory(warrantEntity.getCategory());
                targetEntity.setWarrantTradeType(warrantEntity.getTradeType());
            }
        }

    }
}
