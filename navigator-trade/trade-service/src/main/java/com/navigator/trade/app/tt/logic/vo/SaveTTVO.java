package com.navigator.trade.app.tt.logic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:22
 * @Version 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveTTVO {
    /**
     * TTid
     */
    private Integer ttId;

    /**
     * TT编号
     */
    private String code;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同ID
     */
    private Integer contractId;

    /**
     * 协议Id
     */
    private Integer signId;

    /**
     * 协议编号
     */
    private String protocolCode;

    /**
     * 协议状态
     */
    private String protocolStatus;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 卖方客户名称
     */
    private String supplierName;

    /**
     * 交易类型：1、New（新增）2、Split（变更）3、Transfer（变更包含转厂执行） 4、resale（回购再重售）5、washout（解约定赔）
     */
    private Integer tradeType;

    /**
     * TT类型（1、销售合同新增；2、销售合同变更；3、销售合同回购；4、点价申请）
     */
    private Integer type;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 审批状态（0、无需审批 4、审批驳回  5、审批通过  1、待A签 2、待B签 3、待C签 ）
     */
    private Integer approvalStatus;

    /**
     * 失效原因
     */
    private String invalidReason;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 签订日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date signDate;

    /**
     * 是否可作废判断条件(0:不可作废 1: 可作废)
     */
    private Integer invalidStatus;

    /**
     * 是否可撤回判断条件(0:不可撤回 1: 可撤回)
     */
    private Integer cancelStatus;

    /**
     * 合同状态
     */
    private String contractStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 来源标识：1原合同 2新合同
     */
    private Integer sourceFlag = 2;

    /**
     * 期货合约
     */
    private String domainCode;

    /**
     * 默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装
     */
    private String goodsPackageName;

    /**
     * 规格（默认43%），可选
     */
    private String goodsSpecName;

    /**
     * 交货工厂编码
     */
    private String deliveryFactoryCode;

    /**
     * 发货库点ID，默认为工厂豆粕库，可选
     */
    private String shipWarehouseName;

    /**
     * 客户集团名称
     */
    private String enterpriseName;

    /**
     * 合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）
     */
    private Integer contractType;

    /**
     * 基差价格
     */
    private BigDecimal extraPrice;

    /**
     * 含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 交提货方式（卖方车板交货/码头卖方船板自提）
     */
    private String deliveryType;

    /**
     * 袋皮扣重
     */
    private String packageWeight;

    /**
     * 点价截止时间
     */
    private String priceEndTime;

    /**
     * 总数量
     */
    private BigDecimal contractNum;

    /**
     * 开始交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    /**
     * 截止交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    /**
     * 履约保证金比例
     */
    private Integer depositRate;

    /**
     * 履约保证金点价后补缴
     */
    private Integer addedDepositRate;

    /**
     * 追加履约保证金比例
     */
    private Integer addedDepositRate2;

    /**
     * 赊销账期
     */
    private Integer creditDays;

    /**
     * 备注
     */
    private String memo;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 来源类型: 1.保存 2.提交（默认）
     */
    private Integer sourceType;
}
