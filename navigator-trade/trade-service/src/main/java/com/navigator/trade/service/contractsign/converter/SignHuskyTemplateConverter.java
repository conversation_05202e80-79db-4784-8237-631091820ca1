package com.navigator.trade.service.contractsign.converter;

import com.navigator.trade.pojo.dto.contractsign.SignHuskyTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-13 15:30
 **/
public class SignHuskyTemplateConverter {
    public static SignHuskyTemplateDTO convert(SignTemplateDTO templateDTO) {
        return new SignHuskyTemplateDTO()
                .setTemplateCondition(templateDTO.getTemplateCondition())
                .setKeyVariableDTO(templateDTO.getKeyVariableDTO())
                .setGoodsCategorySymbol(templateDTO.getVrjc())
                .setGoodsCategoryCode(templateDTO.getVrqc())
                .setContractCode(templateDTO.getNo())
                .setQrCodeImage(templateDTO.getEwm())
                .setBarCodeImage(templateDTO.getTxm())
                .setSignDate(templateDTO.getDoc())
                .setOriginalSignDate(templateDTO.getYdoc())
                .setDeliveryTypeInfo(templateDTO.getDg())
                .setContractTypeInfo(templateDTO.getTy())
                .setPaymentTypeInfo(templateDTO.getPm())
                .setLdcSignPlace(templateDTO.getAd())
                .setCustomerName(templateDTO.getNa())
                .setCustomerSplitName(templateDTO.getNna())
                .setLdcName(templateDTO.getMe())
                .setLdcAddress(templateDTO.getBscd())
                .setSupplierAddress(templateDTO.getSscd())
                .setGoodsCategoryName(templateDTO.getVr())
                .setContractNum(templateDTO.getMt())
                .setWeightTolerance(templateDTO.getOs())
                .setGoodsSpecId(templateDTO.getEg())
                .setPackageWeight(templateDTO.getAg())
                .setOriginalPackageWeight(templateDTO.getYag())
                .setUnitPrice(templateDTO.getPr())
                .setUnitPriceDetail(templateDTO.getPrx())
                .setUnitPriceDiffDetail(templateDTO.getPrxy())
                .setTransportPrice(templateDTO.getYf())
                .setOriginalTransportPrice(templateDTO.getYyf())
                .setWarehouseAddress(templateDTO.getDs())
                .setWarehouseDeliveryPoint(templateDTO.getDd())
                .setDeliveryTime(templateDTO.getPo())
                .setDestination(templateDTO.getPy())
                .setOriginalDestination(templateDTO.getPye())
                .setWeightCheck(templateDTO.getPe())
                .setCreditDays(templateDTO.getMes())
                .setLdcBankName(templateDTO.getKh())
                .setLdcBankAccountNo(templateDTO.getZh())
                .setDepositPayEndDay(templateDTO.getJzfk())
                .setDepositRateInfo(templateDTO.getMr())
                .setAddedDepositRateInfo(templateDTO.getDmr())
                .setCustomerContactName(templateDTO.getFox())
                .setDomainCodeInfo(templateDTO.getHy())
                .setDomainCode(templateDTO.getHyj())
                .setCategoryDomainCode(templateDTO.getVrjc() + templateDTO.getHyj())
                .setExtraPrice(templateDTO.getJcj())
                .setPriceEndTime(templateDTO.getDjj())
                .setProtocolStartDate(templateDTO.getKjr())
                .setProtocolNo(templateDTO.getKjh())
                .setSignProtocolCode(templateDTO.getXyb())
                .setOriginalPriceNum(templateDTO.getHtdj())
                .setPriceApplyTime(templateDTO.getDjs())
                .setTransactionPrice(templateDTO.getDjjg())
                .setRemainPriceNum(templateDTO.getWdjl())
                .setTradeTicketTime(templateDTO.getTtxr())
                .setOriginalContractCode(templateDTO.getNoy())
                .setTransferNum(templateDTO.getZysl())
                .setOriginalDomainCode(templateDTO.getYhy())
                .setThisTimeFee(templateDTO.getSxf())
                .setDeliveryFactoryName(templateDTO.getJhgc())
                .setSourceContractNum(templateDTO.getCfsl())
                .setOriginalDeliveryFactoryName(templateDTO.getYjhgc())
                .setRemainBillNum(templateDTO.getWkdl())
                .setRemainDeliveryNum(templateDTO.getWthl())
                .setWashoutNum(templateDTO.getXdsl())
                .setWashoutDiffPrice(templateDTO.getXdcj())
                .setWashoutPrice(templateDTO.getXdscj())
                .setTransferTTPrice(templateDTO.getZyjc())
                .setOriginalWeightTolerance(templateDTO.getYos())
                .setOriginalTTUnitPrice(templateDTO.getXpr())
                .setOriginalContractNum(templateDTO.getSysl())
                .setWashoutDiffAmount(templateDTO.getXdze())
                .setJointAddedDepositRate(templateDTO.getBzjzj())
                .setComplaintDiscountPrice(templateDTO.getKszj())
                .setComplaintDiscountAmount(templateDTO.getKszjz())
                .setDelayPrice(templateDTO.getZqf())
                .setDelayAmount(templateDTO.getZqfz())
                .setBusinessPrice(templateDTO.getSxlx())
                .setSymbolFactory(templateDTO.getGqbz())
                .setCustomerContactAddresses(templateDTO.getAds())
                .setCustomerContactEmails(templateDTO.getEma())
                .setCustomerContactPhones(templateDTO.getMbo())
                .setLdcContactAddresses(templateDTO.getMads())
                .setLdcContactNames(templateDTO.getMfox())
                .setLdcContactEmails(templateDTO.getMema())
                .setLdcContactPhones(templateDTO.getMmbo())
                .setTotalAmount(templateDTO.getPrt())
                .setNoTaxTotalAmount(templateDTO.getNprt())
                .setAddedTaxTotalAmount(templateDTO.getSz())
                .setTaxRateModify(templateDTO.getTaxRateModify())
                .setTotalAmountModify(templateDTO.getTotalAmountModify())
                .setTotalAmountInfoModify(templateDTO.getTotalAmountInfoModify())
                .setNoTaxTotalAmountInfoModify(templateDTO.getNoTaxTotalAmountInfoModify())
                .setAddedTaxTotalAmountInfoModify(templateDTO.getAddedTaxTotalAmountInfoModify())
                .setCustomerBankName(templateDTO.getKhfk())
                .setCustomerBankAccountNo(templateDTO.getZhfk())
                .setCustomerBankAccountName(templateDTO.getKhmc())
                .setStructurePriceTotalNum(templateDTO.getJghzsl())
                .setStructurePriceStartTime(templateDTO.getJghkrq())
                .setPriceOrderTaxAmount(templateDTO.getDjprt())
                .setWeightedAverageTaxPrice(templateDTO.getLdjjghs())
                .setPriceOrderNoTaxAmount(templateDTO.getDjnprt())
                .setPriceOrderAddedTaxAmount(templateDTO.getDjsz())
                .setWeightedAverageUnitPrice(templateDTO.getLdjjg())
                .setWeightedAverageTaxAmount(templateDTO.getLprt())
                .setWeightedAverageNoTaxAmount(templateDTO.getLnprt())
                .setWeightedAverageAddedTaxAmount(templateDTO.getLsz())
                .setStructurePriceUnitNum(templateDTO.getJghdwl())
                .setTransferMonthTime(templateDTO.getJzlyfk())
                .setStructurePriceEndTime(templateDTO.getJghjrq())
                .setStructureTriggerPrice(templateDTO.getJghcf())
                .setStructureCumulativePrice(templateDTO.getJghlj())
                .setStructureUnitNum(templateDTO.getJghdwsl())
                .setStructureUnitIncrement(templateDTO.getJghdwzl())
                .setDepositText(templateDTO.getLytext())
                .setStructureCashReturn(templateDTO.getJghddxjfh())
                .setOriginalUnitPrice(templateDTO.getYpr())
                .setContractTitle(templateDTO.getXhd())
                .setDepositUseRuleName(templateDTO.getDepositUseRuleName())
                .setAboveDeadlineInfo(templateDTO.getAboveDeadlineInfo())
                .setPriceDeadlineText(templateDTO.getPriceDeadlineText())
                .setInvoicePaymentRateInfo(templateDTO.getInvoicePaymentRateInfo())
                .setInvoicePaymentRateInTurnInfo(templateDTO.getInvoicePaymentRateInTurnInfo())
                .setPriceEndTimeContains(templateDTO.getPriceEndTimeContains())
                .setSpecialCustomerDeliveryInfo(templateDTO.getSpecialCustomerDeliveryInfo())
                ;
    }
}
