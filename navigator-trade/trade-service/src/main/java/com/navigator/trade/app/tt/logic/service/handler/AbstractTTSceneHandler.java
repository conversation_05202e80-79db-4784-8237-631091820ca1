package com.navigator.trade.app.tt.logic.service.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ExchangeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.*;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.service.TTDomainService;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.app.tt.logic.service.handler.impl.TTLogicCheckHandler;
import com.navigator.trade.app.tt.logic.service.handler.impl.TTWarrantLogicCheckHandler;
import com.navigator.trade.dao.*;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.handler.SalesContractHandler;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTSubmitResultDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.*;
import com.navigator.trade.service.contract.ICancelContractModifyService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.impl.convert.ContractSignConvertUtil;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketDOConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
public abstract class AbstractTTSceneHandler implements ITTSceneHandler {

    /***********************DAO***********************/
    @Autowired
    protected TradeTicketDao tradeTicketDao;
    @Autowired
    protected TtAddDao ttAddDao;
    @Autowired
    protected TtPriceDao ttPriceDao;
    @Autowired
    protected TtModifyDao ttModifyDao;
    @Autowired
    protected TtTranferDao ttTranferDao;
    @Autowired
    protected ITTApproveHandler ttApproveHandler;
    @Autowired
    protected TTLogicCheckHandler ttLogicCheckHandler;
    @Autowired
    protected TTWarrantLogicCheckHandler ttWarrantLogicCheckHandler;


    /***********************Service & Handler***********************/
    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    protected ContractQueryLogicService contractQueryLogicService;

    @Autowired
    @Qualifier("TTApproveServiceImpl")
    protected ITTApproveService ttApproveService;

    @Autowired
    protected IContractOperationNewService salesContractOperationService;
    @Autowired
    protected ICancelContractModifyService cancelContractModifyService;
    @Autowired
    protected SalesContractSignHandler salesContractSignHandler;
    @Autowired
    protected SalesContractHandler salesContractHandler;
    @Autowired
    protected TTHandler ttHandler;
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected IContractSignQueryService iContractSignQueryService;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    protected ContractSignConvertUtil contractSignConvertUtil;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;
    @Autowired
    protected SiteFacade siteFacade;
    @Autowired
    protected CompanyFacade companyFacade;
    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected ITtTranferService ttTranferService;

    /***********************Facade***********************/
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected OperationLogFacade operationLogFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Resource
    protected CategoryFacade categoryFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected DepositRuleFacade depositRuleFacade;
    @Autowired
    protected PriceAllocateFacade priceAllocateFacade;
    @Autowired
    protected CustomerBankFacade customerBankFacade;
    @Autowired
    protected EmployFacade employFacade;
    @Autowired
    protected CustomerDepositRateFacade customerDepositRateFacade;
    @Autowired
    protected FileBusinessFacade fileBusinessFacade;
    @Autowired
    protected PriceApplyFacade priceApplyFacade;
    @Autowired
    protected QualityFacade qualityFacade;
    @Autowired
    protected PayConditionFacade payConditionFacade;
    @Autowired
    protected SkuFacade skuFacade;
    @Autowired
    protected ContractPriceFacade contractPriceFacade;
    @Autowired
    protected WarrantFacade warrantFacade;

    /***********************Other***********************/
    @Autowired
    protected SequenceUtil sequenceUtil;
    @Autowired
    protected TransactionTemplate transactionTemplate;
    @Autowired
    protected RedisUtil redisUtil;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected CodeGeneratorUtil codeGeneratorUtil;
    @Autowired
    protected ITradeTicketQueryService tradeTicketQueryService;
    @Autowired
    protected RedisService redisService;

    @Autowired
    protected TTDomainService ttDomainService;

    @Autowired
    protected TTQueryDomainService ttQueryDomainService;

    @Autowired
    protected TradeTicketDOConvert tradeTicketDOConvert;

    @Autowired
    protected WarehouseFacade warehouseFacade;
    @Autowired
    protected TradingConfigFacade tradingConfigFacade;

    List<Integer> ListDoubuleTTTypes = Lists.newArrayList(TTTypeEnum.SPLIT.getType(),
            TTTypeEnum.BUYBACK.getType(),
            TTTypeEnum.REVERSE_PRICE.getType(),
            TTTypeEnum.TRANSFER.getType());

    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return true;
    }


    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        return null;
    }

    @Override
    public TTSubmitResultDTO prepareContractCreateData(TTDTO ttdto) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();

        Result result = new Result<>();
        /**
         * 1、校验数据完整性
         * 2、校验数据逻辑
         * 3、填充数据
         */
        ttSubmitResultDTO = checkDataIntegrity(ttdto);
        if (!ttSubmitResultDTO.getCheckResult()) {
            return ttSubmitResultDTO;
        }

        ttSubmitResultDTO = checkDataLogic(ttdto);
        if (!ttSubmitResultDTO.getCheckResult()) {
            return ttSubmitResultDTO;
        }

        fillData(ttdto);

        return ttSubmitResultDTO;
    }


    @Override
    public TradeTicketDO saveTradeTicketDomainData(TradeTicketDO tradeTicketDO) {
        tradeTicketDO = ttDomainService.createTradeTicketDO2(tradeTicketDO);
        return tradeTicketDO;
    }

    /**
     * 初始化ttdto参数实体
     * (生成tt编号，合同编号等)
     *
     * @param ttdto
     * @return
     */
    public void initDTO(TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);
        String code = salesContractAddTTDTO.getCode();
        //初始化TradeType
        if (Objects.isNull(ttdto.getContractTradeType())) {
            Integer type = fetchTradeType(ttdto);
            ttdto.setContractTradeType(type);
            salesContractAddTTDTO.setTradeType(type);
        }
        //TT编号，合同编号
        //根据code存在与否, 判断是否为新增
        if (StringUtils.isBlank(code)) {
            salesContractAddTTDTO.setCreateStatus(true);
            //生成TT编号
            ContractSalesTypeEnum salesTypeEnum = ContractSalesTypeEnum.getByValue(salesContractAddTTDTO.getSalesType());
            code = CodeGeneratorUtil.genTTNewCodeBySaleType(salesTypeEnum);
            salesContractAddTTDTO.setCode(code);
            if (ObjectUtil.isNotEmpty(salesContractAddTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractAddTTDTO.getSiteCode());
                CompanyEntity companyEntity = companyFacade.queryCompanyById(siteEntity.getCompanyId());
                String contractCode = contractQueryLogicService.genNewContractCode(companyEntity.getShortName(),
                        salesTypeEnum.getValue(), salesContractAddTTDTO.getGoodsCategoryId());
                salesContractAddTTDTO.setContractCode(contractCode);
            }

            // 仓单合同的编号特殊处理 出现TT协议同步不一致 add zengshl 这边迁移到这边实现
            if (BuCodeEnum.WT.getValue().equals(salesContractAddTTDTO.getBuCode())) {
                TradingConfigVO tradingConfigVO = tradingConfigFacade.getDomainTypeByCategoryCode(salesContractAddTTDTO.getFutureCode());
                salesContractAddTTDTO.setExchangeCode(null == tradingConfigVO ? ExchangeEnum.DCE.getValue() : tradingConfigVO.getExchange());
                if (ContractSalesTypeEnum.SALES.getValue() == salesContractAddTTDTO.getSalesType()) {
                    salesContractAddTTDTO.setContractCode(
                            CodeGeneratorUtil.genContractNewCode(salesContractAddTTDTO.getExchangeCode(), salesContractAddTTDTO.getFutureCode(), ContractSalesTypeEnum.SALES));
                } else {
                    salesContractAddTTDTO.setContractCode(
                            CodeGeneratorUtil.genContractNewCode(salesContractAddTTDTO.getExchangeCode(), salesContractAddTTDTO.getFutureCode(), ContractSalesTypeEnum.PURCHASE));
                }
            }

        } else {
            salesContractAddTTDTO.setCreateStatus(false);
            //设置ttId
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(salesContractAddTTDTO.getCode());
            if (tradeTicketEntity == null) {
                throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
            }
            Integer ttId = tradeTicketEntity.getId();
            salesContractAddTTDTO.setTtId(ttId);
        }
    }


    /**
     * 生成groupId
     *
     * @param ttdto
     * @return
     */
    protected String generateGroupId(TTDTO ttdto) {
        if (ListDoubuleTTTypes.contains(ttdto.getSubmitType())
                || TTTypeEnum.REVISE.getType().equals(ttdto.getSubmitType()) && ttdto.getReviseCustomerType() != null && ttdto.getReviseCustomerType()) {
            //生成唯一id绑定两个TT
            return UUID.randomUUID().toString().replaceAll("-", "");
        }
        return null;
    }

    /**
     * 记录日志
     *
     * @param paramDTO
     * @param bizCodeEnum
     * @param ttId
     * @param logLevel
     */
    @Async
    public void recordTTQuery(String paramDTO, LogBizCodeEnum bizCodeEnum, Object ttId, Integer logLevel) {
        String userId = JwtUtils.getCurrentUserId();
        String code = null;
        if (ttId != null) {
            code = tradeTicketDao.getTradeTicketEntityById((Integer) ttId).getCode();
        }
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getBizCode())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(logLevel)
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(userId))
                .setOperationName(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getMsg())
                .setMetaData(paramDTO)
                .setData(paramDTO)
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId((Integer) ttId)
                .setReferBizCode(code);

        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId) {
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        TTDetailVO ttDetailVO = queryTTDetail(ttId);
        // TT详情后续设置
        queryTTDetailAfter(tradeTicketEntity, ttDetailVO);
        return ttDetailVO;
    }

    public abstract TTDetailVO queryTTDetail(Integer ttId);

    /**
     * TT详情后续设置
     *
     * @param tradeTicketEntity
     * @param ttDetailVO
     */
    protected void queryTTDetailAfter(TradeTicketEntity tradeTicketEntity, TTDetailVO ttDetailVO) {
        ttDetailVO.setTtStatus(TTStatusEnum.getDescByValue(tradeTicketEntity.getStatus()))
                .setTtType(tradeTicketEntity.getType())
                .setTradeType(tradeTicketEntity.getTradeType())
                .setTradeTypeInfo(ContractTradeTypeEnum.getDescByValue(tradeTicketEntity.getTradeType()));
        CategoryEntity category2 = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
        CategoryEntity category3 = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());
        ttDetailVO.setCategory2Name(null == category2 ? "" : category2.getName())
                .setCategory3Name(null == category3 ? "" : category3.getName());
        //查询协议状态
        if (TTStatusEnum.WAITING.getType() == tradeTicketEntity.getStatus()) {
            //待修改提交区的协议为异常状态
            ttDetailVO.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
        } else {
            ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(tradeTicketEntity.getId());
            if (contractSignDetail != null) {
                Integer status = contractSignDetail.getStatus();
                ttDetailVO.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());
            }
        }
        if (null != ttDetailVO.getTtQueryDetailVO()) {
            SiteEntity siteEntity = siteFacade.getSiteByCode(tradeTicketEntity.getSiteCode());
            if (null != siteEntity) {
                ttDetailVO.getTtQueryDetailVO().setSiteName(siteEntity.getName());
                ttDetailVO.getTtQueryDetailVO().setSyncSystem(siteEntity.getSyncSystem());
            }
        }
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());
        if (contractEntity != null) {
            Integer status = contractEntity.getStatus();
            ttDetailVO.setContractStatus(ContractStatusEnum.getDescByValue(status));
        }
        // 付款条件代码
        Result payConditionResult = payConditionFacade.getPayConditionById(tradeTicketEntity.getPayConditionId());
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            ttDetailVO.setPayConditionCode(payConditionEntity.getCode());
        }
        // 兼容处理
        if (null == ttDetailVO.getTtQueryDetailVO()) {
            TTQueryDetailVO detailVO = BeanConvertUtils.map(TTQueryDetailVO.class, tradeTicketEntity);
            detailVO.setTtId(tradeTicketEntity.getId());
            ttDetailVO.setTtQueryDetailVO(detailVO);
            if (null != contractEntity) {
                detailVO.setTotalDeliveryNum(contractEntity.getTotalDeliveryNum());
            }
        }

        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(tradeTicketEntity.getId()));
        recordTTQuery(data, ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? LogBizCodeEnum.QUERY_SALES_TT : LogBizCodeEnum.QUERY_PURCHASE_TT,
                tradeTicketEntity.getId(), OperationSourceEnum.SYSTEM.getValue());
    }


    /**
     * 获取交易类型
     *
     * @param ttdto
     * @return
     */
    protected Integer fetchTradeType(TTDTO ttdto) {
        TTTypeEnum ttType = TTTypeEnum.getByType(ttdto.getTtType());
        switch (ttType) {
            case ALLOCATE:
                return ContractTradeTypeEnum.ALLOCATE.getValue();
            case ASSIGN:
                return ContractTradeTypeEnum.ASSIGN.getValue();
            // ... todo
            default:
                return ContractTradeTypeEnum.NEW.getValue();
        }
    }

    public List<String> getManualList() {
        List<String> manualList = new ArrayList<>();
        Field[] fields = SalesContractReviseTTDTO.class.getDeclaredFields();
        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
        manualList.remove("fobUnitPrice");
        manualList.remove("taxRate");
        manualList.remove("cifUnitPrice");
        manualList.remove("totalAmount");
        manualList.remove("unitPrice");
        return manualList;
    }

    public List<String> getIgnoreList() {
        List<String> ignoreList = new ArrayList<>();
//        ignoreList.add("tradeType");
        ignoreList.add("salesType");
        ignoreList.add("contractSource");
        ignoreList.add("contractSignatureStatus");
        ignoreList.add("status");
        ignoreList.add("approvalStatus");
        ignoreList.add("approvalType");
        ignoreList.add("code");
        ignoreList.add("rootContractId");

        ignoreList.add("modifyContent");
        ignoreList.add("content");
        ignoreList.add("contractId");

        ignoreList.add("diffPrice");
        ignoreList.add("type");
        ignoreList.add("tempPrice");
        ignoreList.add("originalDomainCode");

        ignoreList.add("sourceContractId");
        return ignoreList;
    }

    private TTSubmitResultDTO checkDataLogic(TTDTO ttDto) {
        return null;
    }

    private TTSubmitResultDTO checkDataIntegrity(TTDTO ttDto) {
        return null;
    }

    private void fillData(TTDTO ttdto) {

    }
}
