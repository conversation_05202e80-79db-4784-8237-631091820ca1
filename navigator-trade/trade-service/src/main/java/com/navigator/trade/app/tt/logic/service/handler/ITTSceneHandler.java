package com.navigator.trade.app.tt.logic.service.handler;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.tradeticket.SubmitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTSubmitResultDTO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TT业务场景处理器
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
public interface ITTSceneHandler {

    /**
     * 保存
     * 具体场景处理逻辑
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext);

    TradeTicketDO saveTradeTicketDomainData(TradeTicketDO tradeTicketDO);

    /**
     * 查询TT详情
     * @param ttId
     * @return
     */
    TTDetailVO queryDetail(Integer ttId);

    TTSubmitResultDTO prepareContractCreateData(TTDTO ttdto);
}
