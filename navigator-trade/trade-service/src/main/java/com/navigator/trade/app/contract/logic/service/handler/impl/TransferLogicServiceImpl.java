package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.navigator.bisiness.enums.*;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.TransferLogicService;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 转月、反点价的Logic 逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TransferLogicServiceImpl implements TransferLogicService {

    @Autowired
    private ContractQueryDomainService contractQueryDomainService;

    @Autowired
    private CustomerFacade customerFacade;

    @Autowired
    private DeliveryTypeFacade deliveryTypeFacade;

    @Autowired
    private CommonLogicService commonLogicService;

    @Autowired
    private ContractDomainService contractDomainService;

    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public void reversePriceCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        // 校验客户状态
        CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getCustomerId());

        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // N-K nav头寸处理改造 changed by Mr at 2025-07-24 start
        // 校验合同状态
        if (ContractStatusEnum.EFFECTIVE.getValue() != contractEntity.getStatus()) {
            // 如果合同处于修改中，判断是否因为反点价而修改的
            if (ContractStatusEnum.MODIFYING.getValue() == contractEntity.getStatus()) {
                if (contractEntity.getIsReversePrice() == null) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_STATUS_EXCEPTION);
                }
            } else {
                // 合同状态既不是“生效”也不是“修改中”，直接不允许继续
                throw new BusinessException(ResultCodeEnum.CONTRACT_STATUS_EXCEPTION);
            }
        }
        // N-K nav头寸处理改造 changed by Mr at 2025-07-24 end

        // 数量校验
        if (contractTransferDTO.getReversePricingNum().compareTo(contractEntity.getContractNum()) > 0) {
            throw new BusinessException("合同数量不够!无法进行反点价");
        }
    }

    @Override
    public void transferMonthCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        if (contractEntity.getContractNum().compareTo(contractTransferDTO.getAllocateNum()) < 0) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_INSUFFICIENT);
        }
    }

    @Override
    public void buildBaseInfo(ContractEntity parentContractEntity, ContractEntity childContractEntity, ContractTransferDTO contractTransferDTO) {
        try {
            BeanUtils.copyProperties(parentContractEntity, childContractEntity);
            childContractEntity.setParentId(parentContractEntity.getId());
            childContractEntity.setRootId(parentContractEntity.getParentId() == 0 ? parentContractEntity.getId() : parentContractEntity.getParentId());

            String sonContractCode = contractQueryDomainService.genSonContractCode(parentContractEntity.getContractCode(), parentContractEntity.getSalesType());

            childContractEntity.setId(null)
                    .setUuid(IdUtil.simpleUUID())
                    .setContractCode(sonContractCode)
                    .setLinkinageCode(sonContractCode)
                    .setRepeatContractCode(sonContractCode)
                    .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setSignDate(new Date());

            Integer contractSource = null;
            Integer tradeType = null;
            BigDecimal orderNum = null;
            BigDecimal contractNum = null;
            Integer contractType = parentContractEntity.getContractType();

            // add by zengshl 【仓单】|【交易所】【交易平台】
            if (BuCodeEnum.WT.getValue().equals(childContractEntity.getBuCode())
                    && !(WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(childContractEntity.getWarrantTradeType()))) {
                childContractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
            }

            if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
                contractSource = ContractActionEnum.TRANSFER_CONFIRM.getActionValue();
                tradeType = ContractTradeTypeEnum.TRANSFER_PART.getValue();
                orderNum = contractTransferDTO.getAllocateNum();
                contractNum = contractTransferDTO.getAllocateNum();
            } else if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue())) {
                contractType = ContractTypeEnum.JI_CHA.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue();
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue();
                orderNum = contractTransferDTO.getReversePricingNum();
                contractNum = contractTransferDTO.getReversePricingNum();
            } else if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())) {
                contractType = ContractTypeEnum.JI_CHA.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue();
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue();
                orderNum = contractTransferDTO.getReversePricingNum();
                contractNum = contractTransferDTO.getReversePricingNum();
            }

            if (contractSource != null && orderNum != null) {
                childContractEntity
                        .setContractType(contractType)
                        .setContractSource(contractSource)
                        .setTradeType(tradeType)
                        .setOrderNum(orderNum)
                        .setContractNum(contractNum)
                        .setTotalDeliveryNum(BigDecimal.ZERO)
                        .setTotalPriceNum(BigDecimal.ZERO)
                        .setTotalModifyNum(BigDecimal.ZERO)
                        .setTotalTransferNum(BigDecimal.ZERO)
                        .setCreateSource(SystemEnum.MAGELLAN.getValue());
            }

            // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
            // Inherit group_id from parent contract for transfer/reverse pricing operations
            if (parentContractEntity.getGroupId() != null) {
                childContractEntity.setGroupId(parentContractEntity.getGroupId());
            }
            // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

        } catch (Exception e) {
            log.error("Error building base info for child contract: {}", e.getMessage(), e);
            throw new BusinessException(ResultCodeEnum.FAILURE, "Failed to build base info for child contract");
        }
    }


    @Override
    public void buildBizInfo(ContractTransferDTO contractTransferDTO, ContractEntity parentContractEntity, ContractEntity childContractEntity, ArrangeContext arrangeContext) {
        // 客户信息
        updateCustomerInfo(childContractEntity.getCustomerId(),
                childContractEntity.getSupplierId(),
                childContractEntity.getCustomerId(),
                childContractEntity.getSupplierId(),
                childContractEntity);

        // 价格计算
        processContractPrice(contractTransferDTO, parentContractEntity, childContractEntity, arrangeContext);

        // 扣减反点价次数
        childContractEntity = processContractTransferTimes(contractTransferDTO, parentContractEntity, childContractEntity);

        String domainCode = contractTransferDTO.getTransferDominantCode() != null ? contractTransferDTO.getTransferDominantCode() : parentContractEntity.getDomainCode();

        // 点价截止日期
        processPriceEndTime(childContractEntity, childContractEntity.getDeliveryStartTime(), domainCode);

        // 期货部分
        childContractEntity
                .setDomainCode(domainCode)
                .setFutureCode(contractTransferDTO.getTransferFutureCode() != null ? contractTransferDTO.getTransferFutureCode() : parentContractEntity.getFutureCode());
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(childContractEntity.getCategory2())
                .setContractType(childContractEntity.getContractType())
                .setDepositRate(childContractEntity.getDepositRate())
                .setAddedDepositRate(childContractEntity.getAddedDepositRate());
        childContractEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09
        // 清除子合同的定价完成状态
        childContractEntity.setIsPricingCompleted(null);

        // N-K nav头寸处理改造 changed by Mr at 2025-07-24 start
        // 清除子合同的反点价状态
        childContractEntity.setIsReversePrice(null);
        // N-K nav头寸处理改造 changed by Mr at 2025-07-24 end

    }

    @Override
    public void createChildContract(ContractTransferDTO contractTransferDTO, ContractEntity childContractEntity) {
        try {
            contractDomainService.saveContract(childContractEntity);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", childContractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
    }

    @Override
    public void updateTransferFatherContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, List<TTPriceEntity> ttPriceEntityList) {
        TTTranferTypeEnum transferTypeEnum = TTTranferTypeEnum.getByValue(contractTransferDTO.getTtTranferType());

        switch (transferTypeEnum) {
            case PART_TRANSFER_MONTH:
                // 初始转月量
                BigDecimal oldTotalTransferNum = contractEntity.getTotalTransferNum();
                // 新的点价量
                BigDecimal newTotalTransferNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalTransferNum, contractTransferDTO.getAllocateNum());
                BigDecimal newContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), contractTransferDTO.getAllocateNum());
                // 转月导致的原合同全部定价
                BigDecimal totalPrice = BigDecimal.ZERO;
                BigDecimal totalNum = BigDecimal.ZERO;
                for (TTPriceEntity ttPriceEntity : ttPriceEntityList) {
                    totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                    totalNum = totalNum.add(ttPriceEntity.getNum());
                }

                if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                    // 加权平均价
                    BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                    log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                    // 更新期货价格
                    contractDomainService.updateContractForwardPrice(contractEntity, averagePrice);
                }

                contractEntity
                        .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getUnitPrice(), newContractNum))
                        .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                        .setTotalTransferNum(newTotalTransferNum)
                        .setOrderNum(newContractNum)
                        .setContractNum(newContractNum);
                break;
            case TRANSFER_MONTH:
                // 重新计算价格
                // processContractPrice(contractTransferDTO, contractEntity, null, null);

                // 定价截止日期
                processPriceEndTime(contractEntity, contractEntity.getDeliveryStartTime(), contractTransferDTO.getDomainCode());

                // 扣除父合同的转月次数
                contractEntity = processContractTransferTimes(contractTransferDTO, contractEntity, null);

                // 更新合同信息-新的转入合约信息
                contractEntity
                        .setTotalTransferNum(BigDecimal.ZERO)
                        .setFutureCode(contractTransferDTO.getTransferFutureCode())
                        .setDomainCode(contractTransferDTO.getDomainCode())
                        .setStatus(ContractStatusEnum.MODIFYING.getValue())
                        .setTradeType(ContractTradeTypeEnum.TRANSFER_ALL.getValue())
                        .setUpdatedAt(DateTimeUtil.now());
                break;
            case PART_REVERSE_PRICING:
                BigDecimal oldUnitPrice = contractEntity.getUnitPrice();
                BigDecimal oldContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), contractTransferDTO.getReversePricingNum());
                //BigDecimal totalPriceNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getTotalPriceNum(), priceDealDetailDTO.getDealNum());
                BigDecimal newTotalAmount = oldUnitPrice.multiply(oldContractNum);
                contractEntity.setTotalAmount(newTotalAmount)
                        .setContractNum(oldContractNum)
                        .setOrderNum(oldContractNum)
                        //.setTotalPriceNum(totalPriceNum)
                        .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
                break;
            case REVERSE_PRICING:
                contractEntity.setTotalAmount(BigDecimal.ZERO)
                        .setDepositAmount(BigDecimal.ZERO)
                        .setOrderNum(BigDecimal.ZERO)
                        .setContractNum(BigDecimal.ZERO);
                break;
            default:
                break;
        }

        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        // add by zengshl 【仓单】|【交易所】【交易平台】
        if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())
                && !(WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType()))) {
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
        }
        contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void processTransferMonthPrice(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext) {
        processContractPrice(contractTransferDTO, contractEntity, null, arrangeContext);
    }

    /**
     * 更新客户信息
     *
     * @param newCustomerId    新客户ID
     * @param newSupplierId    新供应商ID
     * @param originCustomerId 旧客户ID
     * @param originSupplierId 旧供应商ID
     * @param contractEntity   合同实体
     */
    private void updateCustomerInfo(Integer originCustomerId, Integer originSupplierId,
                                    Integer newCustomerId, Integer newSupplierId, ContractEntity contractEntity) {
        // 默认父合同客户信息
        contractEntity.setCustomerId(originCustomerId);
        contractEntity.setSupplierId(originSupplierId);

        // 更新Customer
        if (null != newCustomerId) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(newCustomerId);
            contractEntity.setCustomerId(customerDTO.getId())
                    .setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName())
                    .setCustomerStatus(customerDTO.getStatus());
        }

        // 更新Supplier
        if (null != newSupplierId) {
            CustomerDTO supplierDTO = customerFacade.getCustomerById(newSupplierId);
            contractEntity.setSupplierId(supplierDTO.getId())
                    .setSupplierName(supplierDTO.getName());
        }
    }

    /**
     * 点价截止日期
     *
     * @param contractEntity    合同实体
     * @param deliveryStartTime 交货开始时间
     * @return
     */
    private void processPriceEndTime(ContractEntity contractEntity, Date deliveryStartTime, String domainCode) {
        Integer priceEndType = null;
        String priceEndTime = null;
        // 开始交货日>期货合约
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        String deliveryTime = DateTimeUtil.formatDateValue(deliveryStartTime).substring(2, 6);
        if (deliveryTime.compareTo(domainCode) < 0) {
            if (null != deliveryTypeEntity) {
                if (deliveryTypeEntity.getType().equals(DeliveryModeEnum.TAKE.getValue())) {
                    priceEndType = 2;
                    priceEndTime = "提货";
                } else if (deliveryTypeEntity.getType().equals(DeliveryModeEnum.SEND.getValue())) {
                    priceEndType = 2;
                    priceEndTime = "发货";
                }
            }
        } else {
            // 点价截止日期”显示为“期货合约”前一个月的20号
            priceEndType = 1;
            priceEndTime = DateTimeUtil.calculatePriceEndTime(domainCode);
        }

        contractEntity.setPriceEndType(priceEndType).setPriceEndTime(priceEndTime);
    }

    /**
     * 更新转月、转月反点价次数
     * 不考虑总次数变化的情况 总次数变化在更改客户的白名单的时候会更新
     *
     * @param contractTransferDTO  转月、转月反点价参数
     * @param parentContractEntity 父合同实体
     * @param childContractEntity  子合同实体
     */
    private ContractEntity processContractTransferTimes(ContractTransferDTO contractTransferDTO, ContractEntity parentContractEntity, ContractEntity childContractEntity) {

        ContractEntity contractEntity = null != childContractEntity ? childContractEntity : parentContractEntity;

        // 只计算销售合同的转月、反点价次数
        if (parentContractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            return contractEntity;
        }

        int totalTransferTimes = parentContractEntity.getTotalTransferTimes();
        int ableTransferTimes = parentContractEntity.getAbleTransferTimes();
        int transferredTimes = parentContractEntity.getTransferredTimes();
        int totalReversePriceTimes = parentContractEntity.getTotalReversePriceTimes();
        int ableReversePriceTimes = parentContractEntity.getAbleReversePriceTimes();
        int reversedPriceTimes = parentContractEntity.getReversedPriceTimes();

        // 根据不同场景判断
        TTTranferTypeEnum typeEnum = TTTranferTypeEnum.getByValue(contractTransferDTO.getTtTranferType());
        switch (typeEnum) {
            case TRANSFER_MONTH:
            case PART_TRANSFER_MONTH:
                transferredTimes = parentContractEntity.getTransferredTimes() + 1;
                ableTransferTimes = Math.max(totalTransferTimes - transferredTimes, 0);
                break;
            case PART_REVERSE_PRICING:
            case REVERSE_PRICING:
                // 反点价次数
                reversedPriceTimes = parentContractEntity.getReversedPriceTimes() + 1;
                ableReversePriceTimes = Math.max(totalReversePriceTimes - reversedPriceTimes, 0);

                // 是否转月
                if (null != contractTransferDTO.getTransferDominantCode() &&
                        !contractTransferDTO.getTransferDominantCode().equals(parentContractEntity.getDomainCode())) {
                    transferredTimes = parentContractEntity.getTransferredTimes() + 1;
                    ableTransferTimes = Math.max(totalTransferTimes - transferredTimes, 0);
                }
                break;
            default:
                break;
        }

        return contractEntity.setTotalTransferTimes(totalTransferTimes)
                .setAbleTransferTimes(ableTransferTimes)
                .setTransferredTimes(transferredTimes)
                .setTotalReversePriceTimes(totalReversePriceTimes)
                .setAbleReversePriceTimes(ableReversePriceTimes)
                .setReversedPriceTimes(reversedPriceTimes);
    }

    /**
     * 处理转月、反点价合同价格
     *
     * @param contractTransferDTO  反点价参数
     * @param parentContractEntity 父合同实体
     * @param childContractEntity  子合同实体
     * @param arrangeContext
     */
    private void processContractPrice(ContractTransferDTO contractTransferDTO, ContractEntity parentContractEntity, ContractEntity childContractEntity, ArrangeContext arrangeContext) {
        ContractPriceEntity contractPriceEntity = contractQueryDomainService.getContractPriceByContractId(parentContractEntity.getId());
        PriceDetailBO priceDetailBO = BeanUtil.toBean(contractPriceEntity, PriceDetailBO.class);

        BigDecimal newExtraPrice;
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            // 基差价(新的基差价=原基差价+转月价差)
            newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, contractPriceEntity.getExtraPrice(), contractTransferDTO.getTransactionDiffPrice());
        } else if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())) {
            //基差价(新的基差价=原基差价+转月价差)
            newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, contractPriceEntity.getExtraPrice(), contractTransferDTO.getTransactionDiffPrice());
        } else {
            // 基差价(原合同的基差价+（原合同期货价格-反点价成交的期货价格）)
            BigDecimal temp = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, contractPriceEntity.getForwardPrice(), contractTransferDTO.getTransactionPrice());
            newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, contractPriceEntity.getExtraPrice(), temp);
        }
        priceDetailBO.setExtraPrice(newExtraPrice);
        // 合同基差价
        if (childContractEntity != null) {
            childContractEntity.setExtraPrice(newExtraPrice);
        }

        // 手续费
        BigDecimal originalFee = null != priceDetailBO.getFee() ? priceDetailBO.getFee() : BigDecimal.ZERO;

        // 获取手续费
        BigDecimal fee = getContractFee(parentContractEntity, contractTransferDTO);
        //本次手续费
        contractTransferDTO.setFee(fee);

        BigDecimal newFee = originalFee.add(fee);
        priceDetailBO.setFee(newFee);
        // 总价
        BigDecimal deliveryPrice = contractPriceEntity.getTransportPrice()
                .add(contractPriceEntity.getLiftingPrice())
                .add(contractPriceEntity.getDelayPrice())
                .add(contractPriceEntity.getTemperaturePrice())
                .add(contractPriceEntity.getOtherDeliveryPrice());
        BigDecimal taxRate = parentContractEntity.getTaxRate();

        priceDetailBO.setForwardPrice(contractTransferDTO.getLatestForwardPrice());
        BigDecimal unitPrice = contractDomainService.calculatePriceDetailBO(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())) {
            BigDecimal totalAmount = unitPrice.multiply(parentContractEntity.getContractNum());
            parentContractEntity.setTotalAmount(totalAmount)
                    .setOrderAmount(totalAmount)
                    .setCifUnitPrice(cifUnitPrice)
                    .setFobUnitPrice(fobUnitPrice)
                    .setUnitPrice(unitPrice)
                    .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(parentContractEntity.getDepositRate() * 0.01)));
            // BUGFIX：case-1003035 合同转月后，N006-销售TT界面的基差未更新 Author: Mr 2025-03-18
            parentContractEntity.setExtraPrice(newExtraPrice);
            if (arrangeContext != null) {
                // 记录本次的手续费-TT使用
                arrangeContext.setThisTimeFee(fee);
                // 记录价格详情-TT使用
                arrangeContext.setPriceDetailBO(priceDetailBO);
            }
        } else {
            BigDecimal totalAmount = unitPrice.multiply(childContractEntity.getContractNum());
            childContractEntity.setTotalAmount(totalAmount)
                    .setOrderAmount(totalAmount)
                    .setCifUnitPrice(cifUnitPrice)
                    .setFobUnitPrice(fobUnitPrice)
                    .setUnitPrice(unitPrice)
                    .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(childContractEntity.getDepositRate() * 0.01)));

            // 记录本次的手续费-TT使用
            arrangeContext.setThisTimeFee(fee);
            // 记录价格详情-TT使用
            arrangeContext.setPriceDetailBO(priceDetailBO);
        }
    }

    /**
     * 获取合同手续费
     * 手续费特油与豆油一致，特种蛋白与豆粕一致，仓单合同与现货一致。
     *
     * @param contractEntity      父合同实体
     * @param contractTransferDTO 转月、转月反点价参数
     * @return 合同手续费
     */
    private BigDecimal getContractFee(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        // 采购合同手续费输入
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            return contractTransferDTO.getFee() == null ? BigDecimal.ZERO : contractTransferDTO.getFee();
        }
        // 需求优化： Case-1002988反点价手续费计算不对 Author: NaNa 2025-02-25 start
        //销售合同转月/反点价，手续费计算
        BigDecimal thisTimeFee;
        if (Arrays.asList(TTTranferTypeEnum.TRANSFER_MONTH.getValue(),
                TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()).contains(contractTransferDTO.getTtTranferType())) {
            // 转月手续费计算
            thisTimeFee = this.getTransferFee(contractEntity, contractTransferDTO);
        } else {
            // 反点价手续费计算
            thisTimeFee = this.getReversePriceFee(contractEntity, contractTransferDTO);
        }
        // 需求优化： Case-1002988反点价手续费计算不对 Author: NaNa 2025-02-25 end
        return thisTimeFee;
    }


    // 需求优化： Case-1002988反点价手续费计算不对 Author: NaNa 2025-02-25 start

    /**
     * 计算转月手续费（反点价且转月也用复用该方法）
     *
     * @param contractEntity
     * @param contractTransferDTO
     * @return
     */
    private BigDecimal getTransferFee(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        // 销售合同-转月手续费计算
        int transferTimes = contractEntity.getTransferredTimes() + 1;
        BigDecimal fee = BigDecimal.ZERO;
        // 豆二没有反点价场景，豆二仓单合同转月记录手续费，手续费规则3、3、6、6
        if (contractEntity.getBuCode().equals(BuCodeEnum.WT.getValue()) && contractEntity.getIsSoybean2() == 1 &&
                Arrays.asList(TTTranferTypeEnum.TRANSFER_MONTH.getValue(), TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()).contains(contractTransferDTO.getTtTranferType())) {
            fee = transferTimes > 2 ? new BigDecimal("6") : new BigDecimal("3");
            log.info("1、豆二合同{}转月=================================,第{}次转月，当此转月手续费为：{}", contractEntity.getContractCode(), transferTimes, fee);
            return fee;
        }
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());
        if (categoryEntity != null) {
            // 转月手续费: 豆粕/特种蛋白:转月1/1/2/2(元/吨/次)，次数超过4次按照第四次的手续费累加
            if (Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getDesc(), GoodsCategoryEnum.SPECIFIC_PROTEIN.getDesc()).contains(categoryEntity.getName())) {
                fee = transferTimes > 2 ? new BigDecimal("2") : new BigDecimal("1");
            }
            // 转月手续费:豆油/特种油脂:转月2/2/4/4(元/吨/次)，次数超过4次按照第四次的手续费累加
            if (Arrays.asList(GoodsCategoryEnum.OSM_OIL.getDesc(), GoodsCategoryEnum.SPECIAL_OIL.getDesc()).contains(categoryEntity.getName())) {
                fee = transferTimes > 2 ? new BigDecimal("4") : new BigDecimal("2");
            }
        }
        log.info("1、合同{}转月=================================,第{}次转月，当次转月手续费为：{}", categoryEntity.getName() + contractEntity.getContractCode(), transferTimes, fee);
        return fee;
    }

    /**
     * 计算反点价手续费
     *
     * @param contractEntity
     * @param contractTransferDTO
     * @return
     */
    private BigDecimal getReversePriceFee(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO) {
        // 销售合同-反点价手续费计算
        int reversedPriceTimes = contractEntity.getReversedPriceTimes() + 1;
        BigDecimal fee = BigDecimal.ZERO;
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());
        if (categoryEntity != null) {
            // 反点价手续费:豆粕/特种蛋白:反点价1/1/2/2(元/吨/次)，次数超过4次按照第四次的手续费累加
            if (Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getDesc(), GoodsCategoryEnum.SPECIFIC_PROTEIN.getDesc()).contains(categoryEntity.getName())) {
                fee = reversedPriceTimes > 2 ? new BigDecimal("2") : new BigDecimal("1");
            }
            // 反点价手续费:豆油/特种油脂:反点价2/2/4/4(元/吨/次)，次数超过4次按照第四次的手续费累加
            if (Arrays.asList(GoodsCategoryEnum.OSM_OIL.getDesc(), GoodsCategoryEnum.SPECIAL_OIL.getDesc()).contains(categoryEntity.getName())) {
                fee = reversedPriceTimes > 2 ? new BigDecimal("4") : new BigDecimal("2");
            }
            if (null != contractTransferDTO.getTransferDominantCode() && !contractTransferDTO.getTransferDominantCode().equals(contractEntity.getDomainCode())) {
                //反点价同时转月，转月手续费计算
                BigDecimal transferFee = getTransferFee(contractEntity, contractTransferDTO);
                log.info("2、合同{}反点价且转月=================================,第{}次反点价，当次反点价手续费为：{}，转月手续费为：{}", categoryEntity.getName() + contractEntity.getContractCode(), reversedPriceTimes, fee, transferFee);
                // 反点价+转月的手续费逻辑改为两者取大
                // 比如 豆油_反点价第一次(手续费2)+转月第三次(手续费4)则本次反点价+转月的手续费为MAX(2,4)
                if (BigDecimalUtil.isGreaterOrEqual(transferFee, fee)) {
                    fee = transferFee;
                }
            }
            log.info("3、合同{}反点价=================================,第{}次反点价，当次最终反点价手续费为：{}", categoryEntity.getName() + contractEntity.getContractCode(), reversedPriceTimes, fee);
        }
        return fee;
    }
    // 需求优化： Case-1002988反点价手续费计算不对 Author: NaNa 2025-02-25 end
}
