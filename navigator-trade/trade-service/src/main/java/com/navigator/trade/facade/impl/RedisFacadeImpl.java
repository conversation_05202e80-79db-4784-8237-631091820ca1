package com.navigator.trade.facade.impl;


import com.navigator.common.dto.Result;
import com.navigator.trade.facade.RedisFacade;
import com.navigator.trade.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class RedisFacadeImpl implements RedisFacade {
    @Autowired
    private RedisService redisService;
    @Override
    public Result saveRedis(String version) {
        return redisService.saveRedis(version);
    }

    @Override
    public Result reloadRedis(String version) {
        return redisService.reloadRedis(version);
    }

    @Override
    public Result updateRedis(String key, String value) {
        return redisService.updateRedis(key,value);
    }

    @Override
    public Result getLockValue(String key) {
        return redisService.getLockValue(key);
    }

    @Override
    public Result insertRedis(String key) {
        return redisService.insertRedis(key);
    }

    @Override
    public Result rollBackRedis(String key) {
        return redisService.rollBackRedis(key);
    }

    @Override
    public Result getByRedisKey(String key) {
        return redisService.getByRedisKey(key);
    }
}
