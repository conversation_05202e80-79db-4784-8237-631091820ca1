package com.navigator.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.*;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.pigeon.pojo.entity.LkgContractInfoEntity;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.dao.ContractVODao;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.ContractPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.*;
import com.navigator.trade.service.*;
import com.navigator.trade.service.contract.IContractChangeEquityService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.navigator.common.constant.RedisConstants.CONTRACT_SAVE_TT_TIMES;

/**
 * <p>
 * 合同表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Slf4j
@Service
@RefreshScope
public class ContractQueryServiceImpl implements IContractQueryService {

    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private FileBusinessFacade fileBusinessFacade;
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private PriceAllocateFacade priceAllocateFacade;
    @Autowired
    private ContractDao contractDao;
    @Autowired
    private ContractVODao contractVODao;
    @Resource
    private IContractStructureService contractStructureService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ITtPriceService ttPriceService;
    @Resource
    private DeliveryTypeFacade deliveryTypeFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private IContractValueObjectService contractValueObjectService;
    @Autowired
    private IContractSignQueryService iContractSignQueryService;
    @Autowired
    private FuturesDomainFacade futuresDomainFacade;
    @Autowired
    private IContractHistoryService contractHistoryService;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private StructureRuleFacade structureRuleFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private GoodsFacade goodsFacade;
    @Resource
    private IContractChangeEquityService contractChangeEquityService;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;

    @Value("${delivery.nonDeliverableFactory:''}")
    private String cannotDeliveryFactoryList;

    @Value("${delivery.queryType:3}")
    private Integer deliveryQueryType;

    @Autowired
    private TTHandler TTHandler;

    @Override
    public String genNewContractCode(String companyName, Integer salesType, Integer goodsCategoryId) {
        // 商品品类
        // String goodsCategory = GoodsCategoryEnum.getByValue(goodsCategoryId).getCode();
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(goodsCategoryId);
        String goodsCategory = categoryEntity.getCode();
        // 生成递增序列号
        long sequence = getContractSequence(salesType, goodsCategoryId, companyName);

        return CodeGeneratorUtil.genNewContractCode(companyName, goodsCategory,
                ContractSalesTypeEnum.getByValue(salesType).getDirectCode(),
                sequence);
    }

    @Override
    public String genStructureContractCode(String companyName) {
        String contractKey = "structure:contract:code";
        if (StringUtil.isNotBlank(companyName)) {
            contractKey = contractKey + ":" + companyName;
        }
        // 获取递增码
        long value = redisUtil.incr(contractKey, 1L);
        String preContractCode = "LDC" + companyName + "OP";

        String newContractCode = preContractCode + String.format("%04d", value);
        ContractEntity contractEntity = this.getBasicContractByCode(newContractCode);
        if (null != contractEntity) {
            ContractEntity lastContractEntity = this.getContractEntityByCodeLike(preContractCode);
            String incMaxStr = lastContractEntity.getContractCode().substring(lastContractEntity.getContractCode().length() - 4);
            Integer incMaxNum = Integer.valueOf(incMaxStr);
            redisUtil.set(contractKey, incMaxNum + 1);
            newContractCode = preContractCode + String.format("%04d", incMaxNum + 1);
        }
        return newContractCode;
    }

    private long getContractSequence(Integer salesType, Integer goodsCategoryId, String companyName) {
//         = GoodsCategoryEnum.getByValue(goodsCategoryId).getCode().toLowerCase();
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(goodsCategoryId);
        String categoryCode = categoryEntity.getCode();
        String salesCode = ContractSalesTypeEnum.SALES.getValue() == salesType ? "sales" : "purchase";

        // 兼容TJIB
        String newCompanyName = "";
        if (!companyName.equals("TJIB")) {
            newCompanyName = ":" + companyName;
        }

        String name = categoryCode + ":" + salesCode + ":contract:sequence" + newCompanyName;

        log.info("getContractSequence name:{}", name);

        // 获取递增码
        if (null == redisUtil.get(name)) {
            String maxContractCode = contractDao.getMaxContractCode(companyName, salesType, goodsCategoryId);
            if (StringUtil.isNotEmpty(maxContractCode)) {
                String code = maxContractCode.substring(maxContractCode.length() - 5);
                redisUtil.set(name, Long.parseLong(code));
            }
        }

        String contractSequenceYear = categoryCode + ":" + salesCode + ":contract:sequence:year";
        // 是否跨年份
        String yearLast = LocalDate.now().format(DateTimeFormatter.ofPattern("yy"));
        if (null == redisUtil.get(contractSequenceYear)) {
            redisUtil.set(contractSequenceYear, yearLast);
        } else {
            String year = String.valueOf(redisUtil.get(contractSequenceYear));
            if (!yearLast.equals(year)) {
                // 清空计数
                redisUtil.set(name, 0);

                redisUtil.set(contractSequenceYear, yearLast);
            }
        }

        return redisUtil.incr(name, 1L);
    }

    @Override
    public String genSonContractCode(String contractCode, Integer salesType) {
        // 处理合同号
        if (contractCode.contains("-")) {
            contractCode = contractCode.split("-")[0];
        }

        // 获取数据库中最大编号
        List<ContractEntity> contractEntityList = contractDao.getContractCodeList(contractCode);
        List<Integer> codeList = contractEntityList.stream().map(contractEntity -> {
            String code = contractEntity.getContractCode();
            return Integer.parseInt(code.contains("-") ? code.split("-")[1] : "0");
        }).collect(Collectors.toList());

        // 并发问题,暂时用唯一索引 FIXME
        Integer increaseCode = CollectionUtil.max(codeList) + 1;

        return CodeGeneratorUtil.genSonContractCode(contractCode, increaseCode);
    }

    @Override
    public Result queryContract(QueryDTO<ContractQO> queryDTO) {
        ContractQO contractBO = queryDTO.getCondition();
        if (null != contractBO.getTriggerSys() && null != contractBO.getColumbusCustomerId()) {
            if (contractBO.getTriggerSys().equals(SystemEnum.COLUMBUS.getName())) {
                CustomerEntity customerEntity = customerFacade.getCustomerById(contractBO.getColumbusCustomerId());
                if (DisableStatusEnum.DISABLE.getValue() == customerEntity.getStatus()) {
                    throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
                }
                CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
                if (DisableStatusEnum.DISABLE.getValue() == cEmployEntity.getStatus()) {
                    throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
                }
            }
        }
        IPage<ContractVOEntity> contractProcessorEntityIPage = contractVODao.queryContract(queryDTO);
        List<ContractVO> contractList = contractProcessorEntityIPage.getRecords().stream().map(contractVOEntity -> {
            ContractVO contractVO = new ContractVO();
            BeanUtils.copyProperties(contractVOEntity, contractVO);

            contractVO
                    .setTradeType(Integer.valueOf(contractVOEntity.getTradeType()))
                    .setSignDate(DateTimeUtil.parseDateString(contractVOEntity.getSignDate()))
                    .setContractType(Integer.valueOf(contractVOEntity.getContractType()))
                    .setDeliveryStartTime(DateTimeUtil.parseDateString(contractVOEntity.getDeliveryStartTime()))
                    .setDeliveryEndTime(DateTimeUtil.parseDateString(contractVOEntity.getDeliveryEndTime()))
                    .setStatus(Integer.valueOf(contractVOEntity.getStatus()));


            // 主力合约
            String prefix = "M";
            if (contractVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
                prefix = "Y";
            }

            BigDecimal extraPrice = contractVOEntity.getExtraPrice() == null ? BigDecimal.ZERO : contractVOEntity.getExtraPrice();
            String domainCode = BigDecimalUtil.isGreaterEqualThanZero(extraPrice) ?
                    prefix + contractVOEntity.getDomainCode() + "+" + extraPrice.stripTrailingZeros().toPlainString() :
                    prefix + contractVOEntity.getDomainCode() + extraPrice.stripTrailingZeros().toPlainString();
            contractVO.setDomainCode(domainCode);

            Integer priceComplete = PriceCompleteEnum.NOT_PRICE_COMPLETE.getType();

            if (BigDecimalUtil.isEqual(contractVOEntity.getContractNum(), contractVOEntity.getTotalPriceNum()) && Integer.parseInt(contractVOEntity.getContractType()) != ContractTypeEnum.YI_KOU_JIA.getValue()) {
                priceComplete = PriceCompleteEnum.PRICE_COMPLETE.getType();
            }
            contractVO.setPriceComplete(priceComplete);

            // 结构化定价
            if (Integer.parseInt(contractVOEntity.getContractType()) == ContractTypeEnum.STRUCTURE.getValue()) {
                ContractStructureEntity contractStructureEntity = contractStructureService.getContractStructure(contractVOEntity.getId());
                ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
                contractVO.setContractStructureVO(contractStructureDTO);
            }

            // 点价截止时间
            if (StringUtils.isNotBlank(contractVOEntity.getPriceEndTime())) {
                contractVO.setPriceEndTime(contractVOEntity.getPriceEndTime().split(" ")[0]);
            }

            // 交提货方式
            contractVO.setDeliveryTypeName(contractVOEntity.getDeliveryTypeName());

            // 客户集团名称
            contractVO.setEnterpriseName(contractVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractVOEntity.getEnterpriseName() : contractVOEntity.getSupplierEnterpriseName());

            // 已定价量
            contractVO.setPriceNum(contractVOEntity.getTotalPriceNum());

            // 异步执行合同列表
            CompletableFuture<Void> tradeTicketFuture = CompletableFuture.runAsync(() -> {
                TradeTicketEntity tradeTicketEntity = tradeTickService.getCanModifyByContractId(contractVOEntity.getId());
                // 操作人信息
                EmployEntity employEntity = employFacade.getEmployById(null != tradeTicketEntity ? tradeTicketEntity.getCreatedBy() : contractVOEntity.getCreatedBy());
                if (null != employEntity) {
                    contractVO.setOperatorName(employEntity.getName());
                }
                // 操作时间
                contractVO.setOperatorTime(null != tradeTicketEntity ? tradeTicketEntity.getCreatedAt() : contractVOEntity.getCreatedAt());

                contractVO.setPaymentTypeName(PaymentTypeEnum.getByType(Integer.valueOf(contractVOEntity.getPaymentType())) == PaymentTypeEnum.IMPREST ? PaymentTypeEnum.IMPREST.getDesc() : PaymentTypeEnum.CREDIT.getDesc() + "，" + contractVOEntity.getCreditDays() + "天")
                        .setLatestTtId(null != tradeTicketEntity ? tradeTicketEntity.getId() : null);
            });

            CompletableFuture<Void> showButtonFuture = CompletableFuture.runAsync(() -> {
                // 展示反点价
                int isShowReverse = DisableStatusEnum.DISABLE.getValue();
                if (BigDecimalUtil.isGreaterThanZero(contractVOEntity.getContractNum())                                        // 合同量>0
                        && (Integer.parseInt(contractVOEntity.getStatus()) == ContractStatusEnum.EFFECTIVE.getValue())         // 生效中
                        && BigDecimalUtil.isZero(contractVOEntity.getCloseTailNum())                                           // 尾量关闭=0
                        && (Integer.parseInt(contractVOEntity.getContractType()) == ContractTypeEnum.YI_KOU_JIA.getValue())) { // 一口价
                    // 销售合同判断可反点次数
                    if (contractVOEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
                        // 可反点次数>0
                        if (contractVOEntity.getAbleReversePriceTimes() > 0) {
                            isShowReverse = DisableStatusEnum.ENABLE.getValue();
                        }
                    } else {
                        // 采购合同判断是否有反点价权限
                        isShowReverse = DisableStatusEnum.ENABLE.getValue();
                    }
                }
                contractVO.setIsShowReversePrice(isShowReverse);

                // 展示定价完成
                int isShowPriceComplete = DisableStatusEnum.DISABLE.getValue();

                String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractVOEntity.getContractCode();
                int priceCompleteStatus = redisUtil.get(priceStatus) == null ? 0 : (int) redisUtil.get(priceStatus);

                if (Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.ZAN_DING_JIA.getValue(),
                        ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()).contains(Integer.valueOf(contractVOEntity.getContractType()))
                        && BigDecimalUtil.isGreaterThanZero(contractVOEntity.getContractNum())                             // 合同量>0
                        && BigDecimalUtil.isEqual(contractVOEntity.getContractNum(), contractVOEntity.getTotalPriceNum())  // 合同量=定价量
                        && (Integer.parseInt(contractVOEntity.getStatus()) == ContractStatusEnum.EFFECTIVE.getValue())     // 生效中
                        && priceCompleteStatus == 0) {                                                                     // 定价完成
                    isShowPriceComplete = DisableStatusEnum.ENABLE.getValue();
                }
                contractVO.setIsShowPriceComplete(isShowPriceComplete);
            });
            CompletableFuture.allOf(tradeTicketFuture, showButtonFuture).join();

            // 是否尾量关闭
            contractVO.setIsCloseTailNum(BigDecimalUtil.isGreaterThanZero(contractVOEntity.getCloseTailNum()) ? 1 : 0);

            return contractVO;
        }).collect(Collectors.toList());

        // 统一获取lkg接口  contractBO.setTriggerSys(SystemEnum.COLUMBUS.getName());
        if (queryDTO.getCondition().getTriggerSys().equals(SystemEnum.COLUMBUS.getName())) {
            setContractNumInfo(contractList);
        }

        return Result.page(contractProcessorEntityIPage, contractList);
    }

    @Override
    public Result pageContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO) {

        IPage<ContractEntity> page = contractDao.queryContractsByDomainCode(queryDTO);
        QueryContractDTO queryContractDTO = queryDTO.getCondition();

        List<ContractVO> assignableContracts = this.completeContract(page.getRecords(), queryContractDTO.getType(),
                queryContractDTO.getPriceAllocateId());

        return Result.page(page, assignableContracts);
    }

    @Override
    public List<ContractVO> listContractsByDomainCode(QueryContractDTO queryContractDTO) {
        QueryDTO<QueryContractDTO> queryDTO = new QueryDTO<>();
        queryDTO.setCondition(queryContractDTO);
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);

        // 获取数据
        IPage<ContractEntity> iPage = contractDao.queryContractsByDomainCode(queryDTO);

        // 数据处理
        List<ContractVO> contractVOS = this.completeContract(iPage.getRecords(), queryContractDTO.getType(), null);
        return contractVOS;
    }

    @Override
    public List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO) {
        // 获取数据
        List<ContractEntity> contractEntities = contractDao.queryContractsByDomainCodeList(queryContractDTO);

        return contractEntities;
    }


    @Override
    public Result queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO) {
        Integer companyId = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId();
        IPage<ContractEntity> iPage = contractDao.queryContractsColumbus(queryDTO, companyId);
        //IPage<ContractEntity> iPage = contractService.queryContractsColumbus(queryDTO, 1);
        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractEntity -> {
                    // 获取合同所属商务
                    EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
                    ContractVO contractVO = new ContractVO();
                    BeanUtil.copyProperties(contractEntity, contractVO);
                    // 获取客户合同url列表
                    List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setCustomerContractUrl(customerContractUrls);

                    // 获取合同模板url地址
                    List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);


                    contractVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);

                    // 获取品类编码
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
                    contractVO.setCategoryCode(categoryEntity != null ? categoryEntity.getCode() : null);
                    // 获取LDC合同url地址
                    List<FileInfoEntity> ldcContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setLdcContractUrl(ldcContractUrls != null && ldcContractUrls.size() > 0 ? ldcContractUrls.get(0).getFileUrl() : null);

                    // 交提货方式
                    contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());

                    // 获取customer信息
                    CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getCustomerId());
                    contractVO.setCustomerDTO(customerDTO);
                    // 重量质检
                    if (StringUtils.isNotBlank(contractEntity.getWeightCheck())) {
                        SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getWeightCheck()));
                        contractVO.setWeightCheckName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null);
                    }
                    // 目的地名
                    if (StringUtils.isNotBlank(contractEntity.getDestination())) {
                        SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getDestination()));
                        contractVO.setDestinationName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null);
                    }

                    // 根据createdBy获取employ信息
                    EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
                    contractVO.setCreatedEntity(employEntity);

                    return contractVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, contractVOList);
    }

    @Override
    public Result<ContractDetailVO> getContractDetailByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return getContractDetail(CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0) : null, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public Integer getContractIdByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0).getId() : null;
    }

    @Override
    public ContractEntity getContractByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0) : null;
    }

    @Override
    public Result<ContractDetailVO> getContractDetailById(String contractId) {
        ContractEntity contractEntity = contractDao.getContractById(Integer.valueOf(contractId));
        return getContractDetail(contractEntity, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public Result<ContractDetailVO> getBasicContractDetailById(String contractId) {
        ContractEntity contractEntity = contractDao.getContractById(Integer.valueOf(contractId));
        return getContractDetail(contractEntity, DisableStatusEnum.DISABLE.getValue());
    }

    /**
     * 获取合同详情
     *
     * @param contractEntity
     * @return
     */
    private Result<ContractDetailVO> getContractDetail(ContractEntity contractEntity, Integer syncStatus) {
        ContractDetailVO contractDetailVO = new ContractDetailVO();
        BeanUtil.copyProperties(contractEntity, contractDetailVO);
        CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractEntity.getCustomerId() : contractEntity.getSupplierId());
        if (null != customerDTO) {
            contractDetailVO.setEnterprise(customerDTO.getEnterprise())
                    .setLkgCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
            contractDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());

        }
        FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(contractEntity.getShipWarehouseId());
        if (factoryWarehouseEntity != null) {
            contractDetailVO.setShipWarehouseName(factoryWarehouseEntity.getName())
                    .setShipWarehouseAddress(factoryWarehouseEntity.getDeliveryPoint())
                    .setLkgWarehouseCode(factoryWarehouseEntity.getLkgWarehouseCode());
        }

        // 目的地名
        String destinationName = contractEntity.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
            if (destinationSystemRuleItemEntity != null) {
                contractDetailVO.setDestinationName(destinationName)
                        .setLkgDestinationCode(destinationSystemRuleItemEntity.getLkgCode());
            }
        }
        // 交提货方式
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        contractDetailVO.setDeliveryTypeEntity(deliveryTypeEntity)
                .setDeliveryTypeName(deliveryTypeEntity != null ? deliveryTypeEntity.getName() : "");

        CategoryEntity categoryDTO = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
        contractDetailVO.setGoodsCategoryName(categoryDTO.getName());

        // 获取合同模板url地址
        List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
        contractDetailVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);

        // 获取客户合同url列表
        List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
        contractDetailVO.setCustomerContractUrl(customerContractUrls);


        // 重量质检
        if (StringUtils.isNotBlank(contractEntity.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getWeightCheck()));
            contractDetailVO.setWeightCheckName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null)
                    .setLkgWeightCheckCode(systemRuleItemEntity != null ? systemRuleItemEntity.getLkgCode() : null);
        }
        // 袋皮扣重
        if (StrUtil.isNotBlank(contractEntity.getPackageWeight())) {
            SystemRuleItemEntity pacSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getPackageWeight()));
            contractDetailVO.setPackageWeightName(pacSystemRuleItemEntity != null ? pacSystemRuleItemEntity.getRuleKey() : null);
        }

        contractDetailVO.setInvoiceType(contractEntity.getInvoiceType());
        contractDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(contractEntity.getInvoiceType()).getDesc());

        // 获取最新的tt与协议的编号
        TradeTicketEntity tradeTicketEntity = tradeTickService.getCanModifyByContractId(contractEntity.getId());
        if (null != tradeTicketEntity) {
            contractDetailVO.setTtId(tradeTicketEntity.getId());
            contractDetailVO.setTtCode(tradeTicketEntity.getCode());
            contractDetailVO.setTtCreatedAt(tradeTicketEntity.getCreatedAt());
            contractDetailVO.setSignId(tradeTicketEntity.getSignId());
            contractDetailVO.setProtocolCode(tradeTicketEntity.getProtocolCode());
        }

        // 所属商务
        EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
        contractDetailVO.setBusinessPersonName(businessPerson != null ? businessPerson.getName() : null);
        //创建人
        if (null != contractEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
            contractDetailVO.setCreateByName(employEntity != null ? employEntity.getName() : null);
        }


        // 合同对应价格信息
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailVO priceDetailVO = BeanConvertUtils.map(PriceDetailVO.class, contractPriceEntity);
        contractDetailVO.setPriceDetailVO(priceDetailVO);

        //补充信息-标签ID集合
        if (!StringUtils.isBlank(contractEntity.getTagConfigIds())) {

            List<String> tagConfigIds = new ArrayList<>();
            String[] tagIdList = contractEntity.getTagConfigIds().split(",");

            for (String tagId : tagIdList) {
                SystemRuleItemEntity systemRuleItem = systemRuleFacade.getRuleItemById(Integer.parseInt(tagId));
                tagConfigIds.add(systemRuleItem.getRuleValue());
            }
            contractDetailVO.setTagConfigList(tagConfigIds);
        }
        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {
            ContractStructureEntity contractStructureEntity = contractStructureService.getContractStructure(contractEntity.getId());
            ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
            if (contractStructureDTO != null) {
                if (StringUtils.isBlank(contractStructureEntity.getStructureName())) {
                    String structureName = structureRuleFacade.getNameById(contractStructureEntity.getStructureType());
                    contractStructureDTO.setStructureTypeName(structureName);
                } else {
                    contractStructureDTO.setStructureTypeName(StructureCodeUtil.numToCode(contractStructureEntity.getStructureType()) + "-" + contractStructureEntity.getStructureName());
                }
            }

            contractDetailVO.setContractStructureVO(contractStructureDTO);
        }

        // 合同数量
        ContractModifyNumVO contractNumInfo = getContractNumInfo(contractEntity, syncStatus);
        BeanUtils.copyProperties(contractNumInfo, contractDetailVO);

        // 可回购数量
        BigDecimal buyBackNum = contractEntity.getContractNum().subtract(contractEntity.getTotalBuyBackNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBuyBackNum());
        BigDecimal maxNum = contractEntity.getTotalBillNum();
        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum());
        } else if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
        }
        contractDetailVO.setBuyBackNum(buyBackNum.subtract(maxNum));

        // 可关闭数量
        BigDecimal closeNum = contractEntity.getContractNum().subtract(contractEntity.getTotalDeliveryNum());

        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            closeNum = contractEntity.getContractNum().subtract(BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum()));
        }

        contractDetailVO.setCloseNum(closeNum);

        // 回购原合同的编号
        if (ContractActionEnum.BUYBACK.getActionValue() == contractEntity.getContractSource()) {
            ContractEntity parentContract = contractValueObjectService.getContractById(contractEntity.getParentId());
            if (null != parentContract) {
                contractDetailVO.setBuyBackContractCode(parentContract.getContractCode());
                contractDetailVO.setBuyBackContractId(parentContract.getId());
            }
        }

        // 合同详情按钮展示
        setShowButton(contractEntity, contractDetailVO);
        contractDetailVO.setPriceStartTime(DateTimeUtil.parseTimeStamp2359(contractDetailVO.getPriceStartTime()));

        // 付款条件代码
        Result payConditionResult = payConditionFacade.getPayConditionById(contractDetailVO.getPayConditionId());
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            contractDetailVO.setPayConditionCode(payConditionEntity.getCode());
        }

        String key = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
        Object o = redisUtil.get(key);
        if (Objects.isNull(o)) {
            contractDetailVO.setPriceCompleteStatus(0);
        } else {
            contractDetailVO.setPriceCompleteStatus(1);
        }
        contractDetailVO.setUsage(contractEntity.getUsage());
        if (contractEntity.getUsage() != null) {
            contractDetailVO.setUsageString(UsageEnum.getDescByValue(contractEntity.getUsage()));
        }
        return Result.success(contractDetailVO);
    }

    // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 Start
    public BigDecimal contractAvgPrice(Integer contractId) {
        // 已定价量
        BigDecimal sumPriceNum = BigDecimal.ZERO;
        // 定价价格
        BigDecimal sumPrice = BigDecimal.ZERO;
        // 加权平均价
        BigDecimal avgPrice = BigDecimal.ZERO;

        List<TTPriceEntity> TTPriceEntityList = ttPriceService.getConfirmPriceList(contractId);

        if (CollectionUtil.isNotEmpty(TTPriceEntityList)) {
            for (TTPriceEntity ttPriceEntity : TTPriceEntityList) {
                sumPriceNum = sumPriceNum.add(ttPriceEntity.getNum());
                sumPrice = sumPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
            }
        }

        if (BigDecimalUtil.isGreaterThanZero(sumPriceNum)) {
            avgPrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, sumPrice, sumPriceNum);
        }
        return avgPrice;
    }
    // BUGFIX：case-1002774 Case-采购合同部分点价，剩余部分转月，原合同含税单价不正确 Author: wan 2024-10-24 end

    private void setShowButton(ContractEntity contractEntity, ContractDetailVO contractDetailVO) {

        // 是否展示作废按钮
        int isShowInvalid = DisableStatusEnum.DISABLE.getValue();

        // 合同剩余量为0
        if (BigDecimalUtil.isZero(contractEntity.getContractNum())) {
            // 判断是否变更过主体
            List<ContractEntity> sonContractList = contractDao.getContractByPid(contractEntity.getId());
            if (CollectionUtil.isNotEmpty(sonContractList) && sonContractList.stream()
                    .anyMatch(sonContract -> (sonContract.getContractSource().equals(ContractActionEnum.REVISE_CUSTOMER.getActionValue()) ||
                            sonContract.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())))) {
                isShowInvalid = DisableStatusEnum.ENABLE.getValue();
            }
        }

        // 合同已作废不显示
        if (contractEntity.getStatus() == ContractStatusEnum.INVALID.getValue()) {
            isShowInvalid = DisableStatusEnum.DISABLE.getValue();
        }
        contractDetailVO.setIsShowInvalid(isShowInvalid);

        // 是否展示反点价按钮
        int isShowReverse = DisableStatusEnum.DISABLE.getValue();

        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getContractNum())                                        // 合同量>0
                && (contractEntity.getStatus() == ContractStatusEnum.EFFECTIVE.getValue())                           // 生效中
                && BigDecimalUtil.isZero(contractEntity.getCloseTailNum())                                           // 尾量关闭=0
                && (contractEntity.getContractType() == ContractTypeEnum.YI_KOU_JIA.getValue())) {                   // 一口价
            // 销售合同判断可反点次数
            if (contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
                // 可反点次数>0
                if (contractEntity.getAbleReversePriceTimes() > 0) {
                    isShowReverse = DisableStatusEnum.ENABLE.getValue();
                }
            } else {
                // 采购合同判断是否有反点价权限
                isShowReverse = DisableStatusEnum.ENABLE.getValue();
            }
        }
        contractDetailVO.setIsShowReversePrice(isShowReverse);

        // 是否展示定价完成
        int isShowPriceComplete = DisableStatusEnum.DISABLE.getValue();
        // 原合同类型
        Integer originContractType = contractEntity.getContractType();

        String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
        int priceCompleteStatus = redisUtil.get(priceStatus) == null ? 0 : (int) redisUtil.get(priceStatus);

        if (Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.ZAN_DING_JIA.getValue(),
                ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()).contains(originContractType)
                && BigDecimalUtil.isGreaterThanZero(contractEntity.getContractNum())                            // 合同量>0
                && BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())   // 合同量=定价量
                && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())                   // 生效中
                && priceCompleteStatus == 0) {                                                                  // 定价完成
            isShowPriceComplete = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowPriceComplete(isShowPriceComplete);

        // 是否展示暂定价定价
        int isShowCreateTtPrice = DisableStatusEnum.DISABLE.getValue();

        if (contractEntity.getContractType().equals(ContractTypeEnum.ZAN_DING_JIA.getValue())
                && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())
                && !BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {

            isShowCreateTtPrice = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowCreateTtPrice(isShowCreateTtPrice);


        // 拆分修改是否展示保存按钮
        int isShowSaveButton = DisableStatusEnum.DISABLE.getValue();

        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
        if (StringUtils.isBlank(saveTimes) && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            isShowSaveButton = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowSaveButton(isShowSaveButton);
    }

    @Override
    public Result getContractPdfs(String contractId) {

        List<FileInfoVO> fileInfoVOs = new ArrayList<>();

        // 获取有效电子合同（初始合同）
        List<FileInfoEntity> enContractPdfSignatureLdcOriginalFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
        List<FileInfoVO> enableLdcOriginalFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(enContractPdfSignatureLdcOriginalFileInfos, DisableStatusEnum.ENABLE.getValue());
        fileInfoVOs.addAll(enableLdcOriginalFileInfoVOS);

        // 获取有效电子合同（客户签章）
        List<FileInfoEntity> enContractPdfSignatureCustomerFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
        List<FileInfoVO> enableCusFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(enContractPdfSignatureCustomerFileInfos, DisableStatusEnum.ENABLE.getValue());
        fileInfoVOs.addAll(enableCusFileInfoVOS);

        // 获取有效电子合同（LDC签章）
//        List<FileInfoEntity> enContractPdfSignatureLdcFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.ENABLE.getName());
//        List<FileInfoVO> enableLdcFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(enContractPdfSignatureLdcFileInfos, DisableStatusEnum.ENABLE.getName());
//        fileInfoVOs.addAll(enableLdcFileInfoVOS);

        // 获取有效电子合同（初始合同）
        List<FileInfoEntity> disContractPdfSignatureLdcOriginalFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.DISABLE.getValue());
        List<FileInfoVO> disableLdcOriginalFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(disContractPdfSignatureLdcOriginalFileInfos, DisableStatusEnum.DISABLE.getValue());
        fileInfoVOs.addAll(disableLdcOriginalFileInfoVOS);

        // 获取失效电子合同（LDC签章）
//        List<FileInfoEntity> disContractPdfSignatureLdcFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.DISABLE.getName());
//        List<FileInfoVO> disableLdcFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(disContractPdfSignatureLdcFileInfos, DisableStatusEnum.DISABLE.getName());
//        fileInfoVOs.addAll(disableLdcFileInfoVOS);

        // 获取失效电子合同（客户签章）
        List<FileInfoEntity> disContractPdfSignatureCustomerFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.DISABLE.getValue());
        List<FileInfoVO> disableCusFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(disContractPdfSignatureCustomerFileInfos, DisableStatusEnum.DISABLE.getValue());
        fileInfoVOs.addAll(disableCusFileInfoVOS);

        return Result.success(fileInfoVOs);
    }

    @Override
    public boolean canSign(String contractId) {

        TradeTicketEntity ticketEntity = tradeTickService.getBySignId(contractId);
        // TT是否存在
        if (ticketEntity == null) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }


        if (TTTypeEnum.SPLIT.getType().equals(ticketEntity.getType())) {

            TradeTicketEntity tradeTicketEntitie = tradeTickService.getByGroupId(ticketEntity.getGroupId(), ticketEntity.getContractId());

            // TT状态审批通过
            if (!ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                    && !ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue())) {

                throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
            }

            // TT状态审批通过
            if ((!tradeTicketEntitie.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                    && !tradeTicketEntitie.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue()))) {

                throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
            }

        }

        // TT状态审批通过
        if (!ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                && !ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
        }
        return true;
    }

    @Override
    public Result getContractUnitPriceDetail(String contractId) {
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(Integer.parseInt(contractId));
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtil.copyProperties(contractPriceEntity, priceDetailVO);
        return Result.success(priceDetailVO);
    }


    @Override
    public ContractEntity queryContractByUuid(String uuid) {
        return contractDao.getOne(Wrappers.<ContractEntity>lambdaQuery()
                .eq(ContractEntity::getUuid, uuid)
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    // TODO 待去掉
    @Override
    public ContractEntity getContractById(Integer id) {
        return contractValueObjectService.getContractById(id);
    }

    @Override
    public ContractEntity getBasicContractById(Integer id) {
        return contractDao.getContractById(id);
    }

    @Override
    public ContractEntity getBasicContractByCode(String code) {
        return contractDao.getBasicContractByCode(code);
    }

    @Override
    public ContractEntity getContractEntityByCodeLike(String code) {
        return contractDao.getContractEntityByCodeLike(code);
    }

    @Override
    public ContractDetailInfoDTO getContractDetailInfoDTO(Integer id) {
        ContractDetailInfoDTO contractDetailInfoDTO = null;

        ContractEntity contractEntity = getContractById(id);
        if (null != contractEntity) {
            contractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, contractEntity);

            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());

            contractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity));
        }

        return contractDetailInfoDTO;

    }

    @Override
    public Integer updateContract(ContractEntity contractEntity) {
        return contractValueObjectService.updateContractById(contractEntity) ? 1 : 0;
    }


    private List<FileInfoVO> fileInfoEntityCovertFileInfoVO(List<FileInfoEntity> fileInfoEntityList, Integer status) {
        List<FileInfoVO> fileInfoVOS = new ArrayList<>();
        for (FileInfoEntity fileInfoEntity : fileInfoEntityList) {
            FileInfoVO fileInfoVO = new FileInfoVO();
            BeanUtil.copyProperties(fileInfoEntity, fileInfoVO);
            fileInfoVO.setStatus(status);
            fileInfoVOS.add(fileInfoVO);
        }
        return fileInfoVOS;
    }

    @Override
    public List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO) {
        return contractDao.queryContractsFutures(contractFuturesDTO);
    }

    @Override
    public List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO) {
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        //权益变更校验代码迁移
        List<ContractEntity> contractEntities = contractDao.queryContractsFuturesNum(contractFuturesDTO);
        if (CollectionUtil.isEmpty(contractEntities)) {
            return Collections.emptyList();
        }

        List<Integer> contractIdSet = contractEntities.stream()
                .map(ContractEntity::getId)
                .collect(Collectors.toList());

        List<ContractChangeEquityEntity> contractEquityChanges = contractChangeEquityService.getChangeContractEquityDetailByContractIds(contractIdSet);
        //查询是否存在权益变更的数据
        if(!CollectionUtil.isEmpty(contractEquityChanges)){
            List<Integer> changedContractIds = contractEquityChanges.stream()
                    .map(ContractChangeEquityEntity::getContractId)
                    .collect(Collectors.toList());
            //将权益变更数据移除
            contractEntities.removeIf(contractEntity -> changedContractIds.contains(contractEntity.getId()));
        }
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        return contractEntities;
    }

    @Override
    public Result futureContracts(QueryDTO<ContractFuturesDTO> queryDTO) {

        IPage<ContractEntity> iPage = contractDao.futureContracts(queryDTO);

        //IPage<ContractEntity> iPage = contractService.queryContractsColumbus(queryDTO, 1);
        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractEntity -> {
                    ContractVO contractVO = new ContractVO();
                    BeanUtil.copyProperties(contractEntity, contractVO);
                    // 交提货方式
                    contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());

                    // 目的地名
                    if (StringUtils.isNotBlank(contractEntity.getDestination()) && contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
                        SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getDestination()));
                        contractVO.setDestinationName(systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : null);
                    } else {
                        contractVO.setDestinationName(contractEntity.getDestination());
                    }

                    return contractVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, contractVOList);
    }

    @Override
    public List<ContractModifyVO> getContractModifyLog(ContractModifyDTO contractModifyDTO) {
        ContractEntity contractEntity = contractDao.getContractById(contractModifyDTO.getContractId());
        if (contractEntity == null) {
            return new ArrayList<>();
        }
        List<TradeTicketEntity> tradeTicketEntityList = tradeTickService.queryChangeLogByContractId(contractModifyDTO.getContractId(), contractEntity.getContractCode());
        List<ContractModifyVO> contractModifyVOList = tradeTicketEntityList.stream().map(i -> {
            ContractModifyVO contractModifyVO = new ContractModifyVO();
            if (i.getIsDeleted().equals(IsDeletedEnum.DELETED.getValue())) {

                TradeTicketEntity newTradeTicketEntity = tradeTickService.getByGroupId(i.getGroupId(), i.getId());
                if (newTradeTicketEntity != null) {
                    contractModifyVO
                            .setTtType(newTradeTicketEntity.getType())
                            .setContractType(newTradeTicketEntity.getContractType())
                            .setTradeType(newTradeTicketEntity.getTradeType())
                            .setContractNum(newTradeTicketEntity.getChangeContractNum() == null ? null : "-" + i.getChangeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                            .setPriceNum(null)
                            .setTtId(newTradeTicketEntity.getId())
                            .setTtCode(newTradeTicketEntity.getCode())
                            .setTtStatus(newTradeTicketEntity.getStatus())
                            .setSignId(newTradeTicketEntity.getSignId())
                            .setProtocolCode(newTradeTicketEntity.getProtocolCode())
                            .setCreatedAt(newTradeTicketEntity.getCreatedAt())
                            .setCustomerName(newTradeTicketEntity.getCustomerName())
                            .setIsDeleted(newTradeTicketEntity.getIsDeleted())
                            .setSonContractCode(newTradeTicketEntity.getContractCode())
                            .setSonContractId(newTradeTicketEntity.getContractId())
                            .setChangeContractNum(newTradeTicketEntity.getChangeContractNum())
                            .setApprovalStatus(newTradeTicketEntity.getApprovalStatus() == null ? TTApproveStatusEnum.WITHOUT_APPROVE.getDesc() : TTApproveStatusEnum.getDescByValue(newTradeTicketEntity.getApprovalStatus()))
                            .setCancelReason(null == newTradeTicketEntity.getCancelReason() ? "" : newTradeTicketEntity.getCancelReason())
                    ;
                }

                if (ContractTradeTypeEnum.TRANSFER_ALL.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                ) {
                    contractModifyVO.setIsDeleted(IsDeletedEnum.DELETED.getValue());
                }

                if (ContractSalesTypeEnum.SALES.getValue() == i.getSalesType()) {
                    contractModifyVO.setCustomerName(i.getCustomerName());
                } else {
                    contractModifyVO.setCustomerName(i.getSupplierName());
                }

            } else {
                if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == i.getTradeType() || ContractTradeTypeEnum.SPLIT_NORMAL.getValue() == i.getTradeType()) {
                    TradeTicketEntity tradeTicketEntity = tradeTickService.getByGroupId(i.getGroupId(), i.getId());
                    if (null != tradeTicketEntity) {
                        TradeTicketEntity originalEntity = i;
                        i = tradeTicketEntity;
                        i.setTradeType(originalEntity.getTradeType());
                        contractModifyVO.setSonContractId(i.getContractId());
                        contractModifyVO.setSonContractCode(i.getContractCode());
                    }
                }

                contractModifyVO
                        .setTtType(i.getType())
                        .setContractType(i.getContractType())
                        .setTradeType(i.getTradeType())
                        .setContractNum((i.getChangeContractNum() == null || BigDecimal.ZERO.compareTo(i.getChangeContractNum()) == 0) ? null : i.getChangeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                        .setPriceNum(null)
                        .setTtId(i.getId())
                        .setTtCode(i.getCode())
                        .setTtStatus(i.getStatus())
                        .setSignId(i.getSignId())
                        .setProtocolCode(i.getProtocolCode())
                        .setCreatedAt(i.getCreatedAt())
                        .setCustomerName(i.getCustomerName())
                        .setIsDeleted(i.getIsDeleted())
                        .setChangeContractNum(i.getChangeContractNum())
                        .setSalesType(i.getSalesType())
                        .setApprovalStatus(i.getApprovalStatus() == null ? TTApproveStatusEnum.WITHOUT_APPROVE.getDesc() : TTApproveStatusEnum.getDescByValue(i.getApprovalStatus()))
                        .setCancelReason(null == i.getCancelReason() ? "" : i.getCancelReason())
                ;
//                if (TTTypeEnum.NEW.getType() != i.getType() && null != contractModifyVO.getContractNum()) {
//                    contractModifyVO.setContractNum("-" + contractModifyVO.getContractNum());
//                }
                if (ContractSalesTypeEnum.SALES.getValue() == i.getSalesType()) {
                    contractModifyVO.setCustomerName(i.getCustomerName());
                } else {
                    contractModifyVO.setCustomerName(i.getSupplierName());
                }

                if (ContractTradeTypeEnum.NEW.getValue() != i.getTradeType() && null != contractModifyVO.getContractNum()) {
                    contractModifyVO.setContractNum("-" + contractModifyVO.getContractNum());
                }
                if (ContractTradeTypeEnum.REVISE_NORMAL.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.TRANSFER_ALL.getValue() == i.getTradeType()
                ) {
                    contractModifyVO.setContractNum(null);
                }
                if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                ) {
                    BigDecimal afterContractNum = i.getAfterContractNum() == null ? BigDecimal.ZERO : i.getAfterContractNum();
                    BigDecimal beforeContractNum = i.getBeforeContractNum() == null ? BigDecimal.ZERO : i.getBeforeContractNum();
                    String contractNum = afterContractNum.subtract(beforeContractNum).setScale(3, RoundingMode.HALF_UP).toPlainString();
                    contractModifyVO.setContractNum(contractNum);
                    contractModifyVO.setSonContractCode(i.getContractCode());
                    contractModifyVO.setSonContractId(i.getContractId());
                }

                if (ContractTradeTypeEnum.PRICE.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.FIXED.getValue() == i.getTradeType()) {
                    BigDecimal changeContractNum = i.getChangeContractNum() == null ? BigDecimal.ZERO : i.getChangeContractNum();
                    contractModifyVO.setPriceNum(changeContractNum.setScale(3, RoundingMode.HALF_UP).toPlainString());
                    contractModifyVO.setContractNum(null);
                }

                // 合同关闭变更记录合同数量为“-”
                if (ContractTradeTypeEnum.CLOSED.getValue() == i.getTradeType()) {
                    contractModifyVO.setContractNum("-" + contractModifyVO.getContractNum());
                }

            }

            //创建人
            if (null != i.getCreatedBy()) {
                EmployEntity employEntity = employFacade.getEmployById(i.getCreatedBy());
                if (null != employEntity) {
                    contractModifyVO.setCreatedBy(employEntity.getName());
                }
            }
            return contractModifyVO;
        }).collect(Collectors.toList());

        List<ContractModifyVO> list = contractModifyVOList.stream()
                .map(p -> {
                    //查询协议状态
                    if (String.valueOf(TTStatusEnum.WAITING.getType()).equals(String.valueOf(p.getTtStatus()))) {
                        //待修改提交区的协议为异常状态
                        p.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
                    } else {
                        ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(p.getTtId());
                        if (contractSignDetail != null) {
                            Integer status = contractSignDetail.getStatus();
                            p.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());
                        }
                    }
                    return p;
                })
                .filter(i -> !IsDeletedEnum.DELETED.getValue().equals(i.getIsDeleted()))
                .filter(i -> i.getTtId() != null)
                .sorted(Comparator.comparing(ContractModifyVO::getTtId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list) && ContractTradeTypeEnum.NEW.getValue() != list.get(0).getTradeType()) {
            String changeContractNumber = list.get(0).getChangeContractNum() == null ? null : list.get(0).getChangeContractNum().toPlainString();
            list.get(0).setContractNum(changeContractNumber);
            list.get(0).setTtType(TTTypeEnum.NEW.getType());
        }
        return list;
    }
/*

    //获取根节点
    private List<ContractModifyVO> getRootModifyVO(List<ContractModifyVO> modifyList) {
        List<ContractModifyVO> rootModifyVOList = new ArrayList<>();
        rootModifyVOList.add(modifyList.stream()
                .filter(contractModifyVO -> contractModifyVO.getParentContractId() == 0)
                .findAny().orElse(modifyList.get(0)));
        return rootModifyVOList;
    }
*/

 /*   //建立树形结构
    public List<ContractModifyVO> buildTreeModifyVO(List<ContractModifyVO> modifyList) {
        List<ContractModifyVO> treeModifyVOList = new ArrayList<>();
        for (ContractModifyVO contractModifyVO : getRootModifyVO(modifyList)) {
            contractModifyVO = buildChildTree(contractModifyVO, modifyList);
            treeModifyVOList.add(contractModifyVO);
        }
        return treeModifyVOList;
    }*/

/*    //递归，建立子树形结构
    private ContractModifyVO buildChildTree(ContractModifyVO parentModifyVO, List<ContractModifyVO> modifyList) {
        List<ContractModifyVO> childModifyVOList = new ArrayList<>();
        for (ContractModifyVO contractModifyVO : modifyList) {
            if (contractModifyVO.getParentContractId().equals(parentModifyVO.getContractId())) {
                childModifyVOList.add(buildChildTree(contractModifyVO, modifyList));
            }
        }
        parentModifyVO.setContractModifyVOList(childModifyVOList);
        return parentModifyVO;
    }*/

    @Override
    public ContractModifyNumVO getContractModifyNumInfo(Integer contractId) {
        ContractEntity contractEntity = contractDao.getById(contractId);
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        return getContractNumInfo(contractEntity, DisableStatusEnum.ENABLE.getValue());
    }

    /**
     * 获取合同数量信息
     *
     * @param contractEntity
     * @return
     */
    private ContractModifyNumVO getContractNumInfo(ContractEntity contractEntity, Integer syncStatus) {
        ContractModifyNumVO contractModifyNumVO = new ContractModifyNumVO();
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = BigDecimal.ZERO;
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;

        // 是否启用LKG
        if (syncStatus.equals(DisableStatusEnum.ENABLE.getValue())) {

            ContractEntity objectContract = contractValueObjectService.getContractById(contractEntity.getId());
            // 已开单数量
            totalBillNum = objectContract.getTotalBillNum();
            // 已提数量
            totalDeliveryNum = objectContract.getTotalDeliveryNum();
            // 调拨数量
            allocateNum = objectContract.getAllocateNum();
        }

        // 处理合同数量
        contractEntity.setTotalBillNum(totalBillNum);
        contractEntity.setTotalDeliveryNum(totalDeliveryNum);
        contractEntity.setAllocateNum(allocateNum);

        contractModifyNumVO.setTotalBillNum(totalBillNum);
        contractModifyNumVO.setTotalDeliveryNum(totalDeliveryNum);
        contractModifyNumVO.setAllocateNum(allocateNum);

        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO()
                .setDomainCode(contractEntity.getDomainCode())
                .setSalesType(contractEntity.getSalesType())
                .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerId(String.valueOf(contractEntity.getCustomerId()));
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            contractFuturesDTO.setCustomerId(contractEntity.getSupplierId().toString());
        }
        switch (typeEnum) {
            case YI_KOU_JIA:
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(BigDecimal.ZERO)
                        // 可变更量
                        .setCanModifyNum(contractNum.subtract(totalDeliveryNum));
                break;
            case JI_CHA:
                Result transferResult = futuresDomainFacade.mayTransferNum(contractFuturesDTO);
                CustomerFuturesDTO transferDTO = JSON.parseObject(JSON.toJSONString(transferResult.getData()), CustomerFuturesDTO.class);
//                BigDecimal allocateNum = priceAllocateFacade.getSumPriceAllocateOfContract(String.valueOf(contractEntity.getId()));
                contractModifyNumVO.setTransferNum(transferDTO.getMayTransferNum());
            case JI_CHA_ZAN_DING_JIA:
                Result priceResult = futuresDomainFacade.mayPriceNum(contractFuturesDTO);
                CustomerFuturesDTO ablePriceDTO = JSON.parseObject(JSON.toJSONString(priceResult.getData()), CustomerFuturesDTO.class);
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(contractNum.subtract(totalPriceNum))
                        .setAblePriceNum(ablePriceDTO.getMayPriceNum())
                        // 定价状态
                        .setPricedStatus(judgePricedStatus(contractNum, totalPriceNum));
                break;
            case ZAN_DING_JIA:
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(contractNum.subtract(totalPriceNum));
                break;
            default:
                break;
        }
        contractModifyNumVO
                .setPricedNum(totalPriceNum)
                .setDeliveryNum(totalDeliveryNum)
                .setContractNum(contractNum)
                .setApplyDeliveryNum(contractEntity.getApplyDeliveryNum());

        return contractModifyNumVO;
    }

    private void setContractNumInfo(List<ContractVO> contractVOList) {
        for (ContractVO contractVO : contractVOList) {
            try {
                ContractEntity contractEntity = contractValueObjectService.getContractById(contractVO.getId());
                setContractVOInfo(contractVO, contractEntity);
            } catch (Exception e) {
                log.error("contractId:{} getLkgInfo Exception,cause:{}", contractVO.getId(), e.getMessage());
                for (ContractVO newContractVO : contractVOList) {
                    ContractEntity newContractEntity = this.getBasicContractById(newContractVO.getId());
                    newContractVO.setStatus(ContractStatusEnum.LKG_EXCEPTION.getValue());
                    setContractVOInfo(newContractVO, newContractEntity);
                }
                break;
            }
        }
    }

    private ContractVO setContractVOInfo(ContractVO contractVO, ContractEntity contractEntity) {

        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());

        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        // 已开单数量
        BigDecimal totalBillNum = contractEntity.getTotalBillNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBillNum();

        contractVO.setPriceNum(totalPriceNum);

        switch (typeEnum) {
            case YI_KOU_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(BigDecimal.ZERO)
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            case JI_CHA:
            case JI_CHA_ZAN_DING_JIA:
            case ZAN_DING_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(contractNum.subtract(totalPriceNum))
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            default:
                break;
        }
        return contractVO;
    }

    /**
     * 判断定价状态
     *
     * @param contractNum   合同数量
     * @param totalPriceNum 已定价量
     * @return
     */
    private Integer judgePricedStatus(BigDecimal contractNum, BigDecimal totalPriceNum) {
        // 定价状态
        int pricedStatus = 0;

        if (BigDecimal.ZERO.equals(totalPriceNum)) {
            pricedStatus = ContractPricedStatusEnum.NOT_PRICED.getValue();
        } else if (contractNum.equals(totalPriceNum)) {
            pricedStatus = ContractPricedStatusEnum.PRICED.getValue();
        } else {
            pricedStatus = ContractPricedStatusEnum.PART_PRICED.getValue();
        }

        return pricedStatus;
    }


    @Override
    public List<ConfirmPriceVO> getConfirmPriceList(Integer contractId) {
        List<ConfirmPriceVO> confirmPriceVOList = new ArrayList<>();

        List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractId);
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            ConfirmPriceVO confirmPriceVO = new ConfirmPriceVO();
            confirmPriceVO.setContractId(ttPriceEntity.getContractId())
                    .setTtPriceId(ttPriceEntity.getId())
                    .setConfirmPrice(ttPriceEntity.getPrice())
                    .setConfirmNum(ttPriceEntity.getNum());
            confirmPriceVOList.add(confirmPriceVO);
        }

        return confirmPriceVOList;
    }

    @Override
    public List<PriceDealDetailVO> getContractStructurePriceList(Integer contractId) {
        List<PriceDealDetailVO> priceDealDetailVOList = new ArrayList<>();
        ContractStructureEntity contractStructureEntity = contractStructureService.getContractStructure(contractId);
        if (null != contractStructureEntity) {
            priceDealDetailVOList = contractStructureService.getContractStructurePriceList(contractStructureEntity.getPriceApplyId());
        }
        return priceDealDetailVOList;
    }

    @Override
    public List<ContractEntity> getContractList(Integer customerId, Integer goodsCategoryId, String domainCode, List<Integer> contractTypeList) {
        return contractDao.getContractList(customerId, goodsCategoryId, domainCode, contractTypeList);
    }

    @Override
    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 Start
    public List<ContractEntity> getContractList(Integer customerId, List<Integer> category3List, List<Integer> contractStatusList, List<Integer> contractTypeList) {
        return contractDao.getContractList(customerId, category3List, contractStatusList, contractTypeList);
        // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 End
    }

    @Override
    public List<ContractEntity> getContractList(List<String> contractCodeList, String startDateTime, String endDateTime) {
        return contractDao.getContractList(contractCodeList, startDateTime, endDateTime);
    }

    @Override
    public boolean judgeCanModifyGoods(Integer contractId) {
        /*ContractEntity contractEntity = contractDao.getById(contractId);
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 合同未”拆分、点/转/反、回购、或 洗单操作”(未提货)
        if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getOrderNum())
                && BigDecimalUtil.isZero(contractEntity.getTotalTransferNum())
                && BigDecimalUtil.isZero(contractEntity.getTotalDeliveryNum())
                && BigDecimalUtil.isZero(contractEntity.getTotalBuyBackNum())) {

            // 是否存在点转反的申请
            Result result = priceApplyFacade.judgeExistApply(contractId);
            if (result.isSuccess() && !(boolean) result.getData()) {
                return true;
            }
        }*/
        return true;
    }

    @Override
    public List<ContractEntity> getDailyContractList(Integer salesType, String startDateTime, String endDateTime) {
        return contractDao.getDailyContractList(salesType, startDateTime, endDateTime);
    }

    private List<ContractVO> completeContract(List<ContractEntity> contractEntityList, String type, Integer priceAllocateId) {

        List<ContractVO> contractVOList = new ArrayList<>();
        for (ContractEntity contractEntity : contractEntityList) {
            List<ContractChangeEquityEntity> contractChangeEquityEntities = contractChangeEquityService.getChangeContractEquityDetailByNotApprove(contractEntity.getId());
            if (!contractChangeEquityEntities.isEmpty()) {
                continue;
            }
            ContractVO contractVO = new ContractVO();
            BeanUtil.copyProperties(contractEntity, contractVO);

            BigDecimal sumPriceAllocateOfContract = priceAllocateFacade.getSumPriceAllocateOfContract(String.valueOf(contractEntity.getId()));

            CategoryEntity category = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
            contractVO.setCategoryName(category.getName());

            BigDecimal canAllocateOfContract = BigDecimal.ZERO;

            // canAllocateOfContract = 合同总量 - 已点量 - 待审核(转月和点价)
            BigDecimal num = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), contractEntity.getTotalPriceNum());
            canAllocateOfContract = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, num, sumPriceAllocateOfContract);

            contractVO.setSumPriceAllocateOfContract(sumPriceAllocateOfContract);


            if (null != priceAllocateId) {
                PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(priceAllocateId.toString());

                if (priceAllocateEntity.getContractId().equals(contractEntity.getId())) {
                    canAllocateOfContract = canAllocateOfContract.add(priceAllocateEntity.getAllocateNum());
                }
            }
            contractVO.setCanAllocateOfContract(canAllocateOfContract);

            contractVO.setFactoryWarehouseName(contractEntity.getShipWarehouseValue());

            // 根据createdBy获取employ信息
            EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
            contractVO.setCreatedEntity(employEntity);
            CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
            // 获取合同所属商务
            EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
            contractVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);
            contractVO.setCompanyName(companyEntity.getName());
            //查询合同价格详情
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            if (null != contractPriceEntity) {
                contractVO.setProteinDiffPrice(contractPriceEntity.getProteinDiffPrice())
                        .setFee(contractPriceEntity.getFee());
            } else {
                contractVO.setProteinDiffPrice(BigDecimal.ZERO);
            }
            contractVOList.add(contractVO);
        }
        return contractVOList;
       /* return contractEntityList.stream().map(contractEntity -> {

        }).collect(Collectors.toList());*/

    }

    @Override
    public boolean prePareToContract(ContractEntity contractEntity) {
        // 同步合同
        contractDao.save(contractEntity);

        // 同步合同价格
        ContractPriceEntity contractPriceEntity = BeanConvertUtils.convert(ContractPriceEntity.class, contractEntity.getContractPriceEntity());

        if (null != contractPriceEntity) {
            contractPriceEntity
                    .setTtId(-contractEntity.getId())
                    .setContractId(contractEntity.getId())
                    .setContractCode(contractEntity.getLinkinageCode());
            contractPriceService.syncContractPrice(contractPriceEntity);
        }

        // 同步合同版本记录
        contractHistoryService.backupContract(contractEntity, String.valueOf(contractEntity.getTradeType()), null);

        return true;
    }

    @Override
    public List<ContractEntity> getContractByPid(Integer id) {
        return contractDao.getContractByPid(id);
    }

    @Override
    public Result updateUnitPrice(String contractCode, BigDecimal unitPrice) {
        List<ContractEntity> contractList = contractDao.getByContractCode(contractCode);
        if (contractList.size() != 1)
            return Result.failure();
        ContractEntity contract = contractList.get(0);
        contract.setUnitPrice(unitPrice);
        contract.setFobUnitPrice(unitPrice);
        contract.setCifUnitPrice(BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, contract.getTaxRate().add(BigDecimal.ONE)));
        contract.setTotalAmount((BigDecimalUtil.multiply(CalcTypeEnum.PRICE, unitPrice, contract.getContractNum())));
        contract.setDepositAmount(contract.getTotalAmount().multiply(BigDecimal.valueOf(contract.getDepositRate() * 0.01)));
        return contractDao.updateById(contract) ? Result.success() : Result.failure();
    }

    @Override
    public Integer updateAndBackUpContract(ContractBackUpDTO contractBackUpDTO) {
        return contractValueObjectService.updateContractById(contractBackUpDTO.getContractEntity(),
                contractBackUpDTO.getBackTradeType(),
                contractBackUpDTO.getReferCode()) ? 1 : 0;
    }

    @Override
    public Result updateContractStatus(MultipartFile uploadFile, Integer status) {
        List<ContractEntity> contractList = EasyPoiUtils.importExcel(uploadFile, 0, 1, ContractEntity.class);
        if (CollectionUtils.isEmpty(contractList)) {
            return Result.success("无数据信息！");
        }
        status = null == status ? ContractStatusEnum.CLOSED.getValue() : status;
        List<String> contractCodeList = new ArrayList<>();
        for (ContractEntity contractInfo : contractList) {
            ContractEntity contractEntity = contractDao.getBasicContractByCode(contractInfo.getContractCode().trim());
            contractDao.updateById(contractEntity.setStatus(status));
            contractCodeList.add(contractInfo.getContractCode().trim());
        }
        return Result.success("合同更新成功" + FastJsonUtils.getBeanToJson(contractCodeList));
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO) {

        // 商品不可提货list
        List<Integer> cannotDeliveryGoodsIdList = goodsFacade.queryCannotDeliveryGoodsIdList();
        // 工厂不可提货list
        List<String> cannotDeliveryFactoryList = Arrays.asList(this.cannotDeliveryFactoryList.split(","));
        // 提货类型不能为“送货”
        List<Integer> cannotDeliveryTypeIdList = deliveryTypeFacade.getSendDeliveryTypeIdList();

        List<ContractEntity> deliveryApplyContractList = contractDao.getDeliveryApplyContractGroup(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                cannotDeliveryFactoryList,
                cannotDeliveryTypeIdList);

        // 批量获取lkg合同信息
        List<ContractEntity> newApplyContractList = getBatchLkgContractList(deliveryApplyContractList);

        // 对合同进行商品名称/提货工厂/供应商名称进行分组然后统计合同的数量
        Map<String, List<ContractEntity>> collect = newApplyContractList.stream()
                .collect(Collectors.groupingBy(contractEntity -> contractEntity.getGoodsName()
                        + contractEntity.getDeliveryFactoryCode()
                        + contractEntity.getSupplierName()));

        List<ContractEntity> contractEntityList = new ArrayList<>();
        collect.forEach((key, value) -> {
            ContractEntity contractEntity = new ContractEntity()
                    .setGoodsId(value.get(0).getGoodsId())
                    .setGoodsName(value.get(0).getGoodsName())
                    .setDeliveryFactoryCode(value.get(0).getDeliveryFactoryCode())
                    .setSupplierId(value.get(0).getSupplierId())
                    .setSupplierName(value.get(0).getSupplierName());

            // 可提货数量
            BigDecimal canDeliveryNum = BigDecimal.ZERO;

            // 可提货数量 = 合同数量 - 开单数量 - 申请提货数量
            for (ContractEntity entity : value) {
                canDeliveryNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, canDeliveryNum, entity.getContractNum());
                canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, BigDecimalUtil.max(entity.getTotalBillNum(), entity.getTotalDeliveryNum(), entity.getAllocateNum()));
                canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, entity.getApplyDeliveryNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(canDeliveryNum)) {
                contractEntityList.add(contractEntity.setCanDeliveryNum(canDeliveryNum));
            }
        });

        // 按照商品的id倒序排序
        contractEntityList.sort(Comparator.comparing(ContractEntity::getGoodsId).reversed());

        return contractEntityList;
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO) {
        // 商品不可提货list
        List<Integer> cannotDeliveryGoodsIdList = goodsFacade.queryCannotDeliveryGoodsIdList();
        // 工厂不可提货list
        List<String> cannotDeliveryFactoryList = Arrays.asList(this.cannotDeliveryFactoryList.split(","));
        // 提货类型不能为“送货”
        List<Integer> cannotDeliveryTypeIdList = deliveryTypeFacade.getSendDeliveryTypeIdList();

        List<ContractEntity> deliveryApplyContractList = contractDao.getDeliveryApplyContractList(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                cannotDeliveryFactoryList,
                cannotDeliveryTypeIdList);

        return getContractDeliveryList(deliveryApplyContractList, false);
    }

    @Override
    public List<ContractEntity> getContractListByIds(List<Integer> contractIdList) {

        List<ContractEntity> deliveryApplyContractList = contractDao.getContractListByIds(contractIdList);

        return getContractDeliveryList(deliveryApplyContractList, true);
    }

    @Override
    public List<ContractRelativeDTO> getContractTraceList(Integer contractId) {
        ContractEntity contractEntity = contractDao.getContractById(contractId);
        if (null == contractEntity) {
            return new ArrayList<>();
        }
        //根合同信息
        ContractEntity rootContractEntity = 0 == contractEntity.getParentId() ? contractEntity :
                this.getRootContract(contractEntity);
        if (null == rootContractEntity) {
            return new ArrayList<>();
        }
        log.info("合同溯源-根合同ID:" + rootContractEntity.getId());
        ContractRelativeDTO rootContractDto = this.convertContractDto(rootContractEntity);
        List<ContractRelativeDTO> contractRelativeDTOList = new ArrayList<>();
        contractRelativeDTOList.add(rootContractDto);

        this.getSonContractList(rootContractDto, contractRelativeDTOList);
        log.info("合同溯源-总合同条数:" + contractRelativeDTOList.size());
        contractRelativeDTOList.sort(Comparator.comparing(ContractRelativeDTO::getCreatedAt));
        return contractRelativeDTOList;
    }

    /**
     * 转换合同信息
     *
     * @param contractEntity
     * @return
     */
    private ContractRelativeDTO convertContractDto(ContractEntity contractEntity) {
        List<TradeTicketEntity> tradeTicketEntityList = tradeTickService.getByContractId(contractEntity.getId());
        if (CollectionUtils.isNotEmpty(tradeTicketEntityList)) {
            tradeTicketEntityList = tradeTicketEntityList.stream()
                    .filter(it -> {
                        return IsDeletedEnum.NOT_DELETED.getValue().equals(it.getIsDeleted());
                    })
                    .sorted(Comparator.comparing(TradeTicketEntity::getId))
                    .collect(Collectors.toList());
        }
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityList.get(0);
        EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
        //当前合同信息
        ContractRelativeDTO contractRelativeDTO = new ContractRelativeDTO()
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setContractNum(tradeTicketEntity.getAfterContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                .setParentContractNum(tradeTicketEntity.getBeforeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                .setParentId(contractEntity.getParentId())
                .setFactoryCode(contractEntity.getDeliveryFactoryCode())
                //contractSource、TradeType取值TT信息
                .setContractSource(tradeTicketEntity.getContractSource())
                .setContractSourceInfo(ContractActionEnum.getDescByValue(tradeTicketEntity.getContractSource()))
                .setTradeType(tradeTicketEntity.getTradeType())
//                .setTradeTypeInfo(ContractTradeTypeEnum.getTradeTypeInfo())
                .setContractStatus(contractEntity.getStatus())
                .setCreatedBy(null != employEntity ? employEntity.getName() : "")
                .setCreatedAt(tradeTicketEntity.getCreatedAt())
                .setTradeTicketEntity(tradeTicketEntity);
        if (ContractTradeTypeEnum.NEW.getValue() == tradeTicketEntity.getTradeType()) {
            if (TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
                contractRelativeDTO.setTradeType(ContractTradeTypeEnum.BUYBACK.getValue());
            }
        }
        if (0 != contractEntity.getParentId()) {
            //父合同信息
            ContractEntity parentContractEntity = contractDao.getContractById(contractEntity.getParentId());
            contractRelativeDTO.setParentContractCode(parentContractEntity.getContractCode())
                    .setParentFactoryCode(parentContractEntity.getDeliveryFactoryCode())
                    .setParentSiteName(parentContractEntity.getSiteName());
            TradeTicketEntity originalContractTT = tradeTickService.getByGroupId(tradeTicketEntity.getGroupId(), tradeTicketEntity.getId());
            if (null != originalContractTT) {
                contractRelativeDTO.setParentContractNum(originalContractTT.getBeforeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                        .setOriginalContractTtId(originalContractTT.getId());
                contractRelativeDTO.setTradeType(originalContractTT.getTradeType());
                //拆分或转月的变更字段信息
                Map<String, String> modifyFieldMap = getModifyFieldMap();
                if (Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType(), TTTypeEnum.TRANSFER.getType(), TTTypeEnum.REVERSE_PRICE.getType()).contains(tradeTicketEntity.getType())) {
                    TradeTicketDTO tradeTicketDTO = tradeTickService.getTTDetailInfo(tradeTicketEntity.getId());
                    if (null != tradeTicketDTO) {
                        String modifyContent = "";
                        if (null != tradeTicketDTO.getContractModifyTTDTO()) {
                            modifyContent = StringUtils.isNotBlank(tradeTicketDTO.getContractModifyTTDTO().getContent()) ? tradeTicketDTO.getContractModifyTTDTO().getContent() : tradeTicketDTO.getContractModifyTTDTO().getModifyContent();
                        }
                        if (null != tradeTicketDTO.getContractTransferTTDTO()) {
                            modifyContent = StringUtils.isNotBlank(tradeTicketDTO.getContractTransferTTDTO().getContent()) ? tradeTicketDTO.getContractTransferTTDTO().getContent() : tradeTicketDTO.getContractTransferTTDTO().getModifyContent();
                        }
                        List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(modifyContent, CompareObjectDTO.class);
                        if (CollectionUtils.isNotEmpty(compareObjectDTOList)) {
                            if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == (tradeTicketEntity.getTradeType())) {
                                for (CompareObjectDTO compareObjectDTO : compareObjectDTOList) {
                                    String contractNum = "contractNum";
                                    if (contractNum.equals(compareObjectDTO.getName())) {
                                        if (StringUtils.isNotBlank(compareObjectDTO.getAfter())) {
                                            contractRelativeDTO.setContractNum(new BigDecimal(compareObjectDTO.getAfter()).setScale(3, RoundingMode.HALF_UP).toPlainString());
                                        }
                                        if (StringUtils.isNotBlank(compareObjectDTO.getBefore())) {
                                            contractRelativeDTO.setParentContractNum(new BigDecimal(compareObjectDTO.getBefore()).setScale(3, RoundingMode.HALF_UP).toPlainString());
                                        }
                                    }
                                }
                            }

                            String modifyFieldInfo = this.getModifyInfo(modifyFieldMap, compareObjectDTOList);
                            contractRelativeDTO.setModifyFieldInfo(modifyFieldInfo);
                        }
                    }
                }
                if (TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
                    contractRelativeDTO.setTradeType(tradeTicketEntity.getTradeType());
                    List<String> ignoreList = tradeTicketConvertUtil.getIgnoreList();
                    ignoreList.remove("salesType");
                    List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(parentContractEntity, contractEntity, ignoreList, new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(compareObjectDTOList)) {
                        String modifyFieldInfo = this.getModifyInfo(modifyFieldMap, compareObjectDTOList);
                        contractRelativeDTO.setModifyFieldInfo(modifyFieldInfo);
                    }
                }
            }
        }
        return contractRelativeDTO;
    }

    private String getModifyInfo(Map<String, String> modifyFieldMap, List<CompareObjectDTO> compareObjectDTOList) {
        compareObjectDTOList = compareObjectDTOList
                .stream()
                .filter(it -> {
                    return StringUtils.isNotBlank(modifyFieldMap.get(it.getName())) && !it.getBefore().equals(it.getAfter()) && StringUtils.isNotBlank(it.getAfter());
                })
                .collect(Collectors.toList());
        String modifyFieldInfo = compareObjectDTOList.stream().map(it -> {
            return modifyFieldMap.get(it.getName());
        }).collect(Collectors.joining(","));
        return modifyFieldInfo;
    }

    /**
     * 递归查询根合同
     *
     * @param sonContractEntity
     * @return
     */
    private ContractEntity getRootContract(ContractEntity sonContractEntity) {
        sonContractEntity = contractDao.getContractById(sonContractEntity.getParentId());
        if (0 != sonContractEntity.getParentId()) {
            sonContractEntity = this.getRootContract(sonContractEntity);
        }
        return sonContractEntity;
    }

    /**
     * 递归查询所有子合同信息
     *
     * @param rootContractDto
     * @param contractEntityList
     */
    private void getSonContractList(ContractRelativeDTO rootContractDto, List<ContractRelativeDTO> contractEntityList) {
        List<ContractEntity> sonContractList = contractDao.getContractByPid(rootContractDto.getContractId());
        if (CollectionUtils.isNotEmpty(sonContractList)) {
            log.info("合同溯源-子合同条数:" + sonContractList.size());
            List<ContractRelativeDTO> sonContractDTOList = sonContractList.stream()
                    .map(this::convertContractDto).collect(Collectors.toList());
            contractEntityList.addAll(sonContractDTOList);
            log.info("合同溯源-子合同总条数:" + sonContractList.size());
            for (ContractRelativeDTO sonContractDTO : sonContractDTOList) {
                this.getSonContractList(sonContractDTO, contractEntityList);
            }
        }
    }

    /**
     * 获取可修改的字段
     *
     * @return
     */
    private Map<String, String> getModifyFieldMap() {
        Map<String, String> modifyMap = new HashMap<>();
        modifyMap.put("salesType", "采销类型");
        modifyMap.put("signDate", "签订日期");
        modifyMap.put("customerName", "买方主体简称");
        modifyMap.put("supplierName", "卖方主体简称");
//        modifyMap.put("tradeType", "交易类型");
        modifyMap.put("contractType", "合同类型");
        modifyMap.put("domainCode", "期货合约");
        modifyMap.put("goodsId", "货物");
        modifyMap.put("contractNum", "总数量");
        modifyMap.put("packageWeight", "袋皮扣重");
        modifyMap.put("totalAmount", "合同总金额");
        modifyMap.put("paymentType", "付款方式");
        modifyMap.put("creditDays", "赊销账期");
        modifyMap.put("deliveryFactoryName", "交货工厂");
        modifyMap.put("deliveryType", "交货方式");
        modifyMap.put("shipWarehouseId", "发货库点");
        modifyMap.put("destination", "目的地");
        modifyMap.put("weightTolerance", "溢短装");
        modifyMap.put("deliveryStartTime", "开始交货日");
        modifyMap.put("deliveryEndTime", "截止交货日");
        modifyMap.put("priceEndTime", "点价截止日期");
        modifyMap.put("payConditionId", "付款条件代码");
        modifyMap.put("unitPrice", "含税单价");
        modifyMap.put("extraPrice", "基差价");
        modifyMap.put("forwardPrice", "期货价格");
        modifyMap.put("proteinDiffPrice", "蛋白价格");
        modifyMap.put("compensationPrice", "散粕补贴");
        modifyMap.put("optionPrice", "期权费");
        modifyMap.put("transportPrice", "运费");
        modifyMap.put("liftingPrice", "起吊费");
        modifyMap.put("delayPrice", "滞期费");
        modifyMap.put("temperaturePrice", "高温费");
        modifyMap.put("otherDeliveryPrice", "其他物流费");
        modifyMap.put("buyBackPrice", "和解款折价");
        modifyMap.put("complaintDiscountPrice", "客诉折价");
        modifyMap.put("transferFactoryPrice", "转厂补贴");
        modifyMap.put("otherPrice", "其他补贴");
        modifyMap.put("businessPrice", "商务补贴");
        modifyMap.put("fee", "手续费");
        modifyMap.put("depositRate", "履约保证金比例");
        modifyMap.put("addedDepositRate", "履约保证金点价后补缴");
        modifyMap.put("invoicePaymentRate", "发票后补缴货款比例");
        modifyMap.put("depositReleaseType", "履约保证金释放方式");
        modifyMap.put("weightCheck", "重量检验");
        modifyMap.put("memo", "备注");
        modifyMap.put("usage", "用途");
        return modifyMap;
    }

    /**
     * 获取可提货的合同信息
     *
     * @param deliveryApplyContractList 合同列表
     * @return
     */
    private List<ContractEntity> getContractDeliveryList(List<ContractEntity> deliveryApplyContractList, Boolean showZero) {

        // 批量获取lkg合同信息
        List<ContractEntity> batchLkgContractList = getBatchLkgContractList(deliveryApplyContractList);

        Map<Integer, ContractEntity> contractMap = new HashMap<>();
        // 计算合同的可提货数量
        batchLkgContractList.forEach(contractEntity -> {
            // 开单量
            BigDecimal billNum = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getTotalDeliveryNum(), contractEntity.getAllocateNum());

            contractEntity.setIsBilled(BigDecimalUtil.isGreaterThanZero(billNum) ? 1 : 0);

            // 可提货数量
            BigDecimal canDeliveryNum = BigDecimal.ZERO;

            // 可提货数量 = 合同数量 - 开单数量 - 申请提货数量
            canDeliveryNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, canDeliveryNum, contractEntity.getContractNum());
            canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, billNum);
            canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, contractEntity.getApplyDeliveryNum());

            contractEntity.setCanDeliveryNum(canDeliveryNum);

            if (showZero) {
                contractMap.put(contractEntity.getId(), contractEntity);
            } else if (BigDecimalUtil.isGreaterThanZero(canDeliveryNum)) {
                contractMap.put(contractEntity.getId(), contractEntity);
            }
        });

        // 按照原来的顺序返回
        List<ContractEntity> newApplyContractList = new ArrayList<>();
        for (ContractEntity contractEntity : deliveryApplyContractList) {
            ContractEntity newContractEntity = contractMap.get(contractEntity.getId());
            if (newContractEntity != null) {
                newApplyContractList.add(newContractEntity);
            }
        }
        // 再按照是否开单排序
        newApplyContractList.sort(Comparator.comparing(ContractEntity::getIsBilled).reversed());

        return newApplyContractList;
    }

    /**
     * 批量获取lkg合同信息
     *
     * @param deliveryApplyContractList 合同列表
     * @return
     */
    private List<ContractEntity> getBatchLkgContractList(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();

        // 提货查询方式： 1.多线程查询lkg 2.多线程查询本地数据库 3.单线程查询本地数据库+批量查询
        switch (this.deliveryQueryType) {
            case 1:
                newApplyContractList = getBatchLkgContractList1(deliveryApplyContractList);
                break;
            case 2:
                newApplyContractList = getBatchLkgContractList2(deliveryApplyContractList);
                break;
            case 3:
                newApplyContractList = getBatchLkgContractList3(deliveryApplyContractList);
                break;
            default:
                break;
        }
        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList1(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();

        // 多线程调用LKG
        CompletableFuture[] futures = new CompletableFuture[deliveryApplyContractList.size()];

        for (int i = 0; i < deliveryApplyContractList.size(); i++) {
            final int taskIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                log.info("Task " + taskIndex + " is running " + deliveryApplyContractList.get(taskIndex).getContractCode());

                // 调用LKG接口
                ContractEntity contractEntity = getContractById(deliveryApplyContractList.get(taskIndex).getId());
                if (null != contractEntity) {
                    newApplyContractList.add(contractEntity);
                }
            });
        }
        CompletableFuture.allOf(futures).join();

        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList2(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();

        // 多线程调用LKG
        CompletableFuture[] futures = new CompletableFuture[deliveryApplyContractList.size()];

        for (int i = 0; i < deliveryApplyContractList.size(); i++) {
            final int taskIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                log.info("Task " + taskIndex + " is running " + deliveryApplyContractList.get(taskIndex).getContractCode());

                // 调用LKG接口
                ContractEntity contractEntity = contractValueObjectService.getLocalLkgContractByContractId(deliveryApplyContractList.get(taskIndex).getId());
                if (null != contractEntity) {
                    newApplyContractList.add(contractEntity);
                }
            });
        }
        CompletableFuture.allOf(futures).join();
        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList3(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();

        List<Integer> contractIdList = deliveryApplyContractList.stream().map(ContractEntity::getId).collect(Collectors.toList());
        List<String> contractCodeList = deliveryApplyContractList.stream().map(ContractEntity::getContractCode).collect(Collectors.toList());

        // 将lkg合同信息存放map中
        List<LkgContractInfoEntity> lkgContractInfoList = contractValueObjectService.getLocalLkgContractByContractCodeList(contractCodeList);
        if (CollectionUtil.isEmpty(lkgContractInfoList)) {
            return newApplyContractList;
        }

        // lkg合同信息去重
        lkgContractInfoList = lkgContractInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LkgContractInfoEntity::getContractNumber))), ArrayList::new));

        Map<String, LkgContractInfoEntity> lkgContractInfoMap = lkgContractInfoList.stream().collect(Collectors.toMap(LkgContractInfoEntity::getContractNumber, lkgContractInfoEntity -> lkgContractInfoEntity));

        // 遍历contractEntityList 设置lkg合同信息
        List<ContractEntity> contractEntityList = contractDao.getContractListByIds(contractIdList);
        for (ContractEntity contractEntity : contractEntityList) {
            LkgContractInfoEntity lkgContractInfoEntity = lkgContractInfoMap.get(contractEntity.getContractCode());
            if (null != lkgContractInfoEntity && lkgContractInfoEntity.getStatus() != 7) {
                newApplyContractList.add(contractEntity.setTotalDeliveryNum(lkgContractInfoEntity.getContractFactOutCount())
                        .setTotalBillNum(lkgContractInfoEntity.getContractOutCount())
                        .setAllocateNum(lkgContractInfoEntity.getDbOrderCount()));
            }
        }
        return newApplyContractList;
    }

}
