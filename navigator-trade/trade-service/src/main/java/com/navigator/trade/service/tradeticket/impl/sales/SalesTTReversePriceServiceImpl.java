package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import org.springframework.stereotype.Component;

@Component("SBM_S_TT_REVERSE_PRICE,SBO_S_TT_REVERSE_PRICE")
public class SalesTTReversePriceServiceImpl extends SalesTTTransferServiceImpl {
    public SalesTTReversePriceServiceImpl() {
        ttTypeEnum = TTTypeEnum.REVERSE_PRICE;
        contractTradeTypeEnum = ContractTradeTypeEnum.REVERSE_PRICE_ALL;
        processorType = ProcessorTypeEnum.SBM_S_REVERSE_PRICE.getTtValue();
        logBizCodeEnum = LogBizCodeEnum.SUBMIT_SALES_TT;
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.SALES;
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getTransferTradeTicketEntity(ttdto.getSalesContractTTTransferDTO());
        injectionProperty(tradeTicketEntity);
        tradeTicketEntity.setContractSource(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue()) ? ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue() : ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue());
        tradeTicketEntity.setTradeType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue()) ? ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue() : ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue());
        return tradeTicketEntity;
    }

    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());

            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }
        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }


}
