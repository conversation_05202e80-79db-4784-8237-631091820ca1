package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 合同创建子类Logic 逻辑处理
 *
 * <AUTHOR>
 */
public interface CreateLogicService {

    /**
     * 补充合同的基本信息
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    void buildBaseInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity);

    /**
     * 补充业务信息，主要是从客户配置域获取合同创建的条件数据
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    void buildBizInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity);

    /**
     * 创建合同
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    void create(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity);

    /**
     * 创建结构化合同
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    void createAdditionalInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity);

    /**
     * 创建后操作
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    void afterCreateProcess(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity);

}
