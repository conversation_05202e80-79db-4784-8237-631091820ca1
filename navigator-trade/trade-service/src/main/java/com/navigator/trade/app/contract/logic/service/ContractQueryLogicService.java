package com.navigator.trade.app.contract.logic.service;

import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractMdmInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractRelativeDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:34
 * @Version 1.0
 */

public interface ContractQueryLogicService {

    /**
     * 根据条件获取合同 仓单合同|现货合同|采销区分|不同的业务类型
     *
     * @param queryDTO
     * @return
     */
    Result queryContract(QueryDTO<ContractQO> queryDTO);


    /**
     * 根据合同Id查询合同明细 | 现货 | 仓单
     *
     * @param contractId
     * @return
     */
    Result<ContractDetailVO> getContractDetailById(String contractId);

    /**
     * 根据合同Id查询合同明细 | 现货 | 仓单 - 不获取LKG的内容
     *
     * @param contractId
     * @return
     */
    Result<ContractDetailVO> getBasicContractDetailById(String contractId);

    /**
     * 根据合同编码获取合同 | 现货 | 仓单
     *
     * @param contractCode
     * @return
     */
    Result<ContractDetailVO> getContractDetailByCode(String contractCode);

    ContractDetailInfoDTO getContractDetailInfoDTO(Integer id);

    /**
     * 根据合同Id获取合同含税单价详情 | 现货 | 仓单
     *
     * @param contractId
     * @return
     */
    Result getContractUnitPriceDetail(String contractId);

    /**
     * 获取合同溯源信息 现货 | 仓单
     *
     * @param contractId
     * @return
     */
    List<ContractRelativeDTO> getContractTraceList(Integer contractId);

    /**
     * 仓单合同获取提货信息 | 仓单
     *
     * @param contractId
     * @return
     */
    List<ContractDeliveryVO> getDeliveryContractByContractId(Integer contractId);

    /**
     * 根据ID 获取合同信息
     *
     * @param id
     * @return
     */
    ContractEntity getContractById(Integer id);

    /**
     * 根据ID 获取合同基础信息
     *
     * @param id
     * @return
     */
    ContractEntity getBasicContractById(Integer id);

    /**
     * 根据合同编码获取合同数据
     *
     * @param contractCode
     * @return
     */
    ContractEntity getBasicContractByCode(String contractCode);

    /**
     * 根据仓单编码获取合同数据
     *
     * @param warrantCode
     * @return
     */
    List<ContractEntity> getContractByWarrantCode(String warrantCode);


    /**
     * 根据仓单采购合同获取关联的销售合同
     *
     * @param purchaseContract
     * @return
     */
    List<ContractEntity> getContractByPurchase(ContractEntity purchaseContract);

    /**
     * 根据条件查询结构化定价合同
     *
     * @param queryDTO
     * @return
     */
    Result queryContractStructure(QueryDTO<ContractBO> queryDTO);

    /**
     * 根据合约号获取当前客户可以分配的合同(分页查询)
     *
     * @param queryDTO
     * @return
     */
    Result pageContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 根据合约月查询合同信息
     *
     * @param queryContractDTO
     * @return
     */
    List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO);

    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @return
     */
    Result queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 根据合同编码查询是否存在合同信息
     *
     * @param contractCode
     * @return
     */
    Integer getContractIdByCode(String contractCode);

    /**
     * 根据合同IDS获取结构化信息
     *
     * @param contractIds
     * @return
     */
    List<ContractStructureEntity> getValidStructureContract(List<Integer> contractIds);

    /**
     * 根据合同Id获取电子合同
     *
     * @param contractId
     * @return
     */
    Result getContractPdfs(String contractId);

    /**
     * 查询合同是否可签章
     *
     * @param contractId 合同id
     * @return
     */
    boolean canSign(String contractId);

    /**
     * 查询客户的期货合约数据
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO);

    /**
     * 分组查询期货合约
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO);

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    Result futureContracts(QueryDTO<ContractFuturesDTO> queryDTO);

    /**
     * 查询合同拆分记录
     *
     * @param contractModifyDTO
     * @return
     */
    List<ContractModifyVO> getContractModifyLog(ContractModifyDTO contractModifyDTO);

    /**
     * 获取合同变更所需要的数量
     *
     * @param contractId
     * @return
     */
    ContractModifyNumVO getContractModifyNumInfo(Integer contractId);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return ConfirmPriceVO
     */
    List<ConfirmPriceVO> getConfirmPriceList(Integer contractId);

    /**
     * 根据合同ID获取提货列表信息
     *
     * @param contractIdList
     * @return
     */
    List<ContractEntity> getContractListByIds(List<Integer> contractIdList);

    /**
     * 提货申请获取合同列表信息
     *
     * @param deliveryApplyContractQO
     * @return
     */
    List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO);

    /**
     * 提货申请获取合同列表信息
     *
     * @param deliveryApplyContractQO
     * @return
     */
    List<ContractEntity> getDeliveryApplyContractListForAtlas(DeliveryApplyContractQO deliveryApplyContractQO);

    /**
     * 获取提货数据分组
     *
     * @param deliveryApplyContractQO
     * @return
     */
    List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO);

    /**
     * 获取尾巴关闭记录
     *
     * @param contractCode
     * @return
     */
    List<OperationDetailEntity> getCloseTailNumRecord(String contractCode);

    /**
     * 根据合同ID获取合同价格信息
     *
     * @param contractId 合同ID
     * @return
     */
    ContractPriceEntity getContractPriceEntityContractId(Integer contractId);

    /**
     * 根据仓单合同Id获取提货权记录信息
     *
     * @param contractId
     * @return
     */
    List<ContractVO> getCargoRightsContractById(Integer contractId);

    /**
     * 新增合同生成合同编号【抽离到公共的方法去】
     *
     * @param salesName       买/卖方主体简称
     * @param salesType       销售合同类型
     * @param goodsCategoryId 商品品类
     * @return
     */
    String genNewContractCode(String salesName, Integer salesType, Integer goodsCategoryId);

    /**
     * 校验某些品种下是否存在生效或过程中的合同
     *
     * @param category3List
     * @return
     */
    List<String> judgeCategory3InProcessContractForSite(List<Integer> category3List, String siteCode);

    /**
     * 根据合同编码获取合同已锁定的数量
     *
     * @param contractCode 合同编码
     * @return
     */
    BigDecimal getContractBlockedNum(String contractCode);

    /**
     * 根据合同ID和主版本号获取合同历史信息
     *
     * @param contractId  合同ID
     * @param mainVersion 主版本号
     * @return
     */
    ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion);

    /**
     * 根据合同编码获取合同MDM信息
     *
     * @param contractCode 合同编码
     * @return 合同MDM信息
     */
    ContractMdmInfoDTO getContractMdmInfo(String contractCode);

    List<ContractEntity> getContractByStatus(String status, List<String> contractNumbers);

    /**
     * 根据父合同ID获取源合同信息
     *
     * @param parentContractId 父合同ID
     * @return 合同类型
     */
    Integer getOriginalContractType(Integer parentContractId);
}
