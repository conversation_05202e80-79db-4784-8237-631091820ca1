package com.navigator.trade.app.tt.domain.service.processor;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TTTranferEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("TRANSFER")
public class TTTransferDomainProcessor extends AbstractTTDomainProcessor {

    @Autowired
    TtTranferDao ttTranferDao;

    public TTTransferDomainProcessor() {
        System.out.println("TTTransferDomainProcessor");

    }

    @Override
    void addTTSubEntity(TradeTicketDO tradeTicketDO) {

        //set value
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(tradeTicketDO.getTradeTicketEntity().getId());

        TTTranferEntity tranferEntity = (TTTranferEntity) ttSubEntity;

        TTTranferEntity oldTransferEntity = ttTranferDao.getTTTransferEntityByTTId(tranferEntity.getTtId());
        if (Objects.nonNull(oldTransferEntity)) {
            tranferEntity.setId(oldTransferEntity.getId());
        }
        ttTranferDao.save(tranferEntity);
    }

    @Override
    boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {
        int rtn = ttTranferDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        return rtn > 0;
    }
}
