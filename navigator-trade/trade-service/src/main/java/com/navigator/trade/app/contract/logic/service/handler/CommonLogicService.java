package com.navigator.trade.app.contract.logic.service.handler;


import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.common.dto.Result;
import com.navigator.koala.pojo.dto.CancelledWarrantDTO;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.SyncSwitchEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同域通用的方法 动作处理通用方法
 *
 * <AUTHOR>
 * @Date 2024-07-15
 */
public interface CommonLogicService {

    /**
     * 新增合同的操作记录
     *
     * @param contractEntity 合同信息
     * @param bizCodeEnum    操作枚举
     * @param data           数据
     * @param systemEnum     操作系统
     */
    void addContractOperationLog(ContractEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum);

    /**
     * 根据系统类型记录操作日志
     *
     * @param operationDetailDTO
     * @param systemEnum
     */
    void addContractOperationLog(OperationDetailDTO operationDetailDTO, Integer systemEnum);

    /**
     * 更新客户信息
     *
     * @param newCustomerId    新客户ID
     * @param newSupplierId    新供应商ID
     * @param originCustomerId 旧客户ID
     * @param originSupplierId 旧供应商ID
     * @param contractEntity   合同实体
     */
    void updateCustomerInfo(Integer originCustomerId, Integer originSupplierId,
                            Integer newCustomerId, Integer newSupplierId, ContractEntity contractEntity);

    /**
     * 判断客户是否被禁用
     *
     * @param contractEntity
     * @return
     */
    boolean isEnableCustomerStatus(ContractEntity contractEntity);

    /**
     * 获取转月次数
     *
     * @param customerId
     * @param category2
     * @return
     */
    ContractTransferCountDTO getContractTransferCount(Integer customerId, Integer category2, Integer category3);

    /**
     * 新的服务接口，根据账套来决定是同步到哪个系统
     *
     * @param contractEntity 合同实体
     * @param ttId           ttId 不存在传null
     * @param syncType       同步类型
     * @param syncSwitchEnum 是否同步到atlas
     */
    void syncContractInfo(ContractEntity contractEntity, Integer ttId, Integer syncType, SyncSwitchEnum syncSwitchEnum);

    /**
     * 同步定价单信息
     *
     * @param siteCode 账套Code
     * @param ttId     ttId
     */
    void syncTTPriceInfo(String siteCode, Integer ttId, Integer syncType);

    /**
     * 同步合同定价单信息
     *
     * @param ttId
     * @param contractEntity
     */
    void syncContractPriceUpdateInfo(Integer ttId, ContractEntity contractEntity);

    /**
     * 采购合同生效需要创建 仓单 属性的仓单
     *
     * @param contractEntity
     * @return
     */
    WarrantEntity createPurchaseWarrant(ContractEntity contractEntity, Integer warrantCategory, BigDecimal deliveryMarginAmount);

    /**
     * 合同生效|作废更新仓单数量
     *
     * @param contractEntity
     */
    void updateWarrantNum(ContractEntity contractEntity, Integer modifyType, BigDecimal changeWarrantCount, Integer warrantCancellationId);

    /**
     * 合同生效|作废更新仓单数量
     *
     * @param warrantCode
     */
    void updateWarranStatus(String warrantCode);

    /**
     * 生成注销记录
     *
     * @param warrant             仓单合同
     * @param cargoRights         货权合同
     * @param deliverys           提货合同
     * @param trades              贸易合同
     * @param purchase            采购合同
     * @param cancelledWarrantDTO 注销记录
     */
    public void cancelledWarrantContract(ContractEntity warrant,
                                         List<ContractEntity> cargoRights,
                                         List<ContractEntity> deliverys,
                                         List<ContractEntity> trades,
                                         ContractEntity purchase,
                                         CancelledWarrantDTO cancelledWarrantDTO);

    /**
     * 计算合同的含税单价
     *
     * @param priceDetailDTO
     * @param taxRate
     * @return
     */
    ContractUnitPriceVO calcContractUnitPrice(PriceDetailBO priceDetailDTO, BigDecimal taxRate);

    /**
     * 结构化合同关闭
     *
     * @param contractEntity
     */
    void closeContractStructureTT(ContractEntity contractEntity);

    /**
     * 获取合同Atlas 的可提量
     *
     * @param contractEntity
     * @return
     */
    Result<BigDecimal> getContractOpenQuantity(ContractEntity contractEntity);

    /**
     * 校验定价是否在盘面数据中
     * @param id
     * @return
     */
    boolean getNotAllocateByContractId(Integer id);
}
