package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.bean.BeanUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractSplitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.service.IContractPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/20
 * @Version 1.0
 */
@Slf4j
@Component
public class ContractPriceEntityConvert {

    @Autowired
    private IContractPriceService contractPriceService;

    public ContractPriceEntity add2ContractPriceEntity(PriceDetailBO priceDetailBO,String ttCode,String ContractCode) {
        //保存合同价格信息
        ContractPriceEntity contractPriceEntity = BeanConvertUtils.map(ContractPriceEntity.class, priceDetailBO);
        contractPriceEntity.setTtCode(ttCode);
        contractPriceEntity.setContractCode(ContractCode);
        return contractPriceEntity;
    }

    public ContractPriceEntity writeOff2ContractPriceEntity(PriceDetailBO priceDetailBO, TradeTicketEntity ttEntity) {
        //保存合同价格信息
        ContractPriceEntity contractPriceEntity = BeanConvertUtils.map(ContractPriceEntity.class, priceDetailBO);
        contractPriceEntity.setTtCode(ttEntity.getCode());
        contractPriceEntity.setContractId(ttEntity.getContractId());
        contractPriceEntity.setContractCode(ttEntity.getContractCode());
        return contractPriceEntity;
    }

    public ContractPriceEntity split2ContractPriceEntity(TTDTO ttdto, TTModifyEntity ttModifyEntity) {
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();

        // 除普通之外的拆分 || 保存不生成合同价格详情
        if (salesContractSplitTTDTO.getAddedSignatureType() != 0 || ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            return null;
        }

        return BeanUtil.toBean(ttModifyEntity, ContractPriceEntity.class);
    }

    public ContractPriceEntity revise2ContractPriceEntity(TTDTO ttdto, TTModifyEntity ttModifyEntity) {

        // 保存不生成合同价格详情
        if (ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            return null;
        }

        return BeanUtil.toBean(ttModifyEntity, ContractPriceEntity.class);
    }
}
