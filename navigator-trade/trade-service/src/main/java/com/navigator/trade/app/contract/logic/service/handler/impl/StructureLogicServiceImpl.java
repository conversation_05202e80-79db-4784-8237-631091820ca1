package com.navigator.trade.app.contract.logic.service.handler.impl;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 合同结构化定价的业务Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class StructureLogicServiceImpl implements StructureLogicService {

    /**
     * 合同域查服务
     */
    @Autowired
    ContractQueryDomainService contractQueryDomainService;

    /**
     * 结构化定价单DAO - 考虑移到Domain
     */
    @Autowired
    ContractStructureDao contractStructureDao;

    /**
     * 跨域头寸服务 价格分配服务
     */
    @Autowired
    PriceAllocateFacade priceAllocateFacade;

    @Override
    public Boolean verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO) {

        //根据客户,合约,品类查询是否存在结构化定价
        List<ContractStructureEntity> contractStructureEntities = contractQueryDomainService.verifyContractStructureNum(verifyContractStructureNumDTO);
        if (contractStructureEntities.isEmpty()) {
            return false;
        }
        //如果存在结构化定价校验数量
        BigDecimal num = BigDecimal.ZERO;

        for (ContractStructureEntity contractStructureEntity : contractStructureEntities) {
            //结构化定价总量
            BigDecimal totalNum = contractStructureEntity.getTotalNum();
            //总量加上累积成交量
            totalNum = totalNum.add(contractStructureEntity.getCumulativeDealNum());

            //查询分配通过的分配单
            List<PriceAllocateEntity> priceAllocateEntities = priceAllocateFacade.getAllocateByContractStructureId(contractStructureEntity.getContractId());
            //分配单为空时 结束本次循环
            if (priceAllocateEntities.isEmpty()) {
                num = num.add(totalNum);
                continue;
            }
            //查询
            for (PriceAllocateEntity priceAllocateEntity : priceAllocateEntities) {
                totalNum = totalNum.subtract(priceAllocateEntity.getAllocateNum());
            }

            num = num.add(totalNum);
        }
        //根据客户,合约,品类查询合同
        BigDecimal contractNums = BigDecimal.ZERO;
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO
                .setCustomerId(verifyContractStructureNumDTO.getCustomerId().toString())
                .setCompanyId(verifyContractStructureNumDTO.getCompanyId())
                .setGoodsCategoryId(verifyContractStructureNumDTO.getGoodsCategoryId())
                .setDomainCode(verifyContractStructureNumDTO.getDomainCode())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setContractType(Arrays.asList(ContractTypeEnum.JI_CHA.getValue()))
                .setPriceType(PriceTypeEnum.TRANSFER_MONTH.getValue());
        // 这块还需要再优化一下
        List<ContractEntity> contractEntities = contractQueryDomainService.queryContractsFuturesNum(contractFuturesDTO);

        for (ContractEntity contractEntity : contractEntities) {
            BigDecimal contractNum = contractEntity.getContractNum();
            contractNum = contractNum.subtract(contractEntity.getTotalPriceNum());

            contractNums = contractNums.add(contractNum);
        }
        //合同可操作数量减去结构化定定价量
        BigDecimal operationNum = contractNums.subtract(num);
        //减去给的合同数量
        operationNum = operationNum.subtract(verifyContractStructureNumDTO.getContractNum());
        //小于0
        if (operationNum.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }

        return false;
    }

}
