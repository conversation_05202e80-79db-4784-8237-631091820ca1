package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("TRANSFER_HANDLER")
public class TTTransferSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        Integer contractId = salesContractTTTransferDTO.getContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        List<CompareObjectDTO> compareObjectDTOList = getTransferCompareObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractTTTransferDTO.setModifyContent(modifyContent);

        //获取前后字段
        PriceDetailBO priceDetailBO = ttdto.getPriceDetailBO();
        List<CompareObjectDTO> contentDTOList = getTransferContentObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity, priceDetailBO);
        String content = JSON.toJSONString(contentDTOList);
        salesContractTTTransferDTO.setContent(content);
        //初始化交易、销售类型、合同来源
        Integer tradeType = null;
        Integer contractSource = null;


        // 判断是否是销售合同
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        // 新合同的TT编号
        String code = isSales ? sequenceUtil.generateSpotSalesTTCode() : sequenceUtil.generateSpotPurchaseTTCode();
        switch (TTTranferTypeEnum.getByValue(salesContractTTTransferDTO.getType())) {
            case TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_ALL.getValue();
                contractSource = ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue();
                //code = getSalesTTCode(contractId);
                break;
            case PART_TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_PART.getValue();
                contractSource = ContractActionEnum.TRANSFER_CONFIRM.getActionValue();
                break;
            case REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue();
                break;
            case PART_REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue();
                break;
            default:
                break;
        }
        salesContractTTTransferDTO.setTradeType(tradeType);

        salesContractTTTransferDTO.setSalesType(contractEntity.getSalesType());
        salesContractTTTransferDTO.setStatus(TTStatusEnum.APPROVING.getType());
        salesContractTTTransferDTO.setContractSource(contractSource);
        //协议签署状态
        salesContractTTTransferDTO.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        salesContractTTTransferDTO.setUserId(JwtUtils.getCurrentUserId());

        // add by zengshl
        if (BuCodeEnum.WT.getValue().equals(salesContractTTTransferDTO.getBuCode())
                && !(WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(salesContractTTTransferDTO.getWarrantTradeType()))) {
            salesContractTTTransferDTO.setStatus(TTStatusEnum.DONE.getType());
        }
        //生成TT编号
        salesContractTTTransferDTO.setCode(code);

        ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
    }

    private List<CompareObjectDTO> getTransferContentObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        //对比价格字段
        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
        BigDecimal newExtraPrice = BigDecimal.ZERO;
        if (salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())) {
            newExtraPrice = salesContractTTTransferDTO.getPrice();
        } else {
            newExtraPrice = contractPriceEntity.getExtraPrice().add(salesContractTTTransferDTO.getPrice());
        }
        newContractPriceEntity.setExtraPrice(newExtraPrice);

        List<String> manualList = getManualList();
        List<CompareObjectDTO> priceList = BeanCompareUtils.getFields(contractPriceEntity, newContractPriceEntity, null, manualList);
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        ContractEntity newContractEntity = new ContractEntity();
        BeanUtil.copyProperties(contractEntity, newContractEntity);
        BeanUtil.copyProperties(salesContractTTTransferDTO, newContractEntity);
        newContractEntity.setPriceEndType(salesContractTTTransferDTO.getPriceEndType());
        newContractEntity.setPriceEndTime(salesContractTTTransferDTO.getPriceEndTime());
        contractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(contractEntity.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(newContractEntity.getSignDate());
        newContractEntity.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        ignoreList.add("extraPrice");

        // BUGFIX：case-1003043 合同转月申请撤回后含税单价不正确 Author: Mr 2025-03-18 start
        ContractEntity compareContractEntity = BeanConvertUtils.map(ContractEntity.class, contractEntity);

        // 重新汇总原合同的含税单价 - (对于不生成子合同的合同，需要用使用原合同的含税单价)
        PriceDetailBO oldPriceDetail = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);

        // 含税单价
        ContractUnitPriceVO contractUnitPriceVO = contractPriceService.calcContractUnitPrice(oldPriceDetail, contractEntity.getTaxRate());
        BigDecimal totalAmount = contractUnitPriceVO.getUnitPrice().multiply(contractEntity.getContractNum());
        BigDecimal depositAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, totalAmount, BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));

        compareContractEntity
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setTotalAmount(totalAmount)
                .setDepositAmount(depositAmount);

        // 比较获取不同字段返回list，转换成json
        // List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(contractEntity, newContractEntity, ignoreList, manualList);
        List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(compareContractEntity, newContractEntity, ignoreList, manualList);
        // BUGFIX：case-1003043 合同转月申请撤回后含税单价不正确 Author: Mr 2025-03-18 End
        compareObjectDTOS.addAll(priceList);
        return compareObjectDTOS;
    }

    private List<CompareObjectDTO> getTransferCompareObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity) {
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        List<String> manualList = getManualList();
        SalesContractTTTransferDTO originalDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, originalDTO);
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractTTTransferDTO.getSignDate());
        salesContractTTTransferDTO.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        // 比较获取不同字段返回list，转换成json
        return BeanCompareUtils.compareFields(originalDTO, salesContractTTTransferDTO, ignoreList, manualList);
    }

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.TRANSFER.equals(ttTypeEnum);
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 1. 初始化
        initDTO(ttdto);

        // 2. 转换
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.transfer2TradeTicketDO(ttdto);

        log.info("转月TT价格信息===================================={}", FastJsonUtils.getBeanToJson(tradeTicketDO.getContractPriceEntity()));
        // 3. 保存至数据库
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);

        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);

        TradeTicketEntity ticketEntity = tradeTicketDO.getTradeTicketEntity();

        // 4. 提交审批【TT完成也是要调用-出审批记录】
        ttApproveHandler.startTTApprove(ticketEntity.getId(), ttdto, null);

        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        if (salesContractTTTransferDTO != null) {
            // 5. 隐藏TT
            ttDomainService.dealGroupTT(ticketEntity.getId(),
                    ticketEntity.getType(),
                    salesContractTTTransferDTO.getAddedSignatureType());

            // 6. 返回结果 - 前端展示

            TTQueryVO ttQueryVO = new TTQueryVO();
            ttQueryVO.setSourceFlag(salesContractTTTransferDTO.getAddedSignatureType() != 0 ? 1 : 2)
                    .setContractCode(ticketEntity.getContractCode())
                    .setCode(ticketEntity.getCode())
                    .setTtId(ticketEntity.getId());
            ttQueryVOS.add(ttQueryVO);
            salesContractTTTransferDTO.setTtId(ticketEntity.getId());
        }
        return ttQueryVOS;
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTTranferEntity ttTranferEntity = (TTTranferEntity) tradeTicketDO.getTtSubEntity();
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        if (ttTranferEntity.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())
//                || ttTranferEntity.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())
        ) {
            String modifyContent = ttTranferEntity.getContent();
            List<CompareObjectDTO> list = tradeTicketConvertUtil.getReviseCompareList(modifyContent, tradeTicketEntity, ttTranferEntity);
            ttDetailVO.setDetailType("11");
            ttDetailVO.setCompareObjectDTOList(list);
        } else {
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());

            PriceDetailVO priceDetailVO = new PriceDetailVO();
            BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
            BeanUtils.copyProperties(ttTranferEntity, ttQueryDetailVO);
            BeanUtils.copyProperties(contractEntity, ttQueryDetailVO);
            if (contractPriceEntity != null) {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
            ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

            //合同类型
            if (null != tradeTicketEntity.getContractType()) {
                ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
            }

            //卖家
            if (null != ttTranferEntity.getSupplierId()) {
                ttQueryDetailVO.setSupplierId(String.valueOf(ttTranferEntity.getSupplierId()));
            }

            //买家
            if (null != ttTranferEntity.getCustomerId()) {
                ttQueryDetailVO.setCustomerId(String.valueOf(ttTranferEntity.getCustomerId()));
            }
            ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttTranferEntity.getCustomerId());
            if (null != customerDTO) {
                ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
                ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
                ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS().isEmpty() ? null : customerDTO.getCustomerBankDTOS());
            }
            // 获取仓单类型
            if (ObjectUtil.isNotEmpty(contractEntity.getWarrantId())) {
                Result result = warrantFacade.queryWarrantByID(contractEntity.getWarrantId());
                WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
                if (ObjectUtil.isNotEmpty(warrantEntity)) {
                    ttQueryDetailVO.setWarrantCategory(warrantEntity.getCategory())
                            .setDepositPaymentType(warrantEntity.getDepositPaymentType());
                }
            }
            //商品信息
            if (null != ttTranferEntity.getGoodsCategoryId()) {
                ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttTranferEntity.getGoodsCategoryId()));
            }

            //商务
            if (null != tradeTicketEntity.getOwnerId()) {
                ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
                EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
                if (null != employEntity) {
                    ttQueryDetailVO.setOwnerName(employEntity.getName());
                }
            }

            //创建人
            if (null != tradeTicketEntity.getCreatedBy()) {
                EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
                if (null != employEntity) {
                    ttQueryDetailVO.setCreatedBy(employEntity.getName());
                }
            }
            //应付履约保证金状态
            if (null != contractEntity.getDepositAmount()) {
                int depositAmountStatus = contractEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
                ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
            }

            // 履约保证金释放方式
            ttQueryDetailVO.setDepositUseRule(contractEntity.getDepositReleaseType());
            // 点价截止日期
            ttQueryDetailVO.setPriceEndTime(contractEntity.getPriceEndTime());

            if (null != contractEntity.getInvoiceType()) {
                ttQueryDetailVO.setInvoiceType(contractEntity.getInvoiceType());
                ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
            }
            if (null != ttQueryDetailVO.getDeliveryType()) {
                DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
                if (null != deliveryTypeEntity) {
                    ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
                }
            }

            //查询工厂信息
            ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, contractEntity.getShipWarehouseId());
            if (StringUtils.isNotBlank(contractEntity.getShipWarehouseValue())) {
                ttQueryDetailVO.setShipWarehouseName(contractEntity.getShipWarehouseValue());
            }
            //查询配置名称
            //目的地
            String destinationName = ttQueryDetailVO.getDestination();
            if (StringUtils.isNumeric(destinationName)) {
                SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
                destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
            }
            ttQueryDetailVO.setDestinationName(destinationName);

            //重量检测
            if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
                if (systemRuleItemEntity != null) {
                    ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
                }
            }
            //袋皮扣重
            if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
                if (systemRuleItemEntity != null) {
                    ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
                }
            }
            //企标文件编号
            if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
                SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
                ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

            }
            if (tradeTicketEntity.getUsage() != null) {
                ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
            }
            String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
            recordTTQuery(data, LogBizCodeEnum.QUERY_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

            ttDetailVO.setDetailType("0");
            ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        }
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(tradeTicketEntity.getContractCode())
                .setContractId(tradeTicketEntity.getContractId())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));

        return ttDetailVO;
    }
}
