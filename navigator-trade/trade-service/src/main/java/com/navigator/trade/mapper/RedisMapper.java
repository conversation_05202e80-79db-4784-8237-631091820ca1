package com.navigator.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.trade.pojo.entity.RedisEntity;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface RedisMapper extends BaseMapper<RedisEntity> {

    @Select("select distinct(version) from dba_redis")
    List<String> getVersionList();

    @Update("UPDATE dba_redis SET redis_value = CAST ( CAST ( redis_value AS INT ) + 1 AS VARCHAR (255)) WHERE version = 'LOCK' and  redis_key = #{key} and redis_value = #{value}")
    Integer updateRedis(@RequestParam("key") String key, @RequestParam("value") String value);

    @Update("INSERT INTO [dba_redis]( [redis_key], [redis_value], [created_time], [version]) VALUES ( #{key},'1', CURRENT_TIMESTAMP, 'LOCK')")
    Integer insertRedis(@RequestParam("key") String key);

    @Select("select redis_value from [dba_redis] where version = 'LOCK' and  redis_key = #{key} ")
    String getLockValue(@RequestParam("key") String key);
}
