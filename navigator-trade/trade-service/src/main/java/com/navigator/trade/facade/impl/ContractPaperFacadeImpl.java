package com.navigator.trade.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.trade.facade.ContractPaperFacade;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.service.IContractPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/26 11:02
 */
@RestController
public class ContractPaperFacadeImpl implements ContractPaperFacade {

    @Autowired
    private IContractPaperService iContractPaperService;

    @Override
    public Result getContractPaper(Integer contractSignId) {
        return Result.success(iContractPaperService.getContractPaper(contractSignId));
    }

    @Override
    public boolean saveContractPaper(ContractPaperDTO contractPaperDTO) {
        return iContractPaperService.saveContractPaper(contractPaperDTO);
    }

}
