package com.navigator.trade.app.tt.domain.service;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;

/**
 * <AUTHOR>
 * @Description TT领域服务
 * @Date 2024/7/14 16:16
 * @Version 1.0
 */

public interface TTDomainService {

    TradeTicketDO createTradeTicketDO2(TradeTicketDO tradeTicketDO);

    /**
     * 创建TT
     *
     * @param tradeTicketDO TT领域模型
     * @return 主TTid
     */
    TradeTicketDO createTradeTicketDO(TradeTicketDO tradeTicketDO);

    /**
     * 更新TT合同id
     *
     * @param tradeTicketDO TT领域模型
     * @return true 更新成功
     */
    Boolean updateContractId(TradeTicketDO tradeTicketDO);

    /**
     * 更新TT
     *
     * @param tradeTicketDO TT领域模型
     * @return true 更新成功
     */
    Boolean updateTradeTicketDO(TradeTicketDO tradeTicketDO);

    /**
     * 更新主TT
     *
     * @param tradeTicketEntity 主TT
     * @return true 更新成功
     */
    Boolean updateTradeTicketEntity(TradeTicketEntity tradeTicketEntity);

    /**
     * 更新子TT
     *
     * @param ttSubEntity 子TT
     * @return true 更新成功
     */
    Boolean updateTTBaseEntity(TTSubEntity ttSubEntity);

    /**
     * 保存TT定价单信息
     *
     * @param ttPriceEntity TT定价单信息
     * @return true 保存成功
     */
    Boolean saveTtPrice(TTPriceEntity ttPriceEntity);

    /**
     * 更新TT定价单信息
     *
     * @param ttPriceEntity TT定价单信息
     * @return true 更新成功
     */
    Boolean updateTtPrice(TTPriceEntity ttPriceEntity);

    /**
     * 隐藏TT
     *
     * @param ttId               TTid
     * @param ttType             TT类型
     * @param addedSignatureType 添加签名类型
     */
    void dealGroupTT(Integer ttId, Integer ttType, Integer addedSignatureType);

    /**
     * 更新TT中协议的内容
     *
     * @param ttId               TT编号
     * @param contractSignEntity 合同签署信息
     */
    void updateSignInfo(Integer ttId, ContractSignEntity contractSignEntity);

    /**
     * 更新修改、拆分保存的TT信息
     *
     * @param ttdto
     * @param ticketEntity TT信息
     */
    void updateSaveModifyTTInfo(TTDTO ttdto, TradeTicketEntity ticketEntity);

    /**
     * 更新合同定价 定价完成
     *
     * @param priceEntity
     * @param contractEntity
     */
    void updateTtPriceComplete(TTPriceEntity priceEntity, ContractEntity contractEntity);

    /**
     * 更具TTID作废TT
     *
     * @param type
     * @param memo
     * @param ttId
     */
    void invalidTTById(int type, String memo, Integer ttId);

    /**
     * 更新TT transfer中子TT的allocateId
     *
     * @param contractId 合同id
     * @param allocateId 子TT的allocateId
     */
    boolean updatePriceAllocateId(Integer contractId, Integer allocateId);

    /**
     * 更新TTADD仓单信息
     *
     * @param ttId
     * @param warrantId
     * @param warrantCode
     * @return
     */
    Boolean updateTTAddWarrantId(Integer ttId, Integer warrantId, String warrantCode);


    /**
     * 根据合同id修改price
     *
     * @param contractPriceEntity
     * @return
     */
    boolean updatePriceByContractId(ContractPriceEntity contractPriceEntity);

    /**
     * 修改转月,反点价单
     *
     * @param ttTranferEntity
     * @return
     */
    Boolean updateTTTranferById(TTTranferEntity ttTranferEntity);

    /**
     * 修改定价单
     *
     * @param ttPriceEntity
     * @return
     */
    Boolean updateTTPriceById(TTPriceEntity ttPriceEntity);
}
