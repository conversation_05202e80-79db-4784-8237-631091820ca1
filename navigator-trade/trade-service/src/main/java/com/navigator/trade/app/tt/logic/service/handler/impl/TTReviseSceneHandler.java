package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */
@Slf4j
@Component("REVISE_HANDLER")
public class TTReviseSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();
        PriceDetailBO priceDetailBo = ttdto.getPriceDetailBO();
        Integer sourceContractId = salesContractReviseTTDTO.getSourceContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(sourceContractId);
        //salesContractReviseTTDTO.setSupplierId(contractEntity.getSupplierId());
        //获取变更字段
        List<CompareObjectDTO> compareObjectDTOList = getReviseCompareObjectDTO(salesContractReviseTTDTO, priceDetailBo, sourceContractId);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractReviseTTDTO.setModifyContent(modifyContent);

        //获取前后字段
        List<CompareObjectDTO> contentDTOList = getContentList(salesContractReviseTTDTO, priceDetailBo, sourceContractId);
        String content = JSON.toJSONString(contentDTOList);
        salesContractReviseTTDTO.setContent(content);

        // 判断是否是销售合同
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();

        boolean isCustomerChanged = ttdto.getContractTradeType() == ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue();

        int contractSource = isCustomerChanged ? ContractActionEnum.REVISE_CUSTOMER.getActionValue() : ContractActionEnum.REVISE.getActionValue();
        int tradeType = isCustomerChanged ? ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() : ContractTradeTypeEnum.REVISE_NORMAL.getValue();

        if (isCustomerChanged) {
            salesContractReviseTTDTO.setContractId(salesContractReviseTTDTO.getSonContractId());
        }

        // 编号生成
        String code = getReviseTTCode(isSales, sourceContractId, ttdto.getSubmitType(), salesContractReviseTTDTO.getAddedSignatureType(), changeFlag(modifyContent, compareObjectDTOList));

        salesContractReviseTTDTO
                .setTtType(TTTypeEnum.REVISE.getType())
                .setUserId(JwtUtils.getCurrentUserId())
                .setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setStatus(TTStatusEnum.NEW.getType())
                .setSalesType(contractEntity.getSalesType())
                .setCode(code)
                .setContractSource(contractSource)
                .setTradeType(tradeType);

        ttdto.setSalesContractReviseTTDTO(salesContractReviseTTDTO);
    }

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.REVISE.equals(ttTypeEnum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();

        // 1. 初始化
        initDTO(ttdto);

        // 2. Convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.revise2TradeTicketDO(ttdto);

        arrangeContext.setTradeTicketDO(tradeTicketDO);

        // 3. 保存至数据库
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);
        TradeTicketEntity ticketEntity = tradeTicketDO.getTradeTicketEntity();

        // 4. 处理修改保存的数据
        ttDomainService.updateSaveModifyTTInfo(ttdto, ticketEntity);

        // 5. 提交审批
        startTTApprove(ttdto, ticketEntity);
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();

        if (salesContractReviseTTDTO != null) {
            // 6. 隐藏TT
            ttDomainService.dealGroupTT(ticketEntity.getId(),
                    salesContractReviseTTDTO.getTtType(),
                    salesContractReviseTTDTO.getAddedSignatureType());

            // 7. 返回结果-前端展示
            // 普通修改 原合同 新tt
            // 主体修改 原合同
            //         新合同 新tt
            String ttCode = ttdto.getContractTradeType() == ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue()
                    && salesContractReviseTTDTO.getAddedSignatureType() == -1 ? "" : ticketEntity.getCode();

            ttQueryVO
                    .setSourceFlag(salesContractReviseTTDTO.getAddedSignatureType() != 0 ? 1 : 2)
                    .setContractCode(ticketEntity.getContractCode())
                    .setCode(ttCode)
                    .setTtId(ticketEntity.getId());

            salesContractReviseTTDTO.setTtId(ticketEntity.getId());
            ttQueryVOS.add(ttQueryVO);
        }
        return ttQueryVOS;
    }

    private void startTTApprove(TTDTO ttdto, TradeTicketEntity tradeTicketEntity) {
        if (ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            log.info("修改保存的TT不发起审批，ttId:{}", tradeTicketEntity.getId());
            return;
        }

        boolean shouldNotApprove = false;

        // 基差变一口价合同,不发起审批
        if (ttdto.getSalesContractReviseTTDTO().getModifyContent().contains("contractType")) {
            List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(ttdto.getSalesContractReviseTTDTO().getModifyContent(), CompareObjectDTO.class);
            shouldNotApprove = compareObjectDTOList.stream()
                    .anyMatch(i -> "contractType".equalsIgnoreCase(i.getName())
                            && i.getBefore().equalsIgnoreCase(String.valueOf(ContractTypeEnum.JI_CHA.getValue()))
                            && i.getAfter().equalsIgnoreCase(String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue())));
            log.info("operateTT.revise.notCreate.list:{}", JSON.toJSONString(compareObjectDTOList));
        }

        // 基差暂定价、暂定价合同 定价完成，不发起审批
        if (!shouldNotApprove) {
            List<CompareObjectDTO> contentList = JSON.parseArray(ttdto.getSalesContractReviseTTDTO().getContent(), CompareObjectDTO.class);
            List<CompareObjectDTO> contractCodeList = contentList.stream().filter(i -> "contractCode".equalsIgnoreCase(i.getName())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(contractCodeList)) {
                String contractCode = contractCodeList.get(0).getAfter();
                List<CompareObjectDTO> contractTypeList = contentList.stream().filter(i -> "contractType".equalsIgnoreCase(i.getName())).collect(Collectors.toList());
                log.info("operateTT.revise.notCreate.contractCode:{}", contractCode);
                if (CollectionUtil.isNotEmpty(contractTypeList) &&
                        !String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()).equalsIgnoreCase(contractTypeList.get(0).getAfter())
                        && ttdto.getSalesContractReviseTTDTO().getReviseType() == 2) {
                    shouldNotApprove = true;
                }
            }
        }
        log.info("operateTT.revise.notCreate.flag:{}", shouldNotApprove);

        if (shouldNotApprove) {
            // 更新TT状态
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.DONE.getType(), TTApproveStatusEnum.WITHOUT_APPROVE.getValue(), tradeTicketEntity.getCode());
            //ttdto.setSalesContractReviseTTDTO(null);
            return;
        }

        ttApproveHandler.startTTApprove(tradeTicketEntity.getId(), ttdto, null);
    }


    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTModifyEntity ttModifyEntity = (TTModifyEntity) tradeTicketDO.getTtSubEntity();

        TTDetailVO ttDetailVO = new TTDetailVO();
        String modifyContent = ttModifyEntity.getContent();

        List<CompareObjectDTO> list = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        ttDetailVO.setDetailType("10");
        ttDetailVO.setCompareObjectDTOList(list);

        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, priceDetailVO);
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(ttModifyEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(ttModifyEntity.getPriceEndTime());

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttModifyEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttModifyEntity.getSupplierId()));
        }

        //买家
        if (null != ttModifyEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttModifyEntity.getCustomerId()));
        }
        // 获取仓单类型|仓单合同基差定价完成有涉及
        if (ObjectUtil.isNotEmpty(ttModifyEntity.getWarrantId()) && ttModifyEntity.getWarrantId() != 0) {
            Result result = warrantFacade.queryWarrantByID(ttModifyEntity.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            if (ObjectUtil.isNotEmpty(warrantEntity)) {
                ttQueryDetailVO.setWarrantCategory(warrantEntity.getCategory())
                        .setDepositPaymentType(warrantEntity.getDepositPaymentType());
            }
        }
        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttModifyEntity.getDepositAmount()) {
            int depositAmountStatus = ttModifyEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttModifyEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttModifyEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttQueryDetailVO.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        if (null != ttModifyEntity.getDepositRate()) {
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateFacade.getCustomerDepositRateById(ttModifyEntity.getDepositRate());
            if (null != customerDepositRateEntity) {
                ttQueryDetailVO.setDepositRateValue(customerDepositRateEntity.getDepositRate());
            }
        }

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttModifyEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttModifyEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttModifyEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }


        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ContractEntity originalContractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setSourceType(tradeTicketEntity.getSourceType())
                .setOriginContractNum(originalContractEntity.getContractNum())
                .setOriginContractType(originalContractEntity.getContractType())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        return ttDetailVO;
    }

    public boolean changeFlag(String modifyContent, List<CompareObjectDTO> compareObjectDTOList) {
        // 基差转一口价
        if (modifyContent.contains("contractType")) {
            List<CompareObjectDTO> list = compareObjectDTOList.stream().filter(i -> "contractType".equalsIgnoreCase(i.getName())
                    && i.getAfter().equalsIgnoreCase(String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()))
                    && i.getBefore().equalsIgnoreCase(String.valueOf(ContractTypeEnum.JI_CHA.getValue()))).collect(Collectors.toList());
            return CollectionUtil.isNotEmpty(list);
        }
        return false;
    }

    private String getReviseTTCode(boolean isSales, Integer sourceContractId, Integer submitType, Integer addedSignatureType, boolean priceCompleted) {
        // 生成TT编号
        String code = isSales ? sequenceUtil.generateSpotSalesTTCode() : sequenceUtil.generateSpotPurchaseTTCode();

        // 原合同TT直接生成新的TT编号
        if (addedSignatureType == -1) {
            return code;
        }

        // 基差变一口价的TT
        if (priceCompleted) {
            return code;
        }

        // 原合同的TT编号
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(sourceContractId);

        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                // 合同基差转一口价外的TT
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        // 保存的TT
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());

        // 不存在TT记录
        if (tradeTicketEntities.isEmpty()) {
            return code;
        }

        // 保存拆分新生成-001 保存提交则使用最新的一条保存的记录
        if (submitType == SubmitTypeEnum.SAVE_SUBMIT.getValue()) {
            return tradeTicketEntities.get(0).getCode();
        }
        return sequenceUtil.generateSpotChildTTCode(tradeTicketEntities.get(0).getCode());
    }

    private List<CompareObjectDTO> getContentList(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);

        /*GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO()
                .setSupplierId(salesContractReviseTTDTO.getSupplierId())
                .setCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                .setPackageId(salesContractReviseTTDTO.getGoodsPackageId())
                .setSpecId(salesContractReviseTTDTO.getGoodsSpecId());
        GoodsInfoVO goodsInfoVO = goodsFacade.acquireGoodsInfo(goodsSpecDTO);

        salesContractReviseTTDTO.setGoodsId(goodsInfoVO.getGoodsId());*/
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.getFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractReviseTTDTO.getSignDate());
        salesContractReviseTTDTO.setSignDate(signDate);
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.getFields(originalDTO, salesContractReviseTTDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }

    private List<CompareObjectDTO> getReviseCompareObjectDTO(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);

        /*GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO()
                .setSupplierId(salesContractReviseTTDTO.getSupplierId())
                .setCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                .setPackageId(salesContractReviseTTDTO.getGoodsPackageId())
                .setSpecId(salesContractReviseTTDTO.getGoodsSpecId());
        GoodsInfoVO goodsInfoVO = goodsFacade.acquireGoodsInfo(goodsSpecDTO);

        salesContractReviseTTDTO.setGoodsId(goodsInfoVO.getGoodsId());*/
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        manualList.remove("signDate");
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.compareFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractReviseTTDTO.getSignDate());
        salesContractReviseTTDTO.setSignDate(signDate);
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(originalDTO, salesContractReviseTTDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }

}
