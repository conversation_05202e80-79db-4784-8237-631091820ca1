package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.BusinessUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.future.constant.ConfigurationConstants;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CreateLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CreateStructureLogicService;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.contractsign.IContractSignService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.UUID;

/**
 * 仓单注销的Logic 逻辑处理|直接处理仓单合同创建|现货合同创建|采销日志差异
 *
 * <AUTHOR>
 * @date 20240715
 */
@Service
public class CreateStructureLogicServiceImpl implements CreateStructureLogicService {

    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private FuturesDomainFacade futuresDomainFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private PriceApplyFacade priceApplyFacade;
    /**
     * 合同域写服务处理
     */
    @Autowired
    private ContractDomainService contractDomainService;
    /**
     * 合同域读服务处理
     */
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    /**
     * 合同域通用逻辑处理
     */
    @Autowired
    private CommonLogicService commonLogicService;
    /**
     * 需要查询一个TT用来写日志
     */
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    @Autowired
    private TradeDomainRemoteService tradeDomainRemoteService;



    @Override
    public ContractEntity createStructureContract(SalesStructurePriceTTDTO structurePriceTTDTO) {
        if (structurePriceTTDTO == null) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }
        structurePriceTTDTO.setSignDate(DateTimeUtil.parseTimeStamp2359(structurePriceTTDTO.getSignDate()));
        Integer ttId = structurePriceTTDTO.getTtId();
        //校验可操作量
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO
                .setCustomerId(structurePriceTTDTO.getCustomerId().toString())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setSupplierId(structurePriceTTDTO.getSupplierId().toString())
                .setStatus(PriceTypeEnum.STRUCTURE_PRICING.getValue())
                .setDomainCode(structurePriceTTDTO.getDomainCode())
                .setGoodsCategoryId(structurePriceTTDTO.getCategory2());
        Result result = futuresDomainFacade.mayTransferNum(contractFuturesDTO);
        CustomerFuturesDTO customerFuturesDTO = new CustomerFuturesDTO();
        if (result.isSuccess()) {
            customerFuturesDTO = JSON.parseObject(JSON.toJSONString(result.getData()), CustomerFuturesDTO.class);
        } else {
            throw new BusinessException(ResultCodeEnum.ADD_PRICE_APPLY_BE_DEFEATED);
        }
        //创建合同
        ContractEntity contractEntity = createContract(structurePriceTTDTO);
        return contractEntity;
    }


    /**
     * 创建结构化合同
     * @param structurePriceTTDTO
     * @return
     */
    private ContractEntity createContract(SalesStructurePriceTTDTO structurePriceTTDTO) {
        //结构化定价编号重复删合同不合理
//        List<ContractEntity> oldList = contractQueryDomainService.getByContractCode(structurePriceTTDTO.getContractCode());
//        oldList.forEach(oldContractEntity -> {
//            try {
//                contractDomainService.updateContractById(oldContractEntity.setRepeatContractCode(oldContractEntity.getRepeatContractCode() + "-" + oldContractEntity.getId()).setIsDeleted(IsDeletedEnum.DELETED.getValue()));
//            } catch (Exception e) {
//                throw new BusinessException(ResultCodeEnum.FAILURE);
//            }
//        });
        if (StringUtils.isBlank(structurePriceTTDTO.getCustomerName())) {
            CustomerEntity customerEntity = customerFacade.getCustomerById(structurePriceTTDTO.getCustomerId());
            structurePriceTTDTO.setCustomerName(customerEntity.getName()).setCustomerCode(customerEntity.getLinkageCustomerCode());
        }
        ContractEntity contractEntity = new ContractEntity();
        contractEntity.setUuid(UUID.randomUUID().toString())
                .setContractCode(structurePriceTTDTO.getContractCode())
                .setRepeatContractCode(structurePriceTTDTO.getContractCode())
                .setContractType(ContractTypeEnum.STRUCTURE.getValue())
                .setSalesType(structurePriceTTDTO.getSalesType())
                .setContractSource(1)
                .setCustomerId(structurePriceTTDTO.getCustomerId())
                .setCustomerName(structurePriceTTDTO.getCustomerName())
                .setCustomerCode(structurePriceTTDTO.getCustomerCode())
                .setSupplierId(structurePriceTTDTO.getSupplierId())
                .setSupplierName(structurePriceTTDTO.getSupplierName())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setBuCode(BuCodeEnum.ST.getValue())
                .setNeedOriginalPaper(1)//需要正本
                .setSignatureType(String.valueOf(SignatureTypeEnum.BOTH_SIGNATURE.getValue()))
                .setTradeType(ContractTradeTypeEnum.NEW.getValue())
                .setDomainCode(structurePriceTTDTO.getDomainCode())
                .setFutureCode(structurePriceTTDTO.getFutureCode())
                .setGoodsId(structurePriceTTDTO.getGoodsCategoryId())
                .setGoodsCategoryId(structurePriceTTDTO.getCategory2())
                .setContractNum(structurePriceTTDTO.getTotalNum())
                .setPriceStartTime(structurePriceTTDTO.getStartTime())
                .setPriceEndTime(DateTimeUtil.formatDateTimeString(structurePriceTTDTO.getEndTime()))
                .setOwnerId(Integer.valueOf(structurePriceTTDTO.getOwnerId()))
                .setSignDate(structurePriceTTDTO.getSignDate())
                .setBelongCustomerId(structurePriceTTDTO.getBelongCustomerId())
                .setPriceEndType(ContractPriceEndTypeEnum.DATE.ordinal())
                .setIsDeleted(0)
                .setCompanyId(structurePriceTTDTO.getCompanyId())
                .setCompanyName(structurePriceTTDTO.getCompanyName())
                .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setCreatedAt(DateTimeUtil.now())
                .setCategory1(structurePriceTTDTO.getCategory1())
                .setCategory2(structurePriceTTDTO.getCategory2())
                .setCategory3(structurePriceTTDTO.getCategory3())
                .setSiteCode(structurePriceTTDTO.getSiteCode())
                .setSiteName(structurePriceTTDTO.getSiteName());
        try {
            contractDomainService.saveContract(contractEntity);
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
        // 处理结构化合同谷信息
        structurePriceTTDTO.setContractId(contractEntity.getId());
        // TODO 这个正常应该原来的进行作废|修改待提交的的时候
        List<ContractStructureEntity> oldStructureList = contractQueryDomainService.getByStructureContractCode(structurePriceTTDTO.getContractCode());
        oldStructureList.forEach(oldStructure -> contractDomainService.updateById(oldStructure.setIsDeleted(IsDeletedEnum.DELETED.getValue())));

        ContractStructureEntity contractStructure = new ContractStructureEntity();
        BeanUtils.copyProperties(structurePriceTTDTO, contractStructure);
        contractStructure.setContractId(contractEntity.getId());
        contractStructure.setSupplierName(contractEntity.getSupplierName());
        contractStructure.setCumulativeRelease(new BigDecimal(0));
        contractStructure.setStructureName(structurePriceTTDTO.getStructureName());
        contractDomainService.saveStructureContract(contractStructure);

        //做新增结构化定价的记录
        saveStructureTTOperationDetail(structurePriceTTDTO);
        //创建点价单
        Result<Integer> priceResult = structurePriceApply(structurePriceTTDTO, contractStructure);

        if (priceResult == Result.failure()) {
            throw new BusinessException(ResultCodeEnum.ADD_PRICE_APPLY_BE_DEFEATED);
        }
        //创建好点价单后，将点价单号赋值给结构化定价
        contractStructure.setPriceApplyId(priceResult.getData());
        contractDomainService.updateById(contractStructure);

        return contractEntity;
    }

    /**
     * 保存操作类型
     * @param structurePriceTTDTO
     */
    private void saveStructureTTOperationDetail(SalesStructurePriceTTDTO structurePriceTTDTO) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO().setBizCode("addStructure")
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(1).setSource(9)
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setMetaData(null).setCreatedAt(DateTimeUtil.now())
                .setReferBizId(structurePriceTTDTO.getTtId())
                .setReferBizCode(structurePriceTTDTO.getCode())
                .setTtCode(structurePriceTTDTO.getCode());
        operationLogFacade.recordOperationLogDetail(operationDetailDTO);
    }

    /**
     * 创建点价单
     * @param tt
     * @param contract
     * @return
     */
    private Result<Integer> structurePriceApply(SalesStructurePriceTTDTO tt, ContractStructureEntity contract) {
        PriceApplyDTO priceApplyDTO = new PriceApplyDTO();
        CompanyEntity companyEntity = companyFacade.queryCompanyById(tt.getCompanyId());
        priceApplyDTO
                .setCustomerId(tt.getCustomerId())
                .setCode(contract.getContractCode())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setCustomerId(tt.getCustomerId())
                .setCustomerName(tt.getCustomerName())
                .setCategory1(String.valueOf(tt.getCategory1()))
                .setCategory2(String.valueOf(tt.getCategory2()))
                .setCategory3(String.valueOf(tt.getCategory3()))
                .setFutureCode(tt.getFutureCode())
                .setType(PriceTypeEnum.STRUCTURE_PRICING.getValue()).setDominantCode(tt.getDomainCode())
                .setContractId(contract.getId())
                .setApplyNum(tt.getTotalNum())
                .setContractId(contract.getContractId())
                .setStartTime(tt.getStartTime())
                .setEndTime(tt.getEndTime())
                .setStructureType(1)
                .setCompanyId(tt.getCompanyId())
                .setCompanyName(companyEntity.getName())
                .setTotalDay(contract.getTotalDay())
                .setApplyHandNum(BigDecimalUtil.div(RoundingMode.HALF_UP, tt.getTotalNum().setScale(0, BigDecimal.ROUND_UP), BigDecimalUtil.multiply(CalcTypeEnum.COUNT, ConfigurationConstants.HAND_BASICS, ConfigurationConstants.HAND_RATE)))
        ;
        return priceApplyFacade.priceApply(priceApplyDTO);
    }



}
