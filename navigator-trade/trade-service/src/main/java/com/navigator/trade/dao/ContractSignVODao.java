package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.enums.EarlyWarningEnum;
import com.navigator.customer.pojo.enums.FrameProtocolEnum;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.trade.mapper.ContractSignVOMapper;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractSignVOEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.qo.ContractSignQO;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Dao
public class ContractSignVODao extends BaseDaoImpl<ContractSignVOMapper, ContractSignVOEntity> {

    public IPage<ContractSignVOEntity> queryContractSign(QueryDTO<ContractSignQO> queryDTO,
                                                         List<Integer> customerIds,
                                                         List<String> siteCodeList) {

        ObjectMapper mapper = new ObjectMapper();
        ContractSignQO contractSignQO = mapper.convertValue(queryDTO.getCondition(), ContractSignQO.class);
        String warningType = contractSignQO.getWarningType();
        LambdaQueryWrapper<ContractSignVOEntity> wrapper = new LambdaQueryWrapper<ContractSignVOEntity>()
                .eq(contractSignQO.getLdcFrame() != null, ContractSignVOEntity::getLdcFrame, contractSignQO.getLdcFrame())
                .eq(contractSignQO.getTtType() != null, ContractSignVOEntity::getTtType, contractSignQO.getTtType())
                .eq(null != contractSignQO.getNeedOriginalPaper(), ContractSignVOEntity::getNeedOriginalPaper, contractSignQO.getNeedOriginalPaper())
                // 协议/TT/仓单
                .and(StrUtil.isNotBlank(contractSignQO.getContractCode()),
                        bizWrapper -> bizWrapper.like(ContractSignVOEntity::getContractCode, "%" + (StrUtil.isNotBlank(contractSignQO.getContractCode()) ? contractSignQO.getContractCode().trim() : contractSignQO.getContractCode()) + "%")
                                .or().like(ContractSignVOEntity::getTtCode, "%" + (StrUtil.isNotBlank(contractSignQO.getContractCode()) ? contractSignQO.getContractCode().trim() : contractSignQO.getContractCode()) + "%")
                                .or().like(ContractSignVOEntity::getWarrantCode, "%" + (StrUtil.isNotBlank(contractSignQO.getContractCode()) ? contractSignQO.getContractCode().trim() : contractSignQO.getContractCode()) + "%"))
                // 货品名称模糊查询
                .and(StrUtil.isNotBlank(contractSignQO.getGoodsName()),
                        bizWrapper -> bizWrapper.like(ContractSignVOEntity::getGoodsName, "%" + (StrUtil.isNotBlank(contractSignQO.getGoodsName()) ? contractSignQO.getGoodsName().trim() : contractSignQO.getGoodsName()) + "%")
                                .or().like(ContractSignVOEntity::getCommodityName, "%" + (StrUtil.isNotBlank(contractSignQO.getGoodsName()) ? contractSignQO.getGoodsName().trim() : contractSignQO.getGoodsName()) + "%"))
                .eq(null != contractSignQO.getGoodsId(), ContractSignVOEntity::getGoodsId, contractSignQO.getGoodsId())
                .eq(null != contractSignQO.getSalesType(), ContractSignVOEntity::getSalesType, contractSignQO.getSalesType())
                .eq(StringUtils.isNotBlank(contractSignQO.getBuCode()), ContractSignVOEntity::getBuCode, contractSignQO.getBuCode())
                .eq(StringUtils.isNotBlank(contractSignQO.getSiteCode()), ContractSignVOEntity::getSiteCode, contractSignQO.getSiteCode())
                .eq(null != contractSignQO.getGoodsCategoryId(), ContractSignVOEntity::getCategory2, contractSignQO.getGoodsCategoryId())
                .eq(null != contractSignQO.getCategory3(), ContractSignVOEntity::getCategory3, contractSignQO.getCategory3())
                .eq(StrUtil.isNotBlank(contractSignQO.getProtocolCode()), ContractSignVOEntity::getProtocolCode, contractSignQO.getProtocolCode())
                .eq(ContractSignVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != contractSignQO.getSupplierId(), ContractSignVOEntity::getSupplierId, contractSignQO.getSupplierId())
                .eq(null != contractSignQO.getCustomerId(), ContractSignVOEntity::getCustomerId, contractSignQO.getCustomerId())
                .eq(StringUtils.isNotBlank(contractSignQO.getDomainCode()), ContractSignVOEntity::getDomainCode, contractSignQO.getDomainCode())
                .eq(StringUtils.isNotBlank(contractSignQO.getFutureCode()), ContractSignVOEntity::getFutureCode, contractSignQO.getFutureCode())
                .between(StrUtil.isNotBlank(contractSignQO.getSignDate()), ContractSignVOEntity::getCreatedAt, contractSignQO.getSignDate() + " 00:00:00.000", contractSignQO.getSignDate() + " 23:59:59.000")
                .between(StrUtil.isNotBlank(contractSignQO.getStartSignDate()) && StrUtil.isNotBlank(contractSignQO.getEndSignDate()), ContractSignVOEntity::getCreatedAt, contractSignQO.getStartSignDate() + " 00:00:00.000", contractSignQO.getEndSignDate() + " 23:59:59.000")
                .between(StrUtil.isNotBlank(contractSignQO.getStartSignDate()) && StrUtil.isBlank(contractSignQO.getEndSignDate()), ContractSignVOEntity::getCreatedAt, contractSignQO.getStartSignDate() + " 00:00:00.000", contractSignQO.getStartSignDate() + " 23:59:59.000")
//                .in((null != contractSignQO.getDeliveryFactoryCode() && !contractSignQO.getDeliveryFactoryCode().isEmpty()), ContractSignEntity::getDeliveryFactoryCode, contractSignQO.getDeliveryFactoryCode())
                .eq(null != contractSignQO.getUseWS(), ContractSignEntity::getWsType, contractSignQO.getUseWS());
        // 哥伦布
        if (SystemEnum.COLUMBUS.getValue() == contractSignQO.getSystem()) {
            // 按照状态查询
            if (StringUtil.isNotEmpty(contractSignQO.getStatus())) {
                // (【已完成】状态对应Magellan【正本】&【已完成】2个状态)
                if (Integer.parseInt(contractSignQO.getStatus()) == ContractSignStatusEnum.PROCESSING.getValue()) {
                    wrapper.in(ContractSignVOEntity::getStatus, Arrays.asList(ContractSignStatusEnum.PROCESSING.getValue(), ContractSignStatusEnum.PAPER.getValue()));
                } else {
                    wrapper.eq(ContractSignVOEntity::getStatus, contractSignQO.getStatus());
                }
            } else {
                wrapper.in(ContractSignVOEntity::getStatus, Arrays.asList(ContractSignStatusEnum.WAIT_BACK.getValue(),
                        ContractSignStatusEnum.WAIT_CONFIRM.getValue(), ContractSignStatusEnum.PROCESSING.getValue(),
                        ContractSignStatusEnum.PAPER.getValue(), ContractSignStatusEnum.INVALID.getValue()));
            }
        } else {
            // 麦哲伦
            if (CollectionUtil.isEmpty(siteCodeList)) {
                return this.page(new Page<>(0, 0, 0));
            }
            if (StringUtil.isNotEmpty(contractSignQO.getStatus())) {
                wrapper.eq(ContractSignVOEntity::getStatus, contractSignQO.getStatus());
            } else {
                wrapper.notIn(ContractSignVOEntity::getStatus, Arrays.asList(ContractSignStatusEnum.INVALID.getValue()));
            }
            wrapper.in(ContractSignVOEntity::getSiteCode, siteCodeList);
//            wrapper.and(k -> {
//                companyCustomerIdMap.entrySet().forEach(i -> {
//                    k.or(v -> v
//                            .eq(ContractSignVOEntity::getCompanyId, i.getKey())
//                            .in(ContractSignVOEntity::getBelongCustomerId, i.getValue()))
//                    ;
//                });
//            });
        }

        if (null != contractSignQO.getNonFrame()) {
            if (GeneralEnum.YES.getValue().equals(contractSignQO.getNonFrame())) {
                wrapper.eq(ContractSignVOEntity::getCustomerNonFrame, GeneralEnum.YES.getValue());
            } else {
                wrapper.eq(ContractSignVOEntity::getCustomerNonFrame, GeneralEnum.NO.getValue());
            }
        }

        // 客户id
        if (ContractSalesTypeEnum.SALES.getValue() == contractSignQO.getSalesType()) {
            wrapper.in(CollectionUtil.isNotEmpty(customerIds), ContractSignVOEntity::getCustomerId, customerIds);
        } else {
            wrapper.in(CollectionUtil.isNotEmpty(customerIds), ContractSignVOEntity::getSupplierId, customerIds);
        }

        if (StringUtils.isNotBlank(warningType) && warningType.equals(EarlyWarningEnum.FRAME_EXPIRED.getValue().toString())) {
            wrapper.eq(ContractSignVOEntity::getFrameProtocol, FrameProtocolEnum.CONTRACT.getValue());
            wrapper.and(k -> k.gt(ContractSignVOEntity::getProtocolStartDate, new Date())
                    .or(i -> i.lt(ContractSignVOEntity::getProtocolEndDate, new Date())));
        }

//        if (null != contractSignQO.getUseWS() && UseYqqEnum.USE_YQQ.getValue().equals(contractSignQO.getUseWS())) {
//            wrapper.eq(ContractSignVOEntity::getUseYqq, 1)
//                    .eq(ContractSignVOEntity::getIsColumbus, 1);
//        }
//
//        if (null != contractSignQO.getUseWS() && UseYqqEnum.NOT_USE_YQQ.getValue().equals(contractSignQO.getUseWS())) {
//            wrapper.eq(ContractSignVOEntity::getUseYqq, 0)
//                    .eq(ContractSignVOEntity::getIsColumbus, 1);
//        }
//
//        if (null != contractSignQO.getUseWS() && UseYqqEnum.NOT_SYSTEM.getValue().equals(contractSignQO.getUseWS())) {
//            wrapper.eq(ContractSignVOEntity::getUseYqq, 0)
//                    .eq(ContractSignVOEntity::getIsColumbus, 0);
//        }

        if (StrUtil.isNotBlank(contractSignQO.getStatus()) && Arrays.asList(ContractSignStatusEnum.WAIT_PROVIDE.getValue(), ContractSignStatusEnum.WAIT_BACK.getValue()).contains(Integer.parseInt(contractSignQO.getStatus()))) {
            wrapper.orderByDesc(ContractSignVOEntity::getUpdatedAt, ContractSignVOEntity::getId);
        } else {
            wrapper.orderByDesc(ContractSignVOEntity::getId, ContractSignVOEntity::getUpdatedAt);
        }

        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
    }
}
