package com.navigator.trade.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.approval.LoaApprovalRuleFacade;
import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.ContractApproveBizInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.DroolsUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractApproveConfigEnum;
import com.navigator.trade.pojo.enums.ContractApproveConfigItemEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITTApproveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TT审批类
 *
 * <AUTHOR>
 * @date 2021/11/29 10:17
 */
@Service
@Slf4j
public class TTApproveServiceImpl implements ITTApproveService {

    @Resource
    private ApproveFacade approveFacade;
    @Resource
    private IContractQueryService contractService;
    @Resource
    OperationLogFacade operationLogFacade;
    @Resource
    private LoaApprovalRuleFacade approvalRuleFacade;

    @Override
    public Result<ApproveResultDTO> startTTApprove(ApproveDTO approveDTO) {
        // 启动流程
        Result result = approveFacade.start(approveDTO);
        return result;
    }

    @Override
    public Result<ApproveResultDTO> approve(ApproveDTO approveDTO) {
        log.error("=======================================");
        log.error("=======================================");
        log.error("===================TTApproveServiceImpl.approveTT====================");
        log.error(JSON.toJSONString(approveDTO));
        // approve
        Result result = approveFacade.approve(approveDTO);
        return result;
    }

    @Override
    public void cancel(ApproveDTO approveDTO) {
        approveFacade.cancel(approveDTO);
    }

    @Override
    public Integer calcApproveRuleValue(Integer goodsCategoryId, String unitPrice, String contractNum1, Date deliveryStartTime, Date deliveryEndTime) {
        if (StringUtils.isBlank(contractNum1) || StringUtils.isBlank(unitPrice) || null == goodsCategoryId) {
            return null;
        }
        BigDecimal contractNum = new BigDecimal(contractNum1);
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, new BigDecimal(unitPrice));
        // 判断审批类型
        // C双签
        if (totalAmount.compareTo(ContractApproveConfigEnum.getValueByType(goodsCategoryId, "C")) > 0
                && !getTime(deliveryStartTime, deliveryEndTime)) {
            return ContractApproveRuleEnum.ABC12.getValue();

        }

        // 满足C签单签
        if (totalAmount.compareTo(ContractApproveConfigEnum.getValueByType(goodsCategoryId, "C")) > 0
                && getTime(deliveryStartTime, deliveryEndTime)) {
            return ContractApproveRuleEnum.ABC.getValue();
        }

        // 满足B签
        if (totalAmount.compareTo(ContractApproveConfigEnum.getValueByType(goodsCategoryId, "B")) > 0) {
            return ContractApproveRuleEnum.AB.getValue();
        }

        // 满足A签
        if (contractNum.compareTo(ContractApproveConfigEnum.getValueByType(goodsCategoryId, "A")) > 0) {
            return ContractApproveRuleEnum.A.getValue();

        }

        return ContractApproveRuleEnum.NONE.getValue();
    }

    @Override
    public int calcApproveRuleValue(Integer type, TTDTO ttdto) {
        List<CompareObjectDTO> compareObjectDTOList;
        Integer goodsCategoryId;

        if (type == ContractActionEnum.REVISE.getActionValue()) {
            compareObjectDTOList = JSON.parseArray(ttdto.getSalesContractReviseTTDTO().getModifyContent(), CompareObjectDTO.class);
            goodsCategoryId = ttdto.getSalesContractReviseTTDTO().getGoodsCategoryId();
        } else {
            compareObjectDTOList = JSON.parseArray(ttdto.getSalesContractSplitTTDTO().getModifyContent(), CompareObjectDTO.class);
            goodsCategoryId = ttdto.getSalesContractSplitTTDTO().getGoodsCategoryId();
        }

        List<String> nameList = compareObjectDTOList.stream().map(CompareObjectDTO::getName).collect(Collectors.toList());
        int value = ContractApproveRuleEnum.A.getValue();
        // 拆分
        if (type.equals(ContractActionEnum.SPLIT.getActionValue())) {
            // 判断合同基础属性 走add流程  salesContractSplitTTDTO
            int approveType = this.calcApproveRuleValue(ttdto.getSalesContractSplitTTDTO().getGoodsCategoryId(), ttdto.getSalesContractSplitTTDTO().getUnitPrice().toString(), ttdto.getSalesContractSplitTTDTO().getContractNum().toString(), new Date(), ttdto.getSalesContractSplitTTDTO().getDeliveryStartTime());
            value = value > approveType ? value : approveType;
        }
        if (value == ContractApproveRuleEnum.ABC12.getValue()) {
            return value;
        }

        // 豆粕
        if (goodsCategoryId.equals(GoodsCategoryEnum.OSM_MEAL.getValue())) {

            // C签判断
            // 所有平台利息费用  一期不涉及金额
            if (value == ContractApproveRuleEnum.AB.getValue()) {
                return value;
            }

            // B签判断
            // 主体变更
            if (nameList.contains("customerId")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            if (nameList.contains("supplierId")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // 客诉折价
            if (nameList.contains("complaintDiscountPrice")) {

                BigDecimal contractNum = null != ttdto.getSalesContractReviseTTDTO() ? ttdto.getSalesContractReviseTTDTO().getContractNum() :
                        ttdto.getSalesContractSplitTTDTO() == null ? BigDecimal.ZERO : ttdto.getSalesContractSplitTTDTO().getContractNum();

                if (ttdto.getPriceDetailBO().getComplaintDiscountPrice().multiply(contractNum).compareTo(new BigDecimal("10000")) > 0) {
                    return ContractApproveRuleEnum.AB.getValue();
                }
                value = ContractApproveRuleEnum.A.getValue() > value ? ContractApproveRuleEnum.A.getValue() : value;
            }

            // 副产品的合同拆分 一期不含副产品

            // 蛋白超出商务提供标准±10 对应TT字段中【规格】的数据变化
            if (nameList.contains("goodsSpecId")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // 变更交期 交货开始日期或交货结束日期发生变化
            if (nameList.contains("deliveryStartTime") || nameList.contains("deliveryEndTime")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // 三方协议 买方主体变更
            // 变更前买方主体与变更后买方主体非集团公司，且勾选了三方协议后，发生变更走A签

            // 同集团合同作平均价 对应TT字段中【含税单价】组成中的【期货价格】数据变化
            if (nameList.contains("forwardPrice")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            if (value >= ContractApproveRuleEnum.A.getValue()) {
                return value;
            }

            // A签判断
            // 合同拆分-转厂、同客户集团 同集团客户，不同工厂之间的转换 对应TT字段中【交货工厂】数据变化
            if (nameList.contains("deliveryFactoryCode")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            // 对应TT字段中【含税单价】组成的【蛋白价差】数据变化
            if (nameList.contains("proteinDiffPrice")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            // 对应TT字段中【包装】数据变化
            if (nameList.contains("goodsPackageId")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            // 对应TT字段中【含税单价】组成中的【其他补贴】数据变化
            if (nameList.contains("otherPrice")) {
                return ContractApproveRuleEnum.A.getValue();
            }
            // 对应TT字段中【含税单价】组成中的【散粕补贴】数据变化
            if (nameList.contains("compensationPrice")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            // 对应TT字段中【付款方式】 发生变化时，走工厂A签
            if (nameList.contains("paymentType")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            //  对应TT字段中【目的地】的数据变化
            if (nameList.contains("destination")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            //  对应TT字段中【交提货方式】的数据变化
            if (nameList.contains("deliveryType")) {
                return ContractApproveRuleEnum.A.getValue();
            }

            // 对应TT字段中【重量检验】的数据变化
            if (nameList.contains("weightCheck")) {
                return ContractApproveRuleEnum.A.getValue();
            }
        }

        // 豆油
        if (goodsCategoryId.equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
            // B签判断
            // 主体变更
            if (nameList.contains("customerId")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // B签判断
            // 变更交期 交货开始日期或交货结束日期发生变化
            if (nameList.contains("deliveryStartTime") || nameList.contains("deliveryEndTime")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            //  交货地点变更 对应TT字段中【目的地】的数据变化
            if (nameList.contains("destination")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // 豆油等级变更 对应TT字段中【规格】的数据变化
            if (nameList.contains("goodsSpecId")) {
                return ContractApproveRuleEnum.AB.getValue();
            }

            // A签判断
            // 合同拆分-转厂、同客户集团 同集团客户，不同工厂之间的转换 对应TT字段中【交货工厂】数据变化
            if (nameList.contains("deliveryFactoryCode")) {
                return ContractApproveRuleEnum.A.getValue();
            }
        }

        return value;
    }

    @Override
    public ApproveDTO adaptApproveRule(ApproveDTO approveDTO) {
        try {
            log.info("==============================================================adaptApproveRule");
            ContractApproveBizInfoDTO approveBizInfoDTO = (ContractApproveBizInfoDTO) approveDTO.getApproveBizInfoDTO();
            log.info("=========================adaptApproveRule.approveBizInfoDTO:{}", JSON.toJSONString(approveBizInfoDTO));
            //获取审批规则
            LoaApprovalRuleDTO loaApprovalRuleDT = new LoaApprovalRuleDTO();
            loaApprovalRuleDT
                    .setTtType(approveBizInfoDTO.getTtType())
                    .setBuCode(approveBizInfoDTO.getBuCode())
                    .setSalesType(String.valueOf(approveBizInfoDTO.getSalesType()))
                    .setCategory2(approveBizInfoDTO.getCategory2())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
            ;
            log.info("=========================adaptApproveRule.loaApprovalRuleDT:{}", JSON.toJSONString(loaApprovalRuleDT));
            LoaApprovalRuleEntity loaApprovalRuleEntity = approvalRuleFacade.queryLoaApprovalRule(loaApprovalRuleDT);
            log.info("=========================adaptApproveRule.loaApprovalRuleEntity:{}", JSON.toJSONString(loaApprovalRuleEntity));
            if (null != loaApprovalRuleEntity &&
                    (null == approveBizInfoDTO.getWarrantTradeType()
                            || 0 == approveBizInfoDTO.getWarrantTradeType()
                            || WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(approveBizInfoDTO.getWarrantTradeType()))
            ) {

                //匹配所有条款，记录命中了哪些条款
                DroolsUtil.runRuleContent(loaApprovalRuleEntity.getRuleInfo(), approveBizInfoDTO);
                log.info("adaptApproveRule.approveBizInfoDTO:{}", JSON.toJSONString(approveBizInfoDTO));
                approveDTO.setApproveRuleValue(approveBizInfoDTO.getRuleResult());
                String approveCause = formatApproveCause(approveBizInfoDTO);
                if (approveBizInfoDTO.getRuleResult() == ContractApproveRuleEnum.NONE.getValue()
                        && StringUtil.isEmpty(approveCause)) {
                    approveCause = "免签；";
                    approveDTO.setApproveCause(approveCause);
                } else {
                    approveDTO.setApproveCause(approveCause);
                }

            } else {
                approveDTO.setApproveRuleValue(ContractApproveRuleEnum.NONE.getValue());
                approveDTO.setApproveCause("免签；");
            }
            //String rulesFileName = getRuleFileName(approveBizInfoDTO);
            //String allRulesFileName = DROOLS_RULE_FOLDER + ALL_CONTRACT_RULES_FILE_NAME;
            //先匹配所有条款，记录命中了哪些条款
            //DroolsUtil.run(allRulesFileName, approveBizInfoDTO);

        } catch (Exception e) {
            operationLogFacade.saveTraceLog(new TraceLogDTO(String.valueOf(approveDTO.getReferBizId()), "adaptApproveRule.exception", JSON.toJSONString(e)));
            log.debug("=======adaptApproveRule error,referBizId:{},exception:{}", approveDTO.getReferBizId(), JSON.toJSONString(e));
        }
        log.info("============================adaptApproveRule,over==================================");
        return approveDTO;

    }

    private String getRuleFileName(ContractApproveBizInfoDTO bizParameter) {
        StringBuilder sbRuleName = new StringBuilder();
        sbRuleName.append("rules/");
        //sbRuleName.append(GoodsCategoryEnum.getByValue(bizParameter.getCategoryId()).getCode());
        sbRuleName.append("SBMO");//油粕一致

        sbRuleName.append("_SP");//采销一体

        ContractTradeTypeEnum tradeTypeEnum = ContractTradeTypeEnum.getByValue(bizParameter.getTradeType());
        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(bizParameter.getTtType());
        switch (ttTypeEnum) {
            case NEW:
            case REVISE:
            case SPLIT:
            case BUYBACK:
            case WASHOUT:
            case CLOSED:
                sbRuleName.append("_").append(ttTypeEnum.getCode());
                break;
            default:
                sbRuleName.append("_").append("OTHER");
                break;
        }
        sbRuleName.append(".drl");
        return sbRuleName.toString();
    }


    /**
     * 判断是否大于一年
     *
     * @param deliveryStartTime
     * @param deliveryEndTime
     * @return
     */
    private boolean getTime(Date deliveryStartTime, Date deliveryEndTime) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(deliveryStartTime);
        calendar.add(Calendar.YEAR, 1);
        Date startDate = calendar.getTime();
        if (startDate.before(deliveryEndTime)) {
            return false;
        }
        return true;
    }

    private String formatApproveCause(ContractApproveBizInfoDTO contractApproveBizInfoDTO) {
        String approveCause = null == contractApproveBizInfoDTO.getRuleMemo() ? "" : contractApproveBizInfoDTO.getRuleMemo().toString();

        if (StringUtil.isEmpty(approveCause)) {
            return "";
        }

        String maxAmount = StringUtil.formatMoney(contractApproveBizInfoDTO.getMaxTotalAmount());
        String minAmount = StringUtil.formatMoney(contractApproveBizInfoDTO.getMinTotalAmount());
        String deliveryDueMonth = StringUtil.formatMonth(contractApproveBizInfoDTO.getDeliveryDueMonthLimit());
        String remainContractNumber = StringUtil.formatTon(contractApproveBizInfoDTO.getRemainContractNumLimit());

        approveCause = approveCause.replace(ContractApproveConfigItemEnum.MAX_AMOUNT.getPlaceholder(), maxAmount);
        approveCause = approveCause.replace(ContractApproveConfigItemEnum.MIN_AMOUNT.getPlaceholder(), minAmount);
        approveCause = approveCause.replace(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.getPlaceholder(), deliveryDueMonth);
        approveCause = approveCause.replace(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.getPlaceholder(), remainContractNumber);

        return approveCause;
    }

}
