package com.navigator.trade.service.contract;

/**
 * 1003270 batch TT creation group_id field changed by <PERSON> at 2025-06-17 start
 * Group ID generation service for batch contract creation tracking
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface GroupIdGenerationService {

    /**
     * Generate a new group ID for batch contract creation using database sequence
     * This method is thread-safe and prevents race conditions
     *
     * @return new group ID
     */
    Integer generateGroupId();
}
// 1003270 batch TT creation group_id field changed by <PERSON> at 2025-06-17 end
