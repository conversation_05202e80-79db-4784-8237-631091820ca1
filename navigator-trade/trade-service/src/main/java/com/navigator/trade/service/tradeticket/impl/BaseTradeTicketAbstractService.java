package com.navigator.trade.service.tradeticket.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.activiti.pojo.enums.ApproveActionEnum;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.BizApproveStatusEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.approval.CategoryApprovalModelFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.LogTargetRecordTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.ContractApproveBizInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.customer.pojo.enums.TTCustomerTradeStatusEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.ConfirmLogicService;
import com.navigator.trade.dao.*;
import com.navigator.trade.handler.SalesContractHandler;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignReviewDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.SubmitTTVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.*;
import com.navigator.trade.service.async.ContractAsyncExecutor;
import com.navigator.trade.service.contract.ICancelContractModifyService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.GroupIdGenerationService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.contractsign.IContractSignService;
import com.navigator.trade.service.sync.AtlasSyncService;
import com.navigator.trade.service.sync.LkgSyncService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.service.tradeticket.impl.convert.ContractSignConvertUtil;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.navigator.bisiness.enums.ProcessorTypeEnum.*;
import static com.navigator.common.constant.RedisConstants.CONTRACT_SAVE_TT_TIMES;


@Slf4j
@Service
public abstract class BaseTradeTicketAbstractService<T> implements ITradeTicketService {
    public static List<String> addProcessorList = Arrays.asList(SBM_S_ADD.getTtValue(), SBM_P_ADD.getTtValue(), SBM_P_BUYBACK.getTtValue(),
            SBO_S_ADD.getTtValue(), SBO_P_ADD.getTtValue(), SBO_P_BUYBACK.getTtValue());
    public static String LOCK_VERSION = "LOCK";
    /***********************DAO***********************/
    @Autowired
    protected TradeTicketDao tradeTicketDao;
    @Autowired
    protected TtAddDao ttAddDao;
    @Autowired
    protected TtPriceDao ttPriceDao;
    @Autowired
    protected TtModifyDao ttModifyDao;
    @Autowired
    protected TtTranferDao ttTranferDao;
    /***********************Service & Handler***********************/
    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    @Qualifier("TTApproveServiceImpl")
    protected ITTApproveService ttApproveService;
    @Autowired
    protected IContractOperationNewService salesContractOperationService;
    @Autowired
    protected ICancelContractModifyService cancelContractModifyService;
    @Autowired
    protected SalesContractSignHandler salesContractSignHandler;
    @Autowired
    protected SalesContractHandler salesContractHandler;
    @Autowired
    protected TTHandler ttHandler;
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected IContractSignQueryService iContractSignQueryService;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    protected ContractSignConvertUtil contractSignConvertUtil;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;
    @Autowired
    protected LkgSyncService lkgSyncService;
    @Autowired
    protected AtlasSyncService atlasSyncService;
    @Autowired
    protected CompanyFacade companyFacade;
    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected ITtTranferService ttTranferService;
    /***********************Facade***********************/
    @Autowired
    protected WarehouseFacade warehouseFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected OperationLogFacade operationLogFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected SkuFacade skuFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected DepositRuleFacade depositRuleFacade;
    @Autowired
    protected PriceAllocateFacade priceAllocateFacade;
    @Autowired
    protected CustomerBankFacade customerBankFacade;
    @Autowired
    protected EmployFacade employFacade;
    @Autowired
    protected CustomerDepositRateFacade customerDepositRateFacade;
    @Autowired
    protected FileBusinessFacade fileBusinessFacade;
    @Autowired
    protected PriceApplyFacade priceApplyFacade;
    @Autowired
    protected QualityFacade qualityFacade;
    @Autowired
    protected CustomerCreditPaymentFacade customerCreditPaymentFacade;
    /***********************Other***********************/
    @Autowired
    protected TransactionTemplate transactionTemplate;
    @Autowired
    protected RedisUtil redisUtil;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected CodeGeneratorUtil codeGeneratorUtil;
    @Autowired
    protected ITradeTicketQueryService tradeTicketQueryService;
    @Autowired
    protected RedisService redisService;
    protected String moduleType = ModuleTypeEnum.TRADE_TICKET.getModule();
    protected TTTypeEnum ttTypeEnum;
    protected ContractTradeTypeEnum contractTradeTypeEnum;
    protected ContractSalesTypeEnum salesType;
    protected Integer contractSource;
    protected Integer contractStatus;
    protected Integer operationSource;
    protected Integer goodsCategoryId;
    protected Integer subGoodsCategoryId;
    protected Integer status;
    protected String processorType;
    protected Integer contractSignatureStatus;
    protected LogBizCodeEnum logBizCodeEnum;
    @Autowired
    private ProteinPriceConfigFacade proteinPriceConfigFacade;
    @Autowired
    private ApproveFacade approveFacade;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private UserFacade userFacade;
    @Autowired
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Autowired
    private ContractAsyncExecutor contractAsyncExecutor;
    @Autowired
    protected CustomerInvoiceFacade customerInvoiceFacade;
    @Resource
    protected CategoryApprovalModelFacade categoryApprovalModelFacade;
    @Resource
    protected CategoryFacade categoryFacade;
    @Autowired
    protected CommonLogicService commonLogicService;
    @Autowired
    protected ConfirmLogicService confirmLogicService;


    @Value("${tt.approve.switcher}")
    private Boolean approveRuleSwitcher;
    //CaseId-1002453: RR status in navigator-后端TT提交开关，Author By NaNa
    @Value("${tt.residual.risk.switcher}")
    private Boolean residualRiskSwitcher;
    @Autowired
    protected SequenceUtil sequenceUtil;
    @Autowired
    protected SiteFacade siteFacade;

    // adding batch TT creation group_id field by Jason Shi at 2025-01-06 start
    @Autowired
    protected GroupIdGenerationService groupIdGenerationService;
    // adding batch TT creation group_id field by Jason Shi at 2025-01-06 end

    /**
     * DTO转化为主表实体类
     *
     * @param ttdto
     * @return
     */
    public abstract TradeTicketEntity convertToTradeTicket(TTDTO ttdto);

    /**
     * 转化为协议DTO
     *
     * @param
     * @return
     */
    public abstract ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto);

    /**
     * 保存TT副表
     *
     * @param ttId
     * @param ttDto
     */
    public abstract void saveTTSubInfo(Integer ttId, TTDTO ttDto) throws Exception;

    /**
     * 查询TT详情
     *
     * @param ttId
     * @param tradeTicketEntity
     */
    public abstract TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity);

    /**
     * 初始化TT参数实体
     * (初始化状态 , 判断审批节点, 生成tt编号等)
     *
     * @param ttdto
     * @return
     */
    public abstract TTDTO initDTO(TTDTO ttdto);

    /**
     * 转换为模板DTO
     *
     * @param ttId
     * @return
     */
    public abstract SignTemplateDTO convertToSignTemplateDTO(Integer ttId);

    protected void createContract(TradeTicketEntity tradeTicketEntity, TTDTO ttDto) {
    }

    @Override
    public RecordBizOperationDTO startTTApprove(Integer ttId, TTDTO ttDto, ContractPriceEntity contractPriceEntity) {
        log.info("===>startTTApproveLog,ttId:{},startTTApprove:{}", ttId, JSON.toJSONString(ttDto));
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        Integer salesType = tradeTicketEntity.getSalesType();

        log.info("============================================================================组装数据");
        //组装审批数据
        ApproveDTO approveDTO = buildApproveDTO(ttId, ttDto, tradeTicketEntity, salesType, contractPriceEntity);
        log.info("============================================================================组装数据结束");
        //审批规则适配
        log.info("============================================================================审批规则适配");
        approveDTO = ttApproveService.adaptApproveRule(approveDTO);
        int value = approveDTO.getApproveRuleValue();
        log.info("============================================================================审批规则适配value:{}", value);
        //记录日志
        operationLogFacade.saveTraceLog(new TraceLogDTO(ttId.toString(), "startTTApprove.buildApproveDTO", JSON.toJSONString(approveDTO)));

        //开关
        if (null == approveRuleSwitcher || !approveRuleSwitcher) {
            value = getApprovalValue(ttId, ttDto, tradeTicketEntity);
            //组装审批数据
            approveDTO = getApproveDTO(ttId, ttDto, tradeTicketEntity, salesType, value);
            //记录过程数据
            operationLogFacade.saveTraceLog(new TraceLogDTO(ttId.toString(), "startTTApprove.buildApproveDTO(OLD)", JSON.toJSONString(approveDTO)));
        }

        Result result = ttApproveService.startTTApprove(approveDTO);
        if (!result.isSuccess()) {
            log.warn("发起审批失败,提交失败,result:{}", JSON.toJSONString(result));
            throw new BusinessException(ResultCodeEnum.START_APPROVAL_ERROR);
        }
        log.info("startTTApprove.result:{}", JSON.toJSONString(result));

        // 更改tt状态,更新审批规则
        if (Objects.equals(ContractApproveRuleEnum.NONE.getValue(), value)) {
            //免签
            log.info("check_code_question2 ttId:{} BaseTradeTicketAbstractService  startTTApprove6   ", ttId);
            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WITHOUT_APPROVE.getValue(), tradeTicketEntity.getCode(), value);

            //更新合同状态
            if (ContractActionEnum.NEW.getActionValue() != tradeTicketEntity.getContractSource()
                    && ttDto.getSalesContractReviseTTDTO() != null) {
                ttDto.getSalesContractReviseTTDTO().setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
        } else {
            //审批中
            log.info("check_code_question2 ttId:{} BaseTradeTicketAbstractService startTTApprove5   ", ttId);
            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_A_SIGN.getValue(), tradeTicketEntity.getCode(), value);
        }

        ApproveResultDTO approveResultDTO = JSON.parseObject(JSON.toJSONString(result.getData()), ApproveResultDTO.class);
        RecordBizOperationDTO recordBizOperationDTO = new RecordBizOperationDTO();
        recordBizOperationDTO.setApproveDTO(approveDTO);
        recordBizOperationDTO.setApproveResultDTO(approveResultDTO);
        return recordBizOperationDTO;
    }

    private ApproveDTO getApproveDTO(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity, Integer salesType, int value) {
        //获得流程定义
        /*ApproveProcessEnum approveProcessEnum = ApproveProcessEnum.getByTradeInfo(
                salesType,
                GoodsCategoryEnum.getByValue(subGoodsCategoryId).name(),
                contractTradeTypeEnum.name());*/
        log.info("============ tt.ttId:{}", JSON.toJSONString(ttId));
        log.info("============ tt.contractCode:{}", JSON.toJSONString(tradeTicketEntity.getContractCode()));
        log.info("============ tt.subCategoryId:{}", JSON.toJSONString(tradeTicketEntity.getGoodsCategoryId()));
        log.info("============ tt.categoryId:{}", JSON.toJSONString(tradeTicketEntity.getSubGoodsCategoryId()));
        log.info("============ tt.subCategoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getGoodsCategoryId())));
        log.info("============ tt.categoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getSubGoodsCategoryId())));

        String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

        CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory1());
        CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
        CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: Wan 2024-12-04 Start
                .setTtTypeEnum(TTTypeEnum.getByType(tradeTicketEntity.getType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: Wan 2024-12-04 end
                .setCategory1Name(category1Entity.getName())
                .setCategory2Name(category2Entity.getName())
                .setCategory3Name(category3Entity.getName())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(salesType))
                .setUserId(JwtUtils.getCurrentUserId())
                .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                //.setRoleId("")
                .setCustomerName(tradeTicketEntity.getCustomerName())
                .setCustomerId(tradeTicketEntity.getCustomerId())
                .setSupplierId(tradeTicketEntity.getSupplierId())
                .setSupplierName(tradeTicketEntity.getSupplierName())
                .setCompanyId(tradeTicketEntity.getCompanyId())
                .setCompanyName(tradeTicketEntity.getCompanyName())
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSiteCode(tradeTicketEntity.getSiteCode())
                .setSiteName(tradeTicketEntity.getSiteName())
                .setBuCode(tradeTicketEntity.getBuCode())
                .setProcessKey(processKey)
                .setBizModule(moduleType)
                .setBizId(ttId)
                .setBizCode(tradeTicketEntity.getCode())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode())
                .setApproveRuleValue(value);


        buildApproveBizData(approveDTO, ttDto, tradeTicketEntity);
        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue())) {
            approveDTO
                    .setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_ALL)
                    .setTtTypeEnum(TTTypeEnum.TRANSFER)
            ;
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())) {
            approveDTO
                    .setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_PART)
                    .setTtTypeEnum(TTTypeEnum.TRANSFER)
            ;
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())) {
            approveDTO
                    .setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER)
                    .setTtTypeEnum(TTTypeEnum.SPLIT)
            ;
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT.getActionValue())) {
            approveDTO
                    .setTtTypeEnum(TTTypeEnum.SPLIT)
                    .setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_NORMAL);
        }
        // BUGFIX：case-1002824 LOA审批场景错误 Author: wam 2024-12-04 end
        approveDTO.setApproveCause("进审原因：" + approveDTO.getApproveRuleValue());
        return approveDTO;
    }

    private ApproveDTO buildApproveDTO(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity, Integer salesType, ContractPriceEntity contractPriceEntity) {

        log.info("======================================================buildApproveDTO");
        ApproveDTO approveDTO = new ApproveDTO();
        Integer contractId = tradeTicketEntity.getContractId();
        ContractDetailInfoDTO contractDetailInfoDTO = null;
        if (null != contractId) {
            contractDetailInfoDTO = contractService.getContractDetailInfoDTO(contractId);
        }
        SalesContractAddTTDTO addTTDTO = ttDto.getSalesContractAddTTDTO();
        SalesContractReviseTTDTO reviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        SalesContractSplitTTDTO splitTTDTO = ttDto.getSalesContractSplitTTDTO();

        BigDecimal addTotalAmout = BigDecimal.ZERO;
        BigDecimal contractTotalAmount = BigDecimal.ZERO;

        //低于基差阈值
        boolean isExtraPriceWarning = false;

        try {
            //获得流程定义
            /*ApproveProcessEnum approveProcessEnum = ApproveProcessEnum.getByTradeInfo(
                    salesType,
                    GoodsCategoryEnum.getByValue(subGoodsCategoryId).name(),
                    contractTradeTypeEnum.name());*/
            log.info("=========================================================category1:{}", tradeTicketEntity.getCategory1());
            log.info("=========================================================category2:{}", tradeTicketEntity.getCategory2());
            log.info("=========================================================category3:{}", tradeTicketEntity.getCategory3());
            CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory1());
            if (null != category1Entity) {
                approveDTO.setCategory1Name(category1Entity.getName());
            }
            CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
            if (null != category2Entity) {
                approveDTO.setCategory2Name(category2Entity.getName());
            }
            if (0 != tradeTicketEntity.getCategory3()) {
                CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());
                if (null != category3Entity) {
                    approveDTO.setCategory3Name(category3Entity.getName());
                }
            }

            String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

            log.info("=========================================================processKey:{}", processKey);
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                    .setTtTypeEnum(TTTypeEnum.getByType(tradeTicketEntity.getType()))
                    .setBuCode(tradeTicketEntity.getBuCode())
                    .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(salesType))
                    .setUserId(JwtUtils.getCurrentUserId())
                    .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                    .setCustomerName(tradeTicketEntity.getCustomerName())
                    .setCustomerId(tradeTicketEntity.getCustomerId())
                    .setSupplierId(tradeTicketEntity.getSupplierId())
                    .setSupplierName(tradeTicketEntity.getSupplierName())
                    .setCompanyId(tradeTicketEntity.getCompanyId())
                    .setCompanyName(tradeTicketEntity.getCompanyName())
                    .setCategory1(tradeTicketEntity.getCategory1())
                    .setCategory2(tradeTicketEntity.getCategory2())
                    .setCategory3(tradeTicketEntity.getCategory3())
                    .setSiteCode(tradeTicketEntity.getSiteCode())
                    .setSiteName(tradeTicketEntity.getSiteName())
                    .setProcessKey(processKey)
                    .setBizModule(moduleType)
                    .setBizId(ttId)
                    .setBizCode(tradeTicketEntity.getCode())
                    .setReferBizId(tradeTicketEntity.getContractId())
                    .setReferBizCode(tradeTicketEntity.getContractCode());


            //组装审批需要的业务数据
            buildApproveBizData(approveDTO, ttDto, tradeTicketEntity);
            log.info("=========================================================processKey:{}", processKey);
            ContractApproveBizInfoDTO contractApproveBizInfoDTO = new ContractApproveBizInfoDTO();
//            contractApproveBizInfoDTO.setCategoryId(tradeTicketEntity.getCategory2());
            contractApproveBizInfoDTO.setSalesType(salesType);
            contractApproveBizInfoDTO.setTtType(tradeTicketEntity.getType());
            contractApproveBizInfoDTO.setTradeType(approveDTO.getContractTradeTypeEnum().getValue());
            contractApproveBizInfoDTO.setCategory2(approveDTO.getCategory2());
            contractApproveBizInfoDTO.setContractType(tradeTicketEntity.getContractType());
            //获取配置值
            SystemRuleDTO systemRuleDTO = new SystemRuleDTO();
            systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
            systemRuleDTO.setRuleCode(SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG.getRuleCode());
            List<SystemRuleVO> listSystemRule = systemRuleFacade.getSystemRule(systemRuleDTO);
            log.info("=========================================================listSystemRule:{}", JSONObject.toJSONString(listSystemRule));

            listSystemRule.forEach(systemRuleVO -> {
                List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = systemRuleVO.getSystemRuleItemVOList();
                List<SystemRuleVO.SystemRuleItemVO> list = systemRuleItemVOList.stream().filter(i -> String.valueOf(tradeTicketEntity.getCompanyId()).equals(i.getCompanyIds())).collect(Collectors.toList());
                systemRuleVO.setSystemRuleItemVOList(list);
            });
            //解析配置
            if (CollectionUtils.isNotEmpty(listSystemRule)) {
                SystemRuleVO systemRuleVO = listSystemRule.get(0);
                HashMap<String, String> mapItem = new HashMap<>();
                List<SystemRuleVO.SystemRuleItemVO> itemList = systemRuleVO.getSystemRuleItemVOList();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (SystemRuleVO.SystemRuleItemVO systemRuleItemVO : itemList) {
                        mapItem.put(systemRuleItemVO.getRuleItemKey(), systemRuleItemVO.getRuleItemValue());
                    }

                    String maxTotalAmount = mapItem.get(ContractApproveConfigItemEnum.MIN_AMOUNT.name());
                    String minTotalAmount = mapItem.get(ContractApproveConfigItemEnum.MAX_AMOUNT.name());
                    String deliveryDueMonthLimit = mapItem.get(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.name());
                    String remainContractNumLimit = mapItem.get(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.name());

                    contractApproveBizInfoDTO.setMinTotalAmount(StringUtil.isNotEmpty(maxTotalAmount) ? Integer.valueOf(maxTotalAmount) : Integer.valueOf(ContractApproveConfigItemEnum.MIN_AMOUNT.getDefaultValue()));
                    contractApproveBizInfoDTO.setMaxTotalAmount(StringUtil.isNotEmpty(minTotalAmount) ? Integer.valueOf(minTotalAmount) : Integer.valueOf(ContractApproveConfigItemEnum.MAX_AMOUNT.getDefaultValue()));
                    contractApproveBizInfoDTO.setDeliveryDueMonthLimit(StringUtil.isNotEmpty(deliveryDueMonthLimit) ? Integer.valueOf(deliveryDueMonthLimit) : Integer.valueOf(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.getDefaultValue()));
                    contractApproveBizInfoDTO.setRemainContractNumLimit(StringUtil.isNotEmpty(remainContractNumLimit) ? Integer.valueOf(remainContractNumLimit) : Integer.valueOf(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.getDefaultValue()));
                }
            }/* else {
                contractApproveBizInfoDTO.setMinTotalAmount(Integer.valueOf(ContractApproveConfigItemEnum.MIN_AMOUNT.getDefaultValue()));
                contractApproveBizInfoDTO.setMaxTotalAmount(Integer.valueOf(ContractApproveConfigItemEnum.MAX_AMOUNT.getDefaultValue()));
                contractApproveBizInfoDTO.setDeliveryDueMonthLimit(Integer.valueOf(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.getDefaultValue()));
                contractApproveBizInfoDTO.setRemainContractNumLimit(Integer.valueOf(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.getDefaultValue()));
            }*/

            log.info("==================================================================contractDetailInfoDTO:{}", JSON.toJSONString(contractDetailInfoDTO));
            if (null != contractDetailInfoDTO) {
                contractTotalAmount = contractDetailInfoDTO.getTotalAmount();
                contractApproveBizInfoDTO.setDeliveryStartTime(contractDetailInfoDTO.getDeliveryStartTime());
                contractApproveBizInfoDTO.setDeliveryEndTime(contractDetailInfoDTO.getDeliveryEndTime());
                contractApproveBizInfoDTO.setTotalBillNum(contractDetailInfoDTO.getTotalBillNum());
                contractApproveBizInfoDTO.setTotalDeliveryNum(contractDetailInfoDTO.getTotalDeliveryNum());
                contractApproveBizInfoDTO.setContractNum(contractDetailInfoDTO.getContractNum());
            }
            Integer TTType = TTTypeEnum.NEW.getType();
            //关键价格信息
            if (null != contractPriceEntity
                    && (tradeTicketEntity.getType().equals(TTTypeEnum.NEW.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.ASSIGN.getType())
            )) {

                BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), TTType);

                contractApproveBizInfoDTO.setProteinDiffPrice(proteinDiffPrice);
                contractApproveBizInfoDTO.setTransportPrice(contractPriceEntity.getTransportPrice());
                contractApproveBizInfoDTO.setRefineDiffPrice(contractPriceEntity.getRefineDiffPrice());
                contractApproveBizInfoDTO.setBusinessPrice(contractPriceEntity.getBusinessPrice());
                contractApproveBizInfoDTO.setOtherPrice(contractPriceEntity.getOtherPrice());
            }
            //关键价格是否变化
            List<CompareObjectDTO> compareObjectDTOList = null;
            if (null != ttDto.getSalesContractReviseTTDTO()) {
                TTType = TTTypeEnum.REVISE.getType();
                compareObjectDTOList = JSON.parseArray(ttDto.getSalesContractReviseTTDTO().getModifyContent(), CompareObjectDTO.class);
            }
            if (null != ttDto.getSalesContractSplitTTDTO()) {
                TTType = TTTypeEnum.SPLIT.getType();
                compareObjectDTOList = JSON.parseArray(ttDto.getSalesContractSplitTTDTO().getModifyContent(), CompareObjectDTO.class);
                if (ttDto.getSalesContractSplitTTDTO().getAddedSignatureType() == 1) {
                    contractApproveBizInfoDTO.setAddedSignatureType(1);
                }
            }
            log.info("==================================================================compareObjectDTOList:{}", JSON.toJSONString(compareObjectDTOList));
            if (null != compareObjectDTOList) {
                Map<String, CompareObjectDTO> mapObj = BeanCompareUtils.mapCompareObjectDTO(compareObjectDTOList);
                Set<String> nameList = mapObj.keySet();
                //List<String> nameList = compareObjectDTOList.stream().map(CompareObjectDTO::getName).collect(Collectors.toList());
                if (CommonListUtil.notNullOrEmpty(nameList)) {
                    // 对应TT字段中【含税单价】组成的【蛋白价差】数据变化
                    log.info("nameList", JSON.toJSONString(nameList));

                    if (nameList.contains("contractType")) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("contractType");
                        Integer contractType1 = Integer.valueOf(compareObjectDTO.getBefore());
                        Integer contractType2 = Integer.valueOf(compareObjectDTO.getAfter());
                        if (contractType1 == ContractTypeEnum.JI_CHA.getValue()
                                && contractType2 == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
                            contractApproveBizInfoDTO.setContract2To4(true);
                        }
                    }

                    Boolean changed = true;
                    if (nameList.contains("customerId") && salesType == 2) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("customerId");
                        String before = compareObjectDTO.getBefore();
                        String after = compareObjectDTO.getAfter();
                        changed = customerFacade.customerJudge(Integer.parseInt(before), Integer.parseInt(after));
                    }

                    if (nameList.contains("supplierId") && salesType == 1) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("supplierId");
                        String before = compareObjectDTO.getBefore();
                        String after = compareObjectDTO.getAfter();
                        changed = customerFacade.customerJudge(Integer.parseInt(before), Integer.parseInt(after));
                    }
                    //判断是否同集团
                    if (!changed) {
                        contractApproveBizInfoDTO.setCustomerGroupChanged(true);
                    }

                    /*BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), TTType);
                    contractApproveBizInfoDTO.setProteinDiffPriceChanged(proteinDiffPrice.compareTo(BigDecimal.ONE) == 0);*/

                    if (nameList.contains("proteinDiffPrice")) {
                        BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), TTType);
                        contractApproveBizInfoDTO.setProteinDiffPriceChanged(proteinDiffPrice.compareTo(BigDecimal.ONE) == 0);
                    }
                    if (nameList.contains("transportPrice")) {
                        contractApproveBizInfoDTO.setTransportPriceChanged(true);
                    }
                    if (nameList.contains("refineDiffPrice")) {
                        contractApproveBizInfoDTO.setRefineDiffPriceChanged(true);
                    }
                    if (nameList.contains("businessPrice")) {
                        contractApproveBizInfoDTO.setBusinessPriceChanged(true);
                    }
                    if (nameList.contains("otherPrice")) {
                        contractApproveBizInfoDTO.setOtherPriceChanged(true);
                    }
                    // 变更交期 交货结束日期发生变化
                    if (nameList.contains("deliveryEndTime")) {
                        contractApproveBizInfoDTO.setDeliveryEndTimeChanged(true);
                    }
                }
            }

            if (null != addTTDTO) {
                //如果是新增
                contractApproveBizInfoDTO.setDeliveryStartTime(addTTDTO.getDeliveryStartTime());
                contractApproveBizInfoDTO.setDeliveryEndTime(addTTDTO.getDeliveryEndTime());
                contractApproveBizInfoDTO.setSignDate(addTTDTO.getSignDate());
            }
            log.info("===================================================================ttTypeEnum:{}", ttTypeEnum.getType());
            switch (ttTypeEnum) {
                case NEW:
                case BUYBACK:
                case ASSIGN:
                    BigDecimal contractNum = new BigDecimal(addTTDTO.getContractNum());
                    addTotalAmout = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, new BigDecimal(addTTDTO.getUnitPrice()));

                    contractApproveBizInfoDTO.setTotalAmount(addTotalAmout);

                    if (null != addTTDTO.getContractType() && ContractTypeEnum.ZAN_DING_JIA.getValue() == Integer.valueOf(addTTDTO.getContractType())) {
                        contractApproveBizInfoDTO.setNewContract3(true);
                    }
                    isExtraPriceWarning = LOAPriceWarning(ttId);
                    break;
                case REVISE:
                    contractApproveBizInfoDTO.setDeliveryStartTime(reviseTTDTO.getDeliveryStartTime());
                    contractApproveBizInfoDTO.setDeliveryEndTime(reviseTTDTO.getDeliveryEndTime());
                    contractApproveBizInfoDTO.setTotalAmount(reviseTTDTO.getTotalAmount());
                    contractApproveBizInfoDTO.setSignDate(reviseTTDTO.getSignDate());
                    break;
                case SPLIT:
                    contractApproveBizInfoDTO.setDeliveryStartTime(splitTTDTO.getDeliveryStartTime());
                    contractApproveBizInfoDTO.setDeliveryEndTime(splitTTDTO.getDeliveryEndTime());
                    contractApproveBizInfoDTO.setTotalAmount(splitTTDTO.getTotalAmount());
                    contractApproveBizInfoDTO.setSignDate(splitTTDTO.getSignDate());
                    break;
                case WASHOUT:
                    BigDecimal washoutContractNum = addTTDTO.getContractNum() == null ? BigDecimal.ZERO : new BigDecimal(addTTDTO.getContractNum());
                    BigDecimal washoutUnitPrice = addTTDTO.getWashoutUnitPrice();
                    if (null != ttDto.getPriceDetailBO() && tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()) {
                        washoutUnitPrice = washoutUnitPrice.add(ttDto.getPriceDetailBO().getForwardPrice());
                    }
                    contractTotalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, washoutContractNum, washoutUnitPrice);
                    contractApproveBizInfoDTO.setTotalAmount(contractTotalAmount);
                    break;

                case CLOSED:
                    BigDecimal contractRemainNum = contractApproveBizInfoDTO.getContractNum()
                            .subtract(contractApproveBizInfoDTO.getTotalDeliveryNum());

                    Integer weightTolerance = contractDetailInfoDTO.getWeightTolerance();
                    BigDecimal maxWeightTolerance = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractDetailInfoDTO.getContractNum(), BigDecimalUtil.div(weightTolerance, 100));

                    log.info("========================================================contractRemainNum:{}", contractRemainNum);
                    log.info("========================================================weightTolerance:{}", weightTolerance);
                    log.info("========================================================maxWeightTolerance:{}", maxWeightTolerance);
                    log.info("CLOSED contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
                    if (contractRemainNum.compareTo(new BigDecimal(String.valueOf(contractApproveBizInfoDTO.getRemainContractNumLimit()))) >= 0
                            && BigDecimalUtil.isGreater(contractRemainNum, maxWeightTolerance)) {
                        contractApproveBizInfoDTO.setRemainMuch(true);
                    }
                    break;
                default:
                    break;
            }

            contractApproveBizInfoDTO.setLowExtraPrice(isExtraPriceWarning);
            contractApproveBizInfoDTO.setBuCode(BuCodeEnum.ST.getValue());
            contractApproveBizInfoDTO.setTtCreatedAt(tradeTicketEntity.getCreatedAt());

            //交期大于等于12个月（配置）
            int deliveryDueMonth = DateTimeUtil.getDiffMonth(contractApproveBizInfoDTO.getSignDate(), contractApproveBizInfoDTO.getDeliveryStartTime());
            log.info("ttCode:{},ttId:{},deliveryDueMonth:{}", tradeTicketEntity.getCode(), ttId, deliveryDueMonth);
            if (deliveryDueMonth >= contractApproveBizInfoDTO.getDeliveryDueMonthLimit()) {
                contractApproveBizInfoDTO.setDeliveryLong(true);
            }
            log.info("ttCode:{},ttId:{}, finalContractApproveBizInfoDTO:{}", tradeTicketEntity.getCode(), ttId, JSON.toJSONString(contractApproveBizInfoDTO));

            approveDTO.setApproveBizInfoDTO(contractApproveBizInfoDTO);
        } catch (Exception e) {
            log.debug("buildApproveDTO exception:{}", e);
            approveDTO.setMemo("buildApproveDTO exception");
            operationLogFacade.saveTraceLog(new TraceLogDTO(String.valueOf(approveDTO.getReferBizId()), "buildApproveDTO.exception", JSON.toJSONString(e)));
        }
        log.info("=======================================================================参数组装结束");
        return approveDTO;
    }

    private int getApprovalValue(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity) {
        int value = ContractApproveRuleEnum.NONE.getValue();

        //TODO NEO 这个要修改为按照TradeType进行判断
        switch (ContractActionEnum.getByType(tradeTicketEntity.getContractSource())) {
            case NEW:
                //新增审批流程
                SalesContractAddTTDTO salesContractAddTTDTO = ttDto.getSalesContractAddTTDTO();
                String unitPrice = salesContractAddTTDTO.getUnitPrice();
                value = ttApproveService.calcApproveRuleValue(salesContractAddTTDTO.getGoodsCategoryId(), unitPrice, salesContractAddTTDTO.getContractNum(), new Date(), salesContractAddTTDTO.getDeliveryStartTime());

                //新增基差预警
                if (value == ContractApproveRuleEnum.NONE.getValue() && tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()) {
                    if (this.LOAPriceWarning(ttId)) {
                        value = ContractApproveRuleEnum.A.getValue();
                    }
                }
                break;
            case REVISE:
            case REVISE_CUSTOMER:
                value = ttApproveService.calcApproveRuleValue(ContractActionEnum.REVISE.getActionValue(), ttDto);
                break;
            case SPLIT:
            case SPLIT_CUSTOMER:
                //修改、拆分的审批流程
                value = ttApproveService.calcApproveRuleValue(ContractActionEnum.SPLIT.getActionValue(), ttDto);
                break;
            case STRUCTURE_PRICE_CONFIRM:
            case PRICE_CONFIRM:
            case PRICE_FIXED:
                value = ContractApproveRuleEnum.NONE.getValue();
                break;
            case TRANSFER_CONFIRM:
            case TRANSFER_ALL_CONFIRM:
                ContractEntity contractEntity = contractService.getBasicContractById(ttDto.getSalesContractTTTransferDTO().getSonContractId());
                if (ContractSalesTypeEnum.PURCHASE.getValue() == ttDto.getSalesContractTTTransferDTO().getSalesType()) {
                    value = ContractApproveRuleEnum.AB.getValue();
                } else {
                    // 已转月次数
                    int transferTimes = contractEntity.getTransferredTimes();
                    // 判断是否是超远期合同
                    int standard = contractEntity.getIsOverForward() == 1 ? 3 : 2;
                    value = transferTimes >= standard ? ContractApproveRuleEnum.AB.getValue() : ContractApproveRuleEnum.A.getValue();
                }
                break;
            case REVERSE_PRICE_CONFIRM:
            case REVERSE_PRICE_ALL_CONFIRM:
            case PUT_BACK:
//            case STRUCTURE_PRICING:
            case BUYBACK:
            case WASHOUT:
                value = ContractApproveRuleEnum.AB.getValue();
                break;
            case CLOSED:
            case INVALID:
                value = ContractApproveRuleEnum.A.getValue();
                break;
            default:
                break;
        }
        return value;
    }

    protected void buildApproveBizData(ApproveDTO approveDTO, TTDTO ttdto, TradeTicketEntity tradeTicketEntity) {
        List<ApproveBizInfoDTO> approveBizInfoDTOList = new ArrayList<>();
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(1)
                .setName("salesType")
                .setDisplayName("采/销")
                .setValue(salesType.getDescription() + "合同")
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                        .setIndex(2)
                        .setName("subCategoryName")
                        .setDisplayName("品种")
                        // BUGFIX：case-1002589 荆州天佳 TJIBSBMP2400175是豆粕合同，但是审批流程显示的是豆油合同 Author: Mr 2024-06-17 Start
                        //.setValue(GoodsCategoryEnum.getByValue(subGoodsCategoryId).getDesc())
                        .setValue(approveDTO.getCategory2Name())
                // BUGFIX：case-1002589 荆州天佳 TJIBSBMP2400175是豆粕合同，但是审批流程显示的是豆油合同 Author: Mr 2024-06-17 End
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(3)
                .setName("tradeTypeName")
                .setDisplayName("交易类型")
                .setValue(ttTypeEnum.getDesc())
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(4)
                .setName("contractTypeName")
                .setDisplayName("合同类型")
                .setValue(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(5)
                .setName("contractCode")
                .setDisplayName("合同编号")
                .setValue(approveDTO.getReferBizCode())
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(6)
                .setName("customerName")
                .setDisplayName("客户名称")
                .setValue(approveDTO.getCustomerName())
        );

        String bizDetailDescription = buildBizDetailDescription(ttdto);

        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(7)
                .setName("bizDetailDescription")
                .setDisplayName("业务详情")
                .setValue(bizDetailDescription)
        );
        Object data = JSON.toJSONString(approveBizInfoDTOList);
        approveDTO.setBizData(data);

    }

    public abstract String buildBizDetailDescription(TTDTO ttdto);

    /**
     * 合同协议相关操作
     */
    @Override
    public ContractSignEntity createContractSign(ContractSignCreateDTO contractSignCreateDTO, Integer ttId) {
        //创建合同协议
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        contractSignCreateDTO.setTradeType(tradeTicketEntity.getTradeType())
                .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-协议列表 Author: Mr 2024-04-29 Start
                .setCustomerId(tradeTicketEntity.getCustomerId())
                .setSupplierId(String.valueOf(tradeTicketEntity.getSupplierId()))
                .setCustomerName(tradeTicketEntity.getCustomerName())
                .setSupplierName(tradeTicketEntity.getSupplierName())
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSiteCode(tradeTicketEntity.getSiteCode())
                .setSiteName(tradeTicketEntity.getSiteName())
        ;
        if (null != tradeTicketEntity.getSignId()) {
            contractSignCreateDTO.setContractSignId(tradeTicketEntity.getSignId());
        }
        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-协议列表 Author: Mr 2024-04-29 End

        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        ContractSignEntity contractSign = contractSignService.createOrUpdateContractSign(contractSignCreateDTO);
        //回填协议信息到TT
        log.info("check_code_question   createContractSign3  ttId:{} ", ttId);
        tradeTicketDao.updateSignInfo(ttId, contractSign);
        return contractSign;
    }

    public void updateContractSign(ContractSignEntity contractSignEntity, ContractEntity contractEntity, TradeTicketEntity tradeTicketEntity) {

        Integer customerId = contractSignEntity.getCustomerId();

        Integer companyId = null;
        String companyName = null;

        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType()) {
            customerId = Integer.valueOf(contractSignEntity.getSupplierId());
        }

        //查询客户是否是需要正本
        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();

        customerOriginalPaperDTO.setCustomerId(customerId);
        customerOriginalPaperDTO.setSaleType(contractEntity.getSalesType());
        customerOriginalPaperDTO.setStatus(DisableStatusEnum.ENABLE.getValue());
        customerOriginalPaperDTO.setCompanyId(contractEntity.getCompanyId());
        customerOriginalPaperDTO.setCategory2(String.valueOf(contractEntity.getCategory2()));
        customerOriginalPaperDTO.setCategory3(String.valueOf(contractEntity.getCategory3()));


        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);


        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        contractSignService.updateContractSign(contractEntity, customerOriginalPaperEntity, contractSignEntity);
    }

    /**
     * 保存TT，所有处理的场景入口
     */
    @Override
    public Result<List<TTQueryVO>> saveTT(TTDTO ttdto) {
        log.info("check_code_question2  saveTT   ");
        log.info("==========saveTT.ttdto:{}", JSON.toJSONString(ttdto));
        List<TTQueryVO> list = new ArrayList<>();
        //生成唯一id绑定两个TT
        String groupId = null;
        if (ttdto.getProcessorType().contains("SPLIT")
                || ttdto.getProcessorType().contains("BUYBACK")
                || ttdto.getProcessorType().contains("REVERSE_PRICE")
                || (ttdto.getProcessorType().contains("TRANSFER")
                || (ttdto.getProcessorType().contains("REVISE") && ttdto.getReviseCustomerType() != null && ttdto.getReviseCustomerType())
        )
        ) {
            //生成唯一id绑定两个TT
            groupId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        ttdto.setGroupId(groupId);

        //生成主合同TT
        TradeTicketEntity tradeTicketEntity = save(ttdto);
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);

        // 修改、拆分保存操作只产生新录入的TT
        if (ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            return saveModifyTT(ttdto, tradeTicketEntity, list);
        }

        //拆分
        if (ttdto.getProcessorType().contains("SPLIT")) {
            SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();
            //全量主体拆分不生成数量补充协议
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            //生成数量补充tt,协议
            if (salesContractSplitTTDTO.getContractNum().compareTo(contractEntity.getContractNum()) == 0) {
                salesContractSplitTTDTO.setSignType(ContractSignTypeEnum.ALL_SPLIT.getValue());
            } else {
                salesContractSplitTTDTO.setSignType(ContractSignTypeEnum.PARTLY_SPLIT.getValue());
            }
            salesContractSplitTTDTO.setAddedSignatureType(1);
            ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
            tradeTicketEntity = save(ttdto);
            TTQueryVO sourceTtQueryVO = new TTQueryVO();
            sourceTtQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                    .setSourceFlag(1)
                    .setCode(tradeTicketEntity.getCode())
                    .setTtId(tradeTicketEntity.getId());
            list.add(sourceTtQueryVO);
        }

        //回购生成原合同不可见关联TT
        if (ttdto.getProcessorType().contains("BUYBACK")) {
            SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
            salesContractAddTTDTO.setAddedSignatureType(-1);
            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
            save(ttdto);
        }

        //部分转月,反点价生成原合同不可见关联TT
        if (ttdto.getProcessorType().contains("REVERSE_PRICE")
                || (ttdto.getProcessorType().contains("TRANSFER"))
            // && TTTransferTypeEnum.PART_TRANSFER_MONTH.getValue().equals(ttdto.getSalesContractTTTransferDTO().getType()))
        ) {
            SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
            salesContractTTTransferDTO.setAddedSignatureType(-1);
            ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
            save(ttdto);
        }

        //修改主体生成原合同不可见关联TT
        if (ttdto.getProcessorType().contains("REVISE") && ttdto.getReviseCustomerType() != null && ttdto.getReviseCustomerType()) {
            SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();
            salesContractReviseTTDTO.setAddedSignatureType(-1);
            ttdto.setSalesContractReviseTTDTO(salesContractReviseTTDTO);
            save(ttdto);
        }

        return Result.success(list);
    }

    // 保存修改、拆分的TT
    private Result<List<TTQueryVO>> saveModifyTT(TTDTO ttdto, TradeTicketEntity tradeTicketEntity, List<TTQueryVO> list) {

        // 修改主体更新主体信息
        if (ttdto.getChangeCustomerFlag() != null && ttdto.getChangeCustomerFlag()) {
            Integer salesType = tradeTicketEntity.getSalesType();
            if (salesType != null && salesType.equals(ContractSalesTypeEnum.SALES.getValue())) {
                CustomerDTO customer = customerFacade.getCustomerById(ttdto.getOriginCustomerId());
                if (customer != null) {
                    updateEntityAndModify(tradeTicketEntity, ttModifyDao.getByTTId(tradeTicketEntity.getId()), customer.getId(), customer.getName(), customer.getLinkageCustomerCode(), true);
                }
            } else {
                CustomerDTO supplier = customerFacade.getCustomerById(ttdto.getOriginCustomerId());
                if (supplier != null) {
                    updateEntityAndModify(tradeTicketEntity, ttModifyDao.getByTTId(tradeTicketEntity.getId()), supplier.getId(), supplier.getName(), null, false);
                }
            }
        }

        // 拆分一口价保存定价单信息
        if (StringUtils.isNotBlank(ttdto.getConfirmPriceInfo())) {
            tradeTicketDao.updateById(tradeTicketEntity.setConfirmPriceInfo(ttdto.getConfirmPriceInfo()));
        }

        return Result.success(list);
    }

    // 更新TT和副表
    private void updateEntityAndModify(TradeTicketEntity entity, TTModifyEntity modifyEntity, Integer id, String name, String code, boolean isCustomer) {
        Optional.ofNullable(entity)
                .ifPresent(e -> {
                    if (isCustomer) {
                        e.setCustomerId(id).setCustomerName(name).setCustomerCode(code);
                    } else {
                        e.setSupplierId(id).setSupplierName(name);
                    }
                    tradeTicketDao.updateById(e);
                });

        Optional.ofNullable(modifyEntity)
                .ifPresent(m -> {
                    if (isCustomer) {
                        m.setCustomerId(id).setCustomerName(name).setCustomerCode(code);
                    } else {
                        m.setSupplierId(id).setSupplierName(name);
                    }
                    ttModifyDao.updateById(m);
                });
    }

    private void createOriginalContractTT(TTDTO ttdto, SalesContractSplitTTDTO salesContractSplitTTDTO, List<TTQueryVO> list) {
        log.info("check_code_question2  createOriginalContractTT   ");
        salesContractSplitTTDTO.setAddedSignatureType(-1);
        ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
        TradeTicketEntity tradeTicketEntity = save(ttdto);
        TTQueryVO sourceTtQueryVO = new TTQueryVO();
        sourceTtQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setSourceFlag(1)
                .setTtId(tradeTicketEntity.getId());
        list.add(sourceTtQueryVO);
    }

    public void intParam(String processorType) {

    }

    protected TradeTicketEntity save(TTDTO ttdto) {
        intParam(ttdto.getProcessorType());
        TTDTO ttDto1 = initDTO(ttdto);
        TradeTicketEntity tradeTicketEntity = operateTT(ttDto1);

        if (ttdto.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            // 如果存在定价单，同步定价单信息
            commonLogicService.syncTTPriceInfo(tradeTicketEntity.getSiteCode(), tradeTicketEntity.getId(), LkgInterfaceActionEnum.PRICE.getSyncType());
        }

        return tradeTicketEntity;
    }

    public TradeTicketEntity operateTT(TTDTO ttDto) {
        return transactionTemplate.execute(new TransactionCallback<TradeTicketEntity>() {
            @Override
            public TradeTicketEntity doInTransaction(TransactionStatus transactionStatus) {
                try {
                    //保存主表信息
                    TradeTicketEntity tradeTicketEntity = saveTradeTicket(ttDto);
                    log.info("==========operateTT.tradeTicketEntity:{}", JSON.toJSONString(tradeTicketEntity));
                    Integer ttId = tradeTicketEntity.getId();
                    log.info("==========operateTT.ttId:{}", ttId);
                    //保存副表
                    saveTTSubInfo(ttId, ttDto);

                    // 修改、拆分保存操作只产生新录入的TT
                    if (ttDto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {

                        SalesContractAddTTDTO salesContractAddTTDTO = ttDto.getSalesContractAddTTDTO();
                        // 删除原来的tt,保证tt编号相同
                        if (null != salesContractAddTTDTO) {
                            Integer oldTtId = salesContractAddTTDTO.getTtId();
                            if (null != oldTtId) {
                                tradeTicketDao.deleteById(oldTtId);
                                tradeTicketEntity.setCode(salesContractAddTTDTO.getCode());

                                // 回滚redis
                                //redisFacade.rollBackRedis(salesContractAddTTDTO.getCode().split("-")[0]);
                            }
                        }

                        // 状态为新录入状态
                        tradeTicketDao.updateById(tradeTicketEntity
                                .setStatus(TTStatusEnum.NEW.getType())
                                .setSourceType(SubmitTypeEnum.SAVE.getValue()));
                        return tradeTicketEntity;
                    }

                    //拆分 原合同 记录TT
                    if ((ttDto.getProcessorType().contains("SPLIT") && ttDto.getSalesContractSplitTTDTO().getAddedSignatureType() == -1) ||
                            (ttDto.getProcessorType().contains("BUYBACK") && ttDto.getSalesContractAddTTDTO().getAddedSignatureType() == -1) ||
                            (ttDto.getProcessorType().contains("REVERSE_PRICE") && ttDto.getSalesContractTTTransferDTO().getAddedSignatureType() == -1) ||
                            (ttDto.getProcessorType().contains("TRANSFER") && ttDto.getSalesContractTTTransferDTO().getAddedSignatureType() == -1) ||
                            (ttDto.getProcessorType().contains("REVISE") && ttDto.getSalesContractReviseTTDTO().getAddedSignatureType() == -1)
                    ) {
                        tradeTicketDao.hideTT(ttId);
                        return tradeTicketEntity;
                    }

                    //新增TT,回购 TT 未提交前不创建协议 FIXME 在外层方法中进行了提交
                    if (addProcessorList.contains(ttDto.getProcessorType())) {
                        return tradeTicketEntity;
                    }
                    log.info("=============================================ttDto.getSalesContractTTTransferDTO():{}", ttDto.getSalesContractTTTransferDTO());
                    ContractSignCreateDTO contractSignCreateDTO = convertToContractSignCreateDTO(ttId, ttDto);
                    //创建协议
                    log.info("check_code_question2 ttId:{} operateTT  doInTransaction  ", ttId);
                    ContractSignEntity contractSign = createContractSign(contractSignCreateDTO, ttId);
                    log.info("==========operateTT.contractSign:{}", JSON.toJSONString(contractSign));


                    if ((ttDto.getProcessorType().contains("REVISE"))) {
                        List<CompareObjectDTO> list = new ArrayList<>();

                        //基差变一口价合同,不发起审批
                        if (ttDto.getSalesContractReviseTTDTO().getModifyContent().contains("contractType")) {
                            List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(ttDto.getSalesContractReviseTTDTO().getModifyContent(), CompareObjectDTO.class);
                            list = compareObjectDTOList.stream().filter(i -> "contractType".equalsIgnoreCase(i.getName())
                                    && i.getBefore().equalsIgnoreCase(String.valueOf(ContractTypeEnum.JI_CHA.getValue()))
                                    && i.getAfter().equalsIgnoreCase(String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()))).collect(Collectors.toList());
                            log.info("operateTT.revise.notCreate.list:{}", JSON.toJSONString(list));
                        }

                        //基差暂定价、暂定价合同 定价完成，不发起审批
                        List<CompareObjectDTO> contentList = JSON.parseArray(ttDto.getSalesContractReviseTTDTO().getContent(), CompareObjectDTO.class);
                        List<CompareObjectDTO> collect = contentList.stream().filter(i -> "contractCode".equalsIgnoreCase(i.getName())).collect(Collectors.toList());
                        boolean flag = false;
                        if (CollectionUtil.isNotEmpty(collect)) {
                            String contractCode = collect.get(0).getAfter();
                            List<CompareObjectDTO> contractTypeList = contentList.stream().filter(i -> "contractType".equalsIgnoreCase(i.getName())).collect(Collectors.toList());
                            log.info("operateTT.revise.notCreate.contractCode:{}", contractCode);
                            ContractEntity contractEntity = contractService.getBasicContractByCode(contractCode);
                            if (!String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()).equalsIgnoreCase(contractTypeList.get(0).getAfter())
                                    &&
                                    //contractEntity.getContractNum().compareTo(contractEntity.getTotalPriceNum()) == 0
                                    ttDto.getSalesContractReviseTTDTO().getReviseType() == 2
                            ) {
                                flag = true;
                            }
                        }
                        log.info("operateTT.revise.notCreate.flag:{}", flag);
                        if (CollectionUtil.isNotEmpty(list) || flag) {
                            //更新TT状态
                            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.DONE.getType(), TTApproveStatusEnum.WITHOUT_APPROVE.getValue(), tradeTicketEntity.getCode());
                            //更新协议的状态
                            IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
                            contractSignService.deleteContractSignById(contractSign.getId());
                            return tradeTicketEntity;
                        }
                    }
                    //生成合同(仅在结构化定价时)
                    createContract(tradeTicketEntity, ttDto);

                    //发起审批
                    recordTTOperationDetail(OperationActionEnum.SUBMIT, tradeTicketEntity);

                    log.info("check_code_question2 ttId:{} startTTApprove4   ", ttId);
                    startTTApprove(ttId, ttDto, null);

                    return tradeTicketEntity;
                } catch (BusinessException e) {
                    transactionStatus.setRollbackOnly();
                    log.error("save TT BusinessException error :{}", e);
                    e.printStackTrace();
                    throw e;
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();
                    log.error("save TT error :{}", e);
                    e.printStackTrace();
                    throw new BusinessException(ResultCodeEnum.TT_FAIL, JSON.toJSONString(e));
                }
            }
        });
    }

    /**
     * 更新
     */
    @Override
    public Result<List<TTQueryVO>> updateTT(TTDTO ttdto) {
        log.info("check_code_question2  updateTT   ");
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        TradeTicketEntity tradeTicketEntity = save(ttdto);
        Integer salesType = salesContractAddTTDTO.getSalesType();
        recordTTOperationDetail(OperationActionEnum.UPDATE_INFO, tradeTicketEntity);

        // 待修改提交保存，不改变TT状态
        tradeTicketDao.updateById(tradeTicketEntity.setStatus(TTStatusEnum.WAITING.getType()));

        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        return Result.success(list);
    }

    @Override
    public void approveTT(ApprovalDTO approvalDTO, Integer ttType) {
        log.info("===================BaseTradeTicketAbstractService.approveTT====================");
        log.info(JSON.toJSONString(approvalDTO));
        //审批
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(approvalDTO.getTtCode());
        /*String processorType = TTHandlerUtil.getTTProcessor(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        String contractSignValue = getByTTValue(processorType).getProcessKey();*/
        String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));
        Result result = approve(approvalDTO, processKey, tradeTicketEntity);

        log.info("===================approveResult:===================");
        log.info("approveTTCode:{},result:{}", approvalDTO.getTtCode(), JSON.toJSONString(result));
        if (!result.isSuccess()) {
            log.info(" approveTTCode:{},approve error : {} ", approvalDTO.getTtCode(), JSON.toJSONString(result));
            throw new BusinessException(result.getMessage());
        }
        //根据审批结果更新tt状态
        handleAfterApproving(result, ttType, approvalDTO.getTtCode(), approvalDTO.getMemo());
    }

    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        log.info("handleAfterApprovingResult:{}", JSON.toJSONString(result.getData()));
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            log.info("check_approve_question ticketCode:{} handleAfterApproving finish", ttCode);
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());
            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }
        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }

    public void handleCancelOrRejectResult(TradeTicketEntity tradeTicketEntity, String memo) {
        //合同作废
        ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
        contractModifyDTO.setTtId(tradeTicketEntity.getId());
        contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
        contractModifyDTO.setTtType(tradeTicketEntity.getType());
        contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
        cancelContractModifyService.cancelContractModify(contractModifyDTO);
        log.info("========== handleCancelOrRejectResult.ttId:{}", tradeTicketEntity.getId());
        log.info("========== handleCancelOrRejectResult.contractModifyDTO:{}", JSON.toJSONString(contractModifyDTO));
        log.info("check_code_question  invalidTTById ");
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), memo, tradeTicketEntity.getId());
        //作废协议
        invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, memo, tradeTicketEntity.getSignId());
        if (tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())) {
            TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(tradeTicketEntity.getId());
            TTModifyEntity modifyByRelationId = ttModifyDao.getModifyByRelationTTId(ttModifyEntity.getRelationId(), tradeTicketEntity.getId());
            log.info("========== handleCancelOrRejectResult.modifyByRelationId:{}", JSON.toJSONString(modifyByRelationId));
            if (modifyByRelationId != null) {
                log.info("check_code_question  invalidTTById ");
                tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), "关联作废:" + memo, modifyByRelationId.getTtId());
                TradeTicketEntity tradeTicketEntityById = tradeTicketDao.getTradeTicketEntityById(modifyByRelationId.getTtId());
                log.info("========== handleCancelOrRejectResult.tradeTicketEntityById:{}", JSON.toJSONString(tradeTicketEntityById));
                if (null != tradeTicketEntityById.getSignId() && tradeTicketEntityById.getSignId() != 0) {
                    invalidContractSign(modifyByRelationId.getTtId(), tradeTicketEntityById, "关联作废:" + memo, tradeTicketEntityById.getSignId());
                    cancelActiviti("关联作废:" + memo, JwtUtils.getCurrentUserId(), tradeTicketEntityById);
                }
            }
        }
    }

    @Override
    public void cancelTT(OperateTTDTO operateTTDTO) {
        Integer ttId = operateTTDTO.getTtId();
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        cancel(operateTTDTO, tradeTicketEntity);
        //记录操作日志
        recordTTOperationDetail(OperationActionEnum.CANCEL_SUBMIT, tradeTicketEntity);
    }

    @Override
    public void invalidTT(OperateTTDTO operateTTDTO) {
        Integer ttId = operateTTDTO.getTtId();
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        invalid(operateTTDTO, tradeTicketEntity);
        // 保存文件关系表
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(tradeTicketEntity.getSignId())
                .setCategoryType(FileCategoryType.CANCEL_CONTRACT.getCode())
                .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setFileIdList(operateTTDTO.getFileIdList())
        );
        //记录操作日志
        recordTTOperationDetail(OperationActionEnum.CANCEL_TT, tradeTicketEntity);
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = queryDetail(ttId, tradeTicketEntity);
        ttDetailVO.setTtStatus(TTStatusEnum.getDescByValue(tradeTicketEntity.getStatus()));
        //查询协议状态
        if (TTStatusEnum.WAITING.getType() == tradeTicketEntity.getStatus()) {
            //待修改提交区的协议为异常状态
            ttDetailVO.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
        } else {
            ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(tradeTicketEntity.getId());
            if (contractSignDetail != null) {
                Integer status = contractSignDetail.getStatus();
                ttDetailVO.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());
            }
        }
        ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getContractId());
        if (contractEntity != null) {
            Integer status = contractEntity.getStatus();
            ttDetailVO.setContractStatus(ContractStatusEnum.getDescByValue(status));
        }
        // 付款条件代码
        Result payConditionResult = payConditionFacade.getPayConditionById(tradeTicketEntity.getPayConditionId());
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            ttDetailVO.setPayConditionCode(payConditionEntity.getCode());
        }
        // 兼容处理
        if (null == ttDetailVO.getTtQueryDetailVO()) {
            TTQueryDetailVO ttQueryDetailVO = BeanConvertUtils.map(TTQueryDetailVO.class, tradeTicketEntity);
            ttQueryDetailVO.setTtId(tradeTicketEntity.getId());
            ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
            if (null != ttQueryDetailVO && null != contractEntity) {
                ttQueryDetailVO.setTotalDeliveryNum(contractEntity.getTotalDeliveryNum());
            }
        }
        return ttDetailVO;
    }

    @Override
    public SignTemplateDTO getSignTemplateDTO(Integer ttId) {
        return convertToSignTemplateDTO(ttId);
    }

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return tradeTicketDao.updateContractId(ttId, contractId);
    }

    @Override
    public Result complete(Integer contractId, Integer ttId) {
        //修改TT状态
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        log.info("check_code_question  complete ");
        tradeTicketDao.updateStatusById(TTStatusEnum.DONE.getType(), TTApproveStatusEnum.APPROVE.getValue(), ttId);
        ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
        contractModifyDTO.setContractId(contractId)
                .setContractSource(tradeTicketEntity.getContractSource())
                .setTtId(ttId);

        log.info("start complete syncContract:{}", JSON.toJSONString(contractModifyDTO));

        ContractConfirmResultDTO contractConfirmResultDTO = confirmLogicService.confirmContractModify(contractModifyDTO);

        log.info("end complete syncContract:{}", JSON.toJSONString(contractConfirmResultDTO));

        // 记录同步结果
        contractAsyncExecutor.recordContractConfirmLog(tradeTicketEntity, JSON.toJSONString(contractModifyDTO), JSON.toJSONString(contractConfirmResultDTO));
        return Result.success();
    }

    /**
     * 新增业务的入口
     *
     * @param submitTTDTO
     * @return
     */
    @Override
    public Result<List<TTQueryVO>> submitTT(SubmitTTDTO submitTTDTO) {
        intParam(submitTTDTO.getTtProcessor());
        return submit(submitTTDTO);
    }

    @Override
    public void deleteTT(Integer ttId) {
        tradeTicketDao.deleteById(ttId);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);

        // 删除：一个合同仅支持保存一个变更TT
        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + tradeTicketEntity.getContractCode());
        if (StringUtils.isNotBlank(saveTimes) && StringUtils.isNotBlank(tradeTicketEntity.getContractCode())) {
            redisUtil.del(CONTRACT_SAVE_TT_TIMES + tradeTicketEntity.getContractCode());
        }

        recordTTOperationDetail(OperationActionEnum.DELETE_TT, tradeTicketEntity);
    }

    public void handleApprovingResult(TradeTicketEntity tradeTicketEntity, String procInstStatus) {
        BizApproveStatusEnum bizApproveStatusEnum = BizApproveStatusEnum.getByDesc(procInstStatus);
        log.info("=======>tradeTicketEntityCode:{}handleApprovingResult.procInstStatus;{}", tradeTicketEntity.getCode(), procInstStatus);
        switch (bizApproveStatusEnum) {
            case A_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_A_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            case B_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_B_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            case C_Approving:
            case CEO_Approving:
            case CFO_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_C_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            default:
                break;
        }
    }

    @Override
    public SubmitTTVO submitTTBatch(SubmitTTBatchDTO submitTTBatchDTO) {
        return submitBatch(submitTTBatchDTO);
    }

    public Result approve(ApprovalDTO approvalDTO, String processKey, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        ApproveDTO<Object> approveDTO = new ApproveDTO<>();
        //发起审批
        if (Objects.equals(TTReviewStatusEnum.PASS.getValue(), approvalDTO.getApproveStatus())) {
            approveDTO.setActionValue(ApproveActionEnum.AGREE.getValue());
        } else {
            approveDTO.setActionValue(ApproveActionEnum.REJECT.getValue());
        }

        approveDTO
//                .setCategoryEnum(GoodsCategoryEnum.getByValue(goodsCategoryId))
//                .setSubCategoryEnum(GoodsCategoryEnum.getByValue(subGoodsCategoryId))
                .setSalesTypeEnum(salesType)
                .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtTypeEnum(TTTypeEnum.getByType(tradeTicketEntity.getType()))
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setMemo(approvalDTO.getMemo())
                .setUserId(userId)
                .setTaskId(approvalDTO.getTaskId())
                .setProcessKey(processKey)
                .setBizId(tradeTicketEntity.getId())
                .setBizCode(tradeTicketEntity.getCode())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode());
        if (tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType()) && tradeTicketEntity.getTradeType() == ContractTradeTypeEnum.NEW.getValue()) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER);
        }
        return ttApproveService.approve(approveDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result submit(SubmitTTDTO submitTTDTO) {
        String userId = JwtUtils.getCurrentUserId();
        OMContractAddTTDTO omContractAddTTDTO = submitTTDTO.getCreateTradeTicketDTO();
        omContractAddTTDTO.setUserId(userId);

        List<Integer> ttIdList = new ArrayList<>();

        for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {
            SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();
            BeanUtils.copyProperties(keyTradeInfoTTDTO, salesContractAddTTDTO);
            BeanUtils.copyProperties(omContractAddTTDTO, salesContractAddTTDTO);
            salesContractAddTTDTO.setDomainCode(keyTradeInfoTTDTO.getDomainCode());
            salesContractAddTTDTO.setContractNum(keyTradeInfoTTDTO.getContractNum());
            salesContractAddTTDTO.setSupplierId(StringUtils.isBlank(omContractAddTTDTO.getSupplierId()) ? null : Integer.parseInt(omContractAddTTDTO.getSupplierId()));
            salesContractAddTTDTO.setCustomerId(StringUtils.isBlank(omContractAddTTDTO.getCustomerId()) ? null : Integer.parseInt(omContractAddTTDTO.getCustomerId()));
            salesContractAddTTDTO.setGoodsCategoryId(StringUtils.isBlank(omContractAddTTDTO.getGoodsCategoryId()) ? null : Integer.parseInt(omContractAddTTDTO.getGoodsCategoryId()));
            salesContractAddTTDTO.setAddedDepositRate(StringUtils.isBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? 0 : Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()));
            // 校验付款代码是否禁用
            salesContractAddTTDTO.setPayConditionId(Integer.valueOf(keyTradeInfoTTDTO.getPayConditionId()));
            salesContractAddTTDTO.setPriceEndTime(keyTradeInfoTTDTO.getPriceEndTime());
            salesContractAddTTDTO.setPriceEndType(keyTradeInfoTTDTO.getPriceEndType());
            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, keyTradeInfoTTDTO.getPriceDetailDTO());
            createAddTT(submitTTDTO, ttIdList, salesContractAddTTDTO, priceDetailBO);
        }
        //提交审批
        SubmitTTBatchDTO submitTTBatchDTO = new SubmitTTBatchDTO();
        BeanUtils.copyProperties(submitTTDTO, submitTTBatchDTO);
        submitTTBatchDTO.setTtIdList(ttIdList);
        SubmitTTVO submitTTVO = submitBatch(submitTTBatchDTO);
        List<TTQueryVO> list = submitTTVO.getList();
        //CaseId-1002453: RR status in navigator，Author By NaNa
        if (submitTTVO.getFailedByResidualRiskList().size() > 0) {
            return Result.failure(submitTTVO.getFailedByResidualRiskErrorMsg(), list);
        }
        if (submitTTVO.getFailedByCompleteList().size() > 0) {
            return Result.failure(ResultCodeEnum.NOT_COMPLETED, list);
        }
        if (submitTTVO.getFailedByPayConditionList().size() > 0) {
            return Result.failure(ResultCodeEnum.PAYCONDITION_NOT_COMPLETED, list);
        }
        if (submitTTVO.getFailedByGoodList().size() > 0) {
            return Result.failure(ResultCodeEnum.GOODS_NOT_COMPLETED, list);
        }
        if (submitTTVO.getFailedByQualityList().size() > 0) {
            return Result.failure(ResultCodeEnum.QUALITY_NOT_COMPLETED, list);
        }
        if (submitTTVO.getFailedByCustomerList().size() > 0) {
            return Result.failure(ResultCodeEnum.RISK_RESIDUAL_NOT_GET, list);
        }
        if (submitTTVO.getFailedByApproveList().size() > 0) {
            return Result.failure(ResultCodeEnum.SUBMIT_FAIL, list);
        }
        if (submitTTVO.getFailedByCreditPaymentList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_CREDIT_PAYMENT, list);
        }
        if (submitTTVO.getFailedByDeliveryList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_DELIVERY, list);
        }
        if (submitTTVO.getFailedByPackageList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_PACKAGE_WEIGHT, list);
        }
        if (submitTTVO.getFailedByWeightList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_WEIGHT, list);
        }
        if (submitTTVO.getFailedByDepositRateList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_DEPOSIT_RATE, list);
        }
        if (submitTTVO.getFailedByLOAList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_PRICE_LOA, list);
        }
        if (submitTTVO.getFailedByInvoiceList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_INVOICE, list);
        }
        if (submitTTVO.getFailedByWarehouseList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_WAREHOUSE, list);
        }
        if (submitTTVO.getFailedByCustomerAccountList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_ACCOUNT, list);
        }
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 Start
        if (submitTTVO.getFailedByDepositReleaseTypeList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_DEPOSIT_RELEASE_TYPE, list);
        }
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 End

        if (submitTTVO.getFailedByDomainCodeList().size() > 0) {
            return Result.failure(ResultCodeEnum.TT_DOMAIN_CODE, list);
        }

        return Result.success(list);
    }

    /**
     * 校验付款代码条件
     *
     * @param payConditionId
     * @param buCode
     */
    private void checkPaymentCode(Integer payConditionId, String buCode) {
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(payConditionId);
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
            if (!payConditionEntity.getBuCode().equals(buCode)) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_EXIST);
            }
        } else {
            throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_EXIST);
        }
    }

    private void createAddTT(SubmitTTDTO submitTTDTO, List<Integer> ttIdList, SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO) {
        log.info("check_code_question2  createAddTT   ");
        TTDTO ttdto = new TTDTO();
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        ttdto.setProcessorType(submitTTDTO.getTtProcessor());
        ttdto.setPriceDetailBO(priceDetailBO);
        //回购逻辑
        //生成唯一id绑定两个TT
        String groupId = null;
        if (ttdto.getProcessorType().contains("BUYBACK")) {
            groupId = UUID.randomUUID().toString().replaceAll("-", "");
        }
        // TODO 在保存的时候已经生成，走新的方法；这个暂时不需要
        // ttdto.setGroupId(groupId);

        TradeTicketEntity tradeTicketEntity = save(ttdto);
        ttIdList.add(tradeTicketEntity.getId());
        //回购生成原合同不可见关联TT TODO 在保存的时候已经生成，走新的方法；这个暂时不需要
//        if (ttdto.getProcessorType().contains("BUYBACK")) {
//            salesContractAddTTDTO.setAddedSignatureType(-1);
//            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
//            save(ttdto);
//        }

    }

    public SubmitTTVO submitBatch(SubmitTTBatchDTO submitTTBatchDTO) {
        String userId = JwtUtils.getCurrentUserId();
        submitTTBatchDTO.setUserId(userId);
        List<Integer> ttIdList = submitTTBatchDTO.getTtIdList();

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Generate group ID for batch TT submission
        Integer batchGroupId = null;
        if (ttIdList.size() >= 1) {
            batchGroupId = groupIdGenerationService.generateGroupId();
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
        SubmitTTVO submitTTVO = new SubmitTTVO();
        List<Integer> successList = new ArrayList<>();
        List<Integer> failedList = new ArrayList<>();
        List<Integer> failedByCompleteList = new ArrayList<>();
        List<Integer> failedByApproveList = new ArrayList<>();
        List<Integer> failedByGoodList = new ArrayList<>();
        List<Integer> failedByQualityList = new ArrayList<>();
        List<Integer> failedByPayConditionList = new ArrayList<>();
        List<Integer> failedByCustomerList = new ArrayList<>();
        List<Integer> failedByCustomerAccountList = new ArrayList<>();
        List<Integer> failedByCreditPaymentList = new ArrayList<>();
        List<Integer> failedByPackageList = new ArrayList<>();
        List<Integer> failedByDeliveryList = new ArrayList<>();
        List<Integer> failedByWeightList = new ArrayList<>();
        List<Integer> failedByDepositRateList = new ArrayList<>();
        List<Integer> failedByLOAList = new ArrayList<>();
        List<Integer> failedByInvoiceList = new ArrayList<>();
        List<Integer> failedByWarehouseList = new ArrayList<>();
        //CaseId-1002453: RR status in navigator，Author By NaNa
        List<Integer> failedByResidualRiskList = new ArrayList<>();
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18
        List<Integer> failedByDepositReleaseTypeList = new ArrayList<>();
        List<Integer> failedByDomainCodeList = new ArrayList<>();
        ResultCodeEnum failedByResidualRiskErrorMsg = null;
        List<TTQueryVO> list = new ArrayList<>();
        for (Integer ttId : ttIdList) {
            //信息不完整则失败
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
            TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
            if (0 == ttAddEntity.getCompletedStatus()) {
                failedList.add(ttId);
                failedByCompleteList.add(ttId);
                log.info("信息不完整,提交失败,ttId:{}", ttId);
                continue;
            }
            //1. 校验客户是否有效
            Integer customerId = tradeTicketEntity.getCustomerId();
            if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                customerId = tradeTicketEntity.getSupplierId();
            }
            CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
            if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
                failedList.add(ttId);
                failedByCustomerList.add(ttId);
                log.info("客户无效,提交失败,ttId:{}", ttId);
                continue;
            }

            // 采购需要校验账户的有效性
            if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(tradeTicketEntity.getBankId());
                if (customerBankEntity != null && DisableStatusEnum.DISABLE.getValue().equals(customerBankEntity.getStatus())) {
                    failedList.add(ttId);
                    failedByCustomerAccountList.add(ttId);
                    log.info("验账户的有效性,提交失败,ttId:{}", ttId);
                    continue;
                }
            }

            //2. 《RR 处理逻辑》 TODO写在下面
            if (null != residualRiskSwitcher && residualRiskSwitcher && TTTypeEnum.NEW.getType().equals(tradeTicketEntity.getType())) {
                if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                    CrisGlobalEntity crisGlobalEntity = customerFacade.getCustomerResidualRiskInfo(tradeTicketEntity.getCustomerId());
                    if (null == crisGlobalEntity) {
                        log.info("无法获取该客户RR limit数据，TT无法提交，请检查客户主数据,提交失败,ttId:{},customerId:{}", ttId, tradeTicketEntity.getCustomerId());
                        failedByResidualRiskErrorMsg = ResultCodeEnum.RISK_RESIDUAL_NOT_EXIST;
                        failedList.add(ttId);
                        failedByResidualRiskList.add(ttId);
                        continue;
                    }
                    Boolean residualRiskForceSubmit = null != submitTTBatchDTO.getResidualRiskForceSubmit() && submitTTBatchDTO.getResidualRiskForceSubmit();
                    //剩余风险使用
                    BigDecimal riskResidue = crisGlobalEntity.getResidualRiskResidue();
                    if (!TTCustomerTradeStatusEnum.getCommonTradeStatusList().contains(crisGlobalEntity.getTradeStatus())) {
                        failedByResidualRiskErrorMsg = ResultCodeEnum.RISK_RESIDUAL_NOT_GET;
                        failedList.add(ttId);
                        failedByResidualRiskList.add(ttId);
                        continue;
                    } else if (TTCustomerTradeStatusEnum.NO_TRADE.getValue().equals(crisGlobalEntity.getTradeStatus())) {
                        failedByResidualRiskErrorMsg = ResultCodeEnum.RISK_RESIDUAL_CUSTOMER_NO_TRADE;
                        failedList.add(ttId);
                        failedByResidualRiskList.add(ttId);

                    } else if (TTCustomerTradeStatusEnum.getJudgeRrNumTradeStatusList().contains(crisGlobalEntity.getTradeStatus()) && riskResidue.compareTo(BigDecimal.ZERO) < 0) {
                        // 其中RR Residue = RR Limit - RR Usage
                        // Residue ≤ 0且RR Usage > 150kUSD时，若超额则报错提示：“该客户RR limit已超过平台最大限额，请联系风控部门提额后提交”
                        BigDecimal riskUsage = crisGlobalEntity.getResidualRiskUsage();
//                    Integer riskResidue = crisGlobalEntity.getLimitResidualRisk() - crisGlobalEntity.getGlobalPeakResidualTotal();
                        if (riskUsage.compareTo(new BigDecimal(150)) > 0) {
                            failedByResidualRiskErrorMsg = ResultCodeEnum.RISK_RESIDUAL_USAGE_OVER;
                            failedList.add(ttId);
                            failedByResidualRiskList.add(ttId);
                            continue;
                        }
                        if (riskUsage.compareTo(new BigDecimal(150)) <= 0) {
                            //不强制提交
                            log.info("该客户RR limit已超限，请确认新合同是否可以继续提交,请在CRIS系统申请提额后提交！ttId:{},customerId:{}", ttId, tradeTicketEntity.getCustomerId());
                            failedByResidualRiskErrorMsg = ResultCodeEnum.RISK_RESIDUAL_USAGE_OFF;
                            failedList.add(ttId);
                            failedByResidualRiskList.add(ttId);
                            continue;
                        }
                    }
                }

            }

            //3. 校验SKU是否有效
            SkuEntity skuEntity = skuFacade.getSkuById(ttAddEntity.getGoodsId());
            if (skuEntity == null || DisableStatusEnum.DISABLE.getValue().equals(skuEntity.getStatus())) {
                failedList.add(ttId);
                failedByGoodList.add(ttId);
                log.info("SKU配置有误,提交失败,ttId:{}", ttId);
                continue;
            }
            //4. 校验该SKU的品种是否绑定选定帐套
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(tradeTicketEntity.getSiteCode());
            if (siteEntity == null || !siteEntity.getCategory3().contains(tradeTicketEntity.getCategory3().toString())) {
                failedList.add(ttId);
                failedByGoodList.add(ttId);
                log.info("SKU的品种绑定选定帐套有误,提交失败,ttId:{}", ttId);
                continue;
            }
            //5. 校验客户配置-赊销&预付是否有效
            CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
            creditPaymentDTO.setCustomerId(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                            ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCompanyId(tradeTicketEntity.getCompanyId())
                    .setCategory1(String.valueOf(tradeTicketEntity.getCategory1()))
                    .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                    .setCategory3(String.valueOf(tradeTicketEntity.getCategory3()))
                    .setPaymentType(ttAddEntity.getPaymentType())
                    .setBuCode(tradeTicketEntity.getBuCode())
                    .setIsSales(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                    .setIsProcurement(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
            Result result = customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
            if (result.isSuccess()) {
                List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
                if (CollectionUtil.isEmpty(creditPaymentVOList)) {
                    failedList.add(ttId);
                    failedByCreditPaymentList.add(ttId);
                    log.info("校验客户配置-赊销&预付,提交失败,ttId:{}", ttId);
                    continue;
                } else {
                    CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                    // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                    if (ttAddEntity.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                        failedList.add(ttId);
                        failedByCreditPaymentList.add(ttId);
                        log.info("最多可赊销{" + customerCreditPaymentVO.getCreditDays() + "}天,ttId:{}", ttId);
                        continue;
                    }
                }
            }
            //6. 校验业务配置-袋皮扣重配置是否有效
            if (StringUtils.isNotBlank(ttAddEntity.getPackageWeight())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttAddEntity.getPackageWeight()));
                if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                    failedList.add(ttId);
                    failedByPackageList.add(ttId);
                    log.info("袋皮扣重配置是否有效,提交失败,ttId:{}", ttId);
                    continue;
                }
            }

            // 7. 保证金比例的校验
            CustomerDepositRateDTO customerDepositRateDTO = new CustomerDepositRateDTO();
            customerDepositRateDTO.setCustomerId(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                            ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId())
                    .setDepositRate(ttAddEntity.getDepositRate())
                    .setInvoicePaymentRate(ttAddEntity.getInvoicePaymentRate())
                    .setCategory1(String.valueOf(tradeTicketEntity.getCategory1()))
                    .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                    .setBuCode(tradeTicketEntity.getBuCode())
                    .setStatus(DisableStatusEnum.ENABLE.getValue());
            if (tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                customerDepositRateDTO.setIsSales(GeneralEnum.YES.getValue());
            } else {
                customerDepositRateDTO.setIsProcurement(GeneralEnum.YES.getValue());
            }
            if (ContractTypeEnum.YI_KOU_JIA.getValue() != tradeTicketEntity.getContractType()) {
                customerDepositRateDTO.setPricingDepositRate(ttAddEntity.getAddedDepositRate());
            }
            List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateFacade.queryCustomerDepositRate(customerDepositRateDTO);
            if (ObjectUtil.isEmpty(customerDepositRateEntities) || customerDepositRateEntities.size() < 0) {
                failedList.add(ttId);
                failedByDepositRateList.add(ttId);
                log.info("客户履约保证金配置无效,提交失败,ttId:{}", ttId);
                continue;
            }

            //8. 校验通用配置-付款条件代码，业务类型=现货且是否有效
            if (tradeTicketEntity.getPayConditionId() != null) {
                try {
                    checkPaymentCode(tradeTicketEntity.getPayConditionId(), tradeTicketEntity.getBuCode());
                } catch (Exception e) {
                    log.error("=======submitTT error,ttid:{},exception:{}", ttId, JSON.toJSONString(e));
                    failedList.add(ttId);
                    failedByPayConditionList.add(ttId);
                    continue;
                }
            }

            // 9 . 二级品类=豆粕/豆油且基差基准价或蛋白价差超阈值超出了 备注的校验 TODO 后处理
            if (ObjectUtil.isEmpty(ttAddEntity.getMemo()) && (GoodsCategoryEnum.OSM_OIL.getValue().equals(tradeTicketEntity.getCategory2())
                    || GoodsCategoryEnum.OSM_MEAL.getValue().equals(tradeTicketEntity.getCategory2()))) {
                BigDecimal loaProtein = LOAProteinDiffPrice(tradeTicketEntity.getId(), tradeTicketEntity.getType());
                if (LOAPriceWarning(tradeTicketEntity.getId()) || loaProtein.compareTo(BigDecimal.ZERO) != 0) {
                    failedList.add(ttId);
                    failedByLOAList.add(ttId);
                    log.info("信息不完整,提交失败,ttId:{}", ttId);
                    continue;
                }
            }

            //10. 校验通用配置-交提货方式配置是否有效
            if (ObjectUtil.isNotEmpty(ttAddEntity.getDeliveryType())) {
                DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttAddEntity.getDeliveryType());
                if (ObjectUtil.isEmpty(deliveryTypeEntity) || DisableStatusEnum.DISABLE.getValue().equals(deliveryTypeEntity.getStatus())) {
                    log.error("=======submitTT error,ttid:{},exception:{}", ttId, JSON.toJSONString(ttAddEntity));
                    failedList.add(ttId);
                    failedByDeliveryList.add(ttId);
                    continue;
                }
            }
            //11. 校验业务配置-重量验收是否有效
            if (StringUtils.isNotBlank(ttAddEntity.getWeightCheck())) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttAddEntity.getWeightCheck()));
                if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                    failedList.add(ttId);
                    failedByWeightList.add(ttId);
                    log.info("重量验收是否有效,提交失败,ttId:{}", ttId);
                    continue;
                }
            }
            //12.TODO 校验通用配置-库点，库点属性 | 校验该库点是否绑定选定帐套 前端绑定了
            if (ObjectUtil.isNotEmpty(ttAddEntity.getShipWarehouseId())) {
                Result<WarehouseEntity> warehouseEntityResult = warehouseFacade.getWarehouseById(ttAddEntity.getShipWarehouseId());
                if (warehouseEntityResult.isSuccess()) {
                    WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(warehouseEntityResult.getData()), WarehouseEntity.class);
                    if (null == warehouseEntity
                            || DisableStatusEnum.DISABLE.getValue().equals(warehouseEntity.getStatus())
                            || ObjectUtil.isEmpty(warehouseEntity.getSiteCodes())
                            || (ObjectUtil.isNotEmpty(warehouseEntity.getSiteCodes()) && !warehouseEntity.getSiteCodes().contains(tradeTicketEntity.getSiteCode()))) {
                        failedList.add(ttId);
                        failedByWarehouseList.add(ttId);
                        log.info("库点是否有效,提交失败,ttId:{}", ttId);
                        continue;
                    }
                }
            }

            // 13. 校验质量指标是否有效
            // 豆粕|副产品  基于帐套的工厂+货品+发货库点+用途+采销 匹配 业务配置-质量指标条款是否有效
            // 特种油脂 基于帐套的工厂+货品+企标+发货库点+用途+采销 匹配 业务配置-质量指标条款是否有效
            QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
            qualityInfoDTO
                    .setGoodsCategoryId(ttAddEntity.getGoodsCategoryId())
                    .setFactoryCode(ttAddEntity.getDeliveryFactoryCode())
                    .setWarehouseId(ttAddEntity.getShipWarehouseId())
                    .setUsage(tradeTicketEntity.getUsage())
                    .setGoodsId(ttAddEntity.getGoodsId())
                    .setSalesType(tradeTicketEntity.getSalesType())
                    .setCustomerId(customerId);
            // 特种油脂
            if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(tradeTicketEntity.getCategory2())) {
                qualityInfoDTO.setStandardType(ttAddEntity.getStandardType());
            }
            Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
            if (!existQuality) {
                failedList.add(ttId);
                failedByQualityList.add(ttId);
                log.info("校验质量指标,提交失败,ttId:{}", ttId);
                continue;
            }


            // 14.验证发票信息
            CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
            customerInvoiceDTO
                    .setCompanyId(tradeTicketEntity.getCompanyId())
                    .setCustomerId(customerId)
                    .setCategory1(String.valueOf(tradeTicketEntity.getCategory1()))
                    .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                    .setCategory3(String.valueOf(tradeTicketEntity.getCategory3()))
            ;
            List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
            // BUGFIX：Case-1003197 TJIBSBOS2503315， TJIBSBOS2503316合同传输错误 Author: Mr 2025-05-09 Start
            if (customerInvoiceEntities.isEmpty() || ObjectUtil.equal(ttAddEntity.getInvoiceType(), 0) || ttAddEntity.getInvoiceType() == null) {
                // BUGFIX：Case-1003197 TJIBSBOS2503315， TJIBSBOS2503316合同传输错误 Author: Mr 2025-05-09 End
                failedList.add(ttId);
                failedByInvoiceList.add(ttId);
                log.info("客户发票配置无效,提交失败,ttId:{}", ttId);
                continue;
            }

            // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 Start
            // 15.校验履约保证金释放方式是否正确
            if (ttAddEntity.getDepositUseRule() == null || ttAddEntity.getDepositUseRule() == 0) {
                failedList.add(ttId);
                failedByDepositReleaseTypeList.add(ttId);
                log.info("履约保证金释放方式无效,提交失败,ttId:{}", ttId);
                continue;
            }
            // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 End

            // 16.校验期货合约和期货月份是否有效
            // 判断是否是特种油脂或特种蛋白，并且合同类型为基差或基差暂定价合同
            boolean isSpecialCategory = GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(tradeTicketEntity.getCategory2())
                    || GoodsCategoryEnum.SPECIFIC_PROTEIN.getValue().equals(tradeTicketEntity.getCategory2());

            boolean isBasisContract = tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()
                    || tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue();

            if (isSpecialCategory && isBasisContract) {
                // 判断期货合约和期货月份是否为空
                if (StringUtils.isBlank(ttAddEntity.getFutureCode()) || StringUtils.isBlank(ttAddEntity.getDomainCode())) {
                    failedList.add(ttId);
                    failedByDomainCodeList.add(ttId);

                    // 记录失败日志
                    log.info("提交失败：期货合约或期货月份无效，ttId: {}", ttId);
                    continue;
                }
            }

            //Case-1002453: RR status in navigator-只校验新增（修改保存TT提交不判断），Author:NaNa,Date:20240522 end
            //CaseId-1002453: RR status in navigator，Author By NaNa
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
            String code = tradeTicketEntity.getCode();
            //发起审批

            TTDTO ttdto = new TTDTO();
            tradeTicketConvertUtil.prepareData(tradeTicketEntity, ttAddEntity, contractPriceEntity, ttdto);
            RecordBizOperationDTO recordBizOperationDTO;
            try {
                log.info("check_code_question2 ttId:{} submitBatch2   ", ttId);
                recordBizOperationDTO = startTTApprove(ttId, ttdto, contractPriceEntity);
            } catch (Exception e) {
                log.error("=======submitTT error,ttid:{},exception:{}", ttId, JSON.toJSONString(e));
                failedList.add(ttId);
                failedByApproveList.add(ttId);
                continue;
            }
            log.warn("----------startTTApprove success! contractCode:{},ttid:{}", tradeTicketEntity.getContractCode(), ttId);
            //创建协议
            ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
            BeanUtils.copyProperties(ttAddEntity, contractSignCreateDTO);
            contractSignCreateDTO.setTtCode(code).setTtType(tradeTicketEntity.getType())
                    .setSalesType(tradeTicketEntity.getSalesType())
                    .setSupplierId(String.valueOf(ttAddEntity.getSupplierId()))
                    .setOwnerId(tradeTicketEntity.getOwnerId());
            if ("2".equalsIgnoreCase(submitTTBatchDTO.getSubmitStatus())) {
                contractSignCreateDTO.setContractSignId(tradeTicketEntity.getSignId());
                contractSignCreateDTO.setTtId(tradeTicketEntity.getId());
            }
            log.info("check_code_question  submitBatch  doInTransaction ttId:{} ", ttId);
            ContractSignEntity contractSign = createContractSign(contractSignCreateDTO, ttId);
            log.warn("----------createContractSign success! contractCode:{},ttid:{},signId:{}", tradeTicketEntity.getContractCode(), ttId, contractSign.getId());
            //创建合同
            // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
            ContractEntity contractEntity = createContract(tradeTicketEntity, ttAddEntity, contractPriceEntity, submitTTBatchDTO.getSubmitStatus(), batchGroupId);
            // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
            if (null == tradeTicketEntity.getContractId() || tradeTicketEntity.getContractId() == 0) {
                tradeTicketEntity.setContractId(contractEntity.getId());
                recordBizOperationDTO.getApproveDTO().setReferBizId(contractEntity.getId());
            }
            log.warn("----------createContract success! contractCode:{},ttid:{},signId:{},contractId:{}", tradeTicketEntity.getContractCode(), ttId, contractSign.getId(), contractEntity.getId());
            updateContractSign(contractSign, contractEntity, tradeTicketEntity);

            successList.add(ttId);

            TTQueryVO ttQueryVO = new TTQueryVO();
            ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                    .setSignId(contractSign.getId())
                    .setCode(tradeTicketEntity.getCode())
                    .setTtId(tradeTicketEntity.getId());
            list.add(ttQueryVO);
            //记录操作日志
            if (tradeTicketEntity.getType().equals(TTTypeEnum.BUYBACK.getType())) {
                logBizCodeEnum = LogBizCodeEnum.SUBMIT_BUYBACK_TT;
            }
            recordTTOperationDetail(OperationActionEnum.SUBMIT, tradeTicketEntity);

            approveFacade.recordBizOperation(recordBizOperationDTO);
        }

        submitTTVO.setSuccessList(successList);
        submitTTVO.setFailedList(failedList);
        submitTTVO.setFailedByApproveList(failedByApproveList);
        submitTTVO.setFailedByCompleteList(failedByCompleteList);
        submitTTVO.setFailedByGoodList(failedByGoodList);
        submitTTVO.setFailedByCustomerList(failedByCustomerList);
        submitTTVO.setFailedByPayConditionList(failedByPayConditionList);
        submitTTVO.setFailedByQualityList(failedByQualityList);
        submitTTVO.setFailedByCreditPaymentList(failedByCreditPaymentList);
        submitTTVO.setFailedByPackageList(failedByPackageList);
        submitTTVO.setFailedByDeliveryList(failedByDeliveryList);
        submitTTVO.setFailedByWeightList(failedByWeightList);
        submitTTVO.setFailedByDepositRateList(failedByDepositRateList);
        submitTTVO.setFailedByLOAList(failedByLOAList);
        submitTTVO.setSuccessListSize(successList.size());
        submitTTVO.setFailedListSize(failedList.size());
        submitTTVO.setFailedByInvoiceList(failedByInvoiceList);
        submitTTVO.setFailedByWarehouseList(failedByWarehouseList);
        submitTTVO.setFailedByCustomerAccountList(failedByCustomerAccountList);
        submitTTVO.setFailedByResidualRiskList(failedByResidualRiskList)
                .setFailedByResidualRiskErrorMsg(failedByResidualRiskErrorMsg);
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18
        submitTTVO.setFailedByDepositReleaseTypeList(failedByDepositReleaseTypeList);
        submitTTVO.setFailedByDomainCodeList(failedByDomainCodeList);
        submitTTVO.setList(list);
        return submitTTVO;
    }

    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
    private ContractEntity createContract(TradeTicketEntity tradeTicketEntity, TTAddEntity ttAddEntity, ContractPriceEntity contractPriceEntity, String submitStatus, Integer groupId) {
    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
        ContractCreateDTO contractCreateDTO = new ContractCreateDTO();
        BeanUtils.copyProperties(tradeTicketEntity, contractCreateDTO);
        BeanUtils.copyProperties(ttAddEntity, contractCreateDTO);
        BeanUtils.copyProperties(contractPriceEntity, contractCreateDTO);
        contractCreateDTO.setStatus(null);
        // 计量单位
        contractCreateDTO.setWeightUnit(ttAddEntity.getUnit());
        // 合同类型
        contractCreateDTO.setContractType(tradeTicketEntity.getContractType());
        // 当前ttId
        contractCreateDTO.setCurrentTtId(tradeTicketEntity.getId());
        // 合同总数量
        contractCreateDTO.setOrderNum(ttAddEntity.getContractNum());
        // 卖方主体收款账号信息
        contractCreateDTO.setSupplierAccountId(tradeTicketEntity.getBankId());
        // 保证金释放方式
        contractCreateDTO.setDepositReleaseType(ttAddEntity.getDepositUseRule());
        // is_soybean2 默认为0 是否为豆二注销生成（0否;1是）
        contractCreateDTO.setIsSoybean2(0);

        contractCreateDTO.setTradeType(contractTradeTypeEnum.getValue());
        contractCreateDTO.setActionSource(contractSource);
        contractCreateDTO.setPriceEndTime(ttAddEntity.getPriceEndTime());

        if (ContractActionEnum.BUYBACK.getActionValue() == contractSource) {
            contractCreateDTO.setParentId(tradeTicketEntity.getSourceContractId());
        }
        contractCreateDTO.setOwnerId(tradeTicketEntity.getOwnerId());

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Set group_id for batch contract creation
        if (groupId != null) {
            contractCreateDTO.setGroupId(groupId);
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

        IContractService contractService = salesContractHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getTradeType(), tradeTicketEntity.getSubGoodsCategoryId());
        return contractService.createContract(contractCreateDTO);
    }

    public void cancel(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        //取消工作流审批
        cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
        //作废合同 ,TT ,协议
        handleCancelOrRejectResult(tradeTicketEntity, operateTTDTO.getMemo());
    }

    public void cancelContractSign(TradeTicketEntity tradeTicketEntity, String memo) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(tradeTicketEntity.getId());
        contractSignService.abnormalContractSign(contractSignEntity.getId(), memo);
    }

    public void invalid(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        //tt审批作废
        Integer status = tradeTicketEntity.getStatus();
        Integer approvalStatus = tradeTicketEntity.getApprovalStatus();
        if (TTStatusEnum.DONE.getType() == status || TTApproveStatusEnum.REJECT.getValue() == approvalStatus) {
            cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
        }
        String memo = operateTTDTO.getMemo();

        log.info("check_code_question  invalidTTById ");
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), memo, operateTTDTO.getTtId());

        //作废合同
        invalidContract(tradeTicketEntity, memo);

        //作废协议
        invalidContractSign(operateTTDTO.getTtId(), tradeTicketEntity, memo, tradeTicketEntity.getSignId());

        //结构化定价释放合约量
        releasePriceNum(tradeTicketEntity);
    }

    public void releasePriceNum(TradeTicketEntity tradeTicketEntity) {
    }


    protected void invalidContractSign(Integer ttId, TradeTicketEntity tradeTicketEntity, String memo, Integer signId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
        contractSignReviewDTO.setTtId(ttId)
                .setReviewRemark(memo)
                .setContractSignId(signId)
        ;
        contractSignService.invalidContractSign(contractSignReviewDTO);
    }

    public void cancelActiviti(String memo, String userId, TradeTicketEntity tradeTicketEntity) {
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setTaskId("");
        String code = tradeTicketEntity.getCode();
        approveDTO
                .setUserId(userId)
                .setBizCode(code)
                .setMemo(memo)
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSalesTypeEnum(salesType)
                .setContractTradeTypeEnum(contractTradeTypeEnum)
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtTypeEnum(ttTypeEnum)
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setActionName("TT撤回")
        ;
        log.info("========== cancelActiviti.approveDTO:{}", JSON.toJSONString(approveDTO));
        ttApproveService.cancel(approveDTO);
    }

    private void invalidContract(TradeTicketEntity tradeTicketEntity, String memo) {
        ContractBaseDTO contractBaseDTO = new ContractBaseDTO();
        contractBaseDTO.setContractId(tradeTicketEntity.getContractId());
        contractBaseDTO.setInvalidReason(memo);
        salesContractOperationService.invalidContractByTT(contractBaseDTO);
    }

    /**
     * 保存TT主表
     *
     * @param ttDto
     * @return
     */
    private TradeTicketEntity saveTradeTicket(TTDTO ttDto) {
        //保存tt基本信息
        TradeTicketEntity tradeTicketEntity = convertToTradeTicket(ttDto);
        if (ObjectUtil.isNotEmpty(ttDto.getGroupId())) {
            tradeTicketEntity.setGroupId(ttDto.getGroupId());
        }
        tradeTicketDao.saveOrUpdate(tradeTicketEntity);
        return tradeTicketEntity;
    }

    /**
     * 记录日志
     *
     * @param paramDTO
     * @param bizCodeEnum
     * @param ttId
     * @param logLevel
     */
    @Async
    public void recordTTQuery(String paramDTO, LogBizCodeEnum bizCodeEnum, Object ttId, Integer logLevel) {
        String userId = JwtUtils.getCurrentUserId();
        String code = null;
        if (ttId != null) {
            code = tradeTicketDao.getTradeTicketEntityById((Integer) ttId).getCode();
        }
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getBizCode())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(logLevel)
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(userId))
                .setOperationName(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getMsg())
                .setMetaData(paramDTO)
                .setData(paramDTO)
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId((Integer) ttId)
                .setReferBizCode(code);

        operationLogFacade.recordOperationLogOLD(operationDetailDTO);

    }

    @Async
    public void recordTTOperationDetail(OperationActionEnum operationActionEnum, TradeTicketEntity tt) {
        OperationDetailDTO operationDetailDTO = buildTTOperationDetail(operationActionEnum, tt);
        operationLogFacade.recordOperationLogDetail(operationDetailDTO);
    }

    @Async
    public OperationDetailDTO buildTTOperationDetail(OperationActionEnum operationActionEnum, TradeTicketEntity tt) {

        OperationSourceEnum logLevelEnum = OperationSourceEnum.getByName(operationActionEnum.getLogLevel());

        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tt.getTradeType());

        return new OperationDetailDTO()
                .setBizCode(operationActionEnum.getCode())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(logLevelEnum.getValue())
                .setSource(logLevelEnum.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorId(tt.getCreatedBy())
                .setOperationName(operationActionEnum.getAction())
                .setMetaData(null)
                .setData(JSON.toJSONString(tt))
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId(tt.getId())
                .setReferBizCode(tt.getCode())
                .setTargetRecordId(tt.getContractId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name())
                .setTtCode(tt.getCode())
                .setTradeTypeName(contractTradeTypeEnum.getDesc());
    }

    public void injectionProperty(TradeTicketEntity tradeTicketEntity) {
        tradeTicketEntity
                .setSalesType(salesType.getValue())
                .setContractStatus(contractStatus)
                .setOperationSource(operationSource)
                .setType(ttTypeEnum.getType())
                .setContractSource(contractSource)
                .setContractSignatureStatus(contractSignatureStatus)
                .setStatus(status)
                .setGoodsCategoryId(tradeTicketEntity.getCategory1())
                .setSubGoodsCategoryId(tradeTicketEntity.getCategory2());
        // add by zengshl 如果是待修改提交的状态需要保留
        if (ObjectUtil.isNotEmpty(tradeTicketEntity.getId())) {
            TradeTicketEntity oldTradeTicket = tradeTicketDao.getTradeTicketEntityById(tradeTicketEntity.getId());
            tradeTicketEntity.setStatus(oldTradeTicket.getStatus());
        } else {
            tradeTicketEntity.setStatus(status);
        }
    }

    protected void addApprovalHandler(Result result, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();

        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());
        }
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            //更新审批状态
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.WAITING.getType(), TTApproveStatusEnum.REJECT.getValue(), tradeTicketEntity.getCode());
            //取消协议
            cancelContractSign(tradeTicketEntity, memo);
        }
        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }

    public TTDTO initPurchaseDTOCommon(TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        //初始化交易、销售类型、合同来源
        salesContractAddTTDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesContractAddTTDTO.setSalesType(salesType.getValue());
        salesContractAddTTDTO.setContractSource(contractSource);
        salesContractAddTTDTO.setStatus(status);
        salesContractAddTTDTO.setType(ttTypeEnum.getType());

        //协议签署状态
        salesContractAddTTDTO.setContractSignatureStatus(contractSignatureStatus);

        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);

        //生成TT编号
        String code = getPurchaseTTCode(salesContractAddTTDTO.getSourceContractId());
        salesContractAddTTDTO.setCode(code);
        salesContractAddTTDTO.setCreateStatus(true);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        return ttdto;
    }

    public TTDTO initSalesDTOCommon(TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        //初始化交易、销售类型、合同来源
        salesContractAddTTDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesContractAddTTDTO.setSalesType(salesType.getValue());
        salesContractAddTTDTO.setContractSource(contractSource);
        salesContractAddTTDTO.setStatus(status);
        salesContractAddTTDTO.setType(ttTypeEnum.getType());

        //协议签署状态
        salesContractAddTTDTO.setContractSignatureStatus(contractSignatureStatus);

        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);

        //生成TT编号
        String code = getSalesTTCode(salesContractAddTTDTO.getSourceContractId());

        salesContractAddTTDTO.setCode(code);
        salesContractAddTTDTO.setCreateStatus(true);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        return ttdto;
    }

    public void initMealParam() {
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
    }

    public void initOilParam() {
        goodsCategoryId = GoodsCategoryEnum.OSM_OIL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_OIL.getValue();
    }


    public boolean LOAPriceWarning(Integer ttId) {

        log.info("======================================================================LOAPriceWarning:{}", ttId);
        TradeTicketEntity tradeTicketEntity = tradeTicketQueryService.getByTtId(ttId);
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        if (null == ttAddEntity) {
            return false;
        }
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        Integer goodsSpecId = null;
        if (null != ttAddEntity.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(ttAddEntity.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    goodsSpecId = skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId();
                }
            }
        }

        log.info("======================================================================goodsSpecId:{}", goodsSpecId);

        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
        systemRuleDTO.setFactoryCode(ttAddEntity.getDeliveryFactoryCode());
        systemRuleDTO.setCompanyId(tradeTicketEntity.getCompanyId().toString());
        systemRuleDTO.setAttributeValueId(goodsSpecId);
        //systemRuleDTO.setGoodsId(ttAddEntity.getGoodsId());
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(ttAddEntity.getDeliveryStartTime()));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = systemRuleFacade.filterBasicPrice(systemRuleDTO);
        log.info("======================================================================systemRuleDTO:{}", JSONObject.toJSONString(systemRuleDTO));
        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return false;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于基差价
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //基差价
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());

            BigDecimal extraPrice = ruleValue.subtract(memo);
            log.info("=========================================================");
            log.info("不能低于基差价:" + extraPrice);
            log.info("基差价:" + contractPriceEntity.getExtraPrice());
            log.info("基差价是否低于基础价" + (contractPriceEntity.getExtraPrice().compareTo(ruleValue.subtract(memo)) < 0));
            log.info("=========================================================");

            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) < 0;
            } else {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) > 0;
            }
        }
        return false;
    }


    public BigDecimal LOAProteinDiffPrice(Integer ttId, Integer TTType) {

        log.info("======================================================================LOAProteinDiffPrice:{}", ttId);
        TradeTicketEntity tradeTicketEntity = tradeTicketQueryService.getByTtId(ttId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);

        String deliveryFactoryCode = null;
        Integer goodsSpecId = null;
        Integer goodsCategoryId = null;
        Integer goodsId = null;
        Date deliveryStartTime = null;

        if (TTType.equals(TTTypeEnum.NEW.getType()) || TTType.equals(TTTypeEnum.ASSIGN.getType())) {
            TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);

            if (null != ttAddEntity) {
                deliveryFactoryCode = ttAddEntity.getDeliveryFactoryCode();
                goodsId = ttAddEntity.getGoodsId();
                deliveryStartTime = ttAddEntity.getDeliveryStartTime();
            }

        } else if (TTType.equals(TTTypeEnum.REVISE.getType()) || TTType.equals(TTTypeEnum.SPLIT.getType())) {
            TTModifyEntity ttAddEntityByTTId = ttModifyDao.getTTModifyEntityByTTId(ttId);

            if (null != ttAddEntityByTTId) {
                deliveryFactoryCode = ttAddEntityByTTId.getDeliveryFactoryCode();
                goodsId = ttAddEntityByTTId.getGoodsId();
                deliveryStartTime = ttAddEntityByTTId.getDeliveryStartTime();
            }
        }
        log.info("======================================================================goodsId:{}", goodsId);

        if (null != goodsId) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(goodsId);
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    goodsSpecId = skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId();
                }
            }
        }

        log.info("======================================================================goodsSpecId:{}", goodsSpecId);


        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
        systemRuleDTO.setFactoryCode(deliveryFactoryCode);
        systemRuleDTO.setAttributeValueId(goodsSpecId);
        systemRuleDTO.setGoodsId(goodsId);
        systemRuleDTO.setCompanyId(String.valueOf(tradeTicketEntity.getCompanyId()));
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(deliveryStartTime));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = proteinPriceConfigFacade.filterBasicProtein(systemRuleDTO);

        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return BigDecimal.ZERO;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于蛋白价差范围预警值
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //蛋白价差
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());

            BigDecimal proteinPrice = ruleValue.subtract(memo);
            log.info("=========================================================");
            log.info("不能低于蛋白价差价:" + proteinPrice);
            log.info("蛋白价差:" + contractPriceEntity.getProteinDiffPrice());
            log.info("低于蛋白价差范围预警值" + (contractPriceEntity.getProteinDiffPrice().compareTo(ruleValue.subtract(memo)) < 0));
            log.info("=========================================================");

            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) < 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            } else {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) > 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            }


        }
        return BigDecimal.ZERO;
    }

    public String getSalesTTCode(Integer contractId) {
        String code;//生成子编号
        String newCode = CodeGeneratorUtil.genSalesTTNewCode();
        log.info("========== getSalesTTCode.newCode:{}", newCode);
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);
        if (tradeTicketEntityList.isEmpty()) {
            log.error("合同无对应的TT");
            return newCode;
        }
        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());
        if (tradeTicketEntities.isEmpty()) {
            log.error("合同基差转一口价外的TT");
            return newCode;
        }

        /*String redisKey = MAX_SBM_SALES_TT_CODE;
        if (subGoodsCategoryId.equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
            redisKey = MAX_SBO_SALES_TT_CODE;
        }
        //code = codeGeneratorUtil.genTTSonCode(redisKey, tradeTicketEntities.get(0).getCode());
        code = genTTSonCode(redisKey, tradeTicketEntities.get(0).getCode());
        return code;*/
        return sequenceUtil.generateSpotChildTTCode(tradeTicketEntities.get(0).getCode());
    }

    public String getPurchaseTTCode(Integer contractId) {
        String code;//生成子编号
        String newCode = CodeGeneratorUtil.genPurchaseTTNewCode();
        log.info("========== getPurchaseTTCode.newCode:{}", newCode);
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);
        if (tradeTicketEntityList.isEmpty()) {
            log.error("合同无对应的TT");
            return newCode;
        }

        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());

        if (tradeTicketEntities.isEmpty()) {
            log.error("合同基差转一口价外的TT");
            return newCode;
        }
        /*String redisKey = MAX_SBM_PURCHASE_TT_CODE;
        if (subGoodsCategoryId.equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
            redisKey = MAX_SBO_PURCHASE_TT_CODE;
        }
//        code = codecodeGeneratorUtil.genTTSonCode(redisKey, tradeTicketEntities.get(0).getCode());
        code = genTTSonCode(redisKey, tradeTicketEntities.get(0).getCode());
        return code;*/
        return sequenceUtil.generateSpotChildTTCode(tradeTicketEntities.get(0).getCode());
    }

    // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 Start

    /**
     * 销售保存提交生成子TT编号
     *
     * @param contractId 合同id
     * @return
     */
    public String getSaveSalesTTCode(Integer contractId) {
        String newCode = CodeGeneratorUtil.genSalesTTNewCode();
        log.info("========== getSaveSalesTTCode.newCode:{}", newCode);
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);
        if (tradeTicketEntityList.isEmpty()) {
            log.error("合同无对应的TT");
            return newCode;
        }
        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());
        if (tradeTicketEntities.isEmpty()) {
            log.error("合同基差转一口价外的TT");
            return newCode;
        }
        return tradeTicketEntities.get(0).getCode();
    }

    /**
     * 采购保存提交生成子TT编号
     *
     * @param contractId 合同id
     * @return
     */
    public String getSavePurchaseTTCode(Integer contractId) {
        String newCode = CodeGeneratorUtil.genPurchaseTTNewCode();
        log.info("========== getPurchaseTTCode.newCode:{}", newCode);
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);
        if (tradeTicketEntityList.isEmpty()) {
            log.error("合同无对应的TT");
            return newCode;
        }

        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());

        if (tradeTicketEntities.isEmpty()) {
            log.error("合同基差转一口价外的TT");
            return newCode;
        }
        return tradeTicketEntities.get(0).getCode();
    }
    // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 End

    public boolean changeFlag(String modifyContent, List<CompareObjectDTO> compareObjectDTOList) {
        //基差转一口价
        if (modifyContent.contains("contractType")) {
            List<CompareObjectDTO> list = compareObjectDTOList.stream().filter(i -> "contractType".equalsIgnoreCase(i.getName())
                    && i.getBefore().equalsIgnoreCase(String.valueOf(ContractTypeEnum.JI_CHA.getValue()))
                    && i.getAfter().equalsIgnoreCase(String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()))).collect(Collectors.toList());
            return CollectionUtil.isNotEmpty(list);
        }
        return false;
    }

    /**
     * 生成TT子编号
     *
     * @return
     */
    public String genTTSonCode(String preKey, String code) {
        String key = preKey + code;
        int index = code.indexOf("-");
        if (index == -1) {
            // BUGFIX：case-1002560 分配审核TT创建失败 Author: Mr 2024-04-26 Start

            // 注释旧方法：1.插入报错会直接报错 并不会抛出异常 2.未考虑异常导致原编号占用
            /*Result result = redisService.insertRedis(key);
            Integer status = JSON.parseObject(JSON.toJSONString(result.getData()), Integer.class);
            if (status == null || status != 1) {
                throw new BusinessException("并发操作Insert");
            }
            code = code + "-001";
            redisUtil.incr(key, 1L);*/

            code = code + "-001";
            try {
                // 尝试向Redis中插入数据
                redisService.insertRedis(key);
            } catch (Exception e) {
                // 捕获异常，判断是否因异常导致的数据问题
                TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(code);
                if (null != tradeTicketEntity) {
                    // 如果能查询到相应的实体，则认为是并发操作导致的错误，记录错误日志并抛出业务异常
                    log.error("生成TT子编号异常:{}", e.getMessage());
                    throw new BusinessException("并发操作Insert");
                }
            }
            redisUtil.incr(key, 1L);

            // BUGFIX：case-1002560 分配审核TT创建失败 Author: Mr 2024-04-26 End
        } else {
            key = preKey + code.substring(0, index);
            Result lockValue = redisService.getLockValue(key);
            String currentVersion = JSON.parseObject(JSON.toJSONString(lockValue.getData()), String.class);
            Result result = redisService.updateRedis(key, currentVersion);
            Integer status = JSON.parseObject(JSON.toJSONString(result.getData()), Integer.class);
            if (status == null || status != 1) {
                throw new BusinessException("并发操作Update");
            }
            Integer num = Integer.parseInt(currentVersion) + 1;
            String subCode = String.format("%03d", num);
            code = code.substring(0, index + 1) + subCode;
            redisUtil.incr(key, 1L);
        }
        return code;
    }

    /**
     * 规则值转对象 add by zengshl
     *
     * @param ruleItemId
     * @return
     */
    public String systemRuleConvertValue(String ruleItemId) {
        if (ObjectUtil.isNotEmpty(ruleItemId)) {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ruleItemId));
            return ObjectUtil.isNotEmpty(itemEntity) ? itemEntity.getRuleKey() : "";
        }
        return "";
    }

    /**
     * 发票类型：dbt_invoice_type
     *
     * @param invoiceType
     * @return
     */
    public String invoiceTypeConvertValue(Integer invoiceType) {
        return InvoiceTypeEnum.getDescByValue(invoiceType);
    }

    /**
     * 交提货方式：dbt_delivery_type
     * 值转对象 add by zengshl
     *
     * @param deliveryTypeId
     * @return
     */
    public String deliveryTypeConvertValue(Integer deliveryTypeId) {
        if (ObjectUtil.isNotEmpty(deliveryTypeId)) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(deliveryTypeId);
            return ObjectUtil.isNotEmpty(deliveryTypeEntity) ? deliveryTypeEntity.getName() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dba_factory_warehouse 改为 dba_warehouse
     * 值转对象 add by zengshl
     *
     * @param warehouseId
     * @return
     */
    public String factoryConvertValue(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : null;
            }
        }
        return "";
    }

    @Override
    public Result contraryPrice(ContraryPriceDTO contraryPriceDTO) {
        return null;
    }
}


