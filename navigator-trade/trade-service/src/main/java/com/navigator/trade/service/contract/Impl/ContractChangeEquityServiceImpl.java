package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.dto.*;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.activiti.pojo.enums.*;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.dao.ContractChangeEquityDao;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.dao.ContractEquityVODao;
import com.navigator.trade.pojo.dto.contractEquity.ContractChangeEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import com.navigator.trade.pojo.dto.tradeticket.ApprovalDTO;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractEquityVOEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;
import com.navigator.trade.pojo.enums.TTReviewStatusEnum;
import com.navigator.trade.pojo.vo.ContractEquityDetailVO;
import com.navigator.trade.pojo.vo.ContractEquityOperationVO;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.async.ContractAsyncExecutor;
import com.navigator.trade.service.contract.IContractChangeEquityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * dbt_contract_change_equity 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractChangeEquityServiceImpl implements IContractChangeEquityService {

    // dao
    private final ContractDao contractDao;
    private final ContractChangeEquityDao changeEquityDao;
    private final ContractEquityVODao contractEquityVODao;
    // service
    private final IContractQueryService contractQueryService;
    // facade
    private final EmployFacade employFacade;
    private final ApproveFacade approveFacade;
    private final OperationLogFacade operationLogFacade;
    private final PriceApplyFacade priceApplyFacade;
    private final CategoryFacade categoryFacade;
    // util
    private final RedisUtil redisUtil;
    private final ContractAsyncExecutor contractAsyncExecutor;

    @Override
    public IPage<ContractEquityVOEntity> getChangeContractEquityList(QueryDTO<ContractEquityQueryDTO> queryDTO) {
        return contractEquityVODao.getChangeContractEquityList(queryDTO);
    }

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public boolean changeContractEquity(ContractChangeEquityDTO changeEquityDTO) {
        // 校验合同权益变更
        List<ContractEquityDTO> contractEquityDTOList = changeEquityDTO.getContractEquityDTOList();
        if (CollectionUtil.isEmpty(contractEquityDTOList)) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_EQUITY_LIST_NOT_NULL);
        }

        // 变更编号 QYBG+年月+编码
        String changeEquityCode = genContractChangeEquityCode();

        // 申请说明
        String applyRemark = changeEquityDTO.getApplyRemark();

        // 申请人
        String applyName = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId())).getName();

        for (ContractEquityDTO contractEquityDTO : contractEquityDTOList) {
            ContractEntity contractEntity = contractQueryService.getBasicContractById(contractEquityDTO.getContractId());

            checkContractEquity(contractEquityDTO, contractEntity);

            if (null != contractEntity) {
                // 保存合同权益变更
                ContractChangeEquityEntity contractChangeEquity = saveContractChangeEquity(contractEquityDTO, changeEquityCode, contractEntity, applyName, applyRemark);

                // 发起工作流审批
                startApprove(contractChangeEquity, contractEntity);

                // 保存合同权益变更记录
                contractAsyncExecutor.recordChangeEquityLog(contractChangeEquity, Integer.valueOf(JwtUtils.getCurrentUserId()), LogBizCodeEnum.SUBMIT_CONTRACT_CHANGE_EQUITY);
            }
        }
        return true;
    }


    /**
     * 校验合同权益变更
     *
     * @param contractEquityDTO 合同权益变更DTO(前端传入)
     * @param contractEntity    合同实体
     */
    private void checkContractEquity(ContractEquityDTO contractEquityDTO, ContractEntity contractEntity) {
        // 是否存在未审批的合同权益变更
        List<ContractChangeEquityEntity> changeEquityEntityList = changeEquityDao.getChangeContractEquityDetailByNotApprove(contractEquityDTO.getContractId());
        if (CollectionUtil.isNotEmpty(changeEquityEntityList)) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_EQUITY_NOT_APPROVE);
        }

        // 校验合同的次数是否改变
        if (!Objects.equals(contractEquityDTO.getAbleTransferTimes(), contractEntity.getAbleTransferTimes())
                || !Objects.equals(contractEquityDTO.getTransferredTimes(), contractEntity.getTransferredTimes())
                || !Objects.equals(contractEquityDTO.getAbleReversePriceTimes(), contractEntity.getAbleReversePriceTimes())
                || !Objects.equals(contractEquityDTO.getReversedPriceTimes(), contractEntity.getReversedPriceTimes())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_EQUITY_CHANGE_TIMES_ERROR);
        }

        // 基差合同在头寸处理分配待审核状态时,生效中的一口价合同反点价在待挂单、待成交状态时
        Result result = priceApplyFacade.getNotDealByContractId(contractEquityDTO.getContractId());
        if (result.isSuccess()) {
            boolean existFlag = (boolean) result.getData();
            if (existFlag) {
                Integer contractType = contractEntity.getContractType();
                if (Objects.equals(contractType, ContractTypeEnum.JI_CHA.getValue())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_EQUITY_CHANGE_BASIS_NOT_APPROVE);
                }
                if (Objects.equals(contractType, ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_EQUITY_CHANGE_PRICE_NOT_APPROVE);
                }
            }
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }
    }

    @Override
    public ContractEquityDetailVO getChangeEquityDetailByApplyCode(String applyCode) {
        ContractEquityDetailVO contractEquityDetailVO = new ContractEquityDetailVO();

        contractEquityDetailVO.setApplyCode(applyCode);

        // 业务变更信息
        List<ContractChangeEquityEntity> changeEquityEntityList = changeEquityDao.getChangeEquityDetailByApplyCode(applyCode);

        List<EquityChangeInfoDTO> changeInfoDTOList = new ArrayList<>();
        for (ContractChangeEquityEntity contractChangeEquity : changeEquityEntityList) {
            EquityChangeInfoDTO equityChangeInfoDTO = BeanUtil.toBean(contractChangeEquity, EquityChangeInfoDTO.class);

            // 客户名称
            ContractEntity contract = contractDao.getContractById(contractChangeEquity.getContractId());
            if (null != contract) {
                equityChangeInfoDTO
                        .setCustomerId(contract.getCustomerId())
                        .setCustomerName(contract.getCustomerName());
            }

            equityChangeInfoDTO
                    .setChangeAbleTransferTimes(getChangeTimes(equityChangeInfoDTO.getBeforeAbleTransferTimes(), equityChangeInfoDTO.getAfterAbleTransferTimes()))
                    .setChangeTransferredTimes(getChangeTimes(equityChangeInfoDTO.getBeforeTransferredTimes(), equityChangeInfoDTO.getAfterTransferredTimes()))
                    .setChangeAbleReversePriceTimes(getChangeTimes(equityChangeInfoDTO.getBeforeAbleReversePriceTimes(), equityChangeInfoDTO.getAfterAbleReversePriceTimes()))
                    .setChangeReversedPriceTimes(getChangeTimes(equityChangeInfoDTO.getBeforeReversedPriceTimes(), equityChangeInfoDTO.getAfterReversedPriceTimes()));

            changeInfoDTOList.add(equityChangeInfoDTO);
        }
        contractEquityDetailVO.setEquityChangeInfoDTOList(changeInfoDTOList);

        // 工作流审批信息
        String approvalRemark = "";
        String startUserName = "";
        Date startTime = null;

        if (CollectionUtil.isNotEmpty(changeEquityEntityList)) {
            String bizCode = changeEquityEntityList.get(0).getApplyCode() + ";" + changeEquityEntityList.get(0).getContractCode();
            approvalRemark = changeEquityEntityList.get(0).getRemark();

            Result result = approveFacade.getActHiTaskByBizCode(bizCode);
            if (result.isSuccess()) {
                List<ActHiTaskinstEntity> taskinstEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), ActHiTaskinstEntity.class);
                if (CollectionUtil.isNotEmpty(taskinstEntityList)) {
                    Result taskResult = approveFacade.queryHiTaskByProcInstId(taskinstEntityList.get(0).getProcInstId());
                    if (taskResult.isSuccess()) {
                        List<ApproveTaskActInfoDTO> approveTaskActInfoDTOS = JSON.parseArray(JSON.toJSONString(taskResult.getData()), ApproveTaskActInfoDTO.class);
                        contractEquityDetailVO.setApproveTaskInfoList(approveTaskActInfoDTOS);
                        if (CollectionUtil.isNotEmpty(approveTaskActInfoDTOS)) {
                            startUserName = approveTaskActInfoDTOS.get(0).getApproveUserName();
                            startTime = approveTaskActInfoDTOS.get(0).getStartTime();
                        }
                    }
                }
            }
        }

        contractEquityDetailVO.setApproveRuleName("CFO审批")
                .setApproveCause(approvalRemark)
                .setStartUserName(startUserName)
                .setStartTime(startTime);
        return contractEquityDetailVO;
    }

    @Override
    public List<ContractChangeEquityEntity> getChangeEquityByContractCode(String contractCode) {
        return changeEquityDao.getChangeEquityByContractCode(contractCode);
    }

    @Override
    public boolean approveEquityChange(ApprovalDTO approvalDTO) {
        List<ContractChangeEquityEntity> changeEquityEntityList = changeEquityDao.getChangeEquityDetailByApplyCode(approvalDTO.getEquityApplyCode());
        if (CollectionUtil.isNotEmpty(changeEquityEntityList)) {

            String userId = JwtUtils.getCurrentUserId();
            ApproveDTO<Object> approveDTO = new ApproveDTO<>();
            // 审批通过 || 审批驳回
            if (Objects.equals(TTReviewStatusEnum.PASS.getValue(), approvalDTO.getApproveStatus())) {
                approveDTO.setActionValue(ApproveActionEnum.AGREE.getValue());
            } else {
                approveDTO.setActionValue(ApproveActionEnum.REJECT.getValue());
            }

            StringBuilder contractCode = new StringBuilder();
            String approvalResult = "";
            String createdBy = "";

            for (ContractChangeEquityEntity contractChangeEquity : changeEquityEntityList) {
                // 审批状态为审批通过或审批驳回的不再审批
                if (contractChangeEquity.getApproveStatus() == TTApproveStatusEnum.APPROVE.getValue()
                        || contractChangeEquity.getApproveStatus() == TTApproveStatusEnum.REJECT.getValue()) {
                    continue;
                }

                ContractEntity contractEntity = contractQueryService.getBasicContractById(contractChangeEquity.getContractId());

                if (null != contractEntity) {
                    // taskId
                    String taskId = approvalDTO.getTaskId();
                    if (changeEquityEntityList.size() > 1) {
                        Result result = approveFacade.getActHiTaskByBizCode(contractChangeEquity.getApplyCode() + ";" + contractChangeEquity.getContractCode());
                        if (result.isSuccess()) {
                            List<ActHiTaskinstEntity> taskinstEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), ActHiTaskinstEntity.class);
                            if (CollectionUtil.isNotEmpty(taskinstEntityList)) {
                                taskId = taskinstEntityList.get(0).getId();
                            }
                        }
                    }

                    approveDTO
                            .setBizModule(ModuleTypeEnum.CONTRACT_EQUITY.getModule())
                            .setCategory1(contractEntity.getCategory1())
                            .setCategory2(contractEntity.getCategory2())
                            .setCategory3(contractEntity.getCategory3())
                            .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(contractEntity.getSalesType()))
                            .setMemo(approvalDTO.getMemo())
                            .setContractTradeTypeEnum(ContractTradeTypeEnum.EQUITY_CHANGE)
                            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                            .setTtTypeEnum(TTTypeEnum.EQUITY_CHANGE)
                            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                            .setUserId(userId)
                            .setTaskId(taskId)
                            .setBuCode(contractEntity.getBuCode())
                            .setBizId(contractChangeEquity.getId())
                            .setBizCode(contractChangeEquity.getApplyCode())
                            .setReferBizId(contractEntity.getId())
                            .setReferBizCode(contractEntity.getContractCode())
                            .setProcessKey(Objects.equals(contractEntity.getGoodsCategoryId(), GoodsCategoryEnum.OSM_MEAL.getValue())
                                    ? ApproveProcessEnum.SC_EQUITY.getProcessKey() : ApproveProcessEnum.SC_SBO_EQUITY.getProcessKey());

                    log.info("审批合同权益变更申请，参数：{}", JSON.toJSONString(approveDTO));
                    Result result = approveFacade.approve(approveDTO);

                    log.info("===================approveResult:===================");
                    log.info("approveEquityApplyCode:{},result:{}", approvalDTO.getEquityApplyCode(), JSON.toJSONString(result));
                    if (!result.isSuccess()) {
                        log.info("approveEquityApplyCode:{},approve error : {} ", approvalDTO.getEquityApplyCode(), JSON.toJSONString(result));
                        throw new BusinessException(result.getMessage());
                    }

                    ApproveResultDTO approveResultDTO = JSON.parseObject(JSON.toJSONString(result.getData()), ApproveResultDTO.class);
                    Integer approveResult = approveResultDTO.getApproveResult();

                    // 审批前的状态
                    TTApproveStatusEnum approveStatusEnum = TTApproveStatusEnum.getByValue(contractChangeEquity.getApproveStatus());

                    // 根据审批结果更新和合同权益状态
                    if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
                        handleApprovingResult(contractChangeEquity, approveResultDTO.getProcInstStatus());
                    }

                    if (approveResult == ApproveResultEnum.AGREE.getValue()) {
                        changeEquityDao.updateApprovalStatusByCode(TTApproveStatusEnum.APPROVE.getValue(), contractChangeEquity.getApplyCode());

                        // 更新合同权益的次数
                        contractEntity.setAbleTransferTimes(contractChangeEquity.getAfterAbleTransferTimes())
                                .setAbleReversePriceTimes(contractChangeEquity.getAfterAbleReversePriceTimes())
                                .setTransferredTimes(contractChangeEquity.getAfterTransferredTimes())
                                .setReversedPriceTimes(contractChangeEquity.getAfterReversedPriceTimes())
                                .setTotalTransferTimes(contractChangeEquity.getAfterAbleTransferTimes() + contractChangeEquity.getAfterTransferredTimes())
                                .setTotalReversePriceTimes(contractChangeEquity.getAfterAbleReversePriceTimes() + contractChangeEquity.getAfterReversedPriceTimes());
                        contractDao.updateById(contractEntity);

                        // 发送站内信
                        approvalResult = "已通过";
                    }

                    //审批驳回
                    if (approveResult == ApproveResultEnum.REJECT.getValue()) {
                        changeEquityDao.updateApprovalStatusByCode(TTApproveStatusEnum.REJECT.getValue(), contractChangeEquity.getApplyCode());

                        // 发送站内信
                        approvalResult = "被驳回";
                    }

                    // 记录审批日志
                    LogBizCodeEnum logBizCodeEnum = getChangeEquityLogBizCode(approveStatusEnum, approveResult);
                    if (null != logBizCodeEnum) {
                        contractAsyncExecutor.recordChangeEquityLog(contractChangeEquity, Integer.valueOf(JwtUtils.getCurrentUserId()), logBizCodeEnum);
                    }

                    createdBy = contractChangeEquity.getApplyBy() == null ? "" : contractChangeEquity.getApplyBy().toString();

                }
                contractCode.append(contractChangeEquity.getContractCode()).append(" ");
            }

            // 发送站内信
            if (StrUtil.isNotBlank(approvalResult)) {
                contractAsyncExecutor.sendInMailMessage(String.valueOf(contractCode), approvalResult, createdBy);
            }
        }
        return true;
    }

    @Override
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId) {
        return changeEquityDao.getChangeContractEquityDetailByNotApprove(contractId);
    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
    @Override
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByContractIds(List<Integer> contractIds) {
        return changeEquityDao.getChangeContractEquityDetailByContractIds(contractIds);
    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end

    @Override
    public List<ContractEquityOperationVO> getChangeContractEquityRecord(String contractCode) {

        Map<String, List<OperationDetailEntity>> operationLogMap = new HashMap<>();

        Result result = operationLogFacade.queryOperationDetailByReferBizCode(contractCode, OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue());
        if (result.isSuccess()) {
            List<OperationDetailEntity> operationDetailList = JSON.parseArray(JSON.toJSONString(result.getData()), OperationDetailEntity.class);
            operationLogMap = operationDetailList.stream().collect(Collectors.groupingBy(OperationDetailEntity::getTtCode));
        }

        // 遍历operationLogMap
        List<ContractEquityOperationVO> contractEquityOperationVOList = new ArrayList<>();
        for (Map.Entry<String, List<OperationDetailEntity>> entry : operationLogMap.entrySet()) {
            ContractEquityOperationVO equityOperationVO = new ContractEquityOperationVO();
            equityOperationVO.setApplyCode(entry.getKey());

            List<OperationDetailEntity> operationDetailEntityList = entry.getValue();
            if (CollectionUtil.isNotEmpty(operationDetailEntityList)) {
                OperationDetailEntity operationDetailEntity = operationDetailEntityList.get(0);
                equityOperationVO.setCreatedAt(operationDetailEntity.getCreatedAt());
            }
            equityOperationVO.setOperationLogList(operationDetailEntityList);

            contractEquityOperationVOList.add(equityOperationVO);
        }

        return contractEquityOperationVOList;
    }

    /**
     * 生成变更单号 规则：QYBG+年月+编码
     */
    private String genContractChangeEquityCode() {

        // 当前年月
        String yyyyMM = DateUtil.format(DateUtil.date(), "yyyyMM");

        // 获取当月最大的申请单号
        String sequence = "contract:equity:change:code";

        // 获取递增码
        long incr = redisUtil.incr(sequence, 1L);

        return "QYBG" + yyyyMM + String.format("%05d", incr);
    }

    /**
     * 保存合同权益变更
     *
     * @param contractEquityDTO 合同权益变更DTO
     * @param changeEquityCode  变更编号
     * @param contractEntity    合同实体
     * @param applyName         申请人
     * @param applyRemark       申请说明
     * @return 合同权益变更实体
     */
    private ContractChangeEquityEntity saveContractChangeEquity(ContractEquityDTO contractEquityDTO, String changeEquityCode, ContractEntity contractEntity, String applyName, String applyRemark) {
        ContractChangeEquityEntity contractChangeEquity = new ContractChangeEquityEntity();
        contractChangeEquity.setApplyCode(changeEquityCode)
                .setApproveStatus(TTApproveStatusEnum.WAIT_A_SIGN.getValue())
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setBeforeAbleTransferTimes(contractEntity.getAbleTransferTimes())
                .setBeforeAbleReversePriceTimes(contractEntity.getAbleReversePriceTimes())
                .setBeforeTransferredTimes(contractEntity.getTransferredTimes())
                .setBeforeReversedPriceTimes(contractEntity.getReversedPriceTimes())
                .setAfterAbleTransferTimes(contractEntity.getAbleTransferTimes() + contractEquityDTO.getChangeAbleTransferTimes())
                .setAfterAbleReversePriceTimes(contractEntity.getAbleReversePriceTimes() + contractEquityDTO.getChangeAbleReversePriceTimes())
                .setAfterTransferredTimes(contractEntity.getTransferredTimes() + contractEquityDTO.getChangeTransferredTimes())
                .setAfterReversedPriceTimes(contractEntity.getReversedPriceTimes() + contractEquityDTO.getChangeReversedPriceTimes())
                .setApplyBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setCreatedBy(applyName)
                .setUpdatedBy(applyName)
                .setRemark(applyRemark);
        changeEquityDao.save(contractChangeEquity);
        return contractChangeEquity;
    }

    /**
     * 发起变更申请
     *
     * @param contractChangeEquity 变更申请实体
     */
    private void startApprove(ContractChangeEquityEntity contractChangeEquity, ContractEntity contractEntity) {
        CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory1());
        CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());
        CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory3());

        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO
                .setUserId(JwtUtils.getCurrentUserId())
                .setActionValue(ApproveActionEnum.START.getValue())
                .setApproveCause(contractChangeEquity.getRemark())
                .setApproveRuleValue(ContractApproveRuleEnum.ABC1.getValue())
                .setBelongCustomerId(contractEntity.getBelongCustomerId())
                .setBizId(contractChangeEquity.getId())
                .setBizCode(contractChangeEquity.getApplyCode())
                .setReferBizId(contractEntity.getId())
                .setReferBizCode(contractEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_EQUITY.getModule())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(contractEntity.getSalesType()))
                .setBuCode(contractEntity.getBuCode())
                .setCompanyId(contractEntity.getCompanyId())
                .setCompanyName(contractEntity.getCompanyName())
                .setContractTradeTypeEnum(ContractTradeTypeEnum.EQUITY_CHANGE)
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtTypeEnum(TTTypeEnum.EQUITY_CHANGE)
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setCustomerId(contractEntity.getCustomerId())
                .setCustomerName(contractEntity.getCustomerName())
                .setSupplierId(contractEntity.getSupplierId())
                .setSupplierName(contractEntity.getSupplierName())
                .setCategory1(contractEntity.getCategory1())
                .setCategory1Name(category1Entity.getName())
                .setCategory2(contractEntity.getCategory2())
                .setCategory2Name(category2Entity.getName())
                .setCategory3(contractEntity.getCategory3())
                .setCategory3Name(category3Entity.getName())
                .setSiteCode(contractEntity.getSiteCode())
                .setSiteName(contractEntity.getSiteName())
                .setProcessKey(Objects.equals(contractEntity.getGoodsCategoryId(), GoodsCategoryEnum.OSM_MEAL.getValue())
                        ? ApproveProcessEnum.SC_EQUITY.getProcessKey() : ApproveProcessEnum.SC_SBO_EQUITY.getProcessKey());

        // 客户名称（审批需要）
        contractChangeEquity.setCustomerId(contractEntity.getCustomerId());
        contractChangeEquity.setCustomerName(contractEntity.getCustomerName());

        // 业务数据
        approveDTO.setBizData(buildApproveBizData(contractChangeEquity));

        log.info("发起合同权益变更申请，参数：{}", JSON.toJSONString(approveDTO));

        approveFacade.start(approveDTO);

    }

    /**
     * 构建业务数据
     *
     * @param contractChangeEquity 合同权益变更实体
     * @return
     */
    private String buildApproveBizData(ContractChangeEquityEntity contractChangeEquity) {
        List<ApproveBizInfoDTO> approveBizInfoDTOList = new ArrayList<>();
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setName("contractEquityChange")
                .setDisplayName("合同权益变更")
                .setIndex(1)
                .setValue(JSON.toJSONString(contractChangeEquity)));
        return JSONArray.toJSONString(approveBizInfoDTOList);
    }

    /**
     * 获取变更次数
     *
     * @param beforeTimes 变更前次数
     * @param afterTimes  变更后次数
     * @return
     */
    private String getChangeTimes(Integer beforeTimes, Integer afterTimes) {
        return afterTimes - beforeTimes > 0 ? "+" + (afterTimes - beforeTimes) :
                afterTimes - beforeTimes < 0 ? (afterTimes - beforeTimes) + "" : "0";
    }

    /**
     * 获取变更日志code
     *
     * @param approveStatusEnum 审批状态
     * @param approveResult     审批结果
     * @return
     */
    private LogBizCodeEnum getChangeEquityLogBizCode(TTApproveStatusEnum approveStatusEnum, Integer approveResult) {
        LogBizCodeEnum logBizCodeEnum = null;

        // 记录审批驳回日志
        switch (approveStatusEnum) {
            case WAIT_A_SIGN:
                if (approveResult == ApproveResultEnum.REJECT.getValue()) {
                    logBizCodeEnum = LogBizCodeEnum.A_REJECT_CONTRACT_CHANGE_EQUITY;
                } else {
                    logBizCodeEnum = LogBizCodeEnum.A_APPROVE_CONTRACT_CHANGE_EQUITY;
                }
                break;
            case WAIT_B_SIGN:
                if (approveResult == ApproveResultEnum.REJECT.getValue()) {
                    logBizCodeEnum = LogBizCodeEnum.B_REJECT_CONTRACT_CHANGE_EQUITY;
                } else {
                    logBizCodeEnum = LogBizCodeEnum.B_APPROVE_CONTRACT_CHANGE_EQUITY;
                }
                break;
            case WAIT_C_SIGN:
                if (approveResult == ApproveResultEnum.REJECT.getValue()) {
                    logBizCodeEnum = LogBizCodeEnum.C_REJECT_CONTRACT_CHANGE_EQUITY;
                } else {
                    logBizCodeEnum = LogBizCodeEnum.C_APPROVE_CONTRACT_CHANGE_EQUITY;
                }
                break;
            default:
                break;
        }
        return logBizCodeEnum;
    }

    private void handleApprovingResult(ContractChangeEquityEntity contractChangeEquity, String procInstStatus) {
        BizApproveStatusEnum bizApproveStatusEnum = BizApproveStatusEnum.getByDesc(procInstStatus);
        log.info("=======>contractChangeEquity:{}handleApprovingResult.procInstStatus;{}", contractChangeEquity.getApplyCode(), procInstStatus);
        switch (bizApproveStatusEnum) {
            case A_Approving:
                changeEquityDao.updateApprovalStatusByCode(TTApproveStatusEnum.WAIT_A_SIGN.getValue(), contractChangeEquity.getApplyCode());
                break;
            case B_Approving:
                changeEquityDao.updateApprovalStatusByCode(TTApproveStatusEnum.WAIT_B_SIGN.getValue(), contractChangeEquity.getApplyCode());
                break;
            case C_Approving:
            case CEO_Approving:
            case CFO_Approving:
                changeEquityDao.updateApprovalStatusByCode(TTApproveStatusEnum.WAIT_C_SIGN.getValue(), contractChangeEquity.getApplyCode());
                break;
            default:
                break;
        }
    }
}
