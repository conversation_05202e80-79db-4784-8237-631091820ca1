package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.logic.service.handler.BuyBackLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 合同回购 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class BuyBackLogicServiceImpl implements BuyBackLogicService {


    @Autowired
    private ContractDomainService contractDomainService;

    @Autowired
    private CommonLogicService commonLogicService;

    @Autowired
    private StructureLogicService structureLogicService;

    @Override
    public void buyBackContractCheck(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        // 数量校验:可回购数量≤合同总量-MAX（已定价量/已开单量）
        if (BigDecimalUtil.isZero(contractBuyBackDTO.getBuyBackNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_BUY_BACK_NUM_EXCEPTION);
        }

        BigDecimal buyBackNum = contractEntity.getContractNum().subtract(contractEntity.getTotalBuyBackNum());
        BigDecimal requestedBuyBackNum = contractBuyBackDTO.getBuyBackNum();

        // 回购数量校验
        switch (ContractTypeEnum.getByValue(contractEntity.getContractType())) {
            case YI_KOU_JIA: {
                // 对于一口价合同，使用采购合同时，取总提单量和注销量中较大者
                BigDecimal maxBillOrCancel = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getWarrantCancelCount());
                BigDecimal limit = buyBackNum.subtract(maxBillOrCancel);
                if (BigDecimalUtil.isGreater(requestedBuyBackNum, limit)) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_BUY_BACK_DELIVERY_NUM_EXCEPTION);
                }
                break;
            }
            case JI_CHA: {
                // 对于基差合同，取剩余数量为合同剩余数减去已计入价格的数量，再减注销数量
                BigDecimal limit = buyBackNum.subtract(contractEntity.getTotalPriceNum())
                        .subtract(contractEntity.getWarrantCancelCount());
                if (BigDecimalUtil.isGreater(requestedBuyBackNum, limit)) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_BUY_BACK_PRICE_NUM_EXCEPTION);
                }

                // 结构化定价合同校验
                Boolean b = structureLogicService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                        .setCustomerId(contractEntity.getCustomerId())
                        .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                        .setDomainCode(contractEntity.getDomainCode())
                        .setContractNum(contractBuyBackDTO.getBuyBackNum())
                        .setCompanyId(contractEntity.getCompanyId())
                );
                if (b) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
                }
                break;
            }
            case JI_CHA_ZAN_DING_JIA: {
                //case-1003202 回购场景一口价报文Pricing Method处理 Author:Mr 2025-05-15 Start
                // 基差暂定价可回购数量为已定价部分
                BigDecimal pricedNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
                BigDecimal buyBackedNum = contractEntity.getTotalBuyBackNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBuyBackNum();
                buyBackNum = pricedNum.subtract(buyBackedNum);
                //case-1003202 回购场景一口价报文Pricing Method处理 Author:Mr 2025-05-15 end

                // 再校验提单相关数量：取总提单量、分配数量和注销数量中的最大值
                BigDecimal maxValue = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getAllocateNum(), contractEntity.getWarrantCancelCount());
                BigDecimal limitDelivery = buyBackNum.subtract(maxValue);
                if (BigDecimalUtil.isGreater(requestedBuyBackNum, limitDelivery)) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_BUY_BACK_DELIVERY_NUM_EXCEPTION);
                }
                break;
            }
            default:
                break;
        }

        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }
        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

    }

    @Override
    public void operateFatherContract(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        // 回购数量
        BigDecimal buyBackNum = contractEntity.getTotalBuyBackNum().add(contractBuyBackDTO.getBuyBackNum());
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        contractDomainService.updateContractById(contractEntity.setTotalBuyBackNum(buyBackNum));

    }

    @Override
    public void recordOperationLog(ContractBuyBackDTO contractBuyBackDTO, ContractEntity contractEntity) {
        // 记录回购日志
        commonLogicService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.SALES_CONTRACT_BUY_BACK, JSONUtil.toJsonStr(contractBuyBackDTO),
                SystemEnum.MAGELLAN.getValue());
    }
}
