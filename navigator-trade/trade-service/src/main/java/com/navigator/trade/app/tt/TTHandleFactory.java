package com.navigator.trade.app.tt;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.app.tt.domain.model.TTConfigValueDTO;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.service.processor.AbstractTTDomainProcessor;
import com.navigator.trade.app.tt.logic.service.handler.ITTSceneHandler;
import com.navigator.trade.pojo.enums.TTDomainProcessTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class TTHandleFactory {

    @Autowired
    @Lazy
    private Map<String, AbstractTTDomainProcessor> ttDomainProcessorMap;

    @Autowired
    @Lazy
    private List<ITTSceneHandler> ttSceneHandlers;

    @Autowired
    @Lazy
    private Map<String, ITTSceneHandler> ttSceneHandlerMap;


    public TTConfigValueDTO getTtConfigValueDTO(TradeTicketDO tradeTicketDO) {
        TTConfigValueDTO ttConfigValueDTO = new TTConfigValueDTO();
        ttConfigValueDTO.setTtDomainProcessTypeEnum(TTDomainProcessTypeEnum.getByTTType(tradeTicketDO.getTTType()));
        return ttConfigValueDTO;
    }

    /**
     * 获取TT Domain层的处理器
     * @param ttTypeEnum
     * @return
     */
    public AbstractTTDomainProcessor getTTDomainProcessor(TTTypeEnum ttTypeEnum) {
        AbstractTTDomainProcessor ttDomainProcessor;

        TTDomainProcessTypeEnum processKey = TTDomainProcessTypeEnum.getByTTType(ttTypeEnum);

        ttDomainProcessor = ttDomainProcessorMap.get(processKey.name());

        return ttDomainProcessor;
    }


    /**
     * 获取TTLogicService的场景处理器
     * @param ttTypeEnum
     * @return
     */
    public ITTSceneHandler getTTSceneHandler(TTTypeEnum ttTypeEnum) {
        ITTSceneHandler ittSceneHandler;

        String handlerName = ttTypeEnum.name() + "_HANDLER";

        if (ttTypeEnum == TTTypeEnum.NEW) {
            handlerName = TTTypeEnum.NEW.name() + "_HANDLER";
        }

        ittSceneHandler = ttSceneHandlerMap.get(handlerName);

        //兼容可以处理多个类型的处理器
        if (null == ittSceneHandler) {
            for (String s : ttSceneHandlerMap.keySet()) {
                if (s.contains(handlerName)) {
                    return ttSceneHandlerMap.get(s);
                }
            }
        }

        return ittSceneHandler;
    }
}
