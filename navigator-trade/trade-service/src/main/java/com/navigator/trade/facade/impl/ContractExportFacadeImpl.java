package com.navigator.trade.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.trade.facade.ContractExportFacade;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.service.contract.IContractExportService;
import com.navigator.trade.service.sync.AtlasSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合同导出的接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/07/16
 */
@RestController
public class ContractExportFacadeImpl implements ContractExportFacade {

    @Autowired
    private IContractExportService contractExportService;
    @Resource
    private AtlasSyncService atlasSyncService;

    @Override
    public Result exportDailyContract(String startDateTime, String endDateTime) {

        return Result.success(contractExportService.exportDailyContract(startDateTime, endDateTime));
    }

    @Override
    public Result exportContractExcel(ContractBO contractBO) {
        return Result.success(contractExportService.exportContractExcel(contractBO));
    }

    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 start
    @Override
    public Result exportContractExcelMultiStatus(ContractBO contractBO) {
        return Result.success(contractExportService.exportContractExcelMultiStatus(contractBO));
    }
    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 end

    @Override
    public Result dataMigration(String status, Map<String, List<String>> requestBody) {
        List<String> contractCodes = requestBody != null ? requestBody.get("contractCodes") : null;
        return Result.success(atlasSyncService.contractMigration(status, contractCodes));
    }
    @Override
    public Result priceMigration(String status, Map<String, List<String>> requestBody) {
        List<String> contractCodes = requestBody != null ? requestBody.get("contractCodes") : null;
        return Result.success(atlasSyncService.priceMigration(status, contractCodes));
    }
}
