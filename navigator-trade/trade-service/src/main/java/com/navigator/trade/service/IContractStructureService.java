package com.navigator.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.entity.ContractStructureEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IContractStructureService extends IService<ContractStructureEntity> {

    ContractStructureEntity submitStructureContract(SalesStructurePriceTTDTO structurePriceTTDTO);

    boolean updateStructureContractPricingStatus(ContractStructureDTO contractStructureDTO);

    ContractStructureEntity getContractStructure(Integer contractId);

    List<PriceDealDetailVO> getContractStructurePriceList(Integer priceApplyId);


    /**
     * 根据条件查询结构化定价合同
     *
     * @param queryDTO
     * @return
     */
    Result queryContractStructure(QueryDTO<ContractBO> queryDTO);

    /**
     * 写
     * @param contractId
     * @param notDealNum
     * @return
     */
    boolean addStructureRelease(Integer contractId, BigDecimal notDealNum);

    Integer getApplyIdByStructureContractId(Integer contractId);

    ContractStructureEntity getContractStructureById(Integer contractId);

    ContractStructureEntity getContractStructureVOById(Integer contractId);

    List<ContractStructureEntity> getValidStructureContract(List<Integer> contractIds);

    Integer getApplyIdByStructureContractCode(String contractCode);

    /**
     * 写动作
     * @param contractStructureEntity
     * @return
     */
    boolean updateStructureContract(ContractStructureEntity contractStructureEntity);

    /**
     * 写动作
     * @param verifyContractStructureNumDTO
     * @return
     */
    Boolean verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO);
}
