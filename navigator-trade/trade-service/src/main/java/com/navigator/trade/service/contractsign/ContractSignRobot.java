package com.navigator.trade.service.contractsign;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.TemplateTypeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.Html2PdfUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.trade.dao.ContractSignTemplateLogDao;
import com.navigator.trade.dao.TemplateAttributeDao;
import com.navigator.trade.dao.TemplateDao;
import com.navigator.trade.pojo.dto.contractsign.ContractSignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractSignTemplateLogEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class ContractSignRobot {
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Resource
    private AzureBlobUtil azureBlobUtil;
    @Autowired
    private TemplateAttributeDao templateAttributeDao;
    @Autowired
    private TemplateDao templateDao;
    @Resource
    private Html2PdfUtils html2PdfUtils;
    @Resource
    private ContractSignBuildProcessor contractSignBuildProcessor;
    @Resource
    private ContractSignTemplateLogDao contractSignTemplateLogDao;
    /*@Value("${contract.sign.qrCodeUrl}")
    private String qrCodeUrl;*/


    public String assembleTemplateInfo(Integer templateId) {
        TemplateEntity templateEntity = this.templateDao.getById(templateId);
        if (null == templateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        String templateContentResult = templateEntity.getContent();
        if (StringUtils.isNotBlank(templateContentResult) && StringUtils.isNotBlank(templateEntity.getSubTemplateIds())) {
            List<String> mTemplateIds = Arrays.asList(templateEntity.getSubTemplateIds().split(","));
            for (String mTemplateId : mTemplateIds) {
                TemplateEntity mTemplateEntity = this.templateDao.getById(Integer.valueOf(mTemplateId.trim()));
                if (null != mTemplateEntity) {
                    //M条款拼接E模版信息
                    String mTemplateContent = getMTemplateContent(mTemplateEntity);
                    templateContentResult = templateContentResult.replace("#" + mTemplateEntity.getCode() + "#", mTemplateContent);
                }
            }
        }
        return templateContentResult;
    }

    private String getMTemplateContent(TemplateEntity mTemplateEntity) {
        if (null == mTemplateEntity) {
            return "";
        }
        String mTemplateContent = mTemplateEntity.getContent();
        if (StringUtils.isNotBlank(mTemplateEntity.getSubTemplateIds())) {
            List<String> eTemplateIds = Arrays.asList(mTemplateEntity.getSubTemplateIds().split(","));
            for (String eTemplateId : eTemplateIds) {
                TemplateEntity eTemplateEntity = this.templateDao.getById(Integer.valueOf(eTemplateId));
                if (null != eTemplateEntity && TemplateTypeEnum.ORIGIN_TEMPLATE.getValue().equals(eTemplateEntity.getType())) {
                    mTemplateContent = mTemplateContent.replace("#" + eTemplateEntity.getCode() + "#", eTemplateEntity.getContent());
                }
            }
        }
        return mTemplateContent;
    }

    public void createPdf(ContractSignTemplateDTO contractSignTemplateDTO) {
        SignTemplateDTO signTemplateDTO = contractSignBuildProcessor.buildSignTemplateDTO(contractSignTemplateDTO);

        ContractSignEntity contractSignEntity = contractSignTemplateDTO.getContractSignDTO();

        // 1、获取合同的模板
        QueryTemplateAttributeDTO templateAttributeDTO = new QueryTemplateAttributeDTO()
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setContractType(contractSignEntity.getContractType())
                .setSalesType(contractSignEntity.getSalesType())
                .setTemplateType(contractSignEntity.getFrameProtocolType())
                .setSignType(contractSignEntity.getSignType() != null ? contractSignEntity.getSignType() : 0)
                //除了split:3，转月：4,tradeType为0
                .setTradeType(contractSignEntity.getTradeType() != null && Arrays.asList(TTTypeEnum.SPLIT.getType(), TTTypeEnum.TRANSFER.getType(), TTTypeEnum.REVERSE_PRICE.getType()).contains(contractSignEntity.getTtType()) ? contractSignEntity.getTradeType() : 0)
                .setActionType(contractSignEntity.getTtType());

        TemplateAttributeEntity templateAttributeEntity = templateAttributeDao.queryTemplateAttribute(templateAttributeDTO);
        String templateContent = assembleTemplateInfo(null != templateAttributeEntity ? templateAttributeEntity.getTemplateId() : TemplateConstant.DEFAULT_TEMPLATE_ID);


        // 3、数据对象->map
        Map<String, Object> dataMap = BeanUtil.beanToMap(signTemplateDTO);
        String bizContent = "";
        String errorMessage = "";
        // 4、渲染模板
        try {
            bizContent = TemplateRenderUtil.templateRender(dataMap, templateContent);

            System.out.println("========================================");
            System.out.println("========================================");
            System.out.println(bizContent);
        } catch (Exception e) {
            System.out.println("*********************************");
            bizContent = templateContent;
            errorMessage = e.getMessage();
        }

        Integer cid = contractSignEntity.getContractId();
        String contractCode = contractSignEntity.getContractCode();
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + cid + "_" + contractCode + "_" + DateTimeUtil.formatDateTimeValue() + ".html")
                .setHtmlContent(bizContent);

        html2PdfUtils.genPdfAndImage(pdfInfoDTO);

        HtmlInfoDTO pdfInfoDTO2 = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + cid + "_" + contractCode + "_" + DateTimeUtil.formatDateTimeValue() + "_000.html")
                .setHtmlContent(printSignInfo(signTemplateDTO, contractSignEntity));

        html2PdfUtils.genPdfAndImage(pdfInfoDTO2);
    }

    public void createPdf(Integer logId) {
        ContractSignTemplateLogEntity templateLogEntity = contractSignTemplateLogDao.getTemplateLog4(logId);

        String old = templateLogEntity.getBizData();
        String neo = templateLogEntity.getMemo().replace("模板数据对比", "");
        String templateContent = templateLogEntity.getContent();

        SignTemplateDTO oldDTO = JSON.parseObject(old, SignTemplateDTO.class);
        SignTemplateDTO signTemplateDTO = JSON.parseObject(neo, SignTemplateDTO.class);

        // 3、数据对象->map
        Map<String, Object> dataMap = BeanUtil.beanToMap(signTemplateDTO);
        String bizContent = "";
        String errorMessage = "";
        // 4、渲染模板
        try {
            bizContent = TemplateRenderUtil.templateRender(dataMap, templateContent);

        } catch (Exception e) {
            System.out.println("*********************************");
            bizContent = templateContent;
            errorMessage = e.getMessage();
        }

        Integer cid = templateLogEntity.getContractId();
        Integer ttid = templateLogEntity.getTtId();
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + ttid + "_" + cid + "_" + DateTimeUtil.formatDateTimeValue() + ".html")
                .setHtmlContent(bizContent);

        html2PdfUtils.genPdfAndImage(pdfInfoDTO);

        HtmlInfoDTO pdfInfoDTO2 = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + ttid + "_" + cid + "_" + DateTimeUtil.formatDateTimeValue() + "_000.html")
                .setHtmlContent(printSignInfo(signTemplateDTO, oldDTO));

        html2PdfUtils.genPdfAndImage(pdfInfoDTO2);


    }

    public void createPdf(SignTemplateDTO signTemplateDTO, ContractSignEntity contractSignEntity) {

        // 1、获取合同的模板
        QueryTemplateAttributeDTO templateAttributeDTO = new QueryTemplateAttributeDTO()
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setContractType(contractSignEntity.getContractType())
                .setSalesType(contractSignEntity.getSalesType())
                .setTemplateType(contractSignEntity.getFrameProtocolType())
                .setSignType(contractSignEntity.getSignType() != null ? contractSignEntity.getSignType() : 0)
                //除了split:3，转月：4,tradeType为0
                .setTradeType(contractSignEntity.getTradeType() != null && Arrays.asList(TTTypeEnum.SPLIT.getType(), TTTypeEnum.TRANSFER.getType(), TTTypeEnum.REVERSE_PRICE.getType()).contains(contractSignEntity.getTtType()) ? contractSignEntity.getTradeType() : 0)
                .setActionType(contractSignEntity.getTtType());

        TemplateAttributeEntity templateAttributeEntity = templateAttributeDao.queryTemplateAttribute(templateAttributeDTO);
        String templateContent = assembleTemplateInfo(null != templateAttributeEntity ? templateAttributeEntity.getTemplateId() : TemplateConstant.DEFAULT_TEMPLATE_ID);


        // 3、数据对象->map
        Map<String, Object> dataMap = BeanUtil.beanToMap(signTemplateDTO);
        String bizContent = "";
        String errorMessage = "";
        // 4、渲染模板
        try {
            bizContent = TemplateRenderUtil.templateRender(dataMap, templateContent);

            System.out.println("========================================");
            System.out.println("========================================");
            System.out.println(bizContent);
        } catch (Exception e) {
            System.out.println("*********************************");
            bizContent = templateContent;
            errorMessage = e.getMessage();
        }

        Integer cid = contractSignEntity.getContractId();
        String contractCode = contractSignEntity.getContractCode();
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + cid + "_" + contractCode + "_" + DateTimeUtil.formatDateTimeValue() + ".html")
                .setHtmlContent(bizContent);

        html2PdfUtils.genPdfAndImage(pdfInfoDTO);

        HtmlInfoDTO pdfInfoDTO2 = new HtmlInfoDTO()
                .setHtmlUrl("D:\\TestTemplate\\" + cid + "_" + contractCode + "_" + DateTimeUtil.formatDateTimeValue() + "_000.html")
                .setHtmlContent(printSignInfo(signTemplateDTO, contractSignEntity));

        html2PdfUtils.genPdfAndImage(pdfInfoDTO2);
    }


    public String printSignInfo(SignTemplateDTO dto, ContractSignEntity signDTO) {

        StringBuilder sb = new StringBuilder();

        sb.append("<br>");
        sb.append("合同编号：").append(signDTO.getContractCode()).append("<br>");
        sb.append("处理类型：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getTradeType())).append("<br>");
        sb.append("TTID：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getTtId())).append("<br>");
        sb.append("合同ID：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getContractId())).append("<br>");

        sb.append("<br>");
        sb.append("<table style=\"border-width: 1px; border-style: solid;cellspacing:0;cellpadding:0 \">\n <tbody>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("协议信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("协议编号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XYB").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXyb()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("销售合同/订单").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XHD").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXhd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("条形码").append("</td>").append("<td width=\"255\" valign=\"top\">").append("TXM").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getTxm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("二维码").append("</td>").append("<td width=\"255\" valign=\"top\">").append("EWM").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getEwm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("采购合同/订单").append("</td>").append("<td width=\"255\" valign=\"top\">").append("CHD").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getChd()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("合同编号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("NO").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getNo()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("合同类型").append("</td>").append("<td width=\"255\" valign=\"top\">").append("TY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getTy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("签约日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DOC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDoc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("吨数").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MT").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("TT修改日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("TTXR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getTtxr()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-基差信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("期货合约").append("</td>").append("<td width=\"255\" valign=\"top\">").append("HY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getHy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("期货合约").append("</td>").append("<td width=\"255\" valign=\"top\">").append("HYJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getHyj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("基差价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JCJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJcj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("点价截止日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DJJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDjj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("点价后补缴比例").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DMR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDmr()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同量信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("合同未定价量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("WDJL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getWdjl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("未开单量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("wkdl").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getWkdl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("合同剩余未开单量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("Xwkdl").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXwkdl()).append("</td>").append("</tr>\n");


        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-买卖主体").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方主体").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ME").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMe()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方主体所在地址简称").append("</td>").append("<td width=\"255\" valign=\"top\">").append("AD").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getAd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方地址").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MADS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMads()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方收件人").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MFOX").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMfox()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方电子邮箱").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MEMA").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMema()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("卖方电话号码").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MMBO").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMmbo()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("开户行").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KH").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("银行账号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZH").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("银行账号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZHFK").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZhfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("开户行").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KHFK").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKhfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("买方主体").append("</td>").append("<td width=\"255\" valign=\"top\">").append("NA").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getNa()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("买方收件人").append("</td>").append("<td width=\"255\" valign=\"top\">").append("FOX").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getFox()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("买方地址").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ADS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getAds()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("买方电子邮箱").append("</td>").append("<td width=\"255\" valign=\"top\">").append("EMA").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getEma()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("买方电话号码").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MBO").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMbo()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-客户信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("框架合同号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KJH").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKjh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("框架合同签约日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KJR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKjr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("账期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MES").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMes()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-商品信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("品种").append("</td>").append("<td width=\"255\" valign=\"top\">").append("VR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getVr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("蛋白含量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("EG").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getEg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("交货工厂").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JHGC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJhgc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("溢短装").append("</td>").append("<td width=\"255\" valign=\"top\">").append("OS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getOs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("重量验收").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PE").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPe()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("包装计算重量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("AG").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getAg()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-交提货").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("交提货方式").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DG").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("目的港").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("交货周期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PO").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPo()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-付款信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("合同付款方式").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PM").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("保证金比例").append("</td>").append("<td width=\"255\" valign=\"top\">").append("MR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getMr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("保证金追加比例").append("</td>").append("<td width=\"255\" valign=\"top\">").append("BZJZJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getBzjzj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("付款截止日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JZFK").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJzfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("含税单价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("含税总金额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PRT").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPrt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("不含税总金额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("NPRT").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getNprt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("增值税总金额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("SZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getSz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("含税单价明细").append("</td>").append("<td width=\"255\" valign=\"top\">").append("PRX").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getPrx()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("手续费").append("</td>").append("<td width=\"255\" valign=\"top\">").append("SXF").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getSxf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("含税单价中的运费").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YF").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("赊销利息").append("</td>").append("<td width=\"255\" valign=\"top\">").append("SXLX").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getSxlx()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("客诉折价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KSZJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKszj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("客诉折价总金额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KSZJZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKszjz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("滞期费").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZQF").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZqf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("滞期费总金额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZQFZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZqfz()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-交货工厂").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("交货地点").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DD").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("地址").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("国标/企标值").append("</td>").append("<td width=\"255\" valign=\"top\">").append("GQBZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getGqbz()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-点价定价").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("定价单TT数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("HTDJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getHtdj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("点价日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DJS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDjs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("定价单价格").append("</td>").append("<td width=\"255\" valign=\"top\">").append("DJJG").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getDjjg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("加权平均价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JQPJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJqpj()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-转月").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原期货合约").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YHY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYhy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("转月价差").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZYJC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZyjc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("转月数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("ZYSL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getZysl()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-解约定赔").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("解约定赔数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XDSL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXdsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("解约定赔市场价格").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XDSCJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXdscj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("解约定赔差价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XDCJ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXdcj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("解约定赔差价总额").append("</td>").append("<td width=\"255\" valign=\"top\">").append("xdze").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXdze()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-结构化定价").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价总数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHZSL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghzsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价开始日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHKRQ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghkrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价结束日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHJRQ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghjrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价敲出价格").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHQC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghqc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价增强价格").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHZQ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghzq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("结构化定价单位量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("JGHDWL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getJghdwl()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-源合同").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("父合同编号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("NOY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getNoy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("CFSL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getCfsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同剩余数量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("SYSL").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getSysl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("修改前含税单价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("XPR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getXpr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原包装计算重量").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YAG").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYag()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同签约日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YDOC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYdoc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同交货工厂").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YJHGC").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYjhgc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同溢短装").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YOS").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYos()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同含税单价").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YPR").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYpr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("原合同含税单价中的运费").append("</td>").append("<td width=\"255\" valign=\"top\">").append("YYF").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getYyf()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("其他").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("赊销日期").append("</td>").append("<td width=\"255\" valign=\"top\">").append("SXRQ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getSxrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("投诉单号").append("</td>").append("<td width=\"255\" valign=\"top\">").append("TSDH").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getTsdh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("投诉原因").append("</td>").append("<td width=\"255\" valign=\"top\">").append("TSYY").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getTsyy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("客户盖章").append("</td>").append("<td width=\"255\" valign=\"top\">").append("KGZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getKgz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"255\" valign=\"top\">").append("ldc盖章").append("</td>").append("<td width=\"255\" valign=\"top\">").append("LGZ").append("</td>").append("<td width=\"255\" valign=\"top\">").append(dto.getLgz()).append("</td>").append("</tr>\n");

        sb.append("</tbody>\n</table>");

        return sb.toString();

    }

    public String printSignInfo(SignTemplateDTO dto, SignTemplateDTO dto2) {

        StringBuilder sb = new StringBuilder();

        sb.append("<br>");
        sb.append("合同编号：").append(dto.getNo()).append("<br>");
/*
        sb.append("处理类型：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getTradeType())).append("<br>");
        sb.append("TTID：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getTtId())).append("<br>");
        sb.append("合同ID：").append(ContractTradeTypeEnum.getDescByValue(signDTO.getContractId())).append("<br>");
*/
        sb.append("<br>");
        sb.append("<table border=\"1\" cellspacing=\"0\" style=\"width:100%;table-layout:fixed; word-wrap: break-word;\">\n <tbody>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("协议信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("协议编号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XYB").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXyb()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXyb()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("销售合同/订单").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XHD").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXhd()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXhd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("条形码").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("TXM").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getTxm()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getTxm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("二维码").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("EWM").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getEwm()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getEwm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("采购合同/订单").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("CHD").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getChd()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getChd()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("合同编号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("NO").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getNo()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getNo()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("合同类型").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("TY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getTy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getTy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("签约日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DOC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDoc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDoc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("吨数").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MT").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMt()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("TT修改日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("TTXR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getTtxr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getTtxr()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-基差信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("期货合约").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("HY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getHy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getHy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("期货合约").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("HYJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getHyj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getHyj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("基差价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JCJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJcj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJcj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("点价截止日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DJJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDjj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDjj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("点价后补缴比例").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DMR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDmr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDmr()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同量信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("合同未定价量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("WDJL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getWdjl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getWdjl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("未开单量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("wkdl").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getWkdl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getWkdl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("合同剩余未开单量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("Xwkdl").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXwkdl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXwkdl()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-买卖主体").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方主体").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ME").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMe()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMe()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方主体所在地址简称").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("AD").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getAd()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getAd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方地址").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MADS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMads()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMads()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方收件人").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MFOX").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMfox()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMfox()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方电子邮箱").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MEMA").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMema()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMema()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("卖方电话号码").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MMBO").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMmbo()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMmbo()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("开户行").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KH").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKh()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("银行账号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZH").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZh()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("银行账号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZHFK").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZhfk()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZhfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("开户行").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KHFK").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKhfk()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKhfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("买方主体").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("NA").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getNa()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getNa()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("买方收件人").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("FOX").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getFox()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getFox()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("买方地址").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ADS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getAds()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getAds()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("买方电子邮箱").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("EMA").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getEma()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getEma()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("买方电话号码").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MBO").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMbo()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMbo()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-客户信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("框架合同号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KJH").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKjh()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKjh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("框架合同签约日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KJR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKjr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKjr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("账期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MES").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMes()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMes()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-商品信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("品种").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("VR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getVr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getVr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("蛋白含量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("EG").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getEg()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getEg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("交货工厂").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JHGC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJhgc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJhgc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("溢短装").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("OS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getOs()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getOs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("重量验收").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PE").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPe()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPe()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("包装计算重量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("AG").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getAg()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getAg()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-交提货").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("交提货方式").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DG").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDg()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("目的港").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("交货周期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PO").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPo()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPo()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-付款信息").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("合同付款方式").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PM").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPm()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPm()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("保证金比例").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("MR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getMr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getMr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("保证金追加比例").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("BZJZJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getBzjzj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getBzjzj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("付款截止日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JZFK").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJzfk()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJzfk()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("含税单价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("含税总金额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PRT").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPrt()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPrt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("不含税总金额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("NPRT").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getNprt()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getNprt()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("增值税总金额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("SZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getSz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getSz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("含税单价明细").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("PRX").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getPrx()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getPrx()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("手续费").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("SXF").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getSxf()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getSxf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("含税单价中的运费").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YF").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYf()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("赊销利息").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("SXLX").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getSxlx()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getSxlx()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("客诉折价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KSZJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKszj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKszj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("客诉折价总金额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KSZJZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKszjz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKszjz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("滞期费").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZQF").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZqf()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZqf()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("滞期费总金额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZQFZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZqfz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZqfz()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-交货工厂").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("交货地点").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DD").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDd()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDd()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("地址").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDs()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("国标/企标值").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("GQBZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getGqbz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getGqbz()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-点价定价").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("定价单TT数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("HTDJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getHtdj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getHtdj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("点价日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DJS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDjs()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDjs()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("定价单价格").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("DJJG").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getDjjg()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getDjjg()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("加权平均价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JQPJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJqpj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJqpj()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"3\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-转月").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原期货合约").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YHY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYhy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYhy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("转月价差").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZYJC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZyjc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZyjc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("转月数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("ZYSL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getZysl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getZysl()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-解约定赔").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("解约定赔数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XDSL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXdsl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXdsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("解约定赔市场价格").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XDSCJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXdscj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXdscj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("解约定赔差价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XDCJ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXdcj()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXdcj()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("解约定赔差价总额").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("xdze").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXdze()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXdze()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-结构化定价").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价总数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHZSL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghzsl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghzsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价开始日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHKRQ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghkrq()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghkrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价结束日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHJRQ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghjrq()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghjrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价敲出价格").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHQC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghqc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghqc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价增强价格").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHZQ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghzq()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghzq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("结构化定价单位量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("JGHDWL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getJghdwl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getJghdwl()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("合同-源合同").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("父合同编号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("NOY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getNoy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getNoy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("CFSL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getCfsl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getCfsl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同剩余数量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("SYSL").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getSysl()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getSysl()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("修改前含税单价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("XPR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getXpr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getXpr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原包装计算重量").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YAG").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYag()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYag()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同签约日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YDOC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYdoc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYdoc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同交货工厂").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YJHGC").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYjhgc()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYjhgc()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同溢短装").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YOS").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYos()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYos()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同含税单价").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YPR").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYpr()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYpr()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("原合同含税单价中的运费").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("YYF").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getYyf()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getYyf()).append("</td>").append("</tr>\n");

        sb.append("<tr><td valign=\"top\" rowspan=\"1\" colspan=\"4\"style=\"word-break: break-all;\"><span style=\"color: rgb(255, 0, 0);\">")
                .append("其他").append("</td></tr>");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("赊销日期").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("SXRQ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getSxrq()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getSxrq()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("投诉单号").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("TSDH").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getTsdh()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getTsdh()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("投诉原因").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("TSYY").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getTsyy()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getTsyy()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("客户盖章").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("KGZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getKgz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getKgz()).append("</td>").append("</tr>\n");
        sb.append("<tr>").append("<td width=\"15%\" valign=\"top\">").append("ldc盖章").append("</td>").append("<td width=\"15%\" valign=\"top\">").append("LGZ").append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto.getLgz()).append("</td>").append("<td width=\"35%\" valign=\"top\">").append(dto2.getLgz()).append("</td>").append("</tr>\n");


        sb.append("</tbody>\n</table>");

        return sb.toString();
    }

}
