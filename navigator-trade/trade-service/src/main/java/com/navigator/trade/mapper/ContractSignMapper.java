package com.navigator.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 合同前置协议表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface ContractSignMapper extends BaseMapper<ContractSignEntity> {


    @Select("<script>" +
            "SELECT " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as 'waitBack'," +
            "SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as 'waitConfirm'," +
            "SUM(CASE WHEN status = 6 OR status = 7 THEN 1 ELSE 0 END) as 'processing'," +
            "SUM(CASE WHEN status = 15 THEN 1 ELSE 0 END) as 'canceled' " +
            "FROM dbt_contract_sign WHERE is_deleted = 0 " +
            "AND sales_type = #{signBO.salesType} AND category2 = #{signBO.goodsCategoryId} " +
            "<when test='signBO.customerId != null'> and customer_id = #{signBO.customerId} </when>"+
            "<when test='signBO.supplierId != null'> and supplier_id = #{signBO.supplierId} </when>"+
            "</script>")
    ContractSignAllStatusNumVO getColumbusContractSignStat(@Param("signBO") QueryContractSignBO signBO);


    @Select("<script>" +
            "SELECT " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as 'waitProvide'," +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as 'waitReview'," +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as 'waitStamp'," +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as 'waitBack'," +
            "SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as 'waitConfirm'," +
            "SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as 'originalPaper'," +
            "SUM(CASE WHEN status = 7 THEN 1 ELSE 0 END) as 'processing'," +
            "SUM(CASE WHEN status = 9 THEN 1 ELSE 0 END) as 'abnormal'," +
            "SUM(CASE WHEN status = 15 THEN 1 ELSE 0 END) as 'invalid' " +
            "FROM dbt_contract_sign WHERE is_deleted = 0 " +
            "AND sales_type = #{signBO.salesType} AND category2 = #{signBO.goodsCategoryId} " +
            "AND ldc_frame = #{signBO.ldcFrame} " +
            "AND site_code IN " +
            "<foreach item=\"siteCode\" collection=\"signBO.siteCodeList\" open=\"(\" separator=\",\" close=\")\">\n" +
            " #{siteCode} " +
            "</foreach>"+
//            "AND belong_customer_id IN " +
//            "<foreach item=\"customerId\" collection=\"signBO.customerIdList\" open=\"(\" separator=\",\" close=\")\">\n" +
//            " #{customerId} " +
//            "</foreach>"+
            "</script>")
    ContractSignAllStatusNumVO getMagellanContractSignStat(@Param("signBO") QueryContractSignBO signBO);

}
