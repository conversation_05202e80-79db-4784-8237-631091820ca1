package com.navigator.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.dao.TtPriceDao;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.service.ITtPriceService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 定价单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class TtPriceServiceImpl implements ITtPriceService {

    @Autowired
    private TtPriceDao ttPriceDao;

    @Override
    public boolean saveTtPrice(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.save(ttPriceEntity);
    }

    @Override
    public List<TTPriceEntity> getTtPrice(String contractCode) {
        return ttPriceDao.list(new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getContractCode, contractCode)
                //.eq(TTPriceEntity::getContraryStatus, ContraryStatusEnum.NOT_CONTRARY.getDescription())
        );
    }

    @Override
    public TTPriceEntity getById(Integer id) {
        return ttPriceDao.getById(id);
    }

    @Override
    public int updateTtPrice(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.updateById(ttPriceEntity) ? 1 : 0;
    }

    @Override
    public List<TTPriceEntity> getConfirmPriceList(Integer contractId) {
        return ttPriceDao.getConfirmPriceList(contractId);
    }

    @Override
    public List<ConfirmPriceDTO> getTTPriceByContractId(Integer contractId){
        List<TTPriceEntity> confirmPriceList = ttPriceDao.getTTPriceByContractId(contractId);
        return BeanConvertUtils.mapAsList(ConfirmPriceDTO.class, confirmPriceList);
    }

    @Override
    public List<TTPriceEntity> getPriceByContractNotId(Integer contractId, Integer id) {
        return ttPriceDao.getPriceByContractNotId(contractId, id);
    }

    @Override
    public TTPriceEntity getTTPriceEntityByTTId(Integer ttId) {
        return ttPriceDao.getTTPriceEntityByTTId(ttId);
    }

    @Override
    public List<ConfirmPriceDTO> getContractPricingList(Integer contractId) {
        List<TTPriceEntity> confirmPriceList = getConfirmPriceList(contractId);
        return BeanConvertUtils.mapAsList(ConfirmPriceDTO.class, confirmPriceList);
    }

    @Override
    public ConfirmPriceDTO getConfirmPricedInfo(Integer contractId, Integer ttPriceId) {
        TTPriceEntity ttPriceEntity = ttPriceDao.getConfirmPriceInfo(contractId, ttPriceId);
        return BeanConvertUtils.map(ConfirmPriceDTO.class, ttPriceEntity);
    }

    @Override
    public TTPriceEntity getByContractIdAndPrice(Integer contractId, BigDecimal price) {
        List<TTPriceEntity> priceEntityList = ttPriceDao.list(new LambdaQueryWrapper<TTPriceEntity>()
                .eq(TTPriceEntity::getContractId, contractId)
                .eq(TTPriceEntity::getPrice, price));

        return CollectionUtils.isNotEmpty(priceEntityList) ? priceEntityList.get(0) : null;
    }

    @Override
    public List<TTPriceEntity> getTTPriceByApplyId(Integer priceApplyId) {
        return ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getPriceApplyId, priceApplyId)
        );
    }

    @Override
    public TTPriceEntity getTTPriceByAllocateId(Integer allocateId) {
        List<TTPriceEntity> ttPriceEntities = ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getAllocateId, allocateId)
        );

        return ttPriceEntities.isEmpty() ? null : ttPriceEntities.get(0);
    }

    @Override
    public List<TTPriceEntity> getTTPriceBySourceId(Integer sourceId) {
        return ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getSourceId, sourceId)
                        .ne(TTPriceEntity::getSourceId, 0)
        );
    }

    @Override
    public Boolean updateTTPriceById(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.updateById(ttPriceEntity);
    }
}
