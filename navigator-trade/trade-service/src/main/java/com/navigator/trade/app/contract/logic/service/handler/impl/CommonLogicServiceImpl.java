package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.dto.CancelledWarrantDTO;
import com.navigator.koala.pojo.dto.UpdateWarrantNumDTO;
import com.navigator.koala.pojo.dto.WarrantDTO;
import com.navigator.koala.pojo.entity.CancellationRelationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.koala.pojo.enums.WarrantSourceEnum;
import com.navigator.koala.pojo.enums.WarrantStatusEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.SyncSwitchEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.sync.AtlasSyncService;
import com.navigator.trade.service.sync.LkgSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.navigator.common.constant.BizConstant.*;

;

/**
 * 合同域通用逻辑处理 日志/数据转化/业务配置等
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class CommonLogicServiceImpl implements CommonLogicService {


    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private WarrantFacade warrantFacade;
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    private AtlasSyncService atlasSyncService;
    @Autowired
    private LkgSyncService lkgSyncService;
    @Autowired
    private EmployFacade employFacade;

    @Override
    public void addContractOperationLog(ContractEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractEntity.getId())
                .setTargetRecordId(contractEntity.getId())
                .setReferBizCode(contractEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setData(data)
                .setTriggerSys(SystemEnum.getByValue(systemEnum).getDescription());
        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                    .setSource(OperationSourceEnum.SYSTEM.getValue())
                    .setOperatorId(1);
        }

        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    @Override
    @Async
    public void addContractOperationLog(OperationDetailDTO operationDetailDTO, Integer systemEnum) {
        operationDetailDTO
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());

        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                    .setSource(OperationSourceEnum.SYSTEM.getValue())
                    .setOperatorId(1);
        }

        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    /**
     * 更新客户信息
     *
     * @param newCustomerId    新客户ID
     * @param newSupplierId    新供应商ID
     * @param originCustomerId 旧客户ID
     * @param originSupplierId 旧供应商ID
     * @param contractEntity   合同实体
     */
    @Override
    public void updateCustomerInfo(Integer originCustomerId, Integer originSupplierId,
                                   Integer newCustomerId, Integer newSupplierId, ContractEntity contractEntity) {
        // 默认父合同客户信息
        contractEntity.setCustomerId(originCustomerId);
        contractEntity.setSupplierId(originSupplierId);
        // 更新Customer
        if (null != newCustomerId) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(newCustomerId);
            contractEntity.setCustomerId(customerDTO.getId())
                    .setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName())
                    .setCustomerStatus(customerDTO.getStatus());
        }
        // 更新Supplier
        if (null != newSupplierId) {
            CustomerDTO supplierDTO = customerFacade.getCustomerById(newSupplierId);
            contractEntity.setSupplierId(supplierDTO.getId())
                    .setSupplierAccount(supplierDTO.getBankAccountNo())
                    .setSupplierName(supplierDTO.getName());
        }
    }

    /**
     * 判断客户是否可用
     *
     * @param
     * @return
     */
    public boolean isEnableCustomerStatus(ContractEntity contractEntity) {
        int customerId = contractEntity.getCustomerId();
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            customerId = contractEntity.getSupplierId();
        }
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

        return null != customerDTO && DisableStatusEnum.ENABLE.getValue().equals(customerDTO.getStatus());
    }


    /**
     * 获取转月次数
     *
     * @param customerId
     * @param category2
     * @param category3
     * @return
     */
    @Override
    public ContractTransferCountDTO getContractTransferCount(Integer customerId, Integer category2, Integer category3) {
        // 转月次数限制
        int transferTimes = NORMAL_CUSTOMER_TRANSFER_COUNT;
        // 反点价次数限制
        int reversePriceTimes = NORMAL_CUSTOMER_REVERSE_COUNT;

        ContractTransferCountDTO contractTransferCountDTO = new ContractTransferCountDTO();

        // 判断是否是白名单用户（4次转月机会，1次反点价机会，可额外申请次数）
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO.setCustomerId(customerId)
                .setCategory2(String.valueOf(category2))
                .setCategory3(String.valueOf(category3))
        ;
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);
        if (!customerDetailEntityList.isEmpty()) {
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            if (null != customerDetailEntity) {
                // 先判断是否开通转月权限
                if (customerDetailEntity.getIsWhiteList() == 1) {
                    transferTimes = VIP_CUSTOMER_TRANSFER_COUNT;
                }
                // 再判断是否开通反点价权限
                if (customerDetailEntity.getIsReversePrice() == 1) {
                    reversePriceTimes = VIP_CUSTOMER_REVERSE_COUNT;
                }
            }
        }

        return contractTransferCountDTO
                .setAbleTransferTimes(transferTimes)
                .setAbleReversePriceTimes(reversePriceTimes)
                .setTotalReversePriceTimes(reversePriceTimes)
                .setTotalTransferTimes(transferTimes);
    }

    /**
     * 同步合同根据账套区分同步到Atlas 还是 lkg
     *
     * @param contractEntity 合同实体
     * @param ttId           ttId
     * @param syncType       同步类型
     * @param syncSwitchEnum 是否同步到atlas
     */
    @Override
    public void syncContractInfo(ContractEntity contractEntity, Integer ttId, Integer syncType, SyncSwitchEnum syncSwitchEnum) {
        log.info("syncContractInfo start, contractEntity:{}, ttId:{}, syncType:{}, syncAtlas:{}", contractEntity, ttId, syncType, syncSwitchEnum);

        // 获取账套信息
        SiteEntity siteEntity = tradeDomainRemoteService.getSiteEntity(contractEntity.getSiteCode());
        if (siteEntity == null) {
            log.error("Failed to get SiteEntity for contractEntity: {}", contractEntity);
            return;
        }

        // 获取同步系统类型
        String syncSystem = siteEntity.getSyncSystem();

        // 如果是仓单合同直接同步到 Atlas
        if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
            syncSystem = SyncSystemEnum.ATLAS.getValue();
        }

        // 根据 SyncSwitchEnum 和 syncSystem 决定同步的系统
        if (Arrays.asList(SyncSwitchEnum.LKG, SyncSwitchEnum.BOTH).contains(syncSwitchEnum) && SyncSystemEnum.LINKINAGE.getValue().equals(syncSystem)) {
            lkgSyncService.syncContractInfo(contractEntity, ttId, syncType);
        }

        if (Arrays.asList(SyncSwitchEnum.ATLAS, SyncSwitchEnum.BOTH).contains(syncSwitchEnum) && SyncSystemEnum.ATLAS.getValue().equals(syncSystem)) {
            atlasSyncService.syncContractInfo(contractEntity, ttId, syncType, getOperatorName());
        }
    }

    @Override
    public void syncTTPriceInfo(String siteCode, Integer ttId, Integer syncType) {
        // 获取账套信息
        SiteEntity siteEntity = tradeDomainRemoteService.getSiteEntity(siteCode);
        // 如果账套配置是LKG
        if (SyncSystemEnum.LINKINAGE.getValue().equals(siteEntity.getSyncSystem())) {
            lkgSyncService.syncTTPriceInfo(ttId, syncType);
        }
        // 如果状态是Atlas
        if (SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem())) {
            atlasSyncService.syncTTPriceInfo(ttId, getOperatorName());
        }
    }

    /**
     * 获取操作员名称
     */
    private String getOperatorName() {
        EmployEntity employ = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
        return (employ != null) ? employ.getName() : "";
    }

    @Override
    public void syncContractPriceUpdateInfo(Integer ttId, ContractEntity contractEntity) {
        // 获取账套信息
        SiteEntity siteEntity = tradeDomainRemoteService.getSiteEntity(contractEntity.getSiteCode());
        // 如果账套配置是LKG
        if (SyncSystemEnum.LINKINAGE.getValue().equals(siteEntity.getSyncSystem())) {
            lkgSyncService.syncContractPriceUpdateInfo(ttId, contractEntity);
        }
    }

    @Override
    public ContractUnitPriceVO calcContractUnitPrice(PriceDetailBO priceDetailBO, BigDecimal taxRate) {
        ContractUnitPriceVO contractUnitPriceVO = new ContractUnitPriceVO();
        // 含税单价
        BigDecimal unitPrice = contractDomainService.calculatePriceDetailBO(priceDetailBO);
        // 运输费
        BigDecimal deliveryPrice = BigDecimal.ZERO.add(priceDetailBO.getTransportPrice() != null ? priceDetailBO.getTransportPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getLiftingPrice() != null ? priceDetailBO.getLiftingPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getDelayPrice() != null ? priceDetailBO.getDelayPrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getTemperaturePrice() != null ? priceDetailBO.getTemperaturePrice() : BigDecimal.ZERO)
                .add(priceDetailBO.getOtherDeliveryPrice() != null ? priceDetailBO.getOtherDeliveryPrice() : BigDecimal.ZERO);

        // fob含税单价
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        // cif含税单价
        if (null != taxRate) {
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
            contractUnitPriceVO.setCifUnitPrice(cifUnitPrice);
        }
        return contractUnitPriceVO
                .setUnitPrice(unitPrice)
                .setFobUnitPrice(fobUnitPrice);
    }

    @Override
    public void closeContractStructureTT(ContractEntity contractEntity) {
        ContractStructureEntity contractStructureEntity = contractQueryDomainService.getContractStructureById(contractEntity.getId());
        //ttLogicService.updateTTStatusById(contractStructureEntity.getTtId(), TTStatusEnum.INVALID);
    }

    @Override
    public Result<BigDecimal> getContractOpenQuantity(ContractEntity contractEntity) {
        return tradeDomainRemoteService.getContractOpenQuantity(contractEntity);
    }

    @Override
    public boolean getNotAllocateByContractId(Integer contractId) {
        return tradeDomainRemoteService.getNotAllocateByContractId(contractId);
    }

    @Override
    public WarrantEntity createPurchaseWarrant(ContractEntity contractEntity, Integer warrantCategory, BigDecimal deliveryMarginAmount) {
        // 1.创建仓单
        WarrantEntity warrantEntity = new WarrantEntity();
        // TODO 待构建属性
        BeanUtils.copyProperties(contractEntity, warrantEntity);
        warrantEntity.setSource(WarrantSourceEnum.PURCHASE_CONTRACT.getValue());
        warrantEntity.setWarehouseName(contractEntity.getShipWarehouseValue());
        warrantEntity.setRegisteCount(contractEntity.getContractNum());
        warrantEntity.setCategory1(String.valueOf(contractEntity.getCategory1()));
        warrantEntity.setCategory2(String.valueOf(contractEntity.getCategory2()));
        warrantEntity.setDepositPaymentType(contractEntity.getPaymentType());
        warrantEntity.setRegisteId(contractEntity.getCompanyId());
        warrantEntity.setStatus(WarrantStatusEnum.EFFECTIVE.getValue());
        warrantEntity.setRegisteName(contractEntity.getCompanyName());
        warrantEntity.setDepositAmount(deliveryMarginAmount);
        warrantEntity.setCategory(warrantCategory);
        warrantEntity.setId(null);
        Result result = warrantFacade.submitWarrant(warrantEntity);
        warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
        // 2.更新合同
        if (ObjectUtil.isNotEmpty(warrantEntity.getCode())) {
            contractEntity.setWarrantCode(warrantEntity.getCode());
            contractEntity.setWarrantId(warrantEntity.getId());
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
            contractDomainService.updateContractById(contractEntity);

        }
        return warrantEntity;
    }

    @Override
    public void updateWarrantNum(ContractEntity contractEntity, Integer modifyType, BigDecimal changeWarrantCount, Integer warrantCancellationId) {
        UpdateWarrantNumDTO updateWarrantNumDTO = new UpdateWarrantNumDTO();
        // TODO 待构建属性
        updateWarrantNumDTO
                .setModifyType(modifyType)
                .setChangeWarrantCount(changeWarrantCount)
                .setWarrantCode(contractEntity.getWarrantCode())
                .setContractCode(contractEntity.getContractCode())
                .setContractId(contractEntity.getId())
                .setWarrantCancellationId(warrantCancellationId)
                .setSalesType(contractEntity.getSalesType())
        ;

        warrantFacade.updateWarrantNum(updateWarrantNumDTO);
    }

    @Override
    public void updateWarranStatus(String warrantCode) {
        WarrantDTO warrantDTO = new WarrantDTO();
        warrantDTO.setCode(warrantCode);
        warrantFacade.updateWarrantStatus(warrantDTO);
    }

    @Override
    public void cancelledWarrantContract(ContractEntity warrant,
                                         List<ContractEntity> cargoRights,
                                         List<ContractEntity> deliverys,
                                         List<ContractEntity> trades,
                                         ContractEntity purchase,
                                         CancelledWarrantDTO cancelledWarrantDTO) {
        // 做到参数去传
        // CancelledWarrantDTO cancelledWarrantDTO = new CancelledWarrantDTO();
        cancelledWarrantDTO.setModifyType(WarrantModifyTypeEnum.CONTRACT_CANCEL.getValue());
        cancelledWarrantDTO.setContractId(warrant.getId());
        cancelledWarrantDTO.setWarrantCode(warrant.getWarrantCode());
        cancelledWarrantDTO.setContractId(warrant.getId());
        cancelledWarrantDTO.setContractCode(warrant.getContractCode());
        cancelledWarrantDTO.setSalesType(warrant.getSalesType());

        List<CancellationRelationEntity> cancellationRelationEntities = new ArrayList<>();
        // 提货权合同
        if (ObjectUtil.isNotEmpty(cargoRights) && cargoRights.size() > 0) {
            for (ContractEntity cargoRight : cargoRights) {
                CancellationRelationEntity cancellationRelationEntity = new CancellationRelationEntity();
                cancellationRelationEntity
                        .setReferContractId(cargoRight.getId())
                        .setReferContractCode(cargoRight.getContractCode())
                        .setSalesType(cargoRight.getSalesType())
                        .setContractNature(cargoRight.getContractNature());
                cancellationRelationEntities.add(cancellationRelationEntity);
                // 都是同一个客户
                cancelledWarrantDTO.setDeliveryCustomerId(cargoRight.getCustomerId());
                cancelledWarrantDTO.setDeliveryCustomerCode(cargoRight.getCustomerCode());
                cancelledWarrantDTO.setDeliveryCustomerName(cargoRight.getCustomerName());
            }
        }
        // 提货合同
        if (ObjectUtil.isNotEmpty(deliverys) && deliverys.size() > 0) {

            for (ContractEntity delivery : deliverys) {
                CancellationRelationEntity cancellationRelationEntity = new CancellationRelationEntity();
                cancellationRelationEntity
                        .setReferContractId(delivery.getId())
                        .setReferContractCode(delivery.getContractCode())
                        .setSalesType(delivery.getSalesType())
                        .setContractNature(delivery.getContractNature());
                cancellationRelationEntities.add(cancellationRelationEntity);
                // 都是同一个客户
                cancelledWarrantDTO.setDeliveryCustomerId(delivery.getCustomerId());
                cancelledWarrantDTO.setDeliveryCustomerCode(delivery.getCustomerCode());
                cancelledWarrantDTO.setDeliveryCustomerName(delivery.getCustomerName());
            }

        }
        // 贸易合同
        if (ObjectUtil.isNotEmpty(trades) && trades.size() > 0) {
            for (ContractEntity trade : trades) {
                CancellationRelationEntity cancellationRelationEntity = new CancellationRelationEntity();
                cancellationRelationEntity
                        .setReferContractId(trade.getId())
                        .setReferContractCode(trade.getContractCode())
                        .setSalesType(trade.getSalesType())
                        .setContractNature(trade.getContractNature());
                cancellationRelationEntities.add(cancellationRelationEntity);
            }

        }
        // 采购合同
        if (ObjectUtil.isNotEmpty(purchase)) {
            CancellationRelationEntity cancellationRelationEntity = new CancellationRelationEntity();
            cancellationRelationEntity
                    .setReferContractId(purchase.getId())
                    .setReferContractCode(purchase.getContractCode())
                    .setSalesType(purchase.getSalesType())
                    .setContractNature(purchase.getContractNature())
            ;
            cancellationRelationEntities.add(cancellationRelationEntity);
        }
        cancelledWarrantDTO.setCustomerId(warrant.getCustomerId());
        cancelledWarrantDTO.setCustomerCode(warrant.getCustomerCode());
        cancelledWarrantDTO.setCustomerName(warrant.getCustomerName());
        cancelledWarrantDTO.setCancellationRelationEntities(cancellationRelationEntities);
        warrantFacade.cancelledWarrantContract(cancelledWarrantDTO);
    }

}
