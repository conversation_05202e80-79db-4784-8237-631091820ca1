package com.navigator.trade.app.contract.domain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.ContractMdmInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractRelativeDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.ConfirmPriceVO;
import com.navigator.trade.pojo.vo.ContractDeliveryVO;

import java.util.List;

/**
 * <p>
 * 合同表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface ContractQueryDomainService {

    //包含合同历史，合同结构化，合同价格（TT）

    /**
     * 新增合同生成合同编号【抽离到公共的方法去】
     *
     * @param salesName       买/卖方主体简称
     * @param salesType       销售合同类型
     * @param goodsCategoryId 商品品类
     * @return
     */
    String genNewContractCode(String salesName, Integer salesType, Integer goodsCategoryId);

    /**
     * 新增结构化定价生成合同编号
     *
     * @return
     */
    String genStructureContractCode(String companyName);

    /**
     * 生成子合同编号
     *
     * @param contractCode 父合同编号
     * @param salesType    销售合同类型
     * @return
     */
    String genSonContractCode(String contractCode, Integer salesType);

    /**
     * 生成子提货权合同编号
     *
     * @param contractCode 父合同编号
     * @param salesType    销售合同类型
     * @return
     */
    String genCargoRightsContractCode(String contractCode, Integer salesType);

    /**
     * 根据条件获取合同|查询仓单合同|现货合同|采销类型
     *
     * @param queryDTO
     * @return
     */
    Result queryContract(QueryDTO<ContractQO> queryDTO);

    /**
     * 根据合同id查询基本合同信息(不调用lkg)
     *
     * @param id
     * @return
     */
    ContractEntity getBasicContractById(Integer id);

    /**
     * 根据合同编码获取合同
     *
     * @param code
     * @return
     */
    ContractEntity getBasicContractByCode(String code);

    /**
     * 根据合同编码获取合同-存在多个
     *
     * @param code
     * @return
     */
    List<ContractEntity> getByContractCode(String code);

    /**
     * 查询LKG接口迁移到Logic层 - 废弃
     * @param id
     * @return ContractDetailInfoDTO getContractDetailInfoDTO(Integer id);
     */

    /**
     * 根据合同Id查询合同明细
     *
     * @param contractId
     * @return
     */
    ContractEntity getContractDetailById(String contractId);

    /**
     * 根据合同Code查询合同明细
     *
     * @param contractCode
     * @return
     */
    List<ContractEntity> getContractDetailByCode(String contractCode);

    /**
     * 根据合同Code查询合同Id
     *
     * @param contractCode
     * @return
     */
    Integer getContractIdByCode(String contractCode);

    /**
     * 根据合同Code查询合同信息
     *
     * @param contractCode
     * @return
     */
    ContractEntity getContractByCode(String contractCode);

    /**
     * 根据仓单编码获取合同列表
     *
     * @param warrantCode
     * @return
     */
    List<ContractEntity> getContractByWarrantCode(String warrantCode);

    /**
     * 根据合同Id获取合同含税单价详情
     *
     * @param contractId
     * @return
     */
    Result getContractUnitPriceDetail(String contractId);


    /**
     * 查询客户的期货合约数据
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO);


    /**
     * 分组查询期货合约
     *
     * @param contractFuturesDTO
     * @return
     */
    List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO);


    /**
     * 根据合约月查询合同列表信息
     *
     * @param queryContractDTO
     * @return
     */
    List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO);

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    IPage<ContractEntity> futureContracts(QueryDTO<ContractFuturesDTO> queryDTO);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return ConfirmPriceVO
     */
    List<ConfirmPriceVO> getConfirmPriceList(Integer contractId);

    /**
     * 根据条件查询合同
     *
     * @param customerId       客户id
     * @param goodsCategoryId  商品品类id
     * @param domainCode       主力合约
     * @param contractTypeList 合同类型
     * @return
     */
    List<ContractEntity> getContractList(Integer customerId, Integer goodsCategoryId, String domainCode, List<Integer> contractTypeList);

    List<ContractEntity> getContractList(Integer customerId, List<Integer> category2List, List<Integer> category3List, List<Integer> contractStatusList, List<Integer> contractTypeList);

    List<ContractEntity> getContractList(List<String> contractCodeList, String startDateTime, String endDateTime);

    /**
     * 获取每日合同数据
     *
     * @param salesType     销售类型
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return
     */
    List<ContractEntity> getDailyContractList(Integer salesType, String startDateTime, String endDateTime);

    /**
     * 根据父合同id获取合同信息
     *
     * @param pid
     * @return
     */
    List<ContractEntity> getContractByPid(Integer pid);

    /**
     * 根据合同ID获取列表信息
     *
     * @param contractIdList
     * @return
     */
    List<ContractEntity> getContractListByIds(List<Integer> contractIdList);

    List<ContractRelativeDTO> getContractTraceList(Integer contractId);

    /**
     * 获取结构化信息
     *
     * @param contractId
     * @return
     */
    ContractStructureEntity getContractStructure(Integer contractId);

    /**
     * 仓单合同获取提货列表信息
     *
     * @param contractId
     * @return
     */
    List<ContractDeliveryVO> getDeliveryContractByContractId(Integer contractId);

    /**
     * 查询结构化合同
     *
     * @param queryDTO
     * @return
     */
    IPage<ContractStructureEntity> queryContractStructure(QueryDTO<ContractBO> queryDTO);

    /**
     * 根据编码查询合同信息
     *
     * @param queryDTO
     * @return
     */
    IPage<ContractEntity> queryContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO);

    /**
     * 获取权益变更数据List
     *
     * @param contractId
     * @return
     */
    List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId);

    /**
     * 查询合同结构化数据
     *
     * @param contractIds
     * @return
     */
    List<ContractStructureEntity> queryContractStructureList(List<Integer> contractIds);

    /**
     * 回去解约索赔LIST
     *
     * @param contractId
     * @return
     */
    List<ContractEntity> getWashOutList(Integer contractId);

    /**
     * 查询结构化定价信息
     *
     * @param verifyContractStructureNumDTO
     * @return
     */
    List<ContractStructureEntity> verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO);

    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @param companyId
     * @return
     */
    IPage<ContractEntity> queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO, Integer companyId);

    /**
     * 查询获取提货申请数据
     *
     * @param deliveryApplyContractQO
     * @param cannotDeliveryGoodsIdList
     * @param cannotDeliveryFactoryList
     * @param cannotDeliveryTypeIdList
     * @return
     */
    List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO,
                                                      List<Integer> cannotDeliveryGoodsIdList,
                                                      List<String> cannotDeliveryFactoryList,
                                                      List<Integer> cannotDeliveryTypeIdList);

    /**
     * 获取提货申请合同数据分组
     *
     * @param deliveryApplyContractQO
     * @param cannotDeliveryGoodsIdList
     * @param cannotDeliveryFactoryList
     * @param cannotDeliveryTypeIdList
     * @return
     */
    List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO,
                                                       List<Integer> cannotDeliveryGoodsIdList,
                                                       List<String> cannotDeliveryFactoryList,
                                                       List<Integer> cannotDeliveryTypeIdList);


    /**
     * 根据合同ID获取结构化舌痛
     *
     * @param contractId
     * @return
     */
    ContractStructureEntity getContractStructureById(Integer contractId);

    /**
     * 根据合同ID获取合同价格
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    ContractPriceEntity getContractPriceByContractId(Integer contractId);

    /**
     * 根据仓单合同ID获取提货权合同数据
     *
     * @param contractId
     * @return
     */
    List<ContractEntity> getCargoRightsContractById(Integer contractId);

    List<String> judgeCategory3InProcessContractForSite(List<Integer> category3List, String siteCode);

    /**
     * 获取结构化合同
     *
     * @param contractCode
     * @return
     */
    List<ContractStructureEntity> getByStructureContractCode(String contractCode);

    /**
     * 根据合同ID获取合同历史信息
     *
     * @param contractId  合同ID
     * @param mainVersion 主版本号
     * @return
     */
    ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion);

    /**
     * 根据合同编码获取合同MDM信息
     *
     * @param contractCode 合同编码
     * @return 合同MDM信息
     */
    ContractMdmInfoDTO getContractMdmInfo(String contractCode);

    List<ContractEntity> getContractByStatus(String status, List<String> contractNumbers);

    /**
     * 根据采购合同查询关联的销售合同
     * @param purchaseContract
     * @return
     */
    List<ContractEntity> getContractByPurchase(ContractEntity purchaseContract);
}
