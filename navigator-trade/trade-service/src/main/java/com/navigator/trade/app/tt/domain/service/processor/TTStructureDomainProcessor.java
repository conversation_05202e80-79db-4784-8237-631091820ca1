package com.navigator.trade.app.tt.domain.service.processor;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTStructureEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("STRUCTURE")
public class TTStructureDomainProcessor extends AbstractTTDomainProcessor {

    @Autowired
    TtStructureDao ttStructureDao;

    public TTStructureDomainProcessor() {
        System.out.println("TTStructureDomainProcessor");

    }

    @Override
    void addTTSubEntity(TradeTicketDO tradeTicketDO) {

        //set value
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(tradeTicketDO.getTradeTicketEntity().getId());

        TTStructureEntity StructureEntity = (TTStructureEntity) ttSubEntity;

        TTStructureEntity oldStructureEntity = ttStructureDao.getByTTId(StructureEntity.getTtId());
        if (Objects.nonNull(oldStructureEntity)) {
            StructureEntity.setId(oldStructureEntity.getId());
        }
        ttStructureDao.save(StructureEntity);
    }

    @Override
    boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {
        int rtn = ttStructureDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        return rtn > 0;
    }
}
