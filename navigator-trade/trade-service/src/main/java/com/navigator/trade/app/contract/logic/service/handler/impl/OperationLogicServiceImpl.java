package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.OperationLogicService;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtAddService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同基本操作的业务逻辑实现，合同补充，合同结构化定价，合同修改等
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
public class OperationLogicServiceImpl implements OperationLogicService {

    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected CategoryFacade categoryFacade;
    @Autowired
    protected IContractValueObjectService contractValueObjectService;
    @Autowired
    protected ITtAddService ttAddService;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private TTHandler ttHandler;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private DeliveryApplyFacade deliveryApplyFacade;
    @Autowired
    private RedisUtil redisUtil;
    /**
     * 合同域Domain 以及其他动作的交互
     */
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private CommonLogicService commonLogicService;
    @Autowired
    private ContractQueryLogicService contractQueryLogicService;

    @Override
    public boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO) {
        // 校验合同
        ContractEntity contractEntity = checkFixedContractInfo(confirmPriceDTO);

        // 更新contractPrice  TODO TT域提供查询价格和更新价格接口
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        // 判断品类 副产品：ve含量*Ve价格；其他：定价价格
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory3());
        contractPriceEntity.setId(null);
        PriceDetailBO priceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
        ContractUnitPriceVO contractUnitPriceVO;
        if (categoryEntity != null && Arrays.asList("脂肪酸", "豆油脂肪酸", "特油脂肪酸").contains(categoryEntity.getName())) {
            priceDetailBO.setVePrice(confirmPriceDTO.getVePrice()).setVeContent(confirmPriceDTO.getVeContent());
            // 计算含税单价
             contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());
            // 定价价格
            confirmPriceDTO.setConfirmPrice(confirmPriceDTO.getVePrice().multiply(confirmPriceDTO.getVeContent()));
//            confirmPriceDTO.setConfirmPrice(contractUnitPriceVO.getUnitPrice());
        } else {
            // 定价价格
            if (confirmPriceDTO.getConfirmPrice() == null) {
                throw new BusinessException(ResultCodeEnum.CONFIRMED_PRICE_FAILED);
            }
            priceDetailBO.setForwardPrice(confirmPriceDTO.getConfirmPrice());
            contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());
        }
//        contractPriceService.updatePriceByContractId(contractPriceEntity);

        // 生成tt和协议
        TTDTO ttdto = new TTDTO();
        SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
        // 处理变更的数据
        BeanUtils.copyProperties(contractEntity, salesContractTTPriceDTO);
        // 生成定价单
        salesContractTTPriceDTO.setContractId(contractEntity.getId());
        salesContractTTPriceDTO.setSourceContractId(contractEntity.getId());
        salesContractTTPriceDTO.setUserId(String.valueOf(contractEntity.getCreatedBy()));
        salesContractTTPriceDTO.setType(PriceTypeEnum.CONFIRM_PRICED.getValue());
        salesContractTTPriceDTO.setNum(contractEntity.getContractNum());
        salesContractTTPriceDTO.setDiffPrice(contractEntity.getExtraPrice());
        salesContractTTPriceDTO.setTempPrice(contractEntity.getTemporaryPrice());
        //定价价格（脂肪酸：新VE单价*新VE含量）
        salesContractTTPriceDTO.setPrice(confirmPriceDTO.getConfirmPrice());
        //最终合同含税单价
        salesContractTTPriceDTO.setUnitPrice(contractUnitPriceVO.getUnitPrice());
        salesContractTTPriceDTO.setTransactionPrice(confirmPriceDTO.getConfirmPrice());
        salesContractTTPriceDTO.setSupplierId(contractEntity.getSupplierId());
        salesContractTTPriceDTO.setRemainPriceNum(BigDecimal.ZERO);
        salesContractTTPriceDTO.setTotalPriceNum(contractEntity.getContractNum());
        salesContractTTPriceDTO.setThisContractNum(contractEntity.getContractNum());
        salesContractTTPriceDTO.setOriginalPriceNum(contractEntity.getContractNum());
        salesContractTTPriceDTO.setPriceEndType(contractEntity.getPriceEndType());
        salesContractTTPriceDTO.setPriceEndTime(contractEntity.getPriceEndTime());
        salesContractTTPriceDTO.setDomainCode(contractEntity.getDomainCode());
        salesContractTTPriceDTO.setOwnerId(contractEntity.getOwnerId());
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

        //获取TT处理类
        String processorType = TTHandlerUtil.getTTProcessorByTradeType(contractEntity.getSalesType(),
                ContractTradeTypeEnum.FIXED.getValue(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(processorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(processorType);

        Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);

        int ttId;
        String ttCode;
        try {
            ttId = listResult.getData().get(0).getTtId();
            ttCode = listResult.getData().get(0).getCode();
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.CONFIRMED_PRICE_FAILED);
        }
        //fixed by nana:暂定价定价生成新价格
//        contractPriceEntity.setId(null)
//                .setTtId(ttId)
//                .setTtCode(ttCode);
//        contractPriceService.saveOrUpdate(contractPriceEntity);
//        PriceDetailBO priceDetailBO = new PriceDetailBO();
//        BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);

        // 计算含税单价
//        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 更新合同
        contractDomainService.updateContractById(contractEntity
                .setTotalPriceNum(contractEntity.getContractNum())
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE,
                        contractEntity.getContractNum(), contractUnitPriceVO.getUnitPrice()))
        );

        // 同步ATLAS
        // commonLogicService.syncContractInfo(contractEntity, ttId, LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> invalidContract(ContractBaseDTO contractBaseDTO) {
        // TODO 改造做编排-需要抽出来处理
        // 校验合同
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractBaseDTO.getContractId());
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 处理TT
        List<TTQueryVO> ttQueryVOS = operateTradeTicket(contractEntity);
        // 更新状态
        contractValueObjectService.updateContractById(contractEntity
                .setStatus(ContractStatusEnum.MODIFYING.getValue()));
        // 记录操作日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        return ttQueryVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidContractByTT(ContractBaseDTO contractBaseDTO) {

        // 校验合同
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractBaseDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 更新状态
        contractDomainService.updateContractById(contractEntity
                .setStatus(ContractStatusEnum.INVALID.getValue())
                .setInvalidReason(contractBaseDTO.getInvalidReason()));
        // 记录操作日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fillContract(ContractBaseDTO contractBaseDTO) {
        log.info("合同补充信息" + FastJsonUtils.getBeanToJson(contractBaseDTO) + CollectionUtils.isEmpty(contractBaseDTO.getTagConfigIdList()));
        // 校验合同
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractBaseDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 记录操作日志
        if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
            commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.FILL_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        } else {
            commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.FILL_PURCHASE_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        }
        // 更新状态
        return contractDomainService.updateContractById(contractEntity
                .setCustomerContractCode(contractBaseDTO.getCustomerContractCode())
                .setIsStf(contractBaseDTO.getStf() ? 1 : 0)
                .setTagConfigIds(CollectionUtils.isEmpty(contractBaseDTO.getTagConfigIdList()) ? "" :
                        contractBaseDTO.getTagConfigIdList().stream().map(String::valueOf).collect(Collectors.joining(",")))
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContractByApproved(ContractModifyDTO contractModifyDTO) {
        // TODO 不走此方法
        if (true) {
            return;
        }
    }
//  =====================================私有方法=====================================================

    /**
     * 暂定价定价校验合同
     *
     * @param confirmPriceDTO 定价信息
     * @return
     */
    protected ContractEntity checkFixedContractInfo(ConfirmPriceDTO confirmPriceDTO) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(confirmPriceDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }

        // 合同类型为暂定价
        if (!contractEntity.getContractType().equals(ContractTypeEnum.ZAN_DING_JIA.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_PRICE);
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

        // 是否全部定价
        if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
            throw new BusinessException(ResultCodeEnum.CONFIRMED_ALREADY_PRICE);
        }

        return contractEntity;
    }

    /**
     * 变更主体作废生成tt
     *
     * @param contractEntity
     */
    private List<TTQueryVO> operateTradeTicket(ContractEntity contractEntity) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        // 变更主体作废合同需要生成tt
        List<ContractEntity> sonContractList = contractQueryDomainService.getContractByPid(contractEntity.getId());
        List<ContractEntity> entityList = sonContractList.stream()
                .filter(sonContract -> (sonContract.getContractSource().equals(ContractActionEnum.REVISE_CUSTOMER.getActionValue()) ||
                        sonContract.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())) &&
                        BigDecimalUtil.isEqual(sonContract.getOrderNum(), contractEntity.getOrderNum()))
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(entityList)) {
            TTDTO ttdto = new TTDTO();

            // 处理数据
            SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
            salesContractAddTTDTO.setContractId(contractEntity.getId());
            salesContractAddTTDTO.setSourceContractId(contractEntity.getId());

            // 价格处理
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);

            ttdto.setPriceDetailBO(priceDetailBO);

            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

            String ttProcessorType = TTHandlerUtil.getTTProcessor(
                    contractEntity.getSalesType(),
                    TTTypeEnum.INVALID.getType(),
                    contractEntity.getGoodsCategoryId());
            ttdto.setProcessorType(ttProcessorType);
            ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
            Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);
            ttQueryVOS = listResult.getData();
            ttQueryVOS.forEach(queryVO -> queryVO.setSourceFlag(1));
        }
        return ttQueryVOS;
    }

    @Override
    public Boolean closeTailNumByContractId(Integer contractId, Integer triggerSys) {

        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractId);
        // 校验合同是否可以尾量关闭
        checkContractClose(contractEntity, triggerSys);
        // 尾量关闭
        contractDomainService.updateContractById(contractEntity
                .setCloseTailNum(contractEntity.getContractNum().subtract(contractEntity.getTotalDeliveryNum()))
                .setContractCloseType(ContractCloseTypeEnum.TAIL_CLOSE.getCode())
                .setUpdatedAt(new Date())
        );
        // MGL关闭尾量-->CLB不可操作关闭尾量
        if (triggerSys.equals(SystemEnum.COLUMBUS.getValue())) {
            String closeStatus = RedisConstants.CONTRACT_CLOSE_TAIL_TIMES + contractEntity.getContractCode();
            redisUtil.set(closeStatus, "1");
        }

        // 同步ATLAS
        commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.CLOSE.getSyncType(), SyncSwitchEnum.ATLAS);

        // 记录合同关闭尾量日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.CLOSE_TAIL_NUM, "", triggerSys);
        return true;
    }

    /**
     * 校验合同是否可以尾量关闭
     *
     * @param contractEntity 合同信息
     */
    private void checkContractClose(ContractEntity contractEntity, Integer triggerSys) {

        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        boolean closeFlag = true;
        StringBuilder cannotCloseReason = new StringBuilder();
        // 合同关闭尾量状态为是
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            closeFlag = false;
            cannotCloseReason.append("合同关闭尾量状态为是").append(";");
        }
        // 未提货量小于等于30t并且不超过溢短装范围（合同的溢短装比例*合同数量）内
        BigDecimal notDeliveryNum = contractEntity.getContractNum().subtract(contractEntity.getTotalDeliveryNum());
//        BigDecimal weightToleranceNum = BigDecimalUtil.multiply(CalcTypeEnum.COUNT,
//                new BigDecimal("0.01").multiply(BigDecimal.valueOf(contractEntity.getWeightTolerance())),
//                contractEntity.getContractNum());
        if (BigDecimalUtil.isGreater(notDeliveryNum, new BigDecimal(30))) {
            closeFlag = false;
            cannotCloseReason.append("未提货量超出 30t").append(";");
        }
//        if (BigDecimalUtil.isGreater(notDeliveryNum, weightToleranceNum)) {
//            closeFlag = false;
//            cannotCloseReason.append("未提货量超过溢短装范围").append(";");
//        }
        // 合同状态为生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            closeFlag = false;
            cannotCloseReason.append("合同状态为").append(ContractStatusEnum.getDescByValue(contractEntity.getStatus())).append(";");
        }
        // 总数量>0
        if (BigDecimalUtil.isLessEqualThanZero(contractEntity.getContractNum())) {
            closeFlag = false;
            cannotCloseReason.append("总数量为0").append(";");
        }
        // 没有正在待审核的提货委托分配
        if (deliveryApplyFacade.isContractInDeliveryAudit(contractEntity.getId()).getData()) {
            closeFlag = false;
            cannotCloseReason.append("合同存在待审核的提货委托分配").append(";");
        }
        // 哥伦布一个合同仅可关闭一次
        if (triggerSys.equals(SystemEnum.COLUMBUS.getValue())) {
            // redis中是否存在该合同的关闭记录
            String closeStatus = RedisConstants.CONTRACT_CLOSE_TAIL_TIMES + contractEntity.getContractCode();
            if (redisUtil.get(closeStatus) != null && redisUtil.get(closeStatus).equals("1")) {
                closeFlag = false;
                cannotCloseReason.append("合同只允许关闭一次").append(";");
            }
        }
        if (!closeFlag) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_CLOSE_TAIL_NUM, cannotCloseReason.toString());
        }
    }

    @Override
    public String batchCloseTailNum(List<Integer> contractIds, Integer triggerSys) {
        // 批量提交 - 统计成功与失败的数量
        int successCount = 0;
        int failCount = 0;
        StringBuilder result = new StringBuilder();
        StringBuilder successResult = new StringBuilder();
        StringBuilder failureResult = new StringBuilder();

        for (Integer contractId : contractIds) {
            try {
                closeTailNumByContractId(contractId, triggerSys);
                successResult.append(contractQueryDomainService.getBasicContractById(contractId).getContractCode()).append(" ");
                successCount++;
            } catch (Exception e) {
                e.printStackTrace();
                failCount++;
                ContractEntity contract = contractQueryDomainService.getBasicContractById(contractId);
                if (contract == null) {
                    failureResult.append("合同ID:").append(contractId).append("不存在").append(System.lineSeparator());
                    continue;
                }
                failureResult.append("合同").append(contract.getContractCode()).append(e.getMessage()).append(System.lineSeparator());
            }
        }
        result.append("已完成批量提交").append(contractIds.size()).append("条，结果如下:").append(System.lineSeparator());
        result.append("共提交").append(contractIds.size()).append("条，成功").append(successCount).append("条，失败").append(failCount).append("条").append(System.lineSeparator());
        if (successCount > 0) {
            result.append("合同").append(successResult).append("操作成功").append(System.lineSeparator());
        }
        if (failCount > 0) {
            result.append(failureResult);
        }
        return result.toString();
    }


    @Override
    public Boolean cancelCloseTailNumByContractId(Integer contractId) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractId);
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        // 未关闭尾量
        if (BigDecimalUtil.isLessEqualThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_CLOSE_TAIL_NUM2);
        }
        // v1 去掉关闭类型
        contractDomainService.updateContractById(contractEntity
                .setCloseTailNum(BigDecimal.ZERO)
                .setContractCloseType(ContractCloseTypeEnum.TAIL_CLOSE.getCode())
                .setUpdatedAt(new Date())
        );
        // v1 去掉关闭类型

        // 同步ATLAS
        commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.CLOSE.getSyncType(), SyncSwitchEnum.ATLAS);

        // 记录合同取消关闭尾量日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.CANCEL_CLOSE_TAIL_NUM, "", SystemEnum.MAGELLAN.getValue());
        return true;
    }

    @Override
    public String batchCancelCloseTailNum(List<Integer> contractIds) {
        // 批量提交 - 统计成功与失败的数量
        int successCount = 0;
        int failCount = 0;

        StringBuilder result = new StringBuilder();
        StringBuilder successResult = new StringBuilder();
        StringBuilder failureResult = new StringBuilder();

        for (Integer contractId : contractIds) {
            try {
                cancelCloseTailNumByContractId(contractId);
                successResult.append(contractQueryDomainService.getBasicContractById(contractId).getContractCode()).append(" ");
                successCount++;
            } catch (Exception e) {
                e.printStackTrace();

                failCount++;
                ContractEntity contract = contractQueryDomainService.getBasicContractById(contractId);
                if (contract == null) {
                    failureResult.append("合同ID:").append(contractId).append("不存在").append(System.lineSeparator());
                    continue;
                }
                failureResult.append("合同").append(contract.getContractCode()).append("不可操作，").append(e.getMessage()).append(System.lineSeparator());
            }
        }

        result.append("已完成批量提交").append(contractIds.size()).append("条，结果如下:").append(System.lineSeparator());
        result.append("共提交").append(contractIds.size()).append("条，成功").append(successCount).append("条，失败").append(failCount).append("条").append(System.lineSeparator());
        if (successCount > 0) {
            result.append("合同").append(successResult).append("取消关闭尾量成功").append(System.lineSeparator());
        }
        if (failCount > 0) {
            result.append(failureResult);
        }

        return result.toString();

    }

    @Override
    public List<OperationDetailEntity> getCloseTailNumRecord(String contractCode) {

        Result result = operationLogFacade.queryOperationDetailByReferBizCode(contractCode, OperationSourceEnum.EMPLOYEE.getValue());
        if (result.isSuccess()) {
            List<OperationDetailEntity> operationDetailList = JSON.parseArray(JSON.toJSONString(result.getData()), OperationDetailEntity.class);
            return operationDetailList.stream().filter(operationDetailEntity ->
                            operationDetailEntity.getBizCode().equals(LogBizCodeEnum.CLOSE_TAIL_NUM.getBizCode()) ||
                                    operationDetailEntity.getBizCode().equals(LogBizCodeEnum.CANCEL_CLOSE_TAIL_NUM.getBizCode()))
                    .collect(Collectors.toList());
        }

        return null;
    }


    /**
     * 根据账套ID和合同代码关闭合同。
     *
     * @param siteId       账套ID，用于确定要关闭的合同所属的站点。
     * @param contractCode 合同代码，用于唯一标识要关闭的合同。
     * @return 返回一个布尔值，如果合同成功关闭，则返回true；如果合同不存在或关闭失败，则返回false。
     */
    @Override
    public Boolean closeContractBySiteIdAndContractCode(Integer siteId, String contractCode) {
        // TODO 没有套账ID了，需要优化处理
        // return contractDao.closeContractBySiteIdAndContractCode(siteId, contractCode);
        return false;
    }

}
