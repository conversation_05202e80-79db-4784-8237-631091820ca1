package com.navigator.trade.app.contract.logic.service;

import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同业务逻辑处理Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface ContractLogicService {

    /**
     * 现货采购合同创建/现货销售合同，仓单销售合同创建
     *
     * @param contractCreateDTO
     * @return
     */
    ContractEntity createContract(ContractCreateDTO contractCreateDTO);

    /**
     * 创建结构化合同
     *
     * @param tradeTicketEntity
     * @param ttdto
     * @return
     */
    ContractEntity createStructureContract(TradeTicketEntity tradeTicketEntity, TTDTO ttdto);

    /**
     * 校验合同信息-修改校验
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void modifyContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 变更创建合同 - [变更主体]创建子合同
     *
     * @param contractModifyDTO
     * @return
     */
    ContractEntity modifyContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 变更创建合同 - 定价完成
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void modifyPriceComplete(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 变更创建合同 - 修改处理父合同信息
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void modifyFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 校验拆分合同信息-修改校验
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void splitContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 拆分创建合同 - 创建子合同
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO
     * @return
     */
    ContractEntity splitContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 拆分创建合同 - 处理父合同信息
     *
     * @param contractEntity
     * @param contractModifyDTO
     */
    void splitFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, List<TTPriceEntity> ttPriceEntityList);

    /**
     * 解约索赔校验合同信息
     *
     * @param contractWashOutDTO 解约定赔dto
     */
    void washOutContractCheck(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO);

    /**
     * 解约索赔处理父合同信息
     *
     * @param contractEntity     合同实体
     * @param contractWashOutDTO 解约索赔
     */
    void washOutContract(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO);

    /**
     * 回购合同校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 合同实体
     */
    void buyBackContractCheck(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 销售合同回购
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 合同实体
     */
    void buyBackContract(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 关闭合同校验合同信息
     *
     * @param contractEntity 合同实体
     */
    void closeContractCheck(ContractEntity contractEntity);

    /**
     * 关闭合同
     *
     * @param contractEntity 父合同
     */
    void closeContract(ContractEntity contractEntity);

    /**
     * 校验合同注销
     *
     * @param contractEntity      合同实体
     * @param contractWriteOffDTO 注销dto
     */
    void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO);


    /**
     * （非豆二场景一，不修改货品）创建子合同 （货权合同即可）（不可见）
     * 注销不修改提货方且不修改其他字段；
     * 注销修改提货方且不修改其他字段；
     *
     * @param contractEntity
     * @param contractWriteOffDTO 注销仓单数据信息
     * @return
     */
    ContractEntity createCargoRights(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO);

    /**
     * 场景：
     * 注销不修改提货方且修改其他字段
     * 创建提货合同
     *
     * @param contractEntity
     * @param contractWriteOffDTO
     */
    ContractEntity createDeliveryContract(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO, int tradeType);

    /**
     * 场景：注销同时修改货品、修改提货方；
     *
     * @param contractEntity
     * @param contractWriteOffDTO
     * @return
     */
    ContractEntity createPurchaseContract(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO);

    /**
     * 豆二合同注销校验
     *
     * @param contractEntity
     * @param contractWriteOffOMDTO
     */
    void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO);

    /**
     * 场景：
     * 豆二生成提货合同 豆油合同A，豆粕合同B
     * 创建提货合同
     *
     * @param contractEntity
     * @param contractWriteOffOMDTO
     * @param contractNatrue        生成的合同类型
     */
    List<ContractEntity> createOMContract(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO, Integer contractNatrue);

    /**
     * 注销撤回
     *
     * @param contractEntity              仓单合同
     * @param contractWriteOffWithDrawDTO 注销撤回DTO
     */
    void writeOffWithDrawCheck(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * 退回仓单及仓单合同更新的数量信息|以及作废合同|调用Atlas
     *
     * @param contractEntity
     * @param contractWriteOffWithDrawDTO
     */
    void writeOffWithDraw(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * 仓单合同作废
     *
     * @param contractEntity
     */
    void warrantContractInvalid(ContractEntity contractEntity);


    /**
     * 仓单合同关闭
     *
     * @param contractCode
     */
    ContractEntity warrantContractClose(String contractCode);

    /**
     * 确认合规后取消合同操作
     *
     * @param contractModifyDTO
     */
    void cancelContractModify(ContractModifyDTO contractModifyDTO);

    /**
     * 确认合规后合同操作
     *
     * @param contractModifyDTO
     */
    ContractConfirmResultDTO confirmContractModify(ContractModifyDTO contractModifyDTO);

    /**
     * 补充合同信息
     *
     * @param contractBaseDTO
     * @return
     */
    boolean fillContract(ContractBaseDTO contractBaseDTO);

    /**
     * 暂定价合同定价（生成定价单）
     *
     * @param confirmPriceDTO
     * @return
     */
    boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO);

    /**
     * 根据合同id关闭尾数
     *
     * @param contractId 合同id
     * @param triggerSys 触发系统
     * @return
     */
    Boolean closeTailNumByContractId(Integer contractId, Integer triggerSys);

    /**
     * 批量关闭尾数
     *
     * @param contractIds 合同id集合
     * @param triggerSys  触发系统
     * @return
     */
    String batchCloseTailNum(List<Integer> contractIds, Integer triggerSys);

    /**
     * 取消尾量关闭
     *
     * @param contractId 合同id
     * @return
     */
    Boolean cancelCloseTailNumByContractId(Integer contractId);

    /**
     * 批量取消关闭尾数
     *
     * @param contractIds 合同id集合
     * @return
     */
    String batchCancelCloseTailNum(List<Integer> contractIds);

    /**
     * 结构化合同关闭-TT进行作废处理
     *
     * @param contractEntity
     */
    void closeContractStructureTT(ContractEntity contractEntity);

    /**
     * 更新合同信息
     *
     * @param contractEntity
     * @return
     */
    boolean updateContract(ContractEntity contractEntity);

    /**
     * 转月合同校验合同信息
     *
     * @param contractEntity
     * @param contractTransferDTO
     */
    void transferMonthCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO);

    /**
     * 转月/反点价合同创建
     *
     * @param contractEntity      原合同实体
     * @param contractTransferDTO 转月/反点价dto
     * @param arrangeContext      上下文信息
     * @return
     */
    ContractEntity createTransferContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext);

    /**
     * 更新转月/反点价合同父合同信息
     *
     * @param contractEntity
     * @param contractTransferDTO
     * @param ttPriceEntityList
     */
    void updateTransferFatherContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, List<TTPriceEntity> ttPriceEntityList);


    /**
     * 反点价合同校验合同信息
     *
     * @param contractEntity
     * @param contractTransferDTO
     */
    void reversePriceCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO);

    /**
     * 更新点价合同父合同信息
     *
     * @param newTotalPriceNum  新定价数量
     * @param contractEntity    合同实体
     * @param ttPriceEntityList 定价单列表
     */
    void updatePriceFactorContract(BigDecimal newTotalPriceNum, ContractEntity contractEntity, List<TTPriceEntity> ttPriceEntityList);

    /**
     * 回购生效需要更新合同的状态
     *
     * @param contractId
     * @param tradeTicketEntity
     */
    void updateContractStatus(Integer contractId, TradeTicketEntity tradeTicketEntity);

    /**
     * 回购TT作废更新合同的状态和回购量
     *
     * @param contractId
     * @param buyBackNum
     */
    void updateBuyBackContract(Integer contractId, BigDecimal buyBackNum);

    /**
     * TT 除非作废合同动作
     *
     * @param contractBaseDTO
     */
    void invalidContractByTT(ContractBaseDTO contractBaseDTO);

    /**
     * 处理转月合同的定价
     *
     * @param contractEntity
     */
    void processTransferMonthPrice(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext);
}
