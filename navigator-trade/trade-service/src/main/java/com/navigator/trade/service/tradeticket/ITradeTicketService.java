package com.navigator.trade.service.tradeticket;

import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.vo.SubmitTTVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

public interface ITradeTicketService {
    /**
     * @param ttdto
     * @return
     * @description: 保存TT
     */
    Result<List<TTQueryVO>> saveTT(TTDTO ttdto);

    /**
     * @param ttdto
     * @description: 修改TT
     * @return:
     */
    Result<List<TTQueryVO>> updateTT(TTDTO ttdto);

    /**
     * @param submitTTDTO
     * @description: 提交TT
     * @return:
     */
    Result<List<TTQueryVO>> submitTT(SubmitTTDTO submitTTDTO);

    ContractSignEntity createContractSign(ContractSignCreateDTO contractSignCreateDTO, Integer ttId) ;

    /**
     * @param submitTTBatchDTO
     * @return SubmitTTVO
     * @description: 批量提交TT
     */
    SubmitTTVO submitTTBatch(SubmitTTBatchDTO submitTTBatchDTO);

    /**
     * @param approvalDTO
     * @param ttType
     * @description: TT审批
     * @return:
     */
    void approveTT(ApprovalDTO approvalDTO, Integer ttType);

    /**
     * @param ttId
     * @return
     * @description: 删除TT
     */
    void deleteTT(Integer ttId);

    /**
     * @param operateTTDTO
     * @return
     * @description: TT撤回
     */
    void cancelTT(OperateTTDTO operateTTDTO);

    /**
     * @param operateTTDTO
     * @return
     * @description: TT作废
     */
    void invalidTT(OperateTTDTO operateTTDTO);

    TTDetailVO queryTTDetail(Integer ttId, TradeTicketEntity tradeTicketEntity);

    SignTemplateDTO getSignTemplateDTO(Integer ttId);

    /**
     * 更新tt中的合同 Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    int updateContractId(Integer ttId, Integer contractId);


    /**
     * 确认合规完成TT及合同
     *
     * @param
     * @param
     * @return
     */
    Result complete(Integer contractId,Integer ttId);

    void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType);

    RecordBizOperationDTO startTTApprove(Integer ttId, TTDTO ttDto, ContractPriceEntity contractPriceEntity);

    /**
     * 转月/反点价/点价/撤回
     *
     * @param contraryPriceDTO
     * @return
     */
    Result contraryPrice(ContraryPriceDTO contraryPriceDTO);

//    Result submitModifyTT(SubmitTTDTO submitTTDTO);
}
