package com.navigator.trade.app.contract.logic.service.handler;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffWithDrawDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractWriteOffStatusEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 */
public interface WriteOffLogicService {


    /**
     * 校验合同注销
     *
     * @param contractEntity     合同实体
     * @param contractWriteOffDTO 解约定赔dto
     */
    void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO);

    /**
     * 修改补充补充基本信息-创建子合同
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    void buildBaseInfo(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity, boolean isPurchase);

    /**
     * 处理变更的业务信息
     * 修改货品，交割库（库点），交货方式，目的港，注销说明备注，赊销账期，交货周期，短装溢，所属商务，履约保证金
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    void buildBizInfo(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity);

    /**
     * 构建采购合同的业务信息
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    void buildBizInfoPurchase(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity);

    /**
     * 创建合同
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    void createContract(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity);

    /**
     * 处理仓单销售合同信息
     *
     * @param contractWriteOffDTO 注销信息
     * @param contractEntity    仓单合同
     */
    void operateFatherContract(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity);

    /**
     * 注销合同撤回校验
     * @param contractEntity
     * @param contractWriteOffWithDrawDTO
     */
    void writeOffWithDrawCheck(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * 注销撤回
     * 退回仓单及仓单合同更新的数量信息|以及作废合同|调用Atlas
     * @param contractEntity
     * @param contractWriteOffWithDrawDTO
     */
    void writeOffWithDraw(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);
}
