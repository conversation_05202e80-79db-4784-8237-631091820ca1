package com.navigator.trade.service.tradeticket.impl.purchase;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.tradeticket.OperateTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static com.navigator.admin.pojo.enums.LogBizCodeEnum.SUBMIT_PURCHASE_TT;

@Slf4j
@Component("SBM_P_TT_ADD,SBO_P_TT_ADD")
public class PurchaseTTAddServiceImpl extends BaseTradeTicketAbstractService {
    public PurchaseTTAddServiceImpl() {
        ttTypeEnum = TTTypeEnum.NEW;
        contractTradeTypeEnum = ContractTradeTypeEnum.NEW;
        contractSource = ContractActionEnum.NEW.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.NEW.getType();
        salesType = ContractSalesTypeEnum.PURCHASE;
        processorType = ProcessorTypeEnum.SBM_P_ADD.getTtValue();
        logBizCodeEnum = SUBMIT_PURCHASE_TT;

    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_P_ADD.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) throws Exception {
        saveTTAdd(ttDto, ttId);
        if (ttDto.getProcessorType().contains("BUYBACK") && ttDto.getSalesContractAddTTDTO().getAddedSignatureType() == -1) {
            return;
        }
        tradeTicketConvertUtil.saveAddContractPrice(ttDto, ttId);
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        //初始化交易、销售类型、合同来源
        salesContractAddTTDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesContractAddTTDTO.setSalesType(salesType.getValue());
        salesContractAddTTDTO.setContractSource(contractSource);
        salesContractAddTTDTO.setStatus(status);

        //协议签署状态
        salesContractAddTTDTO.setContractSignatureStatus(contractSignatureStatus);

        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);
        String code = salesContractAddTTDTO.getCode();
        //根据code存在与否, 判断是否为新增
        if (StringUtils.isBlank(code)) {
            salesContractAddTTDTO.setCreateStatus(true);
            //生成TT编号
            code = CodeGeneratorUtil.genPurchaseTTNewCode();
            //salesContractAddTTDTO.setCode(code);
            salesContractAddTTDTO.setCode(code);
            //获取供应商信息,生成合同编号
            /*CustomerDTO customerDTO = null;
            if (null != salesContractAddTTDTO.getSupplierId()) {
                customerDTO = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
            }
            if (customerDTO != null) {
                CompanyEntity companyEntity = companyFacade.queryCompanyById(customerDTO.getCompanyId());
                String contractCode = contractService.genNewContractCode(companyEntity.getShortName(),
                        ContractSalesTypeEnum.PURCHASE.getValue(), salesContractAddTTDTO.getGoodsCategoryId());
                salesContractAddTTDTO.setContractCode(contractCode);
            }*/
            if (StringUtils.isNotBlank(salesContractAddTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractAddTTDTO.getSiteCode());
                String contractCode = contractService.genNewContractCode(siteEntity.getCompanyCode(),
                        ContractSalesTypeEnum.PURCHASE.getValue(), salesContractAddTTDTO.getGoodsCategoryId());
                salesContractAddTTDTO.setContractCode(contractCode);
            }
        } else {
            salesContractAddTTDTO.setCreateStatus(false);

            //设置ttId
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(salesContractAddTTDTO.getCode());
            if (tradeTicketEntity == null) {
                throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
            }
            Integer ttId = tradeTicketEntity.getId();
            salesContractAddTTDTO.setTtId(ttId);
        }
        salesContractAddTTDTO.setType(ttTypeEnum.getType());
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        return ttdto;
    }

    public TTAddEntity saveTTAdd(TTDTO ttdto, Integer ttId) throws Exception {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        //保存销售合同信息
        TTAddEntity ttAddEntity = convertToTTAdd(salesContractAddTTDTO, ttdto.getPriceDetailBO());
        ttAddEntity.setTtId(ttId);
        if (!salesContractAddTTDTO.getCreateStatus()) {
            TTAddEntity ttAddEntityByTTId = ttAddDao.getTTAddEntityByTTId(ttId);
            if (ttAddEntityByTTId != null) {
                Integer ttAddId = ttAddEntityByTTId.getId();
                ttAddEntity.setId(ttAddId);
            }
        }
        ttAddDao.saveOrUpdate(ttAddEntity);
        return ttAddEntity;
    }

//    public GoodsInfoVO getGoodsInfoVO(SalesContractAddTTDTO salesContractAddTTDTO) {
//        GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO()
//                .setCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
//                .setPackageId(Integer.parseInt(salesContractAddTTDTO.getGoodsPackageId()))
//                .setSpecId(Integer.parseInt(salesContractAddTTDTO.getGoodsSpecId()))
//                .setSupplierId(salesContractAddTTDTO.getSupplierId());
//        return goodsFacade.acquireGoodsInfo(goodsSpecDTO);
//    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }

    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        addApprovalHandler(result, ttCode, memo);
    }

    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        TTAddEntity ttAdd = ttAddDao.getTTAddEntityByTTId(ttId);
        return contractSignConvertUtil.getAddSignTemplateDTO(ttId, ttAdd, ttAdd.getSupplierId());
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getAddTradeTicketEntity(ttdto.getSalesContractAddTTDTO());
        injectionProperty(tradeTicketEntity);
        return tradeTicketEntity;
    }

    public TTAddEntity convertToTTAdd(SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO) throws Exception {

        TTAddEntity ttAddEntity = new TTAddEntity();
        BeanUtils.copyProperties(salesContractAddTTDTO, ttAddEntity);
        ttAddEntity.setPriceEndTime(salesContractAddTTDTO.getPriceEndTime());
        ttAddEntity.setRootContractId(salesContractAddTTDTO.getRootContractId());
        //查询客户信息
        CustomerDTO customer = null;
        CustomerDetailEntity customerDetailEntities = null;
        if (null != salesContractAddTTDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
        }
        if (customer != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(customer.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setCustomerCode(customer.getLinkageCustomerCode());
            ttAddEntity.setCustomerId(customer.getId());
            ttAddEntity.setCustomerName(customer.getName());
            // BUGFIX：Case-1003197 TJIBSBOS2503315， TJIBSBOS2503316合同传输错误 Author: Mr 2025-05-13
            // ttAddEntity.setInvoiceType(customer.getInvoiceType());
            ttAddEntity.setEnterprise(customer.getEnterprise());
        }

        //迟付款罚金
        if (StringUtil.isNumeric(salesContractAddTTDTO.getDelayPayFine())) {
            ttAddEntity.setDelayPayFine(new BigDecimal(salesContractAddTTDTO.getDelayPayFine()));
        }
        //查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractAddTTDTO.getSupplierId()) {
//            supplier = customerFacade.getCustomerById(salesContractAddTTDTO.getSupplierId());
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(salesContractAddTTDTO.getSupplierId())
                    .setCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                    .setFactoryCode(salesContractAddTTDTO.getDeliveryFactoryCode())
                    .setSalesType(salesContractAddTTDTO.getSalesType())
                    .setCompanyId(salesContractAddTTDTO.getCompanyId())
                    .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                    .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
            ;
            supplier = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        }
        if (supplier != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(supplier.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setSupplierId(supplier.getId());
            ttAddEntity.setSupplierName(supplier.getName());
            //ttAddEntity.setSignPlace(supplier.getSignPlace());
            List<CustomerBankDTO> customerBankList = supplier.getCustomerBankDTOS();
            if (customerBankList != null && customerBankList.size() > 0) {
                ttAddEntity.setSupplierAccount(customerBankList.get(0).getBankAccountNo());
            }
        }
        if (salesContractAddTTDTO.getSupplierAccountId() != null) {
            CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(salesContractAddTTDTO.getSupplierAccountId());
            if (customerBankEntity != null) {
                ttAddEntity.setSupplierAccount(customerBankEntity.getBankAccountNo());
            }
        }
        //签订地
        ttAddEntity.setSignPlace(salesContractAddTTDTO.getSignPlace());

        //单位
        ttAddEntity.setUnit(UnitEnum.TON.name());

        //袋皮扣重【采销同适用】
        //      a.豆粕：当【袋皮扣重】选择不扣袋皮时，【包装计算重量】默认为否；当选择具体的值时，【包装计算重量】默认为是
        //      b.豆油：默认【包装计算重量】为否，袋皮扣重【不扣皮】
        ttAddEntity.setPackageWeight(salesContractAddTTDTO.getPackageWeight());
        Integer needPackageWeight = 0;
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(salesContractAddTTDTO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                String ruleKey = systemRuleItemEntity.getRuleKey();
                if (!"不扣皮".equalsIgnoreCase(ruleKey)) {
                    needPackageWeight = 1;
                }
            }
        }
        ttAddEntity.setNeedPackageWeight(needPackageWeight);
        //币种
        ttAddEntity.setCurrencyType(CurrencyTypeEnum.CNY.getDesc());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);

        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));

        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);

        //获取商品信息
        SkuEntity skuEntity = skuFacade.getSkuById(salesContractAddTTDTO.getGoodsId());
        if (null != skuEntity.getTaxRate()) {
            //税率
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            ttAddEntity.setTaxRate(taxRate);
            //cifUnitPrice
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
            ttAddEntity.setCifUnitPrice(cifUnitPrice);
        }

        if (null != salesContractAddTTDTO.getGoodsCategoryId()) {
            ttAddEntity.setGoodsCategoryId(salesContractAddTTDTO.getGoodsCategoryId());
        }
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getGoodsPackageId())) {
            ttAddEntity.setGoodsPackageId(Integer.parseInt(salesContractAddTTDTO.getGoodsPackageId()));
        }
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getGoodsSpecId())) {
            ttAddEntity.setGoodsSpecId(Integer.parseInt(salesContractAddTTDTO.getGoodsSpecId()));
        }


        if (null != salesContractAddTTDTO.getSupplierId() && null != salesContractAddTTDTO.getGoodsCategoryId()) {
            customerDetailEntities = customerDetailFacade.queryCustomerDetailEntity(salesContractAddTTDTO.getSupplierId(), salesContractAddTTDTO.getCategory3());
        }
        if (customerDetailEntities != null) {
            ttAddEntity.setQualityCheck(customerDetailEntities.getQualityCheckContent());
        }

        //查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(salesContractAddTTDTO.getCompanyId())
                .setCustomerId(salesContractAddTTDTO.getSupplierId())
                .setCategory1(String.valueOf(salesContractAddTTDTO.getCategory1()))
                .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);

        if(!customerInvoiceEntities.isEmpty()){
            int invoiceId = customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (systemRuleItemEntity != null) {
                //发票类型
                Integer invoiceType = Integer.parseInt(systemRuleItemEntity.getRuleKey());
                ttAddEntity.setInvoiceType(invoiceType);
                ttAddEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(invoiceType));
            }
        }

        //合同总量
        BigDecimal contractNum = BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getContractNum());
        ttAddEntity.setContractNum(contractNum);

        //计算总价(确认无 temporaryPrice  transactionPrice)
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, unitPrice);
        ttAddEntity.setTotalAmount(totalAmount);

        //基差价
        ttAddEntity.setExtraPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getExtraPrice()));

        //期货价
        ttAddEntity.setForwardPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getForwardPrice()));
        //付款方式
        if (salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() != 0) {
            ttAddEntity.setPaymentType(PaymentTypeEnum.CREDIT.getType());
        } else {
            ttAddEntity.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        }
        //应付履约保证金
        ttAddEntity.setDepositAmount(BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getDepositAmount()));

        //追加履约保证金
/*        // 客户Id
        Integer customerId = salesContractAddTTDTO.getCustomerId();
        // 品类Id
        Integer categoryId = salesContractAddTTDTO.getGoodsCategoryId();
        // 合同类型
        String contractType = salesContractAddTTDTO.getContractType();

        if (null != customerId && null != categoryId && StringUtils.isNotBlank(contractType)) {
            DepositRuleDTO depositRuleDTO = new DepositRuleDTO()
                    .setContractType(Integer.parseInt(contractType))
                    .setCustomerId(customerId)
                    .setCategoryId(categoryId)
                    .setRuleType(DepositSceneEnum.FALL.getType())
                    .setContractNum(contractNum);
            BigDecimal addedDepositAmount = depositRuleFacade.calcContractUseDeposit(depositRuleDTO);
            ttAddEntity.setAddedDepositAmount(addedDepositAmount);
        }*/
        BigDecimal addedDepositAmount = BigDecimal.ZERO;
        int contractType = StringUtils.isNotBlank(salesContractAddTTDTO.getContractType()) ? Integer.parseInt(salesContractAddTTDTO.getContractType()) :
                ContractTypeEnum.YI_KOU_JIA.getValue();
        switch (ContractTypeEnum.getByValue(contractType)) {
            case YI_KOU_JIA:
            case ZAN_DING_JIA:
                addedDepositAmount = totalAmount.multiply(BigDecimal.valueOf(0.05));
                break;
            case JI_CHA:
                addedDepositAmount = contractNum.multiply(BigDecimal.valueOf(150));
                break;
            default:
                break;
        }
        ttAddEntity.setAddedDepositAmount(addedDepositAmount);


        //交货时间
        ttAddEntity.setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime());
        ttAddEntity.setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime());

        //点价截止时间
        ttAddEntity.setPriceEndTime(salesContractAddTTDTO.getPriceEndTime());

        //履约保证金比例id
        ttAddEntity.setDepositRate(salesContractAddTTDTO.getDepositRate());

        //期货合约
        ttAddEntity.setDomainCode(salesContractAddTTDTO.getDomainCode());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getShipWarehouseId())) {
            ttAddEntity.setShipWarehouseId(Integer.parseInt(salesContractAddTTDTO.getShipWarehouseId()));
        }
        ttAddEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttAddEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (salesContractAddTTDTO.getSourceContractId() != null) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractAddTTDTO.getSourceContractId());
            if (contractEntity != null) {
                ttAddEntity.setSourceContractNum(contractEntity.getContractNum());
            }
        }
        ttAddEntity.setWashoutPriceDetail(salesContractAddTTDTO.getWashoutPriceDetailBO());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getContractType())) {
            ttAddEntity.setContractType(Integer.parseInt(salesContractAddTTDTO.getContractType()));
        }
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(salesContractAddTTDTO.getCategory2())
                .setContractType(Integer.valueOf(salesContractAddTTDTO.getContractType()))
                .setDepositRate(salesContractAddTTDTO.getDepositRate())
                .setAddedDepositRate(salesContractAddTTDTO.getAddedDepositRate());
        ttAddEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // V1 值转对象 Author:zengshl 2024-06-18 start
        ttAddEntity.setDestinationValue(systemRuleConvertValue(ttAddEntity.getDestination()));
        ttAddEntity.setPackageWeightValue(systemRuleConvertValue(ttAddEntity.getPackageWeight()));
        ttAddEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttAddEntity.getDeliveryType()));
        ttAddEntity.setWeightCheckValue(systemRuleConvertValue(ttAddEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        ttAddEntity.setInvoiceTypeValue(invoiceTypeConvertValue(ttAddEntity.getInvoiceType()));
        ttAddEntity.setShipWarehouseValue(factoryConvertValue(ttAddEntity.getShipWarehouseId()));
        // V1 值转对象 Author:zengshl 2024-06-18 end
        return ttAddEntity;
    }


    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        TTQueryDetailVO ttQueryDetailVO = BeanConvertUtils.map(TTQueryDetailVO.class, tradeTicketEntity);
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(ttAddEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(ttAddEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttAddEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttAddEntity.getSupplierId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        //买家
        if (null != ttAddEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttAddEntity.getCustomerId()));
        }

        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }
        if (null != ttAddEntity.getGoodsPackageId()) {
            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttAddEntity.getGoodsPackageId()));
        }
        if (null != ttAddEntity.getGoodsSpecId()) {
            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttAddEntity.getGoodsSpecId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());

        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);


        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        //原合同信息
        ttQueryDetailVO.setRootContractId(tradeTicketEntity.getSourceContractId());
        if (null != tradeTicketEntity.getSourceContractId()) {
            ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getSourceContractId());
            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }

        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_PURCHASE_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        ttDetailVO.setDetailType("0");
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        return ttDetailVO;
    }

    @Override
    public void cancel(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        if (ttTypeEnum == TTTypeEnum.NEW) {
            String userId = JwtUtils.getCurrentUserId();
            //取消工作流审批
            cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
            log.info("check_code_question  cancel ");
            //修改tt状态为待修改提交
            tradeTicketDao.updateStatusById(TTStatusEnum.WAITING.getType(), null, operateTTDTO.getTtId());
            //取消协议
            cancelContractSign(tradeTicketEntity, operateTTDTO.getMemo());
        } else {
            super.cancel(operateTTDTO, tradeTicketEntity);
        }
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {

    }
}

