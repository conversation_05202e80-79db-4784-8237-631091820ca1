package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.TemplateAttributeMapper;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/3 19:12
 */
@Dao
public class TemplateAttributeDao extends BaseDaoImpl<TemplateAttributeMapper, TemplateAttributeEntity> {


    public IPage<TemplateAttributeEntity> queryTemplateAttributeList(QueryDTO<TemplateAttributeEntity> queryDTO) {
        Integer pageNo = queryDTO.getPageNo();
        Integer pageSize = queryDTO.getPageSize();

        ObjectMapper mapper = new ObjectMapper();
        TemplateAttributeEntity templateAttribute = mapper.convertValue(queryDTO.getCondition(), TemplateAttributeEntity.class);
        Integer contractType = templateAttribute.getContractType();
//        String goodsName = templateAttribute.getGoodsName();
        IPage<TemplateAttributeEntity> page = this.page(new Page<>(pageNo, pageSize), new LambdaQueryWrapper<TemplateAttributeEntity>()
                .eq(contractType != null, TemplateAttributeEntity::getContractType, contractType));
//                .eq(StrUtil.isNotBlank(goodsName), TemplateAttributeEntity::getGoodsName, goodsName));
        return page;
    }

    public TemplateAttributeEntity queryTemplateAttribute(QueryTemplateAttributeDTO queryTemplateAttributeDTO) {
        List<TemplateAttributeEntity> templateAttributeEntityList = this.list(new LambdaQueryWrapper<TemplateAttributeEntity>()
                .eq(null != queryTemplateAttributeDTO.getContractType(), TemplateAttributeEntity::getContractType, 0)
                .eq(null != queryTemplateAttributeDTO.getCategoryId(), TemplateAttributeEntity::getCategoryId, queryTemplateAttributeDTO.getCategoryId())
                .eq(null != queryTemplateAttributeDTO.getSalesType(), TemplateAttributeEntity::getSalesType, queryTemplateAttributeDTO.getSalesType())
                .eq(null != queryTemplateAttributeDTO.getCategoryId(), TemplateAttributeEntity::getTemplateType, queryTemplateAttributeDTO.getTemplateType())
                .eq(null != queryTemplateAttributeDTO.getActionType(), TemplateAttributeEntity::getActionType, queryTemplateAttributeDTO.getActionType())
                .eq(null != queryTemplateAttributeDTO.getTradeType(), TemplateAttributeEntity::getTradeType, queryTemplateAttributeDTO.getTradeType())
                .eq(null != queryTemplateAttributeDTO.getSignType(), TemplateAttributeEntity::getSignType, queryTemplateAttributeDTO.getSignType())
        );
        if (CollectionUtils.isEmpty(templateAttributeEntityList)) {
            templateAttributeEntityList = this.list(new LambdaQueryWrapper<TemplateAttributeEntity>()
                    .eq(null != queryTemplateAttributeDTO.getContractType(), TemplateAttributeEntity::getContractType, 0)
                    .eq(null != queryTemplateAttributeDTO.getCategoryId(), TemplateAttributeEntity::getCategoryId, queryTemplateAttributeDTO.getCategoryId())
                    .eq(null != queryTemplateAttributeDTO.getSalesType(), TemplateAttributeEntity::getSalesType, queryTemplateAttributeDTO.getSalesType())
                    .eq(null != queryTemplateAttributeDTO.getCategoryId(), TemplateAttributeEntity::getTemplateType, -1)
                    .eq(null != queryTemplateAttributeDTO.getActionType(), TemplateAttributeEntity::getActionType, queryTemplateAttributeDTO.getActionType())
                    .eq(null != queryTemplateAttributeDTO.getTradeType(), TemplateAttributeEntity::getTradeType, queryTemplateAttributeDTO.getTradeType())
                    .eq(null != queryTemplateAttributeDTO.getSignType(), TemplateAttributeEntity::getSignType, queryTemplateAttributeDTO.getSignType())
            );
        }
        return CollectionUtils.isEmpty(templateAttributeEntityList) ? null : templateAttributeEntityList.get(0);
    }
}
