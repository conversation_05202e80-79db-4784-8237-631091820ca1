package com.navigator.trade.service.check;

import java.util.List;

/**
 * <p>
 * 合同校验服务
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/18
 */
public interface ICheckContractService {

    /**
     * 校验合同数据
     *
     * @param contractCodeList 合同号List
     * @param isOpenSync       是否开启获取lkg
     * @param startDateTime    开始校验时间
     * @param endDateTime      结束校验时间
     */
    void checkContract(List<String> contractCodeList, Integer isOpenSync, String startDateTime, String endDateTime);

    /**
     * 根据code和batch重试
     *
     * @param contractCodeList 合同号List
     * @param isOpenSync       是否开启获取lkg
     * @param batch            校验的batch
     */
    void retryCheckContractByBatch(List<String> contractCodeList, Integer isOpenSync, String batch);

    /**
     * 校验lkg数据
     *
     * @param contractCodeList contractEntity
     */
    void checkLkgContractByCodeList(List<String> contractCodeList);

    void checkDailyContract();
}
