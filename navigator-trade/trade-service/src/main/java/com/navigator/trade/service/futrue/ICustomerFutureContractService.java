package com.navigator.trade.service.futrue;

import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.future.pojo.dto.CustomerDomainTransDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;

import java.util.Date;

public interface ICustomerFutureContractService {

    /**
     * 获取用户的转月、反点价次数
     *
     * @param customerId      客户id
     * @param category2 商品品类id
     * @return
     */
    ContractTransferCountDTO getContractTransferCount(Integer customerId, Integer category2, Integer category3);


    /**
     * lkg导入合同用
     *
     * @param customerId      客户id
     * @param goodsCategoryId 商品品类id
     * @return
     */
    ContractTransferCountDTO getContractTransferNum(Integer customerId,
                                                    Integer goodsCategoryId,
                                                    String domainCode,
                                                    Date deliveryEndTime,
                                                    Integer category2);

    ContractTransferCountDTO getContractTransferNum(Integer customerId, Integer goodsCategoryId, Integer isOverForward, Integer category2);

    /**
     * 获取用户的点价、转月量的信息
     *
     * @param customerId      客户id
     * @param goodsCategoryId 商品品类id
     * @param domainCode      主力合约
     * @param priceTypeEnum   操作类型
     * @return
     */
    CustomerDomainTransDTO getCustomerFutureContractInfo(Integer customerId, Integer goodsCategoryId, String domainCode, PriceTypeEnum priceTypeEnum);


    /**
     * 更改客户的白名单状态
     *
     * @param customerDetailDTO
     */
    boolean changeCustomerWhiteList(CustomerDetailDTO customerDetailDTO);
}
