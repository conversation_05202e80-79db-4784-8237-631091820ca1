package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BusinessUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.handler.SalesContractHandler;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.*;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.contractsign.IContractSignService;
import com.navigator.trade.service.futrue.ICustomerFutureContractService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同的接口默认实现（暂未处理，默认空实现）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
public abstract class BaseContractAbstractService implements IContractService {
    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    protected ContractDao contractDao;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected SkuFacade skuFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected ITtAddService ttAddService;
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected TTHandler ttHandler;
    @Autowired
    protected SalesContractSignHandler salesContractSignHandler;
    @Autowired
    protected ICustomerFutureContractService customerFutureService;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected SalesContractHandler salesContractHandler;
    @Autowired
    protected PriceApplyFacade priceApplyFacade;
    @Autowired
    protected IContractValueObjectService contractValueObjectService;
    @Resource
    protected ContractPriceFacade contractPriceFacade;
    @Autowired
    protected ITtModifyService ttModifyService;
    @Autowired
    protected CustomerCreditPaymentFacade customerCreditPaymentFacade;
    @Autowired
    protected FuturesDomainFacade futuresDomainFacade;
    @Autowired
    protected PriceAllocateFacade priceAllocateFacade;
    @Autowired
    protected IContractStructureService contractStructureService;
    @Autowired
    protected ITradeTicketQueryService tradeTicketQueryService;
    @Autowired
    protected RedisUtil redisUtil;
    @Autowired
    protected PayConditionFacade payConditionFacade;
    @Autowired
    protected QualityFacade qualityFacade;
    @Autowired
    protected CustomerInvoiceFacade customerInvoiceFacade;

    /**
     * 根据tt创建合同
     *
     * @param contractCreateDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createContract(ContractCreateDTO contractCreateDTO) {
        ContractEntity contractEntity = new ContractEntity();

        // 补充基本信息
        buildBaseInfo(contractCreateDTO, contractEntity);
        // 补充业务信息
        buildBizInfo(contractCreateDTO, contractEntity);
        // 创建合同
        create(contractCreateDTO, contractEntity);
        // 创建合同附件信息
        createAdditionalInfo(contractCreateDTO, contractEntity);
        // 后续操作
        afterCreateProcess(contractCreateDTO, contractEntity);

        return contractEntity;
    }

    //===================================================合同新增===================================================

    /**
     * 补充基本信息
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    protected void buildBaseInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 拷贝基本信息
        BeanUtils.copyProperties(contractCreateDTO, contractEntity);

        // 补充基本信息
        contractEntity.setUuid(IdUtil.simpleUUID())
                .setLinkinageCode(contractCreateDTO.getContractCode())
                .setRepeatContractCode(contractCreateDTO.getContractCode())
                .setContractType(contractCreateDTO.getContractType())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setPriceEndTime(contractCreateDTO.getPriceEndTime())
                .setPriceEndType(contractCreateDTO.getPriceEndType())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setCreateBatch(null);
    }

    /**
     * 补充业务信息
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    protected void buildBizInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        int isLdcFrame = DisableStatusEnum.ENABLE.getValue();
        int signatureType = DisableStatusEnum.DISABLE.getValue();
        int needOriginalPaper = DisableStatusEnum.DISABLE.getValue();

        // 主体id
        Integer customerId = contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue() ? contractCreateDTO.getCustomerId() : contractCreateDTO.getSupplierId();

        // 客户信息
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-新增、回购 Author: Mr 2024-04-26 Start
        updateCustomerInfo(contractEntity.getCustomerId(),
                contractEntity.getSupplierId(),
                contractEntity.getCustomerId(),
                contractEntity.getSupplierId(),
                contractEntity);
        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-新增、回购 Author: Mr 2024-04-26 End

        if (customerDTO != null) {
            // 判断是否需要正本
            needOriginalPaper = customerDTO.getOriginalPaper();
            // 签章类型:是否使用易企签
            signatureType = customerDTO.getUseYqq();
        }

        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
        customerOriginalPaperDTO
                .setCustomerId(contractCreateDTO.getCustomerId())
                .setCompanyId(contractCreateDTO.getCompanyId())
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setSaleType(contractCreateDTO.getSalesType())
                .setCategory2(String.valueOf(contractCreateDTO.getCategory2()))
                .setCategory3(String.valueOf(contractCreateDTO.getCategory3()))
        ;
        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
        if (customerOriginalPaperEntity != null) {
            // 模板类型
            isLdcFrame = customerOriginalPaperEntity.getLdcFrame();
        }

        // 转月次数处理
        processorCreateTransferTimes(contractCreateDTO, contractEntity);

        contractEntity
                .setNeedOriginalPaper(needOriginalPaper)
                .setLdcFrame(isLdcFrame)
                .setSignatureType(String.valueOf(signatureType));

        // 合同数量处理
        if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            contractEntity.setTotalPriceNum(contractEntity.getContractNum());
        }

        // 签订总金额
        contractEntity.setOrderAmount(contractEntity.getTotalAmount());

        // 可提数量
        contractEntity.setApplyDeliveryNum(BigDecimal.ZERO);

        // V1 套账ID Author:zengshl 2024-06-18 start TODO 客户域需要调整
        // contractEntity.setSiteId(customerDTO.getSiteId());
        // V1 套账ID Author:zengshl 2024-06-18 end
    }

    /**
     * 处理转月次数
     *
     * @param contractCreateDTO 合同创建DTO
     * @param contractEntity    合同实体
     */
    private void processorCreateTransferTimes(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        int totalTransferTimes = 1;
        int totalReversePriceTimes = 0;

        // 是否是超远期合同
        int isOverForward = ObjectUtil.isNotEmpty(contractEntity.getDeliveryEndTime()) &&
                BusinessUtil.isOverForwardContract(contractEntity.getDomainCode(), contractEntity.getDeliveryEndTime())
                && contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? 1 : 0;

        //根据客户和品类查询客户配置
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO
                .setCategory2(String.valueOf(contractCreateDTO.getCategory2()))
                .setCategory3(String.valueOf(contractCreateDTO.getCategory3()))
                .setCustomerId(contractCreateDTO.getCustomerId());
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

        if (CollectionUtils.isNotEmpty(customerDetailEntityList)) {
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            // 转月次数
            totalTransferTimes = customerDetailEntity.getIsWhiteList() == 1 ? 4 : (isOverForward == 1 ? 2 : 1);

            if (customerDetailEntity.getIsReversePrice() == 1) {
                totalReversePriceTimes = 1;
            }
        } else {
            // 没有客户配置，默认转月次数
            totalTransferTimes = isOverForward == 1 ? 2 : 1;
        }

        // 初始化合同转月、反点价次数
        contractEntity.setTotalTransferTimes(totalTransferTimes)
                .setAbleTransferTimes(totalTransferTimes)
                .setIsOverForward(isOverForward)
                .setTransferredTimes(0)
                .setTotalReversePriceTimes(totalReversePriceTimes)
                .setAbleReversePriceTimes(totalReversePriceTimes)
                .setReversedPriceTimes(0);
    }

    /**
     * 创建合同
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    protected void create(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 查询是否存在相同的合同编号，存在即修改待提交，此时删除旧的合同
        List<ContractEntity> oldList = contractDao.getByContractCode(contractEntity.getContractCode());
        oldList.forEach(oldContractEntity -> {
            try {
                contractValueObjectService.updateContractById(oldContractEntity
                        .setRepeatContractCode(oldContractEntity.getRepeatContractCode() + "-" + oldContractEntity.getId())
                        .setIsDeleted(IsDeletedEnum.DELETED.getValue()));
            } catch (Exception e) {
                log.error("contractCode: {} update fail cause by: {}", oldContractEntity.getContractCode(), e.getMessage());
                throw new BusinessException(ResultCodeEnum.FAILURE);
            }
        });

        try {
            contractDao.save(contractEntity);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }

        // 更新TT的合同关系 tt/ttAdd/contractPrice
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(
                contractEntity.getSalesType(),
                TTTypeEnum.NEW.getType(),
                contractEntity.getGoodsCategoryId());
        tradeTicketService.updateContractId(contractCreateDTO.getCurrentTtId(), contractEntity.getId());

        // 更新ttAdd的合同关系
        ttAddService.updateContractId(contractCreateDTO.getCurrentTtId(), contractEntity.getId());

        // 更新TT-price的合同关系
        contractPriceService.updateContractId(contractCreateDTO.getCurrentTtId(), contractEntity.getId());

        // 更新contractSign的合同关系
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(
                contractEntity.getSalesType(),
                TTTypeEnum.NEW.getType(),
                contractEntity.getGoodsCategoryId());
        contractSignService.updateContractId(contractCreateDTO.getCurrentTtId(), contractEntity.getId());
    }

    /**
     * 创建之后的操作
     *
     * @param contractCreateDTO
     * @param contractEntity
     */
    protected void afterCreateProcess(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
    }

    protected void createAdditionalInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
    }

    /**
     * 判断客户是否可用
     *
     * @param
     * @return
     */
    protected boolean isEnableCustomerStatus(ContractEntity contractEntity) {
        int customerId = contractEntity.getCustomerId();
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            customerId = contractEntity.getSupplierId();
        }
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

        return null != customerDTO && DisableStatusEnum.ENABLE.getValue().equals(customerDTO.getStatus());
    }


    @Override
    public ContractEntity createContractByModify(ContractModifyDTO contractModifyDTO) {
        return null;
    }

    @Override
    public ContractEntity createSonContract(ContractTransferDTO contractTransferDTO) {
        return null;
    }

    @Override
    public List<TTQueryVO> splitContract(ContractModifyDTO contractModifyDTO) {
        return null;
    }

    @Override
    public List<TTQueryVO> reviseContract(ContractModifyDTO contractModifyDTO) {
        return null;
    }

    @Override
    public List<TTQueryVO> applyBuyBack(ContractBuyBackDTO contractBuyBackDTO) {
        return null;
    }

    @Override
    public List<TTQueryVO> applyWashOut(ContractWashOutDTO contractWashOutDTO) {
        return null;
    }

    @Override
    public List<TTQueryVO> applyClosed(Integer contractId) {
        return null;
    }

    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, List<TTQueryVO> ttQueryVOS, int ttType) {
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                contractEntity.getSalesType(),
                ttType,
                contractEntity.getGoodsCategoryId());
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);

        if (ttType == TTTypeEnum.SPLIT.getType() && CollectionUtils.isNotEmpty(ttQueryVOS)) {
            ttQueryVOS = ttQueryVOS.stream().filter(i -> i.getSourceFlag() == 1 && i.getTtId() != null).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(ttQueryVOS)) {
            Integer ttId = ttQueryVOS.get(0).getTtId();
            tradeTicketService.updateModifyContent(originalContractEntity, contractEntity, ttId, ttType);
        }

    }

    // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头 Author: Mr 2024-04-26 Start

    /**
     * 更新客户信息
     *
     * @param newCustomerId    新客户ID
     * @param newSupplierId    新供应商ID
     * @param originCustomerId 旧客户ID
     * @param originSupplierId 旧供应商ID
     * @param contractEntity   合同实体
     */
    protected void updateCustomerInfo(Integer originCustomerId, Integer originSupplierId,
                                      Integer newCustomerId, Integer newSupplierId, ContractEntity contractEntity) {
        // 默认父合同客户信息
        contractEntity.setCustomerId(originCustomerId);
        contractEntity.setSupplierId(originSupplierId);

        // 更新Customer
        if (null != newCustomerId) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(newCustomerId);
            contractEntity.setCustomerId(customerDTO.getId())
                    .setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName())
                    .setCustomerStatus(customerDTO.getStatus());
        }

        // 更新Supplier
        if (null != newSupplierId) {
            CustomerDTO supplierDTO = customerFacade.getCustomerById(newSupplierId);
            contractEntity.setSupplierId(supplierDTO.getId())
                    .setSupplierName(supplierDTO.getName());
        }
    }
    // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头 Author: Mr 2024-04-26 End

}
