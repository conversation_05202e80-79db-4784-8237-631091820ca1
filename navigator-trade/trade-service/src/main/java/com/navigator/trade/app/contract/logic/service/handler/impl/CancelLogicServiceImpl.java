package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CancelLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.tt.domain.qo.TTAddQO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.ContractWriteOffStatusEnum;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtAddService;
import com.navigator.trade.service.ITtPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 取消合同变更业务逻辑处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/20
 */
@Slf4j
@Service
public class CancelLogicServiceImpl implements CancelLogicService {
    /**
     * TODO 待解耦
     */
    @Autowired
    protected ITtPriceService ttPriceService;  // 价格要解耦
    @Autowired
    protected ITtAddService ttAddService;  // 获取解约索赔的数量
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private ContractSignQueryLogicService contractSignQueryLogicService;
    @Autowired
    private CommonLogicService commonLogicService;
    @Autowired
    private TTQueryDomainService ttQueryDomainService;
    @Autowired
    private TTQueryLogicService ttQueryLogicService;

    @Override
    public void cancelContractModify(ContractModifyDTO contractModifyDTO) {
        // 获取合同信息
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractModifyDTO.getContractId());
        // 场景：审批驳回
        switch (ContractActionEnum.getByType(contractModifyDTO.getContractSource())) {
            case REVISE:
                rollBackContractChange(contractModifyDTO);
                break;
            case CLOSED:
            case INVALID:
                // 合同进入生效中：Status
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                break;
            case REVISE_CUSTOMER:
                // 父合同进入生效中:Status、ContractNum
                ContractEntity reviseParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                if (null != reviseParentContract && reviseParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    contractDomainService.updateContractById(reviseParentContract
                            .setContractNum(reviseParentContract.getContractNum().add(contractEntity.getContractNum()))
                            .setTotalAmount(reviseParentContract.getTotalAmount().add(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), reviseParentContract.getUnitPrice())))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, reviseParentContract.getTotalAmount(), BigDecimal.valueOf(reviseParentContract.getDepositRate() * 0.01)))
                            .setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                }
                // 子合同进入已作废
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
            case SPLIT:
            case SPLIT_CUSTOMER:
                // 查询父合同
                ContractEntity splitCustomerContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                // 1.子合同撤回
                if (null != splitCustomerContract
                        && contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                    // 原合同进入拆分中，子合同进入已作废
                    updateBySplitRejected(splitCustomerContract, contractEntity);
                } else {
                    // 2. 父合同撤回 部分拆分的父合同审批  -- 在TT域里面处理 是否有子合同
                    if (contractEntity.getStatus() != ContractStatusEnum.INEFFECTIVE.getValue()) {
                        // 子合同进入已作废
                        TradeTicketQO qo = new TradeTicketQO();
                        qo.setTtId(contractModifyDTO.getTtId());
                        TradeTicketEntity ticketEntity = ttQueryLogicService.fetchTradeTicketEntity(qo);
                        if (null != ticketEntity) {
                            qo.setTtId(ticketEntity.getId());
                            qo.setGroupId(ticketEntity.getGroupId());
                            TradeTicketEntity sonTicketEntity = ttQueryLogicService.fetchTradeTicketEntity(qo);
                            if (null != sonTicketEntity) {
                                ContractEntity sonContract = contractQueryDomainService.getBasicContractById(sonTicketEntity.getContractId());
                                if (null != sonContract) {
                                    updateBySplitRejected(contractEntity, sonContract);
                                }
                            }
                        }
                    }
                }
                break;
            case WASHOUT:
                // 更改修改中的合同状态
                if (!contractEntity.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    break;
                }
                // 解约定赔导致的原合同全部定价
                if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue()) && BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                    // 回滚合同数据
                    ContractEntity rollBackContract = contractDomainService.rollBackContract(contractEntity.getId());
                    contractEntity.setUnitPrice(rollBackContract.getUnitPrice())
                            .setFobUnitPrice(rollBackContract.getFobUnitPrice())
                            .setCifUnitPrice(rollBackContract.getCifUnitPrice())
                            .setExtraPrice(rollBackContract.getExtraPrice());
                }
                // 释放解约定赔数量
                TTAddQO qo = new TTAddQO();
                qo.setTtId(contractModifyDTO.getTtId());
                TTAddEntity ttAddEntity = ttQueryLogicService.fetchTTAddEntity(qo);
                if (null != ttAddEntity) {
                    contractEntity
                            .setContractNum(contractEntity.getContractNum().add(ttAddEntity.getContractNum()))
                            .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), contractEntity.getUnitPrice()))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
                }
                // 合同进入生效中：Status
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                break;
            case BUYBACK:
                // 父合同进入生效中:Status、TotalBuyBackNum
                ContractEntity buyBackParentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                if (buyBackParentContract != null) {
                    Integer parentStatus = buyBackParentContract.getStatus();
                    if (parentStatus.equals(ContractStatusEnum.MODIFYING.getValue())
                            || parentStatus.equals(ContractStatusEnum.COMPLETED.getValue())) {
                        contractDomainService.updateContractById(buyBackParentContract
                                .setTotalBuyBackNum(buyBackParentContract.getTotalBuyBackNum().subtract(contractEntity.getContractNum()))
                                .setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                    }
                }
                // 子合同进入已作废
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
            case ALLOCATE:
            case ASSIGN:
                // 仓单还原
                commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_INVALID.getValue(), contractEntity.getContractNum(), null);
                // 合同作废
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
            case WARRANT_WRITEOFF:
                // 区分注销B和注销C的处理
                ContractEntity warrantContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
                // 合同作废
                contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                // 1.退回仓单住销量
                WarrantCancellationEntity cancellation = tradeDomainRemoteService.queryWarrantCancellation(contractEntity.getContractCode());
                commonLogicService.updateWarrantNum(warrantContract, WarrantModifyTypeEnum.CONTRACT_CANCEL_REVOKE.getValue(),
                        contractEntity.getContractNum(), cancellation.getId());

                // 更新仓单已注销数量【分场景进行恢复注销量|合同量|注销B是拆分的动作】
                if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractModifyDTO.getTtTradeType()) {
                    // 重新计算价格和合同数量
                    BigDecimal contractNum = warrantContract.getContractNum().add(contractEntity.getContractNum());
                    warrantContract
                            .setContractNum(contractNum)
                            .setOrderNum(contractNum)
                            .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, warrantContract.getUnitPrice()))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, warrantContract.getTotalAmount(), BigDecimal.valueOf(warrantContract.getDepositRate() * 0.01)))
                            .setTotalPriceNum(contractNum)
                            .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, warrantContract.getUnitPrice()));
                } else {
                    warrantContract.setWarrantCancelCount(warrantContract.getWarrantCancelCount().subtract(contractEntity.getContractNum()));
                    // 关联的合同也要进行作废 采购或者销售的合同
                    TradeTicketEntity tradeTicketEntity = ttQueryDomainService.getByTtId(contractModifyDTO.getTtId());
                    TradeTicketEntity tradeTicketOther = ttQueryDomainService.getByGroupId(tradeTicketEntity.getGroupId(), tradeTicketEntity.getId());
                    ContractEntity otherContract = contractQueryDomainService.getBasicContractById(tradeTicketOther.getContractId());
                    // 另外一个合同作废
                    contractDomainService.updateContractById(otherContract.setStatus(ContractStatusEnum.INVALID.getValue()));
                }
                if (warrantContract.getWarrantCancelCount().compareTo(BigDecimal.ZERO) == 0) {
                    warrantContract.setWriteOffStatus(ContractWriteOffStatusEnum.NOT_WRITEOFF.getValue());
                } else {
                    warrantContract.setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
                }
                warrantContract.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
                // 更新仓单合同
                contractDomainService.updateContractById(warrantContract);

                break;
            default:
                break;
        }
    }

    /**
     * 仓单合同关闭
     * 合同状态更新为已关闭
     * NAV需校验，剩余可注销量=0并且原合同及注销产生的新合同没有出具中的协议，若不符合条件则接口报错，发送错误报告给（合同不变）
     * 符合条件需锁定此合同的已注销量及提货权，不计入提货委托量的计算，同步合同的状态为已关闭，可提货数量为；0
     *
     * @param contractCode
     */
    @Override
    public ContractEntity warrantContractClose(String contractCode) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractByCode(contractCode);
        BigDecimal canWriteOffNum = contractEntity.getContractNum().subtract(contractEntity.getWarrantCancelCount())
                .subtract(contractEntity.getTotalBuyBackNum());
        //TODO 发送错误给Atlas
        if (BigDecimal.ZERO.compareTo(canWriteOffNum) < 0) {
            throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_CLOSE_NUM);
        }
        // TODO 原合同及注销产生的新合同没有出具中的协议【出现未生效的合同就是说明协议出具未完成】
        List<ContractEntity> contractEntities = contractQueryDomainService.getContractByPid(contractEntity.getId());
        contractEntities.forEach(item -> {
            if (ContractStatusEnum.EFFECTIVE.getValue() != item.getStatus()) {
                throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_CLOSE_SIGN);
            }
        });
        // 同步合同的状态为已关闭，可提货数量为0；
        contractEntity.setStatus(ContractStatusEnum.CLOSED.getValue());
        contractDomainService.updateContractById(contractEntity);
        // 记录日志
        commonLogicService.addContractOperationLog(contractEntity, LogBizCodeEnum.ATLAS_CLOSE_APPLY, "", SystemEnum.MAGELLAN.getValue());
        return contractEntity;
    }


    /**
     * 普通拆分的驳回
     *
     * @param parentContract
     * @param contractEntity
     */
    private void updateBySplitRejected(ContractEntity parentContract, ContractEntity contractEntity) {
        // 子合同进入已作废
        contractDomainService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));

        BigDecimal totalPriceNum = parentContract.getTotalPriceNum();

        // 基差拆一口价定价单的更新 -- TT域去处理 - 更新合同的价格信息
        TTPriceQO qo = new TTPriceQO();
        qo.setContractId(contractEntity.getId());
        List<TTPriceEntity> confirmPriceList = ttQueryLogicService.fetchTTPriceEntities(qo);
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            Integer sourceId = ttPriceEntity.getSourceId();
            TTPriceEntity priceEntity = ttPriceService.getById(sourceId);
            // TODO 待调整
            ttPriceService.updateTtPrice(priceEntity.setNum(priceEntity.getNum().add(ttPriceEntity.getNum())));
            // 父合同增加已定价量
            totalPriceNum = totalPriceNum.add(ttPriceEntity.getNum());
            ttPriceService.updateTtPrice(ttPriceEntity.setNum(BigDecimal.ZERO));
        }
        // 一口价合同拆分已定价量
        if (parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.add(contractEntity.getContractNum());
        }
        // 拆分导致的原合同全部定价
        if (!parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && parentContract.getContractNum().add(contractEntity.getContractNum()).equals(totalPriceNum)) {
            // 原合同需要自动计算加权平均价
            qo.setContractId(parentContract.getId());
            List<TTPriceEntity> ttPriceEntityList = ttQueryLogicService.fetchTTPriceEntities(qo);
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : ttPriceEntityList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }
            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);
                // 更新期货价格
                // TODO 需要调整
                contractPriceService.updateContractForwardPrice(parentContract, averagePrice);
            }
        }
        // 合同状态控制
        int status = ContractStatusEnum.EFFECTIVE.getValue();
        // 是否存在拆分中的合同
        List<ContractEntity> contractEntityList = contractQueryDomainService.getContractByPid(parentContract.getId());
        List<ContractEntity> sonContractList = contractEntityList.stream()
                .filter(entity ->
                        Arrays.asList(ContractActionEnum.SPLIT.getActionValue(), ContractActionEnum.SPLIT_CUSTOMER.getActionValue()).contains(entity.getContractSource())
                                && ContractStatusEnum.INEFFECTIVE.getValue() == entity.getStatus())
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(sonContractList)) {
            status = ContractStatusEnum.SPLITTING.getValue();
        }
        contractDomainService.updateContractById(parentContract
                .setContractNum(parentContract.getContractNum().add(contractEntity.getContractNum()))
                .setTotalModifyNum(parentContract.getTotalModifyNum().subtract(contractEntity.getContractNum()))
                .setTotalPriceNum(totalPriceNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, parentContract.getContractNum(), parentContract.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, parentContract.getTotalAmount(), BigDecimal.valueOf(parentContract.getDepositRate() * 0.01)))
                .setStatus(status));
    }

    /**
     * 回滚合同信息-全量
     *
     * @param contractModifyDTO
     */
    private void rollBackContractChange(ContractModifyDTO contractModifyDTO) {
        contractDomainService.rollBackContract(contractModifyDTO.getContractId());
    }
}
