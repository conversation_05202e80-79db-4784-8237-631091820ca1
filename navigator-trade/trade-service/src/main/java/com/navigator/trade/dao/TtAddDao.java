package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.TTAddMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class TtAddDao extends BaseDaoImpl<TTAddMapper, TTAddEntity> {
    /**
     * 根据ttId查询TT子表
     *
     * @return TTAddEntity
     */
    public TTAddEntity getTTAddEntityByTTId(Integer ttId) {
        return getOne(Wrappers.<TTAddEntity>lambdaUpdate().eq(TTAddEntity::getTtId, ttId));
    }

    /**
     * 根据TTQueryDTO查询TTAddEntity集合
     *
     * @return List<TTAddEntity>
     */
    public List<TTAddEntity> queryListByTTQueryDTO(TTQueryDTO ttQueryDTO) {
        List<TTAddEntity> ttAddEntityList = list(Wrappers.<TTAddEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TTAddEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TTAddEntity::getGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
        );
        return ttAddEntityList;
    }

    /**
     * 更新price中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    public int updateContractId(Integer ttId, Integer contractId) {
        return update(Wrappers.<TTAddEntity>lambdaUpdate()
                .set(TTAddEntity::getContractId, contractId)
                .eq(TTAddEntity::getTtId, ttId)) ? 1 : 0;
    }



    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        return update(Wrappers.<TTAddEntity>lambdaUpdate()
                .set(TTAddEntity::getContractId, contractEntity.getId())
                .set(TTAddEntity::getContractCode, contractEntity.getContractCode())
                .set(TTAddEntity::getContractType, contractEntity.getContractType())
                .set(TTAddEntity::getRootContractId, contractEntity.getRootId())
                .eq(TTAddEntity::getTtId, ttId)) ? 1 : 0;
    }

    public List<TTAddEntity> getDestination() {
        return list(new QueryWrapper<TTAddEntity>()
                .select("DISTINCT destination").lambda().
                        orderByAsc(TTAddEntity::getDestination));

    }

    public boolean updateTTAddWarrantId(Integer ttId, Integer warrantId, String warrantCode) {
        return update(Wrappers.<TTAddEntity>lambdaUpdate()
                .set(TTAddEntity::getWarrantId, warrantId)
                .set(TTAddEntity::getWarrantCode, warrantCode)
                .eq(TTAddEntity::getTtId, ttId));
    }
}
