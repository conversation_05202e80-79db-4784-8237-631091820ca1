package com.navigator.trade.app.contract.domain.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.dao.*;
import com.navigator.trade.mapper.ContractMapper;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.ContractMdmInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractRelativeDTO;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.ConfirmPriceVO;
import com.navigator.trade.pojo.vo.ContractDeliveryVO;
import com.navigator.trade.pojo.vo.ContractVO;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.service.IContractHistoryService;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Slf4j
@Service
@RefreshScope
public class ContractQueryDomainServiceImpl implements ContractQueryDomainService {

    /**
     * TODO facade 的逻辑渐渐抽离到 Logic面去，抽离了一些，还要继续重构
     */
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private ContractDao contractDao;
    @Autowired
    private ContractVODao contractVODao;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ITtPriceService ttPriceService;
    @Autowired
    private ContractHistoryDao contractHistoryDao;
    @Autowired
    private ContractMapper contractMapper;
    /*
     * TODO 渐渐抽离到QuerydDomain里面去，抽离了一些，还要继续重构
     */
    @Autowired
    private IContractValueObjectService contractValueObjectService;
    @Autowired
    private IContractHistoryService contractHistoryService;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;
    @Autowired
    private ContractStructureDao contractStructureDao;
    @Autowired
    private ContractChangeEquityDao changeEquityDao;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private EmployFacade employFacade;
    @Resource
    private PayConditionFacade payConditionFacade;

    @Override
    public String genNewContractCode(String companyName, Integer salesType, Integer goodsCategoryId) {
        // 商品品类
        // String goodsCategory = GoodsCategoryEnum.getByValue(goodsCategoryId).getCode();
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(goodsCategoryId);
        String goodsCategory = categoryEntity.getCode();
        // 生成递增序列号
        long sequence = getContractSequence(salesType, goodsCategoryId, companyName);

        return CodeGeneratorUtil.genNewContractCode(companyName, goodsCategory,
                ContractSalesTypeEnum.getByValue(salesType).getDirectCode(),
                sequence);
    }

    @Override
    public String genStructureContractCode(String companyName) {
        String contractCode = "structure:contract:code";
        if (StringUtil.isNotBlank(companyName)) {
            contractCode = contractCode + ":" + companyName;
        }
        // 获取递增码
        long value = redisUtil.incr(contractCode, 1L);
        return "LDC" + companyName + "OP" + String.format("%04d", value);
    }

    private long getContractSequence(Integer salesType, Integer goodsCategoryId, String companyName) {
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(goodsCategoryId);
        String categoryCode = null == categoryEntity ? "NULL" : categoryEntity.getCode();
        String salesCode = ContractSalesTypeEnum.SALES.getValue() == salesType ? "sales" : "purchase";
        // 兼容TJIB
        String newCompanyName = "";
        if (!companyName.equals("TJIB")) {
            newCompanyName = ":" + companyName;
        }
        String name = categoryCode + ":" + salesCode + ":contract:sequence" + newCompanyName;
        // 获取递增码
        if (null == redisUtil.get(name)) {
            String maxContractCode = contractDao.getMaxContractCode(companyName, salesType, goodsCategoryId);
            if (StringUtil.isNotEmpty(maxContractCode)) {
                String code = maxContractCode.substring(maxContractCode.length() - 5);
                redisUtil.set(name, Long.parseLong(code));
            }
        }
        String contractSequenceYear = categoryCode + ":" + salesCode + ":contract:sequence:year";
        // 是否跨年份
        String yearLast = LocalDate.now().format(DateTimeFormatter.ofPattern("yy"));
        if (null == redisUtil.get(contractSequenceYear)) {
            redisUtil.set(contractSequenceYear, yearLast);
        } else {
            String year = String.valueOf(redisUtil.get(contractSequenceYear));
            if (!yearLast.equals(year)) {
                // 清空计数
                redisUtil.set(name, 0);
                redisUtil.set(contractSequenceYear, yearLast);
            }
        }
        return redisUtil.incr(name, 1L);
    }

    @Override
    public String genSonContractCode(String contractCode, Integer salesType) {
        // 处理合同号
        if (contractCode.contains("-")) {
            contractCode = contractCode.split("-")[0];
        }
        // 获取数据库中最大编号
        List<ContractEntity> contractEntityList = contractDao.getContractCodeList(contractCode);
        List<Integer> codeList = contractEntityList.stream().map(contractEntity -> {
            String code = contractEntity.getContractCode();
            code = code.replace("T", "");
            return Integer.parseInt(code.contains("-") ? code.split("-")[1] : "0");
        }).collect(Collectors.toList());
        // 并发问题,暂时用唯一索引 FIXME
        Integer increaseCode = CollectionUtil.max(codeList) + 1;
        return CodeGeneratorUtil.genSonContractCode(contractCode, increaseCode);
    }

    @Override
    public String genCargoRightsContractCode(String contractCode, Integer salesType) {
        // 处理合同号
        if (contractCode.contains("-")) {
            contractCode = contractCode.split("-")[0];
        }
        // 获取数据库中最大编号
        List<ContractEntity> contractEntityList = contractDao.getContractCodeList(contractCode);
        List<Integer> codeList = contractEntityList.stream().map(contractEntity -> {
            String code = contractEntity.getContractCode();
            code = code.replace("T", "");
            return Integer.parseInt(code.contains("-") ? code.split("-")[1] : "0");
        }).collect(Collectors.toList());
        // 并发问题,暂时用唯一索引 FIXME
        Integer increaseCode = CollectionUtil.max(codeList) + 1;
        return CodeGeneratorUtil.genCargoRightsContractCode(contractCode, increaseCode);
    }

    @Override
    public Result queryContract(QueryDTO<ContractQO> queryDTO) {

        IPage<ContractVOEntity> contractProcessorEntityIPage = contractVODao.queryContract(queryDTO);
        List<ContractVO> contractList = contractProcessorEntityIPage.getRecords().stream()
                .map(this::getContractVO).collect(Collectors.toList());

        // 统一获取lkg接口  TODO 需要优化，这块执行的性能卡顿
        /*if (SystemEnum.COLUMBUS.getName().equals(queryDTO.getCondition().getTriggerSys())) {
            setContractNumInfo(contractList);
        }*/

        return Result.page(contractProcessorEntityIPage, contractList);
    }

    private ContractVO getContractVO(ContractVOEntity contractVOEntity) {
        ContractVO contractVO = new ContractVO();
        BeanUtils.copyProperties(contractVOEntity, contractVO);
        contractVO
                .setTradeType(Integer.valueOf(contractVOEntity.getTradeType()))
                .setSignDate(DateTimeUtil.parseDateString(contractVOEntity.getSignDate()))
                .setContractType(Integer.valueOf(contractVOEntity.getContractType()))
                .setDeliveryStartTime(DateTimeUtil.parseDateString(contractVOEntity.getDeliveryStartTime()))
                .setDeliveryEndTime(DateTimeUtil.parseDateString(contractVOEntity.getDeliveryEndTime()))
                .setStatus(Integer.valueOf(contractVOEntity.getStatus()));
        //contractVO.setDomainCode(contractVOEntity.getFutureCode() + contractVOEntity.getDomainCode());

        // 期货合约&基差价
        BigDecimal extraPrice = contractVOEntity.getExtraPrice() == null ? BigDecimal.ZERO : contractVOEntity.getExtraPrice();
        String domainCode = BigDecimalUtil.isGreaterEqualThanZero(extraPrice) ?
                contractVOEntity.getFutureCode() + contractVOEntity.getDomainCode() + "+" + extraPrice.stripTrailingZeros().toPlainString() :
                contractVOEntity.getFutureCode() + contractVOEntity.getDomainCode() + extraPrice.stripTrailingZeros().toPlainString();
        contractVO.setDomainCode(domainCode);

        Integer priceComplete = PriceCompleteEnum.NOT_PRICE_COMPLETE.getType();

        if (BigDecimalUtil.isEqual(contractVOEntity.getContractNum(), contractVOEntity.getTotalPriceNum()) && Integer.parseInt(contractVOEntity.getContractType()) != ContractTypeEnum.YI_KOU_JIA.getValue()) {
            priceComplete = PriceCompleteEnum.PRICE_COMPLETE.getType();
        }
        contractVO.setPriceComplete(priceComplete);

        // 结构化定价
        if (Integer.parseInt(contractVOEntity.getContractType()) == ContractTypeEnum.STRUCTURE.getValue()) {
            ContractStructureEntity contractStructureEntity = contractStructureDao.getContractStructure(contractVOEntity.getId());
            ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
            contractVO.setContractStructureVO(contractStructureDTO);
        }
        // 点价截止时间
        if (StringUtils.isNotBlank(contractVOEntity.getPriceEndTime())) {
            contractVO.setPriceEndTime(contractVOEntity.getPriceEndTime().split(" ")[0]);
        }
        // 交提货方式
        contractVO.setDeliveryTypeName(contractVOEntity.getDeliveryTypeName());
        // 客户集团名称
        contractVO.setEnterpriseName(contractVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractVOEntity.getEnterpriseName() : contractVOEntity.getSupplierEnterpriseName());
        // 已定价量
        contractVO.setPriceNum(contractVOEntity.getTotalPriceNum())
                .setCanCancelCount(BigDecimal.ZERO);
        // 可注销量 modify zengshl 视图算过
        if (BuCodeEnum.WT.getValue().equals(contractVOEntity.getBuCode()) &&
                String.valueOf(ContractTypeEnum.YI_KOU_JIA.getValue()).equals(contractVOEntity.getContractType())) {
            contractVO.setCanCancelCount(contractVOEntity.getCanCancelCount());
        }
        // 采购仓单的可注销量-仓单持有量
        if (BuCodeEnum.WT.getValue().equals(contractVOEntity.getBuCode())
                && ObjectUtil.isNotEmpty(contractVOEntity.getHoldCount())
                && ContractSalesTypeEnum.PURCHASE.getValue() == contractVOEntity.getSalesType()
                && contractVOEntity.getCanCancelCount().compareTo(BigDecimal.ZERO) > 0
                && contractVOEntity.getCanCancelCount().compareTo(contractVOEntity.getHoldCount()) > 0) {
            contractVO.setCanCancelCount(contractVOEntity.getHoldCount());

        }
        // 付款条件代码
        contractVO.setPayConditionCode(this.getPayConditionCode(contractVOEntity.getPayConditionId()));

        // TODO 这个逻辑待调整 异步执行合同列表-改造到视图里面,待优化
        CompletableFuture<Void> tradeTicketFuture = CompletableFuture.runAsync(() -> {
            // TradeTicketEntity tradeTicketEntity = tradeTickService.getCanModifyByContractId(String.valueOf(contractVOEntity.getId()));
            // null != tradeTicketEntity ? tradeTicketEntity.getCreatedBy() :
            // null != tradeTicketEntity ? tradeTicketEntity.getCreatedAt() :
            // null != tradeTicketEntity ? tradeTicketEntity.getId() :
            // 操作人信息
            Integer employId = contractVOEntity.getCreatedBy();
            // IDNAME 装换
            contractVO.setOperatorName(IdNameConverter.getName(IdNameType.user_id_name, employId.toString()));
            // 操作时间
            contractVO.setOperatorTime(contractVOEntity.getCreatedAt());
            contractVO.setPaymentTypeName(PaymentTypeEnum.getByType(Integer.valueOf(contractVOEntity.getPaymentType())) == PaymentTypeEnum.IMPREST ? PaymentTypeEnum.IMPREST.getDesc() : PaymentTypeEnum.CREDIT.getDesc() + "，" + contractVOEntity.getCreditDays() + "天")
                    .setLatestTtId(null);
            contractVO.setPaymentType(Integer.valueOf(contractVOEntity.getPaymentType()));
        });
        CompletableFuture<Void> showButtonFuture = CompletableFuture.runAsync(() -> {
            // 展示反点价
            int isShowReverse = DisableStatusEnum.DISABLE.getValue();

            ContractEntity contract = contractDao.getContractById(contractVOEntity.getId());

            if (BigDecimalUtil.isGreaterThanZero(contractVOEntity.getContractNum())                                        // 合同量>0
                    // N-K nav头寸处理改造 changed by Mr at 2025-07-24 start
                    && (Integer.parseInt(contractVOEntity.getStatus()) == ContractStatusEnum.EFFECTIVE.getValue()          // 生效中
                    || (Integer.parseInt(contractVOEntity.getStatus()) == ContractStatusEnum.MODIFYING.getValue() && contract.getIsReversePrice() != null)) // 反点价中
                    // N-K nav头寸处理改造 changed by Mr at 2025-07-24 end
                    && BigDecimalUtil.isZero(contractVOEntity.getCloseTailNum())                                           // 尾量关闭=0
                    && Arrays.asList(11, 12, 25, 26).contains(contractVOEntity.getGoodsCategoryId())                       // 只对豆粕 豆油 特油 特种蛋白显示
                    && contract.getIsSoybean2() == 0                                                                       // 排除豆二
                    && (Integer.parseInt(contractVOEntity.getContractType()) == ContractTypeEnum.YI_KOU_JIA.getValue())    // 一口价
                    && StringUtils.isNotBlank(contractVOEntity.getDomainCode()) && StringUtils.isNotBlank(contractVOEntity.getFutureCode()) // 期货合约不为空
            ) {
                // 销售合同判断可反点次数
                if (contractVOEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
                    // 可反点次数>0
                    if (contractVOEntity.getAbleReversePriceTimes() > 0) {
                        isShowReverse = DisableStatusEnum.ENABLE.getValue();
                    }
                } else {
                    // 采购合同判断是否有反点价权限
                    isShowReverse = DisableStatusEnum.ENABLE.getValue();
                }
            }
            contractVO.setIsShowReversePrice(isShowReverse);

            // 展示定价完成
            int isShowPriceComplete = DisableStatusEnum.DISABLE.getValue();

            // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 Start
            // String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractVOEntity.getContractCode();
            // int priceCompleteStatus = redisUtil.get(priceStatus) == null ? 0 : (int) redisUtil.get(priceStatus);
            int priceCompleteStatus = contract.getIsPricingCompleted() == null ? 0 : contract.getIsPricingCompleted();
            // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 End

            if (Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.ZAN_DING_JIA.getValue(),
                    ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()).contains(Integer.valueOf(contractVOEntity.getContractType()))
                    && BigDecimalUtil.isGreaterThanZero(contractVOEntity.getContractNum())                             // 合同量>0
                    && BigDecimalUtil.isEqual(contractVOEntity.getContractNum(), contractVOEntity.getTotalPriceNum())  // 合同量=定价量
                    && (Integer.parseInt(contractVOEntity.getStatus()) == ContractStatusEnum.EFFECTIVE.getValue())     // 生效中
                    && priceCompleteStatus == 0) {                                                                     // 定价完成
                isShowPriceComplete = DisableStatusEnum.ENABLE.getValue();
            }
            contractVO.setIsShowPriceComplete(isShowPriceComplete);
        });
        CompletableFuture.allOf(tradeTicketFuture, showButtonFuture).join();
        // 是否尾量关闭
        contractVO.setIsCloseTailNum(BigDecimalUtil.isGreaterThanZero(contractVOEntity.getCloseTailNum()) ? 1 : 0);
        return contractVO;
    }


    @Override
    public List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO) {
        // 获取数据
        List<ContractEntity> contractEntities = contractDao.queryContractsByDomainCodeList(queryContractDTO);
        return contractEntities;
    }


    @Override
    public List<ContractEntity> getContractDetailByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return contractEntityList;
    }

    @Override
    public Integer getContractIdByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0).getId() : null;
    }

    @Override
    public ContractEntity getContractByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        return CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0) : null;
    }

    @Override
    public List<ContractEntity> getContractByWarrantCode(String warrantCode) {
        return contractDao.getContractByWarrantCode(warrantCode);
    }

    @Override
    public ContractEntity getContractDetailById(String contractId) {
        return contractDao.getContractById(Integer.valueOf(contractId));
    }


    @Override
    public Result getContractUnitPriceDetail(String contractId) {
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(Integer.parseInt(contractId));
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtil.copyProperties(contractPriceEntity, priceDetailVO);
        return Result.success(priceDetailVO);
    }


    @Override
    public ContractEntity getBasicContractById(Integer id) {
        return contractDao.getContractById(id);
    }

    @Override
    public ContractEntity getBasicContractByCode(String code) {
        return contractDao.getBasicContractByCode(code);
    }

    @Override
    public List<ContractEntity> getByContractCode(String code) {
        return contractDao.getByContractCode(code);
    }

    @Override
    public List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO) {
        return contractDao.queryContractsFutures(contractFuturesDTO);
    }

    @Override
    public List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO) {
        return contractDao.queryContractsFuturesNum(contractFuturesDTO);
    }

    @Override
    public IPage<ContractEntity> futureContracts(QueryDTO<ContractFuturesDTO> queryDTO) {
        IPage<ContractEntity> iPage = contractDao.futureContracts(queryDTO);
        return iPage;
    }


    private void setContractNumInfo(List<ContractVO> contractVOList) {
        for (ContractVO contractVO : contractVOList) {
            try {
                ContractEntity contractEntity = contractValueObjectService.getContractById(contractVO.getId());
                setContractVOInfo(contractVO, contractEntity);
            } catch (Exception e) {
                log.error("contractId:{} getLkgInfo Exception,cause:{}", contractVO.getId(), e.getMessage());
                for (ContractVO newContractVO : contractVOList) {
                    ContractEntity newContractEntity = this.getBasicContractById(newContractVO.getId());
                    newContractVO.setStatus(ContractStatusEnum.LKG_EXCEPTION.getValue());
                    setContractVOInfo(newContractVO, newContractEntity);
                }
                break;
            }
        }
    }

    private ContractVO setContractVOInfo(ContractVO contractVO, ContractEntity contractEntity) {

        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        // 已开单数量
        BigDecimal totalBillNum = contractEntity.getTotalBillNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBillNum();
        contractVO.setPriceNum(totalPriceNum);
        switch (typeEnum) {
            case YI_KOU_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(BigDecimal.ZERO)
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            case JI_CHA:
            case JI_CHA_ZAN_DING_JIA:
            case ZAN_DING_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(contractNum.subtract(totalPriceNum))
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            default:
                break;
        }
        return contractVO;
    }


    @Override
    public List<ConfirmPriceVO> getConfirmPriceList(Integer contractId) {
        List<ConfirmPriceVO> confirmPriceVOList = new ArrayList<>();

        List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractId);
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            ConfirmPriceVO confirmPriceVO = new ConfirmPriceVO();
            confirmPriceVO.setContractId(ttPriceEntity.getContractId())
                    .setTtPriceId(ttPriceEntity.getId())
                    .setConfirmPrice(ttPriceEntity.getPrice())
                    .setConfirmNum(ttPriceEntity.getNum());
            confirmPriceVOList.add(confirmPriceVO);
        }

        return confirmPriceVOList;
    }

    @Override
    public List<ContractEntity> getContractList(Integer customerId, Integer goodsCategoryId, String domainCode, List<Integer> contractTypeList) {
        return contractDao.getContractList(customerId, goodsCategoryId, domainCode, contractTypeList);
    }

    @Override
    public List<ContractEntity> getContractList(Integer customerId, List<Integer> category2List, List<Integer> category3List, List<Integer> contractStatusList, List<Integer> contractTypeList) {
        return contractDao.getContractList(customerId, category3List, contractStatusList, contractTypeList);
    }

    @Override
    public List<ContractEntity> getContractList(List<String> contractCodeList, String startDateTime, String endDateTime) {
        return contractDao.getContractList(contractCodeList, startDateTime, endDateTime);
    }


    @Override
    public List<ContractEntity> getDailyContractList(Integer salesType, String startDateTime, String endDateTime) {
        return contractDao.getDailyContractList(salesType, startDateTime, endDateTime);
    }


    @Override
    public List<ContractEntity> getContractByPid(Integer id) {
        return contractDao.getContractByPid(id);
    }


    @Override
    public List<ContractEntity> getContractListByIds(List<Integer> contractIdList) {
        return contractDao.getContractListByIds(contractIdList);
    }

    @Override
    public List<ContractRelativeDTO> getContractTraceList(Integer contractId) {
        ContractEntity contractEntity = contractDao.getContractById(contractId);
        if (null == contractEntity) {
            return new ArrayList<>();
        }
        //根合同信息
        ContractEntity rootContractEntity = 0 == contractEntity.getParentId() ? contractEntity :
                this.getRootContract(contractEntity);
        if (null == rootContractEntity) {
            return new ArrayList<>();
        }
        log.info("合同溯源-根合同ID:" + rootContractEntity.getId());
        ContractRelativeDTO rootContractDto = this.convertContractDto(rootContractEntity);
        List<ContractRelativeDTO> contractRelativeDTOList = new ArrayList<>();
        contractRelativeDTOList.add(rootContractDto);

        this.getSonContractList(rootContractDto, contractRelativeDTOList);
        log.info("合同溯源-总合同条数:" + contractRelativeDTOList.size());
        contractRelativeDTOList.sort(Comparator.comparing(ContractRelativeDTO::getCreatedAt));
        return contractRelativeDTOList;
    }

    @Override
    public ContractStructureEntity getContractStructure(Integer contractId) {
        return contractStructureDao.getContractStructure(contractId);
    }

    @Override
    public List<ContractDeliveryVO> getDeliveryContractByContractId(Integer contractId) {
        // 1.获取仓单合同
        ContractEntity contractEntity = contractDao.getContractById(contractId);
        String warrantGoodsName = contractEntity.getGoodsName();
        // 2.查询提货合同信息
        List<ContractEntity> contractEntities = contractDao.getContractByPid(contractId);
        // 3.检查处理数据组装
        List<ContractDeliveryVO> contractDeliveryVOS = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contractEntities)) {
            for (ContractEntity contract : contractEntities) {
                ContractDeliveryVO deliveryVO = new ContractDeliveryVO();
                BeanUtils.copyProperties(contract, deliveryVO);
                deliveryVO.setWarrantGoodsName(warrantGoodsName);
                contractDeliveryVOS.add(deliveryVO);
            }
        }
        return contractDeliveryVOS;
    }

    @Override
    public IPage<ContractStructureEntity> queryContractStructure(QueryDTO<ContractBO> queryDTO) {
        return contractStructureDao.queryContractStructure(queryDTO);
    }

    @Override
    public IPage<ContractEntity> queryContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO) {
        return contractDao.queryContractsByDomainCode(queryDTO);
    }

    @Override
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId) {
        return changeEquityDao.getChangeContractEquityDetailByNotApprove(contractId);
    }

    @Override
    public IPage<ContractEntity> queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO, Integer companyId) {
        return contractDao.queryContractsColumbus(queryDTO, companyId);
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO,
                                                             List<Integer> cannotDeliveryGoodsIdList,
                                                             List<String> cannotDeliveryFactoryList,
                                                             List<Integer> cannotDeliveryTypeIdList) {
        return contractDao.getDeliveryApplyContractList(deliveryApplyContractQO, cannotDeliveryGoodsIdList
                , cannotDeliveryFactoryList, cannotDeliveryTypeIdList);
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO,
                                                              List<Integer> cannotDeliveryGoodsIdList,
                                                              List<String> cannotDeliveryFactoryList,
                                                              List<Integer> cannotDeliveryTypeIdList) {
        List<ContractEntity> deliveryApplyContractList = contractDao.getDeliveryApplyContractGroup(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                cannotDeliveryFactoryList,
                cannotDeliveryTypeIdList);
        return deliveryApplyContractList;
    }

    @Override
    public ContractStructureEntity getContractStructureById(Integer contractId) {

        LambdaQueryWrapper<ContractStructureEntity> structureWrapper = new LambdaQueryWrapper<>();
        structureWrapper.eq(ContractStructureEntity::getContractId, contractId).orderBy(true, true, ContractStructureEntity::getContractId);
        return contractStructureDao.getOne(structureWrapper);
    }

    @Override
    public ContractPriceEntity getContractPriceByContractId(Integer contractId) {
        return contractPriceService.getContractPriceEntityContractId(contractId);
    }

    @Override
    public List<ContractEntity> getCargoRightsContractById(Integer contractId) {
        return contractDao.getCargoRightsContractById(contractId);
    }

    @Override
    public List<String> judgeCategory3InProcessContractForSite(List<Integer> category3List, String siteCode) {
        List<ContractEntity> contractEntityList = contractDao.getContractByCategory3List(category3List, siteCode);
        List<ContractEntity> inProgressContractList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractEntityList)) {
            inProgressContractList = contractEntityList.stream()
                    .filter(it -> {
                        return !ContractStatusEnum.getFinalContractStatus().contains(it.getStatus());
                    })
                    .filter(it -> {
                        return BigDecimalUtil.isGreaterEqualThanZero(it.getContractNum().subtract(it.getTotalBuyBackNum()).subtract(it.getWarrantCancelCount()));
                    })
                    .collect(Collectors.toList());
        }
        return inProgressContractList.stream()
                .map(ContractEntity::getCategory3).distinct()
                .map(GoodsCategoryEnum::getDesc).collect(Collectors.toList());
    }

    @Override
    public List<ContractStructureEntity> getByStructureContractCode(String contractCode) {
        return contractStructureDao.getByContractCode(contractCode);
    }

    @Override
    public ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        return contractHistoryDao.getContractHistoryEntity(contractId, mainVersion);
    }

    @Override
    public ContractMdmInfoDTO getContractMdmInfo(String contractCode) {
        List<ContractEntity> contractEntityList = contractDao.getByContractCode(contractCode);
        if (CollectionUtils.isEmpty(contractEntityList)) {
            return null;
        }

        ContractEntity contractEntity = contractEntityList.get(0);
        return contractEntity.getStatus() != ContractStatusEnum.MODIFYING.getValue()
                ? contractMapper.getContractMdmInfo(contractEntity.getId())
                : contractMapper.getContractHisMdmInfo(contractEntity.getId());
    }

    @Override
    public List<ContractEntity> getContractByStatus(String status, List<String> contractNumbers) {
        return contractDao.getContractByStatus(status, contractNumbers);
    }

    @Override
    public List<ContractEntity> getContractByPurchase(ContractEntity purchaseContract) {
        return contractDao.getContractByPurchase(purchaseContract);
    }

    @Override
    public List<ContractStructureEntity> queryContractStructureList(List<Integer> contractIds) {
        LambdaQueryWrapper<ContractStructureEntity> structureWrapper = new LambdaQueryWrapper<>();
        structureWrapper.in(ContractStructureEntity::getContractId, contractIds).orderBy(true, true, ContractStructureEntity::getContractId);
        return contractStructureDao.list(structureWrapper);
    }

    @Override
    public List<ContractEntity> getWashOutList(Integer contractId) {
        return contractDao.getWashOutList(contractId);
    }

    @Override
    public List<ContractStructureEntity> verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO) {
        return contractStructureDao.verifyContractStructureNum(verifyContractStructureNumDTO);
    }

    /**
     * 获取付款条件代码
     *
     * @param payConditionId
     * @return
     */
    private String getPayConditionCode(Integer payConditionId) {
        String payConditionCode = "";
        Result payConditionResult = payConditionFacade.getPayConditionById(payConditionId);
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            payConditionCode = payConditionEntity.getCode();
        }
        return payConditionCode;
    }


    /**
     * 转换合同信息 TODO 待有优化
     *
     * @param contractEntity
     * @return
     */
    private ContractRelativeDTO convertContractDto(ContractEntity contractEntity) {
        List<TradeTicketEntity> tradeTicketEntityList = tradeTickService.getByContractId(contractEntity.getId());
        if (CollectionUtils.isNotEmpty(tradeTicketEntityList)) {
            tradeTicketEntityList = tradeTicketEntityList.stream()
                    .filter(it -> {
                        return IsDeletedEnum.NOT_DELETED.getValue().equals(it.getIsDeleted());
                    })
                    .sorted(Comparator.comparing(TradeTicketEntity::getId))
                    .collect(Collectors.toList());
        }
        TradeTicketEntity tradeTicketEntity = tradeTicketEntityList.get(0);
        //当前合同信息
        ContractRelativeDTO contractRelativeDTO = new ContractRelativeDTO()
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setContractNum(tradeTicketEntity.getAfterContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                .setParentContractNum(tradeTicketEntity.getBeforeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                .setParentId(contractEntity.getParentId())
                .setFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setSiteName(contractEntity.getSiteName())
                //contractSource、TradeType取值TT信息
                .setContractSource(tradeTicketEntity.getContractSource())
                .setContractSourceInfo(ContractActionEnum.getDescByValue(tradeTicketEntity.getContractSource()))
                .setTradeType(tradeTicketEntity.getTradeType())
                .setTradeTypeInfo(ContractTradeTypeEnum.getDescByValue(tradeTicketEntity.getTradeType()))
                .setContractStatus(contractEntity.getStatus())
                .setCreatedBy(employFacade.getEmployById(contractEntity.getCreatedBy()).getName())
                .setCreatedAt(tradeTicketEntity.getCreatedAt())
                .setTradeTicketEntity(tradeTicketEntity);
        if (ContractTradeTypeEnum.NEW.getValue() == tradeTicketEntity.getTradeType()) {
            if (TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
                contractRelativeDTO.setTradeType(ContractTradeTypeEnum.BUYBACK.getValue());
            }
        }
        if (0 != contractEntity.getParentId()) {
            //父合同信息
            ContractEntity parentContractEntity = contractDao.getContractById(contractEntity.getParentId());
            contractRelativeDTO.setParentContractCode(parentContractEntity.getContractCode())
                    .setParentFactoryCode(parentContractEntity.getDeliveryFactoryCode())
                    .setParentSiteName(parentContractEntity.getSiteName());
            TradeTicketEntity originalContractTT = tradeTickService.getByGroupId(tradeTicketEntity.getGroupId(), tradeTicketEntity.getId());
            if (null != originalContractTT) {
                contractRelativeDTO.setParentContractNum(originalContractTT.getBeforeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                        .setOriginalContractTtId(originalContractTT.getId());
                if (null != originalContractTT.getTradeType()) {
                    contractRelativeDTO.setTradeType(originalContractTT.getTradeType())
                            .setTradeTypeInfo(ContractTradeTypeEnum.getDescByValue(tradeTicketEntity.getTradeType()));
                }
                //拆分或转月的变更字段信息
                Map<String, String> modifyFieldMap = getModifyFieldMap();
                if (Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType(), TTTypeEnum.TRANSFER.getType(), TTTypeEnum.REVERSE_PRICE.getType(), TTTypeEnum.WRITE_OFF.getType(), TTTypeEnum.WRITE_OFF_OM.getType()).contains(tradeTicketEntity.getType())) {
                    TradeTicketDTO tradeTicketDTO = tradeTickService.getTTDetailInfo(tradeTicketEntity.getId());
                    if (null != tradeTicketDTO) {
                        String modifyContent = "";
                        if (null != tradeTicketDTO.getContractModifyTTDTO()) {
                            modifyContent = StringUtils.isNotBlank(tradeTicketDTO.getContractModifyTTDTO().getContent()) ? tradeTicketDTO.getContractModifyTTDTO().getContent() : tradeTicketDTO.getContractModifyTTDTO().getModifyContent();
                        }
                        if (null != tradeTicketDTO.getContractTransferTTDTO()) {
                            modifyContent = StringUtils.isNotBlank(tradeTicketDTO.getContractTransferTTDTO().getContent()) ? tradeTicketDTO.getContractTransferTTDTO().getContent() : tradeTicketDTO.getContractTransferTTDTO().getModifyContent();
                        }
                        List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(modifyContent, CompareObjectDTO.class);
                        if (CollectionUtils.isNotEmpty(compareObjectDTOList)) {
                            if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == (tradeTicketEntity.getTradeType())) {
                                for (CompareObjectDTO compareObjectDTO : compareObjectDTOList) {
                                    String contractNum = "contractNum";
                                    if (contractNum.equals(compareObjectDTO.getName())) {
                                        if (StringUtils.isNotBlank(compareObjectDTO.getAfter())) {
                                            contractRelativeDTO.setContractNum(new BigDecimal(compareObjectDTO.getAfter()).setScale(3, RoundingMode.HALF_UP).toPlainString());
                                        }
                                        if (StringUtils.isNotBlank(compareObjectDTO.getBefore())) {
                                            contractRelativeDTO.setParentContractNum(new BigDecimal(compareObjectDTO.getBefore()).setScale(3, RoundingMode.HALF_UP).toPlainString());
                                        }
                                    }
                                }
                            }

                            String modifyFieldInfo = this.getModifyInfo(modifyFieldMap, compareObjectDTOList);
                            contractRelativeDTO.setModifyFieldInfo(modifyFieldInfo);
                        }
                    }
                }
                if (TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
                    contractRelativeDTO.setTradeType(tradeTicketEntity.getTradeType());
                    List<String> ignoreList = tradeTicketConvertUtil.getIgnoreList();
                    ignoreList.remove("salesType");
                    List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(parentContractEntity, contractEntity, ignoreList, new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(compareObjectDTOList)) {
                        String modifyFieldInfo = this.getModifyInfo(modifyFieldMap, compareObjectDTOList);
                        contractRelativeDTO.setModifyFieldInfo(modifyFieldInfo);
                    }
                }
            }
        }
        return contractRelativeDTO;
    }

    private String getModifyInfo(Map<String, String> modifyFieldMap, List<CompareObjectDTO> compareObjectDTOList) {
        compareObjectDTOList = compareObjectDTOList
                .stream()
                .filter(it -> {
                    return StringUtils.isNotBlank(modifyFieldMap.get(it.getName())) && !it.getBefore().equals(it.getAfter()) && StringUtils.isNotBlank(it.getAfter());
                })
                .collect(Collectors.toList());
        String modifyFieldInfo = compareObjectDTOList.stream().map(it -> {
            return modifyFieldMap.get(it.getName());
        }).collect(Collectors.joining(","));
        return modifyFieldInfo;
    }

    /**
     * 递归查询根合同
     *
     * @param sonContractEntity
     * @return
     */
    private ContractEntity getRootContract(ContractEntity sonContractEntity) {
        sonContractEntity = contractDao.getContractById(sonContractEntity.getParentId());
        if (0 != sonContractEntity.getParentId()) {
            sonContractEntity = this.getRootContract(sonContractEntity);
        }
        return sonContractEntity;
    }

    /**
     * 递归查询所有子合同信息
     *
     * @param rootContractDto
     * @param contractEntityList
     */
    private void getSonContractList(ContractRelativeDTO rootContractDto, List<ContractRelativeDTO> contractEntityList) {
        List<ContractEntity> sonContractList = contractDao.getContractByPid(rootContractDto.getContractId());
        if (CollectionUtils.isNotEmpty(sonContractList)) {
            // add by zengshl需要过滤掉货权合同【不可见并且没有TT】
            sonContractList = sonContractList.stream()
                    .filter(item -> !ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(item.getContractNature())).collect(Collectors.toList());
            log.info("合同溯源-子合同条数:" + sonContractList.size());
            List<ContractRelativeDTO> sonContractDTOList = sonContractList.stream()
                    .map(this::convertContractDto).collect(Collectors.toList());
            contractEntityList.addAll(sonContractDTOList);
            log.info("合同溯源-子合同总条数:" + sonContractList.size());
            for (ContractRelativeDTO sonContractDTO : sonContractDTOList) {
                this.getSonContractList(sonContractDTO, contractEntityList);
            }
        }
    }

    /**
     * 获取可修改的字段
     *
     * @return
     */
    private Map<String, String> getModifyFieldMap() {
        Map<String, String> modifyMap = new HashMap<>();
        modifyMap.put("salesType", "采销类型");
        modifyMap.put("signDate", "签订日期");
        modifyMap.put("customerName", "买方主体简称");
        modifyMap.put("supplierName", "卖方主体简称");
//        modifyMap.put("tradeType", "交易类型");
        modifyMap.put("contractType", "合同类型");
        modifyMap.put("domainCode", "期货合约");
        modifyMap.put("goodsId", "货物");
        modifyMap.put("contractNum", "总数量");
        modifyMap.put("packageWeight", "袋皮扣重");
        modifyMap.put("totalAmount", "合同总金额");
        modifyMap.put("paymentType", "付款方式");
        modifyMap.put("creditDays", "赊销账期");
        modifyMap.put("deliveryFactoryName", "交货工厂");
        modifyMap.put("deliveryType", "交货方式");
        modifyMap.put("shipWarehouseId", "发货库点");
        modifyMap.put("destination", "目的地");
        modifyMap.put("weightTolerance", "溢短装");
        modifyMap.put("deliveryStartTime", "开始交货日");
        modifyMap.put("deliveryEndTime", "截止交货日");
        modifyMap.put("priceEndTime", "点价截止日期");
        modifyMap.put("payConditionId", "付款条件代码");
        modifyMap.put("unitPrice", "含税单价");
        modifyMap.put("extraPrice", "基差价");
        modifyMap.put("forwardPrice", "期货价格");
        modifyMap.put("proteinDiffPrice", "蛋白价格");
        modifyMap.put("compensationPrice", "散粕补贴");
        modifyMap.put("optionPrice", "期权费");
        modifyMap.put("transportPrice", "运费");
        modifyMap.put("liftingPrice", "起吊费");
        modifyMap.put("delayPrice", "滞期费");
        modifyMap.put("temperaturePrice", "高温费");
        modifyMap.put("otherDeliveryPrice", "其他物流费");
        modifyMap.put("buyBackPrice", "和解款折价");
        modifyMap.put("complaintDiscountPrice", "客诉折价");
        modifyMap.put("transferFactoryPrice", "转厂补贴");
        modifyMap.put("otherPrice", "其他补贴");
        modifyMap.put("businessPrice", "商务补贴");
        modifyMap.put("fee", "手续费");
        modifyMap.put("depositRate", "履约保证金比例");
        modifyMap.put("addedDepositRate", "履约保证金点价后补缴");
        modifyMap.put("invoicePaymentRate", "发票后补缴货款比例");
        modifyMap.put("depositReleaseType", "履约保证金释放方式");
        modifyMap.put("weightCheck", "重量检验");
        modifyMap.put("memo", "备注");
        modifyMap.put("usage", "用途");
        return modifyMap;
    }


}
