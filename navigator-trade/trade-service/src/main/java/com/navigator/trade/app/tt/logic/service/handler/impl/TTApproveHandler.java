package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.activiti.pojo.enums.ApproveActionEnum;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.BizApproveStatusEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.ProteinPriceConfigFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.approval.CategoryApprovalModelFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.ContractApproveBizInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import com.navigator.customer.pojo.enums.TTCustomerTradeStatusEnum;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.logic.service.handler.ITTApproveHandler;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.dao.TtAddDao;
import com.navigator.trade.dao.TtModifyDao;
import com.navigator.trade.dao.TtPriceDao;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignReviewDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITTApproveService;
import com.navigator.trade.service.contract.ICancelContractModifyService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contractsign.IContractSignService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TT审批相关处理器
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("Approve_HANDLER")
public class TTApproveHandler implements ITTApproveHandler {
    @Autowired
    protected TradeTicketDao tradeTicketDao;
    @Autowired
    protected TtAddDao ttAddDao;
    @Autowired
    protected TtPriceDao ttPriceDao;
    @Autowired
    protected TtModifyDao ttModifyDao;
    @Autowired
    @Qualifier("TTApproveServiceImpl")
    protected ITTApproveService ttApproveService;

    @Autowired
    protected IContractOperationNewService salesContractOperationService;

    @Autowired
    protected ICancelContractModifyService cancelContractModifyService;

    @Autowired
    protected SalesContractSignHandler salesContractSignHandler;
    @Autowired
    protected IContractPriceService contractPriceService;

    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;

    @Autowired
    protected SkuFacade skuFacade;

    @Autowired
    protected QualityFacade qualityFacade;

    @Value("${tt.residual.risk.switcher}")
    private Boolean residualRiskSwitcher;

    @Value("${tt.approve.switcher}")
    private Boolean approveRuleSwitcher;

    @Autowired
    protected CustomerFacade customerFacade;

    @Autowired
    protected OperationLogFacade operationLogFacade;

    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    protected ContractQueryLogicService contractQueryLogicService;

    @Autowired
    protected SystemRuleFacade systemRuleFacade;

    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private CategoryFacade categoryFacade;

    @Autowired
    protected ITradeTicketQueryService tradeTicketQueryService;

    @Autowired
    private ProteinPriceConfigFacade proteinPriceConfigFacade;
    @Autowired
    protected CategoryApprovalModelFacade categoryApprovalModelFacade;

    @Override
    public ResultCodeEnum submit(Integer ttId, ArrangeContext arrangeContext) {
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        // todo:校验
//        ResultCodeEnum resultCodeEnum = submitBeforeCheck(tradeTicketEntity, ttAddEntity);
//        if (!ResultCodeEnum.OK.equals(resultCodeEnum)) {
//            return resultCodeEnum;
//        }
        //发起审批
        TTDTO ttdto = new TTDTO();
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        tradeTicketConvertUtil.prepareData(tradeTicketEntity, ttAddEntity, contractPriceEntity, ttdto);
        RecordBizOperationDTO recordBizOperationDTO;
        try {
            log.info("check_code_question2 ttId:{} submitBatch2   ", ttId);
            recordBizOperationDTO = startTTApprove(ttId, ttdto, contractPriceEntity);
            // 操作日志
            if (Objects.nonNull(arrangeContext)) {
                arrangeContext.setRecordBizOperationDTO(recordBizOperationDTO);
            }
        } catch (Exception e) {
            return ResultCodeEnum.SUBMIT_FAIL;
        }
        log.warn("----------startTTApprove success! contractCode:{},ttid:{}", tradeTicketEntity.getContractCode(), ttId);
        return ResultCodeEnum.OK;
    }

    @Override
    public void approveTT(ApprovalDTO approvalDTO, Integer ttType) {
        log.info("===================BaseTradeTicketAbstractService.approveTT====================");
        log.info(JSON.toJSONString(approvalDTO));
        //审批
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(approvalDTO.getTtCode());
        /*String processorType = TTHandlerUtil.getTTProcessor(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        String contractSignValue = getByTTValue(processorType).getProcessKey();*/

        String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

        Result result = approve(approvalDTO, processKey, tradeTicketEntity);

        log.info("===================approveResult:===================");
        log.info("approveTTCode:{},result:{}", approvalDTO.getTtCode(), JSON.toJSONString(result));
        if (!result.isSuccess()) {
            log.info(" approveTTCode:{},approve error : {} ", approvalDTO.getTtCode(), JSON.toJSONString(result));
            throw new BusinessException(result.getMessage());
        }
        //根据审批结果更新tt状态
        handleAfterApproving(result, ttType, approvalDTO.getTtCode(), approvalDTO.getMemo());
    }

    public Result approve(ApprovalDTO approvalDTO, String processKey, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        ApproveDTO<Object> approveDTO = new ApproveDTO<>();
        //发起审批
        if (Objects.equals(TTReviewStatusEnum.PASS.getValue(), approvalDTO.getApproveStatus())) {
            approveDTO.setActionValue(ApproveActionEnum.AGREE.getValue());
        } else {
            approveDTO.setActionValue(ApproveActionEnum.REJECT.getValue());
        }

        approveDTO
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(tradeTicketEntity.getSalesType()))
                .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                .setMemo(approvalDTO.getMemo())
                .setUserId(userId)
                .setTaskId(approvalDTO.getTaskId())
                .setProcessKey(processKey)
                .setBizId(tradeTicketEntity.getId())
                .setBizCode(tradeTicketEntity.getCode())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode());
        if (tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType()) && tradeTicketEntity.getTradeType() == ContractTradeTypeEnum.NEW.getValue()) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER);
        }
        return ttApproveService.approve(approveDTO);
    }

    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        log.info("handleAfterApprovingResult:{}", JSON.toJSONString(result.getData()));
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            log.info("check_approve_question ticketCode:{} handleAfterApproving finish", ttCode);
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());
            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }
        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }


    public void handleCancelOrRejectResult(TradeTicketEntity tradeTicketEntity, String memo) {
        //合同作废
        ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
        contractModifyDTO.setTtId(tradeTicketEntity.getId());
        contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
        contractModifyDTO.setTtType(tradeTicketEntity.getType());
        contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
        cancelContractModifyService.cancelContractModify(contractModifyDTO);
        log.info("========== handleCancelOrRejectResult.ttId:{}", tradeTicketEntity.getId());
        log.info("========== handleCancelOrRejectResult.contractModifyDTO:{}", JSON.toJSONString(contractModifyDTO));
        log.info("check_code_question  invalidTTById ");
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), memo, tradeTicketEntity.getId());
        //作废协议
        invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, memo, tradeTicketEntity.getSignId());
        if (tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())) {
            TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(tradeTicketEntity.getId());
            TTModifyEntity modifyByRelationId = ttModifyDao.getModifyByRelationTTId(ttModifyEntity.getRelationId(), tradeTicketEntity.getId());
            log.info("========== handleCancelOrRejectResult.modifyByRelationId:{}", JSON.toJSONString(modifyByRelationId));
            if (modifyByRelationId != null) {
                log.info("check_code_question  invalidTTById ");
                tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), "关联作废:" + memo, modifyByRelationId.getTtId());
                TradeTicketEntity tradeTicketEntityById = tradeTicketDao.getTradeTicketEntityById(modifyByRelationId.getTtId());
                log.info("========== handleCancelOrRejectResult.tradeTicketEntityById:{}", JSON.toJSONString(tradeTicketEntityById));
                if (null != tradeTicketEntityById.getSignId() && tradeTicketEntityById.getSignId() != 0) {
                    invalidContractSign(modifyByRelationId.getTtId(), tradeTicketEntityById, "关联作废:" + memo, tradeTicketEntityById.getSignId());
                    cancelActiviti("关联作废:" + memo, JwtUtils.getCurrentUserId(), tradeTicketEntityById);
                }
            }
        }
    }

    public void handleApprovingResult(TradeTicketEntity tradeTicketEntity, String procInstStatus) {
        BizApproveStatusEnum bizApproveStatusEnum = BizApproveStatusEnum.getByDesc(procInstStatus);
        log.info("=======>tradeTicketEntityCode:{}handleApprovingResult.procInstStatus;{}", tradeTicketEntity.getCode(), procInstStatus);
        switch (bizApproveStatusEnum) {
            case A_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_A_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            case B_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_B_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            case C_Approving:
            case CEO_Approving:
            case CFO_Approving:
                tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_C_SIGN.getValue(), tradeTicketEntity.getCode());
                break;
            default:
                break;
        }
    }

    protected void invalidContractSign(Integer ttId, TradeTicketEntity tradeTicketEntity, String memo, Integer signId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
        contractSignReviewDTO.setTtId(ttId)
                .setReviewRemark(memo)
                .setContractSignId(signId)
        ;
        contractSignService.invalidContractSign(contractSignReviewDTO);
    }


    public void cancelActiviti(String memo, String userId, TradeTicketEntity tradeTicketEntity) {
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setTaskId("");
        String code = tradeTicketEntity.getCode();
        approveDTO
                .setUserId(userId)
                .setBizCode(code)
                .setMemo(memo)
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(tradeTicketEntity.getSalesType()))
                .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                .setActionName("TT撤回");
        ;
        log.info("========== cancelActiviti.approveDTO:{}", JSON.toJSONString(approveDTO));
        ttApproveService.cancel(approveDTO);
    }


    private ResultCodeEnum submitBeforeCheck(TradeTicketEntity tradeTicketEntity, TTAddEntity ttAddEntity) {
        // 只校验现货
        if (!BuCodeEnum.ST.getValue().equals(tradeTicketEntity.getBuCode())) {
            return ResultCodeEnum.OK;
        }
        //1、信息不完整则失败
        if (0 == ttAddEntity.getCompletedStatus()) {
            return ResultCodeEnum.NOT_COMPLETED;
        }
        //2、货品配置有误则失败
//        Integer goodsPackageId = ttAddEntity.getGoodsPackageId();
//        Integer goodsSpecId = ttAddEntity.getGoodsSpecId();
//        Integer goodsCategoryId = ttAddEntity.getGoodsCategoryId();
//        GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO();
//        goodsSpecDTO.setCategoryId(goodsCategoryId);
//        goodsSpecDTO.setPackageId(goodsPackageId);
//        goodsSpecDTO.setSpecId(goodsSpecId);
//        Result result = goodsFacade.queryGoodsTaxRate(goodsSpecDTO);
//        if (result == null || result.getCode() != ResultCodeEnum.OK.getCode()) {
//            return ResultCodeEnum.GOODS_NOT_COMPLETED;
//        }
        SkuEntity skuEntity = skuFacade.getSkuById(tradeTicketEntity.getGoodsId());
        if (null == skuEntity || !DisableStatusEnum.ENABLE.getValue().equals(skuEntity.getStatus())) {
            return ResultCodeEnum.GOODS_NOT_COMPLETED;
        }
        //3、校验付款代码是否禁用
        if (tradeTicketEntity.getPayConditionId() != null) {
            try {
                checkPaymentCode(tradeTicketEntity.getPayConditionId());
            } catch (Exception e) {
                return ResultCodeEnum.PAYCONDITION_NOT_COMPLETED;
            }
        }

        //4、校验质量指标
        /*QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        Integer customerId = tradeTicketEntity.getCustomerId();
        if (tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            customerId = tradeTicketEntity.getSupplierId();
        }
        qualityInfoDTO
                .setGoodsCategoryId(tradeTicketEntity.getSubGoodsCategoryId())
                .setFactoryCode(ttAddEntity.getDeliveryFactoryCode())
                .setWarehouseId(ttAddEntity.getShipWarehouseId())
                .setUsage(tradeTicketEntity.getUsage())
                .setSpecId(ttAddEntity.getGoodsSpecId())
                .setSalesType(tradeTicketEntity.getSalesType())
                .setCustomerId(customerId);
        Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            return ResultCodeEnum.QUALITY_NOT_COMPLETED;
        }*/
        // 5、校验剩余风险使用
        //Case-1002453: RR status in navigator-只校验新增（修改保存TT提交不判断），Author:NaNa,Date:20240522 start
        if (null != residualRiskSwitcher && residualRiskSwitcher && TTTypeEnum.NEW.getType().equals(tradeTicketEntity.getType())) {
            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                CrisGlobalEntity crisGlobalEntity = customerFacade.getCustomerResidualRiskInfo(tradeTicketEntity.getCustomerId());
                if (null == crisGlobalEntity) {
                    log.info("无法获取该客户RR limit数据，TT无法提交，请检查客户主数据,提交失败,ttId:{},customerId:{}", tradeTicketEntity.getId(), tradeTicketEntity.getCustomerId());
                    return ResultCodeEnum.RISK_RESIDUAL_NOT_EXIST;
                }
                //剩余风险使用
                BigDecimal riskResidue = crisGlobalEntity.getResidualRiskResidue();
                if (!TTCustomerTradeStatusEnum.getCommonTradeStatusList().contains(crisGlobalEntity.getTradeStatus())) {
                    return ResultCodeEnum.RISK_RESIDUAL_NOT_GET;
                } else if (TTCustomerTradeStatusEnum.NO_TRADE.getValue().equals(crisGlobalEntity.getTradeStatus())) {
                    return ResultCodeEnum.RISK_RESIDUAL_CUSTOMER_NO_TRADE;
                } else if (TTCustomerTradeStatusEnum.getJudgeRrNumTradeStatusList().contains(crisGlobalEntity.getTradeStatus()) && riskResidue.compareTo(BigDecimal.ZERO) < 0) {
                    // 其中RR Residue = RR Limit - RR Usage
                    // Residue ≤ 0且RR Usage > 150kUSD时，若超额则报错提示：“该客户RR limit已超过平台最大限额，请联系风控部门提额后提交”
                    BigDecimal riskUsage = crisGlobalEntity.getResidualRiskUsage();
                    if (riskUsage.compareTo(new BigDecimal(150)) > 0) {
                        return ResultCodeEnum.RISK_RESIDUAL_USAGE_OVER;
                    }
                    if (riskUsage.compareTo(new BigDecimal(150)) <= 0) {
                        return ResultCodeEnum.RISK_RESIDUAL_USAGE_OFF;
                    }
                }
            }

        }
        //Case-1002453: RR status in navigator-只校验新增（修改保存TT提交不判断），Author:NaNa,Date:20240522 end
        //CaseId-1002453: RR status in navigator，Author By NaNa

        return ResultCodeEnum.OK;
    }

    @Override
    public RecordBizOperationDTO startTTApprove(Integer ttId, TTDTO ttDto, ContractPriceEntity contractPriceEntity) {
        log.info("===>startTTApproveLog,ttId:{},startTTApprove:{}", ttId, JSON.toJSONString(ttDto));
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        Integer salesType = tradeTicketEntity.getSalesType();

        if (null == contractPriceEntity) {
            contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        }

        //组装审批数据
        ApproveDTO approveDTO = buildApproveDTO(ttId, ttDto, tradeTicketEntity, salesType, contractPriceEntity);

        //审批规则适配
        approveDTO = ttApproveService.adaptApproveRule(approveDTO);
        int value = approveDTO.getApproveRuleValue();
        //记录日志
        operationLogFacade.saveTraceLog(new TraceLogDTO(ttId.toString(), "startTTApprove.buildApproveDTO", JSON.toJSONString(approveDTO)));

        //开关
        if (null == approveRuleSwitcher || !approveRuleSwitcher) {
            value = getApprovalValue(ttId, ttDto, tradeTicketEntity);
            //组装审批数据
            approveDTO = getApproveDTO(ttId, ttDto, tradeTicketEntity, salesType, value);
            //记录过程数据
            operationLogFacade.saveTraceLog(new TraceLogDTO(ttId.toString(), "startTTApprove.buildApproveDTO(OLD)", JSON.toJSONString(approveDTO)));
        }

        Result result = ttApproveService.startTTApprove(approveDTO);
        if (!result.isSuccess()) {
            log.warn("发起审批失败,提交失败,result:{}", JSON.toJSONString(result));
            throw new BusinessException(ResultCodeEnum.START_APPROVAL_ERROR);
        }
        log.info("startTTApprove.result:{}", JSON.toJSONString(result));

        // 更改tt状态,更新审批规则
        if (Objects.equals(ContractApproveRuleEnum.NONE.getValue(), value)) {
            //免签
            log.info("check_code_question2 ttId:{} BaseTradeTicketAbstractService  startTTApprove6   ", ttId);
            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WITHOUT_APPROVE.getValue(), tradeTicketEntity.getCode(), value);

            //更新合同状态
            if (ContractActionEnum.NEW.getActionValue() != tradeTicketEntity.getContractSource()
                    && ttDto.getSalesContractReviseTTDTO() != null) {
                ttDto.getSalesContractReviseTTDTO().setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
        } else {
            //审批中
            log.info("check_code_question2 ttId:{} BaseTradeTicketAbstractService startTTApprove5   ", ttId);
            tradeTicketDao.updateTTInfoByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.WAIT_A_SIGN.getValue(), tradeTicketEntity.getCode(), value);
        }

        ApproveResultDTO approveResultDTO = JSON.parseObject(JSON.toJSONString(result.getData()), ApproveResultDTO.class);
        RecordBizOperationDTO recordBizOperationDTO = new RecordBizOperationDTO();
        recordBizOperationDTO.setApproveDTO(approveDTO);
        recordBizOperationDTO.setApproveResultDTO(approveResultDTO);
        return recordBizOperationDTO;
    }

    private int getApprovalValue(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity) {
        int value = ContractApproveRuleEnum.NONE.getValue();

        switch (ContractActionEnum.getByType(tradeTicketEntity.getContractSource())) {
            case NEW:
                //新增审批流程
                SalesContractAddTTDTO salesContractAddTTDTO = ttDto.getSalesContractAddTTDTO();
                String unitPrice = salesContractAddTTDTO.getUnitPrice();
                value = ttApproveService.calcApproveRuleValue(salesContractAddTTDTO.getGoodsCategoryId(), unitPrice, salesContractAddTTDTO.getContractNum(), new Date(), salesContractAddTTDTO.getDeliveryStartTime());

                //新增基差预警
                if (value == ContractApproveRuleEnum.NONE.getValue() && tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()) {
                    if (this.LOAPriceWarning(ttId)) {
                        value = ContractApproveRuleEnum.A.getValue();
                    }
                }
                break;
            case REVISE:
            case REVISE_CUSTOMER:
                value = ttApproveService.calcApproveRuleValue(ContractActionEnum.REVISE.getActionValue(), ttDto);
                break;
            case SPLIT:
            case SPLIT_CUSTOMER:
                //修改、拆分的审批流程
                value = ttApproveService.calcApproveRuleValue(ContractActionEnum.SPLIT.getActionValue(), ttDto);
                break;
            case STRUCTURE_PRICE_CONFIRM:
            case PRICE_CONFIRM:
            case PRICE_FIXED:
                value = ContractApproveRuleEnum.NONE.getValue();
                break;
            case TRANSFER_CONFIRM:
            case TRANSFER_ALL_CONFIRM:
                ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttDto.getSalesContractTTTransferDTO().getSonContractId());
                if (ContractSalesTypeEnum.PURCHASE.getValue() == ttDto.getSalesContractTTTransferDTO().getSalesType()) {
                    value = ContractApproveRuleEnum.AB.getValue();
                } else {
                    // 已转月次数
                    int transferTimes = contractEntity.getTransferredTimes();
                    // 判断是否是超远期合同
                    int standard = contractEntity.getIsOverForward() == 1 ? 3 : 2;
                    value = transferTimes >= standard ? ContractApproveRuleEnum.AB.getValue() : ContractApproveRuleEnum.A.getValue();
                }
                break;
            case REVERSE_PRICE_CONFIRM:
            case REVERSE_PRICE_ALL_CONFIRM:
            case PUT_BACK:
//            case STRUCTURE_PRICING:
            case BUYBACK:
            case WASHOUT:
                value = ContractApproveRuleEnum.AB.getValue();
                break;
            case CLOSED:
            case INVALID:
                value = ContractApproveRuleEnum.A.getValue();
                break;
            default:
                break;
        }
        return value;
    }

    private ApproveDTO getApproveDTO(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity, Integer salesType, int value) {
        //获得流程定义
        /*ApproveProcessEnum approveProcessEnum = ApproveProcessEnum.getByTradeInfo(
                salesType,
                GoodsCategoryEnum.getByValue(ttDto.getSalesContractAddTTDTO().getCategory2()).name(),
                ContractTradeTypeEnum.getByValue(ttDto.getContractTradeType()).name());*/

        log.info("============ tt.ttId:{}", JSON.toJSONString(ttId));
        log.info("============ tt.contractCode:{}", JSON.toJSONString(tradeTicketEntity.getContractCode()));
        log.info("============ tt.subCategoryId:{}", JSON.toJSONString(tradeTicketEntity.getGoodsCategoryId()));
        log.info("============ tt.categoryId:{}", JSON.toJSONString(tradeTicketEntity.getSubGoodsCategoryId()));
        log.info("============ tt.subCategoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getGoodsCategoryId())));
        log.info("============ tt.categoryEnum:{}", JSON.toJSONString(GoodsCategoryEnum.getByValue(tradeTicketEntity.getSubGoodsCategoryId())));
        ApproveDTO approveDTO = new ApproveDTO();

        log.info("=========================================================category1:{}", tradeTicketEntity.getCategory1());
        log.info("=========================================================category2:{}", tradeTicketEntity.getCategory2());
        log.info("=========================================================category3:{}", tradeTicketEntity.getCategory3());
        CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory1());
        if (null != category1Entity) {
            approveDTO.setCategory1Name(category1Entity.getName());
        }
        CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
        if (null != category2Entity) {
            approveDTO.setCategory2Name(category2Entity.getName());
        }
        if (0 != tradeTicketEntity.getCategory3()) {
            CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());
            if (null != category3Entity) {
                approveDTO.setCategory3Name(category3Entity.getName());
            }
        }

        String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

        log.info("=========================================================processKey:{}", processKey);

        approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                .setBuCode(tradeTicketEntity.getBuCode())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(salesType))
                .setUserId(JwtUtils.getCurrentUserId())
                .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                //.setRoleId("")
                .setCustomerName(tradeTicketEntity.getCustomerName())
                .setCustomerId(tradeTicketEntity.getCustomerId())
                .setSupplierId(tradeTicketEntity.getSupplierId())
                .setSupplierName(tradeTicketEntity.getSupplierName())
                .setCompanyId(tradeTicketEntity.getCompanyId())
                .setCompanyName(tradeTicketEntity.getCompanyName())
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSiteCode(tradeTicketEntity.getSiteCode())
                .setSiteName(tradeTicketEntity.getSiteName())
                .setProcessKey(processKey)
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getModule())
                .setBizId(ttId)
                .setBizCode(tradeTicketEntity.getCode())
                .setReferBizId(tradeTicketEntity.getContractId())
                .setReferBizCode(tradeTicketEntity.getContractCode())
                .setApproveRuleValue(value);


        buildApproveBizData(approveDTO, ttDto, tradeTicketEntity);

        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_ALL);
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_PART);
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER);
        }
        if (tradeTicketEntity.getContractSource().equals(ContractActionEnum.SPLIT.getActionValue())) {
            approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.SPLIT_NORMAL);
        }
        approveDTO.setApproveCause("进审原因：" + approveDTO.getApproveRuleValue());
        return approveDTO;
    }


    private ApproveDTO buildApproveDTO(Integer ttId, TTDTO ttDto, TradeTicketEntity tradeTicketEntity, Integer salesType, ContractPriceEntity contractPriceEntity) {
        ApproveDTO approveDTO = new ApproveDTO();
        Integer contractId = tradeTicketEntity.getContractId();
        ContractDetailInfoDTO contractDetailInfoDTO = null;
        if (null != contractId) {
            contractDetailInfoDTO = contractQueryLogicService.getContractDetailInfoDTO(contractId);
        }
        ttDto.setTtType(tradeTicketEntity.getType());
        SalesContractAddTTDTO addTTDTO = ttDto.getSalesContractAddTTDTO();
        SalesContractReviseTTDTO reviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        SalesContractSplitTTDTO splitTTDTO = ttDto.getSalesContractSplitTTDTO();
        ContractEntity contractEntity = ttDto.getContractEntity();

        BigDecimal addTotalAmout = BigDecimal.ZERO;
        BigDecimal contractTotalAmount = BigDecimal.ZERO;

        //低于基差阈值
        boolean isExtraPriceWarning = false;

        try {
            //获得流程定义
           /* ApproveProcessEnum approveProcessEnum = ApproveProcessEnum.getByTradeInfo(
                    salesType,
                    GoodsCategoryEnum.getByValue(tradeTicketEntity.getCategory2()).name(),
                    ContractTradeTypeEnum.getByValue(ttDto.getContractTradeType()).name());*/
            log.info("=========================================================category1:{}", tradeTicketEntity.getCategory1());
            log.info("=========================================================category2:{}", tradeTicketEntity.getCategory2());
            log.info("=========================================================category3:{}", tradeTicketEntity.getCategory3());
            CategoryEntity category1Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory1());
            if (null != category1Entity) {
                approveDTO.setCategory1Name(category1Entity.getName());
            }
            CategoryEntity category2Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory2());
            if (null != category2Entity) {
                approveDTO.setCategory2Name(category2Entity.getName());
            }
            if (0 != tradeTicketEntity.getCategory3()) {
                CategoryEntity category3Entity = categoryFacade.getBasicCategoryBySerialNo(tradeTicketEntity.getCategory3());
                if (null != category3Entity) {
                    approveDTO.setCategory3Name(category3Entity.getName());
                }
            }

            String processKey = categoryApprovalModelFacade.queryCategoryApprovalModelKeyByCategory2(String.valueOf(tradeTicketEntity.getCategory2()));

            log.info("=========================================================processKey:{}", processKey);

            approveDTO
                    .setTtTypeEnum(TTTypeEnum.getByType(tradeTicketEntity.getType()))
                    .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                    .setBuCode(tradeTicketEntity.getBuCode())
                    .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(salesType))
                    .setUserId(JwtUtils.getCurrentUserId())
                    .setBelongCustomerId(tradeTicketEntity.getBelongCustomerId())
                    .setCustomerName(tradeTicketEntity.getCustomerName())
                    .setCustomerId(tradeTicketEntity.getCustomerId())
                    .setSupplierId(tradeTicketEntity.getSupplierId())
                    .setSupplierName(tradeTicketEntity.getSupplierName())
                    .setCategory1(tradeTicketEntity.getCategory1())
                    .setCategory2(tradeTicketEntity.getCategory2())
                    .setCategory3(tradeTicketEntity.getCategory3())
                    .setSiteCode(tradeTicketEntity.getSiteCode())
                    .setSiteName(tradeTicketEntity.getSiteName())
                    .setCompanyId(tradeTicketEntity.getCompanyId())
                    .setCompanyName(tradeTicketEntity.getCompanyName())
                    .setProcessKey(processKey)
                    .setBizModule(ModuleTypeEnum.TRADE_TICKET.getModule())
                    .setBizId(ttId)
                    .setBizCode(tradeTicketEntity.getCode())
                    .setReferBizId(tradeTicketEntity.getContractId())
                    .setReferBizCode(tradeTicketEntity.getContractCode())
                    .setBuCode(tradeTicketEntity.getBuCode())
            ;
            log.info("=========================================================approveDTO.getProcessKey():{}", approveDTO.getProcessKey());

            //组装审批需要的业务数据
            buildApproveBizData(approveDTO, ttDto, tradeTicketEntity);

            ContractApproveBizInfoDTO contractApproveBizInfoDTO = new ContractApproveBizInfoDTO();
            contractApproveBizInfoDTO.setCategory2(tradeTicketEntity.getCategory2());
//            contractApproveBizInfoDTO.setCategoryId(tradeTicketEntity.getCategory2());
            contractApproveBizInfoDTO.setSalesType(salesType);
            contractApproveBizInfoDTO.setTtType(tradeTicketEntity.getType());
            contractApproveBizInfoDTO.setTradeType(approveDTO.getContractTradeTypeEnum().getValue());
            contractApproveBizInfoDTO.setBuCode(tradeTicketEntity.getBuCode());
            contractApproveBizInfoDTO.setContractType(tradeTicketEntity.getContractType());
            //获取配置值
            SystemRuleDTO systemRuleDTO = new SystemRuleDTO();
            systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
            systemRuleDTO.setRuleCode(SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG.getRuleCode());
            List<SystemRuleVO> listSystemRule = systemRuleFacade.getSystemRule(systemRuleDTO);
            log.info("=========================================================listSystemRule:{}", JSON.toJSONString(listSystemRule));
            listSystemRule.forEach(systemRuleVO -> {
                List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = systemRuleVO.getSystemRuleItemVOList();
                List<SystemRuleVO.SystemRuleItemVO> list = systemRuleItemVOList.stream().filter(i -> String.valueOf(tradeTicketEntity.getCompanyId()).equals(i.getCompanyIds())).collect(Collectors.toList());
                systemRuleVO.setSystemRuleItemVOList(list);
            });
            //解析配置
            if (CollectionUtils.isNotEmpty(listSystemRule)) {
                SystemRuleVO systemRuleVO = listSystemRule.get(0);
                HashMap<String, String> mapItem = new HashMap<>();
                List<SystemRuleVO.SystemRuleItemVO> itemList = systemRuleVO.getSystemRuleItemVOList();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (SystemRuleVO.SystemRuleItemVO systemRuleItemVO : itemList) {
                        mapItem.put(systemRuleItemVO.getRuleItemKey(), systemRuleItemVO.getRuleItemValue());
                    }

                    String maxTotalAmount = mapItem.get(ContractApproveConfigItemEnum.MIN_AMOUNT.name());
                    String minTotalAmount = mapItem.get(ContractApproveConfigItemEnum.MAX_AMOUNT.name());
                    String deliveryDueMonthLimit = mapItem.get(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.name());
                    String remainContractNumLimit = mapItem.get(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.name());

                    contractApproveBizInfoDTO.setMinTotalAmount(StringUtil.isNotEmpty(maxTotalAmount) ? Integer.valueOf(maxTotalAmount) : Integer.valueOf(ContractApproveConfigItemEnum.MIN_AMOUNT.getDefaultValue()));
                    contractApproveBizInfoDTO.setMaxTotalAmount(StringUtil.isNotEmpty(minTotalAmount) ? Integer.valueOf(minTotalAmount) : Integer.valueOf(ContractApproveConfigItemEnum.MAX_AMOUNT.getDefaultValue()));
                    contractApproveBizInfoDTO.setDeliveryDueMonthLimit(StringUtil.isNotEmpty(deliveryDueMonthLimit) ? Integer.valueOf(deliveryDueMonthLimit) : Integer.valueOf(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.getDefaultValue()));
                    contractApproveBizInfoDTO.setRemainContractNumLimit(StringUtil.isNotEmpty(remainContractNumLimit) ? Integer.valueOf(remainContractNumLimit) : Integer.valueOf(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.getDefaultValue()));
                }
            }/* else {
                contractApproveBizInfoDTO.setMinTotalAmount(Integer.valueOf(ContractApproveConfigItemEnum.MIN_AMOUNT.getDefaultValue()));
                contractApproveBizInfoDTO.setMaxTotalAmount(Integer.valueOf(ContractApproveConfigItemEnum.MAX_AMOUNT.getDefaultValue()));
                contractApproveBizInfoDTO.setDeliveryDueMonthLimit(Integer.valueOf(ContractApproveConfigItemEnum.DELIVERY_DUE_MONTH.getDefaultValue()));
                contractApproveBizInfoDTO.setRemainContractNumLimit(Integer.valueOf(ContractApproveConfigItemEnum.REMAIN_CONTRACT_NUMBER.getDefaultValue()));
            }*/
            log.info("=========================================================contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));

            if (null != contractDetailInfoDTO) {
                contractTotalAmount = contractDetailInfoDTO.getTotalAmount();
                contractApproveBizInfoDTO.setDeliveryStartTime(contractDetailInfoDTO.getDeliveryStartTime());
                contractApproveBizInfoDTO.setDeliveryEndTime(contractDetailInfoDTO.getDeliveryEndTime());
                contractApproveBizInfoDTO.setTotalBillNum(contractDetailInfoDTO.getTotalBillNum());
                contractApproveBizInfoDTO.setTotalDeliveryNum(contractDetailInfoDTO.getTotalDeliveryNum());
                contractApproveBizInfoDTO.setContractNum(contractDetailInfoDTO.getContractNum());
            }
            log.info("=========================================================contractDetailInfoDTO===contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
            Integer TTType = TTTypeEnum.NEW.getType();
            //关键价格信息
            if (null != contractPriceEntity
                    && (tradeTicketEntity.getType().equals(TTTypeEnum.NEW.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())
                    || tradeTicketEntity.getType().equals(TTTypeEnum.ASSIGN.getType())
            )) {

                BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), tradeTicketEntity.getType());

                contractApproveBizInfoDTO.setProteinDiffPrice(proteinDiffPrice);
                contractApproveBizInfoDTO.setTransportPrice(contractPriceEntity.getTransportPrice());
                contractApproveBizInfoDTO.setRefineDiffPrice(contractPriceEntity.getRefineDiffPrice());
                contractApproveBizInfoDTO.setBusinessPrice(contractPriceEntity.getBusinessPrice());
                contractApproveBizInfoDTO.setOtherPrice(contractPriceEntity.getOtherPrice());
            }
            log.info("=========================================================TTType===contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
            //关键价格是否变化
            List<CompareObjectDTO> compareObjectDTOList = null;
            if (null != ttDto.getSalesContractReviseTTDTO()) {
                compareObjectDTOList = JSON.parseArray(ttDto.getSalesContractReviseTTDTO().getModifyContent(), CompareObjectDTO.class);
            }
            if (null != ttDto.getSalesContractSplitTTDTO()) {
                compareObjectDTOList = JSON.parseArray(ttDto.getSalesContractSplitTTDTO().getModifyContent(), CompareObjectDTO.class);
                if (ttDto.getSalesContractSplitTTDTO().getAddedSignatureType() == 1) {
                    contractApproveBizInfoDTO.setAddedSignatureType(1);
                }
            }
            log.info("=========================================================compareObjectDTOList===contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
            if (null != compareObjectDTOList) {
                Map<String, CompareObjectDTO> mapObj = BeanCompareUtils.mapCompareObjectDTO(compareObjectDTOList);
                Set<String> nameList = mapObj.keySet();
                //List<String> nameList = compareObjectDTOList.stream().map(CompareObjectDTO::getName).collect(Collectors.toList());
                if (CommonListUtil.notNullOrEmpty(nameList)) {
                    // 对应TT字段中【含税单价】组成的【蛋白价差】数据变化
                    log.info("nameList", JSON.toJSONString(nameList));

                    if (nameList.contains("contractType")) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("contractType");
                        Integer contractType1 = Integer.valueOf(compareObjectDTO.getBefore());
                        Integer contractType2 = Integer.valueOf(compareObjectDTO.getAfter());
                        if (contractType1 == ContractTypeEnum.JI_CHA.getValue()
                                && contractType2 == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
                            contractApproveBizInfoDTO.setContract2To4(true);
                        }
                    }

                    Boolean changed = true;
                    if (nameList.contains("customerId") && salesType == 2) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("customerId");
                        String before = compareObjectDTO.getBefore();
                        String after = compareObjectDTO.getAfter();
                        changed = customerFacade.customerJudge(Integer.parseInt(before), Integer.parseInt(after));
                    }

                    if (nameList.contains("supplierId") && salesType == 1) {
                        CompareObjectDTO compareObjectDTO = mapObj.get("supplierId");
                        String before = compareObjectDTO.getBefore();
                        String after = compareObjectDTO.getAfter();
                        changed = customerFacade.customerJudge(Integer.parseInt(before), Integer.parseInt(after));
                    }
                    //判断是否同集团
                    if (!changed) {
                        contractApproveBizInfoDTO.setCustomerGroupChanged(true);
                    }

                    /*BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), TTType);
                    contractApproveBizInfoDTO.setProteinDiffPriceChanged(proteinDiffPrice.compareTo(BigDecimal.ONE) == 0);*/

                    if (nameList.contains("proteinDiffPrice")) {
                        BigDecimal proteinDiffPrice = this.LOAProteinDiffPrice(tradeTicketEntity.getId(), tradeTicketEntity.getType());
                        contractApproveBizInfoDTO.setProteinDiffPriceChanged(proteinDiffPrice.compareTo(BigDecimal.ONE) == 0);
                    }
                    if (nameList.contains("transportPrice")) {
                        contractApproveBizInfoDTO.setTransportPriceChanged(true);
                    }
                    if (nameList.contains("refineDiffPrice")) {
                        contractApproveBizInfoDTO.setRefineDiffPriceChanged(true);
                    }
                    if (nameList.contains("businessPrice")) {
                        contractApproveBizInfoDTO.setBusinessPriceChanged(true);
                    }
                    if (nameList.contains("otherPrice")) {
                        contractApproveBizInfoDTO.setOtherPriceChanged(true);
                    }
                    // 变更交期 交货结束日期发生变化
                    if (nameList.contains("deliveryEndTime")) {
                        contractApproveBizInfoDTO.setDeliveryEndTimeChanged(true);
                    }
                }
            }
            log.info("=========================================================contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
            if (null != addTTDTO) {
                //如果是新增
                contractApproveBizInfoDTO.setWarrantTradeType(addTTDTO.getWarrantTradeType());
                contractApproveBizInfoDTO.setDeliveryStartTime(addTTDTO.getDeliveryStartTime());
                contractApproveBizInfoDTO.setDeliveryEndTime(addTTDTO.getDeliveryEndTime());
                contractApproveBizInfoDTO.setSignDate(addTTDTO.getSignDate());
            }
            log.info("=========================================================addTTDTO====contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
            if (null != contractEntity && BuCodeEnum.WT.getValue().equals(tradeTicketEntity.getBuCode())) {
                log.info("=========================================================contractEntity.getWarrantTradeType():{}", tradeTicketEntity.getBuCode());
                //合同数据不为空
                contractApproveBizInfoDTO.setWarrantTradeType(contractEntity.getWarrantTradeType());
            }

            switch (TTTypeEnum.getByType(ttDto.getTtType())) {
                case NEW:
                case BUYBACK:
                case ASSIGN:
                    BigDecimal contractNum = new BigDecimal(addTTDTO.getContractNum());
                    addTotalAmout = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, new BigDecimal(addTTDTO.getUnitPrice()));

                    contractApproveBizInfoDTO.setTotalAmount(addTotalAmout);

                    if (null != addTTDTO.getContractType() && ContractTypeEnum.ZAN_DING_JIA.getValue() == Integer.valueOf(addTTDTO.getContractType())) {
                        contractApproveBizInfoDTO.setNewContract3(true);
                    }
                    isExtraPriceWarning = LOAPriceWarning(ttId);
                    break;
                case REVISE:
                    contractApproveBizInfoDTO.setDeliveryStartTime(reviseTTDTO.getDeliveryStartTime());
                    contractApproveBizInfoDTO.setDeliveryEndTime(reviseTTDTO.getDeliveryEndTime());
                    contractApproveBizInfoDTO.setTotalAmount(reviseTTDTO.getTotalAmount());
                    contractApproveBizInfoDTO.setSignDate(reviseTTDTO.getSignDate());
                    break;
                case SPLIT:
                    contractApproveBizInfoDTO.setDeliveryStartTime(splitTTDTO.getDeliveryStartTime());
                    contractApproveBizInfoDTO.setDeliveryEndTime(splitTTDTO.getDeliveryEndTime());
                    contractApproveBizInfoDTO.setTotalAmount(splitTTDTO.getTotalAmount());
                    contractApproveBizInfoDTO.setSignDate(splitTTDTO.getSignDate());
                    break;
                case WASHOUT:
                    BigDecimal washoutContractNum = addTTDTO.getContractNum() == null ? BigDecimal.ZERO : new BigDecimal(addTTDTO.getContractNum());
                    BigDecimal washoutUnitPrice = addTTDTO.getWashoutUnitPrice();
                    if (null != ttDto.getPriceDetailBO() && tradeTicketEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()) {
                        washoutUnitPrice = washoutUnitPrice.add(ttDto.getPriceDetailBO().getForwardPrice());
                    }
                    contractTotalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, washoutContractNum, washoutUnitPrice);
                    contractApproveBizInfoDTO.setTotalAmount(contractTotalAmount);
                    break;

                case CLOSED:
                    log.info("========================================================contractApproveBizInfoDTO.getContractNum():{}", contractApproveBizInfoDTO.getContractNum());
                    log.info("========================================================contractApproveBizInfoDTO.getTotalDeliveryNum():{}", contractApproveBizInfoDTO.getTotalDeliveryNum());
                    BigDecimal contractRemainNum = contractApproveBizInfoDTO.getContractNum()
                            .subtract(contractApproveBizInfoDTO.getTotalDeliveryNum());
                    log.info("========================================================contractDetailInfoDTO.getTotalDeliveryNum():{}", contractDetailInfoDTO.getTotalDeliveryNum());

                    Integer weightTolerance = contractDetailInfoDTO.getWeightTolerance();
                    BigDecimal maxWeightTolerance = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractDetailInfoDTO.getContractNum(), BigDecimalUtil.div(weightTolerance, 100));
                    log.info("========================================================contractRemainNum:{}", contractRemainNum);
                    log.info("========================================================weightTolerance:{}", weightTolerance);
                    log.info("========================================================maxWeightTolerance:{}", maxWeightTolerance);
                    log.info("CLOSED contractApproveBizInfoDTO:{}", JSON.toJSONString(contractApproveBizInfoDTO));
                    if (contractRemainNum.compareTo(new BigDecimal(String.valueOf(contractApproveBizInfoDTO.getRemainContractNumLimit()))) >= 0
                            && BigDecimalUtil.isGreater(contractRemainNum, maxWeightTolerance)) {
                        contractApproveBizInfoDTO.setRemainMuch(true);
                    }
                    break;
                default:
                    break;
            }

            contractApproveBizInfoDTO.setLowExtraPrice(isExtraPriceWarning);

            contractApproveBizInfoDTO.setTtCreatedAt(tradeTicketEntity.getCreatedAt());

            //交期大于等于12个月（配置）
            int deliveryDueMonth = DateTimeUtil.getDiffMonth(contractApproveBizInfoDTO.getSignDate(), contractApproveBizInfoDTO.getDeliveryStartTime());
            log.info("ttCode:{},ttId:{},deliveryDueMonth:{}", tradeTicketEntity.getCode(), ttId, deliveryDueMonth);
            if (deliveryDueMonth >= contractApproveBizInfoDTO.getDeliveryDueMonthLimit()) {
                contractApproveBizInfoDTO.setDeliveryLong(true);
            }
            log.info("ttCode:{},ttId:{}, finalContractApproveBizInfoDTO:{}", tradeTicketEntity.getCode(), ttId, JSON.toJSONString(contractApproveBizInfoDTO));

            approveDTO.setApproveBizInfoDTO(contractApproveBizInfoDTO);
        } catch (Exception e) {
            approveDTO.setMemo("buildApproveDTO exception");
            operationLogFacade.saveTraceLog(new TraceLogDTO(String.valueOf(approveDTO.getReferBizId()), "buildApproveDTO.exception", JSON.toJSONString(e)));
        }

        return approveDTO;
    }


    private void checkPaymentCode(Integer payConditionId) {
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(payConditionId);
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
        } else {
            throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_EXIST);
        }
    }


    public boolean LOAPriceWarning(Integer ttId) {
        TradeTicketEntity tradeTicketEntity = tradeTicketQueryService.getByTtId(ttId);
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);

        if (null == ttAddEntity) {
            return false;
        }

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);

        Integer goodsSpecId = null;
        if (null != ttAddEntity.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(ttAddEntity.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    goodsSpecId = skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId();
                }
            }
        }

        log.info("======================================================================goodsSpecId:{}", goodsSpecId);

        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
        systemRuleDTO.setFactoryCode(ttAddEntity.getDeliveryFactoryCode());
        systemRuleDTO.setCompanyId(tradeTicketEntity.getCompanyId().toString());
        systemRuleDTO.setAttributeValueId(goodsSpecId);
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(ttAddEntity.getDeliveryStartTime()));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = systemRuleFacade.filterBasicPrice(systemRuleDTO);

        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return false;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于基差价
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //基差价
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());

            BigDecimal extraPrice = ruleValue.subtract(memo);
            log.info("=========================================================");
            log.info("不能低于基差价:" + extraPrice);
            log.info("基差价:" + contractPriceEntity.getExtraPrice());
            log.info("基差价是否低于基础价" + (contractPriceEntity.getExtraPrice().compareTo(ruleValue.subtract(memo)) < 0));
            log.info("=========================================================");

            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) < 0;
            } else {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) > 0;
            }
        }
        return false;
    }


    protected void buildApproveBizData(ApproveDTO approveDTO, TTDTO ttdto, TradeTicketEntity tradeTicketEntity) {
        List<ApproveBizInfoDTO> approveBizInfoDTOList = new ArrayList<>();
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(1)
                .setName("salesType")
                .setDisplayName("采/销")
                .setValue(ContractSalesTypeEnum.getByValue(tradeTicketEntity.getSalesType()).getDescription() + "合同")
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                        .setIndex(2)
                        .setName("subCategoryName")
                        .setDisplayName("品种")
                        // BUGFIX：case-1002589 荆州天佳 TJIBSBMP2400175是豆粕合同，但是审批流程显示的是豆油合同 Author: Mr 2024-06-17 Start
                        //.setValue(GoodsCategoryEnum.getByValue(subGoodsCategoryId).getDesc())
                        .setValue(approveDTO.getCategory2Name())
                // BUGFIX：case-1002589 荆州天佳 TJIBSBMP2400175是豆粕合同，但是审批流程显示的是豆油合同 Author: Mr 2024-06-17 End
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(3)
                .setName("tradeTypeName")
                .setDisplayName("交易类型")
                .setValue(TTTypeEnum.getDescByValue(tradeTicketEntity.getType()))
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(4)
                .setName("contractTypeName")
                .setDisplayName("合同类型")
                .setValue(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(5)
                .setName("contractCode")
                .setDisplayName("合同编号")
                .setValue(approveDTO.getReferBizCode())
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(6)
                .setName("customerName")
                .setDisplayName("客户名称")
                .setValue(approveDTO.getCustomerName())
        );
        approveBizInfoDTOList.add(new ApproveBizInfoDTO()
                .setIndex(7)
                .setName("bizDetailDescription")
                .setDisplayName("业务详情")
                .setValue("")
        );
        Object data = JSON.toJSONString(approveBizInfoDTOList);
        approveDTO.setBizData(data);

    }


    public BigDecimal LOAProteinDiffPrice(Integer ttId, Integer TTType) {

        log.info("======================================================================LOAProteinDiffPrice:{}", ttId);
        TradeTicketEntity tradeTicketEntity = tradeTicketQueryService.getByTtId(ttId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);

        String deliveryFactoryCode = null;
        Integer goodsSpecId = null;
        Integer goodsCategoryId = null;
        Integer goodsId = null;
        Date deliveryStartTime = null;

        if (TTType.equals(TTTypeEnum.NEW.getType()) || TTType.equals(TTTypeEnum.ASSIGN.getType())) {
            TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);

            if (null != ttAddEntity) {
                deliveryFactoryCode = ttAddEntity.getDeliveryFactoryCode();
                goodsId = ttAddEntity.getGoodsId();
                deliveryStartTime = ttAddEntity.getDeliveryStartTime();
            }

        } else if (TTType.equals(TTTypeEnum.REVISE.getType()) || TTType.equals(TTTypeEnum.SPLIT.getType())) {
            TTModifyEntity ttAddEntityByTTId = ttModifyDao.getTTModifyEntityByTTId(ttId);

            if (null != ttAddEntityByTTId) {
                deliveryFactoryCode = ttAddEntityByTTId.getDeliveryFactoryCode();
                goodsId = ttAddEntityByTTId.getGoodsId();
                deliveryStartTime = ttAddEntityByTTId.getDeliveryStartTime();
            }
        }
        log.info("======================================================================goodsId:{}", goodsId);

        if (null != goodsId) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(goodsId);
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    goodsSpecId = skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId();
                }
            }
        }

        log.info("======================================================================goodsSpecId:{}", goodsSpecId);
        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getCategory2());
        systemRuleDTO.setFactoryCode(deliveryFactoryCode);
        systemRuleDTO.setAttributeValueId(goodsSpecId);
        systemRuleDTO.setGoodsId(goodsId);
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(deliveryStartTime));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = proteinPriceConfigFacade.filterBasicProtein(systemRuleDTO);

        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return BigDecimal.ZERO;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于蛋白价差范围预警值
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //蛋白价差
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());

            BigDecimal proteinPrice = ruleValue.subtract(memo);
            log.info("=========================================================");
            log.info("不能低于蛋白价差价:" + proteinPrice);
            log.info("蛋白价差:" + contractPriceEntity.getProteinDiffPrice());
            log.info("低于蛋白价差范围预警值" + (contractPriceEntity.getProteinDiffPrice().compareTo(ruleValue.subtract(memo)) < 0));
            log.info("=========================================================");

            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) < 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            } else {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) > 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            }


        }
        return BigDecimal.ZERO;
    }


}
