package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.future.enums.AllocateStatusEnum;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 解约定赔的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
public abstract class BaseContractWashOutAbstractService extends BaseContractAbstractService {

    @Override
    // BUGFIX：case-1002789 解约定赔出现两条重复协议 Author: Mr 2024-10-24 Start
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    // BUGFIX：case-1002789 解约定赔出现两条重复协议 Author: Mr 2024-10-24 End
    public List<TTQueryVO> applyWashOut(ContractWashOutDTO contractWashOutDTO) {
        ContractEntity contractEntity = contractValueObjectService.getContractById(contractWashOutDTO.getContractId());


        ContractEntity originalContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, originalContractEntity);

        // 1.校验合同数据
        checkContractInfo(contractEntity, contractWashOutDTO);

        // 2.处理TT
        List<TTQueryVO> ttQueryVOS = operateTradeTicket(contractEntity, contractWashOutDTO);

        // 3.更新父合同
        operateFatherContract(contractEntity, contractWashOutDTO);

        //  4.记录日志
        recordOperationLog(contractWashOutDTO, contractEntity);


        //合同前后信息变更记录
        updateModifyContent(originalContractEntity, contractEntity, ttQueryVOS, TTTypeEnum.WASHOUT.getType());

        return ttQueryVOS;

    }

    /**
     * 处理父合同信息
     *
     * @param contractEntity     父合同
     * @param contractWashOutDTO 回购dto
     */
    private void operateFatherContract(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO) {
        // 解约定赔数量
        BigDecimal contractNum = contractEntity.getContractNum().subtract(contractWashOutDTO.getWashOutNum());

        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));

        // 解约定赔导致的原合同全部定价
        if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && BigDecimalUtil.isEqual(contractNum, contractEntity.getTotalPriceNum())
                && BigDecimalUtil.isGreaterThanZero(contractNum)) {
            // 原合同需要自动计算加权平均价
            List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : confirmPriceList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                // 更新期货价格
                contractPriceService.updateContractForwardPrice(contractEntity, averagePrice);
            }
        }

        // 合同状态改为修改中
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());

        // 更新合同信息
        contractValueObjectService.updateContractById(contractEntity);


    }

    /**
     * 处理TT信息
     *
     * @param contractEntity     原合同信息
     * @param contractWashOutDTO 回购dto
     */
    protected List<TTQueryVO> operateTradeTicket(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO) {
        TTDTO ttdto = new TTDTO();

        // 处理回购的数据
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
        salesContractAddTTDTO.setContractId(contractEntity.getId());

        // 价格处理
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        // 解约定陪不改变合同价格
        ttdto.setPriceDetailBO(priceDetailBO);

        PriceDetailBO washOutPriceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())
                || (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())
                && totalPriceNum.compareTo(BigDecimal.ZERO) == 0)
        ) {
            washOutPriceDetailBO.setForwardPrice(BigDecimal.ZERO);
        }

        if (contractWashOutDTO.getExtraPrice() != null) {
            washOutPriceDetailBO.setExtraPrice(contractWashOutDTO.getExtraPrice());
        }
        if (contractWashOutDTO.getForwardPrice() != null) {
            washOutPriceDetailBO.setForwardPrice(contractWashOutDTO.getForwardPrice());
        }

        // 解约定赔含税单价
        BigDecimal washoutUnitPrice = contractPriceFacade.calculatePriceBo(washOutPriceDetailBO);

//        BigDecimal contractNum = contractEntity.getContractNum() == null ? BigDecimal.ZERO : contractEntity.getContractNum();
//        if (contractNum.compareTo(totalPriceNum) != 0) {
//            washoutUnitPrice = washoutUnitPrice.subtract(contractPriceEntity.getForwardPrice()).setScale(2, RoundingMode.HALF_UP);
//        }
        salesContractAddTTDTO
                .setWashoutUnitPrice(washoutUnitPrice)
                .setWashoutPriceDetailBO(JSON.toJSONString(washOutPriceDetailBO))
                .setUnitPrice(String.valueOf(contractEntity.getUnitPrice()))
                .setContractNum(String.valueOf(contractWashOutDTO.getWashOutNum()))
                .setSourceContractId(contractEntity.getId());

        // 期货合约
        if (StringUtils.isNotBlank(contractWashOutDTO.getDomainCode())) {
            salesContractAddTTDTO.setDomainCode(contractWashOutDTO.getDomainCode());
        }

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Inherit group_id from parent contract for washout operations
        if (contractEntity.getGroupId() != null) {
            salesContractAddTTDTO.setGroupId(contractEntity.getGroupId());
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

        // 获取处理TT接口
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                contractEntity.getSalesType(),
                TTTypeEnum.WASHOUT.getType(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(ttProcessorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
        List<TTQueryVO> ttQueryVOS = tradeTicketService.saveTT(ttdto).getData();
        ttQueryVOS.forEach(queryVO -> queryVO.setSourceFlag(1));

        return ttQueryVOS;
    }

    /**
     * 校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 解约定赔dto
     */
    protected void checkContractInfo(ContractEntity contractEntity, ContractWashOutDTO contractBuyBackDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 判断客户是否可用
        if (!isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }

        //暂定价合同不可
        if (ContractTypeEnum.ZAN_DING_JIA.getValue() == contractEntity.getContractType()) {
            throw new BusinessException(ResultCodeEnum.ZAN_DING_JIA_CANNOT);
        }

        // 判断此合同及关联合同是否未洗过单
        List<ContractEntity> washOutList = contractDao.getWashOutList(contractEntity.getId());
        if (CollectionUtil.isNotEmpty(washOutList)) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_WASH_OUT_EXCEPTION);
        }

        boolean status = priceAllocateFacade.getNotAllocateByContractId(contractEntity.getId());
        if (status) {
            throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_4);
        }

        /**
         * 1、基差暂定价合同需根据条件判断有定价单时是否可解约定赔
         * 基差暂定价合同解约定赔逻辑：
         * b.定价量=0且 已提货量>0 不可以解约定赔
         * c.定价量<总量 不能解约定赔
         * d.定价量=总量且未操作定价完毕 不能解约定赔
         */
        //定价量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        //已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        //未定价量
        //BigDecimal subtract = contractEntity.getContractNum().subtract(totalPriceNum);
        String key = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
        Object object = redisUtil.get(key);

        if (ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType()) {
            if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum())) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
            }
        }

        if (ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == contractEntity.getContractType()) {
            List<PriceAllocateEntity> priceAllocateEntityList = priceAllocateFacade.getByContractId(contractEntity.getId());
            List<PriceAllocateEntity> list = priceAllocateEntityList.stream().filter(i -> AllocateStatusEnum.WAIT_AUDIT.getValue() == i.getStatus()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException(ResultCodeEnum.ALLOCATE_EXIST);
            }

            //定价量=0且 已提货量>0 不可以解约定赔
            //定价量<总量 不能解约定赔
            //定价量=总量且未操作定价完毕 不能解约定赔
            if (totalPriceNum.compareTo(BigDecimal.ZERO) == 0
                    && totalDeliveryNum.compareTo(BigDecimal.ZERO) > 0) {
                throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_1);
            }

            if (totalPriceNum.compareTo(contractEntity.getContractNum()) < 0
                    && totalPriceNum.compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_2);
            }

            if (totalPriceNum.compareTo(contractEntity.getContractNum()) == 0
                    && Objects.isNull(object)) {
                throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_3);
            }

        }

        // 数量校验:可解约定赔数量≤合同总量-MAX（已定价量/已开单量）
        BigDecimal maxNum = contractEntity.getTotalBillNum();

        if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {

            if (totalPriceNum.compareTo(contractEntity.getContractNum()) == 0
                    && Objects.isNull(object)) {
                throw new BusinessException(ResultCodeEnum.WASHOUT_ERROE_5);
            }

            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum());
//        } else if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
//            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
//        }
            if (BigDecimalUtil.isGreater(contractBuyBackDTO.getWashOutNum(), contractEntity.getContractNum().subtract(maxNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
            }

            Boolean b = contractStructureService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractEntity.getContractNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );

            if (b) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }

        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

    }

    /**
     * 日志处理
     *
     * @param contractWashOutDTO
     * @param contractEntity
     */
    protected abstract void recordOperationLog(ContractWashOutDTO contractWashOutDTO, ContractEntity contractEntity);

}
