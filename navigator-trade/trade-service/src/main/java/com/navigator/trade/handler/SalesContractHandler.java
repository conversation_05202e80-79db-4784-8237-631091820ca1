package com.navigator.trade.handler;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.trade.service.contract.IContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 销售合同变更处理器
 *
 * <AUTHOR>
 */
@Service
public class SalesContractHandler {
    @Autowired
    @Lazy
    private Map<String, IContractService> contractServiceMap;

    public IContractService getStrategy(String processKey) {
        for (String s : contractServiceMap.keySet()) {
            if (s.contains(processKey)) {
                return contractServiceMap.get(s);
            }
        }
        return null;
    }

    /**
     * 获取合同接口
     *
     * @param salesType       销售类型
     * @param tradeType       tradeType类型
     * @param goodsCategoryId 品类名称
     * @return
     */
    public IContractService getStrategy(Integer salesType, Integer tradeType, Integer goodsCategoryId) {
        // 回购特殊处理
        if (ContractTradeTypeEnum.BUYBACK.getValue() == tradeType) {
            if (ContractSalesTypeEnum.SALES.getValue() == salesType) {
                salesType = ContractSalesTypeEnum.PURCHASE.getValue();
            }
        }

//        String processKey = GoodsCategoryEnum.getByValue(goodsCategoryId).getCode()
//                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
//                + "_CONTRACT_" +
//                ContractTradeTypeEnum.getByValue(tradeType).getContractBisinessType().getCode();
        String processKey = "SBM"
                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_CONTRACT_" +
                ContractTradeTypeEnum.getByValue(tradeType).getContractBisinessType().getCode();
        return getStrategy(processKey);
    }

}
