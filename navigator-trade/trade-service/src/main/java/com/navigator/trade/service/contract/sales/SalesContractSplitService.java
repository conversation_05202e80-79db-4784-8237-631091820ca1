package com.navigator.trade.service.contract.sales;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.Impl.BaseContractSplitAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 豆粕合同拆分的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_S_CONTRACT_SPLIT,SBO_S_CONTRACT_SPLIT")
public class SalesContractSplitService extends BaseContractSplitAbstractService {
    @Resource
    public IContractOperationNewService salesContractOperationService;

    @Override
    protected void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 记录操作记录
        salesContractOperationService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.SPLIT_SALES_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                SystemEnum.MAGELLAN.getValue());
    }

    @Override
    protected ContractEntity operateSonContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 判断主题是否发生变更
        if (!contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId())) {
            contractModifyDTO.setContractSource(ContractActionEnum.SPLIT_CUSTOMER.getActionValue());


            if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {
                Boolean b = contractStructureService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                        .setCustomerId(contractEntity.getCustomerId())
                        .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                        .setDomainCode(contractEntity.getDomainCode())
                        .setContractNum(contractEntity.getContractNum())
                        .setCompanyId(contractEntity.getCompanyId())
                );
                if (b) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
                }
            }
        } else {
            contractModifyDTO.setContractSource(ContractActionEnum.SPLIT.getActionValue());
        }
        contractModifyDTO.setParentContractEntity(contractEntity);

        // 生成子合同
        IContractService createService = salesContractHandler.getStrategy(
                contractEntity.getSalesType(),
                ContractTradeTypeEnum.NEW.getValue(),
                contractEntity.getGoodsCategoryId());

        return createService.createContractByModify(contractModifyDTO);
    }

}
