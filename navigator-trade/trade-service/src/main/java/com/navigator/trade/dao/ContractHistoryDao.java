package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.ContractHistoryMapper;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:44
 */
@Dao
public class ContractHistoryDao extends BaseDaoImpl<ContractHistoryMapper, ContractHistoryEntity> {

    public ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        ContractHistoryEntity contractHistoryEntity = this.baseMapper.selectOne(
                Wrappers.<ContractHistoryEntity>lambdaQuery()
                        .eq(ContractHistoryEntity::getContractId, contractId)
                        .eq(ContractHistoryEntity::getMainVersion, mainVersion)
        );

        return contractHistoryEntity;
    }
}
