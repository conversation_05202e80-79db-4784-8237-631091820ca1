package com.navigator.trade.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.ContractVOMapper;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.entity.ContractVOEntity;
import com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PriceCompleteEnum;
import com.navigator.trade.pojo.qo.ContractQO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 合同视图结构的查询
 *
 * <AUTHOR>
 * @since 20240722
 */
@Dao
public class ContractVODao extends BaseDaoImpl<ContractVOMapper, ContractVOEntity> {

    /**
     * 查询合同视图数据
     *
     * @param contractBO
     * @return
     */
    public List<ContractVOEntity> getContractByCondition(ContractBO contractBO) {

        LambdaQueryWrapper<ContractVOEntity> queryWrapper = Wrappers.<ContractVOEntity>lambdaQuery()
                .eq(null != contractBO.getSalesType(), ContractVOEntity::getSalesType, contractBO.getSalesType())
                .eq(null != contractBO.getGoodsCategoryId(), ContractVOEntity::getGoodsCategoryId, contractBO.getGoodsCategoryId())
                .eq(null != contractBO.getStatus(), ContractVOEntity::getStatus, contractBO.getStatus())
                .eq(ObjectUtil.isNotEmpty(contractBO.getDeliveryFactoryName()), ContractVOEntity::getDeliveryFactoryCode, contractBO.getDeliveryFactoryName())
                .between(ObjectUtil.isNotEmpty(contractBO.getSignStartDate()) && ObjectUtil.isNotEmpty(contractBO.getSignEndDate()), ContractVOEntity::getSignDate, contractBO.getSignStartDate(), contractBO.getSignEndDate())
                .eq(ContractVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .notIn(ContractVOEntity::getContractType, Collections.singletonList(ContractTypeEnum.STRUCTURE.getValue()));

        // 交货时间
        String deliveryStartTime = contractBO.getDeliveryStartTime();
        String deliveryEndTime = contractBO.getDeliveryEndTime();
        if (ObjectUtil.isNotEmpty(deliveryStartTime) && ObjectUtil.isNotEmpty(deliveryEndTime)) {
            queryWrapper
                    .ge(ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ContractVOEntity::getDeliveryEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1));

        } else {
            queryWrapper
                    .ge(ObjectUtil.isNotEmpty(contractBO.getDeliveryStartTime()), ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()), ContractVOEntity::getDeliveryEndTime, ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()) ? DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1) : deliveryEndTime);
        }
        return this.baseMapper.selectList(queryWrapper);
    }

    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 start
    /**
     * 查询合同视图数据（支持多选状态）
     *
     * @param contractBO
     * @return
     */
    public List<ContractVOEntity> getContractByConditionMultiStatus(ContractBO contractBO) {

        LambdaQueryWrapper<ContractVOEntity> queryWrapper = Wrappers.<ContractVOEntity>lambdaQuery()
                .eq(null != contractBO.getSalesType(), ContractVOEntity::getSalesType, contractBO.getSalesType())
                .eq(null != contractBO.getGoodsCategoryId(), ContractVOEntity::getGoodsCategoryId, contractBO.getGoodsCategoryId())
                .eq(ObjectUtil.isNotEmpty(contractBO.getDeliveryFactoryName()), ContractVOEntity::getDeliveryFactoryCode, contractBO.getDeliveryFactoryName())
                .between(ObjectUtil.isNotEmpty(contractBO.getSignStartDate()) && ObjectUtil.isNotEmpty(contractBO.getSignEndDate()), ContractVOEntity::getSignDate, contractBO.getSignStartDate(), contractBO.getSignEndDate())
                .eq(ContractVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .notIn(ContractVOEntity::getContractType, Collections.singletonList(ContractTypeEnum.STRUCTURE.getValue()));

        // 多选状态处理
        if (ObjectUtil.isNotEmpty(contractBO.getStatusList()) && !contractBO.getStatusList().isEmpty()) {
            queryWrapper.in(ContractVOEntity::getStatus, contractBO.getStatusList());
        } else if (null != contractBO.getStatus()) {
            // 兼容原有单选状态
            queryWrapper.eq(ContractVOEntity::getStatus, contractBO.getStatus());
        }

        // 交货时间
        String deliveryStartTime = contractBO.getDeliveryStartTime();
        String deliveryEndTime = contractBO.getDeliveryEndTime();
        if (ObjectUtil.isNotEmpty(deliveryStartTime) && ObjectUtil.isNotEmpty(deliveryEndTime)) {
            queryWrapper
                    .ge(ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ContractVOEntity::getDeliveryEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1));

        } else {
            queryWrapper
                    .ge(ObjectUtil.isNotEmpty(contractBO.getDeliveryStartTime()), ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()), ContractVOEntity::getDeliveryEndTime, ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()) ? DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1) : deliveryEndTime);
        }
        return this.baseMapper.selectList(queryWrapper);
    }
    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 end

    /**
     * 查询合同列表 | 仓单合同 | 销售合同 | 采销类型
     *
     * @param queryDTO
     * @return
     */
    public IPage<ContractVOEntity> queryContract(QueryDTO<ContractQO> queryDTO) {

        ContractQO contractQO = queryDTO.getCondition();
        // 处理查询条件
        boolean bo = null != contractQO.getPriceEndType() && ObjectUtil.isNotEmpty(contractQO.getPriceEndTime()) && ContractPriceEndTypeEnum.DATE.getValue() == contractQO.getPriceEndType();
        LambdaQueryWrapper<ContractVOEntity> queryWrapper = Wrappers.<ContractVOEntity>lambdaQuery()
                .eq(null != contractQO.getSalesType(), ContractVOEntity::getSalesType, contractQO.getSalesType())
                .like(ObjectUtil.isNotEmpty(contractQO.getContractCode()), ContractVOEntity::getContractCode, "%" + (ObjectUtil.isNotEmpty(contractQO.getContractCode()) ? contractQO.getContractCode().trim() : contractQO.getContractCode()) + "%")
                .eq(ObjectUtil.isNotEmpty(contractQO.getDeliveryFactoryName()), ContractVOEntity::getDeliveryFactoryCode, contractQO.getDeliveryFactoryName())
                .eq(null != contractQO.getDeliveryType(), ContractVOEntity::getDeliveryType, contractQO.getDeliveryType())
                .like(StringUtils.isNotBlank(contractQO.getCustomerName()), contractQO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? ContractVOEntity::getCustomerName : ContractVOEntity::getSupplierName, "%" + contractQO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(contractQO.getSupplierId()), ContractVOEntity::getSupplierId, contractQO.getSupplierId())
                .eq(StringUtils.isNotBlank(contractQO.getCustomerId()), ContractVOEntity::getCustomerId, contractQO.getCustomerId())
                .eq(StringUtils.isNotBlank(contractQO.getDomainCode()), ContractVOEntity::getDomainCode, contractQO.getDomainCode())
                .eq(StringUtils.isNotBlank(contractQO.getFutureCode()), ContractVOEntity::getFutureCode, contractQO.getFutureCode())
//                .eq(null != contractQO.getGoodsPackageId(), ContractVOEntity::getGoodsPackageId, contractQO.getGoodsPackageId())
//                .eq(null != contractQO.getGoodsSpecId(), ContractVOEntity::getGoodsSpecId, contractQO.getGoodsSpecId())
                .eq(null != contractQO.getContractType(), ContractVOEntity::getContractType, contractQO.getContractType())
                .eq(null != contractQO.getContractNature(), ContractVOEntity::getContractNature, contractQO.getContractNature())
                .eq(null != contractQO.getGoodsCategoryId(), ContractVOEntity::getCategory2, contractQO.getGoodsCategoryId())
                .eq(null != contractQO.getCategory3(), ContractVOEntity::getCategory3, contractQO.getCategory3())
                .eq(null != contractQO.getStatus(), ContractVOEntity::getStatus, contractQO.getStatus())
                .eq(null != contractQO.getPriceEndType(), ContractVOEntity::getPriceEndType, contractQO.getPriceEndType())
                .eq(null != contractQO.getPriceEndType() && ObjectUtil.isNotEmpty(contractQO.getPriceEndTime()) && ContractPriceEndTypeEnum.TEXT.getValue() == contractQO.getPriceEndType(), ContractVOEntity::getPriceEndTime, contractQO.getPriceEndTime())
                .eq(null != contractQO.getAbleReversePriceTimes(), ContractVOEntity::getAbleReversePriceTimes, contractQO.getAbleReversePriceTimes())
                .eq(null != contractQO.getAbleReversePriceTimes(), ContractVOEntity::getContractType, ContractTypeEnum.YI_KOU_JIA.getValue())
                .in(null != contractQO.getPriceComplete(), ContractVOEntity::getContractType, Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.ZAN_DING_JIA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()))
                .apply(null != contractQO.getPriceComplete() && PriceCompleteEnum.PRICE_COMPLETE.getType() == contractQO.getPriceComplete(), "contract_num = total_price_num")
                .apply(null != contractQO.getPriceComplete() && PriceCompleteEnum.NOT_PRICE_COMPLETE.getType() == contractQO.getPriceComplete(), "contract_num <> total_price_num")
                .eq(null != contractQO.getShipWarehouseId(), ContractVOEntity::getShipWarehouseId, contractQO.getShipWarehouseId())
                .eq(StringUtils.isNotBlank(contractQO.getCompanyId()), ContractVOEntity::getCompanyId, contractQO.getCompanyId())
                .eq(ContractVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .between(StrUtil.isNotBlank(contractQO.getSignDate()), ContractVOEntity::getSignDate, contractQO.getSignDate() + " 00:00:00.000", contractQO.getSignDate() + " 23:59:59.000")
                .between(ObjectUtil.isNotEmpty(contractQO.getSignStartDate()) && ObjectUtil.isNotEmpty(contractQO.getSignEndDate()), ContractVOEntity::getSignDate, contractQO.getSignStartDate(), contractQO.getSignEndDate())
                .between(bo, ContractVOEntity::getPriceEndTime, bo ? DateTimeUtil.parseTimeStamp0000(contractQO.getPriceEndTime().split(" ")[0]) : "", bo ? DateTimeUtil.parseTimeStamp2359(contractQO.getPriceEndTime().split(" ")[0]) : "")
                .eq(ObjectUtil.isNotEmpty(contractQO.getOperatorId()), ContractVOEntity::getUpdatedBy, contractQO.getOperatorId())
                .eq(ObjectUtil.isNotEmpty(contractQO.getGoodsId()), ContractVOEntity::getGoodsId, contractQO.getGoodsId())
                .like(ObjectUtil.isNotEmpty(contractQO.getEnterpriseName()),
                        contractQO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? ContractVOEntity::getEnterpriseName : ContractVOEntity::getSupplierEnterpriseName,
                        "%" + (ObjectUtil.isNotEmpty(contractQO.getEnterpriseName()) ? contractQO.getEnterpriseName().trim() : contractQO.getEnterpriseName()) + "%")
                // 货品名称模糊查询
                .and(StrUtil.isNotBlank(contractQO.getGoodsName()),
                        bizWrapper -> bizWrapper.like(ContractVOEntity::getGoodsName, "%" + (StrUtil.isNotBlank(contractQO.getGoodsName()) ? contractQO.getGoodsName().trim() : contractQO.getGoodsName()) + "%")
                                .or().like(ContractVOEntity::getCommodityName, "%" + (StrUtil.isNotBlank(contractQO.getGoodsName()) ? contractQO.getGoodsName().trim() : contractQO.getGoodsName()) + "%"))
                .orderByDesc(ContractVOEntity::getUpdatedAt);

        if (null != contractQO.getTriggerSys()) {
            if (contractQO.getTriggerSys().equals(SystemEnum.COLUMBUS.getName())) {
                if (ContractSalesTypeEnum.SALES.getValue() == contractQO.getSalesType()) {
                    queryWrapper.eq(ContractVOEntity::getCustomerId, contractQO.getColumbusCustomerId());
                } else {
                    queryWrapper.eq(ContractVOEntity::getSupplierId, contractQO.getColumbusCustomerId());
                }
                //如果是哥伦布端，且合同属性为提货权 即哥伦布的注销记录列表页，不展示被注销撤回的记录：过滤状态为已作废的
                if (ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(contractQO.getContractNature())) {
                    queryWrapper.ne(ContractVOEntity::getStatus, ContractStatusEnum.INVALID.getValue());
                }
            } else {
                queryWrapper.in(ContractVOEntity::getSiteCode, contractQO.getSiteCodeList());
//                queryWrapper.and(k -> {
//                    contractQO.getCompanyCustomerIdMap().entrySet().forEach(i -> {
//                        k.or(v -> v.eq(ContractVOEntity::getCompanyId, i.getKey())
//                                .in(ContractVOEntity::getBelongCustomerId, i.getValue()));
//                    });
//                });
            }
        }

        // 交货时间
        String deliveryStartTime = contractQO.getDeliveryStartTime();
        String deliveryEndTime = contractQO.getDeliveryEndTime();
        if (ObjectUtil.isNotEmpty(deliveryStartTime) && ObjectUtil.isNotEmpty(deliveryEndTime)) {
            queryWrapper
                    .ge(ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ContractVOEntity::getDeliveryEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1));

        } else {
            queryWrapper
                    .ge(ObjectUtil.isNotEmpty(contractQO.getDeliveryStartTime()), ContractVOEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ObjectUtil.isNotEmpty(contractQO.getDeliveryEndTime()), ContractVOEntity::getDeliveryEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1));
        }

        // 尾量关闭
        if (null != contractQO.getTailClosing()) {
            if (contractQO.getTailClosing() == 1) {
                queryWrapper.gt(ContractVOEntity::getCloseTailNum, BigDecimal.ZERO);
            }
            // 0 null
            if (contractQO.getTailClosing() == 0) {
                queryWrapper.and(i -> i.isNull(ContractVOEntity::getCloseTailNum).or().eq(ContractVOEntity::getCloseTailNum, BigDecimal.ZERO));
            }
        }
        // 是否可反点价 0.否 1.是
        if (null != contractQO.getIsReversePrice()) {
            queryWrapper
                    .and(i -> i.isNull(ContractVOEntity::getCloseTailNum).or().eq(ContractVOEntity::getCloseTailNum, BigDecimal.ZERO))
                    .gt(ContractVOEntity::getContractNum, BigDecimal.ZERO)
                    .eq(ContractVOEntity::getContractType, ContractTypeEnum.YI_KOU_JIA.getValue())
                    .eq(ContractVOEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue());

            if (contractQO.getIsReversePrice() == 1) {
                queryWrapper.gt(ContractVOEntity::getAbleReversePriceTimes, BigDecimal.ZERO);
            }
            // 0 null
            if (contractQO.getIsReversePrice() == 0) {
                queryWrapper.and(i -> i.isNull(ContractVOEntity::getAbleReversePriceTimes).or().eq(ContractVOEntity::getAbleReversePriceTimes, BigDecimal.ZERO));
            }
        }

        // 增加仓单的查询条件
        queryWrapper.eq(ObjectUtil.isNotEmpty(contractQO.getBuCode()), ContractVOEntity::getBuCode, contractQO.getBuCode());
        // 仓单不查询货权合同 【哥伦布要查货权合同】
        if (ObjectUtil.isNotEmpty(contractQO.getBuCode()) && BuCodeEnum.WT.getValue().equals(contractQO.getBuCode())
                && !SystemEnum.COLUMBUS.getName().equals(contractQO.getTriggerSys())) {
            queryWrapper.ne(ContractVOEntity::getContractNature, ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue());
        }
        // 注册号|仓单编码
        queryWrapper.eq(ObjectUtil.isNotEmpty(contractQO.getWarrantCode()), ContractVOEntity::getWarrantCode, contractQO.getWarrantCode());
        // 注销状态
        queryWrapper.eq(ObjectUtil.isNotEmpty(contractQO.getWriteOffStatus()), ContractVOEntity::getWriteOffStatus, contractQO.getWriteOffStatus());
        // 账套
        queryWrapper.eq(ObjectUtil.isNotEmpty(contractQO.getSiteCode()), ContractVOEntity::getSiteCode, contractQO.getSiteCode());
        // 注销量是否大于 0
        if (ObjectUtil.isNotEmpty(contractQO.getIsWriteOffNum())) {
            if (contractQO.getIsWriteOffNum() == 1) {
                queryWrapper.gt(ContractVOEntity::getCanCancelCount, 0);

                // add by zengshl 如果是采购的情况下，可注销量仓单的持有量或者合同的可注销量
                if (ContractSalesTypeEnum.PURCHASE.getValue() == contractQO.getSalesType()) {
                    queryWrapper.gt(ContractVOEntity::getHoldCount, 0);
                }

            }
            if (contractQO.getIsWriteOffNum() == 0) {
                queryWrapper.eq(ContractVOEntity::getCanCancelCount, 0);
            }
            queryWrapper.eq(ContractVOEntity::getContractType, ContractTypeEnum.YI_KOU_JIA.getValue());
        }
        // 仓单交易类型
        queryWrapper.eq(ObjectUtil.isNotEmpty(contractQO.getWarrantTradeType()), ContractVOEntity::getWarrantTradeType, contractQO.getWarrantTradeType());
        // 注销时间
        String writeOffStartTime = contractQO.getWriteOffStartTime();
        String writeOffEndTime = contractQO.getWriteOffEndTime();
        if (StringUtils.isNotBlank(writeOffStartTime)) {
            queryWrapper.ge(ContractVOEntity::getWriteOffStartTime, writeOffStartTime + "-01");
        }
        if (StringUtils.isNotBlank(writeOffEndTime)) {
            queryWrapper.lt(ContractVOEntity::getWriteOffEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(writeOffEndTime + "-01"), 1));

        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

}
