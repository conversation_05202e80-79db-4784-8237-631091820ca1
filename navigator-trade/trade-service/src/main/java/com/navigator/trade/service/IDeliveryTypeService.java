package com.navigator.trade.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IDeliveryTypeService {

    List<DeliveryTypeEntity> getAllDeliveryTypeList(Integer status, Integer categoryId, String siteCode, String buCode, Integer type);

    List<DeliveryTypeVO> getDeliveryTypeByCategoryId(Integer categoryId, String siteCode, String buCode);

    List<DeliveryTypeEntity> getAllDeliveryByAddressType(Integer status, Integer addressType);

    DeliveryTypeEntity getDeliveryTypeById(Integer id);

    DeliveryTypeEntity getDeliveryTypeByLkgCode(String lkgCode);

    boolean saveOrUpdateDeliveryType(DeliveryTypeEntity deliveryTypeEntity);

    boolean invalidStatus(Integer deliveryTypeId);

    Result importDeliveryType(MultipartFile uploadFile);

    List<Integer> getSendDeliveryTypeIdList();
}
