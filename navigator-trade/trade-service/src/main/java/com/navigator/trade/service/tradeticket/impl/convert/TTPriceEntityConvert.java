package com.navigator.trade.service.tradeticket.impl.convert;

import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TTPriceEntityConvert {

    @Autowired
    protected CustomerFacade customerFacade;


    public TTPriceEntity price2TTPriceEntity(TTDTO ttDto, TradeTicketEntity tradeTicketEntity) {
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttDto.getSalesContractTTPriceDTO();
        // 保存 ttPrice
        TTPriceEntity ttPriceEntity = BeanConvertUtils.convert(TTPriceEntity.class, salesContractTTPriceDTO);

        if (ttPriceEntity == null) {
            return null;
        }

        //查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractTTPriceDTO.getSupplierId()) {
            supplier = customerFacade.getCustomerById(salesContractTTPriceDTO.getSupplierId());
        }
        if (supplier != null) {
            ttPriceEntity.setSupplierId(supplier.getId());
            ttPriceEntity.setSupplierName(supplier.getName());

        }

        ttPriceEntity.setGoodsId(tradeTicketEntity.getGoodsId());

        ttPriceEntity.setTtId(tradeTicketEntity.getId());
        ttPriceEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttPriceEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        return ttPriceEntity;
    }
}
