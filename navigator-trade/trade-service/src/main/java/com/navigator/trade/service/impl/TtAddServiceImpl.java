package com.navigator.trade.service.impl;

import com.navigator.trade.dao.TtAddDao;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.service.ITtAddService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ttAdd表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class TtAddServiceImpl implements ITtAddService {

    @Autowired
    private TtAddDao ttAddDao;

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return ttAddDao.updateContractId(ttId, contractId);
    }

    @Override
    public TTAddEntity getTTAddEntityByTTId(Integer ttId) {
        return ttAddDao.getTTAddEntityByTTId(ttId);
    }
}
