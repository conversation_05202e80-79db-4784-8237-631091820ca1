package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 合同修改的业务子类处理 Logic 逻辑处理
 *
 * <AUTHOR>
 * @Date 2024-07-15
 */
public interface ReviseLogicService {

    /**
     * 校验合同信息-修改交易
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void modifyContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 定价完成处理
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    void modifyPriceComplete(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 更新父子合同数据
     *
     * @param contractEntity
     * @param contractModifyDTO
     */
    void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);


    /**
     * 修改记录操作日志
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);
}
