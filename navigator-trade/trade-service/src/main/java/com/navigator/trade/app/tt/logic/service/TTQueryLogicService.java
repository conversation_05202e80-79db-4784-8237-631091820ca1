package com.navigator.trade.app.tt.logic.service;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.app.tt.domain.qo.TTAddQO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.vo.TTDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
public interface TTQueryLogicService {

    /**
     *查询TT列表
     * @return
     */
    Result queryTTList(QueryDTO<TTQueryDTO> ttQueryDTO);

    /**
     * 根据ttId查询TT详情
     * @param ttId
     * @param ttTypeEnum
     * @return
     */
    TTDetailVO queryTTDetail(Integer ttId, TTTypeEnum ttTypeEnum);

    /**
     * 查询TradeTicketEntity详情，查到多个时，会排序，返回第一条数据
     *  字段优先级为：1、ttId 2、contractId 3、signId
     * @param qo
     * @return
     */
    TradeTicketEntity fetchTradeTicketEntity(TradeTicketQO qo);

    /**
     * 查询TTAddEntity详情
     *
     * @param qo
     * @return
     */
    TTAddEntity fetchTTAddEntity(TTAddQO qo);

    /**
     *  查询TTAddEntity列表
     *
     * @param qo
     * @return
     */
    List<TTPriceEntity> fetchTTPriceEntities(TTPriceQO qo);

    /**
     *
     * @param contractId
     * @return
     */
    ContractPriceEntity getContractPriceEntityContractId(Integer contractId);

    /**
     * 根据ttid查询TT
     *
     * @param ttId 协议id
     * @return
     */
    TradeTicketEntity getByTtId(Integer ttId);

    /**
     * 根据groupId获取关联的另一条数据
     *
     * @param
     * @return
     */
    TradeTicketEntity getIncludeDeletedByGroupId(String groupId, Integer id);

}
