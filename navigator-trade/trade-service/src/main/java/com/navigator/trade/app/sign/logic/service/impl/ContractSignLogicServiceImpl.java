package com.navigator.trade.app.sign.logic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.rule.CommonConfigFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.dto.rule.CommonConfigRuleMatchDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.LogTargetRecordTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.*;
import com.navigator.common.config.properties.AzureBlobProperties;
import com.navigator.common.dto.*;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.FilePagesUtils;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.customer.pojo.enums.*;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.facade.TemplateGroupFacade;
import com.navigator.husky.facade.TemplateLoadFacade;
import com.navigator.husky.facade.TemplateSignFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import com.navigator.husky.pojo.enums.TemplateLoadTypeEnum;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.facade.YqqSignParameterFacade;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.sign.domain.service.ContractSignDomainService;
import com.navigator.trade.app.sign.domain.service.ContractSignQueryDomainService;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.dao.ContractSignTemplateLogDao;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.*;
import com.navigator.trade.pojo.dto.notice.CustomerSignNoticeItemDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.ITtTranferService;
import com.navigator.trade.service.contractsign.ContractSignBuildProcessor;
import com.navigator.trade.service.contractsign.ContractSignBuildProcessorV2;
import com.navigator.trade.service.contractsign.converter.SignHuskyTemplateConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同业务逻辑处理Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class ContractSignLogicServiceImpl implements ContractSignLogicService {
    @Resource
    ContractSignBuildProcessorV2 contractSignBuildProcessorV2;
    @Resource
    MessageFacade messageFacade;
    @Autowired
    AzureBlobUtil azureBlobUtil;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Resource
    private CustomerProtocolFacade customerProtocolFacade;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Resource
    private FileBusinessFacade fileBusinessFacade;
    @Resource
    private TemplateFacade templateFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private DbtSignatureFacade signatureFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private ContractSignTemplateLogDao contractSignTemplateLogDao;
    @Resource
    private PriceApplyFacade priceApplyFacade;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Value("${contract.sign.qrCodeUrl}")
    private String qrCodeUrl;
    @Resource
    private CRoleFacade cRoleFacade;
    @Resource
    private AzureBlobProperties azureBlobProperties;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private TemplateSignFacade templateSignFacade;
    @Resource
    private TemplateLoadFacade templateLoadFacade;
    @Resource
    private QualityFacade qualityFacade;
    @Resource
    private SkuFacade skuFacade;
    /**
     * TODO TT域提供查询
     */
    @Resource
    private ITtTranferService ttTranferService;
    @Resource
    private TemplateGroupFacade templateGroupFacade;
    /**
     * 协议域
     */
    @Autowired
    private ContractSignDomainService contractSignDomainService;
    @Autowired
    private ContractSignQueryDomainService contractSignQueryDomainService;
    /**
     * 合同TT域LOGIC
     */
    @Autowired
    private ContractSignQueryLogicService contractSignQueryLogicService;
    @Resource
    private ContractQueryLogicService contractQueryLogicService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private TTQueryLogicService ttQueryLogicService;
    @Resource
    @Lazy
    private TTLogicService ttLogicService;
    @Resource
    private CommonConfigFacade commonConfigFacade;
    @Resource
    private YqqSignParameterFacade yqqSignParameterFacade;

    @Override
    public ContractSignEntity createOrUpdateContractSign(ContractSignCreateDTO signCreateDTO) {
        // 填充协议参数
        ContractSignEntity contractSignEntity = this.prepareSignInfo(signCreateDTO);
        // 创建协议
        contractSignDomainService.saveContractSign(contractSignEntity);
        FileInfoEntity barCodeInfo = fileProcessFacade.generateBarCodeImg(signCreateDTO.getContractCode(), "");
        FileInfoEntity qrCodeInfo = fileProcessFacade.generateQrCodeImg(qrCodeUrl + contractSignEntity.getId());
        contractSignEntity.setBarCodeImage(null == barCodeInfo ? "" : barCodeInfo.getFilePathUrl()).setQrCodeImage(null == qrCodeInfo ? "" : qrCodeInfo.getFilePathUrl());
        contractSignDomainService.updateSignEntity(contractSignEntity);
        // 创建协议正本
        if (null != contractSignEntity.getId() && null != contractSignEntity.getContractId()) {
            saveContractSignDetail(contractSignEntity);
        }
        // add by zengshl 如果协议是直接完成的情况下需要更新TT为完成的状态
        if (ContractSignStatusEnum.PROCESSING.getValue() == contractSignEntity.getStatus()) {
            ttLogicService.updateTTStatusById(contractSignEntity.getTtId(), TTStatusEnum.DONE);
        }
        try {
            // 记录操作日志
            addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.NEW_SALES_CONTRACT_SIGN, JSONUtil.toJsonStr(contractSignEntity), SystemEnum.MAGELLAN.getValue());
        } catch (Exception e) {
            log.debug("createOrUpdateContractSign:{}", e.getMessage());
        }
        return contractSignEntity;
    }

    @Override
    public void updateContractSign(ContractEntity contractEntity, CustomerOriginalPaperEntity customerOriginalPaperEntity, ContractSignEntity contractSignEntity) {
        // 判断是否需要正本
        Integer needOriginalPaper = isNeedOriginalPaper(customerOriginalPaperEntity, contractSignEntity, null);
        contractSignEntity.setNeedOriginalPaper(needOriginalPaper)
                .setContractId(contractEntity.getId());
        saveContractSignDetail(contractSignEntity);
        contractSignDomainService.updateSignEntity(contractSignEntity);
    }


    /**
     * 协议新增记录关联数据
     *
     * @param contractSignEntity
     */
    @Async
    public void saveContractSignDetail(ContractSignEntity contractSignEntity) {
        ContractSignDetailEntity contractSignDetailEntity = new ContractSignDetailEntity();
        contractSignDetailEntity
                .setContractSignId(contractSignEntity.getId())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setTtType(contractSignEntity.getTtType())
                .setTradeType(contractSignEntity.getTradeType())
        ;
        ContractSignTemplateDTO contractSignTemplateDTO = contractSignBuildProcessorV2.getContractSignTemplateDTO(contractSignEntity.getId());
        if (null != contractSignTemplateDTO) {
            contractSignDetailEntity.setNewContractContent(JSON.toJSONString(contractSignTemplateDTO.getContractDetailInfoDTO()));
            contractSignDetailEntity.setSourceContractContent(JSON.toJSONString(contractSignTemplateDTO.getSourceContractDetailInfoDTO()));
            contractSignDetailEntity.setModifyContent(JSON.toJSONString(contractSignTemplateDTO.getTradeTicketDTO()));
        }
        contractSignDomainService.saveContractSignDetail(contractSignDetailEntity);
    }


    /**
     * 协议参数填充
     *
     * @param signCreateDTO
     * @return
     */
    private ContractSignEntity prepareSignInfo(ContractSignCreateDTO signCreateDTO) {
        ContractSignEntity contractSignEntity = BeanConvertUtils.convert(ContractSignEntity.class, signCreateDTO);

        Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();
        // 查询客户信息
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        // 判断是否需要正本
        Integer needOriginalPaper = 0;
        //查询客户是否是需要正本
        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
        customerOriginalPaperDTO.setCustomerId(customerId);
        customerOriginalPaperDTO.setSaleType(signCreateDTO.getSalesType());
        customerOriginalPaperDTO.setStatus(DisableStatusEnum.ENABLE.getValue());
        customerOriginalPaperDTO.setCompanyId(signCreateDTO.getCompanyId());
        customerOriginalPaperDTO.setCategory2(String.valueOf(signCreateDTO.getCategory2()));
        customerOriginalPaperDTO.setCategory3(String.valueOf(signCreateDTO.getCategory3()));

        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
        log.info("===============================================================customerOriginalPaperEntity:{}", JSON.toJSONString(customerOriginalPaperEntity));
        needOriginalPaper = isNeedOriginalPaper(customerOriginalPaperEntity, contractSignEntity, signCreateDTO);
        log.info("===================================================================needOriginalPaper:{}", needOriginalPaper);
        // 签章类型:是否使用易企签
        Integer signatureType = null != customerDTO.getUseYqq() && UseYqqEnum.USE_YQQ.getValue().equals(customerDTO.getUseYqq()) ? SignatureWayEnum.YI_QI_QIAN.getType() : SignatureWayEnum.FILE_UPLOAD.getType();
        // 根据客户和品类查询客户配置
        customerOriginalPaperDTO.setCustomerId(customerId)
                .setCategoryId(signCreateDTO.getGoodsCategoryId())
                .setCategory2(String.valueOf(signCreateDTO.getCategory2()))
                .setCategory3(String.valueOf(signCreateDTO.getCategory3()))
                .setCompanyId(signCreateDTO.getCompanyId())
                .setSaleType(signCreateDTO.getSalesType())
                .setStatus(DisableStatusEnum.ENABLE.getValue());

        log.info("===============================================================新CompanyId:{}", JSON.toJSONString(customerOriginalPaperDTO.getCompanyId()));

        Integer isLdcFrame;
        ContractSignEntity signEntity = contractSignQueryDomainService.queryContractSignByContractCode(signCreateDTO.getContractCode());
        if (null != signEntity) {
            isLdcFrame = signEntity.getLdcFrame();
            contractSignEntity.setCustomerNonFrame(signEntity.getCustomerNonFrame());
        } else {
            log.info("===================================================================customerOriginalPaperDTO:{}", customerOriginalPaperDTO);

            customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);

            log.info("===================================================================customerOriginalPaperEntity:{}", customerOriginalPaperEntity);
            if (null == customerOriginalPaperEntity) {
                isLdcFrame = LdcFrameEnum.LDC.getValue();
                contractSignEntity.setCustomerNonFrame(LdcFrameEnum.LDC.getValue());
            } else {
                isLdcFrame = DisableStatusEnum.ENABLE.getValue().equals(customerOriginalPaperEntity.getStatus())
                        ? customerOriginalPaperEntity.getLdcFrame() : LdcFrameEnum.LDC.getValue();
                contractSignEntity.setCustomerNonFrame(customerOriginalPaperEntity.getLdcFrame());
            }
            if (ContractTradeTypeEnum.getNoHuskyTemplateTradeList().contains(contractSignEntity.getTradeType())) {
                isLdcFrame = LdcFrameEnum.NOT_FRAME.getValue();
            }
            // todo:数字合同开关
            //数字合同开关未配置，或者数字合同开关关闭，TT的质量指标配置判断，不生效，直接返回true，不做校验
//            SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.HUSKY_CONTRACT_PROVIDE.getRuleCode()));
//            if (null != systemRuleVO) {
//                if (!CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList())) {
//                    if (DisableStatusEnum.DISABLE.getValue().equals(systemRuleVO.getSystemRuleItemVOList().get(0).getStatus())) {
//                        if ("FL".equals(signCreateDTO.getDeliveryFactoryCode()) || signCreateDTO.getCompanyId() != 1) {
//                            isLdcFrame = LdcFrameEnum.NOT_FRAME.getValue();
//                        }
//                    }
//                }
//
//            }

            log.info("===================================================================isLdcFrame:{}", isLdcFrame);
        }
        // TODO 仓单生成的协议先不走数字合同
//        if (BuCodeEnum.WT.getValue().equals(signCreateDTO.getBuCode())) {
//            isLdcFrame = 0;
//        }
        CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
        customerProtocolDTO
                .setCustomerId(customerId)
                .setCompanyId(signCreateDTO.getCompanyId())
                .setCategoryId(signCreateDTO.getGoodsCategoryId())
                .setCategory2(String.valueOf(signCreateDTO.getCategory2()))
                .setSaleType(signCreateDTO.getSalesType());
        //框架协议属性，订单大合同信息
        CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
        Integer frameProtocolType = null != customerProtocolEntity ? customerProtocolEntity.getFrameProtocol() : FrameProtocolEnum.ORDER.getValue();
        Integer tradeType = null == signCreateDTO.getTradeType() ? 0 : signCreateDTO.getTradeType();
        if (!ContractTradeTypeEnum.getNewTemplateProtocol().contains(tradeType)) {
            List<ContractSignEntity> signEntityList = contractSignQueryDomainService.queryByContractId(contractSignEntity.getContractId());
            if (!CollectionUtils.isEmpty(signEntityList)) {
                signEntityList = signEntityList.stream()
                        .filter(it -> {
                            return !Arrays.asList(ContractSignStatusEnum.ABNORMAL.getValue(), ContractSignStatusEnum.INVALID.getValue()).contains(it.getStatus());
                        })
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(signEntityList)) {
                    signEntityList.sort(Comparator.comparing(ContractSignEntity::getId));
                    frameProtocolType = signEntityList.get(0).getFrameProtocolType();
                }
            }
        }
        if (ContractSalesTypeEnum.PURCHASE.getValue() == signCreateDTO.getSalesType()) {
            frameProtocolType = ProtocolTypeEnum.CONTRACT.getCode();
        }
        //查询yqq是否启用
        String provideType = ProvideTypeEnum.HUSKY_CONTRACT.getCode();
        //查询签章通用状态是否开启
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        SystemRuleVO.SystemRuleItemVO systemRuleItemVO = systemRuleVO.getSystemRuleItemVOList().get(0);
        if (isLdcFrame.equals(LdcFrameEnum.NOT_FRAME.getValue())) {
            provideType = ProvideTypeEnum.FILE_UPLOAD.getCode();
        }

        //默认静默签
        String ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.SILENCE.getCode());

        if (systemRuleItemVO.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
            ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.OFFLINE_SIGN.getCode());
        } else if (isLdcFrame.equals(LdcFrameEnum.NOT_FRAME.getValue())) {
            ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.NORMAL.getCode());
        }

        Integer contractSignType = tradeType;

        if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.NEW.getValue();
        } else if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        } else if (ContractTradeTypeEnum.FIXED.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.PRICE.getValue();
        } else if (ContractTradeTypeEnum.BUYBACK.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.NEW.getValue();
        }

        String customerSignatureType = UseYqqEnum.USE_YQQ.getValue().equals(customerDTO.getUseYqq()) && GeneralEnum.YES.getValue().equals(customerDTO.getIsColumbus())
                ? CustomerSignatureTypeEnum.YQQ_SIGN.getCode() : CustomerSignatureTypeEnum.OFFLINE_SIGN.getCode();

        Integer SignType = null == signCreateDTO.getSignType() ? ContractSignTypeEnum.REMAIN_NUM.getValue() : signCreateDTO.getSignType();

        // 保存协议信息
        contractSignEntity
                .setContractSignType(String.valueOf(contractSignType))
                .setTermineType(String.valueOf(SignType))
                .setWsType(getWsTypeEnum(customerDTO) != null ? String.valueOf(getWsTypeEnum(customerDTO)) : null)
                .setLdcSignatureType(ldcSignatureType)
                .setCustomerSignatureType(customerSignatureType)
                .setNeedReview(String.valueOf(NeedReviewEnum.REVIEW.getValue()))
                .setProvideType(provideType)
                .setCategory1(signCreateDTO.getCategory1())
                .setCategory2(signCreateDTO.getCategory2())
                .setCategory3(signCreateDTO.getCategory3())
                .setGoodsCategoryId(signCreateDTO.getCategory2())
                .setUuid(IdUtil.simpleUUID())
                .setSalesType(signCreateDTO.getSalesType())
                .setCompanyId(signCreateDTO.getCompanyId())
                .setCompanyName(signCreateDTO.getCompanyName())
                .setProtocolCode(signCreateDTO.getTtCode().contains("-") ? signCreateDTO.getTtCode().substring(signCreateDTO.getTtCode().indexOf("-") + 1) : "000")
                .setNeedOriginalPaper(needOriginalPaper)
                .setBelongCustomerId(signCreateDTO.getBelongCustomerId())
                .setLdcFrame(isLdcFrame)
                .setFrameProtocolType(frameProtocolType)
                .setSignType(SignType)
                .setTradeType(tradeType)
                .setContractSource(null == signCreateDTO.getContractSource() ? 0 : signCreateDTO.getContractSource())
                .setSignatureWay(signatureType)
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
        return contractSignEntity;
    }

    //多品类V1 协议多品类字段添加是否是使用系统校验 Author:Wan 2024-07-01 start
    public Integer getWsTypeEnum(CustomerDTO customerDTO) {

        Map<String, Integer> wsMap = ImmutableMap.<String, Integer>builder()
                //不使用易企签，使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.NOT_USE_YQQ.getValue())
                //使用易企签，使用哥伦布
                .put(UseYqqEnum.USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.USE_YQQ.getValue())
                //不使用易企签，不使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.DISABLE.getValue().toString(), UseYqqEnum.NOT_SYSTEM.getValue())
                .build();
        return wsMap.get(customerDTO.getUseYqq().toString() + customerDTO.getIsColumbus().toString());
    }
    //多品类V1 协议多品类字段添加是否是使用系统校验 Author:Wan 2024-07-01 end

    @Override
    public FileBaseInfoDTO generateSignPdf(ContractSignProvideDTO signProvideDTO) {
        ContractSignEntity contractSignEntity = this.checkContractSign(signProvideDTO.getContractSignId());
        String htmlContent = signProvideDTO.getHtmlContent();
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genFileRelativePath(String.valueOf(contractSignEntity.getId()),
                contractSignEntity.getContractCode(),
                FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(),
                FilePathType.HTML)).setHtmlContent(htmlContent);
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        return fileBaseInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean provideContractSign(ContractSignProvideDTO signProvideDTO) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signProvideDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_PROVIDE.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_PROVIDE);
        }
        if (LdcFrameEnum.NOT_FRAME.getValue().equals(contractSignEntity.getLdcFrame())) {
            // 判断前端有文件回传
            if (CollectionUtils.isEmpty(signProvideDTO.getFileIdList())) {
                throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
            }
            // 保存文件关系表
            fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                    .setBizId(signProvideDTO.getContractSignId())
                    .setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode())
                    .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                    .setFileIdList(signProvideDTO.getFileIdList()));
        } else {
//            FileBaseInfoDTO fileBaseInfoDTO = generateSignPdfV2(contractSignEntity);
            String htmlContent = signProvideDTO.getHtmlContent();
//            if (StringUtils.isNotBlank(htmlContent)) {
//                htmlContent = Byte2HexUtil.hexStr2Str(htmlContent);
//                htmlContent =  Byte2HexUtil.unicodeToString(htmlContent);
//            }
            // 记录用户提交的模版信息
            contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO()
                    .setContractSignId(contractSignEntity.getId())
                    .setContractId(contractSignEntity.getContractId())
                    .setTtId(contractSignEntity.getTtId())
                    .setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue())
                    .setTemplateContent(htmlContent));
            HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                    .setHtmlUrl(FilePathUtil.genFileRelativePath(String.valueOf(contractSignEntity.getId()),
                            contractSignEntity.getContractCode(),
                            FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(),
                            FilePathType.HTML)).setHtmlContent(htmlContent)
                    .setContractCode(contractSignEntity.getContractCode())
                    .setTtCode(contractSignEntity.getTtCode());
            //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
            pdfInfoDTO.setCompanyId(contractSignEntity.getCompanyId());

            FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
            // 插入文件信息表
            FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
            // 插入文件关系表
            FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO()
                    .setFileIdList(Collections.singletonList(fileInfoEntity.getId()))
                    .setBizId(contractSignEntity.getId())
                    .setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode())
                    .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule());
            fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);

        }
        contractSignEntity.setIsCustomerSignature(signProvideDTO.getIsCustomerSignature());
        // 更新合同状态为待审核状态
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_REVIEW.getValue(), "");

        //记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.PROVIDE_SALES_CONTRACT_SIGN, "", SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.CREATE_E_CONTRACT, contractSignEntity, SystemEnum.MAGELLAN.getName());

        return true;
    }

    @Override
    public FileBaseInfoDTO previewHuskyContractPdf(TemplateEntity templateEntity) {
        ContractSignEntity contractSignEntity = this.checkContractSign(templateEntity.getContractSignId());
        //数字合同husky数据记录处理
        String htmlContent = templateSignFacade.provideContractSign(templateEntity);
        TemplateLoadEntity templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, templateEntity);
        //todo:nana
        templateLoadEntity.setType(TemplateLoadTypeEnum.USER_PREVIEW.getValue())
                .setContractSignId(templateEntity.getContractSignId())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTemplateId(templateEntity.getId())
                .setTemplateCode(templateEntity.getCode())
                .setTemplateName(templateEntity.getName())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.USER_PREVIEW) + "预览提交的Html文本：" + htmlContent)
                .setMemo(TemplateLoadTypeEnum.USER_PREVIEW.getDesc())
                .setBizData(FastJsonUtils.getBeanToJson(templateEntity));
        templateLoadEntity = templateLoadFacade.saveTemplateLoadLog(templateLoadEntity);
        // 记录用户提交的模版
        contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO().setContractSignId(contractSignEntity.getId()).setContractId(contractSignEntity.getContractId()).setTtId(contractSignEntity.getTtId()).setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue()).setTemplateContent(htmlContent));
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genHuskyFileRelativePath(String.valueOf(contractSignEntity.getId()), contractSignEntity.getContractCode(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(), FilePathType.HTML)).setHtmlContent(htmlContent)
                .setContractCode(contractSignEntity.getContractCode())
                .setTtCode(contractSignEntity.getTtCode())
                .setCompanyId(contractSignEntity.getCompanyId());
        //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        templateLoadEntity.setContent(templateLoadEntity.getContent() + "\n文件信息：=====" + FastJsonUtils.getBeanToJson(fileBaseInfoDTO));
        templateLoadFacade.updateTemplateLoadLog(templateLoadEntity);
        return fileBaseInfoDTO;
    }

    /**
     * 数字合同出具合同信息
     *
     * @param templateEntity
     * @return
     */
    @Override
    public TemplateEntity provideHuskyContractSign(TemplateEntity templateEntity) {
        if (null == templateEntity.getContractSignId()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(templateEntity.getContractSignId());
        if (ContractSignStatusEnum.WAIT_PROVIDE.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_PROVIDE);
        }
        //数字合同husky数据记录处理
        String htmlContent = templateSignFacade.provideContractSign(templateEntity);
        TemplateLoadEntity templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, templateEntity);

        templateLoadEntity.setType(TemplateLoadTypeEnum.USER_SUBMIT.getValue())
                .setContractSignId(templateEntity.getContractSignId())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTemplateId(templateEntity.getId())
                .setTemplateCode(templateEntity.getCode())
                .setTemplateName(templateEntity.getName())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.USER_SUBMIT) + "出具提交的Html文本：" + htmlContent)
                .setMemo(TemplateLoadTypeEnum.USER_SUBMIT.getDesc())
                .setBizData(FastJsonUtils.getBeanToJson(templateEntity));
        templateLoadFacade.saveTemplateLoadLog(templateLoadEntity);
        // 记录用户提交的模版
        contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO().setContractSignId(contractSignEntity.getId()).setContractId(contractSignEntity.getContractId()).setTtId(contractSignEntity.getTtId()).setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue()).setTemplateContent(htmlContent));
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genHuskyFileRelativePath(String.valueOf(contractSignEntity.getId()), contractSignEntity.getContractCode(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(), FilePathType.HTML)).setHtmlContent(htmlContent)
                .setContractCode(contractSignEntity.getContractCode())
                .setTtCode(contractSignEntity.getTtCode())
                .setCompanyId(contractSignEntity.getCompanyId());
        //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        // 插入文件信息表
        FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
        // 插入文件关系表
        FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO().setFileIdList(Collections.singletonList(fileInfoEntity.getId())).setBizId(contractSignEntity.getId()).setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode()).setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule());
        fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);
//        contractSignEntity.setIsCustomerSignature(templateEntity.getIsCustomerSignature());
        // 更新合同状态为待审核状态
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_REVIEW.getValue(), "");

        // 记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.PROVIDE_SALES_CONTRACT_SIGN, "", SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.CREATE_E_CONTRACT, contractSignEntity, SystemEnum.MAGELLAN.getName());
        this.sendProvideHuskyChangeSignEmail(templateEntity, contractSignEntity);
        return templateEntity;
    }

    /**
     * 数字合同-邮件通知：协议出具内容修改
     */
    public void sendProvideHuskyChangeSignEmail(TemplateEntity templateEntity, ContractSignEntity signEntity) {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.MAGELLAN_HUSKY_TEMPLATE_CHANGE.name())
                .setBusinessSceneCode(BusinessSceneEnum.MAGELLAN_HUSKY_TEMPLATE_CHANGE.getDesc())
                .setSystem(SystemEnum.MAGELLAN.getValue());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String operator = employFacade.getEmployCache(userId);
        Map<String, Object> map = new HashMap<>();
        List<String> modifyGroupCodeList = templateEntity.getGroupList().stream()
                .filter(groupEntity -> {
                    return null != groupEntity.getModify() && groupEntity.getModify() && StringUtils.isNotBlank(groupEntity.getCode());
                })
                .map(TemplateGroupEntity::getCode)
                .collect(Collectors.toList());
        String modifyGroupCodes = "无";
        if (!CollectionUtils.isEmpty(modifyGroupCodeList)) {
            Result groupResult = templateGroupFacade.getTemplateGroupByCodeList(modifyGroupCodeList, null);
            List<TemplateGroupEntity> modifyGroupEntityList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupEntity.class);
            Map<String, List<TemplateGroupEntity>> groupMap = modifyGroupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
            modifyGroupCodeList = modifyGroupCodeList.stream().map(modifyGroupCode -> {
                String modifyGroupName = CollectionUtils.isEmpty(groupMap.get(modifyGroupCode)) ? "" : groupMap.get(modifyGroupCode).get(0).getName();
                return modifyGroupCode + "(" + modifyGroupName + ")";
            }).collect(Collectors.toList());
            modifyGroupCodes = StringUtils.join(modifyGroupCodeList, "、");
        }
        List<TemplateGroupEntity> addGroupList = templateEntity.getGroupList().stream()
                .filter(groupEntity -> {
                    return StringUtils.isBlank(groupEntity.getCode());
                })
                .collect(Collectors.toList());
        String deleteGroupCodes = "无";
        if (!CollectionUtils.isEmpty(templateEntity.getDeleteGroupCodeList())) {
            Result groupResult = templateGroupFacade.getTemplateGroupByCodeList(templateEntity.getDeleteGroupCodeList(), null);
            List<TemplateGroupEntity> deleteGroupEntityList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupEntity.class);
            Map<String, List<TemplateGroupEntity>> groupMap = deleteGroupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
            deleteGroupCodes = templateEntity.getDeleteGroupCodeList().stream().map(deleteGroupCode -> {
                String modifyGroupName = CollectionUtils.isEmpty(groupMap.get(deleteGroupCode)) ? "" : groupMap.get(deleteGroupCode).get(0).getName();
                return deleteGroupCode + "(" + modifyGroupName + ")";
            }).collect(Collectors.joining("、"));
        }
        //如果未修改、新增、删除条款组，则不发短信
        if (CollectionUtils.isEmpty(modifyGroupCodeList) && CollectionUtils.isEmpty(addGroupList) && CollectionUtils.isEmpty(templateEntity.getDeleteGroupCodeList())) {
            return;
        }

        map.put("contractCode", signEntity.getContractCode());
        map.put("ttCode", signEntity.getTtCode());
        map.put("tradeType", ContractTradeTypeEnum.getDescByValue(signEntity.getTradeType()));
        map.put("templateCode", templateEntity.getCode());
        map.put("templateName", templateEntity.getName());
        map.put("modifyGroupCodes", StringUtils.isNotBlank(modifyGroupCodes) ? modifyGroupCodes : "无");
        map.put("addGroupNum", addGroupList.size());
        map.put("deleteGroupCodes", deleteGroupCodes);
        map.put("operator", operator);
        map.put("sendDate", DateTimeUtil.formatDateStringCN(new Date()));
        //        List<String> receivers = new ArrayList<>();
//        receivers.add(String.valueOf(userId));
//        messageInfoDTO.setReceivers(receivers);
        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
    }


    /**
     * 模板匹配操作信息拼接
     *
     * @param templateLoadTypeEnum
     * @return
     */
    private String getTemplateLoadAct(TemplateLoadTypeEnum templateLoadTypeEnum) {
        return "(" + templateLoadTypeEnum.getValue() + ")" + templateLoadTypeEnum.getDesc() + "\n";
    }

    /**
     * 重新改造方法：buildSignTemplateDTO
     *
     * @param contractSignId 协议ID
     * @return
     */
    @Override
    public SignHuskyTemplateDTO buildHuskySignTemplateV2(Integer contractSignId) {
        //新的组装模板数据的方法
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();
        TemplateLoadEntity templateLoadEntity = new TemplateLoadEntity();
        contractSignTemplateDTO = contractSignBuildProcessorV2.getContractSignTemplateDTO(contractSignId);
        SignHuskyTemplateDTO signHuskyTemplateDTO1 = new SignHuskyTemplateDTO();
        SignHuskyTemplateDTO signHuskyTemplateDTO = new SignHuskyTemplateDTO();

        if (null != contractSignTemplateDTO) {
            //记录合同信息
            TemplateLoadEntity templateLoadEntity1 = new TemplateLoadEntity()
                    .setType(0)
                    .setContractSignId(contractSignId)
                    .setContractId(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractId() : 0)
                    .setContractCode(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractCode() : "")
                    .setTtId(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtId() : 0)
                    .setTtCode(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtCode() : "")
                    .setContent("(0)合同基础信息获取："+FastJsonUtils.getBeanToJson(contractSignTemplateDTO))
                    .setMemo("合同TT信息获取");
            templateLoadFacade.saveTemplateLoadLog(templateLoadEntity1);
            //业务信息获取
            signHuskyTemplateDTO = contractSignBuildProcessorV2.buildSignTemplateDTO(contractSignTemplateDTO);
//            signHuskyTemplateDTO1 = getSignHuskyTemplateDTO(contractSignTemplateDTO);
//            //出具合同，取值优化数据比对
//            List<CompareObjectDTO> diffObjectDTOList = BeanCompareUtils.compareFields(signHuskyTemplateDTO1, signHuskyTemplateDTO, Arrays.asList("barCodeImage", "qrCodeImage", "transferMonthTime"), new ArrayList<>());

            //模板日志基础信息装载
            templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, signHuskyTemplateDTO.getKeyVariableDTO());
            templateLoadEntity.setContractSignId(contractSignId)
                    .setContractId(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractId() : 0)
                    .setContractCode(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractCode() : "")
                    .setTtId(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtId() : 0)
                    .setTtCode(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtCode() : "");
            signHuskyTemplateDTO.setTemplateLoadEntity(templateLoadEntity);
        }
        return signHuskyTemplateDTO;
    }

    protected SignTemplateDTO fillSignTemplateDTO(SignTemplateDTO signTemplateDTO, ContractSignTemplateDTO contractSignTemplateDTO) {
        //TODO 如果各个场景有特有的设置，可以在之类中重写该方法
        return signTemplateDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewContractSign(ContractSignReviewDTO signReviewDTO) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_REVIEW.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_CHECKED);
        }
        // 审核通过：待签章

        if (ContractReviewStatusEnum.PASS.getValue() == signReviewDTO.getReviewStatus()) {
            contractSignDomainService.updateSignEntity(contractSignEntity
                    .setStatus(ContractSignStatusEnum.WAIT_STAMP.getValue())
                    //审核通过修改协议签署状态
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.NOT_START.getValue())
                    // 清空驳回原因
                    .setRejectReason(""));
        } else {
            this.reviewReject(contractSignEntity, signReviewDTO.getReviewRemark());
        }
        recordSignOperationLogDetail(OperationActionEnum.REVIEW_CONTRACT_PASS, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    /**
     * 协议审核通过
     *
     * @param contractSignEntity 协议实体
     * @return 审核通过结果
     */
    private boolean reviewPass(ContractSignEntity contractSignEntity) {
        List<FileInfoEntity> fileInfoEntityList = fileBusinessFacade.getFileInfoByBizIdAndType(contractSignEntity.getId(),
                FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(),
                DisableStatusEnum.ENABLE.getValue());

        //查询签章通用状态是否开启
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        SystemRuleVO.SystemRuleItemVO systemRuleItemVO = systemRuleVO.getSystemRuleItemVOList().get(0);


        boolean yqqSys = systemRuleItemVO.getStatus().equals(DisableStatusEnum.ENABLE.getValue());
        boolean yqqAuth = this.customerYqqAuth(contractSignEntity);

        //调用易企签配置
        YqqSignParameterEntity yqqSignParameterEntity = yqqSignParameterFacade.queryYqqSignParameterByCompanyId(contractSignEntity.getCompanyId());

        Boolean b = yqqSignParameterVerify(yqqSignParameterEntity, contractSignEntity);

        if (b) {
            return true;
        }

        if (yqqSys) {
            Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();
            CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
            customerOriginalPaperDTO.setCustomerId(customerId)
                    .setCategoryId(contractSignEntity.getGoodsCategoryId())
                    .setCompanyId(contractSignEntity.getCompanyId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setSaleType(contractSignEntity.getSalesType())
            ;

            fileInfoEntityList.forEach(fileInfoEntity -> {

                String sasToken = azureBlobUtil.getSharedAccessSignature();
                //解析文件路径

                String filePathUrl = urlPathAnalysis(fileInfoEntity.getFilePathUrl());
                String url = filePathUrl + sasToken;
                log.info("------------------------------------------------------------------");
                log.info("FilePathUrl:{}", url);
                log.info("------------------------------------------------------------------");

                //获取文件页数
                Integer pageNo = FilePagesUtils.filesPage(url, fileInfoEntity.getExtension());


                // 发起签章
                StartEnvelopeDTO startEnvelopeDTO = new StartEnvelopeDTO()
                        .setLoginId(Integer.parseInt(JwtUtils.getCurrentUserId()))
                        .setContractId(contractSignEntity.getId())
                        .setContractCode(contractSignEntity.getContractCode())
                        .setContractSignId(contractSignEntity.getId())
                        .setUuid(contractSignEntity.getUuid())
                        .setCustomerId(contractSignEntity.getCustomerId())
                        .setFileId(fileInfoEntity.getId()).setPageNo(pageNo)
                        .setExtension(fileInfoEntity.getExtension())
                        .setUrl(url).setSystem(SystemEnum.MAGELLAN.getValue())
                        .setTitle(contractSignEntity.getContractCode())
                        .setLdcFrame(null != contractSignEntity.getLdcFrame() ? contractSignEntity.getLdcFrame() : LdcFrameEnum.LDC.getValue())
                        .setCompanyId(contractSignEntity.getCompanyId())
                        .setYqqSignParameterEntity(yqqSignParameterEntity)
                        .setIsCustomerUseYqq(yqqAuth);
                //签章返回签章状态
                signatureFacade.envelopesStart(startEnvelopeDTO);
                contractSignEntity
                        .setIsOnLineSign(IsOnLineSignEnum.ON_LINE.getValue())
                        .setSignatureType(yqqAuth ? SignatureTypeEnum.BOTH_SIGNATURE.getValue() : SignatureTypeEnum.LDC_ONE_SIGNATURE.getValue())
                        .setVoluntarilySignType(VoluntarilySignTypeEnum.NODE_START.getValue())
                        .setSignErrorMessage(ContractSignYQQErrorEnum.SUCCEED.getMessage());
            });
        } else {
            contractSignEntity.setSignErrorMessage(ContractSignYQQErrorEnum.NOT_AUTH.getMessage()).setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue());
        }

        contractSignDomainService.updateSignEntity(contractSignEntity);
        recordSignOperationLogDetail(OperationActionEnum.SIGNATURE_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    /**
     * 判断易企签配置
     *
     * @param yqqSignParameterEntity
     * @param contractSignEntity
     * @return
     */
    private Boolean yqqSignParameterVerify(YqqSignParameterEntity yqqSignParameterEntity, ContractSignEntity contractSignEntity) {
        if (null == yqqSignParameterEntity) {
            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("未找到易企签配置");
            contractSignDomainService.updateSignEntity(contractSignEntity);
            return true;
        } else if (DisableStatusEnum.DISABLE.getValue().equals(yqqSignParameterEntity.getStatus())) {
            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("易企签配置已被禁用");
            contractSignDomainService.updateSignEntity(contractSignEntity);
            return true;
        } else if (StringUtil.isEmpty(yqqSignParameterEntity.getAppId())
                || StringUtil.isEmpty(yqqSignParameterEntity.getAppSecretKey())
                || StringUtil.isEmpty(yqqSignParameterEntity.getSealName())) {

            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("配置缺少参数");
            contractSignDomainService.updateSignEntity(contractSignEntity);
            return true;
        }

        return false;
    }

    /**
     * 文件路径解析
     *
     * @param url
     * @return
     */
    private String urlPathAnalysis(String url) {
        int index = url.lastIndexOf("/");
        String hostUrl = getHostUrl();
        try {
            //判断路径层级
            if (index >= 0) {
                String uFileName = url.substring(index);
                String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
                String pathDir = url.substring(0, index);
                url = pathDir + fileNameEncode;
            } else {
                String fileNameEncode = URLEncoder.encode(url, "UTF-8");
                url = hostUrl + fileNameEncode;
            }
        } catch (Exception ex) {
            log.error("FileNotFoundException", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        return url;
    }

    /**
     * 获取文件访问url域名路径
     */
    public String getHostUrl() {
        String host = azureBlobProperties.getHost();
        String env = azureBlobProperties.getEnv();
        String containName = azureBlobProperties.getContainName();
        return host + "/" + containName + "/" + env + "/";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean contractSignStartSignature(ContractSignReviewDTO signReviewDTO) {

        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_STAMP.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR);
        }

        if (VoluntarilySignTypeEnum.NODE_START.getValue().equals(contractSignEntity.getVoluntarilySignType()) || VoluntarilySignTypeEnum.VOLUNTARILY_SIGN_FAILED.getValue().equals(contractSignEntity.getVoluntarilySignType())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR);
        }

        reviewPass(contractSignEntity);
        return true;
    }


    @Override
    public boolean UpdateContractSignError(ContractSignErrorDTO contractSignErrorDTO) {
        ContractSignEntity contractSignEntity = contractSignQueryDomainService.queryByUUId(contractSignErrorDTO.getUuid());
        if (null != contractSignEntity) {
            contractSignEntity.setSignErrorCode(contractSignErrorDTO.getSignErrorCode()).setSignErrorMessage(contractSignErrorDTO.getSignErrorMessage());
            return contractSignDomainService.updateSignEntity(contractSignEntity);
        }
        return false;
    }

    /**
     * 协议审核驳回
     *
     * @param contractSignEntity 协议实体
     * @param reviewRemark       驳回原因
     * @return 审核驳回结果
     */
    private boolean reviewReject(ContractSignEntity contractSignEntity, String reviewRemark) {
        // 审核驳回:待出具,清除文件信息
        fileBusinessFacade.dropFileRelation(contractSignEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), reviewRemark);

        contractSignEntity.setRejectReason(StringUtil.isEmpty(reviewRemark) ? "未填写" : reviewRemark);

        // 记录驳回原因
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_PROVIDE.getValue(), reviewRemark);

        // 记录审核日志
        recordSignOperationLogDetail(OperationActionEnum.REVIEW_CONTRACT_REJECT, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean abnormalContractSign(Integer contractSignId, String remark) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(contractSignId);

        // 更新状态
        contractSignDomainService.updateSignEntity(contractSignEntity.setStatus(ContractSignStatusEnum.ABNORMAL.getValue()).setInvalidReason(remark));

        // 清除所有文件信息
        FileCategoryType.getAllContractPdfType().forEach(type -> fileBusinessFacade.dropFileRelation(contractSignId, type, ""));

        // 记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.ABNORMAL_SALES_CONTRACT_SIGN, "", null);
        recordSignOperationLogDetail(OperationActionEnum.ABNORMAL_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidContractSign(ContractSignReviewDTO signReviewDTO) {
        // add zengshl 根据合同获取协议数据
        if (ObjectUtil.isNotEmpty(signReviewDTO.getContractId())) {
            List<ContractSignEntity> contractSignEntities = contractSignQueryDomainService.queryByContractId(signReviewDTO.getContractId());
            if (ObjectUtil.isNotEmpty(contractSignEntities) && contractSignEntities.size() > 0) {
                signReviewDTO.setContractSignId(contractSignEntities.get(0).getId());
            }
        }
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());
        // 待回签状态需要出具取消声明的TT
        Integer signStatus = contractSignEntity.getStatus();
        ContractSignEntity canalSignEntity = BeanConvertUtils.map(ContractSignEntity.class, contractSignEntity);
        // 更新状态
        contractSignDomainService.updateSignEntity(contractSignEntity
                // FIXME 作废的显示问题
                .setStatus(ContractSignStatusEnum.INVALID.getValue()).setInvalidReason(signReviewDTO.getReviewRemark()));

        // 清除所有文件信息
        FileCategoryType.getAllContractPdfType().forEach(type -> fileBusinessFacade.dropFileRelation(contractSignEntity.getId(), type, signReviewDTO.getReviewRemark()));
        // 记录操作日志
        recordSignOperationLogDetail(OperationActionEnum.INVALID_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());

        // TODO 合同取消TT，待审批，合同取消协议，待出具；不产生新合同关联协议的原合同【是否是复制的方式】
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == signStatus) {
            ArrangeContext arrangeContext = new ArrangeContext();
            TTDTO ttdto = new TTDTO();
            // 获取合同
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractSignEntity.getContractId());
            ttdto.setBuCode(contractEntity.getBuCode());
            ttdto.setTtType(TTTypeEnum.CONTRACT_CANCEL.getType());
            //2.2 创建TT
            arrangeContext.setContractCode(contractEntity.getContractCode());
            arrangeContext.setContractId(contractEntity.getId());
            ttdto.setContractEntity(contractEntity);
            // 处理数据
            SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
            salesContractAddTTDTO.setContractId(contractEntity.getId());
            salesContractAddTTDTO.setSourceContractId(contractEntity.getId());
            // 价格处理
            ContractPriceEntity contractPriceEntity = contractQueryLogicService.getContractPriceEntityContractId(contractEntity.getId());
            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
            ttdto.setPriceDetailBO(priceDetailBO);
            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
            // 创建TT  TODO 先注释掉，要分结构化TT和非结构化TT的内容，结构话TT取不到价格报错
//            ttLogicService.saveTT(ttdto, arrangeContext);
//            TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
//            TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
//            // 创建待出具的协议
//            canalSignEntity.setTtId(tradeTicketEntity.getId());
//            canalSignEntity.setTtType(TTTypeEnum.CONTRACT_CANCEL.getType());
//            canalSignEntity.setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
//            contractSignDomainService.saveContractSign(canalSignEntity);
//            // 协议和TT的关系
//            tradeTicketDO.getTradeTicketEntity().setSignId(canalSignEntity.getId()).setProtocolCode(canalSignEntity.getProtocolCode());
//            ttLogicService.updateContractId(tradeTicketDO);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContractSignYQQUrl(ContractSignYQQUrlDTO contractSignYQQUrlDTO) {
        ContractSignEntity contractSignEntity = contractSignQueryDomainService.queryByUUId(contractSignYQQUrlDTO.getUuid());

        //判断协议是否为空
        if (contractSignEntity == null) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        //协议状态
        Integer status = contractSignYQQUrlDTO.getStatus();
        if (null == status) {
            status = contractSignEntity.getStatus();
        }

        Integer oldStatus = contractSignEntity.getStatus();
        // 状态是否改变: 下一个状态是否满足
        if (oldStatus == ContractSignStatusEnum.PROCESSING.getValue() || oldStatus == ContractSignStatusEnum.INVALID.getValue() || oldStatus == ContractSignStatusEnum.ABNORMAL.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_STATUS_EXCEPTION);
        }

        contractSignEntity = BeanConvertUtils.convert(ContractSignEntity.class, contractSignYQQUrlDTO);

        //协议签章地址
        String url = contractSignYQQUrlDTO.getSignatureUrl();
        //协议签章状态
        Integer signatureStatus = contractSignEntity.getSignatureStatus();

        contractSignDomainService.updateSignEntity(contractSignEntity);
        try {
            recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        } catch (Exception e) {
            log.error("记录日志错误");
        }
        return true;
    }

    @Override
    public boolean updateContractSignYQQCallback(ContractSignYQQCallbackDTO contractSignYQQCallbackDTO) {
        log.info("updateContractSignYQQCallback.begin:.uuid:{} -- data:{}", contractSignYQQCallbackDTO.getUuid(), JSON.toJSONString(contractSignYQQCallbackDTO));

        ContractSignEntity contractSignEntity = contractSignQueryDomainService.queryByUUId(contractSignYQQCallbackDTO.getUuid());

        //判断协议是否为空
        if (contractSignEntity == null) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        if (null != contractSignYQQCallbackDTO.getStatus()
                && ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignYQQCallbackDTO.getStatus()
                && StrUtil.isBlank(contractSignYQQCallbackDTO.getSignatureUrl())
                && SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType()) {
            sendSignJudgeInmail(contractSignEntity, contractSignYQQCallbackDTO);
            //记录日志
            recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
            return true;
        }

        Integer oldStatus = contractSignEntity.getStatus();
        // 状态是否改变: 下一个状态是否满足
        if (oldStatus == ContractSignStatusEnum.PROCESSING.getValue() || oldStatus == ContractSignStatusEnum.INVALID.getValue() || oldStatus == ContractSignStatusEnum.ABNORMAL.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_STATUS_EXCEPTION);
        }
        contractSignEntity.setStatus(null == contractSignYQQCallbackDTO.getStatus() ? contractSignEntity.getStatus() : contractSignYQQCallbackDTO.getStatus());
        contractSignEntity.setSignatureUrl(StrUtil.isBlank(contractSignYQQCallbackDTO.getSignatureUrl()) ? "" : contractSignYQQCallbackDTO.getSignatureUrl());
        contractSignEntity.setSignatureStatus(null == contractSignYQQCallbackDTO.getSignatureStatus() ? contractSignEntity.getSignatureStatus() : contractSignYQQCallbackDTO.getSignatureStatus());
        contractSignEntity.setVoluntarilySignType(null == contractSignYQQCallbackDTO.getVoluntarilySignType() ? contractSignEntity.getVoluntarilySignType() : contractSignYQQCallbackDTO.getVoluntarilySignType());

        contractSignEntity.setSignErrorCode("").setSignErrorMessage("");

        contractSignDomainService.updateSignEntity(contractSignEntity);

        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus()
                && (TTTypeEnum.REVERSE_PRICE.getType().equals(contractSignEntity.getTtType()))) {
            //点价转月修改原合同状态
            priceUpdateContractStatusByWaitBack(contractSignEntity);
        }
        sendSignJudgeInmail(contractSignEntity, contractSignYQQCallbackDTO);
        //记录日志
        recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        log.info("updateContractSignYQQCallback.finish.uuid:{} -- data:{}", contractSignEntity.getUuid(), JSON.toJSONString(contractSignEntity));
        return true;
    }

    //点价返点价修改
    private void priceUpdateContractStatusByWaitBack(ContractSignEntity contractSignEntity) {

        Integer contractId = contractSignEntity.getContractId();

        Integer contractStatus = ContractStatusEnum.EFFECTIVE.getValue();
        //查询TT
        TradeTicketQO qo = new TradeTicketQO();
        qo.setTtId(contractSignEntity.getTtId());
        TradeTicketEntity tradeTicketEntity = ttQueryLogicService.fetchTradeTicketEntity(qo);
        //根据GroupID查询出原合同TT
        qo.setTtId(tradeTicketEntity.getId());
        qo.setGroupId(tradeTicketEntity.getGroupId());
        TradeTicketEntity tradeTicketGroup = ttQueryLogicService.fetchTradeTicketEntity(qo);
        //获取原合同id
        contractId = tradeTicketGroup.getContractId();
        //根据ttID查询 ttTranfer
        TTTranferEntity ttTranferEntity = ttTranferService.selectTTTranferByTTId(tradeTicketEntity.getId());
        if (null != ttTranferEntity) {
            List<TTTranferEntity> ttTranferEntities = ttTranferService.getTTTranferByContractId(contractId, ttTranferEntity.getId());
            for (TTTranferEntity ttTranfer : ttTranferEntities) {
                ContractSignEntity contractSign = contractSignQueryDomainService.getSignDetailByTtId(ttTranfer.getTtId());
                if (null != contractSign && ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSign.getStatus()) {
                    contractStatus = ContractStatusEnum.MODIFYING.getValue();
                    break;
                }
            }
        }
        //修改合同状态
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);
        contractEntity.setStatus(contractStatus);
        contractDomainService.updateContractById(contractEntity);

    }

    private void sendSignJudgeInmail(ContractSignEntity contractSignEntity, ContractSignYQQCallbackDTO contractSignYQQCallbackDTO) {
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus() && StrUtil.isNotBlank(contractSignYQQCallbackDTO.getSignatureUrl())) {
            log.info("========================================");
            log.info("updateContractSignYQQCallback.finish.uuid:{} -- Status:{}", contractSignEntity.getUuid(), contractSignEntity.getStatus());
            CustomerEntity customerEntity = customerFacade.getCustomerById(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerId() : Integer.parseInt(contractSignEntity.getSupplierId()));
            log.info("updateContractSignYQQCallback.finish.uuid:{},customerEntity:{}", contractSignEntity.getUuid(), JSON.toJSONString(customerEntity));
            if (null != customerEntity && GeneralEnum.YES.getValue().equals(customerEntity.getIsColumbus())) {
                try {
                    log.info("========================================");
                    sendSignInmail(contractSignEntity, customerEntity);
                } catch (Exception e) {
                    log.debug("sendSignInmail.Exception{}", e.getMessage());
                }
            }

        }
    }

    /**
     * 上传文件,给客户发送站内信
     *
     * @param contractSignEntity
     * @param customerEntity
     */
    public void sendSignInmail(ContractSignEntity contractSignEntity, CustomerEntity customerEntity) {
        log.info("sendSignInmail:messageFacade.sendMessage:TtCode{},sendStart", contractSignEntity.getTtCode());

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_SIGN_INMAIL.getDesc());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_SIGN_INMAIL.name());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setCustomerId(customerEntity.getId());

        Integer roleDef = ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? 203 : 206;
        log.info("sendSignInmail.roleDef:TtCode:{},roleDef:{}", contractSignEntity.getTtCode(), roleDef);
        Integer salesType = ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? ContractSalesTypeEnum.PURCHASE.getValue() : ContractSalesTypeEnum.SALES.getValue();

        List<CRoleEntity> cRoleEntities = cRoleFacade.queryRoleListByDefInfosSalesType(Arrays.asList(roleDef), contractSignEntity.getGoodsCategoryId(), salesType);
        log.info("sendSignInmail.roleDef:TtCode:{},cRoleEntities", contractSignEntity.getTtCode());
        if (cRoleEntities.isEmpty()) {
            return;
        }
        CRoleEntity roleEntity = cRoleEntities.get(0);
        List<String> receiver = new ArrayList<>();
        receiver.add(String.valueOf(roleEntity.getId()));
        messageInfoDTO.setReceivers(receiver);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("contractCode", contractSignEntity.getContractCode());
        dataMap.put("TTCode", contractSignEntity.getTtCode());
        messageInfoDTO.setDataMap(dataMap);

        messageFacade.sendMessage(messageInfoDTO);

        log.info("sendSignInmail:messageFacade.sendMessage:TtCode{},sendAccomplish", contractSignEntity.getTtCode());
    }


    /**
     * 上传文件,给客户发送邮件
     *
     * @param contractSignEntity
     */
    public void signSendBothSignatureMail(ContractSignEntity contractSignEntity) {

        StringBuilder errorMsg = new StringBuilder();

        try {
            //记录日志
            operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail", JSON.toJSONString(contractSignEntity)));
        } catch (Exception e) {
            log.error("记录日志错误signSendBothSignatureMail:{}", JSON.toJSONString(e));
        }
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        messageInfoDTO.setReferBizId(contractSignEntity.getId().toString());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name());
        messageInfoDTO.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setSalesType(contractSignEntity.getSalesType());

        Integer customerId;
        if (contractSignEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            customerId = contractSignEntity.getCustomerId();
        } else {
            customerId = Integer.valueOf(contractSignEntity.getSupplierId());
        }

        try {
            List<String> receivers = new ArrayList<>();
            receivers.add(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                    ? contractSignEntity.getSupplierId() : customerId.toString());
            messageInfoDTO.setReceivers(receivers);
        } catch (NumberFormatException e) {
            errorMsg.append("获取客户联系人异常");
        }
        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = getCustomerEmailData(contractSignEntity);

        //业务数据
        messageInfoDTO.setDataMap(BeanUtil.beanToMap(customerSignNoticeItemDTO));

        //发送消息
        try {
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            errorMsg.append("消息服务异常:" + e.getMessage());
        }

        try {
            //记录traceLog
            if (StringUtil.isNotEmpty(errorMsg.toString())) {
                //记录日志
                operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail.Exception", JSON.toJSONString(errorMsg)));
            }
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }

    }

    /**
     * 保存快递信息
     *
     * @param contractPaperDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveContractPaperSign(ContractPaperDTO contractPaperDTO) {

        boolean resultRow = false;
        ContractPaperEntity contractPaperEntity = contractSignQueryDomainService.getContractPaper(contractPaperDTO.getContractSignId());
        ContractSignEntity contractSignEntity = checkContractSign(contractPaperDTO.getContractSignId());

        if (null != contractPaperEntity) {
            //修改快递信息
            contractPaperEntity = BeanConvertUtils.convert(ContractPaperEntity.class, contractPaperDTO);
            contractPaperEntity.setUpdatedAt(new Date());
            contractSignDomainService.updatePaperEntity(contractPaperEntity);
            //修改协议状态
            contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
            return contractSignDomainService.updateSignEntity(contractSignEntity) ? 1 : 0;
        }

        // 保存合同的正本信息
        contractSignDomainService.saveContractPaper(contractPaperDTO);

        // 修改协议状态
        contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
        resultRow = contractSignDomainService.updateSignEntity(contractSignEntity);

        try {
            recordSignOperationLogDetail(OperationActionEnum.SAVE_CONTRACT_PAPER, contractSignEntity, SystemEnum.MAGELLAN.getName());
        } catch (Exception e) {
            log.debug("saveContractPaperSign:{}", e.getMessage());
        }

        return resultRow ? 1 : 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean postBackContractSign(ContractBaseSignDTO contractBaseSignDTO, Integer status, OperationActionEnum operationActionEnum) {
        // 通过页面的文件上传到服务器，返回文件信息
        // 校验合同
        ContractSignEntity contractSignEntity = checkContractSign(contractBaseSignDTO.getContractSignId());

        // 校验文件信息
        if (ArrayUtil.isEmpty(contractBaseSignDTO.getFileIdList())) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
        }

        int fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode();

        if (ContractSignStatusEnum.WAIT_CONFIRM.getValue() == status) {
            fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode();
        }

        // 保存文件关系表
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(contractBaseSignDTO.getContractSignId())
                .setCategoryType(fileCategoryType)
                .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setFileIdList(Arrays.stream(contractBaseSignDTO.getFileIdList()).collect(Collectors.toList())));

        // 更改合同状态为待确认合规
        updateStatus(contractSignEntity, status, "");

        //上传发送站内信
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == status) {
            CustomerEntity customerEntity = customerFacade.getCustomerById(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerId() : Integer.parseInt(contractSignEntity.getSupplierId()));
            //点价/反点价修改协议状态
            if (TTTypeEnum.REVERSE_PRICE.getType().equals(contractSignEntity.getTtType())) {
                //点价转月修改原合同状态
                priceUpdateContractStatusByWaitBack(contractSignEntity);
            }
            sendSignInmail(contractSignEntity, customerEntity);
            sendCustomerNoticeEmail(contractSignEntity);
        }

        SystemEnum systemEnum = SystemEnum.getByValue(contractBaseSignDTO.getSystem());
        recordSignOperationLogDetail(operationActionEnum, contractSignEntity, systemEnum.getName());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContractSign(Integer ttId) {
        ContractSignEntity contractSignEntity = contractSignQueryDomainService.getSignDetailByTtId(ttId);
        return contractSignDomainService.updateSignEntity(contractSignEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue()));
    }

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return contractSignDomainService.updateContractId(ttId, contractId);
    }

    @Override
    public boolean updateContractSignStatus(Integer signId, Integer status, String memo) {
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);
        contractSignEntity.setStatus(status);
        if (StringUtils.isNotBlank(memo)) {
            String memoInfo = StringUtils.isNotBlank(contractSignEntity.getMemo()) ? contractSignEntity.getMemo() : "";
            contractSignEntity.setMemo(memoInfo + memo);
        }
        return contractSignDomainService.updateSignEntity(contractSignEntity);
    }

    @Override
    public boolean deleteContractSignById(Integer signId) {
        //基差修改为一口价，合同全部定价后，在合同详情中点击【定价完成】按钮，确认提交后，立即生效（不可撤回、作废）；无审批、协议出具；系统会自动生成修改的TT，TT记录了合同类型修改为一口价。合同其他数据保持不变。
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);
        contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue()).setIsDeleted(IsDeletedEnum.DELETED.getValue());
        return contractSignDomainService.updateSignEntity(contractSignEntity);
    }

    @Override
    public Result sendCustomerNoticeEmail(Integer signId) throws Exception {
        //获取签章记录
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);

        sendCustomerNoticeEmail(contractSignEntity);

        return Result.success();
    }

    @Override
    public boolean updateSignWarrantId(Integer signId, Integer warrantId, String warrantCode) {
        return contractSignDomainService.updateSignWarrantId(signId, warrantId, warrantCode);
    }

    public void sendCustomerNoticeEmail(ContractSignEntity contractSignEntity) {
        StringBuilder errorMsg = new StringBuilder();
        //记录日志
        operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail", JSON.toJSONString(contractSignEntity)));

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        messageInfoDTO.setReferBizId(contractSignEntity.getId().toString());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGN_NOTICE.name());
        messageInfoDTO.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setSalesType(contractSignEntity.getSalesType());

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = null;

        try {
            //拼装客户签章提醒的业务数据
            customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();
            customerSignNoticeItemDTO = getCustomerNoticeEmailData(contractSignEntity);
        } catch (Exception e) {
            errorMsg.append("获取签章记录数据异常；");
        }
        try {
            Integer customerId = customerSignNoticeItemDTO.getCustomerId();
            List<String> receivers = new ArrayList<>();
            receivers.add(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? contractSignEntity.getSupplierId() : customerId.toString());
            messageInfoDTO.setReceivers(receivers);
        } catch (NumberFormatException e) {
            errorMsg.append("获取客户联系人异常");
        }

        //业务数据
        messageInfoDTO.setDataMap(BeanUtil.beanToMap(customerSignNoticeItemDTO));

        operationLogFacade.recordOperationLog(new OperationDetailDTO()
                .setBizCode(LogBizCodeEnum.SEND_CUSTOMER_NOTICE_MAIL.getBizCode())
                .setBizModule(ModuleTypeEnum.MSG.getDesc())
                .setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setMetaData(JSON.toJSONString(contractSignEntity)));
        //发送消息
        try {
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            errorMsg.append("消息服务异常:" + e.getMessage());
        }

        //记录traceLog
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            //记录日志
            operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail.Exception", JSON.toJSONString(errorMsg)));
        }
    }

    public CustomerSignNoticeItemDTO getCustomerEmailData(ContractSignEntity contractSignEntity) {

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();

        customerSignNoticeItemDTO.setSignId(contractSignEntity.getId())
                .setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name())
                .setFactoryCode(contractSignEntity.getDeliveryFactoryCode())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSalesType(contractSignEntity.getSalesType())
                .setCategoryCode(GoodsCategoryEnum.getDescByValue(contractSignEntity.getGoodsCategoryId()))
                .setContractCode(contractSignEntity.getContractCode())
                .setContractFilePath(contractSignEntity.getSignatureUrl())
                .setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId())
                .setCustomerName(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());
        //customerSignNoticeItemDTO.setSender(ReceiverTypeEnum.FACTORY_MAIL.getValue());

        return customerSignNoticeItemDTO;
    }


    public CustomerSignNoticeItemDTO getCustomerNoticeEmailData(ContractSignEntity contractSignEntity) {

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();

        customerSignNoticeItemDTO.setSignId(contractSignEntity.getId())
                .setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGN_NOTICE.name())
                .setFactoryCode(contractSignEntity.getDeliveryFactoryCode())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSalesType(contractSignEntity.getSalesType())
                .setCategoryCode(GoodsCategoryEnum.getDescByValue(contractSignEntity.getGoodsCategoryId()))
                .setContractCode(contractSignEntity.getContractCode())
                .setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId())
                .setCustomerName(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());

        String filePath = "";

        //获取签章文件
        List<FileInfoEntity> fileInfoEntityList = fileBusinessFacade.getFileInfoByBizIdAndType(contractSignEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), null);
        if (CommonListUtil.notNullOrEmpty(fileInfoEntityList)) {
            filePath = fileInfoEntityList.get(0).getFileUrl();
            customerSignNoticeItemDTO.setContractFilePath(filePath);
        }

        //customerSignNoticeItemDTO.setSender(ReceiverTypeEnum.FACTORY_MAIL.getValue());

        return customerSignNoticeItemDTO;
    }

    /**
     * 校验易企签是否实名
     *
     * @param contractSignEntity
     * @return
     */
    private boolean customerYqqAuth(ContractSignEntity contractSignEntity) {
        //校验客户是否是实名认证
        if (ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(contractSignEntity.getCustomerId());
            //判断客户是否使用易企签
            if (customerDTO.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue()) && customerDTO.getIsColumbus().equals(GeneralEnum.YES.getValue())) {
                return true;
            }
            return false;
        } else {
            CustomerDTO customerDTO = customerFacade.getCustomerById(Integer.valueOf(contractSignEntity.getSupplierId()));
            //判断客户是否使用易企签
            if (customerDTO.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue()) && customerDTO.getIsColumbus().equals(GeneralEnum.YES.getValue())) {
                return true;
            }
            return false;
        }
    }


    /**
     * 校验协议
     *
     * @param contractSignId 协议ID
     * @return 协议信息
     */
    private ContractSignEntity checkContractSign(Integer contractSignId) {
        ContractSignEntity contractSignEntity = contractSignQueryDomainService.getById(contractSignId);
        // 是否存在
        if (null == contractSignEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }
        return contractSignEntity;
    }

    private ContractSignEntity checkContractSign1(Integer contractSignId) {
        return contractSignQueryDomainService.getById(contractSignId);
    }

    public boolean updateStatus(ContractSignEntity contractSignEntity, int status, String remark) {
        // 是否存在
        if (null == contractSignEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }

        return contractSignDomainService.updateSignEntity(contractSignEntity.setStatus(status).setRejectReason(remark));
    }

    /**
     * 协议操作日志记录
     *
     * @param contractSignEntity 合同信息
     * @param bizCodeEnum        操作枚举
     * @param data               数据
     * @param systemEnum         操作系统
     */
    private void addContractSignOperationLog(ContractSignEntity contractSignEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractSignEntity.getId())
                .setReferBizCode(contractSignEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setData(data)
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());

        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue()).setSource(OperationSourceEnum.EMPLOYEE.getValue()).setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            //客户操作记录,获取获取客户id
            Integer customerId = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCompanyId();
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue()).setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue()).setOperatorId(customerId);
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue()).setSource(OperationSourceEnum.SYSTEM.getValue()).setOperatorId(1);
        }
        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractSignEntity confirmContract(ContractBaseDTO contractBaseDTO) {
        OperationActionEnum operationActionEnum;
        // 校验合同
        ContractSignEntity contractSignEntity = checkContractSign(contractBaseDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_CONFIRM.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_CHECKED);
        }
        // 通过合同查看正本内容判断进入执行中
        if (contractBaseDTO.getReviewStatus().equals(ContractReviewStatusEnum.PASS.getValue())) {
            // 判断合同是否需要正本
            Integer newStatus = ContractSignStatusEnum.PROCESSING.getValue();

            Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();

            CustomerEntity customerEntity = customerFacade.getCustomerById(customerId);

            String paperMemo = "";
            //校验合同是否是新合同
            ContractSignEntity signEntity = contractSignQueryDomainService.queryContractSignByContractCode(contractSignEntity.getContractCode());
            log.info("===============================================================================signEntity:{}",JSONUtil.toJsonStr(signEntity));
            if (null != signEntity && !contractBaseDTO.getContractSignId().equals(signEntity.getId())) {

                if (TTTypeEnum.REVISE.getType().equals(contractSignEntity.getTtType()) && ContractSignStatusEnum.PROCESSING.getValue() == signEntity.getStatus()) {
                    //匹配规则
                    ContractSignEntity contractSign = checkPaperOverdueLimit(contractSignEntity);
                    newStatus = contractSign.getStatus();
                    paperMemo = contractSign.getPaperReason();
                    log.info("===============================================================================contractSign:{}",JSONUtil.toJsonStr(contractSign));
                } else {
                    newStatus = signEntity.getStatus();
                    paperMemo = "继承原合同状态";
                }
                log.info("===============================================================================newStatus:{}",newStatus);
                log.info("===============================================================================paperMemo:{}",paperMemo);
            } else if (!OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue().equals(contractSignEntity.getNeedOriginalPaper())) {
                //客户需要正本
                newStatus = ContractSignStatusEnum.PAPER.getValue();
                paperMemo = "客户需要正本，needOriginalPaper != 0";
            } else if (GeneralEnum.YES.getValue().equals(customerEntity.getIsAuthorization()) || BackStatusEnum.YQQ.getValue().equals(contractSignEntity.getBackStatus())) {
                //易企签回传或客户有授权书，到已完成
                newStatus = ContractSignStatusEnum.PROCESSING.getValue();

                paperMemo = GeneralEnum.YES.getValue().equals(customerEntity.getIsAuthorization()) ? "客户有授权书,customer.isAuthorization = 1" : "";
                paperMemo = paperMemo + (BackStatusEnum.YQQ.getValue().equals(contractSignEntity.getBackStatus()) ? "易企签回传,backStatus = 1" : "");
            } else {
                //匹配规则
                ContractSignEntity contractSign = checkPaperOverdueLimit(contractSignEntity);
                newStatus = contractSign.getStatus();
                paperMemo = contractSign.getPaperReason();
            }

            log.info("=================================================newStatus:" + newStatus);
            contractSignDomainService.updateSignEntity(contractSignEntity
                    .setStatus(newStatus)
                    .setRejectReason("")
                    .setUpdatedAt(new Date())
                    .setPaperReason(paperMemo)
            );
            // 同步合同到linkage
            operationActionEnum = OperationActionEnum.CONFIRM_CONTRACT_PASS;
            log.info("=================================================contractSignEntity.getStatus():" + contractSignEntity.getStatus());

            //结构化定价修改申请单状态
            if (ContractTypeEnum.STRUCTURE.getValue() == contractSignEntity.getContractType()) {
                List<Integer> contractIdS = new ArrayList<>();
                List<PriceApplyEntity> priceApplyEntities = priceApplyFacade.queryPriceApplyByContractId(contractSignEntity.getContractId());
                for (PriceApplyEntity priceApplyEntity : priceApplyEntities) {
                    contractIdS.add(priceApplyEntity.getId());
                }
                priceApplyFacade.batchPending(contractIdS);
            }

        } else {
            contractBaseDTO.setReviewRemark(StringUtil.isEmpty(contractBaseDTO.getReviewRemark()) ? "未填写" : contractBaseDTO.getReviewRemark());
            // 驳回合同进入待回传 ,清除文件信息
            fileBusinessFacade.dropFileRelation(contractBaseDTO.getContractSignId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), contractBaseDTO.getReviewRemark());

            contractSignEntity.setRejectReason(contractBaseDTO.getReviewRemark())
                    .setConfirmStatus(SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType() ? 1 : 0)
                    .setStatus(ContractSignStatusEnum.WAIT_BACK.getValue())
                    .setUpdatedAt(new Date());
            // 记录驳回原因
            contractSignDomainService.updateSignEntity(contractSignEntity);
            operationActionEnum = OperationActionEnum.CONFIRM_CONTRACT_REJECT;
        }

        // 记录操作日志
        recordSignOperationLogDetail(operationActionEnum, contractSignEntity, SystemEnum.MAGELLAN.getName());

        return contractSignEntity;
    }

    /**
     * 确认合规匹配规则
     *
     * @param contractSignEntity
     * @return
     */
    private ContractSignEntity checkPaperOverdueLimit(ContractSignEntity contractSignEntity) {
        ContractSignEntity contractSign = new ContractSignEntity();
        String paperMemo;
        Integer newStatus = ContractSignStatusEnum.PROCESSING.getValue();
        //是否超期超限
        paperMemo = "超期超限规则判断：";
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractSignEntity.getContractId());
        Map<String, Object> mapBizData = new HashMap<>();
        mapBizData.put("category1", contractSignEntity.getCategory1());
        mapBizData.put("category2", contractSignEntity.getCategory2());
        mapBizData.put("category3", contractSignEntity.getCategory3());
        mapBizData.put("buCode", contractSignEntity.getCategory3());
        mapBizData.put("salesType", contractSignEntity.getCategory3());
        mapBizData.put("contractNum", contractEntity.getContractNum());

        if (ObjectUtil.isNotEmpty(contractEntity.getDeliveryEndTime()) && ObjectUtil.isNotEmpty(contractEntity.getSignDate())) {
            long diffdiveryEndMonth = getTime(contractEntity.getSignDate(), contractEntity.getDeliveryEndTime());
            mapBizData.put("diffdiveryEndMonth", diffdiveryEndMonth);
            log.info("================================================================================================diffdiveryEndMonth:{}\n", JSON.toJSONString(diffdiveryEndMonth));

        }
        log.info("================================================================================================mapBizData:{}\n", JSON.toJSONString(mapBizData));
        paperMemo += JSON.toJSONString(mapBizData);
        CommonConfigRuleMatchDTO ruleMatchDTO = new CommonConfigRuleMatchDTO();
        ruleMatchDTO
                .setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                .setCategory1(contractSignEntity.getCategory1())
                .setCategory2(contractSignEntity.getCategory2())
                .setCategory3(contractSignEntity.getCategory3())
                .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setGroupCode(SystemCodeConfigEnum.SIGN_PAPER_RULE_CONFIG.getRuleCode())
                .setSalesType(contractSignEntity.getSalesType())
                .setMapBizData(mapBizData)
                .setTtCode(contractSignEntity.getTtCode())
                .setContractCode(contractSignEntity.getContractCode())
                .setReferCode(contractSignEntity.getTtCode())
                .setReferId(contractSignEntity.getId().toString())
        ;
        log.info("================================================================================================ruleMatchDTO:{}\n", JSON.toJSONString(ruleMatchDTO));


        Boolean isMatch = commonConfigFacade.matchConfigRuleInfo(ruleMatchDTO);

        log.info("============================================isMatch:" + isMatch);
        if (isMatch) {
            newStatus = ContractSignStatusEnum.PAPER.getValue();
        }
        paperMemo += "\n是否超期超限结果：" + isMatch;
        contractSign
                .setStatus(newStatus)
                .setPaperReason(paperMemo)
        ;
        return contractSign;
    }


    /**
     * 协议是否需要正本
     *
     * @param customerOriginalPaperEntity
     * @param contractSignEntity
     * @param signCreateDTO
     * @return
     */
    private Integer isNeedOriginalPaper(CustomerOriginalPaperEntity customerOriginalPaperEntity, ContractSignEntity contractSignEntity, ContractSignCreateDTO signCreateDTO) {
        // 判断是否需要正本
        //String ttCode = contractSignEntity.getTtCode();
        //查询原始TT
        /*if (ttCode.contains("-")) {
            ttCode = ttCode.substring(0, ttCode.indexOf("-"));
            //继承初始TT正本信息
            ContractSignEntity contractSign = contractSignQueryDomainService.queryByTTCode(ttCode);
            if (null != contractSign) {
                //不为空时继承初始正本状态
                return contractSign.getNeedOriginalPaper();

                *//*if (OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue().equals(contractSign.getNeedOriginalPaper())) {
                    //超期需要正本
                    if (null != signCreateDTO && null != signCreateDTO.getDeliveryEndTime() && getTime(signCreateDTO.getSignDate(), signCreateDTO.getDeliveryEndTime())) {
                        //合同签订日期比交货截至日期小于两个月需要正本
                        return OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue();
                    }
                } else if (OriginalPaperEnum.ORIGINAL_PAPER.getValue().equals(contractSign.getNeedOriginalPaper())) {
                    return contractSign.getNeedOriginalPaper();
                }*//*
            }
        }*/

        ContractSignEntity signEntity = contractSignQueryDomainService.queryContractSignByContractCode(contractSignEntity.getContractCode());
        if (null != signEntity) {
            return signEntity.getNeedOriginalPaper();
        }

        //1,LDC需要正本 non-frame
        if (null != customerOriginalPaperEntity && LdcFrameEnum.NOT_FRAME.getValue().equals(customerOriginalPaperEntity.getLdcFrame())) {
            //是non-frame合同的时候ldc需要正本
            return OriginalPaperEnum.LDC_ORIGINAL_PAPER.getValue();
        }
        //客户需要正本
        if (null != customerOriginalPaperEntity && OriginalPaperEnum.ORIGINAL_PAPER.getValue().equals(customerOriginalPaperEntity.getOriginalPaper())) {
            return OriginalPaperEnum.ORIGINAL_PAPER.getValue();
        }
        /*//超期需要正本 分配和转让没有过来的提货时间
        if (ObjectUtil.isNotEmpty(signCreateDTO.getDeliveryEndTime()) &&
                getTime(signCreateDTO.getSignDate(), signCreateDTO.getDeliveryEndTime())) {
            //合同签订日期比交货截至日期小于两个月需要正本
            return OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue();
        }
        Map<String, Object> mapBizData = new HashMap<>();
        mapBizData.put("category1", contractSignEntity.getCategory1());
        mapBizData.put("category2", contractSignEntity.getCategory2());
        mapBizData.put("category3", contractSignEntity.getCategory3());
        mapBizData.put("buCode", contractSignEntity.getCategory3());
        mapBizData.put("salesType", contractSignEntity.getCategory3());
        mapBizData.put("contractNum", signCreateDTO.getContractNum());

        if(ObjectUtil.isNotEmpty(signCreateDTO.getDeliveryEndTime()) && ObjectUtil.isNotEmpty(signCreateDTO.getSignDate())){
            long diffdiveryEndMonth = getTime(signCreateDTO.getSignDate(), signCreateDTO.getDeliveryEndTime());
            mapBizData.put("diffdiveryEndMonth", diffdiveryEndMonth);
        }

        CommonConfigRuleMatchDTO ruleMatchDTO = new CommonConfigRuleMatchDTO();
        ruleMatchDTO
                .setSystemId(SystemEnum.MAGELLAN.getName())
                .setCategory1(contractSignEntity.getCategory1())
                .setCategory2(contractSignEntity.getCategory2())
                .setCategory3(contractSignEntity.getCategory3())
                .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setGroupCode(SystemCodeConfigEnum.CONTRACT_PAPER_CONFIG.getRuleCode())
                .setSalesType(contractSignEntity.getSalesType())
                .setMapBizData(mapBizData)
        ;

        Boolean isMatch = commonConfigFacade.matchConfigRuleInfo(ruleMatchDTO);
        if (isMatch) {
            return OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue();
        }
        */
        return 0;
        // return exceedExpectOriginalPaper(contractEntity);
    }


    public Integer exceedExpectOriginalPaper(ContractEntity contractEntity) {
        //超限需要正本
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(contractEntity.getGoodsCategoryId())) {
            //豆粕合同数量>2000需要正本
            return BigDecimalUtil.isGreater(contractEntity.getContractNum(), new BigDecimal(2000)) ? OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
        }
        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(contractEntity.getGoodsCategoryId())) {
            //豆油合同数量>600需要正本
            return BigDecimalUtil.isGreater(contractEntity.getContractNum(), new BigDecimal(600)) ? OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
        }
        //客户需要正本
        return OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
    }


    /**
     * 判断是否大于两个月
     *
     * @param signDate
     * @param deliveryEndTime
     * @return
     */
    private long getTime(Date signDate, Date deliveryEndTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(signDate);
        calendar.add(Calendar.MONTH, -1);
        signDate = calendar.getTime();
        //算出signDate和signDate中间差了几个月
        long month = DateUtil.betweenMonth(signDate, deliveryEndTime, false);
        return month;
    }


    /**
     * 新增合同的操作记录
     *
     * @param contractEntity 合同信息
     * @param bizCodeEnum    操作枚举
     * @param data           数据
     */
    public void addContractOperationLog(ContractSignEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data) {

        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractEntity.getId())
                .setReferBizCode(contractEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setData(data)
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }


    @Async
    public void recordSignOperationLogDetail(OperationActionEnum operationActionEnum, ContractSignEntity signEntity, String opSystem) {
        OperationDetailDTO operationDetailDTO = buildTTOperationDetail(operationActionEnum, signEntity);
        operationDetailDTO.setTriggerSys(opSystem);
        if (operationActionEnum == OperationActionEnum.REVIEW_CONTRACT_REJECT || operationActionEnum == OperationActionEnum.CONFIRM_CONTRACT_REJECT) {
            String operationInfo = "原因：" + signEntity.getRejectReason();
            operationDetailDTO.setOperationInfo(signEntity.getRejectReason());
        }
        try {
            operationLogFacade.recordOperationLogDetail(operationDetailDTO);
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }
    }

    @Async
    public OperationDetailDTO buildTTOperationDetail(OperationActionEnum operationActionEnum, ContractSignEntity signEntity) {

        OperationSourceEnum logLevelEnum = OperationSourceEnum.getByName(operationActionEnum.getLogLevel());

        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(signEntity.getTradeType());
        return new OperationDetailDTO()
                .setBizCode(operationActionEnum.getCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setLogLevel(logLevelEnum.getValue())
                .setSource(logLevelEnum.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setOperationName(operationActionEnum.getAction())
                .setMetaData(null).setData(JSON.toJSONString(signEntity))
                .setCreatedAt(DateTimeUtil.now()).setReferBizId(signEntity.getId())
                .setReferBizCode(signEntity.getProtocolCode())
                .setTargetRecordId(signEntity.getContractId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name())
                .setTtCode(signEntity.getTtCode())
                .setTradeTypeName(contractTradeTypeEnum.getDesc())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
    }


}
