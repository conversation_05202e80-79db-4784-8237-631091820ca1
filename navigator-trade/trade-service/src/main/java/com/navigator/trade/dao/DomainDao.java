package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.DomainMapper;
import com.navigator.trade.pojo.entity.DomainEntity;

/**
 * <AUTHOR>
 * @date 2021/12/20 10:46
 */
@Dao
public class DomainDao extends BaseDaoImpl<DomainMapper, DomainEntity> {

    public DomainEntity getDomainByNameAndCategoryId(String name, Integer categoryId) {
        return this.baseMapper.selectOne(
                Wrappers.<DomainEntity>lambdaQuery()
                        .eq(DomainEntity::getName, name)
                        .eq(DomainEntity::getCategoryId, categoryId)
        );
    }

}
