package com.navigator.trade.app.contract.logic.service.handler.impl;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CloseLogicService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractCloseTypeEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 合同关闭子类处理Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 20240724
 */
@Service
public class CloseLogicServiceImpl implements CloseLogicService {

    @Autowired
    private PriceApplyFacade priceApplyFacade;

    @Autowired
    private SiteFacade siteFacade;

    @Autowired
    private StructureLogicService structureLogicService;
    /**
     * 合同域处理
     */
    @Autowired
    private ContractDomainService contractDomainService;

    /**
     * 校验关闭合同的条件
     *
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeContractCheck(ContractEntity contractEntity) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 校验合同类型
        if (contractEntity.getContractType() == ContractTypeEnum.ZAN_DING_JIA.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_CLOSED);
        }
        if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {
            Boolean flag = structureLogicService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractEntity.getContractNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );
            if (flag) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }
        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 开单量校验
        SiteEntity siteEntity = siteFacade.getSiteByCode(contractEntity.getSiteCode());

        if (SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem())) {

            // 校验：合同可分配的量（open_qty）=合同量-实际已提量-已回购量，确保合同不存在分配中的合同被关闭
            // 例：100 提货申请 30 open_qty 70 != 100-0-0
            BigDecimal buybackNum = contractEntity.getTotalBuyBackNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBuyBackNum();
            if (!BigDecimalUtil.isEqual(contractEntity.getTotalBillNum(), contractEntity.getTotalDeliveryNum().add(buybackNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_DELIVERY_NUM_EXCEPTION);
            }
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }
    }


    /**
     * 关闭合同
     *
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeContract(ContractEntity contractEntity) {
        // 系统不出具补充协议且合同关闭成功
        contractEntity.setStatus(ContractStatusEnum.CLOSING.getValue());
        contractEntity.setContractCloseType(ContractCloseTypeEnum.SIGN_CLOSE.getCode());
        contractDomainService.updateContractById(contractEntity);
        // 关闭申请单
        if (ContractTypeEnum.STRUCTURE.getValue() == contractEntity.getContractType()) {
            priceApplyFacade.closePriceApplyByContractId(contractEntity.getId());
        }
    }

}
