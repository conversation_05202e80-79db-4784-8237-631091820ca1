package com.navigator.trade.app.tt.domain.service.processor;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("PRICE")
public class TTPriceDomainProcessor extends AbstractTTDomainProcessor {

    @Autowired
    TtPriceDao ttPriceDao;


    public TTPriceDomainProcessor() {
        System.out.println("TTPriceDomainProcessor");

    }

    @Override
    void addTTSubEntity(TradeTicketDO tradeTicketDO) {
        //set value
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(tradeTicketDO.getTradeTicketEntity().getId());

        TTPriceEntity priceEntity = (TTPriceEntity) ttSubEntity;

        TTPriceEntity oldPriceEntity = ttPriceDao.getTTPriceEntityByTTId(priceEntity.getTtId());
        if (Objects.nonNull(oldPriceEntity)) {
            priceEntity.setId(oldPriceEntity.getId());
        }
        ttPriceDao.save(priceEntity);
    }

    @Override
    boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {
        int rtn = ttPriceDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        return rtn > 0;
    }
}
