package com.navigator.trade.service.futrue.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.bisiness.enums.AuditStatusEnum;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.vo.PriceApplyVO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.trade.dao.DomainPriceDao;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceAuditDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceLeadDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import com.navigator.trade.pojo.entity.DomainPriceEntity;
import com.navigator.trade.pojo.vo.DomainPriceLeadVO;
import com.navigator.trade.service.futrue.IDomainCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 期货合约表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Service
@Slf4j
public class DomainCodeServiceImpl implements IDomainCodeService {
    @Resource
    private DomainPriceDao domainPriceDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private EmployFacade employFacade;
    @Autowired
    private AzureBlobUtil azureBlobUtil;
    @Autowired
    private PriceApplyFacade priceApplyFacade;

    @Value("${domain.price.minTime}")
    private Integer domainPriceMinTime;
    @Value("${domain.price.maxTime}")
    private Integer domainPriceMaxTime;

    @Override
    public DomainPriceEntity getClosingPrice(Integer categoryId, String name, String categoryCode) {
        return getClosingPrice(categoryId, name, new Date(), categoryCode);
    }

    @Override
    public DomainPriceEntity getClosingPrice(Integer categoryId, String domainCode, Date tradeDay, String categoryCode) {
        DomainPriceEntity domainPriceEntity = domainPriceDao.getDomainPrice(categoryId, domainCode, DateTimeUtil.formatDateString(tradeDay), categoryCode);
        if (null == domainPriceEntity) {
            throw new BusinessException(ResultCodeEnum.CLOSING_PRICE_NOT_EXIST);
        }
        return domainPriceEntity;
    }

    @Override
    public DomainPriceEntity getLastestClosingPrice(Integer categoryId, String domainCode, Date signDate, String categoryCode) {
        log.info("get LastestClosingPrice:[categoryId]:{},[domainCode]:{},[signDate]:{}", categoryId, domainCode, signDate);
        DomainPriceEntity domainPriceEntity = domainPriceDao.getLastestClosingPrice(categoryId, domainCode, signDate, categoryCode);
        if (null == domainPriceEntity) {
            throw new BusinessException(ResultCodeEnum.CLOSING_PRICE_NOT_EXIST);
        }
        return domainPriceEntity;
    }

    @Override
    public List<DomainPriceLeadDTO> previewDomainPrice(MultipartFile uploadFile) {
        List<DomainPriceLeadDTO> domainPriceLeadResultList = new ArrayList<>();
        try {
            List<DomainPriceLeadDTO> domainPriceLeadList = EasyPoiUtils.importExcel(uploadFile, 1, 1, DomainPriceLeadDTO.class);
            if (CollectionUtil.isEmpty(domainPriceLeadList)) {
                return domainPriceLeadList;
            }
            domainPriceLeadResultList = domainPriceLeadList;
            //文件数据重复Key（品类Code+合约号（M2205）+2022-09-15）
//            List<String> categoryDomainKeyList = new ArrayList<>();
//            String tradeDate = DateTimeUtil.formatDateString();
//            for (DomainPriceLeadDTO priceLeadDTO : domainPriceLeadList) {
//                String categoryDomainKey = priceLeadDTO.getCategoryCode() + priceLeadDTO.getDomainCode() + tradeDate;
//                Integer categoryId = GoodsCategoryEnum.getByCode(priceLeadDTO.getCategoryCode()).getValue();
//                //文件是否重复数据
//                if (categoryDomainKeyList.contains(categoryDomainKey)) {
//                    priceLeadDTO.setAbnormalJudge(true)
//                            .setAbnormalDesc(ResultCodeEnum.RECORD_REPEAT.getMsg());
//                    categoryDomainKeyList.add(categoryDomainKey);
//                    domainPriceLeadResultList.add(priceLeadDTO);
//                    continue;
//                }
            //系统是否存在未被驳回的数据
//                DomainPriceEntity domainPriceEntity = domainPriceDao.getDomainPrice(categoryId, priceLeadDTO.getDomainCode(), tradeDate);
//                if (null != domainPriceEntity && AuditStatusEnum.REJECT.getValue() != domainPriceEntity.getStatus()) {
//                    log.info("收盘价系统存在数据" + domainPriceEntity.getCategoryCode() + domainPriceEntity.getDomainCode());
//                    priceLeadDTO.setAbnormalJudge(true)
//                            .setAbnormalDesc("系统已上传该品类合约的收盘价，审核状态：" + AuditStatusEnum.getByValue(domainPriceEntity.getStatus()).getDescription());
//                }
//                priceLeadDTO.setAbnormalJudge(false);
//                categoryDomainKeyList.add(categoryDomainKey);
//                domainPriceLeadResultList.add(priceLeadDTO);
//            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        }
        return domainPriceLeadResultList;
    }

    @Override
    public Result uploadDomainPrice(MultipartFile uploadFile) {
        int nowHour = DateUtil.hour(new Date(), true);
        log.info("系统时间：" + nowHour + "日期" + new Date());
        if (nowHour < domainPriceMinTime || nowHour >= domainPriceMaxTime) {
            return Result.failure(ResultCodeEnum.TIME_LIMIT_DOMAIN_PRICE_UPLOAD, +domainPriceMinTime + "-" + domainPriceMaxTime + "点");
        }
        List<DomainPriceLeadDTO> domainPriceLeadList = this.previewDomainPrice(uploadFile);
        domainPriceLeadList = domainPriceLeadList.stream().filter(it -> {
            return StringUtils.isNotBlank(it.getDomainCode()) && StringUtils.isNotBlank(it.getCategoryCode());
        })
                .collect(Collectors.toList());
        String tradeDate = DateTimeUtil.formatDateString();
//        domainPriceLeadList = domainPriceLeadList.stream().filter(it -> !it.getAbnormalJudge()).collect(Collectors.toList());
        log.info("导入收盘价数据：" + domainPriceLeadList.size() + "条数据，" + FastJsonUtils.getBeanToJson(domainPriceLeadList));
        String path = FileConstant.FILE_UPLOAD + "magellan/" + DateTimeUtil.formatDateString(DateTime.now());
        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.upload(uploadFile, path, "");
        domainPriceLeadList.forEach(priceLeadDTO -> {
            DomainPriceEntity domainPriceInfoEntity = domainPriceDao.getDomainPrice(priceLeadDTO.getCategoryId(), priceLeadDTO.getDomainCode(), tradeDate, priceLeadDTO.getCategoryCode());
            if (null == domainPriceInfoEntity) {
                DomainPriceEntity domainPriceEntity = new DomainPriceEntity()
                        .setCategoryCode(priceLeadDTO.getCategoryCode())
//                        .setCategoryId(categoryId)
                        .setDomainCode(priceLeadDTO.getDomainCode())
                        .setPrice(priceLeadDTO.getPrice())
                        .setTradeDate(priceLeadDTO.getTradeDate())
                        .setStatus(AuditStatusEnum.PASS.getValue())
                        .setSourceFileName(uploadFile.getOriginalFilename())
                        .setSourceFilePath(fileBaseInfoDTO.getAttachUrl())
                        .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                domainPriceDao.save(domainPriceEntity);
            } else {
                domainPriceInfoEntity.setPrice(priceLeadDTO.getPrice())
                        .setSourceFileName(uploadFile.getOriginalFilename())
                        .setSourceFilePath(fileBaseInfoDTO.getAttachUrl())
                        .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()));
                domainPriceDao.updateById(domainPriceInfoEntity);
            }
        });
        return Result.success("收盘价导入成功：" + domainPriceLeadList.size() + "条数据～");
    }

    @Override
    public Result queryDomainPrice(QueryDTO<QueryDomainPriceBO> queryDTO) {

        IPage<DomainPriceEntity> domainPriceEntityPage = domainPriceDao.queryDomainPrice(queryDTO);
        List<DomainPriceLeadDTO> domainPriceLeadList = domainPriceEntityPage.getRecords().stream()
                .map(domainPriceEntity -> {
                    return new DomainPriceLeadDTO()
                            .setId(domainPriceEntity.getId())
                            .setCategoryId(domainPriceEntity.getCategoryId())
                            .setCategoryCode(domainPriceEntity.getCategoryCode())
//                            .setCategoryName(null != categoryDTO ? categoryDTO.getName() : "")
                            .setDomainCode(domainPriceEntity.getDomainCode())
                            .setTradeDate(domainPriceEntity.getTradeDate())
                            .setPrice(domainPriceEntity.getPrice())
                            .setStatus(domainPriceEntity.getStatus())
                            .setUploadTime(domainPriceEntity.getCreatedAt())
                            .setUploadBy(null != domainPriceEntity.getCreatedBy() ? domainPriceEntity.getCreatedBy().toString() : "")
                            .setAuditBy(null != domainPriceEntity.getUpdatedBy() ? domainPriceEntity.getUpdatedBy().toString() : "")
                            .setSourceFileName(domainPriceEntity.getSourceFileName())
                            .setSourceFilePath(domainPriceEntity.getSourceFilePath())
                            .setAuditDate(domainPriceEntity.getAuditDate())
                            .setRejectReason(domainPriceEntity.getRejectReason());
                }).collect(Collectors.toList());
        IdNameConverter.toName(IdNameType.category_id_name, domainPriceLeadList);
        IdNameConverter.toName(IdNameType.user_id_name, domainPriceLeadList);
        return Result.page(domainPriceEntityPage, domainPriceLeadList);
    }

    @Override
    public boolean audit(DomainPriceAuditDTO domainPriceAuditDTO) {
        DomainPriceEntity domainPriceEntity = domainPriceDao.getById(domainPriceAuditDTO.getDomainPriceId());
        if (null == domainPriceEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        domainPriceEntity.setStatus(domainPriceAuditDTO.getAuditStatus())
                .setAuditDate(DateTimeUtil.now())
                .setRejectReason(StringUtils.isBlank(domainPriceAuditDTO.getRejectReason()) ?
                        "" : domainPriceAuditDTO.getRejectReason());
        return domainPriceDao.updateById(domainPriceEntity);
    }

    @Override
    public List<DomainPriceLeadVO> queryDomainPriceToday(DomainPriceTodayDTO domainPriceTodayDTO) {

        PriceDealDetailEntity priceDealDetailEntity = priceApplyFacade.priceDealDealDetailById(domainPriceTodayDTO.getPriceDealDetailId());

        Result result = priceApplyFacade.getpriceApplyDetail(priceDealDetailEntity.getPriceApplyId());

        PriceApplyVO priceApplyVO = (PriceApplyVO) result.getData();

        List<DomainPriceEntity> domainPriceEntities = domainPriceDao.queryDomainPriceToday(domainPriceTodayDTO, priceApplyVO.getDominantCode());

        List<DomainPriceLeadVO> domainPriceLeadVOS = BeanConvertUtils.convert2List(DomainPriceLeadVO.class, domainPriceEntities);


        return domainPriceLeadVOS;
    }
}
