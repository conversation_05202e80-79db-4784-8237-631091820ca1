package com.navigator.trade.app.sign.logic.service;

import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;

/**
 * 合同业务逻辑处理Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface ContractSignLogicService {
    /**
     * 创建协议信息
     *
     * @param signCreateDTO 协议创建参数
     * @return 创建结果
     */
    ContractSignEntity createOrUpdateContractSign(ContractSignCreateDTO signCreateDTO);

    /**
     * 更新协议合同正本
     * @param contractEntity
     * @param customerOriginalPaperEntity
     * @param contractSignEntity
     */
    void updateContractSign(ContractEntity contractEntity, CustomerOriginalPaperEntity customerOriginalPaperEntity, ContractSignEntity contractSignEntity);

    SignHuskyTemplateDTO buildHuskySignTemplateV2(Integer contractSignId);

    FileBaseInfoDTO generateSignPdf(ContractSignProvideDTO signProvideDTO);

    /**
     * 出具协议提交
     *
     * @param contractSignProvideDTO 出具协议修改数据
     * @return 出具结果
     */
    boolean provideContractSign(ContractSignProvideDTO contractSignProvideDTO);

    FileBaseInfoDTO previewHuskyContractPdf(TemplateEntity templateEntity);
    /**
     * 数字合同出具合同信息
     *
     * @param templateEntity
     * @return
     */
    TemplateEntity provideHuskyContractSign(TemplateEntity templateEntity);


    /**
     * 审核协议
     *
     * @param contractSignReviewDTO 协议审核参数
     * @return 审核协议结果
     */
    boolean reviewContractSign(ContractSignReviewDTO contractSignReviewDTO);


    /**
     * 发起签章
     *
     * @param signReviewDTO
     * @return
     */
    boolean contractSignStartSignature(ContractSignReviewDTO signReviewDTO);

    /**
     * 签章发起时错误回调
     *
     * @param contractSignErrorDTO
     * @return
     */
    boolean UpdateContractSignError(ContractSignErrorDTO contractSignErrorDTO);

    /**
     * 协议-异常
     *
     * @param contractSignId 协议ID
     * @return 协议结果
     */
    boolean abnormalContractSign(Integer contractSignId, String remark);


    /**
     * 确认合同合规
     *
     * @param contractBaseDTO
     * @return
     */
    ContractSignEntity confirmContract(ContractBaseDTO contractBaseDTO);

    /**
     * 作废协议
     *
     * @param contractSignReviewDTO 协议作废参数
     * @return 作废协议结果
     */
    boolean invalidContractSign(ContractSignReviewDTO contractSignReviewDTO);


    /**
     * 易企签带签章地址
     *
     * @param contractSignYQQUrlDTO
     * @return
     */
    boolean updateContractSignYQQUrl(ContractSignYQQUrlDTO contractSignYQQUrlDTO);


    boolean updateContractSignYQQCallback(ContractSignYQQCallbackDTO contractSignYQQCallbackDTO);

    /**
     * 保存快递信息
     *
     * @param contractPaperDTO
     * @return
     */
    Integer saveContractPaperSign(ContractPaperDTO contractPaperDTO);


    /**
     * 回传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    boolean postBackContractSign(ContractBaseSignDTO contractBaseSignDTO, Integer status, OperationActionEnum operationActionEnum);

    /**
     * 删除协议
     *
     * @param ttId
     * @return
     */
    boolean deleteContractSign(Integer ttId);

    /**
     * 更新sign中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    int updateContractId(Integer ttId, Integer contractId);

    boolean updateContractSignStatus(Integer signId, Integer status, String memo);

    boolean deleteContractSignById(Integer signId);

    Result sendCustomerNoticeEmail(Integer signId) throws Exception;

    /**
     * 更新协议的仓单信息
     * @param signId
     * @param warrantId
     * @param warrantCode
     * @return
     */
    boolean updateSignWarrantId(Integer signId, Integer warrantId, String warrantCode);
}
