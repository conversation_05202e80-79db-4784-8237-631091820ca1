package com.navigator.trade.service.contract;

import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.entity.ContractVOEntity;

import java.util.List;

/**
 * <p>
 * 合同导出服务
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/4
 */
public interface IContractExportService {
    /**
     * 每日合同导出
     *
     * @return
     */
    List<FileBaseInfoDTO> exportDailyContract(String startDateTime, String endDateTime);


    /**
     * 合同报表导出
     *
     * @param contractBO
     */
    List<ContractVOEntity> exportContractExcel(ContractBO contractBO);

    // 1003312 界面优化-报表中心 changed by <PERSON> at 2025-7-1 start
    /**
     * 合同报表导出（支持多选状态）
     *
     * @param contractBO
     */
    List<ContractVOEntity> exportContractExcelMultiStatus(ContractBO contractBO);
    // 1003312 界面优化-报表中心 changed by <PERSON> at 2025-7-1 end
}
