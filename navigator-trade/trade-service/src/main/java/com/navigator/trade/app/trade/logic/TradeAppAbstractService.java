package com.navigator.trade.app.trade.logic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.koala.pojo.dto.CancelledWarrantDTO;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.service.contract.GroupIdGenerationService;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.app.trade.TradeAppService;
import com.navigator.trade.app.trade.converter.ContactConverter;
import com.navigator.trade.app.trade.converter.ContactSignConverter;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignReviewDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.impl.convert.TTDTOConverter;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:37
 * @Version 1.0
 */
@Slf4j
public abstract class TradeAppAbstractService implements TradeAppService {

    @Autowired
    TTLogicService ttLogicService;

    @Autowired
    TTQueryLogicService ttQueryLogicService;

    @Autowired
    ContractSignLogicService contractSignLogicService;

    @Autowired
    ContractLogicService contractLogicService;

    @Autowired
    ContractQueryLogicService contractQueryLogicService;

    @Autowired
    CommonLogicService commonLogicService;

    @Autowired
    TradeDomainRemoteService tradeDomainRemoteService;

    @Autowired
    protected RedisUtil redisUtil;




    @Override
    public Result<List<TTDTO>> prepareContractCreateData(SubmitTTDTO submitTTDTO) {
        return ttLogicService.prepareContractCreateData(submitTTDTO);
    }

    public List<TTQueryVO> saveTradeTicketData(TTDTO ttdto) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        ArrangeContext arrangeContext = new ArrangeContext();
        //保存数据
        TTSubmitResultDTO ttSubmitResultDTO = ttLogicService.saveTradeTicketDomainData(ttdto, arrangeContext);
        //处理返回值
        ttQueryVOS = convert2TTQueryVOList(ttSubmitResultDTO);
        return ttQueryVOS;
    }

    /**
     * 现货采销合同创建
     *
     * @param submitTTDTO
     * @return
     */
    @Override
    public List<TTQueryVO> contractCreate(SubmitTTDTO submitTTDTO) {
        List<TTQueryVO> listTTQueryVO = new ArrayList<>();
        ArrangeContext arrangeContext = new ArrangeContext();

        List<TTDTO> ttdtoList = TTDTOConverter.convert2TTDTO(submitTTDTO);



        // 1.保存的动作 - 业务编排
        if (submitTTDTO.getCreateTradeTicketDTO().getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            for (TTDTO ttdto : ttdtoList) {
                List<TTQueryVO> ttQueryVOS = contractCreate(ttdto, arrangeContext);
                listTTQueryVO.addAll(ttQueryVOS);
            }
            return listTTQueryVO;
        }
        // 2.提交的动作 - 业务编排
        else {
            for (TTDTO ttdto : ttdtoList) {
                List<TTQueryVO> ttQueryVOS = contractCreate(ttdto, arrangeContext);
                listTTQueryVO.addAll(ttQueryVOS);
            }
        }

        return listTTQueryVO;
    }

    @Override
    public List<TTQueryVO> contractCreate(List<TTDTO> ttdtoList) {
        List<TTQueryVO> listTTQueryVO = new ArrayList<>();
        ArrangeContext arrangeContext = new ArrangeContext();

        for (TTDTO ttdto : ttdtoList) {
            List<TTQueryVO> ttQueryVOS = contractCreate(ttdto);
            listTTQueryVO.addAll(ttQueryVOS);
        }

        return listTTQueryVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> contractCreate(TTDTO ttdto) {
        ArrangeContext arrangeContext = new ArrangeContext();
        return contractCreate(ttdto, arrangeContext);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> contractCreate(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 1.保存的动作 - 业务编排
        if (Arrays.asList(SubmitTypeEnum.SAVE.getValue(), SubmitTypeEnum.COPY_SAVE.getValue()).contains(ttdto.getSubmitType())) {
            // 保存的校验
            ttLogicService.saveTTCheck(ttdto);
            return ttLogicService.saveTT(ttdto, arrangeContext);
        }
        // 2.提交的动作 - 业务编排
        else {
            // 1.创建TT域处理
            List<TTQueryVO> ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
            TTQueryVO ttQueryVO = ttQueryVOS.get(0);
            // 2. 创建协议
            TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
            ContractSignCreateDTO contractSignCreateDTO = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.NEW.getType());
            // 2.1撤回待提交的情况先删除原来的协议，在新增
            if (ObjectUtil.isNotEmpty(tradeTicketDO.getTradeTicketEntity().getSignId()) && tradeTicketDO.getTradeTicketEntity().getSignId() > 0) {
                contractSignLogicService.deleteContractSignById(tradeTicketDO.getTradeTicketEntity().getSignId());
            }
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            ttQueryVO.setSignId(contractSignEntity.getId());
            ttQueryVO.setProtocolCode(contractSignEntity.getProtocolCode());
            // 3. 创建合同
            ContractCreateDTO contractDTO = ContactConverter.createContractDTO(tradeTicketDO.getTradeTicketEntity(), (TTAddEntity) tradeTicketDO.getTtSubEntity(), tradeTicketDO.getContractPriceEntity(), ttdto);



            ContractEntity contractEntity = contractLogicService.createContract(contractDTO);
            ttQueryVO.setContractId(contractEntity.getId());
            ttQueryVO.setContractCode(contractEntity.getContractCode());
            // 4. TT域处理 创建TT和合同关系 以及 合同和价格的关系
            tradeTicketDO.getTradeTicketEntity().setContractId(contractEntity.getId())
                    .setSignId(contractSignEntity.getId()).setProtocolCode(contractSignEntity.getProtocolCode());
            ttLogicService.updateContractId(tradeTicketDO);
            // 5. 创建协议和合同关系
            contractSignLogicService.updateContractId(tradeTicketDO.getTradeTicketEntity().getId(), contractEntity.getId());
            // 6. 如果是TT仓单/采购，生成仓单并且交易类型是线上的那么合同是直接生效【剔除回购-跟销售合同是关联关系】
            if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())
                    && !TTTypeEnum.BUYBACK.getType().equals(ttdto.getTtType())
                    && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())
                    && ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()
            ) {
                WarrantEntity warrantEntity = commonLogicService.createPurchaseWarrant(contractEntity,
                        ((TTAddEntity) tradeTicketDO.getTtSubEntity()).getWarrantCategory(),
                        ((TTAddEntity) tradeTicketDO.getTtSubEntity()).getDeliveryMarginAmount()
                );
                // 更新TT/TT_ADD的仓单信息
                ttLogicService.updateTTAddWarrantId(tradeTicketDO.getTradeTicketEntity().getId(), warrantEntity.getId(), warrantEntity.getCode());
                // 更新协议
                contractSignLogicService.updateSignWarrantId(contractSignEntity.getId(), warrantEntity.getId(), warrantEntity.getCode());
            }
            // 7.如果是回购生成的采购合同并且TT和协议无需审批那么合同修改为生效
            if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())
                    && TTTypeEnum.BUYBACK.getType().equals(ttdto.getTtType())
                    && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())
                    && ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()
            ) {
                // 更新仓单的持有量
                commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_BUYBACK.getValue(),
                        contractEntity.getContractNum(), null);
                // 更新父合同为生效 | 并更新Atlas|Lkg 推送
                contractLogicService.updateContractStatus(contractEntity.getParentId(), tradeTicketDO.getTradeTicketEntity());
            }

            // 8. 合同直接完成的情况，需要推送给Atlas|lkg
            if (ContractStatusEnum.EFFECTIVE.getValue() == contractEntity.getStatus()) {
                // 仓单合同分配|转让线上的直接生效 推送到Atlas 两个服务存在事务的问题 手动提交事务后执行异步方法
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        commonLogicService.syncContractInfo(contractEntity, contractDTO.getCurrentTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                    }
                });
            }
            return ttQueryVOS;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> structureContractCreate(TTDTO ttdto) {
        ArrangeContext arrangeContext = new ArrangeContext();
        // 1.创建TT域处理
        List<TTQueryVO> ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
        TTQueryVO ttQueryVO = ttQueryVOS.get(0);
        // 2. 创建协议
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        ContractSignCreateDTO contractSignCreateDTO = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.STRUCTURE_PRICE.getType());
        ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
        ttQueryVO.setSignId(contractSignEntity.getId());
        ttQueryVO.setProtocolCode(contractSignEntity.getProtocolCode());
        // 3. 创建结构化合同
        ContractEntity contractEntity = contractLogicService.createStructureContract(tradeTicketDO.getTradeTicketEntity(), ttdto);
        ttQueryVO.setContractId(contractEntity.getId());
        ttQueryVO.setContractCode(contractEntity.getContractCode());
        // 4. TT域处理 创建TT和合同关系 以及 合同和价格的关系
        tradeTicketDO.getTradeTicketEntity().setContractId(contractEntity.getId())
                .setSignId(contractSignEntity.getId()).setProtocolCode(contractSignEntity.getProtocolCode());
        ttLogicService.updateContractId(tradeTicketDO);
        // 5. 创建协议和合同关系
        contractSignLogicService.updateContractId(tradeTicketDO.getTradeTicketEntity().getId(), contractEntity.getId());
        return ttQueryVOS;

    }


    /**
     * 现货合同的修改-服务编排-采销共用
     *
     * @param contractModifyDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> spotContractModify(ContractModifyDTO contractModifyDTO) {
        // 创建上下文
        ArrangeContext arrangeContext = new ArrangeContext();

        // 获取修改原合同
        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractModifyDTO.getContractId());

        // 1. 校验合同数据
        contractLogicService.modifyContractCheck(contractEntity, contractModifyDTO);

        // 2. 处理子合同
        ContractEntity sonContractEntity = contractLogicService.modifyContract(contractEntity, contractModifyDTO);

        // 3. 定价完成的处理
        contractLogicService.modifyPriceComplete(contractEntity, contractModifyDTO);

        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 4. 处理TT数据
        //case-1002943: 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(contractEntity.getCategory2())
                .setContractType(contractModifyDTO.getSonContractType())
                .setDepositRate(contractModifyDTO.getDepositRate())
                .setAddedDepositRate(contractModifyDTO.getAddedDepositRate());
        contractModifyDTO.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));
        log.info("合同修改的追加履约保证金：====================：{},品类：{}", contractModifyDTO.getAddedDepositRate2(), contractEntity.getCategory2());
        List<TTDTO> ttdtoList = TTDTOConverter.createReviseTTDTO(sonContractEntity, contractEntity, contractModifyDTO);
        ttdtoList.forEach(ttdto -> {
            List<TTQueryVO> ttQueryVOList = ttLogicService.saveTT(ttdto, arrangeContext);
            ttQueryVOS.addAll(ttQueryVOList);
        });

        try {
            // 普通修改需要根据ttId更新合同信息
            contractModifyDTO.setTtId(arrangeContext.getTradeTicketDO().getTradeTicketEntity().getId());
        } catch (Exception e) {
            log.error("ttId is null, {}", e.getMessage());
        }

        // 5. 修改保存-不处理协议的操作
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            // 一个合同仅支持保存一个变更TT！
            redisUtil.set(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode(), "1");
            return ttQueryVOS;
        }

        // 6.协议处理
        List<ContractSignCreateDTO> signCreateDTOList = ContactSignConverter.createReviseContractSignDTO(ttdtoList);
        signCreateDTOList.forEach(contractSignCreateDTO -> {
            // 创建或更新协议
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            // 更新TT的协议信息
            ttLogicService.updateSignInfo(contractSignEntity.getTtId(), contractSignEntity);
            // 定价完成更新协议状态
            if (contractModifyDTO.getReviseType() == 2) {
                contractSignLogicService.deleteContractSignById(contractSignEntity.getId());
            }
        });

        // 7. 更新父合同
        contractLogicService.modifyFatherContract(contractEntity, contractModifyDTO);

        // 8. 定价完成同步接口
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (contractModifyDTO.getReviseType() == 2) {
                    contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());

                    // 基差变一口价
                    if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                        // 定价单更新
                        commonLogicService.syncContractPriceUpdateInfo(contractModifyDTO.getTtId(), contractEntity);
                    }

                    // ATLAS 调用定价更新接口 (基差 -> 一口价、基差暂定价 -> 基差暂定价)
                    if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                            || contractModifyDTO.getSonContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                        commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                    }

                    // 原合同更新
                    commonLogicService.syncContractInfo(contractEntity, contractModifyDTO.getTtId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.LKG);
                }
            }
        });

        // 9. 合同前后信息变更记录
        commonLogicService.addContractOperationLog(contractEntity, ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()
                        ? LogBizCodeEnum.REVISE_SALES_CONTRACT
                        : LogBizCodeEnum.REVISE_PURCHASE_CONTRACT,
                JSONUtil.toJsonStr(contractModifyDTO), SystemEnum.MAGELLAN.getValue());
        return ttQueryVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> spotContractSplit(ContractModifyDTO contractModifyDTO) {
        // 创建上下文对象，用于流程中信息的传递和共享
        ArrangeContext arrangeContext = new ArrangeContext();

        // 获取待拆分的原始合同
        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractModifyDTO.getContractId());

        ContractEntity originalContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, originalContractEntity);

        // 1. 校验合同数据
        ttLogicService.splitTTPriceCheck(contractEntity, contractModifyDTO);
        contractLogicService.splitContractCheck(contractEntity, contractModifyDTO);

        // 2. 处理子合同数据，创建新的子合同
        ContractEntity sonContractEntity = contractLogicService.splitContract(contractEntity, contractModifyDTO);

        // 3. 处理拆分后的定价单数据
        ttLogicService.splitTTPrice(sonContractEntity, contractModifyDTO, arrangeContext);

        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 4. 处理TT数据
        List<TTDTO> ttdtoList = TTDTOConverter.createSplitTTDTO(sonContractEntity, contractEntity, contractModifyDTO);
        ttdtoList.forEach(ttdto -> {
            List<TTQueryVO> ttQueryVOList = ttLogicService.saveTT(ttdto, arrangeContext);
            ttQueryVOS.addAll(ttQueryVOList);
        });

        // 5. 拆分保存-不处理协议的操作
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            redisUtil.set(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode(), "1");
            return ttQueryVOS;
        }

        // 6. 处理协议数据
        List<ContractSignCreateDTO> signCreateDTOList = ContactSignConverter.createSplitContractSignDTO(sonContractEntity, contractEntity, ttdtoList);
        signCreateDTOList.forEach(contractSignCreateDTO -> {
            // 创建或更新协议
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            // 更新TT的协议信息
            ttLogicService.updateSignInfo(contractSignEntity.getTtId(), contractSignEntity);
        });

        // 7. 处理父合同数据
        List<TTPriceEntity> ttPriceEntityList = ttQueryLogicService.fetchTTPriceEntities(new TTPriceQO().setContractId(contractEntity.getId()));
        contractLogicService.splitFatherContract(contractEntity, contractModifyDTO, ttPriceEntityList);

        // 8. 记录合同前后信息变更，用于审计和追踪
        List<TTQueryVO> finalTtQueryVOS = ttQueryVOS.stream().filter(i -> i.getSourceFlag() == 1 && i.getTtId() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(finalTtQueryVOS)) {
            Integer ttId = finalTtQueryVOS.get(0).getTtId();
            ttLogicService.updateModifyContent(originalContractEntity, contractEntity, ttId, TTTypeEnum.SPLIT.getType());
        }

        // 9. 记录日志
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        commonLogicService.addContractOperationLog(contractEntity,
                isSales ? LogBizCodeEnum.SPLIT_SALES_CONTRACT : LogBizCodeEnum.SPLIT_PURCHASE_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                SystemEnum.MAGELLAN.getValue());
        return ttQueryVOS;
    }

    @Override
    public List<TTQueryVO> contractBuyBack(ContractBuyBackDTO contractBuyBackDTO) {
        // 创建上下文
        ArrangeContext arrangeContext = new ArrangeContext();
        List<TTQueryVO> ttQueryVOS = null;
        // 获取回购合同 销售合同
        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractBuyBackDTO.getContractId());
        // 1.校验合同数据
        contractLogicService.buyBackContractCheck(contractEntity, contractBuyBackDTO);
        // 2.处理TT 双TT，原销售合同TT隐藏【TT处理】
        TTDTO ttdto = new TTDTO();
        ttdto.setTtType(TTTypeEnum.BUYBACK.getType());
        ttdto.setContractTradeType(ContractTradeTypeEnum.BUYBACK.getValue());
        ttdto.setContractEntity(contractEntity);
        // 新录入的状态
        ttdto.setSubmitType(SubmitTypeEnum.SAVE.getValue());
        ttdto.setContractBuyBackDTO(contractBuyBackDTO);
        ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);

        // 页面展示
        TTQueryVO ttQueryVO = new TTQueryVO()
                .setSourceFlag(1)
                .setContractCode(contractEntity.getContractCode());
        ttQueryVOS.add(ttQueryVO);

        // 3.更新父合同 修改中
        contractLogicService.buyBackContract(contractEntity, contractBuyBackDTO);
        return ttQueryVOS;
    }

    @Override
    public List<TTQueryVO> spotContractWashOut(ContractWashOutDTO contractWashOutDTO) {
        // 创建上下文
        ArrangeContext arrangeContext = new ArrangeContext();
        // 获取合同信息
        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractWashOutDTO.getContractId());
        ContractEntity originalContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, originalContractEntity);
        // 1.校验合同数据
        contractLogicService.washOutContractCheck(contractEntity, contractWashOutDTO);

        ContractPriceEntity contractPriceEntity = contractQueryLogicService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        // 2.处理TT
        List<TTQueryVO> ttQueryVOS = null;
        TTDTO ttdto = new TTDTO();
        ttdto.setBuCode(BuCodeEnum.ST.getValue());
        ttdto.setTtType(TTTypeEnum.WASHOUT.getType());
        ttdto.setContractTradeType(ContractTradeTypeEnum.WASHOUT.getValue());
        ttdto.setContractEntity(contractEntity);
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setContractWashOutDTO(contractWashOutDTO);
        ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        TTQueryVO ttQueryVO = ttQueryVOS.get(0);
        // 3.创建协议
        ContractSignCreateDTO contractSignCreateDTO = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WASHOUT.getType());
        ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
        tradeTicketDO.getTradeTicketEntity().setSignId(contractSignEntity.getId()).setProtocolCode(contractSignEntity.getProtocolCode());
        ttLogicService.updateContractId(tradeTicketDO);
        // 4. 更新父合同/记录日志
        contractLogicService.washOutContract(contractEntity, contractWashOutDTO);
        tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.SALES_CONTRACT_WASH_OUT,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
        // 5.合同前后信息变更记录
        ttLogicService.updateModifyContent(originalContractEntity, contractEntity, contractSignEntity.getTtId(), TTTypeEnum.WASHOUT.getType());
        return ttQueryVOS;

    }

    @Override
    public List<TTQueryVO> spotContractClose(Integer contractId) {
        List<TTQueryVO> ttQueryVOS = null;
        // 创建上下文
        ArrangeContext arrangeContext = new ArrangeContext();
        ContractEntity contractEntity = contractQueryLogicService.getContractById(contractId);
        ContractEntity originalContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, originalContractEntity);
        // 1.校验合同数据
        contractLogicService.closeContractCheck(contractEntity);
        ContractSignEntity contractSignEntity = new ContractSignEntity();
        // 2.处理TT
        if (ContractTypeEnum.STRUCTURE.getValue() == contractEntity.getContractType()) {
            contractLogicService.closeContractStructureTT(contractEntity);
        } else {
            TTDTO ttdto = new TTDTO();
            ttdto.setBuCode(BuCodeEnum.ST.getValue());
            ttdto.setTtType(TTTypeEnum.CLOSED.getType());
            ttdto.setContractTradeType(ContractTradeTypeEnum.CLOSED.getValue());
            ttdto.setContractEntity(contractEntity);
            ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
            TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
            TTQueryVO ttQueryVO = ttQueryVOS.get(0);
            // 3.创建协议
            ContractSignCreateDTO contractSignCreateDTO = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.CLOSED.getType());
            contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            tradeTicketDO.getTradeTicketEntity().setSignId(contractSignEntity.getId()).setProtocolCode(contractSignEntity.getProtocolCode());
            ttLogicService.updateContractId(tradeTicketDO);
            ttQueryVO.setSignId(contractSignEntity.getId());
            ttQueryVO.setProtocolCode(contractSignEntity.getProtocolCode());
        }
        // 4.关闭合同
        contractLogicService.closeContract(contractEntity);
        // 5.合同前后信息变更记录
        if (ObjectUtil.isNotEmpty(contractSignEntity)) {
            ttLogicService.updateModifyContent(originalContractEntity, contractEntity, contractSignEntity.getTtId(), TTTypeEnum.CLOSED.getType());
        }
        return ttQueryVOS;
    }

    /**
     * 仓单转让|分配生成仓单销售合同（交割所/线下交易所仓单）
     * 转让：如果线下交易所仓单（需要出具协议和确认合同生效的过程）
     *
     * @param ttdto
     * @return
     */
    @Override
    public abstract List<TTQueryVO> createWarrantSalesContract(TTDTO ttdto);

    /**
     * 仓单合同注销
     *
     * @param contractWriteOffDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> warrantContractWriteOff(ContractWriteOffDTO contractWriteOffDTO) {
        List<TTQueryVO> ttQueryVOS = null;
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        // 1.获取注销合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractWriteOffDTO.getContractId());
        contractWriteOffDTO.setParentContractEntity(contractEntity);
        // 2.校验注销条件【判断注销场景】
        contractLogicService.writeOffContractCheck(contractEntity, contractWriteOffDTO);
        // 处理注销过程 1. 不产生新的注销合同 2. 产生新的销售提货合同 3.产生销售提货合同和仓单采购合同"
        ContractEntity sonContractEntity = new ContractEntity();
        ArrangeContext arrangeContext = new ArrangeContext();
        TTDTO ttdto = new TTDTO();
        ttdto.setBuCode(BuCodeEnum.WT.getValue());
        ttdto.setTtType(TTTypeEnum.WRITE_OFF.getType());
        ttdto.setContractWriteOffDTO(contractWriteOffDTO);
        // 生成groupId
        arrangeContext.setGroupId(UUID.randomUUID().toString().replaceAll("-", ""));

        //创建原合同的TT（不可见-isdelete=1，不需要生成协议）
        ttdto.setContractTradeType(contractWriteOffDTO.getWriteOffAction());
        ttdto.setWriteOffTTAction(TTWriteOffActionEnum.REVISE);
        ttdto.setSalesType(contractEntity.getSalesType());
        ttdto.setContractEntity(contractEntity);
        arrangeContext.setContractCode(contractEntity.getContractCode());
        arrangeContext.setContractId(contractEntity.getId());
        ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
        tradeTicketDO = arrangeContext.getTradeTicketDO();
        // 记录合同的注销日志
        tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.WARRANT_CONTRACT_WRITE_OFF,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());

        // 注销记录需要的对象信息
        CancelledWarrantDTO cancelledWarrantDTO = new CancelledWarrantDTO();
        cancelledWarrantDTO.setChangeWarrantCount(contractWriteOffDTO.getWriteOffNum());
        cancelledWarrantDTO.setWriteOffDate(contractWriteOffDTO.getWriteOffDate());
        cancelledWarrantDTO.setFutureCode(contractWriteOffDTO.getFutureCode());
        cancelledWarrantDTO.setDomainCode(contractWriteOffDTO.getDomainCode());
        cancelledWarrantDTO.setGoodsName(contractWriteOffDTO.getGoodsName());
        cancelledWarrantDTO.setDeliveryPassword(contractWriteOffDTO.getDeliveryPassword());
        cancelledWarrantDTO.setWriteOffAction(contractWriteOffDTO.getWriteOffAction());

        switch (ContractActionEnum.getByType(contractWriteOffDTO.getWriteOffAction())) {
            case WRITE_OFF_A:
                // 主合同进更新并备份
                contractWriteOffDTO.setTtId(tradeTicketDO.getTradeTicketEntity().getId());
                sonContractEntity = contractLogicService.createCargoRights(contractEntity, contractWriteOffDTO);
                //2.同步到仓单合同的进行变更合同的住销量/生成注销记录信息(记录合同日志)
                List<ContractEntity> cargoRights = new ArrayList<>();
                cargoRights.add(sonContractEntity);
                commonLogicService.cancelledWarrantContract(contractEntity, cargoRights, null, null, null, cancelledWarrantDTO);
                break;
            case WRITE_OFF_B:
                //2.创建提货合同
                //2.1 创建提货合同|处理销售合同的日志和变更记录
                sonContractEntity = contractLogicService.createDeliveryContract(contractEntity, contractWriteOffDTO, ContractTradeTypeEnum.WRITE_OFF_B.getValue());
                //2.2 创建提货合同TT  审批
                ttdto.setWriteOffTTAction(TTWriteOffActionEnum.DELIVERY_ADD);
                arrangeContext.setContractCode(sonContractEntity.getContractCode());
                arrangeContext.setContractId(sonContractEntity.getId());
                ttdto.setContractEntity(sonContractEntity);
                ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
                tradeTicketDO = arrangeContext.getTradeTicketDO();
                ttdto.setSalesType(contractEntity.getSalesType());
                TTQueryVO ttQueryVO = ttQueryVOS.get(0);
                ttQueryVO.setContractCode(sonContractEntity.getContractCode());
                ttQueryVO.setContractId(sonContractEntity.getId());
                //2.3 创建提货合同协议 待出具
                ContractSignCreateDTO signCreate2 = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                signCreate2 = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                signCreate2.setContractCode(sonContractEntity.getContractCode());
                signCreate2.setContractId(sonContractEntity.getId());
                ContractSignEntity signEntity = contractSignLogicService.createOrUpdateContractSign(signCreate2);
                // 协议和TT的关系
                tradeTicketDO.getTradeTicketEntity().setSignId(signEntity.getId()).setProtocolCode(signEntity.getProtocolCode());
                ttLogicService.updateContractId(tradeTicketDO);
                //3.同步到仓单合同的进行变更合同的住销量/生成注销记录信息
                List<ContractEntity> deliverys = new ArrayList<>();
                deliverys.add(sonContractEntity);
                commonLogicService.cancelledWarrantContract(contractEntity, null, deliverys, null, null, cancelledWarrantDTO);
                // 4.记录合同创建日志
                tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_CONTRACT,
                        JSONUtil.toJsonStr(sonContractEntity), SystemEnum.MAGELLAN.getValue());
                break;
            case WRITE_OFF_C:

                //2.创建提货合同
                //2.1 创建提货合同 | 处理销售合同的日志和变更记录
                sonContractEntity = contractLogicService.createDeliveryContract(contractEntity, contractWriteOffDTO, ContractTradeTypeEnum.WRITE_OFF_C.getValue());
                //2.2 创建提货合同TT   审批
                ttdto.setWriteOffTTAction(TTWriteOffActionEnum.DELIVERY_ADD);
                ttdto.setSalesType(ContractSalesTypeEnum.SALES.getValue());
                ttdto.setContractEntity(sonContractEntity);
                arrangeContext.setContractCode(sonContractEntity.getContractCode());
                arrangeContext.setContractId(sonContractEntity.getId());
                ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
                tradeTicketDO = arrangeContext.getTradeTicketDO();
                ttQueryVO = ttQueryVOS.get(0);
                ttQueryVO.setContractCode(sonContractEntity.getContractCode());
                ttQueryVO.setContractId(sonContractEntity.getId());
                //2.3 创建提货合同协议  待出具
                ContractSignCreateDTO signCreate3 = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                signCreate3.setContractCode(sonContractEntity.getContractCode());
                signCreate3.setContractId(sonContractEntity.getId());
                ContractSignEntity signEntity1 = contractSignLogicService.createOrUpdateContractSign(signCreate3);
                // 协议和TT的关系
                tradeTicketDO.getTradeTicketEntity().setSignId(signEntity1.getId()).setProtocolCode(signEntity1.getProtocolCode());
                ttLogicService.updateContractId(tradeTicketDO);
                // 2.4.记录合同创建日志
                tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_CONTRACT,
                        JSONUtil.toJsonStr(sonContractEntity), SystemEnum.MAGELLAN.getValue());

                //3.创建采购合同
                // 3.1 创建采购合同
                List<TTQueryVO> ttPurchaseQueryVOS = null;
                ContractEntity purchaseContractEntity = contractLogicService.createPurchaseContract(contractEntity, contractWriteOffDTO);
                // 3.2 创建采购TT
                ttdto.setWriteOffTTAction(TTWriteOffActionEnum.PURCHASE_ADD);
                ttdto.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue());
                ttdto.setContractEntity(purchaseContractEntity);
                arrangeContext.setContractCode(purchaseContractEntity.getContractCode());
                arrangeContext.setContractId(purchaseContractEntity.getId());
                ttPurchaseQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
                tradeTicketDO = arrangeContext.getTradeTicketDO();
                TTQueryVO ttPurchaseQueryVO = ttPurchaseQueryVOS.get(0);
                ttPurchaseQueryVO.setPurchaseContractCode(purchaseContractEntity.getContractCode());
                ttPurchaseQueryVO.setPurchaseContractId(purchaseContractEntity.getId());
                ttQueryVOS.add(ttPurchaseQueryVO);
                // 3.3 创建协议
                ContractSignCreateDTO signCreatePurchase = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                signCreatePurchase.setContractCode(purchaseContractEntity.getContractCode());
                signCreatePurchase.setContractId(purchaseContractEntity.getId());
                ContractSignEntity signEntity2 = contractSignLogicService.createOrUpdateContractSign(signCreatePurchase);
                // 协议和TT的关系
                tradeTicketDO.getTradeTicketEntity().setSignId(signEntity2.getId()).setProtocolCode(signEntity2.getProtocolCode());
                ttLogicService.updateContractId(tradeTicketDO);
                // 3.4.记录合同创建日志
                tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_CONTRACT,
                        JSONUtil.toJsonStr(sonContractEntity), SystemEnum.MAGELLAN.getValue());

                // 4.同步到仓单合同的进行变更合同的住销量/生成注销记录信息/同时触发仓单的注销量和采购量信息
                List<ContractEntity> deliveryContract = new ArrayList<>();
                deliveryContract.add(sonContractEntity);
                commonLogicService.cancelledWarrantContract(contractEntity, null, deliveryContract, null, purchaseContractEntity, cancelledWarrantDTO);
                break;
            default:
                break;
        }
        return ttQueryVOS;
    }

    /**
     * 注销撤回
     *
     * @param contractWriteOffWithDrawDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> warrantContractWriteOffWithdraw(ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        List<TTQueryVO> ttQueryVOS = null;
        // 1.获取注销合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractWriteOffWithDrawDTO.getContractId());
        // 2.校验注销撤回条件【判断注销场景】 a) ）撤回时需校验撤回注销数量该货品<= 可提货申请量 b) 协议：待回签和待确认合规不可撤回
        contractLogicService.writeOffWithDrawCheck(contractEntity, contractWriteOffWithDrawDTO);
        // 3.退回仓单及仓单合同更新的数量信息|以及作废合同|调用Atlas
        contractLogicService.writeOffWithDraw(contractEntity, contractWriteOffWithDrawDTO);
        // 4.过滤掉提货权合同-处理下TT和协议的作废操作
        List<WriteOffContractDTO> writeOffContractDTOList = contractWriteOffWithDrawDTO.getWriteOffContractDTOS()
                .stream().filter(item -> !ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(item.getContractNature())).collect(Collectors.toList());

        writeOffContractDTOList.forEach(item -> {
            ttLogicService.invalidTTByContractId(item.getReferContractId());
            // 新仓单合同【提货】TT协议更新为已作废
            ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
            contractSignReviewDTO.setContractId(item.getReferContractId());
            contractSignLogicService.invalidContractSign(contractSignReviewDTO);
        });

        // 3.4.记录合同创建日志 TODO  TT 这边处理下需要查TT
        ttLogicService.withdrawContractLog(contractWriteOffWithDrawDTO);

        //5. 推送到Atlas 进行跟新或者作废处理 手动提交事务后执行异步方法
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                log.info("===============[{}] Atlas Sync Open : {}===============", contractEntity.getContractCode(), "撤回");
                // 注销二或者豆二注销,合同修改推送 撤回原合同更新有注销B,豆二注销，合同必须都是生效的
                if (ContractStatusEnum.EFFECTIVE.getValue() == contractEntity.getStatus() &&
                        (contractEntity.getIsSoybean2() == 1 || ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffWithDrawDTO.getWriteOffAction())) {
                    commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                }

                //  atlas合同作废推送接口 注销B，注销C，豆二豆油和豆粕提货和贸易,合同必须都是生效的,在合同处理前需要获取
                writeOffContractDTOList.forEach(item -> {
                    ContractEntity closeContract = contractQueryLogicService.getBasicContractById(item.getReferContractId());
                    log.info("===============[{}] 子合同关闭判断 : [{}]===============", closeContract.getStatus(), ContractStatusEnum.EFFECTIVE.getValue() == item.getContractStatus());
                    if (ContractStatusEnum.EFFECTIVE.getValue() == item.getContractStatus()) {
                        commonLogicService.syncContractInfo(closeContract, null, LkgInterfaceActionEnum.CLOSE.getSyncType(), SyncSwitchEnum.ATLAS);
                    }
                });
            }
        });

        return ttQueryVOS;
    }

    /**
     * 仓单合同作废
     *
     * @param contractId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> warrantContractInvalid(Integer contractId) {
        List<TTQueryVO> ttQueryVOS = null;
        ArrangeContext arrangeContext = new ArrangeContext();
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        TTDTO ttdto = new TTDTO();
        ttdto.setBuCode(BuCodeEnum.WT.getValue());
        ttdto.setTtType(TTTypeEnum.INVALID.getType());
        // 获取仓单合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);
        contractLogicService.warrantContractInvalid(contractEntity);

        //2.2 创建TT
        arrangeContext.setContractCode(contractEntity.getContractCode());
        arrangeContext.setContractId(contractEntity.getId());
        ttdto.setContractEntity(contractEntity);

        // 处理数据
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
        salesContractAddTTDTO.setContractId(contractEntity.getId());
        salesContractAddTTDTO.setSourceContractId(contractEntity.getId());

        // 价格处理
        ContractPriceEntity contractPriceEntity = contractQueryLogicService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

        ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
        tradeTicketDO = arrangeContext.getTradeTicketDO();
        ttdto.setSalesType(contractEntity.getSalesType());
        TTQueryVO ttQueryVO = ttQueryVOS.get(0);
        ttQueryVO.setContractCode(contractEntity.getContractCode());
        ttQueryVO.setContractId(contractEntity.getId());
        //2.3 创建提货合同协议 【待出具-线下交易待出具】【其他直接完成】
        ContractSignCreateDTO signCreate = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.INVALID.getType());
        signCreate.setContractCode(contractEntity.getContractCode());
        signCreate.setContractId(contractEntity.getId());
        ContractSignEntity signEntity = contractSignLogicService.createOrUpdateContractSign(signCreate);
        // 协议和TT的关系
        tradeTicketDO.getTradeTicketEntity().setSignId(signEntity.getId()).setProtocolCode(signEntity.getProtocolCode());
        ttLogicService.updateContractId(tradeTicketDO);

        // 2.4.记录合同创建日志
        tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.WARRANT_CONTRACT_INVALID,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());

        // 3. 推送 atlas的作废接口
        // 执行作废
        if (!WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    log.info("===============[{}] Atlas Sync Open : {}===============", contractEntity.getContractCode(), "作废");
                    // atlas合同作废推送接口
                    commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.CLOSE.getSyncType(), SyncSwitchEnum.ATLAS);
                    // 存在定价的话进行取消定价
                    List<TTPriceEntity> priceList = ttLogicService.withdrawPrice(contractEntity.getId());
                    if (ObjectUtil.isNotEmpty(priceList) && priceList.size() > 0) {
                        commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), priceList.get(0).getTtId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
                    }
                }
            });

        }
        return ttQueryVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> warrantContractSoyBean2WriteOff(ContractWriteOffOMDTO contractWriteOffOMDTO) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        // 1.获取注销合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractWriteOffOMDTO.getContractId());
        contractWriteOffOMDTO.setParentContractEntity(contractEntity);
        // 2.校验注销条件【判断注销场景】
        contractLogicService.writeOffContractCheck(contractEntity, contractWriteOffOMDTO);
        ArrangeContext arrangeContext = new ArrangeContext();
        TTDTO ttdto = new TTDTO();
        ttdto.setBuCode(BuCodeEnum.WT.getValue());
        ttdto.setTtType(TTTypeEnum.WRITE_OFF_OM.getType());
        ttdto.setContractWriteOffOMDTO(contractWriteOffOMDTO);
        // 生成groupId
        arrangeContext.setGroupId(UUID.randomUUID().toString().replaceAll("-", ""));

        //创建原合同的TT（不可见-isdelete=1，不需要生成协议）
        ttdto.setWriteOffTTAction(TTWriteOffActionEnum.REVISE);
        ttdto.setContractEntity(contractEntity);
        ttdto.setPriceDetailBO(new PriceDetailBO().setForwardPrice(contractEntity.getUnitPrice()));
        ttdto.setSalesType(contractEntity.getSalesType());
        // TODO 两个枚举数据一样
        ttdto.setContractTradeType(contractWriteOffOMDTO.getWriteOffAction());
        arrangeContext.setContractCode(contractEntity.getContractCode());
        arrangeContext.setContractId(contractEntity.getId());
        ttLogicService.saveTT(ttdto, arrangeContext);
        tradeTicketDO = arrangeContext.getTradeTicketDO();
        contractWriteOffOMDTO.setTtId(tradeTicketDO.getTradeTicketEntity().getId());
        // 记录合同的注销日志
        tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.WARRANT_CONTRACT_WRITE_OFF,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());

        // 注销记录需要的对象信息
        CancelledWarrantDTO cancelledWarrantDTO = new CancelledWarrantDTO();
        cancelledWarrantDTO.setChangeWarrantCount(contractWriteOffOMDTO.getWriteOffNum());
        cancelledWarrantDTO.setWriteOffDate(contractWriteOffOMDTO.getWriteOffDate());
        cancelledWarrantDTO.setGoodsName(contractWriteOffOMDTO.getSbmItem().getGoodsName() + "," + contractWriteOffOMDTO.getSboItem().getGoodsName());
        cancelledWarrantDTO.setDeliveryPassword(contractWriteOffOMDTO.getDeliveryPassword());
        cancelledWarrantDTO.setWriteOffAction(contractWriteOffOMDTO.getWriteOffAction());

        switch (ContractActionEnum.getByType(contractWriteOffOMDTO.getWriteOffAction())) {
            case WRITE_OFF_OM_A:
                // 1. 生成提货合同
                List<ContractEntity> deliveryContracts =
                        contractLogicService.createOMContract(contractEntity, contractWriteOffOMDTO, ContractNatureEnum.WAREHOUSE_DELIVERY.getValue());
                ttdto.setContractTradeType(ContractTradeTypeEnum.WRITE_OFF_OM_A.getValue());
                //2. 处理TT和协议
                for (ContractEntity sonContractEntity : deliveryContracts) {
                    //2.2 创建提货合同TT   直接完成
                    ttdto.setWriteOffTTAction(TTWriteOffActionEnum.DELIVERY_ADD);
                    ttdto.setSalesType(contractEntity.getSalesType());
                    ttdto.setContractEntity(sonContractEntity);
                    // TODO 如何判断是豆油还是豆粕需要调整
                    if (GoodsCategoryEnum.OSM_OIL.getValue().equals(sonContractEntity.getCategory2())) {
                        ttdto.setPriceDetailBO(contractWriteOffOMDTO.getSboItem().getPriceDetailDTO());
                    } else {
                        ttdto.setPriceDetailBO(contractWriteOffOMDTO.getSbmItem().getPriceDetailDTO());
                    }
                    arrangeContext.setContractCode(sonContractEntity.getContractCode());
                    arrangeContext.setContractId(sonContractEntity.getId());
                    ttdto.setTtType(TTTypeEnum.WRITE_OFF_OM.getType());
                    List<TTQueryVO> ttSonQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
                    tradeTicketDO = arrangeContext.getTradeTicketDO();
                    TTQueryVO ttQueryVO = ttSonQueryVOS.get(0);
                    ttQueryVO.setContractCode(sonContractEntity.getContractCode());
                    ttQueryVO.setContractId(sonContractEntity.getId());
                    ttQueryVOS.add(ttQueryVO);
                    //2.3 创建提货合同协议  完成
                    ContractSignCreateDTO signCreate3 = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                    signCreate3.setContractCode(sonContractEntity.getContractCode());
                    signCreate3.setContractId(sonContractEntity.getId());
                    signCreate3.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
                    ContractSignEntity signEntity1 = contractSignLogicService.createOrUpdateContractSign(signCreate3);
                    // 协议和TT的关系
                    tradeTicketDO.getTradeTicketEntity().setSignId(signEntity1.getId()).setProtocolCode(signEntity1.getProtocolCode());
                    ttLogicService.updateContractId(tradeTicketDO);
                    // 2.4.记录合同创建日志
                    tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_CONTRACT,
                            JSONUtil.toJsonStr(sonContractEntity), SystemEnum.MAGELLAN.getValue());
                }
                //3.同步到仓单合同的进行变更合同的住销量/生成注销记录信息(记录合同日志)
                commonLogicService.cancelledWarrantContract(contractEntity, null, deliveryContracts, null, null, cancelledWarrantDTO);

                //4.豆二合同都是直接生效的，直接推送给Atlas 两个服务存在事务的问题 手动提交事务后执行异步方法
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        deliveryContracts.forEach(item -> {
                            commonLogicService.syncContractInfo(item, null, LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.ATLAS);
                        });
                        commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                    }
                });
                break;
            case WRITE_OFF_OM_B:
                // 1. 生成贸易合同|提货权合同过滤下
                List<ContractEntity> contractEntities =
                        contractLogicService.createOMContract(contractEntity, contractWriteOffOMDTO, ContractNatureEnum.WAREHOUSE_TRADE.getValue());
                // 交易合同
                List<ContractEntity> tradeContracts = contractEntities.stream().
                        filter(item -> item.getContractNature() == ContractNatureEnum.WAREHOUSE_TRADE.getValue()).collect(Collectors.toList());
                // 提货权合同
                List<ContractEntity> cargoRights = contractEntities.stream().
                        filter(item -> item.getContractNature() == ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue()).collect(Collectors.toList());
                // 2. 生成TT和协议
                ttdto.setContractTradeType(ContractTradeTypeEnum.WRITE_OFF_OM_B.getValue());
                for (ContractEntity sonContractEntity : tradeContracts) {
                    //2.2 创建提货合同TT   直接完成
                    ttdto.setWriteOffTTAction(TTWriteOffActionEnum.TRADE_ADD);
                    ttdto.setSalesType(contractEntity.getSalesType());
                    ttdto.setContractEntity(sonContractEntity);
                    // TODO 如何判断是豆油还是豆粕需要调整
                    if (GoodsCategoryEnum.OSM_OIL.getValue().equals(sonContractEntity.getCategory2())) {
                        ttdto.setPriceDetailBO(contractWriteOffOMDTO.getSboItem().getPriceDetailDTO());
                    } else {
                        ttdto.setPriceDetailBO(contractWriteOffOMDTO.getSbmItem().getPriceDetailDTO());
                    }
                    arrangeContext.setContractCode(sonContractEntity.getContractCode());
                    arrangeContext.setContractId(sonContractEntity.getId());
                    ttdto.setTtType(TTTypeEnum.WRITE_OFF_OM.getType());
                    List<TTQueryVO> ttSonQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
                    tradeTicketDO = arrangeContext.getTradeTicketDO();
                    TTQueryVO ttQueryVO = ttSonQueryVOS.get(0);
                    ttQueryVO.setContractCode(sonContractEntity.getContractCode());
                    ttQueryVO.setContractId(sonContractEntity.getId());
                    ttQueryVOS.add(ttQueryVO);
                    //2.3  创建提货合同协议  完成
                    ContractSignCreateDTO signCreate = ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.WRITE_OFF.getType());
                    signCreate.setContractCode(sonContractEntity.getContractCode());
                    signCreate.setContractId(sonContractEntity.getId());
                    signCreate.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
                    ContractSignEntity signEntity1 = contractSignLogicService.createOrUpdateContractSign(signCreate);
                    // 协议和TT的关系
                    tradeTicketDO.getTradeTicketEntity().setSignId(signEntity1.getId()).setProtocolCode(signEntity1.getProtocolCode());
                    ttLogicService.updateContractId(tradeTicketDO);
                    // 2.4.记录合同创建日志
                    tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_CONTRACT,
                            JSONUtil.toJsonStr(sonContractEntity), SystemEnum.MAGELLAN.getValue());
                }
                //3.同步到仓单合同的进行变更合同的住销量/生成注销记录信息
                commonLogicService.cancelledWarrantContract(contractEntity, cargoRights, null, tradeContracts, null, cancelledWarrantDTO);
                //4.豆二合同都是直接生效的，直接推送给Atlas 两个服务存在事务的问题 手动提交事务后执行异步方法
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        tradeContracts.forEach(item -> {
                            commonLogicService.syncContractInfo(item, null, LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.ATLAS);
                        });
                        commonLogicService.syncContractInfo(contractEntity, null, LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                    }
                });
                break;
            default:
                break;
        }
        return ttQueryVOS;
    }


    @Override
    public void approveTT(ApprovalDTO approvalDTO, Integer ttType) {
        ttLogicService.approveTT(approvalDTO, ttType);
        // todo 审批业务编排
        // 1.发起审批拿到审批结果
        // 2.更新TT状态
        // 3.更新合同状态
        // 4.更新TT状态
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> contractPrice(SalesContractTTPriceDTO salesContractTTPriceDTO) {
        // 创建上下文对象，用于流程中信息的传递和共享
        ArrangeContext arrangeContext = new ArrangeContext();

        // 获取点价的原始合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(salesContractTTPriceDTO.getContractId());

        // 1. 校验
        BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, contractEntity.getTotalPriceNum(), salesContractTTPriceDTO.getAllocateNum());
        if (contractEntity.getContractNum().compareTo(newTotalPriceNum) < 0) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_INSUFFICIENT);
        }
        contractEntity.setTotalPriceNum(newTotalPriceNum);

        // 2. 处理TT数据
        TTDTO priceTTDTO = TTDTOConverter.createPriceTTDTO(contractEntity, salesContractTTPriceDTO);
        priceTTDTO.setContractEntity(contractEntity);
        List<TTQueryVO> ttQueryVOS = ttLogicService.saveTT(priceTTDTO, arrangeContext);

        // 3. 处理协议数据
        Integer ttId = ttQueryVOS.get(0).getTtId();
        ContractSignCreateDTO priceContractSignDTO = ContactSignConverter.createPriceContractSignDTO(ttId, contractEntity, priceTTDTO);

        // 创建或更新协议
        ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(priceContractSignDTO);
        // 更新TT的协议信息
        ttLogicService.updateSignInfo(contractSignEntity.getTtId(), contractSignEntity);


        // 4. 更新父合同信息
        List<TTPriceEntity> ttPriceEntityList = ttQueryLogicService.fetchTTPriceEntities(new TTPriceQO().setContractId(contractEntity.getId()));
        contractLogicService.updatePriceFactorContract(newTotalPriceNum, contractEntity, ttPriceEntityList);

        // 5. 记录操作日志
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.CONTRACT_PRICE,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
        // 6. 同步定价单
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), ttId, LkgInterfaceActionEnum.PRICE.getSyncType());
            }
        });

        return ttQueryVOS;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity contractTransferMonth(ContractTransferDTO contractTransferDTO) {
        // 创建上下文对象，用于流程中信息的传递和共享
        ArrangeContext arrangeContext = new ArrangeContext();

        Integer contractId = contractTransferDTO.getContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        // 1. 校验合同是否满足变更条件
        contractLogicService.transferMonthCheck(contractEntity, contractTransferDTO);

        // 2. 部分转月生成子合同
        ContractEntity childContractEntity = null;
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            childContractEntity = contractLogicService.createTransferContract(contractEntity, contractTransferDTO, arrangeContext);
            log.info("部分转月合同价格信息========================================{},子合同信息{}", FastJsonUtils.getBeanToJson(arrangeContext.getPriceDetailBO()), FastJsonUtils.getBeanToJson(childContractEntity));
        }

        // 处理全部转月的价格问题
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())) {
            contractLogicService.processTransferMonthPrice(contractEntity, contractTransferDTO, arrangeContext);
        }

        // 3. 处理TT
        List<TTDTO> ttdtoList = TTDTOConverter.createTransferMonthTTDTO(contractEntity, childContractEntity, contractTransferDTO, arrangeContext);
        TradeTicketEntity childTicket = new TradeTicketEntity();
        TradeTicketEntity parentTicket = new TradeTicketEntity();
        for (TTDTO ttdto : ttdtoList) {
            ttLogicService.saveTT(ttdto, arrangeContext);
            TradeTicketEntity ticketEntity = arrangeContext.getTradeTicketDO().getTradeTicketEntity();
            if (contractEntity.getId().equals(ticketEntity.getContractId())) {
                parentTicket = ticketEntity;
            }
            if (ObjectUtil.isNotEmpty(childContractEntity) && childContractEntity.getId().equals(ticketEntity.getContractId())) {
                childTicket = ticketEntity;
            }
        }

        // 4. 处理协议数据
        List<ContractSignCreateDTO> signCreateDTOList = ContactSignConverter.createTransferContractSignDTO(contractEntity, ttdtoList);
        signCreateDTOList.forEach(contractSignCreateDTO -> {
            // 创建或更新协议
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            // 更新TT的协议信息
            ttLogicService.updateSignInfo(contractSignEntity.getTtId(), contractSignEntity);
        });

        // 5. 更新父合同
        List<TTPriceEntity> ttPriceEntityList = ttQueryLogicService.fetchTTPriceEntities(new TTPriceQO().setContractId(contractEntity.getId()));
        contractLogicService.updateTransferFatherContract(contractEntity, contractTransferDTO, ttPriceEntityList);

        // 6。记录合同日志 全部双月单日志，部分转月双日志【合同生效-审批记录日志】
        tradeDomainRemoteService.addContractOperationLog(parentTicket, LogBizCodeEnum.CONTRACT_MONTH,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            tradeDomainRemoteService.addContractOperationLog(childTicket, LogBizCodeEnum.CONTRACT_MONTH,
                    JSONUtil.toJsonStr(childContractEntity), SystemEnum.MAGELLAN.getValue());
        }

        // 6.同步 atlas|lkg 交易所和交易平台直接同步
        ContractEntity finalChildContractEntity = childContractEntity;
        TradeTicketEntity finalChildTicket = childTicket;
        TradeTicketEntity finalParentTicket = parentTicket;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode()) && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())) {
                    ContractEntity parentContract = contractQueryLogicService.getBasicContractById(contractId);
                    if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
                        // 部分转月 - 拆分
                        // BUGFIX：仓单转月接口无法同步 Author: Mr 2025-04-01 start
                        // commonLogicService.syncContractInfo(finalChildContractEntity, finalChildTicket.getId(), null, SyncSwitchEnum.BOTH);
                        commonLogicService.syncContractInfo(finalChildContractEntity, finalChildTicket.getId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                    } else {
                        // 全部转月 - 修改
                        // commonLogicService.syncContractInfo(parentContract, finalParentTicket.getId(), null, SyncSwitchEnum.BOTH);
                        commonLogicService.syncContractInfo(parentContract, finalParentTicket.getId(), LkgInterfaceActionEnum.UPDATE.getSyncType(), SyncSwitchEnum.ATLAS);
                        // BUGFIX：仓单转月接口无法同步 Author: Mr 2025-04-01 end
                    }
                }
            }
        });
        return childContractEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity spotContractReversePrice(ContractTransferDTO contractTransferDTO) {
        // 创建上下文对象，用于流程中信息的传递和共享
        ArrangeContext arrangeContext = new ArrangeContext();

        Integer contractId = contractTransferDTO.getContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);

        // 1. 校验合同是否满足变更条件
        contractLogicService.reversePriceCheck(contractEntity, contractTransferDTO);

        // 2. 生成子合同
        ContractEntity childContractEntity = contractLogicService.createTransferContract(contractEntity, contractTransferDTO, arrangeContext);

        // 3. 处理TT
        List<TTDTO> ttdtoList = TTDTOConverter.createReversePriceTTDTO(contractEntity, childContractEntity, contractTransferDTO, arrangeContext);
        ttdtoList.forEach(ttdto -> ttLogicService.saveTT(ttdto, arrangeContext));

        // 4. 处理协议数据
        List<ContractSignCreateDTO> signCreateDTOList = ContactSignConverter.createTransferContractSignDTO(contractEntity, ttdtoList);
        signCreateDTOList.forEach(contractSignCreateDTO -> {
            // 创建或更新协议
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            // 更新TT的协议信息
            ttLogicService.updateSignInfo(contractSignEntity.getTtId(), contractSignEntity);
        });

        // 5. 更新父合同
        contractLogicService.updateTransferFatherContract(contractEntity, contractTransferDTO, null);

        // 6.同步 atlas|lkg 交易所和交易平台直接同步
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode()) && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())) {
                    Integer ttId = signCreateDTOList.get(0).getTtId();
                    commonLogicService.syncContractInfo(childContractEntity, ttId, LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.BOTH);
                }
            }
        });


        return childContractEntity;
    }


    private TTQueryVO convert2TTQueryVO(TTSubmitResultDTO ttSubmitResultDTO) {
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setTtId(ttSubmitResultDTO.getTtId());
        ttQueryVO.setCode(ttSubmitResultDTO.getTtCode());
        ttQueryVO.setProtocolCode(ttSubmitResultDTO.getProtocolCode());
        ttQueryVO.setContractCode(ttSubmitResultDTO.getContractCode());
        ttQueryVO.setContractId(ttSubmitResultDTO.getContractId());
        ttQueryVO.setSignId(ttSubmitResultDTO.getSignId());
        return ttQueryVO;
    }

    private List<TTQueryVO> convert2TTQueryVOList(TTSubmitResultDTO ttSubmitResultDTO) {
        List<TTQueryVO> ttQueryVOList = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setTtId(ttSubmitResultDTO.getTtId());
        ttQueryVO.setCode(ttSubmitResultDTO.getTtCode());
        ttQueryVO.setProtocolCode(ttSubmitResultDTO.getProtocolCode());
        ttQueryVO.setContractCode(ttSubmitResultDTO.getContractCode());
        ttQueryVO.setContractId(ttSubmitResultDTO.getContractId());
        ttQueryVO.setSignId(ttSubmitResultDTO.getSignId());

        ttQueryVOList.add(ttQueryVO);
        return ttQueryVOList;
    }
}
