package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.contract.ContractTransferDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;

import java.util.List;

/**
 * 合同转月、反点价业务逻辑处理 逻辑处理
 *
 * <AUTHOR>
 */
public interface TransferLogicService {

    /**
     * 反点价校验
     */
    void reversePriceCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO);

    /**
     * 转月校验
     */
    void transferMonthCheck(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO);

    /**
     * 构建基础信息
     */
    void buildBaseInfo(ContractEntity childContractEntity, ContractEntity contractEntity, ContractTransferDTO contractTransferDTO);

    /**
     * 构建业务信息
     */
    void buildBizInfo(ContractTransferDTO contractTransferDTO, ContractEntity contractEntity, ContractEntity childContractEntity, ArrangeContext arrangeContext);

    /**
     * 创建子合同
     */
    void createChildContract(ContractTransferDTO contractTransferDTO, ContractEntity childContractEntity);

    /**
     * 更新父合同
     */
    void updateTransferFatherContract(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, List<TTPriceEntity> ttPriceEntityList);

    /**
     * 处理全部转月价格
     */
    void processTransferMonthPrice(ContractEntity contractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext);
}

