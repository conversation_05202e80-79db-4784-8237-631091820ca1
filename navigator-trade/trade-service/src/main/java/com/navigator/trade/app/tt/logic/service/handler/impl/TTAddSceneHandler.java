package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.UsageEnum;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 处理现货采购TT新增、现货销售TT新增、仓单采购TT新增
 * @Date 2024/7/15
 * @Version 1.0
 */
@Slf4j
@Component("NEW_HANDLER")
public class TTAddSceneHandler extends AbstractTTSceneHandler {

    /**
     * 新增场景保存
     * 仅提供TT保存&提交能力
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        // 1、初始化
        initDTO(ttdto);
        // 2、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.add2TradeTicketDO(ttdto);

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Set group_id from ArrangeContext if available (for batch TT creation)
        if (arrangeContext.getContractGroupId() != null) {
            TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
            tradeTicketEntity.setGroupId(String.valueOf(arrangeContext.getContractGroupId()));
            log.info("Set TT group_id {} for TT creation", arrangeContext.getContractGroupId());
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

        // TODO 新增仓单TT 回购仓单TT  新增现货TT 回购现货TT 提交校验,如果校验有异常直接抛出
        if (SubmitTypeEnum.SUBMIT.getValue() == ttdto.getSubmitType()) {
            TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
            ttAddEntity.setCompletedStatus(1);
            String checkReult = ttLogicCheckHandler.ttSubmitLogicCheck(tradeTicketDO.getTradeTicketEntity(), ttAddEntity, tradeTicketDO.getContractPriceEntity());
            if (ObjectUtil.isNotEmpty(checkReult) && checkReult.length() > 0) {
                throw new BusinessException(checkReult);
            }
        }
        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        // 3、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        if (Arrays.asList(SubmitTypeEnum.SAVE.getValue(), SubmitTypeEnum.COPY_SAVE.getValue()).contains(ttdto.getSubmitType())) {
            ttQueryVO.setContractCode(tradeTicketEntity.getContractCode()).setCode(tradeTicketEntity.getCode()).setTtId(tradeTicketEntity.getId());
            list.add(ttQueryVO);
            return list;
        }
        // 4、提交审批【TT完成也是要调用-出审批记录】
        Integer ttId = tradeTicketEntity.getId();
        ResultCodeEnum submitResult = ttApproveHandler.submit(ttId, arrangeContext);
        // 5、返回结果
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode()).setCode(tradeTicketEntity.getCode()).setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        if (ResultCodeEnum.OK.equals(submitResult)) {
            return list;
        } else {
            throw new BusinessException(submitResult);
        }
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
        ContractPriceEntity contractPriceEntity = tradeTicketDO.getContractPriceEntity();

        // 2、基础信息convert
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(ttAddEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(ttAddEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }
        //卖家
        ttQueryDetailVO.setSupplierId(String.valueOf(tradeTicketEntity.getSupplierId()));
        //买家
        ttQueryDetailVO.setCustomerId(String.valueOf(tradeTicketEntity.getCustomerId()));
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        // String type = String.format("%s_%s", buCode, salesType);
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }
//        if (null != ttAddEntity.getGoodsPackageId()) {
//            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttAddEntity.getGoodsPackageId()));
//        }
//        if (null != ttAddEntity.getGoodsSpecId()) {
//            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttAddEntity.getGoodsSpecId()));
//        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }

        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());
        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttAddEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttAddEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        // 发货库点
        //查询库点信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());

        //原合同信息
        ttQueryDetailVO.setRootContractId(ttAddEntity.getRootContractId());
        if (null != ttAddEntity.getRootContractId()) {
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttAddEntity.getRootContractId());
            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        ttDetailVO.setDetailType("0");
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);

        // TT详情后续设置
//        queryTTDetailAfter(tradeTicketEntity, ttDetailVO);
        return ttDetailVO;
    }

}
