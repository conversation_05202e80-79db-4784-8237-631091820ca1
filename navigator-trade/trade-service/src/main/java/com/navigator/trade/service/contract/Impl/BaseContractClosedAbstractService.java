package com.navigator.trade.service.contract.Impl;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 合同关闭的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
public abstract class BaseContractClosedAbstractService extends BaseContractAbstractService {

    @Override
    public List<TTQueryVO> applyClosed(Integer contractId) {
        ContractEntity contractEntity = contractValueObjectService.getContractById(contractId);

        ContractEntity originalContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, originalContractEntity);

        // 1.校验合同数据
        checkContractInfo(contractEntity);

        // 2.处理TT
        List<TTQueryVO> ttQueryVOS = operateTradeTicket(contractEntity);

        // 3.关闭合同
        closeContract(contractEntity);

        //合同前后信息变更记录
        updateModifyContent(originalContractEntity, contractEntity, ttQueryVOS, TTTypeEnum.CLOSED.getType());

        //关闭申请单
        if (ContractTypeEnum.STRUCTURE.getValue() == contractEntity.getContractType()) {
            priceApplyFacade.closePriceApplyByContractId(contractEntity.getId());
        }
        return ttQueryVOS;
    }

    private List<TTQueryVO> operateTradeTicket(ContractEntity contractEntity) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 生效中合同
        if (contractEntity.getStatus() == ContractStatusEnum.EFFECTIVE.getValue()) {
            // 提货量
            BigDecimal deliveryNum = contractEntity.getTotalDeliveryNum();

            if (ContractTypeEnum.STRUCTURE.getValue() == contractEntity.getContractType()) {
                closeContractStructureTT(contractEntity);
            } else if (BigDecimalUtil.isGreaterThanZero(deliveryNum)) {
                ttQueryVOS = saveTT(contractEntity, ContractSignTypeEnum.REMAIN_NUM.getValue());
            } else {
                ttQueryVOS = saveTT(contractEntity, ContractSignTypeEnum.ADDED_PROTOCOL.getValue());
            }
            ttQueryVOS.forEach(queryVO -> queryVO.setSourceFlag(1));
        }

        return ttQueryVOS;
    }

    /**
     * 保存Tt
     *
     * @param contractEntity 合同
     * @param signType       协议类型
     */
    private List<TTQueryVO> saveTT(ContractEntity contractEntity, Integer signType) {
        TTDTO ttdto = new TTDTO();
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
        salesContractAddTTDTO.setContractId(contractEntity.getId());
        salesContractAddTTDTO.setSourceContractId(contractEntity.getId());

        // 价格处理
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);

        salesContractAddTTDTO.setSignType(signType)
                .setUnitPrice(String.valueOf(contractEntity.getUnitPrice()))
                .setDepositRate(contractEntity.getDepositRate())
                .setDepositAmount(String.valueOf(contractEntity.getDepositAmount()))
                .setContractNum(String.valueOf(contractEntity.getContractNum()))
                .setDeliveryStartTime(contractEntity.getDeliveryStartTime())
                .setDeliveryEndTime(contractEntity.getDeliveryEndTime())
                .setDomainCode(contractEntity.getDomainCode());
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

        // 获取处理TT接口
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                contractEntity.getSalesType(),
                TTTypeEnum.CLOSED.getType(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(ttProcessorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
        Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);
        return listResult.getData();
    }


    private void closeContract(ContractEntity contractEntity) {

        // V1 合同关闭类型 Author:zengshl 2024-07-8 start
        // 系统不出具补充协议且合同关闭成功
        contractEntity.setStatus(ContractStatusEnum.CLOSING.getValue());
        contractEntity.setContractCloseType(ContractCloseTypeEnum.SIGN_CLOSE.getCode());
        contractValueObjectService.updateContractById(contractEntity);
        // V1 合同关闭类型 Author:zengshl 2024-07-8 end

        // 推送合同关闭申请（包括合同状态和合同信息）至Linkinage，linkinage系统再将合同状态和合同信息同步给Magellan系统并自动更新合同信息数据
    }

    private void closeContractStructureTT(ContractEntity contractEntity) {
        ContractStructureEntity contractStructureEntity = contractStructureService.getContractStructureById(contractEntity.getId());
        tradeTicketQueryService.updateTTStatusById(contractStructureEntity.getTtId(), TTStatusEnum.INVALID);
    }

    private void checkContractInfo(ContractEntity contractEntity) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 判断客户是否可用
        if (!isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 校验合同类型
        if (contractEntity.getContractType() == ContractTypeEnum.ZAN_DING_JIA.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_CLOSED);
        }

        if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {
            Boolean b = contractStructureService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractEntity.getContractNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );

            if (b) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }

        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }
    }
}
