package com.navigator.trade.service.tradeticket.impl.mock;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.trade.dao.TtModifyDao;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.service.impl.TtModifyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TTModifyMockService {

    @Autowired
    private TtModifyDao ttModifyDao;

    public void mockModify(Integer t,String v) {

        TTModifyEntity tm = ttModifyDao.getByTTId(t);
        List<CompareObjectDTO> list = splitV(v);
        tm.setModifyContent(JSON.toJSONString(list));

        ttModifyDao.updateById(tm);
    }


    private List<CompareObjectDTO> splitV(String v) {
        List<CompareObjectDTO> list = new ArrayList<>();
        String[] src = {"contractType", "deliveryStartTime", "deliveryEndTime", "customerCode", "creditDays", "packageWeight", "shipWarehouseId", "destination", "weightTolerance", "unitPrice", "deliveryType", "paymentType", "deliveryFactoryCode", "priceEndTime", "depositRate", "addedDepositRate", "goodsPackageId", "goodsSpecId", "weightCheck", "goodsPackageId", "priceEndType", "depositReleaseType", "usage", "vip", "addedDepositRate2"};

        char[] vc = v.toCharArray();
        for (int i = 0; i < vc.length; i++) {
            String vv = String.valueOf(vc[i]);
            if (!"0".equals(vv)) {
                String field = src[i];
                String before = "";
                String after = "";
                if ("A".equals(vv)) {
                    before = "0";
                    after = "5";
                } else if ("B".equals(vv)) {
                    before = "0";
                    after = "10";
                } else if ("C".equals(vv)) {
                    before = "5";
                    after = "0";
                } else if ("D".equals(vv)) {
                    before = "5";
                    after = "10";
                } else if ("E".equals(vv)) {
                    before = "10";
                    after = "0";
                } else if ("F".equals(vv)) {
                    before = "10";
                    after = "5";
                } else if ("G".equals(vv)) {
                    before = "0";
                    after = "15";
                } else if ("H".equals(vv)) {
                    before = "15";
                    after = "0";
                } else {
                    before = "0";
                    after = "3";
                }
                list.add(mockObject(field, before, after));
            }
        }
        list.add(mockObject("totalAmount", "10000", "15000"));

        return  list;

    }

    private CompareObjectDTO mockObject(String field, String before, String after) {
        CompareObjectDTO co = new CompareObjectDTO();
        String name = field;

        String source = "mock";

        if ("totalAmount".equals(field)) {
            before = "10000";
            after = "15000";
        }

        co.setName(name);
        co.setBefore(before);
        co.setAfter(after);
        co.setSource(source);
        co.setUpdateTime("20230901");

        return co;
    }


}
