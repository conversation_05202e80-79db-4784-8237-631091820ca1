package com.navigator.trade.app.tt.logic.service.handler;

import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.tradeticket.ApprovalDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractPriceEntity;

/**
 * <AUTHOR>
 * @Description TT审批处理器
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
public interface ITTApproveHandler {

    /**
     * @param approvalDTO
     * @param ttType
     * @description: TT审批
     * @return:
     */
    void approveTT(ApprovalDTO approvalDTO, Integer ttType);

    /**
     * TT提交审批
     *
     * @param ttId
     * @param arrangeContext
     * @return
     */
    ResultCodeEnum submit(Integer ttId, ArrangeContext arrangeContext);

    /**
     * 发起TT审批
     *
     * @param ttId
     * @param ttDto
     * @param contractPriceEntity
     * @return
     */
    RecordBizOperationDTO startTTApprove(Integer ttId, TTDTO ttDto, ContractPriceEntity contractPriceEntity);

}
