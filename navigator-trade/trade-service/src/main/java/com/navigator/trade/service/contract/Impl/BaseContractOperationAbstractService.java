package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.*;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.trade.dao.ContractDao;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtAddService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.sync.AtlasSyncService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同基本操作的抽象类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
public abstract class BaseContractOperationAbstractService implements IContractOperationNewService {
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private ContractDao contractDao;
    @Autowired
    private TTHandler ttHandler;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected IContractValueObjectService contractValueObjectService;
    @Autowired
    protected ITtAddService ttAddService;
    @Autowired
    private DeliveryApplyFacade deliveryApplyFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    CustomerFacade customerFacade;
    @Autowired
    private AtlasSyncService atlasSyncService;

    @Override
    public void addContractOperationLog(ContractEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractEntity.getId())
                .setReferBizCode(contractEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setData(data)
                .setTriggerSys(SystemEnum.getByValue(systemEnum).getDescription());

        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                    .setSource(OperationSourceEnum.SYSTEM.getValue())
                    .setOperatorId(1);
        }

        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    @Override
    @Async
    public void addContractOperationLog(OperationDetailDTO operationDetailDTO, Integer systemEnum) {
        operationDetailDTO
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());

        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                    .setSource(OperationSourceEnum.SYSTEM.getValue())
                    .setOperatorId(1);
        }

        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    @Override
    public boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO) {
        // 校验合同
        ContractEntity contractEntity = checkContractInfo(confirmPriceDTO.getContractId());
        // 暂定价合同 → 判断合同类型
        if (contractEntity.getContractType().equals(ContractTypeEnum.ZAN_DING_JIA.getValue())) {
            // 尾量关闭校验
            if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
            }

            if (confirmPriceDTO.getConfirmPrice() == null) {
                throw new BusinessException(ResultCodeEnum.CONFIRMED_PRICE_FAILED);
            } else if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                throw new BusinessException(ResultCodeEnum.CONFIRMED_ALREADY_PRICE);
            }

            // 生成tt和协议
            TTDTO ttdto = new TTDTO();

            SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();

            // 处理变更的数据
            BeanUtils.copyProperties(contractEntity, salesContractTTPriceDTO);

            // 生成定价单
            salesContractTTPriceDTO.setContractId(contractEntity.getId());
            salesContractTTPriceDTO.setSourceContractId(contractEntity.getId());
            salesContractTTPriceDTO.setUserId(String.valueOf(contractEntity.getCreatedBy()));
            salesContractTTPriceDTO.setType(PriceTypeEnum.CONFIRM_PRICED.getValue());
            salesContractTTPriceDTO.setNum(contractEntity.getContractNum());
            salesContractTTPriceDTO.setDiffPrice(contractEntity.getExtraPrice());
            salesContractTTPriceDTO.setTempPrice(contractEntity.getTemporaryPrice());
            salesContractTTPriceDTO.setPrice(confirmPriceDTO.getConfirmPrice());
            salesContractTTPriceDTO.setTransactionPrice(confirmPriceDTO.getConfirmPrice());
            salesContractTTPriceDTO.setSupplierId(contractEntity.getSupplierId());
            salesContractTTPriceDTO.setRemainPriceNum(BigDecimal.ZERO);
            salesContractTTPriceDTO.setTotalPriceNum(contractEntity.getContractNum());
            salesContractTTPriceDTO.setOriginalPriceNum(contractEntity.getContractNum());
            salesContractTTPriceDTO.setPriceEndType(contractEntity.getPriceEndType());
            salesContractTTPriceDTO.setPriceEndTime(contractEntity.getPriceEndTime());
            salesContractTTPriceDTO.setDomainCode(contractEntity.getDomainCode());
            salesContractTTPriceDTO.setOwnerId(contractEntity.getOwnerId());

            ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

            //获取TT处理类
            String processorType = TTHandlerUtil.getTTProcessorByTradeType(contractEntity.getSalesType(),
                    ContractTradeTypeEnum.FIXED.getValue(),
                    contractEntity.getGoodsCategoryId());
            ttdto.setProcessorType(processorType);
            ITradeTicketService tradeTicketService = ttHandler.getStrategy(processorType);

            Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);

            int ttId;
            try {
                ttId = listResult.getData().get(0).getTtId();
            } catch (Exception e) {
                throw new BusinessException(ResultCodeEnum.CONFIRMED_PRICE_FAILED);
            }

            // 更新contractPrice
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            contractPriceService.updatePriceByContractId((ContractPriceEntity) contractPriceEntity.setForwardPrice(confirmPriceDTO.getConfirmPrice()));
            PriceDetailBO priceDetailBO = new PriceDetailBO();
            BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);

            // 计算含税单价
            ContractUnitPriceVO contractUnitPriceVO = contractPriceService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

            // 更新合同
            contractValueObjectService.updateContractById(contractEntity
                    .setTotalPriceNum(contractEntity.getContractNum())
                    .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                    .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                    .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                    .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE,
                            contractEntity.getContractNum(), contractUnitPriceVO.getUnitPrice()))
            );
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> invalidContract(ContractBaseDTO contractBaseDTO) {

        // 校验合同
        ContractEntity contractEntity = contractDao.getById(contractBaseDTO.getContractId());
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // add zengshl 避免重复提交
        String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_INVALID_TT_SAVE + contractEntity.getContractCode());
        if (StringUtils.isNotBlank(saveTimes)) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_SAVE_TT);
        }
        // 处理TT
        List<TTQueryVO> ttQueryVOS = operateTradeTicket(contractEntity);
        // 更新状态
        contractValueObjectService.updateContractById(contractEntity
                .setStatus(ContractStatusEnum.MODIFYING.getValue()));

        // 一个合同仅支持保存一个变更TT！
        redisUtil.set(RedisConstants.CONTRACT_INVALID_TT_SAVE + contractEntity.getContractCode(), "1");
        // 记录操作日志
        addContractOperationLog(contractEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        return ttQueryVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidContractByTT(ContractBaseDTO contractBaseDTO) {

        // 校验合同
        ContractEntity contractEntity = contractDao.getById(contractBaseDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 更新状态
        contractValueObjectService.updateContractById(contractEntity
                .setStatus(ContractStatusEnum.INVALID.getValue())
                .setInvalidReason(contractBaseDTO.getInvalidReason()));

        // 记录操作日志
        addContractOperationLog(contractEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fillContract(ContractBaseDTO contractBaseDTO) {
        log.info("合同补充信息" + FastJsonUtils.getBeanToJson(contractBaseDTO) + CollectionUtils.isEmpty(contractBaseDTO.getTagConfigIdList()));
        // 校验合同
        ContractEntity contractEntity = contractDao.getById(contractBaseDTO.getContractId());
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 记录操作日志
        if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
            addContractOperationLog(contractEntity, LogBizCodeEnum.FILL_SALES_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        } else {
            addContractOperationLog(contractEntity, LogBizCodeEnum.FILL_PURCHASE_CONTRACT, "", SystemEnum.MAGELLAN.getValue());
        }

        // 更新状态
        return contractValueObjectService.updateContractById(contractEntity
                .setCustomerContractCode(contractBaseDTO.getCustomerContractCode())
                .setIsStf(contractBaseDTO.getStf() ? 1 : 0)
                .setTagConfigIds(CollectionUtils.isEmpty(contractBaseDTO.getTagConfigIdList()) ? "" :
                        contractBaseDTO.getTagConfigIdList().stream().map(String::valueOf).collect(Collectors.joining(",")))
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContractByApproved(ContractModifyDTO contractModifyDTO) {
        // TODO 不走此方法
        if (true) {
            return;
        }
    }

    public void afterContractProcess(ContractEntity contractEntity) {
        //子类重载实现
        //结构化定价
    }

//  =====================================私有方法=====================================================

    /**
     * 校验合同
     *
     * @param contractId 合同id
     * @return
     */
    protected ContractEntity checkContractInfo(Integer contractId) {
        ContractEntity contractEntity = contractDao.getById(contractId);
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        return contractEntity;
    }

    /**
     * 变更主体作废生成tt
     *
     * @param contractEntity
     */
    private List<TTQueryVO> operateTradeTicket(ContractEntity contractEntity) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        // 变更主体作废合同需要生成tt
        List<ContractEntity> sonContractList = contractDao.getContractByPid(contractEntity.getId());
        List<ContractEntity> entityList = sonContractList.stream()
                .filter(sonContract -> (sonContract.getContractSource().equals(ContractActionEnum.REVISE_CUSTOMER.getActionValue()) ||
                        sonContract.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())) &&
                        BigDecimalUtil.isEqual(sonContract.getOrderNum(), contractEntity.getOrderNum()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(entityList)) {
            TTDTO ttdto = new TTDTO();

            // 处理数据
            SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
            salesContractAddTTDTO.setContractId(contractEntity.getId());
            salesContractAddTTDTO.setSourceContractId(contractEntity.getId());

            // 价格处理
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);

            ttdto.setPriceDetailBO(priceDetailBO);

            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

            String ttProcessorType = TTHandlerUtil.getTTProcessor(
                    contractEntity.getSalesType(),
                    TTTypeEnum.INVALID.getType(),
                    contractEntity.getGoodsCategoryId());
            ttdto.setProcessorType(ttProcessorType);
            ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
            Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);
            ttQueryVOS = listResult.getData();
            ttQueryVOS.forEach(queryVO -> queryVO.setSourceFlag(1));
        }
        return ttQueryVOS;
    }

}
