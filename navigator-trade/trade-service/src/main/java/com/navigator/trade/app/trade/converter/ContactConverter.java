package com.navigator.trade.app.trade.converter;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * TT转合同参数
 *
 * <AUTHOR>
 * @Date 2024-07-22
 */
@Service
public class ContactConverter {

    /**
     * 转化合同创建数据
     * @param arrangeContext
     * @param ttdto
     * @return
     */
    public static ContractCreateDTO converterContract(ArrangeContext arrangeContext, TTDTO ttdto){
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        return ContactConverter.createContractDTO(tradeTicketDO.getTradeTicketEntity(),
                (TTAddEntity) tradeTicketDO.getTtSubEntity(), tradeTicketDO.getContractPriceEntity(), ttdto);
    }

    /**
     * TT 创建合同进行转化
     *
     * @param tradeTicketEntity
     * @param ttAddEntity
     * @param contractPriceEntity
     * @return
     */
    public static ContractCreateDTO createContractDTO(TradeTicketEntity tradeTicketEntity, TTAddEntity ttAddEntity,
                                                      ContractPriceEntity contractPriceEntity, TTDTO ttdto) {
        ContractCreateDTO contractCreateDTO = new ContractCreateDTO();
        BeanUtils.copyProperties(tradeTicketEntity, contractCreateDTO);
        BeanUtils.copyProperties(ttAddEntity, contractCreateDTO);
        BeanUtils.copyProperties(contractPriceEntity, contractCreateDTO);
        contractCreateDTO.setStatus(null);
        // 计量单位
        contractCreateDTO.setWeightUnit(ttAddEntity.getUnit());
        // 合同类型
        contractCreateDTO.setContractType(tradeTicketEntity.getContractType());
        // 当前ttId
        contractCreateDTO.setCurrentTtId(tradeTicketEntity.getId());
        // TT 编码
        contractCreateDTO.setTtCode(tradeTicketEntity.getCode());
        // TT交易类型
        contractCreateDTO.setTtTradeType(tradeTicketEntity.getTradeType());
        // 合同总数量
        contractCreateDTO.setOrderNum(ttAddEntity.getContractNum());
        // 卖方主体收款账号信息
        contractCreateDTO.setSupplierAccountId(tradeTicketEntity.getBankId());
        // 保证金释放方式
        contractCreateDTO.setDepositReleaseType(ttAddEntity.getDepositUseRule());
        // is_soybean2 默认为0 是否为豆二注销生成（0否;1是）
        contractCreateDTO.setIsSoybean2(ObjectUtil.isNotEmpty(contractCreateDTO.getIsSoybean2())?contractCreateDTO.getIsSoybean2():0);
        // 重新处理下编码规则 仓单编码|现货编码|
        contractCreateDTO.setFutureCode(ttdto.getSalesContractAddTTDTO().getFutureCode());
        contractCreateDTO.setExchangeCode(ttdto.getSalesContractAddTTDTO().getExchangeCode());
        contractCreateDTO.setGoodsCategoryId(ttdto.getSalesContractAddTTDTO().getGoodsCategoryId());

        contractCreateDTO.setActionSource(tradeTicketEntity.getContractSource());
        contractCreateDTO.setPriceEndTime(ttAddEntity.getPriceEndTime());
        contractCreateDTO.setPriceEndType(ttAddEntity.getPriceEndType());

        if (ContractActionEnum.BUYBACK.getActionValue() == tradeTicketEntity.getContractSource()) {
           contractCreateDTO.setParentId(tradeTicketEntity.getSourceContractId());
           contractCreateDTO.setRootId(tradeTicketEntity.getSourceContractId());
        }
        return contractCreateDTO;
    }

}
