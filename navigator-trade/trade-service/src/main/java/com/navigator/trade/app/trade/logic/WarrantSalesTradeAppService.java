package com.navigator.trade.app.trade.logic;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.trade.converter.ContactConverter;
import com.navigator.trade.app.trade.converter.ContactSignConverter;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.SyncSwitchEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;

/**
 * 仓单合同可操作注销、回购、作废、点价、转月、定价完成
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:34
 * @Version 1.0
 */
@Slf4j
@Service("WT_S_CONTRACT")
public class WarrantSalesTradeAppService extends TradeAppAbstractService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> createWarrantSalesContract(TTDTO ttdto) {
        List<TTQueryVO> ttQueryVOS = null;
        ArrangeContext arrangeContext = new ArrangeContext();
        // 保存的动作
        if (ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            List<TTQueryVO> result = ttLogicService.saveTT(ttdto, arrangeContext);
            return result;
        }
        // 提交的动作
        if (ttdto.getSubmitType() == SubmitTypeEnum.SUBMIT.getValue()) {
            // 1. 创建TT
            ttQueryVOS = ttLogicService.saveTT(ttdto, arrangeContext);
            TTQueryVO ttQueryVO = ttQueryVOS.get(0);
            // 转让和分配的时候进行信息校验
            if (ObjectUtil.isNotEmpty(ttQueryVO.getInvalidReason())) {
                return ttQueryVOS;
            }
            // 2. 创建协议
            TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
            ContractSignCreateDTO contractSignCreateDTO =  ContactSignConverter.converterSign(arrangeContext, ttdto, TTTypeEnum.ASSIGN.getType());
            ContractSignEntity contractSignEntity = contractSignLogicService.createOrUpdateContractSign(contractSignCreateDTO);
            // 3. 创建合同
            ContractCreateDTO contractDTO = ContactConverter.createContractDTO(tradeTicketDO.getTradeTicketEntity(),
                    (TTAddEntity) tradeTicketDO.getTtSubEntity(), tradeTicketDO.getContractPriceEntity(), ttdto);
            ContractEntity contractEntity = contractLogicService.createContract(contractDTO);
            ttQueryVO.setContractId(contractEntity.getId());
            ttQueryVO.setContractCode(contractEntity.getContractCode());
            ttQueryVO.setCustomerId(contractEntity.getCustomerId());
            ttQueryVO.setCustomerName(contractEntity.getCustomerName());
            ttQueryVO.setSupplierId(contractEntity.getSupplierId());
            ttQueryVO.setSupplierName(contractEntity.getSupplierName());
            // 4. 创建TT和合同关系
            tradeTicketDO.getTradeTicketEntity().setContractId(contractEntity.getId())
                    .setSignId(contractSignEntity.getId()).setProtocolCode(contractSignEntity.getProtocolCode());
            ttLogicService.updateContractId(tradeTicketDO);
            // 5. 创建协议和合同关系
            contractSignLogicService.updateContractId(tradeTicketDO.getTradeTicketEntity().getId(), contractEntity.getId());
            // 6.记录仓单合同创建日志【分配转让新增销售合同】 TODO 先不记录在创建合同的时候加了
            //tradeDomainRemoteService.addContractOperationLog(tradeTicketDO.getTradeTicketEntity(), LogBizCodeEnum.NEW_WARRANT_SALES_CONTRACT,
            //        JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
            // 7. 仓单分配和转让直接完成的 有些仓单合同是直接生效
            if (ContractStatusEnum.EFFECTIVE.getValue() == contractEntity.getStatus()) {
                commonLogicService.updateWarranStatus(contractEntity.getWarrantCode());
                // 仓单合同分配|转让线上的直接生效 推送到Atlas 两个服务存在事务的问题 手动提交事务后执行异步方法
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization(){
                    @Override
                    public void afterCommit(){
                        commonLogicService.syncContractInfo(contractEntity, contractDTO.getCurrentTtId(), LkgInterfaceActionEnum.ADD.getSyncType(), SyncSwitchEnum.ATLAS);
                    }});
            }

        }
        return ttQueryVOS;
    }

}
