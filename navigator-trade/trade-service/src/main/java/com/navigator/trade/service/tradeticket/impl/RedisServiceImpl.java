package com.navigator.trade.service.tradeticket.impl;

import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.trade.dao.RedisDao;
import com.navigator.trade.mapper.RedisMapper;
import com.navigator.trade.pojo.entity.RedisEntity;
import com.navigator.trade.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private RedisDao redisDao;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RedisMapper redisMapper;

    @Override
    public Result saveRedis(String version) {
        List<String> versionList = redisMapper.getVersionList();
        if (versionList.contains(version)) {
            throw new BusinessException("version is not unique");
        }
        Date date = new Date();
        Set<String> sb = redisUtil.keys("sb*");
        Set<String> sc = redisUtil.keys("contract*");
        Set<String> set = redisUtil.keys("structure*");
        sb.addAll(set);
        sb.addAll(sc);
        for (String s : sb) {
            Object o = redisUtil.get(s);
            RedisEntity redisEntity = new RedisEntity();
            redisEntity.setRedisKey(s)
                    .setRedisValue(String.valueOf(o))
                    .setCreatedTime(date)
                    .setVersion(String.valueOf(version))
            ;
            redisDao.save(redisEntity);
        }
        return Result.success();
    }

    @Override
    public Result reloadRedis(String version) {
        List<RedisEntity> redisEntityList = redisDao.queryByVersion(version);
        for (RedisEntity redisEntity : redisEntityList) {
            if (StringUtils.isNumeric(redisEntity.getRedisValue())) {
                redisUtil.set(redisEntity.getRedisKey(), Long.parseLong(redisEntity.getRedisValue()));
            } else {
                redisUtil.set(redisEntity.getRedisKey(), redisEntity.getRedisValue());
            }
        }
        return Result.success();
    }

    @Override
    public Result rollBackRedis(String key) {
        RedisEntity redisEntity = redisDao.getByRedisKey(key);
        if (null != redisEntity) {
            int value = Integer.parseInt(redisEntity.getRedisValue()) - 1;
            // 回滚redis_value
            redisDao.updateById(redisEntity.setRedisValue(String.valueOf(value)));
        }
        return Result.success();
    }

    @Override
    public Result getByRedisKey(String key) {
        return Result.success(redisDao.getByRedisKey(key));
    }

    @Override
    public Result updateRedis(String key, String value) {
        Integer integer = redisMapper.updateRedis(key, value);
        return Result.success(integer);
    }

    @Override
    public Result getLockValue(String key) {
        return Result.success(redisMapper.getLockValue(key));
    }

    @Override
    public Result insertRedis(String key) {
        return Result.success(redisMapper.insertRedis(key));
    }
}
