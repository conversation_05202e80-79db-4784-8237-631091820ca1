package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * <p>
 * 取消合同变更
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/20
 */
public interface CancelLogicService {

    /**
     * 取消合同变更
     *
     * @param contractModifyDTO
     */
    void cancelContractModify(ContractModifyDTO contractModifyDTO);

    /**
     * 仓单合同取消关闭
     * @param contractCode
     */
    ContractEntity warrantContractClose(String contractCode);
}
