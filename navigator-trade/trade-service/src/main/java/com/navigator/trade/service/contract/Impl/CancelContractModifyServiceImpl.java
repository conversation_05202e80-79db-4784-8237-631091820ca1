package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITtAddService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.ICancelContractModifyService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 取消合同变更
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/20
 */
@Slf4j
@Service
public class CancelContractModifyServiceImpl implements ICancelContractModifyService {
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private IContractQueryService contractQueryService;
    @Autowired
    protected ITtPriceService ttPriceService;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected IContractValueObjectService contractValueObjectService;
    @Autowired
    protected ITtAddService ttAddService;
    @Autowired
    protected IContractPriceService contractPriceService;


    @Override
    public void cancelContractModify(ContractModifyDTO contractModifyDTO) {
        // 获取合同信息
        ContractEntity contractEntity = contractQueryService.getBasicContractById(contractModifyDTO.getContractId());
        // 场景：审批驳回
        switch (ContractActionEnum.getByType(contractModifyDTO.getContractSource())) {
            // BUGFIX：case-1003113 双汇合同已作废，报表中显示未生效，请将合同状态调整为已作废 Author: Mr 2025-04-09 Start
            case NEW:
                // 合同进入已作废
                contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
                // BUGFIX：case-1003113 双汇合同已作废，报表中显示未生效，请将合同状态调整为已作废 Author: Mr 2025-04-09 End
            case REVISE:
                rollBackContractChange(contractModifyDTO);
                break;
            case CLOSED:
            case INVALID:
                // 合同进入生效中：Status
                contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                break;
            case REVISE_CUSTOMER:
                // 父合同进入生效中:Status、ContractNum
                ContractEntity reviseParentContract = contractQueryService.getBasicContractById(contractEntity.getParentId());
                if (null != reviseParentContract && reviseParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    contractValueObjectService.updateContractById(reviseParentContract
                            .setContractNum(reviseParentContract.getContractNum().add(contractEntity.getContractNum()))
                            .setTotalAmount(reviseParentContract.getTotalAmount().add(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), reviseParentContract.getUnitPrice())))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, reviseParentContract.getTotalAmount(), BigDecimal.valueOf(reviseParentContract.getDepositRate() * 0.01)))
                            .setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                }
                // 子合同进入已作废
                contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
            case SPLIT:
            case SPLIT_CUSTOMER:
                ContractEntity splitCustomerContract = contractQueryService.getBasicContractById(contractEntity.getParentId());

                // 主体拆分的子合同审批
                if (null != splitCustomerContract
                        && contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                    // 原合同进入拆分中，子合同进入已作废
                    updateBySplitRejected(splitCustomerContract, contractEntity);
                } else {
                    // 部分拆分的父合同审批
                    if (!contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                        // 子合同进入已作废
                        TradeTicketEntity ticketEntity = tradeTickService.getByTtId(contractModifyDTO.getTtId());
                        if (null != ticketEntity) {
                            TradeTicketEntity sonTicketEntity = tradeTickService.getByGroupId(ticketEntity.getGroupId(), ticketEntity.getId());

                            if (null != sonTicketEntity) {
                                ContractEntity sonContract = contractQueryService.getBasicContractById(sonTicketEntity.getContractId());

                                if (null != sonContract) {
                                    updateBySplitRejected(contractEntity, sonContract);
                                }
                            }
                        }
                    }
                }
                break;
            case WASHOUT:
                // 更改修改中的合同状态
                if (!contractEntity.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    break;
                }

                // 解约定赔导致的原合同全部定价
                if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue()) && BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                    // 回滚合同数据
                    ContractEntity rollBackContract = contractValueObjectService.rollBackContract(contractEntity.getId());
                    contractEntity.setUnitPrice(rollBackContract.getUnitPrice())
                            .setFobUnitPrice(rollBackContract.getFobUnitPrice())
                            .setCifUnitPrice(rollBackContract.getCifUnitPrice())
                            .setExtraPrice(rollBackContract.getExtraPrice());
                }

                // 释放解约定赔数量
                TTAddEntity ttAddEntity = ttAddService.getTTAddEntityByTTId(contractModifyDTO.getTtId());
                if (null != ttAddEntity) {
                    contractEntity
                            .setContractNum(contractEntity.getContractNum().add(ttAddEntity.getContractNum()))
                            .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), contractEntity.getUnitPrice()))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
                }

                // 合同进入生效中：Status
                contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                break;
            case BUYBACK:
                // 父合同进入生效中:Status、TotalBuyBackNum
                ContractEntity buyBackParentContract = contractQueryService.getBasicContractById(contractEntity.getParentId());
                if (null != buyBackParentContract && buyBackParentContract.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())) {
                    contractValueObjectService.updateContractById(buyBackParentContract
                            .setTotalBuyBackNum(buyBackParentContract.getTotalBuyBackNum().subtract(contractEntity.getContractNum()))
                            .setStatus(ContractStatusEnum.EFFECTIVE.getValue()));
                }
                // 子合同进入已作废
                contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));
                break;
            case PRICE_CONFIRM:
            case TRANSFER_CONFIRM:
            case TRANSFER_ALL_CONFIRM:
            case REVERSE_PRICE_CONFIRM:
            case REVERSE_PRICE_ALL_CONFIRM:
                // 暂不处理
                break;
            default:
                break;
        }
    }


    /**
     * 普通拆分的驳回
     *
     * @param parentContract
     * @param contractEntity
     */
    private void updateBySplitRejected(ContractEntity parentContract, ContractEntity contractEntity) {
        // 子合同进入已作废
        contractValueObjectService.updateContractById(contractEntity.setStatus(ContractStatusEnum.INVALID.getValue()));

        BigDecimal totalPriceNum = parentContract.getTotalPriceNum();

        // 基差拆一口价定价单的更新
        List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            Integer sourceId = ttPriceEntity.getSourceId();
            TTPriceEntity priceEntity = ttPriceService.getById(sourceId);
            ttPriceService.updateTtPrice(priceEntity.setNum(priceEntity.getNum().add(ttPriceEntity.getNum())));

            // 父合同增加已定价量
            totalPriceNum = totalPriceNum.add(ttPriceEntity.getNum());
            ttPriceService.updateTtPrice(ttPriceEntity.setNum(BigDecimal.ZERO));
        }

        // 一口价合同拆分已定价量
        if (parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.add(contractEntity.getContractNum());
        }

        // 拆分导致的原合同全部定价
        if (!parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && parentContract.getContractNum().add(contractEntity.getContractNum()).equals(totalPriceNum)) {

            // 原合同需要自动计算加权平均价
            List<TTPriceEntity> ttPriceEntityList = ttPriceService.getConfirmPriceList(parentContract.getId());
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : ttPriceEntityList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                // 更新期货价格
                contractPriceService.updateContractForwardPrice(parentContract, averagePrice);
            }
        }

        // 合同状态控制
        int status = ContractStatusEnum.EFFECTIVE.getValue();

        // 是否存在拆分中的合同
        List<ContractEntity> contractEntityList = contractQueryService.getContractByPid(parentContract.getId());

        List<ContractEntity> sonContractList = contractEntityList.stream()
                .filter(entity ->
                        Arrays.asList(ContractActionEnum.SPLIT.getActionValue(), ContractActionEnum.SPLIT_CUSTOMER.getActionValue()).contains(entity.getContractSource())
                                && ContractStatusEnum.INEFFECTIVE.getValue() == entity.getStatus())
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(sonContractList)) {
            status = ContractStatusEnum.SPLITTING.getValue();
        }

        contractValueObjectService.updateContractById(parentContract
                .setContractNum(parentContract.getContractNum().add(contractEntity.getContractNum()))
                .setTotalModifyNum(parentContract.getTotalModifyNum().subtract(contractEntity.getContractNum()))
                .setTotalPriceNum(totalPriceNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, parentContract.getContractNum(), parentContract.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, parentContract.getTotalAmount(), BigDecimal.valueOf(parentContract.getDepositRate() * 0.01)))
                .setStatus(status));
    }

    /**
     * 回滚合同信息-全量
     *
     * @param contractModifyDTO
     */
    private void rollBackContractChange(ContractModifyDTO contractModifyDTO) {
        contractValueObjectService.rollBackContract(contractModifyDTO.getContractId());
    }

    /**
     * 回滚合同信息-量
     */
    private void giveBackContractChange() {

    }
}
