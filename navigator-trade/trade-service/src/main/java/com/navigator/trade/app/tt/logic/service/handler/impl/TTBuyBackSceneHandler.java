package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("BUYBACK_HANDLER")
public class TTBuyBackSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        ContractEntity contractEntity = ttdto.getContractEntity();
        ContractBuyBackDTO contractBuyBackDTO = ttdto.getContractBuyBackDTO();
        // 处理回购的数据
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);
        // 价格处理
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        if (contractBuyBackDTO.getExtraPrice() != null) {
            priceDetailBO.setExtraPrice(contractBuyBackDTO.getExtraPrice());
        }
        if (contractBuyBackDTO.getForwardPrice() != null) {
            priceDetailBO.setForwardPrice(contractBuyBackDTO.getForwardPrice());
        }
        salesContractAddTTDTO.setContractNum(String.valueOf(contractBuyBackDTO.getBuyBackNum()));
        ttdto.setPriceDetailBO(priceDetailBO);
        // 原合同id
        salesContractAddTTDTO.setSourceContractId(contractEntity.getId());
        salesContractAddTTDTO.setRootContractId(contractEntity.getRootId());
        // 校验状态
        salesContractAddTTDTO.setCompletedStatus(0);
        // 回购,买卖主体对调
        Integer customerId = salesContractAddTTDTO.getCustomerId();
        Integer supplierId = salesContractAddTTDTO.getSupplierId();
        salesContractAddTTDTO.setSupplierId(customerId);
        salesContractAddTTDTO.setCustomerId(supplierId);
        // 基差暂定价合同，回购后类型仅可以为基差
        if (contractBuyBackDTO.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
            salesContractAddTTDTO.setContractType(String.valueOf(ContractTypeEnum.JI_CHA.getValue()));
        }
        // 保证金释放方式
        salesContractAddTTDTO.setDepositUseRule(contractEntity.getDepositReleaseType());
        // 期货合约
        if (StringUtils.isNotBlank(contractBuyBackDTO.getDomainCode())) {
            salesContractAddTTDTO.setDomainCode(contractBuyBackDTO.getDomainCode());
        }
        salesContractAddTTDTO.setDelayPayFine(String.valueOf(contractEntity.getDelayPayFine()));
        // 创建TT的属性,生成唯一id绑定两个TT
        ttdto.setGroupId(UUID.randomUUID().toString().replaceAll("-", ""));
        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);
        String code = salesContractAddTTDTO.getCode();
        salesContractAddTTDTO.setCreateStatus(true);
        //生成TT编号
        code = CodeGeneratorUtil.genPurchaseTTNewCode();
        salesContractAddTTDTO.setCode(code);
        CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
        String contractCode = contractQueryLogicService.genNewContractCode(companyEntity.getShortName(),
                ContractSalesTypeEnum.PURCHASE.getValue(), salesContractAddTTDTO.getGoodsCategoryId());
        salesContractAddTTDTO.setContractCode(contractCode);
        // 期货代码
        salesContractAddTTDTO.setFutureCode(contractBuyBackDTO.getFutureCode() != null ? contractBuyBackDTO.getFutureCode() : contractEntity.getFutureCode());
        // 如果是仓单回购那么需要创建仓单采购的CODE exchangeCode
        if (BuCodeEnum.WT.getValue().equals(salesContractAddTTDTO.getBuCode())) {
            salesContractAddTTDTO.setContractCode(
                    sequenceUtil.generateWarrantPurchaseContractCode(salesContractAddTTDTO.getExchangeCode(), salesContractAddTTDTO.getFutureCode()));
        }
        salesContractAddTTDTO.setType(TTTypeEnum.BUYBACK.getType());
        //初始化交易、销售类型、合同来源
        salesContractAddTTDTO.setTradeType(ContractTradeTypeEnum.BUYBACK.getValue());
        salesContractAddTTDTO.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue());
        salesContractAddTTDTO.setContractSource(ContractActionEnum.BUYBACK.getActionValue());
        salesContractAddTTDTO.setStatus(TTStatusEnum.NEW.getType());
        salesContractAddTTDTO.setSourceContractId(contractEntity.getId());
        //协议签署状态
        salesContractAddTTDTO.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        ttdto.setSubmitType(SubmitTypeEnum.SAVE.getValue());
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
    }


    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.BUYBACK.equals(ttTypeEnum);
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> list = new ArrayList<>();
        // 从合同发起的保存操作
        if (SubmitTypeEnum.SAVE.getValue() == ttdto.getSubmitType()) {
            // 1.生成采购的TT
            list = saveTT(ttdto, arrangeContext);
            // 2.回购生成原合同不可见关联TT
            SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
            salesContractAddTTDTO.setAddedSignatureType(-1);
            salesContractAddTTDTO.setTtId(arrangeContext.getTradeTicketDO().getTradeTicketEntity().getId());
            salesContractAddTTDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
            saveTT(ttdto, arrangeContext);
            return list;
        }
        // 从TT发起的提交操作
        if (SubmitTypeEnum.SUBMIT.getValue() == ttdto.getSubmitType()) {
            list = submitTT(ttdto, arrangeContext);
        }
        return list;
    }

    /**
     * 回购会生成两个TT ,一个原合同不可见的TT，一个新的采购TT处于新录入状态
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    public List<TTQueryVO> saveTT(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        //1.初值化回购TT的
        if (ObjectUtil.isEmpty(ttdto.getSalesContractAddTTDTO())) {
            initDTO(ttdto);
        }
        // 2、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.add2TradeTicketDO(ttdto);
        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        //销售的原合同有的不可见TT不生成价格
        //start Case：1002967：做回购生成的采购合同修改信息保存时提示错误 by nana Date:20250219
        log.info("回购保存TT内容========================================：{}", FastJsonUtils.getBeanToJson(tradeTicketDO));
        //end  Case：1002967：做回购生成的采购合同修改信息保存时提示错误 by nana Date:20250219
        // 3、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        //生成主合同TT+Sub
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        // 4、提交审批
        Integer ttId = tradeTicketEntity.getId();
        // TODO 回购不提交审批，是生成新录入的结果
        //ResultCodeEnum submitResult = ttApproveHandler.submit(ttId, arrangeContext);
        // 5、返回结果
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        return list;
    }

    /**
     * 回购TT提交
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    public List<TTQueryVO> submitTT(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> list = new ArrayList<>();
        TTQueryVO ttQueryVO = new TTQueryVO();
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        // TODO TT提交的校验数据完整性和有效性 待完善
        String userId = JwtUtils.getCurrentUserId();
        salesContractAddTTDTO.setUserId(userId);
        String code = salesContractAddTTDTO.getCode();
        //设置ttId
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(salesContractAddTTDTO.getCode());
        if (tradeTicketEntity == null) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }
        Integer ttId = tradeTicketEntity.getId();
        salesContractAddTTDTO.setTtId(ttId);
        // 2、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.add2TradeTicketDO(ttdto);
        // TODO 新增仓单TT 回购仓单TT  新增现货TT 回购现货TT 提交校验,如果校验有异常直接抛出
        TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
        ttAddEntity.setCompletedStatus(1);
        String checkReult = ttLogicCheckHandler.ttSubmitLogicCheck(tradeTicketDO.getTradeTicketEntity(), ttAddEntity, tradeTicketDO.getContractPriceEntity());
        if (ObjectUtil.isNotEmpty(checkReult) && checkReult.length() > 0) {
            throw new BusinessException(checkReult);
        }
        // 3、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        // tradeTicketDO放到上下文中【最新的】
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        //生成主合同TT+Sub
        tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        // 4、提交审批【比如特殊仓单采购的线上交割直接完成】 先不走
        ResultCodeEnum submitResult = ResultCodeEnum.OK;
        submitResult = ttApproveHandler.submit(ttId, arrangeContext);
        // 5、返回结果
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        list.add(ttQueryVO);
        if (ResultCodeEnum.OK.equals(submitResult)) {
            return list;
        } else {
            throw new BusinessException(submitResult);
        }
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
        ContractPriceEntity contractPriceEntity = tradeTicketDO.getContractPriceEntity();

        // 2、基础信息convert
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(ttAddEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(ttAddEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }
        // 3、买家/卖家信息
        //卖家
        ttQueryDetailVO.setSupplierId(String.valueOf(tradeTicketEntity.getSupplierId()));
        //买家
        ttQueryDetailVO.setCustomerId(String.valueOf(tradeTicketEntity.getCustomerId()));
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        // String type = String.format("%s_%s", buCode, salesType);
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        // 获取仓单类型
        if (ObjectUtil.isNotEmpty(ttAddEntity.getWarrantId()) && ttAddEntity.getWarrantId() != 0) {
            Result result = warrantFacade.queryWarrantByID(ttAddEntity.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            if (ObjectUtil.isNotEmpty(warrantEntity)) {
                ttQueryDetailVO.setWarrantCategory(warrantEntity.getCategory())
                        .setDepositPaymentType(warrantEntity.getDepositPaymentType());
            }
        }
        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }
//        if (null != ttAddEntity.getGoodsPackageId()) {
//            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttAddEntity.getGoodsPackageId()));
//        }
//        if (null != ttAddEntity.getGoodsSpecId()) {
//            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttAddEntity.getGoodsSpecId()));
//        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }

        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());
        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttAddEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttAddEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        // 发货库点
        //查询库点信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());

        //case-1002970: TT详情内原合同编号显示问题
        ttQueryDetailVO.setRootContractId(tradeTicketEntity.getSourceContractId());
        if (null != tradeTicketEntity.getSourceContractId()) {
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getSourceContractId());
            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        ttDetailVO.setDetailType("0");
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);

        // TT详情后续设置
//        queryTTDetailAfter(tradeTicketEntity, ttDetailVO);
        return ttDetailVO;
    }

}
