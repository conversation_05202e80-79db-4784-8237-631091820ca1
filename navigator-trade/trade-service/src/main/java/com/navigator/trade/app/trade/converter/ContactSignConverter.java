package com.navigator.trade.app.trade.converter;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.bisiness.enums.*;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * TT转合同参数
 *
 * <AUTHOR>
 * @Date 2024-07-22
 */
@Service
public class ContactSignConverter {

    /**
     * TT 转协议可创建的数据
     *
     * @param arrangeContext
     * @return
     */
    public static ContractSignCreateDTO converterSign(ArrangeContext arrangeContext, TTDTO ttdto, Integer ttType) {
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        // 创建|回购|转让|分配  TTADD
        if (TTTypeEnum.NEW.getType().equals(ttType) ||
                TTTypeEnum.ASSIGN.getType().equals(ttType) ||
                TTTypeEnum.ALLOCATE.getType().equals(ttType) ||
                TTTypeEnum.BUYBACK.getType().equals(ttType) ||
                TTTypeEnum.CLOSED.getType().equals(ttType) ||
                TTTypeEnum.INVALID.getType().equals(ttType) ||
                TTTypeEnum.WASHOUT.getType().equals(ttType)) {
            contractSignCreateDTO = ContactSignConverter.ttAddSignCreateDTO(tradeTicketDO.getTradeTicketEntity(),
                    (TTAddEntity) tradeTicketDO.getTtSubEntity(), ttdto);
        }
        // 注销 | 修改 | 拆分 TTModify
        if (TTTypeEnum.WRITE_OFF.getType().equals(ttType)) {
            contractSignCreateDTO = ContactSignConverter.ttWriteOffSignCreateDTO(tradeTicketDO.getTradeTicketEntity(),
                    (TTModifyEntity) tradeTicketDO.getTtSubEntity(), ttdto);
        }

        // 结构化定价
        if (TTTypeEnum.STRUCTURE_PRICE.getType().equals(ttType)) {
            contractSignCreateDTO = ContactSignConverter.ttStructSignCreateDTO(tradeTicketDO.getTradeTicketEntity(),
                    (TTStructureEntity) tradeTicketDO.getTtSubEntity(), ttdto);
        }
        return contractSignCreateDTO;
    }


    /**
     * TT转协议创建对象
     * TTTypeEnum ttTypeEnum
     *
     * @param tradeTicket
     * @param ttAddEntity
     * @param ttDto
     * @return
     */
    public static ContractSignCreateDTO ttAddSignCreateDTO(TradeTicketEntity tradeTicket, TTAddEntity ttAddEntity, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractAddTTDTO salesContractAddTTDTO = ttDto.getSalesContractAddTTDTO();
        BeanUtils.copyProperties(tradeTicket, contractSignCreateDTO);
        BeanUtils.copyProperties(ttAddEntity, contractSignCreateDTO);
        contractSignCreateDTO.setCategoryCode(salesContractAddTTDTO.getFutureCode());
        contractSignCreateDTO.setExchangeCode(salesContractAddTTDTO.getExchangeCode());
        // 默认待出具状态
        contractSignCreateDTO.setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        // 仓单| 销售 | 交易类型线上 协议直接创建生成的协议 | 采购 | 销售 | 仓单销售回购- 采购合同
        if (BuCodeEnum.WT.getValue().equals(contractSignCreateDTO.getBuCode()) &&
                !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractSignCreateDTO.getWarrantTradeType())) {
            contractSignCreateDTO.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
        }
        contractSignCreateDTO
                .setTtId(tradeTicket.getId())
                .setTtCode(salesContractAddTTDTO.getCode())
                .setContractSignId(ObjectUtil.isNotEmpty(tradeTicket.getSignId())?tradeTicket.getSignId():null)
                .setSignatureStatus(salesContractAddTTDTO.getContractSignatureStatus())
                .setGoodsCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                .setOwnerId(ObjectUtil.isNotEmpty(salesContractAddTTDTO.getOwnerId()) ? Integer.parseInt(salesContractAddTTDTO.getOwnerId()) : 0)
                // TT类型
                .setTtType(tradeTicket.getType())
                .setSalesType(salesContractAddTTDTO.getSalesType())
                .setSupplierId(ObjectUtil.isNotEmpty(salesContractAddTTDTO.getSupplierId()) ? String.valueOf(salesContractAddTTDTO.getSupplierId()) : "")
                .setBelongCustomerId(tradeTicket.getBelongCustomerId())
                .setSignType(salesContractAddTTDTO.getSignType())
                .setSignDate(salesContractAddTTDTO.getSignDate())
                .setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime())
                .setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime())
                .setContractNum(new BigDecimal(salesContractAddTTDTO.getContractNum()));
        return contractSignCreateDTO;
    }

    /**
     * TT转协议创建对象 TT注销
     * TTTypeEnum ttTypeEnum
     *
     * @param tradeTicket
     * @param ttModifyEntity
     * @param ttDto
     * @return
     */
    public static ContractSignCreateDTO ttWriteOffSignCreateDTO(TradeTicketEntity tradeTicket, TTModifyEntity ttModifyEntity, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        BeanUtils.copyProperties(tradeTicket, contractSignCreateDTO);
        BeanUtils.copyProperties(ttModifyEntity, contractSignCreateDTO);
        contractSignCreateDTO.setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        contractSignCreateDTO
                .setTtId(tradeTicket.getId())
                .setTtCode(tradeTicket.getCode())
                .setOwnerId(ObjectUtil.isNotEmpty(tradeTicket.getOwnerId()) ? tradeTicket.getOwnerId() : 0)
                // TT类型
                .setTtType(tradeTicket.getType())
                .setCustomerId(tradeTicket.getCustomerId())
                .setSupplierId(tradeTicket.getSupplierId().toString())
                .setSalesType(tradeTicket.getSalesType())
                .setBelongCustomerId(tradeTicket.getBelongCustomerId());
        return contractSignCreateDTO;
    }

    /**
     * 结构化定价的协议DTO创建
     * @param tradeTicketEntity
     * @param ttSubEntity
     * @param ttDto
     * @return
     */
    private static ContractSignCreateDTO ttStructSignCreateDTO(TradeTicketEntity tradeTicketEntity, TTStructureEntity ttSubEntity, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttDto.getSalesStructurePriceTTDTO();
        BeanUtils.copyProperties(salesStructurePriceTTDTO, contractSignCreateDTO);
        contractSignCreateDTO.setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        contractSignCreateDTO.setBuCode(BuCodeEnum.ST.getValue());
        contractSignCreateDTO
                .setTtCode(salesStructurePriceTTDTO.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setGoodsCategoryId(tradeTicketEntity.getCategory2())
                .setTtType(TTTypeEnum.STRUCTURE_PRICE.getType())
                .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                .setSupplierId(salesStructurePriceTTDTO.getSupplierId().toString())
                .setContractType(ContractTypeEnum.STRUCTURE.getValue())
                .setCustomerName(tradeTicketEntity.getCustomerName())
                .setSignatureStatus(salesStructurePriceTTDTO.getContractSignatureStatus())
                .setOwnerId(Integer.parseInt(salesStructurePriceTTDTO.getOwnerId()))
                .setTradeType(salesStructurePriceTTDTO.getTradeType())
                .setBelongCustomerId(salesStructurePriceTTDTO.getBelongCustomerId())
                .setSignDate(salesStructurePriceTTDTO.getSignDate());
       return contractSignCreateDTO;
    }



    public static List<ContractSignCreateDTO> createSplitContractSignDTO(ContractEntity sonContractEntity, ContractEntity contractEntity, List<TTDTO> ttdtoList) {
        List<ContractSignCreateDTO> signCreateDTOList = new ArrayList<>();

        for (TTDTO ttdto : ttdtoList) {
            SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();
            try {
                ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
                if (salesContractSplitTTDTO.getAddedSignatureType() == 1) {
                    BeanUtils.copyProperties(salesContractSplitTTDTO, contractSignCreateDTO);
                    BeanUtils.copyProperties(contractEntity, contractSignCreateDTO);
                    setCommonProperties(contractSignCreateDTO, salesContractSplitTTDTO, contractEntity);
                    contractSignCreateDTO.setContractId(salesContractSplitTTDTO.getSourceContractId());
                } else if (salesContractSplitTTDTO.getAddedSignatureType() == 0) {
                    BeanUtils.copyProperties(salesContractSplitTTDTO, contractSignCreateDTO);
                    BeanUtils.copyProperties(sonContractEntity, contractSignCreateDTO);
                    setCommonProperties(contractSignCreateDTO, salesContractSplitTTDTO, null);
                    contractSignCreateDTO.setContractId(salesContractSplitTTDTO.getSonContractId());
                    if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == salesContractSplitTTDTO.getTradeType()) {
                        contractSignCreateDTO.setTradeType(ContractTradeTypeEnum.NEW.getValue());
                    }
                } else if (salesContractSplitTTDTO.getAddedSignatureType() == -1) {
                    continue; // 跳过当前循环，处理下一个元素
                }
                signCreateDTOList.add(contractSignCreateDTO);
            } catch (Exception e) {
                // 记录错误日志，根据实际情况处理异常
                e.printStackTrace();
            }
        }
        return signCreateDTOList;
    }

    private static void setCommonProperties(ContractSignCreateDTO contractSignCreateDTO, SalesContractSplitTTDTO salesContractSplitTTDTO, ContractEntity contractEntity) {
        contractSignCreateDTO
                .setTtId(salesContractSplitTTDTO.getTtId())
                .setTtCode(salesContractSplitTTDTO.getCode())
                .setSignatureStatus(salesContractSplitTTDTO.getContractSignatureStatus())
                .setGoodsCategoryId(contractEntity != null ? contractEntity.getGoodsCategoryId() : salesContractSplitTTDTO.getGoodsCategoryId())
                .setGoodsId(contractEntity != null ? contractEntity.getGoodsId() : salesContractSplitTTDTO.getGoodsId())
                .setOwnerId(contractEntity != null ? contractEntity.getOwnerId() : salesContractSplitTTDTO.getOwnerId())
                .setTtType(TTTypeEnum.SPLIT.getType())
                .setTradeType(salesContractSplitTTDTO.getTradeType())
                .setBelongCustomerId(contractEntity != null ? contractEntity.getBelongCustomerId() : salesContractSplitTTDTO.getBelongCustomerId())
                .setCustomerId(contractEntity != null ? contractEntity.getCustomerId() : salesContractSplitTTDTO.getCustomerId())
                .setSupplierId(contractEntity != null ? String.valueOf(contractEntity.getSupplierId()) : String.valueOf(salesContractSplitTTDTO.getSupplierId()))
                .setSignType(salesContractSplitTTDTO.getSignType())
                .setSignDate(salesContractSplitTTDTO.getSignDate())
                .setDeliveryStartTime(salesContractSplitTTDTO.getDeliveryStartTime())
                .setDeliveryEndTime(salesContractSplitTTDTO.getDeliveryEndTime())
                .setContractNum(salesContractSplitTTDTO.getContractNum())
                .setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
    }

    public static List<ContractSignCreateDTO> createReviseContractSignDTO(List<TTDTO> ttdtoList) {

        List<ContractSignCreateDTO> signCreateDTOList = new ArrayList<>();

        for (TTDTO ttdto : ttdtoList) {
            ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
            SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();
            // 不出协议处理
            if (salesContractReviseTTDTO.getAddedSignatureType() == -1) {
                continue;
            }

            BeanUtils.copyProperties(salesContractReviseTTDTO, contractSignCreateDTO);
            contractSignCreateDTO
                    .setTtId(salesContractReviseTTDTO.getTtId())
                    .setTtCode(salesContractReviseTTDTO.getCode())
                    .setSignatureStatus(salesContractReviseTTDTO.getContractSignatureStatus())
                    .setGoodsCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                    .setGoodsId(salesContractReviseTTDTO.getGoodsId())
                    .setOwnerId(Integer.parseInt(salesContractReviseTTDTO.getOwnerId()))
                    .setTtType(TTTypeEnum.REVISE.getType())
                    .setSupplierId(String.valueOf(salesContractReviseTTDTO.getSupplierId()))
                    .setBelongCustomerId(salesContractReviseTTDTO.getBelongCustomerId())
                    .setTradeType(salesContractReviseTTDTO.getTradeType())
                    .setSignDate(salesContractReviseTTDTO.getSignDate())
                    .setDeliveryStartTime(salesContractReviseTTDTO.getDeliveryStartTime())
                    .setDeliveryEndTime(salesContractReviseTTDTO.getDeliveryEndTime())
                    .setContractNum(salesContractReviseTTDTO.getContractNum())
                    .setContractSource(salesContractReviseTTDTO.getContractSource());

            signCreateDTOList.add(contractSignCreateDTO);

        }
        return signCreateDTOList;
    }

    public static ContractSignCreateDTO createPriceContractSignDTO(Integer ttId, ContractEntity contractEntity, TTDTO priceTTDTO) {
        ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
        SalesContractTTPriceDTO salesContractTTPriceDTO = priceTTDTO.getSalesContractTTPriceDTO();
        BeanUtils.copyProperties(salesContractTTPriceDTO, contractSignCreateDTO);
        contractSignCreateDTO
                .setTtCode(salesContractTTPriceDTO.getCode())
                .setTtId(ttId)
                .setSupplierId(String.valueOf(salesContractTTPriceDTO.getSupplierId()))
                .setTtType(TTTypeEnum.PRICE.getType())
                .setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setBelongCustomerId(salesContractTTPriceDTO.getBelongCustomerId())
                .setTradeType(salesContractTTPriceDTO.getTradeType())
        //.setContractId(salesContractTTPriceDTO.getContractId())
        ;
        // 仓单| 销售 | 交易类型线上 协议直接创建生成的协议 | 采购 | 销售 | 仓单销售回购- 采购合同
        if (BuCodeEnum.WT.getValue().equals(contractSignCreateDTO.getBuCode()) &&
                !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractSignCreateDTO.getWarrantTradeType())) {
            contractSignCreateDTO.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
        }

        // 补充信息
        contractSignCreateDTO.setSupplierId(String.valueOf(contractEntity.getSupplierId()))
                .setSupplierName(contractEntity.getSupplierName())
                .setGoodsId(contractEntity.getGoodsId());

        return contractSignCreateDTO;
    }

    public static List<ContractSignCreateDTO> createTransferContractSignDTO(ContractEntity contractEntity, List<TTDTO> ttdtoList) {

        List<ContractSignCreateDTO> signCreateDTOList = new ArrayList<>();

        for (TTDTO ttdto : ttdtoList) {
            ContractSignCreateDTO contractSignCreateDTO = new ContractSignCreateDTO();
            SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
            BeanUtils.copyProperties(salesContractTTTransferDTO, contractSignCreateDTO);
            contractSignCreateDTO.setTtId(salesContractTTTransferDTO.getTtId())
                    .setContractId(salesContractTTTransferDTO.getSonContractId() != null ? salesContractTTTransferDTO.getSonContractId() : contractEntity.getId())
                    .setTtCode(salesContractTTTransferDTO.getCode())
                    .setTtType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()) ? TTTypeEnum.TRANSFER.getType() : TTTypeEnum.REVERSE_PRICE.getType())
                    .setSignatureStatus(salesContractTTTransferDTO.getContractSignatureStatus())
                    .setGoodsCategoryId(salesContractTTTransferDTO.getGoodsCategoryId())
                    .setOwnerId(salesContractTTTransferDTO.getOwnerId())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setSupplierId(String.valueOf(salesContractTTTransferDTO.getSupplierId()))
                    .setBelongCustomerId(salesContractTTTransferDTO.getBelongCustomerId())
                    .setTradeType(salesContractTTTransferDTO.getTradeType())
                    .setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                    .setGoodsId(salesContractTTTransferDTO.getGoodsId());
            // 仓单| 销售 | 交易类型线上 协议直接创建生成的协议 | 采购 | 销售 | 仓单销售回购- 采购合同
            if (BuCodeEnum.WT.getValue().equals(contractSignCreateDTO.getBuCode()) &&
                    !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractSignCreateDTO.getWarrantTradeType())) {
                contractSignCreateDTO.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
            }

            // 双TT的转月、反点价暂时不生成协议
            if (salesContractTTTransferDTO.getAddedSignatureType() != -1) {
                signCreateDTOList.add(contractSignCreateDTO);
            }

        }
        return signCreateDTOList;
    }
}
