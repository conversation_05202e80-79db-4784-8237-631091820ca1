package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.enums.ContraryStatusEnum;
import com.navigator.trade.mapper.TtTranferMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON>ong on 2022/1/21 14:44
 */
@Dao
public class TtTranferDao extends BaseDaoImpl<TtTranferMapper, TTTranferEntity> {
    public List<TTTranferEntity> queryListByTTQueryDTO(TTQueryDTO ttQueryDTO) {
        List<TTTranferEntity> ttModifyEntityList = list(Wrappers.<TTTranferEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TTTranferEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TTTranferEntity::getGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
        );
        return ttModifyEntityList;
    }

    public TTTranferEntity getTTTransferEntityByTTId(Integer ttId) {
        List<TTTranferEntity> list = list(Wrappers.<TTTranferEntity>lambdaUpdate().eq(TTTranferEntity::getTtId, ttId));
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId) {
        return list(Wrappers.<TTTranferEntity>lambdaQuery()
                .eq(TTTranferEntity::getPriceApplyId, priceApplyId)
                .eq(TTTranferEntity::getContraryStatus, ContraryStatusEnum.NOT_CONTRARY.getValue())
        );
    }

    public List<TTTranferEntity> getTTTranferByPriceAllocateId(Integer priceAllocateId) {
        return list(Wrappers.<TTTranferEntity>lambdaQuery()
                .eq(TTTranferEntity::getPriceAllocateId, priceAllocateId)
                .eq(TTTranferEntity::getContraryStatus, ContraryStatusEnum.NOT_CONTRARY.getValue())
        );
    }

    public List<TTTranferEntity> getTTTranferByContractId(Integer contractId, Integer Id) {
        return list(Wrappers.<TTTranferEntity>lambdaQuery()
                .eq(TTTranferEntity::getSourceContractId, contractId)
                .ne(TTTranferEntity::getId, Id)
        );
    }

    public List<TTTranferEntity> selectTTTranferByPriceAllocateId(Integer priceAllocateId) {
        return list(Wrappers.<TTTranferEntity>lambdaQuery()
                .eq(TTTranferEntity::getPriceAllocateId, priceAllocateId)
        );
    }

    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        return update(Wrappers.<TTTranferEntity>lambdaUpdate()
                .set(TTTranferEntity::getContractId, contractEntity.getId())
                .set(TTTranferEntity::getContractCode, contractEntity.getContractCode())
                .set(TTTranferEntity::getContractType, contractEntity.getContractType())
                .set(TTTranferEntity::getSourceContractId, contractEntity.getParentId())
                .eq(TTTranferEntity::getTtId, ttId)) ? 1 : 0;
    }

    public boolean updatePriceAllocateId(Integer contractId, Integer allocateId) {
        return update(Wrappers.<TTTranferEntity>lambdaUpdate()
                .eq(TTTranferEntity::getContractId, contractId)
                .set(TTTranferEntity::getPriceAllocateId, allocateId)
        );
    }
}
