package com.navigator.trade.service.tradeticket.impl;


import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.service.tradeticket.IContractTradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public abstract class ContractTradeTicketAbstractService<T> implements IContractTradeTicketService {

    /***********************DAO***********************/


    /***********************Service & Handler***********************/


    /***********************Facade***********************/
    @Autowired
    protected CustomerFacade customerFacade;

    /***********************Other***********************/

    /***********************Service属性***********************/
    protected ModuleTypeEnum moduleType = ModuleTypeEnum.TRADE_TICKET;
    protected TTTypeEnum ttTypeEnum;
    protected ContractTradeTypeEnum contractTradeTypeEnum;
    protected ContractSalesTypeEnum salesType;
    protected OperationSourceEnum operationSource;
    protected LogBizCodeEnum logBizCodeEnum;
    private GoodsCategoryEnum goodsCategoryEnum;
    private GoodsCategoryEnum subGoodsCategoryEnum;

    public ContractTradeTicketAbstractService() {
    }

    /**
     * 初始化服务属性
     */
    protected abstract void abstractInitService();

    @Override
    public void saveAddContractTT(TradeTicketDTO tradeTicketDTO) throws Exception {

        /*
            1、初始化数据
            2、校验数据
            3、保存/更新主表
            4、生成合同
            5、保存/更新子表
        */

        //初始化服务属性
        initService(tradeTicketDTO);


    }

    private void initTradeTicketData(TradeTicketDTO tradeTicketDTO) {
        //初始化客户、供应商信息
        Integer customerId = tradeTicketDTO.getCustomerId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (null != customerDTO) {
            tradeTicketDTO.setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName());
        }
        //供应商信息
        Integer supplierId = tradeTicketDTO.getSupplierId();
        CustomerDTO supplierDTO = customerFacade.getCustomerById(supplierId);
        if (null != supplierDTO) {
            tradeTicketDTO.setSupplierCode(null != customerDTO ? customerDTO.getLinkageCustomerCode() : "")
                    .setSupplierName(customerDTO == null ? "" : customerDTO.getName());
        }
    }


    /**
     * 支持场景：新增合同、新增结构化定价合同
     */
    public void saveTT() {
        /*
        1、初始化数据
        2、校验数据
        3、保存/更新主表
        4、生成合同
        5、保存/更新子表
        */

    }

    /**
     * 支持场景：所有
     */
    public void saveAndSubmitTT() {
        /*
        1、保存数据
        2、发起审批
        3、生成合同协议
        4、更新合同
        */
        saveTT();
        //TODO submit


    }

    public void submitTT() {

    }

    /**
     * 初始化服务属性
     *
     * @param tradeTicketDTO
     */
    private void initService(TradeTicketDTO tradeTicketDTO) {
        this.salesType = ContractSalesTypeEnum.getByValue(tradeTicketDTO.getSalesType());
        this.ttTypeEnum = TTTypeEnum.getByType(tradeTicketDTO.getType());
        this.contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeTicketDTO.getTradeType());
        this.operationSource = OperationSourceEnum.getByValue(tradeTicketDTO.getOperationSource());
        this.goodsCategoryEnum = GoodsCategoryEnum.getByValue(tradeTicketDTO.getGoodsCategoryId());
        this.subGoodsCategoryEnum = GoodsCategoryEnum.getByValue(tradeTicketDTO.getSubGoodsCategoryId());
        abstractInitService();
    }


}
