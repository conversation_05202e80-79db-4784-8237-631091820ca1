/*
package com.navigator.trade.facade.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/send")
public class SendController {

    private static final String DESTINATION_NAME = "topic1";

    private static final Logger logger = LoggerFactory.getLogger(SendController.class);

    @Autowired
    private JmsTemplate jmsTemplate;



    @GetMapping("/messages")
    public String postMessage() {
        logger.info("Sending message");
        jmsTemplate.convertAndSend(DESTINATION_NAME, "Hello World");
        return "发送成功";
    }
}*/
