package com.navigator.trade.service;

import com.navigator.trade.pojo.entity.TTTranferEntity;

import java.util.List;

/**
 * <p>
 * TT-转月、反点价 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-21
 */
public interface ITtTranferService {

    boolean saveTtTranfer(TTTranferEntity ttTranferEntity);

    TTTranferEntity getTransferByTtId(Integer ttId);

    /**
     * TODO NEO 确认一下这个转月逻辑
     * 合同生成的时候，根据客户配置，生成可转月次数
     * 转月，每转月一次就减1，只要该数量大于0即可转
     */
    List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId);

    List<TTTranferEntity> getTTTranferByContractId(Integer contractId, Integer Id);

    /**
     * 修改转月,反点价单
     *
     * @param ttTranferEntity
     * @return
     */
    Boolean updateTTTranferById(TTTranferEntity ttTranferEntity);

    TTTranferEntity selectTTTranferByTTId(Integer TTId);

    List<TTTranferEntity> selectTTTranferByPriceAllocateId(Integer priceAllocateId);
}
