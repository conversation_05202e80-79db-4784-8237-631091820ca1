package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractSplitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
@Component("SBM_S_TT_SPLIT,SBO_S_TT_SPLIT")
public class SalesTTSplitServiceImpl extends BaseTradeTicketAbstractService {
    public SalesTTSplitServiceImpl() {
        ttTypeEnum = TTTypeEnum.SPLIT;
        contractTradeTypeEnum = ContractTradeTypeEnum.SPLIT_NORMAL;
        contractSource = ContractActionEnum.SPLIT.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.NEW.getType();
        salesType = ContractSalesTypeEnum.SALES;
        processorType = ProcessorTypeEnum.SBM_S_SPLIT.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_SPLIT.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getSplitTradeTicketEntity(ttdto.getSalesContractSplitTTDTO(), ttdto.getGroupId());
        injectionProperty(tradeTicketEntity);
        return tradeTicketEntity;
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) {
        // 保存tt-modify
        TTModifyEntity ttModifyEntity = saveTTModify(ttDto, ttId);
        // 保存价格信息
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttDto.getSalesContractSplitTTDTO();
        // BUGFIX：case-1002581 定价单含税单价错误-变更保存价格明细 Author: Mr 2024-04-30 Start
        // 拆分保存时候不存价格明细，ttModify中存在价格明细
        if (salesContractSplitTTDTO.getAddedSignatureType() == 0 && ttDto.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            saveContractPrice(ttModifyEntity);
        }
        // BUGFIX：case-1002581 定价单含税单价错误-变更保存价格明细 Author: Mr 2024-04-30 End
    }

    private void saveContractPrice(TTModifyEntity ttModifyEntity) {
        // 保存价格信息
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(ttModifyEntity, contractPriceEntity);

        contractPriceService.save(contractPriceEntity.setId(null)
                .setContractId(ttModifyEntity.getContractId())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now()));
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getSplitContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    private TTModifyEntity saveTTModify(TTDTO ttDto, Integer ttId) {
        TTModifyEntity ttModifyEntity = tradeTicketConvertUtil.convertToSplitTtModify(ttDto, ttId, ttTypeEnum.getType());
        // V1 值转对象 Author:zengshl 2024-06-18 start
        ttModifyEntity.setDestinationValue(systemRuleConvertValue(ttModifyEntity.getDestination()));
        ttModifyEntity.setPackageWeightValue(systemRuleConvertValue(ttModifyEntity.getPackageWeight()));
        ttModifyEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttModifyEntity.getDeliveryType()));
        ttModifyEntity.setWeightCheckValue(systemRuleConvertValue(ttModifyEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        ttModifyEntity.setInvoiceTypeValue(invoiceTypeConvertValue(ttModifyEntity.getInvoiceType()));
        ttModifyEntity.setShipWarehouseValue(factoryConvertValue(ttModifyEntity.getShipWarehouseId()));
        // V1 值转对象 Author:zengshl 2024-06-18 end
        ttModifyDao.save(ttModifyEntity);
        return ttModifyEntity;
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();
        Integer contractId = salesContractSplitTTDTO.getSourceContractId();
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);

        PriceDetailBO priceDetailBo = ttdto.getPriceDetailBO();

        List<CompareObjectDTO> compareObjectDTOList = getCompareObjectDTOList(salesContractSplitTTDTO, priceDetailBo, contractId);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractSplitTTDTO.setModifyContent(modifyContent);

        log.info("salesContractSplitTTDTO_CustomerId:{},contractEntity_CustomerId", salesContractSplitTTDTO.getCustomerId(), contractEntity.getCustomerId());
        //生成TT编号
        String code = CodeGeneratorUtil.genSalesTTNewCode();
        //判断主体是否变更
        if (salesContractSplitTTDTO.getCustomerId().equals(contractEntity.getCustomerId())) {
            contractTradeTypeEnum = ContractTradeTypeEnum.SPLIT_NORMAL;
            contractSource = ContractActionEnum.SPLIT.getActionValue();
        } else {
            contractTradeTypeEnum = ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER;
            contractSource = ContractActionEnum.SPLIT_CUSTOMER.getActionValue();
        }
        salesContractSplitTTDTO.setContractSource(contractSource);
        salesContractSplitTTDTO.setTradeType(contractTradeTypeEnum.getValue());

        if (salesContractSplitTTDTO.getAddedSignatureType() == -1
                || salesContractSplitTTDTO.getAddedSignatureType() == 1
                || ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()
        ) {
            // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 Start
            if (ttdto.getSubmitType() != SubmitTypeEnum.SAVE_SUBMIT.getValue()) {
                code = getSalesTTCode(contractId);
            } else {
                code = getSaveSalesTTCode(contractId);
            }
            // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 End
        } else {
            salesContractSplitTTDTO.setTradeType(ContractTradeTypeEnum.NEW.getValue());
        }

        //初始化交易、销售类型、合同来源
        salesContractSplitTTDTO.setTtType(ttTypeEnum.getType());
        salesContractSplitTTDTO.setSalesType(salesType.getValue());
        salesContractSplitTTDTO.setStatus(status);

        //协议签署状态
        salesContractSplitTTDTO.setContractSignatureStatus(contractSignatureStatus);
        salesContractSplitTTDTO.setUserId(JwtUtils.getCurrentUserId());
        salesContractSplitTTDTO.setCode(code);

        ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
        return ttdto;
    }


    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        return null;
    }

    private List<CompareObjectDTO> getCompareObjectDTOList(SalesContractSplitTTDTO salesContractSplitTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        return tradeTicketConvertUtil.getSplitCompareObjectDTO(salesContractSplitTTDTO, priceDetailBo, contractId);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
        String modifyContent = ttModifyEntity.getContent();
        List<CompareObjectDTO> compareList = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        List<CompareObjectDTO> list = tradeTicketConvertUtil.getSplitCompareList(compareList, tradeTicketEntity, ttModifyEntity);
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, priceDetailVO);
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        ttQueryDetailVO.setTradeType(tradeTicketEntity.getTradeType());
        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(ttModifyEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(ttModifyEntity.getPriceEndTime());

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttModifyEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttModifyEntity.getSupplierId()));
        }

        //买家
        if (null != ttModifyEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttModifyEntity.getCustomerId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttModifyEntity.getCustomerId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }

        //商品信息
        if (null != ttModifyEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttModifyEntity.getGoodsCategoryId()));
        }
        if (null != ttModifyEntity.getGoodsPackageId()) {
            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttModifyEntity.getGoodsPackageId()));
        }
        if (null != ttModifyEntity.getGoodsSpecId()) {
            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttModifyEntity.getGoodsSpecId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttModifyEntity.getDepositAmount()) {
            int depositAmountStatus = ttModifyEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttModifyEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttModifyEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttQueryDetailVO.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        if (null != ttModifyEntity.getDepositRate()) {
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateFacade.getCustomerDepositRateById(ttModifyEntity.getDepositRate());
            if (null != customerDepositRateEntity) {
                ttQueryDetailVO.setDepositRateValue(customerDepositRateEntity.getDepositRate());
            }
        }

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttModifyEntity.getShipWarehouseId());

        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        if (ttModifyEntity.getNewContractNum() != null) {
            ttQueryDetailVO.setContractNum(ttModifyEntity.getNewContractNum());
            BigDecimal totalAmount = ttModifyEntity.getNewContractNum().multiply(ttModifyEntity.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);
            ttQueryDetailVO.setTotalAmount(totalAmount);
        }
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ttDetailVO.setCompareObjectDTOList(list);
        if (CollectionUtils.isEmpty(compareList)) {
            ttDetailVO.setDetailType("7");
        } else {
            ttDetailVO.setDetailType("8");
        }
        ContractEntity originalContractEntity = contractService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setSourceType(tradeTicketEntity.getSourceType())
                .setOriginContractNum(originalContractEntity.getContractNum())
                .setOriginContractType(originalContractEntity.getContractType())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        return ttDetailVO;
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {
        tradeTicketConvertUtil.updateModifyContent(originalContractEntity, contractEntity, ttId);
    }
}
