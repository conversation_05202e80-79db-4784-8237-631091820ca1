package com.navigator.trade.app.contract.logic.service.handler;


import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 销售合同回购逻辑处理
 *
 * <AUTHOR>
 * @Date 2024-07-15
 */
public interface BuyBackLogicService {

    /**
     * 校验合同信息 -- 回购校验
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 变更dto
     */
    void buyBackContractCheck(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 更新父子合同数据以及操作日志
     *
     * @param contractEntity
     * @param contractBuyBackDTO
     */
    void operateFatherContract(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO);

    /**
     * 记录回购日志
     *
     * @param contractBuyBackDTO
     * @param contractEntity
     */
    void recordOperationLog(ContractBuyBackDTO contractBuyBackDTO, ContractEntity contractEntity);
}
