package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantModifyTypeEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.WriteOffLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.facade.impl.DeliveryTypeFacadeImpl;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffWithDrawDTO;
import com.navigator.trade.pojo.dto.contract.WriteOffContractDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class WriteOffLogicServiceImpl implements WriteOffLogicService {


    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private WarrantFacade warrantFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private DeliveryTypeFacadeImpl deliveryTypeFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerInvoiceFacade customerInvoiceFacade;
    @Resource
    private SequenceUtil sequenceUtil;
    @Resource
    private TradeDomainRemoteService tradeDomainRemoteService;
    @Value("${sync.atlas.openQuery:0}")
    private Integer openAtlasQuery;  // 是否开启atlas查询

    /**
     * 合同域
     */
    @Autowired
    ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    @Autowired
    private ContractSignQueryLogicService contractSignQueryLogicService;

    @Autowired
    private CommonLogicService commonLogicService;


    /**
     * 前提：仅合同类型为一口价
     * 且合同状态为生效中
     * 且注销状态为未注销注销中
     * 合同可注销回购量合同总量已注销量已回购量注销撤回量
     * 同时计算下属于哪个注销场景
     *
     * @param contractEntity      合同实体
     * @param contractWriteOffDTO 解约定赔dto
     */
    @Override
    public void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        // 合同类型为一口价
        if (!(ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_WRITEOFF);
        }
        // 销状态为未注销注销中
        if (ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue() == contractEntity.getWriteOffStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_WRITE_OFF_STATUS);
        }
        // 合同可注销 = 合同总量-已注销量-已回购量 + 注销撤回量（已经算到已住销量了）
        BigDecimal canWriteOffNum = contractEntity.getContractNum().subtract(contractEntity.getWarrantCancelCount())
                .subtract(contractEntity.getTotalBuyBackNum());
        if (contractWriteOffDTO.getWriteOffNum().compareTo(canWriteOffNum) > 0) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_NUM_LESS_WRITE_NUM);
        }
        // 采购注销需要计算最大注销量
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            // 获取仓单持有量
            Result result = warrantFacade.queryWarrantByID(contractEntity.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            BigDecimal minWriteOffNum = warrantEntity.getHoldCount();
            if (contractWriteOffDTO.getWriteOffNum().compareTo(minWriteOffNum) > 0) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_NUM_LESS_WARRANT_NUM);
            }

        }
        if (StringUtils.isNotBlank(contractWriteOffDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteByCode(contractWriteOffDTO.getSiteCode());
            if (null != siteEntity) {
                contractWriteOffDTO.setDeliveryFactoryCode(siteEntity.getFactoryCode());
            }
        }
        // 判断注销走哪个场景
        judgeWriteOffScene(contractEntity, contractWriteOffDTO);

    }


    /**
     * 修改补充补充基本信息-创建子合同
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     * @param isPurchase          是否场景三需要生成新的采购合同编号
     */
    @Override
    public void buildBaseInfo(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity, boolean isPurchase) {
        ContractEntity parentContractEntity = contractWriteOffDTO.getParentContractEntity();
        // 复制父合同信息
        BeanUtils.copyProperties(parentContractEntity, contractEntity);
        // 复制填写的销售信息
        BeanUtils.copyProperties(contractWriteOffDTO, contractEntity);
        // 付款方式
        // contractEntity.setPaymentType(contractWriteOffDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());
        // 处理基本数据
        String sonContractCode = "";
        // 子编号|【新采购合同编号】
        if (isPurchase) {
            sonContractCode = CodeGeneratorUtil.genContractNewCode(contractEntity.getExchangeCode(), contractWriteOffDTO.getFutureCode(), ContractSalesTypeEnum.PURCHASE);
        } else {
            sonContractCode = contractQueryDomainService.genSonContractCode(parentContractEntity.getContractCode(), parentContractEntity.getSalesType());
        }
        if (StringUtils.isNotBlank(contractWriteOffDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteByCode(contractWriteOffDTO.getSiteCode());
            if (null != siteEntity) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteEntity.getFactoryCode());
                contractEntity.setDeliveryFactoryCode(siteEntity.getFactoryCode())
                        .setDeliveryFactoryName(null != factoryEntity ? factoryEntity.getName() : "");
            }
        }
        contractEntity.setId(null)
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setParentId(parentContractEntity.getId())
                .setRootId(parentContractEntity.getParentId() == 0 ? parentContractEntity.getId() : parentContractEntity.getParentId())
                .setShipWarehouseId(Integer.valueOf(contractWriteOffDTO.getShipWarehouseId()))
                .setMemo(contractWriteOffDTO.getMemo())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setCreateBatch(null);

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Inherit group_id from parent contract for write-off operations
        if (parentContractEntity.getGroupId() != null) {
            contractEntity.setGroupId(parentContractEntity.getGroupId());
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
    }

    /**
     * 处理变更的业务信息
     * 修改货品，交割库（库点），交货方式，目的港，注销说明备注，赊销账期，交货周期，短装溢，所属商务，履约保证金
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    @Override
    public void buildBizInfo(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity) {

        ContractEntity parentContractEntity = contractWriteOffDTO.getParentContractEntity();
        commonLogicService.updateCustomerInfo(parentContractEntity.getCustomerId(), parentContractEntity.getSupplierId(),
                contractWriteOffDTO.getCustomerId(), contractWriteOffDTO.getSupplierId(), contractEntity);

        // 修改货品 配置域获取数据信息
        SkuEntity skuEntity = skuFacade.getSkuById(contractWriteOffDTO.getGoodsId());
        List<String> nickNameList = StringUtil.split(skuEntity.getNickName(), "\\$\\$");
        contractEntity.setGoodsCategoryId(skuEntity.getCategory2())
                .setGoodsPackageId(skuEntity.getPackageId())
                .setGoodsSpecId(skuEntity.getSpecId())
                .setGoodsId(skuEntity.getId())
                .setGoodsName(skuEntity.getFullName())
                // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 start
                .setCommodityName(CollectionUtils.isEmpty(nickNameList) ? "" : nickNameList.get(0))
                // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end
                .setCategory1(skuEntity.getCategory1())
                .setCategory2(skuEntity.getCategory2())
                .setCategory3(skuEntity.getCategory3());
        //税率
        if (skuEntity.getTaxRate() != null) {
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            contractEntity.setTaxRate(taxRate);
        }
        // 价格处理
        PriceDetailBO priceDetailBO = contractWriteOffDTO.getPriceDetailDTO();
        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 交货方式
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        contractEntity.setDeliveryTypeValue(deliveryTypeEntity != null ? deliveryTypeEntity.getName() : "");

        // 目的港
        if (ObjectUtil.isNotEmpty(contractEntity.getDestination())) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getDestination()));
            contractEntity.setDestinationValue(destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "");
        }

        // 处理下交割库数据
        if (ObjectUtil.isNotEmpty(contractWriteOffDTO.getShipWarehouseId())) {
            contractEntity.setShipWarehouseValue(contractWriteOffDTO.getShipWarehouseName());
        }

        //查询发票信息
        Integer customerId = contractEntity.getCustomerId();
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            customerId = contractEntity.getSupplierId();
        }
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (!customerInvoiceEntities.isEmpty()) {
            int invoiceId = customerInvoiceEntities.isEmpty() ? 0 : customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (null != systemRuleItemEntity) {
                //发票类型
                contractEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
                contractEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(Integer.parseInt(systemRuleItemEntity.getRuleKey())));

            }
        }

        // 注销提货时间变动,前端计算了
        if (ObjectUtil.isNotEmpty(contractWriteOffDTO.getWriteOffDeliveryStartTime())) {
            contractEntity.setDeliveryStartTime(contractWriteOffDTO.getWriteOffDeliveryStartTime());
            contractEntity.setDeliveryEndTime(contractWriteOffDTO.getWriteOffDeliveryEndTime());
        }

        if (ContractActionEnum.WRITE_OFF_C.getActionValue() == contractWriteOffDTO.getWriteOffAction()) {
            //注销C
            contractEntity.setCreditDays(contractWriteOffDTO.getCreditDays())
                    .setDepositRate(contractWriteOffDTO.getDepositRate())
                    .setAddedDepositRate(contractWriteOffDTO.getAddedDepositRate())
                    .setPayConditionId(contractWriteOffDTO.getPayConditionId())
                    //.setDepositAmount(contractWriteOffDTO.getDepositAmount())
                    .setInvoicePaymentRate(contractWriteOffDTO.getInvoicePaymentRate());
        } else {
            // 提货合同和提货权合同继承父合同信息
            contractEntity.setCreditDays(parentContractEntity.getCreditDays())
                    .setDepositRate(parentContractEntity.getDepositRate())
                    .setAddedDepositRate(parentContractEntity.getAddedDepositRate())
                    .setPayConditionId(parentContractEntity.getPayConditionId())
                    //.setDepositAmount(parentContractEntity.getDepositAmount())
                    .setInvoicePaymentRate(parentContractEntity.getInvoicePaymentRate());
        }

        // 重新汇总数据
        contractEntity.setOrderNum(contractWriteOffDTO.getWriteOffNum())
                .setContractNum(contractWriteOffDTO.getWriteOffNum())
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractWriteOffDTO.getWriteOffNum(), contractUnitPriceVO.getUnitPrice()))
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractWriteOffDTO.getWriteOffNum(), contractUnitPriceVO.getUnitPrice()))
                .setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum())
                .setTotalPriceNum(contractWriteOffDTO.getWriteOffNum())
                .setTotalModifyNum(BigDecimal.ZERO)
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setTotalTransferNum(BigDecimal.ZERO)
                .setTotalBuyBackNum(BigDecimal.ZERO)
                .setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue())
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setExtraPrice(priceDetailBO.getExtraPrice())
                .setBaseDiffPrice(priceDetailBO.getExtraPrice());

        // 付款方式
        contractEntity.setPaymentType(contractEntity.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

        // 履约保证金计算
        contractEntity.setDepositAmount(contractEntity.getTotalAmount().multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
    }

    /**
     * 构建采购的合同的基础信息
     * 修改货品，交割库（库点），交货方式，目的港，注销说明备注，赊销账期，交货周期，短装溢，所属商务，履约保证金
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    @Override
    public void buildBizInfoPurchase(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity) {
        // 采购填写的信息回填到合同里面
        BeanUtils.copyProperties(contractWriteOffDTO.getContractWriteOffPurchaseDTO(), contractEntity);
        ContractEntity parentContractEntity = contractWriteOffDTO.getParentContractEntity();
        // 主体变更
        commonLogicService.updateCustomerInfo(parentContractEntity.getSupplierId(), parentContractEntity.getCustomerId(),
                contractWriteOffDTO.getSupplierId(), contractWriteOffDTO.getCustomerId(), contractEntity);

        // 修改货品 配置域获取数据信息
        SkuEntity skuEntity = skuFacade.getSkuById(contractWriteOffDTO.getContractWriteOffPurchaseDTO().getGoodsId());
        List<String> nickNameList = StringUtils.isNotBlank(skuEntity.getNickName()) ? StringUtil.split(skuEntity.getNickName(), "\\$\\$") : new ArrayList<>();
        String nickName = CollectionUtils.isEmpty(nickNameList) ? skuEntity.getFullName() : nickNameList.get(0);
        contractEntity.setGoodsCategoryId(skuEntity.getCategory2())
                .setGoodsPackageId(skuEntity.getPackageId())
                .setGoodsSpecId(skuEntity.getSpecId())
                .setGoodsId(skuEntity.getId())
                .setGoodsName(skuEntity.getFullName())
                .setCommodityName(nickName)
                .setCategory1(skuEntity.getCategory1())
                .setCategory2(skuEntity.getCategory2())
                .setCategory3(skuEntity.getCategory3());
        //税率
        if (skuEntity.getTaxRate() != null) {
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            contractEntity.setTaxRate(taxRate);
        }
        // 价格处理
        PriceDetailBO priceDetailBO = contractWriteOffDTO.getContractWriteOffPurchaseDTO().getPriceDetailDTO();
        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 交货方式
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        contractEntity.setDeliveryTypeValue(deliveryTypeEntity != null ? deliveryTypeEntity.getName() : "");

        // 目的港
        SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getDestination()));
        contractEntity.setDestinationValue(destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "");

        //查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(contractEntity.getSupplierId())
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (!customerInvoiceEntities.isEmpty()) {
            int invoiceId = customerInvoiceEntities.isEmpty() ? 0 : customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (null != systemRuleItemEntity) {
                //发票类型
                contractEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
                contractEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(Integer.parseInt(systemRuleItemEntity.getRuleKey())));

            }
        }

        // 注销提货时间变动,前端计算了
        if (ObjectUtil.isNotEmpty(contractWriteOffDTO.getWriteOffDeliveryStartTime())) {
            contractEntity.setDeliveryStartTime(contractWriteOffDTO.getWriteOffDeliveryStartTime());
            contractEntity.setDeliveryEndTime(contractWriteOffDTO.getWriteOffDeliveryEndTime());
        }

        // 重新汇总数据
        contractEntity.setOrderNum(contractWriteOffDTO.getWriteOffNum())
                .setContractNum(contractWriteOffDTO.getWriteOffNum())
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractWriteOffDTO.getWriteOffNum(), contractUnitPriceVO.getUnitPrice()))
                .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractWriteOffDTO.getWriteOffNum(), contractUnitPriceVO.getUnitPrice()))
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setWarrantCancelCount(contractWriteOffDTO.getWriteOffNum())
                .setTotalPriceNum(contractWriteOffDTO.getWriteOffNum())
                .setTotalModifyNum(BigDecimal.ZERO)
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setTotalTransferNum(BigDecimal.ZERO)
                .setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue())
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setExtraPrice(priceDetailBO.getExtraPrice())
                .setBaseDiffPrice(priceDetailBO.getExtraPrice());
    }

    /**
     * 创建合同
     *
     * @param contractWriteOffDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createContract(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity) {
        // 保存合同
        try {
            contractDomainService.saveContract(contractEntity);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
        // 记录子合同信息
        contractWriteOffDTO.setSonContractId(contractEntity.getId())
                .setSonContractType(contractEntity.getContractType());
    }

    /**
     * 处理仓单销售合同信息
     *
     * @param contractWriteOffDTO 注销信息
     * @param contractEntity      仓单合同
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void operateFatherContract(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity) {
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 注销量
        BigDecimal writeOffNum = ObjectUtil.isNotEmpty(contractEntity.getWarrantCancelCount())
                ? contractEntity.getWarrantCancelCount() : BigDecimal.ZERO;
        // 生成提货合同
        if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffDTO.getWriteOffAction()) {
            contractNum = contractNum.subtract(contractWriteOffDTO.getWriteOffNum());
            contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
            contractEntity.setCreateBatch(ContractTradeTypeEnum.WARRANT_WITHDRAW.getDesc());
        }
        // 生成提货合同和采购合同
        if (ContractActionEnum.WRITE_OFF_C.getActionValue() == contractWriteOffDTO.getWriteOffAction()) {
            writeOffNum = writeOffNum.add(contractWriteOffDTO.getWriteOffNum());
        }
        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setOrderNum(contractNum)
                .setWarrantCancelCount(writeOffNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                .setTotalPriceNum(contractNum)
                .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        // 注销状态【计算下回购量】
        BigDecimal buyBackNum = ObjectUtil.isNotEmpty(contractEntity.getTotalBuyBackNum()) ? contractEntity.getTotalBuyBackNum() : BigDecimal.ZERO;
        if (contractEntity.getContractNum().compareTo(contractEntity.getWarrantCancelCount().add(buyBackNum)) == 0) {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue());
        } else {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        }
        // 记录合同日志
        contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void writeOffWithDrawCheck(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {

        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 原合同状态处于生效中 原仓单合同被作废、关闭、回购时应不支持注销撤回
        if (!(contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue()) ||
                (contractEntity.getStatus().equals(ContractStatusEnum.MODIFYING.getValue())
                        && ContractTradeTypeEnum.WARRANT_WITHDRAW.getDesc().equals(contractEntity.getCreateBatch())))) {
            throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_INVALID_STATUS);
        }
        // 协议：待回签和待确认合规不可撤回
        List<Integer> ttStatusList = new ArrayList<Integer>();
        ttStatusList.add(ContractSignStatusEnum.WAIT_BACK.getValue());
        ttStatusList.add(ContractSignStatusEnum.WAIT_CONFIRM.getValue());
        // 提货合同协议校验【注销B和注销C】
        if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffWithDrawDTO.getWriteOffAction()) {
            contractWriteOffWithDrawDTO.setDeliveryContractId(contractWriteOffWithDrawDTO.getWriteOffContractDTOS().get(0).getReferContractId());
        }
        if (ContractActionEnum.WRITE_OFF_C.getActionValue() == contractWriteOffWithDrawDTO.getWriteOffAction()) {
            for (WriteOffContractDTO item : contractWriteOffWithDrawDTO.getWriteOffContractDTOS()) {
                if (ContractSalesTypeEnum.SALES.getValue() == item.getSalesType()) {
                    contractWriteOffWithDrawDTO.setDeliveryContractId(item.getReferContractId());
                } else {
                    contractWriteOffWithDrawDTO.setPurchaseContractId(item.getReferContractId());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(contractWriteOffWithDrawDTO.getDeliveryContractId())) {
            List<ContractSignEntity> contractSignEntities = contractSignQueryLogicService.querySignListByContractId(
                    contractWriteOffWithDrawDTO.getDeliveryContractId(), ttStatusList);
            if (ObjectUtil.isNotEmpty(contractSignEntities) && contractSignEntities.size() > 0) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_WITH_DRAW_SIGN_STATUS);
            }
        }
        // 采购合同协议校验
        if (ObjectUtil.isNotEmpty(contractWriteOffWithDrawDTO.getPurchaseContractId())) {
            List<ContractSignEntity> contractSignEntities = contractSignQueryLogicService.querySignListByContractId(
                    contractWriteOffWithDrawDTO.getPurchaseContractId(), ttStatusList);
            if (ObjectUtil.isNotEmpty(contractSignEntities) && contractSignEntities.size() > 0) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_WITH_DRAW_SIGN_STATUS);
            }
        }

        // 【采购】不校验提货数量 TODO 都生效的情况下，多个子合同都要进行验证
        if (ContractSalesTypeEnum.SALES.equals(contractEntity.getSalesType())) {
            // BI接口查询 仓单合同注销撤回:根据注销记录的条件【提货主体、货品、提货工厂、卖方主体、提货方式】 【校验撤回注销数量该货品<= 可提货申请量】
            BigDecimal withDrawNum = contractWriteOffWithDrawDTO.getWithDrawNum();
            contractWriteOffWithDrawDTO.getWriteOffContractDTOS().forEach(item -> {
                ContractEntity contract = contractQueryDomainService.getBasicContractById(item.getReferContractId());
                ExchangePickQtyQO pickQtyQO = new ExchangePickQtyQO();
                pickQtyQO.setGoodsId(contract.getGoodsId());
                pickQtyQO.setCompanyId(contract.getCompanyId());
                pickQtyQO.setCustomerId(contract.getCustomerId());
                pickQtyQO.setDeliveryType(contract.getDeliveryType());
                pickQtyQO.setDeliveryFactoryCode(contract.getDeliveryFactoryCode());

                // 销售合同才校验提货
                if (ContractSalesTypeEnum.SALES.equals(contract.getSalesType())) {
                    ExchangePickQtyDTO exchangePickQtyDTO = tradeDomainRemoteService.getExchangePickQty(pickQtyQO);
                    if (ObjectUtil.isNotEmpty(exchangePickQtyDTO) && withDrawNum.compareTo(exchangePickQtyDTO.getPickupQty()) > 0) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_WITH_DRAW_NUM_MORE_DELIVERY_NUM);
                    }
                }

            });
        }

        // add by zengshl 注销B和注销C需要验证合同的提货量和撤回量的对比 采购注销B，销售有注销B和C
        BigDecimal withDrawNum = contractWriteOffWithDrawDTO.getWithDrawNum();
        // 注销B或者C 撤回
        if (ObjectUtil.isNotEmpty(contractWriteOffWithDrawDTO.getDeliveryContractId()) && openAtlasQuery == 1) {
            ContractEntity contract = contractQueryDomainService.getBasicContractById(contractWriteOffWithDrawDTO.getDeliveryContractId());
            if (ContractStatusEnum.EFFECTIVE.getValue() == contract.getStatus()) {
                Result<BigDecimal> result = commonLogicService.getContractOpenQuantity(contract);
                if (result.isSuccess()) {
                    BigDecimal deliveredNum = result.getData().setScale(6, RoundingMode.HALF_UP);
                    log.info("===============注销数量 [{}] 可提量 : [{}] 比较结果 ===============[{}]", withDrawNum, deliveredNum, withDrawNum.compareTo(deliveredNum) != 0);
                    if (withDrawNum.compareTo(deliveredNum) != 0) {
                        throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_WITHDRAW_ATLAS);
                    }
                }
            }

        }
        // 注销C 撤回
        if (ObjectUtil.isNotEmpty(contractWriteOffWithDrawDTO.getPurchaseContractId()) && openAtlasQuery == 1) {
            ContractEntity contract = contractQueryDomainService.getBasicContractById(contractWriteOffWithDrawDTO.getPurchaseContractId());
            if (ContractStatusEnum.EFFECTIVE.getValue() == contract.getStatus()) {
                Result<BigDecimal> result = commonLogicService.getContractOpenQuantity(contract);
                if (result.isSuccess()) {
                    BigDecimal deliveredNum = result.getData().setScale(6, RoundingMode.HALF_UP);
                    log.info("===============注销数量 [{}] 可提量 : [{}] 比较结果 [{}]=============", withDrawNum, deliveredNum, withDrawNum.compareTo(deliveredNum) != 0);
                    if (withDrawNum.compareTo(deliveredNum) != 0) {
                        throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_WITHDRAW_ATLAS);
                    }
                }
            }

        }
        // 豆二注销 撤回
        if (contractEntity.getIsSoybean2() != null && contractEntity.getIsSoybean2() == 1 && openAtlasQuery == 1) {
            List<WriteOffContractDTO> writeOffContractDTOList = contractWriteOffWithDrawDTO.getWriteOffContractDTOS()
                    .stream().filter(item -> !ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(item.getContractNature())).collect(Collectors.toList());
            writeOffContractDTOList.forEach(item -> {
                ContractEntity contract = contractQueryDomainService.getBasicContractById(item.getReferContractId());
                Result<BigDecimal> result = commonLogicService.getContractOpenQuantity(contract);
                if (result.isSuccess()) {
                    BigDecimal deliveredNum = result.getData().setScale(6, RoundingMode.HALF_UP);
                    log.info("===============注销数量 [{}] 可提量 : [{}] 比较结果 [{}]=============", contract.getContractNum(), deliveredNum, contract.getContractNum().compareTo(deliveredNum) != 0);
                    if (contract.getContractNum().compareTo(deliveredNum) != 0) {
                        throw new BusinessException(ResultCodeEnum.WARRANT_CONTRACT_WITHDRAW_ATLAS);
                    }
                }
            });
        }


    }

    @Override
    public void writeOffWithDraw(ContractEntity contractEntity, ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        // 1.退回仓单住销量
        commonLogicService.updateWarrantNum(contractEntity, WarrantModifyTypeEnum.CONTRACT_CANCEL_REVOKE.getValue(), contractWriteOffWithDrawDTO.getWithDrawNum(), contractWriteOffWithDrawDTO.getCancellationId());
        // 2.货权合同作废|采购合同作废|提货合同作废 非豆二
        contractWriteOffWithDrawDTO.getWriteOffContractDTOS().forEach(item -> {
            ContractEntity contract = contractQueryDomainService.getBasicContractById(item.getReferContractId());
            item.setContractStatus(contract.getStatus());
            writeOffInvalidContract(item);
        });

        // 更新仓单已注销数量【分场景进行恢复注销量|合同量|注销B是拆分的动作】
        if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffWithDrawDTO.getWriteOffAction()) {
            // 重新计算价格和合同数量
            BigDecimal contractNum = contractEntity.getContractNum().add(contractWriteOffWithDrawDTO.getWithDrawNum());
            contractEntity
                    .setContractNum(contractNum)
                    .setOrderNum(contractNum)
                    .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                    .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                    .setTotalPriceNum(contractNum)
                    .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()));
        } else {
            contractEntity.setWarrantCancelCount(contractEntity.getWarrantCancelCount().subtract(contractWriteOffWithDrawDTO.getWithDrawNum()));
        }
        if (contractEntity.getWarrantCancelCount().compareTo(BigDecimal.ZERO) == 0) {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.NOT_WRITEOFF.getValue());
        } else {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        }
        // 更新仓单合同
        contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue());
        contractDomainService.updateContractById(contractEntity);


        // 3.记录撤销日志
        commonLogicService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.WRITE_OFF_WITH_DRAW, JSONUtil.toJsonStr(contractWriteOffWithDrawDTO),
                SystemEnum.MAGELLAN.getValue());
    }

    /**
     * 注销作废合同
     *
     * @param writeOffContractDTO
     */
    private void writeOffInvalidContract(WriteOffContractDTO writeOffContractDTO) {
        if (ObjectUtil.isNotEmpty(writeOffContractDTO)) {
            contractDomainService.updateContractStatusById(writeOffContractDTO.getReferContractId(), ContractStatusEnum.INVALID.getValue());
        }
    }


    /**
     * 判断注销的场景|协议是否出具
     *
     * @param contractEntity
     * @param contractWriteOffDTO
     */
    private void judgeWriteOffScene(ContractEntity contractEntity, ContractWriteOffDTO contractWriteOffDTO) {
        // 计算下属于哪个注销场景
        if (contractEntity.getGoodsId().equals(contractWriteOffDTO.getGoodsId()) &&
                contractEntity.getUnitPrice().compareTo(contractWriteOffDTO.getUnitPrice()) == 0) {
            contractWriteOffDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_A.getActionValue());
        } else {
            // 采购不判断提货方，就是场景二
            if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                contractWriteOffDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_B.getActionValue());
            } else {
                // 销售需要多个一个提货方修改的判断，计算属性和修改提货方走一还是走二 提货方是customerId 提货方
                if (contractEntity.getCustomerId().equals(contractWriteOffDTO.getCustomerId())) {
                    // 判断是否修改其他字段
                    contractWriteOffDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_B.getActionValue());
                } else {
                    // 修改了提货方
                    contractWriteOffDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_C.getActionValue());
                }
            }

        }

    }

}
