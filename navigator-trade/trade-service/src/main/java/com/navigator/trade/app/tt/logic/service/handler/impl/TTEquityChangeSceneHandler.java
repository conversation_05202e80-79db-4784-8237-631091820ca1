package com.navigator.trade.app.tt.logic.service.handler.impl;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("EQUITY_CHANGE_HANDLER")
public class TTEquityChangeSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {

    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        return null;
    }

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return false;
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        return null;
    }

}
