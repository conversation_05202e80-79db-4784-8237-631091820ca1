package com.navigator.trade.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.AuditStatusEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.DomainPriceMapper;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import com.navigator.trade.pojo.entity.DomainPriceEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/20 10:46
 */
@Dao
public class DomainPriceDao extends BaseDaoImpl<DomainPriceMapper, DomainPriceEntity> {

    /**
     * 根据合约和交易日查询合约价格
     *
     * @param categoryId 品类
     * @param domainCode 合约
     * @param tradeDate  交易日
     * @return
     */
    public DomainPriceEntity getDomainPrice(Integer categoryId, String domainCode, String tradeDate, String categoryCode) {
        List<DomainPriceEntity> domainPriceEntityList = this.list(
                Wrappers.<DomainPriceEntity>lambdaQuery()
//                        .eq(StringUtil.isNotNullBlank(categoryId), DomainPriceEntity::getCategoryId, categoryId)
                        .eq(StringUtil.isNotNullBlank(categoryCode), DomainPriceEntity::getCategoryCode, categoryCode)
                        .eq(DomainPriceEntity::getDomainCode, domainCode)
                        .eq(DomainPriceEntity::getTradeDate, tradeDate)
                        .eq(DomainPriceEntity::getStatus, AuditStatusEnum.PASS.getValue())
                        .eq(DomainPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(domainPriceEntityList) ? null : domainPriceEntityList.get(0);
    }

    /**
     * 列表查询收盘价
     * {@link AuditStatusEnum}
     *
     * @param queryDTO 状态（0 待审核 1通过 2 驳回）
     * @return
     */
    public IPage<DomainPriceEntity> queryDomainPrice(QueryDTO<QueryDomainPriceBO> queryDTO) {
        Integer status = null != queryDTO.getCondition() ? queryDTO.getCondition().getStatus() : null;
        Integer categoryId = null != queryDTO.getCondition() ? queryDTO.getCondition().getCategoryId() : null;
        String categoryCode = null != queryDTO.getCondition() ? queryDTO.getCondition().getCategoryCode() : null;
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), new LambdaQueryWrapper<DomainPriceEntity>()
                .eq(null != status, DomainPriceEntity::getStatus, status)
//                .eq(null != categoryId, DomainPriceEntity::getCategoryId, categoryId)
                .eq(StringUtil.isNotNullBlank(categoryCode), DomainPriceEntity::getCategoryCode, categoryCode)
                .orderByDesc(DomainPriceEntity::getUpdatedAt)
        );
    }


    public List<DomainPriceEntity> queryDomainPriceToday(DomainPriceTodayDTO domainPriceTodayDTO, String domainCode) {
        return this.list(Wrappers.<DomainPriceEntity>lambdaQuery()
//                .eq(null != domainPriceTodayDTO.getCategoryId(), DomainPriceEntity::getCategoryId, domainPriceTodayDTO.getCategoryId())
                .eq(StringUtil.isNotNullBlank(domainPriceTodayDTO.getCategoryCode()), DomainPriceEntity::getCategoryCode, domainPriceTodayDTO.getCategoryCode())
                .eq(DomainPriceEntity::getDomainCode, domainCode)
                .eq(DomainPriceEntity::getTradeDate, new Date())
                .eq(DomainPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != domainPriceTodayDTO.getAuditStatus(), DomainPriceEntity::getStatus, domainPriceTodayDTO.getAuditStatus()));
    }

    public DomainPriceEntity getLastestClosingPrice(Integer categoryId, String domainCode, Date signDate, String categoryCode) {
        List<DomainPriceEntity> list = this.list(Wrappers.<DomainPriceEntity>lambdaQuery()
//                .eq(StringUtil.isNotNullBlank(categoryId), DomainPriceEntity::getCategoryId, categoryId)
                .eq(StringUtil.isNotNullBlank(categoryCode), DomainPriceEntity::getCategoryCode, categoryCode)
                .eq(DomainPriceEntity::getDomainCode, domainCode)
                .lt(DomainPriceEntity::getTradeDate, DateTimeUtil.formatDateTimeString00(signDate))
                .eq(DomainPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(DomainPriceEntity::getTradeDate)
        );
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

}
