package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;

/**
 * 结构化定价业务处理过程 逻辑处理
 *
 * <AUTHOR>
 */
public interface StructureLogicService {

    /**
     * 校验合同结构化数量
     *
     * @param verifyContractStructureNumDTO
     * @return
     */
    Boolean verifyContractStructureNum(VerifyContractStructureNumDTO verifyContractStructureNumDTO);

}
