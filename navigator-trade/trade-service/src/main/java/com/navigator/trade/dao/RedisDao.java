package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.RedisMapper;
import com.navigator.trade.pojo.entity.RedisEntity;

import java.util.List;

@Dao
public class RedisDao extends BaseDaoImpl<RedisMapper, RedisEntity> {
    public List<RedisEntity> queryByVersion(String version) {

        List<RedisEntity> list = list(Wrappers.<RedisEntity>lambdaQuery().eq(RedisEntity::getVersion, version));
        return list;
    }

    public RedisEntity getByRedisKey(String key) {
        List<RedisEntity> list = list(Wrappers.<RedisEntity>lambdaQuery().like(RedisEntity::getRedisKey, key));

        return !list.isEmpty() ? list.get(0) : null;
    }
}