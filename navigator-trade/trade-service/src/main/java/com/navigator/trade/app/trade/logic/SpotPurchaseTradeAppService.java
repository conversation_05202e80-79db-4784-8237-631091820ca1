package com.navigator.trade.app.trade.logic;

import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:34
 * @Version 1.0
 */
@Slf4j
@Service("ST_P_CONTRACT")
public class SpotPurchaseTradeAppService extends TradeAppAbstractService {

    @Autowired
    TTLogicService ttLogicService;

    @Autowired
    ContractSignLogicService contractSignLogicService;

    @Autowired
    ContractLogicService contractLogicService;


    @Override
    public List<TTQueryVO> createWarrantSalesContract(TTDTO ttdto) {return null;}

}
