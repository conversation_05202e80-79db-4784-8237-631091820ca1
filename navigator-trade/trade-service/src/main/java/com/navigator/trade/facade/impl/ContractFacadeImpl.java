package com.navigator.trade.facade.impl;

import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.trade.SignAppService;
import com.navigator.trade.app.trade.TradeAppService;
import com.navigator.trade.app.trade.TradeHandler;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.handler.SalesContractHandler;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import com.navigator.trade.pojo.vo.ContractVO;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IContractStructureService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.IContractService;
import com.navigator.trade.service.contract.IContractValueObjectService;
import com.navigator.trade.service.futrue.ICustomerFutureContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 合同对外暴露的接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@RestController
@Slf4j
public class ContractFacadeImpl implements ContractFacade {
    @Resource
    private IContractQueryService contractService;
    @Autowired
    private IContractOperationNewService salesContractOperationService;
    @Autowired
    private SalesContractHandler salesContractHandler;
    @Resource
    private IContractStructureService contractStructureService;
    @Resource
    private ITtPriceService ttPriceService;
    @Resource
    protected IContractValueObjectService contractValueObjectService;
    @Resource
    private ICustomerFutureContractService iCustomerFutureContractService;
    @Resource
    private TradeHandler tradeHandler;
    /**
     * 合同子域 读服务能力
     */
    @Resource
    private ContractQueryLogicService contractQueryLogicService;
    /**
     * 合同域，写服务能力，简单的直接走Domain
     */
    @Resource
    private ContractDomainService contractDomainService;
    /**
     * 合同域，写服务能力
     */
    @Resource
    private ContractLogicService contractLogicService;

    @Resource
    private SignAppService signAppService;

    /**
     * TODO 这个应该迁移到 sign
     *
     * @param contractBaseDTO
     * @return
     */
    @Override
    public Result confirmContract(ContractBaseDTO contractBaseDTO) {
        return Result.judge(signAppService.confirmContract(contractBaseDTO));
    }

    @Override
    public Result fillContract(ContractBaseDTO contractBaseDTO) {
        return Result.judge(contractLogicService.fillContract(contractBaseDTO));
    }

    @Override
    public ContractEntity getContractById(Integer id) {
        return contractQueryLogicService.getContractById(id);
    }

    @Override
    public ContractEntity getBasicContractById(Integer id) {
        return contractQueryLogicService.getBasicContractById(id);
    }

    @Override
    public ContractEntity getBasicContractByCode(String contractCode) {
        return contractQueryLogicService.getBasicContractByCode(contractCode);
    }

    @Override
    public List<ContractEntity> getContractByWarrantCode(String warrantCode) {
        return contractQueryLogicService.getContractByWarrantCode(warrantCode);
    }

    /**
     * 读服务实现 - 更新合同
     *
     * @param contractEntity
     * @return
     */
    @Override
    public Integer updateContract(ContractEntity contractEntity) {
        return contractDomainService.updateContractById(contractEntity) ? 1 : 0;
    }

    @Override
    public Integer updateAndBackUpContract(ContractBackUpDTO contractBackUpDTO) {
        return contractDomainService.updateContractById(contractBackUpDTO.getContractEntity(),
                contractBackUpDTO.getBackTradeType(),
                contractBackUpDTO.getReferCode()) ? 1 : 0;
    }

    @Override
    public Integer updateContractByCode(String contractCode, Integer contractStatus) {
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractByCode(contractCode);
        if (null == contractEntity) {
            return 0;
        }
        contractEntity.setStatus(contractStatus);
        return contractDomainService.updateContractById(contractEntity) ? 1 : 0;
    }

    @Override
    public boolean updateStructureContractPricingStatus(ContractStructureDTO contractStructureDTO) {
        return contractDomainService.updateStructureContractPricingStatus(contractStructureDTO);
    }


    @Override
    public Result queryContractStructure(QueryDTO<ContractBO> queryDTO) {
        return contractQueryLogicService.queryContractStructure(queryDTO);
    }

    @Override
    public Result queryContract(QueryDTO<ContractQO> queryDTO) {
        return contractQueryLogicService.queryContract(queryDTO);
    }

    @Override
    public Result pageContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO) {
        return contractQueryLogicService.pageContractsByDomainCode(queryDTO);
    }


    @Override
    public List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO) {
        return contractQueryLogicService.queryContractsByDomainCodeList(queryContractDTO);
    }

    @Override
    public Result queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO) {
        return contractQueryLogicService.queryContractsColumbus(queryDTO);
    }

    @Override
    public Result<ContractDetailVO> getContractByContractId(String contractId) {
        return contractQueryLogicService.getContractDetailById(contractId);
    }

    @Override
    public Result getBasicContractByContractId(String contractId) {
        return contractQueryLogicService.getBasicContractDetailById(contractId);
    }

    @Override
    public Result getContractByContractCode(String contractCode) {
        return contractQueryLogicService.getContractDetailByCode(contractCode);
    }

    @Override
    public Result getContractIdByCode(String contractCode) {
        return Result.success(contractQueryLogicService.getContractIdByCode(contractCode));
    }

    @Override
    public List<ContractStructureEntity> getValidStructureContract(List<Integer> contractIds) {
        return contractQueryLogicService.getValidStructureContract(contractIds);
    }

    @Override
    public Result getContractPdfs(String contractId) {
        return contractQueryLogicService.getContractPdfs(contractId);
    }

    @Override
    public Result canSign(String contractId) {
        return Result.judge(contractQueryLogicService.canSign(contractId));
    }

    /**
     * 改造到新的读服务
     *
     * @param contractId
     * @return
     */
    @Override
    public Result getContractUnitPriceDetail(String contractId) {
        return contractQueryLogicService.getContractUnitPriceDetail(contractId);
    }

    @Override
    public List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO) {
        return contractQueryLogicService.queryContractsFuturesNum(contractFuturesDTO);
    }

    @Override
    public List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO) {
        return contractQueryLogicService.queryContractsFutures(contractFuturesDTO);
    }

    @Override
    public Result createTtPrice(ConfirmPriceDTO confirmPriceDTO) {
        boolean ttPriceStatus = contractLogicService.createTtPrice(confirmPriceDTO);
        if (ttPriceStatus) {
            return Result.success("注意在LKG / ATLAS系统中完成回填均价动作", true);
        } else {
            return Result.failure();
        }
    }

    @Override
    public Result futureContracts(QueryDTO<ContractFuturesDTO> queryDTO) {
        return contractQueryLogicService.futureContracts(queryDTO);
    }

    @Override
    public Result getContractModifyLog(ContractModifyDTO modifyDTO) {
        return Result.success(contractQueryLogicService.getContractModifyLog(modifyDTO));
    }

    @Override
    public Result getContractModifyNumInfo(Integer contractId) {
        return Result.success(contractQueryLogicService.getContractModifyNumInfo(contractId));
    }

    @Override
    public Result getConfirmPriceList(Integer contractId) {
        return Result.success(contractQueryLogicService.getConfirmPriceList(contractId));
    }

    // TODO TT域提供改造或者进行迁移
    @Override
    public Result getConfirmPricedList(Integer contractId) {
        return Result.success(ttPriceService.getContractPricingList(contractId));
    }

    @Override
    public Result getTTPriceByContractId(Integer contractId) {
        return Result.success(ttPriceService.getTTPriceByContractId(contractId));
    }

    @Override
    public Result getConfirmPricedInfo(Integer contractId, Integer ttPriceId) {
        return Result.success(ttPriceService.getConfirmPricedInfo(contractId, ttPriceId));
    }

    @Override
    public Result saveConfirmPrice(TTPriceEntity ttPriceEntity) {
        return Result.success(ttPriceService.saveTtPrice(ttPriceEntity));
    }

    @Override
    public Result reviseContract(ContractModifyDTO contractModifyDTO) {
        // 采销统一个方法
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.spotContractModify(contractModifyDTO));
    }

    @Override
    public Result splitContract(ContractModifyDTO contractModifyDTO) {
        // 采销统一个方法
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.spotContractSplit(contractModifyDTO));
    }

    @Override
    public Result priceContract(SalesContractTTPriceDTO salesContractTTPriceDTO) {
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.contractPrice(salesContractTTPriceDTO));
    }

    @Override
    public Result transferMonthContract(ContractTransferDTO contractTransferDTO) {
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.contractTransferMonth(contractTransferDTO));
    }

    @Override
    public Result reversePriceContract(ContractTransferDTO contractTransferDTO) {
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.spotContractReversePrice(contractTransferDTO));
    }

    @Override
    public Result createSonContract(ContractTransferDTO contractTransferDTO) {
        ContractEntity contractEntity = contractTransferDTO.getContractEntity();
        ContractEntity sonContractEntity = null;
        if (null != contractEntity) {
            IContractService createService = salesContractHandler.getStrategy(
                    contractEntity.getSalesType(),
                    ContractTradeTypeEnum.NEW.getValue(),
                    contractEntity.getGoodsCategoryId());
            sonContractEntity = createService.createSonContract(contractTransferDTO);
        }
        return Result.success(sonContractEntity);
    }

    @Override
    public Result applyBuyBack(ContractBuyBackDTO contractBuyBackDTO) {
        // 采销统一个方法
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.contractBuyBack(contractBuyBackDTO));
    }

    @Override
    public Result applyWashOut(ContractWashOutDTO contractWashOutDTO) {
        // 采销统一个方法
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.spotContractWashOut(contractWashOutDTO));
    }

    @Override
    public Result applyClosed(Integer contractId) {
        // 采销统一个方法
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.ST.getValue());
        return Result.success(tradeAppService.spotContractClose(contractId));
    }

    @Override
    public Result applyAtlasClosed(String contractCode) {
        return Result.success(contractLogicService.warrantContractClose(contractCode));
    }

    @Override
    public Result applyWriteOff(ContractWriteOffDTO contractWriteOffDTO) {
        // 采购销售共用
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
        return Result.success(tradeAppService.warrantContractWriteOff(contractWriteOffDTO));
    }

    @Override
    public Result applySoyBean2WriteOff(ContractWriteOffOMDTO contractWriteOffOMDTO) {
        // 采购销售共用
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
        return Result.success(tradeAppService.warrantContractSoyBean2WriteOff(contractWriteOffOMDTO));
    }


    @Override
    public Result applyWriteOffWithDraw(ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        // 采购销售共用
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
        return Result.success(tradeAppService.warrantContractWriteOffWithdraw(contractWriteOffWithDrawDTO));
    }

    @Override
    public Result applyWarrantContractInvalid(Integer contractId) {
        // 采购销售共用
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
        return Result.success(tradeAppService.warrantContractInvalid(contractId));
    }

    /**
     * TODO 待作废
     *
     * @param contractId
     * @return
     */
    @Override
    public Result applyInvalid(Integer contractId) {
        return Result.success(salesContractOperationService.invalidContract(new ContractBaseDTO().setContractId(contractId)));
    }

    @Override
    public Result judgeCanModifyGoods(Integer contractId) {
        // contractService.judgeCanModifyGoods(contractId)
        return Result.success(true);
    }

    @Override
    public boolean prePareToContract(ContractEntity contractEntity) {
        return contractService.prePareToContract(contractEntity);
    }

    @Override
    public ContractTransferCountDTO getContractTransferNum(Integer customerId, Integer goodsCategoryId, String domainCode, Date deliveryEndTime, Integer category2) {
        return iCustomerFutureContractService.getContractTransferNum(customerId, goodsCategoryId, domainCode, deliveryEndTime, category2);
    }

    @Override
    public Result changeCustomerWhiteList(CustomerDetailDTO customerDetailDTO) {
        return Result.success(iCustomerFutureContractService.changeCustomerWhiteList(customerDetailDTO));
    }

    @Override
    public boolean addStructureRelease(Integer contractId, BigDecimal notDealNum) {
        return contractStructureService.addStructureRelease(contractId, notDealNum);
    }

    @Override
    public Integer getApplyIdByStructureContractId(Integer contractId) {
        return contractStructureService.getApplyIdByStructureContractId(contractId);
    }

    @Override
    public Integer getApplyIdByStructureContractCode(String contractCode) {
        return contractStructureService.getApplyIdByStructureContractCode(contractCode);
    }

    @Override
    public ContractStructureEntity getContractStructureById(Integer contractId) {
        return contractStructureService.getContractStructureById(contractId);
    }

    @Override
    public ContractStructureEntity getContractStructureVOById(Integer contractId) {
        return contractStructureService.getContractStructureVOById(contractId);
    }

    @Override
    public Result updateUnitPrice(String contractCode, BigDecimal unitPrice) {
        return contractService.updateUnitPrice(contractCode, unitPrice);
    }

    @Override
    public boolean updateStructureContract(ContractStructureEntity contractStructureEntity) {
        return contractDomainService.updateStructureContract(contractStructureEntity);
    }

    @Override
    public Result updateContractStatus(MultipartFile uploadFile, Integer status) {
        return contractDomainService.updateContractStatus(uploadFile, status);
    }

    @Override
    public List<TTPriceEntity> getTTPriceByApplyId(Integer priceApplyId) {
        return ttPriceService.getTTPriceByApplyId(priceApplyId);
    }

    @Override
    public TTPriceEntity getTTPriceByAllocateId(Integer allocateId) {
        return ttPriceService.getTTPriceByAllocateId(allocateId);
    }

    @Override
    public List<TTPriceEntity> getTTPriceBySourceId(Integer sourceId) {
        return ttPriceService.getTTPriceBySourceId(sourceId);
    }

    @Override
    public Boolean updateTTPriceById(TTPriceEntity ttPriceEntity) {
        return ttPriceService.updateTTPriceById(ttPriceEntity);
    }

    @Override
    public Result closeTailNumByContractId(Integer contractId, Integer triggerSys) {
        return Result.success(contractLogicService.closeTailNumByContractId(contractId, triggerSys));
    }

    @Override
    public Result batchCloseTailNum(List<Integer> contractIds, Integer triggerSys) {
        return Result.success(contractLogicService.batchCloseTailNum(contractIds, triggerSys));
    }

    @Override
    public Result cancelCloseTailNumByContractId(Integer contractId) {
        return Result.success(contractLogicService.cancelCloseTailNumByContractId(contractId));
    }

    @Override
    public Result batchCancelCloseTailNum(List<Integer> contractIds) {
        return Result.success(contractLogicService.batchCancelCloseTailNum(contractIds));
    }

    @Override
    public Result getCloseTailNumRecord(String contractCode) {
        return Result.success(contractQueryLogicService.getCloseTailNumRecord(contractCode));
    }

    @Override
    public Result closedBySiteCodeAndContractCode(String siteCode, String contractCode) {
        return Result.success(contractDomainService.closedBySiteCodeAndContractCode(siteCode, contractCode));
    }

    @Override
    public Result getDeliveryContractByContractId(Integer contractId) {
        return Result.success(contractQueryLogicService.getDeliveryContractByContractId(contractId));
    }

    @Override
    public List<String> judgeCategory3InProcessContractForSite(List<Integer> category3List, String siteCode) {
        return contractQueryLogicService.judgeCategory3InProcessContractForSite(category3List, siteCode);
    }

    @Override
    public Result<BigDecimal> getContractBlockedNum(String contractCode) {
        return Result.success(contractQueryLogicService.getContractBlockedNum(contractCode));
    }
    @Override
    public Result<ContractHistoryEntity> getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        return Result.success(contractQueryLogicService.getContractHistoryEntity(contractId, mainVersion));
    }

    @Override
    public Result<ContractMdmInfoDTO> getContractMdmInfo(String contractCode) {
        return Result.success(contractQueryLogicService.getContractMdmInfo(contractCode));
    }

    @Override
    public Result<Integer> getOriginalContractType(Integer contractId) {
        return Result.success(contractQueryLogicService.getOriginalContractType(contractId));
    }

    @Override
    public Result getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO) {
        return Result.success(contractQueryLogicService.getDeliveryApplyContractGroup(deliveryApplyContractQO));
    }

    @Override
    public Result getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO) {
        return Result.success(contractQueryLogicService.getDeliveryApplyContractList(deliveryApplyContractQO));
    }

    @Override
    public Result getDeliveryApplyContractListForAtlas(DeliveryApplyContractQO deliveryApplyContractQO) {
        return Result.success(contractQueryLogicService.getDeliveryApplyContractListForAtlas(deliveryApplyContractQO));
    }

    @Override
    public Result getContractListByIds(List<Integer> contractIdList) {
        return Result.success(contractQueryLogicService.getContractListByIds(contractIdList));
    }

    @Override
    public Result<List<ContractRelativeDTO>> getContractTraceList(Integer contractId) {
        return Result.success(contractQueryLogicService.getContractTraceList(contractId));
    }

    @Override
    public Result<List<ContractVO>> getCargoRightsContractById(Integer contractId) {
        return Result.success(contractQueryLogicService.getCargoRightsContractById(contractId));
    }
}
