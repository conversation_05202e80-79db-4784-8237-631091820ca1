package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerCreditPaymentFacade;
import com.navigator.customer.facade.CustomerDepositRateFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.customer.pojo.vo.CustomerDepositRateVO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.ReviseLogicService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReviseLogicServiceImpl implements ReviseLogicService {

    /**
     * 合同域
     */
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    SystemRuleFacade systemRuleFacade;
    @Autowired
    WarehouseFacade warehouseFacade;
    @Autowired
    CustomerInvoiceFacade customerInvoiceFacade;
    @Autowired
    IDeliveryTypeService iDeliveryTypeService;
    /**
     * 合同域查询通用
     */
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    /**
     * 域内服务调用
     */
    @Autowired
    private StructureLogicService structureLogicService;
    @Autowired
    private CommonLogicService commonLogicService;
    @Autowired
    private TTQueryDomainService ttQueryDomainService;
    /**
     * 跨域调用
     */
    @Autowired
    private TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    private CustomerDepositRateFacade customerDepositRateFacade;
    @Autowired
    private CustomerCreditPaymentFacade customerCreditPaymentFacade;
    @Autowired
    private QualityFacade qualityFacade;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 校验合同信息-修改交易
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        /******************************** 修改保存校验 ****************************************/

        // 保存拆分需要校验是否多次提交，其他条件不校验
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
            if (StringUtils.isNotBlank(saveTimes)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_SAVE_TT);
            }
            return;
        }

        /******************************** 普通修改校验 ****************************************/

        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        Integer customerId = contractModifyDTO.getCustomerId();
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            customerId = contractModifyDTO.getSupplierId();
        }

        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
        }

        // 判断客户是否可用
        if (!commonLogicService.isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }
        // 校验付款代码是否禁用
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(contractModifyDTO.getPayConditionId());
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
            if (!contractEntity.getBuCode().equals(payConditionEntity.getBuCode())) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_BUCODE);
            }
        }

        //3. 校验SKU是否有效
        SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
        if (skuEntity == null || DisableStatusEnum.DISABLE.getValue().equals(skuEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.GOODS_NOT_COMPLETED);
        }
        //4. 校验该SKU的品种是否绑定选定帐套
        SiteEntity siteEntity = siteFacade.getSiteDetailByCode(contractEntity.getSiteCode());
        if (siteEntity == null || !siteEntity.getCategory3().contains(contractEntity.getCategory3().toString())) {
            throw new BusinessException(ResultCodeEnum.GOODS_NOT_COMPLETED);
        }


        // 校验客户主数据赊销账期/预付款
        CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
        creditPaymentDTO.setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
                .setBuCode(contractEntity.getBuCode())
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setCompanyId(contractEntity.getCompanyId())
                .setIsSales(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                .setIsProcurement(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
        Result result = customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
        if (result.isSuccess()) {
            List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
            if (CollectionUtil.isNotEmpty(creditPaymentVOList)) {
                CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                if (contractModifyDTO.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                    String message = "最多可赊销{" + customerCreditPaymentVO.getCreditDays() + "}天";
                    throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_CREDIT_HAS_CHANGE, message);
                }
            } else {
                throw new BusinessException(ResultCodeEnum.TT_CREDIT_PAYMENT);
            }
        }
        //6. 校验业务配置-袋皮扣重配置是否有效
        if (StringUtils.isNotBlank(contractModifyDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractModifyDTO.getPackageWeight()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_PACKAGE_WEIGHT);
            }
        }

        // 9 . 二级品类=豆粕/豆油且基差基准价或蛋白价差超阈值超出了 备注的校验 TODO 后处理
        if (ObjectUtil.isEmpty(contractModifyDTO.getRemark()) && (GoodsCategoryEnum.OSM_OIL.getValue().equals(contractEntity.getCategory2())
                || GoodsCategoryEnum.OSM_MEAL.getValue().equals(contractEntity.getCategory2()))) {

            BasicPriceConfigQueryDTO priceConfigQueryDTO = new BasicPriceConfigQueryDTO();
            priceConfigQueryDTO.setGoodsId(contractModifyDTO.getGoodsId());
            priceConfigQueryDTO.setCategoryId(contractEntity.getCategory2());
            priceConfigQueryDTO.setFactoryCode(contractModifyDTO.getDeliveryFactoryCode());
            priceConfigQueryDTO.setCompanyId(contractEntity.getCompanyId().toString());
            priceConfigQueryDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(contractEntity.getDeliveryStartTime()));
            priceConfigQueryDTO.setDomainCode(contractEntity.getDomainCode());
            boolean extraPriceWarning = tradeDomainRemoteService.basePriceWarning
                    (priceConfigQueryDTO, contractModifyDTO.getPriceDetailDTO().getExtraPrice(), contractEntity.getSalesType());

            // 蛋白的校验
            priceConfigQueryDTO.setCompanyId(null);
            tradeDomainRemoteService.basePriceWarning
                    (priceConfigQueryDTO, contractModifyDTO.getPriceDetailDTO().getProteinDiffPrice(), contractEntity.getSalesType());
//            BigDecimal loaProtein = LOAProteinDiffPrice(tradeTicketEntity.getId(),tradeTicketEntity.getType());
//            if (LOAPriceWarning(tradeTicketEntity.getId()) || loaProtein.compareTo(BigDecimal.ZERO) != 0) {
//
//            }
        }


        //8. 校验通用配置-交提货方式配置是否有效
        if (ObjectUtil.isNotEmpty(contractModifyDTO.getDeliveryType())) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(contractModifyDTO.getDeliveryType());
            if (ObjectUtil.isEmpty(deliveryTypeEntity) || DisableStatusEnum.DISABLE.getValue().equals(deliveryTypeEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_DELIVERY);
            }
        }
        //9. 校验业务配置-重量验收是否有效
        if (StringUtils.isNotBlank(contractModifyDTO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractModifyDTO.getWeightCheck()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_WEIGHT);
            }
        }

        //10.TODO 校验通用配置-库点，库点属性 | 校验该库点是否绑定选定帐套 前端绑定了
        if (ObjectUtil.isNotEmpty(contractModifyDTO.getShipWarehouseId())) {
            Result<WarehouseEntity> warehouseEntityResult = warehouseFacade.getWarehouseById(Integer.valueOf(contractModifyDTO.getShipWarehouseId()));
            if (warehouseEntityResult.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(warehouseEntityResult.getData()), WarehouseEntity.class);
                if (null == warehouseEntity
                        || DisableStatusEnum.DISABLE.getValue().equals(warehouseEntity.getStatus())
                        || ObjectUtil.isEmpty(warehouseEntity.getSiteCodes())
                        || (ObjectUtil.isNotEmpty(warehouseEntity.getSiteCodes())
                        && contractModifyDTO.getSiteCode() != null && !warehouseEntity.getSiteCodes().contains(contractModifyDTO.getSiteCode()))) {
                    throw new BusinessException(ResultCodeEnum.TT_WAREHOUSE);
                }
            }
        }

        //校验质量指标
        QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        qualityInfoDTO
                .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                .setWarehouseId(Integer.valueOf(contractModifyDTO.getShipWarehouseId()))
                .setUsage(contractModifyDTO.getUsage())
                .setGoodsId(contractModifyDTO.getGoodsId())
                .setSalesType(contractEntity.getSalesType())
                .setCustomerId(customerId);
        // 特种油脂 TODO 修改的参数需完善
        // BUGFIX: 定价完成的时候前端不传企标类型
        // if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(contractEntity.getCategory2())) {
        if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(contractEntity.getCategory2()) && contractModifyDTO.getReviseType() == 1) {
            if ("国标".equals(contractModifyDTO.getStandardType()) || ObjectUtil.isEmpty(contractModifyDTO.getStandardRemark())) {
                qualityInfoDTO.setStandardType(contractModifyDTO.getStandardType());
            }
        }
        Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            throw new BusinessException(ResultCodeEnum.QUALITY_NOT_COMPLETED);
        }

        // 15.验证发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (customerInvoiceEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.TT_INVOICE);
        }

        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 Start
        // 16.校验履约保证金释放方式是否正确
        if (contractModifyDTO.getDepositReleaseType() == null || contractModifyDTO.getDepositReleaseType() == 0) {
            // 只校验现货
            if (BuCodeEnum.ST.getValue().equals(contractEntity.getBuCode())) {
                throw new BusinessException(ResultCodeEnum.TT_DEPOSIT_RELEASE_TYPE);
            }
        }
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 End

        // 校验合同类型
        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        switch (typeEnum) {
            case YI_KOU_JIA:
                // 一口价不支持修改类型
                if (!contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_MODIFY);
                }
                // 数量校验
                if (BigDecimalUtil.isGreaterThanZero(contractEntity.getTotalBillNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_BILL_NUM_EXCEPTION);
                }
                break;
            case JI_CHA:
                // 全部定价不支持修改基差暂定价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                    if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_CONFIRMED_ALL);
                    }
                }
                // 未全部定价不能修改一口价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_CONFIRMED_ALL);
                    }
                }
                break;
            case JI_CHA_ZAN_DING_JIA:
                // 数量校验
                if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                    if (BigDecimalUtil.isGreaterThanZero(contractEntity.getTotalBillNum()) || BigDecimalUtil.isGreaterThanZero(contractEntity.getAllocateNum())) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_BILL_NUM_EXCEPTION);
                    }
                }
                // 未全部定价不能修改一口价
                if (contractModifyDTO.getSonContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    if (!BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {
                        BigDecimal minNum = BigDecimalUtil.min(contractEntity.getTotalPriceNum(),
                                contractEntity.getContractNum().subtract(contractEntity.getTotalBillNum()),
                                contractEntity.getContractNum().subtract(contractEntity.getAllocateNum()));
                        if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), minNum)) {
                            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
                        }
                        break;
                    }
                }
                break;
            default:
                break;
        }
        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum()
                && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())
                && contractModifyDTO.getReviseType() == 1) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }
        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

        // 定价完成校验
        if (contractModifyDTO.getReviseType() == 2) {
            // 一个合同只能定价完成一次
            if (null != redisUtil.get(RedisConstants.CONTRACT_CONFIRM_COMPLETE_CODE + contractEntity.getContractCode())) {
                // 防止异常导致无法定价完成
                if (BigDecimalUtil.isEqual(contractEntity.getTotalPriceNum(), contractEntity.getContractNum())) {
                    throw new BusinessException(ResultCodeEnum.REPEATED_SUBMIT);
                }
            } else {
                redisUtil.set(RedisConstants.CONTRACT_CONFIRM_COMPLETE_CODE + contractEntity.getContractCode(), contractEntity.getContractCode());
            }
        }

        // 校验是否结构化定价中 前提条件： 1.变更主体 2.基差合同 3.销售合同
        if (!contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId())
                && ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()
                && contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            Boolean verified = structureLogicService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractEntity.getContractNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );
            if (Boolean.TRUE.equals(verified)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }

    }

    @Override
    public void modifyPriceComplete(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 基差修改一口价||暂定价||基差暂定价 履约保证金更新
        if (contractModifyDTO.getReviseType() == 2) {
            // NAV系统的履约保证金更新=NAV变更前履约保证金+NAV变更前履约保证金点价后补缴
            int depositRate = contractEntity.getDepositRate();
            int addedDepositRate = contractEntity.getAddedDepositRate();
            int newDepositRate = depositRate + addedDepositRate;

            // 查询客户的保证金配置是否存在
            Result result = customerDepositRateFacade.getCustomerDepositRateAddTT(new CustomerDepositRateBO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setContractType(contractModifyDTO.getSonContractType())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setCategory1(String.valueOf(contractEntity.getCategory1()))
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
                    .setBuCode(contractEntity.getBuCode())
            );
            if (result.getCode() == ResultCodeEnum.OK.code()) {
                List<CustomerDepositRateVO> customerDepositRateVOS = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerDepositRateVO.class);
                boolean exist = customerDepositRateVOS.stream().anyMatch(depositRateVO ->
                        depositRateVO.getDepositRate() == newDepositRate
                                && depositRateVO.getPricingDepositRate() == 0
                                && depositRateVO.getIsSales().equals(GeneralEnum.YES.getValue()));
                // 不存在则增加
                if (!exist) {
                    CustomerDepositRateDTO customerDepositRateDTO = new CustomerDepositRateDTO();
                    customerDepositRateDTO.setCustomerId(contractEntity.getCustomerId())
                            .setStatus(DisableStatusEnum.ENABLE.getValue())
                            .setCategoryId(contractEntity.getGoodsCategoryId())
                            .setIsSales(GeneralEnum.YES.getValue())
                            .setDepositRate(newDepositRate)
                            .setContractType(contractModifyDTO.getSonContractType())
                            .setPricingDepositRate(0);
                    customerDepositRateFacade.saveCustomerDepositRate(customerDepositRateDTO);
                }
            }
        }
    }

    /**
     * 更新父子合同数据
     *
     * @param contractEntity
     * @param contractModifyDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 判断合同主体是否发生修改 采购|销售
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        boolean isCustomerChanged = isSales ?
                !contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId()) :
                !contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId());

        if (isCustomerChanged) {
            contractEntity.setContractNum(BigDecimal.ZERO)
                    .setTotalAmount(BigDecimal.ZERO)
                    .setDepositAmount(BigDecimal.ZERO);
        } else {
            TTModifyEntity modifyEntity = ttQueryDomainService.getTTModifyEntityByTTId(contractModifyDTO.getTtId());
            if (ObjectUtil.isNotEmpty(modifyEntity)) {

                // 修改之前的交货工厂
                String beforeDeliveryFactoryCode = contractEntity.getDeliveryFactoryCode();
                // 合同的创建日期
                Date beforeCreateDate = contractEntity.getCreatedAt();

                BeanUtils.copyProperties(modifyEntity, contractEntity);
                contractEntity.setId(contractModifyDTO.getContractId())
                        .setPayConditionId(contractModifyDTO.getPayConditionId())
                        .setCreatedAt(beforeCreateDate);

                // 账套更新
                if (StringUtils.isNotBlank(contractModifyDTO.getSiteCode()) && !contractEntity.getSiteCode().equals(contractModifyDTO.getSiteCode())) {
                    SiteEntity siteEntity = siteFacade.getSiteDetailByCode(contractModifyDTO.getSiteCode());
                    contractEntity
                            .setSiteCode(siteEntity.getCode())
                            .setSiteName(siteEntity.getName())
                            .setBelongCustomerId(siteEntity.getBelongCustomerId())
                            .setDeliveryFactoryCode(siteEntity.getFactoryCode())
                            .setDeliveryFactory(siteEntity.getFactoryCode())
                            .setDeliveryFactoryName(siteEntity.getFactoryName())
                            .setCompanyId(siteEntity.getCompanyId())
                            .setCompanyName(siteEntity.getCompanyName());
                }

                // 交货工厂是否变更
                contractEntity.setIsChangeFactory(StringUtils.isNotBlank(contractModifyDTO.getDeliveryFactoryCode())
                        && !contractModifyDTO.getDeliveryFactoryCode().equals(beforeDeliveryFactoryCode) ? 1 : 0);

                // 商品更新
                if (contractModifyDTO.getGoodsId() != null && !contractModifyDTO.getGoodsId().equals(contractEntity.getGoodsId())) {
                    SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
                    contractEntity.setGoodsId(contractModifyDTO.getGoodsId())
                            .setGoodsCategoryId(skuEntity.getCategory2())
                            .setGoodsPackageId(skuEntity.getPackageId())
                            .setGoodsSpecId(skuEntity.getSpecId())
                            .setGoodsName(skuEntity.getFullName())
                            .setCategory1(skuEntity.getCategory1())
                            .setCategory2(skuEntity.getCategory2())
                            .setCategory3(skuEntity.getCategory3());
                    // 税率
                    if (skuEntity.getTaxRate() != null) {
                        BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                        contractEntity.setTaxRate(taxRate);

                        PriceDetailBO priceDetailBO = contractModifyDTO.getPriceDetailDTO();
                        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());
                        contractEntity.setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice());
                    }
                }

                // 商品昵称
                if (StringUtils.isNotBlank(contractModifyDTO.getCommodityName())) {
                    contractEntity.setCommodityName(contractModifyDTO.getCommodityName());
                }
            }
        }

        // 基差修改一口价直接进入生效中
        if (contractModifyDTO.getReviseType() == 2) {
            contractEntity.setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                    .setOriginContractType(ContractTypeEnum.JI_CHA.getValue());
            // 记录定价完成
            // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 Start
            // String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
            // redisUtil.set(priceStatus, 1);
            contractEntity.setIsPricingCompleted(1);
            // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 End

            // 基差修改一口价直接记录版本
            contractDomainService.updateContractById(contractEntity,
                    String.valueOf(ContractTradeTypeEnum.REVISE_NORMAL.getValue()), contractModifyDTO.getTtCode());
            return;
        }
        // 合同进入修改中
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        contractEntity.setUsage(contractModifyDTO.getUsage());
        contractDomainService.updateContractById(contractEntity);
    }

    @Override
    public void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 记录操作记录 采购|销售
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            commonLogicService.addContractOperationLog(contractEntity,
                    LogBizCodeEnum.REVISE_PURCHASE_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                    SystemEnum.MAGELLAN.getValue());
        } else {
            commonLogicService.addContractOperationLog(contractEntity,
                    LogBizCodeEnum.REVISE_SALES_CONTRACT, JSONUtil.toJsonStr(contractModifyDTO),
                    SystemEnum.MAGELLAN.getValue());
        }

    }


}
