package com.navigator.trade.facade.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.app.trade.TradeAppService;
import com.navigator.trade.app.trade.TradeHandler;
import com.navigator.trade.app.tt.domain.service.TTDomainService;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.facade.RedisFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.future.OilPriceDetailDTO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.*;
import com.navigator.trade.service.contract.IContractChangeEquityService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.service.tradeticket.impl.mock.TTModifyMockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.navigator.common.constant.RedisConstants.*;

@RestController
@Slf4j
public class TradeTicketFacadeImpl implements TradeTicketFacade {

    @Autowired
    private ITradeTicketQueryService tradeTicketQueryService;

    @Autowired
    private TTHandler TTHandler;

    @Autowired
    private TradeTicketDao tradeTicketDao;

    @Autowired
    TTModifyMockService ttModifyMockService;

    @Autowired
    private IContractChangeEquityService changeEquityService;

    @Autowired
    private IContractSignQueryService contractSignQueryService;

    @Resource
    private ContractFacade contractFacade;

    @Resource
    private CustomerFacade customerFacade;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisFacade redisFacade;

    @Resource
    private ContractSignFacade contractSignFacade;

    @Resource
    private TradeHandler tradeHandler;

    @Resource
    TTQueryLogicService ttQueryLogicService;

    @Resource
    private TTDomainService ttDomainService;

    @Resource
    private TTLogicService ttLogicService;

    @Override
    public Result createWarrantSalesContract(TTDTO ttdto) {
        // 路由到处理器
        TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
        // 创建
        List<TTQueryVO> ttQueryVOList = tradeAppService.createWarrantSalesContract(ttdto);
        if (ObjectUtil.isNotEmpty(ttQueryVOList.get(0).getInvalidReason())) {
            return Result.failure(ttQueryVOList.get(0).getInvalidReason());
        }
        return Result.success(ttQueryVOList);
    }


    /**
     * 保存TT数据
     * 在Web层已经进行了处理，用户录入的多个TT数据分别调用该方法
     *
     * @param ttdto
     * @return
     */
    @Override
    public Result saveTT(TTDTO ttdto) {
        // 新增/回购走重构接口
        if (TTTypeEnum.NEW.getType().equals(ttdto.getTtType()) || TTTypeEnum.BUYBACK.getType().equals(ttdto.getTtType())) {
            TradeAppService tradeAppService = tradeHandler.getStrategy(ttdto.getSalesType(), BuCodeEnum.ST.getValue());
            List<TTQueryVO> ttQueryVOList = tradeAppService.contractCreate(ttdto);
            return Result.success(ttQueryVOList);
        }

        // 新增结构化定价 TODO 走新的逻辑
        if (TTTypeEnum.STRUCTURE_PRICE.getType().equals(ttdto.getTtType())) {
            TradeAppService tradeAppService = tradeHandler.getStrategy(ttdto.getSalesType(), BuCodeEnum.ST.getValue());
            List<TTQueryVO> ttQueryVOList = tradeAppService.structureContractCreate(ttdto);
            return Result.success(ttQueryVOList);
        }

        // 拆分修改的保存需处理参数后再保存
        processModifySaveTT(ttdto);

        ITradeTicketService tradeTicketService = TTHandler.getStrategy(ttdto.getProcessorType());
        return tradeTicketService.saveTT(ttdto);
    }

    /**
     * 处理拆分/修改保存的TT
     *
     * @param ttdto TT数据
     */
    private void processModifySaveTT(TTDTO ttdto) {
        // 保存时，判断是否是新增的TT
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        if (salesContractAddTTDTO != null) {
            Integer ttId = salesContractAddTTDTO.getTtId();
            TradeTicketEntity tradeTicket = tradeTicketDao.getById(ttId);

            // 修改拆分的TT保存 - 对TT的修改
            if (tradeTicket != null && tradeTicket.getSourceType() != null
                    && tradeTicket.getSourceType() == SubmitTypeEnum.SAVE.getValue()) {

                // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变 Author: Mr 2024-05-14 Start
                // 保存校验信息是否改变
                checkModifyContent(tradeTicket.getCode(), tradeTicket.getContractCode(), ttId, SubmitTypeEnum.SAVE.getValue());
                // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变 Author: Mr 2024-05-14 End

                ttdto.setSubmitType(SubmitTypeEnum.SAVE.getValue());

                // 判断是否变更主体
                Optional<ContractEntity> contractEntity = Optional.ofNullable(contractFacade.getBasicContractById(tradeTicket.getContractId()));

                contractEntity.ifPresent(entity -> {
                    boolean isSalesType = tradeTicket.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue());
                    if (isSalesType) {
                        changeCustomerIfNecessary(salesContractAddTTDTO, ttdto, entity.getCustomerId());
                    } else {
                        changeSupplierIfNecessary(salesContractAddTTDTO, ttdto, entity.getSupplierId());
                    }
                });

                // 修改TT的内容
                if (tradeTicket.getType().equals(TTTypeEnum.REVISE.getType())) {
                    SalesContractReviseTTDTO contractReviseTTDTO = BeanConvertUtils.map(SalesContractReviseTTDTO.class, salesContractAddTTDTO);
                    contractReviseTTDTO.setSourceContractId(salesContractAddTTDTO.getContractId());

                    contractReviseTTDTO.setPaymentType(salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

                    CustomerDTO customerDTO = customerFacade.getCustomerById(salesContractAddTTDTO.getSupplierId());
                    if (customerDTO != null) {
                        contractReviseTTDTO.setSupplierName(customerDTO.getName());
                    }

                    contractReviseTTDTO.setDepositReleaseType(salesContractAddTTDTO.getDepositUseRule());
                    ttdto.setSalesContractReviseTTDTO(contractReviseTTDTO);
                } else if (tradeTicket.getType().equals(TTTypeEnum.SPLIT.getType())) {
                    SalesContractSplitTTDTO salesContractSplitTTDTO = BeanConvertUtils.map(SalesContractSplitTTDTO.class, salesContractAddTTDTO);
                    salesContractSplitTTDTO.setSourceContractId(salesContractAddTTDTO.getContractId());

                    salesContractSplitTTDTO.setPaymentType(salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

                    CustomerDTO customerDTO = customerFacade.getCustomerById(salesContractAddTTDTO.getSupplierId());
                    if (customerDTO != null) {
                        salesContractSplitTTDTO.setSupplierName(customerDTO.getName());
                    }

                    salesContractSplitTTDTO.setDepositReleaseType(salesContractAddTTDTO.getDepositUseRule());
                    ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
                }
            }
        }
    }

    private void changeCustomerIfNecessary(SalesContractAddTTDTO salesContractAddTTDTO, TTDTO ttdto, Integer expectedId) {
        if (!Objects.equals(salesContractAddTTDTO.getCustomerId(), expectedId)) {
            Integer originCustomerId = salesContractAddTTDTO.getCustomerId();
            salesContractAddTTDTO.setCustomerId(expectedId);
            ttdto.setChangeCustomerFlag(true);
            ttdto.setOriginCustomerId(originCustomerId);
        }
    }

    private void changeSupplierIfNecessary(SalesContractAddTTDTO salesContractAddTTDTO, TTDTO ttdto, Integer expectedId) {
        if (!Objects.equals(salesContractAddTTDTO.getSupplierId(), expectedId)) {
            Integer originCustomerId = salesContractAddTTDTO.getSupplierId();
            salesContractAddTTDTO.setSupplierId(expectedId);
            ttdto.setChangeCustomerFlag(true);
            ttdto.setOriginCustomerId(originCustomerId);
        }
    }

    @Override
    public TradeTicketDTO getTTDetailInfo(String ttCode){
        return tradeTicketQueryService.getTTDetailInfo(ttCode);
    }

    @Override
    public Result updateTT(TTDTO ttdto) {
        ITradeTicketService tradeTicketService = TTHandler.getStrategy(ttdto.getProcessorType());
        return tradeTicketService.updateTT(ttdto);
    }


    @Override
    public Result submitTT(SubmitTTDTO submitTTDTO) {

        log.info("SalesContractAddTTDTO:{}", JSON.toJSONString(submitTTDTO.getCreateTradeTicketDTO()));
        OMContractAddTTDTO createTradeTicketDTO = submitTTDTO.getCreateTradeTicketDTO();
        // 特殊TT类型处理
        Integer type = createTradeTicketDTO.getType();

        if (Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType(), TTTypeEnum.CLOSED.getType(), TTTypeEnum.INVALID.getType()).contains(type)) {
            type = TTTypeEnum.NEW.getType();
        }
        // TODO 去掉执行策略
        if (null != createTradeTicketDTO.getSalesType() && ObjectUtil.isNotEmpty(createTradeTicketDTO.getGoodsCategoryId())) {
            if (Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType(), TTTypeEnum.CLOSED.getType(), TTTypeEnum.INVALID.getType()).contains(type)) {
                type = TTTypeEnum.NEW.getType();
            }
            String processorType = TTHandlerUtil.getTTProcessor(createTradeTicketDTO.getSalesType(), type, Integer.valueOf(createTradeTicketDTO.getGoodsCategoryId()));
            log.info("processorType:{}", processorType);
            submitTTDTO.setTtProcessor(processorType);
        } else {
            log.info("processorType:{}", ProcessorTypeEnum.SBM_S_ADD.getTtValue());
            submitTTDTO.setTtProcessor(ProcessorTypeEnum.SBM_S_ADD.getTtValue());
        }
        log.info("ttProcessor:{}", submitTTDTO.getTtProcessor());

        // 拆分/修改的TT提交
        if (Objects.equals(submitTTDTO.getSubmitStatus(), "3") && createTradeTicketDTO != null) {
            return submitSaveTradeTicket(submitTTDTO, createTradeTicketDTO);
        }

        // TODO zengshl 走新的逻辑测试 未处理

        ITradeTicketService tradeTicketService = TTHandler.getStrategy(submitTTDTO.getTtProcessor());
        return tradeTicketService.submitTT(submitTTDTO);
    }

    /**
     * 提交拆分/修改的TT
     *
     * @param submitTTDTO
     * @param createTradeTicketDTO
     * @return
     */
    private Result submitSaveTradeTicket(SubmitTTDTO submitTTDTO, OMContractAddTTDTO createTradeTicketDTO) {
        String code = createTradeTicketDTO.getCode();
        OMContractAddTTDTO tradeTicketDTO = submitTTDTO.getCreateTradeTicketDTO();

        // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变 Author: Mr 2024-05-14 Start
        // 提交时的合同信息与修改前的合同信息是否一致
        checkModifyContent(code, tradeTicketDTO.getContractCode(), submitTTDTO.getTtId(), SubmitTypeEnum.SUBMIT.getValue());
        // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变 Author: Mr 2024-05-14 Ebd

        // 组装修改拆分的DTO
        ContractModifyDTO contractModifyDTO = getContractModifyDTO(tradeTicketDTO);

        // 调用修改拆分的接口
        Result result;
        if (createTradeTicketDTO.getType().equals(TTTypeEnum.REVISE.getType())) {
            result = contractFacade.reviseContract(contractModifyDTO);
        } else if (createTradeTicketDTO.getType().equals(TTTypeEnum.SPLIT.getType())) {
            result = contractFacade.splitContract(contractModifyDTO);
        } else {
            return Result.failure("不支持的交易票类型");
        }

        // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 Start
        // 更改逻辑前：保存 → TT001 | 保存提交 → TT002 → TT001 在提交完后覆盖新编号，但是由于没有覆盖完所有的场景
        // 更改逻辑后：保存 → TT001 | 保存提交 → TT001 在提交的时候使用保存的TT编号
        // 执行共同的业务逻辑
        // return processCommonLogic(result, tradeTicketDTO, code, createTradeTicketDTO.getType());

        // 删除预保存的TT
        if (tradeTicketDTO.getTtId() != null) {
            tradeTicketDao.deleteById(tradeTicketDTO.getTtId());
        }

        // 删除redis缓存的保存次数
        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + tradeTicketDTO.getContractCode());
        if (StringUtils.isNotBlank(saveTimes) && StringUtils.isNotBlank(tradeTicketDTO.getContractCode())) {
            redisUtil.del(CONTRACT_SAVE_TT_TIMES + tradeTicketDTO.getContractCode());
        }
        return result;
        // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 End
    }

    /**
     * 组装修改拆分的DTO
     *
     * @param tradeTicketDTO
     * @return
     */
    private ContractModifyDTO getContractModifyDTO(OMContractAddTTDTO tradeTicketDTO) {
        // submitTTDTO → contractModifyDTO
        ContractModifyDTO contractModifyDTO = BeanConvertUtils.map(ContractModifyDTO.class, tradeTicketDTO);

        // 如果是豆粕类别，从TT中获取关键信息，并设置到合同修改信息中
        KeyTradeInfoTTDTO keyTradeInfoTTDTO = tradeTicketDTO.getTtKernelDTOList().get(0);
        contractModifyDTO
                .setPriceDetailDTO(mapToPriceDetailBO(keyTradeInfoTTDTO.getPriceDetailDTO()))
                .setUnitPrice(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getUnitPrice()))
                .setPriceEndTime(keyTradeInfoTTDTO.getPriceEndTime())
                .setRemark(keyTradeInfoTTDTO.getMemo())
                .setModifyNum(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getContractNum()))
                .setDeliveryStartTime(keyTradeInfoTTDTO.getDeliveryStartTime())
                .setDeliveryEndTime(keyTradeInfoTTDTO.getDeliveryEndTime())
                .setDepositRate(keyTradeInfoTTDTO.getDepositRate())
                .setAddedDepositRate(StringUtils.isNotBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? Integer.valueOf(keyTradeInfoTTDTO.getAddedDepositRate()) : null)
                .setDepositAmount(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getDepositAmount()))
                .setInvoicePaymentRate(keyTradeInfoTTDTO.getInvoicePaymentRate())
                .setPayConditionId(StringUtils.isNotBlank(keyTradeInfoTTDTO.getPayConditionId()) ? Integer.valueOf(keyTradeInfoTTDTO.getPayConditionId()) : null);

        contractModifyDTO.setTtId(tradeTicketDTO.getTtId())
                .setTtCode(tradeTicketDTO.getCode())
                .setSonContractType(Integer.valueOf(tradeTicketDTO.getContractType()))
                .setPaymentType(tradeTicketDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 start
        contractModifyDTO.setDepositReleaseType(tradeTicketDTO.getDepositUseRule());
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 End

        // 定价单信息
        TradeTicketEntity tradeTicket = tradeTicketDao.getById(tradeTicketDTO.getTtId());
        if (tradeTicket != null && StringUtils.isNotBlank(tradeTicket.getConfirmPriceInfo())) {
            String confirmPriceInfo = tradeTicket.getConfirmPriceInfo();

            List<ConfirmPriceDTO> confirmPriceDTOS = JSON.parseArray(confirmPriceInfo, ConfirmPriceDTO.class);
            contractModifyDTO.setConfirmPriceDTOList(confirmPriceDTOS);
        }

        // 指定提交类型
        contractModifyDTO.setSubmitType(SubmitTypeEnum.SAVE_SUBMIT.getValue());
        return contractModifyDTO;
    }

    /**
     * 执行共同的业务逻辑
     *
     * @return
     */
    private Result processCommonLogic(Result result, OMContractAddTTDTO tradeTicketDTO, String code, Integer type) {
        if (!result.isSuccess()) {
            return result;
        }

        // 覆盖原TT同号
        List<TTQueryVO> ttQueryVOS = JSON.parseArray(JSON.toJSONString(result.getData()), TTQueryVO.class);

        for (TTQueryVO ttQueryVO : ttQueryVOS) {
            TradeTicketEntity tradeTicket = tradeTicketDao.getById(ttQueryVO.getTtId());
            if (tradeTicket == null) {
                continue;
            }

            // 修改主体特殊处理
            if (tradeTicket.getTradeType().equals(ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue())) {
                TradeTicketEntity originTradeTicket = tradeTicketDao.getByGroupId(tradeTicket.getGroupId(), tradeTicket.getId());
                tradeTicketDao.updateById(originTradeTicket.setCode(code));
                break;
            }

            // 修改覆盖新TT||拆分覆盖原TT
            if ((ttQueryVO.getSourceFlag() == 1 && type.equals(TTTypeEnum.SPLIT.getType())) ||
                    ttQueryVO.getSourceFlag() == 2 && type.equals(TTTypeEnum.REVISE.getType())) {

                String protocolCode = code.contains("-") ? code.substring(code.indexOf("-") + 1) : "000";

                // 更新TT编号
                tradeTicketDao.updateById(tradeTicket.setCode(code).setProtocolCode(protocolCode));

                //更新协议的编号
                ContractSignEntity signEntity = contractSignFacade.getContractSignDetailByTtId(tradeTicket.getId());
                contractSignFacade.updateById(signEntity
                        .setTtCode(code)
                        .setProtocolCode(protocolCode));

                // 前端页面展示
                ttQueryVO.setCode(code);
            }
        }

        // 删除预保存的TT
        if (tradeTicketDTO.getTtId() != null) {
            tradeTicketDao.deleteById(tradeTicketDTO.getTtId());
        }

        // 回滚dba_redis的数据
        String redisKeyPrefix = tradeTicketDTO.getCode().split("-")[0];
        redisFacade.rollBackRedis(redisKeyPrefix);

        // 删除redis缓存的保存次数
        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + tradeTicketDTO.getContractCode());
        if (StringUtils.isNotBlank(saveTimes) && StringUtils.isNotBlank(tradeTicketDTO.getContractCode())) {
            redisUtil.del(CONTRACT_SAVE_TT_TIMES + tradeTicketDTO.getContractCode());
        }

        return Result.success(ttQueryVOS);
    }

    // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变(重构原有方法) Author: Mr 2024-05-14 Start

    /**
     * 检查修改内容是否合同信息发生改变
     *
     * @param code         TT编码
     * @param contractCode 合同编码
     * @param ttId         TT的唯一标识符
     * @param submitType   提交类型
     * @throws BusinessException 如果TT代码的值与Redis中存储的值不相等，则抛出业务异常
     */
    private void checkModifyContent(String code, String contractCode, Integer ttId, Integer submitType) {

        String prefix = submitType == SubmitTypeEnum.SAVE.getValue() ? CONTRACT_SAVE_TT_SAVE : CONTRACT_SAVE_TT_SUBMIT;

        // 防止重复提交
        String saveSubmit = redisUtil.getString(prefix + code);
        if (StringUtils.isNotBlank(saveSubmit)) {
            throw new BusinessException(ResultCodeEnum.REPEATED_SUBMIT);
        }
        // 过期时间，以秒为单位。此处设置为一分钟（60秒）。
        redisUtil.set(prefix + code, "1", 60);

        // 根据合同编号查询TT列表
        List<TradeTicketEntity> ticketEntityList = tradeTicketDao.getTradeTicketEntityByContractCode(contractCode);
        if (CollectionUtil.isNotEmpty(ticketEntityList)) {
            // 获取最新一条TT
            TradeTicketEntity tradeTicketEntity = ticketEntityList.get(ticketEntityList.size() - 1);

            // 如果编号不一致则认为是更改合同信息（暂未考虑撤回情况）
            if (!tradeTicketEntity.getCode().equals(code)) {
                // 删除当前的TT
                if (ttId != null) {
                    tradeTicketDao.deleteById(ttId);
                }

                // 删除redis
                if (StringUtils.isNotBlank(contractCode)) {
                    redisUtil.del(CONTRACT_SAVE_TT_TIMES + contractCode);
                }
                throw new BusinessException(ResultCodeEnum.TT_MODIFY_NOT_EQUAL);
            }
        }
    }
    // BUGFIX：case-1002581 定价单含税单价错误-提交拆分/修改保存TT校验合同信息是否改变(重构原有方法) Author: Mr 2024-05-14 End

    /**
     * 将交易信息DTO映射为价格详情BO。
     *
     * @param tradeInfoDTO 交易信息的数据传输对象，可以是不同类型的具体实现。
     * @return PriceDetailBO 价格详情业务对象，根据传入的tradeInfoDTO类型不同，映射自不同的DTO。
     * @throws IllegalStateException 如果传入的tradeInfoDTO类型未被支持，则抛出异常。
     */
    private PriceDetailBO mapToPriceDetailBO(Object tradeInfoDTO) {
        // 如果tradeInfoDTO是KeyTradeInfoTTDTO类型的实例
        if (tradeInfoDTO instanceof PriceDetailDTO) {
            // 通过BeanConvertUtils将KeyTradeInfoTTDTO内的PriceDetailDTO映射为PriceDetailBO
            return BeanConvertUtils.map(PriceDetailBO.class, tradeInfoDTO);
        } else if (tradeInfoDTO instanceof OilPriceDetailDTO) {
            // 如果tradeInfoDTO是OilKeyTradeInfoTTDTO类型的实例，将OilKeyTradeInfoTTDTO内的OilPriceDetailDTO映射为PriceDetailBO
            return BeanConvertUtils.map(PriceDetailBO.class, tradeInfoDTO);
        }
        // 如果tradeInfoDTO不是任何已知类型的实例，抛出异常
        throw new IllegalStateException("Unsupported trade info DTO type: " + tradeInfoDTO.getClass().getName());
    }

    @Override
    public Result submitTTBatch(SubmitTTBatchDTO submitTTBatchDTO) {
        List<Integer> ttIdList = submitTTBatchDTO.getTtIdList();
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getList(ttIdList, null);
        Map<Integer, List<TradeTicketEntity>> map = tradeTicketEntityList.stream().collect(Collectors.groupingBy(TradeTicketEntity::getType));
        SubmitTTVO submitTTVO = new SubmitTTVO();
        List<Integer> successList = new ArrayList<>();
        List<Integer> failedList = new ArrayList<>();
        List<TTQueryVO> list = new ArrayList<>();
        Integer successListSize = 0;
        Integer failedListSize = 0;
        List<Integer> failedByCompleteList = new ArrayList<>();
        List<Integer> failedByApproveList = new ArrayList<>();
        List<Integer> failedByGoodList = new ArrayList<>();
        List<Integer> failedByQualityList = new ArrayList<>();
        List<Integer> failedByPayConditionList = new ArrayList<>();
        //CaseId-1002453: RR status in navigator，Author By NaNa
        List<Integer> failedByResidualRiskList = new ArrayList<>();
        String processorType = null;
        for (Map.Entry<Integer, List<TradeTicketEntity>> entry : map.entrySet()) {
            if (null != submitTTBatchDTO.getSalesType() && StringUtils.isNotBlank(submitTTBatchDTO.getGoodsCategoryId())) {
                processorType = TTHandlerUtil.getTTProcessor(submitTTBatchDTO.getSalesType(), entry.getKey(), Integer.valueOf(submitTTBatchDTO.getGoodsCategoryId()));
            } else {
                processorType = ProcessorTypeEnum.SBM_S_ADD.getTtValue();
            }
            ITradeTicketService tradeTicketService = TTHandler.getStrategy(processorType);
            List<Integer> idList = entry.getValue().stream().map(TradeTicketEntity::getId).collect(Collectors.toList());
            submitTTBatchDTO.setTtIdList(idList);
            //CaseId-1002453: RR status in navigator，Author By NaNa
            submitTTBatchDTO.setResidualRiskForceSubmit(true);
            //批量提交
            SubmitTTVO result = tradeTicketService.submitTTBatch(submitTTBatchDTO);
            successList.addAll(result.getSuccessList());
            failedList.addAll(result.getFailedList());
            failedByCompleteList.addAll(result.getFailedByCompleteList());
            failedByApproveList.addAll(result.getFailedByApproveList());
            failedByGoodList.addAll(result.getFailedByGoodList());
            failedByQualityList.addAll(result.getFailedByQualityList());
            failedByPayConditionList.addAll(result.getFailedByPayConditionList());
            //CaseId-1002453: RR status in navigator，Author By NaNa
            failedByResidualRiskList.addAll(result.getFailedByResidualRiskList());
            successListSize = successListSize + result.getSuccessListSize();
            failedListSize = failedListSize + result.getFailedListSize();
            list.addAll(result.getList());
        }
        submitTTVO
                .setSuccessList(successList)
                .setFailedList(failedList)
                .setSuccessListSize(successListSize)
                .setFailedListSize(failedListSize)
                .setFailedByCompleteList(failedByCompleteList)
                .setFailedByApproveList(failedByApproveList)
                .setFailedByGoodList(failedByGoodList)
                .setFailedByQualityList(failedByQualityList)
                .setFailedByPayConditionList(failedByPayConditionList)
                //CaseId-1002453: RR status in navigator，Author By NaNa
                .setFailedByResidualRiskList(failedByResidualRiskList)
                .setList(list)
        ;

        return Result.success(submitTTVO);
    }

    @Override
    public Result approveTT(ApprovalDTO approvalDTO) {
        log.error("=======================================");
        log.error("=======================================");
        log.error("===================TradeTicketFacadeImpl.approveTT====================");
        log.error(JSON.toJSONString(approvalDTO));
        // 权益变更审批
        if (StrUtil.isNotBlank(approvalDTO.getEquityApplyCode())) {
            return Result.success(changeEquityService.approveEquityChange(approvalDTO));
        }

        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(approvalDTO.getTtCode());
        if (null == tradeTicketEntity) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }

        Integer ttType = tradeTicketEntity.getType();
        ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
        // todo 临时代码 转让/分配/新增 走领域审批
        ArrayList<Integer> ttTypes = Lists.newArrayList(TTTypeEnum.NEW.getType(),
                TTTypeEnum.ALLOCATE.getType(),
                TTTypeEnum.ASSIGN.getType());
        if (ttTypes.contains(ttType)) {
            TradeAppService tradeAppService = tradeHandler.getStrategy(ContractSalesTypeEnum.SALES.getValue(), BuCodeEnum.WT.getValue());
            tradeAppService.approveTT(approvalDTO, ttType);
        } else {
            tradeTicketService.approveTT(approvalDTO, ttType);
        }

        return Result.success();
    }

    @Override
    public Result batchApproveTT(List<ApprovalDTO> approvalDTOList) {
        log.error("=======================================");
        log.error("=======================================");
        log.error("===================TradeTicketFacadeImpl.batchApproveTT====================");
        log.error(JSON.toJSONString(approvalDTOList));

        // 权益变更审批
        List<ApprovalDTO> equityApplyList = approvalDTOList.stream()
                .filter(approvalDTO -> StrUtil.isNotBlank(approvalDTO.getEquityApplyCode()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ApprovalDTO::getEquityApplyCode))), ArrayList::new));

        // tt审批
        List<ApprovalDTO> ttApplyList = approvalDTOList.stream().filter(approvalDTO -> StrUtil.isBlank(approvalDTO.getEquityApplyCode())).collect(Collectors.toList());

        equityApplyList.addAll(ttApplyList);

        for (ApprovalDTO approvalDTO : equityApplyList) {
            approveTT(approvalDTO);
        }
        return Result.success();
    }

    @Override
    public Result deleteTT(Integer ttId) {
        ttLogicService.deleteTT(ttId);
//        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
//        ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
//        tradeTicketService.deleteTT(ttId);
        return Result.success();
    }

    @Override
    public Result cancelTT(OperateTTDTO operateTTDTO) {
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(operateTTDTO.getTtId());
        ContractSignEntity contractSignEntity = contractSignQueryService.getContractSignDetailById(tradeTicketEntity.getSignId());
        //case ：1002520 结构化定价和新增TT待回签不允许撤回，修改TT、拆分TT、回购TT、关闭TT、解约定陪TT、回签状态下允许撤回，注销
        if ((TTTypeEnum.NEW.getType().equals(tradeTicketEntity.getType()) && null != contractSignEntity && ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus())
                || (TTTypeEnum.STRUCTURE_PRICE.getType().equals(tradeTicketEntity.getType()) && null != contractSignEntity && ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus())
                || (!TTTypeEnum.ASSIGN.getType().equals(tradeTicketEntity.getType()) && null != contractSignEntity && ContractSignStatusEnum.WAIT_BACK.getValue() < contractSignEntity.getStatus())
                || (TTTypeEnum.ASSIGN.getType().equals(tradeTicketEntity.getType()) && null != contractSignEntity && ContractSignStatusEnum.WAIT_CONFIRM.getValue() < contractSignEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
        }
        // 仓单注销撤回做一个TT业务的判断
//        String ttProcessor = TTHandlerUtil.getTTProcessor(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
//        ITradeTicketService tradeTicketService = TTHandler.getStrategy(ttProcessor);
//        tradeTicketService.cancelTT(operateTTDTO);
        // TODO 走新的撤回逻辑
        ttLogicService.cancelTT(operateTTDTO);
        return Result.success();
    }

    /**
     * 转月/反点价/点价/撤回
     *
     * @param contraryPriceDTO
     * @return
     */
    @Override
    public Result contraryPrice(ContraryPriceDTO contraryPriceDTO) {

        // TODO 走新的逻辑
        return  ttLogicService.contraryPrice(contraryPriceDTO);
//        Integer ttType;
//        if (PriceTypeEnum.TRANSFER_MONTH.getValue() == contraryPriceDTO.getPriceApplyType()) {
//            ttType = TTTypeEnum.TRANSFER.getType();
//        } else if (PriceTypeEnum.REVERSE_PRICING.getValue() == contraryPriceDTO.getPriceApplyType()) {
//            ttType = TTTypeEnum.REVERSE_PRICE.getType();
//        } else {
//            ttType = TTTypeEnum.PRICE.getType();
//        }
//        ITradeTicketService tradeTicketService = TTHandler.getStrategy(contraryPriceDTO.getSalesType(), ttType, contraryPriceDTO.getCategoryId());
//        return tradeTicketService.contraryPrice(contraryPriceDTO);
    }

    @Override
    public Result invalidTT(OperateTTDTO operateTTDTO) {
        //TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(operateTTDTO.getTtId());
        //ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
        //tradeTicketService.invalidTT(operateTTDTO);
        // TODO 走新的做法逻辑
        ttLogicService.invalidTT(operateTTDTO);
        return Result.success();
    }

    @Override
    public Result queryTTList(@RequestBody QueryDTO<TTQueryDTO> TTQueryDTO) {
        return ttQueryLogicService.queryTTList(TTQueryDTO);
    }

    @Override
    public Result queryTTDetail(@PathVariable Integer ttId) {
        log.info("queryTTDetail.ttId:{}", JSON.toJSONString(ttId));
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        log.info("queryTTDetail.tradeTicketEntity:{}", JSON.toJSONString(tradeTicketEntity));
        ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
        TTDetailVO ttDetailVO = null;
        ttDetailVO = ttQueryLogicService.queryTTDetail(ttId, TTTypeEnum.getByType(tradeTicketEntity.getType()));

//        // todo 临时按照TT类型路由处理
//        ArrayList<Integer> ttTypes = Lists.newArrayList(TTTypeEnum.NEW.getType(),
//                TTTypeEnum.ALLOCATE.getType(),
//                TTTypeEnum.ASSIGN.getType(),
//                TTTypeEnum.WRITE_OFF.getType());
//        if (ttTypes.contains(tradeTicketEntity.getType())) {
//            ttDetailVO = ttQueryLogicService.queryTTDetail(ttId, TTTypeEnum.getByType(tradeTicketEntity.getType()));
//        } else {
//            ttDetailVO = tradeTicketService.queryTTDetail(ttId, tradeTicketEntity);
//        }
        ttDetailVO.setTradeTicketEntity(tradeTicketEntity);
        return Result.success(ttDetailVO);
    }

    @Override
    public SignTemplateDTO getSignTemplateInfo(Integer ttId) {
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
        return tradeTicketService.getSignTemplateDTO(ttId);
    }

    @Override
    public Result complete(Integer contractId, Integer ttId) {
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        ITradeTicketService tradeTicketService = getTradeTicketService(tradeTicketEntity);
        return tradeTicketService.complete(contractId, ttId);
    }

    @Override
    public Result queryTTReport(ReportDTO reportDTO) {
        List<ReportVO> reportVOList = tradeTicketQueryService.queryTTReport(reportDTO);
        TTReportVO ttReportVO = new TTReportVO();
        Map<Integer, List<ReportVO>> contractTypeMap = reportVOList.stream().collect(Collectors.groupingBy(ReportVO::getContractType));
        BigDecimal unsubmitTotalSize = reportVOList.stream().map(ReportVO::getUnsubmitSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal approvingTotalSize = reportVOList.stream().map(ReportVO::getApprovingSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal doneTotalSize = reportVOList.stream().map(ReportVO::getDoneSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal invalidTotalSize = reportVOList.stream().map(ReportVO::getInvalidSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPrice = unsubmitTotalSize.add(approvingTotalSize).add(doneTotalSize).setScale(0, RoundingMode.HALF_UP);
        ttReportVO
                .setYiReportVOList(contractTypeMap.get(ContractTypeEnum.YI_KOU_JIA.getValue()))
                .setJiReportVOList(contractTypeMap.get(ContractTypeEnum.JI_CHA.getValue()))
                .setZanReportVOList(contractTypeMap.get(ContractTypeEnum.ZAN_DING_JIA.getValue()))
                .setUnsubmitTotalSize(unsubmitTotalSize)
                .setApprovingTotalSize(approvingTotalSize)
                .setDoneTotalSize(doneTotalSize)
                .setInvalidTotalSize(invalidTotalSize)
                .setTotalSize(totalPrice)
        ;

        return Result.success(ttReportVO);
    }

    @Override
    public Result queryTotalReport(ReportDTO reportDTO) {
        TotalReportVO totalReportVO = new TotalReportVO();
        List<ReportVO> reportVOList = tradeTicketQueryService.queryTTReport(reportDTO);
        Map<Integer, List<ReportVO>> contractTypeMap = reportVOList.stream().collect(Collectors.groupingBy(ReportVO::getContractType));
        List<ReportVO> yiReportVOList = contractTypeMap.get(ContractTypeEnum.YI_KOU_JIA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.YI_KOU_JIA.getValue());
        List<ReportVO> jiReportVOList = contractTypeMap.get(ContractTypeEnum.JI_CHA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.JI_CHA.getValue());
        List<ReportVO> zanReportVOList = contractTypeMap.get(ContractTypeEnum.ZAN_DING_JIA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.ZAN_DING_JIA.getValue());
        TTReportVO yiReportVO = getReportVO(yiReportVOList);
        TTReportVO jiReportVO = getReportVO(jiReportVOList);
        TTReportVO zanReportVO = getReportVO(zanReportVOList);
        List<String> collect = reportVOList.stream().sorted(Comparator.comparing(ReportVO::getDeliveryStartTime)).map(ReportVO::getDeliveryStartTime).distinct().collect(Collectors.toList());
        totalReportVO
                .setYiReportVO(yiReportVO)
                .setJiReportVO(jiReportVO)
                .setZanReportVO(zanReportVO)
                .setDeliveryTimeList(collect)
        ;

        return Result.success(totalReportVO);
    }

    @Override
    public Result generateTTByContractId(List<Integer> idList) {
        return tradeTicketQueryService.generateTTByContractId(idList);
    }

    @Override
    public Result generateTT(Integer idStart, Integer idEnd) {
        List<Integer> idList = new ArrayList<>();
        for (int i = idStart; i < idEnd + 1; i++) {
            idList.add(i);
        }
        return generateTTByContractId(idList);
    }

    @Override
    public Result queryListByContractCode(String contractCode) {
        return Result.success(tradeTicketQueryService.queryListByContractCode(contractCode));
    }

    private TTReportVO getReportVO(List<ReportVO> list) {
        TTReportVO ttReportVO = new TTReportVO();
        BigDecimal unsubmitTotalSize = list.stream().map(ReportVO::getUnsubmitSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal approvingTotalSize = list.stream().map(ReportVO::getApprovingSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal doneTotalSize = list.stream().map(ReportVO::getDoneSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal invalidTotalSize = list.stream().map(ReportVO::getInvalidSize).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPrice = unsubmitTotalSize.add(approvingTotalSize).add(doneTotalSize).setScale(0, RoundingMode.HALF_UP);
        ttReportVO
                .setReportVOList(list)
                .setUnsubmitTotalSize(unsubmitTotalSize)
                .setApprovingTotalSize(approvingTotalSize)
                .setDoneTotalSize(doneTotalSize)
                .setInvalidTotalSize(invalidTotalSize)
                .setTotalSize(totalPrice)
        ;
        return ttReportVO;
    }

    private ITradeTicketService getTradeTicketService(TradeTicketEntity tradeTicketEntity) {
        String processorType = TTHandlerUtil.getTTProcessor(tradeTicketEntity.getSalesType(), tradeTicketEntity.getType(), tradeTicketEntity.getSubGoodsCategoryId());
        log.info("ttProcessorType:{}", JSON.toJSONString(processorType));
        ITradeTicketService strategy = TTHandler.getStrategy(processorType);
        log.info("ttStrategy:{}", JSON.toJSONString(strategy));
        return strategy;
    }

    @Override
    public Result getTTStat(StatQueryDTO statQueryDTO) {
        TTAllStatusNumVO ttAllStatusNumVO = tradeTicketQueryService.getTTStat(statQueryDTO);
        return Result.success(ttAllStatusNumVO);
    }

    @Override
    public Result queryModifyLog(String contractCode) {
        List<ContractModifyLogVO> contractModifyLogVOList = tradeTicketQueryService.queryModifyLog(contractCode);
        return Result.success(contractModifyLogVOList);
    }

    @Override
    public Result getDestination() {
        List<String> list = tradeTicketQueryService.getDestination();
        return Result.success(list);
    }


    /**
     * 新增销售/采购/结构化定价合同时，调用此方法
     * TODO 没有被调用
     * @param omContractAddTTDTO
     * @return
     */
    private List<TradeTicketDTO> convertAddTradeTicketDTO(OMContractAddTTDTO omContractAddTTDTO) {
        List<TradeTicketDTO> tradeTicketDTOList = new ArrayList<>();

        String strGoodsCategoryId = omContractAddTTDTO.getGoodsCategoryId();
        //默认豆粕
        Integer subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        if (null != strGoodsCategoryId && strGoodsCategoryId.trim().length() > 0) {
            subGoodsCategoryId = Integer.valueOf(strGoodsCategoryId);
        }
        Integer goodsCategoryId = GoodsCategoryEnum.getByValue(subGoodsCategoryId).getParentValue();

        ContractTradeTypeEnum tradeTypeEnum = null == omContractAddTTDTO.getTradeType() ? ContractTradeTypeEnum.NEW : ContractTradeTypeEnum.getByValue(omContractAddTTDTO.getTradeType());

        TradeTicketDTO mainTradeTicketDTO = new TradeTicketDTO();
        mainTradeTicketDTO.setId(omContractAddTTDTO.getTtId())
                .setContractCode(omContractAddTTDTO.getContractCode())
                .setContractId(omContractAddTTDTO.getContractId())
                .setCode(omContractAddTTDTO.getCode())
                .setType(tradeTypeEnum.getTTType().getType())
                .setContractType(Integer.valueOf(omContractAddTTDTO.getContractType()))
                .setStatus(omContractAddTTDTO.getStatus())
                .setApprovalStatus(omContractAddTTDTO.getApprovalStatus())
                .setApprovalType(omContractAddTTDTO.getApprovalType())
                .setContractStatus(ContractStatusEnum.DRAFT.getValue()) //默认：草稿
                //.setContractSignatureStatus()
                .setOperationSource(OperationSourceEnum.EMPLOYEE.getValue()) //默认：员工操作
                .setContractSource(omContractAddTTDTO.getContractSource())
                .setTradeType(tradeTypeEnum.getValue())
                .setOwnerId(Integer.valueOf(omContractAddTTDTO.getOwnerId()))
                .setSalesType(omContractAddTTDTO.getSalesType())
                .setInvalidReason("")
                .setIsDeleted(0)
                //.setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))  //后置逻辑
                //.setCreatedAt()
                //.setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId())
                //.setUpdatedAt()
                .setProtocolCode("")
                //.setSignId()
                .setGoodsCategoryId(goodsCategoryId)
                .setSubGoodsCategoryId(subGoodsCategoryId)
                .setCustomerId(Integer.valueOf(omContractAddTTDTO.getCustomerId()))
                .setCustomerCode(omContractAddTTDTO.getCustomerCode())
                .setCustomerName(omContractAddTTDTO.getCustomerName())
                .setSupplierId(Integer.valueOf(omContractAddTTDTO.getSupplierId()))
                .setSupplierCode("")
                .setSupplierName("")
                .setBankId(omContractAddTTDTO.getSupplierAccountId())
                .setDomainCode(omContractAddTTDTO.getDomainCode());

        for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {

            String domainCode = keyTradeInfoTTDTO.getDomainCode();

            PriceDetailDTO priceDetailDTO = keyTradeInfoTTDTO.getPriceDetailDTO();
            ContractPriceBaseEntity contractPriceBaseEntity = new ContractPriceBaseEntity();
            if (null != priceDetailDTO) {
                contractPriceBaseEntity = new ContractPriceBaseEntity(priceDetailDTO);
            }

            ContractAddTTDTO contractAddTTDTO = (ContractAddTTDTO) new ContractAddTTDTO()
                    .setContractId(omContractAddTTDTO.getContractId())
                    .setContractCode(omContractAddTTDTO.getContractCode())
                    .setRootContractId(omContractAddTTDTO.getRootContractId())
                    .setStatus(TTStatusEnum.NEW.getType())
                    .setContractType(Integer.valueOf(omContractAddTTDTO.getContractType()))
                    .setSupplierAccount(String.valueOf(omContractAddTTDTO.getSupplierAccountId()))
                    .setSignDate(omContractAddTTDTO.getSignDate())
                    .setSignPlace(omContractAddTTDTO.getSignPlace())
                    .setDomainCode(keyTradeInfoTTDTO.getDomainCode())
                    //.setGoodsId()
                    //.setGoodsName()
                    .setUnit(UnitEnum.TON.name())
                    .setGoodsPackageId(Integer.valueOf(omContractAddTTDTO.getGoodsPackageId()))
                    .setGoodsSpecId(Integer.valueOf(omContractAddTTDTO.getGoodsSpecId()))
                    .setCreditDays(omContractAddTTDTO.getCreditDays())
                    .setNeedPackageWeight(omContractAddTTDTO.getNeedPackageWeight())
                    .setPackageWeight(omContractAddTTDTO.getPackageWeight())
                    //.setQualityCheck()    //客户选项
                    .setCurrencyType(CurrencyTypeEnum.CNY.getDesc())
                    .setUnitPrice(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getUnitPrice()))
                    .setContractNum(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getContractNum()))
                    //.setFobUnitPrice()    //计算
                    //.setTaxRate()         //发票->
                    //.setInvoiceType()     //客户->发票类型
                    //.setCifUnitPrice()    //计算
                    //.setTemporaryPrice()  //每日不同，使用时取值
                    //.setTransactionPrice()
                    //.setTotalAmount()     //计算
                    .setExtraPrice(contractPriceBaseEntity.getExtraPrice())
                    .setCreditDays(omContractAddTTDTO.getCreditDays())
                    .setPaymentType(omContractAddTTDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType())
                    .setDepositAmount(BigDecimalUtil.parseZero(keyTradeInfoTTDTO.getDepositAmount()))
                    //.setAddedDepositAmount()      //计算
                    .setDepositRate(keyTradeInfoTTDTO.getDepositRate())
                    .setInvoicePaymentRate(keyTradeInfoTTDTO.getInvoicePaymentRate())
                    .setDepositUseRule(omContractAddTTDTO.getDepositUseRule())
                    .setDelayPayFine(BigDecimalUtil.parseZero(omContractAddTTDTO.getDelayPayFine()))
                    .setOem(omContractAddTTDTO.getOem())
                    .setDeliveryStartTime(keyTradeInfoTTDTO.getDeliveryStartTime())
                    .setDeliveryEndTime(keyTradeInfoTTDTO.getDeliveryEndTime())
                    .setDeliveryType(omContractAddTTDTO.getDeliveryType())
                    .setDeliveryFactoryCode(omContractAddTTDTO.getDeliveryFactoryCode())
                    .setDeliveryFactoryName(omContractAddTTDTO.getDeliveryFactoryName())
                    .setDestination(omContractAddTTDTO.getDestination())
                    .setShipWarehouseId(Integer.valueOf(omContractAddTTDTO.getShipWarehouseId()))
                    //.setIsArrangeTransport()   //TBD
                    .setWeightCheck(omContractAddTTDTO.getWeightCheck())
                    .setWeightTolerance(omContractAddTTDTO.getWeightTolerance())
                    .setPriceStartTime(DateTimeUtil.parseTimeStamp0000(omContractAddTTDTO.getPriceStartTime()))
                    //.setPriceEndTime(omContractAddTTDTO.getPriceEndTime())
                    //.setPriceEndType(omContractAddTTDTO.getPriceEndType())
                    //.setMemo(omContractAddTTDTO.getMemo())
                    .setIsStf(omContractAddTTDTO.getIsStf())
                    .setAddedDepositRate(StringUtils.isNotBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()) : null)
                    //.setAddedDepositAmount()  //计算
                    //.setEnterprise()          //客户
                    .setCompletedStatus(omContractAddTTDTO.getCompletedStatus());
            contractAddTTDTO.setTtId(mainTradeTicketDTO.getId());
            //价格信息
            contractAddTTDTO.setContractPriceInfo(contractPriceBaseEntity);

            TradeTicketDTO tradeTicketDTO = new TradeTicketDTO();
            tradeTicketDTO = BeanConvertUtils.copy(tradeTicketDTO, mainTradeTicketDTO);
            tradeTicketDTO.setDomainCode(domainCode);

            tradeTicketDTO.setContractAddTTDTO(contractAddTTDTO);

            tradeTicketDTOList.add(tradeTicketDTO);
        }

        if (ContractTypeEnum.STRUCTURE.getValue() == Integer.valueOf(omContractAddTTDTO.getContractType())) {
            ContractStructurePriceAddDTO contractStructurePriceAddDTO = new ContractStructurePriceAddDTO();
            contractStructurePriceAddDTO  //保存时处理
                    .setContractId(null)    //保存时处理
                    .setContractCode(null)  //保存时处理
                    //.setPriceApplyId(null)
                    .setStructureType(omContractAddTTDTO.getStructureType())
                    .setTotalNum(omContractAddTTDTO.getStructureTotalNum())
                    //.setMinPrice(omContractAddTTDTO.getMinPrice())
                    //.setMaxPrice(omContractAddTTDTO.getMaxPrice())
                    .setTotalDay(omContractAddTTDTO.getTotalDay())
                    .setUnitNum(BigDecimal.valueOf(omContractAddTTDTO.getUnitNum()))
                    .setDomainCode(omContractAddTTDTO.getDomainCode())
                    //.setPriceRule("")
                    //.setMemo(omContractAddTTDTO.getMemo())
                    .setStartTime(omContractAddTTDTO.getStructurePriceStartTime())
                    .setEndTime(omContractAddTTDTO.getStructurePriceEndTime());

            contractStructurePriceAddDTO.setTtId(null);

            mainTradeTicketDTO.setTradeType(ContractTradeTypeEnum.STRUCTURE_PRICE.getValue());
            mainTradeTicketDTO.setType(TTTypeEnum.STRUCTURE_PRICE.getType());
            mainTradeTicketDTO.setContractStructurePriceAddDTO(contractStructurePriceAddDTO);
            tradeTicketDTOList.add(mainTradeTicketDTO);
        }
        return tradeTicketDTOList;
    }


    @Override
    public Result mockModify(Integer t, String v) {
        ttModifyMockService.mockModify(t, v);
        return Result.success();
    }

    @Override
    public Result getTradeTicketById(Integer ttId) {
        return Result.success(tradeTicketDao.getTradeTicketEntityById(ttId));
    }

    @Override
    public Result getTtAddByTtId(Integer ttId) {
        return Result.success(tradeTicketQueryService.getLkgTTAddByTTId(ttId));
    }

    @Override
    public Result getTtModifyByTtId(Integer ttId) {
        return Result.success(tradeTicketQueryService.getLkgTTModifyByTTId(ttId));
    }

    @Override
    public Result updatePriceAllocateId(Integer contractId, Integer allocateId) {
        return Result.success(ttDomainService.updatePriceAllocateId(contractId, allocateId));
    }
}
