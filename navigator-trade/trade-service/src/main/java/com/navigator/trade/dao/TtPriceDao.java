package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.enums.ContraryStatusEnum;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.mapper.TtPriceMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:48
 */
@Dao
public class TtPriceDao extends BaseDaoImpl<TtPriceMapper, TTPriceEntity> {
    public List<TTPriceEntity> queryListByTTQueryDTO(TTQueryDTO ttQueryDTO) {
        List<TTPriceEntity> TTPriceEntityList = list(Wrappers.<TTPriceEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TTPriceEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TTPriceEntity::getGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
        );
        return TTPriceEntityList;
    }

    public TTPriceEntity getTTPriceEntityByTTId(Integer ttId) {
        // Case：1002935-合同定价协议出具报错，fixed By nana
        List<TTPriceEntity> priceEntityList = list(Wrappers.<TTPriceEntity>lambdaUpdate()
                .eq(TTPriceEntity::getTtId, ttId)
                .orderByAsc(TTPriceEntity::getId)
        );
        return CollectionUtils.isEmpty(priceEntityList) ? null : priceEntityList.get(0);
    }

    public List<TTPriceEntity> getConfirmPriceList(Integer contractId) {
        return list(Wrappers.<TTPriceEntity>lambdaQuery()
                .eq(TTPriceEntity::getContractId, contractId)
                .eq(TTPriceEntity::getContraryStatus, ContraryStatusEnum.NOT_CONTRARY.getValue())
        );
    }

    public List<TTPriceEntity> getTTPriceByContractId(Integer contractId) {
        return list(Wrappers.<TTPriceEntity>lambdaQuery()
                .eq(TTPriceEntity::getContractId, contractId)
        );
    }

    public List<TTPriceEntity> getPriceByContractNotId(Integer contractId, Integer id) {
        return list(Wrappers.<TTPriceEntity>lambdaQuery()
                .eq(TTPriceEntity::getContractId, contractId)
                .ne(TTPriceEntity::getId, id)
        );
    }

    public TTPriceEntity getConfirmPriceInfo(Integer contractId, Integer ttPriceId) {
        List<TTPriceEntity> TTPriceEntityList = list(Wrappers.<TTPriceEntity>lambdaQuery()
                .eq(null != contractId, TTPriceEntity::getContractId, contractId)
                .eq(null != ttPriceId, TTPriceEntity::getId, ttPriceId)
        );
        return CollectionUtils.isEmpty(TTPriceEntityList) ? null : TTPriceEntityList.get(0);
    }


    public List<TTPriceEntity> selectMany(TTPriceQO qo) {
        LambdaQueryWrapper<TTPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(qo.getTtId())) {
            queryWrapper.eq(TTPriceEntity::getTtId, qo.getTtId());
        }
        if (Objects.nonNull(qo.getContractId())) {
            queryWrapper.eq(TTPriceEntity::getContractId, qo.getContractId());
            queryWrapper.eq(TTPriceEntity::getContraryStatus, ContraryStatusEnum.NOT_CONTRARY.getValue());
        }
        if (StringUtils.isNotBlank(qo.getContractCode())) {
            queryWrapper.eq(TTPriceEntity::getContractCode, qo.getContractCode());
        }
        if (Objects.nonNull(qo.getPriceApplyId())) {
            queryWrapper.eq(TTPriceEntity::getPriceApplyId, qo.getPriceApplyId());
        }
        if (Objects.nonNull(qo.getSourceId())) {
            queryWrapper.eq(TTPriceEntity::getSourceId, qo.getSourceId());
            queryWrapper.ne(TTPriceEntity::getSourceId, 0);
        }
        if (Objects.nonNull(qo.getNeId())) {
            queryWrapper.ne(TTPriceEntity::getId, qo.getNeId());
        }
        return this.list(queryWrapper);
    }

    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        return update(Wrappers.<TTPriceEntity>lambdaUpdate()
                .set(TTPriceEntity::getContractId, contractEntity.getId())
                .set(TTPriceEntity::getContractCode, contractEntity.getContractCode())
                .set(TTPriceEntity::getSourceContractId, contractEntity.getParentId())
                .eq(TTPriceEntity::getTtId, ttId)) ? 1 : 0;
    }
}
