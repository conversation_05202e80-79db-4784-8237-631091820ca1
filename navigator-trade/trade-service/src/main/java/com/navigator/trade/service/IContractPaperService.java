package com.navigator.trade.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;

import java.util.List;

/**
 * <p>
 * 正本快递信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface IContractPaperService {

    ContractPaperEntity getContractPaper(Integer contractSignId);

    boolean saveContractPaper(ContractPaperDTO contractPaperDTO);

    List<Integer> sendContractSignOriginalPaper();

}
