package com.navigator.trade.facade.impl;

import com.navigator.trade.facade.TtTranferFacade;
import com.navigator.trade.pojo.entity.TTTranferEntity;
import com.navigator.trade.service.ITtTranferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/21 14:46
 */
@RestController
public class TtTranferFacadeImpl implements TtTranferFacade {

    @Autowired
    private ITtTranferService iTtTranferService;

    @Override
    public boolean saveTtTranfer(TTTranferEntity ttTranferEntity) {
        return iTtTranferService.saveTtTranfer(ttTranferEntity);
    }

    @Override
    public TTTranferEntity getTransferByTtId(Integer ttId) {
        return iTtTranferService.getTransferByTtId(ttId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId) {
        return iTtTranferService.getTTTranferByPriceApplyId(priceApplyId);
    }

    @Override
    public TTTranferEntity selectTTTranferByTTId(Integer TTId) {
        return iTtTranferService.selectTTTranferByTTId(TTId);
    }

    @Override
    public List<TTTranferEntity> selectTTTranferByPriceAllocateId(Integer priceAllocateId) {
        return iTtTranferService.selectTTTranferByPriceAllocateId(priceAllocateId);
    }

}
