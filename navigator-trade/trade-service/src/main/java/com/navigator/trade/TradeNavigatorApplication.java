package com.navigator.trade;

import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.trade.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
public class TradeNavigatorApplication {
    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }

    public static void main(String[] args) {
        SpringApplication.run(TradeNavigatorApplication.class, args);
    }
}
