package com.navigator.trade.app.sign.domain.service.impl;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.app.sign.domain.service.ContractSignDomainService;
import com.navigator.trade.dao.ContractPaperDao;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.ContractSignDetailDao;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.ContractSignDetailEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 协议域一些写的操作
 * <AUTHOR>
 * @Date  20240805
 */
@Service
public class ContractSignDomainServiceImpl implements ContractSignDomainService {

    @Resource
    private ContractSignDetailDao contractSignDetailDao;
    @Resource
    private ContractPaperDao contractPaperDao;
    @Resource
    private ContractSignDao contractSignDao;


    @Override
    public void saveContractSignDetail(ContractSignDetailEntity contractSignDetailEntity) {
        contractSignDetailDao.save(contractSignDetailEntity);
    }

    @Override
    public boolean saveContractPaper(ContractPaperDTO contractPaperDTO) {
        ContractPaperEntity contractPaperEntity = BeanConvertUtils.convert(ContractPaperEntity.class, contractPaperDTO);
        ContractSignEntity contractSignEntity = contractSignDao.getById(contractPaperEntity.getContractSignId());
        if (ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType()) {
            contractPaperEntity.setCustomerId(contractSignEntity.getCustomerId());
        } else {
            contractPaperEntity.setCustomerId(Integer.parseInt(contractSignEntity.getSupplierId()));
        }
        contractPaperEntity.setCreatedAt(new Date())
                .setUpdatedAt(new Date());
        if (null == contractPaperDTO.getId()) {
            return contractPaperDao.save(contractPaperEntity);
        }
        return contractPaperDao.updateById(contractPaperEntity);
    }

    @Override
    public boolean updatePaperEntity(ContractPaperEntity contractPaperEntity) {
        return contractPaperDao.updateById(contractPaperEntity);
    }

    @Override
    public boolean updateSignEntity(ContractSignEntity contractSignEntity) {
        return contractSignDao.updateById(contractSignEntity);
    }

    @Override
    public boolean saveContractSign(ContractSignEntity contractSignEntity) {
        return contractSignDao.save(contractSignEntity);
    }

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return contractSignDao.updateContractId(ttId,contractId);
    }

    @Override
    public boolean updateSignWarrantId(Integer signId, Integer warrantId, String warrantCode) {
        return contractSignDao.updateSignWarrantId(signId,warrantId,warrantCode);
    }
}
