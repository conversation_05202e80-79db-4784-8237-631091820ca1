package com.navigator.trade.service.futrue.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.util.BusinessUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.future.pojo.dto.CustomerDomainTransDTO;
import com.navigator.trade.facade.ContractEquityFacade;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.futrue.ICustomerFutureContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.navigator.common.constant.BizConstant.*;

@Slf4j
@Service
public class CustomerFutureContractServiceImpl implements ICustomerFutureContractService {
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private ContractEquityFacade contractEquityFacade;
    @Autowired
    private IContractQueryService contractQueryService;
    @Autowired
    public IContractOperationNewService salesContractOperationService;

    @Override
    public ContractTransferCountDTO getContractTransferCount(Integer customerId, Integer category2, Integer category3) {
        // 转月次数限制
        int transferTimes = NORMAL_CUSTOMER_TRANSFER_COUNT;
        // 反点价次数限制
        int reversePriceTimes = NORMAL_CUSTOMER_REVERSE_COUNT;

        ContractTransferCountDTO contractTransferCountDTO = new ContractTransferCountDTO();
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO.setCustomerId(customerId)
                .setCategory2(String.valueOf(category2))
                .setCategory3(String.valueOf(category3))
        ;
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

        if (!customerDetailEntityList.isEmpty()) {
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            // 先判断是否开通转月权限
            if (customerDetailEntity.getIsWhiteList() == 1) {
                transferTimes = VIP_CUSTOMER_TRANSFER_COUNT;
            }
            // 再判断是否开通反点价权限
            if (customerDetailEntity.getIsReversePrice() == 1) {
                reversePriceTimes = VIP_CUSTOMER_REVERSE_COUNT;
            }
        }

        return contractTransferCountDTO
                .setAbleTransferTimes(transferTimes)
                .setAbleReversePriceTimes(reversePriceTimes)
                .setTotalReversePriceTimes(reversePriceTimes)
                .setTotalTransferTimes(transferTimes);
    }


    @Override
    public ContractTransferCountDTO getContractTransferNum(Integer customerId,
                                                           Integer goodsCategoryId,
                                                           String domainCode,
                                                           Date deliveryEndTime,
                                                           Integer category2) {
        // 是否是超远月
        int isOverForward = BusinessUtil.isOverForwardContract(domainCode, deliveryEndTime) ? 1 : 0;

        return getContractTransferNum(customerId, goodsCategoryId, isOverForward, category2);
    }


    @Override
    public ContractTransferCountDTO getContractTransferNum(Integer customerId, Integer goodsCategoryId, Integer isOverForward, Integer category2) {
        // 转月次数限制
        int transferTimes = NORMAL_CUSTOMER_TRANSFER_COUNT;
        // 反点价次数限制
        int reversePriceTimes = NORMAL_CUSTOMER_REVERSE_COUNT;
        ContractTransferCountDTO contractTransferCountDTO = new ContractTransferCountDTO();
        // 判断是否是白名单用户（4次转月机会，1次反点价机会，可额外申请次数）
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO.setCustomerId(customerId)
                .setCategory2(String.valueOf(category2));
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

        if (!customerDetailEntityList.isEmpty()) {
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            if (null != customerDetailEntity) {
                // 先判断是否开通转月权限
                if (customerDetailEntity.getIsWhiteList() == 1) {
                    transferTimes = 4;
                } else {
                    // 普通用户考虑超远期合同
                    if (isOverForward == 1) {
                        transferTimes = 2;
                    }
                }
                // 再判断是否开通反点价权限
                if (customerDetailEntity.getIsReversePrice() == 1) {
                    reversePriceTimes = 1;
                }
            }
        }

        return contractTransferCountDTO
                .setIsOverForward(isOverForward)
                .setAbleTransferTimes(transferTimes)
                .setTotalTransferTimes(transferTimes)
                .setTransferredTimes(0)
                .setAbleReversePriceTimes(reversePriceTimes)
                .setTotalReversePriceTimes(reversePriceTimes)
                .setReversedPriceTimes(0);
    }

    @Override
    public CustomerDomainTransDTO getCustomerFutureContractInfo(Integer customerId, Integer goodsCategoryId, String domainCode, PriceTypeEnum priceTypeEnum) {
        CustomerDomainTransDTO customerDomainTransDTO = new CustomerDomainTransDTO();
        // 合同签订量
        BigDecimal totalCount = BigDecimal.ZERO;
        // 有效合同量
        BigDecimal validCount = BigDecimal.ZERO;
        // 已转月量
        BigDecimal transferredCount = BigDecimal.ZERO;
        // 已点价量
        BigDecimal pricedCount = BigDecimal.ZERO;

        // 根据点价类型分别获取数据（点价/转月）
        switch (priceTypeEnum) {
            case PRICING:
                // 获取所有基差合同、基差暂定价合同的数据
                List<ContractEntity> priceContractList = contractQueryService.getContractList(customerId, goodsCategoryId, domainCode, Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()));
                for (ContractEntity contractEntity : priceContractList) {
                    totalCount = totalCount.add(contractEntity.getOrderNum());
                    // 有效合同量=签订量-已拆分量-拆分中量-已提货量-开单中的量
                    validCount = validCount.add(contractEntity.getContractNum());
                    // 已点价量
                    pricedCount = pricedCount.add(contractEntity.getTotalPriceNum());
                }

                break;
            case TRANSFER_MONTH:
                // 获取所有转月次数大于0的基差合同的数据
                List<ContractEntity> transferContractList = contractQueryService.getContractList(customerId, goodsCategoryId, domainCode, Arrays.asList(ContractTypeEnum.JI_CHA.getValue()));
                for (ContractEntity contractEntity : transferContractList) {
                    if (contractEntity.getAbleTransferTimes() > 0) {
                        totalCount = totalCount.add(contractEntity.getOrderNum());
                        // 有效合同量=签订量-已拆分量-拆分中量-已提货量-开单中的量
                        validCount = validCount.add(contractEntity.getContractNum());
                        // 已转月量
                        transferredCount = transferredCount.add(contractEntity.getTotalTransferNum());
                    }
                }
                break;
            default:
                break;
        }
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        return customerDomainTransDTO
                //.setValidCount(validCount)
                .setPricedCount(pricedCount);
                //.setTransferredCount(transferredCount);
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
    }

    @Override
    public boolean changeCustomerWhiteList(CustomerDetailDTO customerDetailDTO) {
        // 转月状态
        Integer transferStatus = customerDetailDTO.getIsWhiteList();
        // 反点价状态
        Integer reversePriceStatus = customerDetailDTO.getIsReversePrice();
        // 客户id
        Integer customerId = customerDetailDTO.getCustomerId();

        log.info("changeCustomerWhiteList[customerId:{},transferStatus:{},reversePriceStatus:{}]", customerId, transferStatus, reversePriceStatus);
        //List<Integer> category2List = Arrays.stream(customerDetailDTO.getCategory2().split(",")).map(category2 -> Integer.parseInt(category2.trim())).collect(Collectors.toList());
        List<Integer> category3List = Arrays.stream(customerDetailDTO.getCategory3().split(",")).map(category3 -> Integer.parseInt(category3.trim())).collect(Collectors.toList());
        // 转月
        if (customerDetailDTO.isTransferStatusChange()) {
            // 获取该客户下面的所有生效中的合同
            List<ContractEntity> transferContractList = contractQueryService.getContractList(customerId,
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 Start
                    category3List,
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 End
                    Arrays.asList(ContractStatusEnum.INEFFECTIVE.getValue(), ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.MODIFYING.getValue(),
                            ContractStatusEnum.CLOSING.getValue(), ContractStatusEnum.SPLITTING.getValue()),
                    Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.YI_KOU_JIA.getValue()));

            // 开通白名单
            for (ContractEntity contractEntity : transferContractList) {
                // 原始合同信息
                String originContractInfo = JSONUtil.toJsonStr(contractEntity);
                log.info("[before]contractCode:{},TransferTimes:{},{},{}", contractEntity.getContractCode(),
                        contractEntity.getTotalTransferTimes(), contractEntity.getTransferredTimes(), contractEntity.getAbleTransferTimes());

                // 总次数
                int totalTransferTimes = contractEntity.getTotalTransferTimes();

                // 可转
                int ableTransferTimes = contractEntity.getAbleTransferTimes();

                // 已转
                int transferredTimes = contractEntity.getTransferredTimes() == 0 ? totalTransferTimes - ableTransferTimes : contractEntity.getTransferredTimes();

                // 普通用户开通白名单
                if (transferStatus.equals(DisableStatusEnum.ENABLE.getValue())) {
                    if (changeTimesFlag(contractEntity)) {
                        if (ableTransferTimes > 4) {
                            continue;
                        }
                    }

                    // 初始化总次数
                    totalTransferTimes = Math.max(totalTransferTimes, 4);

                    // 计算可转次数：总次数-已转次数
                    ableTransferTimes = totalTransferTimes - transferredTimes;

                } else {
                    // 白名单用户取消白名单

                    // 判断是否修改过可转月次数
                    if (changeTimesFlag(contractEntity)) continue;


                    if ((contractEntity.getIsOverForward() == 1 && totalTransferTimes <= 2)
                            || (contractEntity.getIsOverForward() == 0 && totalTransferTimes <= 1)
                    ) {
                        continue;
                    }

                    // 判断合同是否是超远期合同
                    if (contractEntity.getIsOverForward() == 1) {
                        totalTransferTimes = 2;
                        ableTransferTimes = Math.max(ableTransferTimes - 2, 0);
                    } else {
                        totalTransferTimes = 1;
                        ableTransferTimes = Math.max(ableTransferTimes - 3, 0);
                    }
                }


                log.error("=====================================================================================================totalTransferTimes:{}", totalTransferTimes);
                // 更新合同信息
                contractEntity.setAbleTransferTimes(ableTransferTimes)
                        .setTotalTransferTimes(totalTransferTimes)
                        .setTransferredTimes(transferredTimes);

                log.info("[after]contractCode:{},TransferTimes:{},{},{}", contractEntity.getContractCode(),
                        contractEntity.getTotalTransferTimes(), contractEntity.getTransferredTimes(), contractEntity.getAbleTransferTimes());

                contractQueryService.updateContract(contractEntity);
                log.error("=====================================================================================================totalTransferTimes:{}", contractEntity.getTotalTransferTimes());
                // 新合同信息
                String newContractInfo = JSONUtil.toJsonStr(contractEntity);

                // 记录操作记录
                Map<String, Integer> parameterMap = new HashMap<>();
                parameterMap.put("customerId", customerId);
                parameterMap.put("transferStatus", transferStatus);
                parameterMap.put("reversePriceStatus", reversePriceStatus);

                salesContractOperationService.addContractOperationLog(new OperationDetailDTO()
                        .setBizCode(LogBizCodeEnum.CHANGE_CUSTOMER_WHITE_LIST.getBizCode())
                        .setOperationName(LogBizCodeEnum.CHANGE_CUSTOMER_WHITE_LIST.getMsg())
                        .setReferBizId(contractEntity.getId())
                        .setReferBizCode(contractEntity.getContractCode())
                        .setMetaData(originContractInfo)
                        .setData(JSON.toJSONString(parameterMap))
                        .setReferOperationData(newContractInfo), SystemEnum.MAGELLAN.getValue());
            }
        }

        // 反点价
        if (customerDetailDTO.isReversePriceStatusChange()) {
            // 获取该客户下面的所有生效中的合同
            List<ContractEntity> reversePriceContractList = contractQueryService.getContractList(customerId,
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 Start
                    category3List,
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 End
                    Arrays.asList(ContractStatusEnum.INEFFECTIVE.getValue(), ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.MODIFYING.getValue(),
                            ContractStatusEnum.CLOSING.getValue(), ContractStatusEnum.SPLITTING.getValue()),
                    Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.YI_KOU_JIA.getValue()));

            // 开通反点价
            for (ContractEntity contractEntity : reversePriceContractList) {
                // 原始合同信息
                String originContractInfo = JSONUtil.toJsonStr(contractEntity);
                log.info("[before]contractCode:{},ReversePriceTimes:{},{},{}", contractEntity.getContractCode(),
                        contractEntity.getTotalReversePriceTimes(), contractEntity.getReversedPriceTimes(), contractEntity.getAbleReversePriceTimes());

                // 总次数
                int totalReversePriceTimes = contractEntity.getTotalReversePriceTimes();

                // 可转
                int ableReversePriceTimes = contractEntity.getAbleReversePriceTimes();

                // 已转
                int reversedPriceTimes = contractEntity.getReversedPriceTimes() == 0 ? totalReversePriceTimes - ableReversePriceTimes : contractEntity.getReversedPriceTimes();

                // 普通用户开通反点价
                if (reversePriceStatus.equals(DisableStatusEnum.ENABLE.getValue())) {
                    // 若可反点价次数大于1次则可转月次数不变，若可反点价次数小于1次则可转月次数更新为1
                    if (changeTimesFlag(contractEntity)) {
                        if (ableReversePriceTimes > 1) {
                            continue;
                        }
                    }
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对-权益变更引起的反点价次数更新 Author: Mr 2024-06-24 Start
                    // 初始化总次数
                    // totalReversePriceTimes = 1;
                    totalReversePriceTimes = Math.max(totalReversePriceTimes, 1);

                    // 计算可转次数：总次数-已转次数
                    // ableReversePriceTimes = ableReversePriceTimes + 1;
                    ableReversePriceTimes = totalReversePriceTimes - reversedPriceTimes;
                    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对-权益变更引起的反点价次数更新 Author: Mr 2024-06-24 End
                } else {
                    // 白名单用户取消反点价
                    // 判断是否修改过可反点价次数
                    if (changeTimesFlag(contractEntity)) continue;

                    totalReversePriceTimes = 0;
                    ableReversePriceTimes = Math.max(ableReversePriceTimes - 1, 0);
                }

                // 更新合同信息
                contractEntity
                        .setAbleReversePriceTimes(ableReversePriceTimes)
                        .setTotalReversePriceTimes(totalReversePriceTimes)
                        .setReversedPriceTimes(Math.max(reversedPriceTimes, 0));

                log.info("[after]contractCode:{},ReversePriceTimes:{},{},{}", contractEntity.getContractCode(),
                        contractEntity.getTotalReversePriceTimes(), contractEntity.getReversedPriceTimes(), contractEntity.getAbleReversePriceTimes());

                contractQueryService.updateContract(contractEntity);

                // 新合同信息
                String newContractInfo = JSONUtil.toJsonStr(contractEntity);

                // 记录操作记录
                Map<String, Integer> parameterMap = new HashMap<>();
                parameterMap.put("customerId", customerId);
                parameterMap.put("transferStatus", transferStatus);
                parameterMap.put("reversePriceStatus", reversePriceStatus);

                salesContractOperationService.addContractOperationLog(new OperationDetailDTO()
                        .setBizCode(LogBizCodeEnum.CHANGE_CUSTOMER_WHITE_LIST.getBizCode())
                        .setOperationName(LogBizCodeEnum.CHANGE_CUSTOMER_WHITE_LIST.getMsg())
                        .setReferBizId(contractEntity.getId())
                        .setReferBizCode(contractEntity.getContractCode())
                        .setMetaData(originContractInfo)
                        .setData(JSON.toJSONString(parameterMap))
                        .setReferOperationData(newContractInfo), SystemEnum.MAGELLAN.getValue());
            }
        }
        return true;
    }

    /**
     * 判断合同是否修改过可转月次数
     *
     * @param contractEntity 合同信息
     * @return
     */
    private boolean changeTimesFlag(ContractEntity contractEntity) {
        Result result = contractEquityFacade.getChangeEquityByContractCode(contractEntity.getContractCode());
        if (result.isSuccess()) {
            List<ContractChangeEquityEntity> changeEquityEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractChangeEquityEntity.class);

            return changeEquityEntityList.stream().anyMatch(contractChangeEquityEntity ->
                    contractChangeEquityEntity.getApproveStatus() == TTApproveStatusEnum.APPROVE.getValue());
        }
        return false;
    }
}
