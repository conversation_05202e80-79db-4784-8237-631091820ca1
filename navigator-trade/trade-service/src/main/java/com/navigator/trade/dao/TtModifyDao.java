package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.TtModifyMapper;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class TtModifyDao extends BaseDaoImpl<TtModifyMapper, TTModifyEntity> {

    public List<TTModifyEntity> queryListByTTQueryDTO(TTQueryDTO ttQueryDTO) {
        List<TTModifyEntity> ttModifyEntityList = list(Wrappers.<TTModifyEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(ttQueryDTO.getCustomerName()), TTModifyEntity::getCustomerName, "%" + ttQueryDTO.getCustomerName() + "%")
                .eq(StringUtils.isNotBlank(ttQueryDTO.getGoodsCategoryId()), TTModifyEntity::getGoodsCategoryId, ttQueryDTO.getGoodsCategoryId())
        );
        return ttModifyEntityList;
    }

    public TTModifyEntity getTTModifyEntityByTTId(Integer ttId) {
        List<TTModifyEntity> list = list(Wrappers.<TTModifyEntity>lambdaUpdate().eq(TTModifyEntity::getTtId, ttId));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<TTModifyEntity> getModifyList(Integer contractId, Integer contractSource) {
        return this.list(Wrappers.<TTModifyEntity>lambdaQuery()
                .eq(TTModifyEntity::getContractId, contractId)
                .eq(TTModifyEntity::getType, contractSource)
        );
    }

    public TTModifyEntity getModifyByRelationId(String relationId, Integer id) {
        List<TTModifyEntity> list = list(Wrappers.<TTModifyEntity>lambdaQuery()
                .eq(TTModifyEntity::getRelationId, relationId)
                .ne(TTModifyEntity::getId, id)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public TTModifyEntity getModifyByRelationTTId(String relationId, Integer ttId) {
        List<TTModifyEntity> list = list(Wrappers.<TTModifyEntity>lambdaQuery()
                .eq(TTModifyEntity::getRelationId, relationId)
                .ne(TTModifyEntity::getTtId, ttId)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public TTModifyEntity getByTTId(Integer ttId) {
        List<TTModifyEntity> list = list(Wrappers.<TTModifyEntity>lambdaQuery()
                .eq(TTModifyEntity::getTtId, ttId)
        );
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public int updateContractInfo(Integer ttId, ContractEntity contractEntity) {
        return update(Wrappers.<TTModifyEntity>lambdaUpdate()
                .set(TTModifyEntity::getContractId, contractEntity.getId())
                .set(TTModifyEntity::getContractCode, contractEntity.getContractCode())
                .set(TTModifyEntity::getContractType, contractEntity.getContractType())
                .set(TTModifyEntity::getSourceContractId, contractEntity.getParentId())
                .set(TTModifyEntity::getRootContractId, contractEntity.getRootId())
                .eq(TTModifyEntity::getTtId, ttId)) ? 1 : 0;
    }
}
