package com.navigator.trade.app.trade;

import com.navigator.common.dto.Result;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.tradeticket.ApprovalDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SubmitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:34
 * @Version 1.0
 */
public interface TradeAppService {


    /**
     * 现货合同创建数据准备（TT域）
     *  1、数据转换（TT域）
     *  2、数据校验（TT域）
     *  3、数据保存（TT域）
     * @param submitTTDTO
     * @return
     */
    Result<List<TTDTO>> prepareContractCreateData(SubmitTTDTO submitTTDTO);

    /**
     * 现货采销|仓单采购合同创建 - 实际是有两个过程
     * saveTT(ttdto)
     * submitTT(ttdto)
     * 兼容保存和提交
     * @param submitTTDTO
     * @return
     */
    List<TTQueryVO> contractCreate(SubmitTTDTO submitTTDTO);

    List<TTQueryVO> contractCreate(List<TTDTO> ttdtoList);

    List<TTQueryVO> contractCreate(TTDTO ttdto);

    List<TTQueryVO> contractCreate(TTDTO ttdto, ArrangeContext arrangeContext);
    /**
     * 保存的场景
     * @param ttdto
     * @return
     */
    List<TTQueryVO> saveTradeTicketData(TTDTO ttdto);

    /**
     * 创建结构化合同
     * @param ttdto
     * @return
     */
    List<TTQueryVO> structureContractCreate(TTDTO ttdto);
    /**
     * 现货合同修改 - 实际是有两个过程
     * saveTT(ttdto)
     * submitTT(ttdto)
     *
     * @param contractModifyDTO
     * @return
     */
    List<TTQueryVO> spotContractModify(ContractModifyDTO contractModifyDTO);

    /**
     * 现货合同拆分动作 采购|销售
     *
     * @param contractModifyDTO
     * @return
     */
    List<TTQueryVO> spotContractSplit(ContractModifyDTO contractModifyDTO);

    /**
     * 现货|仓单合同回购
     *
     * @param contractBuyBackDTO
     * @return
     */
    List<TTQueryVO> contractBuyBack(ContractBuyBackDTO contractBuyBackDTO);


    /**
     * 现货解约索赔
     *
     * @param contractWashOutDTO
     * @return
     */
    List<TTQueryVO> spotContractWashOut(ContractWashOutDTO contractWashOutDTO);

    /**
     * 现货合同关闭
     *
     * @param contractId
     * @return
     */
    List<TTQueryVO> spotContractClose(Integer contractId);

    /**
     * 仓单转让生成仓单销售合同（交割所/线下交易所仓单）
     *
     * @param ttdto 参数待定
     * @return
     */
    List<TTQueryVO> createWarrantSalesContract(TTDTO ttdto);

    /**
     * 仓单合同注销
     *
     * @param contractWriteOffDTO
     */
    List<TTQueryVO> warrantContractWriteOff(ContractWriteOffDTO contractWriteOffDTO);

    /**
     * 仓单合同注销撤回
     *
     * @param contractWriteOffWithDrawDTO
     */
    List<TTQueryVO> warrantContractWriteOffWithdraw(ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * 仓单合同作废
     *
     * @param contractId
     * @return
     */
    List<TTQueryVO> warrantContractInvalid(Integer contractId);

    /**
     * 仓单合同注销-豆二的处理
     *
     * @param contractWriteOffOMDTO
     */
    List<TTQueryVO> warrantContractSoyBean2WriteOff(ContractWriteOffOMDTO contractWriteOffOMDTO);

    /**
     * tt审批流程
     * @param approvalDTO
     * @param ttType
     */
    void approveTT(ApprovalDTO approvalDTO, Integer ttType);
    /**
     * 现货、仓单合同点价
     */
    List<TTQueryVO> contractPrice(SalesContractTTPriceDTO salesContractTTPriceDTO);

    /**
     * 现货、仓单合同转月
     */
    ContractEntity contractTransferMonth(ContractTransferDTO contractTransferDTO);

    /**
     * 反点价操作的编排
     */
    ContractEntity spotContractReversePrice(ContractTransferDTO contractTransferDTO);

}
