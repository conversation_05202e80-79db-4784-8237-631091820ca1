package com.navigator.trade.service.contract;

import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <p>
 * 合同基本操作的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
public interface IContractOperationNewService {

    /**
     * 新增合同的操作记录
     *
     * @param contractEntity 合同信息
     * @param bizCodeEnum    操作枚举
     * @param data           数据
     * @param systemEnum     操作系统
     */
    void addContractOperationLog(ContractEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum);

    void addContractOperationLog(OperationDetailDTO operationDetailDTO, Integer systemEnum);

    /**
     * 暂定价合同定价（生成定价单）
     *
     * @param confirmPriceDTO
     * @return
     */
    boolean createTtPrice(ConfirmPriceDTO confirmPriceDTO);

    /**
     * 作废合同
     *
     * @param contractBaseDTO
     * @return
     */
    List<TTQueryVO> invalidContract(ContractBaseDTO contractBaseDTO);

    /**
     * tt作废合同
     *
     * @param contractBaseDTO
     * @return
     */
    boolean invalidContractByTT(ContractBaseDTO contractBaseDTO);


    /**
     * 补充合同信息
     *
     * @param contractBaseDTO
     * @return
     */
    boolean fillContract(ContractBaseDTO contractBaseDTO);

    /**
     * 审批通过更新合同 → 更新合同的同步
     *
     * @param contractModifyDTO
     */
    void updateContractByApproved(ContractModifyDTO contractModifyDTO);

}
