package com.navigator.trade.service.tradeticket.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.PowerFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.dao.*;
import com.navigator.trade.mapper.TradeTicketMapper;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractModifyLogVO;
import com.navigator.trade.pojo.vo.ReportVO;
import com.navigator.trade.pojo.vo.TTAllStatusNumVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.impl.ContractQueryServiceImpl;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.navigator.trade.pojo.enums.ContractSignStatusEnum.*;

/**
 * <p>
 * TT申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
@Slf4j
public class TradeTicketQueryServiceImpl implements ITradeTicketQueryService {
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private TradeTicketDao tradeTicketDao;
    @Autowired
    private TtAddDao ttAddDao;
    @Autowired
    private TtModifyDao ttModifyDao;
    @Autowired
    private TtTranferDao ttTranferDao;
    @Autowired
    private TtPriceDao ttPriceDao;
    @Autowired
    private TtStructureDao ttStructureDao;
    @Resource
    private TradeTicketMapper tradeTicketMapper;
    @Autowired
    private ContractQueryServiceImpl contractQueryService;
    @Autowired
    private IContractSignQueryService iContractSignQueryService;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TradeTicketVODao tradeTicketVODao;
    @Autowired
    private AttributeFacade attributeFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    private ContractPriceDao contractPriceDao;
    @Autowired
    private ContractDao contractDao;
    @Autowired
    private PowerFacade powerFacade;
    @Autowired
    private CompanyFacade companyFacade;

    private static Pattern NUMBER_PATTERN = Pattern.compile("^[0-9]*$");

    private static List<Integer> newTypeList = Arrays.asList(TTTypeEnum.NEW.getType(), TTTypeEnum.STRUCTURE_PRICE.getType());

    private static List<Integer> cancelList = Arrays.asList(WAIT_PROVIDE.getValue(), WAIT_REVIEW.getValue(), WAIT_STAMP.getValue());

    private static List<Integer> forbiddenCancelTypeList = Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.TRANSFER.getType(),
            TTTypeEnum.REVERSE_PRICE.getType(), TTTypeEnum.FIXED.getType());

    private static List<Integer> invalidList = Arrays.asList(WAIT_BACK.getValue(), WAIT_CONFIRM.getValue());

    private static boolean matches(TTAddEntity i) {
        if (null == i) {
            return false;
        }
        return !NUMBER_PATTERN.matcher(i.getDestination()).matches();
    }

    @Override
    public TradeTicketEntity getCanModifyByContractId(Integer contractId) {
        return tradeTicketDao.getCanModifyByContractId(contractId).size() > 0 ? tradeTicketDao.getCanModifyByContractId(contractId).get(0) : null;
    }

    @Override
    public TradeTicketEntity getBySignId(String signId) {
        return tradeTicketDao.getBySignId(signId).size() > 0 ? tradeTicketDao.getBySignId(signId).get(0) : null;
    }

    @Override
    public TradeTicketEntity getByTtId(Integer ttId) {
        return tradeTicketDao.getById(ttId);
    }

    @Override
    public TradeTicketDTO getTTDetailInfo(Integer ttId) {
        return getTTDetailInfo(ttId, "");
    }

    @Override
    public TradeTicketDTO getTTDetailInfo(String ttCode) {
        return getTTDetailInfo(0, ttCode);
    }

    private TradeTicketDTO getTTDetailInfo(Integer ttId, String ttCode) {
        TradeTicketDTO tradeTicketDTO = new TradeTicketDTO();
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        if (null != ttId && ttId > 0) {
            tradeTicketEntity = tradeTicketDao.getById(ttId);
        } else {
            tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
            ttId = tradeTicketEntity.getId();
        }

        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
        TTPriceEntity ttPriceEntity = ttPriceDao.getTTPriceEntityByTTId(ttId);
        TTTranferEntity tranferEntity = ttTranferDao.getTTTransferEntityByTTId(ttId);
        TTStructureEntity ttStructureEntity = ttStructureDao.getByTTId(ttId);
        tradeTicketDTO = BeanConvertUtils.convert(TradeTicketDTO.class, tradeTicketEntity);
        if (null != ttAddEntity) {
            tradeTicketDTO.setContractAddTTDTO(BeanConvertUtils.convert(ContractAddTTDTO.class, ttAddEntity));
        }
        if (null != ttModifyEntity) {
            tradeTicketDTO.setContractModifyTTDTO(BeanConvertUtils.convert(ContractModifyTTDTO.class, ttModifyEntity));
        }
        if (null != ttPriceEntity) {
            tradeTicketDTO.setContractTTPriceDTO(ttPriceEntity);
        }
        if (null != tranferEntity) {
            tradeTicketDTO.setContractTransferTTDTO(BeanConvertUtils.convert(ContractTransferTTDTO.class, tranferEntity));
        }
        if (null != ttStructureEntity) {
            tradeTicketDTO.setContractStructurePriceAddDTO(BeanConvertUtils.convert(ContractStructurePriceAddDTO.class, ttStructureEntity));
        }

        return tradeTicketDTO;
    }

    @Override
    public List<ContractModifyLogVO> queryModifyLog(String contractCode) {
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getTradeTicketEntityByContractCode(contractCode);
        return tradeTicketEntityList.stream().map(i -> {
            ContractModifyLogVO contractModifyLogVO = new ContractModifyLogVO();
            TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(i.getId());
            return contractModifyLogVO
                    .setContractCode(i.getContractCode())
                    .setContractSource(ContractActionEnum.getByType(i.getContractId()).getDesc())
                    .setCustomerName(ttModifyEntity.getCustomerName())
                    .setTtCode(i.getCode())
                    .setProtocolCode(i.getProtocolCode())
                    .setCreatedAt(i.getCreatedAt());

        }).collect(Collectors.toList());
    }

    @Override
    public List<String> getDestination() {
        List<TTAddEntity> list = ttAddDao.getDestination();
        List<String> destinationList = new ArrayList<>();
        list.stream().filter(TradeTicketQueryServiceImpl::matches)
                .forEach(i -> destinationList.add(i.getDestination()));
        return destinationList;
    }

    @Override
    public List<TradeTicketEntity> getSplitList(String contractCode) {
        return tradeTicketDao.getSplitList(contractCode);
    }

    @Override
    public List<TradeTicketEntity> getReviseList(String contractCode) {
        return tradeTicketDao.getReviseList(contractCode);
    }

    @Override
    public List<TradeTicketEntity> getByContractId(Integer contractId) {
        return tradeTicketDao.getByContractId(contractId);
    }

    @Override
    public TTModifyEntity getModifyByRelationId(String relationId, Integer id) {
        return ttModifyDao.getModifyByRelationId(relationId, id);
    }

    @Override
    public TradeTicketEntity getByGroupId(String groupId, Integer id) {
        return tradeTicketDao.getByGroupId(groupId, id);
    }

    @Override
    public TTTranferEntity getTransferById(Integer ttId) {
        return ttTranferDao.getTTTransferEntityByTTId(ttId);
    }

    @Override
    public List<TradeTicketEntity> queryChangeLogByContractId(Integer contractId, String contractCode) {
        return tradeTicketDao.queryChangeLogByContractId(contractId, contractCode);
    }

    @Override
    public List<ReportVO> queryTTReport(ReportDTO reportDTO) {
        List<ReportVO> resultList = new ArrayList<>();
        //查询满足条件信息
        List<TradeTicketVOEntity> tradeTicketVOEntities = tradeTicketVODao.queryTTReport(reportDTO);
        List<ReportVO> reportVOList = tradeTicketVOEntities.stream().map(i -> {
            ReportVO reportVO = new ReportVO();
            reportVO
                    .setDeliveryStartTime(DateTimeUtil.formatDateCN(i.getDeliveryStartTime()))
                    .setContractTypeName(ContractTypeEnum.getDescByValue(i.getContractType()))
                    .setContractType(i.getContractType())
                    .setFactoryCode(i.getDeliveryFactoryCode())
                    .setUnitPrice(i.getUnitPrice())
                    .setStatus(i.getStatus())
                    .setChangeContractNum(i.getChangeContractNum())
            ;
            return reportVO;
        }).collect(Collectors.toList());

        Map<Integer, List<ReportVO>> contractTypeMap = reportVOList.stream().collect(Collectors.groupingBy(ReportVO::getContractType));
        //按合同类型分组
        List<ReportVO> yiList = contractTypeMap.get(ContractTypeEnum.YI_KOU_JIA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.YI_KOU_JIA.getValue());
        dealByTime(resultList, yiList, ContractTypeEnum.YI_KOU_JIA, reportDTO.getFactoryCode());
        List<ReportVO> jiList = contractTypeMap.get(ContractTypeEnum.JI_CHA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.JI_CHA.getValue());
        dealByTime(resultList, jiList, ContractTypeEnum.JI_CHA, reportDTO.getFactoryCode());
        List<ReportVO> zanList = contractTypeMap.get(ContractTypeEnum.ZAN_DING_JIA.getValue()) == null ? Collections.emptyList() : contractTypeMap.get(ContractTypeEnum.ZAN_DING_JIA.getValue());
        dealByTime(resultList, zanList, ContractTypeEnum.ZAN_DING_JIA, reportDTO.getFactoryCode());
        return resultList;
    }

    @Override
    public Result generateTTByContractId(List<Integer> idList) {
        for (Integer id : idList) {
            ContractPriceEntity contractPriceEntity = contractPriceDao.getById(id);
            TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
            if (contractPriceEntity != null) {
                BeanUtils.copyProperties(contractPriceEntity, tradeTicketEntity);
                ContractEntity contractEntity = contractDao.getContractById(contractPriceEntity.getContractId());
                if (contractEntity != null) {
                    BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
                    String code = null;
                    if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
                        code = CodeGeneratorUtil.genSalesTTNewCode();
                    } else {
                        code = CodeGeneratorUtil.genPurchaseTTNewCode();
                    }
                    tradeTicketEntity
                            .setContractCode(contractPriceEntity.getContractCode())
                            .setId(contractPriceEntity.getTtId())
                            .setType(TTTypeEnum.NEW.getType())
                            .setCode(code)
                            .setContractStatus(contractEntity.getStatus())
                            .setContractSignatureStatus(null)
                            .setStatus(TTStatusEnum.DONE.getType())
                            .setApprovalStatus(ApproveResultEnum.AGREE.getValue())
                            .setApprovalType(TTApproveStatusEnum.WITHOUT_APPROVE.getValue())
                            .setBeforeContractNum(BigDecimal.ZERO)
                            .setBelongCustomerId(null)
                            .setSubGoodsCategoryId(tradeTicketEntity.getCategory2())
                            .setChangeContractNum(contractEntity.getContractNum())
                            .setAfterContractNum(contractEntity.getContractNum())
                    ;
                    tradeTicketDao.saveOrUpdate(tradeTicketEntity);
                }
                TTAddEntity ttAddEntity = new TTAddEntity();
                BeanUtils.copyProperties(contractEntity, ttAddEntity);
                ttAddEntity
                        .setContractCode(contractPriceEntity.getContractCode())
                        .setContractId(contractPriceEntity.getContractId())
                        .setTtId(contractPriceEntity.getTtId());
                ttAddDao.save(ttAddEntity);
            }
        }
        return Result.success();
    }

    @Override
    public List<TradeTicketEntity> queryListByContractCode(String contractCode) {
        return tradeTicketDao.getTradeTicketEntityByContractCode(contractCode);
    }

    @Override
    public List<TradeTicketEntity> queryIncompleteTTByContractId(Integer contractId, String groupId) {
        return tradeTicketDao.queryIncompleteTTByContractId(contractId, groupId);
    }

    @Override
    public void updateTTStatusById(Integer ttId, TTStatusEnum ttStatusEnum) {
        log.info("check_code_question  updateTTStatusById ");
        tradeTicketDao.updateStatusById(ttStatusEnum.getType(), null, ttId);
    }

    private void dealByTime(List<ReportVO> resultList, List<ReportVO> reportVOList, ContractTypeEnum contractTypeEnum, String factoryCode) {
        //按时间分组
        Map<String, List<ReportVO>> collect = reportVOList.stream().sorted(Comparator.comparing(ReportVO::getDeliveryStartTime)).collect(Collectors.groupingBy(ReportVO::getDeliveryStartTime));
        LinkedHashMap<String, List<ReportVO>> map = collect.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (s, s2) -> s,
                LinkedHashMap::new));
        for (Map.Entry<String, List<ReportVO>> entry : map.entrySet()) {
            ReportVO reportVO = new ReportVO();
            List<ReportVO> list = entry.getValue();
            BigDecimal totalPrice = list.stream().map(ReportVO::getUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            String averagePrice = totalPrice.divide(new BigDecimal(list.size()), 0, RoundingMode.HALF_UP).toPlainString();
            Map<Integer, List<ReportVO>> statusMap = list.stream().collect(Collectors.groupingBy(ReportVO::getStatus));
            BigDecimal newSize = statusMap.get(TTStatusEnum.NEW.getType()) == null ? BigDecimal.ZERO : statusMap.get(TTStatusEnum.NEW.getType()).stream().map(ReportVO::getChangeContractNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal approvingSize = statusMap.get(TTStatusEnum.APPROVING.getType()) == null ? BigDecimal.ZERO : statusMap.get(TTStatusEnum.APPROVING.getType()).stream().map(ReportVO::getChangeContractNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal doneSize = statusMap.get(TTStatusEnum.DONE.getType()) == null ? BigDecimal.ZERO : statusMap.get(TTStatusEnum.DONE.getType()).stream().map(ReportVO::getChangeContractNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal waitingSize = statusMap.get(TTStatusEnum.WAITING.getType()) == null ? BigDecimal.ZERO : statusMap.get(TTStatusEnum.WAITING.getType()).stream().map(ReportVO::getChangeContractNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal invalidSize = statusMap.get(TTStatusEnum.INVALID.getType()) == null ? BigDecimal.ZERO : statusMap.get(TTStatusEnum.INVALID.getType()).stream().map(ReportVO::getChangeContractNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal unsubmitSize = newSize.add(waitingSize).setScale(0, RoundingMode.HALF_UP);
            BigDecimal totalSize = newSize.add(approvingSize).add(doneSize).add(waitingSize).add(invalidSize).setScale(0, RoundingMode.HALF_UP);
            reportVO
                    .setAveragePrice(averagePrice)
                    .setFactoryCode(factoryCode)
                    .setContractTypeName(contractTypeEnum.getDesc())
                    .setDeliveryStartTime(entry.getKey())
                    .setUnsubmitSize(unsubmitSize)
                    .setApprovingSize(approvingSize)
                    .setDoneSize(doneSize)
                    .setInvalidSize(invalidSize)
                    .setTotalSize(totalSize)
                    .setContractType(contractTypeEnum.getValue())
            ;
            resultList.add(reportVO);
        }
    }

    @Override
    public TTAllStatusNumVO getTTStat(StatQueryDTO statQueryDTO) {
        String userId = JwtUtils.getCurrentUserId();
        statQueryDTO.setUserId(userId);
//        List<Integer> customerIdList = Collections.singletonList(-1);
//        PermissionBO permissionBO = employFacade.queryPermission(userId, Integer.parseInt(statQueryDTO.getGoodsCategoryId()));
//        if (CollectionUtils.isNotEmpty(permissionBO.getCustomerIdList())) {
//            customerIdList = permissionBO.getCustomerIdList();
//        }
//        statQueryDTO.setCustomerIdList(customerIdList);
        PermissionBO permissionBO = employFacade.querySitePermission(userId, Integer.parseInt(statQueryDTO.getGoodsCategoryId()));
//        Map<Integer, List<Integer>> companyCustomerIdMap = permissionBO.getCompanyCustomerIdMap();
//        if (companyCustomerIdMap.isEmpty()) {
//            companyCustomerIdMap.put(-1, Collections.singletonList(-1));
//        }
//        statQueryDTO.setCompanyCustomerIdMap(companyCustomerIdMap);
        statQueryDTO.setSiteCodeList(permissionBO.getSiteCodeList());
        TTAllStatusNumVO ttAllStatusNumVO = tradeTicketMapper.getTTStat(statQueryDTO);
        if (ttAllStatusNumVO == null) {
            ttAllStatusNumVO = new TTAllStatusNumVO(0, 0, 0, 0, 0);
        }
        return ttAllStatusNumVO;
    }


    private void recordTTOperation(String paramDTO, LogBizCodeEnum bizCodeEnum, String operationName, Object ttId, Integer logLevel) {
        String userId = JwtUtils.getCurrentUserId();
        String code = null;
        if (ttId != null) {
            code = tradeTicketDao.getTradeTicketEntityById((Integer) ttId).getCode();
        }
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(logLevel)
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(userId))
                .setOperationName(operationName)
                .setMetaData(paramDTO)
                .setData(paramDTO)
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId((Integer) ttId)
                .setReferBizCode(code);

        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }


    @Override
    public Result queryTTList(QueryDTO<TTQueryDTO> queryDTO) {
        String userId = JwtUtils.getCurrentUserId();
        Page<TradeTicketVOEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        TTQueryDTO ttQueryDTO = queryDTO.getCondition();
        //分页查询
        PermissionBO permissionBO = employFacade.querySitePermission(userId, Integer.parseInt(ttQueryDTO.getGoodsCategoryId()));
//        Map<Integer, List<Integer>> companyCustomerIdMap = permissionBO.getCompanyCustomerIdMap();
//        if (companyCustomerIdMap.isEmpty()) {
//            companyCustomerIdMap.put(-1, Collections.singletonList(-1));
//        }
        if (StringUtils.isNotBlank(ttQueryDTO.getCreateBy())) {
            ttQueryDTO.setCreateBy(userId);
        }
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        IPage<TradeTicketVOEntity> iPage = tradeTicketVODao.queryVOPageByTTQueryDTO(page, ttQueryDTO, permissionBO.getSiteCodeList());
        List<TTQueryVO> ttQueryVOList = iPage.getRecords().stream().map(i -> {
                    ContractEntity contractEntity = contractQueryService.getBasicContractById(i.getContractId());
                    TTQueryVO ttQueryVO = new TTQueryVO();
                    BeanUtils.copyProperties(i, ttQueryVO);
                    ttQueryVO
                            .setInvalidReason(i.getInvalidReason())
                            .setTtId(i.getId())
                            .setSignId(i.getSignId())
                            .setProtocolCode(i.getProtocolCode())
                    ;
                    //创建人
                    if (null != i.getCreatedBy()) {
                        EmployEntity employEntity = employFacade.getEmployById(i.getCreatedBy());
                        if (null != employEntity) {
                            ttQueryVO.setCreateBy(employEntity.getName());
                        }
                    }
                    //商品名称
                    SkuEntity skuDTO = skuFacade.getSkuById(i.getGoodsId());
                    if (skuDTO != null) {
                        ttQueryVO.setGoodsName(skuDTO.getFullName());
                    }
                    // 包装名称
                    AttributeValueEntity packageValueEntity = attributeFacade.getAttributeValueById(i.getGoodsPackageId());
                    if (packageValueEntity != null) {
                        ttQueryVO.setGoodsPackageName(packageValueEntity.getName());
                    }
                    // 规格名称
                    AttributeValueEntity specValueEntity = attributeFacade.getAttributeValueById(i.getGoodsSpecId());
                    if (specValueEntity != null) {
                        ttQueryVO.setGoodsSpecName(specValueEntity.getName());
                    }
                    // 发货库点
                    FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(i.getShipWarehouseId());
                    if (null != factoryWarehouseEntity) {
                        ttQueryVO.setShipWarehouseName(factoryWarehouseEntity.getName());
                    }
                    // 带皮扣重
                    if (StringUtils.isNotBlank(i.getPackageWeight())) {
                        SystemRuleItemEntity pacSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getPackageWeight()));
                        ttQueryVO.setPackageWeight(pacSystemRuleItemEntity != null ? pacSystemRuleItemEntity.getRuleKey() : null);
                    }

                    // 总数量
                    ttQueryVO.setContractNum(BigDecimalUtil.isZero(i.getChangeContractNum()) ? i.getAfterContractNum() : i.getChangeContractNum());

                    // 交提货方式
                    DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(i.getDeliveryType());
                    if (null != deliveryTypeEntity) {
                        ttQueryVO.setDeliveryType(deliveryTypeEntity.getName());
                    }

                    // 期货合约&基差价
                    if (StringUtils.isBlank(i.getDomainCode()) && contractEntity != null) {
                        i.setDomainCode(contractEntity.getDomainCode());
                    }
                    ttQueryVO.setDomainCode(getDomainCode(i.getSubGoodsCategoryId(), i.getDomainCode(), i.getExtraPrice()));

                    // 点价截止时间
                    if (i.getPriceEndTime() != null && DateTimeUtil.isDate(i.getPriceEndTime())) {
                        ttQueryVO.setPriceEndTime(i.getPriceEndTime().split(" ")[0]);
                    }

                    // 客户集团名称
                    ttQueryVO.setEnterpriseName(i.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? i.getEnterpriseName() : i.getSupplierEnterpriseName());

                    //查询协议状态
                    if (String.valueOf(TTStatusEnum.WAITING.getType()).equalsIgnoreCase(queryDTO.getCondition().getStatus())) {
                        //待修改提交区的协议为异常状态
                        ttQueryVO.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
                    } else {
                        ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(i.getId());
                        if (contractSignDetail != null) {
                            Integer status = contractSignDetail.getStatus();
                            ttQueryVO.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());

                            //根据合同状态判断按钮显示
                            ttQueryVO.setInvalidStatus(0);
                            ttQueryVO.setCancelStatus(0);
                            if (newTypeList.contains(i.getType())) {
                                if (cancelList.contains(status)) {
                                    ttQueryVO.setCancelStatus(1);
                                }
                                if (invalidList.contains(status)) {
                                    ttQueryVO.setInvalidStatus(1);
                                }
                            } else {
                                //根据协议状态判断撤销按钮的显示
                                if (!forbiddenCancelTypeList.contains(i.getType()) && status < ContractSignStatusEnum.WAIT_CONFIRM.getValue()) {
                                    ttQueryVO.setCancelStatus(1);
                                }
                            }
                        }
                    }

                    if (contractEntity != null) {
                        Integer status = contractEntity.getStatus();
                        ttQueryVO.setContractStatus(ContractStatusEnum.getDescByValue(status));
                    }
                    companyEntityList.stream().peek(k -> {
                        if (k.getId().equals(i.getCompanyId())) {
                            ttQueryVO.setCompanyName(k.getShortName());
                        }
                    });
                    return ttQueryVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, ttQueryVOList);
    }

    /**
     * 获取合同列表的主力合约
     *
     * @param goodsCategoryId
     * @param domainCode
     * @param extraPrice
     * @return
     */
    private String getDomainCode(Integer goodsCategoryId, String domainCode, BigDecimal extraPrice) {
        String prefix = "M";
        if (goodsCategoryId.equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
            prefix = "Y";
        }
        if (extraPrice == null) {
            return prefix + domainCode;
        }

        return BigDecimalUtil.isGreaterEqualThanZero(extraPrice) ?
                prefix + domainCode + "+" + extraPrice.stripTrailingZeros().toPlainString() :
                prefix + domainCode + extraPrice.stripTrailingZeros().toPlainString();
    }

    @Override
    public boolean isOccupy(Integer ttId) {
        TradeTicketEntity tradeTicket = tradeTicketDao.getById(ttId);
        if (null == tradeTicket || null == tradeTicket.getOccupyStatus())
            return false;
        return tradeTicket.getOccupyStatus() == 1 ? true : false;
    }

    @Override
    public void setOccupyStatus(Integer ttId, boolean isOccupy) {
        tradeTicketDao.updateOccupyStatusById(ttId, isOccupy);
    }

    @Override
    public TTAddEntity getLkgTTAddByTTId(Integer ttId) {
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        if (null == ttAddEntity) {
            TradeTicketEntity ticketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
            if (ticketEntity == null) {
                return null;
            }
            TTAddEntity addEntity = BeanUtil.toBean(ticketEntity, TTAddEntity.class);
            addEntity.setTtId(ttId);
            return addEntity;
        }
        return ttAddEntity;
    }

    @Override
    public TTModifyEntity getLkgTTModifyByTTId(Integer ttId) {
        TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
        if (null == ttModifyEntity) {
            TradeTicketEntity ticketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
            TTModifyEntity modifyEntity = BeanUtil.toBean(ticketEntity, TTModifyEntity.class);
            modifyEntity.setTtId(ttId);
            return modifyEntity;
        }
        return ttModifyEntity;
    }

}
