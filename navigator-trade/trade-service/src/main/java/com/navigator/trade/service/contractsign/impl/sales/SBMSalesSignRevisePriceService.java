package com.navigator.trade.service.contractsign.impl.sales;

import com.navigator.trade.service.contractsign.impl.BaseAbstractContractSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022-02-18 11:37
 */
@Slf4j
@Component("SBM_S_SIGN_REVERSE_PRICE,SBO_S_SIGN_REVERSE_PRICE")
public class SBMSalesSignRevisePriceService  extends BaseAbstractContractSignService {
}
