package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.TTCustomerTradeStatusEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import com.navigator.trade.service.IDeliveryTypeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class TTLogicCheckHandler {

    @Autowired
    TradeDomainRemoteService tradeDomainRemoteService;

    @Autowired
    IDeliveryTypeService iDeliveryTypeService;

    @Value("${tt.residual.risk.switcher}")
    private Boolean residualRiskSwitcher;

    /**
     * TT提交逻辑校验
     * @param tradeTicketEntity
     * @param ttAddEntity
     * @return
     */
    String ttSubmitLogicCheck(TradeTicketEntity tradeTicketEntity , TTAddEntity ttAddEntity ,  ContractPriceEntity contractPriceEntity) {

        // 逻辑校验的失败信息说明
        StringBuilder sb = new StringBuilder();
        //信息不完整则失败,批量提交的时候有效,单个提交前端已经校验了
        if (0 == ttAddEntity.getCompletedStatus()) {
            sb.append("信息填写不完整,请填写完整后再提交");
            return sb.toString();
        }
        //1. 校验客户是否有效
        Integer customerId = tradeTicketEntity.getCustomerId();
        if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
            customerId = tradeTicketEntity.getSupplierId();
        }
        CustomerEntity customerEntity = tradeDomainRemoteService.getCustomerEntity(customerId);
        if (DisableStatusEnum.DISABLE.getValue().equals(customerEntity.getStatus())) {
            sb.append("无法获取该客户状态，TT无法提交，请检查客户主数据");
            return sb.toString();
        }

        // 2.采购需要校验账户的有效性
        if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
            CustomerBankEntity customerBankEntity = tradeDomainRemoteService.queryCustomerBankById(tradeTicketEntity.getBankId());
            if (customerBankEntity != null && DisableStatusEnum.DISABLE.getValue().equals(customerBankEntity.getStatus())) {
                sb.append("请检查客户配置客户账户是否正确");
                return sb.toString();
            }
        }

        //3. 《RR 处理逻辑》 TODO写在下面
        if (null != residualRiskSwitcher && residualRiskSwitcher
                && TTTypeEnum.NEW.getType().equals(tradeTicketEntity.getType())
                && BuCodeEnum.ST.getValue().equals(tradeTicketEntity.getBuCode())) {
            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                CrisGlobalEntity crisGlobalEntity = tradeDomainRemoteService.getCustomerResidualRiskInfo(tradeTicketEntity.getCustomerId());
                if (null == crisGlobalEntity) {
                    sb.append("无法获取该客户RR limit数据，TT无法提交，请检查客户主数据");
                    return sb.toString();
                }
                //剩余风险使用
                BigDecimal riskResidue = crisGlobalEntity.getResidualRiskResidue();
                if (!TTCustomerTradeStatusEnum.getCommonTradeStatusList().contains(crisGlobalEntity.getTradeStatus())) {
                    sb.append(ResultCodeEnum.RISK_RESIDUAL_NOT_GET.msg());
                    return sb.toString();
                } else if (TTCustomerTradeStatusEnum.NO_TRADE.getValue().equals(crisGlobalEntity.getTradeStatus())) {
                    sb.append(ResultCodeEnum.RISK_RESIDUAL_CUSTOMER_NO_TRADE.msg());
                    return sb.toString();
                } else if (TTCustomerTradeStatusEnum.getJudgeRrNumTradeStatusList().contains(crisGlobalEntity.getTradeStatus()) && riskResidue.compareTo(BigDecimal.ZERO) < 0) {
                    // Residue ≤ 0且RR Usage > 150kUSD时，若超额则报错提示：“该客户RR limit已超过平台最大限额，请联系风控部门提额后提交”
                    BigDecimal riskUsage = crisGlobalEntity.getResidualRiskUsage();
                    if (riskUsage.compareTo(new BigDecimal(150)) > 0) {
                        sb.append(ResultCodeEnum.RISK_RESIDUAL_USAGE_OVER.msg());
                        return sb.toString();
                    }
                    if (riskUsage.compareTo(new BigDecimal(150)) <= 0) {
                        sb.append(ResultCodeEnum.RISK_RESIDUAL_USAGE_OFF.msg());
                        return sb.toString();
                    }
                }
            }
        }

        //4. 校验SKU是否有效
        SkuEntity skuEntity =  tradeDomainRemoteService.getSkuEntity(ttAddEntity.getGoodsId());
        if (skuEntity == null || DisableStatusEnum.DISABLE.getValue().equals(skuEntity.getStatus())) {
            sb.append("请检查货品配置是否正确");
            return sb.toString();
        }
        //5. 校验该SKU的品种是否绑定选定帐套 -- TODO 这个应该不需要
        SiteEntity siteEntity = tradeDomainRemoteService.getSiteDetailByCode(tradeTicketEntity.getSiteCode());
        if (siteEntity == null || !siteEntity.getCategory3().contains(tradeTicketEntity.getCategory3().toString())) {
            sb.append("请检查货品配置账套是否正确");
            return sb.toString();
        }
        //6. 校验客户配置-赊销&预付是否有效
        CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
        creditPaymentDTO.setCustomerId(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                        ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId())
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setCompanyId(tradeTicketEntity.getCompanyId())
                .setCategory1(String.valueOf(tradeTicketEntity.getCategory1()))
                .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                .setCategory3(String.valueOf(tradeTicketEntity.getCategory3()))
                .setPaymentType(ttAddEntity.getPaymentType())
                .setBuCode(tradeTicketEntity.getBuCode())
                .setIsSales(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                .setIsProcurement(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
        // 交易类型是线下的取现货的
//        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(ttAddEntity.getWarrantTradeType())) {
//            creditPaymentDTO.setBuCode(BuCodeEnum.ST.getValue());
//        }
        // 线下交易所仓单 + 自行结算 取现货配置
        creditPaymentDTO.setBuCode(getBuCode(tradeTicketEntity.getBuCode(), ttAddEntity.getWarrantTradeType(), ttAddEntity.getSettleType()));

        Result result = tradeDomainRemoteService.customerCreditPayment(creditPaymentDTO);
        if (result.isSuccess()) {
            List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
            if (CollectionUtil.isEmpty(creditPaymentVOList)) {
                sb.append("请检查客户的赊销预付款配置是否正确");
                return sb.toString();
            } else {
                CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                if (ttAddEntity.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                    sb.append("请检查客户的赊销预付款配置是否正确");
                    return sb.toString();
                }
            }
        }
        //7. 校验业务配置-袋皮扣重配置是否有效
        if (StringUtils.isNotBlank(ttAddEntity.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = tradeDomainRemoteService.getRuleItemById(Integer.parseInt(ttAddEntity.getPackageWeight()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                sb.append("请检查袋皮扣重配置是否正确");
                return sb.toString();
            }
        }

        //8. 保证金比例的校验
        CustomerDepositRateDTO customerDepositRateDTO = new CustomerDepositRateDTO();
        customerDepositRateDTO.setCustomerId(customerId)
                .setDepositRate(ttAddEntity.getDepositRate())
                .setInvoicePaymentRate(ttAddEntity.getInvoicePaymentRate())
                .setCategory1(String.valueOf(tradeTicketEntity.getCategory1()))
                .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                .setBuCode(tradeTicketEntity.getBuCode())
                .setStatus(DisableStatusEnum.ENABLE.getValue());
        // 交易类型是线下的取现货的
//        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(ttAddEntity.getWarrantTradeType())) {
//            customerDepositRateDTO.setBuCode(BuCodeEnum.ST.getValue());
//        }
        // 线下交易所仓单 + 自行结算 取现货配置
        customerDepositRateDTO.setBuCode(getBuCode(tradeTicketEntity.getBuCode(), ttAddEntity.getWarrantTradeType(), ttAddEntity.getSettleType()));

        if(tradeTicketEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            customerDepositRateDTO.setIsSales(GeneralEnum.YES.getValue());
        }else {
            customerDepositRateDTO.setIsProcurement(GeneralEnum.YES.getValue());
        }
        if (ContractTypeEnum.YI_KOU_JIA.getValue() != tradeTicketEntity.getContractType()) {
            customerDepositRateDTO.setPricingDepositRate(ttAddEntity.getAddedDepositRate());
        }
        List<CustomerDepositRateEntity> customerDepositRateEntities = tradeDomainRemoteService.queryCustomerDepositRate(customerDepositRateDTO);
        if (ObjectUtil.isEmpty(customerDepositRateEntities) || customerDepositRateEntities.size() < 0) {
            sb.append("请检客户履约保证金配置是否正确");
            return sb.toString();
        }

        //9. 校验通用配置-付款条件代码，业务类型=现货且是否有效
        if (tradeTicketEntity.getPayConditionId() != null) {
            String payCondition = checkPaymentCode(tradeTicketEntity.getPayConditionId(),tradeTicketEntity.getBuCode(), ttAddEntity.getWarrantTradeType(), ttAddEntity.getSettleType());
            if (ObjectUtil.isNotEmpty(payCondition)) {
                sb.append(payCondition+",");
                return sb.toString();
            }
        }
        // 9 . 二级品类=豆粕/豆油且基差基准价或蛋白价差超阈值超出了 备注的校验 TODO 后处理
        //10. 校验通用配置-交提货方式配置是否有效
        if (ObjectUtil.isNotEmpty(ttAddEntity.getDeliveryType())) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttAddEntity.getDeliveryType());
            if (ObjectUtil.isEmpty(deliveryTypeEntity) || DisableStatusEnum.DISABLE.getValue().equals(deliveryTypeEntity.getStatus())) {
                sb.append("请检查交提货方式配置是否正确");
                return sb.toString();
            }
        }
        //11. 校验业务配置-重量验收是否有效
        if (StringUtils.isNotBlank(ttAddEntity.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = tradeDomainRemoteService.getRuleItemById(Integer.parseInt(ttAddEntity.getWeightCheck()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                sb.append("请检查重量验收配置是否正确");
                return sb.toString();
            }
        }

        //12.TODO 校验通用配置-库点，库点属性 不需要 | 校验该库点是否绑定选定帐套 前端绑定了 仓单必须是"是交割库"
        if (ObjectUtil.isNotEmpty(ttAddEntity.getShipWarehouseId())) {
            WarehouseEntity warehouseEntity = tradeDomainRemoteService.getFactoryWarehouse(ttAddEntity.getShipWarehouseId());
            if(BuCodeEnum.WT.getValue().equals(tradeTicketEntity.getBuCode())) {
                if (null == warehouseEntity
                        || 0 == warehouseEntity.getIsDce()
                        || DisableStatusEnum.DISABLE.getValue().equals(warehouseEntity.getStatus())
                        || ObjectUtil.isEmpty(warehouseEntity.getSiteCodes())
                        || (ObjectUtil.isNotEmpty(warehouseEntity.getSiteCodes()) && !warehouseEntity.getSiteCodes().contains(tradeTicketEntity.getSiteCode()))) {
                    sb.append("请检查库点配置信息是否有效，库点是否关联账套");
                    return sb.toString();
                }
            } else {
                if (null == warehouseEntity
                        || DisableStatusEnum.DISABLE.getValue().equals(warehouseEntity.getStatus())
                        || ObjectUtil.isEmpty(warehouseEntity.getSiteCodes())
                        || (ObjectUtil.isNotEmpty(warehouseEntity.getSiteCodes()) && !warehouseEntity.getSiteCodes().contains(tradeTicketEntity.getSiteCode()))) {
                    sb.append("请检查库点配置信息是否有效，库点是否关联账套");
                    return sb.toString();
                }
            }
        }

        // 13. 校验质量指标是否有效
        // 豆粕|副产品  基于帐套的工厂+货品+发货库点+用途+采销 匹配 业务配置-质量指标条款是否有效
        // 特种油脂 基于帐套的工厂+货品+企标+发货库点+用途+采销 匹配 业务配置-质量指标条款是否有效
        QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        qualityInfoDTO
                .setGoodsCategoryId(ttAddEntity.getGoodsCategoryId())
                .setFactoryCode(ttAddEntity.getDeliveryFactoryCode())
                .setWarehouseId(ttAddEntity.getShipWarehouseId())
                .setUsage(tradeTicketEntity.getUsage())
                .setGoodsId(ttAddEntity.getGoodsId())
                .setSalesType(tradeTicketEntity.getSalesType())
                .setCustomerId(customerId);
        // 特种油脂
        if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(tradeTicketEntity.getCategory2())
                && BuCodeEnum.ST.getValue().equals(tradeTicketEntity.getBuCode())) {
            if ("国标".equals(ttAddEntity.getStandardType()) || ObjectUtil.isEmpty(ttAddEntity.getStandardRemark()))  {
                qualityInfoDTO.setStandardType(ttAddEntity.getStandardType());
            }
        }
        Boolean existQuality = tradeDomainRemoteService.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            sb.append("请检查发货库点配置质量指标配置是否正确");
            return sb.toString();
        }


        // 14.验证发票信息
        List<CustomerInvoiceEntity> customerInvoiceEntities = tradeDomainRemoteService.
                queryCustomerInvoiceList(customerId,tradeTicketEntity.getCategory1(),tradeTicketEntity.getCategory2(),tradeTicketEntity.getCategory3(),tradeTicketEntity.getCompanyId());
        if (customerInvoiceEntities.isEmpty()) {
            sb.append("请检查客户配置发票信息是否正确");
            return sb.toString();
        }

        // 15.校验客户的银行账户信息 add by zengshl
        FactoryEntity factoryEntity = tradeDomainRemoteService.getFactoryByCode(ttAddEntity.getDeliveryFactoryCode());
        Integer factoryId = null != factoryEntity ? factoryEntity.getId() : null;
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO().setCustomerId(customerId)
                .setCategoryId(tradeTicketEntity.getSubGoodsCategoryId())
                .setSalesType(tradeTicketEntity.getSalesType())
                .setFactoryCode(ttAddEntity.getDeliveryFactoryCode())
                .setCompanyId(tradeTicketEntity.getCompanyId())
                .setFactoryId(factoryId)
                .setCategory2(String.valueOf(tradeTicketEntity.getCategory2()))
                .setCategory3(String.valueOf(tradeTicketEntity.getCategory3()));
        CustomerDTO customerDTO = tradeDomainRemoteService.queryCustomerAllMessage(customerAllMessageDTO);
        if (ObjectUtil.isEmpty(customerDTO.getCustomerBankDTOS()) || customerDTO.getCustomerBankDTOS().size() <= 0){
            sb.append("请检查客户配置银行信息是否正确");
            return sb.toString();
        }


        return sb.toString();
    }

    /**
     * 检查付款条件代码
     * @param payConditionId
     */
    private String checkPaymentCode(Integer payConditionId, String buCode, Integer warrantTradeType, String settleType) {
        Result<PayConditionEntity> payCondition = tradeDomainRemoteService.getPayConditionById(payConditionId);
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                 return ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE.getMsg();
            }
            // 交易类型是线下的取现货的
//            if (!payConditionEntity.getBuCode().equals(buCode) && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(warrantTradeType)) {
//                return ResultCodeEnum.PAY_CONDITION_IS_NOT_BUCODE.getMsg();
//            }
            // 线下交易所仓单 + 自行结算 取现货配置
            if (!payConditionEntity.getBuCode().equals(getBuCode(buCode, warrantTradeType, settleType))) {
                return ResultCodeEnum.PAY_CONDITION_IS_NOT_BUCODE.getMsg();
            }
            return null;
        } else {
                return ResultCodeEnum.PAY_CONDITION_IS_NOT_EXIST.getMsg();
        }
    }


    /**
     * 基差价差预警
     * @param tradeTicketEntity
     * @return
     */
    public boolean LOAPriceWarning(TradeTicketEntity tradeTicketEntity , TTAddEntity ttAddEntity , ContractPriceEntity contractPriceEntity) {

        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getSubGoodsCategoryId());
        systemRuleDTO.setFactoryCode(ttAddEntity.getDeliveryFactoryCode());
        systemRuleDTO.setCompanyId(tradeTicketEntity.getCompanyId().toString());
        systemRuleDTO.setGoodsId(GoodsCategoryEnum.OSM_OIL.getValue().equals(ttAddEntity.getGoodsSpecId()) ? ttAddEntity.getGoodsSpecId() : null);
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(ttAddEntity.getDeliveryStartTime()));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = tradeDomainRemoteService.filterBasicPrice(systemRuleDTO);

        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return false;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于基差价
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //基差价
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());
            BigDecimal extraPrice = ruleValue.subtract(memo);
            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) < 0;
            } else {
                return contractPriceEntity.getExtraPrice().compareTo(extraPrice) > 0;
            }
        }
        return false;
    }


    /**
     * 蛋白价价差
     * @param tradeTicketEntity
     * @param ttAddEntity
     * @return
     */
    public BigDecimal LOAProteinDiffPrice(TradeTicketEntity tradeTicketEntity , TTAddEntity ttAddEntity ,  ContractPriceEntity contractPriceEntity) {

        String deliveryFactoryCode = null;
        Integer goodsSpecId = null;
        Integer goodsCategoryId = null;
        Integer goodsId = null;
        Date deliveryStartTime = null;

        deliveryFactoryCode = ttAddEntity.getDeliveryFactoryCode();
        goodsSpecId = ttAddEntity.getGoodsSpecId();
        goodsCategoryId = ttAddEntity.getGoodsCategoryId();
        goodsId = ttAddEntity.getGoodsId();
        deliveryStartTime = ttAddEntity.getDeliveryStartTime();

        BasicPriceConfigQueryDTO systemRuleDTO = new BasicPriceConfigQueryDTO();
        systemRuleDTO.setCategoryId(tradeTicketEntity.getSubGoodsCategoryId());
        systemRuleDTO.setFactoryCode(deliveryFactoryCode);
        systemRuleDTO.setAttributeValueId(goodsSpecId);
        systemRuleDTO.setGoodsId(GoodsCategoryEnum.OSM_OIL.getValue().equals(goodsCategoryId) ? goodsId : null);
        systemRuleDTO.setDeliveryBeginDate(DateTimeUtil.formatDateMMString(deliveryStartTime));
        systemRuleDTO.setDomainCode(tradeTicketEntity.getDomainCode());
        Result result = tradeDomainRemoteService.filterBasicProtein(systemRuleDTO);

        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);

        if (null == systemRuleItemEntity) {
            return BigDecimal.ZERO;
        }

        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于蛋白价差范围预警值
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //蛋白价差
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());

            BigDecimal proteinPrice = ruleValue.subtract(memo);
            if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) < 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            } else {
                return contractPriceEntity.getProteinDiffPrice().compareTo(proteinPrice) > 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取buCode
     *
     * @param buCode           业务编码
     * @param warrantTradeType 仓单交易类型
     * @param settleType       结算类型
     * @return buCode
     */
    private String getBuCode(String buCode, Integer warrantTradeType, String settleType) {
        // 线下交易所仓单 + 自行结算 取现货配置
        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(warrantTradeType)) {
            if (String.valueOf(SettleType.AUTO_SELF.getValue()).equals(settleType)) {
                buCode = BuCodeEnum.ST.getValue();
            }
        }
        return buCode;
    }
}
