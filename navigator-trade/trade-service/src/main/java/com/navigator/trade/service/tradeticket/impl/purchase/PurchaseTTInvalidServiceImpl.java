package com.navigator.trade.service.tradeticket.impl.purchase;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.service.tradeticket.impl.sales.SalesTTAddServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Slf4j
@Component("SBM_P_TT_INVALID,SBO_P_TT_INVALID")
public class PurchaseTTInvalidServiceImpl extends SalesTTAddServiceImpl {
    public PurchaseTTInvalidServiceImpl() {
        ttTypeEnum = TTTypeEnum.INVALID;
        contractTradeTypeEnum = ContractTradeTypeEnum.INVALID;
        contractSource = ContractActionEnum.INVALID.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.PURCHASE;
        processorType = ProcessorTypeEnum.SBM_P_INVALID.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_P_INVALID.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = tradeTicketConvertUtil.getClosedTTDetailVO(ttId, tradeTicketEntity, "5");
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        TTQueryDetailVO ttQueryDetailVO = BeanConvertUtils.map(TTQueryDetailVO.class, tradeTicketEntity);
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(ttAddEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(ttAddEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttAddEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttAddEntity.getSupplierId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        //买家
        if (null != ttAddEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttAddEntity.getCustomerId()));
        }

        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }
        if (null != ttAddEntity.getGoodsPackageId()) {
            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttAddEntity.getGoodsPackageId()));
        }
        if (null != ttAddEntity.getGoodsSpecId()) {
            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttAddEntity.getGoodsSpecId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());

        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);


        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        //原合同信息
        ttQueryDetailVO.setRootContractId(tradeTicketEntity.getSourceContractId());
        if (null != tradeTicketEntity.getSourceContractId()) {
            ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getSourceContractId());
            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_PURCHASE_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        // BUGFIX：case-1002684 合同关闭TT详情不正确 Author: Mr 2024-07-16 Start
        // ttDetailVO.setDetailType("0");
        // BUGFIX：case-1002684 合同关闭TT详情不正确 Author: Mr 2024-07-16 End
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        return ttDetailVO;
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        return initPurchaseDTOCommon(ttdto);
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }


    @Override
    public void handleAfterApproving(Result result, Integer ttType, String ttCode, String memo) {
        String json = JSON.toJSONString(result.getData());
        ApproveResultDTO approveResultDTO = JSON.parseObject(json, ApproveResultDTO.class);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(ttCode);
        Integer approveResult = approveResultDTO.getApproveResult();
        String procInstStatus = approveResultDTO.getProcInstStatus();
        //根据审批结果更新tt状态
        if (approveResult == ApproveResultEnum.AGREE.getValue()) {
            tradeTicketDao.updateApprovalStatusByCode(TTStatusEnum.APPROVING.getType(), TTApproveStatusEnum.APPROVE.getValue(), tradeTicketEntity.getCode());

            //更新合同状态
            ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
            contractModifyDTO.setTtId(tradeTicketEntity.getId());
            contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
            contractModifyDTO.setTtType(tradeTicketEntity.getType());
            contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
            if (tradeTicketEntity.getType().equals(TTTypeEnum.REVISE.getType())) {
                contractModifyDTO.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            }
            salesContractOperationService.updateContractByApproved(contractModifyDTO);
        }
        //审批驳回
        if (approveResult == ApproveResultEnum.REJECT.getValue()) {
            handleCancelOrRejectResult(tradeTicketEntity, memo);
        }

        if (approveResult == ApproveResultEnum.APPROVING.getValue()) {
            handleApprovingResult(tradeTicketEntity, procInstStatus);
        }
    }
}
