package com.navigator.trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.trade.pojo.dto.contract.ContractMdmInfoDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 合同表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
public interface ContractMapper extends BaseMapper<ContractEntity> {

    @Update("UPDATE contract " +
            "SET contract.status = 7, " +
            "    contract.contract_close_type = 'EXECUTE_CLOSE', " +
            "    contract.memo = (CASE WHEN contract.memo IS NULL OR contract.memo = '' " +
            "                         THEN '' " +
            "                         ELSE contract.memo + '; ' " +
            "                    END) + CONVERT(varchar, DATEADD(hour, 8, GETDATE()), 120) + '合同关闭程序更新为已关闭' " +
            "FROM dbt_contract contract " +
            "WHERE contract.is_deleted = 0 " +
            "  AND contract.contract_code = #{contractCode} " +
            "  AND contract.site_code = #{siteCode}")
    Integer closedBySiteCodeAndContractCode(String siteCode, String contractCode);

    @Select("SELECT TOP 1 " +
            "contract.contract_code AS contract_code, " +
            "site.atlas_code as business_entity, " +
            "sku_mdm.mdm_id AS commodityCode, " +
            "delivery_type.atlas_code AS contract_terms " +
            "FROM " +
            "dbt_contract contract " +
            "LEFT JOIN dba_site site ON contract.site_code = site.code " +
            "LEFT JOIN dbg_sku_mdm sku_mdm ON contract.goods_id = sku_mdm.sku_id AND sku_mdm.type = 1 " +
            "LEFT JOIN dbt_delivery_type  delivery_type on delivery_type.id = contract.delivery_type " +
            "WHERE contract.id = #{contractId} " +
            "ORDER BY contract.id DESC")
    ContractMdmInfoDTO getContractMdmInfo(Integer contractId);

    @Select("SELECT TOP 1 " +
            "contract.contract_code AS contract_code, " +
            "site.atlas_code as business_entity, " +
            "sku_mdm.mdm_id AS commodityCode, " +
            "delivery_type.atlas_code AS contract_terms " +
            "FROM " +
            "dbt_contract_history contract " +
            "LEFT JOIN dba_site site ON contract.site_code = site.code " +
            "LEFT JOIN dbg_sku_mdm sku_mdm ON contract.goods_id = sku_mdm.sku_id AND sku_mdm.type = 1 " +
            "LEFT JOIN dbt_delivery_type  delivery_type on delivery_type.id = contract.delivery_type " +
            "WHERE contract.contract_id = #{contractId} " +
            "ORDER BY contract.id DESC")
    ContractMdmInfoDTO getContractHisMdmInfo(Integer contractId);

    // adding batch TT creation group_id field by Jason Shi at 2025-01-06 start
    /**
     * Get next group ID from database sequence
     * This method is thread-safe and prevents race conditions
     *
     * @return next group ID from sequence
     */
    @Select("SELECT NEXT VALUE FOR dbo.group_id_seq")
    Integer getNextGroupId();
    // adding batch TT creation group_id field by Jason Shi at 2025-01-06 end

}
