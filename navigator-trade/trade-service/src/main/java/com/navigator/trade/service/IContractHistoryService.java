package com.navigator.trade.service;

import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;

/**
 * <p>
 * 合同历史表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface IContractHistoryService {

    void backupContract(ContractEntity contractEntity, String backTradeType, String referCode);

    ContractEntity getContractEntity(Integer contractId, Integer mainVersion);

    ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion);
}
