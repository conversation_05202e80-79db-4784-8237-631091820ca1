package com.navigator.trade.service.impl;

import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.ContractSignDetailDao;
import com.navigator.trade.pojo.entity.ContractSignDetailEntity;
import com.navigator.trade.service.ContractSignDetailServer;
import feign.Contract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/26
 */

@Slf4j
@Service
public class ContractSignDetailServerImpl implements ContractSignDetailServer {

    @Resource
    private ContractSignDetailDao contractSignDetailDao;


    @Override
    public void saveContractSignDetail(ContractSignDetailEntity contractSignDetailEntity) {
        contractSignDetailDao.save(contractSignDetailEntity);
    }
}
