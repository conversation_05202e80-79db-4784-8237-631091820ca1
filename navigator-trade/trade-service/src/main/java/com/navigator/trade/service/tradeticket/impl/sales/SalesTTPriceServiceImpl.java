package com.navigator.trade.service.tradeticket.impl.sales;

import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.future.enums.AllocateTypeEnum;
import com.navigator.future.enums.ContraryStatusEnum;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.pojo.dto.contract.SyncContractDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.OperateTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component("SBM_S_TT_PRICE,SBO_S_TT_PRICE")
public class SalesTTPriceServiceImpl extends BaseTradeTicketAbstractService {
    public SalesTTPriceServiceImpl() {
        ttTypeEnum = TTTypeEnum.PRICE;
        contractTradeTypeEnum = ContractTradeTypeEnum.PRICE;
        contractSource = ContractActionEnum.PRICE_CONFIRM.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.SALES;
        processorType = ProcessorTypeEnum.SBM_S_PRICE.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_PRICE.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getPriceTradeTicketEntity(ttdto.getSalesContractTTPriceDTO());
        injectionProperty(tradeTicketEntity);
        return tradeTicketEntity;
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) {
        saveTTPrice(ttDto, ttId);
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getPriceContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttdto.getSalesContractTTPriceDTO();
        //初始化交易、销售类型、合同来源
        salesContractTTPriceDTO.setStatus(status);
        salesContractTTPriceDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesContractTTPriceDTO.setSalesType(salesType.getValue());

        //协议签署状态
        salesContractTTPriceDTO.setContractSignatureStatus(contractSignatureStatus);

        ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTPriceDTO.getContractId());

        //生成TT编号
        String code = null;
        salesContractTTPriceDTO.setContractSource(contractSource);
        //主体未变更生成TT编号
        code = getSalesTTCode(contractEntity.getId());
        salesContractTTPriceDTO.setCode(code);
        ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

        return ttdto;
    }

    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        return null;
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = tradeTicketConvertUtil.getPriceTTDetailVO(ttId, tradeTicketEntity);
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());
        return ttDetailVO;
    }

    private void saveTTPrice(TTDTO ttDto, Integer ttId) {
        contractSignConvertUtil.saveTTPrice(ttDto, ttId);
    }


    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        return "";
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {

    }

    @Override
    public void cancel(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        //取消工作流审批
        cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
        log.info("check_code_question  cancel ");
        //修改tt状态为待修改提交
        tradeTicketDao.updateStatusById(TTStatusEnum.WAITING.getType(), null, operateTTDTO.getTtId());
        //取消协议
        cancelContractSign(tradeTicketEntity, operateTTDTO.getMemo());
    }

    /**
     * 根据申请单id作废定价单
     *
     * @param contraryPriceDTO
     */
    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Result contraryPrice(ContraryPriceDTO contraryPriceDTO) {
        PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(String.valueOf(contraryPriceDTO.getAllocateId()));

        /*if (AllocateTypeEnum.ALL.getValue() == priceAllocateEntity.getType()) {
            return priceAllocateContraryAll(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        } else {
            return priceAllocateContraryPortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        }*/

        return priceAllocateContraryPortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());

    }

    /**
     * 部分撤回
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result priceAllocateContraryPortion(PriceAllocateEntity priceAllocateEntity, String contraryCause) {
        String userId = JwtUtils.getCurrentUserId();
        //撤回点价
        //部分点价
        TTPriceEntity ttPriceEntity = ttPriceService.getTTPriceByAllocateId(priceAllocateEntity.getId());
        //遍历定价单

        Integer ttId = ttPriceEntity.getTtId();
        //校验协议是否已经签署
        ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttPriceEntity.getTtId());
        //已签署不予撤回
        /*if (ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
        }*/

        //查询定价单是否已被拆分
        List<TTPriceEntity> ttPrices = ttPriceService.getTTPriceBySourceId(ttPriceEntity.getId());
        //拆分后不予撤回
        if (!ttPrices.isEmpty()) {
            //不予撤回
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_SPLIT_NOT_CONTRARY);
        }
        //1,撤回定价单
        BigDecimal priceApplyNum = ttPriceEntity.getNum();

        ttPriceEntity.setNum(BigDecimal.ZERO)
                .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                .setMemo(contraryCause);
        ttPriceService.updateTTPriceById(ttPriceEntity);
        //2,撤回TT
        //修改tt状态为待修改提交
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttPriceEntity.getTtId());
        tradeTicketEntity.setCancelReason(contraryCause)
                .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                .setStatus(TTStatusEnum.INVALID.getType());

        tradeTicketDao.updateById(tradeTicketEntity);
        //取消工作流审批
        cancelActiviti(contraryCause, userId, tradeTicketEntity);
        //3,撤回协议
        this.invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, contraryCause, contractSignEntity.getId());
        //5,修改合同
        //修改定价数量
        ContractEntity contractEntity = contractService.getContractById(ttPriceEntity.getContractId());
        //定价数量
        priceAllocateContraryAll(contractEntity, ttPriceEntity, priceApplyNum);
       /* if (AllocateTypeEnum.ALL.getValue() == priceAllocateEntity.getType()) {

        } else {
            boolean b = contraryTTPrice(contractEntity.getId(), ttPriceEntity.getId());

            BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum().subtract(priceApplyNum);
            contractEntity.setTotalPriceNum(oldTotalPriceNum)
                    .setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus());
            contractService.updateContract(contractEntity);
        }*/

        //调用同步lkg
        /*SyncContractDTO syncContractDTO = new SyncContractDTO();
        syncContractDTO
                .setTtId(ttId)
                .setConfirmPriceId(ttPriceEntity.getId())
                .setSyncType(LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType())
                .setPriceContractId(ttPriceEntity.getContractId())
        ;
        syncContractService.syncTTPrice(syncContractDTO);*/

        lkgSyncService.syncTTPriceInfo(ttPriceEntity.getId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());

        return Result.success();
    }


    private boolean contraryTTPrice(Integer contractId, Integer ttPriceId) {
        List<TTPriceEntity> ttPriceEntities = ttPriceService.getPriceByContractNotId(contractId, ttPriceId);

        for (TTPriceEntity ttPriceEntity : ttPriceEntities) {
            ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttPriceEntity.getTtId());

            if (ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSignEntity.getStatus()) {
                return false;
            }
        }
        return true;
    }

    private void priceAllocateContraryAll(ContractEntity contractEntity, TTPriceEntity ttPriceEntity, BigDecimal priceApplyNum) {
        //定价数量,合同价格
        //计算合同物流相关费用
        BigDecimal deliveryPrice = contractEntity.getUnitPrice().subtract(contractEntity.getFobUnitPrice());
        //计算合同总金额
        BigDecimal totalAmount = ttPriceEntity.getUnitPrice().multiply(contractEntity.getContractNum());
        BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum().subtract(priceApplyNum);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, ttPriceEntity.getUnitPrice(), contractEntity.getTaxRate().add(BigDecimal.ONE));
        //boolean b = contraryTTPrice(contractEntity.getId(), ttPriceEntity.getId());
        //修改合同价格
        contractEntity
                .setTotalPriceNum(oldTotalPriceNum)
                .setUnitPrice(ttPriceEntity.getUnitPrice())
                .setFobUnitPrice(ttPriceEntity.getUnitPrice().subtract(deliveryPrice))
                .setCifUnitPrice(cifUnitPrice)
                .setTotalAmount(totalAmount)
                .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
        //.setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus())
        ;
        contractService.updateContract(contractEntity);
        //查询合同price
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
        //修改合同price
        contractPriceService.updatePriceByContractId(contractPriceEntity);
    }

    /**
     * 全部撤回
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result priceAllocateContraryAll(PriceAllocateEntity priceAllocateEntity, String contraryCause) {
        String userId = JwtUtils.getCurrentUserId();
        //撤回点价
        //部分点价
        List<TTPriceEntity> ttPriceEntities = ttPriceService.getTTPriceByApplyId(priceAllocateEntity.getPriceApplyId());
        //遍历定价单
        for (TTPriceEntity ttPriceEntity : ttPriceEntities) {
            Integer ttId = ttPriceEntity.getTtId();
            //校验协议是否已经签署
            ContractSignEntity contractSignEntity = iContractSignQueryService.getContractSignDetailByTtId(ttId);
            //已签署不予撤回
            if (ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
            }
            //查询定价单是否已被拆分
            if (0 != ttPriceEntity.getSourceId()) {
                List<TTPriceEntity> ttPrices = ttPriceService.getTTPriceBySourceId(ttPriceEntity.getSourceId());
                //拆分后不予撤回
                if (!ttPrices.isEmpty()) {
                    //不予撤回
                    throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_SPLIT_NOT_CONTRARY);
                }
            }
            //1,撤回定价单
            BigDecimal priceApplyNum = ttPriceEntity.getNum();

            ttPriceEntity.setNum(BigDecimal.ZERO)
                    .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                    .setMemo(contraryCause);
            ttPriceService.updateTTPriceById(ttPriceEntity);
            //2,撤回TT
            //修改tt状态为待修改提交
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttPriceEntity.getTtId());
            tradeTicketEntity.setCancelReason(contraryCause)
                    .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                    .setStatus(TTStatusEnum.INVALID.getType());

            tradeTicketDao.updateById(tradeTicketEntity);
            //取消工作流审批
            cancelActiviti(contraryCause, userId, tradeTicketEntity);
            //3,撤回协议
            this.invalidContractSign(tradeTicketEntity.getId(), tradeTicketEntity, contraryCause, contractSignEntity.getId());
            //4,修改合同
            ContractEntity contractEntity = contractService.getContractById(ttPriceEntity.getContractId());
            //定价数量,合同价格
            //计算合同物流相关费用
            BigDecimal deliveryPrice = contractEntity.getUnitPrice().subtract(contractEntity.getFobUnitPrice());
            //计算合同总金额
            BigDecimal totalAmount = ttPriceEntity.getUnitPrice().multiply(contractEntity.getContractNum());
            BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum().subtract(priceApplyNum);
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, ttPriceEntity.getUnitPrice(), contractEntity.getTaxRate().add(BigDecimal.ONE));
            //修改合同价格
            contractEntity
                    .setTotalPriceNum(oldTotalPriceNum)
                    .setUnitPrice(ttPriceEntity.getUnitPrice())
                    .setFobUnitPrice(ttPriceEntity.getUnitPrice().subtract(deliveryPrice))
                    .setCifUnitPrice(cifUnitPrice)
                    .setTotalAmount(totalAmount)
                    .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
            ;
            contractService.updateContract(contractEntity);
            //查询合同price
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
            //修改合同price
            contractPriceService.updatePriceByContractId(contractPriceEntity);
            //调用同步lkg
            /*SyncContractDTO syncContractDTO = new SyncContractDTO();
            syncContractDTO
                    .setTtId(ttId)
                    .setConfirmPriceId(ttPriceEntity.getId())
                    .setSyncType(LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType())
                    .setPriceContractId(ttPriceEntity.getContractId())
            ;
            syncContractService.syncTTPrice(syncContractDTO);*/
            lkgSyncService.syncTTPriceInfo(ttPriceEntity.getId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
        }
        return Result.success();
    }
}
