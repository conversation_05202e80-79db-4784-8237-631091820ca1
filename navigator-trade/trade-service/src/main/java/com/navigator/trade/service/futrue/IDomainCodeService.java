package com.navigator.trade.service.futrue;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceAuditDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceLeadDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import com.navigator.trade.pojo.entity.DomainPriceEntity;
import com.navigator.trade.pojo.vo.DomainPriceLeadVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 期货合约表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
public interface IDomainCodeService {

    /**
     * 当天收盘价
     *
     * @param categoryId 品类id
     * @param name       合约
     * @return
     */
    DomainPriceEntity getClosingPrice(Integer categoryId, String name,String categoryCode);

    DomainPriceEntity getClosingPrice(Integer categoryId, String domainCode, Date tradeDay, String categoryCode);

    DomainPriceEntity getLastestClosingPrice(Integer categoryId, String domainCode, Date signDate, String categoryCode);

    List<DomainPriceLeadDTO> previewDomainPrice(MultipartFile file);

    Result uploadDomainPrice(MultipartFile file);

    Result queryDomainPrice(QueryDTO<QueryDomainPriceBO> queryDTO);

    boolean audit(DomainPriceAuditDTO domainPriceAuditDTO);

    List<DomainPriceLeadVO> queryDomainPriceToday(DomainPriceTodayDTO domainPriceTodayDTO);

    /**
     * TODO NEO 优化方向
     * 合约   总合同量    基差总量    基差暂定价总量 点价申请量    结构化点价申请量    转月申请量
     * 可点价量 可结构化定价量 可转月量
     * 合同服务提供：合约   总合同量    基差总量
     * 期货服务提供：合约    点价申请量   结构化点价申请量    转月申请量
     *
     * 聚合服务在此提供
     *
     *
     *
     */


}
