package com.navigator.trade.service.tradeticket.impl.convert;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.*;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * TT转换器
 */
@Service
public class TTDTOConverter {
    // Adding a private constructor to prevent instantiation
    private TTDTOConverter() {
        // Private constructor body can be left empty or used for initialization
    }

    public static List<TTDTO> convert2TTDTOForAdd(OMContractAddTTDTO omContractAddTTDTO) {
        List<TTDTO> ttdtoList = new ArrayList<>();

        for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {
            TTDTO ttdto = new TTDTO();
            SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();

            BeanUtils.copyProperties(keyTradeInfoTTDTO, salesContractAddTTDTO);
            BeanUtils.copyProperties(omContractAddTTDTO, salesContractAddTTDTO);

            salesContractAddTTDTO.setDomainCode(keyTradeInfoTTDTO.getDomainCode());
            salesContractAddTTDTO.setFutureCode(keyTradeInfoTTDTO.getFutureCode());
            salesContractAddTTDTO.setContractNum(keyTradeInfoTTDTO.getContractNum());
            salesContractAddTTDTO.setSupplierId(StringUtils.isBlank(omContractAddTTDTO.getSupplierId()) ? null : Integer.parseInt(omContractAddTTDTO.getSupplierId()));
            salesContractAddTTDTO.setCustomerId(StringUtils.isBlank(omContractAddTTDTO.getCustomerId()) ? null : Integer.parseInt(omContractAddTTDTO.getCustomerId()));
            salesContractAddTTDTO.setGoodsCategoryId(StringUtils.isBlank(omContractAddTTDTO.getGoodsCategoryId()) ? null : Integer.parseInt(omContractAddTTDTO.getGoodsCategoryId()));
            salesContractAddTTDTO.setAddedDepositRate(StringUtils.isBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()));
            salesContractAddTTDTO.setPayConditionId(StringUtils.isBlank(keyTradeInfoTTDTO.getPayConditionId()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getPayConditionId()));
            salesContractAddTTDTO.setUsage(omContractAddTTDTO.getUsage());

            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, keyTradeInfoTTDTO.getPriceDetailDTO());

            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
            ttdto.setPriceDetailBO(priceDetailBO);


        }
        return ttdtoList;
    }

    public static List<TTDTO> convert2TTDTO(SubmitTTDTO submitTTDTO) {

        OMContractAddTTDTO omContractAddTTDTO = submitTTDTO.getCreateTradeTicketDTO();
        omContractAddTTDTO.setUserId(submitTTDTO.getUserId());

        List<TTDTO> ttdtoList = new ArrayList<>();

        for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {
            TTDTO ttdto = new TTDTO();

            ttdto.setSubmitType(omContractAddTTDTO.getSubmitType());
            ttdto.setBuCode(omContractAddTTDTO.getBuCode());
            ttdto.setSalesType(omContractAddTTDTO.getSalesType());
            ttdto.setTtType(omContractAddTTDTO.getType());
            ttdto.setContractTradeType(omContractAddTTDTO.getTradeType());

            SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();

            BeanUtils.copyProperties(keyTradeInfoTTDTO, salesContractAddTTDTO);
            BeanUtils.copyProperties(omContractAddTTDTO, salesContractAddTTDTO);

            salesContractAddTTDTO.setDomainCode(keyTradeInfoTTDTO.getDomainCode());
            salesContractAddTTDTO.setFutureCode(keyTradeInfoTTDTO.getFutureCode());
            salesContractAddTTDTO.setContractNum(keyTradeInfoTTDTO.getContractNum());
            salesContractAddTTDTO.setSupplierId(StringUtils.isBlank(omContractAddTTDTO.getSupplierId()) ? null : Integer.parseInt(omContractAddTTDTO.getSupplierId()));
            salesContractAddTTDTO.setCustomerId(StringUtils.isBlank(omContractAddTTDTO.getCustomerId()) ? null : Integer.parseInt(omContractAddTTDTO.getCustomerId()));
            salesContractAddTTDTO.setGoodsCategoryId(StringUtils.isBlank(omContractAddTTDTO.getGoodsCategoryId()) ? null : Integer.parseInt(omContractAddTTDTO.getGoodsCategoryId()));
            salesContractAddTTDTO.setAddedDepositRate(StringUtils.isBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()));
            salesContractAddTTDTO.setPayConditionId(StringUtils.isBlank(keyTradeInfoTTDTO.getPayConditionId()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getPayConditionId()));
            salesContractAddTTDTO.setUsage(omContractAddTTDTO.getUsage());

            PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, keyTradeInfoTTDTO.getPriceDetailDTO());

            ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
            ttdto.setPriceDetailBO(priceDetailBO);

            ttdtoList.add(ttdto);

        }
        return ttdtoList;
    }


    /**
     * 合同转TTDTO
     *
     * @param sonContractEntity 子合同
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更信息
     * @return
     */
    public static List<TTDTO> createSplitTTDTO(ContractEntity sonContractEntity, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        List<TTDTO> ttdtoList = new ArrayList<>();

        // 子合同的TT信息
        TTDTO sonTTDTO = new TTDTO();

        // 生成groupId绑定两个TT
        sonTTDTO.setGroupId(UUID.randomUUID().toString().replace("-", ""));
        // 交易类型
        sonTTDTO.setContractTradeType(sonContractEntity.getTradeType());

        SalesContractSplitTTDTO salesContractSplitTTDTO = new SalesContractSplitTTDTO();

        // 处理变更的数据
        BeanUtils.copyProperties(sonContractEntity, salesContractSplitTTDTO);
        BeanUtils.copyProperties(contractModifyDTO, salesContractSplitTTDTO);

        // 特殊处理
        salesContractSplitTTDTO.setSonContractId(sonContractEntity.getId());
        salesContractSplitTTDTO.setSourceContractId(contractEntity.getId());
        salesContractSplitTTDTO.setRootContractId(contractEntity.getRootId());
        salesContractSplitTTDTO.setSupplierId(contractModifyDTO.getSupplierId() != null ? contractModifyDTO.getSupplierId() : sonContractEntity.getSupplierId());
        salesContractSplitTTDTO.setPackageWeight(contractModifyDTO.getPackageWeight());

        // 付款方式
        salesContractSplitTTDTO.setPaymentType(contractModifyDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());
        salesContractSplitTTDTO.setUsage(contractModifyDTO.getUsage());
        salesContractSplitTTDTO.setAddedDepositRate2(sonContractEntity.getAddedDepositRate2());

        // 合同来源
        salesContractSplitTTDTO.setContractSource(contractModifyDTO.getContractSource());

        // 期货代码
        salesContractSplitTTDTO.setFutureCode(contractModifyDTO.getFutureCode() == null ? contractEntity.getFutureCode() : contractModifyDTO.getFutureCode());

        // 价格修改
        sonTTDTO.setPriceDetailBO(contractModifyDTO.getPriceDetailDTO());

        // 保存定价单信息
        sonTTDTO.setConfirmPriceInfo(JSON.toJSONString(contractModifyDTO.getConfirmPriceDTOList()));

        // 设置tt类型
        sonTTDTO.setTtType(TTTypeEnum.SPLIT.getType());

        // 拆分保存处理的字段-只生成一个TT
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            salesContractSplitTTDTO.setTtId(contractModifyDTO.getTtId());
            salesContractSplitTTDTO.setCode(contractModifyDTO.getTtCode());
            salesContractSplitTTDTO.setContractType(contractModifyDTO.getSonContractType());
            salesContractSplitTTDTO.setContractNum(contractModifyDTO.getModifyNum());
            salesContractSplitTTDTO.setSonContractId(contractEntity.getId());
            salesContractSplitTTDTO.setContractCode(contractEntity.getContractCode());

            sonTTDTO.setSubmitType(contractModifyDTO.getSubmitType());

            sonTTDTO.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
            ttdtoList.add(sonTTDTO);

            return ttdtoList;
        }

        sonTTDTO.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
        ttdtoList.add(sonTTDTO);

        // 原合同的tt信息
        TTDTO parentTTDTO = BeanUtil.toBean(sonTTDTO, TTDTO.class);
        SalesContractSplitTTDTO parentSplitDTO = BeanUtil.toBean(salesContractSplitTTDTO, SalesContractSplitTTDTO.class);

        // 生成数量补充tt,协议
        parentSplitDTO.setSignType(salesContractSplitTTDTO.getContractNum().compareTo(contractEntity.getContractNum()) == 0 ?
                ContractSignTypeEnum.ALL_SPLIT.getValue() : ContractSignTypeEnum.PARTLY_SPLIT.getValue());
        parentSplitDTO.setAddedSignatureType(1);
        parentTTDTO.setSalesContractSplitTTDTO(parentSplitDTO);

        ttdtoList.add(parentTTDTO);

        return ttdtoList;
    }


    public static List<TTDTO> createReviseTTDTO(ContractEntity sonContractEntity, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        List<TTDTO> ttdtoList = new ArrayList<>();

        TTDTO ttdto = new TTDTO();
        SalesContractReviseTTDTO salesContractReviseTTDTO = new SalesContractReviseTTDTO();

        // 是否变更主体
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        boolean isCustomerChanged = isSales ?
                !contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId()) :
                !contractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId());

        if (isCustomerChanged) {
            // 生成groupId绑定两个TT
            ttdto.setGroupId(UUID.randomUUID().toString().replace("-", ""));
        }

        boolean changeCustomerFlag = false;
        int originCustomerId = isSales ? contractModifyDTO.getCustomerId() : contractModifyDTO.getSupplierId();

        // 保存修改接口不走修改主体的逻辑，走普通修改逻辑，然后最后修改TT的主体信息
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue() && isCustomerChanged) {
            if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
                contractModifyDTO.setCustomerId(contractEntity.getCustomerId());
            } else {
                contractModifyDTO.setSupplierId(contractEntity.getSupplierId());
            }
            isCustomerChanged = false;
            changeCustomerFlag = true;
        }

        // 处理变更的数据
        if (isCustomerChanged) {
            BeanUtils.copyProperties(sonContractEntity, salesContractReviseTTDTO);
            salesContractReviseTTDTO.setSonContractId(sonContractEntity.getId());
            salesContractReviseTTDTO.setSourceContractId(contractEntity.getId());
            salesContractReviseTTDTO.setRootContractId(contractEntity.getRootId());
            salesContractReviseTTDTO.setSignDate(new Date());
            salesContractReviseTTDTO.setContractSource(ContractActionEnum.REVISE_CUSTOMER.getActionValue());
            ttdto.setReviseCustomerType(true);
        } else {
            BeanUtils.copyProperties(contractEntity, salesContractReviseTTDTO);
            BeanUtils.copyProperties(contractModifyDTO, salesContractReviseTTDTO);

            // 价格处理
            PriceDetailBO priceDetailDTO = contractModifyDTO.getPriceDetailDTO();
            BigDecimal unitPrice = contractModifyDTO.getUnitPrice();

            // 运输费
            BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailDTO.getTransportPrice())
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getLiftingPrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getDelayPrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getTemperaturePrice()))
                    .add(BigDecimalUtil.initBigDecimal(priceDetailDTO.getOtherDeliveryPrice()));

            // 计算fobUnitPrice价格
            BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
            salesContractReviseTTDTO.setFobUnitPrice(fobUnitPrice);

            // 计算总金额
            salesContractReviseTTDTO.setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getContractNum(), unitPrice));
            salesContractReviseTTDTO.setAddedDepositRate(contractModifyDTO.getAddedDepositRate() == null ? contractEntity.getAddedDepositRate() : contractModifyDTO.getAddedDepositRate());

            // 基差修改一口价||暂定价||基差暂定价 履约保证金更新
            if (contractModifyDTO.getReviseType() == 2) {
                // NAV系统的履约保证金更新=NAV变更前履约保证金+NAV变更前履约保证金点价后补缴
                int depositRate = contractEntity.getDepositRate();
                int addedDepositRate = contractEntity.getAddedDepositRate();
                int newDepositRate = depositRate + addedDepositRate;
                salesContractReviseTTDTO.setDepositRate(newDepositRate)
                        .setAddedDepositRate(0)
                        .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, salesContractReviseTTDTO.getTotalAmount(), BigDecimal.valueOf(salesContractReviseTTDTO.getDepositRate() * 0.01)));
            }

            salesContractReviseTTDTO.setSignDate(contractEntity.getSignDate());
            ttdto.setReviseCustomerType(false);
        }

        // 处理点价截止日期
        String priceEndTime = contractModifyDTO.getPriceEndTime();

        if (DateTimeUtil.isDate(priceEndTime)) {
            salesContractReviseTTDTO.setPriceEndType(ContractPriceEndTypeEnum.DATE.getValue());
        } else {
            salesContractReviseTTDTO.setPriceEndType(ContractPriceEndTypeEnum.TEXT.getValue());
        }
        salesContractReviseTTDTO.setStandardType(contractModifyDTO.getStandardType());
        salesContractReviseTTDTO.setStandardFileId(contractModifyDTO.getStandardFileId());
        salesContractReviseTTDTO.setStandardRemark(contractModifyDTO.getStandardRemark());
        salesContractReviseTTDTO.setContractType(contractModifyDTO.getSonContractType());
        salesContractReviseTTDTO.setShipWarehouseId(Integer.parseInt(contractModifyDTO.getShipWarehouseId()));
        salesContractReviseTTDTO.setWeightCheck(Integer.parseInt(contractModifyDTO.getWeightCheck()));
        salesContractReviseTTDTO.setMemo(contractModifyDTO.getRemark());
        salesContractReviseTTDTO.setDelayPayFine(contractModifyDTO.getDelayPayFine() == null ? contractEntity.getDelayPayFine() : contractModifyDTO.getDelayPayFine());
        salesContractReviseTTDTO.setDepositReleaseType(contractModifyDTO.getDepositReleaseType() == null ? contractEntity.getDepositReleaseType() : contractModifyDTO.getDepositReleaseType());
        salesContractReviseTTDTO.setNeedPackageWeight(contractModifyDTO.getNeedPackageWeight() == null ? contractEntity.getNeedPackageWeight() : contractModifyDTO.getNeedPackageWeight());
        salesContractReviseTTDTO.setPackageWeight(contractModifyDTO.getPackageWeight() == null ? contractEntity.getPackageWeight() : contractModifyDTO.getPackageWeight());
        salesContractReviseTTDTO.setSupplierAccountId(contractModifyDTO.getSupplierAccountId() == null ? contractEntity.getSupplierAccountId() : contractModifyDTO.getSupplierAccountId());
        salesContractReviseTTDTO.setSourceContractId(contractEntity.getId());
        salesContractReviseTTDTO.setRootContractId(contractEntity.getRootId());
        // 付款方式
        Integer paymentType = null;
        if (contractModifyDTO.getCreditDays() != null) {
            paymentType = contractModifyDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType();
        }
        salesContractReviseTTDTO.setPaymentType(paymentType);
        salesContractReviseTTDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        salesContractReviseTTDTO.setReviseType(contractModifyDTO.getReviseType());
        salesContractReviseTTDTO.setFutureCode(contractModifyDTO.getFutureCode() == null ? contractEntity.getFutureCode() : contractModifyDTO.getFutureCode());

        // 商品昵称
        if (StringUtils.isNotBlank(contractModifyDTO.getCommodityName())) {
            salesContractReviseTTDTO.setCommodityName(contractModifyDTO.getCommodityName());
        }

        // add by zengshl 业务想编码 账套信息设置,修改一些数据无法获取
        salesContractReviseTTDTO.setSiteCode(ObjectUtil.isNotEmpty(contractModifyDTO.getSiteCode()) ? contractModifyDTO.getSiteCode() :
                contractEntity.getSiteCode());
        salesContractReviseTTDTO.setSiteName(ObjectUtil.isNotEmpty(contractModifyDTO.getSiteName()) ? contractModifyDTO.getSiteName() :
                contractEntity.getSiteName());
        salesContractReviseTTDTO.setContractNature(contractEntity.getContractNature());
        salesContractReviseTTDTO.setGoodsId(ObjectUtil.isNotEmpty(contractModifyDTO.getGoodsId()) ? contractModifyDTO.getGoodsId() :
                contractEntity.getGoodsId());
        salesContractReviseTTDTO.setSettleType(ObjectUtil.isNotEmpty(contractModifyDTO.getSettleType()) ? contractModifyDTO.getSettleType() :
                contractEntity.getSettleType());
        salesContractReviseTTDTO.setPayConditionId(ObjectUtil.isNotEmpty(contractModifyDTO.getPayConditionId()) ? contractModifyDTO.getPayConditionId() :
                contractEntity.getPayConditionId());
        ttdto.setBuCode(contractEntity.getBuCode());

        /*Integer addedDepositRate2 = getAddedDepositRate2(contractModifyDTO);
        salesContractReviseTTDTO.setAddedDepositRate2(addedDepositRate2);*/
        ttdto.setSalesContractReviseTTDTO(salesContractReviseTTDTO);

        // 价格修改
        ttdto.setPriceDetailBO(contractModifyDTO.getPriceDetailDTO());

        ttdto.setSubmitType(contractModifyDTO.getSubmitType());
        ttdto.setChangeCustomerFlag(changeCustomerFlag);
        ttdto.setOriginCustomerId(originCustomerId);


        // 交易类型
        ttdto.setContractTradeType(isCustomerChanged ? ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() : ContractTradeTypeEnum.REVISE_NORMAL.getValue());

        // 设置tt类型
        ttdto.setTtType(TTTypeEnum.REVISE.getType());

        ttdtoList.add(ttdto);

        // 原合同的tt信息
        if (isCustomerChanged) {
            TTDTO parentTTDTO = BeanUtil.toBean(ttdto, TTDTO.class);
            SalesContractReviseTTDTO contractReviseTTDTO = BeanUtil.toBean(salesContractReviseTTDTO, SalesContractReviseTTDTO.class);
            contractReviseTTDTO.setAddedSignatureType(-1);
            parentTTDTO.setSalesContractReviseTTDTO(contractReviseTTDTO);
            ttdtoList.add(parentTTDTO);
        }

        return ttdtoList;
    }

    public static TTDTO createPriceTTDTO(ContractEntity contractEntity, SalesContractTTPriceDTO salesContractTTPriceDTO) {
        TTDTO ttdto = new TTDTO();

        salesContractTTPriceDTO
                .setSiteCode(contractEntity.getSiteCode())
                .setSiteName(contractEntity.getSiteName())
                .setCategory1(contractEntity.getCategory1())
                .setCategory2(contractEntity.getCategory2())
                .setCategory3(contractEntity.getCategory3())
                .setType(PriceTypeEnum.PRICING.getValue())
                .setRemainPriceNum(contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()))
                .setOriginalPriceNum(salesContractTTPriceDTO.getAllocateNum())
                .setThisContractNum(contractEntity.getContractNum())
                .setUnitPrice(contractEntity.getUnitPrice())
                .setTotalPriceNum(contractEntity.getTotalPriceNum())
                .setPriceEndType(contractEntity.getPriceEndType())
                .setPriceEndTime(contractEntity.getPriceEndTime())
                .setWarrantTradeType(contractEntity.getWarrantTradeType())
                .setOwnerId(contractEntity.getOwnerId())
                .setGoodsPackageId(contractEntity.getGoodsPackageId())
                .setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setWarrantId(contractEntity.getWarrantId())
                .setWarrantCode(contractEntity.getWarrantCode())
                .setTempPrice(contractEntity.getTemporaryPrice())
                .setDiffPrice(contractEntity.getExtraPrice())
                .setNum(salesContractTTPriceDTO.getAllocateNum())
                .setUserId(JwtUtils.getCurrentUserId())
                .setContractType(contractEntity.getContractType())
                .setGoodsId(contractEntity.getGoodsId())
                .setGoodsSpecId(contractEntity.getGoodsSpecId())
                .setGoodsPackageId(contractEntity.getGoodsPackageId())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                .setCustomerId(contractEntity.getCustomerId())
                .setSupplierId(contractEntity.getSupplierId())
                .setCustomerName(contractEntity.getCustomerName())
                .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerCode(contractEntity.getCustomerCode())
                .setSupplierName(contractEntity.getSupplierName())
                .setSalesType(contractEntity.getSalesType())
                .setSourceContractId(contractEntity.getId())
                .setDomainCode(contractEntity.getDomainCode())
                .setBuCode(contractEntity.getBuCode())
                .setContractNature(contractEntity.getContractNature())
                .setCompanyId(contractEntity.getCompanyId())
                .setGoodsName(contractEntity.getGoodsName())
                .setFutureCode(contractEntity.getFutureCode())
                .setCompanyName(contractEntity.getCompanyName())
                .setCommodityName(contractEntity.getCommodityName())
        ;
        salesContractTTPriceDTO.setBelongCustomerId(contractEntity.getBelongCustomerId());

        // 设置tt类型
        ttdto.setTtType(TTTypeEnum.PRICE.getType());

        ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

        return ttdto;
    }

    public static List<TTDTO> createTransferMonthTTDTO(ContractEntity contractEntity, ContractEntity childContractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext) {

        List<TTDTO> ttdtoList = new ArrayList<>();

        TTDTO ttdto = new TTDTO();

        // 生成groupId绑定两个TT
        ttdto.setGroupId(UUID.randomUUID().toString().replace("-", ""));
        ttdto.setContractTradeType(contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()) ?
                ContractTradeTypeEnum.TRANSFER_PART.getValue() : ContractTradeTypeEnum.TRANSFER_ALL.getValue());
        SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);
        // 业务线
        salesContractTTTransferDTO.setBuCode(contractEntity.getBuCode());
        // 合同性质
        salesContractTTTransferDTO.setContractNature(contractEntity.getContractNature());

        // 全部转月
        salesContractTTTransferDTO
                .setCategory1(contractEntity.getCategory1())
                .setCategory2(contractEntity.getCategory2())
                .setCategory3(contractEntity.getCategory3())
                .setContractType(contractEntity.getContractType())
                .setContractCode(contractEntity.getContractCode())
                .setPriceApplyId(contractTransferDTO.getPriceApplyId())
                .setPriceAllocateId(contractTransferDTO.getPriceAllocateId())
                .setNum(contractTransferDTO.getAllocateNum())
                .setCategoryId(contractEntity.getGoodsCategoryId())
                .setGoodsPackageId(contractEntity.getGoodsPackageId())
                .setOriginalDomainCode(contractEntity.getDomainCode())
                .setDiffPrice(contractEntity.getExtraPrice())
                .setPrice(contractTransferDTO.getTransactionDiffPrice())
                .setTempPrice(contractEntity.getTemporaryPrice())
                .setContractId(contractEntity.getId())
                .setTotalAmount(contractEntity.getTotalAmount())
                .setCifUnitPrice(contractEntity.getCifUnitPrice())
                .setFobUnitPrice(contractEntity.getFobUnitPrice())
                .setUnitPrice(contractEntity.getUnitPrice())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                .setType(contractTransferDTO.getTtTranferType())
                .setPriceEndType(contractEntity.getPriceEndType())
                .setPriceEndTime(contractEntity.getPriceEndTime())
                .setBelongCustomerId(contractEntity.getBelongCustomerId())
                .setSalesType(contractEntity.getSalesType())
                .setContractSource(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue())
                .setTradeType(ContractTradeTypeEnum.TRANSFER_ALL.getValue())
                .setSourceContractId(contractEntity.getId())
                .setDomainCode(contractTransferDTO.getDomainCode() != null ? contractTransferDTO.getDomainCode() : contractEntity.getDomainCode())
                .setFutureCode(contractTransferDTO.getTransferFutureCode() != null ? contractTransferDTO.getTransferFutureCode() : contractEntity.getFutureCode());

        ttdto.setTtType(TTTypeEnum.TRANSFER.getType());
        // 部分转月生成双TT，付合同TT隐藏
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            // 原合同的tt信息
            TTDTO parentTTDTO = BeanUtil.toBean(ttdto, TTDTO.class);
            SalesContractTTTransferDTO contractTTTransferDTO = BeanUtil.toBean(salesContractTTTransferDTO, SalesContractTTTransferDTO.class);
            salesContractTTTransferDTO.setTradeType(ContractTradeTypeEnum.TRANSFER_PART.getValue());

            // 生成数量补充tt,协议
            contractTTTransferDTO.setAddedSignatureType(-1)
                    .setTradeType(ContractTradeTypeEnum.TRANSFER_PART.getValue());
            parentTTDTO.setSalesContractTTTransferDTO(contractTTTransferDTO);
            parentTTDTO.setPriceDetailBO(arrangeContext.getPriceDetailBO());
            ttdtoList.add(parentTTDTO);
        } else {
            ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
            ttdto.setPriceDetailBO(arrangeContext.getPriceDetailBO());
            ttdtoList.add(ttdto);
        }
        salesContractTTTransferDTO.setThisTimeFee(arrangeContext.getThisTimeFee());
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            // 部分转月
            salesContractTTTransferDTO
                    .setCategory1(contractEntity.getCategory1())
                    .setCategory2(contractEntity.getCategory2())
                    .setCategory3(contractEntity.getCategory3())
                    .setContractType(contractEntity.getContractType())
                    .setPriceApplyId(contractTransferDTO.getPriceApplyId())
                    .setPriceAllocateId(contractTransferDTO.getPriceAllocateId())
                    .setFobUnitPrice(childContractEntity.getFobUnitPrice())
                    .setCifUnitPrice(childContractEntity.getCifUnitPrice())
                    .setUnitPrice(childContractEntity.getUnitPrice())
                    .setTotalAmount(childContractEntity.getTotalAmount())
                    .setType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                    .setNum(childContractEntity.getContractNum())
                    .setSonContractId(childContractEntity.getId())
                    .setSonContractCode(childContractEntity.getContractCode())
//                    .setContractCode(contractEntity.getContractCode())
                    .setContractCode(childContractEntity.getContractCode())
                    .setGoodsPackageId(contractEntity.getGoodsPackageId())
                    .setOriginalDomainCode(contractEntity.getDomainCode())
                    .setTempPrice(contractEntity.getTemporaryPrice())
                    .setDiffPrice(contractEntity.getExtraPrice())
                    .setContractId(contractEntity.getId())
                    .setPrice(contractTransferDTO.getTransactionDiffPrice())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setSignDate(childContractEntity.getSignDate())
                    .setPriceEndTime(childContractEntity.getPriceEndTime())
                    .setPriceEndType(childContractEntity.getPriceEndType())
                    .setBelongCustomerId(childContractEntity.getBelongCustomerId())
                    .setSalesType(contractEntity.getSalesType())
                    .setDomainCode(contractTransferDTO.getDomainCode())
                    .setContractSource(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())
                    .setTradeType(ContractTradeTypeEnum.TRANSFER_PART.getValue())
                    .setSourceContractId(contractEntity.getId());

            // 设置tt类型
            salesContractTTTransferDTO.setAddedSignatureType(0);
            ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
            ttdto.setPriceDetailBO(arrangeContext.getPriceDetailBO());
            ttdtoList.add(ttdto);
        }
        return ttdtoList;
    }

    public static List<TTDTO> createReversePriceTTDTO(ContractEntity contractEntity, ContractEntity childContractEntity, ContractTransferDTO contractTransferDTO, ArrangeContext arrangeContext) {
        List<TTDTO> ttdtoList = new ArrayList<>();

        TTDTO ttdto = new TTDTO();

        // 生成groupId绑定两个TT
        ttdto.setGroupId(UUID.randomUUID().toString().replace("-", ""));

        SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);

        // 部分反点价
        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue())) {
            salesContractTTTransferDTO.setType(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setTradeType(ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue());
            salesContractTTTransferDTO.setContractSource(ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue());
        } else {
            salesContractTTTransferDTO.setType(TTTranferTypeEnum.REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setTradeType(ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue());
            salesContractTTTransferDTO.setContractSource(ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue());
        }

        salesContractTTTransferDTO
                .setThisTimeFee(arrangeContext.getThisTimeFee())
                .setNum(contractTransferDTO.getReversePricingNum())
                .setOriginalDomainCode(contractEntity.getDomainCode())
                .setContractId(contractEntity.getId())
                .setSonContractId(childContractEntity.getId())
                .setContractCode(childContractEntity.getContractCode())
                .setContractType(ContractTypeEnum.JI_CHA.getValue())
                .setDeliveryFactoryCode(childContractEntity.getDeliveryFactoryCode())
                .setDeliveryFactoryName(childContractEntity.getDeliveryFactoryName())
                .setTotalAmount(childContractEntity.getTotalAmount())
                .setCifUnitPrice(childContractEntity.getCifUnitPrice())
                .setFobUnitPrice(childContractEntity.getFobUnitPrice())
                .setWarrantTradeType(contractEntity.getWarrantTradeType())
                .setUnitPrice(childContractEntity.getUnitPrice())
                .setSignDate(contractEntity.getCreatedAt())
                .setPriceEndType(childContractEntity.getPriceEndType())
                .setPriceEndTime(childContractEntity.getPriceEndTime())
                .setPriceApplyId(contractTransferDTO.getPriceApplyId())
                .setPriceAllocateId(contractTransferDTO.getPriceAllocateId())
                .setGoodsPackageId(childContractEntity.getGoodsPackageId())
                .setGoodsSpecId(childContractEntity.getGoodsSpecId())
                .setCategoryId(childContractEntity.getGoodsCategoryId())
                .setPrice(childContractEntity.getExtraPrice())
                .setAddedDepositRate2(childContractEntity.getAddedDepositRate2())
                .setBelongCustomerId(childContractEntity.getBelongCustomerId())
                .setSupplierId(childContractEntity.getSupplierId())
                .setSourceContractId(contractEntity.getId())
                .setDomainCode(contractTransferDTO.getTransferDominantCode() != null ? contractTransferDTO.getTransferDominantCode() : contractEntity.getDomainCode())
                .setFutureCode(contractTransferDTO.getTransferFutureCode() != null ? contractTransferDTO.getTransferFutureCode() : contractEntity.getFutureCode())
                .setCategory1(contractEntity.getCategory1())
                .setCategory2(contractEntity.getCategory2())
                .setCategory3(contractEntity.getCategory3())
        ;
        ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
        ttdto.setPriceDetailBO(arrangeContext.getPriceDetailBO());

        // 设置tt类型
        ttdto.setTtType(TTTypeEnum.REVERSE_PRICE.getType());

        ttdtoList.add(ttdto);

        // 原合同的tt信息
        TTDTO parentTTDTO = BeanUtil.toBean(ttdto, TTDTO.class);
        SalesContractTTTransferDTO contractTTTransferDTO = BeanUtil.toBean(salesContractTTTransferDTO, SalesContractTTTransferDTO.class);

        // 生成数量补充tt,协议
        contractTTTransferDTO.setAddedSignatureType(-1);
        parentTTDTO.setSalesContractTTTransferDTO(contractTTTransferDTO);
        ttdtoList.add(parentTTDTO);

        return ttdtoList;
    }
}
