package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.pigeon.facade.LkgContractDailyCheckFacade;
import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;
import com.navigator.trade.dao.ContractVODao;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.contract.ContractExportDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractVOEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.contract.IContractExportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 合同导出服务
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/4
 */
@Service
public class ContractExportServiceImpl implements IContractExportService {
    @Autowired
    private IContractQueryService contractQueryService;

    @Autowired
    private AzureBlobUtil azureBlobUtil;

    @Autowired
    private LkgContractDailyCheckFacade lkgContractDailyCheckFacade;

    @Autowired
    private ContractVODao contractVODao;

    @Override
    public List<FileBaseInfoDTO> exportDailyContract(String startDateTime, String endDateTime) {
        List<FileBaseInfoDTO> fileBaseInfoDTOS = new ArrayList<>();
        // 销售合同
        FileBaseInfoDTO salesFileInfo = exportDailyContractBySalesType(ContractSalesTypeEnum.SALES.getValue(), startDateTime, endDateTime);
        fileBaseInfoDTOS.add(salesFileInfo);

        // 采购合同
        FileBaseInfoDTO purchaseFileInfo = exportDailyContractBySalesType(ContractSalesTypeEnum.PURCHASE.getValue(), startDateTime, endDateTime);
        fileBaseInfoDTOS.add(purchaseFileInfo);
        return fileBaseInfoDTOS;
    }

    @Override
    public List<ContractVOEntity> exportContractExcel(ContractBO contractBO) {

        List<ContractVOEntity> contractVOList = contractVODao.getContractByCondition(contractBO);

        for (ContractVOEntity contractVOEntity : contractVOList) {
            // 交易类型
            contractVOEntity.setTradeType(ContractTradeTypeEnum.getTTType(Integer.parseInt(contractVOEntity.getTradeType())).name());
            // 合同类型
            int contractType = Integer.parseInt(contractVOEntity.getContractType());
            contractVOEntity.setContractType(ContractTypeEnum.getDescByValue(contractType));
            // 合同状态
            contractVOEntity.setStatus(ContractStatusEnum.getDescByValue(Integer.parseInt(contractVOEntity.getStatus())));
            // 期货合约
            contractVOEntity.setDomainCode(contractVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_MEAL.getValue()) ? "M" + contractVOEntity.getDomainCode() : "Y" + contractVOEntity.getDomainCode());
            // 付款方式
            contractVOEntity.setPaymentType(PaymentTypeEnum.getDescByValue(Integer.valueOf(contractVOEntity.getPaymentType())));
            // 履约保证金释放方式
            contractVOEntity.setDepositReleaseType(DepositUseRuleEnum.getDescByValue(Integer.valueOf(contractVOEntity.getDepositReleaseType())));
            // 代加工
            contractVOEntity.setOem(Integer.parseInt(contractVOEntity.getOem()) == 1 ? "是" : "否");
            // STF
            contractVOEntity.setIsStf(Integer.parseInt(contractVOEntity.getIsStf()) == 1 ? "是" : "否");
            // 集团客户
            contractVOEntity.setEnterpriseName(contractVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractVOEntity.getEnterpriseName() : contractVOEntity.getSupplierEnterpriseName());
            // 发票类型
            contractVOEntity.setInvoiceType(StringUtils.isNotBlank(contractVOEntity.getInvoiceType()) ? InvoiceTypeEnum.getByValue(Integer.parseInt(contractVOEntity.getInvoiceType())).getDesc() : "");
            // 点价截止时间
            if (contractType != ContractTypeEnum.YI_KOU_JIA.getValue()) {
                if (StringUtils.isNotEmpty(contractVOEntity.getPriceEndTime())) {
                    contractVOEntity.setPriceEndTime(contractVOEntity.getPriceEndTime().split(" ")[0]);
                }
            } else {
                // 一口价不展示点价截止时间
                contractVOEntity.setPriceEndTime("");
            }
        }

        return contractVOList;
    }

    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 start
    @Override
    public List<ContractVOEntity> exportContractExcelMultiStatus(ContractBO contractBO) {
        List<ContractVOEntity> contractVOList = contractVODao.getContractByConditionMultiStatus(contractBO);

        for (ContractVOEntity contractVOEntity : contractVOList) {
            // 交易类型
            contractVOEntity.setTradeType(ContractTradeTypeEnum.getTTType(Integer.parseInt(contractVOEntity.getTradeType())).name());
            // 合同类型
            int contractType = Integer.parseInt(contractVOEntity.getContractType());
            contractVOEntity.setContractType(ContractTypeEnum.getDescByValue(contractType));
            // 合同状态
            contractVOEntity.setStatus(ContractStatusEnum.getDescByValue(Integer.parseInt(contractVOEntity.getStatus())));
            // 期货合约
            contractVOEntity.setDomainCode(contractVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_MEAL.getValue()) ? "M" + contractVOEntity.getDomainCode() : "Y" + contractVOEntity.getDomainCode());
            // 付款方式
            contractVOEntity.setPaymentType(PaymentTypeEnum.getDescByValue(Integer.valueOf(contractVOEntity.getPaymentType())));
            // 履约保证金释放方式
            contractVOEntity.setDepositReleaseType(DepositUseRuleEnum.getDescByValue(Integer.valueOf(contractVOEntity.getDepositReleaseType())));
            // 代加工
            contractVOEntity.setOem(Integer.parseInt(contractVOEntity.getOem()) == 1 ? "是" : "否");
            // STF
            contractVOEntity.setIsStf(Integer.parseInt(contractVOEntity.getIsStf()) == 1 ? "是" : "否");
            // 集团客户
            contractVOEntity.setEnterpriseName(contractVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractVOEntity.getEnterpriseName() : contractVOEntity.getSupplierEnterpriseName());
            // 发票类型
            contractVOEntity.setInvoiceType(StringUtils.isNotBlank(contractVOEntity.getInvoiceType()) ? InvoiceTypeEnum.getByValue(Integer.parseInt(contractVOEntity.getInvoiceType())).getDesc() : "");
            // 点价截止时间
            if (contractType != ContractTypeEnum.YI_KOU_JIA.getValue()) {
                if (StringUtils.isNotEmpty(contractVOEntity.getPriceEndTime())) {
                    contractVOEntity.setPriceEndTime(contractVOEntity.getPriceEndTime().split(" ")[0]);
                }
            } else {
                // 一口价不展示点价截止时间
                contractVOEntity.setPriceEndTime("");
            }
        }

        return contractVOList;
    }
    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 end

    private FileBaseInfoDTO exportDailyContractBySalesType(Integer salesType, String startDateTime, String endDateTime) {
        String salesTypes = salesType.equals(ContractSalesTypeEnum.SALES.getValue()) ? "sales" : "purchase";

        // 文件名称
        String fileName = DateUtil.format(DateUtil.date(), "yyyyMMdd") + "_00_" + salesTypes + "_detail_" + DateUtil.format(DateUtil.date(), "HHmmssSSS") + ".csv";

        // 文件路径
        String filePath = FileConstant.FILE_EXPORT + "/" + ModuleTypeEnum.CONTRACT.getModule() + "/" + DateTimeUtil.formatDateString(DateTime.now()) + "/" + salesTypes + "/";

        String fileFullPath = filePath + fileName;
        File csvFile = FileUtil.file(fileFullPath);
        CsvWriter writer = CsvUtil.getWriter(csvFile, StandardCharsets.UTF_8, true);

        // 标题
        if (salesType.equals(ContractSalesTypeEnum.SALES.getValue())) {
            writer.write(ContractExportDTO.getSalesCsvTitle());
        } else {
            writer.write(ContractExportDTO.getPurchaseCsvTitle());
        }

        // 数据处理
        // 获取每天的合同数据
        List<ContractEntity> dailyContractList = contractQueryService.getDailyContractList(salesType, startDateTime, endDateTime);
        for (ContractEntity contractEntity : dailyContractList) {
            // 数据填充
            ContractExportDTO contractImportDTO = new ContractExportDTO(contractEntity);

            if (salesType.equals(ContractSalesTypeEnum.SALES.getValue())) {
                writer.write(contractImportDTO.toSalesStringArray());
            } else {
                writer.write(contractImportDTO.toPurchaseStringArray());
            }
        }
        writer.flush();
        writer.close();

        // 将文件上传到Blob上
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(csvFile);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByInputStream(inputStream, filePath, fileName, BlobFileContextEnum.CSV.getFileType());

        // 保存文件记录
        LkgDailyFileEntity lkgDailyFileEntity = new LkgDailyFileEntity();
        lkgDailyFileEntity.setFileName(fileName)
                .setFilePath(filePath)
                .setFileSource("nav")
                .setSalesType(salesType)
                .setCheckDate(new SimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.addDays(1)))
                .setTotalCount(dailyContractList.size())
                .setHandleTime(new Date());
        lkgContractDailyCheckFacade.saveDailyFile(lkgDailyFileEntity);

        return fileBaseInfoDTO;
    }
}
