package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffPurchaseDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/20
 * @Version 1.0
 */
@Slf4j
@Component
public class TradeTicketEntityConvert {

    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired

    private IContractQueryService contractService;
    @Autowired
    private IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    private IContractPriceService contractPriceService;

    @Autowired
    protected SequenceUtil sequenceUtil;

    @Autowired
    private CompanyFacade companyFacade;

    @Autowired
    private SkuFacade skuFacade;

    @Autowired
    private SiteFacade siteFacade;

    /**
     * TT新增场景convert2TradeTicketEntity
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity add2TradeTicketEntity(TTDTO ttdto) {
        SalesContractAddTTDTO addTTDto = ttdto.getSalesContractAddTTDTO();
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getAddTradeTicketEntity(ttdto.getSalesContractAddTTDTO());
        tradeTicketEntity.setOperationSource(OperationSourceEnum.SYSTEM.getValue());
        tradeTicketEntity.setType(ttdto.getTtType());
        tradeTicketEntity.setGroupId(ttdto.getGroupId());
        tradeTicketEntity.setContractSource(ContractActionEnum.NEW.getActionValue());

        if (Arrays.asList(SubmitTypeEnum.SAVE.getValue(), SubmitTypeEnum.COPY_SAVE.getValue()).contains(ttdto.getSubmitType())) {
            tradeTicketEntity.setStatus(TTStatusEnum.NEW.getType());
        } else {
            tradeTicketEntity.setStatus(TTStatusEnum.APPROVING.getType());
        }
        // add by zengshl 如果是回购|关闭|解约定赔
        if (TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
            tradeTicketEntity.setStatus(TTStatusEnum.NEW.getType());
            //tradeTicketEntity.setSourceType(1);
            tradeTicketEntity.setContractSource(ContractActionEnum.BUYBACK.getActionValue());
        }
        if (TTTypeEnum.WASHOUT.getType().equals(tradeTicketEntity.getType())) {
            tradeTicketEntity.setContractSource(ContractActionEnum.WASHOUT.getActionValue());
            //tradeTicketEntity.setSourceType(2);
        }
        if (TTTypeEnum.CLOSED.getType().equals(tradeTicketEntity.getType())) {
            tradeTicketEntity.setContractSource(ContractActionEnum.CLOSED.getActionValue());
            //tradeTicketEntity.setSourceType(2);
        }
        // add by zengshl 如果是采购仓单|并且交易类型是线上的是直接完成的,TT 发起，回购发起的提交
        if (BuCodeEnum.WT.getValue().equals(tradeTicketEntity.getBuCode()) && SubmitTypeEnum.SUBMIT.getValue() == ttdto.getSubmitType()
                && !WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(addTTDto.getWarrantTradeType())
                && ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()
        ) {
            tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
        }
        // TODO 待优化
        tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
        tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        tradeTicketEntity.setGoodsCategoryId(tradeTicketEntity.getCategory1());
        tradeTicketEntity.setSubGoodsCategoryId(tradeTicketEntity.getCategory2());
        return tradeTicketEntity;
    }

    /**
     * 仓单注销场景convert2TradeTicketEntity
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity writeOff2TradeTicketEntity(TTDTO ttdto, ArrangeContext arrangeContext) {
        ContractWriteOffDTO contractWriteOffDTO = ttdto.getContractWriteOffDTO();
        ContractEntity contractEntity = ttdto.getContractEntity();
        // 父合同
        ContractEntity parentContract = contractWriteOffDTO.getParentContractEntity();
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        ContractSalesTypeEnum salesType = ContractSalesTypeEnum.getByValue(ttdto.getSalesType());
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        tradeTicketEntity.setContractId(arrangeContext.getContractId());
        tradeTicketEntity.setContractCode(arrangeContext.getContractCode());
        tradeTicketEntity.setFutureCode(contractEntity.getFutureCode());
        tradeTicketEntity.setGroupId(arrangeContext.getGroupId());
        tradeTicketEntity.setBuCode(BuCodeEnum.WT.getValue());
        tradeTicketEntity.setUsage(UsageEnum.DELIVERY.getValue());
        tradeTicketEntity.setBeforeContractNum(parentContract.getContractNum());
        tradeTicketEntity.setChangeContractNum(contractWriteOffDTO.getWriteOffNum());
        tradeTicketEntity.setAfterContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setSiteCode(contractWriteOffDTO.getSiteCode());
        tradeTicketEntity.setSiteName(contractWriteOffDTO.getSiteName());
        tradeTicketEntity.setType(TTTypeEnum.WRITE_OFF.getType());
        tradeTicketEntity.setContractType(ContractTypeEnum.YI_KOU_JIA.getValue());
        // TradeType
        tradeTicketEntity.setTradeType(ttdto.getContractTradeType());
        tradeTicketEntity.setFutureCode(contractWriteOffDTO.getFutureCode());
        // userId
        String userId = JwtUtils.getCurrentUserId();
        tradeTicketEntity.setCreatedBy(Integer.parseInt(userId));
        tradeTicketEntity.setUpdatedBy((Integer.parseInt(userId)));
        // 生成TT编号
        String ttCode = CodeGeneratorUtil.genTTNewCodeBySaleType(salesType);
        tradeTicketEntity.setCode(ttCode);
        // 记录父合同数量
        if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffDTO.getWriteOffAction()) {
            tradeTicketEntity.setBeforeContractNum(contractEntity.getContractNum());
        }
        // goods
        Integer goodsId;
        if (TTWriteOffActionEnum.PURCHASE_ADD.equals(writeOffTTAction)) {
            ContractWriteOffPurchaseDTO purchaseDTO = contractWriteOffDTO.getContractWriteOffPurchaseDTO();
            tradeTicketEntity.setGoodsId(purchaseDTO.getGoodsId());
            tradeTicketEntity.setGoodsName(purchaseDTO.getGoodsName());
            tradeTicketEntity.setPayConditionId(purchaseDTO.getPayConditionId() == null ? contractEntity.getPayConditionId() : purchaseDTO.getPayConditionId());
            goodsId = purchaseDTO.getGoodsId();
        } else {
            goodsId = contractWriteOffDTO.getGoodsId();
            tradeTicketEntity.setGoodsId(goodsId);
            tradeTicketEntity.setGoodsName(contractWriteOffDTO.getGoodsName());
            tradeTicketEntity.setPayConditionId(contractWriteOffDTO.getPayConditionId() == null ? contractEntity.getPayConditionId() : contractWriteOffDTO.getPayConditionId());
        }
        SkuEntity skuEntity = skuFacade.getSkuById(goodsId);
        if (ObjectUtil.isNotEmpty(skuEntity)) {
            tradeTicketEntity.setCategory1(skuEntity.getCategory1());
            tradeTicketEntity.setCategory2(skuEntity.getCategory2());
            tradeTicketEntity.setCategory3(skuEntity.getCategory3());
            tradeTicketEntity.setGoodsName(skuEntity.getFullName());
            tradeTicketEntity.setCommodityName(skuEntity.getNickName());
            tradeTicketEntity.setGoodsCategoryId(skuEntity.getCategory1());
            tradeTicketEntity.setSubGoodsCategoryId(skuEntity.getCategory2());
        }
        // customer  modify by zengshl
        tradeTicketEntity.setCustomerId(contractEntity.getCustomerId());
        tradeTicketEntity.setCustomerCode(contractEntity.getCustomerCode());
        tradeTicketEntity.setCustomerName(contractEntity.getCustomerName());
        tradeTicketEntity.setSupplierId(contractEntity.getSupplierId());
        tradeTicketEntity.setSupplierName(contractEntity.getSupplierName());
        tradeTicketEntity.setSalesType(salesType.getValue());
        tradeTicketEntity.setOperationSource(OperationSourceEnum.SYSTEM.getValue());
        // add by zengshl 记录新合同的数据信息 company
        if (ObjectUtil.isNotEmpty(ttdto.getContractEntity())) {
            tradeTicketEntity.setDomainCode(ttdto.getContractEntity().getDomainCode());
            tradeTicketEntity.setBelongCustomerId(ttdto.getContractEntity().getBelongCustomerId());
            tradeTicketEntity.setContractNature(ttdto.getContractEntity().getContractNature());
            tradeTicketEntity.setCompanyId(ttdto.getContractEntity().getCompanyId());
            tradeTicketEntity.setCompanyName(ttdto.getContractEntity().getCompanyName());
        }
        // add by zengshl 记录下原合同|合同来源是注销
        tradeTicketEntity.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        tradeTicketEntity.setSourceContractId(contractWriteOffDTO.getContractId());
        // 新增
        if (TTWriteOffActionEnum.PURCHASE_ADD.equals(writeOffTTAction) || TTWriteOffActionEnum.DELIVERY_ADD.equals(writeOffTTAction)) {
            tradeTicketEntity.setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue());
            tradeTicketEntity.setStatus(TTStatusEnum.APPROVING.getType());
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            tradeTicketEntity.setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue());
            tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        }
        if (TTWriteOffActionEnum.REVISE.equals(writeOffTTAction)) {
            tradeTicketEntity.setSiteCode(contractEntity.getSiteCode())
                    .setContractStatus(contractEntity.getStatus())
                    .setSiteName(contractEntity.getSiteName())
                    .setGoodsId(contractEntity.getGoodsId())
                    .setGoodsName(contractEntity.getGoodsName());
            tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
            if (ContractActionEnum.WRITE_OFF_B.getActionValue() == contractWriteOffDTO.getWriteOffAction()) {
                tradeTicketEntity.setBeforeContractNum(parentContract.getContractNum().subtract(contractWriteOffDTO.getWriteOffNum()));
            }
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            // 修改-隐藏
            tradeTicketEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
        }
        return tradeTicketEntity;
    }


    /**
     * 仓单豆二注销场景convert2TradeTicketEntity
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity writeOff2OMTradeTicketEntity(TTDTO ttdto, ArrangeContext arrangeContext) {
        ContractEntity contractEntity = ttdto.getContractEntity();
        ContractEntity parentContract = ttdto.getContractWriteOffOMDTO().getParentContractEntity();
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        ContractSalesTypeEnum salesType = ContractSalesTypeEnum.getByValue(ttdto.getSalesType());
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
        tradeTicketEntity.setId(null);
        tradeTicketEntity.setContractId(arrangeContext.getContractId());
        tradeTicketEntity.setContractCode(arrangeContext.getContractCode());
        tradeTicketEntity.setGroupId(arrangeContext.getGroupId());
        tradeTicketEntity.setChangeContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setAfterContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setBuCode(BuCodeEnum.WT.getValue())
                .setUsage(UsageEnum.DELIVERY.getValue());
        tradeTicketEntity.setGoodsCategoryId(contractEntity.getCategory1());
        tradeTicketEntity.setSubGoodsCategoryId(contractEntity.getCategory2());
        tradeTicketEntity.setType(TTTypeEnum.WRITE_OFF.getType());
        tradeTicketEntity.setContractType(ContractTypeEnum.YI_KOU_JIA.getValue());
        tradeTicketEntity.setBeforeContractNum(parentContract.getContractNum());
        // TradeType
        tradeTicketEntity.setTradeType(ttdto.getContractTradeType());
        // userId
        String userId = JwtUtils.getCurrentUserId();
        tradeTicketEntity.setCreatedBy(Integer.parseInt(userId));
        tradeTicketEntity.setUpdatedBy((Integer.parseInt(userId)));
        // 生成TT编号
        String ttCode = CodeGeneratorUtil.genTTNewCodeBySaleType(salesType);
        tradeTicketEntity.setCode(ttCode);
        // add by zengshl 记录下原合同|合同来源是注销
        tradeTicketEntity.setContractSource(ContractActionEnum.WARRANT_WRITEOFF.getActionValue());
        tradeTicketEntity.setSourceContractId(contractEntity.getId());
        // 新增
        tradeTicketEntity.setContractStatus(ContractStatusEnum.EFFECTIVE.getValue());
        tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
        tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
        tradeTicketEntity.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
        tradeTicketEntity.setContractStatus(ContractStatusEnum.EFFECTIVE.getValue());
        tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.PROCESSING.getValue());

        // 原仓单生成不可见的TT
        if (TTWriteOffActionEnum.REVISE.equals(writeOffTTAction)) {
            tradeTicketEntity.setStatus(TTStatusEnum.NEW.getType());
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            // 修改-隐藏
            tradeTicketEntity.setIsDeleted(1);
        }
        return tradeTicketEntity;
    }

    /**
     * 取消声明
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity cancelTradeTicketEntity(TTDTO ttdto) {
        ContractEntity contractEntity = ttdto.getContractEntity();
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        ContractSalesTypeEnum salesType = ContractSalesTypeEnum.getByValue(ttdto.getSalesType());
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
        tradeTicketEntity.setId(null);
        tradeTicketEntity.setContractId(contractEntity.getId());
        tradeTicketEntity.setSourceContractId(contractEntity.getId());
        tradeTicketEntity.setContractCode(contractEntity.getContractCode());
        tradeTicketEntity.setChangeContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setAfterContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setBuCode(contractEntity.getBuCode());
        tradeTicketEntity.setGoodsCategoryId(contractEntity.getCategory1());
        tradeTicketEntity.setSubGoodsCategoryId(contractEntity.getCategory2());
        tradeTicketEntity.setType(TTTypeEnum.CONTRACT_CANCEL.getType());
        tradeTicketEntity.setTradeType(ContractTradeTypeEnum.CONTRACT_CANCEL.getValue());
        tradeTicketEntity.setContractSource(ContractActionEnum.CONTRACT_CANCEL.getTradeType());
        // userId
        String userId = JwtUtils.getCurrentUserId();
        tradeTicketEntity.setCreatedBy(Integer.parseInt(userId));
        tradeTicketEntity.setUpdatedBy((Integer.parseInt(userId)));
        // 生成TT编号
        String ttCode = CodeGeneratorUtil.genTTNewCodeBySaleType(salesType);
        tradeTicketEntity.setCode(ttCode);
        // add by zengshl 记录下原合同|合同来源是注销
        tradeTicketEntity.setContractSource(ContractActionEnum.CONTRACT_CANCEL.getActionValue());
        tradeTicketEntity.setSourceContractId(contractEntity.getId());
        tradeTicketEntity.setStatus(TTStatusEnum.APPROVING.getType());
        tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        BeanUtils.copyProperties(tradeTicketEntity, salesContractAddTTDTO);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        return tradeTicketEntity;
    }

    /**
     * 仓单合同作废
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity invalidTradeTicketEntity(TTDTO ttdto) {
        ContractEntity contractEntity = ttdto.getContractEntity();
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        ContractSalesTypeEnum salesType = ContractSalesTypeEnum.getByValue(ttdto.getSalesType());
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
        tradeTicketEntity.setId(null);
        tradeTicketEntity.setContractId(contractEntity.getId());
        tradeTicketEntity.setSourceContractId(contractEntity.getId());
        tradeTicketEntity.setContractCode(contractEntity.getContractCode());
        tradeTicketEntity.setChangeContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setAfterContractNum(contractEntity.getContractNum());
        tradeTicketEntity.setBuCode(contractEntity.getBuCode());
        tradeTicketEntity.setGoodsCategoryId(contractEntity.getCategory1());
        tradeTicketEntity.setSubGoodsCategoryId(contractEntity.getCategory2());
        tradeTicketEntity.setType(TTTypeEnum.INVALID.getType());
        tradeTicketEntity.setTradeType(ContractTradeTypeEnum.INVALID.getValue());
//        tradeTicketEntity.setContractSource(ContractActionEnum.INVALID.getTradeType());
        // TradeType
//        tradeTicketEntity.setTradeType(ttdto.getContractTradeType());
        // userId
        String userId = JwtUtils.getCurrentUserId();
        tradeTicketEntity.setCreatedBy(Integer.parseInt(userId));
        tradeTicketEntity.setUpdatedBy((Integer.parseInt(userId)));
        // 生成TT编号
        String ttCode = CodeGeneratorUtil.genTTNewCodeBySaleType(salesType);
        tradeTicketEntity.setCode(ttCode);
        // add by zengshl 记录下原合同|合同来源是注销
        tradeTicketEntity.setContractSource(ContractActionEnum.INVALID.getActionValue());
        tradeTicketEntity.setSourceContractId(contractEntity.getId());
        // 新增 TODO 待优化
        if (WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(contractEntity.getWarrantTradeType())) {
            tradeTicketEntity.setStatus(TTStatusEnum.APPROVING.getType());
            tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());
        } else {
            tradeTicketEntity.setStatus(TTStatusEnum.DONE.getType());
            tradeTicketEntity.setApprovalStatus(TTApproveStatusEnum.WITHOUT_APPROVE.getValue());
            tradeTicketEntity.setApprovalType(ContractApproveRuleEnum.NONE.getValue());
            tradeTicketEntity.setContractStatus(ContractStatusEnum.EFFECTIVE.getValue());
            tradeTicketEntity.setContractSignatureStatus(ContractSignStatusEnum.PROCESSING.getValue());
        }
        BeanUtils.copyProperties(tradeTicketEntity, salesContractAddTTDTO);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        return tradeTicketEntity;
    }

    /**
     * 仓单分配场景convert2TradeTicketEntity
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity allocate2TradeTicketEntity(TTDTO ttdto) {
        SalesContractAddTTDTO addDto = ttdto.getSalesContractAddTTDTO();
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getAddTradeTicketEntity(addDto);
        tradeTicketEntity
                .setSalesType(addDto.getSalesType())
                .setContractStatus(ContractStatusEnum.COMPLETED.getValue())
                .setOperationSource(OperationSourceEnum.SYSTEM.getValue())
                .setType(addDto.getType())
                .setContractSource(ContractActionEnum.NEW.getActionValue())
                .setContractSignatureStatus(ContractSignStatusEnum.PROCESSING.getValue())
                .setStatus(TTStatusEnum.DONE.getType())
                .setGoodsCategoryId(tradeTicketEntity.getCategory1())
                .setSubGoodsCategoryId(tradeTicketEntity.getCategory2())
                .setBuCode(addDto.getBuCode());

        // 设置下合同来源
        if (TTTypeEnum.ALLOCATE.getType().equals(tradeTicketEntity.getType())) {
            tradeTicketEntity.setContractSource(ContractActionEnum.ALLOCATE.getActionValue());
        }
        if (TTTypeEnum.ASSIGN.getType().equals(tradeTicketEntity.getType())) {
            tradeTicketEntity.setContractSource(ContractActionEnum.ASSIGN.getActionValue());
        }
        return tradeTicketEntity;
    }

    /**
     * 交提货方式：dbt_delivery_type
     * 值转对象 add by zengshl
     *
     * @param deliveryTypeId
     * @return
     */
    public String deliveryTypeConvertValue(Integer deliveryTypeId) {
        if (ObjectUtil.isNotEmpty(deliveryTypeId)) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(deliveryTypeId);
            return ObjectUtil.isNotEmpty(deliveryTypeEntity) ? deliveryTypeEntity.getName() : "";
        }
        return "";
    }

    public TradeTicketEntity split2TradeTicketEntity(TTDTO ttdto, ArrangeContext arrangeContext) {

        SalesContractSplitTTDTO salesContractSplitTTDTO = ttdto.getSalesContractSplitTTDTO();
        String groupId = ttdto.getGroupId();

        TradeTicketEntity tradeTicketEntity = null;
        BigDecimal beforeContractNum = BigDecimal.ZERO;
        BigDecimal changeContractNum = salesContractSplitTTDTO.getContractNum() != null ? salesContractSplitTTDTO.getContractNum() : BigDecimal.ZERO;
        BigDecimal afterContractNum = changeContractNum;
        if (salesContractSplitTTDTO.getAddedSignatureType() == 1 || salesContractSplitTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, contractEntity);
            tradeTicketEntity
                    .setId(null)
                    .setCode(salesContractSplitTTDTO.getCode())
                    .setContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractSplitTTDTO.getSupplierAccountId())
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
            ;

            if (salesContractSplitTTDTO.getSourceContractId() != null) {
                beforeContractNum = contractEntity.getContractNum();
                changeContractNum = salesContractSplitTTDTO.getContractNum();
                afterContractNum = beforeContractNum.subtract(changeContractNum);
            }
        } else {
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractSplitTTDTO);
            tradeTicketEntity
                    .setContractId(salesContractSplitTTDTO.getSonContractId())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractSplitTTDTO.getSupplierAccountId())
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
            ;
            //查询客户信息
            CustomerDTO customer = null;
            if (null != salesContractSplitTTDTO.getCustomerId()) {
                customer = customerFacade.getCustomerById(salesContractSplitTTDTO.getCustomerId());
                if (customer != null) {
                    tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
                    tradeTicketEntity.setCustomerId(customer.getId());
                    tradeTicketEntity.setCustomerName(customer.getName());
                }
            }

            CustomerDTO supplier = null;
            if (null != salesContractSplitTTDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractSplitTTDTO.getSupplierId());
                if (supplier != null) {
                    tradeTicketEntity.setSupplierId(supplier.getId());
                    tradeTicketEntity.setSupplierName(supplier.getName());
                }
            }

            // 账套更新
            if (StringUtils.isNotBlank(salesContractSplitTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractSplitTTDTO.getSiteCode());
                Integer belongCustomerId = siteEntity.getBelongCustomerId();
                Integer companyId = siteEntity.getCompanyId();
                String companyName = siteEntity.getCompanyName();

                tradeTicketEntity
                        .setSiteCode(siteEntity.getCode())
                        .setSiteName(siteEntity.getName())
                        .setBelongCustomerId(belongCustomerId)
                        .setCompanyId(companyId)
                        .setCompanyName(companyName);

                salesContractSplitTTDTO.setCompanyId(companyId)
                        .setCompanyName(companyName)
                        .setBelongCustomerId(belongCustomerId);
            }

            // 商品信息
            if (salesContractSplitTTDTO.getGoodsId() != null) {
                SkuEntity skuEntity = skuFacade.getSkuById(salesContractSplitTTDTO.getGoodsId());
                if (skuEntity != null) {
                    tradeTicketEntity
                            .setGoodsId(skuEntity.getId())
                            .setGoodsName(skuEntity.getFullName())
                            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 start
                            .setCommodityName(StringUtils.isNotBlank(salesContractSplitTTDTO.getCommodityName()) ? salesContractSplitTTDTO.getCommodityName() : skuEntity.getNickName())
                            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 end
                            .setCategory1(skuEntity.getCategory1())
                            .setCategory2(skuEntity.getCategory2())
                            .setCategory3(skuEntity.getCategory3())
                            .setGoodsCategoryId(skuEntity.getCategory1())
                            .setSubGoodsCategoryId(skuEntity.getCategory2());
                }

                // 商品昵称
//                if (StringUtils.isNotBlank(salesContractSplitTTDTO.getCommodityName())) {
//                    tradeTicketEntity.setCommodityName(salesContractSplitTTDTO.getCommodityName());
//                }
            }
        }

        tradeTicketEntity.setBuCode(BuCodeEnum.ST.getValue());
        tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());

        tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setBeforeContractNum(beforeContractNum);
        tradeTicketEntity.setChangeContractNum(changeContractNum);
        tradeTicketEntity.setAfterContractNum(afterContractNum);

        // 补充基本信息
        tradeTicketEntity
                .setGroupId(groupId)
                .setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setOperationSource(OperationSourceEnum.SYSTEM.getValue())
                .setType(TTTypeEnum.SPLIT.getType())
                .setGoodsCategoryId(salesContractSplitTTDTO.getCategory1())
                .setSubGoodsCategoryId(salesContractSplitTTDTO.getCategory2())
                .setConfirmPriceInfo(arrangeContext.getConfirmPriceInfo())
                .setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setContractSource(salesContractSplitTTDTO.getContractSource());

        return tradeTicketEntity;
    }

    public TradeTicketEntity revise2TradeTicketEntity(TTDTO ttdto) {
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();
        String groupId = ttdto.getGroupId();

        TradeTicketEntity tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractReviseTTDTO);
        if (salesContractReviseTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getSourceContractId());
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, contractEntity);
            tradeTicketEntity
                    .setId(null)
                    .setCode(salesContractReviseTTDTO.getCode())
                    .setType(TTTypeEnum.SPLIT.getType())
                    .setContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setTradeType(salesContractReviseTTDTO.getTradeType())
                    .setGroupId(groupId)
                    .setBankId(salesContractReviseTTDTO.getSupplierAccountId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId());
        } else {
            tradeTicketEntity
                    .setGroupId(groupId)
                    .setType(TTTypeEnum.REVISE.getType())
                    .setBankId(salesContractReviseTTDTO.getSupplierAccountId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId());

            if (salesContractReviseTTDTO.getSourceContractId() != null) {
                ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getSourceContractId());
                BigDecimal beforeContractNum = contractEntity.getContractNum();
                BigDecimal changeContractNum = salesContractReviseTTDTO.getContractNum();
                if (salesContractReviseTTDTO.getTradeType().equals(ContractTradeTypeEnum.REVISE_NORMAL.getValue())) {
                    changeContractNum = BigDecimal.ZERO;
                }
                BigDecimal afterContractNum = beforeContractNum.subtract(changeContractNum);
                tradeTicketEntity.setBeforeContractNum(beforeContractNum);
                tradeTicketEntity.setChangeContractNum(changeContractNum);
                tradeTicketEntity.setAfterContractNum(afterContractNum);
            }

            // 账套更新
            if (StringUtils.isNotBlank(salesContractReviseTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractReviseTTDTO.getSiteCode());
                Integer belongCustomerId = siteEntity.getBelongCustomerId();
                Integer companyId = siteEntity.getCompanyId();
                String companyName = siteEntity.getCompanyName();

                tradeTicketEntity
                        .setSiteCode(siteEntity.getCode())
                        .setSiteName(siteEntity.getName())
                        .setBelongCustomerId(belongCustomerId)
                        .setCompanyId(companyId)
                        .setCompanyName(companyName);

                salesContractReviseTTDTO.setCompanyId(companyId)
                        .setCompanyName(companyName)
                        .setBelongCustomerId(belongCustomerId);
            }

            // 商品信息
            if (salesContractReviseTTDTO.getGoodsId() != null) {
                SkuEntity skuEntity = skuFacade.getSkuById(salesContractReviseTTDTO.getGoodsId());
                if (skuEntity != null) {
                    tradeTicketEntity
                            .setGoodsId(skuEntity.getId())
                            .setCommodityName(StringUtils.isNotBlank(salesContractReviseTTDTO.getCommodityName()) ? salesContractReviseTTDTO.getCommodityName() : skuEntity.getNickName())
                            .setGoodsName(skuEntity.getFullName())
                            .setCategory1(skuEntity.getCategory1())
                            .setCategory2(skuEntity.getCategory2())
                            .setCategory3(skuEntity.getCategory3())
                            .setGoodsCategoryId(skuEntity.getCategory1())
                            .setSubGoodsCategoryId(skuEntity.getCategory2());
                    salesContractReviseTTDTO.setGoodsName(skuEntity.getFullName());

                    // 税率
                    if (skuEntity.getTaxRate() != null) {
                        BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                        salesContractReviseTTDTO.setTaxRate(taxRate);
                        // cif价格
                        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, salesContractReviseTTDTO.getUnitPrice(), taxRate.add(BigDecimal.ONE));
                        salesContractReviseTTDTO.setCifUnitPrice(cifUnitPrice);
                    }
                }
            }
            // 商品昵称
            if (StringUtils.isNotBlank(salesContractReviseTTDTO.getCommodityName())) {
                //tradeTicketEntity.setCommodityName(salesContractReviseTTDTO.getCommodityName());
            }
        }

        Date date = new Date();
        tradeTicketEntity
                .setContractStatus(ContractStatusEnum.MODIFYING.getValue())
                .setOperationSource(OperationSourceEnum.SYSTEM.getValue())
                .setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setStatus(TTStatusEnum.APPROVING.getType())
                .setBuCode(ObjectUtil.isNotEmpty(ttdto.getBuCode()) ? ttdto.getBuCode() : BuCodeEnum.ST.getValue())
                .setContractNature(salesContractReviseTTDTO.getContractNature())
                .setCreatedAt(date)
                .setUpdatedAt(date)
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        return tradeTicketEntity;
    }

    public TradeTicketEntity price2TradeTicketEntity(TTDTO ttdto) {
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttdto.getSalesContractTTPriceDTO();
        TradeTicketEntity tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractTTPriceDTO);
        String userId = JwtUtils.getCurrentUserId();

        if (salesContractTTPriceDTO.getContractType() != null) {
            tradeTicketEntity.setContractType(salesContractTTPriceDTO.getContractType());
        }

        try {
            if (salesContractTTPriceDTO.getOwnerId() != null) {
                tradeTicketEntity.setOwnerId(salesContractTTPriceDTO.getOwnerId());
            }

            tradeTicketEntity
                    .setBeforeContractNum(BigDecimal.ZERO)
                    .setChangeContractNum(salesContractTTPriceDTO.getNum())
                    .setAfterContractNum(salesContractTTPriceDTO.getNum())
                    .setDomainCode(salesContractTTPriceDTO.getDomainCode());

            if (salesContractTTPriceDTO.getSupplierId() != null && salesContractTTPriceDTO.getGoodsId() != null) {
                SkuEntity skuEntity = skuFacade.getSkuById(salesContractTTPriceDTO.getGoodsId());
                if (skuEntity != null) {
                    tradeTicketEntity
                            .setGoodsId(skuEntity.getId())
                            .setGoodsName(skuEntity.getFullName())
                            .setCategory1(skuEntity.getCategory1())
                            .setCategory2(skuEntity.getCategory2())
                            .setCategory3(skuEntity.getCategory3())
                            .setGoodsCategoryId(skuEntity.getCategory1())
                            .setSubGoodsCategoryId(skuEntity.getCategory2());
                }
            }

            tradeTicketEntity
                    .setBuCode(salesContractTTPriceDTO.getBuCode())
                    .setContractNature(salesContractTTPriceDTO.getContractNature());

        } catch (Exception e) {
            log.error("Error while converting price2TradeTicketEntity", e);
            throw new RuntimeException("Conversion failed", e);
        }

        tradeTicketEntity
                .setId(salesContractTTPriceDTO.getTtId())
                .setCreatedBy(Integer.parseInt(userId))
                .setUpdatedBy(Integer.parseInt(userId))
                .setSalesType(salesContractTTPriceDTO.getSalesType())
                .setBankId(salesContractTTPriceDTO.getSupplierAccountId())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setSourceContractId(salesContractTTPriceDTO.getSourceContractId())
                .setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setOperationSource(OperationSourceEnum.SYSTEM.getValue())
                .setType(TTTypeEnum.PRICE.getType())
                .setContractSource(ContractActionEnum.PRICE_CONFIRM.getActionValue())
                .setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setStatus(TTStatusEnum.APPROVING.getType());

        return tradeTicketEntity;
    }

    public TradeTicketEntity transfer2TradeTicketEntity(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();

        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BigDecimal beforeContractNum = BigDecimal.ZERO;
        BigDecimal changeContractNum = salesContractTTTransferDTO.getNum() != null ? salesContractTTTransferDTO.getNum() : BigDecimal.ZERO;
        BigDecimal afterContractNum = changeContractNum;

        ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTTransferDTO.getSourceContractId());
        BeanUtils.copyProperties(contractEntity, tradeTicketEntity);

        if (salesContractTTTransferDTO.getAddedSignatureType() == -1) {
            tradeTicketEntity
                    .setId(null)
                    .setContractId(salesContractTTTransferDTO.getSourceContractId())
                    .setBankId(salesContractTTTransferDTO.getSupplierAccountId())

            ;
            if (salesContractTTTransferDTO.getSourceContractId() != null) {
                beforeContractNum = contractEntity.getContractNum();
                changeContractNum = salesContractTTTransferDTO.getNum();
                afterContractNum = beforeContractNum.subtract(changeContractNum);
            }
        } else {
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractTTTransferDTO);

            tradeTicketEntity
                    .setContractId(salesContractTTTransferDTO.getSonContractId() != null ? salesContractTTTransferDTO.getSonContractId() : salesContractTTTransferDTO.getContractId())
                    .setBankId(salesContractTTTransferDTO.getSupplierAccountId())
            ;

            if (salesContractTTTransferDTO.getContractType() != null) {
                tradeTicketEntity.setContractType(salesContractTTTransferDTO.getContractType());
            }
        }

        tradeTicketEntity.setBeforeContractNum(beforeContractNum);
        tradeTicketEntity.setChangeContractNum(changeContractNum);
        tradeTicketEntity.setAfterContractNum(afterContractNum);

        // 商品信息
        if (contractEntity.getGoodsId() != null) {
            SkuEntity skuEntity = skuFacade.getSkuById(contractEntity.getGoodsId());
            if (skuEntity != null) {
                tradeTicketEntity
                        .setGoodsId(skuEntity.getId())
                        .setGoodsName(skuEntity.getFullName())
                        .setCategory1(skuEntity.getCategory1())
                        .setCategory2(skuEntity.getCategory2())
                        .setCategory3(skuEntity.getCategory3())
                        .setGoodsCategoryId(skuEntity.getCategory1())
                        .setSubGoodsCategoryId(skuEntity.getCategory2());
            }
        }

        // 判断是否是销售合同
        Integer tradeType = null;
        Integer contractSource = null;
        Integer ttType = null;
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();
        String code = isSales ? sequenceUtil.generateSpotSalesTTCode() : sequenceUtil.generateSpotPurchaseTTCode();
        switch (TTTranferTypeEnum.getByValue(salesContractTTTransferDTO.getType())) {
            case TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_ALL.getValue();
                contractSource = ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue();
                ttType = TTTypeEnum.TRANSFER.getType();
//                code = sequenceUtil.generateSpotTransferTTCode(); TODO 生成子编号
                break;
            case PART_TRANSFER_MONTH:
                tradeType = ContractTradeTypeEnum.TRANSFER_PART.getValue();
                contractSource = ContractActionEnum.TRANSFER_CONFIRM.getActionValue();
                ttType = TTTypeEnum.TRANSFER.getType();
                break;
            case REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue();
                ttType = TTTypeEnum.REVERSE_PRICE.getType();
                break;
            case PART_REVERSE_PRICING:
                tradeType = ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue();
                contractSource = ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue();
                ttType = TTTypeEnum.REVERSE_PRICE.getType();
                break;
            default:
                break;
        }
        Date date = new Date();
        tradeTicketEntity
                .setCode(code)
                .setContractSource(contractSource)
                .setTradeType(tradeType)
                .setType(ttType)
                .setGroupId(ttdto.getGroupId())
                .setSalesType(salesContractTTTransferDTO.getSalesType())
                .setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setOperationSource(OperationSourceEnum.SYSTEM.getValue())
                .setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setStatus(TTStatusEnum.APPROVING.getType())
                .setBuCode(salesContractTTTransferDTO.getBuCode())
                .setContractNature(salesContractTTTransferDTO.getContractNature())
                .setSourceContractId(salesContractTTTransferDTO.getSourceContractId())
                .setCreatedAt(date)
                .setUpdatedAt(date)
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        salesContractTTTransferDTO.setCode(code);

        return tradeTicketEntity;
    }

}
