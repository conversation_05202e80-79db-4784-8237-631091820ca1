package com.navigator.trade.app.tt.domain.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TTAddQO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TT领域查询服务
 * @Date 2024/7/14 16:16
 * @Version 1.0
 */

public interface TTQueryDomainService {
    /**
     *查询TT列表
     * @return
     */
    Result queryTTList(QueryDTO<TTQueryDTO> ttQueryDTO);

    Result queryTTListNew(QueryDTO<TTQueryDTO> ttQueryDTO);

    /**
     * 根据ttId查询TT详情
     * @param qo
     * @return
     */
    TradeTicketDO queryTradeTicketDOByTTID(TradeTicketQO qo);


    /**
     * 查询TradeTicketEntity详情，查到多个时，会排序，返回第一条数据
     *  字段优先级为：1、ttId 2、contractId 3、signId
     * @param qo
     * @return
     */
    TradeTicketEntity fetchTradeTicketEntity(TradeTicketQO qo);


    /**
     * 查询TTAddEntity详情
     *
     * @param qo
     * @return
     */
    TTAddEntity fetchTTAddEntity(TTAddQO qo);


    /**
     *  查询TTPriceEntity列表
     *
     * @param qo
     * @return
     */
    List<TTPriceEntity> fetchTTPriceEntities(TTPriceQO qo);

    /**
     * 查询TTPriceEntity详情
     *
     * @param qo
     * @return
     */
    TTPriceEntity fetchTTPriceEntity(TTPriceQO qo);

    /**
     * 获取ContractPrice
     *
     * @param contractId
     * @return
     */
    ContractPriceEntity getContractPriceEntityContractId(Integer contractId);


    /**
     * 根据ttId获取Modify
     * @param ttId
     * @return
     */
    TTModifyEntity getTTModifyEntityByTTId(Integer ttId);

    /**
     * 根据ttid查询TT
     *
     * @param ttId 协议id
     * @return
     */
    TradeTicketEntity getByTtId(Integer ttId);

    /**
     * 根据groupId获取关联的另一条数据
     *
     * @param
     * @return
     */
    TradeTicketEntity getByGroupId(String groupId, Integer id);

    /**
     * 根据groupId-仓单比较特殊注销C有三个TT
     *
     * @param
     * @return
     */
    TradeTicketEntity getWarrantByGroupId(String groupId, Integer id);

    /**
     * 根据合同ID groupId 获取TT | 或者合同ID直接获取
     *
     * @param
     * @return
     */
    TradeTicketEntity getByContractId(Integer contractId, String groupId);

    /**
     * TODO NEO 确认一下这个转月逻辑
     * 合同生成的时候，根据客户配置，生成可转月次数
     * 转月，每转月一次就减1，只要该数量大于0即可转
     */
    List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId);

    List<TTTranferEntity> getTTTranferByPriceAllocateId(Integer priceAllocateId);

    List<TTTranferEntity> getTTTranferByContractId(Integer contractId, Integer Id);

    /**
     * 根据申请单id查询定价单
     *
     * @param priceApplyId
     * @return
     */
    List<TTPriceEntity> getTTPriceByApplyId(Integer priceApplyId);

    List<TTPriceEntity> getTTPriceByAllocateId(Integer allocateId);

    /**
     * 根据sourceId查询定价单
     *
     * @param sourceId
     * @return
     */
    List<TTPriceEntity> getTTPriceBySourceId(Integer sourceId);
}
