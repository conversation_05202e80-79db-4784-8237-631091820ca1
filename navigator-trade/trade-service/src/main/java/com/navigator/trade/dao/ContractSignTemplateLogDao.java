package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.ContractSignTemplateLogMapper;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateLogDTO;
import com.navigator.trade.pojo.entity.ContractSignTemplateLogEntity;
import com.navigator.trade.pojo.enums.ContractSignTemplateType;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2022-03-03 18:55
 */
@Dao
public class ContractSignTemplateLogDao extends BaseDaoImpl<ContractSignTemplateLogMapper, ContractSignTemplateLogEntity> {

    /**
     * 记录协议模版生成、提交信息
     *
     * @param signTemplateLogDTO 协议模板生成日志信息
     */
    public ContractSignTemplateLogEntity saveSignTemplateLog(SignTemplateLogDTO signTemplateLogDTO) {
        //异常信息记录
        String errorMessage = StringUtils.isBlank(signTemplateLogDTO.getMemo()) ? "" : signTemplateLogDTO.getMemo();
        ContractSignTemplateLogEntity signTemplateLogEntity = new ContractSignTemplateLogEntity()
                .setContractSignId(signTemplateLogDTO.getContractSignId())
                .setContractId(signTemplateLogDTO.getContractId())
                .setTtId(signTemplateLogDTO.getTtId())
                .setBizData(signTemplateLogDTO.getBizData())
                .setContent(signTemplateLogDTO.getTemplateContent())
                .setType(signTemplateLogDTO.getSignTemplateType())
                .setMemo(ContractSignTemplateType.getEnumByValue(signTemplateLogDTO.getSignTemplateType()).getDesc() + errorMessage);
        this.save(signTemplateLogEntity);
        return signTemplateLogEntity;
    }

    public ContractSignTemplateLogEntity getTemplateLog4(Integer logId) {
        return this.getOne(Wrappers.<ContractSignTemplateLogEntity>lambdaQuery()
                .eq(ContractSignTemplateLogEntity::getId, logId)
                .eq(ContractSignTemplateLogEntity::getType, 4));

    }
}
