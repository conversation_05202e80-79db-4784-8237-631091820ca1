package com.navigator.trade.service.contractsign.impl.purchase;

import com.navigator.trade.service.contractsign.impl.BaseAbstractContractSignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022-02-18 11:37
 */
@Slf4j
@Component("SBM_P_SIGN_REVERSE_PRICE,SBO_P_SIGN_REVERSE_PRICE")
public class SBMPurchaseSignRevisePriceService extends BaseAbstractContractSignService {
}
