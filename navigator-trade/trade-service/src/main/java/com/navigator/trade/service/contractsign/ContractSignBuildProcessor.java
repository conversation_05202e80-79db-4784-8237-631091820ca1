package com.navigator.trade.service.contractsign;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.CustomerProtocolTypeEnum;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.*;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.TemplateConditionDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.navigator.common.constant.BizConstant.DEFAULT_PAY_DAYS;
import static com.navigator.common.constant.ContractSignConstant.*;

@Service
@Slf4j
public class ContractSignBuildProcessor {
    @Autowired
    protected AttributeFacade attributeFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected CustomerProtocolFacade customerProtocolFacade;
    @Autowired
    protected CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    private ContractSignDao contractSignDao;
    @Resource
    IContractQueryService contractQueryService;
    @Resource
    ContractQueryLogicService contractQueryLogicService;
    @Resource
    ITradeTicketQueryService ttQueryService;
    @Resource
    private IDeliveryTypeService deliveryTypeService;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Resource
    private AzureBlobUtil azureBlobUtil;
    @Resource
    private ITtPriceService ttPriceService;

    @Value("${contract.sign.qrCodeUrl}")
    private String qrCodeUrl;

    @Deprecated
    public ContractSignTemplateDTO getContractSignTemplateDTO(Integer signId) {
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();

        //协议信息
        ContractSignEntity contractSignEntity = contractSignDao.getById(signId);
        contractSignTemplateDTO.setContractSignDTO(contractSignEntity);

        //合同信息
        int contractId = contractSignEntity.getContractId();
        ContractDetailInfoDTO contractDetailInfoDTO = contractQueryLogicService.getContractDetailInfoDTO(contractId);
        contractSignTemplateDTO.setContractDetailInfoDTO(contractDetailInfoDTO);

        //TT信息
        Integer ttid = contractSignEntity.getTtId();
        TradeTicketDTO tradeTicketDTO = ttQueryService.getTTDetailInfo(ttid);
        contractSignTemplateDTO.setTradeTicketDTO(tradeTicketDTO);

        Integer srcContractId = tradeTicketDTO.getSourceContractId();
        if (null != srcContractId && srcContractId > 0) {
            ContractDetailInfoDTO srcContractDetailInfoDTO = contractQueryLogicService.getContractDetailInfoDTO(srcContractId);
            contractSignTemplateDTO.setSourceContractDetailInfoDTO(srcContractDetailInfoDTO);
        }
        return contractSignTemplateDTO;
    }

    public SignTemplateDTO buildSignTemplateDTO(ContractSignTemplateDTO contractSignTemplateDTO) {
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();

        log.info("协议出具的合同基本信息===============" + contractSignTemplateDTO.getContractCode() + FastJsonUtils.getBeanToJson(contractSignTemplateDTO));

        ContractSignEntity contractSignDTO = contractSignTemplateDTO.getContractSignDTO();

        ContractDetailInfoDTO contractDetailInfoDTO = contractSignTemplateDTO.getContractDetailInfoDTO();
        ContractDetailInfoDTO sourceContractDetailInfoDTO = contractSignTemplateDTO.getSourceContractDetailInfoDTO();

        TradeTicketDTO tradeTicketDTO = contractSignTemplateDTO.getTradeTicketDTO();

        ContractAddTTDTO addTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractAddTTDTO();
        ContractStructurePriceAddDTO structurePriceAddDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractStructurePriceAddDTO();
        TTPriceEntity ttPriceEntity = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTTPriceDTO();
        ContractTransferTTDTO transferTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTransferTTDTO();
        ContractModifyTTDTO modifyTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractModifyTTDTO();


        BigDecimal contractUnitPrice = null == contractDetailInfoDTO ? BigDecimal.ZERO : contractDetailInfoDTO.getUnitPrice();

        //吨数、合同量、已开单量、未开单量、已定价量、未定价量、合同剩余数量
        BigDecimal contractRemainNum = BigDecimal.ZERO;

        String modifyContent = "";

        String contentInfo = "";

        TemplateConditionDTO templateCondition = new TemplateConditionDTO();
        templateCondition
                .setSalesType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getSalesType())
                .setSpecId(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getGoodsSpecId())
                .setModifyList(new ArrayList<>())
                .setContractType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getContractType())
                .setDeliveryType(null == contractDetailInfoDTO ? null : getDeliveryType(contractDetailInfoDTO.getDeliveryType()))
                .setPaymentType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getPaymentType())
                .setDeliveryFactoryCode(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getDeliveryFactoryCode())
                .setPriceEndType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getPriceEndType())
                .setDepositReleaseType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getDepositReleaseType())
                .setDepositAmount(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getDepositRate())
                .setAddedDepositRate((null == contractDetailInfoDTO || null == contractDetailInfoDTO.getAddedDepositRate()) ? 0 : contractDetailInfoDTO.getAddedDepositRate())
                .setInvoicePaymentRate((null == contractDetailInfoDTO || null == contractDetailInfoDTO.getInvoicePaymentRate()) ? 0 : contractDetailInfoDTO.getInvoicePaymentRate())
                .setActionType(null == tradeTicketDTO ? null : tradeTicketDTO.getContractSource())
                //定价未定价量
                //.setNotPriceNum((TTTypeEnum.PRICE.getType() == tradeTicketDTO.getType() || TTTypeEnum.FIXED.getType() == tradeTicketDTO.getType()) && null != ttPriceEntity.getRemainPriceNum() ? BigDecimal.ZERO : ttPriceEntity.getRemainPriceNum())
                //全部反点价：尾量终止
                .setGoodsCategoryId(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getGoodsCategoryId())
                .setSignType((null == tradeTicketDTO || TTTypeEnum.REVERSE_PRICE.getType().equals(tradeTicketDTO.getType())) ? 1 : contractSignDTO.getSignType())
                .setStructureType(null == structurePriceAddDTO ? 1 : structurePriceAddDTO.getStructureType())
                .setProtocolTypeCondition(null == contractSignDTO ? "" : ProtocolTypeEnum.getByCode(contractSignDTO.getFrameProtocolType()).getValue())
                .setEnterpriseCode("");
        if (ContractSalesTypeEnum.PURCHASE.getValue() == templateCondition.getSalesType()) {
            templateCondition.setProtocolTypeCondition(null == contractSignDTO ? "" : ProtocolTypeEnum.CONTRACT.getValue());
        }
        if (contractSignDTO != null && StringUtils.isNotBlank(contractSignDTO.getDeliveryFactoryCode())) {
            templateCondition.setDeliveryFactoryCode(contractSignDTO.getDeliveryFactoryCode());
        }
        Integer lytext = 150;
        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(contractDetailInfoDTO.getGoodsCategoryId()) && ContractSalesTypeEnum.SALES.getValue() == contractDetailInfoDTO.getSalesType()
                && ContractTypeEnum.JI_CHA.getValue() == contractDetailInfoDTO.getContractType()) {
            lytext = 300;
        }
        String depositUseRuleName = "";
        //保证金使用规则描述（option1/option2） 区分采销，保证金释放方式
        if (DepositUseRuleEnum.RATIO.getValue() == contractDetailInfoDTO.getDepositReleaseType()) {
            depositUseRuleName = ContractSalesTypeEnum.SALES.getValue() == contractDetailInfoDTO.getSalesType() ?
                    TemplateConstant.DePOSIT_USE_RULE_RATIO_OPTION2_SALE : TemplateConstant.DePOSIT_USE_RULE_RATIO_OPTION2_PURCHASE;
        } else {
            depositUseRuleName = TemplateConstant.DePOSIT_USE_RULE_LAST_OPTION1;
        }
        signTemplateDTO.setLytext(lytext)
                .setDepositUseRuleName(depositUseRuleName)
                .setTemplateCondition(templateCondition);
        if (null == contractDetailInfoDTO)
            throw new NullPointerException();
        buildContractInfo(signTemplateDTO, contractDetailInfoDTO);

        buildContractSignInfo(signTemplateDTO, contractSignDTO);

//        if(null == sourceContractDetailInfoDTO)
//            throw new NullPointerException();
        buildSourceContractInfo(signTemplateDTO, sourceContractDetailInfoDTO);

        //TT修改时间
        if (null != tradeTicketDTO) {
            signTemplateDTO.setJzlyfk(DateTimeUtil.formatDateStringCN(DateTimeUtil.addDays(tradeTicketDTO.getCreatedAt(), 1, false)));
            signTemplateDTO.setTtxr(DateTimeUtil.formatDateStringCN(tradeTicketDTO.getCreatedAt()));
        }

        //达孚的客户
        Integer customerId = contractDetailInfoDTO.getCustomerId();
        //达孚自己
        Integer ldcSelfCustomerId = contractDetailInfoDTO.getSupplierId();
        if (contractDetailInfoDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            ldcSelfCustomerId = contractDetailInfoDTO.getCustomerId();
            customerId = contractDetailInfoDTO.getSupplierId();
        }

        //=========================customer处理区==============================
        buildCustomerInfo(signTemplateDTO, customerId, contractDetailInfoDTO.getGoodsCategoryId(), contractDetailInfoDTO.getSalesType(), contractDetailInfoDTO.getCompanyId(), contractDetailInfoDTO.getCategory2(), contractDetailInfoDTO.getCategory3());
        //=========================LDC自身客户信息处理区==============================
        buildLdcSelfCutomerInfo(signTemplateDTO, ldcSelfCustomerId, contractDetailInfoDTO.getGoodsCategoryId(), contractDetailInfoDTO.getSalesType(), contractDetailInfoDTO.getCompanyId(), contractDetailInfoDTO.getCategory2(), contractDetailInfoDTO.getCategory3());

        //=========================factory处理区==============================
        buildFactoryInfo(signTemplateDTO, contractDetailInfoDTO.getShipWarehouseId());

        //=========================附件处理add（新增、关闭）==============================
        if (null != addTTDTO) {
            buildTTAddInfo(signTemplateDTO, addTTDTO, tradeTicketDTO.getTradeType());
        }

        //=========================处理修改/拆分==============================
        if (null != modifyTTDTO) {
            modifyContent = modifyTTDTO.getModifyContent();
            contentInfo = modifyTTDTO.getContent();
            buildTTModifyInfo(signTemplateDTO, modifyTTDTO);
        }

        //=========================处理解约定赔==============================
        //解约定赔数量、解约定赔市场价格、解约定赔差价、解约定赔差价总额
        if (contractSignDTO.getTradeType() == ContractTradeTypeEnum.WASHOUT.getValue()) {
            ContractPriceDTO contractPriceDTO = contractDetailInfoDTO.getContractPriceDTO();
            BigDecimal forwardPrice = null == contractPriceDTO || contractPriceDTO.getForwardPrice() == null ? BigDecimal.ZERO : contractPriceDTO.getForwardPrice();
            BigDecimal contractPrice = sourceContractDetailInfoDTO == null ? BigDecimal.ZERO : (sourceContractDetailInfoDTO.getUnitPrice() == null ? BigDecimal.ZERO : sourceContractDetailInfoDTO.getUnitPrice());
            if ((ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == templateCondition.getContractType() && BigDecimal.ZERO.compareTo(contractDetailInfoDTO.getTotalPriceNum()) == 0)
                    || ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
                if (null != contractDetailInfoDTO) {
                    if (null != contractDetailInfoDTO.getUnitPrice()) {
                        contractPrice = contractDetailInfoDTO.getUnitPrice().subtract(forwardPrice);
                    } else {
                        contractPrice = BigDecimal.ZERO;
                    }
                } else {
                    contractPrice = BigDecimal.ZERO;
                }
//                if (null != sourceContractDetailInfoDTO) {
//                    if (null != sourceContractDetailInfoDTO.getUnitPrice()) {
//                        contractPrice = sourceContractDetailInfoDTO.getUnitPrice().subtract(forwardPrice);
//                    } else {
//                        contractPrice = BigDecimal.ZERO;
//                    }
//                } else {
//                    contractPrice = BigDecimal.ZERO;
//                }
            }
            buildWashoutInfo(signTemplateDTO, addTTDTO, contractPrice, templateCondition, contractDetailInfoDTO);
        }

        if (contractSignDTO.getTradeType() == ContractTradeTypeEnum.WASHOUT.getValue()
                || contractSignDTO.getTradeType() == ContractTradeTypeEnum.CLOSED.getValue()
                || contractSignDTO.getTradeType() == ContractTradeTypeEnum.INVALID.getValue()
        ) {
            if (addTTDTO != null) {
                signTemplateDTO.setCfsl(BigDecimalUtil.formatBigDecimalZero(addTTDTO.getSourceContractNum(), 3, RoundingMode.HALF_UP));
            }
        }


        //=========================处理点价==============================
        if (null != ttPriceEntity) {
            //定价未定价量
            templateCondition.setNotPriceNum(Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()).contains(tradeTicketDTO.getType()) && null != ttPriceEntity ? ttPriceEntity.getRemainPriceNum() : BigDecimal.ZERO);
            //未定价量
            signTemplateDTO.setWdjl(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getRemainPriceNum(), 3, RoundingMode.HALF_UP));
            buildPriceInfo(signTemplateDTO, ttPriceEntity, contractSignTemplateDTO);
        }


        //=========================处理结构化定价==============================
        //结构化定价相关
        buildSturctureInfo(signTemplateDTO, structurePriceAddDTO);

        //=========================处理转月,反点价==============================
        if (null != transferTTDTO) {
            signTemplateDTO.setSxf(BigDecimalUtil.formatBigDecimalZero(contractSignDTO.getThisTimeFee(), 0, RoundingMode.HALF_UP));
            modifyContent = transferTTDTO.getModifyContent();
            contentInfo = transferTTDTO.getModifyContent();
            buildTransferInfo(signTemplateDTO, transferTTDTO);
        }

        //重新build模板条件中的修改列表
        if (StringUtils.isNotBlank(modifyContent)) {
            templateCondition = buildModifyList(signTemplateDTO, templateCondition, modifyContent, null == tradeTicketDTO ? null : tradeTicketDTO.getType());
        }
        if (StringUtils.isNotBlank(contentInfo)) {
            buildContentInfo(signTemplateDTO, templateCondition, contentInfo, null == tradeTicketDTO ? null : tradeTicketDTO.getType());
            //含税总金额
            signTemplateDTO.setTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(signTemplateDTO.getTotalAmountModify(), 2, RoundingMode.HALF_UP));
            //不含税总金额  todo
            signTemplateDTO.setNoTaxTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(signTemplateDTO.getTotalAmountModify().divide(signTemplateDTO.getTaxRateModify().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP));
            //增值税总金额
            signTemplateDTO.setAddedTaxTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(signTemplateDTO.getTotalAmountInfoModify()).subtract(new BigDecimal(signTemplateDTO.getNoTaxTotalAmountInfoModify())), 2, RoundingMode.HALF_UP));

        }
        String aboveDeadlineInfo = "";
        //以上期限条款文本(交货截止日期 < 合同签约日期）
        if (contractDetailInfoDTO.getDeliveryEndTime().before(contractDetailInfoDTO.getSignDate())) {
            aboveDeadlineInfo = TemplateConstant.ABOVE_DEADLINE_INFO;
        }
        String priceDeadlineText = ContractPriceEndTypeEnum.DATE.getValue() == templateCondition.getPriceEndType() ?
                TemplateConstant.PRICE_DEADLINE_DATE : TemplateConstant.PRICE_DEADLINE_TEXT;
        priceDeadlineText = priceDeadlineText.replace("#priceEndTime#", signTemplateDTO.getDjj());
        Integer invoicePaymentRate = templateCondition.getInvoicePaymentRate();

        //正大“交货地点”特殊条款触发逻辑：客户=正大集团（配置的3个） 并且  有特殊备注  并且 交货工厂=TJ,
        // TT的备注信息中，包含被“@#￥”括起来的信息，信息内容不做判断
        Integer isZDSpecialDeliveryCustomer = 0;
        List<String> deliveryMemoList = new ArrayList<>();
        if ("TJ".equals(contractDetailInfoDTO.getDeliveryFactoryCode()) && "ZDJT".equals(templateCondition.getEnterpriseCode())) {
            String memo = contractDetailInfoDTO.getMemo();
            String specialZdSymbol = "@#￥";
            if (memo.contains(specialZdSymbol)) {
                String secondMemo = memo.replaceFirst(specialZdSymbol, "");
                if (secondMemo.contains(specialZdSymbol)) {
                    isZDSpecialDeliveryCustomer = 1;
                    deliveryMemoList = renderZDSpecialDeliveryList(memo);
                }
            }
        }
        templateCondition.setIsZDSpecialDeliveryCustomer(isZDSpecialDeliveryCustomer);

        signTemplateDTO.setAboveDeadlineInfo(aboveDeadlineInfo)
                .setPriceDeadlineText(priceDeadlineText)
                .setInvoicePaymentRateInfo(invoicePaymentRate + "%")
                .setInvoicePaymentRateInTurnInfo((100 - invoicePaymentRate) + "%")
                .setSpecialCustomerDeliveryInfo(CollectionUtils.isEmpty(deliveryMemoList) ? "" : deliveryMemoList.get(0))
                .setTemplateCondition(templateCondition);
        return signTemplateDTO;
    }

    public void buildContentInfo(SignTemplateDTO signTemplateDTO, TemplateConditionDTO templateConditionDTO, String contentInfo, Integer ttType) {
        try {
            List<CompareObjectDTO> contentObjectDTOList = JSON.parseArray(contentInfo, CompareObjectDTO.class);
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal afterTotalAmount = BigDecimal.ZERO;
            for (CompareObjectDTO i : contentObjectDTOList) {
                if (("taxRate").equals(i.getName())) {
                    BigDecimal afterModifyTaxRate = new BigDecimal(i.getAfter()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    signTemplateDTO.setTaxRateModify(afterModifyTaxRate);
                }
                if (("taxRate").equals(i.getName())) {
                    BigDecimal afterModifyTaxRate = new BigDecimal(i.getAfter()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    signTemplateDTO.setTaxRateModify(afterModifyTaxRate);
                }
                if (("unitPrice").equals(i.getName())) {
                    unitPrice = new BigDecimal(i.getBefore()).setScale(3, BigDecimal.ROUND_HALF_UP);
                }
                if (("totalAmount").equals(i.getName())) {
                    afterTotalAmount = new BigDecimal(i.getAfter()).setScale(6, BigDecimal.ROUND_HALF_UP);
                    signTemplateDTO.setTotalAmountModify(afterTotalAmount);
                }
                if (("contractType").equals(i.getName())) {
                    templateConditionDTO.setOriginalContractType(Integer.valueOf(i.getBefore()));
                    if (!TTTypeEnum.SPLIT.getType().equals(ttType)) {
                        templateConditionDTO.setContractType(Integer.valueOf(i.getAfter()));
                    }
                }
            }
            //除了修改、拆分（出在原合同上），其他出在新合同上，取before
            if (!Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType()).contains(ttType)) {
                afterTotalAmount = unitPrice.multiply(new BigDecimal(signTemplateDTO.getSysl()).setScale(2, RoundingMode.HALF_UP)).setScale(6, RoundingMode.HALF_UP);
                signTemplateDTO.setTotalAmountModify(afterTotalAmount);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public TemplateConditionDTO buildModifyList(SignTemplateDTO signTemplateDTO, TemplateConditionDTO templateConditionDTO,
                                                String modifyContent, Integer ttType) {
        List<String> modifyList = new ArrayList<>();
        int transferFactory = TransferFactoryEnum.NOT_INVOLVE.getValue();
        int splitType = SplitTypeEnum.ALL_SPLIT.getValue();

        try {
            List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(modifyContent, CompareObjectDTO.class);

            for (CompareObjectDTO i : compareObjectDTOList) {
                modifyList.add(i.getName());
                if (("deliveryFactoryCode").equals(i.getName())) {
                    transferFactory = TransferFactoryEnum.INVOLVE.getValue();
                }
                //追加履约保证金 原合同字段为空 则不加入规则
                if (("addedDepositRate2").equals(i.getName()) && StringUtils.isBlank(i.getBefore())) {
                    modifyList.remove(i.getName());
                }
                if (("depositAmount").equals(i.getName())) {
                    modifyList.remove(i.getName());
                }
                //履约保证金比例的问题
                if (("depositRate").equals(i.getName())) {
                    modifyList.add("depositAmount");
                }
                if (("deliveryType").equals(i.getName())) {
                    modifyList.remove("deliveryType");
                    //提货方式发生变化
                    modifyList.add("deliveryTypeInfo");
                    DeliveryTypeEntity beforeDeliveryType = deliveryTypeService.getDeliveryTypeById(Integer.valueOf(i.getBefore()));
                    DeliveryTypeEntity afterDeliveryType = deliveryTypeService.getDeliveryTypeById(Integer.valueOf(i.getAfter()));
                    if (!beforeDeliveryType.getType().equals(afterDeliveryType.getType())) {
                        // 提货方式对应的类型发生变化
                        modifyList.add("deliveryType");
                        modifyList.add("deliveryMode");
                    }
                }
                if (ttType.equals(TTTypeEnum.SPLIT.getType())) {
                    if (("contractNum").equals(i.getName()) && !i.getAfter().equals(i.getBefore())) {
                        splitType = SplitTypeEnum.PART_SPLIT.getValue();
                    }
                }
                if (("taxRate").equals(i.getName())) {
                    BigDecimal afterModifyTaxRate;
                    if (TTTypeEnum.REVISE.getType().equals(ttType)) {
                        afterModifyTaxRate = new BigDecimal(i.getAfter()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else {
                        afterModifyTaxRate = new BigDecimal(i.getBefore()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    signTemplateDTO.setTaxRateModify(afterModifyTaxRate);
                }
                if (("totalAmount").equals(i.getName())) {
                    BigDecimal afterTotalAmount;
                    if (TTTypeEnum.REVISE.getType().equals(ttType)) {
                        afterTotalAmount = new BigDecimal(i.getAfter()).setScale(6, BigDecimal.ROUND_HALF_UP);
                    } else {
                        afterTotalAmount = new BigDecimal(i.getBefore()).subtract(new BigDecimal(i.getAfter())).setScale(6, BigDecimal.ROUND_HALF_UP);
                    }
                    signTemplateDTO.setTotalAmountModify(afterTotalAmount);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        templateConditionDTO.setModifyList(modifyList)
                .setTransferFactory(transferFactory)
                .setSplitType(ttType.equals(TTTypeEnum.TRANSFER.getType()) || ttType.equals(TTTypeEnum.REVERSE_PRICE.getType()) ? templateConditionDTO.getSplitType() : splitType);

        return templateConditionDTO;
    }

    private void buildContractSignInfo(SignTemplateDTO signTemplateDTO, ContractSignEntity contractSignDTO) {
        //获取二维码
        String qrCodeImage = "";
        String barCodeImage = "";
        String sasToken = "";

        qrCodeImage = StringUtils.isNotBlank(contractSignDTO.getQrCodeImage()) ? contractSignDTO.getQrCodeImage() :
                fileProcessFacade.generateQrCodeImg(qrCodeUrl + contractSignDTO.getId()).getFileUrl();
        //获取条形码
        barCodeImage = StringUtils.isNotBlank(contractSignDTO.getBarCodeImage()) ? contractSignDTO.getBarCodeImage() :
                fileProcessFacade.generateBarCodeImg(contractSignDTO.getContractCode(), "").getFileUrl();
        sasToken = azureBlobUtil.getSharedAccessSignature();
        //todo:二维码+token
//        signTemplateDTO.setEwm(qrCodeImage);
//        signTemplateDTO.setTxm(barCodeImage);
        signTemplateDTO.setEwm(qrCodeImage + sasToken);
        signTemplateDTO.setTxm(barCodeImage + sasToken);
        signTemplateDTO.setXyb(contractSignDTO.getProtocolCode());

        String salesType = ContractSalesTypeEnum.getDescByValue(contractSignDTO.getSalesType());
        String protocolType = CustomerProtocolTypeEnum.getShortDescByValue(contractSignDTO.getFrameProtocolType());
        signTemplateDTO.setXhd(salesType + protocolType);
        if (contractSignDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            signTemplateDTO.setChd(salesType + protocolType);
        } else {
            signTemplateDTO.setXhd(salesType + protocolType);
        }
    }

    private void buildContractInfo(SignTemplateDTO signTemplateDTO, ContractDetailInfoDTO contractDetailInfoDTO) {
        BigDecimal contractNum = contractDetailInfoDTO.getContractNum();
        BigDecimal billNum = BigDecimal.ZERO;
        BigDecimal unBillNum = BigDecimal.ZERO;
        BigDecimal pricedNum = BigDecimal.ZERO;
        BigDecimal unPricedNum = BigDecimal.ZERO;
        BigDecimal contractUnitPrice = contractDetailInfoDTO.getUnitPrice();
        BigDecimal totalBillNum = contractDetailInfoDTO.getTotalBillNum();
        BigDecimal totalDeliveryNum = contractDetailInfoDTO.getTotalDeliveryNum();
        BigDecimal contractRemainNum = contractDetailInfoDTO.getContractNum().subtract(totalBillNum);

        BigDecimal noFaxPrice = contractDetailInfoDTO.getCifUnitPrice();
        BigDecimal noFaxAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, noFaxPrice, contractNum);


        //合同基本信息========================================
        //合同编号
        signTemplateDTO.setNo(contractDetailInfoDTO.getContractCode());
        signTemplateDTO.setVrjc(GoodsCategoryEnum.getByValue(contractDetailInfoDTO.getGoodsCategoryId()).getLkgFutureSymbol());
        signTemplateDTO.setVrqc(GoodsCategoryEnum.getByValue(contractDetailInfoDTO.getGoodsCategoryId()).getCode());
        //合同类型
        signTemplateDTO.setTy(contractDetailInfoDTO.getContractTypeName());
        //签约日期
        signTemplateDTO.setDoc(DateTimeUtil.formatDateStringCN(contractDetailInfoDTO.getSignDate()));
        //吨数
        signTemplateDTO.setMt(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP));
        //.setTtxr("")  // TT修改日期 TODO

        //合同-基差信息========================================
        //期货合约
        signTemplateDTO.setHy(getDomainCodeCN(contractDetailInfoDTO.getDomainCode()));
        //期货合约
        signTemplateDTO.setHyj(contractDetailInfoDTO.getDomainCode());
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //基差价
            signTemplateDTO.setJcj(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getExtraPrice(), 2, RoundingMode.HALF_UP));
            if (contractDetailInfoDTO.getContractPriceDTO().getExtraPrice().compareTo(BigDecimal.ZERO) < 0) {
                signTemplateDTO.setJcj("(" + signTemplateDTO.getJcj() + ")");
            }
        }
        //点价截止日期
        signTemplateDTO.setDjj(getPriceEndTimeCN(contractDetailInfoDTO.getPriceEndType(), contractDetailInfoDTO.getPriceEndTime()))
                .setPriceEndTimeContains(getPriceEndTimeContains(contractDetailInfoDTO.getPriceEndType(), contractDetailInfoDTO.getPriceEndTime()));
        //点价后补缴比例
        signTemplateDTO.setDmr(getRateString(contractDetailInfoDTO.getAddedDepositRate()));

        //合同-量========================================
        //未开单量
        signTemplateDTO.setWkdl(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum().subtract(totalBillNum), 3, RoundingMode.HALF_UP));
        //未开单量igD
        signTemplateDTO.setXwkdl(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum().subtract(totalBillNum), 3, RoundingMode.HALF_UP));
        signTemplateDTO.setWthl(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum().subtract(totalDeliveryNum), 3, RoundingMode.HALF_UP));
        //合同-商品信息========================================
        //品种
        signTemplateDTO.setVr(contractDetailInfoDTO.getGoodsCategoryName());
        //蛋白（规格）
        signTemplateDTO.setEg(getGoodsSpecName(contractDetailInfoDTO.getGoodsSpecId()));
        //交货工厂
        signTemplateDTO.setJhgc(contractDetailInfoDTO.getDeliveryFactoryName());
        //溢短装
        signTemplateDTO.setOs(contractDetailInfoDTO.getWeightTolerance() + "%");
        //包装计算重量
        signTemplateDTO.setAg(getPackageWeightInfo(contractDetailInfoDTO.getPackageWeight(), contractDetailInfoDTO.getGoodsCategoryId()));
        //目的地
        signTemplateDTO.setPy(getDestinationInfo(contractDetailInfoDTO.getDestination(), contractDetailInfoDTO.getSalesType(), signTemplateDTO.getTemplateCondition()));
        signTemplateDTO.setPye(getDestinationForWeight(contractDetailInfoDTO.getDestination()));
        //重量验收标准
        signTemplateDTO.setPe(getWeightCheckInfo(contractDetailInfoDTO.getWeightCheck(), signTemplateDTO.getPye()));

        //合同-交提货========================================
        //交提货方式
        signTemplateDTO.setDg(DeliveryModeEnum.getDescByValue(getDeliveryType(contractDetailInfoDTO.getDeliveryType())));
        //交货周期
        signTemplateDTO.setPo(getDeliveryInfo(contractDetailInfoDTO.getDeliveryStartTime(), contractDetailInfoDTO.getDeliveryEndTime()));

        //合同-付款金额信息========================================
        //付款方式
        signTemplateDTO.setPm(contractDetailInfoDTO.getPaymentTypeName());
        //赊销账期
        signTemplateDTO.setMes(contractDetailInfoDTO.getCreditDays());
        //保证金比例
        signTemplateDTO.setMr(getRateString(contractDetailInfoDTO.getDepositRate()));
        //追加保证金比例
//        signTemplateDTO.setBzjzj(getRateString(getAddRateWhenDrop(contractDetailInfoDTO.getDepositRate(), contractDetailInfoDTO.getGoodsCategoryId())));
        signTemplateDTO.setBzjzj(getRateString(getAddRateByRule(contractDetailInfoDTO.getDepositRate(), contractDetailInfoDTO.getAddedDepositRate(), contractDetailInfoDTO.getGoodsCategoryId(), contractDetailInfoDTO.getContractType())));
        //付款截止日期
        signTemplateDTO.setJzfk(getDepositPayEndDayCN(contractDetailInfoDTO.getSignDate()));
        signTemplateDTO.setTaxRateModify(contractDetailInfoDTO.getTaxRate());
        signTemplateDTO.setTotalAmountModify(contractDetailInfoDTO.getTotalAmount());
        //含税单价
        signTemplateDTO.setPr(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
        //含税总金额
        signTemplateDTO.setPrt(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getTotalAmount(), 2, RoundingMode.HALF_UP));
        //不含税总金额  todo
        signTemplateDTO.setNprt(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getTotalAmount().divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP));
//        signTemplateDTO.setNprt(BigDecimalUtil.formatBigDecimalZero(noFaxAmount, 2, RoundingMode.HALF_UP));
        //增值税总金额
//        signTemplateDTO.setSz(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getTotalAmount().subtract(noFaxAmount), 2, RoundingMode.HALF_UP));
        signTemplateDTO.setSz(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(signTemplateDTO.getPrt()).subtract(new BigDecimal(signTemplateDTO.getNprt())), 2, RoundingMode.HALF_UP));
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //含税单价明细
            signTemplateDTO.setPrx(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType(), false));
            signTemplateDTO.setPrxy(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType(), true));
            //手续费
            signTemplateDTO.setSxf(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getFee(), 2, RoundingMode.HALF_UP));
            //含税单价中的运费
            signTemplateDTO.setYf(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP));
            //赊销利息
            signTemplateDTO.setSxlx(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getBusinessPrice(), 2, RoundingMode.HALF_UP));
            //客诉折价
            signTemplateDTO.setKszj(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP));
            //客诉折价总金额
            signTemplateDTO.setKszjz(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice()), 2, RoundingMode.HALF_UP));
            //滞期费
            signTemplateDTO.setZqf(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice(), 2, RoundingMode.HALF_UP));
            //滞期费总金额
            signTemplateDTO.setZqfz(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice()), 2, RoundingMode.HALF_UP));
            //转月含税单价明细
            //signTemplateDTO.setPrxzy(getOtherPriceInfoZY(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType()));
            //合同-其他（固定值）========================================
        }
        //国标/企标
        String factoryCode = contractDetailInfoDTO.getDeliveryFactoryCode();
        signTemplateDTO.setGqbz(TemplateConstant.defaultDeliveryFactoryList.contains(factoryCode) ? "企标值" : "国标值");
        //投诉单号
        signTemplateDTO.setTsdh("");
        //投诉原因
        signTemplateDTO.setTsyy("");
        //赊销日期
        signTemplateDTO.setSxrq("");


    }

    private void buildSourceContractInfo(SignTemplateDTO signTemplateDTO, ContractDetailInfoDTO sourceContractDetailInfoDTO) {

        if (null != sourceContractDetailInfoDTO) {
            //父合同编号
            signTemplateDTO.setNoy(sourceContractDetailInfoDTO.getContractCode());
            //原合同签约日期
            signTemplateDTO.setYdoc(DateTimeUtil.formatDateStringCN(sourceContractDetailInfoDTO.getSignDate()));
            //原合同剩余数量,0, RoundingMode.HALF_UP)
            signTemplateDTO.setCfsl(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP));
            // 原合同剩余数量(合同总量-拆分数量)
            signTemplateDTO.setSysl(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP));
            //修改前含税单价
            signTemplateDTO.setXpr(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getUnitPrice(), 2, RoundingMode.HALF_UP));
            //原合同签约日期
            signTemplateDTO.setYag(getPackageWeightInfo(sourceContractDetailInfoDTO.getPackageWeight(), sourceContractDetailInfoDTO.getGoodsCategoryId()));
            //原合同交货工厂
            signTemplateDTO.setYjhgc(sourceContractDetailInfoDTO.getDeliveryFactoryName());
            //原合同溢短装
            signTemplateDTO.setYos(sourceContractDetailInfoDTO.getWeightTolerance() + "%");
            //原合同含税单价
            signTemplateDTO.setYpr(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getUnitPrice(), 2, RoundingMode.HALF_UP));
            if (null != sourceContractDetailInfoDTO.getContractPriceDTO()) {
                //原合同含税单价中的运费
                signTemplateDTO.setYyf(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    /**
     * 设置客户相关信息
     * 销售时：customerId取自于合同的customerId
     * 采购时：customerId取自于合同的SupplierId
     *
     * @param signTemplateDTO
     * @param customerId
     * @param goodsCategoryId
     * @param salesType
     * @param companyId
     */
    private void buildCustomerInfo(SignTemplateDTO signTemplateDTO, Integer customerId, Integer goodsCategoryId, Integer salesType, Integer companyId, Integer category2, Integer category3) {
//        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        String factoryCode = signTemplateDTO.getTemplateCondition().getDeliveryFactoryCode();
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(factoryCode);
        Integer factoryId = null != factoryEntity ? factoryEntity.getId() : null;
        CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(new CustomerAllMessageDTO().setCustomerId(customerId)
                .setSalesType(salesType)
                .setFactoryCode(factoryCode)
                .setCompanyId(companyId)
                .setFactoryId(factoryId)
                .setCategory2(String.valueOf(category2))
                .setCategory3(String.valueOf(category3))
        );
//        CustomerTemplateVO customerTemplateVO = customerFacade.queryTemplateContactFactoryByCustomerId(customerId, goodsCategoryId);
        log.info("===========>customer:{},goodsCategoryId:{}", customerId, goodsCategoryId);
        log.info("===========>customerTemplateVO:{}", JSON.toJSONString(customerDTO));
        ContactEntity contactDTO = null;

        if (CollectionUtils.isEmpty(customerDTO.getContactDTO())) {

            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_CONTACT, customerDTO.getName() + "-工厂" + factoryEntity.getShortName() + "-" + GoodsCategoryEnum.getDescByValue(goodsCategoryId) + ContractSalesTypeEnum.getDescByValue(salesType));

        }
        if (customerDTO != null) {
            try {
                signTemplateDTO.setNa(customerDTO.getName())
                        .setNna(customerDTO.getName())
                        .setSscd(customerDTO.getAddress());
                // 集团客户信息
                signTemplateDTO.getTemplateCondition().setEnterpriseCode(null == customerDTO.getTemplateVipCode() ? "" : customerDTO.getTemplateVipCode());
                List<String> allName = new ArrayList<>();
                List<String> allEmail = new ArrayList<>();
                List<String> allPhone = new ArrayList<>();
                List<String> allAddress = new ArrayList<>();
                for (ContactEntity contactEntity : customerDTO.getContactDTO()) {
                    String nameItem = contactEntity.getContactName();
                    if (null != nameItem && !nameItem.equals("")) {
                        String[] split = nameItem.split(";");
                        allName.addAll(Arrays.asList(split));
                    }
                    String emailItem = contactEntity.getEmail();
                    if (null != emailItem && !emailItem.equals("")) {
                        String[] split = emailItem.split(";");
                        allEmail.addAll(Arrays.asList(split));
                    }
                    String phoneItem = contactEntity.getContactPhone();
                    if (null != phoneItem && !phoneItem.equals("")) {
                        String[] split = phoneItem.split(";");
                        allPhone.addAll(Arrays.asList(split));
                    }
                    String addressItem = contactEntity.getAddress();
                    if (null != addressItem && !addressItem.equals("")) {
                        String[] split = addressItem.split(";");
                        allAddress.addAll(Arrays.asList(split));
                    }
                }
                String contactNames = allName.stream().distinct().collect(Collectors.joining(";"));
                String contactEmails = allEmail.stream().distinct().collect(Collectors.joining(";"));
                String contactPhones = allPhone.stream().distinct().collect(Collectors.joining(";"));
                String contactAddresses = allAddress.stream().distinct().collect(Collectors.joining(";"));

                signTemplateDTO.setAds(contactAddresses);
                signTemplateDTO.setFox(contactNames);
                signTemplateDTO.setEma(contactEmails);
                signTemplateDTO.setMbo(contactPhones);


                if (salesType == ContractSalesTypeEnum.PURCHASE.getValue()) {
                    List<CustomerBankDTO> bankDTOList = customerDTO.getCustomerBankDTOS();
                    if (CommonListUtil.notNullOrEmpty(bankDTOList)) {
                        String bankName = bankDTOList.stream().map(CustomerBankDTO::getBankName).distinct().collect(Collectors.joining(";"));
                        String bankAccountNo = bankDTOList.stream().map(CustomerBankDTO::getBankAccountNo).distinct().collect(Collectors.joining(";"));
                        String bankAccountName = bankDTOList.stream().map(CustomerBankDTO::getBankAccountName).distinct().collect(Collectors.joining(";"));

                        signTemplateDTO.setKhfk(bankName);
                        signTemplateDTO.setZhfk(bankAccountNo);
                        signTemplateDTO.setKhmc(bankAccountName);
                    } else {
                        signTemplateDTO.setKhfk(WHEN_ERROR_BANK_NAME);
                        signTemplateDTO.setZhfk(WHEN_ERROR_BANK_ACCOUNT_NO);
                        signTemplateDTO.setKhmc(WHEN_ERROR_BANK_ACCOUNT_NAME);
                    }
                }

                //设置客户的框架协议相关信息
                CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
                customerProtocolDTO.setCustomerId(customerId)
                        .setCategoryId(goodsCategoryId)
                        .setCompanyId(companyId)
                        .setSaleType(salesType)
                        .setCategory2(String.valueOf(category2))
                        .setCategory3(String.valueOf(category3))
                ;
                CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);

                if (null != customerProtocolEntity && null != customerProtocolEntity.getProtocolStartDate()) {
                    signTemplateDTO.setKjr(DateTimeUtil.formatDateStringCN(customerProtocolEntity.getProtocolStartDate()));
                    signTemplateDTO.setKjh(customerProtocolEntity.getProtocolNo());
                } else {
                    signTemplateDTO.setKjr(WHEN_ERROR_PROTOCOL_START_DATE);
                    signTemplateDTO.setKjh(WHEN_ERROR_PROTOCOL_NO);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void buildLdcSelfCutomerInfo(SignTemplateDTO signTemplateDTO, Integer ldcSelfCustomerId, Integer goodsCategoryId, Integer salesType, Integer companyId, Integer category2, Integer category3) {
        String factoryCode = signTemplateDTO.getTemplateCondition().getDeliveryFactoryCode();
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(factoryCode);
        Integer factoryId = null != factoryEntity ? factoryEntity.getId() : null;
        CustomerDTO ldcSelfCustomerDTO = customerFacade.queryCustomerAllMessage(new CustomerAllMessageDTO().setCustomerId(ldcSelfCustomerId)
                .setSalesType(salesType)
                .setFactoryCode(factoryCode)
                .setCompanyId(companyId)
                .setFactoryId(factoryId)
                .setCategory2(String.valueOf(category2))
                .setCategory3(String.valueOf(category3))
        );
//        CustomerDTO ldcSelfCustomerDTO = customerFacade.getCustomerById(ldcSelfCustomerId);
//        CustomerTemplateVO supplierTemplateVO = customerFacade.queryTemplateContactFactoryByCustomerId(ldcSelfCustomerId, goodsCategoryId);
        //CustomerTemplateVO.ContactFactory contactFactory = null != supplierTemplateVO ? supplierTemplateVO.getContactFactories().get(0) : null;
        ContactEntity contactDTO = null;

        if (CollectionUtils.isEmpty(ldcSelfCustomerDTO.getContactDTO())) {

            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_CONTACT, ldcSelfCustomerDTO.getName() + "-工厂" + factoryEntity.getShortName() + "-" + GoodsCategoryEnum.getDescByValue(goodsCategoryId) + ContractSalesTypeEnum.getDescByValue(salesType));

        }
        try {
            signTemplateDTO.setAd(ldcSelfCustomerDTO.getSignPlace());
            signTemplateDTO.setMe(ldcSelfCustomerDTO.getName())
                    .setBscd(ldcSelfCustomerDTO.getAddress());

            List<String> allName = new ArrayList<>();
            List<String> allEmail = new ArrayList<>();
            List<String> allPhone = new ArrayList<>();
            List<String> allAddress = new ArrayList<>();
            for (ContactEntity contactEntity : ldcSelfCustomerDTO.getContactDTO()) {
                String nameItem = contactEntity.getContactName();
                if (null != nameItem && !nameItem.equals("")) {
                    String[] split = nameItem.split(";");
                    allName.addAll(Arrays.asList(split));
                }
                String emailItem = contactEntity.getEmail();
                if (null != emailItem && !emailItem.equals("")) {
                    String[] split = emailItem.split(";");
                    allEmail.addAll(Arrays.asList(split));
                }
                String phoneItem = contactEntity.getContactPhone();
                if (null != phoneItem && !phoneItem.equals("")) {
                    String[] split = phoneItem.split(";");
                    allPhone.addAll(Arrays.asList(split));
                }
                String addressItem = contactEntity.getAddress();
                if (null != addressItem && !addressItem.equals("")) {
                    String[] split = addressItem.split(";");
                    allAddress.addAll(Arrays.asList(split));
                }
            }
            String contactNames = allName.stream().distinct().collect(Collectors.joining(";"));
            String contactEmails = allEmail.stream().distinct().collect(Collectors.joining(";"));
            String contactPhones = allPhone.stream().distinct().collect(Collectors.joining(";"));
            String contactAddresses = allAddress.stream().distinct().collect(Collectors.joining(";"));

            signTemplateDTO.setMads(contactAddresses);
            signTemplateDTO.setMfox(contactNames);
            signTemplateDTO.setMema(contactEmails);
            signTemplateDTO.setMmbo(contactPhones);
            List<CustomerBankDTO> bankDTOList = ldcSelfCustomerDTO.getCustomerBankDTOS();
            if (CommonListUtil.notNullOrEmpty(bankDTOList)) {
                CustomerBankDTO bankDTO = bankDTOList.get(0);

                String bankName = null == bankDTO || StringUtil.isEmpty(bankDTO.getBankName()) ? WHEN_ERROR_BANK_NAME : bankDTO.getBankName();
                String bankAccountNo = null == bankDTO || StringUtil.isEmpty(bankDTO.getBankAccountNo()) ? WHEN_ERROR_BANK_ACCOUNT_NO : bankDTO.getBankAccountNo();

                signTemplateDTO.setKh(bankName);
                signTemplateDTO.setZh(bankAccountNo);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private void buildFactoryInfo(SignTemplateDTO signTemplateDTO, Integer shipWarehouseId) {
        try {
            if (null != shipWarehouseId) {
                FactoryWarehouseEntity warehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(shipWarehouseId);
                signTemplateDTO.setDs(null != warehouseEntity ? warehouseEntity.getAddress() : WHEN_ERROR_DELIVERY_ADDRESS);
                signTemplateDTO.setDd(null != warehouseEntity ? warehouseEntity.getDeliveryPoint() : WHEN_ERROR_DELIVERY_POINT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void buildTTAddInfo(SignTemplateDTO signTemplateDTO, ContractAddTTDTO addTTDTO, Integer tradeType) {
        if (null != addTTDTO) {
            if (tradeType == ContractTradeTypeEnum.CLOSED.getValue()) {
                signTemplateDTO.setWkdl(BigDecimalUtil.formatBigDecimalZero(addTTDTO.getContractNum(), 3, RoundingMode.HALF_UP));
            }
        }
    }

    private void buildTTModifyInfo(SignTemplateDTO signTemplateDTO, ContractModifyTTDTO modifyTTDTO) {
        signTemplateDTO.setTtxr(DateTimeUtil.formatDateStringCN(modifyTTDTO.getCreatedAt()));
        log.info("modifyTTDTO.ttId:{}", modifyTTDTO.getTtId());
        TradeTicketEntity tradeTicketEntity = ttQueryService.getByTtId(modifyTTDTO.getTtId());
        TTModifyEntity newContractTT = null;
        TTModifyEntity originalContractTT = null;
        if (null != tradeTicketEntity
                && Arrays.asList(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.SPLIT_NORMAL.getValue()).contains(tradeTicketEntity.getTradeType())
                && tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())
        ) {
            newContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), modifyTTDTO.getId());
            originalContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), newContractTT.getId());
        } else {
            originalContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), modifyTTDTO.getId());
            newContractTT = modifyTTDTO;
        }
        log.info("newContractTT.ttId:{}", newContractTT.getTtId());
        TradeTicketEntity newTradeTicketEntity = ttQueryService.getByTtId(newContractTT.getTtId());

        if (null != tradeTicketEntity && null != newContractTT) {
            buildFactoryInfo(signTemplateDTO, newContractTT.getShipWarehouseId());
            signTemplateDTO.getTemplateCondition()
                    .setSalesType(tradeTicketEntity.getSalesType())
                    .setSpecId(newContractTT.getGoodsSpecId())
//                    .setContractType(newContractTT.getContractType())
                    .setDeliveryType(getDeliveryType(newContractTT.getDeliveryType()))
                    .setPaymentType(newContractTT.getPaymentType())
                    .setDeliveryFactoryCode(newContractTT.getDeliveryFactoryCode())
                    .setPriceEndType(newContractTT.getPriceEndType())
                    .setDepositReleaseType(newContractTT.getDepositReleaseType())
                    .setDepositAmount(newContractTT.getDepositRate())
                    .setAddedDepositRate(null == newContractTT.getAddedDepositRate() ? 0 : newContractTT.getAddedDepositRate())
                    .setActionType(tradeTicketEntity.getContractSource());
            if (!TTTypeEnum.SPLIT.getType().equals(modifyTTDTO.getType())) {
                // 非拆分
                signTemplateDTO.getTemplateCondition().setContractType(newContractTT.getContractType());
            }
            signTemplateDTO.setPr(newContractTT.getUnitPrice() == null ? "0" : BigDecimalUtil.formatBigDecimalZero(newContractTT.getUnitPrice(), 2, RoundingMode.HALF_UP))
                    .setHy(getDomainCodeCN(newContractTT.getDomainCode()))
                    .setHyj(newContractTT.getDomainCode());
        }
        if (null != originalContractTT) {
            //原合同未拆分前的数量
            signTemplateDTO.setCfsl(BigDecimalUtil.formatBigDecimalZero(originalContractTT.getContractNum(), 3, RoundingMode.HALF_UP));
            //原合同拆分后剩余量
            String contractNumAfter = BigDecimalUtil.formatBigDecimalZero(originalContractTT.getContractNum().subtract(newContractTT.getContractNum()), 3, RoundingMode.HALF_UP);
            signTemplateDTO.setSysl(contractNumAfter);
            //todo:hoanna-部分拆分（主体变化）
            signTemplateDTO.setNna(signTemplateDTO.getNa())
                    .setYhy(DateTimeUtil.formatDateDomainCodeCN(originalContractTT.getDomainCode()));
            if (Arrays.asList(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.SPLIT_NORMAL.getValue()).contains(originalContractTT.getTradeType())) {
                signTemplateDTO.setMt(BigDecimalUtil.formatBigDecimalZero(newContractTT.getContractNum(), 3, RoundingMode.HALF_UP));
                signTemplateDTO.setNo(newContractTT.getContractCode());
                signTemplateDTO.setNna(ContractSalesTypeEnum.SALES.getValue() == newTradeTicketEntity.getSalesType() ? newTradeTicketEntity.getCustomerName() : newTradeTicketEntity.getSupplierName());
            }
            //原合同溢短装比例
            signTemplateDTO.setYos(originalContractTT.getWeightTolerance() + "%");
            //银行账号
            if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                signTemplateDTO.setZh(newContractTT.getSupplierAccount());

                CustomerBankEntity customerBankEntity = customerBankFacade.queryBankByBankAccountNo(String.valueOf(newContractTT.getSupplierAccount()));
                if (customerBankEntity != null) {
                    //开户名称
//                signTemplateDTO.setMe(customerBankEntity.getBankAccountName());
                    //开户行
                    signTemplateDTO.setKh(customerBankEntity.getBankName());
                }
            }
        }
    }

    /**
     * 处理点价
     *
     * @param signTemplateDTO
     * @param ttPriceEntity
     * @return
     */
    private void buildPriceInfo(SignTemplateDTO signTemplateDTO, TTPriceEntity ttPriceEntity, ContractSignTemplateDTO contractSignTemplateDTO) {
        if (null != ttPriceEntity) {

            //原合同含税单价-原合同期货价格
            BigDecimal sourcePrice = BigDecimal.ZERO;
            ContractDetailInfoDTO contractDetailInfoDTO = contractSignTemplateDTO.getContractDetailInfoDTO();

            if (null != ttPriceEntity.getContractPriceDetail()) {
                ContractPriceEntity contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
                sourcePrice = ttPriceEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            }

            //合同数量
            signTemplateDTO.setCfsl(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getThisContractNum(), 3, RoundingMode.HALF_UP));

            //定价单TT数量
            signTemplateDTO.setHtdj(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getOriginalPriceNum(), 3, RoundingMode.HALF_UP));
            //点价日期
            signTemplateDTO.setDjs(DateTimeUtil.formatDateStringCN(ttPriceEntity.getCreatedAt()));
            //定价单价格
            signTemplateDTO.setDjjg(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getTransactionPrice(), 2, RoundingMode.HALF_UP));
            //加权平均价
            signTemplateDTO.setJqpj(BigDecimal.ZERO.setScale(2).toPlainString());//TODO

            BigDecimal source = ttPriceEntity.getTransactionPrice().add(sourcePrice);

            //定价价格
            BigDecimal djjg = ttPriceEntity.getTransactionPrice();

            //定价单含税总金额
            BigDecimal jqpj = source.multiply(ttPriceEntity.getOriginalPriceNum());
            signTemplateDTO.setDjprt(BigDecimalUtil.formatBigDecimalZero(jqpj, 2, RoundingMode.HALF_UP));

            //定价价格不含税总金额
            //BigDecimal jdbhs = (djjg.add(sourcePrice)).divide(BigDecimal.valueOf(1.09), 6, BigDecimal.ROUND_CEILING);

            //定价单不含税总金额
            //BigDecimal djnprt = jdbhs.multiply(ttPriceEntity.getNum());
            BigDecimal djnprt = jqpj.divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            signTemplateDTO.setDjnprt(BigDecimalUtil.formatBigDecimalZero(djnprt, 2, RoundingMode.HALF_UP));

            //定价单增值税总金额
            signTemplateDTO.setDjsz(BigDecimalUtil.formatBigDecimalZero(jqpj.subtract(djnprt), 2, RoundingMode.HALF_UP));

            //加权平均价
            //List<ConfirmPriceDTO> confirmPriceDTOList = ttPriceService.getContractPricingList(ttPriceEntity.getContractId());
            //BigDecimal lbjjg = this.ldjjg(confirmPriceDTOList);
            if (BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO)) {

                BigDecimal lbjjg = ttPriceEntity.getAvePrice();
                signTemplateDTO.setLdjjg(BigDecimalUtil.formatBigDecimalZero(lbjjg, 2, RoundingMode.HALF_UP));

                //加权平均含税价
                signTemplateDTO.setLdjjghs(BigDecimalUtil.formatBigDecimalZero(lbjjg.add(sourcePrice), 2, RoundingMode.HALF_UP));

                //加权含税总金额
                BigDecimal lprt = (lbjjg.add(sourcePrice)).multiply(ttPriceEntity.getThisContractNum());
                signTemplateDTO.setLprt(BigDecimalUtil.formatBigDecimalZero(lprt, 2, RoundingMode.HALF_UP));

                //加权不含税总金额
                //BigDecimal lnpr = (lbjjg.add(sourcePrice)).divide(BigDecimal.valueOf(1.09), 6, BigDecimal.ROUND_CEILING);
                //lnpr = lnpr.multiply(contractDetailInfoDTO.getContractNum());
                //todo:税率
                BigDecimal lnpr = lprt.divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
                signTemplateDTO.setLnprt(BigDecimalUtil.formatBigDecimalZero(lnpr, 2, RoundingMode.HALF_UP));

                //加权增值税总金额
                signTemplateDTO.setLsz(BigDecimalUtil.formatBigDecimalZero(lprt.subtract(lnpr), 2, RoundingMode.HALF_UP));
            }

        }
    }


    private BigDecimal ldjjg(List<ConfirmPriceDTO> confirmPriceDTOList) {

        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalNum = BigDecimal.ZERO;
        for (TTPriceEntity ttPriceEntity : confirmPriceDTOList) {
            totalPrice = totalPrice.add(ttPriceEntity.getPrice().setScale(2, RoundingMode.HALF_UP).multiply(ttPriceEntity.getNum()));
            totalNum = totalNum.add(ttPriceEntity.getNum());
        }
        // 加权平均价

        BigDecimal ldjjg = BigDecimalUtil.isGreaterThanZero(totalNum) ? BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum) : BigDecimal.ZERO;

        return ldjjg;
    }

    /**
     * 处理转月
     *
     * @param signTemplateDTO
     * @param transferTTDTO
     * @return
     */
    private void buildTransferInfo(SignTemplateDTO signTemplateDTO, ContractTransferTTDTO transferTTDTO) {
        if (null != transferTTDTO) {
            //期货合约
            signTemplateDTO.setHy(getDomainCodeCN(transferTTDTO.getDomainCode()));
            //期货合约
            signTemplateDTO.setHyj(transferTTDTO.getDomainCode());
            //转月数量
            signTemplateDTO.setZysl(BigDecimalUtil.formatBigDecimalZero(transferTTDTO.getNum(), 3, RoundingMode.HALF_UP));
            //原期货合约
            signTemplateDTO.setYhy(DateTimeUtil.formatDateDomainCodeCN(transferTTDTO.getOriginalDomainCode()));
            //转月价差
            signTemplateDTO.setZyjc(BigDecimalUtil.formatBigDecimalZero(transferTTDTO.getPrice(), 2, RoundingMode.HALF_UP));

            TradeTicketEntity tradeTicketEntity = ttQueryService.getByTtId(transferTTDTO.getTtId());
            TradeTicketEntity originalContractTT = ttQueryService.getByGroupId(tradeTicketEntity.getGroupId(), transferTTDTO.getTtId());
            if (null != originalContractTT) {


                signTemplateDTO.getTemplateCondition().setSplitType(ContractActionEnum.TRANSFER_CONFIRM.getActionValue() == tradeTicketEntity.getContractSource() || ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue() == tradeTicketEntity.getContractSource() ? SplitTypeEnum.PART_SPLIT.getValue() : SplitTypeEnum.ALL_SPLIT.getValue());

                TTTranferEntity originalTransferEntity = ttQueryService.getTransferById(originalContractTT.getId());
                ContractEntity contractEntity = contractQueryService.getContractById(originalContractTT.getContractId());
                //原合同未拆分前的数量
                signTemplateDTO.setCfsl(BigDecimalUtil.formatBigDecimalZero(originalTransferEntity.getNum(), 3, RoundingMode.HALF_UP));
                //原合同拆分后剩余量
                String contractNumAfter = BigDecimalUtil.formatBigDecimalZero(originalTransferEntity.getNum().subtract(transferTTDTO.getNum()), 3, RoundingMode.HALF_UP);
                signTemplateDTO.setSysl(contractNumAfter);
                //原合同溢短装比例
                signTemplateDTO.setYos(contractEntity.getWeightTolerance() + "%");
                //银行账号
//                signTemplateDTO.setMe(contractEntity.getSupplierAccount());
                if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                    CustomerBankEntity customerBankEntity = customerBankFacade.queryBankByBankAccountNo(String.valueOf(contractEntity.getSupplierAccount()));
                    if (customerBankEntity != null) {
                        //开户名称
//                    signTemplateDTO.setMe(customerBankEntity.getBankAccountName());
                        //开户行
                        signTemplateDTO.setKh(customerBankEntity.getBankName());
                    }
                }
            }
        }


    }

    /**
     * 处理解约定赔
     *
     * @param signTemplateDTO
     * @param addTTDTO
     * @param contractUnitPrice
     * @param templateCondition
     * @return
     */
    private void buildWashoutInfo(SignTemplateDTO signTemplateDTO, ContractAddTTDTO addTTDTO, BigDecimal contractUnitPrice, TemplateConditionDTO templateCondition, ContractDetailInfoDTO contractDetailInfoDTO) {
        if (null != addTTDTO) {
            BigDecimal washoutNum = addTTDTO.getContractNum();
            BigDecimal washoutPrice = addTTDTO.getWashoutUnitPrice().setScale(2, RoundingMode.HALF_UP);
            BigDecimal washoutDiffPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, contractUnitPrice, washoutPrice).setScale(2, RoundingMode.HALF_UP);
            templateCondition.setWashOutDiffUnitPrice(washoutDiffPrice)
                    .setWashOutDiffUnitPriceType(CompareUnitPriceEnum.getCompareUnitPrice(washoutDiffPrice, templateCondition.getSalesType()));
            washoutDiffPrice = washoutDiffPrice.abs();
            BigDecimal washoutDiffAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, washoutDiffPrice, washoutNum).setScale(2, RoundingMode.HALF_UP);
            //解约定赔数量
            signTemplateDTO.setXdsl(BigDecimalUtil.formatBigDecimalZero(washoutNum, 3, RoundingMode.HALF_UP));
            //解约定赔市场价格
            signTemplateDTO.setXdscj(BigDecimalUtil.formatBigDecimalZero(washoutPrice, 2, RoundingMode.HALF_UP));
            //解约定赔差价
            signTemplateDTO.setXdcj(BigDecimalUtil.formatBigDecimalZero(washoutDiffPrice, 2, RoundingMode.HALF_UP));
            //解约定赔差价总额
            signTemplateDTO.setXdze(BigDecimalUtil.formatBigDecimalZero(washoutDiffAmount, 2, RoundingMode.HALF_UP));
            signTemplateDTO.setPr(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
            String domainCode = "(" + GoodsCategoryEnum.getByValue(addTTDTO.getGoodsCategoryId()).getLkgFutureSymbol() + addTTDTO.getDomainCode() + ")";
//            if (ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
//                signTemplateDTO.setPr(domainCode + (contractUnitPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
//            }
            if ((ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == templateCondition.getContractType() && BigDecimal.ZERO.compareTo(contractDetailInfoDTO.getTotalPriceNum()) == 0)
                    || ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
                signTemplateDTO.setPr(domainCode + (contractUnitPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP))
                        .setXdscj(domainCode + (washoutPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + signTemplateDTO.getXdscj());
            }
            buildFactoryInfo(signTemplateDTO, addTTDTO.getShipWarehouseId());
        }


    }

    /**
     * 处理结构化定价
     *
     * @param signTemplateDTO
     * @param structurePriceAddDTO
     * @return
     */
    private void buildSturctureInfo(SignTemplateDTO signTemplateDTO, ContractStructurePriceAddDTO structurePriceAddDTO) {
        if (null != structurePriceAddDTO) {
            //结构化定价总数量
            signTemplateDTO.setJghzsl(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getTotalNum(), 3, RoundingMode.HALF_UP));
            //结构化定价开始日期
            signTemplateDTO.setJghkrq(DateTimeUtil.formatDateStringCN(structurePriceAddDTO.getStartTime()));
            //结构化定价结束日期
            signTemplateDTO.setJghjrq(DateTimeUtil.formatDateStringCN(structurePriceAddDTO.getEndTime()));
            //结构化定价敲出价格
//            signTemplateDTO.setJghqc(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getMaxPrice(), 2, RoundingMode.HALF_UP));
            //结构化定价增强价格
//            signTemplateDTO.setJghzq(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getMaxPrice(), 2, RoundingMode.HALF_UP));
            //结构化定价触发价格
            signTemplateDTO.setJghcf(structurePriceAddDTO.getTriggerPrice());
//            signTemplateDTO.setJghcf(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(structurePriceAddDTO.getTriggerPrice()), 3, RoundingMode.HALF_UP));
            //结构化定价累积价格
            signTemplateDTO.setJghlj(structurePriceAddDTO.getCumulativePrice());
//            signTemplateDTO.setJghlj(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(structurePriceAddDTO.getCumulativePrice()), 3, RoundingMode.HALF_UP));
            //结构化定价单位量
            signTemplateDTO.setJghdwl(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getUnitNum(), 3, RoundingMode.HALF_UP));
            // 结构化定价单位数量
            signTemplateDTO.setJghdwsl(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getStructureUnitNum(), 3, RoundingMode.HALF_UP));
            //结构化定价单位增量
            signTemplateDTO.setJghdwzl(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getUnitIncrement(), 3, RoundingMode.HALF_UP));
            //单吨现金返还金额
            signTemplateDTO.setJghddxjfh(structurePriceAddDTO.getCashReturn());
        }

    }

    private String getDeliveryInfo(Date d1, Date d2) {
        try {
            return DateTimeUtil.formatDateStringCN(d1, d2);
        } catch (Exception e) {
            return WHEN_ERROR_DELIVERY_DATE;
        }
    }

    private Date getDepositPayEndDay(Date signDate) {
        return DateTimeUtil.addDays(signDate, DEFAULT_PAY_DAYS, false);
    }

    private String getDepositPayEndDayCN(Date signDate) {
        try {
            Date depositPayEndDay = getDepositPayEndDay(signDate);
            return DateTimeUtil.formatDateStringCN(depositPayEndDay);
        } catch (Exception e) {
            return WHEN_ERROR_DEPOSIT_PAY_END_DATE;
        }
    }

    private String getDomainCodeCN(String domainCode) {
        if (StringUtil.isNotEmpty(domainCode)) {
            return DateTimeUtil.formatDateDomainCodeCN(domainCode);
        }
        return "";
    }

    private String getPriceEndTimeContains(Integer priceEndType, String priceEndTime) {
        String rtn = priceEndTime;
        try {
            if (priceEndType == ContractPriceEndTypeEnum.DATE.getValue()) {
                rtn = DateTimeUtil.formatDayStringCN(rtn) + "（含）";
            }
//            else if (StringUtils.isNotBlank(rtn)) {
//                rtn = rtn.substring(0, 2);
//            }
        } catch (Exception e) {
            log.info("点价截止日期格式化失败" + e.toString());
        }
        return rtn;
    }

    private String getPriceEndTimeCN(Integer priceEndType, String priceEndTime) {
        String rtn = priceEndTime;
        try {
            if (priceEndType == ContractPriceEndTypeEnum.DATE.getValue()) {
                rtn = DateTimeUtil.formatDayStringCN(rtn);
            }
//            else if (StringUtils.isNotBlank(rtn)) {
//                rtn = rtn.substring(0, 2);
//            }
        } catch (Exception e) {
            log.info("点价截止日期格式化失败" + e.toString());
        }
        return rtn;
    }

    public String getRateString(Integer rate) {
        if (null == rate) {
            return "0";
        }
        return 0 == rate ? "0" : rate + "%";
    }

    /**
     * 豆粕有更新
     * 履约保证金=除5、10、15、20外，显示5%
     * 履约保证金=5%，显示5%
     * 履约保证金=10%，显示5%
     * 履约保证金=15%，显示10%
     * 履约保证金=20%，显示10%
     * 豆油新增
     * 履约保证金=除5、10、15、20外，显示5%
     * 履约保证金=5%，显示5%
     * 履约保证金=10%，显示7%
     * 履约保证金=15%，显示10%
     * 履约保证金=20%，显示10%
     *
     * @param rate
     * @param categoryId
     * @return
     */
    public Integer getAddRateWhenDrop(Integer rate, Integer categoryId) {
        if (HIGH_DEPOSIT_RATE_10.equals(rate)) {
            rate = GoodsCategoryEnum.OSM_MEAL.getValue().equals(categoryId) ? HIGH_DEPOSIT_RATE_5 : HIGH_DEPOSIT_RATE_7;
        } else if (HIGH_DEPOSIT_RATE_15.equals(rate)) {
            rate = HIGH_DEPOSIT_RATE_10;
        } else if (HIGH_DEPOSIT_RATE_20.equals(rate)) {
            rate = HIGH_DEPOSIT_RATE_10;
        } else {
            rate = HIGH_DEPOSIT_RATE_5;
        }
        return rate;
    }

    /**
     * * 豆粕#BZJZJ#
     * 基差合同
     * * 履约保证金比例+履约保证金补缴比例≤10%，显示5%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 履约保证金比例≤10%，显示5%
     * * 履约保证金比例>10%，显示10%
     * * 豆油#BZJZJ#
     * 基差合同
     * * 履约保证金比例+履约保证金补缴比例≤5%，显示5%
     * * 5%<履约保证金比例+履约保证金补缴比例≤10%，显示7%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 履约保证金比例≤5%，显示5%
     * * 5%<履约保证金比例≤10%，显示7%
     * * 履约保证金比例>10%，显示10%
     *
     * @return
     */
    public Integer getAddRateByRule(Integer depositRate, Integer addedDepositRate, Integer categoryId, Integer contractType) {
        if (contractType.equals(ContractTypeEnum.JI_CHA.getValue())) {
            depositRate = depositRate + addedDepositRate;
        }
        if (categoryId.equals(GoodsCategoryEnum.OSM_MEAL.getValue())) {
            if (depositRate <= HIGH_DEPOSIT_RATE_10) {
                depositRate = HIGH_DEPOSIT_RATE_5;
            } else {
                depositRate = HIGH_DEPOSIT_RATE_10;
            }
        } else {
            if (depositRate <= HIGH_DEPOSIT_RATE_5) {
                depositRate = HIGH_DEPOSIT_RATE_5;
            } else if (depositRate <= HIGH_DEPOSIT_RATE_10) {
                depositRate = HIGH_DEPOSIT_RATE_7;
            } else {
                depositRate = HIGH_DEPOSIT_RATE_10;
            }
        }
        return depositRate;
    }

    private String getGoodsSpecName(Integer goodsSpecId) {
        AttributeValueEntity attributeValueEntity = attributeFacade.getAttributeValueById(goodsSpecId);
        String specName = null != attributeValueEntity ? attributeValueEntity.getName() : "";
        if (specName.contains("RZ")) {
            specName = specName.substring(0, specName.length() - 3);
        }
        return specName;
    }

    private String getWeightCheckInfo(String weightCheck, String destination) {
        try {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(weightCheck));
            String weightCheckInfo = null != systemRuleItemEntity ? systemRuleItemEntity.getRuleValue() : "";
            return StringUtils.isNotBlank(weightCheckInfo) ? weightCheckInfo.replace("#PYE#", destination) : weightCheckInfo;
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
    }

    private String getDestinationInfo(String destination, Integer salesType, TemplateConditionDTO templateCondition) {
        String destinationInfo = destination;
        try {
            //目的港
            if (StringUtils.isNumeric(destination)) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(destination));
                destinationInfo = systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : destination;
                templateCondition.setDestinationCode(systemRuleItemEntity != null ? systemRuleItemEntity.getLkgCode() : "");
                if (!DESTINATION_LKG_CODE_LIST.contains(systemRuleItemEntity == null ? "" : systemRuleItemEntity.getLkgCode())) {
                    String jointText = ContractSalesTypeEnum.PURCHASE.getValue() == salesType ? "（下称“交货地点”)" : "（“指定地点”）";
                    destinationInfo = "送到：" + destinationInfo + jointText;
                }
            } else {
                destinationInfo = destination;
            }
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
        return destinationInfo;
    }

    private String getDestinationForWeight(String destination) {
        String destinationInfo = destination;
        try {
            //目的港
            if (StringUtils.isNumeric(destination)) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(destination));
                destinationInfo = systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : destination;
                if (DESTINATION_LKG_CODE_LIST.contains(systemRuleItemEntity == null ? "" : systemRuleItemEntity.getLkgCode())) {
                    destinationInfo = "目的港";
                }
            } else {
                destinationInfo = destination;
            }
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
        return destinationInfo;
    }

    private String getPackageWeightInfo(String packageWeight, Integer goodsCategoryId) {
        try {
            String packageWeightInfo = packageWeight;
            if (GoodsCategoryEnum.OSM_OIL.getValue().equals(goodsCategoryId)) {
                return WHEN_OIL_PACKAGE_WEIGHT;
            }
            //袋皮扣重
            if (StringUtils.isNumeric(packageWeight)) {
                SystemRuleItemEntity packageWeightItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(packageWeight));
                packageWeightInfo = packageWeightItemEntity != null ? packageWeightItemEntity.getMemo() : packageWeight;
            }
            return packageWeightInfo;
        } catch (NumberFormatException e) {
            return WHEN_ERROR_PACKAGE_WEIGHT;
        }
    }

    /**
     * 仅仅
     *
     * @param contractPriceEntity
     * @param contractType
     * @param containExtraPrice
     * @return
     */
    public String getOtherPriceInfo(ContractPriceEntity contractPriceEntity, Integer contractType, boolean containExtraPrice) {
        String preInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "+" : "";
        String stuffInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "" : ",";
        //当containExtraPrice=true,#PRXY#仅基差暂定价使用，基差自行拼接#基差基准#+#PRX#
        String extraPriceInfo = containExtraPrice ? BigDecimalUtil.formatDiffPriceCN("基准基差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getExtraPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) : "";
        String otherPrice = extraPriceInfo + BigDecimalUtil.formatPriceCN("出厂价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("蛋白价格", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getProteinDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("散粕补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getCompensationPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("期权费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOptionPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("运费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransportPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("起吊费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getLiftingPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("滞期费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getDelayPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("高温费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTemperaturePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("其他物流费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherDeliveryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("和解款折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBuyBackPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("客诉折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("转厂补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransferFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("其他补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("商务补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBusinessPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("手续费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFee(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("装运费单价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getShippingFeePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("精炼价差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getRefineDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo);
        if (StringUtil.isNotEmpty(otherPrice) && ContractTypeEnum.JI_CHA.getValue() != contractType) {
            otherPrice = "；单价包含" + otherPrice;
        }
        return StringUtils.isNotBlank(otherPrice) && !ContractTypeEnum.getBasicList().contains(contractType) ? otherPrice.substring(0, otherPrice.length() - 1) : otherPrice;
    }

    public String getOtherPriceInfoZY(ContractPriceEntity contractPriceEntity, Integer contractType) {
        String preInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "+" : "";
        String stuffInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "" : ",";
        String otherPrice = BigDecimalUtil.formatPriceZY("出厂价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("蛋白价格", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getProteinDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("散粕补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getCompensationPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("期权费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOptionPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("运费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransportPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("起吊费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getLiftingPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("滞期费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getDelayPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("高温费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTemperaturePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("其他物流费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherDeliveryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("和解款折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBuyBackPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("客诉折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("转厂补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransferFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("其他补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("商务补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBusinessPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("装运费单价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getShippingFeePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("精炼价差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getRefineDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo);
        if (StringUtil.isNotEmpty(otherPrice) && ContractTypeEnum.getBasicList().contains(contractType)) {
            otherPrice = "；单价包含" + otherPrice;
        }
        return StringUtils.isNotBlank(otherPrice) && !ContractTypeEnum.getBasicList().contains(contractType) ? otherPrice.substring(0, otherPrice.length() - 1) : otherPrice;
    }

    private Integer getDeliveryType(Integer deliveryType) {
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeService.getDeliveryTypeById(deliveryType);
        if (null == deliveryTypeEntity) {
            return DeliveryModeEnum.TAKE.getValue();
        }
        return deliveryTypeEntity.getType();
    }

    private static List<String> renderZDSpecialDeliveryList(String content) {
        // ${!}
//        String regex1 = "\\$\\{([^}]*)!\\}";
        String a = "@#￥";
        String regex = "@#￥([^}]*)@#￥";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> keyList = new ArrayList<>();
        while (matcher.find()) {
            keyList.add(matcher.group().replace(a, ""));
        }
        return keyList;
    }

}
