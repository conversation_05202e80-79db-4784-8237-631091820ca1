package com.navigator.trade.service.contract.purchase;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.Impl.BaseContractCreateAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 采购合同创建的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_P_CONTRACT_ADD,SBO_P_CONTRACT_ADD")
public class PurchaseContractCreateService extends BaseContractCreateAbstractService {
    @Resource
    public IContractOperationNewService purchaseContractOperationService;

    @Override
    protected void createAdditionalInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        return;
    }

    @Override
    protected void afterCreateProcess(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 记录日志
        // 操作名称
        LogBizCodeEnum bizCodeEnum;

        if (contractCreateDTO.getActionSource().equals(ContractActionEnum.NEW.getActionValue())) {
            bizCodeEnum = LogBizCodeEnum.NEW_PURCHASE_CONTRACT;
        } else if (contractCreateDTO.getActionSource().equals(ContractActionEnum.BUYBACK.getActionValue())) {
            bizCodeEnum = LogBizCodeEnum.SALES_CONTRACT_BUYBACK;
        } else {
            bizCodeEnum = LogBizCodeEnum.CHANGE_PURCHASE_CONTRACT;
        }

        String jsonData = JSONUtil.toJsonStr(contractEntity);

        // 记录操作记录
        purchaseContractOperationService.addContractOperationLog(contractEntity, bizCodeEnum, jsonData, SystemEnum.MAGELLAN.getValue());
    }
}
