package com.navigator.trade.service.impl;

import com.navigator.trade.dao.TtTranferDao;
import com.navigator.trade.pojo.entity.TTTranferEntity;
import com.navigator.trade.service.ITtTranferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * TT-转月、反点价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-21
 */
@Service
public class TtTranferServiceImpl implements ITtTranferService {

    @Autowired
    private TtTranferDao ttTranferDao;

    @Override
    public boolean saveTtTranfer(TTTranferEntity ttTranferEntity) {
        return ttTranferDao.save(ttTranferEntity);
    }

    @Override
    public TTTranferEntity getTransferByTtId(Integer ttId) {
        return ttTranferDao.getTTTransferEntityByTTId(ttId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId) {
        return ttTranferDao.getTTTranferByPriceApplyId(priceApplyId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByContractId(Integer contractId, Integer Id) {
        return ttTranferDao.getTTTranferByContractId(contractId, Id);
    }

    @Override
    public Boolean updateTTTranferById(TTTranferEntity ttTranferEntity) {
        return ttTranferDao.updateById(ttTranferEntity);
    }

    @Override
    public TTTranferEntity selectTTTranferByTTId(Integer TTId) {
        return ttTranferDao.getTTTransferEntityByTTId(TTId);
    }

    @Override
    public List<TTTranferEntity> selectTTTranferByPriceAllocateId(Integer priceAllocateId) {
        return ttTranferDao.selectTTTranferByPriceAllocateId(priceAllocateId);
    }
}
