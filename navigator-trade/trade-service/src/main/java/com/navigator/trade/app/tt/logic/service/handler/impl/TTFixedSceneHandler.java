package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum;
import com.navigator.trade.pojo.enums.PriceCompleteEnum;
import com.navigator.trade.pojo.enums.UsageEnum;
import com.navigator.trade.pojo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("FIXED_HANDLER")
public class TTFixedSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {

    }


    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return false;
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        return null;
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTPriceEntity ttPriceEntity = (TTPriceEntity) tradeTicketDO.getTtSubEntity();
//        TTPriceEntity ttPriceEntity = ttPriceDao.getTTPriceEntityByTTId(ttId);

        TTDetailVO ttDetailVO = new TTDetailVO();
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();

        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());

        //ContractSignEntity contractSignEntity = contractSignQueryService.getContractSignDetailById(tradeTicketEntity.getSignId());

        //计算定价单单价
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        BigDecimal sourcePrice = BigDecimal.ZERO;
        BigDecimal source = BigDecimal.ZERO;

        ttDetailVO.setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setTtId(tradeTicketEntity.getId())
                .setTtCode(tradeTicketEntity.getCode())
                .setPriceApplyTime(tradeTicketEntity.getCreatedAt())
                .setContractSignId(tradeTicketEntity.getSignId())
                .setContractSignCode(tradeTicketEntity.getProtocolCode());

        if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() && TTTypeEnum.PRICE.getType().equals(tradeTicketEntity.getType())) {
            PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(String.valueOf(ttPriceEntity.getAllocateId()));
            ttDetailVO.setPriceApplyCode(priceAllocateEntity.getPriceApplyCode())
                    .setPriceApplyId(priceAllocateEntity.getPriceApplyId());
        }

        if (null != ttPriceEntity.getContractPriceDetail()) {
            contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
            sourcePrice = ttPriceEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            source = ttPriceEntity.getTransactionPrice().add(sourcePrice);
        }

        //定价单含税总额
        BigDecimal applyTaxUnitPrice = source.multiply(ttPriceEntity.getOriginalPriceNum());
        //定价单不含税总额
        BigDecimal applyNotTaxUnitPrice = applyTaxUnitPrice.divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        //定价单增值税总额
        BigDecimal ApplyAddedTaxAllPrice = applyTaxUnitPrice.subtract(applyNotTaxUnitPrice);

        Integer priceComplete = BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO) ? PriceCompleteEnum.PRICE_COMPLETE.getType() : PriceCompleteEnum.NOT_PRICE_COMPLETE.getType();
        // TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        // TT编号 定价数量 定价价格 创建时间 取点价分配单上的点价申请时间
        ttPriceDetailVO
                .setCode(tradeTicketEntity.getCode())
                .setNum(ttPriceEntity.getNum())
                .setPrice(ttPriceEntity.getPrice())
                .setCreateTime(ttPriceEntity.getCreatedAt())
                .setSignDate(contractEntity.getSignDate())
                .setCreatedAt(tradeTicketEntity.getCreatedAt())
                .setPriceApplyTime(ttPriceEntity.getPriceTime())
                .setCustomerName(ttPriceEntity.getCustomerName())
                .setThisTimePriceNum(ttPriceEntity.getOriginalPriceNum())
                .setSupplierName(ttPriceEntity.getSupplierName())
                .setDomainCode(contractEntity.getDomainCode())
                .setGoodsId(contractEntity.getGoodsName())
                .setContractNum(ttPriceEntity.getThisContractNum())
                .setCategoryName(GoodsCategoryEnum.getDescByValue(ttPriceEntity.getGoodsCategoryId()))
                .setTotalPriceNum(null != ttPriceEntity.getTotalPriceNum() ? ttPriceEntity.getTotalPriceNum() : null)
                .setApplyUnitPrice(source)
                .setApplyTaxUnitPrice(applyTaxUnitPrice)
                .setApplyNotTaxUnitPrice(applyNotTaxUnitPrice)
                .setApplyAddedTaxAllPrice(ApplyAddedTaxAllPrice)
                .setAvePrice(ttPriceEntity.getAvePrice())
                .setRemainPriceNum(ttPriceEntity.getRemainPriceNum())
                .setPriceEndTime(StrUtil.isNotBlank(ttPriceEntity.getPriceEndTime()) ? ContractPriceEndTypeEnum.DATE.getValue() == ttPriceEntity.getPriceEndType() ? DateTimeUtil.formatDateString(DateTimeUtil.parseDateString(ttPriceEntity.getPriceEndTime())) : ttPriceEntity.getPriceEndTime() : null)
                .setPriceComplete(priceComplete)
                .setContractPriceEntity(contractPriceEntity);

        //定价完成
        if (priceComplete == PriceCompleteEnum.PRICE_COMPLETE.getType() && null != ttPriceEntity.getEndContractPrice()) {
            //最终合同价格
            ttPriceDetailVO.setEndContractPrice(ttPriceEntity.getEndContractPrice());
            //最终全额货款
            ttPriceDetailVO.setEndAllPrice(ttPriceEntity.getEndAllPrice());
            //增值税不含税总金额
            BigDecimal endNotTaxAllPrice = ttPriceDetailVO.getEndAllPrice().divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setEndNotTaxAllPrice(endNotTaxAllPrice);
            //最终增值税总金额
            ttPriceDetailVO.setEndAddedTaxAllPrice(ttPriceDetailVO.getEndAllPrice().subtract(endNotTaxAllPrice));

        }

//        //企标文件编号
//        if (null != contractEntity.getStandardFileId() && contractEntity.getStandardFileId() > 0) {
//            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(contractEntity.getStandardFileId());
//            ttPriceDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());
//
//        }
//        // 发货库点
//        //查询库点信息
//        ttPriceDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());
//
//        //原合同信息
//        ttQueryDetailVO.setRootContractId(ttAddEntity.getRootContractId());
//        if (null != ttAddEntity.getRootContractId()) {
//            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttAddEntity.getRootContractId());
//            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
//            ttQueryDetailVO.setRootContractCode(contractCode);
//        }
//        if (tradeTicketEntity.getUsage() != null) {
//            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
//        }
        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_DETAIL_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);
        ttDetailVO.setDetailType("2");

        return ttDetailVO;

    }

}
