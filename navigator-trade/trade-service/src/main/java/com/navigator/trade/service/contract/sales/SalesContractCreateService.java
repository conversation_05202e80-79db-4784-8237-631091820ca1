package com.navigator.trade.service.contract.sales;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStructurePricingStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.Impl.BaseContractCreateAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 销售合同创建的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_S_CONTRACT_ADD,SBM_S_CONTRACT_STRUCTURE_PRICE,SBO_S_CONTRACT_ADD")
public class SalesContractCreateService extends BaseContractCreateAbstractService {
    @Autowired
    public IContractOperationNewService salesContractOperationService;
    @Resource
    ContractStructureDao contractStructureDao;

    @Override
    protected void createAdditionalInfo(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {
            ContractStructureEntity contractStructureEntity = BeanConvertUtils.convert(ContractStructureEntity.class, contractCreateDTO.getContractStructureDTO());
            contractStructureEntity.setContractId(contractEntity.getId())
                    .setContractCode(contractEntity.getContractCode())
                    .setPriceStatus(ContractStructurePricingStatusEnum.INVALID.getValue())
                    .setTotalNum(contractEntity.getContractNum())
//                    .setPriceRule(contractCreateDTO.getContractStructureDTO().getPriceRule())
                    .setCreatedBy(contractEntity.getCreatedBy())
                    .setUpdatedBy(contractEntity.getUpdatedBy());

            contractStructureDao.save(contractStructureEntity);
        }
    }

    @Override
    protected void afterCreateProcess(ContractCreateDTO contractCreateDTO, ContractEntity contractEntity) {
        // 记录日志
        // 操作名称
        LogBizCodeEnum bizCodeEnum;

        if (contractCreateDTO.getActionSource().equals(ContractActionEnum.NEW.getActionValue())) {
            bizCodeEnum = LogBizCodeEnum.NEW_SALES_CONTRACT;
        } else if (contractCreateDTO.getActionSource().equals(ContractActionEnum.STRUCTURE_PRICING.getActionValue())) {
            bizCodeEnum = LogBizCodeEnum.NEW_SALES_STRUCTURE_PRICING;
        } else {
            bizCodeEnum = LogBizCodeEnum.CHANGE_SALES_CONTRACT;
        }

        String jsonData = JSONUtil.toJsonStr(contractEntity);

        // 记录操作记录
        try {
            salesContractOperationService.addContractOperationLog(contractEntity, bizCodeEnum, jsonData, SystemEnum.MAGELLAN.getValue());
        } catch (Exception e) {
            log.debug("AADContract,afterCreateProcess:{}", e.getMessage());
        }
    }
}
