package com.navigator.trade.app.trade;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.trade.app.trade.TradeAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 *  合同中台处理器
 *
 * <AUTHOR>
 */
@Service
public class TradeHandler {
    @Autowired
    @Lazy
    private Map<String, TradeAppService> tradeAppServiceMap;

    public TradeAppService getStrategy(String processKey) {
        for (String s : tradeAppServiceMap.keySet()) {
            if (s.contains(processKey)) {
                return tradeAppServiceMap.get(s);
            }
        }
        return null;
    }

    /**
     * 获取合同接口 ST_P_CONTRACT , WT_S_CONTRACT_WT , WT_P_CONTRACT , ST_S_CONTRACT
     *
     * @param salesType     销售类型
     * @param buCode       业务类型
     * @return
     */
    public TradeAppService getStrategy(Integer salesType, String buCode) {
        String processKey =  buCode + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_CONTRACT";
        return getStrategy(processKey);
    }

}
