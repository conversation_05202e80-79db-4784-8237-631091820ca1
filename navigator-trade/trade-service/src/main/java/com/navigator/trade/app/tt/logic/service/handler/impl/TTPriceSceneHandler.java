package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTPriceDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("PRICE_HANDLER")
public class TTPriceSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        SalesContractTTPriceDTO salesContractTTPriceDTO = ttdto.getSalesContractTTPriceDTO();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(salesContractTTPriceDTO.getContractId());

        // 初始化交易、销售类型、合同来源
        salesContractTTPriceDTO.setStatus(TTStatusEnum.APPROVING.getType());
        salesContractTTPriceDTO.setTradeType(ContractTradeTypeEnum.PRICE.getValue());
        salesContractTTPriceDTO.setSalesType(contractEntity.getSalesType());

        // 协议签署状态
        salesContractTTPriceDTO.setContractSignatureStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue());

        salesContractTTPriceDTO.setContractSource(ContractActionEnum.PRICE_CONFIRM.getActionValue());

        // add by zengshl
        if(BuCodeEnum.WT.getValue().equals(salesContractTTPriceDTO.getBuCode())
                && !(WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(salesContractTTPriceDTO.getWarrantTradeType()))) {
            salesContractTTPriceDTO.setStatus(TTStatusEnum.DONE.getType());
        }

        // 生成TT编号
        String code = getPriceTTCode(ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType(), contractEntity.getId());
        salesContractTTPriceDTO.setCode(code);
        ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);
    }


    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.PRICE.equals(ttTypeEnum);
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();
        // 1. 初始化
        initDTO(ttdto);

        // 2. 转换
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.price2TradeTicketDO(ttdto);

        // 3. 保存至数据库
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);
        // tradeTicketDO放到上下文中
        arrangeContext.setTradeTicketDO(tradeTicketDO);

        TradeTicketEntity ticketEntity = tradeTicketDO.getTradeTicketEntity();

        // 4. 定价完成处理
        TTPriceEntity priceEntity = (TTPriceEntity) tradeTicketDO.getTtSubEntity();
        ttDomainService.updateTtPriceComplete(priceEntity, ttdto.getContractEntity());

        // 5. 提交审批【TT完成也是要调用-出审批记录】
        ttApproveHandler.startTTApprove(ticketEntity.getId(), ttdto, null);


        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setContractCode(ticketEntity.getContractCode())
                .setCode(ticketEntity.getCode())
                .setTtId(ticketEntity.getId());

        ttQueryVOS.add(ttQueryVO);
        return ttQueryVOS;

    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {

        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTPriceEntity ttPriceEntity = (TTPriceEntity) tradeTicketDO.getTtSubEntity();
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();

        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());

        //ContractSignEntity contractSignEntity = contractSignQueryService.getContractSignDetailById(tradeTicketEntity.getSignId());

        //计算定价单单价
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        BigDecimal sourcePrice = BigDecimal.ZERO;
        BigDecimal source = BigDecimal.ZERO;

        ttDetailVO.setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setTtId(tradeTicketEntity.getId())
                .setTtCode(tradeTicketEntity.getCode())
                .setPriceApplyTime(tradeTicketEntity.getCreatedAt())
                .setContractSignId(tradeTicketEntity.getSignId())
                .setContractSignCode(tradeTicketEntity.getProtocolCode());

        if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() && TTTypeEnum.PRICE.getType().equals(tradeTicketEntity.getType())) {
            PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(String.valueOf(ttPriceEntity.getAllocateId()));
            ttDetailVO.setPriceApplyCode(priceAllocateEntity.getPriceApplyCode())
                    .setPriceApplyId(priceAllocateEntity.getPriceApplyId());
        }

        if (null != ttPriceEntity.getContractPriceDetail()) {
            contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
            sourcePrice = ttPriceEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            source = ttPriceEntity.getTransactionPrice().add(sourcePrice);
        }
        //定价单含税总额
        BigDecimal applyTaxUnitPrice = source.multiply(ttPriceEntity.getOriginalPriceNum());
        //定价单不含税总额
        BigDecimal applyNotTaxUnitPrice = applyTaxUnitPrice.divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        //定价单增值税总额
        BigDecimal ApplyAddedTaxAllPrice = applyTaxUnitPrice.subtract(applyNotTaxUnitPrice);

        Integer priceComplete = BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO) ? PriceCompleteEnum.PRICE_COMPLETE.getType() : PriceCompleteEnum.NOT_PRICE_COMPLETE.getType();
//         TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        // TT编号 定价数量 定价价格 创建时间 取点价分配单上的点价申请时间
        //企标文件编号
        ttPriceDetailVO
                .setCode(tradeTicketEntity.getCode())
                .setNum(ttPriceEntity.getNum())
                .setPrice(ttPriceEntity.getPrice())
                .setCreateTime(ttPriceEntity.getCreatedAt())
                .setSignDate(contractEntity.getSignDate())
                .setCreatedAt(tradeTicketEntity.getCreatedAt())
                .setPriceApplyTime(ttPriceEntity.getPriceTime())
                .setCustomerName(ttPriceEntity.getCustomerName())
                .setThisTimePriceNum(ttPriceEntity.getOriginalPriceNum())
                .setSupplierName(ttPriceEntity.getSupplierName())
                .setDomainCode(contractEntity.getDomainCode())
                .setGoodsId(contractEntity.getGoodsName())
                .setContractNum(ttPriceEntity.getThisContractNum())
                .setCategoryName(GoodsCategoryEnum.getDescByValue(ttPriceEntity.getGoodsCategoryId()))
                .setTotalPriceNum(null != ttPriceEntity.getTotalPriceNum() ? ttPriceEntity.getTotalPriceNum() : null)
                .setApplyUnitPrice(source)
                .setApplyTaxUnitPrice(applyTaxUnitPrice)
                .setApplyNotTaxUnitPrice(applyNotTaxUnitPrice)
                .setApplyAddedTaxAllPrice(ApplyAddedTaxAllPrice)
                .setAvePrice(ttPriceEntity.getAvePrice())
                .setRemainPriceNum(ttPriceEntity.getRemainPriceNum())
                .setPriceEndTime(StrUtil.isNotBlank(ttPriceEntity.getPriceEndTime()) ? ContractPriceEndTypeEnum.DATE.getValue() == ttPriceEntity.getPriceEndType() ? DateTimeUtil.formatDateString(DateTimeUtil.parseDateString(ttPriceEntity.getPriceEndTime())) : ttPriceEntity.getPriceEndTime() : null)
                .setPriceComplete(priceComplete)
                .setContractPriceEntity(contractPriceEntity);

        //定价完成
        if (priceComplete == PriceCompleteEnum.PRICE_COMPLETE.getType() && null != ttPriceEntity.getEndContractPrice()) {
            //最终合同价格
            ttPriceDetailVO.setEndContractPrice(ttPriceEntity.getEndContractPrice());
            //最终全额货款
            ttPriceDetailVO.setEndAllPrice(ttPriceEntity.getEndAllPrice());
            //增值税不含税总金额
            BigDecimal endNotTaxAllPrice = ttPriceDetailVO.getEndAllPrice().divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setEndNotTaxAllPrice(endNotTaxAllPrice);
            //最终增值税总金额
            ttPriceDetailVO.setEndAddedTaxAllPrice(ttPriceDetailVO.getEndAllPrice().subtract(endNotTaxAllPrice));

        }
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO)
                .setTtQueryDetailVO(new TTQueryDetailVO())
                .setDetailType("2");

        return ttDetailVO;
    }

    private String getPriceTTCode(boolean isSales, Integer contractId) {

        // 新合同的TT编号
        String code = isSales ? sequenceUtil.generateSpotSalesTTCode() : sequenceUtil.generateSpotPurchaseTTCode();

        // 原合同的TT编号
        List<TradeTicketEntity> tradeTicketEntityList = tradeTicketDao.getByContractId(contractId);

        List<TradeTicketEntity> tradeTicketEntities = tradeTicketEntityList.stream()
                // 合同基差转一口价外的TT
                .filter(i -> !(TTTypeEnum.REVISE.getType().equals(i.getType()) && i.getApprovalType() == null) ||
                        // 保存的TT
                        (i.getSourceType() != null && i.getSourceType() == SubmitTypeEnum.SAVE.getValue()))
                .collect(Collectors.toList());

        // 不存在TT记录
        if (tradeTicketEntities.isEmpty()) {
            return code;
        }
        return sequenceUtil.generateSpotChildTTCode(tradeTicketEntities.get(0).getCode());
    }
}
