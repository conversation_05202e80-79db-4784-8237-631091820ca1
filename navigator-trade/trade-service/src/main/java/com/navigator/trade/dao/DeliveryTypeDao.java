package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.trade.mapper.DeliveryTypeMapper;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-12-06 18:04
 */
@Dao
public class DeliveryTypeDao extends BaseDaoImpl<DeliveryTypeMapper, DeliveryTypeEntity> {
    /**
     * 获取所有交提货方式
     *
     * @return 交提货方式集合
     */
    public List<DeliveryTypeEntity> getAllDeliveryTypeList(Integer status, Integer categoryId, String buCode, Integer type) {
        return this.list(new LambdaQueryWrapper<DeliveryTypeEntity>()
                .eq(null != status, DeliveryTypeEntity::getStatus, status)
                .eq(null != categoryId, DeliveryTypeEntity::getCategoryId, categoryId)
                .eq(StringUtils.isNotBlank(buCode), DeliveryTypeEntity::getBuCode, buCode)
                .eq(null != type, DeliveryTypeEntity::getType, type)
                .eq(DeliveryTypeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<DeliveryTypeEntity> getAllDeliveryByAddressType(Integer status, Integer addressType) {
        return this.list(new LambdaQueryWrapper<DeliveryTypeEntity>()
                .eq(null != status, DeliveryTypeEntity::getStatus, status)
                .eq(null != addressType, DeliveryTypeEntity::getAddressType, addressType)
                .eq(DeliveryTypeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 获取提货方式
     *
     * @return 交提货方式集合
     */
    public DeliveryTypeEntity getDeliveryTypeByLkgCode(String lkgCode) {
        return this.getOne(new LambdaQueryWrapper<DeliveryTypeEntity>()
                .eq(StringUtil.isNotEmpty(lkgCode), DeliveryTypeEntity::getLkgCode, lkgCode)
                .eq(DeliveryTypeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public DeliveryTypeEntity getDeliveryTypeByLkgCodeNotId(String lkgCode, Integer deliveryTypeId) {
        return this.getOne(new LambdaQueryWrapper<DeliveryTypeEntity>()
                .eq(StringUtil.isNotEmpty(lkgCode), DeliveryTypeEntity::getLkgCode, lkgCode)
                .notIn(null != deliveryTypeId, DeliveryTypeEntity::getId, deliveryTypeId)
                .eq(DeliveryTypeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<Integer> getSendDeliveryTypeIdList() {
        // 获取提货类型为送货的ID集合
        List<DeliveryTypeEntity> typeEntityList = this.list(new LambdaQueryWrapper<DeliveryTypeEntity>()
                .eq(DeliveryTypeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(DeliveryTypeEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(DeliveryTypeEntity::getType, DeliveryModeEnum.SEND.getValue()));

        return typeEntityList.stream().map(DeliveryTypeEntity::getId).collect(Collectors.toList());
    }
}
