package com.navigator.trade.app.tt.domain.model;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TT领域模型
 * @Date 2024/7/14 15:54
 * @Version 1.0
 * <p>
 * TT(TradeTicketDO)
 * ⬇
 * 主TT(TradeTicketEntity)   子TT(TradeBaseEntity)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeTicketDO {
    /**
     * 主TT
     */
    private TradeTicketEntity tradeTicketEntity;

    /**
     * 子TT
     */
    private TTSubEntity ttSubEntity;


    /**
     * 价格详情
     */
    private ContractPriceEntity contractPriceEntity;


    public TTTypeEnum getTTType() {
        if (Objects.nonNull(tradeTicketEntity)) {
            return  TTTypeEnum.getByType(tradeTicketEntity.getType());
        }
        return null;
    }
}
