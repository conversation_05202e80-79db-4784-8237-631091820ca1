package com.navigator.trade.service.impl;

import com.navigator.activiti.pojo.dto.ApproveRuleDTO;
import com.navigator.activiti.pojo.dto.ApproveRuleItemDTO;

public class TTApproveRuleLogicService {

    public void readme(){


        //想想如何使用如下两个DTO
        //将TTApproveServiceImpl中的calcApproveRuleValue方法迁移到这里来
        //先分析审批规则，确保各个规则都能用ApproveRuleItemDTO来承载，该类尚不完善
        //最终返回ApproveRuleDTO，并放到ApproveDTO中传到Activiti
        ApproveRuleDTO approveRuleDTO;

        ApproveRuleItemDTO approveRuleItemDTO;
    }



}
