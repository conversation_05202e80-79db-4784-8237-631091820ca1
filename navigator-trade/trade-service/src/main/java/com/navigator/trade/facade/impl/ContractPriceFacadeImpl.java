package com.navigator.trade.facade.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.vo.TtPriceEntityVO;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITtPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

@RestController
public class ContractPriceFacadeImpl implements ContractPriceFacade {
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private ITtPriceService ttPriceService;
    @Autowired
    private IContractQueryService contractService;

    @Override
    public Result calculatePrice(PriceDetailBO priceDetailBO) {
        return contractPriceService.calculatePrice(priceDetailBO);
    }

    @Override
    public BigDecimal calculatePriceBo(PriceDetailBO priceDetailBO) {
        return contractPriceService.calculatePriceBo(priceDetailBO);
    }

    @Override
    public boolean saveTtPrice(TTPriceEntity ttPriceEntity) {
        return ttPriceService.saveTtPrice(ttPriceEntity);
    }

    @Override
    public TtPriceEntityVO getTtPrice(String contractCode) {
        TtPriceEntityVO ttPriceEntityVO = new TtPriceEntityVO();
        // 已定价量
        BigDecimal sumPriceNum = BigDecimal.ZERO;
        // 定价价格
        BigDecimal sumPrice = BigDecimal.ZERO;
        // 加权平均价
        BigDecimal avgPrice = BigDecimal.ZERO;

        List<TTPriceEntity> TTPriceEntityList = ttPriceService.getTtPrice(contractCode);

        if (CollectionUtil.isNotEmpty(TTPriceEntityList)) {
            ContractEntity contractEntity = contractService.getBasicContractById(TTPriceEntityList.get(0).getContractId());
            for (TTPriceEntity ttPriceEntity : TTPriceEntityList) {
                sumPriceNum = sumPriceNum.add(ttPriceEntity.getNum());
                sumPrice = sumPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
            }
            ttPriceEntityVO.setContractNum(contractEntity.getContractNum());
        }

        if (BigDecimalUtil.isGreaterThanZero(sumPriceNum)) {
            avgPrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, sumPrice, sumPriceNum);
        }
        ttPriceEntityVO
                .setSumPriceNum(sumPriceNum)
                .setAvgPrice(avgPrice)
                .setTTPriceEntityList(TTPriceEntityList);
        return ttPriceEntityVO;
    }

    @Override
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        return contractPriceService.getContractPriceEntityContractId(contractId);
    }

    @Override
    public ContractPriceEntity getContractPriceEntityByTTId(Integer ttId) {
        return contractPriceService.getContractPriceEntityByTTId(ttId);
    }

    @Override
    public boolean updatePriceByContractId(ContractPriceEntity contractPriceEntity) {
        return contractPriceService.updatePriceByContractId(contractPriceEntity);
    }

    @Override
    public Result updateContractForwardPrice(ContractEntity contractEntity, BigDecimal forwardPrice) {
        return Result.success(contractPriceService.updateContractForwardPrice(contractEntity, forwardPrice));
    }


    @Override
    public boolean syncContractPrice(ContractPriceEntity contractPriceEntity) {
        return contractPriceService.syncContractPrice(contractPriceEntity);
    }
}
