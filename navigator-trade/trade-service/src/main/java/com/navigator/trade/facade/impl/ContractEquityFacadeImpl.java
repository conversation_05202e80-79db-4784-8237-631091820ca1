package com.navigator.trade.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.facade.ContractEquityFacade;
import com.navigator.trade.pojo.dto.contractEquity.ContractChangeEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.service.contract.IContractChangeEquityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 权益变更接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/10
 */
@RestController
@RequiredArgsConstructor
public class ContractEquityFacadeImpl implements ContractEquityFacade {

    private final IContractChangeEquityService changeEquityService;

    @Override
    public Result getChangeContractEquityList(QueryDTO<ContractEquityQueryDTO> queryDTO) {
        return Result.page(changeEquityService.getChangeContractEquityList(queryDTO));
    }

    @Override
    public Result changeContractEquity(ContractChangeEquityDTO changeEquityDTO) {
        return Result.success(changeEquityService.changeContractEquity(changeEquityDTO));
    }

    @Override
    public Result getChangeEquityDetailByApplyCode(String applyCode) {
        return Result.success(changeEquityService.getChangeEquityDetailByApplyCode(applyCode));
    }

    @Override
    public Result getChangeEquityByContractCode(String contractCode) {
        return Result.success(changeEquityService.getChangeEquityByContractCode(contractCode));
    }

    @Override
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId) {
        return changeEquityService.getChangeContractEquityDetailByNotApprove(contractId);
    }

    @Override
    public Result getChangeContractEquityRecord(String contractCode) {
        return Result.success(changeEquityService.getChangeContractEquityRecord(contractCode));
    }
}
