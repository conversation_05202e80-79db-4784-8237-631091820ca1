package com.navigator.trade.app.tt.domain.service.processor;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("ADD")
public class TTAddDomainProcessor extends AbstractTTDomainProcessor {

    @Autowired
    TtAddDao ttAddDao;


    public TTAddDomainProcessor() {
        //super();
        System.out.println("TTAddDomainProcessor");
        System.out.println(getClass().getName());

    }


    @Override
    void addTTSubEntity(TradeTicketDO tradeTicketDO) {

        //set value
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(tradeTicketDO.getTradeTicketEntity().getId());

        TTAddEntity addEntity = (TTAddEntity) ttSubEntity;
        TTAddEntity oldAddEntity = ttAddDao.getTTAddEntityByTTId(addEntity.getTtId());
        if (Objects.nonNull(oldAddEntity)) {
            addEntity.setId(oldAddEntity.getId());
        }
        ttAddDao.saveOrUpdate(addEntity);
    }

    @Override
    boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {
        int rtn = ttAddDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        return rtn > 0;
    }
}
