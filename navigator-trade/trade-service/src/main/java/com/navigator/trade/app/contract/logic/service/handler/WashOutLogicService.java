package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.entity.ContractEntity;

/**
 * 合同解约索赔业务处理过程 逻辑处理
 *
 * <AUTHOR>
 */
public interface WashOutLogicService {

    /**
     * 校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractWashOutDTO 解约定赔dto
     */
    void washOutContractCheck(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO);

    /**
     * 处理父合同信息
     *
     * @param contractEntity     父合同
     * @param contractWashOutDTO 回购dto
     */
    void operateFatherContract(ContractEntity contractEntity, ContractWashOutDTO contractWashOutDTO);

    /**
     * 记录日志信息
     * @param contractWashOutDTO
     * @param contractEntity
     */
    void recordOperationLog(ContractWashOutDTO contractWashOutDTO, ContractEntity contractEntity);
}
