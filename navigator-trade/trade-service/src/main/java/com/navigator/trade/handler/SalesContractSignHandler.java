package com.navigator.trade.handler;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.trade.service.contractsign.IContractSignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 协议处理器
 *
 * <AUTHOR>
 */
@Service
public class SalesContractSignHandler {
    @Autowired
    @Lazy
    private Map<String, IContractSignService> contractSignServiceMap;

    public IContractSignService getStrategy(String processKey) {
        for (String s : contractSignServiceMap.keySet()) {
            if (s.contains(processKey)) {
                return contractSignServiceMap.get(s);
            }
        }
        return null;
    }
//
//    /**
//     * 获取协议接口
//     *
//     * @param salesType       销售类型
//     * @param ttType          tt类型
//     * @param goodsCategoryId 品类名称
//     * @return
//     */
//    public IContractSignService getStrategy(Integer salesType, Integer ttType, Integer goodsCategoryId) {
//
//        String processKey = GoodsCategoryEnum.getByValue(goodsCategoryId).getCode()
//                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
//                + "_SIGN_" + TTTypeEnum.getCodeByValue(ttType);
//        return getStrategy(processKey);
//    }
    /**
     * 获取协议接口
     *
     * @param salesType       销售类型
     * @param ttType          tt类型
     * @param goodsCategoryId 品类名称
     * @return
     */
    public IContractSignService getStrategy(Integer salesType, Integer ttType, Integer goodsCategoryId) {

        String processKey =  "SBM_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_SIGN_" + TTTypeEnum.getCodeByValue(ttType);
        return getStrategy(processKey);
    }
}
