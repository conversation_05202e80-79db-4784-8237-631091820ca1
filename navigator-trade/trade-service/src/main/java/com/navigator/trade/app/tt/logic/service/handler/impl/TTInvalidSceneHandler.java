package com.navigator.trade.app.tt.logic.service.handler.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.enums.UsageEnum;
import com.navigator.trade.pojo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("INVALID_HANDLER")
public class TTInvalidSceneHandler extends AbstractTTSceneHandler {


    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return false;
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        // 1、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.invalidTradeTicketDO(ttdto, arrangeContext);
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        // 2、保存
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);
        // 3、如果是线下那么需要进行审批的【都走审批需要有日志】
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        ResultCodeEnum submitResult = ttApproveHandler.submit(tradeTicketEntity.getId(), arrangeContext);
        // 4、返回结果
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        return Lists.newArrayList(ttQueryVO);
    }


    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTAddEntity ttAddEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();
        ttPriceDetailVO
                //合同编号
                .setContractCode(tradeTicketEntity.getContractCode())
                //合同id
                .setContractId(tradeTicketEntity.getContractId())
                //TT编号
                .setCode(tradeTicketEntity.getCode())
                //品种
                .setCategoryName(GoodsCategoryEnum.getByValue(ttAddEntity.getGoodsCategoryId()).getDesc())
                //合同类型
                .setContractType(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
                //关闭数量
                .setNum(ttAddEntity.getContractNum())
                //创建时间
                .setCreateTime(ttAddEntity.getCreatedAt())
                .setSignId(tradeTicketEntity.getSignId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode());
        ttDetailVO.setDetailType("5");
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);

        String modifyContent = ttAddEntity.getContent();
        List<CompareObjectDTO> list = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        ttDetailVO.setCompareObjectDTOList(list);
        ContractEntity originalContractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(ttAddEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(ttAddEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttAddEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttAddEntity.getSupplierId()));
        }

        //买家

        if (null != ttAddEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttAddEntity.getCustomerId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getCustomerId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }

        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }

        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(ttAddEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttAddEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        //原合同信息
        ttQueryDetailVO.setRootContractId(ttAddEntity.getRootContractId());
        if (null != ttAddEntity.getRootContractId()) {
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttAddEntity.getRootContractId());
            String contractCode = contractEntity != null ? contractEntity.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        return ttDetailVO;
    }
}
