package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.dao.TtAddDao;
import com.navigator.trade.dao.TtModifyDao;
import com.navigator.trade.dao.TtPriceDao;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.*;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TradeTicketConvertUtil {
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private WarehouseFacade warehouseFacade;
    @Autowired
    private PriceAllocateFacade priceAllocateFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TtPriceDao ttPriceDao;
    @Autowired
    private TtAddDao ttAddDao;
    @Autowired
    private TtModifyDao ttModifyDao;
    @Autowired
    private TradeTicketDao tradeTicketDao;

    @Autowired
    private IContractQueryService contractService;
    @Autowired
    private IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    private IContractPriceService contractPriceService;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private CompanyFacade companyFacade;


    public TradeTicketEntity getAddTradeTicketEntity(SalesContractAddTTDTO salesContractAddTTDTO) {
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BigDecimal beforeContractNum = BigDecimal.ZERO;
        BigDecimal changeContractNum = StringUtils.isNotBlank(salesContractAddTTDTO.getContractNum()) ?
                new BigDecimal(salesContractAddTTDTO.getContractNum()) : BigDecimal.ZERO;
        BigDecimal afterContractNum = changeContractNum;
        // TT 不可见
        if (salesContractAddTTDTO.getAddedSignatureType() == -1) {
            TradeTicketEntity tradeTicketEntityById = tradeTicketDao.getTradeTicketEntityById(salesContractAddTTDTO.getTtId());
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractAddTTDTO.getSourceContractId());
            if (null != contractEntity) {
                BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
                Date date = new Date();

                // 回购需要记录父合同的数量溯源需要显示
                if (ContractTradeTypeEnum.BUYBACK.getValue() == salesContractAddTTDTO.getTradeType() && salesContractAddTTDTO.getSourceContractId() != null) {
                    beforeContractNum = contractEntity.getContractNum();
                }

                tradeTicketEntity
                        .setContractId(contractEntity.getId())
                        .setSalesType(contractEntity.getSalesType())
                        .setIsDeleted(1)
                        .setCreatedBy(contractEntity.getCreatedBy())
                        .setUpdatedBy(contractEntity.getUpdatedBy())
                        .setCreatedAt(date)
                        .setUpdatedAt(date)
                        .setBankId(contractEntity.getSupplierAccountId())
                        .setSourceContractId(tradeTicketEntityById.getSourceContractId());

                TradeTicketEntity tradeTicketEntityByGroupId = tradeTicketDao.getByGroupId(tradeTicketEntityById.getGroupId(), salesContractAddTTDTO.getTtId());
                if (tradeTicketEntityByGroupId != null) {
                    tradeTicketEntity.setId(tradeTicketEntityByGroupId.getId());
                    tradeTicketEntity.setCode(tradeTicketEntityByGroupId.getCode());
                } else {
                    tradeTicketEntity.setId(null);
                    tradeTicketEntity.setCode(CodeGeneratorUtil.genPurchaseTTNewCode());
                }
            }
        } else {
            BeanUtils.copyProperties(salesContractAddTTDTO, tradeTicketEntity);
            Date date = new Date();
            tradeTicketEntity
                    .setId(salesContractAddTTDTO.getTtId())
                    .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    .setCreatedAt(date)
                    .setUpdatedAt(date)
                    .setBankId(salesContractAddTTDTO.getSupplierAccountId())
                    .setCode(salesContractAddTTDTO.getCode())
                    .setSourceContractId(salesContractAddTTDTO.getSourceContractId())
            ;
            if (StringUtils.isNotBlank(salesContractAddTTDTO.getContractType())) {
                tradeTicketEntity.setContractType(Integer.parseInt(salesContractAddTTDTO.getContractType()));
            }
            if (StringUtils.isNotBlank(salesContractAddTTDTO.getOwnerId())) {
                tradeTicketEntity.setOwnerId(Integer.parseInt(salesContractAddTTDTO.getOwnerId()));
            }
            CustomerDTO customer = null;
            Integer companyId = 1;
            // TODO 需要改成从账套去获取
            if (null != salesContractAddTTDTO.getCustomerId()) {
                customer = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
            }
            if (customer != null) {
                tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
                tradeTicketEntity.setCustomerId(customer.getId());
                tradeTicketEntity.setCustomerName(customer.getName());
            }
            CustomerDTO supplier = null;
            if (null != salesContractAddTTDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractAddTTDTO.getSupplierId());
            }
            if (supplier != null) {
                tradeTicketEntity.setSupplierId(supplier.getId());
                tradeTicketEntity.setSupplierName(supplier.getName());
                tradeTicketEntity.setSupplierCode(supplier.getLinkageCustomerCode());
            }

            // add by zengshl
            if (ObjectUtil.isNotEmpty(salesContractAddTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractAddTTDTO.getSiteCode());
                tradeTicketEntity.setBelongCustomerId(siteEntity.getBelongCustomerId());
                tradeTicketEntity.setCompanyId(siteEntity.getCompanyId());
                tradeTicketEntity.setCompanyName(siteEntity.getCompanyName());
                salesContractAddTTDTO.setCompanyId(siteEntity.getCompanyId());
                salesContractAddTTDTO.setCompanyName(siteEntity.getCompanyName());
                salesContractAddTTDTO.setBelongCustomerId(siteEntity.getBelongCustomerId());
                salesContractAddTTDTO.setDeliveryFactoryCode(siteEntity.getFactoryCode());
                salesContractAddTTDTO.setDeliveryFactoryName(siteEntity.getFactoryName());
            }

            if (ContractTradeTypeEnum.BUYBACK.getValue() == salesContractAddTTDTO.getTradeType() && salesContractAddTTDTO.getSourceContractId() != null) {
                ContractEntity contractEntity = contractService.getBasicContractById(salesContractAddTTDTO.getSourceContractId());
                tradeTicketEntity.setSourceContractId(salesContractAddTTDTO.getSourceContractId());
                tradeTicketEntity.setTradeType(ContractTradeTypeEnum.BUYBACK.getValue());
                beforeContractNum = contractEntity.getContractNum();
                afterContractNum = beforeContractNum.subtract(changeContractNum);
            }
        }
        tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setBeforeContractNum(beforeContractNum);
        tradeTicketEntity.setChangeContractNum(changeContractNum);
        tradeTicketEntity.setAfterContractNum(afterContractNum);
        // TODO 根据商品SKUID去获取 商品信息
        if (salesContractAddTTDTO.getGoodsId() != null) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractAddTTDTO.getGoodsId());
            tradeTicketEntity.setGoodsId(skuEntity.getId());
            tradeTicketEntity.setGoodsName(skuEntity.getFullName());
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 start
            tradeTicketEntity.setCommodityName(StringUtils.isNotBlank(salesContractAddTTDTO.getCommodityName()) ? salesContractAddTTDTO.getCommodityName() : skuEntity.getNickName());
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-10 end
            tradeTicketEntity.setCategory1(skuEntity.getCategory1());
            tradeTicketEntity.setCategory2(skuEntity.getCategory2());
            tradeTicketEntity.setCategory3(skuEntity.getCategory3());
            tradeTicketEntity.setGoodsCategoryId(skuEntity.getCategory1());
            tradeTicketEntity.setSubGoodsCategoryId(skuEntity.getCategory2());
        }

        tradeTicketEntity.setBuCode(ObjectUtil.isNotEmpty(salesContractAddTTDTO.getBuCode()) ? salesContractAddTTDTO.getBuCode() : BuCodeEnum.ST.getValue());
        // 合同性质差异
        if (BuCodeEnum.WT.getValue().equals(tradeTicketEntity.getBuCode())) {
            tradeTicketEntity.setContractNature(ContractNatureEnum.WAREHOUSE_TRADE.getValue());
        } else {
            tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        }
        tradeTicketEntity.setSiteCode(salesContractAddTTDTO.getSiteCode());
        tradeTicketEntity.setSiteName(salesContractAddTTDTO.getSiteName());
        return tradeTicketEntity;
    }

    public TradeTicketEntity getReviseTradeTicketEntity(SalesContractReviseTTDTO salesContractReviseTTDTO, String groupId) {
        TradeTicketEntity tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractReviseTTDTO);
        if (salesContractReviseTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getSourceContractId());
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, contractEntity);
            tradeTicketEntity
                    .setId(null)
                    .setCode(salesContractReviseTTDTO.getCode())
                    .setType(TTTypeEnum.SPLIT.getType())
                    .setContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
                    .setTradeType(salesContractReviseTTDTO.getTradeType())
                    .setGroupId(groupId)
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractReviseTTDTO.getSupplierAccountId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
            ;
        } else {
            tradeTicketEntity
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractReviseTTDTO.getSupplierAccountId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
            ;
            //查询客户信息
            //List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
            //Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
            CustomerDTO customer = null;
            //Integer companyId = 1;
            if (null != salesContractReviseTTDTO.getCustomerId()) {
                customer = customerFacade.getCustomerById(salesContractReviseTTDTO.getCustomerId());
            }
            if (customer != null) {
                tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
                tradeTicketEntity.setCustomerId(customer.getId());
                tradeTicketEntity.setCustomerName(customer.getName());
                /*if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = customer.getCompanyId();
                }*/
            }

            CustomerDTO supplier = null;
            if (null != salesContractReviseTTDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractReviseTTDTO.getSupplierId());
            }
            if (supplier != null) {
                tradeTicketEntity.setSupplierId(supplier.getId());
                tradeTicketEntity.setSupplierName(supplier.getName());
                /*if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = supplier.getCompanyId();
                }*/
            }

            /*CompanyEntity companyEntity = companyFacade.queryCompanyById(companyId);

            tradeTicketEntity.setCompanyId(companyId);
            tradeTicketEntity.setCompanyName(companyEntity.getShortName());
            salesContractReviseTTDTO.setCompanyId(companyId);
            salesContractReviseTTDTO.setCompanyName(companyEntity.getShortName());

            if (StringUtils.isNotBlank(salesContractReviseTTDTO.getDeliveryFactoryCode())) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(salesContractReviseTTDTO.getDeliveryFactoryCode());
                if (factoryEntity != null) {
                    CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), companyId);
                    tradeTicketEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                    salesContractReviseTTDTO.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                }
            }*/

            // 账套更新
            if (StringUtils.isNotBlank(salesContractReviseTTDTO.getSiteCode())) {
                SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractReviseTTDTO.getSiteCode());
                Integer belongCustomerId = siteEntity.getBelongCustomerId();
                Integer companyId = siteEntity.getCompanyId();
                String companyName = siteEntity.getCompanyName();

                tradeTicketEntity
                        .setSiteCode(siteEntity.getCode())
                        .setSiteName(siteEntity.getName())
                        .setBelongCustomerId(belongCustomerId)
                        .setCompanyId(companyId)
                        .setCompanyName(companyName);

                salesContractReviseTTDTO.setCompanyId(companyId)
                        .setCompanyName(companyName)
                        .setBelongCustomerId(belongCustomerId);
            }


            if (salesContractReviseTTDTO.getSourceContractId() != null) {
                ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getSourceContractId());
                BigDecimal beforeContractNum = contractEntity.getContractNum();
                BigDecimal changeContractNum = salesContractReviseTTDTO.getContractNum();
                if (salesContractReviseTTDTO.getTradeType().equals(ContractTradeTypeEnum.REVISE_NORMAL.getValue())) {
                    changeContractNum = BigDecimal.ZERO;
                }
                BigDecimal afterContractNum = beforeContractNum.subtract(changeContractNum);
                tradeTicketEntity.setBeforeContractNum(beforeContractNum);
                tradeTicketEntity.setChangeContractNum(changeContractNum);
                tradeTicketEntity.setAfterContractNum(afterContractNum);
            }
            tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
            tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        }

        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 start
        /*if (null != salesContractReviseTTDTO.getSupplierId() &&
                null != salesContractReviseTTDTO.getGoodsCategoryId() &&
                ObjectUtil.isNotEmpty(salesContractReviseTTDTO.getGoodsPackageId()) &&
                ObjectUtil.isNotEmpty(salesContractReviseTTDTO.getGoodsSpecId())) {
            GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO()
                    .setSupplierId(salesContractReviseTTDTO.getSupplierId())
                    .setCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                    .setPackageId(salesContractReviseTTDTO.getGoodsPackageId())
                    .setSpecId(salesContractReviseTTDTO.getGoodsSpecId());
            GoodsInfoVO goodsInfoVO = goodsFacade.acquireGoodsInfo(goodsSpecDTO);
            tradeTicketEntity.setGoodsId(goodsInfoVO.getGoodsId());
            tradeTicketEntity.setGoodsName(goodsInfoVO.getGoodsName());
            tradeTicketEntity.setCategory1(goodsInfoVO.getCategory1());
            tradeTicketEntity.setCategory2(goodsInfoVO.getCategory2());
            tradeTicketEntity.setCategory3(goodsInfoVO.getCategory3());
        }*/
        // 商品信息
        if (salesContractReviseTTDTO.getGoodsId() != null) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractReviseTTDTO.getGoodsId());
            if (skuEntity != null) {
                tradeTicketEntity
                        .setGoodsId(skuEntity.getId())
                        .setGoodsName(skuEntity.getFullName())
                        .setCategory1(skuEntity.getCategory1())
                        .setCategory2(skuEntity.getCategory2())
                        .setCategory3(skuEntity.getCategory3())
                        .setGoodsCategoryId(skuEntity.getCategory1())
                        .setSubGoodsCategoryId(skuEntity.getCategory2());
                salesContractReviseTTDTO.setGoodsId(skuEntity.getId());
                salesContractReviseTTDTO.setGoodsName(skuEntity.getFullName());
                salesContractReviseTTDTO.setCommodityName(StringUtils.isNotBlank(salesContractReviseTTDTO.getCommodityName()) ? salesContractReviseTTDTO.getCommodityName() : skuEntity.getNickName());
            }
        }
        // 商品昵称
        if (StringUtils.isNotBlank(salesContractReviseTTDTO.getCommodityName())) {
            tradeTicketEntity.setCommodityName(salesContractReviseTTDTO.getCommodityName());
        }

        // add by zengshl TT 这边需要新增的字段信息 （默认取编码或者值）
        tradeTicketEntity.setBuCode(BuCodeEnum.ST.getValue());
        tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 end

        return tradeTicketEntity;
    }

    public TradeTicketEntity getSplitTradeTicketEntity(SalesContractSplitTTDTO salesContractSplitTTDTO, String groupId) {
        TradeTicketEntity tradeTicketEntity = null;
        BigDecimal beforeContractNum = BigDecimal.ZERO;
        BigDecimal changeContractNum = salesContractSplitTTDTO.getContractNum() != null ? salesContractSplitTTDTO.getContractNum() : BigDecimal.ZERO;
        BigDecimal afterContractNum = changeContractNum;
        if (salesContractSplitTTDTO.getAddedSignatureType() == 1 || salesContractSplitTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, contractEntity);
            tradeTicketEntity
                    .setId(null)
                    .setCode(salesContractSplitTTDTO.getCode())
                    .setType(TTTypeEnum.SPLIT.getType())
                    .setContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractSplitTTDTO.getSupplierAccountId())
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setGroupId(groupId)
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())

            ;

            if (salesContractSplitTTDTO.getSourceContractId() != null) {
                beforeContractNum = contractEntity.getContractNum();
                changeContractNum = salesContractSplitTTDTO.getContractNum();
                afterContractNum = beforeContractNum.subtract(changeContractNum);
            }
        } else {
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractSplitTTDTO);
            tradeTicketEntity
                    .setContractId(salesContractSplitTTDTO.getSonContractId())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setBankId(salesContractSplitTTDTO.getSupplierAccountId())
                    .setGroupId(groupId)
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
            ;
            //查询客户信息
            //List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
            //Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
            CustomerDTO customer = null;
            Integer companyId = 1;
            if (null != salesContractSplitTTDTO.getCustomerId()) {
                customer = customerFacade.getCustomerById(salesContractSplitTTDTO.getCustomerId());
            }
            if (customer != null) {
                tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
                tradeTicketEntity.setCustomerId(customer.getId());
                tradeTicketEntity.setCustomerName(customer.getName());
                if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = customer.getCompanyId();
                }
            }

            CustomerDTO supplier = null;
            if (null != salesContractSplitTTDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractSplitTTDTO.getSupplierId());
            }
            if (supplier != null) {
                tradeTicketEntity.setSupplierId(supplier.getId());
                tradeTicketEntity.setSupplierName(supplier.getName());
                if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = supplier.getCompanyId();
                }
            }

            CompanyEntity companyEntity = companyFacade.queryCompanyById(companyId);

            tradeTicketEntity.setCompanyId(companyId);
            tradeTicketEntity.setCompanyName(companyEntity.getShortName());
            salesContractSplitTTDTO.setCompanyId(companyId);
            salesContractSplitTTDTO.setCompanyName(companyEntity.getShortName());

            if (StringUtils.isNotBlank(salesContractSplitTTDTO.getDeliveryFactoryCode())) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(salesContractSplitTTDTO.getDeliveryFactoryCode());
                if (factoryEntity != null) {
                    CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), companyId);
                    tradeTicketEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                    salesContractSplitTTDTO.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                }
            }

        }
        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 start
        if (null != salesContractSplitTTDTO.getGoodsId()) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractSplitTTDTO.getGoodsId());
            if (null != skuEntity) {
                tradeTicketEntity.setGoodsId(skuEntity.getId());
                tradeTicketEntity.setGoodsName(skuEntity.getFullName());
                tradeTicketEntity.setCategory1(skuEntity.getCategory1());
                tradeTicketEntity.setCategory2(skuEntity.getCategory2());
                tradeTicketEntity.setCategory3(skuEntity.getCategory3());
                tradeTicketEntity.setGoodsCategoryId(skuEntity.getCategory1());
                tradeTicketEntity.setSubGoodsCategoryId(skuEntity.getCategory2());
            }
        }
        // add by zengshl TT 这边需要新增的字段信息 （默认取编码或者值）
        tradeTicketEntity.setBuCode(BuCodeEnum.ST.getValue());
        tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 end

        tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setBeforeContractNum(beforeContractNum);
        tradeTicketEntity.setChangeContractNum(changeContractNum);
        tradeTicketEntity.setAfterContractNum(afterContractNum);
        return tradeTicketEntity;
    }

    public TradeTicketEntity getPriceTradeTicketEntity(SalesContractTTPriceDTO salesContractTTPriceDTO) {
        TradeTicketEntity tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractTTPriceDTO);
        Date date = new Date();
        tradeTicketEntity
                .setId(salesContractTTPriceDTO.getTtId())
                .setCreatedBy(Integer.parseInt(salesContractTTPriceDTO.getUserId()))
                .setUpdatedBy(Integer.parseInt(salesContractTTPriceDTO.getUserId()))
                .setSalesType(salesContractTTPriceDTO.getSalesType())
                .setBankId(salesContractTTPriceDTO.getSupplierAccountId())
                .setCreatedAt(date)
                .setUpdatedAt(date)
                .setSourceContractId(salesContractTTPriceDTO.getSourceContractId())
        ;
        if (salesContractTTPriceDTO.getContractType() != null) {
            tradeTicketEntity.setContractType(salesContractTTPriceDTO.getContractType());
        }
        //查询客户信息
        //List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        //Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
        CustomerDTO customer = null;
        //Integer companyId = 1;
        if (null != salesContractTTPriceDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesContractTTPriceDTO.getCustomerId());
        }
        if (customer != null) {
            tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
            tradeTicketEntity.setCustomerId(customer.getId());
            tradeTicketEntity.setCustomerName(customer.getName());
            /*if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                companyId = customer.getCompanyId();
            }*/
        }

        CustomerDTO supplier = null;
        if (null != salesContractTTPriceDTO.getSupplierId()) {
            supplier = customerFacade.getCustomerById(salesContractTTPriceDTO.getSupplierId());
        }
        if (supplier != null) {
            tradeTicketEntity.setSupplierId(supplier.getId());
            tradeTicketEntity.setSupplierName(supplier.getName());
            /*if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                companyId = supplier.getCompanyId();
            }*/
        }

         /*CompanyEntity companyEntity = companyFacade.queryCompanyById(companyId);

        tradeTicketEntity.setCompanyId(companyId);
        tradeTicketEntity.setCompanyName(companyEntity.getShortName());
        if (StringUtils.isNotBlank(salesContractTTPriceDTO.getDeliveryFactoryCode())) {
            FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(salesContractTTPriceDTO.getDeliveryFactoryCode());
            if (factoryEntity != null) {
                CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), companyId);
                tradeTicketEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                salesContractTTPriceDTO.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
            }
        }*/
        // 账套更新
        if (StringUtils.isNotBlank(salesContractTTPriceDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesContractTTPriceDTO.getSiteCode());
            Integer belongCustomerId = siteEntity.getBelongCustomerId();
            Integer companyId = siteEntity.getCompanyId();
            String companyName = siteEntity.getCompanyName();

            tradeTicketEntity
                    .setSiteCode(siteEntity.getCode())
                    .setSiteName(siteEntity.getName())
                    .setBelongCustomerId(belongCustomerId)
                    .setCompanyId(companyId)
                    .setCompanyName(companyName);
            salesContractTTPriceDTO.setBelongCustomerId(belongCustomerId);
        }

        // 所属商务
        if (null != salesContractTTPriceDTO.getOwnerId()) {
            tradeTicketEntity.setOwnerId(salesContractTTPriceDTO.getOwnerId());
        }
        tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setBeforeContractNum(BigDecimal.ZERO);
        tradeTicketEntity.setChangeContractNum(salesContractTTPriceDTO.getNum());
        tradeTicketEntity.setAfterContractNum(salesContractTTPriceDTO.getNum());
        tradeTicketEntity.setDomainCode(salesContractTTPriceDTO.getDomainCode());

        // 商品信息
        if (salesContractTTPriceDTO.getGoodsId() != null) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractTTPriceDTO.getGoodsId());
            if (skuEntity != null) {
                tradeTicketEntity
                        .setGoodsId(skuEntity.getId())
                        .setGoodsName(skuEntity.getFullName())
                        .setCategory1(skuEntity.getCategory1())
                        .setCategory2(skuEntity.getCategory2())
                        .setCategory3(skuEntity.getCategory3())
                        .setGoodsCategoryId(skuEntity.getCategory1())
                        .setSubGoodsCategoryId(skuEntity.getCategory2());
            }
        }
        // add by zengshl TT 这边需要新增的字段信息 （默认取编码或者值）
        tradeTicketEntity.setBuCode(BuCodeEnum.ST.getValue());
        tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 end

        return tradeTicketEntity;
    }

    public TradeTicketEntity getTransferTradeTicketEntity(SalesContractTTTransferDTO salesContractTTTransferDTO) {
        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        BigDecimal beforeContractNum = BigDecimal.ZERO;
        BigDecimal changeContractNum = salesContractTTTransferDTO.getNum() != null ? salesContractTTTransferDTO.getNum() : BigDecimal.ZERO;
        BigDecimal afterContractNum = changeContractNum;
        if (salesContractTTTransferDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTTransferDTO.getSourceContractId());
            BeanUtils.copyProperties(contractEntity, tradeTicketEntity);
            Date date = new Date();
            String code = CodeGeneratorUtil.genSalesTTNewCode();
            if (ContractSalesTypeEnum.PURCHASE.getValue() == salesContractTTTransferDTO.getSalesType()) {
                code = CodeGeneratorUtil.genPurchaseTTNewCode();
            }
            tradeTicketEntity
                    .setId(null)
                    .setCode(code)
                    .setType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()) ? TTTypeEnum.TRANSFER.getType() : TTTypeEnum.REVERSE_PRICE.getType())
                    .setContractId(salesContractTTTransferDTO.getSourceContractId())
                    .setTradeType(salesContractTTTransferDTO.getTradeType())
                    .setCreatedBy(Integer.parseInt(salesContractTTTransferDTO.getUserId()))
                    .setUpdatedBy(Integer.parseInt(salesContractTTTransferDTO.getUserId()))
                    .setContractSource(salesContractTTTransferDTO.getContractSource())
                    .setBankId(salesContractTTTransferDTO.getSupplierAccountId())
                    .setCreatedAt(date)
                    .setUpdatedAt(date)
                    .setSourceContractId(salesContractTTTransferDTO.getSourceContractId())
            ;
            if (salesContractTTTransferDTO.getSourceContractId() != null) {
                beforeContractNum = contractEntity.getContractNum();
                changeContractNum = salesContractTTTransferDTO.getNum();
                afterContractNum = beforeContractNum.subtract(changeContractNum);
            }

        } else {
            tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesContractTTTransferDTO);
            Date date = new Date();
            tradeTicketEntity
                    .setType(salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue()) ? TTTypeEnum.TRANSFER.getType() : TTTypeEnum.REVERSE_PRICE.getType())
                    .setContractId(salesContractTTTransferDTO.getSonContractId() != null ? salesContractTTTransferDTO.getSonContractId() : salesContractTTTransferDTO.getContractId())
                    .setTradeType(salesContractTTTransferDTO.getTradeType())
                    .setCreatedBy(Integer.parseInt(salesContractTTTransferDTO.getUserId()))
                    .setUpdatedBy(Integer.parseInt(salesContractTTTransferDTO.getUserId()))
                    .setContractSource(salesContractTTTransferDTO.getContractSource())
                    .setBankId(salesContractTTTransferDTO.getSupplierAccountId())
                    .setCreatedAt(date)
                    .setUpdatedAt(date)
                    .setSourceContractId(salesContractTTTransferDTO.getSourceContractId())
            ;

            if (salesContractTTTransferDTO.getContractType() != null) {
                tradeTicketEntity.setContractType(salesContractTTTransferDTO.getContractType());
            }
            //查询客户信息
            //List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
            //Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
            CustomerDTO customer = null;
            Integer companyId = 1;
            if (null != salesContractTTTransferDTO.getCustomerId()) {
                customer = customerFacade.getCustomerById(salesContractTTTransferDTO.getCustomerId());
            }
            if (customer != null) {
                tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
                tradeTicketEntity.setCustomerId(customer.getId());
                tradeTicketEntity.setCustomerName(customer.getName());
                if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = customer.getCompanyId();
                }
            }

            CustomerDTO supplier = null;
            if (null != salesContractTTTransferDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractTTTransferDTO.getSupplierId());
            }
            if (supplier != null) {
                tradeTicketEntity.setSupplierId(supplier.getId());
                tradeTicketEntity.setSupplierName(supplier.getName());
                if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType()) {
                    companyId = supplier.getCompanyId();
                }
            }

            CompanyEntity companyEntity = companyFacade.queryCompanyById(companyId);

            tradeTicketEntity.setCompanyId(companyId);
            tradeTicketEntity.setCompanyName(companyEntity.getShortName());
            salesContractTTTransferDTO.setCompanyId(companyId);
            salesContractTTTransferDTO.setCompanyName(companyEntity.getShortName());

            if (StringUtils.isNotBlank(salesContractTTTransferDTO.getDeliveryFactoryCode())) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(salesContractTTTransferDTO.getDeliveryFactoryCode());
                if (factoryEntity != null) {
                    CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), companyId);
                    tradeTicketEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                    salesContractTTTransferDTO.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
                }
            }
        }
        tradeTicketEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        tradeTicketEntity.setBeforeContractNum(beforeContractNum);
        tradeTicketEntity.setChangeContractNum(changeContractNum);
        tradeTicketEntity.setAfterContractNum(afterContractNum);

        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 start
        if (null != salesContractTTTransferDTO.getGoodsId()) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractTTTransferDTO.getGoodsId());
            if (null != skuEntity) {
                tradeTicketEntity.setGoodsId(skuEntity.getId());
                tradeTicketEntity.setGoodsName(skuEntity.getFullName());
                tradeTicketEntity.setCategory1(skuEntity.getCategory1());
                tradeTicketEntity.setCategory2(skuEntity.getCategory2());
                tradeTicketEntity.setCategory3(skuEntity.getCategory3());
                tradeTicketEntity.setGoodsCategoryId(skuEntity.getCategory1());
                tradeTicketEntity.setSubGoodsCategoryId(skuEntity.getCategory2());
            }
        }
        // add by zengshl TT 这边需要新增的字段信息 （默认取编码或者值）
        tradeTicketEntity.setBuCode(BuCodeEnum.ST.getValue());
        tradeTicketEntity.setContractNature(ContractNatureEnum.SPOT_TRADE.getValue());
        // V1 转化TT获取商品信息字段以及默认的编码 Author:zengshl 2024-06-18 end

        return tradeTicketEntity;
    }

    /**
     * TODO 没有账套|没有品种
     * 设置结构化定价TT
     *
     * @param ttdto
     * @return
     */
    public TradeTicketEntity getStructureTradeTicketEntity(TTDTO ttdto) {

        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        TradeTicketEntity tradeTicketEntity = BeanConvertUtils.map(TradeTicketEntity.class, salesStructurePriceTTDTO);
        tradeTicketEntity.setContractType(ContractTypeEnum.STRUCTURE.getValue());
        tradeTicketEntity.setType(TTTypeEnum.STRUCTURE_PRICE.getType());
        tradeTicketEntity.setContractStatus(ContractStatusEnum.INEFFECTIVE.getValue());
        if (StringUtils.isNotBlank(salesStructurePriceTTDTO.getOwnerId())) {
            tradeTicketEntity.setOwnerId(Integer.parseInt(salesStructurePriceTTDTO.getOwnerId()));
        }
        //查询客户信息
        CustomerDTO customer = null;
        Integer companyId = 1;
        if (null != salesStructurePriceTTDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesStructurePriceTTDTO.getCustomerId());
        }
        if (customer != null) {
            tradeTicketEntity.setCustomerCode(customer.getLinkageCustomerCode());
            tradeTicketEntity.setCustomerId(customer.getId());
            tradeTicketEntity.setCustomerName(customer.getName());
        }

        CustomerDTO supplier = null;
        if (null != salesStructurePriceTTDTO.getSupplierId()) {
            supplier = customerFacade.getCustomerById(salesStructurePriceTTDTO.getSupplierId());
        }
        if (supplier != null) {
            tradeTicketEntity.setSupplierId(supplier.getId());
            tradeTicketEntity.setSupplierName(supplier.getName());
        }
        // 查询账套信息
        SiteEntity siteEntity = siteFacade.getSiteDetailByCode(salesStructurePriceTTDTO.getSiteCode());
        if (ObjectUtil.isNotEmpty(siteEntity)) {
            tradeTicketEntity.setCompanyId(siteEntity.getCompanyId());
            tradeTicketEntity.setCompanyName(siteEntity.getCompanyName());
            tradeTicketEntity.setBelongCustomerId(siteEntity.getBelongCustomerId());
            tradeTicketEntity.setSiteName(siteEntity.getName());
            salesStructurePriceTTDTO.setCompanyId(siteEntity.getCompanyId());
            salesStructurePriceTTDTO.setCompanyName(siteEntity.getCompanyName());
            salesStructurePriceTTDTO.setSiteName(siteEntity.getName());
            salesStructurePriceTTDTO.setBelongCustomerId(siteEntity.getBelongCustomerId());
        }

        Date date = new Date();
        tradeTicketEntity
                .setBuCode(BuCodeEnum.ST.getValue())
                .setId(salesStructurePriceTTDTO.getTtId())
                .setGoodsCategoryId(salesStructurePriceTTDTO.getCategory1())
                .setSubGoodsCategoryId(salesStructurePriceTTDTO.getCategory2())
                .setCategory1(salesStructurePriceTTDTO.getCategory1())
                .setCategory2(salesStructurePriceTTDTO.getCategory2())
                .setCategory3(salesStructurePriceTTDTO.getCategory3())
                .setCreatedAt(date)
                .setUpdatedAt(date)
                .setCode(salesStructurePriceTTDTO.getCode())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setChangeContractNum(salesStructurePriceTTDTO.getTotalNum())
                .setAfterContractNum(salesStructurePriceTTDTO.getTotalNum());

        return tradeTicketEntity;
    }

    public TTTranferEntity convertToTTTransfer(SalesContractTTTransferDTO salesContractTTTransferDTO, Integer ttId) {
        TTTranferEntity ttTranferEntity = new TTTranferEntity();
        if (salesContractTTTransferDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractTTTransferDTO.getSourceContractId());
            BeanConvertUtils.copy(ttTranferEntity, contractEntity);
            // V1 本次手续费 Author:zengshl 2024-06-18 start
            ttTranferEntity.setThisFee(salesContractTTTransferDTO.getThisTimeFee());
            // V1 本次手续费 Author:zengshl 2024-06-18 end
            //查询供应商信息
            CustomerDTO supplier = null;
            if (null != salesContractTTTransferDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractTTTransferDTO.getSupplierId());
            }
            if (supplier != null) {
                ttTranferEntity.setSupplierId(supplier.getId());
                ttTranferEntity.setSupplierName(supplier.getName());

            }
            //获取商品信息
            ttTranferEntity.setGoodsId(contractEntity.getGoodsId());

            ttTranferEntity
                    .setContent(salesContractTTTransferDTO.getContent())
                    .setSourceContractId(salesContractTTTransferDTO.getSourceContractId())
                    .setContractId(salesContractTTTransferDTO.getSourceContractId())
                    .setNum(contractEntity.getContractNum())
                    .setId(null)
                    .setTtId(ttId)
            ;
        } else {
            BeanConvertUtils.copy(ttTranferEntity, salesContractTTTransferDTO);
            ttTranferEntity.setContractId(salesContractTTTransferDTO.getSonContractId());
            // add zengshl 本次手续费
            ttTranferEntity.setThisFee(salesContractTTTransferDTO.getThisTimeFee());
            //查询供应商信息
            CustomerDTO supplier = null;
            if (null != salesContractTTTransferDTO.getSupplierId()) {
                supplier = customerFacade.getCustomerById(salesContractTTTransferDTO.getSupplierId());
            }
            if (supplier != null) {
                ttTranferEntity.setSupplierId(supplier.getId());
                ttTranferEntity.setSupplierName(supplier.getName());

            }
            //获取商品信息
            ttTranferEntity.setGoodsId(salesContractTTTransferDTO.getGoodsId());
            ttTranferEntity
                    .setSourceContractId(salesContractTTTransferDTO.getSourceContractId())
                    .setModifyContent(salesContractTTTransferDTO.getModifyContent())
                    .setContent(salesContractTTTransferDTO.getContent())
                    .setTtId(ttId)
            ;
        }

        ttTranferEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttTranferEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        return ttTranferEntity;
    }

    /***********************CompareObject***********************/
    public List<CompareObjectDTO> getSplitCompareObjectDTO(SalesContractSplitTTDTO salesContractSplitTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        //salesContractSplitTTDTO.setSupplierId(contractEntity.getSupplierId());
        salesContractSplitTTDTO.setGoodsId(salesContractSplitTTDTO.getGoodsId());
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.compareFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO newDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(salesContractSplitTTDTO, newDTO);
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(newDTO.getSignDate());
        newDTO.setSignDate(signDate);
        newDTO.setUsage(salesContractSplitTTDTO.getUsage());
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(originalDTO, newDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }

    public List<CompareObjectDTO> getReviseCompareObjectDTOS(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        salesContractReviseTTDTO.setGoodsId(salesContractReviseTTDTO.getGoodsId());
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        manualList.remove("signDate");
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.compareFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractReviseTTDTO.getSignDate());
        salesContractReviseTTDTO.setSignDate(signDate);
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(originalDTO, salesContractReviseTTDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }


    public List<CompareObjectDTO> getContentObjectDTOS(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        //获取原合同属性
        ContractEntity contractEntity = contractService.getBasicContractById(contractId);

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);

        /*GoodsSpecDTO goodsSpecDTO = new GoodsSpecDTO()
                .setSupplierId(salesContractReviseTTDTO.getSupplierId())
                .setCategoryId(salesContractReviseTTDTO.getGoodsCategoryId())
                .setPackageId(salesContractReviseTTDTO.getGoodsPackageId())
                .setSpecId(salesContractReviseTTDTO.getGoodsSpecId());
        GoodsInfoVO goodsInfoVO = goodsFacade.acquireGoodsInfo(goodsSpecDTO);

        salesContractReviseTTDTO.setGoodsId(goodsInfoVO.getGoodsId());*/
        //对比价格字段
        PriceDetailBO originalPriceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, originalPriceDetailBO);
        List<String> manualList = getManualList();
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.getFields(originalPriceDetailBO, priceDetailBo, null, manualList);

        //对比获取修改字段
        SalesContractReviseTTDTO originalDTO = new SalesContractReviseTTDTO();
        BeanUtils.copyProperties(contractEntity, originalDTO);
        originalDTO.setWeightCheck(StringUtils.isBlank(contractEntity.getWeightCheck()) ? null : Integer.parseInt(contractEntity.getWeightCheck()));
        originalDTO.setOwnerId(String.valueOf(contractEntity.getOwnerId()));
        List<String> ignoreList = getIgnoreList();
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractReviseTTDTO.getSignDate());
        salesContractReviseTTDTO.setSignDate(signDate);
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.getFields(originalDTO, salesContractReviseTTDTO, ignoreList, manualList);
        compareObjectDTOList.addAll(compareDTOList);
        return compareObjectDTOList;
    }


    public List<CompareObjectDTO> getContentObjectDTOS(Integer ttType, ContractEntity originalContractEntity, ContractEntity contractEntity) {
//        List<String> manualList = getManualList();
        //对比价格字段
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(originalContractEntity.getId());
        List<CompareObjectDTO> compareDTOList = BeanCompareUtils.getFields(contractPriceEntity, contractPriceEntity, null, null);

        //对比获取修改字段
        List<String> ignoreList = getIgnoreList();
        originalContractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(originalContractEntity.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(contractEntity.getSignDate());
        contractEntity.setSignDate(signDate);
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.getFields(originalContractEntity, contractEntity, ignoreList, null);
        compareObjectDTOList.addAll(compareDTOList);

        if (TTTypeEnum.CLOSED.getType().equals(ttType)) {
            ContractEntity contractById = contractService.getContractById(contractEntity.getId());

            String ableBillNum = contractEntity.getContractNum().subtract(contractById.getTotalBillNum()).setScale(6, RoundingMode.HALF_UP).toPlainString();
            String ablePriceNum = contractEntity.getContractNum().subtract(contractById.getTotalPriceNum()).setScale(6, RoundingMode.HALF_UP).toPlainString();
            CompareObjectDTO compareObjectDTO = new CompareObjectDTO();
            compareObjectDTO.setName("ableBillNum")
                    .setBefore(ableBillNum)
                    .setAfter("")
                    .setSource("")
//                    .setSource(ModifySourceEnum.SYSTEM.getValue())
                    .setUpdateTime(DateTimeUtil.formatDateTimeString(new Date()));
            compareObjectDTOList.add(compareObjectDTO);

            CompareObjectDTO compareObjectDTO1 = new CompareObjectDTO();
            compareObjectDTO1.setName("ablePriceNum")
                    .setBefore(ablePriceNum)
                    .setAfter("")
                    .setSource("")
//                    .setSource(ModifySourceEnum.SYSTEM.getValue())
                    .setUpdateTime(DateTimeUtil.formatDateTimeString(new Date()));
            compareObjectDTOList.add(compareObjectDTO1);
        }


        return compareObjectDTOList;
    }


    public List<CompareObjectDTO> getTransferContentObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        //对比价格字段
        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
        BigDecimal newExtraPrice = BigDecimal.ZERO;
        if (salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())) {
            newExtraPrice = salesContractTTTransferDTO.getPrice();
        } else {
            newExtraPrice = contractPriceEntity.getExtraPrice().add(salesContractTTTransferDTO.getPrice());
        }
        newContractPriceEntity.setExtraPrice(newExtraPrice);

        List<String> manualList = getManualList();
        List<CompareObjectDTO> priceList = BeanCompareUtils.getFields(contractPriceEntity, newContractPriceEntity, null, manualList);
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        ContractEntity newContractEntity = new ContractEntity();
        BeanUtil.copyProperties(contractEntity, newContractEntity);
        BeanUtil.copyProperties(salesContractTTTransferDTO, newContractEntity);
        newContractEntity.setPriceEndType(salesContractTTTransferDTO.getPriceEndType());
        newContractEntity.setPriceEndTime(salesContractTTTransferDTO.getPriceEndTime());
        contractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(contractEntity.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(newContractEntity.getSignDate());
        newContractEntity.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        ignoreList.add("extraPrice");

        // BUGFIX：case-1003043 合同转月申请撤回后含税单价不正确 Author: Mr 2025-03-18 start
        ContractEntity compareContractEntity = BeanConvertUtils.map(ContractEntity.class, contractEntity);

        // 重新汇总原合同的含税单价 - (对于不生成子合同的合同，需要用使用原合同的含税单价)
        PriceDetailBO oldPriceDetail = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);

        // 含税单价
        ContractUnitPriceVO contractUnitPriceVO = contractPriceService.calcContractUnitPrice(oldPriceDetail, contractEntity.getTaxRate());
        BigDecimal totalAmount = contractUnitPriceVO.getUnitPrice().multiply(contractEntity.getContractNum());
        BigDecimal depositAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, totalAmount, BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));

        compareContractEntity
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setTotalAmount(totalAmount)
                .setDepositAmount(depositAmount);

        // 比较获取不同字段返回list，转换成json
        // List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(contractEntity, newContractEntity, ignoreList, manualList);
        List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(compareContractEntity, newContractEntity, ignoreList, manualList);
        // BUGFIX：case-1003043 合同转月申请撤回后含税单价不正确 Author: Mr 2025-03-18 End
        compareObjectDTOS.addAll(priceList);
        return compareObjectDTOS;
    }

    public List<CompareObjectDTO> getTransferCompareObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity) {
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        List<String> manualList = getManualList();
        SalesContractTTTransferDTO originalDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, originalDTO);
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractTTTransferDTO.getSignDate());
        salesContractTTTransferDTO.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        // 比较获取不同字段返回list，转换成json
        List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.compareFields(originalDTO, salesContractTTTransferDTO, ignoreList, manualList);
        return compareObjectDTOS;
    }

    public List<CompareObjectDTO> getCompareList(String modifyContent, TradeTicketEntity tradeTicketEntity) {
        List<CompareObjectDTO> compareObjectDTOList1 = JSON.parseArray(modifyContent, CompareObjectDTO.class);
        if (compareObjectDTOList1 == null) {
            return new ArrayList<>();
        }

        List<CompareObjectDTO> compareObjectDTOList = compareObjectDTOList1.stream().filter(i -> !i.getName().equalsIgnoreCase("protocolCode")).collect(Collectors.toList());
        CompareObjectDTO compareObjectDTO = new CompareObjectDTO();
        compareObjectDTO
                .setName("protocolCode")
                .setBefore("")
                .setAfter(tradeTicketEntity.getProtocolCode())
                .setSource("")
                .setUpdateTime("")
                .setSignId(String.valueOf(tradeTicketEntity.getSignId()));
        compareObjectDTOList.add(compareObjectDTO);
        List<CompareObjectDTO> siteCompareList = compareObjectDTOList.stream().filter(i -> "siteCode".equals(i.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(siteCompareList)) {
            List<CompareObjectDTO> factoryCompareList = compareObjectDTOList.stream().filter(i -> "deliveryFactoryCode".equals(i.getName())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(factoryCompareList)) {
                Integer companyId = tradeTicketEntity.getCompanyId();
                String beforeDeliveryFactoryCode = factoryCompareList.get(0).getBefore();
                String afterDeliveryFactoryCode = factoryCompareList.get(0).getAfter();
                CompareObjectDTO siteCodeCompareObjectDTO = new CompareObjectDTO()
                        .setName("siteCode")
                        .setBefore("")
                        .setAfter("")
                        .setSource("1")
                        .setUpdateTime("");
                compareObjectDTOList.add(compareObjectDTO);
                CompareObjectDTO siteNameCompareObjectDTO = new CompareObjectDTO()
                        .setName("siteName")
                        .setBefore("")
                        .setAfter("")
                        .setSource("1")
                        .setUpdateTime("");
                compareObjectDTOList.add(compareObjectDTO);
                if (StringUtils.isNotBlank(beforeDeliveryFactoryCode)) {
                    SiteEntity beforeSiteEntity = siteFacade.getSiteByCompanyIdAndFactoryCode(companyId, beforeDeliveryFactoryCode);
                    siteCodeCompareObjectDTO.setBefore(beforeSiteEntity.getCode());
                    siteNameCompareObjectDTO.setBefore(beforeSiteEntity.getName());
                }
                if (StringUtils.isNotBlank(afterDeliveryFactoryCode)) {
                    SiteEntity afterSiteEntity = siteFacade.getSiteByCompanyIdAndFactoryCode(companyId, afterDeliveryFactoryCode);
                    siteCodeCompareObjectDTO.setAfter(afterSiteEntity.getCode());
                    siteNameCompareObjectDTO.setAfter(afterSiteEntity.getName());
                }
                compareObjectDTOList.add(siteCodeCompareObjectDTO);
                compareObjectDTOList.add(siteNameCompareObjectDTO);
            }

        }
        return compareObjectDTOList.stream().map(i -> {
            if ("contractCode".equalsIgnoreCase(i.getName())) {
                i.setContractId(String.valueOf(tradeTicketEntity.getContractId()));
                i.setSourceContractId(String.valueOf(tradeTicketEntity.getSourceContractId()));
            }
            if ("paymentType".equalsIgnoreCase(i.getName())) {
                i.setBefore(PaymentTypeEnum.getByType(Integer.parseInt(i.getBefore())).getDesc());
                i.setAfter(PaymentTypeEnum.getByType(Integer.parseInt(i.getAfter())).getDesc());

            }

            if ("customerId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    CustomerDTO customerBefore = customerFacade.getCustomerById(Integer.parseInt(i.getBefore()));
                    if (customerBefore != null) {
                        i.setBefore(customerBefore.getName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    CustomerDTO customerAfter = customerFacade.getCustomerById(Integer.parseInt(i.getAfter()));
                    if (customerAfter != null) {
                        i.setAfter(customerAfter.getName());
                    }
                }

            }
            if ("goodsId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    SkuEntity goodsBefore = skuFacade.getSkuById(Integer.parseInt(i.getBefore()));
                    if (goodsBefore != null) {
                        i.setBefore(goodsBefore.getFullName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    SkuEntity goodsAfter = skuFacade.getSkuById(Integer.parseInt(i.getAfter()));
                    if (goodsAfter != null) {
                        i.setAfter(goodsAfter.getFullName());
                    }
                }
            }

            //发货库点
            if ("shipWarehouseId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(factoryConvertValue(Integer.parseInt(i.getBefore())));
                }
                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(factoryConvertValue(Integer.parseInt(i.getAfter())));
                }
            }

            //目的地,带皮扣重,重量检验
            if ("packageWeight".equalsIgnoreCase(i.getName())
                    || "weightCheck".equalsIgnoreCase(i.getName())
                    || "destination".equalsIgnoreCase(i.getName())
            ) {
                resetSystemValue(i);
            }
            //企标文件(特种油脂-二级)
            if ("standardFileId".equalsIgnoreCase(i.getName())
            ) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    SystemRuleItemEntity standardFileItemBefore = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getBefore()));
                    if (standardFileItemBefore != null) {
                        i.setBefore(standardFileItemBefore.getRuleKey());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    SystemRuleItemEntity standardFileItemAfter = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getAfter()));
                    if (standardFileItemAfter != null) {
                        i.setAfter(standardFileItemAfter.getRuleKey());
                    }
                }
            }
            //交提货方式
            if ("deliveryType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(Integer.parseInt(i.getBefore()));
                    if (deliveryTypeEntity != null) {
                        i.setBefore(deliveryTypeEntity.getName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(Integer.parseInt(i.getAfter()));
                    if (deliveryTypeEntity != null) {
                        i.setAfter(deliveryTypeEntity.getName());
                    }
                }
            }
            //代加工
            if ("oem".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    if ("0".equals(i.getBefore())) {
                        i.setBefore("否");
                    } else {
                        i.setBefore("是");
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    if ("0".equals(i.getAfter())) {
                        i.setAfter("否");
                    } else {
                        i.setAfter("是");
                    }
                }
            }

            //包装是否计算重量
            if ("needPackageWeight".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    if ("0".equals(i.getBefore())) {
                        i.setBefore("否");
                    } else {
                        i.setBefore("是");
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    if ("0".equals(i.getAfter())) {
                        i.setAfter("否");
                    } else {
                        i.setAfter("是");
                    }
                }
            }
            //卖方主体收款账号信息
            if ("supplierAccountId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(Integer.parseInt(i.getBefore()));
                    i.setBefore(customerBankEntity != null ? customerBankEntity.getBankAccountNo() : null);
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(Integer.parseInt(i.getAfter()));
                    i.setAfter(customerBankEntity != null ? customerBankEntity.getBankAccountNo() : null);
                }
            }
            //合同类型
            if ("contractType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(ContractTypeEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(ContractTypeEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            //交易类型
            if ("tradeType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(ContractTradeTypeEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(ContractTradeTypeEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            //履约保证金释放方式
            if ("depositReleaseType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(DepositUseRuleEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }
                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(DepositUseRuleEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            // 付款条件代码
            if ("payConditionId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    Result result = payConditionFacade.getPayConditionById(Integer.parseInt(i.getBefore()));
                    if (null != result && ResultCodeEnum.OK.getCode() == result.getCode()) {
                        PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(result.getData()), PayConditionEntity.class);
                        i.setBefore(payConditionEntity.getCode());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    Result result = payConditionFacade.getPayConditionById(Integer.parseInt(i.getAfter()));
                    if (null != result && ResultCodeEnum.OK.getCode() == result.getCode()) {
                        PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(result.getData()), PayConditionEntity.class);
                        i.setAfter(payConditionEntity.getCode());
                    }
                }
            }

            //用途
            if ("usage".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(UsageEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }
                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(UsageEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            if (String.valueOf(i.getBefore()).equalsIgnoreCase(String.valueOf(i.getAfter()))) {
                i.setAfter("");
                i.setSource("");
                i.setUpdateTime("");
            }

            return i;
        }).filter(i -> !getHideList().contains(i.getName())).collect(Collectors.toList());
    }

    private void resetSystemValue(CompareObjectDTO i) {
        if (StringUtils.isNotBlank(i.getBefore())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getBefore()));
            if (systemRuleItemEntity != null) {
                i.setBefore(systemRuleItemEntity.getRuleKey());
            }
        }

        if (StringUtils.isNotBlank(i.getAfter())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getAfter()));
            if (systemRuleItemEntity != null) {
                i.setAfter(systemRuleItemEntity.getRuleKey());
            }
        }
    }


    public List<String> getManualList() {
        List<String> manualList = new ArrayList<>();
        Field[] fields = SalesContractReviseTTDTO.class.getDeclaredFields();
        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
        manualList.remove("fobUnitPrice");
        manualList.remove("taxRate");
        manualList.remove("cifUnitPrice");
        manualList.remove("totalAmount");
        manualList.remove("unitPrice");
        return manualList;
    }

    public List<String> getIgnoreList() {
        List<String> ignoreList = new ArrayList<>();
//        ignoreList.add("tradeType");
        ignoreList.add("salesType");
        ignoreList.add("contractSource");
        ignoreList.add("contractSignatureStatus");
        ignoreList.add("status");
        ignoreList.add("approvalStatus");
        ignoreList.add("approvalType");
        ignoreList.add("code");
        ignoreList.add("rootContractId");

        ignoreList.add("modifyContent");
        ignoreList.add("contractId");

        ignoreList.add("diffPrice");
        ignoreList.add("type");
        ignoreList.add("tempPrice");
        ignoreList.add("originalDomainCode");

        ignoreList.add("sourceContractId");
        return ignoreList;
    }

    public List<String> getHideList() {
        List<String> hideList = new ArrayList<>();
//        Field[] fields = PriceDetailBO.class.getDeclaredFields();
//        Arrays.stream(fields).forEach(i -> hideList.add(i.getName()));
//        hideList.add("goodsPackageId");
//        hideList.add("goodsSpecId");
//        hideList.add("goodsCategoryId");
//        hideList.add("num");
//        hideList.add("supplierAccountId");
//        hideList.add("price");
//        hideList.add("ownerId");
//        hideList.add("priceEndType");
//        hideList.add("sourceContractId");
//        hideList.add("customerId");
//        hideList.add("customerCode");
        return hideList;
    }

    public TTModifyEntity convertToReviseTtModify(TTDTO ttDto, Integer ttId, Integer type) {
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        PriceDetailBO priceDetailBo = ttDto.getPriceDetailBO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getContractId());
        if (salesContractReviseTTDTO.getAddedSignatureType() == -1) {
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractReviseTTDTO.getSourceContractId());
            BeanUtils.copyProperties(salesContractReviseTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(contractPriceEntity, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(contractEntity.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setContractCode(contractEntity.getContractCode())
                    .setRootContractId(contractEntity.getRootId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setNewContractNum(salesContractReviseTTDTO.getContractNum())
                    .setTradeType(salesContractReviseTTDTO.getTradeType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setSupplierAccount(contractEntity.getSupplierAccount())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        } else {
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(salesContractReviseTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(priceDetailBo, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setType(type)
                    .setContractId(salesContractReviseTTDTO.getContractId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setContractCode(salesContractReviseTTDTO.getContractCode())
                    .setGoodsName(skuFacade.getSkuById(salesContractReviseTTDTO.getGoodsId()).getFullName())
                    .setModifyContent(salesContractReviseTTDTO.getModifyContent())
                    .setContent(salesContractReviseTTDTO.getContent())
                    .setWeightCheck(String.valueOf(salesContractReviseTTDTO.getWeightCheck()))
                    .setBaseDiffPrice(contractEntity.getBaseDiffPrice())
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setDeliveryFactoryCode(salesContractReviseTTDTO.getDeliveryFactoryCode())
                    .setSupplierAccount(salesContractReviseTTDTO.getSupplierAccount())
                    .setDeliveryFactoryName(salesContractReviseTTDTO.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        }
        ttModifyEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttModifyEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        return ttModifyEntity;
    }


    public TTModifyEntity convertToSplitTtModify(TTDTO ttDto, Integer ttId, Integer type) {
        SalesContractSplitTTDTO salesContractSplitTTDTO = ttDto.getSalesContractSplitTTDTO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        if (salesContractSplitTTDTO.getAddedSignatureType() == 1 || salesContractSplitTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractSplitTTDTO.getSourceContractId());
            BeanUtils.copyProperties(salesContractSplitTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(contractPriceEntity, ttModifyEntity);
            BigDecimal newContractNum = null;
            if (contractEntity.getContractNum() != null && salesContractSplitTTDTO.getContractNum() != null) {
                newContractNum = contractEntity.getContractNum().subtract(salesContractSplitTTDTO.getContractNum());
            }
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(contractEntity.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setContractCode(contractEntity.getContractCode())
                    .setRootContractId(contractEntity.getRootId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setNewContractNum(newContractNum)
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setSupplierAccount(contractEntity.getSupplierAccount())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        } else {
            //子合同
            Integer sonContractId = salesContractSplitTTDTO.getSonContractId();
            if (ttDto.getSubmitType().equals(SubmitTypeEnum.SAVE.getValue())) {
                sonContractId = salesContractSplitTTDTO.getSourceContractId();
            }
            PriceDetailBO priceDetailBo = ttDto.getPriceDetailBO();
            ContractEntity contractEntity = contractService.getBasicContractById(sonContractId);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(salesContractSplitTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(priceDetailBo, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(salesContractSplitTTDTO.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(sonContractId)
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setContractCode(salesContractSplitTTDTO.getContractCode())
                    .setRootContractId(salesContractSplitTTDTO.getRootContractId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setDeliveryFactoryCode(salesContractSplitTTDTO.getDeliveryFactoryCode())
                    .setSupplierAccount(salesContractSplitTTDTO.getSupplierAccount())
                    .setDeliveryFactoryName(salesContractSplitTTDTO.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        }
        ttModifyEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttModifyEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        return ttModifyEntity;
    }


    public TTDetailVO getPriceTTDetailVO(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTPriceEntity ttPriceEntity = ttPriceDao.getTTPriceEntityByTTId(ttId);

        TTDetailVO ttDetailVO = new TTDetailVO();
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();

        ContractEntity contractEntity = contractService.getBasicContractById(tradeTicketEntity.getContractId());

        //ContractSignEntity contractSignEntity = contractSignQueryService.getContractSignDetailById(tradeTicketEntity.getSignId());

        //计算定价单单价
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        BigDecimal sourcePrice = BigDecimal.ZERO;
        BigDecimal source = BigDecimal.ZERO;

        ttDetailVO.setContractId(contractEntity.getId())
                .setContractCode(contractEntity.getContractCode())
                .setTtId(tradeTicketEntity.getId())
                .setTtCode(tradeTicketEntity.getCode())
                .setPriceApplyTime(tradeTicketEntity.getCreatedAt())
                .setContractSignId(tradeTicketEntity.getSignId())
                .setContractSignCode(tradeTicketEntity.getProtocolCode());

        if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() && TTTypeEnum.PRICE.getType().equals(tradeTicketEntity.getType())) {
            PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(String.valueOf(ttPriceEntity.getAllocateId()));
            ttDetailVO.setPriceApplyCode(priceAllocateEntity.getPriceApplyCode())
                    .setPriceApplyId(priceAllocateEntity.getPriceApplyId());
        }

        if (null != ttPriceEntity.getContractPriceDetail()) {
            contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
            sourcePrice = ttPriceEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            source = ttPriceEntity.getTransactionPrice().add(sourcePrice);
        }

        //定价单含税总额
        BigDecimal applyTaxUnitPrice = source.multiply(ttPriceEntity.getOriginalPriceNum());
        //定价单不含税总额
        BigDecimal applyNotTaxUnitPrice = applyTaxUnitPrice.divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        //定价单增值税总额
        BigDecimal ApplyAddedTaxAllPrice = applyTaxUnitPrice.subtract(applyNotTaxUnitPrice);

        Integer priceComplete = BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO) ? PriceCompleteEnum.PRICE_COMPLETE.getType() : PriceCompleteEnum.NOT_PRICE_COMPLETE.getType();
        // TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        // TT编号 定价数量 定价价格 创建时间 取点价分配单上的点价申请时间
        ttPriceDetailVO
                .setCode(tradeTicketEntity.getCode())
                .setNum(ttPriceEntity.getNum())
                .setPrice(ttPriceEntity.getPrice())
                .setCreateTime(ttPriceEntity.getCreatedAt())
                .setSignDate(contractEntity.getSignDate())
                .setCreatedAt(tradeTicketEntity.getCreatedAt())
                .setPriceApplyTime(ttPriceEntity.getPriceTime())
                .setCustomerName(ttPriceEntity.getCustomerName())
                .setThisTimePriceNum(ttPriceEntity.getOriginalPriceNum())
                .setSupplierName(ttPriceEntity.getSupplierName())
                .setDomainCode(contractEntity.getDomainCode())
                .setGoodsId(contractEntity.getGoodsName())
                .setContractNum(ttPriceEntity.getThisContractNum())
                .setCategoryName(GoodsCategoryEnum.getDescByValue(ttPriceEntity.getGoodsCategoryId()))
                .setTotalPriceNum(null != ttPriceEntity.getTotalPriceNum() ? ttPriceEntity.getTotalPriceNum() : null)
                .setApplyUnitPrice(source)
                .setApplyTaxUnitPrice(applyTaxUnitPrice)
                .setApplyNotTaxUnitPrice(applyNotTaxUnitPrice)
                .setApplyAddedTaxAllPrice(ApplyAddedTaxAllPrice)
                .setAvePrice(ttPriceEntity.getAvePrice())
                .setRemainPriceNum(ttPriceEntity.getRemainPriceNum())
                .setPriceEndTime(StrUtil.isNotBlank(ttPriceEntity.getPriceEndTime()) ? ContractPriceEndTypeEnum.DATE.getValue() == ttPriceEntity.getPriceEndType() ? DateTimeUtil.formatDateString(DateTimeUtil.parseDateString(ttPriceEntity.getPriceEndTime())) : ttPriceEntity.getPriceEndTime() : null)
                .setPriceComplete(priceComplete)
                .setContractPriceEntity(contractPriceEntity);

        //定价完成
        if (priceComplete == PriceCompleteEnum.PRICE_COMPLETE.getType() && null != ttPriceEntity.getEndContractPrice()) {
            //最终合同价格
            ttPriceDetailVO.setEndContractPrice(ttPriceEntity.getEndContractPrice());
            //最终全额货款
            ttPriceDetailVO.setEndAllPrice(ttPriceEntity.getEndAllPrice());
            //增值税不含税总金额
            BigDecimal endNotTaxAllPrice = ttPriceDetailVO.getEndAllPrice().divide(contractEntity.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setEndNotTaxAllPrice(endNotTaxAllPrice);
            //最终增值税总金额
            ttPriceDetailVO.setEndAddedTaxAllPrice(ttPriceDetailVO.getEndAllPrice().subtract(endNotTaxAllPrice));

        }
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);
        ttDetailVO.setDetailType("2");

        return ttDetailVO;
    }

    public TTDetailVO getWashoutTTDetailVO(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();
        Integer contractId = ttAddEntity.getContractId();
        ContractEntity contractEntity = null;
        ContractPriceEntity originalContractPriceEntity = null;
        if (null != contractId) {
            contractEntity = contractService.getBasicContractById(contractId);
            originalContractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        }
        // 合同查询详情
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
        //合同编号
        BigDecimal washoutUnitPrice = ttAddEntity.getWashoutUnitPrice();
        BigDecimal unitPrice = contractEntity.getUnitPrice();
        String domainCode = contractEntity.getDomainCode();
        ttPriceDetailVO.setDomainCode(domainCode);
        BigDecimal washoutCalPrice = BigDecimal.ZERO;
        StringBuilder washoutPrice = new StringBuilder();
        String pre = "";
        String unitPriceString = "";
        //取消数量:  ttPriceDetailVO.num
        //合同价格: ttPriceDetailVO.unitPrice
        //市场价格: ttPriceDetailVO.washoutPrice
        //总金额 totalPrice
        //差价: ttPriceDetailVO.buyBackPrice
        //定价量
        if (washoutUnitPrice != null) {
            BigDecimal totalPriceNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
            BigDecimal sourceContractNum = ttAddEntity.getSourceContractNum() == null ? BigDecimal.ZERO : ttAddEntity.getSourceContractNum();
            if (ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType()
                    || (sourceContractNum.compareTo(totalPriceNum) <= 0)) {
                washoutPrice.append(washoutUnitPrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                washoutCalPrice = washoutUnitPrice;
            } else {
                //BigDecimal tempPrice = washoutUnitPrice.subtract(contractPriceEntity.getForwardPrice()).setScale(2, RoundingMode.HALF_UP);
                //String symbol = GoodsCategoryEnum.getByValue(contractEntity.getGoodsCategoryId()).getLkgFutureSymbol();
                //washoutPrice = symbol + contractEntity.getDomainCode() + tempPrice;
                //washoutCalPrice = tempPrice;
                String symbol = contractEntity.getFutureCode();
                pre = symbol + domainCode;
                washoutPrice.append(pre);
                if (washoutUnitPrice.compareTo(BigDecimal.ZERO) >= 0) {
                    washoutPrice.append("+");
                }
                washoutPrice.append(washoutUnitPrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                washoutCalPrice = washoutUnitPrice;
            }
        }

        ttPriceDetailVO.setWashoutPrice(String.valueOf(washoutPrice));
        if ((unitPrice != null
                && contractEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()
                && originalContractPriceEntity != null)
                || (unitPrice != null
                && contractEntity.getContractType() == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()
                && contractEntity.getTotalPriceNum().compareTo(BigDecimal.ZERO) == 0)) {
            unitPrice = unitPrice.subtract(originalContractPriceEntity.getForwardPrice());
        }
        if (StringUtils.isNotBlank(pre)) {
            if (unitPrice.compareTo(BigDecimal.ZERO) >= 0) {
                unitPriceString = pre + "+" + unitPrice.stripTrailingZeros().toPlainString();
            } else {
                unitPriceString = pre + unitPrice.stripTrailingZeros().toPlainString();
            }
        } else {
            unitPriceString = unitPrice.stripTrailingZeros().toPlainString();
        }
        ttPriceDetailVO.setUnitPriceString(unitPriceString);
        ttPriceDetailVO.setUnitPrice(unitPrice);
        if (null != unitPrice && null != washoutUnitPrice) {
            //解约定赔
            BigDecimal buyBackPrice = unitPrice.subtract(washoutCalPrice).abs().setScale(2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setBuyBackPrice(buyBackPrice);
            BigDecimal totalPrice = buyBackPrice.multiply(ttAddEntity.getContractNum()).setScale(2, RoundingMode.HALF_UP);
            ttPriceDetailVO.setTotalPrice(totalPrice);
        }
        if (StringUtils.isNotBlank(ttAddEntity.getWashoutPriceDetail())) {
            PriceDetailBO priceDetailBO = JSON.parseObject(ttAddEntity.getWashoutPriceDetail(), PriceDetailBO.class);
            ttPriceDetailVO.setForwardPrice(priceDetailBO.getForwardPrice());
            ttPriceDetailVO.setExtraPrice(priceDetailBO.getExtraPrice());
        }
        if (null != ttAddEntity.getSupplierId()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
            ttPriceDetailVO.setSupplierName(null != customerDTO ? customerDTO.getName() : null);
        }
        if (null != ttAddEntity.getCustomerId()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getCustomerId());
            ttPriceDetailVO.setCustomerName(null != customerDTO ? customerDTO.getName() : null);
        }


        ttPriceDetailVO
                //合同编号
                .setContractCode(tradeTicketEntity.getContractCode())
                //合同id
                .setContractId(tradeTicketEntity.getContractId())
                //TT编号
                .setCode(tradeTicketEntity.getCode())
                //品种
                .setCategoryName(GoodsCategoryEnum.getByValue(ttAddEntity.getGoodsCategoryId()).getDesc())
                //合同类型
                .setContractType(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
                //含税单价
//                .setPrice(contractEntity.getUnitPrice())
                .setPrice(washoutUnitPrice)
                //解约定赔数量
                .setNum(ttAddEntity.getContractNum())
                //创建时间
                .setCreateTime(ttAddEntity.getCreatedAt())
                //协议id
                .setSignId(tradeTicketEntity.getSignId())
                //协议编号
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setSignDate(ttAddEntity.getSignDate())
        ;


        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttAddEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);

        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttAddEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttAddEntity.getSupplierId()));
        }
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        CustomerDTO customerDTO = customerFacade.getCustomerById(ttAddEntity.getSupplierId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        //买家
        if (null != ttAddEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttAddEntity.getCustomerId()));
        }

        //商品信息
        if (null != ttAddEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttAddEntity.getGoodsCategoryId()));
        }
        if (null != ttAddEntity.getGoodsPackageId()) {
            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttAddEntity.getGoodsPackageId()));
        }
        if (null != ttAddEntity.getGoodsSpecId()) {
            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttAddEntity.getGoodsSpecId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttAddEntity.getDepositAmount()) {
            int depositAmountStatus = ttAddEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttAddEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttAddEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttAddEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttAddEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        ttQueryDetailVO.setDepositRate(ttAddEntity.getDepositRate());

        //查询工厂信息
        ttQueryDetailVO = setShipWarehouse(ttQueryDetailVO, ttAddEntity.getShipWarehouseId());

        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);


        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        //原合同信息
        ttQueryDetailVO.setRootContractId(ttAddEntity.getContractId());
        String contractCode = contractEntity.getContractCode();
        ttQueryDetailVO.setRootContractCode(contractCode);
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ttDetailVO.setDetailType("3");
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);

        ContractEntity originalContractEntity = contractService.getBasicContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));

        String modifyContent = ttAddEntity.getContent();
        List<CompareObjectDTO> list = getCompareList(modifyContent, tradeTicketEntity);
        ttDetailVO.setCompareObjectDTOList(list);

        return ttDetailVO;
    }

    public TTDetailVO getClosedTTDetailVO(Integer ttId, TradeTicketEntity tradeTicketEntity, String detailType) {
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        TTPriceDetailVO ttPriceDetailVO = new TTPriceDetailVO();
        ttPriceDetailVO
                //合同编号
                .setContractCode(tradeTicketEntity.getContractCode())
                //合同id
                .setContractId(tradeTicketEntity.getContractId())
                //TT编号
                .setCode(tradeTicketEntity.getCode())
                //品种
                .setCategoryName(GoodsCategoryEnum.getByValue(ttAddEntity.getGoodsCategoryId()).getDesc())
                //合同类型
                .setContractType(ContractTypeEnum.getDescByValue(tradeTicketEntity.getContractType()))
                //关闭数量
                .setNum(ttAddEntity.getContractNum())
                //创建时间
                .setCreateTime(ttAddEntity.getCreatedAt())
                .setSignId(tradeTicketEntity.getSignId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
        ;
        ttDetailVO.setDetailType(detailType);
        ttDetailVO.setTtPriceDetailVO(ttPriceDetailVO);

        String modifyContent = ttAddEntity.getContent();
        List<CompareObjectDTO> list = getCompareList(modifyContent, tradeTicketEntity);
        ttDetailVO.setCompareObjectDTOList(list);


        ContractEntity originalContractEntity = contractService.getContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        return ttDetailVO;
    }

    public TTQueryDetailVO setShipWarehouse(TTQueryDetailVO ttQueryDetailVO, Integer shipWarehouseId) {
        if (ObjectUtil.isNotEmpty(shipWarehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(shipWarehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                ttQueryDetailVO.setShipWarehouseId(String.valueOf(shipWarehouseId));
                ttQueryDetailVO.setShipWarehouseName(ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "");
            }
        }
        return ttQueryDetailVO;
    }

    /**
     * 交提货方式：dba_factory_warehouse
     * 值转对象 add by zengshl
     *
     * @param warehouseId
     * @return
     */
    public String factoryConvertValue(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }


    public ContractPriceEntity saveTransferContractPrice(TTDTO ttDto, Integer ttId) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttDto.getSalesContractTTTransferDTO();
//        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractTTTransferDTO.getContractId());
        PriceDetailBO priceDetailBO = ttDto.getPriceDetailBO();
        //1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息) Date:20241023 By:NanaHou
//        if (!salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())) {
        ContractPriceEntity sonContractPriceEntity = new ContractPriceEntity();
        BeanUtil.copyProperties(priceDetailBO, sonContractPriceEntity);
        sonContractPriceEntity
                .setId(null)
                .setTtId(ttId)
                .setTtCode(StringUtils.isNotBlank(salesContractTTTransferDTO.getCode()) ? salesContractTTTransferDTO.getCode() : null)
                .setContractId(salesContractTTTransferDTO.getSonContractId())
                .setContractCode(salesContractTTTransferDTO.getContractCode())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
        ;
        if (null == sonContractPriceEntity.getContractId() || 0 == sonContractPriceEntity.getContractId()) {
            sonContractPriceEntity
                    .setContractId(salesContractTTTransferDTO.getSourceContractId())
            ;
        }
        contractPriceService.save(sonContractPriceEntity);
        if (!salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.TRANSFER_MONTH.getValue())) {
            ttDto.getSalesContractTTTransferDTO().setContractId(salesContractTTTransferDTO.getSonContractId());
        }
        return sonContractPriceEntity;
    }

    public void saveAddContractPrice(TTDTO ttdto, Integer ttId) {
        //保存合同价格信息
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        ContractPriceEntity contractPriceEntity = BeanConvertUtils.map(ContractPriceEntity.class, ttdto.getPriceDetailBO());

        contractPriceEntity.setTtCode(salesContractAddTTDTO.getCode());
        contractPriceEntity.setTtId(ttId);
        contractPriceEntity.setContractId(salesContractAddTTDTO.getContractId());
        contractPriceEntity.setContractCode(salesContractAddTTDTO.getContractCode());
        if (!salesContractAddTTDTO.getCreateStatus()) {
            Integer contractPriceId = contractPriceService.getContractPriceEntityByTTId(salesContractAddTTDTO.getTtId()).getId();
            contractPriceEntity.setId(contractPriceId);
        }
        contractPriceService.saveOrUpdate(contractPriceEntity);
    }

    public void prepareData(TradeTicketEntity tradeTicketEntity, TTAddEntity ttAddEntity, ContractPriceEntity contractPriceEntity, TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();
        BeanUtils.copyProperties(ttAddEntity, salesContractAddTTDTO);
        salesContractAddTTDTO.setGoodsCategoryId(tradeTicketEntity.getSubGoodsCategoryId());
        salesContractAddTTDTO.setContractNum(ttAddEntity.getContractNum().toString());
        salesContractAddTTDTO.setDeliveryStartTime(ttAddEntity.getDeliveryStartTime());
        salesContractAddTTDTO.setDeliveryEndTime(ttAddEntity.getDeliveryEndTime());
        salesContractAddTTDTO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        salesContractAddTTDTO.setSignDate(ttAddEntity.getSignDate());
        salesContractAddTTDTO.setForwardPrice(ttAddEntity.getForwardPrice());
        PriceDetailBO priceDetailBO = new PriceDetailBO();
        BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
        BigDecimal price = contractPriceService.calculatePriceBo(priceDetailBO);
        String unitPrice = price.setScale(6, RoundingMode.HALF_UP).toPlainString();
        salesContractAddTTDTO.setUnitPrice(unitPrice);
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
    }

    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId) {
        TTModifyEntity ttModifyEntity = ttModifyDao.getByTTId(ttId);
        List<CompareObjectDTO> contentObjectDTOS = getContentObjectDTOS(null, originalContractEntity, contractEntity);
        String content = JSON.toJSONString(contentObjectDTOS);
        ttModifyEntity.setContent(content);
        ttModifyDao.saveOrUpdate(ttModifyEntity);
    }

    public void updateAddContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {
        TTAddEntity ttAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
        List<CompareObjectDTO> contentObjectDTOS = getContentObjectDTOS(ttType, originalContractEntity, contractEntity);
        String content = JSON.toJSONString(contentObjectDTOS);
        ttAddEntity.setContent(content);
        ttAddDao.saveOrUpdate(ttAddEntity);
    }

    public List<CompareObjectDTO> getSplitCompareList(List<CompareObjectDTO> compareList, TradeTicketEntity tradeTicketEntity, TTModifyEntity ttModifyEntity) {
        TTModifyEntity newTTModifyEntity = ttModifyDao.getModifyByRelationTTId(ttModifyEntity.getRelationId(), tradeTicketEntity.getId());

        if (newTTModifyEntity == null) {
            return null;
        }

        List<CompareObjectDTO> list = new ArrayList<>(compareList);
        CompareObjectDTO compareObjectDTO = new CompareObjectDTO();
        compareObjectDTO
                .setName("newContractCode")
                .setBefore("")
                .setAfter(newTTModifyEntity.getContractCode())
                .setSource("")
                .setUpdateTime("")
                .setContractId(String.valueOf(tradeTicketEntity.getContractId()));
        list.add(compareObjectDTO);

        CompareObjectDTO compareObjectDTO2 = new CompareObjectDTO();
        compareObjectDTO2
                .setName("newContractNum")
                .setBefore("")
                .setAfter(newTTModifyEntity.getContractNum().setScale(3, BigDecimal.ROUND_HALF_UP).toPlainString())
                .setSource("")
                .setUpdateTime("")
                .setContractId("");
        list.add(compareObjectDTO2);

        CompareObjectDTO compareObjectDTO1 = new CompareObjectDTO();
        if (tradeTicketEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            compareObjectDTO1
                    .setName("newCustomerName")
                    .setBefore("")
                    .setAfter(newTTModifyEntity.getCustomerName())
                    .setSource("")
                    .setUpdateTime("");
        } else {
            compareObjectDTO1
                    .setName("newCustomerName")
                    .setBefore("")
                    .setAfter(newTTModifyEntity.getSupplierName())
                    .setSource("")
                    .setUpdateTime("");
        }
        list.add(compareObjectDTO1);

        return list;
    }

    public List<CompareObjectDTO> getReviseCompareList(String modifyContent, TradeTicketEntity tradeTicketEntity, TTTranferEntity ttTranferEntity) {
        List<CompareObjectDTO> compareList = getCompareList(modifyContent, tradeTicketEntity);

        CompareObjectDTO compareObjectDTO = new CompareObjectDTO();

        if (tradeTicketEntity.getTradeType() == ContractTradeTypeEnum.TRANSFER_ALL.getValue()) {
            compareObjectDTO
                    .setName("newDiffPrice")
                    .setBefore("")
                    .setAfter(ttTranferEntity.getPrice().toString())
                    .setSource("1")
                    .setUpdateTime("");
        }

        compareList.add(compareObjectDTO);
        return compareList;
    }

}
