package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTranferTypeEnum;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <p>
 * 创建合同的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
public abstract class BaseContractCreateAbstractService extends BaseContractAbstractService {
    /**
     * 变更创建合同
     *
     * @param contractModifyDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createContractByModify(ContractModifyDTO contractModifyDTO) {
        ContractEntity contractEntity = new ContractEntity();

        // 补充基本信息
        buildModifyBaseInfo(contractModifyDTO, contractEntity);
        // 补充业务信息
        buildModifyBizInfo(contractModifyDTO, contractEntity);
        // 创建合同
        createByModify(contractModifyDTO, contractEntity);
        // 后续操作
        afterModifyProcess(contractModifyDTO, contractEntity);

        return contractEntity;
    }

    /**
     * 根据原合同创建子合同
     *
     * @param contractTransferDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEntity createSonContract(ContractTransferDTO contractTransferDTO) {

        ContractEntity sonContract = contractTransferDTO.getContractEntity();

        // 获取客户属性
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO
                .setCategory2(String.valueOf(sonContract.getCategory2()))
                .setCategory3(String.valueOf(sonContract.getCategory3()))
                .setCustomerId(sonContract.getCustomerId());
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

        if (!customerDetailEntityList.isEmpty()) {
            // 普通用户考虑超远期合同
            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            if (customerDetailEntity.getIsWhiteList() == 0) {
                int totalTransferTimes;
                if (sonContract.getIsOverForward() == 1) {
                    totalTransferTimes = 2;
                } else {
                    totalTransferTimes = 1;
                }
                sonContract.setAbleTransferTimes(Math.max(totalTransferTimes - sonContract.getTransferredTimes(), 0))
                        .setTotalTransferTimes(totalTransferTimes);
            }

            int totalReversePriceTimes = 0;
            if (customerDetailEntity.getIsReversePrice() == 1) {
                totalReversePriceTimes = 1;
            }
            sonContract.setAbleReversePriceTimes(Math.max(totalReversePriceTimes - sonContract.getReversedPriceTimes(), 0))
                    .setTotalReversePriceTimes(totalReversePriceTimes);
        }

        if (StringUtils.isNotBlank(sonContract.getDeliveryFactoryCode())) {
            FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(sonContract.getDeliveryFactoryCode());
            if (factoryEntity != null) {
                CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), sonContract.getCompanyId());
                sonContract.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
            }
        }

        // 处理基本数据
        String sonContractCode = contractService.genSonContractCode(sonContract.getContractCode(), sonContract.getSalesType());
        sonContract.setId(null)
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setContractType(ContractTypeEnum.JI_CHA.getValue())
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setSignDate(sonContract.getSignDate())
        ;

        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())) {
            sonContract.setContractSource(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())
                    .setDomainCode(contractTransferDTO.getDomainCode())
                    .setTradeType(ContractTradeTypeEnum.TRANSFER_PART.getValue())
                    .setOrderNum(contractTransferDTO.getTransferNum())
                    .setContractNum(contractTransferDTO.getTransferNum())
                    .setTotalDeliveryNum(BigDecimal.ZERO)
                    .setTotalPriceNum(BigDecimal.ZERO)
                    .setTotalModifyNum(BigDecimal.ZERO)
                    .setTotalTransferNum(BigDecimal.ZERO);
        }

        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue())) {
            sonContract.setContractSource(ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue())
                    .setTradeType(ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue())
                    .setDomainCode(contractTransferDTO.getDomainCode())
                    .setOrderNum(contractTransferDTO.getReversePricingNum())
                    .setContractNum(contractTransferDTO.getReversePricingNum())
                    .setTotalDeliveryNum(BigDecimal.ZERO)
                    .setTotalPriceNum(BigDecimal.ZERO)
                    .setTotalModifyNum(BigDecimal.ZERO)
                    .setTotalTransferNum(BigDecimal.ZERO);
        }

        if (contractTransferDTO.getTtTranferType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())) {
            sonContract.setContractSource(ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue())
                    .setTradeType(ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue())
                    .setDomainCode(contractTransferDTO.getDomainCode())
                    .setOrderNum(contractTransferDTO.getReversePricingNum())
                    .setContractNum(contractTransferDTO.getReversePricingNum())
                    .setTotalDeliveryNum(BigDecimal.ZERO)
                    .setTotalPriceNum(BigDecimal.ZERO)
                    .setTotalModifyNum(BigDecimal.ZERO)
                    .setTotalTransferNum(BigDecimal.ZERO);
        }

        // 签订总金额
        sonContract.setOrderAmount(sonContract.getTotalAmount());
        // 合同来源
        sonContract.setCreateSource(SystemEnum.MAGELLAN.getValue());
        // 批次
        sonContract.setCreateBatch(null);

        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(sonContract.getCategory2())
                .setContractType(sonContract.getContractType())
                .setDepositRate(sonContract.getDepositRate())
                .setAddedDepositRate(sonContract.getAddedDepositRate());
        sonContract.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-转月、反点价 Author: Mr 2024-04-26 Start
        updateCustomerInfo(sonContract.getCustomerId(),
                sonContract.getSupplierId(),
                sonContract.getCustomerId(),
                sonContract.getSupplierId(),
                sonContract);
        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-转月、反点价 Author: Mr 2024-04-26 End

        try {
            contractDao.save(sonContract);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", sonContract.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }

        return sonContract;
    }

    //===================================================合同变更===================================================

    /**
     * 补充基本信息
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected void buildModifyBaseInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        ContractEntity parentContractEntity = contractModifyDTO.getParentContractEntity();

        BeanUtils.copyProperties(parentContractEntity, contractEntity);
        BeanUtils.copyProperties(contractModifyDTO, contractEntity);

        // 处理交易类型
        int tradeType;
        if (contractModifyDTO.getContractSource() == ContractActionEnum.REVISE_CUSTOMER.getActionValue()) {
            tradeType = ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue();
        } else if (contractModifyDTO.getContractSource() == ContractActionEnum.SPLIT_CUSTOMER.getActionValue()) {
            tradeType = ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue();
        } else {
            tradeType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        }

        // 处理基本数据
        String sonContractCode = "";
        // 保存不生成编号
        if (contractModifyDTO.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            sonContractCode = contractService.genSonContractCode(parentContractEntity.getContractCode(), parentContractEntity.getSalesType());
        }

        contractEntity.setId(null)
                .setUsage(contractModifyDTO.getUsage())
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setContractType(contractModifyDTO.getSonContractType())
                .setParentId(parentContractEntity.getId())
                .setRootId(parentContractEntity.getParentId() == 0 ? parentContractEntity.getId() : parentContractEntity.getParentId())
                .setShipWarehouseId(Integer.valueOf(contractModifyDTO.getShipWarehouseId()))
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setTradeType(tradeType)
                .setMemo(contractModifyDTO.getRemark())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setCreateBatch(null);
    }

    /**
     * 处理变更的业务信息
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected void buildModifyBizInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        ContractEntity parentContractEntity = contractModifyDTO.getParentContractEntity();
        // 客户主体变更
        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-拆分、变更主体 Author: Mr 2024-04-26 Start
        // 注释旧方法：1.客户信息只针对于变更主体修改，导致没有修改主体的合同仍然使用旧的信息 2.方法的可读性太差，重构
        /*if (!parentContractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId())) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(contractModifyDTO.getCustomerId());
            contractEntity.setCustomerId(customerDTO.getId())
                    .setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName())
                    .setCustomerStatus(customerDTO.getStatus());
        }

        if (contractModifyDTO.getSupplierId() != null && !parentContractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId())) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(contractModifyDTO.getSupplierId());
            contractEntity.setSupplierId(customerDTO.getId())
                    .setSupplierName(customerDTO.getName());
            contractEntity.setSupplierAccountId(contractModifyDTO.getSupplierAccountId());
        } else {
            contractEntity.setSupplierId(parentContractEntity.getSupplierId());
        }*/
        updateCustomerInfo(parentContractEntity.getCustomerId(), parentContractEntity.getSupplierId(),
                contractModifyDTO.getCustomerId(), contractModifyDTO.getSupplierId(), contractEntity);
        contractEntity.setSupplierAccountId(contractModifyDTO.getSupplierAccountId());
        // BUGFIX：case-1002556 修改合同主体但是没有跳转新抬头-拆分、变更主体 Author: Mr 2024-04-26 End

        // 品类改变
//        if (!parentContractEntity.getGoodsCategoryId().equals(contractModifyDTO.getGoodsCategoryId()) ||
//                !parentContractEntity.getGoodsPackageId().equals(contractModifyDTO.getGoodsPackageId()) ||
//                !parentContractEntity.getGoodsSpecId().equals(contractModifyDTO.getGoodsSpecId())) {
//
//        }
        SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
        if (null != skuEntity.getTaxRate()) {
            //税率
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            contractEntity
                    .setCategory1(skuEntity.getCategory1())
                    .setCategory2(skuEntity.getCategory2())
                    .setCategory3(skuEntity.getCategory3())
                    .setGoodsCategoryId(skuEntity.getCategory2())
                    .setTaxRate(taxRate)
                    .setGoodsName(skuEntity.getFullName());
        }
        if (null != contractModifyDTO.getCustomerId() && null != contractModifyDTO.getGoodsCategoryId()) {
            CustomerDetailEntity customerDetailEntities = customerDetailFacade.queryCustomerDetailEntity(contractModifyDTO.getCustomerId(), skuEntity.getCategory3());
            if (null != customerDetailEntities) {
                contractEntity.setQualityCheck(customerDetailEntities.getQualityCheckContent());
                //迟付款罚金
                contractEntity.setDelayPayFine(customerDetailEntities.getDeliveryDelayFine());
            }
        }

        //查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(contractEntity.getCustomerId())
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);

        if(!customerInvoiceEntities.isEmpty()){
            int invoiceId = customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);

            if (null != systemRuleItemEntity) {
                //发票类型
                contractEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
            }
        }

        // belongCustomerId变更
        if (StringUtils.isNotBlank(contractEntity.getDeliveryFactoryCode())) {
            FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(contractEntity.getDeliveryFactoryCode());
            if (factoryEntity != null) {
                CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryEntity.getId(), contractEntity.getCompanyId());
                contractEntity.setBelongCustomerId(null != customerEntity ? customerEntity.getId() : null);
            }
        }

        // 处理点价截止日期
        String priceEndTime = contractModifyDTO.getPriceEndTime();

        if (DateTimeUtil.isDate(priceEndTime)) {
            contractEntity.setPriceEndType(ContractPriceEndTypeEnum.DATE.getValue());
        } else {
            contractEntity.setPriceEndType(ContractPriceEndTypeEnum.TEXT.getValue());
        }

        // 转月次数
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {

            // 获取客户属性
            CustomerDetailBO customerDetailBO = new CustomerDetailBO();
            customerDetailBO
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
                    .setCustomerId(contractEntity.getCustomerId());
            List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

            if (!customerDetailEntityList.isEmpty()) {
                // 普通用户考虑超远期合同
                CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
                int totalTransferTimes;
                int totalReversePriceTimes = 0;
                // 先判断是否开通转月权限
                if (customerDetailEntity.getIsWhiteList() == 1) {
                    totalTransferTimes = 4;
                } else {
                    // 超远期合同-继承父合同
                    if (parentContractEntity.getIsOverForward() == 1) {
                        totalTransferTimes = 2;
                    } else {
                        totalTransferTimes = 1;
                    }
                }

                // 再判断是否开通反点价权限
                if (customerDetailEntity.getIsReversePrice() == 1) {
                    totalReversePriceTimes = 1;
                }

                contractEntity.setAbleTransferTimes(Math.max(totalTransferTimes - contractEntity.getTransferredTimes(), 0))
                        .setTotalTransferTimes(totalTransferTimes)
                        .setIsOverForward(parentContractEntity.getIsOverForward())
                        .setAbleReversePriceTimes(Math.max(totalReversePriceTimes - contractEntity.getReversedPriceTimes(), 0))
                        .setTotalReversePriceTimes(totalReversePriceTimes);
            }
        }

        // 交货工厂是否变更
        int isChangeFactory = 0;
        if (!parentContractEntity.getDeliveryFactoryCode().equals(contractModifyDTO.getDeliveryFactoryCode())) {
            isChangeFactory = 1;
        }

        // 开户行变更
        if (!parentContractEntity.getDeliveryFactoryCode().equals(contractModifyDTO.getDeliveryFactoryCode())
                && parentContractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(parentContractEntity.getSupplierId())
                    .setCompanyId(parentContractEntity.getCompanyId())
                    .setCategoryId(parentContractEntity.getGoodsCategoryId())
                    .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                    .setSalesType(parentContractEntity.getSalesType())
                    .setCategory2(String.valueOf(parentContractEntity.getCategory2()))
                    .setCategory3(String.valueOf(parentContractEntity.getCategory3()))
            ;
            CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
            if (null != customerDTO) {
                List<CustomerBankDTO> customerBankDTOS = customerDTO.getCustomerBankDTOS();
                if (CollectionUtil.isNotEmpty(customerBankDTOS)) {
                    contractEntity.setSupplierAccount(customerBankDTOS.get(0).getBankAccountNo())
                            .setSupplierAccountId(customerBankDTOS.get(0).getId());
                }
            }
        }

        // 拆分包含已定量
        BigDecimal totalPriceNum = BigDecimal.ZERO;

        // 原始合同类型
        Integer originContractType = parentContractEntity.getOriginContractType();

        List<ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();

        if (CollectionUtil.isNotEmpty(confirmPriceDTOList)) {
            originContractType = parentContractEntity.getContractType();

            totalPriceNum = confirmPriceDTOList.stream()
                    .map(ConfirmPriceDTO::getConfirmNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 一口价合同拆分已定价量
        if (parentContractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.add(contractModifyDTO.getModifyNum());
        }

        // 价格处理
        PriceDetailBO priceDetailBO = contractModifyDTO.getPriceDetailDTO();
        ContractUnitPriceVO contractUnitPriceVO = contractPriceService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 重新汇总数据
        contractEntity.setOrderNum(contractModifyDTO.getModifyNum())
                .setContractNum(contractModifyDTO.getModifyNum())
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractModifyDTO.getModifyNum(), contractModifyDTO.getUnitPrice()))
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setTotalPriceNum(totalPriceNum)
                .setTotalModifyNum(BigDecimal.ZERO)
                .setTotalTransferNum(BigDecimal.ZERO)
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setExtraPrice(priceDetailBO.getExtraPrice())
                .setBaseDiffPrice(priceDetailBO.getExtraPrice())
                .setIsChangeFactory(isChangeFactory)
                .setOriginContractType(originContractType);

        // 签订总金额
        contractEntity.setOrderAmount(contractEntity.getTotalAmount());

        // 付款方式
        contractEntity.setPaymentType(contractModifyDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

        // 袋皮扣重
        contractEntity.setPackageWeight(StringUtils.isNotBlank(contractModifyDTO.getPackageWeight())
                ? contractModifyDTO.getPackageWeight() : parentContractEntity.getPackageWeight());
                // TODO 客户域需要调整
        //contractEntity.setSiteId(customerDTO.getSiteId());
        // V1 套账ID Author:zengshl 2024-06-18 end
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setContractType(contractModifyDTO.getSonContractType())
                .setDepositRate(contractModifyDTO.getDepositRate())
                .setAddedDepositRate(contractModifyDTO.getAddedDepositRate());
        contractEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));
        // V1 套账ID Author:zengshl 2024-06-18 start
        // 主体id
        Integer customerId = contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue() ? contractModifyDTO.getCustomerId() : contractModifyDTO.getSupplierId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

    }

    /**
     * 创建合同
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected void createByModify(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 保存合同
        try {
            if (contractModifyDTO.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
                contractDao.save(contractEntity);
            }
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }

        // 记录子合同信息
        contractModifyDTO.setSonContractId(contractEntity.getId())
                .setSonContractType(contractEntity.getContractType());
    }

    /**
     * 创建后操作
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected void afterModifyProcess(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 保存拆分合同的定价单
        if (contractModifyDTO.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            List<ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();
            if (CollectionUtil.isNotEmpty(confirmPriceDTOList)) {
                for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {
                    TTPriceEntity priceEntity = ttPriceService.getById(confirmPriceDTO.getTtPriceId());
                    int sourceId = priceEntity.getId();
                    priceEntity
                            .setId(null)
                            .setSourceContractId(priceEntity.getContractId())
                            .setContractCode(contractEntity.getContractCode())
                            .setContractId(contractEntity.getId())
                            .setNum(confirmPriceDTO.getConfirmNum())
                            .setSourceId(sourceId)
                            .setCreatedAt(DateTimeUtil.now())
                            .setUpdatedAt(DateTimeUtil.now())
                            .setTtId(null)
                    ;
                    ttPriceService.saveTtPrice(priceEntity);
                }
            }
        }
    }
}
