package com.navigator.trade.app.tt.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TTAddQO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.impl.ContractQueryServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.navigator.trade.pojo.enums.ContractSignStatusEnum.*;

/**
 * <AUTHOR>
 * @Description TT领域查询服务
 * @Date 2024/7/14 16:17
 * @Version 1.0
 */
@Service
public class TTQueryDomainServiceImpl implements TTQueryDomainService {

    @Autowired
    TradeTicketDao tradeTicketDao;
    @Autowired
    TtAddDao ttAddDao;
    @Autowired
    TtModifyDao ttModifyDao;
    @Autowired
    TtPriceDao ttPriceDao;
    @Autowired
    TtStructureDao ttStructureDao;
    @Autowired
    TtTranferDao ttTranferDao;
    @Autowired
    ContractPriceDao contractPriceDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private TradeTicketVODao tradeTicketVODao;
    @Autowired
    private ContractQueryServiceImpl contractQueryService;
    @Autowired
    private AttributeFacade attributeFacade;
    @Autowired
    private WarehouseFacade warehouseFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    private IContractSignQueryService iContractSignQueryService;
    @Autowired
    private CustomerFacade customerFacade;

    private static List<Integer> newTypeList = Arrays.asList(TTTypeEnum.NEW.getType(), TTTypeEnum.STRUCTURE_PRICE.getType());

    private static List<Integer> cancelList = Arrays.asList(WAIT_PROVIDE.getValue(), WAIT_REVIEW.getValue(), WAIT_STAMP.getValue());

    private static List<Integer> forbiddenCancelTypeList = Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.TRANSFER.getType(),
            TTTypeEnum.REVERSE_PRICE.getType(), TTTypeEnum.FIXED.getType());
    private static List<Integer> invalidList = Arrays.asList(WAIT_BACK.getValue(), WAIT_CONFIRM.getValue());

    @Override
    public Result queryTTList(QueryDTO<TTQueryDTO> queryDTO) {
        String userId = JwtUtils.getCurrentUserId();
        Page<TradeTicketVOEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        TTQueryDTO ttQueryDTO = queryDTO.getCondition();
        ttQueryDTO.setBuCode(StringUtils.isNotBlank(ttQueryDTO.getBuCode()) ? ttQueryDTO.getBuCode() : BuCodeEnum.ST.getValue());
        //分页查询
        PermissionBO permissionBO = employFacade.querySitePermission(userId, Integer.parseInt(ttQueryDTO.getGoodsCategoryId()));
//        Map<Integer, List<Integer>> companyCustomerIdMap = permissionBO.getCompanyCustomerIdMap();
//        if (companyCustomerIdMap.isEmpty()) {
//            companyCustomerIdMap.put(-1, Collections.singletonList(-1));
//        }
        if (StringUtils.isNotBlank(ttQueryDTO.getCreateBy())) {
            ttQueryDTO.setCreateBy(userId);
        }
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        IPage<TradeTicketVOEntity> iPage = tradeTicketVODao.queryVOPageByTTQueryDTO(page, ttQueryDTO, permissionBO.getSiteCodeList());
        List<TTQueryVO> ttQueryVOList = iPage.getRecords().stream().map(i -> {
                    ContractEntity contractEntity = contractQueryService.getBasicContractById(i.getContractId());
                    TTQueryVO ttQueryVO = new TTQueryVO();
                    BeanUtils.copyProperties(i, ttQueryVO);
                    ttQueryVO
                            .setInvalidReason(i.getInvalidReason())
                            .setTtId(i.getId())
                            .setSignId(i.getSignId())
                            .setProtocolCode(i.getProtocolCode())
                    ;
                    //创建人
                    if (null != i.getCreatedBy()) {
                        EmployEntity employEntity = employFacade.getEmployById(i.getCreatedBy());
                        if (null != employEntity) {
                            ttQueryVO.setCreateBy(employEntity.getName());
                        }
                    }
                    // 发货库点
                    ttQueryVO.setShipWarehouseName(warehouseConvertValue(i.getShipWarehouseId()));
                    // 带皮扣重
                    if (StringUtils.isNotBlank(i.getPackageWeight())) {
                        SystemRuleItemEntity pacSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getPackageWeight()));
                        ttQueryVO.setPackageWeight(pacSystemRuleItemEntity != null ? pacSystemRuleItemEntity.getRuleKey() : null);
                    }

                    // 总数量
                    ttQueryVO.setContractNum(BigDecimalUtil.isZero(i.getChangeContractNum()) ? i.getAfterContractNum() : i.getChangeContractNum());

                    // 交提货方式
                    DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(i.getDeliveryType());
                    if (null != deliveryTypeEntity) {
                        ttQueryVO.setDeliveryType(deliveryTypeEntity.getName());
                    }

                    // 期货合约&基差价
                    if (StringUtils.isBlank(i.getDomainCode()) && contractEntity != null) {
                        i.setDomainCode(contractEntity.getDomainCode());
                    }
                    ttQueryVO.setDomainCode(getDomainCode(i.getFutureCode(), i.getDomainCode(), i.getExtraPrice()));

                    // 点价截止时间
                    if (i.getPriceEndTime() != null && DateTimeUtil.isDate(i.getPriceEndTime())) {
                        ttQueryVO.setPriceEndTime(i.getPriceEndTime().split(" ")[0]);
                    }

                    // 客户集团名称
                    ttQueryVO.setEnterpriseName(i.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? i.getEnterpriseName() : i.getSupplierEnterpriseName());

                    //查询协议状态
                    if (String.valueOf(TTStatusEnum.WAITING.getType()).equalsIgnoreCase(queryDTO.getCondition().getStatus())) {
                        //待修改提交区的协议为异常状态
                        ttQueryVO.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
                    } else {
                        ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(i.getId());
                        if (contractSignDetail != null) {
                            Integer status = contractSignDetail.getStatus();
                            ttQueryVO.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());

                            //根据合同状态判断按钮显示
                            ttQueryVO.setInvalidStatus(0);
                            ttQueryVO.setCancelStatus(0);
                            if (newTypeList.contains(i.getType())) {
                                if (cancelList.contains(status)) {
                                    ttQueryVO.setCancelStatus(1);
                                }
                                if (invalidList.contains(status)) {
                                    ttQueryVO.setInvalidStatus(1);
                                }
                            } else {
                                //根据协议状态判断撤销按钮的显示
                                if (!forbiddenCancelTypeList.contains(i.getType()) && status < ContractSignStatusEnum.WAIT_CONFIRM.getValue()) {
                                    ttQueryVO.setCancelStatus(1);
                                }
                            }
                        }
                    }

                    if (contractEntity != null) {
                        Integer status = contractEntity.getStatus();
                        ttQueryVO.setContractStatus(ContractStatusEnum.getDescByValue(status))
                                .setIsSoybean2(contractEntity.getIsSoybean2());
                    }
                    companyEntityList.stream().peek(k -> {
                        if (k.getId().equals(i.getCompanyId())) {
                            ttQueryVO.setCompanyName(k.getShortName());
                        }
                    });
                    return ttQueryVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, ttQueryVOList);
    }

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
    @Override
    public Result queryTTListNew(QueryDTO<TTQueryDTO> queryDTO) {
        // 查询前校验
        if (queryDTO == null || queryDTO.getCondition() == null) {
            throw new BusinessException("查询参数不能为空");
        }

        // 无权限直接返回空
        PermissionBO permissionBO = employFacade.querySitePermission(JwtUtils.getCurrentUserId(), Integer.parseInt(queryDTO.getCondition().getGoodsCategoryId()));
        if (permissionBO == null || CollectionUtils.isEmpty(permissionBO.getSiteCodeList())) {
            return Result.page(new Page<>(), null);
        }

        // 预处理查询参数
        TTQueryDTO ttQueryDTO = handleQueryDTO(queryDTO, permissionBO);

        // 分页查询：根据条件分页查询数据
        List<TradeTicketVOEntity> ticketVOEntityList = tradeTicketDao.queryTTPagedList(ttQueryDTO);

        // 构建结果列表
        List<TTQueryVO> ttQueryVOList = mapTicketVOToTTQueryVO(ticketVOEntityList, queryDTO);

        // 总记录
        Integer totalCount = tradeTicketDao.queryTTTotalCount(ttQueryDTO);

        // 构建分页信息
        Page<TTQueryVO> page = new Page<>();
        page.setRecords(ttQueryVOList);
        page.setTotal(totalCount);
        page.setSize(queryDTO.getPageSize());
        page.setCurrent(queryDTO.getPageNo());
        page.setPages(PageUtil.totalPage(totalCount, queryDTO.getPageSize()));

        // 返回分页结果
        return Result.page(page, ttQueryVOList);
    }


    /**
     * 预处理查询参数
     *
     * @param queryDTO     查询参数
     * @param permissionBO 权限信息
     * @return TTQueryDTO
     */
    private TTQueryDTO handleQueryDTO(QueryDTO<TTQueryDTO> queryDTO, PermissionBO permissionBO) {
        TTQueryDTO ttQueryDTO = queryDTO.getCondition();
        // 账套
        ttQueryDTO.setSiteCodeList(permissionBO.getSiteCodeList());

        // 业务线
        ttQueryDTO.setBuCode(StringUtils.isNotBlank(ttQueryDTO.getBuCode()) ? ttQueryDTO.getBuCode() : BuCodeEnum.ST.getValue());

        // 创建人
        ttQueryDTO.setCreateBy(StringUtils.isNotBlank(ttQueryDTO.getCreateBy()) ? JwtUtils.getCurrentUserId() : null);

        // 业务编号
        ttQueryDTO.setContractCode(StringUtils.trimToNull(ttQueryDTO.getContractCode()))
                .setBizCode(StringUtils.trimToNull(ttQueryDTO.getBizCode()));

        // 集团客户的模糊查询
        if (StringUtils.isNotBlank(ttQueryDTO.getEnterpriseName())) {
            List<Integer> enterpriseIds = customerFacade.getCustomerIdsByEnterpriseName(ttQueryDTO.getEnterpriseName());

            if (ttQueryDTO.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
                ttQueryDTO.setSupplierEnterpriseIds(enterpriseIds);
            } else if (ttQueryDTO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                ttQueryDTO.setCustomerEnterpriseIds(enterpriseIds);
            }
        }

        // 初始化分页参数
        ttQueryDTO.setStartRow((queryDTO.getPageNo() - 1) * queryDTO.getPageSize());
        ttQueryDTO.setPageSize(queryDTO.getPageSize());

        return ttQueryDTO;
    }

    /**
     * 处理列表映射的逻辑提取到单独的函数
     */
    private List<TTQueryVO> mapTicketVOToTTQueryVO(List<TradeTicketVOEntity> ticketVOEntityList, QueryDTO<TTQueryDTO> queryDTO) {
        List<TTQueryVO> ttQueryVOList = new ArrayList<>();

        Map<String, String> enterpriseNameMap = new HashMap<>();

        for (TradeTicketVOEntity tradeTicketVOEntity : ticketVOEntityList) {
            TTQueryVO ttQueryVO = new TTQueryVO();

            // 属性拷贝
            BeanUtils.copyProperties(tradeTicketVOEntity, ttQueryVO);

            // 设置额外属性
            ttQueryVO
                    .setTtId(tradeTicketVOEntity.getId())
                    .setCreateBy(IdNameConverter.getName(IdNameType.user_id_name, tradeTicketVOEntity.getCreatedBy().toString()))
                    .setShipWarehouseName(tradeTicketVOEntity.getShipWarehouseName())
                    .setPackageWeight(tradeTicketVOEntity.getPackageWeightName())
                    .setContractNum(BigDecimalUtil.isZero(tradeTicketVOEntity.getChangeContractNum()) ? tradeTicketVOEntity.getAfterContractNum() : tradeTicketVOEntity.getChangeContractNum())
                    .setDeliveryType(tradeTicketVOEntity.getDeliveryTypeName())
                    .setDomainCode(getDomainCode(tradeTicketVOEntity.getFutureCode(), tradeTicketVOEntity.getDomainCode(), tradeTicketVOEntity.getExtraPrice()))
                    .setPriceEndTime(tradeTicketVOEntity.getPriceEndTime() != null ? tradeTicketVOEntity.getPriceEndTime().split(" ")[0] : null)
                    .setEnterpriseName(tradeTicketVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? tradeTicketVOEntity.getEnterpriseName() : tradeTicketVOEntity.getSupplierEnterpriseName());

            // 客户集团名称
            Integer customerId = tradeTicketVOEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                    ? tradeTicketVOEntity.getCustomerId()
                    : tradeTicketVOEntity.getSupplierId();

            if (enterpriseNameMap.containsKey(tradeTicketVOEntity.getSalesType() + "-" + customerId)) {
                ttQueryVO.setEnterpriseName(enterpriseNameMap.get(tradeTicketVOEntity.getSalesType() + "-" + customerId));
            } else {
                CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
                if (customerDTO != null) {
                    ttQueryVO.setEnterpriseName(customerDTO.getEnterpriseName());
                    enterpriseNameMap.put(tradeTicketVOEntity.getSalesType() + "-" + customerId, customerDTO.getEnterpriseName());
                }
            }

            // 设置协议状态和按钮显示
            setProtocolStatusAndActions(queryDTO, tradeTicketVOEntity, ttQueryVO);

            ttQueryVOList.add(ttQueryVO);
        }

        return ttQueryVOList;
    }

    /**
     * 设置协议状态和按钮显示
     */
    private void setProtocolStatusAndActions(QueryDTO<TTQueryDTO> queryDTO, TradeTicketVOEntity tradeTicketVOEntity, TTQueryVO ttQueryVO) {
        if (String.valueOf(TTStatusEnum.WAITING.getType()).equalsIgnoreCase(queryDTO.getCondition().getStatus())) {
            //待修改提交区的协议为异常状态
            ttQueryVO.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
        } else {
            ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(tradeTicketVOEntity.getId());
            if (contractSignDetail != null) {
                Integer status = contractSignDetail.getStatus();
                ttQueryVO.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());

                //根据合同状态判断按钮显示
                ttQueryVO.setInvalidStatus(0);
                ttQueryVO.setCancelStatus(0);
                if (newTypeList.contains(tradeTicketVOEntity.getType())) {
                    if (cancelList.contains(status)) {
                        ttQueryVO.setCancelStatus(1);
                    }
                    if (invalidList.contains(status)) {
                        ttQueryVO.setInvalidStatus(1);
                    }
                } else {
                    //根据协议状态判断撤销按钮的显示
                    if (!forbiddenCancelTypeList.contains(tradeTicketVOEntity.getType()) && status < ContractSignStatusEnum.WAIT_CONFIRM.getValue()) {
                        ttQueryVO.setCancelStatus(1);
                    }
                }
            }
        }
    }
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end

    @Override
    public TradeTicketDO queryTradeTicketDOByTTID(TradeTicketQO qo) {
        Integer ttId = qo.getTtId();
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(tradeTicketEntity.getType());
        TTSubEntity subEntity = null;
        switch (ttTypeEnum) {
            case NEW:
            case BUYBACK:
            case WASHOUT:
            case CLOSED:
            case ALLOCATE:
            case ASSIGN:
            case INVALID:
            case CONTRACT_CANCEL:
                subEntity = ttAddDao.getTTAddEntityByTTId(ttId);
                break;
            case REVISE:
            case SPLIT:
            case WRITE_OFF:
                subEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
                break;
            case TRANSFER:
            case REVERSE_PRICE:
                subEntity = ttTranferDao.getTTTransferEntityByTTId(ttId);
                break;
            case PRICE:
            case FIXED:
                subEntity = ttPriceDao.getTTPriceEntityByTTId(ttId);
                break;
            case STRUCTURE_PRICE:
                subEntity = ttStructureDao.getByTTId(ttId);
                break;
            default:
                break;
        }
        ContractPriceEntity priceEntity = contractPriceDao.getContractPriceEntityByTTId(ttId);
        TradeTicketDO tradeTicketDO = new TradeTicketDO();
        tradeTicketDO.setTradeTicketEntity(tradeTicketEntity);
        tradeTicketDO.setTtSubEntity(subEntity);
        tradeTicketDO.setContractPriceEntity(priceEntity);
        return tradeTicketDO;
    }

    @Override
    public TradeTicketEntity fetchTradeTicketEntity(TradeTicketQO qo) {
        if (Objects.nonNull(qo.getSignId())) {
            List<TradeTicketEntity> tradeTicketEntities = tradeTicketDao.getBySignId(qo.getSignId());
            if (CollectionUtils.isNotEmpty(tradeTicketEntities)) {
                return tradeTicketEntities.get(0);
            }
        } else if (Objects.nonNull(qo.getContractId())) {
            List<TradeTicketEntity> tradeTicketEntities = tradeTicketDao.getCanModifyByContractId(qo.getContractId());
            if (CollectionUtils.isNotEmpty(tradeTicketEntities)) {
                return tradeTicketEntities.get(0);
            }
        } else if (Objects.nonNull(qo.getTtId())) {
            // 根据groupId 查询
            if (Objects.nonNull(qo.getGroupId())) {
                return tradeTicketDao.getByGroupId(qo.getGroupId(), qo.getTtId());
            }
            return tradeTicketDao.getById(qo.getTtId());
        }
        return null;
    }

    @Override
    public TTAddEntity fetchTTAddEntity(TTAddQO qo) {
        return ttAddDao.getTTAddEntityByTTId(qo.getTtId());
    }

    @Override
    public List<TTPriceEntity> fetchTTPriceEntities(TTPriceQO qo) {
        return ttPriceDao.selectMany(qo);
    }

    @Override
    public TTPriceEntity fetchTTPriceEntity(TTPriceQO qo) {
        return ttPriceDao.getById(qo.getNeId());
    }

    @Override
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        return contractPriceDao.getContractPriceEntityContractId(contractId);
    }

    @Override
    public TTModifyEntity getTTModifyEntityByTTId(Integer ttId) {
        return ttModifyDao.getTTModifyEntityByTTId(ttId);
    }

    @Override
    public TradeTicketEntity getByTtId(Integer ttId) {
        return tradeTicketDao.getById(ttId);
    }

    @Override
    public TradeTicketEntity getByGroupId(String groupId, Integer id) {
        return tradeTicketDao.getByGroupId(groupId, id);
    }

    @Override
    public TradeTicketEntity getWarrantByGroupId(String groupId, Integer id) {
        return tradeTicketDao.getWarrantByGroupId(groupId, id);
    }

    @Override
    public TradeTicketEntity getByContractId(Integer contractId, String groupId) {
        return tradeTicketDao.getByContractId(contractId, groupId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByPriceApplyId(Integer priceApplyId) {
        return ttTranferDao.getTTTranferByPriceApplyId(priceApplyId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByPriceAllocateId(Integer priceAllocateId) {
        return ttTranferDao.getTTTranferByPriceAllocateId(priceAllocateId);
    }

    @Override
    public List<TTTranferEntity> getTTTranferByContractId(Integer contractId, Integer Id) {
        return ttTranferDao.getTTTranferByContractId(contractId, Id);
    }

    @Override
    public List<TTPriceEntity> getTTPriceByApplyId(Integer priceApplyId) {
        return ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getPriceApplyId, priceApplyId)
        );
    }

    @Override
    public List<TTPriceEntity> getTTPriceByAllocateId(Integer allocateId) {
        return ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getAllocateId, allocateId)
        );
    }

    @Override
    public List<TTPriceEntity> getTTPriceBySourceId(Integer sourceId) {
        return ttPriceDao.list(
                new LambdaQueryWrapper<TTPriceEntity>()
                        .eq(TTPriceEntity::getSourceId, sourceId)
                        .ne(TTPriceEntity::getSourceId, 0)
        );
    }


    /**
     * 获取合同列表的主力合约
     *
     * @param futureCode
     * @param domainCode
     * @param extraPrice
     * @return
     */
    private String getDomainCode(String futureCode, String domainCode, BigDecimal extraPrice) {
        if (extraPrice == null) {
            return futureCode + domainCode;
        }

        return BigDecimalUtil.isGreaterEqualThanZero(extraPrice) ?
                futureCode + domainCode + "+" + extraPrice.stripTrailingZeros().toPlainString() :
                futureCode + domainCode + extraPrice.stripTrailingZeros().toPlainString();
    }

    /**
     * 交提货方式：dba_factory_warehouse
     * 值转对象 add by zengshl
     *
     * @param warehouseId
     * @return
     */
    public String warehouseConvertValue(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }

}
