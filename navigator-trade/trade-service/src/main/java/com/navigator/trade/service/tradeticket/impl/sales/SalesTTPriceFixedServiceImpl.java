package com.navigator.trade.service.tradeticket.impl.sales;

import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component("SBM_S_TT_FIXED,SBO_S_TT_FIXED")
public class SalesTTPriceFixedServiceImpl extends SalesTTPriceServiceImpl {
    public SalesTTPriceFixedServiceImpl() {
        ttTypeEnum = TTTypeEnum.FIXED;
        contractTradeTypeEnum = ContractTradeTypeEnum.FIXED;
        contractSource = ContractActionEnum.PRICE_FIXED.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.SALES;
        processorType = ProcessorTypeEnum.SBM_S_FIXED.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_FIXED.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getPriceContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        return "";
    }

}
