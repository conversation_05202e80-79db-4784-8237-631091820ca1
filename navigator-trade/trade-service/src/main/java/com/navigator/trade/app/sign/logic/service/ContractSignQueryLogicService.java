package com.navigator.trade.app.sign.logic.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import com.navigator.trade.pojo.vo.ContractSignDetailFileVO;
import com.navigator.trade.pojo.vo.ContractSignFileVO;

import java.util.List;

/**
 * 协议域的独立内容处理
 * <AUTHOR>
 * @since 2024-07-15 17:48
 */
public interface ContractSignQueryLogicService {

    /**
     * 协议列表高级分页搜索
     *
     * @param queryDTO
     * @return
     */
    Result queryContContractSigns(QueryDTO<ContractSignQO> queryDTO);

    /**
     * 根据协议ID，查询协议详情
     *
     * @param contractSignId 协议Id
     * @return 协议详情信息
     */
    ContractSignEntity getContractSignDetailById(Integer contractSignId);

    /**
     * 根据ttId，查询协议详情
     *
     * @param ttId ttId
     * @return 协议详情信息
     */
    ContractSignEntity getContractSignDetailByTtId(Integer ttId);

    /**
     * 查询各个状态下的合同数量
     *
     * @return 结果
     */
    ContractSignAllStatusNumVO getCustomerSignStat(Integer salesType);

    /**
     * 查询各个状态下的合同数量
     */
    ContractSignAllStatusNumVO getMagellanSignStat(Integer ldcFrame, Integer salesType, Integer goodsCategoryId);

    /**
     * 根据唯一编号查询协议
     *
     * @param uuid
     * @return
     */
    ContractSignEntity queryByUUId(String uuid);

    /**
     * 根据合同ID，查询所有已签署的协议信息
     *
     * @param contractId 合同Id
     * @return 合同的所有的协议信息（文件只有双签）
     */
    List<ContractSignFileVO> getSignFileListByContractId(Integer contractId, Integer system);

    /**
     * 根据协议ID，查询所有的协议文件信息
     *
     * @param contractSignId 协议Id
     * @param system         系统(1麦哲伦 2哥伦布)
     * @return 协议的所有文件信息
     */
    List<ContractSignDetailFileVO> getAllSignFileListById(Integer contractSignId, Integer system);

    /**
     * 查询客户各个状态下的合同数量(哥伦布)
     *
     * @return
     */
    ContractSignAllStatusNumVO getContractSignStat(QueryContractSignBO signBO);


    /**
     * 获取合同下未完成的协议
     *
     * @param ids        合同编号
     * @param ttTypeList tt类型
     * @return
     */
    List<ContractSignEntity> queryIncompleteByContractId(List<Integer> ids, List<Integer> ttTypeList);


    /**
     * 根据合同id获取协议
     *
     * @param contractId
     * @param ttStatusList
     * @return
     */
    List<ContractSignEntity> querySignListByContractId(Integer contractId, List<Integer> ttStatusList);

    /**
     * query sonContract Incomplete Sign
     *
     * @param ids
     * @return
     */
    List<ContractSignEntity> querySonContractSplitIncomplete(List<Integer> ids);

    void sendContractSignOriginalPaper();

    Boolean updateById(ContractSignEntity signEntity);
}
