package com.navigator.trade.service.contract.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractSplitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 拆分合同的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
public abstract class BaseContractSplitAbstractService extends BaseContractAbstractService {

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public List<TTQueryVO> splitContract(ContractModifyDTO contractModifyDTO) {
        ContractEntity contractEntity = contractValueObjectService.getContractById(contractModifyDTO.getContractId());

        // 区分是保存还是提交
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {

            String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
            if (StringUtils.isNotBlank(saveTimes)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_SAVE_TT);
            }

            // 处理子合同
            ContractEntity sonContractEntity = operateSonContract(contractEntity, contractModifyDTO);
            // 保存tt到新录入状态
            List<TTQueryVO> ttQueryVOS = operateTradeTicket(sonContractEntity, contractEntity, contractModifyDTO);

            // 一个合同仅支持保存一个变更TT！
            redisUtil.set(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode(), "1");
            return ttQueryVOS;
        }

        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SUBMIT.getValue() ||
                contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE_SUBMIT.getValue()) {

            ContractEntity originalContractEntity = new ContractEntity();
            BeanUtils.copyProperties(contractEntity, originalContractEntity);

            // 1.校验合同数据
            checkContractInfo(contractEntity, contractModifyDTO);

            // 2.处理子合同
            ContractEntity sonContractEntity = operateSonContract(contractEntity, contractModifyDTO);

            // 3.处理TT
            List<TTQueryVO> ttQueryVOS = operateTradeTicket(sonContractEntity, contractEntity, contractModifyDTO);

            // 4.记录拆分日志
            recordOperationLog(contractModifyDTO, contractEntity);

            // 5.更新父合同
            operateFatherContract(contractEntity, contractModifyDTO);

            //合同前后信息变更记录
            updateModifyContent(originalContractEntity, contractEntity, ttQueryVOS, TTTypeEnum.SPLIT.getType());

            return ttQueryVOS;
        }

        return null;
    }

    /**
     * 日志处理
     *
     * @param contractModifyDTO
     * @param contractEntity
     */
    protected abstract void recordOperationLog(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity);

    @Override
    public List<TTQueryVO> reviseContract(ContractModifyDTO contractModifyDTO) {
        // 不处理
        return null;
    }

    //====抽象方法：子类必须实现和替换的方法====

    /**
     * 校验合同信息
     *
     * @param contractEntity    合同实体
     * @param contractModifyDTO 变更dto
     */
    protected void checkContractInfo(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 判断客户是否可用
        if (!isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 合同状态处于生效中
        if (!Arrays.asList(ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.SPLITTING.getValue()).contains(contractEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_SPLITTING);
        }

        // 校验付款代码是否禁用
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(contractModifyDTO.getPayConditionId());
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
        }

        //校验质量指标
        QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        Integer customerId = contractModifyDTO.getCustomerId();
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            customerId = contractModifyDTO.getSupplierId();
        }
        qualityInfoDTO
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                .setWarehouseId(Integer.parseInt(contractModifyDTO.getShipWarehouseId()))
                .setUsage(contractModifyDTO.getUsage())
                .setSpecId(contractModifyDTO.getGoodsSpecId())
                .setGoodsId(contractModifyDTO.getGoodsId())
                .setSalesType(contractEntity.getSalesType())
                .setCustomerId(customerId);
        Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            throw new BusinessException(ResultCodeEnum.QUALITY_NOT_COMPLETED);
        }

        // 校验客户主数据赊销账期/预付款
        if (contractModifyDTO.getPaymentType().equals(PaymentTypeEnum.CREDIT.getType())) {
            // 获取主数据
            CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
            creditPaymentDTO.setCustomerId(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractModifyDTO.getCustomerId() : contractModifyDTO.getSupplierId())
                    .setCategoryId(contractModifyDTO.getGoodsCategoryId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setCategory1(String.valueOf(contractEntity.getCategory1()))
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
                    .setBuCode(contractEntity.getBuCode())
                    .setIsSales(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                    .setIsProcurement(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
            Result result = customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
            if (result.isSuccess()) {
                List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
                if (CollectionUtil.isNotEmpty(creditPaymentVOList)) {
                    CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                    // 客户主数据赊销改为预付,系统提醒"“客户主数据配置，付款方式已更新为：预付”。不允许提交。
                    if (customerCreditPaymentVO.getPaymentType().equals(PaymentTypeEnum.IMPREST.getType())) {
                        throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_TYPE_HAS_CHANGE);
                    }

                    // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                    if (contractModifyDTO.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                        String message = "最多可赊销{" + customerCreditPaymentVO.getCreditDays() + "}天";
                        throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_CREDIT_HAS_CHANGE, message);
                    }
                }
            }
        }

        // 普通拆分
        BigDecimal maxNum = contractEntity.getTotalBillNum();

        // 申请提货数量
        if (CollectionUtil.isEmpty(contractModifyDTO.getConfirmPriceDTOList())) {
            ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO()
                    .setDomainCode(contractEntity.getDomainCode())
                    .setSalesType(contractEntity.getSalesType())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setCustomerId(String.valueOf(contractEntity.getCustomerId()));
            if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                contractFuturesDTO.setCustomerId(String.valueOf(contractEntity.getSupplierId()));
            }
            if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
                maxNum = contractEntity.getTotalPriceNum();
//                if (ContractTypeEnum.getBasicTypeList().contains(contractModifyDTO.getSonContractType())) {
//                    Result transferResult = futuresDomainFacade.mayTransferNum(contractFuturesDTO);
////                    BigDecimal allocateNum = priceAllocateFacade.getSumPriceAllocateOfContract(String.valueOf(contractEntity.getId()));
//                    CustomerFuturesDTO transferDTO = JSON.parseObject(JSON.toJSONString(transferResult.getData()), CustomerFuturesDTO.class);
////                    if (contractModifyDTO.getModifyNum().compareTo(transferDTO.getMayTransferNum().subtract(allocateNum)) > 0) {
//                        if (contractModifyDTO.getModifyNum().compareTo(transferDTO.getMayTransferNum()) > 0) {
//                        throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION,"拆分数量不可超出该合约可转月量！");
//                    }
//                }
            } else if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
//                if (ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == contractModifyDTO.getSonContractType()) {
//                    Result priceResult = futuresDomainFacade.mayPriceNum(contractFuturesDTO);
//                    CustomerFuturesDTO transferDTO = JSON.parseObject(JSON.toJSONString(priceResult.getData()), CustomerFuturesDTO.class);
//                    if (contractModifyDTO.getModifyNum().compareTo(transferDTO.getMayPriceNum()) > 0) {
//                        throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION,"拆分数量不可超出该合约可点价量！");
//                    }
//                }
            }

            if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(maxNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
            }

            if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(contractEntity.getApplyDeliveryNum()).subtract(maxNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
            }

            // 尾量关闭校验
            if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
            }
        } else {
            // 校验定价单数量
            contractModifyDTO.getConfirmPriceDTOList().forEach(confirmPriceDTO -> {
                Integer ttPriceId = confirmPriceDTO.getTtPriceId();
                TTPriceEntity ttPriceEntity = ttPriceService.getById(ttPriceId);
                if (null != ttPriceEntity && BigDecimalUtil.isGreater(confirmPriceDTO.getConfirmNum(), ttPriceEntity.getNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TOTAL_PRICE_NUM_EXCEPTION);
                }
            });

            // 全量拆分一口价
            if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                maxNum = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
                if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(maxNum))) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
                }

                if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(contractEntity.getApplyDeliveryNum()).subtract(maxNum))) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
                }

                // 尾量关闭校验
                if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
                }
            }
        }
    }

    /**
     * 处理子合同信息
     *
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     * @return
     */
    protected abstract ContractEntity operateSonContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 处理TT信息
     *
     * @param sonContractEntity 子合同
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     */
    protected List<TTQueryVO> operateTradeTicket(ContractEntity sonContractEntity, ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        TTDTO ttdto = new TTDTO();

        SalesContractSplitTTDTO salesContractSplitTTDTO = new SalesContractSplitTTDTO();

        // 处理变更的数据
        BeanUtils.copyProperties(sonContractEntity, salesContractSplitTTDTO);
        BeanUtils.copyProperties(contractModifyDTO, salesContractSplitTTDTO);

        // 特殊处理
        salesContractSplitTTDTO.setSonContractId(sonContractEntity.getId());
        salesContractSplitTTDTO.setSourceContractId(contractEntity.getId());
        salesContractSplitTTDTO.setRootContractId(contractEntity.getRootId());
        salesContractSplitTTDTO.setSupplierId(contractModifyDTO.getSupplierId() == null ?
                sonContractEntity.getSupplierId() : contractModifyDTO.getSupplierId());
        salesContractSplitTTDTO.setPackageWeight(contractModifyDTO.getPackageWeight());

        // 付款方式
        salesContractSplitTTDTO.setPaymentType(contractModifyDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());
        salesContractSplitTTDTO.setUsage(contractModifyDTO.getUsage());
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setContractType(contractModifyDTO.getSonContractType())
                .setDepositRate(contractModifyDTO.getDepositRate())
                .setAddedDepositRate(contractModifyDTO.getAddedDepositRate());
        salesContractSplitTTDTO.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // 拆分保存处理的字段
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            salesContractSplitTTDTO.setTtId(contractModifyDTO.getTtId());
            salesContractSplitTTDTO.setCode(contractModifyDTO.getTtCode());
            salesContractSplitTTDTO.setContractType(contractModifyDTO.getSonContractType());
            salesContractSplitTTDTO.setContractNum(contractModifyDTO.getModifyNum());
            salesContractSplitTTDTO.setSonContractId(contractEntity.getId());
            salesContractSplitTTDTO.setContractCode(contractEntity.getContractCode());

            ttdto.setSubmitType(contractModifyDTO.getSubmitType());
        }

        ttdto.setSalesContractSplitTTDTO(salesContractSplitTTDTO);
        // 价格修改
        ttdto.setPriceDetailBO(contractModifyDTO.getPriceDetailDTO());

        // 保存定价单信息
        ttdto.setConfirmPriceInfo(JSON.toJSONString(contractModifyDTO.getConfirmPriceDTOList()));

        // 获取处理TT接口
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                contractEntity.getSalesType(),
                TTTypeEnum.SPLIT.getType(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(ttProcessorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
        List<TTQueryVO> ttQueryVOS = tradeTicketService.saveTT(ttdto).getData();

        // 普通拆分，追加原合同编号
        if (ttQueryVOS.size() == 1) {

            // 前端展示的合同编号
            TTQueryVO ttQueryVO = new TTQueryVO();
            ttQueryVO.setSourceFlag(1).setContractCode(contractEntity.getContractCode());
            ttQueryVOS.add(ttQueryVO);
        }

        return ttQueryVOS;
    }

    /**
     * 处理父合同信息
     *
     * @param contractEntity    父合同
     * @param contractModifyDTO 变更dto
     */
    protected void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        // 拆分数量
        BigDecimal contractNum = contractEntity.getContractNum().subtract(contractModifyDTO.getModifyNum());
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        BigDecimal totalModifyNum = contractEntity.getTotalModifyNum().add(contractModifyDTO.getModifyNum());

        // 一口价合同拆分已定价量,但不会拆分定价单
        if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.subtract(contractModifyDTO.getModifyNum());
        } else {

            // 拆分包含已定量-前端传
            List<ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();
            log.info("split contractId:{},confirmPriceDTOList:{}", contractEntity.getId(), confirmPriceDTOList);

            if (CollectionUtil.isNotEmpty(confirmPriceDTOList)) {
                for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {
                    // 父合同减少已定价量
                    totalPriceNum = totalPriceNum.subtract(confirmPriceDTO.getConfirmNum());

                    TTPriceEntity ttPriceEntity = ttPriceService.getById(confirmPriceDTO.getTtPriceId());
                    if (null != ttPriceEntity) {
                        log.info("split contractId:{},ttPriceEntity:{}", contractEntity.getId(), ttPriceEntity.getId());
                        // 更新定价单
                        ttPriceService.updateTtPrice(ttPriceEntity.setNum(BigDecimal.ZERO));
                    }
                }
            }
        }

        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                .setTotalPriceNum(totalPriceNum)
                .setTotalModifyNum(totalModifyNum);

        // 拆分导致的原合同全部定价
        if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && BigDecimalUtil.isEqual(contractNum, totalPriceNum)
                && BigDecimalUtil.isGreaterThanZero(contractNum)) {
            // 原合同需要自动计算加权平均价
            List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : confirmPriceList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                // 更新期货价格
                contractPriceService.updateContractForwardPrice(contractEntity, averagePrice);
            }
        }

        // 拆分后原合同状态改为拆分中
        contractEntity.setStatus(ContractStatusEnum.SPLITTING.getValue());

        // 更新合同信息
        contractValueObjectService.updateContractById(contractEntity);
    }
}
