package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.bisiness.enums.TTWriteOffActionEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.CurrencyTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.UnitEnum;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/20
 * @Version 1.0
 */
@Slf4j
@Component
public class TTAddEntityConvert {

    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected WarehouseFacade warehouseFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected CustomerBankFacade customerBankFacade;
    @Autowired
    protected SkuFacade skuFacade;
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;
    @Autowired
    protected CustomerInvoiceFacade customerInvoiceFacade;


    /**
     * TT新增场景convert2TTAddEntity
     *
     * @param ttdto
     * @return
     */
    public TTAddEntity add2TTAddEntity(TTDTO ttdto) {
        SalesContractAddTTDTO addDto = ttdto.getSalesContractAddTTDTO();
        if (ContractSalesTypeEnum.SALES.getValue() == addDto.getSalesType()) {
            return spotSales2TTAddEntity(ttdto.getSalesContractAddTTDTO(), ttdto.getPriceDetailBO());
        } else {
            return spotPurchase2TTAddEntity(ttdto.getSalesContractAddTTDTO(), ttdto.getPriceDetailBO());
        }
    }

    /**
     * TT分配场景convert2TTAddEntity
     *
     * @param ttdto
     * @return
     */
    public TTAddEntity allocate2TTAddEntity(TTDTO ttdto) {
        return spotSales2TTAddEntity(ttdto.getSalesContractAddTTDTO(), ttdto.getPriceDetailBO());
    }

    /**
     * 现货销售新增convert2TTAddEntity
     *
     * @param salesContractAddTTDTO
     * @param priceDetailBO
     * @return
     */
    private TTAddEntity spotSales2TTAddEntity(SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO) {
        TTAddEntity ttAddEntity = new TTAddEntity();
        BeanUtils.copyProperties(salesContractAddTTDTO, ttAddEntity);
        //1、查询客户信息
        CustomerDTO customer = null;
        CustomerDetailEntity customerDetailEntities = null;
        if (null != salesContractAddTTDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
        }
        if (customer != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(customer.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setCustomerCode(customer.getLinkageCustomerCode());
            ttAddEntity.setCustomerId(customer.getId());
            ttAddEntity.setCustomerName(customer.getName());
            ttAddEntity.setEnterprise(customer.getEnterprise());
            if (TTTypeEnum.NEW.getType().equals(salesContractAddTTDTO.getType())) {
                CrisGlobalEntity crisGlobalEntity = customerFacade.getCustomerResidualRiskInfo(customer.getId());
                if (null != crisGlobalEntity) {
                    ttAddEntity.setResidualRiskLimit(null != crisGlobalEntity.getResidualRiskLimit() ? String.valueOf(crisGlobalEntity.getResidualRiskLimit()) : null)
                            .setResidualRiskUsage(null != crisGlobalEntity.getResidualRiskUsage() ? String.valueOf(crisGlobalEntity.getResidualRiskUsage()) : null)
                            .setResidualRiskResidue(null != crisGlobalEntity.getResidualRiskResidue() ? String.valueOf(crisGlobalEntity.getResidualRiskResidue()) : null)
                            .setResidualRiskTradeStatus(crisGlobalEntity.getTradeStatus());
                }
            }
        }

        //2、查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractAddTTDTO.getSupplierId()) {
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(salesContractAddTTDTO.getSupplierId())
                    .setCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                    .setFactoryCode(salesContractAddTTDTO.getDeliveryFactoryCode())
                    .setSalesType(salesContractAddTTDTO.getSalesType())
                    .setCompanyId(salesContractAddTTDTO.getCompanyId())
                    .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                    .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
            ;
            supplier = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        }
        if (supplier != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(supplier.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setSupplierId(supplier.getId());
            ttAddEntity.setSupplierName(supplier.getName());
            ttAddEntity.setSignPlace(supplier.getSignPlace());
            List<CustomerBankDTO> customerBankList = supplier.getCustomerBankDTOS();
            if (customerBankList != null && !customerBankList.isEmpty()) {
                ttAddEntity.setSupplierAccount(customerBankList.get(0).getBankAccountNo());
            }
        }
        //单位
        ttAddEntity.setUnit(UnitEnum.TON.name());

        //袋皮扣重【采销同适用】
        //      a.豆粕：当【袋皮扣重】选择不扣袋皮时，【包装计算重量】默认为否；当选择具体的值时，【包装计算重量】默认为是
        //      b.豆油：默认【包装计算重量】为否，袋皮扣重【不扣皮】
        ttAddEntity.setPackageWeight(salesContractAddTTDTO.getPackageWeight());
        Integer needPackageWeight = 0;
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(salesContractAddTTDTO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                String ruleKey = systemRuleItemEntity.getRuleKey();
                if (!"不扣皮".equalsIgnoreCase(ruleKey)) {
                    needPackageWeight = 1;
                }
            }
        }
        ttAddEntity.setNeedPackageWeight(needPackageWeight);

        //币种
        ttAddEntity.setCurrencyType(CurrencyTypeEnum.CNY.getDesc());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);

        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));

        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);

        // add by zengshl 修获取商品信息设置 获取商品信息
        if (ObjectUtil.isNotEmpty(salesContractAddTTDTO.getGoodsId())) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractAddTTDTO.getGoodsId());
            ttAddEntity.setGoodsName(skuEntity.getName());
            ttAddEntity.setGoodsPackageId(skuEntity.getPackageId());
//            ttAddEntity.setGoodsSpecId(skuEntity.getSpecId());
            ttAddEntity.setGoodsCategoryId(skuEntity.getCategory2());
            if (null != skuEntity.getTaxRate()) {
                //税率
                BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                ttAddEntity.setTaxRate(taxRate);
                //cifUnitPrice
                BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
                ttAddEntity.setCifUnitPrice(cifUnitPrice);
            }
        }

        // add by zengshl 发货库点交割库
        if (ObjectUtil.isNotEmpty(salesContractAddTTDTO.getShipWarehouseId())) {
            ttAddEntity.setShipWarehouseId(Integer.parseInt(salesContractAddTTDTO.getShipWarehouseId()));
            ttAddEntity.setShipWarehouseValue(salesContractAddTTDTO.getShipWarehouseName());
        }
        // 获取发票信息
        if (null != ttAddEntity.getCustomerId() && null != ttAddEntity.getGoodsCategoryId()) {
            customerDetailEntities = customerDetailFacade.queryCustomerDetailEntity(ttAddEntity.getCustomerId(), ttAddEntity.getGoodsCategoryId());
        }
        if (customerDetailEntities != null) {
            ttAddEntity.setQualityCheck(customerDetailEntities.getQualityCheckContent());
            //迟付款罚金
            ttAddEntity.setDelayPayFine(customerDetailEntities.getDeliveryDelayFine());
        }

        SystemRuleItemEntity systemRuleItemEntity = null;

        //查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(salesContractAddTTDTO.getCompanyId())
                .setCustomerId(salesContractAddTTDTO.getCustomerId())
                .setCategory1(String.valueOf(salesContractAddTTDTO.getCategory1()))
                .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (!customerInvoiceEntities.isEmpty()) {
            systemRuleItemEntity = systemRuleFacade.getRuleItemById(customerInvoiceEntities.get(0).getInvoiceId());
        }
        if (systemRuleItemEntity != null) {
            //发票类型
            //发票类型
            Integer invoiceType = Integer.parseInt(systemRuleItemEntity.getRuleKey());
            ttAddEntity.setInvoiceType(invoiceType);
            ttAddEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(invoiceType));
        }

        //合同总量
        BigDecimal contractNum = BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getContractNum());
        ttAddEntity.setContractNum(contractNum);

        //计算总价(确认无 temporaryPrice  transactionPrice)
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, unitPrice);
        ttAddEntity.setTotalAmount(totalAmount);

        //基差价
        ttAddEntity.setExtraPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getExtraPrice()));

        //期货价
        ttAddEntity.setForwardPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getForwardPrice()));

        //付款方式
        if (salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() != 0) {
            ttAddEntity.setPaymentType(PaymentTypeEnum.CREDIT.getType());
        } else {
            ttAddEntity.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        }
        //应付履约保证金
        ttAddEntity.setDepositAmount(BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getDepositAmount()));

        if (StringUtil.isNotBlank(salesContractAddTTDTO.getContractType())) {
            BigDecimal addedDepositAmount = calculateAddedDepositAmount(Integer.parseInt(salesContractAddTTDTO.getContractType()), totalAmount, contractNum);
            ttAddEntity.setAddedDepositAmount(addedDepositAmount);
        }

        //交货时间
        ttAddEntity.setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime());
        ttAddEntity.setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime());

        //点价截止时间
        ttAddEntity.setPriceEndTime(salesContractAddTTDTO.getPriceEndTime());

        //履约保证金比例id
        ttAddEntity.setDepositRate(salesContractAddTTDTO.getDepositRate());

        //期货合约
        ttAddEntity.setDomainCode(salesContractAddTTDTO.getDomainCode());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getShipWarehouseId())) {
            ttAddEntity.setShipWarehouseId(Integer.parseInt(salesContractAddTTDTO.getShipWarehouseId()));
        }
        ttAddEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttAddEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttAddEntity.setRootContractId(salesContractAddTTDTO.getRootContractId());
        if (salesContractAddTTDTO.getSourceContractId() != null) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractAddTTDTO.getSourceContractId());
            if (contractEntity != null) {
                ttAddEntity.setSourceContractNum(contractEntity.getContractNum());
            }
        }
        ttAddEntity.setWashoutPriceDetail(salesContractAddTTDTO.getWashoutPriceDetailBO());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getContractType())) {
            ttAddEntity.setContractType(Integer.parseInt(salesContractAddTTDTO.getContractType()));
        }
        //计算追加履约保证金比例
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(salesContractAddTTDTO.getCategory2())
                .setContractType(Integer.valueOf(salesContractAddTTDTO.getContractType()))
                .setDepositRate(salesContractAddTTDTO.getDepositRate())
                .setAddedDepositRate(salesContractAddTTDTO.getAddedDepositRate());
        ttAddEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // V1 值转对象 Author:zengshl 2024-06-18 start
        ttAddEntity.setDestinationValue(systemRuleConvertValue(ttAddEntity.getDestination()));
        ttAddEntity.setPackageWeightValue(systemRuleConvertValue(ttAddEntity.getPackageWeight()));
        ttAddEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttAddEntity.getDeliveryType()));
        ttAddEntity.setWeightCheckValue(systemRuleConvertValue(ttAddEntity.getWeightCheck()));

        ttAddEntity.setShipWarehouseValue(factoryConvertValue(ttAddEntity.getShipWarehouseId()));
        // V1 值转对象 Author:zengshl 2024-06-18 end
        ttAddEntity.setWarrantTradeType(salesContractAddTTDTO.getWarrantTradeType());
        ttAddEntity.setSettleType(salesContractAddTTDTO.getSettleType());
        ttAddEntity.setWriteOffStartTime(salesContractAddTTDTO.getWriteOffStartTime());
        ttAddEntity.setWriteOffEndTime(salesContractAddTTDTO.getWriteOffEndTime());
        ttAddEntity.setWarrantId(salesContractAddTTDTO.getWarrantId());
        ttAddEntity.setWarrantCode(salesContractAddTTDTO.getWarrantCode());
        return ttAddEntity;
    }

    /**
     * 现货采购新增convert2TTAddEntity
     *
     * @param salesContractAddTTDTO
     * @param priceDetailBO
     * @return
     */
    private TTAddEntity spotPurchase2TTAddEntity(SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO) {

        TTAddEntity ttAddEntity = new TTAddEntity();
        BeanUtils.copyProperties(salesContractAddTTDTO, ttAddEntity);
        ttAddEntity.setRootContractId(salesContractAddTTDTO.getRootContractId());
        //查询客户信息
        CustomerDTO customer = null;
        CustomerDetailEntity customerDetailEntities = null;
        if (null != salesContractAddTTDTO.getCustomerId()) {
            customer = customerFacade.getCustomerById(salesContractAddTTDTO.getCustomerId());
        }
        if (customer != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(customer.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setCustomerCode(customer.getLinkageCustomerCode());
            ttAddEntity.setCustomerId(customer.getId());
            ttAddEntity.setCustomerName(customer.getName());
            // BUGFIX：Case-1003197 TJIBSBOS2503315， TJIBSBOS2503316合同传输错误 Author: Mr 2025-05-13
            // ttAddEntity.setInvoiceType(customer.getInvoiceType());
            ttAddEntity.setEnterprise(customer.getEnterprise());
        }

        //迟付款罚金
        if (StringUtil.isNumeric(salesContractAddTTDTO.getDelayPayFine())) {
            ttAddEntity.setDelayPayFine(new BigDecimal(salesContractAddTTDTO.getDelayPayFine()));
        }
        //查询供应商信息
        CustomerDTO supplier = null;
        if (null != salesContractAddTTDTO.getSupplierId()) {
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(salesContractAddTTDTO.getSupplierId())
                    .setCategoryId(salesContractAddTTDTO.getGoodsCategoryId())
                    .setFactoryCode(salesContractAddTTDTO.getDeliveryFactoryCode())
                    .setSalesType(salesContractAddTTDTO.getSalesType())
                    .setCompanyId(salesContractAddTTDTO.getCompanyId())
                    .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                    .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
            ;
            supplier = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        }
        if (supplier != null) {
            if (DisableStatusEnum.DISABLE.getValue().equals(supplier.getStatus())) {
                throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
            }
            ttAddEntity.setSupplierId(supplier.getId());
            ttAddEntity.setSupplierName(supplier.getName());
            List<CustomerBankDTO> customerBankList = supplier.getCustomerBankDTOS();
            if (customerBankList != null && !customerBankList.isEmpty()) {
                ttAddEntity.setSupplierAccount(customerBankList.get(0).getBankAccountNo());
            }
        }
        if (salesContractAddTTDTO.getSupplierAccountId() != null) {
            CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(salesContractAddTTDTO.getSupplierAccountId());
            if (customerBankEntity != null) {
                ttAddEntity.setSupplierAccount(customerBankEntity.getBankAccountNo());
            }
        }
        //签订地
        ttAddEntity.setSignPlace(salesContractAddTTDTO.getSignPlace());

        //单位
        ttAddEntity.setUnit(UnitEnum.TON.name());

        //袋皮扣重【采销同适用】
        //      a.豆粕：当【袋皮扣重】选择不扣袋皮时，【包装计算重量】默认为否；当选择具体的值时，【包装计算重量】默认为是
        //      b.豆油：默认【包装计算重量】为否，袋皮扣重【不扣皮】
        ttAddEntity.setPackageWeight(salesContractAddTTDTO.getPackageWeight());
        Integer needPackageWeight = 0;
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(salesContractAddTTDTO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                String ruleKey = systemRuleItemEntity.getRuleKey();
                if (!"不扣皮".equalsIgnoreCase(ruleKey)) {
                    needPackageWeight = 1;
                }
            }
        }
        ttAddEntity.setNeedPackageWeight(needPackageWeight);

        //币种
        ttAddEntity.setCurrencyType(CurrencyTypeEnum.CNY.getDesc());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);

        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));

        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);

        // add by zengshl 修获取商品信息设置 获取商品信息
        if (ObjectUtil.isNotEmpty(salesContractAddTTDTO.getGoodsId())) {
            SkuEntity skuEntity = skuFacade.getSkuById(salesContractAddTTDTO.getGoodsId());
            ttAddEntity.setGoodsName(skuEntity.getFullName());
            ttAddEntity.setGoodsPackageId(skuEntity.getPackageId());
//            ttAddEntity.setGoodsSpecId(skuEntity.getSpecId());
            ttAddEntity.setGoodsCategoryId(skuEntity.getCategory2());
            if (null != skuEntity.getTaxRate()) {
                //税率
                BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                ttAddEntity.setTaxRate(taxRate);
                //cifUnitPrice
                BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
                ttAddEntity.setCifUnitPrice(cifUnitPrice);
            }
        }


        if (null != ttAddEntity.getSupplierId() && null != ttAddEntity.getGoodsCategoryId()) {
            //查询发票信息
            CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
            customerInvoiceDTO
                    .setCompanyId(salesContractAddTTDTO.getCompanyId())
                    .setCustomerId(salesContractAddTTDTO.getSupplierId())
                    .setCategory1(String.valueOf(salesContractAddTTDTO.getCategory1()))
                    .setCategory2(String.valueOf(salesContractAddTTDTO.getCategory2()))
                    .setCategory3(String.valueOf(salesContractAddTTDTO.getCategory3()))
            ;
            List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);

            int invoiceId = customerInvoiceEntities.isEmpty() ? 0 : customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (systemRuleItemEntity != null) {
                //发票类型
                ttAddEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
            }
        }

        //合同总量
        BigDecimal contractNum = BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getContractNum());
        ttAddEntity.setContractNum(contractNum);

        //计算总价(确认无 temporaryPrice  transactionPrice)
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, unitPrice);
        ttAddEntity.setTotalAmount(totalAmount);

        //迟付款罚金
        if (StringUtil.isNumeric(salesContractAddTTDTO.getDelayPayFine())) {
            ttAddEntity.setDelayPayFine(new BigDecimal(salesContractAddTTDTO.getDelayPayFine()));
        }

        //基差价
        ttAddEntity.setExtraPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getExtraPrice()));

        //期货价
        ttAddEntity.setForwardPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getForwardPrice()));
        //付款方式
        if (salesContractAddTTDTO.getCreditDays() != null && salesContractAddTTDTO.getCreditDays() != 0) {
            ttAddEntity.setPaymentType(PaymentTypeEnum.CREDIT.getType());
        } else {
            ttAddEntity.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        }
        //应付履约保证金
        ttAddEntity.setDepositAmount(BigDecimalUtil.initBigDecimal(salesContractAddTTDTO.getDepositAmount()));

        BigDecimal addedDepositAmount = calculateAddedDepositAmount(Integer.parseInt(salesContractAddTTDTO.getContractType()), totalAmount, contractNum);
        ttAddEntity.setAddedDepositAmount(addedDepositAmount);

        //交货时间
        ttAddEntity.setDeliveryStartTime(salesContractAddTTDTO.getDeliveryStartTime());
        ttAddEntity.setDeliveryEndTime(salesContractAddTTDTO.getDeliveryEndTime());

        //点价截止时间
        ttAddEntity.setPriceEndTime(salesContractAddTTDTO.getPriceEndTime());

        //履约保证金比例id
        ttAddEntity.setDepositRate(salesContractAddTTDTO.getDepositRate());

        //期货合约
        ttAddEntity.setDomainCode(salesContractAddTTDTO.getDomainCode());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getShipWarehouseId())) {
            ttAddEntity.setShipWarehouseId(Integer.parseInt(salesContractAddTTDTO.getShipWarehouseId()));
        }
        ttAddEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttAddEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (salesContractAddTTDTO.getSourceContractId() != null) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractAddTTDTO.getSourceContractId());
            if (contractEntity != null) {
                ttAddEntity.setSourceContractNum(contractEntity.getContractNum());
            }
        }
        ttAddEntity.setWashoutPriceDetail(salesContractAddTTDTO.getWashoutPriceDetailBO());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getContractType())) {
            ttAddEntity.setContractType(Integer.parseInt(salesContractAddTTDTO.getContractType()));
        }
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(salesContractAddTTDTO.getCategory2())
                .setContractType(Integer.valueOf(salesContractAddTTDTO.getContractType()))
                .setDepositRate(salesContractAddTTDTO.getDepositRate())
                .setAddedDepositRate(salesContractAddTTDTO.getAddedDepositRate());
        ttAddEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // V1 值转对象 Author:zengshl 2024-06-18 start
        ttAddEntity.setDestinationValue(systemRuleConvertValue(ttAddEntity.getDestination()));
        ttAddEntity.setPackageWeightValue(systemRuleConvertValue(ttAddEntity.getPackageWeight()));
        ttAddEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttAddEntity.getDeliveryType()));
        ttAddEntity.setWeightCheckValue(systemRuleConvertValue(ttAddEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        if (Objects.nonNull(ttAddEntity.getInvoiceType())) {
            ttAddEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(ttAddEntity.getInvoiceType()));
        }
        ttAddEntity.setShipWarehouseValue(factoryConvertValue(ttAddEntity.getShipWarehouseId()));
        // V1 值转对象 Author:zengshl 2024-06-18 end
        ttAddEntity.setDepositPaymentType(salesContractAddTTDTO.getDepositPaymentType());
        if (StringUtils.isNotBlank(salesContractAddTTDTO.getDeliveryMarginAmount())) {
            ttAddEntity.setDeliveryMarginAmount(new BigDecimal(salesContractAddTTDTO.getDeliveryMarginAmount()));
        }
        return ttAddEntity;
    }

    /**
     * 仓单注销convert2TTModifyEntity
     *
     * @param ttdto
     * @param tradeTicketEntity
     * @param salesType
     * @return
     */
    public TTAddEntity writeOff2TTAddEntity(TTDTO ttdto, TradeTicketEntity tradeTicketEntity, ContractSalesTypeEnum salesType) {
        TTAddEntity ttAddEntity = new TTAddEntity();
        ContractWriteOffDTO contractWriteOffDTO = ttdto.getContractWriteOffDTO();
        ContractEntity contractEntity = contractWriteOffDTO.getParentContractEntity();
        PriceDetailBO priceDetailBO;
        if (ContractSalesTypeEnum.SALES.equals(salesType)) {
            priceDetailBO = contractWriteOffDTO.getPriceDetailDTO();
        } else {
            priceDetailBO = contractWriteOffDTO.getContractWriteOffPurchaseDTO().getPriceDetailDTO();
        }

        ttAddEntity.setContractCode(tradeTicketEntity.getContractCode());
        ttAddEntity.setStatus(tradeTicketEntity.getStatus());
        ttAddEntity.setCustomerId(tradeTicketEntity.getCustomerId());
        ttAddEntity.setCustomerCode(tradeTicketEntity.getCustomerCode());
        ttAddEntity.setCustomerName(tradeTicketEntity.getCustomerName());
        ttAddEntity.setSupplierId(tradeTicketEntity.getSupplierId());
        ttAddEntity.setSupplierName(tradeTicketEntity.getSupplierName());
        ttAddEntity.setContractNum(contractWriteOffDTO.getWriteOffNum());
        ttAddEntity.setUnit(UnitEnum.TON.name());
        //币种
        ttAddEntity.setCurrencyType(CurrencyTypeEnum.CNY.getDesc());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);

        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));

        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);

        //商品信息
        ttAddEntity.setGoodsId(contractWriteOffDTO.getGoodsId());
        ttAddEntity.setGoodsName(contractWriteOffDTO.getGoodsName());
        //税率
        SkuEntity skuEntity = skuFacade.getSkuById(contractWriteOffDTO.getGoodsId());
        if (Objects.nonNull(skuEntity)) {
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            ttAddEntity.setTaxRate(taxRate);
            //cifUnitPrice
            BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
            ttAddEntity.setCifUnitPrice(cifUnitPrice);
        }
        //计算总价(确认无 temporaryPrice  transactionPrice)
        BigDecimal contractNum = contractWriteOffDTO.getWriteOffNum();
        BigDecimal totalAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, unitPrice);
        ttAddEntity.setTotalAmount(totalAmount);

        //基差价
        ttAddEntity.setExtraPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getExtraPrice()));

        //期货价
        ttAddEntity.setForwardPrice(BigDecimalUtil.initBigDecimal(priceDetailBO.getForwardPrice()));

        //付款方式
        if (contractWriteOffDTO.getCreditDays() != null && contractWriteOffDTO.getCreditDays() != 0) {
            ttAddEntity.setPaymentType(PaymentTypeEnum.CREDIT.getType());
        } else {
            ttAddEntity.setPaymentType(PaymentTypeEnum.IMPREST.getType());
        }
        //应付履约保证金
        ttAddEntity.setDepositAmount(BigDecimalUtil.initBigDecimal(contractWriteOffDTO.getDepositAmount()));
        BigDecimal addedDepositAmount = calculateAddedDepositAmount(ContractTypeEnum.YI_KOU_JIA.getValue(), totalAmount, contractNum);
        ttAddEntity.setAddedDepositAmount(addedDepositAmount);
        ttAddEntity.setShipWarehouseId(contractWriteOffDTO.getShipWarehouseId());
        ttAddEntity.setCreatedBy(tradeTicketEntity.getCreatedBy());
        ttAddEntity.setUpdatedBy(tradeTicketEntity.getCreatedBy());
        ttAddEntity.setRootContractId(contractWriteOffDTO.getContractId());
        ttAddEntity.setContractType(ContractTypeEnum.YI_KOU_JIA.getValue());
        ttAddEntity.setDestinationValue(systemRuleConvertValue(ttAddEntity.getDestination()));
        ttAddEntity.setPackageWeightValue(systemRuleConvertValue(ttAddEntity.getPackageWeight()));
        ttAddEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttAddEntity.getDeliveryType()));
        ttAddEntity.setWeightCheckValue(systemRuleConvertValue(ttAddEntity.getWeightCheck()));
        ttAddEntity.setShipWarehouseValue(factoryConvertValue(ttAddEntity.getShipWarehouseId()));
        ttAddEntity.setWarrantTradeType(contractWriteOffDTO.getWarrantTradeType());
        ttAddEntity.setWarrantId(contractEntity.getWarrantId());
        ttAddEntity.setWarrantCode(contractEntity.getWarrantCode());
        return ttAddEntity;
    }

    /**
     * 交提货方式：dbt_delivery_type
     * 值转对象 add by zengshl
     *
     * @param deliveryTypeId
     * @return
     */
    public String deliveryTypeConvertValue(Integer deliveryTypeId) {
        if (ObjectUtil.isNotEmpty(deliveryTypeId)) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(deliveryTypeId);
            return ObjectUtil.isNotEmpty(deliveryTypeEntity) ? deliveryTypeEntity.getName() : "";
        }
        return "";
    }


    /**
     * 规则值转对象 add by zengshl
     *
     * @param ruleItemId
     * @return
     */
    public String systemRuleConvertValue(String ruleItemId) {
        if (ObjectUtil.isNotEmpty(ruleItemId)) {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ruleItemId));
            return ObjectUtil.isNotEmpty(itemEntity) ? itemEntity.getRuleKey() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dba_factory_warehouse
     * 值转对象 add by zengshl
     *
     * @param factoryId
     * @return
     */
    public String factoryConvertValue(Integer factoryId) {
        if (ObjectUtil.isNotEmpty(factoryId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(factoryId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }


    /**
     * 计算履约保证金
     *
     * @param contractType
     * @param totalAmount
     * @param contractNum
     * @return
     */
    private static BigDecimal calculateAddedDepositAmount(int contractType, BigDecimal totalAmount, BigDecimal contractNum) {
        BigDecimal addedDepositAmount = BigDecimal.ZERO;
        switch (ContractTypeEnum.getByValue(contractType)) {
            case YI_KOU_JIA:
            case ZAN_DING_JIA:
                addedDepositAmount = totalAmount.multiply(BigDecimal.valueOf(0.05));
                break;
            case JI_CHA:
                addedDepositAmount = contractNum.multiply(BigDecimal.valueOf(150));
                break;
            default:
                break;
        }
        return addedDepositAmount;
    }


    /**
     * 作废子类DTO
     *
     * @param ttdto
     * @return
     */
    public TTAddEntity invalidTTAddEntity(TTDTO ttdto) {

        TTAddEntity ttAddEntity = new TTAddEntity();
        // add by zengshl 拷贝一子合同的一些字段信息
        ContractEntity contractEntity = ttdto.getContractEntity();
        if (ObjectUtil.isNotEmpty(ttdto.getContractEntity())) {
            BeanUtils.copyProperties(ttdto.getContractEntity(), ttAddEntity);
        }
        ttAddEntity.setContractId(contractEntity.getId());
        ttAddEntity.setContractCode(contractEntity.getContractCode());
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        PriceDetailBO priceDetailBO;
        priceDetailBO = ttdto.getPriceDetailBO();
        String currentUserId = JwtUtils.getCurrentUserId();
        if (StringUtils.isNotBlank(currentUserId)) {
            ttAddEntity.setCreatedBy(Integer.parseInt(currentUserId));
            ttAddEntity.setUpdatedBy(Integer.parseInt(currentUserId));
        }
        //含税单价 TODO 去掉
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);
        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));
        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);
        return ttAddEntity;
    }

    /**
     * 取消声明子类DTO
     *
     * @param ttdto
     * @return
     */
    public TTAddEntity cancelTTAddEntity(TTDTO ttdto) {

        TTAddEntity ttAddEntity = new TTAddEntity();
        // add by zengshl 拷贝一子合同的一些字段信息
        ContractEntity contractEntity = ttdto.getContractEntity();
        if (ObjectUtil.isNotEmpty(ttdto.getContractEntity())) {
            BeanUtils.copyProperties(ttdto.getContractEntity(), ttAddEntity);
        }
        ttAddEntity.setContractId(contractEntity.getId());
        ttAddEntity.setContractCode(contractEntity.getContractCode());
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        PriceDetailBO priceDetailBO;
        priceDetailBO = ttdto.getPriceDetailBO();
        String currentUserId = JwtUtils.getCurrentUserId();
        if (StringUtils.isNotBlank(currentUserId)) {
            ttAddEntity.setCreatedBy(Integer.parseInt(currentUserId));
            ttAddEntity.setUpdatedBy(Integer.parseInt(currentUserId));
        }
        //含税单价 TODO 去掉
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttAddEntity.setUnitPrice(unitPrice);
        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));
        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttAddEntity.setFobUnitPrice(fobUnitPrice);
        return ttAddEntity;
    }
}
