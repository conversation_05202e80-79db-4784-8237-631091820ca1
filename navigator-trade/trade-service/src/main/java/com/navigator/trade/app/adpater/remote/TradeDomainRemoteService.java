package com.navigator.trade.app.adpater.remote;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.LogTargetRecordTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import com.navigator.delivery.facade.IDeliveryBIQueryFacade;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.trade.pojo.dto.TradeSupportDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 集中外部接口的所有调用方法
 */
@Service
public class TradeDomainRemoteService {

    /**
     * 跨域获取配置-后续不断优化完善到Logic进行净化替换
     */
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerCreditPaymentFacade customerCreditPaymentFacade;
    @Autowired
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private CustomerDepositRateFacade customerDepositRateFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private PriceAllocateFacade priceAllocateFacade;
    @Autowired
    private CustomerInvoiceFacade customerInvoiceFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private ProteinPriceConfigFacade proteinPriceConfigFacade;
    @Autowired
    private QualityFacade qualityFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Autowired
    private WarehouseFacade warehouseFacade;
    @Autowired
    private WarrantFacade warrantFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Resource
    private ApproveFacade approveFacade;
    @Resource
    private FileBusinessFacade fileBusinessFacade;
    @Resource
    private IDeliveryBIQueryFacade iDeliveryBIQueryFacade;
    @Resource
    private RoleFacade roleFacade;
    @Autowired
    private AtlasContractFacade atlasContractFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;


    /**
     * 获取TT填充需要的其他域的数据信息
     *
     * @param tradeTicketEntity
     * @return
     */
    public TradeSupportDTO getTradeSupportDTO(TradeTicketEntity tradeTicketEntity) {
        TradeSupportDTO tradeSupportDTO = new TradeSupportDTO();

        String siteCode = tradeTicketEntity.getSiteCode();
        Integer customerId = tradeTicketEntity.getCustomerId();
        Integer category1 = tradeTicketEntity.getCategory1();
        Integer category2 = tradeTicketEntity.getCategory2();
        Integer category3 = tradeTicketEntity.getCategory3();
        Integer supplierId = tradeTicketEntity.getSupplierId();
        Integer skuId = tradeTicketEntity.getGoodsId();

        SiteEntity siteEntity = getSiteDetailByCode(siteCode);

        Integer companyId = siteEntity.getCompanyId();
        String factoryCode = siteEntity.getFactoryCode();

        CustomerDTO customerDTO = getCustomerDTO(customerId, category1, category2, category3, siteCode, companyId, factoryCode);
        CustomerDTO supplierDTO = getCustomerDTO(supplierId, category1, category2, category3, siteCode, companyId, factoryCode);
        SkuEntity skuEntity = getSkuEntity(skuId);

        CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailEntity(customerId, category3);

        tradeSupportDTO.setCustomerDTO(customerDTO);
        tradeSupportDTO.setSupplierDTO(supplierDTO);
        tradeSupportDTO.setSkuEntity(skuEntity);
        tradeSupportDTO.setSiteEntity(siteEntity);
        if (customerDetailEntity != null) {
            tradeSupportDTO.setCustomerDetailEntity(customerDetailEntity);
        }

        return tradeSupportDTO;
    }

    /**
     * 获取客户信息
     *
     * @param customerId
     * @return
     */
    public CustomerDTO getCustomerDTO(Integer customerId,
                                      Integer category1,
                                      Integer category2,
                                      Integer category3,
                                      String siteCode,
                                      Integer companyId,
                                      String factoryCode) {
        return customerFacade.getCustomerBizInfo(customerId, category2, category3, siteCode, companyId, factoryCode);
    }

    /**
     * 获取账套详细信息
     *
     * @param siteCode 账套编码
     * @return
     */
    public SiteEntity getSiteDetailByCode(String siteCode) {
        return siteFacade.getSiteDetailByCode(siteCode);
    }

    /**
     * 获取账套接触信息
     *
     * @param siteCode 账套编码
     * @return
     */
    public SiteEntity getSiteEntity(String siteCode) {
        return siteFacade.getSiteByCode(siteCode);
    }

    /**
     * 获取公司信息
     *
     * @param companyId
     * @return
     */
    public CompanyEntity getCompanyEntity(Integer companyId) {
        return companyFacade.queryCompanyById(companyId);
    }

    public SkuEntity getSkuEntity(Integer skuId) {
        return skuFacade.getSkuById(skuId);
    }

    public CustomerDetailEntity getCustomerDetailDTO(Integer customerId, Integer categoryId) {
        return customerDetailFacade.queryCustomerDetailEntity(customerId, categoryId);
    }

    /**
     * 获取当前用户的权限账套信息
     *
     * @return
     */
    public LinkedHashSet<String> getUserPermissionSite() {
        LinkedHashSet<String> noPermiss = new LinkedHashSet<>();
        noPermiss.add("-1");
        Result<LinkedHashSet<String>> result = roleFacade.queryRoleSiteCodeSet(Integer.valueOf(JwtUtils.getCurrentUserId()));
        if (result.isSuccess()) {
            LinkedHashSet<String> siteCodeHash = JSON.parseObject(JSON.toJSONString(result.getData()), LinkedHashSet.class);
            return ObjectUtil.isNotEmpty(siteCodeHash) ? siteCodeHash : noPermiss;
        }
        return noPermiss;
    }

    /**
     * 获取合同发票信息
     *
     * @param customerId
     * @param category1
     * @param category2
     * @param category3
     * @param companyId
     * @return
     */
    public List<CustomerInvoiceEntity> queryCustomerInvoiceList(Integer customerId,
                                                                Integer category1,
                                                                Integer category2,
                                                                Integer category3,
                                                                Integer companyId) {
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO.setCustomerId(customerId);
        customerInvoiceDTO.setCategory1(category1 + "");
        customerInvoiceDTO.setCategory2(category2 + "");
        customerInvoiceDTO.setCategory3(category3 + "");
        customerInvoiceDTO.setCompanyId(companyId);

        List<CustomerInvoiceEntity> list = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        return list;
    }


    /**
     * 查询库点名称
     *
     * @param warehouseId
     * @return
     */
    public WarehouseEntity getFactoryWarehouse(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity : null;
            }
        }
        return null;
    }

    /**
     * 规则值转对象 add by zengshl
     *
     * @param ruleItemId
     * @return
     */
    public String systemRuleConvertValue(String ruleItemId) {
        if (ObjectUtil.isNotEmpty(ruleItemId)) {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ruleItemId));
            return ObjectUtil.isNotEmpty(itemEntity) ? itemEntity.getRuleKey() : "";
        }
        return "";
    }

    /**
     * 获取客户实体
     *
     * @param customerId
     * @return
     */
    public CustomerEntity getCustomerEntity(Integer customerId) {
        return customerFacade.getCustomerById(customerId);
    }

    public Result customerCreditPayment(CustomerCreditPaymentDTO creditPaymentDTO) {
        return customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
    }

    /**
     * TT的操作日志
     *
     * @param operationActionEnum
     * @param tt
     */
    @Async
    public void recordTTOperationDetail(OperationActionEnum operationActionEnum, TradeTicketEntity tt) {
        OperationDetailDTO operationDetailDTO = buildTTOperationDetail(operationActionEnum, tt);
        operationLogFacade.recordOperationLogDetail(operationDetailDTO);
    }

    @Async
    public OperationDetailDTO buildTTOperationDetail(OperationActionEnum operationActionEnum, TradeTicketEntity tt) {
        OperationSourceEnum logLevelEnum = OperationSourceEnum.getByName(operationActionEnum.getLogLevel());
        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tt.getTradeType());
        return new OperationDetailDTO()
                .setBizCode(operationActionEnum.getCode())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getDesc())
                .setLogLevel(logLevelEnum.getValue())
                .setSource(logLevelEnum.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorId(tt.getCreatedBy())
                .setOperationName(operationActionEnum.getAction())
                .setMetaData(null)
                .setData(JSON.toJSONString(tt))
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId(tt.getId())
                .setReferBizCode(tt.getCode())
                .setTargetRecordId(tt.getContractId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name())
                .setTtCode(tt.getCode())
                .setTradeTypeName(contractTradeTypeEnum.getDesc());
    }

    /**
     * 统一记录存储文件关系
     *
     * @param fileBusinessRelationDTO 记录附件关系请求信息
     */
    public void recordFileRelation(FileBusinessRelationDTO fileBusinessRelationDTO) {
        fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);
    }


    /**
     * 取消工作量引擎
     *
     * @param memo
     * @param userId
     * @param tradeTicketEntity
     */
    public void cancelActiviti(String memo, String userId, TradeTicketEntity tradeTicketEntity) {
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setTaskId("");
        String code = tradeTicketEntity.getCode();
        approveDTO
                .setUserId(userId)
                .setBizCode(code)
                .setMemo(memo)
                .setCategory1(tradeTicketEntity.getCategory1())
                .setCategory2(tradeTicketEntity.getCategory2())
                .setCategory3(tradeTicketEntity.getCategory3())
                .setSalesTypeEnum(ContractSalesTypeEnum.getByValue(tradeTicketEntity.getSalesType()))
                .setContractTradeTypeEnum(ContractTradeTypeEnum.getByValue(tradeTicketEntity.getTradeType()))
                .setActionName("TT撤回");
        approveFacade.cancel(approveDTO);
    }

    /**
     * 重构合同的操作日志，主要需要从TT发起
     *
     * @param tradeTicketEntity
     * @param bizCodeEnum
     * @param data
     * @param systemEnum
     */
    public void addContractOperationLog(TradeTicketEntity tradeTicketEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(tradeTicketEntity.getId())
                .setReferBizCode(tradeTicketEntity.getCode())
                .setTargetRecordId(tradeTicketEntity.getContractId())
                .setTtCode(tradeTicketEntity.getCode())
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setTargetRecordType(ModuleTypeEnum.CONTRACT.getModule())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setTradeTypeName(bizCodeEnum.getMsg())
                .setData(data)
                .setTriggerSys(SystemEnum.getByValue(systemEnum).getDescription());
        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                    .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                    .setSource(OperationSourceEnum.SYSTEM.getValue())
                    .setOperatorId(1);
        }
        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    /**
     * 获取头寸数据信息
     *
     * @param allocateId
     * @return
     */
    public PriceAllocateEntity getPriceAllocateById(String allocateId) {
        return priceAllocateFacade.getPriceAllocateById(allocateId);
    }

    /**
     * 查询BI的提货数据
     *
     * @param pickQtyQO
     * @return
     */
    public ExchangePickQtyDTO getExchangePickQty(ExchangePickQtyQO pickQtyQO) {
        List<ExchangePickQtyDTO> pickQtyDTOList = iDeliveryBIQueryFacade.getExchangePickQty(pickQtyQO);
        return ObjectUtil.isNotEmpty(pickQtyDTOList) ? pickQtyDTOList.get(0) : null;
    }

    /**
     * 获取规则配置
     *
     * @param ruleItemId
     * @return
     */
    public SystemRuleItemEntity getRuleItemById(Integer ruleItemId) {
        return systemRuleFacade.getRuleItemById(ruleItemId);
    }

    public Boolean judgeExistQuality(QualityInfoDTO qualityInfoDTO) {
        return qualityFacade.judgeExistQuality(qualityInfoDTO);
    }

    public CustomerBankEntity queryCustomerBankById(Integer bankId) {
        return customerBankFacade.queryCustomerBankById(bankId);
    }

    public CrisGlobalEntity getCustomerResidualRiskInfo(Integer customerId) {
        return customerFacade.getCustomerResidualRiskInfo(customerId);
    }

    public Result<PayConditionEntity> getPayConditionById(Integer payConditionId) {
        return payConditionFacade.getPayConditionById(payConditionId);
    }

    /**
     * 查询客户履约保证金
     *
     * @param customerDepositRateDTO
     * @return
     */
    public List<CustomerDepositRateEntity> queryCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        return customerDepositRateFacade.queryCustomerDepositRate(customerDepositRateDTO);
    }

    public Result filterBasicPrice(BasicPriceConfigQueryDTO systemRuleDTO) {
        return systemRuleFacade.filterBasicPrice(systemRuleDTO);
    }

    /**
     * 蛋白价差
     *
     * @param systemRuleDTO
     * @return
     */
    public Result filterBasicProtein(BasicPriceConfigQueryDTO systemRuleDTO) {
        return proteinPriceConfigFacade.filterBasicProtein(systemRuleDTO);
    }

    /**
     * 根据合同编码获取注销记录信息
     *
     * @param contractCode
     * @return
     */
    public WarrantCancellationEntity queryWarrantCancellation(String contractCode) {
        return warrantFacade.queryWarrantCancellation(contractCode);
    }

    /**
     * 基差价阀值的校验
     *
     * @param priceConfigQueryDTO
     * @param ttExtraPrice
     * @param salesType
     * @return
     */
    public boolean basePriceWarning(BasicPriceConfigQueryDTO priceConfigQueryDTO, BigDecimal ttExtraPrice, Integer salesType) {

        if (ObjectUtil.isNotEmpty(ttExtraPrice)) {
            return false;
        }
        if (null != priceConfigQueryDTO.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(priceConfigQueryDTO.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    priceConfigQueryDTO.setAttributeValueId(skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId());
                }
            }
        }
        Result result = systemRuleFacade.filterBasicPrice(priceConfigQueryDTO);
        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);
        if (null == systemRuleItemEntity) {
            return false;
        }
        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于基差价
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //基差价
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());
            BigDecimal extraPrice = ruleValue.subtract(memo);
            if (ContractSalesTypeEnum.SALES.getValue() == salesType) {
                return ttExtraPrice.compareTo(extraPrice) < 0;
            } else {
                return ttExtraPrice.compareTo(extraPrice) > 0;
            }
        }
        return false;
    }


    /**
     * 蛋白价差的校验
     *
     * @param basicPriceConfigQueryDTO
     * @param ttProteinDiffPrice
     * @param salesType
     * @return
     */
    public BigDecimal LOAProteinDiffPrice(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO, BigDecimal ttProteinDiffPrice, Integer salesType) {

        if (null != basicPriceConfigQueryDTO.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(basicPriceConfigQueryDTO.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    basicPriceConfigQueryDTO.setAttributeValueId(skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId());
                }
            }
        }
        Result result = proteinPriceConfigFacade.filterBasicProtein(basicPriceConfigQueryDTO);
        ObjectMapper mapper = new ObjectMapper();
        SystemRuleItemEntity systemRuleItemEntity = mapper.convertValue(result.getData(), SystemRuleItemEntity.class);
        if (null == systemRuleItemEntity) {
            return BigDecimal.ZERO;
        }
        if (StrUtil.isNotBlank(systemRuleItemEntity.getMemo()) && StrUtil.isNotBlank(systemRuleItemEntity.getRuleValue())) {
            //低于蛋白价差范围预警值
            BigDecimal memo = new BigDecimal(systemRuleItemEntity.getMemo());
            //蛋白价差
            BigDecimal ruleValue = new BigDecimal(systemRuleItemEntity.getRuleValue());
            BigDecimal proteinPrice = ruleValue.subtract(memo);
            if (ContractSalesTypeEnum.SALES.getValue() == salesType) {
                return ttProteinDiffPrice.compareTo(proteinPrice) < 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            } else {
                return ttProteinDiffPrice.compareTo(proteinPrice) > 0 ? BigDecimal.ONE : BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取Atlas的可提量
     *
     * @param contractEntity
     * @return
     */
    public Result<BigDecimal> getContractOpenQuantity(ContractEntity contractEntity) {
        SiteEntity siteEntity = this.getSiteEntity(contractEntity.getSiteCode());
        Result<BigDecimal> result = atlasContractFacade.getContractOpenQuantity(siteEntity.getAtlasCode(), contractEntity.getContractCode());
        return result;
    }

    /**
     * 合同是是否被头寸分配掉
     *
     * @param contractId
     * @return
     */
    public boolean getNotAllocateByContractId(Integer contractId) {
        return priceAllocateFacade.getNotAllocateByContractId(contractId);
    }

    /**
     * 获取客户配置所有的数据新
     *
     * @param customerAllMessageDTO
     * @return
     */
    public CustomerDTO queryCustomerAllMessage(CustomerAllMessageDTO customerAllMessageDTO) {
        return customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
    }

    /**
     * 获取配置工程编码
     *
     * @param factoryCode
     * @return
     */
    public FactoryEntity getFactoryByCode(String factoryCode) {
        return factoryWarehouseFacade.getFactoryByCode(factoryCode);
    }
}
