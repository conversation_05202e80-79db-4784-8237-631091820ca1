package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.SplitLogicService;
import com.navigator.trade.app.contract.logic.service.handler.StructureLogicService;
import com.navigator.trade.app.sign.domain.service.ContractSignQueryDomainService;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.AddedDepositRate2RuleDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.utils.AddedDepositRate2CalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

/**
 * 合同拆分业务逻辑 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class SplitLogicServiceImpl implements SplitLogicService {

    /**
     * 合同域
     */
    @Autowired
    private ContractDomainService contractDomainService;
    /**
     * 合同域查询通用
     */
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    /**
     * 域内服务调用
     */
    @Autowired
    private StructureLogicService structureLogicService;
    @Autowired
    private CommonLogicService commonLogicService;
    /**
     * 跨域调用
     */
    @Autowired
    private CustomerCreditPaymentFacade customerCreditPaymentFacade;
    @Autowired
    private QualityFacade qualityFacade;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    private WarehouseFacade warehouseFacade;
    @Autowired
    private CustomerInvoiceFacade customerInvoiceFacade;
    @Autowired
    private ContractSignQueryDomainService contractSignQueryDomainService;


    @Override
    public void splitContractCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {

        /******************************** 拆分保存校验 ****************************************/

        // 保存拆分需要校验是否多次提交，其他条件不校验
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
            if (StringUtils.isNotBlank(saveTimes)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_SAVE_TT);
            }
            return;
        }

        /******************************** 普通拆分校验 ****************************************/

        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        Integer customerId = contractModifyDTO.getCustomerId();
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            customerId = contractModifyDTO.getSupplierId();
        }

        // 判断客户是否可用
        if (!commonLogicService.isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
        }

        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.RISK_RESIDUAL_NOT_GET);
        }

        // 合同状态处于生效中
        if (!Arrays.asList(ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.SPLITTING.getValue()).contains(contractEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_SPLITTING);
        }

        // 校验付款代码是否禁用
        Result<PayConditionEntity> payCondition = payConditionFacade.getPayConditionById(contractModifyDTO.getPayConditionId());
        if (payCondition.isSuccess()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payCondition.getData()), PayConditionEntity.class);
            if (payConditionEntity.getStatus() == 0) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_ENABLE);
            }
            if (!contractEntity.getBuCode().equals(payConditionEntity.getBuCode())) {
                throw new BusinessException(ResultCodeEnum.PAY_CONDITION_IS_NOT_BUCODE);
            }
        }


        //3. 校验SKU是否有效
        SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
        if (skuEntity == null || DisableStatusEnum.DISABLE.getValue().equals(skuEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.GOODS_NOT_COMPLETED);
        }
        //4. 校验该SKU的品种是否绑定选定帐套
        SiteEntity siteEntity = siteFacade.getSiteDetailByCode(contractEntity.getSiteCode());
        if (siteEntity == null || !siteEntity.getCategory3().contains(contractEntity.getCategory3().toString())) {
            throw new BusinessException(ResultCodeEnum.GOODS_NOT_COMPLETED);
        }

        // 校验客户主数据赊销账期/预付款
        if (contractModifyDTO.getPaymentType().equals(PaymentTypeEnum.CREDIT.getType())) {
            // 获取主数据
            CustomerCreditPaymentDTO creditPaymentDTO = new CustomerCreditPaymentDTO();
            creditPaymentDTO.setCustomerId(customerId)
                    .setCategoryId(contractModifyDTO.getGoodsCategoryId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setCategory1(String.valueOf(contractEntity.getCategory1()))
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
                    .setBuCode(contractEntity.getBuCode())
                    .setIsSales(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                    .setIsProcurement(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue());
            Result result = customerCreditPaymentFacade.customerCreditPaymentAllList(creditPaymentDTO);
            if (result.isSuccess()) {
                List<CustomerCreditPaymentVO> creditPaymentVOList = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerCreditPaymentVO.class);
                if (CollectionUtil.isNotEmpty(creditPaymentVOList)) {
                    CustomerCreditPaymentVO customerCreditPaymentVO = creditPaymentVOList.get(0);
                    // 客户主数据赊销天数修改：合同赊销账期天数＞客户主数-赊销账期天数，系统提醒"客户主数据配置，付款方式已更新为：最多可赊销{x}天”，不允许提交。
                    if (contractModifyDTO.getCreditDays() > customerCreditPaymentVO.getCreditDays()) {
                        String message = "最多可赊销{" + customerCreditPaymentVO.getCreditDays() + "}天";
                        throw new BusinessException(ResultCodeEnum.CUSTOMER_PAYMENT_CREDIT_HAS_CHANGE, message);
                    }
                }
            }
        }

        //6. 校验业务配置-袋皮扣重配置是否有效
        if (StringUtils.isNotBlank(contractModifyDTO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractModifyDTO.getPackageWeight()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_PACKAGE_WEIGHT);
            }
        }

        //8. 校验通用配置-交提货方式配置是否有效
        if (ObjectUtil.isNotEmpty(contractModifyDTO.getDeliveryType())) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(contractModifyDTO.getDeliveryType());
            if (ObjectUtil.isEmpty(deliveryTypeEntity) || DisableStatusEnum.DISABLE.getValue().equals(deliveryTypeEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_DELIVERY);
            }
        }
        //9. 校验业务配置-重量验收是否有效
        if (StringUtils.isNotBlank(contractModifyDTO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractModifyDTO.getWeightCheck()));
            if (ObjectUtil.isEmpty(systemRuleItemEntity) || DisableStatusEnum.DISABLE.getValue().equals(systemRuleItemEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.TT_WEIGHT);
            }
        }

        //10.TODO 校验通用配置-库点，库点属性 | 校验该库点是否绑定选定帐套 前端绑定了
        if (ObjectUtil.isNotEmpty(contractModifyDTO.getShipWarehouseId())) {
            Result<WarehouseEntity> warehouseEntityResult = warehouseFacade.getWarehouseById(Integer.valueOf(contractModifyDTO.getShipWarehouseId()));
            if (warehouseEntityResult.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(warehouseEntityResult.getData()), WarehouseEntity.class);
                if (null == warehouseEntity
                        || DisableStatusEnum.DISABLE.getValue().equals(warehouseEntity.getStatus())
                        || ObjectUtil.isEmpty(warehouseEntity.getSiteCodes())
                        || (ObjectUtil.isNotEmpty(warehouseEntity.getSiteCodes()) && !warehouseEntity.getSiteCodes().contains(contractModifyDTO.getSiteCode()))) {
                    throw new BusinessException(ResultCodeEnum.TT_WAREHOUSE);
                }
            }
        }

        //校验质量指标
        QualityInfoDTO qualityInfoDTO = new QualityInfoDTO();
        qualityInfoDTO
                .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                .setWarehouseId(Integer.valueOf(contractModifyDTO.getShipWarehouseId()))
                .setUsage(contractModifyDTO.getUsage())
                .setGoodsId(contractModifyDTO.getGoodsId())
                .setSalesType(contractEntity.getSalesType())
                .setCustomerId(customerId);
        // 特种油脂
        if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(contractEntity.getCategory2())) {
            if ("国标".equals(contractModifyDTO.getStandardType()) || ObjectUtil.isEmpty(contractModifyDTO.getStandardRemark())) {
                qualityInfoDTO.setStandardType(contractModifyDTO.getStandardType());
            }
        }
        Boolean existQuality = qualityFacade.judgeExistQuality(qualityInfoDTO);
        if (!existQuality) {
            throw new BusinessException(ResultCodeEnum.QUALITY_NOT_COMPLETED);
        }

        // 15.验证发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (customerInvoiceEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.TT_INVOICE);
        }

        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 Start
        // 16.校验履约保证金释放方式是否正确
        if (contractModifyDTO.getDepositReleaseType() == null || contractModifyDTO.getDepositReleaseType() == 0) {
            throw new BusinessException(ResultCodeEnum.TT_DEPOSIT_RELEASE_TYPE);
        }
        // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18 End

        // 普通拆分
        BigDecimal maxNum = contractEntity.getTotalBillNum();

        // 申请提货数量
        if (CollectionUtil.isEmpty(contractModifyDTO.getConfirmPriceDTOList())) {
            ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO()
                    .setDomainCode(contractEntity.getDomainCode())
                    .setSalesType(contractEntity.getSalesType())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setCustomerId(String.valueOf(contractEntity.getCustomerId()));
            if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
                contractFuturesDTO.setCustomerId(String.valueOf(contractEntity.getSupplierId()));
            }
            if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
                maxNum = contractEntity.getTotalPriceNum();
            } else if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
            }

            if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(maxNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
            }

            if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(contractEntity.getApplyDeliveryNum()).subtract(maxNum))) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
            }

            // 尾量关闭校验
            if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
            }
        } else {
            // 校验定价单数量 --抽离app层校验
            /*contractModifyDTO.getConfirmPriceDTOList().forEach(confirmPriceDTO -> {
                Integer ttPriceId = confirmPriceDTO.getTtPriceId();
                TTPriceEntity ttPriceEntity = ttPriceService.getById(ttPriceId);
                if (null != ttPriceEntity && BigDecimalUtil.isGreater(confirmPriceDTO.getConfirmNum(), ttPriceEntity.getNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TOTAL_PRICE_NUM_EXCEPTION);
                }
            });*/

            // 全量拆分一口价
            if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                maxNum = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
                if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(maxNum))) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
                }

                if (BigDecimalUtil.isGreater(contractModifyDTO.getModifyNum(), contractEntity.getContractNum().subtract(contractEntity.getApplyDeliveryNum()).subtract(maxNum))) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
                }

                // 尾量关闭校验
                if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
                }
            }
        }

        // 校验是否结构化定价中 前提条件： 1.变更主体 2.基差合同 3.销售合同
        if (!contractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId())
                && ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()
                && contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            Boolean verified = structureLogicService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractEntity.getContractNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );
            if (Boolean.TRUE.equals(verified)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildBaseInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        ContractEntity parentContractEntity = contractModifyDTO.getParentContractEntity();

        // 区分采销，判断主体是否修改
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == parentContractEntity.getSalesType();
        boolean isCustomerChanged = isSales ?
                !parentContractEntity.getCustomerId().equals(contractModifyDTO.getCustomerId()) :
                !parentContractEntity.getSupplierId().equals(contractModifyDTO.getSupplierId());

        // 设置合同修改来源
        int contractSource = isCustomerChanged ? ContractActionEnum.SPLIT_CUSTOMER.getActionValue() : ContractActionEnum.SPLIT.getActionValue();

        // 合同的来源 REVISE_CUSTOMER
        if (isCustomerChanged
                && contractModifyDTO.getContractSource() != null
                && contractModifyDTO.getContractSource() == ContractActionEnum.REVISE_CUSTOMER.getActionValue()) {
            contractSource = ContractActionEnum.REVISE_CUSTOMER.getActionValue();
        }

        BeanUtils.copyProperties(parentContractEntity, contractEntity);
        BeanUtils.copyProperties(contractModifyDTO, contractEntity);

        // 处理基本数据
        String sonContractCode = "";
        // 保存不生成编号
        if (contractModifyDTO.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            sonContractCode = contractQueryDomainService.genSonContractCode(parentContractEntity.getContractCode(), parentContractEntity.getSalesType());
        }

        contractEntity.setId(null)
                .setUsage(contractModifyDTO.getUsage())
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setContractType(contractModifyDTO.getSonContractType())
                .setParentId(parentContractEntity.getId())
                .setRootId(parentContractEntity.getParentId() == 0 ? parentContractEntity.getId() : parentContractEntity.getParentId())
                .setShipWarehouseId(Integer.valueOf(contractModifyDTO.getShipWarehouseId()))
                .setStatus(ContractStatusEnum.INEFFECTIVE.getValue())
                .setMemo(contractModifyDTO.getRemark())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setContractSource(contractSource)
                .setCreateBatch(null);

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Inherit group_id from parent contract for split operations
        if (parentContractEntity.getGroupId() != null) {
            contractEntity.setGroupId(parentContractEntity.getGroupId());
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
    }

    @Override
    public void buildBizInfo(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 是否是销售合同
        boolean isSales = ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType();

        int customerId = isSales ? contractModifyDTO.getCustomerId() : contractModifyDTO.getSupplierId();

        // 账套更新
        if (StringUtils.isNotBlank(contractEntity.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(contractModifyDTO.getSiteCode());
            contractEntity
                    .setSiteCode(siteEntity.getCode())
                    .setSiteName(siteEntity.getName())
                    .setBelongCustomerId(siteEntity.getBelongCustomerId())
                    .setDeliveryFactoryCode(siteEntity.getFactoryCode())
                    .setDeliveryFactory(siteEntity.getFactoryCode())
                    .setDeliveryFactoryName(siteEntity.getFactoryName())
                    .setCompanyId(siteEntity.getCompanyId())
                    .setCompanyName(siteEntity.getCompanyName());
        }

        // 处理交易类型
        int tradeType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        if (contractEntity.getContractSource() == ContractActionEnum.REVISE_CUSTOMER.getActionValue()) {
            tradeType = ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue();
        } else if (contractEntity.getContractSource() == ContractActionEnum.SPLIT_CUSTOMER.getActionValue()) {
            tradeType = ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue();
        }

        ContractEntity parentContractEntity = contractModifyDTO.getParentContractEntity();
        // 客户主体变更
        commonLogicService.updateCustomerInfo(parentContractEntity.getCustomerId(), parentContractEntity.getSupplierId(),
                contractModifyDTO.getCustomerId(), contractModifyDTO.getSupplierId(), contractEntity);
        contractEntity.setSupplierAccountId(contractModifyDTO.getSupplierAccountId());

        // 商品更新
        if (contractEntity.getGoodsId() != null) {
            SkuEntity skuEntity = skuFacade.getSkuById(contractModifyDTO.getGoodsId());
            contractEntity.setGoodsId(contractModifyDTO.getGoodsId())
                    .setGoodsCategoryId(skuEntity.getCategory2())
                    .setGoodsPackageId(skuEntity.getPackageId())
                    .setGoodsSpecId(skuEntity.getSpecId())
                    .setGoodsName(skuEntity.getFullName())
                    .setCategory1(skuEntity.getCategory1())
                    .setCategory2(skuEntity.getCategory2())
                    .setCategory3(skuEntity.getCategory3());
            // 税率
            if (skuEntity.getTaxRate() != null) {
                BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                contractEntity.setTaxRate(taxRate);
            }
            contractModifyDTO.setGoodsCategoryId(skuEntity.getCategory2());

            contractEntity.setCommodityName(contractModifyDTO.getCommodityName() == null ? parentContractEntity.getCommodityName() : contractModifyDTO.getCommodityName());

            CustomerDetailEntity customerDetailEntities = customerDetailFacade.queryCustomerDetailEntity(customerId, skuEntity.getCategory3());
            if (null != customerDetailEntities) {
                contractEntity.setQualityCheck(customerDetailEntities.getQualityCheckContent());
                //迟付款罚金
                contractEntity.setDelayPayFine(customerDetailEntities.getDeliveryDelayFine());
            }
        }

        // 查询发票信息
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);

        if (!customerInvoiceEntities.isEmpty()) {
            int invoiceId = customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (null != systemRuleItemEntity) {
                //发票类型
                Integer invoiceType = Integer.parseInt(systemRuleItemEntity.getRuleKey());
                contractEntity.setInvoiceType(invoiceType);
                contractEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(invoiceType));
            }
        }

        // 处理点价截止日期
        String priceEndTime = contractModifyDTO.getPriceEndTime();

        if (DateTimeUtil.isDate(priceEndTime)) {
            contractEntity.setPriceEndType(ContractPriceEndTypeEnum.DATE.getValue());
        } else {
            contractEntity.setPriceEndType(ContractPriceEndTypeEnum.TEXT.getValue());
        }

        // 转月次数
        int totalTransferTimes = 1;
        int totalReversePriceTimes = 0;
        if (contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            // 获取客户属性
            List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(new CustomerDetailBO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setCategory2(String.valueOf(contractEntity.getCategory2()))
                    .setCategory3(String.valueOf(contractEntity.getCategory3()))
            );
            if (!customerDetailEntityList.isEmpty()) {
                CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
                // 普通用户考虑超远期合同
                if (null != customerDetailEntity) {
                    // 转月次数
                    totalTransferTimes = customerDetailEntity.getIsWhiteList() == 1 ? 4 : (parentContractEntity.getIsOverForward() == 1 ? 2 : 1);

                    // 再判断是否开通反点价权限
                    if (customerDetailEntity.getIsReversePrice() == 1) {
                        totalReversePriceTimes = 1;
                    }
                }
            } else {
                // 没有客户配置，默认转月次数
                totalTransferTimes = parentContractEntity.getIsOverForward() == 1 ? 2 : 1;
            }

            contractEntity.setAbleTransferTimes(Math.max(totalTransferTimes - contractEntity.getTransferredTimes(), 0))
                    .setTotalTransferTimes(totalTransferTimes)
                    .setIsOverForward(parentContractEntity.getIsOverForward())
                    .setAbleReversePriceTimes(Math.max(totalReversePriceTimes - contractEntity.getReversedPriceTimes(), 0))
                    .setTotalReversePriceTimes(totalReversePriceTimes);
        }

        // 交货工厂是否变更
        int isChangeFactory = parentContractEntity.getDeliveryFactoryCode().equals(contractModifyDTO.getDeliveryFactoryCode()) ? 0 : 1;

        // 开户行变更
        if (!parentContractEntity.getDeliveryFactoryCode().equals(contractModifyDTO.getDeliveryFactoryCode())
                && parentContractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(parentContractEntity.getSupplierId())
                    .setCompanyId(parentContractEntity.getCompanyId())
                    .setCategoryId(parentContractEntity.getGoodsCategoryId())
                    .setFactoryCode(contractModifyDTO.getDeliveryFactoryCode())
                    .setSalesType(parentContractEntity.getSalesType())
                    .setCategory2(String.valueOf(parentContractEntity.getCategory2()))
                    .setCategory3(String.valueOf(parentContractEntity.getCategory3()))
            ;
            CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
            if (null != customerDTO) {
                List<CustomerBankDTO> customerBankDTOS = customerDTO.getCustomerBankDTOS();
                if (CollectionUtil.isNotEmpty(customerBankDTOS)) {
                    contractEntity.setSupplierAccount(customerBankDTOS.get(0).getBankAccountNo())
                            .setSupplierAccountId(customerBankDTOS.get(0).getId());
                }
            }
        }

        // 拆分包含已定量
        BigDecimal totalPriceNum = BigDecimal.ZERO;
        List<ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();

        // 原合同类型
        Integer originContractType = parentContractEntity.getOriginContractType();

        if (CollectionUtil.isNotEmpty(confirmPriceDTOList)) {

            originContractType = parentContractEntity.getContractType();

            totalPriceNum = confirmPriceDTOList.stream()
                    .map(ConfirmPriceDTO::getConfirmNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 一口价合同拆分已定价量
        if (parentContractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.add(contractModifyDTO.getModifyNum());
        }

        // 价格处理
        PriceDetailBO priceDetailBO = contractModifyDTO.getPriceDetailDTO();
        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 重新汇总数据
        contractEntity.setOrderNum(contractModifyDTO.getModifyNum())
                .setContractNum(contractModifyDTO.getModifyNum())
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractModifyDTO.getModifyNum(), contractModifyDTO.getUnitPrice()))
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setTotalPriceNum(totalPriceNum)
                .setTotalModifyNum(BigDecimal.ZERO)
                .setTotalTransferNum(BigDecimal.ZERO)
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setExtraPrice(priceDetailBO.getExtraPrice())
                .setBaseDiffPrice(priceDetailBO.getExtraPrice())
                .setIsChangeFactory(isChangeFactory)
                .setTradeType(tradeType)
                .setOriginContractType(originContractType);

        // 签订总金额
        contractEntity.setOrderAmount(contractEntity.getTotalAmount());

        // 付款方式
        contractEntity.setPaymentType(contractModifyDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

        // 袋皮扣重
        contractEntity.setPackageWeight(StringUtils.isNotBlank(contractModifyDTO.getPackageWeight())
                ? contractModifyDTO.getPackageWeight() : parentContractEntity.getPackageWeight());
        // 根据规则计算追加履约保证金限额 by:nana date:240914
        AddedDepositRate2RuleDTO depositRate2RuleDTO = new AddedDepositRate2RuleDTO()
                .setGoodsCategoryId(contractModifyDTO.getGoodsCategoryId())
                .setContractType(contractModifyDTO.getSonContractType())
                .setDepositRate(contractModifyDTO.getDepositRate())
                .setAddedDepositRate(contractModifyDTO.getAddedDepositRate());
        contractEntity.setAddedDepositRate2(AddedDepositRate2CalculateUtil.getAddedDepositRate2(depositRate2RuleDTO));

        // 值对象处理
        contractEntity.setDestinationValue(systemRuleConvertValue(contractEntity.getDestination()));
        contractEntity.setPackageWeightValue(systemRuleConvertValue(contractEntity.getPackageWeight()));
        contractEntity.setDeliveryTypeValue(deliveryTypeConvertValue(contractEntity.getDeliveryType()));
        contractEntity.setWeightCheckValue(systemRuleConvertValue(contractEntity.getWeightCheck()));
        contractEntity.setShipWarehouseValue(factoryConvertValue(contractEntity.getShipWarehouseId()));

        // 期货代码
        contractEntity.setFutureCode(parentContractEntity.getFutureCode());

        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09
        // 清除子合同的定价完成状态
        contractEntity.setIsPricingCompleted(null);
    }

    @Override
    public void createChildContract(ContractModifyDTO contractModifyDTO, ContractEntity contractEntity) {
        // 保存合同
        try {
            if (contractModifyDTO.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
                contractDomainService.saveContract(contractEntity);
            }
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }

        // 记录子合同信息
        contractModifyDTO.setSonContractId(contractEntity.getId())
                .setSonContractType(contractEntity.getContractType());
    }


    @Override
    public void operateFatherContract(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, List<TTPriceEntity> ttPriceEntityList) {
        // 拆分数量
        BigDecimal contractNum = contractEntity.getContractNum().subtract(contractModifyDTO.getModifyNum());
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        BigDecimal totalModifyNum = contractEntity.getTotalModifyNum().add(contractModifyDTO.getModifyNum());

        // 一口价合同拆分已定价量,但不会拆分定价单
        if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
            totalPriceNum = totalPriceNum.subtract(contractModifyDTO.getModifyNum());
        } else {

            // 拆分包含已定量-前端传
            List<ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();
            log.info("split contractId:{},confirmPriceDTOList:{}", contractEntity.getId(), confirmPriceDTOList);

            if (CollectionUtil.isNotEmpty(confirmPriceDTOList)) {
                for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {
                    // 父合同减少已定价量
                    totalPriceNum = totalPriceNum.subtract(confirmPriceDTO.getConfirmNum());
                }
            }
        }

        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                .setTotalPriceNum(totalPriceNum)
                .setTotalModifyNum(totalModifyNum);

        // 拆分导致的原合同全部定价
        if (!contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())
                && BigDecimalUtil.isEqual(contractNum, totalPriceNum)
                && BigDecimalUtil.isGreaterThanZero(contractNum)) {
            // 原合同需要自动计算加权平均价
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalNum = BigDecimal.ZERO;
            for (TTPriceEntity ttPriceEntity : ttPriceEntityList) {
                totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                totalNum = totalNum.add(ttPriceEntity.getNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                // 加权平均价
                BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                // 更新期货价格
                contractDomainService.updateContractForwardPrice(contractEntity, averagePrice);
            }
        }

        // 拆分后原合同状态改为拆分中
        contractEntity.setStatus(ContractStatusEnum.SPLITTING.getValue());

        // 更新合同信息
        contractDomainService.updateContractById(contractEntity);
    }

    /**
     * 规则值转对象 add by zengshl
     *
     * @param ruleItemId
     * @return
     */
    public String systemRuleConvertValue(String ruleItemId) {
        if (ObjectUtil.isNotEmpty(ruleItemId)) {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ruleItemId));
            return ObjectUtil.isNotEmpty(itemEntity) ? itemEntity.getRuleKey() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dbt_delivery_type
     * 值转对象 add by zengshl
     *
     * @param deliveryTypeId
     * @return
     */
    public String deliveryTypeConvertValue(Integer deliveryTypeId) {
        if (ObjectUtil.isNotEmpty(deliveryTypeId)) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(deliveryTypeId);
            return ObjectUtil.isNotEmpty(deliveryTypeEntity) ? deliveryTypeEntity.getName() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dba_factory_warehouse
     * 值转对象 add by zengshl
     *
     * @param factoryId
     * @return
     */
    public String factoryConvertValue(Integer factoryId) {
        if (ObjectUtil.isNotEmpty(factoryId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(factoryId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }

}
