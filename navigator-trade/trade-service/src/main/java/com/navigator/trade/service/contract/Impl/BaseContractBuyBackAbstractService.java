package com.navigator.trade.service.contract.Impl;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractBuyBackDTO;
import com.navigator.trade.pojo.dto.contract.VerifyContractStructureNumDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 合同回购的抽象类-公共处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-20
 */
@Slf4j
public abstract class BaseContractBuyBackAbstractService extends BaseContractAbstractService {

    @Override
    public List<TTQueryVO> applyBuyBack(ContractBuyBackDTO contractBuyBackDTO) {
        ContractEntity contractEntity = contractValueObjectService.getContractById(contractBuyBackDTO.getContractId());
        // 1.校验合同数据
        checkContractInfo(contractEntity, contractBuyBackDTO);

        // 2.处理TT
        List<TTQueryVO> ttQueryVOS = operateTradeTicket(contractEntity, contractBuyBackDTO);

        // 3.更新父合同
        operateFatherContract(contractEntity, contractBuyBackDTO);

        //  4.记录日志
        recordOperationLog(contractBuyBackDTO, contractEntity);

        return ttQueryVOS;
    }

    /**
     * 处理父合同信息
     *
     * @param contractEntity     父合同
     * @param contractBuyBackDTO 回购dto
     */
    private void operateFatherContract(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        // 回购数量
        BigDecimal buyBackNum = contractEntity.getTotalBuyBackNum().add(contractBuyBackDTO.getBuyBackNum());
        contractValueObjectService.updateContractById(contractEntity.setTotalBuyBackNum(buyBackNum)
                .setStatus(ContractStatusEnum.MODIFYING.getValue()));
    }

    /**
     * 处理TT信息
     *
     * @param contractEntity     合同信息
     * @param contractBuyBackDTO 回购dto
     */
    protected List<TTQueryVO> operateTradeTicket(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        TTDTO ttdto = new TTDTO();

        // 处理回购的数据
        SalesContractAddTTDTO salesContractAddTTDTO = BeanConvertUtils.map(SalesContractAddTTDTO.class, contractEntity);

        // 价格处理
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, contractPriceEntity);
        if (contractBuyBackDTO.getExtraPrice() != null) {
            priceDetailBO.setExtraPrice(contractBuyBackDTO.getExtraPrice());
        }
        if (contractBuyBackDTO.getForwardPrice() != null) {
            priceDetailBO.setForwardPrice(contractBuyBackDTO.getForwardPrice());
        }

        salesContractAddTTDTO.setContractNum(String.valueOf(contractBuyBackDTO.getBuyBackNum()));

        ttdto.setPriceDetailBO(priceDetailBO);
        // 原合同id
        salesContractAddTTDTO.setSourceContractId(contractEntity.getId());
        salesContractAddTTDTO.setRootContractId(contractEntity.getRootId());
        // 校验状态
        salesContractAddTTDTO.setCompletedStatus(0);
        //发货工厂
        //salesContractAddTTDTO.setDeliveryFactoryCode(null);
        //salesContractAddTTDTO.setDeliveryFactoryName(null);
        //发货库点
//        salesContractAddTTDTO.setShipWarehouseId("26");
        // 回购,买卖主体对调
        Integer customerId = salesContractAddTTDTO.getCustomerId();
        Integer supplierId = salesContractAddTTDTO.getSupplierId();
        salesContractAddTTDTO.setSupplierId(customerId);
        salesContractAddTTDTO.setCustomerId(supplierId);
        // 基差暂定价合同，回购后类型仅可以为基差
        if (contractBuyBackDTO.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
            salesContractAddTTDTO.setContractType(String.valueOf(ContractTypeEnum.JI_CHA.getValue()));
        }

        // 保证金释放方式
        salesContractAddTTDTO.setDepositUseRule(contractEntity.getDepositReleaseType());

        // 期货合约
        if (StringUtils.isNotBlank(contractBuyBackDTO.getDomainCode())) {
            salesContractAddTTDTO.setDomainCode(contractBuyBackDTO.getDomainCode());
        }

        salesContractAddTTDTO.setDelayPayFine(String.valueOf(contractEntity.getDelayPayFine()));

        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);

        // 获取处理TT接口
        String ttProcessorType = TTHandlerUtil.getTTProcessor(
                ContractSalesTypeEnum.PURCHASE.getValue(),
                TTTypeEnum.BUYBACK.getType(),
                contractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(ttProcessorType);
        ITradeTicketService tradeTicketService = ttHandler.getStrategy(ttProcessorType);
        Result<List<TTQueryVO>> listResult = tradeTicketService.saveTT(ttdto);
        List<TTQueryVO> ttQueryVOS = listResult.getData();

        TTQueryVO ttQueryVO = new TTQueryVO();

        ttQueryVO.setSourceFlag(1).setContractCode(contractEntity.getContractCode());
        ttQueryVOS.add(ttQueryVO);

        return ttQueryVOS;
    }


    /**
     * 校验合同信息
     *
     * @param contractEntity     合同实体
     * @param contractBuyBackDTO 回购dto
     */
    protected void checkContractInfo(ContractEntity contractEntity, ContractBuyBackDTO contractBuyBackDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // 判断客户是否可用
        if (!isEnableCustomerStatus(contractEntity)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }

        // 数量校验:可回购数量≤合同总量-MAX（已定价量/已开单量）
        if (BigDecimalUtil.isZero(contractBuyBackDTO.getBuyBackNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
        }

        BigDecimal buyBackNum = contractEntity.getContractNum().subtract(contractEntity.getTotalBuyBackNum());
        BigDecimal maxNum = contractEntity.getTotalBillNum();
        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum());
        } else if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
            maxNum = BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
        }
        if (BigDecimalUtil.isGreater(contractBuyBackDTO.getBuyBackNum(), buyBackNum.subtract(maxNum))) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_EXCEPTION);
        }

        if (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType()) {

            Boolean b = contractStructureService.verifyContractStructureNum(new VerifyContractStructureNumDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                    .setDomainCode(contractEntity.getDomainCode())
                    .setContractNum(contractBuyBackDTO.getBuyBackNum())
                    .setCompanyId(contractEntity.getCompanyId())
            );

            if (b) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_STRUCTURE_NUM_EXCEPTION);
            }
        }

        // 可提数量校验
        if (null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }
    }

    /**
     * 日志处理
     *
     * @param contractBuyBackDTO
     * @param contractEntity
     */
    protected abstract void recordOperationLog(ContractBuyBackDTO contractBuyBackDTO, ContractEntity contractEntity);

}
