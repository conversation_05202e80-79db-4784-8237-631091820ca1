package com.navigator.trade.service.contract.purchase;

import cn.hutool.json.JSONUtil;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.trade.pojo.dto.contract.ContractWashOutDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.service.contract.IContractOperationNewService;
import com.navigator.trade.service.contract.Impl.BaseContractWashOutAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 豆粕合同洗單的具体实现
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
@Slf4j
@Service("SBM_P_CONTRACT_WASHOUT,SBO_P_CONTRACT_WASHOUT")
public class PurchaseContractWashOutService extends BaseContractWashOutAbstractService {

    @Resource
    public IContractOperationNewService salesContractOperationService;

    @Override
    protected void recordOperationLog(ContractWashOutDTO contractWashOutDTO, ContractEntity contractEntity) {
        // 记录操作记录
        salesContractOperationService.addContractOperationLog(contractEntity,
                LogBizCodeEnum.PURCHASE_CONTRACT_WASH_OUT, JSONUtil.toJsonStr(contractWashOutDTO),
                SystemEnum.MAGELLAN.getValue());
    }
}
