package com.navigator.trade.app.sign.domain.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.app.sign.domain.service.ContractSignQueryDomainService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.dao.ContractPaperDao;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.ContractSignVODao;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractSignVOEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 处理协议的查询包括协议正本信息处理
 *
 * <AUTHOR>
 * @date 20240715
 */
@Service
public class ContractSignQueryDomainServiceImpl implements ContractSignQueryDomainService {

    @Resource
    private ContractSignVODao contractSignVODao;

    @Resource
    private ContractSignDao contractSignDao;

    @Autowired
    private ContractPaperDao contractPaperDao;

    /**
     * 查询协议列表数据
     *
     * @param queryDTO
     * @param customerIds
     * @param siteCodeList
     * @return
     */
    @Override
    public IPage<ContractSignVOEntity> queryContractSign(QueryDTO<ContractSignQO> queryDTO,
                                                         List<Integer> customerIds,
                                                         List<String> siteCodeList) {
        return contractSignVODao.queryContractSign(queryDTO, customerIds, siteCodeList);
    }

    @Override
    public ContractSignEntity getById(Integer contractSignId) {
        return contractSignDao.getById(contractSignId);
    }

    @Override
    public ContractSignEntity getSignDetailByTtId(Integer ttId) {
        return contractSignDao.getSignDetailByTtId(ttId);
    }

    @Override
    public ContractSignEntity queryByUUId(String uuid) {
        return contractSignDao.queryByUUId(uuid);
    }

    @Override
    public List<ContractSignEntity> queryByContractId(Integer contractId) {
        return contractSignDao.queryByContractId(contractId);
    }

    @Override
    public Integer getContractSignStat(Integer status, Integer ldcFrame, Integer customerId,
                                       Integer salesType, Integer goodsCategoryId,
                                       List<String> siteCodeList) {
        return contractSignDao.getContractSignStat(status,
                ldcFrame, customerId, salesType, goodsCategoryId, siteCodeList);
    }

    @Override
    public List<ContractSignEntity> getContractSignList(Integer status, Integer ldcFrame, Integer customerId, Integer salesType, Integer goodsCategoryId, List<Integer> belongCustomerIdList) {
        return contractSignDao.getContractSignList(status,
                ldcFrame, customerId, salesType, goodsCategoryId, belongCustomerIdList);
    }

    @Override
    public ContractSignAllStatusNumVO getColumbusContractSignStat(QueryContractSignBO signBO) {
        return contractSignDao.getColumbusContractSignStat(signBO);
    }
    @Override
    public ContractSignAllStatusNumVO getMagellanContractSignStat(QueryContractSignBO signBO) {
        return contractSignDao.getMagellanContractSignStat(signBO);
    }

    @Override
    public List<ContractSignEntity> queryIncompleteByContractId(List<Integer> ids, List<Integer> ttTypeList) {
        return contractSignDao.queryIncompleteByContractId(ids, ttTypeList);
    }

    @Override
    public List<ContractSignEntity> querySignListByContractId(Integer contractId, List<Integer> ttStatusList) {
        return contractSignDao.querySignListByContractId(contractId, ttStatusList);
    }

    @Override
    public List<ContractSignEntity> querySonContractSplitIncomplete(List<Integer> ids) {
        return contractSignDao.querySonContractSplitIncomplete(ids);
    }


    @Override
    public ContractPaperEntity getContractPaper(Integer contractSignId) {
        ContractPaperEntity contractPaper = contractPaperDao.getContractPaper(contractSignId);
        return contractPaper;
    }

    @Override
    public List<ContractSignEntity> sendContractSignOriginalPaper(List<Integer> contractSingId) {
        return contractSignDao.sendContractSignOriginalPaper(contractSingId);
    }


    @Override
    public List<Integer> sendContractSignOriginalPaper() {
        List<ContractPaperEntity> paperGroups = contractPaperDao.sendContractSignOriginalPaper();
        return paperGroups.stream().map(ContractPaperEntity::getContractSignId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<ContractSignEntity> queryContractSignByIdSCustomerIdSalesType(List<Integer> contractSingId, Integer customerId,
                                                                              String supplierId, Integer goodsCategoryId) {
        return contractSignDao.queryContractSignByIdSCustomerIdSalesType(contractSingId,customerId,supplierId,goodsCategoryId);
    }

    @Override
    public ContractSignEntity queryContractSignByContractCode(String contractCode) {
        return contractSignDao.queryContractSignByContractCode(contractCode);
    }

    @Override
    public ContractSignEntity queryByTTCode(String ttCode) {
        return contractSignDao.queryByTTCode(ttCode);
    }


}
