package com.navigator.trade.service.tradeticket.impl.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.bisiness.enums.TTWriteOffActionEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffOMDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffPurchaseDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractSplitTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/20
 * @Version 1.0
 */
@Slf4j
@Component
public class TTModifyEntityConvert {

    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected WarehouseFacade warehouseFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected CustomerBankFacade customerBankFacade;
    @Autowired
    protected SkuFacade skuFacade;
    @Autowired
    protected IContractPriceService contractPriceService;
    @Autowired
    protected IContractQueryService contractService;
    @Autowired
    protected IDeliveryTypeService iDeliveryTypeService;
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;

    public TTModifyEntity writeOff2TTModifyEntity(TTDTO ttdto, ArrangeContext arrangeContext) {
        ContractWriteOffDTO contractWriteOffDTO = ttdto.getContractWriteOffDTO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        // add by zengshl 拷贝一子合同的一些字段信息
        if (ObjectUtil.isNotEmpty(ttdto.getContractEntity())) {
            BeanUtils.copyProperties(ttdto.getContractEntity(), ttModifyEntity);
        }
        ttModifyEntity.setContractId(arrangeContext.getContractId());
        ttModifyEntity.setContractCode(arrangeContext.getContractCode());
        ttModifyEntity.setType(TTTypeEnum.WRITE_OFF.getType());
        ttModifyEntity.setSourceContractId(contractWriteOffDTO.getContractId());
        ttModifyEntity.setRootContractId(contractWriteOffDTO.getContractId());
        ttModifyEntity.setWriteOffNum(contractWriteOffDTO.getWriteOffNum());
        ttModifyEntity.setContractNum(contractWriteOffDTO.getWriteOffNum());
        ttModifyEntity.setDeliveryPassword(contractWriteOffDTO.getDeliveryPassword());
        ttModifyEntity.setWriteOffDate(contractWriteOffDTO.getWriteOffDate());
        ttModifyEntity.setModifyContent(contractWriteOffDTO.getModifyContent());
        ttModifyEntity.setContent(contractWriteOffDTO.getContent());
        ttModifyEntity.setWriteOffDeliveryStartTime(contractWriteOffDTO.getWriteOffDeliveryStartTime());
        ttModifyEntity.setWriteOffDeliveryEndTime(contractWriteOffDTO.getWriteOffDeliveryEndTime());
        ttModifyEntity.setShipWarehouseId(contractWriteOffDTO.getShipWarehouseId());
        ttModifyEntity.setDeliveryType(contractWriteOffDTO.getDeliveryType());
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        PriceDetailBO priceDetailBO;
        if (TTWriteOffActionEnum.PURCHASE_ADD.equals(writeOffTTAction)) {
            ContractWriteOffPurchaseDTO purchaseDTO = contractWriteOffDTO.getContractWriteOffPurchaseDTO();
            ttModifyEntity.setUnitPrice(purchaseDTO.getUnitPrice());
            ttModifyEntity.setCreditDays(purchaseDTO.getCreditDays());
            ttModifyEntity.setDepositRate(purchaseDTO.getDepositRate());
            ttModifyEntity.setDepositAmount(purchaseDTO.getDepositAmount());
            ttModifyEntity.setInvoicePaymentRate(purchaseDTO.getInvoicePaymentRate());
            ttModifyEntity.setGoodsId(purchaseDTO.getGoodsId());
            ttModifyEntity.setGoodsName(purchaseDTO.getGoodsName());
            priceDetailBO = purchaseDTO.getPriceDetailDTO();
        } else {
            ttModifyEntity.setUnitPrice(contractWriteOffDTO.getUnitPrice());
            ttModifyEntity.setCreditDays(contractWriteOffDTO.getCreditDays());
            ttModifyEntity.setDepositRate(contractWriteOffDTO.getDepositRate());
            ttModifyEntity.setAddedDepositRate(contractWriteOffDTO.getAddedDepositRate());
            ttModifyEntity.setDepositAmount(contractWriteOffDTO.getDepositAmount());
            ttModifyEntity.setInvoicePaymentRate(contractWriteOffDTO.getInvoicePaymentRate());
            ttModifyEntity.setGoodsId(contractWriteOffDTO.getGoodsId());
            ttModifyEntity.setGoodsName(contractWriteOffDTO.getGoodsName());
            priceDetailBO = contractWriteOffDTO.getPriceDetailDTO();
        }
        if (ContractSalesTypeEnum.PURCHASE.getValue() == ttdto.getSalesType()
                && TTWriteOffActionEnum.PURCHASE_ADD.getValue().equals(ttdto.getWriteOffTTAction().getValue())
                && null != contractWriteOffDTO.getContractWriteOffPurchaseDTO()) {
            ttModifyEntity.setDeliveryType(contractWriteOffDTO.getContractWriteOffPurchaseDTO().getDeliveryType());
        }
        ttModifyEntity.setFutureCode(contractWriteOffDTO.getFutureCode());
        ttModifyEntity.setDomainCode(contractWriteOffDTO.getDomainCode());
        ttModifyEntity.setDestination(contractWriteOffDTO.getDestination());
        ttModifyEntity.setWarrantTradeType(contractWriteOffDTO.getWarrantTradeType());
        ttModifyEntity.setMemo(contractWriteOffDTO.getMemo());
        ttModifyEntity.setSignDate(contractWriteOffDTO.getSignDate());

        if (StringUtils.isNotBlank(contractWriteOffDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteByCode(contractWriteOffDTO.getSiteCode());
            if (null != siteEntity) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteEntity.getFactoryCode());
                ttModifyEntity.setDeliveryFactoryCode(siteEntity.getFactoryCode())
                        .setDeliveryFactoryName(null != factoryEntity ? factoryEntity.getName() : "");
            }
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        if (StringUtils.isNotBlank(currentUserId)) {
            ttModifyEntity.setCreatedBy(Integer.parseInt(currentUserId));
            ttModifyEntity.setUpdatedBy(Integer.parseInt(currentUserId));
        }
        if (TTWriteOffActionEnum.DELIVERY_ADD.equals(writeOffTTAction) || TTWriteOffActionEnum.PURCHASE_ADD.equals(writeOffTTAction)) {
            ttModifyEntity.setDestinationValue(systemRuleConvertValue(ttModifyEntity.getDestination()));
            ttModifyEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttModifyEntity.getDeliveryType()));
            String warehouseValue = factoryConvertValue(ttModifyEntity.getShipWarehouseId());
            ttModifyEntity.setShipWarehouseValue(warehouseValue);
        }
        ContractEntity parentContractEntity = ttdto.getContractEntity();
        ttModifyEntity.setWarrantId(parentContractEntity.getWarrantId());
        ttModifyEntity.setWarrantCode(parentContractEntity.getWarrantCode());

        //含税单价
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttModifyEntity.setUnitPrice(unitPrice);
        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));
        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttModifyEntity.setFobUnitPrice(fobUnitPrice);
        return ttModifyEntity;
    }


    /**
     * 豆二注销生成TT子类
     *
     * @param ttdto
     * @param arrangeContext
     * @return
     */
    public TTModifyEntity writeOff2OMTTModifyEntity(TTDTO ttdto, ArrangeContext arrangeContext) {
        ContractWriteOffOMDTO contractWriteOffOMDTO = ttdto.getContractWriteOffOMDTO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        // add by zengshl 拷贝一子合同的一些字段信息
        ContractEntity contractEntity = ttdto.getContractEntity();
        if (ObjectUtil.isNotEmpty(ttdto.getContractEntity())) {
            BeanUtils.copyProperties(ttdto.getContractEntity(), ttModifyEntity);
        }
        if (ContractSalesTypeEnum.PURCHASE.getValue() == ttdto.getSalesType() &&
                TTWriteOffActionEnum.PURCHASE_ADD.getValue().equals(ttdto.getWriteOffTTAction().getValue())) {
            Integer purchaseWriteOffDeliveryType = 63;
            ttModifyEntity.setDeliveryType(purchaseWriteOffDeliveryType);
        }
        ttModifyEntity.setContractId(arrangeContext.getContractId());
        ttModifyEntity.setContractCode(arrangeContext.getContractCode());
        ttModifyEntity.setType(TTTypeEnum.WRITE_OFF.getType());
        ttModifyEntity.setSourceContractId(contractWriteOffOMDTO.getContractId());
        ttModifyEntity.setRootContractId(contractWriteOffOMDTO.getContractId());
        ttModifyEntity.setWriteOffNum(contractEntity.getContractNum());
        ttModifyEntity.setDeliveryPassword(contractWriteOffOMDTO.getDeliveryPassword());
        ttModifyEntity.setWriteOffDate(contractWriteOffOMDTO.getWriteOffDate());
        ttModifyEntity.setWriteOffDeliveryStartTime(contractWriteOffOMDTO.getWriteOffDeliveryStartTime());
        ttModifyEntity.setWriteOffDeliveryEndTime(contractWriteOffOMDTO.getWriteOffDeliveryEndTime());
        ttModifyEntity.setContent(contractWriteOffOMDTO.getContent());
        ttModifyEntity.setModifyContent(contractWriteOffOMDTO.getModifyContent());
        TTWriteOffActionEnum writeOffTTAction = ttdto.getWriteOffTTAction();
        PriceDetailBO priceDetailBO;
        priceDetailBO = ttdto.getPriceDetailBO();
        ttModifyEntity.setSignDate(contractWriteOffOMDTO.getSignDate());
        String currentUserId = JwtUtils.getCurrentUserId();
        if (StringUtils.isNotBlank(currentUserId)) {
            ttModifyEntity.setCreatedBy(Integer.parseInt(currentUserId));
            ttModifyEntity.setUpdatedBy(Integer.parseInt(currentUserId));
        }
        if (StringUtils.isNotBlank(contractWriteOffOMDTO.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteByCode(contractWriteOffOMDTO.getSiteCode());
            if (null != siteEntity) {
                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteEntity.getFactoryCode());
                ttModifyEntity.setDeliveryFactoryCode(siteEntity.getFactoryCode())
                        .setDeliveryFactoryName(null != factoryEntity ? factoryEntity.getName() : "");
            }
        }
        //含税单价 TODO 去掉
        BigDecimal unitPrice = contractPriceService.calculatePriceBo(priceDetailBO);
        ttModifyEntity.setUnitPrice(unitPrice);
        //运输费
        BigDecimal deliveryPrice = BigDecimalUtil.initBigDecimal(priceDetailBO.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(priceDetailBO.getOtherDeliveryPrice()));
        //计算fobUnitPrice价格
        BigDecimal fobUnitPrice = unitPrice.subtract(deliveryPrice).setScale(6, RoundingMode.HALF_UP);
        ttModifyEntity.setFobUnitPrice(fobUnitPrice);
        return ttModifyEntity;
    }

    /**
     * TT拆分场景convert2TTAddEntity
     *
     * @return
     */
    public TTModifyEntity split2TTModifyEntity(TTDTO ttDto, Integer ttId, Integer type) {

        SalesContractSplitTTDTO salesContractSplitTTDTO = ttDto.getSalesContractSplitTTDTO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        if (salesContractSplitTTDTO.getAddedSignatureType() == 1 || salesContractSplitTTDTO.getAddedSignatureType() == -1) {
            ContractEntity contractEntity = contractService.getBasicContractById(salesContractSplitTTDTO.getSourceContractId());
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractSplitTTDTO.getSourceContractId());
            BeanUtils.copyProperties(salesContractSplitTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(contractPriceEntity, ttModifyEntity);
            BigDecimal newContractNum = null;
            if (contractEntity.getContractNum() != null && salesContractSplitTTDTO.getContractNum() != null) {
                newContractNum = contractEntity.getContractNum().subtract(salesContractSplitTTDTO.getContractNum());
            }
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(contractEntity.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setContractCode(contractEntity.getContractCode())
                    .setRootContractId(contractEntity.getRootId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setNewContractNum(newContractNum)
                    .setTradeType(salesContractSplitTTDTO.getTradeType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setSupplierAccount(contractEntity.getSupplierAccount())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        } else {
            //子合同
            Integer sonContractId = salesContractSplitTTDTO.getSonContractId();
            if (ttDto.getSubmitType().equals(SubmitTypeEnum.SAVE.getValue())) {
                sonContractId = salesContractSplitTTDTO.getSourceContractId();
            }
            PriceDetailBO priceDetailBo = ttDto.getPriceDetailBO();
            ContractEntity contractEntity = contractService.getBasicContractById(sonContractId);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(salesContractSplitTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(priceDetailBo, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(salesContractSplitTTDTO.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(sonContractId)
                    .setSourceContractId(salesContractSplitTTDTO.getSourceContractId())
                    .setContractCode(salesContractSplitTTDTO.getContractCode())
                    .setRootContractId(salesContractSplitTTDTO.getRootContractId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setDeliveryFactoryCode(salesContractSplitTTDTO.getDeliveryFactoryCode())
                    .setSupplierAccount(salesContractSplitTTDTO.getSupplierAccount())
                    .setDeliveryFactoryName(salesContractSplitTTDTO.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        }
        ttModifyEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttModifyEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        ttModifyEntity.setDestinationValue(systemRuleConvertValue(ttModifyEntity.getDestination()));
        ttModifyEntity.setPackageWeightValue(systemRuleConvertValue(ttModifyEntity.getPackageWeight()));
        ttModifyEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttModifyEntity.getDeliveryType()));
        ttModifyEntity.setWeightCheckValue(systemRuleConvertValue(ttModifyEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        ttModifyEntity.setInvoiceTypeValue(invoiceTypeConvertValue(ttModifyEntity.getInvoiceType()));
        ttModifyEntity.setShipWarehouseValue(factoryConvertValue(ttModifyEntity.getShipWarehouseId()));
        return ttModifyEntity;
    }

    /**
     * 发票类型：dbt_invoice_type
     *
     * @param invoiceType
     * @return
     */
    public String invoiceTypeConvertValue(Integer invoiceType) {
        return InvoiceTypeEnum.getDescByValue(invoiceType);
    }

    /**
     * 规则值转对象 add by zengshl
     *
     * @param ruleItemId
     * @return
     */
    public String systemRuleConvertValue(String ruleItemId) {
        if (ObjectUtil.isNotEmpty(ruleItemId)) {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ruleItemId));
            return ObjectUtil.isNotEmpty(itemEntity) ? itemEntity.getRuleKey() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dbt_delivery_type
     * 值转对象 add by zengshl
     *
     * @param deliveryTypeId
     * @return
     */
    public String deliveryTypeConvertValue(Integer deliveryTypeId) {
        if (ObjectUtil.isNotEmpty(deliveryTypeId)) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(deliveryTypeId);
            return ObjectUtil.isNotEmpty(deliveryTypeEntity) ? deliveryTypeEntity.getName() : "";
        }
        return "";
    }

    /**
     * 交提货方式：dba_factory_warehouse
     * 值转对象 add by zengshl
     *
     * @param factoryId
     * @return
     */
    public String factoryConvertValue(Integer factoryId) {
        if (ObjectUtil.isNotEmpty(factoryId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(factoryId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity.getName() : "";
            }
        }
        return "";
    }

    public TTModifyEntity revise2TTModifyEntity(TTDTO ttDto, Integer ttId, Integer type) {
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttDto.getSalesContractReviseTTDTO();
        PriceDetailBO priceDetailBo = ttDto.getPriceDetailBO();
        TTModifyEntity ttModifyEntity = new TTModifyEntity();
        ContractEntity contractEntity = contractService.getBasicContractById(salesContractReviseTTDTO.getContractId());

        // 开户行变更
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                && StringUtils.isNotBlank(contractEntity.getDeliveryFactoryCode())
                && !contractEntity.getDeliveryFactoryCode().equals(salesContractReviseTTDTO.getDeliveryFactoryCode())) {

            CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
            customerAllMessageDTO.setCustomerId(contractEntity.getSupplierId())
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setFactoryCode(salesContractReviseTTDTO.getDeliveryFactoryCode())
                    .setSalesType(contractEntity.getSalesType())
                    .setCompanyId(contractEntity.getCompanyId())
            ;
            CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
            if (null != customerDTO) {
                List<CustomerBankDTO> customerBankDTOS = customerDTO.getCustomerBankDTOS();
                if (CollectionUtil.isNotEmpty(customerBankDTOS)) {
                    salesContractReviseTTDTO.setSupplierAccount(customerBankDTOS.get(0).getBankAccountNo())
                            .setSupplierAccountId(customerBankDTOS.get(0).getId());
                }
            }
        }

        if (salesContractReviseTTDTO.getAddedSignatureType() == -1) {
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(salesContractReviseTTDTO.getSourceContractId());
            BeanUtils.copyProperties(salesContractReviseTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(contractPriceEntity, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setDeliveryFactory(contractEntity.getDeliveryFactory())
                    .setRelationId(ttDto.getGroupId())
                    .setContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setContractCode(contractEntity.getContractCode())
                    .setRootContractId(contractEntity.getRootId())
                    .setType(type)
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setNewContractNum(salesContractReviseTTDTO.getContractNum())
                    .setTradeType(salesContractReviseTTDTO.getTradeType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setSupplierAccount(contractEntity.getSupplierAccount())
                    .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        } else {
            BeanUtils.copyProperties(contractEntity, ttModifyEntity);
            BeanUtils.copyProperties(salesContractReviseTTDTO, ttModifyEntity);
            BeanUtils.copyProperties(priceDetailBo, ttModifyEntity);
            ttModifyEntity.setId(null)
                    .setType(type)
                    .setContractId(salesContractReviseTTDTO.getContractId())
                    .setSourceContractId(salesContractReviseTTDTO.getSourceContractId())
                    .setContractCode(salesContractReviseTTDTO.getContractCode())
                    .setModifyContent(salesContractReviseTTDTO.getModifyContent())
                    .setContent(salesContractReviseTTDTO.getContent())
                    .setWeightCheck(String.valueOf(salesContractReviseTTDTO.getWeightCheck()))
                    .setBaseDiffPrice(contractEntity.getBaseDiffPrice())
                    .setCreatedAt(null)
                    .setUpdatedAt(null)
                    .setDeliveryFactoryCode(salesContractReviseTTDTO.getDeliveryFactoryCode())
                    .setSupplierAccount(salesContractReviseTTDTO.getSupplierAccount())
                    .setDeliveryFactoryName(salesContractReviseTTDTO.getDeliveryFactoryName())
                    .setTtId(ttId)
            ;
        }
        //追加履约保证金的问题，0217，by nana
        ttModifyEntity.setAddedDepositRate2(salesContractReviseTTDTO.getAddedDepositRate2());
        ttModifyEntity.setStandardType(salesContractReviseTTDTO.getStandardType());
        ttModifyEntity.setStandardFileId(salesContractReviseTTDTO.getStandardFileId());
        ttModifyEntity.setStandardRemark(salesContractReviseTTDTO.getStandardRemark());
        // add by zengshl 重新取一下商品信息
        SkuEntity skuEntity = skuFacade.getSkuById(salesContractReviseTTDTO.getGoodsId());
        if (ObjectUtil.isNotEmpty(skuEntity)) {
            ttModifyEntity.setGoodsCategoryId(skuEntity.getCategory2());
            ttModifyEntity.setGoodsSpecId(skuEntity.getSpecId());
            ttModifyEntity.setGoodsPackageId(skuEntity.getPackageId());
        }

        ttModifyEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        ttModifyEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        // 值对象处理
        ttModifyEntity.setDestinationValue(systemRuleConvertValue(ttModifyEntity.getDestination()));
        ttModifyEntity.setPackageWeightValue(systemRuleConvertValue(ttModifyEntity.getPackageWeight()));
        ttModifyEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttModifyEntity.getDeliveryType()));
        ttModifyEntity.setWeightCheckValue(systemRuleConvertValue(ttModifyEntity.getWeightCheck()));
        ttModifyEntity.setInvoiceTypeValue(invoiceTypeConvertValue(ttModifyEntity.getInvoiceType()));
        ttModifyEntity.setShipWarehouseValue(factoryConvertValue(ttModifyEntity.getShipWarehouseId()));
        return ttModifyEntity;
    }

}
