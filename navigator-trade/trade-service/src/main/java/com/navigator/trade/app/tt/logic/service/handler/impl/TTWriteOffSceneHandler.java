package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.bisiness.enums.TTWriteOffActionEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.UsageEnum;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 仓单销售合同注销
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("WRITE_OFF_HANDLER")
public class TTWriteOffSceneHandler extends AbstractTTSceneHandler {

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.WRITE_OFF.equals(ttTypeEnum);
    }

    @Override
    public void initDTO(TTDTO ttdto) {
        // 注销前后的变更字段
        ContractWriteOffDTO contractWriteOffDTO = ttdto.getContractWriteOffDTO();
        PriceDetailBO priceDetailBO = contractWriteOffDTO.getPriceDetailDTO();
        //原合同信息
        Integer contractId = contractWriteOffDTO.getContractId();
        ContractEntity parentContractEntity = contractQueryLogicService.getBasicContractById(contractId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        ContractEntity sonContractEntity = ttdto.getContractEntity();
        // modify by zengshl 子计划的变更记录需要重置下
        contractWriteOffDTO.setModifyContent(null);
        contractWriteOffDTO.setContent(null);
        List<CompareObjectDTO> compareObjectDTOList = getTransferCompareObjectDTOS(sonContractEntity, parentContractEntity, contractPriceEntity, priceDetailBO);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        contractWriteOffDTO.setModifyContent(modifyContent);
        //获取前后字段
        List<CompareObjectDTO> contentDTOList = getTransferContentObjectDTOS(sonContractEntity, parentContractEntity, contractPriceEntity, priceDetailBO);
        String content = JSON.toJSONString(contentDTOList);
        contractWriteOffDTO.setContent(content);
        ttdto.setContractWriteOffDTO(contractWriteOffDTO);

    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        initDTO(ttdto);
        // 1、convert
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.writeOff2TradeTicketDO(ttdto, arrangeContext);
        arrangeContext.setTradeTicketDO(tradeTicketDO);
        // 2、保存
        ttDomainService.createTradeTicketDO(tradeTicketDO);
        // 3、提交审批
        if (TTWriteOffActionEnum.DELIVERY_ADD.equals(ttdto.getWriteOffTTAction())
                || TTWriteOffActionEnum.PURCHASE_ADD.equals(ttdto.getWriteOffTTAction())) {
            TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
            Integer ttId = tradeTicketEntity.getId();
            //3.1 提交审批【TT完成也是要调用-出审批记录】
            ttApproveHandler.startTTApprove(tradeTicketEntity.getId(), ttdto, null);
        }
        // 4、返回结果
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTQueryVO ttQueryVO = new TTQueryVO();
        ttQueryVO.setContractCode(tradeTicketEntity.getContractCode())
                .setCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId());
        return Lists.newArrayList(ttQueryVO);
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTModifyEntity ttModifyEntity = (TTModifyEntity) tradeTicketDO.getTtSubEntity();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());
        // 2、基础信息convert
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(contractEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, ttQueryDetailVO);
        ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityByTTId(ttId);
        BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        // 买方
        ttQueryDetailVO.setCustomerId(String.valueOf(tradeTicketEntity.getCustomerId()));
        ttQueryDetailVO.setCustomerCode(tradeTicketEntity.getCustomerCode());
        ttQueryDetailVO.setCustomerName(tradeTicketEntity.getCustomerName());
        // 卖方
        ttQueryDetailVO.setSupplierId(String.valueOf(tradeTicketEntity.getSupplierId()));
        ttQueryDetailVO.setSupplierName(tradeTicketEntity.getSupplierName());
        // 客户集团信息
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }
        // 仓单信息
        Integer warrantId = ttModifyEntity.getWarrantId();
        Result result = warrantFacade.queryWarrantByID(warrantId);
        if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            ttQueryDetailVO.setWarrantCategory(warrantEntity.getCategory())
                    .setDepositPaymentType(warrantEntity.getDepositPaymentType());
        }
        // 发货库点/交割仓库
        if (Objects.nonNull(ttModifyEntity.getShipWarehouseId())) {
            ttQueryDetailVO.setShipWarehouseId(String.valueOf(ttModifyEntity.getShipWarehouseId()));
            Result<WarehouseEntity> warehouseResult = warehouseFacade.getWarehouseById(ttModifyEntity.getShipWarehouseId());
            if (warehouseResult.isSuccess() && Objects.nonNull(warehouseResult.getData())) {
                WarehouseEntity warehouseEntity = warehouseResult.getData();
                ttQueryDetailVO.setShipWarehouseCode(warehouseEntity.getCode());
                ttQueryDetailVO.setShipWarehouseName(warehouseEntity.getName());
            }
        }
        if (StringUtils.isNotBlank(ttModifyEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(ttModifyEntity.getShipWarehouseValue());
        }
        ttQueryDetailVO.setContractNum(ttModifyEntity.getWriteOffNum());
        //目的地
        ttQueryDetailVO.setDestination(ttModifyEntity.getDestination());
        if (Objects.nonNull(ttModifyEntity.getDestination())) {
            SystemRuleItemEntity destination = systemRuleFacade.getRuleItemById(Integer.parseInt(ttModifyEntity.getDestination()));
            ttQueryDetailVO.setDestinationName(destination != null ? destination.getRuleKey() : "");
        }

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }
        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        // 付款条件代码
        Result payConditionResult = payConditionFacade.getPayConditionById(tradeTicketEntity.getPayConditionId());
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            ttQueryDetailVO.setPayConditionCode(payConditionEntity.getCode());
        }
        // 价格明细
        // 履约保证金
        ttQueryDetailVO.setDepositRate(ttModifyEntity.getDepositRate());
        ttQueryDetailVO.setDepositAmount(ttModifyEntity.getDepositAmount());
        if (null != ttModifyEntity.getDepositRate()) {
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateFacade.getCustomerDepositRateById(ttModifyEntity.getDepositRate());
            if (null != customerDepositRateEntity) {
                ttQueryDetailVO.setDepositRateValue(customerDepositRateEntity.getDepositRate());
            }
        }
        //应付履约保证金状态
        if (null != ttModifyEntity.getDepositAmount()) {
            int depositAmountStatus = ttModifyEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttModifyEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttModifyEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttModifyEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(ttModifyEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }
        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(ttModifyEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(ttModifyEntity.getPriceEndTime());

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }
        //创建人
        ttQueryDetailVO.setCreatedAt(tradeTicketEntity.getCreatedAt());
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        // TT详情后续设置
        queryTTDetailAfter(tradeTicketEntity, ttDetailVO);
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        return ttDetailVO;
    }

//    /**
//     * 变更内容
//     * @param contractWriteOffDTO
//     * @param contractEntity
//     * @param contractPriceEntity
//     * @return
//     */
//    private List<CompareObjectDTO> getTransferCompareObjectDTOS(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity) {
//        // 获取变更字段
//        List<String> manualList = new ArrayList<>();
//        Field[] fields = ContractWriteOffDTO.class.getDeclaredFields();
//        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
//        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
//        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
//        ContractWriteOffDTO originalDTO = new ContractWriteOffDTO();
//        BeanUtil.copyProperties(contractEntity, originalDTO);
//        ContractWriteOffDTO newDTO = new ContractWriteOffDTO();
//        BeanUtil.copyProperties(contractWriteOffDTO, newDTO);
//        // 如果是采购需要复制采购的信息
//        if (ObjectUtil.isNotEmpty(contractWriteOffDTO.getContractWriteOffPurchaseDTO()) &&
//                ObjectUtil.isNotEmpty(contractWriteOffDTO.getContractWriteOffPurchaseDTO().getGoodsId())) {
//            BeanUtil.copyProperties(contractWriteOffDTO.getContractWriteOffPurchaseDTO(), newDTO);
//        }
//        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
//        if (ObjectUtil.isEmpty(newDTO.getSignDate())) {
//            Date signDate = DateTimeUtil.parseTimeStamp0000(newDTO.getSignDate());
//            newDTO.setSignDate(signDate);
//        };
//        List<String> ignoreList = getIgnoreList();
//        // 比较获取不同字段返回list，转换成json
//        return BeanCompareUtils.compareFields(originalDTO, newDTO, ignoreList, manualList);
//    }

    /**
     * 变更内容
     *
     * @return
     */
    private List<CompareObjectDTO> getTransferCompareObjectDTOS(ContractEntity sonContractEntity, ContractEntity parentContractEntity,
                                                                ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        // 获取变更字段
        List<String> manualList = new ArrayList<>();
        Field[] fields = ContractWriteOffDTO.class.getDeclaredFields();
        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
        if (ObjectUtil.isEmpty(sonContractEntity.getSignDate())) {
            sonContractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(sonContractEntity.getSignDate()));
        }
        ;
        if (ObjectUtil.isEmpty(parentContractEntity.getSignDate())) {
            parentContractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(parentContractEntity.getSignDate()));
        }
        ;
        List<String> ignoreList = getIgnoreList();
        //对比价格字段
        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
        List<CompareObjectDTO> priceList = BeanCompareUtils.compareFields(contractPriceEntity, newContractPriceEntity, null, manualList);
        // 比较获取不同字段返回list，转换成json
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.compareFields(parentContractEntity, sonContractEntity, ignoreList, manualList);
        compareObjectDTOList.addAll(priceList);
        return compareObjectDTOList;
    }

    /**
     * 变更内容
     *
     * @return
     */
    private List<CompareObjectDTO> getTransferContentObjectDTOS(ContractEntity sonContractEntity, ContractEntity parentContractEntity,
                                                                ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        // 获取变更字段
        List<String> manualList = new ArrayList<>();
        Field[] fields = ContractWriteOffDTO.class.getDeclaredFields();
        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
        if (ObjectUtil.isEmpty(sonContractEntity.getSignDate())) {
            sonContractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(sonContractEntity.getSignDate()));
        }
        if (ObjectUtil.isEmpty(parentContractEntity.getSignDate())) {
            parentContractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(parentContractEntity.getSignDate()));
        }
        List<String> ignoreList = getIgnoreList();
        ignoreList.add("extraPrice");
        //对比价格字段
        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
        List<CompareObjectDTO> priceList = BeanCompareUtils.getFields(contractPriceEntity, newContractPriceEntity, null, manualList);
        // 比较获取不同字段返回list，转换成json
        List<CompareObjectDTO> compareObjectDTOList = BeanCompareUtils.getFields(parentContractEntity, sonContractEntity, ignoreList, manualList);
        compareObjectDTOList.addAll(priceList);
        return compareObjectDTOList;
    }

//    /**
//     * 变更内容
//     * @param contractWriteOffDTO
//     * @param contractEntity
//     * @param contractPriceEntity
//     * @param priceDetailBO
//     * @return
//     */
//    private List<CompareObjectDTO> getTransferContentObjectDTOS(ContractWriteOffDTO contractWriteOffDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
//        //对比价格字段
//        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
//        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
//        BigDecimal newExtraPrice = BigDecimal.ZERO;
//
//        List<String> manualList = new ArrayList<>();
//        Field[] fields = ContractWriteOffDTO.class.getDeclaredFields();
//        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
//        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
//        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
//
//        List<CompareObjectDTO> priceList = BeanCompareUtils.getFields(contractPriceEntity, newContractPriceEntity, null, manualList);
//        // 获取变更字段
//        ContractEntity newContractEntity = new ContractEntity();
//        BeanUtil.copyProperties(contractEntity, newContractEntity);
//        BeanUtil.copyProperties(contractWriteOffDTO, newContractEntity);
//        // 如果是采购需要复制采购的信息
//        if (ObjectUtil.isNotEmpty(contractWriteOffDTO.getContractWriteOffPurchaseDTO()) &&
//                ObjectUtil.isNotEmpty(contractWriteOffDTO.getContractWriteOffPurchaseDTO().getGoodsId())) {
//            BeanUtil.copyProperties(contractWriteOffDTO.getContractWriteOffPurchaseDTO(), newContractEntity);
//        }
//        contractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(contractEntity.getSignDate()));
//        if (ObjectUtil.isEmpty(newContractEntity.getSignDate())) {
//            Date signDate = DateTimeUtil.parseTimeStamp0000(newContractEntity.getSignDate());
//            newContractEntity.setSignDate(signDate);
//        };
//        List<String> ignoreList = getIgnoreList();
//        ignoreList.add("extraPrice");
//        // 比较获取不同字段返回list，转换成json
//        List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(contractEntity, newContractEntity, ignoreList, manualList);
//        compareObjectDTOS.addAll(priceList);
//        return compareObjectDTOS;
//    }

}
