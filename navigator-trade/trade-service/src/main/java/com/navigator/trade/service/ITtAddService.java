package com.navigator.trade.service;

import com.navigator.trade.pojo.entity.TTAddEntity;

/**
 * <p>
 * ttAdd表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface ITtAddService {

    /**
     * 更新price中的合同Id
     *
     * @param ttId
     * @param contractId
     * @return
     */
    int updateContractId(Integer ttId, Integer contractId);

    /**
     * 根据ttId获取TTadd
     *
     * @param ttId
     * @return
     */
    TTAddEntity getTTAddEntityByTTId(Integer ttId);
}
