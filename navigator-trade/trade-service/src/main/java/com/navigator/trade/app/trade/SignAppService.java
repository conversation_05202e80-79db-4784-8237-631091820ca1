package com.navigator.trade.app.trade;

import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 协议内容操作
 * <AUTHOR>
 * @Description
 * @Date 2024/7/19 22:34
 * @Version 1.0
 */
public interface SignAppService {


    /**
     * 确认合同合规
     *
     * @param contractBaseDTO
     * @return
     */
    boolean confirmContract(ContractBaseDTO contractBaseDTO);

}
