package com.navigator.trade.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.TemplateMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/3 18:57
 */
@Dao
public class TemplateDao extends BaseDaoImpl<TemplateMapper, TemplateEntity> {

    public TemplateEntity getTemplateByCodeAndType(String code, Integer type) {
        List<TemplateEntity> templateEntityList = this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(StringUtils.isNotBlank(code), TemplateEntity::getCode, code.trim())
                .eq(null != type, TemplateEntity::getType, type));
        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
    }

    public List<TemplateEntity> getTemplateByCodeList(List<String> codeList) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(TemplateEntity::getCode, codeList));
    }

    public List<TemplateEntity> findAllTemplate(List<Integer> typeList, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(!CollectionUtils.isEmpty(typeList), TemplateEntity::getType, typeList)
                .eq(null != status, TemplateEntity::getStatus, status)
                .orderByAsc(TemplateEntity::getId)
        );
    }

    public IPage<TemplateEntity> queryTemplate(QueryDTO<QueryTemplateDTO> queryDTO) {
        QueryTemplateDTO queryTemplateDTO = queryDTO.getCondition();
        Integer type = null == queryTemplateDTO ? null : queryTemplateDTO.getType();
        Integer status = null == queryTemplateDTO ? null : queryTemplateDTO.getStatus();
        String code = null == queryTemplateDTO ? null : queryTemplateDTO.getCode();
        String name = null == queryTemplateDTO ? null : queryTemplateDTO.getName();
        String contentKey = null == queryTemplateDTO ? null : queryTemplateDTO.getContentKey();
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), new LambdaQueryWrapper<TemplateEntity>()
                .eq(null != type, TemplateEntity::getType, type)
                .eq(null != status, TemplateEntity::getStatus, status)
                .eq(StringUtils.isNotBlank(code), TemplateEntity::getCode, code)
                .like(StringUtils.isNotBlank(name), TemplateEntity::getName, "%" + name + "%")
                .like(StringUtils.isNotBlank(contentKey), TemplateEntity::getContent, "%" + contentKey + "%")
                .orderByAsc(TemplateEntity::getId)
        );
    }

    public TemplateEntity getLoginSignature(Integer value) {
        List<TemplateEntity> templateEntityList = this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(TemplateEntity::getType, value));
        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
    }
}
