package com.navigator.trade.app.sign.domain.service;

import com.navigator.trade.dao.ContractSignDetailDao;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.ContractSignDetailEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;

/**
 * 处理协议的写操作包括协议正本信息处理
 * <AUTHOR>
 * @date 20240715
 */
public interface ContractSignDomainService {

    /**
     * 保存协议详情信息
     * @param contractSignDetailEntity
     */
    void saveContractSignDetail(ContractSignDetailEntity contractSignDetailEntity);

    /**
     * 保存合同正本
     * @param contractPaperDTO
     * @return
     */
    boolean saveContractPaper(ContractPaperDTO contractPaperDTO);

    /**
     * 更新协议正本
     * @param contractPaperEntity
     * @return
     */
    boolean updatePaperEntity(ContractPaperEntity contractPaperEntity);

    /**
     * 更新协议信息
     * @param contractSignEntity
     * @return
     */
    boolean updateSignEntity(ContractSignEntity contractSignEntity);

    /**
     * 保存协议
     * @param contractSignEntity
     */
    boolean saveContractSign(ContractSignEntity contractSignEntity);

    /**
     * 根据TTID和合同ID进行更新
     * @param ttId
     * @param contractId
     * @return
     */
    int updateContractId(Integer ttId, Integer contractId);

    /**
     * 更新协议的仓单信息
     * @param signId
     * @param warrantId
     * @param warrantCode
     * @return
     */
    boolean updateSignWarrantId(Integer signId, Integer warrantId, String warrantCode);
}
