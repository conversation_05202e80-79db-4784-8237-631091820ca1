package com.navigator.trade.app.tt.logic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.enums.AllocateTypeEnum;
import com.navigator.future.enums.ContraryStatusEnum;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.logic.service.ContractLogicService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.TTHandleFactory;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.domain.service.TTDomainService;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.app.tt.logic.service.TTLogicService;
import com.navigator.trade.app.tt.logic.service.handler.ITTSceneHandler;
import com.navigator.trade.app.tt.logic.service.handler.impl.TTApproveHandler;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.dao.TtModifyDao;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffWithDrawDTO;
import com.navigator.trade.pojo.dto.contract.WriteOffContractDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignReviewDTO;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.TTQueryVO;
import com.navigator.trade.service.tradeticket.impl.convert.TTDTOConverter;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketConvertUtil;
import com.navigator.trade.service.tradeticket.impl.convert.TradeTicketDOConvert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.navigator.common.constant.RedisConstants.CONTRACT_SAVE_TT_TIMES;


/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
@Service
public class TTLogicServiceImpl implements TTLogicService {

    @Autowired
    TTDomainService ttDomainService;
    @Autowired
    TradeTicketDao tradeTicketDao;
    @Autowired
    TtModifyDao ttModifyDao;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    TTQueryDomainService ttQueryDomainService;

    @Autowired
    TTApproveHandler ttApproveHandler;
    @Autowired
    TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    @Lazy
    ContractLogicService contractLogicService;
    @Autowired
    @Lazy
    ContractQueryLogicService contractQueryLogicService;
    @Autowired
    @Lazy
    ContractSignLogicService contractSignLogicService;
    @Autowired
    @Lazy
    ContractSignQueryLogicService contractSignQueryLogicService;
    /**
     * TODO 待优化
     */
    @Autowired
    protected TradeTicketConvertUtil tradeTicketConvertUtil;

    @Autowired
    TTHandleFactory ttHandleFactory;
    @Autowired
    TradeTicketDOConvert tradeTicketDOConvert;
    @Autowired
    private PriceApplyFacade priceApplyFacade;
    @Autowired
    CommonLogicService commonLogicService;


    @Override
    public Result<List<TTDTO>> prepareContractCreateData(SubmitTTDTO submitTTDTO) {

        Result<List<TTDTO>> rtnPrepare = Result.success();
        List<TTDTO> listTTDTO = new ArrayList<>();

        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(submitTTDTO.getCreateTradeTicketDTO().getType());

        List<TTDTO> ttdtoList = TTDTOConverter.convert2TTDTO(submitTTDTO);

        for (TTDTO ttdto : ttdtoList) {
            TTSubmitResultDTO ttSubmitResultDTO = prepareContractCreateData(ttdto, ttTypeEnum);
            if (!ttSubmitResultDTO.getCheckResult()) {
                rtnPrepare.setSuccess(false);
                rtnPrepare.setMessage(rtnPrepare.getMessage() + ttSubmitResultDTO.getErrorMsg().toString());
            }
        }

        rtnPrepare.setData(ttdtoList);

        return rtnPrepare;

    }

    private TTSubmitResultDTO prepareContractCreateData(TTDTO ttdto, TTTypeEnum ttTypeEnum) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();

        // 路由到具体TT类型处理器
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(ttTypeEnum);

        ttSubmitResultDTO = ttSceneHandler.prepareContractCreateData(ttdto);

        return ttSubmitResultDTO;
    }

    @Override
    public TTSubmitResultDTO saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();

        // 路由到具体业务场景的TT处理器（Logic层）
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(ttdto.getTTTypeEnum());

        //转换为TradeTicketDO
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.convert2TradeTicketDO(ttdto);

        //保存数据
        tradeTicketDO = ttSceneHandler.saveTradeTicketDomainData(tradeTicketDO);

        arrangeContext.setTradeTicketDO(tradeTicketDO);

        ttSubmitResultDTO = convert2TTSubmitResultDTO(tradeTicketDO);

        return ttSubmitResultDTO;
    }

    @Override
    public List<TTSubmitResultDTO> submitTradeTicketDomainData(List<TTDTO> ttdtoList, List<ArrangeContext> arrangeContextList) {
        List<TTSubmitResultDTO> ttSubmitResultDTOList = new ArrayList<>();

        for (TTDTO ttdto : ttdtoList) {
            ArrangeContext arrangeContext = new ArrangeContext();
            TTSubmitResultDTO ttSubmitResultDTO = submitTradeTicketDomainData(ttdto, arrangeContext);
            arrangeContextList.add(arrangeContext);
        }

        return ttSubmitResultDTOList;
    }

    public TTSubmitResultDTO submitTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();
        // 路由到具体业务场景的TT处理器（Logic层）
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(ttdto.getTTTypeEnum());
        //转换为TradeTicketDO
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.convert2TradeTicketDO(ttdto);
        // 提交时，需要进行数据校验
        if (ttdto.getSubmitType() == SubmitTypeEnum.SUBMIT.getValue()) {

        }
        //保存数据
        tradeTicketDO = ttSceneHandler.saveTradeTicketDomainData(tradeTicketDO);

        arrangeContext.setTradeTicketDO(tradeTicketDO);

        //返回值
        ttSubmitResultDTO = convert2TTSubmitResultDTO(tradeTicketDO);

        return ttSubmitResultDTO;
    }

    public TTSubmitResultDTO submitTradeTicketDomainData(ArrangeContext arrangeContext) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();
        TradeTicketDO tradeTicketDO = arrangeContext.getTradeTicketDO();
        // 路由到具体业务场景的TT处理器（Logic层）
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(tradeTicketDO.getTTType());

        tradeTicketDO = ttSceneHandler.saveTradeTicketDomainData(tradeTicketDO);

        Integer contractId = tradeTicketDO.getTradeTicketEntity().getContractId();
        if (null != contractId && contractId > 0) {
            ttApproveHandler.submit(tradeTicketDO.getTradeTicketEntity().getId(), arrangeContext);

            //TODO
            RecordBizOperationDTO recordBizOperationDTO = arrangeContext.getRecordBizOperationDTO();
            TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
            tradeTicketEntity.setApprovalType(recordBizOperationDTO.getApproveDTO().getApproveRuleValue());
            tradeTicketEntity.setApprovalStatus(recordBizOperationDTO.getApproveResultDTO().getApproveResult());

            tradeTicketDao.updateById(tradeTicketEntity);

        }

        return ttSubmitResultDTO;

    }

    /**
     * 保存TT（兼容：业务类型-仓现 采销类型 品类-粕油二 业务场景-新增修改拆分）
     *
     * @param ttdto
     * @return
     */
    @Override
    public List<TTQueryVO> saveTT(TTDTO ttdto, ArrangeContext arrangeContext) {

        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(ttdto.getTtType());
        // 路由到具体TT类型处理器
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(ttTypeEnum);
        return ttSceneHandler.saveTradeTicketDomainData(ttdto, arrangeContext);
    }

    @Override
    public Boolean updateContractId(TradeTicketDO tradeTicketDO) {
        return ttDomainService.updateContractId(tradeTicketDO);
    }

    @Override
    public Boolean updateTTAddWarrantId(Integer ttId, Integer warrantId, String warrantCode) {
        return ttDomainService.updateTTAddWarrantId(ttId, warrantId, warrantCode);
    }

    @Override
    public void approveTT(ApprovalDTO approvalDTO, Integer ttType) {
        ttApproveHandler.approveTT(approvalDTO, ttType);
    }

    @Override
    public void splitTTPriceCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO) {
        if (contractModifyDTO.getConfirmPriceDTOList() != null) {
            contractModifyDTO.getConfirmPriceDTOList().forEach(confirmPriceDTO -> {
                Integer ttPriceId = confirmPriceDTO.getTtPriceId();
                TTPriceEntity ttPriceEntity = ttQueryDomainService.fetchTTPriceEntity(new TTPriceQO().setNeId(ttPriceId));
                if (null != ttPriceEntity && BigDecimalUtil.isGreater(confirmPriceDTO.getConfirmNum(), ttPriceEntity.getNum())) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_TOTAL_PRICE_NUM_EXCEPTION);
                }
            });
        }
    }

    @Override
    public void splitTTPrice(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, ArrangeContext
            arrangeContext) {
        // 保存拆分定价单信息以json格式存到TT主表中
        if (contractModifyDTO.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {
            String confirmPriceInfo = JSON.toJSONString(contractModifyDTO.getConfirmPriceDTOList());

            arrangeContext.setConfirmPriceInfo(confirmPriceInfo);
        } else {
            // 正常拆分定价单则生成具体的定价单信息
            List<com.navigator.trade.pojo.dto.future.ConfirmPriceDTO> confirmPriceDTOList = contractModifyDTO.getConfirmPriceDTOList();
            if (CollUtil.isNotEmpty(confirmPriceDTOList)) {
                for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {

                    TTPriceEntity priceEntity = ttQueryDomainService.fetchTTPriceEntity(new TTPriceQO().setNeId(confirmPriceDTO.getTtPriceId()));

                    if (priceEntity != null) {

                        TTPriceEntity newPriceEntity = BeanUtil.toBean(priceEntity, TTPriceEntity.class);
                        // 保存新定价单信息
                        int sourceId = priceEntity.getId();
                        newPriceEntity
                                .setId(null)
                                .setSourceContractId(priceEntity.getContractId())
                                .setContractCode(contractEntity.getContractCode())
                                .setContractId(contractEntity.getId())
                                .setNum(confirmPriceDTO.getConfirmNum())
                                .setSourceId(sourceId)
                                .setCreatedAt(DateTimeUtil.now())
                                .setUpdatedAt(DateTimeUtil.now())
                                // BUGFIX：case-1002982 实际点价一次，但在销售TT中却出现两笔点价信息 Author: Mr 2025-02-24
                                .setTtId(null)
                        ;
                        ttDomainService.saveTtPrice(newPriceEntity);

                        // 更新原定价单的信息
                        ttDomainService.updateTtPrice(priceEntity.setNum(BigDecimal.ZERO));
                    }
                }
            }
        }
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer
            ttId, Integer ttType) {
        if (TTTypeEnum.SPLIT.getType().equals(ttType)) {
            tradeTicketConvertUtil.updateModifyContent(originalContractEntity, contractEntity, ttId);
            return;
        }
        tradeTicketConvertUtil.updateAddContent(originalContractEntity, contractEntity, ttId, ttType);
    }

    @Override
    public void updateSignInfo(Integer ttId, ContractSignEntity contractSignEntity) {
        ttDomainService.updateSignInfo(ttId, contractSignEntity);
    }

    @Override
    public void deleteTT(Integer ttId) {
        tradeTicketDao.deleteById(ttId);
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        // 删除：一个合同仅支持保存一个变更TT
        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + tradeTicketEntity.getContractCode());
        if (StringUtils.isNotBlank(saveTimes) && StringUtils.isNotBlank(tradeTicketEntity.getContractCode())) {
            redisUtil.del(CONTRACT_SAVE_TT_TIMES + tradeTicketEntity.getContractCode());
        }
        // 处理下如果TT是回购的TT，那么需要还原合同
        if (TTTypeEnum.BUYBACK.getType() == tradeTicketEntity.getType()) {
            contractLogicService.updateBuyBackContract(tradeTicketEntity.getSourceContractId(), tradeTicketEntity.getChangeContractNum());
        }
        // 记录TT的日志
        tradeDomainRemoteService.recordTTOperationDetail(OperationActionEnum.DELETE_TT, tradeTicketEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTT(OperateTTDTO operateTTDTO) {
        Integer ttId = operateTTDTO.getTtId();
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        // TODO 新增，结构化定价,作废
        if (tradeTicketEntity.getType() == TTTypeEnum.NEW.getType()
                || tradeTicketEntity.getType() == TTTypeEnum.STRUCTURE_PRICE.getType()) {
            String userId = JwtUtils.getCurrentUserId();
            //TODO 取消工作流审批
            tradeDomainRemoteService.cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
            //修改tt状态为待修改提交
            tradeTicketDao.updateStatusById(TTStatusEnum.WAITING.getType(), null, operateTTDTO.getTtId());
            //取消协议
            ContractSignEntity contractSignEntity = contractSignQueryLogicService.getContractSignDetailByTtId(tradeTicketEntity.getId());
            contractSignLogicService.abnormalContractSign(contractSignEntity.getId(), operateTTDTO.getMemo());
        } else {
            String userId = JwtUtils.getCurrentUserId();
            //TODO 取消工作流审批
            tradeDomainRemoteService.cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
            //作废合同 ,TT ,协议
            handleCancelOrRejectResult(tradeTicketEntity, operateTTDTO.getMemo());
        }
        //记录操作日志
        tradeDomainRemoteService.recordTTOperationDetail(OperationActionEnum.CANCEL_SUBMIT, tradeTicketEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidTT(OperateTTDTO operateTTDTO) {
        Integer ttId = operateTTDTO.getTtId();
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(ttId);
        invalid(operateTTDTO, tradeTicketEntity);
        // 保存文件关系表
        tradeDomainRemoteService.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(tradeTicketEntity.getSignId())
                .setCategoryType(FileCategoryType.CANCEL_CONTRACT.getCode())
                .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setFileIdList(operateTTDTO.getFileIdList())
        );
        //记录操作日志
        tradeDomainRemoteService.recordTTOperationDetail(OperationActionEnum.CANCEL_TT, tradeTicketEntity);
    }

    /**
     * 作废处理过程
     *
     * @param operateTTDTO
     * @param tradeTicketEntity
     */
    public void invalid(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        String userId = JwtUtils.getCurrentUserId();
        //tt审批作废
        Integer status = tradeTicketEntity.getStatus();
        Integer approvalStatus = tradeTicketEntity.getApprovalStatus();
        if (TTStatusEnum.DONE.getType() == status || TTApproveStatusEnum.REJECT.getValue() == approvalStatus) {
            tradeDomainRemoteService.cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
        }
        String memo = operateTTDTO.getMemo();
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), memo, operateTTDTO.getTtId());
        //作废合同
        ContractBaseDTO contractBaseDTO = new ContractBaseDTO();
        contractBaseDTO.setContractId(tradeTicketEntity.getContractId());
        contractBaseDTO.setInvalidReason(memo);
        contractLogicService.invalidContractByTT(contractBaseDTO);
        //作废协议
        ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
        contractSignReviewDTO.setTtId(operateTTDTO.getTtId())
                .setReviewRemark(memo)
                .setContractSignId(tradeTicketEntity.getSignId());
        contractSignLogicService.invalidContractSign(contractSignReviewDTO);

        //结构化定价作废释放量
        if (TTTypeEnum.STRUCTURE_PRICE.getType() == tradeTicketEntity.getType()) {
            // TODO 存在事务的问题，上面处理了合同下面远程调用也处理合同
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    priceApplyFacade.closePriceApplyByContractId(tradeTicketEntity.getContractId());
                }
            });

        }

        // 处理下如果TT是回购的TT，那么需要还原合同
        if (TTTypeEnum.BUYBACK.getType() == tradeTicketEntity.getType()) {
            contractLogicService.updateBuyBackContract(tradeTicketEntity.getSourceContractId(), tradeTicketEntity.getChangeContractNum());
        }
    }


    @Override
    public void invalidTTByContractId(Integer contractId) {
        TradeTicketQO ticketQO = new TradeTicketQO();
        ticketQO.setContractId(contractId);
        TradeTicketEntity tradeTicketEntity = ttQueryDomainService.fetchTradeTicketEntity(ticketQO);
        String userId = JwtUtils.getCurrentUserId();
        //tt审批作废，注销撤回作废
        Integer status = tradeTicketEntity.getStatus();
        Integer approvalStatus = tradeTicketEntity.getApprovalStatus();
        if (TTStatusEnum.DONE.getType() == status || TTApproveStatusEnum.REJECT.getValue() == approvalStatus) {
            // TODO
            tradeDomainRemoteService.cancelActiviti("注销撤回作废", userId, tradeTicketEntity);
        }
        ttDomainService.invalidTTById(TTStatusEnum.INVALID.getType(), "", tradeTicketEntity.getId());
        //记录操作日志
        tradeDomainRemoteService.recordTTOperationDetail(OperationActionEnum.CANCEL_TT, tradeTicketEntity);
    }

    @Override
    public void updateTTStatusById(Integer ttId, TTStatusEnum statusEnum) {
        tradeTicketDao.updateStatusById(statusEnum.getType(), null, ttId);
    }

    @Override
    public void saveTTCheck(TTDTO ttdto) {
        SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
        if (salesContractAddTTDTO != null && StringUtils.isBlank(salesContractAddTTDTO.getSiteCode())) {
            throw new BusinessException(ResultCodeEnum.SITE_NOT_EXIST);
        }
    }

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Result contraryPrice(ContraryPriceDTO contraryPriceDTO) {
        PriceAllocateEntity priceAllocateEntity = tradeDomainRemoteService.getPriceAllocateById(String.valueOf(contraryPriceDTO.getAllocateId()));
        Integer ttType;
        if (PriceTypeEnum.TRANSFER_MONTH.getValue() == contraryPriceDTO.getPriceApplyType() ||
                PriceTypeEnum.REVERSE_PRICING.getValue() == contraryPriceDTO.getPriceApplyType()) {
            return monthReverseAllocatePortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        } else {
            return priceAllocatePortion(priceAllocateEntity, contraryPriceDTO.getContraryCause());
        }
    }

    @Override
    public void withdrawContractLog(ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO) {
        // 过滤掉提货权合同-处理下TT和协议的作废操作
        List<WriteOffContractDTO> writeOffContractDTOList = contractWriteOffWithDrawDTO.getWriteOffContractDTOS()
                .stream().filter(item -> !ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(item.getContractNature())).collect(Collectors.toList());
        String groupId = null;
        for (WriteOffContractDTO writeOffContractDTO : writeOffContractDTOList) {
            // 根据合同ID获取TT
            ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(writeOffContractDTO.getReferContractId());
            TradeTicketEntity tradeTicketEntity = ttQueryDomainService.getByContractId(writeOffContractDTO.getReferContractId(), null);
            tradeDomainRemoteService.addContractOperationLog(tradeTicketEntity, LogBizCodeEnum.WRITE_OFF_WITH_DRAW,
                    JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
            groupId = tradeTicketEntity.getGroupId();
        }
        // 原合同记录下日志
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractWriteOffWithDrawDTO.getContractId());
        TradeTicketEntity tradeTicketEntity = ttQueryDomainService.getByContractId(contractWriteOffWithDrawDTO.getContractId(), groupId);
        tradeDomainRemoteService.addContractOperationLog(tradeTicketEntity, LogBizCodeEnum.WRITE_OFF_WITH_DRAW,
                JSONUtil.toJsonStr(contractEntity), SystemEnum.MAGELLAN.getValue());
    }

    @Override
    public List<TTPriceEntity> withdrawPrice(Integer contractId) {
        TTPriceQO ttPriceQO = new TTPriceQO();
        ttPriceQO.setContractId(contractId);
        List<TTPriceEntity> priceEntities = ttQueryDomainService.fetchTTPriceEntities(ttPriceQO);
        if (ObjectUtil.isNotEmpty(priceEntities)) {
            for (TTPriceEntity item : priceEntities) {
                item.setNum(BigDecimal.ZERO);
                item.setContraryStatus(ContraryStatusEnum.CONTRARY.getValue());
                item.setMemo("合同作废,定价单撤回");
                ttDomainService.updateTtPrice(item);
            }
        }
        return priceEntities;
    }


    /**
     * 操作撤回TT
     *
     * @param tradeTicketEntity
     * @param memo
     */
    public void handleCancelOrRejectResult(TradeTicketEntity tradeTicketEntity, String memo) {
        //合同作废
        ContractModifyDTO contractModifyDTO = new ContractModifyDTO();
        contractModifyDTO.setTtId(tradeTicketEntity.getId());
        contractModifyDTO.setContractId(tradeTicketEntity.getContractId());
        contractModifyDTO.setTtType(tradeTicketEntity.getType());
        contractModifyDTO.setContractSource(tradeTicketEntity.getContractSource());
        contractModifyDTO.setTtTradeType(tradeTicketEntity.getTradeType());
        // 取消合同
        contractLogicService.cancelContractModify(contractModifyDTO);
        // 作废TT
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), memo, tradeTicketEntity.getId());
        //作废协议
        ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
        contractSignReviewDTO.setTtId(tradeTicketEntity.getId())
                .setReviewRemark(memo)
                .setContractSignId(tradeTicketEntity.getSignId());
        contractSignLogicService.invalidContractSign(contractSignReviewDTO);
        if (tradeTicketEntity.getType().equals(TTTypeEnum.SPLIT.getType())) {
            TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(tradeTicketEntity.getId());
            TTModifyEntity modifyByRelationOther = ttModifyDao.getModifyByRelationTTId(ttModifyEntity.getRelationId(), tradeTicketEntity.getId());
            if (modifyByRelationOther != null) {
                tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), "关联作废:" + memo, modifyByRelationOther.getTtId());
                TradeTicketEntity tradeTicketEntityOther = tradeTicketDao.getTradeTicketEntityById(modifyByRelationOther.getTtId());
                if (null != tradeTicketEntityOther.getSignId() && tradeTicketEntityOther.getSignId() != 0) {
                    contractSignReviewDTO.setTtId(tradeTicketEntityOther.getId())
                            .setReviewRemark("关联作废:" + memo)
                            .setContractSignId(tradeTicketEntityOther.getSignId());
                    contractSignLogicService.invalidContractSign(contractSignReviewDTO);
                    //TODO 取消工作流审批
                    tradeDomainRemoteService.cancelActiviti("关联作废:" + memo, JwtUtils.getCurrentUserId(), tradeTicketEntityOther);
                }
            }
        }
        // 注销C的情况，如果是注销的TT并且是C的情况
        if (tradeTicketEntity.getType().equals(TTTypeEnum.WRITE_OFF.getType())
                && ContractTradeTypeEnum.WRITE_OFF_C.getValue() == tradeTicketEntity.getTradeType()) {
            TradeTicketEntity tradeTicketOther = ttQueryDomainService.getWarrantByGroupId(tradeTicketEntity.getGroupId(), tradeTicketEntity.getId());
            if (tradeTicketOther != null) {
                tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), "关联作废:" + memo, tradeTicketOther.getId());
                if (null != tradeTicketOther.getSignId() && tradeTicketOther.getSignId() != 0) {
                    contractSignReviewDTO.setTtId(tradeTicketOther.getId())
                            .setReviewRemark("关联作废:" + memo)
                            .setContractSignId(tradeTicketOther.getSignId());
                    contractSignLogicService.invalidContractSign(contractSignReviewDTO);
                    //TODO 取消工作流审批
                    tradeDomainRemoteService.cancelActiviti("关联作废:" + memo, JwtUtils.getCurrentUserId(), tradeTicketOther);
                }
            }
        }

        // 如果是作废TT的撤回需要关闭redis
        if (TTTypeEnum.INVALID.getType().equals(tradeTicketEntity.getType())) {
            String saveTimes = redisUtil.getString(RedisConstants.CONTRACT_INVALID_TT_SAVE + tradeTicketEntity.getContractCode());
            if (StringUtils.isNotBlank(saveTimes)) {
                redisUtil.del(RedisConstants.CONTRACT_INVALID_TT_SAVE + tradeTicketEntity.getContractCode());
            }
        }

    }


    /**********************************转月和反点价撤回 Start ***************************/

    /**
     * 部分撤回 | 转月 | 反点价
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result monthReverseAllocatePortion(PriceAllocateEntity priceAllocateEntity, String contraryCause) {
        String userId = JwtUtils.getCurrentUserId();
        //撤回反点价
        //部分反点价,部分转月
        //根据申请单查询priceApplyId查询TTTranferEntity;
        List<TTTranferEntity> ttTranferEntities = new ArrayList<>();
        if (ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocateEntity.getSalesType()) {
            ttTranferEntities = ttQueryDomainService.getTTTranferByPriceApplyId(priceAllocateEntity.getPriceApplyId());
        } else {
            ttTranferEntities = ttQueryDomainService.getTTTranferByPriceAllocateId(priceAllocateEntity.getId());
        }
        for (TTTranferEntity ttTranferEntity : ttTranferEntities) {
            //根据TTid转月单
            ContractSignEntity contractSignEntity = contractSignQueryLogicService.getContractSignDetailByTtId(ttTranferEntity.getTtId());
            //1,撤回转月,反点价单
            BigDecimal priceApplyNum = ttTranferEntity.getNum();
            ttTranferEntity.setNum(BigDecimal.ZERO)
                    .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                    .setMemo(contraryCause);
            ttDomainService.updateTTTranferById(ttTranferEntity);
            //2,撤回TT
            //修改tt状态为待修改提交
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttTranferEntity.getTtId());
            tradeTicketEntity.setCancelReason(contraryCause)
                    .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                    .setStatus(TTStatusEnum.INVALID.getType());

            tradeTicketDao.updateById(tradeTicketEntity);
            //取消工作流审批
            tradeDomainRemoteService.cancelActiviti(contraryCause, userId, tradeTicketEntity);
            //3,撤回协议
            ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
            contractSignReviewDTO.setTtId(tradeTicketEntity.getId())
                    .setReviewRemark(contraryCause)
                    .setContractSignId(contractSignEntity.getId());
            contractSignLogicService.invalidContractSign(contractSignReviewDTO);
            // 全部撤回的处理
            if (AllocateTypeEnum.ALL.getValue() == priceAllocateEntity.getType()) {
                monthReverseAllocateAll(ttTranferEntity, priceAllocateEntity);
            } else {
                //4,修改合同数量
                ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttTranferEntity.getSourceContractId());
                BigDecimal contractNum = contractEntity.getContractNum().add(priceAllocateEntity.getAllocateNum());
                //拆分数量
                boolean b = contraryTTTranfer(contractEntity.getId(), ttTranferEntity.getId());
                contractEntity
                        .setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus())
                        .setContractNum(contractNum);

                if (TTTypeEnum.TRANSFER.getType().equals(tradeTicketEntity.getType())) {
                    BigDecimal totalTransferNum = contractEntity.getTotalTransferNum().subtract(priceApplyNum);
                    contractEntity.setTotalTransferNum(totalTransferNum);
                }
                // N-K nav头寸处理改造 changed by Mr at 2025-07-25 start
                // 反点价合同状态-修改中
                if (TTTypeEnum.REVERSE_PRICE.getType().equals(tradeTicketEntity.getType())) {
                    contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
                }
                // N-K nav头寸处理改造 changed by Mr at 2025-07-25 end
                List<CompareObjectDTO> compareObjectDTOS = JSON.parseArray(ttTranferEntity.getContent(), CompareObjectDTO.class);
                contraryContract(compareObjectDTOS, contractEntity, AllocateTypeEnum.PART.getValue());
                contractLogicService.updateContract(contractEntity);
                ContractEntity contract = contractQueryLogicService.getBasicContractById(ttTranferEntity.getContractId());
                contract.setStatus(ContractStatusEnum.INVALID.getValue());
                contractLogicService.updateContract(contract);
            }
        }
        return Result.success();
    }

    /**
     * 全部撤回
     *
     * @param ttTranferEntity
     * @param priceAllocateEntity
     */
    private void monthReverseAllocateAll(TTTranferEntity ttTranferEntity, PriceAllocateEntity priceAllocateEntity) {
        //5,修改合同
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttTranferEntity.getSourceContractId());
        boolean b = contraryTTTranfer(contractEntity.getId(), ttTranferEntity.getId());
        contractEntity
                .setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus())
        ;

        //转月,反点价次数修改
        if (ContractSalesTypeEnum.SALES.getValue() == contractEntity.getSalesType()) {
            if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()) {
                //转月次数
                contractEntity.setAbleTransferTimes(contractEntity.getAbleTransferTimes() + 1)
                        .setTransferredTimes(contractEntity.getTransferredTimes() - 1);
                // BUGFIX：case-1003029 反点价撤回次数问题 Author: Mr 2025-03-05 Start
            /*} else {
                //反点价次数
                contractEntity.setAbleReversePriceTimes(contractEntity.getAbleReversePriceTimes() + 1)
                        .setReversedPriceTimes(contractEntity.getReversedPriceTimes() - 1);*/
                // BUGFIX：case-1003029 反点价撤回次数问题 Author: Mr 2025-03-05 Start
            }
        }

        if (TTTranferTypeEnum.REVERSE_PRICING.getValue().equals(ttTranferEntity.getType())) {
            BigDecimal contractNum = contractEntity.getContractNum().add(priceAllocateEntity.getAllocateNum());
            contractEntity.setContractNum(contractNum);
            ContractEntity contract = contractQueryLogicService.getBasicContractById(ttTranferEntity.getContractId());
            contract.setStatus(ContractStatusEnum.INVALID.getValue());
            contractLogicService.updateContract(contract);
        }
        //修改合同价格
        List<CompareObjectDTO> compareObjectDTOS = JSON.parseArray(ttTranferEntity.getContent(), CompareObjectDTO.class);
        //查询合同price
        contraryContract(compareObjectDTOS, contractEntity, AllocateTypeEnum.ALL.getValue());
    }

    /**
     * 计算拆分量情况
     *
     * @param contractId
     * @param ttTranferId
     * @return
     */
    private boolean contraryTTTranfer(Integer contractId, Integer ttTranferId) {
        List<TTTranferEntity> ttTranferEntities = ttQueryDomainService.getTTTranferByContractId(contractId, ttTranferId);
        for (TTTranferEntity ttTranferEntity : ttTranferEntities) {
            ContractSignEntity contractSignEntity = contractSignQueryLogicService.getContractSignDetailByTtId(ttTranferEntity.getTtId());
            if (null != contractSignEntity && ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSignEntity.getStatus()) {
                return false;
            }
        }
        return true;
    }

    private void contraryContract(List<CompareObjectDTO> compareObjectDTOS, ContractEntity contractEntity, Integer allocateType) {
        ContractPriceEntity contractPriceEntity = ttQueryDomainService.getContractPriceEntityContractId(contractEntity.getId());
        compareObjectDTOS.forEach(i -> {
            //含税单价
            if (i.getName().equals("cifUnitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setCifUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //含税单价
            if (i.getName().equals("unitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //合同总价
            if (i.getName().equals("totalAmount")) {
                if (AllocateTypeEnum.ALL.getValue() == allocateType) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        contractEntity.setTotalAmount(new BigDecimal(i.getBefore()));
                    }
                } else if (AllocateTypeEnum.PART.getValue() == allocateType) {
                    if (StringUtil.isNotEmpty(i.getBefore())) {
                        //合同总价
                        BigDecimal totalAmount = contractEntity.getContractNum().multiply(contractEntity.getUnitPrice());
                        contractEntity.setTotalAmount(totalAmount);
                    }

                }

            }
            //应付履约保证金金额
            BigDecimal depositAmount = contractEntity.getTotalAmount().multiply(BigDecimal.valueOf((contractEntity.getDepositRate() * 0.01)));
            contractEntity.setDepositAmount(depositAmount);

            //基差价格
            if (i.getName().equals("extraPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                    contractPriceEntity.setExtraPrice(new BigDecimal(i.getBefore()));
                }
            }
            //手续费
            if (i.getName().equals("fee")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractPriceEntity.setFee(new BigDecimal(i.getBefore()));
                }
            }
            //期货价
            if (i.getName().equals("forwardPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractPriceEntity.setForwardPrice(new BigDecimal(i.getBefore()));
                }
            }
            //含税单价-物流相关费用
            if (i.getName().equals("fobUnitPrice")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setFobUnitPrice(new BigDecimal(i.getBefore()));
                }
            }
            //期货合约
            if (i.getName().equals("domainCode")) {
                if (StringUtil.isNotEmpty(i.getBefore())) {
                    contractEntity.setDomainCode(i.getBefore());
                }
            }
        });
        ttDomainService.updatePriceByContractId(contractPriceEntity);
        contractLogicService.updateContract(contractEntity);
    }

    /**********************************转月和反点价撤回 end ***************************/

    /**********************************点价撤回 Start ******************************/


    /**
     * 部分撤回
     *
     * @param priceAllocateEntity
     * @return
     */
    private Result priceAllocatePortion(PriceAllocateEntity priceAllocateEntity, String contraryCause) {
        String userId = JwtUtils.getCurrentUserId();
        //撤回点价
        //部分点价
        List<TTPriceEntity> ttPriceEntities = new ArrayList<>();
        if (ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocateEntity.getSalesType()) {
            ttPriceEntities = ttQueryDomainService.getTTPriceByApplyId(priceAllocateEntity.getPriceApplyId());
        } else {
            ttPriceEntities = ttQueryDomainService.getTTPriceByAllocateId(priceAllocateEntity.getId());
        }
        if (ttPriceEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_APPLY);
        }
        TTPriceEntity ttPriceEntity = ttPriceEntities.get(0);
        //遍历定价单
        Integer ttId = ttPriceEntity.getTtId();
        //校验协议是否已经签署
        ContractSignEntity contractSignEntity = contractSignQueryLogicService.getContractSignDetailByTtId(ttPriceEntity.getTtId());
        //查询定价单是否已被拆分
        if (0 != ttPriceEntity.getSourceId()) {
            List<TTPriceEntity> ttPrices = ttQueryDomainService.getTTPriceBySourceId(ttPriceEntity.getSourceId());
            //拆分后不予撤回
            if (!ttPrices.isEmpty()) {
                //不予撤回
                throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_SPLIT_NOT_CONTRARY);
            }
        }
        //1,撤回定价单
        BigDecimal priceApplyNum = ttPriceEntity.getNum();

        ttPriceEntity.setNum(BigDecimal.ZERO)
                .setContraryStatus(ContraryStatusEnum.CONTRARY.getValue())
                .setMemo(contraryCause);
        ttDomainService.updateTTPriceById(ttPriceEntity);
        //2,撤回TT
        //修改tt状态为待修改提交
        TradeTicketEntity tradeTicketEntity = tradeTicketDao.getById(ttPriceEntity.getTtId());
        tradeTicketEntity.setCancelReason(contraryCause)
                .setApprovalStatus(TTApproveStatusEnum.APPROVE.getValue())
                .setStatus(TTStatusEnum.INVALID.getType());

        tradeTicketDao.updateById(tradeTicketEntity);
        //取消工作流审批
        tradeDomainRemoteService.cancelActiviti(contraryCause, userId, tradeTicketEntity);
        //3,撤回协议
        //3,撤回协议
        ContractSignReviewDTO contractSignReviewDTO = new ContractSignReviewDTO();
        contractSignReviewDTO.setTtId(tradeTicketEntity.getId())
                .setReviewRemark(contraryCause)
                .setContractSignId(contractSignEntity.getId());
        contractSignLogicService.invalidContractSign(contractSignReviewDTO);
        //5,修改合同 修改定价数量
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(ttPriceEntity.getContractId());
        priceAllocateAll(contractEntity, ttPriceEntity, priceApplyNum);
        // lkgSyncService.syncTTPriceInfo(ttPriceEntity.getId(), LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
        //撤回平台定价单, 同步定价单
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                commonLogicService.syncTTPriceInfo(contractEntity.getSiteCode(), ttId, LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
            }
        });

        return Result.success();
    }

    /**
     * 全部点价
     *
     * @param contractEntity
     * @param ttPriceEntity
     * @param priceApplyNum
     */
    private void priceAllocateAll(ContractEntity contractEntity, TTPriceEntity ttPriceEntity, BigDecimal priceApplyNum) {
        //定价数量,合同价格
        //计算合同物流相关费用
        BigDecimal deliveryPrice = contractEntity.getUnitPrice().subtract(contractEntity.getFobUnitPrice());
        //计算合同总金额
        BigDecimal totalAmount = ttPriceEntity.getUnitPrice().multiply(contractEntity.getContractNum());
        BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum().subtract(priceApplyNum);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, ttPriceEntity.getUnitPrice(), contractEntity.getTaxRate().add(BigDecimal.ONE));

        contractEntity
                .setTotalPriceNum(oldTotalPriceNum)
                .setUnitPrice(ttPriceEntity.getUnitPrice())
                .setFobUnitPrice(ttPriceEntity.getUnitPrice().subtract(deliveryPrice))
                .setTotalAmount(totalAmount)
                .setCifUnitPrice(cifUnitPrice)
                .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
        //.setStatus(b ? ContractStatusEnum.EFFECTIVE.getValue() : contractEntity.getStatus())
        ;
        contractLogicService.updateContract(contractEntity);
        //查询合同price
        ContractPriceEntity contractPriceEntity = ttQueryDomainService.getContractPriceEntityContractId(contractEntity.getId());
        contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
        //修改合同price
        ttDomainService.updatePriceByContractId(contractPriceEntity);
    }

    /**********************************点价撤回 End ******************************/


    private TTSubmitResultDTO convert2TTSubmitResultDTO(TradeTicketDO tradeTicketDO) {
        TTSubmitResultDTO ttSubmitResultDTO = new TTSubmitResultDTO();
        ttSubmitResultDTO.setTtId(tradeTicketDO.getTradeTicketEntity().getId());
        ttSubmitResultDTO.setTtCode(tradeTicketDO.getTradeTicketEntity().getCode());
        ttSubmitResultDTO.setContractId(tradeTicketDO.getTradeTicketEntity().getContractId());
        ttSubmitResultDTO.setContractCode(tradeTicketDO.getTradeTicketEntity().getContractCode());
        ttSubmitResultDTO.setProtocolCode(tradeTicketDO.getTradeTicketEntity().getProtocolCode());
        ttSubmitResultDTO.setSignId(tradeTicketDO.getTradeTicketEntity().getSignId());
        return ttSubmitResultDTO;
    }

}
