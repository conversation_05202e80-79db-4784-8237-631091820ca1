package com.navigator.trade.service.sync;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class LkgSyncService {
    @Value("${sync.lkg.isOpen:0}")
    private Integer openLkgSync;    // 是否开启lkg同步

    private final ITradeTicketQueryService ticketQueryService;
    private final ITtPriceService ttPriceService;
    private final IContractQueryService contractQueryService;
    private final LkgContractFacade lkgContractFacade;
    private final CustomerFacade customerFacade;

    /**
     * 同步合同信息
     *
     * @param contractEntity 合同信息
     * @param ttId           ttId
     * @param syncType       同步类型
     */
    @Async
    public void syncContractInfo(ContractEntity contractEntity, Integer ttId, int syncType) {
        log.info("{} is working,openLkgSync:{}", Thread.currentThread().getName(), openLkgSync);

        // 合同同步至Lkg
        if (openLkgSync == 1) {
            SyncRequestDTO syncRequestDTO = getSyncRequestDTO(contractEntity, ttId, syncType);

            // 日志记录是否打开atlas同步
            log.info("===============[{}] LKG Sync Open : {}===============", contractEntity.getContractCode(), openLkgSync);
            lkgContractFacade.syncContractRequest(syncRequestDTO);
        }
    }

    /**
     * 同步合同定价单信息
     *
     * @param contractEntity 合同信息
     * @param ttId           ttId
     */
    public void syncContractPriceUpdateInfo(Integer ttId, ContractEntity contractEntity) {
        // 合同同步至Lkg
        if (openLkgSync == 1) {
            // 同步合同信息(类型)到lkg+定价单更新
            List<TTPriceEntity> confirmPriceList = ttPriceService.getConfirmPriceList(contractEntity.getId());

            ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 1,
                    0L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(1024), new ThreadPoolExecutor.AbortPolicy());

            int i = 0;
            for (TTPriceEntity ttPriceEntity : confirmPriceList) {
                try {
                    int j = ++i;
                    executor.submit(() -> {
                        log.info("定价单更新id：{}", ttPriceEntity.getId());

                        // BUGFIX：case-1002982 实际点价一次，但在销售TT中却出现两笔点价信息 Author: Mr 2025-02-24 start
                        int confirmPriceId = (ttPriceEntity.getSourceId() != null && ttPriceEntity.getSourceId() != 0)
                                ? ttPriceEntity.getSourceId()
                                : ttPriceEntity.getId();

                        TTPriceEntity priceEntity = ttPriceService.getById(confirmPriceId);
                        ContractEntity priceContract = contractQueryService.getBasicContractById(priceEntity.getContractId());

                        SyncRequestDTO syncRequestDTO = getSyncRequestDTO(priceContract, ttId, LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());

                        // 定价单id
                        syncRequestDTO.setConfirmPriceId(confirmPriceId);
                        // BUGFIX：case-1002982 实际点价一次，但在销售TT中却出现两笔点价信息 Author: Mr 2025-02-24 end

                        // 合同同步至Lkg
                        lkgContractFacade.syncContractRequest(syncRequestDTO);

                        if (j == confirmPriceList.size()) {
                            executor.shutdown();
                        }

                        // 间隔5s钟执行
                        try {
                            TimeUnit.SECONDS.sleep(5);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                            Thread.currentThread().interrupt();
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("syncPriceUpdate exception:{}", e.getMessage());
                }
            }
        }
    }

    /**
     * 定价单同步
     *
     * @param ttId ttId
     */
    @Async
    public void syncTTPriceInfo(Integer ttId, Integer syncType) {
        TTPriceEntity ttPriceEntity = ttPriceService.getTTPriceEntityByTTId(ttId);
        if (null != ttPriceEntity) {
            // 同步定价信息到到lkg
            try {
                TradeTicketEntity tradeTicket = ticketQueryService.getByTtId(ttId);

                if (null != tradeTicket) {
                    // 获取合同信息
                    ContractEntity contractEntity = contractQueryService.getContractById(ttPriceEntity.getContractId());

                    SyncRequestDTO syncRequestDTO = new SyncRequestDTO();
                    syncRequestDTO.setSyncType(syncType)
                            .setContractType(tradeTicket.getContractType())
                            .setSalesType(tradeTicket.getSalesType())
                            .setTradeType(tradeTicket.getTradeType())
                            .setContractId(contractEntity.getId())
                            .setContractCode(contractEntity.getContractCode())
                            .setParentContractId(contractEntity.getParentId());

                    CustomerDTO customerDTO = customerFacade.getCustomerById(syncRequestDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                            ? contractEntity.getSupplierId() : contractEntity.getCustomerId());

                    syncRequestDTO.setCustomerCode(null != customerDTO ? customerDTO.getLinkageCustomerCode() : "");

                    syncRequestDTO.setTtId(tradeTicket.getId())
                            .setConfirmPriceId(ttPriceEntity.getId());

                    // 同步定价单信息
                    lkgContractFacade.syncContractRequest(syncRequestDTO);
                }
            } catch (Exception e) {
                log.error("合同编号：{}[{}]，定价单同步失败！！！+'\n'+失败原因：{}", ttPriceEntity.getContractCode(), ttPriceEntity.getContractId(), e.getMessage());
            }
        }
    }

    private SyncRequestDTO getSyncRequestDTO(ContractEntity contractEntity, Integer ttId, int syncType) {
        // 获取合同对应的tt内容
        TradeTicketEntity tradeTicket = new TradeTicketEntity();
        if (null != ttId) {
            tradeTicket = ticketQueryService.getByTtId(ttId);
        }

        // 主体拆分的子合同tradeType特殊处理
        int tradeType = tradeTicket.getTradeType();
        if (null != contractEntity && contractEntity.getTradeType() == ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue()) {
            tradeType = ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue();
        }

        // BUGFIX：case-1002958 合同数量传输至LKG错误 Author: Mr 2025-02-18 start
        // 是否转厂
        int changeFactory = (contractEntity != null && contractEntity.getIsChangeFactory() == 1) ? 1 : 0;
        if (changeFactory == 1) {
            TTModifyEntity modifyEntity = ticketQueryService.getLkgTTModifyByTTId(ttId);
            if (modifyEntity != null && modifyEntity.getModifyContent() != null && !modifyEntity.getModifyContent().contains("deliveryFactoryCode")) {
                changeFactory = 0;

                // 更新合同的is_change_factory字段
                contractQueryService.updateContract(contractEntity.setIsChangeFactory(0));
            }
        }
        // BUGFIX：case-1002958 合同数量传输至LKG错误 Author: Mr 2025-02-18 end

        SyncRequestDTO syncRequestDTO = new SyncRequestDTO();
        syncRequestDTO
                .setSyncType(syncType)
                .setContractType(tradeTicket.getContractType())
                .setTtId(tradeTicket.getId())
                .setSalesType(null != contractEntity ? contractEntity.getSalesType() : null)
                .setTradeType(tradeType)
                .setIsChangeFactory(changeFactory)
                .setContractId(null != contractEntity ? contractEntity.getId() : 0)
                .setContractCode(null != contractEntity ? contractEntity.getContractCode() : "")
                .setParentContractId(null != contractEntity ? contractEntity.getParentId() : 0)
                .setCustomerCode(null != contractEntity ? contractEntity.getCustomerCode() : "");
        return syncRequestDTO;
    }
}
