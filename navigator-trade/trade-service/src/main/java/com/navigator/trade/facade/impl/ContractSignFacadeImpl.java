package com.navigator.trade.facade.impl;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.entity.DbzRedAlarmEntity;
import com.navigator.admin.pojo.enums.BizLogModuleEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.customer.pojo.enums.LdcFrameEnum;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.handler.SalesContractSignHandler;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.*;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.IsCustomerSignatureEnum;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import com.navigator.trade.pojo.vo.ContractSignDetailFileVO;
import com.navigator.trade.pojo.vo.ContractSignFileVO;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.contractsign.IContractSignService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 15:23
 */
@RestController
public class ContractSignFacadeImpl implements ContractSignFacade {

    /**
     * TODO 写服务调整
     */
    @Autowired
    private SalesContractSignHandler salesContractSignHandler;
    @Autowired
    OperationLogFacade operationLogFacade;
    /**
     * 协议域 读服务
     */
    @Resource
    private ContractSignQueryLogicService contractSignQueryLogicService;


    @Override
    public Result queryContContractSigns(QueryDTO<ContractSignQO> queryDTO) {
        return contractSignQueryLogicService.queryContContractSigns(queryDTO);
    }

    @Override
    public Result getContractSignStat(QueryContractSignBO signBO) {
        return Result.success(contractSignQueryLogicService.getContractSignStat(signBO));
    }

    @Override
    public ContractSignEntity getContractSignDetailById(Integer contractSignId) {
        return contractSignQueryLogicService.getContractSignDetailById(contractSignId);
    }

    @Override
    public ContractSignEntity getContractSignDetailByTtId(Integer ttId) {
        return contractSignQueryLogicService.getContractSignDetailByTtId(ttId);
    }

    @Override
    public ContractSignAllStatusNumVO getCustomerSignStat(Integer salesType) {
        return contractSignQueryLogicService.getCustomerSignStat(salesType);
    }

    @Override
    public ContractSignAllStatusNumVO getMagellanSignStat(Integer ldcFrame, Integer salesType, Integer goodsCategoryId) {
        return contractSignQueryLogicService.getMagellanSignStat(ldcFrame, salesType, goodsCategoryId);
    }

    /**
     * 根据合同读协议-优化
     * @param contractId 合同Id
     * @param system
     * @return
     */
    @Override
    public List<ContractSignFileVO> getSignFileListByContractId(Integer contractId, Integer system) {
        return contractSignQueryLogicService.getSignFileListByContractId(contractId, system);
    }

    @Override
    public List<ContractSignDetailFileVO> getAllSignFileListById(Integer contractSignId, Integer system) {
        return contractSignQueryLogicService.getAllSignFileListById(contractSignId, system);
    }

    @Override
    public Result UpdateContractSignError(ContractSignErrorDTO contractSignErrorDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.UpdateContractSignError(contractSignErrorDTO));
    }

    @Override
    public boolean updateContractSignYQQUrl(ContractSignYQQUrlDTO contractSignYQQUrlDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return contractSignService.updateContractSignYQQUrl(contractSignYQQUrlDTO);
    }

    @Override
    public boolean updateContractSignYQQCallback(ContractSignYQQCallbackDTO contractSignYQQCallbackDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return contractSignService.updateContractSignYQQCallback(contractSignYQQCallbackDTO);
    }

    @Override
    public ContractSignEntity queryByUUId(String uuid) {
        return contractSignQueryLogicService.queryByUUId(uuid);
    }

    @Override
    public Result saveContractPaperSign(ContractPaperDTO contractPaperDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.saveContractPaperSign(contractPaperDTO));
    }

    @Override
    public Result postBackContractSign(ContractBaseSignDTO contractBaseSignDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.postBackContractSign(contractBaseSignDTO, ContractSignStatusEnum.WAIT_CONFIRM.getValue(), OperationActionEnum.CUSTOMER_SIGN_CONTRACT_UPLOAD));
    }

    @Override
    public Result uploadingBackContractSign(ContractBaseSignDTO contractBaseSignDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        Integer status = 0;

        ContractSignEntity contractSignEntity = contractSignQueryLogicService.getContractSignDetailById(contractBaseSignDTO.getContractSignId());

        if (null == contractSignEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }

        if (contractSignEntity.getIsCustomerSignature() == IsCustomerSignatureEnum.AlREADY_SIGN.getValue()
                && contractSignEntity.getLdcFrame().equals(LdcFrameEnum.NOT_FRAME.getValue())) {
            status = ContractSignStatusEnum.WAIT_CONFIRM.getValue();
        } else {
            status = ContractSignStatusEnum.WAIT_BACK.getValue();
        }

        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus()) {
            throw new BusinessException("文件已签署,请勿重复操作");
        }
        return Result.success(contractSignService.postBackContractSign(contractBaseSignDTO, status, OperationActionEnum.SIGN_CONTRACT_UPLOAD));
    }

    @Override
    public Result generateSignPdfTest(ContractSignProvideDTO signProvideDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.generateSignPdf(signProvideDTO));
    }

    @Override
    public Result provideContractSign(ContractSignProvideDTO contractSignProvideDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.judge(contractSignService.provideContractSign(contractSignProvideDTO));
    }

    @Override
    public Result provideHuskyContractSign(TemplateEntity templateEntity) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.provideHuskyContractSign(templateEntity));
    }

    @Override
    public Result previewHuskyContractPdf(TemplateEntity templateEntity) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.previewHuskyContractPdf(templateEntity));
    }

    @Override
    public Result generateSignTemplate(Integer contractSignId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.generateSignTemplate(contractSignId));
    }

    @Override
    public Result buildHuskySignTemplateDTOV2(Integer contractSignId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.buildHuskySignTemplateV2(contractSignId));
    }

    @Override
    public Result buildHuskySignTemplateDTO(Integer contractSignId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.buildHuskySignTemplateDTO(contractSignId));
    }

    @Override
    public Result reviewContractSign(ContractSignReviewDTO contractSignReviewDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.judge(contractSignService.reviewContractSign(contractSignReviewDTO));
    }

    @Override
    public Result deleteContractSign(Integer ttId) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.judge(contractSignService.deleteContractSign(ttId));
    }

    @Override
    public Result contractSignStartSignature(ContractSignReviewDTO signReviewDTO) {
        IContractSignService contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
        return Result.success(contractSignService.contractSignStartSignature(signReviewDTO));
    }

    @Override
    public Result sendCustomerNoticeEmail(Integer signId) {

        IContractSignService contractSignService = null;
        try {
            contractSignService = salesContractSignHandler.getStrategy(ProcessorTypeEnum.SBM_S_ADD.getContractSignValue());
            return contractSignService.sendCustomerNoticeEmail(signId);
        } catch (Exception e) {
            DbzRedAlarmEntity redAlarmEntity = new DbzRedAlarmEntity();
            redAlarmEntity.setReferBizId(signId)
                    .setBizModule(BizLogModuleEnum.CUSTOMER_SIGN.name())
                    .setBombScene("sendCustomerNoticeEmail")
                    .setMemo("异常：" + e.getMessage());
            operationLogFacade.recordredalarm(redAlarmEntity);
            e.printStackTrace();
        }

        return Result.success();


    }

    @Override
    public void sendContractSignOriginalPaper() {
        contractSignQueryLogicService.sendContractSignOriginalPaper();
    }

    @Override
    public Boolean updateById(ContractSignEntity signEntity) {
        return contractSignQueryLogicService.updateById(signEntity);
    }

}
