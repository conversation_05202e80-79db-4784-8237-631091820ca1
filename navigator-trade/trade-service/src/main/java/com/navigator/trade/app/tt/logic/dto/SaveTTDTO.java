package com.navigator.trade.app.tt.logic.dto;

import com.navigator.bisiness.enums.ProcessorTypeEnum;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 22:21
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveTTDTO {
    /**
     * 新增实体(解约定赔,回购,关闭)
     */
    private SalesContractAddTTDTO salesContractAddTTDTO;
    /**
     * 修改实体
     */
    private SalesContractReviseTTDTO salesContractReviseTTDTO;
    /**
     * 拆分实体
     */
    private SalesContractSplitTTDTO salesContractSplitTTDTO;
    /**
     * 转月实体
     */
    private SalesContractTTTransferDTO salesContractTTTransferDTO;

    /**
     * 点价实体
     */
    private SalesContractTTPriceDTO salesContractTTPriceDTO;

    /**
     * 结构化定价实体
     */
    private SalesStructurePriceTTDTO salesStructurePriceTTDTO;

    /**
     * 价格实体(修改)
     */
    private PriceDetailBO priceDetailBO;
    /**
     * tt类型
     * {@link ProcessorTypeEnum}
     */
    private String processorType;

    /**
     * 业务关联id
     */
    private String groupId;

    /**
     * 是否修改主体
     */
    private Boolean reviseCustomerType;

    /**
     * 提交类型 1.保存 2.提交(默认)
     */
    private Integer submitType = SubmitTypeEnum.SUBMIT.getValue();

    /**
     * 是否修改主体(保存修改主体使用)
     */
    private Boolean changeCustomerFlag;

    /**
     * 修改主体的Id
     */
    private Integer originCustomerId;

    /**
     * 定价单信息
     */
    private String confirmPriceInfo;

}
