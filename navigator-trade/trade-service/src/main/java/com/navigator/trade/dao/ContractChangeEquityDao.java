package com.navigator.trade.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.trade.mapper.ContractChangeEquityMapper;
import com.navigator.trade.pojo.entity.ContractChangeEquityEntity;
import com.navigator.trade.pojo.enums.TTApproveStatusEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * dbt_contract_change_equity Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Dao
public class ContractChangeEquityDao extends BaseDaoImpl<ContractChangeEquityMapper, ContractChangeEquityEntity> {

    public List<ContractChangeEquityEntity> getChangeEquityDetailByApplyCode(String code) {
        return lambdaQuery().eq(ContractChangeEquityEntity::getApplyCode, code).list();
    }

    public List<ContractChangeEquityEntity> getChangeEquityByContractCode(String contractCode) {
        return lambdaQuery().eq(ContractChangeEquityEntity::getContractCode, contractCode).list();
    }

    public void updateApprovalStatusByCode(Integer approvalStatus, String applyCode) {
        lambdaUpdate().set(approvalStatus != null, ContractChangeEquityEntity::getApproveStatus, approvalStatus)
                .eq(ContractChangeEquityEntity::getApplyCode, applyCode).update();
    }

    /**
     * 查询合同权益未通过
     *
     * @param contractId
     * @return
     */
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByNotApprove(Integer contractId) {
        return lambdaQuery()
                .in(ContractChangeEquityEntity::getApproveStatus, Arrays.asList(TTApproveStatusEnum.WAIT_A_SIGN.getValue(), TTApproveStatusEnum.WAIT_B_SIGN.getValue(), TTApproveStatusEnum.WAIT_C_SIGN.getValue()))
                .eq(ContractChangeEquityEntity::getContractId, contractId).list();
    }

    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
    public List<ContractChangeEquityEntity> getChangeContractEquityDetailByContractIds(List<Integer> contractIds) {
        return lambdaQuery()
                .in(ContractChangeEquityEntity::getApproveStatus, Arrays.asList(TTApproveStatusEnum.WAIT_A_SIGN.getValue(), TTApproveStatusEnum.WAIT_B_SIGN.getValue(), TTApproveStatusEnum.WAIT_C_SIGN.getValue()))
                .in(ContractChangeEquityEntity::getContractId, contractIds).list();
    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
}
