package com.navigator.trade.service.contractsign.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.customer.pojo.enums.EarlyWarningEnum;
import com.navigator.customer.pojo.enums.FrameProtocolEnum;
import com.navigator.customer.pojo.enums.UseYqqEnum;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.ContractSignVODao;
import com.navigator.trade.dao.TradeTicketDao;
import com.navigator.trade.dao.TtAddDao;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.SignatureTypeEnum;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;
import com.navigator.trade.pojo.vo.ContractSignDetailFileVO;
import com.navigator.trade.pojo.vo.ContractSignFileVO;
import com.navigator.trade.pojo.vo.ContractSignVO;
import com.navigator.trade.service.IContractPaperService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-01-20 18:28
 */
@Slf4j
@Service
public class ContractSignQueryServiceImpl implements IContractSignQueryService {
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private CustomerProtocolFacade customerProtocolFacade;
    @Resource
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private ContractSignDao contractSignDao;
    @Resource
    private ContractSignVODao contractSignVODao;
    @Resource
    private TradeTicketDao tradeTicketDao;
    @Resource
    private TtAddDao ttAddDao;
    @Resource
    private FileBusinessFacade fileBusinessFacade;
    @Resource
    private ContractFacade contractFacade;
    @Resource
    private IContractPaperService contractPaperService;
    @Resource
    private CRoleFacade cRoleFacade;
    @Resource
    private MessageFacade messageFacade;

    @Override
    public Result queryContContractSigns(QueryDTO<ContractSignQO> queryDTO) {
        log.info("合同协议出具高级搜索开始=============================");
        ObjectMapper mapper = new ObjectMapper();
        ContractSignQO queryContractSignDTO = mapper.convertValue(queryDTO.getCondition(), ContractSignQO.class);

        if (null == queryContractSignDTO.getSalesType()) {
            queryContractSignDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
            queryDTO.setCondition(queryContractSignDTO);
        }

        //判断品种是否为空
        if (null == queryContractSignDTO.getGoodsCategoryId()) {
            queryContractSignDTO.setGoodsCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue());
            queryDTO.setCondition(queryContractSignDTO);
        }

        List<Integer> customerIds = new ArrayList<>();
        //根据客户员工id 查询出客户id
        //哥伦布段多主体customerId由前端传递
        if (null != queryContractSignDTO.getEmployId()) {
            //CEmployEntity cEmployEntity = cEmployFacade.getEmployById(queryContractSignDTO.getEmployId());
            customerIds.add(queryContractSignDTO.getColumbusCustomerId());
        }

        List<String> siteCodeList = new ArrayList<>();
//        Map<Integer, List<Integer>> companyCustomerIdMap = new HashMap<>();
        if (SystemEnum.MAGELLAN.getValue() == queryContractSignDTO.getSystem()) {
            //分页查询
            String userId = JwtUtils.getCurrentUserId();
            PermissionBO permissionBO = employFacade.querySitePermission(userId, queryContractSignDTO.getGoodsCategoryId());
//            companyCustomerIdMap = permissionBO.getCompanyCustomerIdMap();
            siteCodeList = permissionBO.getSiteCodeList();
        }
        if (queryContractSignDTO.getSystem().equals(SystemEnum.COLUMBUS.getValue())) {
            CustomerEntity customerEntity = customerFacade.getCustomerById(queryContractSignDTO.getColumbusCustomerId());
            if (DisableStatusEnum.DISABLE.getValue() == customerEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
            }
            CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
            if (DisableStatusEnum.DISABLE.getValue() == cEmployEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
            }
        }
        //查询合同列表postBackContractSign
        IPage<ContractSignVOEntity> iPage = contractSignVODao.queryContractSign(queryDTO, customerIds, siteCodeList);
        Map<String, Integer> wsMap = ImmutableMap.<String, Integer>builder()
                //不使用易企签，使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.NOT_USE_YQQ.getValue())
                //使用易企签，使用哥伦布
                .put(UseYqqEnum.USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.USE_YQQ.getValue())
                //不使用易企签，不使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.DISABLE.getValue().toString(), UseYqqEnum.NOT_SYSTEM.getValue())
                .build();
        List<ContractSignVO> contractSignVOS = iPage.getRecords().stream().map(contractSignEntity -> {
                            ContractSignVO contractSignVO = new ContractSignVO();
                            BeanUtil.copyProperties(contractSignEntity, contractSignVO);

                            Integer customerId = ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerId() : Integer.valueOf(contractSignEntity.getSupplierId());
                            CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
                            customerProtocolDTO
                                    .setCustomerId(customerId)
                                    .setCompanyId(contractSignEntity.getCompanyId())
                                    .setCategoryId(contractSignEntity.getGoodsCategoryId())
                                    .setSaleType(contractSignEntity.getSalesType())
                                    .setCategory2(String.valueOf(contractSignEntity.getCategory2()))
                                    .setCategory3(String.valueOf(contractSignEntity.getCategory3()))
                            ;
                            CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
                            if (null != customerProtocolEntity) {
                                String warningName = "";
                                if (FrameProtocolEnum.CONTRACT.getValue().equals(customerProtocolEntity.getFrameProtocol())) {
                                    if (null != customerProtocolEntity.getProtocolStartDate()
                                            && null != customerProtocolEntity.getProtocolEndDate()
                                            && !DateTimeUtil.isBetween(new Date(), customerProtocolEntity.getProtocolStartDate(), customerProtocolEntity.getProtocolEndDate())) {
                                        warningName = EarlyWarningEnum.FRAME_EXPIRED.getDescription();
                                    }
                                }
                                contractSignVO.setWarningName(warningName);
                            }
                            ContractEntity contractEntity = contractFacade.getBasicContractById(contractSignEntity.getContractId());
                            if (null != contractEntity) {
                                contractSignVO.setContractStatus(contractEntity.getStatus());
                            }

                            // 获取合同模板url地址
                            Map<Integer, List<FileInfoEntity>> fileMap = fileBusinessFacade.getFileMapByBizIdAndType(contractSignEntity.getId(), FileCategoryType.getAllContractPdfType(), DisableStatusEnum.ENABLE.getValue());
                            if (!CollectionUtils.isEmpty(fileMap)) {
                                List<FileInfoEntity> contractPdfOriginalUrls = fileMap.get(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode());
                                if (!CollectionUtils.isEmpty(contractPdfOriginalUrls)) {
                                    contractSignVO.setContractPdfOriginalUrl(contractPdfOriginalUrls.get(0).getFileUrl())
                                            .setOriginalContractPdfDownUrl(contractPdfOriginalUrls.get(0).getPath())
                                            .setContractPdfDownUrl(contractPdfOriginalUrls.get(0).getPath());
                                }
                                // 获取LDC合同url地址
                                List<FileInfoEntity> ldcContractUrls = fileMap.get(FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode());

                                if (!CollectionUtils.isEmpty(ldcContractUrls)) {
                                    contractSignVO.setLdcContractUrl(ldcContractUrls.get(0).getFileUrl())
                                            .setContractPdfDownUrl(ldcContractUrls.get(0).getPath())
                                            .setLdcContractDownUrl(ldcContractUrls.get(0).getPath());
                                }
                                contractSignVO.setLdcContractUrls(ldcContractUrls);
                                // 获取客户合同url列表
                                List<FileInfoEntity> customerContractUrls = fileMap.get(FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
                                if (!CollectionUtils.isEmpty(customerContractUrls)) {
                                    customerContractUrls.sort(Comparator.comparing(FileInfoEntity::getFileBusinessId));
                                }
                                contractSignVO.setCustomerContractUrl(customerContractUrls);
                                if (!CollectionUtils.isEmpty(customerContractUrls)) {
                                    contractSignVO.setContractPdfDownUrl(customerContractUrls.get(0).getPath());
                                }
                            }
                            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityById(contractSignEntity.getTtId());
                            contractSignVO.setApprovalStatus(tradeTicketEntity != null ? tradeTicketEntity.getApprovalStatus() : null);
                            contractSignVO.setOriginalLdcFrame(contractSignEntity.getCustomerNonFrame());
                            if (null != tradeTicketEntity) {
                                contractSignVO.setTtStatus(tradeTicketEntity.getStatus());
                            }
                            //WS属性
                            contractSignVO.setUseWs(wsMap.get(contractSignEntity.getUseYqq().toString() + contractSignEntity.getIsColumbus().toString()));
                            return contractSignVO;
                        }
                )
//                .sorted(Comparator.comparing(ContractSignVO::getCreatedAt).thenComparing(ContractSignVO::getUpdatedAt).reversed())
                .collect(Collectors.toList());

        return Result.page(iPage, contractSignVOS);
    }

    @Override
    public ContractSignEntity getContractSignDetailById(Integer contractSignId) {
        return contractSignDao.getById(contractSignId);
    }

    @Override
    public ContractSignEntity getContractSignDetailByTtId(Integer ttId) {
        return contractSignDao.getSignDetailByTtId(ttId);
    }

    @Override
    public ContractSignAllStatusNumVO getCustomerSignStat(Integer salesType) {
        Integer customerId = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId();
        ContractSignAllStatusNumVO signAllStatusNumVO = new ContractSignAllStatusNumVO();
        // 待回传
        CompletableFuture<Void> waitBackCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitBackStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_BACK.getValue(),
                    null, customerId, salesType, null, null);
            signAllStatusNumVO.setWaitBack(waitBackStat);
        });
        // 待确认合规
        CompletableFuture<Void> waitConfirmCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitConfirmStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_CONFIRM.getValue(),
                    null, customerId, salesType, null, null);
            signAllStatusNumVO.setWaitConfirm(waitConfirmStat);
        });
        // 执行中
        CompletableFuture<Void> processingCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer processingStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.PROCESSING.getValue(),
                    null, customerId, salesType, null, null);
            signAllStatusNumVO.setProcessing(processingStat);
        });
        // 已作废
        CompletableFuture<Void> invalidCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer invalidStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.INVALID.getValue(),
                    null, customerId, salesType, null, null);
            signAllStatusNumVO.setInvalid(invalidStat);
        });

        CompletableFuture.allOf(waitBackCompletableFuture, waitConfirmCompletableFuture, processingCompletableFuture, invalidCompletableFuture).join();
        return signAllStatusNumVO;
    }

    @Override
    @Deprecated
    public ContractSignAllStatusNumVO getMagellanSignStat(Integer ldcFrame, Integer salesType, Integer goodsCategoryId) {
        //判断是否有品类id
        if (null == goodsCategoryId) {
            goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        }

//        List<Integer> belongCustomerIdList = Collections.singletonList(-1);
        String userId = JwtUtils.getCurrentUserId();
        PermissionBO permissionBO = employFacade.querySitePermission(userId, goodsCategoryId);
//        if (!CollectionUtils.isEmpty(permissionBO.getCustomerIdList())) {
//            belongCustomerIdList = permissionBO.getCustomerIdList();
//        }


        ContractSignAllStatusNumVO signAllStatusNumVO = new ContractSignAllStatusNumVO();
        // 待出具
        Integer finalGoodsCategoryId = goodsCategoryId;
//        List<Integer> finalBelongCustomerIdList = belongCustomerIdList;
        CompletableFuture<Void> waitProvideCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitProvideStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_PROVIDE.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setWaitProvide(waitProvideStat);
        });
        // 待审核
        CompletableFuture<Void> waitReviewCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitReviewStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_REVIEW.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setWaitReview(waitReviewStat);
        });
        // 待签章
        CompletableFuture<Void> waitStampCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitStampStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_STAMP.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setWaitStamp(waitStampStat);
        });
        // 待回传
        CompletableFuture<Void> waitBackCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitBackStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_BACK.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setWaitBack(waitBackStat);
        });
        // 待确认合规
        CompletableFuture<Void> waitConfirmCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer waitConfirmStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.WAIT_CONFIRM.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setWaitConfirm(waitConfirmStat);
        });
        // 正本
        CompletableFuture<Void> originalPaperCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer originalPaperStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.PAPER.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setOriginalPaper(originalPaperStat);
        });
        // 执行中
        CompletableFuture<Void> processingCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer processingStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.PROCESSING.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setProcessing(processingStat);
        });
        // 异常
        CompletableFuture<Void> abnormalCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer abnormalStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.ABNORMAL.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setAbnormal(abnormalStat);
        });
        // 已作废
        CompletableFuture<Void> invalidCompletableFuture = CompletableFuture.runAsync(() -> {
            Integer invalidStat = contractSignDao.getContractSignStat(ContractSignStatusEnum.INVALID.getValue(),
                    ldcFrame, null, salesType, finalGoodsCategoryId, permissionBO.getSiteCodeList());
            signAllStatusNumVO.setInvalid(invalidStat);
        });

        CompletableFuture.allOf(waitProvideCompletableFuture, waitReviewCompletableFuture, waitStampCompletableFuture, waitBackCompletableFuture,
                        waitConfirmCompletableFuture, originalPaperCompletableFuture, processingCompletableFuture, abnormalCompletableFuture, invalidCompletableFuture)
                .join();
        return signAllStatusNumVO;
    }

    @Override
    public ContractSignEntity queryByUUId(String uuid) {
        return contractSignDao.queryByUUId(uuid);
    }

    @Override
    public List<ContractSignFileVO> getSignFileListByContractId(Integer contractId, Integer system) {
        //查询协议信息
        List<ContractSignEntity> contractSignEntities = contractSignDao.queryByContractId(contractId);
        if (CollectionUtils.isEmpty(contractSignEntities)) {
            return Collections.emptyList();
        }

        List<ContractSignFileVO> contractSignFileVOS = new ArrayList<>();

        for (ContractSignEntity contractSignEntity : contractSignEntities) {

            List<Integer> fileCategoryTypeList;
            if (null == system && system.equals(SystemEnum.COLUMBUS.getValue())) {
                fileCategoryTypeList = FileCategoryType.getCustomerContractPdfType();
            } else {
                fileCategoryTypeList = FileCategoryType.getAllContractPdfType();
            }

            List<FileInfoEntity> fileInfoEntities = fileBusinessFacade.getFileInfoByBizIdAndTypeList(contractSignEntity.getId(), fileCategoryTypeList, DisableStatusEnum.ENABLE.getValue(), system);
            fileInfoEntities.sort(Comparator.comparing(FileInfoEntity::getFileCategoryType).reversed().thenComparing(FileInfoEntity::getId));
            if (!CollectionUtils.isEmpty(fileInfoEntities)) {
                for (FileInfoEntity fileInfoEntity : fileInfoEntities) {
                    contractSignFileVOS.add(contractSignFileVOList(contractSignEntity, fileInfoEntity, system));
                }
            } else {
                contractSignFileVOS.add(contractSignFileVOList(contractSignEntity, null, system));
            }

        }


        List<FileInfoEntity> fileInfoEntities = fileBusinessFacade.getFileInfoByBizIdAndTypeList(contractId, FileCategoryType.getContractSelfFileType(), DisableStatusEnum.ENABLE.getValue(), system);
        fileInfoEntities.stream().forEach(fileInfoEntity -> {
            ContractSignFileVO contractSignFileVO = new ContractSignFileVO().setId(fileInfoEntity.getId())
                    .setProtocolCode("人工上传")
                    .setCreatedAt(fileInfoEntity.getCreatedAt())
                    .setSignatureFinish(true)
                    .setTtTypeInfo(FileCategoryType.valueOf(fileInfoEntity.getFileCategoryType()).getSource())
                    .setFileCategoryType(fileInfoEntity.getFileCategoryType())
                    .setSignaturePdfUrl(fileInfoEntity.getFileUrl())
                    .setFileName(fileInfoEntity.getOriginalFileName());
            contractSignFileVOS.add(contractSignFileVO);
        });
        List<ContractSignFileVO> list = contractSignFileVOS.stream().filter(i -> i.getColNotShow() != 1).collect(Collectors.toList());

      /*  List<ContractSignFileVO> contractSelfFileVOList = fileInfoEntities.stream().map(fileInfoEntity -> {
                    return new ContractSignFileVO().setId(fileInfoEntity.getId())
                            .setProtocolCode("人工上传")
                            .setCreatedAt(fileInfoEntity.getCreatedAt())
                            .setSignatureFinish(true)
                            .setTtTypeInfo(FileCategoryType.valueOf(fileInfoEntity.getFileCategoryType()).getSource())
                            .setFileCategoryType(fileInfoEntity.getFileCategoryType())
                            .setSignaturePdfUrl(fileInfoEntity.getFileUrl())
                            .setFileName(fileInfoEntity.getOriginalFileName());
                }

        ).collect(ollectors.toList());*/
        //contractSignFileVOList.addAll(contractSelfFileVOList);
        //return contractSignFileVOList;
        return list;
    }

    public ContractSignFileVO contractSignFileVOList(ContractSignEntity contractSignEntity, FileInfoEntity fileInfoEntity, Integer system) {
        ContractSignFileVO contractSignFileVO = new ContractSignFileVO().setContractSignId(contractSignEntity.getId())
                .setProtocolCode(contractSignEntity.getProtocolCode())
                .setTtType(contractSignEntity.getTtType())
                .setTtTypeInfo(TTTypeEnum.getByType(contractSignEntity.getTtType()).getDesc())
                .setSignatureFinish(contractSignEntity.getStatus() > ContractSignStatusEnum.WAIT_BACK.getValue())
                .setSignaturePdfUrl(null == fileInfoEntity ? null : fileInfoEntity.getFileUrl())
                .setFileCategoryType(null == fileInfoEntity ? null : fileInfoEntity.getFileCategoryType())
                .setId(null == fileInfoEntity ? null : fileInfoEntity.getId())
                .setFileName(null == fileInfoEntity ? null : fileInfoEntity.getOriginalFileName())
                .setCreatedAt(null == fileInfoEntity ? null : fileInfoEntity.getCreatedAt())
                .setPath(null == fileInfoEntity ? null : fileInfoEntity.getPath());


        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus()
                && contractSignEntity.getSignatureType() == SignatureTypeEnum.BOTH_SIGNATURE.getValue()
                && (contractSignFileVO.getFileCategoryType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileCategoryType())
                && system == SystemEnum.COLUMBUS.getValue()
                && fileInfoEntity != null
                && BlobFileContextEnum.PDF.getTypeInfo().equalsIgnoreCase(fileInfoEntity.getExtension())
                && fileInfoEntity.getCreatedAt().after(DateTimeUtil.formatDateTimeDate00("2023-09-21 00:00:00"))
        ) {
            String fileUrl = contractSignFileVO.getSignaturePdfUrl();
            if (StringUtils.isNotBlank(fileUrl)) {
                int i = fileUrl.lastIndexOf("/");
                String preUrl = fileUrl.substring(0, i + 1);
                String fileName = fileUrl.substring(i + 1);
                String whiteFileName = "white_" + fileName;
                String newFileUrl = preUrl + whiteFileName;
                contractSignFileVO.setSignaturePdfUrl(newFileUrl);
            }
            String path = fileInfoEntity.getPath();
            if (StringUtils.isNotBlank(path)) {
                int i1 = path.lastIndexOf("/");
                String preUrl1 = path.substring(0, i1 + 1);
                String fileName1 = path.substring(i1 + 1);
                String whiteFileName1 = "white_" + fileName1;
                String newPath = preUrl1 + whiteFileName1;
                contractSignFileVO.setPath(newPath);
            }

        }

        if (contractSignEntity.getStatus() >= ContractSignStatusEnum.WAIT_CONFIRM.getValue()
                && (contractSignFileVO.getFileCategoryType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileCategoryType())
                && system == SystemEnum.COLUMBUS.getValue()
        ) {
            contractSignFileVO.setColNotShow(1);
        }

        if ((contractSignEntity.getStatus() <= ContractSignStatusEnum.WAIT_STAMP.getValue()
                && (contractSignFileVO.getFileCategoryType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileCategoryType())
                && system == SystemEnum.COLUMBUS.getValue()
                && SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType())
                || (contractSignEntity.getStatus() <= ContractSignStatusEnum.WAIT_BACK.getValue()
                && (contractSignFileVO.getFileCategoryType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileCategoryType())
                && system == SystemEnum.COLUMBUS.getValue()
                && SignatureTypeEnum.BOTH_SIGNATURE.getValue() != contractSignEntity.getSignatureType())
        ) {
            contractSignFileVO
                    .setSignaturePdfUrl(null)
                    .setFileCategoryType(null)
                    .setId(null)
                    .setFileName(null)
                    .setCreatedAt(null)
            ;
        }
        return contractSignFileVO;
    }

    @Override
    public List<ContractSignDetailFileVO> getAllSignFileListById(Integer contractSignId, Integer system) {
        //查询协议基本信息
        ContractSignEntity signEntity = contractSignDao.getById(contractSignId);
        if (null == signEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }
        //协议的附件信息
        List<FileInfoEntity> fileInfoEntities = fileBusinessFacade.getFileInfoByBizIdAndTypeList(signEntity.getId(), FileCategoryType.getAllContractPdfType(), null, system);
        if (CollectionUtils.isEmpty(fileInfoEntities)) {
            return Collections.emptyList();
        }
        List<Integer> filterFileTypeList = new ArrayList<>();
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == signEntity.getStatus()) {
            filterFileTypeList = Arrays.asList(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode());
        } else if (ContractSignStatusEnum.WAIT_CONFIRM.getValue() <= signEntity.getStatus()) {
            filterFileTypeList = Arrays.asList(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode());
        }
        List<Integer> finalFilterFileTypeList = filterFileTypeList;
        List<ContractSignDetailFileVO> signDetailFileVOList = fileInfoEntities.stream().map(fileInfoEntity -> {
            ContractSignDetailFileVO signDetailFileVO = BeanConvertUtils.convert(ContractSignDetailFileVO.class, signEntity);
            signDetailFileVO.setContractSignId(signEntity.getId())
                    .setContractId(signEntity.getContractId().toString())
                    .setInvalidReason(fileInfoEntity.getMemo())
                    .setFileStatus(fileInfoEntity.getBizFileStatus())
                    .setFileUrl(fileInfoEntity.getFileUrl())
                    .setFileType(fileInfoEntity.getFileCategoryType())
                    .setSignatureStatus(fileInfoEntity.getFileCategoryTypeInfo())
                    .setFileExtension(fileInfoEntity.getExtension())
                    .setCreatedAt(fileInfoEntity.getCreatedAt());

            if (ContractSignStatusEnum.WAIT_BACK.getValue() == signEntity.getStatus()
                    && signEntity.getSignatureType() == SignatureTypeEnum.BOTH_SIGNATURE.getValue()
                    && system == SystemEnum.COLUMBUS.getValue()
                    && signDetailFileVO.getFileType() == FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode()
                    && BlobFileContextEnum.PDF.getTypeInfo().equalsIgnoreCase(fileInfoEntity.getExtension())
                    && fileInfoEntity.getCreatedAt().after(DateTimeUtil.formatDateTimeDate00("2023-09-21 00:00:00"))
            ) {
                String fileUrl = fileInfoEntity.getFileUrl();
                int i = fileUrl.lastIndexOf("/");
                String preUrl = fileUrl.substring(0, i + 1);
                String fileName = fileUrl.substring(i + 1);
                String whiteFileName = "white_" + fileName;
                String newFileUrl = preUrl + whiteFileName;
                signDetailFileVO.setFileUrl(newFileUrl);
                signDetailFileVO.setFileType(FileCategoryType.WHITE_PDF.getCode());
            }
            return signDetailFileVO;
        }).collect(Collectors.toList());
        if (signEntity.getStatus() >= ContractSignStatusEnum.WAIT_BACK.getValue() && system.equals(SystemEnum.COLUMBUS.getValue())) {
            signDetailFileVOList = signDetailFileVOList.stream().filter(signFile -> {
                return !(finalFilterFileTypeList.contains(signFile.getFileType()) && DisableStatusEnum.ENABLE.getValue().equals(signFile.getFileStatus()));
            }).collect(Collectors.toList());
        }

        List<ContractSignDetailFileVO> list = signDetailFileVOList.stream().map(contractSignFileVO -> {
                    if ((signEntity.getStatus() <= ContractSignStatusEnum.WAIT_STAMP.getValue()
                            && (contractSignFileVO.getFileType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileType())
                            && system == SystemEnum.COLUMBUS.getValue()
                            && SignatureTypeEnum.BOTH_SIGNATURE.getValue() == signEntity.getSignatureType())
                            || (signEntity.getStatus() <= ContractSignStatusEnum.WAIT_BACK.getValue()
                            && (contractSignFileVO.getFileType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileType())
                            && system == SystemEnum.COLUMBUS.getValue()
                            && SignatureTypeEnum.BOTH_SIGNATURE.getValue() != signEntity.getSignatureType())
                            ||
                            (
                                    signEntity.getConfirmStatus() == 1
                                            && (contractSignFileVO.getFileType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileType())
                                            && system == SystemEnum.COLUMBUS.getValue()
                            )
                    ) {
                        contractSignFileVO
                                .setFileStatus(null)
                                .setFileUrl(null)
                                .setFileType(null)
                                .setSignatureStatus(null)
                                .setFileExtension(null)
                        ;
                        contractSignFileVO.setColNotShow(1);
                    }

//                    if ((signEntity.getStatus() <= ContractSignStatusEnum.WAIT_STAMP.getValue()
//                            && (contractSignFileVO.getFileType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileType())
//                            && system == SystemEnum.COLUMBUS.getValue()
//                            && SignatureTypeEnum.BOTH_SIGNATURE.getValue() == signEntity.getSignatureType())
//                            ||
//                            (
//                                    signEntity.getConfirmStatus() == 1
//                                            && (contractSignFileVO.getFileType() != null && FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode() == contractSignFileVO.getFileType())
//                                            && system == SystemEnum.COLUMBUS.getValue()
//                            )
//                    ) {
//                        contractSignFileVO.setColNotShow(1);
//                    }
                    return contractSignFileVO;
                }
        ).collect(Collectors.toList());

        List<ContractSignDetailFileVO> list1 = list.stream().filter(i -> i.getColNotShow() != 1).collect(Collectors.toList());
        return list1;
    }

    @Override
    public ContractSignAllStatusNumVO getContractSignStat(QueryContractSignBO signBO) {
        // 获取客户id
        Integer customerId = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId();
        if (ContractSalesTypeEnum.SALES.getValue() == signBO.getSalesType()) {
            signBO.setCustomerId(customerId);
        } else {
            signBO.setSupplierId(customerId);
        }

        return contractSignDao.getColumbusContractSignStat(signBO);
    }

    @Override
    public List<ContractSignEntity> queryIncompleteByContractId(List<Integer> ids, List<Integer> ttTypeList) {
        return contractSignDao.queryIncompleteByContractId(ids, ttTypeList);
    }

    @Override
    public List<ContractSignEntity> querySignListByContractId(Integer contractId, List<Integer> ttStatusList) {
        return contractSignDao.querySignListByContractId(contractId, ttStatusList);
    }

    @Override
    public List<ContractSignEntity> querySonContractSplitIncomplete(List<Integer> ids) {
        return contractSignDao.querySonContractSplitIncomplete(ids);
    }

    @Override
    public void sendContractSignOriginalPaper() {
        log.info("sendContractSignOriginalPaper:正本定时任务开始:{}", new Date());

        //查询前一天的客户未填写的正本信息
        List<Integer> contractSingId = contractPaperService.sendContractSignOriginalPaper();

        if (contractSingId.isEmpty()) {
            return;
        }
        //分组查询客户的正本
        List<ContractSignEntity> contractSignGroups = contractSignDao.sendContractSignOriginalPaper(contractSingId);

        for (ContractSignEntity contractSignGroup : contractSignGroups) {

            //区分销售和采购
            if (ContractSalesTypeEnum.SALES.getValue() == contractSignGroup.getSalesType()) {
                List<ContractSignEntity> contractSignEntities = contractSignDao.queryContractSignByIdSCustomerIdSalesType(contractSingId,
                        contractSignGroup.getCustomerId(), null, contractSignGroup.getGoodsCategoryId());

                //CRoleDefEntity cRoleDefEntity = cRoleFacade.getRoleDefById(210);
                this.sendContractSignPaper(contractSignEntities, contractSignGroup, contractSignGroup.getCustomerId());
            } else {
                List<ContractSignEntity> contractSignEntities = contractSignDao.queryContractSignByIdSCustomerIdSalesType(contractSingId,
                        null, contractSignGroup.getSupplierId(), contractSignGroup.getGoodsCategoryId());
                this.sendContractSignPaper(contractSignEntities, contractSignGroup, Integer.parseInt(contractSignGroup.getSupplierId()));
            }
        }

        log.info("sendContractSignOriginalPaper:正本定时任务结束:{}", new Date());
    }

    @Override
    public Boolean updateById(ContractSignEntity signEntity) {
        return contractSignDao.updateById(signEntity);
    }

    private void sendContractSignPaper(List<ContractSignEntity> contractSignEntities, ContractSignEntity contractSignGroup, Integer customerId) {

        //塞入发送消息参数
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_ORIGINAL_PAPER.name());
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_ORIGINAL_PAPER.getDesc());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        messageInfoDTO.setCustomerId(customerId);
        messageInfoDTO.setCategoryId(contractSignGroup.getGoodsCategoryId());
        List<Map<String, Object>> dataMaps = new ArrayList<>();
        messageInfoDTO.setReferBizId(String.valueOf(contractSignEntities.stream().map(ContractSignEntity::getId).distinct().collect(Collectors.toList())));
        List<CRoleEntity> cRoleEntities = cRoleFacade.queryRoleListByDefInfosSalesType(Arrays.asList(210), contractSignGroup.getGoodsCategoryId(), contractSignGroup.getSalesType());

        if (cRoleEntities.isEmpty()) {
            return;
        }
        CRoleEntity roleEntity = cRoleEntities.get(0);
        List<String> receiver = new ArrayList<>();
        receiver.add(String.valueOf(roleEntity.getId()));
        receiver.add(String.valueOf(202));
        messageInfoDTO.setReceivers(receiver);


        for (ContractSignEntity contractSignEntity : contractSignEntities) {


            ContractPaperEntity contractPaperEntity = contractPaperService.getContractPaper(contractSignEntity.getId());

            Map<String, Object> map = new HashMap<>();
            map.put("contractCode", contractSignEntity.getContractCode());
            map.put("TTCode", contractSignEntity.getTtCode());
            map.put("mailGoTime", DateTimeUtil.formatDateStringCN(contractPaperEntity.getMailGoTime()));
            map.put("ldcDeliverySn", contractPaperEntity.getLdcDeliverySn());

            dataMaps.add(map);
        }
        messageInfoDTO.setDataMaps(dataMaps);

        messageFacade.sendMessage(messageInfoDTO);

        log.info("sendContractSignPaper:消息已发送:{}", new Date());
    }

}
