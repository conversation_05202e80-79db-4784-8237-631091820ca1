package com.navigator.trade.service.sync;

import com.google.gson.Gson;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncObjectTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncService {
    @Value("${sync.atlas.isOpen:0}")
    private Integer openAtlasSync;  // 是否开启atlas同步
    @Value("${sync.atlas.useMessageQueue:0}")
    private Integer openAtlasMessageQueue; // 是否使用消息队列同步atlas
    @Value("${messageQueue.cuckoo.syncQueueName}")
    private String syncQueueName;  // 消息队列名称

    private final ITradeTicketQueryService ticketQueryService;
    private final ITtPriceService ttPriceService;
    private final AtlasContractFacade atlasContractFacade;
    private final JmsTemplate jmsTemplate;
    @Resource
    private ContractQueryLogicService contractQueryLogicService;
    @Resource
    private AtlasSyncService atlasSyncService;

    /**
     * Add by Jason for migrate contracts info to atlas
     */
    public List<String> contractMigration(String status, List<String> contractNumbers) {
        List returnList = new ArrayList();
        List<ContractEntity> list = contractQueryLogicService.getContractByStatus(status, contractNumbers);
//         List<ContractEntity> list = new ArrayList<>();
//         ContractEntity subContractEntity = contractQueryLogicService.getBasicContractByCode("TJIBSBMS2400203-006-1");
//         list.add(subContractEntity);
        if (list != null && list.size() > 0) {
            for (ContractEntity contractEntity : list) {
                syncContractsInfo(contractEntity);
                returnList.add("This Contract has been sent to migrate " + contractEntity.getContractCode());
            }
        } else {
            returnList.add("No Contract to migrate");
        }

        return returnList;
    }

    /**
     * Add by Jason for migrate price info to atlas
     */
    public List<String> priceMigration(String status, List<String> contractList) {
        List returnList = new ArrayList();
        List<ContractEntity> list = contractQueryLogicService.getContractByStatus(status, contractList);
//         List<ContractEntity> list = new ArrayList<>();
//         ContractEntity subContractEntity = contractQueryLogicService.getBasicContractByCode("TJIBSBMS2400203-006-1");
//         list.add(subContractEntity);
        if (list != null && list.size() > 0) {
            for (ContractEntity contractEntity : list) {
                atlasSyncService.syncTTPriceInfoWithoutTTID(contractEntity, "Jason Migration Price");
                returnList.add("This Contract pricing has been sent to migrate " + contractEntity.getContractCode());
            }
        } else {
            returnList.add("No Contract price to migrate");
        }
        return returnList;
    }

    /**
     * Add by Jason for migrate contracts info to atlas
     */
    public void syncContractsInfo(ContractEntity contractEntity) {
        Integer ttId = 0;
        String operatorName = "jason migration";

        log.info("{} is working", Thread.currentThread().getName());
        // 默认一个值
        int tradeType = ContractTradeTypeEnum.NEW.getValue();

        // 同步 Atlas
        AtlasSyncRequestDTO atlasSyncRequestDTO = new AtlasSyncRequestDTO();
        atlasSyncRequestDTO.setTtId(ttId)
                .setBizId(contractEntity.getId())
                .setBizCode(contractEntity.getContractCode())
                .setSalesType(contractEntity.getSalesType())
                .setObjectType(AtlasSyncObjectTypeEnum.CONTRACT.getValue())
                .setOperationType(AtlasSyncActionEnum.getOperationByTradeType(tradeType).getValue())
                .setTradeType(tradeType)
                .setCreatedBy(operatorName)
                .setUpdatedBy(operatorName);

        // 合同同步至Atlas
        syncAtlas(atlasSyncRequestDTO);
    }

    /**
     * Add by Jason for migrate price info to atlas
     */
    @Async
    public void syncTTPriceInfoWithoutTTID(ContractEntity contractEntity, String operatorName) {
        Integer ttId = 0;
        log.info("{} is working", Thread.currentThread().getName());


        // 交易类型
        // TradeTicketEntity tradeTicket = ticketQueryService.getByTtId(ttId);
        Integer tradeType = ContractTradeTypeEnum.PRICE.getValue();
        // 暂定价定价走修改同步接口
        if (tradeType.equals(ContractTradeTypeEnum.FIXED.getValue())) {
            log.info("===============[{}] Atlas Not Sync Fixed Price : {}===============", contractEntity.getContractCode(), tradeType);
            return;
        }

        AtlasSyncRequestDTO atlasSyncRequestDTO = new AtlasSyncRequestDTO();
        atlasSyncRequestDTO.setTtId(ttId)
                .setBizId(contractEntity.getId())
                .setBizCode(contractEntity.getContractCode())
                .setSalesType(contractEntity.getSalesType())
                .setObjectType(AtlasSyncObjectTypeEnum.PRICE.getValue())
                .setOperationType(AtlasSyncActionEnum.getOperationByTradeType(tradeType).getValue())
                .setTradeType(tradeType)
                .setCreatedBy(operatorName)
                .setUpdatedBy(operatorName);
        // 合同同步至Atlas
        syncAtlas(atlasSyncRequestDTO);


    }

    @Async
    public void syncContractInfo(ContractEntity contractEntity, Integer ttId, Integer syncType, String operatorName) {
        log.info("{} is working", Thread.currentThread().getName());

        // 获取并处理交易类型（包括初始值和业务调整）
        int tradeType = getTradeType(contractEntity, ttId, syncType);

        // 转厂原合同的处理
        if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1) {
            syncAtlas(createAtlasContractSyncRequest(contractEntity, ttId, ContractTradeTypeEnum.NEW.getValue(), operatorName));
        }

        // 同步 Atlas
        syncAtlas(createAtlasContractSyncRequest(contractEntity, ttId, tradeType, operatorName));
    }

    /**
     * 获取并处理交易类型
     * - 如果 ttId 为 null 或未找到交易票据，返回默认交易类型
     * - 根据不同场景调整交易类型（例如转厂合同和注销场景）
     */
    private int getTradeType(ContractEntity contractEntity, Integer ttId, Integer syncType) {

        // 定价完成直接返回
        if (syncType == LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType()) {
            return ContractTradeTypeEnum.PRICE_RESULT.getValue();
        }

        // 如果 ttId 为 null，直接处理逻辑
        if (ttId == null) {
            // 合同关闭：如果syncType为CLOSE，则返回 CLOSED 交易类型
            if (syncType == LkgInterfaceActionEnum.CLOSE.getSyncType()) {
                return ContractTradeTypeEnum.CLOSED.getValue();
            }

            // 注销撤回：如果syncType为WARRANT_WITHDRAW，则返回 WARRANT_WITHDRAW 交易类型
            if (syncType == LkgInterfaceActionEnum.WARRANT_WITHDRAW.getSyncType()) {
                return ContractTradeTypeEnum.WARRANT_WITHDRAW.getValue();
            }

            // 更新合同：如果syncType为UPDATE，则返回 REVISE_NORMAL 交易类型
            if (syncType == LkgInterfaceActionEnum.UPDATE.getSyncType()) {
                return ContractTradeTypeEnum.REVISE_NORMAL.getValue();
            }

            // 否则返回默认交易类型 NEW
            return ContractTradeTypeEnum.NEW.getValue();
        }

        // 如果 ttId 不为 null，查询 TradeTicketEntity
        TradeTicketEntity tradeTicket = ticketQueryService.getByTtId(ttId);
        int tradeType = tradeTicket.getTradeType();

        // 如果是拆分场景且原始交易类型为 NEW，检查是否有父tt，若有则更新 tradeType
        if (tradeType == ContractTradeTypeEnum.NEW.getValue()) {
            TradeTicketEntity parentTradeTicket = ticketQueryService.getByGroupId(tradeTicket.getGroupId(), tradeTicket.getId());
            if (parentTradeTicket != null) {
                tradeType = parentTradeTicket.getTradeType();
            }
        }

        // 如果是注销场景，根据不同条件调整交易类型
        if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
            // 需要排除的tradeType
            List<Integer> excludeTradeType = Arrays.asList(
                    ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                    ContractTradeTypeEnum.TRANSFER_ALL.getValue(),
                    ContractTradeTypeEnum.BUYBACK.getValue(),
                    ContractTradeTypeEnum.WARRANT_WITHDRAW.getValue(),
                    ContractTradeTypeEnum.CLOSED.getValue());

            if (!excludeTradeType.contains(tradeType)) {
                // 根据同步类型决定是新增还是修改
                if (syncType == LkgInterfaceActionEnum.ADD.getSyncType()) {
                    tradeType = ContractTradeTypeEnum.NEW.getValue();
                } else {
                    tradeType = ContractTradeTypeEnum.REVISE_NORMAL.getValue();
                }
            }
        }
        return tradeType;
    }

    @Async
    public void syncTTPriceInfo(Integer ttId, String operatorName) {
        log.info("{} is working", Thread.currentThread().getName());
        TTPriceEntity ttPriceEntity = ttPriceService.getTTPriceEntityByTTId(ttId);
        if (null != ttPriceEntity) {

            // 交易类型
            TradeTicketEntity tradeTicket = ticketQueryService.getByTtId(ttId);
            if (null != tradeTicket) {
                Integer tradeType = tradeTicket.getTradeType();

                // 暂定价定价走修改同步接口
                if (tradeType.equals(ContractTradeTypeEnum.FIXED.getValue())) {
                    log.info("===============[{}] Atlas Not Sync Fixed Price : {}===============", tradeTicket.getContractCode(), tradeType);
                    return;
                }

                AtlasSyncRequestDTO atlasSyncRequestDTO = new AtlasSyncRequestDTO();
                atlasSyncRequestDTO.setTtId(ttId)
                        .setBizId(tradeTicket.getContractId())
                        .setBizCode(tradeTicket.getContractCode())
                        .setSalesType(tradeTicket.getSalesType())
                        .setObjectType(AtlasSyncObjectTypeEnum.PRICE.getValue())
                        .setOperationType(AtlasSyncActionEnum.getOperationByTradeType(tradeType).getValue())
                        .setTradeType(tradeType)
                        .setCreatedBy(operatorName)
                        .setUpdatedBy(operatorName);
                // 合同同步至Atlas
                syncAtlas(atlasSyncRequestDTO);
            }
        }
    }

    /**
     * 创建Atlas同步请求对象
     *
     * @param contractEntity 合同实体
     * @param ttId           ttId
     * @param tradeType      交易类型
     * @param operatorName   操作人
     * @return
     */
    private AtlasSyncRequestDTO createAtlasContractSyncRequest(ContractEntity contractEntity, Integer ttId, Integer tradeType, String operatorName) {
        AtlasSyncRequestDTO atlasSyncRequestDTO = new AtlasSyncRequestDTO();
        atlasSyncRequestDTO.setTtId(ttId)
                .setBizId(contractEntity.getId())
                .setBizCode(contractEntity.getContractCode())
                .setSalesType(contractEntity.getSalesType())
                .setObjectType(AtlasSyncObjectTypeEnum.CONTRACT.getValue())
                .setOperationType(AtlasSyncActionEnum.getOperationByTradeType(tradeType).getValue())
                .setTradeType(tradeType)
                .setCreatedBy(operatorName)
                .setUpdatedBy(operatorName);
        return atlasSyncRequestDTO;
    }

    /**
     * 同步接口
     *
     * @param atlasSyncRequestDTO atlasSyncRequestDTO
     */
    private void syncAtlas(AtlasSyncRequestDTO atlasSyncRequestDTO) {
        // 合同同步至Atlas
        if (openAtlasSync == 1) {
            // 日志记录是否打开atlas同步
            log.info("===============[{}] Atlas Sync Open : {}===============", atlasSyncRequestDTO.getBizCode(), openAtlasSync);
            // 是否使用消息队列同步atlas
            if (openAtlasMessageQueue == 0) {
                atlasContractFacade.syncContractRequest(atlasSyncRequestDTO);
            } else {
                jmsTemplate.convertAndSend(syncQueueName, new Gson().toJson(atlasSyncRequestDTO));

                log.info("===============[{}] Queue Message Send Success : {}===============", syncQueueName, atlasSyncRequestDTO);
            }
        }
    }
}
