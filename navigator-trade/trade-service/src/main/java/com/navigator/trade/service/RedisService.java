package com.navigator.trade.service;

import com.navigator.common.dto.Result;

public interface RedisService {
    Result insertRedis(String key);

    Result getLockValue(String key);

    Result updateRedis(String key, String currentVersion);

    Result saveRedis(String version);

    Result reloadRedis(String version);

    Result rollBackRedis(String key);

    Result getByRedisKey(String key);
}
