package com.navigator.trade.dao;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.enums.EarlyWarningEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.trade.mapper.ContractMapper;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractPriceEndTypeEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022-01-20
 */
@Dao
public class ContractDao extends BaseDaoImpl<ContractMapper, ContractEntity> {
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CEmployFacade cEmployFacade;

    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
    /**
     * Get next group ID from database sequence
     * This method is thread-safe and prevents race conditions
     *
     * @return next group ID from sequence
     */
    public Integer getNextGroupId() {
        return baseMapper.getNextGroupId();
    }
    // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end

    /**
     * 获取最大的合同编号
     *
     * @param companyName     公司简称
     * @param salesType       销售类型
     * @param goodsCategoryId 品类类型
     * @return
     */
    public String getMaxContractCode(String companyName, Integer salesType, Integer goodsCategoryId) {
        IPage<ContractEntity> page = this.page(new Page<>(1, 1),
                Wrappers.<ContractEntity>lambdaQuery()
                        // 新增编号才累加
                        .likeRight(ObjectUtil.isNotEmpty(companyName), ContractEntity::getContractCode, companyName)
                        .eq(ContractEntity::getContractSource, ContractActionEnum.NEW.getActionValue())
                        .eq(ContractEntity::getSalesType, salesType)
                        .eq(ContractEntity::getCategory2, goodsCategoryId)
                        .ne(ContractEntity::getContractType, ContractTypeEnum.STRUCTURE.getValue())
                        .orderByDesc(ContractEntity::getCreatedAt));

        return page.getRecords().size() > 0 ? page.getRecords().get(0).getContractCode() : "";
    }

    /**
     * 根据id找合同
     *
     * @param id
     * @return
     */
    public ContractEntity getContractById(Integer id) {
        return this.baseMapper.selectOne(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(ContractEntity::getId, id)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

    }

    /**
     * TODO 没有被应用准备删除掉
     * 分页查询合同
     *
     * @param queryDTO
     */
    public IPage<ContractEntity> queryContract(QueryDTO<ContractBO> queryDTO) {
        ContractBO contractBO = queryDTO.getCondition();

        // 权限列表
        List<Integer> customerIdList = Collections.singletonList(-1);
        PermissionBO permissionBO = employFacade.querySitePermission(JwtUtils.getCurrentUserId(), contractBO.getGoodsCategoryId());
        if (CollectionUtils.isEmpty(permissionBO.getSiteCodeList())) {
            return this.page(new Page<>(0, 0, 0));
        }
        boolean bo = null != contractBO.getPriceEndType() && ObjectUtil.isNotEmpty(contractBO.getPriceEndTime()) && ContractPriceEndTypeEnum.DATE.getValue() == contractBO.getPriceEndType();
        LambdaQueryWrapper<ContractEntity> queryWrapper = Wrappers.<ContractEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(permissionBO.getSiteCodeList()), ContractEntity::getSiteCode, permissionBO.getSiteCodeList())
                .eq(null != contractBO.getSalesType(), ContractEntity::getSalesType, contractBO.getSalesType())
                .like(ObjectUtil.isNotEmpty(contractBO.getContractCode()), ContractEntity::getContractCode, "%" + (ObjectUtil.isNotEmpty(contractBO.getContractCode()) ? contractBO.getContractCode().trim() : contractBO.getContractCode()) + "%")
                .eq(ObjectUtil.isNotEmpty(contractBO.getDeliveryFactoryName()), ContractEntity::getDeliveryFactoryCode, contractBO.getDeliveryFactoryName())
                .eq(null != contractBO.getDeliveryType(), ContractEntity::getDeliveryType, contractBO.getDeliveryType())
                .like(StringUtil.isNotEmpty(contractBO.getCustomerName()), contractBO.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? ContractEntity::getCustomerName : ContractEntity::getSupplierName, "%" + contractBO.getCustomerName() + "%")
                .eq(ObjectUtil.isNotEmpty(contractBO.getDomainCode()), ContractEntity::getDomainCode, contractBO.getDomainCode())
                .eq(null != contractBO.getGoodsPackageId(), ContractEntity::getGoodsPackageId, contractBO.getGoodsPackageId())
                .eq(null != contractBO.getGoodsSpecId(), ContractEntity::getGoodsSpecId, contractBO.getGoodsSpecId())
                .eq(null != contractBO.getContractType(), ContractEntity::getContractType, contractBO.getContractType())
                .eq(null != contractBO.getGoodsCategoryId(), ContractEntity::getCategory2, contractBO.getGoodsCategoryId())
                .eq(null != contractBO.getStatus(), ContractEntity::getStatus, contractBO.getStatus())
                .eq(null != contractBO.getPriceEndType(), ContractEntity::getPriceEndType, contractBO.getPriceEndType())
                .eq(null != contractBO.getPriceEndType() && ObjectUtil.isNotEmpty(contractBO.getPriceEndTime()) && ContractPriceEndTypeEnum.TEXT.getValue() == contractBO.getPriceEndType(), ContractEntity::getPriceEndTime, contractBO.getPriceEndTime())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .between(ObjectUtil.isNotEmpty(contractBO.getSignDate()), ContractEntity::getSignDate, ObjectUtil.isNotEmpty(contractBO.getSignDate()) ? DateTimeUtil.parseTimeStamp0000(contractBO.getSignDate().split(" ")[0]) : "", ObjectUtil.isNotEmpty(contractBO.getSignDate()) ? DateTimeUtil.parseTimeStamp2359(contractBO.getSignDate().split(" ")[0]) : "")
                .between(bo, ContractEntity::getPriceEndTime, bo ? DateTimeUtil.parseTimeStamp0000(contractBO.getPriceEndTime().split(" ")[0]) : "", bo ? DateTimeUtil.parseTimeStamp2359(contractBO.getPriceEndTime().split(" ")[0]) : "")
                .eq(ObjectUtil.isNotEmpty(contractBO.getOperatorId()), ContractEntity::getUpdatedBy, contractBO.getOperatorId())
                .orderByDesc(ContractEntity::getUpdatedAt);

        if (null != contractBO.getTriggerSys()) {
            if (contractBO.getTriggerSys().equals(SystemEnum.COLUMBUS.getName())) {
                Integer customerId = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId();
                if (ContractSalesTypeEnum.SALES.getValue() == contractBO.getSalesType()) {
                    queryWrapper.eq(ContractEntity::getCustomerId, customerId);
                } else {
                    queryWrapper.eq(ContractEntity::getSupplierId, customerId);
                }
            } else {
                queryWrapper.in(ContractEntity::getBelongCustomerId, customerIdList);
            }
        }

        // 交货时间
        String deliveryStartTime = contractBO.getDeliveryStartTime();
        String deliveryEndTime = contractBO.getDeliveryEndTime();
        if (ObjectUtil.isNotEmpty(deliveryStartTime) && ObjectUtil.isNotEmpty(deliveryEndTime)) {
            queryWrapper
                    .ge(ContractEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ContractEntity::getDeliveryEndTime, DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1));

        } else {
            queryWrapper
                    .ge(ObjectUtil.isNotEmpty(contractBO.getDeliveryStartTime()), ContractEntity::getDeliveryStartTime, deliveryStartTime + "-01")
                    .lt(ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()), ContractEntity::getDeliveryEndTime, ObjectUtil.isNotEmpty(contractBO.getDeliveryEndTime()) ? DateTimeUtil.addMonth(DateTimeUtil.parseDateString(deliveryEndTime + "-01"), 1) : deliveryEndTime);
        }

        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }


    /**
     * 查询客户的转月或转月数量
     *
     * @param contractFuturesDTO
     * @return
     */
    public List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO) {

        LambdaQueryWrapper<ContractEntity> wrappers = new LambdaQueryWrapper<ContractEntity>()
                .eq(ObjectUtil.isNotEmpty(contractFuturesDTO.getDomainCode()), ContractEntity::getDomainCode, contractFuturesDTO.getDomainCode())
                .eq(null != contractFuturesDTO.getCategory2(), ContractEntity::getCategory2, contractFuturesDTO.getCategory2())
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .eq(null != contractFuturesDTO.getSalesType(), ContractEntity::getSalesType, contractFuturesDTO.getSalesType())
                .eq(StringUtil.isNotEmpty(contractFuturesDTO.getFutureCode()), ContractEntity::getFutureCode, contractFuturesDTO.getFutureCode())
                .eq(ContractEntity::getCompanyId, contractFuturesDTO.getCompanyId())
                .in(null != contractFuturesDTO.getContractType(), ContractEntity::getContractType, contractFuturesDTO.getContractType())
                //.gt(contractFuturesDTO.getPriceType() == PriceTypeEnum.TRANSFER_MONTH.getValue(), ContractEntity::getPriceEndTime, DateUtil.offsetDay(DateUtil.date(),30))
                .gt(ContractEntity::getContractNum, 0)
                .and(wrapper -> wrapper.isNull(ContractEntity::getCloseTailNum).or().eq(ContractEntity::getCloseTailNum, BigDecimal.ZERO))
                .apply("contract_num <> total_price_num")
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(StringUtil.isNotEmpty(contractFuturesDTO.getBuCode()), ContractEntity::getBuCode, contractFuturesDTO.getBuCode());

        if (null != contractFuturesDTO.getSalesType() && contractFuturesDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            wrappers.eq(null != contractFuturesDTO.getCustomerId(), ContractEntity::getSupplierId, contractFuturesDTO.getCustomerId());
        } else {
            wrappers.eq(null != contractFuturesDTO.getCustomerId(), ContractEntity::getCustomerId, contractFuturesDTO.getCustomerId());
                    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
                    //.gt(null != contractFuturesDTO.getPriceType() && PriceTypeEnum.TRANSFER_MONTH.getValue() == contractFuturesDTO.getPriceType() && PriceTypeEnum.STRUCTURE_PRICING.getValue() != contractFuturesDTO.getStatus(), ContractEntity::getAbleTransferTimes, 0);
                    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
        }
        if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus()) {
            wrappers.gt(null != contractFuturesDTO.getPriceBeginTime(), ContractEntity::getPriceStartTime, DateTimeUtil.formatDateString(contractFuturesDTO.getPriceBeginTime()));
        }

        //todo 合同合约时间未定
        return this.baseMapper.selectList(wrappers);
    }

    /**
     * 分组查询客户的期货
     *
     * @param contractFuturesDTO
     * @return
     */
    public List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO) {


        LambdaQueryWrapper<ContractEntity> wrapper = new LambdaQueryWrapper<ContractEntity>()
                .eq(ObjectUtil.isNotEmpty(contractFuturesDTO.getDomainCode()), ContractEntity::getDomainCode, contractFuturesDTO.getDomainCode())
                .eq(ObjectUtil.isNotEmpty(contractFuturesDTO.getFutureCode()), ContractEntity::getFutureCode, contractFuturesDTO.getFutureCode())
                .eq(null != contractFuturesDTO.getCategory2(), ContractEntity::getCategory2, contractFuturesDTO.getCategory2())
                .eq(null != contractFuturesDTO.getContractType(), ContractEntity::getContractType, contractFuturesDTO.getContractType())
                .in( ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()))
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(ContractEntity::getCompanyId, contractFuturesDTO.getCompanyId())
                .gt(ContractEntity::getContractNum, 0)
                .eq(StringUtil.isNotEmpty(contractFuturesDTO.getBuCode()), ContractEntity::getBuCode, contractFuturesDTO.getBuCode())
                .apply("contract_num <> total_price_num")
                .eq(contractFuturesDTO.getSalesType() != null, ContractEntity::getSalesType, contractFuturesDTO.getSalesType());


        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractFuturesDTO.getSalesType()) {
            wrapper.eq(null != contractFuturesDTO.getCustomerId(), ContractEntity::getSupplierId, contractFuturesDTO.getCustomerId());
        } else {
            wrapper.eq(null != contractFuturesDTO.getCustomerId(), ContractEntity::getCustomerId, contractFuturesDTO.getCustomerId());
        }
        if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus()) {
            wrapper.gt(null != contractFuturesDTO.getPriceBeginTime(), ContractEntity::getPriceStartTime, DateTimeUtil.formatDateString(contractFuturesDTO.getPriceBeginTime()))
                    .eq(ContractSalesTypeEnum.SALES.getValue() == contractFuturesDTO.getSalesType() && ObjectUtil.isNotEmpty(contractFuturesDTO.getSupplierId()), ContractEntity::getSupplierId, contractFuturesDTO.getSupplierId());
            //.groupBy(ContractEntity::getDomainCode, ContractEntity::getGoodsCategoryId, ContractEntity::getSupplierId)
            //.select(ContractEntity::getDomainCode, ContractEntity::getGoodsCategoryId, ContractEntity::getSupplierId);
        }
        wrapper.groupBy(ContractEntity::getDomainCode, ContractEntity::getCategory2, ContractEntity::getFutureCode)
                .select(ContractEntity::getDomainCode, ContractEntity::getCategory2, ContractEntity::getFutureCode);

        //todo 查询合同合约时间未定
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    public IPage<ContractEntity> futureContracts(QueryDTO<ContractFuturesDTO> queryDTO) {

        Integer pageNo = queryDTO.getPageNo();
        Integer pageSize = queryDTO.getPageSize();

        ObjectMapper mapper = new ObjectMapper();
        ContractFuturesDTO contractFuturesDTO = mapper.convertValue(queryDTO.getCondition(), ContractFuturesDTO.class);

        if (null == contractFuturesDTO.getSalesType()) {
            contractFuturesDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        }

        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractFuturesDTO.getSalesType()) {
            contractFuturesDTO.setCustomerId(null)
                    .setSupplierId(contractFuturesDTO.getCustomerId());
        }

        LambdaQueryWrapper<ContractEntity> wrapper = new LambdaQueryWrapper<ContractEntity>()
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .eq(null != contractFuturesDTO.getCompanyId(), ContractEntity::getCompanyId, contractFuturesDTO.getCompanyId())
                .eq(ObjectUtil.isNotEmpty(contractFuturesDTO.getCustomerId()), ContractEntity::getCustomerId, contractFuturesDTO.getCustomerId())
                .eq(ContractEntity::getDomainCode, contractFuturesDTO.getDomainCode())
                .eq(ObjectUtil.isNotEmpty(contractFuturesDTO.getSupplierId()), ContractEntity::getSupplierId, contractFuturesDTO.getSupplierId())
                .eq(ContractEntity::getCategory2, contractFuturesDTO.getCategory2())
                .eq(StringUtil.isNotEmpty(contractFuturesDTO.getFutureCode()), ContractEntity::getFutureCode, contractFuturesDTO.getFutureCode())
                .gt(null != contractFuturesDTO.getPriceType() && contractFuturesDTO.getPriceType() == PriceTypeEnum.TRANSFER_MONTH.getValue(), ContractEntity::getAbleTransferTimes, 0)
                .eq(contractFuturesDTO.getSalesType() != null, ContractEntity::getSalesType, contractFuturesDTO.getSalesType())
                .in(ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()))
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .and(wrapper2 -> wrapper2.isNull(ContractEntity::getCloseTailNum).or().eq(ContractEntity::getCloseTailNum, BigDecimal.ZERO));

        if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus()) {
            wrapper.gt(null != contractFuturesDTO.getPriceBeginTime(), ContractEntity::getPriceStartTime, DateTimeUtil.formatDateString(contractFuturesDTO.getPriceBeginTime()));
        }

        return this.page(new Page<>(pageNo, pageSize), wrapper);
    }


    /**
     * 根据合约号获取当前客户可以分配的合同(分页)
     *
     * @param queryDTO
     * @return
     */
    public IPage<ContractEntity> queryContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO) {
        Integer pageNo = queryDTO.getPageNo();
        Integer pageSize = queryDTO.getPageSize();


        // 期货合约  客户Id  操作类型（1、点价 2、转月 3、反点价）
        QueryContractDTO queryContractDTO = queryDTO.getCondition();
        String domainCode = queryContractDTO.getDomainCode();
        String customerId = queryContractDTO.getCustomerId();
        String type = queryContractDTO.getType();

        // 品种Id 提货方式  交货工厂 包装id 规格id 目的港 合同号
        Integer goodsCategoryId = queryContractDTO.getGoodsCategoryId();
        String deliveryType = queryContractDTO.getDeliveryType();
        String deliveryFactoryCode = queryContractDTO.getDeliveryFactoryCode();
        Integer goodsPackageId = queryContractDTO.getGoodsPackageId();
        Integer category2 = queryContractDTO.getCategory2();
        Integer goodsSpecId = queryContractDTO.getGoodsSpecId();
        String destination = queryContractDTO.getDestination();
        String contractCode = queryContractDTO.getContractCode();
        String startTime = queryContractDTO.getStartTime();
        String endTime = queryContractDTO.getEndTime();
        String deliveryFactoryName = queryContractDTO.getDeliveryFactoryName();
        String buCode = queryContractDTO.getBuCode();

        QueryWrapper<ContractEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", ContractStatusEnum.EFFECTIVE.getValue());
        queryWrapper.eq("domain_code", domainCode);

        // 尾量关闭的合同不展示
        queryWrapper.and(wrapper -> wrapper.isNull("close_tail_num").or().eq("close_tail_num", BigDecimal.ZERO));

        if (null != queryContractDTO.getSalesType() && ContractSalesTypeEnum.SALES.getValue() == queryContractDTO.getSalesType()) {
            queryWrapper.eq("customer_id", customerId);
        } else {
            queryWrapper.eq("supplier_id", customerId);
        }

        if (null != queryContractDTO.getSalesType() && PriceTypeEnum.TRANSFER_MONTH.getValue() == Integer.valueOf(type) && ContractSalesTypeEnum.SALES.getValue() == queryContractDTO.getSalesType()) {
            queryWrapper.gt("able_transfer_times", 0);
        }

        if (StringUtil.isNotEmpty(buCode)) {
            queryWrapper.eq("bu_code", buCode);
        }

        if (null != category2) {
            queryWrapper.eq("category2", category2);
        }

        if (null != queryContractDTO.getCategory3()) {
            queryWrapper.eq("category3", queryContractDTO.getCategory3());
        }

        if (StringUtil.isNotEmpty(queryContractDTO.getShipWarehouseId())) {
            queryWrapper.eq("ship_warehouse_id", queryContractDTO.getShipWarehouseId());
        }

        if (StringUtil.isNotEmpty(queryContractDTO.getFutureCode())) {
            queryWrapper.eq("future_code", queryContractDTO.getFutureCode());
        }

        if (goodsCategoryId != null) {
            queryWrapper.eq("goods_category_id", goodsCategoryId);
        }

        if (queryContractDTO.getGoodsId() != null) {
            queryWrapper.eq("goods_id", queryContractDTO.getGoodsId());
        }

        if (ObjectUtil.isNotEmpty(deliveryType)) {
            queryWrapper.eq("delivery_type", deliveryType);
        }

        if (ObjectUtil.isNotEmpty(deliveryType)) {
            queryWrapper.eq("delivery_type", deliveryType);
        }
        if (ObjectUtil.isNotEmpty(deliveryFactoryCode)) {
            queryWrapper.eq("delivery_factory_code", deliveryFactoryCode);
        }

        if (ObjectUtil.isNotEmpty(queryContractDTO.getSiteCode())) {
            queryWrapper.eq("site_code", queryContractDTO.getSiteCode());
        }

        if (ObjectUtil.isNotEmpty(deliveryFactoryName)) {
            queryWrapper.eq("delivery_factory_name", deliveryFactoryName);
        }
        if (goodsPackageId != null) {
            queryWrapper.eq("goods_package_id", goodsPackageId);
        }
        if (StringUtil.isNotEmpty(queryContractDTO.getCompanyId())) {
            queryWrapper.eq("company_id", queryContractDTO.getCompanyId());
        }
        if (goodsSpecId != null) {
            queryWrapper.eq("goods_spec_id", goodsSpecId);
        }
        if (ObjectUtil.isNotEmpty(destination)) {
            queryWrapper.eq("destination", destination);
        }
        if (ObjectUtil.isNotEmpty(contractCode)) {
            queryWrapper.like("contract_code", contractCode);
        }
        if (ObjectUtil.isNotEmpty(startTime) && ObjectUtil.isNotEmpty(endTime)) {
            Date startTimeDate = DateTimeUtil.parseDateTimeString(startTime);
            Date endTimeDate = DateTimeUtil.parseDateTimeString(endTime);

            startTime = DateTimeUtil.formatDateTimeString00(startTimeDate);
            endTime = DateTimeUtil.formatDateTimeString00(endTimeDate);

            queryWrapper.between("delivery_start_Time", startTime, endTime);
        }

        queryWrapper.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue())
                .eq("sales_type", queryContractDTO.getSalesType());

        String jointSql = "";
        if (Integer.valueOf(type) == PriceTypeEnum.PRICING.getValue() || Integer.valueOf(type) == ContractTypeEnum.STRUCTURE.getValue()) {

            queryWrapper.in("contract_type", Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()));
            // 合同量大于合同已点量 + 转月 + 变更 +　该合同已经分配待审核的量

            if (Integer.valueOf(type) == ContractTypeEnum.STRUCTURE.getValue()) {
                jointSql = "contract_num > (total_price_num + (SELECT isnull(SUM (allocate_num),0) FROM dbf_price_allocate WHERE contract_id = id AND status = 1 AND price_apply_type = 1)) ";
            } else {
                jointSql = "contract_num > (total_price_num + (SELECT isnull(SUM (allocate_num),0) FROM dbf_price_allocate WHERE contract_id = id AND status = 1 AND price_apply_type = 5)) ";
            }

        } else if (Integer.valueOf(type) == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
            // 合同量大于合同转月量 + 点价量 + 该合同已经分配待审核的量
            jointSql = "contract_num > (total_price_num + (SELECT isnull(SUM (allocate_num),0) FROM dbf_price_allocate WHERE contract_id = id AND status = 1 AND price_apply_type = 2))";
            // 转月找基差合同
            queryWrapper.eq("contract_type", ContractTypeEnum.JI_CHA.getValue());

        }
        queryWrapper.apply(jointSql);


        IPage<ContractEntity> page = this.page(new Page<>(pageNo, pageSize), queryWrapper);

        return page;
    }

    public List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = new LambdaQueryWrapper<ContractEntity>()
                .eq(ContractEntity::getCategory2, queryContractDTO.getCategory2())
                .eq(ContractEntity::getFutureCode, queryContractDTO.getFutureCode())
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .gt(ContractEntity::getContractNum, BigDecimal.ZERO)
                .ne(ContractEntity::getContractType, ContractTypeEnum.STRUCTURE.getValue())
                .eq(Integer.valueOf(queryContractDTO.getType()) == PriceTypeEnum.TRANSFER_MONTH.getValue(), ContractEntity::getContractType, ContractTypeEnum.JI_CHA.getValue())
                .gt(Integer.valueOf(queryContractDTO.getType()) == PriceTypeEnum.TRANSFER_MONTH.getValue(), ContractEntity::getAbleTransferTimes, 0)
                .in(Integer.valueOf(queryContractDTO.getType()) == PriceTypeEnum.PRICING.getValue(), ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()))
                .eq(ContractEntity::getDomainCode, queryContractDTO.getDomainCode())
                .eq(ContractEntity::getCompanyId, queryContractDTO.getCompanyId())
                .eq(ContractEntity::getCustomerId, queryContractDTO.getCustomerId())
                .and(wrapper -> wrapper.isNull(ContractEntity::getCloseTailNum).or().eq(ContractEntity::getCloseTailNum, BigDecimal.ZERO));

        if (PriceTypeEnum.TRANSFER_MONTH.getValue() == Integer.parseInt(queryContractDTO.getType())) {

            queryWrapper.orderByDesc(ContractEntity::getDeliveryStartTime)
                    .orderByAsc(ContractEntity::getSignDate, ContractEntity::getCreatedAt);

        } else {
            queryWrapper.orderByDesc(ContractEntity::getContractType)
                    .orderByAsc(ContractEntity::getPriceEndTime,
                            ContractEntity::getDeliveryStartTime,
                            ContractEntity::getSignDate, ContractEntity::getCreatedAt);
        }

        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @param customerId
     * @return
     */
    public IPage<ContractEntity> queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO, Integer customerId) {

        ObjectMapper mapper = new ObjectMapper();
        QueryContractDTO queryContractDTO = mapper.convertValue(queryDTO.getCondition(), QueryContractDTO.class);

        // 合同状态 合同编号 客户 交货工厂 合同吨数 含税单价 合同类型 品种 包装 规格 预警类型 签订日期 点价截止日期
        LambdaQueryWrapper<ContractEntity> wrapper = new LambdaQueryWrapper<ContractEntity>()
                .eq(ContractEntity::getCustomerId, customerId)
                .eq(ObjectUtil.isNotEmpty(queryContractDTO.getContractCode()), ContractEntity::getContractCode, queryContractDTO.getContractCode())
                .like(ObjectUtil.isNotEmpty(queryContractDTO.getCustomerName()), ContractEntity::getCustomerName, "%" + queryContractDTO.getCustomerName() + "%")
                .eq(ObjectUtil.isNotEmpty(queryContractDTO.getDeliveryFactoryCode()), ContractEntity::getDeliveryFactoryCode, queryContractDTO.getDeliveryFactoryCode())
                .eq(queryContractDTO.getContractNum() != null, ContractEntity::getContractNum, queryContractDTO.getContractNum())
                .eq(queryContractDTO.getUnitPrice() != null, ContractEntity::getUnitPrice, queryContractDTO.getUnitPrice())
                .eq(queryContractDTO.getContractType() != null, ContractEntity::getContractType, queryContractDTO.getContractType())
                .eq(queryContractDTO.getGoodsCategoryId() != null, ContractEntity::getGoodsCategoryId, queryContractDTO.getGoodsCategoryId())
                .eq(queryContractDTO.getGoodsPackageId() != null, ContractEntity::getGoodsPackageId, queryContractDTO.getGoodsPackageId())
                .eq(queryContractDTO.getGoodsSpecId() != null, ContractEntity::getGoodsSpecId, queryContractDTO.getGoodsSpecId())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(queryContractDTO.getWarningType() != null && queryContractDTO.getWarningType().equals(EarlyWarningEnum.ORIGINAL_PAPER.getValue()), ContractEntity::getNeedOriginalPaper, queryContractDTO.getWarningType())
                .eq(queryContractDTO.getWarningType() != null && queryContractDTO.getWarningType().equals(EarlyWarningEnum.LDC_ORIGINAL_PAPER.getValue()), ContractEntity::getLdcNeedOriginalPaper, queryContractDTO.getWarningType())
                .eq(ObjectUtil.isNotEmpty(queryContractDTO.getSignDate()), ContractEntity::getSignDate, queryContractDTO.getSignDate() + " 00:00:00.000")
                .eq(ObjectUtil.isNotEmpty(queryContractDTO.getPriceEndTime()), ContractEntity::getPriceEndTime, queryContractDTO.getPriceEndTime());

        IPage<ContractEntity> iPage = this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
        return iPage;
    }

    public List<ContractEntity> getWashOutList(Integer contractId) {
        return this.baseMapper.selectList(Wrappers.<ContractEntity>lambdaQuery()
                .eq(ContractEntity::getContractSource, ContractActionEnum.WASHOUT.getActionValue())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .and(wrapper -> wrapper.eq(ContractEntity::getId, contractId).or().eq(ContractEntity::getParentId, contractId))
        );
    }

    public List<ContractEntity> getByContractCode(String contractCode) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(ContractEntity::getContractCode, contractCode)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContractEntity> getContractCodeList(String contractCode) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .likeRight(ContractEntity::getContractCode, contractCode)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContractEntity> getContractByPid(Integer pid) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(ContractEntity::getParentId, pid)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContractEntity> getContractByWarrantCode(String warrantCode) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .likeRight(ContractEntity::getWarrantCode, warrantCode)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContractEntity> getContractList(Integer customerId, Integer goodsCategoryId, String domainCode, List<Integer> contractTypeList) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(null != customerId, ContractEntity::getCustomerId, customerId)
                        .eq(null != goodsCategoryId, ContractEntity::getGoodsCategoryId, goodsCategoryId)
                        .eq(ObjectUtil.isNotEmpty(domainCode), ContractEntity::getDomainCode, domainCode)
                        .in(ObjectUtil.isNotEmpty(contractTypeList), ContractEntity::getContractType, contractTypeList)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 Start
    public List<ContractEntity> getContractList(Integer customerId, List<Integer> category3List, List<Integer> contractStatusList, List<Integer> contractTypeList) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(null != customerId, ContractEntity::getCustomerId, customerId)
                        .in(!category3List.isEmpty(), ContractEntity::getCategory3, category3List)
                        // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-18 End
                        .in(ObjectUtil.isNotEmpty(contractStatusList), ContractEntity::getStatus, contractStatusList)
                        .in(ObjectUtil.isNotEmpty(contractTypeList), ContractEntity::getContractType, contractTypeList)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContractEntity> getDailyContractList(Integer salesType, String startDateTime, String endDateTime) {
        // 获取当天生效的合同和当天关闭的合同
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .between(ObjectUtil.isNotEmpty(startDateTime) && ObjectUtil.isNotEmpty(endDateTime), ContractEntity::getUpdatedAt, startDateTime, endDateTime)
                        .eq(ContractEntity::getSalesType, salesType)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .and(wrapper -> wrapper.in(ContractEntity::getStatus, Arrays.asList(ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.MODIFYING.getValue(), ContractStatusEnum.SPLITTING.getValue(), ContractStatusEnum.CLOSING.getValue()))
                                .or(wrapper2 -> wrapper2.eq(ContractEntity::getStatus, ContractStatusEnum.CLOSED.getValue())
                                        .between(ContractEntity::getUpdatedAt, DateTimeUtil.formatDateTimeString00(DateTimeUtil.now()),
                                                DateTimeUtil.formatDateTimeString24(DateTimeUtil.now())))));
    }

    public ContractEntity getBasicContractByCode(String code) {
        List<ContractEntity> list = list(
                Wrappers.<ContractEntity>lambdaQuery()
                        .eq(ContractEntity::getContractCode, code)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByDesc(ContractEntity::getId)
        );
        return ObjectUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public ContractEntity getContractEntityByCodeLike(String code) {
        List<ContractEntity> list = list(
                Wrappers.<ContractEntity>lambdaQuery()
                        .like(ContractEntity::getContractCode, code)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByDesc(ContractEntity::getId)
        );
        return ObjectUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<ContractEntity> getContractList(List<String> contractCodeList, String startDateTime, String endDateTime) {
        return this.baseMapper.selectList(
                Wrappers.<ContractEntity>lambdaQuery()
                        .in(ObjectUtil.isNotEmpty(contractCodeList), ContractEntity::getContractCode, contractCodeList)
                        .notIn(ContractEntity::getStatus, Arrays.asList(ContractStatusEnum.CLOSED.getValue(), ContractStatusEnum.INVALID.getValue()))
                        .notIn(ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.STRUCTURE.getValue()))
                        .between(ObjectUtil.isNotEmpty(startDateTime) && ObjectUtil.isNotEmpty(endDateTime), ContractEntity::getUpdatedAt, startDateTime, endDateTime)
                        .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO, List<Integer> cannotDeliveryGoodsIdList, List<String> cannotDeliveryFactoryList, List<Integer> cannotDeliveryTypeIdList) {
        return list(Wrappers.<ContractEntity>lambdaQuery()
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .in(ContractEntity::getContractType, Arrays.asList(
                        ContractTypeEnum.YI_KOU_JIA.getValue(),
                        ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue(),
                        ContractTypeEnum.ZAN_DING_JIA.getValue()))
                .gt(ContractEntity::getContractNum, BigDecimal.ZERO)
                .eq(null != deliveryApplyContractQO.getCustomerId(), ContractEntity::getCustomerId, deliveryApplyContractQO.getCustomerId())
                .eq(null != deliveryApplyContractQO.getGoodsId(), ContractEntity::getGoodsId, deliveryApplyContractQO.getGoodsId())
                .eq(null != deliveryApplyContractQO.getGoodsCategoryId(), ContractEntity::getGoodsCategoryId, deliveryApplyContractQO.getGoodsCategoryId())
                .eq(null != deliveryApplyContractQO.getSupplierId(), ContractEntity::getSupplierId, deliveryApplyContractQO.getSupplierId())
                .eq(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryFactoryCode()), ContractEntity::getDeliveryFactoryCode, deliveryApplyContractQO.getDeliveryFactoryCode())
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryGoodsIdList), ContractEntity::getGoodsId, cannotDeliveryGoodsIdList)
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryFactoryList), ContractEntity::getDeliveryFactoryCode, cannotDeliveryFactoryList)
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryTypeIdList), ContractEntity::getDeliveryType, cannotDeliveryTypeIdList)
                .and(wrapper -> wrapper.isNull(ContractEntity::getCloseTailNum).or().eq(ContractEntity::getCloseTailNum, BigDecimal.ZERO))
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO, List<Integer> cannotDeliveryGoodsIdList, List<String> cannotDeliveryFactoryList, List<Integer> cannotDeliveryTypeIdList) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = Wrappers.<ContractEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(deliveryApplyContractQO.getContractIdList()), ContractEntity::getId, deliveryApplyContractQO.getContractIdList())
                .eq(StringUtil.isNotNullBlank(deliveryApplyContractQO.getBuCode()), ContractEntity::getBuCode, deliveryApplyContractQO.getBuCode())
                .eq(ContractEntity::getStatus, ContractStatusEnum.EFFECTIVE.getValue())
                .in(ContractEntity::getContractType, Arrays.asList(
                        ContractTypeEnum.YI_KOU_JIA.getValue(),
                        ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue(),
                        ContractTypeEnum.ZAN_DING_JIA.getValue()))
                .gt(ContractEntity::getContractNum, BigDecimal.ZERO)
                .like(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getContractCode()), ContractEntity::getContractCode, deliveryApplyContractQO.getContractCode())
                .eq(null != deliveryApplyContractQO.getDeliveryType(), ContractEntity::getDeliveryType, deliveryApplyContractQO.getDeliveryType())
                .in(CollUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryTypeList()), ContractEntity::getDeliveryType, deliveryApplyContractQO.getDeliveryTypeList())
                .eq(null != deliveryApplyContractQO.getContractType(), ContractEntity::getContractType, deliveryApplyContractQO.getContractType())
                // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
                .eq(null != deliveryApplyContractQO.getShipWarehouseId(), ContractEntity::getShipWarehouseId, deliveryApplyContractQO.getShipWarehouseId())
                .eq(null != deliveryApplyContractQO.getPaymentType(), ContractEntity::getPaymentType, deliveryApplyContractQO.getPaymentType())
                .between(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getContractSignDate()), ContractEntity::getSignDate,
                        ObjectUtil.isNotEmpty(deliveryApplyContractQO.getContractSignDate()) ? DateTimeUtil.parseTimeStamp0000(deliveryApplyContractQO.getContractSignDate().split(" ")[0]) : "",
                        ObjectUtil.isNotEmpty(deliveryApplyContractQO.getContractSignDate()) ? DateTimeUtil.parseTimeStamp2359(deliveryApplyContractQO.getContractSignDate().split(" ")[0]) : "")
                // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End
                // 提货交期（时间段搜索） 交货开始时间 <= 提货交期 <= 交货结束时间
                .ge(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryStartDate()), ContractEntity::getDeliveryStartTime, ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryStartDate()) ? DateTimeUtil.parseTimeStamp0000(deliveryApplyContractQO.getDeliveryStartDate().split(" ")[0]) : "")
                .le(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryEndDate()), ContractEntity::getDeliveryEndTime, ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryEndDate()) ? DateTimeUtil.parseTimeStamp2359(deliveryApplyContractQO.getDeliveryEndDate().split(" ")[0]) : "")
                .eq(null != deliveryApplyContractQO.getCustomerId(), ContractEntity::getCustomerId, deliveryApplyContractQO.getCustomerId())
                .eq(null != deliveryApplyContractQO.getGoodsId(), ContractEntity::getGoodsId, deliveryApplyContractQO.getGoodsId())
                .eq(null != deliveryApplyContractQO.getSupplierId(), ContractEntity::getSupplierId, deliveryApplyContractQO.getSupplierId())
                .eq(ObjectUtil.isNotEmpty(deliveryApplyContractQO.getDeliveryFactoryCode()), ContractEntity::getDeliveryFactoryCode, deliveryApplyContractQO.getDeliveryFactoryCode())
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryGoodsIdList), ContractEntity::getGoodsId, cannotDeliveryGoodsIdList)
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryFactoryList), ContractEntity::getDeliveryFactoryCode, cannotDeliveryFactoryList)
                .notIn(ObjectUtil.isNotEmpty(cannotDeliveryTypeIdList), ContractEntity::getDeliveryType, cannotDeliveryTypeIdList)
                .and(wrapper -> wrapper.isNull(ContractEntity::getCloseTailNum).or().eq(ContractEntity::getCloseTailNum, BigDecimal.ZERO))
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        // 排序规则
        queryWrapper
                // 在开始提货月份内靠前的优先||暂定>基差暂定价>一口价||预付>赊销||一口价价格更高优先,基差暂定价&一口价暂定价价格更低优先||合同签署日期：靠前的优先||合同总数量更小的优先||合同号小的优先
                .last("ORDER BY delivery_start_time ASC , " +
                        "CASE WHEN contract_type = 3 THEN 1 WHEN contract_type = 4 THEN 2 WHEN contract_type = 1 THEN 3 END ASC, " +
                        "payment_type DESC , " +
                        "CASE WHEN contract_type = 1 THEN -1 * unit_price ELSE unit_price END ASC, " +
                        "sign_date ASC , contract_num ASC , contract_code ASC");

        return list(queryWrapper);
    }

    public List<ContractEntity> getContractListByIds(List<Integer> contractIdList) {
        return list(Wrappers.<ContractEntity>lambdaQuery().in(ContractEntity::getId, contractIdList));
    }

    public Boolean closedBySiteCodeAndContractCode(String siteCode, String contractCode) {
        Integer result = baseMapper.closedBySiteCodeAndContractCode(siteCode, contractCode);
        return result > 0;
    }

    public List<ContractEntity> getCargoRightsContractById(Integer contractId) {
        return list(Wrappers.<ContractEntity>lambdaQuery()
                .eq(null != contractId, ContractEntity::getParentId, contractId)
                .eq(ContractEntity::getContractNature, ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue())
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<ContractEntity> getContractByCategory3List(List<Integer> category3List, String siteCode) {
        return list(Wrappers.<ContractEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(category3List), ContractEntity::getCategory3, category3List)
                .eq(StringUtils.isNotBlank(siteCode), ContractEntity::getSiteCode, siteCode)
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /*
     * Jason Added for get contracts by status
     * */
    public List<ContractEntity> getContractByStatus(String status, List<String> contractNumbers) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = Wrappers.<ContractEntity>lambdaQuery()
                .likeRight(ContractEntity::getStatus, status)
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        if (contractNumbers != null && !contractNumbers.isEmpty()) {
            queryWrapper.in(ContractEntity::getContractCode, contractNumbers);
        }

        return this.baseMapper.selectList(queryWrapper);
    }

    public List<ContractEntity> getContractByPurchase(ContractEntity purchaseContract) {
        List<Integer> contractStatusList = new ArrayList<>();
        contractStatusList.add(ContractStatusEnum.CLOSED.getValue());
        contractStatusList.add(ContractStatusEnum.INVALID.getValue());
        return list(Wrappers.<ContractEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(purchaseContract.getWarrantCode()), ContractEntity::getWarrantCode, purchaseContract.getWarrantCode())
                .eq(ContractEntity::getSalesType, ContractSalesTypeEnum.SALES.getValue())
                .notIn(ContractEntity::getStatus, contractStatusList)
                .eq(ContractEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }
}
