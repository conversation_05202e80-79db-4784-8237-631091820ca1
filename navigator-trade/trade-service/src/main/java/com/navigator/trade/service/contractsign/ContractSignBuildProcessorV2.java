package com.navigator.trade.service.contractsign;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.*;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.CustomerProtocolTypeEnum;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ExchangeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.StandardType;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.*;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.*;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.facade.SpuFacade;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.koala.pojo.enums.WarrantCategoryEnum;
import com.navigator.koala.pojo.enums.WarrantPropertyEnum;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.KeyVariableDTO;
import com.navigator.trade.pojo.dto.contractsign.SignHuskyTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.TemplateConditionDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.IContractQueryService;
import com.navigator.trade.service.IDeliveryTypeService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.navigator.common.constant.BizConstant.DEFAULT_PAY_DAYS;
import static com.navigator.common.constant.ContractSignConstant.*;

@Service
@Slf4j
public class ContractSignBuildProcessorV2 {
    @Autowired
    protected AttributeFacade attributeFacade;
    @Autowired
    protected SystemRuleFacade systemRuleFacade;
    @Autowired
    protected CustomerFacade customerFacade;
    @Autowired
    protected CustomerDetailFacade customerDetailFacade;
    @Autowired
    protected CustomerProtocolFacade customerProtocolFacade;
    @Autowired
    protected CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Autowired
    protected FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    protected WarehouseFacade warehouseFacade;
    @Resource
    private ContractSignDao contractSignDao;
    @Resource
    IContractQueryService contractQueryService;
    @Resource
    ITradeTicketQueryService ttQueryService;
    @Resource
    ContractQueryLogicService contractQueryLogicService;
    @Resource
    private IDeliveryTypeService deliveryTypeService;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Resource
    private AzureBlobUtil azureBlobUtil;
    @Resource
    private ITtPriceService ttPriceService;
    @Resource
    private QualityFacade qualityFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private SpuFacade spuFacade;
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private WarrantFacade warrantFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Value("${contract.sign.qrCodeUrl}")
    private String qrCodeUrl;

    public ContractSignTemplateDTO getContractSignTemplateDTO(Integer signId) {
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();

        //协议信息
        ContractSignEntity contractSignEntity = contractSignDao.getById(signId);
        contractSignTemplateDTO.setContractSignDTO(contractSignEntity);

        //合同信息
        int contractId = contractSignEntity.getContractId();
        ContractDetailInfoDTO contractDetailInfoDTO = contractQueryLogicService.getContractDetailInfoDTO(contractId);
        contractSignTemplateDTO.setContractDetailInfoDTO(contractDetailInfoDTO);

        //TT信息
        Integer ttid = contractSignEntity.getTtId();
        TradeTicketDTO tradeTicketDTO = ttQueryService.getTTDetailInfo(ttid);
        contractSignTemplateDTO.setTradeTicketDTO(tradeTicketDTO);

        Integer srcContractId = tradeTicketDTO.getSourceContractId();
        if (null != srcContractId && srcContractId > 0) {
            ContractDetailInfoDTO srcContractDetailInfoDTO = contractQueryLogicService.getContractDetailInfoDTO(srcContractId);
            contractSignTemplateDTO.setSourceContractDetailInfoDTO(srcContractDetailInfoDTO);
        }
        return contractSignTemplateDTO;
    }

    public SignHuskyTemplateDTO buildSignTemplateDTO(ContractSignTemplateDTO contractSignTemplateDTO) {
        SignHuskyTemplateDTO signHuskyTemplateDTO = new SignHuskyTemplateDTO();
        log.info("1、协议出具的合同基本信息-出具拼装===============" + contractSignTemplateDTO.getContractCode() + FastJsonUtils.getBeanToJson(contractSignTemplateDTO));
        //1、合同/原合同信息
        ContractDetailInfoDTO contractDetailInfoDTO = contractSignTemplateDTO.getContractDetailInfoDTO();
        ContractDetailInfoDTO sourceContractDetailInfoDTO = contractSignTemplateDTO.getSourceContractDetailInfoDTO();
        //2、协议信息
        ContractSignEntity contractSignDTO = contractSignTemplateDTO.getContractSignDTO();
        //3、TT信息
        TradeTicketDTO tradeTicketDTO = contractSignTemplateDTO.getTradeTicketDTO();
        //4、条件变量信息获取
        TemplateConditionDTO templateCondition = this.assemblyTemplateCondition(contractSignDTO, contractDetailInfoDTO, tradeTicketDTO);
        signHuskyTemplateDTO.setTemplateCondition(templateCondition);
        if (null == contractDetailInfoDTO) {
            throw new BusinessException("合同信息获取异常");
        }
        log.info("2、条件变量信息获取-出具拼装===============" + FastJsonUtils.getBeanToJson(templateCondition));
        //5、协议信息-拼装,及条件变量
        this.buildContractSignInfo(signHuskyTemplateDTO, contractSignDTO);
        log.info("3、协议信息-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));
        //6、合同信息-拼装
        this.buildContractInfo(signHuskyTemplateDTO, contractDetailInfoDTO);
        log.info("4、合同信息-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));

        //7、原合同信息-拼装
        this.buildSourceContractInfo(signHuskyTemplateDTO, sourceContractDetailInfoDTO);

        //8、TT信息处理
        this.processTradeTicketInfo(signHuskyTemplateDTO, contractDetailInfoDTO, sourceContractDetailInfoDTO, contractSignDTO, tradeTicketDTO, signHuskyTemplateDTO.getTemplateCondition());
        log.info("7、TT信息处理-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));

        //9、仓单信息处理
        this.processWarrantInfo(signHuskyTemplateDTO, contractDetailInfoDTO);
        log.info("8、仓单信息处理-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));
        //10、根据规则处理逻辑变量信息
        this.afterProcessTemplateInfoByRule(signHuskyTemplateDTO, signHuskyTemplateDTO.getTemplateCondition());
        log.info("9、根据规则处理逻辑变量信息-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));
        //11、关键变量信息获取
        this.getKeyVariable(contractSignDTO, tradeTicketDTO, signHuskyTemplateDTO);
        log.info("10、关键变量信息获取-出具拼装===============" + FastJsonUtils.getBeanToJson(contractSignDTO.getId()));

        return signHuskyTemplateDTO;
    }

    private void processTradeTicketInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractDetailInfoDTO contractDetailInfoDTO,
                                        ContractDetailInfoDTO sourceContractDetailInfoDTO, ContractSignEntity contractSignDTO,
                                        TradeTicketDTO tradeTicketDTO, TemplateConditionDTO templateCondition) {
        //TT信息
        ContractAddTTDTO addTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractAddTTDTO();
        ContractModifyTTDTO modifyTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractModifyTTDTO();
        TTPriceEntity ttPriceEntity = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTTPriceDTO();
        ContractTransferTTDTO transferTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTransferTTDTO();
        ContractStructurePriceAddDTO structurePriceAddDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractStructurePriceAddDTO();
        //TT-主信息统一处理
        String modifyContent = "";
        String contentInfo = "";

        //bugfix:数字合同取值优化：合同拆分变更主体后，原合同补充协议中新公司主体名称取值不对
        if (null != tradeTicketDTO) {
            //======== customer处理区 ==========
            buildCustomerInfo(signHuskyTemplateDTO, tradeTicketDTO, contractSignDTO.getDeliveryFactoryCode());
            //======== LDC自身客户信息处理区 ========
            buildLdcSelfCustomerInfo(signHuskyTemplateDTO, tradeTicketDTO, contractSignDTO.getDeliveryFactoryCode());
        }

        //处理add（新增、关闭、洗单）
        if (null != addTTDTO) {
            buildTTAddInfo(signHuskyTemplateDTO, tradeTicketDTO, addTTDTO, contractDetailInfoDTO);
            //处理解约定赔
            if (contractSignDTO.getTradeType() == ContractTradeTypeEnum.WASHOUT.getValue()) {
                ContractPriceDTO contractPriceDTO = contractDetailInfoDTO.getContractPriceDTO();
                //解约定赔数量、解约定赔市场价格、解约定赔差价、解约定赔差价总额
                BigDecimal forwardPrice = null == contractPriceDTO || contractPriceDTO.getForwardPrice() == null ? BigDecimal.ZERO : contractPriceDTO.getForwardPrice();
                BigDecimal contractPrice = sourceContractDetailInfoDTO == null ? BigDecimal.ZERO : (sourceContractDetailInfoDTO.getUnitPrice() == null ? BigDecimal.ZERO : sourceContractDetailInfoDTO.getUnitPrice());
                if ((ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == templateCondition.getContractType() && BigDecimal.ZERO.compareTo(contractDetailInfoDTO.getTotalPriceNum()) == 0)
                        || ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
                    contractPrice = null != contractDetailInfoDTO && null != contractDetailInfoDTO.getUnitPrice() ? contractDetailInfoDTO.getUnitPrice().subtract(forwardPrice) : BigDecimal.ZERO;
                }
                buildWashoutInfo(signHuskyTemplateDTO, addTTDTO, contractPrice, templateCondition, contractDetailInfoDTO);
            }
        } else if (null != modifyTTDTO) {
            //处理修改/拆分
            modifyContent = modifyTTDTO.getModifyContent();
            contentInfo = modifyTTDTO.getContent();
            buildTTModifyInfo(signHuskyTemplateDTO, tradeTicketDTO, modifyTTDTO, contractDetailInfoDTO);
        } else if (null != ttPriceEntity) {
            //处理点价(点价/合同暂定价定价)
            //未定价量
            buildPriceInfo(signHuskyTemplateDTO, ttPriceEntity, contractDetailInfoDTO);
        } else if (null != transferTTDTO) {
            //处理转月、反点价
            signHuskyTemplateDTO.setThisTimeFee(BigDecimalUtil.formatBigDecimalZero(contractSignDTO.getThisTimeFee(), 2, RoundingMode.HALF_UP));
            signHuskyTemplateDTO.setTransferMonthTime(DateTimeUtil.formatDateStringCN(DateTimeUtil.addDays(tradeTicketDTO.getCreatedAt(), 1, false)));
            modifyContent = transferTTDTO.getModifyContent();
            contentInfo = transferTTDTO.getModifyContent();
            buildTransferInfo(signHuskyTemplateDTO, transferTTDTO);
        } else if (null != structurePriceAddDTO) {
            //==========处理结构化定价===========
            //结构化定价相关
            buildStructureInfo(signHuskyTemplateDTO, structurePriceAddDTO);
        }
        //重新build模板条件中的修改列表
        if (StringUtils.isNotBlank(modifyContent)) {
            templateCondition = buildModifyList(signHuskyTemplateDTO, templateCondition, modifyContent, null == tradeTicketDTO ? null : tradeTicketDTO.getType());
        }
        if (StringUtils.isNotBlank(contentInfo)) {
            //原合同-修改后含税总金额/增值税总金额
            //除了修改、拆分（出在原合同上），其他出在新合同上，取before
            buildContentInfo(signHuskyTemplateDTO, templateCondition, contentInfo, null == tradeTicketDTO ? null : tradeTicketDTO.getType());
        }
        if (null != tradeTicketDTO) {
//            //======== customer处理区 ==========
//            buildCustomerInfo(signHuskyTemplateDTO, tradeTicketDTO);
//            //======== LDC自身客户信息处理区 ========
//            buildLdcSelfCustomerInfo(signHuskyTemplateDTO, tradeTicketDTO);
            SkuEntity skuEntity = null;
            SpuEntity spuEntity = null;
            if (null != tradeTicketDTO.getGoodsId()) {
                skuEntity = skuFacade.getSkuById(tradeTicketDTO.getGoodsId());
            }
            AttributeValueEntity packageValueEntity = null;
            if (null != skuEntity) {
                packageValueEntity = attributeFacade.getAttributeValueById(skuEntity.getPackageId());
                spuEntity = spuFacade.getSpuById(skuEntity.getSpuId());
            }
            SiteEntity siteEntity = null;
            if (StringUtils.isNotBlank(tradeTicketDTO.getSiteCode())) {
                siteEntity = siteFacade.getSiteByCode(tradeTicketDTO.getSiteCode());
            }
            signHuskyTemplateDTO.setTradeTicketTime(DateTimeUtil.formatDateStringCN(tradeTicketDTO.getCreatedAt()));
            signHuskyTemplateDTO
                    //品种
                    .setGoodsCategoryName(tradeTicketDTO.getSubGoodsCategoryName())
                    .setGoodsPackageName(null != packageValueEntity ? packageValueEntity.getName() : "")
                    .setGoodsSpecId(tradeTicketDTO.getGoodsName())
                    .setGoodsFullName(tradeTicketDTO.getGoodsName())
                    .setCommodityName(tradeTicketDTO.getCommodityName())
                    .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(tradeTicketDTO.getSalesType()))
                    // 品种简称(豆粕：M; 豆油：Y)
                    .setGoodsCategorySymbol(tradeTicketDTO.getFutureCode())
                    // 品种全称(豆粕：SBM;豆油：SBO)
                    .setGoodsCategoryCode(GoodsCategoryEnum.getByValue(tradeTicketDTO.getSubGoodsCategoryId()).getCode())
                    // 合同类型
                    .setContractTypeInfo(tradeTicketDTO.getContractTypeName());
            signHuskyTemplateDTO.getTemplateCondition()
                    .setSpuName(null != spuEntity ? spuEntity.getSpuName() : "")
                    .setPackageName(null != packageValueEntity ? packageValueEntity.getName() : "")
                    .setDeliveryFactoryCode(null != siteEntity ? siteEntity.getFactoryCode() : templateCondition.getDeliveryFactoryCode())
            ;
        }
        //指标备注：输入框，字段“企标国标”，选择企标时，字段“指标备注” 为选填项；
        // 选择国标时，字段“指标备注”不可填写；录入的指标备注信息需在协议“质量指标”条款中展示。
        // 当“企标/国标”为企标时，质量指标条款内容优先取“指标备注”内容，若“指标备注”未填写，则取质量指标配置；
        // 当“企标/国标”为国标时，质量指标条款内容取质量指标配置内容。
        if (StringUtils.isNotBlank(contractDetailInfoDTO.getStandardType()) && StandardType.COMPANY_STANDARD.getDesc().equals(contractDetailInfoDTO.getStandardType()) && StringUtils.isNotBlank(contractDetailInfoDTO.getStandardRemark())) {
            signHuskyTemplateDTO.setQualityInfo(contractDetailInfoDTO.getStandardRemark());
        }
    }

    private void processWarrantInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractDetailInfoDTO contractDetailInfoDTO) {
        // 获取仓单类型和结算类型
        if (BuCodeEnum.WT.getValue().equals(contractDetailInfoDTO.getBuCode()) && ObjectUtil.isNotEmpty(contractDetailInfoDTO.getWarrantId())) {
            Result result = warrantFacade.queryWarrantByID(contractDetailInfoDTO.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);

            if (null != warrantEntity) {
                signHuskyTemplateDTO
                        //结算方式: 1,交易所结算  2,自行结算
                        .setSettleTypeInfo(StringUtils.isBlank(contractDetailInfoDTO.getSettleType()) ? "" : SettleType.getDescByValue(Integer.valueOf(contractDetailInfoDTO.getSettleType())))
                        //交割保证金付款方式: 1,保函  2,现金
                        .setDepositPaymentTypeInfo(DepositPaymentType.getDescByValue(warrantEntity.getDepositPaymentType()))
                        //仓单类型:1，厂库仓单 2，仓库仓单
                        .setWarrantCategoryInfo(WarrantCategoryEnum.getDescByValue(warrantEntity.getCategory()))
                        //仓单属性:1自由仓单；2仓单
                        .setWarrantPropertyInfo(WarrantPropertyEnum.getDescByValue(warrantEntity.getProperty()))
                        //仓单ID
                        .setWarrantId(warrantEntity.getId())
                        //仓单编号
                        .setWarrantCode(warrantEntity.getCode())
                        .setExchangeCode(warrantEntity.getExchangeCode())
                        .setExchangeName(ExchangeEnum.getDescByValue(warrantEntity.getExchangeCode()));
                signHuskyTemplateDTO.getTemplateCondition()
                        //结算方式: 1,交易所结算  2,自行结算
                        .setSettleType(Integer.valueOf(contractDetailInfoDTO.getSettleType()))
                        //交割保证金付款方式: 1,保函  2,现金
                        .setDepositPaymentType(warrantEntity.getDepositPaymentType())
                        //仓单类型:1，厂库仓单 2，仓库仓单
                        .setWarrantCategory(warrantEntity.getCategory())
                        //仓单属性:1自由仓单；2仓单
                        .setWarrantProperty(warrantEntity.getProperty());
            }
        }
    }

    /**
     * 复合/逻辑变量-处理
     *
     * @param signHuskyTemplateDTO
     * @param templateCondition
     */
    private void afterProcessTemplateInfoByRule(SignHuskyTemplateDTO signHuskyTemplateDTO, TemplateConditionDTO templateCondition) {
        String priceDeadlineText = "";
        if (null != templateCondition.getPriceEndType() && StringUtils.isNotBlank(signHuskyTemplateDTO.getPriceEndTime())) {
            priceDeadlineText = ContractPriceEndTypeEnum.DATE.getValue() == templateCondition.getPriceEndType() ?
                    TemplateConstant.PRICE_DEADLINE_DATE : TemplateConstant.PRICE_DEADLINE_TEXT;
            priceDeadlineText = priceDeadlineText.replace("#priceEndTime#", signHuskyTemplateDTO.getPriceEndTime());
        }
        Integer invoicePaymentRate = templateCondition.getInvoicePaymentRate();
        //正大集团-TT备注处理
        signHuskyTemplateDTO.setPriceDeadlineText(priceDeadlineText)
                .setInvoicePaymentRateInfo(invoicePaymentRate + "%")
                .setInvoicePaymentRateInTurnInfo((100 - invoicePaymentRate) + "%")
                .setTemplateCondition(templateCondition);
    }

    /**
     * 正大集团-TT备注处理
     *
     * @return
     */
    private String processZDJTDeliveryByMemo(String deliveryFactoryCode, String memo, TemplateConditionDTO templateCondition) {
        //正大“交货地点”特殊条款触发逻辑：客户=正大集团（配置的3个） 并且  有特殊备注  并且 交货工厂=TJ,
        // TT的备注信息中，包含被“@#￥”括起来的信息，信息内容不做判断
        Integer isZDSpecialDeliveryCustomer = 0;
        List<String> deliveryMemoList = new ArrayList<>();
        if ("TJ".equals(deliveryFactoryCode) && "ZDJT".equals(templateCondition.getEnterpriseCode())) {
            String specialZdSymbol = "@#￥";
            if (memo.contains(specialZdSymbol)) {
                String secondMemo = memo.replaceFirst(specialZdSymbol, "");
                if (secondMemo.contains(specialZdSymbol)) {
                    isZDSpecialDeliveryCustomer = 1;
                    deliveryMemoList = renderZDSpecialDeliveryList(memo);
                }
            }
        }
        templateCondition.setIsZDSpecialDeliveryCustomer(isZDSpecialDeliveryCustomer);
        return CollectionUtils.isEmpty(deliveryMemoList) ? "" : deliveryMemoList.get(0);
    }

    /**
     * 组装-关键变量信息
     *
     * @param contractSignDTO
     * @return
     */
    private void getKeyVariable(ContractSignEntity contractSignDTO, TradeTicketDTO tradeTicketDTO,
                                SignHuskyTemplateDTO signHuskyTemplateDTO) {

        CompanyEntity companyEntity = companyFacade.queryCompanyById(tradeTicketDTO.getCompanyId());
//        signHuskyTemplateDTO.setLogo(null == companyEntity ? "" : companyEntity.getLogo() + azureBlobUtil.getSharedAccessSignature());
        signHuskyTemplateDTO.setLogo(null == companyEntity ? "" : companyEntity.getLogo());
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketDTO.getSalesType() ? tradeTicketDTO.getCustomerId() :
                Integer.valueOf(tradeTicketDTO.getSupplierId());
        CustomerEntity customerEntity = customerFacade.getCustomerById(customerId);
        Integer tradeType = tradeTicketDTO.getTradeType();
        String protocolType = ProtocolTypeEnum.getByCode(contractSignDTO.getFrameProtocolType()).getValue();

        if (!ContractTradeTypeEnum.getNeedCustomerProtocolTradeList().contains(tradeType)) {
            protocolType = ProtocolTypeEnum.AGREEMENT.getValue();
        }
        //采购 &&（新增、部分转月、部分反点价、全部反点价,回购）取：大合同
        if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketDTO.getSalesType() && ContractTradeTypeEnum.getNeedCustomerProtocolTradeList().contains(tradeType)) {
            protocolType = ProtocolTypeEnum.CONTRACT.getValue();
        }
        //仓单分配、转让、注销都用大合同
        if (Arrays.asList(TTTypeEnum.ALLOCATE.getType(), TTTypeEnum.ASSIGN.getType(), TTTypeEnum.WRITE_OFF.getType(), TTTypeEnum.WRITE_OFF_OM.getType()).contains(tradeTicketDTO.getType())) {
            protocolType = ProtocolTypeEnum.CONTRACT.getValue();
        }
        KeyVariableDTO keyVariableDTO = new KeyVariableDTO()
                .setBuCode(contractSignDTO.getBuCode())
                .setCompanyCode(null == companyEntity ? "" : companyEntity.getShortName())
                .setSalesType(tradeTicketDTO.getSalesType())
                .setOriginalContractActionType(tradeType)
                //todo:nana 合同操作类型整理
                .setContractActionType(tradeType)
                .setCategoryId(tradeTicketDTO.getCategory2())
                .setCategory1(tradeTicketDTO.getCategory1())
                .setCategory2(tradeTicketDTO.getCategory2())
                .setCategory3(tradeTicketDTO.getCategory3())
                //todo:nana补充协议
                .setProtocolType(protocolType)
                .setCustomerCode(customerEntity.getLinkageCustomerCode());


        if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.NEW.getValue();
        } else if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        } else if (ContractTradeTypeEnum.FIXED.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.PRICE.getValue();
        } else if (Arrays.asList(ContractTradeTypeEnum.BUYBACK.getValue(),
                ContractTradeTypeEnum.ALLOCATE.getValue(), ContractTradeTypeEnum.ASSIGN.getValue()).contains(tradeType)) {
            tradeType = ContractTradeTypeEnum.NEW.getValue();
        }
        keyVariableDTO.setContractActionType(tradeType);
        //集团客户编码
        keyVariableDTO.setEnterpriseCode(signHuskyTemplateDTO.getTemplateCondition().getEnterpriseCode());
        signHuskyTemplateDTO.setKeyVariableDTO(keyVariableDTO);
    }

    /**
     * 条件变量信息组装
     *
     * @param contractSignDTO
     * @param contractDetailInfoDTO
     * @param tradeTicketDTO
     * @return
     */
    private TemplateConditionDTO assemblyTemplateCondition(ContractSignEntity contractSignDTO, ContractDetailInfoDTO contractDetailInfoDTO, TradeTicketDTO tradeTicketDTO) {
        ContractAddTTDTO addTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractAddTTDTO();
        ContractStructurePriceAddDTO structurePriceAddDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractStructurePriceAddDTO();
        TTPriceEntity ttPriceEntity = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTTPriceDTO();
        ContractTransferTTDTO transferTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractTransferTTDTO();
        ContractModifyTTDTO modifyTTDTO = null == tradeTicketDTO ? null : tradeTicketDTO.getContractModifyTTDTO();
        TemplateConditionDTO templateCondition = new TemplateConditionDTO()
                .setContractType(null == tradeTicketDTO ? null : tradeTicketDTO.getContractType())
                .setGoodsCategoryId(null == tradeTicketDTO ? null : tradeTicketDTO.getSubGoodsCategoryId())
                .setCategory1(null == tradeTicketDTO ? null : tradeTicketDTO.getCategory1())
                .setCategory2(null == tradeTicketDTO ? null : tradeTicketDTO.getCategory2())
                .setCategory3(null == tradeTicketDTO ? null : tradeTicketDTO.getCategory3())
                .setSalesType(null == tradeTicketDTO ? null : tradeTicketDTO.getSalesType())
                .setActionType(null == tradeTicketDTO ? null : tradeTicketDTO.getContractSource())
                //全部反点价：尾量终止
                .setSignType((null == tradeTicketDTO || TTTypeEnum.REVERSE_PRICE.getType().equals(tradeTicketDTO.getType())) ? 1 : contractSignDTO.getSignType())
                .setProtocolTypeCondition(null == contractSignDTO ? "" : ProtocolTypeEnum.getByCode(contractSignDTO.getFrameProtocolType()).getValue())
                .setEnterpriseCode("")
//                .setDeliveryFactoryCode(contractSignDTO.getDeliveryFactoryCode())
                .setStructureType(null == structurePriceAddDTO ? 1 : structurePriceAddDTO.getStructureType())
                .setSettleType(null == contractDetailInfoDTO.getSettleType() ? 0 : Integer.valueOf(contractDetailInfoDTO.getSettleType()))
                //定价未定价量
                .setNotPriceNum(Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()).contains(tradeTicketDTO.getType()) && null != ttPriceEntity ? ttPriceEntity.getRemainPriceNum() : BigDecimal.ZERO)
                .setModifyList(new ArrayList<>());

        // 新增
        if (TTTypeEnum.getTtAddTypeList().contains(tradeTicketDTO.getType()) && null != addTTDTO) {
            templateCondition.setSpecId(null == addTTDTO ? null : addTTDTO.getGoodsSpecId())
                    .setDeliveryType(null == addTTDTO ? null : getDeliveryType(addTTDTO.getDeliveryType()))
                    .setPaymentType(null == addTTDTO ? null : addTTDTO.getPaymentType())
                    .setPriceEndType(null == addTTDTO ? null : addTTDTO.getPriceEndType())
                    .setDepositAmount(null == addTTDTO ? null : addTTDTO.getDepositRate())
                    .setAddedDepositRate((null == addTTDTO || null == addTTDTO.getAddedDepositRate()) ? 0 : addTTDTO.getAddedDepositRate())
                    .setInvoicePaymentRate((null == addTTDTO || null == addTTDTO.getInvoicePaymentRate()) ? 0 : addTTDTO.getInvoicePaymentRate())
                    .setDepositPaymentType(null == addTTDTO ? null : addTTDTO.getDepositPaymentType());
        } else if (null != modifyTTDTO) {
            //修改、拆分
            TTModifyEntity newContractTT = modifyTTDTO;
            if (Arrays.asList(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.SPLIT_NORMAL.getValue()).contains(tradeTicketDTO.getTradeType())
                    && TTTypeEnum.SPLIT.getType().equals(tradeTicketDTO.getType())) {
                newContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), modifyTTDTO.getId());
            }
            TradeTicketEntity newTradeTicketEntity = ttQueryService.getByTtId(newContractTT.getTtId());

            if (null != newTradeTicketEntity && null != newContractTT) {
                templateCondition
                        .setSalesType(newTradeTicketEntity.getSalesType())
                        .setSpecId(newContractTT.getGoodsSpecId())
//                    .setContractType(newContractTT.getContractType())
                        .setDeliveryType(getDeliveryType(newContractTT.getDeliveryType()))
                        .setPaymentType(newContractTT.getPaymentType())
//                        .setDeliveryFactoryCode(newContractTT.getDeliveryFactoryCode())
                        .setPriceEndType(newContractTT.getPriceEndType())
                        .setDepositReleaseType(newContractTT.getDepositReleaseType())
                        .setDepositAmount(newContractTT.getDepositRate())
                        .setAddedDepositRate(null == newContractTT.getAddedDepositRate() ? 0 : newContractTT.getAddedDepositRate())
                        .setActionType(tradeTicketDTO.getContractSource())
                        .setInvoicePaymentRate(null == newContractTT.getInvoicePaymentRate() ? 0 : newContractTT.getInvoicePaymentRate());
                if (!TTTypeEnum.SPLIT.getType().equals(newContractTT.getType())) {
                    // 非拆分
                    templateCondition.setContractType(newContractTT.getContractType());
                }
            }
        } else {
//        else if(TTTypeEnum.needContractInfoTtTypeList().contains(tradeTicketDTO.getType())) {
            templateCondition.setSpecId(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getGoodsSpecId())
                    .setDeliveryType(null == contractDetailInfoDTO ? null : getDeliveryType(contractDetailInfoDTO.getDeliveryType()))
                    .setPaymentType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getPaymentType())
                    .setPriceEndType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getPriceEndType())
                    .setDepositReleaseType(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getDepositReleaseType())
                    .setDepositAmount(null == contractDetailInfoDTO ? null : contractDetailInfoDTO.getDepositRate())
                    .setAddedDepositRate((null == contractDetailInfoDTO || null == contractDetailInfoDTO.getAddedDepositRate()) ? 0 : contractDetailInfoDTO.getAddedDepositRate())
                    .setInvoicePaymentRate((null == contractDetailInfoDTO || null == contractDetailInfoDTO.getInvoicePaymentRate()) ? 0 : contractDetailInfoDTO.getInvoicePaymentRate());
        }
        if (ContractSalesTypeEnum.PURCHASE.getValue() == templateCondition.getSalesType()) {
            templateCondition.setProtocolTypeCondition(ProtocolTypeEnum.CONTRACT.getValue());
        }
        if (BuCodeEnum.WT.getValue().equals(tradeTicketDTO.getBuCode())
                && TTTypeEnum.needContractToWarrantTemplate().contains(tradeTicketDTO.getType())) {
            templateCondition.setProtocolTypeCondition(ProtocolTypeEnum.CONTRACT.getValue());
        }
        templateCondition.setDepositReleaseType(null != modifyTTDTO ? modifyTTDTO.getDepositReleaseType() : contractDetailInfoDTO.getDepositReleaseType());
        if (null == templateCondition.getPriceEndType()) {
            templateCondition.setPriceEndType(contractDetailInfoDTO.getPriceEndType());
        }
        //解约定赔TTAdd该字段为0
        return templateCondition;
    }

    public void buildContentInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TemplateConditionDTO templateConditionDTO, String contentInfo, Integer ttType) {
        try {
            List<CompareObjectDTO> contentObjectDTOList = JSON.parseArray(contentInfo, CompareObjectDTO.class);
            BigDecimal unitPrice = BigDecimal.ZERO;
            BigDecimal afterTotalAmount = BigDecimal.ZERO;
            for (CompareObjectDTO i : contentObjectDTOList) {
                if (("taxRate").equals(i.getName())) {
                    BigDecimal afterModifyTaxRate = new BigDecimal(i.getAfter()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    signHuskyTemplateDTO.setTaxRateModify(afterModifyTaxRate);
                }
                if (("unitPrice").equals(i.getName())) {
                    unitPrice = new BigDecimal(i.getBefore()).setScale(3, BigDecimal.ROUND_HALF_UP);
                }
                if (("totalAmount").equals(i.getName())) {
                    afterTotalAmount = new BigDecimal(i.getAfter()).setScale(6, BigDecimal.ROUND_HALF_UP);
                    signHuskyTemplateDTO.setTotalAmountModify(afterTotalAmount);
                }
                if (("contractType").equals(i.getName())) {
                    templateConditionDTO.setOriginalContractType(Integer.valueOf(i.getBefore()));
                    if (!TTTypeEnum.SPLIT.getType().equals(ttType)) {
                        templateConditionDTO.setContractType(Integer.valueOf(i.getAfter()));
                    }
                }
                //modify中规格是否变更特殊处理，查询spu是否有变化
                if (("goodsId").equals(i.getName())) {
                    Integer oldGoodsId = Integer.valueOf(i.getBefore());
                    Integer newGoodsId = Integer.valueOf(i.getAfter());
                    SkuEntity oldGoods = skuFacade.getSkuById(oldGoodsId);
                    SkuEntity newGoods = skuFacade.getSkuById(newGoodsId);
                    List<String> modifyList = templateConditionDTO.getModifyList();
                    if (!oldGoodsId.equals(newGoodsId)) {
                        modifyList.add("goodsId");
                    }
                    if (!oldGoods.getSpuId().equals(newGoods.getSpuId())) {
                        modifyList.add("goodsSpecId");
                    }
                    if (!oldGoods.getPackageId().equals(newGoods.getPackageId())) {
                        modifyList.add("goodsPackageId");
                    }
                    templateConditionDTO.setModifyList(modifyList);
                }
            }
            //除了修改、拆分（出在原合同上），其他出在新合同上，取before
            if (!Arrays.asList(TTTypeEnum.REVISE.getType(), TTTypeEnum.SPLIT.getType()).contains(ttType)) {
                afterTotalAmount = unitPrice.multiply(new BigDecimal(signHuskyTemplateDTO.getOriginalContractNum()).setScale(2, RoundingMode.HALF_UP)).setScale(6, RoundingMode.HALF_UP);
                signHuskyTemplateDTO.setTotalAmountModify(afterTotalAmount);
            }
            //含税总金额
            signHuskyTemplateDTO.setTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(signHuskyTemplateDTO.getTotalAmountModify(), 2, RoundingMode.HALF_UP));
            //不含税总金额
            signHuskyTemplateDTO.setNoTaxTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(signHuskyTemplateDTO.getTotalAmountModify().divide(signHuskyTemplateDTO.getTaxRateModify().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP));
            //增值税总金额
            signHuskyTemplateDTO.setAddedTaxTotalAmountInfoModify(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(signHuskyTemplateDTO.getTotalAmountInfoModify()).subtract(new BigDecimal(signHuskyTemplateDTO.getNoTaxTotalAmountInfoModify())), 2, RoundingMode.HALF_UP));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public TemplateConditionDTO buildModifyList(SignHuskyTemplateDTO signHuskyTemplateDTO, TemplateConditionDTO templateConditionDTO,
                                                String modifyContent, Integer ttType) {
        List<String> modifyList = new ArrayList<>();
        int transferFactory = TransferFactoryEnum.NOT_INVOLVE.getValue();
        int splitType = SplitTypeEnum.ALL_SPLIT.getValue();

        try {
            List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(modifyContent, CompareObjectDTO.class);

            for (CompareObjectDTO i : compareObjectDTOList) {
                modifyList.add(i.getName());
                if (("deliveryFactoryCode").equals(i.getName())) {
                    transferFactory = TransferFactoryEnum.INVOLVE.getValue();
                }
                //追加履约保证金 原合同字段为空 则不加入规则
                if (("addedDepositRate2").equals(i.getName()) && StringUtils.isBlank(i.getBefore())) {
                    modifyList.remove(i.getName());
                }
                if (("depositAmount").equals(i.getName())) {
                    modifyList.remove(i.getName());
                }
                //履约保证金比例的问题
                if (("depositRate").equals(i.getName())) {
                    modifyList.add("depositAmount");
                }
                if (("deliveryType").equals(i.getName())) {
                    modifyList.remove("deliveryType");
                    //提货方式发生变化
                    modifyList.add("deliveryTypeInfo");
                    DeliveryTypeEntity beforeDeliveryType = deliveryTypeService.getDeliveryTypeById(Integer.valueOf(i.getBefore()));
                    DeliveryTypeEntity afterDeliveryType = deliveryTypeService.getDeliveryTypeById(Integer.valueOf(i.getAfter()));
                    if (!beforeDeliveryType.getType().equals(afterDeliveryType.getType())) {
                        // 提货方式对应的类型发生变化
                        modifyList.add("deliveryType");
                        modifyList.add("deliveryMode");
                    }
                }
                if (ttType.equals(TTTypeEnum.SPLIT.getType())) {
                    if (("contractNum").equals(i.getName()) && !i.getAfter().equals(i.getBefore())) {
                        splitType = SplitTypeEnum.PART_SPLIT.getValue();
                    }
                }
                if (("taxRate").equals(i.getName())) {
                    BigDecimal afterModifyTaxRate;
                    if (TTTypeEnum.REVISE.getType().equals(ttType)) {
                        afterModifyTaxRate = new BigDecimal(i.getAfter()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else {
                        afterModifyTaxRate = new BigDecimal(i.getBefore()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    signHuskyTemplateDTO.setTaxRateModify(afterModifyTaxRate);
                }
                if (("totalAmount").equals(i.getName())) {
                    BigDecimal afterTotalAmount;
                    if (TTTypeEnum.REVISE.getType().equals(ttType)) {
                        afterTotalAmount = new BigDecimal(i.getAfter()).setScale(6, BigDecimal.ROUND_HALF_UP);
                    } else {
                        afterTotalAmount = new BigDecimal(i.getBefore()).subtract(new BigDecimal(i.getAfter())).setScale(6, BigDecimal.ROUND_HALF_UP);
                    }
                    signHuskyTemplateDTO.setTotalAmountModify(afterTotalAmount);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String goodsSpecName = "goodsSpecId";
        if (modifyList.contains(goodsSpecName)) {
            modifyList.remove(goodsSpecName);
        }
        String goodsPackageName = "goodsPackageId";
        if (modifyList.contains(goodsPackageName)) {
            modifyList.remove(goodsPackageName);
        }
        String goodsName = "goodsId";
        if (modifyList.contains(goodsName)) {
            modifyList.remove(goodsName);
        }
        templateConditionDTO.setModifyList(modifyList)
                .setTransferFactory(transferFactory)
                .setSplitType(ttType.equals(TTTypeEnum.TRANSFER.getType()) || ttType.equals(TTTypeEnum.REVERSE_PRICE.getType()) ? templateConditionDTO.getSplitType() : splitType);

        return templateConditionDTO;
    }

    /**
     * 协议信息拼装
     *
     * @param signHuskyTemplateDTO
     * @param contractSignDTO
     */
    private void buildContractSignInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractSignEntity contractSignDTO) {
        TemplateConditionDTO templateCondition = signHuskyTemplateDTO.getTemplateCondition();
        //获取二维码
        String qrCodeImage = "";
        String barCodeImage = "";
        String sasToken = "";

        qrCodeImage = StringUtils.isNotBlank(contractSignDTO.getQrCodeImage()) ? contractSignDTO.getQrCodeImage() :
                fileProcessFacade.generateQrCodeImg(qrCodeUrl + contractSignDTO.getId()).getFileUrl();
        //获取条形码
        barCodeImage = StringUtils.isNotBlank(contractSignDTO.getBarCodeImage()) ? contractSignDTO.getBarCodeImage() :
                fileProcessFacade.generateBarCodeImg(contractSignDTO.getContractCode(), "").getFileUrl();
        sasToken = azureBlobUtil.getSharedAccessSignature();
        //todo:二维码+token
        signHuskyTemplateDTO.setQrCodeImage(qrCodeImage + sasToken);
        signHuskyTemplateDTO.setBarCodeImage(barCodeImage + sasToken);
        signHuskyTemplateDTO.setSignProtocolCode(contractSignDTO.getProtocolCode());
        String salesType = ContractSalesTypeEnum.getDescByValue(contractSignDTO.getSalesType());
        String protocolType = BuCodeEnum.ST.getValue().equals(contractSignDTO.getBuCode()) ? CustomerProtocolTypeEnum.getShortDescByValue(contractSignDTO.getFrameProtocolType()) :
                CustomerProtocolTypeEnum.LDC_CONTRACT.getShortDesc();
        String contractTitle = "";
        if (BuCodeEnum.WT.getValue().equals(contractSignDTO.getBuCode()) &&
                Arrays.asList(TTTypeEnum.PRICE.getType(), TTTypeEnum.FIXED.getType()).contains(contractSignDTO.getTtType())) {
            if (ContractSalesTypeEnum.SALES.getValue() == contractSignDTO.getSalesType()) {
                contractTitle = "仓单转让合同";
            } else {
                contractTitle = "仓单采购合同";
            }
        } else {
            contractTitle = salesType + protocolType;
        }
        signHuskyTemplateDTO.setContractTitle(contractTitle);
        signHuskyTemplateDTO.setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(contractSignDTO.getSalesType()))
                .setProtocolTypeInfo(ProtocolTypeEnum.ORDER.getCode().equals(contractSignDTO.getFrameProtocolType()) ? ProtocolTypeEnum.ORDER.getDesc() : ProtocolTypeEnum.CONTRACT.getDesc());
        signHuskyTemplateDTO.setCategoryName1(GoodsCategoryEnum.getDesc(contractSignDTO.getCategory1()))
                .setCategoryName2(GoodsCategoryEnum.getDesc(contractSignDTO.getCategory2()))
                .setCategoryName3(GoodsCategoryEnum.getDesc(contractSignDTO.getCategory3()));
        //豆油-销售-基差（付款条款变更，取300） 其他：150
        Integer lytext = TemplateConstant.SBM_DEPOSIT_AMOUNT;
        if (Arrays.asList(GoodsCategoryEnum.OSM_OIL.getValue(), GoodsCategoryEnum.SPECIAL_OIL.getValue()).contains(templateCondition.getCategory2()) && ContractSalesTypeEnum.SALES.getValue() == templateCondition.getSalesType()
                && ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
            lytext = TemplateConstant.SBO_SALE_DIFF_DEPOSIT_AMOUNT;
        }
        String depositUseRuleName = "";
        //保证金使用规则描述（option1/option2） 区分采销，保证金释放方式
        if (DepositUseRuleEnum.RATIO.getValue() == templateCondition.getDepositReleaseType()) {
            depositUseRuleName = ContractSalesTypeEnum.SALES.getValue() == templateCondition.getSalesType() ?
                    TemplateConstant.DePOSIT_USE_RULE_RATIO_OPTION2_SALE : TemplateConstant.DePOSIT_USE_RULE_RATIO_OPTION2_PURCHASE;
        } else {
            depositUseRuleName = TemplateConstant.DePOSIT_USE_RULE_LAST_OPTION1;
        }
        signHuskyTemplateDTO.setDepositText(lytext)
                .setDepositUseRuleName(depositUseRuleName);

    }

    private void buildContractInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractDetailInfoDTO contractDetailInfoDTO) {
        BigDecimal contractUnitPrice = contractDetailInfoDTO.getUnitPrice();
        BigDecimal totalBillNum = contractDetailInfoDTO.getTotalBillNum();
        BigDecimal totalDeliveryNum = contractDetailInfoDTO.getTotalDeliveryNum();
        BigDecimal contractRemainNum = contractDetailInfoDTO.getContractNum().subtract(totalBillNum);
        //合同基本信息========================================
        //合同编号
        signHuskyTemplateDTO.setContractCode(contractDetailInfoDTO.getContractCode())
                // 品种简称(豆粕：M; 豆油：Y)
                .setGoodsCategorySymbol(contractDetailInfoDTO.getFutureCode())
                // 品种全称(豆粕：SBM;豆油：SBO)
                .setGoodsCategoryCode(GoodsCategoryEnum.getByValue(contractDetailInfoDTO.getCategory2()).getCode())
                // 合同类型
                .setContractTypeInfo(contractDetailInfoDTO.getContractTypeName())
                // 签约日期
                .setSignDate(DateTimeUtil.formatDateStringCN(contractDetailInfoDTO.getSignDate()))
                // 合同吨数
                .setContractNum(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP))
                // 期货合约,示例文本：2022年05月
                .setDomainCodeInfo(getDomainCodeCN(contractDetailInfoDTO.getDomainCode()))
                // 期货合约,示例文本：2205
                .setDomainCode(contractDetailInfoDTO.getDomainCode())
                .setStandardFileId(contractDetailInfoDTO.getStandardFileId())
                .setSettleTypeInfo(StringUtils.isBlank(contractDetailInfoDTO.getSettleType()) ? "" : SettleType.getDescByValue(Integer.valueOf(contractDetailInfoDTO.getSettleType())))
        ;
        // 期货合约,示例文本：M2205
        signHuskyTemplateDTO.setCategoryDomainCode(signHuskyTemplateDTO.getGoodsCategorySymbol() + signHuskyTemplateDTO.getDomainCode());
        // 点价截止日期
        signHuskyTemplateDTO.setPriceEndTime(getPriceEndTimeCN(contractDetailInfoDTO.getPriceEndType(), contractDetailInfoDTO.getPriceEndTime()))
                // 点价截止日期(含)
                .setPriceEndTimeContains(getPriceEndTimeContains(contractDetailInfoDTO.getPriceEndType(), contractDetailInfoDTO.getPriceEndTime()))
                // 点价后补缴比例:5%
                .setAddedDepositRateInfo(getRateString(contractDetailInfoDTO.getAddedDepositRate()))
                .setRemainBillNum(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum().subtract(totalBillNum), 3, RoundingMode.HALF_UP))
                .setRemainDeliveryNum(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractNum().subtract(totalDeliveryNum), 3, RoundingMode.HALF_UP))
                //品种
                .setGoodsCategoryName(contractDetailInfoDTO.getGoodsCategoryName())
                //蛋白（规格）
                .setGoodsSpecId(contractDetailInfoDTO.getGoodsName())
                //交货工厂
                .setDeliveryFactoryName(contractDetailInfoDTO.getDeliveryFactoryName())
                //溢短装
                .setWeightTolerance(contractDetailInfoDTO.getWeightTolerance() + "%")
                //包装计算重量
                .setPackageWeight(getPackageWeightInfo(contractDetailInfoDTO.getPackageWeight(), contractDetailInfoDTO.getCategory2()))
                //合同-付款金额信息========================================
                //付款方式
                .setPaymentTypeInfo(contractDetailInfoDTO.getPaymentTypeName())
                //赊销账期
                .setCreditDays(contractDetailInfoDTO.getCreditDays())
                //保证金比例
                .setDepositRateInfo(getRateString(contractDetailInfoDTO.getDepositRate()))
                //追加保证金比例
//                .setJointAddedDepositRate(getRateString(getAddRateByRule(contractDetailInfoDTO.getDepositRate(), contractDetailInfoDTO.getAddedDepositRate(), contractDetailInfoDTO.getGoodsCategoryId(), contractDetailInfoDTO.getContractType())))
                .setJointAddedDepositRate(getRateString(contractDetailInfoDTO.getAddedDepositRate2()))
                //付款截止日期
                .setDepositPayEndDay(getDepositPayEndDayCN(contractDetailInfoDTO.getSignDate()))
                .setWriteOffStartTime(getWriteOffDateCN(contractDetailInfoDTO.getWriteOffStartTime()))
                .setWriteOffEndTime(getWriteOffDateCN(contractDetailInfoDTO.getWriteOffEndTime()))
                .setTaxRateModify(contractDetailInfoDTO.getTaxRate())
                .setTotalAmountModify(contractDetailInfoDTO.getTotalAmount())
                //含税单价
                .setUnitPrice(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP))
                //含税总金额
                .setTotalAmount(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getTotalAmount(), 2, RoundingMode.HALF_UP))
                //不含税总金额  todo
                .setNoTaxTotalAmount(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getTotalAmount().divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP));
        //增值税总金额
        signHuskyTemplateDTO.setAddedTaxTotalAmount(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(signHuskyTemplateDTO.getTotalAmount()).subtract(new BigDecimal(signHuskyTemplateDTO.getNoTaxTotalAmount())), 2, RoundingMode.HALF_UP));
        //目的地
        signHuskyTemplateDTO.setDestination(getDestinationInfo(contractDetailInfoDTO.getDestination(), contractDetailInfoDTO.getSalesType(), signHuskyTemplateDTO.getTemplateCondition()))
                .setOriginalDestination((getDestinationForWeight(contractDetailInfoDTO.getDestination())))
                //重量验收标准
                .setWeightCheck(getWeightCheckInfo(contractDetailInfoDTO.getWeightCheck(), signHuskyTemplateDTO.getOriginalDestination()))
                //交提货方式
                .setDeliveryTypeInfo(DeliveryModeEnum.getDescByValue(getDeliveryType(contractDetailInfoDTO.getDeliveryType())))
                //交货周期
                .setDeliveryTime(getDeliveryInfo(contractDetailInfoDTO.getDeliveryStartTime(), contractDetailInfoDTO.getDeliveryEndTime()))
        ;
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //基差价
            String extraPrice = (BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getExtraPrice(), 2, RoundingMode.HALF_UP));
            signHuskyTemplateDTO.setExtraPrice(extraPrice);
            if (contractDetailInfoDTO.getContractPriceDTO().getExtraPrice().compareTo(BigDecimal.ZERO) < 0) {
                signHuskyTemplateDTO.setExtraPrice("(" + extraPrice + ")");
            }
        }
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //含税单价明细
            signHuskyTemplateDTO.setUnitPriceDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType(), false))
                    //含税单价明细(含基差价)
                    .setUnitPriceDiffDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType(), true))
                    //手续费
                    .setThisTimeFee(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getFee(), 2, RoundingMode.HALF_UP))
                    //含税单价中的运费
                    .setTransportPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE单价
                    .setVePrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVePrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE含量
                    .setVeContent(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVeContent(), 2, RoundingMode.HALF_UP))
                    //赊销利息
                    .setBusinessPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getBusinessPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价
                    .setComplaintDiscountPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价总金额
                    .setComplaintDiscountAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice()), 2, RoundingMode.HALF_UP))
                    //滞期费
                    .setDelayPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice(), 2, RoundingMode.HALF_UP))
                    //滞期费总金额
                    .setDelayAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice()), 2, RoundingMode.HALF_UP));
            //转月含税单价明细
            //signTemplateDTO.setPrxzy(getOtherPriceInfoZY(contractDetailInfoDTO.getContractPriceDTO(), contractDetailInfoDTO.getContractType()));
            //合同-其他（固定值）========================================
        }
        //=========================factory处理区==============================
        buildFactoryInfo(signHuskyTemplateDTO, contractDetailInfoDTO.getShipWarehouseId(), contractDetailInfoDTO.getDeliveryFactoryCode());
        if (ContractTypeEnum.STRUCTURE.getValue() != contractDetailInfoDTO.getContractType()) {
            //匹配质量指标信息
            QualityInfoDTO qualityInfoDTO = new QualityInfoDTO()
                    .setGoodsCategoryId(contractDetailInfoDTO.getCategory2())
                    .setFactoryCode(contractDetailInfoDTO.getDeliveryFactoryCode())
                    .setWarehouseId(contractDetailInfoDTO.getShipWarehouseId())
                    .setUsage(BuCodeEnum.WT.getValue().equals(contractDetailInfoDTO.getBuCode()) ? UsageEnum.DELIVERY.getValue() : contractDetailInfoDTO.getUsage())
                    .setStandardType(contractDetailInfoDTO.getStandardType())
//                .setSpecId(contractDetailInfoDTO.getGoodsSpecId())
                    .setSalesType(contractDetailInfoDTO.getSalesType())
                    .setGoodsId(contractDetailInfoDTO.getGoodsId())
                    .setCustomerId(ContractSalesTypeEnum.SALES.getValue() == contractDetailInfoDTO.getSalesType() ?
                            contractDetailInfoDTO.getCustomerId() : contractDetailInfoDTO.getSupplierId());
            this.fillQualityInfo(signHuskyTemplateDTO, qualityInfoDTO);
            //以上期限条款文本(交货截止日期 < 合同签约日期）
            String aboveDeadlineInfo = contractDetailInfoDTO.getDeliveryEndTime().before(contractDetailInfoDTO.getSignDate()) ? TemplateConstant.ABOVE_DEADLINE_INFO : "";
            signHuskyTemplateDTO.setAboveDeadlineInfo(aboveDeadlineInfo);
            //正大集团-TT备注处理
            signHuskyTemplateDTO.setSpecialCustomerDeliveryInfo(processZDJTDeliveryByMemo(contractDetailInfoDTO.getDeliveryFactoryCode(), contractDetailInfoDTO.getMemo(), signHuskyTemplateDTO.getTemplateCondition()));
        }
    }

    /**
     * 原合同信息处理
     *
     * @param signHuskyTemplateDTO
     * @param sourceContractDetailInfoDTO
     */
    private void buildSourceContractInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractDetailInfoDTO sourceContractDetailInfoDTO) {

        if (null != sourceContractDetailInfoDTO) {
            //父合同编号
            signHuskyTemplateDTO.setOriginalContractCode(sourceContractDetailInfoDTO.getContractCode());
            //原合同签约日期
            signHuskyTemplateDTO.setOriginalSignDate(DateTimeUtil.formatDateStringCN(sourceContractDetailInfoDTO.getSignDate()));
            //原合同剩余数量,0, RoundingMode.HALF_UP)
            signHuskyTemplateDTO.setSourceContractNum(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP));
            // 原合同剩余数量(合同总量-拆分数量)
            signHuskyTemplateDTO.setOriginalContractNum(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractNum(), 3, RoundingMode.HALF_UP));
            //修改前含税单价
            signHuskyTemplateDTO.setOriginalTTUnitPrice(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getUnitPrice(), 2, RoundingMode.HALF_UP));
            //原合同签约日期
            signHuskyTemplateDTO.setOriginalPackageWeight(getPackageWeightInfo(sourceContractDetailInfoDTO.getPackageWeight(), sourceContractDetailInfoDTO.getCategory2()));
            //原合同交货工厂
            signHuskyTemplateDTO.setOriginalDeliveryFactoryName(sourceContractDetailInfoDTO.getDeliveryFactoryName());
            //原合同溢短装
            signHuskyTemplateDTO.setOriginalWeightTolerance(sourceContractDetailInfoDTO.getWeightTolerance() + "%");
            //原合同含税单价
            signHuskyTemplateDTO.setOriginalUnitPrice(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getUnitPrice(), 2, RoundingMode.HALF_UP));
            if (null != sourceContractDetailInfoDTO.getContractPriceDTO()) {
                //原合同含税单价中的运费
                signHuskyTemplateDTO.setOriginalTransportPrice(BigDecimalUtil.formatBigDecimalZero(sourceContractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP));
            }
        }
    }

    /**
     * 设置客户相关信息
     * 销售时：customerId取自于合同的customerId
     * 采购时：customerId取自于合同的SupplierId
     *
     * @param signHuskyTemplateDTO
     * @param tradeTicketDTO
     */
    private void buildCustomerInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TradeTicketDTO tradeTicketDTO, String factoryCode) {
        //达孚的客户
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketDTO.getSalesType() ? tradeTicketDTO.getCustomerId() : tradeTicketDTO.getSupplierId();
//        String factoryCode = signHuskyTemplateDTO.getTemplateCondition().getDeliveryFactoryCode();
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(factoryCode);
        Integer factoryId = null != factoryEntity ? factoryEntity.getId() : null;
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO().setCustomerId(customerId)
                .setCategoryId(tradeTicketDTO.getSubGoodsCategoryId())
                .setSalesType(tradeTicketDTO.getSalesType())
                .setFactoryCode(factoryCode)
                .setCompanyId(tradeTicketDTO.getCompanyId())
                .setFactoryId(factoryId)
                .setCategory2(String.valueOf(tradeTicketDTO.getCategory2()))
                .setCategory3(String.valueOf(tradeTicketDTO.getCategory3()));
        // 结构化定价（只有2级品类 无品种） 通知人需要区分3级品种 特殊处理
//        if (TTTypeEnum.STRUCTURE_PRICE.getType().equals(tradeTicketDTO.getType())) {
//            List<Integer> category3List = categoryFacade.queryCategorySerialNoList(new CategoryQO()
//                    .setParentSerialNo(tradeTicketDTO.getCategory2())
//                    .setFutureCode(tradeTicketDTO.getFutureCode())
//                    .setStatus(DisableStatusEnum.ENABLE.getValue()));
//            customerAllMessageDTO.setCategory3List(category3List);
//        }
        log.info("5、出具客户信息获取-出具拼装===============" + tradeTicketDTO.getSignId() + FastJsonUtils.getBeanToJson(customerAllMessageDTO));
        CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        log.info("5、出具结果-客户信息获取-出具拼装===============" + tradeTicketDTO.getSignId() + FastJsonUtils.getBeanToJson(customerDTO));
//        CustomerTemplateVO customerTemplateVO = customerFacade.queryTemplateContactFactoryByCustomerId(customerId, goodsCategoryId);
//        log.info("===========>customer:{},goodsCategoryId:{}", customerId, tradeTicketDTO.getSubGoodsCategoryId());
//        log.info("===========>customerTemplateVO:{}", JSON.toJSONString(customerDTO));
        ContactEntity contactDTO = null;
        if (CollectionUtils.isEmpty(customerDTO.getContactDTO())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_CONTACT, customerDTO.getName() + "-工厂" + factoryEntity.getShortName() + "-"
                    + signHuskyTemplateDTO.getCategoryName2() + "/" + signHuskyTemplateDTO.getCategoryName3() + ContractSalesTypeEnum.getDescByValue(tradeTicketDTO.getSalesType()));
        }
        if (customerDTO != null) {
            try {
                signHuskyTemplateDTO.setCustomerName(customerDTO.getName())
                        .setSupplierAddress(customerDTO.getAddress());
                //bugfix:数字合同取值优化：合同拆分变更主体后，原合同补充协议中新公司主体名称取值不对
                if (StringUtils.isBlank(signHuskyTemplateDTO.getCustomerSplitName())) {
                    signHuskyTemplateDTO.setCustomerSplitName(customerDTO.getName());
                }
                // 集团客户信息
                signHuskyTemplateDTO.getTemplateCondition().setEnterpriseCode(null == customerDTO.getTemplateVipCode() ? "" : customerDTO.getTemplateVipCode());
                List<String> allName = new ArrayList<>();
                List<String> allEmail = new ArrayList<>();
                List<String> allPhone = new ArrayList<>();
                List<String> allAddress = new ArrayList<>();
                for (ContactEntity contactEntity : customerDTO.getContactDTO()) {
                    String nameItem = contactEntity.getContactName();
                    if (null != nameItem && !nameItem.equals("")) {
                        String[] split = nameItem.split(";");
                        allName.addAll(Arrays.asList(split));
                    }
                    String emailItem = contactEntity.getEmail();
                    if (null != emailItem && !emailItem.equals("")) {
                        String[] split = emailItem.split(";");
                        allEmail.addAll(Arrays.asList(split));
                    }
                    String phoneItem = contactEntity.getContactPhone();
                    if (null != phoneItem && !phoneItem.equals("")) {
                        String[] split = phoneItem.split(";");
                        allPhone.addAll(Arrays.asList(split));
                    }
                    String addressItem = contactEntity.getAddress();
                    if (null != addressItem && !addressItem.equals("")) {
                        String[] split = addressItem.split(";");
                        allAddress.addAll(Arrays.asList(split));
                    }
                }
                String contactNames = allName.stream().distinct().collect(Collectors.joining(";"));
                String contactEmails = allEmail.stream().distinct().collect(Collectors.joining(";"));
                String contactPhones = allPhone.stream().distinct().collect(Collectors.joining(";"));
                String contactAddresses = allAddress.stream().distinct().collect(Collectors.joining(";"));
                signHuskyTemplateDTO.setCustomerContactAddresses(contactAddresses)
                        .setCustomerContactName(contactNames)
                        .setCustomerContactEmails(contactEmails)
                        .setCustomerContactPhones(contactPhones);
                if (tradeTicketDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
                    List<CustomerBankDTO> bankDTOList = customerDTO.getCustomerBankDTOS();
                    if (CommonListUtil.notNullOrEmpty(bankDTOList)) {
                        String bankName = bankDTOList.stream().map(CustomerBankDTO::getBankName).distinct().collect(Collectors.joining(";"));
                        String bankAccountNo = bankDTOList.stream().map(CustomerBankDTO::getBankAccountNo).distinct().collect(Collectors.joining(";"));
                        String bankAccountName = bankDTOList.stream().map(CustomerBankDTO::getBankAccountName).distinct().collect(Collectors.joining(";"));

                        signHuskyTemplateDTO.setCustomerBankName(bankName)
                                .setCustomerBankAccountNo(bankAccountNo)
                                .setCustomerBankAccountName(bankAccountName);
                    } else {
                        signHuskyTemplateDTO.setCustomerBankName(WHEN_ERROR_BANK_NAME)
                                .setCustomerBankAccountNo(WHEN_ERROR_BANK_ACCOUNT_NO)
                                .setCustomerBankAccountName(WHEN_ERROR_BANK_ACCOUNT_NAME);
                    }
                }
                //设置客户的框架协议相关信息
                CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
                customerProtocolDTO.setCustomerId(customerId)
                        .setCategoryId(tradeTicketDTO.getSubGoodsCategoryId())
                        .setCompanyId(tradeTicketDTO.getCompanyId())
                        .setSaleType(tradeTicketDTO.getSalesType())
                        .setCategory2(String.valueOf(tradeTicketDTO.getCategory2()))
                        .setCategory3(String.valueOf(tradeTicketDTO.getCategory3()))
                ;
                CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
                if (null != customerProtocolEntity && null != customerProtocolEntity.getProtocolStartDate()) {
                    signHuskyTemplateDTO.setProtocolStartDate(DateTimeUtil.formatDateStringCN(customerProtocolEntity.getProtocolStartDate()))
                            .setProtocolNo(customerProtocolEntity.getProtocolNo());
                } else {
                    signHuskyTemplateDTO.setProtocolStartDate(WHEN_ERROR_PROTOCOL_START_DATE)
                            .setProtocolNo(WHEN_ERROR_PROTOCOL_NO);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void buildLdcSelfCustomerInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TradeTicketDTO tradeTicketDTO, String factoryCode) {
        //达孚自己
        Integer ldcSelfCustomerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketDTO.getSalesType() ? tradeTicketDTO.getSupplierId() : tradeTicketDTO.getCustomerId();
//        String factoryCode = signHuskyTemplateDTO.getTemplateCondition().getDeliveryFactoryCode();
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(factoryCode);
        Integer factoryId = null != factoryEntity ? factoryEntity.getId() : null;
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO().setCustomerId(ldcSelfCustomerId)
                .setCategoryId(tradeTicketDTO.getSubGoodsCategoryId())
                .setSalesType(tradeTicketDTO.getSalesType())
                .setFactoryCode(factoryCode)
                .setCompanyId(tradeTicketDTO.getCompanyId())
                .setFactoryId(factoryId)
                .setCategory2(String.valueOf(tradeTicketDTO.getCategory2()))
                .setCategory3(String.valueOf(tradeTicketDTO.getCategory3()));
        // 结构化定价（只有2级品类 无品种） 通知人需要区分3级品种 特殊处理
//        if (TTTypeEnum.STRUCTURE_PRICE.getType().equals(tradeTicketDTO.getType())) {
//            List<Integer> category3List = categoryFacade.queryCategorySerialNoList(new CategoryQO()
//                    .setParentSerialNo(tradeTicketDTO.getCategory2())
//                    .setFutureCode(tradeTicketDTO.getFutureCode())
//                    .setStatus(DisableStatusEnum.ENABLE.getValue()));
//            customerAllMessageDTO.setCategory3List(category3List);
//        }
        log.info("6、出具ldc客户信息获取-出具拼装===============" + tradeTicketDTO.getSignId() + FastJsonUtils.getBeanToJson(customerAllMessageDTO));
        CustomerDTO ldcSelfCustomerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        log.info("6、出具结果-ldc客户信息获取-出具拼装===============" + tradeTicketDTO.getSignId() + FastJsonUtils.getBeanToJson(ldcSelfCustomerDTO));
//        CustomerDTO ldcSelfCustomerDTO = customerFacade.getCustomerById(ldcSelfCustomerId);
//        CustomerTemplateVO supplierTemplateVO = customerFacade.queryTemplateContactFactoryByCustomerId(ldcSelfCustomerId, goodsCategoryId);
        //CustomerTemplateVO.ContactFactory contactFactory = null != supplierTemplateVO ? supplierTemplateVO.getContactFactories().get(0) : null;
        ContactEntity contactDTO = null;

        if (CollectionUtils.isEmpty(ldcSelfCustomerDTO.getContactDTO())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_CONTACT, ldcSelfCustomerDTO.getName() + "-工厂" + factoryEntity.getShortName() + "-"
                    + signHuskyTemplateDTO.getCategoryName2() + "/" + signHuskyTemplateDTO.getCategoryName3() + ContractSalesTypeEnum.getDescByValue(tradeTicketDTO.getSalesType()));
        }
        try {
            signHuskyTemplateDTO.setLdcSignPlace(ldcSelfCustomerDTO.getSignPlace());
            signHuskyTemplateDTO.setLdcName(ldcSelfCustomerDTO.getName())
                    .setLdcAddress(ldcSelfCustomerDTO.getAddress());

            List<String> allName = new ArrayList<>();
            List<String> allEmail = new ArrayList<>();
            List<String> allPhone = new ArrayList<>();
            List<String> allAddress = new ArrayList<>();
            for (ContactEntity contactEntity : ldcSelfCustomerDTO.getContactDTO()) {
                String nameItem = contactEntity.getContactName();
                if (null != nameItem && !nameItem.equals("")) {
                    String[] split = nameItem.split(";");
                    allName.addAll(Arrays.asList(split));
                }
                String emailItem = contactEntity.getEmail();
                if (null != emailItem && !emailItem.equals("")) {
                    String[] split = emailItem.split(";");
                    allEmail.addAll(Arrays.asList(split));
                }
                String phoneItem = contactEntity.getContactPhone();
                if (null != phoneItem && !phoneItem.equals("")) {
                    String[] split = phoneItem.split(";");
                    allPhone.addAll(Arrays.asList(split));
                }
                String addressItem = contactEntity.getAddress();
                if (null != addressItem && !addressItem.equals("")) {
                    String[] split = addressItem.split(";");
                    allAddress.addAll(Arrays.asList(split));
                }
            }
            String contactNames = allName.stream().distinct().collect(Collectors.joining(";"));
            String contactEmails = allEmail.stream().distinct().collect(Collectors.joining(";"));
            String contactPhones = allPhone.stream().distinct().collect(Collectors.joining(";"));
            String contactAddresses = allAddress.stream().distinct().collect(Collectors.joining(";"));

            signHuskyTemplateDTO.setLdcContactAddresses(contactAddresses);
            signHuskyTemplateDTO.setLdcContactNames(contactNames);
            signHuskyTemplateDTO.setLdcContactEmails(contactEmails);
            signHuskyTemplateDTO.setLdcContactPhones(contactPhones);
            List<CustomerBankDTO> bankDTOList = ldcSelfCustomerDTO.getCustomerBankDTOS();
            if (CommonListUtil.notNullOrEmpty(bankDTOList)) {
                CustomerBankDTO bankDTO = bankDTOList.get(0);

                String bankName = null == bankDTO || StringUtil.isEmpty(bankDTO.getBankName()) ? WHEN_ERROR_BANK_NAME : bankDTO.getBankName();
                String bankAccountNo = null == bankDTO || StringUtil.isEmpty(bankDTO.getBankAccountNo()) ? WHEN_ERROR_BANK_ACCOUNT_NO : bankDTO.getBankAccountNo();

                signHuskyTemplateDTO.setLdcBankName(bankName);
                signHuskyTemplateDTO.setLdcBankAccountNo(bankAccountNo);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private void buildFactoryInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, Integer shipWarehouseId, String factoryCode) {
        try {
            if (null != shipWarehouseId) {
                Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(shipWarehouseId);
                WarehouseEntity warehouseEntity = null;
                if (result.isSuccess()) {
                    warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                }
                signHuskyTemplateDTO.setWarehouseName(null != warehouseEntity ? warehouseEntity.getName() : WHEN_ERROR_WARESHOUSE);
                signHuskyTemplateDTO.setWarehouseAddress(null != warehouseEntity ? warehouseEntity.getAddress() : WHEN_ERROR_DELIVERY_ADDRESS);
                signHuskyTemplateDTO.setWarehouseDeliveryPoint(null != warehouseEntity ? warehouseEntity.getDeliveryPoint() : WHEN_ERROR_DELIVERY_POINT);
            }
            //国标/企标
            signHuskyTemplateDTO.setSymbolFactory(TemplateConstant.defaultDeliveryFactoryList.contains(factoryCode) ? "企标值" : "国标值");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void buildTTAddInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TradeTicketDTO tradeTicketDTO,
                                ContractAddTTDTO addTtDTO, ContractDetailInfoDTO contractDetailInfoDTO) {
        Integer tradeType = tradeTicketDTO.getTradeType();
        BigDecimal contractUnitPrice = addTtDTO.getUnitPrice();
        BigDecimal totalBillNum = contractDetailInfoDTO.getTotalBillNum();
        BigDecimal totalDeliveryNum = contractDetailInfoDTO.getTotalDeliveryNum();
        BigDecimal contractRemainNum = addTtDTO.getContractNum().subtract(totalBillNum);

        //合同基本信息========================================
        //合同编号
        signHuskyTemplateDTO.setContractCode(addTtDTO.getContractCode())
                // 品种简称(豆粕：M; 豆油：Y)
                .setGoodsCategorySymbol(addTtDTO.getFutureCode())
                // 品种全称(豆粕：SBM;豆油：SBO)
                .setGoodsCategoryCode(GoodsCategoryEnum.getByValue(tradeTicketDTO.getCategory2()).getCode())
                // 合同类型
                .setContractTypeInfo(addTtDTO.getContractTypeName())
                // 签约日期
                .setSignDate(DateTimeUtil.formatDateStringCN(addTtDTO.getSignDate()))
                // 合同吨数
                .setContractNum(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getContractNum(), 3, RoundingMode.HALF_UP))
                // 期货合约,示例文本：2022年05月
                .setDomainCodeInfo(getDomainCodeCN(addTtDTO.getDomainCode()))
                // 期货合约,示例文本：2205
                .setDomainCode(addTtDTO.getDomainCode());
        // 期货合约,示例文本：M2205
        signHuskyTemplateDTO.setCategoryDomainCode(signHuskyTemplateDTO.getGoodsCategorySymbol() + signHuskyTemplateDTO.getDomainCode());
        // 点价截止日期
        signHuskyTemplateDTO.setPriceEndTime(getPriceEndTimeCN(addTtDTO.getPriceEndType(), addTtDTO.getPriceEndTime()))
                // 点价截止日期(含)
                .setPriceEndTimeContains(getPriceEndTimeContains(addTtDTO.getPriceEndType(), addTtDTO.getPriceEndTime()))
                // 点价后补缴比例:5%
                .setAddedDepositRateInfo(getRateString(addTtDTO.getAddedDepositRate()))
                .setRemainBillNum(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getContractNum().subtract(totalBillNum), 3, RoundingMode.HALF_UP))
                .setRemainDeliveryNum(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getContractNum().subtract(totalDeliveryNum), 3, RoundingMode.HALF_UP))
                //蛋白（规格）
                .setGoodsSpecId(addTtDTO.getGoodsName())
                //交货工厂
                .setDeliveryFactoryName(addTtDTO.getDeliveryFactoryName())
                //溢短装
                .setWeightTolerance(addTtDTO.getWeightTolerance() + "%")
                //包装计算重量
                .setPackageWeight(getPackageWeightInfo(addTtDTO.getPackageWeight(), tradeTicketDTO.getCategory2()))
                //合同-付款金额信息========================================
                //付款方式
                .setPaymentTypeInfo(addTtDTO.getPaymentTypeName())
                //赊销账期
                .setCreditDays(addTtDTO.getCreditDays())
                //保证金比例
                .setDepositRateInfo(getRateString(addTtDTO.getDepositRate()))
                //追加保证金比例
//                .setJointAddedDepositRate(getRateString(getAddRateByRule(addTtDTO.getDepositRate(), addTtDTO.getAddedDepositRate(), addTtDTO.getGoodsCategoryId(), addTtDTO.getContractType())))
                .setJointAddedDepositRate(getRateString(addTtDTO.getAddedDepositRate2()))
                //付款截止日期
                .setDepositPayEndDay(getDepositPayEndDayCN(addTtDTO.getSignDate()))
                .setWriteOffStartTime(getWriteOffDateCN(addTtDTO.getWriteOffStartTime()))
                .setWriteOffEndTime(getWriteOffDateCN(addTtDTO.getWriteOffEndTime()))
                .setTaxRateModify(addTtDTO.getTaxRate())
                .setTotalAmountModify(addTtDTO.getTotalAmount())
                //含税单价
                .setUnitPrice(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP))
                //含税总金额
                .setTotalAmount(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getTotalAmount(), 2, RoundingMode.HALF_UP))
                //不含税总金额  todo
                .setNoTaxTotalAmount(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getTotalAmount().divide(addTtDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP));
        //增值税总金额
        signHuskyTemplateDTO.setAddedTaxTotalAmount(BigDecimalUtil.formatBigDecimalZero(new BigDecimal(signHuskyTemplateDTO.getTotalAmount()).subtract(new BigDecimal(signHuskyTemplateDTO.getNoTaxTotalAmount())), 2, RoundingMode.HALF_UP));
        //目的地
        signHuskyTemplateDTO.setDestination(getDestinationInfo(addTtDTO.getDestination(), tradeTicketDTO.getSalesType(), signHuskyTemplateDTO.getTemplateCondition()))
                .setOriginalDestination((getDestinationForWeight(addTtDTO.getDestination())))
                //重量验收标准
                .setWeightCheck(getWeightCheckInfo(addTtDTO.getWeightCheck(), signHuskyTemplateDTO.getOriginalDestination()))
                //交提货方式
                .setDeliveryTypeInfo(DeliveryModeEnum.getDescByValue(getDeliveryType(addTtDTO.getDeliveryType())))
                //交货周期
                .setDeliveryTime(getDeliveryInfo(addTtDTO.getDeliveryStartTime(), addTtDTO.getDeliveryEndTime()))
        ;
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //基差价
            String extraPrice = (BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getExtraPrice(), 2, RoundingMode.HALF_UP));
            signHuskyTemplateDTO.setExtraPrice(extraPrice);
            if (contractDetailInfoDTO.getContractPriceDTO().getExtraPrice().compareTo(BigDecimal.ZERO) < 0) {
                signHuskyTemplateDTO.setExtraPrice("(" + extraPrice + ")");
            }
        }
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //含税单价明细
            signHuskyTemplateDTO.setUnitPriceDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), addTtDTO.getContractType(), false))
                    //含税单价明细(含基差价)
                    .setUnitPriceDiffDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), addTtDTO.getContractType(), true))
                    //手续费
                    .setThisTimeFee(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getFee(), 2, RoundingMode.HALF_UP))
                    //含税单价中的运费
                    .setTransportPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE单价
                    .setVePrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVePrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE含量
                    .setVeContent(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVeContent(), 2, RoundingMode.HALF_UP))
                    //赊销利息
                    .setBusinessPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getBusinessPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价
                    .setComplaintDiscountPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价总金额
                    .setComplaintDiscountAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice()), 2, RoundingMode.HALF_UP))
                    //滞期费
                    .setDelayPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice(), 2, RoundingMode.HALF_UP))
                    //滞期费总金额
                    .setDelayAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice()), 2, RoundingMode.HALF_UP));
            //转月含税单价明细
            //signTemplateDTO.setPrxzy(getOtherPriceInfoZY(addTtDTO.getContractPriceDTO(), addTtDTO.getContractType()));
            //合同-其他（固定值）========================================
        }
        //国标/企标
        String factoryCode = addTtDTO.getDeliveryFactoryCode();
        signHuskyTemplateDTO.setSymbolFactory(TemplateConstant.defaultDeliveryFactoryList.contains(factoryCode) ? "企标值" : "国标值");
        //=========================factory处理区==============================
        buildFactoryInfo(signHuskyTemplateDTO, addTtDTO.getShipWarehouseId(), addTtDTO.getDeliveryFactoryCode());
        if (tradeType == ContractTradeTypeEnum.CLOSED.getValue()) {
            signHuskyTemplateDTO.setRemainBillNum(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getContractNum(), 3, RoundingMode.HALF_UP));
        }
        if (Arrays.asList(ContractTradeTypeEnum.WASHOUT.getValue(), ContractTradeTypeEnum.CLOSED.getValue(), ContractTradeTypeEnum.INVALID.getValue())
                .contains(tradeTicketDTO.getTradeType()) && null != addTtDTO) {
            signHuskyTemplateDTO.setSourceContractNum(BigDecimalUtil.formatBigDecimalZero(addTtDTO.getSourceContractNum(), 3, RoundingMode.HALF_UP));
        }
        if (ContractTypeEnum.STRUCTURE.getValue() != tradeTicketDTO.getContractType()) {
            //匹配质量指标信息
            QualityInfoDTO qualityInfoDTO = new QualityInfoDTO()
                    .setGoodsCategoryId(tradeTicketDTO.getCategory2())
                    .setFactoryCode(addTtDTO.getDeliveryFactoryCode())
                    .setWarehouseId(addTtDTO.getShipWarehouseId())
                    .setUsage(BuCodeEnum.WT.getValue().equals(tradeTicketDTO.getBuCode()) ? UsageEnum.DELIVERY.getValue() : tradeTicketDTO.getUsage())
//                .setSpecId(addTtDTO.getGoodsSpecId())
                    .setStandardType(addTtDTO.getStandardType())
                    .setGoodsId(tradeTicketDTO.getGoodsId())
                    .setSalesType(tradeTicketDTO.getSalesType())
                    .setCustomerId(ContractSalesTypeEnum.SALES.getValue() == tradeTicketDTO.getSalesType() ?
                            addTtDTO.getCustomerId() : addTtDTO.getSupplierId());
            this.fillQualityInfo(signHuskyTemplateDTO, qualityInfoDTO);
            //以上期限条款文本(交货截止日期 < 合同签约日期）
            if (null != addTtDTO.getDeliveryEndTime() && null != addTtDTO.getSignDate()) {
                String aboveDeadlineInfo = addTtDTO.getDeliveryEndTime().before(addTtDTO.getSignDate()) ? TemplateConstant.ABOVE_DEADLINE_INFO : "";
                signHuskyTemplateDTO.setAboveDeadlineInfo(aboveDeadlineInfo);
            }
            //正大集团-TT备注处理
            signHuskyTemplateDTO.setSpecialCustomerDeliveryInfo(processZDJTDeliveryByMemo(addTtDTO.getDeliveryFactoryCode(), addTtDTO.getMemo(), signHuskyTemplateDTO.getTemplateCondition()));
        }
    }

    private void buildTTModifyInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TradeTicketDTO tradeTicketDTO,
                                   ContractModifyTTDTO modifyTTDTO, ContractDetailInfoDTO contractDetailInfoDTO) {
        BigDecimal contractUnitPrice = modifyTTDTO.getUnitPrice();
        BigDecimal totalBillNum = contractDetailInfoDTO.getTotalBillNum();
        BigDecimal totalDeliveryNum = contractDetailInfoDTO.getTotalDeliveryNum();
        BigDecimal contractRemainNum = modifyTTDTO.getContractNum().subtract(totalBillNum);
        //TT基本信息========================================
        //合同编号
        signHuskyTemplateDTO.setContractCode(modifyTTDTO.getContractCode())
                // 品种简称(豆粕：M; 豆油：Y)
                .setGoodsCategorySymbol(modifyTTDTO.getFutureCode())
                // 品种全称(豆粕：SBM;豆油：SBO)
                .setGoodsCategoryCode(GoodsCategoryEnum.getByValue(tradeTicketDTO.getCategory2()).getCode())
                // 合同类型
                .setContractTypeInfo(modifyTTDTO.getContractTypeName())
                // 签约日期
                .setSignDate(DateTimeUtil.formatDateStringCN(modifyTTDTO.getSignDate()))
                // 合同吨数
                .setContractNum(BigDecimalUtil.formatBigDecimalZero(modifyTTDTO.getContractNum(), 3, RoundingMode.HALF_UP))
                // 期货合约,示例文本：2022年05月
                .setDomainCodeInfo(getDomainCodeCN(modifyTTDTO.getDomainCode()))
                // 期货合约,示例文本：2205
                .setDomainCode(modifyTTDTO.getDomainCode());
        // 期货合约,示例文本：M2205
        signHuskyTemplateDTO.setCategoryDomainCode(signHuskyTemplateDTO.getGoodsCategorySymbol() + signHuskyTemplateDTO.getDomainCode());
        BigDecimal contractNum = null != modifyTTDTO.getNewContractNum() ? modifyTTDTO.getNewContractNum() : modifyTTDTO.getContractNum();
        // 点价截止日期
        signHuskyTemplateDTO.setPriceEndTime(getPriceEndTimeCN(modifyTTDTO.getPriceEndType(), modifyTTDTO.getPriceEndTime()))
                // 点价截止日期(含)
                .setPriceEndTimeContains(getPriceEndTimeContains(modifyTTDTO.getPriceEndType(), modifyTTDTO.getPriceEndTime()))
                // 点价后补缴比例:5%
                .setAddedDepositRateInfo(getRateString(modifyTTDTO.getAddedDepositRate()))
                .setRemainBillNum(BigDecimalUtil.formatBigDecimalZero(contractNum.subtract(totalBillNum), 3, RoundingMode.HALF_UP))
                .setRemainDeliveryNum(BigDecimalUtil.formatBigDecimalZero(contractNum.subtract(totalDeliveryNum), 3, RoundingMode.HALF_UP))
                //蛋白（规格）
//                .setGoodsSpecId(getGoodsSpecName(modifyTTDTO.getGoodsSpecId()))
                //交货工厂
                .setDeliveryFactoryName(modifyTTDTO.getDeliveryFactoryName())
                //溢短装
                .setWeightTolerance(modifyTTDTO.getWeightTolerance() + "%")
                //包装计算重量
                .setPackageWeight(getPackageWeightInfo(modifyTTDTO.getPackageWeight(), modifyTTDTO.getGoodsCategoryId()))
                //合同-付款金额信息========================================
                //付款方式
                .setPaymentTypeInfo(modifyTTDTO.getPaymentTypeName())
                //赊销账期
                .setCreditDays(modifyTTDTO.getCreditDays())
                //保证金比例
                .setDepositRateInfo(getRateString(modifyTTDTO.getDepositRate()))
                //追加保证金比例
//                .setJointAddedDepositRate(getRateString(getAddRateByRule(modifyTTDTO.getDepositRate(), modifyTTDTO.getAddedDepositRate(), modifyTTDTO.getGoodsCategoryId(), modifyTTDTO.getContractType())))
                .setJointAddedDepositRate(getRateString(modifyTTDTO.getAddedDepositRate2()))
                //付款截止日期
                .setDepositPayEndDay(getDepositPayEndDayCN(modifyTTDTO.getSignDate()))
                .setWriteOffStartTime(getWriteOffDateCN(modifyTTDTO.getWriteOffDeliveryStartTime()))
                .setWriteOffEndTime(getWriteOffDateCN(modifyTTDTO.getWriteOffDeliveryEndTime()))
                .setTaxRateModify(modifyTTDTO.getTaxRate())
                .setTotalAmountModify(modifyTTDTO.getTotalAmount())
                //含税单价
                .setUnitPrice(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
        ;
        BigDecimal totalAmount = modifyTTDTO.getTotalAmount();
        if (modifyTTDTO.getNewContractNum() != null) {
            //拆分的原合同，新合同newContractNum为null
            totalAmount = modifyTTDTO.getNewContractNum().multiply(modifyTTDTO.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);
        }
        //目的地
        signHuskyTemplateDTO
                //含税总金额
                .setTotalAmount(BigDecimalUtil.formatBigDecimalZero(totalAmount, 2, RoundingMode.HALF_UP))
                .setDestination(getDestinationInfo(modifyTTDTO.getDestination(), tradeTicketDTO.getSalesType(), signHuskyTemplateDTO.getTemplateCondition()))
                .setOriginalDestination((getDestinationForWeight(modifyTTDTO.getDestination())))
                //重量验收标准
                .setWeightCheck(getWeightCheckInfo(modifyTTDTO.getWeightCheck(), signHuskyTemplateDTO.getOriginalDestination()))
                //交提货方式
                .setDeliveryTypeInfo(DeliveryModeEnum.getDescByValue(getDeliveryType(modifyTTDTO.getDeliveryType())))
                //交货周期
                .setDeliveryTime(getDeliveryInfo(modifyTTDTO.getDeliveryStartTime(), modifyTTDTO.getDeliveryEndTime()))
        ;
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //基差价
            String extraPrice = (BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getExtraPrice(), 2, RoundingMode.HALF_UP));
            signHuskyTemplateDTO.setExtraPrice(extraPrice);
            if (contractDetailInfoDTO.getContractPriceDTO().getExtraPrice().compareTo(BigDecimal.ZERO) < 0) {
                signHuskyTemplateDTO.setExtraPrice("(" + extraPrice + ")");
            }
        }
        if (null != contractDetailInfoDTO.getContractPriceDTO()) {
            //含税单价明细
            signHuskyTemplateDTO.setUnitPriceDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), modifyTTDTO.getContractType(), false))
                    //含税单价明细(含基差价)
                    .setUnitPriceDiffDetail(getOtherPriceInfo(contractDetailInfoDTO.getContractPriceDTO(), modifyTTDTO.getContractType(), true))
                    //手续费
                    .setThisTimeFee(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getFee(), 2, RoundingMode.HALF_UP))
                    //含税单价中的运费
                    .setTransportPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getTransportPrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE单价
                    .setVePrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVePrice(), 2, RoundingMode.HALF_UP))
                    //含税单价中的VE含量
                    .setVeContent(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getVeContent(), 2, RoundingMode.HALF_UP))
                    //赊销利息
                    .setBusinessPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getBusinessPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价
                    .setComplaintDiscountPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP))
                    //客诉折价总金额
                    .setComplaintDiscountAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getComplaintDiscountPrice()), 2, RoundingMode.HALF_UP))
                    //滞期费
                    .setDelayPrice(BigDecimalUtil.formatBigDecimalZero(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice(), 2, RoundingMode.HALF_UP))
                    //滞期费总金额
                    .setDelayAmount(BigDecimalUtil.formatBigDecimalZero(contractRemainNum.multiply(contractDetailInfoDTO.getContractPriceDTO().getDelayPrice()), 2, RoundingMode.HALF_UP));
            //转月含税单价明细
            //signTemplateDTO.setPrxzy(getOtherPriceInfoZY(modifyTTDTO.getContractPriceDTO(), modifyTTDTO.getContractType()));
            //合同-其他（固定值）========================================
        }
        //国标/企标
        String factoryCode = modifyTTDTO.getDeliveryFactoryCode();
        signHuskyTemplateDTO.setSymbolFactory(TemplateConstant.defaultDeliveryFactoryList.contains(factoryCode) ? "企标值" : "国标值");
        //=========================factory处理区==============================
        buildFactoryInfo(signHuskyTemplateDTO, modifyTTDTO.getShipWarehouseId(), modifyTTDTO.getDeliveryFactoryCode());
        if (ContractTypeEnum.STRUCTURE.getValue() != tradeTicketDTO.getContractType()) {
            //匹配质量指标信息
            QualityInfoDTO qualityInfoDTO = new QualityInfoDTO()
                    .setGoodsCategoryId(tradeTicketDTO.getCategory2())
                    .setFactoryCode(modifyTTDTO.getDeliveryFactoryCode())
                    .setWarehouseId(modifyTTDTO.getShipWarehouseId())
                    .setUsage(BuCodeEnum.WT.getValue().equals(tradeTicketDTO.getBuCode()) ? UsageEnum.DELIVERY.getValue() : tradeTicketDTO.getUsage())
//                .setSpecId(modifyTTDTO.getGoodsSpecId())
                    .setStandardType(modifyTTDTO.getStandardType())
                    .setGoodsId(tradeTicketDTO.getGoodsId())
                    .setSalesType(tradeTicketDTO.getSalesType())
                    .setCustomerId(ContractSalesTypeEnum.SALES.getValue() == tradeTicketDTO.getSalesType() ?
                            modifyTTDTO.getCustomerId() : modifyTTDTO.getSupplierId());
            this.fillQualityInfo(signHuskyTemplateDTO, qualityInfoDTO);
            //以上期限条款文本(交货截止日期 < 合同签约日期）
            String aboveDeadlineInfo = modifyTTDTO.getDeliveryEndTime().before(modifyTTDTO.getSignDate()) ? TemplateConstant.ABOVE_DEADLINE_INFO : "";
            signHuskyTemplateDTO.setAboveDeadlineInfo(aboveDeadlineInfo);
            //正大集团-TT备注处理
            signHuskyTemplateDTO.setSpecialCustomerDeliveryInfo(processZDJTDeliveryByMemo(modifyTTDTO.getDeliveryFactoryCode(), modifyTTDTO.getMemo(), signHuskyTemplateDTO.getTemplateCondition()));
        }
        //============================modifyTTDTO-修改拆分信息特殊处理======================================

        signHuskyTemplateDTO.setTradeTicketTime(DateTimeUtil.formatDateStringCN(tradeTicketDTO.getCreatedAt()));
        TTModifyEntity newContractTT = null;
        TTModifyEntity originalContractTT = null;
        if (Arrays.asList(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.SPLIT_NORMAL.getValue()).contains(tradeTicketDTO.getTradeType())
                && TTTypeEnum.SPLIT.getType().equals(tradeTicketDTO.getType())) {
            newContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), modifyTTDTO.getId());
            originalContractTT = modifyTTDTO;
        } else {
            originalContractTT = ttQueryService.getModifyByRelationId(modifyTTDTO.getRelationId(), modifyTTDTO.getId());
            newContractTT = modifyTTDTO;
        }
        log.info("modifyTTDTO.ttId:{}", newContractTT.getTtId());
        log.info("newContractTT.ttId:{}", newContractTT.getTtId());
        TradeTicketEntity newTradeTicketEntity = ttQueryService.getByTtId(newContractTT.getTtId());

        if (null != newTradeTicketEntity && null != newContractTT) {
            buildFactoryInfo(signHuskyTemplateDTO, newContractTT.getShipWarehouseId(), modifyTTDTO.getDeliveryFactoryCode());
            signHuskyTemplateDTO.setUnitPrice(newContractTT.getUnitPrice() == null ? "0" : BigDecimalUtil.formatBigDecimalZero(newContractTT.getUnitPrice(), 2, RoundingMode.HALF_UP))
                    .setDomainCodeInfo(getDomainCodeCN(newContractTT.getDomainCode()))
                    .setDomainCode(newContractTT.getDomainCode());
        }
        if (null != originalContractTT) {
            //原合同未拆分前的数量
            signHuskyTemplateDTO.setSourceContractNum(BigDecimalUtil.formatBigDecimalZero(originalContractTT.getContractNum(), 3, RoundingMode.HALF_UP));
            //原合同拆分后剩余量
            String contractNumAfter = BigDecimalUtil.formatBigDecimalZero(originalContractTT.getContractNum().subtract(newContractTT.getContractNum()), 3, RoundingMode.HALF_UP);
            signHuskyTemplateDTO.setOriginalContractNum(contractNumAfter);
            //部分拆分（主体变化）
            signHuskyTemplateDTO.setCustomerSplitName(signHuskyTemplateDTO.getCustomerName())
                    .setOriginalDomainCode(DateTimeUtil.formatDateDomainCodeCN(originalContractTT.getDomainCode()));
            if (Arrays.asList(ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.SPLIT_NORMAL.getValue()).contains(originalContractTT.getTradeType())) {
                signHuskyTemplateDTO.setContractNum(BigDecimalUtil.formatBigDecimalZero(newContractTT.getContractNum(), 3, RoundingMode.HALF_UP))
                        .setContractCode(newContractTT.getContractCode())
                        .setCustomerSplitName(ContractSalesTypeEnum.SALES.getValue() == newTradeTicketEntity.getSalesType() ? newTradeTicketEntity.getCustomerName() : newTradeTicketEntity.getSupplierName());
            }
            //原合同溢短装比例
            signHuskyTemplateDTO.setOriginalWeightTolerance(originalContractTT.getWeightTolerance() + "%");
            //银行账号
            if (ContractSalesTypeEnum.PURCHASE.getValue() == tradeTicketDTO.getSalesType()) {
                signHuskyTemplateDTO.setLdcBankAccountNo(newContractTT.getSupplierAccount());

                CustomerBankEntity customerBankEntity = customerBankFacade.queryBankByBankAccountNo(String.valueOf(newContractTT.getSupplierAccount()));
                if (customerBankEntity != null) {
                    //开户名称
//                signTemplateDTO.setMe(customerBankEntity.getBankAccountName());
                    //开户行
                    signHuskyTemplateDTO.setLdcBankName(customerBankEntity.getBankName());
                }
            }
        }
    }

    /**
     * 处理点价
     *
     * @param signHuskyTemplateDTO
     * @param ttPriceEntity
     * @return
     */
    private void buildPriceInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, TTPriceEntity ttPriceEntity, ContractDetailInfoDTO contractDetailInfoDTO) {
        if (null != ttPriceEntity) {
            signHuskyTemplateDTO.setRemainPriceNum(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getRemainPriceNum(), 3, RoundingMode.HALF_UP));
            //原合同含税单价-原合同期货价格
            BigDecimal sourcePrice = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(ttPriceEntity.getContractPriceDetail())) {
                ContractPriceEntity contractPriceEntity = JSONObject.parseObject(ttPriceEntity.getContractPriceDetail(), ContractPriceEntity.class);
                sourcePrice = ttPriceEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            }

            //合同数量
            signHuskyTemplateDTO.setSourceContractNum(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getThisContractNum(), 3, RoundingMode.HALF_UP));

            //定价单TT数量
            signHuskyTemplateDTO.setOriginalPriceNum(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getOriginalPriceNum(), 3, RoundingMode.HALF_UP));
            //点价日期
            signHuskyTemplateDTO.setPriceApplyTime(DateTimeUtil.formatDateStringCN(ttPriceEntity.getCreatedAt()));
            //定价单价格
            signHuskyTemplateDTO.setTransactionPrice(BigDecimalUtil.formatBigDecimalZero(ttPriceEntity.getTransactionPrice(), 2, RoundingMode.HALF_UP));
            BigDecimal source = ttPriceEntity.getTransactionPrice().add(sourcePrice);
            //定价价格
            BigDecimal djjg = ttPriceEntity.getTransactionPrice();

            //定价单含税总金额
            BigDecimal originalPriceNum = source.multiply(ttPriceEntity.getOriginalPriceNum());
            signHuskyTemplateDTO.setPriceOrderTaxAmount(BigDecimalUtil.formatBigDecimalZero(originalPriceNum, 2, RoundingMode.HALF_UP));

            //定价价格不含税总金额
            //BigDecimal jdbhs = (djjg.add(sourcePrice)).divide(BigDecimal.valueOf(1.09), 6, BigDecimal.ROUND_CEILING);

            //定价单不含税总金额
            //BigDecimal djnprt = jdbhs.multiply(ttPriceEntity.getNum());
            BigDecimal priceOrderNoTaxAmount = originalPriceNum.divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            signHuskyTemplateDTO.setPriceOrderNoTaxAmount(BigDecimalUtil.formatBigDecimalZero(priceOrderNoTaxAmount, 2, RoundingMode.HALF_UP));

            //定价单增值税总金额
            signHuskyTemplateDTO.setPriceOrderAddedTaxAmount(BigDecimalUtil.formatBigDecimalZero(originalPriceNum.subtract(priceOrderNoTaxAmount), 2, RoundingMode.HALF_UP));

            //加权平均价
            //List<ConfirmPriceDTO> confirmPriceDTOList = ttPriceService.getContractPricingList(ttPriceEntity.getContractId());
            //BigDecimal weightedAverageUnitPrice = this.ldjjg(confirmPriceDTOList);
            if (BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO)) {

                BigDecimal weightedAverageUnitPrice = ttPriceEntity.getAvePrice();
                signHuskyTemplateDTO.setWeightedAverageUnitPrice(BigDecimalUtil.formatBigDecimalZero(weightedAverageUnitPrice, 2, RoundingMode.HALF_UP));

                //加权平均含税价
                signHuskyTemplateDTO.setWeightedAverageTaxPrice(BigDecimalUtil.formatBigDecimalZero(weightedAverageUnitPrice.add(sourcePrice), 2, RoundingMode.HALF_UP));

                //加权含税总金额
                BigDecimal weightedAverageTaxAmount = (weightedAverageUnitPrice.add(sourcePrice)).multiply(ttPriceEntity.getThisContractNum());
                signHuskyTemplateDTO.setWeightedAverageTaxAmount(BigDecimalUtil.formatBigDecimalZero(weightedAverageTaxAmount, 2, RoundingMode.HALF_UP));

                //加权不含税总金额
                //BigDecimal lnpr = (weightedAverageUnitPrice.add(sourcePrice)).divide(BigDecimal.valueOf(1.09), 6, BigDecimal.ROUND_CEILING);
                //lnpr = lnpr.multiply(contractDetailInfoDTO.getContractNum());
                //todo:税率
                BigDecimal weightedAverageNoTaxAmount = weightedAverageTaxAmount.divide(contractDetailInfoDTO.getTaxRate().add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
                signHuskyTemplateDTO.setWeightedAverageNoTaxAmount(BigDecimalUtil.formatBigDecimalZero(weightedAverageNoTaxAmount, 2, RoundingMode.HALF_UP));

                //加权增值税总金额
                signHuskyTemplateDTO.setWeightedAverageAddedTaxAmount(BigDecimalUtil.formatBigDecimalZero(weightedAverageTaxAmount.subtract(weightedAverageNoTaxAmount), 2, RoundingMode.HALF_UP));

                if (!BigDecimalUtil.isEqual(ttPriceEntity.getUnitPrice(), contractDetailInfoDTO.getUnitPrice())) {
                    List<String> modifyList = signHuskyTemplateDTO.getTemplateCondition().getModifyList();
                    modifyList.add("unitPrice");
                    signHuskyTemplateDTO.getTemplateCondition().setModifyList(modifyList);
                }
            }

        }
    }


    private BigDecimal ldjjg(List<ConfirmPriceDTO> confirmPriceDTOList) {

        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalNum = BigDecimal.ZERO;
        for (TTPriceEntity ttPriceEntity : confirmPriceDTOList) {
            totalPrice = totalPrice.add(ttPriceEntity.getPrice().setScale(2, RoundingMode.HALF_UP).multiply(ttPriceEntity.getNum()));
            totalNum = totalNum.add(ttPriceEntity.getNum());
        }
        // 加权平均价

        BigDecimal ldjjg = BigDecimalUtil.isGreaterThanZero(totalNum) ? BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum) : BigDecimal.ZERO;

        return ldjjg;
    }

    /**
     * 处理转月
     *
     * @param signHuskyTemplateDTO
     * @param transferTTDTO
     * @return
     */
    private void buildTransferInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractTransferTTDTO transferTTDTO) {
        if (null != transferTTDTO) {
            //期货合约
            signHuskyTemplateDTO.setDomainCodeInfo(getDomainCodeCN(transferTTDTO.getDomainCode()));
            //期货合约
            signHuskyTemplateDTO.setDomainCode(transferTTDTO.getDomainCode());
            //原期货合约
            signHuskyTemplateDTO.setOriginalDomainCode(DateTimeUtil.formatDateDomainCodeCN(transferTTDTO.getOriginalDomainCode()));
            //转月数量
            signHuskyTemplateDTO.setTransferNum(BigDecimalUtil.formatBigDecimalZero(transferTTDTO.getNum(), 3, RoundingMode.HALF_UP));
            //转月价差
            signHuskyTemplateDTO.setTransferTTPrice(BigDecimalUtil.formatBigDecimalZero(transferTTDTO.getPrice(), 2, RoundingMode.HALF_UP));

            TradeTicketEntity tradeTicketEntity = ttQueryService.getByTtId(transferTTDTO.getTtId());
            TradeTicketEntity originalContractTT = ttQueryService.getByGroupId(tradeTicketEntity.getGroupId(), transferTTDTO.getTtId());
            if (null != originalContractTT) {
                signHuskyTemplateDTO.getTemplateCondition().setSplitType(ContractActionEnum.TRANSFER_CONFIRM.getActionValue() == tradeTicketEntity.getContractSource() || ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue() == tradeTicketEntity.getContractSource() ? SplitTypeEnum.PART_SPLIT.getValue() : SplitTypeEnum.ALL_SPLIT.getValue());

                TTTranferEntity originalTransferEntity = ttQueryService.getTransferById(originalContractTT.getId());
                ContractEntity contractEntity = contractQueryService.getContractById(originalContractTT.getContractId());
                //原合同未拆分前的数量
                signHuskyTemplateDTO.setSourceContractNum(BigDecimalUtil.formatBigDecimalZero(originalTransferEntity.getNum(), 3, RoundingMode.HALF_UP));
                //原合同拆分后剩余量
                String contractNumAfter = BigDecimalUtil.formatBigDecimalZero(originalTransferEntity.getNum().subtract(transferTTDTO.getNum()), 3, RoundingMode.HALF_UP);
                signHuskyTemplateDTO.setOriginalContractNum(contractNumAfter);
                //原合同溢短装比例
                signHuskyTemplateDTO.setOriginalWeightTolerance(contractEntity.getWeightTolerance() + "%");
                //银行账号
//                if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
//                    CustomerBankEntity customerBankEntity = customerBankFacade.queryBankByBankAccountNo(String.valueOf(contractEntity.getSupplierAccount()));
//                    if (customerBankEntity != null) {
//                        //开户行
//                        signHuskyTemplateDTO.setLdcBankName(customerBankEntity.getBankName());
//                    }
//                }
            }
        }


    }

    /**
     * 处理解约定赔
     *
     * @param signHuskyTemplateDTO
     * @param addTTDTO
     * @param contractUnitPrice
     * @param templateCondition
     * @return
     */
    private void buildWashoutInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractAddTTDTO addTTDTO,
                                  BigDecimal contractUnitPrice, TemplateConditionDTO templateCondition, ContractDetailInfoDTO contractDetailInfoDTO) {
        if (null != addTTDTO) {
            BigDecimal washoutNum = addTTDTO.getContractNum();
            BigDecimal washoutPrice = addTTDTO.getWashoutUnitPrice().setScale(2, RoundingMode.HALF_UP);
            BigDecimal washoutDiffPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, contractUnitPrice, washoutPrice).setScale(2, RoundingMode.HALF_UP);
            templateCondition.setWashOutDiffUnitPrice(washoutDiffPrice)
                    .setWashOutDiffUnitPriceType(CompareUnitPriceEnum.getCompareUnitPrice(washoutDiffPrice, templateCondition.getSalesType()));
            washoutDiffPrice = washoutDiffPrice.abs();
            BigDecimal washoutDiffAmount = BigDecimalUtil.multiply(CalcTypeEnum.PRICE, washoutDiffPrice, washoutNum).setScale(2, RoundingMode.HALF_UP);
            //解约定赔数量
            signHuskyTemplateDTO.setWashoutNum(BigDecimalUtil.formatBigDecimalZero(washoutNum, 3, RoundingMode.HALF_UP));
            //解约定赔市场价格
            signHuskyTemplateDTO.setWashoutPrice(BigDecimalUtil.formatBigDecimalZero(washoutPrice, 2, RoundingMode.HALF_UP));
            //解约定赔差价
            signHuskyTemplateDTO.setWashoutDiffPrice(BigDecimalUtil.formatBigDecimalZero(washoutDiffPrice, 2, RoundingMode.HALF_UP));
            //解约定赔差价总额
            signHuskyTemplateDTO.setWashoutDiffAmount(BigDecimalUtil.formatBigDecimalZero(washoutDiffAmount, 2, RoundingMode.HALF_UP));
            signHuskyTemplateDTO.setUnitPrice(BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
            String domainCode = "(" + addTTDTO.getFutureCode() + addTTDTO.getDomainCode() + ")";
//            if (ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
//                signTemplateDTO.setPr(domainCode + (contractUnitPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP));
//            }
            if ((ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == templateCondition.getContractType() && BigDecimal.ZERO.compareTo(contractDetailInfoDTO.getTotalPriceNum()) == 0)
                    || ContractTypeEnum.JI_CHA.getValue() == templateCondition.getContractType()) {
                signHuskyTemplateDTO.setUnitPrice(domainCode + (contractUnitPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + BigDecimalUtil.formatBigDecimalZero(contractUnitPrice, 2, RoundingMode.HALF_UP))
                        .setWashoutPrice(domainCode + (washoutPrice.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "") + signHuskyTemplateDTO.getWashoutPrice());
            }
            buildFactoryInfo(signHuskyTemplateDTO, addTTDTO.getShipWarehouseId(), addTTDTO.getDeliveryFactoryCode());
        }


    }

    /**
     * 处理结构化定价
     *
     * @param signHuskyTemplateDTO
     * @param structurePriceAddDTO
     * @return
     */
    private void buildStructureInfo(SignHuskyTemplateDTO signHuskyTemplateDTO, ContractStructurePriceAddDTO structurePriceAddDTO) {
        //结构化定价总数量
        signHuskyTemplateDTO.setStructurePriceTotalNum(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getTotalNum(), 3, RoundingMode.HALF_UP));
        //结构化定价开始日期
        signHuskyTemplateDTO.setStructurePriceStartTime(DateTimeUtil.formatDateStringCN(structurePriceAddDTO.getStartTime()));
        //结构化定价结束日期
        signHuskyTemplateDTO.setStructurePriceEndTime(DateTimeUtil.formatDateStringCN(structurePriceAddDTO.getEndTime()));
        //结构化定价敲出价格
//            signTemplateDTO.setJghqc(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getMaxPrice(), 2, RoundingMode.HALF_UP));
        //结构化定价增强价格
//            signTemplateDTO.setJghzq(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getMaxPrice(), 2, RoundingMode.HALF_UP));
        //结构化定价触发价格
        signHuskyTemplateDTO.setStructureTriggerPrice(structurePriceAddDTO.getTriggerPrice());
        //结构化定价累积价格
        signHuskyTemplateDTO.setStructureCumulativePrice(structurePriceAddDTO.getCumulativePrice());
        //结构化定价单位量
        signHuskyTemplateDTO.setStructurePriceUnitNum(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getUnitNum(), 3, RoundingMode.HALF_UP));
        // 结构化定价单位数量
        signHuskyTemplateDTO.setStructureUnitNum(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getStructureUnitNum(), 3, RoundingMode.HALF_UP));
        //结构化定价单位增量
        signHuskyTemplateDTO.setStructureUnitIncrement(BigDecimalUtil.formatBigDecimalZero(structurePriceAddDTO.getUnitIncrement(), 3, RoundingMode.HALF_UP));
        //单吨现金返还金额
        signHuskyTemplateDTO.setStructureCashReturn(structurePriceAddDTO.getCashReturn());
    }

    /**
     * 匹配质量指标信息
     *
     * @param huskyTemplateDTO
     * @param qualityInfoDTO
     */
    private void fillQualityInfo(SignHuskyTemplateDTO huskyTemplateDTO, QualityInfoDTO qualityInfoDTO) {
        QualityEntity qualityEntity = qualityFacade.matchQuality(qualityInfoDTO);
        huskyTemplateDTO.setQualityInfo(null == qualityEntity ? "" : qualityEntity.getContent())
                .setQualityId(null == qualityEntity ? 0 : qualityEntity.getId());

    }

    private String getDeliveryInfo(Date d1, Date d2) {
        try {
            return DateTimeUtil.formatDateStringCN(d1, d2);
        } catch (Exception e) {
            return WHEN_ERROR_DELIVERY_DATE;
        }
    }

    private Date getDepositPayEndDay(Date signDate) {
        return DateTimeUtil.addDays(signDate, DEFAULT_PAY_DAYS, false);
    }

    private String getDepositPayEndDayCN(Date signDate) {
        try {
            Date depositPayEndDay = getDepositPayEndDay(signDate);
            return DateTimeUtil.formatDateStringCN(depositPayEndDay);
        } catch (Exception e) {
            return WHEN_ERROR_DEPOSIT_PAY_END_DATE;
        }
    }

    private String getWriteOffDateCN(Date writeOffDate) {
        try {
            return DateTimeUtil.formatDateStringCN(writeOffDate);
        } catch (Exception e) {
            return WHEN_ERROR_DEPOSIT_PAY_END_DATE;
        }
    }

    private String getDomainCodeCN(String domainCode) {
        if (StringUtil.isNotEmpty(domainCode)) {
            return DateTimeUtil.formatDateDomainCodeCN(domainCode);
        }
        return "";
    }

    private String getPriceEndTimeContains(Integer priceEndType, String priceEndTime) {
        String rtn = priceEndTime;
        try {
            if (priceEndType == ContractPriceEndTypeEnum.DATE.getValue()) {
                rtn = DateTimeUtil.formatDayStringCN(rtn) + "（含）";
            }
//            else if (StringUtils.isNotBlank(rtn)) {
//                rtn = rtn.substring(0, 2);
//            }
        } catch (Exception e) {
            log.info("点价截止日期格式化失败" + e.toString());
        }
        return rtn;
    }

    private String getPriceEndTimeCN(Integer priceEndType, String priceEndTime) {
        String rtn = priceEndTime;
        try {
            if (priceEndType == ContractPriceEndTypeEnum.DATE.getValue()) {
                rtn = DateTimeUtil.formatDayStringCN(rtn);
            }
//            else if (StringUtils.isNotBlank(rtn)) {
//                rtn = rtn.substring(0, 2);
//            }
        } catch (Exception e) {
            log.info("点价截止日期格式化失败" + e.toString());
        }
        return rtn;
    }

    public String getRateString(Integer rate) {
        if (null == rate) {
            return "0";
        }
        return 0 == rate ? "0" : rate + "%";
    }

    /**
     * 豆粕有更新
     * 履约保证金=除5、10、15、20外，显示5%
     * 履约保证金=5%，显示5%
     * 履约保证金=10%，显示5%
     * 履约保证金=15%，显示10%
     * 履约保证金=20%，显示10%
     * 豆油新增
     * 履约保证金=除5、10、15、20外，显示5%
     * 履约保证金=5%，显示5%
     * 履约保证金=10%，显示7%
     * 履约保证金=15%，显示10%
     * 履约保证金=20%，显示10%
     *
     * @param rate
     * @param categoryId
     * @return
     */
    public Integer getAddRateWhenDrop(Integer rate, Integer categoryId) {
        if (HIGH_DEPOSIT_RATE_10.equals(rate)) {
            rate = GoodsCategoryEnum.OSM_MEAL.getValue().equals(categoryId) ? HIGH_DEPOSIT_RATE_5 : HIGH_DEPOSIT_RATE_7;
        } else if (HIGH_DEPOSIT_RATE_15.equals(rate)) {
            rate = HIGH_DEPOSIT_RATE_10;
        } else if (HIGH_DEPOSIT_RATE_20.equals(rate)) {
            rate = HIGH_DEPOSIT_RATE_10;
        } else {
            rate = HIGH_DEPOSIT_RATE_5;
        }
        return rate;
    }

    /**
     * * 豆粕#BZJZJ#
     * 基差合同
     * * 履约保证金比例+履约保证金补缴比例≤10%，显示5%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 履约保证金比例≤10%，显示5%
     * * 履约保证金比例>10%，显示10%
     * * 豆油#BZJZJ#
     * 基差合同
     * * 履约保证金比例+履约保证金补缴比例≤5%，显示5%
     * * 5%<履约保证金比例+履约保证金补缴比例≤10%，显示7%
     * * 履约保证金比例+履约保证金补缴比例>10%，显示10%
     * 一口价合同
     * * 履约保证金比例≤5%，显示5%
     * * 5%<履约保证金比例≤10%，显示7%
     * * 履约保证金比例>10%，显示10%
     *
     * @return
     */
    public Integer getAddRateByRule(Integer depositRate, Integer addedDepositRate, Integer categoryId, Integer contractType) {
        if (contractType.equals(ContractTypeEnum.JI_CHA.getValue())) {
            depositRate = depositRate + addedDepositRate;
        }
        if (categoryId.equals(GoodsCategoryEnum.OSM_MEAL.getValue())) {
            if (depositRate <= HIGH_DEPOSIT_RATE_10) {
                depositRate = HIGH_DEPOSIT_RATE_5;
            } else {
                depositRate = HIGH_DEPOSIT_RATE_10;
            }
        } else {
            if (depositRate <= HIGH_DEPOSIT_RATE_5) {
                depositRate = HIGH_DEPOSIT_RATE_5;
            } else if (depositRate <= HIGH_DEPOSIT_RATE_10) {
                depositRate = HIGH_DEPOSIT_RATE_7;
            } else {
                depositRate = HIGH_DEPOSIT_RATE_10;
            }
        }
        return depositRate;
    }

    private String getGoodsSpecName(Integer goodsSpecId) {
        AttributeValueEntity attributeValueEntity = attributeFacade.getAttributeValueById(goodsSpecId);
        String specName = null != attributeValueEntity ? attributeValueEntity.getName() : "";
        if (specName.contains("RZ")) {
            specName = specName.substring(0, specName.length() - 3);
        }
        return specName;
    }

    private String getWeightCheckInfo(String weightCheck, String destination) {
        try {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(weightCheck));
            String weightCheckInfo = null != systemRuleItemEntity ? systemRuleItemEntity.getRuleValue() : "";
            return StringUtils.isNotBlank(weightCheckInfo) ? weightCheckInfo.replace("#PYE#", destination) : weightCheckInfo;
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
    }

    private String getDestinationInfo(String destination, Integer salesType, TemplateConditionDTO templateCondition) {
        String destinationInfo = destination;
        try {
            //目的港
            if (StringUtils.isNumeric(destination)) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(destination));
                destinationInfo = systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : destination;
                templateCondition.setDestinationCode(systemRuleItemEntity != null ? systemRuleItemEntity.getLkgCode() : "");
                if (!DESTINATION_LKG_CODE_LIST.contains(systemRuleItemEntity == null ? "" : systemRuleItemEntity.getLkgCode())) {
                    String jointText = ContractSalesTypeEnum.PURCHASE.getValue() == salesType ? "（下称“交货地点”)" : "（“指定地点”）";
                    destinationInfo = "送到：" + destinationInfo + jointText;
                }
            } else {
                destinationInfo = destination;
            }
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
        return destinationInfo;
    }

    private String getDestinationForWeight(String destination) {
        String destinationInfo = destination;
        try {
            //目的港
            if (StringUtils.isNumeric(destination)) {
                SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(destination));
                destinationInfo = systemRuleItemEntity != null ? systemRuleItemEntity.getRuleKey() : destination;
                if (DESTINATION_LKG_CODE_LIST.contains(systemRuleItemEntity == null ? "" : systemRuleItemEntity.getLkgCode())) {
                    destinationInfo = "目的港";
                }
            } else {
                destinationInfo = destination;
            }
        } catch (NumberFormatException e) {
            return WHEN_ERROR_WEIGHT_CHECK;
        }
        return destinationInfo;
    }

    private String getPackageWeightInfo(String packageWeight, Integer goodsCategoryId) {
        try {
            String packageWeightInfo = packageWeight;
            if (Arrays.asList(GoodsCategoryEnum.OSM_OIL.getValue(), GoodsCategoryEnum.SPECIAL_OIL.getValue()).contains(goodsCategoryId)) {
                return WHEN_OIL_PACKAGE_WEIGHT;
            }
            //袋皮扣重
            if (StringUtils.isNumeric(packageWeight)) {
                SystemRuleItemEntity packageWeightItemEntity = systemRuleFacade.getRuleItemById(Integer.valueOf(packageWeight));
                packageWeightInfo = packageWeightItemEntity != null ? packageWeightItemEntity.getMemo() : packageWeight;
            }
            return packageWeightInfo;
        } catch (NumberFormatException e) {
            return WHEN_ERROR_PACKAGE_WEIGHT;
        }
    }

    /**
     * 仅仅
     *
     * @param contractPriceEntity
     * @param contractType
     * @param containExtraPrice
     * @return
     */
    public String getOtherPriceInfo(ContractPriceEntity contractPriceEntity, Integer contractType, boolean containExtraPrice) {
        String preInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "+" : "";
        String stuffInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "" : ",";
        //当containExtraPrice=true,#PRXY#仅基差暂定价使用，基差自行拼接#基差基准#+#PRX#
        String extraPriceInfo = containExtraPrice ? BigDecimalUtil.formatDiffPriceCN("基准基差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getExtraPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) : "";
        String otherPrice = extraPriceInfo + BigDecimalUtil.formatPriceCN("出厂价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("蛋白价格", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getProteinDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("散粕补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getCompensationPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("期权费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOptionPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("运费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransportPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("起吊费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getLiftingPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("滞期费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getDelayPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("高温费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTemperaturePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("其他物流费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherDeliveryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("和解款折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBuyBackPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("客诉折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("转厂补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransferFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("其他补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("商务补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBusinessPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("手续费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFee(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("装运费单价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getShippingFeePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("精炼价差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getRefineDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceCN("精炼/分提价差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getRefineFracDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo);
        if (StringUtil.isNotEmpty(otherPrice) && ContractTypeEnum.JI_CHA.getValue() != contractType) {
            otherPrice = "；单价包含" + otherPrice;
        }
        return StringUtils.isNotBlank(otherPrice) && !ContractTypeEnum.getBasicList().contains(contractType) ? otherPrice.substring(0, otherPrice.length() - 1) : otherPrice;
    }

    public String getOtherPriceInfoZY(ContractPriceEntity contractPriceEntity, Integer contractType) {
        String preInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "+" : "";
        String stuffInfo = ContractTypeEnum.getBasicList().contains(contractType) ? "" : ",";
        String otherPrice = BigDecimalUtil.formatPriceZY("出厂价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("蛋白价格", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getProteinDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("散粕补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getCompensationPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("期权费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOptionPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("运费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransportPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("起吊费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getLiftingPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("滞期费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getDelayPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("高温费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTemperaturePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("其他物流费", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherDeliveryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("和解款折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBuyBackPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("客诉折价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getComplaintDiscountPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("转厂补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getTransferFactoryPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("其他补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getOtherPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("商务补贴", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getBusinessPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("装运费单价", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getShippingFeePrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo) +
                BigDecimalUtil.formatPriceZY("精炼价差", BigDecimalUtil.formatBigDecimalZero(contractPriceEntity.getRefineDiffPrice(), 2, RoundingMode.HALF_UP), preInfo, stuffInfo);
        if (StringUtil.isNotEmpty(otherPrice) && ContractTypeEnum.getBasicList().contains(contractType)) {
            otherPrice = "；单价包含" + otherPrice;
        }
        return StringUtils.isNotBlank(otherPrice) && !ContractTypeEnum.getBasicList().contains(contractType) ? otherPrice.substring(0, otherPrice.length() - 1) : otherPrice;
    }

    private Integer getDeliveryType(Integer deliveryType) {
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeService.getDeliveryTypeById(deliveryType);
        if (null == deliveryTypeEntity) {
            return DeliveryModeEnum.TAKE.getValue();
        }
        return deliveryTypeEntity.getType();
    }

    /**
     * TT的备注信息中，包含被“@#￥”括起来的信息，信息内容不做判断
     *
     * @param content
     * @return
     */
    private static List<String> renderZDSpecialDeliveryList(String content) {
        // ${!}
//        String regex1 = "\\$\\{([^}]*)!\\}";
        String a = "@#￥";
        String regex = "@#￥([^}]*)@#￥";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> keyList = new ArrayList<>();
        while (matcher.find()) {
            keyList.add(matcher.group().replace(a, ""));
        }
        return keyList;
    }

}
