package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractConfirmResultDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;

/**
 * <p>
 * 确认合同变更
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/16
 */
public interface ConfirmLogicService {

    /**
     * 协议合规确认调用
     *
     * @param contractModifyDTO
     * @return
     */
    ContractConfirmResultDTO confirmContractModify(ContractModifyDTO contractModifyDTO);

}
