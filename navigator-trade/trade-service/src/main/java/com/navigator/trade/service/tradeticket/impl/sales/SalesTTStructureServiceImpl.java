package com.navigator.trade.service.tradeticket.impl.sales;

import cn.hutool.core.bean.BeanUtil;
import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StructureCodeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.dao.TtStructureDao;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.tradeticket.OperateTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.entity.TTStructureEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryStructureVO;
import com.navigator.trade.service.IContractStructureService;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component("SBM_S_TT_STRUCTURE_PRICE,SBO_S_TT_STRUCTURE_PRICE")
public class SalesTTStructureServiceImpl extends BaseTradeTicketAbstractService {

    @Autowired
    private TtStructureDao ttStructureDao;
    @Autowired
    private IContractStructureService contractStructureService;
    @Autowired
    private StructureRuleFacade structureRuleFacade;

    public SalesTTStructureServiceImpl() {
        ttTypeEnum = TTTypeEnum.STRUCTURE_PRICE;
        contractTradeTypeEnum = ContractTradeTypeEnum.STRUCTURE_PRICE;
        contractSource = ContractActionEnum.STRUCTURE_PRICE_CONFIRM.getActionValue();
        contractStatus = ContractStatusEnum.INEFFECTIVE.getValue();
        operationSource = OperationSourceEnum.EMPLOYEE.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.NEW.getType();
        salesType = ContractSalesTypeEnum.SALES;
        processorType = ProcessorTypeEnum.SBM_S_STRUCTURE_PRICE.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_STRUCTURE_PRICE.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getStructureTradeTicketEntity(ttdto);
        injectionProperty(tradeTicketEntity);
        return tradeTicketEntity;
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        ContractSignCreateDTO contractSignCreateDTO = contractSignConvertUtil.getStructureContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
        return contractSignCreateDTO;
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) {
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttDto.getSalesStructurePriceTTDTO();

        TTStructureEntity ttStructureEntity = new TTStructureEntity();
        BeanUtil.copyProperties(salesStructurePriceTTDTO, ttStructureEntity);
        ttStructureEntity
                .setTotalNum(salesStructurePriceTTDTO.getTotalNum())
                .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setSignDate(salesStructurePriceTTDTO.getSignDate())
                .setCustomerName(salesStructurePriceTTDTO.getCustomerName())
                .setDeliveryFactoryCode(salesStructurePriceTTDTO.getDeliveryFactoryCode())
                .setDeliveryFactoryId(salesStructurePriceTTDTO.getDeliveryFactoryId())
                .setStructureName(salesStructurePriceTTDTO.getStructureName())
                .setTtId(ttId)
        ;
        TTStructureEntity ttStructureEntity1 = ttStructureDao.getByTTId(ttId);
        if (ttStructureEntity1 != null) {
            ttStructureEntity.setId(ttStructureEntity1.getId());
        }
        ttStructureDao.saveOrUpdate(ttStructureEntity);
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        //初始化交易、销售类型、合同来源
        salesStructurePriceTTDTO.setStatus(status);
        salesStructurePriceTTDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesStructurePriceTTDTO.setSalesType(salesType.getValue());
        salesStructurePriceTTDTO.setContractSource(contractSource);

        //协议签署状态
        salesStructurePriceTTDTO.setContractSignatureStatus(contractSignatureStatus);
        String userId = JwtUtils.getCurrentUserId();
        salesStructurePriceTTDTO.setUserId(userId);
        String code = salesStructurePriceTTDTO.getCode();

        //根据code存在与否, 判断是否为新增
        if (StringUtils.isBlank(code)) {
            salesStructurePriceTTDTO.setCreateStatus(true);
            //生成TT编号
            code = CodeGeneratorUtil.genSalesTTNewCode();
            salesStructurePriceTTDTO.setCode(code);
            //生成合同编号
            if (null != salesStructurePriceTTDTO.getSupplierId()) {
                CustomerDTO supplier = customerFacade.getCustomerById(salesStructurePriceTTDTO.getSupplierId());
                if (null != supplier) {
                    CompanyEntity companyEntity = companyFacade.queryCompanyById(supplier.getCompanyId());
                    String contractCode = contractService.genStructureContractCode(companyEntity.getShortName());
                    salesStructurePriceTTDTO.setContractCode(contractCode);
                }
            }
        } else {
            salesStructurePriceTTDTO.setCreateStatus(false);
            //设置ttId
            TradeTicketEntity tradeTicketEntity = tradeTicketDao.getTradeTicketEntityByCode(salesStructurePriceTTDTO.getCode());
            if (tradeTicketEntity == null) {
                throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
            }
            Integer ttId = tradeTicketEntity.getId();
            salesStructurePriceTTDTO.setTtId(ttId);
            salesStructurePriceTTDTO.setContractCode(tradeTicketEntity.getContractCode());
        }
        ttdto.setSalesStructurePriceTTDTO(salesStructurePriceTTDTO);

        return ttdto;
    }

    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        return null;
    }

    @Override
    public void createContract(TradeTicketEntity tradeTicketEntity, TTDTO ttdto) {
        SalesStructurePriceTTDTO salesStructurePriceTTDTO = ttdto.getSalesStructurePriceTTDTO();
        salesStructurePriceTTDTO.setTtId(tradeTicketEntity.getId());
        ContractStructureEntity contractStructureEntity = contractStructureService.submitStructureContract(salesStructurePriceTTDTO);
        if (contractStructureEntity != null) {
            tradeTicketDao.updateContractId(tradeTicketEntity.getId(), contractStructureEntity.getContractId());
            ttStructureDao.updateContractId(tradeTicketEntity.getId(), contractStructureEntity.getContractId());
        }
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        return "";
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTQueryStructureVO ttQueryStructureVO = new TTQueryStructureVO();
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTStructureEntity ttStructureEntity = ttStructureDao.getByTTId(ttId);
        BeanUtils.copyProperties(ttStructureEntity, ttQueryStructureVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryStructureVO);
        ttQueryStructureVO
                .setTtId(tradeTicketEntity.getId())
                .setGoodsCategoryId(tradeTicketEntity.getSubGoodsCategoryId())
                .setGoodsCategoryName(GoodsCategoryEnum.getDescByValue(tradeTicketEntity.getSubGoodsCategoryId()))
                .setTradeTypeName(ContractTradeTypeEnum.getDescByValue(tradeTicketEntity.getTradeType()));
        if (StringUtils.isBlank(ttStructureEntity.getStructureName())) {
            if (null != ttStructureEntity.getStructureType()) {
                String structureName = structureRuleFacade.getNameById(ttStructureEntity.getStructureType());
                ttQueryStructureVO.setStructureTypeName(structureName);
            }
        } else {
            ttQueryStructureVO.setStructureTypeName(StructureCodeUtil.numToCode(ttStructureEntity.getStructureType()) + "-" + ttStructureEntity.getStructureName());
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryStructureVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryStructureVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryStructureVO.setCreatedByName(employEntity.getName());
            }
        }
        ttDetailVO.setTtQueryStructureVO(ttQueryStructureVO);
        ttDetailVO.setDetailType("6");
        return ttDetailVO;
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {
    }

    @Override
    public void cancel(OperateTTDTO operateTTDTO, TradeTicketEntity tradeTicketEntity) {
        if (ttTypeEnum == TTTypeEnum.STRUCTURE_PRICE) {
            String userId = JwtUtils.getCurrentUserId();
            //取消工作流审批
            cancelActiviti(operateTTDTO.getMemo(), userId, tradeTicketEntity);
            log.info("check_code_question  cancel ");
            //修改tt状态为待修改提交
            tradeTicketDao.updateStatusById(TTStatusEnum.WAITING.getType(), null, operateTTDTO.getTtId());
            //取消协议
            cancelContractSign(tradeTicketEntity, operateTTDTO.getMemo());
        } else {
            super.cancel(operateTTDTO, tradeTicketEntity);
        }
    }

    @Override
    public void releasePriceNum(TradeTicketEntity tradeTicketEntity) {
        priceApplyFacade.closePriceApplyByContractId(tradeTicketEntity.getContractId());
    }

    ;

}
