package com.navigator.trade.app.tt.domain.service.processor;

import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.TTModifyEntity;
import com.navigator.trade.pojo.entity.TTSubEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("MODIFY")
public class TTModifyDomainProcessor extends AbstractTTDomainProcessor {

    @Autowired
    TtModifyDao ttModifyDao;

    public TTModifyDomainProcessor() {
        System.out.println("TTModifyDomainProcessor");
    }

    @Override
    void addTTSubEntity(TradeTicketDO tradeTicketDO) {

        //set value
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(tradeTicketDO.getTradeTicketEntity().getId());

        TTModifyEntity modifyEntity = (TTModifyEntity) ttSubEntity;

        TTModifyEntity oldModifyEntity = ttModifyDao.getTTModifyEntityByTTId(modifyEntity.getTtId());
        if (Objects.nonNull(oldModifyEntity)) {
            modifyEntity.setId(oldModifyEntity.getId());
        }
        ttModifyDao.save(modifyEntity);
    }

    @Override
    boolean updateTTSubEntityContractInfo(TradeTicketEntity tradeTicketEntity, ContractEntity contractEntity) {
        int rtn = ttModifyDao.updateContractInfo(tradeTicketEntity.getId(), contractEntity);
        return rtn > 0;
    }
}
