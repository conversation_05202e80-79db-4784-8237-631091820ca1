package com.navigator.trade.app.contract.logic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantCancellationEntity;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.dto.LkgQueryRecordDTO;
import com.navigator.pigeon.pojo.entity.LkgContractInfoEntity;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.ContractQueryLogicService;
import com.navigator.trade.app.sign.domain.service.ContractSignQueryDomainService;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.tradeticket.ContractPriceDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.qo.ContractQO;
import com.navigator.trade.pojo.vo.*;
import com.navigator.trade.service.IContractPriceService;
import com.navigator.trade.service.ITtPriceService;
import com.navigator.trade.service.contractsign.IContractSignQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.navigator.common.constant.RedisConstants.CONTRACT_SAVE_TT_TIMES;

/**
 * trade读服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-07-15
 */
@Slf4j
@Service
@RefreshScope
public class ContractQueryLogicServiceImpl implements ContractQueryLogicService {

    /**
     * 其他域信息
     */
    @Resource
    private CustomerFacade customerFacade;

    @Resource
    private CategoryFacade categoryFacade;

    @Resource
    private CEmployFacade cEmployFacade;

    @Resource
    private EmployFacade employFacade;

    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    private WarehouseFacade warehouseFacade;
    @Resource
    private FileBusinessFacade fileBusinessFacade;

    @Resource
    private StructureRuleFacade structureRuleFacade;

    @Resource
    private DeliveryTypeFacade deliveryTypeFacade;

    @Resource
    private SystemRuleFacade systemRuleFacade;

    @Resource
    private PayConditionFacade payConditionFacade;

    @Resource
    private FuturesDomainFacade futuresDomainFacade;

    @Resource
    private LkgContractFacade lkgContractFacade;

    @Resource
    private PriceAllocateFacade priceAllocateFacade;

    @Resource
    private CompanyFacade companyFacade;

    @Resource
    private GoodsFacade goodsFacade;

    @Resource
    private WarrantFacade warrantFacade;

    @Autowired
    private OperationLogFacade operationLogFacade;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SiteFacade siteFacade;

    @Autowired
    private AtlasContractFacade atlasContractFacade;

    @Value("${delivery.nonDeliverableFactory:''}")
    private String cannotDeliveryFactoryList;

    @Value("${delivery.queryType:3}")
    private Integer deliveryQueryType;

    @Value("${sync.lkg.openQuery:0}")
    private Integer openLkgQuery;    // 是否开启lkg查询
    @Value("${sync.atlas.openQuery:0}")
    private Integer openAtlasQuery;  // 是否开启atlas查询

    /**
     * TODO 待优化
     */
    @Autowired
    private ITradeTicketQueryService tradeTickService;
    @Autowired
    private IContractSignQueryService iContractSignQueryService;
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    /**
     * 这个应该规划到合同域里面处理的，待细化  contractPriceLogicService 新的Logic调用
     */
    @Resource
    private IContractPriceService contractPriceService;
    /**
     * 合同域读服务
     */
    @Resource
    private ContractQueryDomainService contractQueryDomainService;
    @Resource
    private ContractSignQueryDomainService contractSignQueryDomainService;
    @Resource
    private ITtPriceService ttPriceService;

    /**
     * TT域读服务
     */

    @Override
    public Result queryContract(QueryDTO<ContractQO> queryDTO) {

        // 处理查询的前置条件
        ContractQO contractQO = queryDTO.getCondition();
        contractQO.setBuCode(StringUtils.isNotBlank(contractQO.getBuCode()) ? contractQO.getBuCode() : BuCodeEnum.ST.getValue());
        if (null != contractQO.getTriggerSys() && null != contractQO.getColumbusCustomerId()) {
            if (contractQO.getTriggerSys().equals(SystemEnum.COLUMBUS.getName())) {
                CustomerEntity customerEntity = customerFacade.getCustomerById(contractQO.getColumbusCustomerId());
                if (customerEntity.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
                    throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
                }
                CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
                if (cEmployEntity.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
                    throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
                }
            }
        }
        // 权限列表
        PermissionBO permissionBO = employFacade.querySitePermission(JwtUtils.getCurrentUserId(), contractQO.getGoodsCategoryId());
//        Map<Integer, List<Integer>> companyCustomerIdMap = permissionBO.getCompanyCustomerIdMap();
//        if (companyCustomerIdMap.isEmpty()) {
//            companyCustomerIdMap.put(-1, Collections.singletonList(-1));
//        }
//        contractQO.setCompanyCustomerIdMap(companyCustomerIdMap);
        contractQO.setSiteCodeList(permissionBO.getSiteCodeList());
        Result result = contractQueryDomainService.queryContract(queryDTO);
        // add by zengshl 哥伦布需要查询提货权合同涉及提货密码信息
        List<ContractVO> contractVOList = (List<ContractVO>) result.getData();
        contractVOList.forEach(item -> {
            if (ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(item.getContractNature())) {
                WarrantCancellationEntity cancellation = warrantFacade.queryWarrantCancellation(item.getContractCode());
                if (ObjectUtil.isNotEmpty(cancellation)) {
                    item.setDeliveryPassword(cancellation.getDeliveryPassword());
                    item.setWriteOffDate(cancellation.getWriteOffDate());
                    // 提货主体
                    item.setDeliveryCustomerName(cancellation.getDeliveryCustomerName());
                    // 买方主体
                    item.setCustomerName(cancellation.getCustomerName());
                }
            }
        });

        // 哥伦布统一调用三方接口
        if (SystemEnum.COLUMBUS.getName().equals(queryDTO.getCondition().getTriggerSys())) {
            for (ContractVO contractVO : contractVOList) {
                try {
                    ContractEntity contractEntity = getContractById(contractVO.getId());
                    setContractVOInfo(contractVO, contractEntity);
                } catch (Exception e) {
                    log.error("哥伦布合同详情查询失败，合同ID：{}，原因：{}", contractVO.getId(), e.getMessage());
                    for (ContractVO newContractVO : contractVOList) {
                        ContractEntity newContractEntity = getBasicContractById(newContractVO.getId());
                        setContractVOInfo(newContractVO, newContractEntity);
                    }
                    break;
                }
            }
        }

        result.setData(contractVOList);
        return result;
    }

    /**
     * 哥伦布合同详情查询
     *
     * @param contractVO     展示的合同信息
     * @param contractEntity 合同实体
     * @return 合同详情信息
     */
    private ContractVO setContractVOInfo(ContractVO contractVO, ContractEntity contractEntity) {

        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        // 已开单数量
        BigDecimal totalBillNum = contractEntity.getTotalBillNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBillNum();
        contractVO.setPriceNum(totalPriceNum);
        switch (typeEnum) {
            case YI_KOU_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(BigDecimal.ZERO)
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            case JI_CHA:
            case JI_CHA_ZAN_DING_JIA:
            case ZAN_DING_JIA:
                contractVO
                        // 未定价量
                        .setNotPriceNum(contractNum.subtract(totalPriceNum))
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
                break;
            default:
                break;
        }
        return contractVO;
    }

    /**
     * 处理合同其他业务字段抽离到APP层实现
     *
     * @param contractId
     * @return
     */
    @Override
    public Result<ContractDetailVO> getContractDetailById(String contractId) {
        ContractEntity contractEntity = contractQueryDomainService.getContractDetailById(contractId);
        // 详情其他信息处理
        return getContractDetail(contractEntity, DisableStatusEnum.ENABLE.getValue());
    }

    /**
     * 不获取LKG合同的内容
     *
     * @param contractId
     * @return
     */
    @Override
    public Result<ContractDetailVO> getBasicContractDetailById(String contractId) {
        ContractEntity contractEntity = contractQueryDomainService.getContractDetailById(contractId);
        // 详情其他信息处理
        return getContractDetail(contractEntity, DisableStatusEnum.DISABLE.getValue());
    }

    /**
     * 根据合同编码获取合同详情信息
     *
     * @param contractCode
     * @return
     */
    @Override
    public Result<ContractDetailVO> getContractDetailByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractQueryDomainService.getContractDetailByCode(contractCode);
        // 详情其他信息处理
        return getContractDetail(CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0) : null, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public ContractDetailInfoDTO getContractDetailInfoDTO(Integer id) {
        ContractDetailInfoDTO contractDetailInfoDTO = null;

        ContractEntity contractEntity = getContractById(id);
        if (null != contractEntity) {
            contractDetailInfoDTO = BeanConvertUtils.convert(ContractDetailInfoDTO.class, contractEntity);
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            contractDetailInfoDTO.setContractPriceDTO(BeanConvertUtils.convert(ContractPriceDTO.class, contractPriceEntity));
        }
        return contractDetailInfoDTO;
    }

    @Override
    public Result getContractUnitPriceDetail(String contractId) {
        return contractQueryDomainService.getContractUnitPriceDetail(contractId);
    }


    @Override
    public List<ContractRelativeDTO> getContractTraceList(Integer contractId) {
        return contractQueryDomainService.getContractTraceList(contractId);
    }

    @Override
    public List<ContractDeliveryVO> getDeliveryContractByContractId(Integer contractId) {
        return contractQueryDomainService.getDeliveryContractByContractId(contractId);
    }

    @Override
    public ContractEntity getContractById(Integer id) {
        // 处理LKG的数据信息-包含LGK的数据
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(id);

        // 根据账套获取不同系统的合同信息
        if (contractEntity != null) {
            String siteCode = contractEntity.getSiteCode();
            SiteEntity siteEntity = siteFacade.getSiteByCode(siteCode);
            if (siteEntity != null) {
                if (SyncSystemEnum.LINKINAGE.getValue().equals(siteEntity.getSyncSystem())) {
                    return getContractLkg(contractEntity);
                }

                if (SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem())) {
                    return getContractAtlas(siteEntity, contractEntity);
                }
            }
        }
        return contractEntity;
    }

    @Override
    public ContractEntity getBasicContractById(Integer id) {
        return contractQueryDomainService.getBasicContractById(id);
    }

    @Override
    public ContractEntity getBasicContractByCode(String contractCode) {
        return contractQueryDomainService.getBasicContractByCode(contractCode);
    }

    @Override
    public List<ContractEntity> getContractByWarrantCode(String warrantCode) {
        return contractQueryDomainService.getContractByWarrantCode(warrantCode);
    }

    @Override
    public List<ContractEntity> getContractByPurchase(ContractEntity purchaseContract) {
        return contractQueryDomainService.getContractByPurchase(purchaseContract);
    }

    @Override
    public Result queryContractStructure(QueryDTO<ContractBO> queryDTO) {
        IPage<ContractStructureEntity> iPage = contractQueryDomainService.queryContractStructure(queryDTO);
        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractStructureEntity -> {
            ContractVO contractVO = new ContractVO();
            ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractStructureEntity.getContractId());
            // 获取合同所属商务
            contractVO.setBusinessPersonName(IdNameConverter.getName(IdNameType.user_id_name, contractEntity.getOwnerId().toString()));
            BeanUtil.copyProperties(contractEntity, contractVO);
            // 获取客户合同url列表
            List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setCustomerContractUrl(customerContractUrls);
            // 获取合同模板url地址
            List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);
            // 获取品类编码
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
            contractVO.setCategoryCode(categoryEntity != null ? categoryEntity.getCode() : null);
            // 获取LDC合同url地址
            List<FileInfoEntity> ldcContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.ENABLE.getValue());
            contractVO.setLdcContractUrl(ldcContractUrls != null && ldcContractUrls.size() > 0 ? ldcContractUrls.get(0).getFileUrl() : null);
            // 交提货方式
            contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());
            // 重量质检
            contractVO.setWeightCheckName(contractEntity.getWeightCheckValue());
            // 目的地名
            contractVO.setDestinationName(contractEntity.getDestinationValue());
            // 根据createdBy获取employ信息
            EmployEntity employEntity = new EmployEntity();
            employEntity.setId(contractEntity.getCreatedBy()).setName(IdNameConverter.getName(IdNameType.user_id_name, contractEntity.getCreatedBy().toString()));
            contractVO.setCreatedEntity(employEntity);
            ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
            contractVO.setContractStructureVO(contractStructureDTO);
            return contractVO;
        }).collect(Collectors.toList());

        return Result.page(iPage, contractVOList);
    }

    @Override
    public Result pageContractsByDomainCode(QueryDTO<QueryContractDTO> queryDTO) {

        IPage<ContractEntity> page = contractQueryDomainService.queryContractsByDomainCode(queryDTO);
        QueryContractDTO queryContractDTO = queryDTO.getCondition();
        // 获取合同定价生效的信息
        List<ContractVO> assignableContracts = this.completeContract(page.getRecords(), queryContractDTO.getType(),
                queryContractDTO.getPriceAllocateId());
        return Result.page(page, assignableContracts);
    }

    @Override
    public List<ContractEntity> queryContractsByDomainCodeList(QueryContractDTO queryContractDTO) {
        return contractQueryDomainService.queryContractsByDomainCodeList(queryContractDTO);
    }

    @Override
    public Result queryContractsColumbus(QueryDTO<QueryContractDTO> queryDTO) {
        Integer companyId = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId();
        IPage<ContractEntity> iPage = contractQueryDomainService.queryContractsColumbus(queryDTO, companyId);
        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractEntity -> {
                    // 获取合同所属商务
                    ContractVO contractVO = new ContractVO();
                    BeanUtil.copyProperties(contractEntity, contractVO);
                    contractVO.setBusinessPersonName(IdNameConverter.getName(IdNameType.user_id_name, contractEntity.getOwnerId().toString()));
                    // 获取客户合同url列表
                    List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setCustomerContractUrl(customerContractUrls);
                    // 获取合同模板url地址
                    List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);
                    // 获取品类编码
                    CategoryEntity categoryDTO = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getGoodsCategoryId());
                    contractVO.setCategoryCode(categoryDTO != null ? categoryDTO.getCode() : null);
                    // 获取LDC合同url地址
                    List<FileInfoEntity> ldcContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), DisableStatusEnum.ENABLE.getValue());
                    contractVO.setLdcContractUrl(ldcContractUrls != null && ldcContractUrls.size() > 0 ? ldcContractUrls.get(0).getFileUrl() : null);
                    // 交提货方式
                    contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());
                    // 获取customer信息
                    CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getCustomerId());
                    contractVO.setCustomerDTO(customerDTO);
                    // 重量质检
                    contractVO.setWeightCheckName(contractEntity.getWeightCheckValue());
                    // 目的地名
                    contractVO.setDestinationName(contractEntity.getDestinationValue());
                    // 根据createdBy获取employ信息
                    EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
                    contractVO.setCreatedEntity(employEntity);
                    return contractVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, contractVOList);
    }

    @Override
    public Integer getContractIdByCode(String contractCode) {
        List<ContractEntity> contractEntityList = contractQueryDomainService.getByContractCode(contractCode);
        return CollectionUtil.isNotEmpty(contractEntityList) ? contractEntityList.get(0).getId() : null;
    }

    @Override
    public List<ContractStructureEntity> getValidStructureContract(List<Integer> contractIds) {
        List<ContractStructureEntity> structureList = new ArrayList<>();
        if (contractIds != null && contractIds.size() > 0) {
            structureList = contractQueryDomainService.queryContractStructureList(contractIds);
            for (int i = 0; i < structureList.size(); i++) {
                structureList.get(i).setPriceStartDate(structureList.get(i).getStartTime());
                structureList.get(i).setPriceEndDate(structureList.get(i).getEndTime());
            }
        }
        return structureList;
    }

    @Override
    public Result getContractPdfs(String contractId) {

        List<FileInfoVO> fileInfoVOs = new ArrayList<>();
        // 获取有效电子合同（初始合同）
        List<FileInfoEntity> enContractPdfSignatureLdcOriginalFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
        List<FileInfoVO> enableLdcOriginalFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(enContractPdfSignatureLdcOriginalFileInfos, DisableStatusEnum.ENABLE.getValue());
        fileInfoVOs.addAll(enableLdcOriginalFileInfoVOS);

        // 获取有效电子合同（客户签章）
        List<FileInfoEntity> enContractPdfSignatureCustomerFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
        List<FileInfoVO> enableCusFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(enContractPdfSignatureCustomerFileInfos, DisableStatusEnum.ENABLE.getValue());
        fileInfoVOs.addAll(enableCusFileInfoVOS);

        // 获取有效电子合同（初始合同）
        List<FileInfoEntity> disContractPdfSignatureLdcOriginalFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.DISABLE.getValue());
        List<FileInfoVO> disableLdcOriginalFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(disContractPdfSignatureLdcOriginalFileInfos, DisableStatusEnum.DISABLE.getValue());
        fileInfoVOs.addAll(disableLdcOriginalFileInfoVOS);

        // 获取失效电子合同（客户签章）
        List<FileInfoEntity> disContractPdfSignatureCustomerFileInfos = fileBusinessFacade.getFileInfoByBizIdAndType(Integer.valueOf(contractId), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.DISABLE.getValue());
        List<FileInfoVO> disableCusFileInfoVOS = this.fileInfoEntityCovertFileInfoVO(disContractPdfSignatureCustomerFileInfos, DisableStatusEnum.DISABLE.getValue());
        fileInfoVOs.addAll(disableCusFileInfoVOS);
        return Result.success(fileInfoVOs);
    }

    @Override
    public boolean canSign(String contractId) {
        // TODO TT的数据信息的接口
        TradeTicketEntity ticketEntity = tradeTickService.getBySignId(contractId);
        // TT是否存在
        if (ticketEntity == null) {
            throw new BusinessException(ResultCodeEnum.TT_IS_NOT_EXIST);
        }
        if (TTTypeEnum.SPLIT.getType().equals(ticketEntity.getType())) {
            TradeTicketEntity tradeTicketEntitie = tradeTickService.getByGroupId(ticketEntity.getGroupId(), ticketEntity.getContractId());
            // TT状态审批通过
            if (!ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                    && !ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue())) {
                throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
            }
            // TT状态审批通过
            if ((!tradeTicketEntitie.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                    && !tradeTicketEntitie.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue()))) {
                throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
            }
        }
        // TT状态审批通过
        if (!ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.APPROVE.getValue())
                && !ticketEntity.getApprovalStatus().equals(TTApproveStatusEnum.WITHOUT_APPROVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.TT_APPROVAL_NOT_COMPLETED);
        }
        return true;
    }

    @Override
    public List<ContractEntity> queryContractsFuturesNum(ContractFuturesDTO contractFuturesDTO) {
        return contractQueryDomainService.queryContractsFuturesNum(contractFuturesDTO);
    }

    @Override
    public List<ContractEntity> queryContractsFutures(ContractFuturesDTO contractFuturesDTO) {
        return contractQueryDomainService.queryContractsFutures(contractFuturesDTO);
    }

    @Override
    public Result futureContracts(QueryDTO<ContractFuturesDTO> queryDTO) {
        IPage<ContractEntity> iPage = contractQueryDomainService.futureContracts(queryDTO);
        List<ContractVO> contractVOList = iPage.getRecords().stream().map(contractEntity -> {
            ContractVO contractVO = new ContractVO();
            BeanUtil.copyProperties(contractEntity, contractVO);
            // 交提货方式
            contractVO.setDeliveryTypeName(DeliveryTypeEnum.getByValue(contractEntity.getDeliveryType()).getDesc());
            // 目的地名
            contractVO.setDestinationName(contractEntity.getDestinationValue());
            return contractVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, contractVOList);
    }

    @Override
    public List<ContractModifyVO> getContractModifyLog(ContractModifyDTO contractModifyDTO) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractModifyDTO.getContractId());
        if (contractEntity == null) {
            return new ArrayList<>();
        }
        List<TradeTicketEntity> tradeTicketEntityList = tradeTickService.queryChangeLogByContractId(contractModifyDTO.getContractId(), contractEntity.getContractCode());
        List<ContractModifyVO> contractModifyVOList = tradeTicketEntityList.stream().map(i -> {
            ContractModifyVO contractModifyVO = new ContractModifyVO();
            if (i.getIsDeleted().equals(IsDeletedEnum.DELETED.getValue())) {

                TradeTicketEntity newTradeTicketEntity = tradeTickService.getByGroupId(i.getGroupId(), i.getId());
                if (newTradeTicketEntity != null) {
                    contractModifyVO
                            .setTtType(newTradeTicketEntity.getType())
                            .setContractType(newTradeTicketEntity.getContractType())
                            .setTradeType(newTradeTicketEntity.getTradeType())
                            .setContractNum(newTradeTicketEntity.getChangeContractNum() == null ? null : "-" + i.getChangeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                            .setPriceNum(null)
                            .setTtId(newTradeTicketEntity.getId())
                            .setTtCode(newTradeTicketEntity.getCode())
                            .setTtStatus(newTradeTicketEntity.getStatus())
                            .setSignId(newTradeTicketEntity.getSignId())
                            .setProtocolCode(newTradeTicketEntity.getProtocolCode())
                            .setCreatedAt(newTradeTicketEntity.getCreatedAt())
                            .setCustomerName(newTradeTicketEntity.getCustomerName())
                            .setIsDeleted(newTradeTicketEntity.getIsDeleted())
                            .setSonContractCode(newTradeTicketEntity.getContractCode())
                            .setSonContractId(newTradeTicketEntity.getContractId())
                            .setChangeContractNum(newTradeTicketEntity.getChangeContractNum())
                            .setApprovalStatus(newTradeTicketEntity.getApprovalStatus() == null ? TTApproveStatusEnum.WITHOUT_APPROVE.getDesc() : TTApproveStatusEnum.getDescByValue(newTradeTicketEntity.getApprovalStatus()))
                            .setCancelReason(null == newTradeTicketEntity.getCancelReason() ? "" : newTradeTicketEntity.getCancelReason())
                    ;
                }

                if (ContractTradeTypeEnum.TRANSFER_ALL.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                ) {
                    contractModifyVO.setIsDeleted(IsDeletedEnum.DELETED.getValue());
                }

                if (ContractSalesTypeEnum.SALES.getValue() == i.getSalesType()) {
                    contractModifyVO.setCustomerName(i.getCustomerName());
                } else {
                    contractModifyVO.setCustomerName(i.getSupplierName());
                }

            } else {
                if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == i.getTradeType() || ContractTradeTypeEnum.SPLIT_NORMAL.getValue() == i.getTradeType()) {
                    TradeTicketEntity tradeTicketEntity = tradeTickService.getByGroupId(i.getGroupId(), i.getId());
                    if (null != tradeTicketEntity) {
                        TradeTicketEntity originalEntity = i;
                        i = tradeTicketEntity;
                        i.setTradeType(originalEntity.getTradeType());
                        contractModifyVO.setSonContractId(i.getContractId());
                        contractModifyVO.setSonContractCode(i.getContractCode());
                    }
                }

                contractModifyVO
                        .setTtType(i.getType())
                        .setContractType(i.getContractType())
                        .setTradeType(i.getTradeType())
                        .setContractNum((i.getChangeContractNum() == null || BigDecimal.ZERO.compareTo(i.getChangeContractNum()) == 0) ? null : i.getChangeContractNum().setScale(3, RoundingMode.HALF_UP).toPlainString())
                        .setPriceNum(null)
                        .setTtId(i.getId())
                        .setTtCode(i.getCode())
                        .setTtStatus(i.getStatus())
                        .setSignId(i.getSignId())
                        .setProtocolCode(i.getProtocolCode())
                        .setCreatedAt(i.getCreatedAt())
                        .setCustomerName(i.getCustomerName())
                        .setIsDeleted(i.getIsDeleted())
                        .setChangeContractNum(i.getChangeContractNum())
                        .setSalesType(i.getSalesType())
                        .setApprovalStatus(i.getApprovalStatus() == null ? TTApproveStatusEnum.WITHOUT_APPROVE.getDesc() : TTApproveStatusEnum.getDescByValue(i.getApprovalStatus()))
                        .setCancelReason(null == i.getCancelReason() ? "" : i.getCancelReason())
                ;
                if (ContractSalesTypeEnum.SALES.getValue() == i.getSalesType()) {
                    contractModifyVO.setCustomerName(i.getCustomerName());
                } else {
                    contractModifyVO.setCustomerName(i.getSupplierName());
                }
                if (ContractTradeTypeEnum.NEW.getValue() != i.getTradeType() && null != contractModifyVO.getContractNum()) {
                    contractModifyVO.setContractNum("-" + contractModifyVO.getContractNum());
                }
                if (ContractTradeTypeEnum.REVISE_NORMAL.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.TRANSFER_ALL.getValue() == i.getTradeType()
                ) {
                    contractModifyVO.setContractNum(null);
                }
                if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == i.getTradeType()
                ) {
                    BigDecimal afterContractNum = i.getAfterContractNum() == null ? BigDecimal.ZERO : i.getAfterContractNum();
                    BigDecimal beforeContractNum = i.getBeforeContractNum() == null ? BigDecimal.ZERO : i.getBeforeContractNum();
                    String contractNum = afterContractNum.subtract(beforeContractNum).setScale(3, RoundingMode.HALF_UP).toPlainString();
                    contractModifyVO.setContractNum(contractNum);
                    contractModifyVO.setSonContractCode(i.getContractCode());
                    contractModifyVO.setSonContractId(i.getContractId());
                }
                if (ContractTradeTypeEnum.PRICE.getValue() == i.getTradeType()
                        || ContractTradeTypeEnum.FIXED.getValue() == i.getTradeType()) {
                    BigDecimal changeContractNum = i.getChangeContractNum() == null ? BigDecimal.ZERO : i.getChangeContractNum();
                    contractModifyVO.setPriceNum(changeContractNum.setScale(3, RoundingMode.HALF_UP).toPlainString());
                    contractModifyVO.setContractNum(null);
                }
                // 合同关闭变更记录合同数量为“-”
                if (ContractTradeTypeEnum.CLOSED.getValue() == i.getTradeType()) {
                    contractModifyVO.setContractNum("-" + contractModifyVO.getContractNum());
                }

            }

            //创建人
            if (null != i.getCreatedBy()) {
                EmployEntity employEntity = employFacade.getEmployById(i.getCreatedBy());
                if (null != employEntity) {
                    contractModifyVO.setCreatedBy(employEntity.getName());
                }
            }
            return contractModifyVO;
        }).collect(Collectors.toList());

        List<ContractModifyVO> list = contractModifyVOList.stream()
                .map(p -> {
                    //查询协议状态
                    if (String.valueOf(TTStatusEnum.WAITING.getType()).equals(String.valueOf(p.getTtStatus()))) {
                        //待修改提交区的协议为异常状态
                        p.setProtocolStatus(ContractSignStatusEnum.ABNORMAL.getDesc());
                    } else {
                        ContractSignEntity contractSignDetail = iContractSignQueryService.getContractSignDetailByTtId(p.getTtId());
                        if (contractSignDetail != null) {
                            Integer status = contractSignDetail.getStatus();
                            p.setProtocolStatus(ContractSignStatusEnum.getEnumByValue(status).getDesc());
                        }
                    }
                    return p;
                })
                .filter(i -> !IsDeletedEnum.DELETED.getValue().equals(i.getIsDeleted()))
                .filter(i -> i.getTtId() != null)
                .sorted(Comparator.comparing(ContractModifyVO::getTtId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list) && ContractTradeTypeEnum.NEW.getValue() != list.get(0).getTradeType()) {
            String changeContractNumber = list.get(0).getChangeContractNum() == null ? null : list.get(0).getChangeContractNum().toPlainString();
            list.get(0).setContractNum(changeContractNumber);
            list.get(0).setTtType(TTTypeEnum.NEW.getType());
        }
        return list;
    }

    @Override
    public ContractModifyNumVO getContractModifyNumInfo(Integer contractId) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractId);
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        return getContractNumInfo(contractEntity, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public List<ConfirmPriceVO> getConfirmPriceList(Integer contractId) {
        List<ConfirmPriceVO> confirmPriceVOList = new ArrayList<>();
        TTPriceQO ttPriceQO = new TTPriceQO();
        ttPriceQO.setContractId(contractId);
        List<TTPriceEntity> confirmPriceList = ttQueryLogicService.fetchTTPriceEntities(ttPriceQO);
        for (TTPriceEntity ttPriceEntity : confirmPriceList) {
            ConfirmPriceVO confirmPriceVO = new ConfirmPriceVO();
            confirmPriceVO.setContractId(ttPriceEntity.getContractId())
                    .setTtPriceId(ttPriceEntity.getId())
                    .setConfirmPrice(ttPriceEntity.getPrice())
                    .setTtId(ttPriceEntity.getTtId())
                    .setConfirmNum(ttPriceEntity.getNum());
            confirmPriceVOList.add(confirmPriceVO);
        }
        return confirmPriceVOList;
    }

    @Override
    public List<ContractEntity> getContractListByIds(List<Integer> contractIdList) {
        List<ContractEntity> deliveryApplyContractList = contractQueryDomainService.getContractListByIds(contractIdList);
        return getContractDeliveryList(deliveryApplyContractList, true);
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO) {
        // 商品不可提货list
        List<Integer> cannotDeliveryGoodsIdList = goodsFacade.queryCannotDeliveryGoodsIdList();
        // 工厂不可提货list
        List<String> cannotDeliveryFactoryList = Arrays.asList(this.cannotDeliveryFactoryList.split(","));
        // 提货类型不能为“送货”
        List<Integer> cannotDeliveryTypeIdList = deliveryTypeFacade.getSendDeliveryTypeIdList();

        List<ContractEntity> deliveryApplyContractList = contractQueryDomainService.getDeliveryApplyContractList(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                cannotDeliveryFactoryList,
                cannotDeliveryTypeIdList);

        return getContractDeliveryList(deliveryApplyContractList, false);
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractListForAtlas(DeliveryApplyContractQO deliveryApplyContractQO) {
        // 商品不可提货list
        List<Integer> cannotDeliveryGoodsIdList = goodsFacade.queryCannotDeliveryGoodsIdList();
        List<ContractEntity> deliveryApplyContractList = contractQueryDomainService.getDeliveryApplyContractList(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                null,
                null);
        return deliveryApplyContractList;
    }

    @Override
    public List<ContractEntity> getDeliveryApplyContractGroup(DeliveryApplyContractQO deliveryApplyContractQO) {

        // 商品不可提货list
        List<Integer> cannotDeliveryGoodsIdList = goodsFacade.queryCannotDeliveryGoodsIdList();
        // 工厂不可提货list
        List<String> cannotDeliveryFactoryList = Arrays.asList(this.cannotDeliveryFactoryList.split(","));
        // 提货类型不能为“送货”
        List<Integer> cannotDeliveryTypeIdList = deliveryTypeFacade.getSendDeliveryTypeIdList();
        List<ContractEntity> deliveryApplyContractList = contractQueryDomainService.getDeliveryApplyContractGroup(deliveryApplyContractQO,
                cannotDeliveryGoodsIdList,
                cannotDeliveryFactoryList,
                cannotDeliveryTypeIdList);

        // 筛选LKG账套的合同
        Result<List<SiteEntity>> result = siteFacade.getSiteListBySyncSystem(SyncSystemEnum.LINKINAGE.getValue());
        if (result.isSuccess()) {
            List<SiteEntity> siteEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), SiteEntity.class);

            // 过滤deliveryApplyContractList中siteCode不在siteEntityList中的数据
            Set<String> siteCodes = siteEntityList.stream()
                    .map(SiteEntity::getCode)
                    .collect(Collectors.toSet());

            deliveryApplyContractList = deliveryApplyContractList.stream()
                    .filter(contractEntity -> siteCodes.contains(contractEntity.getSiteCode()))
                    .collect(Collectors.toList());
        }

        // 批量获取lkg合同信息
        List<ContractEntity> newApplyContractList = getBatchLkgContractList(deliveryApplyContractList);
        // 对合同进行商品名称/提货工厂/供应商名称进行分组然后统计合同的数量
        Map<String, List<ContractEntity>> collect = newApplyContractList.stream()
                .collect(Collectors.groupingBy(contractEntity -> contractEntity.getGoodsName()
                        + contractEntity.getDeliveryFactoryCode()
                        + contractEntity.getSupplierName()));
        List<ContractEntity> contractEntityList = new ArrayList<>();
        collect.forEach((key, value) -> {
            ContractEntity contractEntity = new ContractEntity()
                    .setGoodsId(value.get(0).getGoodsId())
                    .setGoodsName(value.get(0).getGoodsName())
                    .setDeliveryFactoryCode(value.get(0).getDeliveryFactoryCode())
                    .setSupplierId(value.get(0).getSupplierId())
                    .setSupplierName(value.get(0).getSupplierName());

            // 可提货数量
            BigDecimal canDeliveryNum = BigDecimal.ZERO;
            // 可提货数量 = 合同数量 - 开单数量 - 申请提货数量
            for (ContractEntity entity : value) {
                canDeliveryNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, canDeliveryNum, entity.getContractNum());
                canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, BigDecimalUtil.max(entity.getTotalBillNum(), entity.getTotalDeliveryNum(), entity.getAllocateNum()));
                canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, entity.getApplyDeliveryNum());
            }

            if (BigDecimalUtil.isGreaterThanZero(canDeliveryNum)) {
                contractEntityList.add(contractEntity.setCanDeliveryNum(canDeliveryNum));
            }
        });
        // 按照商品的id倒序排序
        contractEntityList.sort(Comparator.comparing(ContractEntity::getGoodsId).reversed());
        return contractEntityList;
    }

    @Override
    public List<OperationDetailEntity> getCloseTailNumRecord(String contractCode) {
        Result result = operationLogFacade.queryOperationDetailByReferBizCode(contractCode, OperationSourceEnum.EMPLOYEE.getValue());
        if (result.isSuccess()) {
            List<OperationDetailEntity> operationDetailList = JSON.parseArray(JSON.toJSONString(result.getData()), OperationDetailEntity.class);
            return operationDetailList.stream().filter(operationDetailEntity ->
                            operationDetailEntity.getBizCode().equals(LogBizCodeEnum.CLOSE_TAIL_NUM.getBizCode()) ||
                                    operationDetailEntity.getBizCode().equals(LogBizCodeEnum.CANCEL_CLOSE_TAIL_NUM.getBizCode()))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        return contractPriceService.getContractPriceEntityContractId(contractId);
    }

    @Override
    public List<ContractVO> getCargoRightsContractById(Integer contractId) {
        List<ContractEntity> contractEntityList = contractQueryDomainService.getCargoRightsContractById(contractId);
        return contractEntityList.stream().map(contractEntity -> {
            ContractVO contractVO = BeanConvertUtils.convert(ContractVO.class, contractEntity);
            if (ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(contractEntity.getContractNature())) {
                WarrantCancellationEntity cancellation = warrantFacade.queryWarrantCancellation(contractEntity.getContractCode());
                if (ObjectUtil.isNotEmpty(cancellation)) {
                    contractVO.setDeliveryPassword(cancellation.getDeliveryPassword());
                    contractVO.setWriteOffDate(cancellation.getWriteOffDate());
                }
            }
            return contractVO;
        }).collect(Collectors.toList());
    }

    @Override
    public String genNewContractCode(String salesName, Integer salesType, Integer goodsCategoryId) {
        return contractQueryDomainService.genNewContractCode(salesName, salesType, goodsCategoryId);
    }

    @Override
    public List<String> judgeCategory3InProcessContractForSite(List<Integer> category3List, String siteCode) {
        return contractQueryDomainService.judgeCategory3InProcessContractForSite(category3List, siteCode);
    }

    @Override
    public BigDecimal getContractBlockedNum(String contractCode) {
        // 获取合同实体
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractByCode(contractCode);

        // 如果合同实体为空，返回零
        if (contractEntity == null) {
            return BigDecimal.ZERO;
        }

        // 获取合同状态
        int contractStatus = contractEntity.getStatus();

        // 修改中或关闭中的合同全量锁定
        if (contractStatus == ContractStatusEnum.MODIFYING.getValue() || contractStatus == ContractStatusEnum.CLOSED.getValue()) {
            return contractEntity.getContractNum();
        }

        // 拆分中的合同锁定部分
        if (contractStatus == ContractStatusEnum.SPLITTING.getValue()) {
            // 查询未完成的拆分合同
            List<ContractSignEntity> signEntityList = iContractSignQueryService.queryIncompleteByContractId(
                    Collections.singletonList(contractEntity.getId()),
                    Collections.singletonList(TTTypeEnum.SPLIT.getType()));

            // 如果没有未完成的拆分合同，返回零
            if (CollectionUtils.isEmpty(signEntityList)) {
                return BigDecimal.ZERO;
            }

            // 使用流直接累加changeContractNum
            return signEntityList.stream()
                    .map(signEntity -> tradeTickService.getByTtId(signEntity.getTtId()))  // 获取对应的TradeTicketEntity
                    .filter(Objects::nonNull)  // 过滤null值
                    .map(TradeTicketEntity::getChangeContractNum)  // 获取changeContractNum
                    .filter(Objects::nonNull)  // 过滤null的changeContractNum
                    .reduce(BigDecimal.ZERO, BigDecimal::add);  // 累加changeContractNum
        }

        // 对于其他状态，返回零
        return BigDecimal.ZERO;
    }

    @Override
    public ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        return contractQueryDomainService.getContractHistoryEntity(contractId, mainVersion);
    }

    @Override
    public ContractMdmInfoDTO getContractMdmInfo(String contractCode) {
        return contractQueryDomainService.getContractMdmInfo(contractCode);
    }

    /**
     * 获取合同具体的详情信息
     *
     * @param contractEntity
     * @return
     */
    private Result<ContractDetailVO> getContractDetail(ContractEntity contractEntity, Integer syncStatus) {
        ContractDetailVO contractDetailVO = new ContractDetailVO();
        BeanUtil.copyProperties(contractEntity, contractDetailVO);
        CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? contractEntity.getCustomerId() : contractEntity.getSupplierId());
        if (null != customerDTO) {
            contractDetailVO.setEnterprise(customerDTO.getEnterprise())
                    .setLkgCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
            contractDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());

        }
        contractDetailVO.setShipWarehouseName(contractEntity.getShipWarehouseValue());
        WarehouseEntity factoryWarehouseEntity = this.getFactoryWarehouse(contractEntity.getShipWarehouseId());
        if (factoryWarehouseEntity != null) {
            contractDetailVO.setShipWarehouseName(StringUtils.isNotBlank(contractEntity.getShipWarehouseValue()) ? contractEntity.getShipWarehouseValue() : factoryWarehouseEntity.getName())
                    .setShipWarehouseAddress(factoryWarehouseEntity.getDeliveryPoint())
                    .setLkgWarehouseCode(factoryWarehouseEntity.getCode());
        }
        CategoryEntity category2 = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());
        CategoryEntity category3 = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory3());
        contractDetailVO.setCategory2Name(null == category2 ? "" : category2.getName())
                .setCategory3Name(null == category3 ? "" : category3.getName());
        // 目的地名
        contractDetailVO.setDestinationName(contractEntity.getDestinationValue());
        // 交提货方式
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        contractDetailVO.setDeliveryTypeEntity(deliveryTypeEntity)
                .setDeliveryTypeName(deliveryTypeEntity != null ? deliveryTypeEntity.getName() : "");
        // 商品名称
        contractDetailVO.setGoodsCategoryName(contractEntity.getGoodsName());
        // 获取合同模板url地址
        List<FileInfoEntity> contractPdfOriginalUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), DisableStatusEnum.ENABLE.getValue());
        contractDetailVO.setContractPdfOriginalUrl(contractPdfOriginalUrls != null && contractPdfOriginalUrls.size() > 0 ? contractPdfOriginalUrls.get(0).getFileUrl() : null);
        // 获取客户合同url列表
        List<FileInfoEntity> customerContractUrls = fileBusinessFacade.getFileInfoByBizIdAndType(contractEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), DisableStatusEnum.ENABLE.getValue());
        contractDetailVO.setCustomerContractUrl(customerContractUrls);
        // 重量质检
        contractDetailVO.setWeightCheckName(contractEntity.getWeightCheckValue());
        // 袋皮扣重
        contractDetailVO.setPackageWeightName(contractEntity.getPackageWeightValue());
        contractDetailVO.setInvoiceType(contractEntity.getInvoiceType());
        contractDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(contractEntity.getInvoiceType()).getDesc());
        //企标文件
        if (null != contractEntity.getStandardFileId() && contractEntity.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(contractEntity.getStandardFileId());
            contractDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }
        // TODO 需要优化处理 获取最新的tt与协议的编号 由TT域提供
        TradeTicketEntity tradeTicketEntity = tradeTickService.getCanModifyByContractId(contractEntity.getId());
        if (null != tradeTicketEntity) {
            contractDetailVO.setTtId(tradeTicketEntity.getId());
            contractDetailVO.setTtCode(tradeTicketEntity.getCode());
            contractDetailVO.setTtCreatedAt(tradeTicketEntity.getCreatedAt());
            contractDetailVO.setSignId(tradeTicketEntity.getSignId());
            contractDetailVO.setProtocolCode(tradeTicketEntity.getProtocolCode());
        }
        // 所属商务
        EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
        contractDetailVO.setBusinessPersonName(businessPerson != null ? businessPerson.getName() : null);
        //创建人
        if (null != contractEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
            contractDetailVO.setCreateByName(employEntity != null ? employEntity.getName() : null);
        }
        // 可注销量
        if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())
                && ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType()) {
            contractDetailVO.setCanCancelCount(contractEntity.getContractNum()
                    .subtract(contractEntity.getWarrantCancelCount()).subtract(contractEntity.getTotalBuyBackNum()));
        }


        // TODO 合同对应价格信息 - 规划到TT域读服务里面 - 【待细化】
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailVO priceDetailVO = BeanConvertUtils.map(PriceDetailVO.class, contractPriceEntity);
        contractDetailVO.setPriceDetailVO(priceDetailVO);

        //补充信息-标签ID集合
        if (!StringUtils.isBlank(contractEntity.getTagConfigIds())) {

            List<String> tagConfigIds = new ArrayList<>();
            String[] tagIdList = contractEntity.getTagConfigIds().split(",");

            for (String tagId : tagIdList) {
                SystemRuleItemEntity systemRuleItem = systemRuleFacade.getRuleItemById(Integer.parseInt(tagId));
                tagConfigIds.add(systemRuleItem.getRuleValue());
            }
            contractDetailVO.setTagConfigList(tagConfigIds);
        }
        // 结构化合同规划到合同读服务里面去
        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {
            ContractStructureEntity contractStructureEntity = contractQueryDomainService.getContractStructure(contractEntity.getId());
            ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
            if (contractStructureDTO != null) {
                if (StringUtils.isBlank(contractStructureEntity.getStructureName())) {
                    String structureName = structureRuleFacade.getNameById(contractStructureEntity.getStructureType());
                    contractStructureDTO.setStructureTypeName(structureName);
                } else {
                    contractStructureDTO.setStructureTypeName(StructureCodeUtil.numToCode(contractStructureEntity.getStructureType()) + "-" + contractStructureEntity.getStructureName());
                }
            }
            contractStructureDTO.setSiteCode(contractDetailVO.getSiteCode());
            contractStructureDTO.setSiteName(contractDetailVO.getSiteName());
            contractStructureDTO.setFutureCode(contractDetailVO.getFutureCode());
            contractDetailVO.setContractStructureVO(contractStructureDTO);
        }

        // TODO 合同数量,调用LKG,仓单合同是否调用
        ContractModifyNumVO contractNumInfo = getContractNumInfo(contractEntity, syncStatus);
        BeanUtils.copyProperties(contractNumInfo, contractDetailVO);

        //case-1003202 回购场景一口价报文Pricing Method处理 Author:Mr 2025-05-15 Start
        // 开单数量
        BigDecimal maxNum = contractEntity.getTotalBillNum();
        // 已回购数量
        BigDecimal buyBackedNum = contractEntity.getTotalBuyBackNum() == null ? BigDecimal.ZERO : contractEntity.getTotalBuyBackNum();
        // 可回购数量
        BigDecimal canBuyBackNum = contractEntity.getContractNum().subtract(buyBackedNum);
        // 基差暂定价可回购数量为已定价部分
        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
            BigDecimal pricedNum = contractEntity.getTotalPriceNum() == null ? BigDecimal.ZERO : contractEntity.getTotalPriceNum();
            canBuyBackNum = pricedNum.subtract(buyBackedNum);
            maxNum = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getAllocateNum());
        }
        // 需要再扣除已注销数量
        contractDetailVO.setBuyBackNum(canBuyBackNum.subtract(BigDecimalUtil.max(maxNum, contractEntity.getWarrantCancelCount())));
        //case-1003202 回购场景一口价报文Pricing Method处理 Author:Mr 2025-05-15 end

        // 可关闭数量
        BigDecimal closeNum = contractEntity.getContractNum().subtract(contractEntity.getTotalDeliveryNum());

        if (contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            closeNum = contractEntity.getContractNum().subtract(BigDecimalUtil.max(contractEntity.getTotalPriceNum(), contractEntity.getTotalBillNum()));
        }

        // 获取仓单类型和结算类型
        if (ObjectUtil.isNotEmpty(contractEntity.getWarrantId()) && contractEntity.getWarrantId() > 0) {
            Result result = warrantFacade.queryWarrantByID(contractEntity.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            if (ObjectUtil.isNotEmpty(warrantEntity)) {
                contractDetailVO.setWarrantType(warrantEntity.getCategory())
                        .setDepositPaymentType(warrantEntity.getDepositPaymentType());

                // 采购仓单的可注销量-仓单持有量
                if (BuCodeEnum.WT.getValue().equals(contractDetailVO.getBuCode())
                        && ContractSalesTypeEnum.PURCHASE.getValue() == contractDetailVO.getSalesType()
                        && contractDetailVO.getCanCancelCount().compareTo(BigDecimal.ZERO) > 0
                        && contractDetailVO.getCanCancelCount().compareTo(warrantEntity.getHoldCount()) > 0) {
                    contractDetailVO.setCanCancelCount(warrantEntity.getHoldCount());
                }
            }
        }

        contractDetailVO.setCloseNum(closeNum);

        // 回购原合同的编号
        if (ContractActionEnum.BUYBACK.getActionValue() == contractEntity.getContractSource()) {
            ContractEntity parentContract = contractQueryDomainService.getBasicContractById(contractEntity.getParentId());
            if (null != parentContract) {
                contractDetailVO.setBuyBackContractCode(parentContract.getContractCode());
                contractDetailVO.setBuyBackContractId(parentContract.getId());
            }
        }

        // 合同详情按钮展示
        setShowButton(contractEntity, contractDetailVO);
        contractDetailVO.setPriceStartTime(DateTimeUtil.parseTimeStamp2359(contractDetailVO.getPriceStartTime()));

        // 付款条件代码
        contractDetailVO.setPayConditionCode(this.getPayConditionCode(contractDetailVO.getPayConditionId()));

        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 Start
        // String key = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
        // Object o = redisUtil.get(key);
        // if (Objects.isNull(o)) {
        //     contractDetailVO.setPriceCompleteStatus(0);
        // } else {
        //     contractDetailVO.setPriceCompleteStatus(1);
        // }
        contractDetailVO.setPriceCompleteStatus(contractEntity.getIsPricingCompleted() == null ? 0 : contractEntity.getIsPricingCompleted());
        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 End

        contractDetailVO.setUsage(contractEntity.getUsage());
        if (contractEntity.getUsage() != null) {
            contractDetailVO.setUsageString(UsageEnum.getDescByValue(contractEntity.getUsage()));
        }
        SiteEntity siteEntity = siteFacade.getSiteByCode(contractEntity.getSiteCode());
        if (null != siteEntity) {
            contractDetailVO.setSiteName(siteEntity.getName());
            contractDetailVO.setSyncSystem(siteEntity.getSyncSystem());
        }

        // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 start
        // 计算未开单量和未到货量
        calculateNotBillAndDeliveryNum(contractDetailVO);
        // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 end

        return Result.success(contractDetailVO);
    }

    /**
     * 获取付款条件代码
     *
     * @param payConditionId
     * @return
     */
    private String getPayConditionCode(Integer payConditionId) {
        String payConditionCode = "";
        Result payConditionResult = payConditionFacade.getPayConditionById(payConditionId);
        if (null != payConditionResult && ResultCodeEnum.OK.getCode() == payConditionResult.getCode()) {
            PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(payConditionResult.getData()), PayConditionEntity.class);
            payConditionCode = payConditionEntity.getCode();
        }
        return payConditionCode;
    }

    /**
     * 获取电子文件信息
     *
     * @param fileInfoEntityList
     * @param status
     * @return
     */
    private List<FileInfoVO> fileInfoEntityCovertFileInfoVO(List<FileInfoEntity> fileInfoEntityList, Integer status) {
        List<FileInfoVO> fileInfoVOS = new ArrayList<>();
        for (FileInfoEntity fileInfoEntity : fileInfoEntityList) {
            FileInfoVO fileInfoVO = new FileInfoVO();
            BeanUtil.copyProperties(fileInfoEntity, fileInfoVO);
            fileInfoVO.setStatus(status);
            fileInfoVOS.add(fileInfoVO);
        }
        return fileInfoVOS;
    }

    /**
     * 获取合同数量信息
     *
     * @param contractEntity
     * @return
     */
    private ContractModifyNumVO getContractNumInfo(ContractEntity contractEntity, Integer syncStatus) {
        ContractModifyNumVO contractModifyNumVO = new ContractModifyNumVO();
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = BigDecimal.ZERO;
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;

        // 是否启用LKG
        if (syncStatus.equals(DisableStatusEnum.ENABLE.getValue())) {

            ContractEntity objectContract = getContractById(contractEntity.getId());
            // 已开单数量
            totalBillNum = objectContract.getTotalBillNum();
            // 已提数量
            totalDeliveryNum = objectContract.getTotalDeliveryNum();
            // 调拨数量
            allocateNum = objectContract.getAllocateNum();
        }

        // BUGFIX：Case-1003256-合同已全量开单，仍可继续拆分导致合同超开 Author: Mr 2025-06-10 Start
        // 已拆分未合规数量
        BigDecimal splitNotCompliantNum = BigDecimal.ZERO;
        // 是否存在拆分中的合同
        SiteEntity siteEntity = siteFacade.getSiteByCode(contractEntity.getSiteCode());
        if (SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem())) {
            List<ContractEntity> sonContractList = contractQueryDomainService.getContractByPid(contractEntity.getId());
            if (CollectionUtil.isNotEmpty(sonContractList)) {
                for (ContractEntity sonContract : sonContractList) {
                    // 父合同协议
                    List<ContractSignEntity> contractSignEntityList = contractSignQueryDomainService.queryIncompleteByContractId(
                            Collections.singletonList(sonContract.getId()),
                            Collections.singletonList(TTTypeEnum.SPLIT.getType()));

                    // BUGFIX：Case-1003308-合同可拆分数量不正确 Author: Mr 2025-06-24 Start
                    // 只筛选tradeType为新增的协议
                    contractSignEntityList = contractSignEntityList.stream()
                            .filter(contractSignEntity -> contractSignEntity.getTradeType() == ContractTradeTypeEnum.NEW.getValue())
                            .collect(Collectors.toList());
                    // BUGFIX：Case-1003308-合同可拆分数量不正确 Author: Mr 2025-06-24 End

                    if (CollectionUtil.isNotEmpty(contractSignEntityList)) {
                        splitNotCompliantNum = splitNotCompliantNum.add(sonContract.getContractNum());
                    }
                }
            }
        }
        contractModifyNumVO.setSplitNotCompliantNum(splitNotCompliantNum);
        // BUGFIX：Case-1003256-合同已全量开单，仍可继续拆分导致合同超开 Author: Mr 2025-06-10 End

        // 处理合同数量
        contractEntity.setTotalBillNum(totalBillNum);
        contractEntity.setTotalDeliveryNum(totalDeliveryNum);
        contractEntity.setAllocateNum(allocateNum);

        contractModifyNumVO.setTotalBillNum(totalBillNum);
        contractModifyNumVO.setTotalDeliveryNum(totalDeliveryNum);
        contractModifyNumVO.setAllocateNum(allocateNum);

        ContractTypeEnum typeEnum = ContractTypeEnum.getByValue(contractEntity.getContractType());
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO()
                .setDomainCode(contractEntity.getDomainCode())
                .setSalesType(contractEntity.getSalesType())
                .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerId(String.valueOf(contractEntity.getCustomerId()));
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            contractFuturesDTO.setCustomerId(contractEntity.getSupplierId().toString());
        }
        switch (typeEnum) {
            case YI_KOU_JIA:
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(BigDecimal.ZERO)
                        // 可变更量
                        .setCanModifyNum(contractNum.subtract(totalDeliveryNum));
                break;
            case JI_CHA:
                // 这边的结果会返回NULL
                Result transferResult = futuresDomainFacade.mayTransferNum(contractFuturesDTO);
                CustomerFuturesDTO transferDTO = JSON.parseObject(JSON.toJSONString(transferResult.getData()), CustomerFuturesDTO.class);
                contractModifyNumVO.setTransferNum(ObjectUtil.isNotEmpty(transferDTO) ? transferDTO.getMayTransferNum() : BigDecimal.ZERO);
            case JI_CHA_ZAN_DING_JIA:
                // 这边的结果会返回NULL
                Result priceResult = futuresDomainFacade.mayPriceNum(contractFuturesDTO);
                CustomerFuturesDTO ablePriceDTO = JSON.parseObject(JSON.toJSONString(priceResult.getData()), CustomerFuturesDTO.class);
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(contractNum.subtract(totalPriceNum))
                        .setAblePriceNum(ObjectUtil.isNotEmpty(ablePriceDTO) ? ablePriceDTO.getMayPriceNum() : BigDecimal.ZERO)
                        // 定价状态
                        .setPricedStatus(judgePricedStatus(contractNum, totalPriceNum));
                break;
            case ZAN_DING_JIA:
                contractModifyNumVO
                        // 未开单量
                        .setNotBillNum(contractNum.subtract(totalBillNum))
                        // 未提货量
                        .setNotDeliveryNum(contractNum.subtract(totalDeliveryNum))
                        // 未定价量
                        .setNotPricedNum(contractNum.subtract(totalPriceNum));
                break;
            default:
                break;
        }
        contractModifyNumVO
                .setPricedNum(totalPriceNum)
                .setDeliveryNum(totalDeliveryNum)
                .setContractNum(contractNum)
                .setApplyDeliveryNum(contractEntity.getApplyDeliveryNum());

        return contractModifyNumVO;
    }

    /**
     * 详情查询按钮显示信息
     *
     * @param contractEntity
     * @param contractDetailVO
     */
    private void setShowButton(ContractEntity contractEntity, ContractDetailVO contractDetailVO) {

        // 是否展示作废按钮
        int isShowInvalid = DisableStatusEnum.DISABLE.getValue();
        // 合同剩余量为0
        if (BigDecimalUtil.isZero(contractEntity.getContractNum())) {
            // 判断是否变更过主体
            List<ContractEntity> sonContractList = contractQueryDomainService.getContractByPid(contractEntity.getId());
            if (ObjectUtil.isNotEmpty(sonContractList) && sonContractList.stream()
                    .anyMatch(sonContract -> (sonContract.getContractSource().equals(ContractActionEnum.REVISE_CUSTOMER.getActionValue()) ||
                            sonContract.getContractSource().equals(ContractActionEnum.SPLIT_CUSTOMER.getActionValue())))) {
                isShowInvalid = DisableStatusEnum.ENABLE.getValue();
            }
        }
        // 合同已作废不显示
        if (contractEntity.getStatus() == ContractStatusEnum.INVALID.getValue()) {
            isShowInvalid = DisableStatusEnum.DISABLE.getValue();
        }
        contractDetailVO.setIsShowInvalid(isShowInvalid);

        // 是否展示反点价按钮
        int isShowReverse = DisableStatusEnum.DISABLE.getValue();

        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getContractNum())                                        // 合同量>0
                // N-K nav头寸处理改造 changed by Mr at 2025-07-24 start
                && (contractEntity.getStatus() == ContractStatusEnum.EFFECTIVE.getValue()                            // 生效中
                || (contractEntity.getStatus() == ContractStatusEnum.MODIFYING.getValue() && contractEntity.getIsReversePrice() != null)) // 反点价中
                // N-K nav头寸处理改造 changed by Mr at 2025-07-24 end
                && BigDecimalUtil.isZero(contractEntity.getCloseTailNum())                                           // 尾量关闭=0
                && Arrays.asList(11, 12, 25, 26).contains(contractEntity.getGoodsCategoryId())                       // 只对豆粕 豆油 特油 特种蛋白显示
                && contractEntity.getIsSoybean2() == 0                                                               // 排除豆二
                && (contractEntity.getContractType() == ContractTypeEnum.YI_KOU_JIA.getValue())                      // 一口价
                && StringUtils.isNotBlank(contractEntity.getDomainCode()) && StringUtils.isNotBlank(contractEntity.getFutureCode()) // 期货合约不为空
        ) {
            // 销售合同判断可反点次数
            if (contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
                // 可反点次数>0
                if (contractEntity.getAbleReversePriceTimes() > 0) {
                    isShowReverse = DisableStatusEnum.ENABLE.getValue();
                }
            } else {
                // 采购合同判断是否有反点价权限
                isShowReverse = DisableStatusEnum.ENABLE.getValue();
            }
        }
        contractDetailVO.setIsShowReversePrice(isShowReverse);

        // 是否展示定价完成
        int isShowPriceComplete = DisableStatusEnum.DISABLE.getValue();
        // 原合同类型
        Integer originContractType = contractEntity.getContractType();

        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 Start
        // String priceStatus = RedisConstants.MODIFY_SBM_SALES_PRICE_STATUS + contractEntity.getContractCode();
        // int priceCompleteStatus = redisUtil.get(priceStatus) == null ? 0 : (int) redisUtil.get(priceStatus);
        int priceCompleteStatus = contractEntity.getIsPricingCompleted() == null ? 0 : contractEntity.getIsPricingCompleted();
        // 优化：case-1003115 定价完成状态目前只放到Redis里面，需要存到数据库里面 Author: Mr 2025-04-09 End

        if (Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.ZAN_DING_JIA.getValue(),
                ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()).contains(originContractType)
                && BigDecimalUtil.isGreaterThanZero(contractEntity.getContractNum())                            // 合同量>0
                && BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())   // 合同量=定价量
                && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())                   // 生效中
                && priceCompleteStatus == 0) {                                                                  // 定价完成
            isShowPriceComplete = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowPriceComplete(isShowPriceComplete);

        // 是否展示暂定价定价
        int isShowCreateTtPrice = DisableStatusEnum.DISABLE.getValue();

        if (contractEntity.getContractType().equals(ContractTypeEnum.ZAN_DING_JIA.getValue())
                && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())
                && !BigDecimalUtil.isEqual(contractEntity.getContractNum(), contractEntity.getTotalPriceNum())) {

            isShowCreateTtPrice = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowCreateTtPrice(isShowCreateTtPrice);
        // 拆分修改是否展示保存按钮
        int isShowSaveButton = DisableStatusEnum.DISABLE.getValue();

        String saveTimes = redisUtil.getString(CONTRACT_SAVE_TT_TIMES + contractEntity.getContractCode());
        if (StringUtils.isBlank(saveTimes) && contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            isShowSaveButton = DisableStatusEnum.ENABLE.getValue();
        }
        contractDetailVO.setIsShowSaveButton(isShowSaveButton);
    }

    /**
     * 判断定价状态
     *
     * @param contractNum   合同数量
     * @param totalPriceNum 已定价量
     * @return
     */
    private Integer judgePricedStatus(BigDecimal contractNum, BigDecimal totalPriceNum) {
        // 定价状态
        int pricedStatus = 0;

        if (BigDecimal.ZERO.equals(totalPriceNum)) {
            pricedStatus = ContractPricedStatusEnum.NOT_PRICED.getValue();
        } else if (contractNum.equals(totalPriceNum)) {
            pricedStatus = ContractPricedStatusEnum.PRICED.getValue();
        } else {
            pricedStatus = ContractPricedStatusEnum.PART_PRICED.getValue();
        }
        return pricedStatus;
    }

    /**
     * 获取LGK数据信息
     *
     * @param contractEntity
     * @return
     */
    private ContractEntity getContractLkg(ContractEntity contractEntity) {

        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum();
        // 已定价数量
        BigDecimal totalPriceNum = contractEntity.getTotalPriceNum();// 获取lkg信息
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;
        // todo:===========nana: 数字合同注释掉此行
        totalBillNum = totalDeliveryNum;
        // 获取lkg信息
        try {
            if (openLkgQuery == 1) {
                if (!contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                    Result result = lkgContractFacade.getLkgContract(contractEntity.getContractCode());
                    log.info("获取lkg信息：" + JSON.toJSONString(result));
                    if (null != result && result.getCode() == ResultCodeEnum.OK.code()) {
                        if (null != result.getData()) {
                            LkgContractDTO lkgContractDTO = JSON.parseObject(JSON.toJSONString(result.getData()), LkgContractDTO.class);
                            if (null != lkgContractDTO && null != lkgContractDTO.getStatus() && lkgContractDTO.getStatus() != -1) {
                                if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                                    totalDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getContractFactOutCount());
                                    totalBillNum = BigDecimal.valueOf(lkgContractDTO.getContractOutCount());
                                    allocateNum = BigDecimal.valueOf(lkgContractDTO.getDbOrderCount());
                                } else {
                                    totalDeliveryNum = BigDecimal.valueOf(lkgContractDTO.getInCount());
                                    totalBillNum = BigDecimal.valueOf(lkgContractDTO.getOrderCount());
                                }
                                log.info("合同数量变更==[{}],已提数量：{}→{},已开单数量：{}→{},数据来源：{}",
                                        contractEntity.getContractCode(),
                                        contractEntity.getTotalDeliveryNum(), totalDeliveryNum,
                                        contractEntity.getTotalBillNum(), totalBillNum,
                                        JSON.toJSONString(lkgContractDTO));
                            }
                        }
                    } else {
                        throw new BusinessException(ResultCodeEnum.GET_LKG_CONTRACT_EXCEPTION);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取LKG合同信息异常:{}", e.getMessage());
            contractEntity.setLkgContractException("获取LKG合同信息异常");
            throw new BusinessException(ResultCodeEnum.GET_LKG_CONTRACT_EXCEPTION);
        }

        // 处理精度丢失
        totalDeliveryNum = totalDeliveryNum.setScale(6, RoundingMode.HALF_UP);
        totalBillNum = totalBillNum.setScale(6, RoundingMode.HALF_UP);
        allocateNum = allocateNum.setScale(6, RoundingMode.HALF_UP);

        contractEntity
                .setTotalDeliveryNum(totalDeliveryNum)
                .setTotalBillNum(totalBillNum)
                .setAllocateNum(allocateNum);

        return contractEntity;
    }

    /**
     * 获取ATLAS数据信息
     *
     * @param siteEntity     账套信息
     * @param contractEntity 合同信息
     * @return
     */
    private ContractEntity getContractAtlas(SiteEntity siteEntity, ContractEntity contractEntity) {

        // 已提数量
        BigDecimal totalDeliveryNum = contractEntity.getTotalDeliveryNum() == null ? BigDecimal.ZERO : contractEntity.getTotalDeliveryNum();
        // 已开单数量
        BigDecimal totalBillNum = totalDeliveryNum;

        // 获取开单量
        try {
            if (openAtlasQuery == 1) {
                if (!contractEntity.getStatus().equals(ContractStatusEnum.INEFFECTIVE.getValue())) {
                    Result<BigDecimal> result = atlasContractFacade.getContractOpenQuantity(siteEntity.getAtlasCode(), contractEntity.getContractCode());
                    if (result.isSuccess()) {
                        BigDecimal notBilledNum = result.getData().setScale(6, RoundingMode.HALF_UP);

                        // 已开单数量 = 合同数量 - 未开单数量
                        totalBillNum = contractEntity.getContractNum().subtract(notBilledNum);
                    }

                    // 获取提货量
                    Result<AtlasMappingContractEntity> mappingContractEntityResult = atlasContractFacade.getByNavContractCode(contractEntity.getContractCode());
                    if (mappingContractEntityResult.isSuccess()) {
                        AtlasMappingContractEntity mappingContractEntity = mappingContractEntityResult.getData();
                        if (mappingContractEntity != null && mappingContractEntity.getId() != null) {
                            totalDeliveryNum = mappingContractEntity.getExecutedNum() == null ? BigDecimal.ZERO : mappingContractEntity.getExecutedNum();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取ATLAS合同信息异常:{}", e.getMessage());
            throw new BusinessException(ResultCodeEnum.GET_ATLAS_CONTRACT_EXCEPTION);
        }

        // 获取提货量
        contractEntity
                .setTotalDeliveryNum(totalDeliveryNum)
                .setTotalBillNum(totalBillNum)
                .setAllocateNum(totalBillNum);

        return contractEntity;
    }

    /**
     * 获取合同生效定价的分配信息
     *
     * @param contractEntityList
     * @param type
     * @param priceAllocateId
     * @return
     */
    private List<ContractVO> completeContract(List<ContractEntity> contractEntityList, String type, Integer priceAllocateId) {

        List<ContractVO> contractVOList = new ArrayList<>();
        for (ContractEntity contractEntity : contractEntityList) {
            List<ContractChangeEquityEntity> contractChangeEquityEntities =
                    contractQueryDomainService.getChangeContractEquityDetailByNotApprove(contractEntity.getId());
            if (!contractChangeEquityEntities.isEmpty()) {
                continue;
            }
            ContractVO contractVO = new ContractVO();
            BeanUtil.copyProperties(contractEntity, contractVO);
            BigDecimal sumPriceAllocateOfContract = priceAllocateFacade.getSumPriceAllocateOfContract(String.valueOf(contractEntity.getId()));

            CategoryEntity category = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());
            contractVO.setCategoryName(category.getName());

            BigDecimal canAllocateOfContract = BigDecimal.ZERO;

            // canAllocateOfContract = 合同总量 - 已点量 - 待审核(转月和点价)
            BigDecimal num = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), contractEntity.getTotalPriceNum());
            canAllocateOfContract = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, num, sumPriceAllocateOfContract);

            contractVO.setSumPriceAllocateOfContract(sumPriceAllocateOfContract);
            if (null != priceAllocateId) {
                PriceAllocateEntity priceAllocateEntity = priceAllocateFacade.getPriceAllocateById(priceAllocateId.toString());

                if (priceAllocateEntity.getContractId().equals(contractEntity.getId())) {
                    canAllocateOfContract = canAllocateOfContract.add(priceAllocateEntity.getAllocateNum());
                }
            }
            contractVO.setCanAllocateOfContract(canAllocateOfContract);

            WarehouseEntity factoryWarehouseEntity = this.getFactoryWarehouse(contractEntity.getShipWarehouseId());
            if (null != factoryWarehouseEntity) {
                contractVO.setFactoryWarehouseName(factoryWarehouseEntity.getName());
            }

            // 根据createdBy获取employ信息
            EmployEntity employEntity = employFacade.getEmployById(contractEntity.getCreatedBy());
            contractVO.setCreatedEntity(employEntity);
            CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
            // 获取合同所属商务
            EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
            contractVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);
            contractVO.setCompanyName(companyEntity.getName());
            contractVO.setShipWarehouseName(contractEntity.getShipWarehouseValue());
            //查询合同价格详情
            ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());
            if (null != contractPriceEntity) {
                contractVO.setProteinDiffPrice(contractPriceEntity.getProteinDiffPrice())
                        .setFee(contractPriceEntity.getFee());
            } else {
                contractVO.setProteinDiffPrice(BigDecimal.ZERO);
            }
            contractVOList.add(contractVO);
        }
        return contractVOList;
    }

    /**
     * 获取可提货的合同信息
     *
     * @param deliveryApplyContractList 合同列表
     * @return
     */
    private List<ContractEntity> getContractDeliveryList(List<ContractEntity> deliveryApplyContractList, Boolean showZero) {

        // 批量获取lkg合同信息
        List<ContractEntity> batchLkgContractList = getBatchLkgContractList(deliveryApplyContractList);

        Map<Integer, ContractEntity> contractMap = new HashMap<>();
        // 计算合同的可提货数量
        batchLkgContractList.forEach(contractEntity -> {
            // 开单量
            BigDecimal billNum = BigDecimalUtil.max(contractEntity.getTotalBillNum(), contractEntity.getTotalDeliveryNum(), contractEntity.getAllocateNum());

            contractEntity.setIsBilled(BigDecimalUtil.isGreaterThanZero(billNum) ? 1 : 0);

            // 可提货数量
            BigDecimal canDeliveryNum = BigDecimal.ZERO;

            // 可提货数量 = 合同数量 - 开单数量 - 申请提货数量
            canDeliveryNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, canDeliveryNum, contractEntity.getContractNum());
            canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, billNum);
            canDeliveryNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, canDeliveryNum, contractEntity.getApplyDeliveryNum());

            contractEntity.setCanDeliveryNum(canDeliveryNum);

            if (showZero) {
                contractMap.put(contractEntity.getId(), contractEntity);
            } else if (BigDecimalUtil.isGreaterThanZero(canDeliveryNum)) {
                contractMap.put(contractEntity.getId(), contractEntity);
            }
        });

        // 按照原来的顺序返回
        List<ContractEntity> newApplyContractList = new ArrayList<>();
        for (ContractEntity contractEntity : deliveryApplyContractList) {
            ContractEntity newContractEntity = contractMap.get(contractEntity.getId());
            if (newContractEntity != null) {
                newApplyContractList.add(newContractEntity);
            }
        }
        // 再按照是否开单排序
        newApplyContractList.sort(Comparator.comparing(ContractEntity::getIsBilled).reversed());

        return newApplyContractList;
    }

    /**
     * 批量获取lkg合同信息
     *
     * @param deliveryApplyContractList 合同列表
     * @return
     */
    private List<ContractEntity> getBatchLkgContractList(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();

        // 提货查询方式： 1.多线程查询lkg 2.多线程查询本地数据库 3.单线程查询本地数据库+批量查询
        switch (this.deliveryQueryType) {
            case 1:
                newApplyContractList = getBatchLkgContractList1(deliveryApplyContractList);
                break;
            case 2:
                newApplyContractList = getBatchLkgContractList2(deliveryApplyContractList);
                break;
            case 3:
                newApplyContractList = getBatchLkgContractList3(deliveryApplyContractList);
                break;
            default:
                break;
        }
        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList1(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();
        // 多线程调用LKG
        CompletableFuture[] futures = new CompletableFuture[deliveryApplyContractList.size()];
        for (int i = 0; i < deliveryApplyContractList.size(); i++) {
            final int taskIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                log.info("Task " + taskIndex + " is running " + deliveryApplyContractList.get(taskIndex).getContractCode());
                // 调用LKG接口
                ContractEntity contractEntity = getContractById(deliveryApplyContractList.get(taskIndex).getId());
                if (null != contractEntity) {
                    newApplyContractList.add(contractEntity);
                }
            });
        }
        CompletableFuture.allOf(futures).join();
        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList2(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();
        // 多线程调用LKG
        CompletableFuture[] futures = new CompletableFuture[deliveryApplyContractList.size()];
        for (int i = 0; i < deliveryApplyContractList.size(); i++) {
            final int taskIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                log.info("Task " + taskIndex + " is running " + deliveryApplyContractList.get(taskIndex).getContractCode());
                // 调用LKG接口
                ContractEntity contractEntity = this.getLocalLkgContractByContractId(deliveryApplyContractList.get(taskIndex).getId());
                if (null != contractEntity) {
                    newApplyContractList.add(contractEntity);
                }
            });
        }
        CompletableFuture.allOf(futures).join();
        return newApplyContractList;
    }

    private List<ContractEntity> getBatchLkgContractList3(List<ContractEntity> deliveryApplyContractList) {
        List<ContractEntity> newApplyContractList = new ArrayList<>();
        List<Integer> contractIdList = deliveryApplyContractList.stream().map(ContractEntity::getId).collect(Collectors.toList());
        List<String> contractCodeList = deliveryApplyContractList.stream().map(ContractEntity::getContractCode).collect(Collectors.toList());
        // 将lkg合同信息存放map中
        List<LkgContractInfoEntity> lkgContractInfoList = this.getLocalLkgContractByContractCodeList(contractCodeList);
        if (CollectionUtil.isEmpty(lkgContractInfoList)) {
            return newApplyContractList;
        }
        // lkg合同信息去重
        lkgContractInfoList = lkgContractInfoList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LkgContractInfoEntity::getContractNumber))), ArrayList::new));

        Map<String, LkgContractInfoEntity> lkgContractInfoMap = lkgContractInfoList.stream().collect(Collectors.toMap(LkgContractInfoEntity::getContractNumber, lkgContractInfoEntity -> lkgContractInfoEntity));
        // 遍历contractEntityList 设置lkg合同信息
        List<ContractEntity> contractEntityList = contractQueryDomainService.getContractListByIds(contractIdList);
        for (ContractEntity contractEntity : contractEntityList) {
            LkgContractInfoEntity lkgContractInfoEntity = lkgContractInfoMap.get(contractEntity.getContractCode());
            if (null != lkgContractInfoEntity && lkgContractInfoEntity.getStatus() != 7) {
                newApplyContractList.add(contractEntity.setTotalDeliveryNum(lkgContractInfoEntity.getContractFactOutCount())
                        .setTotalBillNum(lkgContractInfoEntity.getContractOutCount())
                        .setAllocateNum(lkgContractInfoEntity.getDbOrderCount()));
            }
        }
        return newApplyContractList;
    }


    /**
     * 根据合同ID获取LKG的信息
     *
     * @param contractId
     * @return
     */
    private ContractEntity getLocalLkgContractByContractId(Integer contractId) {
        ContractEntity contractEntity = contractQueryDomainService.getBasicContractById(contractId);
        if (null == contractEntity) {
            return null;
        }
        // 已提数量
        BigDecimal totalDeliveryNum = BigDecimal.ZERO;
        // 已开单数量
        BigDecimal totalBillNum = BigDecimal.ZERO;
        // 调拨数量
        BigDecimal allocateNum = BigDecimal.ZERO;
        Result result = lkgContractFacade.getLocalLkgContractByContractCode(contractEntity.getContractCode());
        log.info("获取localLkg信息：" + JSON.toJSONString(result));
        if (result.isSuccess()) {
            LkgContractInfoEntity lkgContractInfo = JSON.parseObject(JSON.toJSONString(result.getData()), LkgContractInfoEntity.class);
            if (null != lkgContractInfo) {
                // 排除已关闭的合同
                if (lkgContractInfo.getStatus() == 7) {
                    return null;
                }
                totalDeliveryNum = lkgContractInfo.getContractFactOutCount();
                totalBillNum = lkgContractInfo.getContractOutCount();
                allocateNum = lkgContractInfo.getDbOrderCount();
            }
        }
        return contractEntity.setTotalDeliveryNum(totalDeliveryNum)
                .setTotalBillNum(totalBillNum)
                .setAllocateNum(allocateNum);
    }

    public List<LkgContractInfoEntity> getLocalLkgContractByContractCodeList(List<String> contractCodeList) {
        Result result = lkgContractFacade.getLocalLkgContractByContractCodeList(new LkgQueryRecordDTO().setContractCodeList(contractCodeList));
        if (result.isSuccess()) {
            return JSON.parseArray(JSON.toJSONString(result.getData()), LkgContractInfoEntity.class);
        }
        return null;
    }

    private WarehouseEntity getFactoryWarehouse(Integer warehouseId) {
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                return ObjectUtil.isNotEmpty(warehouseEntity) ? warehouseEntity : null;
            }
        }
        return null;
    }

    /**
     * Jason added for getting contract by status
     */
    public List<ContractEntity> getContractByStatus(String status, List<String> contractNumbers) {
        return contractQueryDomainService.getContractByStatus(status, contractNumbers);
    }

    @Override
    public Integer getOriginalContractType(Integer contractId) {
        // 获取初始合同
        ContractEntity currentContract = contractQueryDomainService.getBasicContractById(contractId);

        if (currentContract == null) {
            return null;
        }

        // 只处理一口价的情况
        if (currentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {

            ContractEntity parentContract = contractQueryDomainService.getBasicContractById(currentContract.getParentId());

            // 查询点价单的id
            int priceContractId = contractId;

            while (parentContract != null) {
                if (parentContract.getContractType().equals(ContractTypeEnum.JI_CHA.getValue()) ||
                        parentContract.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                    return parentContract.getContractType();
                }
                if (parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    List<TTPriceEntity> priceList = ttPriceService.getConfirmPriceList(priceContractId);
                    if (CollectionUtil.isNotEmpty(priceList)) {
                        return ContractTypeEnum.JI_CHA.getValue();
                    }
                }
                priceContractId = parentContract.getId();

                parentContract = contractQueryDomainService.getBasicContractById(parentContract.getParentId());
            }

            // 到达顶级合同后，检查是否存在定价单，若存在则返回基差合同类型
            List<TTPriceEntity> priceList = ttPriceService.getConfirmPriceList(priceContractId);
            if (CollectionUtil.isNotEmpty(priceList)) {
                return ContractTypeEnum.JI_CHA.getValue();
            }

            return ContractTypeEnum.YI_KOU_JIA.getValue();
        }

        return currentContract.getContractType();
    }

    // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 start

    /**
     * 计算未开单量和未到货量
     *
     * @param contractDetailVO 合同详情VO
     */
    private void calculateNotBillAndDeliveryNum(ContractDetailVO contractDetailVO) {
        try {
            // 只处理现货采购合同
            if (!isSpotProcurementContract(contractDetailVO)) {
                log.debug("合同{}不是现货采购合同，使用默认计算逻辑", contractDetailVO.getContractCode());
                setDefaultNotBillAndDeliveryNum(contractDetailVO);
                return;
            }

            // 合同数量
            BigDecimal contractNum = contractDetailVO.getContractNum() != null ?
                    contractDetailVO.getContractNum() : BigDecimal.ZERO;

            // 已开单数量
            BigDecimal totalBillNum = contractDetailVO.getTotalBillNum() != null ?
                    contractDetailVO.getTotalBillNum() : BigDecimal.ZERO;

            // 已执行数量（从ATLAS获取）
            BigDecimal executedNum = contractDetailVO.getTotalDeliveryNum() != null ?
                    contractDetailVO.getTotalDeliveryNum() : BigDecimal.ZERO;

            // 未开单量 = 合同数量 - 已开单数量
            BigDecimal notBillNum = contractNum.subtract(totalBillNum);
            contractDetailVO.setNotBillNum(notBillNum);

            // 根据交货方式计算未到货量
            if (isWarrantTransfer(contractDetailVO)) {
                // 仓单转让：未到货量 = 未开单量（ATLAS Open quantity）
                contractDetailVO.setNotDeliveryNum(notBillNum);
                log.debug("合同{}为仓单转让，未到货量使用未开单量：{}", contractDetailVO.getContractCode(), notBillNum);
            } else {
                // 非仓单转让：未到货量 = 合同数量 - 已执行数量
                BigDecimal notDeliveryNum = contractNum.subtract(executedNum);
                contractDetailVO.setNotDeliveryNum(notDeliveryNum);
                log.debug("合同{}为非仓单转让，未到货量 = 合同数量({}) - 已执行数量({}) = {}",
                        contractDetailVO.getContractCode(), contractNum, executedNum, notDeliveryNum);
            }

        } catch (Exception e) {
            log.error("计算合同{}的未开单量和未到货量异常", contractDetailVO.getContractCode(), e);
            // 异常情况下使用默认计算逻辑
            setDefaultNotBillAndDeliveryNum(contractDetailVO);
        }
    }

    /**
     * 检查是否为现货采购合同
     *
     * @param contractDetailVO 合同详情VO
     * @return true-是现货采购合同，false-不是
     */
    private boolean isSpotProcurementContract(ContractDetailVO contractDetailVO) {
        // 检查是否为现货(bu_code='ST')且采购(sales_type=1)
        return "ST".equals(contractDetailVO.getBuCode()) &&
                Integer.valueOf(1).equals(contractDetailVO.getSalesType());
    }

    /**
     * 判断是否为仓单转让交货方式
     *
     * @param contractDetailVO 合同详情VO
     * @return true-是仓单转让，false-不是
     */
    private boolean isWarrantTransfer(ContractDetailVO contractDetailVO) {
        // 通过delivery_type_value字段判断
        return "仓单转让".equals(contractDetailVO.getDeliveryTypeValue());
    }

    /**
     * 设置默认的未开单量和未到货量计算逻辑
     *
     * @param contractDetailVO 合同详情VO
     */
    private void setDefaultNotBillAndDeliveryNum(ContractDetailVO contractDetailVO) {
        BigDecimal contractNum = contractDetailVO.getContractNum() != null ?
                contractDetailVO.getContractNum() : BigDecimal.ZERO;
        BigDecimal totalBillNum = contractDetailVO.getTotalBillNum() != null ?
                contractDetailVO.getTotalBillNum() : BigDecimal.ZERO;
        BigDecimal totalDeliveryNum = contractDetailVO.getTotalDeliveryNum() != null ?
                contractDetailVO.getTotalDeliveryNum() : BigDecimal.ZERO;

        // 未开单量 = 合同数量 - 已开单数量
        contractDetailVO.setNotBillNum(contractNum.subtract(totalBillNum));

        // 未到货量 = 合同数量 - 已提货数量（默认逻辑）
        contractDetailVO.setNotDeliveryNum(contractNum.subtract(totalDeliveryNum));
    }
    // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 end
}
