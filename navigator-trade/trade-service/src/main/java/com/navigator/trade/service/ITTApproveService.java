package com.navigator.trade.service;

import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/29 10:17
 */
public interface ITTApproveService {
    /**
     * 提交审批
     *
     * @param approveDTO
     * @return
     */
    Result<ApproveResultDTO> startTTApprove(ApproveDTO approveDTO);

    Result<ApproveResultDTO> approve(ApproveDTO approveDTO);

    /**
     * 作废
     *
     * @param approveDTO
     */
    void cancel(ApproveDTO approveDTO);

    /**
     * 新增审批规则
     * @param goodsCategoryId
     * @param unitPrice
     * @param contractNum1
     * @param deliveryStartTime
     * @param deliveryEndTime
     * @return
     */
    Integer calcApproveRuleValue(Integer goodsCategoryId, String unitPrice, String contractNum1, Date deliveryStartTime, Date deliveryEndTime);

    /**
     * 变更审批规则
     *
     * @param type        拆分、修改
     * @param ttdto
     * @return
     */
    int calcApproveRuleValue(Integer type, TTDTO ttdto);


    ApproveDTO adaptApproveRule(ApproveDTO approveDTO);
}
