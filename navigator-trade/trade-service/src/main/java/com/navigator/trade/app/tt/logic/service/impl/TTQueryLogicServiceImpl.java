package com.navigator.trade.app.tt.logic.service.impl;

import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.app.tt.TTHandleFactory;
import com.navigator.trade.app.tt.domain.qo.TTAddQO;
import com.navigator.trade.app.tt.domain.qo.TTPriceQO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.domain.service.TTQueryDomainService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.app.tt.logic.service.handler.ITTSceneHandler;
import com.navigator.trade.pojo.dto.tradeticket.TTQueryDTO;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.TTAddEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;
import com.navigator.trade.pojo.vo.TTDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
@Service
public class TTQueryLogicServiceImpl implements TTQueryLogicService {

    @Autowired
    TTQueryDomainService ttQueryDomainService;

    @Autowired
    TTHandleFactory ttHandleFactory;


    @Override
    public TTDetailVO queryTTDetail(Integer ttId,TTTypeEnum ttTypeEnum) {
        // 路由到具体TT类型处理器
        ITTSceneHandler ttSceneHandler = ttHandleFactory.getTTSceneHandler(ttTypeEnum);
        return  ttSceneHandler.queryDetail(ttId);
    }

    @Override
    public Result queryTTList(QueryDTO<TTQueryDTO> ttQueryDTO) {
        // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 start
        return ttQueryDomainService.queryTTListNew(ttQueryDTO);
        // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-20 end
    }


    @Override
    public TradeTicketEntity fetchTradeTicketEntity(TradeTicketQO qo) {
        return ttQueryDomainService.fetchTradeTicketEntity(qo);
    }

    @Override
    public TTAddEntity fetchTTAddEntity(TTAddQO qo) {
        return ttQueryDomainService.fetchTTAddEntity(qo);
    }

    @Override
    public List<TTPriceEntity> fetchTTPriceEntities(TTPriceQO qo) {
        return ttQueryDomainService.fetchTTPriceEntities(qo);
    }

    @Override
    public ContractPriceEntity getContractPriceEntityContractId(Integer contractId) {
        return ttQueryDomainService.getContractPriceEntityContractId(contractId);
    }

    @Override
    public TradeTicketEntity getByTtId(Integer ttId) {
        return ttQueryDomainService.getByTtId(ttId);
    }

    @Override
    public TradeTicketEntity getIncludeDeletedByGroupId(String groupId, Integer id) {
        return ttQueryDomainService.getByGroupId(groupId, id);
    }

}
