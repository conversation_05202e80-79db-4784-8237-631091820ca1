package com.navigator.trade.service.contractsign.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.LogTargetRecordTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.*;
import com.navigator.common.config.properties.AzureBlobProperties;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.FilePagesUtils;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.customer.pojo.enums.*;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.facade.TemplateGroupFacade;
import com.navigator.husky.facade.TemplateLoadFacade;
import com.navigator.husky.facade.TemplateSignFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import com.navigator.husky.pojo.enums.TemplateLoadTypeEnum;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.facade.YqqSignParameterFacade;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.trade.dao.ContractPaperDao;
import com.navigator.trade.dao.ContractSignDao;
import com.navigator.trade.dao.ContractSignTemplateLogDao;
import com.navigator.trade.handler.TTHandler;
import com.navigator.trade.pojo.dto.contract.ContractBaseDTO;
import com.navigator.trade.pojo.dto.contract.ContractDetailInfoDTO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.*;
import com.navigator.trade.pojo.dto.notice.CustomerSignNoticeItemDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.service.*;
import com.navigator.trade.service.contractsign.ContractSignBuildProcessor;
import com.navigator.trade.service.contractsign.ContractSignBuildProcessorV2;
import com.navigator.trade.service.contractsign.IContractSignService;
import com.navigator.trade.service.contractsign.converter.SignHuskyTemplateConverter;
import com.navigator.trade.service.tradeticket.ITradeTicketQueryService;
import com.navigator.trade.service.tradeticket.ITradeTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-01-21 16:18
 */
@Slf4j
public abstract class BaseAbstractContractSignService implements IContractSignService {
    @Autowired
    protected IContractPriceService contractPriceService;
    @Resource
    ContractSignBuildProcessor contractSignBuildProcessor;
    @Resource
    ContractSignBuildProcessorV2 contractSignBuildProcessorV2;
    @Resource
    MessageFacade messageFacade;
    @Resource
    TTHandler ttHandler;
    @Autowired
    AzureBlobUtil azureBlobUtil;
    @Resource
    private ContractSignDao contractSignDao;
    @Resource
    private ContractPaperDao contractPaperDao;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private CustomerDetailFacade customerDetailFacade;
    @Resource
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;
    @Resource
    private CustomerProtocolFacade customerProtocolFacade;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Resource
    private FileBusinessFacade fileBusinessFacade;
    @Resource
    private TemplateFacade templateFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private DbtSignatureFacade signatureFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private IContractQueryService contractQueryService;
    @Resource
    private ContractSignTemplateLogDao contractSignTemplateLogDao;
    @Resource
    private IContractPaperService contractPaperService;
    @Resource
    private PriceApplyFacade priceApplyFacade;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Value("${contract.sign.qrCodeUrl}")
    private String qrCodeUrl;
    @Resource
    private CRoleFacade cRoleFacade;
    @Resource
    private AzureBlobProperties azureBlobProperties;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private TemplateSignFacade templateSignFacade;
    @Resource
    private TemplateLoadFacade templateLoadFacade;
    @Resource
    private QualityFacade qualityFacade;
    @Resource
    private ITradeTicketQueryService tradeTicketQueryService;
    @Resource
    private ITtTranferService ttTranferService;
    @Resource
    private ITtPriceService ttPriceService;
    @Resource
    private TemplateGroupFacade templateGroupFacade;
    @Resource
    private ContractSignDetailServer contractSignDetailServer;
    @Resource
    private YqqSignParameterFacade yqqSignParameterFacade;

    @Override
    public ContractSignEntity createOrUpdateContractSign(ContractSignCreateDTO signCreateDTO) {
        // 填充协议参数
        ContractSignEntity contractSignEntity = this.prepareSignInfo(signCreateDTO);
        //fix 协议重复插入
        ContractSignEntity contractSignEntity1 = null;
        Integer contractSignId = signCreateDTO.getContractSignId();
        if (null != signCreateDTO.getTtId()) {
            contractSignEntity1 = contractSignDao.getSignDetailByTtId(signCreateDTO.getTtId());
            contractSignId = contractSignEntity1 == null ? null : contractSignEntity1.getId();
        }
        if ((null != signCreateDTO.getContractSignId() && null != this.checkContractSign1(signCreateDTO.getContractSignId())) || null != contractSignEntity1) {
            // 修改提交TT，初始化协议
            contractSignEntity
                    .setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.NOT_START.getValue());
            contractSignDao.updateById(contractSignEntity.setId(contractSignId));
        } else {
            // 创建协议
            contractSignDao.save(contractSignEntity);
            FileInfoEntity barCodeInfo = fileProcessFacade.generateBarCodeImg(signCreateDTO.getContractCode(), "");
            FileInfoEntity qrCodeInfo = fileProcessFacade.generateQrCodeImg(qrCodeUrl + contractSignEntity.getId());
            contractSignEntity.setBarCodeImage(null == barCodeInfo ? "" : barCodeInfo.getFilePathUrl()).setQrCodeImage(null == qrCodeInfo ? "" : qrCodeInfo.getFilePathUrl());
            contractSignDao.updateById(contractSignEntity);
        }
        //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 start
        if (null != contractSignEntity.getId() && null != contractSignEntity.getContractId()) {
            saveContractSignDetail(contractSignEntity);
        }
        //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 end
        try {
            // 记录操作日志
            addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.NEW_SALES_CONTRACT_SIGN, JSONUtil.toJsonStr(contractSignEntity), SystemEnum.MAGELLAN.getValue());
        } catch (Exception e) {
            log.debug("createOrUpdateContractSign:{}", e.getMessage());
        }

        return contractSignEntity;
    }

    @Override
    public void updateContractSign(ContractEntity contractEntity, CustomerOriginalPaperEntity customerOriginalPaperEntity, ContractSignEntity contractSignEntity) {
        // 判断是否需要正本
        Integer needOriginalPaper = isNeedOriginalPaper(customerOriginalPaperEntity, contractSignEntity, null);
        contractSignEntity.setNeedOriginalPaper(needOriginalPaper)
                .setContractId(contractEntity.getId());
        //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 start
        saveContractSignDetail(contractSignEntity);
        //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 end
        contractSignDao.updateById(contractSignEntity);
    }

    //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 start

    /**
     * 协议新增记录关联数据
     *
     * @param contractSignEntity
     */
    @Async
    public void saveContractSignDetail(ContractSignEntity contractSignEntity) {
        ContractSignDetailEntity contractSignDetailEntity = new ContractSignDetailEntity();

        contractSignDetailEntity
                .setContractSignId(contractSignEntity.getId())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setTtType(contractSignEntity.getTtType())
                .setTradeType(contractSignEntity.getTradeType())
        ;
        ContractSignTemplateDTO contractSignTemplateDTO = contractSignBuildProcessor.getContractSignTemplateDTO(contractSignEntity.getId());

        if (null != contractSignTemplateDTO) {
            contractSignDetailEntity.setNewContractContent(JSON.toJSONString(contractSignTemplateDTO.getContractDetailInfoDTO()));
            contractSignDetailEntity.setSourceContractContent(JSON.toJSONString(contractSignTemplateDTO.getSourceContractDetailInfoDTO()));
            contractSignDetailEntity.setModifyContent(JSON.toJSONString(contractSignTemplateDTO.getTradeTicketDTO()));
        }

        contractSignDetailServer.saveContractSignDetail(contractSignDetailEntity);
    }
    //多品类V1 协议关联记录表添加数据 Author:Wan 2024-07-01 edn


    /**
     * 协议参数填充
     *
     * @param signCreateDTO
     * @return
     */
    private ContractSignEntity prepareSignInfo(ContractSignCreateDTO signCreateDTO) {
        ContractSignEntity contractSignEntity = BeanConvertUtils.convert(ContractSignEntity.class, signCreateDTO);

        Integer customerId = signCreateDTO.getCustomerId();

        Integer companyId = null;
        String companyName = null;

        if (ContractSalesTypeEnum.PURCHASE.getValue() == signCreateDTO.getSalesType()) {
            customerId = Integer.valueOf(signCreateDTO.getSupplierId());
        }

        if (ContractSalesTypeEnum.PURCHASE.getValue() == signCreateDTO.getSalesType()) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(signCreateDTO.getCustomerId());
            companyId = customerDTO.getCompanyId();
            log.info("===============================================================老CompanyId:{}", JSON.toJSONString(customerDTO.getCompanyId()));
            companyName = customerDTO.getShortName();
        } else {
            CustomerDTO customerDTO = customerFacade.getCustomerById(Integer.parseInt(signCreateDTO.getSupplierId()));
            companyId = customerDTO.getCompanyId();
            log.info("===============================================================老CompanyId:{}", JSON.toJSONString(customerDTO.getCompanyId()));
            companyName = customerDTO.getShortName();
        }

        // 查询客户信息
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        // 判断是否需要正本
        Integer needOriginalPaper = 0;
        //查询客户是否是需要正本
        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
        customerOriginalPaperDTO.setCustomerId(customerId);
        customerOriginalPaperDTO.setCompanyId(companyId);
        customerOriginalPaperDTO.setSaleType(signCreateDTO.getSalesType());
        customerOriginalPaperDTO.setStatus(DisableStatusEnum.ENABLE.getValue());
        customerOriginalPaperDTO.setCategory2(String.valueOf(signCreateDTO.getCategory2()));
        customerOriginalPaperDTO.setCategory3(String.valueOf(signCreateDTO.getCategory3()));
        log.info("===============================================================老CompanyId:{}", JSON.toJSONString(customerOriginalPaperDTO.getCompanyId()));

        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
        log.info("===============================================================customerOriginalPaperEntity:{}", JSON.toJSONString(customerOriginalPaperEntity));
        needOriginalPaper = isNeedOriginalPaper(customerOriginalPaperEntity, contractSignEntity, signCreateDTO);
        log.info("===================================================================needOriginalPaper:{}", needOriginalPaper);

        // 签章类型:是否使用易企签
        Integer signatureType = null != customerDTO.getUseYqq() && UseYqqEnum.USE_YQQ.getValue().equals(customerDTO.getUseYqq()) ? SignatureWayEnum.YI_QI_QIAN.getType() : SignatureWayEnum.FILE_UPLOAD.getType();
        Integer isLdcFrame;
        //CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailList(customerId, signCreateDTO.getGoodsCategoryId());
        ContractSignEntity signEntity = contractSignDao.queryContractSignByContractCode(signCreateDTO.getContractCode());
        if (null != signEntity) {
            isLdcFrame = signEntity.getLdcFrame();
            contractSignEntity.setCustomerNonFrame(signEntity.getCustomerNonFrame());
        } else {

            log.info("===================================================================customerOriginalPaperDTO:{}", customerOriginalPaperDTO);
            if (null == customerOriginalPaperEntity) {
                isLdcFrame = LdcFrameEnum.LDC.getValue();
                contractSignEntity.setCustomerNonFrame(LdcFrameEnum.LDC.getValue());
            } else {
                isLdcFrame = DisableStatusEnum.ENABLE.getValue().equals(customerOriginalPaperEntity.getStatus())
                        ? customerOriginalPaperEntity.getLdcFrame() : LdcFrameEnum.LDC.getValue();
                contractSignEntity.setCustomerNonFrame(customerOriginalPaperEntity.getLdcFrame());
            }
            if (ContractTradeTypeEnum.getNoHuskyTemplateTradeList().contains(contractSignEntity.getTradeType())) {
                isLdcFrame = LdcFrameEnum.NOT_FRAME.getValue();
            }
            // todo:数字合同开关
            //数字合同开关未配置，或者数字合同开关关闭，TT的质量指标配置判断，不生效，直接返回true，不做校验
//            SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.HUSKY_CONTRACT_PROVIDE.getRuleCode()));
//            if (null != systemRuleVO) {
//                if (!CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList())) {
//                    if (DisableStatusEnum.DISABLE.getValue().equals(systemRuleVO.getSystemRuleItemVOList().get(0).getStatus())) {
//                        if ("FL".equals(signCreateDTO.getDeliveryFactoryCode()) || companyId != 1) {
//                            isLdcFrame = LdcFrameEnum.NOT_FRAME.getValue();
//                        }
//                    }
//                }
//
//            }


        }
        CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
        customerProtocolDTO
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setCategoryId(signCreateDTO.getGoodsCategoryId())
                .setSaleType(signCreateDTO.getSalesType())
                .setCategory2(String.valueOf(signCreateDTO.getCategory2()))
        ;
        //框架协议属性，订单大合同信息
        CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
        Integer frameProtocolType = null != customerProtocolEntity ? customerProtocolEntity.getFrameProtocol() : FrameProtocolEnum.ORDER.getValue();
        TradeTicketEntity ticketEntity = tradeTicketQueryService.getByTtId(signCreateDTO.getTtId());
        Integer tradeType = null == ticketEntity ? 0 : ticketEntity.getTradeType();
        if (!ContractTradeTypeEnum.getNewTemplateProtocol().contains(tradeType)) {
            List<ContractSignEntity> signEntityList = contractSignDao.queryByContractId(contractSignEntity.getContractId());
            if (!CollectionUtils.isEmpty(signEntityList)) {
                signEntityList = signEntityList.stream()
                        .filter(it -> {
                            return !Arrays.asList(ContractSignStatusEnum.ABNORMAL.getValue(), ContractSignStatusEnum.INVALID.getValue()).contains(it.getStatus());
                        })
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(signEntityList)) {
                    signEntityList.sort(Comparator.comparing(ContractSignEntity::getId));
//                signEntityList.sort(Comparator.comparing(ContractSignEntity::getId).reversed());
                    frameProtocolType = signEntityList.get(0).getFrameProtocolType();
                }
                log.info("=======================修改协议生成类型============" + frameProtocolType);
            }
        }
        if (ContractSalesTypeEnum.PURCHASE.getValue() == signCreateDTO.getSalesType()) {
            frameProtocolType = ProtocolTypeEnum.CONTRACT.getCode();
        }

        //多品类V1 协议多品类字段添加 Author:Wan 2024-07-01 start
        //查询yqq是否启用
        String provideType = ProvideTypeEnum.HUSKY_CONTRACT.getCode();
        //查询签章通用状态是否开启
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        SystemRuleVO.SystemRuleItemVO systemRuleItemVO = systemRuleVO.getSystemRuleItemVOList().get(0);
        if (isLdcFrame.equals(LdcFrameEnum.NOT_FRAME.getValue())) {
            provideType = ProvideTypeEnum.FILE_UPLOAD.getCode();
        }

        //默认静默签
        String ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.SILENCE.getCode());

        if (systemRuleItemVO.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
            ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.OFFLINE_SIGN.getCode());
        } else if (isLdcFrame.equals(LdcFrameEnum.NOT_FRAME.getValue())) {
            ldcSignatureType = String.valueOf(LdcSignatureTypeEnum.NORMAL.getCode());
        }

        Integer contractSignType = tradeType;

        if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.NEW.getValue();
        } else if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        } else if (ContractTradeTypeEnum.FIXED.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.PRICE.getValue();
        } else if (ContractTradeTypeEnum.BUYBACK.getValue() == contractSignType) {
            contractSignType = ContractTradeTypeEnum.NEW.getValue();
        }

        String customerSignatureType = UseYqqEnum.USE_YQQ.getValue().equals(customerDTO.getUseYqq()) && GeneralEnum.YES.getValue().equals(customerDTO.getIsColumbus())
                ? CustomerSignatureTypeEnum.YQQ_SIGN.getCode() : CustomerSignatureTypeEnum.OFFLINE_SIGN.getCode();

        Integer SignType = null == signCreateDTO.getSignType() ? ContractSignTypeEnum.REMAIN_NUM.getValue() : signCreateDTO.getSignType();
        // 保存协议信息
        contractSignEntity
                .setContractSignType(String.valueOf(contractSignType))
                .setTermineType(String.valueOf(SignType))
                .setWsType(getWsTypeEnum(customerDTO) != null ? String.valueOf(getWsTypeEnum(customerDTO)) : null)
                .setLdcSignatureType(ldcSignatureType)
                .setCustomerSignatureType(customerSignatureType)
                .setNeedReview(String.valueOf(NeedReviewEnum.REVIEW.getValue()))
                .setProvideType(provideType)
                //todo 仓单上线前默认现货
                .setBuCode(BuCodeEnum.ST.getValue())
                .setCategory1(signCreateDTO.getCategory1())
                .setCategory2(signCreateDTO.getCategory2())
                .setCategory3(signCreateDTO.getCategory3())
                //多品类V1 协议多品类字段添加 Author:Wan 2024-07-01 end
                .setUuid(IdUtil.simpleUUID())
                .setSalesType(signCreateDTO.getSalesType())
                .setCompanyId(companyId)
                .setCompanyName(companyName)
                // .setProtocolCode(String.format("%03d", contractSignEntities.size() - 1))
                .setProtocolCode(signCreateDTO.getTtCode().contains("-") ? signCreateDTO.getTtCode().substring(signCreateDTO.getTtCode().indexOf("-") + 1) : "000").setStatus(ContractSignStatusEnum.WAIT_PROVIDE.getValue())
                .setNeedOriginalPaper(needOriginalPaper)
                //.setSignatureType(signatureType)
                .setBelongCustomerId(signCreateDTO.getBelongCustomerId())
                .setLdcFrame(isLdcFrame)
                .setFrameProtocolType(frameProtocolType)
                .setSignType(SignType)
                .setTradeType(tradeType)
                .setContractSource(null == signCreateDTO.getContractSource() ? 0 : signCreateDTO.getContractSource())
                .setSignatureWay(signatureType)
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
        return contractSignEntity;
    }

    //多品类V1 协议多品类字段添加是否是使用系统校验 Author:Wan 2024-07-01 start
    public Integer getWsTypeEnum(CustomerDTO customerDTO) {

        Map<String, Integer> wsMap = ImmutableMap.<String, Integer>builder()
                //不使用易企签，使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.NOT_USE_YQQ.getValue())
                //使用易企签，使用哥伦布
                .put(UseYqqEnum.USE_YQQ.getValue().toString() + DisableStatusEnum.ENABLE.getValue().toString(), UseYqqEnum.USE_YQQ.getValue())
                //不使用易企签，不使用哥伦布
                .put(UseYqqEnum.NOT_USE_YQQ.getValue().toString() + DisableStatusEnum.DISABLE.getValue().toString(), UseYqqEnum.NOT_SYSTEM.getValue())
                .build();
        return wsMap.get(customerDTO.getUseYqq().toString() + customerDTO.getIsColumbus().toString());
    }
    //多品类V1 协议多品类字段添加是否是使用系统校验 Author:Wan 2024-07-01 end

    @Override
    public FileBaseInfoDTO generateSignPdf(ContractSignProvideDTO signProvideDTO) {
        ContractSignEntity contractSignEntity = this.checkContractSign(signProvideDTO.getContractSignId());
        String htmlContent = signProvideDTO.getHtmlContent();
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genFileRelativePath(String.valueOf(contractSignEntity.getId()),
                contractSignEntity.getContractCode(),
                FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(),
                FilePathType.HTML)).setHtmlContent(htmlContent);
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        return fileBaseInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean provideContractSign(ContractSignProvideDTO signProvideDTO) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signProvideDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_PROVIDE.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_PROVIDE);
        }
        if (LdcFrameEnum.NOT_FRAME.getValue().equals(contractSignEntity.getLdcFrame())) {
            // 判断前端有文件回传
            if (CollectionUtils.isEmpty(signProvideDTO.getFileIdList())) {
                throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
            }
            // 保存文件关系表
            fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                    .setBizId(signProvideDTO.getContractSignId())
                    .setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode())
                    .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                    .setFileIdList(signProvideDTO.getFileIdList()));
        } else {
//            FileBaseInfoDTO fileBaseInfoDTO = generateSignPdfV2(contractSignEntity);
            String htmlContent = signProvideDTO.getHtmlContent();
//            if (StringUtils.isNotBlank(htmlContent)) {
//                htmlContent = Byte2HexUtil.hexStr2Str(htmlContent);
//                htmlContent =  Byte2HexUtil.unicodeToString(htmlContent);
//            }
            // 记录用户提交的模版信息
            contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO()
                    .setContractSignId(contractSignEntity.getId())
                    .setContractId(contractSignEntity.getContractId())
                    .setTtId(contractSignEntity.getTtId())
                    .setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue())
                    .setTemplateContent(htmlContent));
            HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                    .setHtmlUrl(FilePathUtil.genFileRelativePath(String.valueOf(contractSignEntity.getId()),
                            contractSignEntity.getContractCode(),
                            FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(),
                            FilePathType.HTML)).setHtmlContent(htmlContent)
                    .setContractCode(contractSignEntity.getContractCode())
                    .setTtCode(contractSignEntity.getTtCode());
            //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
            pdfInfoDTO.setCompanyId(contractSignEntity.getCompanyId());

            FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
            // 插入文件信息表
            FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
            // 插入文件关系表
            FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO()
                    .setFileIdList(Collections.singletonList(fileInfoEntity.getId()))
                    .setBizId(contractSignEntity.getId())
                    .setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode())
                    .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule());
            fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);

        }
        contractSignEntity.setIsCustomerSignature(signProvideDTO.getIsCustomerSignature());
        // 更新合同状态为待审核状态
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_REVIEW.getValue(), "");

        //记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.PROVIDE_SALES_CONTRACT_SIGN, "", SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.CREATE_E_CONTRACT, contractSignEntity, SystemEnum.MAGELLAN.getName());

        return true;
    }

    @Override
    public FileBaseInfoDTO previewHuskyContractPdf(TemplateEntity templateEntity) {
        ContractSignEntity contractSignEntity = this.checkContractSign(templateEntity.getContractSignId());
        //数字合同husky数据记录处理
        String htmlContent = templateSignFacade.provideContractSign(templateEntity);
        TemplateLoadEntity templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, templateEntity);
        //todo:nana
        templateLoadEntity.setType(TemplateLoadTypeEnum.USER_PREVIEW.getValue())
                .setContractSignId(templateEntity.getContractSignId())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTemplateId(templateEntity.getId())
                .setTemplateCode(templateEntity.getCode())
                .setTemplateName(templateEntity.getName())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.USER_PREVIEW) + "预览提交的Html文本：" + htmlContent)
                .setMemo(TemplateLoadTypeEnum.USER_PREVIEW.getDesc())
                .setBizData(FastJsonUtils.getBeanToJson(templateEntity));
        templateLoadEntity = templateLoadFacade.saveTemplateLoadLog(templateLoadEntity);
        // 记录用户提交的模版
        contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO().setContractSignId(contractSignEntity.getId()).setContractId(contractSignEntity.getContractId()).setTtId(contractSignEntity.getTtId()).setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue()).setTemplateContent(htmlContent));
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genHuskyFileRelativePath(String.valueOf(contractSignEntity.getId()), contractSignEntity.getContractCode(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(), FilePathType.HTML)).setHtmlContent(htmlContent)
                .setContractCode(contractSignEntity.getContractCode())
                .setTtCode(contractSignEntity.getTtCode())
                .setCompanyId(contractSignEntity.getCompanyId());
        //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        templateLoadEntity.setContent(templateLoadEntity.getContent() + "\n文件信息：=====" + FastJsonUtils.getBeanToJson(fileBaseInfoDTO));
        templateLoadFacade.updateTemplateLoadLog(templateLoadEntity);
        return fileBaseInfoDTO;
    }

    /**
     * 数字合同出具合同信息
     *
     * @param templateEntity
     * @return
     */
    @Override
    public TemplateEntity provideHuskyContractSign(TemplateEntity templateEntity) {
        if (null == templateEntity.getContractSignId()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(templateEntity.getContractSignId());
        if (ContractSignStatusEnum.WAIT_PROVIDE.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_PROVIDE);
        }
        //数字合同husky数据记录处理
        String htmlContent = templateSignFacade.provideContractSign(templateEntity);
        TemplateLoadEntity templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, templateEntity);

        templateLoadEntity.setType(TemplateLoadTypeEnum.USER_SUBMIT.getValue())
                .setContractSignId(templateEntity.getContractSignId())
                .setTtId(contractSignEntity.getTtId())
                .setTtCode(contractSignEntity.getTtCode())
                .setContractId(contractSignEntity.getContractId())
                .setContractCode(contractSignEntity.getContractCode())
                .setTemplateId(templateEntity.getId())
                .setTemplateCode(templateEntity.getCode())
                .setTemplateName(templateEntity.getName())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.USER_SUBMIT) + "出具提交的Html文本：" + htmlContent)
                .setMemo(TemplateLoadTypeEnum.USER_SUBMIT.getDesc())
                .setBizData(FastJsonUtils.getBeanToJson(templateEntity));
        templateLoadFacade.saveTemplateLoadLog(templateLoadEntity);
        // 记录用户提交的模版
        contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO().setContractSignId(contractSignEntity.getId()).setContractId(contractSignEntity.getContractId()).setTtId(contractSignEntity.getTtId()).setSignTemplateType(ContractSignTemplateType.USER_COMMIT_TEMPLATE_HTML.getValue()).setTemplateContent(htmlContent));
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO().setHtmlUrl(FilePathUtil.genHuskyFileRelativePath(String.valueOf(contractSignEntity.getId()), contractSignEntity.getContractCode(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(), FilePathType.HTML)).setHtmlContent(htmlContent)
                .setContractCode(contractSignEntity.getContractCode())
                .setTtCode(contractSignEntity.getTtCode())
                .setCompanyId(contractSignEntity.getCompanyId());
        //todo:wkhtmltopdf-nana
//            // 根据信息生成html和pdf
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        // 插入文件信息表
        FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
        // 插入文件关系表
        FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO().setFileIdList(Collections.singletonList(fileInfoEntity.getId())).setBizId(contractSignEntity.getId()).setCategoryType(FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode()).setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule());
        fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);
//        contractSignEntity.setIsCustomerSignature(templateEntity.getIsCustomerSignature());
        // 更新合同状态为待审核状态
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_REVIEW.getValue(), "");

        // 记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.PROVIDE_SALES_CONTRACT_SIGN, "", SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.CREATE_E_CONTRACT, contractSignEntity, SystemEnum.MAGELLAN.getName());
        this.sendProvideHuskyChangeSignEmail(templateEntity, contractSignEntity);
        return templateEntity;
    }

    /**
     * 数字合同-邮件通知：协议出具内容修改
     */
    public void sendProvideHuskyChangeSignEmail(TemplateEntity templateEntity, ContractSignEntity signEntity) {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.MAGELLAN_HUSKY_TEMPLATE_CHANGE.name())
                .setBusinessSceneCode(BusinessSceneEnum.MAGELLAN_HUSKY_TEMPLATE_CHANGE.getDesc())
                .setSystem(SystemEnum.MAGELLAN.getValue());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String operator = employFacade.getEmployCache(userId);
        Map<String, Object> map = new HashMap<>();
        List<String> modifyGroupCodeList = templateEntity.getGroupList().stream()
                .filter(groupEntity -> {
                    return null != groupEntity.getModify() && groupEntity.getModify() && StringUtils.isNotBlank(groupEntity.getCode());
                })
                .map(TemplateGroupEntity::getCode)
                .collect(Collectors.toList());
        String modifyGroupCodes = "无";
        String standardFileGroupCode = "M_standardFile";
        if (!CollectionUtils.isEmpty(modifyGroupCodeList)) {
            Result groupResult = templateGroupFacade.getTemplateGroupByCodeList(modifyGroupCodeList, null);
            List<TemplateGroupEntity> modifyGroupEntityList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupEntity.class);
            Map<String, List<TemplateGroupEntity>> groupMap = modifyGroupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
            modifyGroupCodeList = modifyGroupCodeList.stream().map(modifyGroupCode -> {
                String modifyGroupName = CollectionUtils.isEmpty(groupMap.get(modifyGroupCode)) ? "" : groupMap.get(modifyGroupCode).get(0).getName();
                if (standardFileGroupCode.equals(modifyGroupCode)) {
                    modifyGroupName = "企标文件";
                }
                return modifyGroupCode + "(" + modifyGroupName + ")";
            }).collect(Collectors.toList());
            modifyGroupCodes = StringUtils.join(modifyGroupCodeList, "、");
        }
        List<TemplateGroupEntity> addGroupList = templateEntity.getGroupList().stream()
                .filter(groupEntity -> {
                    return StringUtils.isBlank(groupEntity.getCode());
                })
                .collect(Collectors.toList());
        String deleteGroupCodes = "无";
        if (!CollectionUtils.isEmpty(templateEntity.getDeleteGroupCodeList())) {
            Result groupResult = templateGroupFacade.getTemplateGroupByCodeList(templateEntity.getDeleteGroupCodeList(), null);
            List<TemplateGroupEntity> deleteGroupEntityList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupEntity.class);
            Map<String, List<TemplateGroupEntity>> groupMap = deleteGroupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
            deleteGroupCodes = templateEntity.getDeleteGroupCodeList().stream().map(deleteGroupCode -> {
                String modifyGroupName = CollectionUtils.isEmpty(groupMap.get(deleteGroupCode)) ? "" : groupMap.get(deleteGroupCode).get(0).getName();
                return deleteGroupCode + "(" + modifyGroupName + ")";
            }).collect(Collectors.joining("、"));
        }
        //如果未修改、新增、删除条款组，则不发短信
        if (CollectionUtils.isEmpty(modifyGroupCodeList) && CollectionUtils.isEmpty(addGroupList) && CollectionUtils.isEmpty(templateEntity.getDeleteGroupCodeList())) {
            return;
        }

        map.put("contractCode", signEntity.getContractCode());
        map.put("ttCode", signEntity.getTtCode());
        map.put("tradeType", ContractTradeTypeEnum.getDescByValue(signEntity.getTradeType()));
        map.put("templateCode", templateEntity.getCode());
        map.put("templateName", templateEntity.getName());
        map.put("modifyGroupCodes", StringUtils.isNotBlank(modifyGroupCodes) ? modifyGroupCodes : "无");
        map.put("addGroupNum", addGroupList.size());
        map.put("deleteGroupCodes", deleteGroupCodes);
        map.put("operator", operator);
        map.put("sendDate", DateTimeUtil.formatDateStringCN(new Date()));
        //        List<String> receivers = new ArrayList<>();
//        receivers.add(String.valueOf(userId));
//        messageInfoDTO.setReceivers(receivers);
        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
    }


    /**
     * 模板匹配操作信息拼接
     *
     * @param templateLoadTypeEnum
     * @return
     */
    private String getTemplateLoadAct(TemplateLoadTypeEnum templateLoadTypeEnum) {
        return "(" + templateLoadTypeEnum.getValue() + ")" + templateLoadTypeEnum.getDesc() + "\n";
    }

    /**
     * 生成协议Pdf
     *
     * @param contractSignId 协议基本信息
     * @return 生成的协议PDF文件信息
     */
    @Override
    public String generateSignTemplate(Integer contractSignId) {
        ContractSignEntity contractSignEntity = checkContractSign(contractSignId);
        // 根据客户和品类查询客户配置
        Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();
        CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
        customerProtocolDTO
                .setCustomerId(customerId)
                .setCompanyId(contractSignEntity.getCompanyId())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSaleType(contractSignEntity.getSalesType())
                .setCategory2(String.valueOf(contractSignEntity.getCategory2()))
                .setCategory3(String.valueOf(contractSignEntity.getCategory3()))
        ;
        CustomerProtocolEntity customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
        Integer frameProtocolType = null != customerProtocolEntity ? customerProtocolEntity.getFrameProtocol() : FrameProtocolEnum.ORDER.getValue();
        if (!frameProtocolType.equals(contractSignEntity.getFrameProtocolType())) {
            contractSignEntity.setFrameProtocolType(frameProtocolType);
            contractSignDao.updateById(contractSignEntity);
        }
        // 1、获取合同的模板
        QueryTemplateAttributeDTO templateAttributeDTO = new QueryTemplateAttributeDTO()
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setContractType(contractSignEntity.getContractType())
                .setSalesType(contractSignEntity.getSalesType())
                .setTemplateType(frameProtocolType)
                //当拆分变更主体，区分SignType(2 部分拆分 3全部拆分)
                .setSignType(contractSignEntity.getSignType() != null && ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == contractSignEntity.getTradeType() ? contractSignEntity.getSignType() : 0)
                //除了split:3，转月：4,tradeType为0
                .setTradeType(contractSignEntity.getTradeType() != null
                        && Arrays.asList(TTTypeEnum.SPLIT.getType(),
                        TTTypeEnum.TRANSFER.getType(),
                        TTTypeEnum.REVERSE_PRICE.getType(),
                        TTTypeEnum.REVISE.getType()).contains(contractSignEntity.getTtType()) ? contractSignEntity.getTradeType() : 0)
                .setActionType(contractSignEntity.getTtType());
//        if (TTTypeEnum.SPLIT.getType() == contractSignEntity.getTtType()) {
//            templateAttributeDTO.setTemplateType()
//        }
        String templateContent = templateFacade.getTemplateInfo(templateAttributeDTO);
        ContractSignTemplateLogEntity signTemplateLogEntity = contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO()
                .setContractSignId(contractSignId)
                .setContractId(contractSignEntity.getContractId())
                .setTtId(contractSignEntity.getTtId())
                .setSignTemplateType(ContractSignTemplateType.SYSTEM_BASIC_TEMPLATE_HTML.getValue())
                .setTemplateContent(templateContent));

        //TODO NEO 这个方法错了
        //==============================================================
        // 2、获取协议业务数据
        /*SignTemplateDTO signTemplateDTO = tradeTicketFacade.getSignTemplateInfo(contractSignEntity.getTtId());

        if (null != signTemplateDTO.getTemplateCondition()) {
            GoodsInfoVO goodsInfoVO = goodsFacade.findGoodsById(contractSignEntity.getGoodsId());
            signTemplateDTO.getTemplateCondition().setSalesType(contractSignEntity.getSalesType())
                    .setSpecId(null == goodsInfoVO ? 0 : goodsInfoVO.getSpecId())
                    .setModifyList(CollectionUtils.isEmpty(signTemplateDTO.getTemplateCondition().getModifyList()) ? new ArrayList<>() : signTemplateDTO.getTemplateCondition().getModifyList());
        }
        signTemplateDTO.setBuyer(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerName() : contractSignEntity.getSupplierName())
                .setSeller(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());
        //获取二维码
        String qrCodeImage = StringUtils.isNotBlank(contractSignEntity.getQrCodeImage()) ? contractSignEntity.getQrCodeImage() :
                fileProcessFacade.generateQrCodeImg(qrCodeUrl + contractSignEntity.getId()).getFileUrl();
        //获取条形码
        String barCodeImage = StringUtils.isNotBlank(contractSignEntity.getBarCodeImage()) ? contractSignEntity.getBarCodeImage() :
                fileProcessFacade.generateBarCodeImg(contractSignEntity.getContractCode(), "").getFileUrl();
        String sasToken = azureBlobUtil.getSharedAccessSignature();
        signTemplateDTO.setEwm(qrCodeImage + sasToken)
                .setTxm(barCodeImage + sasToken);
        ContractPriceEntity cPrice = contractPriceService.getContractPriceEntityByTTId(contractSignEntity.getTtId());
        if (null == cPrice) {
            cPrice = contractPriceService.getContractPriceEntityContractId(contractSignEntity.getContractId());
        }
        if (null != cPrice) {
            String otherPriceInfo = contractSignBuildProcessor.getOtherPriceInfo(cPrice);
            signTemplateDTO.setPrx(otherPriceInfo);
        }*/
        //==============================================================

        SignTemplateDTO signTemplateDTO = buildSignTemplateDTO(contractSignId);

        /*contractSignTemplateLogDao.saveSignTemplateLog(
                new SignTemplateLogDTO().setContractSignId(contractSignId)
                        .setContractId(contractSignEntity.getContractId())
                        .setTtId(contractSignEntity.getTtId())
                        .setSignTemplateType(ContractSignTemplateType.COMPARE_TEST.getValue())
                        .setBizData(FastJsonUtils.getBeanToJson(signTemplateDTO))
                        .setTemplateContent(templateContent)
                        .setMemo("原始数据"));*/

        String bizContractData = FastJsonUtils.getBeanToJson(signTemplateDTO);
        signTemplateLogEntity.setBizData(bizContractData);
        contractSignTemplateLogDao.updateById(signTemplateLogEntity);

        // 3、数据对象->map
        Map<String, Object> dataMap = BeanUtil.beanToMap(signTemplateDTO);
        String bizContent = "";
        String errorMessage = "";
        // 4、渲染模板
        try {
            bizContent = TemplateRenderUtil.templateRender(dataMap, templateContent);
        } catch (Exception e) {
            bizContent = templateContent;
            errorMessage = e.getMessage();
            log.error(e.getMessage());
        }
        ContractSignTemplateLogEntity templateLogEntity = contractSignTemplateLogDao.saveSignTemplateLog(new SignTemplateLogDTO()
                .setContractSignId(contractSignId)
                .setContractId(contractSignEntity.getContractId())
                .setTtId(contractSignEntity.getTtId())
                .setSignTemplateType(ContractSignTemplateType.SYSTEM_COMPLETE_TEMPLATE_HTML.getValue())
                .setTemplateContent(bizContent)
                .setBizData(FastJsonUtils.getBeanToJson(signTemplateDTO))
                .setMemo(errorMessage));
        return StringUtils.isBlank(errorMessage) ? bizContent : templateLogEntity.getId() + bizContent;
    }

    /**
     * 重新改造方法：buildSignTemplateDTO
     *
     * @param contractSignId 协议ID
     * @return
     */
    @Override
    public SignHuskyTemplateDTO buildHuskySignTemplateV2(Integer contractSignId) {
        //新的组装模板数据的方法
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();
        TemplateLoadEntity templateLoadEntity = new TemplateLoadEntity();
        contractSignTemplateDTO = contractSignBuildProcessor.getContractSignTemplateDTO(contractSignId);
        SignHuskyTemplateDTO signHuskyTemplateDTO1 = new SignHuskyTemplateDTO();
        SignHuskyTemplateDTO signHuskyTemplateDTO = new SignHuskyTemplateDTO();

        if (null != contractSignTemplateDTO) {
            //记录合同信息
            TemplateLoadEntity templateLoadEntity1 = new TemplateLoadEntity()
                    .setType(0)
                    .setContractSignId(contractSignId)
                    .setContractId(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractId() : 0)
                    .setContractCode(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractCode() : "")
                    .setTtId(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtId() : 0)
                    .setTtCode(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtCode() : "")
                    .setContent("(0)合同基础信息获取：" + FastJsonUtils.getBeanToJson(contractSignTemplateDTO))
                    .setMemo("合同TT信息获取");
            templateLoadFacade.saveTemplateLoadLog(templateLoadEntity1);
            //业务信息获取
            signHuskyTemplateDTO = contractSignBuildProcessorV2.buildSignTemplateDTO(contractSignTemplateDTO);
//            signHuskyTemplateDTO1 = getSignHuskyTemplateDTO(contractSignTemplateDTO);
//            //出具合同，取值优化数据比对
//            List<CompareObjectDTO> diffObjectDTOList = BeanCompareUtils.compareFields(signHuskyTemplateDTO1, signHuskyTemplateDTO, Arrays.asList("barCodeImage", "qrCodeImage", "transferMonthTime"), new ArrayList<>());

            //模板日志基础信息装载
            templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, signHuskyTemplateDTO.getKeyVariableDTO());
            templateLoadEntity.setContractSignId(contractSignId)
                    .setContractId(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractId() : 0)
                    .setContractCode(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractCode() : "")
                    .setTtId(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtId() : 0)
                    .setTtCode(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtCode() : "");
            signHuskyTemplateDTO.setTemplateLoadEntity(templateLoadEntity);
        }
        return signHuskyTemplateDTO;
    }

    /**
     * 重新改造方法：buildSignTemplateDTO
     *
     * @param contractSignId 协议ID
     * @return
     */
    @Override
    public SignHuskyTemplateDTO buildHuskySignTemplateDTO(Integer contractSignId) {
        //新的组装模板数据的方法
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();
        TemplateLoadEntity templateLoadEntity = new TemplateLoadEntity();
        contractSignTemplateDTO = contractSignBuildProcessor.getContractSignTemplateDTO(contractSignId);
        SignHuskyTemplateDTO signHuskyTemplateDTO = new SignHuskyTemplateDTO();
        if (null != contractSignTemplateDTO) {
            //业务信息获取
            signHuskyTemplateDTO = getSignHuskyTemplateDTO(contractSignTemplateDTO);

            //todo:模板日志基础信息装载
            templateLoadEntity = BeanConvertUtils.convert(TemplateLoadEntity.class, signHuskyTemplateDTO.getKeyVariableDTO());
            templateLoadEntity.setContractSignId(contractSignId)
                    .setContractId(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractId() : 0)
                    .setContractCode(null != contractSignTemplateDTO.getContractDetailInfoDTO() ? contractSignTemplateDTO.getContractDetailInfoDTO().getContractCode() : "")
                    .setTtId(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtId() : 0)
                    .setTtCode(null != contractSignTemplateDTO.getContractSignDTO() ? contractSignTemplateDTO.getContractSignDTO().getTtCode() : "");
            signHuskyTemplateDTO.setTemplateLoadEntity(templateLoadEntity);
        }
        return signHuskyTemplateDTO;
    }

    private SignHuskyTemplateDTO getSignHuskyTemplateDTO(ContractSignTemplateDTO contractSignTemplateDTO) {
        SignHuskyTemplateDTO signHuskyTemplateDTO;
        SignTemplateDTO signTemplateDTO;
        signTemplateDTO = contractSignBuildProcessor.buildSignTemplateDTO(contractSignTemplateDTO);
        //补充信息，可以在之类中重写
        signTemplateDTO = fillSignTemplateDTO(signTemplateDTO, contractSignTemplateDTO);

        signHuskyTemplateDTO = SignHuskyTemplateConverter.convert(signTemplateDTO);
        ContractDetailInfoDTO contractDetailInfoDTO = contractSignTemplateDTO.getContractDetailInfoDTO();

        signHuskyTemplateDTO.setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(contractDetailInfoDTO.getSalesType()))
                .setProtocolTypeInfo(ProtocolTypeEnum.ORDER.getCode().equals(contractSignTemplateDTO.getContractSignDTO().getFrameProtocolType()) ? ProtocolTypeEnum.ORDER.getDesc() : ProtocolTypeEnum.CONTRACT.getDesc());
        //转换关键变量
        KeyVariableDTO keyVariableDTO = this.getKeyVariable(contractSignTemplateDTO);
        //集团客户编码
        keyVariableDTO.setEnterpriseCode(signTemplateDTO.getTemplateCondition().getEnterpriseCode());

        signHuskyTemplateDTO.setKeyVariableDTO(keyVariableDTO);
        CompanyEntity companyEntity = companyFacade.getCompanyByCode(keyVariableDTO.getCompanyCode());
        signHuskyTemplateDTO.setLogo(companyEntity.getLogo());
        //填充-质量指标信息
        this.fillHuskyTemplateDTO(signHuskyTemplateDTO, contractSignTemplateDTO);
        return signHuskyTemplateDTO;
    }

    private SignTemplateDTO buildSignTemplateDTO(Integer contractSignId) {
        //新的组装模板数据的方法
        ContractSignTemplateDTO contractSignTemplateDTO = new ContractSignTemplateDTO();
        SignTemplateDTO signTemplateDTO = new SignTemplateDTO();
        contractSignTemplateDTO = contractSignBuildProcessor.getContractSignTemplateDTO(contractSignId);
        if (null != contractSignTemplateDTO) {
            signTemplateDTO = contractSignBuildProcessor.buildSignTemplateDTO(contractSignTemplateDTO);
            //补充信息，可以在之类中重写
//            signTemplateDTO = fillSignTemplateDTO(signTemplateDTO, contractSignTemplateDTO);
        }
//        signTemplateDTO.setKeyVariableDTO(this.getKeyVariable(contractSignTemplateDTO));
        return signTemplateDTO;
    }

    /**
     * 组装-关键变量信息
     *
     * @param signTemplateDTO
     * @return
     */
    private KeyVariableDTO getKeyVariable(ContractSignTemplateDTO signTemplateDTO) {
        ContractDetailInfoDTO contractDetailInfoDTO = signTemplateDTO.getContractDetailInfoDTO();
        CompanyEntity companyEntity = companyFacade.queryCompanyById(contractDetailInfoDTO.getCompanyId());
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == contractDetailInfoDTO.getSalesType() ? contractDetailInfoDTO.getCustomerId() :
                contractDetailInfoDTO.getSupplierId();
        CustomerEntity customerEntity = customerFacade.getCustomerById(customerId);
        Integer tradeType = signTemplateDTO.getContractSignDTO().getTradeType();
        String protocolType = ProtocolTypeEnum.getByCode(signTemplateDTO.getContractSignDTO().getFrameProtocolType()).getValue();
        if (!Arrays.asList(ContractTradeTypeEnum.NEW.getValue(), ContractTradeTypeEnum.REVISE_NORMAL.getValue(),
                ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(), ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue(), ContractTradeTypeEnum.BUYBACK.getValue(), ContractTradeTypeEnum.PUT_BACK.getValue()).contains(tradeType)) {
            protocolType = ProtocolTypeEnum.AGREEMENT.getValue();
        }
        //采购 &&（新增、部分转月、部分反点价、全部反点价,回购）取：大合同
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractDetailInfoDTO.getSalesType() &&
                Arrays.asList(ContractTradeTypeEnum.NEW.getValue(), ContractTradeTypeEnum.REVISE_NORMAL.getValue(), ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue(),
                        ContractTradeTypeEnum.TRANSFER_PART.getValue(), ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(),
                        ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue(), ContractTradeTypeEnum.BUYBACK.getValue(), ContractTradeTypeEnum.PUT_BACK.getValue()).contains(tradeType)) {
            protocolType = ProtocolTypeEnum.CONTRACT.getValue();
        }

        KeyVariableDTO keyVariableDTO = new KeyVariableDTO()
                .setBuCode(BuCodeEnum.ST.getValue())
                .setCompanyCode(companyEntity.getShortName())
                .setSalesType(contractDetailInfoDTO.getSalesType())
                .setOriginalContractActionType(tradeType)
                //todo:nana 合同操作类型整理
                .setContractActionType(tradeType)
                .setCategoryId(contractDetailInfoDTO.getGoodsCategoryId())
                .setCategory1(contractDetailInfoDTO.getCategory1())
                .setCategory2(contractDetailInfoDTO.getCategory2())
                .setCategory3(contractDetailInfoDTO.getCategory3())
                //todo:nana补充协议
                .setProtocolType(protocolType)
                .setCustomerCode(customerEntity.getLinkageCustomerCode());
        if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.NEW.getValue();
        } else if (ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        } else if (ContractTradeTypeEnum.FIXED.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.PRICE.getValue();
        } else if (ContractTradeTypeEnum.BUYBACK.getValue() == tradeType) {
            tradeType = ContractTradeTypeEnum.NEW.getValue();
        }
        keyVariableDTO.setContractActionType(tradeType);
        return keyVariableDTO;
    }


    protected SignTemplateDTO fillSignTemplateDTO(SignTemplateDTO signTemplateDTO, ContractSignTemplateDTO contractSignTemplateDTO) {
        //TODO 如果各个场景有特有的设置，可以在之类中重写该方法
        return signTemplateDTO;
    }

    private void fillHuskyTemplateDTO(SignHuskyTemplateDTO huskyTemplateDTO, ContractSignTemplateDTO contractSignTemplateDTO) {
        if (null != contractSignTemplateDTO) {
            ContractDetailInfoDTO contractDetailInfoDTO = contractSignTemplateDTO.getContractDetailInfoDTO();
            QualityInfoDTO qualityInfoDTO = new QualityInfoDTO()
                    .setGoodsCategoryId(contractDetailInfoDTO.getGoodsCategoryId())
                    .setFactoryCode(contractDetailInfoDTO.getDeliveryFactoryCode())
                    .setWarehouseId(contractDetailInfoDTO.getShipWarehouseId())
                    .setUsage(contractDetailInfoDTO.getUsage())
                    .setSpecId(contractDetailInfoDTO.getGoodsSpecId())
                    .setGoodsId(contractDetailInfoDTO.getGoodsId())
                    .setSalesType(contractDetailInfoDTO.getSalesType())
                    .setCustomerId(ContractSalesTypeEnum.SALES.getValue() == contractDetailInfoDTO.getSalesType() ?
                            contractDetailInfoDTO.getCustomerId() : contractDetailInfoDTO.getSupplierId());
            QualityEntity qualityEntity = qualityFacade.matchQuality(qualityInfoDTO);
            huskyTemplateDTO.setQualityInfo(null == qualityEntity ? "" : qualityEntity.getContent())
                    .setQualityId(null == qualityEntity ? 0 : qualityEntity.getId());
        }
    }

//    /**
//     * 生成协议Pdf
//     *
//     * @param contractSignEntity 协议基本信息
//     * @return 生成的协议PDF文件信息
//     */
//    private FileBaseInfoDTO generateSignPdfV2(ContractSignEntity contractSignEntity) {
//        // TODO 合同模板问题
//        // 获取合同的模板
//        String templateData = (String) templateFacade.getTemplateById("1").getData();
//
//        // 数据对象->map
//        Map<String, Object> dataMap = BeanUtil.beanToMap(contractSignEntity);
//        // 渲染模板
//        String content = TemplateRenderUtil.templateRender(dataMap, templateData);
//
//        HtmlInfoDTO htmlInfoDTO = new HtmlInfoDTO()
//                .setHtmlUrl(FilePathUtil.genFileRelativePath(String.valueOf(contractSignEntity.getId()),
//                        contractSignEntity.getContractCode(),
//                        FileCategoryType.CONTRACT_PDF_ORIGINAL.getMsg(),
//                        FilePathType.HTML))
//                .setHtmlContent(content);
//
//        // 根据信息生成html和pdf
//        return fileProcessFacade.html2Pdf(htmlInfoDTO);
//    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewContractSign(ContractSignReviewDTO signReviewDTO) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_REVIEW.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_CHECKED);
        }
        // 审核通过：待签章

        if (ContractReviewStatusEnum.PASS.getValue() == signReviewDTO.getReviewStatus()) {
            contractSignDao.updateById(contractSignEntity
                    .setStatus(ContractSignStatusEnum.WAIT_STAMP.getValue())
                    //审核通过修改协议签署状态
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.NOT_START.getValue())
                    // 清空驳回原因
                    .setRejectReason(""));
        } else {
            this.reviewReject(contractSignEntity, signReviewDTO.getReviewRemark());
        }
        recordSignOperationLogDetail(OperationActionEnum.REVIEW_CONTRACT_PASS, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    /**
     * 协议审核通过
     *
     * @param contractSignEntity 协议实体
     * @return 审核通过结果
     */
    private boolean reviewPass(ContractSignEntity contractSignEntity) {
        List<FileInfoEntity> fileInfoEntityList = fileBusinessFacade.getFileInfoByBizIdAndType(contractSignEntity.getId(),
                FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(),
                DisableStatusEnum.ENABLE.getValue());

        //查询签章通用状态是否开启
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        SystemRuleVO.SystemRuleItemVO systemRuleItemVO = systemRuleVO.getSystemRuleItemVOList().get(0);


        boolean yqqSys = systemRuleItemVO.getStatus().equals(DisableStatusEnum.ENABLE.getValue());
        boolean yqqAuth = this.customerYqqAuth(contractSignEntity);

        //调用易企签配置
        YqqSignParameterEntity yqqSignParameterEntity = yqqSignParameterFacade.queryYqqSignParameterByCompanyId(contractSignEntity.getCompanyId());

        Boolean b = yqqSignParameterVerify(yqqSignParameterEntity, contractSignEntity);

        if (yqqSys) {
            Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();
            CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
            customerOriginalPaperDTO.setCustomerId(customerId)
                    .setCategoryId(contractSignEntity.getGoodsCategoryId())
                    .setCompanyId(contractSignEntity.getCompanyId())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setSaleType(contractSignEntity.getSalesType())
            ;
            /*CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
            Integer isLdcFrame = null != customerOriginalPaperEntity ? customerOriginalPaperEntity.getLdcFrame() : LdcFrameEnum.NOT_FRAME.getValue();*/

            fileInfoEntityList.forEach(fileInfoEntity -> {

                String sasToken = azureBlobUtil.getSharedAccessSignature();
                //解析文件路径

                String filePathUrl = urlPathAnalysis(fileInfoEntity.getFilePathUrl());
                String url = filePathUrl + sasToken;
                log.info("------------------------------------------------------------------");
                log.info("FilePathUrl:{}", url);
                log.info("------------------------------------------------------------------");

                //获取文件页数
                Integer pageNo = FilePagesUtils.filesPage(url, fileInfoEntity.getExtension());


                // 发起签章
                StartEnvelopeDTO startEnvelopeDTO = new StartEnvelopeDTO()
                        .setLoginId(Integer.parseInt(JwtUtils.getCurrentUserId()))
                        .setContractId(contractSignEntity.getId())
                        .setContractCode(contractSignEntity.getContractCode())
                        .setContractSignId(contractSignEntity.getId())
                        .setUuid(contractSignEntity.getUuid())
                        .setCustomerId(contractSignEntity.getCustomerId())
                        .setFileId(fileInfoEntity.getId()).setPageNo(pageNo)
                        .setExtension(fileInfoEntity.getExtension())
                        .setUrl(url).setSystem(SystemEnum.MAGELLAN.getValue())
                        .setTitle(contractSignEntity.getContractCode())
                        .setLdcFrame(null != contractSignEntity.getLdcFrame() ? contractSignEntity.getLdcFrame() : LdcFrameEnum.LDC.getValue())
                        .setCompanyId(contractSignEntity.getCompanyId())
                        .setYqqSignParameterEntity(yqqSignParameterEntity)
                        .setIsCustomerUseYqq(yqqAuth);
                //签章返回签章状态
                signatureFacade.envelopesStart(startEnvelopeDTO);
                contractSignEntity
                        .setIsOnLineSign(IsOnLineSignEnum.ON_LINE.getValue())
                        .setSignatureType(yqqAuth ? SignatureTypeEnum.BOTH_SIGNATURE.getValue() : SignatureTypeEnum.LDC_ONE_SIGNATURE.getValue())
                        .setVoluntarilySignType(VoluntarilySignTypeEnum.NODE_START.getValue())
                        .setSignErrorMessage(ContractSignYQQErrorEnum.SUCCEED.getMessage());
            });
        } else {
            contractSignEntity.setSignErrorMessage(ContractSignYQQErrorEnum.NOT_AUTH.getMessage()).setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue());
        }

        contractSignDao.updateById(contractSignEntity);
        recordSignOperationLogDetail(OperationActionEnum.SIGNATURE_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    /**
     * 判断易企签配置
     *
     * @param yqqSignParameterEntity
     * @param contractSignEntity
     * @return
     */
    private Boolean yqqSignParameterVerify(YqqSignParameterEntity yqqSignParameterEntity, ContractSignEntity contractSignEntity) {
        if (null == yqqSignParameterEntity) {
            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("未找到易企签配置");
            contractSignDao.updateById(contractSignEntity);
            return true;
        } else if (DisableStatusEnum.DISABLE.getValue().equals(yqqSignParameterEntity.getStatus())) {
            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("易企签配置已被禁用");
            contractSignDao.updateById(contractSignEntity);
            return true;
        } else if (StringUtil.isEmpty(yqqSignParameterEntity.getAppId())
                || StringUtil.isEmpty(yqqSignParameterEntity.getAppSecretKey())
                || StringUtil.isEmpty(yqqSignParameterEntity.getSealName())) {

            contractSignEntity
                    .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                    .setSignErrorMessage("配置缺少参数");
            contractSignDao.updateById(contractSignEntity);
            return true;
        }

        return false;
    }

    /**
     * 文件路径解析
     *
     * @param url
     * @return
     */
    private String urlPathAnalysis(String url) {
        int index = url.lastIndexOf("/");
        String hostUrl = getHostUrl();
        try {
            //判断路径层级
            if (index >= 0) {
                String uFileName = url.substring(index);
                String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
                String pathDir = url.substring(0, index);
                url = pathDir + fileNameEncode;
            } else {
                String fileNameEncode = URLEncoder.encode(url, "UTF-8");
                url = hostUrl + fileNameEncode;
            }
        } catch (Exception ex) {
            log.error("FileNotFoundException", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        return url;
    }

    /**
     * 获取文件访问url域名路径
     */
    public String getHostUrl() {
        String host = azureBlobProperties.getHost();
        String env = azureBlobProperties.getEnv();
        String containName = azureBlobProperties.getContainName();
        return host + "/" + containName + "/" + env + "/";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean contractSignStartSignature(ContractSignReviewDTO signReviewDTO) {

        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_STAMP.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR);
        }

        if (VoluntarilySignTypeEnum.NODE_START.getValue().equals(contractSignEntity.getVoluntarilySignType()) || VoluntarilySignTypeEnum.VOLUNTARILY_SIGN_FAILED.getValue().equals(contractSignEntity.getVoluntarilySignType())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR);
        }

        reviewPass(contractSignEntity);
        return true;
    }


    @Override
    public boolean UpdateContractSignError(ContractSignErrorDTO contractSignErrorDTO) {
        ContractSignEntity contractSignEntity = contractSignDao.queryByUUId(contractSignErrorDTO.getUuid());
        if (null != contractSignEntity) {
            contractSignEntity.setSignErrorCode(contractSignErrorDTO.getSignErrorCode()).setSignErrorMessage(contractSignErrorDTO.getSignErrorMessage());

            return contractSignDao.updateById(contractSignEntity);
        }

        return false;
    }

    /**
     * 协议审核驳回
     *
     * @param contractSignEntity 协议实体
     * @param reviewRemark       驳回原因
     * @return 审核驳回结果
     */
    private boolean reviewReject(ContractSignEntity contractSignEntity, String reviewRemark) {
        // 审核驳回:待出具,清除文件信息
        fileBusinessFacade.dropFileRelation(contractSignEntity.getId(), FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode(), reviewRemark);

        contractSignEntity.setRejectReason(StringUtil.isEmpty(reviewRemark) ? "未填写" : reviewRemark);

        // 记录驳回原因
        updateStatus(contractSignEntity, ContractSignStatusEnum.WAIT_PROVIDE.getValue(), reviewRemark);

        // 记录审核日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.REJECT_SALES_CONTRACT_SIGN, contractSignEntity.getContractCode(), SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.REVIEW_CONTRACT_REJECT, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean abnormalContractSign(Integer contractSignId, String remark) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(contractSignId);

        // 更新状态
        contractSignDao.updateById(contractSignEntity.setStatus(ContractSignStatusEnum.ABNORMAL.getValue()).setInvalidReason(remark));

        // 清除所有文件信息
        FileCategoryType.getAllContractPdfType().forEach(type -> fileBusinessFacade.dropFileRelation(contractSignId, type, ""));

        // 记录操作日志
        //addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.ABNORMAL_SALES_CONTRACT_SIGN, "", null);
        recordSignOperationLogDetail(OperationActionEnum.ABNORMAL_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidContractSign(ContractSignReviewDTO signReviewDTO) {
        //校验协议信息
        ContractSignEntity contractSignEntity = this.checkContractSign(signReviewDTO.getContractSignId());

        // 更新状态
        contractSignDao.updateById(contractSignEntity
                // FIXME 作废的显示问题
                .setStatus(ContractSignStatusEnum.INVALID.getValue()).setInvalidReason(signReviewDTO.getReviewRemark()));

        // 清除所有文件信息
        FileCategoryType.getAllContractPdfType().forEach(type -> fileBusinessFacade.dropFileRelation(contractSignEntity.getId(), type, signReviewDTO.getReviewRemark()));

        // 记录操作日志
        // addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.INVALID_SALES_CONTRACT_SIGN, "", SystemEnum.MAGELLAN.getValue());
        recordSignOperationLogDetail(OperationActionEnum.INVALID_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContractSignYQQUrl(ContractSignYQQUrlDTO contractSignYQQUrlDTO) {
        ContractSignEntity contractSignEntity = contractSignDao.queryByUUId(contractSignYQQUrlDTO.getUuid());

        //判断协议是否为空
        if (contractSignEntity == null) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        //协议状态
        Integer status = contractSignYQQUrlDTO.getStatus();
        if (null == status) {
            status = contractSignEntity.getStatus();
        }

        Integer oldStatus = contractSignEntity.getStatus();
        // 状态是否改变: 下一个状态是否满足
        if (oldStatus == ContractSignStatusEnum.PROCESSING.getValue() || oldStatus == ContractSignStatusEnum.INVALID.getValue() || oldStatus == ContractSignStatusEnum.ABNORMAL.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_STATUS_EXCEPTION);
        }

        contractSignEntity = BeanConvertUtils.convert(ContractSignEntity.class, contractSignYQQUrlDTO);

        //协议签章地址
        String url = contractSignYQQUrlDTO.getSignatureUrl();
        //协议签章状态
        Integer signatureStatus = contractSignEntity.getSignatureStatus();

        //this.addContractSignOperationLog(contractSignEntity, LogBizCodeEnum.START_SIGN_SALES_CONTRACT_SIGN, null, null);
        contractSignDao.updateById(contractSignEntity);
        try {
            recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        } catch (Exception e) {
            log.error("记录日志错误");
        }
        return true;
    }

    @Override
    public boolean updateContractSignYQQCallback(ContractSignYQQCallbackDTO contractSignYQQCallbackDTO) {
        log.info("updateContractSignYQQCallback.begin:.uuid:{} -- data:{}", contractSignYQQCallbackDTO.getUuid(), JSON.toJSONString(contractSignYQQCallbackDTO));

        ContractSignEntity contractSignEntity = contractSignDao.queryByUUId(contractSignYQQCallbackDTO.getUuid());

        //判断协议是否为空
        if (contractSignEntity == null) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        if (null != contractSignYQQCallbackDTO.getStatus()
                && ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignYQQCallbackDTO.getStatus()
                && StrUtil.isBlank(contractSignYQQCallbackDTO.getSignatureUrl())
                && SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType()) {
            sendSignJudgeInmail(contractSignEntity, contractSignYQQCallbackDTO);
            //记录日志
            recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
            return true;
        }

        Integer oldStatus = contractSignEntity.getStatus();
        // 状态是否改变: 下一个状态是否满足
        if (oldStatus == ContractSignStatusEnum.PROCESSING.getValue() || oldStatus == ContractSignStatusEnum.INVALID.getValue() || oldStatus == ContractSignStatusEnum.ABNORMAL.getValue()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_STATUS_EXCEPTION);
        }
        contractSignEntity.setStatus(null == contractSignYQQCallbackDTO.getStatus() ? contractSignEntity.getStatus() : contractSignYQQCallbackDTO.getStatus());
        contractSignEntity.setSignatureUrl(StrUtil.isBlank(contractSignYQQCallbackDTO.getSignatureUrl()) ? "" : contractSignYQQCallbackDTO.getSignatureUrl());
        contractSignEntity.setSignatureStatus(null == contractSignYQQCallbackDTO.getSignatureStatus() ? contractSignEntity.getSignatureStatus() : contractSignYQQCallbackDTO.getSignatureStatus());
        contractSignEntity.setVoluntarilySignType(null == contractSignYQQCallbackDTO.getVoluntarilySignType() ? contractSignEntity.getVoluntarilySignType() : contractSignYQQCallbackDTO.getVoluntarilySignType());
        contractSignEntity.setBackStatus(null == contractSignYQQCallbackDTO.getBackStatus() ? contractSignEntity.getBackStatus() : contractSignYQQCallbackDTO.getBackStatus());

        contractSignEntity.setSignErrorCode("").setSignErrorMessage("");

        contractSignDao.updateById(contractSignEntity);

        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus()
                && (TTTypeEnum.REVERSE_PRICE.getType().equals(contractSignEntity.getTtType()))) {
            //点价转月修改原合同状态
            priceUpdateContractStatusByWaitBack(contractSignEntity);
        }
        sendSignJudgeInmail(contractSignEntity, contractSignYQQCallbackDTO);
        //记录日志
        recordSignOperationLogDetail(OperationActionEnum.START_SIGN_SALES_CONTRACT_SIGN, contractSignEntity, SystemEnum.MAGELLAN.getName());
        log.info("updateContractSignYQQCallback.finish.uuid:{} -- data:{}", contractSignEntity.getUuid(), JSON.toJSONString(contractSignEntity));
        return true;
    }

    //点价返点价修改
    private void priceUpdateContractStatusByWaitBack(ContractSignEntity contractSignEntity) {

        Integer contractId = contractSignEntity.getContractId();

        Integer contractStatus = ContractStatusEnum.EFFECTIVE.getValue();
        //if (TTTypeEnum.REVERSE_PRICE.getType().equals(contractSignEntity.getTtType())) {
        //查询TT
        TradeTicketEntity tradeTicketEntity = tradeTicketQueryService.getByTtId(contractSignEntity.getTtId());

        //根据GroupID查询出原合同TT
        TradeTicketEntity tradeTicketGroup = tradeTicketQueryService.getByGroupId(tradeTicketEntity.getGroupId(), tradeTicketEntity.getId());
        //获取原合同id
        contractId = tradeTicketGroup.getContractId();
        //根据ttID查询 ttTranfer
        TTTranferEntity ttTranferEntity = ttTranferService.selectTTTranferByTTId(tradeTicketEntity.getId());
        if (null != ttTranferEntity) {
            List<TTTranferEntity> ttTranferEntities = ttTranferService.getTTTranferByContractId(contractId, ttTranferEntity.getId());

            for (TTTranferEntity ttTranfer : ttTranferEntities) {
                ContractSignEntity contractSign = contractSignDao.getSignDetailByTtId(ttTranfer.getTtId());

                if (null != contractSign && ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSign.getStatus()) {
                    contractStatus = ContractStatusEnum.MODIFYING.getValue();
                    break;
                }
            }
        }
        /*} else {
            TTPriceEntity ttPriceEntity = ttPriceService.getTTPriceEntityByTTId(contractSignEntity.getTtId());
            if (null != ttPriceEntity) {
                List<TTPriceEntity> ttPriceEntities = ttPriceService.getPriceByContractNotId(ttPriceEntity.getContractId(), ttPriceEntity.getId());

                for (TTPriceEntity ttPrice : ttPriceEntities) {
                    ContractSignEntity contractSign = contractSignDao.getSignDetailByTtId(ttPrice.getTtId());

                    if (ContractSignStatusEnum.WAIT_STAMP.getValue() >= contractSign.getStatus()) {
                        contractStatus = ContractStatusEnum.MODIFYING.getValue();
                        break;
                    }
                }
            }
        }*/
        //修改合同状态
        ContractEntity contractEntity = contractQueryService.getBasicContractById(contractId);
        contractEntity.setStatus(contractStatus);
        contractQueryService.updateContract(contractEntity);

    }

    private void sendSignJudgeInmail(ContractSignEntity contractSignEntity, ContractSignYQQCallbackDTO contractSignYQQCallbackDTO) {
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == contractSignEntity.getStatus() && StrUtil.isNotBlank(contractSignYQQCallbackDTO.getSignatureUrl())) {
            log.info("========================================");
            log.info("updateContractSignYQQCallback.finish.uuid:{} -- Status:{}", contractSignEntity.getUuid(), contractSignEntity.getStatus());
            CustomerEntity customerEntity = customerFacade.getCustomerById(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerId() : Integer.parseInt(contractSignEntity.getSupplierId()));
            log.info("updateContractSignYQQCallback.finish.uuid:{},customerEntity:{}", contractSignEntity.getUuid(), JSON.toJSONString(customerEntity));
            if (null != customerEntity && GeneralEnum.YES.getValue().equals(customerEntity.getIsColumbus())) {
                try {
                    log.info("========================================");
                    sendSignInmail(contractSignEntity, customerEntity);
                } catch (Exception e) {
                    log.debug("sendSignInmail.Exception{}", e.getMessage());
                }
            }

        }
    }

    /**
     * 上传文件,给客户发送站内信
     *
     * @param contractSignEntity
     * @param customerEntity
     */
    public void sendSignInmail(ContractSignEntity contractSignEntity, CustomerEntity customerEntity) {
        log.info("sendSignInmail:messageFacade.sendMessage:TtCode{},sendStart", contractSignEntity.getTtCode());

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_SIGN_INMAIL.getDesc());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_SIGN_INMAIL.name());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setCustomerId(customerEntity.getId());

        Integer roleDef = ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? 203 : 206;
        log.info("sendSignInmail.roleDef:TtCode:{},roleDef:{}", contractSignEntity.getTtCode(), roleDef);
        Integer salesType = ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? ContractSalesTypeEnum.PURCHASE.getValue() : ContractSalesTypeEnum.SALES.getValue();

        List<CRoleEntity> cRoleEntities = cRoleFacade.queryRoleListByDefInfosSalesType(Arrays.asList(roleDef), contractSignEntity.getGoodsCategoryId(), salesType);
        log.info("sendSignInmail.roleDef:TtCode:{},cRoleEntities", contractSignEntity.getTtCode());
        if (cRoleEntities.isEmpty()) {
            return;
        }
        CRoleEntity roleEntity = cRoleEntities.get(0);
        List<String> receiver = new ArrayList<>();
        receiver.add(String.valueOf(roleEntity.getId()));
        messageInfoDTO.setReceivers(receiver);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("contractCode", contractSignEntity.getContractCode());
        dataMap.put("TTCode", contractSignEntity.getTtCode());
        messageInfoDTO.setDataMap(dataMap);

        messageFacade.sendMessage(messageInfoDTO);

        log.info("sendSignInmail:messageFacade.sendMessage:TtCode{},sendAccomplish", contractSignEntity.getTtCode());
    }


    /**
     * 上传文件,给客户发送邮件
     *
     * @param contractSignEntity
     */
    public void signSendBothSignatureMail(ContractSignEntity contractSignEntity) {

        StringBuilder errorMsg = new StringBuilder();

        try {
            //记录日志
            operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail", JSON.toJSONString(contractSignEntity)));
        } catch (Exception e) {
            log.error("记录日志错误signSendBothSignatureMail:{}", JSON.toJSONString(e));
        }
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        messageInfoDTO.setReferBizId(contractSignEntity.getId().toString());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name());
        messageInfoDTO.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setSalesType(contractSignEntity.getSalesType());

        Integer customerId;
        if (contractSignEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            customerId = contractSignEntity.getCustomerId();
        } else {
            customerId = Integer.valueOf(contractSignEntity.getSupplierId());
        }

        try {
            List<String> receivers = new ArrayList<>();
            receivers.add(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                    ? contractSignEntity.getSupplierId() : customerId.toString());
            messageInfoDTO.setReceivers(receivers);
        } catch (NumberFormatException e) {
            errorMsg.append("获取客户联系人异常");
        }
        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = getCustomerEmailData(contractSignEntity);

        //业务数据
        messageInfoDTO.setDataMap(BeanUtil.beanToMap(customerSignNoticeItemDTO));

        //发送消息
        try {
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            errorMsg.append("消息服务异常:" + e.getMessage());
        }

        try {
            //记录traceLog
            if (StringUtil.isNotEmpty(errorMsg.toString())) {
                //记录日志
                operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail.Exception", JSON.toJSONString(errorMsg)));
            }
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }

    }

    /**
     * 保存快递信息
     *
     * @param contractPaperDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveContractPaperSign(ContractPaperDTO contractPaperDTO) {

        boolean resultRow = false;

        ContractPaperEntity contractPaperEntity = contractPaperDao.getContractPaper(contractPaperDTO.getContractSignId());
        ContractSignEntity contractSignEntity = checkContractSign(contractPaperDTO.getContractSignId());

        if (null != contractPaperEntity) {
            //修改快递信息
            contractPaperEntity = BeanConvertUtils.convert(ContractPaperEntity.class, contractPaperDTO);
            contractPaperEntity.setUpdatedAt(new Date());
            contractPaperDao.updateById(contractPaperEntity);
            //修改协议状态
            contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
            return contractSignDao.updateById(contractSignEntity) ? 1 : 0;
        }

        // 保存合同的正本信息
        contractPaperService.saveContractPaper(contractPaperDTO);

        // 修改协议状态
        contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue());
        resultRow = contractSignDao.updateById(contractSignEntity);

        try {
            recordSignOperationLogDetail(OperationActionEnum.SAVE_CONTRACT_PAPER, contractSignEntity, SystemEnum.MAGELLAN.getName());
        } catch (Exception e) {
            log.debug("saveContractPaperSign:{}", e.getMessage());
        }

        return resultRow ? 1 : 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean postBackContractSign(ContractBaseSignDTO contractBaseSignDTO, Integer status, OperationActionEnum operationActionEnum) {
        // 通过页面的文件上传到服务器，返回文件信息
        // 校验合同
        ContractSignEntity contractSignEntity = checkContractSign(contractBaseSignDTO.getContractSignId());

        // 校验文件信息
        if (ArrayUtil.isEmpty(contractBaseSignDTO.getFileIdList())) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
        }

        int fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode();

        if (ContractSignStatusEnum.WAIT_CONFIRM.getValue() == status) {
            fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode();
            contractSignEntity.setBackStatus(BackStatusEnum.UPLOAD.getValue());
        } else {
            //发送客户签章提醒
            //sendCustomerNoticeEmail(contractSignEntity);
        }

        // 保存文件关系表
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(contractBaseSignDTO.getContractSignId())
                .setCategoryType(fileCategoryType)
                .setModuleType(ModuleTypeEnum.CONTRACT_SIGN.getModule())
                .setFileIdList(Arrays.stream(contractBaseSignDTO.getFileIdList()).collect(Collectors.toList())));

        // 更改合同状态为待确认合规
        updateStatus(contractSignEntity, status, "");

        //上传发送站内信
        if (ContractSignStatusEnum.WAIT_BACK.getValue() == status) {
            CustomerEntity customerEntity = customerFacade.getCustomerById(ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType() ? contractSignEntity.getCustomerId() : Integer.parseInt(contractSignEntity.getSupplierId()));
            //点价/反点价修改协议状态
            if (TTTypeEnum.REVERSE_PRICE.getType().equals(contractSignEntity.getTtType())) {
                //点价转月修改原合同状态
                priceUpdateContractStatusByWaitBack(contractSignEntity);
            }
            sendSignInmail(contractSignEntity, customerEntity);
            sendCustomerNoticeEmail(contractSignEntity);
        }

        SystemEnum systemEnum = SystemEnum.getByValue(contractBaseSignDTO.getSystem());
        recordSignOperationLogDetail(operationActionEnum, contractSignEntity, systemEnum.getName());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContractSign(Integer ttId) {
        ContractSignEntity contractSignEntity = contractSignDao.getSignDetailByTtId(ttId);
        return contractSignDao.updateById(contractSignEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue()));
    }

    @Override
    public int updateContractId(Integer ttId, Integer contractId) {
        return contractSignDao.updateContractId(ttId, contractId);
    }

    @Override
    public boolean updateContractSignStatus(Integer signId, Integer status, String memo) {
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);
        contractSignEntity.setStatus(status);
        if (StringUtils.isNotBlank(memo)) {
            String memoInfo = StringUtils.isNotBlank(contractSignEntity.getMemo()) ? contractSignEntity.getMemo() : "";
            contractSignEntity.setMemo(memoInfo + memo);
        }
        return contractSignDao.updateById(contractSignEntity);
    }

    @Override
    public boolean deleteContractSignById(Integer signId) {
        //基差修改为一口价，合同全部定价后，在合同详情中点击【定价完成】按钮，确认提交后，立即生效（不可撤回、作废）；无审批、协议出具；系统会自动生成修改的TT，TT记录了合同类型修改为一口价。合同其他数据保持不变。
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);
        contractSignEntity.setStatus(ContractSignStatusEnum.PROCESSING.getValue()).setIsDeleted(IsDeletedEnum.DELETED.getValue());
        return contractSignDao.updateById(contractSignEntity);
    }

    @Override
    public Result sendCustomerNoticeEmail(Integer signId) throws Exception {
        //获取签章记录
        ContractSignEntity contractSignEntity = this.checkContractSign(signId);

        sendCustomerNoticeEmail(contractSignEntity);

        return Result.success();
    }

    public void sendCustomerNoticeEmail(ContractSignEntity contractSignEntity) {
        StringBuilder errorMsg = new StringBuilder();
        //记录日志
        operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail", JSON.toJSONString(contractSignEntity)));

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        messageInfoDTO.setReferBizId(contractSignEntity.getId().toString());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGN_NOTICE.name());
        messageInfoDTO.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setSalesType(contractSignEntity.getSalesType());

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = null;

        try {
            //拼装客户签章提醒的业务数据
            customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();
            customerSignNoticeItemDTO = getCustomerNoticeEmailData(contractSignEntity);
        } catch (Exception e) {
            errorMsg.append("获取签章记录数据异常；");
        }


        try {
            Integer customerId = customerSignNoticeItemDTO.getCustomerId();
            List<String> receivers = new ArrayList<>();
            receivers.add(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? contractSignEntity.getSupplierId() : customerId.toString());
            messageInfoDTO.setReceivers(receivers);

            //SP22021205后废除
            /*
            //获取客户信息（接收人）
            CustomerAllMessageDTO customerInfo = new CustomerAllMessageDTO();

            customerInfo.setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                    ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId());

            customerInfo.setSalesType(contractSignEntity.getSalesType());
            customerInfo.setCategoryId(contractSignEntity.getGoodsCategoryId());
            customerInfo.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
            CustomerDTO customerDTO = customerFacade.queryCustomerContactAllMessage(customerInfo);
            if (null != customerDTO) {
                List<ContactEntity> contactEntityList = customerDTO.getContactDTO();
                //传递所有的客户信息
                List<ReceiverContactVO> receiverList = new ArrayList<>();
                for (ContactEntity contactEntity : contactEntityList) {
                    ReceiverContactVO receiverItem = new ReceiverContactVO();
                    receiverItem.setEmail(contactEntity.getEmail());
                    receiverList.add(receiverItem);
                }

                customerSignNoticeItemDTO.setReceiver(receiverList);
            }
            */
        } catch (NumberFormatException e) {
            errorMsg.append("获取客户联系人异常");
        }

        //业务数据
        messageInfoDTO.setDataMap(BeanUtil.beanToMap(customerSignNoticeItemDTO));

        operationLogFacade.recordOperationLog(new OperationDetailDTO()
                .setBizCode(LogBizCodeEnum.SEND_CUSTOMER_NOTICE_MAIL.getBizCode())
                .setBizModule(ModuleTypeEnum.MSG.getDesc())
                .setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setMetaData(JSON.toJSONString(contractSignEntity)));
        //发送消息
        try {
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            errorMsg.append("消息服务异常:" + e.getMessage());
        }

        //记录traceLog
        if (StringUtil.isNotEmpty(errorMsg.toString())) {
            //记录日志
            operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail.Exception", JSON.toJSONString(errorMsg)));
        }
    }

    public CustomerSignNoticeItemDTO getCustomerEmailData(ContractSignEntity contractSignEntity) {

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();

        customerSignNoticeItemDTO.setSignId(contractSignEntity.getId())
                .setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name())
                .setFactoryCode(contractSignEntity.getDeliveryFactoryCode())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSalesType(contractSignEntity.getSalesType())
                .setCategoryCode(GoodsCategoryEnum.getDescByValue(contractSignEntity.getGoodsCategoryId()))
                .setContractCode(contractSignEntity.getContractCode())
                .setContractFilePath(contractSignEntity.getSignatureUrl())
                .setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId())
                .setCustomerName(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());
        //customerSignNoticeItemDTO.setSender(ReceiverTypeEnum.FACTORY_MAIL.getValue());

        return customerSignNoticeItemDTO;
    }


    public CustomerSignNoticeItemDTO getCustomerNoticeEmailData(ContractSignEntity contractSignEntity) {

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();

        customerSignNoticeItemDTO.setSignId(contractSignEntity.getId())
                .setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGN_NOTICE.name())
                .setFactoryCode(contractSignEntity.getDeliveryFactoryCode())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSalesType(contractSignEntity.getSalesType())
                .setCategoryCode(GoodsCategoryEnum.getDescByValue(contractSignEntity.getGoodsCategoryId()))
                .setContractCode(contractSignEntity.getContractCode())
                .setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId())
                .setCustomerName(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue() ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());

        String filePath = "";

        //获取签章文件
        List<FileInfoEntity> fileInfoEntityList = fileBusinessFacade.getFileInfoByBizIdAndType(contractSignEntity.getId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode(), null);
        if (CommonListUtil.notNullOrEmpty(fileInfoEntityList)) {
            filePath = fileInfoEntityList.get(0).getFileUrl();
            customerSignNoticeItemDTO.setContractFilePath(filePath);
        }

        //customerSignNoticeItemDTO.setSender(ReceiverTypeEnum.FACTORY_MAIL.getValue());

        return customerSignNoticeItemDTO;
    }

    /**
     * 校验易企签是否实名
     *
     * @param contractSignEntity
     * @return
     */
    private boolean customerYqqAuth(ContractSignEntity contractSignEntity) {
        //校验客户是否是实名认证
        if (ContractSalesTypeEnum.SALES.getValue() == contractSignEntity.getSalesType()) {

            CustomerDTO customerDTO = customerFacade.getCustomerById(contractSignEntity.getCustomerId());
            //判断客户是否使用易企签
            return customerDTO.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue()) && customerDTO.getIsColumbus().equals(GeneralEnum.YES.getValue());
        } else {
            CustomerDTO customerDTO = customerFacade.getCustomerById(Integer.valueOf(contractSignEntity.getSupplierId()));
            //判断客户是否使用易企签
            return customerDTO.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue()) && customerDTO.getIsColumbus().equals(GeneralEnum.YES.getValue());
        }
    }

    private boolean queryCustomerYqqAuth(Integer customerId, String phone, String uuid) {
        CheckRequestDTO checkRequestDTO = new CheckRequestDTO().setCustomerId(customerId).setPhone(phone).setCustomTag(uuid);
        try {
            return signatureFacade.hasAuthentication(checkRequestDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 校验协议
     *
     * @param contractSignId 协议ID
     * @return 协议信息
     */
    private ContractSignEntity checkContractSign(Integer contractSignId) {
        ContractSignEntity contractSignEntity = contractSignDao.getById(contractSignId);
        // 是否存在
        if (null == contractSignEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }
        return contractSignEntity;
    }

    private ContractSignEntity checkContractSign1(Integer contractSignId) {
        return contractSignDao.getById(contractSignId);
    }

    public boolean updateStatus(ContractSignEntity contractSignEntity, int status, String remark) {
        // 是否存在
        if (null == contractSignEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }

        return contractSignDao.updateById(contractSignEntity.setStatus(status).setRejectReason(remark));
    }

    /**
     * 协议操作日志记录
     *
     * @param contractSignEntity 合同信息
     * @param bizCodeEnum        操作枚举
     * @param data               数据
     * @param systemEnum         操作系统
     */
    private void addContractSignOperationLog(ContractSignEntity contractSignEntity, LogBizCodeEnum bizCodeEnum, String data, Integer systemEnum) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractSignEntity.getId())
                .setReferBizCode(contractSignEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setData(data)
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());

        //操作来源
        if (systemEnum != null && SystemEnum.MAGELLAN.getValue() == systemEnum) {
            //用户操作日志
            operationDetailDTO.setLogLevel(OperationSourceEnum.EMPLOYEE.getValue()).setSource(OperationSourceEnum.EMPLOYEE.getValue()).setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));
        } else if (systemEnum != null && SystemEnum.COLUMBUS.getValue() == systemEnum) {
            //客户操作记录,获取获取客户id
            Integer customerId = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCompanyId();
            operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue()).setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue()).setOperatorId(customerId);
        } else {
            //系统自动生成
            operationDetailDTO.setLogLevel(OperationSourceEnum.SYSTEM.getValue()).setSource(OperationSourceEnum.SYSTEM.getValue()).setOperatorId(1);
        }
        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmContract(ContractBaseDTO contractBaseDTO) {
        OperationActionEnum operationActionEnum;

        // 校验合同
        ContractSignEntity contractSignEntity = checkContractSign(contractBaseDTO.getContractSignId());
        if (ContractSignStatusEnum.WAIT_CONFIRM.getValue() != contractSignEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_CHECKED);
        }
        //contractQueryService.canSign(contractSignEntity.getId().toString());

        // 通过合同查看正本内容判断进入执行中
        if (contractBaseDTO.getReviewStatus().equals(ContractReviewStatusEnum.PASS.getValue())) {

            // 判断合同是否需要正本
            Integer newStatus = OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue().equals(contractSignEntity.getNeedOriginalPaper()) ? ContractSignStatusEnum.PROCESSING.getValue() : ContractSignStatusEnum.PAPER.getValue();

            contractSignDao.updateById(contractSignEntity
                    .setStatus(newStatus)
                    .setRejectReason("")
                    .setUpdatedAt(new Date()));
            // 同步合同到linkage
            //bizCodeEnum = LogBizCodeEnum.CONFIRMED_SALES_CONTRACT_SIGN;
            operationActionEnum = OperationActionEnum.CONFIRM_CONTRACT_PASS;

//            tradeTicketFacade.complete(contractSignEntity.getContractId(), contractSignEntity.getTtId());
            ITradeTicketService tradeTicketService = ttHandler.getStrategy(contractSignEntity.getSalesType(), contractSignEntity.getTtType(), contractSignEntity.getGoodsCategoryId());
            tradeTicketService.complete(contractSignEntity.getContractId(), contractSignEntity.getTtId());

            //结构化定价修改申请单状态
            if (ContractTypeEnum.STRUCTURE.getValue() == contractSignEntity.getContractType()) {
                List<Integer> contractIdS = new ArrayList<>();
                List<PriceApplyEntity> priceApplyEntities = priceApplyFacade.queryPriceApplyByContractId(contractSignEntity.getContractId());
                for (PriceApplyEntity priceApplyEntity : priceApplyEntities) {
                    contractIdS.add(priceApplyEntity.getId());
                }
                priceApplyFacade.batchPending(contractIdS);
            }

        } else {
            contractBaseDTO.setReviewRemark(StringUtil.isEmpty(contractBaseDTO.getReviewRemark()) ? "未填写" : contractBaseDTO.getReviewRemark());
            // 驳回合同进入待回传 ,清除文件信息
            fileBusinessFacade.dropFileRelation(contractBaseDTO.getContractSignId(), FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(), contractBaseDTO.getReviewRemark());

            contractSignEntity.setRejectReason(contractBaseDTO.getReviewRemark())
                    .setConfirmStatus(SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType() ? 1 : 0)
                    .setStatus(ContractSignStatusEnum.WAIT_BACK.getValue())
                    .setUpdatedAt(new Date());
            // 记录驳回原因
            contractSignDao.updateById(contractSignEntity);

            //bizCodeEnum = LogBizCodeEnum.NOT_CONFIRMED_SALES_CONTRACT_SIGN;
            operationActionEnum = OperationActionEnum.CONFIRM_CONTRACT_REJECT;
        }

        // 记录操作日志
        /*Map<String, String> dataMap = new HashMap<>();
        dataMap.put("contractCode", contractEntity.getContractCode());
        String contractCode = JSONUtil.toJsonStr(dataMap);
        addContractOperationLog(contractEntity, bizCodeEnum, contractCode);*/

        recordSignOperationLogDetail(operationActionEnum, contractSignEntity, SystemEnum.MAGELLAN.getName());

        return true;
    }

    private Integer isNeedOriginalPaper(CustomerOriginalPaperEntity customerOriginalPaperEntity, ContractSignEntity contractSignEntity, ContractSignCreateDTO signCreateDTO) {
        //Integer customerId = ContractSalesTypeEnum.PURCHASE.getValue() == contractSignEntity.getSalesType() ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId();
        // 查询客户信息
        //CustomerEntity customerEntity = customerFacade.queryCustomerById(customerId);
        // 判断是否需要正本
        //Integer needOriginalPaper = null != customerEntity.getOriginalPaper() ? customerEntity.getOriginalPaper() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
        /*String ttCode = contractSignEntity.getTtCode();
        //查询原始TT
        if (ttCode.contains("-")) {
            ttCode = ttCode.substring(0, ttCode.indexOf("-"));
            //继承初始TT正本信息
            ContractSignEntity contractSign = contractSignDao.queryByTTCode(ttCode);

            if (null != contractSign) {
                *//*if (OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue().equals(contractSign.getNeedOriginalPaper())) {
                    //超期需要正本
                    if (null != signCreateDTO && null != signCreateDTO.getDeliveryEndTime() && getTime(contractEntity.getSignDate(), signCreateDTO.getDeliveryEndTime())) {
                        //合同签订日期比交货截至日期小于两个月需要正本
                        return OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue();
                    }
                } else if (OriginalPaperEnum.ORIGINAL_PAPER.getValue().equals(contractSign.getNeedOriginalPaper())) {
                    return contractSign.getNeedOriginalPaper();
                }*//*
                if (OriginalPaperEnum.ORIGINAL_PAPER.getValue().equals(contractSign.getNeedOriginalPaper())) {
                    return contractSign.getNeedOriginalPaper();
                }
            }
        }*/

        ContractSignEntity signEntity = contractSignDao.queryContractSignByContractCode(contractSignEntity.getContractCode());
        if (null != signEntity) {
            return signEntity.getNeedOriginalPaper();
        }


        //1,LDC需要正本 non-frame
        if (null != customerOriginalPaperEntity && LdcFrameEnum.NOT_FRAME.getValue().equals(customerOriginalPaperEntity.getLdcFrame())) {
            //是non-frame合同的时候ldc需要正本
            return OriginalPaperEnum.LDC_ORIGINAL_PAPER.getValue();
        }

        //客户需要正本
        if (null != customerOriginalPaperEntity && OriginalPaperEnum.ORIGINAL_PAPER.getValue().equals(customerOriginalPaperEntity.getOriginalPaper())) {
            return OriginalPaperEnum.ORIGINAL_PAPER.getValue();
        }

        //超期需要正本
        /*if (getTime(contractEntity.getSignDate(), contractEntity.getDeliveryEndTime())) {
            //合同签订日期比交货截至日期小于两个月需要正本
            return OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue();
        }*/
        //客户需要正本
        return OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
    }


    public Integer exceedExpectOriginalPaper(ContractEntity contractEntity) {


        //超限需要正本
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(contractEntity.getGoodsCategoryId())) {
            //豆粕合同数量>2000需要正本
            return BigDecimalUtil.isGreater(contractEntity.getContractNum(), new BigDecimal(2000)) ? OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
        }
        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(contractEntity.getGoodsCategoryId())) {
            //豆油合同数量>600需要正本
            return BigDecimalUtil.isGreater(contractEntity.getContractNum(), new BigDecimal(600)) ? OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
        }

        //客户需要正本
        return OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
    }


    /**
     * 判断是否大于两个月
     *
     * @param signDate
     * @param deliveryEndTime
     * @return
     */
    private boolean getTime(Date signDate, Date deliveryEndTime) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(signDate);
        calendar.add(Calendar.MONTH, 2);
        Date startDate = calendar.getTime();
        return startDate.before(deliveryEndTime);
    }


    /**
     * 新增合同的操作记录
     *
     * @param contractEntity 合同信息
     * @param bizCodeEnum    操作枚举
     * @param data           数据
     */
    public void addContractOperationLog(ContractSignEntity contractEntity, LogBizCodeEnum bizCodeEnum, String data) {

        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(contractEntity.getId())
                .setReferBizCode(contractEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setData(data)
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }


    @Async
    public void recordSignOperationLogDetail(OperationActionEnum operationActionEnum, ContractSignEntity signEntity, String opSystem) {
        OperationDetailDTO operationDetailDTO = buildTTOperationDetail(operationActionEnum, signEntity);
        operationDetailDTO.setTriggerSys(opSystem);
        if (operationActionEnum == OperationActionEnum.REVIEW_CONTRACT_REJECT || operationActionEnum == OperationActionEnum.CONFIRM_CONTRACT_REJECT) {
            String operationInfo = "原因：" + signEntity.getRejectReason();
            operationDetailDTO.setOperationInfo(signEntity.getRejectReason());
        }
        try {
            operationLogFacade.recordOperationLogDetail(operationDetailDTO);
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }
    }

    @Async
    public OperationDetailDTO buildTTOperationDetail(OperationActionEnum operationActionEnum, ContractSignEntity signEntity) {

        OperationSourceEnum logLevelEnum = OperationSourceEnum.getByName(operationActionEnum.getLogLevel());

        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(signEntity.getTradeType());
        return new OperationDetailDTO()
                .setBizCode(operationActionEnum.getCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setLogLevel(logLevelEnum.getValue())
                .setSource(logLevelEnum.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setOperationName(operationActionEnum.getAction())
                .setMetaData(null).setData(JSON.toJSONString(signEntity))
                .setCreatedAt(DateTimeUtil.now()).setReferBizId(signEntity.getId())
                .setReferBizCode(signEntity.getProtocolCode())
                .setTargetRecordId(signEntity.getContractId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name())
                .setTtCode(signEntity.getTtCode())
                .setTradeTypeName(contractTradeTypeEnum.getDesc())
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
    }


}
