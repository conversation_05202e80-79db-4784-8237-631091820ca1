package com.navigator.trade.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.TTPriceEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 定价单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface ITtPriceService {

    boolean saveTtPrice(TTPriceEntity ttPriceEntity);

    List<TTPriceEntity> getTtPrice(String contractCode);

    /**
     * 根据id获取实体
     *
     * @param id
     * @return
     */
    TTPriceEntity getById(Integer id);

    /**
     * 根据id更新实体
     *
     * @param ttPriceEntity
     * @return
     */
    int updateTtPrice(TTPriceEntity ttPriceEntity);

    /**
     * 获取所有定价单
     *
     * @param contractId
     * @return
     */
    List<TTPriceEntity> getConfirmPriceList(Integer contractId);

    /**
     * 查询非id的合同定价单
     *
     * @param contractId
     * @param id
     * @return
     */
    List<TTPriceEntity> getPriceByContractNotId(Integer contractId, Integer id);

    TTPriceEntity getTTPriceEntityByTTId(Integer ttId);

    /**
     * 获取合同定价单列表
     *
     * @param contractId
     * @return List<ConfirmPriceDTO>
     */
    List<ConfirmPriceDTO> getContractPricingList(Integer contractId);

    List<ConfirmPriceDTO> getTTPriceByContractId(Integer contractId);

    /**
     * 获取合同定价单信息
     *
     * @param contractId
     * @return ConfirmPriceDTO
     */
    ConfirmPriceDTO getConfirmPricedInfo(Integer contractId, Integer ttPriceId);

    /**
     * 根据id和price获取实体
     *
     * @param contractId
     * @return
     */
    TTPriceEntity getByContractIdAndPrice(Integer contractId, BigDecimal price);

    /**
     * 根据申请单id查询定价单
     *
     * @param priceApplyId
     * @return
     */
    List<TTPriceEntity> getTTPriceByApplyId(Integer priceApplyId);

    TTPriceEntity getTTPriceByAllocateId(Integer allocateId);

    /**
     * 根据sourceId查询定价单
     *
     * @param sourceId
     * @return
     */
    List<TTPriceEntity> getTTPriceBySourceId(Integer sourceId);

    /**
     * 修改定价单
     *
     * @param ttPriceEntity
     * @return
     */
    Boolean updateTTPriceById(TTPriceEntity ttPriceEntity);
}
