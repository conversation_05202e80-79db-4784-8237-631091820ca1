package com.navigator.trade.app.contract.logic.service.handler.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.sequence.SequenceUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.entity.WarrantEntity;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.trade.app.adpater.remote.TradeDomainRemoteService;
import com.navigator.trade.app.contract.domain.service.ContractDomainService;
import com.navigator.trade.app.contract.domain.service.ContractQueryDomainService;
import com.navigator.trade.app.contract.logic.service.handler.CommonLogicService;
import com.navigator.trade.app.contract.logic.service.handler.Soybean2WriteOffLogicService;
import com.navigator.trade.app.sign.logic.service.ContractSignQueryLogicService;
import com.navigator.trade.app.tt.logic.service.TTQueryLogicService;
import com.navigator.trade.facade.impl.DeliveryTypeFacadeImpl;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffItemDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffOMDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractActionEnum;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.ContractWriteOffStatusEnum;
import com.navigator.trade.pojo.vo.ContractUnitPriceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓单注销的Logic 逻辑处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class Soybean2WriteOffLogicServiceImpl implements Soybean2WriteOffLogicService {

    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private WarrantFacade warrantFacade;
    @Autowired
    private DeliveryTypeFacadeImpl deliveryTypeFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerInvoiceFacade customerInvoiceFacade;
    @Resource
    private SequenceUtil sequenceUtil;
    /**
     * 合同域
     */
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContractQueryDomainService contractQueryDomainService;
    @Autowired
    private TTQueryLogicService ttQueryLogicService;
    @Autowired
    private ContractSignQueryLogicService contractSignQueryLogicService;
    @Autowired
    private TradeDomainRemoteService tradeDomainRemoteService;
    @Autowired
    private CommonLogicService commonLogicService;


    /**
     * 前提：仅合同类型为一口价
     * 且合同状态为生效中
     * 且注销状态为未注销注销中
     * 合同可注销回购量合同总量已注销量已回购量注销撤回量
     * 同时计算下属于哪个注销场景
     *
     * @param contractEntity        合同实体
     * @param contractWriteOffOMDTO 解约定赔dto
     */
    @Override
    public void writeOffContractCheck(ContractEntity contractEntity, ContractWriteOffOMDTO contractWriteOffOMDTO) {
        // 是否存在
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        // 合同状态处于生效中
        if (!contractEntity.getStatus().equals(ContractStatusEnum.EFFECTIVE.getValue())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EFFECTIVE);
        }
        // 合同类型为一口价
        if (!(ContractTypeEnum.YI_KOU_JIA.getValue() == contractEntity.getContractType())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TYPE_NOT_SUPPORT_WRITEOFF);
        }
        // 销状态为未注销注销中
        if (ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue() == contractEntity.getWriteOffStatus()) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_NOT_SUPPORT_WRITE_OFF_STATUS);
        }
        // 合同可注销 = 合同总量-已注销量-已回购量
        BigDecimal canWriteOffNum = contractEntity.getContractNum().subtract(contractEntity.getWarrantCancelCount())
                .subtract(contractEntity.getTotalBuyBackNum());
        if (contractWriteOffOMDTO.getWriteOffNum().compareTo(canWriteOffNum) > 0) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_NUM_LESS_WRITE_NUM);
        }

        // 采购注销需要计算最大注销量
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            // 获取仓单持有量
            Result result = warrantFacade.queryWarrantByID(contractEntity.getWarrantId());
            WarrantEntity warrantEntity = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), WarrantEntity.class);
            BigDecimal minWriteOffNum = warrantEntity.getHoldCount();
            if (contractWriteOffOMDTO.getWriteOffNum().compareTo(minWriteOffNum) > 0) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_WRITE_OFF_NUM_LESS_WARRANT_NUM);
            }

        }

        // 判断注销走哪个场景【采购就一个场景】
        if (contractEntity.getCustomerId().equals(contractWriteOffOMDTO.getCustomerId())
                || contractEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            contractWriteOffOMDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_OM_A.getActionValue());
        } else {
            contractWriteOffOMDTO.setWriteOffAction(ContractActionEnum.WRITE_OFF_OM_B.getActionValue());
        }

    }


    /**
     * 修改补充补充基本信息-创建子合同
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    @Override
    public void buildBaseInfo(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity, String type) {
        ContractEntity parentContractEntity = contractWriteOffOMDTO.getParentContractEntity();
        // 复制父合同信息
        BeanUtils.copyProperties(parentContractEntity, contractEntity);
        // 复制填写的信息
        BeanUtils.copyProperties(contractWriteOffOMDTO, contractEntity);
        // 复制豆油和豆粕的一些基本信息
        ContractWriteOffItemDTO contractWriteOffItemDTO = new ContractWriteOffItemDTO();
        if ("SBM".equals(type)) {
            // 修改货品 配置域获取数据信息
            contractWriteOffItemDTO = contractWriteOffOMDTO.getSbmItem();
        } else {
            contractWriteOffItemDTO = contractWriteOffOMDTO.getSboItem();
        }
        BeanUtils.copyProperties(contractWriteOffItemDTO, contractEntity);
        // 处理基本数据
        String sonContractCode = "";
        // 子编号|【新合同编号】
        sonContractCode = contractQueryDomainService.genSonContractCode(parentContractEntity.getContractCode(), parentContractEntity.getSalesType());

        contractEntity.setId(null)
                .setUuid(IdUtil.simpleUUID())
                .setContractCode(sonContractCode)
                .setLinkinageCode(sonContractCode)
                .setRepeatContractCode(sonContractCode)
                .setParentId(parentContractEntity.getId())
                .setCustomerId(parentContractEntity.getCustomerId())
                .setRootId(parentContractEntity.getParentId() == 0 ? parentContractEntity.getId() : parentContractEntity.getParentId())
                .setMemo(contractWriteOffOMDTO.getMemo())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setTotalBuyBackNum(BigDecimal.ZERO)
                .setCreateSource(SystemEnum.MAGELLAN.getValue())
                .setApplyDeliveryNum(BigDecimal.ZERO)
                .setCreateBatch(null);

        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 start
        // Inherit group_id from parent contract for soybean2 write-off operations
        if (parentContractEntity.getGroupId() != null) {
            contractEntity.setGroupId(parentContractEntity.getGroupId());
        }
        // 1003270 batch TT creation group_id field changed by Jason Shi at 2025-06-17 end
    }

    /**
     * 处理变更的业务信息
     * 修改货品，交割库（库点），交货方式，目的港，注销说明备注，赊销账期，交货周期，短装溢，所属商务，履约保证金
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    @Override
    public void buildBizInfo(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity, String type) {

        ContractEntity parentContractEntity = contractWriteOffOMDTO.getParentContractEntity();
        // 豆二注销只有二 WRITE_OFF_A 和 WRITE_OFF_B 提货合同和贸易合同都是A默认的提货
        // 处理豆油和豆粕处理差异
        Integer goodsId;
        PriceDetailBO priceDetailBO = new PriceDetailBO();
        Integer shipWarehouseId;
        String shipWarehouseName;
        Integer deliveryType;
        BigDecimal unitPrice = BigDecimal.ZERO;
        BigDecimal writeOffNum = BigDecimal.ZERO;
        String weightCheck;
        String destination; // 目的港
        if ("SBM".equals(type)) {
            // 修改货品 配置域获取数据信息
            goodsId = contractWriteOffOMDTO.getSbmItem().getGoodsId();
            priceDetailBO = contractWriteOffOMDTO.getSbmItem().getPriceDetailDTO();
            shipWarehouseId = contractWriteOffOMDTO.getSbmItem().getShipWarehouseId();
            shipWarehouseName = contractWriteOffOMDTO.getSbmItem().getShipWarehouseName();
            deliveryType = contractWriteOffOMDTO.getSbmItem().getDeliveryType();
            unitPrice = contractWriteOffOMDTO.getSbmItem().getUnitPrice();
            writeOffNum = contractWriteOffOMDTO.getSbmItem().getWriteOffNum();
            weightCheck = contractWriteOffOMDTO.getSbmItem().getWeightCheck();
            destination = contractWriteOffOMDTO.getSbmItem().getDestination();
        } else {
            goodsId = contractWriteOffOMDTO.getSboItem().getGoodsId();
            priceDetailBO = contractWriteOffOMDTO.getSboItem().getPriceDetailDTO();
            shipWarehouseId = contractWriteOffOMDTO.getSboItem().getShipWarehouseId();
            shipWarehouseName = contractWriteOffOMDTO.getSboItem().getShipWarehouseName();
            deliveryType = contractWriteOffOMDTO.getSboItem().getDeliveryType();
            unitPrice = contractWriteOffOMDTO.getSboItem().getUnitPrice();
            writeOffNum = contractWriteOffOMDTO.getSboItem().getWriteOffNum();
            weightCheck = contractWriteOffOMDTO.getSboItem().getWeightCheck();
            destination = contractWriteOffOMDTO.getSboItem().getDestination();
        }

        // 处理价格
        SkuEntity skuEntity = skuFacade.getSkuById(goodsId);
        List<String> nickNameList = StringUtils.isNotBlank(skuEntity.getNickName()) ? StringUtil.split(skuEntity.getNickName(), "\\$\\$") : new ArrayList<>();
        String nickName = CollectionUtils.isEmpty(nickNameList) ? skuEntity.getFullName() : nickNameList.get(0);
        contractEntity.setGoodsCategoryId(skuEntity.getCategory2())
                .setGoodsPackageId(skuEntity.getPackageId())
                .setGoodsSpecId(skuEntity.getSpecId())
                .setGoodsId(skuEntity.getId())
                .setGoodsName(skuEntity.getFullName())
                .setCommodityName(nickName)
                .setCategory1(skuEntity.getCategory1())
                .setCategory2(skuEntity.getCategory2())
                .setCategory3(skuEntity.getCategory3());
        //税率
        if (skuEntity.getTaxRate() != null) {
            BigDecimal taxRate = skuEntity.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            contractEntity.setTaxRate(taxRate);
        }
        // 价格处理
        ContractUnitPriceVO contractUnitPriceVO = commonLogicService.calcContractUnitPrice(priceDetailBO, contractEntity.getTaxRate());

        // 交货方式
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(deliveryType);
        contractEntity.setDeliveryTypeValue(deliveryTypeEntity != null ? deliveryTypeEntity.getName() : "");

        // 处理下交割库数据 双重处理
        if (ObjectUtil.isNotEmpty(shipWarehouseId)) {
            WarehouseEntity warehouseEntity = tradeDomainRemoteService.getFactoryWarehouse(shipWarehouseId);
            contractEntity.setShipWarehouseValue(ObjectUtil.isNotEmpty(warehouseEntity)?warehouseEntity.getName():shipWarehouseName);
        }

        //查询发票信息
        Integer customerId = contractEntity.getCustomerId();
        if (ContractSalesTypeEnum.PURCHASE.getValue() == contractEntity.getSalesType()) {
            customerId = contractEntity.getSupplierId();
        }
        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(customerId)
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()));
        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO);
        if (!customerInvoiceEntities.isEmpty()) {
            int invoiceId = customerInvoiceEntities.isEmpty() ? 0 : customerInvoiceEntities.get(0).getInvoiceId();
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(invoiceId);
            if (null != systemRuleItemEntity) {
                //发票类型
                contractEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
                contractEntity.setInvoiceTypeValue(InvoiceTypeEnum.getDescByValue(Integer.parseInt(systemRuleItemEntity.getRuleKey())));

            }
        }
        if (ObjectUtil.isNotEmpty(contractEntity.getWeightCheck())) {
            contractEntity.setWeightCheckValue(tradeDomainRemoteService.systemRuleConvertValue(contractEntity.getWeightCheck()));
        }

        if (ObjectUtil.isNotEmpty(contractEntity.getDestination())) {
            contractEntity.setDestinationValue(tradeDomainRemoteService.systemRuleConvertValue(contractEntity.getDestination()));
        }

        // 注销提货时间变动,前端计算了
        if (ObjectUtil.isNotEmpty(contractWriteOffOMDTO.getWriteOffDeliveryStartTime())) {
            contractEntity.setDeliveryStartTime(contractWriteOffOMDTO.getWriteOffDeliveryStartTime());
            contractEntity.setDeliveryEndTime(contractWriteOffOMDTO.getWriteOffDeliveryEndTime());
        }

        // 重新汇总数据【注销数量前端接口计算好】
        contractEntity.setOrderNum(writeOffNum)
                .setContractNum(writeOffNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, writeOffNum, unitPrice))
                .setTotalDeliveryNum(BigDecimal.ZERO)
                .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, writeOffNum, unitPrice))
                .setWarrantCancelCount(writeOffNum)
                .setTotalPriceNum(writeOffNum)
                .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                .setTotalModifyNum(BigDecimal.ZERO)
                .setTotalTransferNum(BigDecimal.ZERO)
                .setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue())
                .setUnitPrice(contractUnitPriceVO.getUnitPrice())
                .setFobUnitPrice(contractUnitPriceVO.getFobUnitPrice())
                .setCifUnitPrice(contractUnitPriceVO.getCifUnitPrice())
                .setExtraPrice(priceDetailBO.getExtraPrice())
                .setBaseDiffPrice(priceDetailBO.getExtraPrice());
    }


    /**
     * 创建合同
     *
     * @param contractWriteOffOMDTO
     * @param contractEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createContract(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity) {
        // 保存合同
        try {
            contractDomainService.saveContract(contractEntity);
        } catch (Exception e) {
            log.error("contractCode: {} save fail cause by: {}", contractEntity.getContractCode(), e.getMessage());
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
        // 记录子合同信息
        contractWriteOffOMDTO.setSonContractId(contractEntity.getId())
                .setSonContractType(contractEntity.getContractType());
    }

    /**
     * 处理仓单销售合同信息
     *
     * @param contractWriteOffOMDTO 注销信息
     * @param contractEntity        仓单合同
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void operateFatherContract(ContractWriteOffOMDTO contractWriteOffOMDTO, ContractEntity contractEntity) {
        // 合同数量
        BigDecimal contractNum = contractEntity.getContractNum();
        // 注销量
        BigDecimal writeOffNum = ObjectUtil.isNotEmpty(contractEntity.getWarrantCancelCount())
                ? contractEntity.getWarrantCancelCount() : BigDecimal.ZERO;
        writeOffNum = writeOffNum.add(contractWriteOffOMDTO.getWriteOffNum());
        // 重新汇总数据
        contractEntity
                .setContractNum(contractNum)
                .setOrderNum(contractNum)
                .setWarrantCancelCount(writeOffNum)
                .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                .setTotalPriceNum(contractNum)
                .setOrderAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractNum, contractEntity.getUnitPrice()))
                .setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        // 注销状态【计算下回购量】
        BigDecimal buyBackNum = ObjectUtil.isNotEmpty(contractEntity.getTotalBuyBackNum()) ? contractEntity.getTotalBuyBackNum() : BigDecimal.ZERO;
        if (contractEntity.getContractNum().compareTo(contractEntity.getWarrantCancelCount().add(buyBackNum)) == 0) {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.COMPLATE_WRITEOFF.getValue());
        } else {
            contractEntity.setWriteOffStatus(ContractWriteOffStatusEnum.WRITEOFFING.getValue());
        }
        // 记录合同日志
        contractDomainService.updateContractById(contractEntity);
    }
}
