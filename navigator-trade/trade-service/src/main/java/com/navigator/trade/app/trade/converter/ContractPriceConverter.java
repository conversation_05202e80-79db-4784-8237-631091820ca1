package com.navigator.trade.app.trade.converter;

import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.future.PriceDetailDTO;

public class ContractPriceConverter {

    public static PriceDetailBO convertPriceDetailDTO(PriceDetailDTO priceDetailDTO) {
        PriceDetailBO rtn = BeanConvertUtils.map(PriceDetailBO.class, priceDetailDTO);
        return rtn;
    }
}
