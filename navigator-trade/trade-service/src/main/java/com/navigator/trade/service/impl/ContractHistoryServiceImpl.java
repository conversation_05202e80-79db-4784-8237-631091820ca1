package com.navigator.trade.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.trade.dao.ContractHistoryDao;
import com.navigator.trade.dao.ContractPriceDao;
import com.navigator.trade.dao.ContractStructureDao;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractHistoryEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.service.IContractHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 合同历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class ContractHistoryServiceImpl implements IContractHistoryService {

    @Autowired
    ContractHistoryDao contractHistoryDao;
    @Autowired
    ContractPriceDao contractPriceDao;
    @Autowired
    ContractStructureDao contractStructureDao;

    @Override
    public void backupContract(ContractEntity contractEntity, String backTradeType, String referCode) {
        ContractHistoryEntity contractHistoryEntity = new ContractHistoryEntity();
        contractHistoryEntity = BeanConvertUtils.convert(ContractHistoryEntity.class, contractEntity);

        Integer contractId = contractEntity.getId();

        ContractStructureEntity contractStructureEntity = null;
        ContractPriceEntity contractPriceEntity = null;

        if (contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {
            contractStructureEntity = contractStructureDao.getContractStructure(contractId);
        } else {
            contractPriceEntity = contractPriceDao.getContractPriceEntityContractId(contractId);
        }

        contractHistoryEntity.setId(null)
                .setContractId(contractId)
                .setBackTradeType(backTradeType)
                .setBackBizCode(referCode)
                .setBackTime(new Date())
                .setPriceInfo(JSON.toJSONString(contractPriceEntity))
                .setStructureInfo(JSON.toJSONString(contractStructureEntity));

        contractHistoryDao.save(contractHistoryEntity);
    }

    @Override
    public ContractEntity getContractEntity(Integer contractId, Integer mainVersion) {
        ContractEntity contractEntity = null;
        ContractHistoryEntity contractHistoryEntity = contractHistoryDao.getContractHistoryEntity(contractId, mainVersion);
        if (null != contractHistoryEntity) {
            contractEntity = BeanConvertUtils.convert(ContractEntity.class, contractHistoryEntity);
            contractEntity.setId(contractHistoryEntity.getContractId());
        }

        return contractEntity;
    }

    @Override
    public ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        return contractHistoryDao.getContractHistoryEntity(contractId, mainVersion);
    }
}
