package com.navigator.trade.service.contract;

import com.navigator.pigeon.pojo.entity.LkgContractInfoEntity;
import com.navigator.trade.pojo.entity.ContractEntity;

import java.util.List;

/**
 * 合同值对象基础服务
 * 包括合同、合同历史、合同价格、合同结构化定价详情
 */
public interface IContractValueObjectService {

    boolean updateContractById(ContractEntity contractEntity);

    boolean updateContractById(ContractEntity contractEntity, String backTradeType, String referCode);

    boolean updateContractById(ContractEntity contractEntity, Integer ttId);

    ContractEntity getContractById(Integer id);

    ContractEntity rollBackContract(Integer contractId);

    ContractEntity getLocalLkgContractByContractId(Integer contractId);

    List<LkgContractInfoEntity> getLocalLkgContractByContractCodeList(List<String> contractCodeList);
}
