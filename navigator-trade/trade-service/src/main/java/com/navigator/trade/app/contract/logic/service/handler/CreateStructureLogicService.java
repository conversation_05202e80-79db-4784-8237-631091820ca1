package com.navigator.trade.app.contract.logic.service.handler;

import com.navigator.trade.pojo.dto.contract.ContractCreateDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;

/**
 * 结构化合同创建子类Logic 逻辑处理
 *
 * <AUTHOR>
 */
public interface CreateStructureLogicService {

    /**
     * 创建结构化合同
     * @param structurePriceTTDTO
     * @return
     */
    ContractEntity createStructureContract(SalesStructurePriceTTDTO structurePriceTTDTO);
}
