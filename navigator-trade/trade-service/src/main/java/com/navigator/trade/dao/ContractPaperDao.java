package com.navigator.trade.dao;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.mapper.ContractPaperMapper;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.TradeTicketEntity;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/3 18:16
 */
@Dao
public class ContractPaperDao extends BaseDaoImpl<ContractPaperMapper, ContractPaperEntity> {

    public ContractPaperEntity getContractPaper(Integer contractSignId) {

        List<ContractPaperEntity> contractPaperEntities = this.list(new LambdaQueryWrapper<ContractPaperEntity>()
                .eq(ContractPaperEntity::getContractSignId, contractSignId)
                .eq(ContractPaperEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return contractPaperEntities.isEmpty() ? null : contractPaperEntities.get(0);
    }


    public List<ContractPaperEntity> sendContractSignOriginalPaper() {

        return this.baseMapper.selectList(Wrappers.<ContractPaperEntity>lambdaQuery()
                        .ne(ContractPaperEntity::getLdcDeliveryCompany, "")
                        .ne(ContractPaperEntity::getLdcDeliverySn, "")
                        .ne(ContractPaperEntity::getLdcPaperNum, "")
                        .ne(ContractPaperEntity::getMailGoTime, "")
                        .ge(ContractPaperEntity::getCreatedAt, DateUtil.offsetDay(new Date(), -1))
                        .and(wrapper -> wrapper
                                .eq(ContractPaperEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                                .or()
                                .eq(ContractPaperEntity::getCustomerDeliveryCompany, "")
                                .or()
                                .eq(ContractPaperEntity::getCustomerDeliverySn, "")
                                .or()
                                .eq(ContractPaperEntity::getCustomerPaperNum, "")
                                .or()
                                .eq(ContractPaperEntity::getMailReturnTime, "")
                        )
                /*.groupBy(ContractPaperEntity::getCustomerId)
                .select(ContractPaperEntity::getCustomerId)*/
        );

    }
}
