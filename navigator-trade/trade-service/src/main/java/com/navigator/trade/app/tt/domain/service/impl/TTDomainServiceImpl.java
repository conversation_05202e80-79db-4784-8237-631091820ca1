package com.navigator.trade.app.tt.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.service.TTDomainService;
import com.navigator.trade.app.tt.domain.service.processor.AbstractTTDomainProcessor;
import com.navigator.trade.dao.*;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.enums.TTDomainProcessTypeEnum;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.service.IContractPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TT领域服务
 * @Date 2024/7/14 16:17
 * @Version 1.0
 */
@Service
public class TTDomainServiceImpl implements TTDomainService {

    @Autowired
    TradeTicketDao tradeTicketDao;

    @Autowired
    TtAddDao ttAddDao;

    @Autowired
    TtModifyDao ttModifyDao;

    @Autowired
    TtTranferDao ttTranferDao;

    @Autowired
    TtPriceDao ttPriceDao;

    @Autowired
    TtStructureDao ttStructureDao;


    @Autowired
    ContractPriceDao contractPriceDao;

    @Autowired
    IContractPriceService contractPriceService;

    @Autowired
    CustomerFacade customerFacade;// 待优化

    @Autowired
    @Lazy
    private Map<String, AbstractTTDomainProcessor> ttDomainProcessorMap;

    private AbstractTTDomainProcessor getTTDomainProcessor(TTTypeEnum ttTypeEnum) {
        AbstractTTDomainProcessor ttDomainProcessor;

        TTDomainProcessTypeEnum processKey = TTDomainProcessTypeEnum.getByTTType(ttTypeEnum);

        ttDomainProcessor = ttDomainProcessorMap.get(processKey.name());

        return ttDomainProcessor;
    }

    //@Override
    @Transactional(rollbackFor = Exception.class)
    public TradeTicketDO createTradeTicketDO2(TradeTicketDO tradeTicketDO) {

        // 匹配处理器
        AbstractTTDomainProcessor ttDomainProcessor = getTTDomainProcessor(tradeTicketDO.getTTType());

        //处理器处理
        tradeTicketDO = ttDomainProcessor.createTradeTicket(tradeTicketDO);

        return tradeTicketDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeTicketDO createTradeTicketDO(TradeTicketDO tradeTicketDO) {
        //获取主表信息
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        //新增或者更新
        tradeTicketDao.saveOrUpdate(tradeTicketEntity);
        Integer ttId = tradeTicketEntity.getId();
        //获取子表信息
        TTSubEntity ttSubEntity = tradeTicketDO.getTtSubEntity();
        ttSubEntity.setTtId(ttId);
        //DO设置为最新的
        tradeTicketDO.setTradeTicketEntity(tradeTicketDao.getById(ttId));
        switch (ttSubEntity.getClass().getSimpleName()) {
            case "TTAddEntity":
                TTAddEntity addEntity = (TTAddEntity) ttSubEntity;
                addEntity.setTtId(ttId);
                TTAddEntity oldAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
                if (Objects.nonNull(oldAddEntity)) {
                    addEntity.setId(oldAddEntity.getId());
                }
                ttAddDao.saveOrUpdate(addEntity);
                tradeTicketDO.setTtSubEntity(ttAddDao.getById(addEntity.getId()));
                break;
            case "TTModifyEntity":
                TTModifyEntity modifyEntity = (TTModifyEntity) ttSubEntity;
                modifyEntity.setTtId(ttId);
                TTModifyEntity oldModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
                if (Objects.nonNull(oldModifyEntity)) {
                    modifyEntity.setId(oldModifyEntity.getId());
                }
                ttModifyDao.saveOrUpdate(modifyEntity);
                tradeTicketDO.setTtSubEntity(ttModifyDao.getById(modifyEntity.getId()));
                break;
            case "TTPriceEntity":
                TTPriceEntity ttPriceEntity = (TTPriceEntity) ttSubEntity;
                ttPriceEntity.setTtId(ttId);
                ttPriceDao.save(ttPriceEntity);
                tradeTicketDO.setTtSubEntity(ttPriceDao.getById(ttPriceEntity.getId()));
                break;
            case "TTTranferEntity":
                TTTranferEntity ttTranferEntity = (TTTranferEntity) ttSubEntity;
                ttTranferEntity.setTtId(ttId);
                ttTranferDao.save(ttTranferEntity);
                tradeTicketDO.setTtSubEntity(ttTranferDao.getById(ttTranferEntity.getId()));
                break;
            case "TTStructureEntity":
                TTStructureEntity ttStructureEntity = (TTStructureEntity) ttSubEntity;
                ttStructureEntity.setTtId(ttId);
                ttStructureDao.save(ttStructureEntity);
                tradeTicketDO.setTtSubEntity(ttStructureDao.getById(ttStructureEntity.getId()));
                break;
            default:
                throw new IllegalArgumentException("Unsupported TTSubEntity type: " + ttSubEntity.getClass().getName());
        }

        // BUGFIX：case-1003073 PC202503270959233455采购合同无法保存 Author: Mr 2025-03-31 start
        // 检查销售类型是否为空
        if  (tradeTicketEntity.getSalesType() == null ) {
            return tradeTicketDO;
        }
        // BUGFIX：case-1003073 PC202503270959233455采购合同无法保存 Author: Mr 2025-03-31 End

        // 回购原合同不生成价格信息
        if (ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() && TTTypeEnum.BUYBACK.getType().equals(tradeTicketEntity.getType())) {
            return tradeTicketDO;
        }

        ContractPriceEntity priceEntity = tradeTicketDO.getContractPriceEntity();
        if (Objects.nonNull(priceEntity)) {
            ContractPriceEntity oldPriceEntity = contractPriceService.getContractPriceEntityByTTId(ttId);
            if (Objects.nonNull(oldPriceEntity)) {
                priceEntity.setId(oldPriceEntity.getId());
            }
            priceEntity.setTtId(ttId)
                    .setTtCode(tradeTicketEntity.getCode())
                    .setContractId(tradeTicketEntity.getContractId())
                    .setContractCode(tradeTicketEntity.getContractCode());
            contractPriceDao.saveOrUpdate(priceEntity);
        }

        return tradeTicketDO;
    }

    /**
     * 子表数据保存
     *
     * @param tradeTicketDO
     * @param ttId
     */
    private void saveOrUpdateSubEntity(TradeTicketDO tradeTicketDO, Integer ttId) {
        TTTypeEnum ttType = tradeTicketDO.getTTType();
        switch (ttType) {
            case NEW:
            case ALLOCATE:
            case ASSIGN:
                TTAddEntity addEntity = (TTAddEntity) tradeTicketDO.getTtSubEntity();
                addEntity.setTtId(ttId);
                TTAddEntity oldAddEntity = ttAddDao.getTTAddEntityByTTId(ttId);
                if (Objects.nonNull(oldAddEntity)) {
                    addEntity.setId(oldAddEntity.getId());
                }
                ttAddDao.saveOrUpdate(addEntity);
                break;
            case REVISE:
            case SPLIT:
                TTModifyEntity modifyEntity = (TTModifyEntity) tradeTicketDO.getTtSubEntity();
                modifyEntity.setTtId(ttId);
                TTModifyEntity oldModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
                if (Objects.nonNull(oldModifyEntity)) {
                    modifyEntity.setId(oldModifyEntity.getId());
                }
                ttModifyDao.saveOrUpdate(modifyEntity);
                break;
            case TRANSFER:
            case REVERSE_PRICE:
                TTTranferEntity ttTranferEntity = (TTTranferEntity) tradeTicketDO.getTtSubEntity();
                ttTranferEntity.setTtId(ttId);
                TTTranferEntity oldTtTranferEntity = ttTranferDao.getTTTransferEntityByTTId(ttId);
                if (Objects.nonNull(oldTtTranferEntity)) {
                    ttTranferEntity.setId(oldTtTranferEntity.getId());
                }
                ttTranferDao.saveOrUpdate(ttTranferEntity);
                break;
            case PRICE:
            case FIXED:
                TTPriceEntity ttPriceEntity = (TTPriceEntity) tradeTicketDO.getTtSubEntity();
                ttPriceEntity.setTtId(ttId);
                TTPriceEntity oldTTPriceEntity = ttPriceDao.getTTPriceEntityByTTId(ttId);
                if (Objects.nonNull(oldTTPriceEntity)) {
                    ttPriceEntity.setId(oldTTPriceEntity.getId());
                }
                ttPriceDao.saveOrUpdate(ttPriceEntity);
                break;
            case STRUCTURE_PRICE:
                TTStructureEntity structureEntity = (TTStructureEntity) tradeTicketDO.getTtSubEntity();
                structureEntity.setTtId(ttId);
                TTStructureEntity oldStructureEntity = ttStructureDao.getByTTId(ttId);
                if (Objects.nonNull(oldStructureEntity)) {
                    structureEntity.setId(oldStructureEntity.getId());
                }
                ttStructureDao.saveOrUpdate(structureEntity);
                break;
            default:
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateContractId(TradeTicketDO tradeTicketDO) {
        if (Objects.isNull(tradeTicketDO) || Objects.isNull(tradeTicketDO.getTradeTicketEntity())) {
            throw new BusinessException("tradeTicketDO不能为空");
        }
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        Integer ttId = tradeTicketEntity.getId();
        Integer contractId = tradeTicketEntity.getContractId();
        // 更新tradeTicket的合同关系
        tradeTicketDao.updateContractId(ttId, contractId);
        // 更新TT与协议关系
        if (ObjectUtil.isNotEmpty(tradeTicketEntity.getSignId())) {
            tradeTicketDao.updateSignInfo(ttId, new ContractSignEntity()
                    .setId(tradeTicketEntity.getSignId())
                    .setProtocolCode(tradeTicketEntity.getProtocolCode()));
        }
        // 更新ttAdd的合同关系
        ttAddDao.updateContractId(ttId, contractId);
        // 更新TT-price的合同关系
        contractPriceService.updateContractId(ttId, contractId);
        return true;
    }

    @Override
    public Boolean updateTradeTicketDO(TradeTicketDO tradeTicketDO) {
        return null;
    }

    @Override
    public Boolean updateTradeTicketEntity(TradeTicketEntity tradeTicketEntity) {
        return null;
    }

    @Override
    public Boolean updateTTBaseEntity(TTSubEntity ttSubEntity) {
        return null;
    }

    @Override
    public Boolean saveTtPrice(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.save(ttPriceEntity);
    }

    @Override
    public Boolean updateTtPrice(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.saveOrUpdate(ttPriceEntity);
    }

    @Override
    public void dealGroupTT(Integer ttId, Integer ttType, Integer addedSignatureType) {
        if (addedSignatureType == -1 &&
                (ttType.equals(TTTypeEnum.SPLIT.getType()) ||
                        ttType.equals(TTTypeEnum.BUYBACK.getType()) ||
                        ttType.equals(TTTypeEnum.REVERSE_PRICE.getType()) ||
                        ttType.equals(TTTypeEnum.TRANSFER.getType()) ||
                        ttType.equals(TTTypeEnum.REVISE.getType()))) {
            tradeTicketDao.hideTT(ttId);
        }
    }

    @Override
    public void updateSignInfo(Integer ttId, ContractSignEntity contractSignEntity) {
        tradeTicketDao.updateSignInfo(ttId, contractSignEntity);
    }

    @Override
    public void updateSaveModifyTTInfo(TTDTO ttdto, TradeTicketEntity tradeTicketEntity) {

        // 修改、拆分保存操作只产生新录入的TT
        if (ttdto.getSubmitType() == SubmitTypeEnum.SAVE.getValue()) {

            SalesContractAddTTDTO salesContractAddTTDTO = ttdto.getSalesContractAddTTDTO();
            // 删除原来的tt,保证tt编号相同
            if (null != salesContractAddTTDTO) {
                Integer oldTtId = salesContractAddTTDTO.getTtId();
                if (null != oldTtId) {
                    tradeTicketDao.deleteById(oldTtId);
                    tradeTicketEntity.setCode(salesContractAddTTDTO.getCode());
                }
            }

            // 状态为新录入状态
            tradeTicketDao.updateById(tradeTicketEntity
                    .setStatus(TTStatusEnum.NEW.getType())
                    .setSourceType(SubmitTypeEnum.SAVE.getValue()));


            // 修改主体更新主体信息
            if (ttdto.getChangeCustomerFlag() != null && ttdto.getChangeCustomerFlag()) {
                Integer salesType = tradeTicketEntity.getSalesType();
                if (salesType != null && salesType.equals(ContractSalesTypeEnum.SALES.getValue())) {
                    CustomerDTO customer = customerFacade.getCustomerById(ttdto.getOriginCustomerId());
                    if (customer != null) {
                        updateEntityAndModify(tradeTicketEntity, ttModifyDao.getByTTId(tradeTicketEntity.getId()), customer.getId(), customer.getName(), customer.getLinkageCustomerCode(), true);
                    }
                } else {
                    CustomerDTO supplier = customerFacade.getCustomerById(ttdto.getOriginCustomerId());
                    if (supplier != null) {
                        updateEntityAndModify(tradeTicketEntity, ttModifyDao.getByTTId(tradeTicketEntity.getId()), supplier.getId(), supplier.getName(), null, false);
                    }
                }
            }
        }
    }

    @Override
    public void updateTtPriceComplete(TTPriceEntity ttPriceEntity, ContractEntity contractEntity) {

        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractEntity.getId());

        // set定价时合同价格明细
        ttPriceEntity.setContractPriceDetail(JSON.toJSONString(contractPriceEntity));

        // 加权品均价
        List<TTPriceEntity> ttPriceEntities = ttPriceDao.getConfirmPriceList(contractEntity.getId());
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal totalNum = BigDecimal.ZERO;

        if (ttPriceEntities != null && !ttPriceEntities.isEmpty()) {
            for (TTPriceEntity ttPrice : ttPriceEntities) {
                BigDecimal scalePrice = ttPrice.getPrice().setScale(2, RoundingMode.HALF_UP);
                BigDecimal num = ttPrice.getNum();
                totalPrice = totalPrice.add(scalePrice.multiply(num));
                totalNum = totalNum.add(num);
            }
        }

        BigDecimal averagePrice = BigDecimal.ZERO;
        if (totalNum.compareTo(BigDecimal.ZERO) > 0) {
            averagePrice = totalPrice.divide(totalNum, 2, RoundingMode.HALF_UP);
        }
        ttPriceEntity.setAvePrice(averagePrice);
        if (BigDecimalUtil.isEqual(ttPriceEntity.getRemainPriceNum(), BigDecimal.ZERO)) {
            //定价完成
            BigDecimal sourcePrice = contractEntity.getUnitPrice().subtract(contractPriceEntity.getForwardPrice());
            //最终合同价格
            BigDecimal endContractPrice = averagePrice.add(sourcePrice);
            ttPriceEntity.setEndContractPrice(endContractPrice);
            //最终全额货款
            ttPriceEntity.setEndAllPrice(endContractPrice.multiply(contractEntity.getContractNum()));

        }
        ttPriceDao.updateById(ttPriceEntity);
    }

    @Override
    public void invalidTTById(int type, String memo, Integer ttId) {
        tradeTicketDao.invalidTTById(TTStatusEnum.INVALID.getType(), "", ttId);
    }

    @Override
    public boolean updatePriceAllocateId(Integer contractId, Integer allocateId) {
        return ttTranferDao.updatePriceAllocateId(contractId, allocateId);
    }

    @Override
    public Boolean updateTTAddWarrantId(Integer ttId, Integer warrantId, String warrantCode) {
        return ttAddDao.updateTTAddWarrantId(ttId, warrantId, warrantCode);
    }

    @Override
    public boolean updatePriceByContractId(ContractPriceEntity contractPriceEntity) {
        return contractPriceDao.updatePriceByTtId(contractPriceEntity);
    }

    @Override
    public Boolean updateTTTranferById(TTTranferEntity ttTranferEntity) {
        return ttTranferDao.updateById(ttTranferEntity);
    }

    @Override
    public Boolean updateTTPriceById(TTPriceEntity ttPriceEntity) {
        return ttPriceDao.updateById(ttPriceEntity);
    }

    private void updateEntityAndModify(TradeTicketEntity entity, TTModifyEntity modifyEntity, Integer id, String name, String code, boolean isCustomer) {
        Optional.ofNullable(entity)
                .ifPresent(e -> {
                    if (isCustomer) {
                        e.setCustomerId(id).setCustomerName(name).setCustomerCode(code);
                    } else {
                        e.setSupplierId(id).setSupplierName(name);
                    }
                    tradeTicketDao.updateById(e);
                });

        Optional.ofNullable(modifyEntity)
                .ifPresent(m -> {
                    if (isCustomer) {
                        m.setCustomerId(id).setCustomerName(name).setCustomerCode(code);
                    } else {
                        m.setSupplierId(id).setSupplierName(name);
                    }
                    ttModifyDao.updateById(m);
                });
    }


    // TT详情
    // 通用方法 根据TTType 返回要封装的子类

    // DO 通用方法返回TTType

}
