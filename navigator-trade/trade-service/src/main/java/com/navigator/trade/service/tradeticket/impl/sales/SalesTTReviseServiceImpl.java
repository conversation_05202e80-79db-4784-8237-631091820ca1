package com.navigator.trade.service.tradeticket.impl.sales;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignCreateDTO;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.service.tradeticket.impl.BaseTradeTicketAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component("SBM_S_TT_REVISE,SBO_S_TT_REVISE")
public class SalesTTReviseServiceImpl extends BaseTradeTicketAbstractService {
    public SalesTTReviseServiceImpl() {
        ttTypeEnum = TTTypeEnum.REVISE;
        contractTradeTypeEnum = ContractTradeTypeEnum.REVISE_NORMAL;
        contractSource = ContractActionEnum.REVISE.getActionValue();
        contractStatus = ContractStatusEnum.MODIFYING.getValue();
        operationSource = OperationSourceEnum.SYSTEM.getValue();
        goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getParentValue();
        subGoodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        status = TTStatusEnum.APPROVING.getType();
        salesType = ContractSalesTypeEnum.SALES;
        processorType = ProcessorTypeEnum.SBM_S_REVISE.getTtValue();
        contractSignatureStatus = ContractSignStatusEnum.WAIT_PROVIDE.getValue();
    }

    @Override
    public void intParam(String processorType) {
        if (ProcessorTypeEnum.SBO_S_REVISE.getTtValue().equalsIgnoreCase(processorType)) {
            initOilParam();
        } else {
            initMealParam();
        }
    }

    @Override
    public TradeTicketEntity convertToTradeTicket(TTDTO ttdto) {
        TradeTicketEntity tradeTicketEntity = tradeTicketConvertUtil.getReviseTradeTicketEntity(ttdto.getSalesContractReviseTTDTO(), ttdto.getGroupId());
        injectionProperty(tradeTicketEntity);
        return tradeTicketEntity;
    }

    @Override
    public void saveTTSubInfo(Integer ttId, TTDTO ttDto) {
        // 保存tt-modify
        TTModifyEntity ttModifyEntity = saveTTModify(ttDto, ttId);

        // BUGFIX：case-1002581 定价单含税单价错误-变更保存价格明细 Author: Mr 2024-04-30 Start
        // 修改保存时候不存价格明细，ttModify中存在价格明细
        if (ttDto.getSubmitType() != SubmitTypeEnum.SAVE.getValue()) {
            ContractPriceEntity contractPrice = saveContractPrice(ttModifyEntity);
            String priceDetailInfo = JSONUtil.toJsonStr(contractPrice);
            ttModifyEntity.setPriceDetailInfo(priceDetailInfo);
            ttModifyDao.updateById(ttModifyEntity);
        }
        // BUGFIX：case-1002581 定价单含税单价错误-变更保存价格明细 Author: Mr 2024-04-30 End
    }

    private ContractPriceEntity saveContractPrice(TTModifyEntity ttModifyEntity) {
        // 保存价格信息
        ContractPriceEntity contractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(ttModifyEntity, contractPriceEntity);

        contractPriceService.save(contractPriceEntity.setId(null)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now()));
        return  contractPriceEntity;
    }

    @Override
    public ContractSignCreateDTO convertToContractSignCreateDTO(Integer ttId, TTDTO ttDto) {
        return contractSignConvertUtil.getReviseContractSignCreateDTO(ttId, ttDto, ttTypeEnum);
    }


    private TTModifyEntity saveTTModify(TTDTO ttDto, Integer ttId) {
        TTModifyEntity ttModifyEntity = tradeTicketConvertUtil.convertToReviseTtModify(ttDto, ttId, ttTypeEnum.getType());
        // V1 值转对象 Author:zengshl 2024-06-18 start
        ttModifyEntity.setDestinationValue(systemRuleConvertValue(ttModifyEntity.getDestination()));
        ttModifyEntity.setPackageWeightValue(systemRuleConvertValue(ttModifyEntity.getPackageWeight()));
        ttModifyEntity.setDeliveryTypeValue(deliveryTypeConvertValue(ttModifyEntity.getDeliveryType()));
        ttModifyEntity.setWeightCheckValue(systemRuleConvertValue(ttModifyEntity.getWeightCheck()));
        // 发票信息特殊处理取字典的信息
        ttModifyEntity.setInvoiceTypeValue(invoiceTypeConvertValue(ttModifyEntity.getInvoiceType()));
        ttModifyEntity.setShipWarehouseValue(factoryConvertValue(ttModifyEntity.getShipWarehouseId()));
        // V1 值转对象 Author:zengshl 2024-06-18 end
        ttModifyDao.save(ttModifyEntity);
        return ttModifyEntity;
    }

    @Override
    public TTDTO initDTO(TTDTO ttdto) {
        SalesContractReviseTTDTO salesContractReviseTTDTO = ttdto.getSalesContractReviseTTDTO();
        PriceDetailBO priceDetailBo = ttdto.getPriceDetailBO();
        Integer sourceContractId = salesContractReviseTTDTO.getSourceContractId();
        ContractEntity contractEntity = contractService.getBasicContractById(sourceContractId);
        salesContractReviseTTDTO.setSupplierId(contractEntity.getSupplierId());
        //获取变更字段
        List<CompareObjectDTO> compareObjectDTOList = getCompareObjectDTOList(salesContractReviseTTDTO, priceDetailBo, sourceContractId);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractReviseTTDTO.setModifyContent(modifyContent);

        //获取前后字段
        List<CompareObjectDTO> contentDTOList = getContentList(salesContractReviseTTDTO, priceDetailBo, sourceContractId);
        String content = JSON.toJSONString(contentDTOList);
        salesContractReviseTTDTO.setContent(content);

        //判断是否主体变更
        String code = CodeGeneratorUtil.genSalesTTNewCode();
        if (salesContractReviseTTDTO.getCustomerId().equals(contractEntity.getCustomerId())) {
            salesContractReviseTTDTO.setContractSource(contractSource);
            contractTradeTypeEnum = ContractTradeTypeEnum.REVISE_NORMAL;
            contractSource = ContractActionEnum.REVISE.getActionValue();
        } else {
            contractTradeTypeEnum = ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER;
            contractSource = ContractActionEnum.REVISE_CUSTOMER.getActionValue();
            salesContractReviseTTDTO.setContractSource(ContractActionEnum.REVISE_CUSTOMER.getActionValue());
            salesContractReviseTTDTO.setContractId(salesContractReviseTTDTO.getSonContractId());
        }
        //主体未变且不为基差转一口价,生成子编号
        if (salesContractReviseTTDTO.getAddedSignatureType() != -1) {
            boolean customerNotChange = salesContractReviseTTDTO.getCustomerId().equals(contractEntity.getCustomerId());
            if (customerNotChange && !changeFlag(modifyContent, compareObjectDTOList)) {
                // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 Start
                if (ttdto.getSubmitType() != SubmitTypeEnum.SAVE_SUBMIT.getValue()) {
                    code = getSalesTTCode(sourceContractId);
                } else {
                    code = getSaveSalesTTCode(sourceContractId);
                }
                // BUGFIX：case-1002628 协议是通过修改保存后再提交的，目前无法审批，审批报错 Author: Mr 2024-05-29 End
            }
        }

        salesContractReviseTTDTO.setTtType(ttTypeEnum.getType());
        salesContractReviseTTDTO.setCode(code);

        //初始化交易、销售类型、合同来源
        salesContractReviseTTDTO.setTradeType(contractTradeTypeEnum.getValue());
        salesContractReviseTTDTO.setSalesType(salesType.getValue());
        salesContractReviseTTDTO.setStatus(status);

        //协议签署状态
        salesContractReviseTTDTO.setContractSignatureStatus(contractSignatureStatus);
        salesContractReviseTTDTO.setUserId(JwtUtils.getCurrentUserId());

        ttdto.setSalesContractReviseTTDTO(salesContractReviseTTDTO);
        return ttdto;
    }


    @Override
    public SignTemplateDTO convertToSignTemplateDTO(Integer ttId) {
        return null;
    }

    private List<CompareObjectDTO> getCompareObjectDTOList(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        return tradeTicketConvertUtil.getReviseCompareObjectDTOS(salesContractReviseTTDTO, priceDetailBo, contractId);
    }

    private List<CompareObjectDTO> getContentList(SalesContractReviseTTDTO salesContractReviseTTDTO, PriceDetailBO priceDetailBo, Integer contractId) {
        return tradeTicketConvertUtil.getContentObjectDTOS(salesContractReviseTTDTO, priceDetailBo, contractId);
    }

    @Override
    public String buildBizDetailDescription(TTDTO ttdto) {
        //TODO NEO 之类自行实现
        return "";
    }

    @Override
    public TTDetailVO queryDetail(Integer ttId, TradeTicketEntity tradeTicketEntity) {
        TTModifyEntity ttModifyEntity = ttModifyDao.getTTModifyEntityByTTId(ttId);
//        String modifyContent = ttModifyEntity.getModifyContent();
        String modifyContent = ttModifyEntity.getContent();
        List<CompareObjectDTO> list = tradeTicketConvertUtil.getCompareList(modifyContent, tradeTicketEntity);
        TTDetailVO ttDetailVO = new TTDetailVO();
        ttDetailVO.setDetailType("10");
        ttDetailVO.setCompareObjectDTOList(list);

        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(ttModifyEntity, priceDetailVO);
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);

        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(ttModifyEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(ttModifyEntity.getPriceEndTime());

        //合同类型
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        if (null != ttModifyEntity.getSupplierId()) {
            ttQueryDetailVO.setSupplierId(String.valueOf(ttModifyEntity.getSupplierId()));
        }

        //买家
        if (null != ttModifyEntity.getCustomerId()) {
            ttQueryDetailVO.setCustomerId(String.valueOf(ttModifyEntity.getCustomerId()));
        }

        //商品信息
        if (null != ttModifyEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttModifyEntity.getGoodsCategoryId()));
        }
        if (null != ttModifyEntity.getGoodsPackageId()) {
            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttModifyEntity.getGoodsPackageId()));
        }
        if (null != ttModifyEntity.getGoodsSpecId()) {
            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttModifyEntity.getGoodsSpecId()));
        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }
        //应付履约保证金状态
        if (null != ttModifyEntity.getDepositAmount()) {
            int depositAmountStatus = ttModifyEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != ttModifyEntity.getAddedDepositAmount()) {
            int addedDepositAmountStatus = ttModifyEntity.getAddedDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != ttQueryDetailVO.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }

        //履约保证金
        if (null != ttModifyEntity.getDepositRate()) {
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateFacade.getCustomerDepositRateById(ttModifyEntity.getDepositRate());
            if (null != customerDepositRateEntity) {
                ttQueryDetailVO.setDepositRateValue(customerDepositRateEntity.getDepositRate());
            }
        }

        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, ttModifyEntity.getShipWarehouseId());

        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }


        String data = FastJsonUtils.getPropertyToJson("ttId", String.valueOf(ttId));
        recordTTQuery(data, LogBizCodeEnum.QUERY_SALES_TT, ttId, OperationSourceEnum.SYSTEM.getValue());

        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);
        ContractEntity originalContractEntity = contractService.getContractById(tradeTicketEntity.getSourceContractId());
        ttDetailVO.setTtCode(tradeTicketEntity.getCode())
                .setTtId(tradeTicketEntity.getId())
                .setSignId(tradeTicketEntity.getSignId())
                .setContractCode(originalContractEntity.getContractCode())
                .setContractId(tradeTicketEntity.getSourceContractId())
                .setSourceType(tradeTicketEntity.getSourceType())
                .setOriginContractNum(originalContractEntity.getContractNum())
                .setOriginContractType(originalContractEntity.getContractType())
                .setProtocolCode(tradeTicketEntity.getProtocolCode())
                .setUpdateTime(DateTimeUtil.formatDateTimeString(tradeTicketEntity.getUpdatedAt()));
        return ttDetailVO;
    }

    @Override
    public void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType) {

    }
}
