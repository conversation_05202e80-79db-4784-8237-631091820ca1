package com.navigator.trade.app.tt.logic.service.handler.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTranferTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.app.tt.domain.qo.TradeTicketQO;
import com.navigator.trade.app.tt.logic.service.handler.AbstractTTSceneHandler;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.enums.UsageEnum;
import com.navigator.trade.pojo.enums.WarrantTradeTypeEnum;
import com.navigator.trade.pojo.vo.PriceDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import com.navigator.trade.pojo.vo.TTQueryDetailVO;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/15
 * @Version 1.0
 */

@Slf4j
@Component("REVERSE_PRICE_HANDLER")
public class TTReversePriceSceneHandler extends AbstractTTSceneHandler {

    @Override
    public void initDTO(TTDTO ttdto) {
        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        Integer contractId = salesContractTTTransferDTO.getContractId();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(contractId);
        ContractPriceEntity contractPriceEntity = contractPriceService.getContractPriceEntityContractId(contractId);
        List<CompareObjectDTO> compareObjectDTOList = getTransferCompareObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity);
        String modifyContent = JSON.toJSONString(compareObjectDTOList);
        salesContractTTTransferDTO.setModifyContent(modifyContent);

        //获取前后字段
        PriceDetailBO priceDetailBO = ttdto.getPriceDetailBO();
        List<CompareObjectDTO> contentDTOList = getTransferContentObjectDTOS(salesContractTTTransferDTO, contractEntity, contractPriceEntity, priceDetailBO);
        String content = JSON.toJSONString(contentDTOList);
        salesContractTTTransferDTO.setContent(content);

        // add by zengshl
        if(BuCodeEnum.WT.getValue().equals(salesContractTTTransferDTO.getBuCode())
                && !(WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(salesContractTTTransferDTO.getWarrantTradeType()))) {
            salesContractTTTransferDTO.setStatus(TTStatusEnum.DONE.getType());
        }

        ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
    }

    private List<CompareObjectDTO> getTransferContentObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity, PriceDetailBO priceDetailBO) {
        //对比价格字段
        ContractPriceEntity newContractPriceEntity = new ContractPriceEntity();
        BeanUtils.copyProperties(priceDetailBO, newContractPriceEntity);
        BigDecimal newExtraPrice = BigDecimal.ZERO;
        if (salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue()) || salesContractTTTransferDTO.getType().equals(TTTranferTypeEnum.REVERSE_PRICING.getValue())) {
            newExtraPrice = salesContractTTTransferDTO.getPrice();
        } else {
            newExtraPrice = contractPriceEntity.getExtraPrice().add(salesContractTTTransferDTO.getPrice());
        }
        newContractPriceEntity.setExtraPrice(newExtraPrice);

        List<String> manualList = getManualList();
        List<CompareObjectDTO> priceList = BeanCompareUtils.getFields(contractPriceEntity, newContractPriceEntity, null, manualList);
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        ContractEntity newContractEntity = new ContractEntity();
        BeanUtil.copyProperties(contractEntity, newContractEntity);
        BeanUtil.copyProperties(salesContractTTTransferDTO, newContractEntity);
        newContractEntity.setPriceEndType(salesContractTTTransferDTO.getPriceEndType());
        newContractEntity.setPriceEndTime(salesContractTTTransferDTO.getPriceEndTime());
        contractEntity.setSignDate(DateTimeUtil.parseTimeStamp0000(contractEntity.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(newContractEntity.getSignDate());
        newContractEntity.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        ignoreList.add("extraPrice");
        // 比较获取不同字段返回list，转换成json
        List<CompareObjectDTO> compareObjectDTOS = BeanCompareUtils.getFields(contractEntity, newContractEntity, ignoreList, manualList);
        compareObjectDTOS.addAll(priceList);
        return compareObjectDTOS;
    }

    private List<CompareObjectDTO> getTransferCompareObjectDTOS(SalesContractTTTransferDTO salesContractTTTransferDTO, ContractEntity contractEntity, ContractPriceEntity contractPriceEntity) {
        // 获取变更字段
        // 原始的 SalesContractTTTransferDTO 通过原始合同去填充属性）
        List<String> manualList = getManualList();
        SalesContractTTTransferDTO originalDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, originalDTO);
        originalDTO.setSignDate(DateTimeUtil.parseTimeStamp0000(originalDTO.getSignDate()));
        Date signDate = DateTimeUtil.parseTimeStamp0000(salesContractTTTransferDTO.getSignDate());
        salesContractTTTransferDTO.setSignDate(signDate);
        List<String> ignoreList = getIgnoreList();
        // 比较获取不同字段返回list，转换成json
        return BeanCompareUtils.compareFields(originalDTO, salesContractTTTransferDTO, ignoreList, manualList);
    }

    @Override
    public boolean isMatch(TTTypeEnum ttTypeEnum) {
        return TTTypeEnum.REVERSE_PRICE.equals(ttTypeEnum);
    }

    @Override
    public List<TTQueryVO> saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext) {
        List<TTQueryVO> ttQueryVOS = new ArrayList<>();

        // 1. 初始化
        initDTO(ttdto);

        // 2. 转换
        TradeTicketDO tradeTicketDO = tradeTicketDOConvert.transfer2TradeTicketDO(ttdto);

        // 3. 保存至数据库
        tradeTicketDO = ttDomainService.createTradeTicketDO(tradeTicketDO);
        TradeTicketEntity ticketEntity = tradeTicketDO.getTradeTicketEntity();

        SalesContractTTTransferDTO salesContractTTTransferDTO = ttdto.getSalesContractTTTransferDTO();
        if (salesContractTTTransferDTO != null) {
            // 4. 提交审批
            if (salesContractTTTransferDTO.getAddedSignatureType() != -1) {
                ttApproveHandler.startTTApprove(ticketEntity.getId(), ttdto, null);
            }

            // 5. 隐藏TT
            ttDomainService.dealGroupTT(ticketEntity.getId(),
                    ticketEntity.getType(),
                    salesContractTTTransferDTO.getAddedSignatureType());

            // 6. 返回结果 - 前端展示
            TTQueryVO ttQueryVO = new TTQueryVO();
            ttQueryVO.setSourceFlag(salesContractTTTransferDTO.getAddedSignatureType() != 0 ? 1 : 2)
                    .setContractCode(ticketEntity.getContractCode())
                    .setCode(ticketEntity.getCode())
                    .setTtId(ticketEntity.getId());
            ttQueryVOS.add(ttQueryVO);
            salesContractTTTransferDTO.setTtId(ticketEntity.getId());
        }
        return ttQueryVOS;
    }

    @Override
    public TTDetailVO queryTTDetail(Integer ttId) {
        // 1、查询TT基础信息
        TradeTicketQO tradeTicketQO = new TradeTicketQO();
        tradeTicketQO.setTtId(ttId);
        TradeTicketDO tradeTicketDO = ttQueryDomainService.queryTradeTicketDOByTTID(tradeTicketQO);
        TradeTicketEntity tradeTicketEntity = tradeTicketDO.getTradeTicketEntity();
        TTTranferEntity  ttTranferEntity = (TTTranferEntity) tradeTicketDO.getTtSubEntity();
        ContractPriceEntity contractPriceEntity = tradeTicketDO.getContractPriceEntity();
        ContractEntity contractEntity = contractQueryLogicService.getBasicContractById(tradeTicketEntity.getContractId());

        // 2、基础信息convert
        TTDetailVO ttDetailVO = new TTDetailVO();
        TTQueryDetailVO ttQueryDetailVO = new TTQueryDetailVO();
        PriceDetailVO priceDetailVO = new PriceDetailVO();
        BeanUtils.copyProperties(ttTranferEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(tradeTicketEntity, ttQueryDetailVO);
        BeanUtils.copyProperties(contractEntity, ttQueryDetailVO);
        if (null != contractPriceEntity) {
            if (StringUtils.isNotBlank(contractPriceEntity.getPreviousRecord())) {
                ContractPriceEntity contractPriceEntity1 = JSON.parseObject(contractPriceEntity.getPreviousRecord(), ContractPriceEntity.class);
                BeanUtils.copyProperties(contractPriceEntity1, priceDetailVO);
            } else {
                BeanUtils.copyProperties(contractPriceEntity, priceDetailVO);
            }
        }
        priceDetailVO.setForwardPrice(contractPriceEntity.getForwardPrice());
        priceDetailVO.setExtraPrice(contractPriceEntity.getExtraPrice());
        ttQueryDetailVO.setPriceDetailVO(priceDetailVO);
        if (null != tradeTicketEntity.getContractType()) {
            ttQueryDetailVO.setContractType(String.valueOf(tradeTicketEntity.getContractType()));
        }

        //卖家
        ttQueryDetailVO.setSupplierId(String.valueOf(tradeTicketEntity.getSupplierId()));
        //买家
        ttQueryDetailVO.setCustomerId(String.valueOf(tradeTicketEntity.getCustomerId()));
        ttQueryDetailVO.setSupplierAccountId(tradeTicketEntity.getBankId());
        // String type = String.format("%s_%s", buCode, salesType);
        Integer customerId = ContractSalesTypeEnum.SALES.getValue() == tradeTicketEntity.getSalesType() ? tradeTicketEntity.getCustomerId() : tradeTicketEntity.getSupplierId();
        CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);
        if (null != customerDTO) {
            ttQueryDetailVO.setEnterprise(customerDTO.getEnterprise());
            ttQueryDetailVO.setEnterpriseName(customerDTO.getEnterpriseName());
            ttQueryDetailVO.setCustomerBankDTOS(customerDTO.getCustomerBankDTOS());
        }

        //商品信息
        if (null != ttTranferEntity.getGoodsCategoryId()) {
            ttQueryDetailVO.setGoodsCategoryId(String.valueOf(ttTranferEntity.getGoodsCategoryId()));
        }
//        if (null != ttTranferEntity.getGoodsPackageId()) {
//            ttQueryDetailVO.setGoodsPackageId(String.valueOf(ttTranferEntity.getGoodsPackageId()));
//        }
//        if (null != ttTranferEntity.getGoodsSpecId()) {
//            ttQueryDetailVO.setGoodsSpecId(String.valueOf(ttTranferEntity.getGoodsSpecId()));
//        }

        //商务
        if (null != tradeTicketEntity.getOwnerId()) {
            ttQueryDetailVO.setOwnerId(tradeTicketEntity.getOwnerId());
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getOwnerId());
            if (null != employEntity) {
                ttQueryDetailVO.setOwnerName(employEntity.getName());
            }
        }

        //创建人
        if (null != tradeTicketEntity.getCreatedBy()) {
            EmployEntity employEntity = employFacade.getEmployById(tradeTicketEntity.getCreatedBy());
            if (null != employEntity) {
                ttQueryDetailVO.setCreatedBy(employEntity.getName());
            }
        }

        //应付履约保证金状态
        if (null != contractEntity.getDepositAmount()) {
            int depositAmountStatus = contractEntity.getDepositAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setDepositAmountStatus(depositAmountStatus);
        }

        //追加履约保证金状态
        if (null != contractEntity.getAddedDeposit()) {
            int addedDepositAmountStatus = contractEntity.getAddedDeposit().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0;
            ttQueryDetailVO.setAddedDepositAmountStatus(addedDepositAmountStatus);
        }

        if (null != contractEntity.getInvoiceType()) {
            ttQueryDetailVO.setInvoiceType(contractEntity.getInvoiceType());
            ttQueryDetailVO.setInvoiceTypeName(InvoiceTypeEnum.getByValue(ttQueryDetailVO.getInvoiceType()).getDesc());
        }
        if (null != ttQueryDetailVO.getDeliveryType()) {
            DeliveryTypeEntity deliveryTypeEntity = iDeliveryTypeService.getDeliveryTypeById(ttQueryDetailVO.getDeliveryType());
            if (null != deliveryTypeEntity) {
                ttQueryDetailVO.setDeliveryTypeName(deliveryTypeEntity.getName());
            }
        }
        // 履约保证金释放方式
        ttQueryDetailVO.setDepositUseRule(contractEntity.getDepositReleaseType());
        // 点价截止日期
        ttQueryDetailVO.setPriceEndTime(contractEntity.getPriceEndTime());
        //履约保证金
        ttQueryDetailVO.setDepositRate(contractEntity.getDepositRate());
        //查询工厂信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, contractEntity.getShipWarehouseId());
        if (StringUtils.isNotBlank(contractEntity.getShipWarehouseValue())) {
            ttQueryDetailVO.setShipWarehouseName(contractEntity.getShipWarehouseValue());
        }
        //查询配置名称
        //目的地
        String destinationName = ttQueryDetailVO.getDestination();
        if (StringUtils.isNumeric(destinationName)) {
            SystemRuleItemEntity destinationSystemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(destinationName));
            destinationName = destinationSystemRuleItemEntity != null ? destinationSystemRuleItemEntity.getRuleKey() : "";
        }
        ttQueryDetailVO.setDestinationName(destinationName);

        //重量检测
        if (StringUtils.isNotBlank(ttQueryDetailVO.getWeightCheck())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getWeightCheck()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setWeightCheckName(systemRuleItemEntity.getRuleKey());
            }
        }
        //袋皮扣重
        if (StringUtils.isNotBlank(ttQueryDetailVO.getPackageWeight())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(ttQueryDetailVO.getPackageWeight()));
            if (systemRuleItemEntity != null) {
                ttQueryDetailVO.setPackageWeightName(systemRuleItemEntity.getRuleKey());
            }
        }

        //企标文件编号
        if (null != ttQueryDetailVO.getStandardFileId() && ttQueryDetailVO.getStandardFileId() > 0) {
            SystemRuleItemEntity standardFileItem = systemRuleFacade.getRuleItemById(ttQueryDetailVO.getStandardFileId());
            ttQueryDetailVO.setStandardFileCode(null == standardFileItem ? "" : standardFileItem.getRuleKey());

        }

        // 发货库点
        //查询库点信息
        ttQueryDetailVO = tradeTicketConvertUtil.setShipWarehouse(ttQueryDetailVO, contractEntity.getShipWarehouseId());

        //原合同信息
        ttQueryDetailVO.setRootContractId(contractEntity.getParentId());
        if (null != contractEntity.getParentId()) {
            ContractEntity originalContract = contractQueryLogicService.getBasicContractById(contractEntity.getParentId());
            String contractCode = originalContract != null ? originalContract.getContractCode() : null;
            ttQueryDetailVO.setRootContractCode(contractCode);
        }
        if (tradeTicketEntity.getUsage() != null) {
            ttQueryDetailVO.setUsageString(UsageEnum.getDescByValue(tradeTicketEntity.getUsage()));
        }
        ttDetailVO.setDetailType("0");
        ttDetailVO.setTtQueryDetailVO(ttQueryDetailVO);

        return ttDetailVO;
//        return new TTDetailVO();
    }

}
