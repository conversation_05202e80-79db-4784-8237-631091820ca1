package com.navigator.trade.app.sign.domain.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.entity.ContractPaperEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.ContractSignVOEntity;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.qo.ContractSignQO;
import com.navigator.trade.pojo.vo.ContractSignAllStatusNumVO;

import java.util.List;
import java.util.Map;

/**
 * 处理协议的查询包括协议正本信息处理
 * <AUTHOR>
 * @date 20240715
 */
public interface ContractSignQueryDomainService {

    /**
     * 查询协议列表数据
     * @param queryDTO
     * @param customerIds
     * @param siteCodeList
     * @return
     */
    IPage<ContractSignVOEntity> queryContractSign(QueryDTO<ContractSignQO> queryDTO,
                                                  List<Integer> customerIds,
                                                  List<String> siteCodeList);

    /**
     * 根据ID获取协议信息
     * @param contractSignId
     * @return
     */
    ContractSignEntity getById(Integer contractSignId);

    /**
     * 根据TTID获取协议信息
     * @param ttId
     * @return
     */
    ContractSignEntity getSignDetailByTtId(Integer ttId);

    /**
     * 根据唯一编号查询协议信息
     *
     * @param uuid
     * @returnz
     */
    ContractSignEntity queryByUUId(String uuid);
    /**
     * 根据合同编号，查询协议信息
     *
     * @param contractId 合同编号
     * @return
     */
    public List<ContractSignEntity> queryByContractId(Integer contractId);

    /**
     * 统计数量
     *
     * @param status     协议状态
     * @param ldcFrame   是否为non-frame模版
     * @param customerId 客户ID
     * @return
     */
    Integer getContractSignStat(Integer status, Integer ldcFrame, Integer customerId, Integer salesType,
                                       Integer goodsCategoryId, List<String> siteCodeList);
    /**
     * 统计数量
     *
     * @param status     协议状态
     * @param ldcFrame   是否为non-frame模版
     * @param customerId 客户ID
     * @return
     */
    List<ContractSignEntity> getContractSignList(Integer status, Integer ldcFrame, Integer customerId, Integer salesType,
                                Integer goodsCategoryId, List<Integer> belongCustomerIdList);

    /**
     * 获取协议各个状态数量
     * @param signBO
     * @return
     */
    ContractSignAllStatusNumVO getColumbusContractSignStat(QueryContractSignBO signBO);

    /**
     * Magellan-获取协议各个状态数量
     * @param signBO
     * @return
     */
    ContractSignAllStatusNumVO getMagellanContractSignStat(QueryContractSignBO signBO);
    /**
     * 查询完成的协议数据
     * @param ids
     * @param ttTypeList
     * @return
     */
    List<ContractSignEntity> queryIncompleteByContractId(List<Integer> ids, List<Integer> ttTypeList);

    /**
     * 查询签订的协议列表
     * @param contractId
     * @param ttStatusList
     * @return
     */
    List<ContractSignEntity> querySignListByContractId(Integer contractId, List<Integer> ttStatusList);

    /**
     * 查询子合同协议拆分的列表
     * @param ids
     * @return
     */
    List<ContractSignEntity> querySonContractSplitIncomplete(List<Integer> ids);

    /**
     * 查询合同正本
     * @param contractSignId
     * @return
     */
    ContractPaperEntity getContractPaper(Integer contractSignId);

    /**
     * 查询正本的协议列表
     * @param contractSingId
     * @return
     */
    List<ContractSignEntity> sendContractSignOriginalPaper(List<Integer> contractSingId);
    /**
     * 方式合同正本
     * @return
     */
    List<Integer> sendContractSignOriginalPaper();


    /**
     * 根据条件查询列表
     * @param contractSingId
     * @param customerId
     * @param supplierId
     * @param goodsCategoryId
     * @return
     */
    List<ContractSignEntity> queryContractSignByIdSCustomerIdSalesType(List<Integer> contractSingId, Integer customerId,
                                                                       String supplierId, Integer goodsCategoryId);


    /**
     * 根据合同编码查询协议
     * @param contractCode
     * @return
     */
    ContractSignEntity queryContractSignByContractCode(String contractCode);

    /**
     * 根据TT编码获取协议信息
     * @param ttCode
     * @return
     */
    ContractSignEntity queryByTTCode(String ttCode);
}
