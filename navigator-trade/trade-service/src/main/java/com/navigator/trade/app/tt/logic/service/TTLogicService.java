package com.navigator.trade.app.tt.logic.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.app.trade.model.ArrangeContext;
import com.navigator.trade.app.tt.domain.model.TradeTicketDO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.contract.ContractWriteOffWithDrawDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.entity.TTPriceEntity;
import com.navigator.trade.pojo.enums.TTStatusEnum;
import com.navigator.trade.pojo.vo.ConfirmPriceVO;
import com.navigator.trade.pojo.vo.TTQueryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/14 17:00
 * @Version 1.0
 */
public interface TTLogicService {


    Result<List<TTDTO>> prepareContractCreateData(SubmitTTDTO submitTTDTO);

    /**
     * 保存TT（兼容：业务类型-仓现 采销类型 品类-粕油二 业务场景-新增修改拆分）
     * 仅提供：保存TT，提交审批能力
     * submitType 区分保存提交能力
     *
     * @param ttdto
     * @return
     */
    TTSubmitResultDTO saveTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext);

    List<TTSubmitResultDTO> submitTradeTicketDomainData(List<TTDTO> ttdtoList, List<ArrangeContext> arrangeContextList);

    TTSubmitResultDTO submitTradeTicketDomainData(TTDTO ttdto, ArrangeContext arrangeContext);

    List<TTQueryVO> saveTT(TTDTO ttdto, ArrangeContext arrangeContext);

    /**
     * 更新TT中的合同id
     *
     * @param tradeTicketDO TT领域模型
     * @return true 更新成功
     */
    Boolean updateContractId(TradeTicketDO tradeTicketDO);


    /**
     * 更新TT中的合同id
     *
     * @param ttId
     * @return true 更新成功
     */
    Boolean updateTTAddWarrantId(Integer ttId, Integer warrantId, String warrantCode);


    /**
     * @param approvalDTO
     * @param ttType
     * @description: TT审批
     * @return:
     */
    void approveTT(ApprovalDTO approvalDTO, Integer ttType);


    /**
     * 合同拆分定价单校验
     *
     * @param contractEntity
     * @param contractModifyDTO
     */
    void splitTTPriceCheck(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO);

    /**
     * 拆分定价单处理
     *
     * @param contractEntity    合同信息
     * @param contractModifyDTO 变更信息
     */
    void splitTTPrice(ContractEntity contractEntity, ContractModifyDTO contractModifyDTO, ArrangeContext arrangeContext);

    /**
     * 记录前后的变更内容
     *
     * @param originalContractEntity
     * @param contractEntity
     * @param ttId
     * @param ttType
     */
    void updateModifyContent(ContractEntity originalContractEntity, ContractEntity contractEntity, Integer ttId, Integer ttType);

    /**
     * 更新TT中协议的内容
     *
     * @param ttId               TT编号
     * @param contractSignEntity 合同签署信息
     */
    void updateSignInfo(Integer ttId, ContractSignEntity contractSignEntity);

    /**
     * 删除TT
     *
     * @param ttId
     */
    void deleteTT(Integer ttId);

    /**
     * TT撤回
     *
     * @param operateTTDTO
     */
    void cancelTT(OperateTTDTO operateTTDTO);

    /**
     * TT 作废处理
     *
     * @param operateTTDTO
     */
    void invalidTT(OperateTTDTO operateTTDTO);

    /**
     * 根据合同ID注销撤回作废操作
     * 作废TT
     *
     * @param contractId
     */
    void invalidTTByContractId(Integer contractId);

    /**
     * 更新TT的状态
     *
     * @param ttId
     * @param statusEnum
     */
    void updateTTStatusById(Integer ttId, TTStatusEnum statusEnum);

    /**
     * 保存TT检查
     *
     * @param ttdto
     */
    void saveTTCheck(TTDTO ttdto);

    /**
     * 转月/反点价/点价/撤回
     * TODO 待切换新的方法调用
     * @param contraryPriceDTO
     * @return
     */
    Result contraryPrice(ContraryPriceDTO contraryPriceDTO);

    /**
     * 根据合同ID注销撤回作废操作
     * 作废TT
     *
     * @param contractWriteOffWithDrawDTO
     */
    void withdrawContractLog(ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO);

    /**
     * TT定价单的撤回操作-并取消定价
     * @param contractId
     * @return
     */
    List<TTPriceEntity> withdrawPrice(Integer contractId);
}
