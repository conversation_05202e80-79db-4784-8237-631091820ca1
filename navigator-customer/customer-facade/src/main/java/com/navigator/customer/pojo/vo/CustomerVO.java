package com.navigator.customer.pojo.vo;

import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 10:11
 */
@Data
public class CustomerVO extends CustomerEntity {

    @ApiModelProperty(value = "客户详细信息")
    private List<ContactDTO> contactDTO;

    @ApiModelProperty(value = "客户银行详情")
    private List<CustomerBankDTO> customerBankDTOS;

    @ApiModelProperty(value = "客户配置")
    private List<CustomerDetailDTO> customerDetailDTOS;
}
