package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.customer.pojo.enums.BelongAreaEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_factory")
@ApiModel(value = "FactoryEntity对象", description = "")
public class FactoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "linkinage客户编码")
    private String lkgFactoryCode;

    @ApiModelProperty(value = "其他平台客户编码")
    private String mdmFactoryCode;

    @ApiModelProperty(value = "油厂编码")
    private String code;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "油厂名称")
    private String name;

    @ApiModelProperty(value = "油厂简称")
    private String shortName;

    /**
     * 所属区域
     * {@link BelongAreaEnum}
     */
    @ApiModelProperty(value = "所属区域")
    private Integer belongArea;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否禁用 0禁用 1启用")
    private Integer status;

    @ApiModelProperty(value = "是否删除(0:启用 1:删除)")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建日期")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改日期")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;
}
