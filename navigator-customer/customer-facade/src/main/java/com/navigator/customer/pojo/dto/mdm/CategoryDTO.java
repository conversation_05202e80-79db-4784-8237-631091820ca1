package com.navigator.customer.pojo.dto.mdm;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/24
 */
@Data
public class CategoryDTO {

    private String code;
    private String name;
    private String isMainCategory;
    private String sendToCreditRisk;

    private List<SubCategory> subCategories;

    @Data
    public static class SubCategory {
        private String code;
        private String name;
    }
}
