package com.navigator.customer.pojo.bo;

import com.navigator.customer.pojo.entity.BankFactoryEntity;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
@Data
public class CustomerBankBO extends CustomerBankEntity {

    //适用工厂编码
    private List<BankFactoryEntity> bankFactoryEntities;
}
