package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/20 11:34
 */
@Data
@Accessors(chain = true)
public class CustomerDetailDTO extends CustomerDetailEntity {

    /**
     * 转月状态是否改变
     */
    private boolean transferStatusChange = false;

    /**
     * 反点价状态是否改变
     */
    private boolean reversePriceStatusChange = false;

    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-21 Start
    /**
     * 操作编码
     */
    private String detailCode;
    // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-21 End

}
