package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 10:11
 */
@Data
public class CustomerDTO extends CustomerEntity {


    @ApiModelProperty(value = "客户联系人信息")
    private List<ContactEntity> contactDTO;

    @ApiModelProperty(value = "客户银行详情")
    private List<CustomerBankDTO> customerBankDTOS;

    @ApiModelProperty(value = "客户配置")
    private List<CustomerDetailDTO> customerDetailDTOS;

    @ApiModelProperty(value = "保证金")
    private List<CustomerDepositRateEntity> customerDepositRateDTOS;

    //客户配置
    private CustomerDetailDTO customerDetailDTO;

    private String detailCode;

    //采销类型
    private Integer salesType;
    //拆分合同
    private Integer split;
}
