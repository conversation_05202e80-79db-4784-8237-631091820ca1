package com.navigator.customer.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/05/11 10:21
 */

@Data
public class CustomerConfigDTO {

    @Excel(name = "客户编码")
    private String linkageCustomerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "助记符")
    private String customerIndexesName;

    @Excel(name = "AX代码")
    private String axCode;

    @Excel(name = "集团客户名称")
    private String enterpriseName;

    @Excel(name = "集团客户编码")
    private String enterpriseCode;

    @Excel(name = "是否是集团客户")
    private String enterprise;

    @Excel(name = "客户类别")
    private String customerType;

    @Excel(name = "区")
    private String district;

    @Excel(name = "全称")
    private String fullName;

    @Excel(name = "全称(英文)")
    private String fullNameEnglish;

    @Excel(name = "联系人")
    private String contact;

    @Excel(name = "电话")
    private String phone;

    @Excel(name = "FAX")
    private String fax;

    @Excel(name = "地址")
    private String address;

    @Excel(name = "邮编")
    private String postcode;

    @Excel(name = "缺省发票类型")
    private Integer invoiceIdNo;

    @Excel(name = "等级")
    private String level;

    @Excel(name = "终端客户")
    private Integer terminalCustomer;

    @Excel(name = "经销商")
    private Integer dealer;

    @Excel(name = "商务客户")
    private Integer businessCustomer;

    @Excel(name = "允许调拨销售")
    private Integer moving;

    @Excel(name = "允许CNF销售")
    private Integer cnfSelling;

    @Excel(name = "允许信用销售")
    private Integer creditSelling;

    @Excel(name = "信用销售最大额度")
    private BigDecimal theLimit;

    @Excel(name = "共享集团授信")
    private Integer shareEnterpriseCredit;

    @Excel(name = "Trading Limit")
    private BigDecimal tradingLimit;

    @Excel(name = "预付款额度")
    private BigDecimal largestAdvance;

    @Excel(name = "默认到达港口")
    private String defaultPort;

    @Excel(name = "默认运输时间")
    private Date defaultTransportTime;

    @Excel(name = "到达后联系人")
    private String arriveContact;

    @Excel(name = "到达后联系电话")
    private String arrivePhone;

    @Excel(name = "开户银行")
    private String bankName;

    @Excel(name = "银行账号")
    private String bankAccountNo;

    @Excel(name = "税号")
    private String taxNo;

    @Excel(name = "开票要求")
    private String invoiceRequire;

    @Excel(name = "开票地址")
    private String invoiceAddress;

    @Excel(name = "开票电话")
    private String invoicePhone;

    @Excel(name = "邮编")
    private String email;

    @Excel(name = "主页")
    private String homepage;

    @Excel(name = "电子发票接收短信手机")
    private String electronicInvoicePhone;

    @Excel(name = "电子发票接收邮件")
    private String electronicInvoiceEmail;

    @Excel(name = "Categorylist")
    private String categoryList;

    @Excel(name = "业务员")
    private String bizEmploy;

    @Excel(name = "供应商代码")
    private String supplierCode;

    @Excel(name = "客户状态")
    private String status;

    @Excel(name = "创建人")
    private String createdBy;

    @Excel(name = "创建日期")
    private String createdAt;

    @Excel(name = "备注")
    private String mone;

    @Excel(name = "客户签订地")
    private String signPlace;

    @Excel(name = "客户简称")
    private String shortName;

    @Excel(name = "是否有授权书", orderNum = "4", width = 15)
    private String isAuthorization;

    @Excel(name = "是否是供应商")
    private String isSupplier;

}
