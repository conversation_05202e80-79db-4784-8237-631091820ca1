package com.navigator.customer.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@Data
@Accessors(chain = true)
public class CustomerInvoiceDTO {

    //客户id
    private Integer customerId;
    //一级品类
    private String category1;
    //二级品类
    private String category2;
    //三级品类
    private String category3;
    //所属主体Id
    private Integer companyId;
    //发票id
    private Integer invoiceId;

    //发票id
    private String invoiceName;

    private Integer id;

}
