package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.FactoryConfigDTO;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.SupplierEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/1 11:34
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface SupplierFacade {
    /**
     * 根据供应商id查询出供应商信息
     *
     * @param id
     * @return
     */
    @PostMapping("/getSupplierById")
    SupplierDTO getSupplierById(@RequestParam(value = "id") Integer id);

    /**
     * 获取油厂/供应商
     *
     * @param type 0供应商 1油厂
     * @return 供应商集合信息
     */
    @GetMapping("/getSupplierListByType")
    List<SupplierEntity> getSupplierListByType(@RequestParam(value = "type", required = false) Integer type);

    /**
     * 查询供应商信息
     *
     * @return
     */
    @GetMapping("/querySupplierList")
    Result querySupplierList();

    /**
     * 根据供应商id查询油厂
     *
     * @param parentId
     * @return
     */
    @GetMapping("/querySupplierFactory")
    Result querySupplierFactory(@RequestParam(value = "parentId") Integer parentId);


    /**
     * 查询出货工厂
     *
     * @param supplierId
     * @return
     */
    @GetMapping("/factoryConfigBySuppId")
    List<FactoryConfigDTO> factoryConfigBySuppId(@RequestParam(value = "supplierId") Integer supplierId);

    /**
     * 根据id查询出工厂信息
     *
     * @param id
     * @return
     */
    @GetMapping("/getFactoryConfigById")
    FactoryConfigDTO getFactoryConfigById(@RequestParam(value = "id") Integer id);
}
