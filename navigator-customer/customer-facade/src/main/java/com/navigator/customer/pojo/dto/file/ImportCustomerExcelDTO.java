package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ImportCustomerExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "公司名称")
    private String customerName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "品种")
    private String category3;

    @Excel(name = "采销类型")
    private String saleType;

    @Excel(name = "是否需要正本")
    private String originalPaper;

    @Excel(name = "是否NF")
    private String ldcFrame;

    @Excel(name = "所属主体")
    private String companyCode;

}
