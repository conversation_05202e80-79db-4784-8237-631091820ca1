package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/15 16:44
 */

@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerCreditPaymentFacade {

    /**
     * 查询客户赊销&预付款信息
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/customerCreditPaymentAllList")
    Result customerCreditPaymentAllList(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO);

    /**
     * 修改客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/updateCustomerCreditPayment")
    Result updateCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO);


    /**
     * 添加客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/addCustomerCreditPayment")
    Result addCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO);

    /**
     * 导入客户赊销数据
     *
     * @param file
     * @return
     */
    @PostMapping("/leadCustomerCreditPayment")
    Result leadCustomerCreditPayment(@RequestParam(value = "file") MultipartFile file);

}
