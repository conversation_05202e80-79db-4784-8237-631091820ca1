package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@Data
@Accessors(chain = true)
public class CustomerDeliveryWhiteDTO extends CustomerDeliveryWhiteEntity {


    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;

    private Map<Integer, List<Integer>> categoryMap;

    private List<Integer> customerIdList;
}
