package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.FactoryCreateDTO;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.customer.pojo.vo.FactoryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/28 10:34
 */
@FeignClient(name = "navigator-customer-service")
@Component
@Deprecated
public interface FactoryWarehouseFacade {

    /**
     * 保存/更新工厂信息
     *
     * @param factoryCreateDTO 工厂信息
     * @return 保存/更新结果
     */
    @PostMapping("/saveOrUpdateFactory")
    Result saveOrUpdateFactory(@RequestBody FactoryCreateDTO factoryCreateDTO);

    /**
     * 更新工厂状态
     *
     * @param factoryId 工厂ID
     * @return 更新结果
     */
    @GetMapping("/updateStatus")
    Result updateStatus(@RequestParam(value = "factoryId") Integer factoryId);

    /**
     * 获取所有工厂信息
     *
     * @return 工厂列表
     */
    @GetMapping("/getAllFactory")
    List<FactoryEntity> getAllFactory(@RequestParam(value = "status", required = false) Integer status);

    @GetMapping("/queryFactoryList")
    List<FactoryVO> queryFactoryList();

    @GetMapping("/getFactoryDetailById")
    FactoryEntity getFactoryDetailById(@RequestParam(value = "factoryId") Integer factoryId);

    @GetMapping("/getFactoryDetailByLkg")
    FactoryWarehouseEntity getFactoryDetailByLkg(@RequestParam(value = "lkgWareHouseCode") String lkgWareHouseCode);

    /**
     * 根据油厂id查询油厂信息
     *
     * @param factoryId
     * @return
     */
    @GetMapping("/getFactoryInfoById")
    FactoryEntity getFactoryInfoById(@RequestParam(value = "factoryId") Integer factoryId);

    /**
     * 根据油厂id查询油厂信息
     *
     * @param factoryCode 交货工厂编码
     * @return 交货工厂信息
     */
    @GetMapping("/getFactoryByCode")
    FactoryEntity getFactoryByCode(@RequestParam(value = "factoryCode") String factoryCode);

    @GetMapping("/queryFactoryWarehouseById")
    FactoryWarehouseEntity queryFactoryWarehouseById(@RequestParam(value = "id") Integer id);


    @GetMapping("/queryFactoryWarehouseByCode")
    Result queryFactoryWarehouseByCode(@RequestParam(value = "factoryCode") String factoryCode);

    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId
     * @return
     */
    @GetMapping("/getFactoryWarehouseList")
    Result getFactoryWarehouseList(@RequestParam(value = "factoryId", required = false) List<Integer> factoryId,
                                   @RequestParam(value = "factoryCode", required = false) String factoryCode,
                                   @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                   @RequestParam(value = "status", required = false) Integer status);

    @PostMapping("/getFactoryWarehouseList")
    Result getFactoryWarehouseList(@RequestBody FactoryQueryDTO factoryQueryDTO);

    @PostMapping("saveOrUpdateFactoryWarehouse")
    Result saveOrUpdateFactoryWarehouse(@RequestBody FactoryWarehouseEntity warehouseEntity);

    @GetMapping("/invalidWarehouse")
    Result invalidWarehouse(@RequestParam(value = "id")Integer warehouseId);

    @PostMapping("/importFactoryWarehouse")
    Result importFactoryWarehouse(@RequestParam("file") MultipartFile uploadFile);

    @PostMapping("/queryFactoryByCompanyId")
    List<FactoryEntity> queryFactoryByCompanyId(@RequestParam(value = "companyId")Integer companyId);

    @GetMapping("/getWarehouseListById")
    List<FactoryWarehouseEntity> getWarehouseListById(@RequestParam(value = "warehouseIdList")List<Integer> warehouseIdList);
    /**
     * 保存/更新工厂信息
     *
     * @param factoryEntity 工厂信息
     * @return 保存/更新结果
     */
    @PostMapping("/updateFactoryById")
    Result updateFactoryById(@RequestBody FactoryEntity factoryEntity);

}
