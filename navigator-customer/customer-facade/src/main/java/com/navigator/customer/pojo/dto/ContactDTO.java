package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 10:15
 */
@Data
public class ContactDTO extends ContactEntity {

    //客户id
    private Integer customerId;

    //适用工厂编码
    private List<ContactFactoryEntity> contactFactoryEntities;

}
