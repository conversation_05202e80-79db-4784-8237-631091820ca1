package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class CustomerOriginalPaperDTO extends CustomerOriginalPaperEntity {

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    @ApiModelProperty(value = "品类名称")
    private String category3Name;

    private Map<Integer, List<Integer>> categoryMap;

    //主体名称
    private String companyName;
}
