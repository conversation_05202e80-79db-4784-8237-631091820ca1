package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
@Accessors(chain = true)
@TableName("dba_customer_detail")
@ApiModel(value = "CustomerDetailEntity对象", description = "")
public class CustomerDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "质量检验标准")
    private String qualityCheckRule;

    @ApiModelProperty(value = "质量检验名称（默认是通用标准）")
    private String qualityCheckName;

    @ApiModelProperty(value = "质量检验内容")
    private String qualityCheckContent;

    @ApiModelProperty(value = "框架协议过期")
    private Integer frameExpired;

    @ApiModelProperty(value = "是否是ldc模板")
    private Integer ldcFrame;

    @ApiModelProperty(value = "模板协议(0 无,订单 1有,大合同)")
    private Integer frameProtocol;

    @ApiModelProperty(value = "协议生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date protocolStartDate;

    @ApiModelProperty(value = "协议过期日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date protocolEndDate;

    @ApiModelProperty(value = "协议号")
    private String protocolNo;

    @ApiModelProperty(value = "发票id")
    private Integer invoiceId;

    @ApiModelProperty(value = "发票名称")
    private String invoiceName;

    @ApiModelProperty(value = "迟提货/收货    默认载入2元/天/吨；")
    private BigDecimal deliveryDelayFine = new BigDecimal(2);

    @ApiModelProperty(value = "滞期费默认载入   1 元/天/吨的滞期费")
    private BigDecimal demurrageFine = BigDecimal.ONE;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "是否是白名单 (0:不是 1:是)")
    private Integer isWhiteList;

    @ApiModelProperty(value = "反点价服务 (0:不是 1:是)")
    private Integer isReversePrice;

    @ApiModelProperty(value = "结构化定价服务 (0:不是 1:是)")
    private Integer isStructure;

    @ApiModelProperty(value = "评级分数")
    private String gradeScore;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "主体ID")
    private Integer companyId;

    @ApiModelProperty(value = "主体ID")
    private String companyName;

    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "二级品类名称")
    @TableField(exist = false)
    private String category2Name;

    @TableField(exist = false)
    private Map<Integer, List<Integer>> categoryMap;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @ApiModelProperty(value = "三级品类名称")
    @TableField(exist = false)
    private String category3Name;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;
}
