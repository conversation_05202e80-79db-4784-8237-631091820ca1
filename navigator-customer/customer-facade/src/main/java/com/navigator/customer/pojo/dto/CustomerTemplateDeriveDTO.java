package com.navigator.customer.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/26 10:04
 */

@Data
public class CustomerTemplateDeriveDTO {

    @Excel(name = "客户签订地", orderNum = "1", width = 15)
    private String signPlace;

    @Excel(name = "客户简称", orderNum = "2", width = 35)
    private String shortName;

    @Excel(name = "客户状态", orderNum = "3", width = 15)
    private String status;

    @Excel(name = "是否有授权书", orderNum = "4", width = 15)
    private String isAuthorization;

    @Excel(name = "是否是供应商", orderNum = "5", width = 15)
    private String isSupplier;

    @Excel(name = "是否是集团客户", orderNum = "6", width = 15)
    private String enterprise;

    @Excel(name = "客户编码", orderNum = "7", width = 15)
    private String linkageCustomerCode;

    @Excel(name = "客户名称", orderNum = "8", width = 35)
    private String customerName;

    @Excel(name = "助记符", orderNum = "9", width = 20)
    private String customerIndexesName;

    @Excel(name = "集团客户名称", orderNum = "10", width = 35)
    private String enterpriseName;

    @Excel(name = "集团客户编码", orderNum = "11", width = 15)
    private String enterpriseCode;

    @Excel(name = "全称", orderNum = "12", width = 35)
    private String fullName;

    @Excel(name = "全称(英文)", orderNum = "13", width = 35)
    private String fullNameEnglish;

    @Excel(name = "地址", orderNum = "14", width = 45)
    private String address;

    @ApiModelProperty(value = "错误原因")
    private String mistakeCause;


}
