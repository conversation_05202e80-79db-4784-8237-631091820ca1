package com.navigator.customer.pojo.dto.mdm;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/24
 */

@Data
public class MDMCustomerDataDTO {

    //传入参数
    private String requestData;

    //客户编码(MDM_ID)
    private String counterpartyID;
    //英文全称
    private String fullNameEnglish;
    //中文全称
    private String name;
    //客户状态
    private String tradeStatus;
    //是否是供应商
    private List<CategoryDTO> categoryList;
    //地址
    private List<AddressDTO> addressList;
    private String bankDetailsList;

}
