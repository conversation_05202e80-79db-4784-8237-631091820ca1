package com.navigator.customer.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDTO;
import com.navigator.customer.pojo.dto.SystemAndCustomerDTO;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.qo.CustomerQO;
import com.navigator.customer.pojo.vo.CustomerTemplateVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:42
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerFacade {
    /**
     * 分页查询出客户客户信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryCustomerList")
    Result queryCustomerList(@RequestBody QueryDTO<CustomerDTO> queryDTO);

    /**
     * 根据客户id 品种查询客户主体及配置
     *
     * @param id
     * @return
     */
    @GetMapping("/queryCustomerByIdCategoryId")
    CustomerDTO queryCustomerByIdCategoryId(@RequestParam(value = "id") Integer id,
                                            @RequestParam(value = "categoryId") Integer categoryId);

    /**
     * 根据id查询出客户信息（详细信息及其配置,联系方式等）
     *
     * @param id
     * @return
     */
    @GetMapping("/getCustomerById")
    CustomerDTO getCustomerById(@RequestParam(value = "id") Integer id);


    @GetMapping("/getCustomerBizInfo")
    CustomerDTO getCustomerBizInfo(@RequestParam(value = "id") Integer id,
                                   @RequestParam(value = "category2") Integer category2,
                                   @RequestParam(value = "category3") Integer category3,
                                   @RequestParam(value = "siteId") String siteCode,
                                   @RequestParam(value = "companyId") Integer companyId,
                                   @RequestParam(value = "factoryCode") String factoryCode);


    /**
     * 根据id查询客户信息 只返回客户属性 不返回客户配置
     *
     * @param id
     * @return
     */
    @GetMapping("/queryCustomerById")
    CustomerEntity queryCustomerById(@RequestParam(value = "id") Integer id);

    /**
     * 根据id查询出客户信息（详细信息）
     *
     * @param earlyWarning
     * @return
     */
    @GetMapping("/getCustomerEarlyWarning")
    List<Integer> getCustomerEarlyWarning(@RequestParam(value = "earlyWarning") Integer earlyWarning);


    /**
     * 查询客户供应商信息
     *
     * @return
     */
    @PostMapping("/queryCustomerSupplierAll")
    Result queryCustomerSupplierAll(@RequestBody QueryDTO<CustomerDTO> queryDTO);

    /**
     * 查询客户供应商信息
     *
     * @return
     */
    @GetMapping("/queryCustomerAll")
    Result queryCustomerAll();


    /**
     * 根据客户id查询客户是否易企签,是否实名
     *
     * @param id
     * @return
     */
    @GetMapping("/customerSignature")
    Integer customerSignature(@RequestParam(value = "id") Integer id) throws Exception;


    /**
     * 查询供应商
     *
     * @param isSupplier
     * @return
     */
    @GetMapping("/queryIsSupplierList")
    Result querySupplierList(@RequestParam(value = "isSupplier") Integer isSupplier);


    /**
     * 根据客户id 查询出客户信息  系统及账号
     *
     * @param customerId
     * @return
     */
    @GetMapping("/getSystemAndCustomerById")
    Result getSystemAndCustomerById(@RequestParam(value = "customerId") Integer customerId);


    /**
     * 系统及账号提交
     *
     * @param systemAndCustomerDTO
     * @return
     */
    @PostMapping("/updateSystemAndCustomer")
    Result updateSystemAndCustomer(@RequestBody SystemAndCustomerDTO systemAndCustomerDTO);


    /**
     * 更新客户信息  补充客户信息
     *
     * @param customerDTO
     * @return
     */
    @PostMapping("/replenishCustomerMessage")
    Result replenishCustomerMessage(@RequestBody CustomerDTO customerDTO);

    /**
     * 合同模板 通知人 信息查询
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    @GetMapping("/queryTemplateContactFactoryByCustomerId")
    CustomerTemplateVO queryTemplateContactFactoryByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                                               @RequestParam(value = "categoryId") Integer categoryId);

    /**
     * 修改合同模板 通知人 信息
     *
     * @param customerTemplateDTO
     * @return
     */
    @PostMapping("/updateTemplateContactFactory")
    Result updateTemplateContactFactory(@RequestBody CustomerTemplateDTO customerTemplateDTO);

    /**
     * 导入数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/saveCustomerConfig", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result saveCustomerConfig(@RequestPart("file") MultipartFile file);


    /**
     * 校验客户数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/verifyFileCustomerConfig", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result verifyFileCustomerConfig(@RequestPart("file") MultipartFile file);


    /**
     * 导入客户主数据接口
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importDataCustomerFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importDataCustomerFile(@RequestPart("file") MultipartFile file);

    /**
     * 根据客户id查询 客户是否使用系统,是否使用易企签,是否实名  易企签通用配置是否启用
     *
     * @param customerId
     * @return
     */
    @GetMapping(value = "/customerSignParameter")
    Result customerSignParameter(@RequestParam("customerId") Integer customerId);


    /**
     * 查询客户所有配置信息
     *
     * @return
     */
    @PostMapping(value = "/queryCustomerAllMessage")
    CustomerDTO queryCustomerAllMessage(@RequestBody CustomerAllMessageDTO customerAllMessageDTO);


    /**
     * 根据linkage客户编码查询客户信息
     *
     * @param linkageCustomerCode
     * @return
     */
    @GetMapping(value = "/queryCustomerByLinkageCode")
    CustomerEntity queryCustomerByLinkageCode(@RequestParam("linkageCustomerCode") String linkageCustomerCode);

    @GetMapping(value = "/customerJudge")
    Boolean customerJudge(@RequestParam("beforeCustomerId") Integer beforeCustomerId, @RequestParam("afterCustomerId") Integer afterCustomerId);

    /**
     * 查询客户联系人信息
     *
     * @param customerAllMessageDTO
     * @return
     */
    @PostMapping(value = "/queryCustomerContactAllMessage")
    CustomerDTO queryCustomerContactAllMessage(@RequestBody CustomerAllMessageDTO customerAllMessageDTO);

    @GetMapping(value = "/updateCustomerParentId")
    Integer updateCustomerParentId();


    @GetMapping("/queryCustomerByIdList")
    Result queryCustomerByIdList(@RequestParam(value = "idList") List<Integer> idList);

    @GetMapping("/querySonCustomerList")
    Result querySonCustomerList(@RequestParam(value = "customerId") Integer customerId);

    @GetMapping("/deleteCustomerById")
    Result deleteCustomerById(@RequestParam(value = "customerId") Integer customerId);

    @GetMapping("/queryCustomerByCompanyAndFactory")
    CustomerEntity queryCustomerByCompanyAndFactory(@RequestParam(value = "factoryId") Integer factoryId, @RequestParam(value = "companyId") Integer companyId);

    @GetMapping("/queryCustomerByCompanyAndLDC")
    CustomerEntity queryCustomerByCompanyAndLDC(@RequestParam(value = "companyId") Integer companyId);

    @GetMapping("/queryFactoryCustomer")
    List<CustomerEntity> queryFactoryCustomer();

    @GetMapping("/getLdcSupplierByName")
    CustomerEntity getLdcSupplierByName(@RequestParam("supplierName") String supplierName);

    @GetMapping("/getCustomerListByCode")
    List<CustomerEntity> getCustomerListByCode(@RequestParam("customerCodeList") List<String> customerCodeList);

    @GetMapping("/getCustomerListByTemplateVipCode")
    List<CustomerEntity> getCustomerListByTemplateVipCode(@RequestParam("templateVipCode") String templateVipCode);

    @GetMapping("/updateCustomerTemplateVip")
    Boolean updateCustomerTemplateVip(@RequestParam("templateVipCode") String templateVipCode,
                                      @RequestParam("customerCodeList") List<String> customerCodeList);

    @GetMapping("/getCustomerByCompanyId")
    List<CustomerEntity> getCustomerByCompanyId(@RequestParam(value = "companyId", required = false) Integer companyId,
                                                @RequestParam(value = "isLdc", required = false) Integer isLdc);

    @GetMapping("/getCustomerResidualRiskInfo")
    CrisGlobalEntity getCustomerResidualRiskInfo(@RequestParam("customerId") Integer customerId);


    /**
     * 系统及账号提交
     *
     * @param customerEntity
     * @return
     */
    @PostMapping("/updateCustomer")
    Result updateCustomer(@RequestBody CustomerEntity customerEntity);


    @PostMapping("/addCustomer")
    boolean addCustomer(@RequestBody CustomerEntity customerEntity);

    /**
     * 根据条件：获取客户列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取客户列表")
    @PostMapping("/customer/queryCustomerList")
    List<CustomerEntity> queryCustomerList(@RequestBody CustomerQO condition);

    @GetMapping("/updateCustomerTradeStatus")
    void updateCustomerTradeStatus();

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 start
    @ApiOperation(value = "根据集团客户名称获取客户id")
    @GetMapping("/getCustomerIdsByEnterpriseName")
    List<Integer> getCustomerIdsByEnterpriseName(@RequestParam("enterpriseName") String enterpriseName);
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 end
}
