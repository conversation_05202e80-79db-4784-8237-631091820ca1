package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 16:49
 */
@Getter
public enum FrameProtocolEnum {
    /**
     * 模板协议(0 无,订单 1有,LDC模版-大合同)
     */
    ORDER(0,"无，订单"),
    CONTRACT(1,"有，大合同");

    Integer value;
    String description;

    FrameProtocolEnum(Integer value, String description) {
        this.value = value;
        this.description = description;

    }
}
