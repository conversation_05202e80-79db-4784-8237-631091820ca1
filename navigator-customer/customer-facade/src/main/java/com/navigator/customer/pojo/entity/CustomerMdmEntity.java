package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/28
 */

@Data
@Accessors(chain = true)
@TableName("dba_customer_mdm")
@ApiModel(value = "CustomerInvoiceEntity对象", description = "客户发发票类型表")
public class CustomerMdmEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "请求参数")
    private String requiredData;

    @ApiModelProperty(value = "响应参数")
    private String responseDate;

    @ApiModelProperty(value = "客户编码")
    private String linkageCustomerCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String requiredTime;
}
