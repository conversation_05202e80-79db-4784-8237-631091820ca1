package com.navigator.customer.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 14:36
 */
@Data
@Accessors(chain = true)
public class CustomerDepositRateVO {

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "是否是销售 (0:否 1:是)")
    private Integer isSales;

    @ApiModelProperty(value = "是否是采购 (0:否 1:是)")
    private Integer isProcurement;

    @ApiModelProperty(value = "是否启用(1启用0禁用)默认启用")
    private Integer status;

    @ApiModelProperty(value = "1:一口价 2:基差合同 3:暂定价合同 4:基差暂定价合同")
    private Integer contractType;

    @ApiModelProperty(value = "履约保证金点价后补缴比例")
    private Integer pricingDepositRate;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "主体ID")
    private String companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyShortName;

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    private Map<Integer, List<Integer>> categoryMap;
}
