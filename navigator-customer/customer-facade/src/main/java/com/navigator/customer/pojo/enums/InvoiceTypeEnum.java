package com.navigator.customer.pojo.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/1 14:41
 */
@Getter
public enum InvoiceTypeEnum {
    NORMAL_ELECTRONIC(1, "增值税普通发票,电子"),
    NORMAL_PAPER(2, "增值税普通发票,纸质"),
    SPECIAL(3, "增值税专用发票,电子"),
    SPECIAL_PAPER(4, "增值税专用发票,纸质"),
    NORMAL_ALL_ELECTRONIC(5, "增值税普通发票,全电"),
    SPECIAL_ALL_ELECTRONIC(6, "增值税专用发票,全电"),
    ;

    Integer value;
    String desc;

    InvoiceTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static InvoiceTypeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(invoiceTypeEnum -> invoiceTypeEnum.getValue().equals(value))
                .findFirst()
                .orElse(NORMAL_ELECTRONIC);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
