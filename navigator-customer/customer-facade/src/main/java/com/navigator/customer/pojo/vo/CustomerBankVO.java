package com.navigator.customer.pojo.vo;

import com.navigator.customer.pojo.entity.BankFactoryEntity;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/14 15:54
 */
@Data
@Accessors(chain = true)
public class CustomerBankVO extends CustomerBankEntity {

    @ApiModelProperty(value = "主体名称")
    private String companyShortName;

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    private Map<Integer, List<Integer>> categoryMap;

    private List<BankFactoryEntity> bankFactoryEntities;

}
