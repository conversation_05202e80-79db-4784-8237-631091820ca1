package com.navigator.customer.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/23 16:34
 */
@Data
@Accessors(chain = true)
public class CustomerAllMessageDTO {

    //客户id
    private Integer customerId;
    //品种id
    private Integer categoryId;
    private String category1;
    //二级品类
    private String category2;
    //三级品类
    private String category3;
    //工厂id
    private Integer factoryId;
    //工厂code
    private String factoryCode;
    //采销类型
    private Integer salesType;
    //主体id
    private Integer companyId;

    private List<Integer> category3List;
}
