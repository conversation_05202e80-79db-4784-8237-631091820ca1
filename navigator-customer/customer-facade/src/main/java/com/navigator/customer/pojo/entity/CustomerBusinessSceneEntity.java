package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/31
 */

@Data
@Accessors(chain = true)
@TableName("dba_customer_business_scene")
@ApiModel(value = "CustomerBusinessSceneEntity对象", description = "客户业务场景关联表")
public class CustomerBusinessSceneEntity {

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "业务场景类型")
    private Integer bizType;

    @ApiModelProperty(value = "业务场景id")
    private Integer bizId;

    @ApiModelProperty(value = "关联类型")
    private Integer relationType;

    @ApiModelProperty(value = "绑定编码")
    private String bindCode;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

}
