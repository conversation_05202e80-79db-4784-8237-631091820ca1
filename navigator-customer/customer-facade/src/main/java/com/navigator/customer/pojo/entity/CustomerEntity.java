package com.navigator.customer.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.qo.CustomerQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dba_customer")
@ApiModel(value = "Customer对象", description = "客户表")
public class CustomerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "linkage客户编码")
    @Excel(name = "客户编码", orderNum = "2", width = 20)
    private String linkageCustomerCode;

    @ApiModelProperty(value = "其他平台客户编码")
    private String mdmCustomerCode;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "是否使用易企签")
    private Integer useYqq;

    @ApiModelProperty(value = "是否使用 Columbus 系统")
    private Integer isColumbus;

    @ApiModelProperty(value = "框架协议是否过期")
    private Integer frameExpired;

    @ApiModelProperty(value = "客户模板协议是否过期")
    private Integer templateExpired;

    @ApiModelProperty(value = "客户是否需要正本")
    private Integer originalPaper;

    @ApiModelProperty(value = "正本需求")
    private String paperNeed;

    @ApiModelProperty(value = "客户类型 1:公司 2个人")
    private Integer type;

    @ApiModelProperty(value = "等级")
    private String grade;

    @ApiModelProperty(value = "客户名称")
    @Excel(name = "客户名称", orderNum = "1", width = 30)
    private String name;

    @ApiModelProperty(value = "全称")
    private String fullName;

    @ApiModelProperty(value = "简称")
    private String shortName;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "控制预付款上限")
    private BigDecimal largestAdvance;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "客户发票类型")
    private Integer invoiceType;

    @ApiModelProperty(value = "客户发票名称")
    private String invoiceName;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "客户地址")
    private String address;

    @ApiModelProperty(value = "允许调拨销售额度")
    private Integer moving;

    @ApiModelProperty(value = "允许CNF销售额度")
    private Integer cnfSelling;

    @ApiModelProperty(value = "允许信用销售额度")
    private Integer creditSelling;

    @ApiModelProperty(value = "是否为集团客户  0:否 1:是")
    private Integer enterprise;

    @ApiModelProperty(value = "客户集团名称")
    private String enterpriseName;

    @ApiModelProperty(value = "集团客户编码")
    private String enterpriseCustomerCode;

    @ApiModelProperty(value = "模板特殊客户组编码（取集团客户编码）")
    private String templateVipCode;

    @ApiModelProperty(value = "经度")
    private String lng;

    @ApiModelProperty(value = "纬度")
    private String lat;

    @ApiModelProperty(value = "AX代码")
    private String axCode;

    @ApiModelProperty(value = "交易属性（1、payment term 2、CBT 3、NTP 4、active）")
    private String tradeType;

    @ApiModelProperty(value = "客户状态")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "是否是客户 1:是 0:否")
    private Integer isCustomer;

    @ApiModelProperty(value = "是否供应商 1:是 0:否")
    private Integer isSupplier;

    @ApiModelProperty(value = "签订地")
    private String signPlace;

    @ApiModelProperty(value = "客户编号")
    private String code;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "是否是 LDC 0:否 1:是")
    private Integer isLdc;

    @ApiModelProperty(value = "客户公司id")
    private Integer companyId;

    @ApiModelProperty(value = "开票地址")
    private String invoiceAddress;

    @ApiModelProperty(value = "税号")
    private String taxNo;

    @ApiModelProperty(value = "linkage数据id")
    private String siteId;

    @ApiModelProperty(value = "活跃度")
    private Integer active;

    @ApiModelProperty(value = "助记符")
    private String customerIndexesName;

    @ApiModelProperty(value = "K3索引")
    private String kIndexes;

    @ApiModelProperty(value = "客户类别")
    private Integer customerType;

    @ApiModelProperty(value = "区")
    private String district;

    @ApiModelProperty(value = "全称(英文)")
    private String fullNameEnglish;

    @ApiModelProperty(value = "FAX")
    private String fax;

    @ApiModelProperty(value = "邮编")
    private String postcode;

    @ApiModelProperty(value = "发票ID序号")
    private Integer invoiceIdNo;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "终端客户")
    private Integer terminalCustomer;

    @ApiModelProperty(value = "经销商")
    private Integer dealer;

    @ApiModelProperty(value = "商务客户")
    private String businessCustomer;

    @ApiModelProperty(value = "信用销售最大额度")
    private BigDecimal theLimit;

    @ApiModelProperty(value = "共享集团授信")
    private Integer shareEnterpriseCredit;

    @ApiModelProperty(value = "Trading Limit")
    private BigDecimal tradingLimit;

    @ApiModelProperty(value = "默认到达港口")
    private String defaultPort;

    @ApiModelProperty(value = "默认运输时间")
    private Date defaultTransportTime;

    @ApiModelProperty(value = "到达后联系人")
    private String arriveContact;

    @ApiModelProperty(value = "到达后联系电话")
    private String arrivePhone;

    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNo;

    @ApiModelProperty(value = "开票要求")
    private String invoiceRequire;

    @ApiModelProperty(value = "开票电话")
    private String invoicePhone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "主页")
    private String homepage;

    @ApiModelProperty(value = "电子发票接收短信手机")
    private String electronicInvoicePhone;

    @ApiModelProperty(value = "电子发票接收邮件")
    private String electronicInvoiceEmail;

    @ApiModelProperty(value = "Categorylist")
    private String categoryList;

    @ApiModelProperty(value = "业务员")
    private String bizEmploy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "备注")
    @TableField(exist = false)
    private String meno;

    @ApiModelProperty(value = "是否允许集团签章")
    @TableField(exist = false)
    private Integer isEnterpriseSign;

    @ApiModelProperty(value = "模板特殊集团客户")
    @TableField(exist = false)
    private String templateVipName;

    @ApiModelProperty(value = "工厂id")
    private Integer factoryId;

    @ApiModelProperty(value = "是否有授权书")
    private Integer isAuthorization;

    @ApiModelProperty(value = "交易状态")
    private String tradeStatus;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<CustomerEntity> lqw(CustomerQO condition) {
        LambdaQueryWrapper<CustomerEntity> lqw = new LambdaQueryWrapper<CustomerEntity>().eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getLinkageCustomerCode()), CustomerEntity::getLinkageCustomerCode, condition.getLinkageCustomerCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getName()), CustomerEntity::getName, condition.getName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsLdc()), CustomerEntity::getIsLdc, condition.getIsLdc());
        }
        lqw.orderByDesc(CustomerEntity::getId);
        return lqw;
    }
}
