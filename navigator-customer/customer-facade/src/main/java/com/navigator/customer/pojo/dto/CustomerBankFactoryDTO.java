package com.navigator.customer.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CustomerBankFactoryDTO {
    //工厂code
    private String factoryCode;
    //工厂id
    private Integer factoryId;
    //客户id
    private Integer customerId;
    //品类id
    private Integer categoryId;
    //品类1
    private String category1;
    //品类2
    private String category2;
    //品类3
    private String category3;
    //使用类型 0:收款 1:付款 2:收付组合
    private Integer useType;
    //主体ID
    private String companyId;

}
