package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/30 18:48
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerBankFacade {

    /**
     * 根据客户id查询账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/queryBankByCustomerId")
    Result queryBankByCustomerId(@RequestBody CustomerBankDTO customerBankDTO);


    /**
     * 根据客户id查询出客户银行账户信息
     *
     * @param customerId
     * @param useType
     * @return
     */
    @GetMapping("/queryCustomerBankByCustomerId")
    Result queryCustomerBankByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                         @RequestParam(value = "useType") Integer useType);


    /**
     * 根据客户id查询出客户银行账户信息
     *
     * @param
     * @return
     */
    @PostMapping("/queryCustomerBankFactory")
    Result queryCustomerBankFactory(@RequestBody CustomerBankFactoryDTO customerBankFactoryDTO);


    /**
     * 根据银行账号查询账户信息
     *
     * @param
     * @return
     */
    @PostMapping("/queryBankByBankAccountNo")
    CustomerBankEntity queryBankByBankAccountNo(@RequestParam(value = "bankAccountNo") String bankAccountNo);


    /**
     * 根据银行账号查询账户信息
     *
     * @param
     * @return
     */
    @GetMapping("/queryBankByBankAccountNo")
    CustomerBankEntity queryBankByBankAccountNo(@RequestParam(value = "bankAccountNo") String bankAccountNo,
                                                @RequestParam(value = "categoryId") Integer categoryId);


    /**
     * 根据id 删除账户信息
     *
     * @param id
     * @return
     */
    @GetMapping("/deleteCustomerBankById")
    Result deleteCustomerBankById(@RequestParam(value = "id") Integer id);

    /**
     * 修改账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/updateCustomerBank")
    Result updateCustomerBank(@RequestBody CustomerBankDTO customerBankDTO);


    /**
     * 添加账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/saveCustomerBank")
    Result saveCustomerBank(@RequestBody CustomerBankDTO customerBankDTO);


    /**
     * 提交保存账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/redactCustomerBank")
    Result redactCustomerBank(@RequestBody List<CustomerBankDTO> customerBankDTO);


    /**
     * 根据客户银行id 查询出银行信息
     *
     * @param id
     * @return
     */
    @PostMapping("/queryCustomerBankById")
    CustomerBankEntity queryCustomerBankById(@RequestParam(value = "id") Integer id);

    /**
     * 客户银行信息
     *
     * @param file
     * @return
     */
    @PostMapping("/leadCustomerBank")
    Result leadCustomerBank(@RequestParam(value = "file") MultipartFile file);

}
