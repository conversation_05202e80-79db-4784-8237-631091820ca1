package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 20:06
 */
@Getter
public enum ReferTypeEnum {

    CUSTOMER(1, "客户"),
    SUPPLIER(2, "供应商"),
    LDC(3,"LDC"),
    CUSTOMER_AND_SUPPLIER(4,"客户及供应商");

    Integer value;
    String desc;

    ReferTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
