package com.navigator.customer.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/14 18:26
 */
@Data
@Accessors(chain = true)
public class SystemAndCustomerVO {
    @ApiModelProperty(value = "客户id")
    private Integer customerId;
    @ApiModelProperty(value = "是否启用易企签")
    private Integer useYqq;
    @ApiModelProperty(value = "Columbus服务是否开通")
    private Integer isColumbus;
    @ApiModelProperty(value = "账号id")
    private Integer employId;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "修改状态 0:禁止修改 1,可以修改")
    private Integer modifyStatus;
}
