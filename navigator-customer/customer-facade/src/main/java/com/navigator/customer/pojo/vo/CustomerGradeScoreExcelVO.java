package com.navigator.customer.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CustomerGradeScoreVO对象", description = "客户评分表")
public class CustomerGradeScoreExcelVO implements Serializable {

    @Excel(name = "客户编码", orderNum = "1")
    String customerCode;

    @Excel(name = "客户名称", orderNum = "2")
    String customerName;

    @Excel(name = "客户集团名称", orderNum = "3")
    String enterpriseName;

    @Excel(name = "一级品类", orderNum = "4")
    String category1;

    @Excel(name = "二级品类", orderNum = "5")
    String category2;

    @Excel(name = "品种", orderNum = "6")
    String category3;

    @Excel(name = "客户评级")
    private String gradeScore;
}
