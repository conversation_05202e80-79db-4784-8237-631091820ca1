package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 17:53
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface ContactFactoryFacade {


    /**
     * 根据客户联系人查询出联系人适用工厂
     *
     * @param contactId
     * @return
     */
    @GetMapping("/queryContactFactoryByContactId")
    Result queryContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId);


    /**
     * 删除油厂和客户的关系
     *
     * @param contactId
     * @return
     */
    @GetMapping("/deleteContactFactoryByContactId")
    Result deleteContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId);

    /**
     * 根据客户和联系人信息查询出关系
     *
     * @param contactId
     * @param factoryId
     * @return
     */
    @GetMapping("getContactFactoryByContactId")
    Result getContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId,
                                        @RequestParam(value = "factoryId") Integer factoryId);


}
