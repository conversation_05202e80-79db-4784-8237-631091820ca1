package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.bo.CustomerBankBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface UpdateCustomerFacade {

    /**
     * 模板属性编辑
     *
     * @param customerProtocolDTO
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerProtocolFactory")
    Result saveOrUpdateCustomerProtocolFactory(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    /**
     * 通知人编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerNotice")
    Result saveOrUpdateContactEntity(@RequestBody ContactDTO contactDTO);

    /**
     * 正本编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerOriginalPaper")
    Result saveOrUpdateCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    /**
     * 预约保证金编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerDepositRate")
    Result saveOrUpdateCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO);

    /**
     * 客户白名单编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerWhiteList")
    Result saveOrUpdateCustomerWhiteList(@RequestBody CustomerDetailDTO customerWhiteListDTO);

    /**
     * 账户信息编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerBank")
    Result saveOrUpdateCustomerBank(@RequestBody CustomerBankBO customerBankBO);

    /**
     * 发票类型编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerInvoiceType")
    Result saveOrUpdateCustomerInvoiceType(@RequestBody CustomerInvoiceEntity customerInvoiceEntity);

    /**
     * 赊销预付编辑
     *
     * @return
     */
    @PostMapping("/saveOrCustomerCreditPayment")
    Result saveOrCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO);


    /**
     * 评级编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerGradeScore")
    Result saveOrUpdateCustomerGradeScore(@RequestBody CustomerGradeScoreEntity customerGradeScoreEntity);


    /**
     * 新增编辑客户提货白名单
     *
     * @param customerDeliveryWhiteEntity
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerDeliveryWhite")
    Result saveOrUpdateCustomerDeliveryWhite(@RequestBody CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity);
}
