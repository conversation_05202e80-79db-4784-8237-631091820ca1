package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.entity.CustomerBankEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/30 18:53
 */
@Data
public class CustomerBankDTO extends CustomerBankEntity {

    private List<ApplyFactory> applyFactories;

    //工厂
    @Data
    @Accessors(chain = true)
    public static class ApplyFactory{
        //工厂id
        private Integer factoryId;
        //工厂简称
        private String shortName;
    }
}
