package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/8 18:46
 */
@Data
public class PaymentDaysExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "所属主体")
    private String companyName;

    @Excel(name = "付款方式")
    private String paymentType;

    @Excel(name = "赊销账期")
    private String creditDays;

    @Excel(name = "销售")
    private String isSales;

    @Excel(name = "采购")
    private String isProcurement;

    @Excel(name = "业务类型")
    private String buCode;
}
