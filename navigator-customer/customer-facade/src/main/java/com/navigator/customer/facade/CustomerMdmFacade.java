package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.mdm.MDMObjectDataDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/11
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerMdmFacade {

    /**
     * 对接MDM接口
     *
     * @param mdmObjectDataDTO
     * @return
     */
    @PostMapping(value = "/customer/saveOrUpdateMDMCustomer")
    Result saveOrUpdateMDMCustomer(@RequestBody MDMObjectDataDTO mdmObjectDataDTO);
}
