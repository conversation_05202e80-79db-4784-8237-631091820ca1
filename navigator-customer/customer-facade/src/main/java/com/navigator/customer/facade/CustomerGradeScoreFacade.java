package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerGradeScoreFacade {

    /**
     * 查询客户评级
     *
     * @param customerGradeScoreDTO
     * @return
     */
    @PostMapping("/queryCustomerGradeScoreList")
    List<CustomerGradeScoreEntity> queryCustomerGradeScoreList(@RequestBody CustomerGradeScoreDTO customerGradeScoreDTO);


    /**
     * 客户评级导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importCustomerGrade", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importCustomerGrade(@RequestPart("file") MultipartFile file);
}
