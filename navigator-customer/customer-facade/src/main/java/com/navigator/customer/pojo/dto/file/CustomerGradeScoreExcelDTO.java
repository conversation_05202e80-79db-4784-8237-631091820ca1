package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CustomerGradeScoreExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "品类编号")
    private String categoryCode;

    @Excel(name = "客户评级")
    private String gradeScore;

    @Excel(name = "主体")
    private String companyNames;

    @Excel(name = "更新/新增/删除")
    private String editType;
}
