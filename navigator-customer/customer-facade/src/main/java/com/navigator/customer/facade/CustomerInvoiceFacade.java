package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerInvoiceFacade {

    @PostMapping("/queryCustomerInvoiceList")
    List<CustomerInvoiceEntity> queryCustomerInvoiceList(@RequestBody CustomerInvoiceDTO customerInvoiceDTO);

    @GetMapping("/updateCustomerInvoice")
    void updateCustomerInvoiceId();

    /**
     * 发票配置导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importCustomerInvoice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importCustomerInvoice(@RequestPart("file") MultipartFile file);

}
