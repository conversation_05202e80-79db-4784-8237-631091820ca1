package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商（油厂）表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
@Data
@Accessors(chain = true)
@TableName("dba_supplier")
@ApiModel(value="SupplierEntity对象", description="供应商（油厂）表")
public class SupplierEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "linkinage客户编码")
    private String linkageSupplierCode;

    @ApiModelProperty(value = "其他平台客户编码")
    private String mdmSupplierCode;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "是否油厂 1:是油厂 2:不是油厂")
    private Integer isFactory;

    @ApiModelProperty(value = "客户代码")
    private String code;

    @ApiModelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "供应商地址")
    private String address;

    @ApiModelProperty(value = "签订地")
    private String signPlace;

    @ApiModelProperty(value = "是否禁用 0禁用 1启用")
    private Integer status;

    @ApiModelProperty(value = "是否删除(1:启用 0:删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;


}
