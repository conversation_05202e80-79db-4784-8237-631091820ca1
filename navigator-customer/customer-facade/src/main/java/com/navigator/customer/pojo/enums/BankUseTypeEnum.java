package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/4 14:43
 */

@Getter
public enum  BankUseTypeEnum {

    COLLECTION(0, "收款账号"),
    PAYMENT(1, "付款账户"),
    COLLECTION_PAYMENT(2, "收付组合");

    Integer value;
    String description;

    BankUseTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }


    public static BankUseTypeEnum getByValue(Integer value) {
        for (BankUseTypeEnum bankUseTypeEnum : BankUseTypeEnum.values()) {
            if (bankUseTypeEnum.getValue().equals(value)) {
                return bankUseTypeEnum;
            }
        }
        return COLLECTION;
    }

    public static BankUseTypeEnum getByDescription(String description) {
        for (BankUseTypeEnum bankUseTypeEnum : BankUseTypeEnum.values()) {
            if (bankUseTypeEnum.getDescription().equals(description)) {
                return bankUseTypeEnum;
            }
        }
        return COLLECTION;
    }
}
