package com.navigator.customer.pojo.vo;

import com.navigator.customer.pojo.dto.CustomerTemplateDeriveDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/26 11:36
 */
@Data
@Accessors(chain = true)
public class CustomerVerifyVO {

    //校验的错误数据
    private List<CustomerTemplateDeriveDTO> customerTemplateDeriveDTOS;
    //总数
    private Integer allNum;
    //校验错误数量
    private Integer errorNum;

}
