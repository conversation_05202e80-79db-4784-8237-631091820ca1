package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/7 15:26
 */
@Getter
public enum CustomerSignatureEnum {

    NOT_USE_YQQ(0, "不用易企签"),
    NOT_AUTH(1, "未实名"),
    AUTH(2, "已经实名");

    Integer value;
    String description;

    CustomerSignatureEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
