package com.navigator.customer.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/11
 */

@Data
@Accessors(chain = true)
public class CustomerDeliveryWhiteExcelDTO {

    @Excel(name = "客户名称", orderNum = "1")
    private String customerName;

    @Excel(name = "客户编码", orderNum = "2")
    private String customerCode;

    @Excel(name = "二级品类", orderNum = "4")
    private String category2Name;

    @Excel(name = "品种", orderNum = "5")
    private String category3Name;

    @Excel(name = "提货白名单状态", orderNum = "6")
    private String deliveryWhiteStatus;

    //失败原因
    private String failReason;

    //行数
    private Integer index;

    public boolean isAllEqual() {
        return StringUtils.isEmpty(customerName)
                && StringUtils.isEmpty(customerCode)
                && StringUtils.isEmpty(category2Name)
                && StringUtils.isEmpty(category3Name)
                && StringUtils.isEmpty(deliveryWhiteStatus)
                ;
    }
}
