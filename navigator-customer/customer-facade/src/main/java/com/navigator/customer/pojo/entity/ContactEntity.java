package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户联系人表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dba_contact")
@ApiModel(value = "Contact对象", description = "客户联系人表")
public class ContactEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户/供应商Id")
    private String referId;

    @ApiModelProperty(value = "类型（1 客户 2供应商 4 客户及供应商）")
    private Integer referType;

    @ApiModelProperty(value = "供应商类型/LDC为0。 其它类型0系统默认全部")
    private Integer supplierId;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "易企签是否实名认证")
    private Integer yqqAuth;

    @ApiModelProperty(value = "1法人 2合同联系人 3签章联系人")
    private Integer contactType;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系手机")
    private String contactPhone;

    @ApiModelProperty(value = "电话")
    private String specialPlane;

    @ApiModelProperty(value = "传真号码")
    private String faxPhone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;


    @ApiModelProperty(value = "逻辑删除  0:未删除 1:删除")
    private Integer isDeleted;


}
