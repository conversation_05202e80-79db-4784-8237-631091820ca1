package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_factory_warehouse")
@ApiModel(value = "FactoryWarehouseEntity对象", description = "")
public class FactoryWarehouseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品种id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "油厂id")
//    @Excel(name = "品种id")
    private Integer factoryId;

    @ApiModelProperty(value = "油厂Code")
    private String factoryCode;

    @ApiModelProperty(value = "LKG库点代码")
    private String lkgWarehouseCode;

    @ApiModelProperty(value = "发货库点名称")
    private String name;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "交货地点")
    private String deliveryPoint;

    @ApiModelProperty(value = "发货库点的ATLAS编码")
    private String atlasWarehouseCode;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否禁用 0禁用 1启用")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "是否删除(0:启用 1:删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "修改人")
    private Integer updatedBy;

    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String updator;
}
