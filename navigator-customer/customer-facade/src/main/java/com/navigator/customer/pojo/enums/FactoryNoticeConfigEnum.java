package com.navigator.customer.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Get<PERSON>
@AllArgsConstructor
public enum FactoryNoticeConfigEnum {

    //TODO NEO @WAN 说明
    // 后续需要放到配置表里

    ZJG_MEAL("ZJG", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    ZJG_OIL("ZJG", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    //ready
    ZZJ_MEAL("ZZJ", 11, "<EMAIL>",
            "<EMAIL>;<PERSON><PERSON><PERSON>@ldc.com;<PERSON><PERSON>@ldc.com;<PERSON><PERSON><EMAIL>;<PERSON><PERSON>@LDC.com;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    ZZJ_OIL("ZZJ", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    ZZY_MEAL("ZZY", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    ZZY_OIL("ZZY", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    DG_MEAL("DG", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    DG_OIL("DG", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    TJ_MEAL("TJ", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    TJ_OIL("TJ", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    //ready
    LY_MEAL("LY", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    LY_OIL("LY", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    TJIB_MEAL("TJIB", 11, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),
    TJIB_OIL("TJIB", 12, "<EMAIL>",
            "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;","<EMAIL>","LDC中国"),

    ;


    //测试抄送人数据

//    ZJG_MEAL("ZJG", 11, "<EMAIL>",
//                     "<EMAIL>;"),
//    ZJG_OIL("ZJG", 12, "<EMAIL>",
//                    "<EMAIL>;"),
//    //ready
//    ZZJ_MEAL("ZZJ", 11, "<EMAIL>",
//                     "<EMAIL>;"),
//    ZZJ_OIL("ZZJ", 12, "<EMAIL>",
//                    "<EMAIL>;"),
//    ZZY_MEAL("ZZY", 11, "<EMAIL>",
//                     "<EMAIL>;"),
//    ZZY_OIL("ZZY", 12, "<EMAIL>",
//                    "<EMAIL>;"),
//    DG_MEAL("DG", 11, "<EMAIL>",
//                    "<EMAIL>;"),
//    DG_OIL("DG", 12, "<EMAIL>",
//                   "<EMAIL>;"),
//    TJ_MEAL("TJ", 11, "<EMAIL>",
//                    "<EMAIL>;"),
//    TJ_OIL("TJ", 12, "<EMAIL>",
//                   "<EMAIL>;"),
//    //ready
//    LY_MEAL("LY", 11, "<EMAIL>",
//                    "<EMAIL>;"),
//    LY_OIL("LY", 12, "<EMAIL>",
//                   "<EMAIL>;"),
//    TJIB_MEAL("TJIB", 11, "<EMAIL>",
//                      "<EMAIL>;"),
//    TJIB_OIL("TJIB", 12, "<EMAIL>",
//                     "<EMAIL>;"),

    ;


    String factoryCode;
    Integer goodsCategoryId;
    String replyTo;
    String ccList;
    String senderMail;
    String senderName;

    public static FactoryNoticeConfigEnum getConfig(String factoryCode, Integer goodsCategoryId) {
        try {
            for (FactoryNoticeConfigEnum en : FactoryNoticeConfigEnum.values()) {
                if (factoryCode.equals(en.getFactoryCode())
                        && goodsCategoryId.equals(en.goodsCategoryId)) {
                    return en;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


}
