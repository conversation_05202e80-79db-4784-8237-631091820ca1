package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:52
 */
@Getter
public enum ContactTypeEnum {

    LEGAL_PERSON(1, "法人"),
    CONTRACT_CONTACT(2, "合同联系人"),
    SIGNATURE_CONTACT(3, "签章联系人");

    Integer value;
    String description;

    ContactTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
