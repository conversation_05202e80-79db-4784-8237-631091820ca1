package com.navigator.customer.pojo.dto;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CustomerSignParameterDTO {

    //客户id
    private Integer customerId;
    //是否使用易企签
    private Integer useYqq;
    //是否使用 Columbus 系统
    private Integer isColumbus;
    //易企签是否实名  0:未实名 1:已实名
    private Integer yqqAuth;
    //易企签通用配置转台
    private Integer yqqStatus;
}
