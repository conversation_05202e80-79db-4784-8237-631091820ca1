package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/27 19:00
 */
@Data
@Accessors(chain = true)
public class CustomerTemplateExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "主体")
    private String companyNames;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "采销类型")
    private String saleType;

    @Excel(name = "框架协议")
    private String frameProtocol;

    @Excel(name = "框架协议编号")
    private String protocolNo;

    @Excel(name = "有效开始日期")
    private Date protocolStartDate;

    @Excel(name = "有效结束日期")
    private Date protocolEndDate;
}
