package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/05/11 10:21
 */

@Data
@Accessors(chain = true)
public class CustomerDepositRateFileDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String linkageCustomerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "所属主体")
    private String companyName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "业务类型")
    private String buCode;

    @Excel(name = "合同类型")
    private String contractType;

    @Excel(name = "采销类型")
    private String saleType;

    @Excel(name = "状态")
    private String status;

    @Excel(name = "履约保证金")
    private String depositRate;

    @Excel(name = "履约保证金比例后补缴")
    private String pricingDepositRate;

    @Excel(name = "发票后补缴货款比例")
    private String invoicePaymentRate;

    @Excel(name = "操作类型")
    private String operationType;
}