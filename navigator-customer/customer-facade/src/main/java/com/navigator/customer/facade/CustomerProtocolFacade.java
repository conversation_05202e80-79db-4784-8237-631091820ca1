package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerProtocolFacade {
    @PostMapping("/queryCustomerProtocolList")
    Result queryCustomerProtocolList(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    @PostMapping("/saveCustomerProtocol")
    Result saveCustomerProtocol(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    @PostMapping("/queryCustomerProtocolEntity")
    CustomerProtocolEntity queryCustomerProtocolEntity(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    @PostMapping("/updateCustomerProtocol")
    Result updateCustomerProtocol(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    @PostMapping("/queryCustomerProtocolSign")
    Result queryCustomerProtocolSign(@RequestBody CustomerProtocolDTO customerProtocolDTO);

    /**
     * 模板导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importCustomerProtocol", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importCustomerProtocol(@RequestPart("file") MultipartFile file);

}
