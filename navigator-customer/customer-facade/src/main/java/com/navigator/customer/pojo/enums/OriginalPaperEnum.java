package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 19:44
 */
@Getter
public enum OriginalPaperEnum {

    NOT_ORIGINAL_PAPER(0, "", "不需要正本"),
    ORIGINAL_PAPER(1, "正本(客户)", "客户需要正本"),
    EXCEED_EXPECT_ORIGINAL_PAPER(2, "超期超限", "超期超限(需正本)"),
    LDC_ORIGINAL_PAPER(3, "正本(LDC)", "LDC(需要正本) non-frame"),

    ;

    Integer value;
    String name;
    String description;

    OriginalPaperEnum(Integer value, String name,String description) {
        this.value = value;
        this.description = description;
        this.name = name;

    }


    public static OriginalPaperEnum getByValue(Integer value) {
        for (OriginalPaperEnum en : OriginalPaperEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return OriginalPaperEnum.NOT_ORIGINAL_PAPER;
    }
}
