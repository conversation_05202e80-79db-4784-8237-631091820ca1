package com.navigator.customer.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Data
@Accessors(chain = true)
public class CustomerQO {

    @ApiModelProperty(value = "客户名称")
    private String name;

    @ApiModelProperty(value = "linkage客户编码")
    private String linkageCustomerCode;

    @ApiModelProperty(value = "是否是 LDC 0:否 1:是")
    private Integer isLdc;

}
