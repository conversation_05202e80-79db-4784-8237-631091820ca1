package com.navigator.customer.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2022-03-17 11:52
 */
@Getter
@AllArgsConstructor
public enum BelongAreaEnum {
    /**
     * 油厂所属区域
     */
    CHINA_SOUTHERN(1, "华南"),
    CHINA_CENTRAL(2, "华中"),
    CHINA_EAST(3, "华东"),
    CHINA_NORTHEAST(4, "东北"),
    CHINA_SOUTHWEST(5, "西南"),
    CHINA_NORTHWEST(6, "西北");
    Integer value;
    String description;

    public static BelongAreaEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(belongAreaEnum -> value == belongAreaEnum.getValue())
                .findFirst()
                .orElse(CHINA_SOUTHERN);
    }

}
