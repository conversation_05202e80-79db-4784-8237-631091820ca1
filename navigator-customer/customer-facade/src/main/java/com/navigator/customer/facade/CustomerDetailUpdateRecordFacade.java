package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/14
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerDetailUpdateRecordFacade {


    /**
     * 查询客户配置修改信息
     *
     * @param customerDetailUpdateRecordDTO
     * @return
     */
    @PostMapping("/detailUpdateSelect")
    Result detailUpdateSelect(@RequestBody CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO);


    /**
     * 记录客户配置修改
     *
     * @param customerDetailUpdateRecordEntity
     * @return
     */
    @PostMapping("/saveCustomerDetailUpdateRecord")
    Result saveCustomerDetailUpdateRecord(@RequestBody CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity);

}
