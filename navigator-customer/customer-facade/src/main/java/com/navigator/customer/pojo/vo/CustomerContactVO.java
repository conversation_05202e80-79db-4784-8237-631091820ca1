package com.navigator.customer.pojo.vo;

import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 18:10
 */
@Data
@Accessors(chain = true)
public class CustomerContactVO extends ContactEntity {

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    @ApiModelProperty(value = "品种名称")
    private String category3Name;

    private Map<Integer, List<Integer>> categoryMap;

    //同志工厂绑定关系
    List<ContactFactoryEntity> contactFactoryEntities;

    private Integer customerId;
}
