package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerOriginalPaperFacade {

    @PostMapping("/queryCustomerOriginalPaperList")
    Result queryCustomerOriginalPaperList(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping("/saveCustomerOriginalPaper")
    Result saveCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping("/updateCustomerOriginalPaperStatus")
    Result updateCustomerOriginalPaperStatus(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping("/updateCustomerOriginalPaper")
    Result updateCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping("/queryCustomerOriginalPaperEntity")
    CustomerOriginalPaperEntity queryCustomerOriginalPaperEntity(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping("/queryCustomerNonFrame")
    Integer queryCustomerNonFrame(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO);

    @PostMapping(value = "/importCustomer",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importCustomer(@RequestPart("file") MultipartFile file);
}
