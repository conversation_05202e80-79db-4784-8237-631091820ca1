package com.navigator.customer.pojo.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 19:53
 */
@Getter
public enum EarlyWarningEnum {
    /**
     * 预警类型-客户属性
     */
    FRAME_EXPIRED(1, "框架协议过期"),
    USE_WS(2, "使用WS"),
    NOT_USE_WS(3, "非WS但系统"),
    NOT_SYSTEM(4, "非系统"),
    ORIGINAL_PAPER(5, "需要正本(客户)"),
    LDC_ORIGINAL_PAPER(6, "LDC(需要正本) non-frame"),
    TEMPLATE_EXPIRED(7, "模板协议过期"),
    TEMPLATE_NOT_EXPIRED(7, "模板协议未过期")
    ;

    Integer value;
    String description;

    EarlyWarningEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public static EarlyWarningEnum getByValue(Integer value) {
        for (EarlyWarningEnum en : EarlyWarningEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return FRAME_EXPIRED;
    }

    public static EarlyWarningEnum getByValue(Integer value1, Integer value2) {
        for (EarlyWarningEnum en : EarlyWarningEnum.values()) {
            if (value1 == 1 && value2 == 1){
                return USE_WS;
            }
            if (value1 == 0 && value2 == 1){
                return NOT_USE_WS;
            }
            if (value1 == 0 && value2 == 0){
                return NOT_SYSTEM;
            }
        }
        return NOT_SYSTEM;
    }

    /**
     * 合同正本信息
     *
     * @return 集合
     */
    public static List<Integer> getContractPaperType() {
        return Arrays.asList(ORIGINAL_PAPER.getValue(), LDC_ORIGINAL_PAPER.getValue());
    }
}
