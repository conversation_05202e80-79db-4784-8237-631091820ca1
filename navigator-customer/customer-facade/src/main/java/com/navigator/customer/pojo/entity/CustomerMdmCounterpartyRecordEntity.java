package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/24
 */
@Data
@Accessors(chain = true)
@TableName("dba_customer_mdm_counterparty_record")
@ApiModel(value = "CustomerMdmCounterpartyRecordEntity对象", description = "MDM对接表")
public class CustomerMdmCounterpartyRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户编码")
    private String counterpartyId;

    @ApiModelProperty(value = "请求参数")
    private String requestData;

    @ApiModelProperty(value = "地址")
    private String addressList;

    @ApiModelProperty(value = "账户信息")
    private String bankDetailsList;

    @ApiModelProperty(value = "是否是供应商")
    private String categoryList;

    @ApiModelProperty(value = "修改前客户主数据")
    private String beforeData;

    @ApiModelProperty(value = "修改后客户主数据")
    private String afterData;

    @ApiModelProperty(value = "相应参数")
    private String content;

    @ApiModelProperty(value = "相应参数")
    private String responseData;

    @ApiModelProperty(value = "请求时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;

    @ApiModelProperty(value = "0:update;1:insert")
    private Integer operationType;


}
