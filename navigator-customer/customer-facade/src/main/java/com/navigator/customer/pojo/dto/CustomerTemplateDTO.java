package com.navigator.customer.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 18:08
 */
@Data
@Accessors(chain = true)
public class CustomerTemplateDTO {

    //客户id
    private Integer customerId;
    //是否是ldc模板 0：nonFrame 1:是
    private Integer ldcFrame;
    //是否有框架协议 0: 无 1:有
    private Integer frameProtocol;
    //框架协议编号
    private String protocolNo;
    //协议签订日期
    private String protocolStartDate;
    //协议结束日期
    private String protocolEndDate;
    //通知人
    private List<ContactFactory> contactFactories;
    //是否需要正本
    private Integer originalPaper;
    //品类id
    private Integer categoryId;

    private ContactFactory contactFactory;

    private ApplyFactory applyFactory;

    private String detailCode;

    //通知人
    @Data
    @Accessors(chain = true)
    public static class ContactFactory {
        //联系人id
        private Integer contactId;
        //收件人
        private String contactName;
        //邮箱
        private String email;
        //手机号码
        private String contactPhone;
        //电话号码
        private String specialPlane;
        //联系人地址
        private String address;
        //1:销售 2:采购 4:销售/采购
        private Integer referType;
        //适用工厂
        List<ApplyFactory> applyFactories;

    }

    //通知人 负责工厂
    @Data
    @Accessors(chain = true)
    public static class ApplyFactory {
        //工厂id
        private Integer factoryId;
        //工厂简称
        private String shortName;
    }

}
