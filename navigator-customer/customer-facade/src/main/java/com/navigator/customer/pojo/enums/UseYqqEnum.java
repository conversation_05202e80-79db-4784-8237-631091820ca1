package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 13:40
 */
@Getter
public enum UseYqqEnum {

    NOT_USE_YQQ(0,"非WS但系统"),
    USE_YQQ(1 ,"使用WS"),
    NOT_SYSTEM(2 ,"非系统");


    Integer value;
    String description;

    UseYqqEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public static UseYqqEnum getByValue(Integer value) {
        for (UseYqqEnum en : UseYqqEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return UseYqqEnum.NOT_USE_YQQ;
    }
}
