package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/26
 */

@Data
@Accessors(chain = true)
public class CustomerInvoiceExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "所属主体")
    private String companyCode;

    @Excel(name = "客户编码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "发票类型")
    private String invoiceType;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "操作类型")
    private String operationType;
}
