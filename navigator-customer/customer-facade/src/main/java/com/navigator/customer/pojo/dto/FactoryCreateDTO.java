package com.navigator.customer.pojo.dto;

import com.navigator.customer.pojo.enums.BelongAreaEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-17 14:34
 */
@Data
public class FactoryCreateDTO {
    /**
     * <AUTHOR>
     * @since 2022-03-17 14:34
     */
    /**
     * 工厂ID
     */
    private Integer id;
    /**
     * 工厂名称
     */
    private String name;
    /**
     * 工厂简称
     */
    private String shortName;
    /**
     * 所属区域
     * {@link BelongAreaEnum}
     */
    private Integer belongArea;

    private List<Integer> companyIdList;

}
