package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/8 10:22
 */
@Data
public class ContactExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "工厂编号")
    private String factoryCode;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "公司名称")
    private String customerName;

    @Excel(name = "客户代码")
    private String linkageCustomerCode;

    @Excel(name = "联系人")
    private String contactName;

    @Excel(name = "采销类型")
    private String salesType;

    @Excel(name = "电话号码")
    private String contactPhone;

    @Excel(name = "邮箱")
    private String email;

    @Excel(name = "地址")
    private String address;
}
