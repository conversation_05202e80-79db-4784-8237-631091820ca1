package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.vo.CustomerDeliveryWhiteFileReturnVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerDeliveryWhiteFacade {

    /**
     * 根据条件查询数据
     *
     * @param customerDeliveryWhiteDTO
     * @return
     */
    @PostMapping("/queryCustomerDeliveryWhite")
    List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhite(@RequestBody CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO);


    /**
     * 根据条件查询列表
     *
     * @param customerDeliveryWhiteDTO
     * @return
     */
    @PostMapping("/queryCustomerDeliveryWhiteList")
    List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhiteList(@RequestBody CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO);

    /**
     * 下载客户提货委托白名单
     *
     * @return
     */
    @PostMapping("/downloadCustomerWhitelist")
    List<CustomerDeliveryWhiteExcelDTO> downloadCustomerWhitelist(@RequestBody CustomerDTO customerDTO);

    /**
     * 客户提货委托白名单上传校验
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/checkCustomerWhitelist", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result checkCustomerWhitelist(@RequestPart(value = "file") MultipartFile file);

    /**
     * 客户提货委托白名单excel上传
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadCustomerWhitelist", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result uploadCustomerWhitelist(@RequestPart(value = "file") MultipartFile file);
}
