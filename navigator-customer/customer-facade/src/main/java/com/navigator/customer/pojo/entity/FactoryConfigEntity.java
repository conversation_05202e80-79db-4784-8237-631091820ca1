package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Data
@Accessors(chain = true)
@TableName("dba_factory_config")
@ApiModel(value="FactoryConfigEntity对象", description="")
public class FactoryConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "简称")
    private String shortName;

    @ApiModelProperty(value = "所属区域")
    private String belongArea;

    @ApiModelProperty(value = "是否删除(0:启用 1:删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date updatedAt;

    @ApiModelProperty(value = "工厂地址")
    private String address;

    @ApiModelProperty(value = "工厂地址")
    private String libraryPoint;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;


}
