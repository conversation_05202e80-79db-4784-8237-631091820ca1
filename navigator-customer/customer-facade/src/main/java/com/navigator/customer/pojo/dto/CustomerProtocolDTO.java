package com.navigator.customer.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class CustomerProtocolDTO extends CustomerProtocolEntity {
    private List<CustomerProtocolEntity> customerProtocolList;

    private String companyName;

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    @ApiModelProperty(value = "品类名称")
    private String category3Name;

    private Integer ldcFrame;

    private Map<Integer, List<Integer>> categoryMap;
}
