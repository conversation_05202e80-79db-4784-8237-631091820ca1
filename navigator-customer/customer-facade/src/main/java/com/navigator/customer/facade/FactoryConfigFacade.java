package com.navigator.customer.facade;

import com.navigator.customer.pojo.entity.FactoryConfigEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/25 17:32
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface FactoryConfigFacade {

    /**
     * 查询出货工厂
     *
     * @param customerId
     * @return
     */
    @GetMapping("/factoryConfigBySuppId")
    List<FactoryConfigEntity> factoryConfigBySuppId(@RequestParam(value = "customerId") Integer customerId);

}
