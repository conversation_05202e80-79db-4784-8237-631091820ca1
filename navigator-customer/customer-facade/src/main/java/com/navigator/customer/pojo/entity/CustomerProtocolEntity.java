package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * dba_customer_protocol
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName("dba_customer_protocol")
public class CustomerProtocolEntity implements Serializable {
    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String protocolNo;

    private Integer customerId;

    private Integer categoryId;

    private Integer companyId;

    private Integer frameProtocol;

    private Integer saleType;

    private Integer status;

    private Integer isDeleted;

    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolEndDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private Integer createdBy;

    private Integer updatedBy;

    private String createdByName;

    private String updatedByName;

    private static final long serialVersionUID = 1L;
}