package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */

@Data
@Accessors(chain = true)
@TableName("dba_customer_detail_update_record ")
@ApiModel(value = "CustomerDetailEntity对象", description = "")
public class CustomerDetailUpdateRecordEntity {

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "配置编号")
    private String detailCode;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "原数据")
    private String rawData;

    @ApiModelProperty(value = "传入参数")
    private String data;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "主体ID")
    private Integer companyId;
}
