package com.navigator.customer.pojo.bo;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CustomerDepositRateBO {

    //客户id
    private Integer customerId;
    //品类
    private Integer categoryId;
    //是否启用
    private Integer status;
    //合同类型
    private Integer contractType;
    //1:采购 2:销售
    private Integer salesType;
    //主体Id
    private String companyId;
    //一级品类
    private String category1;
    //二级品类
    private String category2;
    //三级品类
    private String category3;
    //业务线
    private String buCode;

}
