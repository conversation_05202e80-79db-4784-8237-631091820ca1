package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.vo.CustomerContactVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 14:37
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface ContactFacade {

    /**
     * 修改联系人
     *
     * @param contactEntity
     * @return
     */
    @PostMapping("/updateContactEntity")
    Integer updateContactEntity(@RequestBody ContactEntity contactEntity);

    /**
     * 添加联系人信息
     *
     * @param contactEntity
     * @return
     */
    @PostMapping("/vaseContactEntity")
    Integer vaseContactEntity(@RequestBody ContactEntity contactEntity);

    /**
     * 根据id查询信息
     *
     * @param id
     * @return
     */
    @GetMapping("/getContactEntityById")
    ContactEntity getContactEntityById(@RequestParam(value = "id") Integer id);


    @PostMapping("/queryContactFactoryList")
    List<CustomerContactVO> queryContactFactoryList(@RequestBody ContactDTO contactDTO);

    /**
     * 根据客户查询出联系人
     *
     * @param referId
     * @param categoryId
     * @param supplierId
     * @return
     */
    @GetMapping("/getContactCustomerByList")
    List<ContactEntity> getContactCustomerByList(@RequestParam(value = "referId") Integer referId,
                                                 @RequestParam(value = "categoryId") Integer categoryId,
                                                 @RequestParam(value = "supplierId") Integer supplierId);

    /**
     * 根据LDC查询出联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    @GetMapping("/getContactLDCByList")
    List<ContactEntity> getContactLDCByList(@RequestParam(value = "referId") Integer referId,
                                            @RequestParam(value = "categoryId") Integer categoryId);


    /**
     * 根据供应商/LDC查询出联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    @GetMapping("/getContactSupplierIdByList")
    List<ContactEntity> getContactSupplierIdByList(@RequestParam(value = "referId") Integer referId,
                                                   @RequestParam(value = "categoryId") Integer categoryId);


    /**
     * 查询客户签章人信息
     *
     * @param customerId
     * @return
     */
    @GetMapping("/getCustomerByCustomerIdYqq")
    ContactDTO getCustomerByCustomerIdYqq(@RequestParam(value = "customerId") Integer customerId);


    /**
     * 根据客户id查询客户联系人
     *
     * @param customerId
     * @param referType
     * @return
     */
    @GetMapping("/getCustomerByCustomerId")
    Result getCustomerByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                   @RequestParam(value = "referType") Integer referType,
                                   @RequestParam(value = "categoryId") Integer categoryId);

    /**
     * 导入客户联系人信息
     *
     * @return
     */
    @PostMapping("/leadCustomerContact")
    Result leadCustomerContact(@RequestParam(value = "file") MultipartFile file);

    @GetMapping("/contactAddress")
    Result contactAddress();
}
