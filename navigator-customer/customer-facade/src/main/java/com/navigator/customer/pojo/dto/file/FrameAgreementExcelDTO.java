package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/8 18:39
 */
@Data
public class FrameAgreementExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "采销类型")
    private String saleType;

    @Excel(name = "是否NON-Frame")
    private String ldcFrame;

    @Excel(name = "是否正本")
    private String originalPaper;
}
