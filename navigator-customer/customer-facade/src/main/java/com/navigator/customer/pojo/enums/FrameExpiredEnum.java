package com.navigator.customer.pojo.enums;

import lombok.Getter;

@Getter
public enum FrameExpiredEnum {

    NOT_PAST_DUE(0,"未过期"),
    PAST_DUE(1,"框架协议过期");

    Integer value;
    String description;

    FrameExpiredEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }


    public static FrameExpiredEnum getByValue(Integer value) {
        for (FrameExpiredEnum en : FrameExpiredEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return FrameExpiredEnum.NOT_PAST_DUE;
    }
}
