package com.navigator.customer.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/05/11 10:21
 */

@Data
@Accessors(chain = true)
public class CustomerCreditDaysDTO {

    @Excel(name = "客户代码")
    private String linkageCustomerCode;

    @Excel(name = "付款方式")
    private String paymentType;

    @Excel(name = "账期")
    private Integer creditDays;

    @Excel(name = "品种")
    private String goodsCategory;

    @Excel(name = "客户名称")
    private String customerName;

}