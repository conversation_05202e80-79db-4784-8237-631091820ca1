package com.navigator.customer.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR> NaNa
 * @since : 2022-05-11 9:28
 **/
@Data
public class FactoryWarehouseLeadDTO {
    @Excel(name = "品种code")
    private String categoryCode;

    @Excel(name = "油厂Code")
    private String factoryCode;

    @Excel(name = "LKG编码")
    private String lkgWarehouseCode;

    @Excel(name = "发货库点名称")
    private String name;

    @Excel(name = "地址")
    private String address;

    @Excel(name = "交货地点")
    private String deliveryPoint;
}
