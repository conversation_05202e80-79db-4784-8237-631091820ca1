package com.navigator.customer.facade;

import com.navigator.customer.pojo.entity.FactoryEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@Component
@FeignClient(name = "navigator-customer-service")
public interface FactoryFacade {

    @GetMapping("/getFactoryEntityByCode")
    FactoryEntity getFactoryByCode(@RequestParam(value = "factoryCode") String factoryCode);

    @GetMapping("/getAllFactoryList")
    List<FactoryEntity> getAllFactoryList(@RequestParam(value = "status", required = false) Integer status);

    @GetMapping("/getAllFactoryBySyncSystem")
    List<FactoryEntity> getAllFactoryBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem);
}
