package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.vo.CustomerGradeScoreExcelVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/20 17:06
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerDetailFacade {

    /**
     * 根据客户和品类查询客户配置
     *
     * @param customerId
     * @param categoryId 非必传
     * @return
     */
    @GetMapping("/queryCustomerDetailList")
    CustomerDetailEntity queryCustomerDetailList(@RequestParam(value = "customerId") Integer customerId,
                                                 @RequestParam(value = "categoryId", required = false) Integer categoryId);


    //根据条件查询客户白名单列表
    @PostMapping("/queryCustomerDetailListByCondition")
    List<CustomerDetailEntity> queryCustomerDetailListByCondition(@RequestBody CustomerDetailBO customerDetailBO);

    /**
     * 新增客户配置
     *
     * @param customerDetailDTO
     * @return
     */
    @ApiOperation(value = "旧接口，不适用")
    @PostMapping("/saveCustomerDetail")
    Integer saveCustomerDetail(@RequestBody CustomerDetailDTO customerDetailDTO);

    /**
     * 修改客户配置信息
     *
     * @param customerDetailBO
     * @return
     */
    @PostMapping("/updateCustomerDetail")
    Integer updateCustomerDetail(@RequestBody CustomerDetailBO customerDetailBO);

    /**
     * 客户被名单导入
     *
     * @param file
     * @return
     */
    @PostMapping("/addCustomerDetailIsWhiteList")
    Result addCustomerDetailIsWhiteList(@RequestParam(value = "file") MultipartFile file);

    /**
     * 客户白名单
     *
     * @param file
     * @return
     */
    @PostMapping("/whiteListCustomerTemplate")
    Result whiteListCustomerTemplate(@RequestParam(value = "file") MultipartFile file);

    /**
     * 查询发票信息
     *
     * @param
     * @return
     */
    @ApiOperation(value = "旧接口，不适用")
    @GetMapping("/queryCustomerInvoiceList")
    List<CustomerDetailEntity> queryCustomerInvoiceList(@RequestParam(value = "customerId") Integer customerId,
                                                        @RequestParam(value = "categoryId", required = false) Integer categoryId);
    @ApiOperation(value = "旧接口，不适用")
    @PostMapping("/saveCustomerInvoice")
    Result saveCustomerInvoice(@RequestBody CustomerDetailBO customerDetailBO);

    @ApiOperation(value = "旧接口，不适用")
    @PostMapping("/modifyCustomerInvoice")
    Result modifyCustomerInvoice(@RequestBody CustomerDetailBO customerDetailBO);

    @GetMapping("/queryCustomerDetailEntity")
    CustomerDetailEntity queryCustomerDetailEntity(@RequestParam(value = "customerId") Integer customerId,
                                                   @RequestParam(value = "category3") Integer category3);

    @GetMapping("/copyCustomerInvoice")
    Result copyCustomerInvoice();

    @PostMapping(value = "/uploadGradeScoreTemplate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result uploadGradeScoreTemplate(@RequestPart("file") MultipartFile file);


    @PostMapping(value = "/exportGradeScoreList")
    List<CustomerTemplateDeriveGradeScoreDTO> exportGradeScoreList(@RequestBody CustomerDTO customerDTO);

    @PostMapping(value = "/importGradeScoreExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importGradeScoreExcel(@RequestPart("file") MultipartFile file);
}
