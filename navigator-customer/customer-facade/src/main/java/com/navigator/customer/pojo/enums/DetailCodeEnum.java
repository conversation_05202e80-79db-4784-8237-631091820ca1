package com.navigator.customer.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */

@Getter
public enum DetailCodeEnum {

    CUSTOMER_MASTER("10001", "客户主数据"),
    SYSTEM_ACCOUNT("10002", "系统及账号"),
    TEMPLATE_CONTACT("20001", "合同模板"),
    EARNEST_MONEY("20002","保证金比例"),
    WHITE_LIST("20003", "白名单"),
    BANK("20004", "账户信息"),
    INVOICE("20005", "发票类型"),
    PREPAY_SELL_ON_CREDIT("20006", "赊销预付"),
    GRADE_DETAIL("20007", "赊销预付");

    String value;
    String description;

    DetailCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DetailCodeEnum getByValue(String value) {
        for (DetailCodeEnum en : DetailCodeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return CUSTOMER_MASTER;
    }
}
