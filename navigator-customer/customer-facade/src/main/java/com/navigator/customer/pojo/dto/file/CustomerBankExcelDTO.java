package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/8 18:52
 */
@Data
public class CustomerBankExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "工厂编码")
    private String factoryCode;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "供应商名称")
    private String customerName;

    @Excel(name = "客户代码")
    private String linkageCustomerCode;

    @Excel(name = "开户名")
    private String bankAccountName;

    @Excel(name = "开户行")
    private String bankName;

    @Excel(name = "银行卡账号")
    private String bankAccountNo;

    @Excel(name = "所属主体")
    private String companyNames;

    @Excel(name = "使用类型")
    private String useType;
}
