package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/26
 */
@Data
@Accessors(chain = true)
public class CustomerWhiteExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "三级品类")
    private String category3;

    @Excel(name = "转月服务")
    private String isWhiteList;

    @Excel(name = "反点价服务")
    private String isReversePrice;

    @Excel(name = "结构化定价服务")
    private String isStructure;
}
