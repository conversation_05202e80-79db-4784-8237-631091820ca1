package com.navigator.customer.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-25 16:31
 **/

@Getter
@AllArgsConstructor
public enum TTCustomerTradeStatusEnum {
    /**
     * tt客户RR-ResidualRisk剩余风险-交易状态
     */
    PENDING("PENDING", "PENDING"),
    CBT("CBT", "CBT-ConsultBeforeTrade"),
    NO_TRADE("NO_TRADE", "NO_TRADE"),
    INACTIVE("INACTIVE", "INACTIVE"),
    ACTIVE("ACTIVE", "ACTIVE"),
    ;

    String value;
    String desc;


    public static TTCustomerTradeStatusEnum getEnumByValue(String value) {
        return Arrays.stream(values())
                .filter(customerTradeStatusEnum -> value.equals(customerTradeStatusEnum.getValue()))
                .findFirst()
                .orElse(PENDING);
    }

    public static List<String> getJudgeRrNumTradeStatusList() {
        return Arrays.asList(CBT.getValue(), INACTIVE.getValue(), ACTIVE.getValue());
    }

    public static List<String> getCommonTradeStatusList() {
        return Arrays.asList(CBT.getValue(), NO_TRADE.getValue(), INACTIVE.getValue(), ACTIVE.getValue());
    }

}
