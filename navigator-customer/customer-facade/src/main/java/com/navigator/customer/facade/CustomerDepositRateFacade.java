package com.navigator.customer.facade;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 18:15
 */
@FeignClient(name = "navigator-customer-service")
@Component
public interface CustomerDepositRateFacade {

    /**
     * 根据客户id 履约保证金比例
     *
     * @param customerDepositRateBO
     * @return
     */
    @PostMapping("/getCustomerDepositRateByCustomerId")
    Result getCustomerDepositRateByCustomerId(@RequestBody CustomerDepositRateBO customerDepositRateBO);

    /**
     * TT新增查询客保证金
     *
     * @param customerDepositRateBO
     * @return
     */
    @PostMapping("/getCustomerDepositRateAddTT")
    Result getCustomerDepositRateAddTT(@RequestBody CustomerDepositRateBO customerDepositRateBO);

    /**
     * 根据id 履约保证金比例
     *
     * @return
     */
    @GetMapping("/getCustomerDepositRateById")
    CustomerDepositRateEntity getCustomerDepositRateById(@RequestParam(value = "id") Integer id);

    /**
     * 添加客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/saveCustomerDepositRate")
    Result saveCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO);


    /**
     * 履约保证金 状态修改
     *
     * @param id
     * @return
     */
    @GetMapping("/updateCustomerDepositRateStatus")
    Result updateCustomerDepositRateStatus(@RequestParam(value = "id") Integer id);


    /**
     * 修改客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/updateCustomerDepositRate")
    Result updateCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO);


    /**
     * 删除客户 履约保证金 比例
     *
     * @param customerDepositRateId
     * @return
     */
    @GetMapping("/deleteCustomerDepositRate")
    Result deleteCustomerDepositRate(@RequestParam(value = "customerDepositRateId") Integer customerDepositRateId);

    /**
     * 编辑提交客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/redactCustomerDepositRate")
    Result redactCustomerDepositRate(@RequestBody List<CustomerDepositRateDTO> customerDepositRateDTO);

    /**
     * 客户保证金比例销售
     *
     * @param file
     * @return
     */
    @PostMapping("/addCustomerSalesDepositRate")
    Result addCustomerSalesDepositRate(@RequestParam(value = "file") MultipartFile file);

    /**
     * 客户履约保证金比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/queryCustomerDepositRate")
    List<CustomerDepositRateEntity> queryCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO);

}
