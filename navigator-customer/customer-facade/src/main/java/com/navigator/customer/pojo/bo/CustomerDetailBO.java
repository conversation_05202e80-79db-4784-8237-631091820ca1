package com.navigator.customer.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/20 11:34
 */
@Data
@Accessors(chain = true)
public class CustomerDetailBO {

    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    //一级品类
    private String category1;

    //二级品类
    private String category2;

    //三级品类
    private String category3;

    @ApiModelProperty(value = "质量检验标准")
    private String qualityCheckRule;

    @ApiModelProperty(value = "质量检验名称（默认是通用标准）")
    private String qualityCheckName;

    @ApiModelProperty(value = "质量检验内容")
    private String qualityCheckContent;

    @ApiModelProperty(value = "框架协议过期")
    private Integer frameExpired;

    @ApiModelProperty(value = "是否是ldc模板 0：nonframe 1:LDC模版")
    private Integer ldcFrame;

    @ApiModelProperty(value = "模板协议(0 无 1有)")
    private Integer frameProtocol;

    @ApiModelProperty(value = "协议生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolStartDate;

    @ApiModelProperty(value = "协议过期日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date protocolEndDate;

    @ApiModelProperty(value = "协议号")
    private String protocolNo;

    @ApiModelProperty(value = "迟提货/收货    默认载入2元/天/吨；")
    private Double deliveryDelayFine;

    @ApiModelProperty(value = "滞期费默认载入   1 元/天/吨的滞期费")
    private Double demurrageFine;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "发票id")
    private Integer invoiceId;

    @ApiModelProperty(value = "发票名称")
    private String invoiceName;

    @ApiModelProperty(value = "是否是白名单 (0:不是 1:是)")
    private Integer isWhiteList;

    @ApiModelProperty(value = "反点价服务 (0:不是 1:是)")
    private Integer isReversePrice;

    @ApiModelProperty(value = "结构化定价服务 (0:不是 1:是)")
    private Integer isStructure;

    @ApiModelProperty(value = "客户评级")
    private String gradeScore;

    private String detailCode;

    private Integer companyId;

}
