package com.navigator.customer.pojo.vo;

import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/11
 */

@Data
@Accessors(chain = true)
public class CustomerDeliveryWhiteFileReturnVO {

    //成功数量
    private Integer successNum;
    //失败数量
    private Integer failNum;
    //总数
    private Integer totalNum;
    //重复
    private Boolean isRepeat = false;
    //返回列
    private List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteDTOList;
}
