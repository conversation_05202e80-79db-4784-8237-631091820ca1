package com.navigator.customer.pojo.dto.file;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/12
 */

@Data
@Accessors(chain = true)
public class CustomerProtocolExcelDTO {

    @Excel(name = "id")
    private String id;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "公司名称")
    private String customerName;

    @Excel(name = "一级品类")
    private String category1;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "品种")
    private String category3;

    @Excel(name = "协议编号")
    private String protocolNo;

    @Excel(name = "模板协议")
    private String frameProtocol;

    @Excel(name = "采销类型")
    private String saleType;

    @Excel(name = "协议生效日期")
    private String protocolStartDate;

    @Excel(name = "协议过期日期")
    private String protocolEndDate;

    @Excel(name = "所属主体")
    private String companyCode;

}
