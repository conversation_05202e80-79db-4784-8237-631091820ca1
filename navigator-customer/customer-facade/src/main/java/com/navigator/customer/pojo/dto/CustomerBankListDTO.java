package com.navigator.customer.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */

@Data
@Accessors(chain = true)
public class CustomerBankListDTO {

    //银行信息
    private List<CustomerBankDTO> customerBankDTOS;

    //客户Id
    private Integer customerId;

    //品种id
    private Integer categoryId;

    //配置编号
    private String detailCode;
}
