package com.navigator.customer.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.navigator.customer.pojo.enums.TTCustomerTradeStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-18 17:03
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("[BI].[CRIS]")
@ApiModel(value = "CrisGlobalEntity对象", description = "")
public class CrisGlobalEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField("CREDIT_REGION")
    private String creditRegion;

    @TableField("CRISID")
    private String crisid;

    @TableField("CRIS_NAME")
    private String crisName;

    @TableField("GRADE_CODE")
    private String gradeCode;

    /**
     * {@link TTCustomerTradeStatusEnum}
     */
    @TableField("TRADE_STATUS")
    private String tradeStatus;

    @TableField("GROUP_ID")
    private String groupId;

    @TableField("LIMIT_OPEN_CREDIT")
    private String limitOpenCredit;

    /**
     * RR Residue
     */
    @TableField("CP_ID")
    private String cpId;

    /**
     * RR Limit(剩余风险限额)
     */
    @TableField("LIMIT_RESIDUAL_RISK")
    private String limitResidualRisk;

    @TableField(exist = false)
    private BigDecimal residualRiskLimit;

    /**
     * RR Usage(剩余风险使用)
     */
    @TableField("GLOBAL_PEAK_RESIDUAL_TOTAL")
    private String globalPeakResidualTotal;

    @TableField(exist = false)
    private BigDecimal residualRiskUsage;

    /**
     * RR Residue（剩余风险剩余）
     * RR_Residue = LIMIT_RESIDUAL_RISK - GLOBAL_PEAK_RESIDUAL_TOTAL
     */
    @TableField("RR_RESIDUE")
    private String rrResidue;

    @TableField(exist = false)
    private BigDecimal residualRiskResidue;

    @TableField("CURRENT_OPEN_CREDIT")
    private String currentOpenCredit;

    @TableField("COB_DATE")
    private String cobDate;

}
