package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.dao.CustomerDao;
import com.navigator.customer.dao.CustomerDepositRateDao;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.dto.file.CustomerDepositRateFileDTO;
import com.navigator.customer.pojo.dto.file.PaymentDaysExcelDTO;
import com.navigator.customer.pojo.entity.CustomerCreditPaymentEntity;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerDepositRateVO;
import com.navigator.customer.service.ICustomerDepositRateService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/14 11:46
 */
@Service
@Slf4j
public class CustomerDepositRateServiceImpl implements ICustomerDepositRateService {


    @Autowired
    private CustomerDepositRateDao customerDepositRateDao;
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;

    private static final int BATCH_SIZE = 1000; // 每个批次的大小

    @Override
    public List<CustomerDepositRateVO> getCustomerDepositRateByCustomerId(CustomerDepositRateBO customerDepositRateBO) {

        List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateDao.getCustomerDepositRateByCustomerId(customerDepositRateBO);

        List<CustomerDepositRateVO> customerDepositRateVOS = BeanConvertUtils.convert2List(CustomerDepositRateVO.class, customerDepositRateEntities);
        if (!customerDepositRateVOS.isEmpty()) {

            for (CustomerDepositRateVO customerDepositRateVO : customerDepositRateVOS) {
                //拼接主体简称
                StringBuilder shortName = new StringBuilder();

                if (StringUtils.isNotBlank(customerDepositRateVO.getCompanyId())) {
                    String[] companyIds = customerDepositRateVO.getCompanyId().split(",");
                    int size = companyIds.length;
                    int index = 0;
                    for (String companyId : companyIds) {
                        CompanyEntity companyEntity = companyFacade.queryCompanyById(Integer.parseInt(companyId));
                        index++;
                        shortName.append(companyEntity.getShortName());
                        if (size != index) {
                            shortName.append(",");
                        }
                    }

                    customerDepositRateVO.setCompanyShortName(shortName.toString());
                }

                StringBuilder category2Name = new StringBuilder();
                if (StringUtils.isNotBlank(customerDepositRateVO.getCategory2())) {
                    List<Integer> category2List = Arrays.stream(customerDepositRateVO.getCategory2().split(","))
                            .map(String::trim)
                            .filter(category2 -> !category2.isEmpty())
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                    for (Integer category2 : category2List) {
                        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                        if(null != categoryEntity){
                            category2Name.append(categoryEntity.getName());
                            //判断是否是最后一个
                            if (!category2.equals(category2List.get(category2List.size() - 1))) {
                                category2Name.append(",");
                            }
                        }
                    }
                    customerDepositRateVO.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, null));
                }
                customerDepositRateVO
                        .setCategory1(customerDepositRateVO.getCategory1().replaceAll("^,+|,+$", ""))
                        .setCategory2(customerDepositRateVO.getCategory2().replaceAll("^,+|,+$", ""))
                        .setCategory2Name(category2Name.toString());

            }

        }

        return customerDepositRateVOS;
    }

    @Override
    public List<CustomerDepositRateVO> getCustomerDepositRateAddTT(CustomerDepositRateBO customerDepositRateBO) {
        return BeanConvertUtils.convert2List(CustomerDepositRateVO.class, customerDepositRateDao.getTTDepositRateByCustomerId(customerDepositRateBO));
    }

    @Override
    public CustomerDepositRateEntity getCustomerDepositRateById(Integer id) {
        return customerDepositRateDao.getById(id);
    }

    @Override
    public boolean saveCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO, String currentUserId, String name) {

        /*if (!customerDepositRateDao.getDepositByVerify(customerDepositRateDTO).isEmpty()) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_DEPOSIT_RATE_EXIST);
        }*/

        CustomerDepositRateEntity customerDepositRate = BeanConvertUtils.convert(CustomerDepositRateEntity.class, customerDepositRateDTO);
        customerDepositRate.setUpdatedAt(new Date())
                .setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(currentUserId))
                .setCreatedByName(name)
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;

//        OperationActionEnum operationActionEnum;
//        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(customerDepositRate.getCategoryId())) {
//            operationActionEnum = OperationActionEnum.SBM_REDACT_CUSTOMER_DEPOSIT_RATE;
//        } else {
//            operationActionEnum = OperationActionEnum.SBO_REDACT_CUSTOMER_DEPOSIT_RATE;
//        }
//        try {
//
//            CustomerDepositRateEntity original = new CustomerDepositRateEntity();
//            BeanUtils.copyProperties(customerDepositRate, original);
//
//            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
//            recordOperationDetail
//                    .setDtoData(JSON.toJSONString(customerDepositRateDTO))
//                    .setBeforeData(JSON.toJSONString(original))
//                    .setAfterData(JSON.toJSONString(customerDepositRate))
//                    .setOperationActionEnum(operationActionEnum)
//            ;
//            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        return customerDepositRateDao.save(customerDepositRate);
    }

    @Override
    public boolean updateCustomerDepositRateStatus(Integer id) {

        CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateDao.getById(id);
        CustomerDepositRateEntity originalCustomerDepositRateEntity = new CustomerDepositRateEntity();
        BeanUtils.copyProperties(customerDepositRateEntity, originalCustomerDepositRateEntity);
        if (customerDepositRateEntity.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
            customerDepositRateEntity.setStatus(DisableStatusEnum.ENABLE.getValue());
        } else {
            customerDepositRateEntity.setStatus(DisableStatusEnum.DISABLE.getValue());
        }
        customerDepositRateEntity.setUpdatedAt(new Date());
        String currentUserId = JwtUtils.getCurrentUserId();
        EmployEntity employ = employFacade.getEmployById(Integer.parseInt(currentUserId));
        customerDepositRateEntity.setUpdatedBy(employ.getId())
                .setCreatedByName(employ.getName())
                .setUpdatedByName(employ.getName())
                .setUpdatedAt(new Date());
        boolean status = customerDepositRateDao.updateById(customerDepositRateEntity);


        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(id))
                    .setBeforeData(JSON.toJSONString(originalCustomerDepositRateEntity))
                    .setAfterData(JSON.toJSONString(customerDepositRateEntity));
            if (customerDepositRateEntity.getStatus().equals(DisableStatusEnum.ENABLE.getValue())) {
                recordOperationDetail.setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_DEPOSIT_RATE_STATUS_OPEN);
            }
            if (customerDepositRateEntity.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
                recordOperationDetail.setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_DEPOSIT_RATE_STATUS_CLOSE);
            }

            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return status;
    }

    @Override
    public boolean updateCustomerDepositRate(CustomerDepositRateEntity customerDepositRateEntity) {
        return customerDepositRateDao.updateById(customerDepositRateEntity);
    }

    @Override
    public boolean saveOrUpdateCustomerDepositRate(CustomerDepositRateEntity customerDepositRateEntity) {
        return customerDepositRateDao.saveOrUpdate(customerDepositRateEntity);
    }

    @Override
    public boolean deleteCustomerDepositRate(Integer customerDepositRateId) {
        CustomerDepositRateEntity customerDepositRate = customerDepositRateDao.getById(customerDepositRateId);
        if (null == customerDepositRate) {
            return false;
        }
        customerDepositRate.setIsDeleted(IsDeletedEnum.DELETED.getValue());
        return customerDepositRateDao.updateById(customerDepositRate);
    }

    @Override
    public boolean redactCustomerDepositRate(List<CustomerDepositRateDTO> customerDepositRateDTO) {

        if (null != customerDepositRateDTO.get(0).getCustomerId()) {

            CustomerDepositRateBO customerDepositRateBO = new CustomerDepositRateBO()
                    .setCustomerId(customerDepositRateDTO.get(0).getCustomerId())
                    .setCategoryId(customerDepositRateDTO.get(0).getCategoryId());
            List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateDao.getCustomerDepositRateByCustomerId(customerDepositRateBO);

            /*for (CustomerDepositRateEntity customerDepositRateEntity : customerDepositRateEntities) {
                this.deleteCustomerDepositRate(customerDepositRateEntity.getId());
            }*/
        }
        boolean b = false;
        for (CustomerDepositRateDTO customerDepositRate : customerDepositRateDTO) {
            //根据id 查询 履约保证金
            CustomerDepositRateEntity customerDepositRateEntity = customerDepositRateDao.getById(customerDepositRate.getId());
            CustomerDepositRateEntity original = new CustomerDepositRateEntity();
            BeanUtils.copyProperties(customerDepositRate, original);
            String currentUserId = JwtUtils.getCurrentUserId();
            String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
            //判断是否为空
            if (null != customerDepositRateEntity) {
                //判断参数是否更变
                customerDepositRateEntity.setDepositRate(customerDepositRate.getDepositRate())
                        .setCustomerId(customerDepositRate.getCustomerId())
                        .setIsSales(customerDepositRate.getIsSales())
                        .setIsProcurement(customerDepositRate.getIsProcurement())
                        .setCompanyId(customerDepositRate.getCompanyId())
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setCreatedBy(Integer.parseInt(currentUserId))
                        .setCreatedByName(name)
                        .setUpdatedBy(Integer.parseInt(currentUserId))
                        .setUpdatedByName(name)
                ;
                this.updateCustomerDepositRate(customerDepositRateEntity);
                b = true;
            } else {
                //为空新增 履约保证金 数据
                this.saveCustomerDepositRate(customerDepositRate, currentUserId, name);
                b = true;
            }
            try {
                RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
                recordOperationDetail
                        .setDtoData(JSON.toJSONString(customerDepositRateDTO))
                        .setBeforeData(JSON.toJSONString(original))
                        .setAfterData(JSON.toJSONString(customerDepositRateEntity))
                        .setOperationActionEnum(OperationActionEnum.SBM_REDACT_CUSTOMER_DEPOSIT_RATE)
                ;
                operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return b;
    }

    /**
     * 客户销售保证经配置
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addCustomerSalesDepositRate(MultipartFile file) {
        List<CustomerDepositRateFileDTO> customerDepositRateFileDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerDepositRateFileDTO.class);


        //查询所有品类
        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);

        addCustomerSalesDepositRate(customerDepositRateFileDTOS, category1Entities, category2Entities);


        return null;
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addCustomerSalesDepositRate(List<CustomerDepositRateFileDTO> customerDepositRateFileDTOS, List<CategoryEntity> category1Entities, List<CategoryEntity> category2Entities) {


        for (CustomerDepositRateFileDTO customerDepositRateFileDTO : customerDepositRateFileDTOS) {
            log.info("导入数据------:{}", customerDepositRateFileDTO.toString());

            //根据客户编码查询出客户信息
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerDepositRateFileDTO.getLinkageCustomerCode());
            if (null == customerEntity) {
                continue;
            }

            //根据申请单主体,隔开转换成list
            StringBuilder companyIds = new StringBuilder();
            String[] companyNameList = customerDepositRateFileDTO.getCompanyName().split(",");
            int size = companyNameList.length;
            int index = 0;
            for (String companyCode : companyNameList) {
                CompanyEntity company = companyFacade.getCompanyByCode(companyCode);
                index++;
                companyIds.append(company.getId());
                if (size != index) {
                    companyIds.append(",");
                }
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(customerDepositRateFileDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerDepositRateFileDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());

            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);

            CustomerDepositRateEntity customerDepositRateEntity = new CustomerDepositRateEntity();


            customerDepositRateEntity
                    .setContractType(ContractTypeEnum.getByDesc(customerDepositRateFileDTO.getContractType()).getValue())
                    .setCustomerId(customerEntity.getId())
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setBuCode(customerDepositRateFileDTO.getBuCode())
                    .setCompanyId(String.valueOf(companyIds))
                    .setContractType(ContractTypeEnum.getByDesc(customerDepositRateFileDTO.getContractType()).getValue())
                    .setDepositRate(StringUtil.isNotEmpty(customerDepositRateFileDTO.getDepositRate()) ? Integer.parseInt(customerDepositRateFileDTO.getDepositRate()) : 0)
                    .setPricingDepositRate(StringUtil.isNotEmpty(customerDepositRateFileDTO.getPricingDepositRate()) ? Integer.parseInt(customerDepositRateFileDTO.getPricingDepositRate()) : 0)
                    .setInvoicePaymentRate(StringUtil.isNotEmpty(customerDepositRateFileDTO.getInvoicePaymentRate()) ? Integer.parseInt(customerDepositRateFileDTO.getInvoicePaymentRate()) : 0)
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(1)
                    .setUpdatedByName("导入")
            ;

            if (customerDepositRateFileDTO.getSaleType().equals("销售")) {
                customerDepositRateEntity
                        .setIsSales(GeneralEnum.YES.getValue())
                        .setIsProcurement(GeneralEnum.NO.getValue())
                ;
            } else if (customerDepositRateFileDTO.getSaleType().equals("采购")) {
                customerDepositRateEntity
                        .setIsSales(GeneralEnum.NO.getValue())
                        .setIsProcurement(GeneralEnum.YES.getValue())
                ;
            } else {
                customerDepositRateEntity
                        .setIsSales(GeneralEnum.YES.getValue())
                        .setIsProcurement(GeneralEnum.YES.getValue());
            }


            if ("更新".equals(customerDepositRateFileDTO.getOperationType()) || "删除".equals(customerDepositRateFileDTO.getOperationType())) {

                CustomerDepositRateDTO customerDepositRateDTO = new CustomerDepositRateDTO();
                customerDepositRateDTO
                        .setDepositRate(customerDepositRateEntity.getDepositRate())
                        .setPricingDepositRate(customerDepositRateEntity.getPricingDepositRate())
                        .setInvoicePaymentRate(customerDepositRateEntity.getInvoicePaymentRate())
                        .setIsSales(customerDepositRateEntity.getIsSales())
                        .setIsProcurement(customerDepositRateEntity.getIsProcurement())
                        .setCustomerId(customerEntity.getId())
                        .setBuCode(customerDepositRateEntity.getBuCode())
                        .setCompanyId(customerDepositRateEntity.getCompanyId())
                        .setContractType(customerDepositRateEntity.getContractType());

                List<Integer> category2s = Arrays.stream(customerDepositRateEntity.getCategory2().split(","))
                        .map(String::trim)
                        .filter(i -> !i.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                for (Integer i : category2s) {
                    customerDepositRateDTO.setCategory2(String.valueOf(i));
                    List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateDao.queryCustomerDepositRate(customerDepositRateDTO);
                    if ("删除".equals(customerDepositRateFileDTO.getOperationType()) && !CollectionUtils.isEmpty(customerDepositRateEntities)) {
                        Integer id = customerDepositRateEntities.get(0).getId();
                        customerDepositRateEntity
                                .setId(id)
                                .setStatus(DisableStatusEnum.DISABLE.getValue())
                        ;
                        customerDepositRateDao.updateById(customerDepositRateEntity);
                        break;
                    } else if ("更新".equals(customerDepositRateFileDTO.getOperationType()) && CollectionUtils.isEmpty(customerDepositRateEntities)) {
                        customerDepositRateDao.save(customerDepositRateEntity);
                        break;
                    }
                }

            } else {
                customerDepositRateEntity.setCreatedAt(new Date())
                        .setCreatedBy(1)
                        .setCreatedByName("导入");
                customerDepositRateDao.save(customerDepositRateEntity);
            }


        }
    }

    @Override
    public CustomerDepositRateEntity queryCustomerDepositRateEntityById(Integer id) {
        return customerDepositRateDao.getById(id);
    }


    @Override
    public List<CustomerDepositRateEntity> queryCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        return customerDepositRateDao.queryCustomerDepositRate(customerDepositRateDTO);
    }

}
