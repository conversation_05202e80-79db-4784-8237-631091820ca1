package com.navigator.customer.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import com.navigator.customer.pojo.vo.CustomerGradeScoreExcelVO;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import com.navigator.customer.service.ICustomerDetailService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/20 17:06
 */
@RestController
public class CustomerDetailFacadeImpl implements CustomerDetailFacade {

    @Resource
    private ICustomerDetailService customerDetailService;
    @Resource
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Resource
    private EmployFacade employFacade;


    @Override
    public CustomerDetailEntity queryCustomerDetailList(Integer customerId, Integer categoryId) {
        return customerDetailService.queryCustomerDetailList(customerId, categoryId);
    }

    @Override
    public List<CustomerDetailEntity> queryCustomerDetailListByCondition(CustomerDetailBO customerDetailBO) {
        return customerDetailService.queryCustomerDetailListByCondition(customerDetailBO);
    }

    @Override
    public Integer saveCustomerDetail(CustomerDetailDTO customerDetailDTO) {
        return customerDetailService.saveCustomerDetail(customerDetailDTO);
    }

    @Override
    public Integer updateCustomerDetail(CustomerDetailBO customerDetailBO) {
        customerDetailBO.setProtocolEndDate(DateTimeUtil.parseDateString(DateTimeUtil.formatDateString(customerDetailBO.getProtocolEndDate())))
                .setProtocolStartDate(DateTimeUtil.parseDateString(DateTimeUtil.formatDateString(customerDetailBO.getProtocolStartDate())));

        CustomerDetailDTO customerDetailDTO = BeanConvertUtils.convert(CustomerDetailDTO.class, customerDetailBO);

        try {
            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
            //记录修改客户主数据人
            customerDetailUpdateRecordEntity
                    .setDetailCode(customerDetailBO.getDetailCode())
                    .setCustomerId(customerDetailBO.getCustomerId())
                    .setCategoryId(customerDetailBO.getCategoryId())
                    .setData(JSON.toJSONString(customerDetailBO))
                    .setCreatedAt(new Date())
                    .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);

        }catch (Exception e){

        }
        if(null == customerDetailDTO.getId()){
            return customerDetailService.saveCustomerDetail(customerDetailDTO);
        }else {
            return customerDetailService.updateCustomerDetail(customerDetailDTO);
        }
    }

    @Override
    public Result addCustomerDetailIsWhiteList(MultipartFile file) {
        return Result.success(customerDetailService.addCustomerDetailIsWhiteList(file));
    }

    @Override
    public Result whiteListCustomerTemplate(MultipartFile file) {
        return customerDetailService.whiteListCustomerTemplate(file);
    }

    @Override
    public List<CustomerDetailEntity> queryCustomerInvoiceList(Integer customerId, Integer categoryId) {
        return customerDetailService.queryCustomerInvoiceList(customerId,categoryId);
    }

    @Override
    public Result saveCustomerInvoice(CustomerDetailBO customerDetailBO) {
       return customerDetailService.saveCustomerInvoice(customerDetailBO);
    }

    @Override
    public Result modifyCustomerInvoice(CustomerDetailBO customerDetailBO) {
       return customerDetailService.modifyCustomerInvoice(customerDetailBO);
    }

    @Override
    public CustomerDetailEntity queryCustomerDetailEntity(Integer customerId, Integer category3) {
        return customerDetailService.queryCustomerDetailEntity(customerId,category3);

    }

    @Override
    public Result copyCustomerInvoice() {
        customerDetailService.copyCustomerInvoice();
        return Result.success();
    }


    @Override
    public Result uploadGradeScoreTemplate(MultipartFile file) {
        return customerDetailService.uploadGradeScoreTemplate(file);
    }

    @Override
    public List<CustomerTemplateDeriveGradeScoreDTO> exportGradeScoreList(CustomerDTO queryDTO) {
        return customerDetailService.exportGradeScoreList(queryDTO);
    }

    @Override
    public Result importGradeScoreExcel(MultipartFile file) {
        customerDetailService.importGradeScoreExcel(file);
        return Result.success();
    }
}
