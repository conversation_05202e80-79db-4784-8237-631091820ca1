package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.customer.service.CustomerOriginalPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class CustomerOriginalPaperFacadeImpl implements CustomerOriginalPaperFacade {

    @Autowired
    private CustomerOriginalPaperService customerOriginalPaperService;

    @Override
    public Result queryCustomerOriginalPaperList(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.queryCustomerOriginalPaperList(customerOriginalPaperDTO);
    }

    @Override
    public Result saveCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.saveCustomerOriginalPaper(customerOriginalPaperDTO);

    }

    @Override
    public Result updateCustomerOriginalPaperStatus(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.updateCustomerOriginalPaperStatus(customerOriginalPaperDTO);
    }

    @Override
    public Result updateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.updateCustomerOriginalPaper(customerOriginalPaperDTO);
    }

    @Override
    public CustomerOriginalPaperEntity queryCustomerOriginalPaperEntity(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
    }

    @Override
    public Integer queryCustomerNonFrame(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperService.queryCustomerNonFrame(customerOriginalPaperDTO);
    }


    @Override
    public Result importCustomer(MultipartFile file) {
        return customerOriginalPaperService.importCustomer(file);
    }
}
