package com.navigator.customer.facade.impl;

import com.navigator.customer.facade.FactoryConfigFacade;
import com.navigator.customer.pojo.entity.FactoryConfigEntity;
import com.navigator.customer.service.IFactoryConfigService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/25 17:34
 */
public class FactoryConfigFacadeImpl implements FactoryConfigFacade {

    @Resource
    private IFactoryConfigService factoryConfigService;

    @Override
    public List<FactoryConfigEntity> factoryConfigBySuppId(Integer customerId) {
        return factoryConfigService.factoryConfigBySuppId(customerId);
    }
}
