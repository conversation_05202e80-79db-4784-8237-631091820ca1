package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.FactoryMapper;
import com.navigator.customer.pojo.entity.FactoryEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/28 9:53
 */
@Dao
public class FactoryDao extends BaseDaoImpl<FactoryMapper, FactoryEntity> {

    public List<FactoryEntity> getAllFactory(Integer status) {
        return this.baseMapper.selectList(Wrappers.<FactoryEntity>lambdaQuery()
                .eq(FactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != status, FactoryEntity::getStatus, status)
        );
    }



    public FactoryEntity getFactoryByCode(String code, Integer status) {
        List<FactoryEntity> factoryEntityList = this.baseMapper.selectList(Wrappers.<FactoryEntity>lambdaQuery()
                .eq(FactoryEntity::getCode, code)
                .eq(FactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != status, FactoryEntity::getStatus, status)
        );
        return CollectionUtils.isEmpty(factoryEntityList) ? null : factoryEntityList.get(0);
    }

    public FactoryEntity getFactoryByShortName(String shortName, Integer status) {
        return this.baseMapper.selectOne(Wrappers.<FactoryEntity>lambdaQuery()
                .eq(!StringUtils.isBlank(shortName), FactoryEntity::getShortName, shortName)
                .eq(FactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != status, FactoryEntity::getStatus, status)
        );
    }
}
