package com.navigator.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.dao.CustomerDao;
import com.navigator.customer.dao.CustomerDetailDao;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.dto.file.CustomerGradeScoreExcelDTO;
import com.navigator.customer.pojo.dto.file.CustomerWhiteExcelDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.customer.pojo.enums.DetailCodeEnum;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerGradeScoreExcelVO;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import com.navigator.customer.service.CustomerGradeScoreService;
import com.navigator.customer.service.ICustomerDetailService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.facade.ContractFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Service
@Slf4j
public class CustomerDetailServiceImpl implements ICustomerDetailService {

    @Resource
    private CustomerDetailDao customerDetailDao;
    @Resource
    private CustomerDao customerDao;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private ContractFacade contractFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private CategoryDisposeMap categoryDisposeMap;
    @Autowired
    private CustomerGradeScoreService customerGradeScoreService;

    @Override
    public CustomerDetailEntity queryCustomerDetailList(Integer customerId, Integer categoryId) {

        CustomerDetailEntity customerDetailEntity = customerDetailDao.queryCustomerDetailList(customerId, categoryId);

        if (null != customerDetailEntity && null != customerDetailEntity.getProtocolStartDate()) {
            customerDetailEntity.setProtocolStartDate(DateTimeUtil.parseDateString(DateTimeUtil.formatDateString(customerDetailEntity.getProtocolStartDate())));
        }
        if (null != customerDetailEntity && null != customerDetailEntity.getProtocolEndDate()) {
            customerDetailEntity.setProtocolEndDate(DateTimeUtil.parseDateString(DateTimeUtil.formatDateString(customerDetailEntity.getProtocolEndDate())));
        }
        if (null != customerDetailEntity && null == customerDetailEntity.getGradeScore())
            customerDetailEntity.setGradeScore("");

        // 副产品 豆二 天生不能反点价 serial_no 27 28
        if (null != customerDetailEntity) {
            if (Objects.equals(categoryId, GoodsCategoryEnum.GRAIN_CORN.getValue()) || Objects.equals(categoryId, GoodsCategoryEnum.SOYBEAN2.getValue())) {
                customerDetailEntity.setIsReversePrice(0);
            }
        }

        return customerDetailEntity;
    }

    @Override
    public List<CustomerDetailEntity> queryCustomerDetailListByCondition(CustomerDetailBO customerDetailBO) {

        List<CustomerDetailEntity> customerDetailList = customerDetailDao.queryCustomerDetailListByCondition(customerDetailBO);


        customerDetailList.forEach(customerDetailEntity -> {
            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = new ArrayList<>();
            if (StringUtil.isNotEmpty(customerDetailEntity.getCategory2())) {
                category2List = Arrays.stream(customerDetailEntity.getCategory2().split(","))
                        .map(String::trim)
                        .filter(category2 -> !category2.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                for (Integer category2 : category2List) {
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                    if(null != categoryEntity){
                        if (categoryEntity != null) {
                            category2Name.append(categoryEntity.getName());
                        }
                        //判断是否是最后一个
                        if (!category2.equals(category2List.get(category2List.size() - 1))) {
                            category2Name.append(",");
                        }
                    }
                }
            }

            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = new ArrayList<>();
            if (StringUtil.isNotEmpty(customerDetailEntity.getCategory3())) {
                category3List = Arrays.stream(customerDetailEntity.getCategory3().split(","))
                        .map(String::trim)
                        .filter(category3 -> !category3.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                for (Integer category3 : category3List) {
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                    if(null != categoryEntity){
                        category3Name.append(categoryEntity.getName());
                        //判断是否是最后一个
                        if (!category3.equals(category3List.get(category3List.size() - 1))) {
                            category3Name.append(",");
                        }
                    }
                }
            }
            customerDetailEntity.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, category3List))
                    .setCategory2Name(category2Name.toString())
                    .setCategory3Name(category3Name.toString())
                    .setCategory1(customerDetailEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerDetailEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerDetailEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;
        });

        return customerDetailList;
    }

    @Override
    public List<CustomerDetailEntity> queryCustomerIdList(Integer customerId) {
        return customerDetailDao.queryCustomerIdList(customerId);
    }

    @Override
    public Integer saveCustomerDetail(CustomerDetailDTO customerDetailDTO) {
        CustomerDetailEntity customerDetailEntity = BeanConvertUtils.convert(CustomerDetailEntity.class, customerDetailDTO.getCategoryId());
        return customerDetailDao.saveCustomerDetail(customerDetailEntity);
    }

    @Override
    public Integer updateCustomerDetail(CustomerDetailDTO customerDetailDTO) {

        //查询客户配置id下是否有数据

        CustomerDetailEntity customerDetailEntity = new CustomerDetailEntity();
        if (null == customerDetailDTO.getId()) {
            BeanUtils.copyProperties(customerDetailDTO, customerDetailEntity);

            return customerDetailDao.save(customerDetailEntity) ? 1 : 0;
        }
        customerDetailEntity = customerDetailDao.getById(customerDetailDTO.getId());

        CustomerDetailEntity originalCustomerDetail = new CustomerDetailEntity();
        BeanUtils.copyProperties(customerDetailEntity, originalCustomerDetail);
        if (null != customerDetailEntity) {
            // 转月状态
            Integer transferStatus = customerDetailDTO.getIsWhiteList();
            if (null != transferStatus && !customerDetailEntity.getIsWhiteList().equals(transferStatus)) {
                customerDetailDTO.setTransferStatusChange(true);
            }

            // 反点价状态
            Integer reversePriceStatus = customerDetailDTO.getIsReversePrice();
            if (null != reversePriceStatus && !customerDetailEntity.getIsReversePrice().equals(reversePriceStatus)) {
                customerDetailDTO.setReversePriceStatusChange(true);
            }

            // 更新合同的转月/反点价次数
            if (customerDetailDTO.isTransferStatusChange() || customerDetailDTO.isReversePriceStatusChange()) {
                customerDetailDTO.setCustomerId(customerDetailEntity.getCustomerId());
                contractFacade.changeCustomerWhiteList(customerDetailDTO);
            }

            customerDetailEntity = BeanConvertUtils.convert(CustomerDetailEntity.class, customerDetailDTO);
            String currentUserId = JwtUtils.getCurrentUserId();
            String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
            customerDetailEntity
                    .setUpdatedBy(currentUserId)
                    .setUpdatedByName(name);

            // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-21 Start
            // 如果修改白名单需要更新不同主体下的白名单次数
            if (DetailCodeEnum.WHITE_LIST.getValue().equals(customerDetailDTO.getDetailCode())) {
                // 查询客户detail 区分品类 不区分主体
                List<CustomerDetailEntity> customerDetailEntities = customerDetailDao.queryCustomerDetailEntitiesList(customerDetailDTO.getCustomerId(), customerDetailDTO.getCategory2(), customerDetailDTO.getCategory3());
                for (CustomerDetailEntity detailEntity : customerDetailEntities) {
                    customerDetailDao.updateById(
                            detailEntity.setIsWhiteList(customerDetailDTO.getIsWhiteList())
                                    .setIsReversePrice(customerDetailDTO.getIsReversePrice())
                                    .setIsStructure(customerDetailDTO.getIsStructure())
                                    .setUpdatedBy(currentUserId)
                                    .setUpdatedByName(name)
                                    .setUpdatedAt(new Date()));
                }
            } else {
                boolean b = customerDetailDao.updateById(customerDetailEntity);
                log.info("返回信息B" + b);
            }
            // BUGFIX：case-1002663 天津淦海在白名单里，合同可转月量和可转月次数不对，可反点价次数不对 Author: Mr 2024-06-21 End

            try {
                RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
                recordOperationDetail
                        .setDtoData(JSON.toJSONString(customerDetailDTO))
                        .setBeforeData(JSON.toJSONString(originalCustomerDetail))
                        .setAfterData(JSON.toJSONString(customerDetailEntity))
                        .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_DETAIL)
                ;
                operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            throw new RuntimeException("未找到客户配置");
        }
        return 1;
    }

    /**
     * 添加客户被名单配置
     *
     * @param file
     * @return
     */
    @Override
    public boolean addCustomerDetailIsWhiteList(MultipartFile file) {

        List<CustomerCreditDaysDTO> customerCreditDaysDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerCreditDaysDTO.class);

        for (CustomerCreditDaysDTO customerCreditDaysDTO : customerCreditDaysDTOS) {
            log.info("客户白名单数据:" + customerCreditDaysDTO.toString());
            if (null != customerCreditDaysDTO.getLinkageCustomerCode()) {
                //根据Linkag编号查询客户信息
                CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerCreditDaysDTO.getLinkageCustomerCode());

                if (null == customerEntity) {
                    continue;
                }

                Integer categoryId = customerCreditDaysDTO.getGoodsCategory().equals("SBM") ? GoodsCategoryEnum.OSM_MEAL.getValue() : GoodsCategoryEnum.OSM_OIL.getValue();

                CustomerDetailEntity customerDetailEntity = customerDetailDao.queryCustomerDetailList(customerEntity.getId(), categoryId);

                if (null != customerDetailEntity) {
                    customerDetailEntity.setIsReversePrice(GeneralEnum.YES.getValue()).setIsStructure(GeneralEnum.YES.getValue()).setIsWhiteList(GeneralEnum.YES.getValue());
                    customerDetailDao.updateById(customerDetailEntity);
                }

            }

        }
        return true;

    }

    @Override
    public boolean saveOrUpdateCustomerDetail(CustomerDetailEntity customerDetailEntity) {
        return customerDetailDao.saveOrUpdate(customerDetailEntity);
    }

    @Override
    public List<Integer> customerProtocol() {

        List<CustomerDetailEntity> customerDetailEntities = customerDetailDao.customerProtocol();

        if (customerDetailEntities.isEmpty()) {
            return null;
        }

        List<Integer> customerId = new ArrayList<>();
        for (CustomerDetailEntity customerDetailEntity : customerDetailEntities) {
            Integer Id = customerDetailEntity.getCustomerId();
            customerId.add(Id);
        }

        return customerId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result whiteListCustomerTemplate(MultipartFile file) {
        List<CustomerWhiteExcelDTO> customerWhiteExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerWhiteExcelDTO.class);

        List<String> customerCodeList = new ArrayList<>();

        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);

        for (CustomerWhiteExcelDTO customerWhiteExcelDTO : customerWhiteExcelDTOS) {
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerWhiteExcelDTO.getCustomerCode());
            //查询货品数据
            List<String> category1List = Arrays.stream(customerWhiteExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerWhiteExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category3List = Arrays.stream(customerWhiteExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);

            log.info("客户数据:===={}", JSON.toJSONString(customerEntity));
            if (null != customerEntity) {
                CustomerDetailEntity customerDetailEntity = new CustomerDetailEntity();
                customerDetailEntity
                        .setCustomerId(customerEntity.getId())
                        .setId(StringUtils.isNotEmpty(customerWhiteExcelDTO.getId()) ? Integer.parseInt(customerWhiteExcelDTO.getId()) : null)
                        .setCategory1(category1)
                        .setCategory2(category2)
                        .setCategory3(category3)
                        .setIsWhiteList("是".equals(customerWhiteExcelDTO.getIsWhiteList()) ? 1 : 0)
                        .setIsReversePrice("是".equals(customerWhiteExcelDTO.getIsReversePrice()) ? 1 : 0)
                        .setIsStructure("是".equals(customerWhiteExcelDTO.getIsStructure()) ? 1 : 0)
                ;
                customerDetailDao.saveOrUpdate(customerDetailEntity);
            } else {
                customerCodeList.add(customerWhiteExcelDTO.getCustomerCode());
            }

        }
        log.info("==========为成功处理的客户账号:{}" + JSON.toJSONString(customerCodeList));
        return null;
    }

    @Override
    public List<CustomerDetailEntity> queryCustomerInvoiceList(Integer customerId, Integer categoryId) {
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailDao.queryCustomerInvoiceList(customerId, categoryId);
        customerDetailEntityList.forEach(i -> {
            CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO = new CustomerDetailUpdateRecordDTO();
            customerDetailUpdateRecordDTO.setCustomerId(customerId)
                    .setCategoryId(categoryId)
                    .setCompanyId(i.getCompanyId())
                    .setDetailCode("20005")
            ;
            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = customerDetailUpdateRecordService.detailUpdateSelect(customerDetailUpdateRecordDTO);
            if (null != customerDetailUpdateRecordEntity) {
                i.setUpdatedByName(customerDetailUpdateRecordEntity.getCreatedBy());
                i.setUpdatedAt(customerDetailUpdateRecordEntity.getCreatedAt());
            }
        });
        return customerDetailEntityList;
    }

    @Override
    public Result saveCustomerInvoice(CustomerDetailBO customerDetailBO) {
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailDao.queryCustomerDetailEntitiesList(customerDetailBO.getCustomerId(), customerDetailBO.getCategory2(), customerDetailBO.getCategory3());
        if (CollectionUtils.isEmpty(customerDetailEntityList)) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_EXISTS);
        }
        List<Integer> companyIdList = customerDetailEntityList.stream().map(CustomerDetailEntity::getCompanyId).collect(Collectors.toList());
        if (companyIdList.contains(customerDetailBO.getCompanyId())) {
            throw new BusinessException(ResultCodeEnum.SAME_INVOICE);
        }
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName, (k1, k2) -> k1));

        CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
        customerDetailEntity
                .setCategoryId(customerDetailBO.getCategoryId())
                .setCustomerId(customerDetailBO.getCustomerId())
                .setInvoiceId(customerDetailBO.getInvoiceId())
                .setInvoiceName(customerDetailBO.getInvoiceName())
                .setCompanyId(customerDetailBO.getCompanyId())
                .setCompanyName(companyNameMap.get(customerDetailBO.getCompanyId()))
                .setId(null);
        customerDetailDao.save(customerDetailEntity);
        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO = new CustomerDetailUpdateRecordDTO();
        customerDetailUpdateRecordDTO.setCustomerId(customerDetailBO.getCustomerId())
                .setCategoryId(customerDetailBO.getCategoryId())
                .setCompanyId(customerDetailBO.getCompanyId())
                .setDetailCode("20005")
                .setCreatedBy(name)
                .setData(JSON.toJSONString(customerDetailBO))
                .setCreatedAt(new Date())
        ;
        customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordDTO);
        return Result.success();
    }

    public List<CustomerDetailEntity> queryCustomerDetailEntitiesList(Integer customerId, String category2, String category3) {
        return customerDetailDao.queryCustomerDetailEntitiesList(customerId, category2, category3);
    }

    @Override
    public Result modifyCustomerInvoice(CustomerDetailBO customerDetailBO) {
        CustomerDetailEntity customerDetailEntity = customerDetailDao.getById(customerDetailBO.getId());
        if (customerDetailEntity == null) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_NOT_EXISTS);
        }
        customerDetailEntity
                .setCategoryId(customerDetailBO.getCategoryId())
                .setCustomerId(customerDetailBO.getCustomerId())
                .setInvoiceId(customerDetailBO.getInvoiceId())
                .setInvoiceName(customerDetailBO.getInvoiceName())
        ;
        customerDetailDao.updateById(customerDetailEntity);
        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO = new CustomerDetailUpdateRecordDTO();
        customerDetailUpdateRecordDTO.setCustomerId(customerDetailBO.getCustomerId())
                .setCategoryId(customerDetailBO.getCategoryId())
                .setCompanyId(customerDetailEntity.getCompanyId())
                .setDetailCode("20005")
                .setCreatedBy(name)
                .setData(JSON.toJSONString(customerDetailBO))
                .setCreatedAt(new Date())
        ;
        customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordDTO);
        return Result.success();
    }

    @Override
    public CustomerDetailEntity queryCustomerDetailEntity(Integer customerId, Integer category3) {
        CustomerDetailEntity customerDetailEntity = customerDetailDao.queryCustomerDetailEntity(customerId, category3);
        return customerDetailEntity;
    }

    @Override
    public void copyCustomerInvoice() {
        customerDetailDao.deleteByCompanyId();
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailDao.queryAll();
        customerDetailEntityList.forEach(i -> {
            i.setCompanyId(2).setCompanyName("FL").setId(null);
        });
        List<List<CustomerDetailEntity>> partition = ListUtils.partition(customerDetailEntityList, 10);
        partition.forEach(i -> {
            i.forEach(j -> {
                customerDetailDao.save(j);
            });
        });
    }

    @Override
    public Result uploadGradeScoreTemplate(MultipartFile file) {
        List<CustomerGradeScoreExcelVO> scoreList = previewGradeScore(file);
        if (scoreList == null || scoreList.size() == 0)
            throw new BusinessException(ResultCodeEnum.UPLOAD_NULL);

        boolean flag = true;
        int index = 1;
        String message = "";

        for (CustomerGradeScoreExcelVO vo : scoreList) {
            String customerCode = vo.getCustomerCode();
            if (StringUtil.isEmpty(customerCode)) {
                flag = false;
                message = "第" + index + "条数据客户编号为空";
                break;
            }
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerCode);
            if (null == customerEntity) {
                flag = false;
                message = "第" + index + "条数据系统无此客户编号";
                break;
            }
            if (StringUtil.isNotEmpty(vo.getCustomerName())) {
                if (!customerEntity.getName().equals(vo.getCustomerName())) {
                    flag = false;
                    message = "第" + index + "条数据客户名称和客户编码不匹配";
                    break;
                }
            }
            if (StringUtil.isNotEmpty(vo.getEnterpriseName())) {
                if (!customerEntity.getEnterpriseName().equals(vo.getEnterpriseName())) {
                    flag = false;
                    message = "第" + index + "条数据客户集团名称和客户编码不匹配";
                    break;
                }
            }
            if (StringUtil.isEmpty(vo.getCategory1())) {
                flag = false;
                message = "第" + index + "一级品类数据为空";
                break;
            }
            if (StringUtil.isEmpty(vo.getCategory2())) {
                flag = false;
                message = "第" + index + "二级品类数据为空";
                break;
            }
            if (StringUtil.isEmpty(vo.getCategory3())) {
                flag = false;
                message = "第" + index + "三级品类数据为空";
                break;
            }
            if (BigDecimalUtil.isLessThanZero(new BigDecimal(vo.getGradeScore()))) {
                flag = false;
                message = "第" + index + "评级数据为非整数";
                break;
            }
            index++;
        }
        if (flag == false) {
            return Result.failure(message);
        }
        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        for (CustomerGradeScoreExcelVO customerGradeScoreExcelVO : scoreList) {
            String customerCode = customerGradeScoreExcelVO.getCustomerCode();
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerCode);
            //根据客户编码查询客户是否存在
            if (null == customerEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(customerGradeScoreExcelVO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerGradeScoreExcelVO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category3List = Arrays.stream(customerGradeScoreExcelVO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);

            CustomerGradeScoreEntity customerGradeScoreEntity = new CustomerGradeScoreEntity();
            customerGradeScoreEntity
                    .setCustomerId(customerEntity.getId())
                    .setGradeScore(customerGradeScoreExcelVO.getGradeScore())
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
                    .setCreatedBy(name)
                    .setCreatedAt(new Date())
                    .setUpdatedBy(name)
                    .setUpdatedAt(new Date())
            ;

            customerGradeScoreService.saveOrUpdateCustomerGradeScore(customerGradeScoreEntity);
        }

        return Result.success("分数导入成功：" + scoreList.size() + "条数据～", true);
    }

    @Override
    public List<CustomerTemplateDeriveGradeScoreDTO> exportGradeScoreList(CustomerDTO queryDTO) {
        String customerCode = StringUtil.isEmpty(queryDTO.getLinkageCustomerCode()) ? null : queryDTO.getLinkageCustomerCode();
        String name = StringUtil.isEmpty(queryDTO.getName()) ? null : queryDTO.getName();
        String updatedByName = StringUtil.isEmpty(queryDTO.getUpdatedByName()) ? null : queryDTO.getUpdatedByName();
        List<CustomerTemplateDeriveGradeScoreDTO> sbmGradeCustomerList = customerDao.getSbmGradeCustomerList(customerCode, name, updatedByName);
        List<CustomerTemplateDeriveGradeScoreDTO> sboGradeCustomerList = customerDao.getSboGradeCustomerList(customerCode, name, updatedByName);

        if (null != sbmGradeCustomerList && sbmGradeCustomerList.size() != 0) {
            int index = 0;
            for (CustomerTemplateDeriveGradeScoreDTO sbm : sbmGradeCustomerList) {
                sbm.setSboGradeScore(sboGradeCustomerList.get(index).getSboGradeScore());
                index++;
            }
        }

        return sbmGradeCustomerList;
    }

    private List<CustomerGradeScoreExcelVO> previewGradeScore(MultipartFile file) {
        List<CustomerGradeScoreExcelVO> resultList = new ArrayList<>();

        try {
            List<CustomerGradeScoreExcelVO> gradeScoreExcelVOList = EasyPoiUtils.importExcel(file, 1, 1, CustomerGradeScoreExcelVO.class);
            if (CollectionUtil.isEmpty(gradeScoreExcelVOList)) {
                return gradeScoreExcelVOList;
            }
            resultList = gradeScoreExcelVOList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        }
        return resultList;
    }

    @Override
    public void importGradeScoreExcel(MultipartFile file) {
        List<CustomerGradeScoreExcelDTO> customerInvoiceFileDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerGradeScoreExcelDTO.class);
        int index = 0;
        List<CompanyEntity> companyEntities = companyFacade.queryCompanyList();
        HashMap companyMap = new HashMap();
        for (CompanyEntity companyEntity : companyEntities) {
            companyMap.put(companyEntity.getShortName(), companyEntity.getId());
        }
        for (CustomerGradeScoreExcelDTO gradeScoreDTO : customerInvoiceFileDTOS) {
            index++;
            if (StringUtil.isEmpty(gradeScoreDTO.getCustomerCode()) || StringUtil.isEmpty(gradeScoreDTO.getCategoryCode()) || StringUtil.isEmpty(gradeScoreDTO.getCompanyNames())) {
                log.info("==========importGradeScoreExcel_fail 有问题的条目 index:{}，customerCode:{}", index, gradeScoreDTO.getCustomerCode());
                continue;
            }
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(gradeScoreDTO.getCustomerCode());
            if (null == customerEntity) {
                log.info("==========importGradeScoreExcel_fail 不存在的客户 index:{}，customerCode:{}", index, gradeScoreDTO.getCustomerCode());
                continue;
            }
            String[] mainList = gradeScoreDTO.getCompanyNames().split(",");
            for (String main : mainList) {
                int mainId = (int) companyMap.get(main);
                CustomerDetailEntity customerDetail = customerDetailDao.queryCustomerDetailEntity(customerEntity.getId(), GoodsCategoryEnum.getValueByCode(gradeScoreDTO.getCategoryCode()));
                if (null == customerDetail) {
                    log.info("==========importGradeScoreExcel_fail 找不到customerDetail index:{}，customerCode:{},companyId", index, gradeScoreDTO.getCustomerCode());
                } else {
                    customerDetail.setGradeScore(gradeScoreDTO.getGradeScore());
                    customerDetailDao.updateById(customerDetail);
                }
            }
        }
    }

    @Override
    public CustomerDetailEntity queryCustomerDetailById(Integer id) {
        return customerDetailDao.getById(id);
    }

    @Override
    public CustomerDetailEntity queryCustomerDetailListByNotId(CustomerDetailDTO customerDetailDTO) {
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailDao.queryCustomerDetailListByNotId(customerDetailDTO);

        return customerDetailEntityList.isEmpty() ? null : customerDetailEntityList.get(0);
    }

    @Override
    public List<CustomerDetailEntity> checkCustomerWhiteList(CustomerDetailDTO customerDetailDTO) {

        return customerDetailDao.queryCustomerDetailListByNotId(customerDetailDTO);
    }

}
