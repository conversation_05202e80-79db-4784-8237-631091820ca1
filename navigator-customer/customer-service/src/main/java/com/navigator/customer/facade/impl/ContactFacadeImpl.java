package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.ContactFacade;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.enums.ReferTypeEnum;
import com.navigator.customer.pojo.vo.CustomerContactVO;
import com.navigator.customer.service.IContactService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 14:32
 */
@RestController
public class ContactFacadeImpl implements ContactFacade {

    @Resource
    private IContactService contactService;

    @Override
    public Integer updateContactEntity(ContactEntity contactEntity) {

        return contactService.updateContactEntity(contactEntity);
    }

    @Override
    public Integer vaseContactEntity(ContactEntity contactEntity) {
        return contactService.vaseContactEntity(contactEntity);
    }

    @Override
    public ContactEntity getContactEntityById(Integer id) {
        return contactService.getContactEntityById(id);
    }

    @Override
    public List<CustomerContactVO> queryContactFactoryList(ContactDTO contactDTO) {
        return contactService.queryContactFactoryList(contactDTO);
    }

    @Override
    public List<ContactEntity> getContactCustomerByList(Integer referId, Integer categoryId, Integer supplierId) {
        return contactService.getContactCustomerByList(referId, categoryId, supplierId);
    }

    @Override
    public List<ContactEntity> getContactLDCByList(Integer referId, Integer categoryId) {
        return contactService.queryLDCContactEntity(referId, ReferTypeEnum.LDC.getValue(), categoryId);
    }

    @Override
    public List<ContactEntity> getContactSupplierIdByList(Integer referId, Integer categoryId) {
        return contactService.queryLDCContactEntity(referId, ReferTypeEnum.SUPPLIER.getValue(), categoryId);
    }

    @Override
    public ContactDTO getCustomerByCustomerIdYqq(Integer customerId) {
        return contactService.getCustomerByCustomerIdYqq(customerId);
    }

    @Override
    public Result getCustomerByCustomerId(Integer customerId, Integer referType, Integer categoryId) {
        return Result.success(contactService.getCustomerByCustomerId(customerId, referType, categoryId));
    }

    @Override
    public Result leadCustomerContact(MultipartFile file){
        return contactService.leadCustomerContact(file);
    }

    @Override
    public Result contactAddress() {
        contactService.contactAddress();
        return Result.success();
    }
}
