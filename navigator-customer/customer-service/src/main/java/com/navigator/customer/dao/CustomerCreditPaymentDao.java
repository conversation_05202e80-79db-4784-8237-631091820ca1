package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerCreditPaymentMapper;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.entity.CustomerCreditPaymentEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 17:08
 */
@Dao
public class CustomerCreditPaymentDao extends BaseDaoImpl<CustomerCreditPaymentMapper, CustomerCreditPaymentEntity> {

    public List<CustomerCreditPaymentEntity> CustomerCreditPaymentAllList(CustomerCreditPaymentDTO customerCreditPaymentDTO) {

        return this.baseMapper.selectList(Wrappers.<CustomerCreditPaymentEntity>lambdaQuery()
                .eq(CustomerCreditPaymentEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CustomerCreditPaymentEntity::getCustomerId, customerCreditPaymentDTO.getCustomerId())
                .eq(null != customerCreditPaymentDTO.getCreditDays(), CustomerCreditPaymentEntity::getCreditDays, customerCreditPaymentDTO.getCreditDays())
                .eq(null != customerCreditPaymentDTO.getCategoryId(), CustomerCreditPaymentEntity::getCategoryId, customerCreditPaymentDTO.getCategoryId())
                .eq(null != customerCreditPaymentDTO.getStatus(), CustomerCreditPaymentEntity::getStatus, customerCreditPaymentDTO.getStatus())
                .eq(null != customerCreditPaymentDTO.getIsSales() && GeneralEnum.YES.getValue().equals(customerCreditPaymentDTO.getIsSales()), CustomerCreditPaymentEntity::getIsSales, customerCreditPaymentDTO.getIsSales())
                .eq(null != customerCreditPaymentDTO.getIsProcurement() && GeneralEnum.YES.getValue().equals(customerCreditPaymentDTO.getIsProcurement()), CustomerCreditPaymentEntity::getIsProcurement, customerCreditPaymentDTO.getIsProcurement())
                .eq(null != customerCreditPaymentDTO.getCompanyId(), CustomerCreditPaymentEntity::getCompanyId, customerCreditPaymentDTO.getCompanyId())
                .eq(StringUtils.isNotEmpty(customerCreditPaymentDTO.getBuCode()), CustomerCreditPaymentEntity::getBuCode, customerCreditPaymentDTO.getBuCode())
                .like(StringUtils.isNotEmpty(customerCreditPaymentDTO.getCategory1()), CustomerCreditPaymentEntity::getCategory1, "," + customerCreditPaymentDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerCreditPaymentDTO.getCategory2()), CustomerCreditPaymentEntity::getCategory2, "," + customerCreditPaymentDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerCreditPaymentDTO.getCategory3()), CustomerCreditPaymentEntity::getCategory3, "," + customerCreditPaymentDTO.getCategory3() + ",")
                .orderByDesc(CustomerCreditPaymentEntity::getUpdatedAt)
                .orderByAsc(CustomerCreditPaymentEntity::getCompanyId)
        );
    }


    public List<CustomerCreditPaymentEntity> queryCustomerCreditPaymentAllList(CustomerCreditPaymentDTO customerCreditPaymentDTO) {

        return this.baseMapper.selectList(Wrappers.<CustomerCreditPaymentEntity>lambdaQuery()
                .eq(CustomerCreditPaymentEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CustomerCreditPaymentEntity::getCustomerId, customerCreditPaymentDTO.getCustomerId())
                .eq(CustomerCreditPaymentEntity::getCategoryId, customerCreditPaymentDTO.getCategoryId())
                .eq(null != customerCreditPaymentDTO.getIsSales(), CustomerCreditPaymentEntity::getIsSales, customerCreditPaymentDTO.getIsSales())
                .eq(null != customerCreditPaymentDTO.getIsProcurement(), CustomerCreditPaymentEntity::getIsProcurement, customerCreditPaymentDTO.getIsProcurement())
                .eq(null != customerCreditPaymentDTO.getStatus(), CustomerCreditPaymentEntity::getStatus, customerCreditPaymentDTO.getStatus())
                .eq(null != customerCreditPaymentDTO.getIsSales() && GeneralEnum.YES.getValue().equals(customerCreditPaymentDTO.getIsSales()), CustomerCreditPaymentEntity::getIsSales, customerCreditPaymentDTO.getIsSales())
                .eq(null != customerCreditPaymentDTO.getIsProcurement() && GeneralEnum.YES.getValue().equals(customerCreditPaymentDTO.getIsProcurement()), CustomerCreditPaymentEntity::getIsProcurement, customerCreditPaymentDTO.getIsProcurement())
                .eq(null != customerCreditPaymentDTO.getCompanyId(), CustomerCreditPaymentEntity::getCompanyId, customerCreditPaymentDTO.getCompanyId())
        );
    }
}
