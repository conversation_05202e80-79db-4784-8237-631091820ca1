package com.navigator.customer.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.columbus.CEmployPermissionFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.DictItemType;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.dao.*;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.*;
import com.navigator.customer.pojo.qo.CustomerQO;
import com.navigator.customer.pojo.vo.CustomerTemplateVO;
import com.navigator.customer.pojo.vo.CustomerVO;
import com.navigator.customer.pojo.vo.CustomerVerifyVO;
import com.navigator.customer.pojo.vo.SystemAndCustomerVO;
import com.navigator.customer.service.*;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class CustomerServiceImpl implements ICustomerService {

    @Resource
    private CustomerDao customerDao;
    @Resource
    private IContactService contactService;
    @Resource
    private DbtSignatureFacade dbtSignatureFacade;
    @Resource
    private ICustomerBankService customerBankService;
    @Resource
    private ICustomerDetailService customerDetailService;
    @Resource
    private CustomerProtocolService customerProtocolService;
    @Resource
    private CustomerOriginalPaperService customerOriginalPaperService;
    @Resource
    private CustomerDetailDao customerDetailDao;
    @Resource
    private CustomerBankDao customerBankDao;
    @Resource
    private IContactFactoryService contactFactoryService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private ContactDao contactDao;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private CustomerDepositRateDao customerDepositRateDao;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CEmployCustomerFacade cEmployCustomerFacade;
    @Resource
    private CEmployPermissionFacade cEmployPermissionFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Resource
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private DictItemFacade dictItemFacade;
    @Autowired
    private CrisGlobalDao crisGlobalDao;

    /**
     * 查询所有客户信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryCustomerList(QueryDTO<CustomerDTO> queryDTO) {
        IPage<CustomerEntity> iPage = customerDao.queryCustomerAll(queryDTO);
        List<CustomerVO> customerVOS = BeanConvertUtils.convert2List(CustomerVO.class, iPage.getRecords());
        customerVOS.stream().forEach(customerVO -> {
            Integer createdBy = Integer.valueOf(customerVO.getCreatedBy());
            EmployEntity employ = employFacade.getEmployById(createdBy);
            Integer systemUserId = 1;
            customerVO.setCreatedBy(systemUserId.equals(createdBy) ? "系统" : employ.getName());
        });
        /*for (CustomerVO customerVO : customerVOS) {
            //客户联系人信息
            List<ContactEntity> contactEntity = contactService.queryContactEntity(customerVO.getId(), ReferTypeEnum.CUSTOMER.getValue(), null);
            customerVO.setContactDTO(BeanConvertUtils.convert2List(ContactDTO.class, contactEntity));
            //客户配置信息
            List<CustomerDetailEntity> customerDetailEntities = customerDetailDao.queryCustomerIdList(customerVO.getId());
            customerVO.setCustomerDetailDTOS(BeanConvertUtils.convert2List(CustomerDetailDTO.class, customerDetailEntities));
            //客户银行信息
            List<CustomerBankEntity> customerBankEntities = customerBankDao.queryCustomerBankByCustomerId(customerVO.getId(), ReferTypeEnum.CUSTOMER.getValue(), null);
            customerVO.setCustomerBankDTOS(BeanConvertUtils.convert2List(CustomerBankDTO.class, customerBankEntities));

        }*/

        return Result.page(iPage, customerVOS);
    }

    @Override
    public List<CustomerEntity> queryCustomerEntityList(CustomerDTO customerDTO) {
        return customerDao.queryCustomerEntityList(customerDTO);
    }

    /**
     * 根据Id查询客户信息
     *
     * @param id
     * @return
     */
    @Override
    public CustomerDTO getCustomerById(Integer id) {
        CustomerEntity customerEntities = customerDao.queryCustomerById(id);
        if (null == customerEntities) {
            return null;
        }
        CustomerDTO customerDTO = BeanConvertUtils.convert(CustomerDTO.class, customerEntities);
        try {

            //查询客户联系方式
            List<ContactEntity> contactEntity = contactService.queryContactEntity(customerDTO.getId(), ReferTypeEnum.CUSTOMER.getValue(), null);
            customerDTO.setContactDTO(contactEntity);
            //查询客户账户下的银行卡号
            List<CustomerBankEntity> customerBankEntities = customerBankService.queryCustomerBankByCustomerId(id, ReferTypeEnum.CUSTOMER.getValue(), null);
            List<CustomerBankDTO> customerBankDTOS = BeanConvertUtils.convert2List(CustomerBankDTO.class, customerBankEntities);
            customerDTO.setCustomerBankDTOS(customerBankDTOS);
            //客户配置信息
            List<CustomerDetailEntity> customerDetailEntities = customerDetailService.queryCustomerIdList(customerDTO.getId());
            customerDTO.setCustomerDetailDTOS(BeanConvertUtils.convert2List(CustomerDetailDTO.class, customerDetailEntities));

        } catch (Exception e) {
            return null;
        }


        return customerDTO;
    }

    @Override
    public CustomerDTO queryCustomerByIdCategoryId(Integer id, Integer categoryId) {
        CustomerEntity customerEntities = customerDao.queryCustomerById(id);

        if (null == customerEntities) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }

        CustomerDTO customerDTO = BeanConvertUtils.convert(CustomerDTO.class, customerEntities);

        //查询客户联系方式
        List<ContactEntity> contactEntity = contactService.queryContactEntity(customerDTO.getId(), ReferTypeEnum.CUSTOMER.getValue(), categoryId);
        //List<ContactDTO> contactDTO = BeanConvertUtils.convert2List(ContactDTO.class, contactEntity);
        customerDTO.setContactDTO(contactEntity);

        //客户配置信息
        CustomerDetailEntity customerDetailEntities = customerDetailService.queryCustomerDetailList(customerDTO.getId(), categoryId);
        customerDTO.setCustomerDetailDTO(BeanConvertUtils.convert(CustomerDetailDTO.class, customerDetailEntities));
        return customerDTO;
    }

    @Override
    public CustomerEntity queryCustomerById(Integer id) {
        CustomerEntity customerEntity = customerDao.getBaseMapper().selectById(id);
        if (StringUtils.isNotBlank(customerEntity.getTemplateVipCode())) {
            DictItemEntity dictItemEntity = dictItemFacade.getDictItemByCode(DictItemType.ENTERPRISE_CODE.getValue(), customerEntity.getTemplateVipCode(), null);
            if (null != dictItemEntity) {
                if (DisableStatusEnum.ENABLE.getValue().equals(dictItemEntity.getStatus())) {
                    customerEntity.setTemplateVipName(dictItemEntity.getItemName());
                } else {
                    customerEntity.setTemplateVipCode("");
                }
            }
        }
        return customerEntity;
    }

    /**
     * 根据预警查询用户id
     *
     * @return
     */
    @Override
    public List<CustomerDTO> getCustomerEarlyWarning(Integer useYqq, Integer frameExpired, Integer originalPaper, Integer isColumbus, Integer templateExpired) {
        return customerDao.getCustomerEarlyWarning(useYqq, frameExpired, originalPaper, isColumbus, templateExpired);

    }

    @Override
    public boolean saveOrUpdateCustomer(CustomerEntity customerEntity) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null == customerEntity.getId()) {
            customerEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(currentUserId)
                    .setCreatedByName(name)
            ;
        }
        customerEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;

        return customerDao.saveOrUpdate(customerEntity);
    }


    @Override
    public Result queryCustomerSupplierAll(QueryDTO<CustomerDTO> queryDTO) {
        ObjectMapper mapper = new ObjectMapper();
        CustomerDTO customerDTO = mapper.convertValue(queryDTO.getCondition(), CustomerDTO.class);

        if (null != customerDTO.getSalesType() && ContractSalesTypeEnum.SALES.getValue() == customerDTO.getSalesType()) {

            if (null != customerDTO.getIsSupplier()) {
                customerDTO.setIsLdc(GeneralEnum.YES.getValue());
                customerDTO.setIsSupplier(null);
            }

        } else if (null != customerDTO.getSalesType() && ContractSalesTypeEnum.PURCHASE.getValue() == customerDTO.getSalesType()) {
            if (null != customerDTO.getIsSupplier()) {
                customerDTO.setIsLdc(GeneralEnum.YES.getValue());
                customerDTO.setIsSupplier(null);


            } else if (null != customerDTO.getIsCustomer()) {
                customerDTO.setIsSupplier(GeneralEnum.YES.getValue());
                customerDTO.setIsCustomer(null);
            }

        }
        queryDTO.setCondition(customerDTO);
        IPage<CustomerEntity> iPage = customerDao.queryCustomerSupplierAll(queryDTO);

        List<CustomerDTO> customerDTOS = iPage.getRecords().stream().map(customerEntity -> {

            CustomerDTO dto = new CustomerDTO();

            BeanUtil.copyProperties(customerEntity, dto);

            return dto;

        }).collect(Collectors.toList());

        return Result.page(iPage, customerDTOS);
    }

    @Override
    public Integer customerSignature(Integer id) throws Exception {
        CustomerEntity customerEntity = customerDao.queryCustomerById(id);
        //查看客户是否需要用易企签
        if (customerEntity.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue())) {
            //查询客户联系人信息
            List<ContactEntity> contactEntities = contactService.queryContactEntity(id, ReferTypeEnum.CUSTOMER.getValue(), null);
            for (ContactEntity contactEntity : contactEntities) {
                //查询签章联系人
                if (contactEntity.getYqqAuth().equals(YqqAuthEnum.AUTH.getValue()) && contactEntity.getContactType().equals(ContactTypeEnum.SIGNATURE_CONTACT.getValue())) {
                    //已实名直接返回
                    return CustomerSignatureEnum.AUTH.getValue();
                } else if (contactEntity.getYqqAuth().equals(YqqAuthEnum.NOT_AUTH.getValue()) && contactEntity.getContactType().equals(ContactTypeEnum.SIGNATURE_CONTACT.getValue())) {
                    //记录中未实名,但使用易企签,进行易企签实名查询
                    boolean num = dbtSignatureFacade.hasAuthentication(new CheckRequestDTO().setPhone(contactEntity.getContactPhone()).setCustomTag(customerEntity.getLinkageCustomerCode()).setCustomerId(id));
                    return num == true ? CustomerSignatureEnum.AUTH.getValue() : CustomerSignatureEnum.NOT_AUTH.getValue();
                }
            }
        }
        return CustomerSignatureEnum.NOT_USE_YQQ.getValue();
    }

    @Override
    public Result querySupplierList(Integer isSupplier) {
        return customerDao.querySupplierList(isSupplier);
    }


    @Override
    public Result queryCustomerAll() {
        return Result.success(customerDao.list(Wrappers.<CustomerEntity>lambdaQuery().eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())));
    }


    @Override
    public SystemAndCustomerVO getSystemAndCustomerById(Integer customerId) {
        //根据客户Id查询出客户信息
        CustomerEntity customerEntity = customerDao.getById(customerId);
        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }
        SystemAndCustomerVO customerVO = new SystemAndCustomerVO();
        customerVO.setCustomerId(customerEntity.getId()).setIsColumbus(customerEntity.getIsColumbus()).setUseYqq(customerEntity.getUseYqq()).setModifyStatus(1);

        //根据客户id查询员工账号
        Result resultData = cEmployFacade.getEmployByCustomerId(customerEntity.getId(), CEmployTypeEnum.DEFAULT.getType());
        List<CEmployEntity> cEmployEntityList = JSON.parseArray(JSON.toJSONString(resultData.getData()), CEmployEntity.class);
        if (CollectionUtils.isNotEmpty(cEmployEntityList)) {
            CEmployEntity cEmployEntity = cEmployEntityList.get(0);
            customerVO.setPhone(cEmployEntity.getPhone()).setName(cEmployEntity.getName()).setEmail(cEmployEntity.getEmail()).setEmployId(cEmployEntity.getId()).setModifyStatus(0);
        }
        return customerVO;
    }

    @Override
    public void updateSystemAndCustomer(SystemAndCustomerDTO systemAndCustomerDTO) {
        //根据客户id 查询客户信息
        CustomerEntity customerEntity = customerDao.queryCustomerById(systemAndCustomerDTO.getCustomerId());
        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }
        CustomerEntity originalCustomerEntity = new CustomerEntity();
        BeanUtils.copyProperties(customerEntity, originalCustomerEntity);
        String phone = systemAndCustomerDTO.getPhone() == null ? "" : systemAndCustomerDTO.getPhone().trim();
        String email = systemAndCustomerDTO.getEmail() == null ? "" : systemAndCustomerDTO.getEmail().trim();
        String name = systemAndCustomerDTO.getName() == null ? "" : systemAndCustomerDTO.getName().trim();


        if (StringUtil.isNotBlank(phone)) {
            //是否初始管理员本身


            Result result = cEmployFacade.queryEmployByPhone(phone);
            List<CEmployEntity> cEmployEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), CEmployEntity.class);
            //List<CEmployEntity> cEmployEntityList1 = cEmployEntityList.stream().filter(i -> customerEntity.getLinkageCustomerCode().equals(i.getCustomerCode())).collect(Collectors.toList());

            //判断账号是否尊在
            if (!cEmployEntityList.isEmpty()) {

                CEmployEntity cEmployEntity = cEmployEntityList.get(0);

                //已经注册的账账号查看是否绑定该主体
                List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.getCEmployCustomerByEmployIdAndCustomerId(cEmployEntity.getId(), systemAndCustomerDTO.getCustomerId());
                //1,该手机号和该主体绑定,且非主管理员抛出异常给前端
                //2,取消之前主管理员权限
                //3,赋值新管理员给该手机号用户

                //判断是否与该主体绑定
                if (!cEmployCustomerEntities.isEmpty()) {
                    //判断是否是该主体下用户
                    CEmployCustomerEntity cEmployCustomerEntity = cEmployCustomerEntities.get(0);
                    if (CEmployTypeEnum.OTHER.getType() == cEmployCustomerEntity.getType()) {
                        throw new BusinessException(ResultCodeEnum.C_EMPLOY_CUSTOMER_BINDING);
                    }

                    //已绑定该主体,且管理员
                    /*if (!cEmployEntity.getEmail().equalsIgnoreCase(email)) {
                        Result employByEmail = cEmployFacade.getEmployByEmail(email);
                        List<CEmployEntity> cEmployEntities = JSON.parseArray(JSON.toJSONString(employByEmail.getData()), CEmployEntity.class);
                        if (CollectionUtils.isNotEmpty(cEmployEntities)) {
                            throw new BusinessException(ResultCodeEnum.EMAIL_ERROR);
                        }
                    }*/
                    cEmployEntity
                            .setName(name)
                            .setEmail(email)
                            .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                            .setUpdatedAt(new Date())
                    ;
                    //修改信息
                    cEmployFacade.updateCEmploy(cEmployEntity);

                    //删除绑定数据
                    cEmployCustomerEntity.setStatus(DisableStatusEnum.ENABLE.getValue());
                    cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity);
                } else {
                    //未绑定该主体
                    newCEmployBindingCustomer(customerEntity, systemAndCustomerDTO, cEmployEntity);
                }
            } else {
                //没有注册的账号线注册账号
                /*Result resultData1 = cEmployFacade.getEmployByEmail(email);
                List<CEmployEntity> list2 = JSON.parseArray(JSON.toJSONString(resultData1.getData()), CEmployEntity.class);
                //判断邮箱是否存在
                if (CollectionUtils.isNotEmpty(list2)) {
                    throw new BusinessException(ResultCodeEnum.EMAIL_ERROR);
                }*/

                //查询该客户下的主管理员
                List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.queryCEmployCustomerByCustomerIdAndType(systemAndCustomerDTO.getCustomerId(), CEmployTypeEnum.DEFAULT.getType(), null);
                //处理掉以前的主管理员
                for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {
                    //解除主管理员
                    cEmployCustomerEntity.setType(CEmployTypeEnum.OTHER.getType());
                    cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity);
                }
                List<Integer> cEmployId = cEmployCustomerEntities.stream().map(CEmployCustomerEntity::getCEmployId).collect(Collectors.toList());
                cEmployFacade.updateTypeByIds(cEmployId);

                //手机未注册新增账号
                Integer employId = addCEmploy(email, customerEntity, name, phone, systemAndCustomerDTO);
                //发送邮件
                try {
                    sendEmail(employId, name, customerEntity.getName());
                } catch (Exception e) {
                    log.debug("updateSystemAndCustomer:sendEmail:{}", e.getMessage());
                }

                //绑定管理员角色
                saveCEmployAdminRole(employId, systemAndCustomerDTO.getCustomerId());

                /*CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
                cEmployRoleEntity
                        .setEmployId(employId)
                        .setCustomerId(systemAndCustomerDTO.getCustomerId())
                        .setRoleDefId(1)
                        .setRoleId(1)
                        .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                        .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                ;
                cEmployPermissionFacade.save(cEmployRoleEntity);*/
            }
        }


        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.getCEmployCustomerByEmployIdAndCustomerId(null, systemAndCustomerDTO.getCustomerId());

        if (!cEmployCustomerEntities.isEmpty()) {
            for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {

                if (GeneralEnum.NO.getValue().equals(systemAndCustomerDTO.getIsColumbus())) {
                    cEmployCustomerEntity.setStatus(DisableStatusEnum.DISABLE.getValue());
                } else {
                    cEmployCustomerEntity.setStatus(DisableStatusEnum.ENABLE.getValue());
                }
                cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity);
            }
        }


        customerEntity.setIsColumbus(systemAndCustomerDTO.getIsColumbus()).setUseYqq(systemAndCustomerDTO.getUseYqq());
        customerDao.updateById(customerEntity);

        CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
        customerDetailUpdateRecordEntity.setData(JSON.toJSONString(customerEntity));

        //记录修改客户主数据人
        customerDetailUpdateRecordEntity
                .setDetailCode(systemAndCustomerDTO.getDetailCode())
                .setCustomerId(systemAndCustomerDTO.getCustomerId())
                .setData(JSON.toJSONString(systemAndCustomerDTO))
                .setCreatedAt(new Date())
                .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(systemAndCustomerDTO))
                    .setBeforeData(JSON.toJSONString(originalCustomerEntity))
                    .setAfterData(JSON.toJSONString(customerEntity))
                    .setOperationActionEnum(OperationActionEnum.UPDATE_SYSTEM_AND_CUSTOMER);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Integer addCEmploy(String email, CustomerEntity customerEntity, String name, String phone, SystemAndCustomerDTO systemAndCustomerDTO) {
        //更改原初始管理员的类型
        Result result1 = cEmployFacade.getEmployByCustomerId(customerEntity.getId(), CEmployTypeEnum.DEFAULT.getType());
        List<CEmployEntity> list1 = JSON.parseArray(JSON.toJSONString(result1.getData()), CEmployEntity.class);
        List<Integer> ids = list1.stream().map(CEmployEntity::getId).collect(Collectors.toList());
        cEmployFacade.updateTypeByIds(ids);

        CEmployEntity cEmployEntity = new CEmployEntity();
        cEmployEntity
                .setPassword(new BCryptPasswordEncoder().encode("111111"))
                .setType(CEmployTypeEnum.DEFAULT.getType())
                .setUpdatedPasswordTime(DateTimeUtil.now())
                .setName(name)
                .setRealName(name)
                .setEmail(email)
                .setPhone(phone)
                .setCustomerId(systemAndCustomerDTO.getCustomerId())
                .setParentCustomerId(customerEntity.getParentId())
                .setRootCustomerId(customerEntity.getParentId())
                .setEnterpriseName(customerEntity.getEnterpriseName())
                .setCustomerName(customerEntity.getName())
                .setCustomerCode(customerEntity.getLinkageCustomerCode())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        Integer cEmployId = cEmployFacade.saveOrUpdate(cEmployEntity);

        //绑定客户主体
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.getCEmployCustomerByEmployIdAndCustomerId(cEmployId, customerEntity.getId());

        if (cEmployCustomerEntities.isEmpty()) {
            CEmployCustomerEntity cEmployCustomerEntity = BeanConvertUtils.convert(CEmployCustomerEntity.class, cEmployEntity);
            cEmployCustomerEntity
                    .setId(null)
                    .setType(CEmployTypeEnum.DEFAULT.getType())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCEmployId(cEmployId)
            ;
            cEmployCustomerFacade.saveCEmployCustomer(cEmployCustomerEntity);
        }

        return cEmployId;
    }

    /**
     * 账号已存在绑定
     *
     * @param systemAndCustomerDTO
     * @return
     */
    public Integer newCEmployBindingCustomer(CustomerEntity customerEntity, SystemAndCustomerDTO systemAndCustomerDTO, CEmployEntity cEmployEntity) {
        //取消原绑定的手机号
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.getCEmployCustomerByEmployIdAndCustomerId(null, systemAndCustomerDTO.getCustomerId());
        for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {
            //删除绑定数据
            cEmployCustomerEntity.setType(CEmployTypeEnum.OTHER.getType());
            cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity);
        }
        //将新手机号进行绑定
        CEmployCustomerEntity cEmployCustomerEntity = new CEmployCustomerEntity();
        cEmployCustomerEntity
                .setParentCustomerId(customerEntity.getParentId())
                .setCustomerId(customerEntity.getId())
                .setCEmployId(cEmployEntity.getId())
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setType(CEmployTypeEnum.DEFAULT.getType())
                .setUpdatedAt(new Date())
                .setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        cEmployCustomerFacade.saveCEmployCustomer(cEmployCustomerEntity);

        cEmployEntity
                .setName(systemAndCustomerDTO.getName())
                .setEmail(systemAndCustomerDTO.getEmail());
        cEmployFacade.updateCEmploy(cEmployEntity);
        //绑定管理员角色
        saveCEmployAdminRole(cEmployEntity.getId(), systemAndCustomerDTO.getCustomerId());

        return cEmployEntity.getId();
    }


    public void saveCEmployAdminRole(Integer cEmployId, Integer customerId) {
        CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
        cEmployRoleEntity
                .setEmployId(cEmployId)
                .setCustomerId(customerId)
                .setRoleDefId(1)
                .setRoleId(1)
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
        ;
        cEmployPermissionFacade.save(cEmployRoleEntity);
    }

    @Override
    public boolean updateCustomer(CustomerEntity customerEntity) {
        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }
        return customerDao.updateById(customerEntity);
    }

    @Override
    public boolean addCustomer(CustomerEntity customerEntity) {
        customerDao.save(customerEntity);
        return true;
    }

    @Override
    public CustomerTemplateVO queryTemplateContactFactoryByCustomerId(Integer customerId, Integer categoryId) {

        CustomerTemplateVO customerTemplateVO = new CustomerTemplateVO();

        //根据客户品类id查询出联系人
        List<ContactEntity> contactEntityS = contactService.queryContactEntity(customerId, null, categoryId);

        if (!contactEntityS.isEmpty()) {
            List<CustomerTemplateVO.ContactFactory> contactFactoryS = new ArrayList<>();
            for (ContactEntity contactEntity : contactEntityS) {
                //填充信息联系人
                CustomerTemplateVO.ContactFactory contactFactory = new CustomerTemplateVO.ContactFactory().setAddress(contactEntity.getAddress()).setContactId(contactEntity.getId()).setContactName(contactEntity.getContactName()).setEmail(contactEntity.getEmail()).setReferType(contactEntity.getReferType()).setContactPhone(contactEntity.getContactPhone());

                //查询联系人适用的油厂
                List<ContactFactoryEntity> contactFactoryEntities = contactFactoryService.queryContactFactoryByContactId(contactEntity.getId());

                if (!contactFactoryEntities.isEmpty()) {

                    List<CustomerTemplateVO.ApplyFactory> applyFactories = new ArrayList<>();
                    for (ContactFactoryEntity contactFactoryEntity : contactFactoryEntities) {
                        //填充油厂信息
                        FactoryEntity factoryEntity = factoryService.getFactoryDetailById(contactFactoryEntity.getFactoryId());

                        CustomerTemplateVO.ApplyFactory applyFactory = new CustomerTemplateVO.ApplyFactory().setFactoryId(factoryEntity.getId()).setShortName(factoryEntity.getShortName());

                        applyFactories.add(applyFactory);

                    }
                    //通知人 负责工厂 List Add
                    contactFactory.setApplyFactories(applyFactories);

                }
                //通知人List Add
                contactFactoryS.add(contactFactory);
            }
            customerTemplateVO.setContactFactories(contactFactoryS);
        }
        return customerTemplateVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplateContactFactory(CustomerTemplateDTO customerTemplateDTO) {

        Integer customerId = customerTemplateDTO.getCustomerId();
        Integer categoryId = customerTemplateDTO.getCategoryId();
        List<ContactEntity> contactEntities = contactService.getCustomerByCustomerId(customerId, null, categoryId);

        for (ContactEntity contactEntity : contactEntities) {
            contactService.deleteContactFactoryEntityBy(contactEntity.getId());
        }


        //通知人信息
        List<CustomerTemplateDTO.ContactFactory> contactFactoryS = customerTemplateDTO.getContactFactories();


        if (null != contactFactoryS) {
            contactService.contactFactoryList(contactFactoryS, customerTemplateDTO);
        }
        try {
            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
            //记录修改客户主数据人
            customerDetailUpdateRecordEntity.setDetailCode(customerTemplateDTO.getDetailCode()).setCustomerId(customerId).setCategoryId(categoryId).setData(JSON.toJSONString(customerTemplateDTO)).setCreatedAt(new Date()).setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerTemplateDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(customerDetailUpdateRecordEntity))
                    .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_TEMPLATE_CONTACT_FACTORY)
            ;

            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * 根据客户id查询 客户是否使用系统,是否使用易企签,是否实名  易企签通用配置是否启用
     *
     * @param customerId
     * @return
     */
    @Override
    public Result customerSignParameter(Integer customerId) {

        //根据id查询客户信息
        CustomerEntity customerEntity = customerDao.getById(customerId);
        //根据客户id查询客户是否实名
//        EmployEntity employEntity = employFacade.queryEmployByCustomerId(customerId);
        //查询签章通用状态是否开启
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        SystemRuleVO.SystemRuleItemVO systemRuleItemVO = systemRuleVO.getSystemRuleItemVOList().get(0);

        CustomerSignParameterDTO customerSignParameterDTO = new CustomerSignParameterDTO();
        customerSignParameterDTO.setCustomerId(customerEntity.getId()).setUseYqq(customerEntity.getUseYqq()).setIsColumbus(customerEntity.getIsColumbus())
//                .setYqqAuth(null != employEntity ? employEntity.getYqqAuth() : YqqAuthEnum.NOT_AUTH.getValue())
                .setYqqStatus(systemRuleItemVO.getStatus());

        return Result.success(customerSignParameterDTO);
    }

    /**
     * 导入客户数据校验
     *
     * @param file
     * @return
     */
    @Override
    public CustomerVerifyVO verifyFileCustomerConfig(MultipartFile file) {

        CustomerVerifyVO customerVerifyVO = new CustomerVerifyVO();

        List<CustomerTemplateDeriveDTO> customerTemplateDeriveDTOS = new ArrayList<>();

        List<CustomerTemplateDeriveDTO> customerTemplateDeriveDTOs = EasyPoiUtils.importExcel(file, 0, 1, CustomerTemplateDeriveDTO.class);

        Integer errorNum = 0;
        Integer allNum = 0;

        // 根据指定属性分组，并统计数量（key：指定属性，value：数量）
        Map<Object, Long> mapCustomer = customerTemplateDeriveDTOs.stream().filter(customerTemplateDeriveDTO -> StringUtil.isNotBlank(customerTemplateDeriveDTO.getLinkageCustomerCode())).collect(Collectors.groupingBy(CustomerTemplateDeriveDTO::getLinkageCustomerCode, Collectors.counting()));

        // 筛选Map中value大于1的key
        List<Object> stringStream = new ArrayList<>();
        for (Map.Entry<Object, Long> entry : mapCustomer.entrySet()) {
            if (entry.getValue() > 1) {
                Object key = entry.getKey();
                stringStream.add(key);
            }
        }

        //校验数据
        for (CustomerTemplateDeriveDTO customerTemplateDeriveDTO : customerTemplateDeriveDTOs) {
            allNum++;
            String mistakeCause = "";
            if (StrUtil.isNotBlank(customerTemplateDeriveDTO.getLinkageCustomerCode()) && stringStream.contains(customerTemplateDeriveDTO.getLinkageCustomerCode())) {
                mistakeCause += "客户编码重复";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }
            if (StrUtil.isBlank(customerTemplateDeriveDTO.getSignPlace())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getShortName())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getStatus())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getIsAuthorization())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getIsSupplier())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getEnterprise())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getLinkageCustomerCode())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getCustomerName())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getCustomerIndexesName())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getFullName())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getFullNameEnglish())
                    || StrUtil.isBlank(customerTemplateDeriveDTO.getAddress())) {

                mistakeCause += "请将信息填写完整";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);

                errorNum++;
                continue;
            }

            if (null != customerDao.queryCustomerByLinkageCustomerCode(customerTemplateDeriveDTO.getLinkageCustomerCode())) {
                mistakeCause += "该客户已在系统中，请勿重复录入";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }

            if (!"有效".equals(customerTemplateDeriveDTO.getStatus()) && !"无效".equals(customerTemplateDeriveDTO.getStatus())) {
                mistakeCause += "客户状态填写错误!请按照规则正确填写数据!";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;

            }
            List<String> judgeContentList = Arrays.asList("是", "否");
            if (!judgeContentList.contains(customerTemplateDeriveDTO.getIsSupplier())) {
                mistakeCause += "是否是供应商填写错误!请按照规则正确填写数据!";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }
            if (!judgeContentList.contains(customerTemplateDeriveDTO.getIsAuthorization())) {
                mistakeCause += "是否有授权书!请按照规则正确填写数据!";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }
            if (StringUtil.isNotBlank(customerTemplateDeriveDTO.getEnterpriseName()) && StringUtil.isBlank(customerTemplateDeriveDTO.getEnterpriseCode())) {
                mistakeCause = "集团客户编码不能为空";
                customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }
            if (StringUtil.isNotBlank(customerTemplateDeriveDTO.getEnterpriseCode())) {
                CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerTemplateDeriveDTO.getEnterpriseCode());
                if (null == customerEntity) {
                    mistakeCause += "集团客户编码为" + customerTemplateDeriveDTO.getEnterpriseCode() + "的客户不存在";
                    customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                    customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                    errorNum++;
                    continue;
                }
                if (StringUtil.isBlank(customerTemplateDeriveDTO.getEnterpriseName())) {
                    customerTemplateDeriveDTO.setEnterpriseName(customerEntity.getName());
                    customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                    continue;
                }
                if (!customerTemplateDeriveDTO.getEnterpriseName().trim().equals(customerEntity.getName().trim())) {
                    mistakeCause = "集团客户代码和集团客户名称不匹配";
                    customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                    customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                    errorNum++;
                }
                if (GeneralEnum.NO.getDescription().equals(customerTemplateDeriveDTO.getEnterprise())) {
                    mistakeCause = "是否是集团客户信息填写错误";
                    customerTemplateDeriveDTO.setMistakeCause(mistakeCause);
                    customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                    errorNum++;
                }
            }
        }

        customerVerifyVO.setCustomerTemplateDeriveDTOS(customerTemplateDeriveDTOS).setErrorNum(errorNum).setAllNum(allNum);
        return customerVerifyVO;
    }

    @Override
    public Result importDataCustomerFile(MultipartFile file) {
        List<CustomerConfigDTO> customerConfigDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerConfigDTO.class);
        CustomerVerifyVO customerVerifyVO = new CustomerVerifyVO();
        Integer errorNum = 0;
        Integer allNum = 0;
        List<CustomerTemplateDeriveDTO> customerTemplateDeriveDTOS = new ArrayList<>();
        for (CustomerConfigDTO customerConfigDTO : customerConfigDTOS) {
            allNum++;
            String mistakeCause = "";
            if (StrUtil.isBlank(customerConfigDTO.getLinkageCustomerCode())) {

                CustomerTemplateDeriveDTO customerTemplateDeriveDTO = BeanConvertUtils.convert(CustomerTemplateDeriveDTO.class, customerConfigDTO);
                customerTemplateDeriveDTOS.add(customerTemplateDeriveDTO);
                errorNum++;
                continue;
            }
            this.addCustomerDTO(customerConfigDTO);
        }
        return Result.success(customerVerifyVO);
    }


    //添加客户信息及配置
    @Override
    public String saveCustomerConfig(MultipartFile file) {

        List<CustomerConfigDTO> customerConfigDTOArrayList = EasyPoiUtils.importExcel(file, 0, 1, CustomerConfigDTO.class);

        // 根据指定属性分组，并统计数量（key：指定属性，value：数量）
        Map<Object, Long> mapCustomer = customerConfigDTOArrayList.stream().filter(customerConfigDTO -> StringUtil.isNotBlank(customerConfigDTO.getLinkageCustomerCode())).collect(Collectors.groupingBy(CustomerConfigDTO::getLinkageCustomerCode, Collectors.counting()));

        // 筛选Map中value大于1的key
        List<Object> stringStream = new ArrayList<>();
        for (Map.Entry<Object, Long> entry : mapCustomer.entrySet()) {
            if (entry.getValue() > 1) {
                Object key = entry.getKey();
                stringStream.add(key);
            }
        }

        Integer saveNum = 0;
        Integer uptNum = 0;
        Integer abnormalNum = 0;
        for (CustomerConfigDTO customerConfigDTO : customerConfigDTOArrayList) {

            boolean b = false;
            if (StrUtil.isNotBlank(customerConfigDTO.getLinkageCustomerCode()) && stringStream.contains(customerConfigDTO.getLinkageCustomerCode())) {
                abnormalNum++;
                continue;
            }

            if (StrUtil.isBlank(customerConfigDTO.getStatus())
                    || StrUtil.isBlank(customerConfigDTO.getIsAuthorization())
                    || StrUtil.isBlank(customerConfigDTO.getIsSupplier())
                    || StrUtil.isBlank(customerConfigDTO.getEnterprise())
                    || StrUtil.isBlank(customerConfigDTO.getLinkageCustomerCode())
                    || StrUtil.isBlank(customerConfigDTO.getCustomerName())
                    || StrUtil.isBlank(customerConfigDTO.getCustomerIndexesName())
                    || StrUtil.isBlank(customerConfigDTO.getFullName())
                    || StrUtil.isBlank(customerConfigDTO.getFullNameEnglish())
                    || StrUtil.isBlank(customerConfigDTO.getAddress())) {
                abnormalNum++;
                continue;
            }

            if (!"有效".equals(customerConfigDTO.getStatus()) && !"无效".equals(customerConfigDTO.getStatus())) {
                abnormalNum++;
                continue;
            }
            List<String> judgeContentList = Arrays.asList("是", "否");
            if (!judgeContentList.contains(customerConfigDTO.getIsSupplier()) || !judgeContentList.contains(customerConfigDTO.getIsAuthorization())) {
                abnormalNum++;
                continue;
            }
            if (StringUtil.isNotBlank(customerConfigDTO.getEnterpriseName()) && StringUtil.isBlank(customerConfigDTO.getEnterpriseCode())) {
                abnormalNum++;
                continue;
            }
            if (StringUtil.isNotBlank(customerConfigDTO.getEnterpriseCode())) {
                CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerConfigDTO.getEnterpriseCode());
                if (null == customerEntity) {
                    abnormalNum++;
                    continue;
                }
                if (StringUtil.isNotBlank(customerConfigDTO.getEnterpriseName()) && !customerConfigDTO.getEnterpriseName().trim().equals(customerEntity.getName().trim())) {
                    abnormalNum++;
                    continue;
                }
                if (GeneralEnum.NO.getDescription().equals(customerConfigDTO.getEnterprise())) {
                    abnormalNum++;
                    continue;
                }
            }
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerConfigDTO.getLinkageCustomerCode());

            customerConfigDTO.setLinkageCustomerCode(customerConfigDTO.getLinkageCustomerCode().trim());

            CustomerEntity customerEntity1 = this.addCustomerDTO(customerConfigDTO);
            //保存客户数据
            if (null == customerEntity) {
                //保存客户配置信息(豆粕)
                this.addCustomerDetail(customerEntity1.getId(), GoodsCategoryEnum.OSM_MEAL.getValue());
                //保存客户配置信息(豆油)
                this.addCustomerDetail(customerEntity1.getId(), GoodsCategoryEnum.OSM_OIL.getValue());
                saveNum++;
            } else {
                uptNum++;
            }
        }
        return "新增" + saveNum + "条，更新" + uptNum + "条，异常" + abnormalNum + "条";
    }

    /**
     * 填充客户信息
     *
     * @param customerConfigDTO
     * @return
     */
    private CustomerEntity addCustomerDTO(CustomerConfigDTO customerConfigDTO) {
        Integer enterprise = null;
        log.info("客户数据" + customerConfigDTO.toString());
        CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerConfigDTO.getLinkageCustomerCode());
        if (null == customerEntity) {
            customerEntity = BeanConvertUtils.convert(CustomerEntity.class, customerConfigDTO);
        } else {
            BeanConvertUtils.copy(customerEntity, customerConfigDTO);
        }

        /*String customerName = customerEntity.getName().replace("(","（");
        customerName = customerName.replace(")","）");

        customerEntity.setName(customerName);*/

        if (StrUtil.isNotBlank(customerConfigDTO.getEnterpriseCode())) {
            enterprise = EnterpriseEnum.YES_ENTERPRISE.getValue();

            CustomerEntity customerEnterpriseEntity = customerDao.queryCustomerByLinkageCustomerCode(customerConfigDTO.getEnterpriseCode());

            if (null != customerEnterpriseEntity) {
                customerEntity
                        .setParentId(customerEnterpriseEntity.getId())
                        .setEnterpriseName(customerConfigDTO.getEnterpriseName())
                        .setEnterpriseCustomerCode(customerConfigDTO.getEnterpriseCode());
            }
        }

        if ("有效".equals(customerConfigDTO.getStatus())) {
            customerEntity.setStatus(DisableStatusEnum.ENABLE.getValue());
        } else {
            customerEntity.setStatus(DisableStatusEnum.DISABLE.getValue());
        }

        if (GeneralEnum.YES.getDescription().equals(customerConfigDTO.getIsAuthorization())) {
            customerEntity.setIsAuthorization(GeneralEnum.YES.getValue());
        } else {
            customerEntity.setIsAuthorization(GeneralEnum.NO.getValue());
        }

        if (GeneralEnum.YES.getDescription().equals(customerConfigDTO.getIsSupplier())) {
            customerEntity.setIsSupplier(GeneralEnum.YES.getValue());
        } else {
            customerEntity.setIsSupplier(GeneralEnum.NO.getValue());
        }

        if (GeneralEnum.YES.getDescription().equals(customerConfigDTO.getEnterprise())) {
            customerEntity.setEnterprise(GeneralEnum.YES.getValue());
        } else {
            customerEntity.setEnterprise(GeneralEnum.NO.getValue());
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerEntity.setEnterprise(enterprise).setName(customerConfigDTO.getCustomerName());

        if (null == customerEntity.getId()) {
            customerEntity.setCreatedAt(DateTimeUtil.now()).setCreatedBy(currentUserId).setCreatedByName(name).setUpdatedAt(DateTimeUtil.now()).setUpdatedBy(currentUserId).setUpdatedByName(name);

            customerDao.save(customerEntity);
            if (StringUtil.isNotBlank(customerEntity.getEnterpriseName()) && customerEntity.getName().equals(customerEntity.getEnterpriseName())) {
                customerEntity.setParentId(customerEntity.getId());
                customerDao.updateById(customerEntity);
            }
        } else {
            if (StringUtils.isBlank(customerConfigDTO.getEnterpriseCode()) && StringUtils.isBlank(customerConfigDTO.getEnterpriseName())) {
                customerEntity.setEnterpriseName("").setParentId(0);
            }
            customerEntity.setUpdatedAt(DateTimeUtil.now()).setUpdatedBy(JwtUtils.getCurrentUserId()).setUpdatedByName(name);
            customerDao.updateById(customerEntity);
        }
        customerEntity
                .setMdmCustomerCode(customerConfigDTO.getLinkageCustomerCode())
                .setLinkageCustomerCode(customerConfigDTO.getLinkageCustomerCode());
        return customerEntity;
    }

    /**
     * 保存客户银行信息
     *
     * @param customerConfigDTO
     * @param customerId
     * @param categoryId
     */
    private boolean addCustomerBank(CustomerConfigDTO customerConfigDTO, Integer customerId, Integer categoryId) {
        CustomerBankEntity customerBankEntity = new CustomerBankEntity()
                .setCustomerId(customerId)
                .setCategoryId(categoryId)
                .setBankAccountName(customerConfigDTO.getCustomerName())
                .setBankAccountNo(customerConfigDTO.getBankAccountNo())
                .setBankName(customerConfigDTO.getBankName())
                .setReferType(ReferTypeEnum.CUSTOMER.getValue())
                .setUseType(BankUseTypeEnum.COLLECTION_PAYMENT.getValue());

        return customerBankDao.save(customerBankEntity);

    }

    /**
     * 保存客户配置
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    private boolean addCustomerDetail(Integer customerId, Integer categoryId) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        CustomerDetailEntity customerDetailEntity = new CustomerDetailEntity()
                .setCustomerId(customerId)
                .setCategoryId(categoryId)
                .setCreatedBy(currentUserId)
                .setCreatedByName(name)
                .setUpdatedBy(currentUserId)
                .setCompanyId(1)
                .setCompanyName("TJIB")
                .setUpdatedByName(name);
        return customerDetailDao.save(customerDetailEntity);
    }

    private boolean addCustomerContact(CustomerConfigDTO customerConfigDTO, Integer customerId, Integer categoryId) {
        ContactEntity contactEntity = new ContactEntity()
                .setReferType(ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue())
                .setReferId(customerId.toString())
                .setCategoryId(categoryId)
                .setContactName(customerConfigDTO.getContact())
                .setContactPhone(customerConfigDTO.getPhone())
                .setEmail(customerConfigDTO.getEmail());
        return contactDao.save(contactEntity);
    }

    /**
     * 保存客户账期
     *
     * @param file
     * @return
     */
    public boolean addCustomerCreditDays(MultipartFile file) {
        List<CustomerCreditDaysDTO> customerCreditDaysDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerCreditDaysDTO.class);

        for (CustomerCreditDaysDTO customerCreditDaysDTO : customerCreditDaysDTOS) {
            if (null != customerCreditDaysDTO.getLinkageCustomerCode()) {
                //根据Linkag编号查询客户信息
                CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerCreditDaysDTO.getLinkageCustomerCode());

                customerEntity
                        .setCreditDays(customerCreditDaysDTO.getCreditDays())
                        .setPaymentType(customerCreditDaysDTO.getCreditDays() > 0 ? PaymentTypeEnum.CREDIT.getType() : PaymentTypeEnum.IMPREST.getType());

                customerDao.updateById(customerEntity);
            }

        }
        return true;
    }

    /**
     * 查询客户所有数据
     *
     * @param customerAllMessageDTO
     * @return
     */
    @Override
    public CustomerDTO queryCustomerAllMessage(CustomerAllMessageDTO customerAllMessageDTO) {

        //查询客户信息
        CustomerEntity customerEntity = this.queryCustomerById(customerAllMessageDTO.getCustomerId());

        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }

        CustomerDTO customerDTO = BeanConvertUtils.convert(CustomerDTO.class, customerEntity);

        //根据客户 品类查询客户配置
        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
        customerDetailBO
                .setCustomerId(customerAllMessageDTO.getCustomerId())
                .setCategory2(String.valueOf(customerAllMessageDTO.getCategory2()))
                .setCategory3(String.valueOf(customerAllMessageDTO.getCategory3()))
        ;
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailDao.queryCustomerDetailListByCondition(customerDetailBO);
        if (!customerDetailEntityList.isEmpty()) {
            customerDTO.setCustomerDetailDTO(BeanConvertUtils.convert(CustomerDetailDTO.class, customerDetailEntityList.get(0)));
        }


       /* //查询客户保证金
        CustomerDepositRateBO customerDepositRateBO = new CustomerDepositRateBO()
                .setCustomerId(customerAllMessageDTO.getCustomerId())
                .setCategoryId(customerAllMessageDTO.getCategoryId())
                .setSalesType(customerAllMessageDTO.getSalesType())
                .setStatus(DisableStatusEnum.ENABLE.getValue());
        List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateDao.getCustomerDepositRateByCustomerId(customerDepositRateBO);
        //保存客户保证金
        customerDTO.setCustomerDepositRateDTOS(customerDepositRateEntities);*/

        if (null != customerAllMessageDTO.getFactoryId() || StrUtil.isNotBlank(customerAllMessageDTO.getFactoryCode())) {
            //客户银行信息
            Integer factoryId = customerAllMessageDTO.getFactoryId();
            if (null == factoryId) {
                factoryId = factoryService.getFactoryByCode(customerAllMessageDTO.getFactoryCode()).getId();
            }
            Integer bankUseTyp = BankUseTypeEnum.COLLECTION.getValue();

            if (GeneralEnum.NO.getValue() == customerEntity.getIsLdc()) {
                bankUseTyp = ContractSalesTypeEnum.PURCHASE.getValue() == customerAllMessageDTO.getSalesType() ? BankUseTypeEnum.COLLECTION.getValue() : BankUseTypeEnum.PAYMENT.getValue();
            } else {
                bankUseTyp = ContractSalesTypeEnum.SALES.getValue() == customerAllMessageDTO.getSalesType() ? BankUseTypeEnum.COLLECTION.getValue() : BankUseTypeEnum.PAYMENT.getValue();
            }

            CustomerBankFactoryDTO customerBankFactoryDTO = new CustomerBankFactoryDTO()
                    .setCustomerId(customerAllMessageDTO.getCustomerId())
                    .setCategory2(customerAllMessageDTO.getCategory2())
                    .setCategory3(customerAllMessageDTO.getCategory3())
                    .setFactoryCode(customerAllMessageDTO.getFactoryCode())
                    .setCompanyId(String.valueOf(customerAllMessageDTO.getCompanyId()))
                    .setUseType(bankUseTyp).setFactoryId(factoryId);

            customerAllMessageDTO.setFactoryId(factoryId);
            List<CustomerBankDTO> customerBankDTOS = BeanConvertUtils.convert2List(CustomerBankDTO.class, customerBankService.queryCustomerBankFactory(customerBankFactoryDTO));
            customerDTO.setCustomerBankDTOS(customerBankDTOS);

            //查询客户联系人
            List<ContactEntity> contactDTOS = contactService.queryContactByFactoryList(customerAllMessageDTO);

            customerDTO.setContactDTO(contactDTOS);
        }

        return customerDTO;
    }

    @Override
    public CustomerDTO queryCustomerContactAllMessage(CustomerAllMessageDTO customerAllMessageDTO) {

        //查询客户信息
        CustomerEntity customerEntity = customerDao.getById(customerAllMessageDTO.getCustomerId());

        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }

        Integer factoryId = customerAllMessageDTO.getFactoryId();
        if (null == factoryId) {
            factoryId = factoryService.getFactoryByCode(customerAllMessageDTO.getFactoryCode()).getId();
        }
        customerAllMessageDTO.setFactoryId(factoryId);
        CustomerDTO customerDTO = BeanConvertUtils.convert(CustomerDTO.class, customerEntity);

        //查询客户联系人
        List<ContactEntity> contactDTOS = contactService.queryContactByFactoryList(customerAllMessageDTO);

        customerDTO.setContactDTO(contactDTOS);

        return customerDTO;
    }


    /**
     * 根据linkage客户编码查询客户信息
     *
     * @param linkageCustomerCode
     * @return
     */
    @Override
    public CustomerEntity queryCustomerByLinkageCode(String linkageCustomerCode) {
        return customerDao.queryCustomerByLinkageCustomerCode(linkageCustomerCode);

    }

    @Override
    public Boolean customerJudge(Integer beforeCustomerId, Integer afterCustomerId) {
        List<CustomerEntity> customerEntities = customerDao.getCustomerByIdList(Arrays.asList(beforeCustomerId, afterCustomerId));
        if (CollectionUtils.isNotEmpty(customerEntities) && customerEntities.size() == 2) {
            String enterpriseName = customerEntities.get(0).getEnterpriseName();
            String enterpriseName1 = customerEntities.get(1).getEnterpriseName();
            return enterpriseName.equalsIgnoreCase(enterpriseName1);
        }
        return false;
    }


    public boolean queryCustomerByLinkageCode(MultipartFile file) {
        List<CustomerConfigDTO> customerConfigDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerConfigDTO.class);

        for (CustomerConfigDTO customerConfigDTO : customerConfigDTOS) {
            //根据Linkag编号查询客户信息
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerConfigDTO.getLinkageCustomerCode());

            if (null != customerEntity) {
                customerEntity.setSignPlace(customerConfigDTO.getSignPlace());
                customerDao.updateById(customerEntity);
            }

        }
        return true;

    }


    @Override
    public Integer updateCustomerParentId() {

        Integer num = 0;
        List<CustomerEntity> customerEntities = customerDao.list();

        for (CustomerEntity customerEntity : customerEntities) {
            if (StrUtil.isNotBlank(customerEntity.getEnterpriseName())) {
                List<CustomerEntity> customerEntities1 = customerDao.queryCustomerByCustomerName(customerEntity.getEnterpriseName());

                if (!customerEntities1.isEmpty() && !customerEntity.getName().equals(customerEntities1.get(0).getName())) {

                    customerEntity.setParentId(customerEntities1.get(0).getId());
                    customerDao.updateById(customerEntity);
                    num++;
                }

            }
        }

        return num;
    }

    @Override
    public List<CustomerEntity> queryCustomerByIdList(List<Integer> idList) {
        return customerDao.queryCustomerByIdList(idList);
    }

    @Override
    public List<CustomerEntity> getCustomerListByCode(List<String> customerCodeList) {
        return customerDao.getCustomerListByCode(customerCodeList);
    }

    @Override
    public List<CustomerEntity> getCustomerListByTemplateVipCode(String templateVipCode) {
        return customerDao.getCustomerListByTemplateVipCode(templateVipCode);
    }

    @Override
    public Boolean updateCustomerTemplateVip(String templateVipCode, List<String> customerCodeList) {
        return customerDao.updateCustomerTemplateVip(templateVipCode, customerCodeList);
    }

    @Override
    public List<CustomerEntity> querySonCustomerList(Integer customerId) {
        return customerDao.querySonCustomerList(customerId);
    }

    @Override
    public CustomerEntity queryCustomerByCompanyAndLDC(Integer companyId) {
        List<CustomerEntity> customerEntityList = customerDao.queryCustomerByCompanyAndLDC(companyId, GeneralEnum.YES.getValue());
        return customerEntityList.isEmpty() ? null : customerEntityList.get(0);
    }

    @Override
    public List<CustomerEntity> getCustomerByCompanyId(Integer isLdc, Integer companyId) {
        return customerDao.queryCustomerByCompanyAndLDC(isLdc, companyId);
    }

    @Override
    public boolean deleteCustomerById(Integer customerId) {

        return customerDao.updateById(customerDao.getById(customerId).setIsDeleted(IsDeletedEnum.DELETED.getValue()));
    }

    @Override
    public CustomerEntity queryCustomerByCompanyAndFactory(Integer factoryId, Integer companyId) {
        return customerDao.queryCustomerByCompanyAndFactory(factoryId, companyId);
    }

    @Override
    public List<CustomerEntity> queryFactoryCustomer() {
        return customerDao.queryFactoryCustomer();
    }

    @Override
    public CustomerEntity getLdcSupplierByName(String supplierName) {
        List<CustomerEntity> customerEntityList = customerDao.queryCustomerByCustomerName(supplierName);
        if (CollectionUtils.isNotEmpty(customerEntityList)) {
            for (CustomerEntity customerEntity : customerEntityList) {
                if (customerEntity.getIsLdc().equals(GeneralEnum.YES.getValue())) {
                    return customerEntity;
                }
            }
        }
        return null;
    }

    /**
     * CaseId-1002453: RR status in navigator，Author By NaNa
     *
     * @param customerId
     * @return 客户的剩余风险控制信息
     */
    @Override
    public CrisGlobalEntity getCustomerResidualRiskInfo(Integer customerId) {
        CustomerEntity customerEntity = customerDao.queryCustomerById(customerId);
        CrisGlobalEntity crisGlobalEntity = crisGlobalDao.getCrisGlobalByCustomerCode(customerEntity.getLinkageCustomerCode());
        if (null != crisGlobalEntity) {
            crisGlobalEntity.setResidualRiskLimit(null != crisGlobalEntity.getLimitResidualRisk() ? BigDecimalUtil.initBigDecimal(crisGlobalEntity.getLimitResidualRisk()).setScale(0, RoundingMode.HALF_UP) : null)
                    .setResidualRiskUsage(null != crisGlobalEntity.getGlobalPeakResidualTotal() ? BigDecimalUtil.initBigDecimal(crisGlobalEntity.getGlobalPeakResidualTotal()).setScale(0, RoundingMode.HALF_UP) : null)
                    .setResidualRiskResidue(null != crisGlobalEntity.getRrResidue() ? BigDecimalUtil.initBigDecimal(crisGlobalEntity.getRrResidue()).setScale(0, RoundingMode.HALF_UP) : null);
            crisGlobalEntity.setTradeStatus(StringUtils.isNotBlank(customerEntity.getTradeStatus()) ?
                    customerEntity.getTradeStatus() : crisGlobalEntity.getTradeStatus());
            return crisGlobalEntity;
        }
        return new CrisGlobalEntity().setTradeStatus(customerEntity.getTradeStatus());
    }


    public void sendEmail(Integer userId, String userName, String companyName) {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_SYSTEM_CHANGE_NOTICE_1.name());
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_SYSTEM_CHANGE_NOTICE_1.getDesc());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        List<String> receivers = new ArrayList<>();
        receivers.add(String.valueOf(userId));
        messageInfoDTO.setReceivers(receivers);
        Map<String, Object> map = new HashMap<>();
        map.put("companyName", companyName);
        map.put("customerName", userName);
        map.put("sendDate", DateTimeUtil.formatDateStringCN(new Date()));
        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
    }

    @Override
    public List<CustomerEntity> queryCustomerList(CustomerQO condition) {
        return customerDao.queryCustomerList(condition);
    }

    @Override
    public void updateCustomerTradeStatus() {
        List<CustomerEntity> customerEntities = customerDao.queryCustomerList(null);
        for (CustomerEntity customerEntity : customerEntities) {
            CrisGlobalEntity crisGlobalEntity = getCustomerResidualRiskInfo(customerEntity.getId());
            if (null != crisGlobalEntity) {
                customerEntity.setTradeStatus(crisGlobalEntity.getTradeStatus());
                customerDao.updateById(customerEntity);
            }
        }
    }

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 start
    @Override
    public List<Integer> getCustomerIdsByEnterpriseName(String enterpriseName) {
        return customerDao.getCustomerIdsByEnterpriseName(enterpriseName);
    }
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 end

}
