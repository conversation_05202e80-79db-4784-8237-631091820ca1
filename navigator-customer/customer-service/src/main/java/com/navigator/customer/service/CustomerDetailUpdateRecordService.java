package com.navigator.customer.service;

import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */
public interface CustomerDetailUpdateRecordService {

    /**
     * 查询客户配置修改信息
     *
     * @param customerDetailUpdateRecordDTO
     * @return
     */
    CustomerDetailUpdateRecordEntity detailUpdateSelect(CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO);


    /**
     * 记录客户配置修改
     *
     * @param customerDetailUpdateRecordEntity
     * @return
     */
    boolean saveCustomerDetailUpdateRecord(CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity);


}
