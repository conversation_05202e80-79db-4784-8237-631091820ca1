package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.ContactFactoryFacade;
import com.navigator.customer.service.IContactFactoryService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 17:54
 */
@RestController
public class ContactFactoryFacadeImpl implements ContactFactoryFacade {

    @Resource
    private IContactFactoryService contactFactoryService;

    @Override
    public Result queryContactFactoryByContactId(Integer contactId) {
        return Result.success(contactFactoryService.queryContactFactoryByContactId(contactId));
    }

    @Override
    public Result deleteContactFactoryByContactId(Integer contactId) {
        return Result.success(contactFactoryService.deleteContactFactoryByContactId(contactId));
    }

    @Override
    public Result getContactFactoryByContactId(Integer contactId, Integer factoryId) {
        return Result.success(contactFactoryService.getContactFactoryByContactId(contactId,factoryId));
    }
}
