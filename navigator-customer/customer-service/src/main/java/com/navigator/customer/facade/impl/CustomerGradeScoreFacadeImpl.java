package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerGradeScoreFacade;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.customer.service.CustomerGradeScoreService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@RestController
public class CustomerGradeScoreFacadeImpl implements CustomerGradeScoreFacade {

    @Resource
    private CustomerGradeScoreService customerGradeScoreService;

    @Override
    public List<CustomerGradeScoreEntity> queryCustomerGradeScoreList(CustomerGradeScoreDTO customerGradeScoreDTO) {
        return customerGradeScoreService.queryCustomerGradeScoreList(customerGradeScoreDTO);
    }

    @Override
    public Result importCustomerGrade(MultipartFile file) {
        return customerGradeScoreService.importCustomerGrade(file);
    }
}
