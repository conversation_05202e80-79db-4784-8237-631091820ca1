package com.navigator.customer.service;

import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.vo.CustomerDeliveryWhiteFileReturnVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
public interface CustomerDeliveryWhiteService {

    /**
     * 根据条件查询数据
     *
     * @param customerDeliveryWhiteDTO
     * @return
     */
    List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhite(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO);


    /**
     * 根据条件查询列表
     *
     * @param customerDeliveryWhiteDTO
     * @return
     */
    List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhiteList(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO);

      CustomerDeliveryWhiteEntity queryCustomerDeliveryWhiteById(Integer id);

    /**
     * 新增编辑客户提货白名单
     *
     * @param customerDeliveryWhiteEntity
     * @return
     */
    boolean saveOrUpdateCustomerDeliveryWhite(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity);


    /**
     * 下载客户提货委托白名单
     *
     * @return
     */
    List<CustomerDeliveryWhiteExcelDTO> downloadCustomerWhitelist(CustomerDTO customerDTO);

    /**
     * 客户提货委托白名单上传校验
     *
     * @param file
     * @return
     */
    CustomerDeliveryWhiteFileReturnVO checkCustomerWhitelist(MultipartFile file);

    /**
     * 客户提货委托白名单excel上传
     *
     * @param file
     * @return
     */
    CustomerDeliveryWhiteFileReturnVO uploadCustomerWhitelist(MultipartFile file);

}
