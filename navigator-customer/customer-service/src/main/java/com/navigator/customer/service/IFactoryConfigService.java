package com.navigator.customer.service;

import com.navigator.customer.pojo.dto.FactoryConfigDTO;
import com.navigator.customer.pojo.entity.FactoryConfigEntity;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface IFactoryConfigService {

    /**
     * 查询出货工厂
     *
     * @param customerId
     * @return
     */
    List<FactoryConfigEntity> factoryConfigBySuppId(Integer customerId);

    /**
     * 根据id查询出工厂信息
     *
     * @param id
     * @return
     */
    FactoryConfigDTO getFactoryConfigById(Integer id);

}
