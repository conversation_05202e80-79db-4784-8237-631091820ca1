package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.vo.CustomerDepositRateVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/14 11:45
 */

public interface ICustomerDepositRateService {

    /**
     * 根据客户id 履约保证金比例
     *
     * @param customerId
     * @return
     */
    List<CustomerDepositRateVO> getCustomerDepositRateByCustomerId(CustomerDepositRateBO customerDepositRateBO);


    List<CustomerDepositRateVO> getCustomerDepositRateAddTT(CustomerDepositRateBO customerDepositRateBO);


    /**
     * 根据id 履约保证金比例
     *
     * @param id
     * @return
     */
    CustomerDepositRateEntity getCustomerDepositRateById(Integer id);


    /**
     * 添加客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @param currentUserId
     * @param name
     * @return
     */
    boolean saveCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO, String currentUserId, String name);


    /**
     * 履约保证金 状态修改
     *
     * @param id
     * @return
     */
    boolean updateCustomerDepositRateStatus(Integer id);


    /**
     * 修改客户 履约保证金 比例
     *
     * @param customerDepositRateEntity
     * @return
     */
    boolean updateCustomerDepositRate(CustomerDepositRateEntity customerDepositRateEntity);


    boolean saveOrUpdateCustomerDepositRate(CustomerDepositRateEntity customerDepositRateEntity);

    /**
     * 删除客户 履约保证金 比例
     *
     * @param customerDepositRateId
     * @return
     */
    boolean deleteCustomerDepositRate(Integer customerDepositRateId);

    /**
     * 编辑提交客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    boolean redactCustomerDepositRate(List<CustomerDepositRateDTO> customerDepositRateDTO);

    Result addCustomerSalesDepositRate(MultipartFile file);

    /**
     * 根据条件查询
     *
     * @param customerDepositRateDTO
     * @return
     */
    List<CustomerDepositRateEntity> queryCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO);

    CustomerDepositRateEntity queryCustomerDepositRateEntityById(Integer id);
}
