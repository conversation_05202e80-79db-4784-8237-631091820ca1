package com.navigator.customer.service;


import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.vo.CustomerContactVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 客户联系人表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IContactService {

    /**
     * 根据客户/供应商id查询详情信息
     *
     * @param referId
     * @param referType
     * @return
     */
    List<ContactEntity> queryContactEntity(Integer referId, Integer referType, Integer categoryId);

    /**
     * 修改客户/供应商详情信息
     *
     * @param contactEntity
     * @return
     */
    Integer updateContactEntity(ContactEntity contactEntity);

    /**
     * 添加客户联系人
     *
     * @param contactEntity
     * @return
     */
    Integer vaseContactEntity(ContactEntity contactEntity);

    /**
     * 据id查询联系人信息
     *
     * @param id
     * @return
     */
    ContactEntity getContactEntityById(Integer id);

    List<CustomerContactVO> queryContactFactoryList(ContactDTO contactDTO);

    /**
     * 根据客户和商品查询联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    List<ContactEntity> getContactCustomerByList(Integer referId, Integer categoryId, Integer supplierId);

    /**
     * 根据供应商/LDC查询出联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    List<ContactEntity> queryLDCContactEntity(Integer referId, Integer referType, Integer categoryId);


    /**
     * 查询客户签章人信息
     *
     * @param customerId
     * @return
     */
    ContactDTO getCustomerByCustomerIdYqq(Integer customerId);

    /**
     * 根据客户id查询客户联系人
     *
     * @param customerId
     * @return
     */
    List<ContactEntity> getCustomerByCustomerId(Integer customerId, Integer referType, Integer categoryId);


    /**
     * 根据id删除客户联系人信息 及 适用油厂信息
     *
     * @param contactId
     * @return
     */
    boolean deleteContactFactoryEntityBy(Integer contactId);


    /**
     * 根据工厂code 客户查询客户联系人
     *
     * @return
     */
    List<ContactEntity> queryContactByFactoryList(CustomerAllMessageDTO customerAllMessageDTO);

    /**
     * 导入客户联系人信息
     *
     * @return
     */
    Result leadCustomerContact(MultipartFile file);


    Boolean contactFactoryList(List<CustomerTemplateDTO.ContactFactory> contactFactoryS, CustomerTemplateDTO customerTemplateDTO);


    void contactAddress();

}
