package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerGradeScoreMapper;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */

@Dao
public class  CustomerGradeScoreDao extends BaseDaoImpl<CustomerGradeScoreMapper, CustomerGradeScoreEntity> {

    public List<CustomerGradeScoreEntity> queryCustomerGradeScoreList(CustomerGradeScoreDTO customerGradeScoreDTO) {
        return this.list(Wrappers.<CustomerGradeScoreEntity>lambdaQuery()
                .eq(CustomerGradeScoreEntity::getCustomerId, customerGradeScoreDTO.getCustomerId())
                .eq(StringUtils.isNotEmpty(customerGradeScoreDTO.getCategory1()), CustomerGradeScoreEntity::getCategory1, "," + customerGradeScoreDTO.getCategory1() + ",")
                .eq(StringUtils.isNotEmpty(customerGradeScoreDTO.getCategory2()), CustomerGradeScoreEntity::getCategory2, "," + customerGradeScoreDTO.getCategory2() + ",")
                .eq(StringUtils.isNotEmpty(customerGradeScoreDTO.getCategory3()), CustomerGradeScoreEntity::getCategory3, "," + customerGradeScoreDTO.getCategory3() + ",")
                .orderByDesc(CustomerGradeScoreEntity::getUpdatedAt)
        );
    }


}
