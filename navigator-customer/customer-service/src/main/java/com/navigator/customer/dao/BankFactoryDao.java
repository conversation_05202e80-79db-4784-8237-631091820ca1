package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.BankFactoryMapper;
import com.navigator.customer.pojo.entity.BankFactoryEntity;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 18:50
 */
@Dao
public class BankFactoryDao extends BaseDaoImpl<BankFactoryMapper, BankFactoryEntity> {

    /**
     * 查询客户银行适用的工厂
     *
     * @param bankId
     * @return
     */
    public List<BankFactoryEntity> queryBankFactoryByBankId(Integer bankId) {
        return this.baseMapper.selectList(Wrappers.<BankFactoryEntity>lambdaQuery()
                .eq(BankFactoryEntity::getBankId, bankId)
                .eq(BankFactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    /**
     * 根据油厂id和账户id 查询数据
     *
     * @param bankId
     * @return
     */
    public List<BankFactoryEntity> queryBankFactoryByBankIdFactoryId(Integer bankId, Integer factoryId) {
        return this.baseMapper.selectList(Wrappers.<BankFactoryEntity>lambdaQuery()
                .eq(BankFactoryEntity::getBankId, bankId)
                .eq(BankFactoryEntity::getFactoryId, factoryId)
                .eq(BankFactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public void deleteByCondition(BankFactoryEntity bankFactoryEntity) {
        remove(Wrappers.<BankFactoryEntity>lambdaUpdate()
                .eq(BankFactoryEntity::getBankId, bankFactoryEntity.getBankId())
        );
    }
}
