package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.mapper.ContactFactoryMapper;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 17:59
 */
@Dao
public class ContactFactoryDao extends BaseDaoImpl<ContactFactoryMapper, ContactFactoryEntity> {

    public List<ContactFactoryEntity> queryContactFactoryByContactId(Integer contactId) {
        return this.baseMapper.selectList(Wrappers.<ContactFactoryEntity>lambdaQuery()
                .eq(ContactFactoryEntity::getContactId, contactId)
                .eq(ContactFactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<ContactFactoryEntity> getContactFactoryByContactId(Integer contactId, Integer factoryId){
        return this.baseMapper.selectList(Wrappers.<ContactFactoryEntity>lambdaQuery()
                .eq(ContactFactoryEntity::getContactId, contactId)
                .eq(ContactFactoryEntity::getFactoryId, factoryId)
                .eq(ContactFactoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void deleteByCondition(ContactFactoryEntity contactFactoryEntity) {
        remove(Wrappers.<ContactFactoryEntity>lambdaUpdate()
                .eq(ContactFactoryEntity::getContactId, contactFactoryEntity.getContactId())
        );
    }

    public List<ContactFactoryEntity> getValidContactFactoryByContactId(Integer contactId, Integer factoryId){
        return this.baseMapper.selectList(Wrappers.<ContactFactoryEntity>lambdaQuery()
                .eq(ContactFactoryEntity::getContactId, contactId)
                .eq(ContactFactoryEntity::getFactoryId, factoryId)
                .eq(ContactFactoryEntity::getIsDeleted,IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<ContactFactoryEntity> getContactFactoryByCustomerId(Integer customerId, Integer factoryId) {
        return this.baseMapper.selectList(Wrappers.<ContactFactoryEntity>lambdaQuery()
                .eq(ContactFactoryEntity::getCustomerId, customerId)
                .eq(ContactFactoryEntity::getFactoryId, factoryId));
    }


    public Integer saveContactFactoryEntity(ContactFactoryEntity contactFactoryEntity){
        return this.baseMapper.insert(contactFactoryEntity);
    }


}
