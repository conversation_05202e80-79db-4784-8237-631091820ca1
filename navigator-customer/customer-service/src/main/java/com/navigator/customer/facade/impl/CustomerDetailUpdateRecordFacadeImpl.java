package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDetailUpdateRecordFacade;
import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/14
 */
@RestController
public class CustomerDetailUpdateRecordFacadeImpl implements CustomerDetailUpdateRecordFacade {

    @Resource
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;

    @Override
    public Result detailUpdateSelect(CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO) {
        return Result.success(customerDetailUpdateRecordService.detailUpdateSelect(customerDetailUpdateRecordDTO));
    }

    @Override
    public Result saveCustomerDetailUpdateRecord(CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity) {
        return Result.success(customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity));
    }
}
