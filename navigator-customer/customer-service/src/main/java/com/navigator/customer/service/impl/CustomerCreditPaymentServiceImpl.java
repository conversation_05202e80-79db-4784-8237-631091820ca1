package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.dao.CustomerCreditPaymentDao;
import com.navigator.customer.dao.CustomerDao;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.dto.file.PaymentDaysExcelDTO;
import com.navigator.customer.pojo.entity.CustomerCreditPaymentEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import com.navigator.customer.service.CustomerCreditPaymentService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 17:10
 */
@Service
@Slf4j
public class CustomerCreditPaymentServiceImpl implements CustomerCreditPaymentService {

    @Resource
    private CustomerCreditPaymentDao customerCreditPaymentDao;
    @Resource
    private CustomerDao customerDao;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;

    private static final int BATCH_SIZE = 1000; // 每个批次的大小

    @Override
    public List<CustomerCreditPaymentVO> customerCreditPaymentAllList(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        if (null == customerCreditPaymentDTO.getIsSales() && null == customerCreditPaymentDTO.getIsProcurement() && null == customerCreditPaymentDTO.getCompanyId()) {
            return customerCreditPaymentVOList(customerCreditPaymentDTO);
        } else {
            List<CustomerCreditPaymentVO> customerCreditPaymentVOS = new ArrayList<>();

            List<CustomerCreditPaymentEntity> customerCreditPaymentEntities = customerCreditPaymentDao.CustomerCreditPaymentAllList(customerCreditPaymentDTO);
            if (!customerCreditPaymentEntities.isEmpty()) {
                CustomerCreditPaymentVO customerCreditPaymentVO = BeanConvertUtils.convert(CustomerCreditPaymentVO.class, customerCreditPaymentEntities.get(0));
                customerCreditPaymentVOS.add(customerCreditPaymentVO);
            }

            return customerCreditPaymentVOS;
        }
    }

    private List<CustomerCreditPaymentVO> customerCreditPaymentVOList(CustomerCreditPaymentDTO customerCreditPaymentDTO) {

        List<CustomerCreditPaymentVO> customerCreditPaymentVOS = new ArrayList<>();
        List<CompanyEntity> companyEntities = companyFacade.queryCompanyList();

        List<CategoryEntity> categoryEntity2List = categoryFacade.queryCategoryList(new CategoryQO()
                .setParentSerialNo(Integer.parseInt(customerCreditPaymentDTO.getCategory1()))
                .setLevel(2)
        );

        categoryEntity2List = categoryEntity2List.stream().filter(categoryEntity -> categoryEntity.getLevel().equals(2)).collect(Collectors.toList());

        for (CategoryEntity categoryEntity2 : categoryEntity2List) {
            customerCreditPaymentDTO.setCategory2(String.valueOf(categoryEntity2.getSerialNo()));
            //List<CategoryEntity> categoryEntity3List = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(categoryEntity2.getSerialNo()));

            for (CompanyEntity companyEntity : companyEntities) {

                customerCreditPaymentDTO.setCompanyId(companyEntity.getId());

                for (ContractSalesTypeEnum contractSalesTypeEnum : ContractSalesTypeEnum.values()) {

                    for (BuCodeEnum codeEnum : BuCodeEnum.values()) {
                        customerCreditPaymentDTO.setBuCode(codeEnum.getValue());

                        if (ContractSalesTypeEnum.SALES.getValue() == contractSalesTypeEnum.getValue()) {
                            customerCreditPaymentDTO
                                    .setIsSales(GeneralEnum.YES.getValue())
                                    .setIsProcurement(null);
                        } else {
                            customerCreditPaymentDTO
                                    .setIsProcurement(GeneralEnum.YES.getValue())
                                    .setIsSales(null);
                        }

                        List<CustomerCreditPaymentEntity> customerCreditPaymentEntities = customerCreditPaymentDao.CustomerCreditPaymentAllList(customerCreditPaymentDTO);
                        CustomerCreditPaymentVO customerCreditPaymentVO = new CustomerCreditPaymentVO();

                        Map<Integer, List<Integer>> map = new HashMap<>();

                        List<Integer> category3List = new ArrayList<>();
                        StringBuilder category3Name = new StringBuilder();
                        if (!customerCreditPaymentEntities.isEmpty()) {
                            customerCreditPaymentVO = BeanConvertUtils.convert(CustomerCreditPaymentVO.class, customerCreditPaymentEntities.get(0));
                            category3List = Arrays.stream(customerCreditPaymentVO.getCategory3().split(","))
                                    .map(String::trim)
                                    .filter(category3 -> !category3.isEmpty())
                                    .map(Integer::parseInt)
                                    .collect(Collectors.toList());
                            for (Integer category3 : category3List) {
                                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                                if(null != categoryEntity){
                                    category3Name.append(categoryEntity.getName());
                                    //判断是否是最后一个
                                    if (!category3.equals(category3List.get(category3List.size() - 1))) {
                                        category3Name.append(",");
                                    }
                                }
                            }

                            map.put(categoryEntity2.getSerialNo(), category3List);
                        } else {
                            if (ContractSalesTypeEnum.SALES.getValue() == contractSalesTypeEnum.getValue()) {
                                customerCreditPaymentVO.setIsSales(GeneralEnum.YES.getValue());
                            } else {
                                customerCreditPaymentVO.setIsProcurement(GeneralEnum.YES.getValue());
                            }
                            map.put(categoryEntity2.getSerialNo(), null);
                        }

                        customerCreditPaymentVO
                                .setBuCode(codeEnum.getValue())
                                .setCategoryMap(map)
                                .setCategory1(customerCreditPaymentDTO.getCategory1())
                                .setCategory2(String.valueOf(categoryEntity2.getSerialNo()))
                                .setCompanyShortName(companyEntity.getShortName())
                                .setCompanyId(companyEntity.getId())
                                .setCategory2Name(categoryEntity2.getName())
                                .setCategory3Name(category3Name.toString())
                                .setCategory1(customerCreditPaymentVO.getCategory1().replaceAll("^,+|,+$", ""))
                                .setCategory2(customerCreditPaymentVO.getCategory2().replaceAll("^,+|,+$", ""))
                                .setCategory3(StringUtil.isNotEmpty(customerCreditPaymentVO.getCategory3()) ? customerCreditPaymentVO.getCategory3().replaceAll("^,+|,+$", "") : null)
                        ;
                        customerCreditPaymentVOS.add(customerCreditPaymentVO);

                    }
                }
            }
        }


        return customerCreditPaymentVOS;
    }

    @Override
    public boolean updateCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {

        CustomerCreditPaymentEntity customerCreditPaymentEntity = customerCreditPaymentDao.getById(customerCreditPaymentDTO.getId());

        if (null == customerCreditPaymentEntity) {
            throw new RuntimeException("客户赊销&预付款信息为空");
        }

        if (customerCreditPaymentEntity.getStatus().equals(DisableStatusEnum.DISABLE.getValue())) {
            customerCreditPaymentEntity.setStatus(DisableStatusEnum.ENABLE.getValue());
        } else {
            customerCreditPaymentEntity.setStatus(DisableStatusEnum.DISABLE.getValue());
        }

        return customerCreditPaymentDao.updateById(customerCreditPaymentEntity);
    }


    @Override
    public boolean addCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        //根据id查询赊销预付款信息
        CustomerCreditPaymentEntity customerCreditPaymentEntity = customerCreditPaymentDao.getById(customerCreditPaymentDTO.getId());
        CustomerCreditPaymentEntity originalCustomerCreditPaymentEntity = new CustomerCreditPaymentEntity();
        customerCreditPaymentEntity = BeanConvertUtils.convert(CustomerCreditPaymentEntity.class, customerCreditPaymentDTO);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null != customerCreditPaymentEntity.getId()) {
            //有数据修改
            BeanUtils.copyProperties(customerCreditPaymentEntity, originalCustomerCreditPaymentEntity);
            customerCreditPaymentEntity
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(currentUserId)
                    .setUpdatedByName(name);
            customerCreditPaymentDao.updateById(customerCreditPaymentEntity);
        } else {

            Integer size = customerCreditPaymentDao.queryCustomerCreditPaymentAllList(customerCreditPaymentDTO).size();
            if (size == 0) {
                //没数据新增
                customerCreditPaymentEntity
                        .setCreatedBy(currentUserId)
                        .setCreatedByName(name)
                        .setUpdatedBy(currentUserId)
                        .setUpdatedAt(new Date())
                        .setCreatedAt(new Date())
                        .setUpdatedByName(name);
                customerCreditPaymentDao.save(customerCreditPaymentEntity);
            } else {
                log.info("=====数据:{}", JSON.toJSONString(customerCreditPaymentDTO));
            }
        }
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerCreditPaymentDTO))
                    .setBeforeData(JSON.toJSONString(originalCustomerCreditPaymentEntity))
                    .setAfterData(JSON.toJSONString(customerCreditPaymentEntity))
                    .setOperationActionEnum(OperationActionEnum.SBM_ADD_CUSTOMER_CREDIT_PAYMENT)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean saveOrUpdateCustomerCreditPayment(CustomerCreditPaymentEntity customerCreditPaymentEntity) {
        return customerCreditPaymentDao.saveOrUpdate(customerCreditPaymentEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result leadCustomerCreditPayment(MultipartFile file) {

        List<PaymentDaysExcelDTO> paymentDaysExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, PaymentDaysExcelDTO.class);

        addCustomerCreditPayment(paymentDaysExcelDTOS);
        return null;
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void addCustomerCreditPayment(List<PaymentDaysExcelDTO> paymentDaysExcelDTOS) {
        List<String> customerCodeList = new ArrayList<>();

        //查询所有品类
        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);

        for (PaymentDaysExcelDTO paymentDaysExcelDTO : paymentDaysExcelDTOS) {
            log.info("导入数据------:{}", paymentDaysExcelDTO.toString());

            //根据客户编码查询出客户信息
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(paymentDaysExcelDTO.getCustomerCode());
            if (null == customerEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(paymentDaysExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(paymentDaysExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());


            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            paymentDaysExcelDTO.getCategory2();

            List<Integer> category2s = Arrays.stream(category2.split(","))
                    .map(String::trim)
                    .filter(category3 -> !category3.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            List<CategoryEntity> category3Entities = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(category2s.get(0)));

            StringBuilder category3 = new StringBuilder()
                    .append(",");
            for (CategoryEntity category3Entity : category3Entities) {
                category3
                        .append(category3Entity.getId())
                        .append(",")
                ;
            }
            Integer paymentType = PaymentTypeEnum.getByDesc(paymentDaysExcelDTO.getPaymentType()).getType();
            //获取公司id
            List<String> companyNameList = Arrays.stream(paymentDaysExcelDTO.getCompanyName().split(",")).map(String::trim).collect(Collectors.toList());
            for (String companyCode : companyNameList) {
                CompanyEntity company = companyFacade.getCompanyByCode(companyCode);
                CustomerCreditPaymentEntity customerCreditPaymentEntity = new CustomerCreditPaymentEntity();

                Integer id = StringUtils.isNotBlank(paymentDaysExcelDTO.getId()) ? Integer.parseInt(paymentDaysExcelDTO.getId()) : null;

                customerCreditPaymentEntity
                        .setId(id)
                        .setCustomerId(customerEntity.getId())
                        .setCategory1(category1)
                        .setCategory2(category2)
                        .setCategory3(category3.toString())
                        .setPaymentType(paymentType)
                        .setCreditDays(Integer.parseInt(paymentDaysExcelDTO.getCreditDays()))
                        .setBuCode(paymentDaysExcelDTO.getBuCode())
                        .setCompanyId(company.getId())
                        .setIsSales(paymentDaysExcelDTO.getIsSales().equals("是") ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                        .setIsProcurement(paymentDaysExcelDTO.getIsProcurement().equals("是") ? GeneralEnum.YES.getValue() : GeneralEnum.NO.getValue())
                ;
                customerCreditPaymentDao.saveOrUpdate(customerCreditPaymentEntity);
            }

        }
        log.info("==========为成功处理的客户账号:{}" + JSON.toJSONString(customerCodeList));
    }

    @Override
    public CustomerCreditPaymentEntity queryCustomerCreditPaymentById(Integer Id) {
        return customerCreditPaymentDao.getById(Id);
    }

}
