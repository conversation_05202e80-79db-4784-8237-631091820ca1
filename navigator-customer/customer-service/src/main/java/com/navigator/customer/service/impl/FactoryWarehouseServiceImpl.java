package com.navigator.customer.service.impl;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.dao.FactoryWarehouseDao;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.dto.FactoryWarehouseLeadDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.customer.service.IFactoryService;
import com.navigator.customer.service.IFactoryWarehouseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
public class FactoryWarehouseServiceImpl implements IFactoryWarehouseService {

    @Resource
    private FactoryWarehouseDao factoryWarehouseDao;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private EmployFacade employFacade;

    @Override
    public FactoryWarehouseEntity queryFactoryWarehouseById(Integer id) {
        return factoryWarehouseDao.getById(id);
    }

    @Override
    public FactoryWarehouseEntity getFactoryDetailByLkg(String lkgWareHouseCode) {
        return factoryWarehouseDao.getFactoryDetailByLkg(lkgWareHouseCode);
    }

    @Override
    public Result queryFactoryWarehouseByCode(String factoryCode) {
        return Result.success(this.getFactoryWarehouseList(null, factoryCode, null, null));
    }

    @Override
    public boolean saveOrUpdateFactoryWarehouse(FactoryWarehouseEntity warehouseEntity) {
        FactoryEntity factoryEntity = factoryService.getFactoryById(warehouseEntity.getFactoryId());
        boolean optResult;
        FactoryWarehouseEntity lkgWarehouseEntity;
        if (null == warehouseEntity.getId()) {
            if (StringUtils.isNotBlank(warehouseEntity.getLkgWarehouseCode())) {
                warehouseEntity.setLkgWarehouseCode(warehouseEntity.getLkgWarehouseCode().trim());
            }
            lkgWarehouseEntity = this.getFactoryDetailByLkg(warehouseEntity.getLkgWarehouseCode());
            if (null != lkgWarehouseEntity) {
                throw new BusinessException(ResultCodeEnum.FACTORY_WAREHOUSE_LKG_CODE_EXIT, lkgWarehouseEntity.getLkgWarehouseCode());
            }
            warehouseEntity.setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setFactoryCode(factoryEntity.getCode())
                    .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedAt(new Date());
            optResult = factoryWarehouseDao.save(warehouseEntity);
        } else {
            FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseDao.getById(warehouseEntity.getId());
            if (null == factoryWarehouseEntity) {
                throw new BusinessException(ResultCodeEnum.FACTORY_WAREHOUSE_NOT_EXIT);
            }
            factoryWarehouseEntity.setName(warehouseEntity.getName())
                    .setFactoryId(warehouseEntity.getFactoryId())
                    .setFactoryCode(factoryEntity.getCode())
                    .setAddress(warehouseEntity.getAddress())
                    .setDeliveryPoint(warehouseEntity.getDeliveryPoint())
                    .setLkgWarehouseCode(warehouseEntity.getLkgWarehouseCode())
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedAt(new Date());
            optResult = factoryWarehouseDao.updateById(factoryWarehouseEntity);
        }
        return optResult;
    }

    @Override
    public boolean invalidWarehouse(Integer warehouseId) {
        FactoryWarehouseEntity warehouseEntity = factoryWarehouseDao.getById(warehouseId);
        if (null == warehouseEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        Integer status = DisableStatusEnum.ENABLE.getValue().equals(warehouseEntity.getStatus()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();
        warehouseEntity.setUpdatedAt(new Date());
        return factoryWarehouseDao.updateById(warehouseEntity.setStatus(status).setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId())));
    }

    @Override
    public Result importFactoryWarehouse(MultipartFile uploadFile) {
        List<FactoryWarehouseLeadDTO> warehouseLeadDTOList;
        List<FactoryWarehouseEntity> saveWarehouseEntityList = new ArrayList<>();
        List<FactoryWarehouseEntity> updateWarehouseEntityList = new ArrayList<>();
        try {
            warehouseLeadDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, FactoryWarehouseLeadDTO.class);
            if (!CollectionUtils.isEmpty(warehouseLeadDTOList)) {
                for (FactoryWarehouseLeadDTO warehouseDTO : warehouseLeadDTOList) {
                    FactoryEntity factoryEntity = factoryService.getFactoryByCode(warehouseDTO.getFactoryCode().trim());
                    Integer categoryId = GoodsCategoryEnum.getByCode(warehouseDTO.getCategoryCode()).getValue();
                    FactoryWarehouseEntity warehouseEntity = factoryWarehouseDao.getWarehouseByName(factoryEntity.getId(), categoryId, warehouseDTO.getName());
                    if (null == warehouseEntity) {
                        FactoryWarehouseEntity newWarehouseEntity = new FactoryWarehouseEntity()
                                .setLkgWarehouseCode(StringUtils.isNotBlank(warehouseDTO.getLkgWarehouseCode()) ? warehouseDTO.getLkgWarehouseCode() : "")
                                .setAddress(StringUtils.isNotBlank(warehouseDTO.getAddress()) ? warehouseDTO.getAddress() : "")
                                .setDeliveryPoint(StringUtils.isNotBlank(warehouseDTO.getDeliveryPoint()) ? warehouseDTO.getDeliveryPoint() : "")
                                .setFactoryId(factoryEntity.getId())
                                .setFactoryCode(warehouseDTO.getFactoryCode())
                                .setGoodsCategoryId(categoryId)
                                .setCreatedBy(1)
                                .setUpdatedBy(1)
                                .setName(warehouseDTO.getName())
                                .setCreator("新增发货库点");
                        factoryWarehouseDao.save(newWarehouseEntity);
                        saveWarehouseEntityList.add(newWarehouseEntity);
                    } else {
                        warehouseEntity.setLkgWarehouseCode(StringUtils.isNotBlank(warehouseDTO.getLkgWarehouseCode()) ? warehouseDTO.getLkgWarehouseCode() : "")
                                .setAddress(StringUtils.isNotBlank(warehouseDTO.getAddress()) ? warehouseDTO.getAddress() : "")
                                .setDeliveryPoint(StringUtils.isNotBlank(warehouseDTO.getDeliveryPoint()) ? warehouseDTO.getDeliveryPoint() : "")
                                .setUpdatedAt(DateTimeUtil.now()).setCreator("更新发货库点");
                        factoryWarehouseDao.updateById(warehouseEntity);
                        updateWarehouseEntityList.add(warehouseEntity);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
        return Result.success("新增库点条数：" + saveWarehouseEntityList.size() + "更新库点条数：" + updateWarehouseEntityList.size(), saveWarehouseEntityList.addAll(updateWarehouseEntityList));
    }

    @Override
    public List<FactoryWarehouseEntity> getWarehouseListById(List<Integer> warehouseIdList) {
        return factoryWarehouseDao.getWarehouseListById(warehouseIdList);
    }


    @Override
    public List<FactoryWarehouseEntity> getFactoryWarehouseList(FactoryQueryDTO factoryQueryDTO) {
        List<FactoryWarehouseEntity> warehouseEntityList = factoryWarehouseDao.getFactoryWarehouseList(factoryQueryDTO);
        if (!CollectionUtils.isEmpty(warehouseEntityList)) {
            warehouseEntityList.forEach(it -> {
                it.setCreator(null == it.getCreatedBy() || 0 == it.getCreatedBy() ? "" : employFacade.getEmployById(it.getCreatedBy()).getName())
                        .setUpdator(null == it.getUpdatedBy() || 0 == it.getCreatedBy() ? "" : employFacade.getEmployById(it.getUpdatedBy()).getName());
            });
        }
        return warehouseEntityList;
    }

    @Override
    public List<FactoryWarehouseEntity> getFactoryWarehouseList(List<Integer> factoryId, String factoryCode, Integer goodsCategoryId, Integer status) {
        List<FactoryWarehouseEntity> warehouseEntityList = factoryWarehouseDao.getFactoryWarehouseList(factoryId, factoryCode, goodsCategoryId, status);
        if (!CollectionUtils.isEmpty(warehouseEntityList)) {
            warehouseEntityList.forEach(it -> {
                it.setCreator(null == it.getCreatedBy() || 0 == it.getCreatedBy() ? "" : employFacade.getEmployById(it.getCreatedBy()).getName())
                        .setUpdator(null == it.getUpdatedBy() || 0 == it.getCreatedBy() ? "" : employFacade.getEmployById(it.getUpdatedBy()).getName());
            });
        }
        return warehouseEntityList;
    }
}
