package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
public interface ICustomerDetailService {

    /**
     * 根据客户id 或品类查询客户配置
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    CustomerDetailEntity queryCustomerDetailList(Integer customerId, Integer categoryId);

    List<CustomerDetailEntity> queryCustomerDetailListByCondition(CustomerDetailBO customerDetailBO);

    /**
     * 根据客户id 或品类查询客户配置
     *
     * @param customerId
     * @return
     */
    List<CustomerDetailEntity> queryCustomerIdList(Integer customerId);

    /**
     * 添加客户配置
     *
     * @param customerDetailDTO
     * @return
     */
    Integer saveCustomerDetail(CustomerDetailDTO customerDetailDTO);

    /**
     * 修改客户配置信息
     *
     * @param customerDetailDTO
     * @return
     */
    Integer updateCustomerDetail(CustomerDetailDTO customerDetailDTO);

    boolean addCustomerDetailIsWhiteList(MultipartFile file);

    boolean saveOrUpdateCustomerDetail(CustomerDetailEntity customerDetailEntity);

    List<Integer> customerProtocol();

    Result whiteListCustomerTemplate(MultipartFile file);

    List<CustomerDetailEntity> queryCustomerInvoiceList(Integer customerId, Integer categoryId);

    Result saveCustomerInvoice(CustomerDetailBO customerDetailBO);

    Result modifyCustomerInvoice(CustomerDetailBO customerDetailBO);

    CustomerDetailEntity queryCustomerDetailEntity(Integer customerId, Integer category3);

    void copyCustomerInvoice();

    Result uploadGradeScoreTemplate(MultipartFile file);

    List<CustomerTemplateDeriveGradeScoreDTO> exportGradeScoreList(CustomerDTO queryDTO);

    void importGradeScoreExcel(MultipartFile file);

    CustomerDetailEntity queryCustomerDetailById(Integer id);

    CustomerDetailEntity queryCustomerDetailListByNotId(CustomerDetailDTO customerDetailDTO);

    List<CustomerDetailEntity> checkCustomerWhiteList(CustomerDetailDTO customerDetailDTO);
}
