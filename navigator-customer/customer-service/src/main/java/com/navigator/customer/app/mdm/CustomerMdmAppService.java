package com.navigator.customer.app.mdm;

import com.navigator.customer.pojo.dto.mdm.MDMCustomerResponseDTO;
import com.navigator.customer.pojo.dto.mdm.MDMObjectDataDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerMdmCounterpartyRecordEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/24
 */
public interface CustomerMdmAppService {

    MDMCustomerResponseDTO saveOrUpdateMDMCustomer(MDMObjectDataDTO mdmObjectDataDTO);
}
