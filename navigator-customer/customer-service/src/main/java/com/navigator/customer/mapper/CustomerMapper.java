package com.navigator.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface CustomerMapper extends BaseMapper<CustomerEntity> {

    @Select(
            "<script>" +
            "SELECT  customer.sign_place,customer.short_name, (case when customer.status=1 then '有效' else '无效' end) as status , \n" +
            "(case when customer.original_paper=1 then '是' else '否' end) as original_paper ,\n" +
            "(case when customer.is_supplier=1 then '是' else '否' end) as is_supplier ,\n" +
            "customer.linkage_customer_code,\n" +
            "customer.name as customer_name,\n" +
            "customer.customer_indexes_name,\n" +
            "customer.enterprise_name,\n" +
            "customer.full_name,\n" +
            "customer.full_name_english,\n" +
            "customer.address,\n" +
            "detail.grade_score as sbm_grade_score\n" +
            "FROM  (select * from dba_customer  where " +
            "1=1  " +
            "<when test='customerCode != null'> and dba_customer.linkage_customer_code = #{customerCode} </when>" +
            "<when test='name != null'> and dba_customer.name = #{name} </when>" +
            "<when test='updatedByName != null'> and dba_customer.updated_by_name = #{updatedByName} </when>" +
            ") as customer  left join dba_customer_detail as detail on customer.id = detail.customer_id and detail.category_id=11 and detail.company_id = 1 " +
            " \n " +
            "</script>")
    List<CustomerTemplateDeriveGradeScoreDTO> getSbmGradeCustomerList(String customerCode, String name, String updatedByName);

    @Select("<script>" +
            "SELECT  customer.sign_place,customer.short_name, (case when customer.status=1 then '有效' else '无效' end) as status , \n" +
            "(case when customer.original_paper=1 then '是' else '否' end) as original_paper ,\n" +
            "(case when customer.is_supplier=1 then '是' else '否' end) as is_supplier ,\n" +
            "customer.linkage_customer_code,\n" +
            "customer.name as customer_name,\n" +
            "customer.customer_indexes_name,\n" +
            "customer.enterprise_name,\n" +
            "customer.full_name,\n" +
            "customer.full_name_english,\n" +
            "customer.address,\n" +
            "detail.grade_score as sbo_grade_score\n" +
            "FROM  (select * from dba_customer  where " +
            "1=1  " +
            "<when test='customerCode != null'> and dba_customer.linkage_customer_code = #{customerCode} </when>" +
            "<when test='name != null'> and dba_customer.name = #{name} </when>" +
            "<when test='updatedByName != null'> and dba_customer.updated_by_name = #{updatedByName} </when>" +
            ") as customer left join dba_customer_detail as detail on customer.id = detail.customer_id and detail.category_id=12 and detail.company_id = 1 " +
            "\n " +
            "</script>")
    List<CustomerTemplateDeriveGradeScoreDTO> getSboGradeCustomerList(String customerCode, String name, String updatedByName);
}
