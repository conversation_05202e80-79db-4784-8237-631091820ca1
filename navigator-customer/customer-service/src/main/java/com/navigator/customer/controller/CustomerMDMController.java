/*
package com.navigator.customer.controller;

import com.navigator.common.dto.Result;
import com.navigator.customer.app.mdm.CustomerMdmAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

*/
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/30
 *//*


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/customerMDM")
public class CustomerMDMController {

    @Resource
    private CustomerMdmAppService customerMdmAppService;

    */
/**
     * 对接MDM接口
     *
     * @param data
     * @return
     *//*

    @PostMapping(value = "/saveOrUpdateMDMCustomer", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result saveOrUpdateMDMCustomer(@RequestPart("data") MultipartFile data){
        customerMdmAppService.saveOrUpdateMDMCustomer(data);
        return Result.success();
    }
}
*/
