package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CrisGlobalMapper;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-18 18:29
 **/
@Dao
public class CrisGlobalDao extends BaseDaoImpl<CrisGlobalMapper, CrisGlobalEntity> {

    /**
     * CaseId-1002453: RR status in navigator，Author By NaNa
     * @param customerCode 客户编码
     * @return 客户的剩余风险控制信息
     */
    public CrisGlobalEntity getCrisGlobalByCustomerCode(String customerCode) {
        List<CrisGlobalEntity> globalEntityList = this.getBaseMapper().selectList(Wrappers.<CrisGlobalEntity>lambdaQuery()
                .eq(CrisGlobalEntity::getCpId, customerCode));
        return CollectionUtils.isEmpty(globalEntityList) ? null : globalEntityList.get(0);
    }
}
