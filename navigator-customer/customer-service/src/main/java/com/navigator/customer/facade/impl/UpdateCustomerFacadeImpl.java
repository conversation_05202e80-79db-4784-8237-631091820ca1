package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.app.customer.CustomerAppService;
import com.navigator.customer.facade.UpdateCustomerFacade;
import com.navigator.customer.pojo.bo.CustomerBankBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */
@RestController
public class UpdateCustomerFacadeImpl implements UpdateCustomerFacade {


    @Resource
    private CustomerAppService customerAppService;

    @Override
    public Result saveOrUpdateCustomerProtocolFactory(CustomerProtocolDTO customerProtocolDTO) {
        return Result.success(customerAppService.saveOrUpdateCustomerProtocolFactory(customerProtocolDTO));
    }

    @Override
    public Result saveOrUpdateContactEntity(ContactDTO contactDTO) {
        return Result.success(customerAppService.saveOrUpdateContactEntity(contactDTO));
    }

    @Override
    public Result saveOrUpdateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return Result.success(customerAppService.saveOrUpdateCustomerOriginalPaper(customerOriginalPaperDTO));
    }

    @Override
    public Result saveOrUpdateCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        return Result.success(customerAppService.saveOrUpdateCustomerDepositRate(customerDepositRateDTO));
    }

    @Override
    public Result saveOrUpdateCustomerWhiteList(CustomerDetailDTO customerWhiteListDTO) {
        return Result.success(customerAppService.saveOrUpdateCustomerWhiteList(customerWhiteListDTO));
    }

    @Override
    public Result saveOrUpdateCustomerBank(CustomerBankBO customerBankBO) {
        return Result.success(customerAppService.saveOrUpdateCustomerBank(customerBankBO));
    }

    @Override
    public Result saveOrUpdateCustomerInvoiceType(CustomerInvoiceEntity customerInvoiceEntity) {
        return Result.success(customerAppService.saveOrUpdateCustomerInvoiceType(customerInvoiceEntity));
    }

    @Override
    public Result saveOrCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return Result.success(customerAppService.saveOrCustomerCreditPayment(customerCreditPaymentDTO));
    }

    @Override
    public Result saveOrUpdateCustomerGradeScore(CustomerGradeScoreEntity customerGradeScoreEntity) {
        return Result.success(customerAppService.saveOrUpdateCustomerGradeScore(customerGradeScoreEntity));
    }

    @Override
    public Result saveOrUpdateCustomerDeliveryWhite(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity) {
        return Result.success(customerAppService.saveOrUpdateCustomerDeliveryWhite(customerDeliveryWhiteEntity));
    }
}
