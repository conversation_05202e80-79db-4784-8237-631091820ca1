package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.dao.CustomerOriginalPaperDao;
import com.navigator.customer.dao.CustomerProtocolDao;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.dto.file.ImportCustomerExcelDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.customer.pojo.enums.LdcFrameEnum;
import com.navigator.customer.pojo.enums.OriginalPaperEnum;
import com.navigator.customer.service.CustomerOriginalPaperService;
import com.navigator.customer.service.ICustomerService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerOriginalPaperServiceImpl implements CustomerOriginalPaperService {
    @Autowired
    private CustomerOriginalPaperDao customerOriginalPaperDao;
    @Autowired
    private CustomerProtocolDao customerProtocolDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;
    @Resource
    private ICustomerService customerService;

    @Override
    public Result queryCustomerOriginalPaperList(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperDao.queryCustomerOriginalPaperList(customerOriginalPaperDTO);
        if (customerOriginalPaperEntities.isEmpty()) {
            return Result.success();
        }

        List<CustomerOriginalPaperDTO> customerOriginalPaperDTOS = new ArrayList<>();

        for (CustomerOriginalPaperEntity customerOriginalPaperEntity : customerOriginalPaperEntities) {
            CustomerOriginalPaperDTO customerOriginalPaper = BeanConvertUtils.convert(CustomerOriginalPaperDTO.class, customerOriginalPaperEntity);

            CompanyEntity companyEntity = companyFacade.queryCompanyById(customerOriginalPaperEntity.getCompanyId());

            if (null != customerOriginalPaper && null != companyEntity) {
                customerOriginalPaper.setCompanyName(companyEntity.getShortName());
            }

            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(customerOriginalPaperEntity.getCategory2().split(","))
                    .map(String::trim)
                    .filter(category2 -> !category2.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if(null != categoryEntity){
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }


            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = Arrays.stream(customerOriginalPaperEntity.getCategory3().split(","))
                    .map(String::trim)
                    .filter(category3 -> !category3.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category3 : category3List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                if (null != categoryEntity) {
                    category3Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category3.equals(category3List.get(category3List.size() - 1))) {
                        category3Name.append(",");
                    }
                }
            }

            Map<Integer, List<Integer>> map = categoryDisposeMap.getCategoryMap(category2List, category3List);
            customerOriginalPaper.setCategoryMap(map)
                    .setCategory2Name(category2Name.toString())
                    .setCategory3Name(category3Name.toString())
                    .setCategory1(customerOriginalPaperEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerOriginalPaperEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerOriginalPaperEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;

            customerOriginalPaperDTOS.add(customerOriginalPaper);
        }

        return Result.success(customerOriginalPaperDTOS);
    }

    @Override
    public Result saveCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperDao.queryCustomerOriginalPaper(customerOriginalPaperDTO);
        if (CollectionUtils.isNotEmpty(customerOriginalPaperEntities)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
        }
        CustomerOriginalPaperEntity customerOriginalPaperEntity = new CustomerOriginalPaperEntity();
        BeanUtils.copyProperties(customerOriginalPaperDTO, customerOriginalPaperEntity);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerOriginalPaperEntity
                .setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(currentUserId))
                .setCreatedByName(name)
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name);
        customerOriginalPaperDao.save(customerOriginalPaperEntity);
        return Result.success();
    }

    @Override
    public Result updateCustomerOriginalPaperStatus(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperDao.getById(customerOriginalPaperDTO.getId());
        if (customerOriginalPaperEntity == null) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerOriginalPaperEntity
                .setStatus(customerOriginalPaperDTO.getStatus())
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;
        customerOriginalPaperDao.updateById(customerOriginalPaperEntity);
        return Result.success();
    }

    @Override
    public Result updateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperDao.queryCustomerOriginalPaper(customerOriginalPaperDTO);

        for (CustomerOriginalPaperEntity customerOriginalPaperEntity : customerOriginalPaperEntities) {
            if (!customerOriginalPaperEntity.getId().equals(customerOriginalPaperDTO.getId())) {
                throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
            }
        }

        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperDao.getById(customerOriginalPaperDTO.getId());
        if (customerOriginalPaperEntity == null) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerOriginalPaperEntity
                .setLdcFrame(customerOriginalPaperDTO.getLdcFrame())
                .setOriginalPaper(customerOriginalPaperDTO.getOriginalPaper())
                .setSaleType(customerOriginalPaperDTO.getSaleType())
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;
        customerOriginalPaperDao.updateById(customerOriginalPaperEntity);
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        recordOperationDetail
                .setDtoData(JSON.toJSONString(customerOriginalPaperDTO))
                .setBeforeData(null)
                .setAfterData(JSON.toJSONString(customerOriginalPaperEntity))
                .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_NF)
        ;
        operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        return Result.success();
    }

    @Override
    public CustomerOriginalPaperEntity queryCustomerOriginalPaperEntity(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperDao.queryCustomerOriginalPaperList(customerOriginalPaperDTO);
        if (CollectionUtils.isNotEmpty(customerOriginalPaperEntities)) {
            return customerOriginalPaperEntities.get(0);
        }
        return null;
    }

    @Override
    public Integer queryCustomerNonFrame(CustomerOriginalPaperDTO customerOriginalPaperDTO) {

        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperDao.queryCustomerOriginalPaper(customerOriginalPaperDTO);

        return !customerOriginalPaperEntities.isEmpty() ? customerOriginalPaperEntities.get(0).getLdcFrame() : 1;
    }

    @Override
    public List<CustomerOriginalPaperEntity> queryCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperDao.queryCustomerOriginalPaper(customerOriginalPaperDTO);
    }

    @Override
    public boolean saveOrUpdateCustomerOriginalPaper(CustomerOriginalPaperEntity customerOriginalPaperEntity) {
        return customerOriginalPaperDao.saveOrUpdate(customerOriginalPaperEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importCustomer(MultipartFile file) {

        //获取所有品类
        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);

        List<ImportCustomerExcelDTO> importCustomerExcelDTOList = EasyPoiUtils.importExcel(file, 0, 1, ImportCustomerExcelDTO.class);
        for (ImportCustomerExcelDTO importCustomerExcelDTO : importCustomerExcelDTOList) {

            CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(importCustomerExcelDTO.getCustomerCode());
            //根据客户编码查询客户是否存在
            if (null == customerEntity) {
                continue;
            }

            //根据主体简称查询主体信息
            CompanyEntity companyEntity = companyFacade.getCompanyByCode(importCustomerExcelDTO.getCompanyCode());
            if (null == companyEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(importCustomerExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(importCustomerExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category3List = Arrays.stream(importCustomerExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);

            //获取LdcFrame
            Integer ldcFrame = "否".equals(importCustomerExcelDTO.getLdcFrame()) ? LdcFrameEnum.LDC.getValue() : LdcFrameEnum.NOT_FRAME.getValue();
            //获取采销类型
            Integer saleType = "销售".equals(importCustomerExcelDTO.getSaleType()) ? ContractSalesTypeEnum.SALES.getValue() : ContractSalesTypeEnum.PURCHASE.getValue();
            //是否正本
            Integer originalPaper = "是".equals(importCustomerExcelDTO.getOriginalPaper()) ? OriginalPaperEnum.ORIGINAL_PAPER.getValue() : OriginalPaperEnum.NOT_ORIGINAL_PAPER.getValue();
            //获取ID
            Integer id = StringUtils.isNotEmpty(importCustomerExcelDTO.getId()) ? Integer.parseInt(importCustomerExcelDTO.getId()) : null;
            CustomerOriginalPaperEntity customerOriginalPaperEntity = new CustomerOriginalPaperEntity();

            customerOriginalPaperEntity
                    .setId(id)
                    .setCustomerId(customerEntity.getId())
                    .setCompanyId(companyEntity.getId())
                    .setLdcFrame(ldcFrame)
                    .setSaleType(saleType)
                    .setOriginalPaper(originalPaper)
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
            ;
            customerOriginalPaperDao.saveOrUpdate(customerOriginalPaperEntity);
        }
        return Result.success();
    }

    @Override
    public CustomerOriginalPaperEntity queryCustomerOriginalPaperById(Integer id) {
        return customerOriginalPaperDao.getById(id);
    }
}
