package com.navigator.customer.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.mapper.FactoryWarehouseMapper;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/28 9:53
 */
@Dao
public class FactoryWarehouseDao extends BaseDaoImpl<FactoryWarehouseMapper, FactoryWarehouseEntity> {

    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId
     * @return
     */
    public List<FactoryWarehouseEntity> getFactoryWarehouseList(List<Integer> factoryId, String factoryCode, Integer goodsCategoryId, Integer status) {
        return this.baseMapper.selectList(Wrappers.<FactoryWarehouseEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(factoryId), FactoryWarehouseEntity::getFactoryId, factoryId)
                .eq(!StringUtils.isBlank(factoryCode), FactoryWarehouseEntity::getFactoryCode, factoryCode)
                .eq(null != goodsCategoryId, FactoryWarehouseEntity::getGoodsCategoryId, goodsCategoryId)
                .eq(null != status, FactoryWarehouseEntity::getStatus, status)
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<FactoryWarehouseEntity> getFactoryWarehouseList(FactoryQueryDTO factoryQueryDTO) {
        return this.baseMapper.selectList(Wrappers.<FactoryWarehouseEntity>lambdaQuery()
                .eq(null != factoryQueryDTO.getFactoryId(), FactoryWarehouseEntity::getFactoryId, factoryQueryDTO.getFactoryId())
                .eq(!StringUtils.isBlank(factoryQueryDTO.getFactoryCode()), FactoryWarehouseEntity::getFactoryCode, factoryQueryDTO.getFactoryCode())
                .like(!StringUtils.isBlank(factoryQueryDTO.getName()), FactoryWarehouseEntity::getName, factoryQueryDTO.getName())
                .eq(null != factoryQueryDTO.getGoodsCategoryId(), FactoryWarehouseEntity::getGoodsCategoryId, factoryQueryDTO.getGoodsCategoryId())
                .eq(null != factoryQueryDTO.getStatus(), FactoryWarehouseEntity::getStatus, factoryQueryDTO.getStatus())
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<FactoryWarehouseEntity> getWarehouseListById(List<Integer> warehouseIdList) {
        return this.baseMapper.selectList(Wrappers.<FactoryWarehouseEntity>lambdaQuery()
                .in(FactoryWarehouseEntity::getId, warehouseIdList)
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId
     * @return
     */
    public FactoryWarehouseEntity getWarehouseByName(Integer factoryId, Integer goodsCategoryId, String name) {
        List<FactoryWarehouseEntity> warehouseEntityList = this.baseMapper.selectList(Wrappers.<FactoryWarehouseEntity>lambdaQuery()
                .eq(null != factoryId, FactoryWarehouseEntity::getFactoryId, factoryId)
                .eq(!StringUtils.isBlank(name), FactoryWarehouseEntity::getName, name)
                .eq(null != goodsCategoryId, FactoryWarehouseEntity::getGoodsCategoryId, goodsCategoryId)
                .eq(FactoryWarehouseEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isEmpty(warehouseEntityList) ? null : warehouseEntityList.get(0);
    }


    /**
     * 获取lkg
     *
     * @param lkgWareHouseCode
     * @return
     */
    public FactoryWarehouseEntity getFactoryDetailByLkg(String lkgWareHouseCode) {
        List<FactoryWarehouseEntity> factoryWarehouseList = this.baseMapper.selectList(Wrappers.<FactoryWarehouseEntity>lambdaQuery()
                .eq(StringUtil.isNotEmpty(lkgWareHouseCode), FactoryWarehouseEntity::getLkgWarehouseCode, lkgWareHouseCode)
                .eq(FactoryWarehouseEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollUtil.isNotEmpty(factoryWarehouseList) ? factoryWarehouseList.get(0) : null;

    }

    /**
     * 删除所有有效库点
     *
     * @param factoryId 工厂ID
     */
    public void deleteWarehouseByFactoryId(Integer factoryId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(FactoryWarehouseEntity::getFactoryId, factoryId)
                .eq(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(FactoryWarehouseEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }

}
