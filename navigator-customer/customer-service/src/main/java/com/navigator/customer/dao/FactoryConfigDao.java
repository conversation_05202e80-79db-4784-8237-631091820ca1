package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.FactoryConfigMapper;
import com.navigator.customer.pojo.entity.FactoryConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/7 11:52
 */
@Dao
public class FactoryConfigDao extends BaseDaoImpl<FactoryConfigMapper, FactoryConfigEntity> {

    public List<FactoryConfigEntity> factoryConfigBySuppId(Integer customerId) {
        return this.baseMapper.selectList(Wrappers.<FactoryConfigEntity>lambdaQuery()
                .eq(FactoryConfigEntity::getCustomerId,customerId)
                .eq(FactoryConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }
}
