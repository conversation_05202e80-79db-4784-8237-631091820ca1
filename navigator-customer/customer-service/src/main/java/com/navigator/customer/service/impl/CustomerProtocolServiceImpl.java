package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.dao.CustomerProtocolDao;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.dto.file.CustomerProtocolExcelDTO;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.FrameProtocolEnum;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.LdcFrameEnum;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import com.navigator.customer.service.CustomerOriginalPaperService;
import com.navigator.customer.service.CustomerProtocolService;
import com.navigator.customer.service.ICustomerService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerProtocolServiceImpl implements CustomerProtocolService {
    @Autowired
    private CustomerProtocolDao customerProtocolDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private CustomerOriginalPaperService customerOriginalPaperService;
    @Autowired
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;
    @Resource
    private ICustomerService customerService;

    @Override
    public Result queryCustomerProtocolList(CustomerProtocolDTO customerProtocolDTO) {
        List<CustomerProtocolEntity> customerProtocolEntityList = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);

        List<CustomerProtocolDTO> customerProtocolDTOS = new ArrayList<>();
        for (CustomerProtocolEntity customerProtocolEntity : customerProtocolEntityList) {
            CustomerProtocolDTO customerProtocol = BeanConvertUtils.convert(CustomerProtocolDTO.class, customerProtocolEntity);

            CompanyEntity companyEntity = companyFacade.queryCompanyById(customerProtocol.getCompanyId());
            customerProtocol.setCompanyName(companyEntity.getShortName());


            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(customerProtocolEntity.getCategory2().split(","))
                    .map(String::trim)
                    .filter(category2 -> !category2.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if(null != categoryEntity){
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }

            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = Arrays.stream(customerProtocolEntity.getCategory3().split(","))
                    .map(String::trim)
                    .filter(category3 -> !category3.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category3 : category3List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                if (null != categoryEntity) {
                    category3Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category3.equals(category3List.get(category3List.size() - 1))) {
                        category3Name.append(",");
                    }
                }
            }

            customerProtocol.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, category3List))
                    .setCategory2Name(category2Name.toString())
                    .setCategory3Name(category3Name.toString())
                    .setCategory1(customerProtocolEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerProtocolEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerProtocolEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;


            if (FrameProtocolEnum.ORDER.getValue().equals(customerProtocol.getFrameProtocol())) {
                CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
                customerOriginalPaperDTO.setCustomerId(customerProtocolEntity.getCustomerId());
                customerOriginalPaperDTO.setCategoryId(customerProtocolEntity.getCategoryId());
                customerOriginalPaperDTO.setSaleType(customerProtocolEntity.getSaleType());
                customerOriginalPaperDTO.setCompanyId(customerProtocolEntity.getCompanyId());

                CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperService.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);

                if (null == customerOriginalPaperEntity || LdcFrameEnum.LDC.getValue().equals(customerOriginalPaperEntity.getLdcFrame())) {
                    customerProtocol
                            .setProtocolEndDate(null)
                            .setProtocolStartDate(null);

                }
            }
            customerProtocolDTOS.add(customerProtocol);
        }

        return Result.success(customerProtocolDTOS);
    }

    //根据条件查询客户正本

    @Override
    public List<CustomerProtocolEntity> queryCustomerProtocolEntityList(CustomerProtocolDTO customerProtocolDTO) {
        List<CustomerProtocolEntity> customerProtocolEntityList = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);
        return customerProtocolEntityList;
    }

    @Override
    public Result saveCustomerProtocol(CustomerProtocolDTO customerProtocolDTO) {
        List<CustomerProtocolEntity> customerProtocolList = customerProtocolDTO.getCustomerProtocolList();
        CustomerProtocolEntity customerProtocolEntity = new CustomerProtocolEntity();
        if (CollectionUtils.isNotEmpty(customerProtocolList)) {
            customerProtocolEntity = customerProtocolList.get(0);
            customerProtocolDao.deleteByCondition(customerProtocolEntity);
        }

        List<CustomerProtocolEntity> customerProtocolEntities = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);

        if (!customerProtocolEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_PROPERTY_EXIST);
        }

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerProtocolEntity = BeanConvertUtils.convert(CustomerProtocolEntity.class, customerProtocolDTO);
        customerProtocolEntity.setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(currentUserId))
                .setCreatedByName(name)
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name);
        customerProtocolDao.save(customerProtocolEntity);
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        recordOperationDetail
                .setDtoData(JSON.toJSONString(customerProtocolDTO))
                .setBeforeData(null)
                .setAfterData(JSON.toJSONString(customerProtocolDTO))
                .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_TEMPLATE_CONTACT_FACTORY)
        ;


        try {
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);

            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
            //记录修改客户主数据人
            customerDetailUpdateRecordEntity
                    .setDetailCode(BusinessDetailCodeEnum.UPDATE_CONTRACT_TEMPLATE.getValue())
                    .setCustomerId(customerProtocolDTO.getCustomerId())
                    .setCategoryId(customerProtocolDTO.getCategoryId())
                    .setData(JSON.toJSONString(recordOperationDetail))
                    .setCreatedAt(new Date())
                    .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success();
    }


    @Override
    public Result updateCustomerProtocol(CustomerProtocolDTO customerProtocolDTO) {

        List<CustomerProtocolEntity> customerProtocolEntities = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);

        for (CustomerProtocolEntity customerProtocolEntity : customerProtocolEntities) {
            if (!customerProtocolEntity.getId().equals(customerProtocolDTO.getId())) {
                throw new BusinessException(ResultCodeEnum.CUSTOMER_PROPERTY_EXIST);
            }
        }

        CustomerProtocolEntity customerProtocolEntity = new CustomerProtocolEntity();

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerProtocolEntity = BeanConvertUtils.convert(CustomerProtocolEntity.class, customerProtocolDTO);
        customerProtocolEntity
                .setProtocolEndDate(customerProtocolDTO.getProtocolEndDate())
                .setProtocolStartDate(customerProtocolDTO.getProtocolStartDate())
                .setProtocolNo(StringUtil.isNotEmpty(customerProtocolDTO.getProtocolNo()) ? customerProtocolDTO.getProtocolNo() : "")
                .setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(currentUserId))
                .setCreatedByName(name)
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name);
        customerProtocolDao.updateById(customerProtocolEntity);
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        recordOperationDetail
                .setDtoData(JSON.toJSONString(customerProtocolDTO))
                .setBeforeData(null)
                .setAfterData(JSON.toJSONString(customerProtocolDTO))
                .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_PROTOCOL)
        ;
        try {
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
            //记录修改客户主数据人
            customerDetailUpdateRecordEntity
                    .setDetailCode(BusinessDetailCodeEnum.UPDATE_CONTRACT_TEMPLATE.getValue())
                    .setCustomerId(customerProtocolDTO.getCustomerId())
                    .setCategoryId(customerProtocolDTO.getCategoryId())
                    .setData(JSON.toJSONString(recordOperationDetail))
                    .setCreatedAt(new Date())
                    .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);


        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success();
    }

    @Override
    public CustomerProtocolEntity queryCustomerProtocolEntity(CustomerProtocolDTO customerProtocolDTO) {
        List<CustomerProtocolEntity> customerProtocolEntityList = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);
        if (CollectionUtils.isNotEmpty(customerProtocolEntityList)) {
            return customerProtocolEntityList.get(0);
        }
        return null;
    }

    @Override
    public Result queryCustomerProtocolSign(CustomerProtocolDTO customerProtocolDTO) {
        List<CustomerProtocolEntity> customerProtocolEntityList = customerProtocolDao.queryCustomerProtocolList(customerProtocolDTO);

        CustomerProtocolDTO customerProtocol = new CustomerProtocolDTO();
        if (!customerProtocolEntityList.isEmpty()) {
            customerProtocol = BeanConvertUtils.convert(CustomerProtocolDTO.class, customerProtocolEntityList.get(0));
        }

        CustomerOriginalPaperDTO customerOriginalPaperDTO = new CustomerOriginalPaperDTO();
        customerOriginalPaperDTO.setCustomerId(customerProtocolDTO.getCustomerId());
        customerOriginalPaperDTO.setCategoryId(customerProtocolDTO.getCategoryId());
        customerOriginalPaperDTO.setSaleType(customerProtocolDTO.getSaleType());
        customerOriginalPaperDTO.setCompanyId(customerProtocolDTO.getCompanyId());

        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperService.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);

        if (null != customerOriginalPaperEntity) {
            customerProtocol
                    .setLdcFrame(customerOriginalPaperEntity.getLdcFrame());
        } else {
            customerProtocol
                    .setLdcFrame(GeneralEnum.YES.getValue());
        }

        return Result.success(customerProtocol);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importCustomerProtocol(MultipartFile file) {
        List<CustomerProtocolExcelDTO> customerProtocolExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerProtocolExcelDTO.class);

        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);
        for (CustomerProtocolExcelDTO customerProtocolExcelDTO : customerProtocolExcelDTOS) {

            CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(customerProtocolExcelDTO.getCustomerCode());
            //根据客户编码查询客户是否存在
            if (null == customerEntity) {
                continue;
            }

            //根据主体简称查询主体信息
            CompanyEntity companyEntity = companyFacade.getCompanyByCode(customerProtocolExcelDTO.getCompanyCode());
            if (null == companyEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(customerProtocolExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerProtocolExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category3List = Arrays.stream(customerProtocolExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);

            Date protocolStartDate = StringUtils.isNotEmpty(customerProtocolExcelDTO.getProtocolStartDate()) ? DateTimeUtil.formatDateTimeDate(customerProtocolExcelDTO.getProtocolStartDate()) : null;
            Date protocolEndDate = StringUtils.isNotEmpty(customerProtocolExcelDTO.getProtocolEndDate()) ? DateTimeUtil.formatDateTimeDate(customerProtocolExcelDTO.getProtocolEndDate()) : null;
            Integer frameProtocol = "有".equals(customerProtocolExcelDTO.getFrameProtocol()) ? FrameProtocolEnum.CONTRACT.getValue() : FrameProtocolEnum.ORDER.getValue();

            Integer saleType = ContractSalesTypeEnum.getByDesc(customerProtocolExcelDTO.getSaleType()).getValue();
            //参数写入实体

            Integer id = StringUtil.isNotEmpty(customerProtocolExcelDTO.getId()) ? Integer.valueOf(customerProtocolExcelDTO.getId()) : null;

            CustomerProtocolEntity customerProtocolEntity = new CustomerProtocolEntity();
            customerProtocolEntity
                    .setSaleType(saleType)
                    .setFrameProtocol(frameProtocol)
                    .setProtocolStartDate(protocolStartDate)
                    .setProtocolEndDate(protocolEndDate)
                    .setProtocolNo(customerProtocolExcelDTO.getProtocolNo())
                    .setId(id)
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
                    .setCustomerId(customerEntity.getId())
                    .setCompanyId(companyEntity.getId())
            ;
            customerProtocolDao.saveOrUpdate(customerProtocolEntity);

        }


        return null;
    }


}
