package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface CustomerOriginalPaperService {

    Result queryCustomerOriginalPaperList(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    Result saveCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    Result updateCustomerOriginalPaperStatus(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    Result updateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    CustomerOriginalPaperEntity queryCustomerOriginalPaperEntity(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    Integer queryCustomerNonFrame(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    List<CustomerOriginalPaperEntity> queryCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    boolean saveOrUpdateCustomerOriginalPaper(CustomerOriginalPaperEntity customerOriginalPaperEntity);

    Result importCustomer(MultipartFile file);

    CustomerOriginalPaperEntity queryCustomerOriginalPaperById(Integer id);
}
