package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.dao.CustomerBankDao;
import com.navigator.customer.dao.CustomerDao;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.file.CustomerBankExcelDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import com.navigator.customer.pojo.entity.BankFactoryEntity;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.BankUseTypeEnum;
import com.navigator.customer.pojo.vo.CustomerBankVO;
import com.navigator.customer.service.BankFactoryService;
import com.navigator.customer.service.ICustomerBankService;
import com.navigator.customer.service.IFactoryService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Service
@Slf4j
public class CustomerBankServiceImpl implements ICustomerBankService {

    @Resource
    private CustomerBankDao customerBankDao;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private BankFactoryService bankFactoryService;
    @Resource
    private CustomerDao customerDao;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;


    /**
     * 根据客户id查询客户银行信息
     *
     * @param customerId
     * @return
     */
    @Override
    public List<CustomerBankEntity> queryCustomerBankByCustomerId(Integer customerId, Integer referType, Integer useType) {
        return customerBankDao.queryCustomerBankByCustomerId(customerId, referType, useType);
    }

    @Override
    public List<CustomerBankVO> queryBankByCustomerId(CustomerBankDTO customerBankDTO) {

        //根据客户id查询出客户的账户信息
        List<CustomerBankEntity> customerBankEntities = customerBankDao.queryBankByConditionList(customerBankDTO);

        List<CustomerBankVO> customerBankVOS = new ArrayList<>();

        for (CustomerBankEntity customerBankEntity : customerBankEntities) {

            CustomerBankVO customerBankVO = BeanConvertUtils.convert(CustomerBankVO.class, customerBankEntity);
            BeanConvertUtils.convert2List(CustomerBankVO.class, customerBankEntities);
            //拼接主体简称
            StringBuilder shortName = new StringBuilder();

            if (StringUtils.isNotBlank(customerBankEntity.getCompanyId())) {
                String[] companyIds = customerBankEntity.getCompanyId().split(",");
                int size = companyIds.length;
                int index = 0;
                for (String companyId : companyIds) {
                    index++;
                    CompanyEntity companyEntity = companyFacade.queryCompanyById(Integer.parseInt(companyId));
                    shortName.append(companyEntity.getShortName());
                    if (size != index) {
                        shortName.append(",");
                    }
                }
            }

            customerBankVO.setCompanyShortName(shortName.toString());

            StringBuilder category2Name = new StringBuilder();
            if (StringUtils.isNotBlank(customerBankEntity.getCategory2())) {
                List<Integer> category2List = Arrays.stream(customerBankEntity.getCategory2().split(","))
                        .map(String::trim)
                        .filter(category2 -> !category2.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                for (Integer category2 : category2List) {
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                    if (null != categoryEntity) {
                        category2Name.append(categoryEntity.getName());
                        //判断是否是最后一个
                        if (!category2.equals(category2List.get(category2List.size() - 1))) {
                            category2Name.append(",");
                        }
                    }
                }
                customerBankVO.setCategory2Name(category2Name.toString());
                customerBankVO.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, null));
            }

            //查询查询工厂适用的客户账户
            List<BankFactoryEntity> bankFactoryEntities = bankFactoryService.queryBankFactoryByBankId(customerBankVO.getId());

            customerBankVO.setBankFactoryEntities(bankFactoryEntities);
            customerBankVO.setCategory1(customerBankEntity.getCategory1().replaceAll("^,+|,+$", ""));
            customerBankVO.setCategory2(customerBankEntity.getCategory2().replaceAll("^,+|,+$", ""));
            customerBankVOS.add(customerBankVO);
        }

        return customerBankVOS;
    }

    @Override
    public List<CustomerBankVO> queryCustomerBankFactory(CustomerBankFactoryDTO customerBankFactoryDTO) {

        //根据客户id查询出客户的账户信息
        List<CustomerBankEntity> customerBankEntities = customerBankDao.queryCustomerBankFactory(customerBankFactoryDTO);

        List<CustomerBankVO> customerBankVOS = new ArrayList<>();

        for (CustomerBankEntity customerBankEntity : customerBankEntities) {
            //查询银行工厂配置
            Integer factoryId = customerBankFactoryDTO.getFactoryId();
            if (StringUtils.isNotBlank(customerBankFactoryDTO.getFactoryCode())) {
                FactoryEntity factoryEntity = factoryService.getFactoryByCode(customerBankFactoryDTO.getFactoryCode());
                if (factoryEntity != null) {
                    factoryId = factoryEntity.getId();
                }
            }
            List<BankFactoryEntity> bankFactoryEntities = bankFactoryService.queryBankFactoryByBankIdFactoryId(customerBankEntity.getId(), factoryId);

            if (!bankFactoryEntities.isEmpty()) {
                CustomerBankVO customerBankVO = BeanConvertUtils.convert(CustomerBankVO.class, customerBankEntity);
                customerBankVOS.add(customerBankVO);
            }

        }

        return customerBankVOS;
    }

    @Override
    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo) {
        return customerBankDao.queryBankByBankAccountNo(bankAccountNo);
    }


    @Override
    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo, Integer categoryId) {
        return customerBankDao.queryBankByBankAccountNo(bankAccountNo, categoryId);
    }

    /**
     * 根据账户id 删除账户信息
     *
     * @param id
     * @return
     */
    @Override
    public boolean deleteCustomerBankById(Integer id) {
        CustomerBankEntity customerBankEntity = customerBankDao.getById(id);
        customerBankEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());

        //删除客户银行和油厂的关系
        //查询银行卡适用的油厂
        List<BankFactoryEntity> bankFactoryEntities = bankFactoryService.queryBankFactoryByBankId(id);

        for (BankFactoryEntity bankFactoryEntity : bankFactoryEntities) {

            bankFactoryEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());

            bankFactoryService.updateBankFactory(bankFactoryEntity);
        }


        return customerBankDao.updateById(customerBankEntity);
    }

    @Override
    public boolean updateCustomerBank(CustomerBankDTO customerBankDTO) {

        //根据id查询 账户信息是否存在
        CustomerBankEntity customerBankEntity = customerBankDao.getById(customerBankDTO.getId());

        if (null == customerBankEntity) {
            return false;
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(currentUserId));
        customerBankEntity = BeanConvertUtils.convert(CustomerBankEntity.class, customerBankDTO);
        customerBankEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(employEntity.getName());
        return customerBankDao.updateById(customerBankEntity);
    }

    @Override
    public boolean saveCustomerBank(CustomerBankDTO customerBankDTO) {
        CustomerBankEntity customerBankEntity = BeanConvertUtils.convert(CustomerBankEntity.class, customerBankDTO);
        return customerBankDao.save(customerBankEntity);
    }

    @Override
    public boolean saveOrUpdateCustomerBank(CustomerBankEntity customerBankEntity) {
        return customerBankDao.saveOrUpdate(customerBankEntity);
    }

    @Override
    public boolean redactCustomerBank(List<CustomerBankDTO> customerBankDTO) {

        Integer customerId = customerBankDTO.get(0).getCustomerId();
        Integer categoryId = customerBankDTO.get(0).getCategoryId();

        List<CustomerBankEntity> customerBankEntityS = customerBankDao.queryBankByCustomerId(customerId, categoryId, null, null);
        for (CustomerBankEntity customerBankEntity : customerBankEntityS) {
            customerBankEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
            customerBankDao.updateById(customerBankEntity);
        }

        for (CustomerBankDTO customerBank : customerBankDTO) {

            //根据id 查询 银行信息
            CustomerBankEntity customerBankEntity = customerBankDao.getById(customerBank.getId());
            //清空客户银行下油厂关系
            bankFactoryService.deleteBankFactoryByBankId(customerBank.getId());

            //判断是否为空
            if (null != customerBankEntity) {

                List<CustomerBankDTO.ApplyFactory> applyFactories = customerBank.getApplyFactories();

                for (CustomerBankDTO.ApplyFactory applyFactory : applyFactories) {
                    //查询银行卡和油厂关联信息
                    List<BankFactoryEntity> bankFactoryEntities = bankFactoryService.queryBankFactoryByBankIdFactoryId(customerBank.getId(), applyFactory.getFactoryId());

                    //银行卡和油厂没有数据时进行添加
                    if (bankFactoryEntities.isEmpty()) {

                        BankFactoryEntity bankFactoryEntity = new BankFactoryEntity()
                                .setCustomerId(customerBank.getCustomerId())
                                .setBankId(customerBankEntity.getId())
                                .setFactoryId(applyFactory.getFactoryId());
                        bankFactoryService.saveBankFactory(bankFactoryEntity);

                    } else {
                        for (BankFactoryEntity bankFactoryEntity : bankFactoryEntities) {
                            bankFactoryEntity.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                            bankFactoryService.updateBankFactory(bankFactoryEntity);
                        }
                    }
                }

                customerBank.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                this.updateCustomerBank(customerBank);
            } else {
                String currentUserId = JwtUtils.getCurrentUserId();
                EmployEntity employ = employFacade.getEmployById(Integer.parseInt(currentUserId));
                CustomerBankEntity customerBankEntity1 = BeanConvertUtils.convert(CustomerBankEntity.class, customerBank);

                /*StringBuilder companyId = new StringBuilder(CompanyEnum.TJ_IB.getShortName());
                if (StringUtils.isNotBlank(customerBank.getCompanyId())) {
                    String[] companyNames = customerBank.getCompanyId().split(",");
                    int size = companyNames.length;
                    int index = 0;
                    for (String companyName : companyNames) {
                        CompanyEnum companyEnum = CompanyEnum.getByShortName(companyName);
                        index++;
                        companyId.append(companyEnum.getShortName());
                        if (size != index) {
                            companyId.append(",");
                        }
                    }
                }
*/
                customerBankEntity1.setCategoryId(customerBank.getCategoryId())
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setCreatedBy(currentUserId)
                        .setCreatedByName(employ.getName())
                        .setUpdatedBy(currentUserId)
                        .setUpdatedByName(employ.getName())
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date())
                ;
                customerBankDao.save(customerBankEntity1);

                List<CustomerBankDTO.ApplyFactory> applyFactories = customerBank.getApplyFactories();

                for (CustomerBankDTO.ApplyFactory applyFactory : applyFactories) {
                    BankFactoryEntity bankFactoryEntity = new BankFactoryEntity()
                            .setCustomerId(customerBank.getCustomerId())
                            .setBankId(customerBankEntity1.getId())
                            .setFactoryId(applyFactory.getFactoryId());
                    bankFactoryService.saveBankFactory(bankFactoryEntity);
                }
            }
        }


        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerBankDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SBM_REDACT_CUSTOMER_BANK)
            ;

            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public CustomerBankEntity queryCustomerBankById(Integer id) {
        return customerBankDao.getById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result leadCustomerBank(MultipartFile file) {

        List<CustomerBankExcelDTO> customerBankExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerBankExcelDTO.class);

        List<String> customerCodeList = new ArrayList<>();
        //获取所有品类信息
        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);

        for (CustomerBankExcelDTO customerBankExcelDTO : customerBankExcelDTOS) {
            CustomerBankEntity customerBankEntity = new CustomerBankEntity();
            String customerCode = null;
            //查询货品数据
            List<String> category1List = Arrays.stream(customerBankExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerBankExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());

            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            //根据客户编号查询客户信息
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(customerBankExcelDTO.getLinkageCustomerCode());
            if (null == customerEntity) {
                customerCode = customerEntity.getLinkageCustomerCode() + "客户数据为空";
                customerCodeList.add(customerCode);
                continue;
            }

            StringBuilder companyIds = new StringBuilder();
            String[] companyNameList = customerBankExcelDTO.getCompanyNames().split(",");
            int size = companyNameList.length;
            int index = 0;
            for (String companyCode : companyNameList) {
                CompanyEntity company = companyFacade.getCompanyByCode(companyCode);
                index++;
                companyIds.append(company.getId());
                if (size != index) {
                    companyIds.append(",");
                }
            }

            //使用类型
            //客户银行账号信息
            customerBankEntity
                    .setId(StringUtils.isNotBlank(customerBankExcelDTO.getId()) ? Integer.parseInt(customerBankExcelDTO.getId()) : null)
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCustomerId(customerEntity.getId())
                    .setBankAccountName(customerBankExcelDTO.getBankAccountName())
                    .setBankAccountNo(customerBankExcelDTO.getBankAccountNo())
                    .setCompanyId(String.valueOf(companyIds))
                    .setBankName(customerBankExcelDTO.getBankName())
                    .setUseType(BankUseTypeEnum.getByDescription(customerBankExcelDTO.getUseType()).getValue())
            ;

            //保存编辑客户银行账户信息
            saveOrUpdateCustomerBank(customerBankEntity);

            List<CustomerBankDTO.ApplyFactory> applyFactories = new ArrayList<>();

            //根据工厂编号查询出工厂信息
            String[] split = customerBankExcelDTO.getFactoryCode().split(",");
            List<BankFactoryEntity> bankFactoryEntities = new ArrayList<>();
            for (int i = 0; i < split.length; i++) {
                FactoryEntity factoryEntity = factoryService.getFactoryByCode(split[i]);
                if (null != factoryEntity) {
                    BankFactoryEntity applyFactory = new BankFactoryEntity();
                    applyFactory.setFactoryId(factoryEntity.getId())
                            .setFactoryCode(factoryEntity.getCode());
                    bankFactoryEntities.add(applyFactory);
                }
            }

            bankFactoryEntities.forEach(bankFactoryEntity -> {
                bankFactoryEntity
                        .setBankId(customerBankEntity.getId())
                        .setCustomerId(customerBankEntity.getCustomerId());
            });

            bankFactoryService.saveOrUpdateBankFactory(bankFactoryEntities);
            //客户账号工厂信息
        }
        //this.redactCustomerBank(customerBankDTOS);
        log.info("==========为成功处理的客户账号:{}" + JSON.toJSONString(customerCodeList));
        return Result.success(customerCodeList);
    }
}
