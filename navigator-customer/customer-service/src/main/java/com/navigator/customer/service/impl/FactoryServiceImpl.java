package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.CompanyFactoryFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.dao.FactoryDao;
import com.navigator.customer.dao.FactoryWarehouseDao;
import com.navigator.customer.pojo.dto.FactoryCreateDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.vo.FactoryVO;
import com.navigator.customer.service.IFactoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
@Service
public class FactoryServiceImpl implements IFactoryService {

    @Resource
    private FactoryDao factoryDao;
    @Resource
    private FactoryWarehouseDao factoryWarehouseDao;
    @Autowired
    protected OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CompanyFactoryFacade companyFactoryFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Override
    public FactoryEntity getFactoryById(Integer id) {
        return factoryDao.getById(id);
    }

    @Override
    public List<FactoryEntity> getAllFactory(Integer status) {
        return factoryDao.getAllFactory(status);
    }

    @Override
    public FactoryEntity getFactoryDetailById(Integer factoryId) {
        return factoryDao.getById(factoryId);
    }

    @Override
    public FactoryEntity getFactoryByCode(String code) {
        return factoryDao.getFactoryByCode(code, null);
    }

    @Override
    public boolean saveOrUpdateFactory(FactoryCreateDTO factoryCreateDTO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null == factoryCreateDTO.getId()) {
            //工厂简称唯一
            if (null != factoryDao.getFactoryByShortName(factoryCreateDTO.getShortName(), DisableStatusEnum.ENABLE.getValue())) {
                throw new BusinessException(ResultCodeEnum.FACTORY_SHORT_NAME_REPEAT);
            }
            // 保存工厂信息
            FactoryEntity factoryEntity = BeanConvertUtils.convert(FactoryEntity.class, factoryCreateDTO);
            factoryEntity.setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCode(factoryCreateDTO.getShortName())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedBy(currentUserId)
                    .setCreatedByName(name)
                    .setUpdatedBy(currentUserId)
                    .setUpdatedByName(name)
            ;
            factoryDao.save(factoryEntity);
            factoryCreateDTO.setId(factoryEntity.getId());

            //case:1002695 测试发现新增工厂未绑定主体 Author:Wan 2024-07-09 start
//            //绑定工厂和主体关系
//            FactoryCompanyDTO factoryCompanyDTO = new FactoryCompanyDTO()
//                    .setFactoryId(factoryEntity.getId())
//                    .setCompanyIdList(factoryCreateDTO.getCompanyIdList());
//            companyFactoryFacade.saveFactoryCompany(factoryCompanyDTO);
            //case:1002695 测试发现新增工厂未绑定主体 Author:Wan 2024-07-09 and

        } else {
            //更新工厂信息
            FactoryEntity factoryEntity = this.checkFactory(factoryCreateDTO.getId());
            factoryEntity = BeanConvertUtils.copy(factoryEntity, factoryCreateDTO);
            factoryEntity.setCode(factoryCreateDTO.getShortName())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setUpdatedBy(currentUserId)
                    .setUpdatedByName(name);
            factoryDao.updateById(factoryEntity);
        }
        //保存发货库点信息
//        this.saveOrUpdateWarehouseInfo(factoryCreateDTO);
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(factoryCreateDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_OR_UPDATE_FACTORY)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean updateStatus(Integer factoryId) {
        FactoryEntity factoryEntity = this.checkFactory(factoryId);
        Integer newStatus = DisableStatusEnum.ENABLE.getValue().equals(factoryEntity.getStatus()) ?
                DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();
        factoryDao.updateById(factoryEntity.setStatus(newStatus));
        return true;
    }

    @Override
    public List<FactoryEntity> queryFactoryByCompanyId(Integer companyId) {
        List<FactoryCompanyEntity> factoryCompanyEntities = companyFactoryFacade.queryFactoryByCompanyId(companyId);
        List<Integer> factoryIdList = factoryCompanyEntities.stream().map(FactoryCompanyEntity::getFactoryId).distinct().collect(Collectors.toList());
        List<FactoryEntity> factoryEntityList = factoryDao.getAllFactory(1);
        return factoryEntityList.stream().filter(i -> factoryIdList.contains(i.getId())).collect(Collectors.toList());
    }

    @Override
    public List<FactoryVO> queryFactoryList() {
        List<FactoryEntity> factoryEntityList = factoryDao.getAllFactory(1);
        List<FactoryCompanyEntity> factoryCompanyEntities = companyFactoryFacade.queryCompanyFactoryList();
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName, (k1, k2) -> k1));
        Map<Integer, List<Integer>> factoryIdCompanyMap =
                factoryCompanyEntities.stream()
                        .collect(Collectors.groupingBy(FactoryCompanyEntity::getFactoryId, Collectors.mapping(FactoryCompanyEntity::getCompanyId, Collectors.toList())));
        return factoryEntityList.stream().map(i -> {
            FactoryVO factoryVO = new FactoryVO();
            BeanUtils.copyProperties(i, factoryVO);
            List<Integer> companyIdList = factoryIdCompanyMap.get(i.getId()) == null ? new ArrayList<>() : factoryIdCompanyMap.get(i.getId());
            List<String> companyNameList = companyIdList.stream().map(companyNameMap::get).collect(Collectors.toList());
            factoryVO.setCompanyIdList(companyIdList);
            factoryVO.setCompanyNameList(companyNameList);
            return factoryVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateFactoryById(FactoryEntity factoryEntity) {
        factoryDao.updateById(factoryEntity);
    }

    /**
     * 校验工厂信息
     *
     * @param factoryId 工厂ID
     * @return 工厂实体
     */
    private FactoryEntity checkFactory(Integer factoryId) {
        FactoryEntity factoryEntity = factoryDao.getById(factoryId);
        if (null == factoryEntity) {
            throw new BusinessException(ResultCodeEnum.FACTORY_NOT_EXIT);
        }
        return factoryEntity;
    }
}
