package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.mapper.CustomerDetailUpdateRecordMapper;
import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */
@Dao
public class CustomerDetailUpdateRecordDao extends BaseDaoImpl<CustomerDetailUpdateRecordMapper, CustomerDetailUpdateRecordEntity> {

    public CustomerDetailUpdateRecordEntity detailUpdateSelect(CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO) {

        QueryDTO queryDTO = new QueryDTO();
        IPage<CustomerDetailUpdateRecordEntity> iPage = this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), Wrappers.<CustomerDetailUpdateRecordEntity>lambdaQuery()
                .eq(CustomerDetailUpdateRecordEntity::getCustomerId, customerDetailUpdateRecordDTO.getCustomerId())
                .eq(null != customerDetailUpdateRecordDTO.getCategoryId(), CustomerDetailUpdateRecordEntity::getCategoryId, customerDetailUpdateRecordDTO.getCategoryId())
                .eq(CustomerDetailUpdateRecordEntity::getDetailCode, customerDetailUpdateRecordDTO.getDetailCode())
                .eq(null != customerDetailUpdateRecordDTO.getCompanyId(), CustomerDetailUpdateRecordEntity::getCompanyId, customerDetailUpdateRecordDTO.getCompanyId())
                .orderByDesc(CustomerDetailUpdateRecordEntity::getCreatedAt)
        );
        List<CustomerDetailUpdateRecordEntity> customerDetailUpdateRecordEntities = BeanConvertUtils.convert2List(CustomerDetailUpdateRecordEntity.class, iPage.getRecords());
        return customerDetailUpdateRecordEntities.isEmpty() ? null : customerDetailUpdateRecordEntities.get(0);
    }
}
