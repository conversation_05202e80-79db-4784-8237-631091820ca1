package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerBusinessSceneMapper;
import com.navigator.customer.pojo.dto.CustomerBusinessSceneDTO;
import com.navigator.customer.pojo.entity.CustomerBusinessSceneEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/31
 */

@Dao
public class CustomerBusinessSceneDao extends BaseDaoImpl<CustomerBusinessSceneMapper, CustomerBusinessSceneEntity> {

    public List<CustomerBusinessSceneEntity> queryCustomerBusinessSceneByCustomerId(CustomerBusinessSceneDTO customerBusinessSceneDTO) {
        return this.baseMapper.selectList(Wrappers.<CustomerBusinessSceneEntity>lambdaQuery()
                .eq(CustomerBusinessSceneEntity::getCustomerId, customerBusinessSceneDTO.getCustomerId())
                .eq(CustomerBusinessSceneEntity::getBizType, customerBusinessSceneDTO.getBizType())
                .eq(CustomerBusinessSceneEntity::getBizId, customerBusinessSceneDTO.getBizId())
                .eq(CustomerBusinessSceneEntity::getRelationType, customerBusinessSceneDTO.getRelationType())
                .eq(CustomerBusinessSceneEntity::getBindCode, customerBusinessSceneDTO.getBindCode())
                .eq(CustomerBusinessSceneEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
