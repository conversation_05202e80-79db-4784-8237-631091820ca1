package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.pojo.entity.CustomerCreditPaymentEntity;
import com.navigator.customer.pojo.vo.CustomerCreditPaymentVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 17:10
 */
public interface CustomerCreditPaymentService {

    /**
     * 查询客户赊销&预付款信息
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    List<CustomerCreditPaymentVO> customerCreditPaymentAllList(CustomerCreditPaymentDTO customerCreditPaymentDTO);

    /**
     * 修改客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    boolean updateCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO);

    /**
     * 添加客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    boolean addCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO);

    /**
     * 保存或更新客户赊销&预付款
     *
     * @param customerCreditPaymentEntity
     * @return
     */
    boolean saveOrUpdateCustomerCreditPayment(CustomerCreditPaymentEntity customerCreditPaymentEntity);

    /**
     * 导入客户赊销数据
     *
     * @param file
     * @return
     */
    Result leadCustomerCreditPayment(MultipartFile file);

    CustomerCreditPaymentEntity queryCustomerCreditPaymentById(Integer Id);
}
