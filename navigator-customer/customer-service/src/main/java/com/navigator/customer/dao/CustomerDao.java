package com.navigator.customer.dao;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ModifyTypeEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.mapper.CustomerMapper;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.qo.CustomerQO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Dao
public class CustomerDao extends BaseDaoImpl<CustomerMapper, CustomerEntity> {

    /**
     * 查询所有客户信息
     *
     * @param queryDTO
     * @return
     */
    public IPage<CustomerEntity> queryCustomerAll(QueryDTO<CustomerDTO> queryDTO) {

        CustomerDTO customerDTO = queryDTO.getCondition();
        Page<CustomerEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());

        String linkageCustomerCode = StrUtil.isNotBlank(customerDTO.getLinkageCustomerCode()) ? customerDTO.getLinkageCustomerCode().trim() : customerDTO.getLinkageCustomerCode();

        LambdaQueryWrapper<CustomerEntity> wrapper = new LambdaQueryWrapper<CustomerEntity>()
                .eq(null != customerDTO.getIsCustomer(), CustomerEntity::getIsCustomer, customerDTO.getIsCustomer())
                .eq(null != customerDTO.getIsSupplier(), CustomerEntity::getIsSupplier, customerDTO.getIsSupplier())
                .eq(null != customerDTO.getEnterprise() && customerDTO.getEnterprise() > 0, CustomerEntity::getEnterprise, customerDTO.getEnterprise())
                .eq(StrUtil.isNotBlank(linkageCustomerCode), CustomerEntity::getLinkageCustomerCode, linkageCustomerCode)
                .like(StrUtil.isNotBlank(customerDTO.getName()), CustomerEntity::getName, customerDTO.getName().trim())
                .like(StrUtil.isNotBlank(customerDTO.getUpdatedByName()), CustomerEntity::getUpdatedByName, customerDTO.getUpdatedByName())
                .and(
                        i -> i.in(CustomerEntity::getCompanyId, Arrays.asList(0, 1))
                                .or()
                                .eq(CustomerEntity::getIsLdc, GeneralEnum.YES.getValue())
                )
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        if (null != customerDTO.getName() && customerDTO.getName().matches("[a-zA-Z]+")) {

            String name = customerDTO.getName().toUpperCase();
            wrapper.and(
                    i -> i.or()
                            .like(CustomerEntity::getCustomerIndexesName, name.trim())
            );
        }
        wrapper.orderByDesc(CustomerEntity::getId);
        return this.getBaseMapper().selectPage(page, wrapper);
    }

    /**
     * 查询客户供应商信息
     *
     * @return
     */
    public IPage<CustomerEntity> queryCustomerSupplierAll(QueryDTO<CustomerDTO> queryDTO) {

        ObjectMapper mapper = new ObjectMapper();
        CustomerDTO customerDTO = mapper.convertValue(queryDTO.getCondition(), CustomerDTO.class);

        LambdaQueryWrapper<CustomerEntity> wrapper = new LambdaQueryWrapper<CustomerEntity>()
                .eq(null != customerDTO.getIsSupplier(), CustomerEntity::getIsSupplier, customerDTO.getIsSupplier())
                .eq(null != customerDTO.getIsCustomer(), CustomerEntity::getIsCustomer, customerDTO.getIsCustomer())
                .eq(null != customerDTO.getIsLdc(), CustomerEntity::getIsLdc, customerDTO.getIsLdc())
                .like(StrUtil.isNotBlank(customerDTO.getName()), CustomerEntity::getName, customerDTO.getName().trim())
                //.like(null != customerDTO.getName(), CustomerEntity::getName, customerDTO.getName())
                .eq(null != customerDTO.getEnterprise() && customerDTO.getEnterprise() > 0, CustomerEntity::getEnterprise, customerDTO.getEnterprise())
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        if (null != customerDTO.getSplit() && ModifyTypeEnum.SPLIT.getValue().equals(customerDTO.getSplit())) {
            CustomerEntity customer = this.getById(customerDTO.getId());
            if (StrUtil.isNotBlank(customer.getEnterpriseName())) {
                wrapper.eq(CustomerEntity::getEnterpriseName, customer.getEnterpriseName());
            } else {
                wrapper.eq(CustomerEntity::getId, customer.getId());
            }

        }

        if (null == customerDTO.getIsLdc()) {
            wrapper.and(
                    i -> i.in(CustomerEntity::getCompanyId, Arrays.asList(0, 1))
                            .or()
                            .eq(CustomerEntity::getIsLdc, GeneralEnum.YES.getValue())
            );
        }

        if (null == customerDTO.getIsLdc()) {
            wrapper.in(CustomerEntity::getCompanyId, Arrays.asList(0, 1));
        }

        if (null != customerDTO.getName() && customerDTO.getName().matches("[a-zA-Z]+")) {

            String name = customerDTO.getName().toUpperCase();
            wrapper.or(wrappers -> wrappers.like(CustomerEntity::getCustomerIndexesName, name.trim()));
        }

        wrapper.orderByAsc(CustomerEntity::getId);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
    }


    /**
     * 根据id查询客户信息
     *
     * @param id
     * @return
     */
    public CustomerEntity queryCustomerById(Integer id) {
        return this.getBaseMapper().selectOne(Wrappers.<CustomerEntity>lambdaQuery().eq(CustomerEntity::getId, id)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CustomerEntity> queryCustomerEntityList(CustomerDTO customerDTO) {

        String linkageCustomerCode = StrUtil.isNotBlank(customerDTO.getLinkageCustomerCode()) ? customerDTO.getLinkageCustomerCode().trim() : customerDTO.getLinkageCustomerCode();

        LambdaQueryWrapper<CustomerEntity> wrapper = new LambdaQueryWrapper<CustomerEntity>()
                .eq(StrUtil.isNotBlank(linkageCustomerCode), CustomerEntity::getLinkageCustomerCode, linkageCustomerCode)
                .like(StrUtil.isNotBlank(customerDTO.getName()), CustomerEntity::getName, customerDTO.getName().trim())
                .like(StrUtil.isNotBlank(customerDTO.getUpdatedByName()), CustomerEntity::getUpdatedByName, customerDTO.getUpdatedByName())
                .in(CustomerEntity::getCompanyId, Arrays.asList(0, 1))
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        if (null != customerDTO.getName() && customerDTO.getName().matches("[a-zA-Z]+")) {
            String name = customerDTO.getName().toUpperCase();
            wrapper.or(wrappers -> wrappers.like(CustomerEntity::getCustomerIndexesName, name.trim()));
        }
        wrapper.orderByDesc(CustomerEntity::getId);

        return this.getBaseMapper().selectList(wrapper);
    }


    /**
     * 根据预警查询用户id
     *
     * @param useYqq
     * @param frameExpired
     * @param originalPaper
     * @param templateExpired
     * @return
     */
    public List<CustomerDTO> getCustomerEarlyWarning(Integer useYqq,
                                                     Integer frameExpired,
                                                     Integer originalPaper,
                                                     Integer isColumbus,
                                                     Integer templateExpired) {
        List<CustomerEntity> customerDTOS = this.getBaseMapper().selectList(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(useYqq != null, CustomerEntity::getUseYqq, useYqq)
                .eq(isColumbus != null, CustomerEntity::getIsColumbus, isColumbus)
                //.eq(frameExpired != null, CustomerEntity::getFrameExpired, frameExpired)
                .eq(originalPaper != null, CustomerEntity::getOriginalPaper, originalPaper)
                .eq(templateExpired != null, CustomerEntity::getTemplateExpired, templateExpired)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return BeanConvertUtils.convert2List(CustomerDTO.class, customerDTOS);

    }


    public Result querySupplierList(Integer isSupplier) {
        List<CustomerEntity> customerEntities = this.getBaseMapper().selectList(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(CustomerEntity::getIsSupplier, isSupplier)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return Result.success(customerEntities);
    }

    public CustomerEntity queryCustomerByLinkageCustomerCode(String linkageCustomerCode) {
        List<CustomerEntity> customerEntityList = this.getBaseMapper().selectList(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(CustomerEntity::getLinkageCustomerCode, linkageCustomerCode.trim())
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(customerEntityList) ? null : customerEntityList.get(0);
    }

    public List<CustomerEntity> queryCustomerByCustomerName(String name) {
        return this.getBaseMapper().selectList(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(CustomerEntity::getName, name)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CustomerEntity> queryCustomerByIdList(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            idList = Collections.singletonList(-1);
        }
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .in(CustomerEntity::getId, idList)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public List<CustomerEntity> getCustomerByIdList(List<Integer> idList) {
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .in(CustomerEntity::getId, idList)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public List<CustomerEntity> querySonCustomerList(Integer customerId) {
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(CustomerEntity::getParentId, customerId)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CustomerEntity> getCustomerListByCode(List<String> customerCodeList) {
        if (CollectionUtils.isEmpty(customerCodeList)) {
            return new ArrayList<>();
        }
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .in(CustomerEntity::getLinkageCustomerCode, customerCodeList)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CustomerEntity> getCustomerListByTemplateVipCode(String templateVipCode) {
        if (StringUtils.isBlank(templateVipCode)) {
            return new ArrayList<>();
        }
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .in(CustomerEntity::getTemplateVipCode, templateVipCode)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public Boolean updateCustomerTemplateVip(String templateVipCode, List<String> customerCodeList) {
        this.initCustomerTemplateVip(templateVipCode);
        return new LambdaUpdateChainWrapper<>(this.baseMapper)
                .in(CustomerEntity::getLinkageCustomerCode, customerCodeList)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(CustomerEntity::getTemplateVipCode, templateVipCode)
                .set(CustomerEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    public Boolean initCustomerTemplateVip(String templateVipCode) {
        return new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(CustomerEntity::getTemplateVipCode, templateVipCode)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(CustomerEntity::getTemplateVipCode, "")
                .set(CustomerEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    public List<CustomerTemplateDeriveGradeScoreDTO> getSbmGradeCustomerList(String customerCode, String name, String updatedByName) {
        return getBaseMapper().getSbmGradeCustomerList(customerCode, name, updatedByName);
    }

    public List<CustomerTemplateDeriveGradeScoreDTO> getSboGradeCustomerList(String customerCode, String name, String updatedByName) {
        return getBaseMapper().getSboGradeCustomerList(customerCode, name, updatedByName);
    }

    public List<CustomerEntity> queryCustomerByCompanyAndLDC(Integer companyId, Integer isLdc) {

        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(null != companyId, CustomerEntity::getCompanyId, companyId)
                .eq(null != isLdc, CustomerEntity::getIsLdc, isLdc)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public CustomerEntity queryCustomerByCompanyAndFactory(Integer factoryId, Integer companyId) {
        List<CustomerEntity> list = list(Wrappers.<CustomerEntity>lambdaQuery()
                .eq(CustomerEntity::getFactoryId, factoryId)
                .eq(CustomerEntity::getCompanyId, companyId)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<CustomerEntity> queryFactoryCustomer() {
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .isNotNull(CustomerEntity::getFactoryId)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    public List<CustomerEntity> queryCustomerList(CustomerQO condition) {
        return this.list(CustomerEntity.lqw(condition));
    }

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 start
    public List<Integer> getCustomerIdsByEnterpriseName(String enterpriseName) {
        return list(Wrappers.<CustomerEntity>lambdaQuery()
                .like(CustomerEntity::getEnterpriseName, enterpriseName)
                .eq(CustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()))
                .stream()
                .map(CustomerEntity::getId)
                .collect(Collectors.toList());
    }
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 end
}
