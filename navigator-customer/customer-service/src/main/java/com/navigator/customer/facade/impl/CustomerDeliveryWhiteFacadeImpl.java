package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDeliveryWhiteFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.vo.CustomerDeliveryWhiteFileReturnVO;
import com.navigator.customer.service.CustomerDeliveryWhiteService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@RestController
public class CustomerDeliveryWhiteFacadeImpl implements CustomerDeliveryWhiteFacade {

    @Resource
    private CustomerDeliveryWhiteService customerDeliveryWhiteService;

    @Override
    public List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhite(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        return customerDeliveryWhiteService.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
    }

    @Override
    public List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhiteList(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        return customerDeliveryWhiteService.queryCustomerDeliveryWhiteList(customerDeliveryWhiteDTO);
    }


    @Override
    public List<CustomerDeliveryWhiteExcelDTO> downloadCustomerWhitelist(CustomerDTO customerDTO) {
        return customerDeliveryWhiteService.downloadCustomerWhitelist(customerDTO);
    }

    @Override
    public Result checkCustomerWhitelist(MultipartFile file) {
        return Result.success(customerDeliveryWhiteService.checkCustomerWhitelist(file));
    }

    @Override
    public Result uploadCustomerWhitelist(MultipartFile file) {
        return Result.success(customerDeliveryWhiteService.uploadCustomerWhitelist(file));
    }
}
