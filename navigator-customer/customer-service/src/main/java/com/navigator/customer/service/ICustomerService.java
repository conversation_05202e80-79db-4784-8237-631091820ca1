package com.navigator.customer.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDTO;
import com.navigator.customer.pojo.dto.SystemAndCustomerDTO;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.qo.CustomerQO;
import com.navigator.customer.pojo.vo.CustomerTemplateVO;
import com.navigator.customer.pojo.vo.CustomerVerifyVO;
import com.navigator.customer.pojo.vo.SystemAndCustomerVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 客户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface ICustomerService {
    /**
     * 查询所有客户信息
     *
     * @param queryDTO
     * @return
     */
    Result queryCustomerList(QueryDTO<CustomerDTO> queryDTO);

    /**
     * 查询客户信息
     *
     * @param customerDTO
     * @return
     */
    List<CustomerEntity> queryCustomerEntityList(CustomerDTO customerDTO);

    /**
     * 根据id查询客户全部信息
     *
     * @param id
     * @return
     */
    CustomerDTO getCustomerById(Integer id);

    /**
     * 根据客户id 品种查询客户信息
     *
     * @param id
     * @return
     */
    CustomerDTO queryCustomerByIdCategoryId(Integer id, Integer categoryId);


    /**
     * 根据id查询客户信息
     *
     * @param id
     * @return
     */
    CustomerEntity queryCustomerById(Integer id);

    /**
     * 根据预警查询用户id
     *
     * @return
     */
    List<CustomerDTO> getCustomerEarlyWarning(Integer useYqq,
                                              Integer framePast,
                                              Integer originalPaper,
                                              Integer isColumbus,
                                              Integer templatePast);

    /**
     * 新增编辑客户信息
     *
     * @param customerEntity
     * @return
     */
    boolean saveOrUpdateCustomer(CustomerEntity customerEntity);

    /**
     * 查询客户供应商信息
     *
     * @return
     */
    Result queryCustomerSupplierAll(QueryDTO<CustomerDTO> queryDTO);

    /**
     * 根据客户id查询客户是否易企签,是否实名
     *
     * @param id
     * @return
     */
    Integer customerSignature(Integer id) throws Exception;


    /**
     * 查询供应商
     *
     * @param isSupplier
     * @return
     */
    Result querySupplierList(Integer isSupplier);


    Result queryCustomerAll();


    /**
     * 根据客户id 查询出客户信息  系统及账号
     *
     * @param customerId
     * @return
     */
    SystemAndCustomerVO getSystemAndCustomerById(Integer customerId);


    /**
     * 系统及账号提交
     *
     * @param systemAndCustomerDTO
     * @return
     */
    void updateSystemAndCustomer(SystemAndCustomerDTO systemAndCustomerDTO);


    /**
     * 更新客户信息
     *
     * @param customerEntity
     * @return
     */
    boolean updateCustomer(CustomerEntity customerEntity);

    boolean addCustomer(CustomerEntity customerEntity);

    /**
     * 合同模板 通知人 信息查询
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    CustomerTemplateVO queryTemplateContactFactoryByCustomerId(Integer customerId, Integer categoryId);

    /**
     * 修改合同模板 通知人 信息
     *
     * @param customerTemplateDTO
     * @return
     */
    boolean updateTemplateContactFactory(CustomerTemplateDTO customerTemplateDTO);


    String saveCustomerConfig(MultipartFile file);

    /**
     * 导入客户数据校验
     *
     * @param file
     * @return
     */
    CustomerVerifyVO verifyFileCustomerConfig(MultipartFile file);


    Result importDataCustomerFile(MultipartFile file);


    /**
     * 根据客户id查询 客户是否使用系统,是否使用易企签,是否实名  易企签通用配置是否启用
     *
     * @param customerId
     * @return
     */
    Result customerSignParameter(Integer customerId);

    /**
     * 查询客户所有信息
     *
     * @param customerAllMessageDTO
     * @return
     */
    CustomerDTO queryCustomerAllMessage(CustomerAllMessageDTO customerAllMessageDTO);


    /**
     * 根据linkage客户编码查询客户信息
     *
     * @param linkageCustomerCode
     * @return
     */
    CustomerEntity queryCustomerByLinkageCode(String linkageCustomerCode);

    Boolean customerJudge(Integer beforeCustomerId,Integer afterCustomerId);

    /**
     * 查询客户联系人信息
     *
     * @param customerAllMessageDTO
     * @return
     */
    CustomerDTO queryCustomerContactAllMessage(CustomerAllMessageDTO customerAllMessageDTO);


    Integer updateCustomerParentId();

    List<CustomerEntity> queryCustomerByIdList(List<Integer> idList);

    List<CustomerEntity> getCustomerListByCode(List<String> customerCodeList);

    List<CustomerEntity> getCustomerListByTemplateVipCode(String templateVipCode);

    Boolean updateCustomerTemplateVip(String templateVipCode, List<String> customerCodeList);

    List<CustomerEntity> querySonCustomerList(Integer customerId);

    CustomerEntity queryCustomerByCompanyAndLDC(Integer companyId);

    List<CustomerEntity> getCustomerByCompanyId(Integer isLdc, Integer companyId);

    /**
     * 删除客户主数据
     *
     * @param customerId
     * @return
     */
    boolean deleteCustomerById(Integer customerId);

    CustomerEntity queryCustomerByCompanyAndFactory(Integer factoryId, Integer companyId);

    List<CustomerEntity> queryFactoryCustomer();

    CustomerEntity getLdcSupplierByName(String supplierName);
    /**
     * CaseId-1002453: RR status in navigator，Author By NaNa
     * @param customerId
     * @return 客户的剩余风险控制信息
     */
    CrisGlobalEntity getCustomerResidualRiskInfo(Integer customerId);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<CustomerEntity> queryCustomerList(CustomerQO condition);

    /**
     * 更新客户交易状态
     */
    void updateCustomerTradeStatus();

    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 start
    /**
     * 根据集团客户名称获取客户id
     *
     * @param enterpriseName 集团客户名称
     * @return id集合
     */
    List<Integer> getCustomerIdsByEnterpriseName(String enterpriseName);
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 end
}
