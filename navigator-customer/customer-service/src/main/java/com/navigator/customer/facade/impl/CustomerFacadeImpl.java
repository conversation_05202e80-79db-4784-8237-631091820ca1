package com.navigator.customer.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.app.mdm.CustomerMdmAppService;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.EarlyWarningEnum;
import com.navigator.customer.pojo.enums.ExpiredEnum;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.OriginalPaperEnum;
import com.navigator.customer.pojo.qo.CustomerQO;
import com.navigator.customer.pojo.vo.CustomerTemplateVO;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import com.navigator.customer.service.ICustomerDetailService;
import com.navigator.customer.service.ICustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:48
 */
@RestController
public class CustomerFacadeImpl implements CustomerFacade {
    @Resource
    private ICustomerService customerService;
    @Resource
    private ICustomerDetailService customerDetailService;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Autowired
    private EmployFacade employFacade;
    @Resource
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Resource
    private CustomerMdmAppService customerMdmAppService;


    @Override
    public Result queryCustomerList(QueryDTO<CustomerDTO> queryDTO) {
        return customerService.queryCustomerList(queryDTO);
    }

    @Override
    public CustomerDTO queryCustomerByIdCategoryId(Integer id, Integer categoryId) {
        return customerService.queryCustomerByIdCategoryId(id, categoryId);
    }

    @Override
    public CustomerDTO getCustomerById(Integer id) {
        return customerService.getCustomerById(id);
    }

    @Override
    public CustomerDTO getCustomerBizInfo(Integer id, Integer category2, Integer category3, String siteCode, Integer companyId, String factoryCode) {
        //TODO WANJIALIN
        return null;
    }

    @Override
    public CustomerEntity queryCustomerById(Integer id) {
        return customerService.queryCustomerById(id);
    }

    @Override
    public List<Integer> getCustomerEarlyWarning(Integer earlyWarning) {
        List<CustomerDTO> customerDTOS = new ArrayList<>();
        switch (EarlyWarningEnum.getByValue(earlyWarning)) {
            case FRAME_EXPIRED:
                return customerDetailService.customerProtocol();
            //customerService.getCustomerEarlyWarning(null, ExpiredEnum.EXPIRED.getValue(), null, null, null);
            case USE_WS:
                customerDTOS = customerService.getCustomerEarlyWarning(GeneralEnum.YES.getValue(), null, null, null, null);
                break;
            case NOT_USE_WS:
                customerDTOS = customerService.getCustomerEarlyWarning(GeneralEnum.NO.getValue(), null, null, GeneralEnum.YES.getValue(), null);
                break;
            case NOT_SYSTEM:
                customerDTOS = customerService.getCustomerEarlyWarning(GeneralEnum.NO.getValue(), null, null, GeneralEnum.NO.getValue(), null);
                break;
            case ORIGINAL_PAPER:
                customerDTOS = customerService.getCustomerEarlyWarning(null, null, OriginalPaperEnum.ORIGINAL_PAPER.getValue(), null, null);
                break;
            case LDC_ORIGINAL_PAPER:
                customerDTOS = customerService.getCustomerEarlyWarning(null, null, OriginalPaperEnum.EXCEED_EXPECT_ORIGINAL_PAPER.getValue(), null, null);
                break;
            case TEMPLATE_EXPIRED:
                customerDTOS = customerService.getCustomerEarlyWarning(null, null, null, null, ExpiredEnum.EXPIRED.getValue());
                break;
            default:
                break;
        }
        List<Integer> customerId = new ArrayList<>();
        for (CustomerDTO customerDTO : customerDTOS) {
            Integer Id = customerDTO.getId();
            customerId.add(Id);
        }
        return customerId;
    }

    @Override
    public Result queryCustomerSupplierAll(QueryDTO<CustomerDTO> queryDTO) {
        return customerService.queryCustomerSupplierAll(queryDTO);
    }

    @Override
    public Result queryCustomerAll() {
        return customerService.queryCustomerAll();
    }

    @Override
    public Integer customerSignature(Integer id) throws Exception {
        return customerService.customerSignature(id);
    }

    @Override
    public Result querySupplierList(Integer isSupplier) {
        return customerService.querySupplierList(isSupplier);
    }

    @Override
    public Result getSystemAndCustomerById(Integer customerId) {
        return Result.success(customerService.getSystemAndCustomerById(customerId));

    }

    @Override
    public Result updateSystemAndCustomer(SystemAndCustomerDTO systemAndCustomerDTO) {
        customerService.updateSystemAndCustomer(systemAndCustomerDTO);
        return Result.success();
    }

    @Override
    public Result replenishCustomerMessage(CustomerDTO customerDTO) {
        CustomerEntity customerEntity = customerService.queryCustomerById(customerDTO.getId());
        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_NOT_EXIST);
        }
        CustomerEntity originalCustomer = new CustomerEntity();
        BeanUtils.copyProperties(customerEntity, originalCustomer);
        String userId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(userId));
        //修改客户状态
        if (!customerEntity.getStatus().equals(customerDTO.getStatus())) {
            String referBizCode = customerEntity.getName() + "修改客户状态";

            recordTTQuery(JSON.toJSONString(customerDTO), JSON.toJSONString(customerEntity), LogBizCodeEnum.UPDATE_CUSTOMER_STATUS, customerEntity.getId(), OperationSourceEnum.SYSTEM.getValue(), userId, referBizCode);
        }
        CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
        customerDetailUpdateRecordEntity.setData(JSON.toJSONString(customerEntity));
        //塞入数据
        customerEntity
                .setIsAuthorization(customerDTO.getIsAuthorization())
                .setShortName(customerDTO.getShortName())
                .setStatus(customerDTO.getStatus())
                .setCreditDays(customerDTO.getCreditDays())
                .setSignPlace(customerDTO.getSignPlace())
                .setTemplateVipCode(StringUtils.isNotBlank(customerDTO.getTemplateVipCode()) ? customerDTO.getTemplateVipCode() : "")
                .setParentId(customerDTO.getParentId())
                .setEnterprise(customerDTO.getEnterprise())
                .setEnterpriseName(customerDTO.getEnterpriseName())
                .setEnterpriseCustomerCode(customerDTO.getEnterpriseCustomerCode())
                .setCustomerIndexesName(customerDTO.getCustomerIndexesName())
                .setInvoiceAddress(customerDTO.getInvoiceAddress())
                .setUpdatedBy(userId)
                .setUpdatedByName(name)
                .setUpdatedAt(new Date());
        boolean bool = customerService.updateCustomer(customerEntity);
        try {

            //记录修改客户主数据人
            customerDetailUpdateRecordEntity
                    .setDetailCode(customerDTO.getDetailCode())
                    .setCustomerId(customerDTO.getId())
                    .setRawData(JSON.toJSONString(customerDTO))
                    .setCreatedAt(new Date())
                    .setCreatedBy(name);
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);

            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerDTO))
                    .setBeforeData(JSON.toJSONString(originalCustomer))
                    .setAfterData(JSON.toJSONString(customerEntity))
                    .setOperationActionEnum(OperationActionEnum.REPLENISH_CUSTOMER_MESSAGE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.success(bool);
    }

    @Override
    public CustomerTemplateVO queryTemplateContactFactoryByCustomerId(Integer customerId, Integer categoryId) {
        return customerService.queryTemplateContactFactoryByCustomerId(customerId, categoryId);

    }

    @Override
    public Result updateTemplateContactFactory(CustomerTemplateDTO customerTemplateDTO) {
        return Result.success(customerService.updateTemplateContactFactory(customerTemplateDTO));
    }

    @Override
    public Result saveCustomerConfig(MultipartFile file) {
        String message = customerService.saveCustomerConfig(file);

        try {
            List<CustomerConfigDTO> customerConfigDTOArrayList = EasyPoiUtils.importExcel(file, 0, 1, CustomerConfigDTO.class);
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerConfigDTOArrayList))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_CUSTOMER_CONFIG)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.success(message);
    }

    @Override
    public Result verifyFileCustomerConfig(MultipartFile file) {
        return Result.success(customerService.verifyFileCustomerConfig(file));
    }

    @Override
    public Result importDataCustomerFile(MultipartFile file) {
        return customerService.importDataCustomerFile(file);
    }

    @Override
    public Result customerSignParameter(Integer customerId) {
        return customerService.customerSignParameter(customerId);
    }

    @Override
    public CustomerDTO queryCustomerAllMessage(CustomerAllMessageDTO customerAllMessageDTO) {
        return customerService.queryCustomerAllMessage(customerAllMessageDTO);
    }

    @Override
    public CustomerEntity queryCustomerByLinkageCode(String linkageCustomerCode) {
        return customerService.queryCustomerByLinkageCode(linkageCustomerCode);
    }

    @Override
    public Boolean customerJudge(Integer beforeCustomerId, Integer afterCustomerId) {
        return customerService.customerJudge(beforeCustomerId, afterCustomerId);
    }


    @Override
    public CustomerDTO queryCustomerContactAllMessage(CustomerAllMessageDTO customerAllMessageDTO) {
        return customerService.queryCustomerContactAllMessage(customerAllMessageDTO);
    }

    @Override
    public Integer updateCustomerParentId() {
        return customerService.updateCustomerParentId();
    }

    @Override
    public Result queryCustomerByIdList(List<Integer> idList) {
        List<CustomerEntity> customerEntities = customerService.queryCustomerByIdList(idList);
        return Result.success(customerEntities);
    }

    @Override
    public Result querySonCustomerList(Integer customerId) {
        List<CustomerEntity> customerEntities = customerService.querySonCustomerList(customerId);
        return Result.success(customerEntities);
    }

    @Override
    public Result deleteCustomerById(Integer customerId) {
        return Result.success(customerService.deleteCustomerById(customerId));
    }

    @Override
    public CustomerEntity queryCustomerByCompanyAndFactory(Integer factoryId, Integer companyId) {
        return customerService.queryCustomerByCompanyAndFactory(factoryId, companyId);
    }

    @Override
    public CustomerEntity queryCustomerByCompanyAndLDC(Integer companyId) {
        return customerService.queryCustomerByCompanyAndLDC(companyId);
    }

    @Override
    public List<CustomerEntity> queryFactoryCustomer() {
        return customerService.queryFactoryCustomer();
    }

    @Override
    public CustomerEntity getLdcSupplierByName(String supplierName) {
        return customerService.getLdcSupplierByName(supplierName);
    }

    @Override
    public List<CustomerEntity> getCustomerListByCode(List<String> customerCodeList) {
        return customerService.getCustomerListByCode(customerCodeList);
    }

    @Override
    public List<CustomerEntity> getCustomerListByTemplateVipCode(String templateVipCode) {
        return customerService.getCustomerListByTemplateVipCode(templateVipCode);
    }

    @Override
    public Boolean updateCustomerTemplateVip(String templateVipCode, List<String> customerCodeList) {
        return customerService.updateCustomerTemplateVip(templateVipCode, customerCodeList);
    }

    @Override
    public List<CustomerEntity> getCustomerByCompanyId(Integer isLdc, Integer companyId) {
        return customerService.getCustomerByCompanyId(isLdc, companyId);
    }

    @Override
    public CrisGlobalEntity getCustomerResidualRiskInfo(Integer customerId) {
        return customerService.getCustomerResidualRiskInfo(customerId);
    }

    @Override
    public boolean addCustomer(CustomerEntity customerEntity) {
        return customerService.addCustomer(customerEntity);
    }

    @Override
    public Result updateCustomer(CustomerEntity customerEntity) {
        return Result.success(customerService.updateCustomer(customerEntity));
    }


    /**
     * 记录日志
     *
     * @param paramDTO
     * @param bizCodeEnum
     * @param customerId
     * @param logLevel
     */
    @Async
    public void recordTTQuery(String paramDTO, String metaData, LogBizCodeEnum bizCodeEnum, Object customerId, Integer logLevel, String userId, String referBizCode) {
        int i;
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getBizCode())
                .setBizModule(ModuleTypeEnum.CUSTOMER.getDesc())
                .setLogLevel(logLevel)
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt(userId))
                .setOperationName(null == bizCodeEnum ? "UNKNOWN" : bizCodeEnum.getMsg())
                .setMetaData(metaData)
                .setData(paramDTO)
                .setCreatedAt(DateTimeUtil.now())
                .setReferBizId((Integer) customerId)
                .setReferBizCode(referBizCode);

        operationLogFacade.recordOperationLogOLD(operationDetailDTO);

    }

    @Override
    public List<CustomerEntity> queryCustomerList(CustomerQO condition) {
        return customerService.queryCustomerList(condition);
    }

    @Override
    public void updateCustomerTradeStatus() {
        customerService.updateCustomerTradeStatus();
    }

    @Override
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 start
    public List<Integer> getCustomerIdsByEnterpriseName(String enterpriseName) {
        return customerService.getCustomerIdsByEnterpriseName(enterpriseName);
    }
    // 优化：case-1002942 页面查询功能阻塞 Author: Mr 2025-02-21 end
}
