package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerOriginalPaperMapper;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import org.redisson.api.RType;

import java.util.List;

@Dao
public class CustomerOriginalPaperDao extends BaseDaoImpl<CustomerOriginalPaperMapper, CustomerOriginalPaperEntity> {

    public List<CustomerOriginalPaperEntity> queryCustomerOriginalPaperList(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return list(Wrappers.<CustomerOriginalPaperEntity>lambdaQuery()
                .eq(CustomerOriginalPaperEntity::getCustomerId, customerOriginalPaperDTO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory1()), CustomerOriginalPaperEntity::getCategory1, "," + customerOriginalPaperDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory2()), CustomerOriginalPaperEntity::getCategory2, "," + customerOriginalPaperDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory3()), CustomerOriginalPaperEntity::getCategory3, "," + customerOriginalPaperDTO.getCategory3() + ",")
                .eq(null != customerOriginalPaperDTO.getSaleType(), CustomerOriginalPaperEntity::getSaleType, customerOriginalPaperDTO.getSaleType())
                .eq(null != customerOriginalPaperDTO.getCompanyId(), CustomerOriginalPaperEntity::getCompanyId, customerOriginalPaperDTO.getCompanyId())
                .eq(null != customerOriginalPaperDTO.getStatus(), CustomerOriginalPaperEntity::getStatus, customerOriginalPaperDTO.getStatus())
                .eq(CustomerOriginalPaperEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CustomerOriginalPaperEntity::getUpdatedAt)
        );
    }

    public List<CustomerOriginalPaperEntity> queryCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return list(Wrappers.<CustomerOriginalPaperEntity>lambdaQuery()
                .eq(CustomerOriginalPaperEntity::getCustomerId, customerOriginalPaperDTO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory1()), CustomerOriginalPaperEntity::getCategory1, "," + customerOriginalPaperDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory2()), CustomerOriginalPaperEntity::getCategory2, "," + customerOriginalPaperDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerOriginalPaperDTO.getCategory3()), CustomerOriginalPaperEntity::getCategory3, "," + customerOriginalPaperDTO.getCategory3() + ",")
                .eq(CustomerOriginalPaperEntity::getSaleType, customerOriginalPaperDTO.getSaleType())
                .eq(CustomerOriginalPaperEntity::getCompanyId, customerOriginalPaperDTO.getCompanyId())
                .eq(CustomerOriginalPaperEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CustomerOriginalPaperEntity::getUpdatedAt)
        );
    }

    public void deleteByCustomerId(String customerId) {
        remove(Wrappers.<CustomerOriginalPaperEntity>lambdaQuery()
                .eq(CustomerOriginalPaperEntity::getCustomerId, customerId)
        );
    }
}