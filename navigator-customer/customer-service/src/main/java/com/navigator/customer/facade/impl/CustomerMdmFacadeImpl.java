package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.app.mdm.CustomerMdmAppService;
import com.navigator.customer.facade.CustomerMdmFacade;
import com.navigator.customer.pojo.dto.mdm.MDMObjectDataDTO;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/11
 */
@RestController
public class CustomerMdmFacadeImpl implements CustomerMdmFacade {

    @Resource
    private CustomerMdmAppService customerMdmAppService;

    @Override
    public Result saveOrUpdateMDMCustomer(MDMObjectDataDTO mdmObjectDataDTO) {
        return Result.success(customerMdmAppService.saveOrUpdateMDMCustomer(mdmObjectDataDTO));
    }
}
