package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.facade.SupplierFacade;
import com.navigator.customer.pojo.dto.FactoryConfigDTO;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.FactoryConfigEntity;
import com.navigator.customer.pojo.entity.SupplierEntity;
import com.navigator.customer.service.IFactoryConfigService;
import com.navigator.customer.service.ISupplierService;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/1 11:36
 */
@RestController
public class SupplierFacadeImpl implements SupplierFacade {
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IFactoryConfigService factoryConfigService;

    @Override
    public SupplierDTO getSupplierById(Integer id) {
        return supplierService.querySupplierById(id);
    }

    @Override
    public List<SupplierEntity> getSupplierListByType(Integer type) {
        return supplierService.getSupplierListByType(type);
    }

    @Override
    public Result querySupplierList() {
        return supplierService.querySupplierList();
    }

    @Override
    public Result querySupplierFactory(Integer parentId) {
        return supplierService.querySupplierFactory(parentId);
    }

    @Override
    public List<FactoryConfigDTO> factoryConfigBySuppId(@RequestParam(value = "supplierId") Integer supplierId) {
        List<FactoryConfigEntity> factoryConfigEntities = factoryConfigService.factoryConfigBySuppId(supplierId);
        List<FactoryConfigDTO> factoryConfigDTOS = BeanConvertUtils.convert2List(FactoryConfigDTO.class, factoryConfigEntities);
        return factoryConfigDTOS;
    }

    @Override
    public FactoryConfigDTO getFactoryConfigById(Integer id) {
        return factoryConfigService.getFactoryConfigById(id);
    }
}
