package com.navigator.customer.service.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/8
 */
@Component
public class CategoryDisposeMap {

    @Resource
    private CategoryFacade categoryFacade;

    public Map<Integer, List<Integer>> getCategoryMap(List<Integer> category2List, List<Integer> category3List) {

        Map<Integer, List<Integer>> map = new HashMap<>();

        for (Integer category2 : category2List) {
            CategoryQO categoryQO = new CategoryQO();
            List<CategoryEntity> categoryEntityList = categoryFacade.queryCategoryList(categoryQO.setParentSerialNo(category2));
            List<Integer> category3s = new ArrayList<>();
            for (CategoryEntity categoryEntity : categoryEntityList) {
                if (CollectionUtil.isNotEmpty(category3List) && category3List.contains(categoryEntity.getSerialNo())) {
                    category3s.add(categoryEntity.getSerialNo());
                }
            }
            map.put(category2, category3s);
        }
        return map;
    }

    public String customerCategoryConfig(List<CategoryEntity> categoryEntityList, List<String> categoryList) {

        if (!CollectionUtils.isEmpty(categoryEntityList) && !CollectionUtils.isEmpty(categoryList)) {

            StringBuilder category1Id = new StringBuilder()
                    .append(",");
            for (String category1 : categoryList) {
                Optional<CategoryEntity> matchingCategory = categoryEntityList.stream()
                        .filter(categoryEntity -> category1.equals(categoryEntity.getName()))
                        .findAny();

                if (matchingCategory.isPresent()) {
                    CategoryEntity categoryEntity = matchingCategory.get();
                    category1Id
                            .append(categoryEntity.getId())
                            .append(",")
                    ;
                }
            }
            return category1Id.toString();
        }
        return null;
    }
}
