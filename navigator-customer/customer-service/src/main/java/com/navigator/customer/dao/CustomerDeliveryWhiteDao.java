package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerDeliveryWhiteMapper;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@Dao
public class CustomerDeliveryWhiteDao extends BaseDaoImpl<CustomerDeliveryWhiteMapper, CustomerDeliveryWhiteEntity> {


    public List<CustomerDeliveryWhiteEntity> queryCustomerDeliveryWhite(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        return this.list(
                Wrappers.<CustomerDeliveryWhiteEntity>lambdaQuery()
                        .eq(null != customerDeliveryWhiteDTO.getCustomerId(), CustomerDeliveryWhiteEntity::getCustomerId, customerDeliveryWhiteDTO.getCustomerId())
                        .in(!CollectionUtils.isEmpty(customerDeliveryWhiteDTO.getCustomerIdList()), CustomerDeliveryWhiteEntity::getCustomerId, customerDeliveryWhiteDTO.getCustomerIdList())
                        .like(StringUtils.isNotEmpty(customerDeliveryWhiteDTO.getCategory1()), CustomerDeliveryWhiteEntity::getCategory1, "," + customerDeliveryWhiteDTO.getCategory1() + ",")
                        .like(StringUtils.isNotEmpty(customerDeliveryWhiteDTO.getCategory2()), CustomerDeliveryWhiteEntity::getCategory2, "," + customerDeliveryWhiteDTO.getCategory2() + ",")
                        .like(StringUtils.isNotEmpty(customerDeliveryWhiteDTO.getCategory3()), CustomerDeliveryWhiteEntity::getCategory3, "," + customerDeliveryWhiteDTO.getCategory3() + ",")
                        .eq(null != customerDeliveryWhiteDTO.getStatus(), CustomerDeliveryWhiteEntity::getStatus, customerDeliveryWhiteDTO.getStatus())
                        .orderByDesc(CustomerDeliveryWhiteEntity::getCreatedAt)
        );
    }

}
