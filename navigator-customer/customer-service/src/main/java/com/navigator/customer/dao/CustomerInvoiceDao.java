package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerInvoiceMapper;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;

import java.sql.Wrapper;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
@Dao
public class CustomerInvoiceDao extends BaseDaoImpl<CustomerInvoiceMapper, CustomerInvoiceEntity> {

    public List<CustomerInvoiceEntity> queryCustomerInvoiceList(CustomerInvoiceDTO customerInvoiceDTO) {
        return this.list(Wrappers.<CustomerInvoiceEntity>lambdaQuery()
                .eq(null != customerInvoiceDTO.getCustomerId(), CustomerInvoiceEntity::getCustomerId, customerInvoiceDTO.getCustomerId())
                .eq(null != customerInvoiceDTO.getCompanyId(), CustomerInvoiceEntity::getCompanyId, customerInvoiceDTO.getCompanyId())
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory1()), CustomerInvoiceEntity::getCategory1, "," + customerInvoiceDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory2()), CustomerInvoiceEntity::getCategory2, "," + customerInvoiceDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory3()), CustomerInvoiceEntity::getCategory3, "," + customerInvoiceDTO.getCategory3() + ",")
                .eq(CustomerInvoiceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CustomerInvoiceEntity::getUpdatedAt)
        );
    }

    public List<CustomerInvoiceEntity> queryCustomerInvoiceListByCustomerId(CustomerInvoiceDTO customerInvoiceDTO) {
        return this.list(Wrappers.<CustomerInvoiceEntity>lambdaQuery()
                .eq(null != customerInvoiceDTO.getCustomerId(), CustomerInvoiceEntity::getCustomerId, customerInvoiceDTO.getCustomerId())
                .eq(null != customerInvoiceDTO.getCompanyId(), CustomerInvoiceEntity::getCompanyId, customerInvoiceDTO.getCompanyId())
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory1()), CustomerInvoiceEntity::getCategory1, "," + customerInvoiceDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory2()), CustomerInvoiceEntity::getCategory2, "," + customerInvoiceDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerInvoiceDTO.getCategory3()), CustomerInvoiceEntity::getCategory3, "," + customerInvoiceDTO.getCategory3() + ",")
                .eq(null != customerInvoiceDTO.getInvoiceId(), CustomerInvoiceEntity::getInvoiceId, customerInvoiceDTO.getInvoiceId())
                .eq(null != customerInvoiceDTO.getInvoiceName(), CustomerInvoiceEntity::getInvoiceName, customerInvoiceDTO.getInvoiceName())
                .ne(null != customerInvoiceDTO.getId(), CustomerInvoiceEntity::getId, customerInvoiceDTO.getId())
                .orderByDesc(CustomerInvoiceEntity::getUpdatedAt)
        );
    }

    public List<CustomerInvoiceEntity> queryCustomerInvoiceListByCategory(Integer categoryId) {
        return this.list(Wrappers.<CustomerInvoiceEntity>lambdaQuery()
                .eq(CustomerInvoiceEntity::getCategory3, categoryId)
        );
    }
}
