package com.navigator.customer.app.customer;

import com.navigator.customer.pojo.bo.CustomerBankBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.service.impl.CustomerGradeScoreServiceImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/31
 */
public interface CustomerAppService {

    /**
     * 模板属性编辑
     *
     * @param customerProtocolDTO
     * @return
     */
    CustomerProtocolEntity saveOrUpdateCustomerProtocolFactory(CustomerProtocolDTO customerProtocolDTO);

    /**
     * 通知人编辑
     *
     * @return
     */
    boolean saveOrUpdateContactEntity(ContactDTO contactDTO);

    /**
     * 正本编辑
     *
     * @return
     */
    CustomerOriginalPaperEntity saveOrUpdateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO);

    /**
     * 预约保证金编辑
     *
     * @return
     */
    CustomerDepositRateEntity saveOrUpdateCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO);

    /**
     * 客户白名单编辑
     *
     * @return
     */
    CustomerDetailEntity saveOrUpdateCustomerWhiteList(CustomerDetailDTO customerWhiteListDTO);

    /**
     * 账户信息编辑
     *
     * @return
     */
    boolean saveOrUpdateCustomerBank(CustomerBankBO customerBankBO);

    /**
     * 发票类型编辑
     *
     * @return
     */
    CustomerInvoiceEntity saveOrUpdateCustomerInvoiceType(CustomerInvoiceEntity customerInvoiceEntity);

    /**
     * 赊销预付编辑
     *
     * @return
     */
    CustomerCreditPaymentEntity saveOrCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO);


    CustomerGradeScoreEntity saveOrUpdateCustomerGradeScore(CustomerGradeScoreEntity customerGradeScoreEntity);

    /**
     * 新增编辑客户提货白名单
     *
     * @param customerDeliveryWhiteEntity
     * @return
     */
    boolean saveOrUpdateCustomerDeliveryWhite(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity);
}
