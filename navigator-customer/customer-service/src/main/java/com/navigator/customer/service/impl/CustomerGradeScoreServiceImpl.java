package com.navigator.customer.service.impl;

import com.navigator.common.dto.Result;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.dao.CustomerGradeScoreDao;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.dto.file.CustomerGradeExcelDTO;
import com.navigator.customer.pojo.dto.file.CustomerProtocolExcelDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.customer.service.CustomerGradeScoreService;
import com.navigator.customer.service.ICustomerService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
@Service
public class CustomerGradeScoreServiceImpl implements CustomerGradeScoreService {

    @Resource
    private CustomerGradeScoreDao customerGradeScoreDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;
    @Resource
    private ICustomerService customerService;

    @Override
    public List<CustomerGradeScoreEntity> queryCustomerGradeScoreList(CustomerGradeScoreDTO customerGradeScoreDTO) {
        List<CustomerGradeScoreEntity> customerGradeScoreList = customerGradeScoreDao.queryCustomerGradeScoreList(customerGradeScoreDTO);

        customerGradeScoreList.forEach(customerGradeScoreEntity -> {

            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(customerGradeScoreEntity.getCategory2().split(","))
                    .map(String::trim)
                    .filter(category2 -> !category2.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if(null != categoryEntity){
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }

            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = Arrays.stream(customerGradeScoreEntity.getCategory3().split(","))
                    .map(String::trim)
                    .filter(category3 -> !category3.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category3 : category3List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                if(null != categoryEntity){
                    category3Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category3.equals(category3List.get(category3List.size() - 1))) {
                        category3Name.append(",");
                    }
                }
            }
            customerGradeScoreEntity.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, category3List))
                    .setCategory2Name(category2Name.toString())
                    .setCategory3Name(category3Name.toString())
                    .setUpdatedByName(customerGradeScoreEntity.getUpdatedBy())
                    .setCategory1(customerGradeScoreEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerGradeScoreEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerGradeScoreEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;

        });

        return customerGradeScoreList;
    }

    @Override
    public boolean saveOrUpdateCustomerGradeScore(CustomerGradeScoreEntity customerGradeScoreEntity) {
        customerGradeScoreDao.saveOrUpdate(customerGradeScoreEntity);
        return true;
    }

    @Override
    public CustomerGradeScoreEntity queryCustomerGradeScoreById(Integer id) {
        return customerGradeScoreDao.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importCustomerGrade(MultipartFile file) {

        List<CustomerGradeExcelDTO> customerGradeExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerGradeExcelDTO.class);

        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);

        for (CustomerGradeExcelDTO customerGradeExcelDTO : customerGradeExcelDTOS) {

            CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(customerGradeExcelDTO.getCustomerCode());
            //根据客户编码查询客户是否存在
            if (null == customerEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(customerGradeExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerGradeExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());


            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);

            String category3 = null;

            if(StringUtil.isNotEmpty(customerGradeExcelDTO.getCategory3())){
                List<String> category3List = Arrays.stream(customerGradeExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());
                category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);
            }else {
                List<Integer> category2s = Arrays.stream(category2.split(","))
                        .map(String::trim)
                        .filter(categoryId3 -> !categoryId3.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                List<CategoryEntity> categoryId3Entities = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(category2s.get(0)));

                StringBuilder categoryId3 = new StringBuilder()
                        .append(",");
                for (CategoryEntity category3Entity : categoryId3Entities) {
                    categoryId3
                            .append(category3Entity.getId())
                            .append(",")
                    ;
                }
                category3 = categoryId3.toString();

            }
            Integer id = StringUtils.isNotBlank(customerGradeExcelDTO.getId()) ? Integer.parseInt(customerGradeExcelDTO.getId()) : null;

            CustomerGradeScoreEntity customerGradeScoreEntity = new CustomerGradeScoreEntity();
            customerGradeScoreEntity
                    .setId(id)
                    .setCustomerId(customerEntity.getId())
                    .setGradeScore(customerGradeExcelDTO.getGradeScore())
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
                    .setCreatedBy("1")
                    .setCreatedAt(new Date())
                    .setUpdatedBy("1")
                    .setUpdatedAt(new Date())
            ;

            customerGradeScoreDao.saveOrUpdate(customerGradeScoreEntity);
        }

        return null;
    }
}
