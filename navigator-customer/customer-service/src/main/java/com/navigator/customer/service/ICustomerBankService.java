package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.vo.CustomerBankVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
public interface ICustomerBankService {

    List<CustomerBankEntity> queryCustomerBankByCustomerId(Integer customerId, Integer referType, Integer useType);


    /**
     * 根据客户id查询银行卡信息
     *
     * @param customerBankDTO
     * @return
     */
    List<CustomerBankVO> queryBankByCustomerId(CustomerBankDTO customerBankDTO);


    /**
     * 查询工厂品类下的客户账户信息
     *
     * @param customerBankFactoryDTO
     * @return
     */
    List<CustomerBankVO> queryCustomerBankFactory(CustomerBankFactoryDTO customerBankFactoryDTO);


    /**
     * 根据银行卡账号查询账户信息
     *
     * @param bankAccountNo
     * @return
     */
    CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo);

    /**
     * 根据银行卡账号查询账户信息
     * @param bankAccountNo
     * @param categoryId
     * @return
     */
    CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo,Integer categoryId);

    /**
     * 根据id 删除账户信息
     *
     * @param id
     * @return
     */
    boolean deleteCustomerBankById(Integer id);

    /**
     * 修改账户信息
     *
     * @param customerBankDTO
     * @return
     */
    boolean updateCustomerBank(CustomerBankDTO customerBankDTO);


    /**
     * 添加账户信息
     *
     * @param customerBankDTO
     * @return
     */
    boolean saveCustomerBank(CustomerBankDTO customerBankDTO);


    /**
     * 保存或者更新账户信息
     *
     * @param customerBankEntity
     * @return
     */
    boolean saveOrUpdateCustomerBank(CustomerBankEntity customerBankEntity);


    /**
     * 提交保存账户信息
     *
     * @param customerBankDTO
     * @return
     */
    boolean redactCustomerBank(List<CustomerBankDTO> customerBankDTO);


    /**
     * 根据客户银行id 查询出银行信息
     *
     * @param id
     * @return
     */
    CustomerBankEntity queryCustomerBankById(Integer id);


    Result leadCustomerBank(MultipartFile file);
}
