package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerDepositRateMapper;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.vo.CustomerDepositRateVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/14 11:39
 */
@Dao
public class CustomerDepositRateDao extends BaseDaoImpl<CustomerDepositRateMapper, CustomerDepositRateEntity> {


    /**
     * 根据客户id 履约保证金比例
     *
     * @param customerDepositRateBO
     * @return
     */
    public List<CustomerDepositRateEntity> getCustomerDepositRateByCustomerId(CustomerDepositRateBO customerDepositRateBO) {

        Integer isSales = null;
        Integer isProcurement = null;
        if (null != customerDepositRateBO.getSalesType() && ContractSalesTypeEnum.PURCHASE.getValue() == customerDepositRateBO.getSalesType()) {

            isProcurement = GeneralEnum.YES.getValue();
        } else if (null != customerDepositRateBO.getSalesType() && ContractSalesTypeEnum.SALES.getValue() == customerDepositRateBO.getSalesType()) {
            isSales = GeneralEnum.YES.getValue();
        }

        return this.baseMapper.selectList(Wrappers.<CustomerDepositRateEntity>lambdaQuery()
                .eq(null != customerDepositRateBO.getCustomerId(), CustomerDepositRateEntity::getCustomerId, customerDepositRateBO.getCustomerId())
//                .eq(null != customerDepositRateBO.getCategoryId(), CustomerDepositRateEntity::getCategoryId, customerDepositRateBO.getCategoryId())
                .like(StringUtils.isNotEmpty(customerDepositRateBO.getCategory1()), CustomerDepositRateEntity::getCategory1, "," + customerDepositRateBO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerDepositRateBO.getCategory2()), CustomerDepositRateEntity::getCategory2, "," + customerDepositRateBO.getCategory2() + ",")
                .eq(null != customerDepositRateBO.getStatus(), CustomerDepositRateEntity::getStatus, customerDepositRateBO.getStatus())
                .eq(null != customerDepositRateBO.getContractType(), CustomerDepositRateEntity::getContractType, customerDepositRateBO.getContractType())
                .eq(null != isSales, CustomerDepositRateEntity::getIsSales, isSales)
                .eq(null != isProcurement, CustomerDepositRateEntity::getIsProcurement, isProcurement)
                .eq(StringUtils.isNotEmpty(customerDepositRateBO.getBuCode()), CustomerDepositRateEntity::getBuCode, customerDepositRateBO.getBuCode())
                .eq(CustomerDepositRateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(CustomerDepositRateEntity::getContractType)
                .orderByDesc(CustomerDepositRateEntity::getUpdatedAt));
    }

    /**
     * 新增TT查询客户履约保证金
     *
     * @param customerDepositRateBO
     * @return
     */
    public List<CustomerDepositRateEntity> getTTDepositRateByCustomerId(CustomerDepositRateBO customerDepositRateBO) {

        Integer isSales = null;
        Integer isProcurement = null;
        if (null != customerDepositRateBO.getSalesType() && ContractSalesTypeEnum.PURCHASE.getValue() == customerDepositRateBO.getSalesType()) {

            isProcurement = GeneralEnum.YES.getValue();
        } else if (null != customerDepositRateBO.getSalesType() && ContractSalesTypeEnum.SALES.getValue() == customerDepositRateBO.getSalesType()) {
            isSales = GeneralEnum.YES.getValue();
        }

        return this.baseMapper.selectList(Wrappers.<CustomerDepositRateEntity>lambdaQuery()
                .eq(null != customerDepositRateBO.getCustomerId(), CustomerDepositRateEntity::getCustomerId, customerDepositRateBO.getCustomerId())
//                .eq(null != customerDepositRateBO.getCategoryId(), CustomerDepositRateEntity::getCategoryId, customerDepositRateBO.getCategoryId())
                .eq(null != customerDepositRateBO.getStatus(), CustomerDepositRateEntity::getStatus, customerDepositRateBO.getStatus())
                .eq(null != customerDepositRateBO.getContractType(), CustomerDepositRateEntity::getContractType, customerDepositRateBO.getContractType())
                .eq(null != isSales, CustomerDepositRateEntity::getIsSales, isSales)
                .eq(null != isProcurement, CustomerDepositRateEntity::getIsProcurement, isProcurement)
                .like(null != customerDepositRateBO.getCompanyId(), CustomerDepositRateEntity::getCompanyId, customerDepositRateBO.getCompanyId())
                .like(null != customerDepositRateBO.getCategory2(), CustomerDepositRateEntity::getCategory2, "," + customerDepositRateBO.getCategory2() + ",")
                .eq(null != customerDepositRateBO.getBuCode(), CustomerDepositRateEntity::getBuCode, customerDepositRateBO.getBuCode())
                .eq(CustomerDepositRateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(CustomerDepositRateEntity::getDepositRate, CustomerDepositRateEntity::getPricingDepositRate));
    }


    public List<CustomerDepositRateEntity> queryCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {

        return this.baseMapper.selectList(Wrappers.<CustomerDepositRateEntity>lambdaQuery()
                .eq(null != customerDepositRateDTO.getCustomerId(), CustomerDepositRateEntity::getCustomerId, customerDepositRateDTO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerDepositRateDTO.getCategory1()), CustomerDepositRateEntity::getCategory1, "," + customerDepositRateDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerDepositRateDTO.getCategory2()), CustomerDepositRateEntity::getCategory2, "," + customerDepositRateDTO.getCategory2() + ",")
                .eq(CustomerDepositRateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CustomerDepositRateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != customerDepositRateDTO.getContractType(), CustomerDepositRateEntity::getContractType, customerDepositRateDTO.getContractType())
                .eq(StringUtils.isNotEmpty(customerDepositRateDTO.getBuCode()), CustomerDepositRateEntity::getBuCode, customerDepositRateDTO.getBuCode())
                .eq(StringUtils.isNotEmpty(customerDepositRateDTO.getCompanyId()), CustomerDepositRateEntity::getCompanyId, customerDepositRateDTO.getCompanyId())
                .eq(null != customerDepositRateDTO.getIsSales(), CustomerDepositRateEntity::getIsSales, customerDepositRateDTO.getIsSales())
                .eq(null != customerDepositRateDTO.getIsProcurement(), CustomerDepositRateEntity::getIsProcurement, customerDepositRateDTO.getIsProcurement())
                .eq(null != customerDepositRateDTO.getDepositRate(), CustomerDepositRateEntity::getDepositRate, customerDepositRateDTO.getDepositRate())
                .eq(null != customerDepositRateDTO.getPricingDepositRate(), CustomerDepositRateEntity::getPricingDepositRate, customerDepositRateDTO.getPricingDepositRate())
                .eq(null != customerDepositRateDTO.getInvoicePaymentRate(), CustomerDepositRateEntity::getInvoicePaymentRate, customerDepositRateDTO.getInvoicePaymentRate())
                .eq(null != customerDepositRateDTO.getBuCode(), CustomerDepositRateEntity::getBuCode, customerDepositRateDTO.getBuCode())
        );
    }
}
