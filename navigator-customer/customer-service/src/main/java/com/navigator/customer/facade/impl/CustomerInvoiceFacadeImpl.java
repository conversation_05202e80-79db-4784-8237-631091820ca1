package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.service.CustomerInvoiceService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */

@RestController
public class CustomerInvoiceFacadeImpl implements CustomerInvoiceFacade {

    @Resource
    private CustomerInvoiceService customerInvoiceService;

    @Override
    public List<CustomerInvoiceEntity> queryCustomerInvoiceList(CustomerInvoiceDTO customerInvoiceDTO){
        return customerInvoiceService.queryCustomerInvoiceList(customerInvoiceDTO);
    }

    @Override
    public void updateCustomerInvoiceId() {
        customerInvoiceService.updateCustomerInvoiceId();
    }

    @Override
    public Result importCustomerInvoice(MultipartFile file) {
        return customerInvoiceService.importCustomerInvoice(file);
    }
}
