package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
public interface CustomerGradeScoreService {

    List<CustomerGradeScoreEntity> queryCustomerGradeScoreList(CustomerGradeScoreDTO customerGradeScoreDTO);

    boolean saveOrUpdateCustomerGradeScore(CustomerGradeScoreEntity customerGradeScoreEntity);

    CustomerGradeScoreEntity queryCustomerGradeScoreById(Integer id);

    /**
     * 客户评级导入
     *
     * @param file
     * @return
     */
    Result importCustomerGrade(MultipartFile file);

}
