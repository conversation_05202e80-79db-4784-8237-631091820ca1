package com.navigator.customer.dao;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.mapper.ContactMapper;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.enums.ContactTypeEnum;
import com.navigator.customer.pojo.enums.ReferTypeEnum;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 客户联系人表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Dao
public class ContactDao extends BaseDaoImpl<ContactMapper, ContactEntity> {

    @Resource
    private ContactMapper mapper;

    /**
     * 查询联系人
     *
     * @param referId
     * @param referType
     * @param categoryId
     * @return
     */
    public List<ContactEntity> queryContactEntity(Integer referId, Integer referType, Integer categoryId, Integer supplierId) {
        List<ContactEntity> contacts = mapper.selectList(Wrappers.<ContactEntity>lambdaQuery()
                .eq(referId != null, ContactEntity::getReferId, referId)
                .in(null != referType, ContactEntity::getReferType, Arrays.asList(referType, ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue()))
                .eq(categoryId != null, ContactEntity::getCategoryId, categoryId)
                .eq(ContactEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(supplierId != null, ContactEntity::getSupplierId, supplierId)
                .orderByAsc(ContactEntity::getId));
        return !contacts.isEmpty() ? contacts : new ArrayList<>();
    }

    public List<ContactEntity> queryContactByConditionList(ContactDTO contactDTO) {
        List<ContactEntity> contacts = mapper.selectList(Wrappers.<ContactEntity>lambdaQuery()
                //.eq(null != contactDTO.getReferId(), ContactEntity::getReferId, contactDTO.getReferId())
                .eq(null != contactDTO.getCustomerId(), ContactEntity::getReferId, contactDTO.getCustomerId())
                .eq(StringUtils.isNotEmpty(contactDTO.getCategory1()), ContactEntity::getCategory1, "," + contactDTO.getCategory1() + ",")
                .eq(StringUtils.isNotEmpty(contactDTO.getCategory2()), ContactEntity::getCategory2, "," + contactDTO.getCategory2() + ",")
                .in(null != contactDTO.getReferType(), ContactEntity::getReferType, Arrays.asList(contactDTO.getReferType(), ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue()))
                .eq(ContactEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(ContactEntity::getId));
        return !contacts.isEmpty() ? contacts : new ArrayList<>();
    }

    /**
     * 查询LDC的联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    public List<ContactEntity> queryLDCContactEntity(Integer referId, Integer referType, Integer categoryId) {
        List<ContactEntity> contacts = mapper.selectList(Wrappers.<ContactEntity>lambdaQuery()
                .eq(referId != null, ContactEntity::getReferId, referId)
                .eq(ContactEntity::getReferType, referType)
                .eq(categoryId != null, ContactEntity::getCategoryId, categoryId));
        return contacts;
    }

    /**
     * 添加联系人信息
     *
     * @param contactEntity
     * @return
     */
    public Integer vaseContactEntity(ContactEntity contactEntity) {
        return mapper.insert(contactEntity);
    }

    /**
     * 修改联系人信息
     *
     * @param contactEntity
     * @return
     */
    public Integer updateContactEntity(ContactEntity contactEntity) {
        return mapper.updateById(contactEntity);
    }

    /**
     * 根据id查询联系人信息
     *
     * @param id
     * @return
     */
    public ContactEntity getContactEntityById(Integer id) {
        return mapper.selectById(id);
    }

    /**
     * 查询客户签章人信息
     *
     * @param customerId
     * @return
     */
    public ContactDTO getCustomerByCustomerIdYqq(Integer customerId) {
        ContactEntity contactEntity = this.getBaseMapper().selectOne(Wrappers.<ContactEntity>lambdaQuery()
                .eq(ContactEntity::getReferId, customerId)
                .eq(ContactEntity::getReferType, ReferTypeEnum.CUSTOMER.getValue())
                .eq(ContactEntity::getContactType, ContactTypeEnum.SIGNATURE_CONTACT.getValue()));
        return BeanConvertUtils.convert(ContactDTO.class, contactEntity);
    }

    /**
     * 根据客户id查询客户联系人
     *
     * @param customerId
     * @return
     */
    public List<ContactEntity> getCustomerByCustomerId(Integer customerId, Integer referType, Integer categoryId) {
        return this.getBaseMapper().selectList(Wrappers.<ContactEntity>lambdaQuery()
                .in(null != referType, ContactEntity::getReferType, Arrays.asList(referType, ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue()))
                .eq(ContactEntity::getReferId, customerId)
                .eq(null != categoryId, ContactEntity::getCategoryId, categoryId)
                .eq(ContactEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

public List<ContactEntity> getContactByCustomerId(CustomerAllMessageDTO customerAllMessageDTO, Integer referType) {
        LambdaQueryWrapper<ContactEntity> queryWrapper = Wrappers.<ContactEntity>lambdaQuery()
                .eq(null != customerAllMessageDTO.getCustomerId(), ContactEntity::getReferId, customerAllMessageDTO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerAllMessageDTO.getCategory1()), ContactEntity::getCategory1, "," + customerAllMessageDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerAllMessageDTO.getCategory2()), ContactEntity::getCategory2, "," + customerAllMessageDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerAllMessageDTO.getCategory3()), ContactEntity::getCategory3, "," + customerAllMessageDTO.getCategory3() + ",")
                .in(ContactEntity::getReferType, Arrays.asList(referType, ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue()))
                .eq(ContactEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
       /* if (!CollectionUtils.isEmpty(customerAllMessageDTO.getCategory3List())) {
            queryWrapper.and(k -> {
                customerAllMessageDTO.getCategory3List().forEach(i -> {
                    k.or(v -> v
                            .eq(ContactEntity::getCategory3, i + ","))
                    ;
                });
            });
        } else {
            queryWrapper.like(StringUtils.isNotEmpty(customerAllMessageDTO.getCategory3()), ContactEntity::getCategory3, "," + customerAllMessageDTO.getCategory3() + ",");
        }*/
        return this.getBaseMapper().selectList(queryWrapper);
    }


}
