package com.navigator.customer.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.mapper.CustomerBankMapper;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.enums.BankUseTypeEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Dao
public class CustomerBankDao extends BaseDaoImpl<CustomerBankMapper, CustomerBankEntity> {

    /**
     * 根据客户id查询客户银行信息
     *
     * @param customerId
     * @return
     */
    public List<CustomerBankEntity> queryCustomerBankByCustomerId(Integer customerId, Integer referType, Integer useType) {
        return this.baseMapper.selectList(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getCustomerId, customerId)
                .in(null != useType, CustomerBankEntity::getUseType, Arrays.asList(useType, BankUseTypeEnum.COLLECTION_PAYMENT.getValue()))
                .eq(CustomerBankEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())

        );
    }

    public List<CustomerBankEntity> queryBankByConditionList(CustomerBankDTO customerBankDTO) {
        return this.baseMapper.selectList(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getCustomerId, customerBankDTO.getCustomerId())
                .like(StringUtil.isNotEmpty(customerBankDTO.getCategory1()), CustomerBankEntity::getCategory1, "," + customerBankDTO.getCategory1() + ",")
                .like(StringUtil.isNotEmpty(customerBankDTO.getCategory2()), CustomerBankEntity::getCategory2, "," + customerBankDTO.getCategory2() + ",")
                .like(StringUtil.isNotEmpty(customerBankDTO.getCompanyId()), CustomerBankEntity::getCompanyId, "," + customerBankDTO.getCompanyId() + ",")
                .in(null != customerBankDTO.getUseType(), CustomerBankEntity::getUseType, Arrays.asList(customerBankDTO.getUseType(), BankUseTypeEnum.COLLECTION_PAYMENT.getValue()))
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public List<CustomerBankEntity> queryBankByCustomerId(Integer customerId, Integer categoryId, Integer useType, String companyId) {
        return this.baseMapper.selectList(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getCustomerId, customerId)
                .eq(null != categoryId, CustomerBankEntity::getCategoryId, categoryId)
                .like(StringUtil.isNotEmpty(companyId), CustomerBankEntity::getCompanyId, companyId)
                .in(null != useType, CustomerBankEntity::getUseType, Arrays.asList(useType, BankUseTypeEnum.COLLECTION_PAYMENT.getValue()))
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public List<CustomerBankEntity> queryCustomerBankFactory(CustomerBankFactoryDTO customerBankFactoryDTO) {
        return this.baseMapper.selectList(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getCustomerId, customerBankFactoryDTO.getCustomerId())
                .eq(null != customerBankFactoryDTO.getCategoryId(), CustomerBankEntity::getCategoryId, customerBankFactoryDTO.getCategoryId())
                .like(StringUtil.isNotEmpty(customerBankFactoryDTO.getCompanyId()), CustomerBankEntity::getCompanyId, customerBankFactoryDTO.getCompanyId())
                .like(null != customerBankFactoryDTO.getCategory2(), CustomerBankEntity::getCategory2, "," + customerBankFactoryDTO.getCategory2() + ",")
                .in(null != customerBankFactoryDTO.getUseType(), CustomerBankEntity::getUseType, Arrays.asList(customerBankFactoryDTO.getUseType(), BankUseTypeEnum.COLLECTION_PAYMENT.getValue()))
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public boolean deleteCustomerBankByCustomerId(CustomerBankEntity customerBankEntity, Integer customerId) {
        return this.baseMapper.update(customerBankEntity, Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getCustomerId, customerId)
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())) == 0 ? false : true;
    }


    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo) {
        List<CustomerBankEntity> list = this.list(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getBankAccountNo, bankAccountNo));
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo, Integer categoryId) {
        return this.getOne(Wrappers.<CustomerBankEntity>lambdaQuery()
                .eq(CustomerBankEntity::getBankAccountNo, bankAccountNo)
                .eq(CustomerBankEntity::getCategoryId, categoryId)
                .eq(CustomerBankEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CustomerBankEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }


}
