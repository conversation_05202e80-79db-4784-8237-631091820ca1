package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
public interface CustomerInvoiceService {

    /**
     * 根据条件查询客户配置
     *
     * @param customerInvoiceDTO
     * @return
     */
    List<CustomerInvoiceEntity> queryCustomerInvoiceList(CustomerInvoiceDTO customerInvoiceDTO);

    boolean saveOrUpdateCustomerInvoice(CustomerInvoiceEntity customerInvoiceEntity);

    List<CustomerInvoiceEntity> queryCustomerInvoiceListByCustomerId(CustomerInvoiceDTO customerInvoiceDTO);

    void updateCustomerInvoiceId();

    CustomerInvoiceEntity queryCustomerInvoiceById(Integer id);

    /**
     * 发票配置导入
     *
     * @param file
     * @return
     */
    Result importCustomerInvoice(MultipartFile file);
}
