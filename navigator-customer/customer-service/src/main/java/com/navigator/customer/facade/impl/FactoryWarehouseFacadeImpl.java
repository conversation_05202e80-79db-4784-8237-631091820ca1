package com.navigator.customer.facade.impl;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.FactoryCreateDTO;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.customer.pojo.vo.FactoryVO;
import com.navigator.customer.service.IFactoryService;
import com.navigator.customer.service.IFactoryWarehouseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/28 10:43
 */
@RestController
public class FactoryWarehouseFacadeImpl implements FactoryWarehouseFacade {

    @Resource
    private IFactoryWarehouseService factoryWarehouseService;
    @Resource
    private IFactoryService iFactoryService;
    @Autowired
    private EmployFacade employFacade;

    @Override
    public FactoryEntity getFactoryInfoById(Integer factoryId) {
        return iFactoryService.getFactoryDetailById(factoryId);
    }

    @Override
    public FactoryEntity getFactoryByCode(String factoryCode) {
        return iFactoryService.getFactoryByCode(factoryCode);
    }

    @Override
    public List<FactoryEntity> getAllFactory(Integer status) {
        return iFactoryService.getAllFactory(status);
    }

    @Override
    public List<FactoryVO> queryFactoryList() {
        return iFactoryService.queryFactoryList();

    }

    @Override
    public FactoryEntity getFactoryDetailById(Integer factoryId) {
        return iFactoryService.getFactoryDetailById(factoryId);
    }

    @Override
    public FactoryWarehouseEntity getFactoryDetailByLkg(String lkgWareHouseCode) {
        return factoryWarehouseService.getFactoryDetailByLkg(lkgWareHouseCode);
    }

    @Override
    public Result saveOrUpdateFactory(FactoryCreateDTO factoryCreateDTO) {
        return Result.judge(iFactoryService.saveOrUpdateFactory(factoryCreateDTO));
    }

    @Override
    public Result updateStatus(Integer factoryId) {
        return Result.judge(iFactoryService.updateStatus(factoryId));
    }

    @Override
    public FactoryWarehouseEntity queryFactoryWarehouseById(Integer id) {
        return factoryWarehouseService.queryFactoryWarehouseById(id);
    }

    @Override
    public Result queryFactoryWarehouseByCode(String factoryCode) {
        return factoryWarehouseService.queryFactoryWarehouseByCode(factoryCode);
    }

    @Override
    public Result getFactoryWarehouseList(List<Integer> factoryId, String factoryCode, Integer goodsCategoryId, Integer status) {
        if (null == goodsCategoryId) {
            goodsCategoryId = GoodsCategoryEnum.OSM_MEAL.getValue();
        }
        return Result.success(factoryWarehouseService.getFactoryWarehouseList(factoryId, factoryCode, goodsCategoryId, status));
    }

    @Override
    public Result getFactoryWarehouseList(FactoryQueryDTO factoryQueryDTO) {
        if (StringUtils.isEmpty(factoryQueryDTO.getGoodsCategoryId())) {
            factoryQueryDTO.setGoodsCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        }
        return Result.success(factoryWarehouseService.getFactoryWarehouseList(factoryQueryDTO));
    }


    @Override
    public Result saveOrUpdateFactoryWarehouse(FactoryWarehouseEntity warehouseEntity) {
        return Result.judge(factoryWarehouseService.saveOrUpdateFactoryWarehouse(warehouseEntity));
    }

    @Override
    public Result invalidWarehouse(Integer warehouseId) {
        return Result.judge(factoryWarehouseService.invalidWarehouse(warehouseId));
    }

    @Override
    public Result importFactoryWarehouse(MultipartFile uploadFile) {
        return factoryWarehouseService.importFactoryWarehouse(uploadFile);
    }

    @Override
    public List<FactoryEntity> queryFactoryByCompanyId(Integer companyId) {
        return iFactoryService.queryFactoryByCompanyId(companyId);
    }

    @Override
    public List<FactoryWarehouseEntity> getWarehouseListById(List<Integer> warehouseIdList) {
        return factoryWarehouseService.getWarehouseListById(warehouseIdList);
    }

    @Override
    public Result updateFactoryById(FactoryEntity factoryEntity) {
         iFactoryService.updateFactoryById(factoryEntity);
        return Result.success();
    }

}
