package com.navigator.customer.service.impl;

import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.customer.dao.ContactFactoryDao;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import com.navigator.customer.service.IContactFactoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 18:01
 */
@Service
public class ContactFactoryServiceImpl implements IContactFactoryService {


    @Resource
    private ContactFactoryDao contactFactoryDao;

    @Override
    public List<ContactFactoryEntity> queryContactFactoryByContactId(Integer contactId) {
        return contactFactoryDao.queryContactFactoryByContactId(contactId);
    }


    @Override
    public boolean deleteContactFactoryByContactId(Integer contactId) {

        int num = 0;
        //查询联系人下的工厂
        List<ContactFactoryEntity> contactFactoryEntities = this.queryContactFactoryByContactId(contactId);

        if (!contactFactoryEntities.isEmpty()) {
            for (ContactFactoryEntity contactFactoryEntity : contactFactoryEntities) {
                //删除联系人工厂信息
                contactFactoryEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
                contactFactoryDao.updateById(contactFactoryEntity);

                num += 1;
            }
        }
        return num == 0 ? false : true;
    }

    @Override
    public List<ContactFactoryEntity> getContactFactoryByContactId(Integer contactId, Integer factoryId) {
        return contactFactoryDao.getContactFactoryByContactId(contactId, factoryId);
    }

    @Override
    public List<ContactFactoryEntity> getContactFactoryByCustomerId(Integer customerId, Integer factoryId) {
        return contactFactoryDao.getContactFactoryByCustomerId(customerId, factoryId);
    }

    @Override
    public boolean saveContactFactoryEntity(ContactFactoryEntity contactFactoryEntity) {
        return contactFactoryDao.saveContactFactoryEntity(contactFactoryEntity) > 0 ? true : false;

    }

    @Override
    public boolean updateContactFactoryEntity(ContactFactoryEntity contactFactoryEntity) {

        if (null == contactFactoryEntity) {
            return false;
        }
        return contactFactoryDao.updateById(contactFactoryEntity);
    }

    @Override
    public boolean deleteContactFactoryEntityByContactId(Integer contactId) {

        List<ContactFactoryEntity> contactFactoryEntities = this.queryContactFactoryByContactId(contactId);
        for (ContactFactoryEntity contactFactoryEntity : contactFactoryEntities) {
            contactFactoryEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
            this.updateContactFactoryEntity(contactFactoryEntity);
        }

        return true;
    }


    @Override
    public boolean saveOrUpdateContactFactoryEntity(List<ContactFactoryEntity> contactFactoryEntityList) {
        if (contactFactoryEntityList.isEmpty()) {
            return false;
        }

        contactFactoryDao.deleteByCondition(contactFactoryEntityList.get(0));
        for (ContactFactoryEntity contactFactoryEntity : contactFactoryEntityList) {

            contactFactoryDao.saveOrUpdate(contactFactoryEntity);
        }

        return true;
    }
}
