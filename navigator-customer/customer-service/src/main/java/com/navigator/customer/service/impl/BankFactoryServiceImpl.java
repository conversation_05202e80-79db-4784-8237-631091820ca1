package com.navigator.customer.service.impl;

import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.customer.dao.BankFactoryDao;
import com.navigator.customer.pojo.entity.BankFactoryEntity;
import com.navigator.customer.pojo.entity.ContactFactoryEntity;
import com.navigator.customer.service.BankFactoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 17:36
 */
@Service
public class BankFactoryServiceImpl implements BankFactoryService {


    @Resource
    private BankFactoryDao bankFactoryDao;

    @Override
    public List<BankFactoryEntity> queryBankFactoryByBankId(Integer bankId) {
        return bankFactoryDao.queryBankFactoryByBankId(bankId);
    }

    @Override
    public boolean updateBankFactory(BankFactoryEntity bankFactoryEntity) {
        return bankFactoryDao.updateById(bankFactoryEntity);
    }


    @Override
    public List<BankFactoryEntity> queryBankFactoryByBankIdFactoryId(Integer bankId, Integer factoryId) {
        return bankFactoryDao.queryBankFactoryByBankIdFactoryId(bankId, factoryId);
    }

    @Override
    public boolean saveOrUpdateBankFactory(List<BankFactoryEntity> bankFactoryEntity) {
        if(bankFactoryEntity.isEmpty()){
            return false;
        }

        bankFactoryDao.deleteByCondition(bankFactoryEntity.get(0));
        for (BankFactoryEntity contactFactoryEntity : bankFactoryEntity){
            bankFactoryDao.saveOrUpdate(contactFactoryEntity);
        }
        return true;
    }

    @Override
    public boolean saveBankFactory(BankFactoryEntity bankFactoryEntity) {
        return bankFactoryDao.save(bankFactoryEntity);
    }

    @Override
    public boolean deleteBankFactoryByBankId(Integer bankId) {
        List<BankFactoryEntity> bankFactoryEntities = this.queryBankFactoryByBankId(bankId);

        for (BankFactoryEntity bankFactoryEntity : bankFactoryEntities) {
            bankFactoryEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
            bankFactoryDao.updateById(bankFactoryEntity);
        }
        return true;
    }


}
