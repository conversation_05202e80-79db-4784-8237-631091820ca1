package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface CustomerProtocolService {
    Result queryCustomerProtocolList(CustomerProtocolDTO customerProtocolDTO);

    List<CustomerProtocolEntity> queryCustomerProtocolEntityList(CustomerProtocolDTO customerProtocolDTO);

    Result saveCustomerProtocol(CustomerProtocolDTO customerProtocolDTO);

    CustomerProtocolEntity queryCustomerProtocolEntity(CustomerProtocolDTO customerProtocolDTO);

    Result updateCustomerProtocol(CustomerProtocolDTO customerProtocolDTO);

    Result queryCustomerProtocolSign(CustomerProtocolDTO customerProtocolDTO);

    /**
     * 模板导入
     *
     * @param file
     * @return
     */
    Result importCustomerProtocol(MultipartFile file);
}
