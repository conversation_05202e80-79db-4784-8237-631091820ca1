package com.navigator.customer.service;

import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.SupplierEntity;

import java.util.List;

/**
 * <p>
 * 供应商（油厂）表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
public interface ISupplierService {

    /**
     * 根据ID查询供应商信息
     *
     * @param id
     * @return
     */
    SupplierDTO querySupplierById(Integer id);

    /**
     * 获取油厂/供应商
     *
     * @param isFactory 0供应商 1油厂
     * @return
     */
    List<SupplierEntity> getSupplierListByType(Integer isFactory);

    /**
     * 查询供应商信息
     *
     * @return
     */
    Result querySupplierList();

    /**
     * 根据父id查询油厂
     *
     * @param parentId
     * @return
     */
    Result querySupplierFactory(Integer parentId);

}
