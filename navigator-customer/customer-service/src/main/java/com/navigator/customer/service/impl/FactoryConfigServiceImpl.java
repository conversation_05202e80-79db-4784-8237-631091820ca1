package com.navigator.customer.service.impl;

import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.dao.FactoryConfigDao;
import com.navigator.customer.pojo.dto.FactoryConfigDTO;
import com.navigator.customer.pojo.entity.FactoryConfigEntity;
import com.navigator.customer.service.IFactoryConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Service
public class FactoryConfigServiceImpl implements IFactoryConfigService {

    @Resource
    private FactoryConfigDao factoryConfigDao;

    @Override
    public List<FactoryConfigEntity> factoryConfigBySuppId(Integer customerId) {
        return factoryConfigDao.factoryConfigBySuppId(customerId);
    }

    @Override
    public FactoryConfigDTO getFactoryConfigById(Integer id) {
        FactoryConfigEntity factoryConfigEntity = factoryConfigDao.getBaseMapper().selectById(id);
        FactoryConfigDTO factoryConfigDTO = BeanConvertUtils.convert(FactoryConfigDTO.class, factoryConfigEntity);
        return factoryConfigDTO;
    }
}
