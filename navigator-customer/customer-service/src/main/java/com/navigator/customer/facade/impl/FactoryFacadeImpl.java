package com.navigator.customer.facade.impl;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.common.dto.Result;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.service.IFactoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@RestController
public class FactoryFacadeImpl implements FactoryFacade {
    @Autowired
    private SiteFacade siteFacade;
    @Autowired
    private IFactoryService factoryService;

    @Override
    public FactoryEntity getFactoryByCode(String factoryCode) {
        return factoryService.getFactoryByCode(factoryCode);
    }

    @Override
    public List<FactoryEntity> getAllFactoryList(Integer status) {
        return factoryService.getAllFactory(status);
    }

    @Override
    public List<FactoryEntity> getAllFactoryBySyncSystem(String syncSystem) {
        // Get all factory ids by sync system
        Result<List<SiteEntity>> result = siteFacade.getSiteListBySyncSystem(syncSystem);
        if (result.isSuccess()) {
            Set<Integer> factoryIds = result.getData().stream()
                    .map(SiteEntity::getFactoryId)
                    .collect(Collectors.toSet());

            return factoryIds.stream()
                    .map(factoryService::getFactoryById)
                    .collect(Collectors.toList());
        }
        return null;
    }
}
