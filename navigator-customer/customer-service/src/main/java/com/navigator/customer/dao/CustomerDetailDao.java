package com.navigator.customer.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.customer.mapper.CustomerDetailMapper;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/20 11:22
 */
@Dao
public class CustomerDetailDao extends BaseDaoImpl<CustomerDetailMapper, CustomerDetailEntity> {

    /**
     * 根据二级品类查询客户配置
     *
     * @param customerId 客户id
     * @param categoryId 二级品类id
     * @return
     */
    public CustomerDetailEntity queryCustomerDetailList(Integer customerId, Integer categoryId) {
        List<CustomerDetailEntity> customerDetailEntities = this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(customerId != null, CustomerDetailEntity::getCustomerId, customerId)
                .like(categoryId != null, CustomerDetailEntity::getCategory2, categoryId)
                .eq(CustomerDetailEntity::getIsDeleted, 0)
                .orderByDesc(CustomerDetailEntity::getUpdatedAt));
        return CollectionUtils.isNotEmpty(customerDetailEntities) ? customerDetailEntities.get(0) : null;
    }

    public List<CustomerDetailEntity> queryCustomerDetailListByCondition(CustomerDetailBO customerDetailBO) {

        return this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(null != customerDetailBO.getCustomerId(), CustomerDetailEntity::getCustomerId, customerDetailBO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerDetailBO.getCategory1()), CustomerDetailEntity::getCategory1, "," + customerDetailBO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerDetailBO.getCategory2()), CustomerDetailEntity::getCategory2, "," + customerDetailBO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerDetailBO.getCategory3()), CustomerDetailEntity::getCategory3, "," + customerDetailBO.getCategory3() + ",")
                .orderByDesc(CustomerDetailEntity::getUpdatedAt)
        );
    }

    public List<CustomerDetailEntity> customerProtocol() {
        return this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .gt(CustomerDetailEntity::getProtocolStartDate, new Date())
                .or(wrapper -> wrapper.lt(CustomerDetailEntity::getProtocolEndDate, new Date()))
        );
    }

    /**
     * 根据客户id 或品类查询客户配置
     *
     * @param customerId
     * @return
     */
    public List<CustomerDetailEntity> queryCustomerIdList(Integer customerId) {
        return this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(customerId != null, CustomerDetailEntity::getCustomerId, customerId));
    }

    public Integer saveCustomerDetail(CustomerDetailEntity customerDetailEntity) {
        return this.baseMapper.insert(customerDetailEntity);
    }


    /**
     * 修改客户配置信息
     *
     * @param customerDetailEntity
     * @return
     */
    public Integer updateCustomerDetail(CustomerDetailEntity customerDetailEntity) {
        return this.baseMapper.updateById(customerDetailEntity);
    }

    public List<CustomerDetailEntity> queryCustomerInvoiceList(Integer customerId, Integer categoryId) {
        return list(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(customerId != null, CustomerDetailEntity::getCustomerId, customerId)
                .eq(categoryId != null, CustomerDetailEntity::getCategoryId, categoryId));
    }


    public List<CustomerDetailEntity> queryCustomerDetailEntitiesList(Integer customerId, String category2, String category3) {
        return this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(customerId != null, CustomerDetailEntity::getCustomerId, customerId)
                .like(StringUtils.isNotEmpty(category2), CustomerDetailEntity::getCategory2, "," + category2 + ",")
                .like(StringUtils.isNotEmpty(category3), CustomerDetailEntity::getCategory2, "," + category3 + ",")
        );
    }

    public CustomerDetailEntity queryCustomerDetailEntity(Integer customerId, Integer category3) {
        List<CustomerDetailEntity> customerDetailEntities = this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(customerId != null, CustomerDetailEntity::getCustomerId, customerId)
                .like(category3 != null, CustomerDetailEntity::getCategory2, category3 + ",")
        );
        return CollectionUtils.isNotEmpty(customerDetailEntities) ? customerDetailEntities.get(0) : null;
    }

    public void deleteByCompanyId() {
        remove(Wrappers.<CustomerDetailEntity>lambdaUpdate().eq(CustomerDetailEntity::getCompanyId, 2));
    }

    public List<CustomerDetailEntity> queryAll() {
        return list(Wrappers.<CustomerDetailEntity>lambdaUpdate().eq(CustomerDetailEntity::getCompanyId, 1));
    }

    public List<CustomerDetailEntity> queryAllCustomerDetailList() {
        return list(Wrappers.<CustomerDetailEntity>lambdaQuery()
        );
    }


    public List<CustomerDetailEntity> queryCustomerDetailListByNotId(CustomerDetailDTO customerDetailDTO) {
        return this.baseMapper.selectList(Wrappers.<CustomerDetailEntity>lambdaQuery()
                .eq(null != customerDetailDTO.getCustomerId(), CustomerDetailEntity::getCustomerId, customerDetailDTO.getCustomerId())
                .like(StringUtils.isNotEmpty(customerDetailDTO.getCategory1()), CustomerDetailEntity::getCategory1, "," + customerDetailDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerDetailDTO.getCategory2()), CustomerDetailEntity::getCategory2, "," + customerDetailDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerDetailDTO.getCategory3()), CustomerDetailEntity::getCategory3, "," + customerDetailDTO.getCategory3() + ",")
                .ne(null != customerDetailDTO.getId(), CustomerDetailEntity::getId, customerDetailDTO.getId())
                .orderByDesc(CustomerDetailEntity::getUpdatedAt)
        );
    }
}
