package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerCreditPaymentFacade;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import com.navigator.customer.service.CustomerCreditPaymentService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/15 16:45
 */
@RestController
public class CustomerCreditPaymentFacadeImpl implements CustomerCreditPaymentFacade {

    @Resource
    private CustomerCreditPaymentService customerCreditPaymentService;

    @Override
    public Result customerCreditPaymentAllList(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return Result.success(customerCreditPaymentService.customerCreditPaymentAllList(customerCreditPaymentDTO));
    }

    @Override
    public Result updateCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return Result.success(customerCreditPaymentService.updateCustomerCreditPayment(customerCreditPaymentDTO));
    }

    /**
     * 添加客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @Override
    public Result addCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return Result.success(customerCreditPaymentService.addCustomerCreditPayment(customerCreditPaymentDTO));
    }

    /**
     * 导入客户赊销数据
     *
     * @param file
     * @return
     */
    @Override
    public Result leadCustomerCreditPayment( MultipartFile file){
        return customerCreditPaymentService.leadCustomerCreditPayment(file);
    }

}
