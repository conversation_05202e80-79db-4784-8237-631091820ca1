package com.navigator.customer.app.customer.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.app.customer.CustomerAppService;
import com.navigator.customer.dao.ContactDao;
import com.navigator.customer.dao.CustomerProtocolDao;
import com.navigator.customer.pojo.bo.CustomerBankBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.customer.service.*;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.facade.ContractFacade;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/31
 */
@Service
public class CustomerAppServiceImpl implements CustomerAppService {

    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private CustomerProtocolService customerProtocolService;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CustomerDetailUpdateRecordService customerDetailUpdateRecordService;
    @Resource
    private CustomerDeliveryWhiteService customerDeliveryWhiteService;
    @Autowired
    private CustomerProtocolDao customerProtocolDao;
    @Autowired
    private ContactDao contactDao;
    @Resource
    private IContactFactoryService contactFactoryService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private CustomerOriginalPaperService customerOriginalPaperService;
    @Resource
    private ICustomerDepositRateService customerDepositRateService;
    @Resource
    private ICustomerBankService customerBankService;
    @Resource
    private BankFactoryService bankFactoryService;
    @Resource
    private CustomerCreditPaymentService customerCreditPaymentService;
    @Resource
    private ContractFacade contractFacade;
    @Resource
    private ICustomerDetailService customerDetailService;
    @Resource
    private CustomerInvoiceService customerInvoiceService;
    @Resource
    private CustomerGradeScoreService customerGradeScoreService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private ICustomerService customerService;


    @Override
    public CustomerProtocolEntity saveOrUpdateCustomerProtocolFactory(CustomerProtocolDTO customerProtocolDTO) {

        List<CustomerProtocolEntity> customerProtocolList = customerProtocolDTO.getCustomerProtocolList();

        if (null == customerProtocolDTO.getCompanyId()
                || StringUtils.isEmpty(customerProtocolDTO.getCategory1())
                || StringUtils.isEmpty(customerProtocolDTO.getCategory2())
                || StringUtils.isEmpty(customerProtocolDTO.getCategory3())
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }

        CustomerProtocolEntity customerProtocolEntity = new CustomerProtocolEntity();
        if (null != customerProtocolDTO.getId() && CollectionUtils.isNotEmpty(customerProtocolList)) {
            customerProtocolEntity = customerProtocolList.get(0);
            customerProtocolDao.deleteByCondition(customerProtocolEntity);
        }


        if (null == customerProtocolDTO.getId()) {
            List<CustomerProtocolEntity> customerProtocolEntities = customerProtocolService.queryCustomerProtocolEntityList(customerProtocolDTO);
            if (!customerProtocolEntities.isEmpty()) {
                throw new BusinessException(ResultCodeEnum.CUSTOMER_PROPERTY_EXIST);
            }
        }

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        customerProtocolEntity = BeanConvertUtils.convert(CustomerProtocolEntity.class, customerProtocolDTO);

        String category2 = "";
        String category3 = "";

        if (null == customerProtocolDTO.getId()) {
            customerProtocolEntity
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name)
                    .setCategory1("," + customerProtocolDTO.getCategory1() + ",")
                    .setCategory2("," + customerProtocolDTO.getCategory2() + ",")
                    .setCategory3("," + customerProtocolDTO.getCategory3() + ",");
        } else {

            if (StringUtil.isNotEmpty(customerProtocolDTO.getCategory1())) {
                customerProtocolEntity.setCategory1("," + customerProtocolDTO.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(customerProtocolDTO.getCategory2())) {
                category2 = "," + customerProtocolDTO.getCategory2() + ",";
                customerProtocolEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerProtocolDTO.getCategory3())) {
                category3 = "," + customerProtocolDTO.getCategory3() + ",";
                customerProtocolEntity.setCategory3(category3);
            }

        }

        customerProtocolEntity.setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name);
        //添加客户配置模板
        customerProtocolDao.saveOrUpdate(customerProtocolEntity);
        CustomerEntity customerEntity = customerService.getCustomerById(customerProtocolDTO.getCustomerId());

        //记录操作日志
        try {
            //todo 多品类操作记录修改
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerProtocolDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(customerProtocolDTO))
                    .setReferName(customerEntity.getName())
                    .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_PROTOCOL)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);

            CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity = new CustomerDetailUpdateRecordEntity();
            //记录修改客户主数据人
            customerDetailUpdateRecordEntity
                    .setDetailCode(BusinessDetailCodeEnum.UPDATE_CONTRACT_TEMPLATE.getValue())
                    .setCustomerId(customerProtocolDTO.getCustomerId())
                    .setCategoryId(customerProtocolDTO.getCategoryId())
                    .setData(JSON.toJSONString(recordOperationDetail))
                    .setCreatedAt(new Date())
                    .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
            customerDetailUpdateRecordService.saveCustomerDetailUpdateRecord(customerDetailUpdateRecordEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return customerProtocolEntity;
    }

    @Override
    public boolean saveOrUpdateContactEntity(ContactDTO contactDTO) {


        ContactEntity contactEntity = contactDTO;
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        BeanUtils.copyProperties(contactDTO, contactEntity);

        String category2 = "";
        String category3 = "";


        if (null == contactEntity.getId()) {
            contactEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name)
                    .setCategory1("," + contactDTO.getCategory1() + ",")
                    .setCategory2("," + contactDTO.getCategory2() + ",")
                    .setCategory3("," + contactDTO.getCategory3() + ",")
            ;
        } else {
            if (StringUtil.isNotEmpty(contactDTO.getCategory1())) {
                contactDTO.setCategory1("," + contactDTO.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(contactDTO.getCategory2())) {
                category2 = "," + contactDTO.getCategory2() + ",";
                contactEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(contactDTO.getCategory3())) {
                category3 = "," + contactDTO.getCategory3() + ",";
                contactEntity.setCategory3(category3);
            }
        }

        contactEntity
                .setReferId(String.valueOf(contactDTO.getCustomerId()))
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;
        contactDao.saveOrUpdate(contactEntity);


        List<ContactFactoryEntity> contactFactoryEntities = contactDTO.getContactFactoryEntities();

        contactFactoryEntities.forEach(contactFactoryEntity -> {
            contactFactoryEntity
                    .setCustomerId(Integer.parseInt(contactEntity.getReferId()))
                    .setContactId(contactEntity.getId())
            ;
        });
        contactFactoryService.saveOrUpdateContactFactoryEntity(contactFactoryEntities);
        //记录操作日志
        CustomerEntity customerEntity = customerService.getCustomerById(contactDTO.getCustomerId());
        try {
            //todo 多品类操作记录修改
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(contactDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(contactDTO))
                    .setReferName(customerEntity.getName())
                    .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_TEMPLATE_CONTACT_FACTORY)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public CustomerOriginalPaperEntity saveOrUpdateCustomerOriginalPaper(CustomerOriginalPaperDTO customerOriginalPaperDTO) {

        if (null == customerOriginalPaperDTO.getCompanyId()
                || null == customerOriginalPaperDTO.getCustomerId()
                || StringUtils.isEmpty(customerOriginalPaperDTO.getCategory1())
                || StringUtils.isEmpty(customerOriginalPaperDTO.getCategory2())
                || StringUtils.isEmpty(customerOriginalPaperDTO.getCategory3())
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }

        List<CustomerOriginalPaperEntity> customerOriginalPaperEntities = customerOriginalPaperService.queryCustomerOriginalPaper(customerOriginalPaperDTO);
        //校验正本配置是否存在
        if (null == customerOriginalPaperDTO && CollectionUtils.isNotEmpty(customerOriginalPaperEntities)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
        }

        for (CustomerOriginalPaperEntity customerOriginalPaperEntity : customerOriginalPaperEntities) {
            if (!customerOriginalPaperEntity.getId().equals(customerOriginalPaperDTO.getId())) {
                throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
            }
        }

        CustomerOriginalPaperEntity customerOriginalPaperEntity = customerOriginalPaperDTO;
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        String category2 = "";
        String category3 = "";

        if (null == customerOriginalPaperEntity.getId()) {
            customerOriginalPaperEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name)
                    .setCategory1("," + customerOriginalPaperDTO.getCategory1() + ",")
                    .setCategory2("," + customerOriginalPaperDTO.getCategory2() + ",")
                    .setCategory3("," + customerOriginalPaperDTO.getCategory3() + ",")
            ;
        } else {
            if (StringUtil.isNotEmpty(customerOriginalPaperDTO.getCategory1())) {
                customerOriginalPaperDTO.setCategory1("," + customerOriginalPaperDTO.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(customerOriginalPaperDTO.getCategory2())) {
                category2 = "," + customerOriginalPaperDTO.getCategory2() + ",";
                customerOriginalPaperEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerOriginalPaperDTO.getCategory3())) {
                category3 = "," + customerOriginalPaperDTO.getCategory3() + ",";
                customerOriginalPaperEntity.setCategory3(category3);
            }
        }


        customerOriginalPaperEntity
                .setLdcFrame(customerOriginalPaperDTO.getLdcFrame())
                .setOriginalPaper(customerOriginalPaperDTO.getOriginalPaper())
                .setSaleType(customerOriginalPaperDTO.getSaleType())
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;
        customerOriginalPaperService.saveOrUpdateCustomerOriginalPaper(customerOriginalPaperEntity);

        //记录操作日志
        CustomerEntity customerEntity = customerService.getCustomerById(customerOriginalPaperEntity.getCustomerId());
        try {
            //todo 多品类操作记录修改
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerOriginalPaperDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(customerOriginalPaperEntity))
                    .setReferName(customerEntity.getName())
                    .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_NF)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public CustomerDepositRateEntity saveOrUpdateCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {

        /*if (null == customerDepositRateDTO.getCompanyId()
                || null == customerDepositRateDTO.getContractType()
                || null == customerDepositRateDTO.getDepositRate()
                || null == customerDepositRateDTO.getIsProcurement()
                || null == customerDepositRateDTO.getIsSales()
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }*/

        List<CustomerDepositRateEntity> customerDepositRateEntities = customerDepositRateService.queryCustomerDepositRate(customerDepositRateDTO);
        if (CollectionUtils.isNotEmpty(customerDepositRateEntities)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
        }

        CustomerDepositRateEntity customerDepositRateEntity = new CustomerDepositRateEntity();
        BeanUtils.copyProperties(customerDepositRateDTO, customerDepositRateEntity);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        if (null == customerDepositRateEntity.getId()) {
            customerDepositRateEntity
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name)
                    .setCreatedAt(new Date())
                    .setCategory1("," + customerDepositRateDTO.getCategory1() + ",")
                    .setCategory2("," + customerDepositRateDTO.getCategory2() + ",")
            ;
        } else {
            if (StringUtil.isNotEmpty(customerDepositRateDTO.getCategory1())) {
                customerDepositRateDTO.setCategory1("," + customerDepositRateDTO.getCategory1() + ",");
            }
            String category2 = "";
            if (StringUtil.isNotEmpty(customerDepositRateDTO.getCategory2())) {
                category2 = "," + customerDepositRateDTO.getCategory2() + ",";
                customerDepositRateEntity.setCategory2(category2);
            }

        }

        customerDepositRateEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;

        customerDepositRateService.saveOrUpdateCustomerDepositRate(customerDepositRateEntity);

        //记录操作记录
        CustomerEntity customerEntity = customerService.getCustomerById(customerDepositRateDTO.getCustomerId());
        try {

            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerDepositRateDTO))
                    .setBeforeData(JSON.toJSONString(customerDepositRateEntity))
                    .setAfterData(JSON.toJSONString(customerDepositRateEntity))
                    .setOperationActionEnum(OperationActionEnum.SBM_REDACT_CUSTOMER_DEPOSIT_RATE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return customerDepositRateEntity;
    }

    @Override
    public CustomerDetailEntity saveOrUpdateCustomerWhiteList(CustomerDetailDTO customerDetailDTO) {

        if (null == customerDetailDTO.getCustomerId()
                || null == customerDetailDTO.getIsWhiteList()
                || null == customerDetailDTO.getIsReversePrice()
                || null == customerDetailDTO.getIsStructure()
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }
        CustomerDetailEntity customerDetailEntity = new CustomerDetailEntity();
        BeanUtils.copyProperties(customerDetailDTO, customerDetailEntity);

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        if (null == customerDetailEntity.getId()) {
            //新增白名单校验客户是否配置了白名单

            customerDetailEntity
                    .setCreatedBy(currentUserId)
                    .setCreatedByName(name)
                    .setCreatedAt(new Date())
                    .setCategory1("," + customerDetailDTO.getCategory1() + ",")
                    .setCategory2("," + customerDetailDTO.getCategory2() + ",")
                    .setCategory3("," + customerDetailDTO.getCategory3() + ",")
            ;
        }
        customerDetailEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;
        //检验白名单是否存在重复参数
        String whiteList = checkCustomerWhiteList(customerDetailDTO);
        if (StringUtil.isNotEmpty(whiteList)) {
            throw new BusinessException(whiteList + "品种重复");
        }

        customerWhiteList(customerDetailDTO);

        String category2 = "";
        String category3 = "";
        if (null != customerDetailDTO.getId()) {
            if (StringUtil.isNotEmpty(customerDetailDTO.getCategory1())) {
                customerDetailEntity.setCategory1("," + customerDetailEntity.getCategory1() + ",");
            }

            if (StringUtil.isNotEmpty(customerDetailDTO.getCategory2())) {
                category2 = "," + customerDetailEntity.getCategory2() + ",";
                customerDetailEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerDetailDTO.getCategory3())) {
                category3 = "," + customerDetailEntity.getCategory3() + ",";
                customerDetailEntity.setCategory3(category3);
            }
        }

        CustomerEntity customerEntity = customerService.getCustomerById(customerDetailEntity.getCustomerId());
        customerDetailService.saveOrUpdateCustomerDetail(customerDetailEntity);
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerDetailDTO))
                    .setBeforeData(JSON.toJSONString(customerDetailEntity))
                    .setAfterData(JSON.toJSONString(customerDetailEntity))
                    .setOperationActionEnum(OperationActionEnum.SBM_UPDATE_CUSTOMER_DETAIL)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return customerDetailEntity;
    }

    /**
     * 白名单
     *
     * @param customerDetailDTO
     */
    private void customerWhiteList(CustomerDetailDTO customerDetailDTO) {
        CustomerDetailEntity customerDetailEntity = customerDetailService.queryCustomerDetailById(customerDetailDTO.getId());

        if (null != customerDetailEntity) {

            //编辑新入品种集合集合处理合同
            //新品种集合
            List<Integer> category3List = Arrays.stream(customerDetailDTO.getCategory3().split(","))
                    .map(String::trim)
                    .filter(category3 -> !category3.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            //原品种集合
            List<Integer> category3s = Arrays.stream(customerDetailEntity.getCategory3().split(","))
                    .filter(category3 -> !category3.isEmpty())
                    .map(category3 -> Integer.parseInt(category3.trim()))
                    .collect(Collectors.toList());

            //新增品种集合
            List<Integer> addList = category3List.stream()
                    .filter(element -> !category3s.contains(element))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(addList)) {
                String addCategory3 = addList.stream().map(String::valueOf).collect(Collectors.joining(","));
                customerDetailDTO
                        .setTransferStatusChange(true)
                        .setReversePriceStatusChange(true)
                        .setCategory3(addCategory3)
                ;


                contractFacade.changeCustomerWhiteList(customerDetailDTO);
            }

            //移除品种处理,关闭白名单
            //移除品种集合
            List<Integer> removeList = category3s.stream()
                    .filter(element -> !category3List.contains(element))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(removeList)) {
                String addCategory3 = removeList.stream().map(String::valueOf).collect(Collectors.joining(","));

                customerDetailDTO
                        .setTransferStatusChange(true)
                        .setReversePriceStatusChange(true)
                        .setIsWhiteList(DisableStatusEnum.DISABLE.getValue())
                        .setIsReversePrice(DisableStatusEnum.DISABLE.getValue())
                        .setCategory3(addCategory3)
                ;
                contractFacade.changeCustomerWhiteList(customerDetailDTO);
            }

            //无变化的品种集合
            List<Integer> commonCategory3List = CommonListUtil.getIntersection(category3List, category3s);
            if (CollectionUtils.isNotEmpty(commonCategory3List)) {
                String commonCategory3 = commonCategory3List.stream().map(String::valueOf).collect(Collectors.joining(","));
                customerDetailDTO.setCategory3(commonCategory3);
                // 转月状态
                Integer transferStatus = customerDetailDTO.getIsWhiteList();
                if (null != transferStatus && !customerDetailEntity.getIsWhiteList().equals(transferStatus)) {
                    customerDetailDTO.setTransferStatusChange(true);
                }

                // 反点价状态
                Integer reversePriceStatus = customerDetailDTO.getIsReversePrice();
                if (null != reversePriceStatus && !customerDetailEntity.getIsReversePrice().equals(reversePriceStatus)) {
                    customerDetailDTO.setReversePriceStatusChange(true);
                }

                // 更新合同的转月/反点价次
                if (customerDetailDTO.isTransferStatusChange() || customerDetailDTO.isReversePriceStatusChange()) {
                    contractFacade.changeCustomerWhiteList(customerDetailDTO);
                }
            }
            /*//开通白名单处理
            if (!GeneralEnum.NO.getValue().equals(customerDetailEntity.getIsWhiteList())
                    && !GeneralEnum.NO.getValue().equals(customerDetailEntity.getIsReversePrice())) {
                //移除品种集合
                List<Integer> removeList1 = category3s.stream()
                        .filter(element -> !category3List.contains(element))
                        .collect(Collectors.toList());

                for (Integer removeId : removeList1) {
                    customerDetailDTO
                            .setCategory2("")
                            .setCategory3(String.valueOf(removeId))
                    ;
                    CustomerDetailEntity customerDetail = customerDetailService.queryCustomerDetailListByNotId(customerDetailDTO);

                    if (null != customerDetail && (GeneralEnum.YES.getValue().equals(customerDetail.getIsWhiteList()) || GeneralEnum.YES.getValue().equals(customerDetail.getIsReversePrice()))) {

                        // 转月状态
                        if (!customerDetailEntity.getIsWhiteList().equals(customerDetailDTO.getIsWhiteList())) {
                            customerDetailDTO.setTransferStatusChange(true);
                        }
                        // 反点价状态
                        if (!customerDetailEntity.getIsReversePrice().equals(customerDetailDTO.getIsReversePrice())) {
                            customerDetailDTO.setReversePriceStatusChange(true);
                        }
                        // 更新合同的转月/反点价次
                        if (customerDetailDTO.isTransferStatusChange() || customerDetailDTO.isReversePriceStatusChange()) {
                            customerDetailDTO
                                    .setCategory3(String.valueOf(removeId))
                            ;
                            contractFacade.changeCustomerWhiteList(customerDetailDTO);
                        }
                    } else {
                        customerDetailDTO
                                .setTransferStatusChange(true)
                                .setReversePriceStatusChange(true)
                                .setIsWhiteList(GeneralEnum.NO.getValue())
                                .setIsReversePrice(GeneralEnum.NO.getValue())
                        ;
                        contractFacade.changeCustomerWhiteList(customerDetailDTO);
                    }

                }
            }

           */
        } else {
            customerDetailDTO
                    .setReversePriceStatusChange(true)
                    .setTransferStatusChange(true)
            ;
            contractFacade.changeCustomerWhiteList(customerDetailDTO)
            ;
        }
    }

    /**
     * 校验新增/编辑白名单是否有重复品种存在
     *
     * @param customerDetailDTO
     * @return
     */
    private String checkCustomerWhiteList(CustomerDetailDTO customerDetailDTO) {
        //新品种集合
        List<Integer> category3List = Arrays.stream(customerDetailDTO.getCategory3().split(","))
                .map(String::trim)
                .filter(category3 -> !category3.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        StringBuilder category3Name = new StringBuilder();
        for (Integer category3 : category3List) {
            CustomerDetailDTO customer = new CustomerDetailDTO();
            customer
                    .setId(customerDetailDTO.getId())
                    .setCustomerId(customerDetailDTO.getCustomerId())
                    .setCategory3(String.valueOf(category3))
            ;

            List<CustomerDetailEntity> customerDetailEntities = customerDetailService.checkCustomerWhiteList(customer);

            if (CollectionUtils.isNotEmpty(customerDetailEntities)) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);

                category3Name.append(categoryEntity.getName()).append(",");
            }
        }
        return category3Name.toString();
    }

    @Override
    public boolean saveOrUpdateCustomerBank(CustomerBankBO customerBankBO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));


        CustomerBankEntity customerBankEntity = BeanConvertUtils.convert(CustomerBankEntity.class, customerBankBO);

        String category1 = "";
        String category2 = "";

        if (null == customerBankEntity.getId()) {
            customerBankEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(currentUserId)
                    .setCreatedByName(name)
                    .setCategory1("," + customerBankBO.getCategory1() + ",")
                    .setCategory2("," + customerBankBO.getCategory2() + ",")
            ;
        } else {

            if (StringUtil.isNotEmpty(customerBankEntity.getCategory1())) {
                customerBankEntity.setCategory1("," + customerBankEntity.getCategory1() + ",");
            }

            if (StringUtil.isNotEmpty(customerBankEntity.getCategory2())) {
                category2 = "," + customerBankEntity.getCategory2() + ",";
                customerBankEntity.setCategory2(category2);
            }

        }

        customerBankEntity
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;
        //保存编辑客户银行账户信息
        customerBankService.saveOrUpdateCustomerBank(customerBankEntity);
        List<BankFactoryEntity> bankFactoryEntities = customerBankBO.getBankFactoryEntities();
        bankFactoryEntities.forEach(bankFactoryEntity -> {
            bankFactoryEntity
                    .setBankId(customerBankEntity.getId())
                    .setCustomerId(customerBankEntity.getCustomerId());
        });

        bankFactoryService.saveOrUpdateBankFactory(bankFactoryEntities);
        CustomerEntity customerEntity = customerService.getCustomerById(customerBankBO.getCustomerId());
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerBankBO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SBM_REDACT_CUSTOMER_BANK)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    @Override
    public CustomerInvoiceEntity saveOrUpdateCustomerInvoiceType(CustomerInvoiceEntity customerInvoiceEntity) {

        CustomerInvoiceEntity customerInvoice = customerInvoiceEntity;

        if (null == customerInvoiceEntity.getInvoiceId()
                || null == customerInvoiceEntity.getInvoiceName()
                || null == customerInvoiceEntity.getCategory1()
                || null == customerInvoiceEntity.getCategory2()
                || null == customerInvoiceEntity.getCategory3()
                || null == customerInvoiceEntity.getCompanyId()
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }
        SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(customerInvoiceEntity.getInvoiceId());
        if (null != systemRuleItemEntity) {
            //发票类型
            customerInvoiceEntity.setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleKey()));
        }

        CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
        customerInvoiceDTO
                .setInvoiceId(customerInvoiceEntity.getInvoiceId())
                .setCategory1(customerInvoiceEntity.getCategory1())
                .setCategory2(customerInvoiceEntity.getCategory2())
                .setCategory3(customerInvoiceEntity.getCategory3())
                .setCompanyId(customerInvoiceEntity.getCompanyId())
                .setCustomerId(customerInvoiceEntity.getCustomerId())
                .setId(customerInvoiceEntity.getId())
        ;

        List<CustomerInvoiceEntity> customerInvoiceEntities = customerInvoiceService.queryCustomerInvoiceListByCustomerId(customerInvoiceDTO);

        if (!customerInvoiceEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.RECORD_REPEAT);
        }

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        if (null == customerInvoiceEntity.getId()) {
            customerInvoiceEntity
                    .setCreatedBy(name)
                    .setCreatedAt(new Date())
            ;
        }
        CompanyEntity companyEntity = companyFacade.queryCompanyById(customerInvoiceEntity.getCompanyId());

        String category1 = "";
        String category2 = "";
        String category3 = "";

        if (null == customerInvoiceEntity.getId()) {
            category1 = "," + customerInvoiceEntity.getCategory1() + ",";
            category2 = "," + customerInvoiceEntity.getCategory2() + ",";
            category3 = "," + customerInvoiceEntity.getCategory3() + ",";
            customerInvoiceEntity
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
            ;
        } else {

            if (StringUtil.isNotEmpty(customerInvoiceEntity.getCategory1())) {
                customerInvoiceEntity.setCategory1("," + customerInvoiceEntity.getCategory1() + ",");
            }

            if (StringUtil.isNotEmpty(customerInvoiceEntity.getCategory2())) {
                category2 = "," + customerInvoiceEntity.getCategory2() + ",";
                customerInvoiceEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerInvoiceEntity.getCategory3())) {
                category3 = "," + customerInvoiceEntity.getCategory3() + ",";
                customerInvoiceEntity.setCategory3(category3);
            }
        }

        customerInvoiceEntity
                .setCompanyName(companyEntity.getShortName())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedAt(new Date())
                .setUpdatedBy(name)
        ;
        customerInvoiceService.saveOrUpdateCustomerInvoice(customerInvoiceEntity);
        CustomerEntity customerEntity = customerService.getCustomerById(customerInvoiceEntity.getCustomerId());
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerInvoice))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_CUSTOMER_INVOICE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public CustomerCreditPaymentEntity saveOrCustomerCreditPayment(CustomerCreditPaymentDTO customerCreditPaymentDTO) {

        if (null == customerCreditPaymentDTO.getCustomerId()
                || null == customerCreditPaymentDTO.getPaymentType()
                || null == customerCreditPaymentDTO.getCreditDays()
        ) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }

        CustomerCreditPaymentEntity customerCreditPaymentEntity = new CustomerCreditPaymentEntity();
        BeanUtils.copyProperties(customerCreditPaymentDTO, customerCreditPaymentEntity);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        if (null == customerCreditPaymentEntity.getId()) {
            customerCreditPaymentEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(currentUserId)
                    .setCreatedByName(name)
            ;
        }

        String category1 = "";
        String category2 = "";
        String category3 = "";

        if (null == customerCreditPaymentDTO.getId()) {
            category1 = "," + customerCreditPaymentDTO.getCategory1() + ",";
            category2 = "," + customerCreditPaymentDTO.getCategory2() + ",";
            category3 = "," + customerCreditPaymentDTO.getCategory3() + ",";
            customerCreditPaymentEntity
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
            ;
        } else {
            if (StringUtil.isNotEmpty(customerCreditPaymentEntity.getCategory1())) {
                customerCreditPaymentEntity.setCategory1("," + customerCreditPaymentEntity.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(customerCreditPaymentEntity.getCategory2())) {
                category2 = "," + customerCreditPaymentEntity.getCategory2() + ",";
                customerCreditPaymentEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerCreditPaymentEntity.getCategory3())) {
                category3 = "," + customerCreditPaymentEntity.getCategory3() + ",";
                customerCreditPaymentEntity.setCategory3(category3);
            }
        }

        customerCreditPaymentEntity
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;

        customerCreditPaymentService.saveOrUpdateCustomerCreditPayment(customerCreditPaymentEntity);
        CustomerEntity customerEntity = customerService.getCustomerById(customerCreditPaymentDTO.getCustomerId());
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerCreditPaymentDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SBM_ADD_CUSTOMER_CREDIT_PAYMENT)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return customerCreditPaymentEntity;
    }

    @Override
    public CustomerGradeScoreEntity saveOrUpdateCustomerGradeScore(CustomerGradeScoreEntity customerGradeScoreEntity) {

        CustomerGradeScoreEntity customerGradeScore = customerGradeScoreEntity;

        if (null == customerGradeScoreEntity.getGradeScore()) {
            throw new BusinessException(ResultCodeEnum.PARAMS_MISS);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null == customerGradeScoreEntity.getId()) {
            customerGradeScoreEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(name)
            ;
        }

        String category1 = "";
        String category2 = "";
        String category3 = "";

        if (null == customerGradeScoreEntity.getId()) {
            category1 = "," + customerGradeScoreEntity.getCategory1() + ",";
            category2 = "," + customerGradeScoreEntity.getCategory2() + ",";
            category3 = "," + customerGradeScoreEntity.getCategory3() + ",";
            customerGradeScoreEntity
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
            ;
        } else {
            if (StringUtil.isNotEmpty(customerGradeScoreEntity.getCategory1())) {
                customerGradeScoreEntity.setCategory1("," + customerGradeScoreEntity.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(customerGradeScoreEntity.getCategory2())) {
                category2 = "," + customerGradeScoreEntity.getCategory2() + ",";
                customerGradeScoreEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerGradeScoreEntity.getCategory3())) {
                category3 = "," + customerGradeScoreEntity.getCategory3() + ",";
                customerGradeScoreEntity.setCategory3(category3);
            }
        }

        customerGradeScoreEntity
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedAt(new Date())
                .setUpdatedBy(name)
        ;

        customerGradeScoreService.saveOrUpdateCustomerGradeScore(customerGradeScoreEntity);

        CustomerEntity customerEntity = customerService.getCustomerById(customerGradeScore.getCustomerId());

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerGradeScore))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_CUSTOMER_GRADE_SCORE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return customerGradeScoreEntity;
    }

    @Override
    public boolean saveOrUpdateCustomerDeliveryWhite(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity) {
        CustomerDeliveryWhiteEntity customerDeliveryWhite = customerDeliveryWhiteEntity;

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null == customerDeliveryWhiteEntity.getId()) {
            customerDeliveryWhiteEntity
                    .setCreatedAt(new Date())
                    .setCreatedBy(name)
                    .setCategory1("," + customerDeliveryWhiteEntity.getCategory1() + ",")
                    .setCategory2("," + customerDeliveryWhiteEntity.getCategory2() + ",")
                    .setCategory3("," + customerDeliveryWhiteEntity.getCategory3() + ",")
            ;
        } else {
            String category2 = "";
            String category3 = "";
            if (StringUtil.isNotEmpty(customerDeliveryWhiteEntity.getCategory1())) {
                customerDeliveryWhiteEntity.setCategory1("," + customerDeliveryWhiteEntity.getCategory1() + ",");
            }
            if (StringUtil.isNotEmpty(customerDeliveryWhiteEntity.getCategory2())) {
                category2 = "," + customerDeliveryWhiteEntity.getCategory2() + ",";
                customerDeliveryWhiteEntity.setCategory2(category2);
            }
            if (StringUtil.isNotEmpty(customerDeliveryWhiteEntity.getCategory3())) {
                category3 = "," + customerDeliveryWhiteEntity.getCategory3() + ",";
                customerDeliveryWhiteEntity.setCategory3(category3);
            }
        }

        customerDeliveryWhiteEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(name)
        ;

        customerDeliveryWhiteService.saveOrUpdateCustomerDeliveryWhite(customerDeliveryWhiteEntity);
        CustomerEntity customerEntity = customerService.getCustomerById(customerDeliveryWhite.getCustomerId());

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(customerEntity.getName())
                    .setDtoData(JSON.toJSONString(customerDeliveryWhite))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_CUSTOMER_DELIVERY_WHITE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }
}
