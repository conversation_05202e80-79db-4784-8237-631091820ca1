package com.navigator.customer.service;


import com.navigator.common.dto.Result;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IFactoryWarehouseService {

    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId
     * @return
     */
    List<FactoryWarehouseEntity> getFactoryWarehouseList(List<Integer> factoryId, String factoryCode, Integer goodsCategoryId, Integer status);

    List<FactoryWarehouseEntity> getFactoryWarehouseList(FactoryQueryDTO factoryQueryDTO);

    FactoryWarehouseEntity queryFactoryWarehouseById(Integer id);

    FactoryWarehouseEntity getFactoryDetailByLkg(String lkgWareHouseCode);

    Result queryFactoryWarehouseByCode(String factoryCode);

    boolean saveOrUpdateFactoryWarehouse(FactoryWarehouseEntity warehouseEntity);

    boolean invalidWarehouse(Integer warehouseId);

    /**
     * 导入发货库点信息
     *
     * @param uploadFile
     * @return
     */
    Result importFactoryWarehouse(MultipartFile uploadFile);

    List<FactoryWarehouseEntity> getWarehouseListById(List<Integer> warehouseIdList);
}
