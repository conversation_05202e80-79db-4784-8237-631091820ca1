package com.navigator.customer.dao;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.mapper.CustomerProtocolMapper;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;

import java.util.List;

@Dao
public class CustomerProtocolDao extends BaseDaoImpl<CustomerProtocolMapper, CustomerProtocolEntity> {

     public void deleteByCondition(CustomerProtocolEntity customerProtocolEntity) {
        remove(Wrappers.<CustomerProtocolEntity>lambdaUpdate()
                .eq(CustomerProtocolEntity::getCustomerId, customerProtocolEntity.getCustomerId())
//                .eq(null != customerProtocolEntity.getCategoryId(), CustomerProtocolEntity::getCategoryId, customerProtocolEntity.getCategoryId())
                .like(StringUtil.isNotEmpty(customerProtocolEntity.getCategory2()), CustomerProtocolEntity::getCategory2, "," + customerProtocolEntity.getCategory2() + ",")
        );
    }

    public List<CustomerProtocolEntity> queryCustomerProtocolList(CustomerProtocolDTO customerProtocolDTO) {
        return list(Wrappers.<CustomerProtocolEntity>lambdaQuery()
                .eq(CustomerProtocolEntity::getCustomerId, customerProtocolDTO.getCustomerId())
//                .eq(null != customerProtocolDTO.getCategoryId(), CustomerProtocolEntity::getCategoryId, customerProtocolDTO.getCategoryId())
                .like(StringUtils.isNotEmpty(customerProtocolDTO.getCategory1()), CustomerProtocolEntity::getCategory1, "," + customerProtocolDTO.getCategory1() + ",")
                .like(StringUtils.isNotEmpty(customerProtocolDTO.getCategory2()), CustomerProtocolEntity::getCategory2, "," + customerProtocolDTO.getCategory2() + ",")
                .like(StringUtils.isNotEmpty(customerProtocolDTO.getCategory3()), CustomerProtocolEntity::getCategory3, "," + customerProtocolDTO.getCategory3() + ",")
                .eq(customerProtocolDTO.getCompanyId() != null, CustomerProtocolEntity::getCompanyId, customerProtocolDTO.getCompanyId())
                .eq(customerProtocolDTO.getSaleType() != null, CustomerProtocolEntity::getSaleType, customerProtocolDTO.getSaleType())
                .eq(customerProtocolDTO.getStatus() != null, CustomerProtocolEntity::getStatus, customerProtocolDTO.getStatus())
                .eq(CustomerProtocolEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CustomerProtocolEntity::getUpdatedAt)
        );
    }

    public void deleteByCustomerId(String customerId) {
        remove(Wrappers.<CustomerProtocolEntity>lambdaQuery()
                .eq(CustomerProtocolEntity::getCustomerId, customerId)
        );
    }
}