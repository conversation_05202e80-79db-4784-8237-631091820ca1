package com.navigator.customer.app.mdm.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.util.BeanCompareUtils;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.app.mdm.CustomerMdmAppService;
import com.navigator.customer.dao.CustomerMdmCounterpartyRecordDao;
import com.navigator.customer.pojo.dto.mdm.*;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerMdmCounterpartyRecordEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.service.ICustomerService;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractReviseTTDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/24
 */
@Service
public class CustomerMdmAppServiceImpl implements CustomerMdmAppService {

    @Resource
    private CustomerMdmCounterpartyRecordDao customerMdmCounterpartyRecordDao;
    @Resource
    private ICustomerService customerService;


    @Override
    public MDMCustomerResponseDTO saveOrUpdateMDMCustomer(MDMObjectDataDTO mdmObjectDataDTO) {
        MDMCustomerResponseDTO mdmCustomerResponseDTO = new MDMCustomerResponseDTO();
        try {
            /*ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> map = mapper.readValue(file.getInputStream(), new TypeReference<Map<String, Object>>() {
            });*/
            MDMCustomerDataDTO mdmCustomerDataDTO = new MDMCustomerDataDTO();
            Object dataObject = mdmObjectDataDTO.getData();
            mdmCustomerDataDTO.setRequestData(dataObject.toString());
            CustomerMdmCounterpartyRecordEntity customerMdmCounterpartyRecordEntity = saveCustomerMdmCounterpartyRecordEntity(mdmCustomerDataDTO);

            if (dataObject instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) dataObject;
                mdmCustomerDataDTO = mdmCustomerDataDTO(dataMap);
                //插入数据
                updateCustomerMdmCounterpartyRecordEntity(mdmCustomerDataDTO, customerMdmCounterpartyRecordEntity);
                //编辑客户数据
                prepareCustomerMdmDataInfo(mdmCustomerDataDTO, customerMdmCounterpartyRecordEntity);
            }

            return mdmCustomerResponseDTO.setCounterpartyID(customerMdmCounterpartyRecordEntity.getCounterpartyId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mdmCustomerResponseDTO;
    }

    private CustomerMdmCounterpartyRecordEntity saveCustomerMdmCounterpartyRecordEntity(MDMCustomerDataDTO mdmCustomerDataDTO) {
        CustomerMdmCounterpartyRecordEntity customerMdmCounterpartyRecordEntity = new CustomerMdmCounterpartyRecordEntity();
        customerMdmCounterpartyRecordEntity
                .setRequestData(mdmCustomerDataDTO.getRequestData())
                .setRequestTime(new Date())
        ;
        customerMdmCounterpartyRecordDao.save(customerMdmCounterpartyRecordEntity);
        return customerMdmCounterpartyRecordEntity;
    }

    private CustomerMdmCounterpartyRecordEntity updateCustomerMdmCounterpartyRecordEntity(MDMCustomerDataDTO mdmCustomerDataDTO, CustomerMdmCounterpartyRecordEntity customerMdmCounterpartyRecordEntity) {
        customerMdmCounterpartyRecordEntity.setCounterpartyId(mdmCustomerDataDTO.getCounterpartyID());
        customerMdmCounterpartyRecordEntity.setAddressList(JSON.toJSONString(mdmCustomerDataDTO.getAddressList(), true));
        customerMdmCounterpartyRecordEntity.setBankDetailsList(mdmCustomerDataDTO.getBankDetailsList());
        customerMdmCounterpartyRecordEntity.setCategoryList(JSON.toJSONString(mdmCustomerDataDTO.getCategoryList(), true));

        customerMdmCounterpartyRecordDao.updateById(customerMdmCounterpartyRecordEntity);
        return customerMdmCounterpartyRecordEntity;
    }


    /**
     * 处理MDMData数据
     */
    private MDMCustomerDataDTO mdmCustomerDataDTO(Map<String, Object> dataMap) {

        MDMCustomerDataDTO mdmCustomerDataDTO = new MDMCustomerDataDTO();

        mdmCustomerDataDTO.setCounterpartyID(String.valueOf(dataMap.get("counterpartyID")));
        mdmCustomerDataDTO.setName(String.valueOf(dataMap.get("localName")));
        mdmCustomerDataDTO.setFullNameEnglish(String.valueOf(dataMap.get("name")));
        //case:1003116 获取MDM trade status接口字段改为globalTradeStatus Author:Wan 2025-04-10 start
        mdmCustomerDataDTO.setTradeStatus(String.valueOf(dataMap.get("globalTradeStatus")));
        //case:1003116 获取MDM trade status接口字段改为globalTradeStatus Author:Wan 2025-04-10 start
        //客户是否是供应商
        List<CategoryDTO> categoryList = new ArrayList<>();
        Object objCategoryList = dataMap.get("categoryList");
        if (null != objCategoryList) {
            categoryList = getCategoryList(objCategoryList);
        }
        mdmCustomerDataDTO.setCategoryList(categoryList);

        //客户地址
        List<AddressDTO> addressList = new ArrayList<>();
        Object objAddressList = dataMap.get("addressList");
        if (objAddressList instanceof Map) {
            addressList = getAddressList(objAddressList);
        }
        mdmCustomerDataDTO.setAddressList(addressList);
        //客户银行账户信息
        String bankDetailsList = JSON.toJSONString(dataMap.get("bankDetailsList"), true);
        mdmCustomerDataDTO.setBankDetailsList(bankDetailsList);

        return mdmCustomerDataDTO;
    }


    /**
     * 是否是供应商 当"code" : "INTERCOMPANY"中的"isMainCategory" : "true"成立时，为否；不成立时为是。
     *
     * @param objCategoryList
     * @return
     */
    private List<CategoryDTO> getCategoryList(Object objCategoryList) {

        List<CategoryDTO> categoryList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        try {
            String categoryData = JSON.toJSONString(objCategoryList, true);
            JsonNode rootNode = mapper.readTree(categoryData);
            JsonNode categoryNode = rootNode.path("category");


            if (categoryNode.isArray()) {
                for (JsonNode category : categoryNode) {
                    CategoryDTO categoryDTO = new CategoryDTO();
                    categoryDTO.setCode(category.path("code").asText());
                    categoryDTO.setName(category.path("name").asText());
                    categoryDTO.setIsMainCategory(category.path("isMainCategory").asText()); // 使用自定义的setter
                    categoryDTO.setSendToCreditRisk(category.path("sendToCreditRisk").asText());

                    JsonNode subCategoriesNode = category.path("subCategories");
                    if (subCategoriesNode.isObject() && subCategoriesNode.has("subCategory")) {
                        List<CategoryDTO.SubCategory> subCategories = new ArrayList<>();
                        for (JsonNode subCategory : subCategoriesNode.path("subCategory")) {
                            CategoryDTO.SubCategory subCategoryDTO = new CategoryDTO.SubCategory();
                            subCategoryDTO.setCode(subCategory.path("code").asText());
                            subCategoryDTO.setName(subCategory.path("name").asText());
                            subCategories.add(subCategoryDTO);
                        }
                        categoryDTO.setSubCategories(subCategories);
                    }

                    categoryList.add(categoryDTO);
                }
            }
            return categoryList;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return categoryList;
    }


    /**
     * 获取地址信息 先判断 data['addressType']==’ALTERNATE_LANGUAGE_ADDRESS‘ 时是否有值，若无则判断 data['addressType']==’REGISTERED_ADDRESS‘是否有值，将其中addrLine1与addrLine2拼接后插入
     *
     * @param objAddressList
     * @return
     */
    private List<AddressDTO> getAddressList(Object objAddressList) {

        List<AddressDTO> addressList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        try {
            String addressData = JSON.toJSONString(objAddressList, true);
            JsonNode rootNode = mapper.readTree(addressData);
            JsonNode categoryNode = rootNode.path("address");


            if (categoryNode.isArray()) {
                for (JsonNode address : categoryNode) {
                    AddressDTO addressDTO = new AddressDTO();
                    addressDTO.setIdAddress(address.path("idAddress").asText());
                    addressDTO.setAddrLine1(address.path("addrLine1").asText());
                    addressDTO.setAddrLine2(address.path("addrLine2").asText());
                    addressDTO.setAddrLine3City(address.path("addrLine3City").asText());
                    addressDTO.setCountyName(address.path("countyName").asText());
                    addressDTO.setCountryCode2(address.path("countryCode2").asText());
                    addressDTO.setCountryName(address.path("countryName").asText());
                    addressDTO.setIsMainAddress(address.path("isMainAddress").asText());

                    //将addressType转换成map
                    Map<String, String> addressTypeMap = new HashMap<>();
                    addressTypeMap.put("code", address.path("addressType").path("code").asText());
                    addressTypeMap.put("name", address.path("addressType").path("name").asText());

                    addressDTO.setAddressType(addressTypeMap);
                    addressList.add(addressDTO);
                }
            }
            return addressList;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return addressList;
    }

    /**
     * 编辑,客户主数据信息处理
     *
     * @param mdmCustomerDataDTO
     * @return
     */
    private CustomerEntity prepareCustomerMdmDataInfo(MDMCustomerDataDTO mdmCustomerDataDTO, CustomerMdmCounterpartyRecordEntity customerMdmCounterpartyRecordEntity) {

        CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(mdmCustomerDataDTO.getCounterpartyID());
        CustomerEntity newCustomerEntity = new CustomerEntity();
        //检验客户是否存在
        if (null == customerEntity) {
            //修改后数据
            customerMDMInfo(mdmCustomerDataDTO, customerMdmCounterpartyRecordEntity, newCustomerEntity);
            customerMdmCounterpartyRecordEntity.setAfterData(JSON.toJSONString(newCustomerEntity, true));
            customerMdmCounterpartyRecordEntity.setOperationType(1);
        } else {
            newCustomerEntity = BeanConvertUtils.convert(CustomerEntity.class, customerEntity);
            //修改前数据
            customerMdmCounterpartyRecordEntity.setBeforeData(JSON.toJSONString(newCustomerEntity, true));
            //处理数据
            customerMDMInfo(mdmCustomerDataDTO, customerMdmCounterpartyRecordEntity, newCustomerEntity);
            //修改前后数据对比集合
            List<CompareObjectDTO> compareObjectList = compareObjectList(newCustomerEntity, customerEntity);
            customerMdmCounterpartyRecordEntity.setContent(JSON.toJSONString(compareObjectList, true));

            //修改后客户数据
            customerMdmCounterpartyRecordEntity.setAfterData(JSON.toJSONString(newCustomerEntity, true));
            customerMdmCounterpartyRecordEntity.setOperationType(0);
        }

        customerMdmCounterpartyRecordEntity.setResponseData(JSON.toJSONString(Result.success(), true));
        customerService.saveOrUpdateCustomer(newCustomerEntity);
        customerMdmCounterpartyRecordDao.updateById(customerMdmCounterpartyRecordEntity);
        return newCustomerEntity;
    }

    private CustomerEntity customerMDMInfo(MDMCustomerDataDTO mdmCustomerDataDTO, CustomerMdmCounterpartyRecordEntity customerMdmCounterpartyRecordEntity, CustomerEntity customerEntity) {
        //客户编码
        String linkageCustomerCode = mdmCustomerDataDTO.getCounterpartyID();
        String customerName = mdmCustomerDataDTO.getName();
        //当"code" : "INTERCOMPANY"中的"isMainCategory" : "true"成立时，为否；不成立时为是。
        //case:1002979 MDM修改客户主数据状态 Author:Wan 2025-02-20 start
        Integer status = "NO_TRADE".equals(mdmCustomerDataDTO.getTradeStatus()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();
        //case:1002979 MDM修改客户主数据状态 Author:Wan 2025-02-20 end
        //case:1002973 采购头寸处理搜不到客户,mdm同步数据是否是供应商校验逻辑修改 Author:Wan 2025-02-20 start
        //case:1002973 获取MDM主数据接口校验取消更新is_supplier字段 Author:Wan 2025-06-03 start
        /*Integer isSupplier = GeneralEnum.YES.getValue();
        for (CategoryDTO categoryDTO : mdmCustomerDataDTO.getCategoryList()) {
            //当"code" : "INTERCOMPANY"中的"isMainCategory" : "true"成立时，为否；不成立时为是。
            if ("INTERCOMPANY".equals(categoryDTO.getCode())) {
                if ("true".equals(categoryDTO.getIsMainCategory())) {
                    isSupplier = GeneralEnum.NO.getValue();
                    break;
                }
            }
        }*/
        //case:1002973 采购头寸处理搜不到客户,mdm同步数据是否是供应商校验逻辑修改 Author:Wan 2025-02-20 end
        //case:1002973 获取MDM主数据接口校验取消更新is_supplier字段 Author:Wan 2025-06-03 end
        String fullName = mdmCustomerDataDTO.getName();
        String fullNameEnglish = mdmCustomerDataDTO.getFullNameEnglish();

        //先判断data['addressType']==’ALTERNATE_LANGUAGE_ADDRESS‘ 时是否有值，若无则判断 data['addressType']==’REGISTERED_ADDRESS‘是否有值，将其中addrLine1与addrLine2拼接后插入
        String address = "";
        //data['addressType'] == ’INVOICING_ADDRESS‘ addrLine1与addrLine2拼接后插入
        String invoiceAddress = "";
        for (AddressDTO addressDTO : mdmCustomerDataDTO.getAddressList()) {
            if (addressDTO.getAddressType().get("code").equals("ALTERNATE_LANGUAGE_ADDRESS")
                    || addressDTO.getAddressType().get("code").equals("REGISTERED_ADDRESS")
            ) {

                String addrLine1 = StringUtil.isNotBlank(addressDTO.getAddrLine1()) ? addressDTO.getAddrLine1() : "";
                String addrLine2 = StringUtil.isNotBlank(addressDTO.getAddrLine2()) ? addressDTO.getAddrLine2() : "";
                address = addrLine1 + addrLine2;
            }
            if (addressDTO.getAddressType().get("code").equals("’INVOICING_ADDRESS")) {
                invoiceAddress = addressDTO.getAddrLine1() + addressDTO.getAddrLine2();
            }

        }
        customerEntity
                .setLinkageCustomerCode(linkageCustomerCode)
                //MDM数据同步MDMCode Author:Wan 2025-03-28 start
                .setMdmCustomerCode(linkageCustomerCode)
                //MDM数据同步MDMCode Author:Wan 2025-03-28 end
                .setName(customerName)
                .setStatus(status)
                //case:1002973 获取MDM主数据接口校验取消更新is_supplier字段 Author:Wan 2025-06-03 start
                //.setIsSupplier(isSupplier)
                //case:1002973 获取MDM主数据接口校验取消更新is_supplier字段 Author:Wan 2025-06-03 end
                .setFullName(fullName)
                .setFullNameEnglish(fullNameEnglish)
                .setInvoiceAddress(invoiceAddress)
                .setAddress(address)
                .setTradeStatus(mdmCustomerDataDTO.getTradeStatus())
        ;
        customerMdmCounterpartyRecordEntity.setAfterData(JSON.toJSONString(customerEntity, true));
        return customerEntity;
    }

    /**
     * 客户主数据信息对比
     *
     * @param newCustomerEntity
     * @param customerEntity
     * @return
     */
    private List<CompareObjectDTO> compareObjectList(CustomerEntity newCustomerEntity, CustomerEntity customerEntity) {
        List<String> ignoreList = getIgnoreList();
        ignoreList.add("extraPrice");
        List<String> manualList = getManualList();
        //客户主数据信息对比
        return BeanCompareUtils.compareFieldsWithNeeding(customerEntity, newCustomerEntity, ignoreList, manualList);
    }

    public List<String> getIgnoreList() {
        List<String> ignoreList = new ArrayList<>();
        ignoreList.add("linkageCustomerCode");
        ignoreList.add("name");
        ignoreList.add("status");
        ignoreList.add("isSupplier");
        ignoreList.add("fullName");
        ignoreList.add("fullNameEnglish");
        ignoreList.add("invoiceAddress");
        ignoreList.add("address");
        return ignoreList;
    }

    public List<String> getManualList() {
        List<String> manualList = new ArrayList<>();
        Field[] fields = SalesContractReviseTTDTO.class.getDeclaredFields();
        Field[] fields1 = PriceDetailBO.class.getDeclaredFields();
        Arrays.stream(fields).forEach(i -> manualList.add(i.getName()));
        Arrays.stream(fields1).forEach(i -> manualList.add(i.getName()));
        manualList.remove("fobUnitPrice");
        manualList.remove("taxRate");
        manualList.remove("cifUnitPrice");
        manualList.remove("totalAmount");
        manualList.remove("unitPrice");
        return manualList;
    }

}
