package com.navigator.customer.service;

import com.navigator.customer.pojo.entity.BankFactoryEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 16:53
 */
public interface BankFactoryService {

    /**
     * 根据银行id查询出银行关联的油厂信息
     *
     * @param bankId
     * @return
     */
    List<BankFactoryEntity> queryBankFactoryByBankId(Integer bankId);

    /**
     * 修改油厂和银行的关联
     *
     * @param bankFactoryEntity
     * @return
     */
    boolean updateBankFactory(BankFactoryEntity bankFactoryEntity);

    /**
     * 根据银行id 油厂id 查询信息
     *
     * @param bankId
     * @param factoryId
     * @return
     */
    List<BankFactoryEntity> queryBankFactoryByBankIdFactoryId(Integer bankId,Integer factoryId);


    boolean saveOrUpdateBankFactory(List<BankFactoryEntity> bankFactoryEntity);

    boolean saveBankFactory(BankFactoryEntity bankFactoryEntity);


    /**
     * 根据银行信息删除工厂关联信息
     * @param bankId
     * @return
     */
    boolean deleteBankFactoryByBankId(Integer bankId);


}
