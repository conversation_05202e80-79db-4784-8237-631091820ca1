package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.customer.service.CustomerProtocolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class CustomerProtocolFacadeImpl implements CustomerProtocolFacade {

    @Autowired
    private CustomerProtocolService customerProtocolService;
    @Override
    public Result queryCustomerProtocolList(CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolService.queryCustomerProtocolList(customerProtocolDTO);
    }

    @Override
    public Result saveCustomerProtocol(CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolService.saveCustomerProtocol(customerProtocolDTO);
    }

    @Override
    public CustomerProtocolEntity queryCustomerProtocolEntity(CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolService.queryCustomerProtocolEntity(customerProtocolDTO);
    }

    @Override
    public Result updateCustomerProtocol(CustomerProtocolDTO customerProtocolDTO){
        return customerProtocolService.updateCustomerProtocol(customerProtocolDTO);
    }

    @Override
    public Result queryCustomerProtocolSign(CustomerProtocolDTO customerProtocolDTO){
        return customerProtocolService.queryCustomerProtocolSign(customerProtocolDTO);
    }

    @Override
    public Result importCustomerProtocol(MultipartFile file) {
        return customerProtocolService.importCustomerProtocol(file);
    }
}
