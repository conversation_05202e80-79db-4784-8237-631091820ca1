package com.navigator.customer.service.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.dao.FactoryConfigDao;
import com.navigator.customer.dao.SupplierDao;
import com.navigator.customer.pojo.dto.SupplierDTO;
import com.navigator.customer.pojo.entity.SupplierEntity;
import com.navigator.customer.service.ISupplierService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 供应商（油厂）表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
@Service
public class SupplierServiceImpl implements ISupplierService {

    @Resource
    private SupplierDao supplierDao;
    @Resource
    private FactoryConfigDao factoryConfigDao;

    /**
     * 更具供应商id查询出供应商信息
     *
     * @param id
     * @return
     */
    @Override
    public SupplierDTO querySupplierById(Integer id) {
        return null;
    }

    @Override
    public List<SupplierEntity> getSupplierListByType(Integer isFactory) {
        return null;
    }

    @Override
    public Result querySupplierList() {
        return null;
    }

    @Override
    public Result querySupplierFactory(Integer parentId) {
        return null;
    }
}
