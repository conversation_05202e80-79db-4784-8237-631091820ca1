package com.navigator.customer.service.impl;


import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.dao.ContactDao;
import com.navigator.customer.dao.ContactFactoryDao;
import com.navigator.customer.dao.CustomerDao;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.dto.file.ContactExcelDTO;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDTO;
import com.navigator.customer.pojo.entity.*;
import com.navigator.customer.pojo.enums.ReferTypeEnum;
import com.navigator.customer.pojo.vo.CustomerContactVO;
import com.navigator.customer.service.IContactFactoryService;
import com.navigator.customer.service.IContactService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户联系人表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class ContactServiceImpl implements IContactService {

    @Resource
    private ContactDao contactDao;
    @Resource
    private CustomerDao customerDao;
    @Resource
    private FactoryServiceImpl factoryService;
    @Resource
    private IContactFactoryService contactFactoryService;
    @Resource
    private ContactFactoryDao contactFactoryDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;

    @Override
    public List<ContactEntity> queryContactEntity(Integer referId, Integer referType, Integer categoryId) {
        return contactDao.queryContactEntity(referId, referType, categoryId, null);
    }

    @Override
    public Integer updateContactEntity(ContactEntity contactEntity) {
        return contactDao.updateContactEntity(contactEntity);
    }

    @Override
    public Integer vaseContactEntity(ContactEntity contactEntity) {
        return contactDao.save(contactEntity) ? 1 : 0;
    }

    @Override
    public ContactEntity getContactEntityById(Integer id) {
        return contactDao.getContactEntityById(id);
    }

    @Override
    public List<CustomerContactVO> queryContactFactoryList(ContactDTO contactDTO) {

        List<CustomerContactVO> customerContactVOS = new ArrayList<>();

        //根据客户品类查询联客户联系人信息
        List<ContactEntity> contactEntities = contactDao.queryContactByConditionList(contactDTO);

        //查询工厂联系人配置
        for (ContactEntity contactEntity : contactEntities) {
            CustomerContactVO customerContactVO = BeanConvertUtils.convert(CustomerContactVO.class, contactEntity);

            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(contactEntity.getCategory2().split(","))
                        .map(String::trim)
                        .filter(category2 -> !category2.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if(null != categoryEntity){
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }

            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = Arrays.stream(contactEntity.getCategory3().split(","))
                        .map(String::trim)
                        .filter(category3 -> !category3.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            for (Integer category3 : category3List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                if (null != categoryEntity) {
                    category3Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category3.equals(category3List.get(category3List.size() - 1))) {
                        category3Name.append(",");
                    }
                }
            }
            customerContactVO.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, category3List));
            customerContactVO.setCategory2Name(category2Name.toString());
            customerContactVO.setCategory3Name(category3Name.toString());
            customerContactVO.setCategory1(contactEntity.getCategory1().replaceAll("^,+|,+$", ""));
            customerContactVO.setCategory2(contactEntity.getCategory2().replaceAll("^,+|,+$", ""));
            customerContactVO.setCategory3(contactEntity.getCategory3().replaceAll("^,+|,+$", ""));
            customerContactVO.setCustomerId(Integer.parseInt(contactEntity.getReferId()));


            List<ContactFactoryEntity> contactFactoryEntities = contactFactoryDao.queryContactFactoryByContactId(contactEntity.getId());
            customerContactVO.setContactFactoryEntities(contactFactoryEntities);
            customerContactVOS.add(customerContactVO);
        }
        return customerContactVOS;
    }

    @Override
    public List<ContactEntity> getContactCustomerByList(Integer referId, Integer categoryId, Integer supplierId) {
        //根据油厂id查询出
        List<ContactEntity> contactEntities = contactDao.queryContactEntity(referId, ReferTypeEnum.CUSTOMER.getValue(), categoryId, supplierId);
        if (contactEntities.isEmpty()) {
            contactEntities = contactDao.queryContactEntity(referId, ReferTypeEnum.CUSTOMER.getValue(), categoryId, 0);
        }
        return contactEntities;
    }

    @Override
    public List<ContactEntity> queryLDCContactEntity(Integer referId, Integer referType, Integer categoryId) {
        //根据油厂id查询出
        List<ContactEntity> contactEntities = contactDao.queryLDCContactEntity(referId, referType, categoryId);
        return contactEntities;
    }

    @Override
    public ContactDTO getCustomerByCustomerIdYqq(Integer customerId) {
        return contactDao.getCustomerByCustomerIdYqq(customerId);
    }


    /**
     * 根据客户id查询客户联系人
     *
     * @param customerId
     * @return
     */
    @Override
    public List<ContactEntity> getCustomerByCustomerId(Integer customerId, Integer referType, Integer categoryId) {
        return contactDao.getCustomerByCustomerId(customerId, referType, categoryId);
    }

    @Override
    public boolean deleteContactFactoryEntityBy(Integer contactId) {
        ContactEntity contactEntity = contactDao.getById(contactId);
        contactEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
        //删除客户和油厂的关系
        contactFactoryService.deleteContactFactoryByContactId(contactId);

        return contactDao.updateById(contactEntity);
    }

    @Override
    public List<ContactEntity> queryContactByFactoryList(CustomerAllMessageDTO customerAllMessageDTO) {
        List<ContactEntity> contactDTOS = new ArrayList<>();
        Integer referType = 0;
        if (ContractSalesTypeEnum.SALES.getValue() == customerAllMessageDTO.getSalesType()) {
            referType = ReferTypeEnum.CUSTOMER.getValue();
        } else {
            referType = ReferTypeEnum.SUPPLIER.getValue();
        }
        //根据客户品类查询联客户联系人信息
        List<ContactEntity> contactEntities = contactDao.getContactByCustomerId(customerAllMessageDTO, referType);
        //查询工厂联系人配置
        for (ContactEntity contactEntity : contactEntities) {

            List<ContactFactoryEntity> contactFactoryEntities = contactFactoryDao.getValidContactFactoryByContactId(contactEntity.getId(), customerAllMessageDTO.getFactoryId());

            if (!contactFactoryEntities.isEmpty()) {
                contactDTOS.add(contactEntity);
            }
        }
        /*if (contactDTOS.isEmpty()) {
            contactDTOS = contactEntities;
        }*/

        return contactDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result leadCustomerContact(MultipartFile file) {
        List<ContactExcelDTO> contactExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, ContactExcelDTO.class);

        List<String> customerCodeList = new ArrayList<>();

        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);

        for (ContactExcelDTO contactExcelDTO : contactExcelDTOS) {
            CustomerEntity customerEntity = customerDao.queryCustomerByLinkageCustomerCode(contactExcelDTO.getLinkageCustomerCode());
            //保存联系人数据
            if (null != customerEntity) {
                //客户信息
                CustomerTemplateDTO customerTemplateDTO = new CustomerTemplateDTO();
                customerTemplateDTO.setCustomerId(customerEntity.getId());
                //查询货品数据
                List<String> category1List = Arrays.stream(contactExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
                List<String> category2List = Arrays.stream(contactExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
                List<String> category3List = Arrays.stream(contactExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

                //获取品种id
                String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
                String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
                String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);
                //通知人信息
                Integer id = null != contactExcelDTO.getId() ? Integer.valueOf(contactExcelDTO.getId()) : null;

                ContactEntity contactEntity = new ContactEntity();
                contactEntity
                        .setReferId(String.valueOf(customerEntity.getId()))
                        .setId(id)
                        .setCategory1(category1)
                        .setCategory2(category2)
                        .setCategory3(category3)
                        .setContactName(contactExcelDTO.getContactName())
                        .setContactPhone(contactExcelDTO.getContactPhone())
                        .setContactPhone(contactExcelDTO.getContactPhone())
                        .setEmail(contactExcelDTO.getEmail())
                        .setAddress(contactExcelDTO.getAddress());

                if ("采购".equals(contactExcelDTO.getSalesType())) {
                    contactEntity.setReferType(ReferTypeEnum.SUPPLIER.getValue());
                }
                if ("销售".equals(contactExcelDTO.getSalesType())) {
                    contactEntity.setReferType(ReferTypeEnum.CUSTOMER.getValue());
                }
                if ("销售/采购".equals(contactExcelDTO.getSalesType())) {
                    contactEntity.setReferType(ReferTypeEnum.CUSTOMER_AND_SUPPLIER.getValue());
                }

                contactDao.saveOrUpdate(contactEntity);
                //删除通知人工厂信息
                contactFactoryService.deleteContactFactoryByContactId(contactEntity.getId());

                //根据工厂编号查询出工厂信息
                String[] split = contactExcelDTO.getFactoryCode().split(",");
                for (int i = 0; i < split.length; i++) {
                    FactoryEntity factoryEntity = factoryService.getFactoryByCode(split[i]);
                    log.info("===========================================工厂名称{}", split[i]);
                    contactFactoryService.saveContactFactoryEntity(new ContactFactoryEntity()
                            .setContactId(contactEntity.getId())
                            .setFactoryId(factoryEntity.getId())
                            .setFactoryCode(factoryEntity.getCode()));
                }
            } else {
                customerCodeList.add(contactExcelDTO.getLinkageCustomerCode());
            }

        }
        log.info("==========未成功处理的客户账号:{}" + JSON.toJSONString(customerCodeList));
        return null;
    }


    @Override
    public Boolean contactFactoryList(List<CustomerTemplateDTO.ContactFactory> contactFactoryS, CustomerTemplateDTO customerTemplateDTO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(currentUserId));
        for (CustomerTemplateDTO.ContactFactory contactFactory : contactFactoryS) {

            contactFactoryService.deleteContactFactoryEntityByContactId(contactFactory.getContactId());

            if (null != contactFactory.getContactId()) {
                //修改客户联系人信息
                ContactEntity contactEntity = contactDao.getContactEntityById(contactFactory.getContactId());
                contactEntity.setContactName(contactFactory.getContactName())
                        .setAddress(contactFactory.getAddress())
                        .setEmail(contactFactory.getEmail())
                        .setReferType(contactFactory.getReferType())
                        .setContactPhone(contactFactory.getContactPhone())
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setUpdatedBy(Integer.parseInt(currentUserId))
                        .setUpdatedAt(new Date())
                        .setUpdatedByName(employEntity.getName());
                contactDao.updateContactEntity(contactEntity);

                //客户适用油厂信息
                List<CustomerTemplateDTO.ApplyFactory> applyFactories = contactFactory.getApplyFactories();
                contactFactoryService.deleteContactFactoryByContactId(contactFactory.getContactId());
                for (CustomerTemplateDTO.ApplyFactory applyFactory : applyFactories) {


                    //查询客户和工厂是否存在关系
                    List<ContactFactoryEntity> contactFactoryEntities = contactFactoryService.getContactFactoryByContactId(contactFactory.getContactId(), applyFactory.getFactoryId());

                    if (contactFactoryEntities.isEmpty()) {
                        ContactFactoryEntity contactFactoryEntity = new ContactFactoryEntity()
                                .setCustomerId(customerTemplateDTO.getCustomerId())
                                .setContactId(contactFactory.getContactId())
                                .setFactoryId(applyFactory.getFactoryId());
                        //为空新增
                        contactFactoryService.saveContactFactoryEntity(contactFactoryEntity);
                    } else {
                        //不为空修改
                        for (ContactFactoryEntity contactFactoryEntity : contactFactoryEntities) {

                            contactFactoryEntity.setContactId(contactFactory.getContactId())
                                    .setFactoryId(applyFactory.getFactoryId())
                                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                            contactFactoryService.updateContactFactoryEntity(contactFactoryEntity);
                        }
                    }
                }
            } else {
                //添加联系人信息

                ContactEntity contactEntity = BeanConvertUtils.convert(ContactEntity.class, contactFactory);
                contactEntity
                        .setCategoryId(customerTemplateDTO.getCategoryId())
                        .setAddress(contactFactory.getAddress())
                        .setReferType(contactFactory.getReferType())
                        .setReferId(customerTemplateDTO.getCustomerId().toString())
                        .setReferType(contactFactory.getReferType())
                        .setCreatedBy(Integer.parseInt(currentUserId))
                        .setCreatedByName(employEntity.getName())
                        .setUpdatedBy(Integer.parseInt(currentUserId))
                        .setUpdatedByName(employEntity.getName())
                ;
                contactDao.vaseContactEntity(contactEntity);

                List<CustomerTemplateDTO.ApplyFactory> applyFactories = contactFactory.getApplyFactories();
                for (CustomerTemplateDTO.ApplyFactory applyFactory : applyFactories) {
                    //保存联系人工厂信息
                    ContactFactoryEntity contactFactoryEntity = new ContactFactoryEntity()
                            .setCustomerId(customerTemplateDTO.getCustomerId())
                            .setContactId(contactEntity.getId())
                            .setFactoryId(applyFactory.getFactoryId());
                    //为空新增
                    contactFactoryService.saveContactFactoryEntity(contactFactoryEntity);
                }

            }

        }
        return true;
    }

    @Override
    public void contactAddress() {
        List<ContactEntity> contactEntities = contactDao.list();
        Integer num = 0;
        for (ContactEntity contactEntity : contactEntities) {

            CustomerEntity customerEntity = customerDao.getById(contactEntity.getReferId());

            if (null != customerEntity) {
                contactDao.updateById(contactEntity.setAddress(customerEntity.getAddress()));
                num++;
            }


        }

        log.info("================成功条数{}", num);


    }

}
