package com.navigator.customer.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.service.ICustomerBankService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/30 18:48
 */
@RestController
public class CustomerBankFacadeImpl implements CustomerBankFacade {
    @Resource
    private ICustomerBankService customerBankService;

    @Override
    public Result queryBankByCustomerId(CustomerBankDTO customerBankDTO) {
        return Result.success(customerBankService.queryBankByCustomerId(customerBankDTO));
    }


    @Override
    public Result queryCustomerBankByCustomerId(Integer customerId, Integer useType) {
        return Result.success(customerBankService.queryCustomerBankByCustomerId(customerId, null, useType));
    }

    @Override
    public Result queryCustomerBankFactory(CustomerBankFactoryDTO customerBankFactoryDTO) {
        return Result.success(customerBankService.queryCustomerBankFactory(customerBankFactoryDTO));
    }

    @Override
    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo) {
        return customerBankService.queryBankByBankAccountNo(bankAccountNo);
    }

    @Override
    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo, Integer categoryId) {
        return customerBankService.queryBankByBankAccountNo(bankAccountNo,categoryId);
    }

    @Override
    public Result deleteCustomerBankById(Integer id) {
        return Result.success(customerBankService.deleteCustomerBankById(id));
    }

    @Override
    public Result updateCustomerBank(CustomerBankDTO customerBankDTO) {
        return Result.success(customerBankService.updateCustomerBank(customerBankDTO));
    }

    @Override
    public Result saveCustomerBank(CustomerBankDTO customerBankDTO) {
        return Result.success(customerBankService.saveCustomerBank(customerBankDTO));
    }

    @Override
    public Result redactCustomerBank(List<CustomerBankDTO> customerBankDTO) {
        return Result.success(customerBankService.redactCustomerBank(customerBankDTO));
    }

    @Override
    public CustomerBankEntity queryCustomerBankById(Integer id) {
        return customerBankService.queryCustomerBankById(id);
    }

    @Override
    public Result leadCustomerBank(MultipartFile file){
        return customerBankService.leadCustomerBank(file);
    }

}
