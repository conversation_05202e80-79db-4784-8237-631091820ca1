package com.navigator.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.dao.CustomerDeliveryWhiteDao;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.vo.CustomerDeliveryWhiteFileReturnVO;
import com.navigator.customer.service.CustomerDeliveryWhiteService;
import com.navigator.customer.service.ICustomerService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;

import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@Slf4j
@Service
public class CustomerDeliveryWhiteServiceImpl implements CustomerDeliveryWhiteService {


    @Resource
    private CustomerDeliveryWhiteDao customerDeliveryWhiteDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;
    @Resource
    private ICustomerService customerService;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private OperationLogFacade operationLogFacade;


    @Override
    public List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhite(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        List<CustomerDeliveryWhiteEntity> customerDeliveryWhiteEntities = customerDeliveryWhiteDao.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);

        if (customerDeliveryWhiteEntities.isEmpty()) {
            return null;
        }

        List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOS = new ArrayList<>();

        for (CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity : customerDeliveryWhiteEntities) {
            CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO1 = BeanConvertUtils.convert(CustomerDeliveryWhiteDTO.class, customerDeliveryWhiteEntity);

            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(customerDeliveryWhiteEntity.getCategory2().split(","))
                    .map(String::trim)
                    .filter(category2 -> !category2.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if(null != categoryEntity){
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }
            customerDeliveryWhiteDTO1.setCategory2Name(category2Name.toString());

            StringBuilder category3Name = new StringBuilder();
            if (StringUtils.isNotEmpty(customerDeliveryWhiteEntity.getCategory3())) {
                List<Integer> category3List = Arrays.stream(customerDeliveryWhiteEntity.getCategory3().split(","))
                        .map(String::trim)
                        .filter(category3 -> !category3.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                for (Integer category3 : category3List) {
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                    if(null != categoryEntity){
                        category3Name.append(categoryEntity.getName());
                        //判断是否是最后一个
                        if (!category3.equals(category3List.get(category3List.size() - 1))) {
                            category3Name.append(",");
                        }
                    }
                }
                Map<Integer, List<Integer>> map = categoryDisposeMap.getCategoryMap(category2List, category3List);
                customerDeliveryWhiteDTO1.setCategoryMap(map);
                customerDeliveryWhiteDTO1.setCategory3Name(category3Name.toString());
            }

            customerDeliveryWhiteDTO1
                    .setCategory1(customerDeliveryWhiteEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerDeliveryWhiteEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerDeliveryWhiteEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;
            customerDeliveryWhiteDTOS.add(customerDeliveryWhiteDTO1);
        }


        return customerDeliveryWhiteDTOS;
    }

    @Override
    public List<CustomerDeliveryWhiteDTO> queryCustomerDeliveryWhiteList(CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        List<CategoryEntity> categoryEntities = categoryFacade.queryCategoryList(new CategoryQO()
                .setParentSerialNo(Integer.parseInt(customerDeliveryWhiteDTO.getCategory1()))
                .setLevel(2)
        );
        if (null == categoryEntities) {
            throw new BusinessException("未查询到二级品类");
        }

        List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOS = new ArrayList<>();
        for (CategoryEntity categoryEntity : categoryEntities) {

            CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO1 = new CustomerDeliveryWhiteDTO();


            customerDeliveryWhiteDTO.setCategory2(String.valueOf(categoryEntity.getSerialNo()));
            List<CustomerDeliveryWhiteEntity> customerDeliveryWhiteEntities = customerDeliveryWhiteDao.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);

            List<Integer> category3List = new ArrayList<>();

            if (!customerDeliveryWhiteEntities.isEmpty()) {
                CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity = customerDeliveryWhiteEntities.get(0);
                StringBuilder category3Name = new StringBuilder();
                if (!StringUtils.isEmpty(customerDeliveryWhiteEntity.getCategory3())) {
                    category3List = Arrays.stream(customerDeliveryWhiteEntity.getCategory3().split(","))
                            .map(String::trim)
                            .filter(category3 -> !category3.isEmpty())
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                    for (Integer category3 : category3List) {
                        CategoryEntity categoryEntity3 = categoryFacade.getBasicCategoryBySerialNo(category3);
                        category3Name.append(categoryEntity3.getName());
                        //判断是否是最后一个
                        if (!category3.equals(category3List.get(category3List.size() - 1))) {
                            category3Name.append(",");
                        }
                    }
                }
                customerDeliveryWhiteDTO1 = BeanConvertUtils.convert(CustomerDeliveryWhiteDTO.class, customerDeliveryWhiteEntity);
                customerDeliveryWhiteDTO1.setCategory3Name(category3Name.toString());
            } else {
                customerDeliveryWhiteDTO1
                        .setCustomerId(customerDeliveryWhiteDTO.getCustomerId())
                        .setCategory1(customerDeliveryWhiteDTO.getCategory1())
                        .setCategory2(String.valueOf(categoryEntity.getSerialNo()))
                ;
            }


            Map<Integer, List<Integer>> map = categoryDisposeMap.getCategoryMap(Collections.singletonList(categoryEntity.getSerialNo()), category3List);
            customerDeliveryWhiteDTO1
                    .setCategory2Name(categoryEntity.getName())
                    .setCategoryMap(map);

            customerDeliveryWhiteDTOS.add(customerDeliveryWhiteDTO1);

        }
        return customerDeliveryWhiteDTOS;
    }

    @Override
    public CustomerDeliveryWhiteEntity queryCustomerDeliveryWhiteById(Integer id) {
        return customerDeliveryWhiteDao.getById(id);
    }

    @Override
    public boolean saveOrUpdateCustomerDeliveryWhite(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        if (null != customerDeliveryWhiteEntity.getId()) {
            customerDeliveryWhiteEntity
                    .setCreatedBy(name)
                    .setCreatedAt(new Date())
            ;
        }
        customerDeliveryWhiteEntity
                .setStatus(!StringUtils.isEmpty(customerDeliveryWhiteEntity.getCategory3()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue())
                .setUpdatedBy(name)
                .setUpdatedAt(new Date())
        ;
        return customerDeliveryWhiteDao.saveOrUpdate(customerDeliveryWhiteEntity);
    }

    @Override
    public List<CustomerDeliveryWhiteExcelDTO> downloadCustomerWhitelist(CustomerDTO customerDTO) {
        List<CustomerEntity> customerEntities = new ArrayList<>();
        List<Integer> customerIdList = null;
        if (StringUtils.isNotEmpty(customerDTO.getName())
                || StringUtils.isNotEmpty(customerDTO.getLinkageCustomerCode())
                || StringUtils.isNotEmpty(customerDTO.getUpdatedByName())) {
            customerEntities = customerService.queryCustomerEntityList(customerDTO);
            if (!CollectionUtils.isEmpty(customerEntities)) {
                customerIdList = customerEntities.stream().map(CustomerEntity::getId).collect(Collectors.toList());
            }
        }

        List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteEntities = queryCustomerDeliveryWhite(new CustomerDeliveryWhiteDTO()
                .setCustomerIdList(customerIdList)
        );

        if (CollectionUtils.isEmpty(customerDeliveryWhiteEntities)) {
            return null;
        }

        return customerDeliveryWhiteEntities.stream().map(customerDeliveryWhiteEntity -> {
            CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO = new CustomerDeliveryWhiteExcelDTO();

            CustomerEntity customerEntity = customerService.queryCustomerById(customerDeliveryWhiteEntity.getCustomerId());
            customerDeliveryWhiteExcelDTO
                    .setCustomerCode(customerEntity.getLinkageCustomerCode())
                    .setCustomerName(customerEntity.getName())
                    .setCategory2Name(customerDeliveryWhiteEntity.getCategory2Name())
                    .setCategory3Name(customerDeliveryWhiteEntity.getCategory3Name())
                    .setDeliveryWhiteStatus(customerDeliveryWhiteEntity.getStatus() == 1 ? "是" : "否")
            ;
            return customerDeliveryWhiteExcelDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerDeliveryWhiteFileReturnVO checkCustomerWhitelist(MultipartFile file) {
        List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerDeliveryWhiteExcelDTO.class);
        CustomerDeliveryWhiteFileReturnVO customerDeliveryWhiteFileReturnVO = new CustomerDeliveryWhiteFileReturnVO();

        Integer successNum = 0;
        Integer failNum = 0;
        Integer totalNum = 0;

        List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteDTOList = new ArrayList<>();

        Map<String, List<IndexedDTO>> duplicatesMap = new HashMap<>();
        CustomerDeliveryWhiteFileReturnVO vo = new CustomerDeliveryWhiteFileReturnVO();
        List<CustomerDeliveryWhiteExcelDTO> list = new ArrayList<>();
        for (int i = 0; i < customerDeliveryWhiteExcelDTOS.size(); i++) {
            CustomerDeliveryWhiteExcelDTO dto = customerDeliveryWhiteExcelDTOS.get(i);
            if (allFieldsAreNull(dto)) {
                continue; // 跳过所有字段都为null的对象
            }

            if (StringUtils.isEmpty(dto.getCustomerCode())
                    || StringUtils.isEmpty(dto.getCustomerName())
                    || StringUtils.isEmpty(dto.getCategory2Name())
                    || StringUtils.isEmpty(dto.getCategory3Name())
                    || StringUtils.isEmpty(dto.getDeliveryWhiteStatus())
            ) {
                dto.setFailReason("数据不完整");
                list.add(dto);
                continue;
            }

            String key = dto.getCustomerName().trim() + "|" +
                    dto.getCustomerCode().trim() + "|" +
                    dto.getCategory2Name().trim() + "|" +
                    dto.getCategory3Name().trim();

            duplicatesMap.computeIfAbsent(key, k -> new ArrayList<>()).add(new IndexedDTO(i, dto));
        }

        if(!CollectionUtils.isEmpty(list)){
            return vo.setCustomerDeliveryWhiteDTOList(list);
        }

        // 遍历Map，找出包含多个DTO的条目（即存在重复）
        for (Map.Entry<String, List<IndexedDTO>> entry : duplicatesMap.entrySet()) {
            List<IndexedDTO> duplicates = entry.getValue();
            if (duplicates.size() > 1) {
                log.info("发现重复项，键为: " + entry.getKey());
                for (IndexedDTO indexedDTO : duplicates) {
                    CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO = new CustomerDeliveryWhiteExcelDTO();
                    customerDeliveryWhiteExcelDTO.setCustomerCode(indexedDTO.dto.getCustomerCode())
                            .setCustomerName(indexedDTO.dto.getCustomerName())
                            .setCategory2Name(indexedDTO.dto.getCategory2Name())
                            .setCategory3Name(indexedDTO.dto.getCategory3Name())
                            .setDeliveryWhiteStatus(indexedDTO.dto.getDeliveryWhiteStatus())
                            .setIndex(indexedDTO.index + 1)
                            .setFailReason("数据重复")
                    ;
                    customerDeliveryWhiteDTOList.add(customerDeliveryWhiteExcelDTO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(customerDeliveryWhiteDTOList)) {
            return customerDeliveryWhiteFileReturnVO
                    .setIsRepeat(true)
                    .setTotalNum(totalNum)
                    .setFailNum(failNum)
                    .setSuccessNum(successNum)
                    .setCustomerDeliveryWhiteDTOList(customerDeliveryWhiteDTOList)
                    ;
        }


        List<CategoryEntity> categoryEntities2 = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> categoryEntities3 = categoryFacade.getAllCategoryList(3);

        for (CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO : customerDeliveryWhiteExcelDTOS) {
            //判断warrantExcelDTO对象中的值是否全部为空
            if (customerDeliveryWhiteExcelDTO.isAllEqual()) {
                continue;
            }

            totalNum++;

            CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO1 = customerDeliveryWhiteCheck(customerDeliveryWhiteExcelDTO, categoryEntities2, categoryEntities3);

            if (null != customerDeliveryWhiteExcelDTO1) {
                failNum++;
                customerDeliveryWhiteExcelDTO1.setIndex(totalNum);
                customerDeliveryWhiteDTOList.add(customerDeliveryWhiteExcelDTO1);
            } else {
                successNum++;
            }
        }
        return customerDeliveryWhiteFileReturnVO
                .setTotalNum(totalNum)
                .setFailNum(failNum)
                .setSuccessNum(successNum)
                .setCustomerDeliveryWhiteDTOList(customerDeliveryWhiteDTOList)
                ;
    }

    // 静态内部类，用于存储索引和DTO对象
    private class IndexedDTO {
        int index;
        CustomerDeliveryWhiteExcelDTO dto;

        IndexedDTO(int index, CustomerDeliveryWhiteExcelDTO dto) {
            this.index = index;
            this.dto = dto;
        }
    }

    private static boolean allFieldsAreNull(CustomerDeliveryWhiteExcelDTO dto) {
        return dto.getCustomerName() == null &&
                dto.getCustomerCode() == null &&
                dto.getCategory2Name() == null &&
                dto.getCategory3Name() == null;
    }

    @Override
    public CustomerDeliveryWhiteFileReturnVO uploadCustomerWhitelist(MultipartFile file) {
        List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerDeliveryWhiteExcelDTO.class);
        CustomerDeliveryWhiteFileReturnVO customerDeliveryWhiteFileReturnVO = new CustomerDeliveryWhiteFileReturnVO();
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));

        Integer successNum = 0;
        Integer failNum = 0;
        Integer totalNum = 0;

        List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteDTOList = new ArrayList<>();
        List<CategoryEntity> categoryEntities2 = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> categoryEntities3 = categoryFacade.getAllCategoryList(3);
        for (CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO : customerDeliveryWhiteExcelDTOS) {
            //判断warrantExcelDTO对象中的值是否全部为空
            if (customerDeliveryWhiteExcelDTO.isAllEqual()) {
                continue;
            }

            totalNum++;

            CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO1 = customerDeliveryWhiteCheck(customerDeliveryWhiteExcelDTO, categoryEntities2, categoryEntities3);

            if (null != customerDeliveryWhiteExcelDTO1) {
                failNum++;
            } else {

                //查询客户主数据
                CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(customerDeliveryWhiteExcelDTO.getCustomerCode().trim());
                //获取二级品种信息
                CategoryEntity categoryEntity2 = categoryEntities2.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory2Name().trim())).findFirst().orElse(null);
                //获取三级品种信息
                CategoryEntity categoryEntity3 = categoryEntities3.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory3Name().trim())).findFirst().orElse(null);
                Integer category3Id = categoryEntity3.getId();
                CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO = new CustomerDeliveryWhiteDTO();
                customerDeliveryWhiteDTO.setCustomerId(customerEntity.getId());
                customerDeliveryWhiteDTO.setCategory2(String.valueOf(categoryEntity2.getId()));
                CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity = new CustomerDeliveryWhiteEntity();
                //根据客户及品种信息
                List<CustomerDeliveryWhiteEntity> customerDeliveryWhite = customerDeliveryWhiteDao.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
                if (!CollectionUtils.isEmpty(customerDeliveryWhite)) {
                    //不为空时编辑数据
                    customerDeliveryWhiteEntity = customerDeliveryWhite.get(0);

                    if (StringUtils.isNotEmpty(customerDeliveryWhiteEntity.getCategory3())) {
                        log.info("==============================================customerDeliveryWhiteEntity:{}", JSON.toJSONString(customerDeliveryWhiteEntity));
                        log.info("==============================================category3List:{}", customerDeliveryWhiteEntity.getCategory3());
                        List<Integer> category3List = Arrays.stream(customerDeliveryWhiteEntity.getCategory3().split(","))
                                .map(String::trim)
                                .filter(category3 -> !category3.isEmpty())
                                .map(Integer::parseInt)
                                .collect(Collectors.toList());
                        if (customerDeliveryWhiteExcelDTO.getDeliveryWhiteStatus().trim().equals("是")) {
                            //校验category3Id是否存在category3List中
                            if (!category3List.contains(category3Id)) {
                                //不存在则将category3Id添加到category3List中
                                category3List.add(category3Id);
                            }
                        } else {
                            //校验category3Id是否存在category3List中
                            if (category3List.contains(category3Id)) {
                                //存在则将category3Id删除
                                category3List.remove(category3Id);
                            }
                        }

                        //将category3List用,隔开
                        String category3 = category3List.stream().map(String::valueOf).collect(Collectors.joining(","));
                        customerDeliveryWhiteEntity
                                .setCategory3(',' + category3 + ',');
                    } else {
                        customerDeliveryWhiteEntity
                                .setCategory3(',' + String.valueOf(category3Id) + ',');
                    }
                    customerDeliveryWhiteEntity
                            .setStatus(!StringUtils.isEmpty(customerDeliveryWhiteEntity.getCategory3()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue())
                            .setUpdatedAt(new Date())
                            .setUpdatedBy(name)
                    ;
                    customerDeliveryWhiteDao.saveOrUpdate(customerDeliveryWhiteEntity);
                } else {
                    //为空时新增数据
                    customerDeliveryWhiteEntity
                            .setCategory1(String.valueOf(categoryEntity2.getParentId()))
                            .setCategory2(String.valueOf(categoryEntity2.getId()))
                            .setCategory3(String.valueOf(categoryEntity3.getId()))
                            .setCustomerId(customerEntity.getId())
                            .setStatus(customerDeliveryWhiteExcelDTO.getDeliveryWhiteStatus().trim().equals("是") ? 1 : 0)
                            .setCreatedAt(new Date())
                            .setCreatedBy(name)
                            .setUpdatedAt(new Date())
                            .setUpdatedBy(name)
                    ;
                    customerDeliveryWhiteDao.saveOrUpdate(customerDeliveryWhiteEntity);

                    //添加日志
                    recordOperationDetail(customerDeliveryWhiteEntity);
                }
                successNum++;
            }
        }
        return customerDeliveryWhiteFileReturnVO
                .setTotalNum(totalNum)
                .setFailNum(failNum)
                .setSuccessNum(successNum)
                .setCustomerDeliveryWhiteDTOList(customerDeliveryWhiteDTOList)
                ;
    }

    private void recordOperationDetail(CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity) {
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(customerDeliveryWhiteEntity))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_CUSTOMER_DELIVERY_WHITE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteCheck(CustomerDeliveryWhiteExcelDTO customerDeliveryWhiteExcelDTO,
                                                                     List<CategoryEntity> categoryEntities2,
                                                                     List<CategoryEntity> categoryEntities3
    ) {

        if (StringUtils.isEmpty(customerDeliveryWhiteExcelDTO.getCustomerCode())
                || StringUtils.isEmpty(customerDeliveryWhiteExcelDTO.getCustomerName())
                || StringUtils.isEmpty(customerDeliveryWhiteExcelDTO.getCategory2Name())
                || StringUtils.isEmpty(customerDeliveryWhiteExcelDTO.getCategory3Name())
                || StringUtils.isEmpty(customerDeliveryWhiteExcelDTO.getDeliveryWhiteStatus())
        ) {
            customerDeliveryWhiteExcelDTO.setFailReason("数据不完整");
            return customerDeliveryWhiteExcelDTO;
        }

        CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(customerDeliveryWhiteExcelDTO.getCustomerCode().trim());
        if (null == customerEntity) {
            customerDeliveryWhiteExcelDTO.setFailReason("根据客户编码未查询到客户数据");
            return customerDeliveryWhiteExcelDTO;
        }

        /*CategoryEntity categoryEntity2 = categoryEntities2.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory3Name())).findFirst().orElse(null);

        if (null == categoryEntity2) {
            customerDeliveryWhiteExcelDTO.setFailReason("根据二级品类名称:(" + customerDeliveryWhiteExcelDTO.getCategory1Name() + ")未查询到二级品类数据");
            return customerDeliveryWhiteExcelDTO;
        }

        CategoryEntity categoryEntity3 = categoryEntities3.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory3Name())).findFirst().orElse(null);

        if (null == categoryEntity3) {
            customerDeliveryWhiteExcelDTO.setFailReason("根据三级品类名称:(" + customerDeliveryWhiteExcelDTO.getCategory1Name() + ")未查询到三级品类数据");
            return customerDeliveryWhiteExcelDTO;
        }*/

/*        List<String> category2List = Arrays.stream(customerDeliveryWhiteExcelDTO.getCategory2Name().split(",")).collect(Collectors.toList());
        for (String category2 : category2List) {
            CategoryEntity category = categoryEntities2.stream().filter(categoryEntity2 -> categoryEntity2.getName().equals(category2)).findFirst().orElse(null);
            if (null == category) {
                customerDeliveryWhiteExcelDTO.setFailReason("根据二级品类名称:(" + category2 + ")未查询到二级品类数据");
                return customerDeliveryWhiteExcelDTO;
            }
        }*/

        //获取二级品种信息
        CategoryEntity categoryEntity2 = categoryEntities2.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory2Name().trim())).findFirst().orElse(null);
        if (null == categoryEntity2) {
            customerDeliveryWhiteExcelDTO.setFailReason("根据二级品类名称:(" + customerDeliveryWhiteExcelDTO.getCategory2Name() + ")未查询到二级品类数据");
            return customerDeliveryWhiteExcelDTO;
        }

        //获取三级品种信息
        CategoryEntity categoryEntity3 = categoryEntities3.stream().filter(i -> i.getName().equals(customerDeliveryWhiteExcelDTO.getCategory3Name().trim())).findFirst().orElse(null);
        if (null == categoryEntity3) {
            customerDeliveryWhiteExcelDTO.setFailReason("根据三级品类名称:(" + customerDeliveryWhiteExcelDTO.getCategory3Name() + ")未查询到三级品类数据");
            return customerDeliveryWhiteExcelDTO;
        }

        if (!categoryEntity3.getParentId().equals(categoryEntity2.getId())) {
            customerDeliveryWhiteExcelDTO.setFailReason("二级品类和三级品种不是绑定关系");
            return customerDeliveryWhiteExcelDTO;
        }

        if (!"是".equals(customerDeliveryWhiteExcelDTO.getDeliveryWhiteStatus()) && !"否".equals(customerDeliveryWhiteExcelDTO.getDeliveryWhiteStatus())) {
            customerDeliveryWhiteExcelDTO.setFailReason("提货白名单状态填写不正确");
            return customerDeliveryWhiteExcelDTO;
        }

        /*List<String> category3List = Arrays.stream(customerDeliveryWhiteExcelDTO.getCategory3Name().split(",")).collect(Collectors.toList());
        for (String category3 : category3List) {
            CategoryEntity category = categoryEntities3.stream().filter(categoryEntity3 -> categoryEntity3.getName().equals(category3)).findFirst().orElse(null);
            if (null == category) {
                customerDeliveryWhiteExcelDTO.setFailReason("根据三级品类名称:(" + category3 + ")未查询到三级品类数据");
                return customerDeliveryWhiteExcelDTO;
            }
        }*/

        return null;
    }
}
