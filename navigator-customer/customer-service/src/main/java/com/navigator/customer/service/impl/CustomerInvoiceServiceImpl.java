package com.navigator.customer.service.impl;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.dao.CustomerInvoiceDao;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.dto.file.CustomerInvoiceExcelDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import com.navigator.customer.service.CustomerInvoiceService;
import com.navigator.customer.service.ICustomerService;
import com.navigator.customer.service.utils.CategoryDisposeMap;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
@Service
public class CustomerInvoiceServiceImpl implements CustomerInvoiceService {

    @Resource
    private CustomerInvoiceDao customerInvoiceDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private CategoryDisposeMap categoryDisposeMap;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private ICustomerService customerService;
    @Resource
    private CompanyFacade companyFacade;

    public List<CustomerInvoiceEntity> queryCustomerInvoiceList(CustomerInvoiceDTO customerInvoiceDTO) {

        List<CustomerInvoiceEntity> customerInvoiceEntityList = customerInvoiceDao.queryCustomerInvoiceList(customerInvoiceDTO);
        customerInvoiceEntityList.forEach(customerInvoiceEntity -> {

            StringBuilder category2Name = new StringBuilder();
            List<Integer> category2List = Arrays.stream(customerInvoiceEntity.getCategory2().split(","))
                    .map(String::trim)
                    .filter(category2 -> !category2.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            for (Integer category2 : category2List) {
                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category2);
                if (null != categoryEntity) {
                    category2Name.append(categoryEntity.getName());
                    //判断是否是最后一个
                    if (!category2.equals(category2List.get(category2List.size() - 1))) {
                        category2Name.append(",");
                    }
                }
            }

            StringBuilder category3Name = new StringBuilder();
            List<Integer> category3List = new ArrayList<>();
            if (StringUtil.isNotEmpty(customerInvoiceEntity.getCategory3())) {
                category3List = Arrays.stream(customerInvoiceEntity.getCategory3().split(","))
                        .map(String::trim)
                        .filter(category3 -> !category3.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                for (Integer category3 : category3List) {
                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(category3);
                    if (null != categoryEntity) {
                        category3Name.append(categoryEntity.getName());
                        //判断是否是最后一个
                        if (!category3.equals(category3List.get(category3List.size() - 1))) {
                            category3Name.append(",");
                        }
                    }
                }
            }

            customerInvoiceEntity
                    .setCategory3Name(category3Name.toString())
                    .setCategory2Name(category2Name.toString())
                    .setUpdatedByName(customerInvoiceEntity.getUpdatedBy())
                    .setCategory1(customerInvoiceEntity.getCategory1().replaceAll("^,+|,+$", ""))
                    .setCategory2(customerInvoiceEntity.getCategory2().replaceAll("^,+|,+$", ""))
                    .setCategory3(customerInvoiceEntity.getCategory3().replaceAll("^,+|,+$", ""))
            ;
            customerInvoiceEntity.setCategoryMap(categoryDisposeMap.getCategoryMap(category2List, category3List));
        });

        return customerInvoiceEntityList;
    }

    @Override
    public boolean saveOrUpdateCustomerInvoice(CustomerInvoiceEntity customerInvoiceEntity) {
        return customerInvoiceDao.saveOrUpdate(customerInvoiceEntity);
    }


    /**
     * 刷客户发票ID
     */
    public void updateCustomerInvoiceId() {

        List<CustomerInvoiceEntity> customerInvoiceEntityList = customerInvoiceDao.queryCustomerInvoiceListByCategory(12);

        for (CustomerInvoiceEntity customerInvoiceEntity : customerInvoiceEntityList) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getInvoiceType(11, customerInvoiceEntity.getInvoiceType(), null);
            customerInvoiceEntity.setInvoiceId(systemRuleItemEntity.getId());
        }

    }

    @Override
    public CustomerInvoiceEntity queryCustomerInvoiceById(Integer id) {
        return customerInvoiceDao.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importCustomerInvoice(MultipartFile file) {

        List<CustomerInvoiceExcelDTO> customerWhiteExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, CustomerInvoiceExcelDTO.class);

        List<CategoryEntity> category1Entities = categoryFacade.getAllCategoryList(1);
        List<CategoryEntity> category2Entities = categoryFacade.getAllCategoryList(2);
        List<CategoryEntity> category3Entities = categoryFacade.getAllCategoryList(3);

        //查询发票配置信息
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO();
        systemRuleDTO
                .setRuleCode(SystemCodeConfigEnum.INVOICE_TYPE.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = systemRuleVO.getSystemRuleItemVOList();

        for (CustomerInvoiceExcelDTO customerWhiteExcelDTO : customerWhiteExcelDTOS) {

            CustomerEntity customerEntity = customerService.queryCustomerByLinkageCode(customerWhiteExcelDTO.getCustomerCode());
            //根据客户编码查询客户是否存在
            if (null == customerEntity) {
                continue;
            }
            //根据发票名称获取发票信息
            SystemRuleVO.SystemRuleItemVO systemRuleItemEntity = systemRuleItemVOList.stream()
                    .filter(item -> item.getMemo().equals(customerWhiteExcelDTO.getInvoiceType()))
                    .findFirst()
                    .orElse(null);
            if (null == systemRuleItemEntity) {
                continue;
            }
            //根据主体简称查询主体信息
            CompanyEntity companyEntity = companyFacade.getCompanyByCode(customerWhiteExcelDTO.getCompanyCode());
            if (null == companyEntity) {
                continue;
            }

            //查询货品数据
            List<String> category1List = Arrays.stream(customerWhiteExcelDTO.getCategory1().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category2List = Arrays.stream(customerWhiteExcelDTO.getCategory2().split(",")).map(String::trim).collect(Collectors.toList());
            List<String> category3List = Arrays.stream(customerWhiteExcelDTO.getCategory3().split(",")).map(String::trim).collect(Collectors.toList());

            //获取品种id
            String category1 = categoryDisposeMap.customerCategoryConfig(category1Entities, category1List);
            String category2 = categoryDisposeMap.customerCategoryConfig(category2Entities, category2List);
            String category3 = categoryDisposeMap.customerCategoryConfig(category3Entities, category3List);

            Integer id = null;

            //参数写入实体
            CustomerInvoiceEntity customerInvoiceEntity = new CustomerInvoiceEntity();
            CustomerInvoiceDTO customerInvoiceDTO = new CustomerInvoiceDTO();
            customerInvoiceDTO
                    .setCustomerId(customerEntity.getId())
                    .setInvoiceName(customerWhiteExcelDTO.getInvoiceType())
                    .setCategory2(category2.replaceAll("^,+|,+$", ""))
                    .setCompanyId(companyEntity.getId())
            ;
            List<CustomerInvoiceEntity> customerInvoiceEntityList = customerInvoiceDao.queryCustomerInvoiceListByCustomerId(customerInvoiceDTO);
            if (!CollectionUtils.isEmpty(customerInvoiceEntityList)) {
                customerInvoiceEntity.setId(customerInvoiceEntityList.get(0).getId());
            }

            if ("删除".equals(customerWhiteExcelDTO.getOperationType())) {
                customerInvoiceEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
            }else {
                customerInvoiceEntity.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
            }
            customerInvoiceEntity
                    .setInvoiceId(systemRuleItemEntity.getRuleItemId())
                    .setInvoiceName(systemRuleItemEntity.getMemo())
                    .setInvoiceType(Integer.parseInt(systemRuleItemEntity.getRuleItemKey()))
                    .setCategory1(category1)
                    .setCategory2(category2)
                    .setCategory3(category3)
                    .setCustomerId(customerEntity.getId())
                    .setCompanyId(companyEntity.getId())
                    .setCompanyName(companyEntity.getShortName())
                    .setCreatedBy("1")
                    .setCreatedAt(new Date())
                    .setUpdatedBy("1")
                    .setUpdatedAt(new Date())
            ;
            saveOrUpdateCustomerInvoice(customerInvoiceEntity);

        }

        return null;
    }

    @Override
    public List<CustomerInvoiceEntity> queryCustomerInvoiceListByCustomerId(CustomerInvoiceDTO customerInvoiceDTO) {
        return customerInvoiceDao.queryCustomerInvoiceListByCustomerId(customerInvoiceDTO);
    }
}
