package com.navigator.customer.service;


import com.navigator.customer.pojo.dto.FactoryCreateDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.vo.FactoryVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-28
 */
public interface IFactoryService {
    /**
     * 根据ID获取工厂信息
     *
     * @param id 工厂ID
     * @return 工厂信息
     */
    FactoryEntity getFactoryById(Integer id);

    /**
     * 查询所有工厂信息
     *
     * @return 工厂集合
     */
    List<FactoryEntity> getAllFactory(Integer status);

    /**
     * 根据油厂id查询油厂信息
     *
     * @param factoryId
     * @return
     */
    FactoryEntity getFactoryDetailById(Integer factoryId);

    FactoryEntity getFactoryByCode(String code);

    /**
     * 保存/更新工厂及库点信息
     *
     * @param factoryCreateDTO 工厂创建信息
     * @return 工厂保存结果
     */
    boolean saveOrUpdateFactory(FactoryCreateDTO factoryCreateDTO);

    /**
     * 更新工厂状态
     *
     * @param factoryId 工厂ID
     * @return 更新结果
     */
    boolean updateStatus(Integer factoryId);

    List<FactoryEntity> queryFactoryByCompanyId(Integer companyId);

    List<FactoryVO> queryFactoryList();

    void updateFactoryById(FactoryEntity factoryEntity);
}
