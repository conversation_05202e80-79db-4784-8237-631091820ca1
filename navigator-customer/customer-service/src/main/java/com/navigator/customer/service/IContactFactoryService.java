package com.navigator.customer.service;

import com.navigator.customer.pojo.entity.ContactFactoryEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 18:01
 */
public interface IContactFactoryService {

    /**
     * 根据客户联系人查询出联系人适用工厂
     *
     * @param contactId
     * @return
     */
    List<ContactFactoryEntity> queryContactFactoryByContactId(Integer contactId);


    /**
     * 删除油厂和客户的关系
     *
     * @param contactId
     * @return
     */
    boolean deleteContactFactoryByContactId(Integer contactId);

    /**
     * 查询客户联系人和工厂信息
     *
     * @param contactId
     * @param factoryId
     * @return
     */
    List<ContactFactoryEntity> getContactFactoryByContactId(Integer contactId, Integer factoryId);

    /**
     * 查询客户联系人和工厂信息
     *
     * @param customerId
     * @param factoryId
     * @return
     */
    List<ContactFactoryEntity> getContactFactoryByCustomerId(Integer customerId, Integer factoryId);


    /**
     * 新增联系人,适用油厂信息
     *
     * @param contactFactoryEntity
     * @return
     */
    boolean saveContactFactoryEntity(ContactFactoryEntity contactFactoryEntity);


    /**
     * 新增联系人,适用油厂信息
     *
     * @param contactFactoryEntity
     * @return
     */
    boolean updateContactFactoryEntity(ContactFactoryEntity contactFactoryEntity);


    /**
     * 删除联系人和油厂信息
     *
     * @param contactId
     * @return
     */
    boolean deleteContactFactoryEntityByContactId(Integer contactId);


    /**
     * 新增或更新联系人,适用油厂信息
     *
     * @param contactFactoryEntity
     * @return
     */
    boolean saveOrUpdateContactFactoryEntity(List<ContactFactoryEntity> contactFactoryEntity);

}
