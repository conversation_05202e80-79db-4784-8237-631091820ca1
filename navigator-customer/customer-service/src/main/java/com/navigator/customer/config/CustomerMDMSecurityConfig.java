//MDM安全认证 Date:20250313 By:wan Start
package com.navigator.customer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/30
 */

@Slf4j
@Component
@EnableWebSecurity
public class CustomerMDMSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${customer.basic-auth.type}")
    private String authType;

    @Value("${customer.basic-auth.userName}")
    private String basicUsername;

    @Value("${customer.basic-auth.password}")
    private String basicPassword;

    @Value("${customer.basic-auth.key}")
    private String apiKey;

    @Value("${customer.basic-auth.value}")
    private String apiValue;

    @Value("${customer.basic-auth.matchers}")
    private String apiMatchers;

    @Override
    protected void configure(HttpSecurity http) throws Exception {

        // 基础配置，允许访问 "/atlas/**" 的请求进行认证
        http.authorizeRequests()
                .antMatchers(apiMatchers).authenticated()
                .and()
                .csrf().disable();

        // 根据配置的认证类型来选择 Basic 认证或 API Key 认证
        if ("basic".equalsIgnoreCase(authType)) {
            log.info("Cuckoo Using Basic authentication");
            // 启用 HTTP Basic 认证
            http.httpBasic();
        } else if ("api_key".equalsIgnoreCase(authType)) {
            log.info("Cuckoo Using API Key authentication");
            // 启用 API Key 认证
            http.addFilterBefore(new ApiKeyAuthenticationFilter(apiKey, apiValue,apiMatchers), UsernamePasswordAuthenticationFilter.class);
        }
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        // 使用内存认证来验证用户
        auth.inMemoryAuthentication()
                .withUser(basicUsername)
                .password(passwordEncoder().encode(basicPassword))
                .roles("USER");
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
//MDM安全认证 Date:20250313 By:wan end