package com.navigator.customer.facade.impl;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerDepositRateFacade;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import com.navigator.customer.pojo.entity.CustomerDepositRateEntity;
import com.navigator.customer.service.ICustomerDepositRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 18:16
 */
@RestController
public class CustomerDepositRateFacadeImpl implements CustomerDepositRateFacade {

    @Resource
    private ICustomerDepositRateService customerDepositRateService;
    @Autowired
    private EmployFacade employFacade;

    @Override
    public Result getCustomerDepositRateByCustomerId(CustomerDepositRateBO customerDepositRateBO) {
        return Result.success(customerDepositRateService.getCustomerDepositRateByCustomerId(customerDepositRateBO));
    }

    @Override
    public Result getCustomerDepositRateAddTT(CustomerDepositRateBO customerDepositRateBO) {
        return Result.success(customerDepositRateService.getCustomerDepositRateAddTT(customerDepositRateBO));
    }

    @Override
    public CustomerDepositRateEntity getCustomerDepositRateById(Integer id) {
        return customerDepositRateService.getCustomerDepositRateById(id);
    }

    @Override
    public Result saveCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        return Result.success(customerDepositRateService.saveCustomerDepositRate(customerDepositRateDTO, currentUserId, name));
    }

    @Override
    public Result updateCustomerDepositRateStatus(Integer id) {
        return Result.success(customerDepositRateService.updateCustomerDepositRateStatus(id));
    }

    @Override
    public Result updateCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        CustomerDepositRateEntity customerDepositRateEntity = BeanConvertUtils.convert(CustomerDepositRateEntity.class,
                customerDepositRateService.getCustomerDepositRateById(customerDepositRateDTO.getId()));
        return Result.success(customerDepositRateService.updateCustomerDepositRate(customerDepositRateEntity));
    }

    @Override
    public Result deleteCustomerDepositRate(Integer customerDepositRateId) {
        return Result.success(customerDepositRateService.deleteCustomerDepositRate(customerDepositRateId));
    }

    @Override
    public Result redactCustomerDepositRate(List<CustomerDepositRateDTO> customerDepositRateDTO) {
        return Result.success(customerDepositRateService.redactCustomerDepositRate(customerDepositRateDTO));
    }

    @Override
    public Result addCustomerSalesDepositRate(MultipartFile file) {
        return customerDepositRateService.addCustomerSalesDepositRate(file);
    }

    @Override
    public List<CustomerDepositRateEntity> queryCustomerDepositRate(CustomerDepositRateDTO customerDepositRateDTO) {
        return customerDepositRateService.queryCustomerDepositRate(customerDepositRateDTO);
    }
}
