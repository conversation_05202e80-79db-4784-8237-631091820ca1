package com.navigator.customer.service.impl;

import com.navigator.customer.dao.CustomerDetailUpdateRecordDao;
import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import com.navigator.customer.pojo.entity.CustomerDetailUpdateRecordEntity;
import com.navigator.customer.service.CustomerDetailUpdateRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/13
 */
@Service
@Slf4j
public class CustomerDetailUpdateRecordServiceImpl implements CustomerDetailUpdateRecordService {

    @Resource
    private CustomerDetailUpdateRecordDao customerDetailUpdateRecordDao;

    @Override
    public CustomerDetailUpdateRecordEntity detailUpdateSelect(CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO) {
        return customerDetailUpdateRecordDao.detailUpdateSelect(customerDetailUpdateRecordDTO);
    }

    @Override
    public boolean saveCustomerDetailUpdateRecord(CustomerDetailUpdateRecordEntity customerDetailUpdateRecordEntity) {
        return customerDetailUpdateRecordDao.save(customerDetailUpdateRecordEntity);
    }

}
