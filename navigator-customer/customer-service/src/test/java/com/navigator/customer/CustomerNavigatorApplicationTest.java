package com.navigator.customer;

import org.junit.runner.RunWith;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/14 17:06
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CustomerNavigatorApplication.class)
@Transactional
@Rollback
public class CustomerNavigatorApplicationTest {

    public static void main(String[] args) {
        SpringApplication.run(CustomerNavigatorApplicationTest.class, args);
    }
}
