package com.navigator.customer.customerContact;

import com.alibaba.fastjson.JSON;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.service.ICustomerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/14 15:16
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class customerTest {

    @Autowired
    private ICustomerService customerService;

    public CustomerAllMessageDTO joint(CustomerAllMessageDTO customerAllMessageDTO){
        return customerAllMessageDTO.setFactoryId(1)
                .setCategoryId(11)
                .setCustomerId(5)
                .setFactoryCode("ZJG")
                .setSalesType(1);
    }

    @Test
    public void queryCustomerAllMessage(){
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
        customerAllMessageDTO = this.joint(customerAllMessageDTO);
        CustomerDTO customerDTO = customerService.queryCustomerAllMessage(customerAllMessageDTO);
        log.info("客户全部数据"+ JSON.toJSONString(customerDTO, true));
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
    }

    @Test
    public void queryCustomerContactAllMessage(){
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
        customerAllMessageDTO = this.joint(customerAllMessageDTO);
        CustomerDTO customerDTO = customerService.queryCustomerContactAllMessage(customerAllMessageDTO);
        log.info("客户联系人数据"+ JSON.toJSONString(customerDTO, true));
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
    }



}
