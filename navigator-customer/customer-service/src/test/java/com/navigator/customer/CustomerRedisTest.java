package com.navigator.customer;

import com.navigator.common.redis.RedisUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.service.ICustomerService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CustomerRedisTest {

    @Autowired
    private ICustomerService customerService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 测试redis插入数据
     *
     */
    @Test
    public void testInsertRedis() {
        long start = System.currentTimeMillis();
        CustomerEntity customerEntity = customerService.queryCustomerById(1);
        for (int i = 0; i < 1000; i++) {
            redisUtil.set("customer:customerName:" + customerEntity.getName() + i, customerEntity);
        }
        System.out.println("发号结束，耗时：" + (System.currentTimeMillis() - start) / 1000 + "s");
    }

    /**
     * 测试redis模糊查询数据
     *
     * @Result: 1000条数据大小1Kb左右，查询结束，耗时：16s
     */
    @Test
    public void testQueryRedis() {
        long start = System.currentTimeMillis();
        String key = "customer:customerName:天门粤海饲料有限公司99*";

        Set<String> keys = redisUtil.keys(key);

        if (CollectionUtils.isNotEmpty(keys)) {
            for (String k : keys) {
                System.out.println(redisUtil.get(k));
            }
        }
        System.out.println("查询结束，耗时：" + (System.currentTimeMillis() - start)  + "ms");
    }

    /**
     * 分页查询测试
     */
    @Test
    public void testPageQuery() {
        long start = System.currentTimeMillis();
        String key = "customer:customerName:天门粤海饲料有限公司*";
        Set<String> keys = redisUtil.keys(key);

        if (CollectionUtils.isNotEmpty(keys)) {
            for (String k : keys) {
                System.out.println(redisUtil.zSetGetByPage(k, 1, 10));
            }
        }
        System.out.println("查询结束，耗时：" + (System.currentTimeMillis() - start)  + "ms");
    }

}
