package com.navigator.admin.facade.magellan;

import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface EmployPermissionFacade {

    @GetMapping("/magellan/getEmployRoleListByRoleIds")
    Result getEmployRoleListByRoleIds(@RequestParam("roleIdList")List<Integer> roleIdList);

    /**
     * employ和role进行绑定
     * jason
     * @param employRoleDTO
     * @return
     */
    @PostMapping("/magellan/addEmployRole")
    void addEmployRole(@RequestBody EmployRoleDTO employRoleDTO);


    @GetMapping("/magellan/addEmployRoles")
    void addEmployRoles(@RequestParam("employId") Integer employId,@RequestParam("roleIds") String roleIds,@RequestParam("roleDefId") Integer roleDefId,@RequestParam("categoryId") Integer categoryId,@RequestParam("customerIds") String customerIds);

    @PostMapping("/magellan/deleteEmployRole")
    void deleteEmployRole(@RequestBody EmployRoleDTO employRoleDTO);


}
