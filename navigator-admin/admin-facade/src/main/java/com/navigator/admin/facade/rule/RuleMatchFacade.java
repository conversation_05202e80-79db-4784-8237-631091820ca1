package com.navigator.admin.facade.rule;

import com.navigator.admin.pojo.dto.rule.RuleMatchDTO;
import com.navigator.admin.pojo.dto.rule.RuleReferInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 14:09
 **/
@FeignClient(value = "navigator-admin-service")
public interface RuleMatchFacade {

    /**
     * 规则引擎执行规则脚本，返回命中结果集
     * @param ruleMatchDTO
     * @return
     */
    @PostMapping("/matchBusinessRule")
    List<RuleReferInfoDTO> matchBusinessRule(@RequestBody RuleMatchDTO ruleMatchDTO);
}
