package com.navigator.admin.pojo.dto;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色授权菜单数据对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("角色授权菜单数据对象")
public class RoleAuthMenuDTO {
    @ApiModelProperty(value = "菜单列表，见返回示例",position = 1)
    private List<Tree<Integer>> menuList;
    @ApiModelProperty(value = "授权ID集合",position = 2)
    private List<Integer> authIdList;
}
