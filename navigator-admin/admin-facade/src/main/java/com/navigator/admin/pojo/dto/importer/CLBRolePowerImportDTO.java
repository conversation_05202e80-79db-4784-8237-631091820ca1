package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CLBRolePowerImportDTO {

    @Excel(name = "一级操作权限")
    private String firstPower;

    @Excel(name = "二级操作权限")
    private String secondPower;

    @Excel(name = "三级操作权限ID")
    private String powerId;

    @Excel(name = "三级操作权限")
    private String powerName;

    @Excel(name = "虚角色ID")
    private String roleDefId;

    @Excel(name = "虚角色")
    private String roleDefName;

    @Excel(name = "品类")
    private String category;

    @Excel(name = "采销")
    private String salesType;

    @Excel(name = "操作")
    private String operation = "";

}
