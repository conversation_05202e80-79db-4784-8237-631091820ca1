package com.navigator.admin.facade.columbus;

import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/3 13:45
 */
@FeignClient(value = "navigator-admin-service")
public interface CMenuFacade {
    /**
     * v1.0 菜单按钮权限独立设置
     */

    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/columbus/getMenusByEmploy")
    @Deprecated
    Result getMenusByEmploy(@RequestBody CMenuDTO menuDTO);


    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/columbus/getMenusByCondition")
    @Deprecated
    Result getMenusByCondition(@RequestBody CMenuDTO menuDTO);

    @PostMapping("/columbus/getMenuByRoleId")
    @Deprecated
    Result getMenuByRoleId(@RequestBody CRoleDTO roleDTO);

    @PostMapping("/columbus/getMenuByEmployId")
    @Deprecated
    Result getMenuByEmployId(@RequestBody CRoleDTO roleDTO);

    @PostMapping("/columbus/saveRoleMenu")
    @Deprecated
    Result saveRoleMenu(@RequestBody CMenuDTO menuDTO);

    /**
     * v2.0 统一菜单按钮权限
     */


    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/columbus/getMenusByEmployV2")
    Result getMenusByEmployV2(@RequestBody CMenuDTO menuDTO);


    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/columbus/getMenusByConditionV2")
    Result getMenusByConditionV2(@RequestBody CMenuDTO menuDTO);

    @PostMapping("/columbus/getMenuByRoleIdV2")
    Result getMenuByRoleIdV2(@RequestBody CRoleDTO roleDTO);

    @PostMapping("/columbus/getMenuByEmployIdV2")
    Result getMenuByEmployIdV2(@RequestBody CRoleDTO roleDTO);

    @PostMapping("/columbus/saveRoleMenuV2")
    Result saveRoleMenuV2(@RequestBody CMenuDTO menuDTO);

    @PostMapping("/columbus/addRoleMenu")
    Result addRoleMenu(@RequestBody CMenuDTO menuDTO);
}
