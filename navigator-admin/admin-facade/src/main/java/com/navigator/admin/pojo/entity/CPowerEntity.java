package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * columbus权限按钮表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_c_powerV2")
@ApiModel(value = "CPowerEntity对象", description = "columbus权限按钮表")
public class CPowerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "权限类编码")
    private String preCode;

    @ApiModelProperty(value = "权限编码")
    private String code;

    @ApiModelProperty(value = "权限名称")
    private String name;

    @ApiModelProperty(value = "权限描述")
    private String describe;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "层级;(0 1,2)")
    private Integer level;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除; 0:启用 1:禁用")
    private Integer isDeleted;

    @TableField(exist = false)
    @ApiModelProperty(value = "子菜单")
    private List<CPowerEntity> children;

    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<CPowerEntity> lqw(PowerQO condition) {
        LambdaQueryWrapper<CPowerEntity> lqw = new LambdaQueryWrapper<CPowerEntity>().eq(CPowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsCategory()), CPowerEntity::getIsCategory, condition.getIsCategory());
            lqw.in(CollUtil.isNotEmpty(condition.getPowerIdList()), CPowerEntity::getId, condition.getPowerIdList());
            lqw.isNotNull(StringUtil.isNotNullBlank(condition.getIsCodeNotNull()) && condition.getIsCodeNotNull() == 1, CPowerEntity::getCode);
        }
        return lqw;
    }
}
