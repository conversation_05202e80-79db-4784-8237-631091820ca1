package com.navigator.admin.pojo.vo.systemrule;

import lombok.Data;

@Data
public class CustomerRuleVO {
    /**
     * 交易属性（1、payment term 2、CBT 3、NTP 4、active）
     */
    private Integer tradeType;

    /**
     * 框架协议过期
     */
    private Integer frameExpired;

    /**
     * 客户模板协议过期
     */
    private Integer templateExpired;

    /**
     * 赊销账期
     */
    private Integer creditDays;

    /**
     * 付款方式:有赊销天数则为赊销 1 ；无赊销天数为预付款 2
     */
    private Integer paymentType;

    /**
     * 客户是否需要正本
     */
    private Integer originalPaper;

    /**
     * 正本需求(编辑文本)
     */
    private Integer paperNeed;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 是否是ldc模板
     */
    private Integer ldcFrame;

    /**
     * 是否使用易企签
     */
    private Integer useYqq;






}
