package com.navigator.admin.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class MenuDetailVO {
    @ApiModelProperty(value = "品类id")
    private Integer categoryId;
    @ApiModelProperty(value = "品种")
    private String categoryName;
    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "菜单编码")
    private String code;
    @ApiModelProperty(value = "父菜单编码")
    private String parentCode;
    @ApiModelProperty(value = "菜单名称")
    private String name;
    @ApiModelProperty(value = "父菜单id")
    private Integer parentId;
    @ApiModelProperty(value = "图标地址")
    private String icon;
    @ApiModelProperty(value = "路径")
    private String url;
    @ApiModelProperty(value = "子菜单")
    private List<MenuDetailVO> children;

    private Integer level;

    @ApiModelProperty(value = "是否已被收藏过（0否; 1是）")
    private Boolean hasCollected;
}
