package com.navigator.admin.pojo.dto.systemrule;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-07 15:10
 **/
@Data
@Accessors(chain = true)
public class PackageWeightImportDTO {

    @Excel(name = "二级品类", orderNum = "1", width = 8)
    private String category2Name;

    @Excel(name = "包装", orderNum = "2", width = 8)
    private String packageName;

    @Excel(name = "袋皮扣重", orderNum = "6", width = 25)
    private String ruleKey;

    @Excel(name = "状态", orderNum = "3", width = 4)
    private Integer status;

    @Excel(name = "对应协议文本", orderNum = "7", width = 25)
    private String memo;

    @Excel(name = "更新/删除/新增", orderNum = "9", width = 20)
    private String operationName;
}
