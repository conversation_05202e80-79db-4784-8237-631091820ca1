package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 登录日志Facade接口
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@FeignClient(value = "navigator-admin-service")
public interface LoginLogFacade {

    /**
     * 保存登录日志
     *
     * @param loginLogDTO 登录日志信息
     * @return 保存结果
     */
    @PostMapping("/loginLog/save")
    Result saveLoginLog(@RequestBody LoginLogDTO loginLogDTO);
}
