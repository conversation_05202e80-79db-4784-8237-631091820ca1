package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AnnouncementStatusEnum {
    /**
     *
     */
    SKETCH(1, "草稿"),
    PUBLISH(2, "发布"),
    CANCEL(3, "取消发布");
    Integer value;
    String description;

    public static AnnouncementStatusEnum getByValue(Integer value) {
        for (AnnouncementStatusEnum announcementStatusEnum : AnnouncementStatusEnum.values()) {
            if (value.equals(announcementStatusEnum.getValue())) {
                return announcementStatusEnum;
            }
        }
        return AnnouncementStatusEnum.SKETCH;
    }
}
