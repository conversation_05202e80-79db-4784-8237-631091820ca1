package com.navigator.admin.pojo.entity.rule;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 17:53
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbr_business_rule_detail")
@ApiModel(value = "BusinessRuleDetailEntity对象", description = "")
public class BusinessRuleDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "业务编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.RuleReferTypeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private String referType;

    /**
     * 条件变量ID
     */
    @ApiModelProperty(value = "条件变量ID")
    private Integer ruleConditionId;

    @ApiModelProperty(value = "加载条件阈值描述")
    private String conditionValueInfo;

    @ApiModelProperty(value = "加载规则信息（drools脚本）")
    private String ruleInfo;

    @ApiModelProperty(value = "加载规则信息描述")
    private String ruleDesc;

    @ApiModelProperty(value = "逻辑关系(&&、||)")
    private String logicRelation;

    @ApiModelProperty(value = "优先级")
    private Integer level;

    @ApiModelProperty(value = "先后顺序")
    private Integer sort;

    /**
     * {@link com.navigator.bisiness.enums.ModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;
    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
