package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 10:03
 **/
@Api(tags = "账套")
@FeignClient(value = "navigator-admin-service")
public interface SiteFacade {

    /**
     * 列表分页查询账套信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySiteByCondition")
    Result querySiteByCondition(@RequestBody QueryDTO<SiteQO> queryDTO);

    /**
     * 根据账套编码，查询账套详细信息
     *
     * @param siteCode
     * @return
     */
    @GetMapping("/getSiteDetailByCode")
    SiteEntity getSiteDetailByCode(@RequestParam(value = "siteCode") String siteCode);

    /**
     * 新增账套
     *
     * @param siteEntity
     * @return
     */
    @PostMapping("/saveSite")
    Result saveSite(@RequestBody SiteEntity siteEntity);

    /**
     * 更新账套
     *
     * @param siteEntity
     * @return
     */
    @PostMapping("/updateSite")
    Result updateSite(@RequestBody SiteEntity siteEntity);

    /**
     * 启用/禁用账套
     *
     * @param siteId
     * @param status
     * @return
     */
    @GetMapping("/updateSiteStatus")
    Result updateSiteStatus(@RequestParam(value = "siteId") Integer siteId,
                            @RequestParam(value = "status") Integer status);

    /**
     * 获取账套集合
     *
     * @param companyId
     * @param category2
     * @param status
     * @return
     */
    @GetMapping("/getSiteList")
    List<SiteEntity> getSiteList(@RequestParam(value = "companyId", required = false) Integer companyId,
                                 @RequestParam(value = "category2", required = false) Integer category2,
                                 @RequestParam(value = "syncSystem", required = false) String syncSystem,
                                 @RequestParam(value = "status", required = false) Integer status);

    /**
     * 根据账套编码，查询账套基本信息（Entity）
     *
     * @param siteCode
     * @return
     */
    @GetMapping("/getSiteByCode")
    SiteEntity getSiteByCode(@RequestParam(value = "siteCode") String siteCode);


    /**
     * 根据主体ID和工厂编码，查询账套集合
     *
     * @param companyId   主体ID
     * @param factoryCode 工厂编码
     * @return
     */
    @GetMapping("/getSiteByCompanyIdAndFactoryCode")
    SiteEntity getSiteByCompanyIdAndFactoryCode(@RequestParam(value = "companyId") Integer companyId,
                                                @RequestParam(value = "factoryCode") String factoryCode);

    /**
     * 获取账套编码
     *
     * @param siteQO
     * @return
     */
    @GetMapping("/getSiteCode")
    String getSiteCode(SiteQO siteQO);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取列表")
    @PostMapping("/site/querySiteList")
    List<SiteEntity> querySiteList(@RequestBody SiteQO condition);

    /**
     * 根据同步系统获取工厂编码列表
     *
     * @param syncSystem
     * @return
     */
    @ApiOperation("根据同步系统获取工厂编码列表")
    @GetMapping("/queryFactoryCodeBySyncSystem")
    Set<String> queryFactoryCodeBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem);

    /**
     * 获取Atlas账套编码集合
     */
    @GetMapping("/getAtlasSiteCodeList")
    Result<List<String>> getAtlasSiteCodeList();

    @GetMapping("/getSiteBySystemCode")
    SiteEntity getSiteBySystemCode(@RequestParam(value = "syncSystem") String syncSystem, @RequestParam(value = "bizCode") String bizCode);

    /**
     * 导入账套信息
     *
     * @param file
     * @return
     */
    @PostMapping("/site/importSite")
    Result importSite(@RequestParam(value = "file") MultipartFile file);

    @GetMapping("/getSiteListBySyncSystem")
    Result<List<SiteEntity>> getSiteListBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem);
}
