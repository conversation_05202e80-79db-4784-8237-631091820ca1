package com.navigator.admin.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ContractOperationLogDTO {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "关联记录code")
    private String referBizCode;

    @ApiModelProperty(value = "关联记录Id")
    private Integer referBizId;

    @ApiModelProperty(value = "0:客户可见 1：用户可见 9：系统级别")
    private Integer logLevel;

    @ApiModelProperty(value = "日志内容")
    private String logInfo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "合同Id")
    private Integer contractId;

    @ApiModelProperty(value = "交易类型")
    private String tradeTypeName;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "目标记录ID")
    private Integer targetRecordId;

}
