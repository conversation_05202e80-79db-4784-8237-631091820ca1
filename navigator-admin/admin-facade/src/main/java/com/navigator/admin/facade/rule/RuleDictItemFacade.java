package com.navigator.admin.facade.rule;

import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 11:40
 **/
@FeignClient(value = "navigator-admin-service")
public interface RuleDictItemFacade {
    @PostMapping("/queryRuleDictItemByCondition")
    Result queryRuleDictItemByCondition(@RequestBody QueryDTO<RuleDictItemEntity> queryDTO);

    @GetMapping("/getRuleDictItemById")
    List<RuleDictItemEntity> getRuleDictItemById(@RequestParam(name = "dictItemIdList") List<Integer> dictItemIdList);

    @GetMapping("/getRuleItemByDictCode")
    List<RuleDictItemEntity> getRuleItemByDictCode(@RequestParam(name = "dictCode") String dictCode, @RequestParam(name = "moduleType") String moduleType,
                                               @RequestParam(name = "systemId") String systemId);

    @GetMapping("/getRuleDictItemByCode")
    RuleDictItemEntity getRuleDictItemByCode(@RequestParam(name = "dictCode") String dictCode, @RequestParam(name = "itemCode") String itemCode,
                                         @RequestParam(name = "itemValue") Integer itemValue, @RequestParam(name = "moduleType") String moduleType,
                                         @RequestParam(name = "systemId") String systemId);

    @PostMapping("/saveRuleDictItem")
    Result saveRuleDictItem(@RequestBody RuleDictItemEntity ruleDictItemEntity);

    @PostMapping("/updateRuleDictItem")
    Result updateRuleDictItem(@RequestBody RuleDictItemEntity ruleDictItemEntity);
}
