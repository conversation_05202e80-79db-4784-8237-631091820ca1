package com.navigator.admin.pojo.dto;

import cn.hutool.core.lang.tree.Tree;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 角色授权权限数据对象
 *
 * <AUTHOR>
 */
@Data
public class RoleAuthPowerDTO {
    @ApiModelProperty(value = "授权权限列表，见返回示例")
    private List<Tree<Integer>> powerList;
    @ApiModelProperty(value = "授权ID集合")
    private Set<Integer> authIdList;
}
