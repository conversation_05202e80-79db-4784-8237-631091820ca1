package com.navigator.admin.pojo.dto.columbus;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CRoleQueryDTO {

    private String roleName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateEndTime;




    private Integer roleId;
    private String roleCode;
    private List<Integer> roleIdList;
    private List<String> roleCodeList;

    private Integer roleDefId;
    private List<Integer> roleDefIdList;
    private Integer categoryId;
    private Integer belongCustomerId;
    private Integer factoryId;
    List<Integer> categoryIdList;

    private Integer isDeleted = 0;


}
