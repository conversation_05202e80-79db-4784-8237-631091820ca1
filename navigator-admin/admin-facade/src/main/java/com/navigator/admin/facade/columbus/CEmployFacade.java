package com.navigator.admin.facade.columbus;

import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.ResetPasswordDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployBusinessDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.admin.pojo.vo.columbus.CEmployDetailVO;
import com.navigator.admin.pojo.vo.columbus.CEmployVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/1 13:39
 */

@FeignClient(value = "navigator-admin-service")
public interface CEmployFacade {

    @GetMapping("/columbus/getEmployByCustomerId")
    Result getEmployByCustomerId(@RequestParam("customerId") Integer customerId, @RequestParam("type") int type);


    /**
     * 查詢员工信息（根据电话）
     *
     * @param phone
     * @return
     */
    @GetMapping("/columbus/getEmployByPhone")
    Result getEmployByPhone(@RequestParam("phone") String phone, @RequestParam(value = "type", required = false) Integer type);
    //-----------------------------------------------


    /**
     * 查詢员工信息（根据电话）
     *
     * @param phone
     * @return
     */
    @GetMapping("/columbus/queryEmployByPhone")
    Result queryEmployByPhone(@RequestParam("phone") String phone);

    /**
     * 查詢员工信息(根据email)
     *
     * @param email
     * @return
     */
    @GetMapping("/columbus/getEmployByEmail")
    Result getEmployByEmail(@RequestParam("email") String email);


    @GetMapping("/columbus/getEmployById")
    CEmployEntity getEmployById(@RequestParam("id") Integer id);


    @GetMapping("/columbus/getCustomColumbusStateById")
    Integer getCustomColumbusStateById(@RequestParam("customerId") Integer customerId);

    /**
     * 查詢员工信息（根据昵称）
     *
     * @param nickName
     * @return
     */
    @GetMapping("/columbus/getEmployNickName")
    CEmployEntity getEmployNickName(@RequestParam("nickName") String nickName);

    /**
     * 查詢员工信息(根据employId)
     *
     * @param employIds 查詢员工信息(根据employIds)
     * @param employIds
     * @return
     */
    @GetMapping("/columbus/getEmployByEmployId")
    List<CEmployEntity> getEmployByEmployIds(@RequestParam("employIds") List<Integer> employIds);


    /**
     * 保存员工信息
     *
     * @param cEmployEntity
     * @return
     */
    @PostMapping("/columbus/saveEmploy")
    Integer saveOrUpdate(@RequestBody CEmployEntity cEmployEntity);

    /**
     * 修改员工信息
     *
     * @param cEmployEntity
     * @return
     */
    @PostMapping("/columbus/editEmploy")
    Integer editEmploy(@RequestBody CEmployEntity cEmployEntity);

    /**
     * 刪除员工信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/columbus/deleteEmploy")
    Integer deleteEmploy(@RequestParam("employId") String employId);

    /**
     * 查询是角色管理员的员工
     *
     * @return
     */
    @GetMapping("/columbus/findRoleAdministratorEmploys")
    Result findRoleAdministratorEmploys();

    /**
     * 如果不存在就新增
     *
     * @param cEmployEntity
     * @return
     */
    @PostMapping("/columbus/ifNotExistToSave")
    CEmployEntity ifNotExistToSave(@RequestBody CEmployEntity cEmployEntity);


    /**
     * 根据角色名称去获取员工id集合
     *
     * @param roleName
     * @return
     */
    @GetMapping("/columbus/getEmployByRoleName")
    List<CEmployEntity> getEmployByRoleName(@RequestParam("roleName") String roleName);

    /**
     * 根据CompnyId查询账号列表（区分企业个人账号）
     *
     * @param companyId
     * @return
     */
    @GetMapping("/columbus/queryEmployByCompanyId")
    List<EmployVO> queryEmployByCompanyId(@RequestParam("companyId") Integer companyId);


    /**
     * 重置客户密码
     *
     * @param id
     * @return
     */
    @GetMapping("/columbus/updateEmployResetPassword")
    Result updateEmployResetPassword(@RequestParam("id") Integer id);

    /**
     * 修改用户名的密码
     * 要求：不允许修改的密码是初始密码
     *
     * @param cEmployEntity
     * @return
     */
    @PostMapping("/columbus/modifyPassword")
    Result modifyPassword(@RequestBody CEmployEntity cEmployEntity);

    /**
     * 根据客户id 查询出员工信息
     *
     * @param customerId
     * @return
     */
    @GetMapping("/columbus/queryEmployByCustomerId")
    CEmployEntity queryEmployByCustomerId(@RequestParam("customerId") Integer customerId);


    /**
     * 保存员工信息
     *
     * @param employBusinessDTO
     * @return
     */
    @PostMapping("/columbus/saveOrUpdateEmploy")
    Result saveOrUpdateEmploy(@RequestBody CEmployBusinessDTO employBusinessDTO);


    /**
     * 查詢员工信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/columbus/queryEmployList")
    Result queryEmployList(@RequestBody QueryDTO<CEmployDTO> queryDTO);


    /**
     * 查詢员工详情信息
     *
     * @param employId
     * @param customerId
     * @return
     */
    @GetMapping("/columbus/queryEmployDetail")
    CEmployDetailVO queryEmployDetail(@RequestParam("employId") Integer employId, @RequestParam("customerId") Integer customerId);

    /**
     * 查詢员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/columbus/queryEmployListByRoleDefId")
    Result<List<CEmployVO>> queryEmployListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    /**
     * 重置客户密码
     *
     * @param employId
     * @return
     */
    @GetMapping("/columbus/resetPassword")
    String resetPassword(@RequestParam("employId") Integer employId);

    /**
     * 查詢员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/columbus/queryAvailableEmployByRoleDefId")
    Result<List<CEmployVO>> queryAvailableEmployByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    @GetMapping("/columbus/queryCategoryFactoryByRole")
    Result queryCategoryFactoryByRole();

    @GetMapping("/columbus/queryPermission")
    PermissionBO queryPermission(@RequestParam("employId") String employId, @RequestParam("categoryId") Integer categoryId);

    @PostMapping("/columbus/updateEmployStatus")
    Result updateEmployStatus(@RequestBody CEmployDTO employDTO);

    @PostMapping("/columbus/saveEmployStatus")
    Result saveEmployStatus(CEmployEntity cEmployEntity);

    @PostMapping("/columbus/resetUserPassword")
    Result resetUserPassword(@RequestBody ResetPasswordDTO resetPasswordDTO);

    @PostMapping("/columbus/sendResetPasswordCode/{mobileNo}")
    Result sendResetPasswordCode(@PathVariable("mobileNo") String mobileNo);

    @PostMapping("/columbus/resetNotLogUserPassword")
    Result resetNotLogUserPassword(@RequestBody ResetPasswordDTO resetPasswordDTO);

    @PostMapping("/columbus/sendResetPasswordPhoneCode/{mobileNo}")
    Result sendResetPasswordPhoneCode(@PathVariable("mobileNo") String mobileNo);

    @PostMapping("/columbus/sendAadCode/{mobileNo}")
    Result sendAadCode(@PathVariable("mobileNo") String mobileNo);

    @PostMapping("/columbus/verifyAadCode")
    Result verifyAadCode(@RequestBody LoginDTO loginDTO);

    /**
     * 角色数据导入
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping(value = "/columbus/importEmploy", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importEmploy(@RequestPart("file") MultipartFile file);

    /**
     * 根据虚角色id ,品类, 采销 获取员工id集合
     * Jason
     *
     * @param
     * @return
     */
    @GetMapping("/columbus/getEmploy")
    List<CEmployEntity> getEmploy(@RequestParam("roleDefId") String roleDefId, @RequestParam("categoryId") String categoryId, @RequestParam("salesType") String salesType);

    /**
     * 根据虚角色code ,品类, 采销 获取员工id集合
     * Jason
     *
     * @param
     * @return
     */
    @GetMapping("/columbus/getEmployByRoleDefCode")
    List<CEmployEntity> getEmployByRoleDefCode(@RequestParam("roleDefCode") String roleDefCode, @RequestParam("categoryId") String categoryId, @RequestParam("salesType") String salesType);


    /**
     * 通过实角色id列表，可以获取所有的人员
     * jason
     *
     * @param roleIds
     * @return
     */
    @PostMapping("/columbus/queryEmployByRoleIds")
    List<CEmployEntity> queryEmployByRoleIds(@RequestParam("roleIds") List<Integer> roleIds);

    @GetMapping("/columbus/getEmployRolesByCustomerId")
    List<CEmployEntity> getEmployRolesByCustomerId(@RequestParam("roleIds") List<Integer> roleIds, @RequestParam("customerId") Integer customerId);

    /**
     * 查詢员工信息
     *
     * @param email
     * @return
     */
    @GetMapping("/columbus/getEmployByEmailOrPhone")
    Result getEmployByEmailOrPhone(@RequestParam("email") String email, @RequestParam("phone") String phone);

    @PostMapping("/columbus/queryChoosedEmployByRoleId")
    Result queryChoosedEmployByRoleId(@RequestBody QueryDTO<CRoleDTO> queryDTO);

    /**
     * 查詢员工详情信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/columbus/queryCurrentEmployDetail")
    CEmployDetailVO queryCurrentEmployDetail(@RequestParam("employId") Integer employId);

    @GetMapping("/columbus/exportEmployRoleList")
    Result exportEmployRoleList();

    @PostMapping("/queryColumbusAdminList")
    Result queryColumbusAdminList(@RequestBody QueryDTO<CEmployDTO> queryDTO);

    @PostMapping("/updateColumbusAdmin")
    Result updateColumbusAdmin(@RequestBody CEmployDTO cEmployDTO);

    @PostMapping("/updateCEmploy")
    Result updateCEmploy(@RequestBody CEmployEntity cEmployEntity);

    @PostMapping("/exportColumbusAdminList")
    Result exportColumbusAdminList(@RequestBody CEmployDTO cEmployDTO);

    @GetMapping("/getBatchEmployList")
    Result getBatchEmployList(@RequestParam("size") int size);

    @PostMapping("/queryColumbusList")
    Result queryColumbusList(@RequestBody QueryDTO<CEmployDTO> queryDTO);

    @PostMapping("/exportColumbusList")
    Result exportColumbusList(@RequestBody CEmployDTO cEmployDTO);

    @PostMapping("/updateTypeByIds")
    void updateTypeByIds(@RequestParam("ids") List<Integer> ids);

    @PostMapping("/setColumbusAdmin")
    Result setColumbusAdmin(@RequestBody CEmployDTO cEmployDTO);

    @PostMapping("/columbus/setColumbusAdmin")
    Result setAdmin(@RequestBody CEmployDTO cEmployDTO);

    @PostMapping("/columbus/queryAdminType")
    Result queryAdminType(@RequestParam(value = "customerId") Integer customerId);

}
