package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CLBRoleImportDTO {
    @Excel(name = "ID")
    private String id;

    @Excel(name = "角色名称")
    private String roleName;

    @Excel(name = "是否区分品类")
    private String isCategory;

    @Excel(name = "是否区分采销")
    private String isSalesType;

    @Excel(name = "二级品类")
    private String category;

    @Excel(name = "采销类型")
    private String salesType;

    @Excel(name = "操作")
    private String operation = "";

}
