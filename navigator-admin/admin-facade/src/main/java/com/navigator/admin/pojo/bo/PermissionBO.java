package com.navigator.admin.pojo.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class PermissionBO implements Serializable {

    private List<Integer> customerIdList = new ArrayList<>();
    private List<Integer> companyIdList = new ArrayList<>();

    private List<Integer> categoryIdList = new ArrayList<>();

    /**
     * 品类 -对应的customerIdList
     */
    private Map<Integer, List<Integer>> permissionMap = new HashMap<>();
    /**
     * companyId - customerIdList
     */
    private Map<Integer, List<Integer>> companyCustomerIdMap = new HashMap<>();

    private List<String> siteCodeList = new ArrayList<>();
}
