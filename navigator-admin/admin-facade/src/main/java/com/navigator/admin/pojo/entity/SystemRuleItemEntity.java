package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统配置明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
@Accessors(chain = true)
@TableName("dbz_system_rule_item")
@ApiModel(value = "SystemRuleItemEntity对象", description = "系统配置明细")
public class SystemRuleItemEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "配置ID")
    private Integer ruleId;

    @ApiModelProperty(value = "配置明细编码")
    private String code;

    @ApiModelProperty(value = "Lkg编码")
    private String lkgCode;

    @ApiModelProperty(value = "配置的mdm编码")
    private String mdmCode;

    @ApiModelProperty(value = "关键字")
    private String ruleKey;

    @ApiModelProperty(value = "配置取值")
    private String ruleValue;

    @ApiModelProperty(value = "取值类型（1、int 2、String字符串 3、decimal 4、Json）")
    private Integer valueType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否系统配置（0手动/其他 1系统）")
    private Integer isSystem;

    @ApiModelProperty(value = "所属主体Id")
    private String companyId;

    @ApiModelProperty(value = "货品Id")
    private String goodsId;

    @ApiModelProperty(value = "状态（0 无效 1 有效）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "备用字段")
    private String memo;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "公司名称")
    @TableField(exist = false)
    private String companyName;
}
