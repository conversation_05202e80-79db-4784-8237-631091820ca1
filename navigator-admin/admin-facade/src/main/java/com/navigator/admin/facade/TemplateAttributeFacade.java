package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2021/11/29 11:02
 */

@FeignClient(value = "navigator-admin-service")
public interface TemplateAttributeFacade {

    @PostMapping("/queryTemplateAttributeList")
    Result queryTemplateAttributeList(@RequestBody QueryDTO<TemplateAttributeEntity> queryDTO);

}
