package com.navigator.admin.facade.rule;

/**
 * <AUTHOR> NaN<PERSON>
 * @since : 2024-09-19 16:59
 **/

import com.navigator.admin.pojo.dto.rule.CommonConfigDTO;
import com.navigator.admin.pojo.dto.rule.CommonConfigRuleMatchDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface CommonConfigFacade {

    /**
     * 保存系统配置
     *
     * @param commonConfigDTO
     * @return
     */
    @PostMapping("/saveOrUpdateCommonConfig")
    Result saveOrUpdateCommonConfig(@RequestBody CommonConfigDTO commonConfigDTO);


    /**
     * 获取配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getCommonConfigDetailById")
    CommonConfigDTO getCommonConfigDetailById(@RequestParam(value = "id") Integer id);

    /**
     * 查询正本配置列表
     *
     * @return
     */
    @GetMapping("/getSignPaperRuleConfig")
    List<CommonConfigDTO> getSignPaperRuleConfig();

    /**
     * 匹配配置规则脚本内容
     * @param ruleMatchDTO
     * @return
     */
    @PostMapping("/matchConfigRuleInfo")
    Boolean matchConfigRuleInfo(@RequestBody CommonConfigRuleMatchDTO ruleMatchDTO);

}
