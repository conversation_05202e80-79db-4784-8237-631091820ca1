package com.navigator.admin.pojo.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/25 19:37
 */
@Data
public class OperationLogBO {

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "业务模块")
    private String bizModule;

    @ApiModelProperty(value = "0:客户可见 1：用户可见 9：系统级别")
    private Integer logLevel;

    @ApiModelProperty(value = "关联记录code")
    private String code;

    @ApiModelProperty(value = "关联业务记录ID")
    private Integer referBizId;

}
