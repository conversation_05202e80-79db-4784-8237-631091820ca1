package com.navigator.admin.pojo.vo.columbus;

import com.navigator.admin.pojo.vo.MenuDetailVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class CMenuVO {
    private List<Map<Integer,String>> categoryList;
    private List<MenuDetailVO> menuDetailVOList = new ArrayList<>();
    private List<MenuDetailVO> defaultList;
    private List<MenuDetailVO> sbmList;
    private List<MenuDetailVO> sboList;
    private List<Integer> chooseIdList;


    }
