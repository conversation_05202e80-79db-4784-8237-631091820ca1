package com.navigator.admin.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 操作来源 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Getter
public enum OperationSourceEnum {
    /**
     * 操作来源
     */
    CUSTOMER(0, "客户"),
    EMPLOYEE(1, "用户"),
    CUSTOMER_OR_EMPLOYEE(2, "客户和用户都可见（日志级别）"),
    SYSTEM(9, "系统");

    int value;
    String description;

    OperationSourceEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static OperationSourceEnum getByName(String name) {
        for (OperationSourceEnum sourceEnum : OperationSourceEnum.values()) {
            if (name.equals(sourceEnum.getValue())) {
                return sourceEnum;
            }
        }
        return OperationSourceEnum.EMPLOYEE;
    }

    public static OperationSourceEnum getByValue(Integer value) {

        for (OperationSourceEnum sourceEnum : OperationSourceEnum.values()) {
            if (sourceEnum.getValue() == value) {
                return sourceEnum;
            }
        }
        return OperationSourceEnum.CUSTOMER;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDescription();
    }
}
