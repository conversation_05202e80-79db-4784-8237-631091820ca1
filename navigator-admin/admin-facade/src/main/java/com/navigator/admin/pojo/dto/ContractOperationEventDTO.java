package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合同日志信息
 *
 * <AUTHOR>
 * @date 20240722
 */
@Data
@Accessors(chain = true)
public class ContractOperationEventDTO {

    @ApiModelProperty(value = "合同标识")
    Integer contractId;

    @ApiModelProperty(value = "TT交易类型名称")
    String tradeTypeName;

    @ApiModelProperty(value = "TT编码")
    String ttCode;

    @ApiModelProperty(value = "操作人")
    String createdAt;

    @ApiModelProperty(value = "操作内容")
    String content;

    @ApiModelProperty(value = "操作日志列表")
    List<ContractOperationLogDTO> operationLogList;
}
