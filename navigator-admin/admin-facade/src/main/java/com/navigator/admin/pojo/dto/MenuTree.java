package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单树
 *
 * <AUTHOR>
 */
@Data
@ApiModel("菜单树")
public class MenuTree {
    @ApiModelProperty(value = "ID", position = 1)
    private Integer id;
    @ApiModelProperty(value = "上级ID", position = 2)
    private Integer parentId;
    @ApiModelProperty(value = "菜单编码", position = 4)
    private String code;
    @ApiModelProperty(value = "父菜单编码", position = 5)
    private String parentCode;
    @ApiModelProperty(value = "图标地址", position = 6)
    private String icon;
    @ApiModelProperty(value = "路径", position = 7)
    private String url;
    @ApiModelProperty(value = "是否已被收藏过（0否; 1是）", position = 8)
    private Boolean hasCollected;
    @ApiModelProperty(value = "子集", position = 99)
    private List<MenuTree> children;
}
