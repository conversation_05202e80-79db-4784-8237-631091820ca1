package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SignatureDTO {
    @ApiModelProperty(value = "收件人")
    @NotBlank
    private String receiver;
    @ApiModelProperty(value = "地址")
    @NotBlank
    private String address;
    @ApiModelProperty(value = "邮箱")
    @NotBlank
    private String email;
    @ApiModelProperty(value = "传真")
    @NotBlank
    private String telephone;
    @ApiModelProperty(value = "手机")
    @NotBlank
    private String phone;
    @ApiModelProperty(value = "客户id")
    @NotBlank
    private String customerId;
}
