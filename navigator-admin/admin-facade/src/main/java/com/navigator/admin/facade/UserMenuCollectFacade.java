package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.UserMenuCollectVO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:41
 **/
@FeignClient(value = "navigator-admin-service")
public interface UserMenuCollectFacade {


    @PostMapping("/saveUserMenuCollect")
    Result saveUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect);

    @PostMapping("/updateUserMenuCollect")
    Result updateUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect);

    @GetMapping("/deleteCollectMenu")
    Result deleteCollectMenu(@RequestParam(value = "id") Integer id);

    @GetMapping("/sortCollectMenu")
    Result sortCollectMenu(@RequestParam(value = "menuCollectIdList") List<Integer> menuCollectIdList);

    @GetMapping("/getUserPowerMenuList")
    List<MenuDetailVO> getUserPowerMenuList(@RequestParam(value = "categoryId") Integer categoryId,
                                            @RequestParam(value = "customerId", required = false) Integer customerId,
                                            @RequestParam(value = "system") Integer system);


    @GetMapping("/getUserCollectMenuList")
    List<UserMenuCollectVO> getUserCollectMenuList(@RequestParam(value = "categoryId") Integer categoryId,
                                                   @RequestParam(value = "customerId", required = false) Integer customerId,
                                                   @RequestParam(value = "system") Integer system);
}
