package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-04-13 01:09
 */
@Data
@Accessors(chain = true)
@TableName("dba_basic_price_config")
@ApiModel(value = "BasicPriceConfig对象", description = "")
public class BasicPriceConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "基差基准价明细ID")
    private Integer ruleItemId;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "基差基配置ID")
    private Integer ruleId;

    @ApiModelProperty(value = "货品id")
    private Integer goodsId;

    @ApiModelProperty(value = "所属主体Id")
    private String companyId;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "主力合约")
    private String domainCode;

    @ApiModelProperty(value = "交货开始月份")
    private String deliveryBeginDate;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
