package com.navigator.admin.facade.magellan;

import com.navigator.admin.pojo.dto.PowerDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Api(tags = "权限")
@FeignClient(value = "navigator-admin-service")
public interface PowerFacade {
    @PostMapping("/magellan/saveOrUpdatePower")
    Result saveOrUpdatePower(@RequestBody PowerDTO powerDTO);

    @PostMapping("/magellan/queryPowerByRoleId")
    Result queryPowerByRoleId(@RequestBody PowerDTO powerDTO);

    @PostMapping("/magellan/queryPowerByEmployId")
    Result queryPowerByEmployId(@RequestParam("employId") Integer employId);

    @PostMapping("/magellan/addRolePower")
    Result addRolePower(@RequestBody PowerDTO powerDTO);

    /**
     * 根据用户查询个主体下的权限
     * @param employId
     * @return
     */
    @PostMapping("/magellan/queryCompanyPowerByEmployId")
    Map<Integer, List<String>> queryCompanyPowerByEmployId(@RequestParam("employId") Integer employId);

}
