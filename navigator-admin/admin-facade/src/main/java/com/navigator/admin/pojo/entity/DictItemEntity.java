package com.navigator.admin.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.customer.pojo.entity.CustomerEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbz_dict_item")
@ApiModel(value = "DbzDictItemEntity对象", description = "")
public class DictItemEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "字典编码")
    private String dictCode;

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典项编码")
    @Excel(name = "集团编码", orderNum = "2", width = 15)
    private String itemCode;

    @ApiModelProperty(value = "字典项名称")
    @Excel(name = "集团名称", orderNum = "1", width = 20)
    private String itemName;

    @ApiModelProperty(value = "字典项值")
    private Integer itemValue;

    @ApiModelProperty(value = "字典项展示信息")
    private String itemDescription;

    @ApiModelProperty(value = "字典项排序")
    private Integer itemSort;

    @ApiModelProperty(value = "状态")
    @Excel(name = "状态", replace = {"启用_1", "禁用_0"}, orderNum = "3", width = 6)
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "5", width = 25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @Excel(name = "更新人", orderNum = "4", width = 20)
    private String updatedBy;

    private String createdBy;

    @ApiModelProperty(value = "特殊模板客户")
    @TableField(exist = false)
    private List<String> customerCodeList;

    @ApiModelProperty(value = "特殊模板客户")
    @TableField(exist = false)

    @ExcelCollection(name = "特殊模板客户", orderNum = "6")
    private List<CustomerEntity> customerList;

    @ApiModelProperty(value = "特殊模板客户编号")
    @TableField(exist = false)
    private String customerCode;

    @TableField(exist = false)
    private String customerNames;

    @ApiModelProperty(value = "更新开始时间")
    @TableField(exist = false)
    private String startDay;
    @ApiModelProperty(value = "更新结束时间")
    @TableField(exist = false)
    private String endDay;

    /**
     * 集团编码或者名称
     */
    @TableField(exist = false)
    private String searchKey;

}
