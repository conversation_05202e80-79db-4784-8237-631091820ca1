package com.navigator.admin.facade.approval;

import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
@FeignClient(value = "navigator-admin-service")
public interface LoaApprovalRuleFacade {

    /**
     * 根据数据查询规则类容
     *
     * @param loaApprovalRuleDTO
     * @return
     */
    @PostMapping("/queryLoaApprovalRule")
    LoaApprovalRuleEntity queryLoaApprovalRule(@RequestBody LoaApprovalRuleDTO loaApprovalRuleDTO);
}
