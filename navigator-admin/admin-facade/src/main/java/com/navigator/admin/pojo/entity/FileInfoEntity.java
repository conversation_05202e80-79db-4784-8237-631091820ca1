package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 文件基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbz_file_info")
@ApiModel(value = "FileInfo对象", description = "文件基础信息表")
public class FileInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户id")
    @TableField(value = "operator_id")
    private Integer operatorId;

    @ApiModelProperty(value = "源文件名")
    @TableField(value = "original_file_name")
    private String originalFileName;

    @ApiModelProperty(value = "格式化名")
    @TableField(value = "new_file_name")
    private String newFileName;

    @ApiModelProperty(value = "文件后缀名 .png")
    @TableField(value = "extension")
    private String extension;

    @ApiModelProperty(value = "文件路径")
    @TableField(value = "path")
    private String path;

    @ApiModelProperty(value = "文件存储类型")
    @TableField(value = "fs_type")
    private Integer fsType;

    @ApiModelProperty(value = "文件存储地址")
    @TableField(value = "fs_path")
    private String fsPath;

    @ApiModelProperty(value = "文件大小")
    @TableField(value = "size")
    private String size;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 文件类型
     * {@link com.navigator.common.enums.FileCategoryType}
     */
    @TableField(exist = false)
    private Integer fileCategoryType;

    /**
     * 文件具体类型
     */
    @TableField(exist = false)
    private String fileCategoryTypeInfo;

    /**
     * 业务文件状态
     */
    @TableField(exist = false)
    private Integer bizFileStatus;
    /**
     * 驳回/撤回原因
     */
    @TableField(exist = false)
    private String memo;

    /**
     * 文件预览地址(域名+地址+sas-token)
     */
    @TableField(exist = false)
    private String fileUrl;
    /**
     * 文件预览地址(域名+地址)
     */
    @TableField(exist = false)
    private String filePathUrl;
    /**
     * 文件预览地址(域名+地址)
     */
    @TableField(exist = false)
    private Integer fileBusinessId;
}
