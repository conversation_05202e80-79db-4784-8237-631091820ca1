package com.navigator.admin.facade.columbus;

import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface CEmployPermissionFacade {
    @GetMapping("/columbus/getEmployRoleListByRoleIds")
    Result getEmployRoleListByRoleIds(@RequestParam("roleIdList") List<Integer> roleIdList);

    /**
     * employ和role进行绑定
     * jason
     * @param employRoleDTO

     * @return
     */
    @PostMapping("/columbus/addEmployRole")
    void addEmployRole(@RequestBody CEmployRoleDTO employRoleDTO);

    @GetMapping("/columbus/addEmployRoles")
    void addEmployRoles(@RequestParam("employId") Integer employId, @RequestParam("roleIds") String roleIds, @RequestParam("roleDefId") Integer roleDefId, @RequestParam("categoryId") Integer categoryId, @RequestParam("customerIds") String customerIds);

    @PostMapping("/columbus/deleteEmployRole")
    void deleteEmployRole(@RequestBody CEmployRoleDTO employRoleDTO);

    @GetMapping("/columbus/deleteByEmployId")
    void deleteByEmployId(@RequestParam("employId") Integer employId);

    @PostMapping("/columbus/save")
    void save(@RequestBody CEmployRoleEntity cEmployRoleEntity);

}
