package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Data
@Accessors(chain = true)
@TableName("dbz_system_rule")
@ApiModel(value="SystemRuleEntity对象", description="系统配置")
public class SystemRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品类ID（0默认为，所有品类适用）")
    private Integer categoryId;

    @ApiModelProperty(value = "父ID")
    private Integer parentId;

    /**
     * {@link com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum}
     */
    @ApiModelProperty(value = "配置编码")
    private String code;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态（0 无效 1 有效）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
