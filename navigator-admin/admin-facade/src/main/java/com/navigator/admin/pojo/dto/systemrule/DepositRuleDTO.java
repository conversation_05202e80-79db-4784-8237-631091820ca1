package com.navigator.admin.pojo.dto.systemrule;

import com.navigator.admin.pojo.enums.systemrule.DepositSceneEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class DepositRuleDTO {
    /**
     * 品类Id
     */
    private Integer categoryId;

    /**
     * 保证金规则类型
     * {@link DepositSceneEnum}
     */
    private Integer ruleType;

    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 总价 (非必传)
     */
    private BigDecimal totalAmount;

    /**
     * 合同数量
     */
    private BigDecimal contractNum;

    /**
     * 客户id
     */
    private Integer customerId;

}
