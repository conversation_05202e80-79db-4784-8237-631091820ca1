package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 付款条件对外暴露的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@FeignClient(value = "navigator-admin-service")
public interface PayConditionFacade {

    @PostMapping("/getPayCondition")
    Result<List<PayConditionEntity>> queryPayCondition(@RequestBody PayConditionDTO payConditionDTO);

    @GetMapping("/getPayConditionById")
    Result getPayConditionById(@RequestParam("payConditionId") Integer payConditionId);

    @PostMapping("/getPayConditionList")
    Result getPayConditionList(@RequestBody QueryDTO<PayConditionDTO> queryDTO);

    @PostMapping("/addPayCondition")
    Result<Boolean> addPayCondition(@RequestBody PayConditionDTO payConditionDTO);

    @PostMapping("/updatePayCondition")
    Result<Boolean> updatePayCondition(@RequestBody PayConditionDTO payConditionDTO);

    @PostMapping("/importPayCondition")
    Result<String> importPayCondition(@RequestParam(value = "file") MultipartFile file);

}
