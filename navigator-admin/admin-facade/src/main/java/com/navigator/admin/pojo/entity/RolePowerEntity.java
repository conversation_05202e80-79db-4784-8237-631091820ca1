package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.RolePowerQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_role_powerV2")
@ApiModel(value = "RolePowerEntity对象", description = "")
public class RolePowerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "操作id")
    private Integer powerId;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "虚角色id")
    private Integer roleDefId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<RolePowerEntity> lqw(RolePowerQO condition) {
        LambdaQueryWrapper<RolePowerEntity> lqw = new LambdaQueryWrapper<RolePowerEntity>().eq(RolePowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleId()), RolePowerEntity::getRoleId, condition.getRoleId());
            lqw.in(CollUtil.isNotEmpty(condition.getRoleIdList()), RolePowerEntity::getRoleId, condition.getRoleIdList());
        }
        lqw.orderByDesc(RolePowerEntity::getId);
        return lqw;
    }
}
