package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class EmployRoleDTO {
    private Integer roleDefId;
    private List<Integer> employIdList;
    private String factoryId;
    private String categoryId;
    private String companyId;
    @ApiModelProperty(value = "角色类型(0:高级 1:普通)")
    private String type;
    private String roleDefCode;
    private List<Integer> factoryIdList;
    private List<Integer> categoryIdList;
    private String name;
}
