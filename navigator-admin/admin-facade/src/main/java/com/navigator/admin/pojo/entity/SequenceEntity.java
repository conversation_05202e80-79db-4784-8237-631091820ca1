package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.SequenceQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 序列号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_sequence")
@ApiModel(value = "sequence对象", description = "序列号")
public class SequenceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "序列号Key")
    private String redisKey;

    @ApiModelProperty(value = "序列号值")
    private Long redisValue;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "删除状态")
    private Integer isDeleted;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<SequenceEntity> lqw(SequenceQO condition) {
        LambdaQueryWrapper<SequenceEntity> lqw = new LambdaQueryWrapper<SequenceEntity>().eq(SequenceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SequenceEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRedisKey()), SequenceEntity::getRedisKey, condition.getRedisKey());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRedisValue()), SequenceEntity::getRedisValue, condition.getRedisValue());
        }
        lqw.orderByDesc(SequenceEntity::getId);
        return lqw;
    }
}
