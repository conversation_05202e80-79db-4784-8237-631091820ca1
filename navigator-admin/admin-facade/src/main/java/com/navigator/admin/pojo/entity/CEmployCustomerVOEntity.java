package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_c_employ_customer")
@ApiModel(value="VCEmployCustomerEntity对象", description="columbus多主体表视图")
public class CEmployCustomerVOEntity extends CEmployCustomerEntity{

    @ApiModelProperty(value = "客户编码")
    private String linkageCustomerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "用户名称")
    private String employName;

    @ApiModelProperty(value = "用户名称")
    private String phone;

    @ApiModelProperty(value = "用户名称")
    private String email;

    @ApiModelProperty(value = "集团名称")
    private String enterpriseName;
}
