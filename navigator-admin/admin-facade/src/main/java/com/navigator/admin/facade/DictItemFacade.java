package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 16:26
 **/
@FeignClient(value = "navigator-admin-service")
public interface DictItemFacade {
    @PostMapping("/queryByCondition")
    Result queryByCondition(@RequestBody QueryDTO<DictItemEntity> queryDTO);

    @PostMapping("/queryExportVipCustomerList")
    List<DictItemEntity> queryExportVipCustomerList(@RequestBody DictItemEntity dictItemQO);

    @GetMapping("/getDictById")
    List<DictItemEntity> getDictItemById(@RequestParam(value = "dictIdList") List<Integer> dictIdList);

    @GetMapping("/getItemByDictCode")
    List<DictItemEntity> getItemByDictCode(@RequestParam(value = "dictCode") String dictCode);

    @GetMapping("/getDictItemByCode")
    DictItemEntity getDictItemByCode(@RequestParam(value = "dictCode") String dictCode,
                                     @RequestParam(value = "itemCode", required = false) String itemCode,
                                     @RequestParam(value = "itemValue", required = false) Integer itemValue
    );

    @PostMapping("/saveDictItem")
    Result saveDictItem(@RequestBody DictItemEntity dictItemEntity);

    @PostMapping("/updateDictItem")
    Result updateDictItem(@RequestBody DictItemEntity dictItemEntity);
}
