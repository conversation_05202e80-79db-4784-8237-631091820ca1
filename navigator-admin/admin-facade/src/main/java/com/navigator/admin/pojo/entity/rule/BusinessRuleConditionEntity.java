package com.navigator.admin.pojo.entity.rule;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 17:53
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbr_business_rule_condition")
@ApiModel(value = "BusinessRuleConditionEntity对象", description = "")
public class BusinessRuleConditionEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "变量ID(dbr_dict_item)")
    private Integer conditionVariableId;

    @ApiModelProperty(value = "条件变量英文名")
    private String conditionVariable;

    @ApiModelProperty(value = "运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)")
    private String patternRelation;

    @ApiModelProperty(value = "条件值")
    private String conditionValue;

    @ApiModelProperty(value = "加载规则脚本（drools脚本）")
    private String ruleInfo;

    @ApiModelProperty(value = "条件值描述")
    private String conditionValueInfo;

    @ApiModelProperty(value = "加载规则信息描述")
    private String ruleDesc;

    /**
     * {@link com.navigator.bisiness.enums.ModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "5", width = 25)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
