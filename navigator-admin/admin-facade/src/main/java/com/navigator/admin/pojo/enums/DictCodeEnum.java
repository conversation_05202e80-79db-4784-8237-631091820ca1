package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-07 14:30
 */
@Getter
@AllArgsConstructor
public enum DictCodeEnum {
    /**
     * 模块状态
     */
    DEFAULT("default", "默认"),
    DESTINATION("destination", "目的港"),
    QUALITY_TESTING("quality_check", "质量检验"),
    WEIGHT_CHECK("weight_check", "重量检验");

    private String code;
    private String msg;

    public static DictCodeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(dictCodeEnum -> StringUtils.equals(code, dictCodeEnum.getCode()))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static List<String> getBasicCodeList() {
        return Arrays.asList(DictCodeEnum.DESTINATION.getCode());
    }

}
