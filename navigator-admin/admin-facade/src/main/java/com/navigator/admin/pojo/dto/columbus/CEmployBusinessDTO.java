package com.navigator.admin.pojo.dto.columbus;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class CEmployBusinessDTO {
    private Integer employId;
    @NotBlank(message = "用户名不能为空")
    private String employName;
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "1\\d{10}", message = "请输入正确手机号")
    private String phone;
    //@NotBlank(message = "邮箱不能为空")
   // @Pattern(regexp = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$", message = "请输入正确邮箱")
    private String email;
    private Integer status;
    private Integer customerId;
    // private List<Integer> roleDefIdList;
    //@NotEmpty(message = "权限不能为空")
//    Integer categoryId; //品类
//    Integer customerId; //主体
//    @ApiModelProperty(value = "角色ID列表")
//    private List<Integer> roleIdList;
    //customerId  roleId
    private Map<Integer,List<Integer>> roleMap;
}
