package com.navigator.admin.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-01 12:01
 */
@Data
@Accessors(chain = true)
public class DictVO {
    private Integer id;

    /**
     * 父id
     */
    private Integer parentId;

    /**
     * 名称
     */
    private String name;

    /**
     * 业务模块
     */
    private String bizModule;

    /**
     * 编码
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 描述
     */
    private String description;

    private List<DictVO> childrenDictList;
}
