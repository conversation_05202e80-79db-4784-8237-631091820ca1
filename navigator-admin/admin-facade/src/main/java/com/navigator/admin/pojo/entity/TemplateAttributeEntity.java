package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 模版属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbz_template_attribute")
@ApiModel(value = "TemplateAttributeEntity对象", description = "模版属性表")
public class TemplateAttributeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板id")
    private Integer templateId;

    @ApiModelProperty(value = "模板编号")
    private String templateCode;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板类型(0:大合同 1:订单.....)")
    private Integer templateType;

    @ApiModelProperty(value = "适用合同类型(1:一口价,2点价.......)")
    private Integer contractType;

    @ApiModelProperty(value = "货品品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "1采购 2销售")
    private String salesType;
    /**
     * ContractActionEnum
     */
    @ApiModelProperty(value = "TT类型")
    private String actionType;

    @ApiModelProperty(value = "交易类型")
    private Integer tradeType;

    /**
     * 协议类型:0.补充协议 1.尾量终止
     */
    @TableField(value = "sign_type")
    private Integer signType;

    @ApiModelProperty(value = "是否删除（0、未删除 1、已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}
