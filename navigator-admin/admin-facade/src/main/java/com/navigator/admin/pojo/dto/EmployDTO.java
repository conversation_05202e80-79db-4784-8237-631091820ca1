package com.navigator.admin.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/9 14:35
 */

@Data
public class EmployDTO implements Serializable {

    private String name;

    //@NotNull(message = "用户Id不能为空")
    private String id;

    //@NotNull(message = "密码不能为空")
    private String password;

    private String status;

    private String UpdatedByName;

    private String phone;

    private String email;

}
