package com.navigator.admin.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class LoginVO {

    @ApiModelProperty(value = "主键 ")
    private Integer id;

    @ApiModelProperty(value = "用户名")
    private String name;

    private String token;

    private String refreshToken;

    private Boolean aadLogin;

    @ApiModelProperty(value = "签署状态")
    private Integer signatureStatus;

    private MenuVO menuVO;

    private List<String> powerCodeList;
}
