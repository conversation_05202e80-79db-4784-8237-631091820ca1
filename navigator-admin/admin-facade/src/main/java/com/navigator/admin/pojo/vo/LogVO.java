package com.navigator.admin.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LogVO {
    @ApiModelProperty(value = "场景")
    @Excel(name = "场景", orderNum = "1")
    private String scenes;

    @ApiModelProperty(value = "操作内容")
    @Excel(name = "操作内容", orderNum = "2")
    private String operationName;

    @ApiModelProperty(value = "操作人")
    @Excel(name = "操作人", orderNum = "3")
    private String operatorName;

    @ApiModelProperty(value = "操作时间")
    @Excel(name = "操作时间", orderNum = "4")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createdAt;

}
