package com.navigator.admin.pojo.dto.rule;

import com.navigator.common.enums.RuleModuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 18:19
 **/
@Data
@Accessors(chain = true)
public class RuleMatchDTO {
    /**
     * 全部数据
     */
    Map<String, Object> mapBizData = new HashMap<>();

    /**
     * 需要执行规则脚本的业务信息
     */
    private List<RuleReferInfoDTO> ruleReferInfoList;

    /**
     * {@link RuleModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;

    /**
     * 是否需要命中的具体的规则
     */
    private Boolean needMatchDetailRule = false;
}
