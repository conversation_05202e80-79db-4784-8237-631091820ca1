package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class WarehouseImportDTO {
    @Excel(name = "ID")
    private String id;

    @Excel(name = "提货库点ID")
    private String sourceId;

    @Excel(name = "库点名称")
    private String warehouseName;

    @Excel(name = "库点编码")
    private String warehouseCode;

    @Excel(name = "地理城市(Y)")
    private String geoCity;

    @Excel(name = "库点类型")
    private String warehouseType;

    @Excel(name = "地理区域")
    private String geoRegion;

    @Excel(name = "是否交割(Y)")
    private String isDelivery;

    @Excel(name = "LDC库/外库(S)")
    private String isLDC;

    @Excel(name = "是否不定库(Y)")
    private String isVariable;

    @Excel(name = "交货地点(S)")
    private String deliveryPoint;

    @Excel(name = "地址(S)")
    private String address;

    @Excel(name = "关联帐套(Y)")
    private String accountBook;

    @Excel(name = "ATLAS warehouse")
    private String atlasWarehouse;

    @Excel(name = "ATLAS terminal")
    private String atlasTerminal;

    @Excel(name = "状态(Y)")
    private String status;
}
