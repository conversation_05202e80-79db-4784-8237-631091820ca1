package com.navigator.admin.facade.rule;

import com.navigator.admin.pojo.dto.rule.ConditionVariableDTO;
import com.navigator.admin.pojo.dto.rule.RuleCreateDTO;
import com.navigator.admin.pojo.dto.rule.RuleQueryDTO;
import com.navigator.admin.pojo.dto.rule.RuleScriptDTO;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 11:41
 **/
@FeignClient(value = "navigator-admin-service")
public interface BusinessRuleFacade {
    /**
     * @param ruleCreateDTO 保存或更新加载条件信息详情
     * @return 条件信息规则
     */
    @PostMapping("/recordBusinessRule")
    BusinessRuleEntity recordBusinessRule(@RequestBody RuleCreateDTO ruleCreateDTO);

    /**
     * 拼接加载条件的规则脚本、和规则描述信息
     *
     * @param conditionVariableList
     * @return
     */
    @PostMapping("/jointRuleConditionInfo")
    RuleScriptDTO jointRuleConditionInfo(@RequestBody List<ConditionVariableDTO> conditionVariableList);

    /**
     * 回显规则详情信息
     *
     * @return
     */
    @PostMapping("/getRuleDetailByBusinessCode")
    BusinessRuleEntity getRuleDetailByBusinessCode(@RequestBody RuleQueryDTO ruleQueryDTO);
}
