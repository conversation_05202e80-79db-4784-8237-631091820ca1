package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.DepositRuleEntity;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;

/**
 * <p>
 * 保证金规则配置对外暴露服务
 * </p>
 *
 * <AUTHOR>
 * @since 2021/12/27
 */
@FeignClient(value = "navigator-admin-service")
public interface DepositRuleFacade {

    /**
     * 获取保证金规则
     *
     * @param depositRuleEntity
     * @return
     */
    @PostMapping("/contract/createDepositRule")
    Result createDepositRule(@RequestBody DepositRuleEntity depositRuleEntity);

    /**
     * 获取保证金规则
     *
     * @param depositRuleDTO
     * @return
     */
    @PostMapping("/contract/findDepositRule")
    Result findDepositRule(@RequestBody DepositRuleDTO depositRuleDTO);

    /**
     * 保证金的使用
     *
     * @param depositRuleDTO
     * @return
     */
    @PostMapping("/contract/calcContractUseDeposit")
    BigDecimal calcContractUseDeposit(@RequestBody DepositRuleDTO depositRuleDTO);
}
