package com.navigator.admin.pojo.vo.columbus;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CRoleListVO {
    private Integer roleDefId;
    private String roleDefName;
    private List<CRoleVO> roleList;
    private List<Integer> categoryIdList;
    private List<String> categoryNameList;
    private List<Integer> salesTypeList;
}
