package com.navigator.admin.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 付款条件的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@Accessors(chain = true)
public class PayConditionDTO {

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "Lkg代码")
    private String lkgCode;

    @ApiModelProperty(value = "Atlas代码")
    private String atlasCode;

    @ApiModelProperty(value = "状态 0-无效 1-有效")
    private Integer status;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "履约保证金比例")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "code")
    private String payConditionCode;

}
