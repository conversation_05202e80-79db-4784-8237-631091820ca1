package com.navigator.admin.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/30 10:59
 */

@Data
@Accessors(chain = true)
public class QueryTemplateAttributeDTO {

    /**
     * 货品品类ID
     */
    private Integer categoryId;

    /**
     * 1采购 2销售
     */
    private Integer salesType;
    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 模版类型（-1 0 订单 1 大合同）
     */
    private Integer templateType;

    /**
     * 合同操作类型
     */
    private Integer actionType;
    /**
     * TT-交易类型
     */
    private Integer tradeType;

    /**
     * 协议类型:0.补充协议 1.尾量终止
     */
    private Integer signType;

}
