package com.navigator.admin.pojo.vo.systemrule;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class SystemRuleVO {


    private Integer ruleId;

    private String ruleName;

    private String ruleCode;

    private Integer categoryId;

    private String categoryName;

    private List<SystemRuleItemVO> systemRuleItemVOList;

    @Data
    @Accessors(chain = true)
    public static class SystemRuleItemVO {
        /**
         * 配置明细Id
         */
        private Integer ruleItemId;
        /**
         * 规则ID
         */
        private Integer ruleId;

        private String ruleCode;

        private String lkgCode;
        /**
         * 配置属性
         */
        private String ruleItemKey;

        /**
         * 配置值
         */
        private String ruleItemValue;

        /**
         * 附加子段
         */
        private String memo;
        /**
         * 重量检验（1采购 2销售）
         */
        private Integer valueType;

        /**
         * 状态
         */
        private Integer status;

        private String categoryName;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createdAt;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updatedAt;

        private String createdBy;

        private String updatedBy;

        /**
         * 工厂编码集合
         */
        private List<String> factoryCodeList;
        /**
         * 交货开始月份集合（2022/05）
         */
        private List<String> deliveryBeginDateList;

        private Integer goodsId;

        private String goodsName;

        /**
         * 规格名称
         */
        private String attributeValueName;

        /**
         * 规格id
         */
        private Integer attributeValueId;

        /**
         * 所属主体Id(命名错误)
         */
        private List<Integer> companyId;

        /**
         * 所属主体Id
         */
        private String companyIds;
        /**
         * 所属主体名称
         */
        private String companyName;
    }

}
