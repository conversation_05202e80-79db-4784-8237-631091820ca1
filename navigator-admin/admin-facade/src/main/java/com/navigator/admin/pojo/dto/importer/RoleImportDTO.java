package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class RoleImportDTO {
    @Excel(name = "ID")
    private String id;

    @Excel(name = "角色名称")
    private String roleName;

    @Excel(name = "角色类型")
    private String roleType;

    @Excel(name = "是否区分品类")
    private String isCategory;

    @Excel(name = "是否区分主体")
    private String isCompany;

    @Excel(name = "是否区分工厂")
    private String isFactory;

    @Excel(name = "二级品类")
    private String category;

    @Excel(name = "主体")
    private String company;

    @Excel(name = "工厂")
    private String factory;

    @Excel(name = "操作")
    private String operation = "";

}
