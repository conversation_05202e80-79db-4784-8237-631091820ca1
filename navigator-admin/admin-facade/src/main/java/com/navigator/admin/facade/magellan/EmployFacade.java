package com.navigator.admin.facade.magellan;

import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.EmployDetailVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/1 13:39
 */

@FeignClient(value = "navigator-admin-service")
public interface EmployFacade {
    /**
     * 查詢员工信息(根据email)
     *
     * @param email
     * @return
     */
    @GetMapping("/magellan/getEmployByEmail")
    Result getEmployByEmail(@RequestParam("email") String email, @RequestParam("system") Integer system);

    /**
     * 查詢员工信息（根据电话）
     *
     * @param phone
     * @return
     */
    @GetMapping("/magellan/getEmployByPhone")
    Result getEmployByPhone(@RequestParam("phone") String phone, @RequestParam("system") Integer system);

    @GetMapping("/magellan/getEmployById")
    EmployEntity getEmployById(@RequestParam("id") Integer id);


    /**
     * 查詢员工信息（根据昵称）
     *
     * @param nickName
     * @return
     */
    @GetMapping("/magellan/getEmployNickName")
    EmployEntity getEmployNickName(@RequestParam("nickName") String nickName, @RequestParam("system") Integer system);

    /**
     * 查詢员工信息(根据employId)
     *
     * @param employIds 查詢员工信息(根据employIds)
     * @param employIds
     * @return
     */
    @GetMapping("/magellan/getEmployByEmployId")
    List<EmployEntity> getEmployByEmployIds(@RequestParam("employIds") List<Integer> employIds);


    /**
     * 保存员工信息
     *
     * @param employEntity
     * @return
     */
    @PostMapping("/magellan/saveEmploy")
    Integer saveEmploy(@RequestBody EmployEntity employEntity);

    /**
     * 修改员工信息
     *
     * @param employEntity
     * @return
     */
    @PostMapping("/magellan/editEmploy")
    Integer editEmploy(@RequestBody EmployEntity employEntity);

    /**
     * 刪除员工信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/magellan/deleteEmploy")
    Integer deleteEmploy(@RequestParam("employId") String employId);

    /**
     * 查询是角色管理员的员工
     *
     * @return
     */
    @GetMapping("/magellan/findRoleAdministratorEmploys")
    Result findRoleAdministratorEmploys();

    /**
     * 如果不存在就新增
     *
     * @param employEntity
     * @return
     */
    @PostMapping("/magellan/ifNotExistToSave")
    EmployEntity ifNotExistToSave(@RequestBody EmployEntity employEntity);


    /**
     * 根据角色名称去获取员工id集合
     *
     * @param roleName
     * @return
     */
    @GetMapping("/magellan/getEmployByRoleName")
    List<EmployEntity> getEmployByRoleName(@RequestParam("roleName") String roleName);

    /**
     * 根据CompnyId查询账号列表（区分企业个人账号）
     *
     * @param companyId
     * @return
     */
    @GetMapping("/magellan/queryEmployByCompanyId")
    List<EmployVO> queryEmployByCompanyId(@RequestParam("companyId") Integer companyId);


    /**
     * 重置客户密码
     *
     * @param id
     * @return
     */
    @GetMapping("/magellan/updateEmployResetPassword")
    Result updateEmployResetPassword(@RequestParam("id") Integer id);

    /**
     * 修改用户名的密码
     * 要求：不允许修改的密码是初始密码
     *
     * @param employEntity
     * @return
     */
    @PostMapping("/magellan/modifyPassword")
    Result modifyPassword(@RequestBody EmployEntity employEntity);

    /**
     * 根据客户id 查询出员工信息
     *
     * @param customerId
     * @return
     */
    @GetMapping("/magellan/queryEmployByCustomerId")
    EmployEntity queryEmployByCustomerId(@RequestParam("customerId") Integer customerId);


    /**
     * 保存员工信息
     *
     * @param employBusinessDTO
     * @return
     */
    @PostMapping("/magellan/modifyEmploy")
    Result modifyEmploy(@RequestBody EmployBusinessDTO employBusinessDTO);


    /**
     * 查詢员工信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/magellan/queryEmployList")
    Result queryEmployList(@RequestBody QueryDTO<EmployDTO> queryDTO);


    /**
     * 查詢员工详情信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/magellan/queryEmployDetail")
    EmployDetailVO queryEmployDetail(@RequestParam("employId") Integer employId);

    /**
     * 查詢员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/magellan/queryEmployListByRoleDefId")
    Result<List<EmployVO>> queryEmployListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    /**
     * 重置客户密码
     *
     * @param employId
     * @return
     */
    @GetMapping("/magellan/resetPassword")
    String resetPassword(@RequestParam("employId") Integer employId);

    /**
     * 查詢员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/magellan/queryAvailableEmployByRoleDefId")
    Result<List<EmployVO>> queryAvailableEmployByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    @GetMapping("/magellan/queryCategoryFactoryByRole")
    Result queryCategoryFactoryByRole();

    @GetMapping("/magellan/queryPermission")
    PermissionBO queryPermission(@RequestParam("employId") String employId, @RequestParam("categoryId") Integer categoryId);

    @GetMapping("/magellan/querySitePermission")
    PermissionBO querySitePermission(@RequestParam("employId") String employId, @RequestParam("category2") Integer category2);

    @PostMapping("/magellan/updateEmployStatus")
    Result updateEmployStatus(@RequestBody EmployDTO employDTO);

    @PostMapping("/magellan/saveEmployStatus")
    Result saveEmployStatus(EmployEntity employEntity);

    @PostMapping("/magellan/resetUserPassword")
    Result resetUserPassword(@RequestBody ResetPasswordDTO resetPasswordDTO);

    @PostMapping("/magellan/sendResetPasswordCode/{mobileNo}")
    Result sendResetPasswordCode(@PathVariable("mobileNo") String mobileNo);

    @PostMapping("/magellan/sendAadCode/{mobileNo}")
    Result sendAadCode(@PathVariable("mobileNo") String mobileNo);

    @PostMapping("/magellan/verifyAadCode")
    Result verifyAadCode(@RequestBody LoginDTO loginDTO);

    /**
     * 角色数据导入
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping(value = "/magellan/importEmploy", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importEmploy(@RequestPart("file") MultipartFile file);

    /**
     * 根据虚角色id ,品类, 主体 获取员工id集合
     * Jason
     *
     * @param
     * @return
     */
    @GetMapping("/magellan/getEmploy")
    List<EmployEntity> getEmploy(@RequestParam("roleDefId") String roleDefId, @RequestParam("categoryId") String categoryId, @RequestParam(value = "factoryId", required = false) String factoryId);

    /**
     * 根据虚角色code ,品类, 主体 获取员工id集合
     * Jason
     *
     * @param
     * @return
     */
    @GetMapping("/magellan/getEmployByRoleDefCode")
    List<EmployEntity> getEmployByRoleDefCode(@RequestParam("roleDefCode") String roleDefCode, @RequestParam("categoryId") String categoryId, @RequestParam("factoryId") String factoryId);


    /**
     * 通过实角色id列表，可以获取所有的人员
     * jason
     *
     * @param roleIds
     * @return
     */
    @PostMapping("/magellan/queryEmployByRoleIds")
    List<EmployEntity> queryEmployByRoleIds(@RequestParam("roleIds") List<Integer> roleIds);


    /**
     * 查詢员工信息
     *
     * @param email
     * @return
     */
    @GetMapping("/magellan/getEmployByEmailOrPhone")
    Result getEmployByEmailOrPhone(@RequestParam("email") String email, @RequestParam("phone") String phone, @RequestParam("system") Integer system);

    @PostMapping("/magellan/queryAvailableEmployByRoleId")
    Result queryChoosedEmployByRoleId(@RequestBody QueryDTO<RoleDTO> queryDTO);

    @PostMapping(value = "/magellan/saveOrUpdateEmployByFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result saveOrUpdateEmployByFile(@RequestPart("file") MultipartFile file);


    @PostMapping("/magellan/createEmploy")
    Result createEmploy(@RequestBody EmployBusinessDTO employBusinessDTO);

    @GetMapping("/magellan/exportEmployRoleList")
    Result exportEmployRoleList();

    @GetMapping("/magellan/getEmployCache")
    String getEmployCache(@RequestParam(value = "id", required = false) Integer id);

}
