package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName dba_factory_company
 */
@TableName(value ="dba_factory_company")
@Data
@Accessors(chain = true)
public class FactoryCompanyEntity implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Integer factoryId;

    /**
     * 
     */
    private Integer companyId;

    /**
     * 
     */
    private Integer status;

    /**
     * 
     */
    private Integer isDeleted;

    /**
     * 
     */
    private Date createdAt;

    /**
     * 
     */
    private Date updatedAt;

    /**
     * 
     */
    private String createdBy;

    /**
     * 
     */
    private String updatedBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}