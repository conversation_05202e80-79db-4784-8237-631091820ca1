package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.SignatureDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "navigator-admin-service")
public interface UserFacade {

    /**
     * @description: magellan登录
     * @param: [loginDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/login")
    Result login(@RequestBody LoginDTO loginDTO);


    /**
     * @description: columbus登录
     * @param: [loginDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/loginByPhone")
    Result loginByPhone(@RequestBody LoginDTO loginDTO);

    @PostMapping("/completeSignature")
    Result completeSignature(@RequestBody SignatureDTO signatureDTO);

    @GetMapping("/test")
    Result test();
}
