package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 贸易节假日表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Data
@Accessors(chain = true)
@TableName("dba_trade_day")
@ApiModel(value = "TradeDayEntity对象", description = "贸易节假日表")
public class TradeDayEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "日")
    private Integer day;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String dayValue;

    @ApiModelProperty(value = "是否是法定节假日（0非法定节假日；1法定节假日）")
    private Integer isHoliday;

    @ApiModelProperty(value = "周几")
    private Integer weekDay;

    @ApiModelProperty(value = "是否为交易日（0否；1是）")
    private Integer isTrade;

    @ApiModelProperty(value = "是否为工作日（0否；1是）")
    private Integer isWorkDay;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;
}
