package com.navigator.admin.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 付款条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@Accessors(chain = true)
@TableName("dba_pay_condition")
@ApiModel(value = "PayConditionEntity对象", description = "付款条件表")
public class PayConditionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "条件名称")
    private String name;

    @ApiModelProperty(value = "条件代码")
    @Excel(name = "LKG代码")
    private String code;

    @ApiModelProperty(value = "ATLAS条件代码")
    @Excel(name = "ATLAS代码")
    private String mdmPayConditionCode;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "业务线")
    @Excel(name = "业务线")
    private String buCode;

    @ApiModelProperty(value = "付款方式(1:赊销 2:预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "赊销账期")
    @Excel(name = "赊销天数")
    private Integer creditDays;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "履约保证金比例")
    @Excel(name = "履约保证金比例", suffix = "%")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    @Excel(name = "履约保证金点价后补缴", suffix = "%")
    private Integer addedDepositRate;

    @ApiModelProperty(value = "发票后补缴货款比例")
    @Excel(name = "发票后补缴货款比例", suffix = "%")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "合同销售类型（0.通用 1.采购 2.销售）")
    @Excel(name = "合同销售类型", replace = {"采/销_0", "采购_1", "销售_2"})
    private Integer salesType;

    @ApiModelProperty(value = "状态 0-禁用1-启用")
    @Excel(name = "状态", replace = {"禁用_0", "启用_1"})
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "创建时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    @Excel(name = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
}
