package com.navigator.admin.pojo.dto.rule;

import com.navigator.bisiness.enums.PatternRelationEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 11:51
 **/
@Data
@Accessors(chain = true)
public class ConditionVariableDTO {
    /**
     * 条件变量ID
     */
    private Integer conditionVariableId;
    /**
     * 条件变量
     */
    private String conditionVariable;
    /**
     * 运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)
     * {@link PatternRelationEnum}
     */
    private String patternRelation;
    /**
     * 加载条件阈值信息
     */
    private String conditionValue;
    /**
     * 加载条件阈值信息(当包含关系传)
     */
    private List<String> conditionValueList;
    private String conditionValueIds;
    /**
     * 逻辑关系(&&、||)
     */
    private String logicRelation;
    /**
     * 优先级
     */
    private Integer level;
    /**
     * 显示顺序
     */
    private Integer sort;

    //--------------中文显示给前端----------------
    /**
     * 加载条件阈值信息ID
     */
    private List<Integer> conditionValueIdList;
    /**
     * 加载条件阈值信息ID
     */
    private List<String> conditionDescList;
    /**
     * 条件变量
     */
    private String conditionVariableInfo;
    /**
     * 运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)
     * {@link PatternRelationEnum}
     */
    private String patternRelationInfo;
    /**
     * 加载条件阈值信息
     */
    private String conditionValueInfo;
    /**
     * 逻辑关系(&&、||)
     */
    private String logicRelationInfo;
    /**
     * 加载规则信息（drools脚本）
     */
    private String ruleInfo;

    private String modifyPatternRelation;
}
