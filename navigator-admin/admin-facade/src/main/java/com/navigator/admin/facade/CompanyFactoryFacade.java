package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface CompanyFactoryFacade {

    @PostMapping("/queryCompanyFactoryList")
    List<FactoryCompanyEntity> queryCompanyFactoryList();

    @PostMapping("/queryFactoryByCompanyId")
    List<FactoryCompanyEntity> queryFactoryByCompanyId(@RequestParam("companyId") Integer companyId);

    @PostMapping("/saveFactoryCompany")
    void saveFactoryCompany(@RequestBody FactoryCompanyDTO factoryCompanyDTO);

}
