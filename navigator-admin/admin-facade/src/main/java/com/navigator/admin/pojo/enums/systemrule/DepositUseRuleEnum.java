package com.navigator.admin.pojo.enums.systemrule;

import lombok.Getter;

@Getter
public enum DepositUseRuleEnum {
    RATIO(1, "随车按比例释放"),
    LAST(2, "抵扣最后一笔"),
    ;

    int value;
    String desc;

    DepositUseRuleEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DepositUseRuleEnum getByValue(Integer value) {
        if (null == value) {
            return DepositUseRuleEnum.RATIO;
        }
        for (DepositUseRuleEnum en : DepositUseRuleEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return DepositUseRuleEnum.RATIO;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
