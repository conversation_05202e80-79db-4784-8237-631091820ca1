package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.CompanyDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface CompanyFacade {

    /**
     * 查询所有主体
     *
     * @param
     * @return
     */
    @PostMapping("/queryCompanyList")
    List<CompanyEntity> queryCompanyList();

    @GetMapping("/getCompanyByCode")
    CompanyEntity getCompanyByCode(@RequestParam(value = "companyCode") String companyCode);

    @GetMapping("/getAllCompany")
    List<CompanyEntity> getAllCompany();

    @GetMapping("/queryCompanyById")
    CompanyEntity queryCompanyById(@RequestParam(value = "id") Integer id);

    @GetMapping("/queryCompanyByName")
    CompanyEntity queryCompanyByName(@RequestParam(value = "name") String name);

    @PostMapping("/queryCompanyDTOList")
    List<CompanyDTO> queryCompanyDTOList();

    @PostMapping("/saveCompany")
    Result saveCompany(@RequestBody CompanyDTO companyDTO);

    @PostMapping("/updateCompany")
    Result updateCompany(@RequestBody CompanyDTO companyDTO);

    @PostMapping("/updateCompanyStatus")
    Result updateCompanyStatus(@RequestBody CompanyDTO companyDTO);

    @GetMapping("/getAllCompanyBySyncSystem")
    List<CompanyEntity> getAllCompanyBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem);
}
