package com.navigator.admin.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@Accessors(chain = true)
public class ResetPasswordDTO {
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "1\\d{10}", message = "请输入正确手机号")
    private String phone;
    @NotBlank(message = "验证码不能为空")
    private String verifyCode;
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = "^(?=.*\\d)(?=.*[a-zA-Z]).{6,18}$", message = "密码必须包含字母和数字的组合，长度在6-18之间")
    private String password;

    private String employId;
}
