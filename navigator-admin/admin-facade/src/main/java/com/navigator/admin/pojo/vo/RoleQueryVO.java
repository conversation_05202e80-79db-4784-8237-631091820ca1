package com.navigator.admin.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class RoleQueryVO implements Serializable {
    private Integer roleDefId;
    private String name;
    private List<String> factoryNameList;
    private List<String> categoryNameList;
    private String createByName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    private List<Integer> factoryIdList;
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

    @ApiModelProperty(value = "角色类型")
    private Integer roleType;

    private Integer roleId;

    private String code;

}
