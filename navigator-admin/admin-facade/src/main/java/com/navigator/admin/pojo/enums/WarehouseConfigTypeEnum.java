package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WarehouseConfigTypeEnum {

    GENERAL(0, "通用"),
    FACTORY(1, "发货"),
    DELIVERY(2, "提货"),
    ;

    private final int value;
    private final String desc;

    public static WarehouseConfigTypeEnum getByValue(int value) {
        for (WarehouseConfigTypeEnum item : WarehouseConfigTypeEnum.values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

}
