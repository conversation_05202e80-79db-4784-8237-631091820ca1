package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * columbus账号角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_c_employ_role")
@ApiModel(value = "CEmployRoleEntity对象", description = "columbus账号角色表")
public class CEmployRoleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer employId;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "角色定义id")
    private Integer roleDefId;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除;（0未被删除 1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<CEmployRoleEntity> lqw(EmployRoleQO condition) {
        LambdaQueryWrapper<CEmployRoleEntity> lqw = new LambdaQueryWrapper<CEmployRoleEntity>().eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getEmployId()), CEmployRoleEntity::getEmployId, condition.getEmployId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCustomerId()), CEmployRoleEntity::getCustomerId, condition.getCustomerId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleId()), CEmployRoleEntity::getRoleId, condition.getRoleId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleDefId()), CEmployRoleEntity::getRoleDefId, condition.getRoleDefId());
        }
        lqw.orderByDesc(CEmployRoleEntity::getId);
        return lqw;
    }
}
