package com.navigator.admin.pojo.dto.systemrule;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/12
 */
@Data
@Accessors(chain = true)
public class ImportLOAExcelDTO {

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "所属主体")
    private String companyName;

    @Excel(name = "阈值编码")
    private String ruleItemKey;

    @Excel(name = "阈值名称")
    private String ruleItemKeyName;

    @Excel(name = "触发条件")
    private String ruleItemValue;
}
