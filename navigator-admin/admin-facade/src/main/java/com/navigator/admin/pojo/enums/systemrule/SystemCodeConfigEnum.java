package com.navigator.admin.pojo.enums.systemrule;

import com.navigator.common.enums.RuleReferTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Getter
public enum SystemCodeConfigEnum {
    /**
     * 系统配置
     */
    FIRST_TYPE("", "", "1", null),
    SHOU("S0001", "手数配置", "1", null),
    TAX_RATE("S0002", "税率", "1", null),
    DELIVERY_TYPE("S0003", "交提货方式", "1", null),
    DESTINATION("S0004", "目的地配置", "1", null),
    WEIGHT_CHECK_2("S0005", "重量检验", "1", null),
    EXTRA_BASIC_PRICE("S0006", "基差基准价配置", "1", null),
    TAG_CONFIG("S0007", "标签配置", "1", null),
    YYQ_CONFIG("S0008", "易企签配置", "1", null),
    INVOICE_TYPE("S0009", "发票类型", "1", null),
    LOGIN_OVER_TIME("S0010", "账号过期时间配置", "1", null),


    CONTRACT_APPROVE_CONFIG("S0011", "合同审批阈值配置", "1", null),
    //CONTRACT_APPROVE_CONFIG_FL("S0013","FL合同审批阈值配置","1"),
    HUSKY_CONTRACT_PROVIDE("S0014", "数字合同出具开关", "1", null),

    PROTEIN_PRICE_CONFIG("S0015", "蛋白价差配置", "1", null),

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    DELIVERY_CAR_TYPE_CONFIG("S0016", "提货委托车辆类型配置", "1", null),
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    GEO_AREA_CONFIG("S0017", "地理区域配置", "1", null),
    GEO_FACTORY_CONFIG("S0018", "地理工厂配置", "1", null),
    STRATEGIC_ARBITRAGE("S0019", "套利配置", "1", null),
    STANDARD_FILE_CONFIG("S0020", "企标文件配置(特种油脂)", "1", null),
    DELIVER_TEMPLATE_FILE_CONFIG("S0022", "提货委托文件配置", "1", null),
    WEIGHT_TOLERANCE_CONFIG("S0023", "溢短装配置", "1", null),
    DELAY_PAY_FINE_CONFIG("S0024", "供应商迟交付罚金配置", "1", null),

    SECOND_TYPE("", "", "1", null),
//    WEIGHT_CHECK("S1001", "重量检验", "2", null),
//    QUALITY_CHECK("S1002", "质量检验", "2", null),
    PACKAGE_WEIGHT("S1003", "袋皮扣重", "2", null),
//    CONTRACT_PAPER_CONFIG("S1004", "正本配置", "2", null),
    SOYBEAN2_OFF_RATIO("S1005", "豆二注销比例", "1", null),


    /**
     * **************************** CommmonConfigEntity ************************************
     */
    SIGN_PAPER_RULE_CONFIG("S0021", "正本规则配置", "1", RuleReferTypeEnum.SIGN_PAPER_RULE),

    ;


    private String ruleCode;
    private String name;
    /**
     * 查找类型
     */
    private String type;

    private RuleReferTypeEnum ruleReferTypeEnum;


    public static String getTypeByRuleCode(String ruleCode) {
        for (SystemCodeConfigEnum en : SystemCodeConfigEnum.values()) {
            if (en.getRuleCode().equalsIgnoreCase(ruleCode)) {
                return en.getType();
            }
        }
        return null;
    }

    public static String getNameByRuleCode(String ruleCode) {
        for (SystemCodeConfigEnum en : SystemCodeConfigEnum.values()) {
            if (en.getRuleCode().equalsIgnoreCase(ruleCode)) {
                return en.getName();
            }
        }
        return null;
    }

    public static SystemCodeConfigEnum getEnumByRuleCode(String ruleCode) {
        return Arrays.stream(values())
                .filter(systemCodeConfigEnum -> StringUtils.equals(ruleCode, systemCodeConfigEnum.getRuleCode()))
                .findFirst()
                .orElse(SHOU);
    }

    public static RuleReferTypeEnum getRuleReferTypeByRuleCode(String ruleCode) {
        for (SystemCodeConfigEnum en : SystemCodeConfigEnum.values()) {
            if (en.getRuleCode().equalsIgnoreCase(ruleCode)) {
                return en.getRuleReferTypeEnum();
            }
        }
        return null;
    }

    /**
     * 单条配置
     *
     * @return
     */
    public static List<String> getSingleItemConfigList() {
        return Arrays.asList(EXTRA_BASIC_PRICE.getRuleCode(), YYQ_CONFIG.getRuleCode());
    }

    /**
     * lkg编码配置
     *
     * @return
     */
    public static List<String> getLkgCodeConfigList() {
        return Arrays.asList(DESTINATION.getRuleCode(), WEIGHT_CHECK_2.getRuleCode());
    }
}
