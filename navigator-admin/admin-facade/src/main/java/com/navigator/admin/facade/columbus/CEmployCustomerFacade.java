package com.navigator.admin.facade.columbus;

import com.azure.core.annotation.Get;
import com.azure.core.annotation.Post;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/8
 */

@FeignClient(value = "navigator-admin-service")
public interface CEmployCustomerFacade {

    /**
     * cEmploy绑定customerId
     *
     * @param cEmployCustomerEntity
     * @return
     */
    @PostMapping("/saveCEmployCustomer")
    boolean saveCEmployCustomer(@RequestBody CEmployCustomerEntity cEmployCustomerEntity);

    /**
     * 根据用户id和customerId查询数据
     *
     * @param cEmployId
     * @param customerId
     * @return
     */
    @GetMapping("/queryCEmployCustomerByEmployIdAndCustomerId")
    List<CEmployCustomerEntity> queryCEmployCustomerByEmployIdAndCustomerId(@RequestParam(value = "cEmployId", required = false) Integer cEmployId,
                                                                            @RequestParam(value = "customerId", required = false) Integer customerId);

    @GetMapping("/getCEmployCustomerByEmployIdAndCustomerId")
    List<CEmployCustomerEntity> getCEmployCustomerByEmployIdAndCustomerId(@RequestParam(value = "cEmployId", required = false) Integer cEmployId,
                                                                          @RequestParam(value = "customerId", required = false) Integer customerId);

    /**
     * 修改用户主体绑定数据
     *
     * @param cEmployCustomerEntity
     * @return
     */
    @PostMapping("/updateCEmployCustomer")
    boolean updateCEmployCustomer(@RequestBody CEmployCustomerEntity cEmployCustomerEntity);

    /**
     * 根据cEmployId查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/getCEmployCustomerById")
    CEmployCustomerEntity getCEmployCustomerById(@RequestParam(value = "id") Integer id);

    /**
     * 哥伦布用户数据绑定
     */
    @GetMapping("/cEmployDataMigration")
    void cEmployDataMigration();

    @GetMapping("/queryCEmployCustomerVO")
    List<CEmployCustomerVO> queryCEmployCustomerVOByCEmployId(@RequestParam(value = "cEmployId") Integer cEmployId);

    @GetMapping("/getCEmployCustomerPower")
    Result getCEmployCustomerPower(@RequestParam(value = "customerId") Integer customerId);

    @GetMapping("/queryCEmployCustomerByCustomerIdAndType")
    List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndType(@RequestParam(value = "customerId") Integer customerId,
                                                                        @RequestParam(value = "type") Integer type,
                                                                        @RequestParam(value = "status", required = false) Integer status);


    @GetMapping("/verifyUserStatus")
    Result verifyUserStatus(@RequestParam(value = "customerId") Integer customerId);
}
