package com.navigator.admin.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/25 16:36
 */
@Data
@Accessors(chain = true)
public class OperationDetailDTO {

    @ApiModelProperty(value = "ID")
    private Integer id;

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "业务模块")
    private String bizModule;

    /**
     * {@link com.navigator.admin.pojo.enums.OperationSourceEnum}
     */
    @ApiModelProperty(value = "0:客户可见 1：用户可见 9：系统级别")
    private Integer logLevel;

    @ApiModelProperty(value = "0:客户 1：用户 9：系统")
    private Integer source;

    @ApiModelProperty(value = "1：用户操作  9：系统生成")
    private Integer operatorType;

    @ApiModelProperty(value = "操作人id，客户联系人id/用户id")
    private Integer operatorId;

    /**
     * 不用填 记录日志时查询
     */
    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "操作动作")
    private String operationName;

    @ApiModelProperty(value = "附加操作动作")
    private String appendOperationName;

    @ApiModelProperty(value = "操作内容")
    private String operationInfo;

    @ApiModelProperty(value = "关联业务记录ID")
    private Integer referBizId;

    @ApiModelProperty(value = "关联业务记录Code")
    private String referBizCode;

    @ApiModelProperty(value = "元数据Json")
    private String metaData;

    @ApiModelProperty(value = "记录的数据，一般为接口参数")
    private String data;

    @ApiModelProperty(value = "关联操作")
    private String referOperation;

    @ApiModelProperty(value = "关联操作的记录的ID")
    private Integer referOperationRecordId;

    @ApiModelProperty(value = "关联操作的数据Json")
    private String referOperationData;

    @ApiModelProperty(value = "触发系统")
    private String triggerSys;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdAt;

    @ApiModelProperty(value = "目标记录ID")
    private Integer targetRecordId;

    @ApiModelProperty(value = "目标记录类型")
    private String targetRecordType;

    @ApiModelProperty(value = "交易类型")
    private String tradeTypeName;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    private Boolean logTrigger = true;
    private Boolean msgTrigger = true;

    /**
     * 非必传  日志内部使用（上面的参数不支持时可以往这里加）
     */
    private Map<String, Object> map = new HashMap<>();

    @ApiModelProperty(value = "修改后的数据")
    private String afterData;

    @ApiModelProperty(value = "场景")
    private String scenes;
}
