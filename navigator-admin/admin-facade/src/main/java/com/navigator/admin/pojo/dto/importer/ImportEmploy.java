package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class ImportEmploy {
    @Excel(name = "ID", orderNum = "0")
    private String employId;
    @Excel(name = "用户名", orderNum = "0")
    private String name;
    @Excel(name = "邮箱", orderNum = "1")
    private String email;
    @Excel(name = "角色名称", orderNum = "2")
    private String role;
    @Excel(name = "二级品类", orderNum = "3")
    private String category;
    @Excel(name = "工厂", orderNum = "4")
    private String factoryCode;
    @Excel(name = "手机号", orderNum = "5")
    private String phone;
    @Excel(name = "LKG账号ID", orderNum = "6")
    private String lkgCode;
    @Excel(name = "微软ID", orderNum = "7")
    private String micoId;
    @Excel(name = "主体", orderNum = "8")
    private String companyCode;
    @Excel(name = "状态(0禁用 1启用)", orderNum = "9")
    private String status;
    @Excel(name = "更新/删除/新增", orderNum = "10")
    private String operateType = "";

}
