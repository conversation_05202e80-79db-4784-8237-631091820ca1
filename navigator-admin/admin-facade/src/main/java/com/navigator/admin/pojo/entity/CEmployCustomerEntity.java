package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_c_employ_customer")
@ApiModel(value = "CEmployCustomerEntity对象", description = "columbus多主体表")
public class CEmployCustomerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键 ")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户Id")
    private Integer cEmployId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "父客户id")
    private Integer parentCustomerId;

    @ApiModelProperty(value = "账号类型;0 初始管理员")
    private Integer type;

   /* @ApiModelProperty(value = "父客户id")
    private Integer parentCustomerId;*/

    @ApiModelProperty(value = "访问时间")
    private Date visitTime;

    @ApiModelProperty(value = "签署状态;0:未签署 1:已签署")
    private Integer signatureStatus;

    @ApiModelProperty(value = "签署时间")
    private Date signatureTime;

    @ApiModelProperty(value = "签署内容")
    private String signatureContent;

    @ApiModelProperty(value = "账号状态;0:禁用 1: 启用")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除;0未被删除 1已被删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "更新人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;


}
