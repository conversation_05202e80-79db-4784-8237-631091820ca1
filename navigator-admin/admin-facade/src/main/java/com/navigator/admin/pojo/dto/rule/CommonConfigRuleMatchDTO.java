package com.navigator.admin.pojo.dto.rule;

import com.navigator.common.enums.RuleModuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-20 10:41
 **/
@Data
@Accessors(chain = true)
public class CommonConfigRuleMatchDTO {
    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;
    /**
     * {@link RuleModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum}
     */
    @ApiModelProperty(value = "配置组编码")
    private String groupCode;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品种")
    private Integer category3;

    @ApiModelProperty(value = "0：采销 1：采购 2：销售")
    private Integer salesType = 0;

    private String referId;
    private String referCode;
    private String ttCode;
    private String contractCode;
    private String memo;
    /**
     * 全部数据
     */
    Map<String, Object> mapBizData = new HashMap<>();

}
