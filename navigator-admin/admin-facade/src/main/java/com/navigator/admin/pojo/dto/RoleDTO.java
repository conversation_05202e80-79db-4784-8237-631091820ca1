package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RoleDTO {


    private String description;

    private Integer adminId;

    private Integer system;

    private List<String> employIds;

    private List<String> operationIds;
//=======
    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "是否区分品种")
    private Integer isBaseCategory;

    @ApiModelProperty(value = "是否区分主体")
    private Integer isBaseCompany;

    @ApiModelProperty(value = "是否区分工厂")
    private Integer isBaseFactory;

    @ApiModelProperty(value = "品种")
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "工厂")
    private List<Integer> factoryIdList;

    @ApiModelProperty(value = "主体")
    private List<Integer> companyIdList;

    @ApiModelProperty(value = "角色组id")
    private Integer parentId;

    @ApiModelProperty(value = "角色编号不能为空")
    private String code;

    @ApiModelProperty(value = "角色类型")
    private Integer roleType;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

    @ApiModelProperty(value = "角色定义id")
    private Integer roleDefId;

    private Integer importStatus = 0;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    private Integer copyId;

    private String employId;

    private String companyId;

    private List<Integer> targetRoleIdList;
}
