package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LogBizCodeEnum {

    /**
     * TT操作Code
     */
    SBM_S_TT_ADD("saveSalesNewTT", "保存新增豆粕销售TT"),
    SBM_S_TT_REVISE("saveSalesReviseTT", "保存修改豆粕销售TT"),
    SBM_S_TT_SPLIT("saveSalesSplitTT", "保存拆分豆粕销售TT"),
    SBM_S_TT_TRANSFER("saveSalesTransferTT", "保存转月豆粕销售TT"),
    SBM_S_TT_PRICE("saveSalesPriceTT", "保存点价豆粕销售TT"),
    SBM_S_TT_REVERSE_PRICE("saveSalesReversePriceTT", "保存反点价豆粕销售TT"),
    SBM_S_TT_STRUCTURE_PRICE("saveSalesStructureTT", "保存结构化定价豆粕销售TT"),
    SBM_P_TT_BUYBACK("saveSalesBugBackTT", "保存回购豆粕销售TT"),
    SBM_S_TT_WASHOUT("saveSalesWashoutTT", "保存解约定赔豆粕销售TT"),
    SBM_S_TT_CLOSED("saveSalesClosedTT", "保存关闭豆粕销售TT"),
    APPROVE_SALES_TT("approveSalesTT", "审批销售TT"),
    SUBMIT_SALES_TT("submitSalesTT", "提交销售TT"),
    CANCEL_SALES_TT("cancelSalesTT", "撤回销售TT"),
    INVALID_SALES_TT("invalidSalesTT", "作废销售TT"),
    UPDATE_SALES_TT("updateSalesTT", "更新销售TT"),
    DELETE_SALES_TT("deleteSalesTT", "删除销售TT"),
    QUERY_SALES_TT("querySalesTT", "查询销售TT列表"),
    QUERY_DETAIL_SALES_TT("queryDetailSalesTT", "查询销售TT详情"),

    SBM_P_TT_ADD("savePurchaseTT", "保存豆粕采购TT"),
    SBM_P_TT_REVISE("savePurchaseReviseTT", "保存修改豆粕采购TT"),
    SBM_P_TT_SPLIT("savePurchaseSplitTT", "保存拆分豆粕采购TT"),
    SBM_P_TT_TRANSFER("savePurchaseTransferTT", "保存转月豆粕采购TT"),
    SBM_P_TT_PRICE("savePurchasePriceTT", "保存点价豆粕采购TT"),
    SBM_P_TT_REVERSE_PRICE("savePurchaseReversePriceTT", "保存反点价豆粕采购TT"),
    SBM_P_TT_STRUCTURE("savePurchaseStructureTT", "保存结构化定价豆粕采购TT"),
    SBM_P_TT_WASHOUT("savePurchaseWashoutTT", "保存解约定赔豆粕采购TT"),
    SBM_P_TT_CLOSED("savePurchaseClosedTT", "保存关闭豆粕采购TT"),

    APPROVE_PURCHASE_TT("approvePurchaseTT", "审批采购TT"),
    SUBMIT_PURCHASE_TT("submitPurchaseTT", "提交采购TT"),
    CANCEL_PURCHASE_TT("cancelPurchaseTT", "撤回采购TT"),
    INVALID_PURCHASE_TT("invalidPurchaseTT", "作废采购TT"),
    UPDATE_PURCHASE_TT("updatePurchaseTT", "更新采购TT"),
    DELETE_PURCHASE_TT("deletePurchaseTT", "删除采购TT"),
    QUERY_PURCHASE_TT("queryPurchaseTT", "查询采购TT列表"),
    QUERY_DETAIL_PURCHASE_TT("queryDetailPurchaseTT", "查询采购TT详情"),

    SUBMIT_BUYBACK_TT("submitBuybackTT", "提交回购TT"),

    /**
     * 合同操作Code
     */
    NEW_WARRANT_CONTRACT("newWarrantContract", "新增仓单合同"),
    NEW_SALES_CONTRACT("newSalesContract", "新增销售合同"),
    NEW_WARRANT_SALES_CONTRACT("newWarrantSalesContract", "新增仓单销售合同"),
    REVISE_SALES_CONTRACT("reviseSalesContract", "修改销售合同"),
    SPLIT_SALES_CONTRACT("splitSalesContract", "拆分销售合同"),
    SPLIT_WARRANT_SALES_CONTRACT("splitWarrantSalesContract", "拆分仓单销售合同"),
    SPLIT_WARRANT_PURCHASE_CONTRACT("splitWarrantPurchaseContract", "拆分仓单采购合同"),
    NEW_PURCHASE_CONTRACT("newPurchaseContract", "新增采购合同"),
    NEW_WARRANT_PURCHASE_CONTRACT("newWarrantPurchaseContract", "新增仓单采购合同"),
    REVISE_PURCHASE_CONTRACT("revisePurchaseContract", "修改采购合同"),
    SPLIT_PURCHASE_CONTRACT("splitPurchaseContract", "拆分采购合同"),
    NEW_SALES_STRUCTURE_PRICING("newSalesStructurePricingContract", "新增销售结构化定价合同"),
    CHANGE_SALES_CONTRACT("changeSalesContract", "TT撤回新增销售合同"),
    CHANGE_PURCHASE_CONTRACT("changePurchaseContract", "TT撤回新增采购合同"),
    WRITE_OFF_WITH_DRAW("writeOffWithDraw", "注销撤回"),
    INVALID_SALES_CONTRACT("invalidSalesContract", "作废销售合同"),
    INVALID_PURCHASE_CONTRACT("invalidSalesContract", "作废采购合同"),
    FILL_SALES_CONTRACT("fillSalesContract", "补充销售合同"),
    FILL_PURCHASE_CONTRACT("fillPurchaseContract", "补充采购合同"),
    SALES_CONTRACT_BUYBACK("salesContractBuyback", "销售合同回购新增合同"),
    SALES_CONTRACT_WASH_OUT("salesContractWashOut", "销售合同解约定陪"),
    PURCHASE_CONTRACT_WASH_OUT("purchaseContractWashOut", "采购合同解约定陪"),
    SALES_CONTRACT_BUY_BACK("salesContractBuyBack", "销售合同回购"),
    SUBMIT_CONTRACT_CHANGE_EQUITY("submitContractChangeEquity", "提交申请"),
    A_APPROVE_CONTRACT_CHANGE_EQUITY("aApproveContractChangeEquity", "A签审批通过"),
    A_REJECT_CONTRACT_CHANGE_EQUITY("aRejectContractChangeEquity", "A签审批驳回"),
    B_APPROVE_CONTRACT_CHANGE_EQUITY("bApproveContractChangeEquity", "B签审批通过"),
    B_REJECT_CONTRACT_CHANGE_EQUITY("bRejectContractChangeEquity", "B签审批驳回"),
    C_APPROVE_CONTRACT_CHANGE_EQUITY("cApproveContractChangeEquity", "C签审批通过"),
    C_REJECT_CONTRACT_CHANGE_EQUITY("cRejectContractChangeEquity", "C签审批驳回"),
    WARRANT_CONTRACT_WRITE_OFF("warrantContractWriteOff", "仓单合同注销"),
    WARRANT_CONTRACT_INVALID("warrantContractInvalid", "仓单合同作废"),
    WARRANT_CONTRACT_BUY_BACK("warrantContractBuyBack", "仓单合同回购"),
    CONTRACT_PRICE("contractPrice", "合同点价"),
    CONTRACT_MONTH("contractMonth", "合同转月"),
    CONTRACT_REVERSE_PRICE("ContractReversePrice", "合同反点价"),

    /**
     * 审批操作Code
     */
    START_CONTRACT_APPROVE("startContractApprove", "提交合同审批"),
    APPROVE("approve", "审批"),
    APPROVE_PASS("approvePass", "审批通过"),
    APPROVE_REJECT("approveReject", "审批驳回"),


    /**
     * 点价操作code
     */
    NEW_PRICE_APPLY("newPriceApply", "新增申请"),
    PRICE_APPLY_PENDING("priceApplyPending", "挂单"),
    UPDATE_PRICE_APPLY("updatePriceApply", "改单申请"),
    UPDATE_PRICE_APPLY_RECORD("updatePriceApplyRecord", "改单记录"),
    CANCEL_PRICE_APPLY("cancelPriceApply", "撤单申请"),
    CANCEL_PRICE_APPLY_RECORD("cancelPriceApplyRecord", "撤单记录"),
    PRICE_APPLY_TRANSACTION("priceApplyTransaction", "成交"),
    PRICE_APPLY_NOT_TRANSACTION("priceApplyNotTransaction", "未成交"),
    PRICE_APPLY_ALLOCATE("priceApplyTransaction", "分配合同"),
    PRICE_APPLY_ALLOCATE_PASS("priceApplyNotTransaction", "审合同通过"),
    PRICE_APPLY_CONTRARY("priceApplyContrary", "撤回成交"),


    /**
     * 套保平仓
     */
    NEW_POSITION_APPLY("newPositionApply", "新增套保平仓申请"),
    POSITION_APPLY_PENDING("PositionApplyPending", "挂单"),
    POSITION_TRANSACTION("PositionTransaction", "成交"),
    POSITION_NOT_TRANSACTION("PositionNotTransaction", "未成交"),
    CANCEL_POSITION("CancelPosition", "撤单"),
    CHANGE_POSITION("ChangePosition", "改单"),

    /**
     * 协议操作Code
     */
    NEW_SALES_CONTRACT_SIGN("newSalesContract", "新增签署协议"),
    PROVIDE_SALES_CONTRACT_SIGN("provideSalesContract", "出具协议"),
    PASS_SALES_CONTRACT_SIGN("passSalesContract", "协议审核通过"),
    REJECT_SALES_CONTRACT_SIGN("rejectSalesContract", "协议审核驳回"),
    POST_BACK_SALES_CONTRACT_SIGN("postBackSalesContract", "回传电子合同"),
    CONFIRMED_SALES_CONTRACT_SIGN("confirmedSalesContract", "协议确认合规"),
    NOT_CONFIRMED_SALES_CONTRACT_SIGN("notConfirmedSalesContract", "协议确认不合规"),
    ABNORMAL_SALES_CONTRACT_SIGN("cancelSalesContract", "协议异常"),
    INVALID_SALES_CONTRACT_SIGN("invalidSalesContract", "作废协议"),
    START_SIGN_SALES_CONTRACT_SIGN("startSignSalesContract", "启动协议签署"),
    COMPLETE_SIGN_SALES_CONTRACT_SIGN("completeSignSalesContract", "(易企签)协议LDC签署完成"),
    COMPLETE_SIGN_VOLUNTARILY_SIGN("completeSignVoluntarilySign", "(静默签)协议LDC签署完成"),
    COMPLETE_SIGN_SALES_CUSTOMER_SIGN("completeSignSalesCustomer", "(易企签)协议客户签署完成"),
    SUBMIT_PAPER_SIGN("submitPaper", "提交正本信息"),

    /**
     * 信息发送
     */
    SEND_HTML_MAIL("sendHtmlMail", "发送HTML邮件"),
    SEND_CUSTOMER_NOTICE_MAIL("sendCustomerNoticeEmail", "发送用户通知邮件"),

    /**
     * 用户操作
     */
    UPDATE_PASSWORD("updatePassword", "修改密码"),
    CHANGE_CUSTOMER_WHITE_LIST("changeCustomerWhiteList", "修改客户白名单"),
    UPDATE_CUSTOMER_STATUS("updateCustomerStatus", "修改客户状态"),

    /**
     * 提货申请
     */
    SAVE_DELIVERY_APPLY("saveDeliveryApply", "保存提货申请"),
    SUBMIT_DELIVERY_APPLY("submitDeliveryApply", "提交提货申请"),
    CANCEL_DELIVERY_APPLY("cancelDeliveryApply", "撤回提货申请"),
    UPDATE_DELIVERY_APPLY("updateDeliveryApply", "编辑提货申请"),
    INVALID_DELIVERY_APPLY("invalidDeliveryApply", "作废提货申请"),
    APPROVE_DELIVERY_APPLY("approveDeliveryApply", "提货申请审核通过"),
    INVALID_APPROVE_DELIVERY_APPLY("invalidApproveDeliveryApply", "作废待审核审核通过"),
    REJECT_DELIVERY_APPLY("rejectDeliveryApply", "提货申请审核驳回"),
    INVALID_REJECT_DELIVERY_APPLY("invalidRejectDeliveryApply", "作废待审核审核驳回"),
    INPUT_DELIVERY_APPLY_BILL_STATUS("inputDeliveryApplyBillStatus", "录入开单状态"),
    CLOSE_TAIL_NUM("closeTailNum", "尾量关闭"),
    CANCEL_CLOSE_TAIL_NUM("cancelCloseTailNum", "取消尾量关闭"),
    ATLAS_CLOSE_APPLY("atlasCloseApply", "atlas合同关闭"),

    /**
     * LKG
     */
    CONFIRM_LKG_CONTRACT_LOG("confirmLkgContractLog", "确认合规LKG日志"),

    /**
     * 仓单操作
     */
    SAVE_WAREHOUSE_APPLY("saveWarehouseApply", "保存仓单"),
    SUBMIT_WAREHOUSE_APPLY("submitWarehouseApply", "提交仓单"),
    UPLOAD_WAREHOUSE_APPLY("uploadWarehouseApply", "仓单上传"),
    LDC_CANCEL_WAREHOUSE_APPLY("LDCCancelWarehouseApply", "LDC注销"),
    UPDATE_WAREHOUSE_APPLY("updateWarehouseApply", "编辑仓单"),
    TRANSFER_WAREHOUSE_APPLY("transferWarehouseApply", "仓单转让"),
    ALLOCATE_WAREHOUSE_APPLY("allocateWarehouseApply", "仓单分配"),
    WAREHOUSE_CONTRACT_BUY_BACK("warehouseContractBuyBack", "仓单合同回购"),
    ;

    private String bizCode;
    private String msg;

    public static LogBizCodeEnum getByName(String name) {
        for (LogBizCodeEnum logBizCodeEnum : LogBizCodeEnum.values()) {
            if (logBizCodeEnum.name().equalsIgnoreCase(name)) {
                return logBizCodeEnum;
            }
        }
        return null;
    }

}
