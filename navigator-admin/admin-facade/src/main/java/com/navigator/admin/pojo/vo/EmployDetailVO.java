package com.navigator.admin.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class EmployDetailVO {
    @ApiModelProperty(value = "工号")
    private String workNo;
    @ApiModelProperty(value = "用户名")
    private String name;
    @ApiModelProperty(value = "电话")
    private String phone;
    @ApiModelProperty(value = "是否启用(默认是)")
    private Integer status;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "品种")
    private List<String> categoryNameList;
    @ApiModelProperty(value = "主体")
    private List<String> factoryNameList;
    @ApiModelProperty(value = "角色")
    private List<String> roleDefNameList;
    @ApiModelProperty(value = "品种id")
    private List<Integer> categoryIdList;
    @ApiModelProperty(value = "主体id")
    private List<Integer> factoryIdList;
    @ApiModelProperty(value = "角色id")
    private List<Integer> roleDefIdList;
    @ApiModelProperty(value = "所属主体名称")
    private String belongFactoryName;
    @ApiModelProperty(value = "易企签是否实名  0:未实名 1:已实名")
    private Integer yqqAuth;
    @ApiModelProperty(value = "是否使用易企签")
    private Integer useYqq;
    @ApiModelProperty(value = "LKG编号")
    private String nickName;
    private List<CategoryFactoryMenuVO.Category> categoryList;

    private List<CategoryFactoryMenuVO.Factory> factoryList;

    //private List<RoleDef> roleDefList;
    private List<Role> roleIdList;
    @ApiModelProperty(value = "账号")
    private String loginAccount;
    @ApiModelProperty(value = "签章人手机号")
    private String yqqPhone;
    @ApiModelProperty(value = "签章人姓名")
    private String realName;

//    @Data
//    public static class RoleDef {
//        private Integer roleDefId;
//        private String name;
//    }

    @Data
    public static class Role {
        private Integer roleId;
        private String name;
    }
}
