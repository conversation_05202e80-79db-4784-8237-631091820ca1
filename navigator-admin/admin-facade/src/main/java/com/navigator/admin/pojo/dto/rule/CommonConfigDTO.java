package com.navigator.admin.pojo.dto.rule;

import com.navigator.admin.pojo.entity.rule.CommonConfigEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-19 15:08
 **/
@Data
@Accessors(chain = true)
public class CommonConfigDTO extends CommonConfigEntity {

    /**
     * 一级品类名称
     */
    private String categoryName1;
    /**
     * 二级品类名称
     */
    private String categoryName2;
    /**
     * 三级品种名称
     */
    private String categoryName3;
    /**
     * 加载条件
     */
    private List<ConditionVariableDTO> conditionVariableList;

}
