package com.navigator.admin.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class RoleDefVO {
    private Integer id;
    private String roleType;
    private String roleName;
    @ApiModelProperty(value = "是否关联品类 0：无关 1：有关")
    private Integer isBaseCategory;
    @ApiModelProperty(value = "是否主体相关 0：无关 1：有关")
    private Integer isBaseCompany;
    @ApiModelProperty(value = "是否工厂相关 0：无关 1：有关")
    private Integer isBaseFactory;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    @ApiModelProperty(value = "更新人")
    private String updatedBy;
}
