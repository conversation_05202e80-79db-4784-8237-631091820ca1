package com.navigator.admin.pojo.vo.columbus;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class CEmployDetailVO {
    @ApiModelProperty(value = "用户名")
    private String name;
    @ApiModelProperty(value = "电话")
    private String phone;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "是否启用(默认是)")
    private Integer status;
    private List<RoleEmploy> roleDefList;
    private List<RoleEmploy> roleList;
    private List<RoleEmploy> customerList;
    @ApiModelProperty(value = "账号类型;0 初始管理员")
    private Integer type;
    @ApiModelProperty(value = "签署时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signatureTime;

    @Data
    public static class RoleEmploy {
        private Integer customerId;
        private String customerName;
        //0: 父集团 ,1:子公司
        private Integer customerType;
        private List<RoleDetail> roleDetailList;
    }

    @ApiModelProperty(value = "易企签是否实名  0:未实名 1:已实名")
    private Integer yqqAuth;
    @ApiModelProperty(value = "是否使用易企签")
    private Integer useYqq;
    @ApiModelProperty(value = "签章人手机号")
    private String yqqPhone;
    @ApiModelProperty(value = "签章人姓名")
    private String realName;

    @Data
    public static class RoleDetail {
        private Integer customerId;
        private String customerName;
        private Integer roleDefId;
        private String roleDefName;
        private Integer roleId;
        private String name;
    }

    @ApiModelProperty(value = "是否为系统管理员 0:否 1 :是")
    private Integer adminStatus = 0;

    @ApiModelProperty(value = "是否为主管理员")
    private String isAdmin;
}
