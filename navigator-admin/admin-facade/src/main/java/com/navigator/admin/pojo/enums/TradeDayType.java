package com.navigator.admin.pojo.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MDM类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TradeDayType {
    /**
     * 豆粕
     */
    SOYBEAN_MEAL("M", "豆粕"),
    /**
     * 豆油
     */
    SOYBEAN_OIL("Y", "豆油"),
    /**
     * 豆二
     */
    SOYBEAN2("B", "豆二"),
    /**
     * 棕榈油
     */
    PALM_OIL("P", "棕榈油"),
    /**
     * 菜粕
     */
    VEGETABLE_MEAL("RM", "菜粕"),
    /**
     * 菜油
     */
    VEGETABLE_OIL("OI", "菜油"),


    ;

    private String value;
    private String desc;

    /**
     * 根据字典值获取枚举类
     *
     * @return
     */
    public static TradeDayType getByValue(String value) {
        for (TradeDayType item : TradeDayType.values()) {
            if (item.equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否等于某个值
     *
     * @param value
     * @return
     */
    public boolean equals(String value) {
        return this.getValue().equals(value);
    }
}
