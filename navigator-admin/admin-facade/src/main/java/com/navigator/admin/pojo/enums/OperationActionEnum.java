package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/10 13:58
 */
@Getter
@AllArgsConstructor
public enum OperationActionEnum {
    /**
     *
     */
    DEFAULT("default", "默认", "", "EMPLOYEE", ""),
    SUBMIT("submit", "提交申请", "出具合同", "CUSTOMER", ""),
    CREATE_E_CONTRACT("createEContract", "出具电子合同", "合同审核", "EMPLOYEE", ""),
    SIGNATURE_CONTRACT_SIGN("signatureContractSing", "带签章电子合同", "协议发起签章", "EMPLOYEE", ""),
    REVIEW_CONTRACT_PASS("reviewContractPass", "合同审核通过", "签署合同", "EMPLOYEE", ""),
    REVIEW_CONTRACT_REJECT("reviewContractReject", "合同审核驳回", "重新出具合同", "EMPLOYEE", ""),
    CREATE_SIGN_RECORD("createSignRecord", "生成合同签章", "", "SYSTEM", ""),
    SIGN_CONTRACT_YQQ("signContractYqq", "签署合同(电子签)", "客户签章", "CUSTOMER", ""),
    SIGN_CONTRACT_UPLOAD("signContractUpload", "签署合同(上传文件)", "客户签章", "CUSTOMER", ""),
    CUSTOMER_SIGN_CONTRACT_YQQ("customerSignContractYqq", "客户签章(电子签)", "确认合规", "CUSTOMER", ""),
    CUSTOMER_SIGN_CONTRACT_UPLOAD("customerSignContractUpload", "客户签章(上传文件)", "确认合规", "CUSTOMER", ""),
    CUSTOMER_SIGN_CONTRACT_UPLOAD_VALET("customerSignContractUploadValet", "代客户签章(上传文件)", "确认合规", "", ""),
    CONFIRM_CONTRACT_PASS("confirmContractPass", "确认合规通过", "合同生效", "EMPLOYEE", ""),
    CONFIRM_CONTRACT_REJECT("confirmContractReject", "确认合规驳回", "客户签章", "EMPLOYEE", ""),
    CONTRACT_EFFECT("contractEffect", "合同生效", "", "CUSTOMER", ""),
    SAVE_CONTRACT_PAPER("saveContractPaper", "录入正本信息", "", "EMPLOYEE", ""),
    CANCEL_TT("cancelTT", "撤回处理", "", "EMPLOYEE", ""),
    CANCEL_SUBMIT("cancelSubMIT", "撤回申请", "重新提交申请", "EMPLOYEE", ""),
    DELETE_TT("deleteTT", "废除申请", "", "EMPLOYEE", ""),
    RESUBMIT("reSubmit", "重新提交", "出具合同", "EMPLOYEE", ""),
    CONTRACT_INVALID("contractInvalid", "合同作废", "", "CUSTOMER", ""),
    CONTRACT_UPDATED("contractUpdated", "合同已更新", "", "CUSTOMER", ""),
    QUERY("query", "查询", "", "SYSTEM", ""),
    VIEW_DETAILS("viewDetails", "查看", "", "SYSTEM", ""),
    SAVE_INFO("saveTTInfo", "保存信息", "提交申请", "SYSTEM", ""),
    SUBMIT_APPROVE("submitApprove", "提交审批", "审批", "EMPLOYEE", ""),
    START_APPROVE("startApprove", "启动审批流程", "审批", "SYSTEM", ""),
    APPROVE_PASS("approvePass", "审批通过", "", "EMPLOYEE", ""),
    APPROVE_REJECT("approveReject", "审批驳回", "", "EMPLOYEE", ""),
    APPROVE_COMPLETE("approveComplete", "审批完成", "", "EMPLOYEE", ""),
    UPDATE_INFO("updateTTInfo", "更新信息", "提交申请", "EMPLOYEE", ""),
    ABNORMAL_SALES_CONTRACT_SIGN("cancelSalesContract", "协议异常", "", "SYSTEM", ""),
    INVALID_SALES_CONTRACT_SIGN("invalidSalesContract", "作废协议", "", "SYSTEM", ""),
    COMPLETE_SIGN_SALES_CONTRACT_SIGN("completeSignSalesContract", "(易企签)协议LDC签署完成", "", "SYSTEM", ""),
    COMPLETE_SIGN_SALES_CUSTOMER_SIGN("completeSignSalesCustomer", "(易企签)协议客户签署完成", "", "SYSTEM", ""),
    START_SIGN_SALES_CONTRACT_SIGN("startSignSalesContract", "启动协议签署", "", "SYSTEM", ""),


    /**
     * 敏感操作日志
     */
    SAVE_CUSTOMER_CONFIG("saveCustomerConfig", "批量上传", "", "EMPLOYEE", "客户配置"),
    REPLENISH_CUSTOMER_MESSAGE("replenishCustomerMessage", "补充基础数据", "", "EMPLOYEE", "客户配置"),
    UPDATE_SYSTEM_AND_CUSTOMER("updateSystemAndCustomer", "系统及账号更新", "", "EMPLOYEE", "客户配置"),

    SBM_UPDATE_CUSTOMER_PROTOCOL("sbm_updateCustomerProtocol", "框架协议新增/更新", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_TEMPLATE_CONTACT_FACTORY("sbm_updateTemplateContactFactory", "通知人新增/更新", "", "EMPLOYEE", "客户配置"),
    SBM_REDACT_CUSTOMER_DEPOSIT_RATE("sbm_redactCustomerDepositRate", "履约保证金新增", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_CUSTOMER_DEPOSIT_RATE_STATUS_OPEN("sbm_updateCustomerDepositRateStatus", "履约保证金启用", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_CUSTOMER_DEPOSIT_RATE_STATUS_CLOSE("sbm_updateCustomerDepositRateStatus", "履约保证金禁用", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_CUSTOMER_DETAIL("sbm_updateCustomerDetail", "白名单新增/更新", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_CUSTOMER_INVOICE("sbm_updateCustomerInvoice", "发票类型更新", "", "EMPLOYEE", "客户配置"),
    SBM_REDACT_CUSTOMER_BANK("sbm_redactCustomerBank", "账户信息新增/更新", "", "EMPLOYEE", "客户配置"),
    SBM_ADD_CUSTOMER_CREDIT_PAYMENT("sbm_addCustomerCreditPayment", "赊销&预付新增/更新", "", "EMPLOYEE", "客户配置"),
    SBM_UPDATE_NF("sbm_updateNF", "NF&正本新增/更新", "", "EMPLOYEE", "客户配置"),
    UPDATE_CUSTOMER_INVOICE("updateCustomerCreditInvoice", "发票新增/更新", "", "EMPLOYEE", "客户配置"),
    UPDATE_CUSTOMER_GRADE_SCORE("updateCustomerCreditGradeScore", "评级新增/更新", "", "EMPLOYEE", "客户配置"),
    UPDATE_CUSTOMER_DELIVERY_WHITE("updateCustomerDeliveryWhite", "提货白名单新增/更新", "", "EMPLOYEE", "客户配置"),

    SAVE_OR_UPDATE_ROLE("saveOrUpdateRole", "新建角色", "", "EMPLOYEE", "客户角色列表"),
    UPDATE_DEF_ROLE_STATUS_OPEN("updateDefRoleStatus", "启用角色", "", "EMPLOYEE", "客户角色列表"),
    UPDATE_DEF_ROLE_STATUS_CLOSE("updateDefRoleStatus", "禁用角色", "", "EMPLOYEE", "客户角色列表"),

    SAVE_ROLE_MENU("saveRoleMenu", "菜单权限更新", "", "EMPLOYEE", "客户角色管理"),
    SAVE_OR_UPDATE_POWER("saveOrUpdatePower", "操作权限更新", "", "EMPLOYEE", "客户角色管理"),

    SAVE_OR_UPDATE_FACTORY("saveOrUpdateFactory", "工厂设置更新", "", "EMPLOYEE", "通用配置"),
    SET_NOT_TRADE("setNotTrade", "非交易日定义更新", "", "EMPLOYEE", "通用配置"),
    SAVE_OR_UPDATE_SYSTEM_RULE("saveOrUpdateSystemRule", "CLB密码过期时间更新", "", "EMPLOYEE", "通用配置"),
    INVALID_STATUS("invalidStatus", "易企签服务状态变更", "", "EMPLOYEE", "通用配置"),
    //角色权限
    SAVE_ROLE("saveRole", "新建角色", "", "EMPLOYEE", "角色权限"),
    UPDATE_ROLE_MENU("updateRoleMenu", "菜单权限更新", "", "EMPLOYEE", "角色权限"),
    UPDATE_POWER("updatePower", "操作权限更新", "", "EMPLOYEE", "角色权限"),
    COPY_PERMISSION("copyPermission", "复制角色设置", "", "EMPLOYEE", "角色权限"),

    //公告栏
    SAVE_ANNOUNCEMENT("saveAnnouncement", "新增公告栏", "", "EMPLOYEE", "公告栏"),
    MODIFY_ANNOUNCEMENT("modifyAnnouncement", "新增公告栏", "", "EMPLOYEE", "公告栏"),
    //支持文件管理
    SAVE_FILE_RECORD("saveFileRecord", "新增文件管理", "", "EMPLOYEE", "支持文件管理"),
    MODIFY_FILE_RECORD("modifyFileRecord", "更新文件管理", "", "EMPLOYEE", "支持文件管理"),

    //商品管理配置
    MODIFY_GOODS_STRUCTURE("modifyGoodsStructure", "货品配置编辑", "", "EMPLOYEE", "商品管理配置"),
    ADD_GOODS_CATEGORY("addGoodsCategory", "品类管理新增", "", "EMPLOYEE", "商品管理配置"),
    UPDATE_GOODS_SPU_CATEGORY("updateGoodsSPUCategory", "品类管理更新SPU", "", "EMPLOYEE", "商品管理配置"),
    UPDATE_GOODS_SKU_CATEGORY("updateGoodsSKUCategory", "品类管理更新SKU", "", "EMPLOYEE", "商品管理配置"),
    UPDATE_GOODS_CATEGORY("updateGoodsCategory", "品类管理编辑", "", "EMPLOYEE", "商品管理配置"),
    ADD_GOODS_ATTRIBUTE("addGoodsAttribute", "规格管理新增", "", "EMPLOYEE", "商品管理配置"),
    MODIFY_GOODS_ATTRIBUTE("addGoodsAttribute", "规格管理编辑", "", "EMPLOYEE", "商品管理配置"),


    //系统配置
    MIN_AMOUNT("MinAmount", "总金额下限阈值更新", "", "EMPLOYEE", "通用配置"),
    MAX_AMOUNT("MaxAmount", "总金额上限阈值更新", "", "EMPLOYEE", "通用配置"),
    DELIVERY_DUE_MONTH("DeliveryDueMonth", "交期阈值更新", "", "EMPLOYEE", "通用配置"),
    REMAIN_CONTRACT_NUMBER("RemainContractNumber", "可提量阈值更新", "", "EMPLOYEE", "通用配置"),
    UPDATE_SYSTEM_RULE("UPDATE_SYSTEM_RULE", "编辑配置", "", "EMPLOYEE", "通用配置"),
    DELIVERY_TYPE("DELIVERY_TYPE", "交提货方式编辑配置", "", "EMPLOYEE", "通用配置"),
    DESTINATION("DESTINATION", "目的地配置编辑配置", "", "EMPLOYEE", "通用配置"),
    WEIGHT_CHECK_2("WEIGHT_CHECK_2", "重量检验", "", "EMPLOYEE", "通用配置"),
    EXTRA_BASIC_PRICE("EXTRA_BASIC_PRICE", "基差基准价配置", "", "EMPLOYEE", "通用配置"),
    TAG_CONFIG("TAG_CONFIG", "标签配置", "", "EMPLOYEE", "通用配置"),
    YYQ_CONFIG("YYQ_CONFIG", "易企签配置", "", "EMPLOYEE", "通用配置"),
    INVOICE_TYPE("INVOICE_TYPE", "发票类型", "", "EMPLOYEE", "通用配置"),
    LOGIN_OVER_TIME("LOGIN_OVER_TIME", "账号过期时间配置", "", "EMPLOYEE", "通用配置"),
    CONTRACT_APPROVE_CONFIG("CONTRACT_APPROVE_CONFIG", "合同审批阈值配置", "", "EMPLOYEE", "通用配置"),
    HUSKY_CONTRACT_PROVIDE("HUSKY_CONTRACT_PROVIDE", "数字合同出具开关", "", "EMPLOYEE", "通用配置"),
    PROTEIN_PRICE_CONFIG("PROTEIN_PRICE_CONFIG", "蛋白价差配置", "", "EMPLOYEE", "通用配置"),
    DELIVERY_CAR_TYPE_CONFIG("DELIVERY_CAR_TYPE_CONFIG", "提货委托车辆类型配置", "", "EMPLOYEE", "通用配置"),
    GEO_AREA_CONFIG("GEO_AREA_CONFIG", "地理区域配置", "", "EMPLOYEE", "通用配置"),
    GEO_FACTORY_CONFIG("GEO_FACTORY_CONFIG", "地理工厂配置", "", "EMPLOYEE", "通用配置"),
    STRATEGIC_ARBITRAGE("STRATEGIC_ARBITRAGE", "套利配置", "", "EMPLOYEE", "通用配置"),
    STANDARD_FILE_CONFIG("STANDARD_FILE_CONFIG", "企标文件配置(特种油脂)", "", "EMPLOYEE", "通用配置"),
    DELIVER_TEMPLATE_FILE_CONFIG("DELIVER_TEMPLATE_FILE_CONFIG", "提货委托文件配置", "", "EMPLOYEE", "通用配置"),
    WEIGHT_TOLERANCE_CONFIG("WEIGHT_TOLERANCE_CONFIG", "溢短装配置", "", "EMPLOYEE", "通用配置"),
    DELAY_PAY_FINE_CONFIG("DELAY_PAY_FINE_CONFIG", "供应商迟交付罚金配置", "", "EMPLOYEE", "通用配置"),
    PACKAGE_WEIGHT("PACKAGE_WEIGHT", "袋皮扣重", "", "EMPLOYEE", "通用配置"),
    SOYBEAN2_OFF_RATIO("SOYBEAN2_OFF_RATIO", "豆二注销比例", "", "EMPLOYEE", "通用配置"),


    UPDATE_MESSAGE_STATUS("updateMessageStatus", "消息禁用/启用", "", "EMPLOYEE", "消息列表"),
    UPDATE_MESSAGE_TEMPLATE("updateMessageTemplate", "消息模版更新", "", "EMPLOYEE", "消息列表"),
    CHANGE_POSITION("changePosition", "套保平仓改单", "", "EMPLOYEE", "消息列表"),
    CANCEL_POSITION("cancelPosition", "套保平仓撤单", "", "EMPLOYEE", "消息列表"),

    NEW_STRUCTURE("newStructure", "新增结构化类型", "", "EMPLOYEE", "通用配置"),
    MODIFY_STRUCTURE("modifyStructure", "修改结构化类型", "", "EMPLOYEE", "通用配置"),
    UPDATE_STRUCTURE_STATUS("modifyStructure", "启用/禁用结构化类型", "", "EMPLOYEE", "通用配置"),

    PRICE_POWER_TIME_ADD("pricePowerTimeAdd", "特权时间管理新增", "", "PRICE", ""),
    PRICE_POWER_TIME_UPDATE("pricePowerTimeUpdate", "特权时间管理更新", "", "PRICE", ""),
    PRICE_POWER_TIME_DELETE("pricePowerTimeDelete", "特权时间管理删除", "", "PRICE", ""),

    PRICE_GRADE_SETTING_UPDATE("priceGradeSettingUpdate", "特权评级更新", "", "PRICE", ""),


    HUSKY_TEMPLATE_QUALITY_SAVE("huskyTemplateQualitySave","数字合同-质量指标-新增","","QUALITY",""),
    HUSKY_TEMPLATE_QUALITY_UPDATE("huskyTemplateQualityUpdate","数字合同-质量指标-修改","","QUALITY",""),
    HUSKY_TEMPLATE_QUALITY_VALID("huskyTemplateQualityValid","数字合同-质量指标-启用/禁用","","QUALITY",""),

    ADD_DELIVERY_WAREHOUSE("addDeliveryWarehouse", "新增提货库点", "", "EMPLOYEE", "通用配置"),
    UPDATE_DELIVERY_WAREHOUSE("updateDeliveryWarehouse", "修改提货库点", "", "EMPLOYEE", "通用配置"),
    DISABLE_DELIVERY_WAREHOUSE_STATUS("disableDeliveryWarehouseStatus", "禁用提货库点", "", "EMPLOYEE", "通用配置"),
    ENABLE_DELIVERY_WAREHOUSE_STATUS("enableDeliveryWarehouseStatus", "启用提货库点", "", "EMPLOYEE", "通用配置"),
    ;


    private String code;
    private String action;
    private String nextAction;
    private String logLevel;
    private String scenes;

    public static OperationActionEnum getByName(String name) {

        for (OperationActionEnum operationActionEnum : OperationActionEnum.values()) {
            if (operationActionEnum.name().equalsIgnoreCase(name)) {
                return operationActionEnum;
            }
        }
        return OperationActionEnum.DEFAULT;
    }

}
