package com.navigator.admin.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.ContentTypeEnum;
import com.navigator.common.enums.TemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbz_template")
@ApiModel(value = "TemplateEntity对象", description = "模板表")
public class TemplateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @Excel(name = "ID", orderNum = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编号")
    @Excel(name = "编号", orderNum = "2")
    private String code;

    @ApiModelProperty(value = "模板名称")
    @Excel(name = "标题", orderNum = "3")
    private String name;

    /**
     * {@link TemplateTypeEnum}
     */
    @ApiModelProperty(value = "模版类型（1、母版 2、销售合同模版 3、日志模版）")
    @Excel(name = "类型", orderNum = "6")
    private Integer type;

    /**
     * {@link TemplateTypeEnum}
     */
    @ApiModelProperty(value = "子模板ID（当为 M条款/销售合同模版时）")
    @Excel(name = "ID组成", orderNum = "4")
    private String subTemplateIds;

    @ApiModelProperty(value = "子模板Code（当为 M条款/销售合同模版时）")
    private String subCodeList;

    @ApiModelProperty(value = "模板内容")
    @Excel(name = "模版内容", orderNum = "5")
    private String content;

    /**
     * {@link ContentTypeEnum}
     */
    @ApiModelProperty(value = "模板类型(ftl:content模板内容的ftl文件路径   default:默认  normal:content)")
    private String contentType;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "状态")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "模板创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "模板创建者")
    private Integer createdBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "模板修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "模板修改者")
    private Integer updatedBy;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;


}
