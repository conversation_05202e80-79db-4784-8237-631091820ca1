package com.navigator.admin.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class CLBRoleMenuImportDTO {

    @Excel(name = "一级菜单权限")
    private String firstMenu;

    @Excel(name = "二级菜单权限")
    private String secondMenu;

    @Excel(name = "三级菜单权限ID")
    private String menuId;

    @Excel(name = "三级菜单权限")
    private String menuName;

    @Excel(name = "虚角色ID")
    private String roleDefId;

    @Excel(name = "虚角色")
    private String roleDefName;

    @Excel(name = "品类")
    private String category;

    @Excel(name = "采销")
    private String salesType;

    @Excel(name = "操作")
    private String operation = "";

}
