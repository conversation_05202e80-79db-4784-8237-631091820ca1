package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class FileRecordDTO {
    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "公告编号")
    private String code;

    @ApiModelProperty(value = "系统id")
    private String systemId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "url地址")
    private String url;

    @ApiModelProperty(value = "状态：1:启用,0:未启用")
    private String status;
}
