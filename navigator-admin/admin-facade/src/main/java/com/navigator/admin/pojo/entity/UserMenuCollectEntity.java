package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:27
 **/
@Data
@Accessors(chain = true)
@TableName("dba_user_menu_collect")
@ApiModel(value = "UserMenuCollectEntity对象", description = "用户菜单收藏夹表")
public class UserMenuCollectEntity  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID(来源于dba_employ或dba_c_employ表)")
    private Integer userId;

    @ApiModelProperty(value = "客户ID(区分主体)")
    private Integer customerId;

    @ApiModelProperty(value = "系统来源(1 Magellan/2Columbus)")
    private Integer system;

    @ApiModelProperty(value = "收藏的名称")
    private String name;

    @ApiModelProperty(value = "自定义的菜单名称")
    private String newMenuName;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "菜单ID")
    private Integer menuId;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode;

    @ApiModelProperty(value = "菜单路径")
    private String menuUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;
}
