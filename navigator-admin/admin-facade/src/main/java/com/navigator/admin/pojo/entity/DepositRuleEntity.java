package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 保证金规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Data
@Accessors(chain = true)
@TableName("dbt_deposit_rule")
@ApiModel(value = "DbtDepositRuleEntity对象", description = "保证金规则表")
public class DepositRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "客户ID（0默认系统全部 非0 具体客户）")
    private Integer customerId;

    @ApiModelProperty(value = "保证金规则名称")
    private String name;

    @ApiModelProperty(value = "合同类型1、一口价 2基差 3、一口价暂定价 4延期定价 5、结构化定价 6、结价合同）")
    private Integer contractType;

    @ApiModelProperty(value = "操作来源（1、新增 2、变更 3、点价 4 下跌 5、上涨）")
    private String contractScene;

    @ApiModelProperty(value = "场景值类型(1、百分比 2、数值)")
    private Integer sceneValueType;

    @ApiModelProperty(value = "场景值")
    private String sceneValue;

    @ApiModelProperty(value = "资金动作（1、缴纳 2、补齐）")
    private String depositAction;

    @ApiModelProperty(value = "是否循环（0、否 1、是）")
    private Integer isCirculate;

    @ApiModelProperty(value = "数据类型(1、百分比 2、数值)")
    private Integer valueType;

    @ApiModelProperty(value = "数值")
    private String value;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
