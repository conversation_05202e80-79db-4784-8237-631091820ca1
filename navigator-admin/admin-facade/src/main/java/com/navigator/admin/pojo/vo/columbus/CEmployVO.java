package com.navigator.admin.pojo.vo.columbus;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class CEmployVO {
    @ApiModelProperty(value = "所属公司id")
    private Integer companyId;

    @ApiModelProperty(value = "所属部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    @Excel(name = "LKG账号ID", orderNum = "7")
    private String nickName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "工号")
    @Excel(name = "微软ID", orderNum = "8")
    private String workNo;

    @ApiModelProperty(value = "邮箱")
    @Excel(name = "邮箱", orderNum = "2")
    private String email;

    @ApiModelProperty(value = "性别 0未知 1男 2女")
    private Integer sex;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "账号类型;0 初始管理员")
    private Integer type;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "账户是否被锁")
    private Integer lock;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    //====
    @ApiModelProperty(value = "id ")
    private Integer id;

    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名", orderNum = "1")
    private String name;

    @ApiModelProperty(value = "电话")
    @Excel(name = "手机号", orderNum = "6")
    private String phone;

    @ApiModelProperty(value = "角色名称")
    private List<String> roleDefName;

    @ApiModelProperty(value = "账号状态(0:禁用 1: 启用)")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "所属主体")
    @Excel(name = "工厂", orderNum = "5")
    private String factoryName;

    private List<String> categoryNameList;

    @ApiModelProperty(value = "角色名称")
    private List<String> roleName;

    @Excel(name = "角色名称", orderNum = "3")
    private String roleNameString;

    @Excel(name = "品类", orderNum = "4")
    private String categoryNameString;

    @ApiModelProperty(value = "是否为系统管理员 0:否 1 :是")
    private Integer adminStatus;

    @ApiModelProperty(value = "是否为主管理员")
    private String isAdmin;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

}
