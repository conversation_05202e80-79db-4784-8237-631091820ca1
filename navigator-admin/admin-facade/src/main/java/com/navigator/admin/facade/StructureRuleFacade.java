package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "navigator-admin-service")
public interface StructureRuleFacade {

    @PostMapping("/save")
    Result save(@RequestBody StructureRuleDTO structureRuleDTO);

    @PostMapping("/modify")
    Result modify(@RequestBody StructureRuleDTO structureRuleDTO);

    @PostMapping("/updateStatus")
    Result updateStatus(@RequestBody StructureRuleDTO structureRuleDTO);

    @GetMapping("/queryStructureCode")
    Result queryStructureCode();

    @PostMapping("/queryStructureList")
    Result queryStructureList(@RequestBody QueryDTO<StructureRuleDTO> structureRuleDTOQueryDTO);

    @GetMapping("/queryById")
    Result queryById(@RequestParam(value = "id", required = false) Integer id);

    @GetMapping("/getNameById")
    String getNameById(@RequestParam(value = "id", required = false) Integer id);

    @GetMapping("/queryAvailableStructureList")
    Result queryAvailableStructureList();

}
