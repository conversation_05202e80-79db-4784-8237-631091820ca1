package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.admin.pojo.entity.AnnouncementEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "navigator-admin-service")
public interface AnnouncementFacade {

    @PostMapping("/announcement/save")
    Result save(@RequestBody AnnouncementDTO announcementDTO);

    @PostMapping("/announcement/modify")
    Result modify(@RequestBody AnnouncementDTO announcementDTO);

    @PostMapping("/announcement/queryAnnouncementDetail")
    Result queryAnnouncementDetail(@RequestBody AnnouncementDTO announcementDTO);

    @PostMapping("/announcement/queryAnnouncementList")
    Result queryAnnouncementList(@RequestBody QueryDTO<AnnouncementDTO> queryDTO);

    @GetMapping("/announcement/queryAnnouncementBySystem")
    Result queryAnnouncementBySystem(@RequestParam("systemId") Integer systemId);
}
