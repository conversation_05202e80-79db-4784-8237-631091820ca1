package com.navigator.admin.pojo.vo.columbus;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ColumbusAdminVO {
    @ApiModelProperty(value = "主键 ")
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer cEmployId;

    @ApiModelProperty(value = "用户名")
    @Excel(name = "账号名称", orderNum = "3")
    private String name;

    @ApiModelProperty(value = "电话")
    @Excel(name = "手机号", orderNum = "4")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @Excel(name = "邮箱", orderNum = "5")
    private String email;

    @ApiModelProperty(value = "是否为系统管理员 0:否 1 :是")
    private Integer adminStatus;

    @ApiModelProperty(value = "账号状态(0:禁用 1: 启用)")
    private Integer status;

    @ApiModelProperty(value = "linkage客户编码")
    @Excel(name = "客户编码", orderNum = "6")
    private String linkageCustomerCode;

    @ApiModelProperty(value = "客户集团名称")
    @Excel(name = "客户集团名称", orderNum = "1")
    private String enterpriseName;

    @ApiModelProperty(value = "客户名称")
    @Excel(name = "客户名称", orderNum = "2")
    private String customerName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "账号状态(0:禁用 1: 启用)")
    @Excel(name = "状态", orderNum = "10")
    private String statusName;

    @Excel(name = "是否为管理员", orderNum = "7")
    private String adminStatusName;

    @Excel(name = "开通时间", orderNum = "11")
    private String signatureTime;

    @Excel(name = "是否为主管理员", orderNum = "8")
    private String isAdmin;

    @ApiModelProperty(value = "账号类型;0 初始管理员")
    private Integer type;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户Id")
    private Integer customerId;

    @ApiModelProperty(value = "是否是多主体")
    private Integer isCompanyList;

    @ApiModelProperty(value = "是否是多主体")
    @Excel(name = "是否对应多主体", orderNum = "9")
    private String isCompanyListName;
}
