package com.navigator.admin.facade;

import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "导入程序相关")
@FeignClient(value = "navigator-admin-service")
public interface ImportFacade {

    // ======================== 角色相关导入 ========================
    @PostMapping("/role/importRole")
    Result<String> importRole(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/role/importRoleMenu")
    Result<String> importRoleMenu(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/role/importRolePower")
    Result<String> importRolePower(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/role/importCLBRole")
    Result<String> importCLBRole(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/role/importCLBRoleMenu")
    Result<String> importCLBRoleMenu(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/role/importCLBRolePower")
    Result<String> importCLBRolePower(@RequestParam(value = "file") MultipartFile file);

}
