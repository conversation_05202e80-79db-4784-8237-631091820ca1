package com.navigator.admin.pojo.vo.systemrule;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DomainVO {
    /**
     * 品类
     */
    private Integer categoryId;

    /**
     * 期货合约
     */
    private String domain;


    private List<DomainProperty> domainPropertyList;



    @Data
    public class  DomainProperty{
        /**
         * 交易时间
         */
        private Date domainTime;

        /**
         * 交易价格
         */
        private Date domainPrice;
    }

}
