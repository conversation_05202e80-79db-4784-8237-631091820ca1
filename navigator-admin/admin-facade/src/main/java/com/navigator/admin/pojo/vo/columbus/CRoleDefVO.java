package com.navigator.admin.pojo.vo.columbus;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class CRoleDefVO {
    private Integer id;
    private String roleName;
    @ApiModelProperty(value = "品种")
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "品种名称")
    private List<String> categoryNameList;

    @ApiModelProperty(value = "采销类型")
    private List<Integer> salesTypeList;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
