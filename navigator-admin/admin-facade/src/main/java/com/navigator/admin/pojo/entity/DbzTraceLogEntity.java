package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 业务追踪日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022年11月14日
 */
@Data
@Accessors(chain = true)
@TableName("dbz_trace_log")
@ApiModel(value = "DbzTraceLogEntity对象", description = "业务追踪日志表")
public class DbzTraceLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务模块")
    private String bizModule = "";

    @ApiModelProperty(value = "关联记录code")
    private String referBizCode = "";

    @ApiModelProperty(value = "关联记录Id")
    private Integer referBizId = 0;

    @ApiModelProperty(value = "Json数据")
    private String data = "";

    @ApiModelProperty(value = "日志内容")
    private String logInfo = "";

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

}
