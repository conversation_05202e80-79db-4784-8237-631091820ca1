package com.navigator.admin.pojo.dto.columbus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Accessors(chain = true)
public class CRoleDTO {

    @ApiModelProperty(value = "角色定义id")
    private Integer roleDefId;

    @NotBlank(message = "角色名称不能为空")
    private String name;

    @ApiModelProperty(value = "是否区分品种")
    private Integer isBaseCategory;

    @ApiModelProperty(value = "品种")
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "是否区分采销")
    private Integer isSalesType;

    @ApiModelProperty(value = "采销类型")
    private List<Integer> salesTypeList;

    @ApiModelProperty(value = "账号状态")
    private Integer status;

    //getMenuByRoleId

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    private String description;

    private Integer parentId;

    private Integer adminId;

    private List<String> employIds;

    private List<String> operationIds;

    @ApiModelProperty(value = "角色编号不能为空")
    private String code;

    @ApiModelProperty(value = "角色类型")
    private Integer roleType;

    private Integer importStatus = 0;



    private String employId;
}
