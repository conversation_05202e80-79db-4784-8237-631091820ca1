package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
@Accessors(chain = true)
@TableName("dba_employ_role")
@ApiModel(value = "EmployRole对象", description = "")
public class EmployRoleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer employId;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "角色定义id")
    private Integer roleDefId;

    @ApiModelProperty(value = "角色定义id")
    private Integer companyId;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<EmployRoleEntity> lqw(EmployRoleQO condition) {
        LambdaQueryWrapper<EmployRoleEntity> lqw = new LambdaQueryWrapper<EmployRoleEntity>().eq(EmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getEmployId()), EmployRoleEntity::getEmployId, condition.getEmployId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleId()), EmployRoleEntity::getRoleId, condition.getRoleId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleDefId()), EmployRoleEntity::getRoleDefId, condition.getRoleDefId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCompanyId()), EmployRoleEntity::getCompanyId, condition.getCompanyId());
        }
        lqw.orderByDesc(EmployRoleEntity::getId);
        return lqw;
    }

}
