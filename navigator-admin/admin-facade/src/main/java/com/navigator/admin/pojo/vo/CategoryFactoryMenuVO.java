package com.navigator.admin.pojo.vo;

import lombok.Data;

import java.util.List;

@Data
public class CategoryFactoryMenuVO {
    private List<Category> categoryList;

    private List<Factory> factoryList;

    private List<SaleType> saleTypeList;

    @Data
    public static class Category {
        private Integer categoryId;
        private String categoryName;

    }
    @Data
    public static class Factory {
        private Integer factoryId;
        private String factoryName;
    }

    @Data
    public static class SaleType {
        private Integer saleType;
        private String saleTypeName;
    }
}


