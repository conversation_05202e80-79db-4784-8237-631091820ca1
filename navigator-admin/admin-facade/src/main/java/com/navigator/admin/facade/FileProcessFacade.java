package com.navigator.admin.facade;

import com.alibaba.fastjson.JSONException;
import com.navigator.admin.pojo.dto.FileItemDTO;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 17:57
 */
@FeignClient(value = "navigator-admin-service")
public interface FileProcessFacade {


    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    @PostMapping(value = "/singleUpload", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    Result singleUpload(@RequestParam("file") MultipartFile file) throws IllegalStateException, IOException, JSONException;

    /**
     * 文件下载
     *
     * @param path     下载路径
     * @param response 请求
     * @return 下载文档结果
     */
    @GetMapping("/download")
    HttpServletResponse download(@RequestParam("path") String path, HttpServletResponse response);

    /**
     * 根据Url下载文件到指定路径
     *
     * @param fileUrl  文件Url
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @return 下载结果
     */
    @GetMapping("/downLoadFileByUrl")
    Boolean downLoadFileByUrl(@RequestParam(value = "url") String fileUrl,
                              @RequestParam(value = "path") String filePath,
                              @RequestParam(value = "name") String fileName);

    /**
     * 根据文件地址找寻文件
     *
     * @param filePath
     * @return
     */
    @GetMapping("/createFileItem")
    Result createFileItem(@RequestParam(value = "filePath") String filePath,
                          @RequestParam(value = "id") Integer id,
                          @RequestParam(value = "categoryType") Integer categoryType) throws IOException;

    @PostMapping("/saveFileItem")
    Result saveFileItem(@RequestBody FileItemDTO fileItemDTO);

    @GetMapping("/testBlobUtilFileItem")
    Result testBlobUtilFileItem(@RequestParam(value = "url") String url);

    /**
     * html转换pdf
     *
     * @param htmlInfoDTO html_url 文件地址  html_string 文件内容
     * @return
     */
    @PostMapping("/html2Pdf")
    FileBaseInfoDTO html2Pdf(@RequestBody HtmlInfoDTO htmlInfoDTO);

    /**
     * html转换pdf
     *
     * @param htmlInfoDTO html_url 文件地址  html_string 文件内容
     * @return
     */
    @PostMapping("/html2PdfV2")
    Result html2PdfV2(@RequestBody HtmlInfoDTO htmlInfoDTO);

    /**
     * 批量下载合同信息zip
     *
     * @param fileIdList 文件路径集合
     * @param contractId 合同ID
     * @param response   响应
     * @return 候娜娜
     */
    @GetMapping("/downloadContractZip")
    HttpServletResponse downloadContractZip(@RequestParam(value = "fileIdList") List<Integer> fileIdList,
                                            @RequestParam(value = "contractId") Integer contractId,
                                            HttpServletResponse response);


    /**
     * 生成条形码
     *
     * @param orderCode   编号
     * @param displayCode 展示文字信息（条形码下方）
     * @return
     */
    @GetMapping("/generateBarCodeImg")
    FileInfoEntity generateBarCodeImg(@RequestParam(value = "orderCode") String orderCode,
                                      @RequestParam(value = "displayCode", required = false) String displayCode);

    /**
     * 生成二维码
     *
     * @param url 扫描链接
     * @return
     */
    @GetMapping("/generateQrCodeImg")
    FileInfoEntity generateQrCodeImg(@RequestParam(value = "url") String url);

    @GetMapping("/getFileListByIds")
    Result getFileListByIds(@RequestParam(value = "fileIdList")List<Integer> fileIdList);

}
