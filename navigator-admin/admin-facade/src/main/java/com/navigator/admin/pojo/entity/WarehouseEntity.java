package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.enums.WarehouseConfigTypeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * dba_warehouse
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_warehouse")
@ApiModel(value = "WarehouseEntity对象", description = "dba_warehouse")
public class WarehouseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "唯一码(暂定，后续根据唯一码查询配置)")
    private String uniqueCode;

    @ApiModelProperty(value = "类型（新增字段）0：不区分 1：发货 2：提货")
    private Integer type;

    @ApiModelProperty(value = "库点名称")
    private String name;

    @ApiModelProperty(value = "库点编码（LKG编码）")
    private String code;

    @ApiModelProperty(value = "地理区域id")
    private Integer geographicAreaId;

    @ApiModelProperty(value = "地理区域")
    @TableField(exist = false)
    private String geographicArea;

    @ApiModelProperty(value = "地理城市id")
    @TableField(value = "geographic_city_id", updateStrategy = FieldStrategy.IGNORED)
    private Integer geographicCityId;

    @ApiModelProperty(value = "地理城市")
    @TableField(exist = false)
    private String geographicCity;

    @ApiModelProperty(value = "是否交割 1：是 0：否")
    private Integer isDce;

    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @ApiModelProperty(value = "是否不定库 1：是 0：否")
    private Integer isUnset;

    @ApiModelProperty(value = "交货地点")
    private String deliveryPoint;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "关联帐套")
    private String siteCodes;

    @ApiModelProperty(value = "ATLAS warehouse")
    private String atlasWarehouseCode;

    @ApiModelProperty(value = "ATLAS terminal")
    private String atlasTerminalCode;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "原数据的id")
    private Integer sourceId;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<WarehouseEntity> lqw(WarehouseBO condition) {
        LambdaQueryWrapper<WarehouseEntity> lqw = new LambdaQueryWrapper<WarehouseEntity>().eq(WarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getName()), WarehouseEntity::getName, condition.getName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCode()), WarehouseEntity::getCode, condition.getCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getWarehouseType()), WarehouseEntity::getWarehouseType, condition.getWarehouseType());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsUnset()), WarehouseEntity::getIsUnset, condition.getIsUnset());
            lqw.eq(StringUtil.isNotNullBlank(condition.getStatus()), WarehouseEntity::getStatus, condition.getStatus());
            lqw.in(CollUtil.isNotEmpty(condition.getIdList()), WarehouseEntity::getId, condition.getIdList());
            lqw.in(StringUtil.isNotNullBlank(condition.getType()), WarehouseEntity::getType, condition.getType(), WarehouseConfigTypeEnum.GENERAL.getValue());
        }
        lqw.orderByDesc(WarehouseEntity::getId);
        return lqw;
    }

    @TableField(exist = false)
    private String siteNames;

    @TableField(exist = false)
    private List<String> siteCodeList;
}
