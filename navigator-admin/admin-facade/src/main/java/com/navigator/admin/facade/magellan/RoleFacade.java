package com.navigator.admin.facade.magellan;

import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.LinkedHashSet;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/3 17:03
 */
@Api(tags = "magellan角色")
@FeignClient(value = "navigator-admin-service")
public interface RoleFacade {

    /**
     * 保存编辑角色
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping("/magellan/saveOrUpdateRole")
    Result saveOrUpdateRole(@RequestBody RoleDTO roleDTO);

    /**
     * 根据角色列表
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping("/magellan/queryRoleList")
    Result queryRoleList(@RequestBody QueryDTO<RoleQueryDTO> roleQueryDTO);

    /**
     * 根据角色id查找角色
     *
     * @param id
     * @return
     */
    @GetMapping("/magellan/getRoleById")
    RoleEntity getRoleById(@RequestParam("id") Integer id);


    /**
     * 根据角色id查询用户
     *
     * @param roldIds
     * @return
     */
    @GetMapping("/magellan/getRoleListByIds")
    List<RoleEntity> getRoleListByIds(@RequestParam("roldIds") List<Integer> roldIds);

    /**
     * 根据虚角色id查找对应实角色列表
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/magellan/getRoleListByRoleDefId")
    List<RoleEntity> getRoleListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    /**
     * 通过虚角色、品类、主体能够获取实角色列表
     * jason
     *
     * @param roleDefId  角色定义Code
     * @param categoryId 品类Id
     * @param factoryId  主体Id
     * @return
     */
    @GetMapping("/magellan/getRoleListByRoleDefInfo")
    List<RoleEntity> getRoleListByRoleDefInfo(@RequestParam("roleDefId") Integer roleDefId, @RequestParam("categoryId") Integer categoryId, @RequestParam("factoryId") Integer factoryId);

    /**
     * 通过虚角色字符串（多个）、品类、主体获取实角色列表
     * jason
     *
     * @param roleDefIds 角色定义Code列表
     * @param categoryId 品类Id
     * @param siteCode   账套编码
     * @return
     */
    @GetMapping("/magellan/getRoleListByRoleDefInfos")
    Result<List<RoleEntity>> getRoleListByRoleDefInfos(@RequestParam("roleDefIds") String roleDefIds, @RequestParam("categoryId") Integer categoryId, @RequestParam("siteCode") String siteCode);

    @GetMapping("/magellan/getRoleListByRoleDefInfos2")
    List<RoleEntity> getRoleListByRoleDefInfos2(@RequestParam("roleDefIds") String roleDefIds, @RequestParam("categoryId") Integer categoryId, @RequestParam("factoryId") Integer factoryId);


    @GetMapping("/magellan/queryRoleDefDetail")
    Result<RoleQueryVO> queryRoleDefDetail(@RequestParam("roleDefId") Integer roleDefId);

    @PostMapping("/magellan/queryRoleByFactory")
    Result<List<RoleQueryVO>> queryRoleByFactory(@RequestBody EmployRoleDTO employRoleDTO);

    @GetMapping("/magellan/queryRoleByEmployId")
    List<RoleEntity> queryRoleByEmployId(@RequestParam("employId") String employId);

    @GetMapping("/magellan/queryRoleIdsByEmployId")
    Result<List<Integer>> queryRoleIdsByEmployId(@RequestParam("employId") String employId);

    @PostMapping("/magellan/queryRoleDefList")
    Result queryRoleDefList(@RequestBody QueryDTO<RoleQueryDTO> roleQueryDTO);

    @PostMapping("/magellan/saveRole")
    Result saveRole(@RequestBody RoleDTO roleDTO);

    @GetMapping("/queryRoleGroupList")
    Result queryRoleGroupList();

    @GetMapping
    List<RoleEntity> getRoleAllListCode(@RequestParam("code") String code);

    @PostMapping("/copyPermission")
    Result copyPermission(@RequestBody RoleDTO roleDTO);

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    @ApiOperation(value = "根据条件：获取已授权的菜单树及权限树")
    @PostMapping("/role/getRoleAuth")
    Result<RoleAuthDTO> getRoleAuth(@RequestBody RoleAuthQO roleAuthQO);

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部菜单树及已授权菜单ID列表")
    @PostMapping("/role/getRoleAuthMenu")
    Result<RoleAuthMenuDTO> getRoleAuthMenu(@RequestBody RoleAuthMenuQO roleAuthMenuQO);

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部权限树及已授权权限ID列表")
    @PostMapping("/role/getRoleAuthPower")
    Result<RoleAuthPowerDTO> getRoleAuthPower(@RequestBody RoleAuthPowerQO roleAuthPowerQO);

    /**
     * 根据用户ID：获取已授权的账套编码列表
     *
     * @param userId
     * @return
     */
    @ApiOperation(value = "根据用户ID：获取已授权的账套编码列表")
    @GetMapping("/role/queryRoleSiteCodeSet")
    Result<LinkedHashSet<String>> queryRoleSiteCodeSet(@RequestParam(value = "userId", required = false) Integer userId);

    /**
     * 根据用户ID：获取已授权的二级品类编码列表
     *
     * @param userId
     * @return
     */
    @ApiOperation(value = "根据用户ID：获取已授权的二级品类编码列表")
    @PostMapping("/role/queryCategory2List")
    Result<LinkedHashSet<Integer>> queryCategory2List(@RequestParam(value = "userId", required = false) Integer userId);

    /**
     * 根据用户ID：判断是否管理员
     *
     * @param userId
     * @return
     */
    @ApiOperation(value = "根据用户ID：判断是否管理员")
    @PostMapping("/role/isAdmin")
    Result<Boolean> isAdmin(@RequestParam(value = "userId", required = false) Integer userId);

}
