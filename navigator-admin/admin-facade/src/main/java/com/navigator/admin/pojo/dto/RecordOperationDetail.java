package com.navigator.admin.pojo.dto;

import com.navigator.admin.pojo.enums.OperationActionEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RecordOperationDetail {
    private OperationActionEnum operationActionEnum;
    private String dtoData;
    private String beforeData;
    private String afterData;
    private String operationName;
    private Integer referBizId;
    private String referBizCode;
    private String category2Name;
    /**
     * 操作对象名称（客户名、账号名、角色名等）
     */
    private String referName = "";
}
