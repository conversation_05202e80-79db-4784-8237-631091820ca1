package com.navigator.admin.facade;

import com.navigator.admin.pojo.bo.OperationLogBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.DbzRedAlarmEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 17:12
 */
@FeignClient(value = "navigator-admin-service")
public interface OperationLogFacade {
    /**
     * 分页查询操作记录
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryOperationLog")
    Result queryOperationLog(QueryDTO<OperationLogBO> queryDTO);

    /**
     * 记录操作日志并发送消息
     *
     * @param operationDetailDTO
     */
    @PostMapping("/recordOperationLog")
    void recordOperationLog(@RequestBody OperationDetailDTO operationDetailDTO);

    /**
     * 记录操作日志并发送消息
     *
     * @param operationDetailDTO
     */
    @PostMapping("/recordOperationLogOLD")
    void recordOperationLogOLD(@RequestBody OperationDetailDTO operationDetailDTO);

    @PostMapping("/recordOperationLogDetail")
    void recordOperationLogDetail(@RequestBody OperationDetailDTO operationDetailDTO);

    /**
     * 根据业务code查询操作记录
     *
     * @param code
     * @return
     */
    @GetMapping("/queryOperationLogByCode")
    Result queryOperationLogByCode(@RequestParam("code") String code, @RequestParam("logLevel") Integer logLevel, @RequestParam(value = "id", required = false) Integer id);

    @PostMapping(value = "/saveOperationDetail", produces = "application/json;charset=UTF-8")
    void saveOperationDetail(@RequestBody OperationDetailDTO operationDetailDTO);

    /**
     * 合同操作日志
     * @param contractId
     * @param logLevel
     * @return
     */
    @GetMapping("/queryContractOperationLog")
    Result<List<ContractOperationEventDTO>> queryContractOperationLog(@RequestParam("contractId") Integer contractId, @RequestParam("logLevel") Integer logLevel);

    @GetMapping("/queryOperationDetailByReferBizCode")
    Result queryOperationDetailByReferBizCode(@RequestParam("referBizCode") String referBizCode, @RequestParam("logLevel") Integer logLevel);

    /**
     * 保存日志详细数据
     *
     * @param traceLogDTO
     */
    @PostMapping("/saveTraceLog")
    void saveTraceLog(@RequestBody TraceLogDTO traceLogDTO);

    @PostMapping("/recordredalarm")
    void recordredalarm(@RequestBody DbzRedAlarmEntity redAlarmEntity);

    @GetMapping("/recordsimpleredalarm")
    void recordsimpleredalarm(@RequestParam("referBizId") Integer referBizId, @RequestParam("bizModule") String bizModule, @RequestParam("memo") String memo);

    @PostMapping("/saveOperationLog")
    void saveOperationLog(@RequestBody ColumbusOperationDTO columbusOperationDTO);

    @PostMapping("/recordMagellanOperationDetail")
    void recordMagellanOperationDetail(@RequestBody RecordOperationDetail recordOperationDetail);

    @PostMapping("/recordColumbusOperationDetail")
    void recordColumbusOperationDetail(@RequestBody RecordOperationDetail RecordOperationDetail);

    @PostMapping("/queryLogList")
    Result queryLogList(@RequestBody QueryDTO<LogDTO> queryDTO);

    @PostMapping("/exportLogList")
    Result exportLogList(@RequestBody QueryDTO<LogDTO> queryDTO);

    @GetMapping("/queryScenesList")
    Result queryScenesList();

}
