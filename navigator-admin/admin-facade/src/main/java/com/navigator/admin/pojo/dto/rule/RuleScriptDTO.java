package com.navigator.admin.pojo.dto.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 18:11
 **/
@Data
@Accessors(chain = true)
public class RuleScriptDTO {
    @ApiModelProperty(value = "加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）")
    String jointConditionInfo;
    @ApiModelProperty(value = "加载规则信息（drools脚本）")
    String jointRuleInfo;
}
