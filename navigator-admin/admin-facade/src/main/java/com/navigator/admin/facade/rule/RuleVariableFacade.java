package com.navigator.admin.facade.rule;

import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 11:30
 **/
@FeignClient(value = "navigator-admin-service")
public interface RuleVariableFacade {
    @PostMapping("/updateVariable")
    Boolean updateVariable(@RequestBody RuleVariableEntity variableEntity);

    /**
     * 根据条件查询条件/关键变量
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryVariableByCondition")
    Result queryVariableByCondition(@RequestBody QueryDTO<RuleVariableEntity> queryDTO);

    @PostMapping("/queryExportVariableList")
    Result queryExportVariableList(@RequestBody RuleVariableEntity queryDTO);

    /**
     * 获取所有变量信息
     *
     * @return
     */
    @GetMapping("/getAllVariableList")
    List<RuleVariableEntity> getAllVariableList(@RequestParam(value = "isCondition", required = false) Integer isCondition,
                                                @RequestParam(value = "isKey", required = false) Integer isKey,
                                                @RequestParam(value = "moduleType", required = false) String moduleType,
                                                @RequestParam(value = "systemId", required = false) String systemId);

    @GetMapping("/getAllVariableEntityList")
    List<RuleVariableEntity> getAllVariableEntityList(@RequestParam(value = "moduleType", required = false) String moduleType,
                                                     @RequestParam(value = "systemId", required = false) String systemId);
}
