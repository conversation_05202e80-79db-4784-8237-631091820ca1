package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/19
 */

@Getter
@AllArgsConstructor
public enum MenuCodeEnum {

    PRICE(1, "销售头寸处理", "N014", "/marketContract/priceTransfer/{category2}/2"),
    POSITION(2, "套保平仓", "N069", "/marketContract/closePrice/{category2}"),
    ACTIVITI(3, "审批任务", "N003", "/approvalCenter/list"),
    C_PRICE(4, "定价管理", "C008", "/marketContract/priceTransfer/{category2}"),
    C_DELIVERY_APPLY(5, "LKG提货委托申请(CLB)", "C022", "/takeDelivery/list/{category2}"),
    M_DELIVERY_APPLY(6, "LKG提货委托申请(MGL)", "N084", "/takeDelivery/list/{category2}"),
    C_DELIVERY_APPLY_ATLAS(8, "ATLAS提货委托申请(CLB)", "C031", "/takeDelivery/atlaslist/{category2}"),
    M_DELIVERY_APPLY_ATLAS(9, "ATLAS提货委托申请(MGL)", "N084", "/takeDelivery/atlaslist/{category2}"),
    S_APPLY_CANCEL(7, "销售撤回", "N014", "/marketContract/priceTransfer/{category2}/2"),
    ;

    int value;
    String name;
    String code;
    String url;

    public static MenuCodeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(menuCodeEnum -> value == menuCodeEnum.getValue())
                .findFirst()
                .orElse(PRICE);
    }


}
