package com.navigator.admin.pojo.vo;

import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-11 14:55
 **/
@Data
@Accessors(chain = true)
public class UserMenuCollectVO extends UserMenuCollectEntity {
    /**
     * 是否有可查看权限
     */
    private Boolean hasPower;

    /**
     * 品类名称
     */
    private String categoryName;

}
