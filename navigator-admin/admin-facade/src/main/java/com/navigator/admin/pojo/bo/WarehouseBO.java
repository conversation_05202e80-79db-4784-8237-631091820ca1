package com.navigator.admin.pojo.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class WarehouseBO {
    @ApiModelProperty("库点名称")
    private String name;

    @ApiModelProperty("库点编码")
    private String code;

    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @ApiModelProperty(value = "是否不定库 1：是 0：否")
    private Integer isUnset;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "ID列表")
    List<Integer> idList;

    @ApiModelProperty(value = "库点类型0：通用 1：发货 2：提货")
    private Integer type;

}
