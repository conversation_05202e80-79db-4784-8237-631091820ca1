package com.navigator.admin.pojo.dto.columbus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class  CEmployRoleDTO {
    private Integer roleDefId;
    private Integer roleId;
    private List<Integer> employIdList;
    private String salesType;
    private String categoryId;
    private Integer customerId;
    @ApiModelProperty(value = "角色类型")
    private String type;
    private String roleDefCode;
    private List<Integer> categoryIdList;
    private String name;
}
