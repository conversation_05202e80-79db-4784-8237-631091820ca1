package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "navigator-admin-service")
public interface        FileRecordFacade {
    @PostMapping("/fileRecord/save")
    Result save(@RequestBody FileRecordDTO fileRecordDTO);

    @PostMapping("/fileRecord/modify")
    Result modify(@RequestBody FileRecordDTO fileRecordDTO);

    @PostMapping("/fileRecord/queryFileRecordDetail")
    Result queryFileRecordDetail(@RequestBody FileRecordDTO fileRecordDTO);

    @PostMapping("/fileRecord/queryFileRecordList")
    Result queryFileRecordList(@RequestBody QueryDTO<FileRecordDTO> queryDTO);

    @GetMapping("/fileRecord/queryFileRecordBySystemId")
    Result queryFileRecordBySystemId(@RequestParam("systemId") Integer systemId);
}
