package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonRelationTypeEnum {
    BANK_ACCOUNT_RELATION(1, "库点与账套"),
    SITE_CATEGORY2_RELATION(2, "账套与二级品类"),
    SITE_CATEGORY3_RELATION(3, "账套与三级品种"),

    ;

    private final int value;
    private final String desc;

    public static CommonRelationTypeEnum getByValue(int value) {
        for (CommonRelationTypeEnum item : CommonRelationTypeEnum.values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

}
