package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * <p>
 * 菜单查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class MenuQO {
    @ApiModelProperty(value = "系统")
    private Integer system;
    @ApiModelProperty(value = "菜单编码")
    private String code;
    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;
    @ApiModelProperty(value = "层级")
    private Integer level;
    @ApiModelProperty(value = "菜单ID列表")
    Set<Integer> menuIdList;
}
