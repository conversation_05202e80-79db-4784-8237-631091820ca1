package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * columbus账号表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_c_employ")
@ApiModel(value = "CEmployEntity对象", description = "columbus账号表")
public class CEmployEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键 ")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "工号")
    private String workNo;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "性别;0未知 1男 2女")
    private Integer sex;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "账号类型;0 初始管理员")
    private Integer type;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "父客户id")
    private Integer parentCustomerId;

    @ApiModelProperty(value = "源id")
    private Integer rootCustomerId;

    @ApiModelProperty(value = "账号状态;0:禁用 1: 启用")
    private Integer status;

    @ApiModelProperty(value = "签署状态;0:未签署 1:已签署")
    private Integer signatureStatus;

    @ApiModelProperty(value = "签署时间")
    private Date signatureTime;

    @ApiModelProperty(value = "签署内容")
    private String signatureContent;

    @ApiModelProperty(value = "修改密码时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_password_time", fill = FieldFill.INSERT)
    private Date updatedPasswordTime;

    @ApiModelProperty(value = "更新人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除;0未被删除 1已被删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "密码更改状态;0未改 1已改")
    private Integer passwordModifyStatus;

    @ApiModelProperty(name = "集团客户名称")
    private String enterpriseName;

    @ApiModelProperty(name = "客户名称")
    private String customerName;

    @ApiModelProperty(name = "客户编号")
    private String customerCode;
}
