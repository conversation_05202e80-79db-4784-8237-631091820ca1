package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * <p>
 * 权限查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class PowerQO {
    @ApiModelProperty(value = "系统")
    private Integer system;
    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;
    @ApiModelProperty(value = "权限ID列表")
    Set<Integer> powerIdList;
    @ApiModelProperty(value = "是否编码不为空")
    private Integer isCodeNotNull;
}
