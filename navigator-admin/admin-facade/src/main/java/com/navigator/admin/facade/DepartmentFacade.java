package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.DepartmentEntity;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/1 17:25
 */
@FeignClient(value = "navigator-admin-service")
public interface DepartmentFacade {

    /**
     * 获取所有的部门（树状结构）
     *
     * @return
     */
    @PostMapping("/getDepartmentList")
    Result getDepartmentList(@RequestParam("system") Integer system);

    /**
     * 查询部门领导人id
     *
     * @param departmentId
     * @return
     */
    @GetMapping("/getDepartmentLeaderId")
    List<DepartmentEntity> getDepartmentLeaderId(@RequestParam("departmentId") List<Integer> departmentId);
}
