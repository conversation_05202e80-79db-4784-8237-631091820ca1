package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库点账套查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class WarehouseSiteQO {
    @ApiModelProperty(value = "主体ID")
    private Integer companyId;
    @ApiModelProperty(value = "交货工厂编码")
    private String factoryCode;
    @ApiModelProperty(value = "二级品类")
    private String category2;
    @ApiModelProperty(value = "账套名称")
    private Integer type;
}
