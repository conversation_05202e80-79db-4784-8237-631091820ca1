package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_powerV2")
@ApiModel(value = "PowerEntity对象", description = "")
public class PowerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "权限类编码")
    private String preCode;

    @ApiModelProperty(value = "权限编码")
    private String code;

    @ApiModelProperty(value = "权限名称")
    private String name;

    @ApiModelProperty(value = "权限描述")
    private String describe;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "层级(0,1,2)")
    private Integer level;

    @ApiModelProperty(value = "所属系统")
    private Integer system;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @TableField(exist = false)
    @ApiModelProperty(value = "子菜单")
    private List<PowerEntity> children;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "关联原ID(刷历史数据关联处理用)")
    private Integer originalId;

    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<PowerEntity> lqw(PowerQO condition) {
        LambdaQueryWrapper<PowerEntity> lqw = new LambdaQueryWrapper<PowerEntity>().eq(PowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getSystem()), PowerEntity::getSystem, condition.getSystem());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsCategory()), PowerEntity::getIsCategory, condition.getIsCategory());
            lqw.in(CollUtil.isNotEmpty(condition.getPowerIdList()), PowerEntity::getId, condition.getPowerIdList());
            lqw.isNotNull(StringUtil.isNotNullBlank(condition.getIsCodeNotNull()) && condition.getIsCodeNotNull() == 1, PowerEntity::getCode);
        }
        lqw.orderByAsc(PowerEntity::getSort, PowerEntity::getId);
        return lqw;
    }
}
