package com.navigator.admin.pojo.dto.systemrule;

import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-21 02:48
 */
@Data
public class SystemRuleCreateDTO {
    /**
     * 品类id
     */
    private Integer categoryId = 0;
    /**
     * 配置编号
     * {@link SystemCodeConfigEnum}
     */
    private String ruleCode;

    /**
     * LKG编码
     */
    private String lkgCode;

    private String ruleName;

    private Integer ruleItemId;

    private Integer ruleId;

    /**
     * 配置属性
     */
    private String ruleItemKey;

    /**
     * 配置值
     */
    private String ruleItemValue;

    private Integer valueType;

    /**
     * 附加子段
     */
    private String memo;

    /**
     * 工厂编码集合
     */
    private List<String> factoryCodeList;
    /**
     * 交货开始月份集合（2022/05）
     */
    private List<String> deliveryBeginDateList;

    /**
     * 货品id
     */
    private List<Integer> goodsId;

    /**
     * 主体id
     */
    private List<String> companyId;

    /**
     * 规格Id;
     */
    List<Integer> attributeValueIds;

}
