package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@Accessors(chain = true)
@TableName("dba_employ")
@ApiModel(value = "Employ对象", description = "")
public class EmployEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键 ")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "所属公司id")
    private Integer companyId;

    @ApiModelProperty(value = "所属部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    private String nickName;//LKG编号

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "客户登录账号")
    private String loginAccount;

    @ApiModelProperty(value = "工号")
    private String workNo;//微软编号

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "性别 0未知 1男 2女")
    private Integer sex;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "1：普通用户 2：管理员 9：超级管理员 ")
    private Integer type;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "易企签是否实名  0:未实名 1:已实名")
    private Integer yqqAuth;

    @ApiModelProperty(value = "账户是否被锁")
    private Integer lock;

    @ApiModelProperty(value = "是否启用(默认是)")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @TableField(exist = false)
    private String token;

    @TableField(exist = false)
    private List<String> departments;

    /*@TableField(exist = false)
    private List<String> roles;*/

    @TableField(exist = false)
    private Boolean aadLogin;

    @ApiModelProperty(value = "签署状态")
    private Integer signatureStatus;

    @ApiModelProperty(value = "签署时间")
    private Date signatureTime;

    @ApiModelProperty(value = "签署内容")
    private String signatureContent;

    @ApiModelProperty(value = "系统(1麦哲伦 2哥伦布)")
    private Integer system;

}
