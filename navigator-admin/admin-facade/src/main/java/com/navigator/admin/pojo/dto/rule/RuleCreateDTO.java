package com.navigator.admin.pojo.dto.rule;

import com.navigator.common.enums.RuleModuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-03 17:21
 **/
@Data
@Accessors(chain = true)
public class RuleCreateDTO {
    private String referCode;
    /**
     * {@link com.navigator.common.enums.RuleReferTypeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private String referType;
    /**
     * {@link RuleModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;
    /**
     * 加载条件
     */
    private List<ConditionVariableDTO> conditionVariableList;
}
