package com.navigator.admin.pojo.dto;

import com.navigator.admin.pojo.entity.EmployBusinessEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import lombok.Data;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON>ong on 2021/11/9 14:35
 */

@Data
public class EmployDetailDTO extends EmployEntity {

    List<EmployBusinessEntity> employBusinessEntityList;

    List<Integer> roleDefIdList;

    List<Integer> roleIdList;

    List<String> powerCodeList;

}
