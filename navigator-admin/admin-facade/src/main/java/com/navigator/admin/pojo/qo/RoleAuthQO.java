package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色授权查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@Accessors(chain = true)
public class RoleAuthQO {
    @ApiModelProperty(value = "用户ID，为空：取当前用户", position = 1)
    private Integer userId;
    @ApiModelProperty(value = "二级品类主编码，0：默认Tab页", position = 2, required = true)
    private Integer category2;
    @ApiModelProperty(value = "客户ID，columbus必填", position = 3)
    private Integer customerId;
}
