package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 操作日志详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbz_operation_detail")
@ApiModel(value="OperationDetailEntity对象", description="操作日志详情表")
public class OperationDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "业务模块")
    private String bizModule;

    @ApiModelProperty(value = "0:客户可见 1：用户可见 9：系统级别")
    private Integer logLevel;

    @ApiModelProperty(value = "0:客户 1：用户 9：系统")
    private Integer source;

    @ApiModelProperty(value = "1：用户操作  9：系统生成")
    private Integer operatorType;

    @ApiModelProperty(value = "操作人id，客户联系人id/用户id")
    private Integer operatorId;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "操作动作")
    private String operationName;

    @ApiModelProperty(value = "操作内容")
    private String operationInfo;

    @ApiModelProperty(value = "关联业务记录ID")
    private Integer referBizId;

    @ApiModelProperty(value = "关联业务记录Code")
    private String referBizCode;

    @ApiModelProperty(value = "元数据Json")
    private String metaData;

    @ApiModelProperty(value = "记录的数据，一般为接口参数")
    private String data;

    @ApiModelProperty(value = "关联操作")
    private String referOperation;

    @ApiModelProperty(value = "关联操作的记录的ID")
    private Integer referOperationRecordId;

    @ApiModelProperty(value = "关联操作的数据Json")
    private String referOperationData;

    @ApiModelProperty(value = "触发系统")
    private String triggerSys;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "目标记录ID")
    private Integer targetRecordId;

    @ApiModelProperty(value = "目标记录类型")
    private String targetRecordType;

    @ApiModelProperty(value = "交易类型")
    private String tradeTypeName;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "修改后的数据")
    private String afterData;

    @ApiModelProperty(value = "场景")
    private String scenes;
}
