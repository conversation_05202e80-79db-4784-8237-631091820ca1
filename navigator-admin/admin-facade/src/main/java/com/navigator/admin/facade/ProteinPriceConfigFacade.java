package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9
 */
@FeignClient(value = "navigator-admin-service")
public interface ProteinPriceConfigFacade {

    @PostMapping("/filterBasicProtein")
    Result filterBasicProtein(@RequestBody BasicPriceConfigQueryDTO basicPriceConfigQueryDTO);

}
