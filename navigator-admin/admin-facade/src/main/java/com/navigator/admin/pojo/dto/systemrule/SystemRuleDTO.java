package com.navigator.admin.pojo.dto.systemrule;

import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SystemRuleDTO {
    /**
     * 品类id
     */
    private Integer categoryId;
    /**
     * 配置编号
     * {@link SystemCodeConfigEnum}
     */
    private String ruleCode;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 采销类型
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;
}
