package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
@Accessors(chain = true)
public class EmployBusinessDTO {
    private Integer employId;
    @NotBlank(message = "用户名不能为空")
    private String employName;
    //@NotBlank(message = "手机号不能为空")
    //@Pattern(regexp = "1\\d{10}", message = "请输入正确手机号")
    private String phone;
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = "(?:[a-zA-Z0-9_'^&/+-])+" + "(?:\\.(?:[a-zA-Z0-9_'^&/+-])+)*@(?:(?:\\[?(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\\.){3}(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\]?)|(?:[a-zA-Z0-9-]+\\.)+(?:[a-zA-Z]){2,}\\.?)", message = "请输入正确邮箱")
    private String email;
    private Integer status;
    // private List<Integer> roleDefIdList;
    //@NotEmpty(message = "权限不能为空")
    @ApiModelProperty(value = "品类")
    Integer categoryId;
    @ApiModelProperty(value = "主体")
    Integer customerId;
    @ApiModelProperty(value = "该品类+主体下的角色ID列表")
    private List<Integer> roleIdList;
    @ApiModelProperty(value = "LKG编号")
    private String nickName;
    @ApiModelProperty(value = "工号")
    private String workNo;
}
