package com.navigator.admin.pojo.dto.systemrule;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-07 15:09
 **/
@Data
@Accessors(chain = true)
public class DestinationImportDTO {
    @Excel(name = "目的港ID(NAV)", orderNum = "1", width = 4)
    private Integer ruleItemId;

    @Excel(name = "二级品类", orderNum = "2", width = 8)
    private String category2Name;

    @Excel(name = "状态", orderNum = "3", width = 4)
    private Integer status;

    @Excel(name = "LKG编码（IT编写）", orderNum = "4", width = 10)
    private String lkgCode;

    @Excel(name = "MDM编码", orderNum = "5", width = 10)
    private String mdmCode;

    @Excel(name = "名称(ruleKey)", orderNum = "6", width = 25)
    private String ruleKey;

    @Excel(name = "更新/删除/新增", orderNum = "7", width = 20)
    private String operationName;
}
