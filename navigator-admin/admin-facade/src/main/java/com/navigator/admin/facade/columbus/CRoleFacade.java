package com.navigator.admin.facade.columbus;

import com.navigator.admin.pojo.dto.RoleAuthDTO;
import com.navigator.admin.pojo.dto.RoleAuthMenuDTO;
import com.navigator.admin.pojo.dto.RoleAuthPowerDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.LinkedHashSet;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/3 17:03
 */
@Api(tags = "columbus角色")
@FeignClient(value = "navigator-admin-service")
public interface CRoleFacade {

    /**
     * 保存编辑角色
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping("/columbus/saveOrUpdateRole")
    Result saveOrUpdateRole(@RequestBody CRoleDTO cRoleDTO);

    /**
     * 根据角色列表
     * Jason
     *
     * @param
     * @return
     */
    @PostMapping("/columbus/queryRoleList")
    Result queryRoleList(@RequestBody QueryDTO<CRoleQueryDTO> roleQueryDTO);

    @PostMapping("/columbus/copyPermission")
    Result copyPermission(@RequestBody RoleDTO roleDTO);

    /**
     * 根据角色id查找角色
     *
     * @param id
     * @return
     */
    @GetMapping("/columbus/getRoleById")
    CRoleEntity getRoleById(@RequestParam("id") Integer id);


    /**
     * 根据角色id查询用户
     *
     * @param roldIds
     * @return
     */
    @GetMapping("/columbus/getRoleListByIds")
    List<CRoleEntity> getRoleListByIds(@RequestParam("roldIds") List<Integer> roldIds);

    /**
     * 根据虚角色id查找对应实角色列表
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/columbus/getRoleListByRoleDefId")
    List<CRoleEntity> getRoleListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId);

    /**
     * 通过虚角色、品类、主体能够获取实角色列表
     * jason
     *
     * @param roleDefId  角色定义Code
     * @param categoryId 品类Id
     * @param factoryId  主体Id
     * @return
     */
    @GetMapping("/columbus/getRoleListByRoleDefInfo")
    List<CRoleEntity> getRoleListByRoleDefInfo(@RequestParam("roleDefId") Integer roleDefId, @RequestParam("categoryId") Integer categoryId, @RequestParam("factoryId") Integer factoryId);

    /**
     * 通过虚角色字符串（多个）、品类、主体获取实角色列表
     * jason
     *
     * @param roleDefIds 角色定义Code列表
     * @param categoryId 品类Id
     * @param companyId  主体Id
     * @return
     */
    @GetMapping("/columbus/getRoleListByRoleDefInfos")
    Result<List<CRoleEntity>> getRoleListByRoleDefInfos(@RequestParam("roleDefIds") String roleDefIds, @RequestParam("categoryId") Integer categoryId, @RequestParam("companyId") Integer companyId);

    @GetMapping("/columbus/getRoleListByRoleDefInfos2")
    List<CRoleEntity> getRoleListByRoleDefInfos2(@RequestParam("roleDefIds") String roleDefIds, @RequestParam("categoryId") Integer categoryId, @RequestParam("factoryId") Integer factoryId);

    @GetMapping("/columbus/queryRoleListByDefInfosSalesType")
    List<CRoleEntity> queryRoleListByDefInfosSalesType(@RequestParam("roleDefIds") List<Integer> roleDefIds, @RequestParam("categoryId") Integer categoryId, @RequestParam("salesType") Integer salesType);

    @GetMapping("/columbus/queryRoleDefDetail")
    Result<RoleQueryVO> queryRoleDefDetail(@RequestParam("roleDefId") Integer roleDefId);

    @PostMapping("/columbus/queryRoleByCondition")
    Result<List<RoleQueryVO>> queryRoleByCondition(@RequestBody CEmployRoleDTO employRoleDTO);

    @GetMapping("/columbus/queryRoleByEmployId")
    List<CRoleEntity> queryRoleByEmployId(@RequestParam("employId") String employId);

    @GetMapping("/columbus/queryRoleIdsByEmployId")
    Result<List<Integer>> queryRoleIdsByEmployId(@RequestParam("employId") String employId);

    @PostMapping("/columbus/queryRoleDefList")
    Result queryRoleDefList(@RequestBody QueryDTO<CRoleQueryDTO> roleQueryDTO);

    @PostMapping("/columbus/updateDefRoleStatus")
    Result updateDefRoleStatus(@RequestBody CRoleDTO cRoleDTO);

    @GetMapping("/columbus/getRoleDefById")
    CRoleDefEntity getRoleDefById(@RequestParam("roleDefId") Integer roleDefId);

    @GetMapping("/queryRole")
    Result queryRole(@RequestParam("customerId") Integer customerId);

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    @ApiOperation(value = "根据条件：获取已授权的菜单树及权限树")
    @PostMapping("/crole/getRoleAuth")
    Result<RoleAuthDTO> getRoleAuth(@RequestBody RoleAuthQO roleAuthQO);

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部菜单树及已授权菜单ID列表")
    @PostMapping("/crole/getRoleAuthMenu")
    Result<RoleAuthMenuDTO> getRoleAuthMenu(@RequestBody RoleAuthMenuQO roleAuthMenuQO);

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部权限树及已授权权限ID列表")
    @PostMapping("/crole/getRoleAuthPower")
    Result<RoleAuthPowerDTO> getRoleAuthPower(@RequestBody RoleAuthPowerQO roleAuthPowerQO);

    /**
     * 根据用户ID：获取已授权的二级品类编码列表
     *
     * @param userId
     * @param customerId
     * @return
     */
    @ApiOperation(value = "根据用户ID：获取已授权的二级品类编码列表")
    @PostMapping("/crole/queryCategory2List")
    Result<LinkedHashSet<Integer>> queryCategory2List(@RequestParam(value = "用户ID，为空默认当前用户", required = false) Integer userId,@RequestParam(value = "客户ID") Integer customerId);

    /**
     * 根据用户ID：判断是否管理员
     *
     * @param userId
     * @param customerId
     * @return
     */
    @ApiOperation(value = "根据用户ID：判断是否管理员")
    @PostMapping("/crole/isAdmin")
    Result<Boolean> isAdmin(@RequestParam(value = "用户ID，为空默认当前用户", required = false) Integer userId,@RequestParam(value = "客户ID") Integer customerId);
}
