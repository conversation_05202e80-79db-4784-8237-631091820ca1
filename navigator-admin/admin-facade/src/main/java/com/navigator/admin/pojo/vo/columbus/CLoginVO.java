package com.navigator.admin.pojo.vo.columbus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CLoginVO {

    @ApiModelProperty(value = "主键 ")
    private Integer id;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    private String token;

    private String refreshToken;

    private Boolean aadLogin;

    @ApiModelProperty(value = "签署状态")
    private Integer signatureStatus;

    private CMenuVO menuVO;

    private List<CEmployCustomerVO> cEmployCustomerVOS;

    private List<String> powerCodeList;

    @ApiModelProperty(value = "密码修改")
    private Integer needModifyPassWord;

    @ApiModelProperty(value = "密码过期时间")
    private String  passwordExpireTime;

    @ApiModelProperty(value = "密码更改状态;0未改 1已改")
    private Integer passwordModifyStatus;

}
