package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.TagDetailEntity;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:37
 */
@FeignClient(value = "navigator-admin-service")
public interface TagDetailFacade {

    @GetMapping("/queryTagDetailTagId")
    Result queryTagDetailTagId(@RequestParam(value = "tagId") Integer tagId);

    @GetMapping("/cancelTagDetail")
    Result cancelTagDetail(@RequestParam(value = "id") Integer id);

    @PostMapping("/saveTagDetail")
    Integer saveTagDetail(@RequestBody TagDetailEntity tagDetailEntity);
}
