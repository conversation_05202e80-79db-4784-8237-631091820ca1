package com.navigator.admin.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-02-26 18:54
 */
@Data
public class TemplateTestImportDTO {
    @Excel(name = "编号")
    private String code;
    @Excel(name = "标题")
    private String title;
    @Excel(name = "ID组成")
    private String subTemplateIds;
    @Excel(name = "模版内容")
    private String content;
    @Excel(name = "类型")
    private Integer type;
}
