package com.navigator.admin.pojo.dto;

import cn.hutool.core.lang.tree.Tree;
import com.navigator.admin.pojo.entity.PowerEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色授权数据对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("角色授权数据对象")
public class RoleAuthDTO {
    @ApiModelProperty(value = "授权菜单列表，见返回示例")
    private List<Tree<Integer>> authMenuList;
    @ApiModelProperty(value = "授权权限列表，见返回示例")
    private List<Tree<Integer>> authPowerList;

    // columbus返回

    @ApiModelProperty(value = "是否需要修改密码")
    private Integer needModifyPassWord;
    @ApiModelProperty(value = "密码过期时间")
    private String passwordExpireTime;
    @ApiModelProperty(value = "签署状态")
    private Integer signatureStatus;
}
