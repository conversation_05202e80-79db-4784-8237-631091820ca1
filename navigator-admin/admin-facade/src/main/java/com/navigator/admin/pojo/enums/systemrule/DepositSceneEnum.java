package com.navigator.admin.pojo.enums.systemrule;

import lombok.Getter;

/**
 * <p>
 * 保证金操作场景 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Getter
public enum DepositSceneEnum {
    /**
     * 保证金操作场景
     */

    ADD(1, "新增"),
    MODIFY(2, "变更"),
    CONFIRM_PRICE(3, "点价"),
    FALL(4, "下跌"),
    RISE(5, "上涨"),
    ;

    Integer type;
    String description;

    DepositSceneEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public static DepositSceneEnum getByType(int type) {
        for (DepositSceneEnum ruleTypeEnum : DepositSceneEnum.values()) {
            if (type == ruleTypeEnum.getType()) {
                return ruleTypeEnum;
            }
        }
        return DepositSceneEnum.ADD;
    }
}
