package com.navigator.admin.facade;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * 序列号 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@FeignClient(value = "navigator-admin-service")
public interface SequenceFacade {

    /**
     * 生成指定长度的简单序列号，如 C001
     *
     * @param sequence 序列号类型
     * @param length   顺序号长度
     * @return
     */
    @ApiOperation(value = "生成指定长度的简单序列号")
    @PostMapping("/generate")
    String generate(@RequestParam String sequence, @RequestParam int length);

    /**
     * 按日期生成的序列号
     * CAD-6位日期-3位顺序号，例如：CAD230712001
     *
     * @param sequence
     * @return
     */
    @ApiOperation(value = "按日期生成的序列号")
    @PostMapping("/generateByDay")
    String generateByDay(@RequestParam String sequence);

    /**
     * 按日期生成的序列号
     * CAD-6位日期-5位顺序号，例如：CAD23071200001
     *
     * @param sequence
     * @param length
     * @return
     */
    @ApiOperation(value = "按日期生成的序列号")
    @PostMapping("/generateByDayLength")
    String generateByDayLength(@RequestParam String sequence, @RequestParam int length);

    /**
     * 仓单注册号 "交易所代码+品种+注册年月+三位递增码 例如：DCEM2401001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @ApiOperation(value = "仓单注册号")
    @PostMapping("/generateWarrantCode")
    String generateWarrantCode(@RequestParam String exchange, @RequestParam String category);

    /**
     * 仓单销售合同号	"交易所+品种+销售+分配年份+四位递增码 例如：DCEMS240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @ApiOperation(value = "仓单销售合同号")
    @PostMapping("/generateWarrantSalesContractCode")
    String generateWarrantSalesContractCode(@RequestParam String exchange, @RequestParam String category);

    /**
     * 仓单销售子合同号	"原合同号+-+3位递增码 例如：DCEMS240001-001"
     *
     * @param warrantSalesContractCode
     * @return
     */
    @ApiOperation(value = "仓单销售子合同号")
    @PostMapping("/generateWarrantSalesChildContractCode")
    String generateWarrantSalesChildContractCode(@RequestParam String warrantSalesContractCode);

    /**
     * 仓单采购合同号	"交易所+品种+采购+分配年份+四位递增码 例如：DCEMP240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @ApiOperation(value = "仓单采购合同号")
    @PostMapping("/generateWarrantPurchaseContractCode")
    String generateWarrantPurchaseContractCode(@RequestParam String exchange, @RequestParam String category);

    /**
     * 仓单采购子合同号 "原合同号+-+3位递增码 例如：DCEMP240001-001"
     *
     * @param warrantPurchaseContractCode
     * @return
     */
    @ApiOperation(value = "仓单采购子合同号")
    @PostMapping("/generateWarrantPurchaseChildContractCode")
    String generateWarrantPurchaseChildContractCode(@RequestParam String warrantPurchaseContractCode);

    /**
     * 仓单合同TT编号 "按照现货规则： SC+年月日+时分秒+四位随机码 SC202408081833001234"
     *
     * @return
     */
    @ApiOperation(value = "仓单合同TT编号")
    @PostMapping("/generateWarrantContractTTCode")
    String generateWarrantContractTTCode();

    /**
     * 仓单合同协议编号	按照现货规则：同一个合同的协议编号从000开始递增，3位递增码
     *
     * @return
     */
    @ApiOperation(value = "仓单合同协议编号")
    @PostMapping("/generateWarrantContractSignCode")
    String generateWarrantContractSignCode();


    /**
     * 仓单合同-提货权	"原合同号+-+T+3位递增码 例如：DCEMS240001-T001"
     *
     * @param contractCode
     * @return
     */
    @ApiOperation(value = "仓单合同-提货权")
    @PostMapping("/generateWarrantContractCargoRightsCode")
    String generateWarrantContractCargoRightsCode(@RequestParam String contractCode);


    /**
     * 现货销售合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    @ApiOperation(value = "现货销售合同编号")
    @PostMapping("/generateSpotSalesContractCode")
    String generateSpotSalesContractCode(@RequestParam String supplier, @RequestParam String category);

    /**
     * 现货销售子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotSalesContractCode
     * @return
     */
    @ApiOperation(value = "现货销售子合同编号")
    @PostMapping("/generateSpotSalesChildContractCode")
    String generateSpotSalesChildContractCode(@RequestParam String spotSalesContractCode);

    /**
     * 现货采购合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    @ApiOperation(value = "现货采购合同编号")
    @PostMapping("/generateSpotPurchaseContractCode")
    String generateSpotPurchaseContractCode(@RequestParam String supplier, @RequestParam String category);

    /**
     * 现货采购子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotPurchaseContractCode
     * @return
     */
    @ApiOperation(value = "现货采购子合同编号")
    @PostMapping("/generateSpotPurchaseChildContractCode")
    String generateSpotPurchaseChildContractCode(@RequestParam String spotPurchaseContractCode);

}