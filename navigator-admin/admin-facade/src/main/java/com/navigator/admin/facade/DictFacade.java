package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.DictEntity;
import com.navigator.admin.pojo.vo.DictCommonVO;
import com.navigator.admin.pojo.vo.DictVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-01 11:58
 */
@FeignClient(value = "navigator-admin-service")
public interface DictFacade {
//    /**
//     * 递归查询字典信息
//     */
//    @GetMapping("/getDiceListByCode")
//    List<DictVO> getDiceListByCode(@RequestParam(value = "bizModuleCode") String bizModuleCode);
//
//    /**
//     * 获取所有配置信息
//     *
//     * @return
//     */
//    @GetMapping("/getCommonDictInfo")
//    DictCommonVO getCommonDictInfo();
//
//    /**
//     * 根据Id查找配置信息
//     *
//     * @param dictId 字典ID
//     * @return 字典配置信息
//     */
//    @GetMapping("/getDictInfoById")
//    DictEntity getDictInfoById(@RequestParam(value = "id") Integer dictId);
}
