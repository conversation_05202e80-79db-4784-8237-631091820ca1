package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.MemoEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/4 14:59
 */
@FeignClient(name = "navigator-admin-service")
public interface MemoFacade {
    /**
     * 查看备忘录
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryMemo")
    Result queryMemo(QueryDTO<MemoEntity> queryDTO);

    /**
     * 添加备忘录
     * @param memo
     * @return
     */
    @PostMapping("addMemo")
    Result saveMemo(MemoEntity memo);

    /**
     * 修改备忘录
     * @param memo
     * @return
     */
    @PostMapping("updateMemo")
    Result updateMemo(@RequestBody MemoEntity memo);

    /**
     * 根据id查询备忘录信息
     * @param id
     * @return
     */
    @GetMapping("memoById")
    Result memoById(@RequestParam(value = "id") Integer id);
}
