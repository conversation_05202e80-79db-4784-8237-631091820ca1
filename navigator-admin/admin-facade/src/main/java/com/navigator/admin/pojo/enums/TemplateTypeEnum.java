package com.navigator.admin.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 保证金操作场景 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Getter
public enum TemplateTypeEnum {
    /**
     * 保证金操作场景
     */
    ADD(-1, "全部"),
    ORDER(0, "大合同-大合同-1-1"),
    CONTRACT(1, "订单-订单-0-0"),
    ORDER_CONTRACT(2, "订单-大合同-1-0"),
    CONTRACT_ORDER(3, "大合同-订单-0-1"),
    ;
    Integer type;
    String description;

    TemplateTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public static TemplateTypeEnum getByType(int type) {
        for (TemplateTypeEnum ruleTypeEnum : TemplateTypeEnum.values()) {
            if (type == ruleTypeEnum.getType()) {
                return ruleTypeEnum;
            }
        }
        return TemplateTypeEnum.ADD;
    }
}
