package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 19:31
 */
@FeignClient(value = "navigator-admin-service")
public interface TemplateFacade {

    /**
     * 根据模板Id获取模板
     *
     * @param templateId
     * @return
     */
    @GetMapping("/getTemplateDetailById")
    TemplateEntity getTemplateDetailById(@RequestParam("templateId") Integer templateId);

    /**
     * 根据模板Id获取模板
     *
     * @param templateCode
     * @return
     */
    @GetMapping("/getTemplateDetailByCode")
    TemplateEntity getTemplateDetailByCode(@RequestParam("templateCode") String templateCode);

    /**
     * 根据条件获取模板
     *
     * @param queryTemplateAttributeDTO
     * @return
     */
    @PostMapping("/getTemplateInfo")
    String getTemplateInfo(@RequestBody QueryTemplateAttributeDTO queryTemplateAttributeDTO);

    /**
     * 导入E
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/importETemplateInfo")
    Result importETemplateInfo(@RequestParam("file") MultipartFile uploadFile);

    /**
     * 导入M
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/importMTermAndE")
    Result importMTermAndE(@RequestParam("file") MultipartFile uploadFile);

    /**
     * 导出所有有效的E、M、底板
     *
     * @param status
     * @param response
     * @return
     */
    @GetMapping("/exportTemplate")
    Result exportTemplate(@RequestParam(value = "status", required = false) Integer status, HttpServletResponse response);

    /**
     * 导出所有有效的E、M、底板-格式化
     *
     * @param response
     * @return
     */
    @GetMapping("/exportTemplateInfo")
    Result exportTemplateInfo(HttpServletResponse response);

    @GetMapping("/getTemplateByCode")
    Result getTemplateByCode(@RequestParam("codeList") List<String> codeList);

    @PostMapping("/saveOrUpdateTemplate")
    Result saveOrUpdateTemplate(@RequestBody TemplateEntity templateEntity);

    @PostMapping("/queryTemplate")
    Result queryTemplate(@RequestBody QueryDTO<QueryTemplateDTO> queryDTO);

    @GetMapping("/getLoginSignature")
    TemplateEntity getLoginSignature();

    @GetMapping("/syncTemplateCodeInfo")
    Result syncTemplateCodeInfo(@RequestParam(value = "type", required = false) Integer type);

}
