package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dba_business_detail_update_record")
@ApiModel(value = "BusinessDetailEntity对象", description = "")
public class BusinessDetailUpdateRecordEntity {

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "businessId")
    private Integer businessId;

    @ApiModelProperty(value = "配置编号")
    private String detailCode;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "原数据")
    private String rawData;

    @ApiModelProperty(value = "传入参数")
    private String data;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
}
