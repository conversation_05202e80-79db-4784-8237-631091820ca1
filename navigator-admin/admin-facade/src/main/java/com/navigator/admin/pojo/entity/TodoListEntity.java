package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 待办业务表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@Accessors(chain = true)
@TableName("dbz_todo_list")
@ApiModel(value = "TodoList对象", description = "待办业务表")
public class TodoListEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务编码")
    private String businessId;

    @ApiModelProperty(value = "业务名称")
    private String businessName;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "待办的人")
    private Integer userId;

    @ApiModelProperty(value = "业务状态")
    private String businessStatus;

    @ApiModelProperty(value = "任务状态")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

}
