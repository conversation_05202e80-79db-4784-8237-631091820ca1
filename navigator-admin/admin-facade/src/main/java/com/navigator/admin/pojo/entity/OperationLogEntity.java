package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbz_operation_log")
@ApiModel(value="OperationLogEntity对象", description="操作日志表")
public class OperationLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "业务模块")
    private String bizModule;

    @ApiModelProperty(value = "详细操作记录ID")
    private Integer operationId;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "关联记录code")
    private String referBizCode;

    @ApiModelProperty(value = "关联记录Id")
    private Integer referBizId;

    @ApiModelProperty(value = "0:客户可见 1：用户可见 9：系统级别")
    private Integer logLevel;

    @ApiModelProperty(value = "模板编号")
    private String templateCode;

    @ApiModelProperty(value = "Json数据")
    private String data;

    @ApiModelProperty(value = "日志内容")
    private String logInfo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

    @ApiModelProperty(value = "目标记录ID")
    private Integer targetRecordId;

    @ApiModelProperty(value = "目标记录类型")
    private String targetRecordType;

    @ApiModelProperty(value = "交易类型")
    private String tradeTypeName;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

}
