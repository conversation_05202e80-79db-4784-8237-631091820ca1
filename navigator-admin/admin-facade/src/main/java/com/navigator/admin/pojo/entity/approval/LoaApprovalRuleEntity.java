package com.navigator.admin.pojo.entity.approval;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */


@Data
@Accessors(chain = true)
@TableName("dba_loa_approval_rule")
@ApiModel(value="LoaApprovalRuleEntity", description="")
public class LoaApprovalRuleEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "采销类型")
    private String salesType;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "操作类型")
    private Integer ttType;

    @ApiModelProperty(value = "规则类容")
    private String ruleInfo;

    @ApiModelProperty(value = "状态（0.禁用 1.启用）")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

}
