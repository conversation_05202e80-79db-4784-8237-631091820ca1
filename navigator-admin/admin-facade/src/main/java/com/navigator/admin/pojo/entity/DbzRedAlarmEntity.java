package com.navigator.admin.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 业务警报表
 * </p>
 *
 * <AUTHOR>
 * @since 2022年11月14日
 */
@Data
@Accessors(chain = true)
@TableName("dbz_red_alarm")
@ApiModel(value = "DbzRedAlarmEntity对象", description = "业务警报表")
public class DbzRedAlarmEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务模块")
    private String bizModule = "";
    ;

    @ApiModelProperty(value = "关联记录code")
    private String referBizCode = "";

    @ApiModelProperty(value = "关联记录Id")
    private Integer referBizId = 0;

    @ApiModelProperty(value = "警报点")
    private String bombScene = "";

    @ApiModelProperty(value = "Json数据")
    private String data = "";

    @ApiModelProperty(value = "日志内容")
    private String memo = "";

    @ApiModelProperty(value = "状态")
    private Integer status = 0;

    @ApiModelProperty(value = "解决方案")
    private String solution = "";

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

}
