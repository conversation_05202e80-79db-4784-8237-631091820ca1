package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.QueryTodoDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "navigator-admin-service")
public interface TodoListFacade {
    /**
    * @description: 获取待办列表
    * @param: [queryDTO]
    * @return: com.navigator.common.dto.Result
    */
    @PostMapping("/todo/queryTodoList")
    Result queryTodoList(@RequestBody QueryDTO<QueryTodoDTO> queryDTO);
}
