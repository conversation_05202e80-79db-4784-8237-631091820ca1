package com.navigator.admin.facade;

import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-11-29 18:22
 */
@FeignClient(value = "navigator-admin-service")
public interface FileBusinessFacade {
    /**
     * 保存文件信息
     *
     * @param fileBaseInfoDTO 文件基本信息
     * @return 文件ID
     */
    @PostMapping("/saveFileInfo")
    FileInfoEntity saveFileInfo(@RequestBody FileBaseInfoDTO fileBaseInfoDTO);

    /**
     * 统一记录存储文件关系
     *
     * @param fileBusinessRelationDTO 记录附件关系请求信息
     */
    @PostMapping("/recordFileRelation")
    Result recordFileRelation(@RequestBody FileBusinessRelationDTO fileBusinessRelationDTO);

    /**
     * 废弃文件关系信息，改状态（合同驳回）
     *
     * @param referId          业务关联ID
     * @param fileCategoryType 业务文件类型
     */
    @GetMapping("/dropFileRelation")
    void dropFileRelation(@RequestParam(value = "referId") Integer referId,
                          @RequestParam(value = "fileCategoryType") Integer fileCategoryType,
                          @RequestParam(value = "memo", required = false) String memo

    );

    /**
     * 根据业务ID、文件类型获取文件关系信息
     *
     * @param bizId            业务ID
     * @param fileCategoryType 文件类型
     *                         {@link DisableStatusEnum}
     * @param statusEnum       状态
     * @return 文件业务绑定关系
     */
    @GetMapping("/getFileInfoByBizIdAndType")
    List<FileInfoEntity> getFileInfoByBizIdAndType(@RequestParam(value = "bizId") Integer bizId,
                                                   @RequestParam(value = "fileCategoryType") Integer fileCategoryType,
                                                   @RequestParam(value = "statusEnum", required = false) Integer statusEnum);

    /**
     * 根据业务ID、文件类型获取文件关系信息
     *
     * @param bizId                业务ID
     * @param fileCategoryTypeList 文件类型集合
     *                             {@link DisableStatusEnum}
     * @param statusEnum           状态
     * @return 文件业务绑定关系
     */
    @GetMapping("/getFileInfoByBizIdAndTypeList")
    List<FileInfoEntity> getFileInfoByBizIdAndTypeList(@RequestParam(value = "bizId") Integer bizId,
                                                       @RequestParam(value = "fileCategoryTypeList") List<Integer> fileCategoryTypeList,
                                                       @RequestParam(value = "statusEnum", required = false) Integer statusEnum,
                                                       @RequestParam(value = "system") Integer system);


    /**
     * 根据来源ID、附件类型，查询附件信息
     *
     * @param bizId                来源ID
     * @param fileCategoryTypeList 附件类型集合
     *                             {@link DisableStatusEnum}
     * @param statusEnum           状态
     * @return 附件信息
     */
    @GetMapping("/getFileMapByBizIdAndType")
    Map<Integer, List<FileInfoEntity>> getFileMapByBizIdAndType(@RequestParam(value = "bizId") Integer bizId,
                                                                @RequestParam(value = "fileCategoryTypeList") List<Integer> fileCategoryTypeList,
                                                                @RequestParam(value = "statusEnum", required = false) Integer statusEnum);
}
