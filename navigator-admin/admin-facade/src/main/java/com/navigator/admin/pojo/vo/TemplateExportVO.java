package com.navigator.admin.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-04-14 10:29
 **/
@Data
@Accessors(chain = true)
public class TemplateExportVO {
    @Excel(name = "模板ID", orderNum = "1")
    private Integer contractTemplateId;
    @Excel(name = "模板编号", orderNum = "2")
    private String contractTemplateCode;
    @Excel(name = "模板名称", orderNum = "3")
    private String contractTemplateName;
    @Excel(name = "模板内容", orderNum = "4")
    private String contractTemplateContent;

    @Excel(name = "条款组ID", orderNum = "5")
    private Integer mmTemplateId;
    @Excel(name = "条款组编号", orderNum = "6")
    private String mmTemplateCode;
    @Excel(name = "条款组名称", orderNum = "7")
    private String mmTemplateName;
    @Excel(name = "条款组内容", orderNum = "8")
    private String mmTemplateContent;

    @Excel(name = "条款ID", orderNum = "9")
    private Integer eeTemplateId;
    @Excel(name = "条款编号", orderNum = "10")
    private String eeTemplateCode;
    @Excel(name = "条款名称", orderNum = "11")
    private String eeTemplateName;
    @Excel(name = "条款内容", orderNum = "12")
    private String eeTemplateContent;
}
