package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.pojo.qo.RoleQO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
@Accessors(chain = true)
@TableName("dba_role")
@ApiModel(value = "Role对象", description = "")
public class RoleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "角色管理员id")
    private Integer adminId;

    @ApiModelProperty(value = "系统(1麦哲伦 2哥伦布)")
    private Integer system;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "修改人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @TableField(exist = false)
    private List<RoleEntity> children;

    @ApiModelProperty(value = "角色编号")
    private String code;
    @ApiModelProperty(value = "角色定义Id")
    private Integer roleDefId;
    @ApiModelProperty(value = "角色定义编号")
    private String roleDefCode;
    @ApiModelProperty(value = "角色名称")
    private String realName;
    @ApiModelProperty(value = "品类Id 0:表示全部品类")
    private Integer categoryId;
    @ApiModelProperty(value = "工厂Id")
    private Integer factoryId;
    @ApiModelProperty(value = "主体Id")
    private Integer companyId;
    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;
    @ApiModelProperty(value = "账套编码")
    private String siteCode;
    @ApiModelProperty(value = "二级品类主编码")
    private Integer category2;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<RoleEntity> lqw(RoleQO condition) {
        LambdaQueryWrapper<RoleEntity> lqw = new LambdaQueryWrapper<RoleEntity>().eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.in(CollUtil.isNotEmpty(condition.getIdList()), RoleEntity::getId, condition.getIdList());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSystem()), RoleEntity::getSystem, condition.getSystem());
            lqw.eq(StringUtil.isNotNullBlank(condition.getRoleDefId()), RoleEntity::getRoleDefId, condition.getRoleDefId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), RoleEntity::getCategory2, condition.getCategory2());
        }
        lqw.orderByDesc(RoleEntity::getId);
        return lqw;
    }
}
