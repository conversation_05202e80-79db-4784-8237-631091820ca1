package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.ContractApproveConfigItemDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.InvoiceTypeDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleCreateDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Api(tags = "系统配置")
@FeignClient(value = "navigator-admin-service")
public interface SystemRuleFacade {


    @PostMapping("/getSystemRule")
    List<SystemRuleVO> getSystemRule(@RequestBody SystemRuleDTO systemRuleDTO);

    @GetMapping("/getRuleItemById")
    SystemRuleItemEntity getRuleItemById(@RequestParam("id") int id);

    /**
     * 查询所有配置信息
     *
     * @param systemRuleDTO
     * @return
     */
    @ApiOperation("查询所有配置信息")
    @PostMapping("/querySystemRuleDetail")
    SystemRuleVO querySystemRuleDetail(@RequestBody SystemRuleDTO systemRuleDTO);

    /**
     * 查询所有配置信息
     *
     * @param systemRuleDTO
     * @return
     */
    @PostMapping("/getNextSystemRule")
    List<SystemRuleVO> getNextSystemRule(@RequestBody SystemRuleDTO systemRuleDTO);

    /**
     * 新增更新配置
     *
     * @param systemRuleCreateDTO
     * @return
     */
    @PostMapping("/saveOrUpdateSystemRule")
    Result saveOrUpdateSystemRule(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO);

    @PostMapping("/updateInvoiceType")
    Result updateInvoiceType(@RequestParam(value = "categoryId") Integer categoryId,
                             @RequestBody List<InvoiceTypeDTO> invoiceTypeDTOList);

    /**
     * 禁用启用配置
     *
     * @param ruleItemId 配置ID
     * @return
     */
    @GetMapping("/invalidStatus")
    Result invalidStatus(@RequestParam(value = "ruleItemId") Integer ruleItemId);

    /**
     * 根据规则查询基差基准价
     *
     * @param basicPriceConfigQueryDTO 规则条件
     * @return 匹配的基差基准价信息
     */
    @PostMapping("/filterBasicPrice")
    Result filterBasicPrice(@RequestBody BasicPriceConfigQueryDTO basicPriceConfigQueryDTO);

    /**
     * @param categoryId  品类ID
     * @param invoiceType 发票类型id
     * @param taxRate     20%，传20
     * @return
     */
    @GetMapping("/getInvoiceType")
    SystemRuleItemEntity getInvoiceType(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                        @RequestParam(value = "invoiceType") Integer invoiceType,
                                        @RequestParam(value = "taxRate") String taxRate);


    /**
     * @param lkgCode lkgCode
     * @return
     */
    @GetMapping("/findByLkgCode")
    SystemRuleItemEntity findByLkgCode(@RequestParam(value = "categoryId") Integer categoryId,
                                       @RequestParam(value = "ruleCode") String ruleCode,
                                       @RequestParam(value = "lkgCode") String lkgCode);

    /**
     * 导入提货类型
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/importSystemRule")
    Result importSystemRule(@RequestParam("file") MultipartFile uploadFile);

    @PostMapping("/updateLOAValue")
    Result updateLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO);

    @PostMapping("/queryLOAValue")
    Result queryLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO);

    @PostMapping("/saveLOAValue")
    Result saveLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO);

    @PostMapping("/queryContractApproveConfigItem")
    List<ContractApproveConfigItemDTO> queryContractApproveConfigItem(@RequestParam(value = "categoryId") Integer categoryId);

    @GetMapping("/queryBasicPriceGoodsConfigByCategoryId")
    Result queryBasicPriceGoodsConfigByCategoryId(@RequestParam(value = "categoryId") Integer categoryId);

    @PostMapping("/importDestination")
    Result importDestination(@RequestParam("file") MultipartFile uploadFile);

    @PostMapping("/importWeightCheck")
    Result importWeightCheck(@RequestParam("file") MultipartFile uploadFile);

    @PostMapping("/importPackageWeight")
    Result importPackageWeight(@RequestParam("file") MultipartFile uploadFile);

    @PostMapping("/importLOAValue")
    Result importLOAValue(@RequestParam("file")  MultipartFile file);
}
