package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 角色查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class RoleQO {
    @ApiModelProperty(value = "角色Id列表")
    List<Integer> idList;
    @ApiModelProperty(value = "系统(1麦哲伦 2哥伦布)")
    private Integer system;
    @ApiModelProperty(value = "角色定义Id")
    private Integer roleDefId;
    @ApiModelProperty(value = "二级品类主编码")
    private Integer category2;

}
