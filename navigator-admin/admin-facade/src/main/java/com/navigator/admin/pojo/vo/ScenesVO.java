package com.navigator.admin.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ScenesVO {
    private String scenes;
    private List<OperationVO> operationVOList;

    @Data
    public static class OperationVO {
        private String bizCode;
        private String operationName;
        private String scenes;
    }

}
