package com.navigator.admin.facade.columbus;

import com.navigator.admin.pojo.dto.columbus.CPowerDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "navigator-admin-service")
public interface CPowerFacade {
    /**
     * v1.0 菜单按钮权限独立设置
     */

    @PostMapping("/columbus/saveOrUpdatePower")
    @Deprecated
    Result saveOrUpdatePower(@RequestBody CPowerDTO powerDTO);

    @PostMapping("/columbus/queryPowerByRoleId")
    @Deprecated
    Result queryPowerByRoleId(@RequestBody CPowerDTO powerDTO);

    @PostMapping("/columbus/queryPowerByEmployId")
    @Deprecated
    Result queryPowerByEmployId(@RequestParam("employId") Integer employId);

    /**
     * v2.0 统一菜单按钮权限
     */

    @PostMapping("/columbus/saveOrUpdatePowerV2")
    Result saveOrUpdatePowerV2(@RequestBody CPowerDTO powerDTO);

    @PostMapping("/columbus/queryPowerByRoleIdV2")
    Result queryPowerByRoleIdV2(@RequestBody CPowerDTO powerDTO);

    @PostMapping("/columbus/queryPowerByEmployIdV2")
    Result queryPowerByEmployIdV2(@RequestParam("employId") Integer employId, @RequestParam("customerId") Integer customerId);

    @PostMapping("/columbus/queryPowerByEmployIdCustomerV2")
    Result queryPowerByEmployIdCustomerV2(@RequestParam("employId") Integer employId, @RequestParam("customerId") Integer customerId);

    @PostMapping("/columbus/addRolePower")
    Result addRolePower(@RequestBody CPowerDTO powerDTO);
}
