package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.TradeDayCycleDTO;
import com.navigator.admin.pojo.dto.systemrule.TradeDayDTO;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/12/21 10:52
 */
@Api(tags = "交易日")
@FeignClient(name = "navigator-admin-service")
public interface TradeDayFacade {
    //TODO NEO YUYONG 修正，改为正常逻辑
    //

    /**
     * 目前设计tradeDay表中存放的是非交易日
     * 能够查到就是传入的day是非交易日
     * 否则就是交易日
     *
     * @param day
     * @return
     */
    @GetMapping("/getTradeDayByDay")
    TradeDayEntity getTradeDayByDay(@RequestParam("day") String day);

    /**
     * 通过Excel将节假日更新到库中
     *
     * @param tradeDayList
     * @return
     */
    @PostMapping("/importTradeDay")
    Result importTradeDay(@RequestBody List<TradeDayEntity> tradeDayList);

    /**
     * 将下一年的日期导入到 dba_trade_day 表
     *
     * @return
     */
    @GetMapping("/importNextYearDays")
    Result importNextYearDays(@RequestParam(value = "year", required = false) Integer year);

    @GetMapping("/getTradeDays")
    int getTradeDays(@RequestParam("startDay") String startDay, @RequestParam("endDay") String endDay);

    @GetMapping("/isTradeDay")
    boolean isTradeDay(@RequestParam("date") String date);


    @GetMapping("/isTradeDayValue")
    boolean isTradeDayValue(@RequestParam("date") String date);

    /**
     * 根据月份获取交易日信息
     *
     * @param month 月份
     * @return 交易日信息
     */
    @GetMapping("/getTradeDayByMonth")
    List<TradeDayEntity> getTradeDayByMonth(@RequestParam("month") String month);

    @GetMapping("/getTradeDayByYear")
    List<TradeDayEntity> getTradeDayByYear(@RequestParam(value = "year", required = false) Integer year);

    /**
     * 设置非交易日
     *
     * @param tradeDayDTO 日期ID集合
     * @return 更新结果
     */
    @PostMapping("/setNotTrade")
    List<String> setNotTrade(@RequestBody List<TradeDayDTO> tradeDayDTOList);

    @GetMapping("/getTradeDayByDayAgo")
    TradeDayEntity getTradeDayByDayAgo(@RequestParam(value = "year") String dayValue);

    /**
     * 根据期货代码和分配日期获取注销周期
     *
     * @param futureCode
     * @param dayValue
     * @return
     */
    @GetMapping("/tradeDay/getTradeDayCycleDTO")
    @ApiOperation("根据期货代码和分配日期获取注销周期")
    Result<TradeDayCycleDTO> getTradeDayCycleDTO(@ApiParam(value = "期货代码：M:豆粕,Y:豆油,B:豆二,P:棕榈油,OI:菜粕,RM:菜油", required = true) @RequestParam(value = "futureCode") String futureCode, @ApiParam(value = "分配日期：为空，默认当前日期") @RequestParam(value = "dayValue", required = false) String dayValue);
}
