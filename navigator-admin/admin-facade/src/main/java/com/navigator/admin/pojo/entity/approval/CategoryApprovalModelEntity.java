package com.navigator.admin.pojo.entity.approval;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */

@Data
@Accessors(chain = true)
@TableName("dba_category_approval_model")
@ApiModel(value="CategoryApprovalModelEntity", description="")
public class CategoryApprovalModelEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "流程图编码")
    private String modelKey;

    @ApiModelProperty(value = "流程图id")
    private Integer modelId;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "状态（0.禁用 1.启用）")
    private Integer status;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
}
