package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 角色菜单关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class RoleMenuQO {
    @ApiModelProperty(value = "角色ID")
    private Integer roleId;
    @ApiModelProperty(value = "角色ID列表")
    private List<Integer> roleIdList;
}
