package com.navigator.admin.pojo.entity.rule;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-14 16:11
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbr_common_config")
@ApiModel(value = "CommonConfigEntity对象", description = "")
public class CommonConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "本记录的唯一编码，groupCode+id")
    private String code;

    @ApiModelProperty(value = "父留的编码")
    private String parentCode = "0";

    /**
     * {@link com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum}
     */
    @ApiModelProperty(value = "组编码")
    @NotBlank(message = "配置编码不能为空！")
    private String groupCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "一级品类")
    private Integer category1 = 0;

    @ApiModelProperty(value = "二级品类")
    private Integer category2 = 0;

    @ApiModelProperty(value = "三级品种")
    private Integer category3 = 0;

    @ApiModelProperty(value = "0：采销 1：采购 2：销售")
    private Integer salesType = 0;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    @ApiModelProperty(value = "规则脚本")
    private String ruleInfo;

    @ApiModelProperty(value = "规则描述")
    private String ruleContent;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "配置描述")
    private String description;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String memo;
    /**
     * {@link com.navigator.bisiness.enums.ModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType = "";
    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：1/2）")
    private String systemId = "1";

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "更新人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

//    @ApiModelProperty(value = "展示名称")
//    private String displayName;
//
//    @ApiModelProperty(value = "描述")
//    private String desc;
//
//    @ApiModelProperty(value = "设定的字符串值")
//    private String settingCode;
//
//    @ApiModelProperty(value = "设定的数值")
//    private Integer settingValue;
//
//    @ApiModelProperty(value = "1:值配置 2:列表配置 3:列表值配置")
//    private Integer type;
}
