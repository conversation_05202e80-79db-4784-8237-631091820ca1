package com.navigator.admin.facade.magellan;

import com.navigator.admin.pojo.dto.MenuDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/3 13:45
 */
@Api(tags = "菜单")
@FeignClient(value = "navigator-admin-service")
public interface MenuFacade {

    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/magellan/getMenusByEmploy")
    Result getMenusByEmploy(@RequestBody MenuDTO menuDTO);


    /**
     * 查詢员工的菜单
     *
     * @param menuDTO
     * @return
     */
    @PostMapping("/magellan/getMenusByCondition")
    Result getMenusByCondition(@RequestBody MenuDTO menuDTO);

    @PostMapping("/magellan/getMenuByRoleId")
    Result getMenuByRoleId(@RequestBody RoleDTO roleDTO);

    @PostMapping("/magellan/getMenuByEmployId")
    Result getMenuByEmployId(@RequestBody RoleDTO roleDTO);

    @PostMapping("/magellan/saveRoleMenu")
    Result saveRoleMenu(@RequestBody MenuDTO menuDTO);

    @PostMapping("/magellan/addRoleMenu")
    Result addRoleMenu(@RequestBody MenuDTO menuDTO);

}
