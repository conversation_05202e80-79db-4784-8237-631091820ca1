package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DbaMenu对象", description = "")
@TableName("dba_menuV2")
public class MenuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "图标地址")
    private String icon;

    @ApiModelProperty(value = "菜单编码")
    private String code;

    @ApiModelProperty(value = "父菜单编码")
    private String parentCode;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "父菜单id")
    private Integer parentId;

    @ApiModelProperty(value = "路径")
    private String url;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否启用(默认是)")
    private Integer status;

    @ApiModelProperty(value = "系统(1麦哲伦 2哥伦布)")
    private Integer system;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "所属品类")
    private Integer categoryId;

    @TableField(exist = false)
    private List<MenuEntity> children;

    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<MenuEntity> lqw(MenuQO condition) {
        LambdaQueryWrapper<MenuEntity> lqw = new LambdaQueryWrapper<MenuEntity>()
                .eq(MenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getSystem()), MenuEntity::getSystem, condition.getSystem());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsCategory()), MenuEntity::getIsCategory, condition.getIsCategory());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCode()), MenuEntity::getCode, condition.getCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getLevel()), MenuEntity::getLevel, condition.getLevel());
            lqw.in(CollUtil.isNotEmpty(condition.getMenuIdList()), MenuEntity::getId, condition.getMenuIdList());
        }
        lqw.orderByAsc(MenuEntity::getSort, MenuEntity::getId);
        return lqw;
    }

}
