package com.navigator.admin.pojo.entity.rule;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.dto.rule.ConditionVariableDTO;
import com.navigator.common.enums.RuleModuleTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 17:53
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbr_business_rule")
@ApiModel(value = "BusinessRuleEntity对象", description = "")
public class BusinessRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "模板编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.RuleReferTypeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private String referType;

    @ApiModelProperty(value = "条件变量(conditionType、)")
    private String conditionVariable;

    @ApiModelProperty(value = "加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）")
    private String conditionInfo;

    @ApiModelProperty(value = "加载规则信息（drools脚本）")
    private String ruleInfo;

    /**
     * {@link RuleModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;
    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    /**
     * 条件变量规则
     */
    @TableField(exist = false)
    private List<ConditionVariableDTO> conditionVariableList;

    @TableField(exist = false)
    private List<BusinessRuleDetailEntity> ruleDetailEntityList;

}
