package com.navigator.admin.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账套查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@Accessors(chain = true)
public class SiteQO {
    @ApiModelProperty(value = "主体ID")
    private Integer companyId;
    @ApiModelProperty(value = "交货工厂ID")
    private Integer factoryId;
    @ApiModelProperty(value = "交货工厂编码")
    private String factoryCode;
    @ApiModelProperty(value = "一级品类（单选）")
    private String category1;

    @ApiModelProperty(value = "账套名称")
    private String siteName;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "ATLAS帐套编号")
    private String atlasCode;

    @ApiModelProperty(value = "同步系统（LKG；ATLAS）")
    private String syncSystem;

}
