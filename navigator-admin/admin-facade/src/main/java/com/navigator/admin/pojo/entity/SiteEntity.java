package com.navigator.admin.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.pojo.bo.CategoryBO;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 09:38
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_site")
@ApiModel(value = "SiteEntity对象", description = "")
public class SiteEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "账套ID", orderNum = "1", width = 4)
    private Integer id;

    @ApiModelProperty(value = "账套编码")
    @Excel(name = "账套编码", orderNum = "2", width = 12)
    private String code;

    @ApiModelProperty(value = "账套名称")
    @NotBlank(message = "账套名称不能为空!")
    @Excel(name = "帐套名称", orderNum = "3", width = 12)
    private String name;

    @ApiModelProperty(value = "lkg编码")
    @NotBlank(message = "LKG账套编码不能为空!")
    @Excel(name = "LKG帐套编号", orderNum = "7", width = 8)
    private String lkgCode;

    @ApiModelProperty(value = "ATLAS帐套编号")
    @NotBlank(message = "ATLAS账套编码不能为空!")
    @Excel(name = "ATLAS帐套编号", orderNum = "8", width = 8)
    private String atlasCode;

    @ApiModelProperty(value = "主体编号")
    @NotBlank(message = "主体不能为空")
    @Excel(name = "主体", orderNum = "5", width = 10)
    private String companyCode;

    @ApiModelProperty(value = "主体ID")
    @NotNull(message = "主体不能为空")
    private Integer companyId;

    @ApiModelProperty(value = "交货工厂ID")
    @NotNull(message = "工厂不能为空")
    private Integer factoryId;

    @ApiModelProperty(value = "交货工厂编码")
    @NotBlank(message = "工厂不能为空")
    @Excel(name = "工厂", orderNum = "6", width = 10)
    private String factoryCode;

    /**
     * 业务范围定义
     */
    @ApiModelProperty(value = "一级品类（单选）")
    private String category1;

    /**
     * 业务范围定义
     */
    @ApiModelProperty(value = "二级品类（多选）")
    private String category2;

    /**
     * 业务范围定义
     */
    @ApiModelProperty(value = "三级品类（多选）")
    private String category3;

    /**
     * {@link SyncSystemEnum}
     */
    @ApiModelProperty(value = "同步系统（LKG；ATLAS）")
    @Excel(name = "系统流向", orderNum = "4", width = 12)
    private String syncSystem;

    @ApiModelProperty(value = "LDC的客户ID（兼容销售的supplierID，采购的customerId）")
    @Excel(name = "belongCusomerId", orderNum = "13", width = 6)
    private Integer belongCustomerId;

    @ApiModelProperty(value = "状态(0禁用 1启用)")
    @TableField(value = "status", fill = FieldFill.INSERT)
    @Excel(name = "状态", replace = {"启用_1", "禁用_0"}, orderNum = "9", width = 6)
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
    //-----------------------值对象--------------------------
    @ApiModelProperty(value = "ldc主体customerId")
    @TableField(exist = false)
    private Integer ldcCustomerId;

    @ApiModelProperty(value = "ldc签订地")
    @TableField(exist = false)
    private String signPlace;

    @ApiModelProperty(value = "主体名称")
    @TableField(exist = false)
    private String companyName;

    @ApiModelProperty(value = "交货工厂名称")
    @TableField(exist = false)
    private String factoryName;

    @TableField(exist = false)
    @Excel(name = "一级品类", orderNum = "10", width = 10)
    private String category1Name;

    @TableField(exist = false)
    @Excel(name = "二级品类（可填写多个，以英文逗号隔开）", orderNum = "11", width = 20)
    private String category2Name;

    @TableField(exist = false)
    @Excel(name = "品种（可填写多个，以英文逗号隔开）", orderNum = "12", width = 20)
    private String category3Name;

    @TableField(exist = false)
    private String memo;

    @TableField(exist = false)
    private List<CategoryBO> category3List;

    @ApiModelProperty(value = "多个二级+三级，编辑回显")
    @TableField(exist = false)
    private Map<Integer, List<Integer>> category3Map;

    @ApiModelProperty(value = "多个二级品类")
    @TableField(exist = false)
    private List<Integer> category2SerialNoList;

    @ApiModelProperty(value = "多个三级品种")
    @TableField(exist = false)
    private List<Integer> category3SerialNoList;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<SiteEntity> lqw(SiteQO condition) {
        LambdaQueryWrapper<SiteEntity> lqw = new LambdaQueryWrapper<>();
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getSiteName()), SiteEntity::getName, condition.getSiteName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getStatus()), SiteEntity::getStatus, condition.getStatus());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCompanyId()), SiteEntity::getCompanyId, condition.getCompanyId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFactoryId()), SiteEntity::getFactoryId, condition.getFactoryId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFactoryCode()), SiteEntity::getFactoryCode, condition.getFactoryCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), SiteEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAtlasCode()), SiteEntity::getAtlasCode, condition.getAtlasCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSyncSystem()), SiteEntity::getSyncSystem, condition.getSyncSystem());
        }
        lqw.orderByDesc(SiteEntity::getId);
        return lqw;
    }
}
