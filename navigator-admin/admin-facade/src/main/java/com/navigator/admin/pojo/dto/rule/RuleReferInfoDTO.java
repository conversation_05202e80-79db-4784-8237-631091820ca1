package com.navigator.admin.pojo.dto.rule;

import com.navigator.admin.pojo.entity.rule.BusinessRuleDetailEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 18:24
 **/
@Data
@Accessors(chain = true)
public class RuleReferInfoDTO {

    @ApiModelProperty(value = "业务编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.RuleReferTypeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private String referType;

    /**
     * 命中的具体的规则明细
     */
    private List<BusinessRuleDetailEntity> ruleDetailList;

}
