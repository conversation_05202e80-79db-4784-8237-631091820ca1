package com.navigator.admin.facade.approval;

import com.navigator.admin.pojo.entity.approval.CategoryApprovalModelEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
@FeignClient(value = "navigator-admin-service")
public interface CategoryApprovalModelFacade {


    /**
     * 根据品类查询流程图编码
     *
     * @param category2
     * @return
     */
    @GetMapping("/queryCategoryApprovalModel")
    CategoryApprovalModelEntity queryCategoryApprovalModel(@RequestParam(value = "category2") String category2);

    /**
     * 根据品类查询流程图编码
     *
     * @param category2
     * @return
     */
    @GetMapping("/queryCategoryApprovalModelKeyByCategory2")
    String queryCategoryApprovalModelKeyByCategory2(@RequestParam(value = "category2") String category2);
}
