package com.navigator.admin.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * columbus菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dba_c_menuV2")
@ApiModel(value = "CMenuEntity对象", description = "columbus菜单表")
public class CMenuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "图标地址")
    private String icon;

    @ApiModelProperty(value = "所属品类")
    private Integer categoryId;

    @ApiModelProperty(value = "菜单编码")
    private String code;

    @ApiModelProperty(value = "父菜单编号")
    private String parentCode;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "父菜单id")
    private Integer parentId;

    @ApiModelProperty(value = "路径")
    private String url;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否启用(默认是)")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除;（0未被删除 1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "是否区分品类")
    private Integer isCategory;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<CMenuEntity> lqw(MenuQO condition) {
        LambdaQueryWrapper<CMenuEntity> lqw = new LambdaQueryWrapper<CMenuEntity>()
                .eq(CMenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsCategory()), CMenuEntity::getIsCategory, condition.getIsCategory());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCode()), CMenuEntity::getCode, condition.getCode());
            lqw.in(CollUtil.isNotEmpty(condition.getMenuIdList()), CMenuEntity::getId, condition.getMenuIdList());
        }
        lqw.orderByAsc(CMenuEntity::getSort, CMenuEntity::getId);
        return lqw;
    }
}
