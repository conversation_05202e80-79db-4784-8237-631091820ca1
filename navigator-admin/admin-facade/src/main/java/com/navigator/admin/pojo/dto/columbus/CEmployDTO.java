package com.navigator.admin.pojo.dto.columbus;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.entity.EmployEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;


@Data
public class CEmployDTO implements Serializable {
    @ApiModelProperty(value = "客户名称")
    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateEndTime;

    //@NotNull(message = "用户Id不能为空")
    @ApiModelProperty(value = "用户id")
    private String id;

    //@NotNull(message = "密码不能为空")
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "客户id")
    private Integer customerId;
    @ApiModelProperty(value = "是否为系统管理员 0:否 1 :是")
    private Integer adminStatus;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String signatureStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String signatureEndTime;

    @ApiModelProperty(value = "客户名称")
    private String  customerName;

    @ApiModelProperty(name = "集团客户名称")
    private String enterpriseName;
}
