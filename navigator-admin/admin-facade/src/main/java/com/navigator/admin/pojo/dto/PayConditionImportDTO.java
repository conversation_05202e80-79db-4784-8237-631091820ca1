package com.navigator.admin.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class PayConditionImportDTO {
    @Excel(name = "ID")
    private Integer id;

    @Excel(name = "LKG付款条件代码")
    private String lkgPayConditionCode;

    @Excel(name = "Atlas付款条件代码")
    private String atlasPayConditionCode;

    @Excel(name = "业务类型")
    private String buCode;

    @Excel(name = "赊销天数")
    private String creditDays;

    @Excel(name = "履约保证金比例")
    private String depositRate;

    @Excel(name = "点价后履约保证金比例")
    private String addedDepositRate;

    @Excel(name = "发票后补缴货款比例（见票）")
    private String invoicePaymentRate;

    @Excel(name = "状态")
    private String status;

    @Excel(name = "适用类型")
    private String applicableType;

    @Excel(name = "更新/新增/删除")
    private String updateOrAddOrDelete;

    @Excel(name = "适用类型in ATLAS")
    private String applicableTypeInAtlas;
}
