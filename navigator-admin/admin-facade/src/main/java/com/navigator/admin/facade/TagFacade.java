package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:38
 */
@FeignClient(value = "navigator-admin-service")
public interface TagFacade {

    @PostMapping("/queryTagEntity")
    Result queryTagEntity(@RequestBody QueryDTO<TagQueryDTO> queryDTO);

    @GetMapping("/queryTageEntity")
    TagEntity queryTageEntity(@RequestParam(value = "id") Integer id);

    @PostMapping("/saveTagEntity")
    Result saveTagEntity(@RequestBody TagEntity tagEntity);

    @PostMapping("/updateTagEntity")
    Result updateTagEntity(@RequestBody TagEntity tagEntity);

    @GetMapping("/systemAdminUserId")
    String systemAdminUserId();
}
