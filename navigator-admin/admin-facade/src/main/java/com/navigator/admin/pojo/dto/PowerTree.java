package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 权限树
 *
 * <AUTHOR>
 */
@Data
@ApiModel("权限树")
public class PowerTree {
    @ApiModelProperty(value = "ID", position = 1)
    private Integer id;
    @ApiModelProperty(value = "上级ID", position = 2)
    private Integer parentId;
    @ApiModelProperty(value = "权限类编码", position = 4)
    private String preCode;
    @ApiModelProperty(value = "权限编码", position = 5)
    private String code;
    @ApiModelProperty(value = "权限描述", position = 6)
    private String describe;
    @ApiModelProperty(value = "层级(0,1,2)", position = 7)
    private Integer level;
    @ApiModelProperty(value = "子集", position = 99)
    private List<PowerTree> children;
}
