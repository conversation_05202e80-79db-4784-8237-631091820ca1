package com.navigator.admin.pojo.entity.rule;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.dto.RuleEnumValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 17:53
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbr_variable")
@ApiModel(value = "RuleVariableEntity对象", description = "")
public class RuleVariableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "变量自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "字典编码")
    @Excel(name = "变量编码", orderNum = "1", width = 25)
    private String code;

    @ApiModelProperty(value = "名称")
//    @Excel(name = "名称", orderNum = "2", width = 25)
    private String name;

    @ApiModelProperty(value = "变量展示名")
    @Excel(name = "变量名称", orderNum = "3", width = 25)
    private String displayName;

    @ApiModelProperty(value = "字段类型（1整数、2字符串）")
    @Excel(name = "字段类型", replace = {"数值_1", "文本_2"}, orderNum = "4", width = 8)
    private Integer valueType;

    @ApiModelProperty(value = "是否条件变量（0不是 1是）")
    @Excel(name = "是否条件变量", replace = {"是_1", "否_0"}, orderNum = "6", width = 12)
    private Integer isCondition;

    @ApiModelProperty(value = "是否关键变量（0不是 1是）")
    @Excel(name = "是否关键变量", replace = {"是_1", "否_0"}, orderNum = "7", width = 12)
    private Integer isKey;

    @ApiModelProperty(value = "是否为常量（0不是 1是）")
    private Integer isConstant;

    @ApiModelProperty(value = "是否为逻辑变量（0不是枚举 1是枚举）")
    private Integer isLogic;

    @ApiModelProperty(value = "是否为枚举（0不是枚举 1是枚举）")
    private Integer isEnum;

    @ApiModelProperty(value = "是否有字典值")
    private Integer hasDict;

    @ApiModelProperty(value = "枚举取值路径")
    private String enumPath;

    @ApiModelProperty(value = "运算关系逗号隔开(“==”、“!=”、“>”、“>=”、“<”、“<=”、“contains”)")
    @Excel(name = "运算关系", orderNum = "11", width = 22)
    private String patternRelations;

    @ApiModelProperty(value = "输入框类型（select、input、boolean）")
//    @Excel(name = "输入框类型", replace = {"select_下拉选择框", "input_输入框", "boolean_是/否", "null_值变量"}, orderNum = "12", width = 12)
    private String inputType;

    /**
     * {@link com.navigator.bisiness.enums.ModuleTypeEnum}
     */
    @ApiModelProperty(value = "功能模块(LOA_APPROVAL：LOA审批CUSTOMER:客户关系)")
    private String moduleType;

    /**
     * {@link com.navigator.bisiness.enums.SystemEnum}
     */
    @ApiModelProperty(value = "系统来源（例：Magellan、Columbus）")
    private String systemId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    @Excel(name = "取值说明", orderNum = "30", width = 38)
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "14", width = 20)
    private Date updatedAt;

    private String createdBy;

    @Excel(name = "更新人", orderNum = "12", width = 15)
    private String updatedBy;
//    /**
//     * 字典表
//     */
//    @TableField(exist = false)
//    private List<DictItemEntity> dictItemEntityList;

    /**
     * 关键字搜索
     */
    @TableField(exist = false)
    private String searchKey;

    /**
     * 枚举值
     */
    @TableField(exist = false)
    @ExcelCollection(name = "可选项值", orderNum = "16")
    private List<RuleEnumValueDTO> enumValueDTOList;


    /**
     * 可选的运算关系
     */
    @TableField(exist = false)
    private List<String> patternRelationList;

    @TableField(exist = false)
    @Excel(name = "输入框类型", orderNum = "12", width = 12)
    private String inputTypeInfo;
}
