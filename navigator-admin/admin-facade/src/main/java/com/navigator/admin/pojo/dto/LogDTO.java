package com.navigator.admin.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LogDTO {
    @ApiModelProperty(value = "业务场景")
    private String bizCode;
    @ApiModelProperty(value = "操作人")
    private String operatorName;
    @ApiModelProperty(value = "操作开始时间")
    private String operatorStartTime;
    @ApiModelProperty(value = "操作结束时间")
    private String operatorEndTime;
    @ApiModelProperty(value = "操作内容")
    private String operationName;
}
