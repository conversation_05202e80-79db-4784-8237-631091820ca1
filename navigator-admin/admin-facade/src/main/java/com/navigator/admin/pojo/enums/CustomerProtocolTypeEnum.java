package com.navigator.admin.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CustomerProtocolTypeEnum {
    LDC_CONTRACT(0, "LDC大合同", "合同"),
    LDC_ORDER(1, "LDC大合同订单", "订单"),
    NON_FRAME_CONTRACT(2, "Non-Frame合同", "Non-Frame合同"),
    ;
    Integer value;
    String desc;
    String shortDesc;

    public static CustomerProtocolTypeEnum getByValue(Integer value) {
        if (null == value) return LDC_CONTRACT;
        for (CustomerProtocolTypeEnum en : CustomerProtocolTypeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return LDC_CONTRACT;
    }

    public static String getShortDescByValue(Integer value) {
        if (null == value) return LDC_CONTRACT.getShortDesc();
        for (CustomerProtocolTypeEnum en : CustomerProtocolTypeEnum.values()) {
            if (en.getValue() == value) {
                return en.getShortDesc();
            }
        }
        return LDC_CONTRACT.getShortDesc();
    }


}
