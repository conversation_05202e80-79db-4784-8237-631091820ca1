package com.navigator.admin.facade;

import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseAreaEntity;
import com.navigator.admin.pojo.entity.WarehouseCityEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.qo.WarehouseSiteQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@FeignClient(value = "navigator-admin-service")
public interface WarehouseFacade {

    @PostMapping("/addWarehouse")
    Result<Boolean> addWarehouse(@RequestBody WarehouseEntity warehouseEntity);

    @PostMapping("/updateWarehouse")
    Result<Boolean> updateWarehouse(@RequestBody WarehouseEntity warehouseEntity);

    @PostMapping("/getWarehouseList")
    Result<List<WarehouseEntity>> getWarehouseList(@RequestBody QueryDTO<WarehouseBO> queryDTO);

    @GetMapping("/getAllWarehouse")
    Result<List<WarehouseEntity>> getAllWarehouse();

    @GetMapping("/getWarehouseBySiteId")
    Result<List<WarehouseEntity>> getWarehouseBySiteCode(@RequestParam("siteCode") String siteCode);

    @GetMapping("/getWarehouseByCompanyId")
    Result<List<WarehouseEntity>> getWarehouseByCompanyId(@RequestParam("companyId") Integer companyId,
                                                          @RequestParam(value = "category2", required = false) Integer category2);

    @GetMapping("/getWarehouseByUniqueCode")
    Result<WarehouseEntity> getWarehouseByUniqueCode(@RequestParam("uniqueCode") String uniqueCode,
                                                     @RequestParam(value = "type", required = false) Integer type);

    @GetMapping("/getDceWarehouseByName")
    Result<WarehouseEntity> getDceWarehouseByName(@RequestParam("name") String name);

    @GetMapping("/getWarehouseByName")
    Result<WarehouseEntity> getWarehouseByName(@RequestParam("name") String name);

    @GetMapping("/getWarehouseById")
    Result<WarehouseEntity> getWarehouseById(@RequestParam("id") Integer id);

    @GetMapping("/getWarehouseMarketZone")
    Result<String> getWarehouseMarketZone(@RequestParam("warehouseId") Integer warehouseId);

    @GetMapping("/getGeographicAreaList")
    Result<List<WarehouseAreaEntity>> getGeographicAreaList();

    @GetMapping("/getGeographicCityByAreaId")
    Result<List<WarehouseCityEntity>> getGeographicCityByAreaId(@RequestParam("areaId") Integer areaId);

    /**
     * 根据工厂编码获取库点信息
     *
     * @param factoryCode
     * @param category2
     * @return
     */
    @GetMapping("/getWarehouseByFactoryCode")
    Result<List<WarehouseEntity>> getWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode,
                                                            @RequestParam(value = "category2", required = false) Integer category2);

    /**
     * 根据Id条件：获取列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取dba_warehouse列表")
    @PostMapping("/warehouse/queryWarehouseList")
    List<WarehouseEntity> queryWarehouseList(@RequestBody WarehouseBO condition);

    /**
     * 根据主体和工厂编码获取库点信息
     *
     * @param warehouseSiteQO
     * @return
     */
    @ApiOperation(value = "根据主体和工厂编码获取库点信息")
    @PostMapping("/warehouse/getWarehouseByCompanyIdAndFactoryCode")
    Result<List<WarehouseEntity>> getWarehouseByCompanyIdAndFactoryCode(@RequestBody WarehouseSiteQO warehouseSiteQO);

    @PostMapping("/warehouse/importWarehouseArea")
    Result<String> importWarehouseArea(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/warehouse/importWarehouseCity")
    Result<String> importWarehouseCity(@RequestParam(value = "file") MultipartFile file);

    @PostMapping("/warehouse/importWarehouse")
    Result<String> importWarehouse(@RequestParam(value = "file") MultipartFile file);
}
