FROM csm4nnvgacr001.azurecr.cn/openjdk-htmltopdf:int
#RUN apt-get update && apt-get install -y wkhtmltopdf && apt-get clean all
#COPY navigator-admin/simsun.ttc /usr/share/fonts
RUN mkdir /config
#COPY navigator-admin/admin-service/src/main/resources/bootstrap-dev.yml /config
#COPY navigator-admin/admin-service/src/main/resources/bootstrap.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
RUN echo 'export WKHTMLTOPDF_HOME="/tmp/wkhtmltox"' >> /etc/profile
RUN echo 'export PATH="$PATH:$WKHTMLTOPDF_HOME/bin"'>> /etc/profile
RUN echo 'export XDG_RUNTIME_DIR=/usr/lib/' >> /etc/profile
RUN echo 'export RUNLEVEL=3' >> /etc/profile
COPY deploy/admin-service/*.jar /navigator-admin-1.0-SNAPSHOT.jar
#ENV export WKHTMLTOPDF_HOME /tmp/wkhtmltox
#ENV export PATH $PATH:$WKHTMLTOPDF_HOME/bin
CMD java  -jar /navigator-admin-1.0-SNAPSHOT.jar
