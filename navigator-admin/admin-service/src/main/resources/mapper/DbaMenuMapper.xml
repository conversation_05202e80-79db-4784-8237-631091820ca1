<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.DbaMenuMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.MenuEntity">
                    <id column="id" property="id"/>
                    <result column="icon" property="icon"/>
                    <result column="code" property="code"/>
                    <result column="name" property="name"/>
                    <result column="level" property="level"/>
                    <result column="parent_id" property="parentId"/>
                    <result column="url" property="url"/>
                    <result column="sort" property="sort"/>
                    <result column="status" property="status"/>
                    <result column="system" property="system"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="is_deleted" property="isDeleted"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, icon, code, name, level, parent_id, url, sort, status, system, created_at, updated_at, is_deleted
        </sql>
</mapper>
