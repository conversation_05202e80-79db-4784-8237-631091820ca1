<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.TagDetailMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.TagDetailEntity">
                    <id column="id" property="id"/>
                    <result column="tag_id" property="tagId"/>
                    <result column="name" property="name"/>
                    <result column="biz_id" property="bizId"/>
                    <result column="type" property="type"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, tag_id, name, biz_id, type, is_deleted, created_at, updated_at, created_by, updated_by
        </sql>
</mapper>
