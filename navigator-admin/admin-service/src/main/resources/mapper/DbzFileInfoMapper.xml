<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.FileInfoMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.FileInfoEntity">
                    <id column="id" property="id"/>
                    <result column="operator_id" property="operatorId"/>
                    <result column="original_file_name" property="originalFileName"/>
                    <result column="new_file_name" property="newFileName"/>
                    <result column="extension" property="extension"/>
                    <result column="path" property="path"/>
                    <result column="fs_type" property="fsType"/>
                    <result column="fs_path" property="fsPath"/>
                    <result column="size" property="size"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, opertor_id, original_file_name, new_file_name, extension, path, fs_type, fs_path, size, is_deleted, created_at, updated_at
        </sql>
</mapper>
