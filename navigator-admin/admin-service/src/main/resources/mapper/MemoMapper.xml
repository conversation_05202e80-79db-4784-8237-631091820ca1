<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.MemoMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.MemoEntity">
                    <result column="id" property="id"/>
                    <result column="title" property="title"/>
                    <result column="user_id" property="userId"/>
                    <result column="contents" property="contents"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, title, user_id, contents, is_deleted, created_at, updated_at, created_by, updated_by
        </sql>
</mapper>
