<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.FileBusinessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.FileBusinessEntity">
        <id column="id" property="id"/>
        <result column="file_id" property="fileId"/>
        <result column="biz_module" property="bizModule"/>
        <result column="biz_id" property="bizId"/>
        <result column="biz_category" property="bizCategory"/>
        <result column="status" property="status"/>
        <result column="memo" property="memo"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
            id, file_id, biz_module, biz_code, biz_id, biz_category, is_deleted,memo,status,created_at, updated_at
        </sql>
</mapper>
