<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.CompanyMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.CompanyEntity">
                    <id column="id" property="id"/>
                    <result column="parent_id" property="parentId"/>
                    <result column="name" property="name"/>
                    <result column="address" property="address"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="status" property="status"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="is_deleted" property="isDeleted"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, parent_id, name, address, created_by, updated_by, status, created_at, updated_at, is_deleted
        </sql>
</mapper>
