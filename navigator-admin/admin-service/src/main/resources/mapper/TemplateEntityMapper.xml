<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.TemplateMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.TemplateEntity">
                    <id column="id" property="id"/>
                    <result column="code" property="code"/>
                    <result column="name" property="name"/>
                    <result column="type" property="type"/>
                    <result column="sub_template_ids" property="subTemplateIds"/>
                    <result column="content" property="content"/>
                    <result column="content_type" property="contentType"/>
                    <result column="memo" property="memo"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="updated_by" property="updatedBy"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, code, name, type, sub_template_ids, content, content_type, memo, created_at, created_by, updated_at, updated_by
        </sql>
</mapper>
