<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.TemplateAttributeMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.TemplateAttributeEntity">
                    <id column="id" property="id"/>
                    <result column="template_id" property="templateId"/>
                    <result column="template_code" property="templateCode"/>
                    <result column="template_name" property="templateName"/>
                    <result column="template_type" property="templateType"/>
                    <result column="contract_type" property="contractType"/>
                    <result column="category_code" property="categoryCode"/>
                    <result column="goods_name" property="goodsName"/>
                    <result column="customer_code" property="customerCode"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, template_id, template_code, template_name, template_type, contract_type, category_code, goods_name, customer_code, is_deleted, created_at, updated_at
        </sql>
</mapper>
