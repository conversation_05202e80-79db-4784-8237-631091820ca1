<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.OperationConfigMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.OperationConfigEntity">
                    <id column="id" property="id"/>
                    <result column="biz_code" property="bizCode"/>
                    <result column="template_code" property="templateCode"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, biz_code, template_code
        </sql>
</mapper>
