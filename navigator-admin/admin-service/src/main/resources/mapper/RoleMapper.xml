<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.RoleMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.RoleEntity">
                    <id column="id" property="id"/>
                    <result column="name" property="name"/>
                    <result column="description" property="description"/>
                    <result column="parent_id" property="parentId"/>
                    <result column="level" property="level"/>
                    <result column="sort" property="sort"/>
                    <result column="admin_id" property="adminId"/>
                    <result column="system" property="system"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="is_deleted" property="isDeleted"/>
        </resultMap>

</mapper>
