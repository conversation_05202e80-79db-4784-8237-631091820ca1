<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.TodoListMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.TodoListEntity">
                    <id column="id" property="id"/>
                    <result column="business_id" property="businessId"/>
                    <result column="business_name" property="businessName" javaType="String" jdbcType="VARCHAR"/>
                    <result column="business_type" property="businessType"/>
                    <result column="user_id" property="userId"/>
                    <result column="business_status" property="businessStatus"/>
                    <result column="status" property="status"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, business_id, business_name, business_type, user_id, business_status, status, is_deleted, created_at, updated_at
        </sql>
</mapper>
