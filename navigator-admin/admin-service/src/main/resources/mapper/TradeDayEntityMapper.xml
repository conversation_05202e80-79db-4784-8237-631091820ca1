<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.TradeDayMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.TradeDayEntity">
                    <id column="id" property="id"/>
                    <result column="year" property="year"/>
                    <result column="month" property="month"/>
                    <result column="day" property="day"/>
                    <result column="day_value" property="dayValue"/>
                    <result column="is_holiday" property="isHoliday"/>
                    <result column="week_day" property="weekDay"/>
                    <result column="is_trade" property="isTrade"/>
                    <result column="is_work_day" property="isWorkDay"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, year, month, day, day_value, is_holiday, week_day, is_trade, is_work_day, created_at, updated_at, created_by, updated_by
        </sql>
</mapper>
