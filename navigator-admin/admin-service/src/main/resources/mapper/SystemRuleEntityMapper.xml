<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.SystemRuleMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.SystemRuleEntity">
                    <id column="id" property="id"/>
                    <result column="category_id" property="categoryId"/>
                    <result column="parent_id" property="parentId"/>
                    <result column="code" property="code"/>
                    <result column="name" property="name"/>
                    <result column="sort" property="sort"/>
                    <result column="status" property="status"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, category_id, parent_id, code, name, sort, status, created_at, updated_at
        </sql>
</mapper>
