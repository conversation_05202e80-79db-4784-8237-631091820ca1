<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.SystemRuleItemMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.SystemRuleItemEntity">
                    <id column="id" property="id"/>
                    <result column="rule_id" property="ruleId"/>
                    <result column="code" property="code"/>
                    <result column="key" property="key"/>
                    <result column="value" property="value"/>
                    <result column="value_type" property="valueType"/>
                    <result column="sort" property="sort"/>
                    <result column="is_system" property="isSystem"/>
                    <result column="status" property="status"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, rule_id, code, key, value, value_type, sort, is_system, status, created_at, updated_at
        </sql>
</mapper>
