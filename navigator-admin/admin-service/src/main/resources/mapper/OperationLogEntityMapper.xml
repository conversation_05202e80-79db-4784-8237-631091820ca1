<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.OperationLogMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.OperationLogEntity">
                    <id column="id" property="id"/>
                    <result column="biz_code" property="bizCode"/>
                    <result column="biz_module" property="bizModule"/>
                    <result column="operation_id" property="operationId"/>
                    <result column="refer_biz_code" property="referBizCode"/>
                    <result column="refer_biz_id" property="referBizId"/>
                    <result column="log_level" property="logLevel"/>
                    <result column="template_code" property="templateCode"/>
                    <result column="data" property="data"/>
                    <result column="log_info" property="logInfo"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, biz_code, biz_module, operation_id, refer_biz_code, log_level, template_code, data, log_info
        </sql>
</mapper>
