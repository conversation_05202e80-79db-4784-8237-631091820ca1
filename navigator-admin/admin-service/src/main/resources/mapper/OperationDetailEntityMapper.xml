<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.admin.mapper.OperationDetailMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.admin.pojo.entity.OperationDetailEntity">
                    <id column="id" property="id"/>
                    <result column="biz_code" property="bizCode"/>
                    <result column="biz_module" property="bizModule"/>
                    <result column="log_level" property="logLevel"/>
                    <result column="source" property="source"/>
                    <result column="operator_type" property="operatorType"/>
                    <result column="operator_id" property="operatorId"/>
                    <result column="operator_name" property="operatorName"/>
                    <result column="operation_name" property="operationName"/>
                    <result column="operation_info" property="operationInfo"/>
                    <result column="refer_biz_id" property="referBizId"/>
                    <result column="refer_biz_code" property="referBizCode"/>
                    <result column="meta_data" property="metaData"/>
                    <result column="data" property="data"/>
                    <result column="refer_operation" property="referOperation"/>
                    <result column="refer_operation_record_id" property="referOperationRecordId"/>
                    <result column="refer_operation_data" property="referOperationData"/>
                    <result column="trigger_sys" property="triggerSys"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, biz_code, biz_module, log_level, source, operator_type, operator_id, operator_name, operation_name, operation_info, refer_biz_id, refer_biz_code, meta_data, data, refer_operation, refer_operation_record_id, refer_operation_data, trigger_sys
        </sql>
</mapper>
