package com.navigator.admin.facade;

import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022-03-02 17:10
 */
@Slf4j
public class TemplateTest  {
    @Resource
    private TemplateFacade templateFacade;

    @Test
    public void  testcc(){
        Integer min=20000000;
        Integer max=50000000;
        Integer mid=5685223;
        Integer mon=12;
        System.out.println(StringUtil.formatMoney(min));
        System.out.println(StringUtil.formatMoney(max));
        System.out.println(StringUtil.formatMoney(mid));
        System.out.println(StringUtil.formatMonth(mon));

    }


    @Test
    public void assembleTemplate() {
        // 获取合同的模板
        QueryTemplateAttributeDTO templateAttributeDTO = new QueryTemplateAttributeDTO()
                .setCategoryId(11)
                .setContractType(1)
                .setSalesType(2)
                .setTemplateType(1)
                .setActionType(1);
        String templateContent = templateFacade.getTemplateInfo(templateAttributeDTO);
        log.info(templateContent);
    }


}
