package com.navigator.admin.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.AdminNavigatorApplication;
import com.navigator.admin.facade.impl.magellan.EmployPermissionFacadeImpl;
import com.navigator.admin.facade.impl.magellan.RoleFacadeImpl;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.common.dto.Result;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.StringUtil;
import org.activiti.engine.identity.Group;
import org.activiti.engine.impl.persistence.entity.GroupEntity;
import org.activiti.engine.impl.persistence.entity.GroupEntityImpl;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;
import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminNavigatorApplication.class)
class RoleFacadeImplTest {

    @Resource
    RoleFacadeImpl roleFacade;
    @Resource
    EmployPermissionFacadeImpl employPermissionFacade;

    @Test
    void queryRoleByEmployId() {

        List<RoleEntity> roleEntities = new ArrayList<>();
        roleEntities = roleFacade.queryRoleByEmployId("1");

        /*try {
            roleEntities = JSON.parseObject(JSON.toJSONString(rtn.getData()), roleEntities.getClass());
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        System.out.println(JSON.toJSONString(roleEntities));

        if (CollectionUtils.isEmpty(roleEntities)) {
            System.out.println("11111111111111");
        }
        List<Group> groupList = new ArrayList<>();
        roleEntities.forEach(roleEntity -> {
            GroupEntity groupEntity = new GroupEntityImpl();
            groupEntity.setId(roleEntity.getId().toString());
            groupEntity.setName(roleEntity.getName());
            groupEntity.setRevision(1);
            groupEntity.setType("");
            groupList.add(groupEntity);
        });


        System.out.println(JSON.toJSONString(groupList));
    }

    @Test
    public void getTaskRoleIdList() {
        String roleDefCodes = "501,500";
        Integer categoryId = 11;
        Integer companyId = 5;
        StringBuilder roleIds = new StringBuilder();
        List<RoleEntity> roleEntityList = roleFacade.getRoleListByRoleDefInfos2(roleDefCodes, categoryId, companyId);
        for (RoleEntity roleEntity : roleEntityList) {
            roleIds.append(roleEntity.getId()).append(SPLIT_SIGN_DH);
        }
        System.out.println(roleIds.toString());

        if(CommonListUtil.notNullOrEmpty(roleEntityList)) {
            Result employRoleEntityList = employPermissionFacade.getEmployRoleListByRoleIds(StringUtil.split2Int(roleIds.toString(), SPLIT_SIGN_DH));
            System.out.println(employRoleEntityList);

        }
    }

}