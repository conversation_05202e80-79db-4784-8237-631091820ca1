package com.navigator.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.dto.ContractOperationEventDTO;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class OperationLogServiceImplTest {

    @Resource
    OperationLogServiceImpl operationLogService;
    @Resource
    OperationDetailServiceImpl detailService;

    @Test
    public void testQueryEvent(){
        List<ContractOperationEventDTO> ll = operationLogService.queryContractOperationLog(416, OperationSourceEnum.EMPLOYEE.getValue());
        System.out.println(JSON.toJSONString(Result.success(ll)));
    }

    @Test
    public void testNotice(){
        List<OperationDetailEntity> ll = detailService.queryOperationDetailByReferBizCode("SC20220604163041465");
        for (OperationDetailEntity detailEntity : ll) {
            OperationDetailDTO detailDTO= BeanConvertUtils.convert(OperationDetailDTO.class,detailEntity);
            operationLogService.saveOperationLog(detailDTO);
        }


    }

}