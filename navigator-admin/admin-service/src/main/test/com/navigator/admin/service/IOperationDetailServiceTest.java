package com.navigator.admin.service;

import org.junit.jupiter.api.Test;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class IOperationDetailServiceTest {
    @Resource
    IOperationDetailService operationDetailService;

    @Test
    public void noticeOperationDetail(){
         operationDetailService.noticeOperationDetail(95069);
    }

    @Test
    public void queryContractOp(){
        operationDetailService.noticeOperationDetail(95069);
    }

}