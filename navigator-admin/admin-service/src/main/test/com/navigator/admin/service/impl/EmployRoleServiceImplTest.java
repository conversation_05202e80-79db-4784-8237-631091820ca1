package com.navigator.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.dto.ContractOperationEventDTO;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.common.dto.Result;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class EmployRoleServiceImplTest {

    @Resource
    IEmployRoleService employRoleService;

    @Test
    public void addEmployRoles() {
        String roleIds = "4064,4065,4066,4067,4068,4069,4070,4071,4072,4073";
        employRoleService.addEmployRoles(199, roleIds);

        String cusomerIds = "5,138,2968,4192,6";
        employRoleService.addEmployRoles(188, 501, 11, cusomerIds);
    }
}