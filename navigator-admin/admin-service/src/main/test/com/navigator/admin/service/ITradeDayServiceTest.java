package com.navigator.admin.service;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveTaskInfoDTO;
import com.navigator.activiti.pojo.dto.ProcessInstDTO;
import com.navigator.activiti.pojo.dto.ProcessNodeDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.SystemUserEnum;
import com.navigator.common.util.time.DateTimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class ITradeDayServiceTest {

    @Resource
    ITradeDayService tradeDayService;


    @Test
    public void testTradeDay() {


        //System.out.println(JSON.toJSONString(processInstDTO));

        Date d1 = DateTimeUtil.addDays(10);
        int tt = tradeDayService.getTradeDays(new Date(), d1);
        System.out.println(JSON.toJSONString(Result.success(tt)));
    }
}