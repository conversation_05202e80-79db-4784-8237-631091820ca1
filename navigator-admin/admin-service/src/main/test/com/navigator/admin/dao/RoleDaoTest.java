package com.navigator.admin.dao;

import com.navigator.admin.AdminNavigatorApplication;
import com.navigator.admin.dao.magellan.RoleDao;
import com.navigator.admin.dao.magellan.RoleDefDao;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminNavigatorApplication.class)
class RoleDaoTest {
    @Resource
    RoleDao roleDao;
    @Resource
    RoleDefDao roleDefDao;

    @Test
    public void testQueryRole() {
        RoleQueryDTO queryDTO = new RoleQueryDTO();


        Integer[] ids = new Integer[]{1, 2, 3};
        String[] codes = new String[]{"a", "b", "c"};
        Integer[] defIds = new Integer[]{63, 51};
        String[] defCodes = new String[]{"x", "y", "z"};

//        queryDTO.setRoleId(170);
//        queryDTO.setRoleIdList(Arrays.asList(ids));
//        queryDTO.setRoleDefCode("ASign");
//        queryDTO.setRoleDefCodeList(Arrays.asList(defCodes));
//        queryDTO.setCategoryId(11);
//        queryDTO.setBelongCustomerId(20);
//        queryDTO.setSystem(1);
//        queryDTO.setRoleName("001");
//        queryDTO.setRoleDefId(63);
        queryDTO.setRoleDefIdList(Arrays.asList(defIds));

        List<RoleEntity> list = roleDao.queryRoleList(queryDTO);

        List<RoleEntity> list2 = roleDao.queryByRoleDefIdList(Arrays.asList(defIds));

        RoleEntity e = roleDao.getRoleById(170);

        System.out.println(list.size());
        System.out.println(list2.size());

        Assert.assertEquals(list.size(), list2.size());

//        System.out.println(list.get(0).getId());
//        System.out.println(e.getId());


    }

    @Test
    public void testInsert() {
        Integer[] cids = {11, 12};
        Integer[] cus = {5, 138, 7, 4192, 6};
        List<RoleDefEntity> rds = roleDefDao.getRoleDefByType("1");

        List<RoleEntity> roleEntityList = new ArrayList<>();
        System.out.println(rds.size());
        for (RoleDefEntity rd : rds) {
            RoleEntity roleEntity = new RoleEntity();
            if (rd.getIsBaseCategory() == 0) {
                roleEntity.setName(rd.getName())
                        .setRoleDefId(rd.getId())
                        .setRoleDefCode(rd.getCode())
                        .setRealName(rd.getName())
                        .setSystem(rd.getSystem())
                        .setCategoryId(0)
                        .setBelongCustomerId(0);
                roleEntityList.add(roleEntity);
                roleDao.save(roleEntity);
            } else {
                for (Integer cid : cids) {
                    for (Integer cu : cus) {
                        RoleEntity roleEntity2 = new RoleEntity();
                        roleEntity2.setName(rd.getName())
                                .setRoleDefId(rd.getId())
                                .setRoleDefCode(rd.getCode())
                                .setRealName(rd.getName() + "_" + cid + "_" + cu)
                                .setSystem(rd.getSystem())
                                .setCategoryId(cid)
                                .setBelongCustomerId(cu);
                        roleDao.save(roleEntity2);
                        roleEntityList.add(roleEntity2);
                    }
                }
            }
        }

        //roleDao.saveBatch(roleEntityList);

        System.out.println(roleEntityList.size());

    }
}