package com.navigator.admin.facade;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.AdminNavigatorApplication;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.common.util.FastJsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2021/12/8 10:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminNavigatorApplication.class)
public class OperationLogFacadeTest {
    @Resource
    OperationLogFacade operationLogFacade;
    @Test
    public void recordOperationLog() {
        /**
         *{
         *     "bizCode":"newTT",
         *     "bizModule":"TT模块",
         *     "logLevel":"1",
         *     "source":"1",
         *     "operatorType":"1",
         *     "operatorId":"1",
         *     "operationName":"新增",
         *     "operationInfo":"正本相关操作",
         *     "referBizId":"1",
         *     "referBizCode":"合同code",
         *     "metaData":"{\"operationInfo\":\"正本相关操作\",\"num\":\"111\"}",
         *     "data":"{\"operationInfo\":\"正本相关操作\"}",
         *     "referOperation":"合同正本",
         *     "referOperationRecordId":"1",
         *     "referOperationData":"{\"operationInfo\":\"正本相关操作\"}",
         *     "triggerSys":""
         *
         * }
         */

        OperationDetailDTO operationDetailDTO = new OperationDetailDTO();
        operationDetailDTO.setBizCode("newTT");
        operationDetailDTO.setBizModule("模块");
        operationDetailDTO.setLogLevel(OperationSourceEnum.CUSTOMER.getValue());
        operationDetailDTO.setSource(OperationSourceEnum.CUSTOMER.getValue());
        operationDetailDTO.setOperatorType(1);
        operationDetailDTO.setOperatorId(1);
        operationDetailDTO.setOperationName("新增");
        operationDetailDTO.setOperationInfo("正本相关操作");
        operationDetailDTO.setReferBizId(1);
        operationDetailDTO.setReferBizCode("合同code");
        // JSON.toJSON(entity)
        operationDetailDTO.setMetaData("{\"operationInfo\":\"正本相关操作\",\"num\":\"111\"}");
        // JSON.toJSON
        operationDetailDTO.setData("{\"operationInfo\":\"正本相关操作\"}");
        operationDetailDTO.setReferOperation("合同正本");
        operationDetailDTO.setReferOperationRecordId(1);
        operationDetailDTO.setReferOperationData("{\"operationInfo\":\"正本相关操作\"}");
        operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }

    @Test
    public void jsonToMap(){
        String str = "{\"a\":\"1\",\"2\":\"3\"}";
        Map<String, Object> jsonToMap = FastJsonUtils.getJsonToMap(str);
        System.out.println(jsonToMap);

    }
}
