package com.navigator.admin.facade.impl;

import com.navigator.admin.AdminNavigatorApplication;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminNavigatorApplication.class)
class SystemRuleFacadeTest {
    @Resource
    SystemRuleFacadeImpl systemRuleFacade;

    @Test
    public void testGetSystemRule(){
        SystemRuleDTO dto=new SystemRuleDTO();
        dto.setRuleCode("S0011");
        dto.setCategoryId(11);
        List<SystemRuleVO>  list= systemRuleFacade.getSystemRule(dto);
        System.out.println(list);
    }

}