package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.common.dto.Result;
import com.navigator.admin.pojo.dto.CompanyDTO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
public interface ICompanyService {

    List<CompanyEntity> queryCompanyList();

    List<CompanyDTO> queryCompanyDTOList();

    CompanyEntity queryCompanyById(Integer id);

    CompanyEntity getCompanyByCode(String companyCode);

    List<CompanyEntity> getAllCompany();

    CompanyEntity queryCompanyByName(String companyName);

    Result saveCompany(CompanyDTO companyDTO);

    Result updateCompany(CompanyDTO companyDTO);

    List<CompanyEntity> getAllCompanyBySyncSystem(String syncSystem);
}
