package com.navigator.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.FileRecordDao;
import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.FileRecordEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.FileRecordVO;
import com.navigator.admin.service.FileRecordService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FileRecordServiceImpl implements FileRecordService {
    @Autowired
    private FileRecordDao fileRecordDao;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Resource
    private IOperationDetailService operationDetailService;


    @Override
    public void save(FileRecordDTO fileRecordDTO) {
        FileRecordEntity fileRecordEntity = new FileRecordEntity();
        fileRecordEntity
                .setName(fileRecordDTO.getName())
                .setSystemId(Integer.parseInt(fileRecordDTO.getSystemId()))
                .setUrl(fileRecordDTO.getUrl())
                .setStatus(Integer.parseInt(fileRecordDTO.getStatus()))
                .setCreatedAt(new Date())
                .setCreatedBy(JwtUtils.getCurrentUserId())
                .setUpdatedAt(new Date())
                .setUpdatedBy(JwtUtils.getCurrentUserId())
        ;
        fileRecordDao.save(fileRecordEntity);
        fileRecordEntity
                .setCode(CodeGeneratorUtil.genFileRecordCode(fileRecordEntity.getSystemId(), fileRecordEntity.getId()));
        fileRecordDao.updateById(fileRecordEntity);


        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(fileRecordDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_ANNOUNCEMENT)
                    .setReferBizId(fileRecordEntity.getId())
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void modify(FileRecordDTO fileRecordDTO) {
        FileRecordEntity fileRecordEntity = fileRecordDao.getById(fileRecordDTO.getId());
        if (verifyContent(fileRecordDTO, fileRecordEntity)) {
            throw new BusinessException(ResultCodeEnum.SAME_ERROR);
        }
        fileRecordEntity
                .setName(fileRecordDTO.getName())
                .setSystemId(Integer.parseInt(fileRecordDTO.getSystemId()))
                .setUrl(fileRecordDTO.getUrl())
                .setStatus(Integer.parseInt(fileRecordDTO.getStatus()))
                .setUpdatedAt(new Date())
                .setUpdatedBy(JwtUtils.getCurrentUserId())
        ;
        fileRecordDao.updateById(fileRecordEntity);


        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(fileRecordDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_ANNOUNCEMENT)
                    .setReferBizId(fileRecordEntity.getId())
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public FileRecordEntity queryFileRecordDetail(FileRecordDTO fileRecordDTO) {
        return fileRecordDao.getById(fileRecordDTO.getId());
    }

    @Override
    public Result queryFileRecordList(QueryDTO<FileRecordDTO> queryDTO) {
        Page<FileRecordEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        FileRecordDTO fileRecordDTO = queryDTO.getCondition();
        IPage<FileRecordEntity> iPage = fileRecordDao.queryFileRecordList(page, fileRecordDTO);
        List<FileRecordVO> list = iPage.getRecords().stream().map(i -> {
            FileRecordVO fileRecordVO = new FileRecordVO();
            BeanUtils.copyProperties(i, fileRecordVO);
            String createByName = redisCacheMap.get(Integer.parseInt(i.getCreatedBy()));
            String updateByName = redisCacheMap.get(Integer.parseInt(i.getUpdatedBy()));
            fileRecordVO
                    .setCreateByName(createByName)
                    .setUpdateByName(updateByName);
            return fileRecordVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);
    }
    private boolean verifyContent(FileRecordDTO fileRecordDTO, FileRecordEntity fileRecordEntity) {
        if (!fileRecordEntity.getName().equalsIgnoreCase(fileRecordDTO.getName())) {
            return false;
        }
        if (!String.valueOf(fileRecordEntity.getSystemId()).equals(fileRecordDTO.getSystemId())) {
            return false;

        }
        if (!fileRecordEntity.getUrl().equalsIgnoreCase(fileRecordDTO.getUrl())) {
            return false;

        }
        if (!String.valueOf(fileRecordEntity.getStatus()).equals(fileRecordDTO.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public List<FileRecordEntity> queryFileRecordBySystemId(Integer systemId) {
        return fileRecordDao.queryFileRecordList(systemId);
    }
}
