package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.CommonRelationMapper;
import com.navigator.admin.pojo.entity.CommonRelationEntity;
import com.navigator.admin.pojo.enums.CommonRelationTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Dao
public class CommonRelationDao extends BaseDaoImpl<CommonRelationMapper, CommonRelationEntity> {


    /**
     * 记录统一关联关系
     * @param bindCode1
     * @param bindCode2List
     * @param relationTypeEnum
     */
    public void recordCommonRelation(String bindCode1, List<String> bindCode2List, CommonRelationTypeEnum relationTypeEnum) {
        this.dropCommonRelation(bindCode1, relationTypeEnum);
        if (!CollectionUtils.isEmpty(bindCode2List)) {
            bindCode2List.forEach(bindCode2 -> {
                CommonRelationEntity commonRelationEntity = new CommonRelationEntity()
                        .setBindCode1(bindCode1)
                        .setBindCode2(bindCode2)
                        .setRelationType(relationTypeEnum.getValue())
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setCreatedAt(DateTimeUtil.now())
                        .setUpdatedAt(DateTimeUtil.now());
                this.save(commonRelationEntity);
            });
        }
    }

    /**
     * @param relationType {@link com.navigator.admin.pojo.enums.CommonRelationTypeEnum}
     * @param bindCode
     * @return
     */
    public List<CommonRelationEntity> queryByBindCode1(Integer relationType, String bindCode) {
        return this.list(Wrappers.<CommonRelationEntity>lambdaQuery()
                .eq(CommonRelationEntity::getRelationType, relationType)
                .eq(CommonRelationEntity::getBindCode1, bindCode)
                .eq(CommonRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<CommonRelationEntity> queryByBindCode2(Integer relationType, String bindCode) {
        return this.list(Wrappers.<CommonRelationEntity>lambdaQuery()
                .eq(CommonRelationEntity::getRelationType, relationType)
                .eq(CommonRelationEntity::getBindCode2, bindCode)
                .eq(CommonRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    private void dropCommonRelation(String bindCode1, CommonRelationTypeEnum relationTypeEnum) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(CommonRelationEntity::getBindCode1, bindCode1)
                .eq(null != relationTypeEnum, CommonRelationEntity::getRelationType, relationTypeEnum.getValue())
                .eq(CommonRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(CommonRelationEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }
}
