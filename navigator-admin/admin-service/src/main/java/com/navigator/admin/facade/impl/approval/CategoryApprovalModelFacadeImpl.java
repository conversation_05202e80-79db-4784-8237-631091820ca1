package com.navigator.admin.facade.impl.approval;

import com.navigator.admin.facade.approval.CategoryApprovalModelFacade;
import com.navigator.admin.pojo.entity.approval.CategoryApprovalModelEntity;
import com.navigator.admin.service.approval.CategoryApprovalModelService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */

@RestController
public class CategoryApprovalModelFacadeImpl implements CategoryApprovalModelFacade {

    @Resource
    private CategoryApprovalModelService categoryApprovalModelService;

    @Override
    public CategoryApprovalModelEntity queryCategoryApprovalModel(String category2) {
        return categoryApprovalModelService.queryCategoryApprovalModel(category2);
    }

    @Override
    public String queryCategoryApprovalModelKeyByCategory2(String category2) {
        return categoryApprovalModelService.queryCategoryApprovalModelKeyByCategory2(category2);
    }
}
