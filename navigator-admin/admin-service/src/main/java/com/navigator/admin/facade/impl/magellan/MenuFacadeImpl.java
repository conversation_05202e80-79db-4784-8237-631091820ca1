package com.navigator.admin.facade.impl.magellan;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.MenuFacade;
import com.navigator.admin.pojo.dto.MenuDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.admin.processor.AbstractAdminProcessor;
import com.navigator.admin.processor.MenuContext;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.magellan.IMenuService;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/3 13:46
 */
@RestController
public class MenuFacadeImpl extends AbstractAdminProcessor<MenuContext> implements MenuFacade {

    @Autowired
    private IMenuService iMenuService;

    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;

    @Resource
    private EmployFacade employFacade;
    @Resource
    private IOperationDetailService operationDetailService;

    @Override
    public Result getMenusByEmploy(MenuDTO menuDTO) {
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setEmployId(menuDTO.getEmployId());
        MenuVO levelMenus = iMenuService.getMenuByEmployId(roleDTO);

        return Result.success(levelMenus);
    }

    @Override
    public Result getMenusByCondition(MenuDTO menuDTO) {
        MenuVO levelMenus = iMenuService.getMenusByCondition(menuDTO);

        return Result.success(levelMenus);
    }

    @Override
    public Result getMenuByRoleId(RoleDTO roleDTO) {
        MenuVO menuVO = iMenuService.getMenuByRoleId(roleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result getMenuByEmployId(RoleDTO roleDTO) {
        MenuVO menuVO = iMenuService.getMenuByEmployId(roleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result saveRoleMenu(MenuDTO menuDTO) {
        BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity = new BusinessDetailUpdateRecordEntity();
        //记录修改客户主数据人
        businessDetailUpdateRecordEntity
                .setDetailCode(BusinessDetailCodeEnum.EMPLOY_ROLE_EDIT.getValue())
                .setBusinessId(Integer.valueOf(menuDTO.getRoleId()))
                .setData(JSON.toJSONString(menuDTO))
                .setCreatedAt(new Date())
                .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        businessDetailUpdateRecordService.saveBusinessDetailUpdateRecord(businessDetailUpdateRecordEntity);

        if (CollectionUtils.isEmpty(menuDTO.getMenuIdList())) {
            throw new BusinessException(ResultCodeEnum.MENU_EMPUTY);
        }

        if (StringUtils.isBlank(menuDTO.getRoleId())) {
            throw new BusinessException(ResultCodeEnum.ROLE_EMPUTY);

        }
        iMenuService.saveRoleMenu(menuDTO);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(menuDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.COPY_PERMISSION)
                    .setReferBizId(Integer.parseInt(menuDTO.getRoleId()))
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.success();
    }

    @Override
    public Result addRoleMenu(MenuDTO menuDTO) {
        iMenuService.addRoleMenu(menuDTO);
        return Result.success();
    }

}
