package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CompanyMapper;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.springframework.util.CollectionUtils;

import java.util.List;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 13:38
 */
@Dao
public class CompanyDao extends BaseDaoImpl<CompanyMapper, CompanyEntity> {

    public CompanyEntity getCompanyByCode(String companyCode) {
        List<CompanyEntity> companyEntityList = this.list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getShortName, companyCode)
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(companyEntityList) ? null : companyEntityList.get(0);
    }

    public List<CompanyEntity> queryCompanyList() {
        List<CompanyEntity> list = list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return list;
    }

    public CompanyEntity queryCompanyByName(String name) {
        List<CompanyEntity> list = list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getName, name)
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<CompanyEntity> queryCompanyByShortName(String shortName, Integer companyId) {
        return list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getShortName, shortName)
                .ne(null != companyId, CompanyEntity::getId, companyId)
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CompanyEntity> queryCompanyByNameNotId(String name, Integer companyId) {
        return list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getName, name)
                .ne(null != companyId, CompanyEntity::getId, companyId)
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CompanyEntity> queryCompanyByCompanyCode(String companyCode) {
        return list(Wrappers.<CompanyEntity>lambdaQuery()
                .eq(CompanyEntity::getShortName, companyCode)
                .eq(CompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }
}
