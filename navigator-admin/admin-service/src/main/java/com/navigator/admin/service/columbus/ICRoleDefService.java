package com.navigator.admin.service.columbus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.admin.pojo.vo.columbus.CRoleListVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface ICRoleDefService {

    CRoleDefEntity getRoleDefById(Integer roleDefId);

    CRoleDefEntity saveOrUpdate(CRoleDTO roleDTO);

    Result queryRoleList(QueryDTO<CRoleQueryDTO> queryDTO);

    RoleQueryVO queryFactoryListByRoleDefIdList(List<Integer> roleDefIdList);

    List<CRoleDefEntity> queryAllList();

    RoleQueryVO queryRoleDefDetail(Integer roleDefId);

    List<RoleQueryVO> queryRoleByCondition(CEmployRoleDTO employRoleDTO);

    List<CRoleDefEntity> getRoleDefByType(String type);

    CRoleDefEntity getRoleDefByName(String name);

    void updateDefRoleStatus(CRoleDTO cRoleDTO);

    IPage<CRoleDefEntity> queryPageByRoleQueryDTO(Page<CRoleDefEntity> page, CRoleQueryDTO roleQueryDTO);

    List<CRoleDefEntity> queryRoleDefForbidden();

    List<CRoleListVO> queryRole(Integer customerId);

    void copyPermission(RoleDTO roleDTO);
}
