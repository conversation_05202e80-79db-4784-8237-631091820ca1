package com.navigator.admin.service.magellan.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.dao.magellan.RoleDefDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.RoleTypeEnum;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.magellan.*;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Service
public class RoleDefServiceImpl implements IRoleDefService {
    @Autowired
    private RoleDefDao roleDefDao;
    @Autowired
    private IRoleService roleService;
    @Autowired
    protected EmployFacade employFacade;
    @Autowired
    private IOperationDetailService operationDetailService;
    @Autowired
    private IRolePowerService rolePowerService;
    @Autowired
    private IRoleMenuService roleMenuService;

    @Override
    public RoleDefEntity getRoleDefById(Integer roleDefId) {
        return roleDefDao.getRoleDefById(roleDefId);
    }

    @Override
    public RoleDefEntity saveOrUpdate(RoleDTO roleDTO) {
        List<RoleDefEntity> roleDefEntityList = roleDefDao.queryRoleDefExist(roleDTO);
        if (CollectionUtils.isNotEmpty(roleDefEntityList) && roleDTO.getImportStatus() != 1) {
            throw new BusinessException(ResultCodeEnum.NAME_CODE_ERROR);
        }
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        RoleDefEntity roleDefEntity = roleDefDao.queryPreData();
        if (roleDefEntity == null) {
            roleDefEntity = new RoleDefEntity();
        }
        roleDefEntity
                .setCode(roleDTO.getCode())
                .setName(roleDTO.getName())
                .setType(roleDTO.getRoleType())
                .setStatus(roleDTO.getStatus())
                .setSystem(1)
                .setCreatedBy(userId)
                .setUpdatedBy(userId)
        ;
        if (null != roleDTO.getRoleDefId()) {
            roleDefEntity.setId(roleDTO.getRoleDefId());
        }
        if (roleDTO.getRoleType() == RoleTypeEnum.COMMON.getValue()) {
            roleDefEntity.setIsBaseCategory(1)
                    .setIsBaseCompany(1);
        } else {
            roleDefEntity.setIsBaseCategory(0)
                    .setIsBaseCompany(0);
        }
        roleDefDao.saveOrUpdate(roleDefEntity);
        roleDTO.setRoleDefId(roleDefEntity.getId());
        return roleDefEntity;
    }

    @Override
    public Result queryRoleList(QueryDTO<RoleQueryDTO> queryDTO) {
        Page<RoleEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        RoleQueryDTO roleQueryDTO = queryDTO.getCondition();

        //分页查询
        return roleService.queryPageByQueryDTO(page, roleQueryDTO);
    }


    @Override
    public RoleQueryVO queryFactoryListByRoleDefIdList(List<Integer> roleDefIdList) {
        RoleQueryVO roleQueryVO = new RoleQueryVO();
        List<RoleEntity> roleEntityList = roleService.queryByRoleDefIdList(roleDefIdList);
        List<Integer> categoryIdList = roleEntityList.stream().map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        List<Integer> factoryIdList = roleEntityList.stream().map(RoleEntity::getFactoryId).distinct().collect(Collectors.toList());
        roleQueryVO.setFactoryIdList(factoryIdList);
        roleQueryVO.setCategoryIdList(categoryIdList);
        return roleQueryVO;
    }

    @Override
    public List<RoleDefEntity> queryAllList() {
        return roleDefDao.list();
    }

    @Override
    public RoleQueryVO queryRoleDefDetail(Integer roleDefId) {
        RoleDefEntity roleDefEntity = roleDefDao.getRoleDefById(roleDefId);
        if (roleDefEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        RoleQueryVO roleQueryVO = new RoleQueryVO();
        List<Integer> list = new ArrayList<>();
        list.add(roleDefId);
        List<RoleEntity> roleEntityList = roleService.queryByRoleDefIdList(list);
        List<Integer> categoryIdList = roleEntityList.stream().map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        List<Integer> factoryIdList = roleEntityList.stream().map(RoleEntity::getFactoryId).distinct().collect(Collectors.toList());
        roleQueryVO.setFactoryIdList(factoryIdList)
                .setCategoryIdList(categoryIdList)
                .setRoleDefId(roleDefEntity.getId())
                .setName(roleDefEntity.getName())
                .setCode(roleDefEntity.getCode())
                .setRoleType(roleDefEntity.getType())
                .setStatus(roleDefEntity.getStatus());
        return roleQueryVO;
    }

    @Override
    public List<RoleQueryVO> queryRoleByFactory(EmployRoleDTO employRoleDTO) {
        //List<RoleDefEntity> roleDefEntityList = roleDefDao.getRoleDefByType(employRoleDTO.getType());
        //List<Integer> roleDefIdList = roleDefEntityList.stream().map(RoleDefEntity::getId).collect(Collectors.toList());
        List<RoleEntity> roleEntityList = roleService.queryRole(employRoleDTO);
        return roleEntityList.stream()
                //.filter(i -> roleDefIdList.contains(i.getRoleDefId()))
                .filter(i -> !"TBD".equalsIgnoreCase(i.getName()))
                .distinct()
                .map(i -> {
                    RoleQueryVO roleQueryVO = new RoleQueryVO();
                    roleQueryVO.setName(i.getName())
                            .setRoleId(i.getId())
                            .setRoleDefId(i.getRoleDefId());
                    return roleQueryVO;
                }).collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(
                                        () -> new TreeSet<>(
                                                Comparator.comparing(i -> i.getRoleDefId())
                                        )
                                ), ArrayList::new
                        )
                );

    }

    @Override
    public List<RoleDefEntity> getRoleDefByType(String type) {
        return roleDefDao.getRoleDefByType(type);
    }

    @Override
    public RoleDefEntity getRoleDefByName(String name) {
        return roleDefDao.getRoleDefByName(name);
    }

    @Override
    public RoleDefEntity save(RoleDTO roleDTO) {
        List<RoleDefEntity> roleDefEntityList = roleDefDao.queryRoleDefExist(roleDTO);
        if (CollectionUtils.isNotEmpty(roleDefEntityList) && roleDTO.getImportStatus() != 1) {
            throw new BusinessException(ResultCodeEnum.NAME_CODE_ERROR);
        }
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        RoleDefEntity roleDefEntity = new RoleDefEntity();
        roleDefEntity
                .setName(roleDTO.getName())
                .setIsBaseCategory(roleDTO.getIsBaseCategory())
                .setIsBaseCompany(roleDTO.getIsBaseCompany())
                .setIsBaseFactory(roleDTO.getIsBaseFactory())
                .setCreatedBy(userId)
                .setUpdatedBy(userId)
                .setStatus(1)
                .setLevel(2)
                .setType(1)
                .setParentId(roleDTO.getParentId())
        ;
        if (null != roleDTO.getRoleDefId()) {
            roleDefEntity.setId(roleDTO.getRoleDefId());
        }
        if (roleDTO.getIsBaseCategory() == 0 && roleDTO.getIsBaseCompany() == 0 && roleDTO.getIsBaseFactory() == 0) {
            roleDefEntity.setType(0);
        }

        roleDefDao.saveOrUpdate(roleDefEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(roleDefEntity.getName())
                    .setDtoData(JSON.toJSONString(roleDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(roleDefEntity))
                    .setOperationActionEnum(OperationActionEnum.SAVE_ROLE)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        roleDTO.setRoleDefId(roleDefEntity.getId());
        return roleDefEntity;
    }

    @Override
    public List<RoleDefEntity> queryRoleGroupList() {
        List<RoleDefEntity> roleDefEntityList = roleDefDao.queryFatherRoleDefList();
        List<Integer> hidedIdList = Arrays.asList(1, 2, 15);
        List<RoleDefEntity> roleGroupList = roleDefEntityList.stream().filter(i -> !hidedIdList.contains(i.getId())).collect(Collectors.toList());
        return roleGroupList;
    }

    @Override
    public void copyPermission(RoleDTO roleDTO) {
        RoleEntity roleEntity = roleService.getRoleById(roleDTO.getRoleId());
        if (roleEntity == null) {
            throw new BusinessException(ResultCodeEnum.NOT_SAME_ROLE_DEF);
        }
        List<RoleEntity> roleEntities = roleService.queryByRoleDefIdList(Arrays.asList(roleEntity.getRoleDefId()));
        List<Integer> roleIdList = roleEntities.stream().map(RoleEntity::getId).collect(Collectors.toList());
        if (!roleIdList.contains(roleDTO.getTargetRoleIdList().get(0))) {
            throw new BusinessException(ResultCodeEnum.NOT_SAME_ROLE_DEF);
        }

        for (Integer roleId : roleDTO.getTargetRoleIdList()) {
            rolePowerService.copyRolePower(roleDTO.getRoleId(), roleId);
            roleMenuService.copyRoleMenu(roleDTO.getRoleId(), roleId);
        }

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(roleEntity.getName())
                    .setDtoData(JSON.toJSONString(roleDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.COPY_PERMISSION)
                    .setReferBizId(roleDTO.getRoleId())
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
