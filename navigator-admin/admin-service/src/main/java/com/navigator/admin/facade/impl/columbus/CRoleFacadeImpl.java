package com.navigator.admin.facade.impl.columbus;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.admin.pojo.vo.columbus.CRoleListVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.columbus.ICRoleDefService;
import com.navigator.admin.service.columbus.ICRoleService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/3 17:03
 */
@Slf4j
@RestController
public class CRoleFacadeImpl implements CRoleFacade {

    @Autowired
    private ICRoleService cRoleService;
    @Autowired
    private ICRoleDefService cRoleDefService;
    @Autowired
    private IOperationDetailService operationDetailService;

    @Override
    public List<CRoleEntity> queryRoleByEmployId(String employId) {
        return cRoleService.queryRoleByEmployId(employId, null);
    }

    @Override
    public Result<List<Integer>> queryRoleIdsByEmployId(String employId) {

        List<Integer> roleIdList = new ArrayList<>();

        List<CRoleEntity> roleEntityList = queryRoleByEmployId(employId);
        for (CRoleEntity CRoleEntity : roleEntityList) {
            roleIdList.add(CRoleEntity.getId());
        }
        return Result.success(roleIdList);
    }

    @Override
    public Result queryRoleDefList(QueryDTO<CRoleQueryDTO> roleQueryDTO) {
        return cRoleService.queryRoleDefList(roleQueryDTO);
    }

    @Override
    public Result updateDefRoleStatus(CRoleDTO cRoleDTO) {
        CRoleDefEntity originalEntity = cRoleDefService.getRoleDefById(cRoleDTO.getRoleDefId());
        cRoleDefService.updateDefRoleStatus(cRoleDTO);
        try {
            CRoleDefEntity afterEntity = originalEntity.setStatus(cRoleDTO.getStatus());
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(originalEntity.getName())
                    .setDtoData(JSON.toJSONString(cRoleDTO))
                    .setBeforeData(JSON.toJSONString(originalEntity))
                    .setAfterData(JSON.toJSONString(afterEntity));
            if (cRoleDTO.getStatus() == 1) {
                recordOperationDetail.setOperationActionEnum(OperationActionEnum.UPDATE_DEF_ROLE_STATUS_OPEN);
            }
            if (cRoleDTO.getStatus() == 0) {
                recordOperationDetail.setOperationActionEnum(OperationActionEnum.UPDATE_DEF_ROLE_STATUS_CLOSE);
            }
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success();
    }

    @Override
    public CRoleDefEntity getRoleDefById(Integer roleDefId) {
        return cRoleDefService.getRoleDefById(roleDefId);
    }

    //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-04-28 start
    @Override
    public Result queryRole(Integer customerId) {
        List<CRoleListVO> roleList = cRoleDefService.queryRole(customerId);
        return Result.success(roleList);
    }
    //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-05-20 End

    @Override
    public Result copyPermission(RoleDTO roleDTO) {

        cRoleDefService.copyPermission(roleDTO);

        return Result.success();
    }

    @Override
    public CRoleEntity getRoleById(Integer id) {
        return cRoleService.getRoleById(id);
    }

    @Override
    public List<CRoleEntity> getRoleListByIds(List<Integer> roldIds) {
        return cRoleService.queryByIdList(roldIds);
    }

    @Override
    public List<CRoleEntity> getRoleListByRoleDefId(Integer roleDefId) {
        return cRoleService.queryRoleListByDefInfo(roleDefId, null, null);
    }

    @Override
    public List<CRoleEntity> getRoleListByRoleDefInfo(Integer roleDefId, Integer categoryId, Integer customerId) {
        return cRoleService.queryRoleListByDefInfo(roleDefId, categoryId, customerId);
    }

    @Override
    public Result<List<CRoleEntity>> getRoleListByRoleDefInfos(String roleDefIds, Integer categoryId, Integer customerId) {
        return Result.success(cRoleService.queryRoleListByDefInfos(StringUtil.split2Int(roleDefIds, SPLIT_SIGN_DH), categoryId, customerId));
    }

    @Override
    public List<CRoleEntity> getRoleListByRoleDefInfos2(String roleDefIds, Integer categoryId, Integer factoryId) {
        return cRoleService.queryRoleListByDefInfos2(StringUtil.split2Int(roleDefIds, SPLIT_SIGN_DH), categoryId, factoryId);
    }


    @Override
    public List<CRoleEntity> queryRoleListByDefInfosSalesType(List<Integer> roleDefIds, Integer categoryId, Integer salesType) {
        return cRoleService.queryRoleListByDefInfosSalesType(roleDefIds, categoryId, salesType);
    }

    @Override
    public Result saveOrUpdateRole(CRoleDTO roleDTO) {
        CRoleDefEntity cRoleDefEntity = cRoleDefService.saveOrUpdate(roleDTO);
        cRoleService.saveOrUpdate(roleDTO, cRoleDefEntity);
        return Result.success(cRoleDefEntity.getId());
    }

    @Override
    public Result<RoleQueryVO> queryRoleDefDetail(Integer roleDefId) {
        RoleQueryVO roleQueryVO = cRoleDefService.queryRoleDefDetail(roleDefId);
        return Result.success(roleQueryVO);
    }

    @Override
    public Result queryRoleList(QueryDTO<CRoleQueryDTO> queryDTO) {
        return cRoleDefService.queryRoleList(queryDTO);
    }

    @Override
    public Result<List<RoleQueryVO>> queryRoleByCondition(CEmployRoleDTO employRoleDTO) {
        List<RoleQueryVO> roleQueryVOList = cRoleDefService.queryRoleByCondition(employRoleDTO);
        return Result.success(roleQueryVOList);
    }

    @Override
    public Result<RoleAuthDTO> getRoleAuth(RoleAuthQO roleAuthQO) {
        return Result.success(cRoleService.getRoleAuth(roleAuthQO));
    }

    @Override
    public Result<RoleAuthMenuDTO> getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO) {
        return Result.success(cRoleService.getRoleAuthMenu(roleAuthMenuQO));
    }

    @Override
    public Result<RoleAuthPowerDTO> getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO) {
        return Result.success(cRoleService.getRoleAuthPower(roleAuthPowerQO));
    }

    @Override
    public Result<LinkedHashSet<Integer>> queryCategory2List(Integer userId, Integer customerId) {
        return Result.success(cRoleService.queryCategory2List(userId, customerId));
    }

    @Override
    public Result<Boolean> isAdmin(Integer userId, Integer customerId) {
        log.info("判断是否管理员");
        Boolean isAdmin = cRoleService.isAdmin(userId, customerId);
        log.info("是否管理员：{}", isAdmin ? "是" : "否");
        return Result.success(isAdmin);
    }
}
