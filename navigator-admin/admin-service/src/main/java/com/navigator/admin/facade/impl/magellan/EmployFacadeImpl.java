package com.navigator.admin.facade.impl.magellan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.mapper.EmployMapper;
import com.navigator.admin.mapper.EmployRoleMapper;
import com.navigator.admin.mapper.RoleMapper;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.EmployDetailVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/1 13:44
 */
@RestController
public class EmployFacadeImpl implements EmployFacade {

    @Resource
    private EmployMapper employMapper;
    @Resource
    private EmployRoleMapper employRoleMapper;
    @Resource
    private RoleMapper roleMapper;
    @Autowired
    private IEmployService iEmployService;
    @Autowired
    private IEmployRoleService iEmployRoleService;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Override
    public Result getEmployByEmail(String email, Integer system) {
        List<EmployEntity> employEntityList = iEmployService.getEmployByEmail(email, system);
        return Result.success(employEntityList);
    }

    @Override
    public Result getEmployByPhone(String phone, Integer system) {
        List<EmployEntity> employEntityList = iEmployService.getEmployByPhone(phone, system);
        return Result.success(employEntityList);
    }

    @Override
    public EmployEntity getEmployById(Integer id) {
        return iEmployService.getEmployById(id);
    }

    @Override
    public EmployEntity getEmployNickName(String nickName, Integer system) {
        return iEmployService.getEmployByNickName(nickName,system);
    }

    @Override
    public List<EmployEntity> getEmployByEmployIds(List<Integer> employIds) {
        return iEmployService.getEmployByEmployIds(employIds);
    }

    @Override
    public Integer saveEmploy(EmployEntity employEntity) {
        return employMapper.insert(employEntity);
    }

    @Override
    public Integer editEmploy(EmployEntity employEntity) {

        return employMapper.updateById(employEntity);
    }

    @Override
    public Integer deleteEmploy(String employId) {
        return employMapper.deleteById(employId);
    }

    @Override
    public Result findRoleAdministratorEmploys() {


        List<EmployEntity> roleAdministratorEmploys = new ArrayList<>();

        QueryWrapper<RoleEntity> queryWrapperRole = new QueryWrapper<>();
        queryWrapperRole.eq("name", "角色管理员");
        RoleEntity roleAdministratorEntity = roleMapper.selectOne(queryWrapperRole);

        QueryWrapper<EmployRoleEntity> queryWrapperEmployRole = new QueryWrapper<>();
        queryWrapperEmployRole.eq("role_id", roleAdministratorEntity.getId());
        List<EmployRoleEntity> employRoleEntities = employRoleMapper.selectList(queryWrapperEmployRole);

        for (EmployRoleEntity employRoleEntity : employRoleEntities) {
            EmployEntity employEntity = employMapper.selectById(employRoleEntity.getEmployId());
            roleAdministratorEmploys.add(employEntity);
        }
        return Result.success(roleAdministratorEmploys);

    }

    @Override
    public EmployEntity ifNotExistToSave(EmployEntity employEntity) {
        return iEmployService.ifNotExistToSave(employEntity);
    }

    @Override
    public List<EmployEntity> queryEmployByRoleIds(List<Integer> roleIds) {
        List<EmployRoleEntity> employRoleEntityList = iEmployRoleService.getEmployRolesByRoleIds(roleIds);
        return iEmployService.getEmployByEmployIds(employRoleEntityList.stream().map(EmployRoleEntity::getEmployId).collect(Collectors.toList()));

    }

    @Override
    public Result getEmployByEmailOrPhone(String email, String phone, Integer system) {
        List<EmployEntity> employEntityList = iEmployService.getEmployByEmailOrPhone(email, phone, system);
        return Result.success(employEntityList);
    }

    @Override
    public Result queryChoosedEmployByRoleId(QueryDTO<RoleDTO> queryDTO) {
        return iEmployService.queryChoosedEmployByRoleId(queryDTO);
    }

    @Override
    public Result saveOrUpdateEmployByFile(MultipartFile file) {
        return iEmployService.saveOrUpdateEmployByFile(file);
    }

    @Override
    public Result createEmploy(EmployBusinessDTO employBusinessDTO) {
        iEmployService.createEmploy(employBusinessDTO);
        return Result.success();
    }

    @Override
    public Result exportEmployRoleList() {
       return iEmployService.exportEmployRoleList();
    }

    @Override
    public String getEmployCache(Integer id) {
        return redisCacheMap.get(id);
    }

    @Override
    public List<EmployVO> queryEmployByCompanyId(Integer companyId) {
        return BeanConvertUtils.convert2List(EmployVO.class, iEmployService.queryEmployByCompanyId(companyId));
    }

    @Override
    public Result updateEmployResetPassword(Integer id) {
        return Result.success(iEmployService.updateEmployResetPassword(id));
    }

    @Override
    public Result modifyPassword(EmployEntity employEntity) {
        return iEmployService.modifyPassword(employEntity);
    }

    @Override
    public List<EmployEntity> getEmployByRoleName(String roleName) {
        return iEmployService.getEmployByRoleName(roleName);
    }

    @Override
    public EmployEntity queryEmployByCustomerId(Integer customerId) {
        return iEmployService.queryEmployByCustomerId(customerId);
    }

    @Override
    public Result modifyEmploy(EmployBusinessDTO employBusinessDTO) {
        iEmployService.modifyEmploy(employBusinessDTO);
        return Result.success();
    }

    @Override
    public Result queryEmployList(QueryDTO<EmployDTO> queryDTO) {
        return iEmployService.queryEmployList(queryDTO);
    }

    @Override
    public EmployDetailVO queryEmployDetail(Integer employId) {
        return iEmployService.queryEmployDetail(employId);
    }

    @Override
    public Result<List<EmployVO>> queryEmployListByRoleDefId(Integer roleDefId) {
        List<EmployVO> list = iEmployService.queryEmployListByRoleDefId(roleDefId);
        return Result.success(list);
    }

    @Override
    public String resetPassword(Integer employId) {
        return iEmployService.resetPassword(employId);
    }

    @Override
    public Result<List<EmployVO>> queryAvailableEmployByRoleDefId(Integer roleDefId) {
        List<EmployVO> list = iEmployService.queryAvailableEmployByRoleDefId(roleDefId);
        return Result.success(list);
    }

    @Override
    public Result queryCategoryFactoryByRole() {
        CategoryFactoryMenuVO categoryFactoryMenuVO = iEmployService.queryCategoryFactoryByRole();
        return Result.success(categoryFactoryMenuVO);
    }

    @Override
    public PermissionBO queryPermission(String employId, Integer categoryId) {
        return iEmployService.queryPermissionByEmployId(employId, categoryId);
    }

    @Override
    public PermissionBO querySitePermission(String employId, Integer category2) {
        return iEmployService.querySitePermission(employId, category2);
    }

    @Override
    public Result updateEmployStatus(EmployDTO employDTO) {
        return iEmployService.updateEmployStatus(employDTO);
    }

    @Override
    public Result saveEmployStatus(EmployEntity employEntity) {
        return iEmployService.saveEmployStatus(employEntity);
    }

    @Override
    public Result resetUserPassword(ResetPasswordDTO resetPasswordDTO) {
        return iEmployService.resetUserPassword(resetPasswordDTO);
    }

    @Override
    public Result sendResetPasswordCode(String mobileNo) {
        return iEmployService.sendResetPasswordCode(mobileNo);
    }

    @Override
    public Result sendAadCode(String mobileNo) {
        return iEmployService.sendAadCode(mobileNo);

    }

    @Override
    public Result verifyAadCode(LoginDTO loginDTO) {
        return iEmployService.verifyAadCode(loginDTO);
    }

    @Override
    public Result importEmploy(MultipartFile file) {
        return iEmployService.importEmploy(file);
    }

    @Override
    public List<EmployEntity> getEmploy(String roleDefId, String categoryId, String factoryId) {
        return iEmployService.getEmploy(roleDefId, categoryId, factoryId);
    }

    @Override
    public List<EmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String factoryId) {
        return iEmployService.getEmployByRoleDefCode(roleDefCode, categoryId, factoryId);
    }

}
