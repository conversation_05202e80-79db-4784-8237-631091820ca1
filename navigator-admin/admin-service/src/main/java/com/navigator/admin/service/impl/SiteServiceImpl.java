package com.navigator.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.CommonRelationDao;
import com.navigator.admin.dao.SiteDao;
import com.navigator.admin.pojo.bo.CategoryBO;
import com.navigator.admin.pojo.entity.CommonRelationEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.CommonRelationTypeEnum;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.SiteService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.trade.facade.ContractFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 11:42
 **/
@Service
@Slf4j
public class SiteServiceImpl implements SiteService {
    @Resource
    private SiteDao siteDao;
    @Resource
    private CommonRelationDao commonRelationDao;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private ICompanyService companyService;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Autowired
    private ContractFacade contractFacade;

    @Override
    public Result querySiteByCondition(QueryDTO<SiteQO> queryDTO) {
        IPage<SiteEntity> siteEntityIPage = siteDao.querySiteByCondition(queryDTO);
        if (!CollectionUtils.isEmpty(siteEntityIPage.getRecords())) {
            siteEntityIPage.getRecords().forEach(this::assemblySiteInfo);
        }
        return Result.page(siteEntityIPage);
    }

    @Override
    public Boolean saveSite(SiteEntity siteEntity) {
        String siteCode = siteEntity.getCompanyCode() + "_" + siteEntity.getFactoryCode() + "_" + siteEntity.getCategory1();
        if (siteDao.judgeDuplicateSite(siteEntity.getName().trim(), siteCode)) {
            throw new BusinessException(ResultCodeEnum.SITE_EXIST);
        }
        String category2 = siteEntity.getCategory2SerialNoList().stream().map(String::valueOf).collect(Collectors.joining(","));
        String category3 = siteEntity.getCategory3SerialNoList().stream().map(String::valueOf).collect(Collectors.joining(","));
        String currentUserId = JwtUtils.getCurrentUserId();
        String userName = redisCacheMap.get(Integer.parseInt(currentUserId));

        siteEntity.setCode(siteCode)
                .setCategory2(category2)
                .setCategory3(category3)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(userName)
                .setCreatedBy(userName);
        siteDao.save(siteEntity);
        //账套+二级品类关系
        commonRelationDao.recordCommonRelation(siteCode, siteEntity.getCategory2SerialNoList().stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY2_RELATION);
        //账套+三级品类关系
        commonRelationDao.recordCommonRelation(siteCode, siteEntity.getCategory3SerialNoList().stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY3_RELATION);
        return true;
    }

    @Override
    public Boolean updateSite(SiteEntity siteEntity) {
        SiteEntity originalSiteEntity = siteDao.getById(siteEntity.getId());
        if (null == originalSiteEntity) {
            throw new BusinessException(ResultCodeEnum.SITE_NOT_EXIST);
        }
        //账套+二级品类关系
        commonRelationDao.recordCommonRelation(siteEntity.getCode(), siteEntity.getCategory2SerialNoList().stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY2_RELATION);
        this.processSiteCategory3Relation(siteEntity);
        String category2 = siteEntity.getCategory2SerialNoList().stream().map(String::valueOf).collect(Collectors.joining(","));
        String category3 = siteEntity.getCategory3SerialNoList().stream().map(String::valueOf).collect(Collectors.joining(","));
        // 禁用校验账套是否存在进行中合同
        if (DisableStatusEnum.DISABLE.getValue().equals(siteEntity.getStatus()) && DisableStatusEnum.ENABLE.getValue().equals(originalSiteEntity.getStatus())) {
            List<String> inProgressCategory3List = contractFacade.judgeCategory3InProcessContractForSite(new ArrayList<>(), siteEntity.getCode());
            if (!CollectionUtils.isEmpty(inProgressCategory3List)) {
                throw new BusinessException(ResultCodeEnum.SITE_EXIST_IN_PROCESS_CONTRACT, StringUtils.join(inProgressCategory3List, ","));
            }
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String userName = redisCacheMap.get(Integer.parseInt(currentUserId));
        originalSiteEntity.setStatus(siteEntity.getStatus())
                .setCategory2(category2)
                .setCategory3(category3)
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(userName);
        return siteDao.updateById(originalSiteEntity);
    }

    /**
     * 更新账套-3级品种的关系
     *
     * @param siteEntity
     */
    private void processSiteCategory3Relation(SiteEntity siteEntity) {
        //校验移除的品种 是否有除了已完成/已关闭的合同
        List<Integer> oldCategory3SerialNoList = commonRelationDao.queryByBindCode1(CommonRelationTypeEnum.SITE_CATEGORY3_RELATION.getValue(), siteEntity.getCode())
                .stream().map(CommonRelationEntity::getBindCode2).map(Integer::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oldCategory3SerialNoList) && !CollectionUtils.isEmpty(siteEntity.getCategory3SerialNoList())) {
            List<Integer> removeCategory3List = CommonListUtil.getDifferences(oldCategory3SerialNoList, siteEntity.getCategory3SerialNoList());
            if (!CollectionUtils.isEmpty(removeCategory3List)) {
                List<String> inProgressCategory3List = contractFacade.judgeCategory3InProcessContractForSite(removeCategory3List, siteEntity.getCode());
                if (!CollectionUtils.isEmpty(inProgressCategory3List)) {
                    throw new BusinessException(ResultCodeEnum.SITE_CATEGORY3_EXIST_IN_PROCESS_CONTRACT, StringUtils.join(inProgressCategory3List, ","));
                }
            }
        }
        //记录更新账套+三级品类关系
        commonRelationDao.recordCommonRelation(siteEntity.getCode(), siteEntity.getCategory3SerialNoList().stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY3_RELATION);
    }

    @Override
    public Boolean updateSiteStatus(Integer siteId, Integer status) {
        SiteEntity siteEntity = siteDao.getById(siteId);
        if (null == siteEntity) {
            throw new BusinessException(ResultCodeEnum.SITE_NOT_EXIST);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String userName = redisCacheMap.get(Integer.parseInt(currentUserId));
        siteEntity.setStatus(status)
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(userName);
        return siteDao.updateById(siteEntity);
    }

    @Override
    public List<SiteEntity> getSiteList(Integer companyId, Integer category2, String syncSystem, Integer status) {
        //1、过滤二级品类 commonRelation
        List<String> filterSiteCodeList = new ArrayList<>();
        if (null != category2) {
            List<CommonRelationEntity> category2SiteRelationList = commonRelationDao.queryByBindCode2(CommonRelationTypeEnum.SITE_CATEGORY2_RELATION.getValue(), String.valueOf(category2));
            filterSiteCodeList = category2SiteRelationList.stream().map(CommonRelationEntity::getBindCode1).collect(Collectors.toList());
        }
        List<SiteEntity> siteList = siteDao.querySiteList(companyId, category2, filterSiteCodeList, syncSystem, status);
        //2、查询二级品类下所有的3级品种（有效）
        List<CategoryEntity> category3List = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(category2).setStatus(DisableStatusEnum.ENABLE.getValue()));
        List<Integer> category3SerialNoList = category3List.stream().map(CategoryEntity::getSerialNo).collect(Collectors.toList());
        List<CustomerEntity> ldcCustomerList = customerFacade.getCustomerByCompanyId(null, GeneralEnum.YES.getValue());
        Map<Integer, CustomerEntity> ldcCustomerMap = ldcCustomerList.stream().collect(Collectors.toMap(CustomerEntity::getCompanyId, Function.identity()));
        if (!CollectionUtils.isEmpty(siteList)) {
            siteList.forEach(siteEntity -> {
                //3、账套的品种与二级品类下品种取交集，TT实际可以选择的品种信息
                List<Integer> siteCategory3IdList = commonRelationDao.queryByBindCode1(CommonRelationTypeEnum.SITE_CATEGORY3_RELATION.getValue(), siteEntity.getCode())
                        .stream().map(CommonRelationEntity::getBindCode2).map(Integer::valueOf).collect(Collectors.toList());
                List<Integer> commonCategory3List = CommonListUtil.getIntersection(siteCategory3IdList, category3SerialNoList);
                List<CategoryBO> categoryBOList = category3List.stream().filter(categoryEntity -> commonCategory3List.contains(categoryEntity.getSerialNo()))
                        .map(categoryEntity -> {
                            return new CategoryBO().setCategoryId(categoryEntity.getSerialNo())
                                    .setCategoryName(categoryEntity.getName())
                                    .setFutureCode(categoryEntity.getFutureCode());
                        }).collect(Collectors.toList());
                // 获取品类名称
                siteEntity.setCategory3List(categoryBOList)
                        .setCategory3SerialNoList(commonCategory3List);
                //4、主体名称信息
                CustomerEntity customerEntity = ldcCustomerMap.get(siteEntity.getCompanyId());

                CompanyEntity companyEntity = companyService.queryCompanyById(siteEntity.getCompanyId());
                siteEntity.setCompanyName(null == companyEntity ? "" : companyEntity.getName())
                        .setLdcCustomerId(customerEntity != null ? customerEntity.getId() : null)
                        .setSignPlace(customerEntity != null ? customerEntity.getSignPlace() : null);

                FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteEntity.getFactoryCode());
                siteEntity.setFactoryName(null != factoryEntity ? factoryEntity.getName() : "");
            });
        }
        return siteList;
    }

    @Override
    public SiteEntity getSiteByCode(String siteCode) {
        return siteDao.getSiteByCode(siteCode);
    }

    @Override
    public SiteEntity getSiteByName(String siteName) {
        return siteDao.getSiteByName(siteName);
    }

    @Override
    public SiteEntity getSiteDetailByCode(String siteCode) {
        SiteEntity siteEntity = siteDao.getSiteByCode(siteCode);
        if (null == siteEntity) {
            return null;
        }
        this.assemblySiteInfo(siteEntity);
        return siteEntity;
    }

    private void assemblySiteInfo(SiteEntity siteEntity) {
        List<CustomerEntity> ldcCustomerList = customerFacade.getCustomerByCompanyId(null, GeneralEnum.YES.getValue());
        Map<Integer, CustomerEntity> ldcCustomerMap = ldcCustomerList.stream().collect(Collectors.toMap(CustomerEntity::getCompanyId, Function.identity()));
        //3、账套的品种与二级品类下品种取交集，TT实际可以选择的品种信息
        List<Integer> category2SerialNoList = commonRelationDao.queryByBindCode1(CommonRelationTypeEnum.SITE_CATEGORY2_RELATION.getValue(), siteEntity.getCode())
                .stream().map(CommonRelationEntity::getBindCode2).map(Integer::valueOf).collect(Collectors.toList());
        // 获取品类名称
        siteEntity.setCategory2SerialNoList(category2SerialNoList);
        //4、账套的品种与三级品类下品种取交集，TT实际可以选择的品种信息
        List<Integer> category3SerialNoList = commonRelationDao.queryByBindCode1(CommonRelationTypeEnum.SITE_CATEGORY3_RELATION.getValue(), siteEntity.getCode())
                .stream().map(CommonRelationEntity::getBindCode2).map(Integer::valueOf).collect(Collectors.toList());
        // 获取品类名称
        siteEntity.setCategory3SerialNoList(category3SerialNoList);
        siteEntity.setCategory1Name(this.assemblyCategoryNames(Arrays.asList(Integer.valueOf(siteEntity.getCategory1()))))
                .setCategory2Name(this.assemblyCategoryNames(category2SerialNoList));
        List<CategoryEntity> category3EntityList = categoryFacade.getCategoryNameBySerialNoList(category3SerialNoList);
        if (!CollectionUtils.isEmpty(category3EntityList)) {
            Map<Integer, List<Integer>> category3Map = category3EntityList.stream().collect(Collectors.groupingBy(CategoryEntity::getParentId, Collectors.mapping(CategoryEntity::getSerialNo, Collectors.toList())));
            String category3Name = category3EntityList.stream().map(CategoryEntity::getName).distinct().collect(Collectors.joining(","));
            siteEntity.setCategory3Name(category3Name)
                    .setCategory3Map(category3Map);
        }
        //5、主体名称信息
        CompanyEntity companyEntity = companyService.queryCompanyById(siteEntity.getCompanyId());
        siteEntity.setCompanyName(null == companyEntity ? "" : companyEntity.getName())
                .setLdcCustomerId(ldcCustomerMap.get(siteEntity.getCompanyId()).getId());
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteEntity.getFactoryCode());
        siteEntity.setFactoryName(null != factoryEntity ? factoryEntity.getName() : "");
    }

    /**
     * 获取拼接品类名称
     *
     * @param categorySerialNoList
     * @return
     */
    private String assemblyCategoryNames(List<Integer> categorySerialNoList) {
        if (CollectionUtils.isEmpty(categorySerialNoList)) {
            return "";
        }
        return categoryFacade.assemblyCategoryNames(categorySerialNoList);
    }

    @Override
    public String getSiteCode(SiteQO siteQO) {
        SiteEntity siteEntity = siteDao.getSiteEntity(siteQO);
        return null == siteEntity ? "" : siteEntity.getCode();
    }

    @Override
    public SiteEntity getSiteByCompanyIdAndFactoryCode(Integer companyId, String factoryCode) {
        List<SiteEntity> siteEntityList = siteDao.getSiteByFactory(companyId, factoryCode);
        return CollectionUtils.isEmpty(siteEntityList) ? null : siteEntityList.get(0);
    }

    @Override
    public List<SiteEntity> querySiteList(SiteQO condition) {
        return siteDao.querySiteList(condition);
    }

    @Override
    public Set<String> queryFactoryCodeBySyncSystem(String syncSystem) {
        List<SiteEntity> list = siteDao.querySiteList(new SiteQO().setSyncSystem(syncSystem).setStatus(1));
        Set<String> set = new HashSet<>();
        list.forEach(item -> set.add(item.getFactoryCode()));
        return set;
    }

    @Override
public List<String> getAtlasSiteCodeList() {
    return siteDao.querySiteList(new SiteQO()).stream()
            .filter(siteEntity -> DisableStatusEnum.ENABLE.getValue().equals(siteEntity.getStatus()))
            .filter(siteEntity -> SyncSystemEnum.ATLAS.getValue().equals(siteEntity.getSyncSystem()))
            .map(SiteEntity::getAtlasCode)
            .distinct()
            .collect(Collectors.toList());
}

    @Override
    public SiteEntity getSiteBySystemCode(String syncSystem, String bizCode) {
        return siteDao.getSiteBySystemCode(syncSystem, bizCode);
    }

    @Override
    public Result importSite(MultipartFile file) {
        List<SiteEntity> siteImportList;
        List<SiteEntity> insertSiteList = new ArrayList<>();
        List<SiteEntity> updateSiteList = new ArrayList<>();
        try {
            siteImportList = EasyPoiUtils.importExcel(file, 0, 1, SiteEntity.class);
            if (!CollectionUtils.isEmpty(siteImportList)) {
                log.info("账套程序导入=====================解析数据条数：{}", siteImportList.size());
                siteImportList = siteImportList.stream().filter(site -> {
                    return StringUtils.isNotBlank(site.getCode()) && StringUtils.isNotBlank(site.getName());
                }).collect(Collectors.toList());
                log.info("账套程序导入=====================过滤需要处理的数据条数：{}", siteImportList.size());
                for (SiteEntity siteImportEntity : siteImportList) {
                    String siteCode = siteImportEntity.getCode();
                    SiteEntity siteEntity = siteDao.getSiteByCode(siteCode);
                    FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryByCode(siteImportEntity.getFactoryCode());
                    CompanyEntity companyEntity = companyService.getCompanyByCode(siteImportEntity.getCompanyCode());
                    List<Integer> category1IdList = this.getSerialNoListByCategoryNames(siteImportEntity.getCategory1Name(), 1);
                    List<Integer> category2IdList = this.getSerialNoListByCategoryNames(siteImportEntity.getCategory2Name(), 2);
                    List<Integer> category3IdList = this.getSerialNoListByCategoryNames(siteImportEntity.getCategory3Name(), 3);
                    Timestamp now = DateTimeUtil.now();
                    if (null == siteEntity) {
                        //新增账套
                        siteImportEntity
                                .setFactoryId(null != factoryEntity ? factoryEntity.getId() : 0)
                                .setCompanyId(null != companyEntity ? companyEntity.getId() : 0)
                                .setCategory1(CollectionUtil.join(category1IdList, ","))
                                .setCategory2(CollectionUtil.join(category2IdList, ","))
                                .setCategory3(CollectionUtil.join(category3IdList, ","))
                                .setSyncSystem(StringUtils.isNotBlank(siteImportEntity.getSyncSystem()) ? siteImportEntity.getSyncSystem() : SyncSystemEnum.ATLAS.getValue())
                                .setCreatedAt(now)
                                .setUpdatedAt(now)
                                .setCreatedBy("admin1")
                                .setUpdatedBy("admin1");
                        siteDao.save(siteImportEntity);
                        insertSiteList.add(siteImportEntity.setMemo("新增"));
                        log.info("程序导入==============新增账套：{},{}", siteCode, FastJsonUtils.getBeanToJson(siteImportEntity));
                    } else {
                        //更新账套
                        siteEntity.setName(siteImportEntity.getName())
                                .setLkgCode(siteImportEntity.getLkgCode())
                                .setAtlasCode(siteImportEntity.getAtlasCode())
                                .setFactoryCode(siteImportEntity.getFactoryCode())
                                .setFactoryId(null != factoryEntity ? factoryEntity.getId() : 0)
                                .setCompanyCode(siteImportEntity.getCompanyCode())
                                .setCompanyId(null != companyEntity ? companyEntity.getId() : 0)
                                .setCategory1(CollectionUtil.join(category1IdList, ","))
                                .setCategory2(CollectionUtil.join(category2IdList, ","))
                                .setCategory3(CollectionUtil.join(category3IdList, ","))
                                .setSyncSystem(StringUtils.isNotBlank(siteImportEntity.getSyncSystem()) ? siteImportEntity.getSyncSystem() : siteEntity.getSyncSystem())
                                .setBelongCustomerId(siteImportEntity.getBelongCustomerId())
                                .setStatus(siteImportEntity.getStatus())
                                .setUpdatedAt(now)
                                .setUpdatedBy("admin1");
                        siteDao.updateById(siteEntity);
                        updateSiteList.add(siteEntity.setMemo("更新"));
                        log.info("程序导入==============新增账套：{},{}", siteCode, FastJsonUtils.getBeanToJson(siteEntity));
                    }
                    // 账套与二级品类关系
                    commonRelationDao.recordCommonRelation(siteCode, category2IdList.stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY2_RELATION);
                    // 账套与品种关系
                    commonRelationDao.recordCommonRelation(siteCode, category3IdList.stream().map(String::valueOf).collect(Collectors.toList()), CommonRelationTypeEnum.SITE_CATEGORY3_RELATION);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("账套模板错误" + e.toString());
        }
        List<SiteEntity> processSiteList = new ArrayList<>();
        processSiteList.addAll(insertSiteList);
        processSiteList.addAll(updateSiteList);
        return Result.success("新增账套条数：" + insertSiteList.size() + "更新账套条数：" + updateSiteList.size(), processSiteList);
    }

    /**
     * 根据逗号隔开的品类名称，获取品类编号
     *
     * @param categoryNames
     * @param level
     * @return
     */
    private List<Integer> getSerialNoListByCategoryNames(String categoryNames, Integer level) {
        if (StringUtils.isBlank(categoryNames)) {
            return new ArrayList<>();
        }
        List<String> categoryNameList = Arrays.stream(categoryNames.split(","))
                .collect(Collectors.toList());
        return categoryFacade.getSerialNoListByCategoryNames(categoryNameList, level);
    }

    @Override
    public List<SiteEntity> getSiteListBySyncSystem(String syncSystem) {
        return siteDao.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(syncSystem), SiteEntity::getSyncSystem, syncSystem)
                .eq(SiteEntity::getStatus, DisableStatusEnum.ENABLE.getValue()));
    }
}
