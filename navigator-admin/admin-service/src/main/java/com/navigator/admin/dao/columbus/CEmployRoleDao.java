package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CEmployRoleMapper;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.ArrayList;
import java.util.List;

@Dao
public class CEmployRoleDao extends BaseDaoImpl<CEmployRoleMapper, CEmployRoleEntity> {

    public List<CEmployRoleEntity> getEmployRolesByEmploy(String employId) {
        return this.list(new LambdaQueryWrapper<CEmployRoleEntity>().eq(CEmployRoleEntity::getEmployId, employId)
                .isNotNull(CEmployRoleEntity::getCustomerId)
                .eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public List<CEmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds) {
        return this.list(Wrappers.<CEmployRoleEntity>lambdaQuery().in(CEmployRoleEntity::getRoleId, roleIds)
                .eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public void deleteByEmployId(Integer employId) {
        remove(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .eq(CEmployRoleEntity::getEmployId, employId));
    }

    public void deleteByEmployIdAndCustomerId(Integer employId, Integer customerId) {
        remove(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .eq(CEmployRoleEntity::getCustomerId, customerId)
                .eq(CEmployRoleEntity::getEmployId, employId)
        );
    }

    public List<CEmployRoleEntity> queryByRoleDefId(Integer roleDefId) {
        return list(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .eq(CEmployRoleEntity::getRoleDefId, roleDefId)
                .eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public void delete(CEmployRoleEntity employRoleEntity) {
        remove(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .eq(CEmployRoleEntity::getEmployId, employRoleEntity.getEmployId())
                .eq(CEmployRoleEntity::getRoleDefId, employRoleEntity.getRoleDefId())
        );
    }

    public List<CEmployRoleEntity> getEmployRolesByEmployAndCustomerId(Integer employId, Integer customerId) {
        return this.list(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .in(CEmployRoleEntity::getEmployId, employId)
                .eq(null != customerId, CEmployRoleEntity::getCustomerId, customerId)
                .eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CEmployRoleEntity> getEmployRolesByCustomerId(List<Integer> roleIds, Integer customerId) {
        return this.list(Wrappers.<CEmployRoleEntity>lambdaQuery()
                .in(CEmployRoleEntity::getRoleId, roleIds)
                .eq(CEmployRoleEntity::getCustomerId, customerId)
                .eq(CEmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 获取角色ID列表
     *
     * @param condition
     * @return
     */
    public List<Integer> queryRoleIdListByEmployId(EmployRoleQO condition) {
        List<CEmployRoleEntity> list = this.list(CEmployRoleEntity.lqw(condition));
        List<Integer> result = new ArrayList<>();
        list.forEach(item -> result.add(item.getRoleId()));
        return result;
    }
}
