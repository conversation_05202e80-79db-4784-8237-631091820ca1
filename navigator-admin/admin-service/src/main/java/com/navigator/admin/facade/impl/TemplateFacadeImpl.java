package com.navigator.admin.facade.impl;

import com.navigator.admin.dao.TemplateDao;
import com.navigator.admin.facade.TemplateFacade;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.admin.service.ITemplateService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.TemplateTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 19:31
 */
@RestController
public class TemplateFacadeImpl implements TemplateFacade {

    @Autowired
    private ITemplateService iTemplateService;
    @Autowired
    private TemplateDao templateDao;

    @Override
    public TemplateEntity getTemplateDetailById(Integer templateId) {
        return templateDao.getById(templateId);
    }

    @Override
    public TemplateEntity getTemplateDetailByCode(String templateCode) {
        return templateDao.getTemplateByCodeAndType(templateCode, null);
    }

    @Override
    public String getTemplateInfo(QueryTemplateAttributeDTO templateAttributeDTO) {
        return iTemplateService.getTemplateInfo(templateAttributeDTO);
    }

    @Override
    public Result importETemplateInfo(MultipartFile uploadFile) {
        return iTemplateService.importETemplateInfo(uploadFile);
    }

    @Override
    public Result importMTermAndE(MultipartFile uploadFile) {
        return iTemplateService.importMTermAndE(uploadFile);
    }

    @Override
    public Result exportTemplate(Integer status, HttpServletResponse response) {
        return iTemplateService.exportTemplate(status, response);
    }
    @Override
    public Result exportTemplateInfo(HttpServletResponse response){
        return iTemplateService.exportTemplateInfo( response);
    }

    @Override
    public Result getTemplateByCode(List<String> codeList) {
        List<TemplateEntity> templateEntityList = templateDao.getTemplateByCodeList(codeList);
        String templateIds = templateEntityList.stream().map(TemplateEntity::getId).map(String::valueOf).collect(Collectors.joining(","));
        return Result.success(templateIds);
    }

    @Override
    public Result saveOrUpdateTemplate(TemplateEntity templateEntity) {
        return Result.success(iTemplateService.saveOrUpdateTemplate(templateEntity));
    }

    @Override
    public Result queryTemplate(QueryDTO<QueryTemplateDTO> queryDTO) {
        return iTemplateService.queryTemplate(queryDTO);
    }

    @Override
    public TemplateEntity getLoginSignature() {
        return iTemplateService.getLoginSignature(TemplateTypeEnum.LOGIN_TEMPLATE.getValue());
    }

    @Override
    public Result syncTemplateCodeInfo(Integer type) {
        return iTemplateService.syncTemplateCodeInfo(type);
    }


}
