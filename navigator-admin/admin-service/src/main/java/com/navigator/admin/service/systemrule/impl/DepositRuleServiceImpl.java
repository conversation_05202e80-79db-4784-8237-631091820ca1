package com.navigator.admin.service.systemrule.impl;

import com.navigator.admin.dao.DepositRuleDao;
import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.DepositRuleEntity;
import com.navigator.admin.pojo.enums.systemrule.DepositSceneEnum;
import com.navigator.admin.service.systemrule.IDepositRuleService;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 * 保证金规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
@Service
public class DepositRuleServiceImpl implements IDepositRuleService {
    @Resource
    private DepositRuleDao depositRuleDao;

    @Override
    public boolean createDepositRule(DepositRuleEntity depositRuleEntity) {
        return depositRuleDao.save(depositRuleEntity);
    }

    @Override
    public DepositRuleEntity findDepositRule(DepositRuleDTO depositRuleDTO) {
        return depositRuleDao.findDepositRule(depositRuleDTO);
    }

    @Override
    public BigDecimal calcContractUseDeposit(DepositRuleDTO depositRuleDTO) {
        BigDecimal calcDeposit = BigDecimal.ZERO;

        // 1.判断保证金的使用规则是否存在
        DepositRuleEntity depositRule = this.findDepositRule(depositRuleDTO);
        if (depositRule == null) {
            return calcDeposit;
        }

        // 2.按照保证金类型
        if (depositRuleDTO.getRuleType().equals(DepositSceneEnum.FALL.getType())
                && depositRuleDTO.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            // 基差：基差价*合同数量
            calcDeposit = BigDecimalUtil.multiply(CalcTypeEnum.PRICE,
                    depositRuleDTO.getContractNum(),
                    new BigDecimal(depositRule.getValue()));
        } else {
            // 普通：合同的总金额*定金比例
            calcDeposit = BigDecimalUtil.multiply(CalcTypeEnum.PRICE,
                    depositRuleDTO.getTotalAmount(),
                    new BigDecimal(depositRule.getValue()).multiply(BigDecimal.valueOf(0.01)));
        }

        // TODO 资金动作（1、缴纳 2、补齐）
        return calcDeposit;
    }
}
