package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.SequenceFacade;
import com.navigator.admin.service.ISequenceService;
import com.navigator.common.enums.SequenceEnum;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 序列号 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@RestController
@Api(tags = "序列号")
public class SequenceFacadeImpl implements SequenceFacade {

    @Resource
    private ISequenceService sequenceService;

    @Override
    public String generate(String sequence, int length) {
        SequenceEnum sequenceEnum = SequenceEnum.getByValue(sequence);
        return sequenceService.generate(sequenceEnum, length);
    }

    @Override
    public String generateByDay(String sequence) {
        SequenceEnum sequenceEnum = SequenceEnum.getByValue(sequence);
        return sequenceService.generateByDay(sequenceEnum);
    }

    @Override
    public String generateByDayLength(String sequence, int length) {
        SequenceEnum sequenceEnum = SequenceEnum.getByValue(sequence);
        return sequenceService.generateByDayLength(sequenceEnum, length);
    }

    @Override
    public String generateWarrantCode(String exchange, String category) {
        return sequenceService.generateWarrantCode(exchange, category);
    }

    @Override
    public String generateWarrantSalesContractCode(String exchange, String category) {
        return sequenceService.generateWarrantSalesContractCode(exchange, category);
    }

    @Override
    public String generateWarrantSalesChildContractCode(String warrantSalesContractCode) {
        return sequenceService.generateWarrantSalesChildContractCode(warrantSalesContractCode);
    }

    @Override
    public String generateWarrantPurchaseContractCode(String exchange, String category) {
        return sequenceService.generateWarrantPurchaseContractCode(exchange, category);
    }

    @Override
    public String generateWarrantPurchaseChildContractCode(String warrantPurchaseContractCode) {
        return sequenceService.generateWarrantPurchaseChildContractCode(warrantPurchaseContractCode);
    }

    @Override
    public String generateWarrantContractTTCode() {
        return sequenceService.generateWarrantContractTTCode();
    }

    @Override
    public String generateWarrantContractSignCode() {
        return sequenceService.generateWarrantContractSignCode();
    }

    @Override
    public String generateWarrantContractCargoRightsCode(String contractCode) {
        return sequenceService.generateWarrantContractCargoRightsCode(contractCode);
    }

    @Override
    public String generateSpotSalesContractCode(String supplier, String category) {
        return sequenceService.generateSpotSalesContractCode(supplier, category);
    }

    @Override
    public String generateSpotSalesChildContractCode(String spotSalesContractCode) {
        return sequenceService.generateWarrantPurchaseChildContractCode(spotSalesContractCode);
    }

    @Override
    public String generateSpotPurchaseContractCode(String supplier, String category) {
        return sequenceService.generateSpotPurchaseContractCode(supplier, category);
    }

    @Override
    public String generateSpotPurchaseChildContractCode(String spotPurchaseContractCode) {
        return sequenceService.generateSpotPurchaseChildContractCode(spotPurchaseContractCode);
    }
}