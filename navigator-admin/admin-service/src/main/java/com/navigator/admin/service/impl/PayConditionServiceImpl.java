package com.navigator.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.PayConditionDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.dto.PayConditionImportDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.service.IPayConditionService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 付款条件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Service
public class PayConditionServiceImpl implements IPayConditionService {
    @Resource
    private PayConditionDao payConditionDao;
    @Resource
    private EmployFacade employFacade;

    @Override
    public List<PayConditionEntity> queryPayCondition(PayConditionDTO payConditionDTO) {
        return payConditionDao.queryPayCondition(payConditionDTO);
    }

    @Override
    public PayConditionEntity getPayConditionById(Integer payConditionId) {
        return payConditionDao.getById(payConditionId);
    }

    @Override
    public IPage<PayConditionEntity> getPayConditionList(QueryDTO<PayConditionDTO> queryDTO) {
        return payConditionDao.pagePayCondition(queryDTO);
    }

    @Override
    public boolean addPayCondition(PayConditionDTO payConditionDTO) {
        // 1.LKG编码是否重复
        List<PayConditionEntity> lkgPayConditionList = payConditionDao.queryPayCondition(new PayConditionDTO().setLkgCode(payConditionDTO.getLkgCode()));
        if (CollectionUtil.isNotEmpty(lkgPayConditionList)) {
            throw new BusinessException(ResultCodeEnum.REPEAT_LKG_CODE, lkgPayConditionList.get(0).getCode());
        }
        // 2.ATLAS编码是否重复
//        List<PayConditionEntity> atlasPayConditionList = payConditionDao.queryPayCondition(new PayConditionDTO().setAtlasCode(payConditionDTO.getAtlasCode()));
//        if (CollectionUtil.isNotEmpty(atlasPayConditionList)) {
//            throw new BusinessException(ResultCodeEnum.REPEAT_ATLAS_CODE, atlasPayConditionList.get(0).getMdmPayConditionCode());
//        }
        // 3.付款条件是否重复
        List<PayConditionEntity> payConditionEntityList = payConditionDao.queryPayCondition(new PayConditionDTO().setCreditDays(payConditionDTO.getCreditDays())
                .setDepositRate(payConditionDTO.getDepositRate())
                .setAddedDepositRate(payConditionDTO.getAddedDepositRate())
                .setBuCode(payConditionDTO.getBuCode())
                .setSalesType(payConditionDTO.getSalesType())
                .setInvoicePaymentRate(payConditionDTO.getInvoicePaymentRate()));
        if (CollectionUtil.isNotEmpty(payConditionEntityList)) {
            throw new BusinessException(ResultCodeEnum.REPEAT_PAYMENT_CODE.getCode(), "已存在LKG代码：" + payConditionEntityList.get(0).getCode()
                    + "，已存在ATLAS代码：" + payConditionEntityList.get(0).getMdmPayConditionCode());
        }

        PayConditionEntity payConditionEntity = BeanUtil.toBean(payConditionDTO, PayConditionEntity.class);

        // 获取当前用户
        String operator = "";
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (null != employEntity) {
            operator = employEntity.getName();
        }

        payConditionEntity.setName(payConditionDTO.getLkgCode())
                .setCode(payConditionDTO.getLkgCode())
                .setMdmPayConditionCode(payConditionDTO.getAtlasCode())
                .setIsDeleted(0)
                .setCreatedBy(operator)
                .setUpdatedBy(operator);

        return payConditionDao.save(payConditionEntity);
    }

    @Override
    public boolean updatePayCondition(PayConditionDTO payConditionDTO) {

        Integer status = payConditionDTO.getStatus();

        // 启用状态下，校验是否重复
        if (status != null && status == 1) {
            // 赊销天数+校验履约保证金比例+履约保证金比例后补缴+发票后补缴货款比例的组合与系统现存数据（包括禁用的）不重复
            List<PayConditionEntity> payConditionEntityList = payConditionDao.queryPayCondition(new PayConditionDTO().setCreditDays(payConditionDTO.getCreditDays())
                    .setDepositRate(payConditionDTO.getDepositRate())
                    .setAddedDepositRate(payConditionDTO.getAddedDepositRate())
                    .setBuCode(payConditionDTO.getBuCode())
                    .setSalesType(payConditionDTO.getSalesType())
                    .setInvoicePaymentRate(payConditionDTO.getInvoicePaymentRate())
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
            );

            if ((payConditionEntityList.size() == 1 && !payConditionEntityList.get(0).getId().equals(payConditionDTO.getPayConditionId()))
                    || payConditionEntityList.size() > 1) {
                throw new BusinessException(ResultCodeEnum.REPEAT_PAYMENT_CODE.getCode(), "已存在LKG代码：" + payConditionEntityList.get(0).getCode()
                        + "，已存在ATLAS代码：" + payConditionEntityList.get(0).getMdmPayConditionCode());
            }
        }

        PayConditionEntity payConditionEntity = BeanUtil.toBean(payConditionDTO, PayConditionEntity.class);

        // 获取当前用户
        String operator = "";
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (null != employEntity) {
            operator = employEntity.getName();
        }

        payConditionEntity.setId(payConditionDTO.getPayConditionId())
                .setName(payConditionDTO.getLkgCode())
                .setCode(payConditionDTO.getLkgCode())
                .setMdmPayConditionCode(payConditionDTO.getAtlasCode())
                .setUpdatedAt(new Date())
                .setUpdatedBy(operator);

        return payConditionDao.updateById(payConditionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importPayCondition(MultipartFile file) {
        StringBuilder builder = new StringBuilder();
        AtomicInteger createCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        List<PayConditionImportDTO> payConditionImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, PayConditionImportDTO.class);

        payConditionImportDTOS.forEach(payConditionImportDTO -> {
            Integer saleType = StringUtil.isNotBlank(payConditionImportDTO.getApplicableTypeInAtlas()) ?
                    ("采购/销售".equals(payConditionImportDTO.getApplicableTypeInAtlas()) ? 0 : "采购".equals(payConditionImportDTO.getApplicableTypeInAtlas()) ? 1 : 2) : null;

            String atlasMdmCode = StringUtil.isNotBlank(payConditionImportDTO.getAtlasPayConditionCode()) ? payConditionImportDTO.getAtlasPayConditionCode().trim() : "";

            // 更新mdmPayConditionCode
            if (payConditionImportDTO.getId() != null) {
                PayConditionEntity payConditionEntity = payConditionDao.getById(payConditionImportDTO.getId());

                payConditionEntity
                        .setMdmPayConditionCode(atlasMdmCode)
                        .setSalesType(saleType)
                        .setBuCode("现货".equals(payConditionImportDTO.getBuCode()) ? "ST" : "WT")
                        .setStatus("启用".equals(payConditionImportDTO.getStatus()) ? 1 : 0)
                        .setUpdatedAt(new Date())
                ;
                payConditionDao.updateById(payConditionEntity);

                updateCount.getAndIncrement();
            } else {
                // 新增
                PayConditionEntity payConditionEntity = new PayConditionEntity();
                payConditionEntity
                        .setName(payConditionImportDTO.getLkgPayConditionCode().trim())
                        .setCode(payConditionImportDTO.getLkgPayConditionCode().trim())
                        .setMdmPayConditionCode(atlasMdmCode)
                        .setBuCode("现货".equals(payConditionImportDTO.getBuCode()) ? "ST" : "WT")
                        .setCreditDays(StringUtil.isNotBlank(payConditionImportDTO.getCreditDays()) ? Integer.parseInt(payConditionImportDTO.getCreditDays()) : 0)
                        .setDepositRate(StringUtil.isNotBlank(payConditionImportDTO.getDepositRate()) ? Integer.parseInt(payConditionImportDTO.getDepositRate()) : 0)
                        .setAddedDepositRate(StringUtil.isNotBlank(payConditionImportDTO.getAddedDepositRate()) ? Integer.parseInt(payConditionImportDTO.getAddedDepositRate()) : 0)
                        .setInvoicePaymentRate(StringUtil.isNotBlank(payConditionImportDTO.getInvoicePaymentRate()) ? Integer.parseInt(payConditionImportDTO.getInvoicePaymentRate()) : 0)
                        .setIsDeleted(0)
                        .setStatus("启用".equals(payConditionImportDTO.getStatus()) ? 1 : 0)
                        .setSalesType(saleType)
                        .setCreatedBy("admin")
                ;
                payConditionDao.save(payConditionEntity);
                createCount.getAndIncrement();
            }
        });

        return builder.append("新增：").append(createCount.get()).append("条，更新：").append(updateCount.get()).append("条").toString();
    }

}
