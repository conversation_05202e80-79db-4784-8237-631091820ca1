package com.navigator.admin.facade.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.navigator.admin.dao.FileInfoDao;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.dto.FileItemDTO;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.service.IFileBusinessService;
import com.navigator.admin.service.IFileInfoService;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.FileDownloadUtil;
import com.navigator.common.util.file.FileUploadUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.common.util.html2pdf.Html2PdfUtils;
import com.navigator.common.util.qrcode.BarCodeUtils;
import com.navigator.common.util.qrcode.QrCodeUtil;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2021-12-03 18:03
 */
@RestController
@Slf4j
public class FileProcessFacadeImpl implements FileProcessFacade {
    @Resource
    private IFileInfoService fileInfoService;
    @Resource
    private FileInfoDao fileInfoDao;
    @Resource
    private IFileBusinessService fileBusinessService;
    @Autowired
    private AzureBlobUtil azureBlobUtil;
    @Resource
    private Html2PdfUtils html2PdfUtils;

    @Override
    public Result singleUpload(MultipartFile file) throws IllegalStateException, IOException, JSONException {
        //调用工具类完成上传，返回相关数据到页面
        FileBaseInfoDTO fileBaseInfoDTO = FileUploadUtil.singleUpload(file);
        if (fileBaseInfoDTO == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        //记录文件信息，并返回页面
        return Result.success(fileInfoService.saveFileInfo(fileBaseInfoDTO));
    }

    @Override
    public HttpServletResponse download(String path, HttpServletResponse response) {
        return FileDownloadUtil.download(path, response);
    }

    @Override
    public Boolean downLoadFileByUrl(String fileUrl, String filePath, String fileName) {
        return FileDownloadUtil.downLoadFileByUrl(fileUrl, filePath, fileName);
    }

    /**
     * 签章完成保存文件
     *
     * @param filePath
     * @param id
     * @return
     * @throws IOException
     */
    @Override
    public Result createFileItem(String filePath, Integer id, Integer categoryType) throws IOException {
        MultipartFile multipartFile = FileUploadUtil.createFileItem(filePath);
        FileBaseInfoDTO fileBaseInfoDTO = FileUploadUtil.singleUpload(multipartFile);
        FileInfoEntity fileInfoEntity = fileInfoService.saveFileInfo(fileBaseInfoDTO);

        // 保存文件关系表
        FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO()
                .setFileIdList(Collections.singletonList(fileInfoEntity.getId()))
                .setBizId(id)
                .setCategoryType(categoryType)
                .setModuleType(ModuleTypeEnum.CONTRACT.getModule());

        return fileBusinessService.recordFileRelation(fileBusinessRelationDTO);
    }

    /**
     * 签章完成保存文件
     *
     * @param fileItemDTO
     * @return
     * @throws IOException
     */
    @Override
    public Result saveFileItem(FileItemDTO fileItemDTO) {

        FileInfoEntity fileInfoEntity = fileInfoService.saveFileInfo(fileItemDTO.getFileBaseInfoDTO());
        // 保存文件关系表
        FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO()
                .setFileIdList(Collections.singletonList(fileInfoEntity.getId()))
                .setBizId(fileItemDTO.getId())
                .setCategoryType(fileItemDTO.getCategoryType())
                .setModuleType(ModuleTypeEnum.CONTRACT.getModule());
        return fileBusinessService.recordFileRelation(fileBusinessRelationDTO);
    }

    @Override
    public Result testBlobUtilFileItem(String url) {

        String fileName = FilePathUtil.genContractSignatureFileName(
                "test",
                DateTimeUtil.formatDateTimeValue() + new Random().nextInt(9999),
                FilePathType.PDF.getValue()
        );

        String filePath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT,
                FilePathType.PDF,
                String.valueOf("test"));

        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByUrl(url, filePath, fileName, BlobFileContextEnum.PDF.getFileType());
        return Result.success(fileBaseInfoDTO);
    }

    @Override
    public FileBaseInfoDTO html2Pdf(HtmlInfoDTO htmlInfoDTO) {
        log.info("html生成pdf" + FastJsonUtils.getBeanToJson(htmlInfoDTO));
        log.info("html22Pdf start" );
        FileBaseInfoDTO fileBaseInfoDTO = html2PdfUtils.genPdfAndImage(htmlInfoDTO);
        if (null != fileBaseInfoDTO) {
            fileBaseInfoDTO.setFileHostUrl(azureBlobUtil.getHostUrl() + fileBaseInfoDTO.getAttachUrl());
            fileBaseInfoDTO.setFileViewUrl(fileBaseInfoDTO.getFileHostUrl() + azureBlobUtil.getSharedAccessSignature());
        }
        log.info("fileBaseInfoDTO:{}", JSON.toJSONString(fileBaseInfoDTO));
        return fileBaseInfoDTO;
    }

    @Override
    public Result html2PdfV2(HtmlInfoDTO htmlInfoDTO) {
        log.info("html生成pdf" + htmlInfoDTO.getHtmlContent());
        log.info("html222pdf start ");
        return html2PdfUtils.executeV2(htmlInfoDTO.getHtmlContent());
    }

    @Override
    public HttpServletResponse downloadContractZip(List<Integer> fileIdList, Integer contractId, HttpServletResponse response) {
        //获取要压缩的文件集合
//        List<File> fileList = getZipFileList(fileIdList);
//        // 生成ZIP压缩文件
//        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.ZIP, contractId.toString());
//        FileUtil.createDir(zipPath);
//        zipPath = zipPath + "合同文件.zip";
//        ZipUtil.zipFiles(fileList, new File(zipPath));
//        return this.download(zipPath, response);
        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.ZIP, contractId.toString());
        zipPath = zipPath + "合同文件.zip";
        List<String> filePathList = getZipFilePathList(fileIdList);
        azureBlobUtil.downloadZip(filePathList, zipPath, response);
        return response;
    }

    @Override
    public FileInfoEntity generateBarCodeImg(String orderCode, String displayCode) {
        try {
            BufferedImage image = StringUtils.isBlank(displayCode) ? BarCodeUtils.getBarCode(orderCode) :
                    BarCodeUtils.createBarCodeImg(orderCode, displayCode);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", os);
            InputStream inputStream = new ByteArrayInputStream(os.toByteArray());
            FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByInputStream(inputStream, FileConstant.FILE_BARCODE_IMG, "条形码.png", BlobFileContextEnum.PNG.getFileType());
            return fileInfoService.saveFileInfo(fileBaseInfoDTO);
        } catch (IOException e) {
            log.error("下载条形码图片，错误信息：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public FileInfoEntity generateQrCodeImg(String url) {
        try {
            BufferedImage image = QrCodeUtil.getBufferedImage(url, 500, null);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", os);
            InputStream inputStream = new ByteArrayInputStream(os.toByteArray());
            FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByInputStream(inputStream, FileConstant.FILE_BARCODE_IMG, "二维码.png", BlobFileContextEnum.PNG.getFileType());
            return fileInfoService.saveFileInfo(fileBaseInfoDTO);
        } catch (IOException e) {
            log.error("下载条形码图片，错误信息：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取要压缩的文件集合
     *
     * @param fileIdList 文件ID集合
     * @return 文件集合
     */
    private List<File> getZipFileList(List<Integer> fileIdList) {
        List<FileInfoEntity> fileInfoEntityList = fileInfoDao.getFileListByIds(fileIdList);
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        List<File> fileList = new ArrayList<>(fileInfoEntityList.size());
        fileInfoEntityList.forEach(fileInfoEntity -> {
            File file = new File(fileInfoEntity.getPath());
            if (file.exists()) {
                fileList.add(file);
            }
        });
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        return fileList;
    }

    /**
     * 获取要压缩的文件集合
     *
     * @param fileIdList 文件ID集合
     * @return 文件集合
     */
    @Override
    public Result getFileListByIds(List<Integer> fileIdList) {
        return Result.success(fileInfoDao.getFileListByIds(fileIdList));
        // return Result.success(JSON.toJSONString(employEntityList));
    }

    /**
     * 获取要压缩的文件集合
     *
     * @param fileIdList 文件ID集合
     * @return 文件集合
     */
    private List<String> getZipFilePathList(List<Integer> fileIdList) {
        List<FileInfoEntity> fileInfoEntityList = fileInfoDao.getFileListByIds(fileIdList);
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        List<String> filePathList = new ArrayList<>(fileInfoEntityList.size());
        fileInfoEntityList.forEach(fileInfoEntity -> {
            filePathList.add(fileInfoEntity.getPath());
        });
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        return filePathList;
    }
}
