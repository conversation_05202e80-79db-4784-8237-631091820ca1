package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.BasicPriceGoodsAttributeConfigMapper;
import com.navigator.admin.pojo.entity.BasicPriceGoodsAttributeConfigEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;


import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17
 */
@Dao
public class BasicPriceGoodsAttributeConfigDao extends BaseDaoImpl<BasicPriceGoodsAttributeConfigMapper, BasicPriceGoodsAttributeConfigEntity> {

    public List<BasicPriceGoodsAttributeConfigEntity> queryBasicPriceGoodsAttributeConfigByAttributeValueId(Integer attributeValueId) {
        return this.baseMapper.selectList(Wrappers.<BasicPriceGoodsAttributeConfigEntity>lambdaQuery()
                .eq(BasicPriceGoodsAttributeConfigEntity::getAttributeValueId, attributeValueId)
        );
    }
}
