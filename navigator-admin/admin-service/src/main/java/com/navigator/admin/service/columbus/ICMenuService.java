package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CMenuEntity;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;

import java.util.List;

public interface ICMenuService {
    /**
     * 根据id找菜单
     *
     * @param id
     * @return
     */
    CMenuEntity getMenuById(String id);

    /**
     * 根据employId 获取菜单（树状结构）
     *
     * @param menuDTO
     * @return
     */
    CMenuVO getMenusByEmploy(CMenuDTO menuDTO);

    CMenuVO getMenusByCondition(CMenuDTO menuDTO, Integer customerId);

    CMenuVO getMenuByRoleId(CRoleDTO roleDTO);

    CMenuVO getMenuByEmployId(CRoleDTO roleDTO);

    void saveRoleMenu(CMenuDTO menuDTO);

    /**
     * v2.0 统一菜单按钮权限
     */

    CMenuVO getMenusByConditionV2(CMenuDTO menuDTO);

    CMenuVO getMenuByRoleIdV2(CRoleDTO roleDTO);

    CMenuVO getMenuByEmployIdV2(CRoleDTO roleDTO);

    void saveRoleMenuV2(CMenuDTO menuDTO);

    void addRoleMenu(CMenuDTO menuDTO);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<CMenuEntity> queryMenuList(MenuQO condition);

    /**
     * 获取授权的二级菜单列表
     *
     * @param userId
     * @param category2
     * @param customerId
     * @return
     */
    List<CMenuEntity> getAuthMenuList(Integer userId, Integer category2, Integer customerId);
}
