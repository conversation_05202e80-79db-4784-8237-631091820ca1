package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.rule.RuleDictItemMapper;
import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 18:27
 **/
@Dao
public class RuleDictItemDao extends BaseDaoImpl<RuleDictItemMapper, RuleDictItemEntity> {


    public IPage<RuleDictItemEntity> queryByCondition(QueryDTO<RuleDictItemEntity> queryDTO) {
        RuleDictItemEntity dictItemQO = queryDTO.getCondition();
        LambdaQueryWrapper<RuleDictItemEntity> queryWrapper = getDictItemQueryWrapper(dictItemQO);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<RuleDictItemEntity> queryDictItemList(RuleDictItemEntity dictItemQO) {
        LambdaQueryWrapper<RuleDictItemEntity> queryWrapper = getDictItemQueryWrapper(dictItemQO);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<RuleDictItemEntity> getDictItemQueryWrapper(RuleDictItemEntity dictItemQO) {
        if (StringUtils.isNotBlank(dictItemQO.getUpdatedBy())) {
            dictItemQO.setUpdatedBy(dictItemQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<RuleDictItemEntity> queryWrapper = new LambdaQueryWrapper<RuleDictItemEntity>()
                .eq(null != dictItemQO.getStatus(), RuleDictItemEntity::getStatus, dictItemQO.getStatus())
                .eq(StringUtils.isNotBlank(dictItemQO.getDictCode()), RuleDictItemEntity::getDictCode, dictItemQO.getDictCode())
                .like(StringUtils.isNotBlank(dictItemQO.getUpdatedBy()), RuleDictItemEntity::getUpdatedBy, dictItemQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(dictItemQO.getStartDay()) && StringUtils.isNotBlank(dictItemQO.getEndDay()), RuleDictItemEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(dictItemQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(dictItemQO.getEndDay()))
                .eq(StringUtils.isNotBlank(dictItemQO.getModuleType()), RuleDictItemEntity::getModuleType, dictItemQO.getModuleType())
                .eq(StringUtils.isNotBlank(dictItemQO.getSystemId()), RuleDictItemEntity::getSystemId, dictItemQO.getSystemId())
                .eq(RuleDictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(dictItemQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(dictItemQO.getSearchKey()), RuleDictItemEntity::getItemName, dictItemQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(dictItemQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(dictItemQO.getSearchKey()), RuleDictItemEntity::getItemCode, dictItemQO.getSearchKey().trim()));
        }
        queryWrapper.orderByDesc(RuleDictItemEntity::getId);
        return queryWrapper;
    }

    public List<RuleDictItemEntity> getDictItemById(List<Integer> dictItemIdList) {
        return list(Wrappers.<RuleDictItemEntity>lambdaQuery()
                .in(RuleDictItemEntity::getId, dictItemIdList)
                .eq(RuleDictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public RuleDictItemEntity getDictItemByCode(String dictCode, String itemCode, Integer itemValue, String moduleType, String systemId) {
        List<RuleDictItemEntity> RuleDictItemEntityList = list(Wrappers.<RuleDictItemEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(dictCode), RuleDictItemEntity::getDictCode, dictCode)
                .eq(StringUtils.isNotBlank(itemCode), RuleDictItemEntity::getItemCode, itemCode)
                .eq(null != itemValue, RuleDictItemEntity::getDictCode, dictCode)
                .eq(StringUtils.isNotBlank(moduleType), RuleDictItemEntity::getModuleType, moduleType)
                .eq(StringUtils.isNotBlank(systemId), RuleDictItemEntity::getSystemId, systemId)
                .eq(RuleDictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(RuleDictItemEntityList) ? null : RuleDictItemEntityList.get(0);
    }

    public List<RuleDictItemEntity> getRuleItemByDictCode(String dictCode, String moduleType, String systemId) {
        return list(Wrappers.<RuleDictItemEntity>lambdaQuery()
                .eq(RuleDictItemEntity::getDictCode, dictCode)
                .eq(StringUtils.isNotBlank(moduleType), RuleDictItemEntity::getModuleType, moduleType)
                .eq(StringUtils.isNotBlank(systemId), RuleDictItemEntity::getSystemId, systemId)
                .eq(RuleDictItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(RuleDictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(RuleDictItemEntity::getItemSort)
        );
    }


    public List<RuleDictItemEntity> getDictItemByCode(String dictCode, String itemName, String itemCode, String moduleType, String systemId) {
        return list(Wrappers.<RuleDictItemEntity>lambdaQuery()
                .eq(RuleDictItemEntity::getDictCode, dictCode)
                .eq(RuleDictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(StringUtils.isNotBlank(itemName), RuleDictItemEntity::getItemName, itemName)
                .eq(StringUtils.isNotBlank(moduleType), RuleDictItemEntity::getModuleType, moduleType)
                .eq(StringUtils.isNotBlank(systemId), RuleDictItemEntity::getSystemId, systemId)
                .or().eq(StringUtils.isNotBlank(itemCode), RuleDictItemEntity::getItemCode, itemCode)
        );
    }
}
