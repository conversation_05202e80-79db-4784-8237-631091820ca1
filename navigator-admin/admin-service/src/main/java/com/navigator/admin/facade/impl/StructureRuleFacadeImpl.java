package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.admin.service.systemrule.StructureRuleService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class StructureRuleFacadeImpl implements StructureRuleFacade {
    @Autowired
    private StructureRuleService structureRuleService;
    @Override
    public Result save(StructureRuleDTO structureRuleDTO) {
        structureRuleService.save(structureRuleDTO);
        return Result.success();
    }

    @Override
    public Result modify(StructureRuleDTO structureRuleDTO) {
        structureRuleService.modify(structureRuleDTO);
        return Result.success();
    }

    @Override
    public Result updateStatus(StructureRuleDTO structureRuleDTO) {
        structureRuleService.updateStatus(structureRuleDTO);
        return Result.success();
    }

    @Override
    public Result queryStructureCode() {
        return structureRuleService.queryStructureCode();
    }

    @Override
    public Result queryStructureList(QueryDTO<StructureRuleDTO> structureRuleDTOQueryDTO) {
        return structureRuleService.queryStructureList(structureRuleDTOQueryDTO);
    }

    @Override
    public Result queryById(Integer id) {
        return structureRuleService.queryById(id);
    }

    @Override
    public String getNameById(Integer id) {
        return structureRuleService.getNameById(id);
    }

    @Override
    public Result queryAvailableStructureList() {
        return structureRuleService.queryAvailableStructureList();
    }


}
