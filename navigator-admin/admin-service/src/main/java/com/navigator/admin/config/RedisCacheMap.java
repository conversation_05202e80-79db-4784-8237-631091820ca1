package com.navigator.admin.config;

import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.service.magellan.IEmployService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RedisCacheMap implements ApplicationRunner {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IEmployService employService;

    private static final String EMPLOY_PREFIX = "cache:employ:EMPLOY_CACHE_";

    @Override
    public void run(ApplicationArguments args) throws Exception {
        List<EmployEntity> employList = employService.getAllEmployList();
        Map<Integer, String> employNameMap = employList.stream().collect(Collectors.toMap(EmployEntity::getId, EmployEntity::getName));
        employNameMap.forEach((key, value) -> stringRedisTemplate.opsForValue().set(EMPLOY_PREFIX + key, value));
    }

    public String get(Integer id) {
        String employName = stringRedisTemplate.opsForValue().get(EMPLOY_PREFIX + id);
        if (StringUtils.isNotBlank(employName)) {
            return employName;
        }
        EmployEntity employEntity = employService.getEmployById(id);
        if (employEntity != null) {
            stringRedisTemplate.opsForValue().set(EMPLOY_PREFIX + id, employEntity.getName());
            return employEntity.getName();
        }
        return null;
    }

}
