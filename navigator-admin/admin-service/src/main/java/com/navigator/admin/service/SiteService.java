package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 11:39
 **/
public interface SiteService {
    /**
     * 列表分页查询账套信息
     *
     * @param queryDTO
     * @return
     */
    Result querySiteByCondition(QueryDTO<SiteQO> queryDTO);


    /**
     * 根据账套编码获取账套信息
     *
     * @param siteCode
     * @return
     */
    SiteEntity getSiteDetailByCode(String siteCode);

    /**
     * 保存账套
     *
     * @param siteEntity
     * @return
     */
    Boolean saveSite(SiteEntity siteEntity);

    /**
     * 编辑账套
     *
     * @param siteEntity
     * @return
     */
    Boolean updateSite(SiteEntity siteEntity);

    /**
     * 启用/禁用账套
     *
     * @param siteId
     * @param status
     * @return
     */
    Boolean updateSiteStatus(Integer siteId, Integer status);

    /**
     * 根据主体ID、二级品类获取账套信息
     *
     * @param companyId 主体ID
     * @param category2 二级品类
     * @return
     */
    List<SiteEntity> getSiteList(Integer companyId, Integer category2, String syncSystem, Integer status);

    /**
     * 根据账套编码 获取账套实体
     *
     * @param siteCode 账套编码
     * @return
     */
    SiteEntity getSiteByCode(String siteCode);

    /**
     * 根据账套名称 获取账套实体
     *
     * @param siteName 账套名称
     * @return SiteEntity
     */
    SiteEntity getSiteByName(String siteName);

    /**
     * 获取账套编码
     *
     * @param siteQO
     * @return
     */
    String getSiteCode(SiteQO siteQO);

    /**
     * 根据主体ID和工厂编码，查询账套集合
     *
     * @param companyId   主体ID
     * @param factoryCode 工厂编码
     * @return
     */
    SiteEntity getSiteByCompanyIdAndFactoryCode(Integer companyId, String factoryCode);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<SiteEntity> querySiteList(SiteQO condition);

    /**
     * 获取工厂编码
     *
     * @param syncSystem
     * @return
     */
    Set<String> queryFactoryCodeBySyncSystem(String syncSystem);

    /**
     * 获取Atlas账套编码列表
     *
     * @return
     */
    List<String> getAtlasSiteCodeList();

    SiteEntity getSiteBySystemCode(String syncSystem, String bizCode);

    Result importSite(MultipartFile file);

    List<SiteEntity> getSiteListBySyncSystem(String syncSystem);
}
