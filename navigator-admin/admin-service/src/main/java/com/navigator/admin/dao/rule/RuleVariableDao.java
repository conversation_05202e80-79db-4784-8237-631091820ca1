package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.rule.RuleVariableMapper;
import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 18:27
 **/
@Dao
public class RuleVariableDao extends BaseDaoImpl<RuleVariableMapper, RuleVariableEntity> {

    public List<RuleVariableEntity> getAllVariableList(Integer isCondition, Integer isKey, String moduleType, String systemId) {
        return this.list(new LambdaQueryWrapper<RuleVariableEntity>()
                .eq(null != isCondition, RuleVariableEntity::getIsCondition, isCondition)
                .eq(null != isKey, RuleVariableEntity::getIsKey, isKey)
                .eq(StringUtils.isNotBlank(moduleType), RuleVariableEntity::getModuleType, moduleType)
                .eq(StringUtils.isNotBlank(systemId), RuleVariableEntity::getSystemId, systemId)
                .eq(RuleVariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public IPage<RuleVariableEntity> queryVariableByCondition(QueryDTO<RuleVariableEntity> queryDTO) {
        RuleVariableEntity variableQO = queryDTO.getCondition();
        LambdaQueryWrapper<RuleVariableEntity> queryWrapper = getVariableQueryWrapper(variableQO);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<RuleVariableEntity> queryExportVariableList(RuleVariableEntity variableQO) {
        LambdaQueryWrapper<RuleVariableEntity> queryWrapper = getVariableQueryWrapper(variableQO);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<RuleVariableEntity> getVariableQueryWrapper(RuleVariableEntity variableQO) {
        LambdaQueryWrapper<RuleVariableEntity> queryWrapper = new LambdaQueryWrapper<RuleVariableEntity>()
                .eq(null != variableQO.getValueType(), RuleVariableEntity::getValueType, variableQO.getValueType())
                .eq(null != variableQO.getIsKey(), RuleVariableEntity::getIsKey, variableQO.getIsKey())
                .eq(null != variableQO.getIsCondition(), RuleVariableEntity::getIsCondition, variableQO.getIsCondition())
                .eq(StringUtils.isNotBlank(variableQO.getModuleType()), RuleVariableEntity::getModuleType, variableQO.getModuleType())
                .eq(StringUtils.isNotBlank(variableQO.getSystemId()), RuleVariableEntity::getSystemId, variableQO.getSystemId());
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(variableQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(variableQO.getSearchKey()), RuleVariableEntity::getName, "%" + variableQO.getSearchKey().trim() + "%")
                    .or(StringUtils.isNotBlank(variableQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(variableQO.getSearchKey()), RuleVariableEntity::getDisplayName, "%" + variableQO.getSearchKey().trim() + "%"));
        }
        return queryWrapper;
    }

    /**
     * 根据变量编码获取变量
     *
     * @param code 编码
     * @return
     */
    public RuleVariableEntity getVariableByCode(String code, String moduleType, String systemId) {
        List<RuleVariableEntity> RuleVariableEntityList = this.list(new LambdaQueryWrapper<RuleVariableEntity>()
                .eq(RuleVariableEntity::getCode, code)
                .eq(StringUtils.isNotBlank(moduleType), RuleVariableEntity::getModuleType, moduleType)
                .eq(StringUtils.isNotBlank(systemId), RuleVariableEntity::getSystemId, systemId)
                .eq(RuleVariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(RuleVariableEntityList) ? RuleVariableEntityList.get(0) : null;
    }

    /**
     * 根据变量展示名获取变量
     *
     * @param displayName 变量展示名
     * @return
     */
    public RuleVariableEntity getVariableByDisplayName(String displayName) {
        List<RuleVariableEntity> RuleVariableEntityList = this.list(new LambdaQueryWrapper<RuleVariableEntity>()
                .eq(RuleVariableEntity::getDisplayName, displayName)
                .eq(RuleVariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(RuleVariableEntityList) ? RuleVariableEntityList.get(0) : null;
    }
}
