package com.navigator.admin.dao;

import cn.hutool.core.collection.CollUtil;
import com.navigator.admin.mapper.SequenceMapper;
import com.navigator.admin.pojo.entity.SequenceEntity;
import com.navigator.admin.pojo.qo.SequenceQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 序列号 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Dao
public class SequenceDao extends BaseDaoImpl<SequenceMapper, SequenceEntity> {

    /**
     * 获取序列号
     *
     * @param redisKey
     * @return
     */
    public SequenceEntity getOrAdd(String redisKey) {
        List<SequenceEntity> list = this.list(SequenceEntity.lqw(new SequenceQO().setRedisKey(redisKey)));
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        SequenceEntity sequenceEntity = new SequenceEntity();
        sequenceEntity.setRedisKey(redisKey);
        sequenceEntity.setRedisValue(0L);
        sequenceEntity.setCreatedBy(JwtUtils.getCurrentUserId());
        sequenceEntity.setCreatedAt(new Date());
        sequenceEntity.setUpdatedBy(sequenceEntity.getCreatedBy());
        sequenceEntity.setUpdatedAt(sequenceEntity.getUpdatedAt());
        sequenceEntity.setIsDeleted(0);
        this.save(sequenceEntity);
        return sequenceEntity;
    }
}
