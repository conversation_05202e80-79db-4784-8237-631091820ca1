package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.DepositRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.DepositRuleEntity;
import com.navigator.admin.service.systemrule.IDepositRuleService;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

@RestController
public class DepositRuleFacadeImpl implements DepositRuleFacade {
    @Resource
    private IDepositRuleService depositRuleService;

    @Override
    public Result createDepositRule(DepositRuleEntity depositRuleEntity) {
        return Result.success(depositRuleService.createDepositRule(depositRuleEntity));
    }

    @Override
    public Result findDepositRule(DepositRuleDTO depositRuleDTO) {
        return Result.success(depositRuleService.findDepositRule(depositRuleDTO));
    }

    @Override
    public BigDecimal calcContractUseDeposit(DepositRuleDTO depositRuleDTO) {
        return depositRuleService.calcContractUseDeposit(depositRuleDTO);
    }

}
