package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.service.IFileBusinessService;
import com.navigator.admin.service.IFileInfoService;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-11-29 18:29
 */
@RestController
public class FileBusinessFacadeImpl implements FileBusinessFacade {
    @Resource
    private IFileInfoService fileInfoService;
    @Resource
    private IFileBusinessService fileBusinessService;

    @Override
    public FileInfoEntity saveFileInfo(FileBaseInfoDTO fileBaseInfoDTO) {
        return fileInfoService.saveFileInfo(fileBaseInfoDTO);
    }

    @Override
    public Result recordFileRelation(FileBusinessRelationDTO fileBusinessRelationDTO) {
        return fileBusinessService.recordFileRelation(fileBusinessRelationDTO);
    }

    @Override
    public void dropFileRelation(Integer referId, Integer fileCategoryType, String memo) {
        fileBusinessService.dropFileRelation(referId, fileCategoryType, memo);
    }

    @Override
    public List<FileInfoEntity> getFileInfoByBizIdAndType(Integer bizId, Integer fileCategoryType, Integer statusEnum) {
        return fileBusinessService.getFileInfoByBizIdAndType(bizId, Arrays.asList(fileCategoryType), statusEnum, null);
    }

    @Override
    public List<FileInfoEntity> getFileInfoByBizIdAndTypeList(Integer bizId, List<Integer> fileCategoryTypeList, Integer statusEnum, Integer system) {
        return fileBusinessService.getFileInfoByBizIdAndType(bizId, fileCategoryTypeList, statusEnum, system);
    }

    @Override
    public Map<Integer, List<FileInfoEntity>> getFileMapByBizIdAndType(Integer bizId, List<Integer> fileCategoryTypeList, Integer statusEnum) {
        return fileBusinessService.getFileMapByBizIdAndType(bizId, fileCategoryTypeList, statusEnum);
    }
}
