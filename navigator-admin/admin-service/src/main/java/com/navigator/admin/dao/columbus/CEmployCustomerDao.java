package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CEmployCustomerMapper;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */
@Dao
public class CEmployCustomerDao extends BaseDaoImpl<CEmployCustomerMapper, CEmployCustomerEntity> {


    public List<CEmployCustomerEntity> cEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return this.list(new LambdaQueryWrapper<CEmployCustomerEntity>()
                .eq(null != cEmployId, CEmployCustomerEntity::getCEmployId, cEmployId)
                .eq(null != customerId, CEmployCustomerEntity::getCustomerId, customerId)
                .ne(CEmployCustomerEntity::getCustomerId, -100)
                .eq(CEmployCustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CEmployCustomerEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .orderByDesc(CEmployCustomerEntity::getVisitTime)
        );
    }

    public List<CEmployCustomerEntity> getCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return this.list(new LambdaQueryWrapper<CEmployCustomerEntity>()
                .eq(null != cEmployId, CEmployCustomerEntity::getCEmployId, cEmployId)
                .eq(null != customerId, CEmployCustomerEntity::getCustomerId, customerId)
                .ne(CEmployCustomerEntity::getCustomerId, -100)
                .eq(CEmployCustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndType(Integer customerId, Integer type, Integer status) {

        return this.list(new LambdaQueryWrapper<CEmployCustomerEntity>()
                .eq(null != type, CEmployCustomerEntity::getType, type)
                .eq(null != customerId, CEmployCustomerEntity::getCustomerId, customerId)
                .eq(CEmployCustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != status, CEmployCustomerEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    public List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndCEmployIdAndType(Integer customerId, Integer cEmployId, Integer type) {

        return this.list(new LambdaQueryWrapper<CEmployCustomerEntity>()
                .eq(null != type, CEmployCustomerEntity::getType, type)
                .eq(null != cEmployId, CEmployCustomerEntity::getCEmployId, cEmployId)
                .eq(null != customerId, CEmployCustomerEntity::getCustomerId, customerId)
                .eq(CEmployCustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CEmployCustomerEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    public List<CEmployCustomerEntity> getCEmployCustomerByCustomerIdAndType(Integer customerId, Integer type) {
        return this.list(new LambdaQueryWrapper<CEmployCustomerEntity>()
                .eq(null != type, CEmployCustomerEntity::getType, type)
                .eq(null != customerId, CEmployCustomerEntity::getCustomerId, customerId)
                .eq(CEmployCustomerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void updateTypeByIds(List<Integer> ids) {
        update(Wrappers.<CEmployCustomerEntity>lambdaUpdate()
                .set(CEmployCustomerEntity::getType, CEmployTypeEnum.OTHER.getType())
                .in(CEmployCustomerEntity::getId, ids)
        );
    }
}
