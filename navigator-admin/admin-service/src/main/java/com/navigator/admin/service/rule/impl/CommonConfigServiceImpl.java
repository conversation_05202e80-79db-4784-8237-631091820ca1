package com.navigator.admin.service.rule.impl;

import com.navigator.admin.dao.rule.CommonConfigDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.rule.*;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import com.navigator.admin.pojo.entity.rule.CommonConfigEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.service.rule.CommonConfigService;
import com.navigator.admin.service.rule.IBusinessRuleService;
import com.navigator.admin.service.rule.IRuleMatchService;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.dto.CategoryDTO;
import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-18 17:35
 **/
@Service
@Slf4j
public class CommonConfigServiceImpl implements CommonConfigService {

    @Resource
    private CommonConfigDao commonConfigDao;
    @Resource
    private EmployFacade employFacade;
    @Autowired
    private IBusinessRuleService businessRuleService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private IRuleMatchService ruleMatchService;

    @Override
    public Boolean saveOrUpdateCommonConfig(CommonConfigDTO commonConfigDTO) {
        return StringUtils.isNotBlank(commonConfigDTO.getCode()) ? this.updateCommonConfig(commonConfigDTO)
                : this.saveCommonConfig(commonConfigDTO);
    }

    /**
     * 新增配置
     *
     * @param commonConfigDTO
     * @return
     */
    private Boolean saveCommonConfig(CommonConfigDTO commonConfigDTO) {
        String groupCode = commonConfigDTO.getGroupCode();
        CommonConfigEntity commonConfigEntity = BeanConvertUtils.convert(CommonConfigEntity.class, commonConfigDTO);
        String configUniqueCode = commonConfigDao.generateConfigUniqueCode(groupCode);
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);

        commonConfigEntity.setName(SystemCodeConfigEnum.getNameByRuleCode(groupCode))
                .setCode(configUniqueCode)
                .setStatus(commonConfigDTO.getStatus())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        commonConfigDao.save(commonConfigEntity);

        RuleCreateDTO ruleCreateDTO = new RuleCreateDTO()
                .setReferCode(configUniqueCode)
                .setConditionVariableList(commonConfigDTO.getConditionVariableList());
        if (SystemCodeConfigEnum.SIGN_PAPER_RULE_CONFIG.getRuleCode().equals(groupCode)) {
            ruleCreateDTO.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                    .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                    .setReferType(RuleReferTypeEnum.SIGN_PAPER_RULE.getValue());
            commonConfigEntity.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                    .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule());
        }
        BusinessRuleEntity ruleEntity = businessRuleService.recordBusinessRule(ruleCreateDTO);
        commonConfigEntity.setRuleCode(ruleEntity.getRuleCode())
                .setRuleInfo(ruleEntity.getRuleInfo())
                .setRuleContent(ruleEntity.getConditionInfo())
                .setUpdatedAt(DateTimeUtil.now());
        commonConfigDao.updateById(commonConfigEntity);
        return true;
    }

    /**
     * 编辑更新配置信息
     *
     * @param commonConfigDTO
     * @return
     */
    private Boolean updateCommonConfig(CommonConfigDTO commonConfigDTO) {
        CommonConfigEntity commonConfigEntity = commonConfigDao.getByUniqueCode(commonConfigDTO.getCode());
        if (null == commonConfigEntity) {
            throw new BusinessException(ResultCodeEnum.COMMON_CONFIG_GROUP_NULL, commonConfigDTO.getCode());
        }
        String groupCode = commonConfigEntity.getGroupCode();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        commonConfigEntity
                .setStatus(commonConfigDTO.getStatus())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        commonConfigDao.updateById(commonConfigEntity);

        RuleCreateDTO ruleCreateDTO = new RuleCreateDTO()
                .setReferCode(commonConfigEntity.getCode())
                .setConditionVariableList(commonConfigDTO.getConditionVariableList());
        if (SystemCodeConfigEnum.SIGN_PAPER_RULE_CONFIG.getRuleCode().equals(groupCode)) {
            ruleCreateDTO.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                    .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                    .setReferType(RuleReferTypeEnum.SIGN_PAPER_RULE.getValue());
            commonConfigEntity.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                    .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule());
        }
        BusinessRuleEntity ruleEntity = businessRuleService.recordBusinessRule(ruleCreateDTO);
        if (null != ruleEntity) {
            commonConfigEntity.setRuleCode(ruleEntity.getRuleCode())
                    .setRuleInfo(ruleEntity.getRuleInfo())
                    .setRuleContent(ruleEntity.getConditionInfo())
                    .setUpdatedAt(DateTimeUtil.now());
            commonConfigDao.updateById(commonConfigEntity);
        }
        return true;
    }

    @Override
    public CommonConfigDTO getCommonConfigDetailById(Integer configId) {
        CommonConfigEntity commonConfigEntity = commonConfigDao.getById(configId);
        if (null == commonConfigEntity) {
            return null;
        }
        CommonConfigDTO commonConfigDTO = BeanConvertUtils.convert(CommonConfigDTO.class, commonConfigEntity);
        RuleQueryDTO ruleQueryDTO = new RuleQueryDTO()
                .setReferCode(commonConfigEntity.getCode());
        if (SystemCodeConfigEnum.SIGN_PAPER_RULE_CONFIG.getRuleCode().equals(commonConfigEntity.getGroupCode())) {
            ruleQueryDTO.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                    .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                    .setReferType(RuleReferTypeEnum.SIGN_PAPER_RULE.getValue());
        }
        BusinessRuleEntity ruleEntity = businessRuleService.getRuleDetailByBusinessCode(ruleQueryDTO);
        if (null != ruleEntity && !CollectionUtils.isEmpty(ruleEntity.getConditionVariableList())) {
            commonConfigDTO.setConditionVariableList(ruleEntity.getConditionVariableList());
        }
        return commonConfigDTO;
    }

    @Override
    public List<CommonConfigDTO> getSignPaperRuleConfig() {
        //1、获取所有有效的二级品类集合
        List<CategoryDTO> categoryDTOList = categoryFacade.queryCategoryDTOList(new CategoryQO().setLevel(2).setStatus(DisableStatusEnum.ENABLE.getValue()));
        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return new ArrayList<>();
        }
        //2、获取所有品类的正本配置信息，并根据二级品类分组
        String paperGroupCode = SystemCodeConfigEnum.SIGN_PAPER_RULE_CONFIG.getRuleCode();
        List<CommonConfigEntity> signPaperConfigList = commonConfigDao.getAllConfigList(paperGroupCode);
        Map<Integer, List<CommonConfigEntity>> signPaperMapByCategory2 = signPaperConfigList.stream().collect(Collectors.groupingBy(CommonConfigEntity::getCategory2));
        //3、组装正本配置列表信息
        List<CommonConfigDTO> commonConfigDTOList = categoryDTOList.stream().map(categoryDTO -> {
            CommonConfigDTO commonConfigDTO = new CommonConfigDTO()
                    .setCategoryName1(categoryDTO.getCategoryName1())
                    .setCategoryName2(categoryDTO.getCategoryName2())
                    .setCategoryName3(categoryDTO.getCategoryName3());
            commonConfigDTO.setCategory1(categoryDTO.getCategory1())
                    .setCategory2(categoryDTO.getCategory2())
                    .setCategory3(categoryDTO.getCategory3())
                    .setGroupCode(paperGroupCode);
            if (!CollectionUtils.isEmpty(signPaperMapByCategory2) && !CollectionUtils.isEmpty(signPaperMapByCategory2.get(categoryDTO.getSerialNo()))) {
                CommonConfigEntity commonConfigEntity = signPaperMapByCategory2.get(categoryDTO.getSerialNo()).get(0);
                commonConfigDTO = BeanConvertUtils.copy(commonConfigDTO, commonConfigEntity);
                //4、填充每个配置的规则详情信息
                RuleQueryDTO ruleQueryDTO = new RuleQueryDTO()
                        .setReferCode(commonConfigEntity.getCode());
                if (paperGroupCode.equals(commonConfigEntity.getGroupCode())) {
                    ruleQueryDTO.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()))
                            .setModuleType(RuleModuleTypeEnum.CONTRACT_SIGN.getModule())
                            .setReferType(RuleReferTypeEnum.SIGN_PAPER_RULE.getValue());
                }
                BusinessRuleEntity ruleEntity = businessRuleService.getRuleDetailByBusinessCode(ruleQueryDTO);
                if (null != ruleEntity && !CollectionUtils.isEmpty(ruleEntity.getConditionVariableList())) {
                    commonConfigDTO.setConditionVariableList(ruleEntity.getConditionVariableList());
                }
            }
            return commonConfigDTO;
        }).collect(Collectors.toList());
        return commonConfigDTOList;
    }

    @Override
    public Boolean matchConfigRuleInfo(CommonConfigRuleMatchDTO configRuleMatchDTO) {
        List<CommonConfigEntity> configEntityList = commonConfigDao.queryCommonConfig(configRuleMatchDTO);
        if (CollectionUtils.isEmpty(configEntityList)) {
            return false;
        }
        List<RuleReferInfoDTO> ruleReferInfoList = configEntityList.stream()
                .map(commonConfigEntity -> {
                    return new RuleReferInfoDTO().setReferType(SystemCodeConfigEnum.getRuleReferTypeByRuleCode(configRuleMatchDTO.getGroupCode()).getValue())
                            .setReferCode(commonConfigEntity.getCode());
                })
                .collect(Collectors.toList());
        RuleMatchDTO ruleMatchDTO = new RuleMatchDTO()
                .setSystemId(configRuleMatchDTO.getSystemId())
                .setModuleType(configRuleMatchDTO.getModuleType())
                .setRuleReferInfoList(ruleReferInfoList)
                .setMapBizData(configRuleMatchDTO.getMapBizData());
        List<RuleReferInfoDTO> ruleResultList = ruleMatchService.matchBusinessRule(ruleMatchDTO);
        log.info("命中得规则信息：{}" + FastJsonUtils.getBeanToJson(ruleResultList));
        return !CollectionUtils.isEmpty(ruleResultList);
    }
}
