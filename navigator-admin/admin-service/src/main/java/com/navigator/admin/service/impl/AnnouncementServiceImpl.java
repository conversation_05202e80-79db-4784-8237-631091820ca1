package com.navigator.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.AnnouncementDao;
import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.AnnouncementEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.enums.AnnouncementStatusEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.AnnouncementVO;
import com.navigator.admin.service.AnnouncementService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CodeGeneratorUtil;
import com.navigator.common.util.JwtUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AnnouncementServiceImpl implements AnnouncementService {

    @Autowired
    private AnnouncementDao announcementDao;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Autowired
    private IRoleService roleService;
    @Resource
    private IOperationDetailService operationDetailService;

    @Override
    public void save(AnnouncementDTO announcementDTO) {
        AnnouncementEntity announcementEntity = new AnnouncementEntity();
        announcementEntity
                .setName(announcementDTO.getName())
                .setSystemId(Integer.parseInt(announcementDTO.getSystemId()))
                .setContent(announcementDTO.getContent())
                .setExpirationDate(announcementDTO.getExpirationDate())
                .setPublishTime(announcementDTO.getPublishTime())
                .setStatus(Integer.parseInt(announcementDTO.getStatus()))
                .setCreatedAt(new Date())
                .setCreatedBy(JwtUtils.getCurrentUserId())
                .setUpdatedAt(new Date())
                .setUpdatedBy(JwtUtils.getCurrentUserId())
        ;
        if (AnnouncementStatusEnum.PUBLISH.getValue().equals(announcementEntity.getStatus())) {
            announcementEntity.setPublishTime(new Date());
        }
        announcementDao.save(announcementEntity);
        announcementEntity.setCode(CodeGeneratorUtil.genAnnouncementCode(announcementEntity.getSystemId(), announcementEntity.getId()));
        announcementDao.updateById(announcementEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(announcementEntity.getName())
                    .setDtoData(JSON.toJSONString(announcementDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_ANNOUNCEMENT)
                    .setReferBizId(announcementDTO.getId())
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void modify(AnnouncementDTO announcementDTO) {
        AnnouncementEntity announcementEntity = announcementDao.getById(announcementDTO.getId());
        if (verifyContent(announcementDTO, announcementEntity)) {
            throw new BusinessException(ResultCodeEnum.SAME_ERROR);
        }
        if ((AnnouncementStatusEnum.SKETCH.getValue().equals(announcementEntity.getStatus())
                && AnnouncementStatusEnum.CANCEL.getValue().equals(Integer.parseInt(announcementDTO.getStatus())))
                || (!AnnouncementStatusEnum.SKETCH.getValue().equals(announcementEntity.getStatus())
                && AnnouncementStatusEnum.SKETCH.getValue().equals(Integer.parseInt(announcementDTO.getStatus())))
        ) {
            throw new BusinessException(ResultCodeEnum.STATUS_ERROR);
        }

        announcementEntity
                .setName(announcementDTO.getName())
                .setSystemId(Integer.parseInt(announcementDTO.getSystemId()))
                .setContent(announcementDTO.getContent())
                .setExpirationDate(announcementDTO.getExpirationDate())
                .setStatus(Integer.parseInt(announcementDTO.getStatus()))
                .setUpdatedAt(new Date())
                .setUpdatedBy(JwtUtils.getCurrentUserId())
        ;
        if (AnnouncementStatusEnum.PUBLISH.getValue().equals(announcementEntity.getStatus())
                && announcementEntity.getPublishTime() == null
        ) {
            announcementEntity.setPublishTime(new Date());
        }
        announcementDao.updateById(announcementEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(announcementEntity.getName())
                    .setDtoData(JSON.toJSONString(announcementDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.MODIFY_ANNOUNCEMENT)
                    .setReferBizId(announcementDTO.getId())
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean verifyContent(AnnouncementDTO announcementDTO, AnnouncementEntity announcementEntity) {
        if (!announcementEntity.getName().equalsIgnoreCase(announcementDTO.getName())) {
            return false;
        }
        if (!String.valueOf(announcementEntity.getSystemId()).equals(announcementDTO.getSystemId())) {
            return false;

        }
        if (!announcementEntity.getContent().equalsIgnoreCase(announcementDTO.getContent())) {
            return false;

        }
        if (!DateUtils.isSameInstant(announcementEntity.getExpirationDate(), announcementDTO.getExpirationDate())) {
            return false;

        }
        if (!String.valueOf(announcementEntity.getStatus()).equals(announcementDTO.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public AnnouncementEntity queryAnnouncementDetail(AnnouncementDTO announcementDTO) {
        return announcementDao.getById(announcementDTO.getId());
    }

    @Override
    public Result queryAnnouncementList(QueryDTO<AnnouncementDTO> queryDTO) {
        Page<AnnouncementEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        AnnouncementDTO announcementDTO = queryDTO.getCondition();
        String currentUserId = JwtUtils.getCurrentUserId();
        List<RoleEntity> roleEntities = roleService.queryRoleByEmployId(currentUserId);
        List<Integer> canRoleList = Arrays.asList(1, 135, 136);
        List<Integer> roleIdList = roleEntities.stream().map(RoleEntity::getId).collect(Collectors.toList());
        if (roleIdList.stream().anyMatch(canRoleList::contains)) {
            announcementDTO.setSketchStatus(false);
        } else {
            announcementDTO.setSketchStatus(true);
        }

        IPage<AnnouncementEntity> iPage = announcementDao.queryAnnouncementList(page, announcementDTO);
        List<AnnouncementVO> list = iPage.getRecords().stream().map(i -> {
            AnnouncementVO announcementVO = new AnnouncementVO();
            BeanUtils.copyProperties(i, announcementVO);
            String createByName = redisCacheMap.get(Integer.parseInt(i.getCreatedBy()));
            String updateByName = redisCacheMap.get(Integer.parseInt(i.getUpdatedBy()));
            announcementVO
                    .setCreateByName(createByName)
                    .setUpdateByName(updateByName);
            return announcementVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);
    }

    @Override
    public List<AnnouncementEntity> queryAnnouncementBySystem(Integer systemId) {
        return announcementDao.queryAnnouncementList(systemId);
    }

}
