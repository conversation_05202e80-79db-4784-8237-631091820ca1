package com.navigator.admin.service.rule;

import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IRuleDictItemService {
    Result queryRuleDictItemByCondition(QueryDTO<RuleDictItemEntity> queryDTO);


    List<RuleDictItemEntity> getRuleDictItemById(List<Integer> dictItemIdList);

    List<RuleDictItemEntity> getRuleItemByDictCode(String dictCode, String moduleType, String systemId);

    RuleDictItemEntity getRuleDictItemByCode(String dictCode, String itemCode, Integer itemValue, String moduleType, String systemId);

    Result saveRuleDictItem(RuleDictItemEntity ruleDictItemEntity);

    Result updateRuleDictItem(RuleDictItemEntity ruleDictItemEntity);
}
