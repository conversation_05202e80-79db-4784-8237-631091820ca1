package com.navigator.admin.service.systemrule;

import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8
 */
public interface ProteinPriceConfigService {

    /**
     * 根据规则查询蛋白价差
     *
     * @param basicPriceConfigQueryDTO 规则条件
     * @return 匹配的询蛋白价差信息
     */
    SystemRuleItemEntity filterBasicProtein(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO);



}
