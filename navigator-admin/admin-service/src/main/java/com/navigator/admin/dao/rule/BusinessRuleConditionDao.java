package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.mapper.rule.BusinessRuleConditionMapper;
import com.navigator.admin.pojo.entity.rule.BusinessRuleConditionEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 18:27
 **/
@Dao
public class BusinessRuleConditionDao extends BaseDaoImpl<BusinessRuleConditionMapper, BusinessRuleConditionEntity> {

    public BusinessRuleConditionEntity getRuleCondition(String ruleInfo, String moduleType, String systemId) {
        List<BusinessRuleConditionEntity> ruleEntityList = this.list(new LambdaQueryWrapper<BusinessRuleConditionEntity>()
                .eq(BusinessRuleConditionEntity::getRuleInfo, ruleInfo)
                .eq(BusinessRuleConditionEntity::getModuleType, moduleType)
                .eq(BusinessRuleConditionEntity::getSystemId, systemId)
                .eq(BusinessRuleConditionEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isEmpty(ruleEntityList) ? null : ruleEntityList.get(0);
    }
}
