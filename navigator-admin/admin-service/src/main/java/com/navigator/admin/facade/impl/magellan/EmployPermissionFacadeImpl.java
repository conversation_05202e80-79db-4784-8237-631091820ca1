package com.navigator.admin.facade.impl.magellan;

import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class EmployPermissionFacadeImpl implements EmployPermissionFacade {
    @Autowired
    private IEmployRoleService employRoleService;

    @Override
    public Result getEmployRoleListByRoleIds(List<Integer> roleIdList) {
        List<EmployRoleEntity> list = employRoleService.getEmployRoleListByRoleIds(roleIdList);
        return Result.success(list);
    }

    @Override
    public void addEmployRole(EmployRoleDTO employRoleDTO) {
        employRoleService.addEmployRole(employRoleDTO);
    }

    @Override
    public void addEmployRoles(Integer employId, String roleIds, Integer roleDefId, Integer categoryId, String customerIds) {
        if (StringUtil.isNotEmpty(roleIds)) {
            employRoleService.addEmployRoles(employId, roleIds);
        }else{
            employRoleService.addEmployRoles(employId, roleDefId,categoryId,customerIds);
        }
    }

    @Override
    public void deleteEmployRole(EmployRoleDTO employRoleDTO) {
        employRoleService.deleteEmployRole(employRoleDTO);
    }
}
