package com.navigator.admin.service.columbus.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.columbus.CEmployRoleDao;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.admin.service.columbus.ICEmployRoleService;
import com.navigator.admin.service.columbus.ICRoleService;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;

@Service
public class CEmployRoleServiceImpl implements ICEmployRoleService {

    @Autowired
    private CEmployRoleDao cEmployRoleDao;
    @Autowired
    private ICRoleService cRoleService;

    @Override
    public List<CEmployRoleEntity> getEmployRolesByEmploy(String employId) {
        return cEmployRoleDao.getEmployRolesByEmploy(employId);
    }

    @Override
    public List<CEmployRoleEntity> getEmployRolesByEmployAndCustomerId(Integer employId, Integer customerId) {
        return cEmployRoleDao.getEmployRolesByEmployAndCustomerId(employId, customerId);
    }

    @Override
    public List<CEmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            roleIds = Collections.singletonList(-1);
        }
        return cEmployRoleDao.getEmployRolesByRoleIds(roleIds);
    }

    @Override
    public void addEmployRole(CEmployRoleDTO employRoleDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        if (CollectionUtils.isEmpty(employRoleDTO.getEmployIdList())) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }
        for (Integer employId : employRoleDTO.getEmployIdList()) {
            CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
            cEmployRoleEntity
                    .setEmployId(employId)
                    .setRoleDefId(employRoleDTO.getRoleDefId())
                    .setRoleId(employRoleDTO.getRoleId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            cEmployRoleDao.save(cEmployRoleEntity);
        }
    }

    @Override
    public List<CEmployRoleEntity> getEmployRolesByCustomerId(List<Integer> roleIds, Integer customerId) {
        return cEmployRoleDao.getEmployRolesByCustomerId(roleIds, customerId);
    }


    @Override
    public void addEmployRoles(Integer employId, String roleIds) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<CRoleEntity> roleEntityList = new ArrayList<>();

        List<Integer> roleIdList = StringUtil.split2Int(roleIds, SPLIT_SIGN_DH);

        roleEntityList = cRoleService.queryByIdList(roleIdList);

        for (CRoleEntity CRoleEntity : roleEntityList) {
            CEmployRoleEntity CEmployRoleEntity = new CEmployRoleEntity()
                    .setEmployId(employId)
                    .setRoleId(CRoleEntity.getId())
                    .setRoleDefId(CRoleEntity.getRoleDefId())
                    .setIsDeleted(1)
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);
            cEmployRoleDao.save(CEmployRoleEntity);
        }

    }

    @Override
    public void addEmployRoles(Integer employId, Integer roleDefId, Integer categoryId, String customerIds) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<CRoleEntity> roleEntityList = new ArrayList<>();

        List<Integer> customerIdList = StringUtil.split2Int(customerIds, SPLIT_SIGN_DH);

        for (Integer customerId : customerIdList) {
            List<CRoleEntity> list = cRoleService.queryRoleListByDefInfo(roleDefId, categoryId, customerId);
            roleEntityList.addAll(list);
        }

        for (CRoleEntity CRoleEntity : roleEntityList) {
            CEmployRoleEntity CEmployRoleEntity = new CEmployRoleEntity()
                    .setEmployId(employId)
                    .setRoleId(CRoleEntity.getId())
                    .setRoleDefId(CRoleEntity.getRoleDefId())
                    .setIsDeleted(1)
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);
            cEmployRoleDao.save(CEmployRoleEntity);
        }
    }

    @Override
    public void deleteEmployRole(CEmployRoleDTO employRoleDTO) {
        if (CollectionUtils.isEmpty(employRoleDTO.getEmployIdList())) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }
        for (Integer employId : employRoleDTO.getEmployIdList()) {
            CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
            cEmployRoleEntity
                    .setEmployId(employId)
                    .setRoleDefId(employRoleDTO.getRoleDefId())
                    .setRoleId(employRoleDTO.getRoleId())
            ;
            cEmployRoleDao.delete(cEmployRoleEntity);
        }
    }

    @Override
    public List<CEmployRoleEntity> queryByRoleDefId(Integer roleDefId) {
        List<CEmployRoleEntity> employRoleEntities = cEmployRoleDao.queryByRoleDefId(roleDefId);
        return employRoleEntities;
    }

    @Override
    public void save(CEmployRoleEntity CEmployRoleEntity) {
        cEmployRoleDao.save(CEmployRoleEntity);
    }

    @Override
    public void deleteByEmployId(Integer employId) {
        cEmployRoleDao.deleteByEmployId(employId);
    }

    @Override
    public void deleteByEmployIdAndCustomerId(Integer employId, Integer customerId) {
        cEmployRoleDao.deleteByEmployIdAndCustomerId(employId, customerId);
    }

    @Override
    public List<CEmployRoleEntity> getEmployRoleListByRoleIds(List<Integer> roleIdList) {
        return cEmployRoleDao.getEmployRolesByRoleIds(roleIdList);
    }

    @Override
    public List<Integer> queryRoleIdList(EmployRoleQO condition) {
        return cEmployRoleDao.queryRoleIdListByEmployId(condition);
    }

}
