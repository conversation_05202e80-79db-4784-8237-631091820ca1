package com.navigator.admin.service.rule.impl;

import com.navigator.admin.dao.rule.BusinessRuleConditionDao;
import com.navigator.admin.dao.rule.BusinessRuleDao;
import com.navigator.admin.dao.rule.BusinessRuleDetailDao;
import com.navigator.admin.pojo.dto.rule.RuleMatchDTO;
import com.navigator.admin.pojo.dto.rule.RuleReferInfoDTO;
import com.navigator.admin.pojo.entity.rule.BusinessRuleDetailEntity;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import com.navigator.admin.service.rule.IRuleMatchService;
import com.navigator.common.dto.DroolsRuleBizInfoDTO;
import com.navigator.common.dto.DroolsRuleDataDTO;
import com.navigator.common.util.DroolsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 19:04
 **/
@Service
@Slf4j
public class RuleMatchServiceImpl implements IRuleMatchService {
    @Autowired
    private BusinessRuleDao businessRuleDao;
    @Autowired
    private BusinessRuleDetailDao businessRuleDetailDao;
    @Autowired
    private BusinessRuleConditionDao businessRuleConditionDao;

    @Override
    public List<RuleReferInfoDTO> matchBusinessRule(RuleMatchDTO ruleMatchDTO) {
        //命中的规则
        List<DroolsRuleDataDTO> matchRuleList = matchMainRule(ruleMatchDTO);
        if (CollectionUtils.isEmpty(matchRuleList)) {
            return new ArrayList<>();
        }
        List<RuleReferInfoDTO> matchResultRuleList = new ArrayList<>();
        for (DroolsRuleDataDTO matchRuleDTO : matchRuleList) {
            matchRuleDetail(ruleMatchDTO, matchResultRuleList, matchRuleDTO);
        }
        return matchResultRuleList;
    }

    /**
     * 执行业务对应的规则脚本
     *
     * @param ruleMatchDTO
     * @return 规则命中结果集
     */
    private List<DroolsRuleDataDTO> matchMainRule(RuleMatchDTO ruleMatchDTO) {
        DroolsRuleBizInfoDTO ruleBizInfoDTO = new DroolsRuleBizInfoDTO()
                .setMapBizData(ruleMatchDTO.getMapBizData());
        List<DroolsRuleDataDTO> ruleDataDTOList = new ArrayList<>();
        for (RuleReferInfoDTO ruleReferDTO : ruleMatchDTO.getRuleReferInfoList()) {
            BusinessRuleEntity businessRuleEntity = businessRuleDao.getRuleByBusinessCode(ruleReferDTO.getReferCode(), ruleReferDTO.getReferType(), ruleMatchDTO.getModuleType(), ruleMatchDTO.getSystemId());
            if (null != businessRuleEntity) {
                DroolsRuleDataDTO ruleDataDTO = new DroolsRuleDataDTO()
                        .setReferType(businessRuleEntity.getReferType())
                        .setRuleCode(ruleReferDTO.getReferCode())
                        .setRuleInfo(businessRuleEntity.getRuleInfo());
                ruleDataDTOList.add(ruleDataDTO);
            }
        }
        if (CollectionUtils.isEmpty(ruleDataDTOList)) {
            return new ArrayList<>();
        }
        //执行规则脚本
        DroolsUtil.runRuleInfos(ruleDataDTOList, ruleBizInfoDTO);
        return ruleBizInfoDTO.getMatchRuleList();
    }


    /**
     * 执行命中规则的明细规则
     *
     * @param ruleMatchDTO
     * @param matchResultRuleList
     * @param matchRuleDTO
     */
    private void matchRuleDetail(RuleMatchDTO ruleMatchDTO, List<RuleReferInfoDTO> matchResultRuleList, DroolsRuleDataDTO matchRuleDTO) {
        RuleReferInfoDTO matchRuleReferDTO = new RuleReferInfoDTO().setReferCode(matchRuleDTO.getRuleCode())
                .setReferType(matchRuleDTO.getReferType());
        if (ruleMatchDTO.getNeedMatchDetailRule()) {
            //获取具体命中的规则
            List<BusinessRuleDetailEntity> ruleDetailEntityList = businessRuleDetailDao.getRuleDetailByReferCode(matchRuleDTO.getRuleCode(), matchRuleDTO.getReferType(), ruleMatchDTO.getModuleType(), ruleMatchDTO.getSystemId());
            List<DroolsRuleDataDTO> ruleDataDetailList = ruleDetailEntityList.stream().map(ruleDetailEntity -> {
                        return new DroolsRuleDataDTO().setReferType(ruleDetailEntity.getReferType())
                                .setRuleCode("RuleDetail_" + ruleDetailEntity.getId().toString())
                                .setRuleInfo(ruleDetailEntity.getRuleInfo());
                    }
            ).collect(Collectors.toList());
            DroolsRuleBizInfoDTO ruleBizDetailInfoDTO = new DroolsRuleBizInfoDTO()
                    .setMapBizData(ruleMatchDTO.getMapBizData());
            //执行规则脚本
            DroolsUtil.runRuleInfos(ruleDataDetailList, ruleBizDetailInfoDTO);
            List<Integer> matchDetailIdList = ruleBizDetailInfoDTO.getMatchRules().stream()
                    .map(it -> {
                        return Integer.valueOf(it.substring(11));
                    }).collect(Collectors.toList());
            List<BusinessRuleDetailEntity> matchDetailEntityList = ruleDetailEntityList.stream().filter(it -> matchDetailIdList.contains(it.getId())).collect(Collectors.toList());
            matchRuleReferDTO.setRuleDetailList(matchDetailEntityList);
        }
        matchResultRuleList.add(matchRuleReferDTO);
    }

}
