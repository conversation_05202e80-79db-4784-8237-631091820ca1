package com.navigator.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * dba_warehouse Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface WarehouseMapper extends BaseMapper<WarehouseEntity> {

    @Insert("<script>" +
            "SET IDENTITY_INSERT  [dbo].[dba_warehouse]  ON;" +
            "INSERT INTO dba_warehouse (id, is_dce, code, geographic_area_id,geographic_city_id, type, is_unset, is_deleted, atlas_terminal_code, delivery_point, address, site_codes, name, warehouse_type, status ) VALUES" +
            " (#{warehouseEntity.id}, #{warehouseEntity.isDce}, #{warehouseEntity.code}, #{warehouseEntity.geographicAreaId}, #{warehouseEntity.geographicCityId}, #{warehouseEntity.type}, #{warehouseEntity.isUnset}, #{warehouseEntity.isDeleted}, #{warehouseEntity.atlasTerminalCode}, #{warehouseEntity.deliveryPoint}, #{warehouseEntity.address}, #{warehouseEntity.siteCodes}, #{warehouseEntity.name}, #{warehouseEntity.warehouseType}, #{warehouseEntity.status})" +
            "SET IDENTITY_INSERT  [dbo].[dba_warehouse]  OFF;" +
            "</script>")
    int saveWareHouseWithId(@Param("warehouseEntity") WarehouseEntity warehouseEntity);
}
