package com.navigator.admin.facade.impl.rule;

import com.navigator.admin.facade.rule.BusinessRuleFacade;
import com.navigator.admin.pojo.dto.rule.ConditionVariableDTO;
import com.navigator.admin.pojo.dto.rule.RuleCreateDTO;
import com.navigator.admin.pojo.dto.rule.RuleQueryDTO;
import com.navigator.admin.pojo.dto.rule.RuleScriptDTO;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import com.navigator.admin.service.rule.IBusinessRuleService;
import com.navigator.admin.service.rule.impl.RuleContentBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 13:45
 **/
@RestController
public class BusinessRuleFacadeImpl implements BusinessRuleFacade {
    @Autowired
    private IBusinessRuleService businessRuleService;
    @Autowired
    private RuleContentBuilder ruleContentBuilder;

    @Override
    public BusinessRuleEntity recordBusinessRule(RuleCreateDTO ruleCreateDTO) {
        return businessRuleService.recordBusinessRule(ruleCreateDTO);
    }

    @Override
    public RuleScriptDTO jointRuleConditionInfo(List<ConditionVariableDTO> conditionVariableList) {
        return ruleContentBuilder.jointConditionInfo(conditionVariableList);
    }

    @Override
    public BusinessRuleEntity getRuleDetailByBusinessCode(RuleQueryDTO ruleQueryDTO) {
        return businessRuleService.getRuleDetailByBusinessCode(ruleQueryDTO);
    }
}
