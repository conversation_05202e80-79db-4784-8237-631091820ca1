package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.RoleDefMapper;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class RoleDefDao extends BaseDaoImpl<RoleDefMapper, RoleDefEntity> {

    public RoleDefEntity getRoleDefById(Integer roleDefId) {
        return getOne(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(RoleDefEntity::getId, roleDefId)
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<RoleDefEntity> queryPageByRoleQueryDTO(Page<RoleDefEntity> page, RoleQueryDTO roleQueryDTO) {
        IPage<RoleDefEntity> iPage = page(page, Wrappers.<RoleDefEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(roleQueryDTO.getRoleName()), RoleDefEntity::getName, "%" + roleQueryDTO.getRoleName() + "%")
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(StringUtils.isNotBlank(roleQueryDTO.getUpdateStartTime()), RoleDefEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(roleQueryDTO.getUpdateStartTime()))
                .le(StringUtils.isNotBlank(roleQueryDTO.getUpdateEndTime()), RoleDefEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp2359(roleQueryDTO.getUpdateEndTime()))
                .eq(RoleDefEntity::getLevel, 2)
                .orderByAsc(RoleDefEntity::getId)
        );

        return iPage;
    }

    public List<RoleDefEntity> getRoleDefByType(String type) {
        return list(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(type), RoleDefEntity::getType, type)
                .eq(RoleDefEntity::getStatus, 1)
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

    }

    public RoleDefEntity getRoleDefByName(String name) {
        List<RoleDefEntity> list = list(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(name), RoleDefEntity::getName, name)
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<RoleDefEntity> queryRoleDefExist(RoleDTO roleDTO) {
        return list(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(roleDTO.getName()), RoleDefEntity::getName, roleDTO.getName())
                .or(StringUtils.isNotBlank(roleDTO.getCode()))
                .eq(StringUtils.isNotBlank(roleDTO.getCode()), RoleDefEntity::getCode, roleDTO.getCode())
                .ne(roleDTO.getRoleDefId() != null, RoleDefEntity::getId, roleDTO.getRoleDefId())
                .eq(RoleDefEntity::getStatus, 1)
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

    }

    public RoleDefEntity queryPreData() {
        List<RoleDefEntity> list = list(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(RoleDefEntity::getName, "TBD")
                .gt(RoleDefEntity::getId, 100)
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }

    public List<RoleDefEntity> queryFatherRoleDefList() {
        return list(Wrappers.<RoleDefEntity>lambdaQuery()
                .eq(RoleDefEntity::getLevel, 1)
                .eq(RoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(RoleDefEntity::getId)
        );
    }

    public boolean updateDeletedById(Integer roleDefId) {
        return update(Wrappers.<RoleDefEntity>lambdaUpdate()
                .eq(RoleDefEntity::getId, roleDefId)
                .set(RoleDefEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));
    }
}
