package com.navigator.admin.facade.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class TopicReceiveController {

    private static final String TOPIC_NAME = "topic1";
    private static final String TOPIC_NAME1 = "topic";

    private static final String SUBSCRIPTION_NAME = "subscribe1";
    private static final String SUBSCRIPTION_NAME2 = "subscribe2";

    private final Logger logger = LoggerFactory.getLogger(TopicReceiveController.class);

//    @JmsListener(destination = TOPIC_NAME,containerFactory = "topicJmsListenerContainerFactory")
//    public void receiveMessage(String message) {
//        logger.info("Received message: {}", message);
//    }
//
//    @JmsListener(destination = TOPIC_NAME,subscription=SUBSCRIPTION_NAME, containerFactory = "topicJmsListenerContainerFactory")
//    public void receiveMessage1(String message) {
//        logger.info("Received message1: {}", message);
//    }
//
//    @JmsListener(destination = TOPIC_NAME,subscription=SUBSCRIPTION_NAME2, containerFactory = "topicJmsListenerContainerFactory")
//    public void receiveMessage2(String message) {
//        logger.info("Received message2: {}", message);
//    }
//
//    @JmsListener(destination = TOPIC_NAME1,subscription=SUBSCRIPTION_NAME2)
//    public void receiveMessage3(String message) {
//        logger.info("Received message3: {}", message);
//    }
}