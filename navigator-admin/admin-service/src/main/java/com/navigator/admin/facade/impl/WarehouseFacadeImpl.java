package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseAreaEntity;
import com.navigator.admin.pojo.entity.WarehouseCityEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.WarehouseConfigTypeEnum;
import com.navigator.admin.pojo.qo.WarehouseSiteQO;
import com.navigator.admin.service.IWarehouseService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class WarehouseFacadeImpl implements WarehouseFacade {
    @Resource
    private IWarehouseService warehouseService;

    @Override
    public Result<Boolean> addWarehouse(WarehouseEntity warehouseEntity) {
        return Result.success(warehouseService.addWarehouse(warehouseEntity));
    }

    @Override
    public Result<Boolean> updateWarehouse(WarehouseEntity warehouseEntity) {
        return Result.success(warehouseService.updateWarehouse(warehouseEntity));
    }

    @Override
    public Result getWarehouseList(QueryDTO<WarehouseBO> queryDTO) {
        return Result.page(warehouseService.getWarehouseList(queryDTO));
    }

    @Override
    public Result<List<WarehouseEntity>> getAllWarehouse() {
        return Result.success(warehouseService.getAllWarehouse());
    }

    @Override
    public Result<List<WarehouseEntity>> getWarehouseBySiteCode(String siteCode) {
        return Result.success(warehouseService.getWarehouseBySiteCode(siteCode, WarehouseConfigTypeEnum.FACTORY.getValue()));
    }

    @Override
    public Result<List<WarehouseEntity>> getWarehouseByCompanyId(Integer companyId, Integer category2) {
        return Result.success(warehouseService.getWarehouseByCompanyId(companyId, category2));
    }

    @Override
    public Result<WarehouseEntity> getWarehouseByUniqueCode(String uniqueCode, Integer type) {
        return Result.success(warehouseService.getWarehouseByUniqueCode(uniqueCode, type));
    }

    @Override
    public Result<WarehouseEntity> getDceWarehouseByName(String name) {
        return Result.success(warehouseService.getWarehouseByName(name, 1));
    }

    @Override
    public Result<WarehouseEntity> getWarehouseByName(String name) {
        return Result.success(warehouseService.getWarehouseByName(name, null));
    }

    @Override
    public Result<WarehouseEntity> getWarehouseById(Integer id) {
        return Result.success(warehouseService.getWarehouseById(id));
    }

    @Override
    public Result<String> getWarehouseMarketZone(Integer warehouseId) {
        return Result.success(warehouseService.getWarehouseMarketZone(warehouseId));
    }

    @Override
    public Result<List<WarehouseAreaEntity>> getGeographicAreaList() {
        return Result.success(warehouseService.getGeographicAreaList());
    }

    @Override
    public Result<List<WarehouseCityEntity>> getGeographicCityByAreaId(Integer areaId) {
        return Result.success(warehouseService.getGeographicCityByAreaId(areaId));
    }

    @Override
    public Result<List<WarehouseEntity>> getWarehouseByFactoryCode(String factoryCode, Integer category2) {
        return Result.success(warehouseService.getWarehouseByFactoryCode(factoryCode, category2));
    }

    @Override
    public List<WarehouseEntity> queryWarehouseList(WarehouseBO condition) {
        return warehouseService.queryWarehouseList(condition);
    }

    @Override
    public Result<List<WarehouseEntity>> getWarehouseByCompanyIdAndFactoryCode(WarehouseSiteQO warehouseSiteQO) {
        return Result.success(warehouseService.getWarehouseByCompanyIdAndFactoryCode(warehouseSiteQO));
    }

    @Override
    public Result<String> importWarehouseArea(MultipartFile file) {
        return Result.success(warehouseService.importWarehouseArea(file));
    }

    @Override
    public Result<String> importWarehouseCity(MultipartFile file) {
        return Result.success(warehouseService.importWarehouseCity(file));
    }

    @Override
    public Result<String> importWarehouse(MultipartFile file) {
        return Result.success(warehouseService.importWarehouse(file));
    }
}
