package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

/**
 * <p>
 * 标签表（商品员工绑定关系） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-queryTagEntity
 */
public interface ITagService{

    Result queryTagEntity(QueryDTO<TagQueryDTO> queryDTO);

    TagEntity queryTageEntity(Integer id);

    Result saveTagEntity(TagEntity tagEntity);

    Result updateTagEntity(TagEntity tagEntity);




}
