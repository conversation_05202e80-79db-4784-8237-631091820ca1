package com.navigator.admin.service.columbus.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.dao.columbus.CRoleDefDao;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.admin.pojo.vo.columbus.CRoleListVO;
import com.navigator.admin.pojo.vo.columbus.CRoleVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.columbus.*;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Service
public class CRoleDefServiceImpl implements ICRoleDefService {
    @Autowired
    private CRoleDefDao cRoleDefDao;
    @Autowired
    private ICRoleService cRoleService;
    @Autowired
    protected IOperationDetailService operationDetailService;
    @Autowired
    protected ICRolePowerService cRolePowerService;
    @Autowired
    protected ICRoleMenuService icRoleMenuService;
    @Autowired
    protected ICEmployService cEmployService;
    @Autowired
    protected CommonProperties commonProperties;
    @Resource
    private DeliveryApplyFacade deliveryApplyFacade;
    @Autowired
    private CustomerFacade customerFacade;

    @Override
    public CRoleDefEntity getRoleDefById(Integer roleDefId) {
        return cRoleDefDao.getRoleDefById(roleDefId);
    }

    @Override
    public CRoleDefEntity saveOrUpdate(CRoleDTO roleDTO) {
        List<CRoleDefEntity> roleDefEntityList = cRoleDefDao.queryRoleDefExist(roleDTO);
        if (CollectionUtils.isNotEmpty(roleDefEntityList) && roleDTO.getImportStatus() != 1) {
            throw new BusinessException(ResultCodeEnum.NAME_CODE_ERROR);
        }
        String userId = JwtUtils.getCurrentUserId();
        CRoleDefEntity cRoleDefEntity = new CRoleDefEntity();
        cRoleDefEntity
                .setName(roleDTO.getName())
                .setIsBaseCategory(roleDTO.getIsBaseCategory())
                .setIsSalesType(roleDTO.getIsSalesType())
                .setRelatedCategoryId(JSON.toJSONString(roleDTO.getCategoryIdList()))
                .setRelatedSalesType(JSON.toJSONString(roleDTO.getSalesTypeList()))
                .setStatus(1)
                .setLevel(2)
        ;

        if (null != roleDTO.getRoleDefId()) {
            cRoleDefEntity.setId(roleDTO.getRoleDefId())
                    .setUpdatedBy(userId);
        } else {
            cRoleDefEntity
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);
        }

        if (roleDTO.getIsSalesType() == 0 && roleDTO.getIsBaseCategory() == 0) {
            cRoleDefEntity.setType(0);
        } else {
            cRoleDefEntity.setType(1);
        }

        cRoleDefDao.saveOrUpdate(cRoleDefEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(cRoleDefEntity.getName())
                    .setDtoData(JSON.toJSONString(roleDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(cRoleDefEntity))
                    .setOperationActionEnum(OperationActionEnum.SAVE_OR_UPDATE_ROLE)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }


        roleDTO.setRoleDefId(cRoleDefEntity.getId());
        return cRoleDefEntity;
    }

    @Override
    public Result queryRoleList(QueryDTO<CRoleQueryDTO> queryDTO) {
        Page<CRoleEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        CRoleQueryDTO roleQueryDTO = queryDTO.getCondition();
        //分页查询
        return cRoleService.queryPageByQueryDTO(page, roleQueryDTO);
    }

    @Override
    public RoleQueryVO queryFactoryListByRoleDefIdList(List<Integer> roleDefIdList) {
        RoleQueryVO roleQueryVO = new RoleQueryVO();
        List<CRoleEntity> roleEntityList = cRoleService.queryByRoleDefIdList(roleDefIdList);
        List<Integer> categoryIdList = roleEntityList.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        roleQueryVO.setCategoryIdList(categoryIdList);
        return roleQueryVO;
    }

    @Override
    public List<CRoleDefEntity> queryAllList() {
        return cRoleDefDao.list();
    }

    @Override
    public RoleQueryVO queryRoleDefDetail(Integer roleDefId) {
        CRoleDefEntity CRoleDefEntity = cRoleDefDao.getRoleDefById(roleDefId);
        if (CRoleDefEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        RoleQueryVO roleQueryVO = new RoleQueryVO();
        List<Integer> list = new ArrayList<>();
        list.add(roleDefId);
        List<CRoleEntity> roleEntityList = cRoleService.queryByRoleDefIdList(list);
        List<Integer> categoryIdList = roleEntityList.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        roleQueryVO
                .setCategoryIdList(categoryIdList)
                .setRoleDefId(CRoleDefEntity.getId())
                .setName(CRoleDefEntity.getName())
                .setCode(CRoleDefEntity.getCode())
                .setRoleType(CRoleDefEntity.getType())
                .setStatus(CRoleDefEntity.getStatus());
        return roleQueryVO;
    }

    @Override
    public List<RoleQueryVO> queryRoleByCondition(CEmployRoleDTO employRoleDTO) {
        List<CRoleDefEntity> roleDefEntityList = cRoleDefDao.getRoleDefByType(employRoleDTO.getType());
        List<Integer> roleDefIdList = roleDefEntityList.stream().map(CRoleDefEntity::getId).collect(Collectors.toList());
        List<CRoleEntity> roleEntityList = cRoleService.queryRole(employRoleDTO);
        return roleEntityList.stream()
                .filter(i -> roleDefIdList.contains(i.getRoleDefId()))
                .filter(i -> !"TBD".equalsIgnoreCase(i.getName()))
                .distinct()
                .map(i -> {
                    RoleQueryVO roleQueryVO = new RoleQueryVO();
                    roleQueryVO.setName(i.getName())
                            .setRoleId(i.getId())
                            .setRoleDefId(i.getRoleDefId());
                    return roleQueryVO;
                }).collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(
                                        () -> new TreeSet<>(
                                                Comparator.comparing(i -> i.getRoleDefId())
                                        )
                                ), ArrayList::new
                        )
                );
    }

    @Override
    public List<CRoleDefEntity> getRoleDefByType(String type) {
        return cRoleDefDao.getRoleDefByType(type);
    }

    @Override
    public CRoleDefEntity getRoleDefByName(String name) {
        return cRoleDefDao.getRoleDefByName(name);
    }

    @Override
    public void updateDefRoleStatus(CRoleDTO cRoleDTO) {
        cRoleDefDao.updateDefRoleStatus(cRoleDTO);
    }

    @Override
    public IPage<CRoleDefEntity> queryPageByRoleQueryDTO(Page<CRoleDefEntity> page, CRoleQueryDTO roleQueryDTO) {
        return cRoleDefDao.queryPageByRoleQueryDTO(page, roleQueryDTO);
    }

    @Override
    public List<CRoleDefEntity> queryRoleDefForbidden() {
        return cRoleDefDao.queryRoleDefForbidden();
    }

    //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-04-28 start
    @Override
    public List<CRoleListVO> queryRole(Integer customerId) {
        List<CRoleDefEntity> roleDefList = cRoleDefDao.getRoleDefByType(null);
        List<CRoleListVO> cRoleListVOList = roleDefList.stream().map(i -> {
            List<CRoleEntity> cRoleEntities = cRoleService.queryByRoleDefIdList(Collections.singletonList(i.getId()));
            List<Integer> categoryIdList = cRoleEntities.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
            List<String> categoryNameList = new ArrayList<>();
            categoryIdList.forEach(id -> categoryNameList.add(IdNameConverter.getName(IdNameType.category_id_name, id.toString())));

            List<Integer> saleTypeList = cRoleEntities.stream().map(CRoleEntity::getSalesType).distinct().collect(Collectors.toList());
            List<CRoleVO> cRoleVOList = cRoleEntities.stream().map(k -> {
                CRoleVO cRoleVO = new CRoleVO();
                cRoleVO.setRoleId(k.getId())
                        .setSalesType(k.getSalesType())
                        .setCategoryId(k.getCategoryId());
                String name = "";
                if (k.getCategoryId() != 0) {
                    cRoleVO.setCategoryName(IdNameConverter.getName(IdNameType.category_id_name, k.getCategoryId().toString()));
                    name = cRoleVO.getCategoryName() + "-";
                }
                if (k.getSalesType() != 0) {
                    name = name + ContractSalesTypeEnum.getDescByValue(k.getSalesType()) + "-";
                }
                name = name + k.getName();
                cRoleVO.setRoleName(name);
                return cRoleVO;
            }).collect(Collectors.toList());
            CRoleListVO cRoleListVO = new CRoleListVO();
            cRoleListVO
                    .setRoleDefId(i.getId())
                    .setRoleDefName(i.getName())
                    .setRoleList(cRoleVOList)
                    .setSalesTypeList(saleTypeList)
                    .setCategoryIdList(categoryIdList)
                    .setCategoryNameList(categoryNameList)
            ;
            return cRoleListVO;
        }).filter(i -> i.getRoleDefId() != 1).collect(Collectors.toList());
        //todo 临时开放权限
        //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-04-28 start
        /*String currentUserId = JwtUtils.getCurrentUserId();
        CEmployEntity cEmployEntity = cEmployService.getEmployById(Integer.parseInt(currentUserId));
        String customerName = cEmployEntity.getCustomerCode();*/
        // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 Start
        // if (commonProperties.getCustomerStatus() && !commonProperties.getCustomerNameList().contains(customerName)) {
        // 优化：物流文员角色不依赖nacos配置 Author: Mr 2025-03-31
        /*CustomerEntity customerEntity = customerFacade.queryCustomerById(customerId);
        Result<TempPermissionDTO> result = deliveryApplyFacade.getCustomerTempPermission();
        if (result.isSuccess()) {
            TempPermissionDTO tempPermissionDTO = result.getData();
            if (tempPermissionDTO.getCustomerStatus() && !tempPermissionDTO.getCustomerNameList().contains(customerEntity.getLinkageCustomerCode())) {
                cRoleListVOList = cRoleListVOList.stream().filter(i -> !"物流文员".equalsIgnoreCase(i.getRoleDefName())).collect(Collectors.toList());
            }
        }*/
        //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-05-20 End
        // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 End

        return cRoleListVOList;
    }

    @Override
    public void copyPermission(RoleDTO roleDTO) {
        CRoleEntity cRoleEntity = cRoleService.getRoleById(roleDTO.getCopyId());
        if (cRoleEntity == null) {
            throw new BusinessException(ResultCodeEnum.NOT_SAME_COLUMBUS_ROLE_DEF);
        }
        //List<CRoleEntity> roleEntities = cRoleService.queryByRoleDefIdList(Arrays.asList(cRoleEntity.getRoleDefId()));
        //List<Integer> roleIdList = roleEntities.stream().map(CRoleEntity::getId).collect(Collectors.toList());

        cRolePowerService.copyRolePower(roleDTO.getCopyId(), roleDTO.getRoleId());
        icRoleMenuService.copyRoleMenu(roleDTO.getCopyId(), roleDTO.getRoleId());

    }

}
