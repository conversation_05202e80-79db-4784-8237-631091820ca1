package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <p>
 * 模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface ITemplateService {
    /**
     * 根据模板code和类型查询模板
     *
     * @param code
     * @param type
     * @return
     */
    TemplateEntity getTemplateByCodeAndType(String code, Integer type);


    String jointOperationLogTemplate(Map map, String code);

    /**
     * 根据业务场景条件，拼接协议模版信息
     *
     * @param queryTemplateAttributeDTO 业务场景条件信息
     * @return 协议模版-拼接结果
     */
    String getTemplateInfo(QueryTemplateAttributeDTO queryTemplateAttributeDTO);

    TemplateEntity saveOrUpdateTemplate(TemplateEntity templateEntity);

    Result importETemplateInfo(MultipartFile uploadFile);

    Result importMTermAndE(MultipartFile uploadFile);

    Result exportTemplate(Integer status, HttpServletResponse response);

    Result exportTemplateInfo(HttpServletResponse response);

    Result queryTemplate(QueryDTO<QueryTemplateDTO> queryDTO);

    TemplateEntity getLoginSignature(Integer value);

    Result syncTemplateCodeInfo(Integer type);
}
