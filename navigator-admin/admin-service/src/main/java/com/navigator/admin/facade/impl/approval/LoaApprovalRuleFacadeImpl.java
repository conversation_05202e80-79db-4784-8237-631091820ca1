package com.navigator.admin.facade.impl.approval;

import com.navigator.admin.facade.approval.LoaApprovalRuleFacade;
import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;
import com.navigator.admin.service.approval.LoaApprovalRuleService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */


@RestController
public class LoaApprovalRuleFacadeImpl implements LoaApprovalRuleFacade {

    @Resource
    private LoaApprovalRuleService loaApprovalRuleService;

    @Override
    public LoaApprovalRuleEntity queryLoaApprovalRule(LoaApprovalRuleDTO loaApprovalRuleDTO) {
        return loaApprovalRuleService.queryLoaApprovalRule(loaApprovalRuleDTO);
    }
}
