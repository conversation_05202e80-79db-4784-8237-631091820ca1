package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.OperationDetailMapper;
import com.navigator.admin.pojo.dto.LogDTO;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

@Dao
public class OperationDetailDao extends BaseDaoImpl<OperationDetailMapper, OperationDetailEntity> {


    public IPage<OperationDetailEntity> queryLogList(Page<OperationDetailEntity> page, LogDTO logDTO) {

        IPage<OperationDetailEntity> iPage = page(page, Wrappers.<OperationDetailEntity>lambdaQuery()
                .eq(OperationDetailEntity::getBizModule, ModuleTypeEnum.MLOG.getModule())
                .eq(StringUtils.isNotBlank(logDTO.getBizCode()), OperationDetailEntity::getBizCode, logDTO.getBizCode())
                .like(StringUtils.isNotBlank(logDTO.getOperationName()), OperationDetailEntity::getOperationName, logDTO.getOperationName())
                .like(StringUtils.isNotBlank(logDTO.getOperatorName()), OperationDetailEntity::getOperatorName, "%" + logDTO.getOperatorName() + "%")
                .gt(StringUtils.isNotBlank(logDTO.getOperatorStartTime()), OperationDetailEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(logDTO.getOperatorStartTime()))
                .lt(StringUtils.isNotBlank(logDTO.getOperatorEndTime()), OperationDetailEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(logDTO.getOperatorEndTime()))
                .isNotNull(OperationDetailEntity::getScenes)
                .orderByDesc(OperationDetailEntity::getCreatedAt)
        );

        return iPage;
    }
}
