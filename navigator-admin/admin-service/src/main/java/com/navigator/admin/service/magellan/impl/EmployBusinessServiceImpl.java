package com.navigator.admin.service.magellan.impl;

import com.navigator.admin.dao.magellan.EmployBusinessDao;
import com.navigator.admin.pojo.entity.EmployBusinessEntity;
import com.navigator.admin.service.magellan.IEmployBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Service
public class EmployBusinessServiceImpl implements IEmployBusinessService {
    @Autowired
    private EmployBusinessDao employBusinessDao;

    @Override
    public List<EmployBusinessEntity> queryListByEmployId(String employId) {
        return employBusinessDao.queryListByEmployId(employId);
    }

    @Override
    public void save(EmployBusinessEntity employBusinessEntity) {
        employBusinessDao.save(employBusinessEntity);
    }

    @Override
    public void deleteByEmployId(Integer employId) {
        employBusinessDao.deleteByEmployId(employId);
    }
}
