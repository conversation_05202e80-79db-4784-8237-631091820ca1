package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.FileBusinessMapper;
import com.navigator.admin.pojo.entity.FileBusinessEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 10:41
 */
@Dao
public class FileBusinessDao extends BaseDaoImpl<FileBusinessMapper, FileBusinessEntity> {


    /**
     * 废弃文件关系信息，改状态
     *
     * @param bizId                来源ID
     *                             {@link com.navigator.common.enums.FileCategoryType}
     * @param fileCategoryTypeList 业务文件类型
     * @param statusEnum           状态（驳回只更新文件状态为无效，isdeleted=0，未删除）
     */
    public List<FileBusinessEntity> getFileInfoByBizIdAndType(Integer bizId,
                                                              List<Integer> fileCategoryTypeList,
                                                              Integer statusEnum,
                                                              Integer system) {
        return this.list(Wrappers.<FileBusinessEntity>lambdaQuery()
                .eq(FileBusinessEntity::getBizId, bizId)
                .in(!CollectionUtils.isEmpty(fileCategoryTypeList), FileBusinessEntity::getBizCategory, fileCategoryTypeList)
                .eq(null != system && SystemEnum.COLUMBUS.getValue() == system, FileBusinessEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .in(null != system && SystemEnum.COLUMBUS.getValue() == system, FileBusinessEntity::getBizCategory, Arrays.asList(FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode(),FileCategoryType.CONTRACT_PDF_ORIGINAL.getCode()))
                .eq(FileBusinessEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != statusEnum, FileBusinessEntity::getStatus, statusEnum)
        );
    }

    /**
     * 废弃文件关系信息，改状态为无效
     *
     * @param bizId        来源ID
     *                     {@link com.navigator.common.enums.FileCategoryType}
     * @param categoryType 业务文件类型
     */
    public void dropFileRelation(Integer bizId, Integer categoryType, String memo) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(FileBusinessEntity::getBizCategory, categoryType)
                .eq(FileBusinessEntity::getBizId, bizId)
                .eq(FileBusinessEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(FileBusinessEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .set(FileBusinessEntity::getStatus, DisableStatusEnum.DISABLE.getValue())
                .set(!StringUtils.isBlank(memo), FileBusinessEntity::getMemo, memo)
                .update();
    }

    /**
     * 删除文件关系信息
     *
     * @param bizId        来源ID
     *                     {@link com.navigator.common.enums.FileCategoryType}
     * @param categoryType 业务文件类型
     */
    public void deleteFileRelation(Integer bizId, Integer categoryType) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(FileBusinessEntity::getBizId, bizId)
                .eq(FileBusinessEntity::getBizCategory, categoryType)
                .set(FileBusinessEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }
}
