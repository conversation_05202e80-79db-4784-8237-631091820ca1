package com.navigator.admin.service.impl;

import com.navigator.admin.dao.FileBusinessDao;
import com.navigator.admin.pojo.entity.FileBusinessEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.service.IFileBusinessService;
import com.navigator.admin.service.IFileInfoService;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.FileServiceTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 文件业务关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class FileBusinessServiceImpl implements IFileBusinessService {

    @Resource
    private IFileInfoService fileInfoService;

    @Resource
    private FileBusinessDao fileBusinessDao;

    @Value("${file.pdf.port}")
    private String filePdfPort;

    @Resource
    private AzureBlobUtil azureBlobUtil;


    @Override
    public Result recordFileRelation(FileBusinessRelationDTO relationDTO) {
//        this.deleteFileRelation(relationDTO.getBizId(), relationDTO.getCategoryType());
        if (CollectionUtils.isEmpty(relationDTO.getFileIdList())) {
            return Result.failure(ResultCodeEnum.FILE_EMPTY);
        }
        List<FileBusinessEntity> fileBusinessEntityList = relationDTO.getFileIdList().stream().map(fileId -> {
                    return new FileBusinessEntity()
                            .setBizModule(relationDTO.getModuleType())
                            .setBizCategory(relationDTO.getCategoryType())
                            .setBizId(relationDTO.getBizId())
                            .setStatus(DisableStatusEnum.ENABLE.getValue())
                            .setFileId(fileId);
                }
        ).collect(Collectors.toList());
        fileBusinessEntityList.forEach(fileBusinessEntity -> fileBusinessDao.save(fileBusinessEntity));
        return Result.success("存储文件关系成功");
    }

    /**
     * 删除文件关系信息
     *
     * @param bizId        来源ID
     *                     {@link com.navigator.common.enums.FileCategoryType}
     * @param categoryType 业务文件类型
     */
    @Override
    public void deleteFileRelation(Integer bizId, Integer categoryType) {
        fileBusinessDao.deleteFileRelation(bizId, categoryType);
    }

    /**
     * 废弃文件关系信息，改状态为无效
     *
     * @param bizId        来源ID
     *                     {@link com.navigator.common.enums.FileCategoryType}
     * @param categoryType 业务文件类型
     */
    @Override
    public void dropFileRelation(Integer bizId, Integer categoryType, String memo) {
        fileBusinessDao.dropFileRelation(bizId, categoryType, memo);
    }


    /**
     * 废弃文件关系信息，改状态
     *
     * @param bizId                来源ID
     *                             {@link com.navigator.common.enums.FileCategoryType}
     * @param fileCategoryTypeList 业务文件类型
     * @param statusEnum           状态（驳回只更新文件状态为无效，isdeleted=0，未删除）
     */
    @Override
    public List<FileInfoEntity> getFileInfoByBizIdAndType(Integer bizId,
                                                          List<Integer> fileCategoryTypeList,
                                                          Integer statusEnum, Integer system) {
        List<FileBusinessEntity> fileBusinessEntityList = fileBusinessDao.getFileInfoByBizIdAndType(bizId, fileCategoryTypeList, statusEnum, system);
        return this.covert2FileBaseInfo(fileBusinessEntityList);
    }

    /**
     * 根据来源ID、附件类型，查询附件信息
     *
     * @param bizId                来源ID
     * @param fileCategoryTypeList 附件类型集合
     * @return 附件信息
     */
    @Override
    public Map<Integer, List<FileInfoEntity>> getFileMapByBizIdAndType(Integer bizId, List<Integer> fileCategoryTypeList, Integer status) {
        List<FileInfoEntity> fileBaseInfoList = this.getFileInfoByBizIdAndType(bizId, fileCategoryTypeList, status, null);
        Map<Integer, List<FileInfoEntity>> fileBaseInfoMap = fileBaseInfoList.stream().collect(Collectors.groupingBy(FileInfoEntity::getFileCategoryType));
        fileCategoryTypeList.forEach(it -> {
            if (CollectionUtils.isEmpty(fileBaseInfoMap.get(it))) {
                fileBaseInfoMap.put(it, Collections.emptyList());
            }
        });
        return fileBaseInfoMap;
    }

    /**
     * 拼接文件基本信息-展示
     *
     * @param fileBusinessEntityList 文件关系
     * @return
     */
    private List<FileInfoEntity> covert2FileBaseInfo(List<FileBusinessEntity> fileBusinessEntityList) {
        List<Integer> attachFileIds = fileBusinessEntityList.stream()
                .map(FileBusinessEntity::getFileId)
                .collect(Collectors.toList());
        List<FileInfoEntity> fileInfoEntityList = !CollectionUtils.isEmpty(attachFileIds) ? fileInfoService.getFileListByIds(attachFileIds) : Collections.emptyList();
        //Map: 文件id，文件类型
        Map<Integer, List<FileBusinessEntity>> fileIdTypeMap = fileBusinessEntityList.stream().collect(Collectors.groupingBy(FileBusinessEntity::getFileId));
        String sasToken = azureBlobUtil.getSharedAccessSignature();
        return fileInfoEntityList.stream()
                .map(fileInfoEntity -> {
                    FileBusinessEntity fileBusinessEntity = fileIdTypeMap.get(fileInfoEntity.getId()).get(0);
                    String viewPort = fileInfoEntity.getFsType().equals(FileServiceTypeEnum.LOCAL_SERVER.getValue()) ? filePdfPort : azureBlobUtil.getHostUrl();
                    return fileInfoEntity.setFileCategoryType(fileBusinessEntity.getBizCategory())
                            //todo:husky-nana
                            .setFileUrl(viewPort + fileInfoEntity.getPath() + sasToken)
//                            .setFileUrl(viewPort + fileInfoEntity.getPath())
                            .setFilePathUrl(viewPort + fileInfoEntity.getPath())
                            .setFileCategoryTypeInfo(FileCategoryType.valueOf(fileBusinessEntity.getBizCategory()).getMsg())
                            .setBizFileStatus(fileBusinessEntity.getStatus())
                            .setFileBusinessId(fileBusinessEntity.getId())
                            .setMemo(!StringUtils.isBlank(fileBusinessEntity.getMemo()) ? fileBusinessEntity.getMemo() : "");
                })
                .collect(Collectors.toList());
    }

}
