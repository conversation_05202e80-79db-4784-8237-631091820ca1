package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.PayConditionMapper;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class PayConditionDao extends BaseDaoImpl<PayConditionMapper, PayConditionEntity> {

    public List<PayConditionEntity> queryPayCondition(PayConditionDTO payConditionDTO) {
        LambdaQueryWrapper<PayConditionEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (null != payConditionDTO.getSalesType()) {
            if (payConditionDTO.getSalesType() == 0) {
                queryWrapper.in(PayConditionEntity::getSalesType, 0, 1, 2);
            } else {
                queryWrapper.in(PayConditionEntity::getSalesType, 0, payConditionDTO.getSalesType());
            }
        }
        queryWrapper.eq(null != payConditionDTO.getCreditDays(), PayConditionEntity::getCreditDays, payConditionDTO.getCreditDays())
                .eq(null != payConditionDTO.getDepositRate(), PayConditionEntity::getDepositRate, payConditionDTO.getDepositRate())
                .eq(StringUtils.isNotBlank(payConditionDTO.getBuCode()), PayConditionEntity::getBuCode, payConditionDTO.getBuCode())
                .eq(null != payConditionDTO.getAddedDepositRate(), PayConditionEntity::getAddedDepositRate, payConditionDTO.getAddedDepositRate())
                .eq(null != payConditionDTO.getInvoicePaymentRate(), PayConditionEntity::getInvoicePaymentRate, payConditionDTO.getInvoicePaymentRate())
                .eq(StringUtils.isNotBlank(payConditionDTO.getLkgCode()), PayConditionEntity::getCode, payConditionDTO.getLkgCode())
                .eq(StringUtils.isNotBlank(payConditionDTO.getAtlasCode()), PayConditionEntity::getMdmPayConditionCode, payConditionDTO.getAtlasCode())
                .eq(StringUtil.isNotBlank(payConditionDTO.getPayConditionCode()), PayConditionEntity::getCode, payConditionDTO.getPayConditionCode())
                .eq(null != payConditionDTO.getStatus(), PayConditionEntity::getStatus, payConditionDTO.getStatus())
                .eq(PayConditionEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        return this.baseMapper.selectList(queryWrapper);
    }

    public IPage<PayConditionEntity> pagePayCondition(QueryDTO<PayConditionDTO> queryDTO) {
        LambdaQueryWrapper<PayConditionEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (null != queryDTO && null != queryDTO.getCondition()) {
            PayConditionDTO payConditionDTO = queryDTO.getCondition();
            queryWrapper = Wrappers.<PayConditionEntity>lambdaQuery()
                    .like(StringUtils.isNotBlank(payConditionDTO.getLkgCode()), PayConditionEntity::getCode, StringUtils.isNotBlank(payConditionDTO.getLkgCode()) ? payConditionDTO.getLkgCode().trim() : null)
                    .like(StringUtils.isNotBlank(payConditionDTO.getAtlasCode()), PayConditionEntity::getMdmPayConditionCode, StringUtils.isNotBlank(payConditionDTO.getAtlasCode()) ? payConditionDTO.getAtlasCode().trim() : null)
                    .eq(null != payConditionDTO.getStatus(), PayConditionEntity::getStatus, payConditionDTO.getStatus())
                    .eq(PayConditionEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .orderByDesc(PayConditionEntity::getUpdatedAt);
        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }
}
