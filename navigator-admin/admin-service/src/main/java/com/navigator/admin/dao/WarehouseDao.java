package com.navigator.admin.dao;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.navigator.admin.mapper.WarehouseMapper;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.WarehouseConfigTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class WarehouseDao extends BaseDaoImpl<WarehouseMapper, WarehouseEntity> {

    public IPage<WarehouseEntity> getWarehouseList(QueryDTO<WarehouseBO> queryDTO) {
        LambdaQueryWrapper<WarehouseEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (null != queryDTO && null != queryDTO.getCondition()) {
            WarehouseBO condition = queryDTO.getCondition();
            queryWrapper = Wrappers.<WarehouseEntity>lambdaQuery()
                    .like(CharSequenceUtil.isNotBlank(condition.getName()), WarehouseEntity::getName, CharSequenceUtil.isNotBlank(condition.getName()) ? condition.getName().trim() : null)
                    .like(CharSequenceUtil.isNotBlank(condition.getCode()), WarehouseEntity::getCode, CharSequenceUtil.isNotBlank(condition.getCode()) ? condition.getCode().trim() : null)
                    .eq(condition.getWarehouseType() != null, WarehouseEntity::getWarehouseType, condition.getWarehouseType())
                    .eq(condition.getStatus() != null, WarehouseEntity::getStatus, condition.getStatus())
                    .eq(condition.getIsUnset() != null, WarehouseEntity::getIsUnset, condition.getIsUnset())
                    .eq(WarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .orderByDesc(WarehouseEntity::getUpdatedAt);
        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<WarehouseEntity> getWarehouseByCondition(WarehouseBO warehouseBO) {
        return this.baseMapper.selectList(Wrappers.<WarehouseEntity>lambdaQuery()
                .like(CharSequenceUtil.isNotBlank(warehouseBO.getName()), WarehouseEntity::getName, CharSequenceUtil.isNotBlank(warehouseBO.getName()) ? warehouseBO.getName().trim() : null)
                .like(CharSequenceUtil.isNotBlank(warehouseBO.getCode()), WarehouseEntity::getCode, CharSequenceUtil.isNotBlank(warehouseBO.getCode()) ? warehouseBO.getCode().trim() : null)
                .eq(warehouseBO.getWarehouseType() != null, WarehouseEntity::getWarehouseType, warehouseBO.getWarehouseType())
                .eq(warehouseBO.getStatus() != null, WarehouseEntity::getStatus, warehouseBO.getStatus())
                .eq(warehouseBO.getIsUnset() != null, WarehouseEntity::getIsUnset, warehouseBO.getIsUnset())
                .eq(WarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(WarehouseEntity::getUpdatedAt));
    }

    public WarehouseEntity getWarehouseByUniqueCode(String uniqueCode, Integer type) {
        List<WarehouseEntity> list = this.lambdaQuery()
                .eq(WarehouseEntity::getUniqueCode, uniqueCode)
                .eq(null != type, WarehouseEntity::getType, type)
                .eq(WarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .list();
        return list.isEmpty() ? null : list.get(0);
    }

    public WarehouseEntity getWarehouseByName(String name, Integer isDce) {
        List<WarehouseEntity> list = this.lambdaQuery()
                .eq(WarehouseEntity::getName, name)
                .eq(isDce != null, WarehouseEntity::getIsDce, isDce)
                .in(WarehouseEntity::getType, WarehouseConfigTypeEnum.GENERAL.getValue(), WarehouseConfigTypeEnum.FACTORY.getValue())
                .eq(WarehouseEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(WarehouseEntity::getUpdatedAt)
                .list();
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据条件：获取dba_warehouse列表
     *
     * @param condition
     * @return
     */
    public List<WarehouseEntity> queryWarehouseList(WarehouseBO condition) {
        return this.list(WarehouseEntity.lqw(condition));
    }

    /**
     * 保存warehouse信息(自定义id)
     *
     * @param warehouseEntity warehouse实体
     * @return
     */
    public boolean saveWareHouseWithId(WarehouseEntity warehouseEntity) {
        return SqlHelper.retBool(this.getBaseMapper().saveWareHouseWithId(warehouseEntity));
    }
}
