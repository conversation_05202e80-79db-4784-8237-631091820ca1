package com.navigator.admin.service.magellan.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.magellan.RolePowerDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.PowerDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.entity.RolePowerEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.qo.RolePowerQO;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.magellan.IRolePowerService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Service
public class RolePowerServiceImpl implements IRolePowerService {

    @Autowired
    private RolePowerDao rolePowerDao;
    @Autowired
    private IRoleService roleService;

    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;

    @Resource
    private EmployFacade employFacade;
    @Resource
    private IOperationDetailService operationDetailService;

    @Override
    public void saveOrUpdateRolePower(PowerDTO powerDTO) {
        RoleEntity roleEntity = roleService.getRoleById(Integer.parseInt(powerDTO.getRoleId()));
        rolePowerDao.deleteRoleByRoleId(Integer.parseInt(powerDTO.getRoleId()));

        BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity = new BusinessDetailUpdateRecordEntity();
        //记录修改客户主数据人
        businessDetailUpdateRecordEntity
                .setDetailCode(BusinessDetailCodeEnum.EMPLOY_ROLE_EDIT.getValue())
                .setBusinessId(Integer.valueOf(powerDTO.getRoleId()))
                .setData(JSON.toJSONString(powerDTO))
                .setCreatedAt(new Date())
                .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        businessDetailUpdateRecordService.saveBusinessDetailUpdateRecord(businessDetailUpdateRecordEntity);

        for (Integer powerId : powerDTO.getPowerIdList()) {
            RolePowerEntity rolePowerEntity = new RolePowerEntity();
            rolePowerEntity.setPowerId(powerId)
                    .setRoleId(Integer.parseInt(powerDTO.getRoleId()))
                    .setRoleDefId(roleEntity.getRoleDefId());
            rolePowerDao.save(rolePowerEntity);
        }

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(roleEntity.getName())
                    .setDtoData(JSON.toJSONString(powerDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.COPY_PERMISSION)
                    .setReferBizId(Integer.parseInt(powerDTO.getRoleId()))
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<RolePowerEntity> queryByRoleId(String roleId) {
        return rolePowerDao.queryByRoleId(roleId);
    }

    @Override
    public List<RolePowerEntity> queryByRoleIdList(List<Integer> roleIdList) {
        return rolePowerDao.queryByRoleIdList(roleIdList);
    }

    @Override
    public void addRolePower(PowerDTO powerDTO) {
        List<RoleEntity> roleEntities = roleService.queryByRoleDefIdList(powerDTO.getRoleDefIdList());
        if (CollectionUtils.isNotEmpty(roleEntities)) {
            for (RoleEntity roleEntity : roleEntities) {
                for (Integer powerId : powerDTO.getPowerIdList()) {
                    RolePowerEntity rolePowerEntity = new RolePowerEntity();
                    rolePowerEntity
                            .setPowerId(powerId)
                            .setRoleId(roleEntity.getId())
                            .setRoleDefId(roleEntity.getRoleDefId());
                    rolePowerDao.save(rolePowerEntity);
                }
            }
        }
    }

    @Override
    public void copyRolePower(Integer sourceRoleId, Integer targetRoleId) {
        List<RolePowerEntity> rolePowerEntities = rolePowerDao.queryByRoleId(String.valueOf(sourceRoleId));
        rolePowerDao.deleteRoleByRoleId(targetRoleId);
        for (RolePowerEntity rolePowerEntity : rolePowerEntities) {
            rolePowerEntity
                    .setId(null)
                    .setRoleId(targetRoleId)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
            ;
            rolePowerDao.save(rolePowerEntity);
        }
    }

    @Override
    public Set<Integer> queryPowerIdList(RolePowerQO condition) {
        return rolePowerDao.queryPowerIdList(condition);
    }

    @Override
    public void updateDeletedByPowerIdAndRoleId(Integer powerId, Integer roleId) {
        rolePowerDao.update(Wrappers.<RolePowerEntity>lambdaUpdate()
                .eq(RolePowerEntity::getPowerId, powerId)
                .eq(RolePowerEntity::getRoleId, roleId)
                .set(RolePowerEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(RolePowerEntity::getUpdatedAt, DateTimeUtil.now()));
    }

    @Override
    public void saveRolePower(RolePowerEntity rolePowerEntity) {
        rolePowerDao.save(rolePowerEntity);
    }

    @Override
    public List<RolePowerEntity> getAllRolePowerList() {
        return rolePowerDao.list(Wrappers.<RolePowerEntity>lambdaQuery()
                .eq(RolePowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

}
