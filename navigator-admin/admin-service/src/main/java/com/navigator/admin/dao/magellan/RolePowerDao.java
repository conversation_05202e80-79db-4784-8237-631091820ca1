package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.RolePowerMapper;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.entity.RolePowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.admin.pojo.qo.RolePowerQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Dao
public class RolePowerDao extends BaseDaoImpl<RolePowerMapper, RolePowerEntity> {
    @Resource
    private PowerDao powerDao;

    public void deleteRoleByRoleId(int roleId) {
        remove(Wrappers.<RolePowerEntity>lambdaQuery()
                .eq(RolePowerEntity::getRoleId, roleId));
    }

    public List<RolePowerEntity> queryByRoleId(String roleId) {
        return list(Wrappers.<RolePowerEntity>lambdaQuery().eq(RolePowerEntity::getRoleId, roleId));
    }

    public List<RolePowerEntity> queryByRoleIdList(List<Integer> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            roleIdList = Collections.singletonList(-1);
        }
        return list(Wrappers.<RolePowerEntity>lambdaQuery()
                .in(RolePowerEntity::getRoleId, roleIdList));

    }

    /**
     * 获取权限ID列表
     *
     * @param condition
     * @return
     */
    public Set<Integer> queryPowerIdList(RolePowerQO condition) {
        Set<Integer> result = this.list(RolePowerEntity.lqw(condition)).stream()
                .map(RolePowerEntity::getPowerId)
                .collect(Collectors.toSet());
        // 如果为空直接返回
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        // 过滤不存在Code的权限ID
        List<PowerEntity> powerEntityList = powerDao.list(PowerEntity.lqw(new PowerQO().setPowerIdList(result).setIsCodeNotNull(1)));

        // set转list
        result = powerEntityList.stream()
                .map(PowerEntity::getId)
                .collect(Collectors.toSet());

        return result;
    }

}
