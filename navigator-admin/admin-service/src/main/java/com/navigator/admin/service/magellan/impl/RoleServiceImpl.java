package com.navigator.admin.service.magellan.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.dao.magellan.RoleDao;
import com.navigator.admin.dao.magellan.RoleDefDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.qo.*;
import com.navigator.admin.pojo.vo.RoleDefVO;
import com.navigator.admin.pojo.vo.RoleVO;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.SiteService;
import com.navigator.admin.service.magellan.*;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class RoleServiceImpl implements IRoleService {

    @Autowired
    private RoleDao roleDao;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private IEmployRoleService iEmployRoleService;
    @Autowired
    private RoleDefDao roleDefDao;
    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private ICompanyService companyService;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private IMenuService menuService;
    @Resource
    private IRoleService roleService;
    @Resource
    private IPowerService powerService;
    @Resource
    private IRoleMenuService roleMenuService;
    @Resource
    private IRolePowerService rolePowerService;
    @Resource
    private SiteService siteService;
    @Resource
    private CommonProperties commonProperties;

    @Override
    public RoleEntity getRoleById(Integer id) {
        return roleDao.getRoleById(id);
    }

    @Override
    public List<RoleEntity> getRoleByRoleName(String roleName) {
        return roleDao.getRoleByRoleName(roleName);
    }

    @Override
    public void saveOrUpdate(RoleDTO roleDTO, RoleDefEntity roleDefEntity) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<Integer> categoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        ;
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        List<Integer> factoryIdList = factoryEntityList.stream().map(FactoryEntity::getId).collect(Collectors.toList());
        List<CustomerEntity> customerEntities = customerFacade.queryFactoryCustomer();
        Map<Integer, Integer> customerIdMap = customerEntities.stream().filter(i -> i.getCompanyId().toString().equals(roleDTO.getCompanyId()))
                .collect(Collectors.toMap(CustomerEntity::getFactoryId, CustomerEntity::getId, (k1, k2) -> k1));
        roleDao.deleteByRoleDefId(roleDTO.getRoleDefId());
        if (roleDefEntity.getIsBaseCategory() == 0 && roleDefEntity.getIsBaseCompany() == 0) {
            RoleEntity roleEntity = new RoleEntity();
            roleEntity.setCategoryId(0)
                    .setBelongCustomerId(0)
                    .setFactoryId(0)
                    .setRoleDefId(roleDTO.getRoleDefId())
                    .setSystem(roleDTO.getSystem())
                    .setName(roleDTO.getName())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            roleDao.save(roleEntity);
        } else {
            for (Integer categoryId : categoryIdList) {
                for (Integer factoryId : factoryIdList) {
                    RoleEntity roleEntity = new RoleEntity();
                    roleEntity.setCategoryId(categoryId)
                            .setBelongCustomerId(customerIdMap.get(factoryId))
                            .setFactoryId(factoryId)
                            .setRoleDefId(roleDTO.getRoleDefId())
                            .setSystem(roleDTO.getSystem())
                            .setName(roleDTO.getName())
                            .setCreatedBy(userId)
                            .setUpdatedBy(userId)
                    ;
                    roleDao.save(roleEntity);
                }
            }
        }

    }

    @Override
    public List<RoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList) {
        return roleDao.queryByRoleDefIdList(roleDefIdList);
    }

    @Override
    public List<RoleEntity> getRoleListByDefId(Integer roleDefId) {
        return roleDao.getRoleListByDefId(roleDefId);
    }

    @Override
    public List<RoleEntity> queryRole(EmployRoleDTO employRoleDTO) {
        List<RoleEntity> roleEntityList = roleDao.queryRole(employRoleDTO);
        return roleEntityList;
    }

    @Override
    public List<RoleEntity> queryByIdList(List<Integer> roleIdList) {
        return roleDao.queryByIdList(roleIdList);
    }

    @Override
    public List<RoleEntity> queryRoleListByDefInfo(Integer roleDefId, Integer categoryId, Integer customerId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefId(roleDefId)
                .setCategoryId(categoryId)
                .setBelongCustomerId(customerId);

        return roleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<RoleEntity> queryRoleListByDefInfos(List<Integer> roleDefIds, Integer categoryId, String siteCode) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();


        roleQueryDTO.setRoleDefIdList(roleDefIds)
                .setCategoryId(categoryId)
                .setSiteCode(siteCode);

        return roleDao.queryRoleListByDefInfos(roleQueryDTO);
    }

    @Override
    public List<RoleEntity> queryRoleListByDefInfos2(List<Integer> roleDefIds, Integer categoryId, Integer factoryId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefIdList(roleDefIds)
                .setCategoryId(categoryId)
                .setFactoryId(factoryId);

        return roleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<RoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId) {
        return roleDao.queryByIdListAndCategory(roleIdList, categoryId);
    }

    @Override
    public List<RoleEntity> getRoleAllList() {
        return roleDao.getRoleAllList();
    }

    @Override
    public List<RoleEntity> getRoleAllListCode(String code) {
        return roleDao.getRoleAllListCode(code);
    }

    @Override
    public List<RoleEntity> queryRoleByEmployId(String employId) {
        List<EmployRoleEntity> employRoleEntityList = iEmployRoleService.getEmployRolesByEmploy(employId);
        if (CollectionUtil.isEmpty(employRoleEntityList)) {
            return Collections.emptyList();
        }
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
        return roleDao.queryByIdList(roleIdList);
    }

    @Override
    public Result queryRoleDefList(QueryDTO<RoleQueryDTO> queryDTO) {
        String userId = JwtUtils.getCurrentUserId();
        Page<RoleDefEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        RoleQueryDTO roleQueryDTO = queryDTO.getCondition();

        //分页查询
        IPage<RoleDefEntity> iPage = roleDefDao.queryPageByRoleQueryDTO(page, roleQueryDTO);
        List<RoleDefVO> list = new ArrayList<>();
        List<RoleDefEntity> roleDefList = roleDefDao.queryFatherRoleDefList();
        Map<Integer, String> fatherNameMap = roleDefList.stream().collect(Collectors.toMap(RoleDefEntity::getId, RoleDefEntity::getName));
        iPage.getRecords().forEach(i -> {
            BusinessDetailUpdateRecordDTO businessDetailUpdateRecordDTO = new BusinessDetailUpdateRecordDTO();
            businessDetailUpdateRecordDTO.setBusinessId(i.getId()).setDetailCode(BusinessDetailCodeEnum.EMPLOY_ROLE_EDIT.getValue());
            BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity
                    = businessDetailUpdateRecordService.detailUpdateSelect(businessDetailUpdateRecordDTO);
            RoleDefVO roleDefVO = new RoleDefVO();
            roleDefVO
                    .setId(i.getId())
                    .setRoleName(i.getName())
                    .setIsBaseCategory(i.getIsBaseCategory())
                    .setIsBaseCompany(i.getIsBaseCompany())
                    .setIsBaseFactory(i.getIsBaseFactory())
                    .setRoleType(fatherNameMap.get(i.getParentId()))
                    .setUpdatedBy(businessDetailUpdateRecordEntity == null ? employFacade.getEmployCache(i.getUpdatedBy()) : businessDetailUpdateRecordEntity.getCreatedBy())
                    .setUpdatedAt(businessDetailUpdateRecordEntity == null ? i.getUpdatedAt() : businessDetailUpdateRecordEntity.getCreatedAt());

            list.add(roleDefVO);
        });
        return Result.page(iPage, list);

    }

    @Override
    public Result queryPageByQueryDTO(Page<RoleEntity> page, RoleQueryDTO roleQueryDTO) {
        List<Integer> categoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> factoryNameMap = factoryEntityList.stream()
                .collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));
        List<Integer> factoryIdList = factoryEntityList.stream().map(FactoryEntity::getId).collect(Collectors.toList());
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        List<Integer> companyIdList = companyEntityList.stream().map(CompanyEntity::getId).collect(Collectors.toList());
        Map<Integer, String> companyNameMap = companyEntityList.stream()
                .collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
        if (CollectionUtils.isEmpty(roleQueryDTO.getCategoryIdList()) || roleQueryDTO.getCategoryIdList().contains(0)) {
            categoryIdList.add(0);
            roleQueryDTO.setCategoryIdList(categoryIdList);
        }

        if (CollectionUtils.isEmpty(roleQueryDTO.getFactoryIdList()) || roleQueryDTO.getFactoryIdList().contains(0)) {
            factoryIdList.add(0);
            roleQueryDTO.setFactoryIdList(factoryIdList);
        }

        if (CollectionUtils.isEmpty(roleQueryDTO.getCompanyIdList()) || roleQueryDTO.getCompanyIdList().contains(0)) {
            companyIdList.add(0);
            roleQueryDTO.setCompanyIdList(companyIdList);
        }
        List<RoleVO> list = new ArrayList<>();
        IPage<RoleEntity> iPage = roleDao.queryPageByQueryDTO(page, roleQueryDTO);
        iPage.getRecords().forEach(i -> {
            RoleVO roleVO = new RoleVO();
            String category2Name = GoodsCategoryEnum.getDesc(i.getCategory2());
            roleVO.setId(i.getId())
                    .setCompanyName(companyNameMap.get(i.getCompanyId()))
                    .setCategoryName("0".equals(category2Name) ? "-" : category2Name)
                    .setRoleName(i.getName())
                    .setFactoryName(factoryNameMap.get(i.getFactoryId()));
            list.add(roleVO);
        });

        return Result.page(iPage, list);

    }

    @Override
    public List<RoleEntity> queryIdListByCategoryAndFactory(String name, List<Integer> categoryIdList, List<Integer> factoryIdList, List<Integer> companyIdList) {
        return roleDao.queryIdListByCategoryAndFactory(name, categoryIdList, factoryIdList, companyIdList);

    }

    @Override
    public void save(RoleDTO roleDTO, RoleDefEntity roleDefEntity) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<Integer> categoryIdList = roleDTO.getCategoryIdList();
        if (roleDTO.getIsBaseCategory() == 0) {
            categoryIdList = Collections.singletonList(0);
        }
        List<Integer> factoryIdList = roleDTO.getFactoryIdList();
        if (roleDTO.getIsBaseFactory() == 0) {
            factoryIdList = Collections.singletonList(0);
        }
        List<Integer> companyIdList = roleDTO.getCompanyIdList();
        if (roleDTO.getIsBaseCompany() == 0) {
            companyIdList = Collections.singletonList(0);
        }
        List<CustomerEntity> customerEntities = customerFacade.queryFactoryCustomer();

        roleDao.deleteByRoleDefId(roleDTO.getRoleDefId());
        for (Integer categoryId : categoryIdList) {
            for (Integer factoryId : factoryIdList) {
                for (Integer companyId : companyIdList) {
                    //case：1002570  多主体 从主体集合钟获取主体id
                    Map<Integer, Integer> customerIdMap = customerEntities.stream().filter(i -> i.getCompanyId().toString().equals(String.valueOf(companyId)))
                            .collect(Collectors.toMap(CustomerEntity::getFactoryId, CustomerEntity::getId, (k1, k2) -> k1));
                    customerIdMap.put(0, 0);
                    RoleEntity roleEntity = new RoleEntity();
                    Integer category2 = 0;
                    String siteCode = null;
                    if (categoryId != null && categoryId != 0) {
                        category2 = Integer.parseInt(IdNameConverter.getName(IdNameType.category_id_serialNo, categoryId.toString()));
                        CategoryEntity category = categoryFacade.getParentCategoryBySerialNo(category2);
                        siteCode = siteService.getSiteCode(new SiteQO().setCompanyId(companyId).setFactoryId(factoryId).setCategory1(String.valueOf(category.getSerialNo())));
                    }
                    roleEntity
                            .setCategoryId(categoryId)
                            .setCategory2(category2)
                            .setFactoryId(factoryId)
                            .setCompanyId(companyId)
                            .setRoleDefId(roleDTO.getRoleDefId())
                            .setName(roleDTO.getName())
                            .setLevel(2)
                            .setCreatedBy(userId)
                            .setUpdatedBy(userId)
                            .setBelongCustomerId(customerIdMap.get(factoryId))
                            .setSiteCode(siteCode)
                    ;
                    roleDao.save(roleEntity);
                }
            }
        }
    }

    @Override
    public RoleAuthDTO getRoleAuth(RoleAuthQO roleAuthQO) {
        RoleAuthDTO roleAuthDTO = new RoleAuthDTO();
        // 授权菜单
        List<MenuEntity> menuList = null;
        // 授权权限
        List<PowerEntity> powerList = null;
        // 默认查询条件
        MenuQO menuQO = new MenuQO().setSystem(1);
        PowerQO powerQO = new PowerQO().setSystem(1).setIsCodeNotNull(1);
        // 查询通用
        if (roleAuthQO.getCategory2() == 0) {
            menuQO.setIsCategory(0);
            powerQO.setIsCategory(0);
        }
        if (this.isAdmin(roleAuthQO.getUserId())) {
            // 是管理员
            menuList = menuService.queryMenuList(menuQO);
            powerList = powerService.queryPowerList(powerQO);
        } else {
            // 不是管理员
            // 用户关联的所有角色
            List<Integer> userRoleIdList = iEmployRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(roleAuthQO.getUserId()));
            //过滤实角色的品类（默认+品类）
            List<RoleEntity> roleEntityList = roleService.queryByIdList(userRoleIdList);
            if (CollUtil.isNotEmpty(roleEntityList)) {
                userRoleIdList = roleEntityList.stream()
                        .filter(role -> Arrays.asList(roleAuthQO.getCategory2(), 0).contains(role.getCategory2()))
                        .map(RoleEntity::getId)
                        .collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(userRoleIdList)) {
                // 角色关联菜单
                Set<Integer> menuIdList = roleMenuService.queryMenuIdList(new RoleMenuQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(menuIdList)) {
                    menuQO.setMenuIdList(menuIdList);
                    menuList = menuService.queryMenuList(menuQO);
                }
                // 角色关联权限
                Set<Integer> powerIdList = rolePowerService.queryPowerIdList(new RolePowerQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(powerIdList)) {
                    powerQO.setPowerIdList(powerIdList);
                    powerList = powerService.queryPowerList(powerQO);
                }
            }
        }
        // 构造菜单树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(9).setWeightKey("sort");
        if (CollUtil.isEmpty(menuList)) {
            menuList = menuService.queryMenuList(new MenuQO().setCode("N001"));
        }
        List<MenuEntity> menuEntityList = this.getParentMenu(menuList);
        List<Tree<Integer>> authMenuList = TreeUtil.build(menuEntityList, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, roleAuthQO.getCategory2()));
        roleAuthDTO.setAuthMenuList(authMenuList);
        // 构造权限列表
        if (CollUtil.isNotEmpty(powerList)) {
            List<Tree<Integer>> authPowerList = new ArrayList<>();
            powerList.forEach(item -> authPowerList.add(this.toTree(item, null, roleAuthQO.getCategory2())));
            roleAuthDTO.setAuthPowerList(authPowerList);
        } else {
            roleAuthDTO.setAuthPowerList(new ArrayList<>());
        }
        return roleAuthDTO;
    }

    private List<MenuEntity> getParentMenu(List<MenuEntity> menuList) {
        List<MenuEntity> menuEntityList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(menuList)) {
            List<Integer> menuIdList = menuList.stream().map(MenuEntity::getId).collect(Collectors.toList());
            menuEntityList.addAll(menuList);
            for (MenuEntity menuEntity : menuList) {
                if (null != menuEntity.getParentId() && 0 != menuEntity.getParentId()) {
                    MenuEntity parentMenuEntity = menuService.getMenuById(String.valueOf(menuEntity.getParentId()));
                    if (null != parentMenuEntity && !menuIdList.contains(parentMenuEntity.getId())) {
                        menuEntityList.add(parentMenuEntity);
                        menuIdList.add(parentMenuEntity.getId());
                    }
                }
            }
        }
        return menuEntityList;
    }

    @Override
    public RoleAuthMenuDTO getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO) {
        // 所有菜单
        List<MenuEntity> list = menuService.queryMenuList(new MenuQO().setSystem(1));
        // 角色关联菜单
        Set<Integer> authIdList = roleMenuService.queryMenuIdList(new RoleMenuQO().setRoleId(roleAuthMenuQO.getRoleId()));
        // 构造树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(99).setWeightKey("sort");
        List<Tree<Integer>> menuList = TreeUtil.build(list, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, null));
        RoleAuthMenuDTO roleAuthMenuDTO = new RoleAuthMenuDTO();
        roleAuthMenuDTO.setMenuList(menuList);
        // 叶子节点
        List<Integer> leafIdList = new ArrayList<>();
        for (Tree<Integer> tree : menuList) {
            if (CollUtil.isNotEmpty(tree.getChildren())) {
                for (Tree<Integer> child : tree.getChildren()) {
                    if (authIdList.contains(child.getId())) {
                        leafIdList.add(child.getId());
                    }
                }
            } else if (authIdList.contains(tree.getId())) {
                leafIdList.add(tree.getId());
            }
        }
        roleAuthMenuDTO.setAuthIdList(leafIdList);
        return roleAuthMenuDTO;
    }

    @Override
    public RoleAuthPowerDTO getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO) {
        // 所有权限
        List<PowerEntity> list = powerService.queryPowerList(new PowerQO().setSystem(1));
        // 角色关联权限
        Set<Integer> authIdList = rolePowerService.queryPowerIdList(new RolePowerQO().setRoleId(roleAuthPowerQO.getRoleId()));
        // 构造树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(99).setWeightKey("sort");
        List<Tree<Integer>> powerList = TreeUtil.build(list, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, null));
        RoleAuthPowerDTO roleAuthPowerDTO = new RoleAuthPowerDTO();
        roleAuthPowerDTO.setPowerList(powerList);
        roleAuthPowerDTO.setAuthIdList(authIdList);
        return roleAuthPowerDTO;
    }

    @Override
    public LinkedHashSet<String> queryRoleSiteCodeSet(Integer userId) {
        LinkedHashSet<String> resultSet = new LinkedHashSet<>();
        // 用户ID
        if (StringUtil.isNullBlank(userId)) {
            userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        }
        // 用户关联角色
        List<Integer> roleIdList = iEmployRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId));
        if (CollUtil.isNotEmpty(roleIdList)) {
            List<RoleEntity> list = roleDao.list(RoleEntity.lqw(null).in(RoleEntity::getId, roleIdList));
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    if (StringUtil.isNotNullBlank(item.getSiteCode())) {
                        resultSet.add(item.getSiteCode());
                    }
                });
            }
        }
        return resultSet;
    }

    @Override
    public LinkedHashSet<Integer> queryCategory2List(Integer userId) {
        LinkedHashSet<Integer> resultSet = new LinkedHashSet<>();
        // 用户ID
        if (StringUtil.isNullBlank(userId)) {
            userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        }
        // 用户关联角色
        List<Integer> roleIdList = iEmployRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId));
        if (CollUtil.isNotEmpty(roleIdList)) {
            List<RoleEntity> list = roleDao.list(RoleEntity.lqw(null).in(RoleEntity::getId, roleIdList));
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    if (StringUtil.isNotNullBlank(item.getCategory2())) {
                        resultSet.add(item.getCategory2());
                    }
                });
            }
        }
        return resultSet;
    }

    /**
     * 菜单转树
     *
     * @param entity
     * @param tree
     * @param category2
     */
    private void toTree(MenuEntity entity, Tree<Integer> tree, Integer category2) {
        tree.setId(entity.getId());
        tree.setParentId(entity.getParentId());
        tree.setName(entity.getName());
        tree.putExtra("code", entity.getCode());
        tree.putExtra("parentCode", entity.getParentCode());
        tree.putExtra("icon", entity.getIcon());
        tree.putExtra("isCategory", entity.getIsCategory());
        if (StringUtil.isNotNullBlank(entity.getUrl()) && StringUtil.isNotNullBlank(category2)) {
            tree.putExtra("url", entity.getUrl().replaceAll("\\{category2\\}", category2.toString()));
        } else {
            tree.putExtra("url", entity.getUrl());
        }
        if (StringUtil.isNullBlank(entity.getSort())) {
            entity.setSort(100);
        }
        tree.putExtra("sort", entity.getSort());
    }

    /**
     * 权限转树
     *
     * @param entity
     * @param tree
     * @param category2
     */
    private Tree<Integer> toTree(PowerEntity entity, Tree<Integer> tree, Integer category2) {
        if (tree == null) {
            tree = new Tree<>();
        }
        tree.setId(entity.getId());
        tree.setParentId(entity.getParentId());
        tree.setName(entity.getName());
        tree.putExtra("preCode", entity.getPreCode());
        if (StringUtil.isNotNullBlank(entity.getCode()) && StringUtil.isNotNullBlank(category2)) {
            tree.putExtra("code", entity.getCode().replaceAll("\\{category2\\}", category2.toString()));
        } else {
            tree.putExtra("code", entity.getCode());
        }
        tree.putExtra("describe", entity.getDescribe());
        tree.putExtra("level", entity.getLevel());
        if (StringUtil.isNullBlank(entity.getSort())) {
            entity.setSort(100);
        }
        tree.putExtra("sort", entity.getSort());
        tree.putExtra("isCategory", entity.getIsCategory());
        return tree;
    }

    @Override
    public Boolean isAdmin(Integer userId) {
        if (userId == null) {
            userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        }
        // 用户ID小于10
        if (new BigDecimal(userId).compareTo(BigDecimal.TEN) <= 0) {
            return true;
        }
        // 用户关联的所有角色
        List<Integer> userRoleIdList = iEmployRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId));
        if (CollUtil.isEmpty(userRoleIdList)) {
//            throw new BusinessException("用户无关联角色！");
            return false;
        }
        if (CollectionUtil.containsAny(userRoleIdList, commonProperties.getSystemRoleList())) {
            return true;
        }
        return false;
    }
}
