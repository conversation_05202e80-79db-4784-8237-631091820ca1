package com.navigator.admin.service.systemrule.impl;

import com.navigator.admin.dao.BasicPriceGoodsConfigDao;
import com.navigator.admin.pojo.entity.BasicPriceGoodsConfigEntity;
import com.navigator.admin.service.systemrule.BasicPriceGoodsConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17
 */
@Service
public class BasicPriceGoodsConfigServiceImpl implements BasicPriceGoodsConfigService {
    @Resource
    private BasicPriceGoodsConfigDao basicPriceGoodsConfigDao;

    @Override
    public List<BasicPriceGoodsConfigEntity> queryBasicPriceGoodsConfigByCategoryId(Integer categoryId) {
        return basicPriceGoodsConfigDao.queryBasicPriceGoodsConfigByCategoryId(categoryId);
    }

    @Override
    public BasicPriceGoodsConfigEntity queryBasicPriceGoodsConfigById(Integer id) {
        return basicPriceGoodsConfigDao.getById(id);
    }
}
