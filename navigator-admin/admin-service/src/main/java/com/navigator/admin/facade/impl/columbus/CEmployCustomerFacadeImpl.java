package com.navigator.admin.facade.impl.columbus;

import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.service.columbus.ICEmployCustomerService;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/8
 */
@RestController
public class CEmployCustomerFacadeImpl implements CEmployCustomerFacade {

    @Resource
    private ICEmployCustomerService icEmployCustomerService;

    @Override
    public boolean saveCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity) {
        return icEmployCustomerService.saveCEmployCustomer(cEmployCustomerEntity);
    }

    @Override
    public List<CEmployCustomerEntity> queryCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return icEmployCustomerService.queryCEmployCustomerByEmployIdAndCustomerId(cEmployId, customerId);
    }

    @Override
    public List<CEmployCustomerEntity> getCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return icEmployCustomerService.getCEmployCustomerByEmployIdAndCustomerId(cEmployId, customerId);
    }

    @Override
    public boolean updateCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity) {
        return icEmployCustomerService.updateCEmployCustomer(cEmployCustomerEntity);
    }

    @Override
    public CEmployCustomerEntity getCEmployCustomerById(Integer id) {
        return icEmployCustomerService.getCEmployCustomerById(id);
    }

    @Override
    public void cEmployDataMigration() {
        icEmployCustomerService.cEmployDataMigration();
    }

    @Override
    public List<CEmployCustomerVO> queryCEmployCustomerVOByCEmployId(Integer cEmployId) {
        return icEmployCustomerService.queryCEmployCustomerVOByCEmployId(cEmployId);
    }

    @Override
    public Result getCEmployCustomerPower(Integer customerId) {
        return Result.success(icEmployCustomerService.getCEmployCustomerPower(customerId));
    }

    @Override
    public List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndType(Integer customerId, Integer type, Integer status) {
        return icEmployCustomerService.queryCEmployCustomerByCustomerIdAndType(customerId, type, status);
    }

    @Override
    public Result verifyUserStatus(Integer customerId) {
        return icEmployCustomerService.verifyUserStatus(customerId);
    }
}
