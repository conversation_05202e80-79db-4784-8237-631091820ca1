package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.dto.MenuDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.entity.MenuEntity;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.admin.pojo.vo.MenuVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface IMenuService {
    /**
     * 根据id找菜单
     *
     * @param id
     * @return
     */
    MenuEntity getMenuById(String id);

    /**
     * 根据employId 获取菜单（树状结构）
     *
     * @param menuDTO
     * @return
     */
    MenuVO getMenusByEmploy(MenuDTO menuDTO);

    MenuVO getMenusByCondition(MenuDTO menuDTO);

    MenuVO getMenuByRoleId(RoleDTO roleDTO);

    MenuVO getMenuByEmployId(RoleDTO roleDTO);

    void saveRoleMenu(MenuDTO menuDTO);

    void addRoleMenu(MenuDTO menuDTO);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<MenuEntity> queryMenuList(MenuQO condition);

    MenuEntity getMenuByParentId(Integer parentId);

    /**
     * 获取授权的二级菜单列表
     *
     * @param userId
     * @param category2
     * @return
     */
    List<MenuEntity> getAuthMenuList(Integer userId, Integer category2);
}
