package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.dto.columbus.CPowerDTO;
import com.navigator.admin.pojo.entity.CRolePowerEntity;
import com.navigator.admin.pojo.qo.RolePowerQO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
public interface ICRolePowerService {
    /**
     * v1.0 菜单按钮权限独立设置
     */

    void saveOrUpdateRolePower(CPowerDTO powerDTO);

    List<CRolePowerEntity> queryByRoleId(String roleId, Integer customerId);

    List<CRolePowerEntity> queryByRoleIdList(List<Integer> roleIdList);


    /**
     * v2.0 统一菜单按钮权限
     */

    void saveOrUpdateRolePowerV2(CPowerDTO powerDTO);

    List<CRolePowerEntity> queryByRoleIdV2(String roleId);

    List<CRolePowerEntity> queryByRoleIdListV2(List<Integer> roleIdList);

    void addRolePower(CPowerDTO powerDTO);

    void copyRolePower(Integer sourceRoleId, Integer targetRoleId);

    /**
     * 获取权限ID列表
     *
     * @param condition
     * @return
     */
    Set<Integer> queryPowerIdList(RolePowerQO condition);

    List<CRolePowerEntity> getAllRolePowerList();

    void updateDeletedByPowerIdAndRoleId(Integer powerId, Integer roleId);

    void saveRolePower(CRolePowerEntity cRolePowerEntity);
}
