package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.entity.CRoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ICRoleMenuService {

    List<CRoleMenuEntity> findRoleMenusByRoleId(String roleId);

    void saveRoleMenu(CRoleMenuEntity roleMenuEntity);

    List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList,Integer customerId);

    void deleteRoleMenu(String roleId,Integer customerId);

    List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList);

    void deleteRoleMenu(String roleId);

    void copyRoleMenu(Integer sourceRoleId, Integer targetRoleId);

    /**
     * 获取菜单ID列表
     *
     * @param condition
     * @return
     */
    Set<Integer> queryMenuIdList(RoleMenuQO condition);

    List<CRoleMenuEntity> getAllRoleMenuList();

    void updateDeletedByMenuIdAndRoleId(Integer menuId, Integer roleId);
}
