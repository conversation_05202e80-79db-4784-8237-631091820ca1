package com.navigator.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.common.dto.QueryDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 付款条件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
public interface IPayConditionService {

    List<PayConditionEntity> queryPayCondition(PayConditionDTO payConditionDTO);

    PayConditionEntity getPayConditionById(Integer payConditionId);

    IPage<PayConditionEntity> getPayConditionList(QueryDTO<PayConditionDTO> queryDTO);

    boolean addPayCondition(PayConditionDTO payConditionDTO);

    boolean updatePayCondition(PayConditionDTO payConditionDTO);

    String importPayCondition(MultipartFile file);
}
