package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 文件业务关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IFileBusinessService{

    /**
     * 统一记录存储文件关系
     *
     * @param fileBusinessRelationDTO 记录附件关系请求信息
     */
    Result recordFileRelation(FileBusinessRelationDTO fileBusinessRelationDTO);

    /**
     * 删除文件关系
     *
     * @param referId 业务关联ID
     * @param type    业务文件类型
     */
    void deleteFileRelation(Integer referId, Integer type);

    /**
     * 废弃文件关系信息，改状态（合同驳回）
     *
     * @param referId 业务关联ID
     * @param type    业务文件类型
     * @param memo    驳回/撤回原因
     */
    void dropFileRelation(Integer referId, Integer type, String memo);

    /**
     * 根据业务ID、文件类型获取文件关系信息
     *
     * @param bizId                业务ID
     * @param fileCategoryTypeList 文件类型
     * @param statusEnum           状态
     * @return 文件业务绑定关系
     */
    List<FileInfoEntity> getFileInfoByBizIdAndType(Integer bizId, List<Integer> fileCategoryTypeList,
                                                   Integer statusEnum,
                                                   Integer system);

    /**
     * 根据来源ID、附件类型，查询附件信息
     *
     * @param bizId                来源ID
     * @param fileCategoryTypeList 附件类型集合
     * @return 附件信息
     */
    Map<Integer, List<FileInfoEntity>> getFileMapByBizIdAndType(Integer bizId, List<Integer> fileCategoryTypeList, Integer status);
}
