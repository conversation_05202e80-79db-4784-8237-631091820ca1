package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CRoleMenuMapper;
import com.navigator.admin.pojo.entity.CRoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Dao
public class CRoleMenuDao extends BaseDaoImpl<CRoleMenuMapper, CRoleMenuEntity> {

    public List<CRoleMenuEntity> findRoleMenusByRoleId(String roleId) {
        return this.list(new LambdaQueryWrapper<CRoleMenuEntity>().eq(CRoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CRoleMenuEntity::getRoleId, roleId));
    }

    public List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList, Integer customerId) {
        LambdaQueryWrapper<CRoleMenuEntity> wrapper = Wrappers.<CRoleMenuEntity>lambdaQuery()
                .eq(roleIdList.contains(1), CRoleMenuEntity::getCustomerId, -1)
                .or(roleIdList.contains(1))
                .eq(CRoleMenuEntity::getCustomerId, customerId);
        wrapper.and(i -> i.in(CRoleMenuEntity::getRoleId, roleIdList)
                .eq(CRoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return list(wrapper);
    }

    public void deleteRoleMenu(String roleId, Integer customerId) {
        remove(Wrappers.<CRoleMenuEntity>lambdaQuery()
                .eq(CRoleMenuEntity::getRoleId, roleId)
                .eq(CRoleMenuEntity::getCustomerId, customerId));
    }


    public List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList) {
        LambdaQueryWrapper<CRoleMenuEntity> wrapper = Wrappers.<CRoleMenuEntity>lambdaQuery()
                .in(CRoleMenuEntity::getRoleId, roleIdList)
                .eq(CRoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return list(wrapper);
    }

    public void deleteRoleMenu(String roleId) {
        remove(Wrappers.<CRoleMenuEntity>lambdaQuery()
                .eq(CRoleMenuEntity::getRoleId, roleId)
        );
    }

    /**
     * 获取菜单ID列表
     *
     * @param condition
     * @return
     */
    public Set<Integer> queryMenuIdList(RoleMenuQO condition) {
        List<CRoleMenuEntity> list = this.list(CRoleMenuEntity.lqw(condition));
        Set<Integer> result = new HashSet<>();
        list.forEach(item -> result.add(item.getMenuId()));
        return result;
    }
}
