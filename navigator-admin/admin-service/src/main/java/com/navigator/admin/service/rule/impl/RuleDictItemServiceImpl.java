package com.navigator.admin.service.rule.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.rule.RuleDictItemDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.admin.service.rule.IRuleDictItemService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class RuleDictItemServiceImpl implements IRuleDictItemService {

    @Resource
    private RuleDictItemDao ruleDictItemDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public Result queryRuleDictItemByCondition(QueryDTO<RuleDictItemEntity> queryDTO) {
        RuleDictItemEntity dictItemQO = queryDTO.getCondition();
        IPage<RuleDictItemEntity> itemEntityIPage = ruleDictItemDao.queryByCondition(queryDTO);
        return Result.page(itemEntityIPage);
    }

    @Override
    public List<RuleDictItemEntity> getRuleDictItemById(List<Integer> dictItemIdList) {
        return ruleDictItemDao.getDictItemById(dictItemIdList);
    }

    @Override
    public List<RuleDictItemEntity> getRuleItemByDictCode(String dictCode, String moduleType, String systemId) {
        return ruleDictItemDao.getRuleItemByDictCode(dictCode, moduleType, systemId);
    }

    @Override
    public RuleDictItemEntity getRuleDictItemByCode(String dictCode, String itemCode, Integer itemValue, String moduleType, String systemId) {
        return ruleDictItemDao.getDictItemByCode(dictCode, itemCode, itemValue, moduleType, systemId);
    }

    @Override
    public Result saveRuleDictItem(RuleDictItemEntity ruleDictItemEntity) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        ruleDictItemEntity.setItemDescription(ruleDictItemEntity.getItemName())
                .setItemSort(0)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        ruleDictItemDao.save(ruleDictItemEntity);
        return Result.success();
    }

    @Override
    public Result updateRuleDictItem(RuleDictItemEntity ruleDictItemEntity) {
        RuleDictItemEntity itemEntity = ruleDictItemDao.getById(ruleDictItemEntity.getId());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        itemEntity.setDictCode(ruleDictItemEntity.getDictCode().trim())
                .setDictName(ruleDictItemEntity.getDictName().trim())
                .setItemName(ruleDictItemEntity.getItemName().trim())
                .setItemDescription(ruleDictItemEntity.getItemName().trim())
                .setStatus(ruleDictItemEntity.getStatus())
                .setItemSort(0)
                .setUpdatedAt(DateTimeUtil.now())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedBy(name);
        ruleDictItemDao.updateById(itemEntity);
        return Result.success();
    }
}
