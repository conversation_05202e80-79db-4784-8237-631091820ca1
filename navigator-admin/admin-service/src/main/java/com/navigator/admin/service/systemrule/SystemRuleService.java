package com.navigator.admin.service.systemrule;

import com.navigator.admin.pojo.dto.ContractApproveConfigItemDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.InvoiceTypeDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleCreateDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SystemRuleService {
    /**
     * 根据品类查询
     *
     * @param systemRuleDTO
     * @return SystemRuleVO
     */
    List<SystemRuleVO> getSystemRule(SystemRuleDTO systemRuleDTO);

    /**
     * 根据itemId查询name
     *
     * @param id
     * @return String
     */
    SystemRuleItemEntity getRuleItemById(int id);

    /**
     * 查询所有配置信息
     *
     * @param systemRuleDTO
     * @return
     */
    SystemRuleVO querySystemRuleDetail(SystemRuleDTO systemRuleDTO);

    /**
     * @param systemRuleDTO
     * @return
     */
    List<SystemRuleVO> getNextSystemRule(SystemRuleDTO systemRuleDTO);

    /**
     * 新增更新配置
     *
     * @param systemRuleCreateDTO
     * @return
     */
    boolean saveOrUpdateSystemRule(SystemRuleCreateDTO systemRuleCreateDTO);

    boolean updateInvoiceType(Integer categoryId, List<InvoiceTypeDTO> invoiceTypeDTOList);

    /**
     * 禁用启用配置
     *
     * @param ruleItemId 配置ID
     * @return
     */
    boolean invalidStatus(Integer ruleItemId);

    SystemRuleItemEntity filterBasicPrice(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO);

    SystemRuleItemEntity getInvoiceType(Integer categoryId, Integer invoiceType, String taxRate);

    SystemRuleItemEntity findByLkgCode(Integer categoryId, String ruleCode, String lkgCode);

    Result importSystemRule(MultipartFile uploadFile);

    Result updateLOAValue(SystemRuleCreateDTO systemRuleCreateDTO);

    Result queryLOAValue(SystemRuleCreateDTO systemRuleCreateDTO);

    Result saveLOAValue(SystemRuleCreateDTO systemRuleCreateDTO);

    List<ContractApproveConfigItemDTO> queryContractApproveConfigItem(Integer categoryId);

    Result importDestination(MultipartFile uploadFile);

    Result importWeightCheck(MultipartFile uploadFile);

    Result importPackageWeight(MultipartFile uploadFile);

    Result importLOAValue(MultipartFile file);

}
