package com.navigator.admin.service.magellan.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.magellan.EmployRoleDao;
import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class EmployRoleServiceImpl implements IEmployRoleService {

    @Autowired
    private EmployRoleDao employRoleDao;
    @Autowired
    private IRoleService roleService;

    @Override
    public List<EmployRoleEntity> getEmployRolesByEmploy(String employId) {
        return employRoleDao.getEmployRolesByEmploy(employId);
    }

    @Override
    public List<EmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            roleIds = Collections.singletonList(-1);
        }
        return employRoleDao.getEmployRolesByRoleIds(roleIds);
    }

    @Override
    public void addEmployRole(EmployRoleDTO employRoleDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        if (CollectionUtils.isEmpty(employRoleDTO.getEmployIdList())) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }
        for (Integer employId : employRoleDTO.getEmployIdList()) {
            EmployRoleEntity employRoleEntity = new EmployRoleEntity();
            employRoleEntity
                    .setEmployId(employId)
                    .setRoleDefId(employRoleDTO.getRoleDefId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            employRoleDao.save(employRoleEntity);
        }
    }


    @Override
    public void addEmployRoles(Integer employId, String roleIds) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<RoleEntity> roleEntityList = new ArrayList<>();

        List<Integer> roleIdList = StringUtil.split2Int(roleIds, SPLIT_SIGN_DH);

        roleEntityList = roleService.queryByIdList(roleIdList);

        for (RoleEntity roleEntity : roleEntityList) {
            EmployRoleEntity employRoleEntity = new EmployRoleEntity()
                    .setEmployId(employId)
                    .setRoleId(roleEntity.getId())
                    .setRoleDefId(roleEntity.getRoleDefId())
                    .setIsDeleted(1)
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);
            employRoleDao.save(employRoleEntity);
        }

    }

    @Override
    public void addEmployRoles(Integer employId, Integer roleDefId, Integer categoryId, String customerIds) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<RoleEntity> roleEntityList = new ArrayList<>();

        List<Integer> customerIdList = StringUtil.split2Int(customerIds, SPLIT_SIGN_DH);

        for (Integer customerId : customerIdList) {
            List<RoleEntity> list = roleService.queryRoleListByDefInfo(roleDefId, categoryId, customerId);
            roleEntityList.addAll(list);
        }

        for (RoleEntity roleEntity : roleEntityList) {
            EmployRoleEntity employRoleEntity = new EmployRoleEntity()
                    .setEmployId(employId)
                    .setRoleId(roleEntity.getId())
                    .setRoleDefId(roleEntity.getRoleDefId())
                    .setIsDeleted(1)
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);
            employRoleDao.save(employRoleEntity);
        }
    }

    @Override
    public void deleteEmployRole(EmployRoleDTO employRoleDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());

        if (CollectionUtils.isEmpty(employRoleDTO.getEmployIdList())) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }
        for (Integer employId : employRoleDTO.getEmployIdList()) {
            EmployRoleEntity employRoleEntity = new EmployRoleEntity();
            employRoleEntity
                    .setEmployId(employId)
                    .setRoleDefId(employRoleDTO.getRoleDefId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            employRoleDao.delete(employRoleEntity);
        }
    }

    @Override
    public List<EmployRoleEntity> queryByRoleDefId(Integer roleDefId) {
        List<EmployRoleEntity> employRoleEntities = employRoleDao.queryByRoleDefId(roleDefId);
        return employRoleEntities;
    }

    @Override
    public void save(EmployRoleEntity employRoleEntity) {
        employRoleDao.save(employRoleEntity);
    }

    @Override
    public void deleteByEmployId(Integer employId) {
        employRoleDao.deleteByEmployId(employId);
    }

    @Override
    public List<EmployRoleEntity> getEmployRoleListByRoleIds(List<Integer> roleIdList) {
        return employRoleDao.getEmployRolesByRoleIds(roleIdList);
    }

    @Override
    public List<EmployRoleEntity> queryAll() {
        return employRoleDao.list();

    }

    @Override
    public List<Integer> queryRoleIdList(EmployRoleQO condition) {
        return employRoleDao.queryRoleIdListByEmployId(condition);
    }

    @Override
    public void updateDeletedByRoleIdAndEmployId(Integer roleId, Integer employId) {
        employRoleDao.updateDeletedByRoleIdAndEmployId(roleId, employId);
    }

    @Override
    public void updateDeletedByIds(List<Integer> ids) {
        employRoleDao.updateDeletedByIds(ids);
    }

    @Override
    public List<EmployRoleEntity> getAllEmployRoleList() {
        return employRoleDao.list(Wrappers.<EmployRoleEntity>lambdaQuery()
                .eq(EmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

}
