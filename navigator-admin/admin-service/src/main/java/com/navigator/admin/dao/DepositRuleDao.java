package com.navigator.admin.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.DepositRuleMapper;
import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.DepositRuleEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/21
 */
@Dao
public class DepositRuleDao extends BaseDaoImpl<DepositRuleMapper, DepositRuleEntity> {
    public DepositRuleEntity findDepositRule(DepositRuleDTO depositRuleDTO) {
        DepositRuleEntity depositRuleEntity = null;
        List<DepositRuleEntity> ruleEntityList = this.list(Wrappers.<DepositRuleEntity>lambdaQuery()
                .eq(null != depositRuleDTO.getCategoryId(), DepositRuleEntity::getCategoryId, depositRuleDTO.getCategoryId())
                .eq(null != depositRuleDTO.getRuleType(), DepositRuleEntity::getContractScene, depositRuleDTO.getRuleType())
                .eq(null != depositRuleDTO.getContractType(), DepositRuleEntity::getContractType, depositRuleDTO.getContractType()));

        // 当customerId 为0时针对所有的客户
        if (CollectionUtil.isNotEmpty(ruleEntityList)) {
            for (DepositRuleEntity ruleEntity : ruleEntityList) {
                if (ruleEntity.getCustomerId().equals(depositRuleDTO.getCustomerId()) || ruleEntity.getCustomerId() == 0) {
                    depositRuleEntity = ruleEntity;
                }
            }
        }
        return depositRuleEntity;
    }
}
