package com.navigator.admin.service.systemrule.impl;

import com.navigator.admin.dao.BasicPriceGoodsAttributeConfigDao;
import com.navigator.admin.pojo.entity.BasicPriceGoodsAttributeConfigEntity;
import com.navigator.admin.service.systemrule.BasicPriceGoodsAttributeConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17
 */
@Service
public class BasicPriceGoodsAttributeConfigServiceImpl implements BasicPriceGoodsAttributeConfigService {

    @Resource
    private BasicPriceGoodsAttributeConfigDao basicPriceGoodsAttributeConfigDao;


    @Override
    public List<BasicPriceGoodsAttributeConfigEntity> queryBasicPriceGoodsAttributeConfigByAttributeValueId(Integer attributeValueId) {
        return basicPriceGoodsAttributeConfigDao.queryBasicPriceGoodsAttributeConfigByAttributeValueId(attributeValueId);
    }
}
