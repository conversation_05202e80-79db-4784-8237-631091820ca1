package com.navigator.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.TemplateDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.dto.TemplateTestImportDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.admin.pojo.vo.TemplateExportVO;
import com.navigator.admin.service.ITemplateAttributeService;
import com.navigator.admin.service.ITemplateService;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.TemplateTypeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.pojo.dto.contractsign.SignTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class TemplateServiceImpl implements ITemplateService {


    @Autowired
    private ITemplateAttributeService iTemplateAttributeService;
    @Autowired
    private TemplateDao templateDao;

    @Autowired
    private EmployFacade employFacade;


    @Override
    public TemplateEntity getTemplateByCodeAndType(String code, Integer type) {
        return templateDao.getTemplateByCodeAndType(code, type);
    }

    @Override
    public String jointOperationLogTemplate(Map map, String code) {
        TemplateEntity templateEntity = this.getTemplateByCodeAndType(code, TemplateTypeEnum.LOG_TEMPLATE.getValue());
        if (null == templateEntity) {
            return "";
        }
        // 填充模板
        return TemplateRenderUtil.templateRender(map, templateEntity.getContent());
    }

    @Override
    public String getTemplateInfo(QueryTemplateAttributeDTO queryTemplateAttributeDTO) {
        TemplateAttributeEntity templateAttributeEntity = iTemplateAttributeService.queryTemplateAttribute(queryTemplateAttributeDTO);
        return this.assembleTemplateInfo(null != templateAttributeEntity ? templateAttributeEntity.getTemplateId() : TemplateConstant.DEFAULT_TEMPLATE_ID);
    }

    @Override
    public Result importETemplateInfo(MultipartFile uploadFile) {
        try {
            List<TemplateTestImportDTO> testImportDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, TemplateTestImportDTO.class);
            Integer result = 0;
            log.info(FastJsonUtils.getBeanToJson(testImportDTOList));
            if (!CollectionUtils.isEmpty(testImportDTOList)) {
                Map<String, Object> beanToMap = BeanUtil.beanToMap(new SignTemplateDTO());
                for (TemplateTestImportDTO testImportDTO : testImportDTOList) {
                    TemplateEntity templateEntity = templateDao.getTemplateByCodeAndType(testImportDTO.getCode().trim(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
                    if (null == templateEntity) {
                        log.info("======================新增" + testImportDTO.getCode() + testImportDTO.getTitle() + "处理");
                        String content = StringUtils.isNotBlank(testImportDTO.getContent()) ? testImportDTO.getContent() : "";
                        if (StringUtils.isNotBlank(content)) {
                            for (String key : beanToMap.keySet()) {
                                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toUpperCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
                                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toLowerCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
                            }
                        }
                        log.info(testImportDTO.getCode() + testImportDTO.getTitle() + ":" + content);
                        TemplateEntity eTemplateEntity = new TemplateEntity()
                                .setCode(testImportDTO.getCode().trim())
                                .setName(testImportDTO.getTitle().trim())
                                .setType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
                                .setContent(content)
                                .setSubTemplateIds("");
                        templateDao.save(eTemplateEntity);
                        result++;
                    } else {
                        log.info("======================更新" + testImportDTO.getCode() + testImportDTO.getTitle() + "处理");
                        String content = StringUtils.isNotBlank(testImportDTO.getContent()) ? testImportDTO.getContent() : "";
                        if (StringUtils.isNotBlank(content)) {
                            for (String key : beanToMap.keySet()) {
                                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toUpperCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
                                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toLowerCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
                            }
                        }
                        log.info(testImportDTO.getCode() + testImportDTO.getTitle() + ":" + content);
                        templateEntity
                                .setCode(testImportDTO.getCode().trim())
                                .setName(testImportDTO.getTitle().trim())
                                .setType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
                                .setContent(content)
                                .setSubTemplateIds("");
                        templateDao.updateById(templateEntity);
                        result++;
                    }
                }
            }
            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
    }

    @Override
    public Result importMTermAndE(MultipartFile uploadFile) {
        try {
            List<TemplateTestImportDTO> testImportDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, TemplateTestImportDTO.class);
            Integer result = 0;
            log.info(FastJsonUtils.getBeanToJson(testImportDTOList));
            if (!CollectionUtils.isEmpty(testImportDTOList)) {
                for (TemplateTestImportDTO testImportDTO : testImportDTOList) {
                    TemplateEntity templateEntity = templateDao.getTemplateByCodeAndType(testImportDTO.getCode().trim(), testImportDTO.getType());
                    if (null == templateEntity) {
                        log.info("======================新增" + testImportDTO.getCode() + testImportDTO.getTitle() + "处理");
                        String content = StringUtils.isNotBlank(testImportDTO.getContent()) ? testImportDTO.getContent() : "";
                        log.info(testImportDTO.getCode() + testImportDTO.getTitle() + ":" + content);
                        TemplateEntity eTemplateEntity = new TemplateEntity()
                                .setCode(testImportDTO.getCode().trim())
                                .setName(testImportDTO.getTitle().trim())
                                .setType(testImportDTO.getType())
                                .setContent(content)
                                .setSubTemplateIds(StringUtils.isNotBlank(testImportDTO.getSubTemplateIds()) ? testImportDTO.getSubTemplateIds().trim() : "");
                        templateDao.save(eTemplateEntity);
                        result++;
                    } else {
                        log.info("======================更新" + testImportDTO.getCode() + testImportDTO.getTitle() + "处理");
                        String content = StringUtils.isNotBlank(testImportDTO.getContent()) ? testImportDTO.getContent() : "";
                        log.info(testImportDTO.getCode() + testImportDTO.getTitle() + ":" + content);
                        templateEntity
                                .setCode(testImportDTO.getCode().trim())
                                .setName(testImportDTO.getTitle().trim())
                                .setType(testImportDTO.getType())
                                .setContent(content)
                                .setSubTemplateIds(StringUtils.isNotBlank(testImportDTO.getSubTemplateIds()) ? testImportDTO.getSubTemplateIds().trim() : "");
                        templateDao.updateById(templateEntity);
                        result++;
                    }
                }
            }
            return Result.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
    }

    @Override
    public Result exportTemplate(Integer status, HttpServletResponse response) {
        List<TemplateEntity> templateEntityList = templateDao.findAllTemplate(TemplateTypeEnum.getTemplateTypeList(), status);
        templateEntityList.sort(Comparator.comparing(TemplateEntity::getType).thenComparing(TemplateEntity::getId));
        String fileName = "合同模版信息" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(templateEntityList, "合同模版信息", "合同模版信息", TemplateEntity.class, fileName, response);
        return Result.success(templateEntityList);
    }

    @Override
    public Result exportTemplateInfo(HttpServletResponse response) {
        List<TemplateEntity> templateEntityList = templateDao.findAllTemplate(Arrays.asList(TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue()), DisableStatusEnum.ENABLE.getValue());
        List<TemplateExportVO> templateExportVOList = new ArrayList<>();
        for (TemplateEntity ContractTemplateEntity : templateEntityList) {
            List<String> mTemplateIds = Arrays.asList(ContractTemplateEntity.getSubTemplateIds().split(","));
            for (String mTemplateId : mTemplateIds) {
                TemplateEntity mTemplateEntity = this.templateDao.getById(Integer.valueOf(mTemplateId.trim()));
                if (null != mTemplateEntity) {
                    //M条款拼接E模版信息
                    if (StringUtils.isNotBlank(mTemplateEntity.getSubTemplateIds())) {
                        List<String> eTemplateIds = Arrays.asList(mTemplateEntity.getSubTemplateIds().split(","));
                        for (String eTemplateId : eTemplateIds) {
                            TemplateEntity eTemplateEntity = this.templateDao.getById(Integer.valueOf(eTemplateId));
                            TemplateExportVO templateExportVO = new TemplateExportVO()
                                    .setContractTemplateId(ContractTemplateEntity.getId())
                                    .setContractTemplateCode(ContractTemplateEntity.getCode())
                                    .setContractTemplateName(ContractTemplateEntity.getName())
                                    .setMmTemplateId(mTemplateEntity.getId())
                                    .setMmTemplateCode(mTemplateEntity.getCode())
                                    .setMmTemplateName(mTemplateEntity.getName())
                                    .setMmTemplateContent(mTemplateEntity.getContent())
                                    .setEeTemplateId(eTemplateEntity.getId())
                                    .setEeTemplateCode(eTemplateEntity.getCode())
                                    .setEeTemplateName(eTemplateEntity.getName())
                                    .setEeTemplateContent(eTemplateEntity.getContent());
                            templateExportVOList.add(templateExportVO);
                        }
                    }
                }
            }
        }
        String fileName = "合同模版信息" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(templateExportVOList, "合同模版信息", "合同模版信息", TemplateExportVO.class, fileName, response);
        return Result.success(templateExportVOList);
    }

    @Override
    public Result queryTemplate(QueryDTO<QueryTemplateDTO> queryDTO) {
        IPage<TemplateEntity> templateEntityIPage = templateDao.queryTemplate(queryDTO);
        return Result.page(templateEntityIPage);
    }

    @Override
    public TemplateEntity getLoginSignature(Integer value) {
        return templateDao.getLoginSignature(value);
    }

    @Override
    public Result syncTemplateCodeInfo(Integer type) {
        List<TemplateEntity> templateEntityList = templateDao.findAllTemplate(Arrays.asList(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue(), TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue()), DisableStatusEnum.ENABLE.getValue());
        if (!CollectionUtils.isEmpty(templateEntityList)) {
            for (TemplateEntity templateEntity : templateEntityList) {
                if (StringUtils.isNotBlank(templateEntity.getSubTemplateIds())) {
                    List<String> templateIdInfo = Arrays.asList(templateEntity.getSubTemplateIds().split(","));
                    List<Integer> templateIds = templateIdInfo.stream().map(Integer::valueOf).collect(Collectors.toList());
                    List<TemplateEntity> subTemplateEntityList = templateDao.getTemplateListByIds(templateIds);
                    String codeLists = subTemplateEntityList.stream().map(TemplateEntity::getCode).collect(Collectors.joining(","));
                    templateEntity.setSubCodeList(codeLists)
                            .setUpdatedAt(new Date());
                    templateDao.updateById(templateEntity);
                }
            }
        }
//        List<TemplateEntity> diffTemplateEntityList = new ArrayList<>();
//        List<TemplateEntity> templateList = templateDao.findAllTemplate(Arrays.asList(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue(), TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue()), DisableStatusEnum.ENABLE.getValue());
//        for (TemplateEntity templateEntity : templateList) {
//
//        }
        return Result.success("同步成功");
    }

    @Override
    public TemplateEntity saveOrUpdateTemplate(TemplateEntity templateEntity) {
        Integer templateId = templateEntity.getId();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        Map<String, Object> beanToMap = BeanUtil.beanToMap(new SignTemplateDTO());
        String content = StringUtils.isNotBlank(templateEntity.getContent()) ? templateEntity.getContent() : "";
        if (StringUtils.isNotBlank(content) && TemplateTypeEnum.ORIGIN_TEMPLATE.getValue().equals(templateEntity.getType())) {
            for (String key : beanToMap.keySet()) {
                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toUpperCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
                content = content.replaceAll(java.util.regex.Matcher.quoteReplacement("#" + key.toLowerCase() + "#"), java.util.regex.Matcher.quoteReplacement("${" + key.toLowerCase() + "!}"));
            }
        }
        String subTemplateIds = "";
        if (StringUtils.isNotBlank(templateEntity.getSubCodeList())) {
            List<String> codeList = Arrays.asList(templateEntity.getSubCodeList().split(","));
            List<TemplateEntity> templateEntities = templateDao.getTemplateListByCodes(codeList);
            subTemplateIds = templateEntities.stream().map(TemplateEntity::getId).map(String::valueOf).collect(Collectors.joining(","));
            templateEntity.setSubTemplateIds(subTemplateIds);
        }
        if (null == templateId) {
            templateEntity.setContent(content).setType(null != templateEntity.getType() ? templateEntity.getType() : TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
            if (StringUtils.isNotBlank(subTemplateIds)) {
                templateEntity.setSubTemplateIds(subTemplateIds)
                        .setSubCodeList(templateEntity.getSubCodeList());
            }
            templateEntity
                    .setCreatedBy(userId)
                    .setCreatedByName(name)
                    .setCreatedAt(new Date())
                    .setUpdatedBy(userId)
                    .setUpdatedByName(name)
                    .setUpdatedAt(new Date());
            templateDao.save(templateEntity);
            return templateEntity;
        } else {
            TemplateEntity templateInfoEntity = templateDao.getById(templateId);
            if (null != templateInfoEntity) {
                templateInfoEntity.setCode(templateEntity.getCode())
                        .setName(templateEntity.getName().trim())
                        .setSubTemplateIds(templateEntity.getSubTemplateIds())
                        .setContent(!StringUtils.isBlank(content) ? content : templateInfoEntity.getContent())
                        .setUpdatedBy(userId)
                        .setUpdatedByName(name)
                        .setUpdatedAt(new Date())
                        .setType(templateEntity.getType());
                if (StringUtils.isNotBlank(subTemplateIds)) {
                    templateInfoEntity.setSubTemplateIds(subTemplateIds).setSubCodeList(templateEntity.getSubCodeList());
                }
                templateDao.updateById(templateInfoEntity);
            }
            return templateInfoEntity;
        }
    }


    /**
     * 将该父模板所有的子模板组合到其中
     * 1、组装M条款->组装E模版
     *
     * @param templateId 模版ID
     * @return 拼接的协议模版结果
     */
    private String assembleTemplateInfo(Integer templateId) {
        TemplateEntity templateEntity = this.templateDao.getById(templateId);
        if (null == templateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        String templateContentResult = templateEntity.getContent();
        if (StringUtils.isNotBlank(templateContentResult) && StringUtils.isNotBlank(templateEntity.getSubTemplateIds())) {
            List<String> mTemplateIds = Arrays.asList(templateEntity.getSubTemplateIds().split(","));
            for (String mTemplateId : mTemplateIds) {
                TemplateEntity mTemplateEntity = this.templateDao.getById(Integer.valueOf(mTemplateId.trim()));
                if (null != mTemplateEntity) {
                    //M条款拼接E模版信息
                    String mTemplateContent = getMTemplateContent(mTemplateEntity);
                    templateContentResult = templateContentResult.replace("#" + mTemplateEntity.getCode() + "#", "<!--" + mTemplateEntity.getCode() + "-->" + mTemplateContent);
                }
            }
        }
        return templateContentResult;
    }

    /**
     * 拼接M条款（--E模版内容）
     *
     * @param mTemplateEntity M模版条款信息
     * @return M模版内容-替换E模板结果
     */
    private String getMTemplateContent(TemplateEntity mTemplateEntity) {
        if (null == mTemplateEntity) {
            return "";
        }
        String mTemplateContent = mTemplateEntity.getContent();
        if (StringUtils.isNotBlank(mTemplateEntity.getSubTemplateIds())) {
            List<String> eTemplateIds = Arrays.asList(mTemplateEntity.getSubTemplateIds().split(","));
            for (String eTemplateId : eTemplateIds) {
                TemplateEntity eTemplateEntity = this.templateDao.getById(Integer.valueOf(eTemplateId));
                if (null != eTemplateEntity && TemplateTypeEnum.ORIGIN_TEMPLATE.getValue().equals(eTemplateEntity.getType())) {
                    mTemplateContent = mTemplateContent.replace("#" + eTemplateEntity.getCode() + "#", "<!--" + eTemplateEntity.getCode() + "-->" + eTemplateEntity.getContent());
                }
            }
        }
        return mTemplateContent;
    }
}
