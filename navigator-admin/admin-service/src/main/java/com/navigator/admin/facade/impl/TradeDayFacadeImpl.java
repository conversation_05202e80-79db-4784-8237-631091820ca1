package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.TradeDayFacade;
import com.navigator.admin.pojo.dto.TradeDayCycleDTO;
import com.navigator.admin.pojo.dto.systemrule.TradeDayDTO;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.admin.service.ITradeDayService;
import com.navigator.common.dto.Result;
import com.navigator.common.util.time.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/21 10:55
 */
@RestController
public class TradeDayFacadeImpl implements TradeDayFacade {

    @Autowired
    private ITradeDayService tradeDayService;

    @Override
    public TradeDayEntity getTradeDayByDay(String day) {
        TradeDayEntity tradeDayByDay = tradeDayService.getTradeDayByDay(day);
        return tradeDayByDay;
    }

    @Override
    public Result importTradeDay(List<TradeDayEntity> tradeDayList) {
        return tradeDayService.importTradeDay(tradeDayList);
    }

    @Override
    public Result importNextYearDays(Integer year) {
        return tradeDayService.importNextYearDays(year);
    }

    @Override
    public int getTradeDays(String startDay, String endDay) {
        return tradeDayService.getTradeDays(DateTimeUtil.parseDateString(startDay), DateTimeUtil.parseDateString(endDay));
    }

    @Override
    public boolean isTradeDay(String date) {
        return tradeDayService.isTradeDay(date);
    }

    @Override
    public boolean isTradeDayValue(String date) {
        return tradeDayService.isTradeDayValue(date);
    }

    @Override
    public List<TradeDayEntity> getTradeDayByMonth(String month) {
        return tradeDayService.getTradeDayByMonth(month);
    }

    @Override
    public List<TradeDayEntity> getTradeDayByYear(Integer year) {
        return tradeDayService.getTradeDayByYear(year);
    }

    @Override
    public List<String> setNotTrade(List<TradeDayDTO> tradeDayDTOList) {
        return tradeDayService.setNotTrade(tradeDayDTOList);
    }

    @Override
    public TradeDayEntity getTradeDayByDayAgo(String dayValue) {
        return tradeDayService.getTradeDayByDayAgo(dayValue);
    }

    @Override
    public Result<TradeDayCycleDTO> getTradeDayCycleDTO(String futureCode, String dayValue) {
        return Result.success(tradeDayService.getTradeDayCycleDTO(futureCode, dayValue));
    }
}
