package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

/**
 * <p>
 * 模版属性表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface ITemplateAttributeService {

    /**
     * 分页查询 TemplateAttributeEntity
     *
     * @param queryDTO
     * @return
     */
    Result queryTemplateAttributeList(QueryDTO<TemplateAttributeEntity> queryDTO);

    /**
     * 根据查询条件获取 TemplateAttributeEntity
     *
     * @param queryTemplateAttributeDTO
     * @return
     */
    TemplateAttributeEntity queryTemplateAttribute(QueryTemplateAttributeDTO queryTemplateAttributeDTO);

}
