package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.TodoListFacade;
import com.navigator.admin.pojo.dto.QueryTodoDTO;
import com.navigator.admin.service.ITodoListService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/todo")
public class TodoListFacadeImpl implements TodoListFacade {
    @Autowired
    private ITodoListService iTodoListService;

    @Override
    @PostMapping("/queryTodoList")
    public Result queryTodoList(@RequestBody QueryDTO<QueryTodoDTO> queryDTO) {
        String userId = JwtUtils.getCurrentUserId();
        queryDTO.getCondition().setUserId(userId);
        return iTodoListService.queryTodoList(queryDTO);
    }
}
