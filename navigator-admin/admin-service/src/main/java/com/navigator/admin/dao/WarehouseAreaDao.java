package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.WarehouseAreaMapper;
import com.navigator.admin.pojo.entity.WarehouseAreaEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class WarehouseAreaDao extends BaseDaoImpl<WarehouseAreaMapper, WarehouseAreaEntity> {

    public WarehouseAreaEntity getByName(String name) {
        List<WarehouseAreaEntity> list = this.list(Wrappers.<WarehouseAreaEntity>lambdaQuery()
                .eq(WarehouseAreaEntity::getName, name)
                .eq(WarehouseAreaEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return list.isEmpty() ? null : list.get(0);
    }
}
