package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IDictService {
    Result queryByCondition(QueryDTO<DictItemEntity> queryDTO);

    List<DictItemEntity> queryExportVipCustomerList(DictItemEntity dictItemQO);

    List<DictItemEntity> getDictItemById(List<Integer> dictItemIdList);

    List<DictItemEntity> getItemByDictCode(String dictCode);

    DictItemEntity getDictItemByCode(String dictCode, String itemCode, Integer itemValue);

    Result saveDictItem(DictItemEntity dictItemEntity);

    Result updateDictItem(DictItemEntity dictItemEntity);
}
