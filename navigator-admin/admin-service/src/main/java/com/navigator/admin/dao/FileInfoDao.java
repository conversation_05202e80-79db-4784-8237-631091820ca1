package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.FileInfoMapper;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 11:24
 */
@Dao
public class FileInfoDao extends BaseDaoImpl<FileInfoMapper, FileInfoEntity> {

    /**
     * 根据ID获取文件信息
     *
     * @param fileIdList 文件ID集合
     * @return 文件基础信息
     */
    public List<FileInfoEntity> getFileListByIds(List<Integer> fileIdList) {
        return this.list(Wrappers.<FileInfoEntity>lambdaQuery()
                .in(FileInfoEntity::getId, fileIdList)
                .eq(FileInfoEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
