package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.MenuMapper;
import com.navigator.admin.pojo.entity.MenuEntity;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;


/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 14:29
 */
@Dao
public class MenuDao extends BaseDaoImpl<MenuMapper, MenuEntity> {

    public List<MenuEntity> queryMenu(Integer system) {
        return this.baseMapper.selectList(
                Wrappers.<MenuEntity>lambdaQuery()
                        .eq(0 != system, MenuEntity::getSystem, system)
                        .eq(MenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                        .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public MenuEntity getMenuById(String id) {
        return this.getOne(new LambdaQueryWrapper<MenuEntity>()
                .eq(MenuEntity::getId, id)
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(MenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    // 寻找menu中ParentId
    public Integer getMenuParentId(Integer menuId) {
        MenuEntity menuEntity = this.getOne(new LambdaQueryWrapper<MenuEntity>().eq(MenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(MenuEntity::getId, menuId));
        return menuEntity.getParentId();
    }


    public List<MenuEntity> getMenuByCategoryId(String categoryId, String systemId, List<Integer> menuIdList) {
        LambdaQueryWrapper<MenuEntity> wrapper = Wrappers.<MenuEntity>lambdaQuery()
                .in(MenuEntity::getCategoryId, Arrays.asList(0, categoryId));

        wrapper.and(i -> i.eq(StringUtils.isNotBlank(systemId), MenuEntity::getSystem, systemId)
                .eq(StringUtils.isBlank(systemId), MenuEntity::getSystem, 1)
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(CollectionUtils.isNotEmpty(menuIdList), MenuEntity::getId, menuIdList));

        return list(wrapper);
    }

    public List<MenuEntity> getMenuParentMenu() {
        return list(Wrappers.<MenuEntity>lambdaQuery()
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(MenuEntity::getParentId, 0));
    }

    public MenuEntity getMenuByParentId(Integer parentId) {
        List<MenuEntity> menuEntityList = list(Wrappers.<MenuEntity>lambdaQuery()
                .eq(MenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(MenuEntity::getParentId, parentId));
        return CollectionUtils.isNotEmpty(menuEntityList) ? menuEntityList.get(0) : null;
    }

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    public List<MenuEntity> queryMenuList(MenuQO condition) {
        return this.list(MenuEntity.lqw(condition));
    }
}
