package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.UserMenuCollectVO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:40
 **/
public interface UserMenuCollectService {
    /**
     * 保存用户收藏的菜单
     *
     * @param userMenuCollect
     * @return
     */
    UserMenuCollectEntity saveUserMenuCollect(UserMenuCollectEntity userMenuCollect);

    /**
     * 修改用户收藏的菜单名称
     *
     * @param userMenuCollect
     * @return
     */
    UserMenuCollectEntity updateUserMenuCollect(UserMenuCollectEntity userMenuCollect);

    /**
     * 根据ID删除收藏夹信息
     *
     * @param id
     * @return
     */
    Result deleteCollectMenu(Integer id);

    /**
     * 排序收藏的菜单
     *
     * @param menuCollectIdList
     * @return
     */
    Result sortCollectMenu(List<Integer> menuCollectIdList);

    /**
     * 查询用户的所有有权限的二级菜单列表
     *
     * @param categoryId 分类
     * @param customerId Columbus区分主体
     * @param system     系统
     * @return
     */
    List<MenuDetailVO> getUserPowerMenuList(Integer categoryId, Integer customerId, Integer system);

    /**
     * 查询用户收藏的收藏夹
     *
     * @param categoryId
     * @param customerId Columbus区分主体
     * @param system
     * @return
     */
    List<UserMenuCollectVO> getUserCollectMenuList(Integer categoryId, Integer customerId,Integer system);

}
