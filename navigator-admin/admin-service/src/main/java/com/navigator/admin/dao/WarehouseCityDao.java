package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.WarehouseCityMapper;
import com.navigator.admin.pojo.entity.WarehouseCityEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class WarehouseCityDao extends BaseDaoImpl<WarehouseCityMapper, WarehouseCityEntity> {

    public WarehouseCityEntity getByAreaNameAndCityName(String geoRegion, String geoCity) {
        List<WarehouseCityEntity> list = this.list(Wrappers.<WarehouseCityEntity>lambdaQuery()
                .eq(WarehouseCityEntity::getName, geoCity)
                .eq(WarehouseCityEntity::getGeographicArea, geoRegion)
                .eq(WarehouseCityEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return list.isEmpty() ? null : list.get(0);
    }
}
