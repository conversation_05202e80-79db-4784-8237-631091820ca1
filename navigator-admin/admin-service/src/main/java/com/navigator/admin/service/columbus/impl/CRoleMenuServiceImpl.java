package com.navigator.admin.service.columbus.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.columbus.CRoleMenuDao;
import com.navigator.admin.pojo.entity.CRoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.columbus.ICRoleMenuService;
import com.navigator.common.enums.IsDeletedEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class CRoleMenuServiceImpl implements ICRoleMenuService {

    @Autowired
    private CRoleMenuDao roleMenuDao;
    @Autowired
    private ICEmployService cEmployService;

    @Override
    public List<CRoleMenuEntity> findRoleMenusByRoleId(String roleId) {
        return roleMenuDao.findRoleMenusByRoleId(roleId);
    }

    @Override
    public List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList, Integer customerId) {
        return roleMenuDao.getMenuIdListByRoleIdList(roleIdList, customerId);
    }

    @Override
    public void saveRoleMenu(CRoleMenuEntity roleMenuEntity) {
        roleMenuDao.save(roleMenuEntity);
    }

    @Override
    public void deleteRoleMenu(String roleId, Integer customerId) {
        roleMenuDao.deleteRoleMenu(roleId, customerId);
    }

    @Override
    public List<CRoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList) {
        return roleMenuDao.getMenuIdListByRoleIdList(roleIdList);
    }

    @Override
    public void deleteRoleMenu(String roleId) {
        roleMenuDao.deleteRoleMenu(roleId);
    }

    @Override
    public void copyRoleMenu(Integer sourceRoleId, Integer targetRoleId) {
        List<CRoleMenuEntity> roleMenuEntities = roleMenuDao.findRoleMenusByRoleId(String.valueOf(sourceRoleId));
        roleMenuDao.deleteRoleMenu(String.valueOf(targetRoleId));
        for (CRoleMenuEntity roleMenuEntity : roleMenuEntities) {
            roleMenuEntity
                    .setId(null)
                    .setRoleId(targetRoleId)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
            ;
            roleMenuDao.save(roleMenuEntity);
        }
    }

    @Override
    public Set<Integer> queryMenuIdList(RoleMenuQO condition) {
        return roleMenuDao.queryMenuIdList(condition);
    }

    @Override
    public List<CRoleMenuEntity> getAllRoleMenuList() {
        return roleMenuDao.list(Wrappers.<CRoleMenuEntity>lambdaQuery()
                .eq(CRoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    @Override
    public void updateDeletedByMenuIdAndRoleId(Integer menuId, Integer roleId) {
        roleMenuDao.update(Wrappers.<CRoleMenuEntity>lambdaUpdate()
                .eq(CRoleMenuEntity::getMenuId, menuId)
                .eq(CRoleMenuEntity::getRoleId, roleId)
                .set(CRoleMenuEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));
    }

}
