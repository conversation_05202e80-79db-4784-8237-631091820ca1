package com.navigator.admin.service.approval.impl;

import com.navigator.admin.dao.approval.CategoryApprovalModelDao;
import com.navigator.admin.pojo.entity.approval.CategoryApprovalModelEntity;
import com.navigator.admin.service.approval.CategoryApprovalModelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
@Service
public class CategoryApprovalModelServiceImpl implements CategoryApprovalModelService {

    @Resource
    private CategoryApprovalModelDao categoryApprovalModelDao;

    @Override
    public CategoryApprovalModelEntity queryCategoryApprovalModel(String category2) {

        List<CategoryApprovalModelEntity> categoryApprovalModelEntities = categoryApprovalModelDao.queryCategoryApprovalModel(category2);

        return categoryApprovalModelEntities.isEmpty() ? null : categoryApprovalModelEntities.get(0);
    }

    @Override
    public String queryCategoryApprovalModelKeyByCategory2(String category2) {
        List<CategoryApprovalModelEntity> categoryApprovalModelEntities = categoryApprovalModelDao.queryCategoryApprovalModel(category2);

        return categoryApprovalModelEntities.isEmpty() ? "START_APPROVAL" : categoryApprovalModelEntities.get(0).getModelKey().toString();
    }
}
