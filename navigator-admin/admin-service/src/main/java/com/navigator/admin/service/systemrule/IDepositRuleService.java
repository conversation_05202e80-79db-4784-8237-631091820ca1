package com.navigator.admin.service.systemrule;


import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.entity.DepositRuleEntity;

import java.math.BigDecimal;

/**
 * <p>
 * 保证金规则 service
 * </p>
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
public interface IDepositRuleService {
    /**
     * 创建保证金使用规则
     *
     * @param depositRuleEntity
     * @return
     */
    boolean createDepositRule(DepositRuleEntity depositRuleEntity);

    /**
     * 根据条件查询保证金比例
     *
     * @param depositRuleDTO
     * @return
     */
    DepositRuleEntity findDepositRule(DepositRuleDTO depositRuleDTO);

    /**
     * 保证金的使用
     *
     * @param depositRuleDTO
     * @return
     */
    BigDecimal calcContractUseDeposit(DepositRuleDTO depositRuleDTO);

}
