package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.MemoFacade;
import com.navigator.admin.mapper.MemoMapper;
import com.navigator.admin.pojo.entity.MemoEntity;
import com.navigator.admin.service.IMemoService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/3 10:25
 */

@RestController
public class MemoFacadeImpl implements MemoFacade {
    @Resource
    private IMemoService iMemoService;
    @Resource
    private MemoMapper mapper;

    /**
     * 查看备忘录
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryMemo(@RequestBody QueryDTO<MemoEntity> queryDTO) {
        return iMemoService.queryMemo(queryDTO);
    }

    /**
     * 添加备忘录
     * @param memo
     * @return
     */
    @Override
    public Result saveMemo(@RequestBody MemoEntity memo) {
        return iMemoService.saveMemo(memo);
    }

    /**
     * 修改备忘录
     * @param memo
     * @return
     */
    @Override
    public Result updateMemo(@RequestBody MemoEntity memo) {
        return Result.success(mapper.updateById(memo));
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @Override
    public Result memoById(@RequestParam(value = "id") Integer id) {
        return Result.success(mapper.selectById(id));
    }

}
