package com.navigator.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.entity.SequenceEntity;
import com.navigator.common.enums.SequenceEnum;

/**
 * <p>
 * 序列号 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
public interface ISequenceService extends IService<SequenceEntity> {

    /**
     * 生成指定长度的简单序列号，如 C001
     *
     * @param sequenceEnum 序列号类型
     * @param length   顺序号长度
     * @return
     */
    String generate(SequenceEnum sequenceEnum, int length);

    /**
     * 按日期生成的序列号
     * CAD-6位日期-3位顺序号，例如：CAD230712001
     *
     * @param sequenceEnum
     * @return
     */
    String generateByDay(SequenceEnum sequenceEnum);

    /**
     * 按日期生成的序列号
     * CAD-6位日期-5位顺序号，例如：CAD23071200001
     *
     * @param sequenceEnum
     * @param length
     * @return
     */
    String generateByDayLength(SequenceEnum sequenceEnum, int length);

    /**
     * 仓单注册号 "交易所代码+品种+注册年月+三位递增码 例如：DCEM2401001"
     *
     * @param exchange
     * @param category
     * @return
     */
    String generateWarrantCode(String exchange, String category);

    /**
     * 仓单销售合同号	"交易所+品种+销售+分配年份+四位递增码 例如：DCEMS240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    String generateWarrantSalesContractCode(String exchange, String category);

    /**
     * 仓单销售子合同号	"原合同号+-+3位递增码 例如：DCEMS240001-001"
     *
     * @param warrantSalesContractCode
     * @return
     */
    String generateWarrantSalesChildContractCode(String warrantSalesContractCode);

    /**
     * 仓单采购合同号	"交易所+品种+采购+分配年份+四位递增码 例如：DCEMP240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    String generateWarrantPurchaseContractCode(String exchange, String category);

    /**
     * 仓单采购子合同号 "原合同号+-+3位递增码 例如：DCEMP240001-001"
     *
     * @param warrantPurchaseContractCode
     * @return
     */
    String generateWarrantPurchaseChildContractCode(String warrantPurchaseContractCode);

    /**
     * 仓单合同TT编号 "按照现货规则： SC+年月日+时分秒+四位随机码 SC202408081833001234"
     *
     * @return
     */
    String generateWarrantContractTTCode();

    /**
     * 仓单合同协议编号	按照现货规则：同一个合同的协议编号从000开始递增，3位递增码
     *
     * @return
     */
    String generateWarrantContractSignCode();


    /**
     * 仓单合同-提货权	"原合同号+-+T+3位递增码 例如：DCEMS240001-T001"
     *
     * @param contractCode
     * @return
     */
    String generateWarrantContractCargoRightsCode(String contractCode);


    /**
     * 现货销售合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    String generateSpotSalesContractCode(String supplier, String category);

    /**
     * 现货销售子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotSalesContractCode
     * @return
     */
    String generateSpotSalesChildContractCode(String spotSalesContractCode);

    /**
     * 现货采购合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    String generateSpotPurchaseContractCode(String supplier, String category);

    /**
     * 现货采购子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotPurchaseContractCode
     * @return
     */
    String generateSpotPurchaseChildContractCode(String spotPurchaseContractCode);
}
