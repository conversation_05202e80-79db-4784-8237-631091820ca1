package com.navigator.admin.service.approval.impl;

import com.navigator.admin.dao.approval.LoaApprovalRuleDao;
import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;
import com.navigator.admin.service.approval.LoaApprovalRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
@Service
public class LoaApprovalRuleServiceImpl implements LoaApprovalRuleService {

    @Resource
    private LoaApprovalRuleDao loaApprovalRuleDao;

    @Override
    public LoaApprovalRuleEntity queryLoaApprovalRule(LoaApprovalRuleDTO loaApprovalRuleDTO) {
        List<LoaApprovalRuleEntity> loaApprovalRuleEntities = loaApprovalRuleDao.queryLoaApprovalRule(loaApprovalRuleDTO);

        return loaApprovalRuleEntities.isEmpty() ? null : loaApprovalRuleEntities.get(0);
    }
}
