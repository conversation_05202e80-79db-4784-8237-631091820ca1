package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.entity.CPowerEntity;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
public interface ICPowerService {

    List<CPowerEntity> queryPower();


    List<CPowerEntity> queryPowerByIdList(List<Integer> powerIdList);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<CPowerEntity> queryPowerList(PowerQO condition);
}
