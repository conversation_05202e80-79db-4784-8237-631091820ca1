package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.RoleMapper;
import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.RoleQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.CommonListUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2021/12/8 18:09
 */
@Dao
public class RoleDao extends BaseDaoImpl<RoleMapper, RoleEntity> {

    public List<RoleEntity> getRoleList(Integer system) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setSystem(system);

        return queryRoleList(roleQueryDTO);
    }

    public RoleEntity getRoleById(Integer id) {
        return this.getOne(new LambdaQueryWrapper<RoleEntity>().eq(RoleEntity::getId, id)
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<RoleEntity> getRoleByRoleName(String roleName) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleName(roleName);

        return queryRoleList(roleQueryDTO);
    }

    public void deleteByRoleDefId(Integer roleDefId) {
        remove(Wrappers.<RoleEntity>lambdaQuery()
                .eq(RoleEntity::getRoleDefId, roleDefId)
        );
    }

    public List<RoleEntity> getRoleListByDefId(Integer roleDefId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefId(roleDefId);

        return queryRoleList(roleQueryDTO);
    }


    public List<RoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefIdList(roleDefIdList);

        return queryRoleList(roleQueryDTO);
    }

    public List<RoleEntity> queryRole(EmployRoleDTO employRoleDTO) {
        if (CollectionUtils.isNotEmpty(employRoleDTO.getCategoryIdList())) {
            employRoleDTO.getCategoryIdList().add(0);
        }
        if (CollectionUtils.isNotEmpty(employRoleDTO.getFactoryIdList())) {
            employRoleDTO.getFactoryIdList().add(0);
        }
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(employRoleDTO.getName()), RoleEntity::getName, employRoleDTO.getName())
                .eq(StringUtils.isNotBlank(employRoleDTO.getCategoryId()), RoleEntity::getCategory2, employRoleDTO.getCategoryId())
                .eq(StringUtils.isNotBlank(employRoleDTO.getFactoryId()), RoleEntity::getFactoryId, employRoleDTO.getFactoryId())
                .eq(StringUtils.isNotBlank(employRoleDTO.getCompanyId()), RoleEntity::getCompanyId, employRoleDTO.getCompanyId())
                .eq(StringUtils.isNotBlank(employRoleDTO.getRoleDefCode()), RoleEntity::getRoleDefCode, employRoleDTO.getRoleDefCode())
                .eq(null != employRoleDTO.getRoleDefId(), RoleEntity::getRoleDefId, employRoleDTO.getRoleDefId())
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(CollectionUtils.isNotEmpty(employRoleDTO.getCategoryIdList()), RoleEntity::getCategory2, employRoleDTO.getCategoryIdList())
                .in(CollectionUtils.isNotEmpty(employRoleDTO.getFactoryIdList()), RoleEntity::getFactoryId, employRoleDTO.getFactoryIdList())

        );
    }

    public List<RoleEntity> queryByIdList(List<Integer> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            roleIdList = Collections.singletonList(-1);
        }
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .in(RoleEntity::getId, roleIdList)
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<RoleEntity> queryRoleList(RoleQueryDTO roleQueryDTO) {
        LambdaQueryWrapper<RoleEntity> queryWrapper = Wrappers.<RoleEntity>lambdaQuery()
                .eq(null != roleQueryDTO.getRoleId(), RoleEntity::getId, roleQueryDTO.getRoleId())
                .eq(null != roleQueryDTO.getSystem(), RoleEntity::getSystem, roleQueryDTO.getSystem())
                .eq(StringUtils.isNotEmpty(roleQueryDTO.getRoleCode()), RoleEntity::getCode, roleQueryDTO.getRoleCode())
                .like(StringUtils.isNotBlank(roleQueryDTO.getRoleName()), RoleEntity::getName, "%" + roleQueryDTO.getRoleName() + "%")

                .eq(null != roleQueryDTO.getRoleDefId(), RoleEntity::getRoleDefId, roleQueryDTO.getRoleDefId())
                //.eq(StringUtils.isNotEmpty(roleQueryDTO.getRoleDefCode()), RoleEntity::getRoleDefCode, roleQueryDTO.getRoleDefCode())
                .eq(RoleEntity::getIsDeleted, roleQueryDTO.getIsDeleted())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleIdList()), RoleEntity::getId, roleQueryDTO.getRoleIdList())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleCodeList()), RoleEntity::getCode, roleQueryDTO.getRoleCodeList())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList()), RoleEntity::getRoleDefId, roleQueryDTO.getRoleDefIdList())
                .eq(StringUtils.isNotEmpty(roleQueryDTO.getSiteCode()), RoleEntity::getSiteCode, roleQueryDTO.getSiteCode())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefCodeList()), RoleEntity::getRoleDefCode, roleQueryDTO.getRoleDefCodeList())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getCategoryIdList()), RoleEntity::getCategoryId, roleQueryDTO.getCategoryIdList())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getCompanyIdList()), RoleEntity::getBelongCustomerId, roleQueryDTO.getCompanyIdList())
                .like(StringUtils.isNotBlank(roleQueryDTO.getFuzzyRoleName()), RoleEntity::getName, roleQueryDTO.getFuzzyRoleName())
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());

        if (null != roleQueryDTO.getRoleDefId()
                || CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList())
        ) {
            if (null != roleQueryDTO.getCategoryId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getCategory2, roleQueryDTO.getCategoryId()).or().eq(RoleEntity::getCategory2, 0));
            }
            if (null != roleQueryDTO.getFactoryId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getFactoryId, roleQueryDTO.getFactoryId()).or().eq(RoleEntity::getFactoryId, 0));
            }
            if (null != roleQueryDTO.getBelongCustomerId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getBelongCustomerId, roleQueryDTO.getBelongCustomerId()).or().eq(RoleEntity::getBelongCustomerId, 0));
            }
        } else {
            queryWrapper.eq(null != roleQueryDTO.getCategoryId(), RoleEntity::getCategory2, roleQueryDTO.getCategoryId())
                    .eq(null != roleQueryDTO.getFactoryId(), RoleEntity::getFactoryId, roleQueryDTO.getFactoryId());
        }

        return list(queryWrapper);
    }

    public List<RoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId) {
        List<Integer> categoryIdList = new ArrayList<>();
        categoryIdList.add(categoryId);
        categoryIdList.add(0);
        if (CollectionUtils.isEmpty(roleIdList)) {
            roleIdList = Collections.singletonList(-1);
        }
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .in(RoleEntity::getId, roleIdList)
                .in(RoleEntity::getCategory2, categoryIdList)
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public List<RoleEntity> getRoleAllList() {
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public List<RoleEntity> getRoleAllListCode(String code) {
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .eq(RoleEntity::getCode, code)
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public IPage<RoleEntity> queryPageByQueryDTO(Page<RoleEntity> page, RoleQueryDTO roleQueryDTO) {
        IPage<RoleEntity> iPage = page(page, Wrappers.<RoleEntity>lambdaQuery()
                .eq(RoleEntity::getRoleDefId, roleQueryDTO.getRoleDefId())
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(RoleEntity::getCategory2, roleQueryDTO.getCategoryIdList())
                .in(RoleEntity::getFactoryId, roleQueryDTO.getFactoryIdList())
                .in(RoleEntity::getCompanyId, roleQueryDTO.getCompanyIdList())
                .orderByDesc(RoleEntity::getUpdatedAt)
        );
        return iPage;
    }

    public List<RoleEntity> queryIdListByCategoryAndFactory(String name, List<Integer> categoryIdList, List<Integer> factoryIdList, List<Integer> companyIdList) {
        categoryIdList.add(0);
        factoryIdList.add(0);
        companyIdList.add(0);
        return list(Wrappers.<RoleEntity>lambdaQuery()
                .eq(RoleEntity::getName, name)
                .eq(RoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(RoleEntity::getCategory2, categoryIdList)
                .in(RoleEntity::getFactoryId, factoryIdList)
                .in(RoleEntity::getCompanyId, companyIdList)
        );
    }

    /**
     * 查询角色ID列表
     *
     * @param condition
     * @return
     */
    public List<Integer> queryRoleIdList(RoleQO condition) {
        List<RoleEntity> list = this.list(RoleEntity.lqw(condition));
        List<Integer> result = new ArrayList<>();
        list.forEach(item -> result.add(item.getId()));
        return result;
    }

    public boolean updateDeletedByRoleDefId(Integer roleDefId) {
        return update(Wrappers.<RoleEntity>lambdaUpdate()
                .eq(RoleEntity::getRoleDefId, roleDefId)
                .set(RoleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));
    }


    public List<RoleEntity> queryRoleListByDefInfos(RoleQueryDTO roleQueryDTO) {
        LambdaQueryWrapper<RoleEntity> queryWrapper = Wrappers.<RoleEntity>lambdaQuery()
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList()), RoleEntity::getRoleDefId, roleQueryDTO.getRoleDefIdList());
        queryWrapper.and(wrapper -> wrapper
                .eq(StringUtils.isNotEmpty(roleQueryDTO.getSiteCode()), RoleEntity::getSiteCode, roleQueryDTO.getSiteCode())
                .or()
                .isNull(RoleEntity::getSiteCode)
        );
        if (null != roleQueryDTO.getRoleDefId()
                || CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList())
        ) {
            if (null != roleQueryDTO.getCategoryId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getCategory2, roleQueryDTO.getCategoryId()).or().eq(RoleEntity::getCategory2, 0));
            }
            if (null != roleQueryDTO.getFactoryId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getFactoryId, roleQueryDTO.getFactoryId()).or().eq(RoleEntity::getFactoryId, 0));
            }
            if (null != roleQueryDTO.getBelongCustomerId()) {
                queryWrapper.and(wrapper -> wrapper.eq(RoleEntity::getBelongCustomerId, roleQueryDTO.getBelongCustomerId()).or().eq(RoleEntity::getBelongCustomerId, 0));
            }
        } else {
            queryWrapper.eq(null != roleQueryDTO.getCategoryId(), RoleEntity::getCategory2, roleQueryDTO.getCategoryId())
                    .eq(null != roleQueryDTO.getFactoryId(), RoleEntity::getFactoryId, roleQueryDTO.getFactoryId());
        }

        return list(queryWrapper);
    }


}
