package com.navigator.admin.facade.impl.magellan;

import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.admin.service.magellan.IRoleDefService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/3 17:03
 */
@RestController
public class RoleFacadeImpl implements RoleFacade {

    @Autowired
    private IRoleService iRoleService;
    @Autowired
    private IRoleDefService iRoleDefService;

    @Override
    public List<RoleEntity> queryRoleByEmployId(String employId) {
        return iRoleService.queryRoleByEmployId(employId);
    }

    @Override
    public Result<List<Integer>> queryRoleIdsByEmployId(String employId) {

        List<Integer> roleIdList = new ArrayList<>();

        List<RoleEntity> roleEntityList = queryRoleByEmployId(employId);
        for (RoleEntity roleEntity : roleEntityList) {
            roleIdList.add(roleEntity.getId());
        }
        return Result.success(roleIdList);
    }

    @Override
    public Result queryRoleDefList(QueryDTO<RoleQueryDTO> roleQueryDTO) {
        return iRoleService.queryRoleDefList(roleQueryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveRole(RoleDTO roleDTO) {
        RoleDefEntity roleDefEntity = iRoleDefService.save(roleDTO);
        iRoleService.save(roleDTO, roleDefEntity);
        return Result.success(roleDefEntity.getId());
    }

    @Override
    public Result queryRoleGroupList() {
        List<RoleDefEntity> roleDefEntityList = iRoleDefService.queryRoleGroupList();
        return Result.success(roleDefEntityList);
    }

    @Override
    public List<RoleEntity> getRoleAllListCode(String code) {
        return iRoleService.getRoleAllListCode(code);
    }

    @Override
    public Result copyPermission(RoleDTO roleDTO) {
        iRoleDefService.copyPermission(roleDTO);
        return Result.success();
    }

    @Override
    public RoleEntity getRoleById(Integer id) {
        return iRoleService.getRoleById(id);
    }

    @Override
    public List<RoleEntity> getRoleListByIds(List<Integer> roldIds) {
        return iRoleService.queryByIdList(roldIds);
    }

    @Override
    public List<RoleEntity> getRoleListByRoleDefId(Integer roleDefId) {
        return iRoleService.queryRoleListByDefInfo(roleDefId, null, null);
    }

    @Override
    public List<RoleEntity> getRoleListByRoleDefInfo(Integer roleDefId, Integer categoryId, Integer customerId) {
        return iRoleService.queryRoleListByDefInfo(roleDefId, categoryId, customerId);
    }

    @Override
    public Result<List<RoleEntity>> getRoleListByRoleDefInfos(String roleDefIds, Integer categoryId, String siteCode) {
        return Result.success(iRoleService.queryRoleListByDefInfos(StringUtil.split2Int(roleDefIds, SPLIT_SIGN_DH), categoryId, siteCode));
    }

    @Override
    public List<RoleEntity> getRoleListByRoleDefInfos2(String roleDefIds, Integer categoryId, Integer factoryId) {
        return iRoleService.queryRoleListByDefInfos2(StringUtil.split2Int(roleDefIds, SPLIT_SIGN_DH), categoryId, factoryId);
    }

    @Override
    public Result saveOrUpdateRole(RoleDTO roleDTO) {
        RoleDefEntity roleDefEntity = iRoleDefService.saveOrUpdate(roleDTO);
        iRoleService.saveOrUpdate(roleDTO, roleDefEntity);
        return Result.success(roleDefEntity.getId());
    }

    @Override
    public Result<RoleQueryVO> queryRoleDefDetail(Integer roleDefId) {
        RoleQueryVO roleQueryVO = iRoleDefService.queryRoleDefDetail(roleDefId);
        return Result.success(roleQueryVO);
    }

    @Override
    public Result queryRoleList(QueryDTO<RoleQueryDTO> queryDTO) {
        return iRoleDefService.queryRoleList(queryDTO);
    }

    @Override
    public Result<List<RoleQueryVO>> queryRoleByFactory(EmployRoleDTO employRoleDTO) {
        List<RoleQueryVO> roleQueryVOList = iRoleDefService.queryRoleByFactory(employRoleDTO);
        return Result.success(roleQueryVOList);
    }

    @Override
    public Result<RoleAuthDTO> getRoleAuth(RoleAuthQO roleAuthQO) {
        return Result.success(iRoleService.getRoleAuth(roleAuthQO));
    }

    @Override
    public Result<RoleAuthMenuDTO> getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO) {
        return Result.success(iRoleService.getRoleAuthMenu(roleAuthMenuQO));
    }

    @Override
    public Result<RoleAuthPowerDTO> getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO) {
        return Result.success(iRoleService.getRoleAuthPower(roleAuthPowerQO));
    }

    @Override
    public Result<LinkedHashSet<String>> queryRoleSiteCodeSet(Integer userId) {
        return Result.success(iRoleService.queryRoleSiteCodeSet(userId));
    }

    @Override
    public Result<LinkedHashSet<Integer>> queryCategory2List(Integer userId) {
        return Result.success(iRoleService.queryCategory2List(userId));
    }

    @Override
    public Result<Boolean> isAdmin(Integer userId) {
        return Result.success(iRoleService.isAdmin(userId));
    }

}
