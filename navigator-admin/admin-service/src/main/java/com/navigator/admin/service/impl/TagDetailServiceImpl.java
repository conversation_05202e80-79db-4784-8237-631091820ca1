package com.navigator.admin.service.impl;

import com.navigator.admin.dao.TagDetailDao;
import com.navigator.admin.pojo.entity.TagDetailEntity;
import com.navigator.admin.service.ITagDetailService;
import com.navigator.common.dto.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 标签关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Service
public class TagDetailServiceImpl implements ITagDetailService {

    @Resource
    private TagDetailDao tagDetailDao;

    @Override
    public Result queryTagDetailTagId(Integer tagId) {
        return tagDetailDao.queryTagDetailTagId(tagId);
    }

    @Override
    public Result cancelTagDetail(Integer id) {
        return tagDetailDao.cancelTagDetail(id);
    }

    @Override
    public Integer saveTagDetail(TagDetailEntity tagDetailEntity) {
        return tagDetailDao.saveTagDetail(tagDetailEntity);
    }
}
