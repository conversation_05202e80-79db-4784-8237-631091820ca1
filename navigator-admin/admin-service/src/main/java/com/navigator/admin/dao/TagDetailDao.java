package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.TagDetailMapper;
import com.navigator.admin.pojo.entity.TagDetailEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 11:08
 */
@Dao
public class TagDetailDao extends BaseDaoImpl<TagDetailMapper, TagDetailEntity> {

    /**
     * 更具标签id查询出绑定的关系表
     *
     * @param tagId
     * @return
     */
    public Result queryTagDetailTagId(Integer tagId) {
        List<TagDetailEntity> list = this.baseMapper.selectList(Wrappers.<TagDetailEntity>lambdaQuery()
                .eq(TagDetailEntity::getTagId, tagId)
                .eq(TagDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return Result.success(list);
    }

    /**
     * 取消和标签的绑定绑定
     *
     * @param id
     * @return
     */
    public Result cancelTagDetail(Integer id) {
        TagDetailEntity tagDetailEntity = this.baseMapper.selectById(id);
        tagDetailEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
        return Result.success(this.baseMapper.updateById(tagDetailEntity));
    }

    /**
     * 绑定标签和业务
     *
     * @param tagDetailEntity
     * @return
     */
    public Integer saveTagDetail(TagDetailEntity tagDetailEntity) {
        return this.baseMapper.insert(tagDetailEntity);
    }
}
