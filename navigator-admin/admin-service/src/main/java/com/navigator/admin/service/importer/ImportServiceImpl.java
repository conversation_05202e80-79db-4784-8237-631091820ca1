package com.navigator.admin.service.importer;


import com.navigator.admin.dao.columbus.CRoleDao;
import com.navigator.admin.dao.columbus.CRoleDefDao;
import com.navigator.admin.dao.magellan.RoleDao;
import com.navigator.admin.dao.magellan.RoleDefDao;
import com.navigator.admin.pojo.dto.importer.*;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.SiteService;
import com.navigator.admin.service.columbus.ICRoleMenuService;
import com.navigator.admin.service.columbus.ICRolePowerService;
import com.navigator.admin.service.magellan.IRoleMenuService;
import com.navigator.admin.service.magellan.IRolePowerService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ImportServiceImpl implements ImportService {
    @Resource
    private RoleDao roleDao;
    @Resource
    private CRoleDao cRoleDao;
    @Resource
    private RoleDefDao roleDefDao;
    @Resource
    private CRoleDefDao cRoleDefDao;
    @Resource
    private IRoleService roleService;
    @Resource
    private IRoleMenuService roleMenuService;
    @Resource
    private ICRoleMenuService cRoleMenuService;
    @Resource
    private IRolePowerService rolePowerService;
    @Resource
    private ICRolePowerService cRolePowerService;
    @Resource
    private SiteService siteService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private FactoryFacade factoryFacade;
    @Resource
    private CategoryFacade categoryFacade;

    private static final int DEFAULT_BATCH_SIZE = 500; // 默认每批次大小
    private static final int DEFAULT_THREAD_POOL_SIZE = 10; // 默认线程池大小

    // 使用Map存储品类、主体和工厂的ID
    private final Map<String, Integer> categoryMap = new HashMap<>();
    private final Map<String, Integer> companyMap = new HashMap<>();
    private final Map<String, Integer> factoryMap = new HashMap<>();
    private final Map<String, String> siteMap = new HashMap<>();
    private final Map<String, Integer> roleMap = new HashMap<>();
    private final Map<String, Integer> cRoleMap = new HashMap<>();

    /**
     * 清理Map数据
     */
    private void clearMapData() {
        categoryMap.clear();
        companyMap.clear();
        factoryMap.clear();
        siteMap.clear();
        roleMap.clear();
        cRoleMap.clear();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importRole(MultipartFile file) {
        int addCount = 0;
        int updateCount = 0;
        int deleteCount = 0;

        // 初始化主体/工厂/品类
        initBasicImport();

        List<RoleImportDTO> roleImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, RoleImportDTO.class);
        for (RoleImportDTO roleImportDTO : roleImportDTOS) {
            String operation = roleImportDTO.getOperation();

            switch (operation) {
                case "更新":
                    RoleDefEntity roleDefEntity = roleDefDao.getById(Integer.parseInt(roleImportDTO.getId()));

                    // 修改虚角色
                    updateRoleDef(roleImportDTO, roleDefEntity);
                    // 删除实角色
                    // roleDao.updateDeletedByRoleDefId(roleDefId);
                    // 批量新增实角色
                    batchSaveRoleList(roleImportDTO, roleDefEntity);
                    updateCount++;
                    break;
                case "删除":
                    // 删除虚角色
                    boolean deleted = roleDefDao.updateDeletedById(Integer.parseInt(roleImportDTO.getId()));
                    // 删除实角色
                    if (deleted) {
                        roleDao.updateDeletedByRoleDefId(Integer.parseInt(roleImportDTO.getId()));
                    }
                    deleteCount++;
                    break;
                case "新增":
                    // 新增虚角色
                    RoleDefEntity roleDef = saveRoleDef(roleImportDTO);
                    // 新增实角色
                    batchSaveRoleList(roleImportDTO, roleDef);
                    addCount++;
                    break;
                default:
                    break;
            }
        }

        // 清理Mqp数据
        clearMapData();

        return "导入成功，新增：" + addCount + "条，修改：" + updateCount + "条，删除：" + deleteCount + "条";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importRoleMenu(MultipartFile file) {
        Map<String, List<String>> allRoleMenuMap = new HashMap<>(12000);
        Map<String, List<Integer>> roleMenuMap = new HashMap<>(50);

        // 初始化主体/工厂/品类/实角色
        initBasicImport();

        // 导入角色菜单
        List<RoleMenuImportDTO> roleMenuImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, RoleMenuImportDTO.class);

        // 初始化菜单角色
        roleMenuService.getAllRoleMenuList().forEach(item -> {
            String key = item.getMenuId() + "-" + item.getRoleId();
            allRoleMenuMap.computeIfAbsent(key, k -> new ArrayList<>()).add(String.valueOf(item.getRoleId()));
        });

        // 删除列表
        List<String> deleteRoleMenuList = new ArrayList<>();

        for (RoleMenuImportDTO roleMenuImportDTO : roleMenuImportDTOS) {

            String operation = roleMenuImportDTO.getOperation();

            List<Integer> roleIdList = getRoleIdList(roleMenuImportDTO.getRoleDefId(), roleMenuImportDTO.getCompany(), roleMenuImportDTO.getFactory(), roleMenuImportDTO.getCategory());

            if (operation.equals("删除")) {
                deleteRoleMenuList.addAll(roleIdList.stream().map(roleId -> roleMenuImportDTO.getMenuId() + "-" + roleId).collect(Collectors.toList()));
            } else {
                // 把需要更新的角色ID放入map
                roleMenuMap.computeIfAbsent(roleMenuImportDTO.getRoleDefId() + "-" + roleMenuImportDTO.getMenuId(), k -> new ArrayList<>()).addAll(roleIdList);
            }
        }

        List<RoleMenuEntity> roleMenuEntityList = new ArrayList<>();

        // 处理新增菜单角色
        for (Map.Entry<String, List<Integer>> entry : roleMenuMap.entrySet()) {
            String key = entry.getKey();
            String menuId = key.split("-")[1];

            for (Integer roleId : entry.getValue()) {
                // 处理新增菜单角色
                if (!allRoleMenuMap.containsKey(menuId + "-" + roleId)) {
                    // 新增菜单角色
                    RoleMenuEntity roleMenuEntity = new RoleMenuEntity();
                    roleMenuEntity
                            .setMenuId(Integer.parseInt(menuId))
                            .setRoleId(roleId);
                    roleMenuEntityList.add(roleMenuEntity);
                }
            }
        }

        // 批量保存菜单角色-分批异步处理
        batchSaveEntities(roleMenuEntityList, DEFAULT_BATCH_SIZE, roleMenuService::saveRoleMenu, "保存MGL角色菜单失败");

        // 异步处理删除菜单角色
        CompletableFuture.runAsync(() -> deleteRoleMenuList.forEach(item -> {
            String[] split = item.split("-");
            roleMenuService.updateDeletedByMenuIdAndRoleId(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
        }));

        // 清理Mqp数据
        clearMapData();

        return "导入成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importRolePower(MultipartFile file) {
        Map<String, List<String>> allRolePowerMap = new HashMap<>(25000);
        Map<String, List<Integer>> rolePowerMap = new HashMap<>(50);

        // 初始化主体/工厂/品类
        initBasicImport();

        // 导入角色权限
        List<RolePowerImportDTO> rolePowerImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, RolePowerImportDTO.class);

        // 初始化权限角色 - key: powerId-roleId, value: roleId
        rolePowerService.getAllRolePowerList().forEach(item -> {
            String key = item.getPowerId() + "-" + item.getRoleId();
            allRolePowerMap.computeIfAbsent(key, k -> new ArrayList<>()).add(String.valueOf(item.getRoleId()));
        });

        // 删除列表
        List<String> deleteRolePowerList = new ArrayList<>();

        for (RolePowerImportDTO rolePowerImportDTO : rolePowerImportDTOS) {
            String operation = rolePowerImportDTO.getOperation();

            List<Integer> roleIdList = getRoleIdList(rolePowerImportDTO.getRoleDefId(), rolePowerImportDTO.getCompany(), rolePowerImportDTO.getFactory(), rolePowerImportDTO.getCategory());

            if (operation.equals("删除")) {
                deleteRolePowerList.addAll(roleIdList.stream().map(roleId -> rolePowerImportDTO.getPowerId() + "-" + roleId).collect(Collectors.toList()));
            } else {
                // 把需要更新的角色ID放入map
                rolePowerMap.computeIfAbsent(rolePowerImportDTO.getRoleDefId() + "-" + rolePowerImportDTO.getPowerId(), k -> new ArrayList<>()).addAll(roleIdList);
            }
        }

        List<RolePowerEntity> powerEntityList = new ArrayList<>();

        // 处理新增权限角色
        for (Map.Entry<String, List<Integer>> entry : rolePowerMap.entrySet()) {
            String key = entry.getKey();
            String roleDefId = key.split("-")[0];
            String powerId = key.split("-")[1];

            for (Integer roleId : entry.getValue()) {
                // 处理新增权限角色
                if (!allRolePowerMap.containsKey(powerId + "-" + roleId)) {
                    // 新增权限角色
                    RolePowerEntity rolePowerEntity = new RolePowerEntity();
                    rolePowerEntity
                            .setPowerId(Integer.parseInt(powerId))
                            .setRoleDefId(Integer.parseInt(roleDefId))
                            .setRoleId(roleId);
                    powerEntityList.add(rolePowerEntity);
                }
            }
        }

        // 批量保存权限角色-分批异步处理
        batchSaveEntities(powerEntityList, DEFAULT_BATCH_SIZE, rolePowerService::saveRolePower, "保存MGL角色权限失败");

        // 异步处理删除权限角色
        CompletableFuture.runAsync(() -> deleteRolePowerList.forEach(item -> {
            String[] split = item.split("-");
            rolePowerService.updateDeletedByPowerIdAndRoleId(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
        }));

        // 清理Mqp数据
        clearMapData();

        return "导入成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importCLBRole(MultipartFile file) {
        Map<String, Integer> cRoleMap = new HashMap<>();

        // 初始化品类
        categoryFacade.getAllCategoryList(2).stream()
                .filter(category -> category.getStatus() == 1)
                .forEach(category -> categoryMap.put(category.getName(), category.getId()));

        // 处理导入数据
        EasyPoiUtils.importExcel(file, 0, 1, CLBRoleImportDTO.class).forEach(roleImportDTO -> {
            if ("更新".equals(roleImportDTO.getOperation())) {
                // 更新虚角色品种
                List<Integer> categoryIdList = Arrays.stream(roleImportDTO.getCategory().split(","))
                        .map(categoryName -> getIdFromMap(categoryMap, categoryName))
                        .collect(Collectors.toList());

                // 更新虚角色采销类型
                List<Integer> salesTypeList = Arrays.stream(roleImportDTO.getSalesType().split(","))
                        .map(salesTypeName -> salesTypeName.equals("采购") ? 1 : 2)
                        .collect(Collectors.toList());

                CRoleDefEntity cRoleDefEntity = cRoleDefDao.getRoleDefById(Integer.parseInt(roleImportDTO.getId()));
                cRoleDefEntity.setRelatedCategoryId(categoryIdList.toString());
                cRoleDefEntity.setRelatedSalesType(salesTypeList.toString());
                cRoleDefDao.updateById(cRoleDefEntity);

                // 获取实角色
                List<CRoleEntity> cRoleEntityList = cRoleDao.getRoleListByDefId(cRoleDefEntity.getId());
                for (CRoleEntity cRoleEntity : cRoleEntityList) {
                    String key = cRoleEntity.getRoleDefId() + "-" + cRoleEntity.getCategoryId() + "-" + cRoleEntity.getSalesType();
                    cRoleMap.put(key, cRoleEntity.getId());
                }

                // 更新实角色
                List<String> categories = getList("category", roleImportDTO.getIsCategory(), roleImportDTO.getCategory());
                List<String> salesTypes = getList("salesType", roleImportDTO.getIsSalesType(), roleImportDTO.getSalesType());

                for (String categoryName : categories) {
                    for (String salesTypeName : salesTypes) {
                        int categoryId = getIdFromMap(categoryMap, categoryName);
                        int salesType = salesTypeName.equals("采购") ? 1 : 2;

                        String key = roleImportDTO.getId() + "-" + categoryId + "-" + salesType;
                        Integer roleId = cRoleMap.get(key);
                        if (roleId == null) {
                            CRoleEntity cRoleEntity = new CRoleEntity();
                            cRoleEntity.setRoleDefId(cRoleDefEntity.getId())
                                    .setName(cRoleDefEntity.getName())
                                    .setCategoryId(categoryId)
                                    .setCategory2(categoryId)
                                    .setSalesType(salesType)
                                    .setLevel(2)
                                    .setCreatedBy(9)
                                    .setUpdatedBy(9)
                                    .setIsDeleted(0);
                            cRoleDao.save(cRoleEntity);
                        }
                    }
                }
            }
        });

        // 清理Mqp数据
        clearMapData();
        return "导入成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importCLBRoleMenu(MultipartFile file) {

        Map<String, List<String>> allCRoleMenuMap = new HashMap<>();
        Map<String, List<Integer>> cRoleMenuMap = new HashMap<>();

        // 初始化品类
        categoryFacade.getAllCategoryList(2).stream()
                .filter(category -> category.getStatus() == 1)
                .forEach(category -> categoryMap.put(category.getName(), category.getId()));

        // 初始化CLB所有角色菜单
        cRoleMenuService.getAllRoleMenuList().forEach(item -> {
            String key = item.getMenuId() + "-" + item.getRoleId();
            allCRoleMenuMap.computeIfAbsent(key, k -> new ArrayList<>()).add(String.valueOf(item.getRoleId()));
        });

        // 初始化实角色
        cRoleDao.getRoleAllList().forEach(item -> {
            String key = item.getRoleDefId() + "-" + item.getCategoryId() + "-" + item.getSalesType();
            cRoleMap.put(key, item.getId());
        });

        List<CLBRoleMenuImportDTO> roleMenuImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, CLBRoleMenuImportDTO.class);
        for (CLBRoleMenuImportDTO roleMenuImportDTO : roleMenuImportDTOList) {
            String operation = roleMenuImportDTO.getOperation();

            if (operation.equals("删除")) {
                List<Integer> deleteRoleIdList = getCRoleIdList(roleMenuImportDTO.getRoleDefId(), roleMenuImportDTO.getCategory(), roleMenuImportDTO.getSalesType());
                deleteRoleIdList.forEach(roleId -> cRoleMenuService.updateDeletedByMenuIdAndRoleId(Integer.parseInt(roleMenuImportDTO.getMenuId()), roleId));
            } else {
                List<Integer> roleIdsList = getCRoleIdList(roleMenuImportDTO.getRoleDefId(), roleMenuImportDTO.getCategory(), roleMenuImportDTO.getSalesType());
                // 把需要更新的角色ID放入map
                cRoleMenuMap.computeIfAbsent(roleMenuImportDTO.getRoleDefId() + "-" + roleMenuImportDTO.getMenuId(), k -> new ArrayList<>()).addAll(roleIdsList);
            }
        }

        List<CRoleMenuEntity> cRoleMenuEntityList = new ArrayList<>();

        // 处理新增菜单角色
        for (Map.Entry<String, List<Integer>> entry : cRoleMenuMap.entrySet()) {
            String key = entry.getKey();
            String menuId = key.split("-")[1];

            for (Integer roleId : entry.getValue()) {
                // 处理新增菜单角色
                if (!allCRoleMenuMap.containsKey(menuId + "-" + roleId)) {
                    // 新增菜单角色
                    CRoleMenuEntity cRoleMenuEntity = new CRoleMenuEntity();
                    cRoleMenuEntity
                            .setMenuId(Integer.parseInt(menuId))
                            .setRoleId(roleId);
                    cRoleMenuEntityList.add(cRoleMenuEntity);
                }
            }
        }

        // 批量保存菜单角色-分批异步处理
        batchSaveEntities(cRoleMenuEntityList, DEFAULT_BATCH_SIZE, cRoleMenuService::saveRoleMenu, "保存CLB角色菜单失败");

        // 清理Mqp数据
        clearMapData();

        return "导入成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importCLBRolePower(MultipartFile file) {

        Map<String, List<String>> allCRolePowerMap = new HashMap<>();
        Map<String, List<Integer>> cRolePowerMap = new HashMap<>();

        // 初始化品类
        categoryFacade.getAllCategoryList(2).stream()
                .filter(category -> category.getStatus() == 1)
                .forEach(category -> categoryMap.put(category.getName(), category.getId()));

        // 初始化CLB所有角色权限
        cRolePowerService.getAllRolePowerList().forEach(item -> {
            String key = item.getPowerId() + "-" + item.getRoleId();
            allCRolePowerMap.computeIfAbsent(key, k -> new ArrayList<>()).add(String.valueOf(item.getRoleId()));
        });

        // 初始化实角色
        cRoleDao.getRoleAllList().forEach(item -> {
            String key = item.getRoleDefId() + "-" + item.getCategoryId() + "-" + item.getSalesType();
            cRoleMap.put(key, item.getId());
        });

        List<CLBRolePowerImportDTO> rolePowerImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, CLBRolePowerImportDTO.class);
        for (CLBRolePowerImportDTO rolePowerImportDTO : rolePowerImportDTOList) {
            String operation = rolePowerImportDTO.getOperation();

            if (operation.equals("删除")) {
                List<Integer> deleteRoleIdList = getCRoleIdList(rolePowerImportDTO.getRoleDefId(), rolePowerImportDTO.getCategory(), rolePowerImportDTO.getSalesType());
                deleteRoleIdList.forEach(roleId -> cRolePowerService.updateDeletedByPowerIdAndRoleId(Integer.parseInt(rolePowerImportDTO.getPowerId()), roleId));
            } else {
                List<Integer> roleIdsList = getCRoleIdList(rolePowerImportDTO.getRoleDefId(), rolePowerImportDTO.getCategory(), rolePowerImportDTO.getSalesType());

                // 把需要更新的角色ID放入map
                cRolePowerMap.computeIfAbsent(rolePowerImportDTO.getRoleDefId() + "-" + rolePowerImportDTO.getPowerId(), k -> new ArrayList<>()).addAll(roleIdsList);
            }
        }

        List<CRolePowerEntity> cRolePowerEntityList = new ArrayList<>();

        // 处理新增角色权限
        for (Map.Entry<String, List<Integer>> entry : cRolePowerMap.entrySet()) {
            String key = entry.getKey();
            String roleDefId = key.split("-")[0];
            String PowerId = key.split("-")[1];

            for (Integer roleId : entry.getValue()) {
                // 处理新增菜单权限
                if (!allCRolePowerMap.containsKey(PowerId + "-" + roleId)) {
                    // 新增菜单权限
                    CRolePowerEntity cRolePowerEntity = new CRolePowerEntity();
                    cRolePowerEntity
                            .setPowerId(Integer.parseInt(PowerId))
                            .setRoleId(roleId)
                            .setRoleDefId(Integer.parseInt(roleDefId));
                    cRolePowerEntityList.add(cRolePowerEntity);
                }
            }
        }

        // 批量保存角色权限-分批异步处理
        batchSaveEntities(cRolePowerEntityList, DEFAULT_BATCH_SIZE, cRolePowerService::saveRolePower, "保存CLB角色权限失败");

        // 清理Mqp数据
        clearMapData();

        return "导入成功";
    }

    // 获取MGL角色ID - 虚角色ID+主体+工厂+品类 -> 实角色ID
    public List<Integer> getRoleIdList(String roleDefId, String company, String factory, String category) {
        Map<String, String> inputParams = new HashMap<>();
        inputParams.put("company", company);
        inputParams.put("factory", factory);
        inputParams.put("category", category);

        List<String> keys = Arrays.asList("company", "factory", "category");

        Map<String, Map<String, Integer>> idMaps = new HashMap<>();
        idMaps.put("company", companyMap);
        idMaps.put("factory", factoryMap);
        idMaps.put("category", categoryMap);

        return getRoleIds(roleDefId, inputParams, keys, idMaps, roleMap);
    }

    // 获取CLB角色ID - 虚角色ID+品类+销售类型 -> 实角色ID
    public List<Integer> getCRoleIdList(String roleDefId, String category, String salesType) {
        Map<String, String> inputParams = new HashMap<>();
        inputParams.put("category", category);
        inputParams.put("salesType", salesType);

        List<String> keys = Arrays.asList("category", "salesType");

        Map<String, Map<String, Integer>> idMaps = new HashMap<>();
        idMaps.put("category", categoryMap);

        // 对 salesType 特殊处理：采购->1, 销售->2, 默认 0
        idMaps.put("salesType", new HashMap<String, Integer>() {{
            put("采购", 1);
            put("销售", 2);
            put("0", 0);
        }});

        return getRoleIds(roleDefId, inputParams, keys, idMaps, cRoleMap);
    }

    private List<Integer> getRoleIds(String roleDefId, Map<String, String> inputParams, List<String> keys, Map<String, Map<String, Integer>> idMaps, Map<String, Integer> roleMap) {
        List<Integer> roleIdList = new ArrayList<>();
        if (roleDefId == null || roleDefId.isEmpty()) {
            return roleIdList;
        }

        // 初始化参数值
        Map<String, String[]> paramArrays = new HashMap<>();
        for (String key : keys) {
            String value = inputParams.getOrDefault(key, null);
            paramArrays.put(key, (value == null || value.isEmpty()) ? new String[]{"0"} : value.split(","));
        }

        // 递归生成组合
        generateCombinations(roleDefId, paramArrays, idMaps, roleMap, keys, 0, new String[keys.size()], roleIdList);

        return roleIdList;
    }

    private void generateCombinations(String roleDefId, Map<String, String[]> paramArrays, Map<String, Map<String, Integer>> idMaps,
                                      Map<String, Integer> roleMap, List<String> keys, int depth, String[] combination, List<Integer> roleIdList) {
        if (depth == keys.size()) {
            // 构造组合键
            StringBuilder keyBuilder = new StringBuilder(roleDefId);
            for (int i = 0; i < keys.size(); i++) {
                String value = combination[i];
                Map<String, Integer> currentMap = idMaps.get(keys.get(i));
                int id = "0".equals(value.trim()) ? 0 : currentMap.getOrDefault(value.trim(), -1);
                keyBuilder.append("-").append(id);
            }
            Integer roleId = roleMap.get(keyBuilder.toString());
            if (roleId != null) {
                roleIdList.add(roleId);
            }
            return;
        }

        String key = keys.get(depth);
        for (String value : paramArrays.get(key)) {
            combination[depth] = value;
            generateCombinations(roleDefId, paramArrays, idMaps, roleMap, keys, depth + 1, combination, roleIdList);
        }
    }

    /**
     * 通用批量保存方法
     *
     * @param entityList      要保存的实体列表
     * @param batchSize       每批次的大小
     * @param saveFunction    单个实体保存的逻辑（Consumer）
     * @param logMessageOnErr 异常日志信息
     * @param <T>             实体类型
     */
    public <T> void batchSaveEntities(
            List<T> entityList,
            int batchSize,
            Consumer<T> saveFunction,
            String logMessageOnErr
    ) {
        ExecutorService executorService = Executors.newFixedThreadPool(DEFAULT_THREAD_POOL_SIZE);

        try {
            // 预先计算最大索引，避免每次循环时重复计算
            int maxIndex = entityList.size();
            // 将实体列表分区
            for (int i = 0; i < maxIndex; i += batchSize) {
                int endIndex = Math.min(i + batchSize, maxIndex);
                List<T> batch = entityList.subList(i, endIndex);

                CompletableFuture.runAsync(() -> batch.forEach(saveFunction), executorService)
                        .exceptionally(ex -> {
                            log.error(logMessageOnErr, ex);
                            return null;
                        });
            }
        } finally {
            executorService.shutdown();
        }
    }

    // 处理新增虚角色
    private RoleDefEntity saveRoleDef(RoleImportDTO roleImportDTO) {
        String roleType = roleImportDTO.getRoleType();
        RoleDefEntity roleDef = roleDefDao.getRoleDefByName(roleType);
        if (roleDef == null) {
            roleDef = new RoleDefEntity();
            roleDef.setName(roleType)
                    .setDescription(roleType)
                    .setParentId(0)
                    .setLevel(1)
                    .setIsBaseCategory(0)
                    .setIsBaseCompany(0)
                    .setIsBaseFactory(0)
                    .setIsDeleted(0)
                    .setSystem(1)
                    .setCreatedBy(9)
                    .setUpdatedBy(9)
                    .setStatus(1)
                    .setType(0);
            roleDefDao.save(roleDef);
        }

        RoleDefEntity newRoleDefEntity = new RoleDefEntity();
        newRoleDefEntity.setName(roleImportDTO.getRoleName())
                .setDescription(roleType)
                .setParentId(roleDef.getId())
                .setLevel(2)
                .setIsBaseCategory(Optional.ofNullable(roleImportDTO.getIsCategory()).map(Integer::parseInt).orElse(0))
                .setIsBaseCompany(Optional.ofNullable(roleImportDTO.getIsCompany()).map(Integer::parseInt).orElse(0))
                .setIsBaseFactory(Optional.ofNullable(roleImportDTO.getIsFactory()).map(Integer::parseInt).orElse(0))
                .setIsDeleted(0)
                .setSystem(1)
                .setCreatedBy(9)
                .setUpdatedBy(9)
                .setStatus(1)
                .setType(0);
        roleDefDao.save(newRoleDefEntity);

        return newRoleDefEntity;
    }

    // 处理修改虚角色
    private void updateRoleDef(RoleImportDTO roleImportDTO, RoleDefEntity roleDefEntity) {

        roleDefEntity.setName(roleImportDTO.getRoleName())
                .setIsBaseCategory(Optional.ofNullable(roleImportDTO.getIsCategory()).map(Integer::parseInt).orElse(0))
                .setIsBaseCompany(Optional.ofNullable(roleImportDTO.getIsCompany()).map(Integer::parseInt).orElse(0))
                .setIsBaseFactory(Optional.ofNullable(roleImportDTO.getIsFactory()).map(Integer::parseInt).orElse(0));
        roleDefDao.updateById(roleDefEntity.setUpdatedAt(new Date()).setUpdatedBy(9));
    }

    // 批量新增实角色
    private void batchSaveRoleList(RoleImportDTO roleImportDTO, RoleDefEntity roleDefEntity) {

        // 获取区分的品类、主体和工厂的列表
        List<String> categories = getList("category", roleImportDTO.getIsCategory(), roleImportDTO.getCategory());
        List<String> companies = getList("company", roleImportDTO.getIsCompany(), roleImportDTO.getCompany());
        List<String> factories = getList("factory", roleImportDTO.getIsFactory(), roleImportDTO.getFactory());

        // 生成所有的组合
        for (String cat : categories) {
            for (String sub : companies) {
                // 特殊处理 TJ主体下仅有TJ工厂，不需要其它工厂
                if ("TJ".equals(sub)) {
                    createAndSaveRole(roleDefEntity, roleImportDTO, cat, sub, "TJ");
                    continue;
                }

                // 其他
                for (String fac : factories) {
                    createAndSaveRole(roleDefEntity, roleImportDTO, cat, sub, fac);
                }
            }
        }
        // 批量保存
        // roleDao.saveBatch(roleEntities);
    }

    // Method to encapsulate the creation and saving of RoleEntity.
    private void createAndSaveRole(RoleDefEntity roleDefEntity, RoleImportDTO roleImportDTO,
                                   String category, String company, String factory) {

        int categoryId = getIdFromMap(categoryMap, category);
        int companyId = getIdFromMap(companyMap, company);
        int factoryId = getIdFromMap(factoryMap, factory);

        String siteCode = getSiteCode(companyId, factoryId, factory);

        // 判断是否存在实角色
        String key = roleDefEntity.getId() + "-" + companyId + "-" + factoryId + "-" + categoryId;
        Integer roleId = roleMap.get(key);
        if (roleId != null) {
            RoleEntity roleEntity = roleDao.getById(roleId);
            roleEntity.setName(roleImportDTO.getRoleName())
                    .setSiteCode(siteCode)
                    .setUpdatedBy(9)
                    .setUpdatedAt(new Date());
            roleDao.updateById(roleEntity);
            return;
        }

        RoleEntity roleEntity = new RoleEntity()
                .setName(roleDefEntity.getName())
                .setDescription(roleDefEntity.getName())
                .setCategoryId(categoryId)
                .setCategory2(categoryId)
                .setFactoryId(factoryId)
                .setCompanyId(companyId)
                .setRoleDefId(roleDefEntity.getId())
                .setName(roleImportDTO.getRoleName())
                .setLevel(2)
                .setCreatedBy(9)
                .setUpdatedBy(9)
                .setSiteCode(siteCode)
                .setIsDeleted(0);

        roleDao.save(roleEntity);
    }

    // 初始化主体/工厂/品类/实角色
    private void initBasicImport() {
        // 主体
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        for (CompanyEntity companyEntity : companyEntityList) {
            companyMap.put(companyEntity.getShortName(), companyEntity.getId());
        }

        // 工厂
        List<FactoryEntity> factoryEntityList = factoryFacade.getAllFactoryList(null);
        for (FactoryEntity factoryEntity : factoryEntityList) {
            factoryMap.put(factoryEntity.getCode(), factoryEntity.getId());
        }

        // 品类
        List<CategoryEntity> allCategoryList = categoryFacade.getAllCategoryList(2);
        // 过滤启用
        allCategoryList = allCategoryList.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
        for (CategoryEntity categoryEntity : allCategoryList) {
            categoryMap.put(categoryEntity.getName(), categoryEntity.getId());
        }

        // 实角色 key: roleDefId-companyId-factoryId-categoryId, value: roleId
        List<RoleEntity> roleEntityList = roleService.getRoleAllList();
        for (RoleEntity roleEntity : roleEntityList) {
            String key = roleEntity.getRoleDefId() + "-" + roleEntity.getCompanyId() + "-" + roleEntity.getFactoryId() + "-" + roleEntity.getCategoryId();
            roleMap.put(key, roleEntity.getId());
        }
    }

    // 获取siteCode
    private String getSiteCode(int companyId, int factoryId, String factoryCode) {
        if (companyId == 0 || factoryId == 0) {
            return "";
        }
        String siteCode = siteMap.get(companyId + "_" + factoryId) == null ? "" : siteMap.get(companyId + "_" + factoryId);
        if (siteCode.isEmpty()) {
            SiteEntity siteEntity = siteService.getSiteByCompanyIdAndFactoryCode(companyId, factoryCode);
            if (siteEntity != null) {
                siteCode = siteEntity.getCode();
                siteMap.put(companyId + "_" + factoryId, siteCode);
            }
        }
        return siteCode;
    }

    // 获取区分的品类、主体或工厂列表
    private List<String> getList(String type, String isDistinguish, String value) {
        List<String> result;
        if ("1".equals(isDistinguish)) {
            result = Arrays.asList(value.split(","));
        } else {
            switch (type) {
                case "category":
                    result = new ArrayList<>(categoryMap.keySet());
                    break;
                case "company":
                    result = new ArrayList<>(companyMap.keySet());
                    break;
                case "factory":
                    result = new ArrayList<>(factoryMap.keySet());
                    break;
                default:
                    throw new IllegalArgumentException("无效的类型: " + type);
            }
        }
        return result;
    }

    // 从map中获取id
    private int getIdFromMap(Map<String, Integer> map, String name) {
        return Optional.ofNullable(map.get(name)).orElse(0);
    }

}
