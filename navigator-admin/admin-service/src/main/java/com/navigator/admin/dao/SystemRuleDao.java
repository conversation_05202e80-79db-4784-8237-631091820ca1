package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.SystemRuleMapper;
import com.navigator.admin.pojo.entity.SystemRuleEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Dao
public class SystemRuleDao extends BaseDaoImpl<SystemRuleMapper, SystemRuleEntity> {

    public List<SystemRuleEntity> querySystemRule(Integer categoryId, String ruleCode) {
        return this.list(Wrappers.<SystemRuleEntity>lambdaQuery()
                .eq(null != categoryId, SystemRuleEntity::getCategoryId, categoryId)
                .eq(!StringUtils.isBlank(ruleCode), SystemRuleEntity::getCode, ruleCode)
                .eq(SystemRuleEntity::getStatus, 1)
                .orderByAsc(SystemRuleEntity::getSort)
        );
    }


    public List<SystemRuleEntity> querySystemRuleListByParentId(Integer ruleId) {
        return list(Wrappers.<SystemRuleEntity>lambdaQuery()
                .eq(SystemRuleEntity::getParentId, ruleId)
                .eq(SystemRuleEntity::getStatus, 1)
        );
    }

    public SystemRuleEntity getPackageRuleByParentId(Integer parentId, String packageName) {
        List<SystemRuleEntity> systemRuleEntityList = list(Wrappers.<SystemRuleEntity>lambdaQuery()
                .eq(SystemRuleEntity::getParentId, parentId)
                .eq(SystemRuleEntity::getName, packageName)
                .eq(SystemRuleEntity::getStatus, 1)
        );
        return CollectionUtils.isEmpty(systemRuleEntityList) ? null : systemRuleEntityList.get(0);
    }
}
