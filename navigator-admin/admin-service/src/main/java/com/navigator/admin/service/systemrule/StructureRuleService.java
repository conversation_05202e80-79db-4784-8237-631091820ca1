package com.navigator.admin.service.systemrule;

import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

public interface StructureRuleService {

    void save(StructureRuleDTO structureRuleDTO);

    void modify(StructureRuleDTO structureRuleDTO);

    void updateStatus(StructureRuleDTO structureRuleDTO);

    Result queryStructureCode();

    Result queryStructureList(QueryDTO<StructureRuleDTO> structureRuleDTOQueryDTO);

    Result queryById(Integer id);

    String getNameById(Integer id);

    Result queryAvailableStructureList();

}
