package com.navigator.admin.service;

import com.navigator.admin.AdminNavigatorApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminNavigatorApplication.class)
@Transactional
@Rollback
class ITradeDayServiceTest {

    @Resource
    ITradeDayService tradeDayService;

    @Test
    void getTradeDays() {
        int d=tradeDayService.getTradeDays("2023-03-01","2023-03-10");
        //assertTrue(d==8);
    }

    @Test
    void isTradeDay() {
        boolean rtn = tradeDayService.isTradeDay("2023-03-03");
        //assertTrue(rtn);
    }
}