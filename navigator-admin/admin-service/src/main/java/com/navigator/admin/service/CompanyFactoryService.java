package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;

import java.util.List;

public interface CompanyFactoryService {
    List<FactoryCompanyEntity> queryCompanyFactoryList();

    List<FactoryCompanyEntity> queryFactoryByCompanyId(Integer companyId);

    void saveFactoryCompany(FactoryCompanyDTO factoryCompanyDTO);
}
