package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.FactoryCompanyMapper;
import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class FactoryCompanyDao extends BaseDaoImpl<FactoryCompanyMapper, FactoryCompanyEntity> {

    public List<FactoryCompanyEntity> queryCompanyFactoryList() {
        return list(Wrappers.<FactoryCompanyEntity>lambdaQuery()
                .eq(FactoryCompanyEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(FactoryCompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<FactoryCompanyEntity> queryFactoryByCompanyId(Integer companyId) {
        return list(Wrappers.<FactoryCompanyEntity>lambdaQuery()
                .eq(FactoryCompanyEntity::getCompanyId,companyId)
                .eq(FactoryCompanyEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(FactoryCompanyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void deleteByFactoryId(Integer factoryId) {
        remove(Wrappers.<FactoryCompanyEntity>lambdaUpdate().eq(FactoryCompanyEntity::getFactoryId, factoryId));
    }
}
