package com.navigator.admin;

import com.navigator.admin.service.IIdNameService;
import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;

@Slf4j
@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.admin.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class AdminNavigatorApplication implements CommandLineRunner {
    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }

    @Resource
    private IIdNameService idNameService;

    public static void main(String[] args) {
        SpringApplication.run(AdminNavigatorApplication.class, args);
    }

    @Override
    public void run(String... args) {
        idNameService.refreshCache();
    }
}
