package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.RoleMenuMapper;
import com.navigator.admin.pojo.entity.RoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 14:22
 */
@Dao
public class RoleMenuDao extends BaseDaoImpl<RoleMenuMapper, RoleMenuEntity> {

    public List<RoleMenuEntity> findRoleMenusByRoleId(String roleId) {
        return this.list(new LambdaQueryWrapper<RoleMenuEntity>().eq(RoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(RoleMenuEntity::getRoleId, roleId));
    }

    public List<RoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList) {
        List<RoleMenuEntity> list = list(Wrappers.<RoleMenuEntity>lambdaQuery()
                .in(RoleMenuEntity::getRoleId, roleIdList)
                .eq(RoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return list;
    }

    public void deleteRoleMenu(String roleId) {
        remove(Wrappers.<RoleMenuEntity>lambdaQuery()
                .eq(RoleMenuEntity::getRoleId, roleId));
    }

    /**
     * 获取菜单ID列表
     *
     * @param condition
     * @return
     */
    public Set<Integer> queryMenuIdList(RoleMenuQO condition) {
        List<RoleMenuEntity> list = this.list(RoleMenuEntity.lqw(condition));
        Set<Integer> result = new HashSet<>();
        list.forEach(item -> result.add(item.getMenuId()));
        return result;
    }

}
