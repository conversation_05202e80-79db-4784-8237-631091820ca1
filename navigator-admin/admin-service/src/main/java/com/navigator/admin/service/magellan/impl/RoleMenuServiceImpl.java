package com.navigator.admin.service.magellan.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.magellan.RoleMenuDao;
import com.navigator.admin.pojo.entity.RoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.admin.service.magellan.IRoleMenuService;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.time.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class RoleMenuServiceImpl implements IRoleMenuService {

    @Autowired
    private RoleMenuDao roleMenuDao;

    @Override
    public List<RoleMenuEntity> findRoleMenusByRoleId(String roleId) {
        return roleMenuDao.findRoleMenusByRoleId(roleId);
    }

    @Override
    public List<RoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList) {
        return roleMenuDao.getMenuIdListByRoleIdList(roleIdList);
    }

    @Override
    public void saveRoleMenu(RoleMenuEntity roleMenuEntity) {
        roleMenuDao.save(roleMenuEntity);
    }

    @Override
    public void deleteRoleMenu(String roleId) {
        roleMenuDao.deleteRoleMenu(roleId);
    }

    @Override
    public void copyRoleMenu(Integer sourceRoleId, Integer targetRoleId) {
        List<RoleMenuEntity> roleMenuEntities = roleMenuDao.findRoleMenusByRoleId(String.valueOf(sourceRoleId));
        roleMenuDao.deleteRoleMenu(String.valueOf(targetRoleId));
        for (RoleMenuEntity roleMenuEntity : roleMenuEntities) {
            roleMenuEntity
                    .setId(null)
                    .setRoleId(targetRoleId)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
            ;
            roleMenuDao.save(roleMenuEntity);
        }
    }

    @Override
    public Set<Integer> queryMenuIdList(RoleMenuQO condition) {
        return roleMenuDao.queryMenuIdList(condition);
    }

    @Override
    public List<RoleMenuEntity> getAllRoleMenuList() {
        return roleMenuDao.list(Wrappers.<RoleMenuEntity>lambdaQuery()
                .eq(RoleMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    @Override
    public void updateDeletedByMenuIdAndRoleId(Integer menuId, Integer roleId) {
        roleMenuDao.update(Wrappers.<RoleMenuEntity>lambdaUpdate()
                .eq(RoleMenuEntity::getMenuId, menuId)
                .eq(RoleMenuEntity::getRoleId, roleId)
                .set(RoleMenuEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(RoleMenuEntity::getUpdatedAt, DateTimeUtil.now()));
    }

}
