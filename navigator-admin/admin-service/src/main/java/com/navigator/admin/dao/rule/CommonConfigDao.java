package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.mapper.rule.CommonConfigMapper;
import com.navigator.admin.pojo.dto.rule.CommonConfigRuleMatchDTO;
import com.navigator.admin.pojo.entity.rule.CommonConfigEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-14 18:01
 **/
@Dao
public class CommonConfigDao extends BaseDaoImpl<CommonConfigMapper, CommonConfigEntity> {
    public CommonConfigEntity getByUniqueCode(String configCode) {
        List<CommonConfigEntity> configEntityList = this.list(new LambdaQueryWrapper<CommonConfigEntity>()
                .eq(CommonConfigEntity::getCode, configCode));
        return CollectionUtils.isEmpty(configEntityList) ? null : configEntityList.get(0);
    }

    public List<CommonConfigEntity> getAllConfigList(String groupCode) {
        return this.list(new LambdaQueryWrapper<CommonConfigEntity>()
                .eq(StringUtils.isNotBlank(groupCode), CommonConfigEntity::getGroupCode, groupCode)
                .eq(CommonConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<CommonConfigEntity> queryCommonConfig(CommonConfigRuleMatchDTO ruleMatchDTO) {
        return this.list(new LambdaQueryWrapper<CommonConfigEntity>()
                .eq(StringUtils.isNotBlank(ruleMatchDTO.getGroupCode()), CommonConfigEntity::getGroupCode, ruleMatchDTO.getGroupCode())
                .eq(StringUtils.isNotBlank(ruleMatchDTO.getSystemId()), CommonConfigEntity::getSystemId, ruleMatchDTO.getSystemId())
                .eq(StringUtils.isNotBlank(ruleMatchDTO.getModuleType()), CommonConfigEntity::getModuleType, ruleMatchDTO.getModuleType())
                .and(null != ruleMatchDTO.getCategory1(),
                        wrapper -> wrapper.eq(CommonConfigEntity::getCategory1, ruleMatchDTO.getCategory1())
                                .or().eq(CommonConfigEntity::getCategory1, 0))
                .and(null != ruleMatchDTO.getCategory2(),
                        wrapper -> wrapper.eq(CommonConfigEntity::getCategory2, ruleMatchDTO.getCategory2())
                                .or().eq(CommonConfigEntity::getCategory2, 0))
                .and(null != ruleMatchDTO.getCategory3(),
                        wrapper -> wrapper.eq(CommonConfigEntity::getCategory3, ruleMatchDTO.getCategory3())
                                .or().eq(CommonConfigEntity::getCategory3, 0))
                .and(null != ruleMatchDTO.getSalesType(),
                        wrapper -> wrapper.eq(CommonConfigEntity::getSalesType, ruleMatchDTO.getSalesType())
                                .or().eq(CommonConfigEntity::getSalesType, 0))
                .eq(CommonConfigEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CommonConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }


    /**
     * 生成配置的唯一编号
     *
     * @param groupCode
     * @return
     */
    public String generateConfigUniqueCode(String groupCode) {
        int count = this.count(new LambdaQueryWrapper<CommonConfigEntity>()
                .eq(CommonConfigEntity::getGroupCode, groupCode));
        return groupCode + "_" + String.format("%05d", count);
    }
}
