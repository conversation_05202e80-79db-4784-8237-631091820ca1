package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.entity.CEmployBusinessEntity;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface ICEmployBusinessService {

    List<CEmployBusinessEntity> queryListByEmployId(String employId);

    void save(CEmployBusinessEntity cEmployBusinessEntity);

    void deleteByEmployId(Integer employId);
}
