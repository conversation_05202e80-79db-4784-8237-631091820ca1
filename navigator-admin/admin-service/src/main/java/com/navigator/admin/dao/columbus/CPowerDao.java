package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CPowerMapper;
import com.navigator.admin.pojo.entity.CPowerEntity;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class CPowerDao extends BaseDaoImpl<CPowerMapper, CPowerEntity> {

    public List<CPowerEntity> queryAllPower() {
        List<CPowerEntity> list = list(Wrappers.<CPowerEntity>lambdaQuery()
                .eq(CPowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return list;

    }

    public List<CPowerEntity> queryPowerByIdList(List<Integer> powerIdList) {
        List<CPowerEntity> list = list(Wrappers.<CPowerEntity>lambdaQuery()
                .in(CPowerEntity::getId,powerIdList)
                .eq(CPowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return list;

    }

    /**
     * 根据条件：获取列表
     * @param condition
     *
     * @return
     */
    public List<CPowerEntity> queryPowerList(PowerQO condition) {
        return this.list(CPowerEntity.lqw(condition));
    }
}
