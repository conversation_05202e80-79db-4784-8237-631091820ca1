package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.CRoleDefMapper;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Dao
public class CRoleDefDao extends BaseDaoImpl<CRoleDefMapper, CRoleDefEntity> {

    public CRoleDefEntity getRoleDefById(Integer roleDefId) {
        return getOne(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(CRoleDefEntity::getId, roleDefId)
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<CRoleDefEntity> queryPageByRoleQueryDTO(Page<CRoleDefEntity> page, CRoleQueryDTO roleQueryDTO) {
        IPage<CRoleDefEntity> iPage = page(page, Wrappers.<CRoleDefEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(roleQueryDTO.getRoleName()), CRoleDefEntity::getName, "%" + roleQueryDTO.getRoleName() + "%")
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ge(StringUtils.isNotBlank(roleQueryDTO.getUpdateStartTime()), CRoleDefEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(roleQueryDTO.getUpdateStartTime()))
                .le(StringUtils.isNotBlank(roleQueryDTO.getUpdateEndTime()), CRoleDefEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp2359(roleQueryDTO.getUpdateEndTime()))
                .eq(CRoleDefEntity::getLevel, 2)
                .gt(CRoleDefEntity::getId, 200)
                .orderByDesc(CRoleDefEntity::getId)
        );

        return iPage;
    }

    public List<CRoleDefEntity> getRoleDefByType(String type) {
        return list(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(type), CRoleDefEntity::getType, type)
                .eq(CRoleDefEntity::getStatus, 1)
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

    }

    public CRoleDefEntity getRoleDefByName(String name) {
        List<CRoleDefEntity> list = list(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(name), CRoleDefEntity::getName, name)
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<CRoleDefEntity> queryRoleDefExist(CRoleDTO roleDTO) {
        return list(Wrappers.<CRoleDefEntity>lambdaQuery()
                        .eq(StringUtils.isNotBlank(roleDTO.getName()), CRoleDefEntity::getName, roleDTO.getName())
//                .or(StringUtils.isNotBlank(roleDTO.getName()))
//                .eq(StringUtils.isNotBlank(roleDTO.getCode()), CRoleDefEntity::getCode, roleDTO.getCode())
                        .ne(roleDTO.getRoleDefId() != null, CRoleDefEntity::getId, roleDTO.getRoleDefId())
//                .eq(CRoleDefEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                        .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

    }

    public CRoleDefEntity queryPreData() {
        List<CRoleDefEntity> list = list(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(CRoleDefEntity::getName, "TBD")
                .gt(CRoleDefEntity::getId, 100)
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }

    public List<CRoleDefEntity> queryFatherRoleDefList() {
        return list(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(CRoleDefEntity::getLevel, 1)
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void updateDefRoleStatus(CRoleDTO cRoleDTO) {
        update(Wrappers.<CRoleDefEntity>lambdaUpdate()
                .eq(CRoleDefEntity::getId, cRoleDTO.getRoleDefId())
                .set(CRoleDefEntity::getStatus, cRoleDTO.getStatus())
                .set(CRoleDefEntity::getUpdatedAt, new Date())
                .set(CRoleDefEntity::getUpdatedBy, JwtUtils.getCurrentUserId())
        )
        ;
    }

    public List<CRoleDefEntity> queryRoleDefForbidden() {
        return list(Wrappers.<CRoleDefEntity>lambdaQuery()
                .eq(CRoleDefEntity::getStatus, DisableStatusEnum.DISABLE.getValue())
                .or()
                .eq(CRoleDefEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
        );
    }
}
