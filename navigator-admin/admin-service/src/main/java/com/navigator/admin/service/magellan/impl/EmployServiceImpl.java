package com.navigator.admin.service.magellan.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.dao.magellan.EmployDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.dto.importer.ImportEmploy;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.EmployDetailVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.SiteService;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.admin.service.magellan.IRoleDefService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.UUIDHexGenerator;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
@Slf4j
public class EmployServiceImpl implements IEmployService {

    @Resource
    private IRoleService iRoleService;
    @Resource
    private IEmployRoleService iEmployRoleService;
    @Resource
    private EmployDao employDao;
    @Autowired
    private IEmployRoleService employRoleService;
    @Autowired
    private IRoleDefService roleDefService;
    @Autowired
    private IRoleService roleService;
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IEmployService employService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Resource
    private CommonProperties commonProperties;
    @Resource
    private EmployFacade employFacade;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private SiteService siteService;

    private final Map<String, String> categoryMap = new HashMap<>();
    private final Map<String, String> companyMap = new HashMap<>();
    private final Map<String, String> factoryMap = new HashMap<>();
    private final Map<String, String> allRoleMap = new HashMap<>();


    @Override
    public IPage<EmployEntity> getEmployByCondition(QueryDTO<EmployDTO> queryDTO) {
        Page<EmployEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        ObjectMapper mapper = new ObjectMapper();
        EmployDTO employDTO = mapper.convertValue(queryDTO.getCondition(), EmployDTO.class);
        QueryWrapper<EmployEntity> queryWrapperEmploy = this.makeQueryWrapper(employDTO);
        // 根据条件查出employs
        IPage<EmployEntity> entityIPage = employDao.getBaseMapper().selectPage(page, queryWrapperEmploy);
        return entityIPage;
    }

    private QueryWrapper<EmployEntity> makeQueryWrapper(EmployDTO employDTO) {

        QueryWrapper<EmployEntity> queryWrapperEmploy = new QueryWrapper<>();
        queryWrapperEmploy.eq("status", DisableStatusEnum.ENABLE.getValue());
        queryWrapperEmploy.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue());
        if (employDTO != null) {
            String name = employDTO.getName();
            if (!StringUtil.isEmpty(name)) {
                queryWrapperEmploy.like("name", '%' + name + '%');
            }
        }
        return queryWrapperEmploy;
    }


    @Override
    public List<EmployEntity> getEmployByEmployIds(List<Integer> employIds) {
        if (CollectionUtil.isEmpty(employIds)) {
            return null;
        }
        return employDao.queryEmployByIdList(employIds);
    }

    @Override
    public List<EmployEntity> getEmployByEmail(String email, Integer system) {
        if (null == system) {
            system = SystemEnum.MAGELLAN.getValue();
        }
        List<EmployEntity> employEntityList = employDao.queryEmployByEmail(email, system);
        return employEntityList;
    }

    @Override
    public List<EmployEntity> getEmployByPhone(String phone, Integer system) {
        if (null == system) {
            system = SystemEnum.MAGELLAN.getValue();
        }
        return employDao.getEmployByPhone(phone, system);
    }

    @Override
    public EmployEntity getEmployByNickName(String nickName, Integer system) {
        if (null == system) {
            system = SystemEnum.MAGELLAN.getValue();
        }
        return employDao.queryEmployNickName(nickName, system);
    }

    @Override
    public EmployEntity getEmployById(Integer id) {
        return employDao.queryEmployById(id);
    }

    @Override
    public EmployEntity ifNotExistToSave(EmployEntity employEntity) {
        List<EmployEntity> employList = this.getEmployByEmailOrPhone(employEntity.getEmail(), employEntity.getPhone(), SystemEnum.MAGELLAN.getValue());
        return CollectionUtils.isEmpty(employList) ? null : employList.get(0);
    }

    @Override
    public List<EmployEntity> getEmployByRoleName(String roleName) {
        // 获取角色列表
        List<RoleEntity> roleEntityList = iRoleService.getRoleByRoleName(roleName);
        List<Integer> roleIdList = roleEntityList.stream().map(RoleEntity::getId).collect(Collectors.toList());

        // 定义分批处理的大小
        final int batchSize = 2000;
        List<EmployRoleEntity> employRoleEntities = new ArrayList<>();

        // 分批处理 roleIdList
        for (int i = 0; i < roleIdList.size(); i += batchSize) {
            List<Integer> batch = roleIdList.subList(i, Math.min(i + batchSize, roleIdList.size()));
            employRoleEntities.addAll(iEmployRoleService.getEmployRolesByRoleIds(batch));
        }

        // 提取 employId 列表
        List<Integer> employIds = employRoleEntities.stream().map(EmployRoleEntity::getEmployId).collect(Collectors.toList());

        // 分批处理 employIds
        List<EmployEntity> employEntities = new ArrayList<>();
        for (int i = 0; i < employIds.size(); i += batchSize) {
            List<Integer> batch = employIds.subList(i, Math.min(i + batchSize, employIds.size()));
            employEntities.addAll(employDao.getBaseMapper().selectList(new LambdaQueryWrapper<EmployEntity>()
                    .in(EmployEntity::getId, batch)
                    .eq(EmployEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                    .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())));
        }
        return employEntities;
    }

    @Override
    public List<EmployEntity> queryEmployByCompanyId(Integer companyId) {
        List<EmployEntity> employEntityList = employDao.queryEmployByCompanyId(companyId);
        return CollectionUtil.isNotEmpty(employEntityList) ? employEntityList : Collections.emptyList();
    }

    @Override
    public Result modifyPassword(EmployEntity employEntity) {
        int update = employDao.getBaseMapper().updateById(employEntity);
        return update > 0 ? Result.success() : Result.failure();
    }

    @Override
    public boolean updateEmployResetPassword(Integer id) {
        EmployEntity employEntity = employDao.getById(id);

        if (null != employEntity) {
            String password = "DF" + UUIDHexGenerator.randomCoding(4);
            employEntity.setPassword(new BCryptPasswordEncoder().encode(password));
            employDao.updateById(employEntity);
            return true;
        }

        return false;
    }

    @Override
    public EmployEntity queryEmployByCustomerId(Integer customerId) {
        return employDao.queryEmployByCustomerId(customerId);
    }

    @Override
    public Result modifyEmploy(EmployBusinessDTO employBusinessDTO) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        EmployEntity employEntity = employDao.queryEmployById(employBusinessDTO.getEmployId());
        if (null == employEntity) {
            throw new BusinessException(ResultCodeEnum.ROLE_NOT_EXISTED);
        }
        List<EmployEntity> employEntityList = employDao.queryEmployByEmail(employBusinessDTO.getEmail(), SystemEnum.MAGELLAN.getValue());
        if ((employEntityList != null && employEntityList.size() > 1) || (employEntityList != null && employEntityList.size() == 1 && !employEntityList.get(0).getId().equals(employBusinessDTO.getEmployId()))) {
            throw new BusinessException(ResultCodeEnum.EMAIL_ERROR);
        }
        employEntity.setName(employBusinessDTO.getEmployName())
                .setStatus(employBusinessDTO.getStatus())
                .setPhone(employBusinessDTO.getPhone())
                .setEmail(employBusinessDTO.getEmail())
                .setUpdatedAt(new Date())
                .setUpdatedByName(name)
                .setUpdatedBy(JwtUtils.getCurrentUserId());
        employDao.saveOrUpdate(employEntity);

        employRoleService.deleteByEmployId(employBusinessDTO.getEmployId());
        List<RoleEntity> roleEntityList = roleService.queryByIdList(employBusinessDTO.getRoleIdList());
        for (RoleEntity roleEntity : roleEntityList) {
            EmployRoleEntity employRoleEntity = new EmployRoleEntity();
            employRoleEntity
                    .setEmployId(employBusinessDTO.getEmployId())
                    .setRoleDefId(roleEntity.getRoleDefId())
                    .setRoleId(roleEntity.getId())
                    .setCompanyId(roleEntity.getCompanyId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            employRoleService.save(employRoleEntity);
        }
//        List<Integer> factoryIdList = roleEntityList.stream().map(RoleEntity::getFactoryId).distinct().collect(Collectors.toList());
//        List<Integer> categoryIdList = roleEntityList.stream().map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
//        List<Integer> companyIdList = roleEntityList.stream().map(RoleEntity::getCompanyId).distinct().collect(Collectors.toList());
//        if (factoryIdList.contains(0)) {
//            List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
//            factoryIdList = factoryEntityList.stream().map(FactoryEntity::getId).collect(Collectors.toList());
//
//        }
//        if (categoryIdList.contains(0)) {
//            List<CategoryEntity> categoryList = categoryFacade.getAllCategoryList(2);
//            categoryIdList = categoryList.stream().map(CategoryEntity::getId).collect(Collectors.toList());
//        }
//        if (companyIdList.contains(0)) {
//            List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
//            companyIdList = companyEntityList.stream().map(CompanyEntity::getId).collect(Collectors.toList());
//        }
//        employBusinessService.deleteByEmployId(employBusinessDTO.getEmployId());
//        for (Integer categoryId : categoryIdList) {
//            for (Integer factoryId : factoryIdList) {
//                for (Integer companyId : categoryIdList) {
//                    EmployBusinessEntity employBusinessEntity = new EmployBusinessEntity();
//                    employBusinessEntity.setCategoryId(categoryId)
//                            .setCompanyId(companyId)
//                            .setEmployId(employBusinessDTO.getEmployId())
//                            .setCreatedBy(userId)
//                            .setUpdatedBy(userId)
//                    ;
//
//                    employBusinessService.save(employBusinessEntity);
//                }
//
//            }
//        }
        return Result.success();
    }

    @Override
    public Result queryEmployList(QueryDTO<EmployDTO> queryDTO) {
        Integer beginEmployId = 100000;
        if (null != commonProperties.getBeginEmployId()) {
            beginEmployId = commonProperties.getBeginEmployId();
        }
        Page<EmployEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        EmployDTO employDTO = queryDTO.getCondition();

        //分页查询
        IPage<EmployEntity> iPage = employDao.queryPageByQueryDTO(page, employDTO, beginEmployId);
        List<EmployVO> list = new ArrayList<>();
//        List<RoleDefEntity> roleDefEntities = roleDefService.queryAllList();
//        Map<Integer, String> nameMap = roleDefEntities.stream().collect(Collectors.toMap(RoleDefEntity::getId, RoleDefEntity::getName));
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> factoryNameMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));

        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
        iPage.getRecords().forEach(i -> {
            EmployVO employVO = new EmployVO();
            List<EmployRoleEntity> employRolesList = employRoleService.getEmployRolesByEmploy(String.valueOf(i.getId()));
            List<Integer> roleIdList = employRolesList.stream().map(EmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
            List<RoleEntity> roleEntityList = roleService.queryByIdList(roleIdList);
//            List<String> roleNameList = roleEntityList.stream().map(RoleEntity::getName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<String> roleNameList = roleEntityList.stream().map(k -> {
                Integer categoryId = k.getCategoryId();
                Integer factoryId = k.getFactoryId();
                Integer companyId = k.getCompanyId();
                String name = k.getName();
                if (GoodsCategoryEnum.getDesc(categoryId) != null && factoryNameMap.get(factoryId) != null) {
                    name = GoodsCategoryEnum.getDesc(categoryId) + "-" + companyNameMap.get(companyId) + "-" + factoryNameMap.get(factoryId) + "-" + k.getName();
                }
                return name;
            }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<String> factoryNameList = roleEntityList.stream().map(j -> factoryNameMap.get(j.getFactoryId())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            String name = String.join("/", factoryNameList);
            employVO
                    .setId(i.getId())
                    .setEmail(i.getEmail())
                    .setName(i.getName())
                    .setPhone(i.getPhone())
                    .setRoleName(roleNameList)
                    .setStatus(i.getStatus())
                    .setCreatedAt(i.getCreatedAt())
                    .setFactoryName(name)
                    .setUpdatedAt(i.getUpdatedAt())
                    .setUpdatedByName(i.getUpdatedByName())
            ;
            list.add(employVO);

        });

        return Result.page(iPage, list);
    }

    @Override
    public EmployDetailVO queryEmployDetail(Integer employId) {
        EmployDetailVO employDetailVO = new EmployDetailVO();

        EmployEntity employEntity = employDao.queryEmployById(employId);
        if (null == employEntity) {
            throw new BusinessException(ResultCodeEnum.ROLE_NOT_EXISTED);
        }
        List<EmployRoleEntity> employRolesList = employRoleService.getEmployRolesByEmploy(String.valueOf(employId));
        List<Integer> roleIdList = employRolesList.stream().map(EmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        log.info("[generateRoleName] roleIdList:{}", roleIdList);
        List<RoleEntity> roleEntityList = roleService.queryByIdList(roleIdList);
        List<Integer> factoryIdList = roleEntityList.stream().map(RoleEntity::getFactoryId).distinct().collect(Collectors.toList());
        List<Integer> categoryIdList = roleEntityList.stream().map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        Map<Integer, String> roleNameMap = roleEntityList.stream().collect(Collectors.toMap(RoleEntity::getId, RoleEntity::getName));
        Map<Integer, Integer> roleCategoryMap = roleEntityList.stream().collect(Collectors.toMap(RoleEntity::getId, RoleEntity::getCategoryId));
        Map<Integer, Integer> roleFactoryMap = roleEntityList.stream().collect(Collectors.toMap(RoleEntity::getId, RoleEntity::getFactoryId));
        Map<Integer, Integer> roleCompanyMap = roleEntityList.stream().collect(Collectors.toMap(RoleEntity::getId, RoleEntity::getCompanyId));

        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> factoryNameMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
        List<EmployDetailVO.Role> roleList = roleIdList.stream().map(i -> {
            log.info("[generateRoleName] roleId:{}", i);
            EmployDetailVO.Role role = new EmployDetailVO.Role();
            role.setRoleId(i);
            String name = roleNameMap.get(i);
            log.info("[generateRoleName] before name:{}", name);
            Integer categoryId = roleCategoryMap.get(i);
            Integer factoryId = roleFactoryMap.get(i);
            Integer companyId = roleCompanyMap.get(i);
            if (categoryId != null && GoodsCategoryEnum.getDesc(categoryId) != null && factoryNameMap.get(factoryId) != null) {
                name = GoodsCategoryEnum.getDesc(categoryId) + "-" + companyNameMap.get(companyId) + "-" + factoryNameMap.get(factoryId) + "-" + roleNameMap.get(i);
            }
            log.info("[generateRoleName] after name:{}", name);
            role.setName(name);
            return role;
        }).distinct().collect(Collectors.toList());

        CustomerDTO customerDTO = customerFacade.getCustomerById(employEntity.getCustomerId());
        if (null != customerDTO) {
            employDetailVO
                    .setUseYqq(customerDTO.getUseYqq());
        }
        List<CategoryFactoryMenuVO.Category> categoryMenuList = categoryIdList.stream().distinct().map(i -> {
            CategoryFactoryMenuVO.Category category = new CategoryFactoryMenuVO.Category();
            category.setCategoryId(i);
            category.setCategoryName(GoodsCategoryEnum.getDescByValue(i));
            return category;
        }).distinct().collect(Collectors.toList());

        List<CategoryFactoryMenuVO.Factory> factoryMenuList = factoryIdList.stream().distinct().filter(Objects::nonNull).map(i -> {
            CategoryFactoryMenuVO.Factory factory = new CategoryFactoryMenuVO.Factory();
            factory.setFactoryId(i);
            factory.setFactoryName(factoryNameMap.get(i));
            return factory;
        }).distinct().collect(Collectors.toList());

        employDetailVO
                .setRealName(employEntity.getRealName())
                .setYqqPhone(employEntity.getPhone())
                .setLoginAccount(employEntity.getLoginAccount())
                .setYqqAuth(employEntity.getYqqAuth())
                .setBelongFactoryName(factoryNameMap.get(employEntity.getCompanyId()))
                .setName(employEntity.getName())
                .setEmail(employEntity.getEmail())
                .setPhone(employEntity.getPhone())
                .setStatus(employEntity.getStatus())
                .setWorkNo(employEntity.getWorkNo())
                .setRoleIdList(roleList)
                .setFactoryList(factoryMenuList)
                .setCategoryList(categoryMenuList)
                .setNickName(employEntity.getNickName())
        ;

        return employDetailVO;
    }

    @Override
    public List<EmployVO> queryEmployListByRoleDefId(Integer roleDefId) {
        QueryDTO<EmployDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        queryDTO.setCondition(new EmployDTO());
        Result result = queryEmployList(queryDTO);
        List<EmployVO> employList = (List<EmployVO>) result.getData();
        List<EmployRoleEntity> employRoleEntities = employRoleService.queryByRoleDefId(roleDefId);
        List<Integer> employIdList = employRoleEntities.stream().map(EmployRoleEntity::getEmployId).collect(Collectors.toList());
        List<EmployVO> list = employList.stream()
                .filter(i -> employIdList.contains(i.getId()))
                .map(i -> {
                    List<EmployRoleEntity> employRolesByEmploy = employRoleService.getEmployRolesByEmploy(String.valueOf(i.getId()));
                    List<Integer> roleIdList = employRolesByEmploy.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
                    List<RoleEntity> roleEntities = roleService.queryByIdList(roleIdList);
                    List<Integer> categoryIdList = roleEntities.stream().map(RoleEntity::getCategoryId).collect(Collectors.toList());
                    List<String> categoryNameList;
                    if (categoryIdList.contains(0)) {
                        categoryNameList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getDesc(), GoodsCategoryEnum.OSM_OIL.getDesc());
                    } else {
                        categoryNameList = categoryIdList.stream().map(GoodsCategoryEnum::getDescByValue).distinct().filter(Objects::nonNull).collect(Collectors.toList());
                    }
                    i.setCategoryNameList(categoryNameList);
                    return i;
                }).collect(Collectors.toList());

        return list;
    }

    @Override
    public String resetPassword(Integer employId) {
        EmployEntity employEntity = employDao.getById(employId);
        if (null == employEntity) {
            throw new BusinessException(ResultCodeEnum.ROLE_NOT_EXISTED);
        }
//        String password = "DF" + UUIDHexGenerator.randomCoding(4);
        String password = "111111";
        employEntity.setPassword(new BCryptPasswordEncoder().encode(password));
        employDao.updateById(employEntity);
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        List<String> receivers = new ArrayList<>();
        receivers.add(String.valueOf(employId));
        messageInfoDTO.setReceivers(receivers);
        messageInfoDTO.setBusinessCode("ResetPassword_Sms");
        Map<String, Object> map = new HashMap<>();
        String message = "已重置密码,新密码为" + password + "请妥善保管";
        map.put("sendContent", message);
        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
        log.info("resetEmployPassword success! password:{}", password);
        return password;
    }

    @Override
    public List<EmployVO> queryAvailableEmployByRoleDefId(Integer roleDefId) {
        QueryDTO<EmployDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        queryDTO.setCondition(new EmployDTO());
        Result result = queryEmployList(queryDTO);
        List<EmployVO> employList = (List<EmployVO>) result.getData();
        List<EmployVO> usedList = queryEmployListByRoleDefId(roleDefId);
        if (CollectionUtils.isEmpty(usedList)) {
            return employList;
        }
        List<Integer> usedIdList = usedList.stream().map(EmployVO::getId).collect(Collectors.toList());
        return employList.stream().filter(i -> !usedIdList.contains(i.getId())).collect(Collectors.toList());
    }

    @Override
    public CategoryFactoryMenuVO queryCategoryFactoryByRole() {
        EmployRoleDTO employRoleDTO = new EmployRoleDTO();
        employRoleDTO.setType("1");
        List<RoleDefEntity> roleDefEntityList = roleDefService.getRoleDefByType(employRoleDTO.getType());
        List<Integer> roleDefIdList = roleDefEntityList.stream().map(RoleDefEntity::getId).collect(Collectors.toList());
        List<RoleEntity> roleEntityList = roleService.queryRole(employRoleDTO);
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> nameMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));
        CategoryFactoryMenuVO categoryFactoryMenuVO = new CategoryFactoryMenuVO();


        List<Integer> categoryIdList = roleEntityList.stream().filter(i -> roleDefIdList.contains(i.getRoleDefId())).map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        }

        List<CategoryFactoryMenuVO.Category> categoryList = categoryIdList.stream().map(i -> {
            CategoryFactoryMenuVO.Category category = new CategoryFactoryMenuVO.Category();
            category.setCategoryId(i);
            category.setCategoryName(GoodsCategoryEnum.getDescByValue(i));
            return category;
        }).collect(Collectors.toList());

        CategoryFactoryMenuVO.Category category = new CategoryFactoryMenuVO.Category();
        category.setCategoryId(0);
        category.setCategoryName("无");
        categoryList.add(category);

        List<Integer> factoryIdList = roleEntityList.stream().map(RoleEntity::getFactoryId).distinct().collect(Collectors.toList());
        if (factoryIdList.contains(0)) {
            factoryIdList = factoryEntityList.stream().map(FactoryEntity::getId).collect(Collectors.toList());
        }
        List<CategoryFactoryMenuVO.Factory> factoryList = factoryIdList.stream().filter(Objects::nonNull)
                .map(i -> {
                    CategoryFactoryMenuVO.Factory factory = new CategoryFactoryMenuVO.Factory();
                    factory.setFactoryId(i);
                    factory.setFactoryName(nameMap.get(i));
                    return factory;
                }).collect(Collectors.toList());

        CategoryFactoryMenuVO.Factory factory = new CategoryFactoryMenuVO.Factory();
        factory.setFactoryId(0);
        factory.setFactoryName("无");
        factoryList.add(factory);

        categoryFactoryMenuVO.setCategoryList(categoryList);
        categoryFactoryMenuVO.setFactoryList(factoryList);
        return categoryFactoryMenuVO;
    }

    @Override
    public Result updateEmployStatus(EmployDTO employDTO) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        EmployEntity employ = employService.getEmployById(userId);
        String name = employ == null ? "" : employ.getName();
        employDao.updateEmployStatus(employDTO, userId, name);
        return Result.success();
    }

    @Override
    public Result saveEmployStatus(EmployEntity employEntity) {

        EmployEntity entity = employDao.queryEmployByCustomerId(employEntity.getCustomerId());

        if (null != entity) {
            return Result.success(false);
        }

        employDao.save(employEntity);
        return Result.success();
    }

    @Override
    public Result resetUserPassword(ResetPasswordDTO resetPasswordDTO) {
        EmployEntity employEntity = employDao.queryEmployById(Integer.parseInt(resetPasswordDTO.getEmployId()));
        if (employEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        if (!employEntity.getPhone().equalsIgnoreCase(resetPasswordDTO.getPhone())) {
            throw new BusinessException(ResultCodeEnum.PHONE_NOT_SAME);
        }
        String key = RedisConstants.C_USER_RESET_PASSWORD_CODE + resetPasswordDTO.getPhone();
        String redisCode = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(redisCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_NOT);
        }
        if (!redisCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
//            if (!timeCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_ERROR1);
//            }
        }
        String newPassword = new BCryptPasswordEncoder().encode(resetPasswordDTO.getPassword());
        employEntity.setPassword(newPassword);
        log.info("columbus user resetPassword success, newPassword:{}", newPassword);
        employDao.updateById(employEntity);
        return Result.success();
    }

    @Override
    public Result sendResetPasswordCode(String mobileNo) {
        String currentUserId = JwtUtils.getCurrentUserId();
        EmployEntity employEntity = employDao.queryEmployById(Integer.parseInt(currentUserId));
        if (employEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        if (!employEntity.getPhone().equalsIgnoreCase(mobileNo)) {
            throw new BusinessException(ResultCodeEnum.PHONE_NOT_SAME);
        }
        return Result.success();
    }

    @Override
    public Result sendAadCode(String mobileNo) {
        List<EmployEntity> employEntityList = employService.getEmployByPhone(mobileNo, SystemEnum.MAGELLAN.getValue());
        if (CollectionUtils.isEmpty(employEntityList)) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        List<EmployEntity> employEntities = employEntityList.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
        if (employEntities.size() == 0) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
        }
        if (employEntities.size() > 1) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_ONLY_ONE);
        }
        return Result.success();
    }

    @Override
    public Result verifyAadCode(LoginDTO loginDTO) {
        String key = RedisConstants.M_AAD_LOGIN_PASSWORD_CODE + loginDTO.getPhone();
        String redisCode = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(redisCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_NOT);
        }
        if (!redisCode.equalsIgnoreCase(loginDTO.getCaptcha())) {
//            if (!timeCode.equalsIgnoreCase(loginDTO.getCaptcha())) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_ERROR1);
//            }
        }
        return Result.success();
    }

    @Override
    public void updateSignature(EmployEntity employEntity) {
        employDao.saveOrUpdate(employEntity);
    }

    @Override
    public PermissionBO queryPermissionByEmployId(String employId, Integer categoryId) {
        PermissionBO permissionBO = new PermissionBO();
        List<CustomerEntity> customerEntities = customerFacade.queryFactoryCustomer();
        Map<Integer, List<Integer>> allPermissionMap = customerEntities
                .stream()
                .collect(Collectors.groupingBy(CustomerEntity::getCompanyId, Collectors.mapping(CustomerEntity::getId, Collectors.toList())));
        List<Integer> allCustomerIdList = customerEntities.stream().map(CustomerEntity::getId).distinct().collect(Collectors.toList());
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        List<Integer> allCompanyIdList = companyEntityList.stream().map(CompanyEntity::getId).collect(Collectors.toList());
        List<Integer> allCategoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));

        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(employId);
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());

        // 系统管理员拥有全部权限
        if (BigDecimal.TEN.compareTo(new BigDecimal(employId)) >= 0
                || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            permissionBO.setCustomerIdList(allCustomerIdList);
            permissionBO.setCategoryIdList(allCategoryIdList);
            permissionBO.setPermissionMap(allPermissionMap);
            permissionBO.setCompanyCustomerIdMap(allPermissionMap);
            return permissionBO;
        }

        List<RoleEntity> roleEntityList = roleService.queryByIdListAndCategory(roleIdList, categoryId);

        Map<Integer, List<Integer>> permissionMap = roleEntityList.stream()
                .collect(Collectors.groupingBy(RoleEntity::getCategoryId, Collectors.mapping(RoleEntity::getBelongCustomerId, Collectors.toList())));

        List<Integer> categoryIdList = roleEntityList.stream().map(RoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = allCategoryIdList;
        }

        List<Integer> customerIdList = roleEntityList.stream()
                .map(RoleEntity::getBelongCustomerId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (customerIdList.contains(0)) {
            customerIdList = allCustomerIdList;
        }

        List<Integer> companyIdList = roleEntityList.stream()
                .map(RoleEntity::getCompanyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());


        Map<Integer, List<Integer>> companyCustomerIdMap = roleEntityList.stream()
                .collect(Collectors.groupingBy(RoleEntity::getCompanyId, Collectors.mapping(RoleEntity::getBelongCustomerId, Collectors.toList())));

//        List<Integer> commCustomerIdList = companyCustomerIdMap.get(0) == null ? new ArrayList<>() : companyCustomerIdMap.get(0);
//        Map<Integer, List<Integer>> companyCustomerIdMap1 = companyCustomerIdMap.entrySet().stream().map(i -> {
//            i.getValue().addAll(commCustomerIdList);
//            if (i.getValue().contains(0)) {
//                i.setValue(allCustomerIdList);
//            }
//            return i;
//        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (s, s2) -> s,
//                HashMap::new));
        if (companyIdList.contains(0)) {
            companyIdList = allCompanyIdList;
        }
        Map<Integer, List<Integer>> companyCustomerIdMap1 = new HashMap<>();
        for (Integer companyId : companyIdList) {
            List<Integer> c = companyCustomerIdMap.get(0) == null ? new ArrayList<>() : companyCustomerIdMap.get(0);
            if (c.contains(0)) {
                c = allCustomerIdList;
            }
            List<Integer> c1 = companyCustomerIdMap.get(companyId) == null ? new ArrayList<>() : companyCustomerIdMap.get(companyId);
            if (c1.contains(0)) {
                c1 = allCustomerIdList;
            }
            c1.addAll(c);
            companyCustomerIdMap1.put(companyId, c1);
        }

        permissionBO.setCompanyIdList(companyIdList);
        permissionBO.setPermissionMap(permissionMap);
        permissionBO.setCustomerIdList(customerIdList);
        permissionBO.setCategoryIdList(categoryIdList);
        permissionBO.setCompanyCustomerIdMap(companyCustomerIdMap1);
        return permissionBO;
    }

    @Override
    public PermissionBO querySitePermission(String employId, Integer category2) {
        PermissionBO permissionBO = new PermissionBO();
        List<SiteEntity> allSiteEntityList = siteService.querySiteList(new SiteQO());
        List<String> allSiteCodeList = allSiteEntityList.stream().map(SiteEntity::getCode).collect(Collectors.toList());
        ;
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(employId);
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
        // 系统管理员拥有全部权限
        if (BigDecimal.TEN.compareTo(new BigDecimal(employId)) >= 0
                || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            return permissionBO.setSiteCodeList(allSiteCodeList);
        }
        List<RoleEntity> roleEntityList = roleService.queryByIdListAndCategory(roleIdList, category2);
        for (RoleEntity roleEntity : roleEntityList) {
            if ("0".equals(roleEntity.getSiteCode()) || StringUtils.isBlank(roleEntity.getSiteCode())) {
                return permissionBO.setSiteCodeList(allSiteCodeList);
            }
        }
        List<String> siteCodeList = roleEntityList.stream()
                .filter(roleEntity ->
                        !"0".equals(roleEntity.getSiteCode()) && StringUtils.isNotBlank(roleEntity.getSiteCode())
                )
                .map(RoleEntity::getSiteCode).distinct().collect(Collectors.toList());
        return permissionBO.setSiteCodeList(siteCodeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importEmploy(MultipartFile file) {
        int addCount = 0;
        int updateCount = 0;
        int deleteCount = 0;

        Map<String, ImportEmploy> updateEmployMap = new HashMap<>();
        Map<String, List<String>> updateEmployRoleMap = new HashMap<>();
        Map<String, List<Integer>> allEmployRoleMap = new HashMap<>();

        // 初始化主体/工厂/品类
        initBasicImport(false);

        // 初始化EmployRole

        //  id<4517的全部更新删除
        List<Integer> deleteEmployRoleIds = employRoleService.getAllEmployRoleList().stream()
                .map(EmployRoleEntity::getId)
                .filter(id -> id < 4517)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(deleteEmployRoleIds)) {
            employRoleService.updateDeletedByIds(deleteEmployRoleIds);
        }
        // 过滤未删除的用户角色关联记录
        employRoleService.getAllEmployRoleList().stream()
                .filter(employRoleEntity -> employRoleEntity.getIsDeleted() == 0)
                .forEach(employRoleEntity -> {
                    String key = employRoleEntity.getEmployId() + "-" + employRoleEntity.getRoleId();
                    allEmployRoleMap.computeIfAbsent(key, k -> new ArrayList<>()).add(employRoleEntity.getRoleId());
                });

        // 初始化实角色 - key: roleName-companyId-factoryId-categoryId, value: roleId
        List<RoleEntity> roleEntityList = roleService.getRoleAllList();
        for (RoleEntity roleEntity : roleEntityList) {
            String key = roleEntity.getName() + "-" + roleEntity.getCompanyId() + "-" + roleEntity.getFactoryId() + "-" + roleEntity.getCategoryId();
            allRoleMap.put(key, roleEntity.getId() + "-" + roleEntity.getRoleDefId());
        }

        List<ImportEmploy> importEmployList = EasyPoiUtils.importExcel(file, 0, 1, ImportEmploy.class);
        for (ImportEmploy importEmploy : importEmployList) {
            // 按照邮箱查询是否存在用户，不存在则新增用户
            updateEmployMap.putIfAbsent(importEmploy.getEmail().trim(), importEmploy);

            List<String> roleIdsList = getRoleIdList(importEmploy.getRole(), importEmploy.getCompanyCode(), importEmploy.getCategory(), importEmploy.getFactoryCode());

            // 把需要更新的角色ID放入map
            updateEmployRoleMap.computeIfAbsent(importEmploy.getEmail(), k -> new ArrayList<>()).addAll(roleIdsList);

        }

        // 批量更新用戶信息
        for (Map.Entry<String, ImportEmploy> entry : updateEmployMap.entrySet()) {
            ImportEmploy importEmploy = entry.getValue();
            saveOrUpdateEmploy(importEmploy);
            updateCount++;
        }

        // 批量更新用户角色关联记录
        for (Map.Entry<String, List<String>> entry : updateEmployRoleMap.entrySet()) {
            String email = entry.getKey();

            List<EmployEntity> employEntityList = employService.getEmployByEmail(email.trim(), SystemEnum.MAGELLAN.getValue());
            EmployEntity employEntity = CollUtil.getFirst(employEntityList);

            if (employEntity == null) {
                continue;
            }

            // 新增关联记录
            List<String> roleIdsList = entry.getValue();
            for (String roleIds : roleIdsList) {

                if (allEmployRoleMap.containsKey(employEntity.getId() + "-" + roleIds.split("-")[0])) {
                    continue;
                }

                // 新增用户角色关联记录
                EmployRoleEntity employRoleEntity = new EmployRoleEntity();
                employRoleEntity.setRoleId(Integer.parseInt(roleIds.split("-")[0]))
                        .setEmployId(employEntity.getId())
                        .setRoleDefId(Integer.parseInt(roleIds.split("-")[1]))
                        .setCreatedBy(9)
                        .setUpdatedBy(9)
                        .setIsDeleted(0)
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date())
                        .setCompanyId(employEntity.getCompanyId());
                employRoleService.save(employRoleEntity);
                updateCount++;
            }
        }

        return Result.success("导入成功，新增：" + addCount + "条，修改：" + updateCount + "条，删除：" + deleteCount + "条");
    }

    // 获取角色ID - 角色名称+主体+工厂+品类 -> 实角色ID-虚角色ID
    private List<String> getRoleIdList(String roleName, String company, String category, String factory) {

        List<String> roleIdList = new ArrayList<>();

        // 不区分主体工厂品类
        if (company == null && category == null && factory == null) {
            String key = roleName + "-0-0-0";
            String roleIds = allRoleMap.get(key);
            return roleIds == null ? roleIdList : Collections.singletonList(roleIds);
        }


        // 实角色 = 角色名称+主体+工厂+品类
        List<String> categories = getList("category", String.valueOf(StringUtil.isNotEmpty(category) ? 1 : 0), category);
        List<String> companies = getList("company", String.valueOf(StringUtil.isNotEmpty(company) ? 1 : 0), company);
        List<String> factories = getList("factory", String.valueOf(StringUtil.isNotEmpty(factory) ? 1 : 0), factory);


        for (String categoryName : categories) {
            for (String companyName : companies) {
                for (String factoryName : factories) {
                    int companyId = companyMap.get(companyName) == null ? 0 : Integer.parseInt(companyMap.get(companyName));
                    int categoryId = categoryMap.get(categoryName) == null ? 0 : Integer.parseInt(categoryMap.get(categoryName));
                    int factoryId = factoryMap.get(factoryName) == null ? 0 : Integer.parseInt(factoryMap.get(factoryName));

                    String key = roleName + "-" + companyId + "-" + factoryId + "-" + categoryId;
                    String roleIds = allRoleMap.get(key);
                    if (roleIds == null) {
                        log.info("角色不存在，{},{},{},{}", roleName, companyName, factoryName, categoryName);
                        continue;
                    }
                    roleIdList.add(roleIds);
                }
            }
        }
        return roleIdList;
    }

    // 获取区分的品类、主体或工厂列表
    private List<String> getList(String type, String isDistinguish, String value) {
        List<String> result;
        if ("1".equals(isDistinguish)) {
            result = Arrays.asList(value.split(","));
        } else {
            switch (type) {
                case "category":
                    result = new ArrayList<>(categoryMap.keySet());
                    break;
                case "company":
                    result = new ArrayList<>(companyMap.keySet());
                    break;
                case "factory":
                    result = new ArrayList<>(factoryMap.keySet());
                    break;
                default:
                    throw new IllegalArgumentException("无效的类型: " + type);
            }
        }
        return result;
    }


    // 保存或更新用户
    private void saveOrUpdateEmploy(ImportEmploy employ) {
        String email = employ.getEmail().trim();
        List<EmployEntity> employEntityList = employService.getEmployByEmail(email, SystemEnum.MAGELLAN.getValue());
        EmployEntity employEntity = CollectionUtil.isNotEmpty(employEntityList) ? employEntityList.get(0) : new EmployEntity();

        if (CollectionUtil.isNotEmpty(employEntityList)) {
            String name = employ.getName().trim();
            if (!employEntity.getName().equals(name) || !employEntity.getStatus().equals(Integer.valueOf(employ.getStatus()))) {
                employEntity.setName(name).setUpdatedAt(new Date());
                employDao.updateById(employEntity);
            }
        } else {
            employEntity.setCompanyId(0)
                    .setName(employ.getName().trim())
                    .setStatus(Integer.valueOf(employ.getStatus()))
                    .setPassword(new BCryptPasswordEncoder().encode("111111"))
                    .setRealName(employ.getName().trim())
                    .setNickName(StringUtils.isNotBlank(employ.getLkgCode()) ? employ.getLkgCode().trim() : null)
                    .setPhone(employ.getPhone().trim())
                    .setEmail(email)
                    .setWorkNo(StringUtils.isNotBlank(employ.getMicoId()) ? employ.getMicoId().trim() : null)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date());
            employDao.save(employEntity);
        }
    }

    @Override
    public List<EmployEntity> getEmploy(String roleDefId, String categoryId, String factoryId) {
        EmployRoleDTO employRoleDTO = new EmployRoleDTO();
        employRoleDTO.setCategoryId(categoryId)
                .setFactoryId(factoryId)
                .setRoleDefId(Integer.valueOf(roleDefId));
        List<RoleEntity> roleList = roleService.queryRole(employRoleDTO);
        List<Integer> roleIdList = roleList.stream().map(RoleEntity::getId).collect(Collectors.toList());
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByRoleIds(roleIdList);
        List<Integer> employIdList = employRoleEntityList.stream().map(EmployRoleEntity::getEmployId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employIdList)) {
            employIdList = Collections.singletonList(-1);
        }
        return employDao.queryEmployByIdList(employIdList);
    }

    @Override
    public List<EmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String factoryId) {
        EmployRoleDTO employRoleDTO = new EmployRoleDTO();
        employRoleDTO.setCategoryId(categoryId)
                .setFactoryId(factoryId)
                .setRoleDefCode(roleDefCode);
        List<RoleEntity> roleList = roleService.queryRole(employRoleDTO);
        List<Integer> roleIdList = roleList.stream().map(RoleEntity::getId).collect(Collectors.toList());
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByRoleIds(roleIdList);
        List<Integer> employIdList = employRoleEntityList.stream().map(EmployRoleEntity::getEmployId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employIdList)) {
            employIdList = Collections.singletonList(-1);
        }
        return employDao.queryEmployByIdList(employIdList);
    }

    @Override
    public List<EmployEntity> getEmployByEmailOrPhone(String email, String phone, Integer system) {
        if (null == system) {
            system = SystemEnum.MAGELLAN.getValue();
        }
        List<EmployEntity> employEntityList = employDao.getEmployByEmailOrPhone(email, phone, system);
        return employEntityList;
    }

    @Override
    public Result queryChoosedEmployByRoleId(QueryDTO<RoleDTO> queryDTO) {
        Page<EmployEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        RoleDTO roleDTO = queryDTO.getCondition();
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByRoleIds(Arrays.asList(roleDTO.getRoleId()));
        if (CollectionUtils.isEmpty(employRoleEntityList)) {
            return Result.page(page);
        }
        List<Integer> employIdList = employRoleEntityList.stream().map(EmployRoleEntity::getEmployId).collect(Collectors.toList());
        IPage<EmployEntity> iPage = employDao.queryPageByIdList(page, employIdList);
        List<EmployVO> list = iPage.getRecords().stream().map(i -> {
            EmployVO employVO = new EmployVO();
            employVO.setId(i.getId())
                    .setName(i.getName())
                    .setPhone(i.getPhone())
                    .setEmail(i.getEmail())
            ;
            return employVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);
    }

    @Override
    public Result saveOrUpdateEmployByFile(MultipartFile file) {
        List<ImportEmploy> importEmployList = EasyPoiUtils.importExcel(file, 0, 1, ImportEmploy.class);
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<String, Integer> factoryMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getCode, FactoryEntity::getId, (k1, k2) -> k1));
        Map<String, Integer> categoryMap = new HashMap<>();
        categoryMap.put(GoodsCategoryEnum.OSM_MEAL.getDesc(), GoodsCategoryEnum.OSM_MEAL.getValue());
        categoryMap.put(GoodsCategoryEnum.OSM_OIL.getDesc(), GoodsCategoryEnum.OSM_OIL.getValue());
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        Map<String, Integer> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getShortName, CompanyEntity::getId, (k1, k2) -> k1));

        Map<String, List<ImportEmploy>> importEmployMap = importEmployList.stream().filter(i -> StringUtils.isNotBlank(i.getOperateType())).collect(Collectors.groupingBy(ImportEmploy::getEmail));
        for (Map.Entry<String, List<ImportEmploy>> entry : importEmployMap.entrySet()) {
            List<ImportEmploy> list = entry.getValue();
            ImportEmploy employ = list.get(0);

            //employ
            if (StringUtils.isBlank(employ.getEmail())) {
                continue;
            }
            EmployEntity employEntity = new EmployEntity();
            List<EmployEntity> employEntityList = employService.getByEmail(employ.getEmail().trim());
            if (CollectionUtil.isNotEmpty(employEntityList)) {
                employEntity = employEntityList.get(0);
            } else {
                employEntity
                        .setEmail(StringUtils.isNotBlank(employ.getEmail()) ? employ.getEmail().trim() : null)
                        .setPassword(new BCryptPasswordEncoder().encode("111111"))
                        .setCreatedAt(new Date())
                ;
            }
            if (StringUtils.isNotBlank(employ.getName())) {
                employEntity.setName(employ.getName().trim());
            }
            if (StringUtils.isNotBlank(employ.getName())) {
                employEntity.setRealName(employ.getName().trim());
            }
            if (StringUtils.isNotBlank(employ.getLkgCode())) {
                employEntity.setNickName(employ.getLkgCode().trim());
            }
            if (StringUtils.isNotBlank(employ.getPhone())) {
                employEntity.setPhone(employ.getPhone().trim());
            }
            if (StringUtils.isNotBlank(employ.getMicoId())) {
                employEntity.setWorkNo(employ.getMicoId().trim());
            }
            employEntity.setUpdatedAt(new Date());

            if ("删除".equalsIgnoreCase(employ.getOperateType())) {
                employEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
                employEntity.setStatus(DisableStatusEnum.DISABLE.getValue());
            }
            employDao.saveOrUpdate(employEntity);

            List<Integer> totalCategoryIdList = new ArrayList<>();
            List<Integer> totalFactoryIdList = new ArrayList<>();

            employRoleService.deleteByEmployId(employEntity.getId());
            for (ImportEmploy importEmploy : list) {
                List<String> categoryNameList = new ArrayList<>();
                if (StringUtils.isNotBlank(importEmploy.getCategory())) {
                    categoryNameList = Arrays.asList(importEmploy.getCategory().split("/"));
                }
                List<String> factoryCodeList = new ArrayList<>();
                if (StringUtils.isNotBlank(importEmploy.getFactoryCode())) {
                    factoryCodeList = Arrays.asList(importEmploy.getFactoryCode().split("/"));
                }
                List<String> companyCodeList = new ArrayList<>();
                if (StringUtils.isNotBlank(importEmploy.getCompanyCode())) {
                    companyCodeList = Arrays.asList(importEmploy.getCompanyCode().split(","));
                }
                List<Integer> companyIdList = companyCodeList.stream().map(companyNameMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                List<Integer> factoryIdList = factoryCodeList.stream().map(factoryMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                totalFactoryIdList.addAll(factoryIdList);
                List<Integer> categoryIdList = categoryNameList.stream().map(categoryMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                totalCategoryIdList.addAll(categoryIdList);
                List<RoleEntity> roleList = roleService.queryIdListByCategoryAndFactory(importEmploy.getRole(), categoryIdList, factoryIdList, companyIdList);
                for (RoleEntity roleEntity : roleList) {
                    EmployRoleEntity employRoleEntity = new EmployRoleEntity();
                    employRoleEntity
                            .setRoleId(roleEntity.getId())
                            .setRoleDefId(roleEntity.getRoleDefId())
                            .setEmployId(employEntity.getId())
                            .setCompanyId(roleEntity.getCompanyId())
                            .setCreatedAt(new Date())
                            .setUpdatedAt(new Date())
                            .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                            .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    ;
                    employRoleService.save(employRoleEntity);
                }
            }
        }
        return Result.success();
    }

    @Override
    public void createEmploy(EmployBusinessDTO employBusinessDTO) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        List<EmployEntity> employEntityList = employDao.queryEmployByEmail(employBusinessDTO.getEmail(), SystemEnum.MAGELLAN.getValue());
        if (CollectionUtils.isNotEmpty(employEntityList)) {
            throw new BusinessException(ResultCodeEnum.EMAIL_ERROR);
        }
        List<EmployEntity> employEntityList1 = employDao.queryEmployByMicroId(employBusinessDTO.getWorkNo(), employBusinessDTO.getNickName(), SystemEnum.MAGELLAN.getValue());
        if (CollectionUtils.isNotEmpty(employEntityList1)) {
            throw new BusinessException(ResultCodeEnum.LKG_ERROR);
        }
        EmployEntity employEntity = new EmployEntity();
        employEntity
                .setRealName(employBusinessDTO.getEmployName())
                .setPassword(new BCryptPasswordEncoder().encode("111111"))
                .setName(employBusinessDTO.getEmployName())
                .setPhone(employBusinessDTO.getPhone())
                .setStatus(employBusinessDTO.getStatus())
                .setEmail(employBusinessDTO.getEmail())
                .setNickName(employBusinessDTO.getNickName())
                .setWorkNo(employBusinessDTO.getWorkNo())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setCreatedBy(JwtUtils.getCurrentUserId())
                .setCreatedByName(name)
        ;
        employDao.saveOrUpdate(employEntity);
    }

    @Override
    public List<EmployEntity> getByEmail(String email) {
        return employDao.getByEmail(email);
    }

    public Result exportEmployRoleList2() {
        List<EmployEntity> employEntityList = employDao.queryAllEmploy();
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> factoryNameMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));
        List<EmployVO> list = new ArrayList<>();
        employEntityList.stream().forEach(i -> {
            List<EmployRoleEntity> employRolesList = employRoleService.getEmployRolesByEmploy(String.valueOf(i.getId()));
            List<Integer> roleDefIdList = employRolesList.stream().map(EmployRoleEntity::getRoleDefId).distinct().collect(Collectors.toList());
            if (roleDefIdList.size() > 1) {
                Map<Integer, List<EmployRoleEntity>> map = employRolesList.stream().collect(Collectors.groupingBy(EmployRoleEntity::getRoleDefId));
                for (Integer roleDefId : roleDefIdList) {
                    handleList(factoryNameMap, list, i, map.get(roleDefId));
                }
            } else {
                handleList(factoryNameMap, list, i, employRolesList);
            }

        });
        return Result.success(list);
    }

    @Override
    public Result exportEmployRoleList() {
        // 初始化数据
        initBasicImport(true);

        List<EmployVO> list = new ArrayList<>();

        // 查询所有员工
        List<EmployEntity> employEntityList = employDao.queryAllEmploy();
        for (EmployEntity employEntity : employEntityList) {
            List<EmployRoleEntity> employRolesList = employRoleService.getEmployRolesByEmploy(String.valueOf(employEntity.getId()));

            List<Integer> roleIdList = employRolesList.stream().map(EmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
            List<RoleEntity> roleEntityList = roleService.queryByIdList(roleIdList);

            // 分组和合并逻辑
            Map<String, Map<String, Set<String>>> groupedData = new HashMap<>();

            for (RoleEntity roleEntity : roleEntityList) {
                String product = categoryMap.get(String.valueOf(roleEntity.getCategoryId()));
                String companyId = companyMap.get(String.valueOf(roleEntity.getCompanyId()));
                String factory = factoryMap.get(String.valueOf(roleEntity.getFactoryId()));
                // 创建组合键：品种 + 主体
                String key = roleEntity.getName() + "|" + product + "|" + companyId;

                // 初始化分组
                groupedData.putIfAbsent(key, new HashMap<>());
                groupedData.get(key).putIfAbsent(product + "|" + companyId, new HashSet<>());

                // 合并工厂
                groupedData.get(key).get(product + "|" + companyId).add(String.valueOf(factory));
            }

            // 格式化输出
            for (Map.Entry<String, Map<String, Set<String>>> group : groupedData.entrySet()) {
                String[] split = group.getKey().split("\\|");

                for (Map.Entry<String, Set<String>> subGroup : group.getValue().entrySet()) {
                    String[] keys = subGroup.getKey().split("\\|");
                    String product = keys[0];
                    String entity = keys[1];
                    String factories = String.join(",", new TreeSet<>(subGroup.getValue()));

                    EmployVO employVO = new EmployVO();
                    employVO
                            .setId(employEntity.getId())
                            .setName(employEntity.getName())
                            .setNickName(employEntity.getNickName())
                            .setEmail(employEntity.getEmail())
                            .setWorkNo(employEntity.getWorkNo())
                            .setPhone(employEntity.getPhone())
                            .setRoleNameString(split[0])
                            .setStatus(employEntity.getStatus())
                            .setCreatedAt(employEntity.getCreatedAt())
                            .setFactoryName(factories)
                            .setCategoryNameString(product)
                            .setCompanyCode(entity);
                    list.add(employVO);
                }
            }
        }
        return Result.success(list);
    }

    // 初始化主体/工厂/品类
    private void initBasicImport(boolean cacheKey) {
        // 主体
        Map<String, String> companyMap = companyService.queryCompanyList().stream()
                .collect(Collectors.toMap(
                        companyEntity -> cacheKey ? String.valueOf(companyEntity.getId()) : companyEntity.getShortName(),
                        companyEntity -> cacheKey ? companyEntity.getShortName() : String.valueOf(companyEntity.getId())
                ));
        this.companyMap.putAll(companyMap);

        // 工厂
        Map<String, String> factoryMap = factoryWarehouseFacade.getAllFactory(null).stream()
                .collect(Collectors.toMap(
                        factoryEntity -> cacheKey ? String.valueOf(factoryEntity.getId()) : factoryEntity.getCode(),
                        factoryEntity -> cacheKey ? factoryEntity.getCode() : String.valueOf(factoryEntity.getId())
                ));
        this.factoryMap.putAll(factoryMap);

        // 品类
        Map<String, String> categoryMap = categoryFacade.getAllCategoryList(2).stream()
                .filter(categoryEntity -> categoryEntity.getStatus() == 1)
                .collect(Collectors.toMap(
                        categoryEntity -> cacheKey ? String.valueOf(categoryEntity.getId()) : categoryEntity.getName(),
                        categoryEntity -> cacheKey ? categoryEntity.getName() : String.valueOf(categoryEntity.getId())
                ));
        this.categoryMap.putAll(categoryMap);
    }

    @Override
    public List<EmployEntity> getAllEmployList() {
        return employDao.queryAllEmploy();
    }

    private void handleList(Map<Integer, String> factoryNameMap, List<EmployVO> list, EmployEntity i, List<EmployRoleEntity> employRolesList) {
        EmployVO employVO = new EmployVO();
        List<Integer> roleIdList = employRolesList.stream().map(EmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        List<RoleEntity> roleEntityList = roleService.queryByIdList(roleIdList);
        List<String> roleNameList = roleEntityList.stream().map(RoleEntity::getName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        String roleNameString = String.join("/", roleNameList);
        List<String> factoryNameList = roleEntityList.stream().map(j -> factoryNameMap.get(j.getFactoryId())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<String> categoryNameList = roleEntityList.stream().map(j -> GoodsCategoryEnum.getByValue(j.getCategoryId()).getDesc()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        String categoryNameString = String.join("/", categoryNameList);
        String name = String.join("/", factoryNameList);
        employVO
                .setId(i.getId())
                .setName(i.getName())
                .setNickName(i.getNickName())
                .setEmail(i.getEmail())
                .setWorkNo(i.getWorkNo())
                .setPhone(i.getPhone())
                .setRoleNameString(roleNameString)
                .setStatus(i.getStatus())
                .setCreatedAt(i.getCreatedAt())
                .setFactoryName(name)
                .setCategoryNameString(categoryNameString)

        ;
        list.add(employVO);
    }


}

