package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.BusinessDetailUpdateRecordDTO;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;

public interface BusinessDetailUpdateRecordService {

    BusinessDetailUpdateRecordEntity detailUpdateSelect(BusinessDetailUpdateRecordDTO businessDetailUpdateRecordDTO);

    boolean saveBusinessDetailUpdateRecord(BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity);

}
