package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.admin.service.IDictService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 16:16
 **/
@RestController
@Slf4j
public class DictItemFacadeImpl implements DictItemFacade {
    @Autowired
    private IDictService dictService;

    @Override
    public Result queryByCondition(QueryDTO<DictItemEntity> queryDTO) {
        return dictService.queryByCondition(queryDTO);
    }

    @Override
    public List<DictItemEntity> queryExportVipCustomerList(DictItemEntity dictItemQO) {
        return dictService.queryExportVipCustomerList(dictItemQO);
    }

    @Override
    public List<DictItemEntity> getDictItemById(List<Integer> dictItemIdList) {
        return dictService.getDictItemById(dictItemIdList);
    }

    @Override
    public List<DictItemEntity> getItemByDictCode(String dictCode) {
        return dictService.getItemByDictCode(dictCode);
    }

    @Override
    public DictItemEntity getDictItemByCode(String dictCode, String itemCode, Integer itemValue) {
        return dictService.getDictItemByCode(dictCode, itemCode, itemValue);
    }

    @Override
    public Result saveDictItem(DictItemEntity dictItemEntity) {
        return dictService.saveDictItem(dictItemEntity);
    }

    @Override
    public Result updateDictItem(DictItemEntity dictItemEntity) {
        return dictService.updateDictItem(dictItemEntity);
    }
}
