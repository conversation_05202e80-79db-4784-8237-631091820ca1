package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.FileRecordMapper;
import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.admin.pojo.entity.FileRecordEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class FileRecordDao extends BaseDaoImpl<FileRecordMapper, FileRecordEntity> {

    public IPage<FileRecordEntity> queryFileRecordList(Page<FileRecordEntity> page, FileRecordDTO fileRecordDTO) {
        String name = "";
        if (StringUtils.isNotBlank(fileRecordDTO.getName())) {
            name = fileRecordDTO.getName().trim();
        }
        IPage<FileRecordEntity> iPage = page(page, Wrappers.<FileRecordEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(fileRecordDTO.getName()), FileRecordEntity::getName, name)
                .eq(StringUtils.isNotBlank(fileRecordDTO.getSystemId()), FileRecordEntity::getSystemId, fileRecordDTO.getSystemId())
                .eq(StringUtils.isNotBlank(fileRecordDTO.getStatus()), FileRecordEntity::getStatus, fileRecordDTO.getStatus())
                .eq(FileRecordEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(FileRecordEntity::getStatus)
                .orderByDesc(FileRecordEntity::getUpdatedAt)
        );
        return iPage;
    }

    public List<FileRecordEntity> queryFileRecordList(Integer systemId) {
        List<FileRecordEntity> list = list(Wrappers.<FileRecordEntity>lambdaQuery()
                .eq(FileRecordEntity::getSystemId,systemId)
                .eq(FileRecordEntity::getStatus, 1)
                .eq(FileRecordEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(FileRecordEntity::getUpdatedAt)
        );
        return list;
    }
}