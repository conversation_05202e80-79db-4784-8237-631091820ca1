package com.navigator.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.CommonRelationDao;
import com.navigator.admin.dao.WarehouseAreaDao;
import com.navigator.admin.dao.WarehouseCityDao;
import com.navigator.admin.dao.WarehouseDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.dto.importer.WarehouseAreaImportDTO;
import com.navigator.admin.pojo.dto.importer.WarehouseCityImportDTO;
import com.navigator.admin.pojo.dto.importer.WarehouseImportDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CommonRelationTypeEnum;
import com.navigator.admin.pojo.enums.WarehouseConfigTypeEnum;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.pojo.qo.WarehouseSiteQO;
import com.navigator.admin.service.IWarehouseService;
import com.navigator.admin.service.SiteService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WarehouseServiceImpl implements IWarehouseService {
    @Resource
    private EmployFacade employFacade;
    @Resource
    private WarehouseDao warehouseDao;
    @Resource
    private WarehouseAreaDao warehouseAreaDao;
    @Resource
    private WarehouseCityDao warehouseCityDao;
    @Resource
    private CommonRelationDao commonRelationDao;
    @Resource
    private SiteService siteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWarehouse(WarehouseEntity warehouseEntity) {
        /*// 1. 校验库存名称是否重复
        List<WarehouseEntity> nameList = warehouseDao.getWarehouseByCondition(new WarehouseBO().setName(warehouseEntity.getName()));
        if (CollectionUtil.isNotEmpty(nameList)) {
            throw new BusinessException(ResultCodeEnum.WAREHOUSE_NAME_REPEAT);
        }

        // 2. 校验库存编码是否重复
        List<WarehouseEntity> codeList = warehouseDao.getWarehouseByCondition(new WarehouseBO().setCode(warehouseEntity.getCode()));
        if (CollectionUtil.isNotEmpty(codeList)) {
            throw new BusinessException(ResultCodeEnum.WAREHOUSE_CODE_REPEAT);
        }*/

        // 获取当前用户
        String operator = "";
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (null != employEntity) {
            operator = employEntity.getName();
        }

        warehouseEntity
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(operator)
                .setUpdatedBy(operator);

        warehouseDao.save(warehouseEntity);

        // 处理账套关联关系
        saveWarehouseSiteRelation(warehouseEntity);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWarehouse(WarehouseEntity warehouseEntity) {

        /*// 校验库存名称是否重复
        List<WarehouseEntity> nameList = warehouseDao.getWarehouseByCondition(new WarehouseBO().setName(warehouseEntity.getName()));
        if (nameList.size() == 1 && !nameList.get(0).getId().equals(warehouseEntity.getId()) || nameList.size() > 1) {
            throw new BusinessException(ResultCodeEnum.WAREHOUSE_NAME_REPEAT);
        }*/

        WarehouseEntity warehouse = warehouseDao.getById(warehouseEntity.getId());

        // 关联账套
        if (StringUtils.isNotBlank(warehouseEntity.getSiteCodes()) && !warehouseEntity.getSiteCodes().equals(warehouse.getSiteCodes())) {
            // 先删除原有的关联关系
            List<CommonRelationEntity> commonRelationEntities = commonRelationDao
                    .queryByBindCode1(CommonRelationTypeEnum.BANK_ACCOUNT_RELATION.getValue(), String.valueOf(warehouse.getId()));
            if (!commonRelationEntities.isEmpty()) {
                commonRelationDao.removeByIds(commonRelationEntities.stream().map(CommonRelationEntity::getId).collect(Collectors.toList()));
            }

            // 处理新的关联关系
            saveWarehouseSiteRelation(warehouseEntity);
        }

        // 获取当前用户
        String operator = "";
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        if (null != employEntity) {
            operator = employEntity.getName();
        }

        warehouseEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(operator);

        return warehouseDao.updateById(warehouseEntity);
    }

    @Override
    public IPage<WarehouseEntity> getWarehouseList(QueryDTO<WarehouseBO> queryDTO) {
        IPage<WarehouseEntity> warehouseList = warehouseDao.getWarehouseList(queryDTO);
        warehouseList.getRecords().forEach(warehouse -> {
            // 查询区域名称
            WarehouseAreaEntity areaEntity = warehouseAreaDao.getById(warehouse.getGeographicAreaId());
            if (areaEntity != null && areaEntity.getIsDeleted() == 0) {
                warehouse.setGeographicArea(areaEntity.getName());
            }

            // 查询城市名称
            WarehouseCityEntity cityEntity = warehouseCityDao.getById(warehouse.getGeographicCityId());
            if (cityEntity != null && cityEntity.getIsDeleted() == 0) {
                warehouse.setGeographicCity(cityEntity.getName());
            }
            if (StringUtils.isNotBlank(warehouse.getSiteCodes())) {
                List<SiteEntity> siteList = siteService.querySiteList(new SiteQO());
                Map<String, List<SiteEntity>> siteMap = siteList.stream().collect(Collectors.groupingBy(SiteEntity::getCode));
                List<String> siteCodeList = Arrays.stream(warehouse.getSiteCodes()
                                .split(","))
                        .distinct()
                        .collect(Collectors.toList());
                String siteNames = siteCodeList.stream()
                        .map(siteCode -> {
                                    return CollectionUtils.isEmpty(siteMap.get(siteCode)) ? "" : siteMap.get(siteCode).get(0).getName();
                                }
                        ).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                warehouse.setSiteCodeList(siteCodeList)
                        .setSiteNames(siteNames);
            }
        });
        return warehouseList;
    }

    @Override
    public List<WarehouseEntity> getWarehouseBySiteCode(String siteCode, Integer type) {
        // 根据账套id获取对应的库点信息
        List<WarehouseEntity> warehouseEntityList = new ArrayList<>();

        List<CommonRelationEntity> relationEntityList = commonRelationDao
                .queryByBindCode2(CommonRelationTypeEnum.BANK_ACCOUNT_RELATION.getValue(), siteCode);
        if (!relationEntityList.isEmpty()) {
            // 根据库点id获取对应的库点信息
            for (CommonRelationEntity commonRelationEntity : relationEntityList) {
                WarehouseEntity warehouseEntity = warehouseDao.getById(Integer.parseInt(commonRelationEntity.getBindCode1()));
                if (warehouseEntity != null
                        && Objects.equals(warehouseEntity.getIsDeleted(), IsDeletedEnum.NOT_DELETED.getValue())
                        // 库点类型 通用 || 发货&提货
                        && (warehouseEntity.getType() == null || warehouseEntity.getType() == WarehouseConfigTypeEnum.GENERAL.getValue() || Objects.equals(warehouseEntity.getType(), type))
                        && Objects.equals(warehouseEntity.getStatus(), 1)) {
                    warehouseEntityList.add(warehouseEntity);
                }
            }
        }

        return warehouseEntityList;
    }

    @Override
    public List<WarehouseEntity> getWarehouseByCompanyId(Integer companyId, Integer category2) {
        // 根据主体id获取对应的账套信息
        List<String> siteCodes = siteService.getSiteList(companyId, category2, "", DisableStatusEnum.ENABLE.getValue())
                .stream()
                .map(SiteEntity::getCode)
                .collect(Collectors.toList());

        // 根据账套id获取对应的库点信息
        return siteCodes.stream()
                .flatMap(siteCode -> getWarehouseBySiteCode(siteCode, WarehouseConfigTypeEnum.FACTORY.getValue()).stream())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据唯一编码获取库存信息
     *
     * @param uniqueCode 库存唯一编码
     * @param type       库点类型 1.发货 2.提货
     * @return
     */
    @Override
    public WarehouseEntity getWarehouseByUniqueCode(String uniqueCode, Integer type) {
        return warehouseDao.getWarehouseByUniqueCode(uniqueCode, type);
    }

    @Override
    public WarehouseEntity getWarehouseByName(String name, Integer isDce) {
        return warehouseDao.getWarehouseByName(name, isDce);
    }

    @Override
    public WarehouseEntity getWarehouseById(Integer id) {
        return warehouseDao.getById(id);
    }

    @Override
    public List<WarehouseEntity> getAllWarehouse() {
        return warehouseDao.list().stream()
                .filter(warehouse -> Objects.equals(warehouse.getIsDeleted(), IsDeletedEnum.NOT_DELETED.getValue())
                        && warehouse.getStatus() == 1)
                .collect(Collectors.toList());
    }

    @Override
    public String getWarehouseMarketZone(Integer warehouseId) {
        WarehouseEntity warehouseEntity = warehouseDao.getById(warehouseId);
        if (warehouseEntity != null && warehouseEntity.getGeographicAreaId() != null) {
            WarehouseAreaEntity warehouseArea = warehouseAreaDao.getById(warehouseEntity.getGeographicAreaId());
            if (warehouseArea != null && warehouseArea.getIsDeleted() == 0) {
                return warehouseArea.getMarketZone();
            }
        }
        return "";
    }

    @Override
    public List<WarehouseAreaEntity> getGeographicAreaList() {
        return warehouseAreaDao.list().stream()
                .filter(warehouseArea -> Objects.equals(warehouseArea.getIsDeleted(), IsDeletedEnum.NOT_DELETED.getValue()))
                .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseCityEntity> getGeographicCityByAreaId(Integer areaId) {
        return warehouseCityDao.list().stream()
                .filter(warehouseCity -> Objects.equals(warehouseCity.getGeographicAreaId(), areaId)
                        && Objects.equals(warehouseCity.getIsDeleted(), IsDeletedEnum.NOT_DELETED.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 处理账套关联关系
     *
     * @param warehouseEntity 库存实体
     */
    private void saveWarehouseSiteRelation(WarehouseEntity warehouseEntity) {
        String siteCodes = warehouseEntity.getSiteCodes();
        if (CharSequenceUtil.isNotBlank(siteCodes)) {
            String[] siteCodeArr = siteCodes.split(",");
            for (String siteCode : siteCodeArr) {
                CommonRelationEntity commonRelationEntity = new CommonRelationEntity();
                commonRelationEntity.setRelationType(1);
                commonRelationEntity.setBindCode1(String.valueOf(warehouseEntity.getId()));
                commonRelationEntity.setBindCode2(siteCode);
                commonRelationDao.save(commonRelationEntity);
            }
        }
    }

    @Override
    public List<WarehouseEntity> getWarehouseByFactoryCode(String factoryCode, Integer category2) {
        // 根据工厂编码获取对应的账套信息
        Set<String> siteCodes = new HashSet<>();
        List<SiteEntity> siteList = siteService.querySiteList(new SiteQO().setFactoryCode(factoryCode).setStatus(1));
        siteList.forEach(item -> {
            if (StringUtil.isNotNullBlank(category2)) {
                List<String> tempList = Arrays.asList(item.getCategory2().split(","));
                if (tempList.contains(category2.toString())) {
                    siteCodes.add(item.getCode());
                }
            } else {
                siteCodes.add(item.getCode());
            }
        });

        // 根据账套id获取对应的库点信息
        return siteCodes.stream()
                .flatMap(siteCode -> getWarehouseBySiteCode(siteCode, WarehouseConfigTypeEnum.FACTORY.getValue()).stream())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseEntity> queryWarehouseList(WarehouseBO condition) {
        return warehouseDao.queryWarehouseList(condition);
    }

    @Override
    public List<WarehouseEntity> getWarehouseByCompanyIdAndFactoryCode(WarehouseSiteQO warehouseSiteQO) {
        // 过滤账套
        Set<String> siteCodes = new HashSet<>();
        List<SiteEntity> siteList = siteService.querySiteList(new SiteQO().setCompanyId(warehouseSiteQO.getCompanyId()).setFactoryCode(warehouseSiteQO.getFactoryCode()).setStatus(1));
        siteList.forEach(item -> {
            if (StringUtil.isNotNullBlank(warehouseSiteQO.getCategory2())) {
                List<String> tempList = Arrays.asList(item.getCategory2().split(","));
                if (tempList.contains(warehouseSiteQO.getCategory2().toString())) {
                    siteCodes.add(item.getCode());
                }
            } else {
                siteCodes.add(item.getCode());
            }
        });
        // 根据账套id获取对应的库点信息
        List<WarehouseEntity> result = siteCodes.stream()
                .flatMap(siteCode -> getWarehouseBySiteCode(siteCode, WarehouseConfigTypeEnum.DELIVERY.getValue()).stream()
                        .filter(item -> warehouseSiteQO.getType() == null || item.getWarehouseType().equals(warehouseSiteQO.getType())))
                .distinct()
                .collect(Collectors.toList());
        result = CollUtil.sort(result, (t1, t2) -> t2.getUpdatedAt().compareTo(t1.getUpdatedAt()));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importWarehouse(MultipartFile file) {
        List<WarehouseImportDTO> warehouseImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, WarehouseImportDTO.class);

        int successCount = 0;
        List<WarehouseEntity> updateWarehouseList = new ArrayList<>();
        List<WarehouseEntity> addWarehouseList = new ArrayList<>();

        for (WarehouseImportDTO warehouseImportDTO : warehouseImportDTOS) {
            // 地理区域
            String geoRegion = warehouseImportDTO.getGeoRegion();
            String geoAreaName = "";
            Integer geoAreaId = null;

            WarehouseAreaEntity warehouseAreaEntity = warehouseAreaDao.getByName(geoRegion);
            if (warehouseAreaEntity != null) {
                geoAreaName = warehouseAreaEntity.getName();
                geoAreaId = warehouseAreaEntity.getId();
            }

            // 地理城市
            String geographicCity = null;
            Integer geographicCityId = null;
            String geoCity = warehouseImportDTO.getGeoCity();
            if (StringUtils.isNotBlank(geoCity)) {
                WarehouseCityEntity warehouseCityEntity = warehouseCityDao.getByAreaNameAndCityName(geoRegion, geoCity);
                if (warehouseCityEntity == null) {
                    throw new RuntimeException("地理城市不存在：" + geoCity);
                }
                geographicCity = warehouseCityEntity.getName();
                geographicCityId = warehouseCityEntity.getId();
            }

            WarehouseEntity warehouseEntity = new WarehouseEntity();
            warehouseEntity
                    .setId(StringUtil.isNotBlank(warehouseImportDTO.getId()) ? Integer.valueOf(warehouseImportDTO.getId()) : null)
                    .setName(warehouseImportDTO.getWarehouseName())
                    .setCode(warehouseImportDTO.getWarehouseCode())
                    .setGeographicArea(geoAreaName)
                    .setGeographicAreaId(geoAreaId)
                    .setGeographicCity(geographicCity)
                    .setGeographicCityId(geographicCityId)
                    // 0：通用 1：发货 2：提货
                    .setType(warehouseImportDTO.getWarehouseType().equals("通用") ? 0 : (warehouseImportDTO.getWarehouseType().equals("发货") ? 1 : 2))
                    .setIsDce(StringUtil.isNotBlank(warehouseImportDTO.getIsDelivery()) ? (warehouseImportDTO.getIsDelivery().equals("是") ? 1 : 0) : 0)
                    .setWarehouseType(StringUtil.isNotBlank(warehouseImportDTO.getIsLDC()) ? (warehouseImportDTO.getIsLDC().equals("外库") ? 2 : 1) : null)
                    .setIsUnset(StringUtil.isNotBlank(warehouseImportDTO.getIsVariable()) ? (warehouseImportDTO.getIsVariable().equals("是") ? 1 : 0) : 0)
                    .setDeliveryPoint(warehouseImportDTO.getDeliveryPoint())
                    .setAddress(warehouseImportDTO.getAddress())
                    .setAtlasTerminalCode(warehouseImportDTO.getAtlasTerminal())
                    .setStatus(StringUtil.isNotBlank(warehouseImportDTO.getStatus()) ? (warehouseImportDTO.getStatus().equals("启用") ? 1 : 0) : 0)
                    .setSourceId(StringUtil.isNotBlank(warehouseImportDTO.getSourceId()) ? Integer.valueOf(warehouseImportDTO.getSourceId()) : null)
                    .setIsDeleted(0)
            ;
            if (StringUtil.isNotBlank(warehouseImportDTO.getAccountBook())) {
                StringBuilder siteCodes = new StringBuilder();
                if (StringUtil.isNotBlank(warehouseImportDTO.getAccountBook())) {
                    // 使用英文或者中文double逗号分隔
                    String replace = warehouseImportDTO.getAccountBook().replace("，", ",");
                    String[] split = replace.split(",");
                    for (String s : split) {
                        SiteEntity siteEntity = siteService.getSiteByName(s);
                        if (siteEntity != null) {
                            siteCodes.append(siteEntity.getCode()).append(",");
                        }
                    }
                }

                // 如果不为空，则去除最后一个逗号
                if (siteCodes.length() > 0) {
                    siteCodes.deleteCharAt(siteCodes.length() - 1);
                }
                warehouseEntity.setSiteCodes(siteCodes.toString());
            }

            if (warehouseEntity.getId() != null) {
                updateWarehouseList.add(warehouseEntity);
            } else {
                addWarehouseList.add(warehouseEntity);
            }
            successCount++;
        }

        // 首先处理更新的数据
        for (WarehouseEntity warehouseEntity : updateWarehouseList) {
            warehouseDao.saveWareHouseWithId(warehouseEntity);
            saveWarehouseSiteRelation(warehouseEntity);
        }

        // 再处理新增的数据
        for (WarehouseEntity warehouseEntity : addWarehouseList) {
            warehouseDao.save(warehouseEntity);
            saveWarehouseSiteRelation(warehouseEntity);
        }

        return "导入数据：" + warehouseImportDTOS.size() + "条，成功导入：" + successCount + "条";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importWarehouseArea(MultipartFile file) {
        List<WarehouseAreaImportDTO> warehouseAreaImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, WarehouseAreaImportDTO.class);
        int successCount = 0;
        for (WarehouseAreaImportDTO warehouseAreaImportDTO : warehouseAreaImportDTOS) {
            WarehouseAreaEntity warehouseAreaEntity = new WarehouseAreaEntity();
            warehouseAreaEntity
                    .setName(warehouseAreaImportDTO.getGeographicArea())
                    .setMarketZone(warehouseAreaImportDTO.getMarketZone())
                    .setIsDeleted(0);

            warehouseAreaDao.save(warehouseAreaEntity);

            successCount++;
        }

        return "导入数据：" + warehouseAreaImportDTOS.size() + "条，成功导入：" + successCount + "条";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importWarehouseCity(MultipartFile file) {
        List<WarehouseCityImportDTO> warehouseCityImportDTOS = EasyPoiUtils.importExcel(file, 0, 1, WarehouseCityImportDTO.class);
        int successCount = 0;
        for (WarehouseCityImportDTO warehouseCityImportDTO : warehouseCityImportDTOS) {
            if (StringUtil.isNotBlank(warehouseCityImportDTO.getGeographicArea())) {
                WarehouseAreaEntity warehouseArea = warehouseAreaDao.getByName(warehouseCityImportDTO.getGeographicArea());

                if (warehouseArea != null) {
                    WarehouseCityEntity warehouseCityEntity = new WarehouseCityEntity();

                    warehouseCityEntity
                            .setName(warehouseCityImportDTO.getGeographicCity())
                            .setGeographicAreaId(warehouseArea.getId())
                            .setGeographicArea(warehouseArea.getName())
                            .setIsDeleted(0);
                    warehouseCityDao.save(warehouseCityEntity);
                }

            }
            successCount++;
        }

        return "导入数据：" + warehouseCityImportDTOS.size() + "条，成功导入：" + successCount + "条";
    }

}
