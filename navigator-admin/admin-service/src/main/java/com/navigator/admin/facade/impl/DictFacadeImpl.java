package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.DictFacade;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021-12-01 12:00
 */
@RestController
public class DictFacadeImpl implements DictFacade {
//    @Resource
//    private IDictService dictService;
//    @Resource
//    private DictDao dictDao;
//
//    @Override
//    public List<DictVO> getDiceListByCode(String bizModuleCode) {
//        return dictService.getDiceListByCode(bizModuleCode);
//    }
//
//    @Override
//    public DictCommonVO getCommonDictInfo() {
//        return dictService.getCommonDictInfo();
//    }
//
//    @Override
//    public DictEntity getDictInfoById(Integer dictId) {
//        return dictDao.getById(dictId);
//    }
}
