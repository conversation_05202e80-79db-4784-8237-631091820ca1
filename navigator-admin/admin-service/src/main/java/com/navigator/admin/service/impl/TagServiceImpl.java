package com.navigator.admin.service.impl;

import com.navigator.admin.dao.TagDao;
import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.admin.service.ITagService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 标签表（商品员工绑定关系） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Service
public class TagServiceImpl implements ITagService {

    @Resource
    private TagDao tagDao;

    @Override
    public Result queryTagEntity(QueryDTO<TagQueryDTO> queryDTO) {
        return tagDao.queryTagEntity(queryDTO);
    }

    @Override
    public TagEntity queryTageEntity(Integer id) {
        return tagDao.queryTageEntity(id);
    }

    @Override
    public Result saveTagEntity(TagEntity tagEntity) {
        return tagDao.saveTagEntity(tagEntity);
    }

    @Override
    public Result updateTagEntity(TagEntity tagEntity) {
        return tagDao.updateTagEntity(tagEntity);
    }
}
