package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.StructureRuleMapper;
import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.admin.pojo.entity.StructureRuleEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class StructureRuleDao extends BaseDaoImpl<StructureRuleMapper, StructureRuleEntity> {

    public List<StructureRuleEntity> queryByName(String structureName) {
        List<StructureRuleEntity> list = list(Wrappers.<StructureRuleEntity>lambdaQuery()
                .eq(StructureRuleEntity::getStructureName, structureName)
                .eq(StructureRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return list;
    }

    public IPage<StructureRuleEntity> queryPageByStructureRuleDTO(Page<StructureRuleEntity> page, StructureRuleDTO structureRuleDTO) {
        IPage<StructureRuleEntity> iPage = page(page, Wrappers.<StructureRuleEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(structureRuleDTO.getStructureName()), StructureRuleEntity::getStructureName, structureRuleDTO.getStructureName())
                .eq(StructureRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(StructureRuleEntity::getStatus)
                .orderByAsc(StructureRuleEntity::getId)
        );
        return iPage;
    }

    public List<StructureRuleEntity> queryAvailableStructureList() {
        return list(Wrappers.<StructureRuleEntity>lambdaQuery()
                .eq(StructureRuleEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(StructureRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(StructureRuleEntity::getId)
        );
    }


}
