package com.navigator.admin.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.ProteinPriceConfigMapper;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.BasicPriceConfigEntity;
import com.navigator.admin.pojo.entity.ProteinPriceConfigEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8
 */
@Dao
public class ProteinPriceConfigDao extends BaseDaoImpl<ProteinPriceConfigMapper, ProteinPriceConfigEntity> {

    /**
     * 根据条件查询蛋白价差
     *
     * @param priceConfigQueryDTO
     * @return
     */
    public List<ProteinPriceConfigEntity> filterBasicProtein(BasicPriceConfigQueryDTO priceConfigQueryDTO) {
        return this.list(Wrappers.<ProteinPriceConfigEntity>lambdaQuery()
                .eq(null != priceConfigQueryDTO.getCategoryId(), ProteinPriceConfigEntity::getCategoryId, priceConfigQueryDTO.getCategoryId())
                .eq(StringUtils.isNotBlank(priceConfigQueryDTO.getFactoryCode()), ProteinPriceConfigEntity::getFactoryCode, priceConfigQueryDTO.getFactoryCode())
                .eq(StringUtils.isNotBlank(priceConfigQueryDTO.getDeliveryBeginDate()), ProteinPriceConfigEntity::getDeliveryBeginDate, priceConfigQueryDTO.getDeliveryBeginDate())
                .eq(null != priceConfigQueryDTO.getAttributeValueId(), ProteinPriceConfigEntity::getAttributeValueId, priceConfigQueryDTO.getAttributeValueId())
                .like(StringUtils.isNotBlank(priceConfigQueryDTO.getCompanyId()), ProteinPriceConfigEntity::getCompanyId, priceConfigQueryDTO.getCompanyId())
                .eq(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(ProteinPriceConfigEntity::getId)
        );
    }

    public List<ProteinPriceConfigEntity> getBasicProteinListByRules(List<Integer> ruleItemIdList, List<String> factoryCodeList, List<String> deliveryBeginDateList, Integer attributeValueId) {
        if (CollectionUtils.isEmpty(ruleItemIdList) || CollectionUtils.isEmpty(factoryCodeList) || CollectionUtils.isEmpty(deliveryBeginDateList)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<ProteinPriceConfigEntity>lambdaQuery()
                .in(ProteinPriceConfigEntity::getRuleItemId, ruleItemIdList)
                .in(ProteinPriceConfigEntity::getFactoryCode, factoryCodeList)
                .in(ProteinPriceConfigEntity::getDeliveryBeginDate, deliveryBeginDateList)
                .eq(ProteinPriceConfigEntity::getAttributeValueId, attributeValueId)
                .eq(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 记录蛋白价差规则信息
     *
     * @param factoryCodeList
     * @param deliveryBeginDateList
     * @param ruleItemEntity
     * @param categoryId
     * @param companyId
     * @param isNew
     */
    public void recordBasicProteinConfig(List<String> factoryCodeList,
                                         List<String> deliveryBeginDateList,
                                         SystemRuleItemEntity ruleItemEntity,
                                         Integer categoryId,
                                         String companyId,
                                         boolean isNew) {
        if (!isNew) {
            this.dropBasicPriceConfig(ruleItemEntity.getId());
        }
        for (String factoryCode : factoryCodeList) {
            for (String deliveryBeginDate : deliveryBeginDateList) {
                List<ProteinPriceConfigEntity> proteinPriceConfigEntities = getBasicPriceListByRules(ruleItemEntity.getId(), factoryCode, deliveryBeginDate);
                if (CollectionUtils.isNotEmpty(proteinPriceConfigEntities)) {
                    continue;
                }
                ProteinPriceConfigEntity basicPriceConfigEntity = new ProteinPriceConfigEntity()
                        .setRuleItemId(ruleItemEntity.getId())
                        .setCategoryId(categoryId)
                        .setRuleId(ruleItemEntity.getRuleId())
                        .setFactoryCode(factoryCode)
                        .setAttributeValueId(Integer.parseInt(ruleItemEntity.getRuleKey()))
                        .setDeliveryBeginDate(deliveryBeginDate)
                        .setCompanyId(JSONObject.toJSONString(companyId));
                this.save(basicPriceConfigEntity);
            }
        }
    }

    public List<ProteinPriceConfigEntity> getBasicPriceListByRules(Integer ruleItemId, String factoryCode, String deliveryBeginDate) {
        return this.list(Wrappers.<ProteinPriceConfigEntity>lambdaQuery()
                .eq(ProteinPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(ProteinPriceConfigEntity::getFactoryCode, factoryCode)
                .eq(ProteinPriceConfigEntity::getDeliveryBeginDate, deliveryBeginDate)
                .eq(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 删除所有关联关系
     *
     * @param ruleItemId 基差基准价ID
     */
    private void dropBasicPriceConfig(Integer ruleItemId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(ProteinPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }

    /**
     * 根据蛋白价差ID查询所有关联关系
     *
     * @param ruleItemId 蛋白价差ID
     * @return 关联关系集合
     */
    public List<ProteinPriceConfigEntity> getBasicProteinListByRuleItemId(Integer ruleItemId) {
        return this.list(Wrappers.<ProteinPriceConfigEntity>lambdaQuery()
                .eq(ProteinPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(ProteinPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
