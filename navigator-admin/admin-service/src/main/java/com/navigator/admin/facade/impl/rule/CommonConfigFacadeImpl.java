package com.navigator.admin.facade.impl.rule;

import com.navigator.admin.facade.rule.CommonConfigFacade;
import com.navigator.admin.pojo.dto.rule.CommonConfigDTO;
import com.navigator.admin.pojo.dto.rule.CommonConfigRuleMatchDTO;
import com.navigator.admin.service.rule.CommonConfigService;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-19 17:00
 **/
@RestController
public class CommonConfigFacadeImpl implements CommonConfigFacade {
    @Autowired
    private CommonConfigService commonConfigService;

    @Override
    public Result saveOrUpdateCommonConfig(CommonConfigDTO commonConfigDTO) {
        return Result.success(commonConfigService.saveOrUpdateCommonConfig(commonConfigDTO));
    }

    @Override
    public CommonConfigDTO getCommonConfigDetailById(Integer configId) {
        return commonConfigService.getCommonConfigDetailById(configId);
    }

    @Override
    public List<CommonConfigDTO> getSignPaperRuleConfig() {
        return commonConfigService.getSignPaperRuleConfig();
    }

    @Override
    public Boolean matchConfigRuleInfo(CommonConfigRuleMatchDTO ruleMatchDTO) {
        return commonConfigService.matchConfigRuleInfo(ruleMatchDTO);
    }
}
