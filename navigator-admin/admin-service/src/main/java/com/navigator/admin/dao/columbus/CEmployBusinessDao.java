package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CEmployBusinessMapper;
import com.navigator.admin.pojo.entity.CEmployBusinessEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class CEmployBusinessDao extends BaseDaoImpl<CEmployBusinessMapper, CEmployBusinessEntity> {
    public List<CEmployBusinessEntity> queryListByEmployId(String employId) {
        return list(Wrappers.<CEmployBusinessEntity>lambdaQuery()
                .eq(CEmployBusinessEntity::getEmployId, employId)
                .eq(CEmployBusinessEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()))
                ;
    }

    public void deleteByEmployId(Integer employId) {
        remove(Wrappers.<CEmployBusinessEntity>lambdaQuery()
                .eq(CEmployBusinessEntity::getEmployId, employId));
    }
}
