package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.mapper.MemoMapper;
import com.navigator.admin.pojo.entity.MemoEntity;
import com.navigator.admin.pojo.vo.MemoVO;
import com.navigator.admin.service.IMemoService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class MemoServiceImpl extends ServiceImpl<MemoMapper, MemoEntity> implements IMemoService {

    @Resource
    private MemoMapper mapper;

    /**
     * 查询用户备忘录
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryMemo(QueryDTO<MemoEntity> queryDTO) {
        Page<MemoEntity> page = new Page<>(queryDTO.getPageNo(),queryDTO.getPageSize());
        IPage<MemoEntity> iPage = mapper.selectPage(page, Wrappers.<MemoEntity>lambdaQuery().eq(MemoEntity::getUserId,
                JwtUtils.getCurrentUserId()).eq(MemoEntity::getIsDeleted,IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(MemoEntity::getCreatedAt));
        List<MemoVO> memoVOList =  BeanConvertUtils.convert2List(MemoVO.class,iPage.getRecords());
//        List<MemoVO> memoVOList =  iPage.getRecords().stream().map(memoEntity -> {
//            return BeanConvertUtils.convert(MemoVO.class,memoEntity);
//        }).collect(Collectors.toList());
        return Result.page(iPage,memoVOList);
    }

    /**
     * 添加备忘录
     * @param memo
     * @return
     */
    @Override
    public Result saveMemo(MemoEntity memo) {
        memo.setUserId(1);
        return Result.success(mapper.insert(memo));
    }
}
