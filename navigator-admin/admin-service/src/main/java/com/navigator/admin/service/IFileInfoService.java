package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.dto.FileBaseInfoDTO;

import java.util.List;

/**
 * <p>
 * 文件基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IFileInfoService{

    /**
     * 保存文件信息
     *
     * @param fileBaseInfoDTO 文件基本信息
     * @return 文件存储结果信息
     */
    FileInfoEntity saveFileInfo(FileBaseInfoDTO fileBaseInfoDTO);

    /**
     * 根据ID获取文件信息
     *
     * @param fileIdList 文件ID集合
     * @return 文件基础信息
     */
    List<FileInfoEntity> getFileListByIds(List<Integer> fileIdList);
}
