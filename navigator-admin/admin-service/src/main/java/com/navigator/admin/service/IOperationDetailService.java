package com.navigator.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.dto.LogDTO;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <p>
 * 操作日志详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IOperationDetailService extends IService<OperationDetailEntity> {

    /**
     * 保存日志详细数据
     *
     * @param operationDetailDTO
     * @return
     */
    void saveOperationDetail(OperationDetailDTO operationDetailDTO);


    /**
     * 记录操作日志并发消息
     *
     * @param operationDetailDTO
     */
    void recordOperationLog(OperationDetailDTO operationDetailDTO);

    List<OperationDetailEntity> queryOperationDetailByReferBizCode(String referBizCode, Integer logLevel);

    /**
     * 记录操作日志并发消息
     *
     * @param operationDetailId
     */
    void noticeOperationDetail(Integer operationDetailId);

    void recordMagellanOperationDetail(RecordOperationDetail recordOperationDetail);

    void recordColumbusOperationDetail(RecordOperationDetail recordOperationDetail);

    Result queryLogList(QueryDTO<LogDTO> queryDTO);

}
