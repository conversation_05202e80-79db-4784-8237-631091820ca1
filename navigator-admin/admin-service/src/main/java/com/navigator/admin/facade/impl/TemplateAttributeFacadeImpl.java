package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.TemplateAttributeFacade;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.admin.service.ITemplateAttributeService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/29 10:59
 */

@RestController
public class TemplateAttributeFacadeImpl implements TemplateAttributeFacade {

    @Autowired
    private ITemplateAttributeService iTemplateAttributeService;


    @Override
    public Result queryTemplateAttributeList(QueryDTO<TemplateAttributeEntity> queryDTO) {
        return iTemplateAttributeService.queryTemplateAttributeList(queryDTO);
    }
}
