package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.TagDetailFacade;
import com.navigator.admin.pojo.entity.TagDetailEntity;
import com.navigator.admin.service.ITagDetailService;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:47
 */
@RestController
public class TagDetailFacadeImpl implements TagDetailFacade {
    @Resource
    private ITagDetailService tagDetailService;

    @Override
    public Result queryTagDetailTagId(Integer tagId) {
        return tagDetailService.queryTagDetailTagId(tagId);
    }

    @Override
    public Result cancelTagDetail(Integer id) {
        return tagDetailService.cancelTagDetail(id);
    }

    @Override
    public Integer saveTagDetail(TagDetailEntity tagDetailEntity) {
        return tagDetailService.saveTagDetail(tagDetailEntity);
    }
}
