package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.service.SiteService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 11:34
 **/
@RestController
public class SiteFacadeImpl implements SiteFacade {
    @Resource
    private SiteService siteService;

    @Override
    public Result querySiteByCondition(QueryDTO<SiteQO> queryDTO) {
        return siteService.querySiteByCondition(queryDTO);
    }

    @Override
    public Result saveSite(SiteEntity siteEntity) {
        return Result.success(siteService.saveSite(siteEntity));
    }

    @Override
    public Result updateSite(SiteEntity siteEntity) {
        return Result.success(siteService.updateSite(siteEntity));
    }

    @Override
    public Result updateSiteStatus(Integer siteId, Integer status) {
        return Result.success(siteService.updateSiteStatus(siteId, status));
    }

    @Override
    public List<SiteEntity> getSiteList(Integer companyId, Integer category2, String syncSystem, Integer status) {
        return siteService.getSiteList(companyId, category2, syncSystem, status);
    }

    @Override
    public SiteEntity getSiteByCode(String siteCode) {
        return siteService.getSiteByCode(siteCode);
    }

    @Override
    public SiteEntity getSiteByCompanyIdAndFactoryCode(Integer companyId, String factoryCode) {
        return siteService.getSiteByCompanyIdAndFactoryCode(companyId, factoryCode);
    }

    @Override
    public SiteEntity getSiteDetailByCode(String siteCode) {
        return siteService.getSiteDetailByCode(siteCode);
    }

    @Override
    public String getSiteCode(SiteQO siteQO) {
        return siteService.getSiteCode(siteQO);
    }

    @Override
    public List<SiteEntity> querySiteList(SiteQO condition) {
        return siteService.querySiteList(condition);
    }

    @Override
    public Set<String> queryFactoryCodeBySyncSystem(String syncSystem) {
        return siteService.queryFactoryCodeBySyncSystem(syncSystem);
    }

    @Override
    public Result<List<String>> getAtlasSiteCodeList() {
        return Result.success(siteService.getAtlasSiteCodeList());
    }

    @Override
    public SiteEntity getSiteBySystemCode(String syncSystem, String bizCode) {
        return siteService.getSiteBySystemCode(syncSystem, bizCode);
    }

    @Override
    public Result importSite(MultipartFile file) {
        return siteService.importSite(file);
    }

    @Override
    public Result<List<SiteEntity>> getSiteListBySyncSystem(String syncSystem) {
        return Result.success(siteService.getSiteListBySyncSystem(syncSystem));
    }
}
