package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.TradeDayCycleDTO;
import com.navigator.admin.pojo.dto.systemrule.TradeDayDTO;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.common.dto.Result;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 贸易节假日表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
public interface ITradeDayService {

    TradeDayEntity getTradeDayByDay(String day);

    Result importTradeDay(List<TradeDayEntity> tradeDayList);

    int getTradeDays(Date startDay, Date endDay);

    int getTradeDays(String startDay, String endDay);

    boolean isTradeDay(String date);

    boolean isTradeDayValue(String date);

    Result importNextYearDays(Integer year);

    List<TradeDayEntity> getTradeDayByMonth(String month);

    List<TradeDayEntity> getTradeDayByYear(Integer year);

    List<String> setNotTrade(List<TradeDayDTO> tradeDayDTOList);

    TradeDayEntity getTradeDayByDayAgo(String dayValue);

    /**
     * 根据日期和二级品类主编码获取开始注销日期、截至注销日期
     *
     * @param dayValue
     * @param category2
     * @return
     */
    TradeDayEntity getTradeDayByDayValueAndCategory2(String dayValue, Integer category2);

    /**
     * 根据期货代码和交易日期获取注销周期
     *
     * @param futureCode
     * @param dayValue
     * @return
     */
    TradeDayCycleDTO getTradeDayCycleDTO(String futureCode, String dayValue);
}
