package com.navigator.admin.service.rule;

import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 14:36
 **/
public interface IRuleVariableService {
    List<RuleVariableEntity> getAllVariableList(Integer isCondition, Integer isKey, String moduleType, String systemId);

    List<RuleVariableEntity> getAllVariableEntityList(String moduleType, String systemId);

    Boolean updateVariable(RuleVariableEntity variableEntity);

    Result queryVariableByCondition(QueryDTO<RuleVariableEntity> queryDTO);

    List<RuleVariableEntity> queryExportVariableList(RuleVariableEntity queryDTO);

    RuleVariableEntity getVariableByCode(String code, String moduleType, String systemId);
}
