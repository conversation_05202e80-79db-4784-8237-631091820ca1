package com.navigator.admin.service.columbus.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.dao.columbus.CEmployCustomerVODao;
import com.navigator.admin.dao.columbus.CEmployDao;
import com.navigator.admin.facade.columbus.CEmployPermissionFacade;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.EmployDTO;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.ResetPasswordDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployBusinessDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.admin.pojo.enums.RoleTypeEnum;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.pojo.vo.columbus.CEmployDetailVO;
import com.navigator.admin.pojo.vo.columbus.CEmployVO;
import com.navigator.admin.pojo.vo.columbus.ColumbusAdminVO;
import com.navigator.admin.service.columbus.*;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.UUIDHexGenerator;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.enums.YqqAuthEnum;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CEmployServiceImpl implements ICEmployService {

    @Resource
    private ICRoleService cRoleService;
    @Resource
    private ICEmployRoleService cEmployRoleService;
    @Resource
    private CEmployDao cEmployDao;
    @Autowired
    private ICRoleDefService cRoleDefService;
    @Autowired
    private ICEmployBusinessService cEmployBusinessService;
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ICEmployService cEmployService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Resource
    private CommonProperties commonProperties;
    @Resource
    private DbtSignatureFacade dbtSignatureFacade;
    @Resource
    private CEmployPermissionFacade cEmployPermissionFacade;
    @Resource
    private ICEmployCustomerService cEmployCustomerService;
    @Resource
    private CEmployCustomerVODao vcEmployCustomerDao;


    @Override
    public IPage<CEmployEntity> getEmployByCondition(QueryDTO<EmployDTO> queryDTO) {
        Page<CEmployEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        ObjectMapper mapper = new ObjectMapper();
        EmployDTO employDTO = mapper.convertValue(queryDTO.getCondition(), EmployDTO.class);
        QueryWrapper<CEmployEntity> queryWrapperEmploy = this.makeQueryWrapper(employDTO);
        // 根据条件查出employs
        IPage<CEmployEntity> entityIPage = cEmployDao.getBaseMapper().selectPage(page, queryWrapperEmploy);
        return entityIPage;
    }

    private QueryWrapper<CEmployEntity> makeQueryWrapper(EmployDTO employDTO) {

        QueryWrapper<CEmployEntity> queryWrapperEmploy = new QueryWrapper<>();
        queryWrapperEmploy.eq("status", DisableStatusEnum.ENABLE.getValue());
        queryWrapperEmploy.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue());
        if (employDTO != null) {
            String name = employDTO.getName();
            if (!StringUtil.isEmpty(name)) {
                queryWrapperEmploy.like("name", '%' + name + '%');
            }
        }
        return queryWrapperEmploy;
    }

    @Override
    public List<CEmployEntity> getEmployByEmployIds(List<Integer> employIds) {
        if (CollectionUtil.isEmpty(employIds)) {
            return null;
        }
        return cEmployDao.queryEmployByIdList(employIds);
    }

    @Override
    public List<CEmployEntity> getEmployByEmail(String email) {
        List<CEmployEntity> employEntityList = cEmployDao.queryEmployByEmail(email);
        return employEntityList;
    }

    @Override
    public List<CEmployEntity> getEmployByPhone(String phone, Integer type) {
        return cEmployDao.getEmployByPhone(phone, type);
    }

    @Override
    public List<CEmployEntity> queryEmployByPhone(String phone) {
        return cEmployDao.queryEmployByPhone(phone);
    }

    @Override
    public CEmployEntity getEmployByNickName(String nickName) {
        return cEmployDao.queryEmployNickName(nickName);
    }

    @Override
    public CEmployEntity getEmployById(Integer id) {
        return cEmployDao.queryEmployById(id);
    }

    @Override
    public CEmployEntity ifNotExistToSave(CEmployEntity CEmployEntity) {
        List<CEmployEntity> employList = this.getEmployByEmailOrPhone(CEmployEntity.getEmail(), CEmployEntity.getPhone());
//        if (CollectionUtils.isEmpty(employList)) {
//            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
////            CEmployEntity.setCreatedAt(DateTimeUtil.now())
////                    .setUpdatedAt(DateTimeUtil.now());
////            cEmployDao.getBaseMapper().insert(CEmployEntity);
////            return CEmployEntity;
//        }
        return CollectionUtils.isEmpty(employList) ? null : employList.get(0);
    }

    @Override
    public List<CEmployEntity> getEmployByRoleName(String roleName) {
        List<CRoleEntity> roleEntityList = cRoleService.getRoleByRoleName(roleName);
        List<Integer> roleIdList = roleEntityList.stream().map(CRoleEntity::getId).collect(Collectors.toList());
        List<CEmployRoleEntity> employRoleEntities = cEmployRoleService.getEmployRolesByRoleIds(roleIdList);
        List<Integer> employIds = employRoleEntities.stream().map(CEmployRoleEntity::getEmployId).collect(Collectors.toList());
        List<CEmployEntity> employEntities = cEmployDao.getBaseMapper().selectList(new LambdaQueryWrapper<CEmployEntity>()
                .in(CEmployEntity::getId, employIds)
                .eq(CEmployEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return employEntities;
    }

    @Override
    public List<CEmployEntity> queryEmployByCompanyId(Integer companyId) {
        List<CEmployEntity> employEntityList = cEmployDao.queryEmployByCompanyId(companyId);
        return CollectionUtil.isNotEmpty(employEntityList) ? employEntityList : Collections.emptyList();
    }

    @Override
    public Result modifyPassword(CEmployEntity CEmployEntity) {
        int update = cEmployDao.getBaseMapper().updateById(CEmployEntity);
        return update > 0 ? Result.success() : Result.failure();
    }

    @Override
    public boolean updateEmployResetPassword(Integer id) {
        CEmployEntity cEmployEntity = cEmployDao.getById(id);

        if (null != cEmployEntity) {
            MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
            messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_COLUMBUS_RESET_PWD.name());
            Map<String, String> mapData = new HashMap<>();

            String password = "DF" + UUIDHexGenerator.randomCoding(4);
            cEmployEntity.setPassword(new BCryptPasswordEncoder().encode(password))
                    .setUpdatedPasswordTime(DateTimeUtil.now());
            cEmployDao.updateById(cEmployEntity);
            /*//todo 未配置邮箱模板 wan
            SendMessageDTO sendMessageDTO = new SendMessageDTO();
            ReceiverContactVO receiverContactVO = new ReceiverContactVO();
            receiverContactVO.setEmail(cEmployDaoById.getEmail());
            sendMessageDTO.setReceiverContactVO(receiverContactVO)
                    .setTitle("修改密码")
                    .setSendContent("新密码为:" + password);*/

            mapData.put("password", password);
            messageInfoDTO.setDataMap(mapData);
            messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
            List<String> receivers = new ArrayList<>();
            receivers.add(cEmployEntity.getId().toString());
            messageInfoDTO.setReceivers(receivers);

            messageFacade.sendMessage(messageInfoDTO);

            return true;
        }

        return false;
    }

    @Override
    public CEmployEntity queryEmployByCustomerId(Integer customerId) {
        return cEmployDao.queryEmployByCustomerId(customerId);
    }

    @Override
    public Result saveOrUpdateEmploy(CEmployBusinessDTO employBusinessDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CEmployEntity cEmployEntity = new CEmployEntity();

        CustomerEntity customerEntity = customerFacade.queryCustomerById(employBusinessDTO.getCustomerId());
        //判断是新增还是编辑
        if (employBusinessDTO.getEmployId() == null) {

            List<CEmployEntity> employEntityList = cEmployDao.getEmployByPhone(employBusinessDTO.getPhone(), null);
            //新增查看手机号是否已经存在
            if (!employEntityList.isEmpty()) {

                //case:1002696 修改禁用状态的用户，保存后，新增一条重复用户 Author:Wan 2024-07-09 start
                List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.getCEmployCustomerByEmployIdAndCustomerId(employEntityList.get(0).getId(), employBusinessDTO.getCustomerId());
                //case:1002696 修改禁用状态的用户，保存后，新增一条重复用户 Author:Wan 2024-07-09 end
                if (!cEmployCustomerEntities.isEmpty()) {
                    throw new BusinessException(ResultCodeEnum.C_EMPLOY_CUSTOMER_BINDING);
                }
                cEmployEntity = employEntityList.get(0);

                addCEmployCustomerBinding(cEmployEntity, customerEntity);

                //修改账号信息
                cEmployEntity
                        .setName(employBusinessDTO.getEmployName())
                        .setEmail(employBusinessDTO.getEmail())
                        .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                        .setUpdatedAt(new Date())
                ;
                cEmployDao.updateById(cEmployEntity);

                //throw new BusinessException(ResultCodeEnum.PHONE_NOT_ONLY_ONE);
            } else {
                String password = new BCryptPasswordEncoder().encode("12345678910abcD");
                cEmployEntity.setType(RoleTypeEnum.COMMON.getValue())
                        .setPassword(password)
                        .setCreatedBy(userId)
                        .setCreatedAt(new Date())
                        .setUpdatedPasswordTime(DateTimeUtil.now());
            }
        }

        if (employBusinessDTO.getEmployId() != null) {
            cEmployEntity = cEmployDao.queryEmployById(employBusinessDTO.getEmployId());
            if (null == cEmployEntity) {
                throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_EXISTED);
            }
            List<CEmployEntity> employEntityList = cEmployDao.getEmployByPhone(employBusinessDTO.getPhone(), null);
            if ((employEntityList != null && employEntityList.size() > 1) || (employEntityList != null && employEntityList.size() == 1 && !employEntityList.get(0).getId().equals(employBusinessDTO.getEmployId()))) {
                throw new BusinessException(ResultCodeEnum.PHONE_NOT_ONLY_ONE);
            }
        }
        CEmployEntity employEntity = cEmployDao.getById(userId);
        //CustomerEntity customerEntity = customerFacade.queryCustomerById(employEntity.getCustomerId());
        Integer parentCustomerId = null;
        if (null != customerEntity) {
            //CustomerVO customerVO = JSON.parseObject(JSON.toJSONString(result.getData()), CustomerVO.class);
            parentCustomerId = customerEntity.getParentId();
            cEmployEntity.setEnterpriseName(customerEntity.getEnterpriseName());
            cEmployEntity.setCustomerName(customerEntity.getName());
            cEmployEntity.setCustomerCode(customerEntity.getLinkageCustomerCode());
        }

        cEmployEntity
                .setId(employBusinessDTO.getEmployId())
                .setName(employBusinessDTO.getEmployName() == null ? null : employBusinessDTO.getEmployName().trim())
                .setStatus(employBusinessDTO.getStatus())
                .setPhone(employBusinessDTO.getPhone() == null ? null : employBusinessDTO.getPhone().trim())
                .setEmail(employBusinessDTO.getEmail())
                .setCustomerId(employEntity.getCustomerId())
                .setParentCustomerId(parentCustomerId)
                .setRootCustomerId(parentCustomerId)
                .setUpdatedBy(userId)
                .setUpdatedAt(new Date())


        ;


        List<CEmployEntity> cEmployEntities = cEmployDao.queryEmployByPhone(employBusinessDTO.getPhone());
        if (null != cEmployEntity.getId()) {
            //id为空的时候修改数据
            cEmployDao.updateById(cEmployEntity);
        } else if (cEmployEntities.isEmpty()) {
            //id为空切手机号未注册,新增数据
            cEmployDao.save(cEmployEntity);
        }

        //case:1002696 修改禁用状态的用户，保存后，新增一条重复用户 Author:Wan 2024-07-09 start
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.getCEmployCustomerByEmployIdAndCustomerId(cEmployEntity.getId(), customerEntity.getId());
        //case:1002696 修改禁用状态的用户，保存后，新增一条重复用户 Author:Wan 2024-07-09 end
        if (cEmployCustomerEntities.isEmpty()) {
            addCEmployCustomerBinding(cEmployEntity, customerEntity);
        }
        if (employBusinessDTO.getEmployId() == null) {
            return Result.success();
        }

        cEmployRoleService.deleteByEmployIdAndCustomerId(employBusinessDTO.getEmployId(), employBusinessDTO.getCustomerId());

        Map<Integer, List<Integer>> roleMap = employBusinessDTO.getRoleMap();
        List<CRoleEntity> roleEntityList = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : roleMap.entrySet()) {
            List<CRoleEntity> roleEntityList1 = cRoleService.queryByIdList(entry.getValue());
            roleEntityList.addAll(roleEntityList1);
            for (CRoleEntity cRoleEntity : roleEntityList1) {
                CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
                cEmployRoleEntity
                        .setEmployId(employBusinessDTO.getEmployId())
                        .setRoleDefId(cRoleEntity.getRoleDefId())
                        .setRoleId(cRoleEntity.getId())
                        .setCustomerId(entry.getKey())
                        .setCreatedBy(userId)
                        .setUpdatedBy(userId)
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date())
                ;
                cEmployRoleService.save(cEmployRoleEntity);
            }
        }

        List<Integer> categoryIdList = roleEntityList.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        }
        List<Integer> salesTypeList = roleEntityList.stream().map(CRoleEntity::getSalesType).distinct().collect(Collectors.toList());
        if (salesTypeList.contains(0)) {
            salesTypeList = Arrays.asList(ContractSalesTypeEnum.SALES.getValue(), ContractSalesTypeEnum.PURCHASE.getValue());
        }
        cEmployBusinessService.deleteByEmployId(employBusinessDTO.getEmployId());
        for (Integer categoryId : categoryIdList) {
            for (Integer saleType : salesTypeList) {
                CEmployBusinessEntity cEmployBusinessEntity = new CEmployBusinessEntity();
                cEmployBusinessEntity.setCategoryId(categoryId)
                        .setSalesType(saleType)
                        .setEmployId(employBusinessDTO.getEmployId())
                        .setCreatedBy(userId)
                        .setUpdatedBy(userId)
                ;
                cEmployBusinessService.save(cEmployBusinessEntity);
            }
        }
        return Result.success();
    }

    public void addCEmployCustomerBinding(CEmployEntity cEmployEntity, CustomerEntity customerEntity) {
        //取消原绑定的手机号
        /*List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerByEmployIdAndCustomerId(systemAndCustomerDTO.getEmployId(), systemAndCustomerDTO.getCustomerId());
        for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {
            //删除绑定数据
            cEmployCustomerEntity.setIsDeleted(IsDeletedEnum.DELETED.getValue());
            cEmployCustomerService.updateCEmployCustomer(cEmployCustomerEntity);
        }*/
        //查询新手机号的账号
        //将新手机号进行绑定
        CEmployCustomerEntity cEmployCustomerEntity = new CEmployCustomerEntity();
        cEmployCustomerEntity
                .setParentCustomerId(customerEntity.getParentId())
                .setCustomerId(customerEntity.getId())
                .setCEmployId(cEmployEntity.getId())
                .setStatus(cEmployEntity.getStatus())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setType(CEmployTypeEnum.OTHER.getType())
                .setUpdatedAt(new Date())
                .setCreatedAt(new Date())
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));

        cEmployCustomerService.saveCEmployCustomer(cEmployCustomerEntity);
        //return cEmployEntity.getId();
    }


    @Override
    public Result queryEmployList(QueryDTO<CEmployDTO> queryDTO) {
        Integer beginEmployId = 100000;
        if (null != commonProperties.getColumbusBeginEmployId()) {
            beginEmployId = commonProperties.getColumbusBeginEmployId();
        }
        Page<CEmployCustomerVOEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        CEmployDTO employDTO = queryDTO.getCondition();
        Integer customerId = employDTO.getCustomerId();
        employDTO.setCustomerId(customerId);
        //分页查询
        IPage<CEmployCustomerVOEntity> iPage = vcEmployCustomerDao.queryPageByQueryDTO(page, employDTO, beginEmployId);

        List<CEmployVO> list = iPage.getRecords().stream().map(i -> {
            CEmployVO cEmployVO = new CEmployVO();
            List<CEmployRoleEntity> employRolesList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(i.getCEmployId(), i.getCustomerId());
            List<CRoleDefEntity> cRoleDefEntities = cRoleDefService.queryRoleDefForbidden();
            List<Integer> forbiddenRoleDefIdList = cRoleDefEntities.stream().map(CRoleDefEntity::getId).collect(Collectors.toList());
            List<Integer> roleIdList = employRolesList.stream().filter(k -> !forbiddenRoleDefIdList.contains(k.getRoleDefId())).map(CEmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(roleIdList) && roleIdList.contains(1)) {
                cEmployVO.setAdminStatus(1);
            } else {
                cEmployVO.setAdminStatus(0);
            }
            List<CRoleEntity> roleEntityList = cRoleService.queryByIdList(roleIdList);
            List<String> roleNameList = roleEntityList.stream().map(k -> {
                String name = "";
                if (k.getCategoryId() != 0) {
                    name = GoodsCategoryEnum.getDesc(k.getCategoryId()) + "-";
                }
                if (k.getSalesType() != 0) {
                    name = name + ContractSalesTypeEnum.getDescByValue(k.getSalesType()) + "-";
                }
                name = name + k.getName();
                return name;
            }).distinct().collect(Collectors.toList());
            cEmployVO
                    .setId(i.getCEmployId())
                    .setName(i.getEmployName())
                    .setPhone(i.getPhone())
                    .setRoleName(roleNameList)
                    .setStatus(i.getStatus())
                    .setUpdatedAt(i.getUpdatedAt())
                    .setEmail(i.getEmail())
                    .setType(i.getType())
                    .setCustomerId(i.getCustomerId())
            ;
            CEmployEntity employEntity = cEmployService.getEmployById(i.getUpdatedBy());
            if (employEntity != null) {
                cEmployVO.setUpdatedBy(employEntity.getName());
            }
            if (CEmployTypeEnum.DEFAULT.getType().equals(i.getType())) {
                cEmployVO.setIsAdmin("是");
            } else {
                cEmployVO.setIsAdmin("否");
            }
            return cEmployVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);
    }

    @Override
    public CEmployDetailVO queryEmployDetail(Integer employId, Integer customerId) {
        CEmployDetailVO employDetailVO = new CEmployDetailVO();
        CEmployEntity cEmployEntity = cEmployDao.queryEmployById(employId);
        if (null == cEmployEntity) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_EXISTED);
        }
        List<CRoleDefEntity> cRoleDefEntities = cRoleDefService.queryAllList();
        Map<Integer, String> roleDefNameMap = cRoleDefEntities.stream().collect(Collectors.toMap(CRoleDefEntity::getId, CRoleDefEntity::getName, (k1, k2) -> k1));

        List<CEmployRoleEntity> employRolesList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(employId, customerId);
        List<Integer> customerIdList = employRolesList.stream().map(CEmployRoleEntity::getCustomerId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerIdList)) {
            customerIdList = Arrays.asList(customerId);
        }
        Result result = customerFacade.queryCustomerByIdList(customerIdList);
        if (result.getCode() != ResultCodeEnum.OK.getCode()) {
            throw new BusinessException();
        }
        List<CustomerEntity> customerEntities = JSON.parseArray(JSON.toJSONString(result.getData()), CustomerEntity.class);
        Map<Integer, String> customerNameMap = customerEntities.stream().collect(Collectors.toMap(CustomerEntity::getId, CustomerEntity::getName, (k1, k2) -> k1));
//        Map<Integer, Integer> collect = employRolesList.stream().collect(Collectors.toMap(CEmployRoleEntity::getCustomerId, CEmployRoleEntity::getRoleId, (k1, k2) -> k1));
        Map<Integer, List<CEmployRoleEntity>> map = employRolesList.stream().collect(Collectors.groupingBy(CEmployRoleEntity::getCustomerId));
        List<CEmployDetailVO.RoleEmploy> roleDefList = map.entrySet().stream().map(i -> {
            List<Integer> roleDefIdList = i.getValue().stream().map(CEmployRoleEntity::getRoleDefId).collect(Collectors.toList());
            List<CEmployDetailVO.RoleDetail> roleDetailDefList1 = roleDefIdList.stream().map(k -> {
                CEmployDetailVO.RoleDetail roleDetail = new CEmployDetailVO.RoleDetail();
                roleDetail
                        .setCustomerId(i.getKey())
                        .setCustomerName(customerNameMap.get(i.getKey()))
                        .setRoleDefId(k)
                        .setRoleDefName(roleDefNameMap.get(k));


                return roleDetail;
            }).distinct().collect(Collectors.toList());

            CEmployDetailVO.RoleEmploy roleEmploy = new CEmployDetailVO.RoleEmploy();
            roleEmploy
                    .setCustomerId(i.getKey())
                    .setCustomerName(customerNameMap.get(i.getKey()))
                    .setRoleDetailList(roleDetailDefList1)
            ;
            if (customerId.equals(i.getKey())) {
                roleEmploy.setCustomerType(0);
            } else {
                roleEmploy.setCustomerType(1);
            }
            return roleEmploy;
        }).distinct().collect(Collectors.toList());

        List<CEmployDetailVO.RoleEmploy> roleList = map.entrySet().stream().map(i -> {
            List<CEmployDetailVO.RoleDetail> roleDetailList1 = i.getValue().stream().map(k -> {
                CEmployDetailVO.RoleDetail roleDetail = new CEmployDetailVO.RoleDetail();
                CRoleEntity cRoleEntity = cRoleService.getRoleById(k.getRoleId());
                roleDetail
                        .setCustomerId(i.getKey())
                        .setCustomerName(customerNameMap.get(i.getKey()))
                        .setRoleDefId(k.getRoleDefId())
                        .setRoleDefName(roleDefNameMap.get(k.getRoleDefId()))
                        .setRoleId(k.getRoleId())
                        .setName(cRoleEntity.getName())
                ;
                if (k.getRoleId() != null && k.getRoleId() == 1) {
                    employDetailVO.setAdminStatus(1);
                }
                String name = "";
                if (cRoleEntity.getCategoryId() != 0) {
                    name = GoodsCategoryEnum.getDesc(cRoleEntity.getCategoryId()) + "-";
                }
                if (cRoleEntity.getSalesType() != 0) {
                    name = name + ContractSalesTypeEnum.getDescByValue(cRoleEntity.getSalesType()) + "-";
                }
                name = name + cRoleEntity.getName();
                roleDetail.setName(name);
                return roleDetail;
            }).distinct().collect(Collectors.toList());

            CEmployDetailVO.RoleEmploy roleEmploy = new CEmployDetailVO.RoleEmploy();
            roleEmploy
                    .setCustomerId(i.getKey())
                    .setCustomerName(customerNameMap.get(i.getKey()))
                    .setRoleDetailList(roleDetailList1)
            ;
            return roleEmploy;
        }).distinct().collect(Collectors.toList());

        Result result1 = customerFacade.querySonCustomerList(customerId);
        if (result1.getCode() != ResultCodeEnum.OK.getCode()) {
            throw new BusinessException();
        }

        List<CEmployDetailVO.RoleEmploy> customerList = new ArrayList<>();
        CEmployDetailVO.RoleEmploy roleEmploy = new CEmployDetailVO.RoleEmploy();
        roleEmploy
                .setCustomerId(customerId)
                .setCustomerName(customerNameMap.get(customerId))
                .setCustomerType(0);
        customerList.add(roleEmploy);
        List<CustomerEntity> customerEntities1 = JSON.parseArray(JSON.toJSONString(result1.getData()), CustomerEntity.class);
        if (CollectionUtils.isNotEmpty(customerEntities1)) {
            List<CEmployDetailVO.RoleEmploy> list = customerEntities1.stream().map(i -> {
                CEmployDetailVO.RoleEmploy roleEmploy1 = new CEmployDetailVO.RoleEmploy();
                roleEmploy1
                        .setCustomerId(i.getId())
                        .setCustomerName(i.getName())
                        .setCustomerType(1);
                return roleEmploy1;
            }).distinct().collect(Collectors.toList());
            customerList.addAll(list);
        }
        if (CollectionUtils.isEmpty(roleDefList)) {
            roleDefList = customerList;
        }
        employDetailVO
                .setEmail(cEmployEntity.getEmail())
                .setName(cEmployEntity.getName())
                .setPhone(cEmployEntity.getPhone())
                .setStatus(cEmployEntity.getStatus())
                .setRoleDefList(roleDefList)
                .setRoleList(roleList)
                .setCustomerList(customerList)
                .setType(cEmployEntity.getType())
                .setSignatureTime(cEmployEntity.getSignatureTime())
        ;
        return employDetailVO;
    }

    @Override
    public List<CEmployVO> queryEmployListByRoleDefId(Integer roleDefId) {
        QueryDTO<CEmployDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        queryDTO.setCondition(new CEmployDTO());
        Result result = queryEmployList(queryDTO);
        List<CEmployVO> employList = (List<CEmployVO>) result.getData();
        List<CEmployRoleEntity> employRoleEntities = cEmployRoleService.queryByRoleDefId(roleDefId);
        List<Integer> employIdList = employRoleEntities.stream().map(CEmployRoleEntity::getEmployId).collect(Collectors.toList());
        List<CEmployVO> list = employList.stream()
                .filter(i -> employIdList.contains(i.getId()))
                .map(i -> {
                    List<CEmployRoleEntity> employRolesByEmploy = cEmployRoleService.getEmployRolesByEmploy(String.valueOf(i.getId()));
                    List<Integer> roleIdList = employRolesByEmploy.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
                    List<CRoleEntity> roleEntities = cRoleService.queryByIdList(roleIdList);
                    List<Integer> categoryIdList = roleEntities.stream().map(CRoleEntity::getCategoryId).collect(Collectors.toList());
                    List<String> categoryNameList;
                    if (categoryIdList.contains(0)) {
                        categoryNameList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getDesc(), GoodsCategoryEnum.OSM_OIL.getDesc());
                    } else {
                        categoryNameList = categoryIdList.stream().map(GoodsCategoryEnum::getDescByValue).distinct().filter(Objects::nonNull).collect(Collectors.toList());
                    }
                    i.setCategoryNameList(categoryNameList);
                    return i;
                }).collect(Collectors.toList());

        return list;
    }

    @Override
    public String resetPassword(Integer employId) {
        CEmployEntity CEmployEntity = cEmployDao.getById(employId);
        if (null == CEmployEntity) {
            throw new BusinessException(ResultCodeEnum.ROLE_NOT_EXISTED);
        }
//        String password = "DF" + UUIDHexGenerator.randomCoding(4);
        String password = "111111";
        CEmployEntity.setPassword(new BCryptPasswordEncoder().encode(password))
                .setUpdatedPasswordTime(DateTimeUtil.now());
        cEmployDao.updateById(CEmployEntity);


        //todo 发送短信邮箱信息
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_COLUMBUS_RESET_PWD.name());

        List<String> receivers = new ArrayList<>();
        receivers.add(String.valueOf(employId));
        messageInfoDTO.setReceivers(receivers);
        Map<String, Object> map = new HashMap<>();
        map.put("password", password);

        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
        log.info("resetEmployPassword success! password:{}", password);

        return password;
    }

    @Override
    public List<CEmployVO> queryAvailableEmployByRoleDefId(Integer roleDefId) {
        QueryDTO<CEmployDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        queryDTO.setCondition(new CEmployDTO());
        Result result = queryEmployList(queryDTO);
        List<CEmployVO> employList = (List<CEmployVO>) result.getData();
        List<CEmployVO> usedList = queryEmployListByRoleDefId(roleDefId);
        if (CollectionUtils.isEmpty(usedList)) {
            return employList;
        }
        List<Integer> usedIdList = usedList.stream().map(CEmployVO::getId).collect(Collectors.toList());
        return employList.stream().filter(i -> !usedIdList.contains(i.getId())).collect(Collectors.toList());
    }

    @Override
    public CategoryFactoryMenuVO queryCategoryFactoryByRole() {
        CEmployRoleDTO employRoleDTO = new CEmployRoleDTO();
        employRoleDTO.setType("1");
        List<CRoleDefEntity> roleDefEntityList = cRoleDefService.getRoleDefByType(employRoleDTO.getType());
        List<Integer> roleDefIdList = roleDefEntityList.stream().map(CRoleDefEntity::getId).collect(Collectors.toList());
        List<CRoleEntity> roleEntityList = cRoleService.queryRole(employRoleDTO);
        List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
        Map<Integer, String> nameMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getId, FactoryEntity::getShortName));
        CategoryFactoryMenuVO categoryFactoryMenuVO = new CategoryFactoryMenuVO();


        List<Integer> categoryIdList = roleEntityList.stream().filter(i -> roleDefIdList.contains(i.getRoleDefId())).map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        }

        List<CategoryFactoryMenuVO.Category> categoryList = categoryIdList.stream().map(i -> {
            CategoryFactoryMenuVO.Category category = new CategoryFactoryMenuVO.Category();
            category.setCategoryId(i);
            category.setCategoryName(GoodsCategoryEnum.getDescByValue(i));
            return category;
        }).collect(Collectors.toList());
        categoryFactoryMenuVO.setCategoryList(categoryList);
        return categoryFactoryMenuVO;
    }

    @Override
    public Result updateEmployStatus(CEmployDTO employDTO) {

        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.getCEmployCustomerByEmployIdAndCustomerId(Integer.parseInt(employDTO.getId()), employDTO.getCustomerId());

        for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {

            cEmployCustomerEntity.setStatus(Integer.parseInt(employDTO.getStatus()));
            cEmployCustomerService.updateCEmployCustomer(cEmployCustomerEntity);
        }

        //cEmployDao.updateEmployStatus(employDTO);
        return Result.success();
    }

    @Override
    public Result saveEmployStatus(CEmployEntity cEmployEntity) {

        CEmployEntity entity = cEmployDao.queryEmployByCustomerId(cEmployEntity.getCustomerId());

        if (null != entity) {
            return Result.success(false);
        }

        cEmployDao.save(cEmployEntity);
        return Result.success();
    }

    @Override
    public Result resetUserPassword(ResetPasswordDTO resetPasswordDTO) {

        CEmployEntity CEmployEntity = new CEmployEntity();
        if (StrUtil.isNotBlank(resetPasswordDTO.getEmployId())) {
            CEmployEntity = cEmployDao.queryEmployById(Integer.parseInt(resetPasswordDTO.getEmployId()));
        } else {
            CEmployEntity = cEmployDao.queryEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        }


        if (CEmployEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        if (!CEmployEntity.getPhone().equalsIgnoreCase(resetPasswordDTO.getPhone())) {
            throw new BusinessException(ResultCodeEnum.PHONE_NOT_SAME);
        }
        String key = RedisConstants.C_USER_RESET_PASSWORD_CODE + resetPasswordDTO.getPhone();
        String redisCode = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(redisCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_NOT);
        }
        if (!redisCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
//            if (!timeCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_ERROR1);
//            }
        }
        String newPassword = new BCryptPasswordEncoder().encode(resetPasswordDTO.getPassword());

        /*if(newPassword.equals(CEmployEntity.getPassword())){
            throw new BusinessException(ResultCodeEnum.PASSWORD_THE_SAME);
        }*/

        CEmployEntity.setPassword(newPassword)
                .setUpdatedPasswordTime(DateTimeUtil.now());
        log.info("columbus user resetPassword success, newPassword:{}", newPassword);
        cEmployDao.updateById(CEmployEntity);
        return Result.success();
    }

    @Override
    public Result sendResetPasswordCode(String mobileNo) {
        String currentUserId = JwtUtils.getCurrentUserId();
        CEmployEntity CEmployEntity = cEmployDao.queryEmployById(Integer.parseInt(currentUserId));
        if (CEmployEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        if (!CEmployEntity.getPhone().equalsIgnoreCase(mobileNo)) {
            throw new BusinessException(ResultCodeEnum.PHONE_NOT_SAME);
        }
        return Result.success();
    }

    @Override
    public Result resetNotLogUserPassword(ResetPasswordDTO resetPasswordDTO) {
        List<CEmployEntity> cEmployEntity = cEmployDao.getEmployByPhone(resetPasswordDTO.getPhone(), null);
        if (cEmployEntity.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        String key = RedisConstants.C_USER_RESET_PASSWORD_CODE + resetPasswordDTO.getPhone();
        String redisCode = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(redisCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_NOT);
        }
        if (!redisCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
//            if (!timeCode.equalsIgnoreCase(resetPasswordDTO.getVerifyCode())) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_ERROR1);
//            }
        }
        String newPassword = new BCryptPasswordEncoder().encode(resetPasswordDTO.getPassword());
        /*if(newPassword.equals(cEmployEntity.get(0).getPassword())){
            throw new BusinessException(ResultCodeEnum.PASSWORD_THE_SAME);
        }*/
        cEmployEntity.get(0).setPassword(newPassword)
                .setUpdatedPasswordTime(DateTimeUtil.now());
        log.info("columbus user resetPassword success, newPassword:{}", newPassword);
        cEmployDao.updateById(cEmployEntity.get(0));
        return Result.success(cEmployEntity.get(0));
    }

    @Override
    public Result sendResetPasswordPhoneCode(String mobileNo) {
        List<CEmployEntity> CEmployEntity = cEmployDao.getEmployByPhone(mobileNo, null);
        if (CEmployEntity.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        return Result.success();
    }

    @Override
    public Result sendAadCode(String mobileNo) {
        List<CEmployEntity> employEntityList = cEmployService.getEmployByPhone(mobileNo, null);
        if (CollectionUtils.isEmpty(employEntityList)) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        List<CEmployEntity> employEntities = employEntityList.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
        if (employEntities.size() == 0) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
        }
        if (employEntities.size() > 1) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_ONLY_ONE);
        }
        return Result.success();
    }

    @Override
    public Result verifyAadCode(LoginDTO loginDTO) {
        String key = RedisConstants.M_AAD_LOGIN_PASSWORD_CODE + loginDTO.getPhone();
        String redisCode = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(redisCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_NOT);
        }
        if (!redisCode.equalsIgnoreCase(loginDTO.getCaptcha())) {
//            if (!timeCode.equalsIgnoreCase(loginDTO.getCaptcha())) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_ERROR1);
//            }
        }
        return Result.success();
    }

    @Override
    public void updateSignature(CEmployEntity cEmployEntity) {
        cEmployDao.saveOrUpdate(cEmployEntity);
    }

    @Override
    public PermissionBO queryPermissionByEmployId(String employId, Integer categoryId) {
        PermissionBO permissionBO = new PermissionBO();
        List<CustomerEntity> customerEntities = customerFacade.queryFactoryCustomer();
        Map<Integer, List<Integer>> allPermissionMap = customerEntities
                .stream()
                .collect(Collectors.groupingBy(CustomerEntity::getCompanyId, Collectors.mapping(CustomerEntity::getId, Collectors.toList())));
        List<Integer> allCustomerIdList = customerEntities.stream().map(CustomerEntity::getId).distinct().collect(Collectors.toList());
        List<Integer> allCategoryIdList = categoryFacade.queryCategoryIdList(new CategoryQO().setLevel(2));
        ;

        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmploy(employId);
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());

        // 系统管理员拥有全部权限
        if (BigDecimal.TEN.compareTo(new BigDecimal(employId)) >= 0
                || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            permissionBO.setCustomerIdList(allCustomerIdList);
            permissionBO.setCategoryIdList(allCategoryIdList);
            permissionBO.setPermissionMap(allPermissionMap);
            return permissionBO;
        }

        List<CRoleEntity> roleEntityList = cRoleService.queryByIdListAndCategory(roleIdList, categoryId);
        Map<Integer, List<Integer>> permissionMap = new HashMap<>();
        Map<Integer, List<CRoleEntity>> collect = roleEntityList.stream().collect(Collectors.groupingBy(CRoleEntity::getCategoryId));
        for (Map.Entry<Integer, List<CRoleEntity>> entry : collect.entrySet()) {
            permissionMap.put(entry.getKey(), entry.getValue().stream().map(CRoleEntity::getId).collect(Collectors.toList()));
        }

        List<Integer> categoryIdList = roleEntityList.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = allCategoryIdList;
        }
        permissionBO.setPermissionMap(permissionMap);
        permissionBO.setCategoryIdList(categoryIdList);
        return permissionBO;
    }

    @Override
    public Result importEmploy(MultipartFile file) {
//        List<ImportEmploy> importEmployList = EasyPoiUtils.importExcel(file, 0, 1, ImportEmploy.class);
//        for (ImportEmploy employ : importEmployList) {
//            if (employ.getEmail() == null) {
//                continue;
//            }
//            String fatherDepartment = employ.getFatherDepartment();
//            DepartmentEntity departmentEntity = departmentService.getDepartmentEntityByName(fatherDepartment);
//            if (departmentEntity == null) {
//                DepartmentEntity departmentEntity1 = new DepartmentEntity();
//                departmentEntity1.setName(fatherDepartment);
//                departmentEntity = departmentService.save(departmentEntity1);
//            }
//
//            //employ
//            List<CEmployEntity> employEntityList = cEmployService.getEmployByEmail(employ.getEmail().trim());
//            CEmployEntity cEmployEntity = new CEmployEntity();
//            if (CollectionUtil.isNotEmpty(employEntityList)) {
//                cEmployEntity = employEntityList.get(0);
//            }
//            cEmployEntity
//                    .setName(employ.getName().trim())
//                    .setPassword(new BCryptPasswordEncoder().encode("111111".trim()))
//                    .setRealName(employ.getName().trim())
//                    .setNickName(StringUtils.isNotBlank(employ.getLkgCode()) ? employ.getLkgCode().trim() : null)
//                    .setPhone(employ.getPhone().trim())
//                    .setEmail(employ.getEmail().trim())
//                    .setWorkNo(StringUtils.isNotBlank(employ.getMicoId()) ? employ.getMicoId().trim() : null)
//                    .setCreatedAt(new Date())
//                    .setUpdatedAt(new Date())
//            ;
//            if (StringUtils.isNotBlank(employ.getEmployId())) {
//                cEmployEntity.setId(Integer.parseInt(employ.getEmployId()));
//            }
//            cEmployDao.saveOrUpdate(cEmployEntity);
//
//            Integer roleDefId = null;
//            //创建roleDef
//            if (employ.getRole() != null) {
//                CRoleDefEntity CRoleDefEntity = cRoleDefService.getRoleDefByName(employ.getRole().trim());
//                if (CRoleDefEntity == null) {
//                    CRoleDTO roleDTO = new CRoleDTO();
//                    roleDTO.setName(employ.getRole())
//                            .setRoleType(RoleTypeEnum.COMMON.getValue())
//                            .setImportStatus(1)
//                    ;
//                    Result result = cRoleFacadeImpl.saveOrUpdateRole(roleDTO);
//                    roleDefId = (Integer) result.getData();
//                } else {
//                    roleDefId = CRoleDefEntity.getId();
//                }
//            }
//
//            List<Integer> categoryIdList = new ArrayList<>();
//
//            if (StringUtils.isBlank(employ.getCategory()) || "ALL".equalsIgnoreCase(employ.getCategory().trim())) {
//                List<CategoryEntity> categoryList = categoryFacade.getAllCategoryList(2);
//                categoryIdList = categoryList.stream().map(CategoryEntity::getId).collect(Collectors.toList());
//            } else {
//                if (employ.getCategory().contains(GoodsCategoryEnum.OSM_MEAL.getDesc())) {
//                    categoryIdList.add(GoodsCategoryEnum.OSM_MEAL.getValue());
//                }
//                if (employ.getCategory().contains(GoodsCategoryEnum.OSM_OIL.getDesc())) {
//                    categoryIdList.add(GoodsCategoryEnum.OSM_OIL.getValue());
//                }
//            }
//
//            List<Integer> factoryIdList = new ArrayList<>();
//            List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(null);
//            Map<String, Integer> factoryMap = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(factoryEntityList)) {
//                factoryMap = factoryEntityList.stream().collect(Collectors.toMap(FactoryEntity::getCode, FactoryEntity::getId, (k1, k2) -> k1));
//            }
//            if (StringUtils.isBlank(employ.getFactoryCode()) || "ALL".equalsIgnoreCase(employ.getFactoryCode().trim())) {
//                factoryIdList = factoryEntityList.stream().map(FactoryEntity::getId).collect(Collectors.toList());
//            } else {
//                String[] split = employ.getFactoryCode().split("/");
//                for (int i = 0; i < split.length; i++) {
//                    Integer factoryId = factoryMap.get(split[i]);
//                    if (factoryId != null) {
//                        factoryIdList.add(factoryId);
//                    }
//                }
//            }
//
//            //employBusiness
//            cEmployBusinessService.deleteByEmployId(cEmployEntity.getId());
//            for (Integer categoryId : categoryIdList) {
//                for (Integer factoryId : factoryIdList) {
//                    CEmployBusinessEntity CEmployBusinessEntity = new CEmployBusinessEntity();
//                    CEmployBusinessEntity
//                            .setCategoryId(categoryId)
//                            .setEmployId(cEmployEntity.getId())
//                    ;
//                    cEmployBusinessService.save(CEmployBusinessEntity);
//                }
//            }
//
//
//            CEmployRoleDTO employRoleDTO = new CEmployRoleDTO();
//            employRoleDTO.setRoleDefId(roleDefId)
//                    .setCategoryIdList(categoryIdList)
//                    .setFactoryIdList(factoryIdList);
//            List<CRoleEntity> roleEntityList = cRoleService.queryRole(employRoleDTO);
//
//
//            //employRole
//            cEmployRoleService.deleteByEmployId(cEmployEntity.getId());
//            for (CRoleEntity CRoleEntity : roleEntityList) {
//                CEmployRoleEntity CEmployRoleEntity = new CEmployRoleEntity();
//                CEmployRoleEntity.setEmployId(cEmployEntity.getId())
//                        .setRoleId(CRoleEntity.getId())
//                        .setRoleDefId(roleDefId)
//                        .setCreatedAt(new Date())
//                        .setUpdatedAt(new Date())
//                ;
//                cEmployRoleService.save(CEmployRoleEntity);
//            }
//            if (StringUtils.isNotBlank(employ.getApproveType())) {
//                String[] split = employ.getApproveType().split("/");
//                for (String s : split) {
//                    CEmployRoleDTO employRoleDTO1 = new CEmployRoleDTO();
//                    employRoleDTO1.setName(s);
//                    List<CRoleEntity> roleEntityList1 = cRoleService.queryRole(employRoleDTO1);
//                    for (CRoleEntity CRoleEntity : roleEntityList1) {
//                        CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
//                        cEmployRoleEntity.setEmployId(cEmployEntity.getId())
//                                .setRoleId(CRoleEntity.getId())
//                                .setRoleDefId(CRoleEntity.getRoleDefId())
//                                .setCreatedAt(new Date())
//                                .setUpdatedAt(new Date())
//                        ;
//                        cEmployRoleService.save(cEmployRoleEntity);
//                    }
//                }
//            }
//
//
//        }
        return Result.success();
    }

    @Override
    public List<CEmployEntity> getEmploy(String roleDefId, String categoryId, String salesType) {
        CEmployRoleDTO employRoleDTO = new CEmployRoleDTO();
        employRoleDTO.setCategoryId(categoryId)
                .setSalesType(salesType)
                .setRoleDefId(Integer.valueOf(roleDefId));
        List<CRoleEntity> roleList = cRoleService.queryRole(employRoleDTO);
        List<Integer> roleIdList = roleList.stream().map(CRoleEntity::getId).collect(Collectors.toList());
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByRoleIds(roleIdList);
        List<Integer> employIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getEmployId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employIdList)) {
            employIdList = Collections.singletonList(-1);
        }
        return cEmployDao.queryEmployByIdList(employIdList);
    }

    @Override
    public List<CEmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String salesType) {
        CEmployRoleDTO employRoleDTO = new CEmployRoleDTO();
        employRoleDTO.setCategoryId(categoryId)
                .setSalesType(salesType)
                .setRoleDefCode(roleDefCode);
        List<CRoleEntity> roleList = cRoleService.queryRole(employRoleDTO);
        List<Integer> roleIdList = roleList.stream().map(CRoleEntity::getId).collect(Collectors.toList());
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByRoleIds(roleIdList);
        List<Integer> employIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getEmployId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employIdList)) {
            employIdList = Collections.singletonList(-1);
        }
        return cEmployDao.queryEmployByIdList(employIdList);
    }

    @Override
    public List<CEmployEntity> getEmployByEmailOrPhone(String email, String phone) {
        List<CEmployEntity> employEntityList = cEmployDao.getEmployByEmailOrPhone(email, phone);
        return employEntityList;
    }

    @Override
    public Result queryChoosedEmployByRoleId(QueryDTO<CRoleDTO> queryDTO) {
        Page<CEmployEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        CRoleDTO roleDTO = queryDTO.getCondition();
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByRoleIds(Collections.singletonList(roleDTO.getRoleId()));
        if (CollectionUtils.isEmpty(employRoleEntityList)) {
            return Result.page(page);
        }
        List<Integer> employIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getEmployId).collect(Collectors.toList());
        IPage<CEmployEntity> iPage = cEmployDao.queryPageByIdList(page, employIdList);
        List<CEmployVO> list = iPage.getRecords().stream().map(i -> {
            CEmployVO cEmployVO = new CEmployVO();
            cEmployVO.setId(i.getId())
                    .setName(i.getName())
                    .setPhone(i.getPhone())
                    .setEmail(i.getEmail())
            ;
            return cEmployVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);
    }

    @Override
    public List<CEmployEntity> getEmployByCustomerId(Integer customerId, int type) {

        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerByCustomerIdAndType(customerId, type, null);

        List<CEmployEntity> cEmployEntities = new ArrayList<CEmployEntity>();
        if (!cEmployCustomerEntities.isEmpty()) {
            cEmployEntities.add(cEmployDao.getById(cEmployCustomerEntities.get(0).getCEmployId()));
        }

        return cEmployEntities;
    }

    @Override
    public Integer saveOrUpdate(CEmployEntity cEmployEntity) {
        cEmployDao.saveOrUpdate(cEmployEntity);
        return cEmployEntity.getId();
    }

    @Override
    public CEmployDetailVO queryCurrentEmployDetail(Integer employId) {
        CEmployDetailVO employDetailVO = new CEmployDetailVO();
        CEmployEntity cEmployEntity = cEmployDao.queryEmployById(employId);
        if (null == cEmployEntity) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_EXISTED);
        }

        CustomerEntity customerEntity = customerFacade.queryCustomerById(cEmployEntity.getCustomerId());
        if (null == customerEntity) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }

        //CustomerEntity customerVO = JSON.parseObject(JSON.toJSONString(customerResult.getData()), CustomerVO.class);
        if (null != customerEntity) {
            employDetailVO
                    .setUseYqq(customerEntity.getUseYqq());

            List<CEmployEntity> cEmployEntities = cEmployDao.getEmployByCustomerId(customerEntity.getId(), CEmployTypeEnum.DEFAULT.getType());
            if (CollectionUtils.isNotEmpty(cEmployEntities)) {
                CEmployEntity cEmployEntity1 = cEmployEntities.get(0);
                employDetailVO
                        .setRealName(cEmployEntity1.getName())
                        .setYqqPhone(cEmployEntity1.getPhone());

                CheckRequestDTO checkRequestDTO = new CheckRequestDTO()
                        .setCustomTag(customerEntity.getLinkageCustomerCode())
                        .setPhone(cEmployEntity1.getPhone());
                try {
                    employDetailVO
                            .setYqqAuth(dbtSignatureFacade.hasAuthentication(checkRequestDTO) ? YqqAuthEnum.AUTH.getValue() : YqqAuthEnum.NOT_AUTH.getValue());
                } catch (Exception e) {
                    log.error("queryEmployDetail:{}", e);
                }
            }
        }
        employDetailVO
                .setName(cEmployEntity.getName())
                .setPhone(cEmployEntity.getPhone())
                .setStatus(cEmployEntity.getStatus())
                .setEmail(cEmployEntity.getEmail())
                .setType(cEmployEntity.getType())
                .setSignatureTime(cEmployEntity.getSignatureTime())
        ;
        return employDetailVO;
    }

    @Override
    public Result queryColumbusAdminList(QueryDTO<CEmployDTO> queryDTO) {
        Integer beginEmployId = 100000;
        if (null != commonProperties.getColumbusBeginEmployId()) {
            beginEmployId = commonProperties.getColumbusBeginEmployId();
        }
        Page<CEmployCustomerVOEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        CEmployDTO employDTO = queryDTO.getCondition();
       /* CustomerEntity customerVO1 = null;
        if (employDTO.getCustomerId() != null) {
            Integer customerId = employDTO.getCustomerId();
            customerVO1 = customerFacade.queryCustomerById(customerId);
        }
        CustomerEntity customerVO = customerVO1;*/
        IPage<CEmployCustomerVOEntity> iPage = vcEmployCustomerDao.queryPageByQueryDTO(page, employDTO, beginEmployId);
        List<ColumbusAdminVO> columbusAdminVOList = iPage.getRecords().stream().map(i -> {

            ColumbusAdminVO columbusAdminVO = new ColumbusAdminVO();

            CEmployEntity cEmployEntity = cEmployDao.getById(i.getCEmployId());
            BeanUtils.copyProperties(cEmployEntity, columbusAdminVO);
            columbusAdminVO
                    .setId(i.getId())
                    .setCEmployId(cEmployEntity.getId());
            List<CEmployRoleEntity> cEmployRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(i.getCEmployId(), i.getCustomerId());
            List<CEmployRoleEntity> list = cEmployRoleEntityList.stream().filter(k -> k.getRoleId() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                columbusAdminVO.setAdminStatus(1);
                columbusAdminVO.setAdminStatusName("是");
            } else {
                columbusAdminVO.setAdminStatus(0);
                columbusAdminVO.setAdminStatusName("否");
            }
            List<CEmployCustomerVO> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerVOByCEmployId(i.getCEmployId());
            if (cEmployCustomerEntities.size() > 1) {
                columbusAdminVO.setIsCompanyList(1)
                        .setIsCompanyListName("是");
            } else {
                columbusAdminVO.setIsCompanyList(0)
                        .setIsCompanyListName("否");
            }
            columbusAdminVO.setType(i.getType());
            String statusName = cEmployEntity.getStatus() == 0 ? "已禁用" : "已启用";
            columbusAdminVO.setStatusName(statusName);
            if (i.getSignatureTime() != null) {
                columbusAdminVO.setSignatureTime(DateTimeUtil.formatDate(i.getSignatureTime()));
            }
            columbusAdminVO.setLinkageCustomerCode(i.getLinkageCustomerCode());
            columbusAdminVO.setCustomerCode(i.getLinkageCustomerCode());
            columbusAdminVO.setCustomerId(i.getCustomerId());

            CustomerEntity customerEntity = customerFacade.queryCustomerById(i.getCustomerId());

            if (customerEntity != null) {
                columbusAdminVO.setCustomerName(customerEntity.getName())
                        .setEnterpriseName(customerEntity.getEnterpriseName())
                        .setLinkageCustomerCode(customerEntity.getLinkageCustomerCode())
                        .setCustomerCode(customerEntity.getCode())
                ;
            }

            if (CEmployTypeEnum.DEFAULT.getType().equals(i.getType())) {
                columbusAdminVO.setIsAdmin("是");
            } else {
                columbusAdminVO.setIsAdmin("否");
            }
            return columbusAdminVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, columbusAdminVOList);
    }

    @Override
    public void updateCEmploy(CEmployEntity cEmployEntity) {
        cEmployDao.updateById(cEmployEntity);
    }

    @Override
    public void updateColumbusAdmin(CEmployDTO cEmployDTO) {
        if (StringUtils.isNotBlank(cEmployDTO.getId()) && StringUtils.isNotBlank(cEmployDTO.getStatus())) {
            cEmployDao.updateEmployStatus(cEmployDTO);
        }
        if (cEmployDTO.getAdminStatus() == 0) {
            CEmployRoleDTO cEmployRoleDTO = new CEmployRoleDTO();
            cEmployRoleDTO
                    .setRoleId(1)
                    .setRoleDefId(1)
                    .setEmployIdList(Arrays.asList(Integer.parseInt(cEmployDTO.getId())))
                    .setCustomerId(cEmployDTO.getCustomerId())
            ;
            cEmployRoleService.deleteEmployRole(cEmployRoleDTO);
        }

        if (cEmployDTO.getAdminStatus() == 1) {
            CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
            cEmployRoleEntity
                    .setRoleId(1)
                    .setRoleDefId(1)
                    .setCustomerId(cEmployDTO.getCustomerId())
                    .setEmployId(Integer.parseInt(cEmployDTO.getId()));
            cEmployRoleService.save(cEmployRoleEntity);
        }

    }

    @Override
    public Result exportEmployRoleList() {
        List<CEmployEntity> employEntityList = cEmployDao.queryAllEmploy();
        List<CEmployVO> list = new ArrayList<>();
        employEntityList.stream().forEach(i -> {
            List<CEmployRoleEntity> employRolesList = cEmployRoleService.getEmployRolesByEmploy(String.valueOf(i.getId()));
            List<Integer> roleDefIdList = employRolesList.stream().map(CEmployRoleEntity::getRoleDefId).distinct().collect(Collectors.toList());
            if (roleDefIdList.size() > 1) {
                Map<Integer, List<CEmployRoleEntity>> map = employRolesList.stream().collect(Collectors.groupingBy(CEmployRoleEntity::getRoleDefId));
                for (Integer roleDefId : roleDefIdList) {
                    handleList(list, i, map.get(roleDefId));
                }
            } else {
                handleList(list, i, employRolesList);
            }
        });
        return Result.success(list);
    }

    @Override
    public List<List<CEmployEntity>> getBatchEmployList(int splitSize) {
        List<CEmployEntity> employEntityList = cEmployDao.queryAllEmploy();
        if (CollectionUtils.isEmpty(employEntityList) || splitSize < 1) {
            return new ArrayList<>();
        }
        int totalSize = employEntityList.size();
        int count = (totalSize % splitSize == 0) ?
                (totalSize / splitSize) : (totalSize / splitSize + 1);

        List<List<CEmployEntity>> batchList = new ArrayList();
        for (int i = 0; i < count; i++) {
            int index = i * splitSize;
            List<CEmployEntity> list = new ArrayList();
            int j = 0;
            while (j < splitSize && index < totalSize) {
                list.add(employEntityList.get(index++));
                j++;
            }
            batchList.add(list);
        }
        return batchList;
    }

    @Override
    public void updateTypeByIds(List<Integer> ids) {
        cEmployDao.updateTypeByIds(ids);
    }

    @Override
    public void setColumbusAdmin(CEmployDTO cEmployDTO) {
        List<CEmployRoleEntity> cEmployRoleEntityList = cEmployRoleService.getEmployRolesByEmploy(cEmployDTO.getId());
        List<Integer> roleIdList = cEmployRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIdList) || !roleIdList.contains(1)) {
            throw new BusinessException();
        }
        List<CEmployEntity> cEmployEntities = cEmployDao.getEmployByCustomerId(cEmployDTO.getCustomerId(), CEmployTypeEnum.DEFAULT.getType());
        cEmployDao.updateTypeByIds(cEmployEntities.stream().map(CEmployEntity::getId).collect(Collectors.toList()));
        CEmployEntity cEmployEntity = cEmployDao.queryEmployById(Integer.parseInt(cEmployDTO.getId()));
        cEmployEntity.setType(CEmployTypeEnum.DEFAULT.getType());
        cEmployDao.updateById(cEmployEntity);
        sendEmail(cEmployEntity.getId(), cEmployEntity.getName(), cEmployEntity.getCustomerName());
        CEmployRoleEntity cEmployRoleEntity = new CEmployRoleEntity();
        cEmployRoleEntity
                .setEmployId(cEmployEntity.getId())
                .setCustomerId(cEmployEntity.getCustomerId())
                .setRoleDefId(1)
                .setRoleId(1)
                .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
        ;
        cEmployPermissionFacade.save(cEmployRoleEntity);
    }

    @Override
    public Result setAdmin(CEmployDTO cEmployDTO) {

        List<CEmployRoleEntity> cEmployRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(Integer.parseInt(cEmployDTO.getId()), cEmployDTO.getCustomerId());
        List<Integer> roleIdList = cEmployRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(roleIdList) || !roleIdList.contains(1)) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_ADMIN);
        }

        Integer cEmployId = Integer.parseInt(JwtUtils.getCurrentUserId());

        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerByCustomerIdAndCEmployIdAndType(cEmployDTO.getCustomerId(), cEmployId, CEmployTypeEnum.DEFAULT.getType());
        //List<CEmployEntity> cEmployEntities = cEmployDao.getEmployByCustomerId(cEmployDTO.getCustomerId(), CEmployTypeEnum.DEFAULT.getType());
        cEmployCustomerService.updateTypeByIds(cEmployCustomerEntities.stream().map(CEmployCustomerEntity::getId).collect(Collectors.toList()));

        List<CEmployCustomerEntity> customerEntities = cEmployCustomerService.queryCEmployCustomerByEmployIdAndCustomerId(Integer.parseInt(cEmployDTO.getId()), cEmployDTO.getCustomerId());
        CEmployCustomerEntity cEmployCustomerEntity = customerEntities.get(0);
        cEmployCustomerEntity.setType(CEmployTypeEnum.DEFAULT.getType());
        cEmployCustomerService.updateCEmployCustomer(cEmployCustomerEntity);
        return Result.success();
    }

    @Override
    public Result queryAdminType(Integer customerId) {
        String currentUserId = JwtUtils.getCurrentUserId();
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerByEmployIdAndCustomerId(Integer.parseInt(currentUserId), customerId);
        CEmployCustomerEntity cEmployCustomerEntity = cEmployCustomerEntities.get(0);

        CEmployEntity cEmployEntity = cEmployDao.queryEmployById(Integer.parseInt(currentUserId));
        cEmployEntity
                .setType(cEmployCustomerEntity.getType());

        return Result.success(cEmployEntity);
    }

    private void handleList(List<CEmployVO> list, CEmployEntity i, List<CEmployRoleEntity> employRolesList) {
        CEmployVO employVO = new CEmployVO();
        List<Integer> roleIdList = employRolesList.stream().map(CEmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        List<CRoleEntity> roleEntityList = cRoleService.queryByIdList(roleIdList);
        List<String> roleNameList = roleEntityList.stream().map(CRoleEntity::getName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        String roleNameString = String.join("/", roleNameList);
        List<String> categoryNameList = roleEntityList.stream().map(j -> GoodsCategoryEnum.getByValue(j.getCategoryId()).getDesc()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        String categoryNameString = String.join("/", categoryNameList);
        employVO
                .setId(i.getId())
                .setName(i.getName())
                .setNickName(i.getNickName())
                .setEmail(i.getEmail())
                .setWorkNo(i.getWorkNo())
                .setPhone(i.getPhone())
                .setRoleNameString(roleNameString)
                .setStatus(i.getStatus())
                .setCreatedAt(i.getCreatedAt())
                .setCategoryNameString(categoryNameString)
        ;
        list.add(employVO);
    }


    public void sendEmail(Integer userId, String userName, String companyName) {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_SYSTEM_CHANGE_NOTICE_2.name());
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_SYSTEM_CHANGE_NOTICE_2.getDesc());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        List<String> receivers = new ArrayList<>();
        receivers.add(String.valueOf(userId));
        messageInfoDTO.setReceivers(receivers);
        Map<String, Object> map = new HashMap<>();
        map.put("companyName", companyName);
        map.put("customerName", userName);
        map.put("sendDate", DateTimeUtil.formatDateStringCN(new Date()));
        messageInfoDTO.setDataMap(map);
        messageFacade.sendMessage(messageInfoDTO);
    }

    @Override
    public List<CEmployEntity> getCEmployEntity() {
        return cEmployDao.list();
    }
}
