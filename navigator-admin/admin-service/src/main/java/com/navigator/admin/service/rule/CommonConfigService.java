package com.navigator.admin.service.rule;

import com.navigator.admin.pojo.dto.rule.CommonConfigDTO;
import com.navigator.admin.pojo.dto.rule.CommonConfigRuleMatchDTO;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-18 11:33
 **/
public interface CommonConfigService {
    /**
     * 保存系统配置
     * @param commonConfigDTO
     * @return
     */
    Boolean saveOrUpdateCommonConfig(CommonConfigDTO commonConfigDTO);

    /**
     * 获取配置详情
     * @param configId
     * @return
     */
    CommonConfigDTO getCommonConfigDetailById(Integer configId);

    /**
     * 查询正本配置列表
     * @return
     */
    List<CommonConfigDTO> getSignPaperRuleConfig();

    /**
     * 执行规则脚本
     * @param ruleMatchDTO
     * @return
     */
    Boolean matchConfigRuleInfo(CommonConfigRuleMatchDTO ruleMatchDTO);


}
