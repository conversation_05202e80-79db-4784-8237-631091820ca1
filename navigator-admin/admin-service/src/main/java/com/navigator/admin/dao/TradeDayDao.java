package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.TradeDayMapper;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/21 10:37
 */
@Dao
public class TradeDayDao extends BaseDaoImpl<TradeDayMapper, TradeDayEntity> {

    public int getTradeDays(String startDay, String endDay) {
        return this.baseMapper.selectCount(
                Wrappers.<TradeDayEntity>lambdaQuery()
                        .between(TradeDayEntity::getDayValue, startDay, endDay)
                        .eq(TradeDayEntity::getIsTrade, 1)
        );
    }

    public TradeDayEntity getTradeDayByDay(String day) {
        return this.getOne(new LambdaQueryWrapper<TradeDayEntity>().eq(TradeDayEntity::getDay, day));
    }

    public TradeDayEntity getTradeDayByDayValue(String dayValue) {
        return this.getOne(new LambdaQueryWrapper<TradeDayEntity>().eq(TradeDayEntity::getDayValue, dayValue));
    }

    public int getTradeDays(Date startDay, Date endDay) {
        return this.count(new LambdaQueryWrapper<TradeDayEntity>()
                .eq(TradeDayEntity::getIsTrade, 1)
                .between(TradeDayEntity::getDayValue, startDay, endDay));
    }

    public List<TradeDayEntity> getTradeDayByMonth(Integer year, String month) {
        return this.list(Wrappers.<TradeDayEntity>lambdaQuery()
                .eq(null != year, TradeDayEntity::getYear, year)
                .eq(!StringUtils.isBlank(month), TradeDayEntity::getMonth, month)
        );

    }

    public TradeDayEntity getTradeDayByDayAgo(String dayValue) {
        List<TradeDayEntity> tradeDayEntities = this.baseMapper.selectList(Wrappers.<TradeDayEntity>lambdaQuery()
                .eq(TradeDayEntity::getIsTrade, 1)
                .lt(TradeDayEntity::getDayValue, dayValue)
                .orderByDesc(TradeDayEntity::getCreatedAt)
        );

        return tradeDayEntities.isEmpty() ? null : tradeDayEntities.get(0);
    }
}
