package com.navigator.admin.service.rule.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.rule.RuleVariableDao;
import com.navigator.admin.pojo.dto.RuleEnumValueDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.admin.pojo.vo.StructureRuleVO;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.admin.service.rule.IRuleDictItemService;
import com.navigator.admin.service.rule.IRuleVariableService;
import com.navigator.admin.service.systemrule.StructureRuleService;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.bisiness.enums.ValueTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.rule.RuleVariableBizCodeEnum;
import com.navigator.common.enums.rule.RuleVariableInputType;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 14:36
 **/
@Service
@Slf4j
public class RuleVariableServiceImpl implements IRuleVariableService {
    @Resource
    private RuleVariableDao variableDao;
    @Resource
    private IRuleDictItemService ruleDictItemService;
    @Autowired
    private IEmployService employService;
    @Autowired
    private AttributeFacade attributeFacade;
    @Autowired
    private StructureRuleService structureRuleService;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public Boolean updateVariable(RuleVariableEntity variableEntity) {
        RuleVariableEntity oldRuleVariableEntity = variableDao.getById(variableEntity.getId());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        EmployEntity employEntity = employService.getEmployById(userId);
        String name = null == employEntity ? "" : employEntity.getName();
        oldRuleVariableEntity.setDisplayName(StringUtils.isNotBlank(variableEntity.getDisplayName()) ? variableEntity.getDisplayName().trim() : oldRuleVariableEntity.getDisplayName().trim())
//                .setTypicalValue(StringUtils.isNotBlank(variableEntity.getTypicalValue()) ? variableEntity.getTypicalValue() : oldRuleVariableEntity.getTypicalValue())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        if (!CollectionUtils.isEmpty(variableEntity.getPatternRelationList())) {
            oldRuleVariableEntity.setPatternRelations(StringUtils.join(variableEntity.getPatternRelationList(), ","));
        }
        return variableDao.updateById(oldRuleVariableEntity);
    }

    @Override
    public Result queryVariableByCondition(QueryDTO<RuleVariableEntity> queryDTO) {
        IPage<RuleVariableEntity> variableEntityIPage = variableDao.queryVariableByCondition(queryDTO);
        if (!CollectionUtils.isEmpty(variableEntityIPage.getRecords())) {
            this.getItemInfoByVariable(variableEntityIPage.getRecords());
        }
        return Result.page(variableEntityIPage);
    }

    @Override
    public List<RuleVariableEntity> queryExportVariableList(RuleVariableEntity variableQO) {
        List<RuleVariableEntity> variableEntityList = variableDao.queryExportVariableList(variableQO);
        if (!CollectionUtils.isEmpty(variableEntityList)) {
            this.getItemInfoByVariable(variableEntityList);
        }
        return variableEntityList;
    }

    @Override
    public RuleVariableEntity getVariableByCode(String code, String moduleType, String systemId) {
        return variableDao.getVariableByCode(code, moduleType, systemId);
    }

    @Override
    public List<RuleVariableEntity> getAllVariableList(Integer isCondition, Integer isKey, String moduleType, String systemId) {
        List<RuleVariableEntity> variableEntityList = variableDao.getAllVariableList(isCondition, isKey, moduleType, systemId);
        if (!CollectionUtils.isEmpty(variableEntityList)) {
            this.getItemInfoByVariable(variableEntityList);
        }
        return variableEntityList;
    }

    @Override
    public List<RuleVariableEntity> getAllVariableEntityList(String moduleType, String systemId) {
        return variableDao.getAllVariableList(null, null, moduleType, systemId);
    }


    private void getItemInfoByVariable(List<RuleVariableEntity> variableEntityList) {
        variableEntityList.forEach(variableEntity -> {
            if (StringUtils.isNotBlank(variableEntity.getPatternRelations())) {
                List<String> patternRelationList = Arrays.stream(variableEntity.getPatternRelations().split(",")).collect(Collectors.toList());
                String patternRelationInfo = patternRelationList.stream().map(PatternRelationEnum::getDescByCode).collect(Collectors.joining(";"));
                variableEntity.setPatternRelationList(patternRelationList)
                        .setPatternRelations(patternRelationInfo);
            }
            if (StringUtils.isNotBlank(variableEntity.getInputType())) {
                variableEntity.setInputTypeInfo(RuleVariableInputType.getByValue(variableEntity.getInputType()).getDesc());
            }
            if (DisableStatusEnum.ENABLE.getValue().equals(variableEntity.getHasDict())) {
                List<RuleDictItemEntity> dictItemEntityList = ruleDictItemService.getRuleItemByDictCode(variableEntity.getCode(), variableEntity.getModuleType(), variableEntity.getSystemId());
                List<RuleEnumValueDTO> enumValueDTOList = dictItemEntityList.stream().map(dictItemEntity -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(dictItemEntity.getDictCode())
                            .setValue(dictItemEntity.getItemValue())
                            .setCode(dictItemEntity.getItemCode())
                            .setMemo(dictItemEntity.getMemo())
                            .setDesc(dictItemEntity.getItemDescription());
                }).collect(Collectors.toList());
                variableEntity.setEnumValueDTOList(enumValueDTOList);
            }
            if (DisableStatusEnum.ENABLE.getValue().equals(variableEntity.getIsEnum())) {
                try {
                    Class<Enum> clazz = (Class<Enum>) Class.forName(variableEntity.getEnumPath());
                    //获取所有枚举实例
                    Enum[] enumConstants = clazz.getEnumConstants();
                    //根据方法名获取方法
                    Method getValue = clazz.getMethod("getValue");
                    Method getDesc = clazz.getMethod("getDesc");
                    List<RuleEnumValueDTO> enumValueDTOList = new ArrayList<>();
                    for (Enum enumInfo : enumConstants) {
                        RuleEnumValueDTO enumValueDTO = new RuleEnumValueDTO()
                                .setEnumName(enumInfo.name())
//                                .setCode(getValue.invoke(enumInfo).toString())
                                .setDesc(getDesc.invoke(enumInfo).toString());
                        if (ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType())) {
                            enumValueDTO.setCode(getValue.invoke(enumInfo).toString());
                        } else {
                            enumValueDTO.setValue((Integer) getValue.invoke(enumInfo));
                            enumValueDTO.setCode(enumValueDTO.getValue().toString());
                        }
                        enumValueDTOList.add(enumValueDTO);
                    }
                    variableEntity.setEnumValueDTOList(enumValueDTOList);
                } catch (Exception e) {
                    log.info(e.toString());
                }
            }
            getBusinessEnumList(variableEntity);
        });
    }

    private void getBusinessEnumList(RuleVariableEntity variableEntity) {
        List<RuleEnumValueDTO> enumValueDTOList = new ArrayList<>();
        if (RuleVariableBizCodeEnum.DELIVERY_FACTORY_CODE.getValue().equals(variableEntity.getCode())) {
            List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(DisableStatusEnum.ENABLE.getValue());
            if (!CollectionUtils.isEmpty(factoryEntityList)) {
                enumValueDTOList = factoryEntityList.stream().map(factory -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.DELIVERY_FACTORY_CODE.getValue())
                            .setValue(factory.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? factory.getCode() : factory.getId().toString())
                            .setDesc(factory.getCode());
                }).collect(Collectors.toList());
            }
        }
//        else if (RuleVariableBizCodeEnum.SPEC_ID.getValue().equals(variableEntity.getCode())) {
//            GoodsAttributeVO attributeVO = attributeFacade.getSpecListByCategoryId(GoodsCategoryEnum.OSM_OIL.getValue());
//            if (null != attributeVO && !CollectionUtils.isEmpty(attributeVO.getSpecList())) {
//                enumValueDTOList = attributeVO.getSpecList().stream().map(specInfo -> {
//                    return new RuleEnumValueDTO()
//                            .setEnumName(specInfo.getAttributeName())
//                            .setValue(specInfo.getAttributeValueId())
//                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? specInfo.getAttributeValue() : specInfo.getAttributeValueId().toString())
//                            .setDesc(specInfo.getAttributeValue());
//                }).collect(Collectors.toList());
//            }
//            variableEntity.setEnumValueDTOList(enumValueDTOList);
//        }
        else if (RuleVariableBizCodeEnum.STRUCTURE_TYPE.getValue().equals(variableEntity.getCode())) {
            Result result = structureRuleService.queryAvailableStructureList();
            List<StructureRuleVO> structureRuleVOList = JSON.parseArray(JSON.toJSONString(result.getData()), StructureRuleVO.class);
            if (!CollectionUtils.isEmpty(structureRuleVOList)) {
                enumValueDTOList = structureRuleVOList.stream().map(structureRuleVO -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.STRUCTURE_TYPE.getValue())
                            .setValue(structureRuleVO.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? structureRuleVO.getCode() : structureRuleVO.getId().toString())
                            .setDesc(structureRuleVO.getCode() + "-" + structureRuleVO.getStructureName());
                }).collect(Collectors.toList());
            }
        } else if (RuleVariableBizCodeEnum.COMPANY_CODE.getValue().equals(variableEntity.getCode())) {
            List<CompanyEntity> companyEntityList = companyService.getAllCompany();
            if (!CollectionUtils.isEmpty(companyEntityList)) {
                enumValueDTOList = companyEntityList.stream().map(company -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.COMPANY_CODE.getValue())
                            .setValue(company.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? company.getShortName() : company.getId().toString())
                            .setCode(company.getShortName())
                            .setDesc(company.getName());
                }).collect(Collectors.toList());
            }
        } else if (RuleVariableBizCodeEnum.CATEGORY1.getValue().equals(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(1);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category1 -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY1.getValue())
                            .setValue(category1.getSerialNo())
                            .setCode(category1.getSerialNo().toString())
                            .setDesc(category1.getName());
                }).collect(Collectors.toList());
            }
        } else if (Arrays.asList(RuleVariableBizCodeEnum.CATEGORY2.getValue(), RuleVariableBizCodeEnum.CATEGORY_ID.getValue()).contains(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(2);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category2 -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY2.getValue())
                            .setValue(category2.getSerialNo())
                            .setCode(category2.getSerialNo().toString())
                            .setDesc(category2.getName());
                }).collect(Collectors.toList());
            }
        } else if (RuleVariableBizCodeEnum.CATEGORY3.getValue().equals(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(3);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category3 -> {
                    return new RuleEnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY3.getValue())
                            .setValue(category3.getSerialNo())
                            .setCode(category3.getSerialNo().toString())
                            .setDesc(category3.getName());
                }).collect(Collectors.toList());
            }
        }
        variableEntity.setEnumValueDTOList(enumValueDTOList);
    }
}
