package com.navigator.admin.service.impl;

import com.navigator.admin.dao.BusinessDetailUpdateRecordDao;
import com.navigator.admin.pojo.dto.BusinessDetailUpdateRecordDTO;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BusinessDetailUpdateRecordServiceImpl implements BusinessDetailUpdateRecordService {

    @Resource
    private BusinessDetailUpdateRecordDao businessDetailUpdateRecordDao;

    @Override
    public BusinessDetailUpdateRecordEntity detailUpdateSelect(BusinessDetailUpdateRecordDTO businessDetailUpdateRecordDTO) {
        return businessDetailUpdateRecordDao.detailUpdateSelect(businessDetailUpdateRecordDTO);
    }

    @Override
    public boolean saveBusinessDetailUpdateRecord(BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity) {
        return businessDetailUpdateRecordDao.save(businessDetailUpdateRecordEntity);
    }
}
