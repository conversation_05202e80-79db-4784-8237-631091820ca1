package com.navigator.admin.service.columbus;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.EmployDTO;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.ResetPasswordDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployBusinessDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.columbus.CEmployDetailVO;
import com.navigator.admin.pojo.vo.columbus.CEmployVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface ICEmployService {


    /**
     * 根据条件查找 employ
     *
     * @param queryDTO
     * @return
     */
    IPage<CEmployEntity> getEmployByCondition(QueryDTO<EmployDTO> queryDTO);

    /**
     * 根据 employIds 获取 Employs
     *
     * @param employIds
     * @return
     */
    List<CEmployEntity> getEmployByEmployIds(List<Integer> employIds);

    /**
     * 根据 email 获取 Employ
     *
     * @param email
     * @return
     */
    List<CEmployEntity> getEmployByEmail(String email);

    /**
     * 根据 phone 获取 Employ
     *
     * @param phone
     * @return
     */
    List<CEmployEntity> getEmployByPhone(String phone, Integer type);

    List<CEmployEntity> queryEmployByPhone(String phone);

    /**
     * 根据 nickName 获取 Employ
     *
     * @param nickName
     * @return
     */
    CEmployEntity getEmployByNickName(String nickName);

    /**
     * 根据 id 获取 Employ
     *
     * @param id
     * @return
     */
    CEmployEntity getEmployById(Integer id);

    /**
     * 如果不存在employ新增 存在返回
     *
     * @param cEmployEntity
     * @return
     */
    CEmployEntity ifNotExistToSave(CEmployEntity cEmployEntity);

    List<CEmployEntity> getEmployByRoleName(String roleName);

    /**
     * 根据CompnyId查询账号列表（区分企业个人账号）
     *
     * @param companyId
     * @return
     */
    List<CEmployEntity> queryEmployByCompanyId(Integer companyId);

    /**
     * 修改密码
     *
     * @param cEmployEntity
     * @return
     */
    Result modifyPassword(CEmployEntity cEmployEntity);


    /**
     * 重置客户密码
     *
     * @param id
     * @return
     */
    boolean updateEmployResetPassword(Integer id);


    /**
     * 根据客户id查询 员工信息
     *
     * @param customerId
     * @return
     */
    CEmployEntity queryEmployByCustomerId(Integer customerId);


    Result saveOrUpdateEmploy(CEmployBusinessDTO employBusinessDTO);

    Result queryEmployList(QueryDTO<CEmployDTO> queryDTO);

    CEmployDetailVO queryEmployDetail(Integer employId, Integer customerId);

    List<CEmployVO> queryEmployListByRoleDefId(Integer roleDefId);

    String resetPassword(Integer employId);

    List<CEmployVO> queryAvailableEmployByRoleDefId(Integer roleDefId);

    CategoryFactoryMenuVO queryCategoryFactoryByRole();

    Result updateEmployStatus(CEmployDTO employDTO);

    Result saveEmployStatus(CEmployEntity cEmployEntity);

    Result resetUserPassword(ResetPasswordDTO resetPasswordDTO);

    Result sendResetPasswordCode(String mobileNo);

    Result sendResetPasswordPhoneCode(String mobileNo);

    Result resetNotLogUserPassword(ResetPasswordDTO resetPasswordDTO);

    Result sendAadCode(String mobileNo);

    Result verifyAadCode(LoginDTO loginDTO);

    void updateSignature(CEmployEntity cEmployEntity);

    /**
     * 根据用户id,品类 获取能查看的 belongCustomer权限
     *
     * @param
     * @return
     */
    PermissionBO queryPermissionByEmployId(String employId, Integer categoryId);

    Result importEmploy(MultipartFile file);


    /**
     * 根据虚角色id ,品类, 采销 获取员工id集合
     *
     * @param
     * @return
     */
    List<CEmployEntity> getEmploy(String roleDefId, String categoryId, String salesType);


    /**
     * 根据虚角色code ,品类, 采销 获取员工id集合
     *
     * @param
     * @return
     */
    List<CEmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String salesType);

    List<CEmployEntity> getEmployByEmailOrPhone(String email, String phone);

    Result queryChoosedEmployByRoleId(QueryDTO<CRoleDTO> queryDTO);

    List<CEmployEntity> getEmployByCustomerId(Integer customerId, int type);

    Integer saveOrUpdate(CEmployEntity cEmployEntity);

    CEmployDetailVO queryCurrentEmployDetail(Integer employId);

    Result queryColumbusAdminList(QueryDTO<CEmployDTO> queryDTO);

    void updateColumbusAdmin(CEmployDTO cEmployDTO);

    void updateCEmploy(CEmployEntity cEmployEntity);

    Result exportEmployRoleList();

    List<List<CEmployEntity>> getBatchEmployList(int size);

    void updateTypeByIds(List<Integer> ids);

    void setColumbusAdmin(CEmployDTO cEmployDTO);

    Result setAdmin(CEmployDTO cEmployDTO);

    Result queryAdminType(Integer customerId);

    List<CEmployEntity> getCEmployEntity();

}
