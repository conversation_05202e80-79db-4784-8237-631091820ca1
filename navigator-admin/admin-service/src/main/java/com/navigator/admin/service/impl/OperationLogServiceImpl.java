package com.navigator.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.dao.DbzRedAlarmDao;
import com.navigator.admin.dao.DbzTraceLogDao;
import com.navigator.admin.dao.OperationLogDao;
import com.navigator.admin.mapper.OperationLogMapper;
import com.navigator.admin.pojo.bo.OperationLogBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.DbzRedAlarmEntity;
import com.navigator.admin.pojo.entity.DbzTraceLogEntity;
import com.navigator.admin.pojo.entity.OperationConfigEntity;
import com.navigator.admin.pojo.entity.OperationLogEntity;
import com.navigator.admin.service.IOperationConfigService;
import com.navigator.admin.service.IOperationLogService;
import com.navigator.admin.service.ITemplateService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.navigator.common.constant.BizConstant.T_CONTRACT_COMMON_OP;

/**
 * <p>
 * 操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLogEntity> implements IOperationLogService {

    @Resource
    IOperationConfigService operationConfigService;
    @Resource
    ITemplateService templateService;
    @Resource
    OperationLogDao operationLogDao;
    @Resource
    DbzRedAlarmDao dbzRedAlarmDao;
    @Resource
    MessageFacade messageFacade;
    @Resource
    private DbzTraceLogDao dbzTraceLogDao;

    @Override
    public void saveOperationLog(OperationDetailDTO operationDetailDTO) {
        // 查询操作日志配置
        List<OperationConfigEntity> operationConfigEntityList = operationConfigService.queryOperationConfigByBizCode(operationDetailDTO.getBizCode());
        if (null == operationConfigEntityList || operationConfigEntityList.size() == 0) {
            return;
        }
//        Map map = operationDetailDTO.getMap();
        // 生成操作日志表数据
        operationConfigEntityList.forEach(operationConfigEntity -> {
            OperationLogEntity operationLogEntity = new OperationLogEntity();
            BeanConvertUtils.copy(operationLogEntity, operationDetailDTO);

            String templateCode = operationConfigEntity.getTemplateCode();
            String content = "";

            if (templateCode.equals(T_CONTRACT_COMMON_OP)) {
                content = operationDetailDTO.getOperationName();
                if (StringUtil.isNotEmpty(operationDetailDTO.getOperationInfo())) {
                    content = content + "，" + operationDetailDTO.getOperationInfo();
                }
            } else {
                //TODO NEO
                //content = templateService.jointOperationLogTemplate(operationDetailDTO.getMap(), operationConfigEntity.getTemplateCode());
                int i = 1;//解决sonar 临时加入，因为if else 不容许相同处理结果，后续上边代码生效后可以删除。
                content = operationDetailDTO.getOperationName();
                if (StringUtil.isNotEmpty(operationDetailDTO.getOperationInfo())) {
                    content = content + "，" + operationDetailDTO.getOperationInfo();
                }
            }

            operationLogEntity.setOperationId(operationDetailDTO.getId())
                    .setReferBizCode(operationDetailDTO.getReferBizCode())
                    .setReferBizId(operationDetailDTO.getReferBizId())
                    .setTemplateCode(templateCode)
                    .setTargetRecordId(operationDetailDTO.getTargetRecordId())
                    .setTargetRecordType(operationDetailDTO.getTargetRecordType())
                    .setTtCode(operationDetailDTO.getTtCode())
                    .setLogInfo(content)
                    .setCreatedAt(operationDetailDTO.getCreatedAt());

            this.save(operationLogEntity);
        });
    }

    @Override
    public void saveOperationLog(ColumbusOperationDTO columbusOperationDTO) {
        OperationLogEntity operationLogEntity = new OperationLogEntity();
        BeanUtils.copyProperties(columbusOperationDTO, operationLogEntity);
        operationLogDao.save(columbusOperationDTO);
    }

    @Override
    public IPage<OperationLogEntity> queryOperationLog(QueryDTO<OperationLogBO> queryDTO) {
        OperationLogBO operationLogBO = queryDTO.getCondition();
        Page page = new Page(queryDTO.getPageNo(), queryDTO.getPageSize());
        return baseMapper.selectPage(page, new LambdaQueryWrapper<OperationLogEntity>()
                .eq(null != operationLogBO.getLogLevel(), OperationLogEntity::getLogLevel, operationLogBO.getLogLevel())
                .eq(StrUtil.isNotBlank(operationLogBO.getBizCode()), OperationLogEntity::getBizCode, operationLogBO.getBizCode())
                .eq(StrUtil.isNotBlank(operationLogBO.getBizModule()), OperationLogEntity::getBizModule, operationLogBO.getBizModule())
                .eq(null != operationLogBO.getCode(), OperationLogEntity::getReferBizCode, operationLogBO.getCode())
        );
    }

    @Override
    public List<OperationLogEntity> queryOperationLogByCode(String code, Integer logLevel, Integer id) {
        return this.baseMapper.selectList(
                Wrappers.<OperationLogEntity>lambdaQuery().eq(OperationLogEntity::getReferBizCode, code)
                        .eq(null != id, OperationLogEntity::getReferBizId, id)
                        .eq(null != logLevel, OperationLogEntity::getLogLevel, logLevel)
        );
    }

    @Override
    public List<ContractOperationEventDTO> queryContractOperationLog(Integer contractId, Integer logLevel) {
        List<ContractOperationEventDTO> eventResultList = new ArrayList<>();
        List<OperationLogEntity> logList = operationLogDao.queryOperationLogList(contractId, logLevel);
        if (CollectionUtils.isEmpty(logList)) {
            return eventResultList;
        }
        Map<String, List<OperationLogEntity>> logMap = logList.stream()
                .filter(log -> {
                    return StringUtils.isNotBlank(log.getTtCode());
                })
                .collect(Collectors.groupingBy(OperationLogEntity::getTtCode));
        for (Map.Entry<String, List<OperationLogEntity>> logEntry : logMap.entrySet()) {
            List<OperationLogEntity> logEntityList = logEntry.getValue();
            OperationLogEntity firstLog = logEntityList.get(0);
            ContractOperationEventDTO contractOperationEventDTO = new ContractOperationEventDTO();
            List<ContractOperationLogDTO> eventLogList = BeanConvertUtils.convert2List(ContractOperationLogDTO.class, logEntityList);
            if (!CollectionUtils.isEmpty(eventLogList)) {
                eventLogList.forEach(eventLog -> eventLog.setContractId(eventLog.getTargetRecordId()));
            }
            contractOperationEventDTO.setContractId(firstLog.getTargetRecordId())
                    .setTradeTypeName(firstLog.getTradeTypeName())
                    .setTtCode(firstLog.getTtCode())
                    .setContent("")
                    .setCreatedAt(DateTimeUtil.formatDateTimeString(firstLog.getCreatedAt()))
                    .setOperationLogList(eventLogList);
            eventResultList.add(contractOperationEventDTO);
        }
        eventResultList.sort(Comparator.comparing(ContractOperationEventDTO::getCreatedAt));
        return eventResultList;
    }

    @Override
    public void saveTraceLog(String bizCode, String bizModule, String logInfo) {
        try {
            DbzTraceLogEntity traceLogEntity = new DbzTraceLogEntity();
            traceLogEntity.setReferBizCode(bizCode)
                    .setBizModule(bizModule)
                    .setLogInfo(logInfo);
            dbzTraceLogDao.save(traceLogEntity);
        } catch (Exception e) {
            DbzTraceLogEntity traceLogEntity = new DbzTraceLogEntity();
            traceLogEntity.setLogInfo(e.getMessage())
                    .setData(JSON.toJSONString(e));
            dbzTraceLogDao.save(traceLogEntity);
        }
    }

    @Override
    public void saveTraceLog(TraceLogDTO traceLogDTO) {
        try {
            DbzTraceLogEntity traceLogEntity = new DbzTraceLogEntity();
            traceLogEntity.setReferBizCode(traceLogDTO.getBizCode())
                    .setBizModule(traceLogDTO.getBizModule())
                    .setLogInfo(traceLogDTO.getLogInfo());
            dbzTraceLogDao.save(traceLogEntity);
        } catch (Exception e) {
            DbzTraceLogEntity traceLogEntity = new DbzTraceLogEntity();
            traceLogEntity.setLogInfo(e.getMessage())
                    .setData(JSON.toJSONString(e));
            dbzTraceLogDao.save(traceLogEntity);
        }
    }

    @Override
    @Async
    public void recordredalarm(DbzRedAlarmEntity redAlarmEntity) {
        try {
            redAlarmEntity.setCreatedAt(new Date());
            redAlarmEntity.setUpdatedAt(new Date());
            dbzRedAlarmDao.save(redAlarmEntity);

            String warningContent = "";

            warningContent = "程序异常:" + redAlarmEntity.getBizModule() + "_" + redAlarmEntity.getReferBizId() + "_" + redAlarmEntity.getId();

            messageFacade.sendRedAlarmEmail(warningContent);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
