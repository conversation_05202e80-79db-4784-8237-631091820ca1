package com.navigator.admin.service.impl;

import com.navigator.admin.dao.FileInfoDao;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.service.IFileInfoService;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.enums.FileServiceTypeEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 文件基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class FileInfoServiceImpl implements IFileInfoService {

    @Resource
    private FileInfoDao fileInfoDao;

    @Value("${file.pdf.port}")
    private String filePdfPort;
    @Resource
    private AzureBlobUtil azureBlobUtil;

    @Override
    public FileInfoEntity saveFileInfo(FileBaseInfoDTO fileBaseInfoDTO) {
        String viewPort = fileBaseInfoDTO.getFsType().equals(FileServiceTypeEnum.LOCAL_SERVER.getValue()) ? filePdfPort : azureBlobUtil.getHostUrl();
        FileInfoEntity fileInfoEntity = new FileInfoEntity()
                .setNewFileName(fileBaseInfoDTO.getAttachName())
                .setOriginalFileName(fileBaseInfoDTO.getOriginalName())
                .setExtension(fileBaseInfoDTO.getAttachStuff())
                .setSize(fileBaseInfoDTO.getSizeInfo())
                //todo:当前操作人,fsType
                .setOperatorId(0)
                .setFsType(null == fileBaseInfoDTO.getFsType() ? FileServiceTypeEnum.LOCAL_SERVER.getValue() : fileBaseInfoDTO.getFsType())
                .setPath(fileBaseInfoDTO.getAttachUrl())
                .setFsPath(viewPort + fileBaseInfoDTO.getAttachUrl());
        fileInfoDao.save(fileInfoEntity);
        String sasToken = azureBlobUtil.getSharedAccessSignature();
        return fileInfoEntity.setFilePathUrl(viewPort + fileBaseInfoDTO.getAttachUrl())
                .setFileUrl(viewPort + fileBaseInfoDTO.getAttachUrl() + sasToken);
    }

    @Override
    public List<FileInfoEntity> getFileListByIds(List<Integer> fileIdList) {
        return fileInfoDao.getFileListByIds(fileIdList);
    }

}
