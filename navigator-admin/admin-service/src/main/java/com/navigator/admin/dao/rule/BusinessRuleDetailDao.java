package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.rule.BusinessRuleDetailMapper;
import com.navigator.admin.pojo.entity.rule.BusinessRuleDetailEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 18:27
 **/
@Dao
public class BusinessRuleDetailDao extends BaseDaoImpl<BusinessRuleDetailMapper, BusinessRuleDetailEntity> {
    /**
     * 根据规则编号获取规则信息
     *
     * @param ruleCode
     * @return
     */
    public List<BusinessRuleDetailEntity> getRuleDetailListByRuleCode(String ruleCode) {
        return this.list(new LambdaQueryWrapper<BusinessRuleDetailEntity>()
                .eq(BusinessRuleDetailEntity::getRuleCode, ruleCode)
                .eq(BusinessRuleDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<BusinessRuleDetailEntity> getRuleDetailByReferCode(String referCode, String referType, String moduleType, String systemId) {
        return this.list(new LambdaQueryWrapper<BusinessRuleDetailEntity>()
                .eq(BusinessRuleDetailEntity::getReferCode, referCode)
                .eq(BusinessRuleDetailEntity::getReferType, referType)
                .eq(BusinessRuleDetailEntity::getModuleType, moduleType)
                .eq(BusinessRuleDetailEntity::getSystemId, systemId)
                .eq(BusinessRuleDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(BusinessRuleDetailEntity::getSort)
        );
    }

    public void dropBusinessRuleDetail(String referCode, String referType, String moduleType, String systemId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(BusinessRuleDetailEntity::getReferCode, referCode)
                .eq(BusinessRuleDetailEntity::getReferType, referType)
                .eq(BusinessRuleDetailEntity::getModuleType, moduleType)
                .eq(BusinessRuleDetailEntity::getSystemId, systemId)
                .eq(BusinessRuleDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(BusinessRuleDetailEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(BusinessRuleDetailEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
