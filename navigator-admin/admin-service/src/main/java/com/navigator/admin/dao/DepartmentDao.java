package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.DepartmentMapper;
import com.navigator.admin.pojo.entity.DepartmentEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 13:46
 */
@Dao
public class DepartmentDao extends BaseDaoImpl<DepartmentMapper, DepartmentEntity> {

    public List<DepartmentEntity> getDepartmentList(Integer system) {

        return this.baseMapper.selectList(
                Wrappers.<DepartmentEntity>lambdaQuery()
                        .eq(0 != system, DepartmentEntity::getSystem, system)
                        .eq(DepartmentEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                        .eq(DepartmentEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())

        );
    }

    public DepartmentEntity getDepartmentEntityById(String departmentId) {
        QueryWrapper<DepartmentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue());
        queryWrapper.eq("status", DisableStatusEnum.ENABLE.getValue());
        queryWrapper.eq("id", departmentId);
        return this.getOne(queryWrapper);
    }

    public List<DepartmentEntity> getDepartmentEntityByCompanyId(String companyId) {
        QueryWrapper<DepartmentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_id", companyId);
        queryWrapper.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue());
        queryWrapper.eq("status", DisableStatusEnum.ENABLE.getValue());
        return this.list(queryWrapper);
    }

    public List<DepartmentEntity> getDepartmentLeaderId(List<Integer> departmentId) {
        QueryWrapper<DepartmentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", departmentId);
        queryWrapper.eq("is_deleted", IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(queryWrapper);
    }


    public DepartmentEntity getDepartmentEntityByName(String fatherDepartment) {
        List<DepartmentEntity> list = list(Wrappers.<DepartmentEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(fatherDepartment), DepartmentEntity::getName, fatherDepartment));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
}
