package com.navigator.admin.service.columbus.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.dao.columbus.CMenuDao;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.columbus.*;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CMenuServiceImpl implements ICMenuService {

    @Autowired
    private ICEmployRoleService cEmployRoleService;
    @Autowired
    private ICRoleMenuService cRoleMenuService;
    @Resource
    private ICRoleService roleService;
    @Autowired
    private CMenuDao cMenuDao;
    @Autowired
    private ICEmployBusinessService cEmployBusinessService;
    @Autowired
    private ICEmployService cEmployService;

    @Autowired
    private ICRoleService cRoleService;
    @Resource
    private CommonProperties commonProperties;
    @Autowired
    protected IOperationDetailService operationDetailService;
    @Resource
    private ICEmployCustomerService icEmployCustomerService;
    @Resource
    private DeliveryApplyFacade deliveryApplyFacade;

    private static List<String> codeList = Arrays.asList("N024", "N027");

    @Override
    public CMenuEntity getMenuById(String id) {
        return cMenuDao.getMenuById(id);
    }

    @Override
    public CMenuVO getMenusByEmploy(CMenuDTO cMenuDTO) {
        CMenuVO menuVO = new CMenuVO();
        String employId = cMenuDTO.getEmployId();
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmploy(employId);
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
        List<CRoleEntity> cRoleEntities = cRoleService.queryByIdList(roleIdList);
        List<Integer> categoryIdList = cRoleEntities.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        // 系统管理员展示所有菜单
        if (categoryIdList.contains(0) || new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).collect(Collectors.toList());

        menuVO.setCategoryList(categoryMap);

        List<CMenuEntity> menuList = cMenuDao.getMenuByCategoryId(cMenuDTO.getCategoryId(), null);
        if (null == menuList) {
            return menuVO;
        }
        List<MenuDetailVO> allMenuDetailVOList = new ArrayList<>();
        menuList.forEach(
                i -> {
                    MenuDetailVO menuDetailVO = new MenuDetailVO();
                    menuDetailVO.setName(i.getCode() + "-" + i.getName())
                            .setIcon(i.getIcon())
                            .setUrl(i.getUrl())
                            .setParentId(i.getParentId())
                            .setId(i.getId())
                            .setCode(i.getCode())

                    ;
                    allMenuDetailVOList.add(menuDetailVO);
                }
        );
        List<MenuDetailVO> parentMenuDetailVOList = allMenuDetailVOList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        Map<Integer, List<MenuDetailVO>> map = allMenuDetailVOList.stream().sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.groupingBy(MenuDetailVO::getParentId));
        parentMenuDetailVOList.forEach(i -> {
            i.setChildren(map.get(i.getId()));
        });
        menuVO.setMenuDetailVOList(parentMenuDetailVOList);
        return menuVO;
    }

    @Override
    public CMenuVO getMenusByCondition(CMenuDTO cMenuDTO, Integer customerId) {
        List<Integer> menuIdList = new ArrayList<>();
        CMenuVO menuVO = new CMenuVO();
        String employId = cMenuDTO.getEmployId();
        if (StringUtils.isNotBlank(employId)) {
            List<CRoleEntity> roleEntityList = cRoleService.queryRoleByEmployId(employId, customerId);
            List<Integer> roleIdList = roleEntityList.stream().map(CRoleEntity::getId).collect(Collectors.toList());
            List<CRoleMenuEntity> roleMenuEntityList = cRoleMenuService.getMenuIdListByRoleIdList(roleIdList, customerId);
            List<Integer> list = roleMenuEntityList.stream().map(CRoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                menuIdList = list;
            } else {
                menuIdList = Collections.singletonList(-1);
            }
            List<Integer> categoryIdList = roleEntityList.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
            // 系统管理员展示所有菜单
            if (categoryIdList.contains(0) || new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
                categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
            }
            List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                    || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
                Map map = new HashMap();
                map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
                return map;
            }).collect(Collectors.toList());
            menuVO.setCategoryList(categoryMap);
        }

        List<CMenuEntity> menuList = cMenuDao.getMenuByCategoryId(cMenuDTO.getCategoryId(), menuIdList);
        if (CollectionUtils.isEmpty(menuList)) {
            return menuVO;
        }
        List<MenuDetailVO> allMenuDetailVOList = menuList.stream().map(
                i -> {
                    MenuDetailVO menuDetailVO = new MenuDetailVO();
                    menuDetailVO.setName(i.getCode() + "-" + i.getName())
                            .setIcon(i.getIcon())
                            .setUrl(i.getUrl())
                            .setParentId(i.getParentId())
                            .setId(i.getId())
                            .setCode(i.getCode())
                            .setCategoryId(i.getCategoryId())
                            .setParentCode(i.getParentCode())
                    ;
                    return menuDetailVO;
                }
        ).collect(Collectors.toList());

        List<MenuDetailVO> parentMenuDetailVOList = allMenuDetailVOList.stream().filter(i -> i.getParentId() == 0).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.toList());
        Map<String, List<MenuDetailVO>> map = allMenuDetailVOList.stream().filter(i -> i.getParentCode() != null).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.groupingBy(MenuDetailVO::getParentCode));
        parentMenuDetailVOList.forEach(i -> {
            i.setChildren(map.get(i.getCode()));
        });
        List<MenuDetailVO> collect = parentMenuDetailVOList.stream().filter(i -> !codeList.contains(i.getCode()) || (codeList.contains(i.getCode()) && i.getCategoryId().toString().equals(cMenuDTO.getCategoryId()))).collect(Collectors.toList());
        menuVO.setMenuDetailVOList(collect);
        return menuVO;
    }

    @Override
    public CMenuVO getMenuByRoleId(CRoleDTO roleDTO) {
        CMenuVO menuVO = new CMenuVO();
        CMenuDTO defaultDTO = new CMenuDTO();
        defaultDTO
                .setCategoryId("0");
        CEmployEntity employEntity = cEmployService.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        Integer customerId = employEntity.getCustomerId();
        roleDTO.setCustomerId(customerId);
        CMenuVO defaultMenu = getMenusByCondition(defaultDTO, customerId);
//        List<String> defaultCode = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(defaultMenu.getMenuDetailVOList())) {
//            defaultCode = defaultMenu.getMenuDetailVOList().stream().map(MenuDetailVO::getCode).collect(Collectors.toList());
//        }
        List<String> defaultCode = defaultMenu.getMenuDetailVOList().stream().map(MenuDetailVO::getCode).collect(Collectors.toList());

        CMenuDTO sbmDTO = new CMenuDTO();
        sbmDTO
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        CMenuVO sbmMenu = getMenusByCondition(sbmDTO, customerId);
        List<MenuDetailVO> sbmList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sbmList = sbmMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());
        }

        CMenuDTO sboDTO = new CMenuDTO();
        sboDTO
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        CMenuVO sboMenu = getMenusByCondition(sboDTO, customerId);
        List<MenuDetailVO> sboList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sboList = sboMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_OIL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_OIL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());

        }
        List<CMenuEntity> parentMenu = cMenuDao.getMenuParentMenu();
        List<Integer> parentMenuIdList = parentMenu.stream().map(CMenuEntity::getId).collect(Collectors.toList());
        List<Integer> roleIdList = Arrays.asList(roleDTO.getRoleId());
        List<CRoleMenuEntity> roleMenuEntityList = cRoleMenuService.getMenuIdListByRoleIdList(roleIdList, customerId);
        List<Integer> menuIdList = roleMenuEntityList.stream().filter(i -> (!parentMenuIdList.contains(i.getMenuId())) || i.getMenuId() == 1).map(CRoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        menuVO.setSbmList(sbmList);
        menuVO.setSboList(sboList);
        menuVO.setChooseIdList(menuIdList);
        return menuVO;
    }

    @Override
    public CMenuVO getMenuByEmployId(CRoleDTO roleDTO) {
        CMenuVO menuVO = new CMenuVO();
        String employId = roleDTO.getEmployId();
        CEmployEntity employEntity = cEmployService.getEmployById(Integer.parseInt(employId));
        Integer customerId = employEntity.getCustomerId();
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmploy(String.valueOf(employId));
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
        List<CRoleEntity> cRoleEntities = cRoleService.queryByIdList(roleIdList);
        List<Integer> categoryIdList = cRoleEntities.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        // 系统管理员展示所有菜单
        if (new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
            employId = null;
        }
        List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).collect(Collectors.toList());

        menuVO.setCategoryList(categoryMap);

        CMenuDTO defaultDTO = new CMenuDTO();
        defaultDTO
                .setEmployId(employId)
                .setCategoryId("0");

        CMenuVO defaultMenu = getMenusByCondition(defaultDTO, customerId);

        CMenuDTO sbmDTO = new CMenuDTO();
        sbmDTO
                .setEmployId(employId)
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        CMenuVO sbmMenu = getMenusByCondition(sbmDTO, customerId);

        CMenuDTO sboDTO = new CMenuDTO();
        sboDTO
                .setEmployId(employId)
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        CMenuVO sboMenu = getMenusByCondition(sboDTO, customerId);

        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        menuVO.setSbmList(sbmMenu.getMenuDetailVOList());
        menuVO.setSboList(sboMenu.getMenuDetailVOList());
        return menuVO;
    }

    @Override
    public void saveRoleMenu(CMenuDTO menuDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CEmployEntity employEntity = cEmployService.getEmployById(userId);
        cRoleMenuService.deleteRoleMenu(menuDTO.getRoleId(), employEntity.getCustomerId());
        for (Integer menuId : menuDTO.getMenuIdList()) {
            CRoleMenuEntity cRoleMenuEntity = new CRoleMenuEntity();
            cRoleMenuEntity.setMenuId(menuId)
                    .setRoleId(Integer.parseInt(menuDTO.getRoleId()))
                    .setCustomerId(employEntity.getCustomerId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            cRoleMenuService.saveRoleMenu(cRoleMenuEntity);
        }
    }


    /**
     * v2.0 统一菜单按钮权限
     */

    @Override
    public CMenuVO getMenusByConditionV2(CMenuDTO cMenuDTO) {
        List<Integer> menuIdList = new ArrayList<>();
        List<CRoleEntity> cRoleEntities = new ArrayList<>();
        if (StringUtils.isNotBlank(cMenuDTO.getEmployId())) {

            List<CEmployCustomerEntity> cEmployCustomerEntities = icEmployCustomerService.getCEmployCustomerByEmployIdAndCustomerId(Integer.parseInt(cMenuDTO.getEmployId()), cMenuDTO.getCustomerId());

            CEmployCustomerEntity cEmployCustomerEntity = cEmployCustomerEntities.get(0);

            List<CRoleEntity> roleEntityList = cRoleService.queryRoleByEmployId(cMenuDTO.getEmployId(), cEmployCustomerEntity.getCustomerId());

            List<Integer> roleIdList = roleEntityList.stream().map(CRoleEntity::getId).collect(Collectors.toList());


            if (CollectionUtils.isEmpty(roleIdList) && CEmployTypeEnum.DEFAULT.getType().equals(cEmployCustomerEntity.getType())) {
                roleIdList = Collections.singletonList(1);
            }
            List<CRoleMenuEntity> roleMenuEntityList = new ArrayList<>();
            if (!roleIdList.isEmpty()) {
                roleMenuEntityList = cRoleMenuService.getMenuIdListByRoleIdList(roleIdList);
            }
            List<Integer> list = roleMenuEntityList.stream().map(CRoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
            cRoleEntities = cRoleService.queryByIdList(roleIdList);
            if (CollectionUtils.isNotEmpty(list)) {
                menuIdList = list;
            } else {
                menuIdList = Collections.singletonList(-1);
            }
        }
        CMenuVO menuVO = new CMenuVO();

        List<Integer> categoryList = cRoleEntities.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());
        if (categoryList.contains(0) || "1".equals(cMenuDTO.getRoleId())) {
            categoryList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        List categoryMap = categoryList.stream().sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).collect(Collectors.toList());
        menuVO.setCategoryList(categoryMap);

        List<CMenuEntity> menuList = cMenuDao.getMenuByCategoryId(cMenuDTO.getCategoryId(), menuIdList);
        if (CollectionUtils.isEmpty(menuList)) {
            return menuVO;
        }
        List<MenuDetailVO> allMenuDetailVOList = menuList.stream().map(
                i -> {
                    MenuDetailVO menuDetailVO = new MenuDetailVO();
                    menuDetailVO.setName(i.getCode() + "-" + i.getName())
                            .setIcon(i.getIcon())
                            .setUrl(i.getUrl())
                            .setParentId(i.getParentId())
                            .setId(i.getId())
                            .setCode(i.getCode())
                            .setCategoryId(i.getCategoryId())
                            .setParentCode(i.getParentCode())
                    ;
                    return menuDetailVO;
                }
        ).collect(Collectors.toList());

        List<MenuDetailVO> parentMenuDetailVOList = allMenuDetailVOList.stream().filter(i -> i.getParentId() == 0).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.toList());
        Map<String, List<MenuDetailVO>> map = allMenuDetailVOList.stream().filter(i -> i.getParentCode() != null).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.groupingBy(MenuDetailVO::getParentCode));
        parentMenuDetailVOList.forEach(i -> {
            i.setChildren(map.get(i.getCode()));
        });
        List<MenuDetailVO> collect = parentMenuDetailVOList.stream().filter(i -> !codeList.contains(i.getCode()) || (codeList.contains(i.getCode()) && i.getCategoryId().toString().equals(cMenuDTO.getCategoryId()))).collect(Collectors.toList());
        menuVO.setMenuDetailVOList(collect);
        return menuVO;
    }

    @Override
    public CMenuVO getMenuByRoleIdV2(CRoleDTO roleDTO) {
        CMenuVO menuVO = new CMenuVO();
        CMenuDTO defaultDTO = new CMenuDTO();
        defaultDTO
                .setCategoryId("0");
        CMenuVO defaultMenu = getMenusByConditionV2(defaultDTO);
//        List<String> defaultCode = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(defaultMenu.getMenuDetailVOList())) {
//            defaultCode = defaultMenu.getMenuDetailVOList().stream().map(MenuDetailVO::getCode).collect(Collectors.toList());
//        }
        List<String> defaultCode = defaultMenu.getMenuDetailVOList().stream().map(MenuDetailVO::getCode).collect(Collectors.toList());

        CMenuDTO sbmDTO = new CMenuDTO();
        sbmDTO
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        CMenuVO sbmMenu = getMenusByConditionV2(sbmDTO);
        List<MenuDetailVO> sbmList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sbmList = sbmMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());
        }

        CMenuDTO sboDTO = new CMenuDTO();
        sboDTO
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        CMenuVO sboMenu = getMenusByConditionV2(sboDTO);
        List<MenuDetailVO> sboList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sboList = sboMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_OIL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_OIL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());

        }
        List<CMenuEntity> parentMenu = cMenuDao.getMenuParentMenu();
        List<Integer> parentMenuIdList = parentMenu.stream().map(CMenuEntity::getId).collect(Collectors.toList());
        List<Integer> roleIdList = Arrays.asList(roleDTO.getRoleId());
        List<CRoleMenuEntity> roleMenuEntityList = cRoleMenuService.getMenuIdListByRoleIdList(roleIdList);
        List<Integer> menuIdList = roleMenuEntityList.stream().filter(i -> (!parentMenuIdList.contains(i.getMenuId())) || i.getMenuId() == 1).map(CRoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        menuVO.setSbmList(sbmList);
        menuVO.setSboList(sboList);
        menuVO.setChooseIdList(menuIdList);
        return menuVO;
    }

    @Override
    public CMenuVO getMenuByEmployIdV2(CRoleDTO roleDTO) {
        CMenuVO menuVO = new CMenuVO();
        String employId = roleDTO.getEmployId();

        //获取主体账号权限
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(Integer.valueOf(employId), roleDTO.getCustomerId());
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        List<CRoleEntity> cRoleEntities = cRoleService.queryByIdList(roleIdList);
        List<Integer> categoryList = cRoleEntities.stream().map(CRoleEntity::getCategoryId).distinct().collect(Collectors.toList());

        List<CEmployCustomerEntity> cEmployCustomerEntity = icEmployCustomerService.queryCEmployCustomerByCustomerIdAndCEmployIdAndType(roleDTO.getCustomerId(), Integer.valueOf(employId), CEmployTypeEnum.DEFAULT.getType());

        if (!cEmployCustomerEntity.isEmpty() && roleIdList.isEmpty()) {
            roleIdList = Collections.singletonList(1);
        }

        if (categoryList.contains(0)) {
            categoryList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        List categoryMap = categoryList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).collect(Collectors.toList());
        // 系统管理员展示所有菜单
        if (new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            List list = new ArrayList<>();
            Map map1 = new HashMap(1);
            map1.put(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_MEAL.getDesc());
            Map map2 = new HashMap(1);
            map2.put(GoodsCategoryEnum.OSM_OIL.getValue(), GoodsCategoryEnum.OSM_OIL.getDesc());
            list.add(map1);
            list.add(map2);
            categoryMap = list;
            employId = null;
        }

        menuVO.setCategoryList(categoryMap);

        CMenuDTO defaultDTO = new CMenuDTO();
        defaultDTO
                .setEmployId(employId)
                .setCustomerId(roleDTO.getCustomerId())
                .setCategoryId("0");

        CMenuVO defaultMenu = getMenusByConditionV2(defaultDTO);

        CMenuDTO sbmDTO = new CMenuDTO();
        sbmDTO
                .setEmployId(employId)
                .setCustomerId(roleDTO.getCustomerId())
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        CMenuVO sbmMenu = getMenusByConditionV2(sbmDTO);

        CMenuDTO sboDTO = new CMenuDTO();
        sboDTO
                .setEmployId(employId)
                .setCustomerId(roleDTO.getCustomerId())
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        CMenuVO sboMenu = getMenusByConditionV2(sboDTO);

        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        //todo 临时开放权限
        List<String> roleNameList = cRoleEntities.stream().map(CRoleEntity::getName).collect(Collectors.toList());
        List<MenuDetailVO> sbmMenuDetailVOList = sbmMenu.getMenuDetailVOList();
        List<MenuDetailVO> sboMenuDetailVOList = sboMenu.getMenuDetailVOList();
        // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 Start
        // if (commonProperties.getCustomerStatus() && !roleNameList.contains("物流文员")) {
        // 优化：物流文员角色不依赖nacos配置 Author: Mr 2025-03-31
        /*Result<TempPermissionDTO> result = deliveryApplyFacade.getCustomerTempPermission();
        if (result.isSuccess()) {
            TempPermissionDTO tempPermissionDTO = result.getData();

            if (tempPermissionDTO.getCustomerStatus() && !roleNameList.contains("物流文员")) {
                sbmMenuDetailVOList = sbmMenuDetailVOList.stream().filter(i -> !Arrays.asList("C021", "C022", "C023").contains(i.getCode())).collect(Collectors.toList());
                sboMenuDetailVOList = sboMenuDetailVOList.stream().filter(i -> !Arrays.asList("C021", "C022", "C023").contains(i.getCode())).collect(Collectors.toList());
            }
        }*/
        // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 End
        /*if (!cEmployCustomerEntity.isEmpty()) {
            menuVO.setCategoryList(sboMenu.getCategoryList());
        }*/

        menuVO.setSbmList(sbmMenuDetailVOList);
        menuVO.setSboList(sboMenuDetailVOList);
        return menuVO;
    }

    @Override
    public void saveRoleMenuV2(CMenuDTO menuDTO) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        cRoleMenuService.deleteRoleMenu(menuDTO.getRoleId());
        for (Integer menuId : menuDTO.getMenuIdList()) {
            CRoleMenuEntity cRoleMenuEntity = new CRoleMenuEntity();
            cRoleMenuEntity.setMenuId(menuId)
                    .setRoleId(Integer.parseInt(menuDTO.getRoleId()))
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            cRoleMenuService.saveRoleMenu(cRoleMenuEntity);
        }

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(menuDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_ROLE_MENU)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void addRoleMenu(CMenuDTO menuDTO) {
        List<CRoleEntity> roleEntities = cRoleService.queryByRoleDefIdList(menuDTO.getRoleDefIdList());
        if (CollectionUtils.isNotEmpty(roleEntities)) {
            for (CRoleEntity roleEntity : roleEntities) {
                for (Integer menuId : menuDTO.getMenuIdList()) {
                    CRoleMenuEntity roleMenuEntity = new CRoleMenuEntity();
                    roleMenuEntity
                            .setMenuId(menuId)
                            .setRoleId(roleEntity.getId());
                    cRoleMenuService.saveRoleMenu(roleMenuEntity);
                }
            }
        }
    }

    @Override
    public List<CMenuEntity> queryMenuList(MenuQO condition) {
        return cMenuDao.queryMenuList(condition);
    }

    @Override
    public List<CMenuEntity> getAuthMenuList(Integer userId, Integer category2, Integer customerId) {
        // 授权菜单
        List<CMenuEntity> menuList = new ArrayList<>();
        // 默认查询条件
        MenuQO menuQO = new MenuQO().setSystem(1).setLevel(1);
        // 查询通用
        if (category2 == 0) {
            menuQO.setIsCategory(0);
        }
        if (cRoleService.isAdmin(userId, customerId)) {
            // 是管理员
            menuList = this.queryMenuList(menuQO);
        } else {
            // 不是管理员
            // 用户关联的所有角色
            List<Integer> userRoleIdList = cEmployRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId).setCustomerId(customerId));
            //过滤实角色的品类（默认+品类）
            List<CRoleEntity> roleEntityList = roleService.queryByIdList(userRoleIdList);
            if (CollUtil.isNotEmpty(roleEntityList)) {
                userRoleIdList = roleEntityList.stream()
                        .filter(role -> Arrays.asList(category2, 0).contains(role.getCategory2()))
                        .map(CRoleEntity::getId)
                        .collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(userRoleIdList)) {
                // 角色关联菜单
                Set<Integer> menuIdList = cRoleMenuService.queryMenuIdList(new RoleMenuQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(menuIdList)) {
                    menuQO.setMenuIdList(menuIdList);
                    menuList = this.queryMenuList(menuQO);
                }
            }
        }
        if (CollUtil.isNotEmpty(menuList)) {
            menuList.forEach(entity -> {
                if (entity.getIsCategory() == 1) {
                    entity.setCategoryId(category2);
                }
                if (StringUtil.isNotNullBlank(entity.getUrl())) {
                    entity.setUrl(entity.getUrl().replaceAll("\\{category2\\}", category2.toString()));
                }
            });
        }
        return menuList;
    }
}
