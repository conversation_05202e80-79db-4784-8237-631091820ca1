package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.dto.PowerDTO;
import com.navigator.admin.pojo.entity.RolePowerEntity;
import com.navigator.admin.pojo.qo.RolePowerQO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
public interface IRolePowerService {

    void saveOrUpdateRolePower(PowerDTO powerDTO);

    List<RolePowerEntity> queryByRoleId(String roleId);

    List<RolePowerEntity> queryByRoleIdList(List<Integer> roleIdList);

    void addRolePower(PowerDTO powerDTO);

    void copyRolePower(Integer sourceRoleId, Integer targetRoleId);

    /**
     * 获取权限ID列表
     *
     * @param condition
     * @return
     */
    Set<Integer> queryPowerIdList(RolePowerQO condition);

    void updateDeletedByPowerIdAndRoleId(Integer powerId, Integer roleId);

    void saveRolePower(RolePowerEntity item);

    List<RolePowerEntity> getAllRolePowerList();
}
