package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.pojo.dto.CompanyDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.service.ICompanyService;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CompanyFacadeImpl implements CompanyFacade {

    @Autowired
    private ICompanyService companyService;

    @Override
    public List<CompanyEntity> queryCompanyList() {
        return companyService.queryCompanyList();
    }

    @Override
    public CompanyEntity queryCompanyById(Integer id) {
        return companyService.queryCompanyById(id);
    }

    @Override
    public CompanyEntity queryCompanyByName(String name) {
        return companyService.queryCompanyByName(name);
    }

    @Override
    public List<CompanyDTO> queryCompanyDTOList() {
        return companyService.queryCompanyDTOList();
    }

    @Override
    public Result saveCompany(CompanyDTO companyDTO) {
        return companyService.saveCompany(companyDTO);
    }

    @Override
    public Result updateCompany(CompanyDTO companyDTO) {
        return companyService.updateCompany(companyDTO);
    }

    @Override
    public Result updateCompanyStatus(CompanyDTO companyDTO) {
        return companyService.updateCompany(companyDTO);
    }

    @Override
    public CompanyEntity getCompanyByCode(String companyCode) {
        return companyService.getCompanyByCode(companyCode);
    }

    @Override
    public List<CompanyEntity> getAllCompany() {
        return companyService.getAllCompany();
    }

    @Override
    public List<CompanyEntity> getAllCompanyBySyncSystem(String syncSystem) {
        return companyService.getAllCompanyBySyncSystem(syncSystem);
    }
}
