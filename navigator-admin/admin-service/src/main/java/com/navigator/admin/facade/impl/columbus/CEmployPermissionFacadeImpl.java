package com.navigator.admin.facade.impl.columbus;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.facade.columbus.CEmployPermissionFacade;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.admin.service.columbus.ICEmployRoleService;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CEmployPermissionFacadeImpl implements CEmployPermissionFacade {

    @Autowired
    private ICEmployRoleService cEmployRoleService;

    @Override
    public Result getEmployRoleListByRoleIds(List<Integer> roleIdList) {
        List<CEmployRoleEntity> list = cEmployRoleService.getEmployRoleListByRoleIds(roleIdList);
        return Result.success(list);
    }

    @Override
    public void addEmployRole(CEmployRoleDTO employRoleDTO) {
        cEmployRoleService.addEmployRole(employRoleDTO);
    }

    @Override
    public void addEmployRoles(Integer employId, String roleIds, Integer roleDefId, Integer categoryId, String customerIds) {
        if (StringUtil.isNotEmpty(roleIds)) {
            cEmployRoleService.addEmployRoles(employId, roleIds);
        } else {
            cEmployRoleService.addEmployRoles(employId, roleDefId, categoryId, customerIds);
        }
    }

    @Override
    public void deleteEmployRole(CEmployRoleDTO employRoleDTO) {
        cEmployRoleService.deleteEmployRole(employRoleDTO);
    }

    @Override
    public void deleteByEmployId(Integer employId) {
        cEmployRoleService.deleteByEmployId(employId);
    }

    @Override
    public void save(CEmployRoleEntity cEmployRoleEntity) {
        cEmployRoleService.save(cEmployRoleEntity);
    }
}
