package com.navigator.admin.service.systemrule.impl;

import com.navigator.admin.dao.ProteinPriceConfigDao;
import com.navigator.admin.dao.SystemRuleItemDao;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.ProteinPriceConfigEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.service.systemrule.ProteinPriceConfigService;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9
 */
@Service
public class ProteinPriceConfigServiceImpl implements ProteinPriceConfigService {

    @Resource
    private ProteinPriceConfigDao proteinPriceConfigDao;
    @Resource
    private SystemRuleItemDao systemRuleItemDao;
    @Resource
    private SkuFacade skuFacade;

    @Override
    public SystemRuleItemEntity filterBasicProtein(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {

        if (null != basicPriceConfigQueryDTO.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(basicPriceConfigQueryDTO.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    basicPriceConfigQueryDTO.setAttributeValueId(skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId());
                }
            }
        }
        List<ProteinPriceConfigEntity> proteinPriceConfigEntities = proteinPriceConfigDao.filterBasicProtein(basicPriceConfigQueryDTO);
        if (proteinPriceConfigEntities.isEmpty()) {
            return null;
        }
        List<Integer> ruleItemIdList = proteinPriceConfigEntities.stream().map(ProteinPriceConfigEntity::getRuleItemId).distinct().collect(Collectors.toList());
        List<SystemRuleItemEntity> ruleItemEntities = systemRuleItemDao.getRuleItemByIdList(ruleItemIdList);
        return CollectionUtils.isEmpty(ruleItemEntities) ? null : ruleItemEntities.get(0);
    }
}
