package com.navigator.admin.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.dao.SequenceDao;
import com.navigator.admin.mapper.SequenceMapper;
import com.navigator.admin.pojo.entity.SequenceEntity;
import com.navigator.admin.service.ISequenceService;
import com.navigator.common.enums.SequenceEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * <p>
 * 序列号 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Slf4j
@Service
public class SequenceServiceImpl extends ServiceImpl<SequenceMapper, SequenceEntity> implements ISequenceService {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
    private final SimpleDateFormat yySdf = new SimpleDateFormat("yy");
    private final SimpleDateFormat yymmSdf = new SimpleDateFormat("yyMM");
    private final SimpleDateFormat yymmddhhhmmssSdf = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final Random random = new Random();

    @Resource
    private SequenceDao sequenceDao;

    @Resource
    private RedisUtil redisUtil;


    /**
     * 生成指定长度的简单序列号，如 C001
     *
     * @param sequenceEnum 序列号类型
     * @param length       顺序号长度
     * @return
     */
    @Override
    public String generate(SequenceEnum sequenceEnum, int length) {
        if (StrUtil.isBlank(sequenceEnum.getPrefix())) {
            throw new BusinessException("请使用具体的序列号生成方法！");
        }
        return nextSeq(length, -1, sequenceEnum, sequenceEnum.getPrefix());
    }

    /**
     * 按日期生成的序列号
     * CAD-6位日期-3位顺序号，例如：CAD230712001
     *
     * @param sequenceEnum
     * @return
     */
    @Override
    public String generateByDay(SequenceEnum sequenceEnum) {
        if (StrUtil.isBlank(sequenceEnum.getPrefix())) {
            throw new BusinessException("请使用具体的序列号生成方法！");
        }
        String today = sdf.format(new Date());
        return nextSeq(3, 48, sequenceEnum, sequenceEnum.getPrefix(), today);
    }

    /**
     * 按日期生成的序列号
     * CAD-6位日期-5位顺序号，例如：CAD23071200001
     *
     * @param sequenceEnum
     * @return
     */
    @Override
    public String generateByDayLength(SequenceEnum sequenceEnum, int length) {
        if (StrUtil.isBlank(sequenceEnum.getPrefix())) {
            throw new BusinessException("请使用具体的序列号生成方法！");
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String today = format.format(new Date());
        return nextSeq(length, 48, sequenceEnum, sequenceEnum.getPrefix(), today);
    }

    /**
     * 仓单注册号 "交易所代码+品种+注册年月+三位递增码 例如：DCEM2401001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @Override
    public String generateWarrantCode(String exchange, String category) {
        String yymm = yymmSdf.format(new Date());
        return nextSeq(3, -1, SequenceEnum.WARRANT_CODE, exchange, category, yymm);
    }

    /**
     * 仓单销售合同号	"交易所+品种+销售+分配年份+四位递增码 例如：DCEMS240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @Override
    public String generateWarrantSalesContractCode(String exchange, String category) {
        String yymm = yymmSdf.format(new Date());
        return nextSeq(4, -1, SequenceEnum.WARRANT_SALES_CONTRACT_CODE, exchange, category, "S", yymm);
    }

    /**
     * 仓单销售子合同号	"原合同号+-+3位递增码 例如：DCEMS240001-001"
     *
     * @param warrantSalesContractCode
     * @return
     */
    @Override
    public String generateWarrantSalesChildContractCode(String warrantSalesContractCode) {
        return nextSeq(3, -1, SequenceEnum.WARRANT_SALES_CHILD_CONTRACT_CODE, warrantSalesContractCode, "-");
    }

    /**
     * 仓单采购合同号	"交易所+品种+采购+分配年份+四位递增码 例如：DCEMP240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    @Override
    public String generateWarrantPurchaseContractCode(String exchange, String category) {
        String yymm = yymmSdf.format(new Date());
        return nextSeq(4, -1, SequenceEnum.WARRANT_PURCHASE_CONTRACT_CODE, exchange, category, "P", yymm);
    }

    /**
     * 仓单采购子合同号 "原合同号+-+3位递增码 例如：DCEMP240001-001"
     *
     * @param warrantPurchaseContractCode
     * @return
     */
    @Override
    public String generateWarrantPurchaseChildContractCode(String warrantPurchaseContractCode) {
        return nextSeq(3, -1, SequenceEnum.WARRANT_PURCHASE_CHILD_CONTRACT_CODE, warrantPurchaseContractCode, "-");
    }

    /**
     * 仓单合同TT编号 "按照现货规则： SC+年月日+时分秒+四位随机码 SC202408081833001234"
     *
     * @return
     */
    @Override
    public String generateWarrantContractTTCode() {
        String yymmddhhhmmss = yymmddhhhmmssSdf.format(new Date());
        return String.format("SC%s%s", yymmddhhhmmss, RandomUtil.randomNumbers(4));
    }

    /**
     * 仓单合同协议编号	按照现货规则：同一个合同的协议编号从000开始递增，3位递增码
     */
    @Override
    public String generateWarrantContractSignCode() {
        return nextSeq(3, -1, SequenceEnum.WARRANT_CONTRACT_SIGN_CODE, "");
    }


    /**
     * 仓单合同-提货权	"原合同号+-+T+3位递增码 例如：DCEMS240001-T001"
     *
     * @param contractCode
     * @return
     */
    @Override
    public String generateWarrantContractCargoRightsCode(String contractCode) {
        return nextSeq(3, -1, SequenceEnum.WARRANT_CONTRACT_CARGO_RIGHTS_CODE, contractCode, "-T");
    }


    /**
     * 现货销售合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    @Override
    public String generateSpotSalesContractCode(String supplier, String category) {
        String yy = yySdf.format(new Date());
        return nextSeq(5, -1, SequenceEnum.SPOT_SALES_CONTRACT_CODE, supplier, category, "S", yy);
    }

    /**
     * 现货销售子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotSalesContractCode
     * @return
     */
    @Override
    public String generateSpotSalesChildContractCode(String spotSalesContractCode) {
        return nextSeq(5, -1, SequenceEnum.SPOT_SALES_CHILD_CONTRACT_CODE, spotSalesContractCode, "-");
    }

    /**
     * 现货采购合同编号 1.父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param supplier
     * @param category
     * @return
     */
    @Override
    public String generateSpotPurchaseContractCode(String supplier, String category) {
        String yy = yySdf.format(new Date());
        return nextSeq(5, -1, SequenceEnum.SPOT_PURCHASE_CONTRACT_CODE, supplier, category, "P", yy);
    }

    /**
     * 现货采购子合同编号 "2.子合同编号规则：原合同编号-001 子合同再次变更也是原合同编号-递增
     *
     * @param spotPurchaseContractCode
     * @return
     */
    @Override
    public String generateSpotPurchaseChildContractCode(String spotPurchaseContractCode) {
        return nextSeq(5, -1, SequenceEnum.SPOT_PURCHASE_CHILD_CONTRACT_CODE, spotPurchaseContractCode, "-");
    }

    /**
     * 获取下一个序列号
     *
     * @param length
     * @param liveHours
     * @param sequenceEnum
     * @param fields
     * @return
     */
    private String nextSeq(int length, int liveHours, SequenceEnum sequenceEnum, String... fields) {
        String redisKey = "SN:" + sequenceEnum.name() + ":" + RedisUtil.getCacheKey(fields);
        String redisValue = redisUtil.getString(redisKey);
        // 如果redis取不到值，则去数据库取
        SequenceEntity sequenceEntity = sequenceDao.getOrAdd(redisKey);
        if (StringUtil.isNullBlank(redisValue)) {
            redisUtil.set(redisKey, sequenceEntity.getRedisValue(), liveHours);
        }
        Long seq = redisUtil.incr(redisKey, 1, liveHours);
        sequenceEntity.setRedisValue(seq);
        sequenceDao.updateById(sequenceEntity);
        String result = StrUtil.join("", fields) + StrUtil.fillBefore(seq + "", '0', length);
        log.info("生成序列号：{}={}", sequenceEnum.getName(), result);
        return result;
    }

}