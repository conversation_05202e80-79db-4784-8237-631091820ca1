package com.navigator.admin.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.BasicPriceConfigMapper;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.entity.BasicPriceConfigEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-13 01:36
 */
@Dao
public class BasicPriceConfigDao extends BaseDaoImpl<BasicPriceConfigMapper, BasicPriceConfigEntity> {

    /**
     * 记录基差基准价规则信息
     *
     * @param factoryCodeList
     * @param deliveryBeginDateList
     * @param ruleItemEntity
     */
    public void recordBasicPriceConfig(List<String> factoryCodeList,
                                       List<String> deliveryBeginDateList,
                                       SystemRuleItemEntity ruleItemEntity,
                                       Integer categoryId,
                                       Integer goodsId,
                                       String companyId,
                                       boolean isNew) {
        if (!isNew) {
            this.dropBasicPriceConfig(ruleItemEntity.getId());
        }
        for (String factoryCode : factoryCodeList) {
            for (String deliveryBeginDate : deliveryBeginDateList) {
                List<BasicPriceConfigEntity> basicPriceConfigEntityList = getBasicPriceListByRules(ruleItemEntity.getId(), factoryCode, deliveryBeginDate);
                if (CollectionUtils.isNotEmpty(basicPriceConfigEntityList)) {
                    continue;
                }
                BasicPriceConfigEntity basicPriceConfigEntity = new BasicPriceConfigEntity()
                        .setRuleItemId(ruleItemEntity.getId())
                        .setCategoryId(categoryId)
                        .setRuleId(ruleItemEntity.getRuleId())
                        .setFactoryCode(factoryCode)
                        .setDeliveryBeginDate(deliveryBeginDate)
                        .setGoodsId(goodsId)
                        .setCompanyId(JSONObject.toJSONString(companyId))
                        .setDomainCode(ruleItemEntity.getRuleKey());
                this.save(basicPriceConfigEntity);
            }
        }
    }

    /**
     * 删除所有关联关系
     *
     * @param ruleItemId 基差基准价ID
     */
    private void dropBasicPriceConfig(Integer ruleItemId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(BasicPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }

    /**
     * 根据基差基准价ID查询所有关联关系
     *
     * @param ruleItemId 基差基准价ID
     * @return 关联关系集合
     */
    public List<BasicPriceConfigEntity> getBasicPriceListByRuleItemId(Integer ruleItemId) {
        return this.list(Wrappers.<BasicPriceConfigEntity>lambdaQuery()
                .eq(BasicPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<BasicPriceConfigEntity> getBasicPriceListByRules(List<Integer> ruleItemIdList, List<String> factoryCodeList, List<String> deliveryBeginDateList, Integer goodsId) {
        if (CollectionUtils.isEmpty(ruleItemIdList) || CollectionUtils.isEmpty(factoryCodeList) || CollectionUtils.isEmpty(deliveryBeginDateList)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<BasicPriceConfigEntity>lambdaQuery()
                .in(BasicPriceConfigEntity::getRuleItemId, ruleItemIdList)
                .in(BasicPriceConfigEntity::getFactoryCode, factoryCodeList)
                .in(BasicPriceConfigEntity::getDeliveryBeginDate, deliveryBeginDateList)
                .eq(null != goodsId, BasicPriceConfigEntity::getGoodsId, goodsId)
                .eq(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<BasicPriceConfigEntity> getBasicPriceListByRules(Integer ruleItemId, String factoryCode, String deliveryBeginDate) {
        return this.list(Wrappers.<BasicPriceConfigEntity>lambdaQuery()
                .eq(BasicPriceConfigEntity::getRuleItemId, ruleItemId)
                .eq(BasicPriceConfigEntity::getFactoryCode, factoryCode)
                .eq(BasicPriceConfigEntity::getDeliveryBeginDate, deliveryBeginDate)
                .eq(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<BasicPriceConfigEntity> filterBasicPrice(BasicPriceConfigQueryDTO priceConfigQueryDTO) {
        return this.list(Wrappers.<BasicPriceConfigEntity>lambdaQuery()
                .eq(null != priceConfigQueryDTO.getCategoryId(), BasicPriceConfigEntity::getCategoryId, priceConfigQueryDTO.getCategoryId())
                .eq(StringUtils.isNotBlank(priceConfigQueryDTO.getDomainCode()), BasicPriceConfigEntity::getDomainCode, priceConfigQueryDTO.getDomainCode())
                .eq(StringUtils.isNotBlank(priceConfigQueryDTO.getFactoryCode()), BasicPriceConfigEntity::getFactoryCode, priceConfigQueryDTO.getFactoryCode())
                .eq(StringUtils.isNotBlank(priceConfigQueryDTO.getDeliveryBeginDate()), BasicPriceConfigEntity::getDeliveryBeginDate, priceConfigQueryDTO.getDeliveryBeginDate())
                .eq(null != priceConfigQueryDTO.getGoodsId(), BasicPriceConfigEntity::getGoodsId, priceConfigQueryDTO.getGoodsId())
                .like(StringUtils.isNotBlank(priceConfigQueryDTO.getCompanyId()), BasicPriceConfigEntity::getCompanyId, priceConfigQueryDTO.getCompanyId())
                .eq(BasicPriceConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
