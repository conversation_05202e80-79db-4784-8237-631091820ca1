package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.TagMapper;
import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.service.BaseDaoImpl;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 11:09
 */
@Dao
public class TagDao extends BaseDaoImpl<TagMapper, TagEntity> {

    /**
     * 分页查询标签
     *
     * @param queryDTO
     * @return
     */
    public Result queryTagEntity(QueryDTO<TagQueryDTO> queryDTO) {
        TagQueryDTO tagQueryDTO = queryDTO.getCondition();
        Page<TagEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        IPage<TagEntity> iPage = this.baseMapper.selectPage(page, Wrappers.<TagEntity>lambdaQuery()
                .eq(tagQueryDTO.getType() != null, TagEntity::getType, tagQueryDTO.getType())
                .eq(tagQueryDTO.getStatus() != null, TagEntity::getStatus, tagQueryDTO.getStatus())
                .like(tagQueryDTO.getName() != null, TagEntity::getName, tagQueryDTO.getName())
                .orderByDesc(TagEntity::getId));
        return Result.page(iPage);
    }

    /**
     * 根据id查询标签
     *
     * @param id
     * @return
     */
    public TagEntity queryTageEntity(Integer id) {
        return this.baseMapper.selectById(id);
    }

    /**
     * 新增标签
     *
     * @param tagEntity
     * @return
     */
    public Result saveTagEntity(TagEntity tagEntity) {
        return Result.success(this.baseMapper.insert(tagEntity));
    }

    /**
     * 修改标签
     *
     * @param tagEntity
     * @return
     */
    public Result updateTagEntity(TagEntity tagEntity) {
        return Result.success(this.baseMapper.updateById(tagEntity));
    }
}
