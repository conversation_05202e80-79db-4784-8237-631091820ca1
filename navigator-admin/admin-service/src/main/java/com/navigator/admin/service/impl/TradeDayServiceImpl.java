package com.navigator.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.dao.TradeDayDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.TradeDayCycleDTO;
import com.navigator.admin.pojo.dto.systemrule.TradeDayDTO;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.admin.pojo.enums.IsTradeDayEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.TradeDayType;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.ITradeDayService;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 贸易节假日表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Service
@Slf4j
public class TradeDayServiceImpl implements ITradeDayService {

    @Resource
    protected IOperationDetailService operationDetailService;
    @Resource
    private TradeDayDao tradeDayDao;
    @Resource
    private EmployFacade employFacade;

    @Override
    public TradeDayEntity getTradeDayByDay(String day) {
        TradeDayEntity tradeDayEntity = tradeDayDao.getTradeDayByDay(day);
        return tradeDayEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importTradeDay(List<TradeDayEntity> tradeDayList) {
        int num = 0;
        for (TradeDayEntity tradeDayEntity : tradeDayList) {
            tradeDayDao.update(tradeDayEntity, new LambdaQueryWrapper<TradeDayEntity>().eq(TradeDayEntity::getDayValue, tradeDayEntity.getDayValue()));
            num++;
        }
        return tradeDayList.size() == num ? Result.success() : Result.failure();
    }

    @Override
    public int getTradeDays(String startDay, String endDay) {
        return tradeDayDao.getTradeDays(startDay, endDay);
    }

    @Override
    public int getTradeDays(Date startDay, Date endDay) {
        return tradeDayDao.getTradeDays(startDay, endDay);
    }

    @Override
    public boolean isTradeDay(String date) {
        TradeDayEntity tradeDayEntity = getTradeDayByDay(date);
        return null != tradeDayEntity && tradeDayEntity.getIsTrade() == 1;
    }

    @Override
    public boolean isTradeDayValue(String date) {
        TradeDayEntity tradeDayEntity = tradeDayDao.getTradeDayByDayValue(date);
        return null != tradeDayEntity && tradeDayEntity.getIsTrade() == 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importNextYearDays(Integer year) {

        year = null == year ? DateUtil.thisYear() + 1 : year;
        // 当前的下一年份
        String nextYear = String.valueOf(year);

        DateTime startTime = DateUtil.parse(nextYear + "-01-01", DatePattern.NORM_DATE_PATTERN);
        DateTime endTime = DateUtil.parse(nextYear + "-12-31", DatePattern.NORM_DATE_PATTERN);

        // 创建日期范围生成
        // start 起始日期时间 end   结束日期时间  unit  步进单位
        List<DateTime> daysOfNextYearList = DateUtil.rangeToList(startTime, endTime, DateField.DAY_OF_YEAR);

        // sqlserver不支持批量返回id，所以会抛出异常 ,改为单挑条插入
        int successNum = 0;

        for (DateTime dateTime : daysOfNextYearList) {
            TradeDayEntity tradeDayEntity = new TradeDayEntity();
            tradeDayEntity.setDayValue(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
            tradeDayEntity.setYear(DateUtil.year(dateTime))
                    .setMonth(DateUtil.month(dateTime) + 1)
                    .setDay(DateUtil.dayOfMonth(dateTime))
                    .setIsHoliday(0)
                    .setIsTrade(DateUtil.dayOfWeekEnum(dateTime) == Week.SUNDAY ? 0 : 1)
                    .setIsWorkDay(DateUtil.dayOfWeekEnum(dateTime) == Week.SUNDAY || DateUtil.dayOfWeekEnum(dateTime) == Week.SATURDAY ? 0 : 1)
                    .setWeekDay(DateUtil.dayOfWeek(dateTime) - 1);
            boolean save = tradeDayDao.save(tradeDayEntity);

            if (save) {
                successNum++;
            }
        }
        return successNum == daysOfNextYearList.size() ? Result.success() : Result.failure();
    }

    @Override
    public List<TradeDayEntity> getTradeDayByMonth(String month) {
        // 当前年份
        Integer nowYear = DateUtil.thisYear();
        return tradeDayDao.getTradeDayByMonth(nowYear, month);
    }

    @Override
    public List<TradeDayEntity> getTradeDayByYear(Integer year) {
//        year = null != year ? year : DateUtil.thisYear();
        return tradeDayDao.getTradeDayByMonth(year, "");
    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public List<String> setNotTrade(TradeDayDTO tradeDayDTO) {
////        if (CollectionUtils.isEmpty(tradeDayDTO.getNotTradeDayList())) {
////            throw new BusinessException(ResultCodeEnum.SELECT_TRADE_DAY);
////        }
//        //判断操作的年份
//        List<Integer> yearList = new ArrayList<>();
//        tradeDayDTO.getNotTradeDayList().forEach(notTradeDay -> {
//            Integer year = DateUtil.year(DateUtil.parse(notTradeDay, DatePattern.NORM_DATE_PATTERN));
//            if (!yearList.contains(year)) {
//                yearList.add(year);
//            }
//        });
//        String operatorId = JwtUtils.getCurrentUserId();
//        yearList.forEach(year -> {
//            List<TradeDayEntity> tradeDayEntityList = tradeDayDao.getTradeDayByMonth(year, "");
//            //
//            if (CollectionUtils.isEmpty(tradeDayEntityList)) {
//                this.importNextYearDays(year);
//                tradeDayEntityList = tradeDayDao.getTradeDayByMonth(year, "");
//            }
//            tradeDayEntityList.forEach(tradeDayEntity -> {
//                tradeDayDao.updateById(tradeDayEntity
//                        .setIsTrade(tradeDayDTO.getNotTradeDayList().contains(tradeDayEntity.getDayValue()) ?
//                                IsTradeDayEnum.NOT_TRADE_DAY.getValue() : IsTradeDayEnum.TRADE_DAY.getValue())
//                        .setUpdatedBy(operatorId)
//                );
//            });
//        });
//        return tradeDayDTO.getNotTradeDayList();
//    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> setNotTrade(List<TradeDayDTO> tradeDayDTOList) {
//        if (CollectionUtils.isEmpty(tradeDayDTO.getNotTradeDayList())) {
//            throw new BusinessException(ResultCodeEnum.SELECT_TRADE_DAY);
//        }
        //判断操作的年份
        String operatorId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(operatorId));
        List<String> notTradeDayList = new ArrayList<>();
        for (TradeDayDTO tradeDayDTO : tradeDayDTOList) {
            TradeDayEntity tradeDayEntity = tradeDayDao.getTradeDayByDayValue(tradeDayDTO.getDayValue());
            if (null == tradeDayEntity) {
                Integer year = DateUtil.year(DateUtil.parse(tradeDayDTO.getDayValue(), DatePattern.NORM_DATE_PATTERN));
                this.importNextYearDays(year);
                tradeDayEntity = tradeDayDao.getTradeDayByDayValue(tradeDayDTO.getDayValue());
            }
            tradeDayDao.updateById(tradeDayEntity
                    .setIsTrade(tradeDayDTO.getIsTradeDay())
                    .setUpdatedBy(operatorId)
                    .setUpdatedByName(name)
                    .setUpdatedAt(new Date())
            );
            if (IsTradeDayEnum.NOT_TRADE_DAY.getValue() == tradeDayDTO.getIsTradeDay()) {
                notTradeDayList.add(tradeDayDTO.getDayValue());
            }
        }

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(tradeDayDTOList))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(notTradeDayList))
                    .setOperationActionEnum(OperationActionEnum.SET_NOT_TRADE)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }


        return notTradeDayList;
    }

    @Override
    public TradeDayEntity getTradeDayByDayAgo(String dayValue) {
        return tradeDayDao.getTradeDayByDayAgo(dayValue);
    }

    @Override
    public TradeDayEntity getTradeDayByDayValueAndCategory2(String dayValue, Integer category2) {
        return null;
    }

    @Override
    public TradeDayCycleDTO getTradeDayCycleDTO(String futureCode, String dayValue) {
        TradeDayType tradeDayType = TradeDayType.getByValue(futureCode);
        if (tradeDayType == null) {
            throw new BusinessException("交易日期类型错误！");
        }
        if (StringUtil.isBlank(dayValue)) {
            dayValue = DateTimeUtil.formatDateString();
        }
        String[] arr = dayValue.split("-");
        String firstDay = arr[0] + "-" + arr[1] + "-01";
        LambdaQueryWrapper<TradeDayEntity> lqw = new LambdaQueryWrapper<TradeDayEntity>().eq(TradeDayEntity::getIsTrade, 1).ge(TradeDayEntity::getDayValue, firstDay).orderByAsc(TradeDayEntity::getDayValue);
        List<TradeDayEntity> list = tradeDayDao.list(lqw);
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException("获取开始注销日期失败！");
        }
        TradeDayCycleDTO tradeDayCycleDTO = new TradeDayCycleDTO();
        tradeDayCycleDTO.setStartDay(this.getStartDayValue(dayValue));
        if (TradeDayType.SOYBEAN_MEAL.equals(futureCode)) {
            // 豆粕：3、7、11月的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "3,7,11", 0, tradeDayCycleDTO.getStartDay()));
        } else if (TradeDayType.SOYBEAN_OIL.equals(futureCode)) {
            // 豆油：3、7、11月的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "3,7,11", 0, tradeDayCycleDTO.getStartDay()));
        } else if (TradeDayType.SOYBEAN2.equals(futureCode)) {
            // 豆二：每个月的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "1,2,3,4,5,6,7,8,9,10,11,12", 0, tradeDayCycleDTO.getStartDay()));
        } else if (TradeDayType.PALM_OIL.equals(futureCode)) {
            // 棕榈油：每个月的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "1,2,3,4,5,6,7,8,9,10,11,12", 0, tradeDayCycleDTO.getStartDay()));
        } else if (TradeDayType.VEGETABLE_MEAL.equals(futureCode)) {
            // 菜粕：3、7、11的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "3,7,11", 0, tradeDayCycleDTO.getStartDay()));
        } else if (TradeDayType.VEGETABLE_OIL.equals(futureCode)) {
            // 菜油：5月的最后一个交易日
            tradeDayCycleDTO.setEndDay(this.getEndDayValue(list, "5", 0, tradeDayCycleDTO.getStartDay()));
        }
        if (StringUtil.isBlank(tradeDayCycleDTO.getEndDay())) {
            throw new BusinessException("获取截止注销日期失败！");
        }
        log.info("截止交易日：{}", tradeDayCycleDTO.getEndDay());
        return tradeDayCycleDTO;
    }

    /**
     * 获取开始注销日期 = 提交日期+1自然日
     *
     * @param dayValue
     * @return
     */
    private String getStartDayValue(String dayValue) {
        Date day = DateTimeUtil.parseDateString(dayValue);
        Date nextDay = DateTimeUtil.addDays(day, 1, true);
        return DateTimeUtil.formatDateString(nextDay);
    }

    /**
     * 获取截止注销日期
     *
     * @param list          集合
     * @param months        指定月份
     * @param dayCount      第几个交易日，0：最后一个
     * @param startDayValue
     * @return
     */
    private String getEndDayValue(List<TradeDayEntity> list, String months, int dayCount, String startDayValue) {
        List<String> monthList = StringUtil.split(months, ",");
        List<String> excludeMonthList = new ArrayList<>();
        log.info("months:{},dayCount:{},startDayValue:{}", months, dayCount, startDayValue);
        int count = dayCount - 1;
        for (int i = 0, listSize = list.size(); i < listSize; i++) {
            TradeDayEntity item = list.get(i);
            String currentMonth = item.getYear() + "-" + item.getMonth();
            if (excludeMonthList.contains(currentMonth)) {
                continue;
            }
            TradeDayEntity next = null;
            if (i + 1 < listSize) {
                next = list.get(i + 1);
            }
            if (monthList.contains(item.getMonth().toString())) {
                log.info("{}:{}", count, item.getDayValue());
                log.info("是指定月份");
                if (count == -1) {
                    log.info("最后一个交易日");
                    if (next != null) {
                        log.info("还有下一个");
                        if (next.getMonth().equals(item.getMonth())) {
                            log.info("下一个是同一个月");
                            continue;
                        } else {
                            log.info("下一个非同一个月");
                            if (item.getDayValue().compareTo(startDayValue) >= 0) {
                                log.info("找到交易日");
                                return item.getDayValue();
                            } else {
                                log.info("截止交易日应大于开始交易日，重新计数。应跳过本月。");
                                excludeMonthList.add(currentMonth);
                                count = dayCount - 1;
                            }
                        }
                    } else {
                        log.info("没有下一个");
                    }
                } else if (count > 0) {
                    log.info("继续查找交易日");
                    if (next != null) {
                        if (next.getMonth().equals(item.getMonth())) {
                            log.info("下一个是同一个月");
                            count--;
                        } else {
                            log.info("下一个非同一个月，重新计数");
                            count = dayCount - 1;
                        }
                    } else {
                        log.info("没有下一个");
                    }
                } else if (count == 0) {
                    if (item.getDayValue().compareTo(startDayValue) >= 0) {
                        log.info("找到交易日");
                        return item.getDayValue();
                    } else {
                        log.info("截止交易日应大于开始交易日，重新计数。应跳过本月。");
                        excludeMonthList.add(currentMonth);
                        count = dayCount - 1;
                    }
                }
            } else {
                log.info("非指定月份，重新计数");
                count = dayCount - 1;
                continue;
            }
        }
        return null;
    }
}
