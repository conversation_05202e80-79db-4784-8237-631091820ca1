package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.CRoleMapper;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.RoleQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.CommonListUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 18:09
 */
@Dao
public class CRoleDao extends BaseDaoImpl<CRoleMapper, CRoleEntity> {

    public List<CRoleEntity> getRoleList(Integer system) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setSystem(system);

        return queryRoleList(roleQueryDTO);
    }

    public CRoleEntity getRoleById(Integer id) {
        return this.getOne(new LambdaQueryWrapper<CRoleEntity>().eq(CRoleEntity::getId, id)
                .eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public List<CRoleEntity> getRoleByRoleName(String roleName) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleName(roleName);

        return queryRoleList(roleQueryDTO);
    }

    public void deleteByRoleDefId(Integer roleDefId) {
        remove(Wrappers.<CRoleEntity>lambdaQuery()
                .eq(CRoleEntity::getRoleDefId, roleDefId)
        );
    }

    public List<CRoleEntity> getRoleListByDefId(Integer roleDefId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefId(roleDefId);

        return queryRoleList(roleQueryDTO);
    }


    public List<CRoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefIdList(roleDefIdList);

        return queryRoleList(roleQueryDTO);
    }

    public List<CRoleEntity> queryRole(CEmployRoleDTO employRoleDTO) {
        if (CollectionUtils.isNotEmpty(employRoleDTO.getCategoryIdList())) {
            employRoleDTO.getCategoryIdList().add(0);
        }
        return list(Wrappers.<CRoleEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(employRoleDTO.getName()),CRoleEntity::getName,employRoleDTO.getName())
                .eq(StringUtils.isNotBlank(employRoleDTO.getSalesType()), CRoleEntity::getRoleDefCode, employRoleDTO.getRoleDefCode())
                .eq(StringUtils.isNotBlank(employRoleDTO.getCategoryId()), CRoleEntity::getCategoryId, employRoleDTO.getCategoryId())
                .eq(StringUtils.isNotBlank(employRoleDTO.getRoleDefCode()), CRoleEntity::getRoleDefCode, employRoleDTO.getRoleDefCode())
                .eq(null != employRoleDTO.getRoleDefId(), CRoleEntity::getRoleDefId, employRoleDTO.getRoleDefId())
                //.eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(CollectionUtils.isNotEmpty(employRoleDTO.getCategoryIdList()), CRoleEntity::getCategoryId, employRoleDTO.getCategoryIdList())

        );
    }

    public List<CRoleEntity> queryByIdList(List<Integer> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            roleIdList = Collections.singletonList(-1);
        }
       return list(Wrappers.<CRoleEntity>lambdaQuery()
               .in(CRoleEntity::getId, roleIdList)
               .eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
       ) ;
    }

    public List<CRoleEntity> queryRoleList(RoleQueryDTO roleQueryDTO) {
        LambdaQueryWrapper<CRoleEntity> queryWrapper = Wrappers.<CRoleEntity>lambdaQuery()
                .eq(null != roleQueryDTO.getRoleId(), CRoleEntity::getId, roleQueryDTO.getRoleId())
                .eq(StringUtils.isNotEmpty(roleQueryDTO.getRoleCode()), CRoleEntity::getCode, roleQueryDTO.getRoleCode())
                .like(StringUtils.isNotBlank(roleQueryDTO.getRoleName()), CRoleEntity::getName, "%" + roleQueryDTO.getRoleName() + "%")

                .eq(null != roleQueryDTO.getRoleDefId(), CRoleEntity::getRoleDefId, roleQueryDTO.getRoleDefId())
                .eq(null != roleQueryDTO.getSalesType(), CRoleEntity::getSalesType, roleQueryDTO.getSalesType())
                //.eq(StringUtils.isNotEmpty(roleQueryDTO.getRoleDefCode()), CRoleEntity::getRoleDefCode, roleQueryDTO.getRoleDefCode())
                .eq(CRoleEntity::getIsDeleted, roleQueryDTO.getIsDeleted())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleIdList()), CRoleEntity::getId, roleQueryDTO.getRoleIdList())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleCodeList()), CRoleEntity::getCode, roleQueryDTO.getRoleCodeList())
                .in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList()), CRoleEntity::getRoleDefId, roleQueryDTO.getRoleDefIdList())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefCodeList()), CRoleEntity::getRoleDefCode, roleQueryDTO.getRoleDefCodeList())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getCategoryIdList()), CRoleEntity::getCategoryId, roleQueryDTO.getCategoryIdList())
                //.in(CommonListUtil.notNullOrEmpty(roleQueryDTO.getCompanyIdList()), CRoleEntity::getBelongCustomerId, roleQueryDTO.getCompanyIdList())
                .like(StringUtils.isNotBlank(roleQueryDTO.getFuzzyRoleName()), CRoleEntity::getName, roleQueryDTO.getFuzzyRoleName());

        if (null != roleQueryDTO.getRoleDefId()
                || CommonListUtil.notNullOrEmpty(roleQueryDTO.getRoleDefIdList())
        ) {
            if (null != roleQueryDTO.getCategoryId()) {
                queryWrapper.and(wrapper -> wrapper.eq(CRoleEntity::getCategoryId, roleQueryDTO.getCategoryId()).or().eq(CRoleEntity::getCategoryId, 0));
            }
        } else {
            queryWrapper.eq(null != roleQueryDTO.getCategoryId(), CRoleEntity::getCategoryId, roleQueryDTO.getCategoryId());
        }

        return list(queryWrapper);
    }

    public List<CRoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId) {
        List<Integer> categoryIdList = new ArrayList<>();
        categoryIdList.add(categoryId);
        categoryIdList.add(0);
        if (CollectionUtils.isEmpty(roleIdList)) {
            roleIdList=Collections.singletonList(-1);
        }
        return list(Wrappers.<CRoleEntity>lambdaQuery()
                .in(CRoleEntity::getId, roleIdList)
                .in(CRoleEntity::getCategoryId,categoryIdList)
                .eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public List<CRoleEntity> getRoleAllList() {
        return list(Wrappers.<CRoleEntity>lambdaQuery()
                .eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

    }

    public IPage<CRoleEntity> queryPageByQueryDTO(Page<CRoleEntity> page, CRoleQueryDTO roleQueryDTO, List<Integer> forbiddenRoleDefIdList) {
        IPage<CRoleEntity> iPage = page(page, Wrappers.<CRoleEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(roleQueryDTO.getRoleName()), CRoleEntity::getName, "%" + roleQueryDTO.getRoleName() + "%")
                .notIn(CollectionUtils.isNotEmpty(forbiddenRoleDefIdList), CRoleEntity::getRoleDefId, forbiddenRoleDefIdList)
                .eq(CRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CRoleEntity::getId)
        );
        return iPage;
    }

    /**
     * 查询角色ID列表
     *
     * @param condition
     * @return
     */
    public List<Integer> queryRoleIdList(RoleQO condition) {
        List<CRoleEntity> list = this.list(CRoleEntity.lqw(condition));
        List<Integer> result = new ArrayList<>();
        list.forEach(item -> result.add(item.getId()));
        return result;
    }
}
