package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.ProteinPriceConfigFacade;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.service.systemrule.ProteinPriceConfigService;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9
 */
@RestController
public class ProteinPriceConfigFacadeImpl implements ProteinPriceConfigFacade {

    @Resource
    private ProteinPriceConfigService proteinPriceConfigService;

    @Override
    public Result filterBasicProtein(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {
        return Result.success(proteinPriceConfigService.filterBasicProtein(basicPriceConfigQueryDTO));
    }
}
