package com.navigator.admin.service.columbus.impl;

import com.navigator.admin.dao.columbus.CPowerDao;
import com.navigator.admin.pojo.entity.CPowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.admin.service.columbus.ICPowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CPowerServiceImpl implements ICPowerService {

    @Autowired
    private CPowerDao cPowerDao;

    @Override
    public List<CPowerEntity> queryPower() {
        return cPowerDao.queryAllPower();
    }

    @Override
    public List<CPowerEntity> queryPowerByIdList(List<Integer> powerIdList) {
        return cPowerDao.queryPowerByIdList(powerIdList);

    }

    @Override
    public List<CPowerEntity> queryPowerList(PowerQO condition) {
        return cPowerDao.queryPowerList(condition);
    }
}
