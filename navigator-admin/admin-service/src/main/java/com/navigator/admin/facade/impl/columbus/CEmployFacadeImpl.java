package com.navigator.admin.facade.impl.columbus;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.mapper.CEmployMapper;
import com.navigator.admin.mapper.CEmployRoleMapper;
import com.navigator.admin.mapper.CRoleMapper;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.ResetPasswordDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployBusinessDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.admin.pojo.vo.columbus.CEmployDetailVO;
import com.navigator.admin.pojo.vo.columbus.CEmployVO;
import com.navigator.admin.pojo.vo.columbus.ColumbusAdminVO;
import com.navigator.admin.service.columbus.ICEmployRoleService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.GeneralEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/1 13:44
 */
@RestController
@Slf4j
public class CEmployFacadeImpl implements CEmployFacade {

    @Resource
    private CEmployMapper cEmployMapper;
    @Resource
    private CEmployRoleMapper cEmployRoleMapper;
    @Resource
    private CRoleMapper cRoleMapper;
    @Autowired
    private ICEmployService cEmployService;
    @Autowired
    private ICEmployRoleService cEmployRoleService;

    @Autowired
    private CustomerFacade customerFacade;

    @Override
    public Result getEmployByEmail(String email) {
        List<CEmployEntity> CEmployEntityList = cEmployService.getEmployByEmail(email);
        return Result.success(CEmployEntityList);
    }

    @Override
    public Result getEmployByPhone(String phone, Integer type) {
        List<CEmployEntity> CEmployEntityList = cEmployService.getEmployByPhone(phone, type);
        return Result.success(CEmployEntityList);
    }

    @Override
    public Result queryEmployByPhone(String phone) {
        List<CEmployEntity> CEmployEntityList = cEmployService.queryEmployByPhone(phone);
        return Result.success(CEmployEntityList);
    }

    @Override
    public CEmployEntity getEmployById(Integer id) {
        return cEmployService.getEmployById(id);
    }

    @Override
    public Integer getCustomColumbusStateById(Integer customerId) {
        CustomerDTO customer = customerFacade.getCustomerById(customerId);
        if (null == customer)
            return 0;
        return GeneralEnum.NO.getValue().equals(customer.getIsColumbus()) ? 0 : 1;
    }

    @Override
    public CEmployEntity getEmployNickName(String nickName) {
        return cEmployService.getEmployByNickName(nickName);
    }

    @Override
    public List<CEmployEntity> getEmployByEmployIds(List<Integer> employIds) {
        return cEmployService.getEmployByEmployIds(employIds);
    }

    @Override
    public Integer saveOrUpdate(CEmployEntity cEmployEntity) {
        return cEmployService.saveOrUpdate(cEmployEntity);
    }

    @Override
    public Integer editEmploy(CEmployEntity cEmployEntity) {
        return cEmployMapper.updateById(cEmployEntity);
    }

    @Override
    public Integer deleteEmploy(String employId) {
        return cEmployMapper.deleteById(employId);
    }

    @Override
    public Result findRoleAdministratorEmploys() {


        List<CEmployEntity> roleAdministratorEmploys = new ArrayList<>();

        QueryWrapper<CRoleEntity> queryWrapperRole = new QueryWrapper<>();
        queryWrapperRole.eq("name", "角色管理员");
        CRoleEntity roleAdministratorEntity = cRoleMapper.selectOne(queryWrapperRole);

        QueryWrapper<CEmployRoleEntity> queryWrapperEmployRole = new QueryWrapper<>();
        queryWrapperEmployRole.eq("role_id", roleAdministratorEntity.getId());
        List<CEmployRoleEntity> employRoleEntities = cEmployRoleMapper.selectList(queryWrapperEmployRole);

        for (CEmployRoleEntity cEmployRoleEntity : employRoleEntities) {
            CEmployEntity cEmployEntity = cEmployMapper.selectById(cEmployRoleEntity.getEmployId());
            roleAdministratorEmploys.add(cEmployEntity);
        }
        return Result.success(roleAdministratorEmploys);

    }

    @Override
    public CEmployEntity ifNotExistToSave(CEmployEntity CEmployEntity) {
        return cEmployService.ifNotExistToSave(CEmployEntity);
    }

    @Override
    public List<CEmployEntity> queryEmployByRoleIds(List<Integer> roleIds) {
        List<CEmployRoleEntity> CEmployRoleEntityList = cEmployRoleService.getEmployRolesByRoleIds(roleIds);
        return cEmployService.getEmployByEmployIds(CEmployRoleEntityList.stream().map(CEmployRoleEntity::getEmployId).collect(Collectors.toList()));

    }

    @Override
    public List<CEmployEntity> getEmployRolesByCustomerId(List<Integer> roleIds, Integer customerId) {
        List<CEmployRoleEntity> CEmployRoleEntityList = cEmployRoleService.getEmployRolesByCustomerId(roleIds, customerId);
        return cEmployService.getEmployByEmployIds(CEmployRoleEntityList.stream().map(CEmployRoleEntity::getEmployId).collect(Collectors.toList()));
    }

    @Override
    public Result getEmployByEmailOrPhone(String email, String phone) {
        List<CEmployEntity> CEmployEntityList = cEmployService.getEmployByEmailOrPhone(email, phone);
        return Result.success(CEmployEntityList);
    }

    @Override
    public Result queryChoosedEmployByRoleId(QueryDTO<CRoleDTO> queryDTO) {
        return cEmployService.queryChoosedEmployByRoleId(queryDTO);
    }

    @Override
    public CEmployDetailVO queryCurrentEmployDetail(Integer employId) {
        return cEmployService.queryCurrentEmployDetail(employId);
    }

    @Override
    public Result exportEmployRoleList() {
        return cEmployService.exportEmployRoleList();
    }

    @Override
    public Result queryColumbusAdminList(QueryDTO<CEmployDTO> queryDTO) {
        return cEmployService.queryColumbusAdminList(queryDTO);
    }

    @Override
    public Result updateColumbusAdmin(CEmployDTO cEmployDTO) {
        cEmployService.updateColumbusAdmin(cEmployDTO);
        return Result.success();
    }

    @Override
    public Result updateCEmploy(CEmployEntity cEmployEntity) {
        cEmployService.updateCEmploy(cEmployEntity);
        return Result.success();
    }

    @Override
    public Result exportColumbusAdminList(CEmployDTO cEmployDTO) {
        QueryDTO<CEmployDTO> queryDTO = new QueryDTO<CEmployDTO>();
        queryDTO.setCondition(cEmployDTO);
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(1000000);
        Result result = cEmployService.queryColumbusAdminList(queryDTO);
        List<ColumbusAdminVO> columbusAdminVOList = new ArrayList<>();
        if (result != null && ResultCodeEnum.OK.getCode() == result.getCode()) {
            columbusAdminVOList = JSON.parseArray(JSON.toJSONString(result.getData()), ColumbusAdminVO.class);
        }
        return Result.success(columbusAdminVOList);
    }

    @Override
    public Result getBatchEmployList(int size) {
        List<List<CEmployEntity>> batchEmployList = cEmployService.getBatchEmployList(size);
        return Result.success(batchEmployList);
    }

    @Override
    public Result queryColumbusList(QueryDTO<CEmployDTO> queryDTO) {
        return queryColumbusAdminList(queryDTO);
    }

    @Override
    public Result exportColumbusList(CEmployDTO cEmployDTO) {
        return exportColumbusAdminList(cEmployDTO);
    }

    @Override
    public void updateTypeByIds(List<Integer> ids) {
        cEmployService.updateTypeByIds(ids);
    }

    @Override
    public Result setColumbusAdmin(CEmployDTO cEmployDTO) {
        cEmployService.setColumbusAdmin(cEmployDTO);
        return Result.success();
    }

    @Override
    public Result setAdmin(CEmployDTO cEmployDTO) {
        return cEmployService.setAdmin(cEmployDTO);
    }

    @Override
    public Result queryAdminType(Integer customerId) {
        return cEmployService.queryAdminType(customerId);
    }

    @Override
    public Result getEmployByCustomerId(Integer customerId, int type) {
        List<CEmployEntity> cEmployEntityList = cEmployService.getEmployByCustomerId(customerId, type);
        return Result.success(cEmployEntityList);
    }

    @Override
    public List<EmployVO> queryEmployByCompanyId(Integer companyId) {
        return BeanConvertUtils.convert2List(EmployVO.class, cEmployService.queryEmployByCompanyId(companyId));
    }

    @Override
    public Result updateEmployResetPassword(Integer id) {
        return Result.success(cEmployService.updateEmployResetPassword(id));
    }

    @Override
    public Result modifyPassword(CEmployEntity CEmployEntity) {
        return cEmployService.modifyPassword(CEmployEntity);
    }

    @Override
    public List<CEmployEntity> getEmployByRoleName(String roleName) {
        return cEmployService.getEmployByRoleName(roleName);
    }

    @Override
    public CEmployEntity queryEmployByCustomerId(Integer customerId) {
        return cEmployService.queryEmployByCustomerId(customerId);
    }

    @Override
    public Result saveOrUpdateEmploy(CEmployBusinessDTO employBusinessDTO) {
        cEmployService.saveOrUpdateEmploy(employBusinessDTO);
        return Result.success();
    }

    @Override
    public Result queryEmployList(QueryDTO<CEmployDTO> queryDTO) {
        return cEmployService.queryEmployList(queryDTO);
    }

    @Override
    public CEmployDetailVO queryEmployDetail(Integer employId, Integer customerId) {
        return cEmployService.queryEmployDetail(employId, customerId);
    }

    @Override
    public Result<List<CEmployVO>> queryEmployListByRoleDefId(Integer roleDefId) {
        List<CEmployVO> list = cEmployService.queryEmployListByRoleDefId(roleDefId);
        return Result.success(list);
    }

    @Override
    public String resetPassword(Integer employId) {
        return cEmployService.resetPassword(employId);
    }

    @Override
    public Result<List<CEmployVO>> queryAvailableEmployByRoleDefId(Integer roleDefId) {
        List<CEmployVO> list = cEmployService.queryAvailableEmployByRoleDefId(roleDefId);
        return Result.success(list);
    }

    @Override
    public Result queryCategoryFactoryByRole() {
        CategoryFactoryMenuVO categoryFactoryMenuVO = cEmployService.queryCategoryFactoryByRole();
        return Result.success(categoryFactoryMenuVO);
    }

    @Override
    public PermissionBO queryPermission(String employId, Integer categoryId) {
        return cEmployService.queryPermissionByEmployId(employId, categoryId);
    }

    @Override
    public Result updateEmployStatus(CEmployDTO employDTO) {
        return cEmployService.updateEmployStatus(employDTO);
    }

    @Override
    public Result saveEmployStatus(CEmployEntity CEmployEntity) {
        return cEmployService.saveEmployStatus(CEmployEntity);
    }

    @Override
    public Result resetUserPassword(ResetPasswordDTO resetPasswordDTO) {
        return cEmployService.resetUserPassword(resetPasswordDTO);
    }

    @Override
    public Result sendResetPasswordCode(String mobileNo) {
        return cEmployService.sendResetPasswordCode(mobileNo);
    }

    @Override
    public Result resetNotLogUserPassword(ResetPasswordDTO resetPasswordDTO) {
        return cEmployService.resetNotLogUserPassword(resetPasswordDTO);
    }

    @Override
    public Result sendResetPasswordPhoneCode(String mobileNo) {
        return cEmployService.sendResetPasswordPhoneCode(mobileNo);
    }

    @Override
    public Result sendAadCode(String mobileNo) {
        return cEmployService.sendAadCode(mobileNo);

    }

    @Override
    public Result verifyAadCode(LoginDTO loginDTO) {
        return cEmployService.verifyAadCode(loginDTO);
    }

    @Override
    public Result importEmploy(MultipartFile file) {
        return cEmployService.importEmploy(file);
    }

    @Override
    public List<CEmployEntity> getEmploy(String roleDefId, String categoryId, String salesType) {
        return cEmployService.getEmploy(roleDefId, categoryId, salesType);
    }

    @Override
    public List<CEmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String salesType) {
        return cEmployService.getEmployByRoleDefCode(roleDefCode, categoryId, salesType);
    }

}
