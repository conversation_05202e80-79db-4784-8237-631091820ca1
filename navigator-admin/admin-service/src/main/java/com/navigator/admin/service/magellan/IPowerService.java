package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
public interface IPowerService {

    List<PowerEntity> queryPower();


    List<PowerEntity> queryPowerByIdList(List<Integer> powerIdList);

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    List<PowerEntity> queryPowerList(PowerQO condition);
}
