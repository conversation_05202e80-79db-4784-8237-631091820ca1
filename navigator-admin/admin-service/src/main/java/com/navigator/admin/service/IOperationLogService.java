package com.navigator.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.bo.OperationLogBO;
import com.navigator.admin.pojo.dto.ColumbusOperationDTO;
import com.navigator.admin.pojo.dto.ContractOperationEventDTO;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.entity.DbzRedAlarmEntity;
import com.navigator.admin.pojo.entity.OperationLogEntity;
import com.navigator.common.dto.QueryDTO;

import java.util.List;

/**
 * <p>
 * 操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IOperationLogService extends IService<OperationLogEntity> {

    /**
     * 保存log及发送消息
     *
     * @param operationDetailDTO
     */
    void saveOperationLog(OperationDetailDTO operationDetailDTO);

    void saveOperationLog(ColumbusOperationDTO columbusOperationDTO);
    /**
     * 分页查询log
     *
     * @param queryDTO
     * @return
     */
    IPage<OperationLogEntity> queryOperationLog(QueryDTO<OperationLogBO> queryDTO);

    /**
     * 根据业务code查询log
     *
     * @param code
     * @return
     */
    List<OperationLogEntity> queryOperationLogByCode(String code, Integer logLevel, Integer id);

    List<ContractOperationEventDTO> queryContractOperationLog(Integer contractId, Integer logLevel);

    void recordredalarm(DbzRedAlarmEntity redAlarmEntity);

    void saveTraceLog(String bizCode, String bizModule, String logInfo);

    void saveTraceLog(TraceLogDTO traceLogDTO);
}
