package com.navigator.admin.facade.impl.rule;

import com.navigator.admin.facade.rule.RuleDictItemFacade;
import com.navigator.admin.pojo.entity.rule.RuleDictItemEntity;
import com.navigator.admin.service.rule.IRuleDictItemService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 13:54
 **/
@RestController
public class RuleDictItemFacadeImpl implements RuleDictItemFacade {
    @Autowired
    private IRuleDictItemService ruleDictItemService;

    @Override
    public Result queryRuleDictItemByCondition(QueryDTO<RuleDictItemEntity> queryDTO) {
        return ruleDictItemService.queryRuleDictItemByCondition(queryDTO);
    }

    @Override
    public List<RuleDictItemEntity> getRuleDictItemById(List<Integer> dictItemIdList) {
        return ruleDictItemService.getRuleDictItemById(dictItemIdList);
    }

    @Override
    public List<RuleDictItemEntity> getRuleItemByDictCode(String dictCode, String moduleType, String systemId) {
        return ruleDictItemService.getRuleItemByDictCode(dictCode, moduleType, systemId);
    }

    @Override
    public RuleDictItemEntity getRuleDictItemByCode(String dictCode, String itemCode, Integer itemValue, String moduleType, String systemId) {
        return ruleDictItemService.getRuleDictItemByCode(dictCode, itemCode, itemValue, moduleType, systemId);
    }

    @Override
    public Result saveRuleDictItem(RuleDictItemEntity ruleDictItemEntity) {
        return ruleDictItemService.saveRuleDictItem(ruleDictItemEntity);
    }

    @Override
    public Result updateRuleDictItem(RuleDictItemEntity ruleDictItemEntity) {
        return ruleDictItemService.updateRuleDictItem(ruleDictItemEntity);
    }
}
