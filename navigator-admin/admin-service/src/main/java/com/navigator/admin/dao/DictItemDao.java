package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.DictItemMapper;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:49
 **/
@Dao
public class DictItemDao extends BaseDaoImpl<DictItemMapper, DictItemEntity> {

    public IPage<DictItemEntity> queryByCondition(QueryDTO<DictItemEntity> queryDTO,
                                                  List<String> filterTemplateVipCode) {
        DictItemEntity dictItemQO = queryDTO.getCondition();
        if (StringUtils.isNotBlank(dictItemQO.getCustomerCode()) && CollectionUtils.isEmpty(filterTemplateVipCode)) {
            return this.page(new Page<>(0, 0));
        }
        LambdaQueryWrapper<DictItemEntity> queryWrapper = getDictItemQueryWrapper(filterTemplateVipCode, dictItemQO);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<DictItemEntity> queryDictItemList(DictItemEntity dictItemQO, List<String> filterTemplateVipCode) {
        if (StringUtils.isNotBlank(dictItemQO.getCustomerCode()) && CollectionUtils.isEmpty(filterTemplateVipCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DictItemEntity> queryWrapper = getDictItemQueryWrapper(filterTemplateVipCode, dictItemQO);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<DictItemEntity> getDictItemQueryWrapper(List<String> filterTemplateVipCode, DictItemEntity dictItemQO) {
        if(StringUtils.isNotBlank(dictItemQO.getUpdatedBy())){
            dictItemQO.setUpdatedBy(dictItemQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<DictItemEntity> queryWrapper = new LambdaQueryWrapper<DictItemEntity>()
                .eq(null != dictItemQO.getStatus(), DictItemEntity::getStatus, dictItemQO.getStatus())
                .eq(StringUtils.isNotBlank(dictItemQO.getDictCode()), DictItemEntity::getDictCode, dictItemQO.getDictCode())
                .in(StringUtils.isNotBlank(dictItemQO.getCustomerCode()), DictItemEntity::getItemCode, filterTemplateVipCode)
                .like(StringUtils.isNotBlank(dictItemQO.getUpdatedBy()), DictItemEntity::getUpdatedBy, dictItemQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(dictItemQO.getStartDay()) && StringUtils.isNotBlank(dictItemQO.getEndDay()), DictItemEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(dictItemQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(dictItemQO.getEndDay()))
                .eq(DictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(dictItemQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(dictItemQO.getSearchKey()), DictItemEntity::getItemName, dictItemQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(dictItemQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(dictItemQO.getSearchKey()), DictItemEntity::getItemCode, dictItemQO.getSearchKey().trim()));
        }
        queryWrapper.orderByDesc(DictItemEntity::getId);
        return queryWrapper;
    }

    public List<DictItemEntity> getDictItemById(List<Integer> dictItemIdList) {
        return list(Wrappers.<DictItemEntity>lambdaQuery()
                .in(DictItemEntity::getId, dictItemIdList)
                .eq(DictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public DictItemEntity getDictItemByCode(String dictCode, String itemCode, Integer itemValue) {
        List<DictItemEntity> dictItemEntityList = list(Wrappers.<DictItemEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(dictCode), DictItemEntity::getDictCode, dictCode)
                .eq(StringUtils.isNotBlank(itemCode), DictItemEntity::getItemCode, itemCode)
                .eq(null != itemValue, DictItemEntity::getDictCode, dictCode)
                .eq(DictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(dictItemEntityList) ? null : dictItemEntityList.get(0);
    }

    public List<DictItemEntity> getItemByDictCode(String dictCode) {
        return list(Wrappers.<DictItemEntity>lambdaQuery()
                .eq(DictItemEntity::getDictCode, dictCode)
                .eq(DictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(DictItemEntity::getItemSort)
        );
    }


    public List<DictItemEntity> getDictItemByCode(String dictCode, String itemName, String itemCode) {
        return list(Wrappers.<DictItemEntity>lambdaQuery()
                .eq(DictItemEntity::getDictCode, dictCode)
                .eq(DictItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(StringUtils.isNotBlank(itemName), DictItemEntity::getItemName, itemName)
                .or().eq(StringUtils.isNotBlank(itemCode), DictItemEntity::getItemCode, itemCode)
        );
    }
}
