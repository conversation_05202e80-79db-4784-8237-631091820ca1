package com.navigator.admin.service.columbus;

import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */
public interface ICEmployCustomerService {

    /**
     * cEmploy绑定customerId
     *
     * @param cEmployCustomerEntity
     * @return
     */
    boolean saveCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity);

    /**
     * 根据用户id和customerId查询数据
     *
     * @param cEmployId
     * @param customerId
     * @return
     */
    List<CEmployCustomerEntity> queryCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId);

    /**
     * @param cEmployId
     * @param customerId
     * @return
     */
    List<CEmployCustomerEntity> getCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId);

    /**
     * 根据用户id和customerId查询数据
     *
     * @param customerId
     * @param type
     * @return
     */
    List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndType(Integer customerId, Integer type, Integer status);


    List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndCEmployIdAndType(Integer customerId, Integer cEmployId, Integer type);

    void updateTypeByIds(List<Integer> ids);

    /**
     * 修改用户主体绑定数据
     *
     * @param cEmployCustomerEntity
     * @return
     */
    boolean updateCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity);

    /**
     * 根据cEmployId查询数据
     *
     * @param id
     * @return
     */
    CEmployCustomerEntity getCEmployCustomerById(Integer id);

    /**
     * 哥伦布用户数据绑定
     */
    void cEmployDataMigration();

    List<CEmployCustomerVO> queryCEmployCustomerVOByCEmployId(Integer cEmployId);

    /**
     * 获取权限
     *
     * @param customerId
     * @return
     */
    CLoginVO getCEmployCustomerPower(Integer customerId);

    Result verifyUserStatus(Integer customerId);
}