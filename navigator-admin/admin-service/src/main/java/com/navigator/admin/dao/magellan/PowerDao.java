package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.PowerMapper;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Dao
public class PowerDao extends BaseDaoImpl<PowerMapper, PowerEntity> {

    public List<PowerEntity> queryAllPower() {
        List<PowerEntity> list = list(Wrappers.<PowerEntity>lambdaQuery()
                .eq(PowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return list;

    }

    public List<PowerEntity> queryPowerByIdList(List<Integer> powerIdList) {
        if (CollectionUtils.isEmpty(powerIdList)) {
            powerIdList = Collections.singletonList(-1);
        }
        List<PowerEntity> list = list(Wrappers.<PowerEntity>lambdaQuery()
                .in(PowerEntity::getId,powerIdList)
                .eq(PowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return list;

    }

    /**
     * 根据条件：获取列表
     * @param condition
     *
     * @return
     */
    public List<PowerEntity> queryPowerList(PowerQO condition) {
        return this.list(PowerEntity.lqw(condition));
    }
}
