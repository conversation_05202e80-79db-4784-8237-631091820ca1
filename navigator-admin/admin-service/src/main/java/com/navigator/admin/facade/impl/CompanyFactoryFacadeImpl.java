package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.CompanyFactoryFacade;
import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;
import com.navigator.admin.service.CompanyFactoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CompanyFactoryFacadeImpl implements CompanyFactoryFacade {

    @Autowired
    private CompanyFactoryService companyFactoryService;

    @Override
    public List<FactoryCompanyEntity> queryCompanyFactoryList() {
        return companyFactoryService.queryCompanyFactoryList();
    }

    @Override
    public List<FactoryCompanyEntity> queryFactoryByCompanyId(Integer companyId) {
        return companyFactoryService.queryFactoryByCompanyId(companyId);

    }

    @Override
    public void saveFactoryCompany(FactoryCompanyDTO factoryCompanyDTO) {
         companyFactoryService.saveFactoryCompany(factoryCompanyDTO);
    }
}
