package com.navigator.admin.service.columbus.impl;

import com.navigator.admin.dao.columbus.CEmployBusinessDao;
import com.navigator.admin.pojo.entity.CEmployBusinessEntity;
import com.navigator.admin.service.columbus.ICEmployBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CEmployBusinessServiceImpl implements ICEmployBusinessService {
    @Autowired
    private CEmployBusinessDao cEmployBusinessDao;

    @Override
    public List<CEmployBusinessEntity> queryListByEmployId(String employId) {
        return cEmployBusinessDao.queryListByEmployId(employId);
    }

    @Override
    public void save(CEmployBusinessEntity cEmployBusinessEntity) {
        cEmployBusinessDao.save(cEmployBusinessEntity);
    }

    @Override
    public void deleteByEmployId(Integer employId) {
        cEmployBusinessDao.deleteByEmployId(employId);
    }
}
