package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.SiteMapper;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 09:58
 **/
@Dao
public class SiteDao extends BaseDaoImpl<SiteMapper, SiteEntity> {

    /**
     * 分页查询账套
     *
     * @param queryDTO
     * @return
     */
    public IPage<SiteEntity> querySiteByCondition(QueryDTO<SiteQO> queryDTO) {
        Integer pageNo = queryDTO.getPageNo();
        Integer pageSize = queryDTO.getPageSize();
        SiteQO siteQO = queryDTO.getCondition();
        return this.page(new Page<>(pageNo, pageSize), SiteEntity.lqw(siteQO));
    }

    public List<SiteEntity> querySiteList(Integer companyId, Integer category2, List<String> siteCodeList, String syncSystem, Integer status) {
        if (null != category2 && 0 != category2 && CollectionUtils.isEmpty(siteCodeList)) {
            return new ArrayList<>();
        }
        syncSystem = StringUtils.isNotBlank(syncSystem) ? syncSystem.trim() : syncSystem;
        return this.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(null != companyId && 0 != companyId, SiteEntity::getCompanyId, companyId)
                .in(null != category2 && 0 != category2, SiteEntity::getCode, siteCodeList)
                .eq(null != status && 0 != status, SiteEntity::getStatus, status)
                .eq(StringUtils.isNotBlank(syncSystem), SiteEntity::getSyncSystem, syncSystem)
                .orderByAsc(SiteEntity::getCompanyId, SiteEntity::getFactoryId, SiteEntity::getCategory1)
        );
    }

    public SiteEntity getSiteByCode(String siteCode) {
        List<SiteEntity> siteEntityList = this.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(siteCode), SiteEntity::getCode, siteCode));
        return CollectionUtils.isEmpty(siteEntityList) || StringUtils.isBlank(siteCode) ? null : siteEntityList.get(0);
    }

    public SiteEntity getSiteByName(String siteName) {
        List<SiteEntity> siteEntityList = this.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(siteName), SiteEntity::getName, siteName));
        return CollectionUtils.isEmpty(siteEntityList) || StringUtils.isBlank(siteName) ? null : siteEntityList.get(0);
    }

    public List<SiteEntity> getSiteByFactory(Integer companyId, String factoryCode) {
        return this.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(null != companyId && 0 != companyId, SiteEntity::getCompanyId, companyId)
                .eq(StringUtils.isNotBlank(factoryCode), SiteEntity::getFactoryCode, factoryCode)
        );
    }

    /**
     * 获取账套编码
     *
     * @param siteQO
     * @return
     */
    public SiteEntity getSiteEntity(SiteQO siteQO) {
        List<SiteEntity> siteEntityList = this.list(SiteEntity.lqw(siteQO));
        return CollectionUtils.isEmpty(siteEntityList) ? null : siteEntityList.get(0);
    }

    public Boolean judgeDuplicateSite(String siteName, String siteCode) {
        List<SiteEntity> siteEntityList = this.list(Wrappers.<SiteEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(siteCode), SiteEntity::getCode, siteCode)
                .or()
                .eq(StringUtils.isNotBlank(siteName), SiteEntity::getName, siteName)
        );
        return !CollectionUtils.isEmpty(siteEntityList);
    }

    /**
     * 根据条件：获取列表
     *
     * @param condition
     * @return
     */
    public List<SiteEntity> querySiteList(SiteQO condition) {
        return this.list(SiteEntity.lqw(condition));
    }

    public SiteEntity getSiteBySystemCode(String syncSystem, String bizCode) {
        LambdaQueryWrapper<SiteEntity> queryWrapper = Wrappers.<SiteEntity>lambdaQuery().eq(SiteEntity::getStatus, 1);

        // syncSystem = LKG , bizCode = lkg_code; syncSystem = ATLAS , bizCode = atlas_code
        if (syncSystem.equals(SyncSystemEnum.LINKINAGE.getValue())) {
            queryWrapper.eq(SiteEntity::getLkgCode, bizCode);
        } else if (syncSystem.equals(SyncSystemEnum.ATLAS.getValue())) {
            queryWrapper.eq(SiteEntity::getAtlasCode, bizCode);
        }
        List<SiteEntity> siteEntityList = this.list(queryWrapper);

        return CollectionUtils.isEmpty(siteEntityList) ? null : siteEntityList.get(0);
    }
}
