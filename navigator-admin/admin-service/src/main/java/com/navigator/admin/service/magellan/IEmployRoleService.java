package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface IEmployRoleService {

    /**
     * 根据 EmployId 获取 List<EmployRoleEntity>
     *
     * @param EmployId
     * @return
     */
    List<EmployRoleEntity> getEmployRolesByEmploy(String EmployId);


    List<EmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds);

    void addEmployRole(EmployRoleDTO employRoleDTO);

    void addEmployRoles(Integer employId, String roleIds);

    void addEmployRoles(Integer employId, Integer roleDefId, Integer categoryId, String customerIds);

    void deleteEmployRole(EmployRoleDTO employRoleDTO);

    List<EmployRoleEntity> queryByRoleDefId(Integer roleDefId);

    void save(EmployRoleEntity employRoleEntity);

    void deleteByEmployId(Integer employId);

    List<EmployRoleEntity> getEmployRoleListByRoleIds(List<Integer> roleIdList);

    List<EmployRoleEntity> queryAll();

    /**
     * 获取角色ID列表
     *
     * @param condition
     * @return
     */
    List<Integer> queryRoleIdList(EmployRoleQO condition);

    void updateDeletedByRoleIdAndEmployId(Integer roleId, Integer employId);

    void updateDeletedByIds(List<Integer> employIds);

    List<EmployRoleEntity> getAllEmployRoleList();
}
