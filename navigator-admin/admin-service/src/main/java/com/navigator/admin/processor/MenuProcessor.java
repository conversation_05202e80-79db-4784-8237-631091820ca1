package com.navigator.admin.processor;

import org.springframework.stereotype.Component;

/**
 * Description: 这里实现上去继承 Processor 完全可以
 *              但是 Processor implements ApplicationContextAware
 *              所以直接继承它必需继承其方法（setApplicationContext）虽无影响 但是不雅
 *              故这边直接继承抽象类（AbstractAdminProcessor）避免这个问题
 * Created by YuYong on 2021/11/6 17:13
 */
@Component
public class MenuProcessor extends AbstractAdminProcessor {


    @Override
    public void before(Object context) {
        System.out.println(((MenuContext) context).getBefore());
    }

    @Override
    public void after(Object context) {
        System.out.println(((MenuContext) context).getAfter());
    }
}
