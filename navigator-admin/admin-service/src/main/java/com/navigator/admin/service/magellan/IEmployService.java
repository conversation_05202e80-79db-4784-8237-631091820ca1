package com.navigator.admin.service.magellan;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.pojo.bo.PermissionBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.CategoryFactoryMenuVO;
import com.navigator.admin.pojo.vo.EmployDetailVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface IEmployService {


    /**
     * 根据条件查找 employ
     *
     * @param queryDTO
     * @return
     */
    IPage<EmployEntity> getEmployByCondition(QueryDTO<EmployDTO> queryDTO);

    /**
     * 根据 employIds 获取 Employs
     *
     * @param employIds
     * @return
     */
    List<EmployEntity> getEmployByEmployIds(List<Integer> employIds);

    /**
     * 根据 email 获取 Employ
     *
     * @param email
     * @return
     */
    List<EmployEntity> getEmployByEmail(String email,Integer system);

    /**
     * 根据 phone 获取 Employ
     *
     * @param phone
     * @return
     */
    List<EmployEntity> getEmployByPhone(String phone,Integer system);

    /**
     * 根据 nickName 获取 Employ
     *
     * @param nickName
     * @return
     */
    EmployEntity getEmployByNickName(String nickName,Integer system);

    /**
     * 根据 id 获取 Employ
     *
     * @param id
     * @return
     */
    EmployEntity getEmployById(Integer id);
    /**
     * 如果不存在employ新增 存在返回
     *
     * @param employEntity
     * @return
     */
    EmployEntity ifNotExistToSave(EmployEntity employEntity);

    List<EmployEntity> getEmployByRoleName(String roleName);

    /**
     * 根据CompnyId查询账号列表（区分企业个人账号）
     *
     * @param companyId
     * @return
     */
    List<EmployEntity> queryEmployByCompanyId(Integer companyId);

    /**
     * 修改密码
     *
     * @param employDTO
     * @return
     */
    Result modifyPassword(EmployEntity employEntity);


    /**
     * 重置客户密码
     *
     * @param id
     * @return
     */
    boolean updateEmployResetPassword(Integer id);


    /**
     * 根据客户id查询 员工信息
     *
     * @param customerId
     * @return
     */
    EmployEntity queryEmployByCustomerId(Integer customerId);


    Result modifyEmploy(EmployBusinessDTO employBusinessDTO);

    Result queryEmployList(QueryDTO<EmployDTO> queryDTO);

    EmployDetailVO queryEmployDetail(Integer employId);

    List<EmployVO> queryEmployListByRoleDefId(Integer roleDefId);

    String resetPassword(Integer employId);

    List<EmployVO> queryAvailableEmployByRoleDefId(Integer roleDefId);

    CategoryFactoryMenuVO queryCategoryFactoryByRole();

    Result updateEmployStatus(EmployDTO employDTO);

    Result saveEmployStatus(EmployEntity employEntity);

    Result resetUserPassword(ResetPasswordDTO resetPasswordDTO);

    Result sendResetPasswordCode(String mobileNo);

    Result sendAadCode(String mobileNo);

    Result verifyAadCode(LoginDTO loginDTO);

    void updateSignature(EmployEntity employEntity);

    /**
     * 根据用户id,品类 获取能查看的 belongCustomer权限
     *
     * @param
     * @return
     */
    PermissionBO queryPermissionByEmployId(String employId, Integer categoryId);
    /**
     * 根据用户id,品类 获取能查看的Site账套权限
     *
     * @param
     * @return
     */
    PermissionBO querySitePermission(String employId, Integer category2);

    Result importEmploy(MultipartFile file);


    /**
     * 根据虚角色id ,品类, 主体 获取员工id集合
     *
     * @param
     * @return
     */
    List<EmployEntity> getEmploy(String roleDefId, String categoryId, String factoryId);


    /**
     * 根据虚角色code ,品类, 主体 获取员工id集合
     *
     * @param
     * @return
     */
    List<EmployEntity> getEmployByRoleDefCode(String roleDefCode, String categoryId, String factoryId);

    List<EmployEntity> getEmployByEmailOrPhone(String email, String phone, Integer system);

    Result queryChoosedEmployByRoleId(QueryDTO<RoleDTO> queryDTO);

    Result saveOrUpdateEmployByFile(MultipartFile file);

    void createEmploy(EmployBusinessDTO employBusinessDTO);

    /**
     * 根据 email 获取 Employ
     *
     * @param email
     * @return
     */
    List<EmployEntity> getByEmail(String email);

    Result exportEmployRoleList();

    List<EmployEntity> getAllEmployList();

}
