package com.navigator.admin.dao.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.rule.BusinessRuleMapper;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-02 18:27
 **/
@Dao
public class BusinessRuleDao extends BaseDaoImpl<BusinessRuleMapper, BusinessRuleEntity> {
    public BusinessRuleEntity getRuleByBusinessCode(String referCode, String referType, String moduleType, String systemId) {
        List<BusinessRuleEntity> ruleEntityList = this.list(new LambdaQueryWrapper<BusinessRuleEntity>()
                .eq(BusinessRuleEntity::getReferCode, referCode)
                .eq(BusinessRuleEntity::getReferType, referType)
                .eq(BusinessRuleEntity::getModuleType, moduleType)
                .eq(BusinessRuleEntity::getSystemId, systemId)
                .eq(BusinessRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return CollectionUtils.isEmpty(ruleEntityList) ? new BusinessRuleEntity() : ruleEntityList.get(0);
    }

    public void dropBusinessRule(String referCode, String referType, String moduleType, String systemId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(BusinessRuleEntity::getReferCode, referCode)
                .eq(BusinessRuleEntity::getReferType, referType)
                .eq(BusinessRuleEntity::getModuleType, moduleType)
                .eq(BusinessRuleEntity::getSystemId, systemId)
                .eq(BusinessRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(BusinessRuleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(BusinessRuleEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    public BusinessRuleEntity getByCode(String ruleCode, String moduleType, String systemId) {
        List<BusinessRuleEntity> ruleEntityList = this.list(new LambdaQueryWrapper<BusinessRuleEntity>()
                .eq(BusinessRuleEntity::getRuleCode, ruleCode)
                .eq(BusinessRuleEntity::getModuleType, moduleType)
                .eq(BusinessRuleEntity::getSystemId, systemId));
        return CollectionUtils.isEmpty(ruleEntityList) ? new BusinessRuleEntity() : ruleEntityList.get(0);
    }
}
