package com.navigator.admin.service.approval;

import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
public interface LoaApprovalRuleService {

    /**
     * 根据数据查询规则类容
     *
     * @param loaApprovalRuleDTO
     * @return
     */
    LoaApprovalRuleEntity queryLoaApprovalRule(LoaApprovalRuleDTO loaApprovalRuleDTO);
}
