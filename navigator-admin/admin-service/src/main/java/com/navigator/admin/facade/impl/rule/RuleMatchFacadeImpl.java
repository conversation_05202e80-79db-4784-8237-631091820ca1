package com.navigator.admin.facade.impl.rule;

import com.navigator.admin.facade.rule.RuleMatchFacade;
import com.navigator.admin.pojo.dto.rule.RuleMatchDTO;
import com.navigator.admin.pojo.dto.rule.RuleReferInfoDTO;
import com.navigator.admin.service.rule.IRuleMatchService;
import com.navigator.admin.service.rule.IRuleVariableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 18:40
 **/
@RestController
public class RuleMatchFacadeImpl implements RuleMatchFacade {
    @Autowired
    private IRuleMatchService ruleMatchService;

    @Override
    public List<RuleReferInfoDTO> matchBusinessRule(RuleMatchDTO ruleMatchDTO) {
        return ruleMatchService.matchBusinessRule(ruleMatchDTO);
    }
}
