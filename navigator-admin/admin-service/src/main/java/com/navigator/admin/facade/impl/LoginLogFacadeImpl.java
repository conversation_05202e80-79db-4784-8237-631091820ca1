package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.LoginLogFacade;
import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.admin.service.ILoginLogService;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志Facade实现类
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@RestController
@Slf4j
public class LoginLogFacadeImpl implements LoginLogFacade {

    @Autowired
    private ILoginLogService loginLogService;

    @Override
    public Result saveLoginLog(LoginLogDTO loginLogDTO) {
        try {
            boolean success = loginLogService.saveLoginLog(loginLogDTO);
            return success
                    ? Result.success("登录日志保存成功")
                    : Result.failure("登录日志保存失败");
        } catch (Exception e) {
            log.error("保存登录日志时发生异常", e);
            return Result.failure("登录日志保存异常");
        }
    }
}
