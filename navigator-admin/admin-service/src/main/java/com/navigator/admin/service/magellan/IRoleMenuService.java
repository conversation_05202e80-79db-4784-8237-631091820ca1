package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.entity.RoleMenuEntity;
import com.navigator.admin.pojo.qo.RoleMenuQO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface IRoleMenuService {

    List<RoleMenuEntity> findRoleMenusByRoleId(String roleId);

    List<RoleMenuEntity> getMenuIdListByRoleIdList(List<Integer> roleIdList);

    void saveRoleMenu(RoleMenuEntity roleMenuEntity);

    void deleteRoleMenu(String roleId);

    void copyRoleMenu(Integer sourceRoleId, Integer targetRoleId);

    /**
     * 获取菜单ID列表
     *
     * @param condition
     * @return
     */
    Set<Integer> queryMenuIdList(RoleMenuQO condition);

    List<RoleMenuEntity> getAllRoleMenuList();

    void updateDeletedByMenuIdAndRoleId(Integer menuId, Integer roleId);
}
