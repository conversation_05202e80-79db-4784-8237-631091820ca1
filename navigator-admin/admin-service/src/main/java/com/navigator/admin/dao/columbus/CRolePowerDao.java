package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CRolePowerMapper;
import com.navigator.admin.pojo.entity.CRolePowerEntity;
import com.navigator.admin.pojo.qo.RolePowerQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Dao
public class CRolePowerDao extends BaseDaoImpl<CRolePowerMapper, CRolePowerEntity> {
    public void deleteRoleByRoleId(int roleId, Integer customerId) {
        remove(Wrappers.<CRolePowerEntity>lambdaQuery()
                .eq(CRolePowerEntity::getRoleId, roleId)
                .eq(CRolePowerEntity::getCustomerId, customerId)
        );
    }

    public List<CRolePowerEntity> queryByRoleId(String roleId, Integer customerId) {
        LambdaQueryWrapper<CRolePowerEntity> wrapper =
                Wrappers.<CRolePowerEntity>lambdaQuery()
                        .eq("1".equals(roleId), CRolePowerEntity::getCustomerId, -1)
                        .or("1".equals(roleId))
                        .eq(CRolePowerEntity::getCustomerId, customerId);
        wrapper.and(i -> i.eq(CRolePowerEntity::getRoleId, roleId));
        return list(wrapper);
    }

    public List<CRolePowerEntity> queryByRoleIdList(List<Integer> roleIdList, Integer customerId) {
        LambdaQueryWrapper<CRolePowerEntity> wrapper =
                Wrappers.<CRolePowerEntity>lambdaQuery()
                        .eq(roleIdList.contains(1), CRolePowerEntity::getCustomerId, -1)
                        .or(roleIdList.contains(1))
                        .eq(CRolePowerEntity::getCustomerId, customerId);
        wrapper.and(i -> i.in(CRolePowerEntity::getRoleId, roleIdList));
        return list(wrapper)
                ;

    }



    public void deleteRoleByRoleId(int roleId) {
        remove(Wrappers.<CRolePowerEntity>lambdaQuery()
                .eq(CRolePowerEntity::getRoleId, roleId)
        );
    }

    public List<CRolePowerEntity> queryByRoleId(String roleId) {
        LambdaQueryWrapper<CRolePowerEntity> wrapper =
                Wrappers.<CRolePowerEntity>lambdaQuery()
                        .eq(CRolePowerEntity::getRoleId, roleId)
                ;
        return list(wrapper);
    }

    public List<CRolePowerEntity> queryByRoleIdList(List<Integer> roleIdList) {
        LambdaQueryWrapper<CRolePowerEntity> wrapper =
                Wrappers.<CRolePowerEntity>lambdaQuery()
                        .in(CRolePowerEntity::getRoleId, roleIdList)
                ;
        return list(wrapper)
                ;

    }

    /**
     * 获取权限ID列表
     *
     * @param condition
     * @return
     */
    public Set<Integer> queryPowerIdList(RolePowerQO condition) {
        List<CRolePowerEntity> list = this.list(CRolePowerEntity.lqw(condition));
        Set<Integer> result = new HashSet<>();
        list.forEach(item -> result.add(item.getPowerId()));
        return result;
    }
}
