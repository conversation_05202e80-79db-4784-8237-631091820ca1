package com.navigator.admin.dao.approval;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.approval.CategoryApprovalModelMapper;
import com.navigator.admin.pojo.entity.approval.CategoryApprovalModelEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */

@Dao
public class CategoryApprovalModelDao extends BaseDaoImpl<CategoryApprovalModelMapper, CategoryApprovalModelEntity> {

    public List<CategoryApprovalModelEntity> queryCategoryApprovalModel(String category2) {

        return this.baseMapper.selectList(Wrappers.<CategoryApprovalModelEntity>lambdaQuery()
                .eq(CategoryApprovalModelEntity::getCategory2, category2)
                .eq(CategoryApprovalModelEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CategoryApprovalModelEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
