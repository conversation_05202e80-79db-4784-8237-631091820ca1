package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.UserFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.SignatureDTO;
import com.navigator.admin.service.IUserService;
import com.navigator.common.dto.Result;
import com.navigator.common.util.file.AzureBlobUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class UserFacadeImpl implements UserFacade {
    @Autowired
    private IUserService userService;
    @Autowired
    AzureBlobUtil azureBlobUtil;

    @Override
    public Result login(LoginDTO loginDTO) {
        return userService.login(loginDTO);
    }

    @Override
    public Result loginByPhone(LoginDTO loginDTO) {
        return userService.loginByPhone(loginDTO);
    }

    @Override
    public Result completeSignature(SignatureDTO signatureDTO) {
        return userService.completeSignature(signatureDTO);
    }

    @Override
    public Result test() {
        String sharedAccessSignature = azureBlobUtil.getSharedAccessSignature();
        System.out.println(sharedAccessSignature);
        return Result.success(sharedAccessSignature);
    }
}
