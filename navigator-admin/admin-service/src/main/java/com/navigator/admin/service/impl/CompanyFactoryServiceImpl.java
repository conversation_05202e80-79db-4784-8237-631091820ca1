package com.navigator.admin.service.impl;

import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.FactoryCompanyDao;
import com.navigator.admin.facade.CompanyFactoryFacade;
import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.admin.pojo.entity.FactoryCompanyEntity;
import com.navigator.admin.service.CompanyFactoryService;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CompanyFactoryServiceImpl implements CompanyFactoryService {

    @Autowired
    private FactoryCompanyDao factoryCompanyDao;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private RedisCacheMap redisCacheMap;


    @Override
    public List<FactoryCompanyEntity> queryCompanyFactoryList() {
        return factoryCompanyDao.queryCompanyFactoryList();
    }

    @Override
    public List<FactoryCompanyEntity> queryFactoryByCompanyId(Integer companyId) {
        return factoryCompanyDao.queryFactoryByCompanyId(companyId);
    }

    @Override
    public void saveFactoryCompany(FactoryCompanyDTO factoryCompanyDTO) {
        factoryCompanyDao.deleteByFactoryId(factoryCompanyDTO.getFactoryId());
        String currentUserId = JwtUtils.getCurrentUserId();
        for (Integer companyId : factoryCompanyDTO.getCompanyIdList()) {
            FactoryCompanyEntity factoryCompanyEntity = new FactoryCompanyEntity();
            factoryCompanyEntity.setCompanyId(companyId)
                    .setFactoryId(factoryCompanyDTO.getFactoryId())
                    .setCreatedBy(currentUserId)
                    .setUpdatedBy(currentUserId)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
            ;
            factoryCompanyDao.save(factoryCompanyEntity);
        }
        FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryInfoById(factoryCompanyDTO.getFactoryId());
        factoryEntity.setUpdatedAt(new Date());
        factoryEntity.setUpdatedBy(currentUserId);
        String updateName = redisCacheMap.get(Integer.parseInt(currentUserId));
        factoryEntity.setUpdatedByName(updateName);
        factoryWarehouseFacade.updateFactoryById(factoryEntity);
    }

}
