package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.AdminIdNameFacade;
import com.navigator.admin.service.IIdNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 管理域Id-Name服务接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AdminIdNameFacadeImpl implements AdminIdNameFacade {

    @Resource
    private IIdNameService idNameService;

    @Override
    public void refreshCache() {
        idNameService.refreshCache();
    }
}
