package com.navigator.admin.service.columbus;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.pojo.dto.RoleAuthDTO;
import com.navigator.admin.pojo.dto.RoleAuthMenuDTO;
import com.navigator.admin.pojo.dto.RoleAuthPowerDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CRoleDefEntity;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.LinkedHashSet;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ICRoleService {

    CRoleEntity getRoleById(Integer id);

    List<CRoleEntity> queryByIdList(List<Integer> roleIdList);

    /**
     * 根据角色名称获取角色实体
     *
     * @param roleName
     * @return
     */
    List<CRoleEntity> getRoleByRoleName(String roleName);

    void saveOrUpdate(CRoleDTO roleDTO, CRoleDefEntity cRoleDefEntity);

    List<CRoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList);

    List<CRoleEntity> getRoleListByDefId(Integer roleDefId);

    List<CRoleEntity> queryRole(CEmployRoleDTO employRoleDTO);

    List<CRoleEntity> queryRoleListByDefInfo(Integer roleDefId, Integer categoryId, Integer customerId);

    List<CRoleEntity> queryRoleListByDefInfos(List<Integer> roleDefIds, Integer categoryId, Integer customerId);

    List<CRoleEntity> queryRoleListByDefInfos2(List<Integer> roleDefIds, Integer categoryId, Integer factoryId);

    List<CRoleEntity> queryRoleListByDefInfosSalesType(List<Integer> roleDefIds, Integer categoryId, Integer salesType);

    List<CRoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId);

    List<CRoleEntity> getRoleAllList();

    List<CRoleEntity> queryRoleByEmployId(String employId, Integer customerId);

    Result queryRoleDefList(QueryDTO<CRoleQueryDTO> roleQueryDTO);

    Result queryPageByQueryDTO(Page<CRoleEntity> page, CRoleQueryDTO roleQueryDTO);

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    RoleAuthDTO getRoleAuth(RoleAuthQO roleAuthQO);

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    RoleAuthMenuDTO getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO);

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    RoleAuthPowerDTO getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO);

    /**
     * 根据用户ID：获取已授权的二级品类编码列表
     *
     * @param userId
     * @param customerId
     * @return
     */
    LinkedHashSet<Integer> queryCategory2List(Integer userId, Integer customerId);

    /**
     * 根据用户ID：判断是否管理员
     *
     * @param userId
     * @param customerId
     * @return
     */
    Boolean isAdmin(Integer userId, Integer customerId);
}
