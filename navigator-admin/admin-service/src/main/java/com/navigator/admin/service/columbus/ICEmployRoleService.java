package com.navigator.admin.service.columbus;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.entity.CEmployRoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.common.enums.IsDeletedEnum;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ICEmployRoleService {

    /**
     * 根据 EmployId 获取 List<CEmployRoleEntity>
     *
     * @param employId
     * @return
     */
    List<CEmployRoleEntity> getEmployRolesByEmploy(String employId);

    List<CEmployRoleEntity> getEmployRolesByEmployAndCustomerId(Integer employId, Integer customerId);

    List<CEmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds);

    void addEmployRole(CEmployRoleDTO employRoleDTO);

    List<CEmployRoleEntity> getEmployRolesByCustomerId(List<Integer> roleIds, Integer customerId);

    void addEmployRoles(Integer employId, String roleIds);

    void addEmployRoles(Integer employId, Integer roleDefId, Integer categoryId, String customerIds);

    void deleteEmployRole(CEmployRoleDTO employRoleDTO);

    List<CEmployRoleEntity> queryByRoleDefId(Integer roleDefId);

    void save(CEmployRoleEntity cEmployRoleEntity);

    void deleteByEmployId(Integer employId);

    void deleteByEmployIdAndCustomerId(Integer employId, Integer customerId);

    List<CEmployRoleEntity> getEmployRoleListByRoleIds(List<Integer> roleIdList);

    /**
     * 获取角色ID列表
     *
     * @param condition
     * @return
     */
    List<Integer> queryRoleIdList(EmployRoleQO condition);
}
