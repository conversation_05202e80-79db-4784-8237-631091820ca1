package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.admin.pojo.entity.AnnouncementEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

public interface AnnouncementService {
    void save(AnnouncementDTO announcementDTO);

    void modify(AnnouncementDTO announcementDTO);

    AnnouncementEntity queryAnnouncementDetail(AnnouncementDTO announcementDTO);

    Result queryAnnouncementList(QueryDTO<AnnouncementDTO> queryDTO);

    List<AnnouncementEntity> queryAnnouncementBySystem(Integer systemId);
}
