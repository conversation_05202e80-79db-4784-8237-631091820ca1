package com.navigator.admin.service.approval;

import com.navigator.admin.pojo.entity.approval.CategoryApprovalModelEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
public interface CategoryApprovalModelService {


    /**
     * 根据品类查询流程图编码
     *
     * @param category2
     * @return
     */
    CategoryApprovalModelEntity queryCategoryApprovalModel(String category2);

    /**
     * 根据品类查询流程图编码
     *
     * @param category2
     * @return
     */
    String queryCategoryApprovalModelKeyByCategory2(String category2);
}
