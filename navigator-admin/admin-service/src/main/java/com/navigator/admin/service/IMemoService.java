package com.navigator.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.entity.MemoEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface IMemoService extends IService<MemoEntity> {

    /**
     * 查看备忘录
     * @param queryDTO
     * @return
     */
    Result queryMemo(QueryDTO<MemoEntity> queryDTO);

    /**
     * 添加备忘录
     * @param memo
     * @return
     */
    Result saveMemo(MemoEntity memo);
}
