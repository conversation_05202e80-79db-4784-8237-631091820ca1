package com.navigator.admin.service.magellan.impl;

import com.navigator.admin.dao.magellan.PowerDao;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.qo.PowerQO;
import com.navigator.admin.service.magellan.IPowerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Service
public class PowerServiceImpl  implements IPowerService {

    @Autowired
    private PowerDao powerDao;
    @Override
    public List<PowerEntity> queryPower() {
        return powerDao.queryAllPower();
    }

    @Override
    public List<PowerEntity> queryPowerByIdList(List<Integer> powerIdList) {
        return powerDao.queryPowerByIdList(powerIdList);

    }

    @Override
    public List<PowerEntity> queryPowerList(PowerQO condition) {
        return powerDao.queryPowerList(condition);
    }
}
