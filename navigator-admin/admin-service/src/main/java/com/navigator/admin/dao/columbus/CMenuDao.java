package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.CMenuMapper;
import com.navigator.admin.pojo.entity.CMenuEntity;
import com.navigator.admin.pojo.entity.MenuEntity;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.Arrays;
import java.util.List;


@Dao
public class CMenuDao extends BaseDaoImpl<CMenuMapper, CMenuEntity> {

    public List<CMenuEntity> queryMenu(Integer system) {
        return this.baseMapper.selectList(
                Wrappers.<CMenuEntity>lambdaQuery()
                        .eq(CMenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                        .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public CMenuEntity getMenuById(String id) {
        return this.getOne(new LambdaQueryWrapper<CMenuEntity>()
                .eq(CMenuEntity::getId, id)
                .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CMenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    // 寻找menu中ParentId
    public Integer getMenuParentId(Integer menuId) {
        CMenuEntity cMenuEntity = this.getOne(new LambdaQueryWrapper<CMenuEntity>().eq(CMenuEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CMenuEntity::getId, menuId));
        return cMenuEntity.getParentId();
    }


    public List<CMenuEntity> getMenuByCategoryId(String categoryId, List<Integer> menuIdList) {
        LambdaQueryWrapper<CMenuEntity> wrapper = Wrappers.<CMenuEntity>lambdaQuery()
                .in(CMenuEntity::getCategoryId, Arrays.asList(0, categoryId));

        wrapper.and(i ->
                i
                        .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .in(CollectionUtils.isNotEmpty(menuIdList), CMenuEntity::getId, menuIdList));

        return list(wrapper);
    }

    public List<CMenuEntity> getMenuParentMenu() {
        return list(Wrappers.<CMenuEntity>lambdaQuery()
                .eq(CMenuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(CMenuEntity::getParentId, 0));


    }

    /**
     * 根据条件：获取列表
     * @param condition
     *
     * @return
     */
    public List<CMenuEntity> queryMenuList(MenuQO condition) {
        return this.list(CMenuEntity.lqw(condition));
    }
}
