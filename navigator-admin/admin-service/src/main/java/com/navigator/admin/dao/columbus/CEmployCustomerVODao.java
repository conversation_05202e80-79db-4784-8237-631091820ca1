package com.navigator.admin.dao.columbus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.VCEmployCustomerMapper;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.entity.CEmployCustomerVOEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */
@Dao
public class CEmployCustomerVODao extends BaseDaoImpl<VCEmployCustomerMapper, CEmployCustomerVOEntity> {


    public IPage<CEmployCustomerVOEntity> queryPageByQueryDTO(Page<CEmployCustomerVOEntity> page, CEmployDTO employDTO, Integer beginEmployId) {
        return page(page, Wrappers.<CEmployCustomerVOEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(employDTO.getName()), CEmployCustomerVOEntity::getEmployName, employDTO.getName() == null ? employDTO.getName() : employDTO.getName().trim())
                .like(StringUtils.isNotBlank(employDTO.getEnterpriseName()), CEmployCustomerVOEntity::getEnterpriseName, employDTO.getEnterpriseName() == null ? employDTO.getEnterpriseName() : employDTO.getEnterpriseName().trim())
                .like(StringUtils.isNotBlank(employDTO.getCustomerName()), CEmployCustomerVOEntity::getCustomerName, employDTO.getCustomerName() == null ? employDTO.getCustomerName() : employDTO.getCustomerName().trim())
                .ge(StringUtils.isNotBlank(employDTO.getUpdateStartTime()), CEmployCustomerVOEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(employDTO.getUpdateStartTime()))
                .le(StringUtils.isNotBlank(employDTO.getUpdateEndTime()), CEmployCustomerVOEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp2359(employDTO.getUpdateEndTime()))
                .like(StringUtils.isNotBlank(employDTO.getPhone()), CEmployCustomerVOEntity::getPhone, employDTO.getPhone())
                .like(StringUtils.isNotBlank(employDTO.getEmail()), CEmployCustomerVOEntity::getEmail, employDTO.getEmail())
                .eq(employDTO.getCustomerId() != null, CEmployCustomerVOEntity::getCustomerId, employDTO.getCustomerId())
                .eq(CEmployCustomerVOEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CEmployCustomerVOEntity::getId)
                .ge(CEmployCustomerVOEntity::getCEmployId, beginEmployId)
                .ge(StringUtils.isNotBlank(employDTO.getSignatureStartTime()), CEmployCustomerVOEntity::getSignatureTime, DateTimeUtil.parseTimeStamp0000(employDTO.getSignatureStartTime()))
                .le(StringUtils.isNotBlank(employDTO.getSignatureEndTime()), CEmployCustomerVOEntity::getSignatureTime, DateTimeUtil.parseTimeStamp2359(employDTO.getSignatureEndTime()))
        );

    }
}
