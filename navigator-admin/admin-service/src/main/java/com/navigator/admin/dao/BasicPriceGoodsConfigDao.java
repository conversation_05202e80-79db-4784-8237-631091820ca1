package com.navigator.admin.dao;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.BasicPriceGoodsConfigMapper;
import com.navigator.admin.pojo.entity.BasicPriceGoodsConfigEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.activiti.bpmn.model.parse.Warning;


import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17
 */
@Dao
public class BasicPriceGoodsConfigDao extends BaseDaoImpl<BasicPriceGoodsConfigMapper, BasicPriceGoodsConfigEntity> {

    public List<BasicPriceGoodsConfigEntity> queryBasicPriceGoodsConfigByCategoryId(Integer categoryId) {
        return this.baseMapper.selectList(Wrappers.<BasicPriceGoodsConfigEntity>lambdaQuery()
                .eq(BasicPriceGoodsConfigEntity::getCategoryId, categoryId)
        );
    }

}
