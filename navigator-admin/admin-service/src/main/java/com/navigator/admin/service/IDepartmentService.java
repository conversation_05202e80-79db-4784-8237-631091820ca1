package com.navigator.admin.service;

import com.navigator.admin.pojo.entity.DepartmentEntity;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface IDepartmentService {


    /**
     * 获取所有的部门（树状结构）
     *
     * @return
     */
    List<DepartmentEntity> getDepartmentList(Integer system);


    /**
     * 根据部门Id获取部门
     *
     * @param departmentId
     * @return
     */
    DepartmentEntity getDepartmentEntityById(String departmentId);

    /**
     * 根据公司id获取部门(树状结构)
     *
     * @param companyId
     * @return
     */
    List<DepartmentEntity> getDepartmentEntityByCompanyId(String companyId);


    List<DepartmentEntity> getDepartmentLeaderId(List<Integer> departmentId);

}
