package com.navigator.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.dao.OperationDetailDao;
import com.navigator.admin.mapper.OperationDetailMapper;
import com.navigator.admin.pojo.dto.LogDTO;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.OperationDetailEntity;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.vo.LogVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.IOperationLogService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 操作日志详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class OperationDetailServiceImpl extends ServiceImpl<OperationDetailMapper, OperationDetailEntity> implements IOperationDetailService {

    @Resource
    IOperationLogService operationLogService;
    @Resource
    MessageFacade messageFacade;
    @Resource
    IEmployService employService;
    @Resource
    ICEmployService cEmployService;
    @Resource
    CustomerFacade customerFacade;
    @Autowired
    private OperationDetailDao operationDetailDao;

    @Override
    public void saveOperationDetail(OperationDetailDTO operationDetailDTO) {
        OperationDetailEntity operationDetailEntity = new OperationDetailEntity();
        BeanConvertUtils.copy(operationDetailEntity, operationDetailDTO);
        baseMapper.insert(operationDetailEntity);
        operationDetailDTO.setId(operationDetailEntity.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOperationLog(OperationDetailDTO operationDetailDTO) {
        operationDetailDTO.setCreatedAt(DateUtil.date().toTimestamp());
        // 操作人不为空 查询用户
        // 操作人不为空 查询用户
        if (StrUtil.isNotBlank(operationDetailDTO.getTriggerSys()) && SystemEnum.COLUMBUS.getDescription().equals(operationDetailDTO.getTriggerSys())) {
            if (null != operationDetailDTO.getOperatorId()) {
                CEmployEntity cEmployEntity = cEmployService.getEmployById(operationDetailDTO.getOperatorId());
                operationDetailDTO.setOperatorName(cEmployEntity.getName());

            }
        } else {
            if (null != operationDetailDTO.getOperatorId()) {
                EmployEntity employEntity = employService.getEmployById(operationDetailDTO.getOperatorId());
                operationDetailDTO.setOperatorName(employEntity.getName());
            }
        }
        // 记录操作详细日志
        this.saveOperationDetail(operationDetailDTO);

        if (operationDetailDTO.getLogTrigger()) {
            noticeOperationLog(operationDetailDTO);
        }

        if (operationDetailDTO.getMsgTrigger()) {
            //noticeMessageService(operationDetailDTO);
        }
    }

    /**
     * 通知日志中心
     *
     * @param operationDetailId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void noticeOperationDetail(Integer operationDetailId) {
        OperationDetailEntity operationDetailEntity = this.baseMapper.selectById(operationDetailId);
        OperationDetailDTO operationDetailDTO = BeanConvertUtils.convert(OperationDetailDTO.class, operationDetailEntity);
        noticeOperationLog(operationDetailDTO);
    }

    @Override
    public void recordMagellanOperationDetail(RecordOperationDetail recordOperationDetail) {
        try {
            OperationDetailDTO operationDetailDTO = new OperationDetailDTO();
            String userId = JwtUtils.getCurrentUserId();
            String category2Name = StringUtils.isBlank(recordOperationDetail.getCategory2Name()) ? "" : recordOperationDetail.getCategory2Name();
            String operationName = category2Name + (StrUtil.isBlank(recordOperationDetail.getOperationName()) ? recordOperationDetail.getOperationActionEnum().getAction() : recordOperationDetail.getOperationName());
            if (StringUtils.isNotBlank(recordOperationDetail.getReferName())) {
                operationName = operationName + "(" + recordOperationDetail.getReferName() + ")";
            }
            operationDetailDTO
                    .setCreatedAt(DateUtil.date().toTimestamp())
                    .setOperatorId(Integer.parseInt(userId))
                    .setMetaData(recordOperationDetail.getBeforeData())
                    .setData(recordOperationDetail.getDtoData())
                    .setAfterData(recordOperationDetail.getAfterData())
                    .setTriggerSys(SystemEnum.MAGELLAN.getName())
                    .setOperationName(operationName)
                    .setBizCode(recordOperationDetail.getOperationActionEnum().getCode())
                    .setBizModule(ModuleTypeEnum.MLOG.getModule())
                    .setSource(1)
                    .setOperatorType(1)
                    .setScenes(recordOperationDetail.getOperationActionEnum().getScenes())
                    .setReferBizId(recordOperationDetail.getReferBizId())
                    .setReferBizCode(recordOperationDetail.getReferBizCode())
            ;
            EmployEntity employEntity = employService.getEmployById(operationDetailDTO.getOperatorId());
            operationDetailDTO.setOperatorName(employEntity.getName());

            this.saveOperationDetail(operationDetailDTO);

            noticeOperationLog(operationDetailDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void recordColumbusOperationDetail(RecordOperationDetail recordOperationDetail) {
        try {
            OperationDetailDTO operationDetailDTO = new OperationDetailDTO();
            String userId = JwtUtils.getCurrentUserId();
            operationDetailDTO
                    .setCreatedAt(DateUtil.date().toTimestamp())
                    .setOperatorId(Integer.parseInt(userId))
                    .setMetaData(recordOperationDetail.getBeforeData())
                    .setData(recordOperationDetail.getDtoData())
                    .setAfterData(recordOperationDetail.getAfterData())
                    .setTriggerSys(SystemEnum.COLUMBUS.getName())
                    .setOperationName(recordOperationDetail.getOperationActionEnum().getAction())
                    .setBizCode(recordOperationDetail.getOperationActionEnum().getCode())
                    .setBizModule(ModuleTypeEnum.CLOG.getModule())
                    .setSource(1)
                    .setOperatorType(1)
            ;
            CEmployEntity cEmployEntity = cEmployService.getEmployById(operationDetailDTO.getOperatorId());
            operationDetailDTO.setOperatorName(cEmployEntity.getName());

            this.saveOperationDetail(operationDetailDTO);

            noticeOperationLog(operationDetailDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Result queryLogList(QueryDTO<LogDTO> queryDTO) {
        Page<OperationDetailEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        LogDTO logDTO = queryDTO.getCondition();
        if (StringUtils.isBlank(logDTO.getOperatorStartTime()) && StringUtils.isBlank(logDTO.getOperatorEndTime())) {
            Date date = new Date();
            String endTime = DateTimeUtil.formatDateTimeString(date);
            Date startDate = DateTimeUtil.addYears(new Date(), -1, false);
            String startTime = DateTimeUtil.formatDateTimeString(startDate);
            logDTO.setOperatorStartTime(startTime);
            logDTO.setOperatorEndTime(endTime);
        }
        IPage<OperationDetailEntity> iPage = operationDetailDao.queryLogList(page, logDTO);
        List<LogVO> logVOList = iPage.getRecords().stream().map(
                i -> {
                    LogVO logVO = new LogVO();
                    logVO.setOperationName(i.getOperationName())
                            .setOperatorName(i.getOperatorName())
                            .setScenes(i.getScenes())
                            .setCreatedAt(DateTimeUtil.formatDate(i.getCreatedAt()))
                    ;
                    return logVO;
                }

        ).collect(Collectors.toList());
        return Result.page(iPage, logVOList);
    }

    /**
     * 通知日志中心
     *
     * @param operationDetailDTO
     * @return
     */
    public void noticeOperationLog(OperationDetailDTO operationDetailDTO) {

        try {
            /*Map<String, Object> map = operationDetailDTO.getMap();
            map.putAll(BeanUtil.beanToMap(operationDetailDTO));
            // 接口参数
            if (StrUtil.isNotBlank(operationDetailDTO.getData())) {
                map.put("data", FastJsonUtils.getJsonToMap(operationDetailDTO.getData()));
            }
            //原数据
            if (StrUtil.isNotBlank(operationDetailDTO.getMetaData())) {
                map.put("metaData", FastJsonUtils.getJsonToMap(operationDetailDTO.getMetaData()));
            }
            //关联操作的数据Json
            if (StrUtil.isNotBlank(operationDetailDTO.getReferOperationData())) {
                map.put("referOperationData", FastJsonUtils.getJsonToMap(operationDetailDTO.getReferOperationData()));
            }
            map.remove("map");
            operationDetailDTO.setMap(map);*/

            // 通知日志中心
            operationLogService.saveOperationLog(operationDetailDTO);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 通知消息中心
     *
     * @param operationDetailDTO
     */
    public void noticeMessageService(OperationDetailDTO operationDetailDTO) {
        // 通知消息中心
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(operationDetailDTO.getBizCode());
        messageInfoDTO.setDataMap(operationDetailDTO.getMap());
        messageInfoDTO.setReferBizId(operationDetailDTO.getReferBizId().toString());
        messageFacade.sendMessage(messageInfoDTO);
    }


    @Override
    public List<OperationDetailEntity> queryOperationDetailByReferBizCode(String referBizCode, Integer logLevel) {
        return this.baseMapper.selectList(
                Wrappers.<OperationDetailEntity>lambdaQuery()
                        .eq(OperationDetailEntity::getReferBizCode, referBizCode)
                        .in(null != logLevel, OperationDetailEntity::getLogLevel, Arrays.asList(logLevel, OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue()))
        );
    }


}