package com.navigator.admin.facade.impl.columbus;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.columbus.CMenuFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.columbus.ICMenuService;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/3 13:46
 */
@RestController
public class CMenuFacadeImpl implements CMenuFacade {

    @Autowired
    private ICMenuService cMenuService;

    @Autowired
    private ICEmployService cEmployService;

    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;

    @Resource
    private EmployFacade employFacade;

    /**
     * v1.0 菜单按钮权限独立设置
     */

    @Override
    public Result getMenusByEmploy(CMenuDTO menuDTO) {
        CRoleDTO cRoleDTO = new CRoleDTO();
        cRoleDTO.setEmployId(menuDTO.getEmployId());
        CMenuVO levelMenus = cMenuService.getMenuByEmployId(cRoleDTO);
        return Result.success(levelMenus);
    }

    @Override
    public Result getMenusByCondition(CMenuDTO menuDTO) {
        CEmployEntity employEntity = cEmployService.getEmployById(Integer.parseInt(menuDTO.getEmployId()));
        CMenuVO levelMenus = cMenuService.getMenusByCondition(menuDTO,employEntity.getCustomerId());
        return Result.success(levelMenus);
    }

    @Override
    public Result getMenuByRoleId(CRoleDTO cRoleDTO) {
        CMenuVO menuVO = cMenuService.getMenuByRoleId(cRoleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result getMenuByEmployId(CRoleDTO cRoleDTO) {
        CMenuVO menuVO = cMenuService.getMenuByEmployId(cRoleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result saveRoleMenu(CMenuDTO cMenuDTO) {
        if (CollectionUtils.isEmpty(cMenuDTO.getMenuIdList())) {
            throw new BusinessException(ResultCodeEnum.MENU_EMPUTY);
        }

        if (StringUtils.isBlank(cMenuDTO.getRoleId())) {
            throw new BusinessException(ResultCodeEnum.ROLE_EMPUTY);
        }
        cMenuService.saveRoleMenu(cMenuDTO);
        return Result.success();
    }

    /**
     * v2.0 统一菜单按钮权限
     */

    @Override
    public Result getMenusByEmployV2(CMenuDTO menuDTO) {
        CRoleDTO cRoleDTO = new CRoleDTO();
        cRoleDTO.setEmployId(menuDTO.getEmployId());
        CMenuVO levelMenus = cMenuService.getMenuByEmployIdV2(cRoleDTO);
        return Result.success(levelMenus);
    }

    @Override
    public Result getMenusByConditionV2(CMenuDTO menuDTO) {
        CMenuVO levelMenus = cMenuService.getMenusByConditionV2(menuDTO);
        return Result.success(levelMenus);
    }

    @Override
    public Result getMenuByRoleIdV2(CRoleDTO cRoleDTO) {
        CMenuVO menuVO = cMenuService.getMenuByRoleIdV2(cRoleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result getMenuByEmployIdV2(CRoleDTO cRoleDTO) {
        CMenuVO menuVO = cMenuService.getMenuByEmployIdV2(cRoleDTO);
        return Result.success(menuVO);
    }

    @Override
    public Result saveRoleMenuV2(CMenuDTO cMenuDTO) {
        BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity = new BusinessDetailUpdateRecordEntity();
        //记录修改主数据人
        businessDetailUpdateRecordEntity
                .setDetailCode(BusinessDetailCodeEnum.C_EMPLOY_ROLE_EDIT.getValue())
                .setBusinessId(Integer.valueOf(cMenuDTO.getRoleId()))
                .setData(JSON.toJSONString(cMenuDTO))
                .setCreatedAt(new Date())
                .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        businessDetailUpdateRecordService.saveBusinessDetailUpdateRecord(businessDetailUpdateRecordEntity);

        if (CollectionUtils.isEmpty(cMenuDTO.getMenuIdList())) {
            throw new BusinessException(ResultCodeEnum.MENU_EMPUTY);
        }

        if (StringUtils.isBlank(cMenuDTO.getRoleId())) {
            throw new BusinessException(ResultCodeEnum.ROLE_EMPUTY);
        }
        cMenuService.saveRoleMenuV2(cMenuDTO);
        return Result.success();
    }

    @Override
    public Result addRoleMenu(CMenuDTO menuDTO) {
        cMenuService.addRoleMenu(menuDTO);
        return Result.success();
    }

}
