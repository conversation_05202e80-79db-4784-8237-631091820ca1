package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.mapper.TodoListMapper;
import com.navigator.admin.pojo.dto.QueryTodoDTO;
import com.navigator.admin.pojo.entity.TodoListEntity;
import com.navigator.admin.pojo.vo.TodoVO;
import com.navigator.admin.service.ITodoListService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 待办业务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
public class TodoListServiceImpl extends ServiceImpl<TodoListMapper, TodoListEntity> implements ITodoListService {
    @Override
    public Result queryTodoList(QueryDTO<QueryTodoDTO> queryDTO) {
        String userId = queryDTO.getCondition().getUserId();
        Page<TodoListEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        IPage<TodoListEntity> iPage = page(page, Wrappers.<TodoListEntity>lambdaQuery().eq(TodoListEntity::getUserId, userId));
        List<TodoVO> todoVOList = new ArrayList<>();
        for (TodoListEntity record : iPage.getRecords()) {
            TodoVO todoVO = new TodoVO();
            todoVO.setBusinessId(record.getBusinessId());
            todoVO.setBusinessName(record.getBusinessName());
            todoVO.setBusinessStatus(record.getBusinessStatus());
            todoVOList.add(todoVO);
        }
        return Result.page(iPage,todoVOList);
    }
}
