package com.navigator.admin.dao.magellan;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.mapper.EmployMapper;
import com.navigator.admin.pojo.dto.EmployDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/22 16:37
 */
@Dao
public class EmployDao extends BaseDaoImpl<EmployMapper, EmployEntity> {

    public List<EmployEntity> queryEmployByCompanyId(Integer companyId) {
        return this.baseMapper.selectList(
                Wrappers.<EmployEntity>lambdaQuery()
                        .eq(EmployEntity::getCompanyId, companyId)
                        .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByAsc(EmployEntity::getType)
        );
    }


    public EmployEntity queryEmployByCustomerId(Integer customerId) {
        List<EmployEntity> employEntityList = this.baseMapper.selectList(
                Wrappers.<EmployEntity>lambdaQuery()
                        .eq(EmployEntity::getCustomerId, customerId)
                        .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return !CollectionUtils.isEmpty(employEntityList) ? employEntityList.get(0) : null;
    }

    public EmployEntity queryEmployById(Integer employId) {
        List<EmployEntity> list = list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getId, employId)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }

    public List<EmployEntity> queryEmployByIdList(List<Integer> employIdList) {
        return list(Wrappers.<EmployEntity>lambdaQuery()
                .in(EmployEntity::getId, employIdList)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<EmployEntity> queryPageByQueryDTO(Page<EmployEntity> page, EmployDTO employDTO, Integer beginEmployId) {
        IPage<EmployEntity> iPage = page(page, Wrappers.<EmployEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(employDTO.getId()), EmployEntity::getId, employDTO.getId())
                .like(StringUtils.isNotBlank(employDTO.getPhone()), EmployEntity::getPhone, employDTO.getPhone() == null ? null : employDTO.getPhone().trim())
                .like(StringUtils.isNotBlank(employDTO.getEmail()), EmployEntity::getEmail, employDTO.getEmail() == null ? null : employDTO.getEmail().trim())
                .like(StringUtils.isNotBlank(employDTO.getName()), EmployEntity::getName,  employDTO.getName() == null ? null : employDTO.getName().trim())
                .like(StrUtil.isNotBlank(employDTO.getUpdatedByName()), EmployEntity::getUpdatedByName, employDTO.getUpdatedByName())
                .eq(EmployEntity::getSystem, SystemEnum.MAGELLAN.getValue())
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(EmployEntity::getId)
                .ge(EmployEntity::getId, beginEmployId)
        );

        return iPage;
    }

    public void updateEmployStatus(EmployDTO employDTO, Integer userId, String name) {
        update(Wrappers.<EmployEntity>lambdaUpdate()
                .set(StringUtils.isNotBlank(employDTO.getStatus()), EmployEntity::getStatus, employDTO.getStatus())
                .set(StringUtils.isNotBlank(name), EmployEntity::getUpdatedByName, name)
                .set(EmployEntity::getUpdatedBy, userId)
                .set(EmployEntity::getUpdatedAt, new Date())
                .eq(EmployEntity::getId, employDTO.getId())
        );
    }

    public List<EmployEntity> getEmployByPhone(String phone, Integer system) {
        LambdaQueryWrapper<EmployEntity> wrapper = Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getPhone, phone)
                .eq(EmployEntity::getSystem, system)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        wrapper.or(i -> i
                .eq(EmployEntity::getSystem, SystemEnum.MAGELLAN.getValue())
                .eq(EmployEntity::getPhone, phone)
                .le(EmployEntity::getId, 101)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return list(wrapper);

    }

    public EmployEntity queryEmployNickName(String nickName, Integer system) {
        List<EmployEntity> list = list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getNickName, nickName)
                .eq(EmployEntity::getSystem, system)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<EmployEntity> queryEmployByEmail(String email, Integer system) {
        return list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getEmail, email)
                .eq(EmployEntity::getSystem, system)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<EmployEntity> getEmployByEmailOrPhone(String email, String phone, Integer system) {
        return list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(email), EmployEntity::getEmail, email)
                .or(StringUtils.isNotBlank(phone))
                .eq(StringUtils.isNotBlank(phone), EmployEntity::getPhone, phone)
                .eq(EmployEntity::getSystem, system)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<EmployEntity> queryPageByIdList(Page<EmployEntity> page, List<Integer> employIdList) {
        IPage<EmployEntity> iPage = page(page, Wrappers.<EmployEntity>lambdaQuery()
                .in(EmployEntity::getId, employIdList)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(EmployEntity::getUpdatedAt)
        );
        return iPage;
    }

    public List<EmployEntity> getByEmail(String email) {
        return list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getEmail, email)
        );
    }

    public List<EmployEntity> queryAllEmploy() {
        return list(Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<EmployEntity> queryEmployByMicroId(String microId, String LkgId, Integer system) {
        LambdaQueryWrapper<EmployEntity> queryWrapper = Wrappers.<EmployEntity>lambdaQuery()
                .eq(EmployEntity::getWorkNo, microId)
                .or()
                .eq(EmployEntity::getNickName, LkgId);
        queryWrapper.and(i -> i.eq(EmployEntity::getSystem, system)
                .eq(EmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return list(queryWrapper);
    }
}
