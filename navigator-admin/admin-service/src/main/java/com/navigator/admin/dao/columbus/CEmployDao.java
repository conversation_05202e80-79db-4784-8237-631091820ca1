package com.navigator.admin.dao.columbus;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.CEmployMapper;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/22 16:37
 */
@Dao
public class CEmployDao extends BaseDaoImpl<CEmployMapper, CEmployEntity> {

    public List<CEmployEntity> queryEmployByCompanyId(Integer companyId) {
        return this.baseMapper.selectList(
                Wrappers.<CEmployEntity>lambdaQuery()
                        .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }


    public CEmployEntity queryEmployByCustomerId(Integer customerId) {
        List<CEmployEntity> employEntityList = this.baseMapper.selectList(
                Wrappers.<CEmployEntity>lambdaQuery()
                        .eq(CEmployEntity::getCustomerId, customerId)
                        .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return !CollectionUtils.isEmpty(employEntityList) ? employEntityList.get(0) : null;
    }

    public CEmployEntity queryEmployById(Integer employId) {
        List<CEmployEntity> list = list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getId, employId)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(list) ? null : list.get(0);

    }

    public List<CEmployEntity> queryEmployByIdList(List<Integer> employIdList) {
        return list(Wrappers.<CEmployEntity>lambdaQuery()
                .in(CEmployEntity::getId, employIdList)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<CEmployEntity> queryPageByQueryDTO(Page<CEmployEntity> page, CEmployDTO employDTO, Integer beginEmployId) {
        return page(page, Wrappers.<CEmployEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(employDTO.getName()), CEmployEntity::getName, employDTO.getName() == null ? employDTO.getName() : employDTO.getName().trim())
                .like(StringUtils.isNotBlank(employDTO.getEnterpriseName()), CEmployEntity::getEnterpriseName, employDTO.getEnterpriseName() == null ? employDTO.getEnterpriseName() : employDTO.getEnterpriseName().trim())
                .like(StringUtils.isNotBlank(employDTO.getCustomerName()), CEmployEntity::getCustomerName, employDTO.getCustomerName() == null ? employDTO.getCustomerName() : employDTO.getCustomerName().trim())
                .ge(StringUtils.isNotBlank(employDTO.getUpdateStartTime()), CEmployEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(employDTO.getUpdateStartTime()))
                .le(StringUtils.isNotBlank(employDTO.getUpdateEndTime()), CEmployEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp2359(employDTO.getUpdateEndTime()))
                .like(StringUtils.isNotBlank(employDTO.getPhone()), CEmployEntity::getPhone, employDTO.getPhone())
                .like(StringUtils.isNotBlank(employDTO.getEmail()), CEmployEntity::getEmail, employDTO.getEmail())
                .eq(employDTO.getCustomerId() != null, CEmployEntity::getCustomerId, employDTO.getCustomerId())
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CEmployEntity::getId)
                .ge(CEmployEntity::getId, beginEmployId)
                .ge(StringUtils.isNotBlank(employDTO.getSignatureStartTime()), CEmployEntity::getSignatureTime, DateTimeUtil.parseTimeStamp0000(employDTO.getSignatureStartTime()))
                .le(StringUtils.isNotBlank(employDTO.getSignatureEndTime()), CEmployEntity::getSignatureTime, DateTimeUtil.parseTimeStamp2359(employDTO.getSignatureEndTime()))
        );

    }

    public void updateEmployStatus(CEmployDTO employDTO) {
        update(Wrappers.<CEmployEntity>lambdaUpdate()
                .set(StringUtils.isNotBlank(employDTO.getStatus()), CEmployEntity::getStatus, employDTO.getStatus())
                .eq(CEmployEntity::getId, employDTO.getId())
        );
    }

    public List<CEmployEntity> getEmployByPhone(String phone, Integer type) {
        LambdaQueryWrapper<CEmployEntity> wrapper = Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getPhone, phone)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(null != type, CEmployEntity::getType, type);

        wrapper.or(i -> i
                .eq(CEmployEntity::getPhone, phone)
                .le(CEmployEntity::getId, 101)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return list(wrapper);

    }

    public List<CEmployEntity> queryEmployByPhone(String phone) {
        LambdaQueryWrapper<CEmployEntity> wrapper = Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getPhone, phone)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return list(wrapper);

    }

    public CEmployEntity queryEmployNickName(String nickName) {
        List<CEmployEntity> list = list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getNickName, nickName)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<CEmployEntity> queryEmployByEmail(String email) {
        return list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getEmail, email)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<CEmployEntity> getEmployByEmailOrPhone(String email, String phone) {
        return list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(email), CEmployEntity::getEmail, email)
                .or(StringUtils.isNotBlank(phone))
                .eq(StringUtils.isNotBlank(phone), CEmployEntity::getPhone, phone)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public IPage<CEmployEntity> queryPageByIdList(Page<CEmployEntity> page, List<Integer> employIdList) {
        IPage<CEmployEntity> iPage = page(page, Wrappers.<CEmployEntity>lambdaQuery()
                .in(CEmployEntity::getId, employIdList)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CEmployEntity::getUpdatedAt)
        );
        return iPage;
    }

    public List<CEmployEntity> getEmployByCustomerId(Integer customerId, int type) {
        return list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getCustomerId, customerId)
                .eq(CEmployEntity::getType, type)
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<CEmployEntity> queryAllEmploy() {
        return list(Wrappers.<CEmployEntity>lambdaQuery()
                .eq(CEmployEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void updateTypeByIds(List<Integer> ids) {
        update(Wrappers.<CEmployEntity>lambdaUpdate()
                .set(CEmployEntity::getType, CEmployTypeEnum.OTHER.getType())
                .in(CEmployEntity::getId, ids)
        );
    }
}
