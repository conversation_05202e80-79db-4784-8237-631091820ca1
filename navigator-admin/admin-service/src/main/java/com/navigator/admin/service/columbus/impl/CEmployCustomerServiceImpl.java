package com.navigator.admin.service.columbus.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.dao.columbus.CEmployCustomerDao;
import com.navigator.admin.facade.columbus.CPowerFacade;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.admin.service.columbus.ICEmployCustomerService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.columbus.ICMenuService;
import com.navigator.admin.service.systemrule.SystemRuleService;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7
 */

@Service
@Slf4j
public class CEmployCustomerServiceImpl implements ICEmployCustomerService {

    @Resource
    private CEmployCustomerDao cEmployCustomerDao;
    @Resource
    private ICEmployService icEmployService;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private ICMenuService icMenuService;
    @Resource
    private SystemRuleService systemRuleService;
    @Resource
    private CPowerFacade cPowerFacade;


    @Override
    public boolean saveCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity) {
        return cEmployCustomerDao.save(cEmployCustomerEntity);
    }

    @Override
    public boolean updateCEmployCustomer(CEmployCustomerEntity cEmployCustomerEntity) {
        return cEmployCustomerDao.updateById(cEmployCustomerEntity);
    }

    @Override
    public CEmployCustomerEntity getCEmployCustomerById(Integer id) {
        return cEmployCustomerDao.getById(id);
    }

    @Override
    public List<CEmployCustomerEntity> queryCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return cEmployCustomerDao.cEmployCustomerByEmployIdAndCustomerId(cEmployId, customerId);
    }

    @Override
    public List<CEmployCustomerEntity> getCEmployCustomerByEmployIdAndCustomerId(Integer cEmployId, Integer customerId) {
        return cEmployCustomerDao.getCEmployCustomerByEmployIdAndCustomerId(cEmployId, customerId);
    }

    @Override
    public List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndType(Integer customerId, Integer type, Integer status) {
        return cEmployCustomerDao.queryCEmployCustomerByCustomerIdAndType(customerId, type, status);
    }

    @Override
    public List<CEmployCustomerEntity> queryCEmployCustomerByCustomerIdAndCEmployIdAndType(Integer customerId, Integer cEmployId, Integer type) {
        return cEmployCustomerDao.queryCEmployCustomerByCustomerIdAndCEmployIdAndType(customerId, cEmployId, type);
    }

    public void updateTypeByIds(List<Integer> ids) {
        cEmployCustomerDao.updateTypeByIds(ids);
    }

    @Override
    public void cEmployDataMigration() {
        List<CEmployEntity> cEmployEntityList = icEmployService.getCEmployEntity();
        for (CEmployEntity cEmployEntity : cEmployEntityList) {

            CEmployCustomerEntity cEmployCustomerEntity = BeanConvertUtils.convert(CEmployCustomerEntity.class, cEmployEntity);
            cEmployCustomerEntity
                    .setId(null)
                    .setCEmployId(cEmployEntity.getId())
            ;

            cEmployCustomerDao.save(cEmployCustomerEntity);
        }
    }

    @Override
    public List<CEmployCustomerVO> queryCEmployCustomerVOByCEmployId(Integer cEmployId) {
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerDao.cEmployCustomerByEmployIdAndCustomerId(cEmployId, null);

        if (cEmployCustomerEntities.isEmpty()) {
            return new ArrayList<>();
        }

        List<CEmployCustomerVO> cEmployCustomerVOS = new ArrayList<>();

        for (CEmployCustomerEntity cEmployCustomerEntity : cEmployCustomerEntities) {
            CEmployCustomerVO cEmployCustomerVO = new CEmployCustomerVO();

            CustomerEntity customerEntity = customerFacade.queryCustomerById(cEmployCustomerEntity.getCustomerId());
            if (null != customerEntity) {
                cEmployCustomerVO
                        .setCustomerId(cEmployCustomerEntity.getCustomerId())
                        .setCustomerName(customerEntity.getName())
                        .setSignatureStatus(cEmployCustomerEntity.getSignatureStatus())
                ;
                cEmployCustomerVOS.add(cEmployCustomerVO);
            }
        }

        return cEmployCustomerVOS;
    }

    @Override
    public CLoginVO getCEmployCustomerPower(Integer customerId) {
        CLoginVO cLoginVO = new CLoginVO();

        CustomerEntity customerEntity = customerFacade.queryCustomerById(customerId);
        if (null == customerEntity || DisableStatusEnum.DISABLE.getValue().equals(customerEntity.getStatus()) || IsDeletedEnum.DELETED.getValue().equals(customerEntity.getIsDeleted())) {
            throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
        }

        Integer employId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerDao.cEmployCustomerByEmployIdAndCustomerId(employId, customerId);
        if (cEmployCustomerEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.COMPANY_CUSTOMER_STSTUS_DISABLE);
        }
        //查询用户信息
        CEmployEntity cEmployEntity = icEmployService.getEmployById(employId);
        //查询菜单
        CRoleDTO roleDTO = new CRoleDTO();
        roleDTO.setEmployId(String.valueOf(employId))
                .setCustomerId(customerId);
        CMenuVO cMenuVO = icMenuService.getMenuByEmployIdV2(roleDTO);

        Result result = cPowerFacade.queryPowerByEmployIdV2(employId, customerId);
        List<String> powerCodeList = new ArrayList<>();
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            powerCodeList = JSON.parseArray(JSON.toJSONString(result.getData()), String.class);
        }
        cLoginVO.setMenuVO(cMenuVO);
        cLoginVO.setPowerCodeList(powerCodeList);
        //查询权限
        cLoginVO.setSignatureStatus(cEmployEntity.getSignatureStatus())
                .setPasswordModifyStatus(cEmployEntity.getPasswordModifyStatus())
        ;


        if (!cEmployCustomerEntities.isEmpty()) {
            CEmployCustomerEntity cEmployCustomerEntity = cEmployCustomerEntities.get(0);
            cEmployCustomerDao.updateById(cEmployCustomerEntity.setVisitTime(new Date()));
        }


        return cLoginVO;
    }


    public Result verifyUserStatus(Integer customerId) {
        //校验账号是否被禁用
        CustomerEntity customerEntity = customerFacade.queryCustomerById(customerId);
        if (customerEntity != null && !GeneralEnum.YES.getValue().equals(customerEntity.getIsColumbus())) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            return Result.success(false);
        }

        if (customerEntity != null && DisableStatusEnum.DISABLE.getValue().equals(customerEntity.getStatus())) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            return Result.success(false);
        }
        Integer cEmployId = Integer.valueOf(JwtUtils.getCurrentUserId());
        CEmployEntity cEmployEntity = icEmployService.getEmployById(cEmployId);

        if (cEmployEntity != null && DisableStatusEnum.DISABLE.getValue().equals(cEmployEntity.getStatus())) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            return Result.success(false);
        }

        List<CEmployCustomerEntity> cEmployCustomerEntitys = cEmployCustomerDao.cEmployCustomerByEmployIdAndCustomerId(cEmployId, customerId);

        if (cEmployCustomerEntitys.isEmpty()) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            return Result.success(false);
        }
        return Result.success(true);
    }
}
