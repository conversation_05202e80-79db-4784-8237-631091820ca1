package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.entity.EmployBusinessEntity;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface IEmployBusinessService {

    List<EmployBusinessEntity> queryListByEmployId(String employId);

    void save(EmployBusinessEntity employBusinessEntity);

    void deleteByEmployId(Integer employId);
}
