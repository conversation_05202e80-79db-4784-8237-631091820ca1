package com.navigator.admin.processor;

import org.springframework.context.ApplicationContextAware;

/**
 * Description: 抽象组件 ——》 具体的组件去实现before、after逻辑
 *              implement ApplicationContextAware 是为了子抽象类（AbstractAdminProcessor）获取 applicationContext
 * Created by <PERSON><PERSON><PERSON> on 2021/11/6 17:49
 */
public abstract class Processor<T> implements ApplicationContextAware {

    void before(T context){}

    void after(T context){}

}
