package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.DictMapper;
import com.navigator.admin.pojo.entity.DictEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-12-03 14:39
 */
@Dao
public class DictDao extends BaseDaoImpl<DictMapper, DictEntity> {


    public List<DictEntity> getDiceListByCodeAndParentId(String bizModuleCode, Integer parentId) {
        return this.list(Wrappers.<DictEntity>lambdaQuery()
                .eq(DictEntity::getCode, bizModuleCode)
                .eq(DictEntity::getParentId, parentId))
                .stream()
                .sorted(Comparator.comparing(DictEntity::getSort).thenComparing(DictEntity::getId))
                .collect(Collectors.toList());
    }
}
