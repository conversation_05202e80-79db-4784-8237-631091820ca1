package com.navigator.admin.service.impl;

import com.navigator.admin.dao.LoginLogDao;
import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.admin.pojo.entity.LoginLogEntity;
import com.navigator.admin.service.ILoginLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 登录日志服务实现类
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@Slf4j
public class LoginLogServiceImpl implements ILoginLogService {

    @Autowired
    private LoginLogDao loginLogDao;

    @Override
    public boolean saveLoginLog(LoginLogDTO loginLogDTO) {
        try {
            LoginLogEntity loginLogEntity = new LoginLogEntity();
            BeanUtils.copyProperties(loginLogDTO, loginLogEntity);

            if (loginLogEntity.getLoginTime() == null) {
                loginLogEntity.setLoginTime(new Date());
            }

            loginLogDao.save(loginLogEntity);
            log.info("登录日志保存成功，用户：{}，系统：{}，状态：{}",
                    loginLogDTO.getUsername(),
                    loginLogDTO.getLoginSystem(),
                    loginLogDTO.getLoginStatus());
            return true;
        } catch (Exception e) {
            log.error("保存登录日志失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public LoginLogEntity getLoginLogById(Integer id) {
        return loginLogDao.getById(id);
    }
}
