package com.navigator.admin.service.columbus.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.dao.columbus.CRoleDao;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.admin.pojo.qo.*;
import com.navigator.admin.pojo.vo.columbus.CRoleDefVO;
import com.navigator.admin.pojo.vo.columbus.CRoleVO;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.columbus.*;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Slf4j
@Service
public class CRoleServiceImpl implements ICRoleService {

    @Autowired
    private CRoleDao cRoleDao;
    @Autowired
    private ICEmployRoleService cEmployRoleService;
    @Autowired
    private IEmployService employService;
    @Autowired
    private ICRoleDefService cRoleDefService;
    @Autowired
    private CustomerFacade customerFacade;

    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;

    @Resource
    private ICMenuService menuService;

    @Resource
    private ICPowerService powerService;
    @Resource
    private ICRoleService roleService;
    @Resource
    private ICRoleMenuService roleMenuService;

    @Resource
    private ICRolePowerService rolePowerService;

    @Resource
    private ICEmployRoleService employRoleService;

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private ICEmployCustomerService employCustomerService;

    @Override
    public CRoleEntity getRoleById(Integer id) {
        return cRoleDao.getRoleById(id);
    }

    @Override
    public List<CRoleEntity> getRoleByRoleName(String roleName) {
        return cRoleDao.getRoleByRoleName(roleName);
    }

    @Override
    public void saveOrUpdate(CRoleDTO roleDTO, CRoleDefEntity cRoleDefEntity) {
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        List<Integer> categoryIdList = roleDTO.getCategoryIdList();
        if (roleDTO.getIsBaseCategory() == 0) {
            categoryIdList = Collections.singletonList(0);
        }
        List<Integer> salesTypeList = roleDTO.getSalesTypeList();
        if (roleDTO.getIsSalesType() == 0) {
            salesTypeList = Collections.singletonList(0);
        }
        cRoleDao.deleteByRoleDefId(roleDTO.getRoleDefId());
        for (Integer categoryId : categoryIdList) {
            for (Integer salesType : salesTypeList) {
                CRoleEntity cRoleEntity = new CRoleEntity();
                Integer category2 = 0;
                if (categoryId != null && categoryId != 0) {
                    category2 = Integer.parseInt(IdNameConverter.getName(IdNameType.category_id_serialNo, categoryId.toString()));
                }
                cRoleEntity.setCategoryId(categoryId)
                        .setSalesType(salesType)
                        .setRoleDefId(roleDTO.getRoleDefId())
                        .setName(roleDTO.getName())
                        .setLevel(2)
                        .setCreatedBy(userId)
                        .setUpdatedBy(userId)
                        .setCategory2(category2)
                ;
                cRoleDao.save(cRoleEntity);
            }
        }
    }

    @Override
    public List<CRoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList) {
        return cRoleDao.queryByRoleDefIdList(roleDefIdList);
    }

    @Override
    public List<CRoleEntity> getRoleListByDefId(Integer roleDefId) {
        return cRoleDao.getRoleListByDefId(roleDefId);
    }

    @Override
    public List<CRoleEntity> queryRole(CEmployRoleDTO employRoleDTO) {
        List<CRoleEntity> roleEntityList = cRoleDao.queryRole(employRoleDTO);
        return roleEntityList;
    }

    @Override
    public List<CRoleEntity> queryByIdList(List<Integer> roleIdList) {
        return cRoleDao.queryByIdList(roleIdList);
    }

    @Override
    public List<CRoleEntity> queryRoleListByDefInfo(Integer roleDefId, Integer categoryId, Integer customerId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefId(roleDefId)
                .setCategoryId(categoryId)
                .setBelongCustomerId(customerId);

        return cRoleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<CRoleEntity> queryRoleListByDefInfos(List<Integer> roleDefIds, Integer categoryId, Integer customerId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();


        roleQueryDTO.setRoleDefIdList(roleDefIds)
                .setCategoryId(categoryId)
                .setBelongCustomerId(customerId);

        return cRoleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<CRoleEntity> queryRoleListByDefInfos2(List<Integer> roleDefIds, Integer categoryId, Integer factoryId) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefIdList(roleDefIds)
                .setCategoryId(categoryId)
                .setFactoryId(factoryId);

        return cRoleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<CRoleEntity> queryRoleListByDefInfosSalesType(List<Integer> roleDefIds, Integer categoryId, Integer salesType) {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();

        roleQueryDTO.setRoleDefIdList(roleDefIds)
                .setCategoryId(categoryId)
                .setSalesType(salesType);

        return cRoleDao.queryRoleList(roleQueryDTO);
    }

    @Override
    public List<CRoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId) {
        return cRoleDao.queryByIdListAndCategory(roleIdList, categoryId);
    }

    @Override
    public List<CRoleEntity> getRoleAllList() {
        return cRoleDao.getRoleAllList();
    }

    @Override
    public List<CRoleEntity> queryRoleByEmployId(String employId, Integer customerId) {
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(Integer.parseInt(employId), customerId);
        if (CollectionUtil.isEmpty(employRoleEntityList)) {
            return Collections.emptyList();
        }
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
        return cRoleDao.queryByIdList(roleIdList);
    }

    @Override
    public Result queryRoleDefList(QueryDTO<CRoleQueryDTO> queryDTO) {
        Page<CRoleDefEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        //查询满足条件信息
        CRoleQueryDTO roleQueryDTO = queryDTO.getCondition();
        //分页查询
        IPage<CRoleDefEntity> iPage = cRoleDefService.queryPageByRoleQueryDTO(page, roleQueryDTO);
        List<CRoleDefVO> list = new ArrayList<>();
        iPage.getRecords().forEach(i -> {
            CRoleDefVO cRoleDefVO = new CRoleDefVO();
            cRoleDefVO
                    .setId(i.getId())
                    .setRoleName(i.getName())
                    .setCategoryIdList(JSON.parseArray(i.getRelatedCategoryId(), Integer.class))
                    .setSalesTypeList(JSON.parseArray(i.getRelatedSalesType(), Integer.class))
                    .setStatus(i.getStatus())
                    .setUpdatedAt(i.getUpdatedAt())
            ;
            if (CollUtil.isNotEmpty(cRoleDefVO.getCategoryIdList())) {
                List<String> categoryNameList = new ArrayList<>();
                cRoleDefVO.getCategoryIdList().forEach(c -> categoryNameList.add(IdNameConverter.getName(IdNameType.category_id_name, c.toString())));
                cRoleDefVO.setCategoryNameList(categoryNameList);
            }
            EmployEntity employEntity = employService.getEmployById(Integer.parseInt(i.getUpdatedBy()));
            if (employEntity != null) {
                cRoleDefVO.setUpdatedBy(employEntity.getName());
            }
            list.add(cRoleDefVO);
        });
        return Result.page(iPage, list);
    }

    @Override
    public Result queryPageByQueryDTO(Page<CRoleEntity> page, CRoleQueryDTO roleQueryDTO) {
        List<CRoleDefEntity> cRoleDefEntities = cRoleDefService.queryRoleDefForbidden();
        List<Integer> forbiddenRoleDefIdList = cRoleDefEntities.stream().map(CRoleDefEntity::getId).collect(Collectors.toList());
        IPage<CRoleEntity> iPage = cRoleDao.queryPageByQueryDTO(page, roleQueryDTO, forbiddenRoleDefIdList);
        List<CRoleVO> list = iPage.getRecords().stream().map(i -> {
            BusinessDetailUpdateRecordDTO businessDetailUpdateRecordDTO = new BusinessDetailUpdateRecordDTO();
            businessDetailUpdateRecordDTO.setBusinessId(i.getId())
                    .setDetailCode(BusinessDetailCodeEnum.C_EMPLOY_ROLE_EDIT.getValue());
            BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity = businessDetailUpdateRecordService.detailUpdateSelect(businessDetailUpdateRecordDTO);
            CRoleVO cRoleVO = new CRoleVO();
            // 品类名称
            if (i.getCategoryId() != null) {
                String category2Name = IdNameConverter.getName(IdNameType.category_id_name, i.getCategoryId().toString());
                cRoleVO.setCategoryName("0".equals(category2Name) ? "-" : category2Name);
            }
            cRoleVO.setRoleId(i.getId())
                    .setCategoryId(i.getCategoryId())
                    .setRoleName(i.getName())
                    .setSalesType(i.getSalesType())
                    .setUpdatedBy(businessDetailUpdateRecordEntity == null ? i.getUpdatedBy() == null ? "" : employService.getEmployById(i.getUpdatedBy()).getName() : businessDetailUpdateRecordEntity.getCreatedBy())
                    .setUpdatedAt(businessDetailUpdateRecordEntity == null ? i.getUpdatedAt() : businessDetailUpdateRecordEntity.getCreatedAt())
            ;
            return cRoleVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, list);

    }

    @Override
    public RoleAuthDTO getRoleAuth(RoleAuthQO roleAuthQO) {
        RoleAuthDTO roleAuthDTO = new RoleAuthDTO();
        // 授权菜单
        List<CMenuEntity> menuList = null;
        // 授权权限
        List<CPowerEntity> powerList = null;
        // 默认查询条件
        MenuQO menuQO = new MenuQO().setSystem(1);
        PowerQO powerQO = new PowerQO().setSystem(1).setIsCodeNotNull(1);
        CustomerEntity customerEntity = customerFacade.queryCustomerById(roleAuthQO.getCustomerId());
        //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 start
        if (null == customerEntity || DisableStatusEnum.DISABLE.getValue().equals(customerEntity.getStatus())) {
            throw new BusinessException(ResultCodeEnum.USER_CUSTOMER_FORBIDDEN);
        }
        //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 end
        // 查询通用
        if (roleAuthQO.getCategory2() == 0) {
            menuQO.setIsCategory(0);
            powerQO.setIsCategory(0);
        }
        if (this.isAdmin(roleAuthQO.getUserId(), roleAuthQO.getCustomerId())) {
            // 是管理员
            menuList = menuService.queryMenuList(menuQO);
            powerList = powerService.queryPowerList(powerQO);
        } else {
            // 不是管理员
            // 用户关联的所有角色
            List<Integer> userRoleIdList = employRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(roleAuthQO.getUserId()).setCustomerId(roleAuthQO.getCustomerId()));
            //过滤实角色的品类（默认+品类）
            List<CRoleEntity> roleEntityList = roleService.queryByIdList(userRoleIdList);
            if (CollUtil.isNotEmpty(roleEntityList)) {
                userRoleIdList = roleEntityList.stream()
                        .filter(role -> Arrays.asList(roleAuthQO.getCategory2(), 0).contains(role.getCategory2()))
                        .map(CRoleEntity::getId)
                        .collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(userRoleIdList)) {
                // 角色关联菜单
                Set<Integer> menuIdList = roleMenuService.queryMenuIdList(new RoleMenuQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(menuIdList)) {
                    menuQO.setMenuIdList(menuIdList);
                    menuList = menuService.queryMenuList(menuQO);
                }
                // 角色关联权限
                Set<Integer> powerIdList = rolePowerService.queryPowerIdList(new RolePowerQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(powerIdList)) {
                    powerQO.setPowerIdList(powerIdList);
                    powerList = powerService.queryPowerList(powerQO);
                }
            }
        }
        // 构造菜单树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(9).setWeightKey("sort");
        if (CollUtil.isEmpty(menuList)) {
            menuList = menuService.queryMenuList(new MenuQO().setCode("C001"));
        }
        List<Tree<Integer>> authMenuList = TreeUtil.build(menuList, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, roleAuthQO.getCategory2()));
        roleAuthDTO.setAuthMenuList(authMenuList);
        // 构造权限列表
        if (CollUtil.isNotEmpty(powerList)) {
            List<Tree<Integer>> authPowerList = new ArrayList<>();
            powerList.forEach(item -> authPowerList.add(this.toTree(item, null, roleAuthQO.getCategory2())));
            roleAuthDTO.setAuthPowerList(authPowerList);
        } else {
            roleAuthDTO.setAuthPowerList(new ArrayList<>());
        }

        // 更新访问时间
        List<CEmployCustomerEntity> cEmployCustomerEntitys = employCustomerService.queryCEmployCustomerByEmployIdAndCustomerId(roleAuthQO.getUserId(), roleAuthQO.getCustomerId());
        if (CollUtil.isNotEmpty(cEmployCustomerEntitys)) {
            cEmployCustomerEntitys.forEach(item -> {
                item.setVisitTime(new Date());
                employCustomerService.updateCEmployCustomer(item);
            });
        }
        return roleAuthDTO;
    }

    @Override
    public RoleAuthMenuDTO getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO) {
        // 所有菜单
        List<CMenuEntity> list = menuService.queryMenuList(new MenuQO().setSystem(1));
        // 角色关联菜单
        Set<Integer> authIdList = roleMenuService.queryMenuIdList(new RoleMenuQO().setRoleId(roleAuthMenuQO.getRoleId()));
        // 构造树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(2).setWeightKey("sort");
        List<Tree<Integer>> menuList = TreeUtil.build(list, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, null));
        RoleAuthMenuDTO roleAuthMenuDTO = new RoleAuthMenuDTO();
        roleAuthMenuDTO.setMenuList(menuList);
        // 叶子节点
        List<Integer> leafIdList = new ArrayList<>();
        for (Tree<Integer> tree : menuList) {
            if (CollUtil.isNotEmpty(tree.getChildren())) {
                for (Tree<Integer> child : tree.getChildren()) {
                    if (authIdList.contains(child.getId())) {
                        leafIdList.add(child.getId());
                    }
                }
            } else if (authIdList.contains(tree.getId())) {
                leafIdList.add(tree.getId());
            }
        }
        roleAuthMenuDTO.setAuthIdList(leafIdList);
        return roleAuthMenuDTO;
    }

    @Override
    public RoleAuthPowerDTO getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO) {
        // 所有权限
        List<CPowerEntity> list = powerService.queryPowerList(new PowerQO().setSystem(1));
        // 角色关联权限
        Set<Integer> authIdList = rolePowerService.queryPowerIdList(new RolePowerQO().setRoleId(roleAuthPowerQO.getRoleId()));
        // 构造树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig().setDeep(2).setWeightKey("sort");
        List<Tree<Integer>> powerList = TreeUtil.build(list, 0, treeNodeConfig, (entity, tree) -> this.toTree(entity, tree, null));
        RoleAuthPowerDTO roleAuthPowerDTO = new RoleAuthPowerDTO();
        roleAuthPowerDTO.setPowerList(powerList);
        roleAuthPowerDTO.setAuthIdList(authIdList);
        return roleAuthPowerDTO;
    }

    @Override
    public LinkedHashSet<Integer> queryCategory2List(Integer userId, Integer customerId) {
        LinkedHashSet<Integer> resultSet = new LinkedHashSet<>();
        // 用户ID
        if (StringUtil.isNullBlank(userId)) {
            userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        }
        log.info("用户ID：{}", userId.toString());
        // 用户关联角色
        List<Integer> roleIdList = employRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId).setCustomerId(customerId));
        log.info("用户关联角色个数：{}", roleIdList.size());
        if (CollUtil.isNotEmpty(roleIdList)) {
            List<CRoleEntity> list = cRoleDao.list(CRoleEntity.lqw(null).in(CRoleEntity::getId, roleIdList));
            if (CollUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    if (StringUtil.isNotNullBlank(item.getCategory2())) {
                        log.info("角色 {} 关联二级品类：{}", item.getId().toString(), item.getCategory2());
                        resultSet.add(item.getCategory2());
                    }
                });
            }
        }
        return resultSet;
    }

    @Override
    public Boolean isAdmin(Integer userId, Integer customerId) {
        if (userId == null) {
            userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        }
        // 用户ID小于10
        if (new BigDecimal(userId).compareTo(BigDecimal.TEN) <= 0) {
            log.info("是否用户ID {} 小于10：是", userId.toString());
            return true;
        }
        // 用户关联的所有角色
        List<Integer> userRoleIdList = employRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId).setCustomerId(customerId));
        log.info("用户关联的角色个数：{}", userRoleIdList.size());
        log.info("用户关联的角色Id：{}", userRoleIdList);
        if (CollUtil.isEmpty(userRoleIdList)) {
            log.info("用户无关联角色");
            // 是否初始管理员
            List<CEmployCustomerEntity> employCustomerList = employCustomerService.queryCEmployCustomerByCustomerIdAndCEmployIdAndType(customerId, userId, CEmployTypeEnum.DEFAULT.getType());
            if (CollUtil.isNotEmpty(employCustomerList)) {
                log.info("是否初始管理员：是");
                return true;
            }
//            throw new BusinessException("用户无关联角色！");
            log.info("是否初始管理员：否");
            return false;
        }
        // 是否配置文件指定管理员
        log.info("配置文件指定管理员：{}", commonProperties.getColumbusRoleList());
        if (CollUtil.containsAny(userRoleIdList, commonProperties.getColumbusRoleList())) {
            log.info("是否配置文件指定管理员：是");
            return true;
        }
        return false;
    }

    /**
     * 菜单转树
     *
     * @param entity
     * @param tree
     * @param category2
     */
    private void toTree(CMenuEntity entity, Tree<Integer> tree, Integer category2) {
        tree.setId(entity.getId());
        tree.setParentId(entity.getParentId());
        tree.setName(entity.getName());
        tree.putExtra("code", entity.getCode());
        tree.putExtra("parentCode", entity.getParentCode());
        tree.putExtra("icon", entity.getIcon());
        tree.putExtra("isCategory", entity.getIsCategory());
        if (StringUtil.isNotNullBlank(entity.getUrl()) && StringUtil.isNotNullBlank(category2)) {
            tree.putExtra("url", entity.getUrl().replaceAll("\\{category2\\}", category2.toString()));
        } else {
            tree.putExtra("url", entity.getUrl());
        }
        if (StringUtil.isNullBlank(entity.getSort())) {
            entity.setSort(100);
        }
        tree.putExtra("sort", entity.getSort());
    }

    /**
     * 权限转树
     *
     * @param entity
     * @param tree
     * @param category2
     */
    private Tree<Integer> toTree(CPowerEntity entity, Tree<Integer> tree, Integer category2) {
        if (tree == null) {
            tree = new Tree<>();
        }
        tree.setId(entity.getId());
        tree.setParentId(entity.getParentId());
        tree.setName(entity.getName());
        tree.putExtra("preCode", entity.getPreCode());
        if (StringUtil.isNotNullBlank(entity.getCode()) && StringUtil.isNotNullBlank(category2)) {
            tree.putExtra("code", entity.getCode().replaceAll("\\{category2\\}", category2.toString()));
        } else {
            tree.putExtra("code", entity.getCode());
        }
        tree.putExtra("describe", entity.getDescribe());
        tree.putExtra("level", entity.getLevel());
        tree.putExtra("isCategory", entity.getIsCategory());
        return tree;
    }
}
