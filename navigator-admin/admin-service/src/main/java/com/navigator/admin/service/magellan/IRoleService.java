package com.navigator.admin.service.magellan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.LinkedHashSet;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface IRoleService {

    RoleEntity getRoleById(Integer id);

    List<RoleEntity> queryByIdList(List<Integer> roleIdList);

    /**
     * 根据角色名称获取角色实体
     *
     * @param roleName
     * @return
     */
    List<RoleEntity> getRoleByRoleName(String roleName);

    void saveOrUpdate(RoleDTO roleDTO, RoleDefEntity roleDefEntity);

    List<RoleEntity> queryByRoleDefIdList(List<Integer> roleDefIdList);

    List<RoleEntity> getRoleListByDefId(Integer roleDefId);

    List<RoleEntity> queryRole(EmployRoleDTO employRoleDTO);

    List<RoleEntity> queryRoleListByDefInfo(Integer roleDefId, Integer categoryId, Integer customerId);

    List<RoleEntity> queryRoleListByDefInfos(List<Integer> roleDefIds, Integer categoryId, String siteCode);

    List<RoleEntity> queryRoleListByDefInfos2(List<Integer> roleDefIds, Integer categoryId, Integer factoryId);

    List<RoleEntity> queryByIdListAndCategory(List<Integer> roleIdList, Integer categoryId);

    List<RoleEntity> getRoleAllList();

    List<RoleEntity> getRoleAllListCode(String code);

    List<RoleEntity> queryRoleByEmployId(String employId);

    Result queryRoleDefList(QueryDTO<RoleQueryDTO> roleQueryDTO);

    Result queryPageByQueryDTO(Page<RoleEntity> page, RoleQueryDTO roleQueryDTO);

    List<RoleEntity> queryIdListByCategoryAndFactory(String name, List<Integer> categoryIdList, List<Integer> factoryIdList, List<Integer> companyIdList);

    void save(RoleDTO roleDTO, RoleDefEntity roleDefEntity);

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    RoleAuthDTO getRoleAuth(RoleAuthQO roleAuthQO);

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    RoleAuthMenuDTO getRoleAuthMenu(RoleAuthMenuQO roleAuthMenuQO);

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    RoleAuthPowerDTO getRoleAuthPower(RoleAuthPowerQO roleAuthPowerQO);

    /**
     * 根据用户ID：获取已授权的账套编码列表
     *
     * @param userId
     * @return
     */
    LinkedHashSet<String> queryRoleSiteCodeSet(Integer userId);

    /**
     * 根据用户ID：获取已授权的二级品类编码列表
     *
     * @param userId
     * @return
     */
    LinkedHashSet<Integer> queryCategory2List(Integer userId);

    /**
     * 根据用户ID：判断是否管理员
     *
     * @param userId
     * @return
     */
    Boolean isAdmin(Integer userId);
}
