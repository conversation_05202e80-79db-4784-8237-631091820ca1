package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.admin.pojo.entity.FileRecordEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

public interface FileRecordService {
    void save(FileRecordDTO fileRecordDTO);

    void modify(FileRecordDTO fileRecordDTO);

    FileRecordEntity queryFileRecordDetail(FileRecordDTO fileRecordDTO);

    Result queryFileRecordList(QueryDTO<FileRecordDTO> queryDTO);

    List<FileRecordEntity> queryFileRecordBySystemId(Integer systemId);
}
