package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.SystemRuleItemMapper;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.service.BaseDaoImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@Dao
@Slf4j
public class SystemRuleItemDao extends BaseDaoImpl<SystemRuleItemMapper, SystemRuleItemEntity> {

    public List<SystemRuleItemEntity> querySystemRuleItemList(Integer ruleId, Integer status) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(null != status, SystemRuleItemEntity::getStatus, status)
                .orderByAsc(SystemRuleItemEntity::getSort)
                .orderByDesc(SystemRuleItemEntity::getUpdatedAt)

        );
    }

    public List<SystemRuleItemEntity> querySystemRuleItemListByType(List<Integer> ruleIdList, Integer status, Integer salesType) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .in(SystemRuleItemEntity::getRuleId, ruleIdList)
                .eq(null != salesType, SystemRuleItemEntity::getValueType, salesType)
                .eq(null != status, SystemRuleItemEntity::getStatus, status)
                .orderByAsc(SystemRuleItemEntity::getSort)

        );
    }

    public SystemRuleItemEntity getWeightCheckByType(Integer ruleId, Integer salesType, String ruleKey) {
        List<SystemRuleItemEntity> ruleItemEntityList = list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(null != salesType, SystemRuleItemEntity::getValueType, salesType)
                .eq(StringUtils.isNotBlank(ruleKey), SystemRuleItemEntity::getRuleKey, ruleKey)
                .orderByDesc(SystemRuleItemEntity::getStatus)
        );
        return org.springframework.util.CollectionUtils.isEmpty(ruleItemEntityList) ? null : ruleItemEntityList.get(0);
    }


    public List<SystemRuleItemEntity> querySystemRuleItemList(Integer ruleId, String ruleKey, String companyId, Integer goodsId) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(StringUtils.isNotBlank(ruleKey), SystemRuleItemEntity::getRuleKey, ruleKey)
                .eq(SystemRuleItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .in(null != goodsId, SystemRuleItemEntity::getGoodsId, Arrays.asList(goodsId))
                .like(SystemRuleItemEntity::getCompanyId, companyId)
        );
    }

    public List<SystemRuleItemEntity> querySystemRuleValueItemList(Integer ruleId, String ruleKey, String ruleVale, String memo) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(StringUtils.isNotBlank(ruleKey), SystemRuleItemEntity::getRuleKey, ruleKey)
                .like(StringUtils.isNotBlank(ruleVale), SystemRuleItemEntity::getRuleValue, ruleVale)
                .like(StringUtils.isNotBlank(memo), SystemRuleItemEntity::getMemo, memo)
                .eq(SystemRuleItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    public SystemRuleItemEntity queryRuleItemByValue(Integer ruleId, String ruleKey, String ruleValue, Integer status) {
        List<SystemRuleItemEntity> systemRuleItemEntities = list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(StringUtils.isNotBlank(ruleKey), SystemRuleItemEntity::getRuleKey, ruleKey)
                .like(StringUtils.isNotBlank(ruleValue), SystemRuleItemEntity::getRuleValue, ruleValue)
                .eq(null != status, SystemRuleItemEntity::getStatus, status)
                .orderByDesc(SystemRuleItemEntity::getStatus, SystemRuleItemEntity::getId)
        );
        return CollectionUtils.isEmpty(systemRuleItemEntities) ? null : systemRuleItemEntities.get(0);
    }

    public SystemRuleItemEntity findByLkgCode(Integer ruleId, String lkgCode, Integer status) {
        List<SystemRuleItemEntity> systemRuleItemEntityList = this.baseMapper.selectList(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getLkgCode, lkgCode)
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(null != status, SystemRuleItemEntity::getStatus, status)
        );
        return CollectionUtils.isEmpty(systemRuleItemEntityList) ? null : systemRuleItemEntityList.get(0);
    }

    public SystemRuleItemEntity findByLkgCodeNotId(Integer ruleId, String lkgCode, Integer ruleItemId) {
        List<SystemRuleItemEntity> systemRuleItemEntityList = this.baseMapper.selectList(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getLkgCode, lkgCode)
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .notIn(SystemRuleItemEntity::getId, ruleItemId)
                .eq(SystemRuleItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
        return CollectionUtils.isEmpty(systemRuleItemEntityList) ? null : systemRuleItemEntityList.get(0);
    }

    public List<SystemRuleItemEntity> queryProteinSystemRuleItemList(Integer ruleId, String ruleKey, String ruleValue, String memo) {
        return this.baseMapper.selectList(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleKey, ruleKey)
                .like(SystemRuleItemEntity::getRuleValue, ruleValue)
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .like(SystemRuleItemEntity::getMemo, memo)
                .orderByDesc(SystemRuleItemEntity::getId)
        );
    }

    public List<SystemRuleItemEntity> getRuleItemByIdList(List<Integer> ruleItemIdList) {
        return this.list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(ruleItemIdList), SystemRuleItemEntity::getId, ruleItemIdList)
                .eq(SystemRuleItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .orderByDesc(SystemRuleItemEntity::getUpdatedAt, SystemRuleItemEntity::getId)
        );
    }

    /**
     * 删除该品类的配置信息
     *
     * @param ruleId 品类ID
     */
    public void dropItemByRuleId(Integer ruleId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(SystemRuleItemEntity::getRuleId, ruleId)
                .eq(SystemRuleItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .set(SystemRuleItemEntity::getStatus, DisableStatusEnum.DISABLE.getValue())
                .update();
    }


    public SystemRuleItemEntity queryRuleItemByRuleKey(String ruleKey, Integer ruleId, Integer companyId) {
        List<SystemRuleItemEntity> systemRuleItemEntities = list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(ruleKey), SystemRuleItemEntity::getRuleKey, ruleKey)
                .eq(ruleId != null, SystemRuleItemEntity::getRuleId, ruleId)
                .eq(ruleId != null, SystemRuleItemEntity::getCompanyId, companyId)
        );
        return CollectionUtils.isEmpty(systemRuleItemEntities) ? null : systemRuleItemEntities.get(0);
    }

    public List<SystemRuleItemEntity> queryRuleItemListByRuleId(List<Integer> ruleIdList) {
        List<SystemRuleItemEntity> systemRuleItemEntities = list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .in(SystemRuleItemEntity::getRuleId, ruleIdList)
        );
        return systemRuleItemEntities;
    }

    public List<SystemRuleItemEntity> querySystemRuleItemListByApproveConfig(List<Integer> ruleIdList, String ruleKey, Integer companyId) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .in(SystemRuleItemEntity::getRuleId, ruleIdList)
                .eq(SystemRuleItemEntity::getRuleKey, ruleKey)
                .eq(SystemRuleItemEntity::getCompanyId, companyId)
        );
    }

    public List<SystemRuleItemEntity> querySystemRuleItemListByApproveConfigNotId(Integer ruleIdList, String ruleKey, Integer companyId) {
        return list(Wrappers.<SystemRuleItemEntity>lambdaQuery()
                .eq(SystemRuleItemEntity::getRuleId, ruleIdList)
                .eq(SystemRuleItemEntity::getRuleKey, ruleKey)
                .eq(SystemRuleItemEntity::getCompanyId, companyId)
        );
    }

}
