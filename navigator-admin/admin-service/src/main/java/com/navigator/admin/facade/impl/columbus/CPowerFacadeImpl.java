package com.navigator.admin.facade.impl.columbus;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.columbus.CPowerFacade;
import com.navigator.admin.pojo.dto.columbus.CPowerDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.CEmployTypeEnum;
import com.navigator.admin.pojo.vo.columbus.CPowerVO;
import com.navigator.admin.service.columbus.*;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class CPowerFacadeImpl implements CPowerFacade {
    @Autowired
    private ICPowerService cPowerService;
    @Autowired
    private ICRolePowerService cRolePowerService;
    @Autowired
    private ICEmployRoleService cEmployRoleService;
    @Resource
    private CommonProperties commonProperties;
    @Autowired
    private ICEmployService cEmployService;
    @Resource
    private ICEmployCustomerService cEmployCustomerService;

    /**
     * v1.0 菜单按钮权限独立设置
     */
    @Override
    public Result saveOrUpdatePower(CPowerDTO powerDTO) {
        cRolePowerService.saveOrUpdateRolePower(powerDTO);
        return Result.success();
    }

    @Override
    public Result queryPowerByRoleId(CPowerDTO powerDTO) {
        CPowerVO powerVO = new CPowerVO();
        List<CPowerEntity> powerEntityList = cPowerService.queryPower();
        Map<Integer, List<CPowerEntity>> map = powerEntityList.stream().sorted(Comparator.comparing(CPowerEntity::getId)).collect(Collectors.groupingBy(CPowerEntity::getParentId));
        List<CPowerEntity> allPowerVOList = powerEntityList.stream().map(
                i -> {
                    i.setChildren(map.get(i.getId()));
                    return i;
                }
        ).collect(Collectors.toList());
        List<CPowerEntity> list = allPowerVOList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        powerVO.setPowerEntityList(list);
        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CEmployEntity employEntity = cEmployService.getEmployById(userId);
        List<CRolePowerEntity> rolePowerEntityList = cRolePowerService.queryByRoleId(powerDTO.getRoleId(), employEntity.getCustomerId());

        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(CRolePowerEntity::getPowerId).collect(Collectors.toList());
            powerVO.setPowerIdList(powerIdList);
        }
        return Result.success(powerVO);
    }

    @Override
    public Result queryPowerByEmployId(Integer employId) {
        List<String> powerCodeList = new ArrayList<>();
        if (employId <= 10) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmploy(String.valueOf(employId));
        if (CollectionUtils.isEmpty(employRoleEntityList)) {
            return Result.success(Collections.emptyList());
        }
        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());

        if (CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }

        List<CRolePowerEntity> rolePowerEntityList = cRolePowerService.queryByRoleIdList(roleIdList);
        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(CRolePowerEntity::getPowerId).distinct().collect(Collectors.toList());
            List<CPowerEntity> powerEntityList = cPowerService.queryPowerByIdList(powerIdList);
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Result.success(powerCodeList);
    }

    /**
     * v2.0 统一菜单按钮权限
     */
    @Override
    public Result saveOrUpdatePowerV2(CPowerDTO powerDTO) {
        cRolePowerService.saveOrUpdateRolePowerV2(powerDTO);
        return Result.success();
    }

    @Override
    public Result queryPowerByRoleIdV2(CPowerDTO powerDTO) {
        CPowerVO powerVO = new CPowerVO();
        List<CPowerEntity> powerEntityList = cPowerService.queryPower();
        Map<Integer, List<CPowerEntity>> map = powerEntityList.stream().sorted(Comparator.comparing(CPowerEntity::getId)).collect(Collectors.groupingBy(CPowerEntity::getParentId));
        List<CPowerEntity> allPowerVOList = powerEntityList.stream().map(
                i -> {
                    i.setChildren(map.get(i.getId()));
                    return i;
                }
        ).collect(Collectors.toList());
        List<CPowerEntity> list = allPowerVOList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        powerVO.setPowerEntityList(list);
        List<CRolePowerEntity> rolePowerEntityList = cRolePowerService.queryByRoleIdV2(powerDTO.getRoleId());

        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(CRolePowerEntity::getPowerId).collect(Collectors.toList());
            powerVO.setPowerIdList(powerIdList);
        }
        return Result.success(powerVO);
    }

    @Override
    public Result queryPowerByEmployIdV2(Integer employId, Integer customerId) {
        List<String> powerCodeList = new ArrayList<>();
        if (employId <= 10) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(employId, customerId);

        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
        //CEmployEntity cEmployEntity = cEmployService.getEmployById(employId);

        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerService.queryCEmployCustomerByCustomerIdAndCEmployIdAndType(customerId, employId, CEmployTypeEnum.DEFAULT.getType());
        if (CollectionUtils.isEmpty(roleIdList) && !cEmployCustomerEntities.isEmpty()) {
            roleIdList = Collections.singletonList(1);
        }
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Result.success(Collections.emptyList());
        }
        if (CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }

        List<CRolePowerEntity> rolePowerEntityList = cRolePowerService.queryByRoleIdListV2(roleIdList);
        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(CRolePowerEntity::getPowerId).distinct().collect(Collectors.toList());
            List<CPowerEntity> powerEntityList = cPowerService.queryPowerByIdList(powerIdList);
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Result.success(powerCodeList);
    }


    @Override
    public Result queryPowerByEmployIdCustomerV2(Integer employId, Integer customerId) {
        List<String> powerCodeList = new ArrayList<>();
        if (employId <= 10) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }
        List<CEmployRoleEntity> employRoleEntityList = cEmployRoleService.getEmployRolesByEmployAndCustomerId(employId, customerId);

        List<Integer> roleIdList = employRoleEntityList.stream().map(CEmployRoleEntity::getRoleId).collect(Collectors.toList());
        CEmployEntity cEmployEntity = cEmployService.getEmployById(employId);
        if (CollectionUtils.isEmpty(roleIdList) && CEmployTypeEnum.DEFAULT.getType().equals(cEmployEntity.getType())) {
            roleIdList = Collections.singletonList(1);
        }
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Result.success(Collections.emptyList());
        }
        if (CollectionUtil.containsAny(roleIdList, commonProperties.getColumbusRoleList())) {
            List<CPowerEntity> powerEntityList = cPowerService.queryPower();
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }

        List<CRolePowerEntity> rolePowerEntityList = cRolePowerService.queryByRoleIdListV2(roleIdList);
        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(CRolePowerEntity::getPowerId).distinct().collect(Collectors.toList());
            List<CPowerEntity> powerEntityList = cPowerService.queryPowerByIdList(powerIdList);
            powerCodeList = powerEntityList.stream().map(CPowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Result.success(powerCodeList);
    }


    @Override
    public Result addRolePower(CPowerDTO powerDTO) {
        cRolePowerService.addRolePower(powerDTO);
        return Result.success();


    }

}
