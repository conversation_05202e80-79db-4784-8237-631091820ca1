package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.admin.mapper.UserMenuCollectMapper;
import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:38
 **/
@Dao
public class UserMenuCollectDao extends BaseDaoImpl<UserMenuCollectMapper, UserMenuCollectEntity> {

    public List<UserMenuCollectEntity> getMenuCollectList(Integer userId, Integer customerId, Integer system) {
        return this.list(Wrappers.<UserMenuCollectEntity>lambdaQuery()
                .eq(null != userId, UserMenuCollectEntity::getUserId, userId)
                .eq(null != customerId && 0 != customerId, UserMenuCollectEntity::getCustomerId, customerId)
                .eq(null != system, UserMenuCollectEntity::getSystem, system)
                .eq(UserMenuCollectEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(UserMenuCollectEntity::getSort)
        );
    }

    public List<UserMenuCollectEntity> getMenuCollectListByCategory(Integer userId, Integer categoryId, Integer system) {
        return this.list(Wrappers.<UserMenuCollectEntity>lambdaQuery()
                .eq(null != userId, UserMenuCollectEntity::getUserId, userId)
                .eq(null != categoryId, UserMenuCollectEntity::getCategoryId, categoryId)
                .eq(null != system, UserMenuCollectEntity::getSystem, system)
                .eq(UserMenuCollectEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(UserMenuCollectEntity::getSort)
        );
    }

    public Boolean judgeExistSameMenuName(String newMenuName, Integer collectId,Integer userId) {
        List<UserMenuCollectEntity> collectEntityList = this.list(Wrappers.<UserMenuCollectEntity>lambdaQuery()
                .ne(null != collectId, UserMenuCollectEntity::getId, collectId)
                .eq(null != userId, UserMenuCollectEntity::getUserId, userId)
                .eq(StringUtils.isNotBlank(newMenuName), UserMenuCollectEntity::getNewMenuName, newMenuName.trim())
                .eq(UserMenuCollectEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return !CollectionUtils.isEmpty(collectEntityList);
    }

    public void deleteMenuCollect(Integer id) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(UserMenuCollectEntity::getId, id)
                .eq(UserMenuCollectEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(UserMenuCollectEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }
}
