package com.navigator.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseAreaEntity;
import com.navigator.admin.pojo.entity.WarehouseCityEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.qo.WarehouseSiteQO;
import com.navigator.common.dto.QueryDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * dba_warehouse 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface IWarehouseService {
    /**
     * 新增库点
     */
    boolean addWarehouse(WarehouseEntity warehouseEntity);

    /**
     * 编辑库点
     */
    boolean updateWarehouse(WarehouseEntity warehouseEntity);

    /**
     * 分页查询库点
     */
    IPage<WarehouseEntity> getWarehouseList(QueryDTO<WarehouseBO> queryDTO);

    /**
     * 根据账套id查询库点
     *
     * @param siteCode 账套编码
     * @param type     库点类型 0 通用 1 发货 2 提货
     */
    List<WarehouseEntity> getWarehouseBySiteCode(String siteCode, Integer type);

    /**
     * 根据主体id查询库点
     */
    List<WarehouseEntity> getWarehouseByCompanyId(Integer companyId, Integer category2);

    /**
     * 根据唯一码查询库点
     */
    WarehouseEntity getWarehouseByUniqueCode(String uniqueCode, Integer type);

    /**
     * 根据名称查询交割库点
     */
    WarehouseEntity getWarehouseByName(String name, Integer isDce);

    /**
     * 根据id查询库点
     */
    WarehouseEntity getWarehouseById(Integer id);

    /**
     * 获取所有库点
     *
     * @return
     */
    List<WarehouseEntity> getAllWarehouse();

    /**
     * 根据warehouseId获取MarketZone
     *
     * @param warehouseId 库点id
     * @return
     */
    String getWarehouseMarketZone(Integer warehouseId);

    /**
     * 获取所有地域信息
     *
     * @return
     */
    List<WarehouseAreaEntity> getGeographicAreaList();

    /**
     * 根据地域id获取城市信息
     *
     * @param areaId 地域id
     * @return
     */
    List<WarehouseCityEntity> getGeographicCityByAreaId(Integer areaId);

    /**
     * 根据工厂编码获取库点信息
     *
     * @param factoryCode
     * @param category2
     * @return
     */
    List<WarehouseEntity> getWarehouseByFactoryCode(String factoryCode, Integer category2);

    /**
     * 根据条件：获取dba_warehouse列表
     *
     * @param condition
     * @return
     */
    List<WarehouseEntity> queryWarehouseList(WarehouseBO condition);

    /**
     * 根据主体和工厂编码获取库点信息
     *
     * @param warehouseSiteQO
     * @return
     */
    List<WarehouseEntity> getWarehouseByCompanyIdAndFactoryCode(WarehouseSiteQO warehouseSiteQO);

    /**
     * 导入库点信息
     *
     * @param file 上传的文件
     * @return
     */
    String importWarehouse(MultipartFile file);

    String importWarehouseArea(MultipartFile file);

    String importWarehouseCity(MultipartFile file);
}
