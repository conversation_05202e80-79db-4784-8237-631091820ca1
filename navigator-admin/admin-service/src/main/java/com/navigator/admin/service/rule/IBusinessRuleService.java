package com.navigator.admin.service.rule;

import com.navigator.admin.pojo.dto.rule.RuleCreateDTO;
import com.navigator.admin.pojo.dto.rule.RuleQueryDTO;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 15:35
 **/
public interface IBusinessRuleService {
    /**
     * @param ruleCreateDTO 加载条件信息详情
     * @return 条件信息规则
     */
    BusinessRuleEntity recordBusinessRule(RuleCreateDTO ruleCreateDTO);

    /**
     * 回显规则信息
     *
     * @return
     */
    BusinessRuleEntity getRuleDetailByBusinessCode(RuleQueryDTO ruleQueryDTO);


    /**
     * @param referCode
     * @param referType {@link com.navigator.common.enums.RuleReferTypeEnum}
     * @return
     */
    BusinessRuleEntity getRuleByBusinessCode(String referCode, String referType, String moduleType, String systemId);

    /**
     * @param referCode
     * @param referType {@link com.navigator.common.enums.RuleReferTypeEnum}
     * @return
     */
    BusinessRuleEntity getRuleDetailEntityByCode(String referCode, String referType, String moduleType, String systemId);
}
