package com.navigator.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.dto.QueryTodoDTO;
import com.navigator.admin.pojo.entity.TodoListEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

/**
 * <p>
 * 待办业务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface ITodoListService extends IService<TodoListEntity> {

    Result queryTodoList(QueryDTO<QueryTodoDTO> queryDTO);
}
