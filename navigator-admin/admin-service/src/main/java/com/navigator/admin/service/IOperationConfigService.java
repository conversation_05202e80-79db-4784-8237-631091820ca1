package com.navigator.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.admin.pojo.entity.OperationConfigEntity;

import java.util.List;

/**
 * <p>
 * 操作日志配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IOperationConfigService extends IService<OperationConfigEntity> {

    /**
     * 根据bizCode查询配置
     *
     * @param bizCode
     * @return
     */
    List<OperationConfigEntity> queryOperationConfigByBizCode(String bizCode);
}
