package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.OperationLogMapper;
import com.navigator.admin.pojo.entity.OperationLogEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class OperationLogDao extends BaseDaoImpl<OperationLogMapper, OperationLogEntity> {

    public List<OperationLogEntity> queryOperationLogList(Integer targetRecordId, Integer logLevel) {
        return this.baseMapper.selectList(
                Wrappers.<OperationLogEntity>lambdaQuery()
                        .eq(null != targetRecordId, OperationLogEntity::getTargetRecordId, targetRecordId)
                        .le(null != logLevel, OperationLogEntity::getLogLevel, logLevel)
                        .ne(OperationLogEntity::getBizModule, "权益变更")
                        .orderByAsc(OperationLogEntity::getId)
        );
    }

}
