package com.navigator.admin.service.impl;

import com.navigator.admin.dao.CommonRelationDao;
import com.navigator.admin.pojo.enums.CommonRelationTypeEnum;
import com.navigator.admin.service.ICommonRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-08-16 17:55
 **/
@Service
public class CommonRelationServiceImpl implements ICommonRelationService {
    @Resource
    private CommonRelationDao commonRelationDao;

    @Override
    public void recordCommonRelation(String bindCode1, List<String> bindCode2List, CommonRelationTypeEnum relationTypeEnum) {
        commonRelationDao.recordCommonRelation(bindCode1, bindCode2List, relationTypeEnum);
    }
}
