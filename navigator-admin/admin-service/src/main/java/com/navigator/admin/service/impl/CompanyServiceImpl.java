package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.CompanyDao;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.dto.CompanyDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.service.ICompanyService;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Service
public class CompanyServiceImpl implements ICompanyService {

    @Autowired
    private CompanyDao companyDao;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Resource
    private SiteFacade siteFacade;

    @Override
    public List<CompanyEntity> queryCompanyList() {
        return companyDao.queryCompanyList();
    }

    @Override
    public List<CompanyDTO> queryCompanyDTOList() {

        List<CompanyEntity> companyEntities = companyDao.queryCompanyList();

        List<CompanyDTO> companyDTOS = BeanConvertUtils.convert2List(CompanyDTO.class, companyEntities);

        companyDTOS.forEach(companyDTO -> {

            //查询客户主数据
            CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndLDC(companyDTO.getId());

            if (null != customerEntity) {
                companyDTO
                        .setLinkageCustomerCode(customerEntity.getLinkageCustomerCode())
                        .setSignPlace(customerEntity.getSignPlace())
                ;
            }
        });

        return companyDTOS;
    }

    @Override
    public CompanyEntity queryCompanyById(Integer id) {
        return companyDao.getById(id);
    }

    @Override
    public List<CompanyEntity> getAllCompany() {
        return this.companyDao.list();
    }

    @Override
    public CompanyEntity queryCompanyByName(String companyName) {
        return companyDao.queryCompanyByName(companyName);
    }

    @Override
    public Result saveCompany(CompanyDTO companyDTO) {
        if (StringUtil.isEmpty(companyDTO.getLinkageCustomerCode())
                || StringUtil.isEmpty(companyDTO.getName())
                || StringUtil.isEmpty(companyDTO.getShortName())
        ) {
            throw new BusinessException(ResultCodeEnum.EMPLOY_PARAM_ERROR);
        }

        //判断主体名称是否存在
        CompanyEntity company = companyDao.queryCompanyByName(companyDTO.getName().trim());
        if (null != company) {
            throw new BusinessException(ResultCodeEnum.COMPANY_NAME_EXISTING);
        }

        //判断主体简称是否存在
        List<CompanyEntity> companyEntities1 = companyDao.queryCompanyByShortName(companyDTO.getShortName().trim(), null);
        if (!companyEntities1.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.COMPANY_SHOR_NAME_EXISTING);
        }

        //判断主体简称是否存在
        List<CompanyEntity> companyEntities2 = companyDao.queryCompanyByCompanyCode(companyDTO.getLinkageCustomerCode().trim());
        if (!companyEntities2.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.COMPANY_LKG_CODE_EXISTING);
        }

        String logo = companyDTO.getLogo().substring(0, companyDTO.getLogo().indexOf('?'));

        CompanyEntity companyEntity = BeanConvertUtils.convert(CompanyEntity.class, companyDTO);

        companyEntity
                .setLogo(logo)
                .setCompanyCode(companyDTO.getLinkageCustomerCode().trim())
                .setShortName(companyDTO.getShortName().trim())
                .setName(companyDTO.getName().trim())
                .setCreatedAt(new Date())
                .setCreatedBy(redisCacheMap.get(Integer.parseInt(JwtUtils.getCurrentUserId())))
                .setUpdatedAt(new Date())
                .setUpdatedBy(redisCacheMap.get(Integer.parseInt(JwtUtils.getCurrentUserId())));
        companyDao.save(companyEntity);


        //判断lkg编码是否存在
        CustomerEntity customerEntity = new CustomerEntity();
        CustomerEntity customer = customerFacade.queryCustomerByLinkageCode(companyDTO.getLinkageCustomerCode().trim());
        if (null != customer && 0 == customer.getIsColumbus()) {
            customer
                    .setIsLdc(GeneralEnum.YES.getValue())
                    .setName(companyDTO.getName().trim())
                    .setIsSupplier(GeneralEnum.YES.getValue())
                    .setAddress(StringUtil.isNotEmpty(companyDTO.getAddress()) ? companyDTO.getAddress() : customer.getAddress())
                    .setSignPlace(companyDTO.getSignPlace())
                    .setCompanyId(companyEntity.getId());
            customerFacade.updateCustomer(customer);
        } else {
            customerEntity
                    .setLinkageCustomerCode(companyDTO.getLinkageCustomerCode().trim())
                    .setName(companyDTO.getName().trim())
                    .setIsLdc(GeneralEnum.YES.getValue())
                    .setIsSupplier(GeneralEnum.YES.getValue())
                    .setShortName(companyDTO.getShortName())
                    .setAddress(companyDTO.getAddress())
                    .setSignPlace(companyDTO.getSignPlace())
                    .setCompanyId(companyEntity.getId());

            //添加客户主体数据
            customerFacade.addCustomer(customerEntity);
        }
        return Result.success();
    }

    @Override
    public Result updateCompany(CompanyDTO companyDTO) {
        if (null == companyDTO.getId()
                || StringUtil.isEmpty(companyDTO.getName())
                || StringUtil.isEmpty(companyDTO.getShortName())
        ) {
            throw new BusinessException("请将信息填写完整");
        }
        CompanyEntity companyEntity = companyDao.getById(companyDTO.getId());
        if (null == companyEntity) {
            throw new BusinessException("主体不存在");
        }

        //判断主体名称是否存在
        List<CompanyEntity> company = companyDao.queryCompanyByNameNotId(companyDTO.getName().trim(), companyDTO.getId());
        if (CollectionUtils.isNotEmpty(company)) {
            throw new BusinessException(ResultCodeEnum.COMPANY_NAME_EXISTING);
        }

        List<CompanyEntity> companyEntities1 = companyDao.queryCompanyByShortName(companyDTO.getShortName().trim(), companyDTO.getId());
        if (companyEntities1.size() > 1) {
            throw new BusinessException(ResultCodeEnum.COMPANY_SHOR_NAME_EXISTING);
        }


        CustomerEntity customerEntity = customerFacade.queryCustomerByLinkageCode(companyDTO.getLinkageCustomerCode());
        if (null != customerEntity) {
            customerEntity
                    .setName(companyDTO.getName().trim())
                    .setSignPlace(companyDTO.getSignPlace())
            ;
            customerFacade.updateCustomer(customerEntity);
        }

        companyEntity = BeanConvertUtils.convert(CompanyEntity.class, companyDTO);
        Integer index = companyDTO.getLogo().indexOf('?');
        if (index > 0) {
            String logo = companyDTO.getLogo().substring(0, companyDTO.getLogo().indexOf('?'));
            companyEntity
                    .setLogo(logo);
        }
        companyEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(redisCacheMap.get(Integer.parseInt(JwtUtils.getCurrentUserId())));
        companyDao.updateById(companyEntity);
        return Result.success();
    }

    @Override
    public CompanyEntity getCompanyByCode(String companyCode) {
        return companyDao.getCompanyByCode(companyCode);
    }

    @Override
    public List<CompanyEntity> getAllCompanyBySyncSystem(String syncSystem) {
        // Get all company ids by sync system
        Result<List<SiteEntity>> result = siteFacade.getSiteListBySyncSystem(syncSystem);
        if (result.isSuccess()) {
            Set<Integer> companyIds = result.getData().stream()
                    .map(SiteEntity::getCompanyId)
                    .collect(Collectors.toSet());

            return companyIds.stream()
                    .map(companyDao::getById)
                    .collect(Collectors.toList());
        }
        return null;
    }

}
