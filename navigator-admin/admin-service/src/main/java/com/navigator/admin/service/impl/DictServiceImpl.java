package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.DictItemDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.admin.service.IDictService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@Slf4j
public class DictServiceImpl implements IDictService {

    @Resource
    private DictItemDao dictItemDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public Result queryByCondition(QueryDTO<DictItemEntity> queryDTO) {
        DictItemEntity dictItemQO = queryDTO.getCondition();
        List<String> filterTemplateVipCode = new ArrayList<>();
        if (StringUtils.isNotBlank(dictItemQO.getCustomerCode())) {
            List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(Arrays.asList(dictItemQO.getCustomerCode()));
            if (!CollectionUtils.isEmpty(customerEntityList)) {
                filterTemplateVipCode = customerEntityList.stream()
                        .filter(it -> {
                            return StringUtils.isNotBlank(it.getTemplateVipCode());
                        })
                        .map(CustomerEntity::getTemplateVipCode).distinct().collect(Collectors.toList());
            }
        }
        log.info("过滤的集团客户编码信息:" + FastJsonUtils.getBeanToJson(filterTemplateVipCode));
        IPage<DictItemEntity> itemEntityIPage = dictItemDao.queryByCondition(queryDTO, filterTemplateVipCode);
        if (!CollectionUtils.isEmpty(itemEntityIPage.getRecords())) {
            itemEntityIPage.getRecords().forEach(dictItemEntity -> {
                List<CustomerEntity> vipCustomerList = customerFacade.getCustomerListByTemplateVipCode(dictItemEntity.getItemCode());
                if (!CollectionUtils.isEmpty(vipCustomerList)) {
                    String customerNames = vipCustomerList.stream().map(CustomerEntity::getName).collect(Collectors.joining(","));
                    List<String> customerCodeList = vipCustomerList.stream().map(CustomerEntity::getLinkageCustomerCode).collect(Collectors.toList());
                    dictItemEntity.setCustomerNames(customerNames)
                            .setCustomerCode(StringUtils.join(customerCodeList, ","))
                            .setCustomerList(vipCustomerList)
                            .setCustomerCodeList(customerCodeList);
                }
            });
        }
        return Result.page(itemEntityIPage);
    }

    @Override
    public List<DictItemEntity> queryExportVipCustomerList(DictItemEntity dictItemQO) {
        List<String> filterTemplateVipCode = new ArrayList<>();
        if (StringUtils.isNotBlank(dictItemQO.getCustomerCode())) {
            List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(Arrays.asList(dictItemQO.getCustomerCode()));
            if (!CollectionUtils.isEmpty(customerEntityList)) {
                filterTemplateVipCode = customerEntityList.stream()
                        .filter(it -> {
                            return StringUtils.isNotBlank(it.getTemplateVipCode());
                        })
                        .map(CustomerEntity::getTemplateVipCode).distinct().collect(Collectors.toList());
            }
        }
        List<DictItemEntity> dictItemEntityList = dictItemDao.queryDictItemList(dictItemQO, filterTemplateVipCode);
        if (!CollectionUtils.isEmpty(dictItemEntityList)) {
            dictItemEntityList.forEach(dictItemEntity -> {
                List<CustomerEntity> vipCustomerList = customerFacade.getCustomerListByTemplateVipCode(dictItemEntity.getItemCode());
                dictItemEntity.setCustomerList(vipCustomerList);
            });
        }
        return dictItemEntityList;
    }

    @Override
    public List<DictItemEntity> getDictItemById(List<Integer> dictItemIdList) {
        return dictItemDao.getDictItemById(dictItemIdList);
    }

    @Override
    public List<DictItemEntity> getItemByDictCode(String dictCode) {
        return dictItemDao.getItemByDictCode(dictCode);
    }

    @Override
    public DictItemEntity getDictItemByCode(String dictCode, String itemCode, Integer itemValue) {
        return dictItemDao.getDictItemByCode(dictCode, itemCode, itemValue);
    }

    @Override
    public Result saveDictItem(DictItemEntity dictItemEntity) {
        List<DictItemEntity> itemEntityList = dictItemDao.getDictItemByCode(dictItemEntity.getDictCode(), dictItemEntity.getItemName(), dictItemEntity.getItemCode());
        if (!CollectionUtils.isEmpty(itemEntityList)) {
            if (dictItemEntity.getItemCode().equals(itemEntityList.get(0).getItemCode())) {
                return Result.failure(ResultCodeEnum.CONFIG_LKG_CODE_EXIST);
            } else {
                return Result.failure(ResultCodeEnum.CONFIG_LKG_NAME_EXIST);
            }
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        dictItemEntity.setItemDescription(dictItemEntity.getItemName())
                .setItemSort(0)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        dictItemDao.save(dictItemEntity);
        //todo:保存客户标签
        if (!CollectionUtils.isEmpty(dictItemEntity.getCustomerCodeList())) {
            customerFacade.updateCustomerTemplateVip(dictItemEntity.getItemCode(), dictItemEntity.getCustomerCodeList());
        }
        return Result.success();
    }

    @Override
    public Result updateDictItem(DictItemEntity dictItemEntity) {
        //根据dictCode、itemCode判断是否有重复
        List<DictItemEntity> itemEntityList = dictItemDao.getDictItemByCode(dictItemEntity.getDictCode(), dictItemEntity.getItemName(), dictItemEntity.getItemCode());
        if (!CollectionUtils.isEmpty(itemEntityList)) {
            itemEntityList = itemEntityList.stream().filter(it -> {
                return !it.getId().equals(dictItemEntity.getId());
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(itemEntityList)) {
                if (dictItemEntity.getItemCode().equals(itemEntityList.get(0).getItemCode())) {
                    return Result.failure(ResultCodeEnum.CONFIG_LKG_CODE_EXIST);
                } else {
                    return Result.failure(ResultCodeEnum.CONFIG_LKG_NAME_EXIST);
                }
            }
        }
        DictItemEntity itemEntity = dictItemDao.getById(dictItemEntity.getId());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        itemEntity.setDictCode(dictItemEntity.getDictCode().trim())
                .setDictName(dictItemEntity.getDictName().trim())
                .setItemName(dictItemEntity.getItemName().trim())
                .setItemDescription(dictItemEntity.getItemName().trim())
                .setStatus(dictItemEntity.getStatus())
                .setItemSort(0)
                .setUpdatedAt(DateTimeUtil.now())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setUpdatedBy(name);
        dictItemDao.updateById(itemEntity);

        if (!CollectionUtils.isEmpty(dictItemEntity.getCustomerCodeList())) {
            customerFacade.updateCustomerTemplateVip(dictItemEntity.getItemCode(), dictItemEntity.getCustomerCodeList());
        }
        return Result.success();
    }
}
