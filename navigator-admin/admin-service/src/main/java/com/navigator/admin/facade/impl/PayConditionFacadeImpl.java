package com.navigator.admin.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.service.IPayConditionService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 付款条件对外暴露的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@RestController
public class PayConditionFacadeImpl implements PayConditionFacade {
    @Resource
    private IPayConditionService payConditionService;

    @Override
    public Result<List<PayConditionEntity>> queryPayCondition(PayConditionDTO payConditionDTO) {
        return Result.success(payConditionService.queryPayCondition(payConditionDTO));
    }

    @Override
    public Result<PayConditionEntity> getPayConditionById(Integer payConditionId) {
        return Result.success(payConditionService.getPayConditionById(payConditionId));
    }

    @Override
    public Result getPayConditionList(QueryDTO<PayConditionDTO> queryDTO) {
        IPage<PayConditionEntity> payConditionPage = payConditionService.getPayConditionList(queryDTO);
        return Result.page(payConditionPage);
    }

    @Override
    public Result<Boolean> updatePayCondition(PayConditionDTO payConditionDTO) {
        return Result.success(payConditionService.updatePayCondition(payConditionDTO));
    }

    @Override
    public Result<String> importPayCondition(MultipartFile file) {
        return Result.success(payConditionService.importPayCondition(file));
    }

    @Override
    public Result<Boolean> addPayCondition(PayConditionDTO payConditionDTO) {
        return Result.success(payConditionService.addPayCondition(payConditionDTO));
    }

}
