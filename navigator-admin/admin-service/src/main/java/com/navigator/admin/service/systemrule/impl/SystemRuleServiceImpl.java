package com.navigator.admin.service.systemrule.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.BasicPriceConfigDao;
import com.navigator.admin.dao.ProteinPriceConfigDao;
import com.navigator.admin.dao.SystemRuleDao;
import com.navigator.admin.dao.SystemRuleItemDao;
import com.navigator.admin.pojo.dto.ContractApproveConfigItemDTO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.systemrule.*;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.admin.service.systemrule.BasicPriceGoodsAttributeConfigService;
import com.navigator.admin.service.systemrule.BasicPriceGoodsConfigService;
import com.navigator.admin.service.systemrule.SystemRuleService;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.pojo.enums.ContractApproveConfigItemEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum.FIRST_TYPE;

@Service
@Slf4j
public class SystemRuleServiceImpl implements SystemRuleService {

    @Autowired
    private SystemRuleDao systemRuleDao;

    @Autowired
    private SystemRuleItemDao systemRuleItemDao;

    @Autowired
    private IEmployService employService;

    @Autowired
    private BasicPriceConfigDao basicPriceConfigDao;

    @Autowired
    private ProteinPriceConfigDao proteinPriceConfigDao;
    @Autowired
    private BasicPriceGoodsAttributeConfigService basicPriceGoodsAttributeConfigService;
    @Autowired
    private BasicPriceGoodsConfigService basicPriceGoodsConfigService;
    @Autowired
    protected IOperationDetailService operationDetailService;
    @Autowired
    private AttributeFacade attributeFacade;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private SkuFacade skuFacade;

    @Override
    public List<SystemRuleVO> getSystemRule(SystemRuleDTO systemRuleDTO) {

        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(systemRuleDTO.getCategoryId(), systemRuleDTO.getRuleCode());
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            return new ArrayList<>();
        }
        List<Integer> idList = systemRuleEntityList.stream().map(SystemRuleEntity::getId).collect(Collectors.toList());
        SystemRuleEntity systemRuleEntity = systemRuleEntityList.get(0);
        Integer id = systemRuleEntity.getId();

        //判断配置查询类型
        if (FIRST_TYPE.getType().equals(SystemCodeConfigEnum.getTypeByRuleCode(systemRuleDTO.getRuleCode()))) {
            //查询单层级配置
            List<SystemRuleItemEntity> systemRuleItemList = systemRuleItemDao.querySystemRuleItemListByType(idList, DisableStatusEnum.ENABLE.getValue(), systemRuleDTO.getSalesType());

            List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = systemRuleItemList.stream().map(k -> new SystemRuleVO.SystemRuleItemVO()
                    .setRuleItemId(k.getId())
                    .setRuleItemKey(k.getRuleKey())
                    .setRuleItemValue(k.getRuleValue())
                    .setMemo(k.getMemo())
                    .setCompanyIds(k.getCompanyId())
            ).collect(Collectors.toList());

            SystemRuleVO systemRuleVO = new SystemRuleVO()
                    .setSystemRuleItemVOList(systemRuleItemVOList);

            List<SystemRuleVO> systemRuleVOList = new ArrayList<>();
            systemRuleVOList.add(systemRuleVO);
            return systemRuleVOList;
        } else {
            //查询双层级配置
            List<SystemRuleEntity> systemRuleList = systemRuleDao.querySystemRuleListByParentId(id);

            return systemRuleList.stream().map(i -> {
                List<SystemRuleItemEntity> systemRuleItemList = systemRuleItemDao.querySystemRuleItemListByType(Arrays.asList(i.getId()), DisableStatusEnum.ENABLE.getValue(), systemRuleDTO.getSalesType());

                List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = systemRuleItemList.stream()
                        .map(k -> new SystemRuleVO.SystemRuleItemVO()
                                .setRuleItemId(k.getId())
                                .setRuleItemKey(k.getRuleKey())
                                .setRuleItemValue(k.getRuleValue()))
                        .collect(Collectors.toList());

                return new SystemRuleVO()
                        .setRuleName(i.getName())
                        .setSystemRuleItemVOList(systemRuleItemVOList);

            }).collect(Collectors.toList());
        }
    }

    @Override
    public SystemRuleItemEntity getRuleItemById(int id) {
        SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.getById(id);
        return systemRuleItemEntity;
    }

    @Override
    public SystemRuleVO querySystemRuleDetail(SystemRuleDTO systemRuleDTO) {
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(systemRuleDTO.getCategoryId(), systemRuleDTO.getRuleCode());
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            return null;
        }
        List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList = new ArrayList<>();
        //判断配置查询类型
        if (FIRST_TYPE.getType().equals(SystemCodeConfigEnum.getTypeByRuleCode(systemRuleDTO.getRuleCode()))) {
            //查询单层级配置
            this.getSingleRuleItemInfo(systemRuleDTO, systemRuleItemVOList, systemRuleEntityList);
        } else {
            //查询双层级配置
            this.getDoubleRuleItemInfo(systemRuleDTO, systemRuleEntityList, systemRuleItemVOList);
        }
        //根据状态倒序，ID倒序排序
        systemRuleItemVOList.sort(Comparator.comparing(SystemRuleVO.SystemRuleItemVO::getStatus).reversed().thenComparing(SystemRuleVO.SystemRuleItemVO::getRuleItemId).reversed());
        return new SystemRuleVO()
                .setRuleId(systemRuleEntityList.get(0).getId())
                .setRuleCode(systemRuleEntityList.get(0).getCode())
                .setRuleName(systemRuleEntityList.get(0).getName())
                .setSystemRuleItemVOList(systemRuleItemVOList);
    }

    private void getDoubleRuleItemInfo(SystemRuleDTO systemRuleDTO, List<SystemRuleEntity> systemRuleEntityList, List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList) {
        for (SystemRuleEntity systemRuleEntity : systemRuleEntityList) {
            List<SystemRuleEntity> systemRuleList = systemRuleDao.querySystemRuleListByParentId(systemRuleEntity.getId());
            String category2Name = this.getCategoryName(systemRuleEntity.getCategoryId());
            for (SystemRuleEntity ruleEntity : systemRuleList) {
                //查询单层级配置
                List<SystemRuleItemEntity> systemRuleItemList = systemRuleItemDao.querySystemRuleItemList(ruleEntity.getId(), systemRuleDTO.getStatus());
                List<SystemRuleVO.SystemRuleItemVO> systemRuleItemDetailList = systemRuleItemList.stream()
                        .map(k -> new SystemRuleVO.SystemRuleItemVO()
                                .setRuleItemId(k.getId())
                                .setRuleItemKey(k.getRuleKey())
                                .setRuleItemValue(k.getRuleValue())
                                .setRuleId(k.getRuleId())
                                .setRuleCode(k.getCode())
                                .setLkgCode(k.getLkgCode())
                                .setMemo(k.getMemo())
                                .setValueType(k.getValueType())
                                .setStatus(k.getStatus())
                                .setCreatedAt(k.getCreatedAt())
                                .setUpdatedAt(k.getUpdatedAt())
                                .setCreatedBy(null == k.getCreatedBy() ? "" : employService.getEmployById(k.getCreatedBy()).getName())
                                .setUpdatedBy(null == k.getUpdatedBy() ? "" : employService.getEmployById(k.getUpdatedBy()).getName())
                                .setCategoryName(category2Name)
                        )
                        .collect(Collectors.toList());
                systemRuleItemVOList.addAll(systemRuleItemDetailList);
            }
        }
    }

    private void getSingleRuleItemInfo(SystemRuleDTO systemRuleDTO, List<SystemRuleVO.SystemRuleItemVO> systemRuleItemVOList, List<SystemRuleEntity> systemRuleEntityList) {
        for (SystemRuleEntity systemRuleEntity : systemRuleEntityList) {
            //查询单层级配置
            List<SystemRuleItemEntity> systemRuleItemList = systemRuleItemDao.querySystemRuleItemListByType(Arrays.asList(systemRuleEntity.getId()), systemRuleDTO.getStatus(), systemRuleDTO.getSalesType());
            String categoryName = GoodsCategoryEnum.getByValue(systemRuleEntity.getCategoryId()).getDesc();
            List<SystemRuleVO.SystemRuleItemVO> systemRuleItemDetailList = systemRuleItemList.stream()
                    .map(k -> {
                                SystemRuleVO.SystemRuleItemVO systemRuleItemVO = new SystemRuleVO.SystemRuleItemVO()
                                        .setRuleItemId(k.getId())
                                        .setRuleId(k.getRuleId())
                                        .setRuleCode(k.getCode())
                                        .setRuleItemKey(k.getRuleKey())
                                        .setRuleItemValue(k.getRuleValue())
                                        .setValueType(k.getValueType())
                                        .setMemo(k.getMemo())
                                        .setLkgCode(k.getLkgCode())
                                        .setStatus(k.getStatus())
                                        .setCreatedAt(k.getCreatedAt())
                                        .setUpdatedAt(k.getUpdatedAt())
                                        .setCreatedBy(null == k.getCreatedBy() ? "" : employService.getEmployById(k.getCreatedBy()).getName())
                                        .setUpdatedBy(null == k.getUpdatedBy() ? "" : employService.getEmployById(k.getUpdatedBy()).getName())
                                        .setCategoryName(categoryName);


                                if (!"0".equals(k.getCompanyId()) && StringUtil.isNotEmpty(k.getCompanyId())) {
                                    List<Integer> companyIds = Stream.of(k.getCompanyId().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                                    StringBuffer buff = new StringBuffer();
                                    Integer num = 0;
                                    for (Integer companyId : companyIds) {
                                        num++;
                                        CompanyEntity companyEntity = companyService.queryCompanyById(companyId);
                                        buff.append(companyEntity.getShortName());

                                        if (num < companyIds.size()) {
                                            buff.append(",");
                                        }
                                    }
                                    systemRuleItemVO
                                            .setCompanyId(companyIds)
                                            .setCompanyName(buff.toString());
                                }

                                //基差基准价
                                if (SystemCodeConfigEnum.EXTRA_BASIC_PRICE.getRuleCode().equals(systemRuleDTO.getRuleCode())) {
                                    List<BasicPriceConfigEntity> priceConfigEntities = basicPriceConfigDao.getBasicPriceListByRuleItemId(k.getId());
                                    if (!CollectionUtils.isEmpty(priceConfigEntities)) {
                                        List<String> factoryCodeList = priceConfigEntities.stream().map(BasicPriceConfigEntity::getFactoryCode).distinct().collect(Collectors.toList());
                                        List<String> deliveryBeginDateList = priceConfigEntities.stream().map(BasicPriceConfigEntity::getDeliveryBeginDate).distinct().collect(Collectors.toList());

                                        systemRuleItemVO.setFactoryCodeList(factoryCodeList)
                                                .setDeliveryBeginDateList(deliveryBeginDateList);

                                        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(systemRuleEntity.getCategoryId()) && StringUtils.isNotBlank(k.getGoodsId())) {
                                            //List<BasicPriceGoodsAttributeConfigEntity> basicPriceGoodsAttributeConfigEntities = basicPriceGoodsAttributeConfigService.queryBasicPriceGoodsAttributeConfigByAttributeValueId(Integer.parseInt(k.getGoodsId()));

                                            BasicPriceGoodsConfigEntity basicPriceGoodsConfigEntity = basicPriceGoodsConfigService.queryBasicPriceGoodsConfigById(Integer.parseInt(k.getGoodsId()));
                                            systemRuleItemVO.setGoodsId(basicPriceGoodsConfigEntity.getId())
                                                    .setGoodsName(basicPriceGoodsConfigEntity.getName());
                                        }
                                    }
                                }


                                if (SystemCodeConfigEnum.PROTEIN_PRICE_CONFIG.getRuleCode().equals(systemRuleDTO.getRuleCode())) {

                                    List<ProteinPriceConfigEntity> priceConfigEntities = proteinPriceConfigDao.getBasicProteinListByRuleItemId(k.getId());
                                    if (!CollectionUtils.isEmpty(priceConfigEntities)) {
                                        List<String> factoryCodeList = priceConfigEntities.stream().map(ProteinPriceConfigEntity::getFactoryCode).distinct().collect(Collectors.toList());
                                        List<String> deliveryBeginDateList = priceConfigEntities.stream().map(ProteinPriceConfigEntity::getDeliveryBeginDate).distinct().collect(Collectors.toList());

                                        if (StringUtils.isNotBlank(k.getRuleKey())) {
                                            AttributeValueEntity attributeValueEntity = attributeFacade.getAttributeValueById(Integer.parseInt(k.getRuleKey()));

                                            systemRuleItemVO.setFactoryCodeList(factoryCodeList)
                                                    .setDeliveryBeginDateList(deliveryBeginDateList)
                                                    .setAttributeValueId(attributeValueEntity.getId())
                                                    .setAttributeValueName(attributeValueEntity.getName())
                                            ;
                                        }
                                    }
                                }
                                return systemRuleItemVO;
                            }
                    )
                    .collect(Collectors.toList());
            systemRuleItemVOList.addAll(systemRuleItemDetailList);
        }
    }

    @Override
    public List<SystemRuleVO> getNextSystemRule(SystemRuleDTO systemRuleDTO) {
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(systemRuleDTO.getCategoryId(), systemRuleDTO.getRuleCode());
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            return null;
        }
        SystemRuleEntity systemRuleEntity = systemRuleEntityList.get(0);
        List<SystemRuleEntity> systemRuleList = systemRuleDao.querySystemRuleListByParentId(systemRuleEntity.getId());
        return systemRuleList.stream()
                .map(k -> {
                    return new SystemRuleVO()
                            .setRuleId(k.getId())
                            .setRuleName(k.getName())
                            .setRuleCode(k.getCode())
                            .setCategoryId(k.getCategoryId());
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSystemRule(SystemRuleCreateDTO ruleCreateDTO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));
        boolean operationResult = true;

        if (SystemCodeConfigEnum.EXTRA_BASIC_PRICE.getRuleCode().equals(ruleCreateDTO.getRuleCode()) && !ruleCreateDTO.getGoodsId().isEmpty()) {
            for (Integer goodsId : ruleCreateDTO.getGoodsId()) {
                operationResult = saveOrUpdateSystemRule(ruleCreateDTO, currentUserId, name, goodsId);
            }
        } else if (SystemCodeConfigEnum.PROTEIN_PRICE_CONFIG.getRuleCode().equals(ruleCreateDTO.getRuleCode())) {

            for (Integer attributeValueId : ruleCreateDTO.getAttributeValueIds()) {
                ruleCreateDTO.setRuleItemKey(String.valueOf(attributeValueId));
                operationResult = saveOrUpdateSystemRule(ruleCreateDTO, currentUserId, name, null);
            }
        } else {
            operationResult = saveOrUpdateSystemRule(ruleCreateDTO, currentUserId, name, null);
        }

        return operationResult;
    }


    public boolean saveOrUpdateSystemRule(SystemRuleCreateDTO ruleCreateDTO, String currentUserId, String name, Integer goodsId) {

        boolean operationResult;

        SystemRuleItemEntity ruleItemEntity;
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(ruleCreateDTO.getCategoryId(), ruleCreateDTO.getRuleCode());
        if (!CollectionUtils.isEmpty(systemRuleEntityList)) {
            SystemRuleEntity systemRuleEntity = systemRuleEntityList.get(0);

            //校验基差基准价（同一品类同一合约、工厂、月份只能有一个）
            if (SystemCodeConfigEnum.EXTRA_BASIC_PRICE.getRuleCode().equals(ruleCreateDTO.getRuleCode())) {
                // SystemRuleEntity systemRuleEntity = systemRuleDao.querySystemRule(ruleCreateDTO.getCategoryId(), ruleCreateDTO.getRuleCode()).get(0);
                List<SystemRuleItemEntity> basicPriceList = systemRuleItemDao.querySystemRuleItemList(systemRuleEntity.getId(),
                        ruleCreateDTO.getRuleItemKey(),
                        String.join(",", ruleCreateDTO.getCompanyId()),
                        goodsId
                );
                List<Integer> basicPriceItemIdList = basicPriceList.stream().map(SystemRuleItemEntity::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(basicPriceItemIdList)) {
                    if (null != ruleCreateDTO.getRuleItemId()) {
                        basicPriceItemIdList.remove(ruleCreateDTO.getRuleItemId());
                    }
                    List<BasicPriceConfigEntity> basicPriceConfigEntityList = basicPriceConfigDao.getBasicPriceListByRules(basicPriceItemIdList,
                            ruleCreateDTO.getFactoryCodeList(),
                            ruleCreateDTO.getDeliveryBeginDateList(),
                            goodsId
                    );
                    if (!CollectionUtils.isEmpty(basicPriceConfigEntityList)) {
                        String duplicateContent = "";
                        for (BasicPriceConfigEntity basicPriceConfigEntity : basicPriceConfigEntityList) {
                            duplicateContent = duplicateContent + basicPriceConfigEntity.getFactoryCode() + "-" + basicPriceConfigEntity.getDeliveryBeginDate() + ";";
                        }
                        throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXTRA_PRICE_UNIQUE, duplicateContent + "重复）");
                    }
                }
            } else if (SystemCodeConfigEnum.PROTEIN_PRICE_CONFIG.getRuleCode().equals(ruleCreateDTO.getRuleCode())) {


                //查询业务配置id
                // SystemRuleEntity systemRuleEntity = systemRuleDao.querySystemRule(ruleCreateDTO.getCategoryId(), ruleCreateDTO.getRuleCode()).get(0);
                Integer ruleId = systemRuleEntity.getId();

                if (null != ruleCreateDTO.getRuleItemId()) {
                    //SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.getById(ruleCreateDTO.getRuleItemId());

                    if (ruleCreateDTO.getAttributeValueIds().indexOf(Integer.parseInt(ruleCreateDTO.getRuleItemKey())) != 0) {
                        ruleCreateDTO.setRuleItemId(null);
                    }
                }

                List<SystemRuleItemEntity> proteinPriceList = systemRuleItemDao.querySystemRuleItemList(ruleId,
                        ruleCreateDTO.getRuleItemKey(),
                        String.join(",", ruleCreateDTO.getCompanyId()),
                        null
                );

                List<Integer> proteinPriceItemIdList = proteinPriceList.stream().map(SystemRuleItemEntity::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(proteinPriceItemIdList)) {
                    if (null != ruleCreateDTO.getRuleItemId()) {
                        proteinPriceItemIdList.remove(ruleCreateDTO.getRuleItemId());
                    }
                /*List<ProteinPriceConfigEntity> proteinPriceConfigEntities = proteinPriceConfigDao.getBasicProteinListByRules(proteinPriceItemIdList,
                        ruleCreateDTO.getFactoryCodeList(),
                        ruleCreateDTO.getDeliveryBeginDateList(),
                        Integer.parseInt(ruleCreateDTO.getRuleItemKey())
                );
                if (!CollectionUtils.isEmpty(proteinPriceConfigEntities)) {
                    String duplicateContent = "";
                    for (ProteinPriceConfigEntity proteinPriceConfigEntity : proteinPriceConfigEntities) {
                        duplicateContent = duplicateContent + proteinPriceConfigEntity.getFactoryCode() + "-" + proteinPriceConfigEntity.getDeliveryBeginDate() + ";";
                    }
                    throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_PROTEIN_PRICE_UNIQUE, duplicateContent + "重复）");
                }*/
                }
            }
        }


        if (null == ruleCreateDTO.getRuleItemId()) {
            //组装配置数据信息
            ruleItemEntity = this.assemblySystemRuleInfo(ruleCreateDTO);
            ruleItemEntity.setUpdatedBy(Integer.parseInt(currentUserId))
                    .setCompanyId(CollectionUtils.isEmpty(ruleCreateDTO.getCompanyId()) ? null : String.join(",", ruleCreateDTO.getCompanyId()))
                    .setUpdatedByName(name)
                    .setGoodsId(null == goodsId ? null : goodsId.toString())
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name);
            operationResult = systemRuleItemDao.save(ruleItemEntity);
        } else {
            ruleItemEntity = systemRuleItemDao.getById(ruleCreateDTO.getRuleItemId());
            if (SystemCodeConfigEnum.getLkgCodeConfigList().contains(ruleCreateDTO.getRuleCode())) {
                SystemRuleItemEntity lkgCodeRuleItem = systemRuleItemDao.findByLkgCodeNotId(ruleItemEntity.getId(), ruleCreateDTO.getLkgCode().trim(), ruleCreateDTO.getRuleItemId());
                if (null != lkgCodeRuleItem) {
                    throw new BusinessException(ResultCodeEnum.CONFIG_LKG_CODE_EXIST);
                }
            }
            ruleItemEntity.setRuleId(null != ruleCreateDTO.getRuleId() ? ruleCreateDTO.getRuleId() : ruleItemEntity.getRuleId())
                    .setCode(systemRuleDao.getById(ruleItemEntity.getRuleId()).getName().trim())
                    .setRuleKey(StringUtils.isBlank(ruleCreateDTO.getRuleItemKey()) ? "" : ruleCreateDTO.getRuleItemKey().trim())
                    .setValueType(null == ruleCreateDTO.getValueType() ? 2 : ruleCreateDTO.getValueType())
                    .setRuleValue(StringUtils.isBlank(ruleCreateDTO.getRuleItemValue()) ? "" : ruleCreateDTO.getRuleItemValue().trim())
                    .setLkgCode(StringUtils.isBlank(ruleCreateDTO.getLkgCode()) ? "" : ruleCreateDTO.getLkgCode().trim())
                    .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                    .setUpdatedByName(name)
                    .setUpdatedAt(DateTimeUtil.now())
                    .setGoodsId(null == goodsId ? null : goodsId.toString())
                    .setCompanyId(CollectionUtils.isEmpty(ruleCreateDTO.getCompanyId()) ? null : String.join(",", ruleCreateDTO.getCompanyId()))
                    .setMemo(StringUtils.isBlank(ruleCreateDTO.getMemo()) ? "" : ruleCreateDTO.getMemo().trim());
            operationResult = systemRuleItemDao.updateById(ruleItemEntity);
        }
        // 基差基准价,记录品类-工厂-合约-开始交货月份 关联关系
        if (SystemCodeConfigEnum.EXTRA_BASIC_PRICE.getRuleCode().equals(ruleCreateDTO.getRuleCode())) {
            basicPriceConfigDao.recordBasicPriceConfig(ruleCreateDTO.getFactoryCodeList(),
                    ruleCreateDTO.getDeliveryBeginDateList(),
                    ruleItemEntity,
                    ruleCreateDTO.getCategoryId(),
                    goodsId,
                    String.join(",", ruleCreateDTO.getCompanyId()),
                    null == ruleCreateDTO.getRuleItemId());
        }

        // 蛋白价差,记录品类-工厂-合约-开始交货月份 关联关系
        if (SystemCodeConfigEnum.PROTEIN_PRICE_CONFIG.getRuleCode().equals(ruleCreateDTO.getRuleCode())) {
            proteinPriceConfigDao.recordBasicProteinConfig(
                    ruleCreateDTO.getFactoryCodeList(),
                    ruleCreateDTO.getDeliveryBeginDateList(),
                    ruleItemEntity,
                    ruleCreateDTO.getCategoryId(),
                    String.join(",", ruleCreateDTO.getCompanyId()),
                    null == ruleCreateDTO.getRuleItemId());
        }
        String category2Name = "";
        if (null != ruleCreateDTO.getCategoryId()) {
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(ruleCreateDTO.getCategoryId());
            if (null != categoryEntity) {
                category2Name = categoryEntity.getName();
            }
        }

        if (StringUtils.isNotBlank(ruleCreateDTO.getRuleCode())) {
            OperationActionEnum operationActionEnum = OperationActionEnum.getByName(ruleCreateDTO.getRuleItemKey());
            try {
                RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
                recordOperationDetail
                        .setDtoData(JSON.toJSONString(ruleCreateDTO))
                        .setBeforeData(null)
                        .setAfterData(null)
                        .setOperationActionEnum(operationActionEnum)
                        .setReferBizId(ruleItemEntity.getId())
                        .setReferBizCode(ruleItemEntity.getCode())
                        .setCategory2Name(category2Name)
                ;
                operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return operationResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInvoiceType(Integer categoryId, List<InvoiceTypeDTO> invoiceTypeDTOList) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));
        String ruleCode = SystemCodeConfigEnum.INVOICE_TYPE.getRuleCode();
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(categoryId, ruleCode);
        SystemRuleEntity systemRuleEntity;
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            systemRuleEntity = new SystemRuleEntity().setCategoryId(categoryId)
                    .setCode(ruleCode)
                    .setName(SystemCodeConfigEnum.INVOICE_TYPE.getName())
                    .setSort(0)
                    .setStatus(DisableStatusEnum.ENABLE.getValue());
            systemRuleDao.save(systemRuleEntity);
        } else {
            systemRuleEntity = systemRuleEntityList.get(0);
        }
        //作废所有发票类型
        systemRuleItemDao.dropItemByRuleId(systemRuleEntity.getId());
        for (InvoiceTypeDTO invoiceTypeDTO : invoiceTypeDTOList) {
            SystemRuleItemEntity systemRuleItem = systemRuleItemDao.queryRuleItemByValue(systemRuleEntity.getId(), invoiceTypeDTO.getInvoiceType().toString(), invoiceTypeDTO.getTaxRate().toString(), DisableStatusEnum.ENABLE.getValue());
            if (null == invoiceTypeDTO.getId()) {
                if (null != systemRuleItem) {
                    continue;
                }
                SystemRuleItemEntity itemEntity = new SystemRuleItemEntity()
                        .setRuleId(systemRuleEntity.getId())
                        .setCode(ruleCode)
                        .setRuleKey(invoiceTypeDTO.getInvoiceType().toString())
                        .setRuleValue(invoiceTypeDTO.getTaxRate().toString())
                        .setValueType(2)
                        .setLkgCode(StringUtils.isBlank(invoiceTypeDTO.getLkgCode()) ? "" : invoiceTypeDTO.getLkgCode())
                        .setMemo(invoiceTypeDTO.getTaxRate().toString() + "%," + InvoiceTypeEnum.getDescByValue(invoiceTypeDTO.getInvoiceType()))
                        .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                        .setCreatedByName(name)
                        .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                        .setUpdatedByName(name)
                        .setStatus(DisableStatusEnum.ENABLE.getValue());
                systemRuleItemDao.save(itemEntity);
            } else {
                if (null != systemRuleItem) {
                    throw new BusinessException(ResultCodeEnum.INVOICE_TYPE_REPEAT);
                }
                SystemRuleItemEntity ruleItemEntity = systemRuleItemDao.getById(invoiceTypeDTO.getId());
                if (null != ruleItemEntity) {
                    ruleItemEntity.setRuleId(systemRuleEntity.getId())
                            .setCode(ruleCode)
                            .setRuleKey(invoiceTypeDTO.getInvoiceType().toString())
                            .setRuleValue(invoiceTypeDTO.getTaxRate().toString())
                            .setValueType(2)
                            .setLkgCode(StringUtils.isBlank(invoiceTypeDTO.getLkgCode()) ? "" : invoiceTypeDTO.getLkgCode())
                            .setMemo(invoiceTypeDTO.getTaxRate().toString() + "%," + InvoiceTypeEnum.getDescByValue(invoiceTypeDTO.getInvoiceType()))
                            .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                            .setUpdatedByName(name)
                            .setStatus(DisableStatusEnum.ENABLE.getValue());
                    systemRuleItemDao.updateById(ruleItemEntity);
                } else {
                    return false;
                }
            }
        }

        return true;
    }

    private String getCategoryName(Integer categorySerialNo) {
        String category2Name = "通用";
        if (null != categorySerialNo && categorySerialNo > 0) {
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(categorySerialNo);
            category2Name = null != categoryEntity ? categoryEntity.getName() : "";
        }
        return category2Name;
    }

    private SystemRuleItemEntity assemblySystemRuleInfo(SystemRuleCreateDTO ruleCreateDTO) {
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(ruleCreateDTO.getCategoryId(), ruleCreateDTO.getRuleCode());
        SystemRuleEntity systemRuleEntity = new SystemRuleEntity();
        String category2Name = this.getCategoryName(ruleCreateDTO.getCategoryId());

        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            systemRuleEntity.setCategoryId(ruleCreateDTO.getCategoryId())
                    .setCode(ruleCreateDTO.getRuleCode())
                    .setName(StringUtils.isNotBlank(ruleCreateDTO.getRuleName()) ? ruleCreateDTO.getRuleName() :
                            SystemCodeConfigEnum.getNameByRuleCode(ruleCreateDTO.getRuleCode()) + "_" + category2Name)
                    .setMemo(StringUtils.isNotBlank(ruleCreateDTO.getRuleName()) ? ruleCreateDTO.getRuleName() :
                            SystemCodeConfigEnum.getNameByRuleCode(ruleCreateDTO.getRuleCode()) + "_" + category2Name)
                    .setSort(0)
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now());
            systemRuleDao.save(systemRuleEntity);
        } else {
            systemRuleEntity = systemRuleEntityList.get(0);
        }
        if (SystemCodeConfigEnum.getLkgCodeConfigList().contains(ruleCreateDTO.getRuleCode())) {
            SystemRuleItemEntity lkgCodeRuleItem = systemRuleItemDao.findByLkgCode(systemRuleEntity.getId(), ruleCreateDTO.getLkgCode().trim(), null);
            if (null != lkgCodeRuleItem) {
                throw new BusinessException(ResultCodeEnum.CONFIG_LKG_CODE_EXIST);
            }
        }
        // 易企签配置只能配置一条数据
        if (SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode().equals(systemRuleEntity.getCode())) {
            List<SystemRuleItemEntity> yyqItemEntities = systemRuleItemDao.querySystemRuleItemList(ruleCreateDTO.getRuleId(), DisableStatusEnum.ENABLE.getValue());
            if (!CollectionUtils.isEmpty(yyqItemEntities)) {
                throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_YYQ_UNIQUE);
            }
        }
        return new SystemRuleItemEntity()
                .setRuleId(null != ruleCreateDTO.getRuleId() ? ruleCreateDTO.getRuleId() : systemRuleEntity.getId())
                .setCode(null != ruleCreateDTO.getRuleId() ? systemRuleDao.getById(ruleCreateDTO.getRuleId()).getName() : systemRuleEntity.getName())
                .setRuleKey(ruleCreateDTO.getRuleItemKey())
                .setRuleValue(ruleCreateDTO.getRuleItemValue())
                .setValueType(null == ruleCreateDTO.getValueType() ? 2 : ruleCreateDTO.getValueType())
                .setMemo(ruleCreateDTO.getMemo())
                .setLkgCode(StringUtils.isBlank(ruleCreateDTO.getLkgCode()) ? "" : ruleCreateDTO.getLkgCode().trim())
                .setCreatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setStatus(DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public boolean invalidStatus(Integer ruleItemId) {
        SystemRuleItemEntity ruleItemEntity = systemRuleItemDao.getById(ruleItemId);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));
        if (null == ruleItemEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        SystemRuleItemEntity systemRuleItemEntity = new SystemRuleItemEntity();
        BeanUtils.copyProperties(ruleItemEntity, systemRuleItemEntity);
        Integer newStatus = DisableStatusEnum.ENABLE.getValue().equals(ruleItemEntity.getStatus()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();
        SystemRuleEntity systemRuleEntity = systemRuleDao.getById(ruleItemEntity.getRuleId());
        //基差基准价只能配置一条数据
        if (DisableStatusEnum.ENABLE.getValue().equals(newStatus)) {
            //易企签
            if (SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode().equals(systemRuleEntity.getCode())) {
                List<SystemRuleItemEntity> ruleItemEntities = systemRuleItemDao.querySystemRuleItemList(ruleItemEntity.getRuleId(), DisableStatusEnum.ENABLE.getValue());
                if (!CollectionUtils.isEmpty(ruleItemEntities)) {
                    throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXTRA_PRICE_UNIQUE);
                }
            }
            //基差基准价只能配置一条有效数据
//            List<SystemRuleItemEntity> basicPriceList = systemRuleItemDao.querySystemRuleValueItemList(ruleItemEntity.getRuleId(), ruleItemEntity.getRuleKey(), ruleItemEntity.getRuleValue(), ruleItemEntity.getMemo());
//            if (!CollectionUtils.isEmpty(basicPriceList)) {
//                throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXTRA_PRICE_UNIQUE);
//            }
//            if (SystemCodeConfigEnum.EXTRA_BASIC_PRICE.getRuleCode().equals(systemRuleEntity.getCode())) {
//                List<SystemRuleItemEntity> basicPriceList = systemRuleItemDao.querySystemRuleItemList(ruleItemEntity.getRuleId(), DisableStatusEnum.ENABLE.getValue());
//                if (!CollectionUtils.isEmpty(basicPriceList)) {
//                    basicPriceList = basicPriceList.stream()
//                            .filter(it -> !StringUtils.isBlank(it.getRuleKey()) &&
//                                    it.getRuleKey().equals(ruleItemEntity.getRuleKey()) &&
//                                    !ruleItemId.equals(it.getId())
//                            )
//                            .collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(basicPriceList)) {
//                        throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXTRA_PRICE_UNIQUE);
//                    }
//                }
//            }
        }
        ruleItemEntity.setStatus(newStatus)
                .setUpdatedBy(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setUpdatedByName(name)
                .setUpdatedAt(new Date())
        ;
        boolean status = systemRuleItemDao.updateById(ruleItemEntity);

        try {

            SystemCodeConfigEnum systemCodeConfigEnum = SystemCodeConfigEnum.getEnumByRuleCode(systemRuleEntity.getCode());
            OperationActionEnum operationActionEnum = OperationActionEnum.getByName(systemCodeConfigEnum.getName());

            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(ruleItemId))
                    .setBeforeData(JSON.toJSONString(systemRuleItemEntity))
                    .setAfterData(JSON.toJSONString(ruleItemEntity))
                    .setOperationActionEnum(operationActionEnum)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return status;
    }

    @Override
    public SystemRuleItemEntity filterBasicPrice(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {

        if (StringUtil.isEmpty(basicPriceConfigQueryDTO.getCompanyId())
                || StringUtil.isEmpty(basicPriceConfigQueryDTO.getDomainCode())
                || StringUtil.isEmpty(basicPriceConfigQueryDTO.getFactoryCode())
                || null == basicPriceConfigQueryDTO.getCategoryId()) {
            return null;
        }

        if (null != basicPriceConfigQueryDTO.getGoodsId()) {
            SkuDTO skuDTO = skuFacade.getSkuDTOById(basicPriceConfigQueryDTO.getGoodsId());
            if (null != skuDTO) {
                if (!CollectionUtils.isEmpty(skuDTO.getSpecAttributeValueList())) {
                    basicPriceConfigQueryDTO.setAttributeValueId(skuDTO.getSpecAttributeValueList().get(0).getAttributeValueId());
                }
            }
        }

        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(basicPriceConfigQueryDTO.getCategoryId())) {
            List<BasicPriceGoodsAttributeConfigEntity> basicPriceGoodsAttributeConfigEntities = basicPriceGoodsAttributeConfigService.queryBasicPriceGoodsAttributeConfigByAttributeValueId(basicPriceConfigQueryDTO.getAttributeValueId());

            if (!CollectionUtils.isEmpty(basicPriceGoodsAttributeConfigEntities)) {
                basicPriceConfigQueryDTO.setGoodsId(basicPriceGoodsAttributeConfigEntities.get(0).getPriceGoodsConfigId());
            }
        }

        List<BasicPriceConfigEntity> priceConfigEntities = basicPriceConfigDao.filterBasicPrice(basicPriceConfigQueryDTO);
        if (CollectionUtils.isEmpty(priceConfigEntities)) {
            return null;
        }

        List<Integer> ruleItemIdList = priceConfigEntities.stream().map(BasicPriceConfigEntity::getRuleItemId).distinct().collect(Collectors.toList());
        List<SystemRuleItemEntity> ruleItemEntities = systemRuleItemDao.getRuleItemByIdList(ruleItemIdList);
        return CollectionUtils.isEmpty(ruleItemEntities) ? null : ruleItemEntities.get(0);
    }

    @Override
    public SystemRuleItemEntity getInvoiceType(Integer categoryId, Integer invoiceType, String taxRate) {
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(categoryId, SystemCodeConfigEnum.INVOICE_TYPE.getRuleCode());
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            return null;
        }
        return systemRuleItemDao.queryRuleItemByValue(systemRuleEntityList.get(0).getId(), invoiceType.toString(), taxRate, null);
    }

    @Override
    public SystemRuleItemEntity findByLkgCode(Integer categoryId, String ruleCode, String lkgCode) {
        List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(categoryId, ruleCode);
        if (CollectionUtils.isEmpty(systemRuleEntityList)) {
            return null;
        }
        return systemRuleItemDao.findByLkgCode(systemRuleEntityList.get(0).getId(), lkgCode, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public Result importSystemRule(MultipartFile uploadFile) {
        try {
            List<SystemRuleImportDTO> testImportDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, SystemRuleImportDTO.class);
            log.info(FastJsonUtils.getBeanToJson(testImportDTOList));
            if (!CollectionUtils.isEmpty(testImportDTOList)) {
                for (SystemRuleImportDTO testImportDTO : testImportDTOList) {
                    SystemRuleItemEntity systemRuleItemEntity = new SystemRuleItemEntity()
                            .setRuleId(testImportDTO.getRuleId())
                            .setCode(systemRuleDao.getById(testImportDTO.getRuleId()).getName())
                            .setRuleKey(StringUtils.isBlank(testImportDTO.getRuleKey()) ? "" : testImportDTO.getRuleKey())
                            .setRuleValue(StringUtils.isBlank(testImportDTO.getRuleValue()) ? "" : testImportDTO.getRuleValue())
                            .setValueType(2)
                            .setMemo(StringUtils.isBlank(testImportDTO.getMemo()) ? "" : testImportDTO.getMemo())
                            .setCreatedBy(1)
                            .setUpdatedBy(1)
                            .setLkgCode(StringUtils.isBlank(testImportDTO.getLkgCode()) ? "" : testImportDTO.getLkgCode())
                            .setStatus(DisableStatusEnum.ENABLE.getValue());
                    systemRuleItemDao.save(systemRuleItemEntity);
                }
            }
            return Result.success(testImportDTOList);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
    }

    @Override
    public Result updateLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {
        //根据id取出LOA配置信息
        SystemRuleItemEntity systemRule = systemRuleItemDao.getById(systemRuleCreateDTO.getRuleItemId());
        if (systemRule == null) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }

        String ruleItemKey = systemRuleCreateDTO.getRuleItemKey();
        String ruleItemValue = systemRuleCreateDTO.getRuleItemValue();
        Integer ruleId = systemRule.getRuleId();
        if (StringUtils.isNotBlank(ruleItemKey) && ContractApproveConfigItemEnum.MIN_AMOUNT.name().equals(ruleItemKey)) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(ContractApproveConfigItemEnum.MAX_AMOUNT.name(), ruleId, Integer.parseInt(systemRule.getCompanyId()));
            if (null != systemRuleItemEntity && new BigDecimal(ruleItemValue).compareTo(new BigDecimal(systemRuleItemEntity.getRuleValue())) >= 0) {
                throw new BusinessException(ResultCodeEnum.SETTING_MIN_ERROR);
            }
        }

        if (StringUtils.isNotBlank(ruleItemKey) && ContractApproveConfigItemEnum.MAX_AMOUNT.name().equals(ruleItemKey)) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(ContractApproveConfigItemEnum.MIN_AMOUNT.name(), ruleId, Integer.parseInt(systemRule.getCompanyId()));
            if (null != systemRuleItemEntity && new BigDecimal(ruleItemValue).compareTo(new BigDecimal(systemRuleItemEntity.getRuleValue())) <= 0) {
                throw new BusinessException(ResultCodeEnum.SETTING_MAX_ERROR);
            }
        }

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));

        SystemRuleItemEntity beforeSystemRuleItemEntity = new SystemRuleItemEntity();

        BeanUtils.copyProperties(systemRule, beforeSystemRuleItemEntity);
        systemRule
                .setRuleValue(systemRuleCreateDTO.getRuleItemValue())
                .setUpdatedByName(name)
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedAt(new Date())
        ;
        systemRuleItemDao.saveOrUpdate(systemRule);

        try {
            OperationActionEnum operationActionEnum = OperationActionEnum.getByName(systemRuleCreateDTO.getRuleItemKey());
            String category2Name = "";
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(systemRuleCreateDTO.getCategoryId());
            if (null != categoryEntity) {
                category2Name = categoryEntity.getName();
            }
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(systemRuleCreateDTO))
                    .setBeforeData(JSON.toJSONString(beforeSystemRuleItemEntity))
                    .setAfterData(JSON.toJSONString(systemRule))
                    .setOperationActionEnum(operationActionEnum)
                    .setCategory2Name(category2Name)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success();
    }

    @Override
    public Result queryLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {
        List<SystemRuleItemEntity> systemRuleItemEntities = getSystemRuleItemEntities(systemRuleCreateDTO.getCategoryId(), SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG.getRuleCode());
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        Map<Integer, String> companyNameMap = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getShortName));
        systemRuleItemEntities.forEach(i -> {
            String companyName = companyNameMap.get(Integer.parseInt(i.getCompanyId()));
            i.setCompanyName(companyName);
        });
//        List<SystemRuleItemEntity> systemRuleItemEntities1 = getSystemRuleItemEntities(systemRuleCreateDTO.getCategoryId(), SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG_FL.getRuleCode());
//        systemRuleItemEntities1.forEach(i -> {i.setCompanyName("FL");});
//        systemRuleItemEntities.addAll(systemRuleItemEntities1);
        return Result.success(systemRuleItemEntities);
    }

    @Override
    public Result saveLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {


        List<SystemRuleEntity> systemRuleEntities = systemRuleDao.querySystemRule(systemRuleCreateDTO.getCategoryId(), SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG.getRuleCode());
        String companyId = systemRuleCreateDTO.getCompanyId().get(0);
        List<Integer> idList = systemRuleEntities.stream().map(SystemRuleEntity::getId).collect(Collectors.toList());
        List<SystemRuleItemEntity> systemRuleItemEntities = systemRuleItemDao.querySystemRuleItemListByApproveConfig(idList, systemRuleCreateDTO.getRuleItemKey(), Integer.parseInt(companyId));

        if (!CollectionUtils.isEmpty(systemRuleItemEntities)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_EXIST);
        }

        ContractApproveConfigItemEnum contractApproveConfigItemEnum = ContractApproveConfigItemEnum.getByName(systemRuleCreateDTO.getRuleItemKey());

        String ruleItemKey = systemRuleCreateDTO.getRuleItemKey();
        String ruleItemValue = systemRuleCreateDTO.getRuleItemValue();
        if (StringUtils.isNotBlank(ruleItemKey) && ContractApproveConfigItemEnum.MIN_AMOUNT.name().equals(ruleItemKey)) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(ContractApproveConfigItemEnum.MAX_AMOUNT.name(), systemRuleEntities.get(0).getId(), Integer.parseInt(companyId));
            if (null != systemRuleItemEntity) {
                if (new BigDecimal(ruleItemValue).compareTo(new BigDecimal(systemRuleItemEntity.getRuleValue())) >= 0) {
                    throw new BusinessException(ResultCodeEnum.SETTING_MIN_ERROR);
                }
            }
        }

        if (StringUtils.isNotBlank(ruleItemKey) && ContractApproveConfigItemEnum.MAX_AMOUNT.name().equals(ruleItemKey)) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(ContractApproveConfigItemEnum.MIN_AMOUNT.name(), systemRuleEntities.get(0).getId(), Integer.parseInt(companyId));
            if (null != systemRuleItemEntity) {
                if (new BigDecimal(ruleItemValue).compareTo(new BigDecimal(systemRuleItemEntity.getRuleValue())) <= 0) {
                    throw new BusinessException(ResultCodeEnum.SETTING_MAX_ERROR);
                }
            }
        }

        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));

        SystemRuleItemEntity beforeSystemRuleItemEntity = BeanConvertUtils.convert(SystemRuleItemEntity.class, systemRuleCreateDTO);

        SystemRuleEntity systemRuleEntity = systemRuleEntities.get(0);
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(systemRuleCreateDTO.getCategoryId());

        String code = categoryEntity.getName() + contractApproveConfigItemEnum.getCode();

        beforeSystemRuleItemEntity
                .setCompanyId(companyId)
                .setRuleId(systemRuleEntity.getId())
                .setCode(code)
                .setRuleKey(contractApproveConfigItemEnum.name())
                .setRuleValue(systemRuleCreateDTO.getRuleItemValue())
                .setUpdatedByName(name)
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedAt(new Date())
        ;
        systemRuleItemDao.save(beforeSystemRuleItemEntity);

        try {
            String actionName = GoodsCategoryEnum.getByValue(systemRuleCreateDTO.getCategoryId()).getCode() + "_" + systemRuleCreateDTO.getRuleItemKey();
            OperationActionEnum operationActionEnum = OperationActionEnum.getByName(actionName);
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(systemRuleCreateDTO))
                    .setBeforeData(JSON.toJSONString(beforeSystemRuleItemEntity))
                    .setAfterData(JSON.toJSONString(beforeSystemRuleItemEntity))
                    .setOperationActionEnum(operationActionEnum)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success();
    }

    @Override
    public List<ContractApproveConfigItemDTO> queryContractApproveConfigItem(Integer categoryId) {

        List<ContractApproveConfigItemDTO> contractApproveConfigItemDTOS = new ArrayList<>();

        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(categoryId);

        for (ContractApproveConfigItemEnum contractApproveConfigItemEnum : ContractApproveConfigItemEnum.values()) {

            ContractApproveConfigItemDTO contractApproveConfigItemDTO = new ContractApproveConfigItemDTO();
            contractApproveConfigItemDTO
                    .setDefaultValue(contractApproveConfigItemEnum.getDefaultValue())
                    .setPlaceholder(contractApproveConfigItemEnum.name())
                    .setName(categoryEntity.getName() + contractApproveConfigItemEnum.getCode())
                    .setValueType(contractApproveConfigItemEnum.getValueType())
                    .setUnit(contractApproveConfigItemEnum.getUnit());
            contractApproveConfigItemDTOS.add(contractApproveConfigItemDTO);
        }

        return contractApproveConfigItemDTOS;
    }

    /**
     * 导入目的港
     *
     * @param file
     * @return
     */
    @Override
    public Result importDestination(MultipartFile file) {
        List<DestinationImportDTO> destinationImportDTOList;
        List<SystemRuleItemEntity> insertDestinationList = new ArrayList<>();
        List<SystemRuleItemEntity> updateDestinationList = new ArrayList<>();
        try {
            destinationImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, DestinationImportDTO.class);
            if (!CollectionUtils.isEmpty(destinationImportDTOList)) {
                List<String> category2NameList = destinationImportDTOList.stream().map(DestinationImportDTO::getCategory2Name)
                        .distinct().collect(Collectors.toList());
                // 生成配置主表（多品种）。返回Map：category2Name, ruleId
                Map<String, Integer> ruleIdMap = this.importSystemRuleInfo(category2NameList, SystemCodeConfigEnum.DESTINATION);
                log.info("目的地配置导入程序===================解析总条数：{}", destinationImportDTOList.size());
                destinationImportDTOList = destinationImportDTOList.stream().filter(it -> {
                            return StringUtils.isNotBlank(it.getCategory2Name()) && StringUtils.isNotBlank(it.getOperationName()) && StringUtils.isNotBlank(it.getRuleKey());
                        })
                        .collect(Collectors.toList());
                log.info("目的地配置导入程序===================过滤需新增/更新的总条数：{}", destinationImportDTOList.size());
                Timestamp now = DateTimeUtil.now();
                for (DestinationImportDTO importDTO : destinationImportDTOList) {
                    Integer ruleId = ruleIdMap.get(importDTO.getCategory2Name());
                    SystemRuleItemEntity destinationRuleItemEntity = null;
                    if (null != importDTO.getRuleItemId()) {
                        destinationRuleItemEntity = systemRuleItemDao.getById(importDTO.getRuleItemId());
                    }
                    if (null == destinationRuleItemEntity) {
                        destinationRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(importDTO.getRuleKey().trim(), ruleId, null);
                    }
                    //通过1、ruleItemId，2、ruleId+ruleKey方式，还是找不到目的地配置，则新增
                    String destinationName = importDTO.getRuleKey().trim();
                    if (null == destinationRuleItemEntity) {
                        //需要新增
                        destinationRuleItemEntity = new SystemRuleItemEntity().setRuleId(ruleId)
                                .setCode(importDTO.getCategory2Name() + "_" + SystemCodeConfigEnum.DESTINATION.getName())
                                .setRuleKey(destinationName)
                                .setRuleValue(destinationName)
                                .setStatus(importDTO.getStatus())
                                .setLkgCode(StringUtils.isNotBlank(importDTO.getLkgCode()) ? importDTO.getLkgCode().trim() : "")
                                .setMdmCode(StringUtils.isNotBlank(importDTO.getMdmCode()) ? importDTO.getMdmCode().trim() : "")
                                .setValueType(2)
                                .setCreatedAt(now)
                                .setUpdatedAt(now)
                                .setCreatedBy(1)
                                .setCreatedByName("Sys");
                        systemRuleItemDao.save(destinationRuleItemEntity);
                        insertDestinationList.add(destinationRuleItemEntity.setMemo("新增"));
                    } else {
                        //需要更新
                        destinationRuleItemEntity
                                .setCode(importDTO.getCategory2Name() + "_" + SystemCodeConfigEnum.DESTINATION.getName())
                                .setRuleKey(destinationName)
                                .setRuleValue(destinationName)
                                .setLkgCode(StringUtils.isNotBlank(importDTO.getLkgCode()) ? importDTO.getLkgCode().trim() : destinationRuleItemEntity.getLkgCode())
                                .setMdmCode(StringUtils.isNotBlank(importDTO.getMdmCode()) ? importDTO.getMdmCode().trim() : destinationRuleItemEntity.getMdmCode())
                                .setStatus(importDTO.getStatus())
                                .setValueType(2)
                                .setUpdatedAt(now)
                                .setUpdatedBy(1)
                                .setUpdatedByName("Sys");
                        systemRuleItemDao.updateById(destinationRuleItemEntity);
                        updateDestinationList.add(destinationRuleItemEntity.setMemo(importDTO.getCategory2Name() + "目的港更新"));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("目的港模板错误" + e.toString());
        }
        return Result.success("新增目的港条数：" + insertDestinationList.size() + "更新目的港条数：" + updateDestinationList.size(), insertDestinationList.addAll(updateDestinationList));
    }

    @Override
    public Result importWeightCheck(MultipartFile file) {
        List<WeightCheckImportDTO> weightCheckImportDTOList;
        List<SystemRuleItemEntity> insertWeightCheckList = new ArrayList<>();
        List<SystemRuleItemEntity> updateWeightCheckList = new ArrayList<>();
        SystemCodeConfigEnum weightCheckConfigEnum = SystemCodeConfigEnum.WEIGHT_CHECK_2;
        try {
            weightCheckImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, WeightCheckImportDTO.class);
            if (!CollectionUtils.isEmpty(weightCheckImportDTOList)) {
                log.info("重量检验配置导入程序===================解析总条数：{}", weightCheckImportDTOList.size());
                weightCheckImportDTOList = weightCheckImportDTOList.stream().filter(it -> {
                            return StringUtils.isNotBlank(it.getCategory2Name()) && StringUtils.isNotBlank(it.getOperationName()) && StringUtils.isNotBlank(it.getRuleKey());
                        })
                        .collect(Collectors.toList());
                log.info("重量检验配置导入程序===================过滤需新增/更新的总条数：{}", weightCheckImportDTOList.size());
                // 生成配置主表（多品种）。返回Map：category2Name, ruleId
                List<String> category2NameList = weightCheckImportDTOList.stream().map(WeightCheckImportDTO::getCategory2Name)
                        .distinct().collect(Collectors.toList());
                Map<String, Integer> ruleIdMap = this.importSystemRuleInfo(category2NameList, weightCheckConfigEnum);
                Timestamp now = DateTimeUtil.now();
                for (WeightCheckImportDTO importDTO : weightCheckImportDTOList) {
                    Integer ruleId = ruleIdMap.get(importDTO.getCategory2Name());
                    Integer salesType = ContractSalesTypeEnum.getByDesc(importDTO.getSalesType()).getValue();
                    String weightCheckName = importDTO.getRuleKey().trim();
                    SystemRuleItemEntity weightCheckRuleItemEntity = null;
                    if (null != importDTO.getRuleItemId()) {
                        weightCheckRuleItemEntity = systemRuleItemDao.getById(importDTO.getRuleItemId());
                    }
                    if (null == weightCheckRuleItemEntity) {
                        weightCheckRuleItemEntity = systemRuleItemDao.getWeightCheckByType(ruleId, salesType, weightCheckName);
                    }
                    //通过1、ruleItemId，2、ruleId+ruleKey方式，还是找不到目的地配置，则新增
                    if (null == weightCheckRuleItemEntity) {
                        //需要新增
                        weightCheckRuleItemEntity = new SystemRuleItemEntity().setRuleId(ruleId)
                                .setCode(importDTO.getCategory2Name() + "_" + weightCheckConfigEnum.getName())
                                .setRuleKey(weightCheckName)
                                .setRuleValue(StringUtils.isNotBlank(importDTO.getRuleValue()) ? importDTO.getRuleValue() : "")
                                .setStatus(importDTO.getStatus())
                                .setLkgCode(StringUtils.isNotBlank(importDTO.getLkgCode()) ? importDTO.getLkgCode().trim() : "")
                                //采销类型
                                .setValueType(salesType)
                                .setCreatedAt(now)
                                .setUpdatedAt(now)
                                .setCreatedBy(1)
                                .setCreatedByName("Sys");
                        systemRuleItemDao.save(weightCheckRuleItemEntity);
                        insertWeightCheckList.add(weightCheckRuleItemEntity.setMemo("新增"));
                    } else {
                        //需要更新
                        weightCheckRuleItemEntity
                                .setCode(importDTO.getCategory2Name() + "_" + weightCheckConfigEnum.getName())
                                .setRuleKey(weightCheckName)
                                .setRuleValue(StringUtils.isNotBlank(importDTO.getRuleValue()) ? importDTO.getRuleValue() : "")
                                .setLkgCode(StringUtils.isNotBlank(importDTO.getLkgCode()) ? importDTO.getLkgCode().trim() : weightCheckRuleItemEntity.getLkgCode())
                                .setStatus(importDTO.getStatus())
                                //采销类型
                                .setValueType(salesType)
                                .setUpdatedAt(now)
                                .setUpdatedBy(1)
                                .setUpdatedByName("Sys");
                        systemRuleItemDao.updateById(weightCheckRuleItemEntity);
                        updateWeightCheckList.add(weightCheckRuleItemEntity.setMemo(importDTO.getCategory2Name() + "重量检验更新"));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("重量检验模板错误" + e.toString());
        }
        return Result.success("新增重量检验条数：" + insertWeightCheckList.size() + "，更新重量检验条数：" + updateWeightCheckList.size(), insertWeightCheckList.addAll(updateWeightCheckList));
    }

    @Override
    public Result importPackageWeight(MultipartFile file) {
        List<PackageWeightImportDTO> packageWeightImportDTOList;
        List<SystemRuleItemEntity> insertPackageWeightList = new ArrayList<>();
        List<SystemRuleItemEntity> updatePackageWeightList = new ArrayList<>();
        SystemCodeConfigEnum packageWeightConfigEnum = SystemCodeConfigEnum.PACKAGE_WEIGHT;
        try {
            packageWeightImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, PackageWeightImportDTO.class);
            if (!CollectionUtils.isEmpty(packageWeightImportDTOList)) {
                log.info("袋皮扣重配置导入程序===================解析总条数：{}", packageWeightImportDTOList.size());
                packageWeightImportDTOList = packageWeightImportDTOList.stream().filter(it -> {
                            return StringUtils.isNotBlank(it.getCategory2Name()) && StringUtils.isNotBlank(it.getOperationName()) && StringUtils.isNotBlank(it.getRuleKey());
                        })
                        .collect(Collectors.toList());
                log.info("袋皮扣重配置导入程序===================过滤需新增/更新的总条数：{}", packageWeightImportDTOList.size());
                // 生成配置主表（多品种）。返回Map：category2Name, ruleId，袋皮扣重第一层rule
                List<String> category2NameList = packageWeightImportDTOList.stream().map(PackageWeightImportDTO::getCategory2Name)
                        .distinct().collect(Collectors.toList());
                Map<String, Integer> ruleIdMap = this.importSystemRuleInfo(category2NameList, packageWeightConfigEnum);
                Timestamp now = DateTimeUtil.now();
                for (PackageWeightImportDTO importDTO : packageWeightImportDTOList) {
                    Integer parentRuleId = ruleIdMap.get(importDTO.getCategory2Name());
                    String PackageWeightName = importDTO.getRuleKey().trim();
                    String packageName = importDTO.getPackageName().trim();
                    //二级-袋皮扣重 第二层rule
                    SystemRuleEntity secondPackageRuleEntity = getSecondPackageWeightRule(packageWeightConfigEnum, importDTO, parentRuleId);
                    Integer ruleId = secondPackageRuleEntity.getId();
                    SystemRuleItemEntity packageWeightRuleItemEntity = null;
                    packageWeightRuleItemEntity = systemRuleItemDao.queryRuleItemByRuleKey(importDTO.getRuleKey().trim(), ruleId, null);
                    //通过1、ruleItemId，2、ruleId+ruleKey方式，还是找不到袋皮扣重配置，则新增
                    if (null == packageWeightRuleItemEntity) {
                        //需要新增
                        packageWeightRuleItemEntity = new SystemRuleItemEntity().setRuleId(ruleId)
                                .setCode(packageName)
                                //袋皮扣重名称
                                .setRuleKey(PackageWeightName)
                                .setRuleValue(PackageWeightName)
                                //协议文本
                                .setMemo(importDTO.getMemo())
                                .setStatus(importDTO.getStatus())
                                .setValueType(2)
                                .setCreatedAt(now)
                                .setUpdatedAt(now)
                                .setCreatedBy(1)
                                .setCreatedByName("Sys");
                        systemRuleItemDao.save(packageWeightRuleItemEntity);
                        insertPackageWeightList.add(packageWeightRuleItemEntity.setMemo("新增"));
                    } else {
                        //需要更新
                        packageWeightRuleItemEntity
                                .setCode(packageName)
                                //袋皮扣重名称
                                .setRuleKey(PackageWeightName)
                                .setRuleValue(PackageWeightName)
                                //协议文本
                                .setMemo(importDTO.getMemo())
                                .setStatus(importDTO.getStatus())
                                .setValueType(2)
                                .setUpdatedAt(now)
                                .setUpdatedBy(1)
                                .setUpdatedByName("Sys");
                        systemRuleItemDao.updateById(packageWeightRuleItemEntity);
                        updatePackageWeightList.add(packageWeightRuleItemEntity.setMemo(importDTO.getCategory2Name() + "袋皮扣重更新"));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("袋皮扣重模板错误" + e.toString());
        }
        return Result.success("新增袋皮扣重条数：" + insertPackageWeightList.size() + "更新袋皮扣重条数：" + updatePackageWeightList.size(), insertPackageWeightList.addAll(updatePackageWeightList));

    }

    private SystemRuleEntity getSecondPackageWeightRule(SystemCodeConfigEnum packageWeightConfigEnum,
                                                        PackageWeightImportDTO importDTO,
                                                        Integer parentRuleId) {
        SystemRuleEntity firstPackageRuleEntity = systemRuleDao.getById(parentRuleId);
        String category2Name = importDTO.getCategory2Name().trim();
        String packageName = importDTO.getPackageName().trim();
        SystemRuleEntity secondPackageRuleEntity = systemRuleDao.getPackageRuleByParentId(parentRuleId, packageName);
        if (null == secondPackageRuleEntity) {
            Timestamp now = DateTimeUtil.now();
            secondPackageRuleEntity = new SystemRuleEntity()
                    .setCategoryId(firstPackageRuleEntity.getCategoryId())
                    .setParentId(parentRuleId)
                    .setCode(packageWeightConfigEnum.getRuleCode())
                    .setName(packageName)
                    .setSort(0)
                    .setStatus(DisableStatusEnum.ENABLE.getValue())
                    .setMemo(packageWeightConfigEnum.getName() + "_" + category2Name)
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            systemRuleDao.save(secondPackageRuleEntity);
        }
        return secondPackageRuleEntity;
    }

    /**
     * 根据品类及配置ruleCode，生成配置主表信息
     *
     * @param category2NameList
     * @return
     */
    private Map<String, Integer> importSystemRuleInfo(List<String> category2NameList, SystemCodeConfigEnum systemCodeConfigEnum) {
        Map<String, Integer> ruleIdMap = new HashMap<>();
        Timestamp now = DateTimeUtil.now();
        for (String category2Name : category2NameList) {
            CategoryEntity category2Entity = categoryFacade.getCategoryByName(category2Name.trim(), 2);
            if (null != category2Entity) {
                Integer category2Id = category2Entity.getSerialNo();
                List<SystemRuleEntity> systemRuleEntityList = systemRuleDao.querySystemRule(category2Id, systemCodeConfigEnum.getRuleCode());
                if (!CollectionUtils.isEmpty(systemRuleEntityList)) {
                    SystemRuleEntity systemRuleEntity = systemRuleEntityList.get(0);
                    systemRuleEntity.setName(systemCodeConfigEnum.getName())
                            .setMemo(systemCodeConfigEnum.getName() + "_" + category2Name.trim())
                    ;
                    systemRuleDao.updateById(systemRuleEntity);
                    //配置主表已存在
                    ruleIdMap.put(category2Name.trim(), systemRuleEntity.getId());
                } else {
                    //生成目的地新品类的主配置
                    SystemRuleEntity systemRuleEntity = new SystemRuleEntity()
                            .setCode(systemCodeConfigEnum.getRuleCode())
                            .setCategoryId(category2Id)
                            .setName(systemCodeConfigEnum.getName())
                            .setMemo(systemCodeConfigEnum.getName() + "_" + category2Name.trim())
                            .setStatus(DisableStatusEnum.ENABLE.getValue())
                            .setSort(0)
                            .setCreatedAt(now)
                            .setUpdatedAt(now);
                    systemRuleDao.save(systemRuleEntity);
                    ruleIdMap.put(category2Name.trim(), systemRuleEntity.getId());
                }
            }
        }
        return ruleIdMap;
    }

    private List<SystemRuleItemEntity> getSystemRuleItemEntities(Integer categoryId, String ruleCode) {
        List<SystemRuleEntity> systemRuleEntities = systemRuleDao.querySystemRule(categoryId, ruleCode);
        if (CollectionUtils.isEmpty(systemRuleEntities)) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }
        List<Integer> ruleIdList = systemRuleEntities.stream().map(SystemRuleEntity::getId).collect(Collectors.toList());
        return systemRuleItemDao.queryRuleItemListByRuleId(ruleIdList);
    }

    //LOA阈值导入
    public Result importLOAValue(MultipartFile file) {
        List<ImportLOAExcelDTO> importLOAExcelDTOS = EasyPoiUtils.importExcel(file, 0, 1, ImportLOAExcelDTO.class);

        for(ImportLOAExcelDTO importLOAExcelDTO : importLOAExcelDTOS){

            //查询所有主体
            List<CompanyEntity> companyEntities = companyService.queryCompanyList();

            //根据品种名称查询
            CategoryEntity categoryEntity = categoryFacade.getCategoryByName(importLOAExcelDTO.getCategory2(),2);

            for(CompanyEntity companyEntity : companyEntities){

                SystemRuleCreateDTO systemRuleCreateDTO = new SystemRuleCreateDTO();
                List<String> companyIds = new ArrayList<>();
                companyIds.add(String.valueOf(companyEntity.getId()));
                systemRuleCreateDTO.setCompanyId(companyIds);
                systemRuleCreateDTO.setCategoryId(categoryEntity.getId());
                systemRuleCreateDTO.setRuleItemKey(importLOAExcelDTO.getRuleItemKey());
                systemRuleCreateDTO.setRuleItemValue(importLOAExcelDTO.getRuleItemValue());
                List<SystemRuleEntity> systemRuleEntities = systemRuleDao.querySystemRule(systemRuleCreateDTO.getCategoryId(), SystemCodeConfigEnum.CONTRACT_APPROVE_CONFIG.getRuleCode());
                String companyId = systemRuleCreateDTO.getCompanyId().get(0);
                List<Integer> idList = systemRuleEntities.stream().map(SystemRuleEntity::getId).collect(Collectors.toList());
                List<SystemRuleItemEntity> systemRuleItemEntities = systemRuleItemDao.querySystemRuleItemListByApproveConfig(idList, systemRuleCreateDTO.getRuleItemKey(), Integer.parseInt(companyId));
                //为空新增
                if(CollectionUtils.isEmpty(systemRuleItemEntities)){
                    saveLOAValue(systemRuleCreateDTO);
                }
            }

        }
        return Result.success();
    }

}
