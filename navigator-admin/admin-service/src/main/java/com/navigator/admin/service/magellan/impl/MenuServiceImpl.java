package com.navigator.admin.service.magellan.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.navigator.admin.dao.MenuDao;
import com.navigator.admin.pojo.dto.MenuDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.MenuEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.entity.RoleMenuEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.admin.pojo.qo.MenuQO;
import com.navigator.admin.pojo.qo.RoleMenuQO;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.admin.service.magellan.IMenuService;
import com.navigator.admin.service.magellan.IRoleMenuService;
import com.navigator.admin.service.magellan.IRoleService;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
public class MenuServiceImpl implements IMenuService {

    @Autowired
    private IEmployRoleService employRoleService;
    @Autowired
    private IRoleMenuService roleMenuService;
    @Autowired
    private MenuDao menuDao;
    @Autowired
    private IRoleService roleService;
    @Resource
    private CommonProperties commonProperties;

    private static List<String> codeList = Arrays.asList("N024", "N027");

    @Override
    public MenuEntity getMenuById(String id) {
        return menuDao.getMenuById(id);
    }

    @Override
    public MenuVO getMenusByEmploy(MenuDTO menuDTO) {
        MenuVO menuVO = new MenuVO();
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(menuDTO.getEmployId());
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
        List<RoleEntity> roleEntities = roleService.queryByIdList(roleIdList);
        List<Integer> categoryIdList = roleEntities.stream().map(RoleEntity::getCategoryId).collect(Collectors.toList());
        // 系统管理员展示所有菜单
        if (categoryIdList.contains(0) || new BigDecimal(menuDTO.getEmployId()).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).collect(Collectors.toList());
        menuVO.setCategoryList(categoryMap);

        List<MenuEntity> menuList = menuDao.getMenuByCategoryId(menuDTO.getCategoryId(), menuDTO.getSystemId(), null);
        if (null == menuList) {
            return menuVO;
        }
        List<MenuDetailVO> allMenuDetailVOList = new ArrayList<>();
        menuList.forEach(
                i -> {
                    MenuDetailVO menuDetailVO = new MenuDetailVO();
                    menuDetailVO.setName(i.getCode() + "-" + i.getName())
                            .setIcon(i.getIcon())
                            .setUrl(i.getUrl())
                            .setParentId(i.getParentId())
                            .setId(i.getId())
                            .setCode(i.getCode())

                    ;
                    allMenuDetailVOList.add(menuDetailVO);
                }
        );
        List<MenuDetailVO> parentMenuDetailVOList = allMenuDetailVOList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        Map<Integer, List<MenuDetailVO>> map = allMenuDetailVOList.stream().sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.groupingBy(MenuDetailVO::getParentId));
        parentMenuDetailVOList.forEach(i -> {
            i.setChildren(map.get(i.getId()));
        });
        menuVO.setMenuDetailVOList(parentMenuDetailVOList);
        return menuVO;
    }

    @Override
    public MenuVO getMenusByCondition(MenuDTO menuDTO) {
        List<Integer> menuIdList = new ArrayList<>();
        String employId = menuDTO.getEmployId();
        MenuVO menuVO = new MenuVO();
        if (StringUtils.isNotBlank(employId)) {
            List<RoleEntity> roleEntityList = roleService.queryRoleByEmployId(employId);
            List<Integer> roleIdList = roleEntityList.stream().map(RoleEntity::getId).collect(Collectors.toList());
            List<RoleMenuEntity> roleMenuEntityList = roleMenuService.getMenuIdListByRoleIdList(roleIdList);
            List<Integer> list = roleMenuEntityList.stream().map(RoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                menuIdList = list;
            } else {
                menuIdList = Collections.singletonList(-1);
            }

            List<RoleEntity> roleEntities = roleService.queryByIdList(roleIdList);
            List<Integer> categoryIdList = roleEntities.stream().map(RoleEntity::getCategoryId).collect(Collectors.toList());
            if (categoryIdList.contains(0)) {
                categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
            }
            // 系统管理员展示所有菜单
            if (new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
                categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
            }
            List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                    || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
                Map map = new HashMap();
                map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
                return map;
            }).collect(Collectors.toList());
            menuVO.setCategoryList(categoryMap);
        }

        List<MenuEntity> menuList = menuDao.getMenuByCategoryId(menuDTO.getCategoryId(), menuDTO.getSystemId(), menuIdList);
        if (CollectionUtils.isEmpty(menuList)) {
            return menuVO;
        }
        List<MenuDetailVO> allMenuDetailVOList = new ArrayList<>();
        menuList.forEach(
                i -> {
                    MenuDetailVO menuDetailVO = new MenuDetailVO();
                    menuDetailVO.setName(i.getCode() + "-" + i.getName())
                            .setIcon(i.getIcon())
                            .setUrl(i.getUrl())
                            .setParentId(i.getParentId())
                            .setId(i.getId())
                            .setCode(i.getCode())
                            .setCategoryId(i.getCategoryId())
                            .setParentCode(i.getParentCode())
                    ;
                    allMenuDetailVOList.add(menuDetailVO);
                }
        );
        List<MenuDetailVO> parentMenuDetailVOList = allMenuDetailVOList.stream().filter(i -> i.getParentId() == 0).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.toList());
        Map<String, List<MenuDetailVO>> map = allMenuDetailVOList.stream().filter(i -> i.getParentCode() != null).sorted(Comparator.comparing(MenuDetailVO::getCode)).collect(Collectors.groupingBy(MenuDetailVO::getParentCode));
        parentMenuDetailVOList.forEach(i -> {
            i.setChildren(map.get(i.getCode()));
        });
        List<MenuDetailVO> collect = parentMenuDetailVOList.stream().filter(i -> !codeList.contains(i.getCode()) || (codeList.contains(i.getCode()) && i.getCategoryId().toString().equals(menuDTO.getCategoryId()))).collect(Collectors.toList());
        menuVO.setMenuDetailVOList(collect);
        return menuVO;
    }

    @Override
    public MenuVO getMenuByRoleId(RoleDTO roleDTO) {
        MenuVO menuVO = new MenuVO();
        MenuDTO defaultDTO = new MenuDTO();
        defaultDTO
                .setCategoryId("0");
        MenuVO defaultMenu = getMenusByCondition(defaultDTO);
        List<String> defaultCode = defaultMenu.getMenuDetailVOList().stream().map(MenuDetailVO::getCode).collect(Collectors.toList());

        MenuDTO sbmDTO = new MenuDTO();
        sbmDTO
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        MenuVO sbmMenu = getMenusByCondition(sbmDTO);
        List<MenuDetailVO> sbmList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sbmList = sbmMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());
        }
        MenuDTO sboDTO = new MenuDTO();
        sboDTO
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        MenuVO sboMenu = getMenusByCondition(sboDTO);
        List<MenuDetailVO> sboList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbmMenu.getMenuDetailVOList())) {
            sboList = sboMenu.getMenuDetailVOList().stream()
                    .peek(i -> {
                        if (i.getChildren() != null) {
                            List<MenuDetailVO> collect = i.getChildren().stream().filter(k -> GoodsCategoryEnum.OSM_OIL.getValue().equals(k.getCategoryId())).collect(Collectors.toList());
                            i.setChildren(collect);
                        }
                    })
                    .filter(i -> !defaultCode.contains(i.getCode()) || (i.getChildren() != null && i.getChildren().stream().anyMatch(m -> GoodsCategoryEnum.OSM_OIL.getValue().equals(m.getCategoryId())))).collect(Collectors.toList());

        }
        List<MenuEntity> parentMenu = menuDao.getMenuParentMenu();
        List<Integer> parentMenuIdList = parentMenu.stream().map(MenuEntity::getId).collect(Collectors.toList());
        List<Integer> roleIdList = Arrays.asList(roleDTO.getRoleId());
        List<RoleMenuEntity> roleMenuEntityList = roleMenuService.getMenuIdListByRoleIdList(roleIdList);
        List<Integer> menuIdList = roleMenuEntityList.stream().filter(i -> (!parentMenuIdList.contains(i.getMenuId())) || i.getMenuId() == 77).map(RoleMenuEntity::getMenuId).distinct().collect(Collectors.toList());
        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        menuVO.setSbmList(sbmList);
        menuVO.setSboList(sboList);
        menuVO.setChooseIdList(menuIdList);
        return menuVO;
    }

    @Override
    public MenuVO getMenuByEmployId(RoleDTO roleDTO) {
        MenuVO menuVO = new MenuVO();
        String employId = roleDTO.getEmployId();
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(employId);
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
        List<RoleEntity> roleEntities = roleService.queryByIdList(roleIdList);
        List<Integer> categoryIdList = roleEntities.stream().map(RoleEntity::getCategoryId).collect(Collectors.toList());
        if (categoryIdList.contains(0)) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
        }
        // 系统管理员展示所有菜单
        if (new BigDecimal(employId).compareTo(BigDecimal.TEN) <= 0 || CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            categoryIdList = Arrays.asList(GoodsCategoryEnum.OSM_MEAL.getValue(), GoodsCategoryEnum.OSM_OIL.getValue());
            employId = null;
        }
        List categoryMap = categoryIdList.stream().filter(i -> GoodsCategoryEnum.OSM_MEAL.getValue().equals(i)
                || GoodsCategoryEnum.OSM_OIL.getValue().equals(i)).sorted().map(i -> {
            Map map = new HashMap();
            map.put(i, GoodsCategoryEnum.getByValue(i).getDesc());
            return map;
        }).distinct().collect(Collectors.toList());

        menuVO.setCategoryList(categoryMap);

        MenuDTO defaultDTO = new MenuDTO();
        defaultDTO.setEmployId(employId)
                .setCategoryId("0");
        MenuVO defaultMenu = getMenusByCondition(defaultDTO);

        MenuDTO sbmDTO = new MenuDTO();
        sbmDTO.setEmployId(employId)
                .setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue().toString());
        MenuVO sbmMenu = getMenusByCondition(sbmDTO);

        MenuDTO sboDTO = new MenuDTO();
        sboDTO.setEmployId(employId)
                .setCategoryId(GoodsCategoryEnum.OSM_OIL.getValue().toString());
        MenuVO sboMenu = getMenusByCondition(sboDTO);

        menuVO.setDefaultList(defaultMenu.getMenuDetailVOList());
        menuVO.setSbmList(sbmMenu.getMenuDetailVOList());
        menuVO.setSboList(sboMenu.getMenuDetailVOList());
        return menuVO;
    }

    @Override
    public void saveRoleMenu(MenuDTO menuDTO) {
        roleMenuService.deleteRoleMenu(menuDTO.getRoleId());
        for (Integer menuId : menuDTO.getMenuIdList()) {
            RoleMenuEntity roleMenuEntity = new RoleMenuEntity();
            roleMenuEntity.setMenuId(menuId)
                    .setRoleId(Integer.parseInt(menuDTO.getRoleId()));
            roleMenuService.saveRoleMenu(roleMenuEntity);
        }
    }

    @Override
    public void addRoleMenu(MenuDTO menuDTO) {
        List<RoleEntity> roleEntities = roleService.queryByRoleDefIdList(menuDTO.getRoleDefIdList());
        if (CollectionUtils.isNotEmpty(roleEntities)) {
            for (RoleEntity roleEntity : roleEntities) {
                for (Integer menuId : menuDTO.getMenuIdList()) {
                    RoleMenuEntity roleMenuEntity = new RoleMenuEntity();
                    roleMenuEntity
                            .setMenuId(menuId)
                            .setRoleId(roleEntity.getId());
                    roleMenuService.saveRoleMenu(roleMenuEntity);
                }
            }
        }
    }

    @Override
    public List<MenuEntity> queryMenuList(MenuQO condition) {
        return menuDao.queryMenuList(condition);
    }

    @Override
    public MenuEntity getMenuByParentId(Integer parentId) {
        return menuDao.getMenuByParentId(parentId);
    }

    @Override
    public List<MenuEntity> getAuthMenuList(Integer userId, Integer category2) {
        // 授权菜单
        List<MenuEntity> menuList = new ArrayList<>();
        // 默认查询条件
        MenuQO menuQO = new MenuQO().setSystem(1).setLevel(1);
        // 查询通用
        if (category2 == 0) {
            menuQO.setIsCategory(0);
        }
        if (roleService.isAdmin(userId)) {
            // 是管理员
            menuList = this.queryMenuList(menuQO);
        } else {
            // 不是管理员
            // 用户关联的所有角色
            List<Integer> userRoleIdList = employRoleService.queryRoleIdList(new EmployRoleQO().setEmployId(userId));
            //过滤实角色的品类（默认+品类）
            List<RoleEntity> roleEntityList = roleService.queryByIdList(userRoleIdList);
            if (CollUtil.isNotEmpty(roleEntityList)) {
                userRoleIdList = roleEntityList.stream()
                        .filter(role -> Arrays.asList(category2, 0).contains(role.getCategory2()))
                        .map(RoleEntity::getId)
                        .collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(userRoleIdList)) {
                // 角色关联菜单
                Set<Integer> menuIdList = roleMenuService.queryMenuIdList(new RoleMenuQO().setRoleIdList(userRoleIdList));
                if (CollUtil.isNotEmpty(menuIdList)) {
                    menuQO.setMenuIdList(menuIdList);
                    menuList = this.queryMenuList(menuQO);
                }
            }
        }
        if (CollUtil.isNotEmpty(menuList)) {
            menuList.forEach(entity -> {
                if (entity.getIsCategory() == 1) {
                    entity.setCategoryId(category2);
                }
                if (StringUtil.isNotNullBlank(entity.getUrl())) {
                    entity.setUrl(entity.getUrl().replaceAll("\\{category2\\}", category2.toString()));
                }
            });
        }
        return menuList;
    }
}
