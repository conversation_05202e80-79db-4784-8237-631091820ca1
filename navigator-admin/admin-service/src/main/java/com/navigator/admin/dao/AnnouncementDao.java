package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.AnnouncementMapper;
import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.admin.pojo.entity.AnnouncementEntity;
import com.navigator.admin.pojo.enums.AnnouncementStatusEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Dao
public class AnnouncementDao extends BaseDaoImpl<AnnouncementMapper, AnnouncementEntity> {

    public IPage<AnnouncementEntity> queryAnnouncementList(Page<AnnouncementEntity> page, AnnouncementDTO announcementDTO) {
        String name = "";
        if (StringUtils.isNotBlank(announcementDTO.getName())) {
            name = announcementDTO.getName().trim();
        }

        IPage<AnnouncementEntity> iPage = page(page, Wrappers.<AnnouncementEntity>lambdaQuery()
                .like(StringUtils.isNotBlank(announcementDTO.getName()), AnnouncementEntity::getName, name)
                .eq(StringUtils.isNotBlank(announcementDTO.getSystemId()), AnnouncementEntity::getSystemId, announcementDTO.getSystemId())
                .eq(StringUtils.isNotBlank(announcementDTO.getStatus()), AnnouncementEntity::getStatus, announcementDTO.getStatus())
                .ge(StringUtils.isNotBlank(announcementDTO.getPublishStartTime()), AnnouncementEntity::getPublishTime, announcementDTO.getPublishStartTime())
                .le(StringUtils.isNotBlank(announcementDTO.getPublishEndTime()), AnnouncementEntity::getPublishTime, announcementDTO.getPublishEndTime())
                .ne(announcementDTO.getSketchStatus(), AnnouncementEntity::getStatus, AnnouncementStatusEnum.SKETCH.getValue())
                .eq(AnnouncementEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(AnnouncementEntity::getStatus)
                .orderByDesc(AnnouncementEntity::getUpdatedAt)
        );

        return iPage;
    }

    public List<AnnouncementEntity> queryAnnouncementList(Integer systemId) {
        List<AnnouncementEntity> list = list(Wrappers.<AnnouncementEntity>lambdaQuery()
                .eq(AnnouncementEntity::getSystemId, systemId)
                .eq(AnnouncementEntity::getStatus, AnnouncementStatusEnum.PUBLISH.getValue())
                .gt(AnnouncementEntity::getExpirationDate, new Date())
                .eq(AnnouncementEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(AnnouncementEntity::getPublishTime)
        );
        return list;
    }
}
