package com.navigator.admin.service.columbus.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.dao.columbus.CRolePowerDao;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.columbus.CPowerDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.qo.RolePowerQO;
import com.navigator.admin.service.BusinessDetailUpdateRecordService;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.columbus.ICRolePowerService;
import com.navigator.admin.service.columbus.ICRoleService;
import com.navigator.bisiness.enums.BusinessDetailCodeEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.JwtUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class CRolePowerServiceImpl implements ICRolePowerService {

    @Autowired
    private CRolePowerDao cRolePowerDao;
    @Autowired
    private ICRoleService cRoleService;
    @Autowired
    private ICEmployService cEmployService;
    @Autowired
    protected IOperationDetailService operationDetailService;

    @Autowired
    private BusinessDetailUpdateRecordService businessDetailUpdateRecordService;

    @Resource
    private EmployFacade employFacade;

    /**
     * v1.0 菜单按钮权限独立设置
     */

    @Override
    public void saveOrUpdateRolePower(CPowerDTO powerDTO) {
        int userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CEmployEntity employEntity = cEmployService.getEmployById(userId);
        CRoleEntity cRoleEntity = cRoleService.getRoleById(Integer.parseInt(powerDTO.getRoleId()));
        cRolePowerDao.deleteRoleByRoleId(Integer.parseInt(powerDTO.getRoleId()), employEntity.getCustomerId());
        for (Integer powerId : powerDTO.getPowerIdList()) {
            CRolePowerEntity cRolePowerEntity = new CRolePowerEntity();
            cRolePowerEntity.setPowerId(powerId)
                    .setRoleId(Integer.parseInt(powerDTO.getRoleId()))
                    .setRoleDefId(cRoleEntity.getRoleDefId())
                    .setCustomerId(employEntity.getCustomerId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            cRolePowerDao.save(cRolePowerEntity);
        }
    }

    @Override
    public List<CRolePowerEntity> queryByRoleId(String roleId, Integer customerId) {
        return cRolePowerDao.queryByRoleId(roleId, customerId);
    }

    @Override
    public List<CRolePowerEntity> queryByRoleIdList(List<Integer> roleIdList) {
        int userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CEmployEntity employEntity = cEmployService.getEmployById(userId);
        return cRolePowerDao.queryByRoleIdList(roleIdList, employEntity.getCustomerId());
    }


    /**
     * v2.0 统一菜单按钮权限
     */
    @Override
    public void saveOrUpdateRolePowerV2(CPowerDTO powerDTO) {

        BusinessDetailUpdateRecordEntity businessDetailUpdateRecordEntity = new BusinessDetailUpdateRecordEntity();
        //记录修改客户主数据人
        businessDetailUpdateRecordEntity
                .setDetailCode(BusinessDetailCodeEnum.C_EMPLOY_ROLE_EDIT.getValue())
                .setBusinessId(Integer.valueOf(powerDTO.getRoleId()))
                .setData(JSON.toJSONString(powerDTO))
                .setCreatedAt(new Date())
                .setCreatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        businessDetailUpdateRecordService.saveBusinessDetailUpdateRecord(businessDetailUpdateRecordEntity);

        int userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        CRoleEntity cRoleEntity = cRoleService.getRoleById(Integer.parseInt(powerDTO.getRoleId()));
        cRolePowerDao.deleteRoleByRoleId(Integer.parseInt(powerDTO.getRoleId()));
        for (Integer powerId : powerDTO.getPowerIdList()) {
            CRolePowerEntity cRolePowerEntity = new CRolePowerEntity();
            cRolePowerEntity.setPowerId(powerId)
                    .setRoleId(Integer.parseInt(powerDTO.getRoleId()))
                    .setRoleDefId(cRoleEntity.getRoleDefId())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId)
            ;
            cRolePowerDao.save(cRolePowerEntity);
        }


        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setReferName(cRoleEntity.getName())
                    .setDtoData(JSON.toJSONString(powerDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.SAVE_OR_UPDATE_POWER)
            ;
            operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<CRolePowerEntity> queryByRoleIdV2(String roleId) {
        return cRolePowerDao.queryByRoleId(roleId);
    }

    @Override
    public List<CRolePowerEntity> queryByRoleIdListV2(List<Integer> roleIdList) {
        return cRolePowerDao.queryByRoleIdList(roleIdList);
    }

    @Override
    public void addRolePower(CPowerDTO powerDTO) {
        List<CRoleEntity> roleEntities = cRoleService.queryByRoleDefIdList(powerDTO.getRoleDefIdList());
        if (CollectionUtils.isNotEmpty(roleEntities)) {
            for (CRoleEntity roleEntity : roleEntities) {
                for (Integer powerId : powerDTO.getPowerIdList()) {
                    CRolePowerEntity rolePowerEntity = new CRolePowerEntity();
                    rolePowerEntity
                            .setPowerId(powerId)
                            .setRoleId(roleEntity.getId())
                            .setRoleDefId(roleEntity.getRoleDefId());
                    cRolePowerDao.save(rolePowerEntity);
                }
            }
        }
    }


    @Override
    public void copyRolePower(Integer sourceRoleId, Integer targetRoleId) {
        List<CRolePowerEntity> rolePowerEntities = cRolePowerDao.queryByRoleId(String.valueOf(sourceRoleId));
        cRolePowerDao.deleteRoleByRoleId(targetRoleId);
        for (CRolePowerEntity rolePowerEntity : rolePowerEntities) {
            rolePowerEntity
                    .setId(null)
                    .setRoleId(targetRoleId)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date())
            ;
            cRolePowerDao.save(rolePowerEntity);
        }
    }

    @Override
    public Set<Integer> queryPowerIdList(RolePowerQO condition) {
        return cRolePowerDao.queryPowerIdList(condition);
    }

    @Override
    public List<CRolePowerEntity> getAllRolePowerList() {
        return cRolePowerDao.list(Wrappers.<CRolePowerEntity>lambdaQuery()
                .eq(CRolePowerEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    @Override
    public void updateDeletedByPowerIdAndRoleId(Integer powerId, Integer roleId) {
        cRolePowerDao.update(Wrappers.<CRolePowerEntity>lambdaUpdate()
                .eq(CRolePowerEntity::getPowerId, powerId)
                .eq(CRolePowerEntity::getRoleId, roleId)
                .set(CRolePowerEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue()));
    }

    @Override
    public void saveRolePower(CRolePowerEntity rolePowerEntity) {
        cRolePowerDao.save(rolePowerEntity);
    }
}
