package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.ContractApproveConfigItemDTO;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.InvoiceTypeDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleCreateDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.admin.service.systemrule.BasicPriceGoodsConfigService;
import com.navigator.admin.service.systemrule.SystemRuleService;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
public class SystemRuleFacadeImpl implements SystemRuleFacade {
    @Autowired
    private SystemRuleService systemRuleService;
    @Autowired
    private BasicPriceGoodsConfigService basicPriceGoodsConfigService;

    @Override
    public List<SystemRuleVO> getSystemRule(SystemRuleDTO systemRuleDTO) {
        List<SystemRuleVO> systemRuleVOList = systemRuleService.getSystemRule(systemRuleDTO);
        return systemRuleVOList;
    }

    @Override
    public SystemRuleItemEntity getRuleItemById(int id) {
        SystemRuleItemEntity systemRuleItemEntity = systemRuleService.getRuleItemById(id);
        return systemRuleItemEntity;
    }

    @Override
    public SystemRuleVO querySystemRuleDetail(SystemRuleDTO systemRuleDTO) {
        return systemRuleService.querySystemRuleDetail(systemRuleDTO);
    }

    @Override
    public List<SystemRuleVO> getNextSystemRule(SystemRuleDTO systemRuleDTO) {
        return systemRuleService.getNextSystemRule(systemRuleDTO);
    }

    @Override
    public Result saveOrUpdateSystemRule(SystemRuleCreateDTO systemRuleCreateDTO) {
        return Result.judge(systemRuleService.saveOrUpdateSystemRule(systemRuleCreateDTO));
    }

    @Override
    public Result updateInvoiceType(Integer categoryId, List<InvoiceTypeDTO> invoiceTypeDTOList) {
        return Result.judge(systemRuleService.updateInvoiceType(categoryId, invoiceTypeDTOList));
    }

    @Override
    public Result invalidStatus(Integer ruleItemId) {
        return Result.judge(systemRuleService.invalidStatus(ruleItemId));
    }

    @Override
    public Result filterBasicPrice(BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {
        return Result.success(systemRuleService.filterBasicPrice(basicPriceConfigQueryDTO));
    }

    @Override
    public SystemRuleItemEntity getInvoiceType(Integer categoryId, Integer invoiceType, String taxRate) {
        return systemRuleService.getInvoiceType(categoryId, invoiceType, taxRate);
    }

    @Override
    public SystemRuleItemEntity findByLkgCode(Integer categoryId, String ruleCode, String lkgCode) {
        return systemRuleService.findByLkgCode(categoryId, ruleCode, lkgCode);
    }

    @Override
    public Result importSystemRule(MultipartFile uploadFile) {
        return systemRuleService.importSystemRule(uploadFile);
    }

    @Override
    public Result updateLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleService.updateLOAValue(systemRuleCreateDTO);
    }

    @Override
    public Result queryLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleService.queryLOAValue(systemRuleCreateDTO);
    }

    @Override
    public Result saveLOAValue(SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleService.saveLOAValue(systemRuleCreateDTO);
    }

    @Override
    public List<ContractApproveConfigItemDTO> queryContractApproveConfigItem(Integer categoryId) {
        return systemRuleService.queryContractApproveConfigItem(categoryId);
    }

    @Override
    public Result queryBasicPriceGoodsConfigByCategoryId(Integer categoryId) {
        return Result.success(basicPriceGoodsConfigService.queryBasicPriceGoodsConfigByCategoryId(categoryId));
    }

    @Override
    public Result importDestination(MultipartFile uploadFile) {
        return systemRuleService.importDestination(uploadFile);
    }

    @Override
    public Result importWeightCheck(MultipartFile uploadFile) {
        return systemRuleService.importWeightCheck(uploadFile);
    }

    @Override
    public Result importPackageWeight(MultipartFile uploadFile) {
        return systemRuleService.importPackageWeight(uploadFile);
    }

    @Override
    public Result importLOAValue(MultipartFile file){
        return systemRuleService.importLOAValue(file);
    }

}
