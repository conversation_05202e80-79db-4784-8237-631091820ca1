package com.navigator.admin.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.BusinessDetailUpdateRecordMapper;
import com.navigator.admin.pojo.dto.BusinessDetailUpdateRecordDTO;
import com.navigator.admin.pojo.entity.BusinessDetailUpdateRecordEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;

import java.util.List;

@Dao
public class BusinessDetailUpdateRecordDao extends BaseDaoImpl<BusinessDetailUpdateRecordMapper, BusinessDetailUpdateRecordEntity> {
    public BusinessDetailUpdateRecordEntity detailUpdateSelect(BusinessDetailUpdateRecordDTO businessDetailUpdateRecordDTO) {
        QueryDTO queryDTO = new QueryDTO();
        IPage<BusinessDetailUpdateRecordEntity> iPage = this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), Wrappers.<BusinessDetailUpdateRecordEntity>lambdaQuery()
                .eq(BusinessDetailUpdateRecordEntity::getBusinessId, businessDetailUpdateRecordDTO.getBusinessId())
                .eq(null != businessDetailUpdateRecordDTO.getCategoryId(), BusinessDetailUpdateRecordEntity::getCategoryId, businessDetailUpdateRecordDTO.getCategoryId())
                .eq(BusinessDetailUpdateRecordEntity::getDetailCode, businessDetailUpdateRecordDTO.getDetailCode())
                .orderByDesc(BusinessDetailUpdateRecordEntity::getCreatedAt)
        );
        List<BusinessDetailUpdateRecordEntity> businessDetailUpdateRecordEntities = BeanConvertUtils.convert2List(BusinessDetailUpdateRecordEntity.class, iPage.getRecords());
        return businessDetailUpdateRecordEntities.isEmpty() ? null : businessDetailUpdateRecordEntities.get(0);
    }
}
