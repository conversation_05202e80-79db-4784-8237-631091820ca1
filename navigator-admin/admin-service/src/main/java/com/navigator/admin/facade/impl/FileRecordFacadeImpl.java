package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.FileRecordFacade;
import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.admin.pojo.entity.FileRecordEntity;
import com.navigator.admin.service.FileRecordService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class FileRecordFacadeImpl implements FileRecordFacade {
    @Autowired
    private FileRecordService fileRecordService;

    @Override
    public Result save(FileRecordDTO fileRecordDTO) {
        fileRecordService.save(fileRecordDTO);
        return Result.success();
    }

    @Override
    public Result modify(FileRecordDTO fileRecordDTO) {
        fileRecordService.modify(fileRecordDTO);
        return Result.success();
    }

    @Override
    public Result queryFileRecordDetail(FileRecordDTO fileRecordDTO) {
        FileRecordEntity fileRecordEntity = fileRecordService.queryFileRecordDetail(fileRecordDTO);
        return Result.success(fileRecordEntity);
    }

    @Override
    public Result queryFileRecordList(QueryDTO<FileRecordDTO> queryDTO) {
        return fileRecordService.queryFileRecordList(queryDTO);
    }

    @Override
    public Result queryFileRecordBySystemId(Integer systemId) {
        List<FileRecordEntity> list = fileRecordService.queryFileRecordBySystemId(systemId);
        return Result.success(list);
    }
}
