package com.navigator.admin.service.systemrule.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.config.RedisCacheMap;
import com.navigator.admin.dao.StructureRuleDao;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.mapper.StructureRuleMapper;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.admin.pojo.entity.StructureRuleEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.StructureRuleVO;
import com.navigator.admin.service.systemrule.StructureRuleService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StructureCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StructureRuleServiceImpl implements StructureRuleService {
    @Autowired
    private StructureRuleDao structureRuleDao;
    @Autowired
    private RedisCacheMap redisCacheMap;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private StructureRuleMapper structureRuleMapper;



    @Override
    public void save(StructureRuleDTO structureRuleDTO) {
        if (StringUtils.isBlank(structureRuleDTO.getStructureName())) {
            throw new BusinessException(ResultCodeEnum.NOT_BLANK);
        }
        List<StructureRuleEntity> structureRuleEntityList = structureRuleDao.queryByName(structureRuleDTO.getStructureName().trim());
        if (CollectionUtils.isNotEmpty(structureRuleEntityList)) {
            throw new BusinessException(ResultCodeEnum.NAME_REPEAT);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));
        StructureRuleEntity structureRuleEntity = new StructureRuleEntity();
        structureRuleEntity
                .setStructureName(structureRuleDTO.getStructureName())
                .setCreatedAt(new Date())
                .setCreatedBy(currentUserId)
                .setCreatedByName(name)
                .setUpdatedAt(new Date())
                .setUpdatedBy(null)
                .setUpdatedByName(null)
        ;
        structureRuleDao.save(structureRuleEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(structureRuleDTO))
                    .setBeforeData(null)
                    .setAfterData(JSON.toJSONString(structureRuleEntity))
                    .setOperationActionEnum(OperationActionEnum.NEW_STRUCTURE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void modify(StructureRuleDTO structureRuleDTO) {
        if (StringUtils.isBlank(structureRuleDTO.getStructureName())) {
            throw new BusinessException(ResultCodeEnum.NOT_BLANK);
        }
        List<StructureRuleEntity> structureRuleEntityList = structureRuleDao.queryByName(structureRuleDTO.getStructureName().trim());
        if (CollectionUtils.isNotEmpty(structureRuleEntityList)) {
            throw new BusinessException(ResultCodeEnum.NAME_REPEAT);
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));

        StructureRuleEntity beforeStructureRuleEntity = new StructureRuleEntity();
        StructureRuleEntity structureRuleEntity = structureRuleDao.getById(structureRuleDTO.getId());
        if (structureRuleEntity == null) {
            throw new BusinessException(ResultCodeEnum.NOT_EXIT);
        }
        BeanUtils.copyProperties(structureRuleEntity, beforeStructureRuleEntity);

        structureRuleEntity
                .setId(structureRuleDTO.getId())
                .setStructureName(structureRuleDTO.getStructureName())
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;
        structureRuleDao.updateById(structureRuleEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(structureRuleDTO))
                    .setBeforeData(JSON.toJSONString(beforeStructureRuleEntity))
                    .setAfterData(JSON.toJSONString(structureRuleEntity))
                    .setOperationActionEnum(OperationActionEnum.MODIFY_STRUCTURE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateStatus(StructureRuleDTO structureRuleDTO) {
        StructureRuleEntity beforeStructureRuleEntity = new StructureRuleEntity();
        StructureRuleEntity structureRuleEntity = structureRuleDao.getById(structureRuleDTO.getId());
        if (structureRuleEntity == null) {
            throw new BusinessException(ResultCodeEnum.NOT_EXIT);
        }
        BeanUtils.copyProperties(structureRuleEntity, beforeStructureRuleEntity);
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = redisCacheMap.get(Integer.parseInt(currentUserId));
        structureRuleEntity
                .setStatus(structureRuleDTO.getStatus())
                .setId(structureRuleDTO.getId())
                .setUpdatedAt(new Date())
                .setUpdatedBy(currentUserId)
                .setUpdatedByName(name)
        ;
        structureRuleDao.updateById(structureRuleEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(structureRuleDTO))
                    .setBeforeData(JSON.toJSONString(beforeStructureRuleEntity))
                    .setAfterData(JSON.toJSONString(structureRuleEntity))
                    .setOperationActionEnum(OperationActionEnum.UPDATE_STRUCTURE_STATUS)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Result queryStructureCode() {
        Integer maxId = structureRuleMapper.queryMaxId();
        if (maxId == null) {
            maxId = 0;
        }
        String code = StructureCodeUtil.numToCode(maxId + 1);
        return Result.success(code);
    }

    @Override
    public Result queryStructureList(QueryDTO<StructureRuleDTO> structureRuleDTOQueryDTO) {
        Page<StructureRuleEntity> page = new Page<>(structureRuleDTOQueryDTO.getPageNo(), structureRuleDTOQueryDTO.getPageSize());
        StructureRuleDTO structureRuleDTO = structureRuleDTOQueryDTO.getCondition();
        IPage<StructureRuleEntity> iPage = structureRuleDao.queryPageByStructureRuleDTO(page, structureRuleDTO);
        List<StructureRuleVO> structureRuleVOList = iPage.getRecords().stream().map(i -> {
            StructureRuleVO structureRuleVO = new StructureRuleVO();
            BeanUtils.copyProperties(i, structureRuleVO);
            structureRuleVO.setCode(StructureCodeUtil.numToCode(i.getId()));
            return structureRuleVO;
        }).collect(Collectors.toList());
        return Result.page(page, structureRuleVOList);
    }

    @Override
    public Result queryById(Integer id) {
        StructureRuleEntity structureRuleEntity = structureRuleDao.getById(id);
        return Result.success(structureRuleEntity);
    }

    @Override
    public String getNameById(Integer id) {
        StructureRuleEntity structureRuleEntity = structureRuleDao.getById(id);
        return StructureCodeUtil.numToCode(id) + "-" + structureRuleEntity.getStructureName();
    }

    @Override
    public Result queryAvailableStructureList() {
        List<StructureRuleEntity> structureRuleEntities = structureRuleDao.queryAvailableStructureList();
        List<StructureRuleVO> structureRuleVOList = structureRuleEntities.stream().map(i -> {
            StructureRuleVO structureRuleVO = new StructureRuleVO();
            BeanUtils.copyProperties(i, structureRuleVO);
            structureRuleVO.setCode(StructureCodeUtil.numToCode(i.getId()));
            return structureRuleVO;
        }).collect(Collectors.toList());
        return Result.success(structureRuleVOList);
    }

}
