package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.mapper.OperationConfigMapper;
import com.navigator.admin.pojo.entity.OperationConfigEntity;
import com.navigator.admin.service.IOperationConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 操作日志配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class OperationConfigServiceImpl extends ServiceImpl<OperationConfigMapper, OperationConfigEntity> implements IOperationConfigService {

    @Override
    public List<OperationConfigEntity> queryOperationConfigByBizCode(String bizCode) {
        QueryWrapper<OperationConfigEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("biz_code", bizCode);
        return baseMapper.selectList(queryWrapper);
    }
}
