package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.admin.dao.magellan.EmployDao;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.service.IIdNameService;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Id-Name服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class IdNameServiceImpl implements IIdNameService {

    @Resource
    private EmployDao employDao;

    @Override
    public void refreshCache() {
        log.info("加载缓存开始。");
        IdNameConverter.setCache(IdNameType.user_id_name, employDao.list(new LambdaQueryWrapper<EmployEntity>().select(EmployEntity::getId, EmployEntity::getName)));
        log.info("加载缓存完成。");
    }
}