package com.navigator.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.dao.TemplateAttributeDao;
import com.navigator.admin.pojo.dto.QueryTemplateAttributeDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.admin.service.ITemplateAttributeService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 模版属性表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class TemplateAttributeServiceImpl implements ITemplateAttributeService {

    @Autowired
    private TemplateAttributeDao templateAttributeDao;

    @Override
    public Result queryTemplateAttributeList(QueryDTO<TemplateAttributeEntity> queryDTO) {

        IPage<TemplateAttributeEntity> iPage = templateAttributeDao.queryTemplateAttributeList(queryDTO);

        return Result.page(iPage);
    }

    @Override
    public TemplateAttributeEntity queryTemplateAttribute(QueryTemplateAttributeDTO queryTemplateAttributeDTO) {
        return templateAttributeDao.queryTemplateAttribute(queryTemplateAttributeDTO);
    }

}
