package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.bo.OperationLogBO;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.entity.DbzRedAlarmEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.vo.ScenesVO;
import com.navigator.admin.service.IOperationDetailService;
import com.navigator.admin.service.IOperationLogService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 17:14
 */
@RestController
@Slf4j
public class OperationLogFacadeImpl implements OperationLogFacade {

    @Resource
    IOperationDetailService operationDetailService;
    @Resource
    IOperationLogService operationLogService;

    @Override
    public Result queryOperationLog(QueryDTO<OperationLogBO> queryDTO) {
        return Result.page(operationLogService.queryOperationLog(queryDTO));
    }

    @Override
    public void recordOperationLog(OperationDetailDTO operationDetailDTO) {
        try {
            operationDetailService.recordOperationLog(operationDetailDTO);
        } catch (Exception e) {
            log.error("OperationLogFacade.recordOperationLog", e.getMessage());
        }
    }

    @Override
    public void recordOperationLogOLD(OperationDetailDTO operationDetailDTO) {
        operationDetailService.recordOperationLog(operationDetailDTO);
    }

    @Override
    public void recordOperationLogDetail(OperationDetailDTO operationDetailDTO) {
        operationDetailService.recordOperationLog(operationDetailDTO);
    }

    @Override
    public Result queryOperationLogByCode(String code, Integer logLevel, Integer id) {
        return Result.success(operationLogService.queryOperationLogByCode(code, logLevel, id));
    }

    @Override
    public void saveOperationDetail(OperationDetailDTO operationDetailDTO) {
        operationDetailService.saveOperationDetail(operationDetailDTO);
    }

    @Override
    public Result<List<ContractOperationEventDTO>> queryContractOperationLog(Integer contractId, Integer logLevel) {
        if (null == contractId || contractId == 0) {
            return Result.success();
        }
        return Result.success(operationLogService.queryContractOperationLog(contractId, logLevel));
    }

    @Override
    public Result queryOperationDetailByReferBizCode(String referBizCode, Integer logLevel) {
        return Result.success(operationDetailService.queryOperationDetailByReferBizCode(referBizCode, logLevel));
    }

    /**
     * 保存日志详细数据
     *
     * @param traceLogDTO
     */
    @Override
    public void saveTraceLog(TraceLogDTO traceLogDTO) {
        try {
            operationLogService.saveTraceLog(traceLogDTO);
        } catch (Exception e) {
            log.error("OperationLogFacade.saveTraceLog", e.getMessage());
        }
    }

    @Override
    public void recordredalarm(DbzRedAlarmEntity redAlarmEntity) {
        try {
            operationLogService.recordredalarm(redAlarmEntity);
        } catch (Exception e) {
            log.error("OperationLogFacade.recordredalarm", e.getMessage());

        }
    }

    @Override
    public void recordsimpleredalarm(Integer referBizId, String bizModule, String memo) {
        DbzRedAlarmEntity redAlarmEntity = new DbzRedAlarmEntity();
        redAlarmEntity.setReferBizId(referBizId)
                .setBizModule(bizModule)
                .setMemo(memo);
        operationLogService.recordredalarm(redAlarmEntity);
    }

    @Override
    public void saveOperationLog(ColumbusOperationDTO columbusOperationDTO) {
        operationLogService.saveOperationLog(columbusOperationDTO);
    }

    @Override
    public void recordMagellanOperationDetail(RecordOperationDetail recordOperationDetail) {
        operationDetailService.recordMagellanOperationDetail(recordOperationDetail);
    }

    @Override
    public void recordColumbusOperationDetail(RecordOperationDetail RecordOperationDetail) {
        operationDetailService.recordColumbusOperationDetail(RecordOperationDetail);
    }

    @Override
    public Result queryLogList(QueryDTO<LogDTO> queryDTO) {
        return operationDetailService.queryLogList(queryDTO);
    }

    @Override
    public Result exportLogList(QueryDTO<LogDTO> queryDTO) {
        return operationDetailService.queryLogList(queryDTO);
    }

    @Override
    public Result queryScenesList() {
        List<OperationActionEnum> operationActionEnums = Arrays.asList(OperationActionEnum.values());

        Map<String, List<ScenesVO.OperationVO>> map = operationActionEnums.stream().map(i -> {
            ScenesVO.OperationVO operationVO = new ScenesVO.OperationVO();
            operationVO
                    .setBizCode(i.getCode())
                    .setOperationName(i.getAction())
                    .setScenes(i.getScenes());
            return operationVO;
        }).filter(k -> StringUtils.isNotBlank(k.getScenes())).distinct().collect(Collectors.groupingBy(ScenesVO.OperationVO::getScenes));

        List<ScenesVO> scenesVOList = operationActionEnums.stream().map(OperationActionEnum::getScenes).filter(StringUtils::isNotBlank).map(i -> {
            ScenesVO scenesVO = new ScenesVO();
            scenesVO.setScenes(i)
                    .setOperationVOList(map.get(i));
            return scenesVO;
        }).distinct().collect(Collectors.toList());
        return Result.success(scenesVOList);
    }

}
