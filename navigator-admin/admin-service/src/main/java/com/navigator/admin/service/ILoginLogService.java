package com.navigator.admin.service;

import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.admin.pojo.entity.LoginLogEntity;

/**
 * 登录日志服务接口
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public interface ILoginLogService {

    /**
     * 保存登录日志
     *
     * @param loginLogDTO 登录日志信息
     * @return 保存结果
     */
    boolean saveLoginLog(LoginLogDTO loginLogDTO);

    /**
     * 根据ID获取登录日志
     *
     * @param id 日志ID
     * @return 登录日志实体
     */
    LoginLogEntity getLoginLogById(Integer id);
}
