package com.navigator.admin.facade.impl.magellan;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.magellan.PowerFacade;
import com.navigator.admin.pojo.dto.PowerDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.PowerEntity;
import com.navigator.admin.pojo.entity.RolePowerEntity;
import com.navigator.admin.pojo.vo.PowerVO;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.magellan.IEmployRoleService;
import com.navigator.admin.service.magellan.IPowerService;
import com.navigator.admin.service.magellan.IRolePowerService;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: No Description
 * Created by YuYong on 2021/11/1 13:44
 */
@RestController
public class PowerFacadeImpl implements PowerFacade {
    @Autowired
    private IPowerService powerService;
    @Autowired
    private IRolePowerService rolePowerService;
    @Autowired
    private IEmployRoleService employRoleService;
    @Resource
    private CommonProperties commonProperties;
    @Autowired
    private ICompanyService companyService;

    @Override
    public Result saveOrUpdatePower(PowerDTO powerDTO) {
        rolePowerService.saveOrUpdateRolePower(powerDTO);
        return Result.success();
    }

    @Override
    public Result queryPowerByRoleId(PowerDTO powerDTO) {
        PowerVO powerVO = new PowerVO();
        List<PowerEntity> powerEntityList = powerService.queryPower();
        Map<Integer, List<PowerEntity>> map = powerEntityList.stream().sorted(Comparator.comparing(PowerEntity::getId)).collect(Collectors.groupingBy(PowerEntity::getParentId));
        List<PowerEntity> allPowerVOList = powerEntityList.stream().map(
                i -> {
                    i.setChildren(map.get(i.getId()));
                    return i;
                }
        ).collect(Collectors.toList());
        List<PowerEntity> list = allPowerVOList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        powerVO.setPowerEntityList(list);

        List<RolePowerEntity> rolePowerEntityList = rolePowerService.queryByRoleId(powerDTO.getRoleId());

        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream().map(RolePowerEntity::getPowerId).collect(Collectors.toList());
            powerVO.setPowerIdList(powerIdList);
        }
        return Result.success(powerVO);
    }

    @Override
    public Result queryPowerByEmployId(Integer employId) {
        List<String> powerCodeList = new ArrayList<>();
        if (employId <= 10) {
            List<PowerEntity> powerEntityList = powerService.queryPower();
            powerCodeList = powerEntityList.stream().map(PowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(String.valueOf(employId));
        if (CollectionUtils.isEmpty(employRoleEntityList)) {
            return Result.success(Collections.emptyList());
        }
        List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());

        if (CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
            List<PowerEntity> powerEntityList = powerService.queryPower();
            powerCodeList = powerEntityList.stream().map(PowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            return Result.success(powerCodeList);
        }
        powerCodeList = getPowerCodeByRoleIdList(roleIdList);
        return Result.success(powerCodeList);
    }

    @Override
    public Result addRolePower(PowerDTO powerDTO) {
        rolePowerService.addRolePower(powerDTO);
        return Result.success();
    }

    @Override
    public Map<Integer, List<String>> queryCompanyPowerByEmployId(Integer employId) {
        Map<Integer, List<String>> companyCodeMap = new HashMap<>();
        List<PowerEntity> powerEntityList = powerService.queryPower();
        List<CompanyEntity> companyEntityList = companyService.queryCompanyList();
        if (employId <= 10) {
            List<String> powerCodeList = powerEntityList.stream().map(PowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            for (CompanyEntity companyEntity : companyEntityList) {
                companyCodeMap.put(companyEntity.getId(), powerCodeList);
            }
            return companyCodeMap;
        }
        List<EmployRoleEntity> employRoleEntityList = employRoleService.getEmployRolesByEmploy(String.valueOf(employId));
        if (CollectionUtils.isEmpty(employRoleEntityList)) {
            return companyCodeMap;
        }
        Map<Integer, List<Integer>> companyRoleIdMap = employRoleEntityList.stream()
                .collect(Collectors.groupingBy(EmployRoleEntity::getCompanyId, Collectors.mapping(EmployRoleEntity::getRoleId, Collectors.toList())));
        List<Integer> commonRole = companyRoleIdMap.get(0) == null ? new ArrayList<>() : companyRoleIdMap.get(0);
        for (CompanyEntity companyEntity : companyEntityList) {
            List<Integer> role = companyRoleIdMap.get(companyEntity.getId()) == null ? new ArrayList<>() : companyRoleIdMap.get(1);
            List<Integer> roleIdList = employRoleEntityList.stream().map(EmployRoleEntity::getRoleId).collect(Collectors.toList());
            if (CollectionUtil.containsAny(roleIdList, commonProperties.getSystemRoleList())) {
                List<String> powerCodeList = powerEntityList.stream().map(PowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
                for (CompanyEntity companyEntity1 : companyEntityList) {
                    companyCodeMap.put(companyEntity1.getId(), powerCodeList);
                }
                return companyCodeMap;
            }
            role.addAll(commonRole);
            List<String> powerCodeList = getPowerCodeByRoleIdList(role);
            companyCodeMap.put(companyEntity.getId(), powerCodeList);
        }
        return companyCodeMap;
    }

    private List<String> getPowerCodeByRoleIdList(List<Integer> roleIdList) {
        List<RolePowerEntity> rolePowerEntityList = rolePowerService.queryByRoleIdList(roleIdList);
        List<String> powerCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rolePowerEntityList)) {
            List<Integer> powerIdList = rolePowerEntityList.stream()
                    .map(RolePowerEntity::getPowerId).distinct().collect(Collectors.toList());
            List<PowerEntity> powerEntityList = powerService.queryPowerByIdList(powerIdList);
            powerCodeList = powerEntityList.stream()
                    .map(PowerEntity::getCode).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return powerCodeList;
    }

}
