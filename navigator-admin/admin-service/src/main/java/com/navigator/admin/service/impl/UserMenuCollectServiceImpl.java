package com.navigator.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.dao.UserMenuCollectDao;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.entity.CMenuEntity;
import com.navigator.admin.pojo.entity.MenuEntity;
import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.admin.pojo.vo.UserMenuCollectVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.admin.service.UserMenuCollectService;
import com.navigator.admin.service.columbus.ICEmployCustomerService;
import com.navigator.admin.service.columbus.ICMenuService;
import com.navigator.admin.service.magellan.IMenuService;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:40
 **/
@Service
@Slf4j
public class UserMenuCollectServiceImpl implements UserMenuCollectService {
    @Resource
    private UserMenuCollectDao userMenuCollectDao;

    @Resource
    private IMenuService menuService;

    @Resource
    private ICMenuService cMenuService;
    @Resource
    private ICEmployCustomerService icEmployCustomerService;

    @Override
    public UserMenuCollectEntity saveUserMenuCollect(UserMenuCollectEntity userMenuCollect) {
        String currentUserId = JwtUtils.getCurrentUserId();
        Integer customerId = SystemEnum.COLUMBUS.getValue() == userMenuCollect.getSystem() ? userMenuCollect.getCustomerId() : 0;
        List<UserMenuCollectEntity> menuCollectList = userMenuCollectDao.getMenuCollectList(Integer.valueOf(currentUserId), customerId, userMenuCollect.getSystem());
        List<UserMenuCollectEntity> sameMenuList = menuCollectList.stream().filter(it -> {
            return it.getMenuId().equals(userMenuCollect.getMenuId()) && it.getCategoryId().equals(userMenuCollect.getCategoryId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sameMenuList)) {
            throw new BusinessException(ResultCodeEnum.MENU_USER_HAS_COLLECTED, userMenuCollect.getName());
        }
        Integer sort = menuCollectList.stream().map(UserMenuCollectEntity::getSort)
                .max(Integer::compare)
                .orElse(0);
        userMenuCollect.setUserId(Integer.valueOf(currentUserId))
                .setCustomerId(customerId)
                .setSort(sort + 1)
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setNewMenuName(userMenuCollect.getNewMenuName().trim())
                .setCreatedBy(currentUserId)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now());
        userMenuCollectDao.save(userMenuCollect);
        return userMenuCollect;
    }

    @Override
    public UserMenuCollectEntity updateUserMenuCollect(UserMenuCollectEntity userMenuCollect) {
        UserMenuCollectEntity menuCollectEntity = userMenuCollectDao.getById(userMenuCollect.getId());
        if (null == menuCollectEntity) {
            throw new BusinessException(ResultCodeEnum.MENU_USER_COLLECT_NOT_EXIST);
        }
        if (userMenuCollectDao.judgeExistSameMenuName(userMenuCollect.getNewMenuName().trim(), userMenuCollect.getId(), menuCollectEntity.getUserId())) {
            throw new BusinessException(ResultCodeEnum.MENU_USER_COLLECT_NAME_SAME);
        }
        menuCollectEntity.setNewMenuName(userMenuCollect.getNewMenuName().trim())
                .setUpdatedBy(JwtUtils.getCurrentUserId())
                .setUpdatedAt(DateTimeUtil.now());
        userMenuCollectDao.updateById(menuCollectEntity);
        return menuCollectEntity;
    }

    @Override
    public Result deleteCollectMenu(Integer id) {
        UserMenuCollectEntity menuCollectEntity = userMenuCollectDao.getById(id);
        if (null == menuCollectEntity) {
            throw new BusinessException(ResultCodeEnum.MENU_USER_COLLECT_NOT_EXIST);
        }
        userMenuCollectDao.deleteMenuCollect(id);
        return Result.success("删除成功");
    }

    @Override
    public Result sortCollectMenu(List<Integer> menuCollectIdList) {
        if (CollectionUtils.isEmpty(menuCollectIdList)) {
            return Result.success("处理成功");
        }
        String currentUserId = JwtUtils.getCurrentUserId();
        for (Integer menuCollectId : menuCollectIdList) {
            UserMenuCollectEntity menuCollectEntity = userMenuCollectDao.getById(menuCollectId);
            if (null == menuCollectEntity) {
                throw new BusinessException(ResultCodeEnum.MENU_USER_COLLECT_NOT_EXIST);
            }
            menuCollectEntity.setSort(menuCollectIdList.size() - menuCollectIdList.indexOf(menuCollectId))
                    .setUpdatedAt(DateTimeUtil.now())
                    .setUpdatedBy(currentUserId);
            userMenuCollectDao.updateById(menuCollectEntity);
        }
        return Result.success("处理成功");
    }

    @Override
    public List<MenuDetailVO> getUserPowerMenuList(Integer categoryId, Integer customerId, Integer system) {
        String currentUserId = JwtUtils.getCurrentUserId();
        List<MenuDetailVO> menuDetailList = this.getChildrenMenuByUserId(currentUserId, categoryId, customerId, system);
        if (CollectionUtils.isEmpty(menuDetailList)) {
            return menuDetailList;
        }
        String categoryName = categoryId == 0 ? "默认" : IdNameConverter.getName(IdNameType.category_id_name, categoryId.toString());
        List<UserMenuCollectEntity> menuCollectList = userMenuCollectDao.getMenuCollectList(Integer.valueOf(currentUserId), customerId, system);
        List<String> collectList = new ArrayList<>();
        menuCollectList.forEach(item -> collectList.add(String.format("%s:%s", item.getCategoryId().toString(), item.getMenuId().toString())));
        for (MenuDetailVO menuDetail : menuDetailList) {
            menuDetail.setCategoryName(0 == menuDetail.getCategoryId() ? "默认" : categoryName);
            String key = String.format("%s:%s", menuDetail.getCategoryId().toString(), menuDetail.getId().toString());
            menuDetail.setHasCollected(collectList.contains(key));
        }
        return menuDetailList;
    }

    @Override
    public List<UserMenuCollectVO> getUserCollectMenuList(Integer categoryId, Integer customerId, Integer system) {
        String currentUserId = JwtUtils.getCurrentUserId();
        customerId = SystemEnum.COLUMBUS.getValue() == system ? customerId : 0;
        List<UserMenuCollectEntity> menuCollectList = userMenuCollectDao.getMenuCollectList(Integer.valueOf(currentUserId), customerId, system);
        menuCollectList = menuCollectList.stream().filter(it -> {
                    return categoryId.equals(it.getCategoryId()) || 0 == it.getCategoryId();
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(menuCollectList)) {
            return new ArrayList<>();
        }
        List<MenuDetailVO> menuDetailList = this.getChildrenMenuByUserId(currentUserId, categoryId, customerId, system);
        List<Integer> userMenuIdList = menuDetailList.stream().map(MenuDetailVO::getId).collect(Collectors.toList());
        return menuCollectList.stream().map(it -> {
            UserMenuCollectVO menuCollectVO = BeanConvertUtils.convert(UserMenuCollectVO.class, it);
            String categoryName = 0 == it.getCategoryId() ? "默认" : IdNameConverter.getName(IdNameType.category_id_name, it.getCategoryId().toString());
            return menuCollectVO.setCategoryName(categoryName)
                    .setHasPower(userMenuIdList.contains(it.getMenuId()));
        }).collect(Collectors.toList());
    }

    /**
     * 根据系统、品类获取某用户有权限的二级菜单列表
     *
     * @param currentUserId
     * @param categoryId
     * @param system
     * @return
     */
    private List<MenuDetailVO> getChildrenMenuByUserId(String currentUserId, Integer categoryId, Integer customerId, Integer system) {
        List<MenuDetailVO> menuDetailList = new ArrayList<>(16);
        if (SystemEnum.MAGELLAN.getValue().equals(system)) {
            List<MenuEntity> menuList = menuService.getAuthMenuList(Integer.parseInt(currentUserId), categoryId);
            menuList.forEach(item -> menuDetailList.add(BeanUtil.copyProperties(item, MenuDetailVO.class)));
        } else {
            List<CMenuEntity> menuList = cMenuService.getAuthMenuList(Integer.parseInt(currentUserId), categoryId, customerId);
            menuList.forEach(item -> menuDetailList.add(BeanUtil.copyProperties(item, MenuDetailVO.class)));
        }
        return menuDetailList.stream()
                .filter(menuDetailVO -> menuDetailVO.getLevel() == 1)
                .collect(Collectors.toList());
//        List<MenuDetailVO> menuDetailTreeList = SystemEnum.MAGELLAN.getValue() == system ? this.getMagellanMenuByUserId(currentUserId, categoryId)
//                : this.getColumbusMenuByUserId(currentUserId, categoryId, customerId);
//        return menuDetailTreeList.stream()
//                .filter(menuDetailVO -> {
//                    return CollectionUtils.isNotEmpty(menuDetailVO.getChildren());
//                })
//                .flatMap(it -> {
//                    return it.getChildren().stream();
//                }).collect(Collectors.toList());
    }

    private List<MenuDetailVO> getMagellanMenuByUserId(String currentUserId, Integer categoryId) {
        MenuVO menuVO = menuService.getMenuByEmployId(new RoleDTO().setEmployId(currentUserId));
        List<MenuDetailVO> menuDetailTreeList = new ArrayList<>();
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(categoryId)) {
            menuDetailTreeList = menuVO.getSbmList();
        } else if (GoodsCategoryEnum.OSM_OIL.getValue().equals(categoryId)) {
            menuDetailTreeList = menuVO.getSboList();
        } else {
            menuDetailTreeList = menuVO.getDefaultList();
        }
        return menuDetailTreeList;
    }

    private List<MenuDetailVO> getColumbusMenuByUserId(String currentUserId, Integer categoryId, Integer customerId) {
        //查询菜单
        CRoleDTO roleDTO = new CRoleDTO()
                .setEmployId(currentUserId)
                .setCustomerId(customerId);
        CMenuVO menuVO = cMenuService.getMenuByEmployIdV2(roleDTO);
        List<MenuDetailVO> menuDetailTreeList = new ArrayList<>();
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(categoryId)) {
            menuDetailTreeList = menuVO.getSbmList();
        } else if (GoodsCategoryEnum.OSM_OIL.getValue().equals(categoryId)) {
            menuDetailTreeList = menuVO.getSboList();
        } else {
            menuDetailTreeList = menuVO.getDefaultList();
        }
        return menuDetailTreeList;
    }

}
