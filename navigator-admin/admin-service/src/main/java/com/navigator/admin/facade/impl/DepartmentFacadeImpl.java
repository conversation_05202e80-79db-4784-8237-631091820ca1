package com.navigator.admin.facade.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.admin.facade.DepartmentFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.DepartmentEntity;
import com.navigator.admin.service.ICompanyService;
import com.navigator.admin.service.IDepartmentService;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON>ong on 2021/11/3 11:18
 */
@RestController
public class DepartmentFacadeImpl implements DepartmentFacade {
    @Autowired
    private IDepartmentService iDepartmentService;


    @Override
    public Result getDepartmentList(Integer system) {
        List<DepartmentEntity> levelDepartments = iDepartmentService.getDepartmentList(system);
        return Result.success(CollectionUtil.isEmpty(levelDepartments) ? Collections.emptyList() : levelDepartments);
    }

    @Override
    public List<DepartmentEntity> getDepartmentLeaderId(List<Integer> departmentId) {
        return iDepartmentService.getDepartmentLeaderId(departmentId);
    }


}
