package com.navigator.admin.service.magellan;

import com.navigator.admin.pojo.dto.EmployRoleDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.RoleQueryDTO;
import com.navigator.admin.pojo.entity.RoleDefEntity;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface IRoleDefService {

    RoleDefEntity getRoleDefById(Integer roleDefId);

    RoleDefEntity saveOrUpdate(RoleDTO roleDTO);

    Result queryRoleList(QueryDTO<RoleQueryDTO> queryDTO);

    RoleQueryVO queryFactoryListByRoleDefIdList(List<Integer> roleDefIdList);

    List<RoleDefEntity> queryAllList();

    RoleQueryVO queryRoleDefDetail(Integer roleDefId);

    List<RoleQueryVO> queryRoleByFactory(EmployRoleDTO employRoleDTO);

    List<RoleDefEntity> getRoleDefByType(String type);

    RoleDefEntity getRoleDefByName(String name);

    RoleDefEntity save(RoleDTO roleDTO);

    List<RoleDefEntity> queryRoleGroupList();

    void copyPermission(RoleDTO roleDTO);

}
