package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.EmployBusinessMapper;
import com.navigator.admin.pojo.entity.EmployBusinessEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

@Dao
public class EmployBusinessDao extends BaseDaoImpl<EmployBusinessMapper, EmployBusinessEntity> {
    public List<EmployBusinessEntity> queryListByEmployId(String employId) {
        return list(Wrappers.<EmployBusinessEntity>lambdaQuery()
                .eq(EmployBusinessEntity::getEmployId, employId)
                .eq(EmployBusinessEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()))
                ;
    }

    public void deleteByEmployId(Integer employId) {
        remove(Wrappers.<EmployBusinessEntity>lambdaQuery()
                .eq(EmployBusinessEntity::getEmployId, employId));
    }
}
