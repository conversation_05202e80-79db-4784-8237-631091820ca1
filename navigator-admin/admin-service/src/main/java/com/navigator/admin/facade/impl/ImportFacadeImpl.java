package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.ImportFacade;
import com.navigator.admin.service.importer.ImportService;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Slf4j
@RestController
public class ImportFacadeImpl implements ImportFacade {
    @Resource
    private ImportService importService;

    @Override
    public Result<String> importRole(MultipartFile file) {
        return Result.success(importService.importRole(file));
    }

    @Override
    public Result<String> importRoleMenu(MultipartFile file) {
        return Result.success(importService.importRoleMenu(file));
    }

    @Override
    public Result<String> importRolePower(MultipartFile file) {
        return Result.success(importService.importRolePower(file));
    }

    @Override
    public Result<String> importCLBRole(MultipartFile file) {
        return Result.success(importService.importCLBRole(file));
    }

    @Override
    public Result<String> importCLBRoleMenu(MultipartFile file) {
        return Result.success(importService.importCLBRoleMenu(file));
    }

    @Override
    public Result<String> importCLBRolePower(MultipartFile file) {
        return Result.success(importService.importCLBRolePower(file));
    }
}
