package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.AnnouncementFacade;
import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.admin.pojo.entity.AnnouncementEntity;
import com.navigator.admin.service.AnnouncementService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class AnnouncementFacadeImpl implements AnnouncementFacade {
    @Autowired
    private AnnouncementService announcementService;

    @Override
    public Result save(AnnouncementDTO announcementDTO) {
        announcementService.save(announcementDTO);
        return Result.success();
    }

    @Override
    public Result modify(AnnouncementDTO announcementDTO) {
        announcementService.modify(announcementDTO);
        return Result.success();
    }

    @Override
    public Result queryAnnouncementDetail(AnnouncementDTO announcementDTO) {
        AnnouncementEntity announcementEntity = announcementService.queryAnnouncementDetail(announcementDTO);
        return Result.success(announcementEntity);
    }

    @Override
    public Result queryAnnouncementList(QueryDTO<AnnouncementDTO> queryDTO) {
       return announcementService.queryAnnouncementList(queryDTO);
    }

    @Override
    public Result queryAnnouncementBySystem(Integer systemId) {
        List<AnnouncementEntity> list = announcementService.queryAnnouncementBySystem(systemId);
        return Result.success(list);
    }

}
