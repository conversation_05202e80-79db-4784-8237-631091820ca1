package com.navigator.admin.dao.magellan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.EmployRoleMapper;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.qo.EmployRoleQO;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/8 14:15
 */
@Dao
public class EmployRoleDao extends BaseDaoImpl<EmployRoleMapper, EmployRoleEntity> {

    public List<EmployRoleEntity> getEmployRolesByEmploy(String employId) {
        return this.list(new LambdaQueryWrapper<EmployRoleEntity>().eq(EmployRoleEntity::getEmployId, employId)
                .eq(EmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public List<EmployRoleEntity> getEmployRolesByRoleIds(List<Integer> roleIds) {
        return this.list(Wrappers.<EmployRoleEntity>lambdaQuery().in(EmployRoleEntity::getRoleId, roleIds)
                .eq(EmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public void deleteByEmployId(Integer employId) {
        remove(Wrappers.<EmployRoleEntity>lambdaQuery()
                .eq(EmployRoleEntity::getEmployId, employId));
    }

    public List<EmployRoleEntity> queryByRoleDefId(Integer roleDefId) {
        return list(Wrappers.<EmployRoleEntity>lambdaQuery()
                .eq(EmployRoleEntity::getRoleDefId, roleDefId)
                .eq(EmployRoleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public void delete(EmployRoleEntity employRoleEntity) {
        remove(Wrappers.<EmployRoleEntity>lambdaQuery()
                .eq(EmployRoleEntity::getEmployId, employRoleEntity.getEmployId())
                .eq(EmployRoleEntity::getRoleDefId, employRoleEntity.getRoleDefId())
        );
    }

    /**
     * 获取角色ID列表
     *
     * @param condition
     * @return
     */
    public List<Integer> queryRoleIdListByEmployId(EmployRoleQO condition) {
        List<EmployRoleEntity> list = this.list(EmployRoleEntity.lqw(condition));
        List<Integer> result = new ArrayList<>();
        list.forEach(item -> result.add(item.getRoleId()));
        return result;
    }

    public void updateDeletedByRoleIdAndEmployId(Integer roleId, Integer employId) {
        this.update(Wrappers.<EmployRoleEntity>lambdaUpdate()
                .set(EmployRoleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(EmployRoleEntity::getUpdatedAt, DateTimeUtil.now())
                .eq(EmployRoleEntity::getRoleId, roleId)
                .eq(EmployRoleEntity::getEmployId, employId));
    }

    public void updateDeletedByIds(List<Integer> ids) {
        this.update(Wrappers.<EmployRoleEntity>lambdaUpdate()
                .set(EmployRoleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(EmployRoleEntity::getUpdatedAt, DateTimeUtil.now())
                .in(EmployRoleEntity::getId, ids));
    }
}
