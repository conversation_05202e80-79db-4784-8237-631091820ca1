package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.UserMenuCollectFacade;
import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.UserMenuCollectVO;
import com.navigator.admin.service.UserMenuCollectService;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-05 15:43
 **/
@RestController
public class UserMenuCollectFacadeImpl implements UserMenuCollectFacade {
    @Autowired
    private UserMenuCollectService userMenuCollectService;

    @Override
    public Result saveUserMenuCollect(UserMenuCollectEntity userMenuCollect) {
        return Result.success(userMenuCollectService.saveUserMenuCollect(userMenuCollect));
    }

    @Override
    public Result updateUserMenuCollect(UserMenuCollectEntity userMenuCollect) {
        return Result.success(userMenuCollectService.updateUserMenuCollect(userMenuCollect));
    }

    @Override
    public Result deleteCollectMenu(Integer id) {
        return userMenuCollectService.deleteCollectMenu(id);
    }

    @Override
    public Result sortCollectMenu(List<Integer> menuCollectIdList) {
        return userMenuCollectService.sortCollectMenu(menuCollectIdList);
    }

    @Override
    public List<MenuDetailVO> getUserPowerMenuList(Integer categoryId, Integer customerId, Integer system) {
        return userMenuCollectService.getUserPowerMenuList(categoryId, customerId, system);
    }

    @Override
    public List<UserMenuCollectVO> getUserCollectMenuList(Integer categoryId, Integer customerId, Integer system) {
        return userMenuCollectService.getUserCollectMenuList(categoryId, customerId, system);
    }
}
