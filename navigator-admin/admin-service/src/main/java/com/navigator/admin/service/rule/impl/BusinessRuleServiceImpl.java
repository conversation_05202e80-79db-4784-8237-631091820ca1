package com.navigator.admin.service.rule.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.dao.rule.BusinessRuleConditionDao;
import com.navigator.admin.dao.rule.BusinessRuleDao;
import com.navigator.admin.dao.rule.BusinessRuleDetailDao;
import com.navigator.admin.dao.rule.RuleVariableDao;
import com.navigator.admin.pojo.dto.rule.ConditionVariableDTO;
import com.navigator.admin.pojo.dto.rule.RuleCreateDTO;
import com.navigator.admin.pojo.dto.rule.RuleQueryDTO;
import com.navigator.admin.pojo.dto.rule.RuleScriptDTO;
import com.navigator.admin.pojo.entity.rule.BusinessRuleConditionEntity;
import com.navigator.admin.pojo.entity.rule.BusinessRuleDetailEntity;
import com.navigator.admin.pojo.entity.rule.BusinessRuleEntity;
import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.admin.service.rule.IBusinessRuleService;
import com.navigator.admin.service.rule.IRuleDictItemService;
import com.navigator.bisiness.enums.LogicRelationEnum;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 15:35
 **/
@Service
@Slf4j
public class BusinessRuleServiceImpl implements IBusinessRuleService {
    @Autowired
    private BusinessRuleDao businessRuleDao;
    @Autowired
    private BusinessRuleDetailDao businessRuleDetailDao;
    @Autowired
    private BusinessRuleConditionDao businessRuleConditionDao;
    @Autowired
    private RuleVariableDao ruleVariableDao;
    @Autowired
    private IRuleDictItemService ruleDictItemService;
    @Autowired
    private RuleContentBuilder ruleContentBuilder;

    @Override
    public BusinessRuleEntity recordBusinessRule(RuleCreateDTO ruleCreateDTO) {
        String referCode = ruleCreateDTO.getReferCode();
        String referType = ruleCreateDTO.getReferType();
        List<ConditionVariableDTO> conditionVariableList = ruleCreateDTO.getConditionVariableList();
        String moduleType = ruleCreateDTO.getModuleType();
        String systemId = ruleCreateDTO.getSystemId();
        BusinessRuleEntity businessRuleEntity = businessRuleDao.getRuleByBusinessCode(referCode, referType, moduleType, systemId);
        if (CollectionUtils.isEmpty(conditionVariableList)) {
            if (null != businessRuleEntity) {
                businessRuleDao.dropBusinessRule(referCode, referType, moduleType, systemId);
                businessRuleDetailDao.dropBusinessRuleDetail(referCode, referType, moduleType, systemId);
            }
            return new BusinessRuleEntity().setRuleCode("").setRuleInfo("").setConditionInfo("");
        }
        List<String> variableList = conditionVariableList.stream().map(ConditionVariableDTO::getConditionVariable).distinct().collect(Collectors.toList());
//        RuleScriptDTO jointRuleInfo = this.jointConditionInfo(conditionVariableList);
        RuleScriptDTO jointRuleInfo = ruleContentBuilder.jointConditionInfo(conditionVariableList);
        String ruleCode = StringUtils.isNotBlank(businessRuleEntity.getRuleCode()) ?
                businessRuleEntity.getRuleCode() : UUID.randomUUID().toString();
        businessRuleEntity
                .setRuleCode(ruleCode)
                .setReferCode(referCode)
                .setReferType(referType)
                .setModuleType(moduleType)
                .setSystemId(systemId)
                .setConditionVariable(StringUtils.join(variableList, ","))
                //加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
                .setConditionInfo(jointRuleInfo.getJointConditionInfo())
                .setRuleInfo(jointRuleInfo.getJointRuleInfo());
        if (null == businessRuleEntity.getId()) {
            businessRuleDao.save(businessRuleEntity);
        } else {
            businessRuleDao.updateById(businessRuleEntity);
        }
        //规则元素信息记录同步
        this.recordRuleDetail(businessRuleEntity, conditionVariableList);

        return businessRuleEntity;
    }

    /**
     * 规则元素信息记录同步
     *
     * @param businessRuleEntity
     * @param conditionVariableList
     */
    public void recordRuleDetail(BusinessRuleEntity businessRuleEntity, List<ConditionVariableDTO> conditionVariableList) {
        businessRuleDetailDao.dropBusinessRuleDetail(businessRuleEntity.getReferCode(), businessRuleEntity.getReferType(), businessRuleEntity.getModuleType(), businessRuleEntity.getSystemId());
        if (CollectionUtils.isEmpty(conditionVariableList)) {
            return;
        }
        Timestamp now = DateTimeUtil.now();
        for (ConditionVariableDTO conditionVariableDTO : conditionVariableList) {
            BusinessRuleConditionEntity ruleCondition = businessRuleConditionDao.getRuleCondition(conditionVariableDTO.getRuleInfo(), businessRuleEntity.getModuleType(), businessRuleEntity.getSystemId());
            if (null == ruleCondition) {
                ruleCondition = new BusinessRuleConditionEntity()
                        .setConditionVariableId(conditionVariableDTO.getConditionVariableId())
                        .setConditionVariable(conditionVariableDTO.getConditionVariable())
                        .setRuleInfo(conditionVariableDTO.getRuleInfo())
                        //运算关系
                        .setPatternRelation(conditionVariableDTO.getPatternRelation())
                        //加载条件阈值
                        .setConditionValue(PatternRelationEnum.getContainRelation().contains(conditionVariableDTO.getPatternRelation()) ?
                                StringUtils.join(conditionVariableDTO.getConditionValueList(), ",") : conditionVariableDTO.getConditionValue())
                        .setConditionValueInfo(StringUtils.join(conditionVariableDTO.getConditionDescList(), ","))
                        .setRuleDesc(conditionVariableDTO.getConditionVariableInfo() + conditionVariableDTO.getPatternRelationInfo() + conditionVariableDTO.getConditionValueInfo())
                        .setModuleType(businessRuleEntity.getModuleType())
                        .setSystemId(businessRuleEntity.getSystemId())
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setCreatedAt(now)
                        .setUpdatedAt(now);
                businessRuleConditionDao.save(ruleCondition);
            }
            BusinessRuleDetailEntity ruleDetailEntity = new BusinessRuleDetailEntity()
                    .setRuleCode(businessRuleEntity.getRuleCode())
                    .setReferCode(businessRuleEntity.getReferCode())
                    .setReferType(businessRuleEntity.getReferType())
                    //加载条件阈值
                    .setRuleConditionId(ruleCondition.getId())
                    .setConditionValueInfo(StringUtils.join(conditionVariableDTO.getConditionDescList(), ","))
                    .setRuleDesc(conditionVariableDTO.getLogicRelationInfo() + conditionVariableDTO.getConditionVariableInfo() + conditionVariableDTO.getPatternRelationInfo() + conditionVariableDTO.getConditionValueInfo())
//                    .setConditionValueIds(StringUtils.join(it.getConditionValueIdList(), ","))
                    .setLevel(conditionVariableDTO.getLevel())
                    .setSort(conditionVariableDTO.getSort())
                    .setLogicRelation(conditionVariableDTO.getLogicRelation())
                    //规则脚本
                    .setRuleInfo(conditionVariableDTO.getRuleInfo())
                    .setModuleType(businessRuleEntity.getModuleType())
                    .setSystemId(businessRuleEntity.getSystemId())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedAt(now)
                    .setUpdatedAt(now);
            businessRuleDetailDao.save(ruleDetailEntity);
        }
    }

    @Override
    public BusinessRuleEntity getRuleByBusinessCode(String referCode, String referType, String moduleType, String systemId) {
        return businessRuleDao.getRuleByBusinessCode(referCode, referType, moduleType, systemId);
    }

    @Override
    public BusinessRuleEntity getRuleDetailEntityByCode(String referCode, String referType, String moduleType, String systemId) {
        BusinessRuleEntity templateRuleEntity = businessRuleDao.getRuleByBusinessCode(referCode, referType, moduleType, systemId);
        if (null == templateRuleEntity) {
            return templateRuleEntity;
        }
        List<BusinessRuleDetailEntity> ruleDetailEntityList = businessRuleDetailDao.getRuleDetailListByRuleCode(templateRuleEntity.getRuleCode());
        return templateRuleEntity.setRuleDetailEntityList(ruleDetailEntityList);
    }

    @Override
    public BusinessRuleEntity getRuleDetailByBusinessCode(RuleQueryDTO ruleQueryDTO) {
        BusinessRuleEntity templateRuleEntity = businessRuleDao.getRuleByBusinessCode(ruleQueryDTO.getReferCode(), ruleQueryDTO.getReferType(), ruleQueryDTO.getModuleType(), ruleQueryDTO.getSystemId());
        if (null == templateRuleEntity) {
            return templateRuleEntity;
        }
        List<BusinessRuleDetailEntity> ruleDetailEntityList = businessRuleDetailDao.getRuleDetailListByRuleCode(templateRuleEntity.getRuleCode());
        List<ConditionVariableDTO> conditionVariableList = ruleDetailEntityList.stream()
                .map(ruleDetailEntity -> {
                    ConditionVariableDTO conditionVariableDTO = new ConditionVariableDTO();
                    // 变量名称
                    BusinessRuleConditionEntity ruleConditionEntity = businessRuleConditionDao.getById(ruleDetailEntity.getRuleConditionId());
                    if (null != ruleConditionEntity) {
                        conditionVariableDTO = BeanConvertUtils.convert(ConditionVariableDTO.class, ruleConditionEntity);
                        if (StringUtils.isNotBlank(conditionVariableDTO.getConditionValueIds())) {
                            List<Integer> conditionValueIdList = Arrays.stream(conditionVariableDTO.getConditionValueIds().split(","))
                                    .map(Integer::valueOf).collect(Collectors.toList());
                            conditionVariableDTO.setConditionValueIdList(conditionValueIdList);
                        }
                        List<String> conditionValueList = Arrays.stream(conditionVariableDTO.getConditionValue().split(",")).collect(Collectors.toList());
                        conditionVariableDTO.setConditionValueList(conditionValueList);
                        conditionVariableDTO.setPatternRelationInfo(PatternRelationEnum.getByCode(ruleConditionEntity.getPatternRelation()).getDescription());
                        RuleVariableEntity variableEntity = ruleVariableDao.getById(ruleConditionEntity.getConditionVariableId());
                        if (null != variableEntity) {
                            conditionVariableDTO.setConditionVariableInfo(variableEntity.getName());
                        }
                    }
                    //阈值描述信息-前端展示
//                    List<DictItemEntity> dictItemEntityList = ruleDictItemService.getDictItemById(conditionValueIdList);
//                    String conditionValueInfo = dictItemEntityList.stream().map(DictItemEntity::getItemName).collect(Collectors.joining(","));
                    List<String> conditionDescList = Arrays.stream(conditionVariableDTO.getConditionValueInfo().split(",")).collect(Collectors.toList());
                    conditionVariableDTO.setLevel(ruleDetailEntity.getLevel())
                            .setSort(ruleDetailEntity.getSort())
                            .setConditionValueInfo(ruleDetailEntity.getConditionValueInfo())
                            .setConditionDescList(conditionDescList)
                            .setLogicRelation(ruleDetailEntity.getLogicRelation())
                            .setLogicRelationInfo(LogicRelationEnum.getByCode(ruleDetailEntity.getLogicRelation()).getDescription());
                    return conditionVariableDTO;
                })
                .collect(Collectors.toList());
        return templateRuleEntity.setConditionVariableList(conditionVariableList);
    }


}
