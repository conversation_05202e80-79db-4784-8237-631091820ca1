package com.navigator.admin.facade.impl.rule;

import com.navigator.admin.facade.rule.RuleVariableFacade;
import com.navigator.admin.pojo.entity.rule.RuleVariableEntity;
import com.navigator.admin.service.rule.IRuleVariableService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-07 11:42
 **/
@RestController
public class RuleVariableFacadeImpl implements RuleVariableFacade {
    @Autowired
    private IRuleVariableService ruleVariableService;

    @Override
    public Boolean updateVariable(RuleVariableEntity variableEntity) {
        return ruleVariableService.updateVariable(variableEntity);
    }

    @Override
    public Result queryVariableByCondition(QueryDTO<RuleVariableEntity> queryDTO) {
        return ruleVariableService.queryVariableByCondition(queryDTO);
    }

    @Override
    public Result queryExportVariableList(RuleVariableEntity queryDTO) {
        return Result.success(ruleVariableService.queryExportVariableList(queryDTO));
    }

    @Override
    public List<RuleVariableEntity> getAllVariableList(Integer isCondition, Integer isKey, String moduleType, String systemId) {
        return ruleVariableService.getAllVariableList(isCondition, isKey, moduleType, systemId);
    }

    @Override
    public List<RuleVariableEntity> getAllVariableEntityList(String moduleType, String systemId) {
        return ruleVariableService.getAllVariableEntityList(moduleType, systemId);
    }
}
