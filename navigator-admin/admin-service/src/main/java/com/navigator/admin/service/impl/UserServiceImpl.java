package com.navigator.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.TemplateFacade;
import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.pojo.dto.ColumbusOperationDTO;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.SignatureDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.admin.service.IUserService;
import com.navigator.admin.service.columbus.ICEmployService;
import com.navigator.admin.service.magellan.IEmployService;
import com.navigator.admin.service.systemrule.SystemRuleService;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserServiceImpl implements IUserService {
    @Autowired
    private IEmployService employService;
    @Autowired
    private ICEmployService icEmployService;
    @Resource
    private TemplateFacade templateFacade;
    @Autowired
    private FileProcessFacade fileProcessFacade;
    @Autowired
    private FileBusinessFacade fileBusinessFacade;
    @Autowired
    private SystemRuleService systemRuleService;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CEmployCustomerFacade cEmployCustomerFacade;

    @Override
    public Result<String> login(LoginDTO loginDTO) {
        List<EmployEntity> employEntityList = employService.getEmployByEmail(loginDTO.getEmail(), SystemEnum.MAGELLAN.getValue());
        if (CollectionUtil.isEmpty(employEntityList)) {
            log.error("========> login  failed ,用户不存在");
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        EmployEntity employEntity = employEntityList.get(0);
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        if (!passwordEncoder.matches(loginDTO.getPassword(), employEntity.getPassword())) {
            log.error("========> login  failed ,用户密码错误");
            throw new BusinessException(ResultCodeEnum.USER_PASSWORD_ERROR);
        }
        if (DisableStatusEnum.DISABLE.getValue().equals(employEntity.getStatus())) {
            log.error("========> login  failed ,用户被禁用");
            throw new BusinessException(ResultCodeEnum.USER_FORBIDDEN);
        }
        return Result.success(employEntity.getPhone());
    }

    @Override
    public Result loginByPhone(LoginDTO loginDTO) {
        List<CEmployEntity> cEmployEntityList = icEmployService.getEmployByPhone(loginDTO.getPhone(), null);
        if (CollectionUtils.isEmpty(cEmployEntityList)) {
            log.error("========> login  failed ,用户不存在");
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        List<CEmployEntity> cEmployEntities = cEmployEntityList.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
        if (cEmployEntities.size() == 0) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
        }
        if (cEmployEntities.size() > 1) {
            log.error("========> login  failed ,手机绑定账号不唯一,请联系管理员!");
            throw new BusinessException(ResultCodeEnum.EMPLOY_NOT_ONLY_ONE);
        }
        CEmployEntity cEmployEntity = cEmployEntities.get(0);
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        if (!passwordEncoder.matches(loginDTO.getPassword(), cEmployEntity.getPassword())) {
            log.error("========> login  failed ,用户密码错误");
            throw new BusinessException(ResultCodeEnum.USER_PASSWORD_ERROR);
        }
        Date date = verifyPasswordExpired(DateTimeUtil.parseTimeStamp0000(cEmployEntity.getUpdatedPasswordTime()));
        String time = DateTimeUtil.formatDateStringCN(date);
        if (new Date().after(date)) {
            log.error("========> login  failed ,用户密码过期");
            return Result.failure(ResultCodeEnum.USER_PASSWORD_EXPIRED, JSON.toJSON(time));
        }
        CustomerEntity customerEntity = customerFacade.getCustomerById(cEmployEntityList.get(0).getCustomerId());
        if (customerEntity != null && !GeneralEnum.YES.getValue().equals(customerEntity.getIsColumbus())) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
        }

        return Result.success(cEmployEntity.getPhone());
    }

    @Override
    public Result completeSignature(SignatureDTO signatureDTO) {
        String userId = JwtUtils.getCurrentUserId();
        CEmployEntity cEmployEntity = icEmployService.getEmployById(Integer.valueOf(userId));
        if (cEmployEntity == null) {
            throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
        }
        // 2022-11-30 需求变更
/*        TemplateEntity loginSignature = templateFacade.getLoginSignature();
        String templateContent = loginSignature.getContent();
        // 3、数据对象->map
        Map<String, Object> dataMap = BeanUtil.beanToMap(signatureDTO);
        String bizContent = "";
        // 4、渲染模板
        try {
            bizContent = TemplateRenderUtil.templateRender(dataMap, templateContent);
        } catch (Exception e) {
            bizContent = templateContent;
            log.error(e.getMessage());
        }
        //生成pdf
        HtmlInfoDTO pdfInfoDTO = new HtmlInfoDTO()
                .setHtmlUrl(FilePathUtil.genLoginPath(FilePathType.HTML, userId))
                .setHtmlContent(bizContent)
                .setModuleType(ModuleTypeEnum.LOGIN.getModule());
        FileBaseInfoDTO fileBaseInfoDTO = fileProcessFacade.html2Pdf(pdfInfoDTO);
        log.info("fileBaseInfoDTO:{}", JSON.toJSONString(fileBaseInfoDTO));
        // 插入文件信息表
        FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
        // 插入文件关系表
        FileBusinessRelationDTO fileBusinessRelationDTO = new FileBusinessRelationDTO()
                .setFileIdList(Collections.singletonList(fileInfoEntity.getId()))
                .setBizId(Integer.parseInt(userId))
                .setCategoryType(FileCategoryType.LOGIN_PDF.getCode())
                .setModuleType(ModuleTypeEnum.LOGIN.getModule());
        log.info("completeSignature fileBusinessRelationDTO:{}", JSON.toJSONString(fileBusinessRelationDTO));
        fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);*/

        if (cEmployEntity.getPasswordModifyStatus() == null || cEmployEntity.getPasswordModifyStatus() == 0) {
            cEmployEntity.setPasswordModifyStatus(1);
        }

        log.info("completeSignature cEmployEntity:{}", JSON.toJSONString(cEmployEntity));
        icEmployService.updateSignature(cEmployEntity);

        List<CEmployCustomerEntity> cEmployCustomerEntities = cEmployCustomerFacade.queryCEmployCustomerByEmployIdAndCustomerId(cEmployEntity.getId(), Integer.parseInt(signatureDTO.getCustomerId()));

        CEmployCustomerEntity cEmployCustomerEntity = cEmployCustomerEntities.get(0);
        cEmployCustomerEntity.setSignatureStatus(1)
                .setSignatureTime(new Date())
                .setSignatureContent(JSON.toJSONString(signatureDTO))
        ;

        cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity);

        ColumbusOperationDTO columbusOperationDTO = new ColumbusOperationDTO();
        columbusOperationDTO.setBizCode(String.valueOf(cEmployEntity.getId()))
                .setBizModule("columbusLogin")
                .setLogLevel(9)
                .setData("阅读并同意系统协议")
                .setCreatedAt(new Date());

        operationLogFacade.saveOperationLog(columbusOperationDTO);
        log.info("completeSignature success");
        return Result.success();
    }

    public Date verifyPasswordExpired(Date date) {
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.LOGIN_OVER_TIME.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleService.querySystemRuleDetail(systemRuleDTO);
        String expireTime = systemRuleVO.getSystemRuleItemVOList().get(0).getRuleItemValue();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, Integer.parseInt(expireTime) + 1);
        return calendar.getTime();
    }

}
