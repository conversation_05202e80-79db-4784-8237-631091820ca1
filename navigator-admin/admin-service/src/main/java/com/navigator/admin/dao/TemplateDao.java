package com.navigator.admin.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.mapper.TemplateMapper;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/12/3 18:57
 */
@Dao
public class TemplateDao extends BaseDaoImpl<TemplateMapper, TemplateEntity> {

    public TemplateEntity getTemplateByCodeAndType(String code, Integer type) {
        List<TemplateEntity> templateEntityList = this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(StringUtils.isNotBlank(code), TemplateEntity::getCode, code.trim())
                .eq(null != type, TemplateEntity::getType, type));
        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
    }

    public List<TemplateEntity> getTemplateByCodeList(List<String> codeList) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(TemplateEntity::getCode, codeList));
    }

    public List<TemplateEntity> findAllTemplate(List<Integer> typeList, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(!CollectionUtils.isEmpty(typeList), TemplateEntity::getType, typeList)
                .eq(null != status, TemplateEntity::getStatus, status)
                .orderByAsc(TemplateEntity::getId)
        );
    }

    public IPage<TemplateEntity> queryTemplate(QueryDTO<QueryTemplateDTO> queryDTO) {
        QueryTemplateDTO queryTemplateDTO = queryDTO.getCondition();
        //默认取E
        if (null != queryTemplateDTO) {
            Integer type = queryTemplateDTO.getType();
//        Integer type = null == queryTemplateDTO ? TemplateTypeEnum.ORIGIN_TEMPLATE.getValue() : queryTemplateDTO.getType();
//        if (null == type || type == 0) {
//            type = TemplateTypeEnum.ORIGIN_TEMPLATE.getValue();
//        }
            Integer status = queryTemplateDTO.getStatus();
            String code = queryTemplateDTO.getCode();
            String name = queryTemplateDTO.getName();
            String contentKey = queryTemplateDTO.getContentKey();
            String updateByName = queryTemplateDTO.getUpdatedByName();
            return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), new LambdaQueryWrapper<TemplateEntity>()
                    .eq(null != type, TemplateEntity::getType, type)
                    .eq(null != status, TemplateEntity::getStatus, status)
                    .eq(StringUtils.isNotBlank(code), TemplateEntity::getCode, code)
                    .like(StringUtils.isNotBlank(name), TemplateEntity::getName, "%" + name + "%")
                    .like(StringUtils.isNotBlank(contentKey), TemplateEntity::getContent, "%" + contentKey + "%")
                    .like(StrUtil.isNotBlank(updateByName), TemplateEntity::getUpdatedByName, updateByName)
                    .orderByAsc(TemplateEntity::getId)
            );
        }
        return null;
    }

    public TemplateEntity getLoginSignature(Integer value) {
        List<TemplateEntity> templateEntityList = this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(TemplateEntity::getType, value));
        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
    }

    public List<TemplateEntity> getTemplateListByIds(List<Integer> templateIdList) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(TemplateEntity::getId, templateIdList));
    }

    public List<TemplateEntity> getTemplateListByCodes(List<String> codeList) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(TemplateEntity::getCode, codeList));
    }
}
