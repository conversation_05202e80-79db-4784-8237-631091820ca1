package com.navigator.admin.service.impl;

import com.navigator.admin.dao.DepartmentDao;
import com.navigator.admin.pojo.entity.DepartmentEntity;
import com.navigator.admin.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
public class DepartmentServiceImpl implements IDepartmentService {

    @Autowired
    private DepartmentDao departmentDao;

    @Override
    public List<DepartmentEntity> getDepartmentList(Integer system) {
        // 1、查询所有分类
        List<DepartmentEntity> entities = departmentDao.getDepartmentList(system);

        // 2、组装成父子的树形结构
        //    1)、找到所有的一级分类
        List<DepartmentEntity> levelDepartments = this.getDepartments(entities);

        return levelDepartments;
    }

    @Override
    public DepartmentEntity getDepartmentEntityById(String departmentId) {
        return departmentDao.getDepartmentEntityById(departmentId);
    }

    @Override
    public List<DepartmentEntity> getDepartmentEntityByCompanyId(String companyId) {
        List<DepartmentEntity> departmentEntities = departmentDao.getDepartmentEntityByCompanyId(companyId);
        List<DepartmentEntity> levelDepartments = this.getDepartments(departmentEntities);
        return levelDepartments;
    }

    @Override
    public List<DepartmentEntity> getDepartmentLeaderId(List<Integer> departmentId) {
        return departmentDao.getDepartmentLeaderId(departmentId);
    }


    /*********************************************************************************************************
     *********************************         私    有    方    法      *************************************
     *********************************************************************************************************/

    private List<DepartmentEntity> getDepartments(List<DepartmentEntity> entities) {
        List<DepartmentEntity> levelDepartments = entities.stream().filter(DepartmentEntity ->
                DepartmentEntity.getParentId() == 0
        ).map((menu -> {
            menu.setChildren(this.getChildrens(menu, entities));
            return menu;
        })).sorted((menu1, menu2) -> {
            return (menu1.getSort() == null ? 0 : menu1.getSort()) - (menu2.getSort() == null ? 0 : menu2.getSort());
        }).collect(Collectors.toList());
        return levelDepartments;
    }


    // 递归查找所有菜单的子菜单
    private List<DepartmentEntity> getChildrens(DepartmentEntity root, List<DepartmentEntity> all) {
        List<DepartmentEntity> children = all.stream().filter(categoryEntity -> {
            return categoryEntity.getParentId().equals(root.getId());
        }).map(categoryEntity -> {
            // 1、找到子部门
            categoryEntity.setChildren(getChildrens(categoryEntity, all));
            return categoryEntity;
        }).sorted((menu1, menu2) -> {
            // 2、部门的排序
            return (menu1.getSort() == null ? 0 : menu1.getSort()) - (menu2.getSort() == null ? 0 : menu2.getSort());
        }).collect(Collectors.toList());
        return children;
    }

    public DepartmentEntity getDepartmentEntityByName(String fatherDepartment) {
        return departmentDao.getDepartmentEntityByName(fatherDepartment);
    }

    public DepartmentEntity save(DepartmentEntity departmentEntity1) {
        departmentDao.save(departmentEntity1);
        return departmentEntity1;
    }
}
