package com.navigator.admin.facade.impl;

import com.navigator.admin.facade.TagFacade;
import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.admin.service.ITagService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:47
 */
@RestController
public class TagFacadeImpl implements TagFacade {

    @Resource
    private ITagService tagService;
    @Resource
    private JwtUtils jwtUtils;

    @Override
    public Result queryTagEntity(QueryDTO<TagQueryDTO> queryDTO) {
        return tagService.queryTagEntity(queryDTO);
    }

    @Override
    public TagEntity queryTageEntity(Integer id) {
        return tagService.queryTageEntity(id);
    }

    @Override
    public Result saveTagEntity(TagEntity tagEntity) {
        return tagService.saveTagEntity(tagEntity);
    }

    @Override
    public Result updateTagEntity(TagEntity tagEntity) {
        return tagService.updateTagEntity(tagEntity);
    }

    public String systemAdminUserId(){
        return JwtUtils.getCurrentUserId();
    }
}
