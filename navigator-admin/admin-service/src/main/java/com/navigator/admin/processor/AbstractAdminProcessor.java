package com.navigator.admin.processor;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description: 抽象的Processor
 * Created by <PERSON><PERSON><PERSON> on 2021/11/6 16:46
 */

@Component
public abstract class AbstractAdminProcessor<T> extends Processor {


    private ApplicationContext container;


    public void beforeProcess(List<String> processorNames, T context) {
        for (String processorName : processorNames) {
            Processor processor = (Processor) container.getBean(processorName);
            processor.before(context);
        }
    }

    public void afterProcess(List<String> processorNames, T context) {
        for (String processorName : processorNames) {
            Processor processor = (Processor) container.getBean(processorName);
            processor.after(context);
        }
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.container = applicationContext;
    }
}
