package com.navigator.admin.dao.approval;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.mapper.approval.LoaApprovalRuleMapper;
import com.navigator.admin.pojo.dto.LoaApprovalRuleDTO;
import com.navigator.admin.pojo.entity.approval.LoaApprovalRuleEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/27
 */
@Dao
public class LoaApprovalRuleDao extends BaseDaoImpl<LoaApprovalRuleMapper, LoaApprovalRuleEntity> {


    public List<LoaApprovalRuleEntity> queryLoaApprovalRule(LoaApprovalRuleDTO loaApprovalRuleDTO) {

        return this.list(Wrappers.<LoaApprovalRuleEntity>lambdaQuery()
                .eq(LoaApprovalRuleEntity::getBuCode, loaApprovalRuleDTO.getBuCode())
                .like(LoaApprovalRuleEntity::getCategory2, loaApprovalRuleDTO.getCategory2())
                .like(LoaApprovalRuleEntity::getSalesType, loaApprovalRuleDTO.getSalesType())
                .eq(LoaApprovalRuleEntity::getTtType, loaApprovalRuleDTO.getTtType())
                .eq(LoaApprovalRuleEntity::getStatus, loaApprovalRuleDTO.getStatus())
                .eq(LoaApprovalRuleEntity::getIsDeleted, loaApprovalRuleDTO.getIsDeleted())
        );
    }
}
