trigger:
  branches:
    include:
      - refs/heads/sim2-prod
resources:
  repositories:
    - repository: self
      type: git
      ref: refs/heads/sim2-prod
jobs:
  - job: Job_1
    displayName: Agent job 1
    pool:
      vmImage: ubuntu-latest
    steps:
      - checkout: self
        fetchDepth: 1

      - script: |
          sudo apt-get update
          sudo apt-get install -y openjdk-11-jdk
          echo "##vso[task.setvariable variable=JAVA_HOME]/usr/lib/jvm/java-11-openjdk-amd64"
          echo "##vso[task.setvariable variable=PATH]$JAVA_HOME/bin:$PATH"
        displayName: 'Install JDK 11'

      - task: CmdLine@2
        displayName: Command Line Script
        enabled: False
        inputs:
          script: java -version
      - task: CmdLine@2
        displayName: Command Line Script
        enabled: False
        inputs:
          script: >+
            echo '<?xml version="1.0" encoding="UTF-8"?>
            
            
            
            <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
                      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                      xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
            
              <pluginGroups>
            
              </pluginGroups>
            
            
              <proxies>
            
              </proxies>
            
            
              <servers>
            
              </servers>
            
            <mirrors>
               <!-- maven镜像 -->
                  <mirror>
                    <id>alimaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云公共仓库</name>
                    <url>https://maven.aliyun.com/repository/central/</url>
                </mirror>
                <!-- 中央仓库1 -->
                <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云插件仓库</name>
                    <url>https://maven.aliyun.com/repository/gradle-plugin</url>
                </mirror>
                <!-- 中央仓库2 -->
                <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云谷歌仓库</name>
                    <url>https://maven.aliyun.com/repository/google</url>
                </mirror>
              <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云阿帕奇仓库</name>
                    <url>https://maven.aliyun.com/repository/apache-snapshots</url>
                </mirror>
              <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云spring仓库</name>
                    <url>https://maven.aliyun.com/repository/spring</url>
                </mirror>
              <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云spring插件仓库</name>
                    <url>https://maven.aliyun.com/repository/spring-plugin</url>
                </mirror>
            <mirror>
                    <id>maven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>maven仓库</name>
                    <url>https://mvnrepository.com/</url>
                </mirror>
            <mirror>
                    <id>evosuite</id>
                    <mirrorOf>*</mirrorOf>
                    <name>evosuite仓库</name>
                    <url>https://evosuite.org/m2</url>
                </mirror>
            <mirror>
                    <id>aliyunmaven</id>
                    <mirrorOf>*</mirrorOf>
                    <name>阿里云公共仓库</name>
                    <url>https://maven.aliyun.com/repository/public</url>
                </mirror>
              </mirrors>
            
            
            
              <profiles>
            
              </profiles>
            
            
            </settings>
            
            '> /usr/share/apache-maven-3.8.5/conf/settings.xml


      - task: Maven@3
        displayName: Maven pom.xml
        inputs:
          goals: clean install
          options: -Dmaven.test.skip=true
      - task: Docker@2
        displayName: buildAndPush
        inputs:
          containerRegistry: 60d32728-0e22-4359-a73b-0aa8a032a8b9
          repository: navigator-admin-sim2-prod
          Dockerfile: navigator-admin/Dockerfile.sim2-prod
          buildContext: ''
      - task: CopyFiles@2
        displayName: Copy Files
        inputs:
          Contents: '**/navigator-admin/navigator-admin-sim2-prod.yaml'
          TargetFolder: $(Build.ArtifactStagingDirectory)
      - task: PublishBuildArtifacts@1
        displayName: 'Publish Artifact: drop'
...
