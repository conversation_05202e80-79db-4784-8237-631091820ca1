FROM csm4pnvgacr001.azurecr.cn/openjdk-htmltopdf:latest
#FROM csm4nnvgacr001.azurecr.cn/openjdk-htmltopdf:latest
#RUN apt-get update && apt-get install -y wkhtmltopdf && apt-get clean all
#COPY navigator-admin/simsun.ttc /usr/share/fonts
RUN mkdir /config
COPY navigator-admin/admin-service/src/main/resources/bootstrap-dev.yml /config
COPY navigator-admin/admin-service/src/main/resources/bootstrap-sim-prod.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/admin-service/*.jar /navigator-admin-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-admin-1.0-SNAPSHOT.jar
