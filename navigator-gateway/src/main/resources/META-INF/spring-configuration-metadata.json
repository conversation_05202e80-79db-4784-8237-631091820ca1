{"groups": [{"sourceType": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration", "name": "动态路由配置属性", "type": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration"}], "hints": [{"name": "ksaas.dynamic.route.enabled", "values": [{"value": true}, {"value": false}]}], "properties": [{"sourceType": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration", "name": "ksaas.dynamic.route.dataId", "type": "java.lang.String", "desc": "dataId specified in Nacos configuration management"}, {"sourceType": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration", "name": "ksaas.dynamic.route.group", "type": "java.lang.String", "desc": "group specified in Nacos configuration management"}, {"sourceType": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration", "name": "ksaas.dynamic.route.ipAddr", "type": "java.lang.String", "desc": "nacos host address"}, {"sourceType": "com.ksaas.cloud.gateway.config.DynamicRouteConfiguration", "name": "ksaas.dynamic.route.enabled", "type": "java.lang.Bo<PERSON>an", "desc": "Flag that enables dynamic route"}]}