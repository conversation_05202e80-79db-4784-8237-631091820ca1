//package com.navigator.gateway.config;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.nacos.api.exception.NacosException;
//import com.alibaba.nacos.api.naming.NamingService;
//import com.alibaba.nacos.api.naming.listener.NamingEvent;
//import com.alibaba.nacos.api.naming.pojo.Instance;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.event.ApplicationContextEvent;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.util.ArrayList;
//import java.util.List;
//
//@Component
//@Slf4j
//public class NacosInstanceChangeEvent extends ApplicationContextEvent {
//    public static final String SERVICE_LIST = "serviceList";
//    @Autowired
//    private ApplicationContext applicationContext;
//    @Autowired
//    private CacheMap cacheMap;
//    @Autowired
//    private NamingService namingService;
//	/**
//	 *
//	 */
//	private static final long serialVersionUID = -5198386861491058274L;
//
//	public NacosInstanceChangeEvent(ApplicationContext source) {
//		super(source);
//	}
//
//    @PostConstruct
//    public void init() {
//        List<String> serviceLists = new ArrayList<>();
//        serviceLists.add("navigator-admin-service");
//        serviceLists.add("navigator-finance-service");
//        serviceLists.add("navigator-delivery-service");
//        serviceLists.add("navigator-trade-service");
//        cacheMap.put(SERVICE_LIST, serviceLists);
//        serviceLists.forEach(serviceName->initNacosSubscribe(namingService,serviceName));
//        //initNacosSubscribe(namingService);
//    }
//
//    /**
//     * 	初始化nacos中的instance的变化监听
//     * @param namingService
//     */
//    public void initNacosSubscribe(NamingService namingService,String service) {
//        try {
//            //需要监听的servicename
//            namingService.subscribe(service, event -> {
//                log.info("event:{}", JSON.toJSON(event));
//                if (event instanceof NamingEvent) {
//                    String serviceName = ((NamingEvent) event).getServiceName();
//                    List<Instance> instanceList = ((NamingEvent) event).getInstances();
//                    log.info("实例发生变更:ServiceName=" + serviceName + ",instanceList=" + instanceList);
//                    //发布事件
//                    applicationContext.publishEvent(new NacosInstanceChangeEvent(applicationContext));
//                }
//            });
//        } catch (NacosException e) {
//            log.error("listen service pods error");
//        }
//    }
//
//}
