//package com.navigator.gateway.config;
//
//import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
//import com.alibaba.cloud.nacos.NacosServiceManager;
//import com.alibaba.nacos.api.naming.NamingService;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class InitConfiguration {
//
//	@Bean
//	public NamingService namingService(NacosServiceManager nacosServiceManager, NacosDiscoveryProperties nacosDiscoveryProperties) {
//		return nacosServiceManager.getNamingService(nacosDiscoveryProperties.getNacosProperties());
//	}
//}
