//package com.navigator.gateway.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cloud.client.ServiceInstance;
//import org.springframework.cloud.client.discovery.DiscoveryClient;
//import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
//import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
//import org.springframework.cloud.gateway.route.Route;
//import org.springframework.cloud.gateway.route.RouteDefinition;
//import org.springframework.cloud.gateway.route.RouteDefinitionWriter;
//import org.springframework.cloud.gateway.route.RouteLocator;
//import org.springframework.cloud.gateway.support.NotFoundException;
//import org.springframework.context.ApplicationEventPublisher;
//import org.springframework.context.ApplicationEventPublisherAware;
//import org.springframework.context.ApplicationListener;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//@Component
//@Slf4j
//public class NacosInstanceChangeListener implements ApplicationListener<NacosInstanceChangeEvent>, ApplicationEventPublisherAware {
//
//    private static final String SERVICE_LIST = "serviceList";
//
//    @Autowired
//    private RouteDefinitionWriter routeDefinitionWriter;
//
//	@Autowired
//	private RouteLocator routeLocator;
//
//	@Autowired
//	private DiscoveryClient discoveryClient;
//
//    private ApplicationEventPublisher publisher;
//
//    @Autowired
//    private CacheMap cacheMap;
//
//	@Override
//	public void onApplicationEvent(NacosInstanceChangeEvent event) {
//		log.info("收到NacosInstanceChangeEvent，准备更新路由");
//		//重新发布路由
//		//1、获取当前所有路由
//		Flux<Route> routes = routeLocator.getRoutes();
//		//更新路由
//		updateUploadClientRoute(routes);
//	}
//
//	@Override
//	public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
//		this.publisher = applicationEventPublisher;
//	}
//
//	/**
//	 * 	增加路由
//	 * @param definition
//	 * @return
//	 */
//    public String add(RouteDefinition definition) {
//        routeDefinitionWriter.save(Mono.just(definition)).subscribe();
//        return "success";
//    }
//
//	/**
//	 * 	更新路由
//	 * @param definition
//	 * @return
//	 */
//    public String update(RouteDefinition definition) {
//        try {
//            delete(definition.getId());
//        } catch (Exception e) {
//            return "update fail,not find route  routeId: "+definition.getId();
//        }
//        try {
//            routeDefinitionWriter.save(Mono.just(definition)).subscribe();
//            this.publisher.publishEvent(new RefreshRoutesEvent(this));
//            return "success";
//        } catch (Exception e) {
//            return "update route  fail";
//        }
//    }
//
//    /**
//     * 	删除路由
//     * @param id
//     * @return
//     */
//    public Mono<ResponseEntity<Object>> delete(String id) {
//        return this.routeDefinitionWriter.delete(Mono.just(id)).then(Mono.defer(() -> {
//            return Mono.just(ResponseEntity.ok().build());
//        })).onErrorResume((t) -> {
//            return t instanceof NotFoundException;
//        }, (t) -> {
//            return Mono.just(ResponseEntity.notFound().build());
//        });
//    }
//
//    /**
//     * 	更新UploadClient路由
//     * @param routes
//     */
//    public void updateUploadClientRoute(Flux<Route> routes) {
//    	//获取需要删除的路由列表，这里以路由id的固定前缀获取
//    	Flux<Route> uploadClientRoutes = routes.filter(route -> route.getId().startsWith("navigator"));
//		//2、删除满足条件的所有路由
//		uploadClientRoutes.subscribe(route -> {
//			log.info("删除路由:{}",route.getId());
//            //调用删除
//			String routeId = route.getId();
//			delete(routeId);
//        }, error -> {
//			log.error("更新路由出错:",error.getCause());
//        }, () -> {
//
//            List<String> serviceLists = (List) cacheMap.get(SERVICE_LIST);
//            if (serviceLists != null && serviceLists.size() > 0) {
//                serviceLists.forEach(serviceName->{
//                    //3、新加新的路由,获取当前nacos中的instance信息
//                    List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
//                    for (ServiceInstance instance : instances) {
//                        Map<String, String> metadata = instance.getMetadata();
//                        String uploadClientId = metadata.get("serviceId");
//                        //路由
//                        RouteDefinition routeDefinition = new RouteDefinition();
//                        //设置路由id
//                        routeDefinition.setId(uploadClientId);
//                        routeDefinition.setUri(instance.getUri());
//                        //设置断言
//                        List<PredicateDefinition> predicateDefinitionList=new ArrayList<>();
//                        PredicateDefinition predicateDefinition = new PredicateDefinition();
//                        //断言类型，Header,Before等,取值来自RouteDefinitionRouteLocator.predicates
//                        predicateDefinition.setName("Header");
//                        //断言参数，比如满足header中的clientid为uploadClientId的
//                        predicateDefinition.addArg("header", "clientId," + uploadClientId);
//                        predicateDefinitionList.add(predicateDefinition);
//                        routeDefinition.setPredicates(predicateDefinitionList);
//                        add(routeDefinition);
//                    }}
//                );
//            }
//        	//发起路由更新事件
//        	this.publisher.publishEvent(new RefreshRoutesEvent(this));
//        });
//    }
//
//}
//
