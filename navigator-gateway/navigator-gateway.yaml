apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-gateway-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-gateway-dev
  template:
    metadata:
      labels:
        app: ldc-navigator-gateway-dev
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-gateway-dev:#{Build.BuildId}#
        name: ldc-navigator-gateway-dev
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "test" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4nnvgsto001-secret  # required
            shareName: logs-test  # required
            server: csm4nnvgsto001.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional
---
apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-gateway-dev
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  namespace: dev
spec:
  type: LoadBalancer
  loadBalancerIP: **************
  ports:
  - port: 80
    protocol: TCP
    targetPort: 80
    #nodePort: 30101
  selector:
    app: ldc-navigator-gateway-dev
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-nginx-ingress-dev
  namespace: dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  tls:
  - hosts:
    - apps-tst.ldcchina.cn
    secretName: navigator-ingress
  rules:
  - host: apps-tst.ldcchina.cn
    http:
      paths:
      - path: /ns-tst/?(.*)
        pathType: Prefix
        backend:
          service:
            name: ldc-navigator-gateway-dev
            port:
              number: 80

