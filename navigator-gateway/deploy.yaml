apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-gateway-__ENV__
  namespace: __ENV__
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-gateway-__ENV__
  template:
    metadata:
      labels:
        app: ldc-navigator-gateway-__ENV__
    spec:
      imagePullSecrets:
        - name: docker-harbor-registry
      containers:
      - image:  40.73.66.75/dev/ldc-navigator-gateway-__ENV__:__VERSION__
        imagePullPolicy: Always
        name: ldc-navigator-gateway-__ENV__


---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-gateway-__ENV__
  namespace: __ENV__
spec:
  ports:
  - port: 80
    protocol: TCP
    targetPort: 80
    nodePort: 30101
  selector:
    app: ldc-navigator-gateway-__ENV__
  type: NodePort
