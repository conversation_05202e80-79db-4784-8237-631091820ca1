apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-dagama-__ENV__
  namespace: __ENV__
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-dagama-__ENV__
  template:
    metadata:
      labels:
        app: ldc-navigator-dagama-__ENV__
    spec:
      imagePullSecrets:
        - name: docker-harbor-registry
      containers:
      - image:  40.73.66.75/dev/ldc-navigator-dagama-__ENV__:__VERSION__
        imagePullPolicy: Always
        name: ldcweb-__ENV__


---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-dagama-__ENV__
  namespace: __ENV__
spec:
  ports:
  - port: 9106
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-dagama-__ENV__
