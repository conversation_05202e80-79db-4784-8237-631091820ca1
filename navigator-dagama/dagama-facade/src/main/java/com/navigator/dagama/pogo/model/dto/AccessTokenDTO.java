package com.navigator.dagama.pogo.model.dto;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 13:39
 */
@Data
public class AccessTokenDTO {
    /** 返回code */
    private Integer errcode;
    /** 返回msg */
    private String errmsg;
    /**
     * 微信公号的accessToken
     */
    private String access_token;
    /**
     * 有效时间
     */
    private long expires_in;
    /**
     * 时间
     */
    private Timestamp date;
}
