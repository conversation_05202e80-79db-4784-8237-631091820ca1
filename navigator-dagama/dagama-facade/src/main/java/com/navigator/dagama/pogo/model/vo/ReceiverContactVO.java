package com.navigator.dagama.pogo.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> lyl
 * @Date: 2021/3/17 17:03
 */
@Data
@Accessors(chain = true)
public class ReceiverContactVO implements Serializable {
    //用户ID(站内信)
    private Integer id;
    //用户真实姓名
    private String name;
    //用户名
    private String userName;
    //用户昵称
    private String nickName;
    //工号
    private Integer workNo;
    //手机号
    private String phone;
    //邮箱
    private String email;
    //微信
    private String openId;
    //企业微信
    private String corpId;
    //公众号
    private String unionId;
    //企业微信用户id
    private String cpUserId;
}
