package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.SystemEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dbm_inmail")
@ApiModel(value="Inmail对象", description="")
public class DbmInmailEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@ApiModelProperty(value = "业务模板id")
	private Integer businessTemplateId;

	@ApiModelProperty(value = "业务场景")
	private Integer businessScene;

	@ApiModelProperty(value = "计划发送时间")
	private Date planSendTime;

	@ApiModelProperty(value = "发送者")
	private String sender;

	@ApiModelProperty(value = "发送者姓名")
	private String senderName;

	@ApiModelProperty(value = "接收者")
	private String receiver;

	@ApiModelProperty(value = "接收者类型，用户")
	private String receiverType;

	@ApiModelProperty(value = "标题")
	private String title;

	@ApiModelProperty(value = "拼接好的消息内容")
	private String content;

	@ApiModelProperty(value = "消息类型（inmail）")
	private String messageType;

	@ApiModelProperty(value = "发送状态")
	private String status;

	@ApiModelProperty(value = "是否已读(0:未读  1:已读)")
	private Integer isRead;

	@ApiModelProperty(value = "品类id")
	private Integer categoryId = 0;

	@ApiModelProperty(value = "菜单code")
	private String menuCode = "";

	@ApiModelProperty(value = "备注")
	private String memo;

	@ApiModelProperty(value = "所属系统")
	private Integer system;

	@TableField(value = "created_at", fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdAt;

	@TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedAt;

	@ApiModelProperty(value = "创建人")
	private String createdBy;

	@ApiModelProperty(value = "修改人")
	private String updatedBy;

	@TableField(exist = false)
	private String menuUrl;

}
