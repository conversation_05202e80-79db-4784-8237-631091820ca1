package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum ReceiverTypeEnum {
    /**
     * 消息接收人的来源所属类型
     */
    DEFAULT("default", "默认"),
    PERSONAL("personal", "个人/多人"),
    ROLE("role", "(多个)角色"),
    CUSTOMER_ROLE("customer_role", "客户(多个)角色"),
    DEPARTMENT_LEADER("department_leader", "部门领导人"),
    DEPARTMENT("department", "部门"),
    CONTRACT("contract","合同"),
    EMAIL_GROUP("email_group","邮件组"),
    EMAIL_LIST("email_list","多个邮件"),
    FACTORY_MAIL("factory_mail","工厂邮箱"),
    CUSTOMER_CONTACT("customer_contact","客户联系人"),
    CUSTOMER_ADMIN("customer_admin","客户管理员"),
    COLUMBUS_USER("columbus_user","Columbus系统用户"),
    WECHAT_TAG("wechat_tag","微信标签"),
    TAG("tag", "单个标签/多个标签"),
    CP_WHCHAT_GROUP("cp_wechat_group", "企业微信群发");
    

    private String value;
    private String desc;

    public static ReceiverTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(receiverTypeEnum -> StringUtils.equals(value,receiverTypeEnum.getValue()))
                .findFirst()
                .orElse(DEFAULT);
    }
}
