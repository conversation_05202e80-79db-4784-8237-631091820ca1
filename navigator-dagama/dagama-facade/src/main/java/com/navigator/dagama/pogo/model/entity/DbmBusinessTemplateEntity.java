package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dbm_business_template")
@ApiModel(value = "BusinessTemplate对象", description = "")
public class DbmBusinessTemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务代码")
    private String businessCode;

    @ApiModelProperty(value = "抄送人")
    private String copyer;

    @ApiModelProperty(value = "抄送人类型")
    private String copyerType;

    @ApiModelProperty(value = "类型(信息:sms,邮件:mail,站内信:inmail)")
    private String messageType;

    @ApiModelProperty(value = "信息类别")
    private String messageCategory;

    @ApiModelProperty(value = "模板id")
    private Integer templateId;

    @ApiModelProperty(value = "发送人")
    private String sender;

    @ApiModelProperty(value = "接收者")
    private String receiver;

    @ApiModelProperty(value = "接收者类型，用户/角色")
    private String receiverType;

    @ApiModelProperty(value = "")
    private String nextAction;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "0:手动 1：自动")
    private Integer auto = 0;

    @ApiModelProperty(value = "1：magellan 2：columbus")
    private Integer system;

    @ApiModelProperty(value = "0：禁用 1：启用")
    private Integer status = 0;

    @ApiModelProperty(value = "逻辑删除（0未被删除，1已被删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "自动发送 1:自动发送 2:手动发送")
    private Integer automaticSend;

    @ApiModelProperty(value = "发送频率")
    private String sendFrequency;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;
}
