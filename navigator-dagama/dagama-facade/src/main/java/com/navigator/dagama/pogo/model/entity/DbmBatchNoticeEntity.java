package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dbm_batch_notice")
@ApiModel(value = "BatchNotice对象", description = "")
public class DbmBatchNoticeEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String businessCode;    // 业务类型
    private String title;           // 标题
    private String content;         // 内容
    private Integer system;         // 所属系统
    private Integer referId;        // 关联Id
    private String receivers;       // 接收人
    private Integer totalCount;     // 总记录数
    private Integer currentId;      // 当前处理到的记录Id
    private Integer status;         // 状态
    private String memo;            // 备注


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String createdBy;

    private String updatedBy;


}
