package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dbm_template")
@ApiModel(value="Template对象", description="")
public class DbmTemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@ApiModelProperty(value = "第三方模板id")
	private String thirdPartyId;

	@ApiModelProperty(value = "模板编码")
	private String templateCode;

	@ApiModelProperty(value = "模板名称")
	private String name;

	@ApiModelProperty(value = "类型(sms,email,wechat,inmail)")
	private String messageType;

	@ApiModelProperty(value = "标题")
	private String title;

	@ApiModelProperty(value = "模板内容")
	private String content;

	@ApiModelProperty(value = "模板类型(ftl:content模板内容的ftl文件   default:默认  normal:content)")
	private String contentType;

	@ApiModelProperty(value = "备注")
	private String memo;

	@ApiModelProperty(value = "模板创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdAt;

	@ApiModelProperty(value = "模板创建者")
	private String createdBy;

	@ApiModelProperty(value = "模板修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedAt;

	@ApiModelProperty(value = "模板修改者")
	private String updatedBy;

	@ApiModelProperty(value = "创建人姓名")
	private String createdByName;

	@ApiModelProperty(value = "更新人姓名")
	private String updatedByName;

	@ApiModelProperty(value = "集合")
	private Integer isGather;

}
