package com.navigator.dagama.pogo.model.dto;

import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 19:16
 */
@Data
@Accessors(chain = true)
public class BusinessTemplateDTO {
    private Integer businessTemplateId;
    //模板
    private DbmTemplateEntity templateEntity;
    //消息类型
    private String messageType;

    private String messageCategory;

    private String nextAction;
    //
    private String receiver;
    //
    private String receiverType;
    //
    private String copyer;
    //
    private String copyerType;
    //
    private String sender;
    //
    private Integer chatId;
}
