package com.navigator.dagama.pogo.model.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-02 09:38
 **/
@Data
@Accessors(chain = true)
@TableName("dbm_mail_notice_config")
@ApiModel(value = "DbmMailNoticeConfig对象", description = "")
public class DbmMailNoticeConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    private Integer id;
    @ApiModelProperty(value = "二级品类ID")
    private String category2;
    @ApiModelProperty(value = "二级品类名称")
    @Excel(name = "category2Name")
    private String category2Name;

    @ApiModelProperty(value = "工厂")
    @Excel(name = "factoryCode")
    private String factoryCode;

    @ApiModelProperty(value = "发送人")
    @Excel(name = "fromMail")
    private String fromMail;

    @ApiModelProperty(value = "密码")
    @Excel(name = "pwd")
    private String pwd;

    @ApiModelProperty(value = "回复人")
    @Excel(name = "replyTo")
    private String replyTo;

    @ApiModelProperty(value = "抄送人")
    @Excel(name = "cc")
    private String cc;

    @ApiModelProperty(value = "是否被删除")
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}
