package com.navigator.dagama.pogo.model.dto;

import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-03-03 18:06
 */
@Data
@Accessors(chain = true)
public class SendMessageDTO {

    private String uuid;
    private String templateId;  // 第三方模板id，例如腾讯短信模板ID
    private String title;
    private String content;
    private String sendContent;
    private Map<String, Object> dataMap;
    private String messageType;

    //基础信息
    private String factoryCode = "";
    private Integer categoryId = 0;
    private Integer salesType = 0;
    private String menuCode = "";
    private String businessScene;
    private Integer customerId;

    private String sender;
    private String replayTo;
    private String copyer;
    private String copyerType;


    //private String url;
    //private String chatId;
    //private List<String> ccList;
    //private ReceiverContactVO receiverContactVO;
    //private String attachmentPath;
    //private Integer signId;
    //private MailSendConfigDTO mailSendConfigDTO;

    private List<ReceiverContactVO> receiver;
    private List<String> receivers;
    private String receiverType;

    private String businessCode;
    private String referBizId;
    private String originData;

    private Integer system;


    public SendMessageDTO() {
    }

}
