package com.navigator.dagama.pogo.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/15 17:54
 */
@Data
public class TemplateVO {
    private String thirdPartyId;
    private String templateCode;
    private String name;
    private String messageType;
    private String title;
    private String content;
    private String contentType;
    private String memo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    private String updatedBy;
    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;
}
