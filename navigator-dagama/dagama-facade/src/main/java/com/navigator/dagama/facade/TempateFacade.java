package com.navigator.dagama.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/11 14:47
 */
@FeignClient(name = "navigator-dagama-service")
@Component
public interface TempateFacade {
    /**
     * 查询模板
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryTemplate")
    Result queryTemplate(@RequestBody QueryDTO<DbmTemplateEntity> queryDTO);

    @GetMapping("/queryTemplateById")
    Result queryTemplateById(@RequestParam(name = "id") Integer id);

    @PostMapping("/updateTemplate")
    Result updateTemplate(@RequestBody DbmTemplateEntity dbmTemplateEntity);

    @PostMapping("/queryBusinessTemplate")
    Result queryBusinessTemplate(@RequestBody QueryDTO<DbmBusinessTemplateEntity> queryDTO);

    @PostMapping("/updateBusinessTemplateStatus")
    Result updateBusinessTemplateStatus(@RequestBody DbmBusinessTemplateEntity dbmBusinessTemplateEntity);

}
