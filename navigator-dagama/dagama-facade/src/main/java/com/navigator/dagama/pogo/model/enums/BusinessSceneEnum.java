package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BusinessSceneEnum {

    /**
     * 消息场景
     */
    LDC_TASK_APPROVE_NOTICE(1, "LDC_TASK_APPROVE_NOTICE", "审批任务提醒"),
    PRICE_ADD_APPLY_FOR(2, "PRICE_ADD_APPLY_FOR", "期货头寸处理"),
    COLUMBUS_DEAL_ALLOCATE(3, "COLUMBUS_DEAL_ALLOCATE", "成交待分配通知"),
    COLUMBUS_SIGN_INMAIL(4, "COLUMBUS_SIGN_INMAIL", "电子文档签章通知"),
    COLUMBUS_ORIGINAL_PAPER(5, "COLUMBUS_ORIGINAL_PAPER", "合同正本通知"),
    COLUMBUS_NOT_DEAL(6, "COLUMBUS_NOT_DEAL", "未成交单据通知"),
    MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL(7, "MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL", "客户撤回申请通知"),
    MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID(8, "MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID", "客户作废申请通知"),
    COLUMBUS_DELIVERY_APPLY_APPROVE_PASS(9, "COLUMBUS_DELIVERY_APPLY_APPROVE_PASS", "执行分配审批通知"),
    COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT(10, "COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT", "执行驳回申请通知"),
    COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE(11, "COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE", "执行审批作废申请通知"),
    COLUMBUS_DELIVERY_APPLY_INVALID(12, "COLUMBUS_DELIVERY_APPLY_INVALID", "执行发起作废通知"),
    COLUMBUS_DELIVERY_APPLY_BLOCKED(15, "COLUMBUS_DELIVERY_APPLY_BLOCKED", "客户锁定"),
    CONTRACT_EQUITY_APPROVE_NOTICE(13, "CONTRACT_EQUITY_APPROVE_NOTICE", "白名单权益变更审批提醒"),
    CONTRACT_EQUITY_APPROVE_RESULT_NOTICE(14, "CONTRACT_EQUITY_APPROVE_RESULT_NOTICE", "白名单权益变更审批结果提醒"),
    COLUMBUS_SYSTEM_CHANGE_NOTICE_1(7, "COLUMBUS_SYSTEM_CHANGE_NOTICE_1", "哥伦布主管理员账户信息变更通知"),
    COLUMBUS_SYSTEM_CHANGE_NOTICE_2(8, "COLUMBUS_SYSTEM_CHANGE_NOTICE_2", "哥伦布主管理员账户信息变更通知"),
    MAGELLAN_PRICE_CONTRARY(2, "MAGELLAN_PRICE_CONTRARY", "头寸处理撤回"),
    MAGELLAN_HUSKY_TEMPLATE_CHANGE(2, "MAGELLAN_HUSKY_TEMPLATE_CHANGE", "协议出具内容修改通知"),
    MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING(16, "MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING", "提货委托分配警告"),
    FUTURE_CLOSE_POSITION(17, "FUTURE_CLOSE_POSITION", "期货套保平仓")
    ;

    private Integer value;
    private String desc;
    private String name;

    public static BusinessSceneEnum getByDesc(String desc) {
        for (BusinessSceneEnum sourceEnum : BusinessSceneEnum.values()) {
            if (desc.equals(sourceEnum.getDesc())) {
                return sourceEnum;
            }
        }
        return BusinessSceneEnum.LDC_TASK_APPROVE_NOTICE;
    }

    public static BusinessSceneEnum getByValue(Integer value) {

        for (BusinessSceneEnum sourceEnum : BusinessSceneEnum.values()) {
            if (sourceEnum.getValue() == value) {
                return sourceEnum;
            }
        }
        return BusinessSceneEnum.LDC_TASK_APPROVE_NOTICE;
    }

    public static List<BusinessSceneEnum> getBusinessSceneList() {
        return Arrays.stream(BusinessSceneEnum.values())
                .collect(Collectors.toList());
    }

}
