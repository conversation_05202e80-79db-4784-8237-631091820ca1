package com.navigator.dagama.pogo.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/15 17:51
 */
@Data
public class SendTaskVO {
    private Integer businessTemplateId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planSendTime;
    private String sender;
    private String senderName;
    private String receiver;
    private String receiverType;
    private String copyer;
    private String copyerType;
    private String title;
    private String content;
    private String messageType;
    private String status;
    private String memo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
