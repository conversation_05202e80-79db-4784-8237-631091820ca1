package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/11/26 15:23
 */
@Getter
@AllArgsConstructor
public enum MessageCategoryEnum {

    APPROVE("approve","审批"),
    FOLLOW("follow","跟随"),
    NOTICE("notice","通知"),
    ;

    private String value;
    private String desc;


    public static MessageCategoryEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(messageCategoryEnum -> StringUtils.equals(value,messageCategoryEnum.getValue()))
                .findFirst()
                .orElse(NOTICE);
    }

}
