package com.navigator.dagama.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/5 11:12
 */
@FeignClient(name = "navigator-dagama-service")
@Component
public interface MessageFacade {

    /**
     * 统一发送消息
     *
     * @param messageInfoDTO
     */
    @PostMapping("/sendMessage")
    Result<String> sendMessage(@RequestBody MessageInfoDTO messageInfoDTO);

    @GetMapping("/reSendMessage")
    Result<String> reSendMessage(@RequestParam(value = "sendTaskId") Integer sendTaskId);

    @GetMapping("/autoReSendCustomerSignNotice")
    Result<String> autoReSendCustomerSignNotice();

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryInmail")
    Result queryInmail(@RequestBody QueryDTO<DbmInmailEntity> queryDTO);


    /**
     * 站内信数量
     *
     * @return
     */
    @GetMapping("/queryInmailCount")
    Result queryInmailCount(@RequestParam(value = "system") Integer system);

    /**
     * 表示是否已读
     *
     * @param id
     * @return
     */
    @GetMapping("/readInmail")
    Result readInmail(@RequestParam(value = "id", required = false) List<Integer> id);


    /**
     * 根据用户查询站内信数量
     *
     * @param receiver
     * @return
     */
    @GetMapping("/queryEmployInMailCount")
    Integer queryEmployInMailCount(@RequestParam(value = "receiver", required = false) Integer receiver);

    /**
     * 发送信息列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySendTas")
    Result querySendTas(@RequestBody QueryDTO<DbmSendTaskEntity> queryDTO);


    @GetMapping("/sendEmailInfo")
    Result sendEmailInfo(@RequestParam(name = "email") String email,
                         @RequestParam(name = "title") String title,
                         @RequestParam(name = "content") String content);

    @GetMapping("/pushWebsocketMsg")
    Result pushWebsocketMsg(@RequestParam(name = "userId") Integer userId,
                            //@RequestParam(name = "system") String system,
                            @RequestParam(name = "content") String content);


    @PostMapping("/pushWebsocketInMailMsg")
    Result pushWebsocketInMailMsg(@RequestBody WebsocketInMailDTO websocketInMailDTO);


    @GetMapping("/sendRedAlarmEmail")
    Result sendRedAlarmEmail(@RequestParam(name = "title") String title);

    @GetMapping("/isSendSuccess")
    boolean isSendSuccess(@RequestParam(name = "bizId") String bizId);

    @GetMapping("/sentBei6")
    Result sentBei6();

    /*@PostMapping("/queryBusinessTemplate")
    Result queryBusinessTemplate(@RequestBody QueryDTO<DbmBusinessTemplateEntity> queryDTO);*/

    /**
     * 导入邮件账号信息
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/importMailNoticeConfig")
    Result importMailNoticeConfig(@RequestParam("file") MultipartFile uploadFile);
}
