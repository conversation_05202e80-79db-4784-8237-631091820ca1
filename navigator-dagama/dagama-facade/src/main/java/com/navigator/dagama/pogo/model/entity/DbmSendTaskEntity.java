package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.SystemEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dbm_send_task")
@ApiModel(value = "SendTask对象", description = "")
public class DbmSendTaskEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务模板id")
    private Integer businessTemplateId;

    @ApiModelProperty(value = "计划发送时间")
    private Date planSendTime;

    @ApiModelProperty(value = "发送者")
    private String sender;

    @ApiModelProperty(value = "发送者姓名")
    private String senderName;

    @ApiModelProperty(value = "接收者")
    private String receiver;

    @ApiModelProperty(value = "接收者类型，用户/角色")
    private String receiverType;

    @ApiModelProperty(value = "抄送人")
    private String copyer;

    @ApiModelProperty(value = "抄送人类型")
    private String copyerType;

    @ApiModelProperty(value = "标题")
    private String title = "";

    @ApiModelProperty(value = "品类id")
    private Integer categoryId = 0;

    @ApiModelProperty(value = "菜单编码")
    private String menuCode = "";

    @ApiModelProperty(value = "拼接好的消息内容")
    private String content;

    @ApiModelProperty(value = "消息类型（短信:sms,邮箱:mail,站内信:inmail)")
    private String messageType;

    @ApiModelProperty(value = "发送状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String memo = "";

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "回复邮箱地址")
    private String replayTo;

    @ApiModelProperty(value = "关联记录Id")
    private String referId;

    @ApiModelProperty(value = "业务场景")
    private String businessCode;

    @ApiModelProperty(value = "重试次数")
    private Integer tryTimes = 0;

    @TableField(exist = false)
    private String pwd;
    @TableField(exist = false)
    private Integer system = SystemEnum.MAGELLAN.getValue();
    @TableField(exist = false)
    private String businessSceneCode;

    @TableField(exist = false)
    private boolean resend = false;
}
