package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    /**
     * 消息类型
     */
    OTHER("other", "其他"),
    SMS("sms", "短信"),
    IN_MAIL("inmail", "站内信"),
    WECHAT("wechat", "微信"),
    WECHAT_UNION("wechatUnion", "微信公众号"),
    COMPANY_WECHAT("companyWechat", "企业微信应用消息单发"),
    COMPANY_WECHAT_GROUP("companyWechatGroup", "企业微信应用消息群发"),
    EMAIL("email", "邮件");


    private String value;
    private String desc;

    public static MessageTypeEnum getByValue(String value) {
        if (null == value) return OTHER;

        return Arrays.stream(values())
                .filter(messageTypeEnum -> StringUtils.equals(value, messageTypeEnum.getValue()))
                .findFirst()
                .orElse(OTHER);
    }
}
