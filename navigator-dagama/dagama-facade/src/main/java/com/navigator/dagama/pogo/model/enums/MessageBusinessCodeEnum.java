package com.navigator.dagama.pogo.model.enums;

public enum MessageBusinessCodeEnum {
    LDC_SYSTEM_ERROR_NOTICE,
    LDC_TASK_APPROVE_NOTICE,
    LDC_TASK_APPROVED_FOLLOW,
    LDC_PROCINST_APPROVED_FOLLOW,
    LDC_MAGELLAN_RESET_PWD,
    CUSTOMER_SIGN_NOTICE,
    CUSTOMER_SIGNATURE_NOTICE,
    CUSTOMER_SIGNATURE_NOTICE_EMAIL,
    CUSTOMER_PRICE_ALLOCATE_NOTICE,
    CUSTOMER_COLUMBUS_RESET_PWD,
    CLB_USER_MANUAL_NOTICE,
    UPDATE_ALLOCATE_SMS_CUSTOMER,
    UPDATE_ALLOCATE_SMS,
    ADD_REVOCATION_APPLY_FOR_INMAIL,
    POSITION_ADD_REVOCATION_APPLY_FOR_INMAIL,
    UPDATE_APPLY_FOR_INMAIL,
    POSITION_UPDATE_APPLY_FOR_INMAIL,
    COLUMBUS_DEAL_ALLOCATE,
    COLUMBUS_SIGN_INMAIL,
    COLUMBUS_ORIGINAL_PAPER,
    COLUMBUS_NOT_DEAL,
    MAGELLAN_PRICE_CONTRARY,
    MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL,
    MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID,
    COLUMBUS_DELIVERY_APPLY_APPROVE_PASS,
    COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT,
    COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE,
    COLUMBUS_DELIVERY_APPLY_INVALID,
    COLUMBUS_DELIVERY_APPLY_BLOCKED,
    CONTRACT_EQUITY_APPROVE_NOTICE,
    CONTRACT_EQUITY_APPROVE_RESULT_NOTICE,
    COLUMBUS_SYSTEM_CHANGE_NOTICE_1,
    COLUMBUS_SYSTEM_CHANGE_NOTICE_2,
    MAGELLAN_HUSKY_TEMPLATE_CHANGE,
    MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING,
}
