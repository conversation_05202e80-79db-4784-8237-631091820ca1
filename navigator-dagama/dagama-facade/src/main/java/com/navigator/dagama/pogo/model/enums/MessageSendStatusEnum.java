package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18 10:48
 */
@Getter
@AllArgsConstructor
public enum MessageSendStatusEnum {
    INIT(0, "待处理"),
    UNSEND(1, "未发送"),
    ABNORMAL(4, "异常"),
    SUCCESS(2, "发送成功"),
    FAIL(3, "发送失败");

    private int code;
    private String msg;

    public static MessageSendStatusEnum getByStatus(boolean status) {
        if (status) {
            return MessageSendStatusEnum.SUCCESS;
        }
        return MessageSendStatusEnum.FAIL;
    }

    public static boolean isSuccess(String statusName) {
        if (SUCCESS.name().equals(statusName)) {
            return true;
        }
        return false;
    }

    public static List<String> getResendStatus() {
        List<String> statusList = new ArrayList<>();
        statusList.add(FAIL.name());
        statusList.add(UNSEND.name());
        return statusList;
    }
}
