package com.navigator.dagama.pogo.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 17:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MessageInfoDTO<T> implements Serializable {
    /**
     * 非必传（如果未传，则取数据库默认值）
     */
    private String uuid;
    private List<String> receivers;
    private String businessCode;//场景code
    private String businessSceneCode;
    private Map<String, Object> dataMap;
    private List<Map<String, Object>> dataMaps;
    //系统
    private Integer system;
    private String referBizId = "";
    private String menuCode = "";

    //基础信息
    private String factoryCode = "";
    private Integer categoryId = 0;
    private Integer salesType = 0;
    private Integer customerId = 0;

    public MessageInfoDTO() {
        setUuid(UUID.randomUUID().toString());
    }
}
