package com.navigator.dagama.pogo.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
@Accessors(chain = true)
@TableName("dbm_my_message")
@ApiModel(value="MyMessage对象", description="")
public class MyMessage implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String title;

    private Integer userId;

    private String contents;

    private Date sendTime;

    private Integer isRead;

    private Integer isDeleted;

    private Date createdAt;

    private Date updatedAt;

    private String createdBy;

    private String updatedBy;


}
