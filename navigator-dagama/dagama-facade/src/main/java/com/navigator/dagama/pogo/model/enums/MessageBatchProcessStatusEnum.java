package com.navigator.dagama.pogo.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022年12月1日
 */
@Getter
@AllArgsConstructor
public enum MessageBatchProcessStatusEnum {
    INIT(0,"待处理"),
    CREATING(1, "创建任务中"),
    SENDING(2, "发送中"),
    SUCCESS(3, "成功");

    private int value;
    private String msg;


    public static boolean isSuccess(String statusName) {
        if (SUCCESS.name().equals(statusName)) {
            return true;
        }
        return false;
    }
}
