spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        server-addr: http://192.168.1.31:8848
        namespace: dev_hounana
      config:
        server-addr: http://192.168.1.31:8848
        file-extension: yaml
        namespace: dev_hounana
        group: SERVICE_GROUP
        shared-configs:
          - data-id: navigator-commons.yaml
            group: DEFAULT_GROUP
            refresh: true
