package com.navigator.dagama;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = "com.navigator")
//@MapperScan(basePackages = {"com.navigator.dagama.mapper"})
@EnableDiscoveryClient
@EnableAsync
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
public class DagamaApplication {

    public static void main(String[] args) {
        SpringApplication.run(DagamaApplication.class, args);
    }

}
