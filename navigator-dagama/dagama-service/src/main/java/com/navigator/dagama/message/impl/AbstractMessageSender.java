package com.navigator.dagama.message.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.dagama.dao.DbmMessageLogDao;
import com.navigator.dagama.dao.DbmSendTaskDao;
import com.navigator.dagama.message.IMessageSender;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmMessageLogEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.service.MessageUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
public abstract class AbstractMessageSender implements IMessageSender {

    @Resource
    DbmSendTaskDao dbmSendTaskDao;
    @Resource
    DbmMessageLogDao messageLogDao;
    @Resource
    MessageUserService messageUserService;
    @Autowired
    protected OperationLogFacade operationLogFacade;


    @Override
    public String sendMessage(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {
        MessageSendStatusEnum messageSendStatusEnum = MessageSendStatusEnum.UNSEND;
        StringBuilder sbTraceLog = new StringBuilder();

        sbTraceLog.append("01开始发送消息处理；");

        //填充发送人信息
        sendMessageDTO = fullMessageSender(sendMessageDTO, sendTaskEntity);

        sbTraceLog.append("02填充发送人信息；");

        //填充接收人信息
        sendMessageDTO = fullMessageReceiver(sendMessageDTO, businessTemplateDTO, sendTaskEntity);

        sbTraceLog.append("03填充接收人信息；");

        //组装SendTaskEntity信息
        sendTaskEntity = buildSendTask(sendMessageDTO, sendTaskEntity);

        sbTraceLog.append("04组装SendTaskEntity信息；");


        //发送消息
        String sendResult = sendTheMessage(sendTaskEntity);

        sbTraceLog.append("05发送消息；");


        //记录日志
        recordMessageLog(sendTaskEntity, sendMessageDTO.getOriginData());

        sbTraceLog.append("06记录消息日志；");

        operationLogFacade.saveTraceLog(new TraceLogDTO(sendTaskEntity.getReferId(), "AbstractMessageSender.sendMessage", sbTraceLog.toString()));

        return sendResult;
    }

    @Override
    public String sendMessage(DbmSendTaskEntity sendTaskEntity) {

        String sendResult = sendTheMessage(sendTaskEntity);
        //记录日志
        recordMessageLog(sendTaskEntity, JSON.toJSONString(sendTaskEntity));

        return sendResult;
    }

    protected abstract SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity);

    protected SendMessageDTO fullMessageSender(SendMessageDTO sendMessageDTO, DbmSendTaskEntity sendTaskEntity) {
        //邮件服务中重写
        return sendMessageDTO;
    }

    protected DbmSendTaskEntity buildSendTask(SendMessageDTO sendMessageDTO, DbmSendTaskEntity sendTaskEntity) {
        operationLogFacade.saveTraceLog(new TraceLogDTO(sendMessageDTO.getUuid(), "IMessageSender.sendMessageDTO", JSON.toJSONString(sendMessageDTO)));
        return sendTaskEntity;
    }

    protected String sendTheMessage(DbmSendTaskEntity sendTaskEntity) {
        return "";
    }

    /**
     * 记录消息日志
     */
    private void recordMessageLog(DbmSendTaskEntity sendTaskEntity, String originData) {
        DbmMessageLogEntity messageLogEntity = BeanConvertUtils.convert(DbmMessageLogEntity.class, sendTaskEntity);
        messageLogEntity.setTaskId(sendTaskEntity.getId())
                .setSendTime(DateTime.now().toTimestamp())
                .setOriginData(originData).setResult(sendTaskEntity.getStatus());
        messageLogDao.save(messageLogEntity);
    }

}
