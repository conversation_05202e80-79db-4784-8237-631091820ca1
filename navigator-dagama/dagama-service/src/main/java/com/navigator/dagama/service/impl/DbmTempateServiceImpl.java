package com.navigator.dagama.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.dao.DbmTempateDao;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import com.navigator.dagama.pogo.model.vo.BusinessTemplateVO;
import com.navigator.dagama.service.DbmTempateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DbmTempateServiceImpl implements DbmTempateService {

    @Resource
    private DbmTempateDao dbmTempateDao;
    @Resource
    private DbmBusinessTemplateDao dbmBusinessTemplateDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private OperationLogFacade operationLogFacade;

    /**
     * 查询模板
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryTemplate(QueryDTO<DbmTemplateEntity> queryDTO) {
        return dbmTempateDao.qureyTemplate(queryDTO);
    }

    @Override
    public Result queryTemplateById(Integer id) {
        return Result.success(dbmTempateDao.getById(id));
    }

    @Override
    public Result updateTemplate(DbmTemplateEntity dbmTemplateEntity) {
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        String updMsg = "";
        DbmTemplateEntity target = dbmTempateDao.getById(dbmTemplateEntity.getId());
        recordOperationDetail.setDtoData(JSON.toJSONString(target));

        if (null != target) {
            target.setTitle(dbmTemplateEntity.getTitle());
            target.setContent(dbmTemplateEntity.getContent())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setUpdatedBy(JwtUtils.getCurrentUserId())
                    .setUpdatedByName(employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());
            recordOperationDetail.setDtoData(JSON.toJSONString(target));
            dbmTempateDao.updateById(target);
            updMsg = "更新成功";
        } else {
            updMsg = "记录不存在，更新失败";
        }

        try {
            //  String operationName = dbmTemplateEntity.getName() + "_" + dbmTemplateEntity.getMessageType() + "_编辑_提交";
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(dbmTemplateEntity))
                    //.setOperationName(operationName)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_MESSAGE_TEMPLATE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success(updMsg);
    }

    @Override
    public Result queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO) {
        IPage<DbmBusinessTemplateEntity> iPage = dbmBusinessTemplateDao.queryBusinessTemplate(queryDTO);
        List<BusinessTemplateVO> businessTemplateVOS = iPage.getRecords().stream().map(dbmBusinessTemplateEntity -> {
                    BusinessTemplateVO businessTemplateVO = new BusinessTemplateVO();
                    BeanUtil.copyProperties(dbmBusinessTemplateEntity, businessTemplateVO);

                    DbmTemplateEntity dbmTemplateEntity = dbmTempateDao.getById(dbmBusinessTemplateEntity.getTemplateId());
                    if (null != dbmTemplateEntity) {
                        businessTemplateVO.setMessageName(dbmTemplateEntity.getName());
                    }

                    return businessTemplateVO;
                }
        ).collect(Collectors.toList());
        return Result.page(iPage, businessTemplateVOS);
    }

    @Override
    public Result updateBusinessTemplateStatus(DbmBusinessTemplateEntity dbmBusinessTemplateEntity) {
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        String updMsg = "";
        DbmBusinessTemplateEntity target = dbmBusinessTemplateDao.getById(dbmBusinessTemplateEntity.getId());
        recordOperationDetail.setBeforeData(JSON.toJSONString(target));
        if (null != target) {
            target.setStatus(dbmBusinessTemplateEntity.getStatus())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setUpdatedBy(JwtUtils.getCurrentUserId())
                    .setUpdatedByName(employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());
            recordOperationDetail.setAfterData(JSON.toJSONString(target));
            dbmBusinessTemplateDao.updateById(target);
            updMsg = "更新成功";
        } else {
            updMsg = "记录不存在，更新失败";
        }
        try {

            DbmTemplateEntity dbmTemplateEntity = dbmTempateDao.getById(dbmBusinessTemplateEntity.getId());
            String operationName = dbmTemplateEntity.getName() + "" + dbmTemplateEntity.getMessageType() + "禁用/启用";
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(dbmBusinessTemplateEntity))
                    .setOperationName(operationName)
                    .setOperationActionEnum(OperationActionEnum.UPDATE_MESSAGE_STATUS)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.success(updMsg);
    }


    private OperationActionEnum messageLogType() {
        return OperationActionEnum.UPDATE_MESSAGE_STATUS;
    }


}