package com.navigator.dagama.service;//package com.navigator.dagama.service;
//
//import com.navigator.dagama.model.dto.MessageInfoDTO;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/3/22 11:49
// */
//@RestController
//@RequestMapping("/message")
//public class TestController {
//    @Resource
//    SendTaskService sendTaskService;
//
//    @PostMapping("/wecom")
//    public void testWechat(@RequestBody MessageInfoDTO messageInfoDTO) throws Exception {
//        sendTaskService.sendMessage(messageInfoDTO);
//    }

//}
