package com.navigator.dagama.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.facade.TempateFacade;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import com.navigator.dagama.service.DbmTempateService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/11 14:48
 */
@RestController
public class TempateFacadeImpl implements TempateFacade {
    @Resource
    private DbmTempateService dbmTempateService;

    /**
     * 查询模板
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryTemplate(QueryDTO<DbmTemplateEntity> queryDTO){
        return dbmTempateService.queryTemplate(queryDTO);
    }

    @Override
    public Result queryTemplateById(Integer id) {
        return dbmTempateService.queryTemplateById(id);
    }

    @Override
    public Result updateTemplate(DbmTemplateEntity dbmTemplateEntity) {
        return dbmTempateService.updateTemplate(dbmTemplateEntity);
    }

    public Result queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO){
        return dbmTempateService.queryBusinessTemplate(queryDTO);
    }

    public Result updateBusinessTemplateStatus(DbmBusinessTemplateEntity dbmBusinessTemplateEntity){
        return dbmTempateService.updateBusinessTemplateStatus(dbmBusinessTemplateEntity);
    }



}
