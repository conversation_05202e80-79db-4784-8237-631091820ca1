package com.navigator.dagama.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.dagama.mapper.DbmChatRoomMapper;
import com.navigator.dagama.pogo.model.entity.DbmChatRoomEntity;

@Dao
public class DbmChatRoomDao extends BaseDaoImpl<DbmChatRoomMapper, DbmChatRoomEntity> {


    public DbmChatRoomEntity getChatRoomByChatId(String chatId) {
        return this.baseMapper.selectOne(Wrappers.<DbmChatRoomEntity>lambdaQuery().eq(DbmChatRoomEntity::getChatRoomId, chatId));
    }
}
