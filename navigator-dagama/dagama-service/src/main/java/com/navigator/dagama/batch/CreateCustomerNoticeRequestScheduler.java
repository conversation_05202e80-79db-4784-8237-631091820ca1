package com.navigator.dagama.batch;

import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.service.SendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class CreateCustomerNoticeRequestScheduler extends BaseScheduleService<CustomerEntity> {
    @Resource
    CEmployFacade cEmployFacade;
    @Resource
    SendTaskService sendTaskService;

    @Override
    protected List<CustomerEntity> fetchList(int batchRecordCount) {
        return null;
    }

    @Override
    protected void process(CustomerEntity item) {

    }

    public void createCustomerNoticeRequest(CEmployEntity cEmployEntity, DbmBusinessTemplateEntity businessTemplateEntity, String title, String content){

        DbmSendTaskEntity sendTaskEntity=new DbmSendTaskEntity();
        sendTaskEntity.setBusinessCode(MessageBusinessCodeEnum.CLB_USER_MANUAL_NOTICE.name());
        sendTaskEntity.setStatus(MessageSendStatusEnum.INIT.name());

        sendTaskEntity.setTitle(title);
        sendTaskEntity.setContent(content);

        sendTaskEntity.setReceiver(cEmployEntity.getId().toString());
        sendTaskEntity.setReceiverType(businessTemplateEntity.getReceiverType());
        sendTaskEntity.setSystem(SystemEnum.COLUMBUS.getValue());

        sendTaskEntity.setMessageType(businessTemplateEntity.getMessageType());

        sendTaskEntity.setReferId(cEmployEntity.getId().toString());

        sendTaskService.save(sendTaskEntity);
    }


}
