package com.navigator.dagama.exception;


import com.navigator.common.enums.ResultCodeEnum;

public class ProjectModuleException extends RuntimeException {

        //错误代码
        ResultCodeEnum resultCode;

        public ProjectModuleException(ResultCodeEnum resultCode) {
            this.resultCode = resultCode;
        }

        public ResultCodeEnum getResultCode() {
            return resultCode;
        }

}
