//package com.navigator.dagama.config.wecom;
//
//import com.google.common.collect.Maps;
//import com.navigator.dagama.pogo.model.entity.DbmChatRoomEntity;
//import com.navigator.dagama.repository.dao.impl.DbmChatRoomDao;
//import com.navigator.dagama.repository.mapper.DbmChatRoomMapper;
//import lombok.val;
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
//import org.springframework.context.annotation.Configuration;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2021/3/29 11:19
// */
//@Configuration
//public class WxCpConfiguration {
//    @Resource
//    DbmChatRoomDao dbmChatRoomDao;
//    @Resource
//    DbmChatRoomMapper dbmChatRoomMapper;
//    //@Value("${message.companyWechat.secret}")
//    private String secret;
//    //@Value("${message.companyWechat.corpId}")
//    private String corpId;
//    //@Value("${message.companyWechat.token}")
//    private String token;
//    //@Value("${message.companyWechat.aesKey}")
//    private String aesKey;
//
//    private static Map<Integer, WxCpService> cpServices = Maps.newHashMap();
//
//    @PostConstruct
//    public void initServices() {
//        List<DbmChatRoomEntity> chatRoomEntityList = dbmChatRoomMapper.selectList(null);
//        chatRoomEntityList.forEach(chatRoomEntity -> {
//            MyWxCpConfig configStorage = new MyWxCpConfig();
//            configStorage.setCorpId(corpId);
//            configStorage.setAgentId(chatRoomEntity.getAgentId());
//            configStorage.setCorpSecret(secret);
//            configStorage.setToken(token);
//            configStorage.setAesKey(aesKey);
//            val service = new WxCpServiceImpl();
//            service.setWxCpConfigStorage(configStorage);
//            cpServices.put(service.getWxCpConfigStorage().getAgentId(), service);
//        });
//
//    }
//
//    public static WxCpService getCpService(Integer agentId) {
//        return cpServices.get(agentId);
//    }
//}
