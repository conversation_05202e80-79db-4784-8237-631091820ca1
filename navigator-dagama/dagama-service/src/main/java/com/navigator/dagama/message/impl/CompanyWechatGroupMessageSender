//package com.navigator.dagama.message.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.navigator.dagama.config.wecom.WxCpConfiguration;
//import com.navigator.dagama.message.IMessageSender;
//import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
//import com.navigator.dagama.pogo.model.entity.DbmChatRoomEntity;
//import com.navigator.dagama.pogo.model.enums.SendStatus;
//import com.navigator.dagama.repository.dao.impl.DbmChatRoomDao;
//import me.chanjar.weixin.common.error.WxErrorException;
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.bean.WxCpAppChatMessage;
//import me.chanjar.weixin.cp.constant.WxCpConsts;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/3/24 15:35
// */
//@Component("companyWechatGroup")
//public class SendCompanyWechatGroupMessage implements SendMessageStrategy {
//
//    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Resource
//    DbmChatRoomDao dbmChatRoomDao;
//
//    @Override
//    public String sendMessage(SendMessageDTO sendMessageDTO) {
//        System.out.println("发送企业微信群发消息" + JSONObject.toJSONString(sendMessageDTO));
//        //发送企业微信消息
//        String status = sendTextToChatRoom(sendMessageDTO);
//        return status;
//
//    }
//
//
//    private String sendTextToChatRoom(SendMessageDTO sendMessageDTO) {
//        try {
//            DbmChatRoomEntity chatRoomEntity = dbmChatRoomDao.getChatRoomByChatId(sendMessageDTO.getChatId());
//            WxCpService cpService = WxCpConfiguration.getCpService(chatRoomEntity.getAgentId());
//            sendChatMsg(cpService, WxCpAppChatMessage.builder()
//                    .chatId(sendMessageDTO.getChatId())
//                    .msgType(WxCpConsts.AppChatMsgType.TEXT)
//                    .content(sendMessageDTO.getSendContent())
//                    .build()
//            );
//            return SendStatus.SUCCESS.getMsg();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("发送微信模板消息回结果,错误信息：", e);
//        }
//        return SendStatus.FAIL.getMsg();
//    }
//
//    /**
//     * 群消息
//     */
//    private void sendChatMsg(WxCpService cpService, WxCpAppChatMessage wxCpAppChatMessage) {
//        try {
//            cpService.getChatService().sendMsg(wxCpAppChatMessage);
//        } catch (WxErrorException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//}
