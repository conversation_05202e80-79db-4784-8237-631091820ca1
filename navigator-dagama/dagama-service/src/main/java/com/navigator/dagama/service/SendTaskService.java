package com.navigator.dagama.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18 10:10
 */
public interface SendTaskService {

    void save(DbmSendTaskEntity dbmSendTaskEntity);

    /**
     * 统一发送消息入口
     *
     * @param messageInfoDTO 消息发送所需参数
     * @throws Exception
     */
    Result<String> sendMessage(MessageInfoDTO messageInfoDTO);

    Result<String> sendMessage(DbmSendTaskEntity sendTaskEntity);

    Result<String> reSendMessage(Integer sendTaskId);

    boolean isSendSuccess(String bizId);

    List<DbmSendTaskEntity> querySendTaskEntity(QueryDTO<DbmSendTaskEntity> queryDTO);

    void pushWebsocketMsg(Integer userId, Object object);

    void pushWebsocketInMailMsg(WebsocketInMailDTO websocketInMailDTO);
}
