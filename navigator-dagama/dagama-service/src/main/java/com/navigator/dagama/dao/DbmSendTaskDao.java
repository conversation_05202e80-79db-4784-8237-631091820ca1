package com.navigator.dagama.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.mapper.DbmSendTaskMapper;
import com.navigator.dagama.pogo.model.dto.QuerySendTaskDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.vo.SendTaskVO;

import java.util.Date;
import java.util.List;

@Dao
public class DbmSendTaskDao extends BaseDaoImpl<DbmSendTaskMapper, DbmSendTaskEntity> {

    public boolean updateById(DbmSendTaskEntity entity) {
        entity.setUpdatedAt(new Date());
        return super.updateById(entity);
    }

    /**
     * 发送列表
     *
     * @param queryDTO
     * @return
     */
    public Result querySendTas(QueryDTO<DbmSendTaskEntity> queryDTO) {
        Page<DbmSendTaskEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        DbmSendTaskEntity condition = queryDTO.getCondition();
        IPage<DbmSendTaskEntity> iPage = this.baseMapper.selectPage(page, Wrappers.<DbmSendTaskEntity>lambdaQuery()
                .eq(StringUtil.isNotEmpty(condition.getBusinessCode()), DbmSendTaskEntity::getBusinessCode, condition.getBusinessCode())
                .eq(StringUtil.isNotEmpty(condition.getStatus()), DbmSendTaskEntity::getStatus, condition.getStatus())
                .orderByDesc(DbmSendTaskEntity::getId));
        List<SendTaskVO> sendTaskVOS = BeanConvertUtils.convert2List(SendTaskVO.class, iPage.getRecords());
        return Result.page(iPage, sendTaskVOS);
    }

    /**
     * 发送列表
     *
     * @param queryDTO
     * @return
     */
    public Result queryUnSendTask(QueryDTO<QuerySendTaskDTO> queryDTO) {
        Page<DbmSendTaskEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        QuerySendTaskDTO condition = queryDTO.getCondition();
        IPage<DbmSendTaskEntity> iPage = this.baseMapper.selectPage(page, Wrappers.<DbmSendTaskEntity>lambdaQuery()
                .eq(StringUtil.isNotEmpty(condition.getBusinessCode()), DbmSendTaskEntity::getBusinessCode, condition.getBusinessCode())
                .in(CommonListUtil.notNullOrEmpty(condition.getStatusList()), DbmSendTaskEntity::getStatus, condition.getStatusList())
                .lt(DbmSendTaskEntity::getCreatedAt, DateTimeUtil.addMinute(-5))
                .orderByDesc(DbmSendTaskEntity::getId));
        //List<SendTaskVO> sendTaskVOS = BeanConvertUtils.convert2List(SendTaskVO.class, iPage.getRecords());
        return Result.page(iPage, iPage.getRecords());
    }

    /**
     * 发送列表
     *
     * @param queryDTO
     * @return
     */
    public Result querySendTask(QueryDTO<DbmSendTaskEntity> queryDTO) {
        Page<DbmSendTaskEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        DbmSendTaskEntity condition = queryDTO.getCondition();
        IPage<DbmSendTaskEntity> iPage = this.baseMapper.selectPage(page, Wrappers.<DbmSendTaskEntity>lambdaQuery()
                .eq(StringUtil.isNotEmpty(condition.getBusinessCode()), DbmSendTaskEntity::getBusinessCode, condition.getBusinessCode())
                .eq(StringUtil.isNotEmpty(condition.getStatus()), DbmSendTaskEntity::getStatus, condition.getStatus())
                .orderByDesc(DbmSendTaskEntity::getId));
        List<DbmSendTaskEntity> sendTaskEntityList = iPage.getRecords();
        return Result.page(iPage, sendTaskEntityList);
    }
}
