package com.navigator.dagama.wechat.impl.wechat;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpUserTagService;
import me.chanjar.weixin.mp.bean.tag.WxTagListUser;
import me.chanjar.weixin.mp.bean.tag.WxUserTag;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/30 10:51
 */
@Service
@Slf4j
public class WeChatService {
    /**
     * 获取用户标签
     *
     * @param wxMpService
     * @param tagName
     * @return
     */
    public List<String> getTagListUser(WxMpService wxMpService, String tagName) {
        try {
            WxMpUserTagService userTagService = wxMpService.getUserTagService();
            List<WxUserTag> wxUserTags = userTagService.tagGet();
            for (WxUserTag wxUserTag : wxUserTags) {
                String name = wxUserTag.getName();
                if (tagName.equals(name)) {
                    Long id = wxUserTag.getId();
                    WxTagListUser wxTagListUser = userTagService.tagListUser(id, "");
                    WxTagListUser.WxTagListUserData tagListUserData = wxTagListUser.getData();
                    return tagListUserData.getOpenidList();
                }
            }
        } catch (WxErrorException e) {
            log.error(this.getClass().getSimpleName() + "getTagListUser error:", e);
        }
        return null;
    }
}
