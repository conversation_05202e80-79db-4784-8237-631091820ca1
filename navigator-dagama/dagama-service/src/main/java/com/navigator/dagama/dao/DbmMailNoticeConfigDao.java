package com.navigator.dagama.dao;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.dagama.mapper.DbmMailNoticeConfigMapper;
import com.navigator.dagama.pogo.model.entity.DbmMailNoticeConfigEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-02 09:49
 **/
@Dao
public class DbmMailNoticeConfigDao extends BaseDaoImpl<DbmMailNoticeConfigMapper, DbmMailNoticeConfigEntity> {

    public DbmMailNoticeConfigEntity getFactoryMailConfig(String factoryCode, Integer category2) {
        if (null == category2) {
            category2 = 0;
        }
        if (StringUtils.isBlank(factoryCode)) {
            factoryCode = "LDC";
        }
        List<DbmMailNoticeConfigEntity> noticeConfigEntityList = this.list(Wrappers.<DbmMailNoticeConfigEntity>lambdaQuery()
                .eq(DbmMailNoticeConfigEntity::getFactoryCode, factoryCode)
                .like(DbmMailNoticeConfigEntity::getCategory2, "," + category2 + ",")
                .eq(DbmMailNoticeConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(noticeConfigEntityList) ? noticeConfigEntityList.get(0) : null;

    }

    public DbmMailNoticeConfigEntity getFactoryMailConfigBySender(String sender) {
        List<DbmMailNoticeConfigEntity> noticeConfigEntityList = this.list(Wrappers.<DbmMailNoticeConfigEntity>lambdaQuery()
                .eq(DbmMailNoticeConfigEntity::getFromMail, sender)
                .eq(DbmMailNoticeConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(noticeConfigEntityList) ? noticeConfigEntityList.get(0) : null;
    }
}
