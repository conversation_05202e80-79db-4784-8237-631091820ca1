package com.navigator.dagama.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;

public interface DbmSendTaskService {

    /**
     * 发送列表信息
     *
     * @param queryDTO
     * @return
     */
    Result querySendTas(QueryDTO<DbmSendTaskEntity> queryDTO);

    Result queryUnSendTask(QueryDTO<DbmSendTaskEntity> queryDTO);

}