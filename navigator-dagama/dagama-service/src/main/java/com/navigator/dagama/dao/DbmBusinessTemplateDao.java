package com.navigator.dagama.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.dagama.mapper.DbmBusinessTemplateMapper;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Dao
public class DbmBusinessTemplateDao extends BaseDaoImpl<DbmBusinessTemplateMapper, DbmBusinessTemplateEntity> {
    @Resource
    private DbmTempateDao dbmTempateDao;

    public List<BusinessTemplateDTO> findTempByCode(String code) {
        List<DbmBusinessTemplateEntity> dbmBusinessTemplateEntities = null;
        try {
            dbmBusinessTemplateEntities = this.list(Wrappers.<DbmBusinessTemplateEntity>lambdaQuery()
                    .eq(DbmBusinessTemplateEntity::getBusinessCode, code)
                    .eq(DbmBusinessTemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                    .eq(DbmBusinessTemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<BusinessTemplateDTO> returnInfoDTOS = new ArrayList<>();
        for (DbmBusinessTemplateEntity entity : dbmBusinessTemplateEntities) {
            BusinessTemplateDTO returnInfoDTO = new BusinessTemplateDTO();

            DbmTemplateEntity templateEntity = dbmTempateDao.getDbmTemplateById(entity.getTemplateId());
            returnInfoDTO.setBusinessTemplateId(entity.getId())
                    .setTemplateEntity(templateEntity)
                    .setMessageType(entity.getMessageType())
                    .setMessageCategory(entity.getMessageCategory())
                    .setNextAction(entity.getNextAction())
                    .setReceiver(entity.getReceiver())
                    .setReceiverType(entity.getReceiverType())
                    .setSender(entity.getSender())
                    .setCopyerType(entity.getCopyerType())
                    .setCopyer(entity.getCopyer());
            returnInfoDTOS.add(returnInfoDTO);
            /*if (!MessageCategoryEnum.NOTICE.getValue().equals(entity.getMessageCategory()) && StrUtil.isNotEmpty(entity.getNextAction())) {
                returnInfoDTOS.addAll(this.findTempByCode(entity.getNextAction()));
            }*/
        }

        return returnInfoDTOS;
    }

    public IPage queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO) {
        Page<DbmBusinessTemplateEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        DbmBusinessTemplateEntity condition = queryDTO.getCondition();
        if (null == condition) {
            condition = new DbmBusinessTemplateEntity();
        }
        IPage<DbmBusinessTemplateEntity> iPage = this.page(page, Wrappers.<DbmBusinessTemplateEntity>lambdaQuery()
                .like(StrUtil.isNotEmpty(condition.getUpdatedByName()), DbmBusinessTemplateEntity::getUpdatedByName, condition.getUpdatedByName())
                .eq(DbmBusinessTemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(DbmBusinessTemplateEntity::getCreatedAt));
        return iPage;
    }


}
