package com.navigator.dagama.service.logic;

import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.batch.BaseScheduleService;
import com.navigator.dagama.dao.DbmBatchNoticeDao;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.dao.DbmSendTaskDao;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.entity.DbmBatchNoticeEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageBatchProcessStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;
import com.navigator.dagama.service.SendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BatchColumbusUserNoticeProcessor extends BaseScheduleService<DbmSendTaskEntity> {
    @Resource
    DbmBusinessTemplateDao dbmBusinessTemplateDao;
    @Resource
    DbmSendTaskDao dbmSendTaskDao;
    @Resource
    DbmBatchNoticeDao dbmBatchNoticeDao;
    @Resource
    CEmployFacade cEmployFacade;
    @Resource
    SendTaskService sendTaskService;

    @Override
    protected List<DbmSendTaskEntity> fetchList(int batchRecordCount) {

        QueryDTO<DbmSendTaskEntity> queryDTO = new QueryDTO<>();
        DbmSendTaskEntity condition = new DbmSendTaskEntity();
        condition.setBusinessCode(MessageBusinessCodeEnum.CLB_USER_MANUAL_NOTICE.name());
        condition.setStatus(MessageSendStatusEnum.INIT.name());
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(100);
        queryDTO.setCondition(condition);

        List<DbmSendTaskEntity> sendTaskEntityList = sendTaskService.querySendTaskEntity(queryDTO);

        return sendTaskEntityList;
    }

    @Override
    protected void process(DbmSendTaskEntity item) {
        item.setStatus("PROCESSING");
        dbmSendTaskDao.updateById(item);

        sendTaskService.sendMessage(item);

        System.out.println("XXXXXXXXXXX" + item.getId());
    }

    public void createColumbusUserNoticeRequest(DbmBatchNoticeEntity dbmBatchNoticeEntity) {
        MessageBatchProcessStatusEnum processStatusEnum = MessageBatchProcessStatusEnum.INIT;

        dbmBatchNoticeEntity.setStatus(processStatusEnum.getValue());
        dbmBatchNoticeDao.save(dbmBatchNoticeEntity);

        String businessCode = dbmBatchNoticeEntity.getBusinessCode();
        List<BusinessTemplateDTO> listBizT = dbmBusinessTemplateDao.findTempByCode(businessCode);

        Integer currentId = 0;
        Integer totalCount = 0;
        Integer pageNum = 0;
        Boolean processing = true;

        QueryDTO<CEmployDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageSize(100);
        CEmployDTO condition = new CEmployDTO();
        queryDTO.setCondition(condition);


        while (processing) {
            processStatusEnum = MessageBatchProcessStatusEnum.CREATING;
            pageNum = pageNum + 1;
            queryDTO.setPageNo(pageNum);

//            Result resultCEmployList = cEmployFacade.queryEmployList(queryDTO);
            Result resultCEmployList = mockCEmployList(queryDTO);
            int currentPage = (int) resultCEmployList.getPageNum();
            int totalPage = (int) resultCEmployList.getTotalPage();

            List<CEmployEntity> cEmployEntityList = (List<CEmployEntity>) resultCEmployList.getData();

            for (CEmployEntity cEmployEntity : cEmployEntityList) {
                createColumbusUserNoticeRequest(dbmBatchNoticeEntity, cEmployEntity, listBizT);
                currentId = cEmployEntity.getId();
            }


            if (currentPage == totalPage) {
                processing = false;
                processStatusEnum = MessageBatchProcessStatusEnum.SENDING;
            }

            dbmBatchNoticeEntity.setTotalCount((int) resultCEmployList.getTotalCount());
            dbmBatchNoticeEntity.setCurrentId(currentId);
            dbmBatchNoticeEntity.setStatus(processStatusEnum.getValue());
            dbmBatchNoticeDao.updateById(dbmBatchNoticeEntity);
        }


    }

    public void createColumbusUserNoticeRequest(DbmBatchNoticeEntity dbmBatchNoticeEntity, CEmployEntity cEmployEntity, List<BusinessTemplateDTO> listBizT) {
        for (BusinessTemplateDTO businessTemplateDTO : listBizT) {
            createColumbusUserNoticeRequest(dbmBatchNoticeEntity, cEmployEntity, businessTemplateDTO);
        }
    }

    public void createColumbusUserNoticeRequest(DbmBatchNoticeEntity dbmBatchNoticeEntity, CEmployEntity cEmployEntity, BusinessTemplateDTO businessTemplateDTO) {
        DbmSendTaskEntity sendTaskEntity = new DbmSendTaskEntity();

        try {
            String messageType = businessTemplateDTO.getMessageType();
            MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByValue(messageType);

            sendTaskEntity.setBusinessCode(dbmBatchNoticeEntity.getBusinessCode());
            sendTaskEntity.setTitle(dbmBatchNoticeEntity.getTitle());
            sendTaskEntity.setContent(dbmBatchNoticeEntity.getContent());

            sendTaskEntity.setMessageType(businessTemplateDTO.getMessageType());
            sendTaskEntity.setBusinessTemplateId(businessTemplateDTO.getBusinessTemplateId());

            sendTaskEntity.setCopyerType(businessTemplateDTO.getCopyerType());
            sendTaskEntity.setCopyer(businessTemplateDTO.getCopyer());
            sendTaskEntity.setReceiverType(businessTemplateDTO.getReceiverType());

            if (messageTypeEnum == MessageTypeEnum.EMAIL) {
                sendTaskEntity.setReceiver(cEmployEntity.getEmail());
            } else if (messageTypeEnum == MessageTypeEnum.IN_MAIL) {
                sendTaskEntity.setReceiver(cEmployEntity.getId().toString());
            } else if (messageTypeEnum == MessageTypeEnum.SMS) {
                sendTaskEntity.setReceiver(cEmployEntity.getPhone());
            }
            sendTaskEntity.setStatus(MessageSendStatusEnum.INIT.name());

            sendTaskEntity.setReferId(dbmBatchNoticeEntity.getId().toString());
            sendTaskEntity.setMemo("批量通知任务；");
            sendTaskEntity.setSystem(dbmBatchNoticeEntity.getSystem());
            sendTaskEntity.setTryTimes(0);

            dbmSendTaskDao.save(sendTaskEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public Result mockCEmployList(QueryDTO<CEmployDTO> queryDTO) {
        Integer pn = queryDTO.getPageNo();
        Integer pz = queryDTO.getPageSize();

        List<CEmployEntity> list = new ArrayList<>();

        for (int i = 0; i < pz; i++) {
            Integer startId = (pn - 1) * pz + i + 110001;
            CEmployEntity cEmployEntity = new CEmployEntity();
            cEmployEntity.setId(startId);
            cEmployEntity.setEmail(startId + "@fn.com");
            cEmployEntity.setPhone("13800" + startId);
            list.add(cEmployEntity);
        }

        Result rtn = new Result();
        rtn.setTotalPage(5);
        rtn.setPageNum(pn);
        rtn.setTotalCount(500);
        rtn.setPageSize(100);
        rtn.setData(list);


        return rtn;

    }

}
