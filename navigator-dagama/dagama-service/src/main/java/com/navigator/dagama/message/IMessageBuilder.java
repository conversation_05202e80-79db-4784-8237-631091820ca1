package com.navigator.dagama.message;

import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;

import java.util.Map;

/**
 * @Author: wang tao
 * @Date: 2021/3/17
 * @Time: 15:23
 * @Desception: 构建完整的消息内容
 */
public interface IMessageBuilder {

    /**
     * 拼接不同的消息内容
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map<String, Object>
     */
    Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity);

    SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity);

}
