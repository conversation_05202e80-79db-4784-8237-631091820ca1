package com.navigator.dagama.message.impl;

import com.navigator.common.util.ExtractKeyUtil;
import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.common.util.TemplateUtil;
import com.navigator.dagama.message.IMessageBuilder;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: wang tao
 * @Date: 2021/3/17
 * @Time: 15:46
 * @Desception: 构建短信消息
 */
@Component("bsms")
public class SmsMessageBuilder implements IMessageBuilder {

    /**
     * 构建短信消息
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity) {
        Map<String, Object> map = new HashMap<>();
        String template = templateEntity.getContent();
        map.put("sendContent", dataMap.get("sendContent"));
        //拼接content,存入task表中
        String content = TemplateUtil.renderStringByBrace(template, dataMap);
        System.out.println("\n\n\n内容:" + content);
        map.put("content", content);

        //拼接sendContent,这是要发送的内容,不用存入数据库
        //1、用正则表达式通过{}获取关键词,按顺序排成一个数组，如[expertName, dateTime, address, password, phone]
        List<String> keysList = ExtractKeyUtil.extractMessageByBrace(template);
        //2、遍历keyList
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < keysList.size(); i++) {
            //3、遍历dataMap
            Set<Map.Entry<String, Object>> sets = dataMap.entrySet();
            for (Map.Entry<String, Object> entry : sets) {
                if (entry.getKey().equals(keysList.get(i))) {
                    //如果entry的key值等于keysList里的某一元素，那么就取出该entry的value值，并用分号隔开
                    stringBuilder.append(entry.getValue() + ";");
                }
            }
        }
        //4、除去sendContent末尾的分号
        String sentContent = "";
        if (stringBuilder.length() > 0) {
            sentContent = stringBuilder.substring(0, stringBuilder.length() - 1);
        }
        //5、put到map中并返回map
        map.put("sendContent", sentContent);
        return map;
    }

    @Override
    public SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity) {
        SendMessageDTO sendMessageDTO = new SendMessageDTO();

        try {
            String content = TemplateRenderUtil.templateRender(messageInfoDTO.getDataMap(), templateEntity.getContent());

            String title = templateEntity.getTitle();

            sendMessageDTO.setTitle(title)
                    .setContent(content)
                    .setSendContent(content);

        } catch (Exception e) {

        }

        return sendMessageDTO;
    }
}
