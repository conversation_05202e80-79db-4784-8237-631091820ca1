package com.navigator.dagama.service.impl;

import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.dao.DbmMailNoticeConfigDao;
import com.navigator.dagama.pogo.model.entity.DbmMailNoticeConfigEntity;
import com.navigator.dagama.service.DbmMailNoticeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-02 10:41
 **/
@Slf4j
@Service
public class DbmMailNoticeConfigServiceImpl implements DbmMailNoticeConfigService {
    @Resource
    private DbmMailNoticeConfigDao mailNoticeConfigDao;

    @Override
    public Result importMailNoticeConfig(MultipartFile uploadFile) {
        List<DbmMailNoticeConfigEntity> mailNoticeConfigEntityList = new ArrayList<>();
        try {
//            mailNoticeConfigEntityList = EasyPoiUtils.importExcel(uploadFile, 0, 1, DbmMailNoticeConfigEntity.class);
            mailNoticeConfigEntityList = EasyPoiUtils.importExcel(uploadFile, 1, 1, DbmMailNoticeConfigEntity.class);
            log.info("导入消息邮件的excel文件内容：{}", FastJsonUtils.getBeanToJson(mailNoticeConfigEntityList));
            for (DbmMailNoticeConfigEntity mailNoticeEntity : mailNoticeConfigEntityList) {
                DbmMailNoticeConfigEntity checkMailNoticeConfigEntity = null;
                String category2Ids = "";
                if (StringUtils.isNotBlank(mailNoticeEntity.getCategory2Name())) {
                    if ("通用".equals(mailNoticeEntity.getCategory2Name())) {
                        category2Ids = "0";
                    } else {
                        List<String> category2NameList = Arrays.stream(mailNoticeEntity.getCategory2Name()
                                .split(",")).collect(Collectors.toList());
                        category2Ids = category2NameList.stream().map(GoodsCategoryEnum::getValueByDesc).map(String::valueOf).collect(Collectors.joining(","));
                    }
                    mailNoticeEntity.setCategory2(category2Ids);
                }
                if (null != mailNoticeEntity.getId()) {
                    checkMailNoticeConfigEntity = mailNoticeConfigDao.getById(mailNoticeEntity.getId());
                }
                Timestamp now = DateTimeUtil.now();
                if (null == checkMailNoticeConfigEntity) {
                    mailNoticeEntity.setCategory2("," + category2Ids + ",")
                            .setCreatedAt(now)
                            .setUpdatedAt(now)
                            .setIsDeleted(null == mailNoticeEntity.getIsDeleted() ? IsDeletedEnum.NOT_DELETED.getValue() : mailNoticeEntity.getIsDeleted())
                            .setCreatedBy("System")
                            .setUpdatedBy("System");
                    mailNoticeConfigDao.save(mailNoticeEntity);
                } else {
                    checkMailNoticeConfigEntity.setCategory2("," + category2Ids + ",")
                            .setCategory2Name(mailNoticeEntity.getCategory2Name())
                            .setFromMail(mailNoticeEntity.getFromMail())
                            .setFactoryCode(mailNoticeEntity.getFactoryCode())
                            .setCc(mailNoticeEntity.getCc())
                            .setReplyTo(mailNoticeEntity.getReplyTo())
                            .setPwd(mailNoticeEntity.getPwd())
                            .setIsDeleted(null == mailNoticeEntity.getIsDeleted() ? checkMailNoticeConfigEntity.getIsDeleted() : mailNoticeEntity.getIsDeleted())
                            .setUpdatedAt(now)
                            .setUpdatedBy("System");
                    mailNoticeConfigDao.updateById(checkMailNoticeConfigEntity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误" + e.toString());
        }
        return Result.success("导入邮件配置条数：" + mailNoticeConfigEntityList.size(), mailNoticeConfigEntityList);
    }
}
