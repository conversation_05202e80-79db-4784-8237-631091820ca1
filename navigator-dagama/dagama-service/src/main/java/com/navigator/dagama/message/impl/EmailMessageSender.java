package com.navigator.dagama.message.impl;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.dao.DbmMailNoticeConfigDao;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmMailNoticeConfigEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;
import com.navigator.dagama.pogo.model.enums.ReceiverTypeEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import com.navigator.dagama.utils.mail.MailUtilImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 18:45
 */
@Component("email")
public class EmailMessageSender extends AbstractMessageSender {
    @Resource
    private MailUtilImpl mailUtil;
    @Value("${spring.mail.username}")
    private String senderMail;
    //    @Resource
//    MailNoticeProperties mailNoticeProperties;
    @Resource
    private DbmMailNoticeConfigDao mailNoticeConfigDao;


    @Override
    public SendMessageDTO fullMessageSender(SendMessageDTO sendMessageDTO, DbmSendTaskEntity sendTaskEntity) {

        Map<String, Object> mapBizData = sendMessageDTO.getDataMap();

        try {

            //指定发送人的情况
            String factoryCode = "LDC";
            Integer categoryId = 0;

            //先取配置的
            String sender = sendMessageDTO.getSender();

            if (StringUtil.isEmpty(sender)) {
                //如果配置为空，则取业务数据（兼容老逻辑，SP20221205后废除）
                Object objSender = mapBizData.get("sender");
                if (null != objSender) {
                    sender = mapBizData.get("sender").toString();
                }
            }

            //指定发送人为工厂邮箱时
            if (ReceiverTypeEnum.FACTORY_MAIL.getValue().equals(sender)) {
                factoryCode = mapBizData.get("factoryCode").toString();
                categoryId = (Integer) mapBizData.get("categoryId");
            }

            //取配置
//            MailSendConfigDTO factoryMailConfig = mailNoticeProperties.getFactoryMailConfig(factoryCode, categoryId);
            DbmMailNoticeConfigEntity factoryMailConfig = mailNoticeConfigDao.getFactoryMailConfig(factoryCode, categoryId);
            if (null != factoryMailConfig) {
                sendTaskEntity.setSender(factoryMailConfig.getFromMail())
                        .setSenderName("")
                        .setPwd(factoryMailConfig.getPwd().trim())
                        .setReplayTo(factoryMailConfig.getReplyTo())
                        .setCopyer(factoryMailConfig.getCc())
                        .setCopyerType(sendMessageDTO.getCopyerType());
            }

            //指定抄送人
            if (StringUtil.isNotEmpty(sendMessageDTO.getCopyer())) {
                sendTaskEntity.setCopyer(sendMessageDTO.getCopyer());
                sendTaskEntity.setCopyerType(sendMessageDTO.getCopyerType());
            }

        } catch (Exception e) {
            System.out.println("no setting for sender");
        }

        //如果没有发送人，取默认配置（兼容）
        if (StringUtil.isEmpty(sendTaskEntity.getSender())) {
            sendTaskEntity.setSender(senderMail);
        }

        dbmSendTaskDao.updateById(sendTaskEntity);

        return sendMessageDTO;
    }

    @Override
    protected SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO
            businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {

        String receiverMail = "";

        List<HashMap<String, String>> receiverList = null;
        try {
            //根据不同的接收人类型进行处理
            /*
            1、客户签章提醒
                接收人类型=CUSTOMER_CONTACT
                sendMessageDTO.receivers=客户ID
            2、审批提醒
                接收人类型=PERSONAL
                sendMessageDTO.receivers=用户ID
            3、指定邮件组、邮件列表
                接收人类型=EMAIL_GROUP、EMAIL_LIST
                取businessTemplateDTO.receiver
             */

            if (StringUtil.isNotEmpty(businessTemplateDTO.getReceiverType())) {
                ReceiverTypeEnum receiverTypeEnum = ReceiverTypeEnum.getByValue(businessTemplateDTO.getReceiverType());

                //指定收件人，取模板中数据
                if (receiverTypeEnum == ReceiverTypeEnum.EMAIL_GROUP || receiverTypeEnum == ReceiverTypeEnum.EMAIL_LIST) {
                    receiverMail = businessTemplateDTO.getReceiver();
                } else if (receiverTypeEnum == ReceiverTypeEnum.CUSTOMER_CONTACT) {
                    //客户联系人，根据CustomerId进行处理
                    //获取客户Id列表，一般只有一个
                    List<String> customerIds = sendMessageDTO.getReceivers();
                    if (CommonListUtil.notNullOrEmpty(customerIds)) {
                        Integer customerId = Integer.valueOf(customerIds.get(0));
                        List<ReceiverContactVO> receiverContactVOS = messageUserService.getReceiverMessage(customerId, sendMessageDTO.getFactoryCode(), sendMessageDTO.getCategoryId(), sendMessageDTO.getSalesType());

                        receiverMail = getReceiverMail(receiverContactVOS);
                    }
                } else if (receiverTypeEnum == ReceiverTypeEnum.PERSONAL || receiverTypeEnum == ReceiverTypeEnum.ROLE) {
                    //个人，取用户Id进行处理
                    //获取用户Id，可以为多个
                    List<String> receivers = sendMessageDTO.getReceivers();
                    if (CommonListUtil.notNullOrEmpty(receivers)) {
                        List<ReceiverContactVO> receiverContactVOS = messageUserService.getReceiverMessage(receivers, sendMessageDTO.getMessageType(), sendMessageDTO.getReceiverType(), sendMessageDTO.getSystem(), sendMessageDTO.getCustomerId());
                        receiverMail = getReceiverMail(receiverContactVOS);
                    } else {
                        //兼容处理（SP20221205后废除）
                        Map<String, Object> mapBizData = sendMessageDTO.getDataMap();
                        receiverList = (List<HashMap<String, String>>) mapBizData.get("receiver");

                        //工厂联系人，取map中的数据
                        if (null != mapBizData) {
                            StringBuilder sbReceiverMail = new StringBuilder();

                            for (HashMap<String, String> m : receiverList) {
                                sbReceiverMail.append(m.get("email")).append(";");
                            }
                            receiverMail = sbReceiverMail.toString();
                        }
                    }
                } else if (receiverTypeEnum == ReceiverTypeEnum.COLUMBUS_USER) {
                    List<String> receivers = sendMessageDTO.getReceivers();
                    if (CommonListUtil.notNullOrEmpty(receivers)) {
                        Integer cEmployId = Integer.valueOf(receivers.get(0));
                        List<ReceiverContactVO> receiverContactVOS = messageUserService.getReceiverMessage(cEmployId);
                        receiverMail = getReceiverMail(receiverContactVOS);
                    }
                } else {
                    List<String> receivers = sendMessageDTO.getReceivers();
                    List<ReceiverContactVO> receiverContactVOS = messageUserService.getReceiverMessage(receivers, sendMessageDTO.getMessageType(), sendMessageDTO.getReceiverType(), sendMessageDTO.getSystem(), sendMessageDTO.getCustomerId());
                    receiverMail = getReceiverMail(receiverContactVOS);
                }
            }
        } catch (Exception e) {
            System.out.println("no setting for receiver");
        }

        sendTaskEntity.setReceiver(receiverMail);
        sendTaskEntity.setReceiverType(businessTemplateDTO.getReceiverType());
        dbmSendTaskDao.updateById(sendTaskEntity);

        return sendMessageDTO;
    }

    @Override
    protected DbmSendTaskEntity buildSendTask(SendMessageDTO sendMessageDTO, DbmSendTaskEntity sendTaskEntity) {
        return sendTaskEntity;
    }

    @Override
    public String sendTheMessage(DbmSendTaskEntity sendTaskEntity) {
        String sendError = "";
        try {
            if (sendTaskEntity.isResend()) {
                sendTaskEntity = reBuildSender(sendTaskEntity);
            }
            System.out.println(sendTaskEntity.getTitle());
            //发送带html的邮件
            sendTaskEntity = mailUtil.sendHtmlMail(sendTaskEntity);

            if (!MessageSendStatusEnum.isSuccess(sendTaskEntity.getStatus())) {
                sendError = sendTaskEntity.getMemo();
            }

            dbmSendTaskDao.updateById(sendTaskEntity);

        } catch (MailException e) {
            e.printStackTrace();
            sendError = "发送异常";
        }
        return sendError;
    }

    private SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, Integer customerId) {
        //3.1 解析接收人  默认取数据库接收人
        List<String> receiverIdList = StrUtil.isEmpty(businessTemplateDTO.getReceiver()) ?
                sendMessageDTO.getReceivers() : Arrays.asList(businessTemplateDTO.getReceiver().split(";"));
        // 获取接收人信息
        List<ReceiverContactVO> userContactVOS = new ArrayList<>();
        userContactVOS = messageUserService.getReceiverMessage(receiverIdList,
                MessageTypeEnum.getByValue(businessTemplateDTO.getMessageType()),
                ReceiverTypeEnum.getByValue(businessTemplateDTO.getReceiverType()),
                sendMessageDTO.getSystem(), customerId);
        if (null != userContactVOS) {
            sendMessageDTO.setReceiver(userContactVOS);
        }

        return sendMessageDTO;
    }

    public DbmSendTaskEntity reBuildSender(DbmSendTaskEntity dbmSendTaskEntity) {
        if (dbmSendTaskEntity.isResend()) {
//            MailSendConfigDTO mailSendConfigDTO = mailNoticeProperties.getFactoryMailConfigBySender(dbmSendTaskEntity.getSender());
            DbmMailNoticeConfigEntity mailSendConfigEntity = mailNoticeConfigDao.getFactoryMailConfigBySender(dbmSendTaskEntity.getSender());
            dbmSendTaskEntity.setSender(mailSendConfigEntity.getFromMail());
            dbmSendTaskEntity.setSenderName("");
            dbmSendTaskEntity.setPwd(mailSendConfigEntity.getPwd());
        }
        operationLogFacade.saveTraceLog(new TraceLogDTO(dbmSendTaskEntity.getReferId(), "EmailMessageSender.reBuildSender", JSON.toJSONString(dbmSendTaskEntity)));
        return dbmSendTaskEntity;
    }

    private String getReceiverMail(List<ReceiverContactVO> receiverContactVOS) {
        String receiverMail = "";
        if (CommonListUtil.notNullOrEmpty(receiverContactVOS)) {
            StringBuilder sbReceiverMail = new StringBuilder();
            for (ReceiverContactVO receiverContactVO : receiverContactVOS) {
                sbReceiverMail.append(receiverContactVO.getEmail()).append(";");
            }
            receiverMail = sbReceiverMail.toString();
        }
        return receiverMail;
    }

}
