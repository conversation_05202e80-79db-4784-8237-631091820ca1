package com.navigator.dagama.service.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.service.DbmBusinessTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DbmBusinessTemplateServiceImpl implements DbmBusinessTemplateService {
    @Resource
    DbmBusinessTemplateDao businessTemplateDao;

    @Override
    public Result queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO) {
        return Result.success(businessTemplateDao.queryBusinessTemplate(queryDTO));
    }
}