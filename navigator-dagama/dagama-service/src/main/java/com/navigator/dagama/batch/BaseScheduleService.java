package com.navigator.dagama.batch;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public abstract class BaseScheduleService<T> {
    protected static final int BATCH_RECORD_COUNT = 10;  //单次获取数据的limit count
    protected int MAX_PROCESS_RECORD_COUNT = 100; // default value - job can process max amount

    protected String jobName = this.getClass().getSimpleName();
    protected int processed_count = 0;

    @Resource
    OperationLogFacade operationLogFacade;


    public void execute() {
        try {
            log.info("{%s} start work.{%s}", jobName, DateTimeUtil.formatDateTimeString());
            saveTraceLog(jobName, "execute", String.format("{%s} start work.{%s}", jobName, DateTimeUtil.formatDateTimeString()));

            boolean finalRoundFlag = false;
            processed_count = 0;

            while (true) {
                int batchRecordCount = getBatchRecordCount();
                if (processed_count >= MAX_PROCESS_RECORD_COUNT) break;

                //Get data list
                List<T> dataList = fetchList(batchRecordCount);

                if (null != dataList && dataList.size() > 0) {
                    if (dataList.size() < batchRecordCount) {
                        finalRoundFlag = true;
                    }

                    saveTraceLog(jobName, "fetchList", String.format("fetch {%s} records", dataList.size()));

                    processList(dataList);

                } else {
                    // Logger.debug(this, "no data found to process");
                    finalRoundFlag = true;
                    saveTraceLog(jobName, "fetchList", String.format("fetch {%s} records", 0));
                }

                if (finalRoundFlag) break;
            }

            log.info("{%s} finished work.process {%s} records。{%s}", jobName, processed_count, DateTimeUtil.formatDateTimeString());
        } catch (Exception e) {
            log.error("{%s} has exception : {%s}", jobName, e.getMessage());
        }
    }

    protected abstract List<T> fetchList(int batchRecordCount);

    protected void processList(List<T> list) {
        if (list.size() > 0) {
            for (T item : list) {
                try {
                    process(item);
                    processed_count = processed_count + 1;
                } catch (Exception e) {
                    saveTraceLog(jobName, "process.exception", String.format("process exception message: {%s} ", e.getMessage()));
                    log.error("{%s} process data has exception : {%s}", jobName, e.getMessage());
                }
            }
        }
    }

    protected abstract void process(T item);

    protected int getBatchRecordCount() {
        return BATCH_RECORD_COUNT;
    }

    private void saveTraceLog(String jobName, String bizModule, String logInfo) {
        try {
            operationLogFacade.saveTraceLog(new TraceLogDTO("Schedule." + jobName, bizModule, logInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
