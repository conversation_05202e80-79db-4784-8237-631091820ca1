package com.navigator.dagama.message.impl;


import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.TemplateUtil;
import com.navigator.dagama.message.IMessageBuilder;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: wang tao
 * @Date: 2021/3/17
 * @Time: 15:48
 * @Desception: 构建微信消息
 */
@Component("bwechat")
public class WeChatIMessageBuilder implements IMessageBuilder {


    /**
     * 构建微信消息
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map
     */
    @Override
    public Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity) {

        String content;
        Map<String, Object> map = new HashMap<>();
        Set<Map.Entry<String, Object>> entries = dataMap.entrySet();

        //拼接content，存入task表
        //1、要将dataMap做一步转化，因为dataMap里的key比template里少了".DATA"
        Map<String, Object> temp = new HashMap<>();
        //2、遍历dataMap，给每个key值加上".DATA"
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            key = key + ".DATA";
            temp.put(key, entry.getValue().toString());
        }
        //3、用正则表达式替换模板里的占位符，得到完整的模板内容
        content = TemplateUtil.renderStringByTwoBrace(templateEntity.getContent(), temp);
        map.put("content", content);

        //拼接sendContent
        List<WxMpTemplateData> wxMpTemplateDataList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : entries) {
            wxMpTemplateDataList.add(new WxMpTemplateData(entry.getKey(), entry.getValue().toString()));
        }
        //将wxMpTemplateDataList转为json
        String sendContent = FastJsonUtils.getBeanToJson(wxMpTemplateDataList);
        map.put("sendContent", sendContent);

        return map;
    }

    @Override
    public SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity) {
        return null;
    }
}
