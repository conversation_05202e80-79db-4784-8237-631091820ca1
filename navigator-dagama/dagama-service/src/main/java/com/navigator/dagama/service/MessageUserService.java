package com.navigator.dagama.service;


import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;
import com.navigator.dagama.pogo.model.enums.ReceiverTypeEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;

import java.util.List;

/**
 * <AUTHOR> lyl
 * @Date: 2021/3/17 17:21
 */
public interface MessageUserService {
    /**
     * 获得接受者的联系方式
     *
     * @param idList       接收人id集合
     * @param messageType  消息类型 例：短信/邮件
     * @param receiverType 接收人类型 例：个人/角色
     * @param customerId
     * @return 联系方式 UserContactVO集合
     */
    List<ReceiverContactVO> getReceiverMessage(List<String> idList, MessageTypeEnum messageType, ReceiverTypeEnum receiverType, Integer system, Integer customerId);

    List<ReceiverContactVO> getReceiverMessage(List<String> idList, String messageType, String receiverType, Integer system, Integer customerId);

    List<ReceiverContactVO> getReceiverMessage(Integer customerId, String factoryCode, Integer categoryId, Integer salesType);

    List<ReceiverContactVO> getReceiverMessage(Integer cEmployId);
}
