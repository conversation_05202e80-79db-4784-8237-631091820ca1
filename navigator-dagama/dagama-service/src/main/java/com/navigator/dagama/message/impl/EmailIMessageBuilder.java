package com.navigator.dagama.message.impl;

import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.dagama.message.IMessageBuilder;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: wang tao
 * @Date: 2021/3/18
 * @Time: 14:09
 * @Desception: 邮件模板信息构建
 */
@Component("bemail")
public class EmailIMessageBuilder implements IMessageBuilder {

    /**
     * 构建邮件消息
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity) {

        return TemplateRenderUtil.assemblyMessageInfo(templateEntity.getTitle(), templateEntity.getContent(), dataMap, templateEntity.getContentType());
        /*String content;
        Map<String, Object> map = new HashMap<>();*/
        //邮件消息需要拼接标题
       /*content = "你好"+dataMap.get("first");
        map.put("title",dataMap.get("title"));
        map.put("content", content);
        map.put("sendContent", content);*/
        //return map;
    }

    @Override
    public SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity) {
        SendMessageDTO sendMessageDTO = new SendMessageDTO();

        try {
            Map<String, Object> mapInfo = TemplateRenderUtil.assemblyMessageInfo(templateEntity.getTitle(), templateEntity.getContent(), messageInfoDTO.getDataMap(), templateEntity.getContentType());

            String title = mapInfo.get("title").toString();
            String content = mapInfo.get("content").toString();

            sendMessageDTO.setTitle(title)
                    .setContent(content);
        } catch (Exception e) {

        }

        return sendMessageDTO;

    }
}
