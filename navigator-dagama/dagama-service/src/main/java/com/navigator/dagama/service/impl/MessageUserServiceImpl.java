package com.navigator.dagama.service.impl;

import com.navigator.admin.facade.DepartmentFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.DepartmentEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.dagama.config.wechat.WxMpProperties;
import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;
import com.navigator.dagama.pogo.model.enums.ReceiverTypeEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import com.navigator.dagama.service.MessageUserService;
import com.navigator.dagama.wechat.impl.wechat.WeChatService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR> lyl
 * @Date: 2021/3/17 17:21
 */


@Service
@Slf4j
public class MessageUserServiceImpl implements MessageUserService {
    @Resource
    private DepartmentFacade departmentFacade;
    @Resource
    private EmployPermissionFacade employPermissionFacade;
    //@Resource
    //DbuUserDetailDao dbuUserDetailDao;
    @Resource
    private RoleFacade roleFacade;
    @Resource
    EmployFacade employFacade;
    //@Resource
    //DbuUserTagDao dbuUserTagDao;
    @Resource
    WeChatService weChatService;
    @Resource
    WxMpService wxMpService;
    @Resource
    WxMpProperties wxMpProperties;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private CustomerFacade customerFacade;


    private static final Integer SOURCE = 1;
    private static final Integer IS_SUBSCRIBE = 1;

    @Override
    public List<ReceiverContactVO> getReceiverMessage(List<String> idList, String messageType, String receiverType, Integer system, Integer customerId) {
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByValue(messageType);
        ReceiverTypeEnum receiverTypeEnum = ReceiverTypeEnum.getByValue(receiverType);

        return getReceiverMessage(idList, messageTypeEnum, receiverTypeEnum, system, customerId);
    }

    @Override
    public List<ReceiverContactVO> getReceiverMessage(List<String> idStrList, MessageTypeEnum messageType, ReceiverTypeEnum receiverType, Integer system, Integer customerId) {
        log.info("getReceiverMessage start {}", receiverType);
        if (CollectionUtils.isEmpty(idStrList)) {
            return Collections.EMPTY_LIST;
        }
        List<Integer> idList = new ArrayList<>();
        //如果接收人是微信标签就不转换
        if (!ReceiverTypeEnum.WECHAT_TAG.getValue().equals(receiverType.getValue())) {
            idList = idStrList.stream().map(Integer::parseInt).collect(Collectors.toList());
        }
        List<ReceiverContactVO> receiverContactVOList = new ArrayList<>();

        switch (receiverType) {
            //单人或多人
            case PERSONAL:
                receiverContactVOList = this.personalReceive(messageType, idList, system);
                break;
            //单个角色或多个角色
            case ROLE:
                receiverContactVOList = roleReceive(messageType, idList);
                break;
            //单个角色或多个角色
            case CUSTOMER_ROLE:
                receiverContactVOList = customerRoleReceive(messageType, idList, customerId);
                break;
            //部门领导人
            case DEPARTMENT_LEADER:
                receiverContactVOList = this.departmentLeaderReceive(messageType, idList);
                break;
            //部门
            case DEPARTMENT:
                receiverContactVOList = this.departmentReceive(messageType, idList);
                break;
            //合同
            case CONTRACT:
                receiverContactVOList = this.contractReceive(messageType, idList);
                break;
            //邮件组
            case EMAIL_GROUP:
                receiverContactVOList = this.emailGroupReceive(messageType, idList);
                break;
            //标签
            case TAG:
                receiverContactVOList = this.tagReceive(messageType, idList);
                break;
            //微信标签
            case WECHAT_TAG:
                receiverContactVOList = this.wechatTagReceive(idStrList);
                break;
            default:
                break;
        }
        return receiverContactVOList;
    }

    @Override
    public List<ReceiverContactVO> getReceiverMessage(Integer customerId, String factoryCode, Integer categoryId, Integer salesType) {
        List<ReceiverContactVO> receiverList = new ArrayList<>();

        CustomerAllMessageDTO customerInfo = new CustomerAllMessageDTO();

        customerInfo.setCustomerId(customerId);

        customerInfo.setSalesType(salesType);
        //发送邮件未区分品类修改
        customerInfo.setCategory2(String.valueOf(categoryId));
        customerInfo.setFactoryCode(factoryCode);
        CustomerDTO customerDTO = customerFacade.queryCustomerContactAllMessage(customerInfo);
        if (null != customerDTO) {
            List<ContactEntity> contactEntityList = customerDTO.getContactDTO();
            //传递所有的客户信息
            for (ContactEntity contactEntity : contactEntityList) {
                ReceiverContactVO receiverItem = new ReceiverContactVO();
                String email = contactEntity.getEmail();

                /*//非生产环境要过滤
                if (!SpringContextUtil.isProdEnv()) {
                    email = StringUtil.filterCustomerEmail(email);
                }*/

                receiverItem.setEmail(email);
                receiverList.add(receiverItem);
            }
        }

        return receiverList;
    }

    @Override
    public List<ReceiverContactVO> getReceiverMessage(Integer cEmployId) {
        CEmployEntity employ = cEmployFacade.getEmployById(cEmployId);
        List<ReceiverContactVO> receiverList = new ArrayList<>();
        ReceiverContactVO contactVO = new ReceiverContactVO();
        contactVO.setEmail(employ != null ? employ.getEmail() : "");
        receiverList.add(contactVO);
        return receiverList;
    }

    /**
     * 微信标签
     */


    private List<ReceiverContactVO> wechatTagReceive(List<String> tagNameList) {
        final List<WxMpProperties.MpConfig> configs = wxMpProperties.getConfigs();
        List<ReceiverContactVO> receiverVOList = new ArrayList<>();
        for (String tagName : tagNameList) {
            List<String> openIdList = weChatService.getTagListUser(wxMpService.switchoverTo(configs.get(0).getAppId()), tagName);
            if (CollectionUtils.isEmpty(openIdList)) {
                continue;
            }
            openIdList.forEach(openId -> {
                ReceiverContactVO receiverVO = new ReceiverContactVO().setOpenId(openId);
                receiverVOList.add(receiverVO);
            });
        }
        return receiverVOList.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 个人/多人接收 userId (改)
     */


    private List<ReceiverContactVO> personalReceive(MessageTypeEnum messageType, List<Integer> idList, Integer system) {
        log.info("personalReceive start:{},{}", messageType, idList.toString());
        //根据用户id查出用户数据
        List<EmployEntity> receiverUserList = new ArrayList<>();
        if (null != system && SystemEnum.COLUMBUS.getValue() == system) {
            String idStr = "";
            if (null != idList)
                idStr = idList.toString();
            log.info("personalReceive idList:{}", idStr);
            List<CEmployEntity> cEmployEntities = new ArrayList<>();
            cEmployEntities = cEmployFacade.getEmployByEmployIds(idList);
            /*CEmployEntity c=new CEmployEntity();
            c.setId(200003)
                    .setEmail("<EMAIL>")
                    .setName("NEO0")
                    .setRealName("NZJ0")
                    .setPhone("15900697179");
            cEmployEntities.add(c);*/


            if (null != cEmployEntities) {
                log.info("personalReceive getEmploy:{}", cEmployEntities.toString());

                receiverUserList = BeanConvertUtils.convert2List(EmployEntity.class, cEmployEntities);

                if (null != receiverUserList) {
                    log.info("personalReceive getEmploy:{}", receiverUserList.toString());
                }

            }
        } else {
            receiverUserList = employFacade.getEmployByEmployIds(idList);
            /*EmployEntity c2=new EmployEntity();
            c2.setId(200003)
                    .setEmail("<EMAIL>")
                    .setName("NEO0")
                    .setRealName("NZJ0")
                    .setPhone("15900697179");
            receiverUserList.add(c2);*/
        }
        //获取接收人的信息
        return acquireReceiverInfoByMessageType(receiverUserList, messageType);
    }


    /**
     * 单个角色/多个角色接收 roleId关联 (改)
     *
     * @param messageType
     * @param roleIdList
     * @return
     */
    private List<ReceiverContactVO> roleReceive(MessageTypeEnum messageType, List<Integer> roleIdList) {
        List<EmployEntity> receiverUserList = employFacade.queryEmployByRoleIds(roleIdList);
        return acquireReceiverInfoByMessageType(receiverUserList, messageType);
    }

    /**
     * 查询客户角色
     *
     * @param messageType
     * @param roleIdList
     * @return
     */
    private List<ReceiverContactVO> customerRoleReceive(MessageTypeEnum messageType, List<Integer> roleIdList, Integer customerId) {
        List<CEmployEntity> receiverUserList = cEmployFacade.getEmployRolesByCustomerId(roleIdList, customerId);
        return customerAcquireReceiverInfoByMessageType(receiverUserList, messageType);
    }

    private List<ReceiverContactVO> roleReceiverOnlyId(List<Integer> roleIdList) {
        List<ReceiverContactVO> receiverContactVOList = new ArrayList<>();
        //根据角色id查询
        Result result = employPermissionFacade.getEmployRoleListByRoleIds(roleIdList);
        if (null != result && result.isSuccess() && null != result.getData()) {
            List<EmployRoleEntity> list = (List<EmployRoleEntity>) result.getData();
            for (EmployRoleEntity employRoleEntity : list) {
                ReceiverContactVO receiverContactVO = new ReceiverContactVO();
                receiverContactVO.setId(employRoleEntity.getEmployId());
                receiverContactVOList.add(receiverContactVO);
            }
        }
        return receiverContactVOList;
    }


    /**
     * 部门领导人接收 deptId关联 (改)
     */


    private List<ReceiverContactVO> departmentLeaderReceive(MessageTypeEnum messageType, List<Integer> deptIdList) {
        //根据部门id查询
        List<DepartmentEntity> deptEntityList = departmentFacade.getDepartmentLeaderId(deptIdList);
        if (CollectionUtils.isEmpty(deptEntityList)) {
            return Collections.EMPTY_LIST;
        }
        //将部门领导人id存入集合
        List<Integer> userIdList = deptEntityList.stream().map(DepartmentEntity::getLeaderId).collect(Collectors.toList());
        List<EmployEntity> receiverUserList = employFacade.getEmployByEmployIds(userIdList);
        return acquireReceiverInfoByMessageType(receiverUserList, messageType);
    }


    /**
     * 部门接收 deptId关联 (改)
     */


    private List<ReceiverContactVO> departmentReceive(MessageTypeEnum messageType, List<Integer> deptIdList) {
        //根据部门id集合查询用户
        /*List<EmployEntity> receiverUserList = employFacade.queryUserByDeptIdList(deptIdList);
        if (CollectionUtils.isEmpty(receiverUserList)){
            return Collections.EMPTY_LIST;
        }
        //将部门员工id存入集合
        List<Integer> userIdList = receiverUserList.stream().map(EmployEntity::getId).collect(Collectors.toList());
        return acquireReceiverInfoByMessageType(userIdList,receiverUserList, messageType);*/
        return null;
    }


    /**
     * 合同接收
     */


    private List<ReceiverContactVO> contractReceive(MessageTypeEnum messageType, List<Integer> idList) {
        return null;
    }


    /**
     * 邮件组接收
     */


    private List<ReceiverContactVO> emailGroupReceive(MessageTypeEnum messageType, List<Integer> idList) {
        //发送邮件
//        if (MessageTypeEnum.EMAIL.getValue().equals(messageType.getValue())) {
//        } else {
//        }
        return null;
    }


    /**
     * 单个标签/多个标签接收 tagId关联
     */


    private List<ReceiverContactVO> tagReceive(MessageTypeEnum messageType, List<Integer> tagIdList) {
        //根据tagId查询中间表
        /*List<DbuUserTagEntity> dbuUserTagEntityList = dbuUserTagDao.queryUserTagByTagIdList(tagIdList);
        if (CollectionUtils.isEmpty(dbuUserTagEntityList)) {
            return Collections.EMPTY_LIST;
        }
        //将查到的userId存入集合
        List<Integer> userIdList = dbuUserTagEntityList.stream().map(DbuUserTagEntity::getUserId).collect(Collectors.toList());
        List<EmployEntity> receiverUserList = dbuUserDao.getUserByIdList(userIdList);
        return acquireReceiverInfoByMessageType(userIdList, receiverUserList, messageType);*/
        return null;
    }

    /**
     * 获取接收人的信息
     *
     * @param receiverUserList
     * @return
     */
    private List<ReceiverContactVO> acquireReceiverInfoByMessageType(List<EmployEntity> receiverUserList, MessageTypeEnum messageType) {
        if (CollectionUtils.isEmpty(receiverUserList)) {
            return Collections.EMPTY_LIST;
        }
        List<ReceiverContactVO> receiverContactVOList = new ArrayList<>();
        log.info("acquireReceiverInfoByMessageType messageType:{}", messageType);
        //短信 邮件 站内信 企业微信
        if (Arrays.asList(MessageTypeEnum.SMS.getValue(), MessageTypeEnum.EMAIL.getValue(),
                MessageTypeEnum.IN_MAIL.getValue(), MessageTypeEnum.COMPANY_WECHAT.getValue())
                .contains(messageType.getValue())) {
            for (EmployEntity employEntity : receiverUserList) {
                ReceiverContactVO receiverContactVO = new ReceiverContactVO();
                receiverContactVO.setId(employEntity.getId())
                        .setName(employEntity.getRealName())
                        .setUserName(employEntity.getName())
                        .setEmail(employEntity.getEmail())
                        //.setCpUserId(employEntity.getCpUserId())
                        .setPhone(employEntity.getPhone());
                receiverContactVOList.add(receiverContactVO);
            }
        } //微信
        /*else if (MessageTypeEnum.WECHAT.getName().equals(messageType.getName())) {
            //根据userIdList查询用户详情表
            List<DbuUserDetailEntity> receiverUserDetailList = dbuUserDetailDao.queryUserDetailByUserIdList(userIdList);
            if (CollectionUtils.isEmpty(receiverUserDetailList)) {
                return Collections.EMPTY_LIST;
            }
            for (DbuUserDetailEntity dbuUserDetailEntity : receiverUserDetailList) {
                    //得到微信号openId
                ReceiverContactVO receiverContactVO = new ReceiverContactVO();
                receiverContactVO.setId(dbuUserDetailEntity.getUserId())
                            .setOpenId(dbuUserDetailEntity.getOpenId());
                    receiverContactVOList.add(receiverContactVO);
            }
        }*/
        //企业微信
        /*else if (MessageTypeEnum.WECHAT_UNION.getName().equals(messageType.getName())){
            //根据userIdList查询用户详情表
            List<DbuUserDetailEntity> receiverUserDetailList = dbuUserDetailDao.queryUserDetailByUserIdList(userIdList);
            if (CollectionUtils.isEmpty(receiverUserDetailList)) {
                return Collections.EMPTY_LIST;
            }
            for (DbuUserDetailEntity dbuUserDetailEntity : receiverUserDetailList) {
                //(Source 1 公众号 2 app 3 小程序) && (isSubscribe 0 未关注 1 已关注)
                //该条数据是公众号并且已关注
                if (SOURCE.equals(dbuUserDetailEntity.getSource()) && IS_SUBSCRIBE.equals(dbuUserDetailEntity.getIsSubscribe())) {
                    //得到公众号id
                    ReceiverContactVO receiverContactVO = new ReceiverContactVO();
                    receiverContactVO.setId(dbuUserDetailEntity.getUserId())
                            .setUnionId(dbuUserDetailEntity.getAppId());
                    receiverContactVOList.add(receiverContactVO);
                }
            }
        }*/
        return receiverContactVOList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取客户接收人信息
     *
     * @param receiverUserList
     * @param messageType
     * @return
     */
    private List<ReceiverContactVO> customerAcquireReceiverInfoByMessageType(List<CEmployEntity> receiverUserList, MessageTypeEnum messageType) {
        if (CollectionUtils.isEmpty(receiverUserList)) {
            return Collections.EMPTY_LIST;
        }
        List<ReceiverContactVO> receiverContactVOList = new ArrayList<>();
        log.info("acquireReceiverInfoByMessageType messageType:{}", messageType);
        //短信 邮件 站内信 企业微信
        if (Arrays.asList(MessageTypeEnum.SMS.getValue(), MessageTypeEnum.EMAIL.getValue(),
                MessageTypeEnum.IN_MAIL.getValue(), MessageTypeEnum.COMPANY_WECHAT.getValue())
                .contains(messageType.getValue())) {
            for (CEmployEntity employEntity : receiverUserList) {
                ReceiverContactVO receiverContactVO = new ReceiverContactVO();
                receiverContactVO.setId(employEntity.getId())
                        .setName(employEntity.getRealName())
                        .setUserName(employEntity.getName())
                        .setEmail(employEntity.getEmail())
                        //.setCpUserId(employEntity.getCpUserId())
                        .setPhone(employEntity.getPhone());
                receiverContactVOList.add(receiverContactVO);
            }
        }
        return receiverContactVOList.stream().distinct().collect(Collectors.toList());
    }

}

