package com.navigator.dagama.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.dagama.config.websokect.WebSocketServer;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.dao.DbmSendTaskDao;
import com.navigator.dagama.message.MessageBuildStrategyContext;
import com.navigator.dagama.message.MessageSendStrategyContext;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.service.SendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18 10:11
 */
@Slf4j
@Service
public class SendTaskServiceImpl implements SendTaskService {
    @Autowired
    DbmSendTaskDao sendTaskDao;
    @Autowired
    DbmBusinessTemplateDao businessTemplateDao;
    @Resource
    private MessageSendStrategyContext messageSender;
    @Resource
    MessageBuildStrategyContext messageBuilder;
    /*@Value("${spring.profiles.active}")
    private String profileActive;*/

    @Override
    public void save(DbmSendTaskEntity dbmSendTaskEntity) {
        sendTaskDao.save(dbmSendTaskEntity);
    }

    @Override
    public Result<String> sendMessage(MessageInfoDTO messageInfoDTO) {
        StringBuilder sbSendReult = new StringBuilder();
        Result r = Result.success();
        // 将业务模板表信息取出
        List<BusinessTemplateDTO> businessTemplateDTOS = businessTemplateDao.findTempByCode(messageInfoDTO.getBusinessCode());
        // 构建任务信息
        for (BusinessTemplateDTO businessTemplateDTO : businessTemplateDTOS) {

            String messageType = businessTemplateDTO.getMessageType();

            // 调用接口得到拼装好的消息内容
            SendMessageDTO sendMessageDTO = messageBuilder.buildMessageContent(messageType, messageInfoDTO, businessTemplateDTO);

            // 记录task任务
            DbmSendTaskEntity sendTaskEntity = recordTask(sendMessageDTO, businessTemplateDTO);

            //发送消息
            String sendResult = messageSender.sendMessage(sendMessageDTO, businessTemplateDTO, sendTaskEntity);

            sbSendReult.append(sendResult).append(";");
        }
        r.setMessage(sbSendReult.toString());

        return r;
    }

    @Override
    public Result<String> sendMessage(DbmSendTaskEntity sendTaskEntity) {
        Result rtn = Result.success();

        String sendResult = messageSender.sendMessage(sendTaskEntity);

        rtn.setMessage(sendResult);

        return rtn;
    }

    @Override
    public Result<String> reSendMessage(Integer sendTaskId) {

        DbmSendTaskEntity sendTaskEntity = new DbmSendTaskEntity();

        sendTaskEntity = sendTaskDao.getById(sendTaskId);

        return sendMessage(sendTaskEntity);

    }

    @Override
    public boolean isSendSuccess(String bizId) {
//        QueryWrapper<DbmSendTaskEntity> sendTaskWrapper = new QueryWrapper<>();
//        sendTaskWrapper.eq(DbmSendTaskEntity::getReferId,String.valueOf(bizId));
//        sendTaskDao.lambdaQuery()
        return false;
    }


    @Override
    public List<DbmSendTaskEntity> querySendTaskEntity(QueryDTO<DbmSendTaskEntity> queryDTO) {
        Result result = sendTaskDao.querySendTask(queryDTO);
        List<DbmSendTaskEntity> sendTaskEntityList = (List<DbmSendTaskEntity>) result.getData();

        return sendTaskEntityList;
    }

    @Override
    public void pushWebsocketMsg(Integer userId, Object object) {
        try {
            WebSocketServer.broadCastInfo(String.valueOf(userId), object);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("websocket推送失败：{}", JSONObject.toJSONString(e));
        }
    }

    @Override
    public void pushWebsocketInMailMsg(WebsocketInMailDTO websocketInMailDTO) {
        try {
            WebSocketServer.broadCastInfo(
                    String.valueOf(websocketInMailDTO.getUserId()),
                    websocketInMailDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("websocket推送失败：{}", JSONObject.toJSONString(e));
        }
    }

    /**
     * 记录task信息
     *
     * @param sendMessageDTO
     * @param businessTemplateDTO
     * @return
     */
    private DbmSendTaskEntity recordTask(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO) {
        DbmSendTaskEntity sendTaskEntity = new DbmSendTaskEntity();

        //String prefix = getPrefix();

        sendTaskEntity.setTitle(sendMessageDTO.getTitle())
                .setContent(sendMessageDTO.getContent())
                .setStatus(MessageSendStatusEnum.UNSEND.name())
                .setReceiver(businessTemplateDTO.getReceiver())
                .setReceiverType(businessTemplateDTO.getReceiverType())
                .setBusinessTemplateId(businessTemplateDTO.getBusinessTemplateId())
                .setSender(businessTemplateDTO.getSender())
                .setCopyer(businessTemplateDTO.getCopyer())
                .setCopyerType(businessTemplateDTO.getCopyerType())
                .setMessageType(sendMessageDTO.getMessageType())
                .setCreatedAt(DateTime.now().toTimestamp())
                .setReferId(sendMessageDTO.getReferBizId())
                .setBusinessCode(sendMessageDTO.getBusinessCode())
                .setSystem(sendMessageDTO.getSystem())
                .setCategoryId(sendMessageDTO.getCategoryId())
                .setMenuCode(sendMessageDTO.getMenuCode())
                .setBusinessSceneCode(sendMessageDTO.getBusinessScene())
        ;
        sendTaskDao.save(sendTaskEntity);
        return sendTaskEntity;
    }

    private String getPrefix() {
        if (SpringContextUtil.isProdEnv()) {
            return "";
        } else {
            return "【" + SpringContextUtil.getEnv() + "】";
        }
    }

}
