package com.navigator.dagama.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.dagama.dao.DbmInmailDao;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import com.navigator.dagama.service.DbmInmailService;
import com.navigator.dagama.service.SendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class DbmInmailServiceImpl implements DbmInmailService {

    @Resource
    private DbmInmailDao dbmInmailDao;
    @Resource
    private SendTaskService sendTaskService;

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryInmail(QueryDTO<DbmInmailEntity> queryDTO) {
        Integer receiver = Integer.parseInt(JwtUtils.getCurrentUserId());
        IPage<DbmInmailEntity> inmailEntityIPage = dbmInmailDao.queryInmail(queryDTO, receiver);
        if (!CollectionUtils.isEmpty(inmailEntityIPage.getRecords())) {
            inmailEntityIPage.getRecords().forEach(dbmInmailEntity -> {
                        if (StringUtils.isNotBlank(dbmInmailEntity.getMenuCode())) {
                            String menuUrl = MenuCodeEnum.getByValue(Integer.valueOf(dbmInmailEntity.getMenuCode())).getUrl();
                            dbmInmailEntity.setMenuUrl(menuUrl.replaceAll("\\{category2\\}", dbmInmailEntity.getCategoryId().toString()));
                        }
                    }
            );
        }
        return Result.page(inmailEntityIPage, inmailEntityIPage.getRecords());
    }

    @Override
    public Result queryInmailCount(Integer system) {
        Integer receiver = Integer.parseInt(JwtUtils.getCurrentUserId());

        /*if (10 >= receiver) {
            receiver = null;
        }*/
        return Result.success(dbmInmailDao.queryInmailCount(receiver, system));
    }

    @Override
    public Integer queryEmployInMailCount(Integer receiver) {
        return dbmInmailDao.queryEmployInMailCount(receiver);
    }


    /**
     * 表示是否已读
     *
     * @param id
     * @return
     */
    @Override
    public Result readInmail(List<Integer> id) {

        dbmInmailDao.readInmail(id);

        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        Integer inMailCount = queryEmployInMailCount(userId);

        WebsocketInMailDTO websocketInMailDTO = new WebsocketInMailDTO();
        websocketInMailDTO.setUserId(userId)
                .setInMailCount(inMailCount);

        sendTaskService.pushWebsocketInMailMsg(websocketInMailDTO);

        return Result.success();
    }
}