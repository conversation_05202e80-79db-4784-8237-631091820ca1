package com.navigator.dagama.utils.mail;

import com.navigator.dagama.DagamaApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DagamaApplication.class)
public class MailTest {

    @Resource
    private MailUtilImpl mailUtil;

    @Test
    public void sendEmail(){
        mailUtil.sendHtmlMail("<EMAIL>","你有一份新合同","你有一份新合同需要签署，请尽快处理");
    }
}
