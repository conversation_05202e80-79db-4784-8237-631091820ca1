//package com.navigator.dagama.config.wechat;
//
//import com.navigator.dagama.pogo.model.dto.MailSendConfigDTO;
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Data
//@Component
//@ConfigurationProperties(prefix = "mail-notice")
//public class MailNoticeProperties {
//    private List<MailSendConfigDTO> configs;
//
//
//    public MailSendConfigDTO getFactoryMailConfig(String factoryCode) {
//        for (MailSendConfigDTO fmc : configs) {
//            if (factoryCode.equals(fmc.getFactoryCode())) {
//                return fmc;
//            }
//        }
//        return configs.get(0);
//    }
//
//    public MailSendConfigDTO getFactoryMailConfig(String factoryCode, Integer categoryId) {
//        if (null == categoryId) {
//            categoryId = 0;
//        }
//        for (MailSendConfigDTO fmc : configs) {
//            if (factoryCode.equals(fmc.getFactoryCode()) && fmc.getCategory() == categoryId) {
//                return fmc;
//            }
//        }
//        return configs.get(0);
//    }
//
//    public MailSendConfigDTO getFactoryMailConfigBySender(String sender) {
//        for (MailSendConfigDTO fmc : configs) {
//            if (fmc.getFrom().equals(sender)) {
//                return fmc;
//            }
//        }
//        return configs.get(0);
//    }
//
//}
