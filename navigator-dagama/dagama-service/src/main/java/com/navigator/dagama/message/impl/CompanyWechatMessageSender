package com.navigator.dagama.message.impl;//package com.navigator.dagama.message.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.navigator.common.core.util.HttpClient;
//import com.navigator.dagama.config.wecom.WxCpConfiguration;
//import com.navigator.dagama.message.IMessageSender;
//import com.navigator.dagama.model.dto.AccessTokenDTO;
//import com.navigator.dagama.model.dto.SendMessageDTO;
//import com.navigator.dagama.model.enums.ResultCode;
//import com.navigator.dagama.model.enums.SendStatus;
//import me.chanjar.weixin.common.error.WxErrorException;
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.bean.WxCpAppChatMessage;
//import me.chanjar.weixin.cp.bean.WxCpMessage;
//import me.chanjar.weixin.cp.constant.WxCpConsts;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/3/17 11:06
// */
//@Component("companyWechat")
//public class SendCompanyWechatMessage implements SendMessageStrategy {
//
//    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Resource
//    HttpClient httpClient;
//
//    @Value("${message.companyWechat.corpId}")
//    private String corpId;
//    @Value("${message.companyWechat.secret}")
//    private String corpsecret;
//    @Value("${message.companyWechat.agentId}")
//    private String agentId;
//
//    @Override
//    public String sendMessage(Object o) throws Exception {
//
//        System.out.println("发送企业微信消息" + JSONObject.toJSONString(o));
//
//        if (o instanceof SendMessageDTO) {
//            SendMessageDTO sendMessageDTO = (SendMessageDTO) o;
//            //发送企业微信消息
//            String status = sendTextToUser(sendMessageDTO);
//            return status;
//        }
//        return SendStatus.UNSEND.getMsg();
//    }
//
////    /**
////     * 发送企业微信消息
////     * https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=ACCESS_TOKEN
////     *
////     * @param sendMessageDTO
////     */
////    public String sendCompanyMessages(SendMessageDTO sendMessageDTO) {
////        String resultMemo = "进入发送准备";
////        boolean result = false;
////        try {
////            AccessTokenDTO companyToken = getCompanyToken(corpId, corpsecret);
////            //发送应用消息
////            String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + companyToken.getAccess_token();
////            JSONObject object = new JSONObject();
////            //指定为@all，则向关注该企业应用的全部成员发送
//////            object.put("touser", "@all");
////            object.put("touser", sendMessageDTO.getReceiverContactVO().getCpUserId());
////            //必填，此时固定为：text
////            object.put("msgtype", "text");
////            object.put("safe", 0);
////            //必填,	企业应用的id，整型
////            object.put("agentid", agentId);
////            //发送模板内容
////            Map<String, String> map = new HashMap();
////            map.put("content", sendMessageDTO.getContent());
////            object.put("text", map);
////            String dataFormat = object.toJSONString();
////            HttpHeaders headers = new HttpHeaders();
////            headers.setContentType(MediaType.APPLICATION_JSON);
////            HttpEntity<String> httpEntity = new HttpEntity<>(dataFormat, headers);
////            String erpAdminTag = httpClient.exchange(url, httpEntity, HttpMethod.POST);
////            logger.info("发送企业微信消息回结果:" + erpAdminTag);
////            JSONObject erpAdminTagObj = JSONObject.parseObject(erpAdminTag);
////            resultMemo = erpAdminTag;
////            if (erpAdminTagObj.getIntValue("errcode") == 0) {
////                // 发送成功
////                return SendStatus.SUCCESS.getMsg();
////            } else {
////                // 发送失败
////                return SendStatus.FAIL.getMsg();
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////            logger.error("发送微信模板消息回结果,错误信息：", e);
////            return SendStatus.FAIL.getMsg();
////        }
////    }
////
////    /**
////     * 获取企业微信token
////     * https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=ID&corpsecret=SECRET
////     */
////    public AccessTokenDTO getCompanyToken(String corpId, String corpsecret) {
////        String exchange = "";
////        try {
////            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpId + "&corpsecret=" + corpsecret;
////            logger.info("获取企业微信accessToken,请求参数：" + url);
////            exchange = httpClient.exchange(url, null, HttpMethod.GET);
////            AccessTokenDTO accessTokenDTO = JSONObject.parseObject(exchange, AccessTokenDTO.class);
////            //errcode	出错返回码，为0表示成功，非0表示调用失败
////            if (ResultCode.SUCCESS.code().intValue() == accessTokenDTO.getErrcode().intValue()) {
////                return accessTokenDTO;
////            }
////            logger.info("获取得到的企业微信accessToken:" + exchange);
////        } catch (Exception e) {
////            logger.info("获取企业微信accessToken,获取提示{},报错:{}.", exchange, e);
////        }
////        return null;
////    }
//
//    private String sendTextToUser(SendMessageDTO sendMessageDTO) {
//        try {
//            WxCpService cpService = WxCpConfiguration.getCpService();
//
//            sendUserMsg(cpService,WxCpMessage.TEXT().content(sendMessageDTO.getContent())
//                    .toUser(sendMessageDTO.getReceiverContactVO().getCpUserId())
//                    .agentId()
//                    .build()
//            );
//            return SendStatus.SUCCESS.getMsg();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("发送微信模板消息回结果,错误信息：", e);
//        }
//        return SendStatus.FAIL.getMsg();
//    }
//
//
//
//
//    /**
//     * 个人消息
//     */
//    private void sendUserMsg(WxCpService cpService, WxCpMessage wxCpMessage) {
//        try {
//            cpService.messageSend(wxCpMessage);
//        } catch (WxErrorException e) {
//            e.printStackTrace();
//        }
//    }
//}
