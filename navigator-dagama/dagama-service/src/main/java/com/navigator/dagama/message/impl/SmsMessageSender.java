package com.navigator.dagama.message.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.qcloudsms.SmsResultBase;
import com.navigator.common.dto.MessageFormDTO;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.MessageUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;
import com.navigator.dagama.pogo.model.enums.ReceiverTypeEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import com.navigator.dagama.utils.UserConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 11:04
 */
@Component("sms")
@Slf4j
public class SmsMessageSender extends AbstractMessageSender {

    @Resource
    MessageUtils messageUtils;

    @Value("${sms.app_id}")
    private Integer appId;
    @Value("${sms.app_key}")
    private String appKey;
    @Value("${sms.sms_sign}")
    private String smsSign;


    @Override
    protected SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {

        //3.1 解析接收人  默认取数据库接收人
        List<String> receiverIdList = StrUtil.isEmpty(businessTemplateDTO.getReceiver()) ?
                sendMessageDTO.getReceivers() : Arrays.asList(businessTemplateDTO.getReceiver().split(";"));
        // 获取接收人信息
        List<ReceiverContactVO> userContactVOS = new ArrayList<>();
        userContactVOS = messageUserService.getReceiverMessage(receiverIdList,
                MessageTypeEnum.getByValue(businessTemplateDTO.getMessageType()),
                ReceiverTypeEnum.getByValue(businessTemplateDTO.getReceiverType()),
                sendMessageDTO.getSystem(),
                sendMessageDTO.getCustomerId());
        if (CommonListUtil.notNullOrEmpty(userContactVOS)) {
            sendMessageDTO.setReceiver(userContactVOS);
            sendTaskEntity.setReceiver(userContactVOS.get(0).getPhone());
            sendTaskEntity.setReceiverType(ReceiverTypeEnum.PERSONAL.getValue());
            dbmSendTaskDao.updateById(sendTaskEntity);
        }

        return sendMessageDTO;
    }

    @Override
    protected String sendTheMessage(DbmSendTaskEntity sendTaskEntity) {
        boolean sendStatus = false;
        String sendResult = sendSMSMessage(sendTaskEntity);

        if (StringUtil.isEmpty(sendResult)) {
            sendStatus = true;
        }

        sendTaskEntity.setStatus(MessageSendStatusEnum.getByStatus(sendStatus).name());
        sendTaskEntity.setMemo(sendTaskEntity.getMemo() + sendResult);
        sendTaskEntity.setTryTimes(sendTaskEntity.getTryTimes() + 1);

        dbmSendTaskDao.updateById(sendTaskEntity);

        return sendResult;
    }

    private String sendSMSMessage(DbmSendTaskEntity sendTaskEntity) {
        //封装
        MessageFormDTO messageForm = new MessageFormDTO();
        //List<ReceiverContactVO> receiverContactVOS = sendTaskEntity.getReceiver();
        if (StringUtil.isEmpty(sendTaskEntity.getReceiver())) {
            return "没有接收人信息;";
        }
        //ReceiverContactVO receiverContactVO=receiverContactVOS.get(0);

        messageForm.setMobiles(sendTaskEntity.getReceiver())
                .setAppId(appId)
                .setAppKey(appKey)
                .setSmsSign(smsSign)
                .setNationCode("86")
                //.setCaptcha(CodeGeneratorUtil.genVerificationCode(4))
                .setCaptcha(sendTaskEntity.getContent())
                //第三方模板
                .setTemplateId(UserConstant.SMS_REGISTER_CODE_TEMPLATE_ID)
                .setParams(sendTaskEntity.getContent().split(";"));
        try {
            //单发短信,
            SmsResultBase smsResultBase = messageUtils.sendMessage(Boolean.TRUE, messageForm);
            //分析结果
            Integer result = JSONObject.parseObject(smsResultBase.toString()).getInteger("result");
            String errmsg = JSONObject.parseObject(smsResultBase.toString()).getString("errmsg");
            if (result == 0 && errmsg.equals("OK")) {
                return "";
            } else {
                return errmsg + ";";
            }
        } catch (Exception e) {
            log.info("发送短信失败" + e.toString());
            return "发送短信异常;";
        }
    }

}
