package com.navigator.dagama.utils.mail;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.io.File;

@Service
@Slf4j
public class MailUtilImpl {

    private final Logger logger = LoggerFactory.getLogger(MailUtilImpl.class);

    @Value("${spring.mail.username}")
    private String from;

    @Value("${spring.mail.valid}")
    private Integer valid;

    /*@Value("${spring.profiles.active}")
    private String profileActive;*/

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private OperationLogFacade operationLogFacade;

    public DbmSendTaskEntity sendHtmlMail(DbmSendTaskEntity sendTaskEntity) {
        boolean sendResult = false;
        StringBuilder sbTraceLog = new StringBuilder();
        String sendMemo = "";

        logger.info("发送HTML邮件开始：signId:{} data:{}", sendTaskEntity.getReferId(), JSON.toJSONString(sendTaskEntity));
        sbTraceLog.append("01发送HTML邮件开始;");
        //operationLogFacade.saveTraceLog(new TraceLogDTO(sendTaskEntity.getReferId(), "MailUtilImpl.sendHtmlMail", JSON.toJSONString(sendTaskEntity)));

        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;

        try {
            sbTraceLog.append("02MimeMessageHelper开始处理;");

            helper = new MimeMessageHelper(message, true);

            helper.setFrom(sendTaskEntity.getSender());
            sbTraceLog.append("03设置发件人;");

            String[] emailList = getEmailList(sendTaskEntity.getReceiver());
            helper.setTo(emailList);
            sbTraceLog.append("04设置收件人;");

            helper.setSubject(sendTaskEntity.getTitle());
            sbTraceLog.append("05设置主题;");

            // 统一使用SpringContextUtil进行判断
//            if (!SpringContextUtil.isProdEnv()) {
//                //delete by Neo for:非直接发送，没必要过滤，即使过滤也应该统一进行
//                //helper.setCc("<EMAIL>");
//            } else {
                if (StringUtil.isNotEmpty(sendTaskEntity.getCopyer())) {
                    String[] scc = getEmailList(sendTaskEntity.getCopyer());
                    helper.setCc(scc);
                }
//            }
            sbTraceLog.append("06设置抄送人;");

            if (StringUtil.isNotEmpty(sendTaskEntity.getReplayTo())) {
                helper.setReplyTo(sendTaskEntity.getReplayTo());
            }
            helper.setText(sendTaskEntity.getContent(), true);//true代表支持html
            sbTraceLog.append("07设置邮件内容;");

            JavaMailSenderImpl ms = (JavaMailSenderImpl) mailSender;
            ms.setUsername(sendTaskEntity.getSender());

            //设置密码
            if (StringUtil.isNotEmpty(sendTaskEntity.getPwd())) {
                ms.setPassword(sendTaskEntity.getPwd());
                //兼容空密码
                if ("null".equals(sendTaskEntity.getPwd())
                        || "empty".equals(sendTaskEntity.getPwd())) {
                    ms.setPassword("");
                }
            }
            sbTraceLog.append("08设置发送邮箱的用户名密码;");


            if (null != valid && valid == 1) {
                ms.send(message);
                logger.info("发送HTML邮件成功;");
                sendMemo = "发送HTML邮件成功;";
                sbTraceLog.append("09发送邮件并成功;");

                sendResult = true;
            } else {
                sendMemo = "邮件发送开关关闭，未发送;";
                logger.info("未打开实际发送开关，不能发送邮件");
                sbTraceLog.append("10未打开实际发送开关，不能发送邮件;");

            }
        } catch (Exception e) {
            sbTraceLog.insert(0, "发送邮件异常:" + e.getMessage());
            sendMemo = "发送邮件异常;";
            System.out.println(e.getMessage());
            operationLogFacade.saveTraceLog(new TraceLogDTO(sendTaskEntity.getReferId(), "MailUtilImpl.sendHtmlMail.Exception", JSON.toJSONString(e)));
            operationLogFacade.recordsimpleredalarm(sendTaskEntity.getId(), "sendHtmlMail", "邮件发送异常");
            logger.error("发送HTML邮件失败：signId:{} ,error:{} ", sendTaskEntity.getReferId(), e.getMessage(), e);
        }

        sbTraceLog.append(JSON.toJSONString(sendTaskEntity));
        operationLogFacade.saveTraceLog(new TraceLogDTO(sendTaskEntity.getReferId(), "MailUtilImpl.sendHtmlMail", sbTraceLog.toString()));

        sendTaskEntity.setMemo(sendTaskEntity.getMemo() + sendMemo);
        sendTaskEntity.setStatus(MessageSendStatusEnum.getByStatus(sendResult).name());
        sendTaskEntity.setTryTimes(sendTaskEntity.getTryTimes() + 1);

        return sendTaskEntity;
    }

    private String[] getEmailList(String emails) {
        emails = emails.replaceAll(" ", "").trim();
        emails = emails.replaceAll("；", ";").trim();
        String[] aEmails = emails.split(";");
        return aEmails;
    }


    public void sendSimpleMail(String[] to, String subject, String content) {

        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);//收信人
        message.setSubject(subject);//主题
        message.setText(content);//内容
        message.setFrom(from);//发信人

        mailSender.send(message);
    }

    public void sendHtmlMail(String to, String subject, String content) {
        String[] tos = to.split(";");
        sendHtmlMail(tos, subject, content);
    }

    public void sendHtmlMail(String[] to, String subject, String content) {

        logger.info("发送HTML邮件开始：{},{},{}", to, subject, content);
        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);//true代表支持html
            mailSender.send(message);
            logger.info("发送HTML邮件成功");
        } catch (Exception e) {
            logger.error("发送HTML邮件失败：", e);
        }
    }

    public void sendAttachmentMail(String to, String subject, String content, String filePath) {

        logger.info("发送带附件邮件开始：{},{},{},{}", to, subject, content, filePath);
        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            FileSystemResource file = new FileSystemResource(new File(filePath));
            String fileName = file.getFilename();
            helper.addAttachment(fileName, file);//添加附件，可多次调用该方法添加多个附件
            mailSender.send(message);
            logger.info("发送带附件邮件成功");
        } catch (Exception e) {
            logger.error("发送带附件邮件失败", e);
        }


    }

    public void sendInlineResourceMail(String to, String subject, String content, String rscPath, String rscId) {

        logger.info("发送带图片邮件开始：{},{},{},{},{}", to, subject, content, rscPath, rscId);
        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            FileSystemResource res = new FileSystemResource(new File(rscPath));
            helper.addInline(rscId, res);//重复使用添加多个图片
            mailSender.send(message);
            logger.info("发送带图片邮件成功");
        } catch (Exception e) {
            logger.error("发送带图片邮件失败", e);
        }
    }

    public void sendBei6(String pwd) {

        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;

        try {
            helper = new MimeMessageHelper(message, true);

            helper.setFrom("<EMAIL>");
            helper.setTo("<EMAIL>");

            helper.setSubject("Bei6邮箱发送测试");
            helper.setCc("<EMAIL>");

            helper.setReplyTo("<EMAIL>");

            if (null == pwd) {
                helper.setText("Bei6邮箱发送测试null", true);//true代表支持html
            } else {
                helper.setText("Bei6邮箱发送测试空", true);//true代表支持html
            }

            JavaMailSenderImpl ms = (JavaMailSenderImpl) mailSender;
            ms.setUsername("<EMAIL>");
            if (null == pwd) {
                ms.setPassword(null);
            } else {
                ms.setPassword("");
            }

            try {
                ms.send(message);
            } catch (MailException e) {
                sendBei6(null);
            }

        } catch (Exception e) {
        }


    }

}
