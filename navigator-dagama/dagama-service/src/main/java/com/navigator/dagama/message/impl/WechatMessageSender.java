package com.navigator.dagama.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.dagama.config.wechat.WxMpProperties;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.wechat.impl.wechat.WxMpTemplateMsgServiceImpl;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 11:05
 */
@Component("wechat")
@AllArgsConstructor
public class WechatMessageSender extends AbstractMessageSender {
    @Resource
    WxMpProperties wxMpProperties;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    WxMpService wxMpService;
    @Resource
    WxMpTemplateMsgServiceImpl wxMpTemplateMsgService;

    @Override
    protected SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {
        return sendMessageDTO;
    }

    @Override
    public String sendTheMessage(DbmSendTaskEntity sendTaskEntity) {
        System.err.println("发送微信公众号" + JSONObject.toJSONString(sendTaskEntity));
        //TODO 待开发
        String status = sendWechatMessage(new SendMessageDTO());
        return status;

    }

    private String sendWechatMessage(SendMessageDTO sendMessageDTO) {
        try {
            List<WxMpTemplateData> templateDataList = FastJsonUtils.getJsonToList(sendMessageDTO.getSendContent(), WxMpTemplateData.class);
            sendTemplentMessage(WxMpTemplateMessage.builder()
                    .toUser("sendMessageDTO.getReceiverContactVO().getOpenId()")
                    .templateId(sendMessageDTO.getTemplateId())
                    .data(templateDataList)
                    .build());
            return MessageSendStatusEnum.SUCCESS.getMsg();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("发送微信模板消息回结果,错误信息：", e.toString());
            return MessageSendStatusEnum.FAIL.getMsg();
        }
    }

    private void sendTemplentMessage(WxMpTemplateMessage wxMpTemplateMessage) {

        try {
            final List<WxMpProperties.MpConfig> configs = wxMpProperties.getConfigs();
            wxMpService.switchoverTo(configs.get(0).getAppId()).getTemplateMsgService().sendTemplateMsg(wxMpTemplateMessage);
        } catch (Exception e) {
            logger.info("发送微信消息模板失败" + e.toString());
            e.printStackTrace();
        }
    }

}
