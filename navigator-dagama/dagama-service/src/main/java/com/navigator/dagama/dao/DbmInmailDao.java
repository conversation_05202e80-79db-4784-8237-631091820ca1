package com.navigator.dagama.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.SystemUserEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.mapper.DbmInmailMapper;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageReadStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.pogo.model.enums.MessageTypeEnum;

import java.util.List;

@Dao
public class DbmInmailDao extends BaseDaoImpl<DbmInmailMapper, DbmInmailEntity> {

    public void saveMessageEntity(DbmSendTaskEntity sendTaskEntity) {

        DbmInmailEntity dbmInmailEntity = new DbmInmailEntity();
        dbmInmailEntity.setBusinessTemplateId(sendTaskEntity.getBusinessTemplateId());

        dbmInmailEntity.setBusinessScene(BusinessSceneEnum.getByDesc(sendTaskEntity.getBusinessSceneCode()).getValue());

        dbmInmailEntity.setTitle(sendTaskEntity.getTitle());
        dbmInmailEntity.setCategoryId(sendTaskEntity.getCategoryId());
        dbmInmailEntity.setMenuCode(sendTaskEntity.getMenuCode());
        dbmInmailEntity.setContent(sendTaskEntity.getContent());
        dbmInmailEntity.setStatus(MessageSendStatusEnum.SUCCESS.name());
        dbmInmailEntity.setIsRead(MessageReadStatusEnum.UNREAD.getValue());
        // 所属系统
        Integer system = sendTaskEntity.getSystem();
        dbmInmailEntity.setSystem(system);
        if (SystemEnum.getByValue(system) == SystemEnum.MAGELLAN) {
            dbmInmailEntity.setSender(SystemUserEnum.SYSTEM.getUserId());
            dbmInmailEntity.setSenderName(SystemUserEnum.SYSTEM.getUserName());
        } else if (SystemEnum.getByValue(system) == SystemEnum.COLUMBUS) {
            dbmInmailEntity.setSender(SystemUserEnum.SYSTEM_COLUMBUS.getUserId());
            dbmInmailEntity.setSenderName(SystemUserEnum.SYSTEM_COLUMBUS.getUserName());
        }
        //消息类型=站内信
        dbmInmailEntity.setMessageType(MessageTypeEnum.IN_MAIL.getValue());

        String receivers = sendTaskEntity.getReceiver();
        List<String> receiverList = StringUtil.split(receivers);
        for (String receiverId : receiverList) {
            dbmInmailEntity.setReceiver(receiverId);
            save(dbmInmailEntity);
        }

    }

    /**
     * 站内信列表
     *
     * @param queryDTO
     * @return
     */
    public IPage<DbmInmailEntity> queryInmail(QueryDTO<DbmInmailEntity> queryDTO, Integer receiver) {
        ObjectMapper mapper = new ObjectMapper();
        DbmInmailEntity dbmInmailDTO = mapper.convertValue(queryDTO.getCondition(), DbmInmailEntity.class);

        Integer system = null == dbmInmailDTO.getSystem() ? SystemEnum.MAGELLAN.getValue() : dbmInmailDTO.getSystem();


        Page<DbmInmailEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        return this.baseMapper.selectPage(page, Wrappers.<DbmInmailEntity>lambdaQuery()
                .eq(StrUtil.isNotEmpty(dbmInmailDTO.getStatus()), DbmInmailEntity::getStatus, dbmInmailDTO.getStatus())
                .eq(null != receiver, DbmInmailEntity::getReceiver, receiver)
                .eq(null != dbmInmailDTO.getBusinessScene(), DbmInmailEntity::getBusinessScene, dbmInmailDTO.getBusinessScene())
                .eq(null != dbmInmailDTO.getIsRead(), DbmInmailEntity::getIsRead, dbmInmailDTO.getIsRead())
                .eq(DbmInmailEntity::getSystem, system)
                .orderByDesc(DbmInmailEntity::getCreatedAt));
        //List<InmailVO> inmailVOS = BeanConvertUtils.convert2List(InmailVO.class, iPage.getRecords());
    }

    public Integer queryInmailCount(Integer receiver, Integer system) {

        return this.baseMapper.selectCount(Wrappers.<DbmInmailEntity>lambdaQuery()
                .eq(DbmInmailEntity::getIsRead, MessageReadStatusEnum.UNREAD.getValue())
                .eq(null != receiver, DbmInmailEntity::getReceiver, receiver)
                .eq(null != receiver, DbmInmailEntity::getSystem, system));
    }

    public Integer queryEmployInMailCount(Integer receiver) {

        return this.baseMapper.selectCount(Wrappers.<DbmInmailEntity>lambdaQuery()
                .eq(DbmInmailEntity::getIsRead, MessageReadStatusEnum.UNREAD.getValue())
                .eq(null != receiver, DbmInmailEntity::getReceiver, receiver));
    }


    /**
     * 表示是否已读
     *
     * @param id
     * @return
     */
    public Result readInmail(List<Integer> id) {
        int updateNum = 0;
        for (Integer read : id) {
            DbmInmailEntity dbmInmailEntity = this.baseMapper.selectById(read);
            dbmInmailEntity.setIsRead(MessageReadStatusEnum.READED.getValue());
            updateNum += this.baseMapper.updateById(dbmInmailEntity);
        }
        return Result.success(updateNum);
    }
}
