package com.navigator.dagama.message;

import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18 13:42
 */
@Component
public class MessageSendStrategyContext {
    private final Map<String, IMessageSender> sendMessageStrategyMap = new ConcurrentHashMap<String, IMessageSender>();

    @Autowired
    public void strategyInterface(Map<String, IMessageSender> strategyMapInfo) {
        this.sendMessageStrategyMap.clear();
        strategyMapInfo.forEach((k, v) -> this.sendMessageStrategyMap.put(k, v));
    }

    private IMessageSender getStrategy(String messageType) {
        return messageType != null ? sendMessageStrategyMap.get(messageType) : null;
    }

    /*@Async*/
    public String sendMessage(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {
        if (null != sendMessageDTO.getMessageType()) {
            IMessageSender sender = this.getStrategy(sendMessageDTO.getMessageType());
            return sender.sendMessage(sendMessageDTO, businessTemplateDTO, sendTaskEntity);
        }
        return MessageSendStatusEnum.UNSEND.name();
    }

    /*@Async*/
    public String sendMessage(DbmSendTaskEntity sendTaskEntity) {
        if (null != sendTaskEntity.getMessageType()) {
            IMessageSender sender = this.getStrategy(sendTaskEntity.getMessageType());
            return sender.sendMessage(sendTaskEntity);
        }
        return MessageSendStatusEnum.UNSEND.name();
    }
}
