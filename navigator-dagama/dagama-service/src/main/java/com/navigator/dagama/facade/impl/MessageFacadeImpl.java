package com.navigator.dagama.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.batch.CustomerSignNoticeResendScheduler;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.service.DbmInmailService;
import com.navigator.dagama.service.DbmMailNoticeConfigService;
import com.navigator.dagama.service.DbmSendTaskService;
import com.navigator.dagama.service.SendTaskService;
import com.navigator.dagama.service.impl.DbmMailNoticeConfigServiceImpl;
import com.navigator.dagama.utils.mail.MailUtilImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;
import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_FH;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/5 14:51
 */
@RestController
public class MessageFacadeImpl implements MessageFacade {
    @Resource
    private SendTaskService sendTaskService;
    @Resource
    private DbmInmailService dbmInmailService;
    @Resource
    private DbmSendTaskService dbmSendTaskService;
    @Resource
    private MailUtilImpl mailUtil;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private DbmMailNoticeConfigService mailNoticeConfigService;
    @Value("${lkgNotice.email}")
    private String lkgNotice;

    @Resource
    private CustomerSignNoticeResendScheduler customerSignNoticeResendScheduler;

    /**
     * 拼接好的信息，直接发送
     *
     * @param messageInfoDTO
     */
    @Override
    public Result<String> sendMessage(MessageInfoDTO messageInfoDTO) {

        //记录日志
        Result<String> stringResult = sendTaskService.sendMessage(messageInfoDTO);
        operationLogFacade.saveTraceLog(new TraceLogDTO(messageInfoDTO.getReferBizId(), "MessageFacade.sendMessage", JSON.toJSONString(messageInfoDTO)));
        return stringResult;
    }

    @Override
    public Result<String> reSendMessage(Integer sendTaskId) {
        return sendTaskService.reSendMessage(sendTaskId);
    }

    @Override
    public Result<String> autoReSendCustomerSignNotice() {
        customerSignNoticeResendScheduler.execute();
        return Result.success();
    }

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result queryInmail(QueryDTO<DbmInmailEntity> queryDTO) {
        return dbmInmailService.queryInmail(queryDTO);
    }

    @Override
    public Result queryInmailCount(Integer system) {
        return dbmInmailService.queryInmailCount(system);
    }

    /**
     * 表示是否已读
     *
     * @param id
     * @return
     */
    @Override
    public Result readInmail(List<Integer> id) {
        return dbmInmailService.readInmail(id);
    }


    @Override
    public Integer queryEmployInMailCount(Integer receiver) {
        return dbmInmailService.queryEmployInMailCount(receiver);
    }


    /**
     * 发送信息列表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result querySendTas(QueryDTO<DbmSendTaskEntity> queryDTO) {
        return dbmSendTaskService.querySendTas(queryDTO);
    }


    @Override
    public Result sendEmailInfo(String email, String title, String content) {
        operationLogFacade.saveTraceLog(new TraceLogDTO(email, "MessageFacade.sendEmailInfo", JSON.toJSONString(content)));

        String[] emailTo;

        if (email.contains(SPLIT_SIGN_DH)) {
            emailTo = email.split(SPLIT_SIGN_DH);
        } else {
            emailTo = email.split(SPLIT_SIGN_FH);
        }

        mailUtil.sendHtmlMail(emailTo, title, content);
        return Result.success();
    }

    @Override
    public Result pushWebsocketMsg(Integer userId, String content) {
        sendTaskService.pushWebsocketMsg(userId, content);
        return Result.success(userId + "," + content);
    }

    @Override
    public Result pushWebsocketInMailMsg(WebsocketInMailDTO websocketInMailDTO) {
        sendTaskService.pushWebsocketInMailMsg(websocketInMailDTO);
        return Result.success(websocketInMailDTO);
    }


    @Override
    public Result sendRedAlarmEmail(String title) {
        String[] lkgNoticeList;
        if (lkgNotice.contains(SPLIT_SIGN_DH)) {
            lkgNoticeList = lkgNotice.split(SPLIT_SIGN_DH);
        } else {
            lkgNoticeList = lkgNotice.split(SPLIT_SIGN_FH);
        }
        mailUtil.sendHtmlMail(lkgNoticeList, title, "请尽快处理");
        return Result.success();
    }

    @Override
    public boolean isSendSuccess(String bizId) {
        return sendTaskService.isSendSuccess(bizId);
    }

    @Override
    public Result sentBei6() {
        mailUtil.sendBei6("");
        return Result.success();
    }

    @Override
    public Result importMailNoticeConfig(MultipartFile uploadFile) {
        return mailNoticeConfigService.importMailNoticeConfig(uploadFile);
    }

    /*@Override
    public Result queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO) {
        return dbmBusinessTemplateService.queryBusinessTemplate(queryDTO);
    }*/
}
