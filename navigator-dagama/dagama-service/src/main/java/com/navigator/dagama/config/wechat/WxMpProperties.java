package com.navigator.dagama.config.wechat;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/29 16:32
 */
@Data
@ConfigurationProperties(prefix = "message.wechat")
public class WxMpProperties {
    /**
     * 多个公众号配置信息
     */
    private List<MpConfig> configs;
    @Data
    public static class MpConfig {

        private String appId;

        private String appSecret;
        /**
         * 设置微信公众号的token
         */
        private String token;

        /**
         * 设置微信公众号的EncodingAESKey
         */
        private String aesKey;
    }
    @Override
    public String toString() {
        return JSONUtil.toJsonPrettyStr(this);
    }
}
