package com.navigator.dagama.service.logic;

import org.springframework.stereotype.Service;

@Service
public class BusinessStakeholderService {
    public String getFactoryEmailSender(String factoryCode, Integer categoryId, Integer salesType) {
        return "";
    }

    public String getFactoryEmailCopyer(String factoryCode, Integer categoryId, Integer salesType) {
        return "";
    }

    public String getCustomerEmailReceiver(Integer customerId, Integer categoryId, Integer salesType) {
        return "";
    }
}
