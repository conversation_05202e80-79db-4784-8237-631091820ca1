package com.navigator.dagama.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;

import java.util.List;

public interface DbmInmailService {

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    Result queryInmail(QueryDTO<DbmInmailEntity> queryDTO);


    /**
     * 站内信数量
     *
     * @param system
     * @return
     */
    Result queryInmailCount(Integer system);

    /**
     * 根据用户查询站内信数量
     *
     * @param receiver
     * @return
     */
    Integer queryEmployInMailCount(Integer receiver);

    /**
     * 表示是否已读
     *
     * @param id
     * @return
     */
    Result readInmail(List<Integer> id);

}