package com.navigator.dagama.batch;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.dao.DbmSendTaskDao;
import com.navigator.dagama.pogo.model.dto.QuerySendTaskDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.service.SendTaskService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class CustomerSignNoticeResendScheduler extends BaseScheduleService<DbmSendTaskEntity> {
    @Resource
    DbmSendTaskDao dbmSendTaskDao;
    @Resource
    SendTaskService sendTaskService;

    @Override
    protected List<DbmSendTaskEntity> fetchList(int batchRecordCount) {
        List<DbmSendTaskEntity> list = new ArrayList<>();
        QueryDTO<QuerySendTaskDTO> queryDto = new QueryDTO<>();
        queryDto.setPageNo(1);
        queryDto.setPageSize(batchRecordCount);
        QuerySendTaskDTO condition = new QuerySendTaskDTO();
        condition.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGN_NOTICE.name());
        condition.setStatusList(MessageSendStatusEnum.getResendStatus());
        queryDto.setCondition(condition);
        Result rtn = dbmSendTaskDao.queryUnSendTask(queryDto);
        if (null != rtn.getData()) {
            list = (List<DbmSendTaskEntity>) rtn.getData();
        }
        return list;
    }

    @Override
    protected void process(DbmSendTaskEntity item) {
        System.out.println("=================:  " + item.getId());
        int tryTimes = item.getTryTimes();
        if (StringUtil.isEmpty(item.getReceiver()) || tryTimes > 3) {
            item.setStatus(MessageSendStatusEnum.ABNORMAL.name());
            item.setMemo(item.getMemo() + DateTimeUtil.formatDateTimeString() + "重发异常了；");
            dbmSendTaskDao.updateById(item);
            operationLogFacade.recordsimpleredalarm(item.getId(), "CustomerSignNoticeResendScheduler.reSend", "客户通知邮件信息异常;");
        } else {
            item.setResend(true);
            item.setMemo(item.getMemo() + DateTimeUtil.formatDateTimeString() + "系统自动进行重发；");
            sendTaskService.sendMessage(item);
        }
    }
}
