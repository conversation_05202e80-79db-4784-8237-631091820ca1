package com.navigator.dagama.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.navigator.common.util.HttpClient;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.dao.DbmInmailDao;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.enums.MessageSendStatusEnum;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import com.navigator.dagama.service.SendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 11:04
 */
@Component("inmail")
@Slf4j
public class InmailMessageSender extends AbstractMessageSender {

    @Resource
    DbmInmailDao dbmInmailDao;
    @Resource
    HttpClient httpClient;
    @Resource
    private SendTaskService sendTaskService;

    @Override
    protected SendMessageDTO fullMessageReceiver(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity) {

        List<ReceiverContactVO> receiverContactVOS = messageUserService.getReceiverMessage(sendMessageDTO.getReceivers(), businessTemplateDTO.getMessageType(), businessTemplateDTO.getReceiverType(), sendMessageDTO.getSystem(), sendMessageDTO.getCustomerId());
        String receiverIds = "";
        for (ReceiverContactVO receiverContactVO : receiverContactVOS) {
            receiverIds = receiverIds + receiverContactVO.getId() + ";";
        }
        sendTaskEntity.setReceiver(receiverIds);
        dbmSendTaskDao.updateById(sendTaskEntity);

        return sendMessageDTO;
    }


    @Override
    protected String sendTheMessage(DbmSendTaskEntity sendTaskEntity) {

        System.out.println("发送站内信1" + JSONObject.toJSONString(sendTaskEntity));
        // 获取对应系统的回调地址
        String url = sendTaskEntity.getMemo();

        Map<String, Object> param = new HashMap<>();
        if (sendTaskEntity.getTitle() != null || sendTaskEntity.getContent() != null) {
            param.put("userId", sendTaskEntity.getReceiver());
            param.put("title", sendTaskEntity.getTitle());
            param.put("content", sendTaskEntity.getContent());
        } else {
            throw new RuntimeException("携带参数为空,无法找到对应参数");
        }
        String post = "";
        if (StringUtils.isEmpty(url)) {
            /*// 本地保存方式
            DbmBusinessTemplateEntity dbmBusinessTemplateEntity = businessTemplateDao.getById(sendTaskEntity.getBusinessTemplateId());

            String businessScene = null;
            if(null != dbmBusinessTemplateEntity){
                businessScene = dbmBusinessTemplateEntity.getBusinessCode();
            }*/

            dbmInmailDao.saveMessageEntity(sendTaskEntity);

            sendTaskEntity.setStatus(MessageSendStatusEnum.SUCCESS.name());
            dbmSendTaskDao.updateById(sendTaskEntity);

            try {
                sendWebSocketInMail(StringUtil.split1Int(sendTaskEntity.getReceiver()), sendTaskEntity.getSystem());
            } catch (Exception e) {
                log.debug("sendWebSocketInMail:{}", e.getMessage());
            }

        } else {
            post = httpClient.post(url, param);
            System.err.println(post);
        }
        return MessageSendStatusEnum.SUCCESS.getMsg();
    }


    public void sendWebSocketInMail(List<Integer> roleIdList, Integer system) {
        for (Integer receiver : roleIdList) {
            Integer inMailCount = dbmInmailDao.queryEmployInMailCount(receiver);

            WebsocketInMailDTO websocketInMailDTO = new WebsocketInMailDTO();
            websocketInMailDTO.setUserId(receiver)
                    .setSystem(system)
                    .setInMailCount(inMailCount);
            sendTaskService.pushWebsocketInMailMsg(websocketInMailDTO);
        }

    }


}
