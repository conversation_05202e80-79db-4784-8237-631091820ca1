package com.navigator.dagama.message;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/18 17:28
 */
@Component
public class MessageBuildStrategyContext {
    private final Map<String, IMessageBuilder> strategyMap = new ConcurrentHashMap<>();

    @Autowired
    public void strategyInterface(Map<String, IMessageBuilder> strategyMapInfo) {
        this.strategyMap.clear();
        strategyMapInfo.forEach((k, v) -> this.strategyMap.put(k, v));
    }

    private IMessageBuilder getStrategy(String messageType) {
        return messageType != null ? strategyMap.get(messageType) : null;
    }

    //    public Map<String, Object> buildMessageContent(String messageType, Map<String, String> dataMap, DbmTemplateEntity templateEntity) {
//        return messageType != null ? this.getStrategy("b" + messageType).buildMessageContent(dataMap, templateEntity) : null;
//    }
    public Map<String, Object> buildMessageContent(String messageType, Map<String, Object> dataMap, BusinessTemplateDTO businessTemplateDTO) {
        if (messageType != null) {

            IMessageBuilder strategy = this.getStrategy("b" + messageType);
            DbmTemplateEntity templateEntity = businessTemplateDTO.getTemplateEntity();
            Map<String, Object> content = strategy.buildMessageContent(dataMap, templateEntity);
            return content;
        }
        return null;
    }

    //组装消息信息
    public SendMessageDTO buildMessageContent(String messageType, MessageInfoDTO messageInfoDTO, BusinessTemplateDTO businessTemplateDTO) {
        if (messageType != null) {

            IMessageBuilder strategy = this.getStrategy("b" + messageType);

            DbmTemplateEntity templateEntity = businessTemplateDTO.getTemplateEntity();

            SendMessageDTO sendMessageDTO = strategy.buildMessageContent(messageInfoDTO, templateEntity);

            String templateId = templateEntity.getId().toString();
            if (StringUtil.isNotEmpty(templateEntity.getThirdPartyId())) {
                templateId = templateEntity.getThirdPartyId();
            }

            List<String> receivers = messageInfoDTO.getReceivers();
            if (CollectionUtil.isEmpty(receivers)) {
                // 如果没有传递接收人，则取模板中的接收人，使用;分割再转为数组
                String receiver = businessTemplateDTO.getReceiver();
                if (StringUtil.isNotEmpty(receiver)) {
                    receivers = CollectionUtil.newArrayList(receiver.split(";"));
                }
            }

            sendMessageDTO.setMessageType(messageType)
                    .setBusinessCode(messageInfoDTO.getBusinessCode())
                    .setDataMap(messageInfoDTO.getDataMap())
                    .setOriginData(JSON.toJSONString(messageInfoDTO))
                    .setTemplateId(templateId)
                    .setReferBizId(messageInfoDTO.getReferBizId())
                    .setSystem(messageInfoDTO.getSystem())
                    .setReceivers(receivers)
                    .setFactoryCode(messageInfoDTO.getFactoryCode())
                    .setCategoryId(messageInfoDTO.getCategoryId())
                    .setSalesType(messageInfoDTO.getSalesType())
                    .setMenuCode(messageInfoDTO.getMenuCode())
                    .setCustomerId(messageInfoDTO.getCustomerId())
                    .setBusinessScene(messageInfoDTO.getBusinessSceneCode())
            ;

            sendMessageDTO.setSender(businessTemplateDTO.getSender())
                    .setCopyer(businessTemplateDTO.getCopyer())
                    .setCopyerType(businessTemplateDTO.getCopyerType())
                    .setReceiverType(businessTemplateDTO.getReceiverType())
            ;

            return sendMessageDTO;
        }
        return null;
    }
}
