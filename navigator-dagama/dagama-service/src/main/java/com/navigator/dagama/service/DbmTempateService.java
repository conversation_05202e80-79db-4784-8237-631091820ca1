package com.navigator.dagama.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;

public interface DbmTempateService {

    /**
     * 查询模板
     * @param queryDTO
     * @return
     */
    Result queryTemplate(QueryDTO<DbmTemplateEntity> queryDTO);

    /**
     * 根据id查询模板
     *
     * @param id
     * @return
     */
    Result queryTemplateById (Integer id);

    /**
     * 修改模板内容
     *
     * @param dbmTemplateEntity
     * @return
     */
    Result updateTemplate(DbmTemplateEntity dbmTemplateEntity);

    /**
     * 业务模板查询
     *
     * @param queryDTO
     * @return
     */
    Result queryBusinessTemplate(QueryDTO<DbmBusinessTemplateEntity> queryDTO);

    /**
     * 业务模板状态修改
     *
     * @param dbmBusinessTemplateEntity
     * @return
     */
    Result updateBusinessTemplateStatus(DbmBusinessTemplateEntity dbmBusinessTemplateEntity);

}