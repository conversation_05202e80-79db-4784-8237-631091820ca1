package com.navigator.dagama.message.impl;


import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.dagama.message.IMessageBuilder;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import com.navigator.dagama.pogo.model.enums.IsGatherEnum;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: wang tao
 * @Date: 2021/3/17
 * @Time: 15:44
 * @Desception: 构建站内信消息
 */
@Component("binmail")
public class InMailIMessageBuilder implements IMessageBuilder {

    /**
     * 构建站内信消息
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity) {

        //站内信需要拼接标题
        return TemplateRenderUtil.assemblyMessageInfo(templateEntity.getTitle(), templateEntity.getContent(), dataMap, templateEntity.getContentType());
    }

    @Override
    public SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity) {
        SendMessageDTO sendMessageDTO = new SendMessageDTO();

        try {
            Map<String, Object> mapInfo;
            if (IsGatherEnum.NO.getValue().equals(templateEntity.getIsGather())) {
                mapInfo = TemplateRenderUtil.assemblyMessageInfo(templateEntity.getTitle(), templateEntity.getContent(), messageInfoDTO.getDataMap(), templateEntity.getContentType());
            } else {
                mapInfo = TemplateRenderUtil.gatherAssemblyMessageInfo(templateEntity.getTitle(), templateEntity.getContent(), messageInfoDTO.getDataMaps());
            }

            String title = mapInfo.get("title").toString();
            String content = mapInfo.get("content").toString();

            sendMessageDTO.setTitle(title)
                    .setContent(content);
        } catch (Exception e) {

        }

        return sendMessageDTO;
    }


}
