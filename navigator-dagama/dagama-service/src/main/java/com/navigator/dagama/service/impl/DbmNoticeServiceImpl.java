package com.navigator.dagama.service.impl;

import com.navigator.dagama.dao.DbmBatchNoticeDao;
import com.navigator.dagama.pogo.model.entity.DbmBatchNoticeEntity;
import com.navigator.dagama.pogo.model.enums.MessageBatchProcessStatusEnum;
import com.navigator.dagama.service.DbmNoticeService;

import javax.annotation.Resource;

public class DbmNoticeServiceImpl implements DbmNoticeService {

    @Resource
    DbmBatchNoticeDao dbmBatchNoticeDao;


    public void saveBatchNotice(DbmBatchNoticeEntity batchNoticeEntity) {
        batchNoticeEntity.setStatus(MessageBatchProcessStatusEnum.INIT.getValue());

        dbmBatchNoticeDao.save(batchNoticeEntity);
    }

}
