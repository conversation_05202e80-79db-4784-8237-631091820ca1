package com.navigator.dagama.service.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.dao.DbmSendTaskDao;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.service.DbmSendTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DbmSendTaskServiceImpl implements DbmSendTaskService {

    @Resource
    private DbmSendTaskDao dbmSendTaskDao;

    /**
     * 发送信息列表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public Result querySendTas(QueryDTO<DbmSendTaskEntity> queryDTO) {
        return dbmSendTaskDao.querySendTas(queryDTO);
    }

    @Override
    public Result queryUnSendTask(QueryDTO<DbmSendTaskEntity> queryDTO) {
        return dbmSendTaskDao.querySendTas(queryDTO);
    }

}