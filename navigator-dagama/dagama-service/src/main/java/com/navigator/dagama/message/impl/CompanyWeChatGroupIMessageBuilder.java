package com.navigator.dagama.message.impl;

import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.dagama.message.IMessageBuilder;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: wang tao
 * @Date: 2021/3/24
 * @Time: 16:07
 * @Desception: 构建企业微信群发消息
 */
@Component("bcompanyWechatGroup")
public class CompanyWeChatGroupIMessageBuilder implements IMessageBuilder {

    /**
     * 构建企业微信群发消息
     *
     * @param dataMap        数据的map集合
     * @param templateEntity 模板entity
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> buildMessageContent(Map<String, Object> dataMap, DbmTemplateEntity templateEntity) {

        //站内信不需要拼接标题
        return TemplateRenderUtil.assemblyMessageInfo(null, templateEntity.getContent(), dataMap, templateEntity.getContentType());
    }

    @Override
    public SendMessageDTO buildMessageContent(MessageInfoDTO messageInfoDTO, DbmTemplateEntity templateEntity) {
        return null;
    }
}
