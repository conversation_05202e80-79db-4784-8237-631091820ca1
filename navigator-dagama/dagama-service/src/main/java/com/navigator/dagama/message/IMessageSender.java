package com.navigator.dagama.message;


import com.navigator.dagama.pogo.model.dto.BusinessTemplateDTO;
import com.navigator.dagama.pogo.model.dto.SendMessageDTO;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 11:03
 */
public interface IMessageSender {

    String sendMessage(SendMessageDTO sendMessageDTO, BusinessTemplateDTO businessTemplateDTO, DbmSendTaskEntity sendTaskEntity);

    String sendMessage(DbmSendTaskEntity sendTaskEntity);

}
