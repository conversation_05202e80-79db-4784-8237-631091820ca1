package com.navigator.dagama.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.dagama.mapper.DbmTempateMapper;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import com.navigator.dagama.pogo.model.vo.TemplateVO;

import java.util.List;

@Dao
public class DbmTempateDao extends BaseDaoImpl<DbmTempateMapper,DbmTemplateEntity> {

    /**
     * 查询模板
     * @param queryDTO
    /queryTemplate     */
    public Result qureyTemplate(QueryDTO<DbmTemplateEntity> queryDTO) {
        Page<DbmTemplateEntity> page = new Page<>(queryDTO.getPageNo(),queryDTO.getPageSize());
        IPage<DbmTemplateEntity> iPage = this.baseMapper.selectPage(page, Wrappers.<DbmTemplateEntity>lambdaQuery()
                .orderByDesc(DbmTemplateEntity::getId));
        List<TemplateVO> testInfoVOS = BeanConvertUtils.convert2List(TemplateVO.class,iPage.getRecords());
        return Result.page(iPage,testInfoVOS);
    }

    public DbmTemplateEntity getDbmTemplateById(Integer id){
        return this.baseMapper.selectById(id);
    }
}
