package com.navigator.dagama.batch;

import com.navigator.dagama.DagamaApplication;
import com.navigator.dagama.service.CustomerSignNoticeItemDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DagamaApplication.class)
public class CustomerSignNoticeResendSchedulerTest {

    @Autowired
    CustomerSignNoticeResendScheduler customerSignNoticeResendScheduler;

    @Test
    public void testScheduler() {
        customerSignNoticeResendScheduler.execute();
    }
}