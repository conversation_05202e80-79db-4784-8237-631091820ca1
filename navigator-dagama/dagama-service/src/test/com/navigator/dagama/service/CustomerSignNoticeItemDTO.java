package com.navigator.dagama.service;

import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.pogo.model.vo.ReceiverContactVO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.Test;

import java.util.List;

@Data
@Accessors(chain = true)
public class CustomerSignNoticeItemDTO {
    String businessCode;
    Integer signId;
    Integer categoryId;
    String categoryCode;
    Integer salesType;
    Integer customerId;
    String customerName;
    String supplierName;
    String factoryCode;
    String factoryName;
    String contractCode;
    String contractFilePath;

    private String sender;
    private String senderName;
    private String replayTo;
    private List<ReceiverContactVO> receiver;
    private List<String> ccList;



    public String getCurrentTime() {
        return DateTimeUtil.formatDateString();
    }

    String currentTime;

    @Test
    public void testVoid(){
        StringBuilder sbMemo=new StringBuilder(        );
        sbMemo.append("01").append("02").append("03");
        sbMemo.insert(0,"00").append("05");
        System.out.println(sbMemo.toString());
    }


}
