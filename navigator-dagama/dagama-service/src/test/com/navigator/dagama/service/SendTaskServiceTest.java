package com.navigator.dagama.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.db.Db;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.UUIDHexGenerator;
import com.navigator.dagama.config.wechat.MailNoticeProperties;
import com.navigator.dagama.dao.DbmBusinessTemplateDao;
import com.navigator.dagama.facade.impl.MessageFacadeImpl;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.entity.DbmBatchNoticeEntity;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.dagama.pogo.model.enums.ReceiverTypeEnum;
import com.navigator.dagama.service.logic.BatchColumbusUserNoticeProcessor;
import com.navigator.dagama.utils.mail.MailUtilImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.util.*;

import static com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum.LDC_TASK_APPROVE_NOTICE;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
class SendTaskServiceTest {
    @Autowired
    SendTaskService sendTaskService;
    @Autowired
    DbmBusinessTemplateDao dbmBusinessTemplateDao;
    @Resource
    MessageFacadeImpl messageFacade;

    @Resource
    private DbmInmailService dbmInmailService;

    @Resource
    MailNoticeProperties mailNoticeProperties;

    @Resource
    DbmBusinessTemplateService dbmBusinessTemplateService;

    @MockBean
    EmployPermissionFacade employPermissionFacade;
    @MockBean
    RoleFacade roleFacade;
    @MockBean
    EmployFacade employFacade;

    @Resource
    BatchColumbusUserNoticeProcessor batchColumbusUserNoticeProcessor;


    @BeforeEach
    void setUp() {

        /*List<EmployRoleEntity> listEmployRoleEntity = new ArrayList<>();
        EmployRoleEntity employRoleEntity = new EmployRoleEntity();
        employRoleEntity.setEmployId(101);
        listEmployRoleEntity.add(employRoleEntity);
        employRoleEntity = new EmployRoleEntity();
        employRoleEntity.setEmployId(102);
        listEmployRoleEntity.add(employRoleEntity);
        employRoleEntity = new EmployRoleEntity();
        employRoleEntity.setEmployId(103);
        listEmployRoleEntity.add(employRoleEntity);
        employRoleEntity = new EmployRoleEntity();
        employRoleEntity.setEmployId(105);
        listEmployRoleEntity.add(employRoleEntity);
        Result result = Result.success();
        result.setData(listEmployRoleEntity);

        Mockito.when(employPermissionFacade.getEmployRoleListByRoleIds(Mockito.anyList())).thenReturn(result);


        List<EmployEntity> list=new ArrayList<>();
        EmployEntity employEntity=new EmployEntity();
        employEntity.setId(101).setEmail("<EMAIL>");
        list.add(employEntity);

        employEntity=new EmployEntity();
        employEntity.setId(102).setEmail("<EMAIL>");
        list.add(employEntity);

        employEntity=new EmployEntity();
        employEntity.setId(103).setEmail("<EMAIL>");
        list.add(employEntity);

        employEntity=new EmployEntity();
        employEntity.setId(104).setEmail("<EMAIL>");
        list.add(employEntity);

        Mockito.when(employFacade.queryEmployByRoleIds(Mockito.anyList())).thenReturn(list);

        Mockito.when(employFacade.getEmployByEmployIds(Mockito.anyList())).thenReturn(list);*/

    }

    @Test
    void sendEmail() {

        List<String> CcList = new ArrayList<>();
        CcList.add("nzj");
        CcList.add("xcy");

        String[] scc = CcList.toArray(new String[0]);


        messageFacade.sendEmailInfo("<EMAIL>", "你有一份新合同", "你有一份新合同需要签署，请尽快处理");
        messageFacade.sendEmailInfo("<EMAIL>,<EMAIL>", "你有一份新合同2", "你有一份新合同需要签署，请尽快处理");
        messageFacade.sendEmailInfo("<EMAIL>;<EMAIL>", "你有一份新合同3", "你有一份新合同需要签署，请尽快处理");

    }

    @Test
    void sendMessage() {
        Map<String, Object> data = new HashMap<>();
        CustomerSignNoticeItemDTO itemDTO = new CustomerSignNoticeItemDTO();
        itemDTO.setBusinessCode("CUSTOMER_SIGN_NOTICE")
                .setSignId(10001)
                .setCategoryId(11)
                .setCategoryCode("SBM")
                .setSalesType(2)
                .setCustomerId(201)
                .setCustomerName("上海甜头菜电子商务有限公司")
                .setSupplierName("路易达孚中国商贸有限公司")
                .setFactoryCode("ZJG")
                .setFactoryName("张家港粮油公司")
                .setContractCode("HT00010001")
                .setContractFilePath("https://www.ldc.cn/file/20221101/HT00010001.pdf")
                //.setSender("factory_mail")
                /*.setSenderName("达孚中国")
                .setReplayTo("<EMAIL>")
                .setCcList(StringUtil.split("<EMAIL>;<EMAIL>;<EMAIL>;", ";"))*/
                .setSalesType(2);

        /*List<ReceiverContactVO> voList = new ArrayList<>();
        ReceiverContactVO vo = new ReceiverContactVO();
        vo.setEmail("<EMAIL>");
        vo.setId(100);
        vo.setName("牛占军");
        voList.add(vo);
        vo = new ReceiverContactVO();
        vo.setEmail("<EMAIL>");
        vo.setId(101);
        vo.setName("胡欣");
        voList.add(vo);
        itemDTO.setReceiver(voList);*/

        data = BeanUtil.beanToMap(itemDTO);

        MessageInfoDTO msgInfoDTO = new MessageInfoDTO();
        msgInfoDTO.setBusinessCode("CUSTOMER_SIGN_NOTICE");
        msgInfoDTO.setReferBizId("10");
        msgInfoDTO.setSystem(SystemEnum.MAGELLAN.getValue());
        msgInfoDTO.setDataMap(data);
        List<String> receivers=new ArrayList<>();
        receivers.add("9001");
        msgInfoDTO.setReceivers(receivers);
        msgInfoDTO.setFactoryCode("DG");
        msgInfoDTO.setCategoryId(11);
        msgInfoDTO.setSalesType(2);

        sendTaskService.sendMessage(msgInfoDTO);

    }

    @Test
    public void testAllocate() {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setSystem(1);
        List<String> receivers = new ArrayList<>();
        receivers.add(String.valueOf(1));
        messageInfoDTO.setReceivers(receivers);


        //发送模板code
        messageInfoDTO.setBusinessCode("UpdateAllocate_Sms");
        Map<String, Object> map = new HashMap<>();
        map.put("customerName", "上海甜头菜");
        map.put("priceApplyCode", "PA002002002");
        map.put("dealNum", 6000);
        map.put("priceType", "结构化定价");
        map.put("transactionPrice", 3500);

        //原合同信息

        map.put("contractCode", "HT0001000011");
        map.put("contractNum", 8800);
        map.put("allocateNum", 120);
        map.put("extraPrice", 3800);
        map.put("deliveryStartTime", "2022年11月11日");
        map.put("deliveryEndTime", "2022年11月12日");

        //新合同信息

        map.put("newContractCode", "HT3000010003");
        map.put("newContractNum", 120);
        map.put("newExtraPrice", 3850);
        map.put("newAllocateNum", 120);
        map.put("newDeliveryStartTime", "2022年11月11日");
        map.put("newDeliveryEndTime", "2022年11月12日");


        messageInfoDTO.setDataMap(map);
        sendTaskService.sendMessage(messageInfoDTO);
    }

    @Test
    public void testResetPWD() {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_COLUMBUS_RESET_PWD.name());
        messageInfoDTO.setReferBizId("200003");
        Map<String, String> mapData = new HashMap<>();

        String password = "DF" + UUIDHexGenerator.randomCoding(4);


        mapData.put("password", password);
        messageInfoDTO.setDataMap(mapData);
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        List<String> receivers = new ArrayList<>();
        receivers.add("200003");
        messageInfoDTO.setReceivers(receivers);

        sendTaskService.sendMessage(messageInfoDTO);
    }

    @Test
    public void testResend() {
        sendTaskService.reSendMessage(180);
    }


    @Test
    public void testApprove() {
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setUuid(UUID.randomUUID().toString())
                .setBusinessCode(LDC_TASK_APPROVE_NOTICE.name())
                .setReferBizId("101202303")
                .setSystem(SystemEnum.MAGELLAN.getValue());

        Map<String, String> mapBizData = new HashMap<>();
        mapBizData.put("contractCode", "HT0000001");
        mapBizData.put("nodeName", "A签");

        messageInfoDTO.setDataMap(mapBizData);
        String receivers = "100056,100057,100070,11,12,13,17,18,19,2,3,4,5,6,7,8,9,11,12,12,";

        messageInfoDTO.setReceivers(StringUtil.split(receivers));
        messageFacade.sendMessage(messageInfoDTO);


    }

    @Test
    public void testQueryBusinessTemplate(){
        QueryDTO<DbmBusinessTemplateEntity> queryDTO=new QueryDTO<>();
        Result result = dbmBusinessTemplateService.queryBusinessTemplate(queryDTO);
        System.out.println(result);

    }

    @Test
    public void testEmploy(){

        /*DbmBatchNoticeEntity batchNoticeEntity=new DbmBatchNoticeEntity();
        batchNoticeEntity.setTitle("Columbus更新了");
        batchNoticeEntity.setContent("Columbus更新了，更新了好多功能");
        batchNoticeEntity.setBusinessCode(MessageBusinessCodeEnum.CLB_USER_MANUAL_NOTICE.name());
        batchNoticeEntity.setReceivers(ReceiverTypeEnum.COLUMBUS_USER.getValue());
        batchNoticeEntity.setSystem(SystemEnum.COLUMBUS.getValue());

        batchColumbusUserNoticeProcessor.createColumbusUserNoticeRequest(batchNoticeEntity);*/
        batchColumbusUserNoticeProcessor.execute();
    }

    public Result mockCEmployList(QueryDTO<CEmployDTO> queryDTO) {
        Integer pn = queryDTO.getPageNo();
        Integer pz = queryDTO.getPageSize();

        List<CEmployEntity> list = new ArrayList<>();

        for (int i = 0; i < pz; i++) {
            Integer startId = (pn - 1) * pz + i + 110001;
            CEmployEntity cEmployEntity = new CEmployEntity();
            cEmployEntity.setId(startId);
            cEmployEntity.setEmail(startId+"@fn.com");
            cEmployEntity.setPhone("13800"+startId);
            list.add(cEmployEntity);
        }

        Result rtn=new Result();
        rtn.setTotalPage(5);
        rtn.setPageNum(pn);
        rtn.setTotalCount(500);
        rtn.setPageSize(100);
        rtn.setData(list);


        return rtn;

    }
}