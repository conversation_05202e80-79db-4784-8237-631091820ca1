FROM openjdk:11.0
RUN mkdir /config
COPY  navigator-dagama/dagama-service/src/main/resources/bootstrap-dev.yml /config
COPY  navigator-dagama/dagama-service/src/main/resources/bootstrap-sim-prod.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/dagama-service/*.jar /navigator-dagama-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-dagama-1.0-SNAPSHOT.jar
