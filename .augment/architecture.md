# Navigator_cloud 架构说明

## 系统架构概览

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐
│   Columbus_web  │    │   Magellan_web  │
│   (外部用户)      │    │   (内部用户)     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │  Navigator Gateway   │
         └───────────┬───────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───▼───┐       ┌───▼───┐       ┌───▼───┐
│微服务1 │  ...  │微服务N │  ...  │微服务13│----> Atlas
└───┬───┘       └───┬───┘       └───┬───┘
    │               │               │
    └───────────────┼───────────────┘
                    │
         ┌──────────▼──────────┐
         │   SQL Server +     │
         │   Redis + Activiti │
         └─────────────────────┘
```

## 微服务详细说明

### 核心业务服务
1. **navigator-trade** - 交易服务
   - 合同管理
   - 交易处理
   - 价格管理

2. **navigator-delivery** - 提货服务
   - 提货申请
   - ATLAS集成
   - 物流管理

3. **navigator-customer** - 客户服务
   - 客户信息管理
   - 权限控制

4. **navigator-goods** - 商品服务
   - 商品信息管理
   - 规格管理

### 工作流和审批服务
5. **navigator-activiti** - 工作流服务
   - LOA审批流程
   - 工作流引擎
   - 审批状态管理

### 外部集成服务
6. **navigator-cuckoo** - ATLAS集成服务
   - ATLAS API调用
   - 数据同步
   - 回调处理

7. **navigator-sparrow** - 消息服务
   - 消息推送
   - 通知管理

8. **navigator-pigeon** - LKG对接服务
   - 与LKG交互
   - 数据同步

### 基础设施服务
9. **navigator-gateway** - 网关服务
   - 路由转发
   - 负载均衡
   - 安全控制

10. **navigator-admin** - 管理服务
    - 系统配置
    - 用户管理
    - 权限管理

### 其他业务服务
11. **navigator-future** - 期货服务, 头寸操作， 点价， 反点价， 挂单， 随盘等
12. **navigator-husky** - 数字合同服务
13. **navigator-koala** - 报表服务

## 数据架构

### 数据库设计
- **主数据库**: SQL Server
- **缓存**: Redis
- **工作流**: Activiti引擎

### 核心表结构
- **dbt_contract** - 合同主表
- **dbd_delivery_apply** - 提货申请表
- **ACT_*** - Activiti工作流表（13个）
- **dbi_atlas_*** - ATLAS集成相关表

### 数据流向
```
前端请求 → Gateway → 微服务 → 数据库
                ↓
            Redis缓存
                ↓
          ATLAS外部系统
```

## 技术栈

### 后端技术
- **框架**: Spring Cloud
- **语言**: Java 11
- **构建**: Maven
- **数据库**: SQL Server
- **缓存**: Redis
- **工作流**: Activiti
- **容器**: Docker

### 前端技术
- **Columbus**: Vue.js（内部用户）
- **Magellan**: Vue.js（外部用户）

### 基础设施
- **注册中心**: Nacos
- **配置中心**: Nacos
- **网关**: Spring Cloud Gateway
- **监控**: 自定义监控

## 部署架构

### 环境分层
- **开发环境**: 本地开发
- **测试环境**: 集成测试
- **预生产环境**: 性能测试
- **生产环境**: 正式运行

### 容器化部署
- 每个微服务独立Docker镜像
- Kubernetes编排管理
- 配置文件外部化

## 集成架构

### 外部系统集成
- **ATLAS**: 通过CUCKOO微服务集成
- **一起签**: 电子签名服务
- **其他第三方**: 根据业务需要

### 内部服务通信
- **同步调用**: Feign客户端
- **异步消息**: 消息队列
- **数据共享**: 数据库和缓存

## 安全架构

### 认证授权
- JWT Token认证
- 基于角色的权限控制
- 接口级权限验证

### 数据安全
- 敏感数据加密
- 数据传输加密
- 审计日志记录

## 监控和运维

### 监控体系
- 应用性能监控
- 业务指标监控
- 系统资源监控

### 日志管理
- 集中化日志收集
- 结构化日志格式
- 日志分析和告警
