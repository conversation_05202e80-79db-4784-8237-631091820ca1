# Navigator_cloud 项目记忆配置

## 项目架构记忆

### 微服务架构
- **架构模式**: Spring Cloud Gateway + 13个业务微服务
- **服务列表**: activiti、admin、cuckoo、customer、delivery、future、gateway、goods、husky、koala、pigeon、sparrow、trade
- **前端项目**: Columbus_web（外部用户）、Magellan_web（内部用户）
- **数据层**: SQL Server + Redis + Activiti 工作流引擎

### 业务域记忆
- **合同管理**: contractType=1(固定价格)、contractType=4(基差临时定价)
- **提货申请**: ATLAS系统集成（通过CUCKOO微服务）
- **审批流程**: 13个ACT_开头的Activiti表结构
- **状态管理**: atlas_apply_status='Cancelled'表示作废

### 技术栈记忆
- **开发语言**: Java 11
- **构建工具**: Maven多模块
- **容器化**: Docker
- **IDE**: IntelliJ IDEA
- **版本控制**: Git

## 开发环境记忆

### 本地配置
- **Nacos地址**: localhost:8848
- **命名空间**: navigator_cloud_int
- **Redis**: 本地Redis实例
- **数据库**: SQL Server

### 路径规律
- **pom.xml**: 各微服务根目录下
- **bootstrap.yml**: service/src/main/resources/
- **前端项目**: ../Columbus_web 和 ../Magellan_web

## 业务规则记忆

### 合同业务
- **group_id批次管理**: 同批次合同使用相同groupId
- **子合同继承**: 拆分、回购、修改等操作继承父合同groupId
- **回购按钮显示**: contractType===1 或 contractType===4 时显示

### 提货业务
- **执行数量跟踪**: executedNum字段由ATLAS回调更新
- **未提货量计算**: allocateNum - executedNum，最小值为0
- **ATLAS集成**: 通过CUCKOO微服务处理所有ATLAS相关服务

### 审批流程
- **LOA审批**: 涉及13个ACT_开头的表
- **工作流引擎**: Activiti
- **审批时间**: updated_at字段表示审批时间

## 常见问题记忆

### 技术选择
- **脚本执行**: 使用PowerShell而非cmd
- **ID生成**: 使用数据库序列而非MAX+1方式
- **Python执行**: 使用虚拟环境

### 编码问题
- **跨工作空间编辑**: 可能导致UTF-8 BOM编码问题
- **中文项目**: 需注意编码处理
- **文件编码**: 保持UTF-8，避免UTF-8 BOM

### 开发习惯
- **最小化修改**: 只修改需求范围内的代码
- **共享功能**: 修改时需格外谨慎
- **现有接口**: 优先使用而非创建新接口
