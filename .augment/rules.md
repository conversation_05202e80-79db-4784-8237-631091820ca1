# Navigator_cloud 开发规则和规范

## 代码修改规范

### 注释规范
```java
// [case number] [case name] changed by <PERSON> Shi at [YYYY-M-D] start
[修改的代码]
// [case number] [case name] changed by <PERSON> Shi at [YYYY-M-D] end
```

### 修改原则
1. **最小化修改影响** - 只修改需求明确提到的功能
2. **不影响现有功能** - 避免对已有功能造成影响
3. **共享功能谨慎** - 修改共用逻辑时需格外小心
4. **保持代码风格** - 不做额外的"优化"或"重构"
5. **避免猜测需求** - 不清楚的细节及时询问

## 架构设计原则

### 微服务边界
- 每个微服务负责特定的业务域
- 服务间通过定义良好的API进行通信
- 避免跨服务的直接数据库访问

### 数据库设计
- 主表添加字段时，考虑相关历史/审计表
- 使用数据库序列生成ID，避免MAX+1方式
- 保持数据一致性和可追溯性

### 前后端分离
- 后端提供RESTful API
- 前端通过API获取数据
- 双前端架构共享同一后端

## 开发流程规范

### 需求分析阶段
1. **信息收集** - 使用codebase-retrieval工具分析相关代码结构
2. **制定计划** - 基于代码分析制定详细的开发计划
3. **需求确认** - 向用户确认关键技术细节和业务逻辑
   - 数据结构和字段含义
   - 业务规则和判断条件
   - 数据处理时机和方式
   - 计算逻辑（累加/覆盖等）
4. **开始开发** - 确认所有细节后再开始编码

### 代码提交
- 提交前编译所有相关微服务
- 确保无编译错误
- 遵循Git提交信息规范

### 测试策略
- 优先使用现有接口进行测试
- 避免修改已测试通过的代码
- 新功能需要相应的测试用例

### 部署规范
- 使用Docker容器化部署
- 配置文件通过环境变量管理
- 遵循蓝绿部署策略

## 代码质量标准

### Java代码规范
- 使用Java 11语言特性
- 遵循阿里巴巴Java开发手册
- 合理使用设计模式

### 数据库规范
- 表名使用下划线命名
- 字段名清晰表达含义
- 适当添加索引优化查询

### 接口设计
- RESTful API设计原则
- 统一的响应格式
- 合理的HTTP状态码使用

## 特殊业务规则

### 合同相关
- contractType=1和4需要显示回购按钮
- group_id字段用于批次管理
- 子合同必须继承父合同的group_id

### 提货相关
- executedNum由ATLAS系统回调更新
- 未提货量 = allocateNum - executedNum（最小值0）
- 所有ATLAS操作通过CUCKOO微服务

### 审批相关
- LOA审批涉及多个ACT_表
- 审批时间使用updated_at字段
- 工作流状态需要正确维护

## 错误处理规范

### 异常处理
- 使用统一的异常处理机制
- 记录详细的错误日志
- 向用户返回友好的错误信息

### 日志规范
- 使用合适的日志级别
- 记录关键业务操作
- 便于问题排查和监控

## 性能优化原则

### 数据库优化
- 合理使用索引
- 避免N+1查询问题
- 适当使用缓存

### 接口优化
- 分页查询大数据集
- 使用异步处理长时间操作
- 合理设置超时时间

## 安全规范

### 数据安全
- 敏感数据加密存储
- 合理的权限控制
- 防止SQL注入

### 接口安全
- 身份认证和授权
- 输入参数验证
- 防止CSRF攻击
