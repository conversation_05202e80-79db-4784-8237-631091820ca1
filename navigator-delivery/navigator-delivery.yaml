apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-delivery-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-delivery-dev
  template:
    metadata:
      labels:
        app: ldc-navigator-delivery-dev
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-delivery-dev:#{Build.BuildId}#
        name: ldc-navigator-delivery-dev
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "test" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName:  storageaccount-csm4nnvgsto001-secret  # required
            shareName: logs-test  # required
            server: csm4nnvgsto001.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional          

---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-delivery-dev
  namespace: dev
spec:
  type: ClusterIP
  ports:
  - port: 9112
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-delivery-dev
