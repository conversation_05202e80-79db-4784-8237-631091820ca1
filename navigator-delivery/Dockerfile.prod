# FROM csm4nnvgacr001.azurecr.cn/openjdk:11.0
FROM openjdk:11.0
RUN mkdir /config
#COPY  navigator-future/future-service/src/main/resources/bootstrap-dev.yml /config
#COPY  navigator-future/future-service/src/main/resources/bootstrap.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/delivery-service/*.jar /navigator-delivery-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-delivery-1.0-SNAPSHOT.jar
