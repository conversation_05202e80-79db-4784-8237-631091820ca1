package com.navigator.delivery.service;

import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;

import java.util.List;

/**
 * <p>
 * 提货BI 查询服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface IDeliveryBIQueryService {

    // 查询仓单的可提货量（BI存储过程）
    List<ExchangePickQtyDTO> getExchangePickQty(ExchangePickQtyQO pickQtyQO);

    // 仓单预分配查询（BI存储过程）
    List<ExecutePreAllocationDTO> getExecutePreAllocation(ExecutePreAllocationQO preAllocationQO);

}
