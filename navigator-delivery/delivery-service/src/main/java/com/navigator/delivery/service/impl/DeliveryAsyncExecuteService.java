package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.ImmutableMap;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.delivery.dao.DeliveryApplyDriverLogDao;
import com.navigator.delivery.pojo.dto.DeliveryApplyDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 异步执行器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryAsyncExecuteService {
    private final DeliveryApplyDriverLogDao deliveryApplyDriverLogDao;
    private final OperationLogFacade operationLogFacade;
    private final CustomerFacade customerFacade;
    private final MessageFacade messageFacade;

    /**
     * 保存司机输入记录
     */
    @Async
    public void recordDriverInputLog(DeliveryApplyEntity deliveryApply, DeliveryApplyDTO deliveryApplyDTO) {
        DeliveryApplyDriverLogEntity driverLogEntity = BeanUtil.toBean(deliveryApplyDTO, DeliveryApplyDriverLogEntity.class);
        driverLogEntity.setCustomerId(deliveryApply.getCustomerId())
                .setCreatedBy(deliveryApply.getCreatedBy())
                .setUpdatedBy(deliveryApply.getUpdatedBy());
        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
        deliveryApplyDriverLogDao.saveDriverRecord(driverLogEntity);
        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    }

    /**
     * 保存操作记录
     */
    @Async
    public void recordDeliveryApplyLog(DeliveryApplyEntity deliveryApply, String beforeData, String afterData, LogBizCodeEnum bizCodeEnum, SystemEnum systemEnum, Integer operatorId) {
        OperationDetailDTO operationDetailDTO = getOperationDetailDTO(deliveryApply, beforeData, afterData, bizCodeEnum, systemEnum, operatorId);
        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    @Async
    public void recordInvalidDeliveryApplyLog(DeliveryApplyEntity deliveryApply, String beforeData, String afterData, LogBizCodeEnum bizCodeEnum, SystemEnum systemEnum, Integer operatorId) {
        OperationDetailDTO operationDetailDTO = getOperationDetailDTO(deliveryApply, beforeData, afterData, bizCodeEnum, systemEnum, operatorId);
        operationDetailDTO.setOperationInfo(deliveryApply.getInvalidReason());
        operationLogFacade.recordOperationLog(operationDetailDTO);
    }

    private static OperationDetailDTO getOperationDetailDTO(DeliveryApplyEntity deliveryApply, String beforeData, String afterData, LogBizCodeEnum bizCodeEnum, SystemEnum systemEnum, Integer operatorId) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCodeEnum.getBizCode())
                .setOperationName(bizCodeEnum.getMsg())
                .setReferBizId(deliveryApply.getId())
                .setReferBizCode(deliveryApply.getCode())
                .setBizModule(ModuleTypeEnum.DELIVERY.getDesc())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setMetaData(beforeData)
                .setData(afterData)
                .setOperatorId(operatorId)
                .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                .setTriggerSys(systemEnum.getDescription());
        // 操作来源
        if (systemEnum == SystemEnum.MAGELLAN) {
            operationDetailDTO.setSource(OperationSourceEnum.EMPLOYEE.getValue());
        } else if (systemEnum == SystemEnum.COLUMBUS) {
            operationDetailDTO.setSource(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue());
        }
        return operationDetailDTO;
    }

    @Async
    public void sendInMailMessage(DeliveryApplyEntity applyEntity, MessageBusinessCodeEnum messageBusinessCodeEnum) {
        Map<String, Object> dataMap = new HashMap<>();

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setCustomerId(applyEntity.getCustomerId());
        messageInfoDTO.setCategoryId(applyEntity.getGoodsCategoryId());

        Map<String, MenuCodeEnum> menuCodeMap = ImmutableMap.<String, MenuCodeEnum>builder()
                .put(SystemEnum.COLUMBUS.getValue().toString() + SystemEnum.LKG.getValue().toString(), MenuCodeEnum.C_DELIVERY_APPLY)
                .put(SystemEnum.MAGELLAN.getValue().toString() + SystemEnum.LKG.getValue().toString(), MenuCodeEnum.M_DELIVERY_APPLY)
                .put(SystemEnum.COLUMBUS.getValue().toString() + SystemEnum.ATLAS.getValue().toString(), MenuCodeEnum.C_DELIVERY_APPLY_ATLAS)
                .put(SystemEnum.MAGELLAN.getValue().toString() + SystemEnum.ATLAS.getValue().toString(), MenuCodeEnum.M_DELIVERY_APPLY_ATLAS)
                .build();
        String menuCode = String.valueOf(menuCodeMap.get(applyEntity.getTriggerSys().toString() + applyEntity.getThirdSys().toString()).getValue());
        switch (messageBusinessCodeEnum) {
            case MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL:
                // 买方名称
                dataMap.put("customerName", customerFacade.queryCustomerById(applyEntity.getCustomerId()).getName());
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.MAGELLAN.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL.getDesc());
                break;
            case MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID:
                // 买方名称
                dataMap.put("customerName", customerFacade.queryCustomerById(applyEntity.getCustomerId()).getName());
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.MAGELLAN.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID.getDesc());
                break;
            case COLUMBUS_DELIVERY_APPLY_APPROVE_PASS:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_PASS.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_PASS.getDesc());
                break;
            case COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT.getDesc());
                break;
            case COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());
                // 审核结果
                dataMap.put("applyResult", applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.INVALID.getValue() ? "审核通过" : "审核驳回");

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE.getDesc());
                break;
            case COLUMBUS_DELIVERY_APPLY_INVALID:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_INVALID.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DELIVERY_APPLY_INVALID.getDesc());
                break;
            case COLUMBUS_DELIVERY_APPLY_BLOCKED:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());
                // 锁定原因
                dataMap.put("atlasSubStatus", applyEntity.getAtlasSubStatus());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_BLOCKED.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DELIVERY_APPLY_BLOCKED.getDesc());
                break;
            case MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING:
                // 申请单号
                dataMap.put("applyCode", applyEntity.getCode());

                // 所属系统
                messageInfoDTO.setSystem(SystemEnum.MAGELLAN.getValue());
                messageInfoDTO.setMenuCode(menuCode);
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING.name());
                messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING.getDesc());
            default:
                break;
        }

        // 客户撤回申请单，发送站内信
        log.info("DeliveryAsyncExecuteService, 提货委托发送消息============================：场景状态{}，内容：======================{}", messageBusinessCodeEnum, FastJsonUtils.getBeanToJson(messageInfoDTO.setDataMap(dataMap)));
        messageFacade.sendMessage(messageInfoDTO.setDataMap(dataMap));
    }
}
