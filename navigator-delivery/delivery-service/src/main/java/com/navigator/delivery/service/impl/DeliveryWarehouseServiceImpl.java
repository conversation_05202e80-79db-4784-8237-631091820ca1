package com.navigator.delivery.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.delivery.dao.DeliveryWarehouseDao;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;
import com.navigator.delivery.service.IDeliveryWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 提货库点配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
@RequiredArgsConstructor
public class DeliveryWarehouseServiceImpl implements IDeliveryWarehouseService {
    private final DeliveryWarehouseDao deliveryWarehouseDao;
    private final EmployFacade employFacade;
    private final OperationLogFacade operationLogFacade;

    @Override
    public Boolean addDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        // 校验LDC库：一个货品+一个工厂可对应唯一库点
        checkSaveDeliveryWareHouse(deliveryWarehouseEntity);

        // 操作人
        String operator = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));

        deliveryWarehouseEntity
                .setStatus(DisableStatusEnum.ENABLE.getValue())
                .setCreatedBy(operator)
                .setUpdatedBy(operator);
        deliveryWarehouseDao.save(deliveryWarehouseEntity);

        // 保存操作日志
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        recordOperationDetail
                .setBeforeData(JSON.toJSONString(deliveryWarehouseEntity))
                .setOperationActionEnum(OperationActionEnum.ADD_DELIVERY_WAREHOUSE);
        operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);

        return true;
    }

    @Override
    public Boolean updateDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        // 校验LDC库：一个货品+一个工厂可对应唯一库点
        checkUpdateDeliveryWareHouse(deliveryWarehouseEntity);

        deliveryWarehouseEntity
                .setUpdatedAt(new Date())
                .setUpdatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));

        deliveryWarehouseDao.updateById(deliveryWarehouseEntity);

        // 保存操作日志
        RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
        recordOperationDetail
                .setAfterData(JSON.toJSONString(deliveryWarehouseEntity))
                .setOperationActionEnum(OperationActionEnum.UPDATE_DELIVERY_WAREHOUSE);
        operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);

        return true;
    }


    /**
     * 校验新增提货库点配置
     *
     * @param deliveryWarehouseEntity 提货库点配置实体
     */
    private void checkSaveDeliveryWareHouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        // 校验编码是否重复
        List<DeliveryWarehouseEntity> warehouseEntityList = deliveryWarehouseDao.getDeliveryWarehouseList(new DeliveryWarehouseDTO()
                .setWarehouseCode(deliveryWarehouseEntity.getCode()));
        if (!warehouseEntityList.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_WAREHOUSE_CODE_REPEAT);
        }

        // LDC库：一个货品+一个工厂可对应唯一库点
        if (deliveryWarehouseEntity.getWarehouseType() == 1) {
            List<DeliveryWarehouseEntity> deliveryWarehouseList = deliveryWarehouseDao.getDeliveryWarehouseList(new DeliveryWarehouseDTO()
                    .setGoodsCategoryId(deliveryWarehouseEntity.getGoodsCategoryId())
                    .setWarehouseType(deliveryWarehouseEntity.getWarehouseType())
                    .setDeliveryFactory(deliveryWarehouseEntity.getDeliveryFactory()));

            if (!deliveryWarehouseList.isEmpty()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_WAREHOUSE_LDC_REPEAT);
            }
        }
    }

    /**
     * 校验修改提货库点配置
     *
     * @param deliveryWarehouseEntity 提货库点配置实体
     */
    private void checkUpdateDeliveryWareHouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        DeliveryWarehouseEntity originDeliveryHouse = deliveryWarehouseDao.getById(deliveryWarehouseEntity.getId());

        // 修改编码时，校验编码是否重复
        if (!originDeliveryHouse.getCode().equals(deliveryWarehouseEntity.getCode())) {
            List<DeliveryWarehouseEntity> warehouseEntityList = deliveryWarehouseDao.getDeliveryWarehouseList(new DeliveryWarehouseDTO()
                    .setWarehouseCode(deliveryWarehouseEntity.getCode()));
            if (!warehouseEntityList.isEmpty()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_WAREHOUSE_CODE_REPEAT);
            }
        }

        // 修改LDC库校验一个货品+一个工厂可对应唯一库点
        if (deliveryWarehouseEntity.getWarehouseType() == 1) {
            if (!originDeliveryHouse.getGoodsCategoryId().equals(deliveryWarehouseEntity.getGoodsCategoryId())
                    || !originDeliveryHouse.getDeliveryFactory().equals(deliveryWarehouseEntity.getDeliveryFactory())
                    || !originDeliveryHouse.getWarehouseType().equals(deliveryWarehouseEntity.getWarehouseType())) {
                List<DeliveryWarehouseEntity> deliveryWarehouseList = deliveryWarehouseDao.getDeliveryWarehouseList(new DeliveryWarehouseDTO()
                        .setGoodsCategoryId(deliveryWarehouseEntity.getGoodsCategoryId())
                        .setWarehouseType(deliveryWarehouseEntity.getWarehouseType())
                        .setDeliveryFactory(deliveryWarehouseEntity.getDeliveryFactory()));

                if (!deliveryWarehouseList.isEmpty()) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_WAREHOUSE_LDC_REPEAT);
                }
            }
        }
    }

    @Override
    public List<DeliveryWarehouseEntity> getDeliveryWarehouseList(DeliveryWarehouseDTO warehouseDTO) {
        return deliveryWarehouseDao.getDeliveryWarehouseList(warehouseDTO);
    }

    @Override
    public Boolean updateDeliveryWarehouseStatus(Integer warehouseId, Integer status) {
        DeliveryWarehouseEntity deliveryWarehouse = deliveryWarehouseDao.getById(warehouseId);
        if (null != deliveryWarehouse) {
            deliveryWarehouse.setStatus(status)
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));

            // 保存操作日志
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setAfterData(status.toString())
                    .setOperationActionEnum(status == 0 ? OperationActionEnum.DISABLE_DELIVERY_WAREHOUSE_STATUS : OperationActionEnum.ENABLE_DELIVERY_WAREHOUSE_STATUS);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
            return deliveryWarehouseDao.updateById(deliveryWarehouse);
        }
        return null;
    }

    @Override
    public DeliveryWarehouseEntity getDeliveryWarehouseById(Integer warehouseId) {
        return deliveryWarehouseDao.getById(warehouseId);
    }
}
