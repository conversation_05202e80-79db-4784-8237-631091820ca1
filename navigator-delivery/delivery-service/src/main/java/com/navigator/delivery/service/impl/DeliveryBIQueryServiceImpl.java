package com.navigator.delivery.service.impl;

import com.navigator.delivery.dao.DeliveryApplyDao;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import com.navigator.delivery.service.IDeliveryBIQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 提货申请表 查询服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryBIQueryServiceImpl implements IDeliveryBIQueryService {
    private final DeliveryApplyDao deliveryApplyDao;

    @Override
    public List<ExchangePickQtyDTO> getExchangePickQty(ExchangePickQtyQO pickQtyQO) {
        return deliveryApplyDao.getExchangePickQty(pickQtyQO);
    }

    @Override
    public List<ExecutePreAllocationDTO> getExecutePreAllocation(ExecutePreAllocationQO preAllocationQO) {
        return deliveryApplyDao.getExecutePreAllocation(preAllocationQO);
    }
}
