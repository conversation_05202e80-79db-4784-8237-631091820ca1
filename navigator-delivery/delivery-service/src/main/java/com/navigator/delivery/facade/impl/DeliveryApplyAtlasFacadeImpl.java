package com.navigator.delivery.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.delivery.facade.DeliveryApplyAtlasFacade;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractAtlasQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyAssignContractVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import com.navigator.delivery.service.IDeliveryApplyAtlasQueryService;
import com.navigator.delivery.service.IDeliveryApplyAtlasService;
import com.navigator.delivery.service.IDeliveryApplyQueryService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 提货委托-atlas
 *
 * <AUTHOR>
 */
@RestController
public class DeliveryApplyAtlasFacadeImpl implements DeliveryApplyAtlasFacade {

    @Resource
    private IDeliveryApplyAtlasQueryService deliveryApplyAtlasQueryService;
    @Resource
    private IDeliveryApplyAtlasService deliveryApplyAtlasService;
    @Resource
    private IDeliveryApplyQueryService deliveryApplyQueryService;

    @Override
    public Result<List<DeliveryApplyContractAtlasVO>> getDeliveryApplyContractList(DeliveryApplyContractAtlasQO deliveryApplyContractAtlasQO) {
        return Result.success(deliveryApplyAtlasQueryService.getDeliveryApplyContractList(deliveryApplyContractAtlasQO));
    }

    @Override
    public Result<PreCheckDeliveryApplyDTO> preCheckDeliveryApply(PreCheckDeliveryApplyQO preCheckDeliveryApplyQO) {
        return Result.success(deliveryApplyAtlasQueryService.preCheckDeliveryApply(preCheckDeliveryApplyQO));
    }

    @Override
    public Result<String> saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        return Result.success(deliveryApplyAtlasService.saveOrSubmitDeliveryApply(deliveryApplyDTO));
    }

    @Override
    public Result<String> updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        return Result.success(deliveryApplyAtlasService.updateOrSubmitDeliveryApply(deliveryApplyDTO));
    }

    @Override
    public Result<Boolean> submitDeliveryApplyById(Integer applyId, Integer triggerSys) {
        return Result.success(deliveryApplyAtlasService.submitDeliveryApplyById(applyId, triggerSys));
    }

    @Override
    public Result<String> batchSubmitDeliveryApply(List<Integer> applyIds) {
        return Result.success(deliveryApplyAtlasService.batchSubmitDeliveryApply(applyIds));
    }

    @Override
    public Result<Boolean> applyInvalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO) {
        return Result.success(deliveryApplyAtlasService.invalidDeliveryApply(applyInvalidDTO));
    }

    @Override
    public Result<String> deliveryApplyImportTemplate(Integer triggerSys) {
        return Result.success(deliveryApplyAtlasQueryService.getDeliveryApplyImportTemplate(triggerSys));
    }

    @Override
    public Result<DeliveryApplyAtlasImportResultDTO> deliveryApplyImportFile(MultipartFile file, Integer customerId, Integer triggerSys) {
        return Result.success(deliveryApplyAtlasService.deliveryApplyImportFile(file, customerId, triggerSys));
    }

    @Override
    public Result deliveryApplyImportSubmit(List<DeliveryApplyAtlasImportDTO> deliveryApplyAtlasImportDTOList, Integer triggerSys) {
        return Result.success(deliveryApplyAtlasService.deliveryApplyImportSubmit(deliveryApplyAtlasImportDTOList, triggerSys));
    }

    @Override
    public Result<DeliveryApplyAssignContractVO> getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO) {
        return Result.success(deliveryApplyAtlasQueryService.getReAssignContractList(assignContractDTO));
    }

    @Override
    public Result sendInMailMessage(Integer applyId) {
        deliveryApplyAtlasService.sendInMailMessage(applyId);
        return Result.success();
    }

    @Override
    public Result<Boolean> updateDeliveryApply(DeliveryApplyEntity deliveryApplyEntity) {
        return Result.success(deliveryApplyAtlasService.updateDeliveryApply(deliveryApplyEntity));
    }

    @Override
    public Result<DeliveryApplyEntity> getDeliveryApplyByCode(String applyCode) {
        return Result.success(deliveryApplyQueryService.getDeliveryApplyByCode(applyCode));
    }

    @Override
    public Result<Boolean> saveDeliveryApplyAckContract(DeliveryAckContractDTO deliveryAckContractDTO) {
        return Result.success(deliveryApplyAtlasService.saveDeliveryApplyAckContract(deliveryAckContractDTO));
    }

    @Override
    public Result getDeliveryApplyById(Integer applyId) {
        return Result.success(deliveryApplyQueryService.getDeliveryApplyById(applyId));
    }

    @Override
    public Result<DeliveryApplyCarpoolInfoDTO> getCarpoolDeliveryRequestInfo(String plateNumber, String planDeliveryTime) {
        return Result.success(deliveryApplyAtlasQueryService.getCarpoolDeliveryRequestInfo(plateNumber, planDeliveryTime));
    }
}
