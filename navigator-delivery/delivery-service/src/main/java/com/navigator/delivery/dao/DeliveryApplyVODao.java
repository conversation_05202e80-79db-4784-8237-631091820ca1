package com.navigator.delivery.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.delivery.mapper.DeliveryApplyVOMapper;
import com.navigator.delivery.pojo.entity.BaseDeliveryApplyEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;

import java.util.List;

/**
 * <p>
 * 提货申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Dao
public class DeliveryApplyVODao extends BaseDaoImpl<DeliveryApplyVOMapper, DeliveryApplyVOEntity> {

    public IPage<DeliveryApplyVOEntity> getDeliveryApplyList(QueryDTO<DeliveryApplyQO> queryDTO) {

        LambdaQueryWrapper<DeliveryApplyVOEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (null != queryDTO && null != queryDTO.getCondition()) {
            DeliveryApplyQO queryCondition = queryDTO.getCondition();

            // atlas
            if (SystemEnum.ATLAS.getValue().equals(queryCondition.getThirdSys())) {
                // 提货委托-新录入只展示CLB和MGL分别保存的提货委托申请，提交后不区分触发系统
                if (queryCondition.getApplyStatus() == null || DeliveryApplyStatusEnum.NEW.getValue() != queryCondition.getApplyStatus()) {
                    queryCondition.setTriggerSys(null);
                }
            } else {
                // lkg
                queryCondition.setTriggerSys(null);
            }

            // 根据业务编号查询合同号或申请单号
            if (StrUtil.isNotBlank(queryCondition.getBusinessCode())) {
                queryWrapper.and(wrapper -> wrapper.like(DeliveryApplyVOEntity::getContractCode, queryCondition.getBusinessCode().trim())
                        .or().like(DeliveryApplyVOEntity::getCode, queryCondition.getBusinessCode().trim()));
            }
            queryWrapper
                    // 1002481 case-提货功能优化-下载申请单优化 Author: Mr 2024-05-13 Start
                    // applyIds
                    .in(CollectionUtil.isNotEmpty(queryCondition.getApplyIds()), DeliveryApplyVOEntity::getId, queryCondition.getApplyIds())
                    // 1002481 case-提货功能优化-下载申请单优化 Author: Mr 2024-05-13 End
                    // 买方主体
                    .eq(null != queryCondition.getCustomerId(), DeliveryApplyVOEntity::getCustomerId, queryCondition.getCustomerId())
                    // 卖方主体
                    .eq(null != queryCondition.getSupplierId(), DeliveryApplyVOEntity::getSupplierId, queryCondition.getSupplierId())
                    // 提货工厂
                    .in(CollectionUtil.isNotEmpty(queryCondition.getDeliveryFactoryCodeList()), DeliveryApplyVOEntity::getDeliveryFactoryCode, queryCondition.getDeliveryFactoryCodeList())
                    // 提货状态
                    .eq(null != queryCondition.getBillStatus(), DeliveryApplyVOEntity::getBillStatus, queryCondition.getBillStatus())
                    // 申请单状态
                    .eq(null != queryCondition.getApplyStatus(), DeliveryApplyVOEntity::getApplyStatus, queryCondition.getApplyStatus())
                    // 车/船牌号
                    .like(StrUtil.isNotBlank(queryCondition.getPlateNumber()), DeliveryApplyVOEntity::getPlateNumber, StrUtil.isNotBlank(queryCondition.getPlateNumber()) ? queryCondition.getPlateNumber().trim() : null)
                    // 是否拼车船
                    .eq(null != queryCondition.getIsCarpool(), DeliveryApplyVOEntity::getIsCarpool, queryCondition.getIsCarpool())
                    // 商品id
                    .eq(null != queryCondition.getGoodsId(), DeliveryApplyVOEntity::getGoodsId, queryCondition.getGoodsId())
                    // 品类id
                    .eq(null != queryCondition.getGoodsCategoryId(), DeliveryApplyVOEntity::getGoodsCategoryId, queryCondition.getGoodsCategoryId())
                    // 创建开始时间
                    .ge(StrUtil.isNotBlank(queryCondition.getCreateStartTime()), DeliveryApplyVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(queryCondition.getCreateStartTime()))
                    // 创建结束时间
                    .le(StrUtil.isNotBlank(queryCondition.getCreateEndTime()), DeliveryApplyVOEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(queryCondition.getCreateEndTime()))
                    // 申请开始时间
                    .ge(StrUtil.isNotBlank(queryCondition.getApplyStartTime()), DeliveryApplyVOEntity::getApplyAt, DateTimeUtil.parseTimeStamp0000(queryCondition.getApplyStartTime()))
                    // 申请结束时间
                    .le(StrUtil.isNotBlank(queryCondition.getApplyEndTime()), DeliveryApplyVOEntity::getApplyAt, DateTimeUtil.parseTimeStamp2359(queryCondition.getApplyEndTime()))
                    // LDC库/外库
                    .eq(null != queryCondition.getWarehouseType(), DeliveryApplyVOEntity::getWarehouseType, queryCondition.getWarehouseType())
                    // 计划提货日期
                    .between(StrUtil.isNotBlank(queryCondition.getPlanDeliveryTime()), DeliveryApplyVOEntity::getPlanDeliveryTime,
                            StrUtil.isNotBlank(queryCondition.getPlanDeliveryTime()) ? DateTimeUtil.parseTimeStamp0000(queryCondition.getPlanDeliveryTime().split(" ")[0]) : "", StrUtil.isNotBlank(queryCondition.getPlanDeliveryTime()) ? DateTimeUtil.parseTimeStamp2359(queryCondition.getPlanDeliveryTime().split(" ")[0]) : "")
                    // 提货库点
                    .eq(null != queryCondition.getDeliveryWarehouseId(), DeliveryApplyVOEntity::getDeliveryWarehouseId, queryCondition.getDeliveryWarehouseId())
                    // 申请人
                    .like(StrUtil.isNotBlank(queryCondition.getCustomerName()), DeliveryApplyVOEntity::getCreatedBy, queryCondition.getCustomerName())
                    // magellan-lkg：不显示新录入的申请单
                    .ne(SystemEnum.MAGELLAN.getValue().equals(queryCondition.getTriggerSys()) && SystemEnum.LKG.getValue().equals(queryCondition.getThirdSys()),
                            DeliveryApplyVOEntity::getApplyStatus, DeliveryApplyStatusEnum.NEW.getValue())
                    // 锁定原因
                    .like(StringUtil.isNotNullBlank(queryCondition.getAtlasSubStatus()), BaseDeliveryApplyEntity::getAtlasSubStatus, queryCondition.getAtlasSubStatus())
                    // 操作方
                    .eq(queryCondition.getOperatorSys() != null, BaseDeliveryApplyEntity::getTriggerSys, queryCondition.getOperatorSys())
                    // 触发系统
                    .eq(queryCondition.getTriggerSys() != null, BaseDeliveryApplyEntity::getTriggerSys, queryCondition.getTriggerSys())
                    // 第三方系统
                    .eq(queryCondition.getThirdSys() != null, BaseDeliveryApplyEntity::getThirdSys, queryCondition.getThirdSys())

            ;

            // 按照不同的状态进行排序
            if (null == queryCondition.getApplyStatus() || queryCondition.getApplyStatus().equals(DeliveryApplyStatusEnum.NEW.getValue())) {
                queryWrapper.orderByDesc(DeliveryApplyVOEntity::getCreatedAt);
            } else {
                queryWrapper.orderByDesc(DeliveryApplyVOEntity::getUpdatedAt);
            }
        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<DeliveryApplyVOEntity> getDeliveryApplyListByIds(List<Integer> applyIds) {
        LambdaQueryWrapper<DeliveryApplyVOEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtil.isNotEmpty(applyIds), DeliveryApplyVOEntity::getId, applyIds);
        return this.baseMapper.selectList(queryWrapper);
    }
}
