package com.navigator.delivery.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.delivery.mapper.DeliveryApplyContractMapper;
import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <p>
 * 提货申请合同 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Dao
public class DeliveryApplyContractDao extends BaseDaoImpl<DeliveryApplyContractMapper, DeliveryApplyContractEntity> {

    public List<DeliveryApplyContractEntity> getByApplyId(Integer applyId) {
        return this.lambdaQuery()
                .eq(DeliveryApplyContractEntity::getApplyId, applyId)
                .eq(DeliveryApplyContractEntity::getIsDeleted, 0)
                .list();
    }

    /**
     * 按申请单ID删除
     *
     * @param applyId
     */
    public void removeByApplyId(Integer applyId) {
        this.lambdaUpdate()
                .eq(DeliveryApplyContractEntity::getApplyId, applyId)
                .remove();
    }

    public DeliveryApplyContractEntity getByApplyIdAndContractId(Integer applyId, Integer contractId) {
        return this.lambdaQuery()
                .eq(DeliveryApplyContractEntity::getApplyId, applyId)
                .eq(DeliveryApplyContractEntity::getContractId, contractId)
                .one();
    }

    public List<DeliveryApplyContractEntity> getByContractId(Integer contractId) {
        return this.lambdaQuery()
                .eq(DeliveryApplyContractEntity::getContractId, contractId)
                .list();
    }

    public List<DeliveryApplyContractEntity> getBySubApplyCodeOrApplyId(String applyCode, Integer deliveryId) {
        if (StringUtils.isNotBlank(applyCode) && applyCode.contains("-")) {
            return this.lambdaQuery()
                    .eq(DeliveryApplyContractEntity::getApplyCode, applyCode)
                    .eq(DeliveryApplyContractEntity::getIsDeleted, 0)
                    .list();
        } else {
            return getByApplyId(deliveryId);
        }
    }

    public DeliveryApplyContractEntity getByApplyCodeAndContractId(String applyCode, Integer contractId) {
        List<DeliveryApplyContractEntity> applyContractEntityList = this.lambdaQuery()
                .eq(DeliveryApplyContractEntity::getApplyCode, applyCode)
                .eq(DeliveryApplyContractEntity::getContractId, contractId)
                .eq(DeliveryApplyContractEntity::getIsDeleted, 0)
                .list();
        return applyContractEntityList.isEmpty() ? null : applyContractEntityList.get(0);
    }


}
