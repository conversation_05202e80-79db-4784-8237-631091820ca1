package com.navigator.delivery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.delivery.pojo.dto.DeliveryApplyAllocateDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;

import java.util.List;

/**
 * <p>
 * 提货申请预分配 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
public interface IDeliveryApplyAllocateService extends IService<DeliveryApplyAllocateEntity> {
    /**
     * 根据条件：获取提货申请预分配列表
     *
     * @param condition
     * @return
     */
    List<DeliveryApplyAllocateDTO> queryDeliveryApplyAllocateList(DeliveryApplyAllocateQO condition);

    /**
     * 根据ID：获取提货申请预分配
     *
     * @param id
     * @return
     */
    DeliveryApplyAllocateEntity getDeliveryApplyAllocateById(Integer id);

    /**
     * 根据申请信息：获取提货申请预分配列表
     *
     * @param deliveryApplyEntity 申请信息
     * @return
     */
    List<DeliveryApplyAllocateEntity> getDeliveryRequestAllocateInfo(DeliveryApplyEntity deliveryApplyEntity);

    /**
     * 根据申请ID：获取提货申请预分配列表
     *
     * @param applyId 申请ID
     * @return
     */
    List<DeliveryApplyAllocateEntity> getDeliveryApplyAllocateByApplyId(Integer applyId);
}
