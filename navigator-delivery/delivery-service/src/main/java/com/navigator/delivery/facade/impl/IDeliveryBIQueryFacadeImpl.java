package com.navigator.delivery.facade.impl;

import com.navigator.delivery.facade.IDeliveryBIQueryFacade;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import com.navigator.delivery.service.IDeliveryBIQueryService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 提货BI 查询服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Api(tags = "提货BI查询")
@RestController
@RequiredArgsConstructor
public class IDeliveryBIQueryFacadeImpl implements IDeliveryBIQueryFacade {

    @Resource
    private IDeliveryBIQueryService iDeliveryBIQueryService;

    /**
     * 查询仓单的可提货量（BI存储过程）
     * @param pickQtyQO
     * @return
     */
    @Override
    public List<ExchangePickQtyDTO> getExchangePickQty(ExchangePickQtyQO pickQtyQO){
        return iDeliveryBIQueryService.getExchangePickQty(pickQtyQO);
    }

    /**
     * 查询仓单的可提货量（BI存储过程）
     * @param preAllocationQO
     * @return
     */
    @Override
    public List<ExecutePreAllocationDTO> getExecutePreAllocation(ExecutePreAllocationQO preAllocationQO){
        return iDeliveryBIQueryService.getExecutePreAllocation(preAllocationQO);
    }

}
