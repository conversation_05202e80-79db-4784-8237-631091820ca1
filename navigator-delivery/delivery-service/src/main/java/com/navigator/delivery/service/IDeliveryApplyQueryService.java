package com.navigator.delivery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyAssignContractDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyDriverLogDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyRpaExportDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;

import java.util.List;

/**
 * <p>
 * 提货申请表 查询服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
public interface IDeliveryApplyQueryService {

    // 申请提货列表-合同的信息汇总
    List<DeliveryApplyContractVO> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO);

    // 分页查询申请提货列表
    IPage<DeliveryApplyVOEntity> getDeliveryApplyList(QueryDTO<DeliveryApplyQO> queryDTO);

    // 申请提货详情
    DeliveryApplyDetailVO getDeliveryApplyDetailById(Integer applyId, String triggerSys);

    // 根据申请单id查看申请单附件
    List<DeliveryApplyFileVO> getFileListByApplyId(Integer applyId);

    // 根据申请单id查询申请审核记录
    List<DeliveryApplyApprovalVO> getAuditRecordListByApplyId(Integer applyId);

    // 根据申请单id查询操作记录
    List<DeliveryApplyOperationVO> getOperateRecordListByApplyCode(String applyCode);

    // 查询分配合同列表
    DeliveryApplyAssignContractVO getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO);

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    // 查询司机输入记录
    List<DeliveryApplyDriverLogEntity> getDriverInputRecord(Integer customerId, Integer goodsCategoryId);

    List<DeliveryApplyDriverLogEntity> getDriverRecordByCondition(DeliveryApplyDriverLogDTO deliveryApplyDriverLogDTO);
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    List<DeliveryApplyRpaExportDTO> getRpaDeliveryApplyList();

    // 根据申请单id查询申请单信息
    List<DeliveryApplyVOEntity> getDeliveryApplyListByIds(List<Integer> applyIds);

    // 根据申请单id查询申请单信息
    DeliveryApplyEntity getDeliveryApplyById(Integer applyId);

    // 根据申请单号查询申请单信息
    DeliveryApplyEntity getDeliveryApplyByCode(String applyCode);

    // 根据申请单id查询申请单信息
    Boolean isContractInDeliveryAudit(Integer contractId);
}
