package com.navigator.delivery;

import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = "com.navigator")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
public class DeliveryNavigatorApplication {
    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }

    public static void main(String[] args) {
        SpringApplication.run(DeliveryNavigatorApplication.class, args);
    }
}
