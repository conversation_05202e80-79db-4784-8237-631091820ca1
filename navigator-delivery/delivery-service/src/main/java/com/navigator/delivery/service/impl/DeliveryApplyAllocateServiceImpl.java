package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.delivery.dao.DeliveryApplyAllocateDao;
import com.navigator.delivery.mapper.DeliveryApplyAllocateMapper;
import com.navigator.delivery.pojo.dto.DeliveryApplyAllocateDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.ContractCategoryTypeEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import com.navigator.delivery.service.IDeliveryApplyAllocateService;
import com.navigator.delivery.service.IDeliveryBIQueryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 提货申请预分配 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Service

public class DeliveryApplyAllocateServiceImpl extends ServiceImpl<DeliveryApplyAllocateMapper, DeliveryApplyAllocateEntity> implements IDeliveryApplyAllocateService {

    @Resource
    private DeliveryApplyAllocateDao deliveryApplyAllocateDao;
    @Resource
    private IDeliveryBIQueryService biQueryService;
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public List<DeliveryApplyAllocateDTO> queryDeliveryApplyAllocateList(DeliveryApplyAllocateQO condition) {
        List<DeliveryApplyAllocateDTO> dtoList = new ArrayList<>();
        List<DeliveryApplyAllocateEntity> entityList = deliveryApplyAllocateDao.queryDeliveryApplyAllocateList(condition);
        for (DeliveryApplyAllocateEntity entity : entityList) {
            DeliveryApplyAllocateDTO dto = BeanUtil.copyProperties(entity, DeliveryApplyAllocateDTO.class);
            if (dto.getCustomerId() != null) {
                CustomerEntity customerEntity = customerFacade.queryCustomerById(dto.getCustomerId());
                dto.setCustomerName(customerEntity != null ? customerEntity.getName() : null);
            }
            if (dto.getSupplierId() != null) {
                CustomerEntity customerEntity = customerFacade.queryCustomerById(dto.getSupplierId());
                dto.setSupplierName(customerEntity != null ? customerEntity.getName() : null);
            }
            // 业务类型
            if (entity.getIsSoybean2() != null && entity.getIsSoybean2() == 1) {
                dto.setContractCategoryType(ContractCategoryTypeEnum.SOYBEAN2.getValue());
            } else if (entity.getIsExchangeDelivery() != null && entity.getIsExchangeDelivery() == 1) {
                dto.setContractCategoryType(ContractCategoryTypeEnum.DCE.getValue());
            } else {
                dto.setContractCategoryType(ContractCategoryTypeEnum.REGULAR.getValue());
            }
            dtoList.add(dto);
        }
        IdNameConverter.toName(IdNameType.sku_id_name, dtoList);
        return dtoList;
    }

    @Override
    public DeliveryApplyAllocateEntity getDeliveryApplyAllocateById(Integer id) {
        return deliveryApplyAllocateDao.getDeliveryApplyAllocateById(id);
    }

    @Override
    public List<DeliveryApplyAllocateEntity> getDeliveryRequestAllocateInfo(DeliveryApplyEntity deliveryApplyEntity) {

        List<DeliveryApplyAllocateEntity> allocateEntityList = new ArrayList<>();

        // 获取主体id
        Integer companyId = 1;
        Integer supplierId = deliveryApplyEntity.getSupplierId();
        CustomerEntity customerEntity = customerFacade.queryCustomerById(supplierId);
        if (customerEntity != null) {
            companyId = customerEntity.getCompanyId();
        }

        // 先查询BI库预分配的信息
        ExecutePreAllocationQO executePreAllocationQO = new ExecutePreAllocationQO();
        executePreAllocationQO
                .setDeliveryFactoryCode(deliveryApplyEntity.getDeliveryFactoryCode())
                .setGoodsId(deliveryApplyEntity.getGoodsId())
                .setCustomerId(deliveryApplyEntity.getCustomerId())
                .setCompanyId(companyId)
                .setSystem(deliveryApplyEntity.getTriggerSys())
                .setDeliveryType(Integer.valueOf(deliveryApplyEntity.getDeliveryType()))
                .setWarehouseId(deliveryApplyEntity.getDeliveryWarehouseId())
                .setRequestQty(deliveryApplyEntity.getApplyNum());

        List<ExecutePreAllocationDTO> allocationDTOList;
        try {
            allocationDTOList = biQueryService.getExecutePreAllocation(executePreAllocationQO);
        } catch (Exception e) {
            log.error("获取预分配信息失败", e);
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_PRE_ALLOCATE_EXCEPTION);
        }

        // 存储过程 → 保存到预分配信息表中
        if (!allocationDTOList.isEmpty()) {
            // 删除原有预分配信息
            deliveryApplyAllocateDao.deleteByApplyId(deliveryApplyEntity.getId());

            for (int i = 0; i < allocationDTOList.size(); i++) {
                ExecutePreAllocationDTO allocationDTO = allocationDTOList.get(i);
                DeliveryApplyAllocateEntity deliveryApplyAllocateEntity = new DeliveryApplyAllocateEntity();
                deliveryApplyAllocateEntity.setApplyId(deliveryApplyEntity.getId())
                        .setApplyCode(deliveryApplyEntity.getCode())
                        .setGoodsId(allocationDTO.getGoodsId())
                        .setCustomerId(deliveryApplyEntity.getCustomerId())
                        .setSupplierId(deliveryApplyEntity.getSupplierId())
                        .setWarrantNumber(allocationDTO.getWarrantNumber())
                        .setIsExchangeDelivery(allocationDTO.getIsExchangeDelivery())
                        .setIsSoybean2(allocationDTO.getIsSoybean2())
                        .setAllocationQty(allocationDTO.getAllocationQty())
                        .setDceContractNo(allocationDTO.getContractCode())
                        .setTakeType(allocationDTO.getTakeType())
                        .setTakeCustomerId(allocationDTO.getTakeCustomer())
                        .setCpid(allocationDTO.getCPID())
                        .setIsDeleted(0)
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date());

                // 生成子编号 格式：申请编号-001
                if (allocationDTOList.size() > 1) {
                    deliveryApplyAllocateEntity.setSubApplyCode(deliveryApplyAllocateEntity.getApplyCode() + "-" + String.format("%03d", i + 1));
                }

                deliveryApplyAllocateDao.save(deliveryApplyAllocateEntity);

                allocateEntityList.add(deliveryApplyAllocateEntity);
            }
        }

        return allocateEntityList;
    }

    @Override
    public List<DeliveryApplyAllocateEntity> getDeliveryApplyAllocateByApplyId(Integer applyId) {
        return deliveryApplyAllocateDao.getDeliveryApplyAllocateByApplyId(applyId);
    }
}
