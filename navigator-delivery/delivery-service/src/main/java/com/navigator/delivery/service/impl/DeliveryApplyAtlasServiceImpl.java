package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.constant.DeliveryConstant;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.customer.facade.CustomerDeliveryWhiteFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.qo.CustomerQO;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.delivery.dao.DeliveryApplyContractDao;
import com.navigator.delivery.dao.DeliveryApplyDao;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryBillStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryQueueStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryTransportWay;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import com.navigator.delivery.service.*;
import com.navigator.delivery.service.sync.AtlasSyncService;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.enums.MdmType;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 提货委托-atlas
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryApplyAtlasServiceImpl implements IDeliveryApplyAtlasService {

    // Dao
    private final DeliveryApplyDao deliveryApplyDao;
    private final DeliveryApplyContractDao deliveryApplyContractDao;
    // facade
    private final EmployFacade employFacade;
    private final CEmployFacade cEmployFacade;
    private final CustomerFacade customerFacade;
    private final ContractFacade contractFacade;
    private final FileBusinessFacade fileBusinessFacade;
    private final SkuFacade skuFacade;
    private final WarehouseFacade warehouseFacade;
    private final FactoryFacade factoryFacade;
    private final SystemRuleFacade systemRuleFacade;
    private final AtlasContractFacade atlasContractFacade;
    private final CustomerDeliveryWhiteFacade customerDeliveryWhiteFacade;
    private final SiteFacade siteFacade;
    // service
    private final DeliveryAsyncExecuteService deliveryAsyncExecute;
    private final AtlasSyncService atlasSyncService;
    private final IDeliveryApplyService deliveryApplyService;
    private final IDeliveryApplyAllocateService deliveryApplyAllocateService;
    private final IDeliveryBIQueryService deliveryBIQueryService;
    private final IDeliveryApplyAtlasQueryService deliveryApplyAtlasQueryService;
    private final RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        DeliveryApplyEntity deliveryApply = BeanUtil.toBean(deliveryApplyDTO, DeliveryApplyEntity.class);
        deliveryApply.setCode(deliveryApplyService.genDeliveryApplyCode(deliveryApplyDTO.getGoodsCategoryId(), deliveryApplyDTO.getSupplierId(), deliveryApply.getDeliveryFactoryCode()));
        deliveryApply.setBillStatus(DeliveryBillStatusEnum.NOT_BILLED.getValue());
        deliveryApply.setThirdSys(SystemEnum.ATLAS.getValue());
        String employName = this.getEmployName(deliveryApplyDTO.getTriggerSys());
        deliveryApply.setCreatedBy(employName);
        deliveryApply.setUpdatedBy(employName);
        LogBizCodeEnum bizCodeEnum;
        if (deliveryApplyDTO.getSubmitType() == 1) {
            // 保存
            bizCodeEnum = LogBizCodeEnum.SAVE_DELIVERY_APPLY;
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.NEW.getValue());
            deliveryApplyDao.save(deliveryApply);
            // 保存分配合同
            saveApplyContract(deliveryApply);
        } else {
            // 是否同步atlas
            boolean syncAtlas = getSyncAtlasStatus(deliveryApply);

            // 提交
            bizCodeEnum = LogBizCodeEnum.SUBMIT_DELIVERY_APPLY;
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
            deliveryApply.setApplyAt(new Date());
            deliveryApplyDao.save(deliveryApply);
            // 校验申请单
            checkDeliveryApply(syncAtlas, deliveryApply);
            // 保存分配合同
            saveApplyContract(deliveryApply);
            // 保存司机信息
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);

            // ATLAS 接口同步
            if (syncAtlas) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        atlasSyncService.syncDeliveryInfo(deliveryApply, deliveryApply.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST.getValue());
                    }
                });
            }
        }
        // 保存附件
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(deliveryApply.getId())
                .setCategoryType(FileCategoryType.DELIVERY_APPLY.getCode())
                .setModuleType(ModuleTypeEnum.DELIVERY.getModule())
                .setFileIdList(deliveryApplyDTO.getFileIdList()));
        // 保存操作记录
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply,
                JSON.toJSONString(deliveryApplyDTO), JSON.toJSONString(deliveryApply.getDeliveryApplyContractList()),
                bizCodeEnum, SystemEnum.getByValue(deliveryApplyDTO.getTriggerSys()),
                Integer.valueOf(JwtUtils.getCurrentUserId()));
        return deliveryApply.getCode();
    }

    @Override
    public String updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        String employName = this.getEmployName(deliveryApplyDTO.getTriggerSys());
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
        DeliveryApplyEntity deliveryApply = BeanUtil.toBean(deliveryApplyDTO, DeliveryApplyEntity.class);
        deliveryApply.setThirdSys(SystemEnum.ATLAS.getValue());
        deliveryApply.setId(deliveryApplyDTO.getApplyId())
                .setUpdatedAt(new Date())
                .setUpdatedBy(employName);

        // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-03 Start
        // 状态判断:已经提交的申请单不能再提交,现系统提交完变成待审核状态（以数据库的状态为准）
        if (null != deliveryApplyDTO.getApplyId()) {
            DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(deliveryApplyDTO.getApplyId());
            if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_STATUS_ERROR);
            }
        }
        // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-03 End

        // 保存分配合同
        saveApplyContract(deliveryApply);

        // 判断是保存还是提交
        if (deliveryApplyDTO.getSubmitType() == 2) {
            // 是否同步atlas
            boolean syncAtlas = getSyncAtlasStatus(deliveryApply);
            // 校验提交数据
            checkDeliveryApply(syncAtlas, deliveryApply);
            // 新录入 → 待审核|| 审核驳回 → 待审核
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
            // 申请时间
            deliveryApply.setApplyAt(new Date());
            deliveryApplyDao.updateById(deliveryApply);
            // 保存司机信息
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
            // 接口同步
            if (syncAtlas) {
                atlasSyncService.syncDeliveryInfo(deliveryApply, deliveryApply.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST.getValue());
            }
        }

        // 更新附件信息
        fileBusinessFacade.dropFileRelation(deliveryApply.getId(), FileCategoryType.DELIVERY_APPLY.getCode(), "更新提货申请附件信息");
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(deliveryApply.getId())
                .setCategoryType(FileCategoryType.DELIVERY_APPLY.getCode())
                .setModuleType(ModuleTypeEnum.DELIVERY.getModule())
                .setFileIdList(deliveryApplyDTO.getFileIdList()));

        // 记录日志信息
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply,
                JSON.toJSONString(deliveryApplyDTO), null,
                LogBizCodeEnum.UPDATE_DELIVERY_APPLY, SystemEnum.getByValue(deliveryApplyDTO.getTriggerSys()),
                Integer.valueOf(JwtUtils.getCurrentUserId()));
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
        deliveryApplyDao.updateById(deliveryApply);
        return deliveryApply.getCode();
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
    }

    @Override
    public boolean submitDeliveryApplyById(Integer applyId, Integer triggerSys) {
        DeliveryApplyEntity deliveryApply = deliveryApplyDao.getById(applyId);
        // 是否同步atlas
        boolean syncAtlas = getSyncAtlasStatus(deliveryApply);
        // 校验申请单
        List<DeliveryApplyContractEntity> contractEntityList = deliveryApplyContractDao.getByApplyId(applyId);
        deliveryApply.setDeliveryApplyContractList(contractEntityList);
        checkDeliveryApply(syncAtlas, deliveryApply);
        // 保存分配合同
        saveApplyContract(deliveryApply);
        // 判断是否已经提交
        if (deliveryApply.getApplyStatus() == DeliveryApplyStatusEnum.NEW.getValue()) {
            // 未提交
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
            // 申请时间
            deliveryApply.setApplyAt(new Date());
            // 保存司机信息
            DeliveryApplyDTO deliveryApplyDTO = BeanUtil.toBean(deliveryApply, DeliveryApplyDTO.class);
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
            // ATLAS 接口同步
            if (syncAtlas) {
                atlasSyncService.syncDeliveryInfo(deliveryApply, deliveryApply.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST.getValue());
            }
        } else {
            // 状态判断:已经提交的申请单不能再提交
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_STATUS_ERROR);
        }

        String employName = this.getEmployName(triggerSys);
        // 保存操作记录
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply, String.valueOf(applyId), JSON.toJSONString(deliveryApply),
                LogBizCodeEnum.SUBMIT_DELIVERY_APPLY, SystemEnum.getByValue(triggerSys),
                Integer.valueOf(JwtUtils.getCurrentUserId()));
        deliveryApply.setUpdatedAt(new Date())
                .setUpdatedBy(employName);
        return deliveryApplyDao.updateById(deliveryApply);
    }

    @Override
    public String batchSubmitDeliveryApply(List<Integer> applyIds) {
        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
        List<Integer> distinctIds = applyIds.stream()
                .distinct()
                .collect(Collectors.toList());
        // 从远到近进行提交
        Collections.reverse(distinctIds);

        // 批量提交 - 统计成功与失败的数量
        int successCount = 0;
        int failCount = 0;
        for (Integer applyId : distinctIds) {
            // 1002481 case-提货功能优化 Author: Mr 2024-04-13 End
            DeliveryApplyEntity deliveryApply = deliveryApplyDao.getById(applyId);
            try {
                // 是否同步atlas
                boolean syncAtlas = getSyncAtlasStatus(deliveryApply);
                // 校验申请单
                List<DeliveryApplyContractEntity> contractEntityList = deliveryApplyContractDao.getByApplyId(applyId);
                deliveryApply.setDeliveryApplyContractList(contractEntityList);
                checkDeliveryApply(syncAtlas, deliveryApply);
                // 保存分配合同
                saveApplyContract(deliveryApply);
                // 判断是否已经提交
                if (deliveryApply.getApplyStatus() == DeliveryApplyStatusEnum.NEW.getValue()) {
                    // 未提交
                    deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
                    // 申请时间
                    deliveryApply.setApplyAt(new Date());
                    deliveryApplyDao.updateById(deliveryApply);
                    // 保存司机信息
                    DeliveryApplyDTO deliveryApplyDTO = BeanUtil.toBean(deliveryApply, DeliveryApplyDTO.class);
                    deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
                    // 接口同步
                    if (syncAtlas) {
                        atlasSyncService.syncDeliveryInfo(deliveryApply, deliveryApply.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST.getValue());
                    }
                    successCount++;
                } else {
                    // 已提交
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量提交申请单异常：{}", e.getMessage(), e);
                failCount++;
            }
        }
        return "成功" + successCount + "条，失败" + failCount + "条";
    }

    /**
     * 校验申请单
     *
     * @param syncAtlas     是否同步ATLAS
     * @param deliveryApply 申请单数据
     * @return
     */
    private void checkDeliveryApply(boolean syncAtlas, DeliveryApplyEntity deliveryApply) {
        // 判断申请单是否存在
        if (null == deliveryApply) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NOT_EXIST);
        }
        // 判断申请数量
        if (BigDecimalUtil.isLessEqualThanZero(deliveryApply.getApplyNum())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
        }
        // BUGFIX：case-1003125 非必填项“核定干舷”不填无法提交DR Author: Mr 2025-04-14 Start
        // 运输方式
        if (DeliveryTransportWay.TRUCK.getValue().equals(deliveryApply.getTransportWay())) {
            // 车提或船提 车/船号、司机/船长姓名、司机/船长身份证号、随车/船电话、是否拼车/船
            if (StringUtil.isNullBlank(deliveryApply.getPlateNumber())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_PLATE_NUMBER_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getDriverName())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_NAME_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getDriverIdNumber())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getOnboardPhone())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_ONBOARD_PHONE_ERROR);
            }
            if (deliveryApply.getIsCarpool() == null) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_IS_SHARE_ERROR);
            }

            // 车辆类型
            if (StringUtil.isNullBlank(deliveryApply.getCarType())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_VEHICLE_TYPE_ERROR);
            }
        } else if (DeliveryTransportWay.SHIPPING.getValue().equals(deliveryApply.getTransportWay())) {
            // 车提或船提 车/船号、司机/船长姓名、司机/船长身份证号、随车/船电话、是否拼车/船
            if (StringUtil.isNullBlank(deliveryApply.getPlateNumber())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_PLATE_NUMBER_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getOnboardPhone())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_ONBOARD_PHONE_ERROR);
            }

            // 自提：车/船号、司机/船长姓名、司机/船长身份证号、随车/船电话、是否拼车/船
            if (ObjectUtil.equals(deliveryApply.getDeliveryType(), DeliveryModeEnum.TAKE.getValue())) {
                if (StringUtil.isNullBlank(deliveryApply.getDriverName())) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_NAME_ERROR);
                }
                if (StringUtil.isNullBlank(deliveryApply.getDriverIdNumber())) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR);
                }

                if (deliveryApply.getIsCarpool() == null) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_IS_SHARE_ERROR);
                }
            }

            // 船提核定干舷如果不为null,需要>0
            if (deliveryApply.getFreeboard() != null && deliveryApply.getFreeboard() <= 0) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRAFT_DEPTH_ERROR);
            }
            // BUGFIX：case-1003125 非必填项“核定干舷”不填无法提交DR Author: Mr 2025-04-14 End
        } else if (DeliveryTransportWay.TRAIN.getValue().equals(deliveryApply.getTransportWay())) {
            // 火车提 车站、车/船号、车辆类型、司机/船长姓名、司机/船长身份证号、随车/船电话
            if (StringUtil.isNullBlank(deliveryApply.getStation())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_STATION_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getPlateNumber())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_PLATE_NUMBER_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getCarType())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_VEHICLE_TYPE_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getDriverName())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_NAME_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getDriverIdNumber())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR);
            }
            if (StringUtil.isNullBlank(deliveryApply.getOnboardPhone())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_ONBOARD_PHONE_ERROR);
            }
        } else if (DeliveryTransportWay.TRANSFER.getValue().equals(deliveryApply.getTransportWay())) {
            // 转货权
        }

        // BUGFIX：case-1003186 航海家身份证号和电话号的传值问题 Author: Mr 2025-05-07 start
        // 身份证和联系方式格式校验
        if (StringUtil.isNotNullBlank(deliveryApply.getDriverIdNumber()) &&
                !deliveryApply.getDriverIdNumber().matches("^[1-9]\\d{5}(19\\d{2}|20\\d{2})\\d{2}\\d{2}\\d{3}[0-9X]$")) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR);
        }

        // 手机号需加入长度、空格、特殊字符校验
        if (StringUtil.isNotNullBlank(deliveryApply.getOnboardPhone()) &&
                !deliveryApply.getOnboardPhone().matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_ONBOARD_PHONE_ERROR);
        }
         // BUGFIX：case-1003186 航海家身份证号和电话号的传值问题 Author: Mr 2025-05-07 end

        // LDC库/外库
        if (deliveryApply.getWarehouseType() == null || deliveryApply.getWarehouseType() == 0) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_WAREHOUSE_TYPE_ERROR);
        }

        // 提货库点
        Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(deliveryApply.getDeliveryWarehouseId());
        if (result.isSuccess()) {
            WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
            if (null == warehouseEntity || Objects.equals(warehouseEntity.getStatus(), DisableStatusEnum.DISABLE.getValue())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DELIVERY_WAREHOUSE_ERROR);
            }
        }

        // 是否拼车/船
        if (deliveryApply.getIsCarpool() == null) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_IS_SHARE_ERROR);
        }

        // 检查拼车信息
        deliveryApplyDao.checkCarpool(deliveryApply, true);

        // 检查是否分配合同
        if (CollUtil.isEmpty(deliveryApply.getDeliveryApplyContractList())) {
            // 查询客户是否需要分配合同
            PreCheckDeliveryApplyQO preCheckDeliveryApplyQO = new PreCheckDeliveryApplyQO();
            preCheckDeliveryApplyQO.setGoodsId(deliveryApply.getGoodsId());
            preCheckDeliveryApplyQO.setDeliveryFactoryCode(deliveryApply.getDeliveryFactoryCode());
            preCheckDeliveryApplyQO.setCustomerId(deliveryApply.getCustomerId());
            preCheckDeliveryApplyQO.setSupplierId(deliveryApply.getSupplierId());
            preCheckDeliveryApplyQO.setDeliveryType(Integer.parseInt(deliveryApply.getDeliveryType()));
            preCheckDeliveryApplyQO.setTriggerSys(deliveryApply.getTriggerSys());
            PreCheckDeliveryApplyDTO preCheckDeliveryApplyDTO = deliveryApplyAtlasQueryService.preCheckDeliveryApply(preCheckDeliveryApplyQO);
            if (preCheckDeliveryApplyDTO.getIsCanAssignContract() == 1) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ALLOCATE);
            }
        } else {
            BigDecimal applyNum = BigDecimal.ZERO;
            for (DeliveryApplyContractEntity item : deliveryApply.getDeliveryApplyContractList()) {
                applyNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, applyNum, item.getAllocateNum());
            }
            if (!BigDecimalUtil.isEqual(deliveryApply.getApplyNum(), applyNum)) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_NOT_MATCH_ALLOCATE);
            }
        }

        // 检查账套
        CustomerEntity supplier = customerFacade.getCustomerById(deliveryApply.getSupplierId());
        SiteEntity site = siteFacade.getSiteByCompanyIdAndFactoryCode(supplier.getCompanyId(), deliveryApply.getDeliveryFactoryCode());
        if (site == null) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_SITE_EXCEPTION);
        }

        // 校验申请数量是否超出可开单量
        if (syncAtlas) {
            try {
                List<DeliveryApplyAllocateEntity> deliveryRequestAllocateInfo = deliveryApplyAllocateService.getDeliveryRequestAllocateInfo(deliveryApply);

                // 校验可开单量
                deliveryRequestAllocateInfo
                        .forEach(entity -> {
                            // 获取客户和供应商信息
                            CustomerEntity customerEntity = customerFacade.queryCustomerById(entity.getCpid() != null ? entity.getCpid() : deliveryApply.getCustomerId());
                            CustomerEntity supplierEntity = customerFacade.getCustomerById(deliveryApply.getSupplierId());
                            String mdmCustomerCode = customerEntity.getMdmCustomerCode();
                            SiteEntity siteEntity = siteFacade.getSiteByCompanyIdAndFactoryCode(supplierEntity.getCompanyId(), deliveryApply.getDeliveryFactoryCode());
                            WarehouseEntity warehouseEntity = warehouseFacade.getWarehouseById(deliveryApply.getDeliveryWarehouseId()).getData();

                            // 查询SKU信息
                            Integer type = MdmType.SPOT.getValue();
                            // 判断是否是仓单
                            if (entity.getIsExchangeDelivery() == 1) {
                                type = MdmType.WARRANT.getValue();
                            }
                            // 判断是否是豆二
                            if (entity.getIsSoybean2() == 1) {
                                type = MdmType.SOYBEAN2.getValue();
                            }
                            Result<List<SkuMdmEntity>> skuMdmResult = skuFacade.querySkuMdmList(new SkuMdmQO().setSkuId(deliveryApply.getGoodsId()).setType(type));
                            String commodityCode = skuMdmResult.isSuccess() && CollUtil.isNotEmpty(skuMdmResult.getData())
                                    ? skuMdmResult.getData().get(0).getMdmId() : "";

                            // 准备查询ATLAS可开单量的请求
                            AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO = new AtlasDeliveryOpenQuantityDTO();
                            AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailDTO openQuantityDetailDTO = new AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailDTO();
                            openQuantityDetailDTO.setShareContractList("Y");
                            List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> parameterList = new ArrayList<>();
                            AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO detailItemDTO = new AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO();
                            detailItemDTO.setBusinessEntity(Optional.ofNullable(siteEntity).map(SiteEntity::getAtlasCode).orElse(""));
                            detailItemDTO.setContractType(DeliveryConstant.CONTRACT_TYPE_S);
                            detailItemDTO.setCurrency(DeliveryConstant.CURRENCY_CNY);
                            detailItemDTO.setCommodityID(commodityCode);
                            detailItemDTO.setContractTerms(deliveryApply.getDeliveryType().equals(String.valueOf(DeliveryModeEnum.TAKE.getValue())) ? "EXW" : "CNF");
                            detailItemDTO.setTerminal(Optional.ofNullable(warehouseEntity).map(WarehouseEntity::getAtlasTerminalCode).orElse(""));
                            detailItemDTO.setWarrantNumber(Optional.ofNullable(entity.getWarrantNumber()).orElse(""));
                            parameterList.add(detailItemDTO);
                            openQuantityDetailDTO.setParameterList(parameterList);
                            deliveryOpenQuantityDTO.setOpenQuantityDetails(openQuantityDetailDTO);

                            // 查询ATLAS接口获取可开单量
                            log.info("查询ATLAS接口获取可开单量：counterpartyId={}, deliveryOpenQuantityDTO={}", mdmCustomerCode, JSON.toJSONString(deliveryOpenQuantityDTO));
                            Result<AtlasDeliveryOpenQuantityDTO> openQuantityResult = atlasContractFacade.getDeliveryOpenQuantity(mdmCustomerCode, deliveryOpenQuantityDTO);

                            if (!openQuantityResult.isSuccess()) {
                                throw new BusinessException(openQuantityResult.getMessage());
                            }

                            // 总可开单量
                            BigDecimal totalOpenQuantity = openQuantityResult.getData().getOpenQuantityDetails().getParameterList().get(0).getOpenQuantity();
                            log.info("总可开单量：{}", totalOpenQuantity);

                            // blocked数量
                            String contractList = openQuantityResult.getData().getOpenQuantityDetails().getContractList();
                            BigDecimal blockedQuantity = deliveryApplyAtlasQueryService.getTotalBlockedQuantity(contractList);
                            log.info("blocked数量：{}", blockedQuantity);

                            // 实际可开单量
                            BigDecimal actualOpenQuantity = totalOpenQuantity.subtract(blockedQuantity);
                            log.info("实际可开单量：{}", actualOpenQuantity);

                            if (BigDecimalUtil.isGreater(entity.getAllocationQty(), actualOpenQuantity)) {
                                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_MORE_THAN_CAN_APPLY);
                            }
                        });
            } catch (BusinessException e) {
                throw e; // 直接抛出业务异常
            } catch (Exception e) {
                log.error("获取提货申请OpenQuantity异常：{}", e.getMessage(), e);
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_OPEN_QUANTITY_EXCEPTION);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO) {
        // 作废申请单
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyInvalidDTO.getApplyId());
        if (null != applyEntity) {

            // 操作系统
            SystemEnum systemEnum = SystemEnum.getByValue(applyInvalidDTO.getTriggerSys());
            String operator;
            if (SystemEnum.COLUMBUS == systemEnum) {
                operator = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();
            } else {
                operator = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();
            }


            DeliveryApplyStatusEnum statusEnum = DeliveryApplyStatusEnum.getByValue(applyEntity.getApplyStatus());

            int applyStatus = DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue();

            switch (statusEnum) {
                case NEW:
                    // 作废申请单
                    applyStatus = DeliveryApplyStatusEnum.INVALID.getValue();
                    break;
                case WAIT_APPROVE:
                case BLOCKED:
                    // 调用atlas作废接口
                    atlasSyncService.syncDeliveryInfo(applyEntity, applyEntity.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST_CANCEL.getValue());
                    break;
                case APPROVED:
                    // 根据车辆状态判断是否可以作废
                    String queueStatus = applyEntity.getQueueStatus();
                    /*if (StringUtil.isBlank(queueStatus)) {
                        throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_INVALID_ERROR.getCode(), "获取车辆状态异常，不可操作作废");
                    }*/

                    DeliveryQueueStatusEnum queueStatusEnum = DeliveryQueueStatusEnum.getByValue(queueStatus);
                    if (queueStatusEnum.getInvalidStatus() == 0) {
                        // 车辆状态xxx，不可操作作废
                        throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_INVALID_ERROR.getCode(), "车辆状态" + queueStatusEnum.getDesc() + "，不可操作作废");
                    }
                    // 调用atlas作废接口
                    atlasSyncService.syncDeliveryInfo(applyEntity, applyEntity.getUpdatedBy(), AtlasSyncActionEnum.DELIVERY_REQUEST_CANCEL.getValue());
                    break;
                default:
                    // 其他状态不允许作废
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_INVALID_ERROR.getCode(), "该提货委托申请单状态为" + statusEnum.getDesc() + "，不可发起作废");
            }

            // 保存操作记录
            deliveryAsyncExecute.recordInvalidDeliveryApplyLog(applyEntity,
                    JSON.toJSONString(applyEntity), null,
                    LogBizCodeEnum.INVALID_DELIVERY_APPLY, systemEnum,
                    Integer.valueOf(JwtUtils.getCurrentUserId()));

            applyEntity
                    .setApplyStatus(applyStatus)
                    .setInvalidReason(applyInvalidDTO.getInvalidReason())
                    .setUpdatedAt(new Date()).setUpdatedBy(operator);

            // 客户作废申请单，发送站内信
            if (applyStatus == DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue()) {
                deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID);
            }

            return deliveryApplyDao.updateById(applyEntity);
        }
        return false;
    }

    /**
     * 作废申请单合同
     *
     * @param applyId    申请单id
     * @param deleteFlag 是否删除
     */
    private void invalidApplyContract(Integer applyId, boolean deleteFlag) {
        List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getByApplyId(applyId);
        // 删除原有的合同 + 更新合同申请提货数量
        applyContractEntityList.forEach(applyContractEntity -> {
            removeApplyContract(applyContractEntity, deleteFlag);
        });
    }

    /**
     * 保存分配合同刪除合同
     *
     * @param applyContractEntity 合同
     * @param deleteFlag          是否删除
     */
    private void removeApplyContract(DeliveryApplyContractEntity applyContractEntity, boolean deleteFlag) {
        // 更新申请单分配合同数量
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyContractEntity.getApplyId());
        applyEntity.setTotalAllocateNum(applyEntity.getTotalAllocateNum().subtract(applyContractEntity.getAllocateNum()))
                .setUpdatedAt(new Date());
        deliveryApplyDao.updateById(applyEntity);

        // 更新合同申请提货数量(已拒绝已扣减合同申请量)
        if (applyEntity.getBillStatus() != DeliveryBillStatusEnum.REFUSED.getValue()) {
            ContractEntity contractEntity = contractFacade.getBasicContractById(applyContractEntity.getContractId());
            BigDecimal applyDeliveryNum = contractEntity.getApplyDeliveryNum().subtract(applyContractEntity.getAllocateNum());
            if (BigDecimalUtil.isLessThanZero(applyDeliveryNum)) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
            }
            contractFacade.updateContract(contractEntity.setApplyDeliveryNum(applyDeliveryNum));
        }

        // 删除原有的合同
        if (deleteFlag) {
            deliveryApplyContractDao.removeById(applyContractEntity);
        } else {
            applyContractEntity.setIsDeleted(1);
            deliveryApplyContractDao.updateById(applyContractEntity);
        }
    }

    @SneakyThrows
    @Override
    public DeliveryApplyAtlasImportResultDTO deliveryApplyImportFile(MultipartFile multipartFile, Integer customerId, Integer triggerSys) {
        // 客户
        CustomerEntity currentCustomerEntity = null;
        if (customerId != null) {
            log.info("customerId:{}", customerId);
            currentCustomerEntity = customerFacade.getCustomerById(customerId);
            if (currentCustomerEntity == null) {
                throw new BusinessException(String.format("未找到买方主体:%s", customerId));
            }
        }
        String nowDay = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        Date nowDate = DateUtil.parse(nowDay, DatePattern.NORM_DATE_PATTERN);
        // 车辆类型
        List<SystemRuleVO> carTypeList = systemRuleFacade.getSystemRule(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.DELIVERY_CAR_TYPE_CONFIG.getRuleCode()));
        // 提货库点
        QueryDTO<WarehouseBO> queryDTO = new QueryDTO<WarehouseBO>()
                .setPageNo(1)
                .setPageSize(10000)
                .setCondition(new WarehouseBO().setIsUnset(0).setStatus(1));
        List<WarehouseEntity> warehouseList = warehouseFacade.getWarehouseList(queryDTO).getData();
        try {
            Map<String, CustomerEntity> customerCodeMap = new HashMap<>();
            Map<String, CustomerEntity> supplierNameMap = new HashMap<>();
            Map<String, SkuEntity> goodsNameMap = new HashMap<>();
            Map<String, String> goodsFailMap = new HashMap<>();
            Map<String, FactoryEntity> factoryCodeMap = new HashMap<>();
            Map<String, BigDecimal> applyNumMap = new HashMap<>();
            Map<String, BigDecimal> canApplyNumMap = new HashMap<>();
            Map<String, String> applyNumFailMap = new HashMap<>();
            DeliveryApplyAtlasImportResultDTO resultDTO = new DeliveryApplyAtlasImportResultDTO();
            List<DeliveryApplyAtlasImportDTO> list = EasyPoiUtils.importExcel(multipartFile, 0, 1, DeliveryApplyAtlasImportDTO.class);
            List<DeliveryApplyAtlasImportDTO> result = new ArrayList<>();
            int totalCount = 0;
            int failCount = 0;
            for (DeliveryApplyAtlasImportDTO item : list) {
                log.info("数据:{}", JSONUtil.toJsonStr(item));
                if (item.isAllNull()) {
                    // 跳过空的
                    continue;
                }
                result.add(item);
                CustomerEntity customer = null;
                CustomerEntity supplier = null;
                SkuEntity sku = null;
                totalCount++;
                if (SystemEnum.COLUMBUS.getValue().equals(triggerSys) && currentCustomerEntity != null) {
                    item.setCustomerId(currentCustomerEntity.getId());
                    item.setCustomerName(currentCustomerEntity.getName());
                    item.setCustomerCode(currentCustomerEntity.getLinkageCustomerCode());
                    customer = currentCustomerEntity;
                }
                log.info(JSONUtil.toJsonStr(item));
                if (goodsFailMap.get(item.getGoodsName()) != null) {
                    // 校验相同的SKU，出现第一条失败，其他的申请不再继续校验，此SKU核验结束，校验其它SKU
                    item.setMessage("上传失败");
                    failCount++;
                    continue;
                }
                // 必填校验
                List<String> messageSet = new ArrayList<>();
                List<String> otherMessageSet = new ArrayList<>();
                List<String> detailMessageSet = new ArrayList<>();
                if (StringUtil.isNullBlank(item.getCustomerName())) {
                    messageSet.add("买方主体");
                }
                if (StringUtil.isNullBlank(item.getCustomerCode())) {
                    messageSet.add("客户编码");
                }
                // 买方主体、客户编码
                if (StringUtil.isNotNullBlank(item.getCustomerCode()) && StringUtil.isNotNullBlank(item.getCustomerName())) {
                    if (SystemEnum.MAGELLAN.getValue().equals(triggerSys)) {
                        customer = customerCodeMap.get(item.getCustomerCode());
                        if (customer == null) {
                            List<CustomerEntity> customerList = customerFacade.queryCustomerList(new CustomerQO().setName(item.getCustomerName()).setLinkageCustomerCode(item.getCustomerCode()));
                            if (CollUtil.isNotEmpty(customerList)) {
                                customer = customerList.get(0);
                                customerCodeMap.put(item.getCustomerCode(), customer);
                            }
                        }
                        if (customer == null) {
                            messageSet.add("买方主体");
                            messageSet.add("客户编码");
                            detailMessageSet.add("买方主体，客户编码：查询不到记录");
                        } else {
                            item.setCustomerId(customer.getId());
                        }
                    }
                }
                // 货品
                if (StringUtil.isNotNullBlank(item.getGoodsName())) {
                    sku = goodsNameMap.get(item.getGoodsName());
                    if (sku == null) {
                        Result<SkuEntity> skuResult = skuFacade.getSkuByFullName(item.getGoodsName());
                        if (skuResult.isSuccess()) {
                            sku = skuResult.getData();
                            if (sku == null) {
                                detailMessageSet.add("货品：查询不到记录");
                            }
                        } else {
                            otherMessageSet.add("货品：" + skuResult.getMessage());
                            detailMessageSet.add("货品：" + skuResult.getMessage());
                            goodsFailMap.put(item.getGoodsName(), item.getGoodsName());
                        }
                        goodsNameMap.put(item.getGoodsName(), sku);
                    }
                    if (sku == null) {
                        messageSet.add("货品");
                    } else {
                        item.setGoodsId(sku.getId());
                        item.setGoodsCategoryId(sku.getCategory2());
                    }
                } else {
                    messageSet.add("货品");
                }
                // 提货工厂
                if (StringUtil.isNotNullBlank(item.getDeliveryFactoryCode())) {
                    FactoryEntity factoryEntity = factoryCodeMap.get(item.getDeliveryFactoryCode());
                    if (factoryEntity == null) {
                        factoryEntity = factoryFacade.getFactoryByCode(item.getDeliveryFactoryCode());
                        factoryCodeMap.put(item.getDeliveryFactoryCode(), factoryEntity);
                    }
                    if (factoryEntity == null) {
                        messageSet.add("提货工厂");
                        detailMessageSet.add("提货工厂：查询不到记录");
                    }
                } else {
                    messageSet.add("提货工厂");
                }
                // 卖方主体
                if (StringUtil.isNotNullBlank(item.getSupplierName())) {
                    supplier = supplierNameMap.get(item.getSupplierName());
                    if (supplier == null) {
                        List<CustomerEntity> customerList = customerFacade.queryCustomerList(new CustomerQO().setName(item.getSupplierName()).setIsLdc(1));
                        if (CollUtil.isNotEmpty(customerList)) {
                            supplier = customerList.get(0);
                            supplierNameMap.put(item.getSupplierName(), supplier);
                        }
                    }
                    if (supplier == null) {
                        messageSet.add("卖方主体");
                        detailMessageSet.add("卖方主体：查询不到记录");
                    } else {
                        item.setSupplierId(supplier.getId());
                    }
                } else {
                    messageSet.add("卖方主体");
                }
                // BUGFIX：case-1003114 配送合同批量上传提示“可提货量不足”，但合同量是够的 Author: Mr 2025-04-09 start
                // if (StringUtil.isNullBlank(item.getDeliveryType())) {
                // 数据校验： 非空校验 || 必须是数字
                if (StringUtil.isNullBlank(item.getDeliveryType()) || !StringUtil.isNumeric(item.getDeliveryType())) {
                    // BUGFIX：case-1003114 配送合同批量上传提示“可提货量不足”，但合同量是够的 Author: Mr 2025-04-09 end
                    messageSet.add("提货方式");
                }
                // 申请日期
                if (StringUtil.isNotNullBlank(item.getApplyTime())) {
                    if (DateUtil.compare(nowDate, item.getApplyTime()) != 0) {
                        messageSet.add("申请日期");
                        detailMessageSet.add("申请日期：只能填写提交当天的日期（年月日）");
                    }
                } else {
                    messageSet.add("申请日期");
                }
                // 数量
                if (StringUtil.isNotNullBlank(item.getApplyNum())) {
                    if (item.getApplyNum().compareTo(BigDecimal.ZERO) <= 0) {
                        messageSet.add("数量");
                        detailMessageSet.add("数量：应大于0");
                    }
                } else {
                    messageSet.add("数量");
                }
                // 提货库点
                if (StringUtil.isNotNullBlank(item.getWarehouseType())) {
                    WarehouseEntity warehouseEntity = null;
                    for (WarehouseEntity warehouseItem : warehouseList) {
                        if (warehouseItem.getWarehouseType().equals(item.getWarehouseType())) {
                            // LDC库，默认取第一个
                            if (StringUtil.isNullBlank(item.getDeliveryWarehouseName()) && item.getWarehouseType() == 1) {
                                warehouseEntity = warehouseItem;
                                break;
                            }
                            // 匹配对应的名称
                            if (warehouseItem.getName().equals(item.getDeliveryWarehouseName())) {
                                warehouseEntity = warehouseItem;
                                break;
                            }
                        }
                    }
                    if (warehouseEntity == null) {
                        messageSet.add("提货库点");
                        detailMessageSet.add("提货库点：查询不到记录");
                    } else {
                        item.setDeliveryWarehouseId(warehouseEntity.getId());
                    }
                } else {
                    messageSet.add("LDC库/外库");
                }
                // 计划提货日期
                if (StringUtil.isNotNullBlank(item.getPlanDeliveryTime())) {
                    if (DateUtil.compare(nowDate, item.getPlanDeliveryTime()) > 0) {
                        messageSet.add("计划提货日期");
                        detailMessageSet.add("计划提货日期：只能填写提交当天和之后的日期（年月日）");
                    }
                } else {
                    messageSet.add("计划提货日期");
                }
                // 运输方式
                if (StringUtil.isNotNullBlank(item.getTransportWay())) {
                    if (DeliveryTransportWay.TRUCK.getValue().equals(item.getTransportWay())) {
                        // 车提
                        if (StringUtil.isNullBlank(item.getCarTypeName())) {
                            messageSet.add("车辆类型");
                        } else {
                            SystemRuleVO.SystemRuleItemVO carType = null;
                            for (SystemRuleVO.SystemRuleItemVO systemRuleItemVO : carTypeList.get(0).getSystemRuleItemVOList()) {
                                if (systemRuleItemVO.getRuleItemValue().equals(item.getCarTypeName())) {
                                    carType = systemRuleItemVO;
                                    break;
                                }
                            }
                            if (carType == null) {
                                messageSet.add("车辆类型");
                                detailMessageSet.add("车辆类型：查询不到记录");
                            } else {
                                item.setCarType(carType.getRuleItemId());
                            }
                        }
                    } else if (DeliveryTransportWay.SHIPPING.getValue().equals(item.getTransportWay())) {
                        // 船提
                    } else if (DeliveryTransportWay.TRAIN.getValue().equals(item.getTransportWay())) {
                        // 火车提
                        if (StringUtil.isNullBlank(item.getStation())) {
                            messageSet.add("车站");
                        }
                    } else if (DeliveryTransportWay.TRANSFER.getValue().equals(item.getTransportWay())) {
                        // 转货权
                    }
                } else {
                    messageSet.add("运输方式");
                }
                // 司机姓名
                if (StringUtil.isNullBlank(item.getDriverName())) {
                    messageSet.add("司机姓名");
                }
                // 司机身份证号
                if (StringUtil.isNullBlank(item.getDriverIdNumber()) || StringUtil.isNotNullBlank(item.getDriverIdNumber()) && item.getDriverIdNumber().length() != 15 && item.getDriverIdNumber().length() != 18) {
                    messageSet.add("司机身份证号");
                }
                // 随车电话
                if (StringUtil.isNullBlank(item.getOnboardPhone()) || StringUtil.isNotNullBlank(item.getOnboardPhone()) && !ReUtil.isMatch("^1\\d{10}$", item.getOnboardPhone())) {
                    messageSet.add("随车电话");
                }
                // 共几方拼车船
                if (StringUtil.isNotNullBlank(item.getIsCarpool())) {
                    if (item.getIsCarpool() == 1) {
                        if (item.getCarpoolCount() == null || item.getCarpoolCount() < 1) {
                            messageSet.add("共几方拼车船");
                        }
                        if (StringUtil.isNullBlank(item.getCarpoolCustomer())) {
                            messageSet.add("拼车船客户");
                        }
                        if (item.getLoadingPriority() == null || item.getLoadingPriority() < 1
                                || item.getCarpoolCount() != null && item.getLoadingPriority() != null && item.getLoadingPriority() > item.getCarpoolCount()) {
                            messageSet.add("装货优先级");
                        }
                        // 装货优先级是否重复
                        if (StringUtil.isNotNullBlank(item.getIsCarpool())
                                && StringUtil.isNotNullBlank(item.getCarpoolCustomer())
                                && StringUtil.isNotNullBlank(item.getLoadingPriority())) {
                            String message = deliveryApplyDao.checkCarpool(BeanUtil.copyProperties(item, DeliveryApplyEntity.class), false);
                            if (StringUtil.isNotNullBlank(message)) {
                                messageSet.add(message);
                                detailMessageSet.add(message);
                            }
                        }
                    }
                } else {
                    messageSet.add("是否拼车/船");
                }
                // 检查账套
                if (CollUtil.isEmpty(messageSet)) {
                    SiteEntity site = siteFacade.getSiteByCompanyIdAndFactoryCode(supplier.getCompanyId(), item.getDeliveryFactoryCode());
                    if (site == null) {
                        messageSet.add("帐套错误");
                    }
                }
                // 可提货量是否不足
                if (CollUtil.isEmpty(messageSet)) {
                    // 累加可提量
                    BigDecimal applyNum = applyNumMap.get(item.getGoodsName());
                    if (applyNum == null) {
                        applyNum = BigDecimal.ZERO;
                    }
                    applyNum = applyNum.add(item.getApplyNum());
                    applyNumMap.put(item.getGoodsName(), applyNum);
                    // 可提量
                    BigDecimal canApplyNum = canApplyNumMap.get(item.getGoodsName());
                    if (canApplyNum == null) {
                        canApplyNum = BigDecimal.ZERO;
                        List<DeliveryApplyContractAtlasVO> deliveryApplyContractAtlasVOList = deliveryApplyAtlasQueryService.getDeliveryApplyContractList(customer, supplier, null, sku, item.getDeliveryFactoryCode(), triggerSys);
                        for (DeliveryApplyContractAtlasVO deliveryApplyContractAtlasVO : deliveryApplyContractAtlasVOList) {
                            // BUGFIX：case-1003114 配送合同批量上传提示“可提货量不足”，但合同量是够的 Author: Mr 2025-04-09 Start
                            /*if (DeliveryModeEnum.TAKE.getValue() == deliveryApplyContractAtlasVO.getDeliveryType()) {
                                canApplyNum = deliveryApplyContractAtlasVO.getCanDeliveryNum();
                                break;
                            }*/
                            canApplyNum = deliveryApplyContractAtlasVO.getCanDeliveryNum();
                            break;
                            // BUGFIX：case-1003114 配送合同批量上传提示“可提货量不足”，但合同量是够的 Author: Mr 2025-04-09 End
                        }
                    }
                    if (applyNum.compareTo(canApplyNum) > 0) {
                        item.setMessage("可提货量不足");
                        failCount++;
                        applyNumFailMap.put(item.getGoodsName(), item.getGoodsName());
                        goodsFailMap.put(item.getGoodsName(), item.getGoodsName());
                        continue;
                    }
                }

                // 结果
                if (CollUtil.isNotEmpty(messageSet)) {
                    item.setMessage(String.format("字段：%s填写错误。", CollUtil.join(messageSet, ",")));
                    if (CollUtil.isNotEmpty(otherMessageSet)) {
                        item.setMessage(item.getMessage() + CollUtil.join(otherMessageSet, ","));
                    }
                    item.setDetailMessage(CollUtil.join(detailMessageSet, "。"));
                    failCount++;
                    goodsFailMap.put(item.getGoodsName(), item.getGoodsName());
                }

            }
            // 查找已有SKU是否可提货量不足
            for (DeliveryApplyAtlasImportDTO item : result) {
                if (StringUtil.isNullBlank(item.getMessage()) && applyNumFailMap.get(item.getGoodsName()) != null) {
                    item.setMessage("可提货量不足");
                    failCount++;
                }
            }
            resultDTO.setTotalCount(totalCount);
            resultDTO.setSuccessCount(totalCount - failCount);
            resultDTO.setFailCount(failCount);
            resultDTO.setList(result);
            return resultDTO;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("导入错误！错误信息：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deliveryApplyImportSubmit(List<DeliveryApplyAtlasImportDTO> deliveryApplyAtlasImportDTOList, Integer triggerSys) {
        int count = 0;
        for (DeliveryApplyAtlasImportDTO item : deliveryApplyAtlasImportDTOList) {
            log.info("记录:{}", JSONUtil.toJsonStr(item));
            if (StringUtil.isNotNullBlank(item.getMessage())) {
                log.info("跳过记录:{}", item.getMessage());
                continue;
            }
            DeliveryApplyDTO deliveryApplyDTO = BeanUtil.copyProperties(item, DeliveryApplyDTO.class);
            deliveryApplyDTO.setSubmitType(1);
            deliveryApplyDTO.setTriggerSys(triggerSys);
            this.saveOrSubmitDeliveryApply(deliveryApplyDTO);
            count++;
        }
        return count;
    }

    /**
     * 获取同步atlas状态
     *
     * @param deliveryApply 提货申请
     * @return true:同步atlas false:不同步atlas
     */
    private boolean getSyncAtlasStatus(DeliveryApplyEntity deliveryApply) {
        SkuEntity skuEntity = skuFacade.getSkuById(deliveryApply.getGoodsId());

        if (skuEntity != null) {
            // 查询客户是否品种白名单
            CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO = new CustomerDeliveryWhiteDTO();
            customerDeliveryWhiteDTO.setCustomerId(deliveryApply.getCustomerId());
            customerDeliveryWhiteDTO.setCategory1(skuEntity.getCategory1().toString());
            customerDeliveryWhiteDTO.setCategory2(skuEntity.getCategory2().toString());
            customerDeliveryWhiteDTO.setCategory3(skuEntity.getCategory3().toString());
            customerDeliveryWhiteDTO.setStatus(1);
            List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOList = customerDeliveryWhiteFacade.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
            boolean isWhite = CollUtil.isNotEmpty(customerDeliveryWhiteDTOList);

            // 查询交割可提量-BI
            ExchangePickQtyQO pickQtyQO = new ExchangePickQtyQO();
            pickQtyQO.setGoodsId(deliveryApply.getGoodsId());
            pickQtyQO.setDeliveryFactoryCode(deliveryApply.getDeliveryFactoryCode());
            CustomerEntity supplier = deliveryApply.getSupplierId() != null ? customerFacade.getCustomerById(deliveryApply.getSupplierId()) : null;
            pickQtyQO.setCompanyId(null == supplier ? 1 : supplier.getCompanyId());
            pickQtyQO.setDeliveryType(Integer.valueOf(deliveryApply.getDeliveryType()));
            pickQtyQO.setSystem(deliveryApply.getTriggerSys().toString());
            pickQtyQO.setCustomerId(deliveryApply.getCustomerId());
            List<ExchangePickQtyDTO> pickQtyDTOList = deliveryBIQueryService.getExchangePickQty(pickQtyQO);
            ExchangePickQtyDTO exchangePickQtyDTO = CollUtil.isEmpty(pickQtyDTOList) ? null : pickQtyDTOList.get(0);
            BigDecimal dceCanDeliveryNum = exchangePickQtyDTO == null ? BigDecimal.ZERO : exchangePickQtyDTO.getPickupQty();
            log.info("当前货品品种的提货白名单:{},交割可提量:{}", isWhite, dceCanDeliveryNum);
            // 交割可提量 = 0且客户属于当前货品品种的提货白名单
            return !isWhite || dceCanDeliveryNum.compareTo(BigDecimal.ZERO) != 0;
        }
        return true;
    }

    /**
     * 保存分配合同
     *
     * @param deliveryApply 申请单数据
     */
    private void saveApplyContract(DeliveryApplyEntity deliveryApply) {
        log.info("保存分配合同");
        deliveryApply.setTotalAllocateNum(BigDecimal.ZERO);
        deliveryApplyContractDao.removeByApplyId(deliveryApply.getId());
        if (CollUtil.isNotEmpty(deliveryApply.getDeliveryApplyContractList())) {
            for (DeliveryApplyContractEntity item : deliveryApply.getDeliveryApplyContractList()) {
                if (BigDecimalUtil.isLessEqualThanZero(item.getAllocateNum())) {
                    continue;
                }
                // 获取合同
                ContractEntity contractEntity = contractFacade.getContractById(item.getContractId());
                // 保存申请单合同数据
                DeliveryApplyContractEntity applyContract = BeanUtil.toBean(contractEntity, DeliveryApplyContractEntity.class);
                applyContract.setApplyId(deliveryApply.getId())
                        .setContractId(contractEntity.getId())
                        .setAllocateNum(item.getAllocateNum())
                        .setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, item.getAllocateNum(), contractEntity.getUnitPrice()))
                        .setContractSignDate(contractEntity.getSignDate())
                        .setCreatedBy(deliveryApply.getCreatedBy())
                        .setUpdatedBy(deliveryApply.getUpdatedBy())
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date());
                log.info("========:{}", JSONUtil.toJsonStr(applyContract));
                deliveryApplyContractDao.save(applyContract);
                // 更新申请单分配合同数量
                deliveryApply.setTotalAllocateNum(deliveryApply.getTotalAllocateNum().add(item.getAllocateNum()));
            }
        }
        deliveryApplyDao.updateById(deliveryApply);
    }

    /**
     * 获取用户名称
     *
     * @param triggerSys
     * @return
     */
    private String getEmployName(Integer triggerSys) {
        if (SystemEnum.MAGELLAN.getValue().equals(triggerSys)) {
            EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
            if (employEntity != null) {
                return employEntity.getName();
            }
        } else {
            CEmployEntity employEntity = cEmployFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
            if (employEntity != null) {
                return employEntity.getName();
            }
        }
        return null;
    }

    @Override
    public void sendInMailMessage(Integer applyId) {
        DeliveryApplyEntity deliveryApplyEntity = deliveryApplyDao.getById(applyId);
        // ATLAS
        if (SystemEnum.ATLAS.getValue().equals(deliveryApplyEntity.getThirdSys())) {
            if (DeliveryApplyStatusEnum.APPROVED.getValue() == deliveryApplyEntity.getApplyStatus()) {
                deliveryAsyncExecute.sendInMailMessage(deliveryApplyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_PASS);
            } else if (DeliveryApplyStatusEnum.REJECTED.getValue() == deliveryApplyEntity.getApplyStatus()) {
                deliveryAsyncExecute.sendInMailMessage(deliveryApplyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT);
            } else if (DeliveryApplyStatusEnum.INVALID.getValue() == deliveryApplyEntity.getApplyStatus()) {
                deliveryAsyncExecute.sendInMailMessage(deliveryApplyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE);
            } else if (DeliveryApplyStatusEnum.BLOCKED.getValue() == deliveryApplyEntity.getApplyStatus()) {
                deliveryAsyncExecute.sendInMailMessage(deliveryApplyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_BLOCKED);
            }
        }
    }

    @Override
    public boolean saveDeliveryApplyAckContract(DeliveryAckContractDTO deliveryAckContractDTO) {
        String deliveryCode = deliveryAckContractDTO.getDeliveryCode();

        // 查询提货单
        DeliveryApplyEntity deliveryApplyEntity = deliveryApplyDao.getDeliveryApplyByCode(deliveryCode.contains("-") ? deliveryCode.split("-")[0] : deliveryCode);
        if (deliveryApplyEntity == null) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NOT_EXIST);
        }

        // 保存creationStatus
        if (StringUtil.isNotBlank(deliveryAckContractDTO.getCreationStatus())) {
            deliveryApplyEntity.setAtlasCreationStatus(deliveryAckContractDTO.getCreationStatus());
            deliveryApplyDao.updateById(deliveryApplyEntity);
        }

        List<DeliveryAckContractDTO.ContractDTO> ackOContractList = deliveryAckContractDTO.getContractList();

        // DR 取消申请单，清除合同信息
        if (CollectionUtil.isEmpty(ackOContractList)) {
            List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getBySubApplyCodeOrApplyId(deliveryAckContractDTO.getDeliveryCode(), deliveryApplyEntity.getId());
            applyContractEntityList.stream()
                    .filter(applyContractEntity -> applyContractEntity.getIsDeleted() == 0)
                    .forEach(this::removeContractAndUpdateExecution);
            return true;
        }

        // BUGFIX：case-1003220 合同未提货量为负数 Author: Mr 2025-05-19 Start
        // 清除不在 ackOContractList 中的合同
        List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getBySubApplyCodeOrApplyId(deliveryAckContractDTO.getDeliveryCode(), deliveryApplyEntity.getId());
        applyContractEntityList.stream()
                .filter(applyContractEntity -> applyContractEntity.getIsDeleted() == 0)
                .filter(applyContractEntity -> ackOContractList.stream().noneMatch(contractDTO -> Objects.equals(applyContractEntity.getContractCode(), contractDTO.getContractCode())))
                .forEach(this::removeContractAndUpdateExecution);

        // DR 保存/更新合同信息
        ackOContractList.forEach(contract -> updateOrCreateContract(deliveryApplyEntity, contract));
        // BUGFIX：case-1003220 合同未提货量为负数 Author: Mr 2025-05-19 End

        return true;
    }

    @Override
    public boolean updateDeliveryApply(DeliveryApplyEntity deliveryApplyEntity) {
        return deliveryApplyDao.updateById(deliveryApplyEntity);
    }

    /**
     * 移除合同并更新执行情况
     *
     * @param deliveryApplyEntity 提货单实体
     * @param contract            合同信息
     */
    private void updateOrCreateContract(DeliveryApplyEntity deliveryApplyEntity, DeliveryAckContractDTO.ContractDTO contract) {
        int updateTimes = 0;
        BigDecimal allocateNum = new BigDecimal(contract.getPlannedQuantity());

        BigDecimal beforeAllocateNum = BigDecimal.ZERO;
        BigDecimal beforeExecutedNum = BigDecimal.ZERO;

        // 保存/更新合同信息
        ContractEntity contractEntity = contractFacade.getBasicContractByCode(contract.getContractCode());
        if (null != contractEntity) {
            // 保存/更新合同信息
            DeliveryApplyContractEntity deliveryApplyContract = deliveryApplyContractDao.getByApplyCodeAndContractId(contract.getDeliveryCode(), contractEntity.getId());

            if (null == deliveryApplyContract) {
                // 新增合同
                deliveryApplyContract = BeanUtil.toBean(contractEntity, DeliveryApplyContractEntity.class);

                deliveryApplyContract
                        .setId(null)
                        .setApplyId(deliveryApplyEntity.getId())
                        .setApplyCode(contract.getDeliveryCode())
                        .setContractId(contractEntity.getId())
                        .setAllocateNum(allocateNum)
                        .setExecutedNum(deliveryApplyEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue() ? allocateNum : BigDecimal.ZERO)
                        .setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, allocateNum, contractEntity.getUnitPrice()))
                        .setContractSignDate(contractEntity.getSignDate())
                        .setCreatedAt(new Date())
                        .setCreatedBy("")
                        .setUpdatedAt(new Date())
                        .setUpdatedBy("")
                        .setAtlasUpdateResult(1)
                        .setAtlasUpdateTimes(updateTimes);
                deliveryApplyContractDao.save(deliveryApplyContract);
            } else {
                updateTimes = deliveryApplyContract.getAtlasUpdateTimes() + 1;

                beforeAllocateNum = deliveryApplyContract.getAllocateNum();
                beforeExecutedNum = deliveryApplyContract.getExecutedNum();

                BigDecimal afterAllocateNum = deliveryApplyEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue() ? beforeAllocateNum : allocateNum;
                BigDecimal afterExecutedNum = deliveryApplyEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue() ? allocateNum : BigDecimal.ZERO;

                // 更新合同信息
                deliveryApplyContract
                        .setAllocateNum(afterAllocateNum)
                        .setExecutedNum(afterExecutedNum)
                        .setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, afterAllocateNum, contractEntity.getUnitPrice()))
                        .setUpdatedAt(new Date())
                        .setUpdatedBy("")
                        .setAtlasUpdateResult(1)
                        .setAtlasUpdateTimes(updateTimes);
                deliveryApplyContractDao.updateById(deliveryApplyContract);
            }

            // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07 Start
            // 更新合同执行情况
            updateContractExecution(deliveryApplyEntity, deliveryApplyContract, contractEntity);
        }
    }

    /**
     * 更新合同执行情况
     *
     * @param deliveryApplyEntity   提货申请单实体
     * @param deliveryApplyContract 申请单合同实体
     * @param contractEntity        合同实体
     */
    private void updateContractExecution(DeliveryApplyEntity deliveryApplyEntity, DeliveryApplyContractEntity deliveryApplyContract, ContractEntity contractEntity) {
        String contractCode = deliveryApplyContract.getContractCode();
        String lockKey = RedisConstants.CONTRACT_EXECUTE_UPDATE + contractCode;

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            // 等待最多10秒，锁持有时间5秒
            locked = lock.tryLock(10, 5, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("获取合同更新锁失败，contractCode={}", contractCode);
                return;
            }

            // 查询合同明细
            List<DeliveryApplyContractEntity> entityList =
                    deliveryApplyContractDao.getByContractId(deliveryApplyContract.getContractId());

            BigDecimal totalAllocateNum = BigDecimal.ZERO;
            BigDecimal totalExecutedNum = BigDecimal.ZERO;

            for (DeliveryApplyContractEntity entity : entityList) {
                if (entity.getIsDeleted() == 0) {
                    totalAllocateNum = totalAllocateNum.add(entity.getAllocateNum());
                    totalExecutedNum = totalExecutedNum.add(entity.getExecutedNum());
                }
            }

            Result<AtlasMappingContractEntity> result = atlasContractFacade.getByNavContractCode(contractCode);
            if (result.isSuccess() && result.getData() != null) {
                AtlasMappingContractEntity mappingContract = result.getData();

                // 触发邮件提醒: 已分配的仓单合同的已注销量和(已提货量+本次分配量)，已注销量为ATLAS接口传输的量，如已注销量小于(已提货量+本次分配量)则触发邮件提醒。
                try {
                    if (BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
                        // 已提货量+本次分配量
                        BigDecimal deliveryNum = (mappingContract.getExecutedNum() == null ? BigDecimal.ZERO : mappingContract.getExecutedNum()).add(deliveryApplyContract.getAllocateNum());

                        // 合同已注销量
                        BigDecimal canceledNum = contractEntity.getWarrantCancelCount() == null ? BigDecimal.ZERO : contractEntity.getWarrantCancelCount();

                        if (BigDecimalUtil.isLess(canceledNum, deliveryNum)) {
                            deliveryAsyncExecute.sendInMailMessage(deliveryApplyEntity, MessageBusinessCodeEnum.MAGELLAN_DELIVERY_WARRANT_ALLOCATION_WARNING);
                        }
                    }
                } catch (Exception e) {
                    log.error("提货委托分配警告邮件发送失败！", e);
                }

                mappingContract.setAllocatedNum(totalAllocateNum);
                mappingContract.setExecutedNum(totalExecutedNum);
                mappingContract.setUpdatedAt(new Date());

                atlasContractFacade.updateAtlasMappingContract(mappingContract);
                log.info("合同更新成功，contractCode={}", contractCode);
            } else {
                log.warn("未查询到Atlas合同，contractCode={}", contractCode);
            }

        } catch (Exception e) {
            log.error("合同执行数据更新异常，contractCode=" + contractCode, e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07 end

    /**
     * 作废DR申请单合同
     *
     * @param applyContractEntity 申请单合同实体
     */
    private void removeContractAndUpdateExecution(DeliveryApplyContractEntity applyContractEntity) {
        // 作废申请单合同
        deliveryApplyContractDao.removeById(applyContractEntity.getId());

        // 更新合同执行情况
        Result<AtlasMappingContractEntity> result = atlasContractFacade.getByNavContractCode(applyContractEntity.getContractCode());
        if (result.isSuccess()) {
            AtlasMappingContractEntity mappingContract = result.getData();
            if (mappingContract != null) {
                // BUGFIX：case-1003220 合同未提货量为负数 Author: Mr 2025-05-19 Start
                BigDecimal allocatedNum = mappingContract.getAllocatedNum().subtract(applyContractEntity.getAllocateNum());
                BigDecimal executedNum = mappingContract.getExecutedNum().subtract(applyContractEntity.getExecutedNum());
                mappingContract.setAllocatedNum(BigDecimalUtil.isLessThanZero(allocatedNum) ? BigDecimal.ZERO : allocatedNum);
                mappingContract.setExecutedNum(BigDecimalUtil.isLessThanZero(executedNum) ? BigDecimal.ZERO : executedNum);
                mappingContract.setUpdatedAt(new Date());
                atlasContractFacade.updateAtlasMappingContract(mappingContract);

                log.info(" DR 作废申请单合同或更新合同，applyCode={}, contractCode={}, 合同更新前，allocatedNum={}, executedNum={} , 合同更新后，allocatedNum={}, executedNum={}",
                        applyContractEntity.getApplyCode(),
                        applyContractEntity.getContractCode(),
                        mappingContract.getAllocatedNum(), mappingContract.getExecutedNum(),
                        allocatedNum, executedNum);
                // BUGFIX：case-1003220 合同未提货量为负数 Author: Mr 2025-05-19 End
            }
        }
    }
}
