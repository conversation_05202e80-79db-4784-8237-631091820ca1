package com.navigator.delivery.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.delivery.mapper.DeliveryApplyAllocateMapper;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;

import java.util.List;

/**
 * <p>
 * 提货申请预分配 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Dao
public class DeliveryApplyAllocateDao extends BaseDaoImpl<DeliveryApplyAllocateMapper, DeliveryApplyAllocateEntity> {
    /**
     * 根据条件：获取提货申请预分配列表
     *
     * @param condition
     * @return
     */
    public List<DeliveryApplyAllocateEntity> queryDeliveryApplyAllocateList(DeliveryApplyAllocateQO condition) {
        return this.list(DeliveryApplyAllocateEntity.lqw(condition));
    }

    /**
     * 根据ID：获取提货申请预分配
     *
     * @param id
     * @return
     */
    public DeliveryApplyAllocateEntity getDeliveryApplyAllocateById(Integer id) {
        return this.getById(id);
    }

    public List<DeliveryApplyAllocateEntity> getDeliveryApplyAllocateByApplyId(Integer applyId) {
        return this.lambdaQuery()
                .eq(DeliveryApplyAllocateEntity::getApplyId, applyId)
                .eq(DeliveryApplyAllocateEntity::getIsDeleted, 0)
                .list();
    }

    public void deleteByApplyId(Integer applyId) {
        this.lambdaUpdate()
                .set(DeliveryApplyAllocateEntity::getIsDeleted, 1)
                .eq(DeliveryApplyAllocateEntity::getApplyId, applyId)
                .update();
    }
}
