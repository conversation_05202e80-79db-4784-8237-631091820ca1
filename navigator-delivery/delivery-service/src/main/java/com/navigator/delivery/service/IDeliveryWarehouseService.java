package com.navigator.delivery.service;

import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;

import java.util.List;

/**
 * <p>
 * 提货库点配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IDeliveryWarehouseService {

    Boolean addDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity);

    Boolean updateDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity);

    List<DeliveryWarehouseEntity> getDeliveryWarehouseList(DeliveryWarehouseDTO warehouseDTO);

    Boolean updateDeliveryWarehouseStatus(Integer warehouseId, Integer status);

    DeliveryWarehouseEntity getDeliveryWarehouseById(Integer warehouseId);
}
