package com.navigator.delivery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * dbd_delivery_apply_driver_log Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface DeliveryApplyDriverLogMapper extends BaseMapper<DeliveryApplyDriverLogEntity> {

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start
    @Select("SELECT dal.* " +
            "FROM dbd_delivery_apply_driver_log dal " +
            "WHERE NOT EXISTS ( " +
            "    SELECT 1 " +
            "    FROM ( SELECT DISTINCT " +
            "        customer_id, " +
            "        driver_id_number, " +
            "        plate_number " +
            "    FROM dbd_delivery_apply " +
            "    WHERE created_at >=  DATEADD(year, -1, GETDATE())) AS uc " +
            "    WHERE dal.plate_number = uc.plate_number AND dal.driver_id_number = uc.driver_id_number AND dal.customer_id = uc.customer_id);")
    List<DeliveryApplyEntity> getUnUseDriverInfoOneYear();
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End
}
