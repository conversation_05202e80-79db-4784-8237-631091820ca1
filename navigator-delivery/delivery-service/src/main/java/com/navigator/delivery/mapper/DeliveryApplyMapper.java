package com.navigator.delivery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 提货申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface DeliveryApplyMapper extends BaseMapper<DeliveryApplyEntity> {

    @Select("{ CALL [dbo].[sp_ExecutePreAllocation]( " +
            "#{allocationQO.goodsId, mode=IN, jdbcType=INTEGER}, " +
            "#{allocationQO.deliveryFactoryCode, mode=IN, jdbcType=NVARCHAR}, " +
            "#{allocationQO.companyId, mode=IN, jdbcType=INTEGER}, " +
            "#{allocationQO.deliveryType, mode=IN, jdbcType=INTEGER}, " +
            "#{allocationQO.customerId, mode=IN, jdbcType=INTEGER}, " +
            "#{allocationQO.system, mode=IN, jdbcType=INTEGER}, " +
            "#{allocationQO.requestQty, mode=IN, jdbcType=DECIMAL}, " +
            "#{allocationQO.warehouseId, mode=IN, jdbcType=INTEGER}) }")
    List<ExecutePreAllocationDTO> callExecutePreAllocation(@Param("allocationQO") ExecutePreAllocationQO allocationQO);

    @Select("{ CALL [dbo].[sp_GetExchangePickQty]( " +
            "#{pickQtyQO.goodsId, mode=IN, jdbcType=INTEGER}, " +
            "#{pickQtyQO.deliveryFactoryCode, mode=IN, jdbcType=NVARCHAR}, " +
            "#{pickQtyQO.companyId, mode=IN, jdbcType=INTEGER}, " +
            "#{pickQtyQO.deliveryType, mode=IN, jdbcType=INTEGER}, " +
            "#{pickQtyQO.system, mode=IN, jdbcType=NVARCHAR}, " +
            "#{pickQtyQO.customerId, mode=IN, jdbcType=INTEGER}) }")
    List<ExchangePickQtyDTO> callExchangePickQty(@Param("pickQtyQO") ExchangePickQtyQO pickQtyQO);
}
