package com.navigator.delivery.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.delivery.facade.DeliveryWarehouseFacade;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;
import com.navigator.delivery.service.IDeliveryWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class DeliveryWarehouseFacadeImpl implements DeliveryWarehouseFacade {
    private final IDeliveryWarehouseService deliveryWarehouseService;

    @Override
    public Result<Boolean> addDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        return Result.success(deliveryWarehouseService.addDeliveryWarehouse(deliveryWarehouseEntity));
    }

    @Override
    public Result<Boolean> updateDeliveryWarehouse(DeliveryWarehouseEntity deliveryWarehouseEntity) {
        return Result.success(deliveryWarehouseService.updateDeliveryWarehouse(deliveryWarehouseEntity));
    }

    @Override
    public Result<Boolean> updateDeliveryWarehouseStatus(Integer warehouseId, Integer status) {
        return Result.success(deliveryWarehouseService.updateDeliveryWarehouseStatus(warehouseId, status));
    }

    @Override
    public Result<List<DeliveryWarehouseEntity>> getDeliveryWarehouseList(DeliveryWarehouseDTO warehouseDTO) {
        return Result.success(deliveryWarehouseService.getDeliveryWarehouseList(warehouseDTO));
    }

    @Override
    public Result<DeliveryWarehouseEntity> getDeliveryWarehouseById(Integer warehouseId) {
        return Result.success(deliveryWarehouseService.getDeliveryWarehouseById(warehouseId));
    }
}
