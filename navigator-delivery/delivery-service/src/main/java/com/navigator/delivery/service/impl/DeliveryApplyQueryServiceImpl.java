package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerDeliveryWhiteFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.delivery.dao.DeliveryApplyContractDao;
import com.navigator.delivery.dao.DeliveryApplyDao;
import com.navigator.delivery.dao.DeliveryApplyDriverLogDao;
import com.navigator.delivery.dao.DeliveryApplyVODao;
import com.navigator.delivery.pojo.dto.DeliveryApplyAssignContractDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyDriverLogDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyRpaExportDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryBlockedStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryQueueStatusEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;
import com.navigator.delivery.service.IDeliveryApplyQueryService;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 提货申请表 查询服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class DeliveryApplyQueryServiceImpl implements IDeliveryApplyQueryService {
    // Dao
    private final DeliveryApplyDao deliveryApplyDao;
    private final DeliveryApplyVODao deliveryApplyVODao;
    private final DeliveryApplyDriverLogDao deliveryApplyDriverLogDao;
    private final DeliveryApplyContractDao deliveryApplyContractDao;
    // facade
    private final ContractFacade contractFacade;
    private final CEmployFacade cEmployFacade;
    private final CustomerFacade customerFacade;
    private final OperationLogFacade operationLogFacade;
    private final DeliveryTypeFacade deliveryTypeFacade;
    private final WarehouseFacade warehouseFacade;
    private final FileBusinessFacade fileBusinessFacade;
    private final SystemRuleFacade systemRuleFacade;
    private final SkuFacade skuFacade;
    private final CustomerDeliveryWhiteFacade customerDeliveryWhiteFacade;
    private final SiteFacade siteFacade;

    @Value("${navigator.showWaitApprovePermission.customerStatus:false}")
    private Boolean customerStatus;

    @Value("${navigator.showWaitApprovePermission.customerIdList:}")
    private List<Integer> customerIdList;

    @Override
    public List<DeliveryApplyContractVO> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO) {

        // 如果没有传入客户id，则默认为当前登录用户的客户id
        if (null == deliveryApplyContractQO.getCustomerId()) {
            deliveryApplyContractQO.setCustomerId(cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getCustomerId());
        }

        // 卖方主体
        CustomerEntity supplier = customerFacade.getCustomerById(deliveryApplyContractQO.getSupplierId());
        // 账套
        SiteEntity siteEntity = siteFacade.getSiteByCompanyIdAndFactoryCode(supplier.getCompanyId(), deliveryApplyContractQO.getDeliveryFactoryCode());

        // 校验账套是否正确
        if (siteEntity == null || !SyncSystemEnum.LINKINAGE.getValue().equals(siteEntity.getSyncSystem())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_SITE_EXCEPTION);
        }

        List<DeliveryApplyContractVO> contractVOList = new ArrayList<>();
        // 调用合同服务接口
        Result result = contractFacade.getDeliveryApplyContractGroup(deliveryApplyContractQO);
        if (result.isSuccess()) {
            contractVOList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyContractVO.class);
        }
        return contractVOList;
    }


    @Override
    public IPage<DeliveryApplyVOEntity> getDeliveryApplyList(QueryDTO<DeliveryApplyQO> queryDTO) {
        // 判断触发系统
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        if (null != applyQO.getTriggerSys() && applyQO.getTriggerSys().equals(SystemEnum.COLUMBUS.getValue())) {

            CustomerEntity customerEntity = customerFacade.queryCustomerById(applyQO.getCustomerId());
            if (Objects.equals(DisableStatusEnum.DISABLE.getValue(), customerEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
            }

            String currentUserId = JwtUtils.getCurrentUserId();
            CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(currentUserId));
            if (Objects.equals(DisableStatusEnum.DISABLE.getValue(), cEmployEntity.getStatus())) {
                throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
            }

            // 清空页码
            int pageSize = queryDTO.getPageSize();
            int pageNo = queryDTO.getPageNo();
            queryDTO.setPageNo(1);
            // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start
            queryDTO.setPageSize(-1);
            // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start

            // 如果是哥伦布系统，则需要根据当前登录用户的客户id查询
            IPage<DeliveryApplyVOEntity> deliveryApplyList = processDeliveryApplyList(queryDTO);

            // 所有状态中，对待审核状态进行去重，但是审核通过状态不去重
            List<DeliveryApplyVOEntity> records = deliveryApplyList.getRecords();
            if (CollectionUtil.isNotEmpty(records)) {
                List<DeliveryApplyVOEntity> distinctList;
                // atlas
                if (SystemEnum.ATLAS.getValue().equals(applyQO.getThirdSys())) {
                    Map<String, Boolean> whiteMap = new HashMap<>();
                    records.forEach(deliveryApplyVOEntity -> {
                        // ATLAS - COLUMBUS - 待审核 - 判断是否白名单
                        if (deliveryApplyVOEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                            // 白名单则显示合同,否则隐藏
                            String key = deliveryApplyVOEntity.getCustomerId() + "-" + deliveryApplyVOEntity.getGoodsId();
                            Boolean white = whiteMap.get(key);
                            if (white == null) {
                                CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO = new CustomerDeliveryWhiteDTO();
                                SkuEntity skuEntity = skuFacade.getSkuById(deliveryApplyVOEntity.getGoodsId());
                                // 商品不存在则不处理
                                if (skuEntity == null) {
                                    return;
                                }
                                customerDeliveryWhiteDTO.setCustomerId(deliveryApplyVOEntity.getCustomerId());
                                customerDeliveryWhiteDTO.setCategory1(skuEntity.getCategory1().toString());
                                customerDeliveryWhiteDTO.setCategory2(skuEntity.getCategory2().toString());
                                customerDeliveryWhiteDTO.setCategory3(skuEntity.getCategory3().toString());
                                customerDeliveryWhiteDTO.setStatus(1);
                                List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOList = customerDeliveryWhiteFacade.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
                                white = CollUtil.isNotEmpty(customerDeliveryWhiteDTOList);
                                whiteMap.put(key, white);
                            }
                            // 非白名单且配置列表没有该客户，则隐藏
                            log.info("客户id:{},白名单结果:{},非白名单列表配置开启状态(true：仅展示列表中的数据。false：展示所有数据)：{},客户id:{}", deliveryApplyVOEntity.getCustomerId(), white, customerStatus, customerIdList);
                            // 仅当 customerStatus 为 true 时进行验证
                            if (customerStatus) {
                                if (!white && !customerIdList.contains(deliveryApplyVOEntity.getCustomerId())) {
                                    deliveryApplyVOEntity
                                            .setContractCode(null)
                                            .setAllocateNum(null)
                                            .setUnitPrice(null)
                                            .setAllocateAmount(null)
                                            .setContractType(null)
                                            .setDeliveryTypeName(null)
                                            .setShipWarehouseName(null)
                                            .setContractSignDate(null)
                                            .setDeliveryStartTime(null)
                                            .setDeliveryEndTime(null);
                                }
                            }
                        }
                    });
                    distinctList = records;
                } else {
                    // 待审核状态
                    List<DeliveryApplyVOEntity> waitApproveList = records.stream().filter(deliveryApplyVOEntity -> deliveryApplyVOEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()).collect(Collectors.toList());
                    log.info("待审核状态，记录数={}", waitApproveList.size());
                    // 其他状态
                    List<DeliveryApplyVOEntity> otherList = records.stream().filter(deliveryApplyVOEntity -> deliveryApplyVOEntity.getApplyStatus() != DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()).collect(Collectors.toList());
                    log.info("其他状态，记录数={}", otherList.size());

                    // 过滤掉待审核状态中的重复数据
                    if (StrUtil.isNotBlank(applyQO.getBusinessCode())) {
                        waitApproveList = waitApproveList.stream().filter(deliveryApplyVOEntity -> {
                            return deliveryApplyVOEntity.getContractCode() == null || !deliveryApplyVOEntity.getContractCode().contains(applyQO.getBusinessCode());
                        }).collect(Collectors.toList());
                    }

                    // 待审核状态去重
                    distinctList = waitApproveList.stream()
                            .map(deliveryApplyVOEntity -> deliveryApplyVOEntity
                                    .setContractCode(null)
                                    .setAllocateNum(null)
                                    .setUnitPrice(null)
                                    .setAllocateAmount(null)
                                    .setContractType(null)
                                    .setDeliveryTypeName(null)
                                    .setShipWarehouseName(null)
                                    .setContractSignDate(null)
                                    .setDeliveryStartTime(null)
                                    .setDeliveryEndTime(null))
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                    new TreeSet<>(Comparator.comparing(DeliveryApplyVOEntity::getCode))), ArrayList::new));
                    log.info("待审核状态去重，记录数={}", waitApproveList.size());

                    // 合并
                    distinctList.addAll(otherList);
                    log.info("合并后，记录数={}", distinctList.size());
                }
                // 排序
                if (null == applyQO.getApplyStatus() || applyQO.getApplyStatus() == DeliveryApplyStatusEnum.NEW.getValue()) {
                    distinctList.sort(Comparator.comparing(DeliveryApplyVOEntity::getCreatedAt).reversed());
                } else {
                    distinctList.sort(Comparator.comparing(DeliveryApplyVOEntity::getUpdatedAt).reversed());
                }

                // 分页参数重新设置
                long pages = distinctList.size() / queryDTO.getPageSize();
                if (distinctList.size() % queryDTO.getPageSize() != 0) {
                    pages++;
                }

                // 总条数
                int total = distinctList.size();

                // 对distinctList进行分页
                // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-06-06 Start
                if (pageSize > 0) {
                    distinctList = distinctList.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
                }
                // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-06-06 End
                deliveryApplyList
                        .setCurrent(pageNo)
                        .setSize(pageSize)
                        .setRecords(distinctList)
                        .setTotal(total)
                        .setPages(pages);
                return deliveryApplyList;
            }
        }
        return processDeliveryApplyList(queryDTO);
    }

    private IPage<DeliveryApplyVOEntity> processDeliveryApplyList(QueryDTO<DeliveryApplyQO> queryDTO) {
        DeliveryApplyQO deliveryApplyQO = queryDTO.getCondition();

        // 获取提货申请列表
        IPage<DeliveryApplyVOEntity> deliveryApplyList = deliveryApplyVODao.getDeliveryApplyList(queryDTO);
        log.info("获取提货申请列表，总记录数={}", deliveryApplyList.getRecords().size());

        // ATLAS列表需要展示
        if (deliveryApplyQO != null && SystemEnum.ATLAS.getValue().equals(deliveryApplyQO.getThirdSys())) {
            // 触发系统
            Integer triggerSys = deliveryApplyQO.getTriggerSys();

            // 处理每个提货申请记录
            deliveryApplyList.getRecords().forEach(deliveryApplyVOEntity -> {
                // 车辆状态
                deliveryApplyVOEntity.setCarStatusName(DeliveryQueueStatusEnum.getDescByValue(deliveryApplyVOEntity.getQueueStatus()));

                // 锁定原因
                DeliveryBlockedStatusEnum statusEnum = DeliveryBlockedStatusEnum.getByStatusAndSubStatus(deliveryApplyVOEntity.getAtlasApplyStatus(), deliveryApplyVOEntity.getAtlasSubStatus());
                String blockedRemark = "";

                // 根据不同的触发系统设置锁定原因
                if (null != triggerSys && Objects.equals(SystemEnum.MAGELLAN.getValue(), triggerSys)) {
                    blockedRemark = statusEnum.getMagellanMessage();
                } else {
                    blockedRemark = statusEnum.getColumbusMessage();

                    // 如果没有Columbus消息，使用审批评论作为锁定原因
                    if (StringUtil.isBlank(blockedRemark)) {
                        blockedRemark = deliveryApplyVOEntity.getApprovalComments();
                    }
                }

                deliveryApplyVOEntity.setBlockedRemark(blockedRemark);

                // 实际提货数量
                if (deliveryApplyVOEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue()) {
                    // 实际提货数量
                    BigDecimal executedNum = deliveryApplyVOEntity.getExecutedNum();
                    deliveryApplyVOEntity.setExecutedNum(executedNum);

                    // 开单未提货数量
                    BigDecimal allocateNum = deliveryApplyVOEntity.getAllocateNum();
                    // deliveryApplyVOEntity.setUnExecutedNum(allocateNum == null || executedNum == null ? null : allocateNum.subtract(executedNum));

                    // 当ATLAS传给实际提货量给NVG，即实际提货量有值的时候，“开单未提量”展示为0
                    deliveryApplyVOEntity.setUnExecutedNum(executedNum != null && BigDecimalUtil.isGreaterThanZero(executedNum) ? BigDecimal.ZERO : allocateNum);
                }
            });
        }
        return deliveryApplyList;
    }

    @Override
    public DeliveryApplyDetailVO getDeliveryApplyDetailById(Integer applyId, String triggerSys) {
        // 查询申请单详情
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyId);
        DeliveryApplyDetailVO applyDetailVO = BeanUtil.toBean(applyEntity, DeliveryApplyDetailVO.class);

        // 商品名称
        if (StringUtil.isNotNullBlank(applyDetailVO.getGoodsId())) {
            applyDetailVO.setGoodsName(IdNameConverter.getName(IdNameType.sku_id_name, applyDetailVO.getGoodsId().toString()));
        }

        // 买方主体
        CustomerEntity customerEntity = customerFacade.queryCustomerById(applyDetailVO.getCustomerId());
        if (null != customerEntity) {
            applyDetailVO.setCustomerName(customerEntity.getName());
        }

        // 卖方主体
        CustomerEntity supplierEntity = customerFacade.queryCustomerById(applyDetailVO.getSupplierId());
        if (null != supplierEntity) {
            applyDetailVO.setSupplierName(supplierEntity.getName());
        }

        // 提货库点
        Result<WarehouseEntity> warehouse = warehouseFacade.getWarehouseById(applyDetailVO.getDeliveryWarehouseId());
        if (null != warehouse && warehouse.isSuccess() && warehouse.getData() != null) {
            applyDetailVO.setDeliveryWarehouseName(warehouse.getData().getName());
        }

        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
        // 车辆类型
        if (null != applyDetailVO.getCarType()) {
            SystemRuleItemEntity carTypeEntity = systemRuleFacade.getRuleItemById(applyDetailVO.getCarType());
            if (null != carTypeEntity) {
                applyDetailVO.setCarTypeName(carTypeEntity.getRuleValue());
            }
        }
        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

        // 车辆状态
        applyDetailVO.setCarStatusName(DeliveryQueueStatusEnum.getDescByValue(applyEntity.getQueueStatus()));

        // 锁定原因
        DeliveryBlockedStatusEnum statusEnum = DeliveryBlockedStatusEnum.getByStatusAndSubStatus(applyEntity.getAtlasApplyStatus(), applyEntity.getAtlasSubStatus());
        String blockedRemark = "";

        // 根据不同的触发系统设置锁定原因
        if (null != triggerSys && Objects.equals(SystemEnum.MAGELLAN.getName(), triggerSys)) {
            blockedRemark = statusEnum.getMagellanMessage();
        } else {
            blockedRemark = statusEnum.getColumbusMessage();

            // 如果没有Columbus消息，使用审批评论作为锁定原因
            if (StringUtil.isBlank(blockedRemark)) {
                blockedRemark = applyEntity.getApprovalComments();
            }
        }
        applyDetailVO.setBlockedRemark(blockedRemark);

        // 查询申请单合同列表 - MAGELLAN系统可以查询列表，COLUMBUS只有当待审核状态不查询列表
        Boolean showContract = true;
        if (SystemEnum.ATLAS.getValue().equals(applyEntity.getThirdSys())) {
            //ATLAS - COLUMBUS - 待审核 - 判断是否白名单
            if ((SystemEnum.COLUMBUS.getName().equals(triggerSys) && applyDetailVO.getApplyStatus().equals(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()))) {
                CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO = new CustomerDeliveryWhiteDTO();
                SkuEntity skuEntity = skuFacade.getSkuById(applyDetailVO.getGoodsId());
                customerDeliveryWhiteDTO.setCustomerId(applyDetailVO.getCustomerId());
                customerDeliveryWhiteDTO.setCategory1(skuEntity.getCategory1().toString());
                customerDeliveryWhiteDTO.setCategory2(skuEntity.getCategory2().toString());
                customerDeliveryWhiteDTO.setCategory3(skuEntity.getCategory3().toString());
                customerDeliveryWhiteDTO.setStatus(1);
                List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOList = customerDeliveryWhiteFacade.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
                boolean isWhite = CollUtil.isNotEmpty(customerDeliveryWhiteDTOList);

                // 非白名单且配置列表没有该客户，则隐藏
                log.info("客户id:{},白名单结果:{},非白名单列表配置开启状态(true：仅展示列表中的数据。false：展示所有数据)：{},客户id:{}", applyDetailVO.getCustomerId(), isWhite, customerStatus, customerIdList);
                // 仅当 customerStatus 为 true 时进行验证
                if (customerStatus) {
                    if (!isWhite && !customerIdList.contains(applyDetailVO.getCustomerId())) {
                        showContract = false;
                    }
                }
            }
        } else {
            // LKG -  MAGELLAN系统可以查询列表，COLUMBUS只有当待审核状态不查询列表
            showContract = SystemEnum.MAGELLAN.getName().equals(triggerSys) ||
                    (SystemEnum.COLUMBUS.getName().equals(triggerSys) && applyDetailVO.getApplyStatus() != DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
        }

        if (showContract) {
            // 实际提货数量
            BigDecimal allocateNum = BigDecimal.ZERO;
            BigDecimal executedNum = BigDecimal.ZERO;

            List<DeliveryApplyContractEntity> contractEntityList = deliveryApplyContractDao.getByApplyId(applyId);
            for (DeliveryApplyContractEntity deliveryApplyContractEntity : contractEntityList) {
                deliveryApplyContractEntity.setContractTypeName(ContractTypeEnum.getDescByValue(deliveryApplyContractEntity.getContractType()));

                // 交提货方式
                String deliveryTypeName = deliveryTypeFacade.getDeliveryTypeById(deliveryApplyContractEntity.getDeliveryType()).getName();
                deliveryApplyContractEntity.setDeliveryTypeName(deliveryTypeName);

                // 发货库点
                Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(deliveryApplyContractEntity.getShipWarehouseId());
                if (result.isSuccess()) {
                    WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                    deliveryApplyContractEntity.setShipWarehouseName(warehouseEntity.getName());
                }

                // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
                // 付款方式
                String paymentTypeName = PaymentTypeEnum.getDescByValue(deliveryApplyContractEntity.getPaymentType());
                deliveryApplyContractEntity.setPaymentTypeName(paymentTypeName);
                // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

                // 计算实际提货数量
                allocateNum = allocateNum.add(Optional.ofNullable(deliveryApplyContractEntity.getAllocateNum()).orElse(BigDecimal.ZERO));
                executedNum = executedNum.add(Optional.ofNullable(deliveryApplyContractEntity.getExecutedNum()).orElse(BigDecimal.ZERO));
            }
            applyDetailVO.setAllocateNum(allocateNum);
            applyDetailVO.setExecutedNum(executedNum);

            // 当ATLAS传给实际提货量给NVG，即实际提货量有值的时候，“开单未提量”展示为0
            // applyDetailVO.setUnExecutedNum(allocateNum.subtract(executedNum));
            // 1003234 修改开单未提的取值位置 changed by Jason Shi at 2025-1-15 start
            // 开单未提量 = 分配数量 - 实际提货量
            BigDecimal unExecutedNum = allocateNum.subtract(executedNum);
            // 如果小于0则显示0，否则显示实际差值
            applyDetailVO.setUnExecutedNum(unExecutedNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : unExecutedNum);
            // 1003234 修改开单未提的取值位置 changed by Jason Shi at 2025-1-15 end

            applyDetailVO.setContractList(contractEntityList);
        }

        // 查询申请单附件列表
        applyDetailVO.setFileList(getFileListByApplyId(applyId));

        // 查询申请单审核记录
        String approvalInfo = applyEntity.getApprovalInfo();
        if (null != approvalInfo) {
            List<DeliveryApplyApprovalVO> deliveryApplyApprovalVOS = JSON.parseArray(approvalInfo, DeliveryApplyApprovalVO.class);
            applyDetailVO.setApprovalRemark(CollectionUtil.isNotEmpty(deliveryApplyApprovalVOS) ? deliveryApplyApprovalVOS.get(deliveryApplyApprovalVOS.size() - 1).getApprovalRemark() : "");
        }

        return applyDetailVO;
    }

    @Override
    public List<DeliveryApplyFileVO> getFileListByApplyId(Integer applyId) {
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyId);

        List<FileInfoEntity> fileInfoEntityList = fileBusinessFacade.getFileInfoByBizIdAndType(applyId,
                FileCategoryType.DELIVERY_APPLY.getCode(), DisableStatusEnum.ENABLE.getValue());

        return fileInfoEntityList.stream().map(fileInfoEntity -> new DeliveryApplyFileVO()
                .setFileId(fileInfoEntity.getId())
                .setFileName(fileInfoEntity.getOriginalFileName())
                .setFilePath(fileInfoEntity.getPath())
                .setFileUrl(fileInfoEntity.getFileUrl())
                .setUploadTime(fileInfoEntity.getCreatedAt())
                .setUploadUser(applyEntity.getCreatedBy())).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryApplyApprovalVO> getAuditRecordListByApplyId(Integer applyId) {
        // 查询申请单详情
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyId);

        if (null != applyEntity) {
            // 查询申请单审核记录
            String approvalInfo = applyEntity.getApprovalInfo();
            if (null != approvalInfo) {
                return JSON.parseArray(approvalInfo, DeliveryApplyApprovalVO.class);
            }
        }
        return null;
    }

    @Override
    public List<DeliveryApplyOperationVO> getOperateRecordListByApplyCode(String applyCode) {
        // 查询申请单详情
        Result result = operationLogFacade.queryOperationDetailByReferBizCode(applyCode, OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue());
        if (result.isSuccess()) {
            List<OperationDetailEntity> operationDetailList = JSON.parseArray(JSON.toJSONString(result.getData()), OperationDetailEntity.class);

            return operationDetailList.stream().map(operationDetailEntity -> new DeliveryApplyOperationVO()
                    .setOperationUser(operationDetailEntity.getOperatorName())
                    .setOperationTime(operationDetailEntity.getCreatedAt())
                    .setOperationContent(operationDetailEntity.getOperationName())
                    .setInvalidReason(operationDetailEntity.getOperationInfo())).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public DeliveryApplyAssignContractVO getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO) {
        DeliveryApplyAssignContractVO assignContractVO = new DeliveryApplyAssignContractVO();

        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(assignContractDTO.getApplyId());

        Map<Integer, DeliveryApplyContractEntity> tempAssignedContractMap = new HashMap<>();
        Map<Integer, DeliveryApplyContractEntity> assignedContractMap = new HashMap<>();
        if (null != applyEntity) {
            // 已分配列表
            List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getByApplyId(assignContractDTO.getApplyId());

            // 暂存已分配列表
            List<Integer> contractIdList = new ArrayList<>();
            for (DeliveryApplyContractEntity applyContractEntity : applyContractEntityList) {
                tempAssignedContractMap.put(applyContractEntity.getContractId(), applyContractEntity);
                contractIdList.add(applyContractEntity.getContractId());
            }

            Result applyResult = contractFacade.getContractListByIds(contractIdList);

            if (applyResult.isSuccess()) {
                List<ContractEntity> applyContractList = JSON.parseArray(JSON.toJSONString(applyResult.getData()), ContractEntity.class);
                List<DeliveryApplyContractEntity> deliveryApplyContractEntityList = applyContractList.stream().map(contractEntity -> {
                    DeliveryApplyContractEntity applyContractEntity = tempAssignedContractMap.get(contractEntity.getId());

                    // 发货库点
                    String shipWarehouseName = "";
                    Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(contractEntity.getShipWarehouseId());
                    if (result.isSuccess()) {
                        WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                        shipWarehouseName = warehouseEntity.getName();
                    }
                    applyContractEntity.setContractTypeName(ContractTypeEnum.getDescByValue(contractEntity.getContractType()))
                            .setCanAllocateNum(contractEntity.getCanDeliveryNum().add(applyContractEntity.getAllocateNum()))
                            .setContractNum(contractEntity.getContractNum())
                            .setDeliveryTypeName(deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType()).getName())
                            // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
                            .setShipWarehouseName(shipWarehouseName)
                            .setPaymentTypeName(PaymentTypeEnum.getDescByValue(contractEntity.getPaymentType()));
                    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

                    assignedContractMap.put(contractEntity.getId(), applyContractEntity);
                    return applyContractEntity;
                }).collect(Collectors.toList());
                assignContractVO.setAssignedContractList(deliveryApplyContractEntityList);
            }

            // 可分配列表
            DeliveryApplyContractQO applyContractQO = BeanUtil.toBean(assignContractDTO, DeliveryApplyContractQO.class);

            Result result = contractFacade.getDeliveryApplyContractList(applyContractQO
                    .setGoodsId(applyEntity.getGoodsId())
                    .setCustomerId(applyEntity.getCustomerId())
                    .setSupplierId(applyEntity.getSupplierId())
                    .setDeliveryFactoryCode(applyEntity.getDeliveryFactoryCode()));
            if (result.isSuccess()) {
                List<ContractEntity> contractList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractEntity.class);

                List<DeliveryApplyContractEntity> applyContractList = contractList.stream().map(contractEntity -> {
                    DeliveryApplyContractEntity applyContractEntity = new DeliveryApplyContractEntity();
                    // 可分配列表-系统推荐分配量+分配金额
                    if (assignedContractMap.containsKey(contractEntity.getId())) {
                        applyContractEntity.setRecommendAllocateNum(assignedContractMap.get(contractEntity.getId()).getAllocateNum())
                                .setAllocateAmount(assignedContractMap.get(contractEntity.getId()).getAllocateAmount());
                    }

                    // 发货库点
                    String shipWarehouseName = "";
                    Result<WarehouseEntity> warehouseResult = warehouseFacade.getWarehouseById(contractEntity.getShipWarehouseId());
                    if (warehouseResult.isSuccess()) {
                        WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(warehouseResult.getData()), WarehouseEntity.class);
                        shipWarehouseName = warehouseEntity.getName();
                    }
                    return applyContractEntity
                            .setContractId(contractEntity.getId())
                            .setContractCode(contractEntity.getContractCode())
                            .setCanAllocateNum(contractEntity.getCanDeliveryNum())
                            .setContractNum(contractEntity.getContractNum())
                            .setUnitPrice(contractEntity.getUnitPrice())
                            .setContractType(contractEntity.getContractType())
                            .setDeliveryStartTime(contractEntity.getDeliveryStartTime())
                            .setDeliveryEndTime(contractEntity.getDeliveryEndTime())
                            .setDeliveryTypeName(deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType()).getName())
                            // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
                            .setContractSignDate(contractEntity.getSignDate())
                            .setShipWarehouseName(shipWarehouseName)
                            .setPaymentTypeName(PaymentTypeEnum.getDescByValue(contractEntity.getPaymentType()));
                    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End
                }).collect(Collectors.toList());
                assignContractVO.setCanAssignContractList(applyContractList);
            }
        }
        return assignContractVO;
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @Override
    public List<DeliveryApplyDriverLogEntity> getDriverInputRecord(Integer customerId, Integer goodsCategoryId) {
        // 查询最近10次司机输入记录
        return deliveryApplyDriverLogDao.getDriverInputRecord(customerId, goodsCategoryId);
    }

    @Override
    public List<DeliveryApplyDriverLogEntity> getDriverRecordByCondition(DeliveryApplyDriverLogDTO deliveryApplyDriverLogDTO) {
        return deliveryApplyDriverLogDao.getDriverRecordByCondition(deliveryApplyDriverLogDTO);
    }
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @Override
    public List<DeliveryApplyRpaExportDTO> getRpaDeliveryApplyList() {
        List<DeliveryApplyVOEntity> applyVOEntityList = deliveryApplyVODao.list();
        return applyVOEntityList.stream().map(deliveryApplyVOEntity -> {
            DeliveryApplyRpaExportDTO deliveryApplyRpaExportDTO = new DeliveryApplyRpaExportDTO();

            deliveryApplyRpaExportDTO
                    .setItem(deliveryApplyVOEntity.getRowId())
                    .setOutboundDate(deliveryApplyVOEntity.getCreatedAt())
                    .setWarehousePort(deliveryApplyVOEntity.getDeliveryWarehouseName())
                    .setContactNo(deliveryApplyVOEntity.getContractCode())
                    .setGoodsName(deliveryApplyVOEntity.getGoodsName())
                    .setDeliveryNum(deliveryApplyVOEntity.getApplyNum())
                    .setGoodsCode(deliveryApplyVOEntity.getLinkageGoodsCode())
                    .setGoodsSpec(deliveryApplyVOEntity.getGoodsSpec())
                    .setTruckNo(deliveryApplyVOEntity.getPlateNumber())
                    .setContactNo(deliveryApplyVOEntity.getDriverName() + deliveryApplyVOEntity.getOnboardPhone())
                    .setIdNo(deliveryApplyVOEntity.getDriverIdNumber())
                    .setDeliveryLocation("")
                    .setCustomerName(deliveryApplyVOEntity.getCustomerName())
                    // 根据提货委托中的车船号，如果存在大写字母则写入“汽车”；否则写入“驳船”
                    .setDeliveryType(deliveryApplyVOEntity.getPlateNumber().matches(".*[A-Z]+.*") ? "汽车" : "驳船")
                    .setDeliveryApplyCode(deliveryApplyVOEntity.getCode());
            return deliveryApplyRpaExportDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryApplyVOEntity> getDeliveryApplyListByIds(List<Integer> applyIds) {
        // 1003234 修改开单未提的取值位置 changed by Jason Shi at 2025-1-15 start
        List<DeliveryApplyVOEntity> deliveryApplyList = deliveryApplyVODao.getDeliveryApplyListByIds(applyIds);

        // 1003234 修复车辆状态在Excel导出中为空的问题 changed by Jason Shi at 2025-6-18 start
        // 为每个记录设置车辆状态
        deliveryApplyList.forEach(deliveryApplyVOEntity -> {
            // 车辆状态
            deliveryApplyVOEntity.setCarStatusName(DeliveryQueueStatusEnum.getDescByValue(deliveryApplyVOEntity.getQueueStatus()));
        });
        // 1003234 修复车辆状态在Excel导出中为空的问题 changed by Jason Shi at 2025-6-18 end

        // 按DR分组计算unExecutedNum字段，确保相同DR的所有记录显示相同的开单未提量
        // 先按DR分组汇总allocateNum和executedNum
        Map<Integer, BigDecimal> drTotalAllocateNumMap = new HashMap<>();
        Map<Integer, BigDecimal> drTotalExecutedNumMap = new HashMap<>();

        // 第一遍遍历：按DR汇总所有合同的allocateNum和executedNum
        deliveryApplyList.forEach(deliveryApplyVOEntity -> {
            Integer applyId = deliveryApplyVOEntity.getId();
            BigDecimal allocateNum = deliveryApplyVOEntity.getAllocateNum() != null ? deliveryApplyVOEntity.getAllocateNum() : BigDecimal.ZERO;
            BigDecimal executedNum = deliveryApplyVOEntity.getExecutedNum() != null ? deliveryApplyVOEntity.getExecutedNum() : BigDecimal.ZERO;

            drTotalAllocateNumMap.put(applyId, drTotalAllocateNumMap.getOrDefault(applyId, BigDecimal.ZERO).add(allocateNum));
            drTotalExecutedNumMap.put(applyId, drTotalExecutedNumMap.getOrDefault(applyId, BigDecimal.ZERO).add(executedNum));
        });

        // 第二遍遍历：为每个记录设置对应DR的unExecutedNum
        deliveryApplyList.forEach(deliveryApplyVOEntity -> {
            Integer applyId = deliveryApplyVOEntity.getId();
            BigDecimal totalAllocateNum = drTotalAllocateNumMap.get(applyId);
            BigDecimal totalExecutedNum = drTotalExecutedNumMap.get(applyId);

            // 计算DR级别的开单未提量 = DR总分配数量 - DR总实际提货量
            BigDecimal drUnExecutedNum = totalAllocateNum.subtract(totalExecutedNum);
            // 如果小于0则显示0，否则显示实际差值
            deliveryApplyVOEntity.setUnExecutedNum(drUnExecutedNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : drUnExecutedNum);
        });

        return deliveryApplyList;
        // 1003234 修改开单未提的取值位置 changed by Jason Shi at 2025-1-15 end
    }

    @Override
    public Boolean isContractInDeliveryAudit(Integer contractId) {
        List<DeliveryApplyContractEntity> contractEntityList = deliveryApplyContractDao.getByContractId(contractId);
        // 判断合同对应的提货申请单是否存在待审核状态
        if (CollectionUtil.isNotEmpty(contractEntityList)) {
            List<Integer> applyIdList = contractEntityList.stream().map(DeliveryApplyContractEntity::getApplyId).collect(Collectors.toList());
            List<DeliveryApplyEntity> applyEntityList = (List<DeliveryApplyEntity>) deliveryApplyDao.listByIds(applyIdList);
            if (CollectionUtil.isNotEmpty(applyEntityList)) {
                for (DeliveryApplyEntity applyEntity : applyEntityList) {
                    if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public DeliveryApplyEntity getDeliveryApplyById(Integer applyId) {
        return deliveryApplyDao.getById(applyId);
    }

    @Override
    public DeliveryApplyEntity getDeliveryApplyByCode(String applyCode) {
        return deliveryApplyDao.getDeliveryApplyByCode(applyCode);
    }
}
