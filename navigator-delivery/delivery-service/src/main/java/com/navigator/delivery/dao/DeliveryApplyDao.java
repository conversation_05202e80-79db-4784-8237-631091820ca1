package com.navigator.delivery.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.delivery.mapper.DeliveryApplyMapper;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.entity.BaseDeliveryApplyEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 提货申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Dao
@Slf4j
public class DeliveryApplyDao extends BaseDaoImpl<DeliveryApplyMapper, DeliveryApplyEntity> {

    public Boolean updateApplyStatusByCode(String applyCode, Integer status) {
        return this.lambdaUpdate()
                .eq(DeliveryApplyEntity::getCode, applyCode)
                .set(DeliveryApplyEntity::getApplyStatus, status)
                .update();
    }

    public DeliveryApplyEntity getDeliveryApplyByCode(String applyCode) {
        return this.lambdaQuery()
                .eq(DeliveryApplyEntity::getCode, applyCode)
                .one();
    }

    public List<ExchangePickQtyDTO> getExchangePickQty(ExchangePickQtyQO pickQtyQO) {
        log.info("getExchangePickQty param: {}", pickQtyQO);
        List<ExchangePickQtyDTO> exchangePickQtyDTOS = this.getBaseMapper().callExchangePickQty(pickQtyQO);
        log.info("getExchangePickQty result: {}", exchangePickQtyDTOS);
        return exchangePickQtyDTOS;
    }

    public List<ExecutePreAllocationDTO> getExecutePreAllocation(ExecutePreAllocationQO preAllocationQO) {
        log.info("getExecutePreAllocation param: {}", preAllocationQO);
        List<ExecutePreAllocationDTO> executePreAllocationDTOS = this.getBaseMapper().callExecutePreAllocation(preAllocationQO);
        log.info("getExecutePreAllocation result: {}", executePreAllocationDTOS);
        return executePreAllocationDTOS;
    }

    /**
     * 检查拼车信息
     *
     * @param deliveryApplyEntity
     * @return
     */
    public String checkCarpool(DeliveryApplyEntity deliveryApplyEntity, boolean exception) {
        if (deliveryApplyEntity.getIsCarpool().equals(1)) {
            List<DeliveryApplyEntity> list = this.list(new LambdaQueryWrapper<DeliveryApplyEntity>()
                    .ne(deliveryApplyEntity.getId() != null, BaseDeliveryApplyEntity::getId, deliveryApplyEntity.getId())
                    .eq(BaseDeliveryApplyEntity::getIsCarpool, 1)
                    .eq(BaseDeliveryApplyEntity::getPlateNumber, deliveryApplyEntity.getPlateNumber())
                    .eq(BaseDeliveryApplyEntity::getPlanDeliveryTime, deliveryApplyEntity.getPlanDeliveryTime())
                    .in(BaseDeliveryApplyEntity::getApplyStatus,
                            new Integer[]{DeliveryApplyStatusEnum.BLOCKED.getValue(),
                                    DeliveryApplyStatusEnum.WAIT_APPROVE.getValue(),
                                    DeliveryApplyStatusEnum.APPROVED.getValue()}));
            for (DeliveryApplyEntity item : list) {
                if (!item.getCarpoolCount().equals(deliveryApplyEntity.getCarpoolCount())) {
                    if (exception) {
                        throw new BusinessException("拼车方数量错误，需与拼车/船方沟通后重新填写");
                    }
                    return "共几方拼车船";
                }
                if (item.getLoadingPriority().equals(deliveryApplyEntity.getLoadingPriority())) {
                    if (exception) {
                        throw new BusinessException("装货优先级重复，需与拼车/船方沟通后重新填写");
                    }
                    return "装货优先级";
                }
            }
        }
        return null;
    }

    public String getMaxDeliveryApplyCode(Integer goodsCategoryId, Integer supplierId, String deliveryFactory) {
        // 按照条件查询最大的申请编号
        IPage<DeliveryApplyEntity> page = this.page(new Page<>(1, 1),
                Wrappers.<DeliveryApplyEntity>lambdaQuery()
                        .eq(DeliveryApplyEntity::getGoodsCategoryId, goodsCategoryId)
                        .eq(DeliveryApplyEntity::getSupplierId, supplierId)
                        .eq(DeliveryApplyEntity::getDeliveryFactoryCode, deliveryFactory)
                        .orderByDesc(DeliveryApplyEntity::getId));

        return !page.getRecords().isEmpty() ? page.getRecords().get(0).getCode() : "";
    }

    public List<DeliveryApplyEntity> getCarpoolDeliveryApplyList(String plateNumber, String planDeliveryTime) {
        return this.list(Wrappers.<DeliveryApplyEntity>lambdaQuery()
                .eq(DeliveryApplyEntity::getIsCarpool, 1)
                .eq(DeliveryApplyEntity::getPlateNumber, plateNumber)
                .eq(DeliveryApplyEntity::getPlanDeliveryTime, planDeliveryTime)
                .in(BaseDeliveryApplyEntity::getApplyStatus, Arrays.asList(DeliveryApplyStatusEnum.BLOCKED.getValue(),
                        DeliveryApplyStatusEnum.WAIT_APPROVE.getValue(),
                        DeliveryApplyStatusEnum.APPROVED.getValue())));
    }
}
