package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.constant.DeliveryConstant;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.customer.facade.CustomerDeliveryWhiteFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.GeneralEnum;
import com.navigator.customer.pojo.enums.TTCustomerTradeStatusEnum;
import com.navigator.delivery.dao.DeliveryApplyAllocateDao;
import com.navigator.delivery.dao.DeliveryApplyContractDao;
import com.navigator.delivery.dao.DeliveryApplyDao;
import com.navigator.delivery.pojo.dto.DeliveryApplyAssignContractDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyCarpoolInfoDTO;
import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.PreCheckDeliveryApplyDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractAtlasQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyAssignContractVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import com.navigator.delivery.service.IDeliveryApplyAtlasQueryService;
import com.navigator.delivery.service.IDeliveryBIQueryService;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.enums.MdmType;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.dto.contract.ContractMdmInfoDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 * 提货申请表 查询服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryApplyAtlasQueryServiceImpl implements IDeliveryApplyAtlasQueryService {

    // Dao
    private final DeliveryApplyDao deliveryApplyDao;
    private final DeliveryApplyContractDao deliveryApplyContractDao;
    private final DeliveryApplyAllocateDao deliveryApplyAllocateDao;
    // facade
    private final ContractFacade contractFacade;
    private final CustomerFacade customerFacade;
    private final DeliveryTypeFacade deliveryTypeFacade;
    private final SystemRuleFacade systemRuleFacade;
    private final CustomerDeliveryWhiteFacade customerDeliveryWhiteFacade;
    private final SkuFacade skuFacade;
    private final AtlasContractFacade atlasContractFacade;
    private final IDeliveryBIQueryService deliveryBIQueryService;
    private final SiteFacade siteFacade;
    private final WarehouseFacade warehouseFacade;

    @Value("${navigator.deliveryApply.atlas:true}")
    private Boolean atlas;

    @Override
    public List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractList(DeliveryApplyContractAtlasQO deliveryApplyContractAtlasQO) {
        // 买方主体
        CustomerEntity customer = deliveryApplyContractAtlasQO.getCustomerId() != null ? customerFacade.getCustomerById(deliveryApplyContractAtlasQO.getCustomerId()) : null;
        // 卖方主体
        CustomerEntity supplier = deliveryApplyContractAtlasQO.getSupplierId() != null ? customerFacade.getCustomerById(deliveryApplyContractAtlasQO.getSupplierId()) : null;
        // 货品
        SkuEntity sku = deliveryApplyContractAtlasQO.getGoodsId() != null ? skuFacade.getSkuById(deliveryApplyContractAtlasQO.getGoodsId()) : null;

        return getDeliveryApplyContractList(customer, supplier, deliveryApplyContractAtlasQO.getGoodsCategoryId(), sku, deliveryApplyContractAtlasQO.getDeliveryFactoryCode(), deliveryApplyContractAtlasQO.getTriggerSys());
    }

    @Override
    public List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractList(CustomerEntity customer,
                                                                           CustomerEntity supplier,
                                                                           Integer category2,
                                                                           SkuEntity sku,
                                                                           String deliveryFactoryCode,
                                                                           Integer triggerSys) {
        if (customer == null) {
            throw new BusinessException("买方主体不能为空");
        } else {
            log.info("买方主体：{}", customer.getName());

            if (StringUtil.isBlank(customer.getMdmCustomerCode())) {
                throw new BusinessException("买方主体MDM编码不能为空");
            }
        }

        // 账套
        SiteQO siteQO = new SiteQO()
                .setStatus(1)
                .setSyncSystem(SyncSystemEnum.ATLAS.getValue())
                .setCompanyId(supplier != null ? supplier.getCompanyId() : null)
                .setFactoryCode(deliveryFactoryCode);
        List<SiteEntity> siteList = siteFacade.querySiteList(siteQO);
        if (CollUtil.isEmpty(siteList)) {
            throw new BusinessException("获取不到账套");
        }
        Set<String> atlasCodeSet = new HashSet<>();
        siteList.forEach(siteEntity -> {
            if (StringUtil.isNotNullBlank(siteEntity.getAtlasCode())) {
                log.info("atlasCode:{}", siteEntity.getAtlasCode());
                atlasCodeSet.add(siteEntity.getAtlasCode());
            }
        });
        // 提货方式
        List<DeliveryTypeEntity> deliveryTypeList = new ArrayList<>();
        deliveryTypeList.add(new DeliveryTypeEntity().setType(1).setAtlasCode("EXW"));
        if (triggerSys != null && triggerSys.equals(SystemEnum.MAGELLAN.getValue())) {
            deliveryTypeList.add(new DeliveryTypeEntity().setType(2).setAtlasCode("CNF"));
        }

        Map<String, DeliveryApplyContractAtlasVO> map = new HashMap<>();
        // ATLAS数据
        List<DeliveryApplyContractAtlasVO> atlasResult = this.getDeliveryApplyContractListFromAtlas(deliveryTypeList, customer, supplier, atlasCodeSet, sku, category2);
        log.info("ATLAS记录数:{}", atlasResult.size() + FastJsonUtils.getBeanToJson(atlasResult));
        // BI数据
        List<DeliveryApplyContractAtlasVO> biResult = this.getDeliveryApplyContractListFromBI(deliveryTypeList, customer, supplier, category2, sku, deliveryFactoryCode, triggerSys);
        log.info("BI记录数:{}", biResult.size() + FastJsonUtils.getBeanToJson(biResult));
        // 按买方主体、卖方主体、提货工厂、货品、提货方式进行分组汇总
        // 结果
        List<DeliveryApplyContractAtlasVO> result = new ArrayList<>();
        result.addAll(atlasResult);
        result.addAll(biResult);
        List<DeliveryApplyContractAtlasVO> distinctResult = new ArrayList<>();
        for (DeliveryApplyContractAtlasVO item : result) {
//            log.info("groupKey:{}", item.getGroupKey());
            if (BigDecimalUtil.isGreaterThanZero(item.getCanDeliveryNum())) {
                DeliveryApplyContractAtlasVO vo = map.get(item.getGroupKey());
                if (vo == null) {
                    // 不存在，则加入
                    distinctResult.add(item);
                    map.put(item.getGroupKey(), item);
                } else {
                    // 存在，则汇总
                    vo.setCanDeliveryNum(BigDecimalUtil.add(CalcTypeEnum.COUNT, vo.getCanDeliveryNum(), item.getCanDeliveryNum()));
                    vo.setDceCanDeliveryNum(BigDecimalUtil.add(CalcTypeEnum.COUNT, vo.getDceCanDeliveryNum(), item.getDceCanDeliveryNum()));
                }
            } else {
//                log.info("不显示可提总量=0的数据");
            }
        }
        log.info("最终记录数:{}", distinctResult.size() + FastJsonUtils.getBeanToJson(distinctResult));
        if (CollUtil.isEmpty(distinctResult)) {
            throw new BusinessException("没有可提总量大于0的数据");
        }
        return distinctResult.stream()
                .sorted(
                        Comparator.comparing(DeliveryApplyContractAtlasVO::getGoodsId)
                                .thenComparing(DeliveryApplyContractAtlasVO::getDeliveryFactoryCode)
                                .thenComparing(DeliveryApplyContractAtlasVO::getSupplierId)
                )
                .collect(Collectors.toList());
    }

    @Override
    public PreCheckDeliveryApplyDTO preCheckDeliveryApply(PreCheckDeliveryApplyQO preCheckDeliveryApplyQO) {
        // 申请单
        if (StringUtil.isNotNullBlank(preCheckDeliveryApplyQO.getApplyId())) {
            DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(preCheckDeliveryApplyQO.getApplyId());
            if (applyEntity == null) {
                throw new BusinessException("获取不到申请单");
            }
            preCheckDeliveryApplyQO = BeanUtil.copyProperties(applyEntity, PreCheckDeliveryApplyQO.class);
        }
        if (StringUtil.isNullBlank(preCheckDeliveryApplyQO.getCustomerId())) {
            throw new BusinessException("请选择买方主体");
        }
        if (StringUtil.isNullBlank(preCheckDeliveryApplyQO.getDeliveryFactoryCode())) {
            throw new BusinessException("请选择提货工厂");
        }
        if (StringUtil.isNullBlank(preCheckDeliveryApplyQO.getGoodsId())) {
            throw new BusinessException("请选择货品");
        }
        // 货品
        SkuEntity skuEntity = skuFacade.getSkuById(preCheckDeliveryApplyQO.getGoodsId());
        if (skuEntity == null) {
            throw new BusinessException("获取不到货品");
        }
        // 查询客户交易状态
        // Change trade status source from BI.CRIS to customer table by Jason Shi at 2025-5-28 Start
        CustomerEntity customerEntity = customerFacade.getCustomerById(preCheckDeliveryApplyQO.getCustomerId());
        if (customerEntity == null) {
            throw new BusinessException("未查询到客户RR Status状态");
        }
        if (TTCustomerTradeStatusEnum.NO_TRADE.getValue().equalsIgnoreCase(customerEntity.getTradeStatus())) {
            if (SystemEnum.MAGELLAN.getValue().equals(preCheckDeliveryApplyQO.getTriggerSys())) {
                throw new BusinessException("该公司交易受限，请联系Credit部门");
            } else {
                throw new BusinessException("贵司交易受限，无法正常开具提单");
            }
        }
        // 查询客户是否品种白名单
        PreCheckDeliveryApplyDTO preCheckDeliveryApplyDTO = new PreCheckDeliveryApplyDTO();
        preCheckDeliveryApplyDTO.setCustomerTradeStatus(customerEntity.getTradeStatus());
        // Change trade status source from BI.CRIS to customer table by Jason Shi at 2025-5-28 End
        CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO = new CustomerDeliveryWhiteDTO();
        customerDeliveryWhiteDTO.setCustomerId(preCheckDeliveryApplyQO.getCustomerId());
        customerDeliveryWhiteDTO.setCategory1(skuEntity.getCategory1().toString());
        customerDeliveryWhiteDTO.setCategory2(skuEntity.getCategory2().toString());
        customerDeliveryWhiteDTO.setCategory3(skuEntity.getCategory3().toString());
        customerDeliveryWhiteDTO.setStatus(1);
        List<CustomerDeliveryWhiteDTO> customerDeliveryWhiteDTOList = customerDeliveryWhiteFacade.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO);
        boolean isWhite = CollUtil.isNotEmpty(customerDeliveryWhiteDTOList);
        preCheckDeliveryApplyDTO.setIsWhite(isWhite);
        //查询交割可提量-BI
        ExchangePickQtyQO pickQtyQO = new ExchangePickQtyQO();
        pickQtyQO.setGoodsId(preCheckDeliveryApplyQO.getGoodsId());
        pickQtyQO.setDeliveryFactoryCode(preCheckDeliveryApplyQO.getDeliveryFactoryCode());
        // 卖方主体
        CustomerEntity supplier = customerFacade.getCustomerById(preCheckDeliveryApplyQO.getSupplierId());
        pickQtyQO.setCompanyId(supplier.getCompanyId());
        pickQtyQO.setDeliveryType(preCheckDeliveryApplyQO.getDeliveryType());
        pickQtyQO.setSystem(preCheckDeliveryApplyQO.getTriggerSys().toString());
        pickQtyQO.setCustomerId(preCheckDeliveryApplyQO.getCustomerId());
        List<ExchangePickQtyDTO> pickQtyDTOList = deliveryBIQueryService.getExchangePickQty(pickQtyQO);
        ExchangePickQtyDTO exchangePickQtyDTO = CollUtil.isEmpty(pickQtyDTOList) ? null : pickQtyDTOList.get(0);
        BigDecimal dceCanDeliveryNum = exchangePickQtyDTO == null ? BigDecimal.ZERO : exchangePickQtyDTO.getPickupQty();
        preCheckDeliveryApplyDTO.setDceCanDeliveryNum(dceCanDeliveryNum);
        // 交割可提量 = 0且客户属于当前货品品种的提货白名单
        if (isWhite && dceCanDeliveryNum.compareTo(BigDecimal.ZERO) == 0) {
            preCheckDeliveryApplyDTO.setIsCanAssignContract(1);
        } else {
            preCheckDeliveryApplyDTO.setIsCanAssignContract(0);
        }
        return preCheckDeliveryApplyDTO;
    }

    @Override
    public String getDeliveryApplyImportTemplate(Integer triggerSys) {
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.DELIVER_TEMPLATE_FILE_CONFIG.getRuleCode()));
        if (systemRuleVO != null) {
            SystemEnum systemEnum = SystemEnum.getByValue(triggerSys);
            for (SystemRuleVO.SystemRuleItemVO item : systemRuleVO.getSystemRuleItemVOList()) {
                if (systemEnum.getShortName().equals(item.getRuleItemKey())) {
                    return item.getRuleItemValue();
                }
            }
        }
        throw new BusinessException(String.format("缺少系统配置：%s", SystemCodeConfigEnum.DELIVER_TEMPLATE_FILE_CONFIG.getRuleCode()));
    }

    @Override
    public DeliveryApplyAssignContractVO getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO) {

        DeliveryApplyAssignContractVO assignContractVO = new DeliveryApplyAssignContractVO();
        Map<Integer, DeliveryApplyContractEntity> tempAssignedContractMap = new HashMap<>();
        Map<Integer, DeliveryApplyContractEntity> assignedContractMap = new HashMap<>();

        if (StringUtil.isNotNullBlank(assignContractDTO.getApplyId())) {
            DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(assignContractDTO.getApplyId());
            if (applyEntity == null) {
                throw new BusinessException("获取不到申请单");
            }
            // 取申请单信息
            assignContractDTO.setGoodsId(applyEntity.getGoodsId());
            assignContractDTO.setCustomerId(applyEntity.getCustomerId());
            assignContractDTO.setSupplierId(applyEntity.getSupplierId());
            assignContractDTO.setDeliveryFactoryCode(applyEntity.getDeliveryFactoryCode());
            assignContractDTO.setDeliveryMode(Integer.parseInt(applyEntity.getDeliveryType()));

            log.info("查询已分配列表");
            List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getByApplyId(assignContractDTO.getApplyId());
            // 暂存已分配列表
            List<Integer> contractIdList = new ArrayList<>();
            for (DeliveryApplyContractEntity applyContractEntity : applyContractEntityList) {
                tempAssignedContractMap.put(applyContractEntity.getContractId(), applyContractEntity);
                contractIdList.add(applyContractEntity.getContractId());
            }
            if (CollUtil.isNotEmpty(contractIdList)) {
                Result applyResult = contractFacade.getDeliveryApplyContractListForAtlas(new DeliveryApplyContractQO().setContractIdList(contractIdList));
                if (applyResult.isSuccess()) {
                    List<ContractEntity> applyContractList = JSON.parseArray(JSON.toJSONString(applyResult.getData()), ContractEntity.class);
                    List<DeliveryApplyContractEntity> deliveryApplyContractEntityList = new ArrayList<>();
                    for (ContractEntity contractEntity : applyContractList) {
                        DeliveryApplyContractEntity applyContractEntity = tempAssignedContractMap.get(contractEntity.getId());
                        BigDecimal canAllocateNum = this.getCanAllocateNum(contractEntity);
                        // 可提量大于0
                        if (BigDecimalUtil.isGreaterThanZero(canAllocateNum)) {
                            applyContractEntity.setCanAllocateNum(canAllocateNum);
                            applyContractEntity.setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getAllocateNum(), contractEntity.getUnitPrice()));
                            // 发货库点
                            String shipWarehouseName = "";
                            Result<WarehouseEntity> result = warehouseFacade.getWarehouseById(contractEntity.getShipWarehouseId());
                            if (result.isSuccess()) {
                                WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(result.getData()), WarehouseEntity.class);
                                shipWarehouseName = warehouseEntity.getName();
                            }
                            applyContractEntity.setContractTypeName(ContractTypeEnum.getDescByValue(contractEntity.getContractType()))
                                    .setContractNum(contractEntity.getContractNum())
                                    .setDeliveryTypeName(deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType()).getName())
                                    .setShipWarehouseName(shipWarehouseName)
                                    .setPaymentTypeName(PaymentTypeEnum.getDescByValue(contractEntity.getPaymentType()));
                            assignedContractMap.put(contractEntity.getId(), applyContractEntity);
                            deliveryApplyContractEntityList.add(applyContractEntity);
                        }
                    }
                    assignContractVO.setAssignedContractList(deliveryApplyContractEntityList);
                }
            }
        }

        log.info("assignContractDTO:{}", JSONUtil.toJsonStr(assignContractDTO));
        if (StringUtil.isNullBlank(assignContractDTO.getGoodsId())) {
            throw new BusinessException("货品ID为空");
        }
        if (StringUtil.isNullBlank(assignContractDTO.getCustomerId())) {
            throw new BusinessException("买方主体ID为空");
        }
        if (StringUtil.isNullBlank(assignContractDTO.getSupplierId())) {
            throw new BusinessException("卖方主体ID为空");
        }
        if (StringUtil.isNullBlank(assignContractDTO.getDeliveryFactoryCode())) {
            throw new BusinessException("提货工厂编码为空");
        }
        log.info("查询可分配合同列表");
        DeliveryApplyContractQO applyContractQO = BeanUtil.toBean(assignContractDTO, DeliveryApplyContractQO.class);
        applyContractQO.setContractIdList(null);
        // 过滤指定提货模式的提货方式，自提、配送
        log.info("查询提货方式");
        List<DeliveryTypeEntity> deliveryTypeEntityList = deliveryTypeFacade.getAllDeliveryTypeList(1, null, null, null, assignContractDTO.getDeliveryMode());
        List<Integer> deliveryTypeList = new ArrayList<>();
        deliveryTypeEntityList.forEach(item -> deliveryTypeList.add(item.getId()));
        log.info("deliveryTypeList:{}", deliveryTypeList.toString());
        applyContractQO.setDeliveryTypeList(deliveryTypeList);
        Result result = contractFacade.getDeliveryApplyContractListForAtlas(applyContractQO
                .setGoodsId(assignContractDTO.getGoodsId())
                .setCustomerId(assignContractDTO.getCustomerId())
                .setSupplierId(assignContractDTO.getSupplierId())
                .setDeliveryFactoryCode(assignContractDTO.getDeliveryFactoryCode()));
        if (result.isSuccess()) {
            List<ContractEntity> contractList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractEntity.class);
            if (CollUtil.isEmpty(contractList)) {
                log.info("查询可分配合同列表无数据");
            } else {
                List<DeliveryApplyContractEntity> applyContractList = new ArrayList<>();
                for (ContractEntity contractEntity : contractList) {
                    DeliveryApplyContractEntity applyContractEntity = new DeliveryApplyContractEntity();
                    applyContractEntity.setAllocateNum(BigDecimal.ZERO);
                    applyContractEntity.setAllocateAmount(BigDecimal.ZERO);
                    BigDecimal canAllocateNum = this.getCanAllocateNum(contractEntity);
                    // 可提量大于0
                    if (BigDecimalUtil.isGreaterThanZero(canAllocateNum)) {
                        applyContractEntity.setCanAllocateNum(canAllocateNum);
                        applyContractEntity.setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getAllocateNum(), contractEntity.getUnitPrice()));
                        // 发货库点
                        log.info("查询发货库点");
                        String shipWarehouseName = "";
                        Result<WarehouseEntity> warehouseResult = warehouseFacade.getWarehouseById(contractEntity.getShipWarehouseId());
                        if (warehouseResult.isSuccess()) {
                            WarehouseEntity warehouseEntity = JSON.parseObject(JSON.toJSONString(warehouseResult.getData()), WarehouseEntity.class);
                            shipWarehouseName = warehouseEntity.getName();
                        }
                        applyContractEntity
                                .setContractId(contractEntity.getId())
                                .setContractCode(contractEntity.getContractCode())
                                .setContractNum(contractEntity.getContractNum())
                                .setUnitPrice(contractEntity.getUnitPrice())
                                .setContractType(contractEntity.getContractType())
                                .setDeliveryStartTime(contractEntity.getDeliveryStartTime())
                                .setDeliveryEndTime(contractEntity.getDeliveryEndTime())
                                .setDeliveryTypeName(deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType()).getName())
                                .setContractSignDate(contractEntity.getSignDate())
                                .setShipWarehouseName(shipWarehouseName)
                                .setPaymentTypeName(PaymentTypeEnum.getDescByValue(contractEntity.getPaymentType()));
                        applyContractList.add(applyContractEntity);
                    }
                }

                // 先按照签订日期排序 再按照合同编码排序 - 创建日期
                applyContractList.sort(Comparator.comparing(DeliveryApplyContractEntity::getContractSignDate)
                        .thenComparing(DeliveryApplyContractEntity::getContractCode));

                assignContractVO.setCanAssignContractList(applyContractList);
            }
        } else {
            log.error("查询可分配合同列表错误");
        }
        return assignContractVO;
    }

    @Override
    public DeliveryApplyCarpoolInfoDTO getCarpoolDeliveryRequestInfo(String plateNumber, String planDeliveryTime) {
        int carpoolDeliveryCount = 0;
        int assignedCount = 0;

        // 查询拼车信息
        List<DeliveryApplyEntity> carpoolDeliveryApplyList = deliveryApplyDao.getCarpoolDeliveryApplyList(plateNumber, planDeliveryTime);

        if (CollUtil.isNotEmpty(carpoolDeliveryApplyList)) {
            // 统计DR数量
            carpoolDeliveryCount = carpoolDeliveryApplyList.size();
            // 统计已分配数量
            // BUGFIX：case-1003187 is_Final计算错误 Author: Mr 2025-05-06 start
//            assignedCount = carpoolDeliveryApplyList.stream()
//                    .mapToInt(item -> deliveryApplyContractDao.getByApplyId(item.getId()).size())
//                    .sum();
            // BUGFIX：case-1003187 is_Final计算错误 Author: Mr 2025-05-06 end
            assignedCount = getAssignedCount(carpoolDeliveryApplyList);
        }

        return new DeliveryApplyCarpoolInfoDTO()
                .setCarpoolDeliveryCount(carpoolDeliveryCount)
                .setAssignedCount(assignedCount);
    }

    // BUGFIX：case-1003187 is_Final计算错误 Author: Mr 2025-05-06 start

    /**
     * 统计已分配数量（已与合同关联的预分配记录数）
     *
     * @param applyList 拼车申请单列表
     * @return 已分配数量
     */
    private int getAssignedCount(List<DeliveryApplyEntity> applyList) {
        int assignedCount = 0;

        for (DeliveryApplyEntity apply : applyList) {
            List<DeliveryApplyAllocateEntity> allocateList = deliveryApplyAllocateDao.getDeliveryApplyAllocateByApplyId(apply.getId());

            for (DeliveryApplyAllocateEntity allocate : allocateList) {
                String code = StringUtil.isBlank(allocate.getSubApplyCode())
                        ? allocate.getApplyCode()
                        : allocate.getSubApplyCode();

                List<DeliveryApplyContractEntity> contracts = deliveryApplyContractDao.getBySubApplyCodeOrApplyId(code, allocate.getApplyId());

                if (CollUtil.isNotEmpty(contracts)) {
                    assignedCount++;
                }
            }
        }

        return assignedCount;
    }
    // BUGFIX：case-1003187 is_Final计算错误 Author: Mr 2025-05-06 end

    @Override
    public BigDecimal getTotalBlockedQuantity(String contractList) {
        // 初始值为零
        BigDecimal blockedQuantity = BigDecimal.ZERO;

        if (StringUtil.isNotBlank(contractList)) {
            String[] contractCodeArr = contractList.split(",");

            // 使用线程池进行异步并行处理
            ExecutorService executorService = Executors.newFixedThreadPool(10); // 根据并发数调整线程池大小

            // 批量请求并行处理
            List<CompletableFuture<BigDecimal>> futures = Arrays.stream(contractCodeArr)
                    .map(contractCode -> CompletableFuture.supplyAsync(() -> {
                        // 获取每个合同的阻塞数量
                        try {
                            Result<BigDecimal> blockedQuantityResult = atlasContractFacade.getContractBlockedQuantity(contractCode);
                            if (blockedQuantityResult.isSuccess()) {
                                return blockedQuantityResult.getData();
                            }
                        } catch (Exception e) {
                            log.error("获取合同{}的阻塞数量失败: {}", contractCode, e.getMessage());
                        }
                        return BigDecimal.ZERO;
                    }, executorService))
                    .collect(Collectors.toList());

            // 等待所有异步操作完成，并计算总和
            try {
                blockedQuantity = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
            } finally {
                executorService.shutdown();
            }
        }

        return blockedQuantity;
    }

    /**
     * 从ATLAS返回可提汇总列表
     *
     * @param deliveryTypeList 提货方式
     * @param customer         买方主体
     * @param supplier
     * @param atlasCodeSet     atlas账套编码
     * @param sku              货品
     * @param category2
     * @return
     */
    private List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractListFromAtlas(List<DeliveryTypeEntity> deliveryTypeList, CustomerEntity customer, CustomerEntity supplier, Set<String> atlasCodeSet, SkuEntity sku, Integer category2) {
        // 现货mdmId
        SkuMdmQO skuMdmQO = new SkuMdmQO().setCategory2(category2).setType(MdmType.SPOT.getValue());
        if (sku != null) {
            skuMdmQO.setSkuId(sku.getId());
        }
        Result<List<SkuMdmEntity>> skuMdmResult = skuFacade.querySkuMdmList(skuMdmQO);
        Set<String> mdmIdSet = new HashSet<>();
        if (skuMdmResult.isSuccess() && CollUtil.isNotEmpty(skuMdmResult.getData())) {
            // 过滤掉mdmId为空的数据
            skuMdmResult.getData().stream()
                    .map(SkuMdmEntity::getMdmId)
                    .filter(StringUtil::isNotBlank)
                    .forEach(mdmIdSet::add);
        }
        if (CollUtil.isEmpty(mdmIdSet)) {
            throw new BusinessException("获取不到现货MdmId");
        }
        log.info("mdmId:{}", mdmIdSet);
        List<DeliveryApplyContractAtlasVO> result = new ArrayList<>();
        Map<String, DeliveryTypeEntity> atlasCodeDeliveryTypeMap = new HashMap<>();
        //查询可提量-ATLAS
        AtlasDeliveryOpenQuantityDTO atlasDeliveryOpenQuantityDTO = new AtlasDeliveryOpenQuantityDTO();
        AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailDTO openQuantityDetailDTO = new AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailDTO();
        atlasDeliveryOpenQuantityDTO.setOpenQuantityDetails(openQuantityDetailDTO);
        openQuantityDetailDTO.setShareContractList("Y");
        List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> parameterList = new ArrayList<>();
        openQuantityDetailDTO.setParameterList(parameterList);
        for (DeliveryTypeEntity deliveryType : deliveryTypeList) {
            for (String atlasCode : atlasCodeSet) {
                for (String mdmId : mdmIdSet) {
                    AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO detailItemDTO = new AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO();
                    detailItemDTO.setBusinessEntity(atlasCode);
                    detailItemDTO.setContractType(DeliveryConstant.CONTRACT_TYPE_S);
                    detailItemDTO.setCurrency(DeliveryConstant.CURRENCY_CNY);
                    detailItemDTO.setCommodityID(mdmId);
                    detailItemDTO.setContractTerms(deliveryType.getAtlasCode());
                    if (!atlas) {
                        detailItemDTO.setOpenQuantity(new BigDecimal(0));
                    }
                    parameterList.add(detailItemDTO);
                }
                // 关联关系
                atlasCodeDeliveryTypeMap.put(deliveryType.getAtlasCode(), deliveryType);
            }
        }

        List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> resultParameterList = parameterList;
        if (atlas) {
            log.info("atlas请求数据:{}", JSONUtil.toJsonStr(atlasDeliveryOpenQuantityDTO));
            Result<AtlasDeliveryOpenQuantityDTO> atlasResult = atlasContractFacade.getDeliveryOpenQuantity(customer.getMdmCustomerCode(), atlasDeliveryOpenQuantityDTO);
            log.info("atlas返回数据:{}", JSONUtil.toJsonStr(atlasResult));
            if (!atlasResult.isSuccess()
                    || atlasResult.getData() == null
                    || atlasResult.getData().getOpenQuantityDetails() == null
                    || CollUtil.isEmpty(atlasResult.getData().getOpenQuantityDetails().getParameterList())) {
                log.error("调用atlas查询可提量：返回数据结果为空");
            } else {
                // resultParameterList = atlasResult.getData().getOpenQuantityDetails().getParameterList();
                // 处理返回数据 TODO 如果数量量大可能会出现接口超时问题
                resultParameterList = processAtlasResult(atlasResult.getData().getOpenQuantityDetails());
            }
        }

        // 遍历atlas结果
        for (AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO detailItemDTO : resultParameterList) {
            // 组装结果
            DeliveryApplyContractAtlasVO atlasVO = new DeliveryApplyContractAtlasVO();
            // 买方主体
            atlasVO.setCustomerId(customer.getId());
            atlasVO.setCustomerName(customer.getName());
            // 账套
            SiteQO siteQO = new SiteQO().setAtlasCode(detailItemDTO.getBusinessEntity()).setSyncSystem(SyncSystemEnum.ATLAS.getValue()).setStatus(1);
            if (supplier != null) {
                siteQO.setCompanyId(supplier.getCompanyId());
                atlasVO.setCompanyId(supplier.getCompanyId());
            }
            List<SiteEntity> siteList = siteFacade.querySiteList(siteQO);

            // BUGFIX：case-1003093 航海家提货委托界面货品名称错误 Author: Mr 2025-04-10 start
            SiteEntity site = new SiteEntity();
            if (CollUtil.isNotEmpty(siteList)) {
                site = siteList.get(0);
                // BUGFIX：case-1003093 航海家提货委托界面货品名称错误 Author: Mr 2025-04-10 end
                // 提货工厂编码
                atlasVO.setDeliveryFactoryCode(site.getFactoryCode());
                List<CustomerEntity> supplierList = customerFacade.getCustomerByCompanyId(site.getCompanyId(), GeneralEnum.YES.getValue());
                // 卖方主体
                if (CollUtil.isNotEmpty(supplierList)) {
                    CustomerEntity supplierEntity = supplierList.get(0);
                    atlasVO.setSupplierId(supplierEntity.getId());
                    atlasVO.setSupplierName(supplierEntity.getName());
                } else {
                    log.error("未获取到卖方主体");
                }
            } else {
                log.error("未获取到账套信息");
            }

            // BUGFIX：case-1003152 天津工厂提货，货品带RZ，且有可提货量 Author: Mr 2025-04-18 start
            // 获取匹配的 SKU
            SkuDTO matchedSku = findMatchedSku(detailItemDTO.getCommodityID(), category2, site.getCompanyId(), site.getFactoryCode());

            if (matchedSku == null || matchedSku.getId() == null) {
                log.error("未获取到有效 SKU，mdmId={}", detailItemDTO.getCommodityID());
                continue;
            }

            atlasVO.setGoodsId(matchedSku.getId());
            atlasVO.setGoodsName(matchedSku.getFullName());
            // BUGFIX：case-1003152 天津工厂提货，货品带RZ，且有可提货量 Author: Mr 2025-04-18 end

            // 提货方式
            DeliveryTypeEntity deliveryType = atlasCodeDeliveryTypeMap.get(detailItemDTO.getContractTerms());
            atlasVO.setDeliveryType(deliveryType.getType());
            // 可提量总量
            atlasVO.setCanDeliveryNum(detailItemDTO.getOpenQuantity());
            // 交割可提量
            atlasVO.setDceCanDeliveryNum(BigDecimal.ZERO);
            result.add(atlasVO);
        }
        return result;
    }

    // BUGFIX：case-1003152 天津工厂提货，货品带RZ，且有可提货量 Author: Mr 2025-04-18 start

    /**
     * 根据 MDM ID 和品类、公司ID、工厂编码查找匹配的 SKU
     */
    private SkuDTO findMatchedSku(String mdmId, Integer category2, Integer companyId, String factoryCode) {

        if (StringUtil.isNullBlank(mdmId)) {
            log.warn("MDM ID 为空，无法查询 SKU");
            return null;
        }

        List<SkuDTO> skuList = skuFacade.querySkuDTOList(
                new SkuQO().setMdmId(mdmId).setIsNeedSkuMdmList(0)
        );

        if (CollUtil.isEmpty(skuList)) {
            log.warn("未查询到 MDM ID [{}] 对应的 SKU", mdmId);
            return null;
        }

        for (SkuDTO candidate : skuList) {
            // 过滤掉不符合条件的 SKU category2 == null的时候
            if ((category2 == null || candidate.getCategory2().equals(category2)) &&
                    matchSku(companyId, factoryCode, candidate)) {
                return candidate;
            }
        }

        log.info("SKU 匹配失败：未找到 category2={} 且匹配工厂/公司条件的 SKU，mdmId={}", category2, mdmId);
        return null;
    }
    // BUGFIX：case-1003152 天津工厂提货，货品带RZ，且有可提货量 Author: Mr 2025-04-18 end

    // BUGFIX：case-1003093 航海家提货委托界面货品名称错误 Author: Mr 2025-04-10 start

    /**
     * 判断 SKU 是否符合公司账套与货品名称的匹配规则。
     * - TJIB-TJIB 账套：必须包含 "RZ"
     * - 其他账套：不能包含 "RZ"
     */
    private boolean matchSku(Integer companyId, String deliveryFactoryCode, SkuDTO skuDTO) {
        // 启用的货品
        if (skuDTO.getStatus() != 1) {
            return false;
        }

        // 是否是TJIB-TJIB账套
        boolean isTjibAccount = Objects.equals(companyId, 1) && "TJIB".equals(deliveryFactoryCode);
        // 是否包含RZ
        boolean hasRZ = skuDTO.getFullName() != null && skuDTO.getFullName().contains("RZ");

        return (isTjibAccount && hasRZ) || (!isTjibAccount && !hasRZ);
    }
    // BUGFIX：case-1003093 航海家提货委托界面货品名称错误 Author: Mr 2025-04-10 end

    /**
     * 处理ATLAS返回数据
     * 扣减blocked数量
     *
     * @param openQuantityDetails ATLAS返回数据
     * @return 扣减blocked数量后的列表
     */
    private List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> processAtlasResult(AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailDTO openQuantityDetails) {
        List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> parameterList = openQuantityDetails.getParameterList();

        // 只有一个列表统一扣减
        if (parameterList.size() == 1) {
            BigDecimal totalBlockedQuantity = getTotalBlockedQuantity(openQuantityDetails.getContractList());
            // 更新OpenQuantity
            parameterList.forEach(item -> item.setOpenQuantity(item.getOpenQuantity().subtract(totalBlockedQuantity)));
        }

        // 判断 parameterList 是否非空且包含多个元素
        if (parameterList.size() > 1) {
            String contractList = openQuantityDetails.getContractList();
            if (StringUtil.isBlank(contractList)) {
                log.error("合同列表为空");
                return parameterList;
            }

            String[] contractCodeArr = contractList.split(",");

            // 遍历每个合同代码
            for (String contractCode : contractCodeArr) {
                BigDecimal blockedQuantity = getTotalBlockedQuantity(contractCode);

                // 获取合同 Mdm 信息
                Result<ContractMdmInfoDTO> contractMdmInfo = contractFacade.getContractMdmInfo(contractCode);
                if (contractMdmInfo != null && contractMdmInfo.isSuccess() && contractMdmInfo.getData() != null) {
                    String businessEntity = contractMdmInfo.getData().getBusinessEntity();
                    String commodityID = contractMdmInfo.getData().getCommodityCode();
                    String contractTerms = contractMdmInfo.getData().getContractTerms();

                    // 根据合同信息过滤匹配的项
                    List<AtlasDeliveryOpenQuantityDTO.OpenQuantityDetailItemDTO> tempList = parameterList.stream()
                            .filter(item -> Objects.equals(item.getBusinessEntity(), businessEntity) &&
                                    Objects.equals(item.getCommodityID(), commodityID) &&
                                    Objects.equals(item.getContractTerms(), contractTerms))
                            .collect(Collectors.toList());

                    if (CollUtil.isEmpty(tempList)) {
                        log.error("未找到合同{}的可提量列表", contractCode);
                        continue;
                    }

                    // 更新匹配项的 OpenQuantity
                    tempList.forEach(item -> {
                        BigDecimal newOpenQuantity = item.getOpenQuantity().subtract(blockedQuantity);
                        item.setOpenQuantity(newOpenQuantity.max(BigDecimal.ZERO));  // 确保OpenQuantity不为负数
                        log.info("更新合同{}的可提量，新的OpenQuantity为: {}", contractCode, item.getOpenQuantity());
                    });
                } else {
                    log.error("获取合同{}的Mdm信息失败或返回数据为空", contractCode);
                }
            }
        }

        return parameterList;
    }

    /**
     * 从BI返回可提汇总列表
     *
     * @param deliveryTypeList    提货方式
     * @param customer            买方主体
     * @param supplier            卖方主体
     * @param category2
     * @param sku                 货品
     * @param deliveryFactoryCode 提货厂家
     * @param triggerSys          触发系统
     * @return
     */
    private List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractListFromBI(List<DeliveryTypeEntity> deliveryTypeList,
                                                                                  CustomerEntity customer,
                                                                                  CustomerEntity supplier,
                                                                                  Integer category2,
                                                                                  SkuEntity sku,
                                                                                  String deliveryFactoryCode,
                                                                                  Integer triggerSys) {
        // 结果
        List<DeliveryApplyContractAtlasVO> result = new ArrayList<>();
        for (DeliveryTypeEntity deliveryType : deliveryTypeList) {
            //查询交割可提量-BI
            ExchangePickQtyQO pickQtyQO = new ExchangePickQtyQO();
            pickQtyQO.setDeliveryType(deliveryType.getType());
            pickQtyQO.setCustomerId(null != customer ? customer.getId() : null);
            if (supplier != null) {
                pickQtyQO.setCompanyId(supplier.getCompanyId());
            }
            pickQtyQO.setDeliveryFactoryCode(StringUtil.isNullBlank(deliveryFactoryCode) ? null : deliveryFactoryCode);
            if (sku != null) {
                pickQtyQO.setGoodsId(sku.getId());
            }
            pickQtyQO.setSystem(triggerSys.toString());
            log.info("查询BI,请求参数===================================" + FastJsonUtils.getBeanToJson(pickQtyQO));
            List<ExchangePickQtyDTO> pickQtyDTOList = deliveryBIQueryService.getExchangePickQty(pickQtyQO);
            log.info("查询BI,返回结果==================================" + FastJsonUtils.getBeanToJson(pickQtyDTOList));
            if (CollUtil.isNotEmpty(pickQtyDTOList)) {
                for (ExchangePickQtyDTO exchangePickQtyDTO : pickQtyDTOList) {
                    // 组装结果
                    DeliveryApplyContractAtlasVO atlasVO = new DeliveryApplyContractAtlasVO();
                    atlasVO.setCustomerId(customer.getId());
                    atlasVO.setCustomerName(customer.getName());
                    if (supplier != null) {
                        atlasVO.setSupplierId(supplier.getId());
                        atlasVO.setCompanyId(supplier.getCompanyId());
                        atlasVO.setSupplierName(supplier.getName());
                    } else {
                        log.info("未指定卖方主体");
                        List<CustomerEntity> supplierList = customerFacade.getCustomerByCompanyId(Integer.parseInt(exchangePickQtyDTO.getCompanyId()), GeneralEnum.YES.getValue());
                        if (CollUtil.isNotEmpty(supplierList)) {
                            atlasVO.setSupplierId(supplierList.get(0).getId());
                            atlasVO.setSupplierName(supplierList.get(0).getName());
                            atlasVO.setCompanyId(supplierList.get(0).getCompanyId());
                        } else {
                            log.info("未找到卖方主体：跳过。companyId={}", exchangePickQtyDTO.getCompanyId());
                            continue;
                        }
                    }
                    if (sku != null) {
                        atlasVO.setGoodsId(sku.getId());
                        atlasVO.setGoodsName(sku.getFullName());
                    } else {
                        log.info("未指定货品");
                        SkuEntity skuEntity = skuFacade.getSkuById(exchangePickQtyDTO.getGoodsId());
                        if (skuEntity != null) {
                            log.info("SKU二级品类:{}", skuEntity.getCategory2());
                            // 非指定二级品类：跳过
                            if (category2 != null && !category2.equals(skuEntity.getCategory2())) {
                                log.info("非指定二级品类：跳过。{}", skuEntity.getFullName());
                                continue;
                            }
                            atlasVO.setGoodsId(skuEntity.getId());
                            atlasVO.setGoodsName(skuEntity.getFullName());
                        } else {
                            log.info("未找到货品：跳过。{}", exchangePickQtyDTO.getGoodsId());
                            continue;
                        }
                    }
                    atlasVO.setDeliveryFactoryCode(exchangePickQtyDTO.getDeliveryFactoryCode());
                    atlasVO.setDeliveryType(deliveryType.getType());
                    atlasVO.setCanDeliveryNum(exchangePickQtyDTO.getPickupQty());
                    atlasVO.setDceCanDeliveryNum(exchangePickQtyDTO.getPickupQty());
                    result.add(atlasVO);
                }
            } else {
                log.error("BI无数据");
            }
        }
        return result;
    }

    /**
     * 获取可分配数量
     *
     * @param contractEntity
     * @return
     */
    private BigDecimal getCanAllocateNum(ContractEntity contractEntity) {
        BigDecimal canAllocateNum = BigDecimal.ZERO;
        if (atlas) {
            log.info("获取账套开始:{}", contractEntity.getSiteCode());
            SiteEntity siteEntity = siteFacade.getSiteByCode(contractEntity.getSiteCode());
            log.info("获取账套结束:{}", JSONUtil.toJsonStr(siteEntity));
            try {
                log.info("atlas请求数据: atlasCode = {},contractCode = {}", siteEntity.getAtlasCode(), contractEntity.getContractCode());
                Result<BigDecimal> contractOpenQuantity = atlasContractFacade.getContractOpenQuantity(siteEntity.getAtlasCode(), contractEntity.getContractCode());
                log.info("atlas返回数据:{}", JSONUtil.toJsonStr(contractOpenQuantity));
                if (contractOpenQuantity.isSuccess()) {
                    canAllocateNum = contractOpenQuantity.getData().setScale(6, RoundingMode.HALF_UP);
                    log.info("可分配数量：{}", canAllocateNum);
                } else {
                    log.info("查询失败");
                }
            } catch (Exception e) {
                log.error("获取ATLAS合同信息异常:{}", e.getMessage(), e);
            }
            log.info("调用ATLAS接口结束");
        } else {
            log.info("ATLAS开关关闭");
        }
        return canAllocateNum;
    }
}
