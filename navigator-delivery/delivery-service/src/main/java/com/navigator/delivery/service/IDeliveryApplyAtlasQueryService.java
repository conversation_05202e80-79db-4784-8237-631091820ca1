package com.navigator.delivery.service;

import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.delivery.pojo.dto.DeliveryApplyAssignContractDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyCarpoolInfoDTO;
import com.navigator.delivery.pojo.dto.PreCheckDeliveryApplyDTO;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractAtlasQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyAssignContractVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import com.navigator.goods.pojo.entity.SkuEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 提货申请表 查询服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface IDeliveryApplyAtlasQueryService {

    /**
     * 查询提货可申请汇总列表
     *
     * @param deliveryApplyContractAtlasQO
     * @return
     */
    List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractList(DeliveryApplyContractAtlasQO deliveryApplyContractAtlasQO);

    /**
     * 查询提货可申请汇总列表
     *
     * @param customer
     * @param supplier
     * @param category2
     * @param sku
     * @param deliveryFactoryCode
     * @param triggerSys
     * @return
     */
    List<DeliveryApplyContractAtlasVO> getDeliveryApplyContractList(CustomerEntity customer,
                                                                    CustomerEntity supplier,
                                                                    Integer category2, SkuEntity sku,
                                                                    String deliveryFactoryCode,
                                                                    Integer triggerSys);

    /**
     * 申请提货预校验
     *
     * @param preCheckDeliveryApplyQO
     * @return
     */
    PreCheckDeliveryApplyDTO preCheckDeliveryApply(PreCheckDeliveryApplyQO preCheckDeliveryApplyQO);

    /**
     * 获取批量上传模板地址
     *
     * @param triggerSys
     * @return
     */
    String getDeliveryApplyImportTemplate(Integer triggerSys);

    /**
     * 查询分配合同列表
     *
     * @param assignContractDTO
     * @return
     */
    DeliveryApplyAssignContractVO getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO);

    /**
     * 查询拼车信息
     *
     * @param plateNumber      车牌号
     * @param planDeliveryTime 计划提货时间
     * @return 拼车信息
     */
    DeliveryApplyCarpoolInfoDTO getCarpoolDeliveryRequestInfo(String plateNumber, String planDeliveryTime);


    /**
     * 获取合同列表中锁定量
     *
     * @param contractList 合同列表
     * @return 锁定量
     */
    BigDecimal getTotalBlockedQuantity(String contractList);

}
