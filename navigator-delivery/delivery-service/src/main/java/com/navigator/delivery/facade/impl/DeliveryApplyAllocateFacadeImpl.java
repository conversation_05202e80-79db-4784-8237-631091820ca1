package com.navigator.delivery.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.delivery.facade.DeliveryApplyAllocateFacade;
import com.navigator.delivery.pojo.dto.DeliveryApplyAllocateDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;
import com.navigator.delivery.service.IDeliveryApplyAllocateService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 提货申请预分配 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@RestController
@Api(tags = "提货申请预分配")
public class DeliveryApplyAllocateFacadeImpl implements DeliveryApplyAllocateFacade {

    @Resource
    private IDeliveryApplyAllocateService deliveryApplyAllocateService;

    @Override
    public List<DeliveryApplyAllocateDTO> queryDeliveryApplyAllocateList(DeliveryApplyAllocateQO condition) {
        return deliveryApplyAllocateService.queryDeliveryApplyAllocateList(condition);
    }

    @Override
    public DeliveryApplyAllocateEntity getDeliveryApplyAllocateById(Integer id) {
        return deliveryApplyAllocateService.getDeliveryApplyAllocateById(id);
    }

    @Override
    public Result getDeliveryRequestAllocateInfo(DeliveryApplyEntity deliveryApplyEntity) {
        return Result.success(deliveryApplyAllocateService.getDeliveryRequestAllocateInfo(deliveryApplyEntity));
    }

    @Override
    public Result<List<DeliveryApplyAllocateEntity>> getDeliveryApplyAllocateByApplyId(Integer applyId) {
        return Result.success(deliveryApplyAllocateService.getDeliveryApplyAllocateByApplyId(applyId));
    }
}
