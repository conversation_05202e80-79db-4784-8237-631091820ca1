package com.navigator.delivery.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.dto.TempPermissionDTO;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;
import com.navigator.delivery.service.IDeliveryApplyQueryService;
import com.navigator.delivery.service.IDeliveryApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RefreshScope
@RestController
@RequiredArgsConstructor
public class DeliveryApplyFacadeImpl implements DeliveryApplyFacade {

    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 Start
    @Value("${navigator.tempPermission.customerStatus:false}")
    private Boolean customerStatus;
    @Value("${navigator.tempPermission.customerNameList:}")
    private List<String> customerNameList;
    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 End

    private final IDeliveryApplyService deliveryApplyService;
    private final IDeliveryApplyQueryService deliveryApplyQueryService;

    @Override
    public Result getDeliveryApplyList(QueryDTO<DeliveryApplyQO> queryDTO) {
        return Result.page(deliveryApplyQueryService.getDeliveryApplyList(queryDTO));
    }

    @Override
    public Result<DeliveryApplyDetailVO> getDeliveryApplyDetailById(Integer applyId, String triggerSys) {
        return Result.success(deliveryApplyQueryService.getDeliveryApplyDetailById(applyId, triggerSys));
    }

    @Override
    public Result<DeliveryApplyAssignContractVO> getReAssignContractList(DeliveryApplyAssignContractDTO assignContractDTO) {
        return Result.success(deliveryApplyQueryService.getReAssignContractList(assignContractDTO));
    }

    @Override
    public Result<List<DeliveryApplyFileVO>> getFileListByApplyId(Integer applyId) {
        return Result.success(deliveryApplyQueryService.getFileListByApplyId(applyId));
    }

    @Override
    public Result<List<DeliveryApplyApprovalVO>> getAuditRecordListByApplyId(Integer applyId) {
        return Result.success(deliveryApplyQueryService.getAuditRecordListByApplyId(applyId));
    }

    @Override
    public Result<List<DeliveryApplyOperationVO>> getOperateRecordListByApplyCode(String applyCode) {
        return Result.success(deliveryApplyQueryService.getOperateRecordListByApplyCode(applyCode));
    }

    @Override
    public Result<List<DeliveryApplyContractVO>> getDeliveryApplyContractList(DeliveryApplyContractQO deliveryApplyContractQO) {
        return Result.success(deliveryApplyQueryService.getDeliveryApplyContractList(deliveryApplyContractQO));
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @Override
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverInputRecord(Integer customerId, Integer goodsCategoryId) {
        return Result.success(deliveryApplyQueryService.getDriverInputRecord(customerId, goodsCategoryId));
    }

    @Override
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverRecordByCondition(DeliveryApplyDriverLogDTO deliveryApplyDriverLogDTO) {
        return Result.success(deliveryApplyQueryService.getDriverRecordByCondition(deliveryApplyDriverLogDTO));
    }
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @Override
    public Result<String> saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        return Result.success(deliveryApplyService.saveOrSubmitDeliveryApply(deliveryApplyDTO));
    }

    @Override
    public Result<String> batchSubmitDeliveryApply(List<Integer> applyIds) {
        return Result.success(deliveryApplyService.batchSubmitDeliveryApply(applyIds));
    }

    @Override
    // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
    public Result<String> updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
        return Result.success(deliveryApplyService.updateOrSubmitDeliveryApply(deliveryApplyDTO));
    }

    @Override
    public Result<Boolean> submitDeliveryApplyById(Integer applyId) {
        return Result.success(deliveryApplyService.submitDeliveryApplyById(applyId));
    }

    @Override
    public Result<Boolean> cancelDeliveryApply(Integer applyId) {
        return Result.success(deliveryApplyService.cancelDeliveryApply(applyId));
    }

    @Override
    public Result<Boolean> inputDeliveryApplyBillStatus(List<Integer> applyIds, Integer billStatus) {
        return Result.success(deliveryApplyService.inputDeliveryApplyBillStatus(applyIds, billStatus));
    }

    @Override
    public Result<Boolean> auditDeliveryApply(DeliveryApplyApprovalDTO deliveryApplyApprovalDTO) {
        return Result.success(deliveryApplyService.auditDeliveryApply(deliveryApplyApprovalDTO));
    }

    @Override
    public Result<Boolean> batchAuditPassDeliveryApply(List<Integer> applyIds) {
        return Result.success(deliveryApplyService.batchAuditPassDeliveryApply(applyIds));
    }

    @Override
    public Result<Boolean> batchAuditRejectDeliveryApply(List<Integer> applyIds) {
        return Result.success(deliveryApplyService.batchAuditRejectDeliveryApply(applyIds));
    }

    @Override
    public Result<Boolean> applyInvalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO) {
        return Result.success(deliveryApplyService.invalidDeliveryApply(applyInvalidDTO));
    }

    @Override
    public Result<Boolean> reAssignContract(DeliveryApplyAssignContractDTO assignContractDTO) {
        return Result.success(deliveryApplyService.reAssignContract(assignContractDTO));
    }

    @Override
    public Result<Boolean> removeAssignContract(Integer applyId, Integer contractId) {
        return Result.success(deliveryApplyService.removeAssignContract(applyId, contractId));
    }

    @Override
    public Result getDeliveryApplyListByIds(List<Integer> applyIds) {
        return Result.success(deliveryApplyQueryService.getDeliveryApplyListByIds(applyIds));
    }

    @Override
    public Result<Boolean> isContractInDeliveryAudit(Integer contractId) {
        return Result.success(deliveryApplyQueryService.isContractInDeliveryAudit(contractId));
    }

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start
    @Override
    public Result<Boolean> clearDriverInfoOneYear() {
        return Result.success(deliveryApplyService.clearDriverInfoOneYear());
    }
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End

    @Override
    public Result getRpaDeliveryApplyList() {
        return Result.success(deliveryApplyQueryService.getRpaDeliveryApplyList());
    }

    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 Start
    @Override
    public Result<TempPermissionDTO> getCustomerTempPermission() {
        return Result.success(new TempPermissionDTO().setCustomerStatus(customerStatus).setCustomerNameList(customerNameList));
    }
    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 End
}
