package com.navigator.delivery.service;

import com.navigator.delivery.pojo.dto.DeliveryApplyApprovalDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyAssignContractDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyInvalidDTO;

import java.util.List;

/**
 * <p>
 * 提货申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IDeliveryApplyService {

    // 保存/提交申请单
    String saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO);

    // 编辑/提交申请单
    String updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO);

    // 根据申请单id提交申请单
    boolean submitDeliveryApplyById(Integer applyId);

    // 申请单号
    String genDeliveryApplyCode(Integer goodsCategoryId, Integer supplierId, String deliveryFactory);

    // 批量提交申请单
    String batchSubmitDeliveryApply(List<Integer> applyIds);

    // 根据申请单id录入开单信息
    boolean inputDeliveryApplyBillStatus(List<Integer> applyIds, Integer billStatus);

    // 申请单重新分配合同
    boolean reAssignContract(DeliveryApplyAssignContractDTO assignContractDTO);

    // 移除分配单合同
    boolean removeAssignContract(Integer applyId, Integer contractId);

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start
    // 清理一年未使用的司机记录
    boolean clearDriverInfoOneYear();
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End

    // ====================申请审核======================

    // 撤回申请单-GLB
    boolean cancelDeliveryApply(Integer applyId);

    // 作废申请单申请-GLB
    boolean invalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO);

    // 提货申请审核
    boolean auditDeliveryApply(DeliveryApplyApprovalDTO deliveryApplyApprovalDTO);

    // 批量审核通过-MLG
    boolean batchAuditPassDeliveryApply(List<Integer> applyIds);

    // 批量审核驳回-MLG
    boolean batchAuditRejectDeliveryApply(List<Integer> applyIds);

}
