package com.navigator.delivery.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.delivery.mapper.DeliveryApplyDriverLogMapper;
import com.navigator.delivery.pojo.dto.DeliveryApplyDriverLogDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import jodd.util.StringUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * dbd_delivery_apply_driver_log Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Dao
public class DeliveryApplyDriverLogDao extends BaseDaoImpl<DeliveryApplyDriverLogMapper, DeliveryApplyDriverLogEntity> {

    public List<DeliveryApplyDriverLogEntity> getDriverInputRecord(Integer customerId, Integer goodsCategoryId) {
        return this.page((new Page<>(1, 10)), Wrappers.<DeliveryApplyDriverLogEntity>lambdaQuery()
                        .eq(null != goodsCategoryId, DeliveryApplyDriverLogEntity::getGoodsCategoryId, goodsCategoryId)
                        .eq(DeliveryApplyDriverLogEntity::getCustomerId, customerId)
                        .orderByDesc(DeliveryApplyDriverLogEntity::getCreatedAt))
                .getRecords();
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start

    /**
     * 查询一年之内的数据
     *
     * @param deliveryApplyDriverLogDTO 查询条件
     * @return
     */
    public List<DeliveryApplyDriverLogEntity> getDriverRecordByCondition(DeliveryApplyDriverLogDTO deliveryApplyDriverLogDTO) {
        return this.list(Wrappers.<DeliveryApplyDriverLogEntity>lambdaQuery()
                .like(StringUtil.isNotBlank(deliveryApplyDriverLogDTO.getDriverName()), DeliveryApplyDriverLogEntity::getDriverName, deliveryApplyDriverLogDTO.getDriverName())
                .like(StringUtil.isNotBlank(deliveryApplyDriverLogDTO.getDriverIdNumber()), DeliveryApplyDriverLogEntity::getDriverIdNumber, deliveryApplyDriverLogDTO.getDriverIdNumber())
                .like(StringUtil.isNotBlank(deliveryApplyDriverLogDTO.getOnboardPhone()), DeliveryApplyDriverLogEntity::getOnboardPhone, deliveryApplyDriverLogDTO.getOnboardPhone())
                .like(StringUtil.isNotBlank(deliveryApplyDriverLogDTO.getPlateNumber()), DeliveryApplyDriverLogEntity::getPlateNumber, deliveryApplyDriverLogDTO.getPlateNumber())
                .like(StringUtil.isNotBlank(deliveryApplyDriverLogDTO.getTrailerNumber()), DeliveryApplyDriverLogEntity::getTrailerNumber, deliveryApplyDriverLogDTO.getTrailerNumber())
                .eq(null != deliveryApplyDriverLogDTO.getCarType(), DeliveryApplyDriverLogEntity::getCarType, deliveryApplyDriverLogDTO.getCarType())
                .eq(null != deliveryApplyDriverLogDTO.getGoodsCategoryId(), DeliveryApplyDriverLogEntity::getGoodsCategoryId, deliveryApplyDriverLogDTO.getGoodsCategoryId())
                .eq(null != deliveryApplyDriverLogDTO.getTransportWay(), DeliveryApplyDriverLogEntity::getTransportWay, deliveryApplyDriverLogDTO.getTransportWay())
                .eq(DeliveryApplyDriverLogEntity::getCustomerId, deliveryApplyDriverLogDTO.getCustomerId())
                .ge(DeliveryApplyDriverLogEntity::getCreatedAt, LocalDateTime.now().minusYears(1))
                .orderByDesc(DeliveryApplyDriverLogEntity::getCreatedAt));
    }

    /**
     * 保存司机输入记录
     *
     * @param deliveryApplyDriverLogEntity 司机输入记录
     */
    public void saveDriverRecord(DeliveryApplyDriverLogEntity deliveryApplyDriverLogEntity) {

        // 若修改运输方式、司机/船长身份证号，车/船号这3个字段，则后台数据库表中新增一条数据记录，若仅修改其它字段则直接覆盖以前的数据
        List<DeliveryApplyDriverLogEntity> driverList = this.list(Wrappers.<DeliveryApplyDriverLogEntity>lambdaQuery()
                .eq(DeliveryApplyDriverLogEntity::getCustomerId, deliveryApplyDriverLogEntity.getCustomerId())
                .eq(DeliveryApplyDriverLogEntity::getPlateNumber, deliveryApplyDriverLogEntity.getPlateNumber())
                .eq(DeliveryApplyDriverLogEntity::getDriverIdNumber, deliveryApplyDriverLogEntity.getDriverIdNumber())
                .eq(DeliveryApplyDriverLogEntity::getTransportWay, deliveryApplyDriverLogEntity.getTransportWay()));

        if (driverList.isEmpty()) {
            this.save(deliveryApplyDriverLogEntity);
        } else {
            DeliveryApplyDriverLogEntity driver = driverList.get(0);
            driver.setDriverName(deliveryApplyDriverLogEntity.getDriverName())
                    .setCarType(deliveryApplyDriverLogEntity.getCarType())
                    .setOnboardPhone(deliveryApplyDriverLogEntity.getOnboardPhone())
                    .setTrailerNumber(deliveryApplyDriverLogEntity.getTrailerNumber())
                    .setRemark(deliveryApplyDriverLogEntity.getRemark())
                    .setTransportWay(deliveryApplyDriverLogEntity.getTransportWay())
                    .setFreeboard(deliveryApplyDriverLogEntity.getFreeboard())
                    .setMmsi(deliveryApplyDriverLogEntity.getMmsi());
            this.updateById(driver);
        }
    }

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start
    public List<DeliveryApplyEntity> getUnUseDriverInfoOneYear() {
        return baseMapper.getUnUseDriverInfoOneYear();
    }
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End
}
