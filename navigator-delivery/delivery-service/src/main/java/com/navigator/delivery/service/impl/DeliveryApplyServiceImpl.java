package com.navigator.delivery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.delivery.dao.DeliveryApplyContractDao;
import com.navigator.delivery.dao.DeliveryApplyDao;
import com.navigator.delivery.dao.DeliveryApplyDriverLogDao;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.BillStatusTransitionEnum;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryBillStatusEnum;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyApprovalVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractVO;
import com.navigator.delivery.service.IDeliveryApplyService;
import com.navigator.delivery.service.sync.AtlasSyncService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 提货申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryApplyServiceImpl implements IDeliveryApplyService {

    // Dao
    private final DeliveryApplyDao deliveryApplyDao;
    private final DeliveryApplyContractDao deliveryApplyContractDao;
    private final DeliveryApplyDriverLogDao driverLogDao;
    // facade
    private final EmployFacade employFacade;
    private final CEmployFacade cEmployFacade;
    private final CustomerFacade customerFacade;
    private final ContractFacade contractFacade;
    private final FileBusinessFacade fileBusinessFacade;
    private final SiteFacade siteFacade;
    private final CategoryFacade categoryFacade;
    private final WarehouseFacade warehouseFacade;
    // service
    private final DeliveryAsyncExecuteService deliveryAsyncExecute;
    private final AtlasSyncService atlasSyncService;
    // util
    private final RedisUtil redisUtil;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        DeliveryApplyEntity deliveryApply = BeanUtil.toBean(deliveryApplyDTO, DeliveryApplyEntity.class);

        deliveryApply
                // 生成申请单号
                .setCode(genDeliveryApplyCode(deliveryApplyDTO.getGoodsCategoryId(), deliveryApplyDTO.getSupplierId(), deliveryApply.getDeliveryFactoryCode()))
                .setBillStatus(DeliveryBillStatusEnum.NOT_BILLED.getValue())
                .setThirdSys(SystemEnum.LKG.getValue());

        // 提货方式
        if (StringUtil.isNullBlank(deliveryApply.getDeliveryType())) {
            String plateNumber = deliveryApplyDTO.getPlateNumber();
            if (StrUtil.isNotBlank(plateNumber)) {
                deliveryApply.setDeliveryType(plateNumber.matches(".*[A-Z]+.*") ? "汽车" : "驳船");
            }
        }

        // 操作人
        CEmployEntity employEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
        if (null != employEntity) {
            deliveryApply
                    //.setCustomerId(employEntity.getCustomerId())
                    .setCreatedBy(employEntity.getName())
                    .setUpdatedBy(employEntity.getName());
        }

        // 判断是保存还是提交
        LogBizCodeEnum bizCodeEnum;
        if (deliveryApplyDTO.getSubmitType() == 1) {
            bizCodeEnum = LogBizCodeEnum.SAVE_DELIVERY_APPLY;
            // 保存
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.NEW.getValue());

            deliveryApplyDao.save(deliveryApply);
        } else {
            bizCodeEnum = LogBizCodeEnum.SUBMIT_DELIVERY_APPLY;
            // 提交
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());

            // 申请时间
            deliveryApply.setApplyAt(new Date());

            deliveryApplyDao.save(deliveryApply);

            // 校验申请单
            checkDeliveryApply(deliveryApply);

            // 自动匹配合同
            autoMatchContract(deliveryApply);

            // 保存司机信息
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
        }

        // 保存附件
        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(deliveryApply.getId())
                .setCategoryType(FileCategoryType.DELIVERY_APPLY.getCode())
                .setModuleType(ModuleTypeEnum.DELIVERY.getModule())
                .setFileIdList(deliveryApplyDTO.getFileIdList()));

        // 保存操作记录
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply,
                JSON.toJSONString(deliveryApplyDTO), JSON.toJSONString(deliveryApply.getDeliveryApplyContractList()),
                bizCodeEnum, SystemEnum.COLUMBUS,
                Integer.valueOf(JwtUtils.getCurrentUserId()));

        return deliveryApply.getCode();
    }

    /**
     * 自动匹配合同
     *
     * @param deliveryApply 申请单数据
     */
    private void autoMatchContract(DeliveryApplyEntity deliveryApply) {
        Result result = contractFacade.getDeliveryApplyContractList(new DeliveryApplyContractQO()
                .setGoodsId(deliveryApply.getGoodsId())
                .setCustomerId(deliveryApply.getCustomerId())
                .setSupplierId(deliveryApply.getSupplierId())
                .setDeliveryFactoryCode(deliveryApply.getDeliveryFactoryCode()));
        if (result.isSuccess()) {
            List<ContractEntity> contractList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractEntity.class);

            // 可提量累加
            BigDecimal totalCanDeliveryNum = contractList.stream().map(ContractEntity::getCanDeliveryNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            // 根据申请的数量依次匹配合同 90
            BigDecimal applyNum = deliveryApply.getApplyNum();

            if (BigDecimalUtil.isGreater(applyNum, totalCanDeliveryNum)) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
            }

            // contract1 50
            // contract2 50
            for (ContractEntity contractEntity : contractList) {
                BigDecimal canDeliveryNum = contractEntity.getCanDeliveryNum();

                if (BigDecimalUtil.isGreaterOrEqual(canDeliveryNum, applyNum)) {
                    // 合同剩余数量大于等于申请数量，直接匹配
                    processDeliveryContract(contractEntity, deliveryApply, applyNum);
                    break;
                } else {
                    // 合同剩余数量小于申请数量，匹配后继续匹配下一个合同
                    // 保存申请单合同数据
                    processDeliveryContract(contractEntity, deliveryApply, canDeliveryNum);
                    applyNum = applyNum.subtract(canDeliveryNum);
                }
            }
        }
    }

    /**
     * 处理提货申请合同数据
     *
     * @param contractEntity 合同数据
     * @param deliveryApply  申请单数据
     * @param applyNum       申请数量
     */
    private void processDeliveryContract(ContractEntity contractEntity, DeliveryApplyEntity deliveryApply, BigDecimal applyNum) {
        // 判断申请数量是否小于等于0
        if (BigDecimalUtil.isLessEqualThanZero(applyNum)) {
            return;
        }
        // 保存申请单合同数据
        DeliveryApplyContractEntity applyContract = BeanUtil.toBean(contractEntity, DeliveryApplyContractEntity.class);
        deliveryApplyContractDao.save(applyContract.setApplyId(deliveryApply.getId())
                .setContractId(contractEntity.getId())
                .setAllocateNum(applyNum)
                .setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, applyNum, contractEntity.getUnitPrice()))
                .setContractSignDate(contractEntity.getSignDate())
                .setCreatedBy(deliveryApply.getCreatedBy())
                .setUpdatedBy(deliveryApply.getUpdatedBy())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date()));

        // 保存申请单合同数据
        if (CollectionUtil.isEmpty(deliveryApply.getDeliveryApplyContractList())) {
            deliveryApply.setDeliveryApplyContractList(new ArrayList<>());
        }
        deliveryApply.getDeliveryApplyContractList().add(applyContract);

        // 更新申请单分配合同数量
        deliveryApply.setTotalAllocateNum(deliveryApply.getTotalAllocateNum().add(applyNum));
        deliveryApplyDao.updateById(deliveryApply);

        // 更新合同申请提货数量
        contractFacade.updateContract(contractEntity.setApplyDeliveryNum(contractEntity.getApplyDeliveryNum().add(applyNum)));

        // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 Start
        // 记录applyNum日志
        recordApplyNumLog(contractEntity.getContractCode(), "提货申请提交", contractEntity.getApplyDeliveryNum().subtract(applyNum), contractEntity.getApplyDeliveryNum());
        // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 End
    }

    /**
     * 生成申请单号 规则：D+主体+工厂+品类+年月+自然序列5位数
     *
     * @param goodsCategoryId 商品品类ID
     * @param supplierId      卖方主体ID
     * @param deliveryFactory 提货工厂
     */
    @Override
    public String genDeliveryApplyCode(Integer goodsCategoryId, Integer supplierId, String deliveryFactory) {
        // 获取商品品类
        CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(goodsCategoryId);
        String goodsCategory = categoryEntity.getCode();

        // 公司名称
        String companyCode = "";
        CustomerDTO customerDTO = customerFacade.getCustomerById(supplierId);
        SiteEntity siteEntity = siteFacade.getSiteByCompanyIdAndFactoryCode(customerDTO.getCompanyId(), deliveryFactory);
        if (siteEntity != null) {
            companyCode = siteEntity.getCompanyCode();
        }

        // 当前年月
        String yyyyMM = DateUtil.format(DateUtil.date(), "yyyyMM");

        // 获取当月最大的申请单号
        String sequence = "deliveryApply:sequence" + ":" + companyCode + ":" + deliveryFactory + ":" + goodsCategory.toLowerCase() + ":" + DateUtil.format(DateUtil.date(), "yyyy");

        // 获取递增码
        if (null == redisUtil.get(sequence)) {
            // 获取最大的申请单号
            String maxCode = deliveryApplyDao.getMaxDeliveryApplyCode(goodsCategoryId, supplierId, deliveryFactory);
            if (StrUtil.isNotBlank(maxCode)) {
                String code = maxCode.substring(maxCode.length() - 5);
                redisUtil.set(sequence, Long.parseLong(code));
            }
        }

        // 获取递增码
        long incr = redisUtil.incr(sequence, 1L);

        return "D" + companyCode + deliveryFactory + goodsCategory + yyyyMM + String.format("%05d", incr);
    }

    @Override
    public String batchSubmitDeliveryApply(List<Integer> applyIds) {
        // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
        List<Integer> distinctIds = applyIds.stream()
                .distinct()
                .collect(Collectors.toList());
        // 从远到近进行提交
        Collections.reverse(distinctIds);

        // 批量提交 - 统计成功与失败的数量
        int successCount = 0;
        int failCount = 0;
        for (Integer applyId : distinctIds) {
            // 1002481 case-提货功能优化 Author: Mr 2024-04-13 End
            DeliveryApplyEntity deliveryApply = deliveryApplyDao.getById(applyId);

            try {
                checkDeliveryApply(deliveryApply);

                // 判断是否已经提交
                if (deliveryApply.getApplyStatus() == DeliveryApplyStatusEnum.NEW.getValue()) {
                    // 未提交
                    deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());

                    // 申请时间
                    deliveryApply.setApplyAt(new Date());

                    deliveryApplyDao.updateById(deliveryApply);

                    // 自动匹配合同
                    autoMatchContract(deliveryApply);

                    // BUGFIX：case-1002642 客户线上提货委托端 无法记忆司机信息 Author: Mr 2024-06-17 Start
                    // 保存司机信息
                    DeliveryApplyDTO deliveryApplyDTO = BeanUtil.toBean(deliveryApply, DeliveryApplyDTO.class);
                    deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
                    // BUGFIX：case-1002642 客户线上提货委托端 无法记忆司机信息 Author: Mr 2024-06-17 End

                    successCount++;
                } else {
                    // 已提交
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量提交申请单异常：{}", e.getMessage(), e);
                failCount++;

                // BUGFIX：case-1003136 客户线上委托（对接lkg），分配的合同为空 Author: Mr 2025-04-17 Start
                // 回滚提单状态
                if (deliveryApply.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                    deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.NEW.getValue());
                    deliveryApply.setApplyAt(null);
                    deliveryApply.setUpdatedAt(new Date());
                    deliveryApplyDao.updateById(deliveryApply);
                }
                // BUGFIX：case-1003136 客户线上委托（对接lkg），分配的合同为空 Author: Mr 2025-04-17 End
            }
        }
        return "成功" + successCount + "条，失败" + failCount + "条";
    }

    @Override
    // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
    public String updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO) {
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
        DeliveryApplyEntity deliveryApply = BeanUtil.toBean(deliveryApplyDTO, DeliveryApplyEntity.class);
        deliveryApply.setId(deliveryApplyDTO.getApplyId())
                .setUpdatedAt(new Date())
                .setUpdatedBy(cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());

        // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-03 Start
        // 状态判断:已经提交的申请单不能再提交,现系统提交完变成待审核状态（以数据库的状态为准）
        if (null != deliveryApplyDTO.getApplyId()) {
            DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(deliveryApplyDTO.getApplyId());
            if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_STATUS_ERROR);
            }
        }
        // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-03 End


        // 判断是保存还是提交
        if (deliveryApplyDTO.getSubmitType() == 2) {
            // 校验提交数据
            checkDeliveryApply(deliveryApply);

            // 新录入 → 待审核|| 审核驳回 → 待审核
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());

            // 申请时间
            deliveryApply.setApplyAt(new Date());

            deliveryApplyDao.updateById(deliveryApply);

            // 自动匹配合同
            autoMatchContract(deliveryApply);

            // 保存司机信息
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
        }

        // 更新附件信息
        fileBusinessFacade.dropFileRelation(deliveryApply.getId(), FileCategoryType.DELIVERY_APPLY.getCode(), "更新提货申请附件信息");

        fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                .setBizId(deliveryApply.getId())
                .setCategoryType(FileCategoryType.DELIVERY_APPLY.getCode())
                .setModuleType(ModuleTypeEnum.DELIVERY.getModule())
                .setFileIdList(deliveryApplyDTO.getFileIdList()));

        // 记录日志信息
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply,
                JSON.toJSONString(deliveryApplyDTO), null,
                LogBizCodeEnum.UPDATE_DELIVERY_APPLY, SystemEnum.COLUMBUS,
                Integer.valueOf(JwtUtils.getCurrentUserId()));

        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
        deliveryApplyDao.updateById(deliveryApply);

        return deliveryApply.getCode();
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
    }

    @Override
    public boolean submitDeliveryApplyById(Integer applyId) {
        DeliveryApplyEntity deliveryApply = deliveryApplyDao.getById(applyId);
        // 校验申请单
        checkDeliveryApply(deliveryApply);

        // 判断是否已经提交
        if (deliveryApply.getApplyStatus() == DeliveryApplyStatusEnum.NEW.getValue()) {
            // 未提交
            deliveryApply.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());

            // 申请时间
            deliveryApply.setApplyAt(new Date());

            // 自动匹配合同
            autoMatchContract(deliveryApply);

            // BUGFIX：case-1002642 客户线上提货委托端 无法记忆司机信息 Author: Mr 2024-06-17 Start
            // 保存司机信息
            DeliveryApplyDTO deliveryApplyDTO = BeanUtil.toBean(deliveryApply, DeliveryApplyDTO.class);
            deliveryAsyncExecute.recordDriverInputLog(deliveryApply, deliveryApplyDTO);
            // BUGFIX：case-1002642 客户线上提货委托端 无法记忆司机信息 Author: Mr 2024-06-17 End
        } else {
            // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-04 Start
            // 状态判断:已经提交的申请单不能再提交
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_STATUS_ERROR);
            // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-07-04 End
        }

        // 保存操作记录
        deliveryAsyncExecute.recordDeliveryApplyLog(deliveryApply, String.valueOf(applyId), JSON.toJSONString(deliveryApply),
                LogBizCodeEnum.SUBMIT_DELIVERY_APPLY, SystemEnum.COLUMBUS,
                Integer.valueOf(JwtUtils.getCurrentUserId()));

        deliveryApply.setUpdatedAt(new Date())
                .setUpdatedBy(cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());
        return deliveryApplyDao.updateById(deliveryApply);
    }

    /**
     * 校验申请单
     *
     * @param deliveryApply 申请单数据
     * @return
     */
    private void checkDeliveryApply(DeliveryApplyEntity deliveryApply) {
        // 判断申请单是否存在
        if (null == deliveryApply) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NOT_EXIST);
        }

        // 判断申请数量
        if (BigDecimalUtil.isLessEqualThanZero(deliveryApply.getApplyNum())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
        }

        // 调用合同服务接口
        Result result = contractFacade.getDeliveryApplyContractGroup(new DeliveryApplyContractQO()
                .setGoodsId(deliveryApply.getGoodsId())
                .setDeliveryFactoryCode(deliveryApply.getDeliveryFactoryCode())
                .setSupplierId(deliveryApply.getSupplierId()));
        if (result.isSuccess()) {
            List<DeliveryApplyContractVO> contractVOList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyContractVO.class);

            if (contractVOList.isEmpty() || BigDecimalUtil.isGreater(deliveryApply.getApplyNum(), contractVOList.get(0).getCanDeliveryNum())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
            }
        }

        // 车船号
        if (StrUtil.isBlank(deliveryApply.getPlateNumber())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_PLATE_NUMBER_ERROR);
        }

        // 司机姓名
        if (StrUtil.isBlank(deliveryApply.getDriverName())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_NAME_ERROR);
        }

        // LDC库/外库
        if (deliveryApply.getWarehouseType() == null || deliveryApply.getWarehouseType() == 0) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_WAREHOUSE_TYPE_ERROR);
        }

        // 提货库点
        Result<WarehouseEntity> warehouseResult = warehouseFacade.getWarehouseById(deliveryApply.getDeliveryWarehouseId());
        if (warehouseResult.isSuccess()) {
            WarehouseEntity deliveryWarehouse = warehouseResult.getData();
            if (null == deliveryWarehouse || Objects.equals(deliveryWarehouse.getStatus(), DisableStatusEnum.DISABLE.getValue())) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DELIVERY_WAREHOUSE_ERROR);
            }
        }

        // 司机身份证号
        if (StrUtil.isBlank(deliveryApply.getDriverIdNumber())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR);
        }

        // 随车电话
        if (StrUtil.isBlank(deliveryApply.getOnboardPhone())) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_ONBOARD_PHONE_ERROR);
        }

        // 是否拼车/船
        if (deliveryApply.getIsCarpool() == null) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_IS_SHARE_ERROR);
        }

        // 检查账套
        CustomerEntity supplier = customerFacade.getCustomerById(deliveryApply.getSupplierId());
        SiteEntity site = siteFacade.getSiteByCompanyIdAndFactoryCode(supplier.getCompanyId(), deliveryApply.getDeliveryFactoryCode());
        if (site == null) {
            throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_SITE_EXCEPTION);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reAssignContract(DeliveryApplyAssignContractDTO assignContractDTO) {

        // 重新分配合同
        invalidApplyContract(assignContractDTO.getApplyId(), true);

        // 保存新的合同
        List<DeliveryApplyContractDTO> contractList = assignContractDTO.getContractList();
        for (DeliveryApplyContractDTO deliveryApplyContractDTO : contractList) {
            // 分配数量为0的合同不保存
            if (BigDecimalUtil.isLessEqualThanZero(deliveryApplyContractDTO.getAllocateNum())) {
                continue;
            }

            ContractEntity contract = contractFacade.getBasicContractById(deliveryApplyContractDTO.getContractId());
            DeliveryApplyContractEntity applyContract = BeanUtil.toBean(contract, DeliveryApplyContractEntity.class);

            String operator = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();

            deliveryApplyContractDao.save(applyContract.setApplyId(assignContractDTO.getApplyId())
                    .setContractId(deliveryApplyContractDTO.getContractId())
                    .setAllocateNum(deliveryApplyContractDTO.getAllocateNum())
                    .setAllocateAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, deliveryApplyContractDTO.getAllocateNum(), contract.getUnitPrice()))
                    .setContractSignDate(contract.getSignDate())
                    .setCreatedBy(operator)
                    .setUpdatedBy(operator)
                    .setCreatedAt(new Date())
                    .setUpdatedAt(new Date()));

            // 更新合同申请提货数量
            contractFacade.updateContract(contract.setApplyDeliveryNum(contract.getApplyDeliveryNum().add(deliveryApplyContractDTO.getAllocateNum())));

            // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 Start
            // 记录applyNum日志
            recordApplyNumLog(contract.getContractCode(), "重新分配合同", contract.getApplyDeliveryNum().subtract(deliveryApplyContractDTO.getAllocateNum()), contract.getApplyDeliveryNum());
            // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 End
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAssignContract(Integer applyId, Integer contractId) {
        // 查询申请单合同数据
        DeliveryApplyContractEntity applyContract = deliveryApplyContractDao.getByApplyIdAndContractId(applyId, contractId);
        if (null != applyContract) {
            removeApplyContract(applyContract, true);
        }
        return true;
    }

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start
    @Override
    public boolean clearDriverInfoOneYear() {
        // 查询一年内未使用过的历史数据
        List<DeliveryApplyEntity> applyList = driverLogDao.getUnUseDriverInfoOneYear();

        log.info("clearDriverInfoOneYear applyList:{}", applyList);

        // 清楚数据
        applyList.forEach(apply -> driverLogDao.removeById(apply.getId()));

        return true;
    }
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End

    @Override
    public boolean auditDeliveryApply(DeliveryApplyApprovalDTO deliveryApplyApprovalDTO) {
        // 审核申请单
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(deliveryApplyApprovalDTO.getApplyId());
        // 申请单状态为待审核
        if (null != applyEntity) {
            if (applyEntity.getApplyStatus() != DeliveryApplyStatusEnum.WAIT_APPROVE.getValue() &&
                    applyEntity.getApplyStatus() != DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_AUDIT_ERROR);
            }

            // 校验合同的状态
            List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getByApplyId(applyEntity.getId());
            if (CollectionUtil.isEmpty(applyContractEntityList)) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_CONTRACT_NOT_EXIST);
            } else {
                boolean errorFlag = false;
                StringBuilder errorInfo = new StringBuilder();
                for (DeliveryApplyContractEntity applyContractEntity : applyContractEntityList) {
                    ContractEntity contractEntity = contractFacade.getBasicContractById(applyContractEntity.getContractId());
                    if (null != contractEntity && contractEntity.getStatus() != ContractStatusEnum.EFFECTIVE.getValue()) {
                        errorFlag = true;
                        errorInfo.append(contractEntity.getContractCode()).append("合同状态").append(ContractStatusEnum.getByValue(contractEntity.getStatus()).getDesc()).append("\r\n");
                    }
                }
                // 合同状态异常
                if (errorFlag) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_AUDIT_ERROR.getCode(), errorInfo.toString());
                }
            }

            String operator = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();

            applyEntity.setApprovalBy(operator)
                    .setApprovalAt(new Date())
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(operator);

            // 保存审核记录
            String approvalInfo = applyEntity.getApprovalInfo();
            List<DeliveryApplyApprovalVO> approvalVOList = new ArrayList<>();
            if (null != approvalInfo) {
                approvalVOList = JSON.parseArray(approvalInfo, DeliveryApplyApprovalVO.class);
                if (CollectionUtil.isEmpty(approvalVOList)) {
                    approvalVOList = new ArrayList<>();
                }
            }

            // 审核状态
            int approvalStatus = deliveryApplyApprovalDTO.getApprovalStatus();
            DeliveryApplyApprovalVO deliveryApplyApprovalVO = new DeliveryApplyApprovalVO()
                    .setApprovalTime(applyEntity.getApprovalAt())
                    .setApprovalResult(DeliveryApplyStatusEnum.getByValue(approvalStatus).getDesc())
                    .setApprovalUser(operator)
                    .setApprovalRemark(deliveryApplyApprovalDTO.getApprovalRemark());

            approvalVOList.add(deliveryApplyApprovalVO);
            applyEntity.setApprovalInfo(JSON.toJSONString(approvalVOList));

            // 保存操作记录
            LogBizCodeEnum logBizCodeEnum = null;
            // 执行审核，发送站内信
            if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                if (approvalStatus == DeliveryApplyStatusEnum.APPROVED.getValue()) {
                    // 待审核 → 审核通过
                    deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_PASS);

                    logBizCodeEnum = LogBizCodeEnum.APPROVE_DELIVERY_APPLY;
                } else {
                    // 待审核 → 审核驳回
                    deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_APPROVE_REJECT);

                    // 作废申请单合同
                    invalidApplyContract(deliveryApplyApprovalDTO.getApplyId(), true);

                    logBizCodeEnum = LogBizCodeEnum.REJECT_DELIVERY_APPLY;
                }
            } else if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue()) {
                // 作废待审核 → 审核通过 || 已作废
                if (approvalStatus == DeliveryApplyStatusEnum.APPROVED.getValue()) {
                    // 审核通过 -> 已作废
                    approvalStatus = DeliveryApplyStatusEnum.INVALID.getValue();

                    // 作废申请单合同
                    invalidApplyContract(deliveryApplyApprovalDTO.getApplyId(), false);

                    logBizCodeEnum = LogBizCodeEnum.INVALID_APPROVE_DELIVERY_APPLY;
                } else {
                    // 审核驳回 -> 审核通过
                    approvalStatus = DeliveryApplyStatusEnum.APPROVED.getValue();

                    logBizCodeEnum = LogBizCodeEnum.INVALID_REJECT_DELIVERY_APPLY;
                }
                deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_INVALID_APPROVE);
            }

            applyEntity.setApplyStatus(approvalStatus);

            deliveryAsyncExecute.recordDeliveryApplyLog(applyEntity,
                    JSON.toJSONString(applyEntity), null,
                    logBizCodeEnum, SystemEnum.MAGELLAN,
                    Integer.valueOf(JwtUtils.getCurrentUserId()));

            return deliveryApplyDao.updateById(applyEntity);
        }
        return true;
    }

    @Override
    public boolean cancelDeliveryApply(Integer applyId) {
        // 撤回申请单
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyId);
        if (null != applyEntity) {
            if (applyEntity.getApplyStatus() != DeliveryApplyStatusEnum.WAIT_APPROVE.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_CANCEL_ERROR);
            }

            applyEntity.setApplyStatus(DeliveryApplyStatusEnum.NEW.getValue())
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());

            applyEntity.setDeliveryApplyContractList(deliveryApplyContractDao.getByApplyId(applyId));

            // 作废申请单合同
            invalidApplyContract(applyId, true);

            // 客户撤回申请单，发送站内信
            deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_CANCEL);

            // 保存操作记录
            deliveryAsyncExecute.recordDeliveryApplyLog(applyEntity,
                    JSON.toJSONString(applyEntity), null,
                    LogBizCodeEnum.CANCEL_DELIVERY_APPLY, SystemEnum.COLUMBUS,
                    Integer.valueOf(JwtUtils.getCurrentUserId()));
            return deliveryApplyDao.updateById(applyEntity);
        }
        return true;
    }

    @Override
    public boolean batchAuditPassDeliveryApply(List<Integer> applyIds) {
        // applyIds去重
        applyIds = applyIds.stream().distinct().collect(Collectors.toList());

        // 批量审核通过
        applyIds.forEach(applyId -> auditDeliveryApply(new DeliveryApplyApprovalDTO()
                .setApplyId(applyId)
                .setApprovalStatus(DeliveryApplyStatusEnum.APPROVED.getValue())
                .setApprovalRemark("批量审核通过")));
        return true;

    }

    @Override
    public boolean batchAuditRejectDeliveryApply(List<Integer> applyIds) {
        // applyIds去重
        applyIds = applyIds.stream().distinct().collect(Collectors.toList());

        // 批量审核驳回
        applyIds.forEach(applyId -> auditDeliveryApply(new DeliveryApplyApprovalDTO()
                .setApplyId(applyId)
                .setApprovalStatus(DeliveryApplyStatusEnum.REJECTED.getValue())
                .setApprovalRemark("批量审核驳回")));
        return true;
    }

    @Override
    public boolean invalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO) {

        // 作废申请单
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyInvalidDTO.getApplyId());
        if (null != applyEntity) {
            // 判断开单状态
            if (null != applyEntity.getBillStatus() && applyEntity.getBillStatus() == DeliveryBillStatusEnum.BILLED.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_INVALID_ERROR);
            }

            // 已作废的申请单和作废待审核的不能再次作废
            if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.INVALID.getValue() ||
                    applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue()) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_INVALID_ERROR);
            }

            int applyStatus = DeliveryApplyStatusEnum.INVALID.getValue();
            SystemEnum systemEnum = SystemEnum.MAGELLAN;
            if (Objects.equals(applyInvalidDTO.getTriggerSys(), SystemEnum.COLUMBUS.getValue())) {
                systemEnum = SystemEnum.COLUMBUS;

                // CLB对审核通过的申请单，只能申请作废一次
                if (null != redisUtil.get("deliveryApply:invalid:code:" + applyEntity.getCode())
                        && applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue()) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_CANCEL_ONLY_ONCE);
                }

                if (applyEntity.getApplyStatus() == DeliveryApplyStatusEnum.APPROVED.getValue()) {
                    // 审核通过 → 作废待审核
                    applyStatus = DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue();
                    // redis记录审核通过的次数
                    String deliveryInvalidTimes = "deliveryApply:invalid:code:" + applyEntity.getCode();
                    redisUtil.set(deliveryInvalidTimes, 1);
                }
            }

            // 已作废的清除合同记录
            if (applyStatus == DeliveryApplyStatusEnum.INVALID.getValue()) {
                invalidApplyContract(applyInvalidDTO.getApplyId(), false);
            }

            applyEntity.setApplyStatus(applyStatus)
                    .setInvalidReason(applyInvalidDTO.getInvalidReason());

            // 保存上传附件
            fileBusinessFacade.recordFileRelation(new FileBusinessRelationDTO()
                    .setBizId(applyEntity.getId())
                    .setCategoryType(FileCategoryType.DELIVERY_APPLY.getCode())
                    .setModuleType(ModuleTypeEnum.DELIVERY.getModule())
                    .setFileIdList(applyInvalidDTO.getFileIdList()));

            String operator;
            // 客户作废申请单，发送站内信
            if (SystemEnum.COLUMBUS == systemEnum) {
                operator = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();
                if (applyStatus == DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue()) {
                    deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.MAGELLAN_DELIVERY_APPLY_CUSTOMER_INVALID);
                }
            } else {
                operator = employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName();
                deliveryAsyncExecute.sendInMailMessage(applyEntity, MessageBusinessCodeEnum.COLUMBUS_DELIVERY_APPLY_INVALID);
            }
            applyEntity
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(operator);

            // 保存操作记录
            deliveryAsyncExecute.recordInvalidDeliveryApplyLog(applyEntity,
                    JSON.toJSONString(applyEntity), null,
                    LogBizCodeEnum.INVALID_DELIVERY_APPLY, systemEnum,
                    Integer.valueOf(JwtUtils.getCurrentUserId()));
            return deliveryApplyDao.updateById(applyEntity);
        }
        return false;
    }

    @Override
    // BUGFIX：case-1003146 提货委托无法分配到某一个合同 Author: Mr 2025-04-17 Start
    public boolean inputDeliveryApplyBillStatus(List<Integer> applyIds, Integer billStatus) {
        // applyIds去重
        applyIds = applyIds.stream().distinct().collect(Collectors.toList());

        // 更新申请单开单状态
        for (Integer applyId : applyIds) {
            DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyId);

            if (applyEntity == null) {
                continue;
            }

            Integer currentBillStatus = applyEntity.getBillStatus();

            // 如果当前状态和目标状态一致，跳过处理
            log.info("inputDeliveryApplyBillStatus：申请单ID：{}，当前状态：{}，目标状态：{}", applyId, currentBillStatus, billStatus);
            Optional<BillStatusTransitionEnum> transitionOpt = BillStatusTransitionEnum.from(currentBillStatus, billStatus);
            if (!transitionOpt.isPresent() || transitionOpt.get().isNoOp()) {
                continue;
            }

            BillStatusTransitionEnum transition = transitionOpt.get();

            // 合法流转，处理业务
            if (applyEntity.getApplyStatus() != DeliveryApplyStatusEnum.INVALID.getValue()) {
                List<DeliveryApplyContractEntity> contractEntityList = deliveryApplyContractDao.getByApplyId(applyId);
                for (DeliveryApplyContractEntity applyContractEntity : contractEntityList) {
                    handleStatusTransition(transition, applyContractEntity);
                }
            }

            // 记录开单状态变化
            Map<String, Integer> afterData = new HashMap<>();
            afterData.put("beforeStatus", currentBillStatus);
            afterData.put("afterStatus", billStatus);

            applyEntity.setBillStatus(billStatus)
                    .setUpdatedBy(employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName());

            // 保存操作记录
            deliveryAsyncExecute.recordDeliveryApplyLog(applyEntity,
                    JSON.toJSONString(applyEntity), afterData.toString(),
                    LogBizCodeEnum.INPUT_DELIVERY_APPLY_BILL_STATUS, SystemEnum.MAGELLAN,
                    Integer.valueOf(JwtUtils.getCurrentUserId()));
            deliveryApplyDao.updateById(applyEntity);
        }
        return true;
    }

    /**
     * 处理开单状态流转
     *
     * @param transition          流转状态
     * @param applyContractEntity 申请单合同
     */
    private void handleStatusTransition(BillStatusTransitionEnum transition, DeliveryApplyContractEntity applyContractEntity) {
        ContractEntity contractEntity = contractFacade.getBasicContractById(applyContractEntity.getContractId());
        String contractCode = contractEntity.getContractCode();
        String desc = transition.getDescription();

        switch (transition) {
            case NOT_BILLED_TO_BILLED:
            case NOT_BILLED_TO_REFUSED:
                BigDecimal applyNum = contractEntity.getApplyDeliveryNum().subtract(applyContractEntity.getAllocateNum());
                if (BigDecimalUtil.isLessThanZero(applyNum)) {
                    throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
                }
                contractFacade.updateContract(contractEntity.setApplyDeliveryNum(applyNum));
                recordApplyNumLog(contractCode, desc,
                        applyNum.add(applyContractEntity.getAllocateNum()), applyNum);
                break;
            case BILLED_TO_NOT_BILLED:
            case REFUSED_TO_NOT_BILLED:
                BigDecimal recoverNum = contractEntity.getApplyDeliveryNum().add(applyContractEntity.getAllocateNum());
                contractFacade.updateContract(contractEntity.setApplyDeliveryNum(recoverNum));
                recordApplyNumLog(contractCode, desc,
                        recoverNum.subtract(applyContractEntity.getAllocateNum()), recoverNum);
                break;
            case BILLED_TO_REFUSED:
            case REFUSED_TO_BILLED:
                // 仅记录日志，无数量变动
                recordApplyNumLog(contractCode, desc, null, null);
                break;

            default:
                break;
        }
    }

    // BUGFIX：case-1003146 提货委托无法分配到某一个合同 Author: Mr 2025-04-17 End

    /**
     * 作废申请单合同
     *
     * @param applyId    申请单id
     * @param deleteFlag 是否删除
     */
    private void invalidApplyContract(Integer applyId, boolean deleteFlag) {
        List<DeliveryApplyContractEntity> applyContractEntityList = deliveryApplyContractDao.getByApplyId(applyId);

        // 删除原有的合同 + 更新合同申请提货数量
        applyContractEntityList.forEach(applyContractEntity -> {
            removeApplyContract(applyContractEntity, deleteFlag);
        });
    }

    /**
     * 自动匹配合同刪除合同
     *
     * @param applyContractEntity 合同
     * @param deleteFlag          是否删除
     */
    private void removeApplyContract(DeliveryApplyContractEntity applyContractEntity, boolean deleteFlag) {

        // 更新申请单分配合同数量
        DeliveryApplyEntity applyEntity = deliveryApplyDao.getById(applyContractEntity.getApplyId());
        applyEntity.setTotalAllocateNum(applyEntity.getTotalAllocateNum().subtract(applyContractEntity.getAllocateNum()))
                .setUpdatedAt(new Date());
        deliveryApplyDao.updateById(applyEntity);

        // 更新合同申请提货数量(已拒绝已扣减合同申请量)
        if (applyEntity.getBillStatus() != DeliveryBillStatusEnum.REFUSED.getValue()) {
            ContractEntity contractEntity = contractFacade.getBasicContractById(applyContractEntity.getContractId());
            BigDecimal applyDeliveryNum = contractEntity.getApplyDeliveryNum().subtract(applyContractEntity.getAllocateNum());
            if (BigDecimalUtil.isLessThanZero(applyDeliveryNum)) {
                throw new BusinessException(ResultCodeEnum.DELIVERY_APPLY_NUM_ERROR);
            }
            contractFacade.updateContract(contractEntity.setApplyDeliveryNum(applyDeliveryNum));

            // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 Start
            // 记录applyNum日志
            recordApplyNumLog(contractEntity.getContractCode(), "自动匹配合同刪除合同", applyDeliveryNum.add(applyContractEntity.getAllocateNum()), applyDeliveryNum);
            // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 End
        }

        // 删除原有的合同
        if (deleteFlag) {
            deliveryApplyContractDao.removeById(applyContractEntity);
        } else {
            applyContractEntity.setIsDeleted(1);
            deliveryApplyContractDao.updateById(applyContractEntity);
        }
    }

    // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 Start

    /**
     * 记录合同申请提货数量日志
     *
     * @param contractCode   合同编号
     * @param action         操作动作
     * @param beforeApplyNum 申请提货数量
     * @param afterApplyNum  操作后申请提货数量
     */
    private void recordApplyNumLog(String contractCode, String action, BigDecimal beforeApplyNum, BigDecimal afterApplyNum) {

        log.info("recordApplyNumLog：业务编号：{}，业务动作：{}，操作对象：{}，操作前值：{}，操作后值：{}",
                contractCode,
                action,
                "applyNum",
                beforeApplyNum,
                afterApplyNum);
    }
    // BUGFIX：case-1002747 提货委托申请无法修改状态为已开单-增加业务变更日志 Author: Mr 2024-11-15 End
}
