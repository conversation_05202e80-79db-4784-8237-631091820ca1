package com.navigator.delivery.service;

import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 提货申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface IDeliveryApplyAtlasService {

    /**
     * 保存/提交申请单
     *
     * @param deliveryApplyDTO
     * @return
     */
    String saveOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO);

    /**
     * 编辑/提交申请单
     *
     * @param deliveryApplyDTO
     * @return
     */
    String updateOrSubmitDeliveryApply(DeliveryApplyDTO deliveryApplyDTO);

    /**
     * 根据申请单id提交申请单
     *
     * @param applyId
     * @param triggerSys
     * @return
     */
    boolean submitDeliveryApplyById(Integer applyId, Integer triggerSys);

    /**
     * 批量提交申请单
     *
     * @param applyIds
     * @return
     */
    String batchSubmitDeliveryApply(List<Integer> applyIds);

    /**
     * 作废申请单
     *
     * @param applyInvalidDTO
     * @return
     */
    boolean invalidDeliveryApply(DeliveryApplyInvalidDTO applyInvalidDTO);

    /**
     * 批量上传-导入文件
     *
     * @param multipartFile
     * @param customerId
     * @param triggerSys
     * @return
     */
    DeliveryApplyAtlasImportResultDTO deliveryApplyImportFile(MultipartFile multipartFile, Integer customerId, Integer triggerSys);

    /**
     * 批量上传-提交
     *
     * @param deliveryApplyAtlasImportDTOList
     * @param triggerSys
     * @return
     */
    int deliveryApplyImportSubmit(List<DeliveryApplyAtlasImportDTO> deliveryApplyAtlasImportDTOList, Integer triggerSys);

    /**
     * 发送消息
     *
     * @param applyId
     */
    void sendInMailMessage(Integer applyId);

    /**
     * 保存/更新 ACK合同
     *
     * @param deliveryAckContractDTO 提货ACK合同
     */
    boolean saveDeliveryApplyAckContract(DeliveryAckContractDTO deliveryAckContractDTO);

    /**
     * 更新申请单
     *
     * @param deliveryApplyEntity 申请单实体
     */
    boolean updateDeliveryApply(DeliveryApplyEntity deliveryApplyEntity);
}
