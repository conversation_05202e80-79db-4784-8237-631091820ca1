package com.navigator.delivery.dao;


import cn.hutool.core.util.StrUtil;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.delivery.mapper.DeliveryWarehouseMapper;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;

import java.util.List;

/**
 * <p>
 * 提货库点配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Dao
public class DeliveryWarehouseDao extends BaseDaoImpl<DeliveryWarehouseMapper, DeliveryWarehouseEntity> {

    public List<DeliveryWarehouseEntity> getDeliveryWarehouseList(DeliveryWarehouseDTO warehouseDTO) {
        return this.lambdaQuery()
                .eq(StrUtil.isNotBlank(warehouseDTO.getWarehouseCode()), DeliveryWarehouseEntity::getCode, warehouseDTO.getWarehouseCode())
                .eq(null != warehouseDTO.getGoodsCategoryId(), DeliveryWarehouseEntity::getGoodsCategoryId, warehouseDTO.getGoodsCategoryId())
                .like(StrUtil.isNotBlank(warehouseDTO.getWarehouseName()), DeliveryWarehouseEntity::getName, StrUtil.isNotBlank(warehouseDTO.getWarehouseName()) ? warehouseDTO.getWarehouseName().trim() : null)
                .eq(StrUtil.isNotBlank(warehouseDTO.getDeliveryFactory()), DeliveryWarehouseEntity::getDeliveryFactory, warehouseDTO.getDeliveryFactory())
                .eq(warehouseDTO.getWarehouseType() != null, DeliveryWarehouseEntity::getWarehouseType, warehouseDTO.getWarehouseType())
                .eq(warehouseDTO.getStatus() != null, DeliveryWarehouseEntity::getStatus, warehouseDTO.getStatus())
                .orderByDesc(DeliveryWarehouseEntity::getUpdatedAt)
                .list();
    }
}
