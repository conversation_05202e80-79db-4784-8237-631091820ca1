package com.navigator.delivery.service.sync;

import com.google.gson.Gson;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.enums.AtlasSyncObjectTypeEnum;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncService {
    @Value("${sync.atlas.isOpen:0}")
    private Integer openAtlasSync;  // 是否开启atlas同步
    @Value("${sync.atlas.useMessageQueue:0}")
    private Integer openAtlasMessageQueue; // 是否使用消息队列同步atlas
    @Value("${messageQueue.cuckoo.syncQueueName}")
    private String syncQueueName;  // 消息队列名称

    private final AtlasContractFacade atlasContractFacade;
    private final JmsTemplate jmsTemplate;

    @Async
    public void syncDeliveryInfo(DeliveryApplyEntity deliveryApply, String operatorName, String operationType) {

        log.info("syncDeliveryInfo : {},operator:{}", deliveryApply.getCode(), operatorName);

        log.info("{} is working", Thread.currentThread().getName());

        AtlasSyncRequestDTO atlasSyncRequestDTO = new AtlasSyncRequestDTO();
        atlasSyncRequestDTO
                .setBizId(deliveryApply.getId())
                .setBizCode(deliveryApply.getCode())
                .setObjectType(AtlasSyncObjectTypeEnum.DELIVERY.getValue())
                .setOperationType(operationType)
                .setCreatedBy(operatorName)
                .setUpdatedBy(operatorName);

        // 提货同步至Atlas
        syncAtlas(atlasSyncRequestDTO);
    }

    private void syncAtlas(AtlasSyncRequestDTO atlasSyncRequestDTO) {
        // 合同同步至Atlas
        if (openAtlasSync == 1) {
            // 日志记录是否打开atlas同步
            log.info("===============[{}] Atlas Sync Open : {}===============", atlasSyncRequestDTO.getBizCode(), openAtlasSync);
            // 是否使用消息队列同步atlas
            if (openAtlasMessageQueue == 0) {
                atlasContractFacade.syncDeliveryRequest(atlasSyncRequestDTO);
            } else {
                jmsTemplate.convertAndSend(syncQueueName, new Gson().toJson(atlasSyncRequestDTO));

                log.info("===============[{}] Queue Message Send Success : {}===============", syncQueueName, atlasSyncRequestDTO);
            }
        }
    }
}
