package com.navigator.delivery.mapper;

import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class DeliveryApplyMapperTest {
    @Resource
    DeliveryApplyMapper deliveryApplyMapper;

    @Test
    public void testCallExchangePickQty() {
        ExchangePickQtyQO pickQtyQO = new ExchangePickQtyQO();
        pickQtyQO.setCompanyId(1)
                .setCustomerId(6)
                .setGoodsId(84)
                .setDeliveryFactoryCode("TJIB");

        ExchangePickQtyDTO exchangePickQtyDTO = deliveryApplyMapper.callExchangePickQty(pickQtyQO).get(0);
        log.info("exchangePickQtyDTO:{}", exchangePickQtyDTO);
    }

    @Test
    public void testCallExecutePreAllocation() {
        ExecutePreAllocationQO preAllocationQO = new ExecutePreAllocationQO();
        preAllocationQO.setCompanyId(1)
                .setCustomerId(6)
                .setGoodsId(84)
                .setDeliveryFactoryCode("TJIB")
                .setRequestQty(new BigDecimal(100));

        List<ExecutePreAllocationDTO> executePreAllocationDTOS = deliveryApplyMapper.callExecutePreAllocation(preAllocationQO);
        log.info("executePreAllocationDTOS:{}", executePreAllocationDTOS);
    }
}
