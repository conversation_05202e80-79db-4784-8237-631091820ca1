apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-delivery-sim2-prod
  namespace: sim2-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-delivery-sim2-prod
  template:
    metadata:
      labels:
        app: ldc-navigator-delivery-sim2-prod
    spec:
      containers:
      - image: csm4pnvgacr001.azurecr.cn/navigator-delivery-sim2-prod:#{Build.BuildId}#
        name: ldc-navigator-delivery-sim2-prod
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "sim2-prod"
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4pnvgsto002-secret  # required
            shareName: logs-sim2-prod  # required
            server: csm4pnvgsto002.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional

---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-delivery-sim2-prod
  namespace: sim2-prod
spec:
  type: ClusterIP
  ports:
  - port: 9112
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-delivery-sim2-prod
