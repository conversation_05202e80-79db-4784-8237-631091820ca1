apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-delivery-test
  namespace: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-delivery-test
  template:
    metadata:
      labels:
        app: ldc-navigator-delivery-test
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-delivery-test:#{Build.BuildId}#
        name: ldc-navigator-delivery-test
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "uat" 

---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-delivery-test
  namespace: test
spec:
  type: ClusterIP
  ports:
  - port: 9112
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-delivery-test
