package com.navigator.delivery.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请表 RPA导出DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyRpaExportDTO {

    @Excel(name = "Item", orderNum = "1")
    @ApiModelProperty(value = "自然序列号")
    private Integer item;

    @Excel(name = "Outbound date", format = "yyyy-MM-dd", orderNum = "2")
    @ApiModelProperty(value = "提货委托申请日期")
    private Date outboundDate;

    @Excel(name = "Warehouse/port", orderNum = "3")
    @ApiModelProperty(value = "提货库点名称")
    private String warehousePort;

    @Excel(name = "Contract No", orderNum = "4", width = 25)
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @Excel(name = "Commodity Name", orderNum = "5", width = 25)
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @Excel(name = "Delivery Num", orderNum = "6")
    @ApiModelProperty(value = "提货数量")
    private BigDecimal deliveryNum;

    @Excel(name = "Commodity", orderNum = "7", width = 20)
    @ApiModelProperty(value = "货品代码")
    private String goodsCode;

    @Excel(name = "Commodity Spec", orderNum = "8")
    @ApiModelProperty(value = "规格")
    private String goodsSpec;

    @Excel(name = "Truck No", orderNum = "9")
    @ApiModelProperty(value = "车船号码")
    private String truckNo;

    @Excel(name = "Contact No", orderNum = "10", width = 30)
    @ApiModelProperty(value = "车船主联系方式")
    private String contactNo;

    @Excel(name = "ID No", orderNum = "11", width = 30)
    @ApiModelProperty(value = "车船主证件号")
    private String idNo;

    @Excel(name = "Delivery Location", orderNum = "12")
    @ApiModelProperty(value = "收发位置说明")
    private String deliveryLocation;

    @Excel(name = "Sale C/P.", orderNum = "13", width = 30)
    @ApiModelProperty(value = "买方名称")
    private String customerName;

    @Excel(name = "Delivery Type", orderNum = "14")
    @ApiModelProperty(value = "汽车/船运")
    private String deliveryType;

    @Excel(name = "Delivery Apply Code", orderNum = "15", width = 25)
    @ApiModelProperty(value = "提货委托申请编号")
    private String deliveryApplyCode;

}
