package com.navigator.delivery.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class BaseDeliveryApplyEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Excel(name = "申请编号", orderNum = "1", width = 25)
    @ApiModelProperty(value = "申请编号")
    private String code;

    @Excel(name = "数量", orderNum = "6", numFormat = "0.000")
    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "已分配合同的数量")
    private BigDecimal totalAllocateNum = BigDecimal.ZERO;

    @Excel(name = "状态", orderNum = "33", replace = {"新录入_1", "待审核_2", "审核通过_3", "审核驳回_4", "作废待审核_5", "已作废_6"})
    @ApiModelProperty(value = "申请状态（0.所有 1.新录入 2.待审核 3.审核通过 4.审核驳回 5.作废待审核 6.已作废）")
    private Integer applyStatus;

    @Excel(name = "提单状态", orderNum = "34", replace = {"未开单_1", "已开单_2", "已拒绝_3"})
    @ApiModelProperty(value = "开单状态（1.未开单 2.已开单）")
    private Integer billStatus;

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体ID")
    private Integer supplierId;

    @ApiModelProperty(value = "货品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品品类id")
    private Integer goodsCategoryId;

    @Excel(name = "LDC库/外库", orderNum = "28", replace = {"LDC库_1", "外库_2"})
    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @ApiModelProperty(value = "提货库点id")
    private Integer deliveryWarehouseId;

    @Excel(name = "提货方式", orderNum = "30")
    @ApiModelProperty(value = "提货方式")
    private String deliveryType;

    @Excel(name = "提货日期", orderNum = "27", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "计划提货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDeliveryTime;

    @Excel(name = "提货工厂", orderNum = "7")
    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @Excel(name = "车/船号", orderNum = "19")
    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @Excel(name = "挂车号", orderNum = "20")
    @ApiModelProperty(value = "挂车号")
    private String trailerNumber;

    @Excel(name = "司机姓名", orderNum = "21")
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @Excel(name = "司机身份证号", orderNum = "22", width = 25)
    @ApiModelProperty(value = "司机身份证号码")
    private String driverIdNumber;

    @Excel(name = "随车电话", orderNum = "23", width = 20)
    @ApiModelProperty(value = "随车电话")
    private String onboardPhone;

    @Excel(name = "是否拼车/船", orderNum = "24", replace = {"是_1", "否_0"}, width = 20)
    @ApiModelProperty(value = "是否拼车/船")
    private Integer isCarpool;

    @ApiModelProperty(value = "拼车/船信息")
    private String carpoolInfo;

    @Excel(name = "拼车/船优先执行", orderNum = "25", replace = {"是_1", "否_0"}, width = 20)
    @ApiModelProperty(value = "拼车/船优先执行")
    private Integer carpoolPriority;

    @ApiModelProperty(value = "船提目的港")
    private String shipDeliveryDestination;

    @ApiModelProperty(value = "审核信息json")
    private String approvalInfo;

    @Excel(name = "备注", orderNum = "31")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @Excel(name = "申请人", orderNum = "31")
    @ApiModelProperty(value = "申请人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @Excel(name = "申请时间", orderNum = "32", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @Excel(name = "审核时间", orderNum = "35", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalAt;

    @Excel(name = "审核人", orderNum = "36")
    @ApiModelProperty(value = "审批人")
    private String approvalBy;

    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyAt;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiModelProperty(value = "车辆类型")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer carType;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiModelProperty(value = "Atlas接口返回的申请状态 Rejected Blocked Awaiting confirmation Confirmed")
    private String atlasApplyStatus;

    @ApiModelProperty(value = "Atlas接口返回的sub_status")
    private String atlasSubStatus;

    @ApiModelProperty(value = "Approval_comments")
    private String approvalComments;

    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

    @ApiModelProperty(value = "车站")
    private String station;

    @ApiModelProperty(value = "核定干舷")
    private Integer freeboard;

    @ApiModelProperty(value = "MMSI")
    private String mmsi;

    @ApiModelProperty(value = "拼车/船方")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer carpoolCount;

    @ApiModelProperty(value = "拼车/船客户")
    private String carpoolCustomer;

    @ApiModelProperty(value = "装货优先级")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer loadingPriority;

    @ApiModelProperty(value = "操作方")
    private Integer triggerSys;

    @ApiModelProperty(value = "第三方系统")
    private Integer thirdSys;

    @ApiModelProperty(value = "拼车人数")
    private Integer isFinal;

    @ApiModelProperty(value = "车辆状态")
    private String queueStatus;

    @ApiModelProperty(value = "Atlas接口返回的creationStatus")
    private String atlasCreationStatus;
}
