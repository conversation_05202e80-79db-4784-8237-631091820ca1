package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 仓单的可提货量DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Accessors(chain = true)
public class ExchangePickQtyDTO {

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "卖方主体")
    private String companyId;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "触发系统")
    private String system;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "可提货量")
    private BigDecimal pickupQty;

}
