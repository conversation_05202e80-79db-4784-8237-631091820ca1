package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 申请提货预校验
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PreCheckDeliveryApplyQO {

    @ApiModelProperty(value = "申请单id，传已有申请单ID或其他参数")
    private Integer applyId;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体id")
    private Integer supplierId;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "触发系统", hidden = true)
    private Integer triggerSys;

}