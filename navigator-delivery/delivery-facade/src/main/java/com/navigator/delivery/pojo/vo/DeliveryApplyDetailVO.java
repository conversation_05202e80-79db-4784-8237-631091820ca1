package com.navigator.delivery.pojo.vo;

import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <p>
 * 提货申请VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeliveryApplyDetailVO extends DeliveryApplyVOEntity {

    @ApiModelProperty(value = "合同列表")
    private List<DeliveryApplyContractEntity> contractList;

    @ApiModelProperty(value = "提货申请附件列表")
    private List<DeliveryApplyFileVO> fileList;

    @ApiModelProperty(value = "审核意见")
    private String approvalRemark;

}
