package com.navigator.delivery.pojo.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <p>
 * 开单状态流转 枚举
 * </p>
 *
 * <AUTHOR>
 * @description BUGFIX：case-1003146 提货委托无法分配到某一个合同
 * @since 2025-04-22
 */
@Getter
public enum BillStatusTransitionEnum {

    NOT_BILLED_TO_BILLED(1, 2, "未开单 → 已开单"),
    BILLED_TO_NOT_BILLED(2, 1, "已开单 → 未开单"),

    NOT_BILLED_TO_REFUSED(1, 3, "未开单 → 已拒绝"),
    REFUSED_TO_NOT_BILLED(3, 1, "已拒绝 → 未开单"),

    BILLED_TO_REFUSED(2, 3, "已开单 → 已拒绝"),
    REFUSED_TO_BILLED(3, 2, "已拒绝 → 已开单"),

    NOT_BILLED_TO_NOT_BILLED(1, 1, "未开单 → 未开单"),
    BILLED_TO_BILLED(2, 2, "已开单 → 已开单"),
    REFUSED_TO_REFUSED(3, 3, "已拒绝 → 已拒绝");

    private final int from;
    private final int to;
    private final String description;

    BillStatusTransitionEnum(int from, int to, String description) {
        this.from = from;
        this.to = to;
        this.description = description;
    }

    public static Optional<BillStatusTransitionEnum> from(int from, int to) {
        return Arrays.stream(values())
                .filter(t -> t.from == from && t.to == to)
                .findFirst();
    }

    public boolean isNoOp() {
        return this.from == this.to;
    }
}