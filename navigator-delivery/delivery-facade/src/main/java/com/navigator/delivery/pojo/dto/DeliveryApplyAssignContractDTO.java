package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表重新分配合同DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyAssignContractDTO {
    @ApiModelProperty(value = "提货申请id")
    private Integer applyId;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "交提货模式,1自提 2配送 3转货权")
    private Integer deliveryMode;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "开始提货日期")
    private String deliveryStartDate;

    @ApiModelProperty(value = "结束提货日期")
    private String deliveryEndDate;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiModelProperty(value = "发货库点ID")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "合同签订日期")
    private String contractSignDate;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiModelProperty(value = "分配合同集合")
    private List<DeliveryApplyContractDTO> contractList;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体")
    private Integer supplierId;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;
}
