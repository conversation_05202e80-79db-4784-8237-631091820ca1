package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 提货申请表DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyContractDTO {

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "分配金额")
    private BigDecimal allocateAmount;
}
