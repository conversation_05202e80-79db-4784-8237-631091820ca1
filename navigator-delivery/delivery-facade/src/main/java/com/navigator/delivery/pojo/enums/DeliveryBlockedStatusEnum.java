package com.navigator.delivery.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliveryBlockedStatusEnum {
    INSUFFICIENT_OPEN_QTY("Blocked", "Insufficient Open quantity", "可开单量不足", "可开单量不足"),
    ABOVE_CREDIT_LIMIT("Blocked", "Above credit limit", "超过赊销额度", "超过赊销额度"),
    OVERDUE_INVOICE("Blocked", "Overdue invoice", "超期未付款", "超期未付款"),
    INSUFFICIENT_DEPOSIT("Blocked", "Insufficient deposit", "定金不足", "定金不足"),
    INSUFFICIENT_CASH("Blocked", "Insufficient cash", "资金不足", "资金不足"),
    COUNTERPARTY_FROZEN("Blocked", "Counterparty frozen", "客户被冻结", "客户被冻结"),
    BLOCKED_DUE_TO_SHARED_TRANSPORTATION("Blocked", "Blocked due to shared transportation", "因其他拼车/船客户原因，锁定提单", "因其他拼车/船客户原因，锁定提单"),
    REJECTED_INSUFFICIENT_OPEN_QTY("Rejected", "Insufficient Open quantity", "可开单量不足", "可开单量不足"),
    REJECTED_MANUAL("Rejected", "Manual", "Atlas驳回", ""),
    AWAITING_CONFIRMATION_PENDING("Awaiting Confirmation", "Pending", "待审核", "待审核"),
    CONFIRMED_MANUAL("Confirmed", "Manual", "Atlas审批通过", ""),
    CONFIRMED_SPECIAL_PRIVILEGE("Confirmed", "Special privilege - DREXECSP", "使用特殊权限审批", ""),
    CANCELLATION_REQUESTED_MANUAL("Cancellation Requested", "Manual", "作废待审核", "作废待审核"),
    CANCELLATION_REQUESTED_SPECIAL_PRIVILEGE("Cancellation Requested", "Special privilege - DREXECSP", "作废待审核", "作废待审核"),
    CANCELLED_MANUAL("Cancelled", "Manual", "审批Atlas申请的作废", ""),
    CANCELLED_CUSTOMER_REQUESTED("Cancelled", "Customer requested", "审批客户端发起的作废申请", "");

    final String status;
    final String subStatus;
    final String magellanMessage;
    final String columbusMessage;

    public static DeliveryBlockedStatusEnum getByStatusAndSubStatus(String status, String subStatus) {
        for (DeliveryBlockedStatusEnum blockedStatus : DeliveryBlockedStatusEnum.values()) {
            if (blockedStatus.status.equals(status) && blockedStatus.subStatus.equals(subStatus)) {
                return blockedStatus;
            }
        }
        return INSUFFICIENT_OPEN_QTY;
    }

}
