package com.navigator.delivery.facade;

import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractAtlasQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyAssignContractVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "提货委托-atlas")
@FeignClient(name = "navigator-delivery-service")
public interface DeliveryApplyAtlasFacade {


    /**
     * 查询提货可申请汇总列表
     *
     * @param deliveryApplyContractAtlasQO 查询条件
     * @return
     */
    @ApiOperation("查询提货可申请汇总列表")
    @PostMapping("/deliveryApplyAtlas/getDeliveryApplyContractList")
    Result<List<DeliveryApplyContractAtlasVO>> getDeliveryApplyContractList(@RequestBody DeliveryApplyContractAtlasQO deliveryApplyContractAtlasQO);

    /**
     * 申请提货预校验
     *
     * @param preCheckDeliveryApplyQO
     * @return
     */
    @ApiOperation("申请提货预校验")
    @PostMapping("/deliveryApplyAtlas/preCheckDeliveryApply")
    Result<PreCheckDeliveryApplyDTO> preCheckDeliveryApply(@RequestBody PreCheckDeliveryApplyQO preCheckDeliveryApplyQO);

    /**
     * 保存或提交申请单
     *
     * @param deliveryApplyDTO 申请单信息
     * @return
     */
    @ApiOperation("保存或提交申请单")
    @PostMapping("/deliveryApplyAtlas/saveOrSubmitDeliveryApply")
    Result<String> saveOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO);

    /**
     * 编辑或提交申请单
     *
     * @param deliveryApplyDTO 申请单信息
     * @return
     */
    @ApiOperation("编辑或提交申请单")
    @PostMapping("/deliveryApplyAtlas/updateOrSubmitDeliveryApply")
    Result<String> updateOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO);

    /**
     * 提交提货申请单
     *
     * @param applyId    申请单id
     * @param triggerSys
     * @return
     */
    @ApiOperation("提交提货申请单")
    @GetMapping("/deliveryApplyAtlas/submitDeliveryApplyById")
    Result<Boolean> submitDeliveryApplyById(@RequestParam("applyId") Integer applyId, @RequestParam("triggerSys") Integer triggerSys);

    /**
     * 批量提交申请单
     *
     * @param applyIds 申请单id集合
     * @return
     */
    @ApiOperation("批量提交申请单")
    @GetMapping("/deliveryApplyAtlas/batchSubmitDeliveryApply")
    Result<String> batchSubmitDeliveryApply(@RequestParam("applyIds") List<Integer> applyIds);

    /**
     * 作废提货申请单
     *
     * @param applyInvalidDTO 作废信息
     * @return
     */
    @ApiOperation("作废提货申请单")
    @PostMapping("/deliveryApplyAtlas/applyInvalidDeliveryApply")
    Result<Boolean> applyInvalidDeliveryApply(@RequestBody DeliveryApplyInvalidDTO applyInvalidDTO);

    /**
     * 批量上传-模板地址
     *
     * @param triggerSys
     * @return
     */
    @ApiOperation("批量上传-模板地址")
    @GetMapping("/deliveryApplyAtlas/deliveryApplyImportTemplate")
    Result<String> deliveryApplyImportTemplate(@RequestParam("triggerSys") Integer triggerSys);

    /**
     * 批量上传-导入文件
     *
     * @param file
     * @param customerId
     * @param triggerSys
     * @return
     */
    @ApiOperation(value = "批量上传-导入文件")
    @PostMapping(value = "/deliveryApplyAtlas/deliveryApplyImportFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<DeliveryApplyAtlasImportResultDTO> deliveryApplyImportFile(@RequestPart("file") MultipartFile file,
                                                                      @RequestParam(value = "customerId", required = false) Integer customerId,
                                                                      @RequestParam("triggerSys") Integer triggerSys);

    /**
     * 批量上传-提交
     *
     * @param deliveryApplyAtlasImportDTOList
     * @param triggerSys
     * @return
     */
    @ApiOperation("批量上传-提交")
    @PostMapping("/deliveryApplyAtlas/deliveryApplyImportSubmit")
    Result deliveryApplyImportSubmit(@RequestBody List<DeliveryApplyAtlasImportDTO> deliveryApplyAtlasImportDTOList, @RequestParam("triggerSys") Integer triggerSys);

    /**
     * 重新分配合同列表
     *
     * @param assignContractDTO 查询条件
     * @return
     */
    @ApiOperation("重新分配合同列表")
    @PostMapping("/deliveryApplyAtlas/getReAssignContractList")
    Result<DeliveryApplyAssignContractVO> getReAssignContractList(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO);

    /**
     * 发送消息
     *
     * @param applyId 申请单id
     * @return
     */
    @PostMapping("/sendInMailMessage")
    Result sendInMailMessage(@RequestParam("applyId") Integer applyId);

    /**
     * 更新提货单
     *
     * @param deliveryApplyEntity 提货单信息
     * @return
     */
    @PostMapping("/updateDeliveryApply")
    Result<Boolean> updateDeliveryApply(@RequestBody DeliveryApplyEntity deliveryApplyEntity);

    /**
     * 根据申请单code查询申请单
     *
     * @param applyCode 申请单code
     * @return
     */
    @GetMapping("/getDeliveryApplyByCode")
    Result<DeliveryApplyEntity> getDeliveryApplyByCode(@RequestParam("applyCode") String applyCode);

    /**
     * 保存ATLAS提货单确认合同
     *
     * @param deliveryAckContractDTO 确认合同信息
     * @return
     */
    @PostMapping("/saveDeliveryApplyAckContract")
    Result<Boolean> saveDeliveryApplyAckContract(@RequestBody DeliveryAckContractDTO deliveryAckContractDTO);

    /**
     * 根据申请单id查询申请单详情
     *
     * @param applyId 申请单id
     * @return
     */
    @GetMapping("/getDeliveryApplyById")
    Result getDeliveryApplyById(@RequestParam("applyId") Integer applyId);

    /**
     * 获取拼车DR列表
     *
     * @param plateNumber      车牌号
     * @param planDeliveryTime 计划提货时间
     * @return DR列表
     */
    @GetMapping("/getCarpoolDeliveryRequestInfo")
    Result<DeliveryApplyCarpoolInfoDTO> getCarpoolDeliveryRequestInfo(@RequestParam("plateNumber") String plateNumber, @RequestParam("planDeliveryTime") String planDeliveryTime);
}
