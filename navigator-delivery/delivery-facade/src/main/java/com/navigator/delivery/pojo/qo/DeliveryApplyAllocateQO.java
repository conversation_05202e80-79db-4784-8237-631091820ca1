package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提货申请预分配
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyAllocateQO {
    @ApiModelProperty(value = "申请单id")
    private Integer applyId;
    @ApiModelProperty(value = "申请单Code")
    private String applyCode;
    @ApiModelProperty(value = "商品id")
    private Integer goodsId;
    @ApiModelProperty(value = "仓单号码")
    private String warrantNumber;
    @ApiModelProperty(value = "是否是DCE")
    private Integer isExchangeDelivery;
    @ApiModelProperty(value = "是否是豆二")
    private Integer isSoybean2;
}
