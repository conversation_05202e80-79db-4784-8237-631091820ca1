package com.navigator.delivery.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbd_delivery_apply")
@ApiModel(value = "DeliveryApplyEntity对象", description = "提货申请表")
public class DeliveryApplyEntity extends BaseDeliveryApplyEntity {

    @TableField(exist = false)
    private List<DeliveryApplyContractEntity> deliveryApplyContractList;

}
