package com.navigator.delivery.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 提货开单状态
 * </p>
 *
 * @since 2023/8/18
 */
@Getter
@AllArgsConstructor
public enum DeliveryBillStatusEnum {
    /**
     * 提货开单状态  1.未开单 2.已开单 3.已拒绝
     */
    NOT_BILLED(1, "未开单"),
    BILLED(2, "已开单"),
    REFUSED(3, "已拒绝");

    final int value;
    final String desc;

    public static DeliveryBillStatusEnum getByValue(int value) {
        for (DeliveryBillStatusEnum statusEnum : DeliveryBillStatusEnum.values()) {
            if (value == statusEnum.getValue()) {
                return statusEnum;
            }
        }
        return DeliveryBillStatusEnum.NOT_BILLED;
    }

}