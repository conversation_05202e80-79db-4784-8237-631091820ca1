package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表作废DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyInvalidDTO {

    @ApiModelProperty(value = "申请单id", required = true)
    private Integer applyId;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @ApiModelProperty(value = "触发系统", hidden = true)
    private Integer triggerSys;

    @ApiModelProperty(value = "附件列表")
    private List<Integer> fileIdList;

}
