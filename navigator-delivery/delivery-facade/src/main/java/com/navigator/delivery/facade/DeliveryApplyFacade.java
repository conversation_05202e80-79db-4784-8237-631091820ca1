package com.navigator.delivery.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.dto.TempPermissionDTO;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "navigator-delivery-service")
@Api(tags = "提货委托")
public interface DeliveryApplyFacade {

    /**
     * 提货委托申请列表
     *
     * @param queryDTO 查询条件
     * @return
     */
    @ApiOperation("提货委托申请列表")
    @PostMapping("/deliveryApply/getDeliveryApplyList")
    Result getDeliveryApplyList(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO);

    /**
     * 查询申请提货详情
     *
     * @param applyId 申请单id
     * @return
     */
    @ApiOperation("查询申请提货详情")
    @PostMapping("/deliveryApply/getDeliveryApplyDetailById")
    Result<DeliveryApplyDetailVO> getDeliveryApplyDetailById(@RequestParam("applyId") Integer applyId, @RequestParam("triggerSys") String triggerSys);

    /**
     * 查询申请单附件
     *
     * @param applyId 申请单id
     * @return
     */
    @GetMapping("/deliveryApply/getFileListByApplyId")
    Result<List<DeliveryApplyFileVO>> getFileListByApplyId(@RequestParam("applyId") Integer applyId);

    /**
     * 查询申请单审核记录
     *
     * @param applyId 申请单id
     * @return
     */
    @GetMapping("/deliveryApply/getAuditRecordListByApplyId")
    Result<List<DeliveryApplyApprovalVO>> getAuditRecordListByApplyId(@RequestParam("applyId") Integer applyId);

    /**
     * 查询申请单操作记录
     *
     * @param applyCode 申请单号
     * @return
     */
    @GetMapping("/deliveryApply/getOperateRecordListByApplyCode")
    Result<List<DeliveryApplyOperationVO>> getOperateRecordListByApplyCode(@RequestParam("applyCode") String applyCode);

    // ==============================COLUMBUS============================================

    /**
     * 提货委托申请合同列表
     *
     * @param deliveryApplyContractQO 查询条件
     * @return
     */
    @PostMapping("/deliveryApply/getDeliveryApplyContractList")
    Result<List<DeliveryApplyContractVO>> getDeliveryApplyContractList(@RequestBody DeliveryApplyContractQO deliveryApplyContractQO);

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start

    /**
     * 查询司机输入记录
     *
     * @return
     */
    @GetMapping("/deliveryApply/getDriverInputRecord")
    Result<List<DeliveryApplyDriverLogEntity>> getDriverInputRecord(@RequestParam("customerId") Integer customerId, @RequestParam("goodsCategoryId") Integer goodsCategoryId);

    /**
     * 按照条件查询司机输入记录
     *
     * @return
     */
    @PostMapping("/deliveryApply/getDriverRecordByCondition")
    Result<List<DeliveryApplyDriverLogEntity>> getDriverRecordByCondition(@RequestBody DeliveryApplyDriverLogDTO applyDriverLogDTO);
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    /**
     * 保存/提交申请单
     *
     * @param deliveryApplyDTO 申请单信息
     * @return
     */
    @PostMapping("/deliveryApply/saveOrSubmitDeliveryApply")
    Result<String> saveOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO);

    /**
     * 批量提交申请单
     *
     * @param applyIds 申请单id集合
     * @return
     */
    @GetMapping("/deliveryApply/batchSubmitDeliveryApply")
    Result<String> batchSubmitDeliveryApply(@RequestParam("applyIds") List<Integer> applyIds);

    /**
     * 编辑提货申请单
     *
     * @param deliveryApplyDTO 申请单信息
     * @return
     */
    @PostMapping("/deliveryApply/updateOrSubmitDeliveryApply")
    // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
    Result<String> updateOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO);
    // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End

    /**
     * 提交提货申请单
     *
     * @param applyId 申请单id
     * @return
     */
    @GetMapping("/deliveryApply/submitDeliveryApplyById")
    Result<Boolean> submitDeliveryApplyById(@RequestParam("applyId") Integer applyId);

    /**
     * 下载提货申请单
     *
     * @param applyId 申请单id
     * @return
     */
    @GetMapping("/deliveryApply/cancelDeliveryApply")
    Result<Boolean> cancelDeliveryApply(@RequestParam("applyId") Integer applyId);

    // ==============================MAGELLAN============================================

    /**
     * 录入提货申请单状态
     *
     * @param applyIds   申请单id集合
     * @param billStatus 状态
     * @return
     */
    @GetMapping("/inputDeliveryApplyBillStatus")
    Result<Boolean> inputDeliveryApplyBillStatus(@RequestParam("applyIds") List<Integer> applyIds, @RequestParam("billStatus") Integer billStatus);

    /**
     * 审核提货申请
     *
     * @param deliveryApplyApprovalDTO 审核信息
     * @return
     */
    @PostMapping("/auditDeliveryApply")
    Result<Boolean> auditDeliveryApply(@RequestBody DeliveryApplyApprovalDTO deliveryApplyApprovalDTO);

    /**
     * 批量审核通过
     *
     * @param applyIds 申请单id集合
     * @return
     */
    @GetMapping("/batchAuditPassDeliveryApply")
    Result<Boolean> batchAuditPassDeliveryApply(@RequestParam("applyIds") List<Integer> applyIds);

    /**
     * 批量审核驳回
     *
     * @param applyIds 申请单id集合
     * @return
     */
    @GetMapping("/batchAuditRejectDeliveryApply")
    Result<Boolean> batchAuditRejectDeliveryApply(@RequestParam("applyIds") List<Integer> applyIds);

    /**
     * 作废提货申请单
     *
     * @param applyInvalidDTO 作废信息
     * @return
     */
    @PostMapping("/applyInvalidDeliveryApply")
    Result<Boolean> applyInvalidDeliveryApply(@RequestBody DeliveryApplyInvalidDTO applyInvalidDTO);

    /**
     * 重新分配合同列表
     *
     * @param assignContractDTO 查询条件
     * @return
     */
    @PostMapping("/getReAssignContractList")
    Result<DeliveryApplyAssignContractVO> getReAssignContractList(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO);

    /**
     * 重新分配合同
     *
     * @param assignContractDTO 重新分配合同信息
     * @return
     */
    @PostMapping("/reAssignContract")
    Result<Boolean> reAssignContract(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO);

    /**
     * 获取RPA提货申请单列表
     *
     * @return
     */
    @GetMapping("/getRpaDeliveryApplyList")
    Result getRpaDeliveryApplyList();

    /**
     * 移除分配合同
     *
     * @param applyId    申請單id
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/removeAssignContract")
    Result<Boolean> removeAssignContract(@RequestParam("applyId") Integer applyId, @RequestParam("contractId") Integer contractId);

    /**
     * 根据申请单id集合查询申请单列表
     *
     * @param applyIds 申请单id集合
     * @return
     */
    @GetMapping("/getDeliveryApplyListByIds")
    Result getDeliveryApplyListByIds(@RequestParam("applyIds") List<Integer> applyIds);

    /**
     * 判断合同是否处于提货待审核状态
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/isContractInDeliveryAudit")
    Result<Boolean> isContractInDeliveryAudit(@RequestParam("contractId") Integer contractId);

    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 Start

    /**
     * 清理一年未使用的司机记录
     *
     * @return
     */
    @GetMapping("/clearDriverInfoOneYear")
    Result<Boolean> clearDriverInfoOneYear();
    // 1002481 case-提货功能优化-清除司机信息 Author: Mr 2024-04-28 End

    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 Start

    /**
     * 获取客户临时权限
     */
    @GetMapping("/getCustomerTempPermission")
    Result<TempPermissionDTO> getCustomerTempPermission();
    // 优化：case-1002609 BR-更改提货委托客户维护列表的位置 Author: Mr 2024-05-27 End

}

