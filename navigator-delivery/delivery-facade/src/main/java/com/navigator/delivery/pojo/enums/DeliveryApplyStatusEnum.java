package com.navigator.delivery.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 提货申请状态
 * </p>
 *
 * @since 2023/8/18
 */
@Getter
@AllArgsConstructor
public enum DeliveryApplyStatusEnum {
    /**
     * 提货申请状态 0.所有 1.新录入 2.待审核 3.审核通过 4.审核驳回 5.作废待审核 6.已作废
     */
    ALL(0, "所有"),
    NEW(1, "新录入"),
    WAIT_APPROVE(2, "待审核"),
    APPROVED(3, "审核通过"),
    REJECTED(4, "审核驳回"),
    INVALID_WAIT_APPROVE(5, "作废待审核"),
    INVALID(6, "已作废"),
    BLOCKED(7, "锁定");

    final int value;
    final String desc;

    public static DeliveryApplyStatusEnum getByValue(int value) {
        for (DeliveryApplyStatusEnum statusEnum : DeliveryApplyStatusEnum.values()) {
            if (value == statusEnum.getValue()) {
                return statusEnum;
            }
        }
        return DeliveryApplyStatusEnum.NEW;
    }

}
