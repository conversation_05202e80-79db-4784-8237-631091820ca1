package com.navigator.delivery.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提货申请表 MagellanExportDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryApplyMagellanExportDTO extends DeliveryApplyVOEntity {

    @Excel(name = "买方主体", orderNum = "2", width = 30)
    @ApiModelProperty(value = "买方主体名称")
    private String customerName;
}
