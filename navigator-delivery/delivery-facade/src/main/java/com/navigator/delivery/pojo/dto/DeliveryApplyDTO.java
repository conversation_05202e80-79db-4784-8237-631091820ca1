package com.navigator.delivery.pojo.dto;

import com.navigator.delivery.pojo.entity.BaseDeliveryApplyEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeliveryApplyDTO extends BaseDeliveryApplyEntity {

    @ApiModelProperty(value = "提交类型 1.保存 2.提交")
    private Integer submitType;

    @ApiModelProperty(value = "申请单id")
    private Integer applyId;

    @ApiModelProperty(value = "分配合同列表")
    private List<DeliveryApplyContractDTO> deliveryApplyContractList;

    @ApiModelProperty(value = "附件列表")
    private List<Integer> fileIdList;

}
