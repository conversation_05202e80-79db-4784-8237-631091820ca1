package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 申请提货预校验
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PreCheckDeliveryApplyDTO {

    @ApiModelProperty(value = "是否白名单")
    private Boolean isWhite;

    @ApiModelProperty(value = "交割可提量")
    private BigDecimal dceCanDeliveryNum;

    @ApiModelProperty(value = "客户交易状态")
    private String customerTradeStatus;

    @ApiModelProperty(value = "是否可分配合同")
    private Integer isCanAssignContract;

}