package com.navigator.delivery.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbd_delivery_apply_driver_log
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbd_delivery_apply_driver_log")
@ApiModel(value = "DeliveryApplyDriverLogEntity对象", description = "dbd_delivery_apply_driver_log")
public class DeliveryApplyDriverLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @ApiModelProperty(value = "挂车号")
    private String trailerNumber;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机身份证号码")
    private String driverIdNumber;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiModelProperty(value = "车辆类型")
    private Integer carType;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start

    @ApiModelProperty(value = "随车电话")
    private String onboardPhone;

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

    @ApiModelProperty(value = "核定干舷")
    private Integer freeboard;

    @ApiModelProperty(value = "MMSI")
    private String mmsi;

}
