package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 预分配查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Accessors(chain = true)
public class ExecutePreAllocationDTO {

    @ApiModelProperty(value = "仓单号码")
    private String warrantNumber;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "是否是DCE")
    private Integer isExchangeDelivery;

    @ApiModelProperty(value = "是否是豆二")
    private Integer isSoybean2;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocationQty;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "提货类型:A/C")
    private String takeType;

    @ApiModelProperty(value = "提货公司")
    private Integer takeCustomer;

    @ApiModelProperty(value = "CPID")
    private Integer CPID;
}
