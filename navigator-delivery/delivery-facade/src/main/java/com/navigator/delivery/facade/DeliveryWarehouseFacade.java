package com.navigator.delivery.facade;

import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "navigator-delivery-service")
public interface DeliveryWarehouseFacade {

    /**
     * 新增提货库点配置
     *
     * @param deliveryWarehouseEntity 提货库点配置
     * @return
     */
    @PostMapping("/addDeliveryWarehouse")
    Result<Boolean> addDeliveryWarehouse(@RequestBody DeliveryWarehouseEntity deliveryWarehouseEntity);

    /**
     * 修改提货库点配置
     *
     * @param deliveryWarehouseEntity 提货库点配置
     * @return
     */
    @PostMapping("/updateDeliveryWarehouse")
    Result<Boolean> updateDeliveryWarehouse(@RequestBody DeliveryWarehouseEntity deliveryWarehouseEntity);

    /**
     * 禁用/启用提货库点配置
     *
     * @param warehouseId 仓库id
     * @param status      状态
     * @return
     */
    @GetMapping("/updateDeliveryWarehouseStatus")
    Result<Boolean> updateDeliveryWarehouseStatus(@RequestParam("warehouseId") Integer warehouseId, @RequestParam("status") Integer status);

    /**
     * 查询提货库点配置列表
     *
     * @param warehouseDTO 查询条件
     * @return
     */
    @PostMapping("/getDeliveryWarehouseList")
    Result<List<DeliveryWarehouseEntity>> getDeliveryWarehouseList(@RequestBody DeliveryWarehouseDTO warehouseDTO);

    /**
     * 根据id查询提货库点配置详情
     *
     * @param warehouseId 仓库id
     * @return
     */
    @GetMapping("/getDeliveryWarehouseById")
    Result<DeliveryWarehouseEntity> getDeliveryWarehouseById(@RequestParam("warehouseId") Integer warehouseId);
}
