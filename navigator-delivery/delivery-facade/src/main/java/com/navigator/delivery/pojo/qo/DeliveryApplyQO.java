package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DeliveryApplyQO {
    @ApiModelProperty(value = "业务编号（合同号/申请编号）")
    private String businessCode;

    @ApiModelProperty(value = "卖方主体id")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方主体")
    private String supplierName;

    @ApiModelProperty(value = "提货工厂")
    private List<String> deliveryFactoryCodeList;

    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @ApiModelProperty(value = "是否拼车/船")
    private Integer isCarpool;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "创建开始日期")
    private String createStartTime;

    @ApiModelProperty(value = "创建结束日期")
    private String createEndTime;

    @ApiModelProperty(value = "申请开始日期")
    private String applyStartTime;

    @ApiModelProperty(value = "申请结束日期")
    private String applyEndTime;

    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @ApiModelProperty(value = "计划提货日期")
    private String planDeliveryTime;

    @ApiModelProperty(value = "提货库点")
    private Integer deliveryWarehouseId;

    @ApiModelProperty(value = "提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "主体名称")
    private String customerName;

    @ApiModelProperty(value = "主体Id")
    private Integer customerId;

    @ApiModelProperty(value = "开单状态")
    private Integer billStatus;

    @ApiModelProperty(value = "申请单状态")
    private Integer applyStatus;

    @ApiModelProperty(value = "操作方系统")
    private Integer operatorSys;

    @ApiModelProperty(value = "触发系统", hidden = true)
    private Integer triggerSys;

    @ApiModelProperty(value = "第三方系统", hidden = true)
    private Integer thirdSys;

    @ApiModelProperty(value = "申请单id集合")
    private List<Integer> applyIds;

    @ApiModelProperty(value = "锁定原因")
    private String atlasSubStatus;
}