package com.navigator.delivery.pojo.vo;

import com.navigator.delivery.pojo.entity.DeliveryApplyContractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表重新分配合同 VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyAssignContractVO {
    @ApiModelProperty(value = "已分配合同列表")
    private List<DeliveryApplyContractEntity> assignedContractList;

    @ApiModelProperty(value = "可分配合同列表")
    private List<DeliveryApplyContractEntity> canAssignContractList;

}
