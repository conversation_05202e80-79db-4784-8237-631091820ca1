package com.navigator.delivery.pojo.dto;

import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提货申请表DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyApprovalDTO {

    @ApiModelProperty(value = "提货申请id")
    private Integer applyId;

    /**
     * {@link DeliveryApplyStatusEnum}
     */
    @ApiModelProperty(value = "审核状态 3.审核通过 4.审核驳回")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核备注")
    private String approvalRemark;
}
