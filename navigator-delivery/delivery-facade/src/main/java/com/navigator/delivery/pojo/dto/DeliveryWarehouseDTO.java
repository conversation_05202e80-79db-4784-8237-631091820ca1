package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提货库点配置DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryWarehouseDTO {
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "货品id")
    private Integer goodsId;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "LDC库/外库")
    private Integer warehouseType;

    @ApiModelProperty(value = "提货库点")
    private String warehouseName;

    @ApiModelProperty(value = "提货库点编码")
    private String warehouseCode;

    @ApiModelProperty(value = "linkinage商品编码")
    private String linkageGoodsCode;
}
