package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DeliveryApplyContractQO {

    @ApiModelProperty(value = "合同ID列表")
    List<Integer> contractIdList;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "卖方主体")
    private Integer supplierId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "交提货方式")
    private List<Integer> deliveryTypeList;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "开始提货日期")
    private String deliveryStartDate;

    @ApiModelProperty(value = "结束提货日期")
    private String deliveryEndDate;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiModelProperty(value = "发货库点ID")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "合同签订日期")
    private String contractSignDate;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "第三方系统", hidden = true)
    private Integer thirdSys;
}
