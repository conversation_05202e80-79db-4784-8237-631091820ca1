package com.navigator.delivery.facade;

import com.navigator.delivery.pojo.dto.ExchangePickQtyDTO;
import com.navigator.delivery.pojo.dto.ExecutePreAllocationDTO;
import com.navigator.delivery.pojo.qo.ExchangePickQtyQO;
import com.navigator.delivery.pojo.qo.ExecutePreAllocationQO;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 提货BI 查询服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Api(tags = "提货BI查询")
@FeignClient(name = "navigator-delivery-service")
public interface IDeliveryBIQueryFacade {

    /**
     * 查询仓单的可提货量（BI存储过程）
     *
     * @param pickQtyQO
     * @return
     */
    @PostMapping("/getExchangePickQty")
    List<ExchangePickQtyDTO> getExchangePickQty(@RequestBody ExchangePickQtyQO pickQtyQO);

    /**
     * 查询仓单的可提货量（BI存储过程）
     *
     * @param preAllocationQO
     * @return
     */
    @PostMapping("/getExecutePreAllocation")
    List<ExecutePreAllocationDTO> getExecutePreAllocation(@RequestBody ExecutePreAllocationQO preAllocationQO);

}
