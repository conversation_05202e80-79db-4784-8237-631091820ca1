package com.navigator.delivery.pojo.dto;

import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提货申请预分配
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyAllocateDTO extends DeliveryApplyAllocateEntity {

    @ApiModelProperty(value = "业务类型：1.现货 2.仓单 3.豆二")
    private Integer contractCategoryType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;
}
