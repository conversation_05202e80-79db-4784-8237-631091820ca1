package com.navigator.delivery.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请预分配
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbd_delivery_apply_allocate")
@ApiModel(value = "deliveryApplyAllocate对象", description = "提货申请预分配")
public class DeliveryApplyAllocateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "申请单id")
    private Integer applyId;

    @ApiModelProperty(value = "申请单Code")
    private String applyCode;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体ID")
    private Integer supplierId;

    @ApiModelProperty(value = "仓单号码")
    private String warrantNumber;

    @ApiModelProperty(value = "是否是DCE")
    private Integer isExchangeDelivery;

    @ApiModelProperty(value = "是否是豆二")
    private Integer isSoybean2;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocationQty;

    @ApiModelProperty(value = "DCE合同编号")
    private String dceContractNo;

    @ApiModelProperty(value = "提货类型")
    private String takeType;

    @ApiModelProperty(value = "提货主体id")
    private Integer takeCustomerId;

    @ApiModelProperty(value = "CPID")
    private Integer cpid;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "子提货申请单号")
    private String subApplyCode;

    @ApiModelProperty(value = "拼车优先级排序")
    @TableField(exist = false)
    private Integer prioritySort;

    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<DeliveryApplyAllocateEntity> lqw(DeliveryApplyAllocateQO condition) {
        LambdaQueryWrapper<DeliveryApplyAllocateEntity> lqw = new LambdaQueryWrapper<DeliveryApplyAllocateEntity>().eq(DeliveryApplyAllocateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getApplyId()), DeliveryApplyAllocateEntity::getApplyId, condition.getApplyId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getApplyCode()), DeliveryApplyAllocateEntity::getApplyCode, condition.getApplyCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getGoodsId()), DeliveryApplyAllocateEntity::getGoodsId, condition.getGoodsId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getWarrantNumber()), DeliveryApplyAllocateEntity::getWarrantNumber, condition.getWarrantNumber());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsExchangeDelivery()), DeliveryApplyAllocateEntity::getIsExchangeDelivery, condition.getIsExchangeDelivery());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsSoybean2()), DeliveryApplyAllocateEntity::getIsSoybean2, condition.getIsSoybean2());
        }
        lqw.orderByDesc(DeliveryApplyAllocateEntity::getId);
        return lqw;
    }
}
