package com.navigator.delivery.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 提货委托-运输方式
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeliveryTransportWay {
    /**
     * 提货委托-运输方式
     */
    TRUCK(1, "车提", "TRUCK"),
    SHIPPING(2, "船提", "BARGE"),
    TRAIN(3, "火车提", "TRAIN"),
    TRANSFER(4, "转货权", "TITTRANS"),
    ;

    final Integer value;
    final String desc;
    final String atlasCode;

    public static DeliveryTransportWay getByDesc(String desc) {
        return Arrays.stream(values())
                .filter(deliveryTransportType -> Objects.equals(desc, deliveryTransportType.getDesc()))
                .findFirst()
                .orElse(TRUCK);
    }

    public static DeliveryTransportWay getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(deliveryTransportType -> Objects.equals(value, deliveryTransportType.getValue()))
                .findFirst()
                .orElse(TRUCK);
    }
}
