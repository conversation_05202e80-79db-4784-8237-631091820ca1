package com.navigator.delivery.pojo.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * ATLAS车辆状态
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Getter
public enum DeliveryQueueStatusEnum {
    /**
     * 车辆状态枚举
     */
    NOT_IN_QUEUE("Not in queue", "未排队", 1),
    QUEUEING("Queueing", "排队中", 1),
    PERMIT_TO_PLANT("Permit to plant", "通知进厂", 1),
    IN_PLANT("In plant", "已进厂", 0),
    LEAVE_THE_PLANT("Leave the plant", "已离厂", 0);

    final String value;
    final String desc;
    // 0: 不可作废，1: 可作废
    final Integer invalidStatus;

    DeliveryQueueStatusEnum(String value, String desc, int invalidStatus) {
        this.value = value;
        this.desc = desc;
        this.invalidStatus = invalidStatus;
    }

    public static DeliveryQueueStatusEnum getByValue(String value) {
        for (DeliveryQueueStatusEnum statusEnum : DeliveryQueueStatusEnum.values()) {
            if (Objects.equals(value, statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return DeliveryQueueStatusEnum.NOT_IN_QUEUE;
    }

    public static String getDescByValue(String value) {
        for (DeliveryQueueStatusEnum statusEnum : DeliveryQueueStatusEnum.values()) {
            if (Objects.equals(value, statusEnum.getValue())) {
                return statusEnum.getDesc();
            }
        }
        return DeliveryQueueStatusEnum.NOT_IN_QUEUE.getDesc();
    }
}
