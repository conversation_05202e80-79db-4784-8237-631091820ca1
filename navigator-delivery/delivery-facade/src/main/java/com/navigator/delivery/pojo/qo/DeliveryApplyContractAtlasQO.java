package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 可提信息列表查询
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyContractAtlasQO {

    @ApiModelProperty(value = "买方主体ID")
    private Integer customerId;

    @ApiModelProperty(value = "卖方主体ID")
    private Integer supplierId;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "二级品类")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "触发系统", hidden = true)
    private Integer triggerSys;
}
