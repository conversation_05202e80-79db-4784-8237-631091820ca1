package com.navigator.delivery.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 预分配查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Accessors(chain = true)
public class ExecutePreAllocationQO {

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "卖方主体")
    private Integer companyId;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "触发系统1:magellan 2:columbus")
    private Integer system;

    @ApiModelProperty(value = "可提货量")
    private BigDecimal requestQty;

    @ApiModelProperty(value = "库点Id")
    private Integer warehouseId;
}
