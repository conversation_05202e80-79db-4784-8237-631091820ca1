package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 提货申请表 atlas ack执行DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryAckContractDTO {
    @ApiModelProperty(value = "Delivery Code")
    private String deliveryCode;

    @ApiModelProperty(value = "Creation Status")
    private String creationStatus;

    @ApiModelProperty(value = "Contract List")
    private List<ContractDTO> contractList;

    @Data
    public static class ContractDTO {
        @ApiModelProperty(value = "deliveryCode")
        private String deliveryCode;

        @ApiModelProperty(value = "contractCode")
        private String contractCode;

        @ApiModelProperty(value = "plannedQuantity")
        private String plannedQuantity;

        @ApiModelProperty(value = "weightUnit")
        private String weightUnit;
    }
}
