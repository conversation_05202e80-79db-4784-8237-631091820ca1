package com.navigator.delivery.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请合同
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbd_delivery_apply_contract")
@ApiModel(value = "DeliveryApplyContractEntity对象", description = "提货申请合同")
public class DeliveryApplyContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "申请单id")
    private Integer applyId;

    @ApiModelProperty(value = "申请单编号")
    private String applyCode;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "合同数量")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "分配金额")
    private BigDecimal allocateAmount;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "发货库点ID")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "合同签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractSignDate;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "合同类型名称")
    @TableField(exist = false)
    private String contractTypeName;

    @ApiModelProperty(value = "交提货方式名称")
    @TableField(exist = false)
    private String deliveryTypeName;

    @ApiModelProperty(value = "系统推荐分配数量")
    @TableField(exist = false)
    private BigDecimal recommendAllocateNum;

    @ApiModelProperty(value = "可分配数量")
    @TableField(exist = false)
    private BigDecimal canAllocateNum;

    @ApiModelProperty(value = "发货库点名称")
    @TableField(exist = false)
    private String shipWarehouseName;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType;

    @ApiModelProperty(value = "付款方式名称")
    @TableField(exist = false)
    private String paymentTypeName;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start

    @ApiModelProperty(value = "是否更新记录")
    private Integer atlasUpdateResult;

    @ApiModelProperty(value = "更新表的次数")
    private Integer atlasUpdateTimes;

    @ApiModelProperty(value = "已执行数量(已提货数量)")
    private BigDecimal executedNum;

}
