package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

// 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
@Data
@Accessors(chain = true)
public class DeliveryApplyDriverLogDTO {
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机身份证号码")
    private String driverIdNumber;

    @ApiModelProperty(value = "随车电话")
    private String onboardPhone;

    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @ApiModelProperty(value = "挂车号")
    private String trailerNumber;

    @ApiModelProperty(value = "车辆类型")
    private Integer carType;

    @ApiModelProperty(value = "品类id")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

}
// 1002481 case-提货功能优化 Author: Mr 2024-04-28 End
