package com.navigator.delivery.facade;

import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.dto.DeliveryApplyAllocateDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 提货申请预分配 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@FeignClient(value = "navigator-delivery-service")
public interface DeliveryApplyAllocateFacade {
    /**
     * 根据条件：获取提货申请预分配列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取提货申请预分配列表")
    @PostMapping("/deliveryApplyAllocate/queryDeliveryApplyAllocateList")
    List<DeliveryApplyAllocateDTO> queryDeliveryApplyAllocateList(@RequestBody DeliveryApplyAllocateQO condition);

    /**
     * 根据ID：获取提货申请预分配
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取提货申请预分配")
    @GetMapping("/deliveryApplyAllocate/getDeliveryApplyAllocateById")
    DeliveryApplyAllocateEntity getDeliveryApplyAllocateById(@RequestParam(value = "id") Integer id);

    /**
     * 根据提货申请ID：获取提货申请预分配
     *
     * @param deliveryApplyEntity 提货申请信息
     * @return
     */
    @PostMapping("/deliveryApplyAllocate/getDeliveryRequestAllocateInfo")
    Result getDeliveryRequestAllocateInfo(@RequestBody DeliveryApplyEntity deliveryApplyEntity);


    /**
     * 根据提货申请ID：获取提货申请预分配
     *
     * @param applyId 提货申请ID
     * @return
     */
    @GetMapping("/deliveryApplyAllocate/getDeliveryApplyAllocateByApplyId")
    Result<List<DeliveryApplyAllocateEntity>> getDeliveryApplyAllocateByApplyId(@RequestParam(value = "applyId") Integer applyId);
}
