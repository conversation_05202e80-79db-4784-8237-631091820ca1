package com.navigator.delivery.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请表 MagellanExportDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyMagellanAtlasExportDTO {

    @Excel(name = "申请编号", orderNum = "1", width = 25)
    @ApiModelProperty(value = "申请编号")
    private String code;

    @Excel(name = "买方主体", orderNum = "2", width = 30)
    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @Excel(name = "货品名称", orderNum = "3", width = 20)
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @Excel(name = "包装", orderNum = "4")
    @ApiModelProperty(value = "包装")
    private String goodsPackage;

    @Excel(name = "规格", orderNum = "5")
    @ApiModelProperty(value = "规格")
    private String goodsSpec;

    @Excel(name = "数量", orderNum = "6", numFormat = "0.000")
    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum = BigDecimal.ZERO;

    @Excel(name = "提货工厂", orderNum = "7")
    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @Excel(name = "卖方主体", orderNum = "8", width = 33)
    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;

    @Excel(name = "合同编号", orderNum = "9", width = 25)
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @Excel(name = "分配数量", orderNum = "10", numFormat = "0.000")
    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocateNum;

    @Excel(name = "合同单价", orderNum = "11", numFormat = "0.00")
    @ApiModelProperty(value = "合同单价")
    private BigDecimal unitPrice;

    @Excel(name = "分配金额", orderNum = "12", numFormat = "0.00")
    @ApiModelProperty(value = "分配金额")
    private BigDecimal allocateAmount;

    @Excel(name = "合同类型", orderNum = "13", replace = {"一口价合同_1", "基差合同_2", "暂定价合同_3", "基差暂定价合同_4", "_null"})
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @Excel(name = "交提货方式", orderNum = "14", width = 25)
    @ApiModelProperty(value = "交提货方式名称")
    private String deliveryTypeName;

    @Excel(name = "发货库点", orderNum = "15", width = 25)
    @ApiModelProperty(value = "发货库点")
    private String shipWarehouseName;

    @Excel(name = "合同签订日期", format = "yyyy-MM-dd", orderNum = "16")
    @ApiModelProperty(value = "合同签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractSignDate;

    @Excel(name = "开始交货日", format = "yyyy-MM-dd", orderNum = "17")
    @ApiModelProperty(value = "开始交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @Excel(name = "截止交货日", format = "yyyy-MM-dd", orderNum = "18")
    @ApiModelProperty(value = "截止交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @Excel(name = "运输方式", orderNum = "19", replace = {"车提_1", "船提_2", "火车提_3", "转货权_4"})
    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

    @Excel(name = "车/船号", orderNum = "20")
    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @Excel(name = "车辆类型", orderNum = "21")
    @ApiModelProperty(value = "车辆类型")
    private String carTypeName;

    @Excel(name = "挂车号", orderNum = "22")
    @ApiModelProperty(value = "挂车号")
    private String trailerNumber;

    @Excel(name = "核定干舷", orderNum = "23")
    @ApiModelProperty(value = "核定干舷")
    private Integer freeboard;

    @Excel(name = "MMSI", orderNum = "24")
    @ApiModelProperty(value = "MMSI")
    private String mmsi;

    @Excel(name = "车站", orderNum = "25")
    @ApiModelProperty(value = "车站")
    private String station;

    @Excel(name = "司机姓名", orderNum = "26")
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @Excel(name = "司机身份证号", orderNum = "27", width = 25)
    @ApiModelProperty(value = "司机身份证号码")
    private String driverIdNumber;

    @Excel(name = "随车电话", orderNum = "28", width = 20)
    @ApiModelProperty(value = "随车电话")
    private String onboardPhone;

    @Excel(name = "是否拼车/船", orderNum = "29", replace = {"是_1", "否_0"}, width = 20)
    @ApiModelProperty(value = "是否拼车/船")
    private Integer isCarpool;

    @Excel(name = "拼车/船方", orderNum = "30")
    @ApiModelProperty(value = "拼车/船方")
    private Integer carpoolCount;

    @Excel(name = "拼车/船客户", orderNum = "31")
    @ApiModelProperty(value = "拼车/船客户")
    private String carpoolCustomer;

    @Excel(name = "装货优先级", orderNum = "32")
    @ApiModelProperty(value = "装货优先级")
    private Integer loadingPriority;

    @Excel(name = "计划提货日期", orderNum = "33", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "计划提货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDeliveryTime;

    @Excel(name = "LDC库/外库", orderNum = "34", replace = {"LDC库_1", "外库_2"})
    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @Excel(name = "提货库点", orderNum = "35")
    @ApiModelProperty(value = "提货库点")
    private String deliveryWarehouseName;

    @Excel(name = "提货方式", orderNum = "36", replace = {"自提_1", "送货_2", "转货权_3"})
    @ApiModelProperty(value = "提货方式")
    private String deliveryType;

    @Excel(name = "备注", orderNum = "37")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Excel(name = "锁定原因", orderNum = "38")
    @ApiModelProperty(value = "锁定原因")
    private String atlasSubStatus;

    @Excel(name = "操作方", orderNum = "39", replace = {"LDC_1", "客户_2"})
    @ApiModelProperty(value = "操作方")
    private Integer triggerSys;

    @Excel(name = "申请人", orderNum = "40")
    @ApiModelProperty(value = "申请人")
    private String createdBy;

    @Excel(name = "申请时间", orderNum = "41", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @Excel(name = "状态", orderNum = "42", replace = {"新录入_1", "待审核_2", "审核通过_3", "审核驳回_4", "作废待审核_5", "已作废_6"})
    @ApiModelProperty(value = "申请状态（0.所有 1.新录入 2.待审核 3.审核通过 4.审核驳回 5.作废待审核 6.已作废）")
    private Integer applyStatus;

    @Excel(name = "提单状态", orderNum = "43", replace = {"未开单_1", "已开单_2", "已拒绝_3"})
    @ApiModelProperty(value = "开单状态（1.未开单 2.已开单）")
    private Integer billStatus;

    @Excel(name = "审核时间", orderNum = "44", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalAt;

    @Excel(name = "审核人", orderNum = "45")
    @ApiModelProperty(value = "审批人")
    private String approvalBy;

    // adding 3 new columns into excel generated by Jason Shi at 2025-6-3 start
    @Excel(name = "实际提货量", orderNum = "46", numFormat = "0.000")
    @ApiModelProperty(value = "实际提货量")
    private BigDecimal executedNum;

    @Excel(name = "开单未提量", orderNum = "47", numFormat = "0.000")
    @ApiModelProperty(value = "开单未提量")
    private BigDecimal unExecutedNum;

    @Excel(name = "车辆状态", orderNum = "48")
    @ApiModelProperty(value = "车辆状态")
    private String carStatusName;
    // adding 3 new columns into excel generated by Jason Shi at 2025-6-3 end

}
