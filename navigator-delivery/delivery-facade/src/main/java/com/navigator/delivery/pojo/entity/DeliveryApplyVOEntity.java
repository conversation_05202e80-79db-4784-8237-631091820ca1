package com.navigator.delivery.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请表 - 一条提货申请对应一条合同
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_delivery_apply")
@ApiModel(value = "DeliveryApplyVOEntity对象", description = "提货申请视图表")
public class DeliveryApplyVOEntity extends BaseDeliveryApplyEntity {
    @ApiModelProperty(value = "递增编号")
    private Integer rowId;

    @Excel(name = "货品名称", orderNum = "3", width = 20)
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @ApiModelProperty(value = "LKG货品code")
    private String linkageGoodsCode;

    @Excel(name = "包装", orderNum = "4")
    @ApiModelProperty(value = "包装")
    private String goodsPackage;

    @Excel(name = "规格", orderNum = "5")
    @ApiModelProperty(value = "规格")
    private String goodsSpec;

    @ApiModelProperty(value = "买方主体名称")
    private String customerName;

    @Excel(name = "卖方主体", orderNum = "8", width = 33)
    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;

    @Excel(name = "合同编号", orderNum = "9", width = 25)
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @Excel(name = "分配数量", orderNum = "10", numFormat = "0.000")
    @ApiModelProperty(value = "分配数量")
    private BigDecimal allocateNum;

    @Excel(name = "合同单价", orderNum = "11", numFormat = "0.00")
    @ApiModelProperty(value = "合同单价")
    private BigDecimal unitPrice;

    @Excel(name = "分配金额", orderNum = "12", numFormat = "0.00")
    @ApiModelProperty(value = "分配金额")
    private BigDecimal allocateAmount;

    @Excel(name = "合同类型", orderNum = "13", replace = {"一口价合同_1", "基差合同_2", "暂定价合同_3", "基差暂定价合同_4", "_null"})
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @Excel(name = "交提货方式", orderNum = "14", width = 25)
    @ApiModelProperty(value = "交提货方式名称")
    private String deliveryTypeName;

    @Excel(name = "提货库点", orderNum = "29")
    @ApiModelProperty(value = "提货库点")
    private String deliveryWarehouseName;

    @Excel(name = "发货库点", orderNum = "15", width = 25)
    @ApiModelProperty(value = "发货库点")
    private String shipWarehouseName;

    @Excel(name = "合同签订日期", format = "yyyy-MM-dd", orderNum = "16")
    @ApiModelProperty(value = "合同签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractSignDate;

    @Excel(name = "开始交货日", format = "yyyy-MM-dd", orderNum = "17")
    @ApiModelProperty(value = "开始交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @Excel(name = "截止交货日", format = "yyyy-MM-dd", orderNum = "18")
    @ApiModelProperty(value = "截止交货日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @Excel(name = "车辆类型", orderNum = "20")
    @ApiModelProperty(value = "车辆类型")
    private String carTypeName;
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiModelProperty(value = "车辆状态")
    @TableField(exist = false)
    private String carStatusName;

    @ApiModelProperty(value = "锁定原因")
    @TableField(exist = false)
    private String blockedRemark;

    @ApiModelProperty(value = "实际提货量")
    private BigDecimal executedNum;

    @ApiModelProperty(value = "开单未提量")
    @TableField(exist = false)
    private BigDecimal unExecutedNum;

}
