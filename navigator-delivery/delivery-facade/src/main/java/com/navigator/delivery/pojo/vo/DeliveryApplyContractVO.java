package com.navigator.delivery.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <p>
 * 提货申请合同VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
public class DeliveryApplyContractVO {
    @ApiModelProperty(value = "商品id")
    private String goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "卖方主体id")
    private String supplierId;

    @ApiModelProperty(value = "卖方主体")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体id")
    private Integer companyId;

    @ApiModelProperty(value = "交提货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "可提货量")
    private BigDecimal canDeliveryNum;

    @ApiModelProperty(value = "交割可提货量")
    private BigDecimal dceCanDeliveryNum;

}
