package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * DR拼车信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyCarpoolInfoDTO {

    @ApiModelProperty(value = "已拼车DR个数")
    private Integer carpoolDeliveryCount;

    @ApiModelProperty(value = "已分配的个数")
    private Integer assignedCount;

}
