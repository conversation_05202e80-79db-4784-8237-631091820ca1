package com.navigator.delivery.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 提货委托上传
 *
 * <AUTHOR>
 */
@Data
public class DeliveryApplyAtlasImportResultDTO {

    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    @ApiModelProperty(value = "成功记录数")
    private Integer successCount;

    @ApiModelProperty(value = "错误记录数")
    private Integer failCount;

    @ApiModelProperty(value = "导入数据")
    private List<DeliveryApplyAtlasImportDTO> list;

}
