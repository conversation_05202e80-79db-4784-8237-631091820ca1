package com.navigator.delivery.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * <p>
 * 提货申请审核记录VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyApprovalVO {
    @ApiModelProperty(value = "审核人")
    private String approvalUser;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    @ApiModelProperty(value = "审核结果")
    private String approvalResult;

    @ApiModelProperty(value = "审核意见")
    private String approvalRemark;

}
