package com.navigator.delivery.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 提货申请批量上传
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
@Accessors(chain = true)
public class DeliveryApplyAtlasImportDTO {

    @NotNull(message = "买方主体")
    @Excel(name = "买方主体", orderNum = "1", width = 30)
    @ApiModelProperty(value = "买方主体名称")
    private String customerName;
    private Integer customerId;

    @NotNull(message = "客户编码")
    @ApiModelProperty(value = "linkage客户编码")
    @Excel(name = "客户编码", orderNum = "2", width = 20)
    private String customerCode;

    @NotNull(message = "货品")
    @Excel(name = "货品", orderNum = "3", width = 20)
    @ApiModelProperty(value = "货品名称")
    private String goodsName;
    private Integer goodsId;
    private Integer goodsCategoryId;

    @NotNull(message = "提货工厂")
    @Excel(name = "提货工厂", orderNum = "4")
    @ApiModelProperty(value = "提货工厂")
    private String deliveryFactoryCode;

    @NotNull(message = "卖方主体")
    @Excel(name = "卖方主体", orderNum = "5", width = 33)
    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;
    private Integer supplierId;

    @NotNull(message = "提货方式")
    // BUGFIX：case-1003114 配送合同批量上传提示“可提货量不足”，但合同量是够的 Author: Mr 2025-04-09
    @Excel(name = "提货方式", orderNum = "6", replace = {"自提_1", "配送_2"})
    @ApiModelProperty(value = "提货方式")
    private String deliveryType;

    @NotNull(message = "申请日期")
    @Excel(name = "申请日期", format = "yyyy-MM-dd", orderNum = "7")
    @ApiModelProperty(value = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyTime;

    @NotNull(message = "数量")
    @Excel(name = "数量", orderNum = "8", numFormat = "0.000")
    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum = BigDecimal.ZERO;

    @NotNull(message = "LDC库/外库")
    @Excel(name = "LDC库/外库", orderNum = "9", replace = {"LDC库_1", "外库_2"})
    @ApiModelProperty(value = "LDC库/外库 1.LDC库 2.外库")
    private Integer warehouseType;

    @Excel(name = "提货库点", orderNum = "10")
    @ApiModelProperty(value = "提货库点")
    private String deliveryWarehouseName;
    private Integer deliveryWarehouseId;

    @NotNull(message = "计划提货日期")
    @Excel(name = "计划提货日期", orderNum = "11", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "计划提货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDeliveryTime;

    @NotNull(message = "运输方式")
    @Excel(name = "运输方式", orderNum = "12", replace = {"车提_1", "船提_2", "火车提_3", "转货权_4"})
    @ApiModelProperty(value = "运输方式")
    private Integer transportWay;

    @Excel(name = "车/船号", orderNum = "13")
    @ApiModelProperty(value = "车/船号")
    private String plateNumber;

    @Excel(name = "车辆类型", orderNum = "14")
    @ApiModelProperty(value = "车辆类型")
    private String carTypeName;
    private Integer carType;

    @Excel(name = "挂车号", orderNum = "15")
    @ApiModelProperty(value = "挂车号")
    private String trailerNumber;

    @Excel(name = "核定干舷", orderNum = "16")
    @ApiModelProperty(value = "核定干舷")
    private Integer freeboard;

    @Excel(name = "MMSI", orderNum = "17")
    @ApiModelProperty(value = "MMSI")
    private String mmsi;

    @Excel(name = "车站", orderNum = "18")
    @ApiModelProperty(value = "车站")
    private String station;

    @NotNull(message = "司机姓名")
    @Excel(name = "司机姓名", orderNum = "19")
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @NotNull(message = "司机身份证号")
    @Excel(name = "司机身份证号", orderNum = "20", width = 25)
    @ApiModelProperty(value = "司机身份证号码")
    private String driverIdNumber;

    @NotNull(message = "随车电话")
    @Excel(name = "随车电话", orderNum = "21", width = 20)
    @ApiModelProperty(value = "随车电话")
    private String onboardPhone;

    @NotNull(message = "是否拼车/船")
    @Excel(name = "是否拼车/船", orderNum = "22", replace = {"是_1", "否_0"}, width = 20)
    @ApiModelProperty(value = "是否拼车/船")
    private Integer isCarpool;

    @Excel(name = "共几方拼车船", orderNum = "23")
    @ApiModelProperty(value = "拼车/船方")
    private Integer carpoolCount;

    @Excel(name = "拼车船客户", orderNum = "24")
    @ApiModelProperty(value = "拼车船客户")
    private String carpoolCustomer;

    @Excel(name = "装货优先级", orderNum = "25")
    @ApiModelProperty(value = "装货优先级")
    private Integer loadingPriority;

    @Excel(name = "备注", orderNum = "26")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "错误原因")
    private String message;

    @ApiModelProperty(value = "详细错误原因")
    private String detailMessage;

    /**
     * 均为空
     *
     * @return
     */
    public Boolean isAllNull() {
        return StringUtil.isNullBlank(customerName) &&
                StringUtil.isNullBlank(customerId) &&
                StringUtil.isNullBlank(customerCode) &&
                StringUtil.isNullBlank(goodsName) &&
                StringUtil.isNullBlank(goodsId) &&
                StringUtil.isNullBlank(goodsCategoryId) &&
                StringUtil.isNullBlank(deliveryFactoryCode) &&
                StringUtil.isNullBlank(supplierName) &&
                StringUtil.isNullBlank(supplierId) &&
                StringUtil.isNullBlank(deliveryType) &&
                StringUtil.isNullBlank(applyTime) &&
                StringUtil.isNullBlank(warehouseType) &&
                StringUtil.isNullBlank(deliveryWarehouseName) &&
                StringUtil.isNullBlank(deliveryWarehouseId) &&
                StringUtil.isNullBlank(planDeliveryTime) &&
                StringUtil.isNullBlank(transportWay) &&
                StringUtil.isNullBlank(plateNumber) &&
                StringUtil.isNullBlank(carTypeName) &&
                StringUtil.isNullBlank(carType) &&
                StringUtil.isNullBlank(trailerNumber) &&
                StringUtil.isNullBlank(freeboard) &&
                StringUtil.isNullBlank(mmsi) &&
                StringUtil.isNullBlank(station) &&
                StringUtil.isNullBlank(driverName) &&
                StringUtil.isNullBlank(driverIdNumber) &&
                StringUtil.isNullBlank(onboardPhone) &&
                StringUtil.isNullBlank(isCarpool) &&
                StringUtil.isNullBlank(carpoolCount) &&
                StringUtil.isNullBlank(carpoolCustomer) &&
                StringUtil.isNullBlank(loadingPriority) &&
                StringUtil.isNullBlank(remark) &&
                StringUtil.isNullBlank(message) &&
                StringUtil.isNullBlank(detailMessage);
    }

    public String getCustomerName() {
        return StringUtil.trim(customerName);
    }

    public String getCustomerCode() {
        return StringUtil.trim(customerCode);
    }

    public String getGoodsName() {
        return StringUtil.trim(goodsName);
    }

    public String getDeliveryFactoryCode() {
        return StringUtil.trim(deliveryFactoryCode);
    }

    public String getSupplierName() {
        return StringUtil.trim(supplierName);
    }

    public String getDeliveryType() {
        return StringUtil.trim(deliveryType);
    }

    public String getDeliveryWarehouseName() {
        return StringUtil.trim(deliveryWarehouseName);
    }

    public String getPlateNumber() {
        return plateNumber == null ? null : plateNumber.replace(" ", "");
    }

    public String getCarTypeName() {
        return StringUtil.trim(carTypeName);
    }

    public String getTrailerNumber() {
        return StringUtil.trim(trailerNumber);
    }

    public String getMmsi() {
        return StringUtil.trim(mmsi);
    }

    public String getStation() {
        return StringUtil.trim(station);
    }

    public String getDriverName() {
        return driverName == null ? null : driverName.replace(" ", "");
    }

    public String getDriverIdNumber() {
        return StringUtil.trim(driverIdNumber);
    }

    public String getOnboardPhone() {
        return StringUtil.trim(onboardPhone);
    }

    public String getCarpoolCustomer() {
        return StringUtil.trim(carpoolCustomer);
    }


    public String getRemark() {
        return StringUtil.trim(remark);
    }


}
