package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RE_NODE")
@ApiModel(value = "ActReNodeEntity对象", description = "节点表")
public class ActReNodeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模型id")
    private String modelId;

    @ApiModelProperty(value = "模型名称")
    private String modelName;

    @ApiModelProperty(value = "部署id")
    private String deploymentId;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "实例类型")
    private String instanceType;

    @ApiModelProperty(value = "审批角色集合")
    private String roleList;

    @ApiModelProperty(value = "审批用户集合")
    private String userList;

    @ApiModelProperty(value = "节点状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;

    @ApiModelProperty(value = "流程定义id")
    private String procDefId;

    @ApiModelProperty(value = "流程名称")
    private String procDefName;

    @ApiModelProperty(value = "流程key")
    private String procDefKey;

    @ApiModelProperty(value = "审批角色")
    private String roleNameList;

    @ApiModelProperty(value = "审批人")
    private String usernameList;

    @ApiModelProperty(value = "节点排序")
    private Integer sort;

    @ApiModelProperty(value = "节点可以执行的动作")
    private String action;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


}
