package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用来保存部署文件的大文本数据
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_GE_BYTEARRAY")
@ApiModel(value = "ActGeBytearrayEntity对象", description = "用来保存部署文件的大文本数据")
public class ActGeBytearrayEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资源文件编号，自增长")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "Version(版本)")
    @TableField("REV_")
    private String rev;

    @ApiModelProperty(value = "部署的文件名称，如：mail.bpmn、mail.png 、mail.bpmn20.xml")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "来自于父表ACT_RE_DEPLOYMENT的主键")
    @TableField("DEPLOYMENT_ID_")
    private String deploymentId;

    @ApiModelProperty(value = "大文本类型，存储文本字节流")
    @TableField("BYTES_")
    private byte[] bytes;

    @ApiModelProperty(value = "是否由Activiti自动产生的资源，0表示false，1为true。")
    @TableField("GENERATED_")
    private Integer generated;


}
