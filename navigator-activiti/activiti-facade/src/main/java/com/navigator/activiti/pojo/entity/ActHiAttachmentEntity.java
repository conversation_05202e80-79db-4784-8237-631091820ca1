package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_ATTACHMENT")
@ApiModel(value = "ActHiAttachmentEntity对象", description = "附件表")
public class ActHiAttachmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "版本号")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "附件对应的用户ID")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "附件名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "附件描述")
    @TableField("DESCRIPTION_")
    private String description;

    @ApiModelProperty(value = "附件类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "该附件对应的任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "对应的流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "连接到该附件的URL")
    @TableField("URL_")
    private String url;

    @ApiModelProperty(value = "附件内容ID，附件的内容将会被保存到资源表中，该字段记录资源数据ID。")
    @TableField("CONTENT_ID_")
    private String contentId;

    @ApiModelProperty(value = "时间")
    @TableField("TIME_")
    private Date time;


}
