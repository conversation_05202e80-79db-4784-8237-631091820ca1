package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 属性数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_GE_PROPERTY")
@ApiModel(value = "ActGePropertyEntity对象", description = "属性数据表")
public class ActGePropertyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @TableId(value = "NAME_")
    private String name;

    @ApiModelProperty(value = "值")
    @TableField("VALUE_")
    private String value;

    @ApiModelProperty(value = "乐观锁版本")
    @TableField("REV_")
    private Integer rev;


}
