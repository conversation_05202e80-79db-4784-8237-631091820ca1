package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 历史流程实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_PROCINST")
@ApiModel(value = "ActHiProcinstEntity对象", description = "历史流程实例信息")
public class ActHiProcinstEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_", type = IdType.AUTO)
    private String id;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "业务KEY")
    @TableField("BUSINESS_KEY_")
    private String businessKey;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("START_TIME_")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME_")
    private Date endTime;

    @ApiModelProperty(value = "时长")
    @TableField("DURATION_")
    private Long duration;

    @ApiModelProperty(value = "开始人员ID")
    @TableField("START_USER_ID_")
    private String startUserId;

    @ApiModelProperty(value = "开始节点")
    @TableField("START_ACT_ID_")
    private String startActId;

    @ApiModelProperty(value = "结束节点")
    @TableField("END_ACT_ID_")
    private String endActId;

    @ApiModelProperty(value = "超级流程实例ID")
    @TableField("SUPER_PROCESS_INSTANCE_ID_")
    private String superProcessInstanceId;

    @ApiModelProperty(value = "删除理由")
    @TableField("DELETE_REASON_")
    private String deleteReason;

    //==========================START=============================
    //    业务信息
    @ApiModelProperty(value = "业务模块")
    @TableField("BIZ_MODULE_")
    private String bizModule;

    @ApiModelProperty(value = "大品类ID")
    @TableField("CATEGORY_ID_")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "大品类名称")
    @TableField("CATEGORY_NAME_")
    private String goodsCategoryName;

    @ApiModelProperty(value = "品类ID")
    @TableField("SUB_CATEGORY_ID_")
    private Integer subGoodsCategoryId;

    @ApiModelProperty(value = "品类名称")
    @TableField("SUB_CATEGORY_NAME_")
    private String subGoodsCategoryName;

    @ApiModelProperty(value = "采销类型")
    @TableField("SALES_TYPE_")
    private Integer salesType;

    @ApiModelProperty(value = "交易类型值")
    @TableField("TRADE_TYPE_VALUE_")
    private Integer tradeTypeValue;

    @ApiModelProperty(value = "交易类型")
    @TableField("TRADE_TYPE_NAME_")
    private String tradeTypeName;

    @ApiModelProperty(value = "业务id")
    @TableField("BIZ_ID_")
    private Integer bizId;

    //TT编号
    @ApiModelProperty(value = "业务编号")
    @TableField("BIZ_CODE_")
    private String bizCode;

    //合同编号
    @ApiModelProperty(value = "关联业务编号")
    @TableField("REFER_BIZ_CODE_")
    private String referBizCode;

    @ApiModelProperty(value = "业务编号")
    @TableField("REFER_BIZ_CODE2_")
    private String referBizCode2;

    @ApiModelProperty(value = "业务id")
    @TableField("BELONG_CUSTOMER_ID_")
    private Integer belongCustomerId;
    //==========================E N D=============================


    //***************************START****************************
    //    工作流信息
    @ApiModelProperty(value = "审批规则值")
    @TableField("APPROVE_RULE_VALUE_")
    private Integer approveRuleValue;

    @ApiModelProperty(value = "审批规则")
    @TableField("APPROVE_RULE_NAME_")
    private String approveRuleName;

    @ApiModelProperty(value = "流程名称")
    @TableField("PROCESS_NAME_")
    private String processName;

    @ApiModelProperty(value = "流程key")
    @TableField("PROCESS_KEY_")
    private String processKey;

    @ApiModelProperty(value = "流程实例名称")
    @TableField("PROCESS_INST_NAME_")
    private String processInstName;

    @TableField("NAME_")
    private String name;

    @TableField("TENANT_ID_")
    private String tenantId;
    //***************************E N D****************************


    @ApiModelProperty(value = "审批状态(包含节点状态)详细状态")
    @TableField("APPROVE_STATUS_")
    private String approveStatus;

    @ApiModelProperty(value = "1审批中2已完成3已作废")
    @TableField("STATUS_")
    private Integer status;

    @ApiModelProperty(value = "买方名称")
    @TableField("CUSTOMER_NAME_")
    private String customerName;

    @ApiModelProperty(value = "买方id")
    @TableField("CUSTOMER_ID_")
    private Integer customerId;

    @ApiModelProperty(value = "供应商名称")
    @TableField("SUPPLIER_NAME_")
    private String supplierName;

    @ApiModelProperty(value = "供应商id")
    @TableField("SUPPLIER_ID_")
    private Integer supplierId;

    @ApiModelProperty(value = "主体ID")
    @TableField("COMPANY_ID_")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    @TableField("COMPANY_NAME_")
    private String companyName;

    @ApiModelProperty(value = "工厂id")
    @TableField("FACTORY_ID_")
    private Integer factoryId;

    @ApiModelProperty(value = "工厂code")
    @TableField("FACTORY_CODE_")
    private String factoryCode;

    @ApiModelProperty(value = "一级品类")
    @TableField("CATEGORY_1_")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    @TableField("CATEGORY_2_")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    @TableField("CATEGORY_3_")
    private Integer category3;

    @ApiModelProperty(value = "账套编码")
    @TableField("SITE_CODE_")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    @TableField("SITE_NAME_")
    private String siteName;

    @ApiModelProperty(value = "期货代码")
    @TableField("FUTURE_CODE_")
    private String futureCode;

    @ApiModelProperty(value = "业务线")
    @TableField("BU_CODE_")
    private String buCode;

    // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
    @ApiModelProperty(value = "ttType")
    @TableField("TT_TYPE_")
    private Integer ttType;
    // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

}
