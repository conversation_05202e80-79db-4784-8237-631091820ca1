package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 历史审批意见表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_COMMENT")
@ApiModel(value = "ActHiCommentEntity对象", description = "历史审批意见表")
public class ActHiCommentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private Long id;

    @ApiModelProperty(value = "event（事件），comment（意见）")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "记录时间")
    @TableField("TIME_")
    private Date time;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "值为下列内容中的一种：AddUserLink、DeleteUserLink、AddGroupLink、DeleteGroupLink、AddComment、AddAttachment、DeleteAttachment")
    @TableField("ACTION_")
    private String action;

    @ApiModelProperty(value = "处理意见")
    @TableField("MESSAGE_")
    private String message;

    @ApiModelProperty(value = "全部消息")
    @TableField("FULL_MSG_")
    private byte[] fullMsg;


}
