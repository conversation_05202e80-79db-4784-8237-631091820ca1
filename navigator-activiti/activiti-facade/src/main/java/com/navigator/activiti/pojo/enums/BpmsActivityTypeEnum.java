package com.navigator.activiti.pojo.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Created  by  jinboYu  on  2019/3/13
 */
@Getter
public enum BpmsActivityTypeEnum {
    START_EVENT("startEvent", "开始事件", true),
    END_EVENT("endEvent", "结束事件", true),
    USER_TASK("userTask", "用户任务", false),
    MANUAL_TASK("manualTask", "人工任务", true),
    EXCLUSIVE_GATEWAY("exclusiveGateway", "排他网关", true),
    PARALLEL_GATEWAY("parallelGateway", "并行网关", true),
    INCLUSIVE_GATEWAY("inclusiveGateway", "包含网关", true);

    private String type;
    private String name;
    private boolean auto;

    BpmsActivityTypeEnum(String type, String name, boolean auto) {
        this.type = type;
        this.name = name;
        this.auto = auto;
    }

    public static BpmsActivityTypeEnum getByType(String type) {
        for (BpmsActivityTypeEnum en : BpmsActivityTypeEnum.values()
        ) {
            if (type.equals(en.getType())) {
                return en;
            }
        }
        return START_EVENT;
    }

    public static List<String> getAutoTypeList() {
        List<String> list = new ArrayList<>();
        for (BpmsActivityTypeEnum en : BpmsActivityTypeEnum.values()) {
            if (en.isAuto()) {
                list.add(en.getType());
            }
        }
        return list;
    }


}
