package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/28 15:41
 */
@Getter
public enum  InstanceTypeEnum {
    SINGLE_INSTANCE("None", "单实例任务"),
    MULTIPLE_INSTANCES("Parallel", "多实例任务"),
    ;

    private String type;
    private String name;

    InstanceTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static InstanceTypeEnum getByType(String type) {
        for (InstanceTypeEnum en : InstanceTypeEnum.values()) {
            if (type.equals(en.type) ) {
                return en;
            }
        }
        return InstanceTypeEnum.MULTIPLE_INSTANCES;
    }
}
