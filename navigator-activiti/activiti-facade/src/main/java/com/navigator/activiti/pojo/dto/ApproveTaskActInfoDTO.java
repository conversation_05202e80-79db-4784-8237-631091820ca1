package com.navigator.activiti.pojo.dto;

import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ApproveTaskActInfoDTO extends ActHiTaskinstEntity {

    private String actId;

    //BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
    private String ttTypeName;
    //BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

    String actType;

    String actName;

    boolean approved;

    String approveResult;

    String approveUserName;

    String approveMemo;

    ProcessInstDTO processInstDTO;

    ProcessNodeDTO processNodeDTO;

}
