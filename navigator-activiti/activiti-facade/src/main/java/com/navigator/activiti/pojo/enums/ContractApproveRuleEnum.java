package com.navigator.activiti.pojo.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;


@Getter
public enum ContractApproveRuleEnum {
    NONE("PROC_DEF_SC_ADD", 1011, "无需审批", "NODE_START,NODE_END_PASS"),
    A("PROC_DEF_SC_ADD", 1012, "A签审批", "NODE_START,NODE_A,NODE_END_PASS"),
    AB("PROC_DEF_SC_ADD", 1013, "B签审批", "NODE_START,NODE_A,NODE_B,NODE_END_PASS"),
    ABC("PROC_DEF_SC_ADD", 1014, "C签(CFO/CEO任一)审批", "NODE_START,NODE_A,NODE_B,NODE_C,NODE_END_PASS"),
    ABC12("PROC_DEF_SC_ADD", 1015, "<PERSON>签(<PERSON><PERSON>和CEO)审批", "NODE_START,NODE_A,NODE_B,NODE_C1,NODE_C2,NODE_END_PASS"),
    ABC1("PROC_DEF_SC_ADD", 1016, "C签(CFO)审批", "NODE_START,NODE_A,NODE_B,NODE_C1,NODE_END_PASS");

    /**
     * {@link ApproveProcessEnum}
     */
    private String processKey;
    private int value;
    private String description;
    private String nodes;

    ContractApproveRuleEnum(String processKey, int value, String description, String nodes) {
        this.processKey = processKey;
        this.value = value;
        this.description = description;
        this.nodes = nodes;
    }


    public static List<ContractApproveRuleEnum> getListByProcessKey(String processKey) {

        List<ContractApproveRuleEnum> list = new ArrayList<>();

        for (ContractApproveRuleEnum en : ContractApproveRuleEnum.values()) {
            if (processKey.equals(en.getProcessKey())) {
                list.add(en);
            }
        }
        return list;
    }

    public static ContractApproveRuleEnum getByValue(int value) {
        for (ContractApproveRuleEnum contractApproveRuleEnum : ContractApproveRuleEnum.values()) {
            if (value == contractApproveRuleEnum.getValue()) {
                return contractApproveRuleEnum;
            }
        }
        return ContractApproveRuleEnum.NONE;
    }


}
