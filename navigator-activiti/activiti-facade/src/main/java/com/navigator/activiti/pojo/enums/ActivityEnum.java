package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/18 13:52
 */
@Getter
public enum ActivityEnum {
    DEFAULT_APPROVE(0, "default_approve", "默认审批"),
    LEADER_COUNTERSIGN(1, "leader_countersign", "领导会签"),
    ;


    int value;
    String actId;
    String desc;

    ActivityEnum(int value, String actId, String desc) {
        this.value = value;
        this.actId = actId;
        this.desc = desc;
    }

    public static ActivityEnum getByValue(int value) {
        for (ActivityEnum en : ActivityEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ActivityEnum.LEADER_COUNTERSIGN;
    }

    public static ActivityEnum getByActId(String actId) {
        for (ActivityEnum en : ActivityEnum.values()) {
            if (actId.equals(en.getActId())) {
                return en;
            }
        }
        return ActivityEnum.LEADER_COUNTERSIGN;
    }
}
