package com.navigator.activiti.facade;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021/11/9 11:30
 */
@FeignClient(name = "navigator-activiti-service")
public interface FlowDiagramFacade {
    @GetMapping(value = "/flowDiagram/getImage/{taskId}")
    void getImage(String taskId,
                  HttpServletResponse response)throws Exception;

    @GetMapping(value = "/getDiagram/{taskId}")
    void getDiagram( String taskId,
                    HttpServletResponse response)throws Exception;
}
