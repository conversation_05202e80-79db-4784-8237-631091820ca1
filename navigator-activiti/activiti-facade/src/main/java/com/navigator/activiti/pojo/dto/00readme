关于DTO的组织说明
1、我的待审批列表，使用ApproveTaskInfoDTO列表
   其中的ProcessInstDTO，不加载列表数据（业务数据、流程实例的节点、流程审批历史）

2、审批详情页
   使用使用ApproveTaskInfoDTO信息
   其中的ProcessInstDTO，加载列表数据（业务数据、流程审批历史）,不加载节点列表

3、审批历史
   使用使用ApproveTaskInfoDTO信息
   其中的ProcessInstDTO，不加载列表数据（业务数据、流程实例的节点、流程审批历史）

4、流程列表
    使用其中的ProcessInstDTO数据，不加载列表数据

5、流程详情
    使用其中的ProcessInstDTO数据，加载列表数据（业务数据，节点列表）


关于枚举的说明
1、所有流程都定义在ApproveProcessEnum
2、所有可选的审批动作在ApproveActionEnum
3、审批状态分为【流程的审批状态】和【流程实例的状态】
   流程的审批状态只有审批中和审批完成，审批中的具体状态即【流程实例的状态】
   审批完成有多种肯能，都在ApproveResultEnum，该枚举实际上是【流程的审批状态】和
   【流程实例的状态】在BizApproveStatusEnum，该枚举会记录所有审批业务的具体子状态（按照processKey聚合）
4、如果要看某个审批任务的状态，其实是该任务对应的节点的状态，在BizApproveRuleEnum
5、如果要看某个流程实例的状态，其实是复合状态
   即如果该流程实例未审批完成，取BizApproveStatusEnum
     如果该流程实例已审批完成，取ApproveResultEnum
6、如何判断某个流程实例应该有哪些审批节点？在BizApproveRuleEnum

