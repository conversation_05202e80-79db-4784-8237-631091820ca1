package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 工作数据表(暂停工作表)
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_SUSPENDED_JOB")
@ApiModel(value = "ActRuSuspendedJobEntity对象", description = "工作数据表(暂停工作表)")
public class ActRuSuspendedJobEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "是否独有,排外")
    @TableField("EXCLUSIVE_")
    private Integer exclusive;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROCESS_INSTANCE_ID_")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "异常信息ID")
    @TableField("EXCEPTION_STACK_ID_")
    private String exceptionStackId;

    @ApiModelProperty(value = "异常信息")
    @TableField("EXCEPTION_MSG_")
    private String exceptionMsg;

    @ApiModelProperty(value = "到期时间")
    @TableField("DUEDATE_")
    private Date duedate;

    @ApiModelProperty(value = "重复")
    @TableField("REPEAT_")
    private String repeat;

    @ApiModelProperty(value = "处理类型")
    @TableField("HANDLER_TYPE_")
    private String handlerType;

    @ApiModelProperty(value = "标识")
    @TableField("HANDLER_CFG_")
    private String handlerCfg;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("RETRIES_")
    private Integer retries;


}
