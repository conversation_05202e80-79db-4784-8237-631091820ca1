package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/21 16:58
 */
@Getter
public enum TaskStatusEnum {
    ALL(9, "全部"),
    APPROVING(0, "审批中"),
    FINISHED(1, "已完成"),
    CANCEL(2,"作废"),
    ABANDON(3,"废弃"),
    ;

    int value;
    String desc;

    TaskStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static TaskStatusEnum getByValue(int value) {
        for (TaskStatusEnum en : TaskStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return TaskStatusEnum.ALL;
    }
}
