package com.navigator.activiti.facade;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.activiti.engine.repository.Model;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/9 11:08
 */
@FeignClient(name = "navigator-activiti-service")
public interface ModelFacade {

    @GetMapping("/model/create")
    void createModel(HttpServletRequest request, HttpServletResponse response) throws Exception;

    @GetMapping(value = "/model/{modelId}/json", produces = "application/json")
    ObjectNode getEditorJson(String modelId) throws Exception;


    @GetMapping(value = "/model/selectModel/{pageNum}/{pageSize}")
    List<Model> selectModel(int pageNum, int pageSize) throws Exception;


    @PutMapping(value = "/model/{modelId}/save")
    @ResponseStatus(value = HttpStatus.OK)
    int saveModel(String modelId, String name, String description, String json_xml, String svg_xml) throws Exception;


    @GetMapping(value = "/model/deployModel/{modelId}")
    int deployModel(String modelId) throws Exception;

    @GetMapping(value = "/model/deployModelByProcessKey/{processKey}")
    int deployModelByProcessKey(String processKey) throws Exception;


    @GetMapping(value = "/model/deployModelAll")
    int deployModelAll() throws Exception;

    @GetMapping("/model/exportBpmn")
    void exportBpmn(String procInstId, HttpServletResponse response);

    @GetMapping("/model/exportPng")
    void exportPng(@RequestParam("procInstId")String procInstId, HttpServletResponse response) throws IOException;

    @GetMapping("/model/importModel")
    Boolean importModel(@RequestParam("file") MultipartFile file);
}
