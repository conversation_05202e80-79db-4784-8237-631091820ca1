package com.navigator.activiti.pojo.dto;

import com.navigator.bisiness.enums.ModuleTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 */
@Data
@Accessors(chain = true)
public class ApproveResultDTO implements Serializable {
    /**
     * 流程实例Id
     */
    private String procInstId;

    private String approveRuleName;

    private String taskId;
    private String taskNodeName;
    private String taskActionName;
    private String taskApproveMemo;
    private String taskApproveUserName;

    private String nextNodeName;
    private String nextNodeCandidateRoles;

    private String taskCandidateUsers;

    /**
     * 业务模块
     * {@link ModuleTypeEnum}
     */
    private String bizModule;
    /**
     * 关联业务记录Id
     */
    private Integer bizId;
    /**
     * 关联业务Code
     */
    private String bizCode;
    /**
     * 审批结果
     * {@link com.navigator.activiti.pojo.enums.ApproveResultEnum}
     */
    private Integer approveResult;

    /**
     * 流程实例的状态
     * {@link com.navigator.activiti.pojo.enums.BizApproveStatusEnum}
     */
    private String procInstStatus;

    private String tradeTypeName;
}
