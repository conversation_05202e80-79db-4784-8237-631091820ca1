package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 事件日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_EVT_LOG")
@ApiModel(value = "ActEvtLogEntity对象", description = "事件日志表")
public class ActEvtLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "LOG_NR_")
    private Long logNr;

    @ApiModelProperty(value = "类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "时间")
    @TableField("TIME_STAMP_")
    private Date timeStamp;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "数据信息")
    @TableField("DATA_")
    private byte[] data;

    @TableField("LOCK_TIME_")
    private Date lockTime;

    @TableField("IS_PROCESSED_")
    private Integer isProcessed;

    @TableField("LOCK_OWNER_")
    private String lockOwner;

}
