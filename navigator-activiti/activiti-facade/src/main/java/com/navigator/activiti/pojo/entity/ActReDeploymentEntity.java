package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 部署信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RE_DEPLOYMENT")
@ApiModel(value = "ActReDeploymentEntity对象", description = "部署信息表")
public class ActReDeploymentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "部署包的名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "类型")
    @TableField("CATEGORY_")
    private String category;

    @ApiModelProperty(value = "部署key")
    @TableField("KEY_")
    private String key;

    @ApiModelProperty(value = "租户（多租户通常是在软件需要为多个不同组织服务时产生的概念）")
    @TableField("TENANT_ID_")
    private String tenantId;

    @ApiModelProperty(value = "部署时间")
    @TableField("DEPLOY_TIME_")
    private Date deployTime;

    @ApiModelProperty(value = "部署版本")
    @TableField("ENGINE_VERSION_")
    private String engineVersion;


}
