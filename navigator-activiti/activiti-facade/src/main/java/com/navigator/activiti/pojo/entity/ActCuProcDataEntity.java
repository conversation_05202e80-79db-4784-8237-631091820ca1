package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 自定义表：流程实例业务数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2022年3月17日
 */
@Data
@Accessors(chain = true)
@TableName("ACT_CU_PROC_DATA")
@ApiModel(value = "ActCuProcDataEntity对象", description = "流程实例业务数据")
public class ActCuProcDataEntity {
    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "流程实例ID")
    @TableId(value = "proc_inst_id")
    private String procInstId;

    @ApiModelProperty(value = "字段名称")
    @TableId(value = "name")
    private String name;

    @ApiModelProperty(value = "字段显示名称")
    @TableId(value = "display_name")
    private String displayName;

    @ApiModelProperty(value = "字段值")
    @TableId(value = "value")
    private String value;

    @ApiModelProperty(value = "显示顺序")
    @TableId(value = "index_no")
    private Integer indexNo;

    @ApiModelProperty(value = "")
    @TableId(value = "memo")
    private String memo;

    @ApiModelProperty(value = "创建时间")
    @TableId(value = "created_at")
    private Date createdAt;

    @ApiModelProperty(value = "创建时间")
    @TableId(value = "updated_at")
    private Date updatedAt;

    @ApiModelProperty(value = "分组编号")
    @TableId(value = "group_code")
    private String groupCode;


}
