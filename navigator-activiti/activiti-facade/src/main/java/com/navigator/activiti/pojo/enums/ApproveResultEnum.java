package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * 审批结果枚举，与流程定义中保持一致
 * 最终审批结果都应在此枚举中
 * 值与ApproveAction保持一致
 * ===========================================
 * 强调：这个是流程实例的最终审批结果
 * ===========================================
 */
@Getter
public enum ApproveResultEnum {
    APPROVING(0, false, "APPROVING", "审批中"),
    AGREE(1, true, "AGREE", "审批通过"),
    REJECT(3, true, "REJECT", "审批驳回"),
    CANCEL(15,true,"CANCEL","审批撤回"),
    REFUSE(9, true, "REFUSE", "审批不通过"),
    ERROR(-1, true, "ERROR", "审批异常");

    int value;
    boolean approved;
    String code;
    String desc;

    ApproveResultEnum(int value, boolean approved, String code, String desc) {
        this.value = value;
        this.approved = approved;
        this.code = code;
        this.desc = desc;
    }

    public static ApproveResultEnum getByValue(Integer value) {
        if (null == value) return ERROR;
        for (ApproveResultEnum en : ApproveResultEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ApproveResultEnum.ERROR;
    }

    public static ApproveResultEnum getByCode(String code) {
        for (ApproveResultEnum en : ApproveResultEnum.values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return ApproveResultEnum.ERROR;
    }

}
