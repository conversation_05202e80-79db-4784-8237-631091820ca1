package com.navigator.activiti.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 流程定义的节点信息，每一个部署的流程都有
 * 即如果某个流程被部署了多次，则会有多份节点信息
 * 主表：ACT_RE_NODE
 */
@Data
@Accessors(chain = true)
public class ProcessNodeDTO implements Serializable {
    /**
     * Node表中的记录的id
     */
    private Integer nodeUniqueId;
    /**
     * Node表中的NodeId
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 节点描述
     */
    private String description;
    /**
     * 节点状态
     */
    private String nodeStatus;
    /**
     * 节点类型
     */
    private String nodeType;
    /**
     * 审批人（定义的）
     */
    private String approver;
    /**
     * 流程定义ID
     */
    private String processDefId;
    /**
     * 流程实例是否涉及
     */
    private boolean included;

}
