package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 运行时流程执行实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_EXECUTION")
@ApiModel(value = "ActRuExecutionEntity对象", description = "运行时流程执行实例表")
public class ActRuExecutionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "乐观锁版本")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "业务ID")
    @TableField("BUSINESS_KEY_")
    private String businessKey;

    @ApiModelProperty(value = "父节点实例ID")
    @TableField("PARENT_ID_")
    private String parentId;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "节点实例ID即ACT_HI_ACTINST中ID")
    @TableField("SUPER_EXEC_")
    private String superExec;

    @ApiModelProperty(value = "根流程实例ID")
    @TableField("ROOT_PROC_INST_ID_")
    private String rootProcInstId;

    @ApiModelProperty(value = "节点实例ID")
    @TableField("ACT_ID_")
    private String actId;

    @ApiModelProperty(value = "是否存活")
    @TableField("IS_ACTIVE_")
    private Integer isActive;

    @ApiModelProperty(value = "执行流是否正在并行")
    @TableField("IS_CONCURRENT_")
    private Integer isConcurrent;

    @ApiModelProperty(value = "挂起状态 1激活 2挂起")
    @TableField("SUSPENSION_STATE_")
    private Integer suspensionState;

    @ApiModelProperty(value = "缓存结束状态")
    @TableField("CACHED_ENT_STATE_")
    private Integer cachedEntState;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "开始时间")
    @TableField("START_TIME_")
    private Date startTime;

    @ApiModelProperty(value = "开始用户ID")
    @TableField("START_USER_ID_")
    private String startUserId;

    @TableField("TASK_COUNT_")
    private Integer taskCount;

    @TableField("ID_LINK_COUNT_")
    private Integer idLinkCount;

    @TableField("SUSP_JOB_COUNT_")
    private Integer suspJobCount;

    @TableField("IS_EVENT_SCOPE_")
    private Integer isEventScope;

    @TableField("IS_COUNT_ENABLED_")
    private Integer isCountEnabled;

    @TableField("JOB_COUNT_")
    private Integer jobCount;

    @TableField("DEADLETTER_JOB_COUNT_")
    private Integer deadletterJobCount;

    @TableField("IS_MI_ROOT_")
    private Integer isMiRoot;

    @TableField("TIMER_JOB_COUNT_")
    private Integer timerJobCount;

    @TableField("EVT_SUBSCR_COUNT_")
    private Integer evtSubscrCount;

    @TableField("IS_SCOPE_")
    private Integer isScope;

    @TableField("VAR_COUNT_")
    private Integer varCount;

    @TableField("LOCK_TIME_")
    private Date lockTime;


}
