package com.navigator.activiti.pojo.enums;

import lombok.Getter;

@Getter
public enum ApproveActionOptionEnum {
    NEW(1, 1, 0, 0),
    REVISE_NORMAL(1, 1, 0, 0),
    REVISE_CHANGE_CUSTOMER(1, 1, 0, 0),
    SPLIT_NORMAL(1, 1, 0, 0),
    SPLIT_CHANGE_CUSTOMER(1, 1, 0, 0),
    PRICE(1, 0, 0, 0),
    PRICE_FIXED(1, 0, 0, 0),
    PRICE_RESULT(1, 0, 0, 0),
    TRANSFER_PART(1, 0, 0, 0),
    TRANSFER_ALL(1, 0, 0, 0),
    TRANSFER_RESULT(1, 0, 0, 0),
    REVERSE_PRICE_PART(1, 0, 0, 0),
    REVERSE_PRICE_ALL(1, 0, 0, 0),
    STRUCTURE_PRICE(1, 1, 0, 0),
    BUYBACK(1, 1, 0, 0),
    WASHOUT(1, 1, 0, 0),
    PUT_BACK(1, 1, 0, 0),
    CLOSED(1, 1, 0, 0),
    INVALID(1, 1, 0, 0),
    EQUITY_CHANGE(1, 1, 0, 0),
    ;

    private int canAgree;
    private int canDisagree;
    private int canTransfer;
    private int canRefuse;

    ApproveActionOptionEnum(int canAgree, int canDisagree, int canTransfer, int canRefuse) {
        this.canAgree = canAgree;
        this.canDisagree = canDisagree;
        this.canTransfer = canTransfer;
        this.canRefuse = canRefuse;
    }

    public static int[] getAbility(String name) {
        if (null == name) return new int[]{0, 0, 0, 0};
        for (ApproveActionOptionEnum en : ApproveActionOptionEnum.values()) {
            if (name.equals(en.name())) {
                return new int[]{en.getCanAgree(), en.getCanDisagree(), en.getCanTransfer(), en.getCanRefuse()};
            }
        }
        return new int[]{0, 0, 0, 0};
    }
}
