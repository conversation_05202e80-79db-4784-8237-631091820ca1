package com.navigator.activiti.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 权益变更流程的业务信息
 */
@Data
@Accessors(chain = true)
public class EquityChangeInfoDTO implements Serializable {

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "变更前可转月次数")
    private Integer beforeAbleTransferTimes;

    @ApiModelProperty(value = "变更前已转月次数")
    private Integer beforeTransferredTimes;

    @ApiModelProperty(value = "变更前可反点价次数")
    private Integer beforeAbleReversePriceTimes;

    @ApiModelProperty(value = "变更前已反点价次数")
    private Integer beforeReversedPriceTimes;

    @ApiModelProperty(value = "变更后可转月次数")
    private Integer afterAbleTransferTimes;

    @ApiModelProperty(value = "变更后已转月次数")
    private Integer afterTransferredTimes;

    @ApiModelProperty(value = "变更后可反点价次数")
    private Integer afterAbleReversePriceTimes;

    @ApiModelProperty(value = "变更后已反点价次数")
    private Integer afterReversedPriceTimes;

    @ApiModelProperty(value = "可转月次数")
    private String changeAbleTransferTimes;

    @ApiModelProperty(value = "可反点价次数")
    private String changeAbleReversePriceTimes;

    @ApiModelProperty(value = "已转月次数")
    private String changeTransferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private String changeReversedPriceTimes;

}
