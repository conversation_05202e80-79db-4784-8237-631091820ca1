package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 历史流程人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_IDENTITYLINK")
@ApiModel(value = "ActHiIdentitylinkEntity对象", description = "历史流程人员表")
public class ActHiIdentitylinkEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private Long id;

    @ApiModelProperty(value = "用户组ID")
    @TableField("GROUP_ID_")
    private String groupId;

    @ApiModelProperty(value = "类型，主要分为以下几种：assignee、candidate、owner、starter 、participant")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;


}
