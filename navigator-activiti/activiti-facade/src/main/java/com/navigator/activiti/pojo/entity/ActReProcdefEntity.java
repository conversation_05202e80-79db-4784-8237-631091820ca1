package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 流程定义：解析表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RE_PROCDEF")
@ApiModel(value = "ActReProcdefEntity对象", description = "流程定义：解析表")
public class ActReProcdefEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "版本号，乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "类型")
    @TableField("CATEGORY_")
    private String category;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "流程编号（该编号就是流程文件process元素的id属性值）")
    @TableField("KEY_")
    private String key;

    @ApiModelProperty(value = "版本")
    @TableField("VERSION_")
    private Integer version;

    @ApiModelProperty(value = "部署ID")
    @TableField("DEPLOYMENT_ID_")
    private String deploymentId;

    @ApiModelProperty(value = "流程bpmn文件名称")
    @TableField("RESOURCE_NAME_")
    private String resourceName;

    @ApiModelProperty(value = "图片资源文件名称")
    @TableField("DGRM_RESOURCE_NAME_")
    private String dgrmResourceName;

    @ApiModelProperty(value = "描述信息")
    @TableField("DESCRIPTION_")
    private String description;

    @ApiModelProperty(value = "start节点是否存在formKey 0否 1是")
    @TableField("HAS_START_FORM_KEY_")
    private Integer hasStartFormKey;

    @ApiModelProperty(value = "是否挂起，激活状态时该字段值为1，中止时字段值为2")
    @TableField("SUSPENSION_STATE_")
    private Integer suspensionState;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("ENGINE_VERSION_")
    private String engineVersion;

    @TableField("HAS_GRAPHICAL_NOTATION_")
    private Integer hasGraphicalNotation;


}
