package com.navigator.activiti.pojo.enums;


import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum BizApproveStatusEnum {
    UNKNOWN("error", "9999", "未知状态"),

    A_Approving("sales_contract", "1001", "A签审批中"),
    B_Approving("sales_contract", "1002", "B签审批中"),
    C_Approving("sales_contract", "1003", "C签(CFO/CEO任一签)审批中"),
    CFO_Approving("sales_contract", "1004", "CFO审批中"),
    CEO_Approving("sales_contract", "1005", "CEO审批中"),


    ;

    String bizModule;
    String code;
    String desc;

    BizApproveStatusEnum(String bizModule, String code, String desc) {
        this.bizModule = bizModule;
        this.code = code;
        this.desc = desc;
    }

    public static List<BizApproveStatusEnum> getListByBizModule(String bizModule) {
        List<BizApproveStatusEnum> list = new ArrayList<>();
        for (BizApproveStatusEnum en : BizApproveStatusEnum.values()) {
            if (bizModule.equals(en.getBizModule())) {
                list.add(en);
            }
        }
        return list;
    }

    public static BizApproveStatusEnum getByDesc(String desc) {
        if (null == desc) return UNKNOWN;
        for (BizApproveStatusEnum en : BizApproveStatusEnum.values()) {
            if (desc.equals(en.getDesc())) {
                return en;
            }
        }
        return BizApproveStatusEnum.UNKNOWN;
    }

    public static BizApproveStatusEnum getByCode(String code) {
        if (null == code) return UNKNOWN;
        for (BizApproveStatusEnum en : BizApproveStatusEnum.values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return BizApproveStatusEnum.UNKNOWN;
    }

    public static String getDescByCode(String code) {
        if (null == code) return "";
        for (BizApproveStatusEnum en : BizApproveStatusEnum.values()) {
            if (code.equals(en.getCode())) {
                return en.getDesc();
            }
        }
        return "";
    }


}
