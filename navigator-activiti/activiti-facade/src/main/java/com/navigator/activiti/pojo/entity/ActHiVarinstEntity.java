package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 历史变量信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_VARINST")
@ApiModel(value = "ActHiVarinstEntity对象", description = "历史变量信息")
public class ActHiVarinstEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "执行ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "变量类型")
    @TableField("VAR_TYPE_")
    private String varType;

    @ApiModelProperty(value = "版本")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "字节数组ID（ACT_GE_BYTEARRAY表的主键）")
    @TableField("BYTEARRAY_ID_")
    private String bytearrayId;

    @ApiModelProperty(value = "存储double类型的数据")
    @TableField("DOUBLE_")
    private Float doubleData;

    @ApiModelProperty(value = "存储Long类型的数据")
    @TableField("LONG_")
    private Long longData;

    @ApiModelProperty(value = "存储String类型的数据")
    @TableField("TEXT_")
    private String text;

    @ApiModelProperty(value = "此处存储的是JPA持久化对象时，才会有值。此值为对象ID")
    @TableField("TEXT2_")
    private String text2;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME_")
    private Date createTime;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("LAST_UPDATED_TIME_")
    private Date lastUpdatedTime;


}
