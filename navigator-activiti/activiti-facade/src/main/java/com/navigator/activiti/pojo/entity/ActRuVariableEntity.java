package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 运行时流程变量数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_VARIABLE")
@ApiModel(value = "ActRuVariableEntity对象", description = "运行时流程变量数据表")
public class ActRuVariableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "字节表的ID（ACT_GE_BYTEARRAY）")
    @TableField("BYTEARRAY_ID_")
    private String bytearrayId;

    @ApiModelProperty(value = "存储变量类型为Double")
    @TableField("DOUBLE_")
    private Float doubleData;

    @ApiModelProperty(value = "存储变量类型为Long")
    @TableField("LONG_")
    private Long longData;

    @ApiModelProperty(value = "存储变量值类型为String 如此处存储持久化对象时，值jpa对象的class")
    @TableField("TEXT_")
    private String text;

    @ApiModelProperty(value = "此处存储的是JPA持久化对象时，才会有值。此值为对象ID")
    @TableField("TEXT2_")
    private String text2;


}
