package com.navigator.activiti.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 流程的业务信息
 */
@Data
@Accessors(chain = true)
public class ApproveBizInfoDTO implements Serializable {
    /**
     * 中文名称  例如：合同ID
     */
    private String displayName;
    /**
     * 英文名称  例如：contractId
     */
    private String name;
    /**
     * 值
     */
    private String value;

    private Integer index;
}
