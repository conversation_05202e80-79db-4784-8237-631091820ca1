package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * 多实例任务完成条件枚举
 *
 * <AUTHOR>
 * @date 2021/10/25 12:00
 */
@Getter
public enum CompleteConditionEnum {
    /**
     * 会签（多实例节点）环节中涉及的几个默认的流程变量：
     * 1、nrOfInstances 该会签环节中总共有多少个实例 。
     * 2、nrOfActiveInstances 当前活动的实例的数量，即还没有 完成的实例数量。
     * 3、nrOfCompletedInstances 已经完成的实例的数量。
     */
    STARTER("starter", "发起人"),
    SINGLE_APPROVAL("${nrOfCompletedInstances>0}", "其中一人审批即可"),
    All_USER_APPROVAL("${nrOfCompletedInstances/nrOfInstances=1}", "需所有成员审批"),
    END("end","流程结束"),
    ;
    String condition;
    String desc;

    CompleteConditionEnum(String condition, String desc) {
        this.condition = condition;
        this.desc = desc;
    }

    public static CompleteConditionEnum getByCondition(String condition) {
        for (CompleteConditionEnum en : CompleteConditionEnum.values()) {
            if (condition == en.getCondition()) {
                return en;
            }
        }
        return CompleteConditionEnum.STARTER;
    }
}
