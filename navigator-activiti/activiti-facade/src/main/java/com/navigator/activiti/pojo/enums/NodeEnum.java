package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/2/11 11:42
 */
@Getter
public enum NodeEnum {

    NODE_START("NODE_START","流程启动"),
    NODE_SUBMIT("NODE_SUBMIT","提交合同组审批"),
    NODE_END_PASS("NODE_END_PASS","审批通过"),
    NODE_END_REJECT("NODE_END_REJECT","审批驳回"),
    NODE_A("NODE_A","A签审批"),
    NODE_B("NODE_B","B签审批"),
    NODE_C("NODE_C","CFO/CEO任一审批"),
    NODE_C1("NODE_C1","CFO审批"),
    NODE_C2("NODE_C2","CEO审批"),
    ;

    private String nodeName;
    private String desc;

    NodeEnum(String nodeName,String desc){
        this.nodeName =nodeName;
        this.desc = desc;
    }

    public static NodeEnum getByNodeName(String nodeName) {
        for (NodeEnum en : NodeEnum.values()) {
            if (nodeName.equals(en.nodeName) ) {
                return en;
            }
        }
        return NodeEnum.NODE_START;
    }
}
