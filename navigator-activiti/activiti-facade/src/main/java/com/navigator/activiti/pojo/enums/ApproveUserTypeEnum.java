package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/25 10:27
 */
@Getter
public enum ApproveUserTypeEnum {
    SINGLE(1, "单人"),
    MULTI_USER(2, "多人"),
    SINGLE_ROLE(3, "单角色"),
    MULTI_ROLE(4, "多角色"),
    ;
    int value;
    String desc;

    ApproveUserTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ApproveUserTypeEnum getByValue(int value) {
        for (ApproveUserTypeEnum en : ApproveUserTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ApproveUserTypeEnum.SINGLE;
    }
}
