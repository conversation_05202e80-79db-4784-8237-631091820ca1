package com.navigator.activiti.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 审批任务信息（包括已审批和待审批的任务）
 * 主表：ACT_RU_TASK
 */
@Data
@Accessors(chain = true)
public class ApproveTaskInfoDTO implements Serializable {
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 节点记录ID  act_re_node 的 id
     */
    private Integer nodeUniqueId;
    /**
     * 流程实例ID
     */
    private String processInstId;
    /**
     * 流程名称
     */
    private String processName;
    /**
     * 流程实例信息
     */
    private ProcessInstDTO processInstDTO;
    /**
     * 节点ID
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private ProcessNodeDTO processNodeDTO;
    /**
     * 是否已审批
     */
    private boolean approved;
    /**
     * 审批角色名称
     */
    private String approveRoleName;
    /**
     * 审批用户ID
     */
    private Integer approveUserId;
    /**
     * 审批用户名
     */
    private String approveUserName;

    /**
     * 待审批的设定角色或人
     * 已审批的审批人
     */
    private String approveUserInfo;

    /**
     * 审批动作
     * {@link com.navigator.activiti.pojo.enums.ApproveActionEnum}
     */
    private String approveActionName;
    /**
     * 审批动作值
     * {@link com.navigator.activiti.pojo.enums.ApproveActionEnum}
     */
    private Integer approveActionValue;
    /**
     * 审批意见
     */
    private String approveMemo;
    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createdAt;
    /**
     * 任务审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp approvedAt;
}
