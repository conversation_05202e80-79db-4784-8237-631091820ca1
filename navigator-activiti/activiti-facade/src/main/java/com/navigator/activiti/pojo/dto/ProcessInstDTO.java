package com.navigator.activiti.pojo.dto;

import com.navigator.activiti.pojo.entity.ActCuProcDataEntity;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.enums.ApproveActionOptionEnum;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 流程实例信息
 * 基于部署的流程
 * 主表：ACT_HI_PROCINST，副表：ACT_RE_PROCDEF，关联ACT_HI_VARINST、ACT_RE_NODE、ACT_HI_TASKINST、ACT_HI_TASKINST
 */
@Data
@Accessors(chain = true)
public class ProcessInstDTO extends ActHiProcinstEntity {

    /**
     * 采/销
     */
    private String salesTypeName;

    private String approveCause;

    //BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
    private String ttTypeName;
    //BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

    /**
     * 业务模块
     */
    private String bizModuleDesc;

    private int[] approveActionOptions;

    /**
     * 审批结果
     * {@link com.navigator.activiti.pojo.enums.ApproveResultEnum}
     * ApproveResultEnum.description
     */
    private String approveResult;

    /**
     * 当前审批人（角色或者人员姓名）
     */
    private String approvingUser;

    /**
     * 发起人姓名
     */
    private String startUserName;

    /**
     * 流程定义流程图
     */
    private String processDefPictureUrl;
    /**
     * 流程实例流程图
     */
    private String processInstPictureUrl;
    /**
     * 业务数据
     */
    private List<ActCuProcDataEntity> approveBizInfoList;
    /**
     * 流程实例涉及的节点
     */
    private List<ProcessNodeDTO> processNodeList;
    /**
     * 流程审批历史
     */
    private List<ApproveTaskActInfoDTO> approveTaskInfoList;

    private ApproveTaskActInfoDTO latestApproveTaskActInfo;

    /**
     * 权益变更业务数据
     */
    private List<EquityChangeInfoDTO> equityChangeBizInfoList;

    public String getSalesTypeName() {
        return ContractSalesTypeEnum.getDescByValue(getSalesType());
    }

    public String getBizModuleDesc() {
        return ModuleTypeEnum.getDescByValue(getBizModule());
    }

    public String getApproveResult() {
        //审批中，取业务审批状态
        if (null != getStatus() && getStatus() == ApproveResultEnum.APPROVING.getValue()) {
            return null == getApproveStatus() ? ApproveResultEnum.APPROVING.getDesc() : getApproveStatus();
            //return BizApproveStatusEnum.getByCode(getApproveStatus()).getDesc();
        }
        //取审批结果
        return ApproveResultEnum.getByValue(getStatus()).getDesc();
    }

    public int[] getApproveActionOptions() {
        Integer tradeTypeValue = getTradeTypeValue();
        ContractTradeTypeEnum tradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeTypeValue);
        return ApproveActionOptionEnum.getAbility(tradeTypeEnum.name());
    }

    //使用该字段作为进审原因
    public String getApproveCause() {
        return null == getDeleteReason() ? "" : getDeleteReason();
    }
}
