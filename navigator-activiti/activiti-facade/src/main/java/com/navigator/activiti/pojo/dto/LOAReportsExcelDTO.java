package com.navigator.activiti.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/26
 */
@Data
@Accessors(chain = true)
public class LOAReportsExcelDTO {

    @Excel(name = "tt/合同编号")
    private String code;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "采销类型")
    private String salesType;

    @Excel(name = "审批类型")
    private String approveType;

    @Excel(name = "驳回原因")
    private String rejectReason;

    @Excel(name = "审批人")
    private String approver;

    @Excel(name = "变更字段")
    private String changeField;

    @Excel(name = "变更前")
    private String beforeValue;

    @Excel(name = "变更后")
    private String afterValue;
}
