package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 运行时流程人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_IDENTITYLINK")
@ApiModel(value = "ActRuIdentitylinkEntity对象", description = "运行时流程人员表")
public class ActRuIdentitylinkEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_")
    private Long id;

    @ApiModelProperty(value = "乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "组ID")
    @TableField("GROUP_ID_")
    private String groupId;

    @ApiModelProperty(value = "类型")
    @TableField("TYPE_")
    private String type;

    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID_")
    private String userId;

    @ApiModelProperty(value = "任务ID")
    @TableField("TASK_ID_")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;


}
