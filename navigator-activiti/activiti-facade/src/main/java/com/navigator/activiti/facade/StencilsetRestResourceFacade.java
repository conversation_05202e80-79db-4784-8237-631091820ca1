package com.navigator.activiti.facade;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2021/11/9 11:30
 */
@FeignClient(name = "navigator-activiti-service")
public interface StencilsetRestResourceFacade {

    @RequestMapping(value = "/editor/stencilset", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    String getStencilset();
}
