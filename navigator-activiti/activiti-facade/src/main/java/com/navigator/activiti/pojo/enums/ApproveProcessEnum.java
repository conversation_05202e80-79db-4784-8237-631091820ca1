package com.navigator.activiti.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/11/29 10:24
 */
@Getter
public enum ApproveProcessEnum {

    SC_ADD("PROC_DEF_SC_ADD", "销售合同", 2, "豆粕销售-新增合同-审批", "10", "OSM_MEAL,OSM_OIL", "NEW"),
    SC_MODIFY("PROC_DEF_SC_MODIFY", "销售合同", 2, "豆粕销售-修改合同-审批", "10", "OSM_MEAL,OSM_OIL", "REVISE_NORMAL,REVISE_CHANGE_CUSTOMER,SPLIT_NORMAL,SPLIT_CHANGE_CUSTOMER"),
    SC_PRICE("PROC_DEF_SC_PRICE", "销售合同", 2, "豆粕销售-点/转/反业务-审批", "10", "OSM_MEAL,OSM_OIL", "TRANSFER_PART,TRANSFER_ALL,REVERSE_PRICE_PART,REVERSE_PRICE_ALL,STRUCTURE_PRICE"),
    SC_STRUCTURE("PROC_DEF_SC_STRUCTURE", "销售合同", 2, "豆粕销售-结构化定价合同-审批", "10", "OSM_MEAL,OSM_OIL", ""),
    SC_BUYBACK("PROC_DEF_SC_BUYBACK", "销售合同", 1, "豆粕销售-回购-审批", "10", "OSM_MEAL,OSM_OIL", "BUYBACK"),
    SC_WASHOUT("PROC_DEF_SC_WASHOUT", "销售合同", 2, "豆粕销售-解约定赔-审批", "10", "OSM_MEAL,OSM_OIL", "WASHOUT"),
    SC_CLOSED("PROC_DEF_SC_CLOSED", "销售合同", 2, "豆粕销售-关闭-审批", "10", "OSM_MEAL,OSM_OIL", "CLOSED"),
    SC_FIXED("PROC_DEF_SC_FIXED", "销售合同", 2, "豆粕销售合同暂定价定价", "10", "OSM_MEAL,OSM_OIL", "FIXED"),
    SC_INVALID("PROC_DEF_SC_INVALID", "销售合同", 2, "作废豆粕销售合同", "10", "OSM_MEAL,OSM_OIL", "INVALID"),
    SC_EQUITY("PROC_DEF_SC_EQUITY", "销售合同", 2, "豆粕销售权益变更审批", "10", "OSM_MEAL,OSM_OIL", "EQUITY"),


    PC_ADD("PROC_DEF_PC_ADD", "采购合同", 1, "豆粕采购-新增合同-审批", "10", "OSM_MEAL,OSM_OIL", "NEW"),
    PC_MODIFY("PROC_DEF_PC_MODIFY", "采购合同", 1, "豆粕采购-修改合同-审批", "10", "OSM_MEAL,OSM_OIL", "REVISE_NORMAL,REVISE_CHANGE_CUSTOMER,SPLIT_NORMAL,SPLIT_CHANGE_CUSTOMER"),
    PC_PRICE("PROC_DEF_PC_PRICE", "采购合同", 1, "豆粕采购-点/转/反业务-审批", "10", "OSM_MEAL,OSM_OIL", "TRANSFER_PART,TRANSFER_ALL,REVERSE_PRICE_PART,REVERSE_PRICE_ALL,STRUCTURE_PRICE"),
    PC_WASHOUT("PROC_DEF_PC_WASHOUT", "采购合同", 1, "豆粕采购-解约定赔-审批", "10", "OSM_MEAL,OSM_OIL", "WASHOUT"),
    PC_CLOSED("PROC_DEF_PC_CLOSED", "采购合同", 1, "豆粕采购-关闭-审批", "10", "OSM_MEAL,OSM_OIL", "CLOSED"),
    PC_FIXED("PROC_DEF_PC_FIXED", "采购合同", 1, "豆粕采购合同暂定价定价", "10", "OSM_MEAL,OSM_OIL", "FIXED"),
    PC_INVALID("PROC_DEF_PC_INVALID", "采购合同", 1, "作废豆粕采购合同", "10", "OSM_MEAL,OSM_OIL", "INVALID"),

    /*******************************豆油 *********************************************/
    SC_SBO_ADD("PROC_SBO_DEF_SC_ADD", "销售合同", 2, "豆油销售-新增合同-审批", "10", "OSM_MEAL,OSM_OIL", "NEW"),
    SC_SBO_MODIFY("PROC_SBO_DEF_SC_MODIFY", "销售合同", 2, "豆油销售-修改合同-审批", "10", "OSM_MEAL,OSM_OIL", "REVISE_NORMAL,REVISE_CHANGE_CUSTOMER,SPLIT_NORMAL,SPLIT_CHANGE_CUSTOMER"),
    SC_SBO_PRICE("PROC_SBO_DEF_SC_PRICE", "销售合同", 2, "豆油销售-点/转/反业务-审批", "10", "OSM_MEAL,OSM_OIL", "TRANSFER_PART,TRANSFER_ALL,REVERSE_PRICE_PART,REVERSE_PRICE_ALL,STRUCTURE_PRICE"),
    SC_SBO_STRUCTURE("PROC_SBO_DEF_SC_STRUCTURE", "销售合同", 2, "豆油销售-结构化定价合同-审批", "10", "OSM_MEAL,OSM_OIL", ""),
    SC_SBO_BUYBACK("PROC_SBO_DEF_SC_BUYBACK", "销售合同", 1, "豆油销售-回购-审批", "10", "OSM_MEAL,OSM_OIL", "BUYBACK"),
    SC_SBO_WASHOUT("PROC_SBO_DEF_SC_WASHOUT", "销售合同", 2, "豆油销售-解约定赔-审批", "10", "OSM_MEAL,OSM_OIL", "WASHOUT"),
    SC_SBO_CLOSED("PROC_SBO_DEF_SC_CLOSED", "销售合同", 2, "豆油销售-关闭-审批", "10", "OSM_MEAL,OSM_OIL", "CLOSED"),
    SC_SBO_FIXED("PROC_SBO_DEF_SC_FIXED", "销售合同", 2, "豆油销售合同暂定价定价", "10", "OSM_MEAL,OSM_OIL", "FIXED"),
    SC_SBO_INVALID("PROC_SBO_DEF_SC_INVALID", "销售合同", 2, "作废豆油销售合同", "10", "OSM_MEAL,OSM_OIL", "INVALID"),
    SC_SBO_EQUITY("PROC_SBO_DEF_SC_EQUITY", "销售合同", 2, "豆油销售权益变更审批", "10", "OSM_MEAL,OSM_OIL", "EQUITY"),


    PC_SBO_ADD("PROC_SBO_DEF_PC_ADD", "采购合同", 1, "豆油采购-新增合同-审批", "10", "OSM_MEAL,OSM_OIL", "NEW"),
    PC_SBO_MODIFY("PROC_SBO_DEF_PC_MODIFY", "采购合同", 1, "豆油采购-修改合同-审批", "10", "OSM_MEAL,OSM_OIL", "REVISE_NORMAL,REVISE_CHANGE_CUSTOMER,SPLIT_NORMAL,SPLIT_CHANGE_CUSTOMER"),
    PC_SBO_PRICE("PROC_SBO_DEF_PC_PRICE", "采购合同", 1, "豆油采购-点/转/反业务-审批", "10", "OSM_MEAL,OSM_OIL", "TRANSFER_PART,TRANSFER_ALL,REVERSE_PRICE_PART,REVERSE_PRICE_ALL,STRUCTURE_PRICE"),
    PC_SBO_WASHOUT("PROC_SBO_DEF_PC_WASHOUT", "采购合同", 1, "豆油采购-解约定赔-审批", "10", "OSM_MEAL,OSM_OIL", "WASHOUT"),
    PC_SBO_CLOSED("PROC_SBO_DEF_PC_CLOSED", "采购合同", 1, "豆油采购-关闭-审批", "10", "OSM_MEAL,OSM_OIL", "CLOSED"),
    PC_SBO_FIXED("PROC_SBO_DEF_PC_FIXED", "采购合同", 1, "豆粕采购合同暂定价定价", "10", "OSM_MEAL,OSM_OIL", "FIXED"),
    PC_SBO_INVALID("PROC_SBO_DEF_PC_INVALID", "采购合同", 1, "作废豆粕采购合同", "10", "OSM_MEAL,OSM_OIL", "INVALID"),


    START_APPROVE("START_APPROVE","发起审批",1,"发起审批","10","OSM_MEAL,OSM_OIL","START_APPROVE")
    ;

    String processKey;      // 流程定义的Key，据此找到流程定义
    String bizModule;       // 业务模块
    Integer salesType;      // 采销类型
    String processName;     // 流程名称
    String moduleCode;      // 业务模块编码
    String categoryNames;   // 支持的业务（二级）
    String tradeTypes;      // 支持的交易类型

    ApproveProcessEnum(String processKey, String bizModule, Integer salesType, String processName, String moduleCode, String categoryNames, String tradeTypes) {
        this.processKey = processKey;
        this.bizModule = bizModule;
        this.salesType = salesType;
        this.processName = processName;
        this.moduleCode = moduleCode;
        this.categoryNames = categoryNames;
        this.tradeTypes = tradeTypes;
    }

    public static ApproveProcessEnum getByValue(String value) {
        for (ApproveProcessEnum en : ApproveProcessEnum.values()) {
            if (value.equals(en.getProcessKey())) {
                return en;
            }
        }
        return ApproveProcessEnum.SC_ADD;
    }

    public static ApproveProcessEnum getByTradeInfo(int salesType, String categoryName, String tradeTypeName) throws NullPointerException {
        for (ApproveProcessEnum en : ApproveProcessEnum.values()) {
            if (en.getSalesType() == salesType
                    && en.getCategoryNames().contains(categoryName)
                    && en.getTradeTypes().contains(tradeTypeName)) {
                return en;
            }
        }
        return ApproveProcessEnum.SC_ADD;
    }

}
