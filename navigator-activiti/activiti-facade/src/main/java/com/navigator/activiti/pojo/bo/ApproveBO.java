package com.navigator.activiti.pojo.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import static com.navigator.common.constant.GlobalConstant.Sys.SUPER_MANAGER_ROLE_ID;

/**
 * <AUTHOR>
 * @date 2021/12/6 13:34
 */
@Data
public class ApproveBO {

    private Integer bizType;

    private Integer tradeType;

    /**
     * 用户id
     */
    private String userId;
    /**
     * 流程key
     */
    private String processKey;
    /**
     * 流程实例状态
     */
    private Integer procInstStatus;

    /**
     * 任务状态
     */
    private Integer taskStatus;
    /**
     * 主体id
     */
    private Integer companyId;
    /**
     * 工厂 code
     */
    private String deliveryFactoryCode;

    /**
     * 买方id
     */
    private Integer customerId;

    /**
     * 买方id
     */
    private Integer supplierId;

    /**
     * 采/销
     */
    private Integer salesType = 0;

    /**
     * 子品种
     */
    private Integer subCategoryId = 0;

    /**
     * 一级品类
     */
    private Integer category1;

    /**
     * 二级品类
     */
    private Integer category2;

    /**
     * 三级品类
     */
    private Integer category3;

    /**
     * 账套编码
     */
    private String siteCode;

    /**
     * 账套名称
     */
    private String siteName;

    /**
     * 模块
     */
    private String bizModule;

    /**
     * 业务编号（兼容referBizCode）
     */
    private String bizCode;

    /**
     * 客户名称
     */
    private String customerName;

    private List<String> procInstIdList;

    List<Integer> tradeTypeList;

    //业务线
    private String buCode;

    /**
     * 审批场景 1.合同审批 2.权益变更
     */
    private Integer approveScene;

    public List<Integer> getTradeTypeList() {
        List<Integer> list = new ArrayList<>();
        if (null != bizType && bizType > 0) {
            List<ContractTradeTypeEnum> contractTradeTypeEnumList = ContractTradeTypeEnum.getByTTType(bizType);
            for (ContractTradeTypeEnum tradeTypeEnum : contractTradeTypeEnumList) {
                list.add(tradeTypeEnum.getValue());
            }
        }
        return list;
    }

    List<Integer> userRoleIdList;

    boolean isSuper;

    public boolean isSuper() {
        if (null == getUserRoleIdList()) {
            setUserRoleIdList(new ArrayList<>());
        }
        return getUserRoleIdList().contains(SUPER_MANAGER_ROLE_ID);
    }
}
