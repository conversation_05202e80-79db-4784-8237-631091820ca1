package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 运行时任务节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_TASK")
@ApiModel(value = "ActRuTaskEntity对象", description = "运行时任务节点表")
public class ActRuTaskEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_", type = IdType.AUTO)
    private String id;

    @ApiModelProperty(value = "乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "父任务ID")
    @TableField("PARENT_TASK_ID_")
    private String parentTaskId;

    @ApiModelProperty(value = "描述")
    @TableField("DESCRIPTION_")
    private String description;

    @ApiModelProperty(value = "任务定义的ID")
    @TableField("TASK_DEF_KEY_")
    private String taskDefKey;

    @ApiModelProperty(value = "拥有者（一般情况下为空，只有在委托时才有值）")
    @TableField("OWNER_")
    private String owner;

    @ApiModelProperty(value = "签收人或委托人")
    @TableField("ASSIGNEE_")
    private String assignee;

    @ApiModelProperty(value = "委托类型")
    @TableField("DELEGATION_")
    private String delegation;

    @ApiModelProperty(value = "优先级别")
    @TableField("PRIORITY_")
    private Integer priority;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME_")
    private Date createTime;

    @ApiModelProperty(value = "到期时间")
    @TableField("DUE_DATE_")
    private Date dueDate;

    @ApiModelProperty(value = "类型")
    @TableField("CATEGORY_")
    private String category;

    @ApiModelProperty(value = "1代表激活 2代表挂起")
    @TableField("SUSPENSION_STATE_")
    private Integer suspensionState;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;

    @ApiModelProperty(value = "转签时间")
    @TableField("CLAIM_TIME_")
    private Date claimTime;

    @TableField("FORM_KEY_")
    private String formKey;

    @TableField("PROC_DEF_KEY_")
    private String processKey;


}
