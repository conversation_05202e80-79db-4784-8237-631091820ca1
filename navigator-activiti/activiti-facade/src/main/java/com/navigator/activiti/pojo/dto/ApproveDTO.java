package com.navigator.activiti.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import com.navigator.bisiness.enums.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.UUID;

@Data
@Accessors(chain = true)
public class ApproveDTO<T> implements Serializable {
    public String uid = UUID.randomUUID().toString();
    ContractSalesTypeEnum salesTypeEnum;            //采销类型

    ContractTradeTypeEnum contractTradeTypeEnum;    //合同交易类型
    // BUGFIX：case-1002824 LOA审批场景错误 Author: Wan 2024-12-04 Start
    TTTypeEnum ttTypeEnum;                          //TT类型
    // BUGFIX：case-1002824 LOA审批场景错误 Author: Wan 2024-12-04 end
    private Integer belongCustomerId;
    //主体id
    private Integer companyId;
    //主体名称
    private String companyName;
    //工厂id
    private Integer factoryId;
    //工厂名称
    private String factoryCode;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "一级品类名称")
    private String category1Name;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    private String userId;      //用户ID
    private String roleId;      //角色id 动态角色使用
    private String userName;    // 用户名称
    /**
     * 流程Key
     * {@link com.navigator.activiti.pojo.enums.ApproveProcessEnum}
     */
    private String processKey;  //

    private String processInstName;
    private String procInstId;

    /**
     * 业务模块
     * {@link ModuleTypeEnum}
     */
    private String bizModule;
    /**
     * 关联业务记录Id
     */
    private Integer bizId;
    /**
     * 关联业务Code
     */
    private String bizCode;
    /**
     * ttCode 关联的contractId
     */
    private Integer referBizId;
    /**
     * ttCode 关联的contractCode
     */
    private String referBizCode;

    private String buCode = BuCodeEnum.ST.getValue();

    /**
     * ttCode 关联的contractCode2
     */
    private String referBizCode2;

    private String customerName;
    private Integer customerId;
    private String supplierName;
    private Integer supplierId;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;

    /**
     * 任务ID（发起时为空）
     */
    private String taskId;

    private String nodeName;

    /**
     * 审批动作值
     * {@link com.navigator.activiti.pojo.enums.ApproveActionEnum}
     */
    private Integer actionValue = 0;
    /**
     * 审批动作名称
     * {@link com.navigator.activiti.pojo.enums.ApproveActionEnum}
     */
    private String actionName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    /**
     * 审批动作时间
     */
    private Timestamp actionTime;
    /**
     * 审批意见
     */
    private String memo;
    /**
     * 审批规则值（发起时）
     * {@link ContractApproveRuleEnum}
     */
    private Integer approveRuleValue = 1011;
    /**
     * 业务数据（发起时或者审批过程中需要特殊处理的数据）
     * {@link ApproveBizInfoDTO}
     */
    private T bizData;


    private T approveBizInfoDTO;

    //进审原因
    private String approveCause;
}
