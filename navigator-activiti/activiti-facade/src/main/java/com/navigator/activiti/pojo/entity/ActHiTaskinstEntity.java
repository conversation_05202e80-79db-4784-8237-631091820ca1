package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 历史任务流程实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_HI_TASKINST")
@ApiModel(value = "ActHiTaskinstEntity对象", description = "历史任务流程实例信息")
public class ActHiTaskinstEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "流程定义Id")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "任务定义Key")
    @TableField("TASK_DEF_KEY_")
    private String taskDefKey;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "父节点ID")
    @TableField("PARENT_TASK_ID_")
    private String parentTaskId;

    @ApiModelProperty(value = "描述")
    @TableField("DESCRIPTION_")
    private String description;

    @ApiModelProperty(value = "签收人（默认为空，只有在委托时才有值）")
    @TableField("OWNER_")
    private String owner;

    @ApiModelProperty(value = "签收人或被委托")
    @TableField("ASSIGNEE_")
    private String assignee;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("START_TIME_")
    private Date startTime;

    @ApiModelProperty(value = "委托时间（转签）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("CLAIM_TIME_")
    private Date claimTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("END_TIME_")
    private Date endTime;

    @ApiModelProperty(value = "时长")
    @TableField("DURATION_")
    private Long duration;

    @ApiModelProperty(value = "删除理由")
    @TableField("DELETE_REASON_")
    private String deleteReason;

    @ApiModelProperty(value = "优先级")
    @TableField("PRIORITY_")
    private Integer priority;

    @ApiModelProperty(value = "应完成时间")
    @TableField("DUE_DATE_")
    private Date dueDate;

    @ApiModelProperty(value = "表单key（desinger节点定义的form_key属性）")
    @TableField("FORM_KEY_")
    private String formKey;

    @TableField("CATEGORY_")
    private String category;

    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("PROCESS_KEY_")
    private String processKey;

    @TableField("ACTION_NAME_")
    @ApiModelProperty(value = "审批动作")
    private String actionName;


    @TableField("MEMO_")
    @ApiModelProperty(value = "审批意见")
    private String memo;

    @TableField("NODE_CANDIDATE_ROLES_")
    @ApiModelProperty(value = "节点候选角色列表")
    private String nodeCandidateRoles;

    @TableField("BELONG_CUSTOMER_ID_")
    @ApiModelProperty(value = "所属主体")
    private Integer belongCustomerId;

    @TableField("CATEGORY_ID_")
    @ApiModelProperty(value = "品类")
    private Integer categoryId;

    @TableField("TASK_CANDIDATE_ROLES_")
    @ApiModelProperty(value = "任务后续角色列表")
    private String taskCandidateRoles;

    @TableField("TASK_CANDIDATE_USERS_")
    @ApiModelProperty(value = "任务后续人列表")
    private String taskCandidateUsers;

    @ApiModelProperty(value = "采销类型")
    @TableField("SALES_TYPE_")
    private Integer salesType;

    @ApiModelProperty(value = "交易类型值")
    @TableField("TRADE_TYPE_VALUE_")
    private Integer tradeTypeValue;

    @ApiModelProperty(value = "业务编号")
    @TableField("BIZ_CODE_")
    private String bizCode;

    @ApiModelProperty(value = "买方名称")
    @TableField("CUSTOMER_NAME_")
    private String customerName;

    @ApiModelProperty(value = "买方id")
    @TableField("CUSTOMER_ID_")
    private Integer customerId;

    @ApiModelProperty(value = "供应商名称")
    @TableField("SUPPLIER_NAME_")
    private String supplierName;

    @ApiModelProperty(value = "供应商id")
    @TableField("SUPPLIER_ID_")
    private Integer supplierId;

    @ApiModelProperty(value = "主体ID")
    @TableField("COMPANY_ID_")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    @TableField("COMPANY_NAME_")
    private String companyName;

    @ApiModelProperty(value = "工厂id")
    @TableField("FACTORY_ID_")
    private Integer factoryId;

    @ApiModelProperty(value = "工厂code")
    @TableField("FACTORY_CODE_")
    private String factoryCode;

    @ApiModelProperty(value = "一级品类")
    @TableField("CATEGORY_1_")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    @TableField("CATEGORY_2_")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    @TableField("CATEGORY_3_")
    private Integer category3;

    @ApiModelProperty(value = "账套编码")
    @TableField("SITE_CODE_")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    @TableField("SITE_NAME_")
    private String siteName;

    @ApiModelProperty(value = "期货代码")
    @TableField("FUTURE_CODE_")
    private String futureCode;

    @ApiModelProperty(value = "业务线")
    @TableField("BU_CODE_")
    private String buCode;

    // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
    @ApiModelProperty(value = "ttType")
    @TableField("TT_TYPE_")
    private Integer ttType;
    // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

}
