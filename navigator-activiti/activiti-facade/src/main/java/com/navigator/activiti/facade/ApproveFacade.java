package com.navigator.activiti.facade;

import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.LOAReportsExcelDTO;
import com.navigator.activiti.pojo.dto.RecordBizOperationDTO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/9 11:29
 */
@FeignClient(name = "navigator-activiti-service")
public interface ApproveFacade {

    /**
     * 提交审批
     *
     * @param approveDTO
     * @return
     */
    @PostMapping("/approveApi/start")
    Result start(@RequestBody ApproveDTO approveDTO);


    /**
     * 任务审批
     *
     * @param approveDTO
     * @return
     */
    @PostMapping("/approveApi/approve")
    Result approve(@RequestBody ApproveDTO approveDTO);

    /**
     * 作废
     *
     * @param approveDTO
     * @return
     */
    @PostMapping("/approveApi/cancel")
    Result cancel(@RequestBody ApproveDTO approveDTO);

    /**
     * 查询待审批与已审批
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/approveApi/queryApproveTask")
    Result queryApproveTask(@RequestBody QueryDTO<ApproveBO> queryDTO);

    /**
     * 查询待审批与已审批
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/approveApi/queryProcessInst")
    Result queryProcessInst(@RequestBody QueryDTO<ApproveBO> queryDTO);

    /**
     * 查询待审批与已审批
     *
     * @param procInstId
     * @return
     */
    @GetMapping("/approveApi/queryProcessInstDetail")
    Result queryProcessInstDetail(@RequestParam("procInstId") String procInstId);


    /**
     * 根据业务编码查询历史信息
     *
     * @param bizCode
     * @return
     */
    @GetMapping("/approveApi/getActHiProcinstByBizCode")
    ActHiProcinstEntity getActHiProcinstByBizCode(@RequestParam("bizCode") String bizCode);

    /**
     * 根据业务code获取审批记录
     *
     * @param bizCode
     * @return
     */
    @GetMapping("/approveApi/queryHiTask")
    Result queryHiTaskByBizCode(@RequestParam("bizCode") String bizCode);

    @GetMapping("/approveApi/getActHiTaskByBizCode")
    Result getActHiTaskByBizCode(@RequestParam("bizCode") String bizCode);

    @GetMapping("/approveApi/queryHiTaskByProcInstId")
    Result queryHiTaskByProcInstId(@RequestParam("procInstId") String procInstId);

    /**
     * 根据taskId获取任务详情
     *
     * @param taskId
     * @return
     */
    @GetMapping("/approveApi/getApproveTaskInfo")
    Result getApproveTaskInfo(@RequestParam("taskId") String taskId);


    /**
     * 记录日志
     *
     * @param recordBizOperationDTO
     * @return
     */
    @PostMapping("/approveApi/recordBizOperation")
    Result recordBizOperation(@RequestBody RecordBizOperationDTO recordBizOperationDTO);


    /**
     * 获取我的待审批任务列表（查询条件ProcKey下拉列表）
     *      TaskId，procName，procKey，procInstId，创建人，创建时间，任务开始时间，任务状态
     * 获取流程实例列表（查询条件procKey下拉列表，审批中/审批完成）
     *      procInstId，procName，procKey，创建人，创建时间，审批状态
     *  查看流程实例信息
     *      procInstId，procName，procKey，流程图url，节点列表，审批历史
     *      业务信息
     *   查看任务信息
     *      流程实例信息：procIstId，procName，procKey，流程图url，节点列表，审批历史
     *      节点信息：taskId，节点名称、节点编号，节点状态
     *      业务信息
     *    对任务进行审批
     *      流程实例信息：procIstId，procName，procKey，流程图url，节点列表，审批历史
     *      业务信息
     *      节点信息：taskId，节点名称、节点编号，节点状态
     *      审批意见
     *
     */

    @GetMapping("/downloadLOAReject")
    void downloadLOAReject(HttpServletResponse response);

}
