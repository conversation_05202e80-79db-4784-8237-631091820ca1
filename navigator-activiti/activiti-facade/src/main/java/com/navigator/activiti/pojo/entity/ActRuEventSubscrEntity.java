package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RU_EVENT_SUBSCR")
@ApiModel(value = "ActRuEventSubscrEntity对象", description = "")
public class ActRuEventSubscrEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "乐观锁Version")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "事件类型")
    @TableField("EVENT_TYPE_")
    private String eventType;

    @ApiModelProperty(value = "事件名称")
    @TableField("EVENT_NAME_")
    private String eventName;

    @ApiModelProperty(value = "执行实例ID")
    @TableField("EXECUTION_ID_")
    private String executionId;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROC_INST_ID_")
    private String procInstId;

    @ApiModelProperty(value = "活动实例ID")
    @TableField("ACTIVITY_ID_")
    private String activityId;

    @ApiModelProperty(value = "配置")
    @TableField("CONFIGURATION_")
    private String configuration;

    @ApiModelProperty(value = "是否创建	")
    @TableField("CREATED_")
    private Date created;

    @ApiModelProperty(value = "流程定义ID")
    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;


}
