package com.navigator.activiti.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@Accessors(chain = true)
@TableName("ACT_RE_MODEL")
@ApiModel(value = "ActReModelEntity对象", description = "")
public class ActReModelEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId(value = "ID_")
    private String id;

    @ApiModelProperty(value = "乐观锁")
    @TableField("REV_")
    private Integer rev;

    @ApiModelProperty(value = "名称")
    @TableField("NAME_")
    private String name;

    @ApiModelProperty(value = "模型KEY")
    @TableField("KEY_")
    private String key;

    @ApiModelProperty(value = "类型")
    @TableField("CATEGORY_")
    private String category;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME_")
    private Date createTime;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("LAST_UPDATE_TIME_")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "版本")
    @TableField("VERSION_")
    private Integer version;

    @ApiModelProperty(value = "数据源信息")
    @TableField("META_INFO_")
    private String metaInfo;

    @ApiModelProperty(value = "部署ID")
    @TableField("DEPLOYMENT_ID_")
    private String deploymentId;

    @ApiModelProperty(value = "编辑源值ID")
    @TableField("EDITOR_SOURCE_VALUE_ID_")
    private String editorSourceValueId;

    @ApiModelProperty(value = "编辑源额外值ID")
    @TableField("EDITOR_SOURCE_EXTRA_VALUE_ID_")
    private String editorSourceExtraValueId;

    @ApiModelProperty(value = "租户")
    @TableField("TENANT_ID_")
    private String tenantId;


}
