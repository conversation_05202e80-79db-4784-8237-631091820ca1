package com.navigator.activiti.pojo.enums;

import lombok.Getter;

@Getter

/**
 * 审批动作枚举，用户所有审批动作均应在此枚举中体现.
 * ===========================================
 * 强调：这个是任务节点的可选动作
 * ===========================================
 */
public enum ApproveActionEnum {
    START(0, "START", "提交审批"),
    AGREE(1, "AGREE", "同意"),
    REJECT(3, "REJECT", "驳回"),
    TRANSFER(4, "TRANSFER", "转签"),
    REFUSE(9, "REFUSE", "拒绝"),
    ;

    int value;
    String code;
    String name;

    ApproveActionEnum(int value, String code, String name) {
        this.value = value;
        this.code = code;
        this.name = name;
    }

    public static ApproveActionEnum getByValue(int value) {
        for (ApproveActionEnum en : ApproveActionEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ApproveActionEnum.AGREE;
    }

    public static ApproveActionEnum getByCode(String code) {
        for (ApproveActionEnum en : ApproveActionEnum.values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return ApproveActionEnum.START;
    }
}
