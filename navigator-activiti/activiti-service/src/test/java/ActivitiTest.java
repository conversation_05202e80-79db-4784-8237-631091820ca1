import com.navigator.activiti.ActivitiApplication;
import com.navigator.activiti.dao.*;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.enums.ApproveProcessEnum;
import com.navigator.activiti.service.ApproveService;
import com.navigator.common.dto.QueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.history.HistoricVariableInstance;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/11 11:11 test
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivitiApplication.class)
@Slf4j
@Rollback
public class ActivitiTest {
    @Resource
    ApproveService approveService;
    @Resource
    ProcessEngine processEngine;
    @Resource
    ActRuIdentitylinkDao ActRuIdentitylinkDao;
    @Resource
    ActRuTaskDao actRuTaskDao;
    @Resource
    ActHiCommentDao actHiCommentDao;
    @Resource
    ActHiTaskinstDao actHiTaskinstDao;
    @Resource
    ActHiProcinstDao actHiProcinstDao;

    @Test
    public void findGroupsByUser() {
        ApproveBO approveBO = new ApproveBO();
        approveBO.setUserId("30");
        List<String> roleIds = new ArrayList<>();
        roleIds.add("35");
        //List<String> taskIds = ActRuIdentitylinkDao.queryCandidateTasks(approveBO.getUserId(), roleIds);
        List<String> taskIds=new ArrayList<>();
        taskIds.add("111");
        taskIds.add("222");


        QueryDTO<ApproveBO> queryDTO = new QueryDTO<>();
        queryDTO.setCondition(approveBO);
        actRuTaskDao.queryRuTask(queryDTO, taskIds);
    }

    @Test
    public void getBizData() {
        HistoricVariableInstance bizData = processEngine.getHistoryService().createHistoricVariableInstanceQuery().processInstanceId("13812501").variableName("bizData").singleResult();
        System.out.println(bizData.getValue());
        System.out.println(bizData);
    }

    @Test
    public void get() {
//        List<String> taskIds = actHiCommentDao.getBaseMapper().selectList(
//                Wrappers.<ActHiCommentEntity>lambdaQuery().eq(ActHiCommentEntity::getType, "AddComment")
//        )
//                .stream().map(ActHiCommentEntity::getTaskId).collect(Collectors.toList());
//        List<ActHiTaskinstEntity> actHiTaskinstEntityList = actHiTaskinstDao.getBaseMapper().selectList(
//                Wrappers.<ActHiTaskinstEntity>lambdaQuery()
//                        .isNotNull(ActHiTaskinstEntity::getEndTime).notIn(ActHiTaskinstEntity::getId, taskIds)
//        );
//        actHiTaskinstEntityList.stream().forEach(
//                actHiTaskinstEntity -> {
////                    approveService.addComment(actHiTaskinstEntity.getId(), "1111", "1", actHiTaskinstEntity.getProcInstId());
//                }
//        );
//        List<String> procInstIds = actHiProcinstDao.list().stream().map(ActHiProcinstEntity::getProcInstId).collect(Collectors.toList());
//        actRuTaskDao.getBaseMapper().delete(Wrappers.<ActRuTaskEntity>lambdaQuery().notIn(ActRuTaskEntity::getProcInstId,procInstIds));


    }

    @Test
    public void test() {
//        LocalDate date = LocalDate.now();
//        System.out.println(date);
//
//        String token = JwtUtils.getJwtToken("1", "lucy");
//        System.out.println(token);
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setTaskId("q212313")
                .setUserId("1")
                .setProcessKey(ApproveProcessEnum.SC_ADD.getProcessKey())
                .setActionValue(1);
        approveService.approve(approveDTO);
    }


}
