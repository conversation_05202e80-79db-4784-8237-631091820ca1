package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.ActivitiApplication;
import com.navigator.activiti.dao.ActHiProcinstDao;
import com.navigator.activiti.facade.impl.ApproveFacadeImpl;
import com.navigator.activiti.manager.RoleEntityManager;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.enums.ApproveActionEnum;
import com.navigator.activiti.pojo.enums.ApproveProcessEnum;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.pojo.enums.ContractApproveRuleEnum;
import com.navigator.activiti.service.ApproveService;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.identity.Group;
import org.activiti.engine.impl.persistence.entity.GroupEntityImpl;
import org.activiti.engine.task.IdentityLink;
import org.junit.After;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;


/**
 * <AUTHOR>
 * @date 2021/12/17 16:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivitiApplication.class)
//@Transactional
//@Rollback
public class ApproveServiceImplTest {

    @Resource
    private ApproveService approveService;

    @Resource
    private ApproveServiceImpl approveServiceImpl;
    @Resource
    ProcessEngine processEngine;

    @Resource
    private TaskService taskService;

    private ApproveDTO approveDTO;

    @MockBean
    RoleEntityManager roleEntityManager;

    @Resource
    ApproveFacadeImpl approveFacade;

    @Resource
    ActHiProcinstDao actHiProcinstDao;

    @Test
    public void approve_batch() {
        // 模拟外部接口  并且模拟该用户无角色即无权限
        //doReturn(new ArrayList<Group>()).when(roleEntityManager).findGroupsByUser(anyString());

        approveDTO = new ApproveDTO();
        approveDTO.setActionValue(1);
        approveDTO.setProcessKey("PROC_DEF_SC_MODIFY");
        approveDTO.setUserId("1");
        approveDTO.setCategory1(10);
        approveDTO.setCategory2(11);
        approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.NEW);
        approveDTO.setSalesTypeEnum(ContractSalesTypeEnum.SALES);
        approveDTO.setMemo("222222222222");
        approveDTO.setTaskId("14618428");
        //approveDTO.setTaskIds("");

        approveFacade.approve(approveDTO);

        System.out.println("");
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 初始化DTO
        ApproveDTO approveDTO = new ApproveDTO();
        String id = String.valueOf(Math.random()).substring(12);
        System.out.println(id);
        approveDTO.setUserId("1")
                .setProcessKey(ApproveProcessEnum.SC_ADD.getProcessKey())
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getModule())
                .setBizId(Integer.valueOf(id))
                .setBizCode("SCTest" + id)
                .setReferBizCode("TJDF" + id);

        this.approveDTO = approveDTO;

    }

    @After
    public void tearDown() throws Exception {
    }


    @Test
    public void start_loa() {
        ApproveDTO approveDTON = new ApproveDTO();
        ActHiProcinstEntity pinst = actHiProcinstDao.getActHiProcinstByProcInstId("15042511");
        String js = pinst.getDeleteReason();
        approveDTON = JSON.parseObject(js, ApproveDTO.class);

        approveService.start(approveDTON);
    }

    @Test
    public void start_NeedSign() {
        approveDTO.setApproveRuleValue(ContractApproveRuleEnum.ABC.getValue());
        // 启动审批流程  需要审批
        ApproveResultDTO approveResultDTO = approveService.start(approveDTO);
        assertNotNull(approveResultDTO);
        assertEquals(approveDTO.getBizCode(), approveResultDTO.getBizCode());
        assertEquals((long) ApproveResultEnum.APPROVING.getValue(), (long) approveResultDTO.getApproveResult());

    }

    @Test
    public void start_NoSign() {
        approveDTO.setApproveRuleValue(ContractApproveRuleEnum.NONE.getValue());
        // 启动审批流程 免签
        ApproveResultDTO approveResultDTO = approveService.start(approveDTO);
        assertNotNull(approveResultDTO);
        assertEquals(approveDTO.getBizCode(), approveResultDTO.getBizCode());
        assertEquals((long) ApproveResultEnum.AGREE.getValue(), (long) approveResultDTO.getApproveResult());
    }

    @Test
    public void start() {
        List<ApproveBizInfoDTO> list = new ArrayList<>();
        list.add(new ApproveBizInfoDTO().setName("bc001")
                .setDisplayName("业务属性1")
                .setValue("2000001")
                .setIndex(1));
        list.add(new ApproveBizInfoDTO().setName("bc002")
                .setDisplayName("业务属性2")
                .setValue("2000002")
                .setIndex(2));
        list.add(new ApproveBizInfoDTO().setName("bc002")
                .setDisplayName("业务属性2")
                .setValue("2000002")
                .setIndex(3));
        approveDTO.setUserId("695929")
                .setCategory1(10)
                .setCategory2(11)
                .setSalesTypeEnum(ContractSalesTypeEnum.SALES)
                .setContractTradeTypeEnum(ContractTradeTypeEnum.STRUCTURE_PRICE)
                .setProcessKey("PROC_DEF_PC_ADD")
                .setBizModule(ModuleTypeEnum.TRADE_TICKET.getModule())
                .setBizId(100000001)
                .setBizCode("PR20220303000001")
                .setReferBizCode("HTTJIB2022030313020302")
                .setReferBizCode2("LKGHT00002")
                .setApproveRuleValue(1015)
                .setBizData(list);

        System.out.println(JSON.toJSONString(approveDTO));
        //approveService.start(approveDTO);
    }


    @Test
    public void start_UserNull() {
        approveDTO.setUserId(null);
        try {
            approveService.start(approveDTO);
        } catch (Exception e) {
            assertEquals("启动人或审批人不能为空", e.getMessage());
        }


    }

    @Test
    public void approve() {
        // 模拟外部接口  并且模拟该用户无角色即无权限
        //{"actionValue":1,
        // "categoryEnum":"OSM",
        // "contractTradeTypeEnum":"TRANSFER_ALL",
        // "memo":"",
        // "processKey":"PROC_DEF_SC_PRICE",
        // "salesTypeEnum":"SALES",
        // "subCategoryEnum":"OSM_MEAL",
        // "taskId":"14559678",
        // "uid":"b1907588-b1f4-450d-b56f-b5eb8a2c5f1e",
        // "userId":"1"}
        ApproveDTO approveDTO2 = new ApproveDTO();
        approveDTO2.setActionValue(ApproveActionEnum.AGREE.getValue())
                .setCategory1(10)
                .setCategory2(11)
                .setContractTradeTypeEnum(ContractTradeTypeEnum.TRANSFER_ALL)
                .setMemo("test")
                .setProcessKey("PROC_DEF_SC_PRICE")
                .setSalesTypeEnum(ContractSalesTypeEnum.SALES)
                .setUserId("1")
                .setCompanyId(1)
                .setTaskId("14559608");

        approveService.approve(approveDTO2);
    }

    @Test
    public void approve_NoTask() {
        // 模拟外部接口  并且模拟该用户无角色即无权限
        doReturn(new ArrayList<Group>()).when(roleEntityManager).findGroupsByUser(anyString());
        approveDTO.setTaskId("YUUHH")
                .setActionValue(ApproveActionEnum.AGREE.getValue());
        approveService.approve(approveDTO);
    }

    @Test
    public void approve_agree() {
        List<Group> groups = new ArrayList<>();
        Group group = new GroupEntityImpl();
        group.setId("35");
        groups.add(group);
        doReturn(groups).when(roleEntityManager).findGroupsByUser(anyString());
        approveDTO.setTaskId(null)
                .setActionValue(ApproveActionEnum.AGREE.getValue());
        ApproveResultDTO rr = approveService.approve(approveDTO);
        System.out.println("");

    }

    @Test
    public void approve_reject() {
        List<Group> groups = new ArrayList<>();
        Group group = new GroupEntityImpl();
        group.setId("35");
        groups.add(group);
        doReturn(groups).when(roleEntityManager).findGroupsByUser(anyString());
        approveDTO.setTaskId("13913230")
                .setActionValue(ApproveActionEnum.REJECT.getValue());
        approveService.approve(approveDTO);

    }

    @Test
    public void timeout() {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void cancel_NoProcInst() {
        approveService.cancel(approveDTO);
    }

    @Test
    public void cancel_NoPermission() {
        approveDTO.setUserId("2");
        approveDTO.setBizCode("8865645645");
        approveService.cancel(approveDTO);
    }

    @Test
    public void cancel() {
        approveDTO.setBizCode("8865645645");
        Boolean cancel = approveService.cancel(approveDTO);
        assertTrue(cancel);
    }

    @Test
    public void claimTask_NoTask() {
        approveService.claimTask("123", "2");
    }

    @Test
    public void claimTask() {
        approveService.claimTask("13913230", "2");
    }

    @Test
    public void unClaimTask() {
        approveService.unClaimTask("13913230");
    }

    @Test
    public void delegate() {
        approveService.delegate("13913230", "2");
    }

    @Test
    public void resolveTask() {
        approveService.resolveTask("13913230");
    }

    @Test
    public void addCandidateStarterUser() {
//        processEngine.getRepositoryService().addCandidateStarterUser("PROC_DEF_SC_ADD:3:14030010","zhangzhang");
//        List<IdentityLink> identityLinkList = processEngine.getRepositoryService().getIdentityLinksForProcessDefinition("PROC_DEF_SC_ADD:3:14030010");
//        ProcessDefinition processDefinition = processEngine.getRepositoryService().createProcessDefinitionQuery().processDefinitionKey("PROC_DEF_SC_ADD").orderByProcessDefinitionVersion().desc().list().get(0);
        processEngine.getRepositoryService().addCandidateStarterGroup("PROC_DEF_SC_ADD:3:14030010", "34");
        List<IdentityLink> identityLinkList = processEngine.getRepositoryService().getIdentityLinksForProcessDefinition("PROC_DEF_SC_ADD:3:14030010");

//        processEngine.getHistoryService().createHistoricVariableInstanceQuery().taskId()

    }

    @Test
    public void testUpdateHiTask() {
        /*ActHiProcinstEntity actHiProcinstEntity = new ActHiProcinstEntity();
        actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId("14618313");
        approveServiceImpl.udpateHiTask(actHiProcinstEntity, "14648340", "tttt");*/

        approveServiceImpl.udpateHiTaskCommonInfo();
    }
}
