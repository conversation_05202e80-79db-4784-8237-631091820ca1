package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.ActivitiApplication;
import com.navigator.activiti.facade.impl.ApproveFacadeImpl;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ProcessInstDTO;
import com.navigator.activiti.service.ProcessInstService;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.activiti.engine.impl.persistence.entity.UserEntityImpl;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/24 11:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivitiApplication.class)
@Transactional
@Rollback
public class ProcessInstServiceImplTest {

    @Resource
    ProcessInstService processInstService;

    @MockBean
    EmployEntityManager employEntityManager;

    @Resource
    ApproveFacadeImpl approveFacade;

    @Before
    public void setUp() throws Exception {

        UserEntity userEntity = new UserEntityImpl();
        userEntity.setFirstName("NEO");
        userEntity.setLastName("");
        userEntity.setId("1");
        Mockito.when(employEntityManager.findById(Mockito.anyString())).thenReturn(userEntity);

    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testApprove() {
        ApproveDTO approveDTO = new ApproveDTO();
        approveDTO.setActionValue(1);
        approveDTO.setProcessKey("PROC_SBO_DEF_SC_ADD");
        approveDTO.setUserId("1");
        approveDTO.setCategory1(10);
        approveDTO.setCategory2(11);
        approveDTO.setContractTradeTypeEnum(ContractTradeTypeEnum.NEW);
        approveDTO.setSalesTypeEnum(ContractSalesTypeEnum.SALES);
        approveDTO.setMemo("222222222222");
        approveDTO.setTaskId("14485020");
        Result rr = approveFacade.approve(approveDTO);

        System.out.println(JSON.toJSONString(rr));
    }

    @Test
    public void test() {
        for (int i = 0; i < 15; i++) {
            System.out.println("===============================");
            Integer bizType = i;
            List<String> list = new ArrayList<>();
            if (null != bizType && bizType > 0) {
                List<ContractTradeTypeEnum> contractTradeTypeEnumList = ContractTradeTypeEnum.getByTTType(bizType);
                for (ContractTradeTypeEnum tradeTypeEnum : contractTradeTypeEnumList) {
                    list.add(tradeTypeEnum.name());
                }
            }
            System.out.println(JSON.toJSONString(list));
        }

    }


    @Test
    public void getProcessInstById() {
        ProcessInstDTO processInstDTO = processInstService.getProcessInstById("14337529", true);
        System.out.println(JSON.toJSONString(processInstDTO));
    }

    @Test
    public void getProcessInstDetailByTaskId() {
    }

    @Test
    public void queryProcessInst() {

        QueryDTO<ApproveBO> querDTO = new QueryDTO<>();
        ApproveBO approveBO = new ApproveBO();
        approveBO.setProcInstStatus(1);
        approveBO.setProcessKey("");
        approveBO.setSubCategoryId(0);
        approveBO.setSalesType(0);
        approveBO.setCustomerName("");
        approveBO.setBizCode("");
        approveBO.setUserId("");
        approveBO.setCustomerName("");
        querDTO.setCondition(approveBO);

        System.out.println(JSON.toJSONString(querDTO));
        Result rr = processInstService.queryProcessInst(querDTO);
        System.out.println(JSON.toJSONString(rr));
    }

    @Test
    public void testGetProcessInstById() {
        ProcessInstDTO pid = processInstService.getProcessInstById("14402501", true);
        System.out.println(JSON.toJSONString(pid));
    }
}
