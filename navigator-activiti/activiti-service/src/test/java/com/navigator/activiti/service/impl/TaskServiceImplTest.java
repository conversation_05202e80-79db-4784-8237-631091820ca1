package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.activiti.ActivitiApplication;
import com.navigator.activiti.dao.*;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveTaskInfoDTO;
import com.navigator.activiti.pojo.entity.ActHiActinstEntity;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.activiti.service.ProcessInstService;
import com.navigator.common.dto.QueryDTO;
import org.activiti.engine.identity.Group;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.activiti.engine.impl.persistence.entity.UserEntityImpl;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @date 2021/12/17 13:43
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivitiApplication.class)
//@Transactional
//@Rollback
public class TaskServiceImplTest {

    /**
     * 范围:bizService层和util层必须写单元测试并且做到单测通过100%和行覆盖100%，dal层或web层可以根据项目的进度进行把控不做强制要求
     * <p>
     * 规范:
     * AIR原则
     * A（automation，自动化）：单元测试应该是全自动执行的，并且非交互。单元测试中不准使用System.out来进行人工验证，必须使用断言来验证。
     * I （Independence，独立性）：单元测试用例之间决不能互相调用，也不能依赖执行的先后次序。
     * R（Repeatable，可重复）：单元测试是可以重复执行的，不能受到外界环境的影响。
     *
     * @throws Exception
     */

    /**
     * "message" {@link com.navigator.common.enums.ResultCodeEnum}
     *
     * expected 期待值
     *
     * actual 实际值
     *
     * delta 误差值
     */

    /**
     * 断言 {@link Assert}
     * Assert.assertTrue(true);  // assertFalse();
     * Assert.assertTrue("message",true);
     * <p>
     * Assert.assertEquals();  // assertNotEquals(); //  assertArrayEquals(); 参数："message",expected,actual,delta   equals
     * <p>
     * assertSame(); // assertNotSame();  "message",object,object  ==
     * <p>
     * Assert.assertNull();  // Assert.assertNotNull();  "message",object
     * <p>
     * <p>
     * assertThat(); "message",T,matcher
     *
     * @throws Exception
     */

    @Resource
    TaskServiceImpl taskServiceImpl;
    @MockBean
    EmployEntityManager employEntityManager;
    @Resource
    ActRuIdentitylinkDao actRuIdentitylinkDao;
    @Resource
    ActRuTaskDao actRuTaskDao;
    @Resource
    ProcessInstService processInstService;
    @Resource
    ActReNodeDao actReNodeDao;
    @Resource
    ActHiTaskinstDao actHiTaskinstDao;
    @Resource
    ActHiCommentDao actHiCommentDao;
    @Resource
    ActHiProcinstDao actHiProcinstDao;
    @Resource
    ActHiActinstDao actHiActinstDao;
    @Resource
    ActHiVarinstDao actHiVarinstDao;

//    @BeforeClass
//    public static void setUpClass() throws Exception {
//        // 全局只会执行一次，而且是第一个运行
//
//        System.out.println("setUpClass");
//    }

    @Before
    public void setUp() throws Exception {
        // 在测试方法运行之前运行
        List<Group> list = new ArrayList<>();

        Mockito.when(employEntityManager.findGroupsByUser(anyString())).thenReturn(list);

        UserEntity userEntity = new UserEntityImpl();
        userEntity.setFirstName("NEO");
        userEntity.setLastName("");
        Mockito.when(employEntityManager.findById(Mockito.anyString())).thenReturn(userEntity);


    }

    @After
    public void tearDown() throws Exception {
        // 在测试方法运行之后允许
        System.out.println("tearDown");
    }

//    @AfterClass
//    public static void tearDownClass() throws Exception {
//
//        // 全局只会执行一次，而且是最后一个运行
//        System.out.println("tearDownClass");
//    }

    @Ignore
    @Test
    public void ignore() {
        System.out.println("ignore");
    }

    @Test
    public void queryApproveTask() {
        QueryDTO<ApproveBO> queryDTO = new QueryDTO<>();
        ApproveBO approveBo = new ApproveBO();
        approveBo.setTaskStatus(0);
        approveBo.setUserId("1");
        approveBo.setSalesType(0);
        approveBo.setSubCategoryId(0);
        approveBo.setBizCode("PC202204072158082442");
        approveBo.setBizModule("");
        approveBo.setCustomerName("");
        approveBo.setProcessKey("");

        queryDTO.setCondition(approveBo);

        System.out.println(JSON.toJSONString(queryDTO));
        /*Result tt = taskServiceImpl.queryApproveTask(queryDTO);
        System.out.println(JSON.toJSONString(tt));*/


        List<ActHiActinstEntity> t1 = actHiActinstDao.queryActHiActinstByProcInstId("14467509");
        List<ActHiActinstEntity> t2 = actHiActinstDao.queryActHiActinstByProcInstId("14467509", true);
        Map<String, ActHiActinstEntity> t3 = actHiActinstDao.queryAutoActHiActinst("14467509");

        System.out.println(t3);
    }

    @Test
    public void queryHiTaskNotExist() {
        doReturn(new ActHiProcinstEntity().setBizId(1)).when(actHiProcinstDao).getActHiProcinstByBizCode(anyString(), any());
        doReturn(null).when(actHiActinstDao).queryActHiActinstByProcInstId(anyString());
        String bizCode = "SC202112151047272697";
        List<ApproveTaskInfoDTO> approveTaskInfoDTOList = taskServiceImpl.queryHiTaskByBizCode(bizCode);

        System.out.println(approveTaskInfoDTOList);
        assertEquals(approveTaskInfoDTOList.size(), 0);
    }

    @Test
    public void getApproveTaskInfo() {
        QueryDTO<ApproveBO> queryDTO = new QueryDTO<>();
        ApproveBO approveBO = new ApproveBO();
        List<Integer> roleIds = new ArrayList<>();
        roleIds.add(1);
        roleIds.add(100);
        roleIds.add(101);
        roleIds.add(102);
        roleIds.add(103);
        roleIds.add(1);
        approveBO.setUserRoleIdList(roleIds);
        approveBO.setSubCategoryId(11);
        queryDTO.setCondition( approveBO);

        System.out.println(approveBO.isSuper());

        IPage<ActHiTaskinstEntity> tt = actHiTaskinstDao.queryApprovingTask(queryDTO);
        System.out.println(tt.getRecords());

        return;


        /*given(actHiTaskinstDao.getActHiTaskinstById(anyString())).willReturn(new ActHiTaskinstEntity());
//        doReturn(new ActHiTaskinstEntity()).when(actHiTaskinstDao).getActHiTaskinstById(anyString());
        taskServiceImpl.getApproveTaskInfo(anyString());
        System.out.println("getApproveTaskInfo");*/
    }
}
