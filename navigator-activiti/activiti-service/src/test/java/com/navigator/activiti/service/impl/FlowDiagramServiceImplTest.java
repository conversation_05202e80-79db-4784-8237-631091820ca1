package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.common.dto.Result;
import org.activiti.engine.identity.Group;
import org.activiti.engine.impl.persistence.entity.GroupEntity;
import org.activiti.engine.impl.persistence.entity.GroupEntityImpl;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/12/24 11:41
 */
public class FlowDiagramServiceImplTest {

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void getResourceDiagramInputStream() {
    }

    @Test
    public void getDiagram() {
        List<RoleEntity> list = new ArrayList<>();
        RoleEntity r1 = new RoleEntity()
                .setName("r1")
                .setBelongCustomerId(2)
                .setCode("r111");
        list.add(r1);

        RoleEntity r2 = new RoleEntity()
                .setName("r2")
                .setBelongCustomerId(2)
                .setCode("r222");
        list.add(r2);

        Result<List<RoleEntity>> rtn = Result.success(list);

        Result rtn2 = Result.success(list);


        System.out.println(rtn);
        System.out.println(rtn2);

        List<RoleEntity> roleEntityList = new ArrayList<>();
        try {
            roleEntityList = JSON.parseObject(JSON.toJSONString(rtn.getData()), roleEntityList.getClass());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(roleEntityList);

        List<Group> list2 = new ArrayList<>();
        if (CollectionUtils.isEmpty(roleEntityList)) {
            list2 = Collections.emptyList();
        }
        List<Group> groupList = new ArrayList<>();
        for (Object obj : roleEntityList) {
            RoleEntity roleEntity = JSON.parseObject(JSON.toJSONString(obj), RoleEntity.class);
            GroupEntity groupEntity = new GroupEntityImpl();
            groupEntity.setId(roleEntity.getId().toString());
            groupEntity.setName(roleEntity.getName());
            groupEntity.setRevision(1);
            groupEntity.setType("");
            groupList.add(groupEntity);
        }

        System.out.println(list2);
        System.out.println(groupList);


    }
}
