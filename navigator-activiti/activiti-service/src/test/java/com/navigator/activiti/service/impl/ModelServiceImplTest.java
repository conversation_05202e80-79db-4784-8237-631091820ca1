package com.navigator.activiti.service.impl;

import com.navigator.activiti.ActivitiApplication;
import com.navigator.common.util.SpringContextUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2021/12/24 11:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivitiApplication.class)
public class ModelServiceImplTest {

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void createModel() {
    }

    @Test
    public void selectModel() {
    }

    @Test
    public void saveModel() {
    }

    @Test
    public void deployModel() {
    }

    @Test
    public void getEditorJson() {
    }

    @Test
    public void importModel() {
    }

    @Test
    public void exportBpmn() {
    }

    @Test
    public void exportPng() {
    }

    @Test
    public void updateModelAndSource() {

        String env = SpringContextUtil.getEnv();
        boolean isp = SpringContextUtil.isProdEnv();
        System.out.println(env);
        System.out.println(isp);
    }
}

