package com.navigator.activiti.utils;

import javax.xml.XMLConstants;
import javax.xml.stream.XMLInputFactory;

/**
 * @apiNote 此类直接从activiti-app下复制过来
 * <AUTHOR>
 */
public class XmlUtil {

	/**
	 * 'safe' is here reflecting:
	 * http://activiti.org/userguide/index.html#advanced.safe.bpmn.xml
	 */
	public static XMLInputFactory createSafeXmlInputFactory() {
		XMLInputFactory xif = XMLInputFactory.newInstance();
		//解决sonar xml相关安全性问题
		xif.setProperty(XMLConstants.ACCESS_EXTERNAL_DTD, "");
		xif.setProperty(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
		if (xif.isPropertySupported(XMLInputFactory.IS_REPLACING_ENTITY_REFERENCES)) {
			xif.setProperty(XMLInputFactory.IS_REPLACING_ENTITY_REFERENCES,
			        false);
		}

		if (xif.isPropertySupported(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES)) {
			xif.setProperty(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES,
			        false);
		}

		if (xif.isPropertySupported(XMLInputFactory.SUPPORT_DTD)) {
			xif.setProperty(XMLInputFactory.SUPPORT_DTD, false);
		}
		return xif;
	}

}
