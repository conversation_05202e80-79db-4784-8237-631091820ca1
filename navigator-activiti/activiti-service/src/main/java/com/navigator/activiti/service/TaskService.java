package com.navigator.activiti.service;

import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveTaskActInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveTaskInfoDTO;
import com.navigator.activiti.pojo.dto.LOAReportsExcelDTO;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/3 19:02
 */
public interface TaskService {

    /**
     * 我的待审批列表
     *
     * @param queryDTO
     * @return
     */
    Result queryApproveTask(QueryDTO<ApproveBO> queryDTO);


    /**
     * 根据流程业务code获取历史审批任务
     *
     * @param bizCode
     * @return
     */
    List<ApproveTaskInfoDTO> queryHiTaskByBizCode(String bizCode);

    /**
     * 根据流程业务code获取历史审批任务
     *
     * @param procInstId
     * @return
     */
    List<ApproveTaskActInfoDTO> queryHiTask(String procInstId);

    ApproveTaskActInfoDTO queryLatestTask(String procInstId);

    /**
     * 根据taskId获取任务详情
     *
     * @param taskId
     * @return
     */
    ApproveTaskInfoDTO getApproveTaskInfo(String taskId);

    /**
     * 根据流程业务code获取历史审批任务
     *
     * @param bizCode 流程业务code
     * @return
     */
    List<ActHiTaskinstEntity> getActHiTaskByBizCode(String bizCode);

    List<LOAReportsExcelDTO> downloadLOAReject() ;

}
