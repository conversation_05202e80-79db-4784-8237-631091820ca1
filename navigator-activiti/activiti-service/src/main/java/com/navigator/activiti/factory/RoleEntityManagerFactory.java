package com.navigator.activiti.factory;

import com.navigator.activiti.manager.RoleEntityManager;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.interceptor.SessionFactory;
import org.activiti.engine.impl.persistence.entity.GroupEntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/11/11 10:33
 */
@Service
public class RoleEntityManagerFactory implements SessionFactory {
    @Autowired
    private RoleEntityManager roleEntityManager;

    @Override
    public Class<?> getSessionType() {
        return GroupEntityManager.class;

    }

    @Override
    public Session openSession(CommandContext commandContext) {
        return roleEntityManager;
    }

    @Autowired
    public void setCustomUserEntityManager(RoleEntityManager roleEntityManager) {
        this.roleEntityManager = roleEntityManager;
    }
}
