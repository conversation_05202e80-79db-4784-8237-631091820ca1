package com.navigator.activiti.drools;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApproveRule implements Serializable {

    private static final long serialVersionUID = -6215513080886165878L;

    //合同类型
    private Integer contractType;

    //合同价格
    private Float contractPrice;

    //买方客户类型
    private Integer buyerType;

    //审批类型
    private Integer approveType;
}
