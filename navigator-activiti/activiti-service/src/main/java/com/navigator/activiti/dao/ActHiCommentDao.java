package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActHiCommentMapper;
import com.navigator.activiti.pojo.entity.ActHiCommentEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 历史审批意见表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiCommentDao extends BaseDaoImpl<ActHiCommentMapper, ActHiCommentEntity> {
    /**
     * 根据
     *
     * @param taskId
     * @return
     */
    public ActHiCommentEntity getActHiCommentByTaskId(String taskId) {
        List<ActHiCommentEntity> actHiCommentEntityList = this.list(
                Wrappers.<ActHiCommentEntity>lambdaQuery()
                        .eq(ActHiCommentEntity::getType,"AddComment")
                        .eq(ActHiCommentEntity::getTaskId, taskId)
        );
        return CollectionUtils.isEmpty(actHiCommentEntityList)?null : actHiCommentEntityList.get(0);
    }
}
