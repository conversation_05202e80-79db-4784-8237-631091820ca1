package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActHiIdentitylinkMapper;
import com.navigator.activiti.pojo.entity.ActHiIdentitylinkEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 历史流程人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiIdentitylinkDao extends BaseDaoImpl<ActHiIdentitylinkMapper, ActHiIdentitylinkEntity> implements ApplicationContextAware {

    private static ApplicationContext applicationContext;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ActHiIdentitylinkDao.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext != null ? applicationContext.getBean(clazz) : null;
    }

    public void deleteIdentitylinkByTask(String taskId) {
        this.remove(
                Wrappers.<ActHiIdentitylinkEntity>lambdaQuery()
                        .eq(ActHiIdentitylinkEntity::getTaskId, taskId)
                        .eq(ActHiIdentitylinkEntity::getType, "candidate")
        );
    }

    public ActHiIdentitylinkEntity queryTaskHiCandidate(String taskId) {
        List<ActHiIdentitylinkEntity> list = new ArrayList<>();
        list = this.baseMapper.selectList(
                Wrappers.<ActHiIdentitylinkEntity>lambdaQuery()
                        .eq(ActHiIdentitylinkEntity::getType, "candidate")
                        .eq(ActHiIdentitylinkEntity::getTaskId, taskId)
                        .orderByDesc(ActHiIdentitylinkEntity::getId)
        );

        return null == list || list.size() == 0 ? null : list.get(0);
    }
}
