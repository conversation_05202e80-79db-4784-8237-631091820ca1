package com.navigator.activiti.manager;

import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.persistence.entity.MembershipEntity;
import org.activiti.engine.impl.persistence.entity.MembershipEntityManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/11 10:33
 */
@Component
public class EmployRoleEntityManager implements MembershipEntityManager, Session {
    @Override
    public void flush() {

    }

    @Override
    public void close() {

    }

    @Override
    public void createMembership(String userId, String groupId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void deleteMembership(String userId, String groupId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void deleteMembershipByGroupId(String groupId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void deleteMembershipByUserId(String userId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public MembershipEntity create() {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public MembershipEntity findById(String entityId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void insert(MembershipEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void insert(MembershipEntity entity, boolean fireCreateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public MembershipEntity update(MembershipEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public MembershipEntity update(MembershipEntity entity, boolean fireUpdateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(String id) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(MembershipEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(MembershipEntity entity, boolean fireDeleteEvent) {
        throw new RuntimeException("this is not method!");
    }
}
