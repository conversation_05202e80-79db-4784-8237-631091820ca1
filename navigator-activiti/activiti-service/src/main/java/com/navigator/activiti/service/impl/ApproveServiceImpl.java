package com.navigator.activiti.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.activiti.dao.*;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.manager.RoleEntityManager;
import com.navigator.activiti.pojo.dto.ApproveBizInfoDTO;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.entity.*;
import com.navigator.activiti.pojo.enums.*;
import com.navigator.activiti.service.ApproveService;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.MyActivityException;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 审批参数统一为approveRuleValue
 * 节点状态统一为status(不必要)
 *
 * <AUTHOR>
 * @date 2021/9/27 9:56
 */
@Service
@Slf4j
public class ApproveServiceImpl implements ApproveService {

    @Resource
    RuntimeService runtimeService;
    @Resource
    TaskService taskService;
    @Resource
    ActHiCommentDao actHiCommentDao;
    @Resource
    ActRuTaskDao actRuTaskDao;
    @Resource
    IdentityService identityService;
    @Resource
    RepositoryService repositoryService;
    @Resource
    ActHiProcinstDao actHiProcinstDao;
    @Resource
    ActHiTaskinstDao actHiTaskinstDao;
    @Resource
    ActHiActinstDao actHiActinstDao;
    @Resource
    EmployEntityManager employEntityManager;
    @Resource
    RoleEntityManager roleEntityManager;
    @Resource
    ProcessEngine processEngine;
    @Resource
    ActHiIdentitylinkDao actHiIdentitylinkDao;
    @Resource
    ActRuIdentitylinkDao actRuIdentitylinkDao;
    @Resource
    ActCuProcDataDao actCuProcDataDao;
    @Resource
    ActReNodeDao actReNodeDao;
    @Resource
    private CategoryFacade categoryFacade;

    @Resource
    CustomerFacade customerFacade;
    @Resource
    FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    OperationLogFacade operationLogFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApproveResultDTO start(ApproveDTO approveDTO) {
        ApproveResultDTO approveResult = new ApproveResultDTO();


        this.checkStart(approveDTO);
        // 重新发起时 查询未废弃的
        ActHiProcinstEntity actHiProcinstByBizCode = actHiProcinstDao.getActHiProcinstByBizCode(approveDTO.getBizCode(), false);

        // BUGFIX：case-1002662 审批流程显示被驳回-权益变更除外，因为权益变更是批量提交，需要根据编号再次发起 Author: Mr 2024-07-16 Start
        if (null != actHiProcinstByBizCode && !ModuleTypeEnum.CONTRACT_EQUITY.getModule().equals(actHiProcinstByBizCode.getBizModule())) {
            // BUGFIX：case-1002662 审批流程显示被驳回-权益变更除外，因为权益变更是批量提交，需要根据编号再次发起 Author: Mr 2024-07-16 End
            // 废弃
            actHiProcinstByBizCode.setStatus(TaskStatusEnum.ABANDON.getValue());
            actHiProcinstDao.updateById(actHiProcinstByBizCode);
        }
        // 设置启动人
        identityService.setAuthenticatedUserId(approveDTO.getUserId());
        Map vars = new HashMap();
        // 记录业务数据
        if (null != approveDTO.getBizData()) {
            vars.put("bizData", approveDTO.getBizData());
        }
        if (null != approveDTO.getApproveRuleValue()) {
            vars.put("approveRuleValue", approveDTO.getApproveRuleValue());
        }
        if (StrUtil.isNotBlank(approveDTO.getRoleId())) {
            vars.put("roleId", approveDTO.getRoleId());
        }
        //启动流程
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(approveDTO.getProcessKey(), approveDTO.getBizCode(), vars);

        //获取流程实例
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(processInstance.getId());

        // 流程定义
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processInstance.getProcessDefinitionId()).singleResult();

        //获取当前任务  如果刚启动任务时就有两个任务 这里就会有问题
        ActRuTaskEntity actRuTaskEntity = actRuTaskDao.getActRuTaskByProcInstId(processInstance.getId());
        StringBuilder instName = new StringBuilder();
        instName.append(null == approveDTO.getCategory2Name() ? "" : approveDTO.getCategory2Name())
                .append(null == approveDTO.getSalesTypeEnum().getDescription() ? "" : approveDTO.getSalesTypeEnum().getDescription())
                .append("_")
                // case-1002824 LOA审批场景错误-审批详情修改 Author: Wan 2024-12-10 Start
                .append(null == approveDTO.getTtTypeEnum().getDesc() ? "" : approveDTO.getTtTypeEnum().getDesc())
                // case-1002824 LOA审批场景错误-审批详情修改 Author: Wan 2024-12-10 end
                .append("_")
                .append("审批");

        log.info("============ acti.contractCode:{}", JSON.toJSONString(approveDTO.getReferBizCode()));
        log.info("============acti.categoryEnum:{}", JSON.toJSONString(approveDTO.getCategory2Name()));

        //根据客户id查询处工厂信息
        CustomerEntity customerEntity = customerFacade.queryCustomerById(approveDTO.getBelongCustomerId());
        if (null != customerEntity) {
            //CustomerVO customerVO = JSON.parseObject(JSON.toJSONString(result.getData()), CustomerVO.class);
            FactoryEntity factoryEntity = factoryWarehouseFacade.getFactoryInfoById(customerEntity.getFactoryId());
            if (null != factoryEntity) {
                approveDTO.setFactoryId(factoryEntity.getId())
                        .setFactoryCode(factoryEntity.getCode());
            }
        }
        //补充审批规则信息 和业务信息
        actHiProcinstEntity.setApproveRuleValue(approveDTO.getApproveRuleValue())
                .setApproveRuleName(ContractApproveRuleEnum.getByValue(approveDTO.getApproveRuleValue()).getDescription())
                .setProcessKey(approveDTO.getProcessKey())                          //流程编号
                .setProcessName(processDefinition.getName())                        //流程名称
                .setProcessInstName(instName.toString())                //流程实例名称
                .setBizModule(approveDTO.getBizModule())                            //业务模块
                .setGoodsCategoryId(approveDTO.getCategory1())             //品类
                .setGoodsCategoryName(approveDTO.getCategory1Name())            //品类
                .setSubGoodsCategoryId(approveDTO.getCategory2())       //子品类
                .setSubGoodsCategoryName(approveDTO.getCategory2Name())      //子品类
                .setSalesType(approveDTO.getSalesTypeEnum().getValue())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTradeTypeValue(approveDTO.getContractTradeTypeEnum().getValue())
                .setTradeTypeName(approveDTO.getContractTradeTypeEnum().getDesc())
                .setTtType(approveDTO.getTtTypeEnum().getType())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setBizId(approveDTO.getBizId())
                .setBizCode(approveDTO.getBizCode())
                .setReferBizCode(approveDTO.getReferBizCode())
                .setReferBizCode2(approveDTO.getReferBizCode2())
                .setCustomerName(approveDTO.getCustomerName())
                .setCompanyId(approveDTO.getCompanyId())
                .setCompanyName(approveDTO.getCompanyName())
                .setCustomerId(approveDTO.getCustomerId())
                .setSupplierId(approveDTO.getSupplierId())
                .setSupplierName(approveDTO.getSupplierName())
                .setFactoryId(approveDTO.getFactoryId())
                .setFactoryCode(approveDTO.getFactoryCode())
                .setBelongCustomerId(approveDTO.getBelongCustomerId())
                .setDeleteReason(approveDTO.getApproveCause())
                .setCategory1(approveDTO.getCategory1())
                .setCategory2(approveDTO.getCategory2())
                .setBuCode(approveDTO.getBuCode())
                .setCategory3(approveDTO.getCategory3())
                .setSiteCode(approveDTO.getSiteCode())
                .setSiteName(approveDTO.getSiteName())
        ;

        approveResult.setTaskId("")
                .setTaskNodeName("流程启动")
                .setTaskActionName("提交审批")
                .setTaskApproveMemo(approveDTO.getMemo())
                .setTaskApproveUserName(approveDTO.getUserName())
                .setProcInstId(actHiProcinstEntity.getId())
                .setApproveRuleName(actHiProcinstEntity.getApproveRuleName());

        if (null != actRuTaskEntity) {
            actRuTaskEntity.setProcessKey(approveDTO.getProcessKey());
            actRuTaskEntity.setParentTaskId("");
            actRuTaskDao.updateById(actRuTaskEntity);

            //处理审批结果和流程实例的审批状态
            if (StrUtil.isNotBlank(actRuTaskEntity.getFormKey())) {
                String approveStatus = BizApproveStatusEnum.getDescByCode(actRuTaskEntity.getFormKey());
                actHiProcinstEntity.setApproveStatus(StringUtil.isEmpty(approveStatus) ? actRuTaskEntity.getName() + "中" : approveStatus);

                approveResult.setProcInstStatus(approveStatus);
            } else {
                approveResult.setProcInstStatus(actRuTaskEntity.getName() + "中");
            }

            actHiProcinstEntity.setStatus(TaskStatusEnum.APPROVING.getValue());
            approveResult.setApproveResult(ApproveResultEnum.APPROVING.getValue());

            //更新历史任务表
            ActHiTaskinstEntity actHiTaskinstEntity = udpateHiTask(actHiProcinstEntity, actRuTaskEntity.getId(), "");

            approveResult.setNextNodeName(actHiTaskinstEntity.getName())
                    .setNextNodeCandidateRoles(actHiTaskinstEntity.getTaskCandidateRoles())
                    .setTaskCandidateUsers(actHiTaskinstEntity.getTaskCandidateUsers());

            // 动态角色  角色id不为空并且未A签
            if (StrUtil.isNotBlank(approveDTO.getRoleId()) && NodeEnum.NODE_A.getNodeName().equals(actRuTaskEntity.getTaskDefKey())) {
                // 删除该任务的角色与用户
                actHiIdentitylinkDao.deleteIdentitylinkByTask(actRuTaskEntity.getId());
                actRuIdentitylinkDao.deleteIdentitylinkByTask(actRuTaskEntity.getId());

                // 新增该任务的角色
                ActRuIdentitylinkEntity actRuIdentitylinkEntity = new ActRuIdentitylinkEntity()
                        .setGroupId(approveDTO.getRoleId())
                        .setType("candidate")
                        .setTaskId(actRuTaskEntity.getId())
                        .setProcInstId(actHiProcinstEntity.getProcInstId());
                actRuIdentitylinkDao.save(actRuIdentitylinkEntity);

                ActHiIdentitylinkEntity actHiIdentitylinkEntity = new ActHiIdentitylinkEntity()
                        .setGroupId(approveDTO.getRoleId())
                        .setType("candidate")
                        .setTaskId(actRuTaskEntity.getId());
                actHiIdentitylinkDao.save(actHiIdentitylinkEntity);
            }
        } else {

            approveResult.setApproveResult(ApproveResultEnum.AGREE.getValue())
                    .setProcInstStatus(ApproveResultEnum.AGREE.getDesc());
            actHiProcinstEntity.setApproveStatus(ApproveResultEnum.AGREE.getDesc())
                    .setStatus(TaskStatusEnum.FINISHED.getValue());
        }

        //更新流程实例信息
        actHiProcinstDao.updateById(actHiProcinstEntity);

        List<ApproveBizInfoDTO> approveBizInfoDTOList = new ArrayList<>();
        try {
            approveBizInfoDTOList = JSON.parseArray(approveDTO.getBizData().toString(), ApproveBizInfoDTO.class);
        } catch (Exception e) {
            approveBizInfoDTOList = null;
            e.printStackTrace();
        }
        if (null != approveBizInfoDTOList) {
            List<ActCuProcDataEntity> actCuProcDataEntityList = new ArrayList<>();
            for (ApproveBizInfoDTO approveBizInfoDTO : approveBizInfoDTOList) {
                actCuProcDataEntityList.add(new ActCuProcDataEntity().setName(approveBizInfoDTO.getName())
                        .setDisplayName(approveBizInfoDTO.getDisplayName())
                        .setValue(approveBizInfoDTO.getValue())
                        .setIndexNo(approveBizInfoDTO.getIndex())
                        .setProcInstId(actHiProcinstEntity.getProcInstId())
                        .setGroupCode(approveDTO.getBizCode())
                        .setCreatedAt(new Date())
                        .setUpdatedAt(new Date()));
            }
            actCuProcDataDao.saveBatch(actCuProcDataEntityList);
        }

        approveDTO.setProcInstId(actHiProcinstEntity.getProcInstId());

        return approveResult.setProcInstId(actHiProcinstEntity.getProcInstId())
                .setBizCode("Approve")
                .setBizModule(actHiProcinstEntity.getTradeTypeName())
                .setBizId(actHiProcinstEntity.getBizId())
                .setTradeTypeName(actHiProcinstEntity.getTradeTypeName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApproveResultDTO approve(ApproveDTO approveDTO) {
        ApproveResultDTO approveResult = new ApproveResultDTO();

        ApproveActionEnum approveAction = ApproveActionEnum.getByValue(approveDTO.getActionValue());
        log.info("check_approve_question ticketCode:{},action:{},nodeName:{}  approve", approveDTO.getBizCode(), approveAction.getName(), approveDTO.getNodeName());
        approveDTO.setActionName(approveAction.getName());

        // 校验审批参数
        this.checkApprove(approveDTO);
        // 校验是否有权限
        //this.checkApprovePermission(approveDTO);
        // 获取当前任务
        ActRuTaskEntity actRuTaskEntity = actRuTaskDao.getActRuTaskById(approveDTO.getTaskId());
        // 驳回并且是点价或变更时  抛异常  不可驳回
        /*if (approveDTO.getActionValue().equals(ApproveActionEnum.REJECT.getValue()) && !actRuTaskEntity.getProcessKey().equals(ApproveProcessEnum.SC_ADD.getProcessKey())
                && !actRuTaskEntity.getProcessKey().equals(ApproveProcessEnum.PC_ADD.getProcessKey())) {
            throw new MyActivityException(ResultCodeEnum.CAN_NOT_REJECT);
        }*/
//        Task task = taskService.createTaskQuery().taskId(approveDTO.getTaskId()).singleResult();

        //获取流程实例
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(actRuTaskEntity.getProcInstId());

        Map<String, Object> vars = new HashMap<>();
        if (null != approveDTO.getActionValue()) {
            vars.put("approveRuleValue", ApproveActionEnum.AGREE.getValue() == approveDTO.getActionValue() ? actHiProcinstEntity.getApproveRuleValue() : approveDTO.getActionValue());
            // 设置全局变量 结果是流程线上的值 不是任务上的变量
            taskService.setVariables(approveDTO.getTaskId(), vars);
        }

        if (null == actRuTaskEntity.getAssignee()) {
            // 设置审批人
            taskService.setAssignee(approveDTO.getTaskId(), approveDTO.getUserId());
        }

        // 完成当前任务  true代表是当前任务变量 false是全局
        try {
            taskService.complete(approveDTO.getTaskId(), vars, true);
        } catch (Exception e) {
            log.error("approve complete error is {}", e);
            operationLogFacade.saveTraceLog(new TraceLogDTO(approveDTO.getTaskId(), "approveCompleteError", e.getMessage()));
            throw new MyActivityException(ResultCodeEnum.ABNORMAL_OPERATION);
        }

        if (StringUtil.isEmpty(approveDTO.getMemo())) {
            approveDTO.setMemo("");
        }

        ActHiTaskinstEntity currentTaskinstEntity = actHiTaskinstDao.getActHiTaskinstById(approveDTO.getTaskId());
        if (null != currentTaskinstEntity) {
            currentTaskinstEntity.setActionName(approveAction.getName());
            currentTaskinstEntity.setMemo(approveDTO.getMemo());
            actHiTaskinstDao.updateByTaskId(currentTaskinstEntity);
        }

        ActHiActinstEntity actHiActinstEntity = actHiActinstDao.getActHiActinst(actRuTaskEntity.getProcInstId(), actRuTaskEntity.getId());
        if (null != actHiActinstEntity) {
            actHiActinstEntity.setActionName(approveAction.getName());
            actHiActinstEntity.setMemo(approveDTO.getMemo());
            actHiActinstDao.updateActHiActinstEntity(actHiActinstEntity);
        }

        // 记录备注
        addComment(approveDTO.getTaskId(), approveDTO.getMemo(), approveDTO.getUserId(), actRuTaskEntity.getProcInstId());

        //同步信息到ApproveDTO中备用
        String nodeName = currentTaskinstEntity.getName();

        approveDTO.setBizCode(actHiProcinstEntity.getBizCode())
                .setReferBizCode(actHiProcinstEntity.getReferBizCode())
                .setNodeName(nodeName)
        ;

        approveResult.setProcInstId(actHiProcinstEntity.getId())
                .setApproveRuleName(actHiProcinstEntity.getApproveRuleName())
                .setTaskId(actRuTaskEntity.getId())
                .setTaskNodeName(actRuTaskEntity.getName())
                .setTaskActionName(approveAction.getName())
                .setTaskApproveMemo(approveDTO.getMemo())
                .setTaskApproveUserName("")//TODO NEO
        ;

        //获取下一个任务
        ActRuTaskEntity nextTask = actRuTaskDao.getActRuTaskByProcInstId(actRuTaskEntity.getProcInstId());
        if (null != nextTask) {

            String approveStatus = BizApproveStatusEnum.getDescByCode(nextTask.getFormKey());
            if (approveStatus.isEmpty()) {
                approveStatus = nextTask.getName();
            }

            String formKey = "";
            if (null == nextTask.getFormKey()) {
                ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(nextTask.getTaskDefKey(), nextTask.getProcessKey());
                if (null != actReNodeEntity) {
                    formKey = actReNodeEntity.getStatus();
                    nextTask.setFormKey(formKey);
                }
            }

            actHiProcinstEntity.setApproveStatus(approveStatus)
                    .setStatus(TaskStatusEnum.APPROVING.getValue());
            nextTask.setProcessKey(approveDTO.getProcessKey())
                    .setParentTaskId("");
            actRuTaskDao.updateById(nextTask);

            // 修改历史任务表
            ActHiTaskinstEntity nextTaskinstEntity = udpateHiTask(actHiProcinstEntity, nextTask.getId(), formKey);

            approveResult.setApproveResult(ApproveResultEnum.APPROVING.getValue())
                    .setProcInstStatus(approveStatus)
                    .setNextNodeName(nextTask.getName())
                    .setNextNodeCandidateRoles(nextTaskinstEntity.getTaskCandidateRoles())
                    .setTaskCandidateUsers(nextTaskinstEntity.getTaskCandidateUsers());
        } else {
            String procInstStatus = ApproveResultEnum.getByValue(approveDTO.getActionValue()).getDesc();
            actHiProcinstEntity.setApproveStatus(procInstStatus)
                    .setStatus(TaskStatusEnum.FINISHED.getValue());

            approveResult.setApproveResult(ApproveResultEnum.getByValue(approveDTO.getActionValue()).getValue())
                    .setProcInstStatus(procInstStatus);

        }

        actHiProcinstDao.updateById(actHiProcinstEntity);
        return approveResult.setProcInstId(actHiProcinstEntity.getProcInstId())
                .setBizCode("NODE_APPROVED")
                .setBizModule(actHiProcinstEntity.getTradeTypeName())
                .setBizId(actHiProcinstEntity.getBizId())
                .setTradeTypeName(actHiProcinstEntity.getTradeTypeName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(ApproveDTO approveDTO) {
        if (StrUtil.isBlank(approveDTO.getMemo())) {
            approveDTO.setMemo("");
        }

        //TODO NEO 要根据taskId进行取消

        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByBizCode(approveDTO.getBizCode(), false);
        if (!actHiProcinstEntity.getStartUserId().equals(approveDTO.getUserId())) {
            throw new MyActivityException(ResultCodeEnum.NO_PERMISSION);
        }

        if (actHiProcinstEntity.getStatus() == TaskStatusEnum.APPROVING.getValue()) {
            // 审批未完成
            runtimeService.deleteProcessInstance(actHiProcinstEntity.getId(), approveDTO.getMemo());
        } else {
            // 审批已完成 只改状态
            actHiProcinstEntity.setDeleteReason(approveDTO.getMemo());
        }

        actHiProcinstEntity.setApproveStatus(TaskStatusEnum.CANCEL.getDesc())
                .setStatus(TaskStatusEnum.CANCEL.getValue());
        actHiProcinstDao.updateById(actHiProcinstEntity);

        List<ActHiActinstEntity> actActinstList = actHiActinstDao.queryActHiActinstByProcInstId(actHiProcinstEntity.getProcInstId());
        if (null != actActinstList && actActinstList.size() > 1) {
            ActHiActinstEntity actHiActinst = actActinstList.get(actActinstList.size() - 1);
            if (null != actHiActinst) {
                actHiActinst.setActionName("TT撤回");
                actHiActinstDao.updateActHiActinstEntity(actHiActinst);
                String taskId = actHiActinst.getTaskId();
                ActHiTaskinstEntity actHiTaskinst = actHiTaskinstDao.getActHiTaskinstById(taskId);
                if (null != actHiTaskinst) {
                    actHiTaskinst.setActionName("TT撤回");
                    actHiTaskinstDao.updateByTaskId(actHiTaskinst);
                }
            }
        }

        return true;
    }


    @Override
    public void claimTask(String taskId, String userId) {
        taskService.claim(taskId, userId);
    }

    @Override
    public void unClaimTask(String taskId) {
        taskService.unclaim(taskId);
    }

    @Override
    public void delegate(String taskId, String delegateToUser) {
        taskService.delegateTask(taskId, delegateToUser);
    }

    @Override
    public void resolveTask(String taskId) {
        taskService.resolveTask(taskId);
    }

    @Override
    public void addStarter(ApproveProcessEnum approveProcessEnum, String userId, String roleId) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(approveProcessEnum.getProcessKey()).orderByProcessDefinitionVersion().desc().list().get(0);
        if (StrUtil.isNotBlank(userId)) {
            repositoryService.addCandidateStarterUser(processDefinition.getId(), userId);
        }
        if (StrUtil.isNotBlank(roleId)) {
            repositoryService.addCandidateStarterGroup(processDefinition.getId(), roleId);
        }
    }

    @Override
    public void deleteStater(ApproveProcessEnum approveProcessEnum, String userId, String roleId) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(approveProcessEnum.getProcessKey()).orderByProcessDefinitionVersion().desc().list().get(0);
        if (StrUtil.isNotBlank(userId)) {
            repositoryService.deleteCandidateStarterUser(processDefinition.getId(), userId);
        }
        if (StrUtil.isNotBlank(roleId)) {
            repositoryService.deleteCandidateStarterGroup(processDefinition.getId(), roleId);
        }
    }

    /**
     * 校验启动参数
     *
     * @param approveDTO
     */
    private void checkStart(ApproveDTO approveDTO) {
        if (StrUtil.isEmpty(approveDTO.getProcessKey())) {
            throw new MyActivityException(ResultCodeEnum.PROCKEY_EMPTY);
        }
        if (StrUtil.isEmpty(approveDTO.getBizCode())) {
            throw new MyActivityException(ResultCodeEnum.BUSINESS_KEY_EMPTY);
        }
        if (StrUtil.isEmpty(approveDTO.getUserId())) {
            throw new MyActivityException(ResultCodeEnum.USER_ID_EMPTY);
        }
        if (null == approveDTO.getApproveRuleValue()) {
            throw new MyActivityException(ResultCodeEnum.APPROVE_RULE_VALUE_EMPTY);
        }
        if (StrUtil.isEmpty(approveDTO.getBizModule())) {
            throw new MyActivityException(ResultCodeEnum.BIZ_MODULE_EMPTY);
        }
        if (null == approveDTO.getBizId()) {
            throw new MyActivityException(ResultCodeEnum.BIZ_ID_EMPTY);
        }

        // 校验当前用户是否可以启动流程
        List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().processDefinitionKey(approveDTO.getProcessKey()).orderByProcessDefinitionVersion().desc().list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        ProcessDefinition processDefinition = list.get(0);

        List<IdentityLink> identityLinkList = repositoryService.getIdentityLinksForProcessDefinition(processDefinition.getId());
        // 未设置启动人就不做权限判断
        if (CollectionUtil.isEmpty(identityLinkList)) {
            return;
        }

        // 设置了启动人再做权限判断
        for (IdentityLink identityLink : identityLinkList) {
            if (StrUtil.isNotBlank(identityLink.getUserId())) {
                if (identityLink.getUserId().equals(approveDTO.getUserId())) {
                    return;
                }
            } else {
                if (employEntityManager.getUserListByRoleIds(Arrays.asList(identityLink.getGroupId())).stream().anyMatch(employEntity -> employEntity.getId().toString().equals(approveDTO.getUserId()))) {
                    return;
                }
            }
        }
        throw new MyActivityException(ResultCodeEnum.NO_PERMISSION);

    }

    /**
     * 校验审批参数
     *
     * @param approveDTO
     */
    private void checkApprove(ApproveDTO approveDTO) {
        if (StrUtil.isEmpty(approveDTO.getTaskId())) {
            throw new MyActivityException(ResultCodeEnum.TASK_ID_EMPTY);
        }
        if (null == approveDTO.getActionValue()) {
            throw new MyActivityException(ResultCodeEnum.RESULT_EMPTY);
        }
        if (StrUtil.isEmpty(approveDTO.getUserId())) {
            throw new MyActivityException(ResultCodeEnum.USER_ID_EMPTY);
        }
        if (StrUtil.isEmpty(approveDTO.getProcessKey())) {
            throw new MyActivityException(ResultCodeEnum.PROCKEY_EMPTY);
        }
    }

    /**
     * 校验当前审批人是否有权限
     *
     * @param approveDTO
     */
    private void checkApprovePermission(ApproveDTO approveDTO) {
        Task task = taskService.createTaskQuery().taskId(approveDTO.getTaskId()).taskCandidateOrAssigned(approveDTO.getUserId()).singleResult();
        if (null == task) {
            throw new MyActivityException(ResultCodeEnum.NO_PERMISSION);
        }
    }

    /**
     * 添加评论、备注
     *
     * @param taskId
     * @param remark
     * @param userId
     */
    private void addComment(String taskId, String remark, String userId, String procInstId) {

//        // type 值为下列内容中的一种：AddUserLink、DeleteUserLink、AddGroupLink、DeleteGroupLink、AddComment、AddAttachment、DeleteAttachment
        ActHiCommentEntity actHiCommentEntity = new ActHiCommentEntity();
        actHiCommentEntity.setType("AddComment")
                .setTime(DateTime.now().toTimestamp())
                .setUserId(userId)
                .setTaskId(taskId)
                .setProcInstId(procInstId)
                .setAction("AddComment")
                .setMessage(remark);
        actHiCommentDao.save(actHiCommentEntity);

//        ActivitiRule activitiRule = new ActivitiRule();
//        processEngine.getRepositoryService().addCandidateStarterUser();
    }


    public ActHiTaskinstEntity udpateHiTask(ActHiProcinstEntity actHiProcinstEntity, String taskId, String formKey) {
        ActHiTaskinstEntity actHiTaskinstEntity = actHiTaskinstDao.getActHiTaskinstById(taskId);

        if (null == actHiTaskinstEntity) {
            return new ActHiTaskinstEntity();
        }

        actHiTaskinstEntity.setCategoryId(actHiProcinstEntity.getSubGoodsCategoryId())
                .setCategory(actHiProcinstEntity.getSubGoodsCategoryName())
                .setSalesType(actHiProcinstEntity.getSalesType())
                .setTradeTypeValue(actHiProcinstEntity.getTradeTypeValue())
                .setBelongCustomerId(actHiProcinstEntity.getBelongCustomerId())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtType(actHiProcinstEntity.getTtType())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                .setProcessKey(actHiProcinstEntity.getProcessKey())
                .setCustomerName(actHiProcinstEntity.getCustomerName())
                .setSupplierId(actHiProcinstEntity.getSupplierId())
                .setSupplierName(actHiProcinstEntity.getSupplierName())
                .setCustomerId(actHiProcinstEntity.getCustomerId())
                .setCompanyId(actHiProcinstEntity.getCompanyId())
                .setCompanyName(actHiProcinstEntity.getCompanyName())
                .setFactoryId(actHiProcinstEntity.getFactoryId())
                .setFactoryCode(actHiProcinstEntity.getFactoryCode())
                .setCategory1(actHiProcinstEntity.getCategory1())
                .setCategory2(actHiProcinstEntity.getCategory2())
                .setCategory3(actHiProcinstEntity.getCategory3())
                .setBuCode(actHiProcinstEntity.getBuCode())
                .setSiteCode(actHiProcinstEntity.getSiteCode())
                .setSiteName(actHiProcinstEntity.getSiteName())
        ;
        StringBuilder bizCode = new StringBuilder();
        bizCode.append(actHiProcinstEntity.getBizCode());
        if (StringUtil.isNotEmpty(actHiProcinstEntity.getReferBizCode())) {
            bizCode.append(";")
                    .append(actHiProcinstEntity.getReferBizCode());
        }
        if (StringUtil.isNotEmpty(actHiProcinstEntity.getReferBizCode2())) {
            bizCode.append(";")
                    .append(actHiProcinstEntity.getReferBizCode2());
        }
        actHiTaskinstEntity.setBizCode(bizCode.toString());

        if (StringUtil.isEmpty(actHiTaskinstEntity.getFormKey())) {
            actHiTaskinstEntity.setFormKey(formKey);
        }

        String nodeCandidateRoles = "";
        String taskCandidateRoles = "";
        String taskCandidateUsers = "";

        List<ActRuIdentitylinkEntity> actRuIdentitylinkEntityList = actRuIdentitylinkDao.queryTaskRuCandidate(taskId);
        if (CommonListUtil.notNullOrEmpty(actRuIdentitylinkEntityList)) {

            for (ActRuIdentitylinkEntity actRuIdentitylinkEntity : actRuIdentitylinkEntityList) {
                nodeCandidateRoles = nodeCandidateRoles + actRuIdentitylinkEntity.getGroupId() + ",";
            }

            taskCandidateRoles = roleEntityManager.getTaskRoleIdList(nodeCandidateRoles, actHiProcinstEntity.getSubGoodsCategoryId(), actHiProcinstEntity.getSiteCode());

            taskCandidateUsers = roleEntityManager.getRoleEmployList(taskCandidateRoles);
        }

        actHiTaskinstEntity.setNodeCandidateRoles(nodeCandidateRoles)
                .setTaskCandidateRoles(taskCandidateRoles)
                .setTaskCandidateUsers(taskCandidateUsers);

        actHiTaskinstDao.updateByTaskId(actHiTaskinstEntity);

        ActHiActinstEntity actHiActinstEntity = actHiActinstDao.getActHiActinst(actHiProcinstEntity.getProcInstId(), taskId);
        actHiActinstEntity.setProcessKey(actHiProcinstEntity.getProcessKey())
                .setNodeCandidateRoles(actHiTaskinstEntity.getNodeCandidateRoles())
                .setTaskCandidateRoles(actHiTaskinstEntity.getTaskCandidateRoles())
                .setTaskCandidateUsers(actHiTaskinstEntity.getTaskCandidateUsers())
                .setCategoryId(actHiProcinstEntity.getSubGoodsCategoryId())
                .setBelongCustomerId(actHiProcinstEntity.getBelongCustomerId())
                .setBizCode(bizCode.toString())
                .setCustomerName(actHiProcinstEntity.getCustomerName())
                .setCustomerId(actHiProcinstEntity.getCustomerId())
                .setSupplierId(actHiProcinstEntity.getSupplierId())
                .setSupplierName(actHiProcinstEntity.getSupplierName())
                .setSalesType(actHiProcinstEntity.getSalesType())
                .setCompanyId(actHiProcinstEntity.getCompanyId())
                .setCompanyName(actHiProcinstEntity.getCompanyName())
                .setFactoryId(actHiProcinstEntity.getFactoryId())
                .setFactoryCode(actHiProcinstEntity.getFactoryCode())
                .setTradeTypeValue(actHiProcinstEntity.getTradeTypeValue())
                .setCategory1(actHiProcinstEntity.getCategory1())
                .setCategory2(actHiProcinstEntity.getCategory2())
                .setCategory3(actHiProcinstEntity.getCategory3())
                .setBuCode(actHiProcinstEntity.getBuCode())
                .setSiteCode(actHiProcinstEntity.getSiteCode())
                .setSiteName(actHiProcinstEntity.getSiteName())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTradeTypeValue(actHiProcinstEntity.getTradeTypeValue())
                .setTtType(actHiProcinstEntity.getTtType())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
        ;
        actHiActinstDao.updateActHiActinstEntity(actHiActinstEntity);

        return actHiTaskinstEntity;

    }

    public void udpateHiTaskCommonInfo() {
        List<ActHiTaskinstEntity> list = actHiTaskinstDao.queryAllTaskInst();
        for (ActHiTaskinstEntity actHiTaskinstEntity : list) {
            udpateHiTaskCommonInfo(actHiTaskinstEntity);
        }
    }

    public void udpateHiTaskCommonInfo(ActHiTaskinstEntity actHiTaskinstEntity) {

        if (null == actHiTaskinstEntity) {
            return;
        }

        String procInstId = actHiTaskinstEntity.getProcInstId();
        String taskId = actHiTaskinstEntity.getId();

        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(procInstId);
        if (null == actHiProcinstEntity) {
            return;
        }


        actHiTaskinstEntity.setCategoryId(actHiProcinstEntity.getSubGoodsCategoryId())
                .setCategory(actHiProcinstEntity.getSubGoodsCategoryName())
                .setSalesType(actHiProcinstEntity.getSalesType())
                .setTradeTypeValue(actHiProcinstEntity.getTradeTypeValue())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .setTtType(actHiProcinstEntity.getTtType())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 edn
                .setBelongCustomerId(actHiProcinstEntity.getBelongCustomerId())
                .setProcessKey(actHiProcinstEntity.getProcessKey())
                .setCustomerName(actHiProcinstEntity.getCustomerName())
                .setCompanyId(actHiProcinstEntity.getCompanyId())
                .setCompanyName(actHiProcinstEntity.getCompanyName())
                .setFactoryId(actHiProcinstEntity.getFactoryId())
                .setFactoryCode(actHiProcinstEntity.getFactoryCode())
                .setCategory1(actHiProcinstEntity.getCategory1())
                .setCategory2(actHiProcinstEntity.getCategory2())
                .setCategory3(actHiProcinstEntity.getCategory3())
                .setBuCode(actHiProcinstEntity.getBuCode())
                .setSiteCode(actHiProcinstEntity.getSiteCode())
                .setSiteName(actHiProcinstEntity.getSiteName())
        ;
        StringBuilder bizCode = new StringBuilder();
        bizCode.append(actHiProcinstEntity.getBizCode());
        if (StringUtil.isNotEmpty(actHiProcinstEntity.getReferBizCode())) {
            bizCode.append(";")
                    .append(actHiProcinstEntity.getReferBizCode());
        }
        if (StringUtil.isNotEmpty(actHiProcinstEntity.getReferBizCode2())) {
            bizCode.append(";")
                    .append(actHiProcinstEntity.getReferBizCode2());
        }
        actHiTaskinstEntity.setBizCode(bizCode.toString());

        String nodeCandidateRoles = "";
        String taskCandidateRoles = "";
        String taskCandidateUsers = "";

        List<ActRuIdentitylinkEntity> actRuIdentitylinkEntityList = actRuIdentitylinkDao.queryTaskRuCandidate(actHiTaskinstEntity.getId());
        if (CommonListUtil.notNullOrEmpty(actRuIdentitylinkEntityList)) {

            for (ActRuIdentitylinkEntity actRuIdentitylinkEntity : actRuIdentitylinkEntityList) {
                nodeCandidateRoles = nodeCandidateRoles + actRuIdentitylinkEntity.getGroupId() + ",";
            }

            taskCandidateRoles = roleEntityManager.getTaskRoleIdList(nodeCandidateRoles, actHiProcinstEntity.getSubGoodsCategoryId(), actHiProcinstEntity.getSiteCode());

            taskCandidateUsers = roleEntityManager.getRoleEmployList(taskCandidateRoles);
        }

        actHiTaskinstEntity.setNodeCandidateRoles(nodeCandidateRoles)
                .setTaskCandidateRoles(taskCandidateRoles)
                .setTaskCandidateUsers(taskCandidateUsers);

    }

    private void recordOperationLogDetail() {

    }


}
