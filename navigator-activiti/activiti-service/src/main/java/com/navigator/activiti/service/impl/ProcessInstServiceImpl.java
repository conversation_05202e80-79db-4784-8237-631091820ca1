package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.activiti.dao.ActCuProcDataDao;
import com.navigator.activiti.dao.ActHiProcinstDao;
import com.navigator.activiti.dao.ActHiTaskinstDao;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ApproveTaskActInfoDTO;
import com.navigator.activiti.pojo.dto.EquityChangeInfoDTO;
import com.navigator.activiti.pojo.dto.ProcessInstDTO;
import com.navigator.activiti.pojo.entity.ActCuProcDataEntity;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.activiti.service.ProcessInstService;
import com.navigator.activiti.service.TaskService;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import org.activiti.engine.HistoryService;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/6 16:05
 */
@Service
public class ProcessInstServiceImpl implements ProcessInstService {

    @Resource
    ActHiProcinstDao actHiProcinstDao;
    @Resource
    ActHiTaskinstDao actHiTaskinstDao;
    @Resource
    EmployEntityManager employEntityManager;
    @Resource
    HistoryService historyService;
    @Resource
    TaskService taskService;
    @Resource
    ActCuProcDataDao actCuProcDataDao;


    public ProcessInstDTO getProcessInstById(String procInstId) {
        return getProcessInstById(procInstId, true);
    }

    @Override
    public ProcessInstDTO getProcessInstDetailByTaskId(String taskId) {
        ActHiTaskinstEntity actHiTaskinstEntity = actHiTaskinstDao.getActHiTaskinstById(taskId);
        return getProcessInstById(actHiTaskinstEntity.getProcInstId());
    }

    @Override
    public Result queryProcessInst(QueryDTO<ApproveBO> queryDTO) {

        IPage<ActHiProcinstEntity> actHiProcinstEntityList = actHiProcinstDao.queryProcessInst(queryDTO);
        List<ProcessInstDTO> processInstDTOList = actHiProcinstEntityList.getRecords().stream().map(actHiProcinstEntity -> {
            ProcessInstDTO processInstDTO = this.getBasicsProcInst(actHiProcinstEntity);

            ApproveTaskActInfoDTO approveTaskActInfoDTO = taskService.queryLatestTask(actHiProcinstEntity.getProcInstId());
            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
            if (null != approveTaskActInfoDTO) {
                processInstDTO.setApprovingUser(approveTaskActInfoDTO.getApproveUserName());
                processInstDTO.setLatestApproveTaskActInfo(approveTaskActInfoDTO);
                approveTaskActInfoDTO.setTtTypeName(TTTypeEnum.getByType(approveTaskActInfoDTO.getTtType()).getDesc());
            }
            processInstDTO.setTtTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc());
            processInstDTO.setTradeTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc());
            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
            return processInstDTO;
        }).collect(Collectors.toList());

        return Result.page(actHiProcinstEntityList, processInstDTOList);

    }

    @Override
    public List<String> queryProcessInstIdList(QueryDTO<ApproveBO> queryDTO) {
        return actHiProcinstDao.queryProcessInstIdList(queryDTO);
    }

    @Override
    public ActHiProcinstEntity getActHiProcinstByBizCode(String bizCode) {
        return actHiProcinstDao.getActHiProcinstByBizCode(bizCode, false);
    }

    @Override
    public ProcessInstDTO getProcessInstById(String procInstId, boolean loadAll) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();

        // 流程实例信息
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(procInstId);

        if (null == actHiProcinstEntity) {
            return null;
        }
        processInstDTO = getBasicsProcInst(actHiProcinstEntity);

        // 获取业务数据
        UserEntity userEntity = employEntityManager.findById(processInstDTO.getStartUserId());
        processInstDTO.setStartUserName(null == userEntity ? "" : userEntity.getFirstName());

        //业务数据
        List<ActCuProcDataEntity> actCuProcDataEntityList = actCuProcDataDao.getProcDataList(procInstId);
        if (null == actCuProcDataEntityList || actCuProcDataEntityList.isEmpty()) {
            actCuProcDataEntityList = buildCommonProcDataEntityList(processInstDTO);
        }
        processInstDTO.setApproveBizInfoList(actCuProcDataEntityList);

        // 权益变更业务数据
        processInstDTO.setEquityChangeBizInfoList(getEquityChangeBizInfo(actCuProcDataEntityList));
        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
        processInstDTO.setTtTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc());
        processInstDTO.setTradeTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getCode());
        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

        //最后的任务信息
        ApproveTaskActInfoDTO approveTaskActInfoDTO = taskService.queryLatestTask(procInstId);

        if (null != approveTaskActInfoDTO) {
            processInstDTO.setApprovingUser(approveTaskActInfoDTO.getApproveUserName());
            processInstDTO.setLatestApproveTaskActInfo(approveTaskActInfoDTO);
        }

        if (loadAll) {
            // 获取审批历史信息
            processInstDTO.setApproveTaskInfoList(taskService.queryHiTask(processInstDTO.getProcInstId()));
        }

        return processInstDTO;
    }

    /**
     * 获取权益变更业务数据
     *
     * @param actCuProcDataEntityList
     * @return
     */
    private List<EquityChangeInfoDTO> getEquityChangeBizInfo(List<ActCuProcDataEntity> actCuProcDataEntityList) {
        List<EquityChangeInfoDTO> changeInfoDTOList = new ArrayList<>();
        if (actCuProcDataEntityList.size() == 1 && actCuProcDataEntityList.get(0).getName().equals("contractEquityChange")) {
            ActCuProcDataEntity actCuProcDataEntity = actCuProcDataEntityList.get(0);
            List<ActCuProcDataEntity> procDataEntityList = actCuProcDataDao.getProcDataListByGroupCode(actCuProcDataEntity.getGroupCode());
            for (ActCuProcDataEntity cuProcDataEntity : procDataEntityList) {
                EquityChangeInfoDTO equityChangeInfoDTO = JSON.parseObject(cuProcDataEntity.getValue(), EquityChangeInfoDTO.class);
                equityChangeInfoDTO
                        .setChangeAbleTransferTimes(getChangeTimes(equityChangeInfoDTO.getBeforeAbleTransferTimes(), equityChangeInfoDTO.getAfterAbleTransferTimes()))
                        .setChangeTransferredTimes(getChangeTimes(equityChangeInfoDTO.getBeforeTransferredTimes(), equityChangeInfoDTO.getAfterTransferredTimes()))
                        .setChangeAbleReversePriceTimes(getChangeTimes(equityChangeInfoDTO.getBeforeAbleReversePriceTimes(), equityChangeInfoDTO.getAfterAbleReversePriceTimes()))
                        .setChangeReversedPriceTimes(getChangeTimes(equityChangeInfoDTO.getBeforeReversedPriceTimes(), equityChangeInfoDTO.getAfterReversedPriceTimes()));

                changeInfoDTOList.add(equityChangeInfoDTO);
            }
            return changeInfoDTOList;
        }
        return null;
    }

    /**
     * 获取变更次数
     *
     * @param beforeTimes 变更前次数
     * @param afterTimes  变更后次数
     * @return
     */
    private String getChangeTimes(Integer beforeTimes, Integer afterTimes) {
        return afterTimes - beforeTimes > 0 ? "+" + (afterTimes - beforeTimes) :
                afterTimes - beforeTimes < 0 ? (afterTimes - beforeTimes) + "" : "0";
    }

    private ProcessInstDTO getBasicsProcInst(ActHiProcinstEntity actHiProcinstEntity) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();
        BeanConvertUtils.copy(processInstDTO, actHiProcinstEntity);
        return processInstDTO;
    }

    private List<ActCuProcDataEntity> buildCommonProcDataEntityList(ProcessInstDTO processInstDTO) {
        List<ActCuProcDataEntity> actCuProcDataEntityList = new ArrayList<>();
        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(1)
                .setName("salesType")
                .setDisplayName("采/销")
                .setValue(processInstDTO.getSalesTypeName() + "合同")
        );

        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(2)
                .setName("subCategoryName")
                .setDisplayName("品种")
                .setValue(processInstDTO.getSubGoodsCategoryName())
        );

        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(3)
                .setName("tradeTypeName")
                .setDisplayName("交易类型")
                .setValue(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc())
        );

        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(4)
                .setName("contractTypeName")
                .setDisplayName("合同类型")
                .setValue("")
        );

        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(5)
                .setName("contractCode")
                .setDisplayName("合同编号")
                .setValue(processInstDTO.getReferBizCode())
        );
        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(6)
                .setName("customerName")
                .setDisplayName("客户名称")
                .setValue(processInstDTO.getCustomerName())
        );
        actCuProcDataEntityList.add(new ActCuProcDataEntity()
                .setIndexNo(7)
                .setName("bizDetailDescription")
                .setDisplayName("业务详情")
                .setValue("默认的业务详情")
        );

        return actCuProcDataEntityList;
    }
}
