package com.navigator.activiti.facade.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.navigator.activiti.facade.ModelFacade;
import com.navigator.activiti.service.ModelService;
import org.activiti.engine.repository.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 流程模型Controller
 * <p>
 * Created  by  jinboYu  on  2019/3/28
 */
@RestController
public class ModelFacadeImpl implements ModelFacade {

    @Autowired
    private ModelService modelService;


    /**
     * 创建流程模型
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @Override
    public void createModel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        modelService.createModel(request, response);
    }


    @Override
    public ObjectNode getEditorJson(@PathVariable String modelId) throws Exception {
        return modelService.getEditorJson(modelId);
    }

    /**
     * 获取流程模型,带分页
     *
     * @param pageNum  当前页
     * @param pageSize
     * @return
     * @throws Exception
     */
    @Override
    public List<Model> selectModel(@PathVariable int pageNum, @PathVariable int pageSize) throws Exception {
        return modelService.selectModel(pageNum, pageSize);
    }

    /**
     * 保存流程模型
     *
     * @param modelId
     * @param name
     * @param description
     * @param json_xml
     * @param svg_xml
     * @return
     * @throws Exception
     */
    @Override
    public int saveModel(@PathVariable String modelId, String name, String description, String json_xml, String svg_xml) throws Exception {
        return modelService.saveModel(modelId, name, description, json_xml, svg_xml);
    }

    /**
     * 部署流程模型
     *
     * @param modelId
     * @return
     * @throws Exception
     */
    @Override
    public int deployModel(@PathVariable String modelId) throws Exception {
        return modelService.deployModel(modelId);
    }

    @Override
    public int deployModelByProcessKey(String processKey) throws Exception {
        return modelService.deployModelByProcessKey(processKey);
    }

    /**
     * 部署所有流程图
     *
     * @return
     * @throws Exception
     */
    @Override
    public int deployModelAll() throws Exception {
        return modelService.deployModelAll();
    }

    @Override
    public void exportBpmn(@RequestParam String procInstId, HttpServletResponse response) {
        modelService.exportBpmn(procInstId, response);
    }

    @Override
    public void exportPng(String procInstId, HttpServletResponse response) throws IOException {
        modelService.exportPng(procInstId, response);
    }

    @Override
    public Boolean importModel(@RequestBody MultipartFile file) {
        return modelService.importModel(file);
    }

}
