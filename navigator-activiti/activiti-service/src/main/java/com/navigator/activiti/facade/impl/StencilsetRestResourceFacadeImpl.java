/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.navigator.activiti.facade.impl;

import com.navigator.activiti.facade.StencilsetRestResourceFacade;
import org.activiti.engine.ActivitiException;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;

/**
 * <AUTHOR> Rademakers
 */
@RestController
public class StencilsetRestResourceFacadeImpl implements StencilsetRestResourceFacade {

//  源码
//  @RequestMapping(value="/editor/stencilset", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
//  public @ResponseBody String getStencilset() {
//    InputStream stencilsetStream = this.getClass().getClassLoader().getResourceAsStream("stencilset.json");
//    try {
//      return IOUtils.toString(stencilsetStream, "utf-8");
//    } catch (Exception e) {
//      throw new ActivitiException("Error while loading stencil set", e);
//    }
//  }

    //修改源码解决Activiti6.0.0及以上版本与activiti-modeler冲突
    @Override
    public @ResponseBody
    String getStencilset() {
        //stencilset.json为Model中的工具栏的名称字符，这里在resources下面查找
        InputStream stencilsetStream = this.getClass().getClassLoader().getResourceAsStream("stencilset.json");
        try {
            return IOUtils.toString(stencilsetStream, "utf-8");
        } catch (Exception e) {
            throw new ActivitiException("Error while loading stencil set", e);
        }
    }
}
