package com.navigator.activiti.listener;

import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;

/**
 * 环节监听器-任务监听器（只有用户活动类型有）
 * <p>
 * 用于监听任务，任务监听器生命周期对应四个事件，分别是assignment（分配）、create（创建）、complete（）、delete。
 * <p>
 * 当流程引擎触发这四种事件类型时，对应的任务监听器会捕获其事件类型，再按照监听器的处理逻辑进行处理。
 * <p>
 * https://blog.csdn.net/qq_29595629/article/details/114377768
 *
 * <AUTHOR>
 * @date 2021/9/24 16:17
 */
@Slf4j
public class ApproveListener implements TaskListener {

    ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Override
    public void notify(DelegateTask delegateTask) {
//        WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();

        log.info("============TaskListener start============");
        String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
        String eventName = delegateTask.getEventName();
        log.info("事件名称:{}", eventName);
        log.info("taskDefinitionKey:{}", taskDefinitionKey);
        log.info("delegateTask:{}", delegateTask);
        log.info("============TaskListener end============");


    }

}
