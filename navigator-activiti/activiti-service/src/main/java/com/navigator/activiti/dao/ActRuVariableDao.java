package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActRuVariableMapper;
import com.navigator.activiti.pojo.entity.ActRuVariableEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.List;

/**
 * <p>
 * 运行时流程变量数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActRuVariableDao extends BaseDaoImpl<ActRuVariableMapper, ActRuVariableEntity> implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public ActRuVariableEntity getActRuVariableByNameAndProcInstId(String name, String procInstId) {

        List<ActRuVariableEntity> actRuVariableEntities = this.getBaseMapper().selectList(Wrappers.<ActRuVariableEntity>lambdaQuery()
                .eq(ActRuVariableEntity::getName, name)
                .eq(ActRuVariableEntity::getProcInstId, procInstId));

        return actRuVariableEntities.isEmpty() ? null : actRuVariableEntities.get(0);

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ActRuVariableDao.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext != null ? applicationContext.getBean(clazz) : null;
    }
}
