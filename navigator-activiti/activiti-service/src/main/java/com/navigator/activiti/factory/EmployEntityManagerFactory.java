package com.navigator.activiti.factory;

import com.navigator.activiti.manager.EmployEntityManager;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.interceptor.SessionFactory;
import org.activiti.engine.impl.persistence.entity.UserEntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/11/10 15:59
 */
@Service
public class EmployEntityManagerFactory implements SessionFactory {
    @Autowired
    private EmployEntityManager employEntityManager;

    @Override
    public Class<?> getSessionType() {
        return UserEntityManager.class;
    }

    @Override
    public Session openSession(CommandContext commandContext) {
        return employEntityManager;
    }

    @Autowired
    public void setCustomUserEntityManager(EmployEntityManager employEntityManager) {
        this.employEntityManager = employEntityManager;
    }
}
