package com.navigator.activiti.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.activiti.mapper.ActHiProcinstMapper;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.activiti.pojo.enums.TaskStatusEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 历史流程实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiProcinstDao extends BaseDaoImpl<ActHiProcinstMapper, ActHiProcinstEntity> {

    public ActHiProcinstEntity getActHiProcinstByProcInstId(String processInstId) {
        List<ActHiProcinstEntity> actHiProcinstEntities = this.list(Wrappers.<ActHiProcinstEntity>lambdaQuery()
                .eq(ActHiProcinstEntity::getProcInstId, processInstId));
        return actHiProcinstEntities.isEmpty() ? null : actHiProcinstEntities.get(0);
    }

    /**
     * 分页查询流程实例信息
     *
     * @param queryDTO
     * @return
     */
    public IPage<ActHiProcinstEntity> queryProcessInst(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO processInstBO = queryDTO.getCondition();

        String bizCode = StrUtil.isNotBlank(processInstBO.getBizCode()) ? processInstBO.getBizCode().trim() : null;
        String customerName = StrUtil.isNotBlank(processInstBO.getCustomerName()) ? processInstBO.getCustomerName().trim() : null;
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()),
                Wrappers.<ActHiProcinstEntity>lambdaQuery()
                        .and(
                                wrapper -> wrapper.eq(0 == processInstBO.getProcInstStatus(), ActHiProcinstEntity::getStatus, 0)
                                        .or().gt(0 != processInstBO.getProcInstStatus(), ActHiProcinstEntity::getStatus, 0)
                        )
                        .eq(null != processInstBO.getCompanyId(), ActHiProcinstEntity::getCompanyId, processInstBO.getCompanyId())
                        .eq(null != processInstBO.getCustomerId(), ActHiProcinstEntity::getCustomerId, processInstBO.getCustomerId())
                        .eq(StrUtil.isNotEmpty(processInstBO.getDeliveryFactoryCode()), ActHiProcinstEntity::getFactoryCode, processInstBO.getDeliveryFactoryCode())
                        .eq(null != processInstBO.getSupplierId(), ActHiProcinstEntity::getSupplierId, processInstBO.getSupplierId())
                        .in(null != processInstBO.getProcInstIdList() && processInstBO.getProcInstIdList().size() > 0, ActHiProcinstEntity::getProcInstId, processInstBO.getProcInstIdList())
                        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                        //.in(null != processInstBO.getBizType(), ActHiProcinstEntity::getTradeTypeValue, processInstBO.getBizType())
                        .eq(null != processInstBO.getBizType() && 0 != processInstBO.getBizType(), ActHiProcinstEntity::getTtType, processInstBO.getBizType())
                        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end
                        .eq(StrUtil.isNotBlank(processInstBO.getProcessKey()), ActHiProcinstEntity::getProcessKey, processInstBO.getProcessKey())
                        .eq(null != processInstBO.getSubCategoryId() && processInstBO.getSubCategoryId() > 0, ActHiProcinstEntity::getSubGoodsCategoryId, processInstBO.getSubCategoryId())
                        .eq(null != processInstBO.getCategory2(), ActHiProcinstEntity::getCategory2, processInstBO.getCategory2())
                        .eq(null != processInstBO.getCategory3(), ActHiProcinstEntity::getCategory3, processInstBO.getCategory3())
                        .eq(null != processInstBO.getCategory1(), ActHiProcinstEntity::getCategory1, processInstBO.getCategory1())
                        .eq(StringUtil.isNotEmpty(processInstBO.getSiteCode()), ActHiProcinstEntity::getSiteCode, processInstBO.getSiteCode())
                        .eq(StringUtil.isNotEmpty(processInstBO.getSiteName()), ActHiProcinstEntity::getSiteCode, processInstBO.getSiteCode())
                        .eq(null != processInstBO.getSalesType() && processInstBO.getSalesType() > 0, ActHiProcinstEntity::getSalesType, processInstBO.getSalesType())
                        .eq(null != processInstBO.getBizModule() && processInstBO.getBizModule().trim().length() > 0, ActHiProcinstEntity::getBizModule, processInstBO.getBizModule())
                        .and(null != processInstBO.getBizCode() && processInstBO.getBizCode().trim().length() > 0,
                                wrapper -> wrapper.like(null != bizCode && bizCode.length() > 0, ActHiProcinstEntity::getBizCode, bizCode)
                                        .or().like(null != bizCode && bizCode.length() > 0, ActHiProcinstEntity::getReferBizCode, bizCode))
                        .eq(null != processInstBO.getUserId() && processInstBO.getUserId().trim().length() > 0, ActHiProcinstEntity::getStartUserId, processInstBO.getUserId())
                        .like(null != customerName && customerName.length() > 0, ActHiProcinstEntity::getCustomerName, customerName)
                        .orderByDesc(ActHiProcinstEntity::getStartTime)
        );
    }

    /**
     * @param bizCode
     * @param abandon 废弃SparrowNavigatorApplicationTest
     * @return
     */
    public ActHiProcinstEntity getActHiProcinstByBizCode(String bizCode, Boolean abandon) {

        List<ActHiProcinstEntity> actHiProcinstEntities = this.baseMapper.selectList(
                Wrappers.<ActHiProcinstEntity>lambdaQuery().eq(ActHiProcinstEntity::getBusinessKey, bizCode)
                        .and(
                                wrapper -> wrapper.eq(abandon, ActHiProcinstEntity::getStatus, TaskStatusEnum.ABANDON.getValue())
                                        .ne(!abandon, ActHiProcinstEntity::getStatus, TaskStatusEnum.ABANDON.getValue())
                        )
        );
        return actHiProcinstEntities.isEmpty() ? null : actHiProcinstEntities.get(0);
    }

    public List<String> queryProcessInstIdList(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO processInstBO = queryDTO.getCondition();

        LambdaQueryWrapper<ActHiProcinstEntity> queryWrapper = Wrappers.<ActHiProcinstEntity>lambdaQuery()
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .eq(null != processInstBO.getBizType() && 0 != processInstBO.getBizType(), ActHiProcinstEntity::getTtType, processInstBO.getBizType())
                //.in(null != processInstBO.getBizType() && processInstBO.getTradeTypeList().size() > 0, ActHiProcinstEntity::getTradeTypeValue, processInstBO.getTradeTypeList())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                .eq(null != processInstBO.getTaskStatus() && processInstBO.getTaskStatus() == 0, ActHiProcinstEntity::getStatus, TaskStatusEnum.APPROVING.getValue())
                .eq(StringUtil.isNotEmpty(processInstBO.getProcessKey()), ActHiProcinstEntity::getProcessKey, processInstBO.getProcessKey())
                .eq(null != processInstBO.getSubCategoryId() && processInstBO.getSubCategoryId() > 0, ActHiProcinstEntity::getSubGoodsCategoryId, processInstBO.getSubCategoryId())
                .eq(null != processInstBO.getCategory2(), ActHiProcinstEntity::getCategory2, processInstBO.getCategory2())
                .eq(null != processInstBO.getCategory1(), ActHiProcinstEntity::getCategory1, processInstBO.getCategory1())
                .eq(StringUtil.isNotEmpty(processInstBO.getSiteCode()), ActHiProcinstEntity::getSiteCode, processInstBO.getSiteCode())
                .eq(StringUtil.isNotEmpty(processInstBO.getSiteName()), ActHiProcinstEntity::getSiteCode, processInstBO.getSiteName())
                .eq(null != processInstBO.getSalesType() && processInstBO.getSalesType() > 0, ActHiProcinstEntity::getSalesType, processInstBO.getSalesType())
                .eq(null != processInstBO.getBizModule() && processInstBO.getBizModule().trim().length() > 0, ActHiProcinstEntity::getBizModule, processInstBO.getBizModule())
                .and(null != processInstBO.getBizCode() && processInstBO.getBizCode().length() > 0,
                        wrapper -> wrapper.like(null != processInstBO.getBizCode() && processInstBO.getBizCode().length() > 0, ActHiProcinstEntity::getBizCode, processInstBO.getBizCode())
                                .or().like(null != processInstBO.getBizCode() && processInstBO.getBizCode().length() > 0, ActHiProcinstEntity::getReferBizCode, processInstBO.getBizCode()))
                .like(null != processInstBO.getCustomerName() && processInstBO.getCustomerName().trim().length() > 0, ActHiProcinstEntity::getCustomerName, processInstBO.getCustomerName());

        List<String> procInstIdList = this.list(queryWrapper).stream().map(ActHiProcinstEntity::getProcInstId).collect(Collectors.toList());

        return procInstIdList;
    }


}
