package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActReNodeMapper;
import com.navigator.activiti.pojo.entity.ActReNodeEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <p>
 * 节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActReNodeDao extends BaseDaoImpl<ActReNodeMapper, ActReNodeEntity> {
    public ActReNodeEntity getActReNodeByDeploymentId(String deploymentId) {

        List<ActReNodeEntity> actReNodeEntities = this.getBaseMapper().selectList(
                Wrappers.<ActReNodeEntity>lambdaQuery().eq(ActReNodeEntity::getDeploymentId, deploymentId));

        return actReNodeEntities.isEmpty() ? null : actReNodeEntities.get(0);

    }
    public ActReNodeEntity getActReNodeByNodeId(String nodeId, String procDefId) {

        List<ActReNodeEntity> actReNodeEntities = this.getBaseMapper().selectList(Wrappers.<ActReNodeEntity>lambdaQuery()
                .eq(ActReNodeEntity::getNodeId, nodeId)
                .eq(ActReNodeEntity::getProcDefId, procDefId));

        return actReNodeEntities.isEmpty() ? null : actReNodeEntities.get(0);
    }

}
