package com.navigator.activiti.manager;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.service.logic.UserTransferService;
import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.entity.EmployRoleEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.common.dto.Result;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.StringUtil;
import org.activiti.engine.identity.Group;
import org.activiti.engine.identity.GroupQuery;
import org.activiti.engine.impl.GroupQueryImpl;
import org.activiti.engine.impl.Page;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.persistence.entity.GroupEntity;
import org.activiti.engine.impl.persistence.entity.GroupEntityManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.navigator.common.constant.GlobalConstant.SPLIT_SIGN_DH;

/**
 * <AUTHOR>
 * @date 2021/11/11 10:33
 */
@Component
public class RoleEntityManager implements GroupEntityManager, Session {
    @Resource
    @Lazy
    RoleFacade roleFacade;
    @Resource
    @Lazy
    EmployPermissionFacade employPermissionFacade;

    @Override
    public void flush() {

    }

    @Override
    public void close() {

    }

    @Override
    public Group createNewGroup(String groupId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public GroupQuery createNewGroupQuery() {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public List<Group> findGroupByQueryCriteria(GroupQueryImpl query, Page page) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public long findGroupCountByQueryCriteria(GroupQueryImpl query) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public List<Group> findGroupsByUser(String userId) {
        List<RoleEntity> roleEntityList = new ArrayList<>();

        roleEntityList = roleFacade.queryRoleByEmployId(userId);
        System.out.println("=============findGroupsByUser===============");
        System.out.println(roleEntityList);

        return UserTransferService.roleToGroup(roleEntityList);
    }


    public List<Integer> findRoleIdList(String userId) {
        List<Integer> roleIdList = new ArrayList<>();

        Result<List<Integer>> listResult = roleFacade.queryRoleIdsByEmployId(userId);
        if (listResult.isSuccess()) {
            roleIdList = listResult.getData();
        }
        /*List<RoleEntity> roleEntityList = new ArrayList<>();

        roleEntityList = roleFacade.queryRoleByEmployId(userId);

        for (RoleEntity roleEntity : roleEntityList) {
            roleIdList.add(roleEntity.getId());
        }*/
        return roleIdList;
    }

    @Override
    public List<Group> findGroupsByNativeQuery(Map<String, Object> parameterMap, int firstResult, int maxResults) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public long findGroupCountByNativeQuery(Map<String, Object> parameterMap) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public boolean isNewGroup(Group group) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public GroupEntity create() {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public GroupEntity findById(String entityId) {
        RoleEntity roleEntity = roleFacade.getRoleById(Integer.valueOf(entityId));
        return UserTransferService.roleToGroup(roleEntity);
    }

    @Override
    public void insert(GroupEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void insert(GroupEntity entity, boolean fireCreateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public GroupEntity update(GroupEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public GroupEntity update(GroupEntity entity, boolean fireUpdateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(String id) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(GroupEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(GroupEntity entity, boolean fireDeleteEvent) {
        throw new RuntimeException("this is not method!");
    }

    public List<RoleEntity> getRoleByRoleIds(List<String> roleIds) {
        List<RoleEntity> groupList = new ArrayList<>();
        for (String roleId : roleIds) {
            groupList.add(roleFacade.getRoleById(Integer.valueOf(roleId)));
        }
        return groupList;
    }

    public String getTaskRoleIdList(String roleDefCodes, Integer categoryId, String siteCode) {
        StringBuilder roleIds = new StringBuilder();
        List<RoleEntity> roleEntityList = new ArrayList<>();

        roleEntityList = roleFacade.getRoleListByRoleDefInfos(roleDefCodes, categoryId, siteCode).getData();

        if (CommonListUtil.notNullOrEmpty(roleEntityList)) {
            for (RoleEntity roleEntity : roleEntityList) {
                roleIds.append(roleEntity.getId()).append(SPLIT_SIGN_DH);
            }
        }
        return roleIds.toString();
    }

    public String getRoleEmployList(String roleIds) {
        StringBuilder employIds = new StringBuilder();
        Result rtn = employPermissionFacade.getEmployRoleListByRoleIds(StringUtil.split2Int(roleIds, SPLIT_SIGN_DH));
        List<EmployRoleEntity> employRoleEntities = new ArrayList<>();
        if (rtn.isSuccess()) {
            List listJson = JSON.parseObject(JSON.toJSONString(rtn.getData()), employRoleEntities.getClass());
            if (CommonListUtil.notNullOrEmpty(listJson)) {
                for (Object obj : listJson) {
                    EmployRoleEntity employRoleEntity = JSON.parseObject(JSON.toJSONString(obj), EmployRoleEntity.class);
                    employIds.append(employRoleEntity.getEmployId()).append(SPLIT_SIGN_DH);
                }
            }
        }

        return employIds.toString();
    }

}
