package com.navigator.activiti.listener;

import com.navigator.activiti.pojo.enums.ActivityEnum;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowElement;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.repository.ProcessDefinition;

import java.util.Collection;
import java.util.List;

/**
 * 执行监听器
 *
 * <AUTHOR>
 * @date 2021/10/18 13:44
 */
@Slf4j
public class MyExecutionListener implements ExecutionListener {


    ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();



    @Override
    public void notify(DelegateExecution execution) {
//        WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
//        EmployEntityManager employEntityManager = wac.getBean(EmployEntityManager.class);
//        ActRuVariableDao actRuVariableDao = ActRuVariableDao.getBean(ActRuVariableDao.class);
//        ActRuIdentitylinkDao actRuIdentitylinkDao = ActRuIdentitylinkDao.getBean(ActRuIdentitylinkDao.class);
//        ActHiIdentitylinkDao actHiIdentitylinkDao = ActHiIdentitylinkDao.getBean(ActHiIdentitylinkDao.class);
        log.info("============executionListener start============");
        String eventName = execution.getEventName();
        String currentActivitiId = execution.getCurrentActivityId();
        log.info("execution{}", execution.toString());
        log.info("事件名称:{}", eventName);
        log.info("ActivitiId:{}", currentActivitiId);
        log.info("============executionListener  end============");

//        // 只有A节点需要动态配置角色
//        if (eventName.equals(NodeEnum.NODE_A.getNodeName())) {
//            log.info("============11111111============");
//            // 在工作流的参数表里找到角色参数 roleId
//            Task task = processEngine.getTaskService().createTaskQuery().taskId(delegateTask.getId()).singleResult();
//
//            ActRuVariableEntity actRuVariableEntity = actRuVariableDao.getActRuVariableByNameAndProcInstId("roleId", task.getProcessInstanceId());
//            // 没有设置roleId则不需要处理
//            if (null == actRuVariableEntity) {
//                return;
//            }
//            String roleId = actRuVariableEntity.getText();
//
//            // 删除该任务的角色与用户
//            actRuIdentitylinkDao.deleteIdentitylinkByTask(task.getId());
//            actHiIdentitylinkDao.deleteIdentitylinkByTask(task.getId());
//            log.info("============2222222============");
//            // 新增该任务的角色
//            actRuIdentitylinkDao.save(new ActRuIdentitylinkEntity().setGroupId(roleId).setType("candidate").setTaskId(task.getId()));
//            actHiIdentitylinkDao.save(new ActHiIdentitylinkEntity().setGroupId(roleId).setType("candidate").setTaskId(task.getId()));
//            log.info("============3333333============");
//


//        log.info("ProcessInstanceId{}", execution.getProcessInstanceId());
//        ProcessInstance processInstance = processEngine.getRuntimeService().createProcessInstanceQuery().processInstanceId(execution.getProcessInstanceId()).singleResult();
//
//        ActivityEnum activityEnum = ActivityEnum.getByActId(currentActivitiId);
//        switch (activityEnum) {
//
//            case LEADER_COUNTERSIGN:
//                try {
//                    UserTask userTask = (UserTask) startApply(processInstance.getProcessDefinitionId(), ActivityEnum.LEADER_COUNTERSIGN);
//                    Map vars = new HashMap();
//                    List<String> assigneeList = new ArrayList<>();
//                    if (!CollectionUtils.isEmpty(userTask.getCandidateUsers())) {
//                        assigneeList = userTask.getCandidateUsers();
//                    }
//                    if (!CollectionUtils.isEmpty(userTask.getCandidateGroups())) {
//                        // 组需要处理  先拿到人  再放进审批人里
//                        for (String groupId : userTask.getCandidateGroups()) {
//
//                            List<EmployEntity> employEntityList = employEntityManager.getUserListByRoleIds(Arrays.asList(groupId));
//                            if (!CollectionUtils.isEmpty(employEntityList)) {
//                                assigneeList.addAll(employEntityList.stream().map(EmployEntity::getId).collect(Collectors.toList()).stream().map(String::valueOf).collect(Collectors.toList()));
//                            }
//                        }
//                    }
//                    assigneeList.stream().distinct();
//                    vars.put("assigneeList", assigneeList);
//                    processEngine.getRuntimeService().setVariables(execution.getId(), vars);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                break;
//            default:
//                break;
//        }

    }

    public FlowElement startApply(String processDefinitionId, ActivityEnum activityEnum) throws Exception {
        //使用流程定义ID获取所有节点，该Id可以通过多种方式获得，
        // 如通过ProcessDefinitionQuery可以查询一个ProcessDefinition对象，Task对象中也包含
        List<ProcessDefinition> list = processEngine.getRepositoryService().createProcessDefinitionQuery().processDefinitionId(processDefinitionId)
                //创建流程定义查询
                .orderByProcessDefinitionVersion().desc().list();

        ProcessDefinition processDefinition = list.get(0);
        String str = processDefinition.getId();

        BpmnModel model = processEngine.getRepositoryService().getBpmnModel(str);
        if (model != null) {
            Collection<FlowElement> flowElements = model.getMainProcess().getFlowElements();
            for (FlowElement e : flowElements) {

                if (activityEnum.getActId().equals(e.getId())) {

                    return e;
                }
            }
        }
        return null;
    }


}


