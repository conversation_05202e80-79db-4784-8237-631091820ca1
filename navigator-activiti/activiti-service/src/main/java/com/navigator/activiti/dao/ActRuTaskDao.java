package com.navigator.activiti.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.activiti.mapper.ActRuTaskMapper;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.entity.ActRuTaskEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <p>
 * 运行时任务节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActRuTaskDao extends BaseDaoImpl<ActRuTaskMapper, ActRuTaskEntity> {
    /**
     * 根据id查询待审批的任务
     *
     * @param id
     * @return
     */
    public ActRuTaskEntity getActRuTaskById(String id) {
        return this.getOne(Wrappers.<ActRuTaskEntity>lambdaQuery().eq(ActRuTaskEntity::getId, id));
    }

    /**
     * 根据流程实例查询待审批的任务
     *
     * @param procInstId
     * @return
     */
    public ActRuTaskEntity getActRuTaskByProcInstId(String procInstId) {
        return this.getOne(Wrappers.<ActRuTaskEntity>lambdaQuery().eq(ActRuTaskEntity::getProcInstId, procInstId));
    }

    public IPage<ActRuTaskEntity> queryRuTask(QueryDTO<ApproveBO> queryDTO, List<String> taskIds) {
        ApproveBO approveBO = queryDTO.getCondition();
        Page<ActRuTaskEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
        return this.baseMapper.selectPage(page,
                Wrappers.<ActRuTaskEntity>lambdaQuery()
                        .in(CollectionUtil.isNotEmpty(approveBO.getProcInstIdList()), ActRuTaskEntity::getProcInstId,approveBO.getProcInstIdList())
                        .and(wrapper -> wrapper.eq(ActRuTaskEntity::getAssignee, approveBO.getUserId())
                                .or(CollectionUtil.isNotEmpty(taskIds), wrapper1 -> wrapper1.isNull(ActRuTaskEntity::getAssignee).in(ActRuTaskEntity::getId, taskIds))
                        )
                        //.eq(ActRuTaskEntity::getAssignee, approveBO.getUserId())
                        //.or(CollectionUtil.isNotEmpty(taskIds), wrapper -> wrapper.isNull(ActRuTaskEntity::getAssignee).in(ActRuTaskEntity::getId, taskIds))
                        .and(StrUtil.isNotBlank(approveBO.getProcessKey()), wrapper -> wrapper.eq(ActRuTaskEntity::getProcessKey, approveBO.getProcessKey()))
                        .orderByDesc(ActRuTaskEntity::getCreateTime)
        );
    }
}
