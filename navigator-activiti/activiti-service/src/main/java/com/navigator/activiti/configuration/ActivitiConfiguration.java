package com.navigator.activiti.configuration;

import com.navigator.activiti.factory.EmployEntityManagerFactory;
import com.navigator.activiti.factory.EmployRoleEntityManagerFactory;
import com.navigator.activiti.factory.RoleEntityManagerFactory;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.manager.EmployRoleEntityManager;
import com.navigator.activiti.manager.RoleEntityManager;
import org.activiti.engine.impl.interceptor.SessionFactory;
import org.activiti.spring.SpringAsyncExecutor;
import org.activiti.spring.SpringProcessEngineConfiguration;
import org.activiti.spring.boot.AbstractProcessEngineAutoConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/10 16:36
 */
@Configuration
public class ActivitiConfiguration extends AbstractProcessEngineAutoConfiguration {

    @Autowired
    EmployEntityManagerFactory employEntityManagerFactory;
    @Autowired
    EmployRoleEntityManagerFactory employRoleEntityManagerFactory;
    @Autowired
    RoleEntityManagerFactory roleEntityManagerFactory;
    @Autowired
    EmployEntityManager employEntityManager;
    @Autowired
    EmployRoleEntityManager employRoleEntityManager;
    @Autowired
    RoleEntityManager roleEntityManager;


    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource activitiDataSource() {
        return DataSourceBuilder.create().build();
    }


    @Bean
    public SpringProcessEngineConfiguration springProcessEngineConfiguration(
            PlatformTransactionManager transactionManager,
            SpringAsyncExecutor springAsyncExecutor) throws IOException {

        SpringProcessEngineConfiguration springProcessEngineConfiguration = baseSpringProcessEngineConfiguration(
                activitiDataSource(),
                transactionManager,
                springAsyncExecutor);
        // 配置自定义的用户和组管理
        springProcessEngineConfiguration.setUserEntityManager(employEntityManager);
        springProcessEngineConfiguration.setGroupEntityManager(roleEntityManager);
        springProcessEngineConfiguration.setMembershipEntityManager(employRoleEntityManager);

        List<SessionFactory> customSessionFactories = new ArrayList<>();
        customSessionFactories.add(employEntityManagerFactory);
        customSessionFactories.add(employRoleEntityManagerFactory);
        customSessionFactories.add(roleEntityManagerFactory);
        springProcessEngineConfiguration.setCustomSessionFactories(customSessionFactories);
        return springProcessEngineConfiguration;
    }
}
