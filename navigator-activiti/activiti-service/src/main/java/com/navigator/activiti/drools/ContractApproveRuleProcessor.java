package com.navigator.activiti.drools;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.ContractApproveBizInfoDTO;
import com.navigator.common.util.DroolsUtil;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Data
@Service
public class ContractApproveRuleProcessor {
    private String ruleName;

    private T bizParameter;


    public String getRuleName(ContractApproveBizInfoDTO bizParameter) {
        StringBuilder sbRuleName = new StringBuilder();
        sbRuleName.append("rules/");
        sbRuleName.append(GoodsCategoryEnum.getByValue(bizParameter.getCategory2()).getCode());
        sbRuleName.append("_").append(ContractSalesTypeEnum.getByValue(bizParameter.getSalesType()).getDirectCode());
        ContractTradeTypeEnum tradeTypeEnum = ContractTradeTypeEnum.getByValue(bizParameter.getTradeType());
        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(bizParameter.getTtType());
        switch (ttTypeEnum) {
            case NEW:
            case REVISE:
                sbRuleName.append("_").append(ttTypeEnum.getCode());
                break;
            default:
                sbRuleName.append("_").append("OTHER");
                break;
        }
        sbRuleName.append(".drl");
        return sbRuleName.toString();
    }

    public void run() {
        ContractApproveBizInfoDTO bizParameter = new ContractApproveBizInfoDTO();
        bizParameter.setCategory2(11);
        bizParameter.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        bizParameter.setTtType(TTTypeEnum.NEW.getType());
        bizParameter.setTradeType(ContractTradeTypeEnum.NEW.getValue());
        bizParameter.setContractType(1);

        bizParameter.setTotalAmount(BigDecimal.valueOf(18000000));
        bizParameter.setProteinDiffPrice(BigDecimal.valueOf(10));
        bizParameter.setTransportPrice(BigDecimal.valueOf(20));
        bizParameter.setRefineDiffPrice(BigDecimal.valueOf(30));
        bizParameter.setBusinessPrice(BigDecimal.valueOf(40));
        bizParameter.setOtherPrice(BigDecimal.valueOf(50));

        bizParameter.setProteinDiffPriceChanged(true);
        bizParameter.setTransportPriceChanged(true);
        bizParameter.setRefineDiffPriceChanged(true);
        bizParameter.setBusinessPriceChanged(true);
        bizParameter.setOtherPriceChanged(true);

        bizParameter.setDeliveryLong(true);

        DroolsUtil.run("rules/contract_rules.drl", bizParameter);
        System.out.println(bizParameter.getRuleMemo());
        DroolsUtil.run(getRuleName(bizParameter), bizParameter);
        System.out.println("endddddddddddd");


    }


}
