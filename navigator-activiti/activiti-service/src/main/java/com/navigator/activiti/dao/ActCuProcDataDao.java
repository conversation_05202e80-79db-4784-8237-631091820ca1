package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActCuProcDataMapper;
import com.navigator.activiti.pojo.entity.ActCuProcDataEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <p>
 * 历史流程实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActCuProcDataDao extends BaseDaoImpl<ActCuProcDataMapper, ActCuProcDataEntity> {

    public List<ActCuProcDataEntity> getProcDataList(String processInstId) {
        return this.baseMapper.selectList(Wrappers.<ActCuProcDataEntity>lambdaQuery()
                .eq(ActCuProcDataEntity::getProcInstId, processInstId)
                .orderByAsc(ActCuProcDataEntity::getIndexNo));
    }

    public List<ActCuProcDataEntity> getProcDataListByGroupCode(String groupCode) {
        return this.baseMapper.selectList(Wrappers.<ActCuProcDataEntity>lambdaQuery()
                .eq(ActCuProcDataEntity::getGroupCode, groupCode));
    }
}
