package com.navigator.activiti.factory;

import com.navigator.activiti.manager.EmployRoleEntityManager;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.interceptor.SessionFactory;
import org.activiti.engine.impl.persistence.entity.MembershipEntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/11/11 10:34
 */
@Service
public class EmployRoleEntityManagerFactory implements SessionFactory {
    @Autowired
    private EmployRoleEntityManager employRoleEntityManager;

    @Override
    public Class<?> getSessionType() {
        return MembershipEntityManager.class;
    }

    @Override
    public Session openSession(CommandContext commandContext) {
        return employRoleEntityManager;
    }

    @Autowired
    public void setCustomUserEntityManager(EmployRoleEntityManager employRoleEntityManager) {
        this.employRoleEntityManager = employRoleEntityManager;
    }
}
