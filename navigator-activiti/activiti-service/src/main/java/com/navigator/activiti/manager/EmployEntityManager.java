package com.navigator.activiti.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.navigator.activiti.service.logic.UserTransferService;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.identity.Group;
import org.activiti.engine.identity.Picture;
import org.activiti.engine.identity.User;
import org.activiti.engine.identity.UserQuery;
import org.activiti.engine.impl.Page;
import org.activiti.engine.impl.UserQueryImpl;
import org.activiti.engine.impl.interceptor.Session;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.activiti.engine.impl.persistence.entity.UserEntityImpl;
import org.activiti.engine.impl.persistence.entity.UserEntityManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 只做查询
 *
 * <AUTHOR>
 * @date 2021/11/10 16:02
 */
@Component
@Slf4j
public class EmployEntityManager implements UserEntityManager, Session {
    @Resource
    @Lazy
    EmployFacade employFacade;
    @Resource
    @Lazy
    RoleFacade roleFacade;


    @Override
    public void flush() {

    }

    @Override
    public void close() {

    }

    @Override
    public User createNewUser(String userId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void updateUser(User updatedUser) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public List<User> findUserByQueryCriteria(UserQueryImpl query, Page page) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public long findUserCountByQueryCriteria(UserQueryImpl query) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public List<Group> findGroupsByUser(String userId) {
        List<RoleEntity> roleEntityList = new ArrayList<>();
        roleEntityList= roleFacade.queryRoleByEmployId(userId);
        return UserTransferService.roleToGroup(roleEntityList);
    }

    @Override
    public UserQuery createNewUserQuery() {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public Boolean checkPassword(String userId, String password) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public List<User> findUsersByNativeQuery(Map<String, Object> parameterMap, int firstResult, int maxResults) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public long findUserCountByNativeQuery(Map<String, Object> parameterMap) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public boolean isNewUser(User user) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public Picture getUserPicture(String userId) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void setUserPicture(String userId, Picture picture) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void deletePicture(User user) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public UserEntity create() {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public UserEntity findById(String entityId) {
        List<EmployEntity> employEntityList = employFacade.getEmployByEmployIds(Arrays.asList(Integer.valueOf(entityId)));
        if (CollectionUtil.isEmpty(employEntityList)) {
            UserEntity userEntity = new UserEntityImpl();
            userEntity.setFirstName("");
            return userEntity;
        }
        return UserTransferService.employToUser(employEntityList.get(0));
    }

    @Override
    public void insert(UserEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void insert(UserEntity entity, boolean fireCreateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public UserEntity update(UserEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public UserEntity update(UserEntity entity, boolean fireUpdateEvent) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(String id) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(UserEntity entity) {
        throw new RuntimeException("this is not method!");
    }

    @Override
    public void delete(UserEntity entity, boolean fireDeleteEvent) {
        throw new RuntimeException("this is not method!");
    }

    public List<EmployEntity> getUserListByRoleIds(List<String> roleIds) {
        log.info("roleIds{}", roleIds.toString());
        return employFacade.queryEmployByRoleIds(roleIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
    }

    public List<EmployEntity> getUserListByUserIds(List<String> userIds) {
        if (CollectionUtil.isNotEmpty(userIds)){
            return new ArrayList<>();
        }
        return employFacade.getEmployByEmployIds(userIds.stream().map(Integer::parseInt).collect(Collectors.toList()));
    }
}
