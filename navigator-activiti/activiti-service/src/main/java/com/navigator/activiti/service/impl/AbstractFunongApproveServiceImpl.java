package com.navigator.activiti.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveResultEnum;
import com.navigator.activiti.service.ApproveService;
import com.navigator.activiti.service.FunongApproveService;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogTargetRecordTypeEnum;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Service
@Slf4j
public class AbstractFunongApproveServiceImpl implements FunongApproveService {

    @Resource
    private ApproveService approveService;
    @Resource
    OperationLogFacade operationLogFacade;
    @Resource
    MessageFacade messageFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApproveResultDTO start(ApproveDTO approveDTO) {
        ApproveResultDTO approveResultDTO = new ApproveResultDTO();

        approveResultDTO = approveService.start(approveDTO);

        if (ContractTradeTypeEnum.NEW.getValue() != approveDTO.getContractTradeTypeEnum().getValue()) {
            recordBizOperation(approveDTO, approveResultDTO);
        }

        //启动即完成，记录一条系统日志
        if (approveResultDTO.getApproveResult() == ApproveResultEnum.AGREE.getValue()) {
            int i;
            OperationDetailDTO operationDetailDTO2 = buildOperationLogDetail(approveResultDTO, approveDTO);
            operationDetailDTO2.setOperatorName("【系统】");
            operationDetailDTO2.setBizCode("PROCINST_APPROVED_FREE");
            operationDetailDTO2.setOperationInfo(approveResultDTO.getProcInstStatus());
            // 记录操作日志
            operationLogFacade.recordOperationLogDetail(operationDetailDTO2);
        } else {
            //发送提醒信息
            String businessCode = MessageBusinessCodeEnum.LDC_TASK_APPROVE_NOTICE.name();
            if (Objects.equals(approveDTO.getBizModule(), ModuleTypeEnum.CONTRACT_EQUITY.getModule())) {
                businessCode = MessageBusinessCodeEnum.CONTRACT_EQUITY_APPROVE_NOTICE.name();
            }
            sendApproveNoticeMessage(businessCode, approveDTO.getProcInstId(), approveDTO.getReferBizCode(), approveResultDTO.getNextNodeName(), approveResultDTO.getTaskCandidateUsers());
        }

        return approveResultDTO;
    }

    @Override
    public void recordBizOperation(ApproveDTO approveDTO, ApproveResultDTO approveResultDTO) {
        OperationDetailDTO operationDetailDTO = buildOperationLogDetail(approveResultDTO, approveDTO);
        try {
            operationDetailDTO.setOperationInfo(approveResultDTO.getApproveRuleName());
            operationDetailDTO.setBizCode(OperationActionEnum.START_APPROVE.getCode());
            // 记录操作日志
            operationLogFacade.recordOperationLogDetail(operationDetailDTO);
        } catch (Exception e) {
            log.error("recordBizOperation error:{}", JSON.toJSONString(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApproveResultDTO approve(ApproveDTO approveDTO) {
        ApproveResultDTO approveResultDTO = new ApproveResultDTO();

        approveResultDTO = approveService.approve(approveDTO);

        OperationDetailDTO operationDetailDTO = buildOperationLogDetail(approveResultDTO, approveDTO);

        try {
            String memo = approveResultDTO.getTaskApproveMemo();
            if (StringUtil.isEmpty(memo)) {
                memo = "无";
            }
            operationDetailDTO.setOperationInfo(approveResultDTO.getTaskActionName() + "，意见：" + memo);

            // 记录操作日志
            operationLogFacade.recordOperationLogDetail(operationDetailDTO);

            //发送任务审批完成Follow消息
            String businessCode = MessageBusinessCodeEnum.LDC_TASK_APPROVE_NOTICE.name();
            if (Objects.equals(approveDTO.getBizModule(), ModuleTypeEnum.CONTRACT_EQUITY.getModule())) {
                businessCode = MessageBusinessCodeEnum.CONTRACT_EQUITY_APPROVE_NOTICE.name();
            }

            //case:1003113 审批驳回驳回合同状态未作废,判空校验 Author:Wan 2025-04-11 start
            if (StringUtil.isNotEmpty(approveResultDTO.getNextNodeName()) && StringUtil.isNotEmpty(approveResultDTO.getTaskCandidateUsers())) {
                sendApproveNoticeMessage(businessCode, approveResultDTO.getProcInstId(), approveDTO.getReferBizCode(), approveResultDTO.getNextNodeName(), approveResultDTO.getTaskCandidateUsers());
            }
            //case:1003113 审批驳回驳回合同状态未作废,判空校验 Author:Wan 2025-04-11 start

        } catch (Exception e) {
            e.printStackTrace();
        }

        //启动即完成，记录一条系统日志
        if (approveResultDTO.getApproveResult() != ApproveResultEnum.APPROVING.getValue()) {
            OperationDetailDTO operationDetailDTO2 = buildOperationLogDetail(approveResultDTO, approveDTO);
            operationDetailDTO2.setOperatorName("【系统】");
            operationDetailDTO2.setBizCode("PROCINST_APPROVED");
            operationDetailDTO2.setOperationInfo(approveResultDTO.getProcInstStatus());
            // 记录操作日志
            operationLogFacade.recordOperationLogDetail(operationDetailDTO2);

            //发送任务审批完成Follow消息
            //sendApproveNoticeMessage(MessageBusinessCodeEnum.LDC_PROCINST_APPROVED_FOLLOW.name(), approveDTO.getProcInstId(), approveDTO.getReferBizCode(), approveDTO.getNodeName(), approveResultDTO.getTaskCandidateUsers());

        }

        return approveResultDTO;

    }

    private OperationDetailDTO buildOperationLogDetail(ApproveResultDTO approveResultDTO, ApproveDTO approveDTO) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO();
        operationDetailDTO.setBizCode(approveResultDTO.getBizCode())
                .setBizModule(approveResultDTO.getBizModule())
                .setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorId(Integer.valueOf(approveDTO.getUserId()))
                .setOperationName(approveResultDTO.getTaskNodeName())
                .setReferBizId(Integer.valueOf(approveResultDTO.getProcInstId()))
                .setReferBizCode(approveResultDTO.getTaskId())
                .setData(JSON.toJSONString(approveDTO))
                .setReferOperationData(JSON.toJSONString(approveResultDTO))
                .setTargetRecordId(approveDTO.getReferBizId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name())
                .setTradeTypeName(approveResultDTO.getTradeTypeName())
                .setTtCode(approveDTO.getBizCode())
        ;

        return operationDetailDTO;
    }

    @Async
    public void sendApproveNoticeMessage(String businessCode, String procInstId, String referBizCode, String nodeName, String receivers) {
        try {
            MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
            messageInfoDTO.setUuid(UUID.randomUUID().toString())
                    .setBusinessCode(businessCode)
                    .setBusinessSceneCode(BusinessSceneEnum.LDC_TASK_APPROVE_NOTICE.getDesc())
                    .setReferBizId(procInstId)
                    .setMenuCode(String.valueOf(MenuCodeEnum.ACTIVITI.getValue()))
                    .setSystem(SystemEnum.MAGELLAN.getValue());
            nodeName = nodeName.replace("审批", "");
            Map<String, String> mapBizData = new HashMap<>();
            mapBizData.put("contractCode", referBizCode);
            mapBizData.put("nodeName", nodeName);

            messageInfoDTO.setDataMap(mapBizData);
            messageInfoDTO.setReceivers(StringUtil.split(receivers));
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            log.error("sendApproveNoticeMessage:{}", e.getMessage());
            e.printStackTrace();
        }
    }

}
