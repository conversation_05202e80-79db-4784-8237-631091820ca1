package com.navigator.activiti.service;

import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;

/**
 * ===================================
 * Activiti 内部接口，外部系统不要调用
 * ===================================
 */
public interface FunongApproveService {

    ApproveResultDTO start(ApproveDTO approveDTO);

    ApproveResultDTO approve(ApproveDTO approveDTO);

    void recordBizOperation(ApproveDTO approveDTO, ApproveResultDTO approveResultDTO);
}
