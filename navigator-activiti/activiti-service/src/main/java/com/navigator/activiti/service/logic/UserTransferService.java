package com.navigator.activiti.service.logic;

import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import org.activiti.engine.identity.Group;
import org.activiti.engine.impl.persistence.entity.GroupEntity;
import org.activiti.engine.impl.persistence.entity.GroupEntityImpl;
import org.activiti.engine.impl.persistence.entity.UserEntity;
import org.activiti.engine.impl.persistence.entity.UserEntityImpl;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/2 20:03
 */
public class UserTransferService {

    public static List<Group> roleToGroup(List<RoleEntity> roleEntityList) {
        if (CollectionUtils.isEmpty(roleEntityList)) {
            return Collections.emptyList();
        }
        List<Group> groupList = new ArrayList<>();
        roleEntityList.forEach(roleEntity -> {
            GroupEntity groupEntity = new GroupEntityImpl();
            groupEntity.setId(roleEntity.getId().toString());
            groupEntity.setName(roleEntity.getName());
            groupEntity.setRevision(1);
            groupEntity.setType("");
            groupList.add(groupEntity);
        });
        return groupList;
    }

    public static GroupEntity roleToGroup(RoleEntity roleEntity) {
        GroupEntity groupEntity = new GroupEntityImpl();
        if (null == roleEntity) {
            return groupEntity;
        }
        groupEntity.setId(roleEntity.getId().toString());
        groupEntity.setName(roleEntity.getName());
        groupEntity.setRevision(1);
        groupEntity.setType("");
        return groupEntity;
    }


    public static UserEntity employToUser(EmployEntity employEntity) {
        UserEntity userEntity = new UserEntityImpl();
        if (null == employEntity) {
            return userEntity;
        }
        userEntity.setId(employEntity.getId().toString());
        userEntity.setFirstName(employEntity.getName());
        userEntity.setEmail(employEntity.getEmail());
        userEntity.setLastName("");
        userEntity.setPassword("");
        return userEntity;
    }

}
