package com.navigator.activiti.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.activiti.dao.*;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.manager.RoleEntityManager;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.*;
import com.navigator.activiti.pojo.entity.*;
import com.navigator.activiti.pojo.enums.*;
import com.navigator.activiti.service.ProcessInstService;
import com.navigator.activiti.service.TaskService;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.dto.tradeticket.ContractModifyTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.ContractTransferTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.TradeTicketDTO;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;
import com.navigator.trade.pojo.enums.UsageEnum;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.identity.Group;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/3 19:03
 */
@Service
@Slf4j
public class TaskServiceImpl implements TaskService {
    @Resource
    EmployEntityManager employEntityManager;
    @Resource
    RoleEntityManager roleEntityManager;
    @Resource
    ActRuIdentitylinkDao actRuIdentitylinkDao;
    @Resource
    ActRuTaskDao actRuTaskDao;
    @Resource
    ProcessInstService processInstService;
    @Resource
    ActReNodeDao actReNodeDao;
    @Resource
    ActHiTaskinstDao actHiTaskinstDao;
    @Resource
    ActHiCommentDao actHiCommentDao;
    @Resource
    ActHiProcinstDao actHiProcinstDao;
    @Resource
    ActHiActinstDao actHiActinstDao;
    @Resource
    ActHiVarinstDao actHiVarinstDao;
    @Autowired
    private TradeTicketFacade tradeTicketFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private PayConditionFacade payConditionFacade;
    @Autowired
    private CustomerBankFacade customerBankFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private DeliveryTypeFacade deliveryTypeFacade;


    @Override
    public Result queryApproveTask(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();

        //查询该用户的角色
        List<Integer> roleIds = roleEntityManager.findRoleIdList(approveBO.getUserId());
        approveBO.setUserRoleIdList(roleIds);

        if (approveBO.getTaskStatus() == TaskStatusEnum.APPROVING.getValue()) {
            //查询审批中的任务
            return this.queryApproveingTask(queryDTO);
            //return this.queryApprovedTask(queryDTO);
            //return queryTodoTask2(queryDTO);
        } else {
            //查询历史任务
            return this.queryApprovedTask(queryDTO);
            //return this.queryApproveingTask(queryDTO);
            //return queryFinshedTask2(queryDTO);
        }

    }

    @Override
    public List<ApproveTaskInfoDTO> queryHiTaskByBizCode(String bizCode) {
        // 查询流程实例
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByBizCode(bizCode, false);
        if (null == actHiProcinstEntity) {
            return Collections.emptyList();
        }
        List<ActHiActinstEntity> actHiActinstEntityList = actHiActinstDao.queryActHiActinstByProcInstId(actHiProcinstEntity.getProcInstId());
        List<ApproveTaskInfoDTO> approveTaskInfoDTOList = new ArrayList<>();
        actHiActinstEntityList.stream().forEach(actHiActinstEntity -> {

            // 任务信息
            ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();
            approveTaskInfoDTO.setProcessInstId(actHiProcinstEntity.getProcInstId())
                    .setNodeId(actHiActinstEntity.getActId())
                    .setCreatedAt(new Timestamp(actHiActinstEntity.getStartTime().getTime()))
                    .setApproved(null != actHiActinstEntity.getEndTime());

            // 查询节点表
            ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiActinstEntity.getActId(), actHiActinstEntity.getProcDefId());
            // 节点信息
            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeName(actHiActinstEntity.getActName());
            // 开始任务  自动的人工任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.MANUAL_TASK.getType())) {
                approveTaskInfoDTO.setApproveRoleName(actReNodeEntity.getRoleNameList())
                        .setApprovedAt(new Timestamp(actHiActinstEntity.getEndTime().getTime()))
                        .setApproveActionName(ApproveActionEnum.START.getName())
                        .setApproveActionValue(ApproveActionEnum.START.getValue())
                        .setApproveUserId(Integer.valueOf(actHiProcinstEntity.getStartUserId()))
                        .setApproveUserName(employEntityManager.findById(actHiProcinstEntity.getStartUserId()).getFirstName());

            }
            // 审批任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.USER_TASK.getType())) {
                processNodeDTO.setDescription(actReNodeEntity.getDescription());
                approveTaskInfoDTO.setApproveRoleName(actReNodeEntity.getRoleNameList())
                        .setTaskId(actHiActinstEntity.getTaskId());
                // 审批人
                if (StrUtil.isNotBlank(actHiActinstEntity.getAssignee())) {
                    approveTaskInfoDTO.setApproveUserId(Integer.valueOf(actHiActinstEntity.getAssignee()))
                            .setApproveUserName(employEntityManager.findById(actHiActinstEntity.getAssignee()).getFirstName());
                }
                // 已结束 并且未作废
                if (null != actHiActinstEntity.getEndTime() && null == actHiActinstEntity.getDeleteReason()) {
                    // 查询审批意见
                    ActHiCommentEntity actHiCommentEntity = actHiCommentDao.getActHiCommentByTaskId(actHiActinstEntity.getTaskId());
                    String approveRuleValue = actHiVarinstDao.getApproveRuleValueBytskId(actHiActinstEntity.getTaskId());
                    approveTaskInfoDTO.setApprovedAt(new Timestamp(actHiActinstEntity.getEndTime().getTime()))
                            .setApproveMemo(actHiCommentEntity.getMessage())
                            .setApproveActionName(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getName())
                            .setApproveActionValue(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getValue());
                }
            }
            // 结束任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.END_EVENT.getType())) {
                // 结束动作为结束节点名称
                approveTaskInfoDTO.setApproveActionName(actReNodeEntity.getNodeName())
                        .setApprovedAt(new Timestamp(actHiActinstEntity.getEndTime().getTime()));
            }
            approveTaskInfoDTO.setProcessNodeDTO(processNodeDTO);
            if (!actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.START_EVENT.getType())) {
                approveTaskInfoDTOList.add(approveTaskInfoDTO);
            }

        });
        return approveTaskInfoDTOList;
    }

    public List<ApproveTaskInfoDTO> queryHiTaskOld(String procInstId) {
        // 查询流程实例
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(procInstId);
        if (null == actHiProcinstEntity) {
            return Collections.emptyList();
        }
        List<ActHiActinstEntity> actHiActinstEntityList = actHiActinstDao.queryActHiActinstByProcInstId(actHiProcinstEntity.getProcInstId());
        List<ApproveTaskInfoDTO> approveTaskInfoDTOList = new ArrayList<>();
        actHiActinstEntityList.stream().forEach(actHiActinstEntity -> {

            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.START_EVENT.getType())) {
                return;
            }
            // 任务信息
            ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();
            approveTaskInfoDTO.setProcessInstId(actHiProcinstEntity.getProcInstId())
                    .setNodeId(actHiActinstEntity.getActId())
                    .setCreatedAt(new Timestamp(actHiActinstEntity.getStartTime().getTime()))
                    .setApproved(null != actHiActinstEntity.getEndTime());

            // 查询节点表
            ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiActinstEntity.getActId(), actHiActinstEntity.getProcDefId());
            // 节点信息
            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeName(actHiActinstEntity.getActName());

            // 开始任务  自动的人工任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.MANUAL_TASK.getType())) {
                approveTaskInfoDTO.setApproveRoleName(actReNodeEntity.getRoleNameList())
                        .setApprovedAt(new Timestamp(actHiActinstEntity.getEndTime().getTime()))
                        .setApproveActionName(ApproveActionEnum.START.getName())
                        .setApproveActionValue(ApproveActionEnum.START.getValue())
                        .setApproveUserId(Integer.valueOf(actHiProcinstEntity.getStartUserId()))
                        .setApproveUserName(employEntityManager.findById(actHiProcinstEntity.getStartUserId()).getFirstName())
                        .setApproveMemo("提交审批")
                ;

            }
            // 审批任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.USER_TASK.getType())) {
                processNodeDTO.setDescription(actReNodeEntity.getDescription());
                approveTaskInfoDTO.setApproveRoleName(actReNodeEntity.getRoleNameList())
                        .setTaskId(actHiActinstEntity.getTaskId())
                        .setApprovedAt(null != actHiActinstEntity.getEndTime() ? new Timestamp(actHiActinstEntity.getEndTime().getTime()) : null);

                // 审批人
                if (StrUtil.isNotBlank(actHiActinstEntity.getAssignee())) {
                    approveTaskInfoDTO.setApproveUserId(Integer.valueOf(actHiActinstEntity.getAssignee()))
                            .setApproveUserName(employEntityManager.findById(actHiActinstEntity.getAssignee()).getFirstName());
                }
                // 已结束 并且未作废
                if (null != actHiActinstEntity.getEndTime() && null == actHiActinstEntity.getDeleteReason()) {
                    approveTaskInfoDTO.setApproveActionName(actHiActinstEntity.getActionName());
                    String memo = actHiActinstEntity.getMemo();
                    if (StringUtil.isEmpty(memo)) {
                        memo = actHiActinstEntity.getActionName();
                    } else {
                        memo = StringUtil.isNotEmpty(actHiActinstEntity.getActionName()) ? actHiActinstEntity.getActionName() + ":" + actHiActinstEntity.getMemo() : actHiActinstEntity.getMemo();
                    }
                    approveTaskInfoDTO.setApproveMemo(memo);
                }


            }
            // 结束任务
            if (actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.END_EVENT.getType())) {
                // 结束动作为结束节点名称
                approveTaskInfoDTO.setApproveActionName(actReNodeEntity.getNodeName())
                        .setApprovedAt(new Timestamp(actHiActinstEntity.getEndTime().getTime()))
                        .setApproveUserName("系统")
                        .setApproveMemo("审批完成")
                ;
            }
            approveTaskInfoDTO.setProcessNodeDTO(processNodeDTO);
            if (!actHiActinstEntity.getActType().equals(BpmsActivityTypeEnum.START_EVENT.getType())) {
                approveTaskInfoDTOList.add(approveTaskInfoDTO);
            }

        });
        return approveTaskInfoDTOList;
    }

    public List<ApproveTaskActInfoDTO> queryHiTask(String procInstId) {
        // 查询流程实例
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(procInstId);
        if (null == actHiProcinstEntity) {
            return Collections.emptyList();
        }
        Map<String, ActHiActinstEntity> mapActHiActinstEntity = actHiActinstDao.queryAutoActHiActinst(actHiProcinstEntity.getProcInstId());
        List<ApproveTaskActInfoDTO> approveTaskActInfoDTOList = new ArrayList<>();

        ActHiActinstEntity startActHiActinstEntity = mapActHiActinstEntity.get(BpmsActivityTypeEnum.MANUAL_TASK.getType());
        if (null != startActHiActinstEntity) {
            ApproveTaskActInfoDTO startTaskActInfoDTO = convertApproveTaskActInfoDTO(startActHiActinstEntity);
            startTaskActInfoDTO.setActionName("提交审批");
            startTaskActInfoDTO.setApproveMemo("提交审批");
            startTaskActInfoDTO.setAssignee(actHiProcinstEntity.getStartUserId());
            startTaskActInfoDTO.setProcessKey(actHiProcinstEntity.getProcessKey());
            startTaskActInfoDTO.setApproveUserName(employEntityManager.findById(actHiProcinstEntity.getStartUserId()).getFirstName());
            startTaskActInfoDTO.setApproved(true);
            startTaskActInfoDTO.setApproveResult("审批启动");
            approveTaskActInfoDTOList.add(startTaskActInfoDTO);
        }

        List<ActHiTaskinstEntity> actHiTaskinstEntityList = actHiTaskinstDao.queryActHiTaskinstByProcInstId(procInstId);

        String latestApproveUserName = "";

        for (ActHiTaskinstEntity actHiTaskinstEntity : actHiTaskinstEntityList) {

            ApproveTaskActInfoDTO approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actHiTaskinstEntity);

            // 审批人
            if (StrUtil.isNotBlank(actHiTaskinstEntity.getAssignee())) {
                latestApproveUserName = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();
                approveTaskActInfoDTO.setApproveUserName(latestApproveUserName);
            }
            // 已结束 并且未作废
            if (null != approveTaskActInfoDTO.getEndTime() && null == approveTaskActInfoDTO.getDeleteReason()) {
                String memo = approveTaskActInfoDTO.getMemo();
                if (StringUtil.isEmpty(memo)) {
                    memo = approveTaskActInfoDTO.getActionName();
                } else {
                    memo = StringUtil.isNotEmpty(approveTaskActInfoDTO.getActionName()) ? approveTaskActInfoDTO.getActionName() + ":" + approveTaskActInfoDTO.getMemo() : approveTaskActInfoDTO.getMemo();
                }
                approveTaskActInfoDTO.setApproveMemo(memo);
            }

            if (null != approveTaskActInfoDTO.getEndTime()) {
                approveTaskActInfoDTO.setApproved(true);
                if (null != actHiTaskinstEntity) {
                    if (!actHiTaskinstEntity.getActionName().equals("TT撤回"))
                        approveTaskActInfoDTO.setApproveResult("审核" + actHiTaskinstEntity.getActionName());
                    else
                        approveTaskActInfoDTO.setApproveResult(actHiTaskinstEntity.getActionName());
                }
            } else {
                approveTaskActInfoDTO.setApproved(false);
                approveTaskActInfoDTO.setApproveResult("审批" + ApproveResultEnum.APPROVING.getDesc());
            }

            approveTaskActInfoDTOList.add(approveTaskActInfoDTO);
        }

        // 结束任务

        ActHiActinstEntity endActHiActinstEntity = mapActHiActinstEntity.get(BpmsActivityTypeEnum.END_EVENT.getType());
        if (null != endActHiActinstEntity) {
            String approveStatus = ApproveResultEnum.getByValue(actHiProcinstEntity.getStatus()).getDesc();
            ApproveTaskActInfoDTO startTaskActInfoDTO = convertApproveTaskActInfoDTO(endActHiActinstEntity);
            startTaskActInfoDTO.setActionName("审批完成");
            startTaskActInfoDTO.setApproveMemo("审批完成：" + approveStatus);
            startTaskActInfoDTO.setAssignee(actHiProcinstEntity.getStartUserId());
            startTaskActInfoDTO.setProcessKey(actHiProcinstEntity.getProcessKey());
            startTaskActInfoDTO.setApproveUserName(latestApproveUserName);
            startTaskActInfoDTO.setApproved(true);
            startTaskActInfoDTO.setApproveResult("完成");
            approveTaskActInfoDTOList.add(startTaskActInfoDTO);

        }

        return approveTaskActInfoDTOList;
    }

    @Override
    public ApproveTaskActInfoDTO queryLatestTask(String procInstId) {
        ApproveTaskActInfoDTO approveTaskActInfoDTO = null;
        List<ActHiTaskinstEntity> actHiTaskinstEntityList = actHiTaskinstDao.queryActHiTaskinstByProcInstId(procInstId);

        if (null != actHiTaskinstEntityList && actHiTaskinstEntityList.size() > 0) {
            approveTaskActInfoDTO = new ApproveTaskActInfoDTO();

            ActHiTaskinstEntity actHiTaskinstEntity = actHiTaskinstEntityList.get(actHiTaskinstEntityList.size() - 1);

            approveTaskActInfoDTO = convertApproveTaskActInfoDTO(actHiTaskinstEntity);
        }
        return approveTaskActInfoDTO;
    }

    @Override
    public ApproveTaskInfoDTO getApproveTaskInfo(String taskId) {
        // 任务信息
        ActHiTaskinstEntity actHiTaskinstEntity = actHiTaskinstDao.getActHiTaskinstById(taskId);
        // 节点信息
        ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiTaskinstEntity.getTaskDefKey(), actHiTaskinstEntity.getProcDefId());
        // 流程实例信息
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(actHiTaskinstEntity.getProcInstId());
        ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();
        approveTaskInfoDTO.setTaskId(taskId)
                .setProcessInstId(actHiTaskinstEntity.getProcInstId())
                .setProcessName(actHiProcinstEntity.getProcessName())
                .setNodeId(actHiTaskinstEntity.getTaskDefKey())
                .setApproved(null != actHiTaskinstEntity.getEndTime())
                .setApproveRoleName(actReNodeEntity.getRoleNameList())
                .setCreatedAt(new Timestamp(actHiProcinstEntity.getStartTime().getTime()));
        if (null != actHiTaskinstEntity.getEndTime()) {
            String approveRuleValue = actHiVarinstDao.getApproveRuleValueBytskId(taskId);
            // 查询审批意见
            ActHiCommentEntity actHiCommentEntity = actHiCommentDao.getActHiCommentByTaskId(taskId);
            approveTaskInfoDTO.setApproveUserId(Integer.valueOf(actHiTaskinstEntity.getAssignee()))
                    .setApproveUserName(employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName())
                    .setApprovedAt(new Timestamp(actHiProcinstEntity.getEndTime().getTime()))
                    .setApproveMemo(actHiCommentEntity.getMessage())
                    .setApproveActionName(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getName())
                    .setApproveActionValue(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getValue());
        }
        return approveTaskInfoDTO;
    }

    @Override
    public List<ActHiTaskinstEntity> getActHiTaskByBizCode(String bizCode) {
        return actHiTaskinstDao.getActHiTaskByBizCode(bizCode);
    }

    /**
     * 待办任务列表
     *
     * @param queryDTO
     * @return
     */
    private Result queryTodoTask(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();
        // 获取用户角色
        List<String> roleIds = roleEntityManager.findGroupsByUser(approveBO.getUserId()).stream().map(Group::getId).collect(Collectors.toList());
        List<String> taskIds = actRuIdentitylinkDao.queryCandidateTasks(approveBO.getUserId(), roleIds);

        List<String> processInstIdList = processInstService.queryProcessInstIdList(queryDTO);
        if (processInstIdList.isEmpty()) {
            processInstIdList.add("none");
        }
        queryDTO.getCondition().setProcInstIdList(processInstIdList);

        IPage<ActRuTaskEntity> actRuTaskEntityList = actRuTaskDao.queryRuTask(queryDTO, taskIds);

        Map<String, ProcessInstDTO> mapProcessInstDTO = new HashMap<>();
        Map<String, ActReNodeEntity> mapActReNodeEntity = new HashMap<>();


        List<ApproveTaskInfoDTO> approveTaskInfoDTOList = actRuTaskEntityList.getRecords().stream().map(actRuTaskEntity -> {
            ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();
            ProcessInstDTO processInstDTO = null;

            if (mapProcessInstDTO.keySet().contains(actRuTaskEntity.getProcInstId())) {
                processInstDTO = mapProcessInstDTO.get(actRuTaskEntity.getProcInstId());
            } else {
                // 流程实例信息
                processInstDTO = processInstService.getProcessInstById(actRuTaskEntity.getProcInstId(), false);
                mapProcessInstDTO.put(actRuTaskEntity.getProcInstId(), processInstDTO);
            }

            // 节点信息
            ActReNodeEntity actReNodeEntity = null;
            String nodeKey = actRuTaskEntity.getProcDefId() + "_" + actRuTaskEntity.getTaskDefKey();
            if (mapActReNodeEntity.keySet().contains(nodeKey)) {
                actReNodeEntity = mapActReNodeEntity.get(nodeKey);
            } else {
                // 节点信息
                actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actRuTaskEntity.getTaskDefKey(), actRuTaskEntity.getProcDefId());
                mapActReNodeEntity.put(nodeKey, actReNodeEntity);
            }

            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeId(actReNodeEntity.getNodeId())
                    .setNodeName(actReNodeEntity.getNodeName());

            String approveUserInfo = "";
            if (!StringUtil.isEmpty(actReNodeEntity.getRoleNameList())) {
                approveUserInfo = approveUserInfo + "角色：" + actReNodeEntity.getRoleNameList();
            }

            processInstDTO.setApprovingUser(approveUserInfo);

            return approveTaskInfoDTO.setTaskId(actRuTaskEntity.getId())
                    .setProcessNodeDTO(processNodeDTO)
                    .setNodeId(actRuTaskEntity.getTaskDefKey())
                    .setProcessInstId(actRuTaskEntity.getProcInstId())
                    .setProcessInstDTO(processInstDTO)
                    .setProcessName(ApproveProcessEnum.getByValue(actRuTaskEntity.getProcessKey()).getProcessName())
                    .setApproved(false)
                    .setApproveRoleName(actReNodeEntity.getRoleNameList())
                    .setApproveUserName(actReNodeEntity.getUsernameList())
                    .setApproveUserInfo(approveUserInfo)
                    .setCreatedAt(new Timestamp(actRuTaskEntity.getCreateTime().getTime()));
        }).collect(Collectors.toList());
        return Result.page(actRuTaskEntityList, approveTaskInfoDTOList);
    }

    private Result queryTodoTask2(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();
        // 获取用户角色
        List<String> roleIds = roleEntityManager.findGroupsByUser(approveBO.getUserId()).stream().map(Group::getId).collect(Collectors.toList());
        List<String> taskIds = actRuIdentitylinkDao.queryCandidateTasks(approveBO.getUserId(), roleIds);

        List<String> processInstIdList = processInstService.queryProcessInstIdList(queryDTO);
        if (processInstIdList.isEmpty()) {
            processInstIdList.add("none");
        }
        queryDTO.getCondition().setProcInstIdList(processInstIdList);

        IPage<ActRuTaskEntity> actRuTaskEntityList = actRuTaskDao.queryRuTask(queryDTO, taskIds);

        Map<String, ProcessInstDTO> mapProcessInstDTO = new HashMap<>();
        Map<String, ActReNodeEntity> mapActReNodeEntity = new HashMap<>();


        List<ApproveTaskActInfoDTO> approveTaskActInfoDTOList = actRuTaskEntityList.getRecords().stream().map(actRuTaskEntity -> {
            ApproveTaskActInfoDTO approveTaskActInfoDTO = new ApproveTaskActInfoDTO();
            ProcessInstDTO processInstDTO = null;

            approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actRuTaskEntity);

            if (mapProcessInstDTO.keySet().contains(actRuTaskEntity.getProcInstId())) {
                processInstDTO = mapProcessInstDTO.get(actRuTaskEntity.getProcInstId());
            } else {
                // 流程实例信息
                processInstDTO = processInstService.getProcessInstById(actRuTaskEntity.getProcInstId(), false);
                mapProcessInstDTO.put(actRuTaskEntity.getProcInstId(), processInstDTO);
            }

            // 节点信息
            ActReNodeEntity actReNodeEntity = null;
            String nodeKey = actRuTaskEntity.getProcDefId() + "_" + actRuTaskEntity.getTaskDefKey();
            if (mapActReNodeEntity.keySet().contains(nodeKey)) {
                actReNodeEntity = mapActReNodeEntity.get(nodeKey);
            } else {
                // 节点信息
                actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actRuTaskEntity.getTaskDefKey(), actRuTaskEntity.getProcDefId());
                mapActReNodeEntity.put(nodeKey, actReNodeEntity);
            }

            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeId(actReNodeEntity.getNodeId())
                    .setNodeName(actReNodeEntity.getNodeName());

            String approveUserInfo = "";
            if (!StringUtil.isEmpty(actReNodeEntity.getRoleNameList())) {
                approveUserInfo = approveUserInfo + "角色：" + actReNodeEntity.getRoleNameList();
            }

            processInstDTO.setApprovingUser(approveUserInfo);

            return approveTaskActInfoDTO.setProcessInstDTO(processInstDTO)
                    .setProcessNodeDTO(processNodeDTO)
                    .setApproved(false)
                    .setApproveUserName(approveUserInfo);
        }).collect(Collectors.toList());

        List<String> roleIds2 = roleEntityManager.findGroupsByUser(approveBO.getUserId()).stream().map(Group::getId).collect(Collectors.toList());

        return Result.page(actRuTaskEntityList, approveTaskActInfoDTOList);
    }

    /**
     * 查询已审批任务
     *
     * @param queryDTO
     * @return
     */
    private Result queryFinshedTask(QueryDTO<ApproveBO> queryDTO) {
        if (StringUtil.isNotEmpty(queryDTO.getCondition().getBizCode())
                || StringUtil.isNotEmpty(queryDTO.getCondition().getBizModule())
                || StringUtil.isNotEmpty(queryDTO.getCondition().getCustomerName())
                || queryDTO.getCondition().getSalesType() > 0
                || (null != queryDTO.getCondition().getTradeTypeList() && queryDTO.getCondition().getTradeTypeList().size() > 0)
        ) {
            List<String> processInstIdList = processInstService.queryProcessInstIdList(queryDTO);
            if (processInstIdList.isEmpty()) {
                processInstIdList.add("none");
            }
            queryDTO.getCondition().setProcInstIdList(processInstIdList);
        }

        IPage<ActHiTaskinstEntity> actHiTaskinstEntityIPage = actHiTaskinstDao.queryFinshedTask(queryDTO);

        Map<String, ProcessInstDTO> mapProcessInstDTO = new HashMap<>();
        Map<String, ActReNodeEntity> mapActReNodeEntity = new HashMap<>();

        List<ApproveTaskInfoDTO> approveTaskInfoDTOList = actHiTaskinstEntityIPage.getRecords().stream().map(actHiTaskinstEntity -> {
            ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();

            ProcessInstDTO processInstDTO = null;
            if (mapProcessInstDTO.keySet().contains(actHiTaskinstEntity.getProcInstId())) {
                processInstDTO = mapProcessInstDTO.get(actHiTaskinstEntity.getProcInstId());
            } else {
                // 流程实例信息
                processInstDTO = processInstService.getProcessInstById(actHiTaskinstEntity.getProcInstId(), false);
                mapProcessInstDTO.put(actHiTaskinstEntity.getProcInstId(), processInstDTO);
            }

            // 节点信息
            ActReNodeEntity actReNodeEntity = null;
            String nodeKey = actHiTaskinstEntity.getProcDefId() + "_" + actHiTaskinstEntity.getTaskDefKey();
            if (mapActReNodeEntity.keySet().contains(nodeKey)) {
                actReNodeEntity = mapActReNodeEntity.get(nodeKey);
            } else {
                // 节点信息
                actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiTaskinstEntity.getTaskDefKey(), actHiTaskinstEntity.getProcDefId());
                mapActReNodeEntity.put(nodeKey, actReNodeEntity);
            }

            // 节点DTO
            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeId(actHiTaskinstEntity.getTaskDefKey())
                    .setNodeName(actHiTaskinstEntity.getName());

            System.out.println(actHiTaskinstEntity.getId());

            String approveRuleValue = actHiVarinstDao.getApproveRuleValueBytskId(actHiTaskinstEntity.getId());

            String approveUserInfo = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();

            processInstDTO.setApprovingUser(approveUserInfo);

            return approveTaskInfoDTO.setTaskId(actHiTaskinstEntity.getId())
                    .setProcessNodeDTO(processNodeDTO)
                    .setApproveRoleName(actReNodeEntity.getRoleNameList())
                    .setNodeId(actHiTaskinstEntity.getTaskDefKey())
                    .setProcessInstId(actHiTaskinstEntity.getProcInstId())
                    .setProcessInstDTO(processInstDTO)
                    .setProcessName(ApproveProcessEnum.getByValue(actHiTaskinstEntity.getProcessKey()).getProcessName())
                    .setApproved(true)
                    .setApproveUserId(Integer.valueOf(actHiTaskinstEntity.getAssignee()))
                    .setApproveUserName(approveUserInfo)
                    .setApproveActionName(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getName())
                    .setApproveActionValue(ApproveActionEnum.getByValue(Integer.valueOf(approveRuleValue)).getValue())
                    .setCreatedAt(new Timestamp(actHiTaskinstEntity.getStartTime().getTime()))
                    .setApprovedAt(new Timestamp(actHiTaskinstEntity.getEndTime().getTime()));
        }).collect(Collectors.toList());
        return Result.page(actHiTaskinstEntityIPage, approveTaskInfoDTOList);
    }

    /**
     * 查询已审批任务
     *
     * @param queryDTO
     * @return
     */
    private Result queryFinshedTask2(QueryDTO<ApproveBO> queryDTO) {
        if (StringUtil.isNotEmpty(queryDTO.getCondition().getBizCode())
                || StringUtil.isNotEmpty(queryDTO.getCondition().getBizModule())
                || StringUtil.isNotEmpty(queryDTO.getCondition().getCustomerName())
                || queryDTO.getCondition().getSalesType() > 0
                || (null != queryDTO.getCondition().getTradeTypeList() && queryDTO.getCondition().getTradeTypeList().size() > 0)
        ) {
            List<String> processInstIdList = processInstService.queryProcessInstIdList(queryDTO);
            if (processInstIdList.isEmpty()) {
                processInstIdList.add("none");
            }
            queryDTO.getCondition().setProcInstIdList(processInstIdList);
        }

        IPage<ActHiTaskinstEntity> actHiTaskinstEntityIPage = actHiTaskinstDao.queryFinshedTask(queryDTO);

        Map<String, ProcessInstDTO> mapProcessInstDTO = new HashMap<>();
        Map<String, ActReNodeEntity> mapActReNodeEntity = new HashMap<>();

        List<ApproveTaskActInfoDTO> approveTaskActInfoDTOList = actHiTaskinstEntityIPage.getRecords().stream().map(actHiTaskinstEntity -> {
            ApproveTaskActInfoDTO approveTaskActInfoDTO = new ApproveTaskActInfoDTO();

            approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actHiTaskinstEntity);

            ProcessInstDTO processInstDTO = null;
            if (mapProcessInstDTO.keySet().contains(actHiTaskinstEntity.getProcInstId())) {
                processInstDTO = mapProcessInstDTO.get(actHiTaskinstEntity.getProcInstId());
            } else {
                // 流程实例信息
                processInstDTO = processInstService.getProcessInstById(actHiTaskinstEntity.getProcInstId(), false);
                mapProcessInstDTO.put(actHiTaskinstEntity.getProcInstId(), processInstDTO);
            }

            // 节点信息
            ActReNodeEntity actReNodeEntity = null;
            String nodeKey = actHiTaskinstEntity.getProcDefId() + "_" + actHiTaskinstEntity.getTaskDefKey();
            if (mapActReNodeEntity.keySet().contains(nodeKey)) {
                actReNodeEntity = mapActReNodeEntity.get(nodeKey);
            } else {
                // 节点信息
                actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiTaskinstEntity.getTaskDefKey(), actHiTaskinstEntity.getProcDefId());
                mapActReNodeEntity.put(nodeKey, actReNodeEntity);
            }

            // 节点DTO
            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeId(actHiTaskinstEntity.getTaskDefKey())
                    .setNodeName(actHiTaskinstEntity.getName());

            //String approveRuleValue = actHiVarinstDao.getApproveRuleValueBytskId(actHiTaskinstEntity.getId());

            String approveUserInfo = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();

            processInstDTO.setApprovingUser(approveUserInfo);

            return approveTaskActInfoDTO.setProcessInstDTO(processInstDTO)
                    .setProcessNodeDTO(processNodeDTO)
                    .setApproved(true)
                    .setApproveUserName(approveUserInfo);
        }).collect(Collectors.toList());
        return Result.page(actHiTaskinstEntityIPage, approveTaskActInfoDTOList);
    }

    /**
     * 查询审批任务
     *
     * @param queryDTO
     * @return
     */
    private Result queryApproveingTask(QueryDTO<ApproveBO> queryDTO) {

        IPage<ActHiTaskinstEntity> actHiTaskinstEntityIPage = actHiTaskinstDao.queryApprovingTask(queryDTO);

        return buildApproveTaskActInfoDTO(actHiTaskinstEntityIPage);
    }

    /**
     * 查询已审批任务
     *
     * @param queryDTO
     * @return
     */
    private Result queryApprovedTask(QueryDTO<ApproveBO> queryDTO) {
        IPage<ActHiTaskinstEntity> actHiTaskinstEntityIPage = actHiTaskinstDao.queryApprovedTask(queryDTO);

        return buildApproveTaskActInfoDTO(actHiTaskinstEntityIPage);
    }

    private Result buildApproveTaskActInfoDTO(IPage<ActHiTaskinstEntity> actHiTaskinstEntityIPage) {
        Map<String, ProcessInstDTO> mapProcessInstDTO = new HashMap<>();
        Map<String, ActReNodeEntity> mapActReNodeEntity = new HashMap<>();

        List<ApproveTaskActInfoDTO> approveTaskActInfoDTOList = actHiTaskinstEntityIPage.getRecords().stream().map(actHiTaskinstEntity -> {
            ApproveTaskActInfoDTO approveTaskActInfoDTO = new ApproveTaskActInfoDTO();

            approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actHiTaskinstEntity);

            ProcessInstDTO processInstDTO = null;
            if (mapProcessInstDTO.keySet().contains(actHiTaskinstEntity.getProcInstId())) {
                processInstDTO = mapProcessInstDTO.get(actHiTaskinstEntity.getProcInstId());
            } else {
                // 流程实例信息
                processInstDTO = processInstService.getProcessInstById(actHiTaskinstEntity.getProcInstId(), false);
                mapProcessInstDTO.put(actHiTaskinstEntity.getProcInstId(), processInstDTO);
            }

            // 节点信息
            ActReNodeEntity actReNodeEntity = null;
            String nodeKey = actHiTaskinstEntity.getProcDefId() + "_" + actHiTaskinstEntity.getTaskDefKey();
            if (mapActReNodeEntity.keySet().contains(nodeKey)) {
                actReNodeEntity = mapActReNodeEntity.get(nodeKey);
            } else {
                // 节点信息
                actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiTaskinstEntity.getTaskDefKey(), actHiTaskinstEntity.getProcDefId());
                mapActReNodeEntity.put(nodeKey, actReNodeEntity);
            }

            // 节点DTO
            ProcessNodeDTO processNodeDTO = new ProcessNodeDTO();
            processNodeDTO.setNodeId(actHiTaskinstEntity.getTaskDefKey())
                    .setNodeName(actHiTaskinstEntity.getName());

            //String approveRuleValue = actHiVarinstDao.getApproveRuleValueBytskId(actHiTaskinstEntity.getId());


            if (null != actHiTaskinstEntity.getAssignee()) {
                String approveUserInfo = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();
                processInstDTO.setApprovingUser(approveUserInfo);
                approveTaskActInfoDTO.setApproveUserName(approveUserInfo);
            }
            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
            approveTaskActInfoDTO.setTtTypeName(TTTypeEnum.getByType(approveTaskActInfoDTO.getTtType()).getDesc());
            processInstDTO.setTtTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc());
            processInstDTO.setTradeTypeName(TTTypeEnum.getByType(processInstDTO.getTtType()).getDesc());
            // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 end

            return approveTaskActInfoDTO.setProcessInstDTO(processInstDTO)
                    .setProcessNodeDTO(processNodeDTO)
                    .setApproved(null != approveTaskActInfoDTO.getEndTime());
        }).collect(Collectors.toList());
        return Result.page(actHiTaskinstEntityIPage, approveTaskActInfoDTOList);
    }

    private ApproveTaskActInfoDTO convertApproveTaskActInfoDTO(ActHiActinstEntity actHiActinstEntity) {
        ApproveTaskActInfoDTO approveTaskActInfoDTO = new ApproveTaskActInfoDTO();
        approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actHiActinstEntity);
        approveTaskActInfoDTO.setId(actHiActinstEntity.getTaskId());
        approveTaskActInfoDTO.setName(actHiActinstEntity.getActName());
        approveTaskActInfoDTO.setTaskDefKey(actHiActinstEntity.getActId());
        approveTaskActInfoDTO.setTaskDefKey(actHiActinstEntity.getActId());
        approveTaskActInfoDTO.setApproved(null != actHiActinstEntity.getEndTime());
        approveTaskActInfoDTO.setApproveResult(actHiActinstEntity.getEndTime() == null ? ApproveResultEnum.APPROVING.getDesc() : actHiActinstEntity.getActionName());

        return approveTaskActInfoDTO;
    }

    private ApproveTaskActInfoDTO convertApproveTaskActInfoDTO(ActHiTaskinstEntity actHiTaskinstEntity) {
        ApproveTaskActInfoDTO approveTaskActInfoDTO = BeanConvertUtils.convert(ApproveTaskActInfoDTO.class, actHiTaskinstEntity);

        // 审批人
        String approveUserName = "";
        if (StrUtil.isNotBlank(actHiTaskinstEntity.getAssignee())) {
            approveUserName = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();
            approveTaskActInfoDTO.setApproveUserName(approveUserName);
        } else {
            ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actHiTaskinstEntity.getTaskDefKey(), actHiTaskinstEntity.getProcDefId());

            if (null != actReNodeEntity && StringUtils.isNotEmpty(actReNodeEntity.getRoleNameList())) {
                approveUserName = "角色：" + actReNodeEntity.getRoleNameList();
            }
        }
        approveTaskActInfoDTO.setApproveUserName(approveUserName);
        approveTaskActInfoDTO.setApproveResult(actHiTaskinstEntity.getEndTime() == null ? ApproveResultEnum.APPROVING.getDesc() : actHiTaskinstEntity.getActionName());

        return approveTaskActInfoDTO;
    }


    public List<LOAReportsExcelDTO> downloadLOAReject() {

        List<LOAReportsExcelDTO> loaReportsExcelDTOS = new ArrayList<>();

        List<ActHiTaskinstEntity> actHiTaskinstEntityList = actHiTaskinstDao.downloadLOAReject();
        for (ActHiTaskinstEntity actHiTaskinstEntity : actHiTaskinstEntityList) {

            LOAReportsExcelDTO loaReportsExcelDTO = new LOAReportsExcelDTO();
            loaReportsExcelDTO.setCode(actHiTaskinstEntity.getBizCode());
            loaReportsExcelDTO.setRejectReason(actHiTaskinstEntity.getMemo());
            loaReportsExcelDTO.setCustomerName(actHiTaskinstEntity.getCustomerName());
            //历史流程实例信息
            ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(actHiTaskinstEntity.getProcInstId());
            //获取用户
            String approveUserName = employEntityManager.findById(actHiTaskinstEntity.getAssignee()).getFirstName();
            loaReportsExcelDTO.setApprover(approveUserName);

            TradeTicketDTO tradeTicketDTO = tradeTicketFacade.getTTDetailInfo(actHiProcinstEntity.getBizCode());

            if (null != tradeTicketDTO) {
                loaReportsExcelDTO.setApproveType(tradeTicketDTO.getApprovalTypeName());
                loaReportsExcelDTO.setSalesType(tradeTicketDTO.getSalesTypeName());

                String modifyContent = "";
                ContractModifyTTDTO contractModifyTTDTO = tradeTicketDTO.getContractModifyTTDTO();
                if (null != contractModifyTTDTO) {
                    modifyContent = contractModifyTTDTO.getModifyContent();
                }

                ContractTransferTTDTO contractTransferTTDTO = tradeTicketDTO.getContractTransferTTDTO();
                if (null != contractTransferTTDTO) {
                    modifyContent = contractTransferTTDTO.getModifyContent();
                }

                if (StringUtil.isNotEmpty(modifyContent)) {
                    List<CompareObjectDTO> compareObjectDTOList = getCompareList(modifyContent);
                    for (CompareObjectDTO compareObjectDTO : compareObjectDTOList) {
                        loaReportsExcelDTO.setChangeField(compareObjectDTO.getName());
                        loaReportsExcelDTO.setBeforeValue(compareObjectDTO.getBefore());
                        loaReportsExcelDTO.setAfterValue(compareObjectDTO.getBefore());
                    }
                }
            }

        }

        return loaReportsExcelDTOS;
    }

    public List<CompareObjectDTO> getCompareList(String modifyContent) {
        List<CompareObjectDTO> compareObjectDTOList = JSON.parseArray(modifyContent, CompareObjectDTO.class);
        return compareObjectDTOList.stream().map(i -> {
            if ("paymentType".equalsIgnoreCase(i.getName())) {
                i.setBefore(PaymentTypeEnum.getByType(Integer.parseInt(i.getBefore())).getDesc());
                i.setAfter(PaymentTypeEnum.getByType(Integer.parseInt(i.getAfter())).getDesc());

            }

            if ("customerId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    CustomerDTO customerBefore = customerFacade.getCustomerById(Integer.parseInt(i.getBefore()));
                    if (customerBefore != null) {
                        i.setBefore(customerBefore.getName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    CustomerDTO customerAfter = customerFacade.getCustomerById(Integer.parseInt(i.getAfter()));
                    if (customerAfter != null) {
                        i.setAfter(customerAfter.getName());
                    }
                }

            }
            if ("goodsId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    SkuDTO goodsBefore = skuFacade.getSkuDTOById(Integer.parseInt(i.getBefore()));
                    if (goodsBefore != null) {
                        i.setBefore(goodsBefore.getFullName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    SkuDTO goodsAfter = skuFacade.getSkuDTOById(Integer.parseInt(i.getAfter()));
                    if (goodsAfter != null) {
                        i.setAfter(goodsAfter.getFullName());
                    }
                }
            }

            //发货库点
            if ("shipWarehouseId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(Integer.parseInt(i.getBefore()));
                    if (factoryWarehouseEntity != null) {
                        i.setBefore(factoryWarehouseEntity.getName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    FactoryWarehouseEntity factoryWarehouseEntity = factoryWarehouseFacade.queryFactoryWarehouseById(Integer.parseInt(i.getAfter()));
                    if (factoryWarehouseEntity != null) {
                        i.setAfter(factoryWarehouseEntity.getName());
                    }
                }
            }

            //目的地,带皮扣重,重量检验
            if ("packageWeight".equalsIgnoreCase(i.getName())
                    || "weightCheck".equalsIgnoreCase(i.getName())
                    || "destination".equalsIgnoreCase(i.getName())
            ) {
                resetSystemValue(i);
            }
            //交提货方式
            if ("deliveryType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(Integer.parseInt(i.getBefore()));
                    if (deliveryTypeEntity != null) {
                        i.setBefore(deliveryTypeEntity.getName());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(Integer.parseInt(i.getAfter()));
                    if (deliveryTypeEntity != null) {
                        i.setAfter(deliveryTypeEntity.getName());
                    }
                }
            }
            //代加工
            if ("oem".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    if ("0".equals(i.getBefore())) {
                        i.setBefore("否");
                    } else {
                        i.setBefore("是");
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    if ("0".equals(i.getAfter())) {
                        i.setAfter("否");
                    } else {
                        i.setAfter("是");
                    }
                }
            }

            //包装是否计算重量
            if ("needPackageWeight".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    if ("0".equals(i.getBefore())) {
                        i.setBefore("否");
                    } else {
                        i.setBefore("是");
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    if ("0".equals(i.getAfter())) {
                        i.setAfter("否");
                    } else {
                        i.setAfter("是");
                    }
                }
            }
            //卖方主体收款账号信息
            if ("supplierAccountId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(Integer.parseInt(i.getBefore()));
                    i.setBefore(customerBankEntity != null ? customerBankEntity.getBankAccountNo() : null);
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    CustomerBankEntity customerBankEntity = customerBankFacade.queryCustomerBankById(Integer.parseInt(i.getAfter()));
                    i.setAfter(customerBankEntity != null ? customerBankEntity.getBankAccountNo() : null);
                }
            }
            //合同类型
            if ("contractType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(ContractTypeEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(ContractTypeEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            //交易类型
            if ("tradeType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(ContractTradeTypeEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(ContractTradeTypeEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            //履约保证金释放方式
            if ("depositReleaseType".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(DepositUseRuleEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }
                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(DepositUseRuleEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            // 付款条件代码
            if ("payConditionId".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    Result result = payConditionFacade.getPayConditionById(Integer.parseInt(i.getBefore()));
                    if (null != result && ResultCodeEnum.OK.getCode() == result.getCode()) {
                        PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(result.getData()), PayConditionEntity.class);
                        i.setBefore(payConditionEntity.getCode());
                    }
                }

                if (StringUtils.isNotBlank(i.getAfter())) {
                    Result result = payConditionFacade.getPayConditionById(Integer.parseInt(i.getAfter()));
                    if (null != result && ResultCodeEnum.OK.getCode() == result.getCode()) {
                        PayConditionEntity payConditionEntity = JSON.parseObject(JSON.toJSONString(result.getData()), PayConditionEntity.class);
                        i.setAfter(payConditionEntity.getCode());
                    }
                }
            }

            //用途
            if ("usage".equalsIgnoreCase(i.getName())) {
                if (StringUtils.isNotBlank(i.getBefore())) {
                    i.setBefore(UsageEnum.getDescByValue(Integer.parseInt(i.getBefore())));
                }
                if (StringUtils.isNotBlank(i.getAfter())) {
                    i.setAfter(UsageEnum.getDescByValue(Integer.parseInt(i.getAfter())));
                }
            }

            if (String.valueOf(i.getBefore()).equalsIgnoreCase(String.valueOf(i.getAfter()))) {
                i.setAfter("");
                i.setSource("");
                i.setUpdateTime("");
            }

            return i;
        }).filter(i -> !getHideList().contains(i.getName())).collect(Collectors.toList());
    }


    private void resetSystemValue(CompareObjectDTO i) {
        if (StringUtils.isNotBlank(i.getBefore())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getBefore()));
            if (systemRuleItemEntity != null) {
                i.setBefore(systemRuleItemEntity.getRuleKey());
            }
        }

        if (StringUtils.isNotBlank(i.getAfter())) {
            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(i.getAfter()));
            if (systemRuleItemEntity != null) {
                i.setAfter(systemRuleItemEntity.getRuleKey());
            }
        }
    }

    public List<String> getHideList() {
        List<String> hideList = new ArrayList<>();
        return hideList;
    }
}
