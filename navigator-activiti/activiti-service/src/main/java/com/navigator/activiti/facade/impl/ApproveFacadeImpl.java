package com.navigator.activiti.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.activiti.facade.ApproveFacade;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.*;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.service.ApproveService;
import com.navigator.activiti.service.FunongApproveService;
import com.navigator.activiti.service.ProcessInstService;
import com.navigator.activiti.service.TaskService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.ServiceLoaderIterator;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/20 20:08
 */
@RestController
@Slf4j
public class ApproveFacadeImpl implements ApproveFacade {
    @Resource
    FunongApproveService funongApproveService;
    @Resource
    ApproveService approveService;
    @Resource
    TaskService taskService;
    @Resource
    ProcessInstService processInstService;


    @Override
    public Result start(ApproveDTO approveDTO) {
        int i = 2;
        return Result.success(funongApproveService.start(approveDTO));

    }

    @Override
    @Transactional
    public Result approve(ApproveDTO approveDTO) {
        log.error("===================approve=======================");
        log.error(JSON.toJSONString(approveDTO));
        return Result.success(funongApproveService.approve(approveDTO));
    }


    @Override
    public Result cancel(ApproveDTO approveDTO) {
        return Result.success(approveService.cancel(approveDTO));

    }

    @Override
    public Result queryApproveTask(QueryDTO<ApproveBO> queryDTO) {
        System.out.println("==========================================");
        System.out.println(JSON.toJSONString(queryDTO));
        Result rtn = taskService.queryApproveTask(queryDTO);

        System.out.println("==========================================");
        System.out.println(JSON.toJSONString(queryDTO));
        return rtn;
    }

    @Override
    public Result queryHiTaskByBizCode(String bizCode) {
        return Result.success(taskService.queryHiTaskByBizCode(bizCode));
    }

    @Override
    public Result getActHiTaskByBizCode(String bizCode) {
        return Result.success(taskService.getActHiTaskByBizCode(bizCode));
    }

    @Override
    public Result queryHiTaskByProcInstId(String procInstId) {
        return Result.success(taskService.queryHiTask(procInstId));
    }

    @Override
    public Result getApproveTaskInfo(String taskId) {
        return Result.success(taskService.getApproveTaskInfo(taskId));
    }

    @Override
    public Result recordBizOperation(RecordBizOperationDTO recordBizOperationDTO) {
        ApproveDTO approveDTO = recordBizOperationDTO.getApproveDTO();
        ApproveResultDTO approveResultDTO = recordBizOperationDTO.getApproveResultDTO();
        funongApproveService.recordBizOperation(approveDTO, approveResultDTO);
        return Result.success();
    }


    @Override
    public Result queryProcessInst(QueryDTO<ApproveBO> queryDTO) {
        return processInstService.queryProcessInst(queryDTO);
    }

    @Override
    public ActHiProcinstEntity getActHiProcinstByBizCode(String bizCode) {
        return processInstService.getActHiProcinstByBizCode(bizCode);
    }

    @Override
    public Result queryProcessInstDetail(String procInstId) {
        ProcessInstDTO processInstDTO = processInstService.getProcessInstById(procInstId, true);
        return Result.success(processInstDTO);
    }

    public void downloadLOAReject(HttpServletResponse response) {
        List<LOAReportsExcelDTO> list = taskService.downloadLOAReject();
        String fileName = "LOA审批记录报表" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(list, null, "LOA审批记录报表", LOAReportsExcelDTO.class, fileName, response);
    }
}
