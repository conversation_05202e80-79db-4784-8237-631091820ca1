package com.navigator.activiti.service;

import com.navigator.activiti.pojo.dto.ApproveDTO;
import com.navigator.activiti.pojo.dto.ApproveResultDTO;
import com.navigator.activiti.pojo.enums.ApproveProcessEnum;

/**
 * <AUTHOR>
 * @date 2021/9/27 9:55
 * <p>
 * ===================================
 * Activiti 内部接口，外部系统不要调用
 * ===================================
 */
public interface ApproveService {

    /**
     * 获取我的待审批任务列表（查询条件ProcKey下拉列表）
     * TaskId，procName，procKey，procInstId，创建人，创建时间，任务开始时间，任务状态
     * 获取流程实例列表（查询条件procKey下拉列表，审批中/审批完成）
     * procInstId，procName，procKey，创建人，创建时间，审批状态
     * 查看流程实例信息
     * procInstId，procName，procKey，流程图url，节点列表，审批历史
     * 业务信息
     * 查看任务信息
     * 流程实例信息：procIstId，procName，procKey，流程图url，节点列表，审批历史
     * 节点信息：taskId，节点名称、节点编号，节点状态
     * 业务信息
     * 对任务进行审批
     * 流程实例信息：procIstId，procName，procKey，流程图url，节点列表，审批历史
     * 业务信息
     * 节点信息：taskId，节点名称、节点编号，节点状态
     * 审批意见
     */


    ApproveResultDTO start(ApproveDTO approveDTO);

    ApproveResultDTO approve(ApproveDTO approveDTO);

    /**
     * 审批撤回
     *
     * @param approveDTO
     * @return
     */
    Boolean cancel(ApproveDTO approveDTO);

    /**
     * 接取任务
     *
     * @param taskId 任务id
     * @param userId 用户
     */
    void claimTask(String taskId, String userId);

    /**
     * 接取任务后 再将任务变成待接取状态
     *
     * @param taskId 任务id
     */
    void unClaimTask(String taskId);

    /**
     * 委托任务  不能直接委托 待领取的任务
     *
     * @param taskId         任务id
     * @param delegateToUser 委托人
     */
    void delegate(String taskId, String delegateToUser);

    /**
     * 取消委托
     *
     * @param taskId
     */
    void resolveTask(String taskId);

    /**
     * 设置流程启动人（可启动流程的人）  设置的是最新部署的流程  根据VERSION（版本）判断
     *
     * @param approveProcessEnum 流程key
     * @param userId             用户id 可为空
     * @param roleId             角色id 可为空
     */
    void addStarter(ApproveProcessEnum approveProcessEnum, String userId, String roleId);

    /**
     * 删除流程启动人
     *
     * @param approveProcessEnum 流程key
     * @param userId             用户id 可为空
     * @param roleId             角色id 可为空
     */
    void deleteStater(ApproveProcessEnum approveProcessEnum, String userId, String roleId);


}
