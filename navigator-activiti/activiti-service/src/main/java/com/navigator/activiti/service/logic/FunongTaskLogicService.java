package com.navigator.activiti.service.logic;

import com.navigator.activiti.dao.ActHiProcinstDao;
import com.navigator.activiti.dao.ActHiTaskinstDao;
import com.navigator.activiti.dao.ActReNodeDao;
import com.navigator.activiti.dao.ActRuTaskDao;
import com.navigator.activiti.pojo.dto.ApproveTaskInfoDTO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.activiti.pojo.entity.ActReNodeEntity;
import com.navigator.activiti.pojo.entity.ActRuTaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FunongTaskLogicService {

    @Autowired
    ActRuTaskDao actRuTaskDao;
    @Autowired
    ActHiTaskinstDao actHiTaskinstDao;
    @Autowired
    ActReNodeDao actReNodeDao;
    @Autowired
    ActHiProcinstDao actHiProcinstDao;

    public ApproveTaskInfoDTO getApproveTaskInfoDTO(String taskId) {
        ApproveTaskInfoDTO approveTaskInfoDTO = null;
        //TODO NEO 待验证
        ActRuTaskEntity actRuTaskEntity = actRuTaskDao.getById(taskId);
        if (null != actRuTaskEntity) {
            approveTaskInfoDTO = getApproveTaskInfoDTO(actRuTaskEntity);
        }
        return approveTaskInfoDTO;
    }

    public ApproveTaskInfoDTO getApproveTaskInfoDTO(ActRuTaskEntity actRuTaskEntity) {
        //参数不能为空
        if (null == actRuTaskEntity) {
            //TODO NEO
            return null;
        }
        ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();

        //获取流程实例信息
        String processInstId = actRuTaskEntity.getProcInstId();
        ActHiProcinstEntity actHiProcinstEntity = actHiProcinstDao.getActHiProcinstByProcInstId(processInstId);
        if (null == actHiProcinstEntity) {
            //TODO NEO
            return null;
        }

        //获取当前节点信息
        ActReNodeEntity actReNodeEntity = actReNodeDao.getActReNodeByNodeId(actRuTaskEntity.getTaskDefKey(), actRuTaskEntity.getProcDefId());

        /*
        String taskId;                          // 任务ID
        Integer nodeUniqueId;                   // 节点记录ID
        String processInstId;                   // 流程实例ID
        ProcessInstDTO processInstDTO;          // 流程实例信息
        String nodeId;                          // 节点ID
        ProcessNodeDTO processNodeDTO;          // 节点名称
        boolean approved;                       // 是否已审批
        Integer approveUserId;                  // 审批用户ID
        String approveUserName;                 // 审批用户名
        String approveActionName;               // 审批动作
        String approveMemo;                     // 审批意见
        Timestamp createdAt;                    // 任务创建时间
        Timestamp approvedAt;                   // 任务审批时间
        */
        approveTaskInfoDTO.setTaskId(actRuTaskEntity.getParentTaskId())
                .setNodeId(actReNodeEntity.getNodeId())
                .setNodeUniqueId(actReNodeEntity.getId())
                .setProcessInstId(actHiProcinstEntity.getProcInstId())
                .setApproved(false)
                .setApproveUserId(-1)
                .setApproveUserName("")
                .setApproveActionName("")
                .setApproveMemo("")
               /* .setCreatedAt(actRuTaskEntity.getCreateTime())*/
                .setApprovedAt(null);
        return approveTaskInfoDTO;
    }

    public ApproveTaskInfoDTO getApproveTaskInfoDTO(ActHiTaskinstEntity actHiTaskinstEntity) {
        ApproveTaskInfoDTO approveTaskInfoDTO = new ApproveTaskInfoDTO();

        return approveTaskInfoDTO;
    }
}
