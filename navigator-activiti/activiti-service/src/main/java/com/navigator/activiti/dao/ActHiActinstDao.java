package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActHiActinstMapper;
import com.navigator.activiti.pojo.entity.ActHiActinstEntity;
import com.navigator.activiti.pojo.enums.BpmsActivityTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 历史节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiActinstDao extends BaseDaoImpl<ActHiActinstMapper, ActHiActinstEntity> {

    public List<ActHiActinstEntity> queryActHiActinstByProcInstId(String procInstId) {
        return queryActHiActinstByProcInstId(procInstId, false);
    }

    public List<ActHiActinstEntity> queryActHiActinstByProcInstId(String procInstId, boolean auto) {
        return this.baseMapper.selectList(
                Wrappers.<ActHiActinstEntity>lambdaQuery()
                        .eq(ActHiActinstEntity::getProcInstId, procInstId)
                        .in(auto, ActHiActinstEntity::getActType, BpmsActivityTypeEnum.getAutoTypeList())
                        .orderByAsc(ActHiActinstEntity::getId)
//                        .orderByAsc(ActHiActinstEntity::getStartTime)
        );
    }

    public Map<String, ActHiActinstEntity> queryAutoActHiActinst(String procInstId) {
        Map<String, ActHiActinstEntity> mapActHiActinstEntity = new HashMap<>();
        List<ActHiActinstEntity> actHiActinstEntityList = queryActHiActinstByProcInstId(procInstId, true);

        for (ActHiActinstEntity actHiActinstEntity : actHiActinstEntityList) {
            mapActHiActinstEntity.put(actHiActinstEntity.getActType(), actHiActinstEntity);
        }
        return mapActHiActinstEntity;
    }

    public ActHiActinstEntity getActHiActinst(String procInstId, String taskId) {
        ActHiActinstEntity actHiActinstEntity = null;
        List<ActHiActinstEntity> list = this.baseMapper.selectList(
                Wrappers.<ActHiActinstEntity>lambdaQuery()
                        .eq(ActHiActinstEntity::getProcInstId, procInstId)
                        .eq(ActHiActinstEntity::getTaskId, taskId)
        );

        if (list.size() > 0) {
            actHiActinstEntity = list.get(0);
        }

        return actHiActinstEntity;
    }

    public void updateActHiActinstEntity(ActHiActinstEntity actHiActinstEntity) {
        this.baseMapper.update(actHiActinstEntity, Wrappers.<ActHiActinstEntity>lambdaUpdate().eq(ActHiActinstEntity::getId, actHiActinstEntity.getId()));
    }


}
