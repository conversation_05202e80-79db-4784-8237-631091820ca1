package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActHiVarinstMapper;
import com.navigator.activiti.pojo.entity.ActHiVarinstEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <p>
 * 历史变量信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiVarinstDao extends BaseDaoImpl<ActHiVarinstMapper, ActHiVarinstEntity> {

    public String getApproveRuleValueBytskId(String taskId) {

        List<ActHiVarinstEntity> actHiVarinstEntityList = this.baseMapper.selectList(
                Wrappers.<ActHiVarinstEntity>lambdaQuery()
                        .eq(ActHiVarinstEntity::getTaskId, taskId)
                        .eq(ActHiVarinstEntity::getName, "approveRuleValue")
        );

        return actHiVarinstEntityList.isEmpty() ? null : actHiVarinstEntityList.get(0).getText();
    }

}
