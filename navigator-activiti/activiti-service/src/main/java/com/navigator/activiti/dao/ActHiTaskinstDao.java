package com.navigator.activiti.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.activiti.mapper.ActHiTaskinstMapper;
import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.activiti.pojo.entity.ActHiTaskinstEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 历史任务流程实例信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActHiTaskinstDao extends BaseDaoImpl<ActHiTaskinstMapper, ActHiTaskinstEntity> {

    public ActHiTaskinstEntity getActHiTaskinstById(String id) {
        List<ActHiTaskinstEntity> actHiTaskinstEntities = this.baseMapper.selectList(Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                .eq(ActHiTaskinstEntity::getId, id));

        return actHiTaskinstEntities.isEmpty() ? null : actHiTaskinstEntities.get(0);
    }

    /**
     * 根据流程实例id获取历史审批任务
     *
     * @param procInstId
     * @return
     */
    public List<ActHiTaskinstEntity> queryActHiTaskinstByProcInstId(String procInstId) {
        return this.baseMapper.selectList(Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                .eq(ActHiTaskinstEntity::getProcInstId, procInstId)
                .orderByAsc(ActHiTaskinstEntity::getStartTime)
        );
    }

    /**
     * 分页查询已审批的历史任务
     *
     * @param queryDTO
     * @return
     */
    public IPage<ActHiTaskinstEntity> queryFinshedTask(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()),
                Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                        .in(null != approveBO.getProcInstIdList() && approveBO.getProcInstIdList().size() > 0, ActHiTaskinstEntity::getProcInstId, approveBO.getProcInstIdList())
                        .eq(ActHiTaskinstEntity::getAssignee, approveBO.getUserId())
                        .eq(StrUtil.isNotBlank(approveBO.getProcessKey()), ActHiTaskinstEntity::getProcessKey, approveBO.getProcessKey())
                        .isNotNull(ActHiTaskinstEntity::getEndTime)
                        .orderByDesc(ActHiTaskinstEntity::getEndTime)
        );
    }

    /**
     * 分页查询已审批的历史任务
     *
     * @param queryDTO
     * @return
     */
    public IPage<ActHiTaskinstEntity> queryApprovedTask(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();
        boolean isSuper = approveBO.isSuper();

        String bizCode = StrUtil.isNotBlank(approveBO.getBizCode()) ? approveBO.getBizCode().trim() : null;
        String customerName = StrUtil.isNotBlank(approveBO.getCustomerName()) ? approveBO.getCustomerName().trim() : null;

        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()),
                Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                        .isNotNull(ActHiTaskinstEntity::getEndTime)
                        .eq(null != approveBO.getCompanyId(), ActHiTaskinstEntity::getCompanyId, approveBO.getCompanyId())
                        .eq(StringUtil.isNotEmpty(approveBO.getBuCode()), ActHiTaskinstEntity::getBuCode, approveBO.getBuCode())
                        .eq(null != approveBO.getCustomerId(), ActHiTaskinstEntity::getCustomerId, approveBO.getCustomerId())
                        .eq(StringUtil.isNotEmpty(approveBO.getDeliveryFactoryCode()), ActHiTaskinstEntity::getFactoryCode, approveBO.getDeliveryFactoryCode())
                        .eq(null != approveBO.getSupplierId(), ActHiTaskinstEntity::getSupplierId, approveBO.getSupplierId())
                        .eq(!isSuper, ActHiTaskinstEntity::getAssignee, approveBO.getUserId())
                        .in(null != approveBO.getProcInstIdList() && approveBO.getProcInstIdList().size() > 0, ActHiTaskinstEntity::getProcInstId, approveBO.getProcInstIdList())
                        .eq(StrUtil.isNotBlank(approveBO.getProcessKey()), ActHiTaskinstEntity::getProcessKey, approveBO.getProcessKey())
                        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                        //in(null != approveBO.getTradeTypeList() && approveBO.getTradeTypeList().size() > 0, ActHiTaskinstEntity::getTradeTypeValue, approveBO.getTradeTypeList())
                        .eq(null != approveBO.getBizType() && 0 != approveBO.getBizType(), ActHiTaskinstEntity::getTtType, approveBO.getBizType())
                        // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 edn
                        .eq(null != approveBO.getSubCategoryId() && approveBO.getSubCategoryId() > 0, ActHiTaskinstEntity::getCategoryId, approveBO.getSubCategoryId())
                        .eq(null != approveBO.getSalesType() && approveBO.getSalesType() > 0, ActHiTaskinstEntity::getSalesType, approveBO.getSalesType())
                        .eq(null != approveBO.getCategory2(), ActHiTaskinstEntity::getCategory2, approveBO.getCategory2())
                        .eq(null != approveBO.getCategory3(), ActHiTaskinstEntity::getCategory3, approveBO.getCategory3())
                        .eq(null != approveBO.getCategory1(), ActHiTaskinstEntity::getCategory1, approveBO.getCategory1())
                        .eq(StringUtil.isNotEmpty(approveBO.getSiteCode()), ActHiTaskinstEntity::getSiteCode, approveBO.getSiteCode())
                        .eq(StringUtil.isNotEmpty(approveBO.getSiteName()), ActHiTaskinstEntity::getSiteCode, approveBO.getSiteName())
                        .like(StringUtil.isNotEmpty(bizCode), ActHiTaskinstEntity::getBizCode, bizCode)
                        .like(StringUtil.isNotEmpty(customerName), ActHiTaskinstEntity::getCustomerName, customerName)
                        .orderByDesc(ActHiTaskinstEntity::getStartTime)
        );
    }

    /**
     * 分页查询已审批的历史任务
     *
     * @param queryDTO
     * @return
     */
    public IPage<ActHiTaskinstEntity> queryApprovingTask(QueryDTO<ApproveBO> queryDTO) {
        ApproveBO approveBO = queryDTO.getCondition();
        if (null == approveBO.getUserRoleIdList()) {
            //防错nop
            approveBO.setUserRoleIdList(new ArrayList<>());
        }
        List<Integer> roleIds = approveBO.getUserRoleIdList();

        String bizCode = StrUtil.isNotBlank(approveBO.getBizCode()) ? approveBO.getBizCode().trim() : null;
        String customerName = StrUtil.isNotBlank(approveBO.getCustomerName()) ? approveBO.getCustomerName().trim() : null;

        LambdaQueryWrapper<ActHiTaskinstEntity> wrapper = Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                //.isNotNull(ActHiTaskinstEntity::getAssignee)
                .isNull(ActHiTaskinstEntity::getEndTime)
                .eq(null != approveBO.getCompanyId(), ActHiTaskinstEntity::getCompanyId, approveBO.getCompanyId())
                .eq(StringUtil.isNotEmpty(approveBO.getBuCode()), ActHiTaskinstEntity::getBuCode, approveBO.getBuCode())
                .eq(null != approveBO.getCustomerId(), ActHiTaskinstEntity::getCustomerId, approveBO.getCustomerId())
                .eq(StringUtil.isNotEmpty(approveBO.getDeliveryFactoryCode()), ActHiTaskinstEntity::getFactoryCode, approveBO.getDeliveryFactoryCode())
                .eq(null != approveBO.getSupplierId(), ActHiTaskinstEntity::getSupplierId, approveBO.getSupplierId())
                .in(null != approveBO.getProcInstIdList() && approveBO.getProcInstIdList().size() > 0, ActHiTaskinstEntity::getProcInstId, approveBO.getProcInstIdList())
                .eq(StrUtil.isNotBlank(approveBO.getProcessKey()), ActHiTaskinstEntity::getProcessKey, approveBO.getProcessKey())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 Start
                //in(null != approveBO.getTradeTypeList() && approveBO.getTradeTypeList().size() > 0, ActHiTaskinstEntity::getTradeTypeValue, approveBO.getTradeTypeList())
                .eq(null != approveBO.getBizType() && 0 != approveBO.getBizType(), ActHiTaskinstEntity::getTtType, approveBO.getBizType())
                // BUGFIX：case-1002824 LOA审批场景错误 Author: wan 2024-12-04 edn
                .eq(null != approveBO.getSubCategoryId() && approveBO.getSubCategoryId() > 0, ActHiTaskinstEntity::getCategoryId, approveBO.getSubCategoryId())
                .eq(null != approveBO.getSalesType() && approveBO.getSalesType() > 0, ActHiTaskinstEntity::getSalesType, approveBO.getSalesType())
                .eq(StringUtil.isNotEmpty(approveBO.getBuCode()), ActHiTaskinstEntity::getBuCode, approveBO.getBuCode())
                .eq(null != approveBO.getCategory2(), ActHiTaskinstEntity::getCategory2, approveBO.getCategory2())
                .eq(null != approveBO.getCategory3(), ActHiTaskinstEntity::getCategory3, approveBO.getCategory3())
                .eq(null != approveBO.getCategory1(), ActHiTaskinstEntity::getCategory1, approveBO.getCategory1())
                .eq(StringUtil.isNotEmpty(approveBO.getSiteCode()), ActHiTaskinstEntity::getSiteCode, approveBO.getSiteCode())
                .eq(StringUtil.isNotEmpty(approveBO.getSiteName()), ActHiTaskinstEntity::getSiteCode, approveBO.getSiteName())
                .like(StringUtil.isNotEmpty(bizCode), ActHiTaskinstEntity::getBizCode, bizCode)
                .like(StringUtil.isNotEmpty(customerName), ActHiTaskinstEntity::getCustomerName, customerName)
                //.in(StringUtil.isNotEmpty(approveBO.getUserId()), ActHiTaskinstEntity::getTaskCandidateRoles, approveBO.getUserId())
                .orderByDesc(ActHiTaskinstEntity::getStartTime);

        wrapper.and(!approveBO.isSuper() && roleIds.size() > 0, qw -> {
                    for (Integer roleId : roleIds) {
                        qw.or().like(ActHiTaskinstEntity::getTaskCandidateRoles, roleId + ",");
                    }
                }
        );

        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), wrapper);
    }


    public void updateByTaskId(ActHiTaskinstEntity actHiTaskinstEntity) {
        this.baseMapper.update(actHiTaskinstEntity, Wrappers.<ActHiTaskinstEntity>lambdaUpdate().eq(ActHiTaskinstEntity::getId, actHiTaskinstEntity.getId()));
    }

    public List<ActHiTaskinstEntity> queryAllTaskInst() {
        return this.baseMapper.selectList(Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                .isNotNull(ActHiTaskinstEntity::getId)
                .orderByAsc(ActHiTaskinstEntity::getId)
        );
    }

    public List<ActHiTaskinstEntity> getActHiTaskByBizCode(String bizCode) {
        return this.baseMapper.selectList(Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                .eq(ActHiTaskinstEntity::getBizCode, bizCode)
                .orderByDesc(ActHiTaskinstEntity::getStartTime));
    }

    //导出驳回审批数据
    public List<ActHiTaskinstEntity> downloadLOAReject() {

        //获取半年前的日期
        LocalDate now = LocalDate.now();
        // 减去半年（6个月）
        LocalDate halfYearAgo = now.minusMonths(6);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = halfYearAgo.format(formatter);
        return this.baseMapper.selectList(Wrappers.<ActHiTaskinstEntity>lambdaQuery()
                .eq(ActHiTaskinstEntity::getActionName, "驳回")
                .gt(ActHiTaskinstEntity::getStartTime, formattedDate)
        );
    }
}
