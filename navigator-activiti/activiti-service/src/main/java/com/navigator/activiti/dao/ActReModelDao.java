package com.navigator.activiti.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActReModelMapper;
import com.navigator.activiti.pojo.entity.ActReModelEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActReModelDao extends BaseDaoImpl<ActReModelMapper, ActReModelEntity> {
    public ActReModelEntity getActReNodeByNodeId(String processKey) {
        List<ActReModelEntity> actReModelEntityList = this.getBaseMapper().selectList(Wrappers.<ActReModelEntity>lambdaQuery()
                .eq(ActReModelEntity::getKey, processKey));

        return actReModelEntityList.isEmpty() ? null : actReModelEntityList.get(0);

    }
}
