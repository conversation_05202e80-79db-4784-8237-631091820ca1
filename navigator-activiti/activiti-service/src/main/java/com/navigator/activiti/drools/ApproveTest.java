package com.navigator.activiti.drools;

import com.navigator.activiti.ActivitiApplication;
import org.junit.jupiter.api.Test;
import org.kie.api.KieBase;
import org.kie.api.io.Resource;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.kie.internal.utils.KieHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = ActivitiApplication.class)
public class ApproveTest {

    @Autowired
    ContractApproveRuleProcessor ruleProcessor;

    String RULE_STR = "package rules\n" +
            "\n" +
            "import com.navigator.activiti.drools.ApproveRule\n" +
            "\n" +
            "lock-on-active true\n" +
            "\n" +
            "//规则一：当合同类型为1，买方类型为1,contractPrice小于600000\n" +
            "rule approve_rule_1\n" +
            "    when\n" +
            "        //$approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice < 600000)\n" +
            "        $approveRule:ApproveRule(contractType == 1 && buyerType == 1)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(1);\n" +
            "        System.out.println(\"规则1匹配\");\n" +
            "end\n" +
            "\n" +
            "//规则二：当合同类型为2，买房类型为1\n" +
            "rule approve_rule_2\n" +
            "    when\n" +
            "        $approveRule:ApproveRule(contractType == 1 && buyerType == 2)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(222222);\n" +
            "        System.out.println(\"规则222222匹配\");\n" +
            "end\n" +
            "\n" +
            "//规则三：当合同类型为1，买房类型为2\n" +
            "rule approve_rule_3\n" +
            "    when\n" +
            "        $approveRule:ApproveRule(contractType == 2 && buyerType == 1)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(3);\n" +
            "        //System.out.println(\"规则3匹配\");\n" +
            "end\n" +
            "\n" +
            "//规则四：当合同类型为2，买房类型为2\n" +
            "rule approve_rule_4\n" +
            "    when\n" +
            "        $approveRule:ApproveRule(contractType == 2 && buyerType == 2)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(4);\n" +
            "        System.out.println(\"规则4匹配\");\n" +
            "end\n" +
            "\n" +
            "//规则五：当合同价格少于300000\n" +
            "rule approve_rule_5\n" +
            "    when\n" +
            "        $approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice >= 600000 && contractPrice < 900000)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(5);\n" +
            "        System.out.println(\"规则5匹配\");\n" +
            "end\n" +
            "\n" +
            "rule approve_rule_6\n" +
            "    when\n" +
            "        $approveRule:ApproveRule(contractType == 1 && buyerType == 1 && contractPrice >= 900000)\n" +
            "    then\n" +
            "        $approveRule.setApproveType(6);\n" +
            "        System.out.println(\"规则6匹配\");\n" +
            "end\n" +
            "\n" +
            "\n" +
            "\n";

    @Test
    public void droolsTest() {

        System.out.println("drools Test start ");

        //DROOLS文本加载模式
        KieHelper helper = new KieHelper();
        //helper.addContent(RULE_STR,ResourceType.DRL);
        //KieSession kieSession = helper.build().newKieSession();
        Resource resource = ResourceFactory.newClassPathResource("rules/approveRule2.drl", "UTF-8");
        helper.addResource(resource);
        KieBase kieBase=helper.build();
        KieSession kieSession=kieBase.newKieSession();


        /*
        //DROOLS脚本加载模式
        KieServices kieServices = KieServices.Factory.get();
        //此方法会加载classpath中的 META-INF/kmodule.xml 文件，根据kmodule.xml中的配置创建kie容器
        KieContainer kieContainer = kieServices.newKieClasspathContainer();
        KieSession kieSession = kieContainer.newKieSession();
        */

        ApproveRule rule1 = new ApproveRule(1, 300000.00f, 1, null);
        ApproveRule rule2 = new ApproveRule(1, 300000.00f, 2, null);
        ApproveRule rule3 = new ApproveRule(2, 300000.00f, 1, null);
        ApproveRule rule4 = new ApproveRule(2, 300000.00f, 2, null);
        ApproveRule rule5 = new ApproveRule(1, 800000.00f, 1, null);
        ApproveRule rule6 = new ApproveRule(1, 1200000.00f, 1, null);

//        kieSession.insert(rule1);
        kieSession.insert(rule2);
//        kieSession.insert(rule3);
        kieSession.insert(rule4);
//        kieSession.insert(rule5);
//        kieSession.insert(rule6);


        int count = kieSession.fireAllRules();

////        Assert.assertEquals(new Integer(1), rule1.getApproveType());
//        Assert.assertEquals(new Integer(222222), rule2.getApproveType());
////        Assert.assertEquals(new Integer(3), rule3.getApproveType());
//        Assert.assertEquals(new Integer(4), rule4.getApproveType());
////        Assert.assertEquals(new Integer(5), rule5.getApproveType());
//        Assert.assertEquals(new Integer(6), rule6.getApproveType());

        kieSession.dispose();
        System.out.println("drools Test end ");
    }

    @Test
    public void droolsTestProcessor() {
        ruleProcessor.run();
    }
}
