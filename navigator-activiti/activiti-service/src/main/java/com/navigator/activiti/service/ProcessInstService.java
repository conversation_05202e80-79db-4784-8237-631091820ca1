package com.navigator.activiti.service;

import com.navigator.activiti.pojo.bo.ApproveBO;
import com.navigator.activiti.pojo.dto.ProcessInstDTO;
import com.navigator.activiti.pojo.entity.ActHiProcinstEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/6 16:05
 */
public interface ProcessInstService {

    /**
     * 流程实例信息
     *
     * @param id
     * @return
     */
    ProcessInstDTO getProcessInstById(String procInstId,boolean loadAll);

    /**
     * 根据业务编号查询历史信息
     *
     * @param bizCode
     * @return
     */
    ActHiProcinstEntity getActHiProcinstByBizCode(String bizCode);

    /**
     * 根据任务id获取流程实例详细信息
     *
     * @param taskId
     * @return
     */
    ProcessInstDTO getProcessInstDetailByTaskId(String taskId);

    /**
     * 查询流程实例 分页
     *
     * @param queryDTO
     * @return
     */
    Result queryProcessInst(QueryDTO<ApproveBO> queryDTO);

    List<String > queryProcessInstIdList(QueryDTO<ApproveBO> queryDTO);

}
