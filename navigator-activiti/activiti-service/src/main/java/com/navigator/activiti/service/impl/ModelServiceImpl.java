package com.navigator.activiti.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.navigator.activiti.constant.ProcessConstants;
import com.navigator.activiti.dao.ActReModelDao;
import com.navigator.activiti.dao.ActReNodeDao;
import com.navigator.activiti.manager.EmployEntityManager;
import com.navigator.activiti.manager.RoleEntityManager;
import com.navigator.activiti.pojo.entity.ActReModelEntity;
import com.navigator.activiti.pojo.entity.ActReNodeEntity;
import com.navigator.activiti.service.ModelService;
import com.navigator.activiti.utils.ImageBpmnModelUtils;
import com.navigator.activiti.utils.ImageGenerator;
import com.navigator.activiti.utils.XmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.BpmnAutoLayout;
import org.activiti.bpmn.converter.BpmnXMLConverter;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.GraphicInfo;
import org.activiti.bpmn.model.Process;
import org.activiti.editor.constants.ModelDataJsonConstants;
import org.activiti.editor.language.json.converter.BpmnJsonConverter;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.persistence.entity.ModelEntityImpl;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.Model;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.image.ProcessDiagramGenerator;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.XMLConstants;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.activiti.editor.constants.ModelDataJsonConstants.MODEL_DESCRIPTION;
import static org.activiti.editor.constants.ModelDataJsonConstants.MODEL_NAME;

/**
 * 流程模型Service实现类
 * <p>
 * Created  by  jinboYu  on  2019/3/28
 */
@Service
@Slf4j
public class ModelServiceImpl implements ModelService {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ProcessEngine processEngine;

    @Resource
    private ActReNodeDao actReNodeDao;

    @Resource
    EmployEntityManager employEntityManager;

    @Resource
    RoleEntityManager roleEntityManager;

    @Resource
    ActReModelDao actReModelDao;

    /**
     * bpmn与json的转换器
     */
    private BpmnJsonConverter bpmnJsonConverter = new BpmnJsonConverter();
    /**
     * bpmn与xml的转换器
     */
    private BpmnXMLConverter bpmnXmlConverter = new BpmnXMLConverter();

    @Override
    public void createModel(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String modelName = "modelName";
        String modelKey = "modelKey";
        String description = "description";

        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode editorNode = objectMapper.createObjectNode();
        editorNode.put("id", "canvas");
        editorNode.put("resourceId", "canvas");
        ObjectNode stencilSetNode = objectMapper.createObjectNode();
        stencilSetNode.put("namespace", "http://b3mn.org/stencilset/bpmn2.0#");
        editorNode.put("stencilset", stencilSetNode);
        Model modelData = repositoryService.newModel();

        ObjectNode modelObjectNode = objectMapper.createObjectNode();
        modelObjectNode.put(MODEL_NAME, modelName);
        modelObjectNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
        modelObjectNode.put(MODEL_DESCRIPTION, description);
        modelData.setMetaInfo(modelObjectNode.toString());
        modelData.setName(modelName);
        modelData.setKey(modelKey);
        //创建默认版本号0,保存后才是1
        modelData.setVersion(0);

        //保存模型
        repositoryService.saveModel(modelData);
        repositoryService.addModelEditorSource(modelData.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
        response.sendRedirect(request.getContextPath() + "/modeler.html?modelId=" + modelData.getId());

    }

    /**
     * 获取流程模型,带分页
     *
     * @param pageNum  当前页
     * @param pageSize 显示数量
     * @return
     */
    @Override
    public List<Model> selectModel(int pageNum, int pageSize) {
        pageNum = (pageNum - 1) * pageSize;
        String selectModelSql = "SELECT * FROM ACT_RE_MODEL WHERE EDITOR_SOURCE_EXTRA_VALUE_ID_ !='' or EDITOR_SOURCE_EXTRA_VALUE_ID_ !=NULL";
        List<Model> modelList = repositoryService.createNativeModelQuery().sql(selectModelSql).listPage(pageNum, pageSize);
        if (modelList != null && modelList.size() > 0) {
            return modelList;
        } else {
            return null;
        }
    }

    /**
     * 保存流程模型
     *
     * @param modelId
     * @param name
     * @param description
     * @param json_xml
     * @param svg_xml
     * @return
     */
    @Override
    public int saveModel(String modelId, String name, String description, String json_xml, String svg_xml) throws Exception {
        // 获取流程模型
        Model model = repositoryService.getModel(modelId);

        ObjectNode modelJson = (ObjectNode) objectMapper.readTree(model.getMetaInfo());

        modelJson.put(MODEL_NAME, name);
        modelJson.put(MODEL_DESCRIPTION, description);
        model.setMetaInfo(modelJson.toString());
        model.setName(name);

        model.setKey(JSON.parseObject(JSON.parseObject(json_xml).get("properties").toString()).get("process_id").toString());
        model.setVersion(model.getVersion() + 1);

        repositoryService.saveModel(model);

        repositoryService.addModelEditorSource(model.getId(), json_xml.getBytes(StandardCharsets.UTF_8));

        InputStream svgStream = new ByteArrayInputStream(svg_xml.getBytes(StandardCharsets.UTF_8));
        TranscoderInput input = new TranscoderInput(svgStream);

        PNGTranscoder transcoder = new PNGTranscoder();
        // 设置输出
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(outStream);

        // 进行转换
        transcoder.transcode(input, output);
        final byte[] result = outStream.toByteArray();
        repositoryService.addModelEditorSourceExtra(model.getId(), result);
        outStream.close();
        return Integer.parseInt(modelId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deployModelByProcessKey(String processKey) throws Exception {
        ActReModelEntity actReModelEntity = actReModelDao.getActReNodeByNodeId(processKey);
        return deployModel(actReModelEntity.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deployModelAll() throws Exception {
        List<ActReModelEntity> actReModelEntities = actReModelDao.list();
        int num = 0;
        for (ActReModelEntity actReModelEntity : actReModelEntities) {
            deployModel(actReModelEntity.getId());
            num++;
        }

        return num;
    }

    /**
     * 根据流程模型ID部署流程
     *
     * @param modelId
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deployModel(String modelId) throws Exception {

        //数据库保存的是模型的元数据，不是XMl格式--需要将元数据转换为XML格式，再进行部署
        Model model = repositoryService.getModel(modelId);
        ObjectNode modelNode = (ObjectNode) new ObjectMapper().readTree(repositoryService.getModelEditorSource(modelId));

        BpmnModel bpmnModel = new BpmnJsonConverter().convertToBpmnModel(modelNode);

        byte[] bytes = new BpmnXMLConverter().convertToXML(bpmnModel, "UTF-8");

        String processName = model.getName() + ".bpmn20.xml";

        //部署流程
        Deployment deployment = repositoryService.createDeployment().name(model.getName()).addString(
                processName, new String(bytes, StandardCharsets.UTF_8)).deploy();

        // 流程定义
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).singleResult();
        JsonNode childShapes = modelNode.get("childShapes");

        for (JsonNode jsonNode : childShapes) {

            String nodeType = String.valueOf(jsonNode.get("stencil").get("id")).replace("\"", "");
            // 不是流则记录
            if (!nodeType.equals("SequenceFlow")) {
                Set userList = new HashSet();
                Set roleList = new HashSet();
                ActReNodeEntity actReNodeEntity = new ActReNodeEntity();

                if (null != jsonNode.get("properties").get("usertaskassignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment").get("assignee")) {
                    userList.add(String.valueOf(jsonNode.get("properties").get("usertaskassignment").get("assignment").get("assignee")).replace("\"", ""));
                }
                if (null != jsonNode.get("properties").get("usertaskassignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment").get("candidateUsers")) {
                    JsonNode candidateUsers = jsonNode.get("properties").get("usertaskassignment").get("assignment").get("candidateUsers");
                    for (JsonNode candidateUser : candidateUsers) {
                        userList.add(String.valueOf(candidateUser.get("value")).replace("\"", ""));
                    }
                }
                if (null != jsonNode.get("properties").get("usertaskassignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment") &&
                        null != jsonNode.get("properties").get("usertaskassignment").get("assignment").get("candidateGroups")) {
                    JsonNode candidateGroups = jsonNode.get("properties").get("usertaskassignment").get("assignment").get("candidateGroups");
                    for (JsonNode candidateGroup : candidateGroups) {
                        roleList.add(String.valueOf(candidateGroup.get("value")).replace("\"", ""));
                    }
                }
                actReNodeEntity.setModelId(modelId)
                        .setModelName(model.getName())
                        .setDeploymentId(deployment.getId())
                        .setNodeType(nodeType)
                        .setNodeId(String.valueOf(jsonNode.get("properties").get("overrideid")).replace("\"", ""))
                        .setNodeName(String.valueOf(jsonNode.get("properties").get("name")).replace("\"", ""))
                        .setDescription(String.valueOf(jsonNode.get("properties").get("documentation")).replace("\"", ""))
                        .setUserList(StringUtils.strip(userList.toString(), "[]"))
                        .setRoleList(StringUtils.strip(roleList.toString(), "[]"))
                        .setProcDefId(processDefinition.getId())
                        .setProcDefName(processDefinition.getName())
                        .setProcDefKey(processDefinition.getKey())
                        .setCreatedAt(DateTime.now().toTimestamp())
                        .setUpdatedAt(DateTime.now().toTimestamp());

                /*
                //性能考虑
                if (CollectionUtil.isNotEmpty(roleDetailList)) {
                    List<EmployEntity> employEntityList = employEntityManager.getUserListByRoleIds(new ArrayList<>(roleDetailList));
                    List<RoleEntity> roleEntityList = roleEntityManager.getRoleByRoleIds(new ArrayList<>(roleDetailList));
                    List<EmployEntity> employEntities = employEntityManager.getUserListByUserIds(new ArrayList<>(userList));

                    if (CollectionUtil.isNotEmpty(employEntities)) {
                        employEntityList.addAll(employEntities);

                    }
                    employEntityList.stream().distinct();

                    try {
                        //TODO NEO 是否有必要？
                        if (CommonListUtil.notNullOrEmpty(employEntityList)) {
                            actReNodeEntity.setUsernameList(String.join(",", employEntityList.stream().map(EmployEntity::getName).collect(Collectors.toList())));
                        }
                        if (CommonListUtil.notNullOrEmpty(roleEntityList)) {
                            actReNodeEntity.setRoleNameList(String.join(",", roleEntityList.stream().map(RoleEntity::getName).collect(Collectors.toList())));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                */


                if (nodeType.equals("UserTask")) {
                    actReNodeEntity.setInstanceType(String.valueOf(jsonNode.get("properties").get("multiinstance_type")).replace("\"", ""));
                }
                if (!nodeType.equals("EndNoneEvent")) {
                    actReNodeEntity.setStatus(String.valueOf(jsonNode.get("properties").get("formkeydefinition")).replace("\"", ""));
                }
                actReNodeDao.save(actReNodeEntity);
            }

        }


        /*//获取流程定义
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).singleResult();
        ExpenseDTO expense = new ExpenseDTO();
        Map map= new HashMap();
        map.put("expense",expense);
        //根据流程定义启动流程
        runtimeService.startProcessInstanceById(processDefinition.getId(),map);*/
        return 1;
    }

    /**
     * 获取模型json数据
     *
     * @param modelId
     * @return
     * @throws Exception
     */
    @Override
    public ObjectNode getEditorJson(String modelId) throws Exception {
        ObjectNode modelNode = null;
        Model model = repositoryService.getModel(modelId);
        if (model != null) {
            if (StringUtils.isNotEmpty(model.getMetaInfo())) {
                modelNode = (ObjectNode) objectMapper.readTree(model.getMetaInfo());
            } else {
                modelNode = objectMapper.createObjectNode();
                modelNode.put("name", model.getName());
            }
            modelNode.put("modelId", model.getId());
            ObjectNode editorJsonNode = (ObjectNode) objectMapper.readTree(
                    new String(repositoryService.getModelEditorSource(model.getId()), StandardCharsets.UTF_8));
            modelNode.put("model", editorJsonNode);

        }
        return modelNode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importModel(MultipartFile file) {
        try {
            // 将bpmn格式的流程文件转BpmnModel类
            XMLInputFactory xif = XmlUtil.createSafeXmlInputFactory();
            //解决sonar xml相关安全性问题
            xif.setProperty(XMLConstants.ACCESS_EXTERNAL_DTD, "");
            xif.setProperty(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
            InputStreamReader xmlIn = new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8);
            XMLStreamReader xtr = xif.createXMLStreamReader(xmlIn);
            BpmnModel bpmnModel = bpmnXmlConverter.convertToBpmnModel(xtr);

            Process mainProcess = bpmnModel.getMainProcess();
            if (null == mainProcess) {
                throw new Exception("No process found in definition " + file.getOriginalFilename());
            }

            // 将文件进行自动布局
            if (bpmnModel.getLocationMap().size() == 0) {
                BpmnAutoLayout bpmnLayout = new BpmnAutoLayout(bpmnModel);
                bpmnLayout.execute();
            }

            String key = mainProcess.getId();
            String name = mainProcess.getName();
            String description = mainProcess.getDocumentation();
            // 将bpmnmodel转成json
            ObjectNode modelNode = bpmnJsonConverter.convertToJson(bpmnModel);

            // 将bpmnModel对象转成字节码部署
//            byte[] modelFileBytes = bpmnXmlConverter.convertToXML(bpmnModel);
//            //设置部署信息，执行部署
//            Deployment deployment = repositoryService
//                    .createDeployment()
//                    .addBytes(key + ProcessConstants.RESOURCE_NAME_SUFFIX, modelFileBytes)
//                    .key(key)
//                    .name(name)
//                    .deploy();

            // 构造model的MateInfo数据
            ObjectNode modelObjectNode = objectMapper.createObjectNode();
            modelObjectNode.put(ProcessConstants.MODEL_NAME, name);
            modelObjectNode.put(ProcessConstants.MODEL_REVISION, 1);
            modelObjectNode.put(ProcessConstants.MODEL_DESCRIPTION, description);
            Model model = new ModelEntityImpl();
            model.setMetaInfo(modelObjectNode.toString());
            model.setName(name);
            model.setKey(key);
//            model.setDeploymentId(deployment.getId());
            // 新增模型数据并增加模型的xml和图片
            String modelId = updateModelAndSource(model, bpmnModel, modelNode);
            this.deployModel(modelId);
            return true;
        } catch (Exception e) {
            log.error("文件解释出错，请检查文件是否为bpmn2.0标准格式", e);
            try {
                throw new Exception("文件解释出错，请检查文件是否为bpmn2.0标准格式");
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public void exportBpmn(String processInstanceId, HttpServletResponse response) {
        FileOutputStream bos = null;

        try {
            HistoricProcessInstance historicProcessInstance = processEngine.getHistoryService().createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            ProcessDefinition processDefinition = processEngine.getRepositoryService().createProcessDefinitionQuery().processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();
            InputStream inputStream = repositoryService.getProcessModel(historicProcessInstance.getProcessDefinitionId());
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            //buff用于存放循环读取的临时数据
            byte[] bpmnBytes = new byte[1024];
            int rc = 0;
            while ((rc = inputStream.read(bpmnBytes, 0, 100)) > 0) {
                swapStream.write(bpmnBytes, 0, rc);
            }
            //in_b为转换之后的结果
            byte[] in_b = swapStream.toByteArray();
            String filename = "E:\\activiti\\" + processDefinition.getName() + ".bpmn";
            // 封装输出流
            bos = new FileOutputStream(filename);
            // 写入流
            bos.write(in_b);
            bos.flush();

//                response.setContentType("application/x-msdownload;");
//                response.setHeader("Content-Disposition",
//                        "attachment; filename=" + filename);
//                response.flushBuffer();
        } catch (IOException e) {
            System.out.println("导出文件失败");
            e.printStackTrace();
        } finally {
            try {
                if (null != bos) {
                    bos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            bos = null;
        }
    }

    @Override
    public void exportPng(String processInstanceId, HttpServletResponse response) throws IOException {
        HistoricProcessInstance processInstance = processEngine.getHistoryService().createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
//        ProcessDefinition processDefinition = processEngine.getRepositoryService().createProcessDefinitionQuery().processDefinitionId(processInstance.getProcessDefinitionId()).singleResult();
//        InputStream processDiagram = processEngine.getRepositoryService().getProcessDiagram(processDefinition.getId());
        // 获取bpmnModel
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        // 使用默认配置获得流程图表生成器，并生成追踪图片字符流
        ProcessDiagramGenerator processDiagramGenerator = processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();

        InputStream imageStream = processDiagramGenerator.generateDiagram(bpmnModel, "png", "宋体", "微软雅黑", "黑体", null, 1.0);
        if (imageStream == null)
            return;
        response.setContentType("image/png");
        BufferedImage image = ImageIO.read(imageStream);
        OutputStream out = response.getOutputStream();
        ImageIO.write(image, "png", out);
        imageStream.close();
        out.close();
    }


    public String updateModelAndSource(Model model, BpmnModel bpmnModel, JsonNode jsonNode) throws IOException {
        repositoryService.saveModel(model);
        byte[] result = null;
        // 保存流程模型的资源(即json数据保存到act_ge_bytearray表)
        this.repositoryService.addModelEditorSource(model.getId(), jsonNode.toString().getBytes(StandardCharsets.UTF_8));
        // 将图片的大小进行缩小
        double scaleFactor = 1.0;
        GraphicInfo diagramInfo = ImageBpmnModelUtils.calculateDiagramSize(bpmnModel);
        if (diagramInfo.getWidth() > 300f) {
            scaleFactor = diagramInfo.getWidth() / 300f;
            ImageBpmnModelUtils.scaleDiagram(bpmnModel, scaleFactor);
        }
        // 按比例生成图片资源
        BufferedImage modelImage = ImageGenerator.createImage(bpmnModel, scaleFactor);
        if (modelImage != null) {
            result = ImageGenerator.createByteArrayForImage(modelImage, "png");
        }
        // 保存图片资源到act_ge_bytearray表
        if (result != null && result.length > 0) {
            this.repositoryService.addModelEditorSourceExtra(model.getId(), result);
        }

        return model.getId();
    }


}
