package com.navigator.activiti.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.activiti.mapper.ActRuIdentitylinkMapper;
import com.navigator.activiti.pojo.entity.ActRuIdentitylinkEntity;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 运行时流程人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Dao
public class ActRuIdentitylinkDao extends BaseDaoImpl<ActRuIdentitylinkMapper, ActRuIdentitylinkEntity> implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 获取用户候选任务ids
     *
     * @param userId
     * @param roleIds
     * @return
     */
    public List<String> queryCandidateTasks(String userId, List<String> roleIds) {
        return this.baseMapper.selectList(
                Wrappers.<ActRuIdentitylinkEntity>lambdaQuery()
//                        .select(ActRuIdentitylinkEntity::getTaskId)
                        .eq(ActRuIdentitylinkEntity::getType, "candidate")
                        .and(wrapper -> wrapper.eq(ActRuIdentitylinkEntity::getUserId, userId).or().in(CollectionUtil.isNotEmpty(roleIds), ActRuIdentitylinkEntity::getGroupId, roleIds))
        ).stream().map(ActRuIdentitylinkEntity::getTaskId).distinct().collect(Collectors.toList());
    }

    public List<ActRuIdentitylinkEntity> queryTaskRuCandidate(String taskId) {
        List<ActRuIdentitylinkEntity> list = new ArrayList<>();
        list = this.baseMapper.selectList(
                Wrappers.<ActRuIdentitylinkEntity>lambdaQuery()
                        .eq(ActRuIdentitylinkEntity::getType, "candidate")
                        .eq(ActRuIdentitylinkEntity::getTaskId, taskId)
                        .isNotNull(ActRuIdentitylinkEntity::getGroupId)
                        .orderByDesc(ActRuIdentitylinkEntity::getId)
        );

        return list;
    }

    public void deleteIdentitylinkByTask(String taskId) {
        this.remove(
                Wrappers.<ActRuIdentitylinkEntity>lambdaQuery()
                        .eq(ActRuIdentitylinkEntity::getTaskId, taskId)
                        .eq(ActRuIdentitylinkEntity::getType, "candidate")
        );
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ActRuIdentitylinkDao.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext != null ? applicationContext.getBean(clazz) : null;
    }

}
