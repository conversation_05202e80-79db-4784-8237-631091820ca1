<?xml version="1.0" encoding="UTF-8" ?>
<kmodule xmlns="http://www.drools.org/xsd/kmodule">

    <!-- packages指定要加载的规则包，对应规则脚本中package指定的包名，有多个包名时逗号隔开 -->
    <kbase name="rules" packages="rules">
        <!-- type指定ksession的类型，stateful 有状态的、stateless 无状态的；default指定是否作为该种类型的默认ksession -->
        <ksession name="statefulKieSession" default="true"/>
        <ksession name="statelessKieSession" type="stateless" default="true"/>
    </kbase>

</kmodule>