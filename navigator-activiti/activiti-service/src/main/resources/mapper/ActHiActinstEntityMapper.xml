<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActHiActinstMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActHiActinstEntity">
                    <id column="ID_" property="id"/>
                    <result column="PROC_DEF_ID_" property="procDefId"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
                    <result column="EXECUTION_ID_" property="executionId"/>
                    <result column="ACT_ID_" property="actId"/>
                    <result column="TASK_ID_" property="taskId"/>
                    <result column="CALL_PROC_INST_ID_" property="callProcInstId"/>
                    <result column="ACT_NAME_" property="actName"/>
                    <result column="ACT_TYPE_" property="actType"/>
                    <result column="ASSIGNEE_" property="assignee"/>
                    <result column="START_TIME_" property="startTime"/>
                    <result column="END_TIME_" property="endTime"/>
                    <result column="DURATION_" property="duration"/>
                    <result column="DELETE_REASON_" property="deleteReason"/>
                    <result column="TENANT_ID_" property="tenantId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, PROC_DEF_ID_, PROC_INST_ID_, EXECUTION_ID_, ACT_ID_, TASK_ID_, CALL_PROC_INST_ID_, ACT_NAME_, ACT_TYPE_, ASSIGNEE_, START_TIME_, END_TIME_, DURATION_, DELETE_REASON_, TENANT_ID_
        </sql>
</mapper>
