<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActReModelMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActReModelEntity">
                    <id column="ID_" property="id"/>
                    <result column="REV_" property="rev"/>
                    <result column="NAME_" property="name"/>
                    <result column="KEY_" property="key"/>
                    <result column="CATEGORY_" property="category"/>
                    <result column="CREATE_TIME_" property="createTime"/>
                    <result column="LAST_UPDATE_TIME_" property="lastUpdateTime"/>
                    <result column="VERSION_" property="version"/>
                    <result column="META_INFO_" property="metaInfo"/>
                    <result column="DEPLOYMENT_ID_" property="deploymentId"/>
                    <result column="EDITOR_SOURCE_VALUE_ID_" property="editorSourceValueId"/>
                    <result column="EDITOR_SOURCE_EXTRA_VALUE_ID_" property="editorSourceExtraValueId"/>
                    <result column="TENANT_ID_" property="tenantId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, REV_, NAME_, KEY_, CATEGORY_, CREATE_TIME_, LAST_UPDATE_TIME_, VERSION_, META_INFO_, DEPLOYMENT_ID_, EDITOR_SOURCE_VALUE_ID_, EDITOR_SOURCE_EXTRA_VALUE_ID_, TENANT_ID_
        </sql>
</mapper>
