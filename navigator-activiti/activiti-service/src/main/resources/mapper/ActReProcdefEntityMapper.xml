<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActReProcdefMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActReProcdefEntity">
                    <id column="ID_" property="id"/>
                    <result column="REV_" property="rev"/>
                    <result column="CATEGORY_" property="category"/>
                    <result column="NAME_" property="name"/>
                    <result column="KEY_" property="key"/>
                    <result column="VERSION_" property="version"/>
                    <result column="DEPLOYMENT_ID_" property="deploymentId"/>
                    <result column="RESOURCE_NAME_" property="resourceName"/>
                    <result column="DGRM_RESOURCE_NAME_" property="dgrmResourceName"/>
                    <result column="DESCRIPTION_" property="description"/>
                    <result column="HAS_START_FORM_KEY_" property="hasStartFormKey"/>
                    <result column="SUSPENSION_STATE_" property="suspensionState"/>
                    <result column="TENANT_ID_" property="tenantId"/>
                    <result column="ENGINE_VERSION_" property="engineVersion"/>
                    <result column="HAS_GRAPHICAL_NOTATION_" property="hasGraphicalNotation"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, SUSPENSION_STATE_, TENANT_ID_, ENGINE_VERSION_, HAS_GRAPHICAL_NOTATION_
        </sql>
</mapper>
