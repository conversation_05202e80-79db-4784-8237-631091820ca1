<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActHiIdentitylinkMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActHiIdentitylinkEntity">
                    <id column="ID_" property="id"/>
                    <result column="GROUP_ID_" property="groupId"/>
                    <result column="TYPE_" property="type"/>
                    <result column="USER_ID_" property="userId"/>
                    <result column="TASK_ID_" property="taskId"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, GROUP_ID_, TYPE_, USER_ID_, TASK_ID_, PROC_INST_ID_
        </sql>
</mapper>
