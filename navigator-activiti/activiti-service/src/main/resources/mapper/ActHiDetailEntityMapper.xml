<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActHiDetailMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActHiDetailEntity">
                    <id column="ID_" property="id"/>
                    <result column="TYPE_" property="type"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
                    <result column="EXECUTION_ID_" property="executionId"/>
                    <result column="TASK_ID_" property="taskId"/>
                    <result column="ACT_INST_ID_" property="actInstId"/>
                    <result column="NAME_" property="name"/>
                    <result column="VAR_TYPE_" property="varType"/>
                    <result column="REV_" property="rev"/>
                    <result column="TIME_" property="time"/>
                    <result column="BYTEARRAY_ID_" property="bytearrayId"/>
                    <result column="DOUBLE_" property="doubleData"/>
                    <result column="LONG_" property="longData"/>
                    <result column="TEXT_" property="text"/>
                    <result column="TEXT2_" property="text2"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, TYPE_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, ACT_INST_ID_, NAME_, VAR_TYPE_, REV_, TIME_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_
        </sql>
</mapper>
