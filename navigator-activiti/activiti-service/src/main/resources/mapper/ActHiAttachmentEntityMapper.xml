<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActHiAttachmentMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActHiAttachmentEntity">
                    <id column="ID_" property="id"/>
                    <result column="REV_" property="rev"/>
                    <result column="USER_ID_" property="userId"/>
                    <result column="NAME_" property="name"/>
                    <result column="DESCRIPTION_" property="description"/>
                    <result column="TYPE_" property="type"/>
                    <result column="TASK_ID_" property="taskId"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
                    <result column="URL_" property="url"/>
                    <result column="CONTENT_ID_" property="contentId"/>
                    <result column="TIME_" property="time"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, REV_, USER_ID_, NAME_, DESCRIPTION_, TYPE_, TASK_ID_, PROC_INST_ID_, URL_, CONTENT_ID_, TIME_
        </sql>
</mapper>
