<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActRuTaskMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActRuTaskEntity">
                    <id column="ID_" property="id"/>
                    <result column="REV_" property="rev"/>
                    <result column="EXECUTION_ID_" property="executionId"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
                    <result column="PROC_DEF_ID_" property="procDefId"/>
                    <result column="NAME_" property="name"/>
                    <result column="PARENT_TASK_ID_" property="parentTaskId"/>
                    <result column="DESCRIPTION_" property="description"/>
                    <result column="TASK_DEF_KEY_" property="taskDefKey"/>
                    <result column="OWNER_" property="owner"/>
                    <result column="ASSIGNEE_" property="assignee"/>
                    <result column="DELEGATION_" property="delegation"/>
                    <result column="PRIORITY_" property="priority"/>
                    <result column="CREATE_TIME_" property="createTime"/>
                    <result column="DUE_DATE_" property="dueDate"/>
                    <result column="CATEGORY_" property="category"/>
                    <result column="SUSPENSION_STATE_" property="suspensionState"/>
                    <result column="TENANT_ID_" property="tenantId"/>
                    <result column="CLAIM_TIME_" property="claimTime"/>
                    <result column="FORM_KEY_" property="formKey"/>
                    <result column="PROC_DEF_KEY_" property="procDefKey"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, REV_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, NAME_, PARENT_TASK_ID_, DESCRIPTION_, TASK_DEF_KEY_, OWNER_, ASSIGNEE_, DELEGATION_, PRIORITY_, CREATE_TIME_, DUE_DATE_, CATEGORY_, SUSPENSION_STATE_, TENANT_ID_, CLAIM_TIME_, FORM_KEY_, PROC_DEF_KEY_
        </sql>
</mapper>
