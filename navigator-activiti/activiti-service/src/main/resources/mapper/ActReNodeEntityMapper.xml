<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActReNodeMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActReNodeEntity">
                    <id column="id" property="id"/>
                    <result column="model_id" property="modelId"/>
                    <result column="model_name" property="modelName"/>
                    <result column="deployment_id" property="deploymentId"/>
                    <result column="node_type" property="nodeType"/>
                    <result column="node_id" property="nodeId"/>
                    <result column="node_name" property="nodeName"/>
                    <result column="description" property="description"/>
                    <result column="instance_type" property="instanceType"/>
                    <result column="role_list" property="roleList"/>
                    <result column="user_list" property="userList"/>
                    <result column="status" property="status"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
                    <result column="proc_def_id" property="procDefId"/>
                    <result column="proc_def_name" property="procDefName"/>
                    <result column="proc_def_key" property="procDefKey"/>
                    <result column="role_name_list" property="roleNameList"/>
                    <result column="username_list" property="usernameList"/>
                    <result column="index" property="index"/>
                    <result column="action" property="action"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            model_id, model_name, deployment_id, node_type, node_id, node_name, description, instance_type, role_list, user_list, status, created_at, updated_at, proc_def_id, proc_def_name, proc_def_key, role_name_list, username_list, index, action, id
        </sql>
</mapper>
