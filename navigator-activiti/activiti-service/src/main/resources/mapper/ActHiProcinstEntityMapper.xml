<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActHiProcinstMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActHiProcinstEntity">
                    <id column="ID_" property="id"/>
                    <result column="PROC_INST_ID_" property="procInstId"/>
                    <result column="BUSINESS_KEY_" property="businessKey"/>
                    <result column="REFER_BIZ_CODE_" property="referBizCode"/>
                    <result column="PROC_DEF_ID_" property="procDefId"/>
                    <result column="START_TIME_" property="startTime"/>
                    <result column="END_TIME_" property="endTime"/>
                    <result column="DURATION_" property="duration"/>
                    <result column="START_USER_ID_" property="startUserId"/>
                    <result column="START_ACT_ID_" property="startActId"/>
                    <result column="END_ACT_ID_" property="endActId"/>
                    <result column="SUPER_PROCESS_INSTANCE_ID_" property="superProcessInstanceId"/>
                    <result column="DELETE_REASON_" property="deleteReason"/>
                    <result column="APPROVE_RULE_VALUE_" property="approveRuleValue"/>
                    <result column="PROC_DEF_NAME_" property="processName"/>
                    <result column="PROC_DEF_KEY_" property="processKey"/>
                    <result column="BIZ_MODULE_" property="bizModule"/>
                    <result column="BIZ_ID_" property="bizId"/>
                    <result column="NAME_" property="name"/>
                    <result column="TENANT_ID_" property="tenantId"/>
        </resultMap>


</mapper>
