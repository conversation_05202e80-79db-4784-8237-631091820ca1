<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.activiti.mapper.ActRuJobMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.activiti.pojo.entity.ActRuJobEntity">
                    <id column="ID_" property="id"/>
                    <result column="REV_" property="rev"/>
                    <result column="TYPE_" property="type"/>
                    <result column="LOCK_EXP_TIME_" property="lockExpTime"/>
                    <result column="LOCK_OWNER_" property="lockOwner"/>
                    <result column="EXCLUSIVE_" property="exclusive"/>
                    <result column="EXECUTION_ID_" property="executionId"/>
                    <result column="PROCESS_INSTANCE_ID_" property="processInstanceId"/>
                    <result column="PROC_DEF_ID_" property="procDefId"/>
                    <result column="EXCEPTION_STACK_ID_" property="exceptionStackId"/>
                    <result column="EXCEPTION_MSG_" property="exceptionMsg"/>
                    <result column="DUEDATE_" property="duedate"/>
                    <result column="REPEAT_" property="repeat"/>
                    <result column="HANDLER_TYPE_" property="handlerType"/>
                    <result column="HANDLER_CFG_" property="handlerCfg"/>
                    <result column="TENANT_ID_" property="tenantId"/>
                    <result column="RETRIES_" property="retries"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            ID_, REV_, TYPE_, LOCK_EXP_TIME_, LOCK_OWNER_, EXCLUSIVE_, EXECUTION_ID_, PROCESS_INSTANCE_ID_, PROC_DEF_ID_, EXCEPTION_STACK_ID_, EXCEPTION_MSG_, DUEDATE_, REPEAT_, HANDLER_TYPE_, HANDLER_CFG_, TENANT_ID_, RETRIES_
        </sql>
</mapper>
