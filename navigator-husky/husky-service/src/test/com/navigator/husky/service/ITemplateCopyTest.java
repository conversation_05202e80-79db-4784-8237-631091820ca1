//package com.navigator.husky.service;
//
//import com.alibaba.fastjson.JSON;
//import com.navigator.common.dto.CompareObjectDTO;
//import com.navigator.husky.dao.*;
//import com.navigator.husky.pojo.entity.*;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.UUID;
//
//@Slf4j
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@ActiveProfiles("dev")
//class ITemplateCopyTest {
//    @Autowired
//    TemplateDao templateDao;
//    @Autowired
//    TemplateGroupDao templateGroupDao;
//    @Autowired
//    TemplateItemDao templateItemDao;
//
//    @Autowired
//    TemplateGroupRelationDao templateGroupRelationDao;
//    @Autowired
//    TemplateItemRelationDao templateItemRelationDao;
//
//    @Autowired
//    TemplateRuleDao templateRuleDao;
//    @Autowired
//    TemplateRuleDetailDao templateRuleDetailDao;
//
//    @Autowired
//    TemplateModifyDao templateModifyDao;
//
//
//    @Test
//    public void test() {
//        String v="11111111";
//        splitV(v);
//    }
//
//    private void splitV(String v) {
//        List<CompareObjectDTO> list = new ArrayList<>();
//        String[] src = {"contractType", "deliveryStartTime", "deliveryEndTime", "customerCode", "creditDays", "packageWeight", "shipWarehouseId", "destination", "weightTolerance", "unitPrice", "deliveryType", "paymentType", "deliveryFactoryCode", "priceEndTime", "depositRate", "addedDepositRate", "goodsPackageId", "goodsSpecId", "weightCheck", "goodsPackageId", "priceEndType", "depositReleaseType", "usage", "vip", "addedDepositRate2"};
//
//        char[] vc = v.toCharArray();
//        for (int i = 0; i < vc.length; i++) {
//            String vv = String.valueOf(vc[i]);
//            if (!"0".equals(vv)) {
//                String field = src[i];
//                String before = "";
//                String after = "";
//                if ("A".equals(vv)) {
//                    before = "0";
//                    after = "5";
//                } else if ("B".equals(vv)) {
//                    before = "0";
//                    after = "10";
//                } else if ("C".equals(vv)) {
//                    before = "5";
//                    after = "0";
//                } else if ("D".equals(vv)) {
//                    before = "5";
//                    after = "10";
//                } else if ("E".equals(vv)) {
//                    before = "10";
//                    after = "0";
//                } else if ("F".equals(vv)) {
//                    before = "10";
//                    after = "5";
//                } else if ("G".equals(vv)) {
//                    before = "0";
//                    after = "15";
//                } else if ("H".equals(vv)) {
//                    before = "15";
//                    after = "0";
//                } else {
//                    before = "0";
//                    after = "3";
//                }
//                list.add(mockObject(field, before, after));
//            }
//        }
//        list.add(mockObject("totalAmount", "10000", "15000"));
//
//        System.out.println(JSON.toJSONString(list));
//
//    }
//
//    private CompareObjectDTO mockObject(String field, String before, String after) {
//        CompareObjectDTO co = new CompareObjectDTO();
//        String name = field;
//
//        String source = "mock";
//
//        if ("totalAmount".equals(field)) {
//            before = "10000";
//            after = "15000";
//        }
//
//        co.setName(name);
//        co.setBefore(before);
//        co.setAfter(after);
//        co.setSource(source);
//        co.setUpdateTime("20230901");
//
//        return co;
//    }
//
//    @Test
//    public void UpdateTName() {
//
//        String oldTcode = "T_12003";
//        String newTCode = "T_12003_Test";
//
//        List<TemplateEntity> listT = templateDao.getTemplateByCode(oldTcode, null);
//        TemplateEntity oldT = listT.get(0);
//
//        oldT.setCode(newTCode);
//        templateDao.updateById(oldT);
//
//    }
//
//
//    @Test
//    public void copyMRule() {
//        String mCode = "";
//        String srcMCode = "";
//
//        TemplateGroupEntity theM = templateGroupDao.getByCode(mCode);
//        TemplateGroupEntity srcM = templateGroupDao.getByCode(srcMCode);
//
//        String ruleCode = srcM.getRuleCode();
//        if (null == ruleCode || ruleCode.length() == 0) {
//            theM.setRuleInfo(srcM.getRuleInfo());
//        } else {
//
//        }
//
//
//    }
//
//    private void copyRule(String ruleCode, String referCode) {
//        TemplateRuleEntity rule = templateRuleDao.getByCode(ruleCode);
//        if (rule != null) {
//
//            TemplateRuleEntity newRule = new TemplateRuleEntity();
//            String newRuleCode = ruleCode + "_RENAME";
//            String newReferCode = "";
//
//            newRule.setRuleCode(newRuleCode);
//            if (null != referCode && referCode.length() > 0) {
//                newReferCode = referCode;
//            } else {
//                newReferCode = rule.getReferCode();
//            }
//            newRule.setReferCode(newReferCode);
//            newRule.setReferType(rule.getReferType());
//            newRule.setConditionVariable(rule.getConditionVariable());
//            newRule.setConditionInfo(rule.getConditionInfo());
//            newRule.setRuleInfo(rule.getRuleInfo());
//            newRule.setMemo("");
//            newRule.setIsDeleted(0);
//
//            templateRuleDao.save(newRule);
//
//            List<TemplateRuleDetailEntity> ruleDetailList = templateRuleDetailDao.getRuleDetailListByRuleCode(ruleCode);
//            for (TemplateRuleDetailEntity templateRuleDetailEntity : ruleDetailList) {
//                TemplateRuleDetailEntity newRuleDetail = new TemplateRuleDetailEntity();
//                newRuleDetail.setRuleCode(newRuleCode);
//                newRuleDetail.setReferCode(newReferCode);
//                newRuleDetail.setReferType(templateRuleDetailEntity.getReferType());
//                newRuleDetail.setConditionVariable(templateRuleDetailEntity.getConditionVariable());
//                newRuleDetail.setConditionVariableId(templateRuleDetailEntity.getConditionVariableId());
//                newRuleDetail.setConditionValue(templateRuleDetailEntity.getConditionValue());
//                newRuleDetail.setPatternRelation(templateRuleDetailEntity.getPatternRelation());
//                newRuleDetail.setRuleInfo(templateRuleDetailEntity.getRuleInfo());
//                newRuleDetail.setLogicRelation(templateRuleDetailEntity.getLogicRelation());
//                newRuleDetail.setLevel(templateRuleDetailEntity.getLevel());
//                newRuleDetail.setSort(templateRuleDetailEntity.getSort());
//                newRuleDetail.setIsDeleted(templateRuleDetailEntity.getIsDeleted());
//                newRuleDetail.setRuleDesc(templateRuleDetailEntity.getRuleDesc());
//                newRuleDetail.setConditionValueInfo(templateRuleDetailEntity.getConditionValueInfo());
//
//                templateRuleDetailDao.save(newRuleDetail);
//            }
//
//        }
//    }
//
//
//}