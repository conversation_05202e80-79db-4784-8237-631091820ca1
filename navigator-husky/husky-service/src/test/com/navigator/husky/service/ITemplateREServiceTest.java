//package com.navigator.husky.service;
//
//import com.navigator.husky.dao.*;
//import com.navigator.husky.pojo.entity.*;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.mvel2.util.Make;
//import org.redisson.misc.Hash;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.*;
//
//@Slf4j
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@ActiveProfiles("dev")
//class ITemplateREServiceTest {
//
//    @Autowired
//    TemplateGroupDao templateGroupDao;
//    @Autowired
//    TemplateGroupRelationDao templateGroupRelationDao;
//    @Autowired
//    TemplateItemRelationDao templateItemRelationDao;
//    @Autowired
//    TemplateRuleDao templateRuleDao;
//    @Autowired
//    TemplateRuleDetailDao templateRuleDetailDao;
//    @Autowired
//    TemplateModifyDao templateModifyDao;
//
//
//    @Test
//    public void copyTE() {
//        /*
//        T_12001	T_12003
//        T_12002	T_12003_Order
//        T_22001	T_22003
//        T_22002	T_22003_Order
//        T_11001	T_11003
//        T_21001	T_21003
//        */
//
//        copyTE("T_12001","T_12003");
//        copyTE("T_12002","T_12003_Order");
//        copyTE("T_22001","T_22003");
//        copyTE("T_22002","T_22003_Order");
//        copyTE("T_11001","T_11003");
//        copyTE("T_21001","T_21003");
//    }
//
//
//    public void copyTE(String srcT, String t) {
//
//        HashMap<String, Integer> fixME = getFixME();
//
//        List<TemplateItemRelationEntity> list = templateItemRelationDao.getTemplateByCode("", t);
//        for (TemplateItemRelationEntity templateItemRelationEntity : list) {
//            String ecode = templateItemRelationEntity.getTemplateItemCode();
//            if (null == fixME.get(ecode)) {
//                templateItemRelationDao.removeById(templateItemRelationEntity.getId());
//            } else {
//            }
//        }
//
//        List<TemplateItemRelationEntity> listSrc = templateItemRelationDao.getTemplateByCode("", srcT);
//        for (TemplateItemRelationEntity src : listSrc) {
//            String ecode = src.getTemplateItemCode();
//            if (null == fixME.get(ecode)) {
//                TemplateItemRelationEntity newser = new TemplateItemRelationEntity();
//                newser.setTemplateCode(t);
//                newser.setTemplateItemCode(ecode);
//                newser.setSort(1);
//                newser.setMemo(srcT);
//                newser.setIsDeleted(0);
//
//                templateItemRelationDao.save(newser);
//            } else {
//            }
//        }
//
//
//        //List<TemplateItemRelationEntity> list=prepareDate();
//    }
//
//    public List<TemplateItemRelationEntity> prepareDate() {
//        List<TemplateItemRelationEntity> list = templateItemRelationDao.getTemplateByCode("", "T_12003");
//        return list;
//    }
//
//    public HashMap<String, Integer> getFixME() {
//        HashMap<String, Integer> m = new HashMap<>();
//        m.put("E1001", 1);
//        m.put("E1002", 1);
//        m.put("E1003", 1);
//        m.put("E1004", 1);
//        m.put("E1005", 1);
//        m.put("E1006", 1);
//        m.put("E1007", 1);
//        m.put("E1008", 1);
//        m.put("E1009", 1);
//        m.put("E1010", 1);
//        m.put("E1011", 1);
//        m.put("E1012", 1);
//        m.put("E1013", 1);
//        m.put("E1014", 1);
//        m.put("E1015", 1);
//        m.put("E1016", 1);
//        m.put("E1017", 1);
//        m.put("E1018", 1);
//        m.put("E1019", 1);
//        m.put("E1020", 1);
//        m.put("E1021", 1);
//        m.put("E1022", 1);
//        m.put("E1023", 1);
//        m.put("E1024", 1);
//        m.put("E1025", 1);
//        m.put("E1026", 1);
//        m.put("E1027", 1);
//        m.put("E1028", 1);
//        m.put("E1029", 1);
//        m.put("E1030", 1);
//        m.put("E1031", 1);
//        m.put("E1032", 1);
//        m.put("E1033", 1);
//        m.put("E1034", 1);
//        m.put("E1035", 1);
//        m.put("E1036", 1);
//        m.put("E1037", 1);
//        m.put("E1038", 1);
//        m.put("E1039", 1);
//        m.put("E1040", 1);
//        m.put("E1041", 1);
//        m.put("E1042", 1);
//        m.put("E1043", 1);
//        m.put("E1044", 1);
//        m.put("E1045", 1);
//        m.put("E1046", 1);
//        m.put("E1047", 1);
//        m.put("E1048", 1);
//        m.put("E1049", 1);
//        m.put("E1050", 1);
//        m.put("E1051", 1);
//        m.put("E1052", 1);
//        m.put("E1053", 1);
//        m.put("E1054", 1);
//        m.put("E1055", 1);
//        m.put("E1056", 1);
//        m.put("E1057", 1);
//        m.put("E1058", 1);
//        m.put("E1059", 1);
//        m.put("E1060", 1);
//        m.put("E1061", 1);
//        m.put("E1062", 1);
//        m.put("E1063", 1);
//        m.put("E1064", 1);
//        m.put("E1065", 1);
//        m.put("E1066", 1);
//        m.put("E1067", 1);
//        m.put("E1068", 1);
//        m.put("E1069", 1);
//        m.put("E1070", 1);
//        m.put("E1071", 1);
//        m.put("E1072", 1);
//        m.put("E1073", 1);
//        m.put("E1074", 1);
//        m.put("E1075", 1);
//        m.put("E1076", 1);
//        m.put("E1077", 1);
//        m.put("E1078", 1);
//        m.put("E1079", 1);
//        m.put("E1080", 1);
//        m.put("E1081", 1);
//        m.put("E1082", 1);
//        m.put("E1083", 1);
//        m.put("E1084", 1);
//        m.put("E1085", 1);
//        m.put("E1086", 1);
//        m.put("E1087", 1);
//        m.put("E1088", 1);
//        m.put("E1089", 1);
//        m.put("E1090", 1);
//        m.put("E1091", 1);
//        m.put("E1092", 1);
//        m.put("E1093", 1);
//        m.put("E1094", 1);
//        m.put("E1095", 1);
//        m.put("E1096", 1);
//        m.put("E1097", 1);
//        m.put("E1098", 1);
//        m.put("E1099", 1);
//        m.put("E1100", 1);
//        m.put("M1001", 1);
//        m.put("M1002", 1);
//        m.put("M1003", 1);
//        m.put("M1004", 1);
//        m.put("M1005", 1);
//        m.put("M1006", 1);
//        m.put("M1007", 1);
//        m.put("M1008", 1);
//        m.put("M1009", 1);
//        m.put("M1010", 1);
//        m.put("M1011", 1);
//        m.put("M1012", 1);
//        m.put("M1013", 1);
//        m.put("M1014", 1);
//        m.put("M1015", 1);
//        m.put("M1016", 1);
//        m.put("M1017", 1);
//        m.put("M1018", 1);
//        m.put("M1019", 1);
//        m.put("M1020", 1);
//        m.put("M1021", 1);
//        m.put("M1022", 1);
//        m.put("M1023", 1);
//        m.put("M1024", 1);
//        m.put("M1025", 1);
//        m.put("M1026", 1);
//        m.put("M1027", 1);
//        m.put("M1028", 1);
//        m.put("M1029", 1);
//        m.put("M1030", 1);
//        m.put("M1031", 1);
//        m.put("M1032", 1);
//        m.put("M1033", 1);
//        m.put("M1034", 1);
//        m.put("M1035", 1);
//        m.put("M1036", 1);
//        m.put("M1037", 1);
//        m.put("M1038", 1);
//        m.put("M1039", 1);
//        m.put("M1040", 1);
//        m.put("M1041", 1);
//        m.put("M1042", 1);
//        m.put("M1043", 1);
//        m.put("M1044", 1);
//        m.put("M1045", 1);
//        m.put("M1046", 1);
//        m.put("M1047", 1);
//        m.put("M1048", 1);
//        m.put("M1049", 1);
//        m.put("M1050", 1);
//
//
//        return m;
//    }
//
//
//}