//package com.navigator.husky.service;
//
//import com.navigator.husky.dao.TemplateModifyDao;
//import com.navigator.husky.pojo.entity.TemplateModifyEntity;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@ActiveProfiles("dev")
//public class TemplateModifyTest {
//    @Autowired
//    TemplateModifyDao templateModifyDao;
//
//    @Test
//    public void test() {
//        List<TemplateModifyEntity> list = templateModifyDao.list();
//
//        HashMap<String, String> m1 = new HashMap<>();
//        HashMap<String, String> m2 = new HashMap<>();
//
//        List<String> dic = getMC();
//        for (String s : dic) {
//            String[] ss = s.split("-");
//            m1.put(ss[0], ss[1]);
//            m2.put(ss[0], ss[2]);
//        }
//
//        HashMap<String, String> m = new HashMap<>();
//
//        StringBuilder sb1 = new StringBuilder();
//        StringBuilder sb2 = new StringBuilder();
//
//
//        for (TemplateModifyEntity tm : list) {
//            String mcode = tm.getMcode();
//            String rcode = tm.getRcode();
//
//            sb1 = new StringBuilder();
//            sb2 = new StringBuilder();
//
//            if ("1".equals(tm.getA())) {
//                sb1.append(m1.get("a")).append(",");
//                sb2.append(m2.get("a")).append(",");
//            }
//            if ("1".equals(tm.getB())) {
//                sb1.append(m1.get("b")).append(",");
//                sb2.append(m2.get("b")).append(",");
//            }
//            if ("1".equals(tm.getC())) {
//                sb1.append(m1.get("c")).append(",");
//                sb2.append(m2.get("c")).append(",");
//            }
//            if ("1".equals(tm.getD())) {
//                sb1.append(m1.get("d")).append(",");
//                sb2.append(m2.get("d")).append(",");
//            }
//            if ("1".equals(tm.getE())) {
//                sb1.append(m1.get("e")).append(",");
//                sb2.append(m2.get("e")).append(",");
//            }
//            if ("1".equals(tm.getF())) {
//                sb1.append(m1.get("f")).append(",");
//                sb2.append(m2.get("f")).append(",");
//            }
//            if ("1".equals(tm.getG())) {
//                sb1.append(m1.get("g")).append(",");
//                sb2.append(m2.get("g")).append(",");
//            }
//            if ("1".equals(tm.getH())) {
//                sb1.append(m1.get("h")).append(",");
//                sb2.append(m2.get("h")).append(",");
//            }
//            if ("1".equals(tm.getI())) {
//                sb1.append(m1.get("i")).append(",");
//                sb2.append(m2.get("i")).append(",");
//            }
//            if ("1".equals(tm.getJ())) {
//                sb1.append(m1.get("j")).append(",");
//                sb2.append(m2.get("j")).append(",");
//            }
//            if ("1".equals(tm.getK())) {
//                sb1.append(m1.get("k")).append(",");
//                sb2.append(m2.get("k")).append(",");
//            }
//            if ("1".equals(tm.getL())) {
//                sb1.append(m1.get("l")).append(",");
//                sb2.append(m2.get("l")).append(",");
//            }
//            if ("1".equals(tm.getM())) {
//                sb1.append(m1.get("m")).append(",");
//                sb2.append(m2.get("m")).append(",");
//            }
//            if ("1".equals(tm.getN())) {
//                sb1.append(m1.get("n")).append(",");
//                sb2.append(m2.get("n")).append(",");
//            }
//            if ("1".equals(tm.getO())) {
//                sb1.append(m1.get("o")).append(",");
//                sb2.append(m2.get("o")).append(",");
//            }
//            if ("1".equals(tm.getP())) {
//                sb1.append(m1.get("p")).append(",");
//                sb2.append(m2.get("p")).append(",");
//            }
//            if ("1".equals(tm.getQ())) {
//                sb1.append(m1.get("q")).append(",");
//                sb2.append(m2.get("q")).append(",");
//            }
//            if ("1".equals(tm.getR())) {
//                sb1.append(m1.get("r")).append(",");
//                sb2.append(m2.get("r")).append(",");
//            }
//            if ("1".equals(tm.getS())) {
//                sb1.append(m1.get("s")).append(",");
//                sb2.append(m2.get("s")).append(",");
//            }
//            if ("1".equals(tm.getT())) {
//                sb1.append(m1.get("t")).append(",");
//                sb2.append(m2.get("t")).append(",");
//            }
//            if ("1".equals(tm.getU())) {
//                sb1.append(m1.get("u")).append(",");
//                sb2.append(m2.get("u")).append(",");
//            }
//            if ("1".equals(tm.getV())) {
//                sb1.append(m1.get("v")).append(",");
//                sb2.append(m2.get("v")).append(",");
//            }
//            if ("1".equals(tm.getW())) {
//                sb1.append(m1.get("w")).append(",");
//                sb2.append(m2.get("w")).append(",");
//            }
//            if ("1".equals(tm.getX())) {
//                sb1.append(m1.get("x")).append(",");
//                sb2.append(m2.get("x")).append(",");
//            }
//            if ("1".equals(tm.getY())) {
//                sb1.append(m1.get("y")).append(",");
//                sb2.append(m2.get("y")).append(",");
//            }
//
//            String content = sb1.toString();
//            String rule = sb2.toString();
//
//
//            if (null != content && content.length() > 0) {
//                tm.setContent(content.substring(0, content.length() - 1));
//            }
//            if (null != rule && content.length() > 0) {
//                tm.setRuleinfo(rule.substring(0, rule.length() - 1));
//            }
//
//            templateModifyDao.updateById(tm);
//        }
//
//
//        System.out.println(m);
//
//    }
//
//    private List<String> getMC() {
//        List<String> list = new ArrayList<>();
//        list.add("a-合同类型-contractType");
//        list.add("b-开始交货日-deliveryStartTime");
//        list.add("c-截止交货日-deliveryEndTime");
//        list.add("d-买方主体-customerCode");
//        list.add("e-赊销账期-creditDays");
//        list.add("f-袋皮扣重-packageWeight");
//        list.add("g-发货库点-shipWarehouseId");
//        list.add("h-目的港-destination");
//        list.add("i-溢短装-weightTolerance");
//        list.add("j-含税单价-unitPrice");
//        list.add("k-合同提货类型对应交提货方式-deliveryType");
//        list.add("l-合同付款方式-paymentType");
//        list.add("m-合同交货工厂-deliveryFactoryCode");
//        list.add("n-点价截止日期-priceEndTime");
//        list.add("o-履约保证金比例-depositRate");
//        list.add("p-履约保证金点价后补缴-addedDepositRate");
//        list.add("q-包装-goodsPackageId");
//        list.add("r-规格-goodsSpecId");
//        list.add("s-重量检验-weightCheck");
//        list.add("t-货物-goodsPackageId");
//        list.add("u-点价截止日期类型-priceEndType");
//        list.add("v-履约保证金释放方式-depositReleaseType");
//        list.add("w-用途-usage");
//        list.add("x-专属客户-vip");
//        list.add("y-追加履约保证金比例-addedDepositRate2");
//
//        return list;
//    }
//
//
//}
