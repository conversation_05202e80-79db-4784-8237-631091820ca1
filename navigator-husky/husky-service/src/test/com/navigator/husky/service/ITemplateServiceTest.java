//package com.navigator.husky.service;
//
//import com.navigator.husky.dao.*;
//import com.navigator.husky.pojo.entity.*;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.mvel2.util.Make;
//import org.redisson.misc.Hash;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.UUID;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@Slf4j
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@ActiveProfiles("dev")
//class ITemplateServiceTest {
//
//    @Autowired
//    TemplateGroupDao templateGroupDao;
//    @Autowired
//    TemplateGroupRelationDao templateGroupRelationDao;
//
//    @Autowired
//    TemplateRuleDao templateRuleDao;
//    @Autowired
//    TemplateRuleDetailDao templateRuleDetailDao;
//    @Autowired
//    TemplateModifyDao templateModifyDao;
//
//
//
//    @Test
//    public void BatchAddModifyM() {
//        /**
//         * 不要随便运行
//         * 批量新增“普通变更”的条款组
//         */
//        if(1==1){
//            return;
//        }
//        HashMap<String, String> m0 = getAllM();
//        List<String> mm = getTM();
//
//
//        for (String s : mm) {
//            String[] ss = s.split("-");
//            String srcTitle = ss[0];
//            String srcMcode = ss[1];
//            String srcTcode = ss[2];
//            String tcode = ss[3];
//            String category = ss[4];
//            String conType = ss[5];
//            String csc = ss[6];
//            String title = ss[7];
//            String name = ss[8];
//            String status = ss[9];
//
//            copyM(srcMcode, s);
//
//        }
//
//
//    }
//
//    @Test
//    public void BatchAddT_M() {
//
//        /**
//         * 不要随便运行
//         * 批量新增“普通变更”的模板-条款组的关联关系
//         */
//        if(1==1){
//            return;
//        }
//        List<String> mm = getTM();
//
//
//        int i = 10;
//
//        for (String s : mm) {
//
//
//            String[] ss = s.split("-");
//            String srcTitle = ss[0];
//            String srcMcode = ss[1];
//            String srcTcode = ss[2];
//            String tcode = ss[3];
//            String category = ss[4];
//            String conType = ss[5];
//            String csc = ss[6];
//            String mcode = ss[7];
//            String title = ss[8];
//            String name = ss[9];
//            String status = ss[10];
//
//            if ("M_Neo_0036".equals(srcMcode)) {
//                addFixT_M(tcode);
//                i = 10;
//            }
//
//            if ("1".equals(status)) {
//                TemplateGroupRelationEntity tg = new TemplateGroupRelationEntity();
//                tg.setTemplateCode(tcode);
//                tg.setTemplateGroupCode(mcode);
//                tg.setIsDeleted(0);
//                tg.setSort(i);
//                templateGroupRelationDao.save(tg);
//                i = i + 1;
//            }
//        }
//
//
//    }
//
//    @Test
//    public void AddModifyRule() {
//
//        /**
//         * 不要随便运行
//         * 批量更新“普通变更”的条件规则
//         */
//        if(1==1){
//            return;
//        }
//
//        /**
//         * templateModifyEntity 源于这个表的数据
//         */
//        List<TemplateModifyEntity> list = templateModifyDao.list();
//        for (TemplateModifyEntity templateModifyEntity : list) {
//            String mcode = templateModifyEntity.getMcode();
//            String conditionText = templateModifyEntity.getContent();
//            String ruleText = templateModifyEntity.getRuleinfo();
//
//            if (null != conditionText && conditionText.length() > 0) {
//                AddModifyRule(mcode, conditionText, ruleText);
//            }
//        }
//    }
//
//
//    @Test
//    public void AddModifyRule(String mcode, String conditionText, String ruleText) {
//
//        TemplateRuleEntity r = new TemplateRuleEntity();
//        TemplateRuleDetailEntity rd = new TemplateRuleDetailEntity();
//
//        String ruleCode = UUID.randomUUID().toString();
//        String conditionInfo = "合同修改字段包含[";
//
//        String ruleInfo = getRuleInfo2(ruleText);
//
//        //ruleInfo = "[" + ruleInfo + "] contains mapBizData.get(\"modifyList\") ";
//
//        r.setRuleCode(ruleCode);
//        r.setReferCode(mcode);
//        r.setReferType(2);
//        r.setConditionVariable("modifyList");
//        r.setConditionInfo(conditionInfo + conditionText + "];");
//        r.setRuleInfo(ruleInfo);
//        r.setMemo("");
//        r.setIsDeleted(0);
//
//        rd.setRuleCode(ruleCode);
//        rd.setReferCode(mcode);
//        rd.setReferType(2);
//        rd.setConditionVariable("modifyList");
//        rd.setConditionVariableId(137);
//        rd.setConditionValue(ruleText);
//        rd.setPatternRelation("contains");
//        rd.setRuleInfo(ruleInfo);
//        rd.setLogicRelation("");
//        rd.setLevel(1);
//        rd.setSort(1);
//        rd.setIsDeleted(0);
//        rd.setRuleDesc(conditionInfo + conditionText + "]");
//        rd.setConditionValueInfo(conditionText);
//
//        templateRuleDao.save(r);
//        templateRuleDetailDao.save(rd);
//
//        TemplateGroupEntity m = new TemplateGroupEntity();
//        List<TemplateGroupEntity> list = templateGroupDao.getTemplateGroupByCode(mcode);
//        m = list.get(0);
//
//        m.setRuleInfo(ruleInfo);
//        m.setRuleCode(ruleCode);
//
//        templateGroupDao.updateById(m);
//
//    }
//
//    private void addFixT_M(String tcode) {
//        TemplateGroupRelationEntity tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1009");
//        tg.setSort(1);
//        tg.setIsDeleted(0);
//
//        templateGroupRelationDao.save(tg);
//
//        tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1003");
//        tg.setSort(2);
//        tg.setIsDeleted(0);
//        templateGroupRelationDao.save(tg);
//
//        tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1004");
//        tg.setSort(3);
//        tg.setIsDeleted(0);
//        templateGroupRelationDao.save(tg);
//
//        tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1005");
//        tg.setSort(4);
//        tg.setIsDeleted(0);
//        templateGroupRelationDao.save(tg);
//
//        tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1014");
//        tg.setSort(5);
//        tg.setIsDeleted(0);
//        templateGroupRelationDao.save(tg);
//
//        tg = new TemplateGroupRelationEntity();
//        tg.setTemplateCode(tcode);
//        tg.setTemplateGroupCode("M1013");
//        tg.setSort(99);
//        tg.setIsDeleted(0);
//        templateGroupRelationDao.save(tg);
//
//
//    }
//
//    private void copyM(String code, String s) {
//
//        String[] ss = s.split("-");
//        String srcTitle = ss[0];
//        String srcMcode = ss[1];
//        String srcTcode = ss[2];
//        String tcode = ss[3];
//        String category = ss[4];
//        String conType = ss[5];
//        String csc = ss[6];
//        String mcode = ss[7];
//        String title = ss[8];
//        String name = ss[9];
//        String status = ss[10];
//
//        List<TemplateGroupEntity> list = templateGroupDao.getTemplateGroupByCode(code);
//        System.out.println(list);
//
//        TemplateGroupEntity src = list.get(0);
//
//        TemplateGroupEntity tg = new TemplateGroupEntity();
//        tg.setCode(mcode);
//        tg.setRealGroupCode(srcMcode);
//        tg.setTitle(title);
//        tg.setName(name);
//        tg.setNeedNum(1);
//        tg.setStatus(Integer.valueOf(status));
//        tg.setMemo(srcTcode + ";" + tcode);
//
//        templateGroupDao.save(tg);
//
//    }
//
//    private HashMap getAllM() {
//        HashMap<String, String> mapM = new HashMap<>();
//        mapM.put("交货工厂", "M_Neo_0036");
//        mapM.put("货物名称", "M_Neo_0005");
//        mapM.put("货物重量", "M_Neo_0006");
//        mapM.put("质量指标", "M_Neo_0007");
//        mapM.put("包装及标识要求", "M_Neo_0008");
//        mapM.put("包装", "M_Neo_032");
//        mapM.put("合同价格", "M_Neo_0009");
//        mapM.put("全额货款", "M_Neo_0010");
//        mapM.put("暂定全额货款", "M_Neo_0011");
//        mapM.put("交货时间/地点", "M_Neo_0012");
//        mapM.put("发货时间/交货地点", "M_Neo_0013");
//        mapM.put("交货方式", "M_Neo_0014");
//        mapM.put("交货费用", "M_Neo_0015");
//        mapM.put("交货运输方式", "M_Neo_0016");
//        mapM.put("重量、质量检验", "M_Neo_0017");
//        mapM.put("重量检验", "M_Neo_0035");
//        mapM.put("付款", "M_Neo_0018");
//        mapM.put("履约保证金", "M_Neo_0019");
//        mapM.put("追加履约保证金", "M_Neo_0020");
//        mapM.put("迟延付款/提货责任", "M_Neo_0021");
//        mapM.put("所有权及风险转移", "M_Neo_0022");
//        mapM.put("完整协议", "M_Neo_0023");
//        mapM.put("不可抗力", "M_Neo_0024");
//        mapM.put("终止", "M_Neo_0025");
//        mapM.put("责任及限制", "M_Neo_0026");
//        mapM.put("争议解决", "M_Neo_0027");
//        mapM.put("通知", "M_Neo_0028");
//        return mapM;
//    }
//
//
//    private List<String> getTM() {
//        List<String> list = new ArrayList<>();
//        list.add("交货工厂-M_Neo_0036-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0036_MSC-交货工厂变更为-交货工厂_普通变更_豆粕大合同-1");
//        list.add("货物名称-M_Neo_0005-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0005_MSC-货物名称变更为-货物名称_普通变更_豆粕大合同-0");
//        list.add("货物重量-M_Neo_0006-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0006_MSC-货物重量变更为-货物重量_普通变更_豆粕大合同-1");
//        list.add("质量指标-M_Neo_0007-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0007_MSC-质量指标变更为-质量指标_普通变更_豆粕大合同-1");
//        list.add("包装及标识要求-M_Neo_0008-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0008_MSC-包装及标识要求变更为-包装及标识要求_普通变更_豆粕大合同-1");
//        list.add("包装-M_Neo_032-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_032_MSC-包装变更为-包装_普通变更_豆粕大合同-0");
//        list.add("合同价格-M_Neo_0009-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0009_MSC-合同价格变更为-合同价格_普通变更_豆粕大合同-1");
//        list.add("全额货款-M_Neo_0010-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0010_MSC-全额货款变更为-全额货款_普通变更_豆粕大合同-1");
//        list.add("暂定全额货款-M_Neo_0011-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0011_MSC-暂定全额货款变更为-暂定全额货款_普通变更_豆粕大合同-1");
//        list.add("交货时间/地点-M_Neo_0012-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0012_MSC-交货时间/地点变更为-交货时间/地点_普通变更_豆粕大合同-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0013_MSC-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆粕大合同-1");
//        list.add("交货方式-M_Neo_0014-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0014_MSC-交货方式变更为-交货方式_普通变更_豆粕大合同-1");
//        list.add("交货费用-M_Neo_0015-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0015_MSC-交货费用变更为-交货费用_普通变更_豆粕大合同-0");
//        list.add("交货运输方式-M_Neo_0016-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0016_MSC-交货运输方式变更为-交货运输方式_普通变更_豆粕大合同-1");
//        list.add("重量、质量检验-M_Neo_0017-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0017_MSC-重量、质量检验变更为-重量、质量检验_普通变更_豆粕大合同-1");
//        list.add("重量检验-M_Neo_0035-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0035_MSC-重量检验变更为-重量检验_普通变更_豆粕大合同-0");
//        list.add("付款-M_Neo_0018-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0018_MSC-付款变更为-付款_普通变更_豆粕大合同-1");
//        list.add("履约保证金-M_Neo_0019-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0019_MSC-履约保证金变更为-履约保证金_普通变更_豆粕大合同-1");
//        list.add("追加履约保证金-M_Neo_0020-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0020_MSC-追加履约保证金变更为-追加履约保证金_普通变更_豆粕大合同-1");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0021_MSC-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆粕大合同-1");
//        list.add("所有权及风险转移-M_Neo_0022-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0022_MSC-所有权及风险转移变更为-所有权及风险转移_普通变更_豆粕大合同-1");
//        list.add("完整协议-M_Neo_0023-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0023_MSC-完整协议变更为-完整协议_普通变更_豆粕大合同-0");
//        list.add("不可抗力-M_Neo_0024-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0024_MSC-不可抗力变更为-不可抗力_普通变更_豆粕大合同-0");
//        list.add("终止-M_Neo_0025-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0025_MSC-终止变更为-终止_普通变更_豆粕大合同-0");
//        list.add("责任及限制-M_Neo_0026-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0026_MSC-责任及限制变更为-责任及限制_普通变更_豆粕大合同-1");
//        list.add("争议解决-M_Neo_0027-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0027_MSC-争议解决变更为-争议解决_普通变更_豆粕大合同-0");
//        list.add("通知-M_Neo_0028-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0028_MSC-通知变更为-通知_普通变更_豆粕大合同-1");
//        list.add("个人信息保护-M_Neo_0029-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0029_MSC-个人信息保护变更为-个人信息保护_普通变更_豆粕大合同-0");
//        list.add("其他-M_Neo_0030-T_12001-T_12003-豆粕-大合同-MSC-Modify_M_Neo_0030_MSC-其他变更为-其他_普通变更_豆粕大合同-0");
//        list.add("交（提）货费用-M__Neo_033-T_12001-T_12003-豆粕-大合同-MSC-Modify_M__Neo_033_MSC-交（提）货费用变更为-交（提）货费用_普通变更_豆粕大合同-0");
//        list.add("延迟交货-M__Neo_034-T_12001-T_12003-豆粕-大合同-MSC-Modify_M__Neo_034_MSC-延迟交货变更为-延迟交货_普通变更_豆粕大合同-0");
//        list.add("交货工厂-M_Neo_0036-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0036_MSORD-交货工厂变更为-交货工厂_普通变更_豆粕订单-1");
//        list.add("货物名称-M_Neo_0005-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0005_MSORD-货物名称变更为-货物名称_普通变更_豆粕订单-0");
//        list.add("货物重量-M_Neo_0006-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0006_MSORD-货物重量变更为-货物重量_普通变更_豆粕订单-1");
//        list.add("质量指标-M_Neo_0007-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0007_MSORD-质量指标变更为-质量指标_普通变更_豆粕订单-1");
//        list.add("包装及标识要求-M_Neo_0008-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0008_MSORD-包装及标识要求变更为-包装及标识要求_普通变更_豆粕订单-0");
//        list.add("包装-M_Neo_032-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_032_MSORD-包装变更为-包装_普通变更_豆粕订单-1");
//        list.add("合同价格-M_Neo_0009-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0009_MSORD-合同价格变更为-合同价格_普通变更_豆粕订单-1");
//        list.add("全额货款-M_Neo_0010-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0010_MSORD-全额货款变更为-全额货款_普通变更_豆粕订单-1");
//        list.add("暂定全额货款-M_Neo_0011-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0011_MSORD-暂定全额货款变更为-暂定全额货款_普通变更_豆粕订单-1");
//        list.add("交货时间/地点-M_Neo_0012-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0012_MSORD-交货时间/地点变更为-交货时间/地点_普通变更_豆粕订单-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0013_MSORD-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆粕订单-1");
//        list.add("交货方式-M_Neo_0014-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0014_MSORD-交货方式变更为-交货方式_普通变更_豆粕订单-0");
//        list.add("交货费用-M_Neo_0015-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0015_MSORD-交货费用变更为-交货费用_普通变更_豆粕订单-0");
//        list.add("交货运输方式-M_Neo_0016-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0016_MSORD-交货运输方式变更为-交货运输方式_普通变更_豆粕订单-1");
//        list.add("重量、质量检验-M_Neo_0017-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0017_MSORD-重量、质量检验变更为-重量、质量检验_普通变更_豆粕订单-0");
//        list.add("重量检验-M_Neo_0035-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0035_MSORD-重量检验变更为-重量检验_普通变更_豆粕订单-1");
//        list.add("付款-M_Neo_0018-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0018_MSORD-付款变更为-付款_普通变更_豆粕订单-1");
//        list.add("履约保证金-M_Neo_0019-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0019_MSORD-履约保证金变更为-履约保证金_普通变更_豆粕订单-1");
//        list.add("追加履约保证金-M_Neo_0020-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0020_MSORD-追加履约保证金变更为-追加履约保证金_普通变更_豆粕订单-1");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0021_MSORD-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆粕订单-0");
//        list.add("所有权及风险转移-M_Neo_0022-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0022_MSORD-所有权及风险转移变更为-所有权及风险转移_普通变更_豆粕订单-0");
//        list.add("完整协议-M_Neo_0023-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0023_MSORD-完整协议变更为-完整协议_普通变更_豆粕订单-0");
//        list.add("不可抗力-M_Neo_0024-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0024_MSORD-不可抗力变更为-不可抗力_普通变更_豆粕订单-0");
//        list.add("终止-M_Neo_0025-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0025_MSORD-终止变更为-终止_普通变更_豆粕订单-0");
//        list.add("责任及限制-M_Neo_0026-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0026_MSORD-责任及限制变更为-责任及限制_普通变更_豆粕订单-0");
//        list.add("争议解决-M_Neo_0027-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0027_MSORD-争议解决变更为-争议解决_普通变更_豆粕订单-0");
//        list.add("通知-M_Neo_0028-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0028_MSORD-通知变更为-通知_普通变更_豆粕订单-1");
//        list.add("个人信息保护-M_Neo_0029-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0029_MSORD-个人信息保护变更为-个人信息保护_普通变更_豆粕订单-0");
//        list.add("其他-M_Neo_0030-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M_Neo_0030_MSORD-其他变更为-其他_普通变更_豆粕订单-0");
//        list.add("交（提）货费用-M__Neo_033-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M__Neo_033_MSORD-交（提）货费用变更为-交（提）货费用_普通变更_豆粕订单-0");
//        list.add("延迟交货-M__Neo_034-T_12002-T_12003_Order-豆粕-订单-MSORD-Modify_M__Neo_034_MSORD-延迟交货变更为-延迟交货_普通变更_豆粕订单-0");
//        list.add("交货工厂-M_Neo_0036-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0036_YSC-交货工厂变更为-交货工厂_普通变更_豆油大合同-1");
//        list.add("货物名称-M_Neo_0005-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0005_YSC-货物名称变更为-货物名称_普通变更_豆油大合同-1");
//        list.add("货物重量-M_Neo_0006-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0006_YSC-货物重量变更为-货物重量_普通变更_豆油大合同-1");
//        list.add("质量指标-M_Neo_0007-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0007_YSC-质量指标变更为-质量指标_普通变更_豆油大合同-1");
//        list.add("包装及标识要求-M_Neo_0008-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0008_YSC-包装及标识要求变更为-包装及标识要求_普通变更_豆油大合同-0");
//        list.add("包装-M_Neo_032-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_032_YSC-包装变更为-包装_普通变更_豆油大合同-0");
//        list.add("合同价格-M_Neo_0009-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0009_YSC-合同价格变更为-合同价格_普通变更_豆油大合同-1");
//        list.add("全额货款-M_Neo_0010-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0010_YSC-全额货款变更为-全额货款_普通变更_豆油大合同-1");
//        list.add("暂定全额货款-M_Neo_0011-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0011_YSC-暂定全额货款变更为-暂定全额货款_普通变更_豆油大合同-1");
//        list.add("交货时间/地点-M_Neo_0012-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0012_YSC-交货时间/地点变更为-交货时间/地点_普通变更_豆油大合同-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0013_YSC-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆油大合同-1");
//        list.add("交货方式-M_Neo_0014-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0014_YSC-交货方式变更为-交货方式_普通变更_豆油大合同-1");
//        list.add("交货费用-M_Neo_0015-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0015_YSC-交货费用变更为-交货费用_普通变更_豆油大合同-0");
//        list.add("交货运输方式-M_Neo_0016-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0016_YSC-交货运输方式变更为-交货运输方式_普通变更_豆油大合同-1");
//        list.add("重量、质量检验-M_Neo_0017-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0017_YSC-重量、质量检验变更为-重量、质量检验_普通变更_豆油大合同-1");
//        list.add("重量检验-M_Neo_0035-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0035_YSC-重量检验变更为-重量检验_普通变更_豆油大合同-0");
//        list.add("付款-M_Neo_0018-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0018_YSC-付款变更为-付款_普通变更_豆油大合同-1");
//        list.add("履约保证金-M_Neo_0019-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0019_YSC-履约保证金变更为-履约保证金_普通变更_豆油大合同-1");
//        list.add("追加履约保证金-M_Neo_0020-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0020_YSC-追加履约保证金变更为-追加履约保证金_普通变更_豆油大合同-1");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0021_YSC-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆油大合同-1");
//        list.add("所有权及风险转移-M_Neo_0022-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0022_YSC-所有权及风险转移变更为-所有权及风险转移_普通变更_豆油大合同-1");
//        list.add("完整协议-M_Neo_0023-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0023_YSC-完整协议变更为-完整协议_普通变更_豆油大合同-0");
//        list.add("不可抗力-M_Neo_0024-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0024_YSC-不可抗力变更为-不可抗力_普通变更_豆油大合同-0");
//        list.add("终止-M_Neo_0025-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0025_YSC-终止变更为-终止_普通变更_豆油大合同-0");
//        list.add("责任及限制-M_Neo_0026-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0026_YSC-责任及限制变更为-责任及限制_普通变更_豆油大合同-1");
//        list.add("争议解决-M_Neo_0027-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0027_YSC-争议解决变更为-争议解决_普通变更_豆油大合同-0");
//        list.add("通知-M_Neo_0028-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0028_YSC-通知变更为-通知_普通变更_豆油大合同-1");
//        list.add("个人信息保护-M_Neo_0029-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0029_YSC-个人信息保护变更为-个人信息保护_普通变更_豆油大合同-0");
//        list.add("其他-M_Neo_0030-T_22001-T_22003-豆油-大合同-YSC-Modify_M_Neo_0030_YSC-其他变更为-其他_普通变更_豆油大合同-0");
//        list.add("交（提）货费用-M__Neo_033-T_22001-T_22003-豆油-大合同-YSC-Modify_M__Neo_033_YSC-交（提）货费用变更为-交（提）货费用_普通变更_豆油大合同-0");
//        list.add("延迟交货-M__Neo_034-T_22001-T_22003-豆油-大合同-YSC-Modify_M__Neo_034_YSC-延迟交货变更为-延迟交货_普通变更_豆油大合同-0");
//        list.add("交货工厂-M_Neo_0036-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0036_YSORD-交货工厂变更为-交货工厂_普通变更_豆油订单-1");
//        list.add("货物名称-M_Neo_0005-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0005_YSORD-货物名称变更为-货物名称_普通变更_豆油订单-1");
//        list.add("货物重量-M_Neo_0006-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0006_YSORD-货物重量变更为-货物重量_普通变更_豆油订单-1");
//        list.add("质量指标-M_Neo_0007-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0007_YSORD-质量指标变更为-质量指标_普通变更_豆油订单-1");
//        list.add("包装及标识要求-M_Neo_0008-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0008_YSORD-包装及标识要求变更为-包装及标识要求_普通变更_豆油订单-0");
//        list.add("包装-M_Neo_032-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_032_YSORD-包装变更为-包装_普通变更_豆油订单-0");
//        list.add("合同价格-M_Neo_0009-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0009_YSORD-合同价格变更为-合同价格_普通变更_豆油订单-1");
//        list.add("全额货款-M_Neo_0010-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0010_YSORD-全额货款变更为-全额货款_普通变更_豆油订单-1");
//        list.add("暂定全额货款-M_Neo_0011-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0011_YSORD-暂定全额货款变更为-暂定全额货款_普通变更_豆油订单-1");
//        list.add("交货时间/地点-M_Neo_0012-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0012_YSORD-交货时间/地点变更为-交货时间/地点_普通变更_豆油订单-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0013_YSORD-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆油订单-1");
//        list.add("交货方式-M_Neo_0014-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0014_YSORD-交货方式变更为-交货方式_普通变更_豆油订单-1");
//        list.add("交货费用-M_Neo_0015-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0015_YSORD-交货费用变更为-交货费用_普通变更_豆油订单-0");
//        list.add("交货运输方式-M_Neo_0016-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0016_YSORD-交货运输方式变更为-交货运输方式_普通变更_豆油订单-1");
//        list.add("重量、质量检验-M_Neo_0017-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0017_YSORD-重量、质量检验变更为-重量、质量检验_普通变更_豆油订单-1");
//        list.add("重量检验-M_Neo_0035-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0035_YSORD-重量检验变更为-重量检验_普通变更_豆油订单-0");
//        list.add("付款-M_Neo_0018-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0018_YSORD-付款变更为-付款_普通变更_豆油订单-1");
//        list.add("履约保证金-M_Neo_0019-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0019_YSORD-履约保证金变更为-履约保证金_普通变更_豆油订单-1");
//        list.add("追加履约保证金-M_Neo_0020-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0020_YSORD-追加履约保证金变更为-追加履约保证金_普通变更_豆油订单-1");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0021_YSORD-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆油订单-0");
//        list.add("所有权及风险转移-M_Neo_0022-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0022_YSORD-所有权及风险转移变更为-所有权及风险转移_普通变更_豆油订单-1");
//        list.add("完整协议-M_Neo_0023-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0023_YSORD-完整协议变更为-完整协议_普通变更_豆油订单-0");
//        list.add("不可抗力-M_Neo_0024-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0024_YSORD-不可抗力变更为-不可抗力_普通变更_豆油订单-0");
//        list.add("终止-M_Neo_0025-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0025_YSORD-终止变更为-终止_普通变更_豆油订单-0");
//        list.add("责任及限制-M_Neo_0026-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0026_YSORD-责任及限制变更为-责任及限制_普通变更_豆油订单-0");
//        list.add("争议解决-M_Neo_0027-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0027_YSORD-争议解决变更为-争议解决_普通变更_豆油订单-0");
//        list.add("通知-M_Neo_0028-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0028_YSORD-通知变更为-通知_普通变更_豆油订单-1");
//        list.add("个人信息保护-M_Neo_0029-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0029_YSORD-个人信息保护变更为-个人信息保护_普通变更_豆油订单-0");
//        list.add("其他-M_Neo_0030-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M_Neo_0030_YSORD-其他变更为-其他_普通变更_豆油订单-0");
//        list.add("交（提）货费用-M__Neo_033-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M__Neo_033_YSORD-交（提）货费用变更为-交（提）货费用_普通变更_豆油订单-0");
//        list.add("延迟交货-M__Neo_034-T_22002-T_22003_Order-豆油-订单-YSORD-Modify_M__Neo_034_YSORD-延迟交货变更为-延迟交货_普通变更_豆油订单-0");
//        list.add("交货工厂-M_Neo_0036-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0036_MPC-交货工厂变更为-交货工厂_普通变更_豆粕采购合同-1");
//        list.add("货物名称-M_Neo_0005-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0005_MPC-货物名称变更为-货物名称_普通变更_豆粕采购合同-0");
//        list.add("质量指标-M_Neo_0007-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0007_MPC-质量指标变更为-质量指标_普通变更_豆粕采购合同-1");
//        list.add("货物重量-M_Neo_0006-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0006_MPC-货物重量变更为-货物重量_普通变更_豆粕采购合同-1");
//        list.add("包装及标识要求-M_Neo_0008-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0008_MPC-包装及标识要求变更为-包装及标识要求_普通变更_豆粕采购合同-0");
//        list.add("包装-M_Neo_032-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_032_MPC-包装变更为-包装_普通变更_豆粕采购合同-1");
//        list.add("合同价格-M_Neo_0009-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0009_MPC-合同价格变更为-合同价格_普通变更_豆粕采购合同-1");
//        list.add("全额货款-M_Neo_0010-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0010_MPC-全额货款变更为-全额货款_普通变更_豆粕采购合同-1");
//        list.add("暂定全额货款-M_Neo_0011-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0011_MPC-暂定全额货款变更为-暂定全额货款_普通变更_豆粕采购合同-0");
//        list.add("交货时间/地点-M_Neo_0012-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0012_MPC-交货时间/地点变更为-交货时间/地点_普通变更_豆粕采购合同-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0013_MPC-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆粕采购合同-0");
//        list.add("交货方式-M_Neo_0014-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0014_MPC-交货方式变更为-交货方式_普通变更_豆粕采购合同-1");
//        list.add("交货费用-M_Neo_0015-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0015_MPC-交货费用变更为-交货费用_普通变更_豆粕采购合同-0");
//        list.add("交货运输方式-M_Neo_0016-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0016_MPC-交货运输方式变更为-交货运输方式_普通变更_豆粕采购合同-0");
//        list.add("重量、质量检验-M_Neo_0017-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0017_MPC-重量、质量检验变更为-重量、质量检验_普通变更_豆粕采购合同-0");
//        list.add("重量检验-M_Neo_0035-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0035_MPC-重量检验变更为-重量检验_普通变更_豆粕采购合同-1");
//        list.add("付款-M_Neo_0018-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0018_MPC-付款变更为-付款_普通变更_豆粕采购合同-1");
//        list.add("履约保证金-M_Neo_0019-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0019_MPC-履约保证金变更为-履约保证金_普通变更_豆粕采购合同-1");
//        list.add("追加履约保证金-M_Neo_0020-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0020_MPC-追加履约保证金变更为-追加履约保证金_普通变更_豆粕采购合同-0");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0021_MPC-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆粕采购合同-0");
//        list.add("所有权及风险转移-M_Neo_0022-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0022_MPC-所有权及风险转移变更为-所有权及风险转移_普通变更_豆粕采购合同-1");
//        list.add("完整协议-M_Neo_0023-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0023_MPC-完整协议变更为-完整协议_普通变更_豆粕采购合同-0");
//        list.add("不可抗力-M_Neo_0024-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0024_MPC-不可抗力变更为-不可抗力_普通变更_豆粕采购合同-0");
//        list.add("终止-M_Neo_0025-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0025_MPC-终止变更为-终止_普通变更_豆粕采购合同-0");
//        list.add("责任及限制-M_Neo_0026-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0026_MPC-责任及限制变更为-责任及限制_普通变更_豆粕采购合同-0");
//        list.add("争议解决-M_Neo_0027-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0027_MPC-争议解决变更为-争议解决_普通变更_豆粕采购合同-0");
//        list.add("通知-M_Neo_0028-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0028_MPC-通知变更为-通知_普通变更_豆粕采购合同-1");
//        list.add("个人信息保护-M_Neo_0029-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0029_MPC-个人信息保护变更为-个人信息保护_普通变更_豆粕采购合同-0");
//        list.add("其他-M_Neo_0030-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M_Neo_0030_MPC-其他变更为-其他_普通变更_豆粕采购合同-0");
//        list.add("交（提）货费用-M__Neo_033-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M__Neo_033_MPC-交（提）货费用变更为-交（提）货费用_普通变更_豆粕采购合同-0");
//        list.add("延迟交货-M__Neo_034-T_11001-T_11003-豆粕-采购合同-MPC-Modify_M__Neo_034_MPC-延迟交货变更为-延迟交货_普通变更_豆粕采购合同-0");
//        list.add("交货工厂-M_Neo_0036-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0036_YPC-交货工厂变更为-交货工厂_普通变更_豆油采购合同-1");
//        list.add("货物名称-M_Neo_0005-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0005_YPC-货物名称变更为-货物名称_普通变更_豆油采购合同-1");
//        list.add("质量指标-M_Neo_0007-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0007_YPC-质量指标变更为-质量指标_普通变更_豆油采购合同-1");
//        list.add("货物重量-M_Neo_0006-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0006_YPC-货物重量变更为-货物重量_普通变更_豆油采购合同-1");
//        list.add("包装及标识要求-M_Neo_0008-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0008_YPC-包装及标识要求变更为-包装及标识要求_普通变更_豆油采购合同-0");
//        list.add("包装-M_Neo_032-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_032_YPC-包装变更为-包装_普通变更_豆油采购合同-1");
//        list.add("合同价格-M_Neo_0009-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0009_YPC-合同价格变更为-合同价格_普通变更_豆油采购合同-1");
//        list.add("全额货款-M_Neo_0010-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0010_YPC-全额货款变更为-全额货款_普通变更_豆油采购合同-1");
//        list.add("暂定全额货款-M_Neo_0011-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0011_YPC-暂定全额货款变更为-暂定全额货款_普通变更_豆油采购合同-0");
//        list.add("交货时间/地点-M_Neo_0012-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0012_YPC-交货时间/地点变更为-交货时间/地点_普通变更_豆油采购合同-1");
//        list.add("发货时间/交货地点-M_Neo_0013-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0013_YPC-发货时间/交货地点变更为-发货时间/交货地点_普通变更_豆油采购合同-0");
//        list.add("交货方式-M_Neo_0014-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0014_YPC-交货方式变更为-交货方式_普通变更_豆油采购合同-1");
//        list.add("交货费用-M_Neo_0015-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0015_YPC-交货费用变更为-交货费用_普通变更_豆油采购合同-0");
//        list.add("交货运输方式-M_Neo_0016-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0016_YPC-交货运输方式变更为-交货运输方式_普通变更_豆油采购合同-0");
//        list.add("重量、质量检验-M_Neo_0017-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0017_YPC-重量、质量检验变更为-重量、质量检验_普通变更_豆油采购合同-0");
//        list.add("重量检验-M_Neo_0035-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0035_YPC-重量检验变更为-重量检验_普通变更_豆油采购合同-1");
//        list.add("付款-M_Neo_0018-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0018_YPC-付款变更为-付款_普通变更_豆油采购合同-1");
//        list.add("履约保证金-M_Neo_0019-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0019_YPC-履约保证金变更为-履约保证金_普通变更_豆油采购合同-1");
//        list.add("追加履约保证金-M_Neo_0020-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0020_YPC-追加履约保证金变更为-追加履约保证金_普通变更_豆油采购合同-0");
//        list.add("延迟付款/提货责任-M_Neo_0021-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0021_YPC-延迟付款/提货责任变更为-延迟付款/提货责任_普通变更_豆油采购合同-0");
//        list.add("所有权及风险转移-M_Neo_0022-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0022_YPC-所有权及风险转移变更为-所有权及风险转移_普通变更_豆油采购合同-1");
//        list.add("完整协议-M_Neo_0023-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0023_YPC-完整协议变更为-完整协议_普通变更_豆油采购合同-0");
//        list.add("不可抗力-M_Neo_0024-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0024_YPC-不可抗力变更为-不可抗力_普通变更_豆油采购合同-0");
//        list.add("终止-M_Neo_0025-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0025_YPC-终止变更为-终止_普通变更_豆油采购合同-0");
//        list.add("责任及限制-M_Neo_0026-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0026_YPC-责任及限制变更为-责任及限制_普通变更_豆油采购合同-0");
//        list.add("争议解决-M_Neo_0027-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0027_YPC-争议解决变更为-争议解决_普通变更_豆油采购合同-0");
//        list.add("通知-M_Neo_0028-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0028_YPC-通知变更为-通知_普通变更_豆油采购合同-1");
//        list.add("个人信息保护-M_Neo_0029-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0029_YPC-个人信息保护变更为-个人信息保护_普通变更_豆油采购合同-0");
//        list.add("其他-M_Neo_0030-T_21001-T_21003-豆油-采购合同-YPC-Modify_M_Neo_0030_YPC-其他变更为-其他_普通变更_豆油采购合同-0");
//        list.add("交（提）货费用-M__Neo_033-T_21001-T_21003-豆油-采购合同-YPC-Modify_M__Neo_033_YPC-交（提）货费用变更为-交（提）货费用_普通变更_豆油采购合同-0");
//        list.add("延迟交货-M__Neo_034-T_21001-T_21003-豆油-采购合同-YPC-Modify_M__Neo_034_YPC-延迟交货变更为-延迟交货_普通变更_豆油采购合同-0");
//
//        return list;
//    }
//
//
//    private String getRuleInfo(String ruleText) {
//        String[] ss = ruleText.split(",");
//        StringBuilder sb = new StringBuilder();
//        int i = 0;
//        for (String s : ss) {
//            if (i == 0) {
//                sb.append("\"").append(s).append("\"");
//            } else {
//                sb.append(",\"").append(s).append("\"");
//            }
//            i = i + 1;
//        }
//
//        String ruleInfo = sb.toString();
//        return ruleInfo;
//    }
//
//    private String getRuleInfo2(String ruleText) {
//
//        ruleText=ruleText +",ldc";
//
//        String[] ss = ruleText.split(",");
//        StringBuilder sb = new StringBuilder();
//
//
//
//
//
//        int i = 0;
//        for (String s : ss) {
//            if (i == 0) {
//                sb.append("(mapBizData.get(\"modifyList\") contains \"").append(s).append("\"");
//            } else {
//                sb.append(" || mapBizData.get(\"modifyList\") contains \"").append(s).append("\"");
//            }
//            i = i + 1;
//        }
//
//
//        sb.append(")");
//
//        String ruleInfo = sb.toString();
//        return ruleInfo;
//    }
//
//
//}