package rules

import java.util.HashMap;
import java.util.List;
import com.navigator.common.dto.DroolsRuleBizInfoDTO

lock-on-active true

rule starting
    when
        $r:DroolsRuleBizInfoDTO(flag==1)
    then
        System.out.println("matched:"+drools.getRule().getName());
end

rule TEST_AGE
    when
        $r:DroolsRuleBizInfoDTO(Integer.valueOf(mapBizData.get("age")) ==18)
    then
        //matchRules.add(drools.getRule().getName());
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
    end

rule SBM_A_ADD_ABC1
    when
        $r:DroolsRuleBizInfoDTO( mapBizData.get("Name") =="NZJ" && Integer.valueOf(mapBizData.get("Rate")) ==5)
    then
        //matchRules.add(drools.getRule().getName());
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
    end

rule SBM_A_ADD_ABC2
    when
        $r:DroolsRuleBizInfoDTO(mapBizData.get("Name") =="NZJ")
    then
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
end

rule SBM_A_ADD_AB
    when
        $r:DroolsRuleBizInfoDTO(mapBizData.get("Name") =="NZJ")
    then
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
end

rule SBM_A_ADD_A1
    when
        $r:DroolsRuleBizInfoDTO(mapBizData.get("Name") =="NZJ")
    then
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
end

rule SBM_A_ADD_A2
    when
        $r:DroolsRuleBizInfoDTO(Integer.valueOf(mapBizData.get("Rate")) ==5)
    then
        $r.getMatchRules().add(drools.getRule().getName());
        System.out.println("matched:"+drools.getRule().getName());
end

rule endding
    when
        $r:DroolsRuleBizInfoDTO(flag==1)
    then
        update($r);
        System.out.println("matched:"+drools.getRule().getName());
end





