package com.navigator.husky.facade.impl;

import com.navigator.husky.facade.TemplateLoadFacade;
import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import com.navigator.husky.service.TemplateLoadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-02 18:07
 **/
@RestController
public class TemplateLoadFacadeImpl implements TemplateLoadFacade {
    @Autowired
    private TemplateLoadService templateLoadService;

    @Override
    public TemplateLoadEntity saveTemplateLoadLog(TemplateLoadEntity templateLoadEntity) {
        return templateLoadService.saveTemplateLoad(templateLoadEntity);
    }

    @Override
    public TemplateLoadEntity updateTemplateLoadLog(TemplateLoadEntity templateLoadEntity) {
        return templateLoadService.updateTemplateLoad(templateLoadEntity);
    }
}
