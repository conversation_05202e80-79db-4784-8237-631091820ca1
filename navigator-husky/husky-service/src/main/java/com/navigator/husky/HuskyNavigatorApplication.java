package com.navigator.husky;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.husky.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class HuskyNavigatorApplication {
    public static void main(String[] args) {
        SpringApplication.run(HuskyNavigatorApplication.class, args);
    }

}
