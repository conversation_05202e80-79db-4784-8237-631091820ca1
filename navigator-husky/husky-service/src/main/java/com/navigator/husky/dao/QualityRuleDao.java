package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.QualityRuleMapper;
import com.navigator.husky.pojo.entity.QualityRuleEntity;
import com.navigator.husky.pojo.enums.VariableBizCodeEnum;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 15:59
 **/
@Dao
public class QualityRuleDao extends BaseDaoImpl<QualityRuleMapper, QualityRuleEntity> {

    /**
     * 记录发货库点/集团客户/专属客户
     *
     * @param qualityId
     * @param qualityRuleCode
     * @param qualityRuleEntityList
     */
    public void recordQualityRuleList(Integer qualityId, String qualityRuleCode, List<QualityRuleEntity> qualityRuleEntityList) {
        if (CollectionUtils.isEmpty(qualityRuleEntityList)) {
            return;
        }
        for (QualityRuleEntity qualityRuleEntity : qualityRuleEntityList) {
            qualityRuleEntity.setQualityId(qualityId)
                    .setQualityRuleCode(qualityRuleCode)
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now());
            this.save(qualityRuleEntity);
        }
    }

    public List<QualityRuleEntity> getQualityRuleListByCode(Integer qualityId, String qualityRuleCode) {
        LambdaQueryWrapper<QualityRuleEntity> queryWrapper = new LambdaQueryWrapper<QualityRuleEntity>()
                .eq(QualityRuleEntity::getQualityId, qualityId)
                .eq(QualityRuleEntity::getQualityRuleCode, qualityRuleCode)
                .eq(QualityRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(queryWrapper);
    }

    /**
     * {@link VariableBizCodeEnum}
     * @param qualityRuleCode
     * @param referId
     * @return
     */
    public List<QualityRuleEntity> getQualityRuleListByRuleCode(String qualityRuleCode, Integer referId) {
        LambdaQueryWrapper<QualityRuleEntity> queryWrapper = new LambdaQueryWrapper<QualityRuleEntity>()
                .eq(QualityRuleEntity::getQualityRuleCode, qualityRuleCode)
                .eq(QualityRuleEntity::getReferId, referId)
                .eq(QualityRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(queryWrapper);
    }

    public List<QualityRuleEntity> getQualityRuleList(List<Integer> qualityIdList, String qualityRuleCode, List<Integer> referIdList) {
        LambdaQueryWrapper<QualityRuleEntity> queryWrapper = new LambdaQueryWrapper<QualityRuleEntity>()
                .in(QualityRuleEntity::getQualityId, qualityIdList)
                .eq(QualityRuleEntity::getQualityRuleCode, qualityRuleCode)
                .in(QualityRuleEntity::getReferId, referIdList)
                .eq(QualityRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        return this.list(queryWrapper);
    }

    public void dropQualityRule(Integer qualityId, String qualityRuleCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(QualityRuleEntity::getQualityId, qualityId)
                .eq(StringUtil.isNotEmpty(qualityRuleCode), QualityRuleEntity::getQualityRuleCode, qualityRuleCode)
                .eq(QualityRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(QualityRuleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(QualityRuleEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
