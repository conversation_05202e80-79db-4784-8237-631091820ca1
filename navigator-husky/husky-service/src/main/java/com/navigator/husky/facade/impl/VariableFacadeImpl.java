package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.VariableFacade;
import com.navigator.husky.pojo.entity.VariableEntity;
import com.navigator.husky.service.VariableService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 13:47
 **/
@RestController
public class VariableFacadeImpl implements VariableFacade {
    @Resource
    private VariableService variableService;

    @Override
    public Boolean updateVariable(VariableEntity variableEntity) {
        return variableService.updateVariable(variableEntity);
    }

    @Override
    public Result queryVariableByCondition(QueryDTO<VariableEntity> queryDTO) {
        return variableService.queryVariableByCondition(queryDTO);
    }

    @Override
    public Result queryExportVariableList(VariableEntity queryDTO) {
        return Result.success(variableService.queryExportVariableList(queryDTO));
    }

    @Override
    public List<VariableEntity> getAllVariableList(Integer isCondition, Integer isKey) {
        return variableService.getAllVariableList(isCondition, isKey);
    }

    @Override
    public List<VariableEntity> getAllVariableBasicList() {
        return variableService.getAllVariableBasicList();
    }

    @Override
    public Result getNotUsedVariableList() {
        return Result.success(variableService.getNotUsedVariableList());
    }

    @Override
    public Result importVariableInfo(MultipartFile file) {
        return variableService.importVariableInfo(file);
    }
}
