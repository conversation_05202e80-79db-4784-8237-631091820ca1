package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.husky.mapper.TemplateCheckMapper;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 14:09
 **/
@Dao
@Slf4j
public class TemplateCheckDao extends BaseDaoImpl<TemplateCheckMapper, TemplateCheckEntity> {
    /**
     * 分页条件查询
     *
     * @param queryDTO 查询条件
     * @return
     */
    public IPage<TemplateCheckEntity> queryCheckByCondition(QueryDTO<TemplateCheckEntity> queryDTO) {
        TemplateCheckEntity templateCheckQO = queryDTO.getCondition();
        LambdaQueryWrapper<TemplateCheckEntity> queryWrapper = getTemplateCheckQueryWrapper(templateCheckQO);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<TemplateCheckEntity> queryCheckList(TemplateCheckEntity templateCheckQO) {
        LambdaQueryWrapper<TemplateCheckEntity> queryWrapper = getTemplateCheckQueryWrapper(templateCheckQO);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<TemplateCheckEntity> getTemplateCheckQueryWrapper(TemplateCheckEntity templateCheckQO) {
        LambdaQueryWrapper<TemplateCheckEntity> queryWrapper = new LambdaQueryWrapper<TemplateCheckEntity>()
                .eq(null != templateCheckQO.getStatus(), TemplateCheckEntity::getStatus, templateCheckQO.getStatus())
//                .eq(null != templateCheckQO.getCategoryId(), TemplateCheckEntity::getCategoryId, templateCheckQO.getCategoryId())
                .eq(null != templateCheckQO.getCategory1(), TemplateCheckEntity::getCategory1, templateCheckQO.getCategory1())
                .eq(null != templateCheckQO.getCategory2(), TemplateCheckEntity::getCategory2, templateCheckQO.getCategory2())
                .eq(null != templateCheckQO.getCategory3(), TemplateCheckEntity::getCategory3, templateCheckQO.getCategory3())
                .eq(null != templateCheckQO.getSalesType(), TemplateCheckEntity::getSalesType, templateCheckQO.getSalesType())
                .eq(null != templateCheckQO.getContractActionType(), TemplateCheckEntity::getContractActionType, templateCheckQO.getContractActionType())
                .eq(StringUtils.isNotBlank(templateCheckQO.getTemplateCode()), TemplateCheckEntity::getTemplateCode, templateCheckQO.getTemplateCode())
                .eq(TemplateCheckEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
//                .like(StringUtils.isNotBlank(templateCheckQO.getUpdatedBy()), TemplateCheckEntity::getUpdatedBy, templateCheckQO.getUpdatedBy())
//                .between(StringUtils.isNotBlank(templateCheckQO.getStartDay()) && StringUtils.isNotBlank(templateCheckQO.getEndDay()), TemplateCheckEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(templateCheckQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(templateCheckQO.getEndDay()));
        queryWrapper.orderByDesc(TemplateCheckEntity::getId);
        return queryWrapper;
    }


    public List<TemplateCheckEntity> getCheckListByTemplateCode(String templateCode, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateCheckEntity>()
                .eq(StringUtils.isNotBlank(templateCode), TemplateCheckEntity::getTemplateCode, templateCode)
                .eq(null != status, TemplateCheckEntity::getStatus, status)
                .eq(TemplateCheckEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
