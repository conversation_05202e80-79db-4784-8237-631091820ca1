package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.husky.mapper.TemplateGroupMapper;
import com.navigator.husky.mapper.TemplateModifyMapper;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.TemplateModifyEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateModifyDao extends BaseDaoImpl<TemplateModifyMapper, TemplateModifyEntity> {
    public List<TemplateModifyEntity> getAll() {

        return this.list(new LambdaQueryWrapper<TemplateModifyEntity>()
                .ge(TemplateModifyEntity::getId, 0))
                ;
    }
}
