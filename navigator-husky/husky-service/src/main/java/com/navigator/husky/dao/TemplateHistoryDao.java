package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateHistoryMapper;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-28 15:27
 **/
@Dao
@Slf4j
public class TemplateHistoryDao extends BaseDaoImpl<TemplateHistoryMapper, TemplateHistoryEntity> {

    public IPage<TemplateHistoryEntity> queryHistoryByCondition(QueryDTO<TemplateHistoryEntity> queryDTO) {
        TemplateHistoryEntity templateQO = queryDTO.getCondition();
        if (StringUtils.isNotBlank(templateQO.getUpdatedBy())) {
            templateQO.setUpdatedBy(templateQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<TemplateHistoryEntity> queryWrapper = new LambdaQueryWrapper<TemplateHistoryEntity>()
                .eq(null != templateQO.getReferType(), TemplateHistoryEntity::getReferType, templateQO.getReferType())
                .eq(null != templateQO.getVersionType(), TemplateHistoryEntity::getVersionType, templateQO.getVersionType())
                .eq(null != templateQO.getOperationType(), TemplateHistoryEntity::getOperationType, templateQO.getOperationType())
                .like(StringUtils.isNotBlank(templateQO.getVersion()), TemplateHistoryEntity::getVersion, templateQO.getVersion())
                .like(StringUtils.isNotBlank(templateQO.getMainVersionDesc()), TemplateHistoryEntity::getMainVersionDesc, templateQO.getMainVersionDesc())
                .like(StringUtils.isNotBlank(templateQO.getUpdatedBy()), TemplateHistoryEntity::getUpdatedBy, templateQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(templateQO.getStartDay()) && StringUtils.isNotBlank(templateQO.getEndDay()),
                        TemplateHistoryEntity::getUpdatedAt, DateTimeUtil.parseDateTimeString(templateQO.getStartDay()), DateTimeUtil.parseDateTimeString(templateQO.getEndDay()));
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(templateQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateHistoryEntity::getReferCode, templateQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(templateQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateHistoryEntity::getReferName, templateQO.getSearchKey().trim()));
        }
        queryWrapper.orderByDesc(TemplateHistoryEntity::getId);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize(), 0), queryWrapper);
    }
}
