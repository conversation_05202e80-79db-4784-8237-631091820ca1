package com.navigator.husky.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.admin.pojo.enums.WarehouseConfigTypeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.facade.SpuFacade;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.qo.SpuQO;
import com.navigator.husky.dao.QualityDao;
import com.navigator.husky.dao.QualityRuleDao;
import com.navigator.husky.pojo.dto.QualityImportDTO;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.entity.QualityRuleEntity;
import com.navigator.husky.pojo.enums.VariableBizCodeEnum;
import com.navigator.husky.pojo.vo.QualityExportVO;
import com.navigator.husky.service.QualityService;
import com.navigator.trade.pojo.enums.UsageEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 14:13
 **/
@Service
@Slf4j
public class QualityServiceImpl implements QualityService {

    @Resource
    private QualityDao qualityDao;
    @Resource
    private QualityRuleDao qualityRuleDao;
    @Resource
    private CustomerFacade customerFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private WarehouseFacade warehouseFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private AttributeFacade attributeFacade;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private SpuFacade spuFacade;
    @Resource
    private CategoryFacade categoryFacade;

    @Override
    public QualityEntity judgeExistQuality(QualityInfoDTO qualityInfoDTO) {
        CustomerEntity customerEntity = customerFacade.queryCustomerById(qualityInfoDTO.getCustomerId());
        qualityInfoDTO.setEnterpriseId(null != customerEntity ? customerEntity.getParentId() : 0);
        qualityInfoDTO.setStatus(DisableStatusEnum.ENABLE.getValue());
        List<Integer> filterCustomerQualityIdList = new ArrayList<>();
        List<Integer> filterEnterpriseQualityIdList = new ArrayList<>();
        List<Integer> filterSpuQualityIdList = new ArrayList<>();
        if (null != qualityInfoDTO.getCustomerId() && qualityInfoDTO.getCustomerId() > 0) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.CUSTOMER.getValue(), qualityInfoDTO.getCustomerId());
            filterCustomerQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getEnterpriseId() && qualityInfoDTO.getEnterpriseId() > 0) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.ENTERPRISE.getValue(), qualityInfoDTO.getEnterpriseId());
            filterEnterpriseQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getGoodsId()) {
            SkuEntity skuEntity = skuFacade.getSkuById(qualityInfoDTO.getGoodsId());
            if (null != skuEntity) {
                List<QualityRuleEntity> spuRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.SPU.getValue(), skuEntity.getSpuId());
                filterSpuQualityIdList = spuRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
            }
        }
        return qualityDao.matchQuality(qualityInfoDTO, filterCustomerQualityIdList, filterEnterpriseQualityIdList, filterSpuQualityIdList);
    }

    @Override
    public Result queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO) {
        QualityInfoDTO qualityInfoDTO = queryDTO.getCondition();
        List<Integer> filterCustomerQualityIdList = new ArrayList<>();
        List<Integer> filterEnterpriseQualityIdList = new ArrayList<>();
        List<Integer> filterSpuQualityIdList = new ArrayList<>();
        if (null != qualityInfoDTO.getCustomerId()) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.CUSTOMER.getValue(), qualityInfoDTO.getCustomerId());
            filterCustomerQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getEnterpriseId()) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.ENTERPRISE.getValue(), qualityInfoDTO.getEnterpriseId());
            filterEnterpriseQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getSpuId()) {
            List<QualityRuleEntity> skuRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.SPU.getValue(), qualityInfoDTO.getSpuId());
            filterSpuQualityIdList = skuRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        IPage<QualityEntity> qualityEntityIPage = qualityDao.queryQualityByCondition(queryDTO, filterCustomerQualityIdList, filterEnterpriseQualityIdList, filterSpuQualityIdList);
        if (!CollectionUtils.isEmpty(qualityEntityIPage.getRecords())) {
            qualityEntityIPage.getRecords().forEach(this::assemblyQualityDetail);
        }
        return Result.page(qualityEntityIPage);
    }

//    @Override
//    public Result queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO) {
//        QualityInfoDTO qualityInfoDTO = queryDTO.getCondition();
//        queryDTO.getCondition().setEnterpriseId(0);
//        if (null != qualityInfoDTO.getCustomerId()) {
//            CustomerEntity customerEntity = customerFacade.queryCustomerById(qualityInfoDTO.getCustomerId());
//            queryDTO.getCondition().setEnterpriseId(null != customerEntity ? customerEntity.getParentId() : 0);
//        }
//        IPage<QualityEntity> qualityEntityIPage = qualityDao.queryQualityByCondition(queryDTO);
//        if (!CollectionUtils.isEmpty(qualityEntityIPage.getRecords())) {
//            qualityEntityIPage.getRecords().forEach(this::assemblyQualityDetail);
//        }
//        //todo:数据填充
//        return Result.page(qualityEntityIPage);
//    }

    @Override
    public QualityEntity getQualityById(Integer qualityId) {
        QualityEntity qualityEntity = qualityDao.getById(qualityId);
        if (null == qualityEntity) {
            throw new BusinessException(ResultCodeEnum.QUALITY_NOT_EXIST);
        }
        //发货库点数据
        this.assemblyQualityDetail(qualityEntity);
        return qualityEntity;
    }

    /**
     * 组装质量指标详情信息
     *
     * @param qualityEntity
     */
    private void assemblyQualityDetail(QualityEntity qualityEntity) {
        List<QualityRuleEntity> warehouseList = qualityRuleDao.getQualityRuleListByCode(qualityEntity.getId(), VariableBizCodeEnum.WAREHOUSE.getValue());
        List<Integer> warehouseIdList = warehouseList.stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
        log.info("查询实时库点信息");
        List<WarehouseEntity> warehouseEntityList = warehouseFacade.queryWarehouseList(new WarehouseBO().setIdList(warehouseIdList).setType(WarehouseConfigTypeEnum.FACTORY.getValue()));
        log.info("库点记录数：{}", warehouseEntityList.size());
        String warehouseName = warehouseEntityList.stream().map(WarehouseEntity::getName).collect(Collectors.joining(","));
        qualityEntity.setGoodsCategoryCode(GoodsCategoryEnum.getDescByValue(qualityEntity.getGoodsCategoryId()))
                .setUsageInfo(UsageEnum.getDescByValue(qualityEntity.getUsage()))
                .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(qualityEntity.getSalesType()))
                .setWarehouseName(warehouseName)
                .setWarehouseList(warehouseList)
                .setStatusInfo(DisableStatusEnum.getDescByValue(qualityEntity.getStatus()));
        if (DisableStatusEnum.ENABLE.getValue().equals(qualityEntity.getHasCustomer())) {
            if (DisableStatusEnum.ENABLE.getValue().equals(qualityEntity.getEnterprise())) {
                qualityEntity.setEnterpriseName(qualityEntity.getCustomerNames())
                        .setCustomerNames("");
            }
            String qualityRuleCode = DisableStatusEnum.ENABLE.getValue().equals(qualityEntity.getEnterprise()) ?
                    VariableBizCodeEnum.ENTERPRISE.getValue() : VariableBizCodeEnum.CUSTOMER.getValue();
            List<QualityRuleEntity> customerList = qualityRuleDao.getQualityRuleListByCode(qualityEntity.getId(), qualityRuleCode);
            qualityEntity.setCustomerList(customerList);
        }
        // 货品
        List<QualityRuleEntity> spuList = qualityRuleDao.getQualityRuleListByCode(qualityEntity.getId(), VariableBizCodeEnum.SPU.getValue());
        qualityEntity.setSpuList(spuList);
    }

    @Override
    public Boolean saveQuality(QualityEntity qualityEntity) {
        if (qualityDao.validateQualityDuplicate(qualityEntity)) {
            throw new BusinessException(ResultCodeEnum.QUALITY_DUPLICATE);
        }
        //填充质量指标基础信息
        this.fillQualityInfo(qualityEntity);
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        qualityEntity.setEnterprise(null == qualityEntity.getEnterprise() ? 0 : qualityEntity.getEnterprise())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreatedBy(name)
                .setUpdatedBy(name);

        qualityDao.save(qualityEntity);
        //同步记录发货库点/客户/集团客户-质量指标关联关系
        this.syncQualityRuleList(qualityEntity);

        //记录系统操作日志
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(FastJsonUtils.getBeanToJson(qualityEntity))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.HUSKY_TEMPLATE_QUALITY_SAVE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override

    public Boolean updateQuality(QualityEntity updateQualityEntity) {
        if (qualityDao.validateQualityDuplicate(updateQualityEntity)) {
            throw new BusinessException(ResultCodeEnum.QUALITY_DUPLICATE);
        }
        //填充质量指标基础信息
        this.fillQualityInfo(updateQualityEntity);
        QualityEntity qualityEntity = qualityDao.getById(updateQualityEntity.getId());

        String beforeData = FastJsonUtils.getBeanToJson(qualityEntity);
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        qualityEntity.setWarehouseIds(updateQualityEntity.getWarehouseIds())
                .setHasCustomer(updateQualityEntity.getHasCustomer())
                .setCustomerIds(updateQualityEntity.getCustomerIds())
                .setCustomerNames(updateQualityEntity.getCustomerNames())
                .setSpuIds(updateQualityEntity.getSpuIds())
                .setSpuNames(updateQualityEntity.getSpuNames())
                .setFactoryCode(updateQualityEntity.getFactoryCode())
                .setUsage(updateQualityEntity.getUsage())
                .setSpecId(updateQualityEntity.getSpecId())
                .setSpecName(updateQualityEntity.getSpecName())
                .setSalesType(updateQualityEntity.getSalesType())
                .setEnterprise(null == updateQualityEntity.getEnterprise() ? 0 : updateQualityEntity.getEnterprise())
                .setContent(updateQualityEntity.getContent())
                .setStatus(updateQualityEntity.getStatus())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(name)
                .setStandardType(updateQualityEntity.getStandardType());
        qualityDao.updateById(qualityEntity);

        //同步记录发货库点/客户/集团客户-质量指标关联关系
        this.syncQualityRuleList(updateQualityEntity);
        //记录系统操作日志
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(FastJsonUtils.getBeanToJson(updateQualityEntity))
                    .setBeforeData(beforeData)
                    .setAfterData(FastJsonUtils.getBeanToJson(qualityEntity))
                    .setOperationActionEnum(OperationActionEnum.HUSKY_TEMPLATE_QUALITY_UPDATE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public Boolean updateQualityStatus(Integer qualityId, Integer status) {
        QualityEntity qualityEntity = qualityDao.getById(qualityId);
        String customerNames = qualityEntity.getCustomerNames();
        this.assemblyQualityDetail(qualityEntity);
        qualityEntity.setCustomerNames(customerNames);
        String beforeData = FastJsonUtils.getBeanToJson(qualityEntity);
        if (DisableStatusEnum.ENABLE.getValue().equals(status) && qualityDao.validateQualityDuplicate(qualityEntity)) {
            throw new BusinessException(ResultCodeEnum.QUALITY_DUPLICATE);
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        qualityEntity.setStatus(status)
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        qualityDao.updateById(qualityEntity);
        //记录系统操作日志
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData("质量指标ID：" + qualityId + "启用/禁用状态：" + status)
                    .setBeforeData(beforeData)
                    .setAfterData(FastJsonUtils.getBeanToJson(qualityEntity))
                    .setOperationActionEnum(OperationActionEnum.HUSKY_TEMPLATE_QUALITY_VALID)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public List<QualityExportVO> exportQualityList(QualityInfoDTO qualityInfoDTO) {
        List<Integer> filterCustomerQualityIdList = new ArrayList<>();
        List<Integer> filterEnterpriseQualityIdList = new ArrayList<>();
        List<Integer> filterSpuQualityIdList = new ArrayList<>();
        if (null != qualityInfoDTO.getCustomerId()) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.CUSTOMER.getValue(), qualityInfoDTO.getCustomerId());
            filterCustomerQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getEnterpriseId()) {
            List<QualityRuleEntity> customerRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.ENTERPRISE.getValue(), qualityInfoDTO.getEnterpriseId());
            filterEnterpriseQualityIdList = customerRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        if (null != qualityInfoDTO.getSpuId()) {
            List<QualityRuleEntity> skuRuleEntityList = qualityRuleDao.getQualityRuleListByRuleCode(VariableBizCodeEnum.SPU.getValue(), qualityInfoDTO.getSpuId());
            filterSpuQualityIdList = skuRuleEntityList.stream().map(QualityRuleEntity::getQualityId).collect(Collectors.toList());
        }
        List<QualityEntity> qualityEntityList = qualityDao.queryQualityList(qualityInfoDTO, filterCustomerQualityIdList, filterEnterpriseQualityIdList, filterSpuQualityIdList);
        if (CollectionUtils.isEmpty(qualityEntityList)) {
            return new ArrayList<>();
        }
        qualityEntityList.forEach(this::assemblyQualityDetail);
        List<QualityExportVO> qualityExportVOList = BeanConvertUtils.convert2List(QualityExportVO.class, qualityEntityList);
        qualityExportVOList.forEach(qualityExportVO -> {
            qualityExportVO.setContent(TemplateUtil.removeParentheses(qualityExportVO.getContent()));
        });
        return qualityExportVOList;
    }

    @Override
    public Result importQuality(MultipartFile uploadFile) {
        List<QualityImportDTO> qualityImportDTOList;
        try {
            qualityImportDTOList = EasyPoiUtils.importExcel(uploadFile, 0, 1, QualityImportDTO.class);
            log.info("1、质量指标导入===================解析文件信息：条数：{},内容：{}", qualityImportDTOList.size(), FastJsonUtils.getBeanToJson(qualityImportDTOList));
            qualityImportDTOList = qualityImportDTOList.stream()
                    .filter(it -> {
                        return StringUtils.isNotBlank(it.getContent()) && StringUtils.isNotBlank(it.getOperationName())
                                && StringUtils.isNotBlank(it.getSpuNames());
                    })
                    .collect(Collectors.toList());
            log.info("2、质量指标导入===================过滤程序要导入的数据：条数：{},内容：{}", qualityImportDTOList.size(), FastJsonUtils.getBeanToJson(qualityImportDTOList));
            List<String> errorWarehouseCodeList = new ArrayList<>();
            List<String> errorCustomerCodeList = new ArrayList<>();
            List<String> errorSpuName = new ArrayList<>();
            if (!CollectionUtils.isEmpty(qualityImportDTOList)) {
                for (QualityImportDTO qualityImportDTO : qualityImportDTOList) {
                    log.info(FastJsonUtils.getBeanToJson("质量指标导入当前信息：" + qualityImportDTO));
                    CategoryEntity category2Entity = categoryFacade.getCategoryByName(qualityImportDTO.getCategory2Name(), 2);
                    Integer category2 = category2Entity.getSerialNo();
                    List<String> warehouseCodeList = Arrays.stream(qualityImportDTO.getWarehouseCodes().split(",")).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(warehouseCodeList)) {
                        continue;
                    }
                    List<WarehouseEntity> warehouseEntityList = new ArrayList<>();
                    for (String warehouseCode : warehouseCodeList) {
                        List<WarehouseEntity> tempList = warehouseFacade.queryWarehouseList(new WarehouseBO().setCode(warehouseCode)
                                .setType(WarehouseConfigTypeEnum.FACTORY.getValue()));
                        if (CollUtil.isNotEmpty(tempList)) {
                            warehouseEntityList.add(tempList.get(0));
                        } else {
                            errorWarehouseCodeList.add(warehouseCode);
                        }
                    }
//                    GoodsAttributeVO goodsAttributeVO = attributeFacade.getSpecListByCategoryId(category2);
//                    List<AttributeValueVO> specList = goodsAttributeVO.getSpecList();
//                    Map<String, List<AttributeValueVO>> specMap = specList.stream().collect(Collectors.groupingBy(AttributeValueVO::getAttributeValue));
                    List<String> spuNameList = StringUtil.split(qualityImportDTO.getSpuNames(), "\\$\\$");
                    List<SpuDTO> filterSpuList = filterSpuList(spuNameList, errorSpuName);
                    if (CollectionUtils.isEmpty(filterSpuList)) {
                        continue;
                    }
                    List<Integer> spuIdList = filterSpuList.stream().map(SpuDTO::getId).collect(Collectors.toList());
                    QualityEntity qualityEntity = new QualityEntity()
                            .setStatus(DisableStatusEnum.ENABLE.getValue())
                            .setFactoryCode(qualityImportDTO.getFactoryCode())
//                            .setSpecId(specMap.get(qualityImportDTO.getSpecInfo().trim()).get(0).getAttributeValueId())
//                            .setSpecName(qualityImportDTO.getSpecInfo().trim())
                            .setSpuIds(StringUtils.join(spuIdList, ",") + ",")
                            .setSpuNames(StringUtils.join(spuNameList, " "))
                            .setStandardType(qualityImportDTO.getStandardType())
                            .setGoodsCategoryId(category2)
                            .setUsage(UsageEnum.getValueByDesc(qualityImportDTO.getUsageInfo()))
                            .setSalesType("销售".equals(qualityImportDTO.getSalesTypeInfo().trim()) ? ContractSalesTypeEnum.SALES.getValue() : ContractSalesTypeEnum.PURCHASE.getValue())
                            .setHasCustomer(StringUtils.isNotBlank(qualityImportDTO.getCustomerCodes()) || StringUtils.isNotBlank(qualityImportDTO.getEnterpriseCodes()) ? 1 : 0)
                            .setEnterprise(StringUtils.isNotBlank(qualityImportDTO.getEnterpriseCodes()) ? 1 : 0);
                    List<QualityRuleEntity> warehouseList = warehouseEntityList.stream().map(warehouseEntity -> {
                                        return new QualityRuleEntity().setReferId(warehouseEntity.getId())
                                                .setReferCode(warehouseEntity.getCode())
                                                .setReferValue(warehouseEntity.getName());
                                    }
                            )
                            .collect(Collectors.toList());
                    qualityEntity.setWarehouseList(warehouseList);
                    List<String> customerCodeList = new ArrayList<>();
                    if (StringUtils.isNotBlank(qualityImportDTO.getCustomerCodes())) {
                        customerCodeList = Arrays.stream(qualityImportDTO.getCustomerCodes().split(",")).distinct().collect(Collectors.toList());
                    }
                    if (StringUtils.isNotBlank(qualityImportDTO.getEnterpriseCodes())) {
                        customerCodeList = Arrays.stream(qualityImportDTO.getEnterpriseCodes().split(",")).distinct().collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(customerCodeList)) {
                        List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(customerCodeList);
                        List<QualityRuleEntity> customerList = customerEntityList.stream().map(customerEntity -> {
                                            return new QualityRuleEntity().setReferCode(customerEntity.getLinkageCustomerCode())
                                                    .setReferId(customerEntity.getId())
                                                    .setReferValue(customerEntity.getName());
                                        }

                                )
                                .collect(Collectors.toList());
                        qualityEntity.setCustomerList(customerList);
                        List<String> existCustomerCodeList = customerEntityList.stream().map(CustomerEntity::getLinkageCustomerCode).collect(Collectors.toList());
                        errorCustomerCodeList = CommonListUtil.getDifferences(customerCodeList, existCustomerCodeList);
                    }
                    if (!CollectionUtils.isEmpty(filterSpuList)) {
                        List<QualityRuleEntity> spuRuleList = filterSpuList.stream().map(spuDTO -> {
                            return new QualityRuleEntity()
                                    .setReferCode(spuDTO.getSpuNo())
                                    .setReferValue(spuDTO.getSpuName())
                                    .setReferId((spuDTO.getId()));
                        }).collect(Collectors.toList());
                        qualityEntity.setSpuList(spuRuleList);
                    }
                    qualityEntity.setContent(qualityImportDTO.getContent());
                    this.saveQuality(qualityEntity);
                }
            }
            return Result.success("异常的发货库点编码:" + FastJsonUtils.getBeanToJson(errorWarehouseCodeList) + ";\n异常的客户编码:" + FastJsonUtils.getBeanToJson(errorCustomerCodeList) +
                    ";\n异常的SPU名称:" + FastJsonUtils.getBeanToJson(errorSpuName));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("质量指标模板错误" + e.toString());
        }
    }

    private List<SpuDTO> filterSpuList(List<String> spuNameList, List<String> errorSpuName) {
        List<SpuDTO> spuList = spuFacade.querySpuDTOList(new SpuQO());
        Map<String, List<SpuDTO>> spuMap = spuList.stream().collect(Collectors.groupingBy(SpuDTO::getSpuName));
        List<SpuDTO> filterSpuList = new ArrayList<>();
        for (String spuName : spuNameList) {
            List<SpuDTO> spuDTOList = spuMap.get(spuName.trim());
            if (!CollectionUtils.isEmpty(spuDTOList)) {
                filterSpuList.addAll(spuDTOList);
            } else {
                errorSpuName.add(spuName);
            }
        }
        return filterSpuList;
    }

    @Override
    public void processHistoryData(Integer id) {
        List<QualityEntity> qualityList = qualityDao.list(QualityEntity.lqw()
//                .isNotNull(QualityEntity::getSpecId)
                .eq(StringUtil.isNotNullBlank(id), QualityEntity::getId, id));
        for (QualityEntity qualityEntity : qualityList) {
            List spuIdList = new ArrayList<>();
            if (null != qualityEntity.getSpecId()) {
                List<SkuEntity> skuList = skuFacade.querySkuBySpecId(qualityEntity.getSpecId());
                if (!CollectionUtils.isEmpty(skuList)) {
                    spuIdList = skuList.stream().map(SkuEntity::getSpuId).filter(StringUtil::isNotNullBlank).distinct().collect(Collectors.toList());
                }
            } else {
                List<QualityRuleEntity> skuList = qualityRuleDao.getQualityRuleListByCode(qualityEntity.getId(), "sku");
                List<Integer> skuIdList = skuList.stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
                List<SkuEntity> skuEntityList = skuFacade.getSkuListByIds(skuIdList);
                spuIdList = skuEntityList.stream().map(SkuEntity::getSpuId).filter(StringUtil::isNotNullBlank).distinct().collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(spuIdList)) {
                List<SpuDTO> spuDTOList = spuFacade.querySpuDTOList(new SpuQO().setSpuIdList(spuIdList));
                List<QualityRuleEntity> qualityRuleList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(spuDTOList)) {
                    List<SpuDTO> resultSpuDTOList = new ArrayList<>();
                    for (SpuDTO spuDTO : spuDTOList) {
                        List<SpuDTO> spuNamesList = spuFacade.querySpuDTOList(new SpuQO().setSpuName(spuDTO.getSpuName()));
                        resultSpuDTOList.addAll(spuNamesList);
                    }
                    log.info("质量指标历史数据清理=======qualityId:{},resultSpuDTOList={}", qualityEntity.getId(), FastJsonUtils.getBeanToJson(resultSpuDTOList));
                    for (SpuDTO spuDTO2 : resultSpuDTOList) {
                        QualityRuleEntity qualityRuleEntity = new QualityRuleEntity();
                        qualityRuleEntity.setReferId(spuDTO2.getId());
                        qualityRuleEntity.setReferCode(spuDTO2.getSpuNo());
                        qualityRuleEntity.setReferValue(spuDTO2.getSpuName());
                        qualityRuleList.add(qualityRuleEntity);
                    }
                    List<Integer> finalSpuIdList = resultSpuDTOList.stream().map(SpuDTO::getId).distinct().collect(Collectors.toList());
                    List<String> spuNameList = resultSpuDTOList.stream().map(SpuDTO::getSpuName).distinct().collect(Collectors.toList());
                    log.info("质量指标历史数据清洗=============qualityId:{},finalSpuIdList：{}", qualityEntity.getId(), FastJsonUtils.getBeanToJson(finalSpuIdList));
                    qualityEntity.setSpuIds(CollUtil.join((finalSpuIdList), ",") + ",");
                    qualityEntity.setSpuNames(CollUtil.join(spuNameList, " "));
//                qualityEntity.setUpdatedBy(JwtUtils.getCurrentUserId());
//                qualityEntity.setUpdatedAt(new Date());
                    qualityDao.updateById(qualityEntity);
                    // 删除
                    qualityRuleDao.dropQualityRule(qualityEntity.getId(), VariableBizCodeEnum.SPU.getValue());
                    // 新增
                    qualityRuleDao.recordQualityRuleList(qualityEntity.getId(), VariableBizCodeEnum.SPU.getValue(), qualityRuleList);

                }
            }
        }
    }

    /**
     * 同步记录发货库点/客户/集团客户-质量指标关联关系
     *
     * @param qualityEntity
     */
    private void syncQualityRuleList(QualityEntity qualityEntity) {
        qualityRuleDao.dropQualityRule(qualityEntity.getId(), null);
        //发货库点
        qualityRuleDao.recordQualityRuleList(qualityEntity.getId(), VariableBizCodeEnum.WAREHOUSE.getValue(), qualityEntity.getWarehouseList());
        if (!CollectionUtils.isEmpty(qualityEntity.getCustomerList())) {
            //专属客户/集团
            String qualityRuleCode = DisableStatusEnum.ENABLE.getValue().equals(qualityEntity.getEnterprise()) ?
                    VariableBizCodeEnum.ENTERPRISE.getValue() : VariableBizCodeEnum.CUSTOMER.getValue();
            qualityRuleDao.recordQualityRuleList(qualityEntity.getId(), qualityRuleCode, qualityEntity.getCustomerList());
        }
        //货品
        qualityRuleDao.recordQualityRuleList(qualityEntity.getId(), VariableBizCodeEnum.SPU.getValue(), qualityEntity.getSpuList());
    }

    /**
     * 新增/修改时，填充质量指标基础信息
     *
     * @param qualityEntity
     */
    private void fillQualityInfo(QualityEntity qualityEntity) {
        String warehouseIds = "";
        if (!CollectionUtils.isEmpty(qualityEntity.getWarehouseList())) {
            List<Integer> warehouseIdList = qualityEntity.getWarehouseList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
            warehouseIds = StringUtils.join(warehouseIdList, ",") + ",";
        }
        String customerIds = "";
        if (!CollectionUtils.isEmpty(qualityEntity.getCustomerList())) {
            List<Integer> customerIdList = qualityEntity.getCustomerList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
            customerIds = StringUtils.join(customerIdList, ",") + ",";
        }
        String customerNames = "";
        if (!CollectionUtils.isEmpty(qualityEntity.getCustomerList())) {
            List<String> customerNameList = qualityEntity.getCustomerList().stream().map(QualityRuleEntity::getReferValue).collect(Collectors.toList());
            customerNames = StringUtils.join(customerNameList, ",") + ",";
        }
        // 货品
        String spuIds = "";
        String spuNames = "";
        if (CollUtil.isNotEmpty(qualityEntity.getSpuList())) {
            List<Integer> spuIdList = qualityEntity.getSpuList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
            spuIds = StringUtils.join(spuIdList, ",") + ",";
            List<String> spuNameList = qualityEntity.getSpuList().stream().map(QualityRuleEntity::getReferValue).collect(Collectors.toList());
            spuNames = StringUtils.join(spuNameList, " ");
        }
        qualityEntity.setWarehouseIds(warehouseIds)
                .setHasCustomer(CollectionUtils.isEmpty(qualityEntity.getCustomerList()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue())
                .setCustomerIds(customerIds)
                .setCustomerNames(customerNames)
                .setSpuIds(spuIds)
                .setSpuNames(spuNames);
    }

}
