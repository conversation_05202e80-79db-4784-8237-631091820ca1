package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateGroupRelationMapper;
import com.navigator.husky.pojo.entity.TemplateGroupRelationEntity;
import com.navigator.husky.pojo.vo.TemplateGroupRelationVO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateGroupRelationDao extends BaseDaoImpl<TemplateGroupRelationMapper, TemplateGroupRelationEntity> {

    /**
     * 重新绑定模板条款组关系
     *
     * @param templateCode                    模板编号
     * @param templateGroupRelationEntityList 模板-条款组绑定关系数组
     */
    public void bindTemplateGroupRelation(String templateCode, List<TemplateGroupRelationVO> templateGroupRelationEntityList) {
        this.dropTemplateGroupRelation(templateCode);
        if (CollectionUtils.isEmpty(templateGroupRelationEntityList)) {
            return;
        }
        int sort = 0;
        for (TemplateGroupRelationVO templateGroupRelationVO : templateGroupRelationEntityList) {
            TemplateGroupRelationEntity groupRelationEntity =
                    new TemplateGroupRelationEntity().setId(null)
                            .setSort(sort)
                            .setTemplateCode(templateCode)
                            .setTemplateGroupCode(templateGroupRelationVO.getTemplateGroupCode())
                            .setCreatedAt(DateTimeUtil.now())
                            .setUpdatedAt(DateTimeUtil.now());
            this.save(groupRelationEntity);
            sort++;
        }
    }

    /**
     * 同步导入的模板-条款组关联关系
     *
     * @param templateCode
     * @param groupRelationList
     */
    public void syncImportTemplateGroupRelation(String templateCode, List<TemplateGroupRelationEntity> groupRelationList) {
        this.dropTemplateGroupRelation(templateCode);
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return;
        }
        groupRelationList.forEach(groupRelationEntity -> {
            groupRelationEntity.setId(null);
            this.save(groupRelationEntity);
        });
    }

    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param groupCodeList 条款组编码
     * @return
     */
    public List<TemplateGroupRelationEntity> getTemplateByGroupCodeList(List<String> groupCodeList) {
        if (CollectionUtils.isEmpty(groupCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<TemplateGroupRelationEntity>()
                .in(CollectionUtils.isNotEmpty(groupCodeList), TemplateGroupRelationEntity::getTemplateGroupCode, groupCodeList)
                .eq(TemplateGroupRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param templateGroupCode 条款组编码
     * @param templateCode      条款编码
     * @return
     */
    public List<TemplateGroupRelationEntity> getTemplateByCode(String templateGroupCode, String templateCode) {
        return this.list(new LambdaQueryWrapper<TemplateGroupRelationEntity>()
                .eq(StringUtils.isNotBlank(templateGroupCode), TemplateGroupRelationEntity::getTemplateGroupCode, templateGroupCode)
                .eq(StringUtils.isNotBlank(templateCode), TemplateGroupRelationEntity::getTemplateCode, templateCode)
                .eq(TemplateGroupRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateGroupRelationEntity::getSort)
        );
    }

    /**
     * 同步更新条款组编码
     *
     * @param oldGroupCode
     * @param newGroupCode
     */
    public void syncTemplateGroupCode(String oldGroupCode, String newGroupCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateGroupRelationEntity::getTemplateGroupCode, oldGroupCode)
                .eq(TemplateGroupRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateGroupRelationEntity::getTemplateGroupCode, newGroupCode)
                .set(TemplateGroupRelationEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    /**
     * 解绑模板-条款组关系
     *
     * @param templateCode
     */
    public void dropTemplateGroupRelation(String templateCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateGroupRelationEntity::getTemplateCode, templateCode)
                .eq(TemplateGroupRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateGroupRelationEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(TemplateGroupRelationEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
