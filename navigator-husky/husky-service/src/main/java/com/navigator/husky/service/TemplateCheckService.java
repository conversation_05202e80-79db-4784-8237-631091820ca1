package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 14:12
 **/
public interface TemplateCheckService {

    Result queryCheckByCondition(QueryDTO<TemplateCheckEntity> queryDTO);

    Boolean saveTemplateCheck(TemplateCheckEntity checkEntity);

    Boolean updateTemplateCheck(TemplateCheckEntity checkEntity);

    List<TemplateCheckEntity> getCheckListByTemplateCode(String templateCode);

    TemplateCheckEntity getTemplateCheckById(Integer id);

    Boolean updateCheckStatus(Integer id, Integer status);

    List<TemplateCheckEntity> queryExportTemplateCheckList(TemplateCheckEntity checkQueryDTO);
}
