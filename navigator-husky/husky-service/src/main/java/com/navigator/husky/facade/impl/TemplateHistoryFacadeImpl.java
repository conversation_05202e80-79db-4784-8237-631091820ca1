package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateHistoryFacade;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;
import com.navigator.husky.service.TemplateHistoryService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-07 18:43
 **/
@RestController
public class TemplateHistoryFacadeImpl implements TemplateHistoryFacade {
    @Resource
    private TemplateHistoryService templateHistoryService;

    @Override
    public Result queryHistoryByCondition(QueryDTO<TemplateHistoryEntity> queryDTO) {
        return templateHistoryService.queryHistoryByCondition(queryDTO);
    }

    @Override
    public List<EnumValueDTO> getOperationTypeList() {
        return templateHistoryService.getOperationTypeList();
    }
}
