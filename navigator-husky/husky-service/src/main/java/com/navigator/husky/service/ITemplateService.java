package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.*;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.pojo.vo.HealthCheckVO;
import com.navigator.husky.pojo.vo.TemplateItemRelationVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 17:22
 **/
public interface ITemplateService {
    Result queryByCondition(QueryDTO<QueryTemplateQO> queryDTO);

    Boolean saveTemplate(TemplateEntity templateEntity);

    Boolean updateTemplate(TemplateEntity templateEntity);

    Result copyTemplateInfo(CopyTemplateDTO copyTemplateDTO);

    Result updateStatus(Integer templateId, Integer status);

    TemplateEntity getTemplateById(Integer templateId);

    List<TemplateEntity> getAllTemplateList(Integer status);

    List<TemplateEntity> getTemplateListByGroupCode(String templateGroupCode);

    Boolean bindTemplateItemRelation(TemplateItemRelationDTO itemRelationDTO);

    Boolean removeTemplateItemRelation(String templateCode, String templateItemCode);

    List<TemplateItemRelationVO> getTemplateItemRelation(String templateCode, String templateItemCode);

    Result syncTemplateRelation(List<TemplateRelationSyncDTO> templateRelationSyncList);

    Result markMainVersion(MarkMainVersionDTO markMainVersionDTO);

    HealthCheckVO healthCheck(Integer templateId);

    Result exportTemplateJson(QueryTemplateQO templateQO);

    Result importTemplateJson(MultipartFile file);

}
