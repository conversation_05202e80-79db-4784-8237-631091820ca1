package com.navigator.husky.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.bisiness.enums.LogicRelationEnum;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.bisiness.enums.ValueTypeEnum;
import com.navigator.common.dto.DroolsRuleBizInfoDTO;
import com.navigator.common.dto.DroolsRuleDataDTO;
import com.navigator.common.util.DroolsUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.husky.dao.VariableDao;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import com.navigator.husky.pojo.dto.RuleScriptDTO;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.entity.VariableEntity;
import com.navigator.trade.pojo.dto.contractsign.KeyVariableDTO;
import com.navigator.trade.pojo.dto.contractsign.SignHuskyTemplateDTO;
import com.navigator.trade.pojo.dto.contractsign.TemplateConditionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TemplateContentBuilder {
    @Autowired
    private VariableDao variableDao;
    @Autowired
    private DictItemFacade dictItemFacade;

    public DroolsRuleBizInfoDTO convertBizParameter(SignHuskyTemplateDTO templateDTO) {
        TemplateConditionDTO templateCondition = templateDTO.getTemplateCondition();
        Map<String, Object> conditionMap = BeanUtil.beanToMap(templateCondition);

        KeyVariableDTO keyVariableDTO = templateDTO.getKeyVariableDTO();
        Map<String, Object> keyVariableMap = BeanUtil.beanToMap(keyVariableDTO);

        conditionMap.putAll(keyVariableMap);
        return new DroolsRuleBizInfoDTO().setMapBizData(conditionMap);
    }

    public DroolsRuleBizInfoDTO convertAllParameterMap(SignHuskyTemplateDTO templateDTO) {
        DroolsRuleBizInfoDTO droolsRuleBizInfoDTO = this.convertBizParameter(templateDTO);
        Map<String, Object> bizDataMap = BeanUtil.beanToMap(templateDTO);
        bizDataMap.putAll(droolsRuleBizInfoDTO.getMapBizData());
        return droolsRuleBizInfoDTO.setMapBizData(bizDataMap);
    }

    public List<String> matchGroup(List<TemplateGroupEntity> groupEntityList, DroolsRuleBizInfoDTO droolsRuleBizInfoDTO) {
        if (CollectionUtils.isEmpty(groupEntityList)) {
            return new ArrayList<>();
        }
        List<DroolsRuleDataDTO> droolsRuleDataDTOList = new ArrayList<>();
        for (TemplateGroupEntity templateGroupEntity : groupEntityList) {
            DroolsRuleDataDTO droolsRuleDataDTO = new DroolsRuleDataDTO();
            droolsRuleDataDTO.setRuleCode(templateGroupEntity.getCode());
            droolsRuleDataDTO.setRuleInfo(templateGroupEntity.getRuleInfo());
            droolsRuleDataDTOList.add(droolsRuleDataDTO);
        }
        DroolsUtil.runRuleInfos(droolsRuleDataDTOList, droolsRuleBizInfoDTO);
        return droolsRuleBizInfoDTO.getMatchRules();

    }

    public List<String> matchItem(List<TemplateItemEntity> itemEntityList, DroolsRuleBizInfoDTO droolsRuleBizInfoDTO) {
        if (CollectionUtils.isEmpty(itemEntityList)) {
            return new ArrayList<>();
        }
        List<DroolsRuleDataDTO> droolsRuleDataDTOList = new ArrayList<>();
        for (TemplateItemEntity itemEntity : itemEntityList) {
            DroolsRuleDataDTO droolsRuleDataDTO = new DroolsRuleDataDTO();
            droolsRuleDataDTO.setRuleCode(itemEntity.getCode());
            droolsRuleDataDTO.setRuleInfo(itemEntity.getRuleInfo());
            droolsRuleDataDTOList.add(droolsRuleDataDTO);
        }
        DroolsUtil.runRuleInfos(droolsRuleDataDTOList, droolsRuleBizInfoDTO);

        return droolsRuleBizInfoDTO.getMatchRules();
    }

    public List<String> matchTemplateCheck(List<TemplateCheckEntity> needRuleCheckList, DroolsRuleBizInfoDTO droolsRuleBizInfoDTO) {
        if (CollectionUtils.isEmpty(needRuleCheckList)) {
            return new ArrayList<>();
        }
        List<DroolsRuleDataDTO> droolsRuleDataDTOList = new ArrayList<>();
        for (TemplateCheckEntity templateCheck : needRuleCheckList) {
            DroolsRuleDataDTO droolsRuleDataDTO = new DroolsRuleDataDTO();
            droolsRuleDataDTO.setRuleCode("template_check_" + templateCheck.getId());
            droolsRuleDataDTO.setRuleInfo(templateCheck.getRuleInfo());
            droolsRuleDataDTOList.add(droolsRuleDataDTO);
        }
        DroolsUtil.runRuleInfos(droolsRuleDataDTOList, droolsRuleBizInfoDTO);
        return droolsRuleBizInfoDTO.getMatchRules();

    }


    /**
     * 拼接条件信息
     * (A&&B||C)||(D&&E）
     * 合同类型=一口价; 且交货工厂=ZJG、YZ、ZS
     *
     * @param conditionVariableList
     * @return
     */
    public RuleScriptDTO jointConditionInfo(List<ConditionVariableDTO> conditionVariableList) {
        String jointConditionInfo = "";
        String jointRuleInfo = "";
        if (CollectionUtils.isEmpty(conditionVariableList)) {
            return null;
        }
        conditionVariableList.forEach(conditionVariableDTO -> {
            //组装单个条件语句，及基本展示信息的组装
            VariableEntity variableEntity = variableDao.getById(conditionVariableDTO.getConditionVariableId());
            String conditionValueInfo = StringUtils.join(conditionVariableDTO.getConditionDescList(), ",");
            conditionValueInfo = PatternRelationEnum.getContainRelation().contains(conditionVariableDTO.getPatternRelation()) ? "[" + conditionValueInfo + "]" : conditionValueInfo;
            conditionVariableDTO.setConditionVariableInfo(variableEntity.getName())
                    .setPatternRelationInfo(PatternRelationEnum.getByCode(conditionVariableDTO.getPatternRelation()).getDescription())
                    .setLogicRelationInfo(StringUtils.isNotBlank(conditionVariableDTO.getLogicRelation()) ? LogicRelationEnum.getByCode(conditionVariableDTO.getLogicRelation()).getDescription() : "")
                    .setConditionValueInfo(conditionValueInfo)
                    //排序
                    .setSort(conditionVariableList.indexOf(conditionVariableDTO) + 1);
            String separator = ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? "\"" : "";
            //拼接单个条件：("北京，上海，成都" contains city) (city memberOf "北京，上海，成都")
            String patternRelation = conditionVariableDTO.getPatternRelation();
            String ruleInfo = "";
            ruleInfo = "mapBizData.get(\"" + conditionVariableDTO.getConditionVariable() + "\") " +
                    patternRelation + " " +
                    //todo
                    (separator + conditionVariableDTO.getConditionValue() + separator);
            //除了modifyList(这里是list contains ??语法)
            if (PatternRelationEnum.getContainRelation().contains(conditionVariableDTO.getPatternRelation())) {
                ruleInfo = ("[" + separator + StringUtils.join(conditionVariableDTO.getConditionValueList(), separator + "," + separator) + separator + "] ") +
                        conditionVariableDTO.getPatternRelation() + " " +
                        "mapBizData.get(\"" + conditionVariableDTO.getConditionVariable() + "\")";
            }
            if ("modifyList".equals(conditionVariableDTO.getConditionVariable())) {
                if (PatternRelationEnum.EQUALS.getCode().equals(patternRelation)) {
                    patternRelation = PatternRelationEnum.CONTAINS.getCode();
                }
                if (PatternRelationEnum.NOT_EQUALS.getCode().equals(patternRelation)) {
                    patternRelation = PatternRelationEnum.NOT_CONTAINS.getCode();
                }
                conditionVariableDTO.setModifyPatternRelation(patternRelation);
                //modifyList的语法在这里拼装
                ruleInfo = conditionVariableDTO.getConditionValueList().stream().map(modifyValue -> {
                    return "mapBizData.get(\"" + conditionVariableDTO.getConditionVariable() + "\") " +
                            conditionVariableDTO.getModifyPatternRelation() + " " +
                            //todo
                            (separator + modifyValue + separator);
                }).collect(Collectors.joining(" || "));
                ruleInfo = ruleInfo + " || " + "mapBizData.get(\"modifyList\") contains \"ldc\"";
                ruleInfo = "(" + ruleInfo + ")";
            }
            conditionVariableDTO.setRuleInfo(ruleInfo);
        });
        log.info("1组装基本规则信息===============" + FastJsonUtils.getBeanToJson(conditionVariableList));
        //按照优先级拼接规则信息
        Map<Integer, List<ConditionVariableDTO>> conditionVariableMap = conditionVariableList.stream().collect(Collectors.groupingBy(ConditionVariableDTO::getLevel));
        //条件组装是否有多个层级，如果多个层级，则用大括号拼接
        Boolean needBracket = conditionVariableMap.size() > 1;
        for (Map.Entry<Integer, List<ConditionVariableDTO>> variableEntry : conditionVariableMap.entrySet()) {
            List<ConditionVariableDTO> variableDTOList = variableEntry.getValue();
            //规则条件描述-展示
            StringBuilder conditionInfo = new StringBuilder();
            //规则条件脚本
            StringBuilder ruleInfo = new StringBuilder();
            for (ConditionVariableDTO conditionVariableDTO : variableDTOList) {
                conditionInfo = conditionInfo.append(needBracket && variableDTOList.indexOf(conditionVariableDTO) == 0 ? "" : conditionVariableDTO.getLogicRelationInfo())
                        .append(conditionVariableDTO.getConditionVariableInfo())
                        .append(conditionVariableDTO.getPatternRelationInfo())
                        .append(conditionVariableDTO.getConditionValueInfo() + ";");
                ruleInfo = ruleInfo.append(needBracket && variableDTOList.indexOf(conditionVariableDTO) == 0 ? "" : conditionVariableDTO.getLogicRelation() + " ")
                        .append(conditionVariableDTO.getRuleInfo() + " ");
            }
            //多个条件加逻辑关系拼接
            jointConditionInfo = needBracket ? jointConditionInfo + variableDTOList.get(0).getLogicRelationInfo() + "(" + conditionInfo.toString() + ")" : jointConditionInfo + conditionInfo;
            jointRuleInfo = needBracket ? jointRuleInfo + variableDTOList.get(0).getLogicRelation() + "(" + ruleInfo.toString() + ")" : jointRuleInfo + ruleInfo.toString();
        }
        return new RuleScriptDTO()
                .setJointConditionInfo(jointConditionInfo)
                .setJointRuleInfo(jointRuleInfo);
    }

}
