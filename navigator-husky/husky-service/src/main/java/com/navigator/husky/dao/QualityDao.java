package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.husky.mapper.QualityMapper;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.entity.QualityRuleEntity;
import com.navigator.husky.pojo.enums.VariableBizCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 15:59
 **/
@Dao
@Slf4j
public class QualityDao extends BaseDaoImpl<QualityMapper, QualityEntity> {
    @Resource
    private QualityRuleDao qualityRuleDao;

    public IPage<QualityEntity> queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO,
                                                        List<Integer> filterCustomerQualityIdList,
                                                        List<Integer> filterEnterpriseQualityIdList,
                                                        List<Integer> filterSkuQualityIdList) {
        QualityInfoDTO qualityInfoDTO = queryDTO.getCondition();
        if (null != qualityInfoDTO.getCustomerId() && CollectionUtils.isEmpty(filterCustomerQualityIdList)) {
            return this.page(new Page<>(0, 0, 0));
        }
        if (null != qualityInfoDTO.getEnterpriseId() && CollectionUtils.isEmpty(filterEnterpriseQualityIdList)) {
            return this.page(new Page<>(0, 0, 0));
        }
        if (null != qualityInfoDTO.getSpuId() && CollectionUtils.isEmpty(filterSkuQualityIdList)) {
            return this.page(new Page<>(0, 0, 0));
        }
        LambdaQueryWrapper<QualityEntity> generalWrapper = this.getQualityQueryWrapper(qualityInfoDTO, filterCustomerQualityIdList, filterEnterpriseQualityIdList, filterSkuQualityIdList);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), generalWrapper);
    }

    public List<QualityEntity> queryQualityList(QualityInfoDTO qualityInfoDTO,
                                                List<Integer> filterCustomerQualityIdList,
                                                List<Integer> filterEnterpriseQualityIdList,
                                                List<Integer> filterSkuQualityIdList) {
        if (null != qualityInfoDTO.getCustomerId() && CollectionUtils.isEmpty(filterCustomerQualityIdList)) {
            return new ArrayList<>();
        }
        if (null != qualityInfoDTO.getEnterpriseId() && CollectionUtils.isEmpty(filterEnterpriseQualityIdList)) {
            return new ArrayList<>();
        }
        if (null != qualityInfoDTO.getSpuId() && CollectionUtils.isEmpty(filterSkuQualityIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<QualityEntity> generalWrapper = this.getQualityQueryWrapper(qualityInfoDTO, filterCustomerQualityIdList, filterEnterpriseQualityIdList, filterSkuQualityIdList);
        return this.list(generalWrapper);
    }

    private LambdaQueryWrapper<QualityEntity> getQualityQueryWrapper(QualityInfoDTO qualityInfoDTO,
                                                                     List<Integer> filterCustomerQualityIdList,
                                                                     List<Integer> filterEnterpriseQualityIdList,
                                                                     List<Integer> filterSpuQualityIdList) {
        LambdaQueryWrapper<QualityEntity> generalWrapper = this.getQualityWrapper(qualityInfoDTO);
        if (null != qualityInfoDTO.getCustomerId()) {
            generalWrapper.eq(null != qualityInfoDTO.getCustomerId(), QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
                    .eq(null != qualityInfoDTO.getCustomerId(), QualityEntity::getEnterprise, DisableStatusEnum.DISABLE.getValue())
                    .in(null != qualityInfoDTO.getCustomerId(), QualityEntity::getId, filterCustomerQualityIdList);
//                    .like(null != qualityInfoDTO.getCustomerId(), QualityEntity::getCustomerIds, qualityInfoDTO.getCustomerId() + ",");
        }
        if (null != qualityInfoDTO.getEnterpriseId()) {
            generalWrapper.eq(QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
                    .eq(QualityEntity::getEnterprise, DisableStatusEnum.ENABLE.getValue())
                    .in(null != qualityInfoDTO.getEnterpriseId(), QualityEntity::getId, filterEnterpriseQualityIdList);
//                    .like(QualityEntity::getCustomerIds, qualityInfoDTO.getEnterpriseId() + ",");
        }
        if (null != qualityInfoDTO.getSpuId()) {
            generalWrapper.in(null != qualityInfoDTO.getSpuId(), QualityEntity::getId, filterSpuQualityIdList);
        }
        generalWrapper.orderByDesc(QualityEntity::getId);
        return generalWrapper;
    }
//
//    public IPage<QualityEntity> queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO) {
//        QualityInfoDTO qualityInfoDTO = queryDTO.getCondition();
//        LambdaQueryWrapper<QualityEntity> generalWrapper = this.getQualityWrapper(qualityInfoDTO);
//        if (null != qualityInfoDTO.getViewEnterpriseId() || null != qualityInfoDTO.getCustomerId()) {
//            generalWrapper.eq(QualityEntity::getHasCustomer, DisableStatusEnum.DISABLE.getValue());
//            //专属客户
//            generalWrapper.or(QueryWrapper -> QueryWrapper
//                    //集团客户判断
//                    .eq(null != qualityInfoDTO.getViewEnterpriseId(), QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
//                    .eq(null != qualityInfoDTO.getViewEnterpriseId(), QualityEntity::getEnterprise, DisableStatusEnum.DISABLE.getValue())
//                    .like(null != qualityInfoDTO.getViewEnterpriseId(), QualityEntity::getCustomerIds, qualityInfoDTO.getViewEnterpriseId() + ",")
//                    .and(customerWrapper -> customerWrapper
//                            .eq(null != qualityInfoDTO.getCustomerId(), QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
//                            .eq(null != qualityInfoDTO.getCustomerId(), QualityEntity::getEnterprise, DisableStatusEnum.DISABLE.getValue())
//                            .like(null != qualityInfoDTO.getCustomerId(), QualityEntity::getCustomerIds, qualityInfoDTO.getCustomerId() + ",")
//                            //集团客户判断
//                            .or()
//                            .eq(0 < qualityInfoDTO.getEnterpriseId(), QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
//                            .eq(0 < qualityInfoDTO.getEnterpriseId(), QualityEntity::getEnterprise, DisableStatusEnum.ENABLE.getValue())
//                            .like(0 < qualityInfoDTO.getEnterpriseId(), QualityEntity::getCustomerIds, qualityInfoDTO.getEnterpriseId() + ",")
//                    ));
//        }
//        generalWrapper.orderByDesc(QualityEntity::getId);
//        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), generalWrapper);
//    }

    /**
     * 根据质量指标匹配信息-匹配质量指标
     * （匹配规则:1、优先专属客户 ——> 2、集团客户 ——> 3、通用不区分客户）
     *
     * @param qualityInfoDTO
     * @return
     */
    public QualityEntity matchQuality(QualityInfoDTO qualityInfoDTO,
                                      List<Integer> filterCustomerQualityIdList,
                                      List<Integer> filterEnterpriseQualityIdList,
                                      List<Integer> filterSpuQualityIdList) {
        if (null != qualityInfoDTO.getGoodsId() && CollectionUtils.isEmpty(filterSpuQualityIdList)) {
            return null;
        }
        //1、优先查询专属客户
        if (!CollectionUtils.isEmpty(filterCustomerQualityIdList)) {
            LambdaQueryWrapper<QualityEntity> customerWrapper = this.getQualityWrapper(qualityInfoDTO)
                    .eq(QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
                    .eq(QualityEntity::getEnterprise, DisableStatusEnum.DISABLE.getValue())
                    .in(QualityEntity::getId, filterCustomerQualityIdList)
                    .in(null != qualityInfoDTO.getGoodsId(), QualityEntity::getId, filterSpuQualityIdList);
//                .like(QualityEntity::getCustomerIds, qualityInfoDTO.getCustomerId() + ",");
            List<QualityEntity> customerQualityList = this.list(customerWrapper);
            if (!CollectionUtils.isEmpty(customerQualityList)) {
                return customerQualityList.get(0);
            }
        }

        //2、查询集团客户配置
        if (!CollectionUtils.isEmpty(filterEnterpriseQualityIdList)) {
            // 当存在集团公司时，匹配集团公司
            LambdaQueryWrapper<QualityEntity> enterpriseWrapper = this.getQualityWrapper(qualityInfoDTO)
                    .eq(QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
                    .eq(QualityEntity::getEnterprise, DisableStatusEnum.ENABLE.getValue())
                    .in(QualityEntity::getId, filterEnterpriseQualityIdList)
                    .in(null != qualityInfoDTO.getGoodsId(), QualityEntity::getId, filterSpuQualityIdList);
//                    .like(QualityEntity::getCustomerIds, qualityInfoDTO.getEnterpriseId() + ",");
            List<QualityEntity> enterpriseQualityList = this.list(enterpriseWrapper);
            if (!CollectionUtils.isEmpty(enterpriseQualityList)) {
                return enterpriseQualityList.get(0);
            }
        }
        // 3、查询不区分客户通用的
        LambdaQueryWrapper<QualityEntity> generalWrapper = this.getQualityWrapper(qualityInfoDTO)
                .in(null != qualityInfoDTO.getGoodsId(), QualityEntity::getId, filterSpuQualityIdList)
                .eq(QualityEntity::getHasCustomer, DisableStatusEnum.DISABLE.getValue());
        List<QualityEntity> generalQualityList = this.list(generalWrapper);
        return CollectionUtils.isEmpty(generalQualityList) ? null : generalQualityList.get(0);
    }


    private LambdaQueryWrapper<QualityEntity> getQualityWrapper(QualityInfoDTO qualityInfoDTO) {
        LambdaQueryWrapper<QualityEntity> lqw = new LambdaQueryWrapper<QualityEntity>()
                .eq(null != qualityInfoDTO.getGoodsCategoryId(), QualityEntity::getGoodsCategoryId, qualityInfoDTO.getGoodsCategoryId())
                .eq(StringUtils.isNotBlank(qualityInfoDTO.getFactoryCode()), QualityEntity::getFactoryCode, qualityInfoDTO.getFactoryCode())
                .eq(null != qualityInfoDTO.getUsage(), QualityEntity::getUsage, qualityInfoDTO.getUsage())
//                .eq(null != qualityInfoDTO.getSpuId(), QualityEntity::getSpuIds, qualityInfoDTO.getSpuId())
                .eq(null != qualityInfoDTO.getSalesType(), QualityEntity::getSalesType, qualityInfoDTO.getSalesType())
                .eq(StringUtils.isNotBlank(qualityInfoDTO.getStandardType()) && GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(qualityInfoDTO.getGoodsCategoryId()), QualityEntity::getStandardType, qualityInfoDTO.getStandardType())
//                .like(null != qualityInfoDTO.getWarehouseId(), QualityEntity::getWarehouseIds, qualityInfoDTO.getWarehouseId() + ",")
                .eq(null != qualityInfoDTO.getStatus(), QualityEntity::getStatus, qualityInfoDTO.getStatus());
        // 搜索库点
        if (StringUtil.isNotNullBlank(qualityInfoDTO.getWarehouseId())) {
            // 匹配 “1,” “xxx,1,”
            lqw.and(QueryWrapper ->
                    QueryWrapper
                            .likeRight(QualityEntity::getWarehouseIds, qualityInfoDTO.getWarehouseId() + ",")
                            .or()
                            .like(QualityEntity::getWarehouseIds, "," + qualityInfoDTO.getWarehouseId() + ",")
            );
        }
        return lqw;
    }

    private LambdaQueryWrapper<QualityEntity> getQualityEntityWrapper(QualityEntity qualityEntity) {
        return new LambdaQueryWrapper<QualityEntity>()
                .eq(QualityEntity::getGoodsCategoryId, qualityEntity.getGoodsCategoryId())
                .eq(QualityEntity::getFactoryCode, qualityEntity.getFactoryCode())
                .eq(QualityEntity::getUsage, qualityEntity.getUsage())
                .eq(StringUtils.isNotBlank(qualityEntity.getStandardType()) && GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(qualityEntity.getGoodsCategoryId()), QualityEntity::getStandardType, qualityEntity.getStandardType())
//                .eq(QualityEntity::getSpecId, qualityEntity.getSpecId())
                .eq(QualityEntity::getSalesType, qualityEntity.getSalesType())
//                .eq(QualityEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                ;
    }

    /**
     * 校验新增/修改是否有重复配置
     * true:重复 false:无重复
     *
     * @param qualityEntity
     * @return
     */
    public boolean validateQualityDuplicate(QualityEntity qualityEntity) {
        //无客户配置
        LambdaQueryWrapper<QualityEntity> qualityEntityWrapper = this.getQualityEntityWrapper(qualityEntity)
                //更新排除当前ID
                .ne(null != qualityEntity.getId(), QualityEntity::getId, qualityEntity.getId());
        List<Integer> warehouseIdList = qualityEntity.getWarehouseList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
        List<Integer> spuIdList = qualityEntity.getSpuList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(qualityEntity.getCustomerList())) {
            qualityEntityWrapper.eq(QualityEntity::getHasCustomer, DisableStatusEnum.DISABLE.getValue());
            List<QualityEntity> qualityEntityList = this.list(qualityEntityWrapper);
            if (CollectionUtils.isEmpty(qualityEntityList)) {
                return false;
            }
            List<Integer> qualityIdList = qualityEntityList.stream().map(QualityEntity::getId).collect(Collectors.toList());
            List<QualityRuleEntity> warehouseRuleList = qualityRuleDao.getQualityRuleList(qualityIdList, VariableBizCodeEnum.WAREHOUSE.getValue(), warehouseIdList);
            if (CollectionUtils.isEmpty(warehouseRuleList)) {
                return false;
            }
            List<QualityRuleEntity> spuRuleList = qualityRuleDao.getQualityRuleList(qualityIdList, VariableBizCodeEnum.SPU.getValue(), spuIdList);
            if (CollectionUtils.isEmpty(spuRuleList)) {
                return false;
            }
            List<Integer> warehouseQualityIdList = warehouseRuleList.stream().map(QualityRuleEntity::getQualityId).distinct().collect(Collectors.toList());
            List<Integer> goodsQualityIdList = spuRuleList.stream().map(QualityRuleEntity::getQualityId).distinct().collect(Collectors.toList());
            List<Integer> commonQualityIdList = CommonListUtil.getIntersection(warehouseQualityIdList, goodsQualityIdList);
            log.info("质量指标-重复" + FastJsonUtils.getBeanToJson(commonQualityIdList));
            return !CollectionUtils.isEmpty(commonQualityIdList);
        } else {
            //集团客户
            qualityEntityWrapper.eq(QualityEntity::getHasCustomer, DisableStatusEnum.ENABLE.getValue())
                    .eq(QualityEntity::getEnterprise, qualityEntity.getEnterprise());
            List<QualityEntity> qualityEntityList = this.list(qualityEntityWrapper);
            if (CollectionUtils.isEmpty(qualityEntityList)) {
                return false;
            }
            List<Integer> qualityIdList = qualityEntityList.stream().map(QualityEntity::getId).collect(Collectors.toList());
            List<Integer> customerIdList = qualityEntity.getCustomerList().stream().map(QualityRuleEntity::getReferId).collect(Collectors.toList());
            List<QualityRuleEntity> warehouseRuleList = qualityRuleDao.getQualityRuleList(qualityIdList, VariableBizCodeEnum.WAREHOUSE.getValue(), warehouseIdList);
            if (CollectionUtils.isEmpty(warehouseRuleList)) {
                return false;
            }
            List<QualityRuleEntity> spuRuleList = qualityRuleDao.getQualityRuleList(qualityIdList, VariableBizCodeEnum.SPU.getValue(), spuIdList);
            if (CollectionUtils.isEmpty(spuRuleList)) {
                return false;
            }
            String qualityRuleCode = DisableStatusEnum.ENABLE.getValue().equals(qualityEntity.getEnterprise()) ?
                    VariableBizCodeEnum.ENTERPRISE.getValue() : VariableBizCodeEnum.CUSTOMER.getValue();
            List<QualityRuleEntity> customerRuleList = qualityRuleDao.getQualityRuleList(qualityIdList, qualityRuleCode, customerIdList);
            if (CollectionUtils.isEmpty(customerRuleList)) {
                return false;
            }
            List<Integer> warehouseQualityIdList = warehouseRuleList.stream().map(QualityRuleEntity::getQualityId).distinct().collect(Collectors.toList());
            List<Integer> customerQualityIdList = customerRuleList.stream().map(QualityRuleEntity::getQualityId).distinct().collect(Collectors.toList());
            List<Integer> spuQualityIdList = spuRuleList.stream().map(QualityRuleEntity::getQualityId).distinct().collect(Collectors.toList());

            List<Integer> commonQualityIdList = CommonListUtil.getIntersection(warehouseQualityIdList, customerQualityIdList);
            if (CollectionUtils.isEmpty(commonQualityIdList)) {
                return false;
            }
            commonQualityIdList = CommonListUtil.getIntersection(commonQualityIdList, spuQualityIdList);
            log.info("质量指标-重复的质量指标：==================" + FastJsonUtils.getBeanToJson(commonQualityIdList));
            return !CollectionUtils.isEmpty(commonQualityIdList);
        }
    }

}
