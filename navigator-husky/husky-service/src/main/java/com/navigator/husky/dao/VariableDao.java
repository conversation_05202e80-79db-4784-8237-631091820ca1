package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.husky.mapper.VariableMapper;
import com.navigator.husky.pojo.entity.VariableEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:57
 **/
@Dao
public class VariableDao extends BaseDaoImpl<VariableMapper, VariableEntity> {

    public List<VariableEntity> getAllVariableList(Integer isCondition, Integer isKey) {
        return this.list(new LambdaQueryWrapper<VariableEntity>()
                .eq(null != isCondition, VariableEntity::getIsCondition, isCondition)
                .eq(null != isKey, VariableEntity::getIsKey, isKey)
                .eq(VariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public IPage<VariableEntity> queryVariableByCondition(QueryDTO<VariableEntity> queryDTO) {
        VariableEntity variableQO = queryDTO.getCondition();
        LambdaQueryWrapper<VariableEntity> queryWrapper = getVariableQueryWrapper(variableQO);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<VariableEntity> queryExportVariableList(VariableEntity variableQO) {
        LambdaQueryWrapper<VariableEntity> queryWrapper = getVariableQueryWrapper(variableQO);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<VariableEntity> getVariableQueryWrapper(VariableEntity variableQO) {
        LambdaQueryWrapper<VariableEntity> queryWrapper = new LambdaQueryWrapper<VariableEntity>()
                .eq(null != variableQO.getValueType(), VariableEntity::getValueType, variableQO.getValueType())
                .eq(null != variableQO.getIsKey(), VariableEntity::getIsKey, variableQO.getIsKey())
                .eq(null != variableQO.getIsCondition(), VariableEntity::getIsCondition, variableQO.getIsCondition());
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(variableQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(variableQO.getSearchKey()), VariableEntity::getName, "%" + variableQO.getSearchKey().trim() + "%")
                    .or(StringUtils.isNotBlank(variableQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(variableQO.getSearchKey()), VariableEntity::getDisplayName, "%" + variableQO.getSearchKey().trim() + "%"));
        }
        return queryWrapper;
    }

    /**
     * 根据变量编码获取变量
     *
     * @param code 编码
     * @return
     */
    public VariableEntity getVariableByCode(String code) {
        List<VariableEntity> variableEntityList = this.list(new LambdaQueryWrapper<VariableEntity>()
                .eq(VariableEntity::getCode, code)
                .eq(VariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(variableEntityList) ? variableEntityList.get(0) : null;
    }

    /**
     * 根据变量展示名获取变量
     *
     * @param displayName 变量展示名
     * @return
     */
    public VariableEntity getVariableByDisplayName(String displayName) {
        List<VariableEntity> variableEntityList = this.list(new LambdaQueryWrapper<VariableEntity>()
                .eq(VariableEntity::getDisplayName, displayName)
                .eq(VariableEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
        return CollectionUtils.isNotEmpty(variableEntityList) ? variableEntityList.get(0) : null;
    }
}
