package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.VariableEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 14:36
 **/
public interface VariableService {
    Boolean updateVariable(VariableEntity variableEntity);

    Result queryVariableByCondition(QueryDTO<VariableEntity> queryDTO);

    List<VariableEntity> queryExportVariableList(VariableEntity queryDTO);

    List<VariableEntity> getAllVariableList(Integer isCondition, Integer isKey);

    List<VariableEntity> getAllVariableBasicList();

    VariableEntity getVariableByCode(String code);

    VariableEntity getVariableByDisplayName(String displayName);

    List<String> getNotUsedVariableList();

    Result importVariableInfo(MultipartFile file);

}
