package com.navigator.husky.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.TemplateUtil;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.husky.dao.TemplateGroupDao;
import com.navigator.husky.dao.TemplateHistoryLogDao;
import com.navigator.husky.dao.TemplateItemDao;
import com.navigator.husky.dao.TemplateItemRelationDao;
import com.navigator.husky.pojo.dto.TemplateItemJsonDTO;
import com.navigator.husky.pojo.entity.*;
import com.navigator.husky.pojo.enums.VersionType;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-07 17:59
 **/
@Service
@Slf4j
public class TemplateItemServiceImpl implements TemplateItemService {
    @Autowired
    private TemplateItemDao templateItemDao;
    @Autowired
    private TemplateItemRelationDao templateItemRelationDao;
    @Autowired
    private TemplateHistoryLogDao templateHistoryLogDao;
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private TemplateGroupService templateGroupService;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TemplateRuleService templateRuleService;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private VariableService variableService;
    @Autowired
    private TemplateHistoryService templateHistoryService;
    @Autowired
    private DictItemFacade dictItemFacade;
    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public Result queryItemByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        QueryTemplateQO templateQO = queryDTO.getCondition();
        //根据关联的条款组 过滤模板编码
        List<String> filterCodeListByTemplate = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateCode())) {
            List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateQO.getTemplateCode(), "");
            filterCodeListByTemplate = itemRelationEntityList.stream().map(TemplateItemRelationEntity::getTemplateItemCode).distinct().collect(Collectors.toList());
        }
        templateQO.setContent(this.aquireItemContentByName(templateQO.getContent()));

        IPage<TemplateItemEntity> templateItemEntityIPage = templateItemDao.queryByCondition(queryDTO, filterCodeListByTemplate);
        if (CollectionUtils.isNotEmpty(templateItemEntityIPage.getRecords())) {
            templateItemEntityIPage.getRecords().forEach(itemEntity -> {
                TemplateRuleEntity ruleEntity = templateRuleService.getRuleByTemplateCode(itemEntity.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
                if (null != ruleEntity) {
                    //加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
                    itemEntity.setConditionInfo(ruleEntity.getConditionInfo());
                }
                //关键变量信息处理
                this.assembleItemEntity(itemEntity);
                //条款内容-替换变量编码为变量名称
                this.aquireItemContentByCode(itemEntity);
            });
        } else {
            templateItemEntityIPage.setTotal(0);
        }
        return Result.page(templateItemEntityIPage);
    }

    @Override
    public Boolean saveTemplateItem(TemplateItemEntity itemEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(itemEntity);
        String itemCode = itemEntity.getCode();
        if (StringUtils.isNotBlank(itemCode)) {
            if (CollectionUtils.isNotEmpty(templateItemDao.getTemplateItemByCode(itemCode, null))) {
                throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_CODE_DUPLICATE);
            }
            itemEntity.setCode(itemCode.trim());
        }
        TemplateGroupEntity groupEntity = templateGroupDao.getGroupEntityByCode(itemEntity.getTemplateGroupCode());
        if (null == groupEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_NOT_EXIT);
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);

        Integer isFixed = DisableStatusEnum.DISABLE.getValue().equals(itemEntity.getIsFixed()) &&
                DisableStatusEnum.DISABLE.getValue().equals(groupEntity.getIsFixed()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();
        itemEntity.setVersion(DateTimeUtil.formatDateTimeValue())
                .setIsFixed(isFixed)
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setSort(null == itemEntity.getSort() ? 10000 : itemEntity.getSort())
                .setCanModify(null == itemEntity.getCanModify() ? DisableStatusEnum.ENABLE.getValue() : itemEntity.getCanModify())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        this.getKeyConditionInfo(itemEntity, itemEntity);
        String oldItemContent = StringUtils.isNotBlank(itemEntity.getContent()) ? itemEntity.getContent() : "";
        //条款内容-替换变量名为编码
        itemEntity.setContent(this.aquireItemContentByName(itemEntity.getContent()));
        templateItemDao.save(itemEntity);
        if (StringUtils.isBlank(itemCode)) {
            itemCode = this.getTemplateItemCode(itemEntity.getId());
            itemEntity.setCode(itemCode);
            templateItemDao.updateById(itemEntity);
        }
        //4、条款条件变量规则记录
        if (!CollectionUtils.isEmpty(itemEntity.getConditionVariableList())) {
            TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(itemCode, TemplateTypeEnum.ORIGIN_TEMPLATE.getValue(),
                    itemEntity.getConditionVariableList());
            itemEntity.setRuleInfo(templateRuleEntity.getRuleInfo()).setRuleCode(templateRuleEntity.getRuleCode());
            templateItemDao.updateById(itemEntity);
        }
        //5、记录条款新增日志
        this.recordItemHistoryLog(itemEntity, BasicOperateEnum.NEW, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(itemEntity), requestInfo);
        return true;
    }

    @Override
    public Boolean updateTemplateItem(TemplateItemEntity templateItemEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(templateItemEntity);
        TemplateItemEntity itemEntity = templateItemDao.getById(templateItemEntity.getId());
        String oldItemCode = itemEntity.getCode();
        String itemCode = templateItemEntity.getCode();
        if (StringUtils.isNotBlank(itemCode)) {
            List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCode(itemCode, null);
            itemEntityList = itemEntityList.stream().filter(it -> !it.getId().equals(itemEntity.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemEntityList)) {
                throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_CODE_DUPLICATE);
            }
        } else {
            itemCode = this.getTemplateItemCode(itemEntity.getId());
        }
        TemplateGroupEntity groupEntity = templateGroupDao.getGroupEntityByCode(templateItemEntity.getTemplateGroupCode());
        if (null == groupEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_NOT_EXIT);
        }
        Integer isFixed = DisableStatusEnum.DISABLE.getValue().equals(templateItemEntity.getIsFixed()) &&
                DisableStatusEnum.DISABLE.getValue().equals(groupEntity.getIsFixed()) ? DisableStatusEnum.DISABLE.getValue() : DisableStatusEnum.ENABLE.getValue();

        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);

        //4、条款组条件变量规则记录
        TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(itemCode, TemplateTypeEnum.ORIGIN_TEMPLATE.getValue(),
                templateItemEntity.getConditionVariableList());

        this.getKeyConditionInfo(templateItemEntity, itemEntity);
        itemEntity.setCode(itemCode.trim())
                .setName(templateItemEntity.getName())
                .setTemplateGroupCode(templateItemEntity.getTemplateGroupCode())
                .setContent(templateItemEntity.getContent())
                //关键变量
                .setTitle(templateItemEntity.getTitle())
                .setNeedNum(templateItemEntity.getNeedNum())
                .setSort(templateItemEntity.getSort())
                .setCanModify(templateItemEntity.getCanModify())
                .setMemo(templateItemEntity.getMemo())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setStatus(templateItemEntity.getStatus())
                .setEnterpriseCode(StringUtils.isNotBlank(templateItemEntity.getEnterpriseCode()) ? templateItemEntity.getEnterpriseCode() : "")
                .setEnterpriseName(StringUtils.isNotBlank(templateItemEntity.getEnterpriseCode()) && StringUtils.isNotBlank(templateItemEntity.getEnterpriseName()) ? templateItemEntity.getEnterpriseName() : "")
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now())
                .setIsFixed(isFixed)
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setRuleInfo(templateRuleEntity.getRuleInfo())
                .setRuleCode(templateRuleEntity.getRuleCode());
        String oldItemContent = StringUtils.isNotBlank(templateItemEntity.getContent()) ? templateItemEntity.getContent() : "";
        //条款内容-替换变量名为编码
        itemEntity.setContent(this.aquireItemContentByName(itemEntity.getContent()));
        templateItemDao.updateById(itemEntity);
//        if (!oldItemCode.equals(itemEntity.getCode())) {
//            //同步模板-条款组的编码
//            templateItemRelationDao.syncTemplateItemCode(oldItemCode, itemEntity.getCode());
//        }
        //5、记录条款组新增日志
        itemEntity.setConditionVariableList(templateItemEntity.getConditionVariableList());

        //6、记录条款更新操作
        this.recordItemHistoryLog(itemEntity, BasicOperateEnum.UPDATE, VersionType.TEMPORARY, oldItemContent, requestInfo);
        return true;
    }

    @Override
    public TemplateItemEntity getTemplateItemById(Integer templateItemId) {
        TemplateItemEntity templateItemEntity = templateItemDao.getById(templateItemId);
        if (null == templateItemEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_ITEM_NOT_EXIT);
        }
        //关键变量信息处理
        this.assembleItemEntity(templateItemEntity);
        TemplateRuleEntity templateRuleEntity = templateRuleService.getRuleDetailByTemplateCode(templateItemEntity.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
        if (null != templateRuleEntity) {
            templateItemEntity.setConditionInfo(templateRuleEntity.getConditionInfo())
                    .setConditionVariableList(templateRuleEntity.getConditionVariableList());
        }
        //获取条款组名称
        if (StringUtils.isNotBlank(templateItemEntity.getTemplateGroupCode())) {
            TemplateGroupEntity groupEntity = templateGroupService.getTemplateGroupByCode(templateItemEntity.getTemplateGroupCode());
            templateItemEntity.setTemplateGroupName(null == groupEntity ? "" : groupEntity.getName());
        }
        if (StringUtils.isNotBlank(templateItemEntity.getEnterpriseCode())) {
            DictItemEntity dictItemEntity = dictItemFacade.getDictItemByCode(DictItemType.ENTERPRISE_CODE.getValue(), templateItemEntity.getEnterpriseCode(), null);
            if (null != dictItemEntity) {
                templateItemEntity.setEnterpriseName(dictItemEntity.getItemName());
            }
        }
        //条款内容-替换变量编码为变量名称
        this.aquireItemContentByCode(templateItemEntity);
        return templateItemEntity;
    }

    @Override
    public List<TemplateItemEntity> getAllItemList(Integer status) {
        return templateItemDao.getAllItemList(status);
    }

    @Override
    public List<TemplateItemEntity> getItemListByGroupCode(String templateGroupCode) {
        return templateItemDao.getItemListByGroupCode(templateGroupCode);
    }

    @Override
    public Boolean markItemMainVersion(TemplateItemEntity itemEntity) {

        itemEntity.setMainVersionStatus(DisableStatusEnum.ENABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(this.getCurrentUserName());
        templateItemDao.updateById(itemEntity);
        this.recordItemHistoryLog(itemEntity, BasicOperateEnum.MARK_MAIN_VERSION, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(itemEntity), "");
        return true;
    }

    private String getCurrentUserName() {
        //2、更新模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        return employFacade.getEmployCache(userId);
    }

    @Override
    public Result exportItemExcel(QueryTemplateQO templateQO) {
        //1、处理条款查询参数，并查询条款
        List<String> filterCodeListByTemplate = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateCode())) {
            List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateQO.getTemplateCode(), "");
            filterCodeListByTemplate = itemRelationEntityList.stream().map(TemplateItemRelationEntity::getTemplateItemCode).distinct().collect(Collectors.toList());
        }
        templateQO.setContent(this.aquireItemContentByName(templateQO.getContent()));
        List<TemplateItemEntity> itemEntityList = templateItemDao.queryItemList(templateQO, filterCodeListByTemplate);

        List<DictItemEntity> dictItemEntityList = dictItemFacade.getItemByDictCode(DictItemType.ENTERPRISE_CODE.getValue());
        Map<String, String> enterpriseMap = dictItemEntityList.stream().collect(Collectors.toMap(DictItemEntity::getItemCode, DictItemEntity::getItemName));

        //2、处理条款的excel展示信息
        List<VariableEntity> variableBasicList = variableService.getAllVariableBasicList();
        Map<String, String> variableMap = variableBasicList.stream().collect(Collectors.toMap(VariableEntity::getCode, VariableEntity::getDisplayName, (k1, k2) -> k1));
        Map<String, String> variableInfoMap = new HashMap<>();
        for (Map.Entry<String, String> variableEntry : variableMap.entrySet()) {
            variableInfoMap.put("${" + variableEntry.getKey() + "!}", variableEntry.getValue());
        }
        if (CollectionUtils.isNotEmpty(itemEntityList)) {
            itemEntityList.forEach(templateItemEntity -> {
                //关键变量信息处理
                this.assembleItemEntity(templateItemEntity);
                TemplateRuleEntity templateRuleEntity = templateRuleService.getRuleByTemplateCode(templateItemEntity.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
                if (null != templateRuleEntity) {
                    templateItemEntity.setConditionInfo(templateRuleEntity.getConditionInfo());
                }
                //获取条款组名称
//                if (StringUtils.isNotBlank(templateItemEntity.getTemplateGroupCode())) {
//                    TemplateGroupEntity groupEntity = templateGroupService.getTemplateGroupByCode(templateItemEntity.getTemplateGroupCode());
//                    templateItemEntity.setTemplateGroupTitle(null == groupEntity ? "" : groupEntity.getTitle());
//                }
                if (StringUtils.isNotBlank(templateItemEntity.getEnterpriseCode())) {
                    templateItemEntity.setEnterpriseName(enterpriseMap.get(templateItemEntity.getEnterpriseCode()));
                }
                //条款内容-替换变量编码为变量名称
                String nameContent = "";
                if (StringUtils.isNotBlank(templateItemEntity.getContent())) {
                    nameContent = templateItemEntity.getContent();
                    List<String> keyList = TemplateUtil.renderKeyVariableList(nameContent);
                    nameContent = TemplateUtil.removeParentheses(nameContent);
                    for (String keyName : keyList) {
                        String variableDisplayName = "${" + variableInfoMap.get(keyName) + "!}";
                        nameContent = nameContent.replaceAll(Pattern.quote(keyName), "<span>" + Matcher.quoteReplacement(variableDisplayName) + "</span>");
//                        nameContent = nameContent.replaceAll(Pattern.quote(keyName), Matcher.quoteReplacement(variableDisplayName));
                    }
                    templateItemEntity.setContentInfo(nameContent);
                }
//                this.aquireItemContentByCode(templateItemEntity);
//                templateItemEntity.setContentInfo(TemplateUtil.removeParentheses(templateItemEntity.getContent()));
                List<TemplateItemRelationEntity> itemRelationList = templateItemRelationDao.getTemplateItemRelation("", templateItemEntity.getCode());
                if (CollectionUtils.isNotEmpty(itemRelationList)) {
                    List<String> bindTemplateCodeList = itemRelationList.stream().map(TemplateItemRelationEntity::getTemplateCode)
                            .distinct().collect(Collectors.toList());
                    templateItemEntity.setBindTemplateCodes(StringUtils.join(bindTemplateCodeList, ","));
                }
            });
        }
        //3、记录条款excel导出日志
//        TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
//                .setReferType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
//                .setOperationType(BasicOperateEnum.EXCEL_EXPORT.getValue())
//                .setOperationTypeInfo(BasicOperateEnum.EXCEL_EXPORT.getDesc())
//                .setContent(FastJsonUtils.getBeanToJson(itemEntityList))
//                .setRequestInfo(FastJsonUtils.getBeanToJson(templateQO));
//        templateHistoryService.saveTemplateHistory(historyEntity);

        return Result.success(itemEntityList);
    }

    @Override
    public Result exportItemJson(QueryTemplateQO templateQO) {
        //1、根据关联的条款组 过滤模板编码
        List<String> filterCodeListByTemplate = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateCode())) {
            List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateQO.getTemplateCode(), "");
            filterCodeListByTemplate = itemRelationEntityList.stream().map(TemplateItemRelationEntity::getTemplateItemCode).distinct().collect(Collectors.toList());
        }
        templateQO.setContent(this.aquireItemContentByName(templateQO.getContent()));
        List<TemplateItemEntity> itemEntityList = templateItemDao.queryItemList(templateQO, filterCodeListByTemplate);
        List<String> notMainVersionItemCodeList = itemEntityList.stream().filter(itemEntity -> {
            return DisableStatusEnum.DISABLE.getValue().equals(itemEntity.getMainVersionStatus());
        }).map(TemplateItemEntity::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notMainVersionItemCodeList)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_ITEM_EXIST_NOT_MAIN_VERSION, StringUtils.join(notMainVersionItemCodeList, ","));
        }
        //2、处理条款详细信息
        List<TemplateItemJsonDTO> itemJsonDTOList = itemEntityList.stream().map(this::getItemDetailJson).collect(Collectors.toList());

        String exportItemCodes = itemEntityList.stream().map(TemplateItemEntity::getCode).collect(Collectors.joining());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        //3、记录条款脚本导出日志
        for (TemplateItemJsonDTO itemJsonDTO : itemJsonDTOList) {
            Timestamp now = DateTimeUtil.now();
            TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
                    .setReferType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
                    .setReferCode(itemJsonDTO.getCode())
                    .setReferName(itemJsonDTO.getName())
                    .setMemo(exportItemCodes)
                    .setMainVersion(itemJsonDTO.getVersion())
                    .setVersion(itemJsonDTO.getVersion())
                    .setOperationType(BasicOperateEnum.JSON_EXPORT.getValue())
                    .setOperationTypeInfo(BasicOperateEnum.JSON_EXPORT.getDesc())
                    .setVersionType(VersionType.MAIN_VERSION.getValue())
                    .setContent(FastJsonUtils.getBeanToJson(itemJsonDTOList))
                    .setRequestInfo(FastJsonUtils.getBeanToJson(templateQO))
                    .setCreatedAt(now)
                    .setUpdatedAt(now)
                    .setCreatedBy(name)
                    .setUpdatedBy(name);
            templateHistoryService.saveTemplateHistory(historyEntity);
        }
        return Result.success(itemJsonDTOList);
    }

    private TemplateItemJsonDTO getItemDetailJson(TemplateItemEntity itemEntity) {
        TemplateItemJsonDTO itemJsonDTO = BeanConvertUtils.convert(TemplateItemJsonDTO.class, itemEntity);
        if (StringUtils.isNotBlank(itemJsonDTO.getRuleInfo())) {
            TemplateRuleEntity ruleEntity = templateRuleService.getRuleDetailEntityByCode(itemJsonDTO.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
            if (null != ruleEntity) {
                itemJsonDTO.setTemplateRule(ruleEntity);
            }
        }
        itemJsonDTO.setTemplateType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
        return itemJsonDTO;
    }


    @Override
    public Result importItemJson(MultipartFile file) {
        String jsonString = JsonFileUtil.readJson(file);

        //2、同步处理条款信息
        List<TemplateItemJsonDTO> itemJsonDTOList = FastJsonUtils.getJsonToList(jsonString, TemplateItemJsonDTO.class);
        if (CollectionUtils.isEmpty(itemJsonDTOList)) {
            return Result.failure("条款脚本数据为空！");
        }
        if (!TemplateTypeEnum.ORIGIN_TEMPLATE.getValue().equals(itemJsonDTOList.get(0).getTemplateType())) {
            return Result.failure("应导入条款脚本，脚本数据类型不匹配！");
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        //3、记录条款脚本导出日志
        for (TemplateItemJsonDTO itemJsonDTO : itemJsonDTOList) {
            Timestamp now = DateTimeUtil.now();
            TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
                    .setReferType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
                    .setReferCode(itemJsonDTO.getCode())
                    .setReferName(itemJsonDTO.getName())
                    .setOperationType(BasicOperateEnum.JSON_IMPORT.getValue())
                    .setOperationTypeInfo(BasicOperateEnum.JSON_IMPORT.getDesc())
                    .setVersionType(VersionType.MAIN_VERSION.getValue())
                    .setMainVersion(itemJsonDTO.getVersion())
                    .setVersion(itemJsonDTO.getVersion())
                    .setContent(jsonString)
                    .setCreatedAt(now)
                    .setUpdatedAt(now)
                    .setCreatedBy(name)
                    .setUpdatedBy(name);
            templateHistoryService.saveTemplateHistory(historyEntity);
        }
        return this.processImportItemList(itemJsonDTOList, "");
    }

    @Override
    public Result processImportItemList(List<TemplateItemJsonDTO> itemJsonDTOList, String mainersion) {
        itemJsonDTOList.sort(Comparator.comparing(TemplateItemJsonDTO::getId));
        for (TemplateItemJsonDTO newItemDTO : itemJsonDTOList) {
            String itemCode = newItemDTO.getCode();
            TemplateItemEntity itemEntity = templateItemDao.getItemEntityByCode(itemCode);
            BasicOperateEnum basicOperateEnum;
            TemplateItemEntity newItemEntity;
            if (null == itemEntity) {
                //条款-新增
                basicOperateEnum = BasicOperateEnum.NEW;
                newItemEntity = BeanConvertUtils.convert(TemplateItemEntity.class, newItemDTO);
                newItemEntity.setId(null);
                templateItemDao.save(newItemEntity);
            } else {
                //条款-更新
                basicOperateEnum = BasicOperateEnum.UPDATE;
                if (newItemDTO.getVersion().equals(itemEntity.getVersion())) {
                    continue;
                }
                newItemEntity = BeanConvertUtils.convert(TemplateItemEntity.class, newItemDTO);
                newItemEntity.setId(itemEntity.getId());
                templateItemDao.updateById(newItemEntity);
            }
            templateRuleService.syncImportTemplateRule(itemCode, TemplateTypeEnum.ORIGIN_TEMPLATE.getValue(),
                    newItemDTO.getTemplateRule());
            this.recordItemHistoryLog(newItemEntity.setMainVersion(mainersion), basicOperateEnum, VersionType.MAIN_VERSION, "", FastJsonUtils.getBeanToJson(newItemDTO));
        }
        return Result.success(itemJsonDTOList);
    }

    /**
     * 生成条款编码
     *
     * @param templateItemId 条款ID
     * @return 条款组编码
     */
    private String getTemplateItemCode(Integer templateItemId) {
        return "Clause_" + String.format("%04d", templateItemId);
    }

    /**
     * 关键变量多选-信息处理
     *
     * @param templateItemEntity
     */
    private void assembleItemEntity(TemplateItemEntity templateItemEntity) {
        //        CompanyEntity companyEntity = companyFacade.getCompanyByCode(templateItemEntity.getCompanyCode());
        if (StringUtils.isNotBlank(templateItemEntity.getCompanyCode())) {
            List<String> companyCodeList = Arrays.stream(templateItemEntity.getCompanyCode()
                    .substring(1, templateItemEntity.getCompanyCode().length() - 1)
                    .split(",")).collect(Collectors.toList());
            templateItemEntity.setCompanyCodeList(companyCodeList)
                    .setCompanyName(templateItemEntity.getCompanyCode().substring(1, templateItemEntity.getCompanyCode().length() - 1));
        }
//        if (StringUtils.isNotBlank(templateItemEntity.getCategoryId())) {
//            List<Integer> categoryIdList = Arrays.stream(templateItemEntity.getCategoryId()
//                    .substring(1, templateItemEntity.getCategoryId().length() - 1)
//                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
//            templateItemEntity.setCategoryIdList(categoryIdList)
//                    .setCategoryName(GoodsCategoryEnum.getCategoryInfo(categoryIdList));
//        }
        if (StringUtils.isNotBlank(templateItemEntity.getCategory1())) {
            List<Integer> category1List = Arrays.stream(templateItemEntity.getCategory1()
                    .substring(1, templateItemEntity.getCategory1().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateItemEntity.setCategory1List(category1List);
        }
        if (StringUtils.isNotBlank(templateItemEntity.getCategory2())) {
            List<Integer> category2List = Arrays.stream(templateItemEntity.getCategory2()
                    .substring(1, templateItemEntity.getCategory2().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateItemEntity.setCategory2List(category2List);
        }
        if (StringUtils.isNotBlank(templateItemEntity.getCategory3())) {
            List<Integer> category3List = Arrays.stream(templateItemEntity.getCategory3()
                    .substring(1, templateItemEntity.getCategory3().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateItemEntity.setCategory3List(category3List);
            List<CategoryEntity> category3EntityList = categoryFacade.getCategoryNameBySerialNoList(category3List);
            if (!org.springframework.util.CollectionUtils.isEmpty(category3EntityList)) {
                Map<Integer, List<Integer>> category3Map = category3EntityList.stream().collect(Collectors.groupingBy(CategoryEntity::getParentId, Collectors.mapping(CategoryEntity::getSerialNo, Collectors.toList())));
                templateItemEntity.setCategory3Map(category3Map);
            }
        }
        if (StringUtils.isNotBlank(templateItemEntity.getSalesType())) {
            List<Integer> salesTypeList = Arrays.stream(templateItemEntity.getSalesType()
                    .substring(1, templateItemEntity.getSalesType().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateItemEntity.setSalesTypeList(salesTypeList)
                    .setSalesTypeInfo(ContractSalesTypeEnum.getSalesTypeInfo(salesTypeList));
        }
        if (StringUtils.isNotBlank(templateItemEntity.getContractActionType())) {
            List<Integer> contractActionTypeList = Arrays.stream(templateItemEntity.getContractActionType()
                    .substring(1, templateItemEntity.getContractActionType().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateItemEntity.setContractActionTypeList(contractActionTypeList)
                    .setContractActionTypeInfo(ContractTradeTypeEnum.getTradeTypeInfo(contractActionTypeList));
        }
        if (StringUtils.isNotBlank(templateItemEntity.getBuCode())) {
            templateItemEntity.setBuCodeList(BuCodeEnum.getBuCodeList(templateItemEntity.getBuCode()))
                    .setBuInfo(BuCodeEnum.getDescByValueList(templateItemEntity.getBuCode()));
        }
        if (StringUtils.isNotBlank(templateItemEntity.getProtocolType())) {
            List<String> protocolTypeList = Arrays.stream(templateItemEntity.getProtocolType()
                    .substring(1, templateItemEntity.getProtocolType().length() - 1)
                    .split(",")).collect(Collectors.toList());
            templateItemEntity.setProtocolTypeList(protocolTypeList)
                    .setProtocolTypeInfo(ProtocolTypeEnum.getProtocolTypeInfo(protocolTypeList));
        }
        if (StringUtils.isNotBlank(templateItemEntity.getCustomerCode())) {
            List<String> customerCodeList = Arrays.stream(templateItemEntity.getCustomerCode()
                    .substring(1, templateItemEntity.getCustomerCode().length() - 1)
                    .split(",")).collect(Collectors.toList());
            templateItemEntity.setCustomerCodeList(customerCodeList)
                    .setCustomerName(StringUtils.isNotBlank(templateItemEntity.getCustomerName()) ? templateItemEntity.getCustomerName().substring(0, templateItemEntity.getCustomerName().length() - 1) : "ALL");
        }
        templateItemEntity.setCanModifyInfo(DisableStatusEnum.ENABLE.getValue().equals(templateItemEntity.getCanModify()) ? "是" : "否");
    }

    /**
     * 条款存储更新-替换变量名称为code
     *
     * @param content
     */
    private String aquireItemContentByName(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        List<String> keyList = TemplateUtil.renderKeyVariableList(content);
        if (CollectionUtils.isEmpty(keyList)) {
            return content;
        }
        for (String keyName : keyList) {
            VariableEntity variableEntity = variableService.getVariableByDisplayName(keyName.substring(2, keyName.length() - 2));
            if (null != variableEntity) {
                String variableCode = "${" + variableEntity.getCode() + "!}";
                content = content.replaceAll(Pattern.quote(keyName), Matcher.quoteReplacement(variableCode));
            }
        }
        return content;
    }

    /**
     * 详情展示变量名内容
     *
     * @param itemEntity
     */
    @Override
    public TemplateItemEntity aquireItemContentByCode(TemplateItemEntity itemEntity) {
        itemEntity.setContentInfo(itemEntity.getContent());
        return itemEntity.setContent(this.aquireDisplayContentByCode(itemEntity.getContent()));
    }

    @Override
    public String aquireDisplayContentByCode(String nameContent) {
        if (StringUtils.isBlank(nameContent)) {
            return "";
        }
        List<String> keyList = TemplateUtil.renderKeyVariableList(nameContent);
        if (CollectionUtils.isEmpty(keyList)) {
            return nameContent;
        }
        List<VariableEntity> variableBasicList = variableService.getAllVariableBasicList();
        Map<String, String> variableMap = variableBasicList.stream().collect(Collectors.toMap(VariableEntity::getCode, VariableEntity::getDisplayName, (k1, k2) -> k1));
        Map<String, String> variableInfoMap = new HashMap<>();
        for (Map.Entry<String, String> variableEntry : variableMap.entrySet()) {
            variableInfoMap.put("${" + variableEntry.getKey() + "!}", variableEntry.getValue());
        }
        for (String keyName : keyList) {
            String variableDisplayName = "${" + variableInfoMap.get(keyName) + "!}";
            log.info("条款文本替换：" + keyName + ",variableDisplayName:" + variableDisplayName);
            nameContent = nameContent.replaceAll(Pattern.quote(keyName), Matcher.quoteReplacement(variableDisplayName));
        }
        return nameContent;
    }


    /**
     * 拼接关键变量信息
     *
     * @param oldItemEntity
     * @param newItemEntity
     * @return
     */
    private TemplateItemEntity getKeyConditionInfo(TemplateItemEntity oldItemEntity, TemplateItemEntity newItemEntity) {
        newItemEntity.setCustomerCode(TemplateConstant.ALL_CODE_SYMBOL)
                .setBuCode(TemplateConstant.ALL_CODE_SYMBOL)
                .setProtocolType(TemplateConstant.ALL_CODE_SYMBOL)
                .setCustomerCode(TemplateConstant.ALL_CODE_SYMBOL)
                .setCustomerName(TemplateConstant.ALL_CODE_SYMBOL)
                .setCompanyCode(TemplateConstant.ALL_CODE_SYMBOL)
                .setCategoryId(TemplateConstant.ALL_ID_SYMBOL)
                .setSalesType(TemplateConstant.ALL_ID_SYMBOL)
                .setContractActionType(TemplateConstant.ALL_ID_SYMBOL);
        if (CollectionUtils.isNotEmpty(oldItemEntity.getBuCodeList())) {
            newItemEntity.setBuCode("," + StringUtils.join(oldItemEntity.getBuCodeList(), ",") + ",");
        }
        if (CollectionUtils.isNotEmpty(oldItemEntity.getProtocolTypeList())) {
            newItemEntity.setProtocolType("," + StringUtils.join(oldItemEntity.getProtocolTypeList(), ",") + ",");
        }

        if (CollectionUtils.isNotEmpty(oldItemEntity.getCompanyCodeList())) {
            newItemEntity.setCompanyCode("," + StringUtils.join(oldItemEntity.getCompanyCodeList(), ",") + ",");
        }
//        if (CollectionUtils.isNotEmpty(oldItemEntity.getCategoryIdList())) {
//            newItemEntity.setCategoryId(StringUtils.join(oldItemEntity.getCategoryIdList(), ",") + ",");
//        }
        if (CollectionUtils.isNotEmpty(oldItemEntity.getSalesTypeList())) {
            newItemEntity.setSalesType("," + StringUtils.join(oldItemEntity.getSalesTypeList(), ",") + ",");
        }
        if (CollectionUtils.isNotEmpty(oldItemEntity.getCategory2List())) {
            String category1 = oldItemEntity.getCategory1List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
            String category2 = oldItemEntity.getCategory2List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
            String category3 = oldItemEntity.getCategory3List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));

            newItemEntity.setCategory1("," + category1 + ",");
            newItemEntity.setCategory2("," + category2 + ",");
            newItemEntity.setCategory3("," + category3 + ",");

            newItemEntity.setCategory1Name(categoryFacade.assemblyCategoryNames(oldItemEntity.getCategory1List()));
            newItemEntity.setCategory2Name(categoryFacade.assemblyCategoryNames(oldItemEntity.getCategory2List()));
            newItemEntity.setCategory3Name(categoryFacade.assemblyCategoryNames(oldItemEntity.getCategory3List()));
        }
        if (CollectionUtils.isNotEmpty(oldItemEntity.getContractActionTypeList())) {
            newItemEntity.setContractActionType("," + StringUtils.join(oldItemEntity.getContractActionTypeList(), ",") + ",");
        }
        if (CollectionUtils.isNotEmpty(oldItemEntity.getCustomerCodeList())) {
            List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(oldItemEntity.getCustomerCodeList());
            String customerName = "";
            if (CollectionUtils.isNotEmpty(customerEntityList)) {
                Map<String, List<CustomerEntity>> customerMap = customerEntityList.stream().collect(Collectors.groupingBy(CustomerEntity::getLinkageCustomerCode));
                for (String customerCode : oldItemEntity.getCustomerCodeList()) {
                    if (CollectionUtils.isNotEmpty(customerMap.get(customerCode))) {
                        customerName = customerName + "," + customerMap.get(customerCode).get(0).getName();
                    }
                }
            }
            customerName = StringUtils.isNotBlank(customerName) ? customerName.substring(1, customerName.length()) + "," : TemplateConstant.ALL_CODE_SYMBOL;
            newItemEntity.setCustomerName(customerName)
                    .setCustomerCode("," + StringUtils.join(oldItemEntity.getCustomerCodeList(), ",") + ",");
        }
        return newItemEntity;
    }

    private List<String> getBuCodeList(String buCode) {
        if (StringUtils.isBlank(buCode)) {
            return new ArrayList<>();
        }
        return Arrays.stream(buCode
                .substring(1, buCode.length() - 1)
                .split(",")).collect(Collectors.toList());
    }
//    /**
//     * 记录模板新增/修改日志
//     *
//     * @param templateItemEntity 条款信息
//     * @param operationType      操作动作
//     */
//    private void recordItemHistoryLog(TemplateItemEntity templateItemEntity, String operationType) {
//        TemplateHistoryLogEntity templateHistoryLogEntity = BeanConvertUtils.convert(TemplateHistoryLogEntity.class, templateItemEntity);
//        templateHistoryLogEntity.setId(null)
//                .setReferId(templateItemEntity.getId())
//                .setReferCode(templateItemEntity.getCode())
//                .setReferType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
//                .setOperationType(operationType)
//                .setConditionVariableInfo(CollectionUtils.isEmpty(templateItemEntity.getConditionVariableList()) ? "" : FastJsonUtils.getBeanToJson(templateItemEntity.getConditionVariableList()))
//        ;
//
//        templateHistoryLogDao.save(templateHistoryLogEntity);
//    }

    /**
     * 记录条款新增/修改日志
     *
     * @param templateItemEntity 模板信息
     * @param basicOperateEnum   操作动作
     */
    private void recordItemHistoryLog(TemplateItemEntity templateItemEntity, BasicOperateEnum basicOperateEnum,
                                      VersionType versionType, String content, String requestInfo) {
        TemplateHistoryEntity templateHistoryEntity = new TemplateHistoryEntity();
        templateHistoryEntity.setId(null)
                .setReferId(templateItemEntity.getId())
                .setReferCode(templateItemEntity.getCode())
                .setReferName(templateItemEntity.getName())
                .setReferType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue())
                .setOperationType(basicOperateEnum.getValue())
                .setOperationTypeInfo(basicOperateEnum.getDesc())
                .setCategoryId(String.valueOf(templateItemEntity.getCategoryId()))
                .setSalesType(String.valueOf(templateItemEntity.getSalesType()))
                .setVersionType(versionType.getValue())
                .setVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateItemEntity.getMainVersion() : templateItemEntity.getVersion())
                .setMainVersionStatus(templateItemEntity.getMainVersionStatus())
                .setMainVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateItemEntity.getVersion() : templateItemEntity.getMainVersion())
//                .setMainVersionDesc(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateItemEntity.getMainVersionDesc() : "")
                .setStatus(templateItemEntity.getStatus())
                .setRequestInfo(requestInfo)
                .setContent(content)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(templateItemEntity.getUpdatedBy())
                .setCreatedBy(templateItemEntity.getCreatedBy());
        if (BasicOperateEnum.needRecordDetailInfoType().contains(basicOperateEnum.getValue())) {
            templateHistoryEntity.setContent(FastJsonUtils.getBeanToJson(this.getItemDetailJson(templateItemEntity)));
        }
        if (StringUtils.isNotBlank(templateHistoryEntity.getMainVersion())) {
            templateHistoryEntity.setMainVersion(templateItemEntity.getVersion());
        }
        templateHistoryService.saveTemplateHistory(templateHistoryEntity);
    }


}
