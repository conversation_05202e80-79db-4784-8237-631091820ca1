package com.navigator.husky.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.TemplateTypeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.dao.TemplateCheckDao;
import com.navigator.husky.dao.TemplateDao;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;
import com.navigator.husky.service.TemplateCheckService;
import com.navigator.husky.service.TemplateRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 14:13
 **/
@Service
@Slf4j
public class TemplateCheckServiceImpl implements TemplateCheckService {
    @Resource
    private TemplateCheckDao templateCheckDao;
    @Autowired
    private TemplateRuleService templateRuleService;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TemplateDao templateDao;

    @Override
    public Result queryCheckByCondition(QueryDTO<TemplateCheckEntity> queryDTO) {
        IPage<TemplateCheckEntity> checkEntityIPage = templateCheckDao.queryCheckByCondition(queryDTO);
        if (!CollectionUtils.isEmpty(checkEntityIPage.getRecords())) {
            checkEntityIPage.getRecords().forEach(checkEntity -> {
                checkEntity.setCategory1Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory1()))
                        .setCategory2Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory2()))
                        .setCategory3Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory3()))
                        .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(checkEntity.getSalesType()))
                        .setContractActionTypeInfo(ContractTradeTypeEnum.getDescByValue(checkEntity.getContractActionType()));
                TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(checkEntity.getTemplateCode());
                checkEntity.setTemplateId(null != templateEntity ? templateEntity.getId() : 0)
                        .setTemplateName(null != templateEntity ? templateEntity.getName() : "");
            });
        }
        return Result.page(checkEntityIPage);
    }

    @Override
    public Boolean saveTemplateCheck(TemplateCheckEntity checkEntity) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        if (StringUtils.isNotBlank(checkEntity.getTemplateCode())) {
            TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(checkEntity.getTemplateCode());
            checkEntity.setTemplateName(templateEntity.getName());
        }
        checkEntity
//                .setCategoryId(checkEntity.getCategory2())
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        templateCheckDao.save(checkEntity);
        //4、条款条件变量规则记录
        if (!CollectionUtils.isEmpty(checkEntity.getConditionVariableList())) {
            TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(checkEntity.getId().toString(), TemplateTypeEnum.TEMPLATE_CHECK_RULE.getValue(),
                    checkEntity.getConditionVariableList());
            checkEntity.setRuleInfo(templateRuleEntity.getRuleInfo())
                    .setRuleCode(templateRuleEntity.getRuleCode())
                    .setConditionInfo(templateRuleEntity.getConditionInfo());
            templateCheckDao.updateById(checkEntity);
        }
        return true;
    }

    @Override
    public Boolean updateTemplateCheck(TemplateCheckEntity checkEntity) {
        TemplateCheckEntity templateCheckEntity = templateCheckDao.getById(checkEntity.getId());
        if (null == templateCheckEntity) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }
        //4、条款组条件变量规则记录
        TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(checkEntity.getId().toString(), TemplateTypeEnum.TEMPLATE_CHECK_RULE.getValue(),
                checkEntity.getConditionVariableList());

        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);

        if (StringUtils.isNotBlank(checkEntity.getTemplateCode())) {
            TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(checkEntity.getTemplateCode());
            templateCheckEntity.setTemplateName(templateEntity.getName());
        }
        templateCheckEntity
//                .setCategoryId(checkEntity.getCategory2())
                .setCategory1(checkEntity.getCategory1())
                .setCategory2(checkEntity.getCategory2())
                .setCategory3(checkEntity.getCategory3())
                .setSalesType(checkEntity.getSalesType())
                .setContractActionType(checkEntity.getContractActionType())
                .setStatus(checkEntity.getStatus())
                .setTemplateCode(checkEntity.getTemplateCode())
                .setRuleInfo(templateRuleEntity.getRuleInfo())
                .setRuleCode(templateRuleEntity.getRuleCode())
                .setConditionInfo(templateRuleEntity.getConditionInfo())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        templateCheckDao.updateById(templateCheckEntity);
        return true;
    }

    @Override
    public List<TemplateCheckEntity> getCheckListByTemplateCode(String templateCode) {
        return templateCheckDao.getCheckListByTemplateCode(templateCode, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public TemplateCheckEntity getTemplateCheckById(Integer id) {
        TemplateCheckEntity checkEntity = templateCheckDao.getById(id);
        checkEntity.setCategory1Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory1()))
                .setCategory2Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory2()))
                .setCategory3Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory3()))
                .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(checkEntity.getSalesType()))
                .setContractActionTypeInfo(ContractTradeTypeEnum.getDescByValue(checkEntity.getContractActionType()));
        TemplateRuleEntity templateRuleEntity = templateRuleService.getRuleDetailByTemplateCode(checkEntity.getId().toString(), TemplateTypeEnum.TEMPLATE_CHECK_RULE.getValue());
        if (null != templateRuleEntity) {
            checkEntity.setConditionVariableList(templateRuleEntity.getConditionVariableList());
        }
        return checkEntity;
    }

    @Override
    public Boolean updateCheckStatus(Integer id, Integer status) {
        TemplateCheckEntity checkEntity = templateCheckDao.getById(id);
        if (null == checkEntity) {
            throw new BusinessException(ResultCodeEnum.SYSTEM_RULE_ERROR);
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        checkEntity.setStatus(status)
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(name);
        templateCheckDao.updateById(checkEntity);
        return true;
    }

    @Override
    public List<TemplateCheckEntity> queryExportTemplateCheckList(TemplateCheckEntity checkQueryDTO) {
//        List<TemplateCheckEntity> checkEntityList = templateCheckDao.getCheckListByTemplateCode("", null);
        List<TemplateCheckEntity> checkEntityList = templateCheckDao.queryCheckList(checkQueryDTO);
        if (!CollectionUtils.isEmpty(checkEntityList)) {
            checkEntityList.forEach(checkEntity -> {
                checkEntity.setCategory1Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory1()))
                        .setCategory2Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory2()))
                        .setCategory3Name(GoodsCategoryEnum.getDesc(checkEntity.getCategory3()))
                        .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(checkEntity.getSalesType()))
                        .setContractActionTypeInfo(ContractTradeTypeEnum.getDescByValue(checkEntity.getContractActionType()))
                        .setStatusInfo(DisableStatusEnum.getDescByValue(checkEntity.getStatus()))
                        .setUpdateTime(DateTimeUtil.formatDateTimeString(checkEntity.getUpdatedAt()));
                TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(checkEntity.getTemplateCode());
                checkEntity.setTemplateId(null != templateEntity ? templateEntity.getId() : 0)
                        .setTemplateName(null != templateEntity ? templateEntity.getName() : "");
            });
        }
        return checkEntityList;
    }
}
