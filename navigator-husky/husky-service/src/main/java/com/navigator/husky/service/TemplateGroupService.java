package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.TemplateGroupJsonDTO;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-04 18:23
 **/
public interface TemplateGroupService {
    Result queryGroupByCondition(QueryDTO<QueryTemplateQO> queryDTO);

    Boolean saveTemplateGroup(TemplateGroupEntity templateEntity);

    Boolean updateTemplateGroup(TemplateGroupEntity templateEntity);

    Boolean updateGroupStatus(Integer templateGroupId, Integer status);

    TemplateGroupEntity getTemplateGroupById(Integer templateGroupId);

    List<TemplateGroupEntity> getTemplateGroupByCodeList(List<String> templateGroupCodeList, Integer status);

    List<TemplateGroupEntity> getAllGroupList(Integer status);

    TemplateGroupEntity getTemplateGroupByCode(String groupCode);

    Boolean markGroupMainVersion(TemplateGroupEntity groupEntity);

    Result exportGroupExcel(QueryTemplateQO queryTemplateQO);

    Result exportGroupJson(QueryTemplateQO queryTemplateQO);

    Result importGroupJson(MultipartFile file);

    Result processImportGroupList(List<TemplateGroupJsonDTO> groupJsonDTOList, String mainVersion);

}
