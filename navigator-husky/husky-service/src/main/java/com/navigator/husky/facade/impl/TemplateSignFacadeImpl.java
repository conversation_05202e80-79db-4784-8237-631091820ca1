package com.navigator.husky.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateSignFacade;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.service.TemplateSignService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-14 15:23
 **/
@RestController
public class TemplateSignFacadeImpl implements TemplateSignFacade {
    @Resource
    private TemplateSignService templateSignService;

    @Override
    public Result matchTemplateByRule(Integer contractSignId) {
        return templateSignService.matchTemplateByRule(contractSignId);
    }

    @Override
    public Result checkTemplateByRule(Integer contractSignId) {
        return templateSignService.checkTemplateByRule(contractSignId);
    }

    @Override
    public Result matchTemplateByRuleV2(Integer contractSignId) {
        return templateSignService.matchTemplateByRuleV2(contractSignId);
    }

    @Override
    public String provideContractSign(TemplateEntity templateEntity) {
        return templateSignService.provideContractSign(templateEntity);
    }
}
