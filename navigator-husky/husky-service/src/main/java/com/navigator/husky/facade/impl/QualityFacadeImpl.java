package com.navigator.husky.facade.impl;

import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.vo.QualityExportVO;
import com.navigator.husky.service.QualityService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 13:56
 **/
@RestController
public class QualityFacadeImpl implements QualityFacade {
    @Resource
    private QualityService qualityService;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Value("${tt.quality.judgeExist}")
    private Boolean existQuality;


    @Override
    public Boolean judgeExistQuality(QualityInfoDTO qualityInfoDTO) {
        //数字合同开关未配置，或者数字合同开关关闭，TT的质量指标配置判断，不生效，直接返回true，不做校验
        if (existQuality) {
            return true;
        }
//        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.HUSKY_CONTRACT_PROVIDE.getRuleCode()));
//        if (null == systemRuleVO) {
//            return true;
//        }
//        if (!CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList())) {
//            if (DisableStatusEnum.DISABLE.getValue().equals(systemRuleVO.getSystemRuleItemVOList().get(0).getStatus())) {
//                return true;
//            }
//        }
        return null != qualityService.judgeExistQuality(qualityInfoDTO);
    }

    @Override
    public QualityEntity matchQuality(QualityInfoDTO qualityInfoDTO) {
        return qualityService.judgeExistQuality(qualityInfoDTO);
    }

    @Override
    public Result queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO) {
        return qualityService.queryQualityByCondition(queryDTO);
    }

    @Override
    public Result<Boolean> saveQuality(QualityEntity qualityEntity) {
        return Result.success(qualityService.saveQuality(qualityEntity));
    }

    @Override
    public Result<Boolean> updateQuality(QualityEntity qualityEntity) {
        return Result.success(qualityService.updateQuality(qualityEntity));
    }

    @Override
    public Boolean updateQualityStatus(Integer qualityId, Integer status) {
        return qualityService.updateQualityStatus(qualityId, status);
    }

    @Override
    public QualityEntity getQualityById(Integer qualityId) {
        return qualityService.getQualityById(qualityId);
    }

    @Override
    public List<QualityExportVO> exportQualityList(QualityInfoDTO qualityInfoDTO) {
        return qualityService.exportQualityList(qualityInfoDTO);
    }

    @Override
    public Result importQuality(MultipartFile uploadFile) {
        return qualityService.importQuality(uploadFile);
    }

    @Override
    public Result processHistoryData(Integer id) {
        qualityService.processHistoryData(id);
        return Result.success();
    }
}
