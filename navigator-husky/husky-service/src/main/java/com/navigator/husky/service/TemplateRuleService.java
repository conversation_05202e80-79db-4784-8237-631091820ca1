package com.navigator.husky.service;

import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 15:35
 **/
public interface TemplateRuleService {
    /**
     * @param referCode             模板编号
     * @param referType             模板类型 c
     * @param conditionVariableList 条款/条款组加载条件信息详情
     * @return 条件信息规则
     */
    TemplateRuleEntity recordTemplateRule(String referCode, Integer referType,
                                          List<ConditionVariableDTO> conditionVariableList);

    void syncImportTemplateRule(String referCode, Integer referType,TemplateRuleEntity templateRuleList);

    /**
     *
     * @param referCode
     * @param referType {@link com.navigator.common.enums.TemplateTypeEnum}
     * @return
     */
    TemplateRuleEntity getRuleByTemplateCode(String referCode, Integer referType);
    /**
     *
     * @param referCode
     * @param referType {@link com.navigator.common.enums.TemplateTypeEnum}
     * @return
     */
    TemplateRuleEntity getRuleDetailEntityByCode(String referCode, Integer referType);
    /**
     *
     * @param referCode
     * @param referType {@link com.navigator.common.enums.TemplateTypeEnum}
     * @return
     */
    TemplateRuleEntity getRuleDetailByTemplateCode(String referCode, Integer referType);
}
