package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateGroupFacade;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.service.TemplateGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-04 18:25
 **/
@RestController
public class TemplateGroupFacadeImpl implements TemplateGroupFacade {
    @Autowired
    private TemplateGroupService templateGroupService;

    @Override
    public Result queryGroupByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        return templateGroupService.queryGroupByCondition(queryDTO);
    }

    @Override
    public Boolean saveTemplateGroup(TemplateGroupEntity groupEntity) {
        return templateGroupService.saveTemplateGroup(groupEntity);
    }

    @Override
    public Boolean updateTemplateGroup(TemplateGroupEntity groupEntity) {
        return templateGroupService.updateTemplateGroup(groupEntity);
    }

    @Override
    public Boolean updateGroupStatus(Integer templateGroupId, Integer status) {
        return templateGroupService.updateGroupStatus(templateGroupId, status);
    }

    @Override
    public TemplateGroupEntity getTemplateGroupById(Integer templateGroupId) {
        return templateGroupService.getTemplateGroupById(templateGroupId);
    }

    @Override
    public Result getTemplateGroupByCodeList(List<String> templateGroupCodeList, Integer status) {
        return Result.success(templateGroupService.getTemplateGroupByCodeList(templateGroupCodeList, status));
    }

    @Override
    public List<TemplateGroupEntity> getAllGroupList(Integer status) {
        return templateGroupService.getAllGroupList(status);
    }

    @Override
    public Result exportGroupExcel(QueryTemplateQO queryTemplateQO) {
        return templateGroupService.exportGroupExcel(queryTemplateQO);
    }

    @Override
    public Result exportGroupJson(QueryTemplateQO queryTemplateQO) {
        return templateGroupService.exportGroupJson(queryTemplateQO);
    }

    @Override
    public Result importGroupJson(MultipartFile file) {
        return templateGroupService.importGroupJson(file);
    }
}
