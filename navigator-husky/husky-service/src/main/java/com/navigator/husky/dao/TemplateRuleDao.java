package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateRuleMapper;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateRuleDao extends BaseDaoImpl<TemplateRuleMapper, TemplateRuleEntity> {
    public TemplateRuleEntity getRuleByTemplateCode(String referCode, Integer referType) {
        List<TemplateRuleEntity> ruleEntityList = this.list(new LambdaQueryWrapper<TemplateRuleEntity>()
                .eq(TemplateRuleEntity::getReferCode, referCode)
                .eq(TemplateRuleEntity::getReferType, referType)
                .eq(TemplateRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));

        return CollectionUtils.isEmpty(ruleEntityList) ? new TemplateRuleEntity() : ruleEntityList.get(0);
    }

    public void dropTemplateRule(String referCode, Integer referType) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateRuleEntity::getReferCode, referCode)
                .eq(TemplateRuleEntity::getReferType, referType)
                .eq(TemplateRuleEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateRuleEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(TemplateRuleEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    public TemplateRuleEntity getByCode(String ruleCode) {
        List<TemplateRuleEntity> ruleEntityList = this.list(new LambdaQueryWrapper<TemplateRuleEntity>()
                .eq(TemplateRuleEntity::getRuleCode, ruleCode));
        return CollectionUtils.isEmpty(ruleEntityList) ? new TemplateRuleEntity() : ruleEntityList.get(0);
    }


}
