package com.navigator.husky.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.husky.dao.*;
import com.navigator.husky.pojo.dto.*;
import com.navigator.husky.pojo.entity.*;
import com.navigator.husky.pojo.enums.VersionType;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.pojo.vo.HealthCheckVO;
import com.navigator.husky.pojo.vo.TemplateGroupRelationVO;
import com.navigator.husky.pojo.vo.TemplateItemRelationVO;
import com.navigator.husky.service.*;
import com.navigator.trade.pojo.dto.contractsign.KeyVariableDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 17:23
 **/
@Service
@Slf4j
public class TemplateServiceImpl implements ITemplateService {
    @Autowired
    private TemplateDao templateDao;
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private TemplateGroupRelationDao templateGroupRelationDao;
    @Autowired
    private TemplateItemRelationDao templateItemRelationDao;
    @Autowired
    private TemplateItemDao templateItemDao;
    @Autowired
    private TemplateHistoryLogDao templateHistoryLogDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private TemplateRuleDao templateRuleDao;
    @Autowired
    private TemplateBindDao templateBindDao;
    @Autowired
    private TemplateHistoryService templateHistoryService;
    @Autowired
    private TemplateGroupService templateGroupService;
    @Autowired
    private TemplateItemService templateItemService;
    @Autowired
    private DictItemFacade dictItemFacade;
    @Autowired
    private TemplateRuleService templateRuleService;
    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public Result queryByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        QueryTemplateQO templateQO = queryDTO.getCondition();
        //根据关联的条款组 过滤模板编码
        List<String> filterCodeListByGroup = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateGroupCode())) {
            List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode(templateQO.getTemplateGroupCode(), "");
            filterCodeListByGroup = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());
        }
        //根据关联的条款 过滤模板编码
        List<String> filterCodeListByItem = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateItemCode())) {
//            List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCode(templateQO.getTemplateItemCode(), null);
//            List<String> groupCodeList = itemEntityList.stream().map(TemplateItemEntity::getTemplateGroupCode).collect(Collectors.toList());
//            List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByGroupCodeList(groupCodeList);
//            if (CollectionUtils.isEmpty(groupRelationEntityList)) {
//                return Result.success();
//            }
//            filterCodeListByItem = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());
            List<TemplateItemRelationEntity> templateItemRelationList = templateItemRelationDao.getTemplateItemRelation("", templateQO.getTemplateItemCode());
            filterCodeListByItem = templateItemRelationList.stream().map(TemplateItemRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());

        }
        IPage<TemplateEntity> templateEntityIPage = templateDao.pageByCondition(queryDTO, filterCodeListByGroup, filterCodeListByItem);
        if (!CollectionUtils.isEmpty(templateEntityIPage.getRecords())) {
            templateEntityIPage.getRecords().forEach(templateEntity -> {
                templateEntity
//                        .setCategoryName(GoodsCategoryEnum.getDesc(templateEntity.getCategoryId()))
                        .setBuInfo(BuCodeEnum.getDescByValueList(templateEntity.getBuCode()))
                        .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(templateEntity.getSalesType()))
                        .setProtocolTypeInfo(ProtocolTypeEnum.getDescByValue(templateEntity.getProtocolType()))
                        .setContractActionTypeInfo(ContractTradeTypeEnum.getDescByValue(templateEntity.getContractActionType()))
                        .setCompanyName(StringUtils.isNotBlank(templateEntity.getCompanyCode()) ? templateEntity.getCompanyCode().substring(1, templateEntity.getCompanyCode().length() - 1) : "");
            });
        } else {
            templateEntityIPage.setTotal(0);
        }
        return Result.page(templateEntityIPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveTemplate(TemplateEntity templateEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(templateEntity);
        templateEntity.setCompanyCodeList(templateEntity.getCompanyCodeList().stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList()));
        templateEntity.setBuCodeList(templateEntity.getBuCodeList().stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList()));
        //1、校验模板编码唯一
        String duplicateTemplateCode = this.judgeTemplateDuplicate(templateEntity);
        if (StringUtils.isNotBlank(duplicateTemplateCode)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_DUPLICATE, "重复模板编码" + duplicateTemplateCode);
        }
        this.assemblyTemplateCode(templateEntity, templateEntity);

        List<TemplateEntity> templateList = templateDao.getTemplateByCode(templateEntity.getCode(), null);
        if (!CollectionUtils.isEmpty(templateList)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_DUPLICATE, ",重复模板编码:" + templateList.get(0).getCode());
        }
        //2、存储模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateEntity.setCustomerCode(StringUtils.isNotBlank(templateEntity.getCustomerCode()) ? templateEntity.getCustomerCode() : "")
                .setBindTemplate(ContractTradeTypeEnum.needTemplateBind().contains(templateEntity.getContractActionType()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        if (StringUtils.isNotBlank(templateEntity.getCustomerCode())) {
            List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(Arrays.asList(templateEntity.getCustomerCode()));
            templateEntity.setCustomerName(CollectionUtils.isEmpty(customerEntityList) ? "" : customerEntityList.get(0).getName());
        }

        templateDao.save(templateEntity);

        //3、记录模板-条款组关系
        templateGroupRelationDao.bindTemplateGroupRelation(templateEntity.getCode(), templateEntity.getTemplateGroupList());

        //4、处理部分转月、全部反点价、部分反点价的模板自动匹配绑定
        this.processTemplateBindInfo(templateEntity);
        //5、记录新增模板的日志记录
        this.recordTemplateHistoryLog(templateEntity, BasicOperateEnum.NEW, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(templateEntity), requestInfo, "");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTemplate(TemplateEntity templateEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(templateEntity);
        templateEntity.setCompanyCodeList(templateEntity.getCompanyCodeList().stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList()));
        templateEntity.setBuCodeList(templateEntity.getBuCodeList().stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList()));
        //1、校验模板编码唯一
        String duplicateTemplateCode = this.judgeTemplateDuplicate(templateEntity);
        if (StringUtils.isNotBlank(duplicateTemplateCode)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_DUPLICATE, "重复模板编码" + duplicateTemplateCode);
        }
        //2、生成模板编号及名称
        TemplateEntity updateTemplateEntity = templateDao.getById(templateEntity.getId());
        String oldTemplateCode = updateTemplateEntity.getCode();
        this.assemblyTemplateCode(templateEntity, updateTemplateEntity);
        List<TemplateEntity> templateEntityList = templateDao.getTemplateByCode(updateTemplateEntity.getCode(), null)
                .stream().filter(it -> !it.getId().equals(templateEntity.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(templateEntityList)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_DUPLICATE, ",重复模板的编码:" + templateEntityList.get(0).getCode());
        }
        //2、更新模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        updateTemplateEntity
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setBindTemplate(ContractTradeTypeEnum.needTemplateBind().contains(templateEntity.getContractActionType()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now())
                .setCode(templateEntity.getCode())
                .setName(templateEntity.getName())
                // BUGFIX：Case-1003310-生产环境特油采购模板无法增加主体 Author: nana 2025-06-25 Star
//                .setBuCode(updateTemplateEntity.getBuCode())
//                .setCompanyCode(updateTemplateEntity.getCompanyCode())
                // BUGFIX：Case-1003310-生产环境特油采购模板无法增加主体 Author: nana 2025-06-25 End
                .setCategoryId(templateEntity.getCategoryId())
                .setSalesType(templateEntity.getSalesType())
                .setProtocolType(templateEntity.getProtocolType())
                .setContractActionType(templateEntity.getContractActionType())
//                .setCustomerCode(StringUtils.isNotBlank(templateEntity.getCustomerCode()) ? templateEntity.getCustomerCode() : "")
                .setEnterpriseCode(StringUtils.isNotBlank(templateEntity.getEnterpriseCode()) ? templateEntity.getEnterpriseCode() : "")
                .setEnterpriseName(StringUtils.isNotBlank(templateEntity.getEnterpriseName()) && StringUtils.isNotBlank(templateEntity.getEnterpriseCode()) ? templateEntity.getEnterpriseName() : "")
                .setLayout(templateEntity.getLayout())
                .setMemo(templateEntity.getMemo())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setStatus(templateEntity.getStatus())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
        ;

//        if (StringUtils.isNotBlank(updateTemplateEntity.getCustomerCode())) {
//            List<CustomerEntity> customerEntityList = customerFacade.getCustomerListByCode(Arrays.asList(updateTemplateEntity.getCustomerCode()));
//            updateTemplateEntity.setCustomerName(CollectionUtils.isEmpty(customerEntityList) ? "" : customerEntityList.get(0).getName());
//        }
        templateDao.updateById(updateTemplateEntity);

        //3、记录模板-条款组关系
        templateGroupRelationDao.dropTemplateGroupRelation(oldTemplateCode);
        templateGroupRelationDao.bindTemplateGroupRelation(templateEntity.getCode(), templateEntity.getTemplateGroupList());
//        if (!oldTemplateCode.equals(updateTemplateEntity.getCode())) {
//            //同步模板-条款组的编码
//            templateItemRelationDao.syncTemplateCode(oldTemplateCode, updateTemplateEntity.getCode());
//        }
        updateTemplateEntity.setTemplateGroupList(templateEntity.getTemplateGroupList());

        //4、处理部分转月、全部反点价、部分反点价的模板自动匹配绑定
        this.processTemplateBindInfo(updateTemplateEntity);
        //5、记录新增模板的日志记录
        this.recordTemplateHistoryLog(updateTemplateEntity, BasicOperateEnum.UPDATE, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(updateTemplateEntity), requestInfo, "");
        return true;
    }

    private String getCurrentUserName() {
        //2、更新模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        return employFacade.getEmployCache(userId);
    }

    @Override
    public Result copyTemplateInfo(CopyTemplateDTO copyTemplateDTO) {
        copyTemplateDTO.setCompanyCodeList(copyTemplateDTO.getCompanyCodeList().stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList()));
        //1、获取需要复制的模板信息
        Integer templateId = copyTemplateDTO.getTemplateId();
        TemplateEntity needCopyTemplate = this.getTemplateById(templateId);
        if (null == needCopyTemplate) {
            return Result.failure(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        String oldTemplateCode = needCopyTemplate.getCode();
        String newTemplateCode = "";
        //2、判断条件重复的模板编号
//        List<String> buCodeList = Arrays.stream(needCopyTemplate.getBuCode()
//                .substring(1, needCopyTemplate.getBuCode().length() - 1)
//                .split(",")).collect(Collectors.toList());
        needCopyTemplate.setId(null)
                .setName("")
                .setCompanyCodeList(copyTemplateDTO.getCompanyCodeList())
                .setBuCodeList(copyTemplateDTO.getBuCodeList())
                .setCategory1List(copyTemplateDTO.getCategory1List())
                .setCategory2List(copyTemplateDTO.getCategory2List())
                .setCategory3List(copyTemplateDTO.getCategory3List())
                .setEnterpriseCode(copyTemplateDTO.getEnterpriseCode());
        String duplicateTemplateCode = this.judgeTemplateDuplicate(needCopyTemplate);
        if (StringUtils.isNotBlank(duplicateTemplateCode)) {
            return Result.failure(ResultCodeEnum.TEMPLATE_COPY_DUPLICATE, "重复的模板编号：" + duplicateTemplateCode);
        }
        //3、生成新的模板编号
        this.assemblyTemplateCode(needCopyTemplate, needCopyTemplate);
        newTemplateCode = needCopyTemplate.getCode();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        //复制模板默认禁用状态
        needCopyTemplate.setId(null)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setMainVersion("")
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setStatus(DisableStatusEnum.DISABLE.getValue())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        templateDao.save(needCopyTemplate);
        //4、记录模板和条款组的关联关系
        templateGroupRelationDao.bindTemplateGroupRelation(newTemplateCode, needCopyTemplate.getTemplateGroupList());
        //5、复制模板与条款的绑定关系
        List<TemplateItemRelationEntity> itemRelationList = templateItemRelationDao.getTemplateItemRelation(oldTemplateCode, "");
        templateItemRelationDao.copyTemplateItemRelation(newTemplateCode, itemRelationList);
        //6、处理模板的绑定关系（部分转月、反点价）
        this.processTemplateBindInfo(needCopyTemplate);
        // 7、记录操作记录
        this.recordTemplateHistoryLog(needCopyTemplate, BasicOperateEnum.COPY_TEMPLATE, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(needCopyTemplate), FastJsonUtils.getBeanToJson(copyTemplateDTO), "");

        return Result.success(needCopyTemplate);
    }

    /**
     * 获取重复条件的模板编号
     *
     * @param needCopyTemplate
     * @return
     */
    private String judgeTemplateDuplicate(TemplateEntity needCopyTemplate) {
        KeyVariableDTO splitKeyVariableDTO = new KeyVariableDTO()
//                .setBuCode(needCopyTemplate.getBuCode())
//                .setCategoryId(needCopyTemplate.getCategoryId())
                .setSalesType(needCopyTemplate.getSalesType())
                .setProtocolType(needCopyTemplate.getProtocolType())
                .setContractActionType(needCopyTemplate.getContractActionType())
                .setEnterpriseCode(needCopyTemplate.getEnterpriseCode());
        List<TemplateEntity> judgeTemplateList = templateDao.judgeCopyTemplate(splitKeyVariableDTO);
        if (!CollectionUtils.isEmpty(judgeTemplateList)) {
            //如果复制的为通用的，只判断通用的
            if (StringUtils.isBlank(needCopyTemplate.getEnterpriseCode())) {
                judgeTemplateList = judgeTemplateList.stream()
                        .filter(judgeTemplate -> {
                            return StringUtils.isBlank(judgeTemplate.getEnterpriseCode());
                        })
                        .collect(Collectors.toList());
            }
            if (StringUtils.isNotBlank(needCopyTemplate.getEnterpriseCode())) {
                judgeTemplateList = judgeTemplateList.stream()
                        .filter(judgeTemplate -> {
                            return StringUtils.isNotBlank(judgeTemplate.getEnterpriseCode());
                        })
                        .collect(Collectors.toList());
            }
            for (TemplateEntity judgeTemplateEntity : judgeTemplateList) {
                //更新的话 不比对
                log.info("重复比对的模板信息================：id={},模板内容=[{}]", judgeTemplateEntity.getId(), FastJsonUtils.getBeanToJson(judgeTemplateEntity));
                if (null != needCopyTemplate.getId() && needCopyTemplate.getId().equals(judgeTemplateEntity.getId())) {
                    continue;
                }
                List<String> duplicateCompanyCodeList = new ArrayList<>();
                List<String> duplicateBuCodeList = new ArrayList<>();
                List<Integer> duplicateCategory2List = new ArrayList<>();
                List<Integer> duplicateCategory3List = new ArrayList<>();
                if (StringUtils.isNotBlank(judgeTemplateEntity.getCompanyCode())) {
                    List<String> companyCodeList = Arrays.stream(judgeTemplateEntity.getCompanyCode()
                            .substring(1, judgeTemplateEntity.getCompanyCode().length() - 1)
                            .split(",")).collect(Collectors.toList());
                    duplicateCompanyCodeList = CommonListUtil.getIntersection(companyCodeList, needCopyTemplate.getCompanyCodeList());
                }
                if (StringUtils.isNotBlank(judgeTemplateEntity.getBuCode())) {
                    List<String> buCodeList = Arrays.stream(judgeTemplateEntity.getBuCode()
                            .substring(1, judgeTemplateEntity.getBuCode().length() - 1)
                            .split(",")).collect(Collectors.toList());
                    duplicateBuCodeList = CommonListUtil.getIntersection(buCodeList, needCopyTemplate.getBuCodeList());
                }
                if (StringUtils.isNotBlank(judgeTemplateEntity.getCategory3())) {
                    List<Integer> category3List = Arrays.stream(judgeTemplateEntity.getCategory3()
                            .substring(1, judgeTemplateEntity.getCategory3().length() - 1)
                            .split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    duplicateCategory3List = CommonListUtil.getIntersection(category3List, needCopyTemplate.getCategory3List());

                }
                if (StringUtils.isNotBlank(judgeTemplateEntity.getCategory2())) {
                    List<Integer> category2List = Arrays.stream(judgeTemplateEntity.getCategory2()
                            .substring(1, judgeTemplateEntity.getCategory2().length() - 1)
                            .split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    duplicateCategory2List = CommonListUtil.getIntersection(category2List, needCopyTemplate.getCategory2List());
                }
                if (!CollectionUtils.isEmpty(duplicateCompanyCodeList) && !CollectionUtils.isEmpty(duplicateBuCodeList)
                        && !CollectionUtils.isEmpty(duplicateCategory3List) && !CollectionUtils.isEmpty(duplicateCategory2List)) {
                    log.info("比对后重复的模板编号为："+judgeTemplateEntity.getCode());
                    return judgeTemplateEntity.getCode();
                }
            }
        }
        return "";
    }

    @Override
    public Result updateStatus(Integer templateId, Integer status) {

        TemplateEntity templateEntity = this.templateDao.getById(templateId);
        if (null == templateEntity) {
            return Result.failure(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        if (DisableStatusEnum.ENABLE.getValue().equals(status)) {
            if (StringUtils.isNotBlank(templateEntity.getCompanyCode())) {
                List<String> companyCodeList = Arrays.stream(templateEntity.getCompanyCode()
                        .substring(1, templateEntity.getCompanyCode().length() - 1)
                        .split(",")).collect(Collectors.toList());
                templateEntity.setCompanyCodeList(companyCodeList);
            }
            if (StringUtils.isNotBlank(templateEntity.getBuCode())) {
                List<String> buCodeList = Arrays.stream(templateEntity.getBuCode()
                        .substring(1, templateEntity.getBuCode().length() - 1)
                        .split(",")).collect(Collectors.toList());
                templateEntity.setBuCodeList(buCodeList);
            }
            if (StringUtils.isNotBlank(templateEntity.getCategory3())) {
                List<Integer> category3List = Arrays.stream(templateEntity.getCategory3()
                                .substring(1, templateEntity.getCategory3().length() - 1)
                                .split(",")).map(Integer::valueOf)
                        .collect(Collectors.toList());
                templateEntity.setCategory3List(category3List);
            }
            String duplicateTemplateCode = this.judgeTemplateDuplicate(templateEntity);
            if (StringUtils.isNotBlank(duplicateTemplateCode)) {
                return Result.failure(ResultCodeEnum.TEMPLATE_COPY_DUPLICATE, "重复的模板编号：" + duplicateTemplateCode);
            }
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateEntity.setStatus(status)
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        templateDao.updateById(templateEntity);
        BasicOperateEnum basicOperateEnum = DisableStatusEnum.getOperateByStatus(status);
        //4、记录新增模板的日志记录
        this.recordTemplateHistoryLog(templateEntity, basicOperateEnum, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(templateEntity), DisableStatusEnum.getDescByValue(status), "");
        return Result.success("模板状态更新成功");
    }

    @Override
    public TemplateEntity getTemplateById(Integer templateId) {
        TemplateEntity templateEntity = templateDao.getById(templateId);
        if (null == templateEntity) {
            return null;
        }
//        CompanyEntity companyEntity = companyFacade.getCompanyByCode(templateEntity.getCompanyCode());
        templateEntity
                .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(templateEntity.getSalesType()))
                .setProtocolTypeInfo(ProtocolTypeEnum.getByValue(templateEntity.getProtocolType()).getDesc())
                .setContractActionTypeInfo(ContractTradeTypeEnum.getByValue(templateEntity.getContractActionType()).getDesc())
                .setLayoutInfo(DisableStatusEnum.ENABLE.getValue().toString().equals(templateEntity.getLayout()) ? "新增" : "修改")
                .setCustomerName(templateEntity.getCustomerName());
        if (StringUtils.isBlank(templateEntity.getLayout())) {
            templateEntity.setLayoutInfo("");
        }
        if (StringUtils.isNotBlank(templateEntity.getBuCode())) {
            templateEntity.setBuCodeList(BuCodeEnum.getBuCodeList(templateEntity.getBuCode()))
                    .setBuInfo(BuCodeEnum.getDescByValueList(templateEntity.getBuCode()));
        }
        if (StringUtils.isNotBlank(templateEntity.getCompanyCode())) {
            List<String> companyCodeList = Arrays.stream(templateEntity.getCompanyCode()
                    .substring(1, templateEntity.getCompanyCode().length() - 1)
                    .split(",")).collect(Collectors.toList());
            templateEntity.setCompanyCodeList(companyCodeList)
                    .setCompanyName(templateEntity.getCompanyCode().substring(1, templateEntity.getCompanyCode().length() - 1));
        }
        if (StringUtils.isNotBlank(templateEntity.getCategory1())) {
            List<Integer> category1List = Arrays.stream(templateEntity.getCategory1()
                    .substring(1, templateEntity.getCategory1().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateEntity.setCategory1List(category1List);
        }
        if (StringUtils.isNotBlank(templateEntity.getCategory2())) {
            List<Integer> category2List = Arrays.stream(templateEntity.getCategory2()
                    .substring(1, templateEntity.getCategory2().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateEntity.setCategory2List(category2List);
        }
        if (StringUtils.isNotBlank(templateEntity.getCategory3())) {
            List<Integer> category3List = Arrays.stream(templateEntity.getCategory3()
                    .substring(1, templateEntity.getCategory3().length() - 1)
                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
            templateEntity.setCategory3List(category3List);
            List<CategoryEntity> category3EntityList = categoryFacade.getCategoryNameBySerialNoList(category3List);
            if (!CollectionUtils.isEmpty(category3EntityList)) {
                Map<Integer, List<Integer>> category3Map = category3EntityList.stream().collect(Collectors.groupingBy(CategoryEntity::getParentId, Collectors.mapping(CategoryEntity::getSerialNo, Collectors.toList())));
                templateEntity.setCategory3Map(category3Map);
            }
        }
        if (StringUtils.isNotBlank(templateEntity.getEnterpriseCode())) {
            DictItemEntity dictItemEntity = dictItemFacade.getDictItemByCode(DictItemType.ENTERPRISE_CODE.getValue(), templateEntity.getEnterpriseCode(), null);
            if (null != dictItemEntity) {
                templateEntity.setEnterpriseName(dictItemEntity.getItemName());
            }
        }
        List<TemplateGroupRelationEntity> relationEntityList = templateGroupRelationDao.getTemplateByCode("", templateEntity.getCode());
        List<TemplateGroupRelationVO> relationVOList = BeanConvertUtils.convert2List(TemplateGroupRelationVO.class, relationEntityList);
        if (!CollectionUtils.isEmpty(relationEntityList)) {
            relationVOList.forEach(relationEntity -> {
                List<TemplateGroupEntity> templateGroupEntityList = templateGroupDao.getTemplateGroupByCode(relationEntity.getTemplateGroupCode(), null);
                relationEntity.setHasBindItem(false);
                if (!CollectionUtils.isEmpty(templateGroupEntityList)) {
                    TemplateGroupEntity templateGroupEntity = templateGroupEntityList.get(0);
                    relationEntity.setTemplateGroupName(templateGroupEntity.getName())
                            .setTemplateGroupId(templateGroupEntity.getId())
                            .setTemplateGroupStatus(templateGroupEntity.getStatus())
                            .setTitle(templateGroupEntity.getTitle())
                            .setRealGroupCode(templateGroupEntity.getRealGroupCode())
                            .setTemplateGroupMemo(templateGroupEntity.getMemo());
                    TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(templateGroupEntity.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
                    if (null != templateRuleEntity) {
                        relationEntity.setConditionInfo(templateRuleEntity.getConditionInfo());
                    }
                    //是否绑定条款
                    List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByGroupCode(templateGroupEntity.getRealGroupCode());
                    if (!CollectionUtils.isEmpty(itemEntityList)) {
                        List<String> itemCodeList = itemEntityList.stream().map(TemplateItemEntity::getCode).collect(Collectors.toList());
                        List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemByCode(itemCodeList, templateEntity.getCode());
                        itemCodeList = itemRelationEntityList.stream().map(TemplateItemRelationEntity::getTemplateItemCode).distinct().collect(Collectors.toList());
                        List<TemplateItemEntity> filterItemEntityList = templateItemDao.getTemplateItemByCodeList(itemCodeList, DisableStatusEnum.ENABLE.getValue());
                        relationEntity.setHasBindItem(!CollectionUtils.isEmpty(filterItemEntityList))
                                .setItemEntityList(filterItemEntityList);
                    }
                }
            });
        }
        return templateEntity.setTemplateGroupList(relationVOList);
    }

    @Override
    public List<TemplateEntity> getAllTemplateList(Integer status) {
        return templateDao.getAllTemplateList(status);
    }

    @Override
    public List<TemplateEntity> getTemplateListByGroupCode(String templateGroupCode) {
        List<TemplateGroupRelationEntity> relationEntityList = templateGroupRelationDao.getTemplateByCode(templateGroupCode, "");
        List<String> templateCodeList = relationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());
        return templateDao.getTemplateByCodeList(templateCodeList, DisableStatusEnum.ENABLE.getValue());
    }

    @Override
    public Boolean bindTemplateItemRelation(TemplateItemRelationDTO itemRelationDTO) {
        //bindType=1,模板绑定条款，否则条款绑定模板
        Boolean bindTemplate = DisableStatusEnum.ENABLE.getValue().equals(itemRelationDTO.getBindType());
        List<String> templateCodeList = bindTemplate ? Arrays.asList(itemRelationDTO.getBindCode()) : itemRelationDTO.getReferCodeList();
        List<String> addTemplateCodeList = templateCodeList;
        if (!bindTemplate) {
            List<TemplateItemRelationVO> templateItemRelationVOList = this.getTemplateItemRelation("", itemRelationDTO.getBindCode());
            List<String> oldTemplateCodeList = templateItemRelationVOList.stream().map(TemplateItemRelationVO::getTemplateCode).distinct().collect(Collectors.toList());
            List<String> deleteTemplateCodeList = CommonListUtil.getDifferences(oldTemplateCodeList, itemRelationDTO.getReferCodeList());
            addTemplateCodeList = CommonListUtil.getDifferences(itemRelationDTO.getReferCodeList(), oldTemplateCodeList);
            addTemplateCodeList.addAll(deleteTemplateCodeList);
        }
        //1、模板-条款绑定关系更新
        Boolean bindResult = templateItemRelationDao.bindTemplateItemRelation(itemRelationDTO);
        templateDao.updateTemplateMainVersionStatus(addTemplateCodeList);
        List<String> itemCodeList = bindTemplate ? itemRelationDTO.getReferCodeList() : Arrays.asList(itemRelationDTO.getBindCode());
        if (!CollectionUtils.isEmpty(addTemplateCodeList)) {
            addTemplateCodeList.forEach(templateCode -> {
                //2、更新模板正式版本状态
                TemplateEntity templateEntity = this.updateTemplateMainVersionStatus(templateCode);
                //3、记录新增模板的日志记录
                this.recordTemplateHistoryLog(templateEntity, BasicOperateEnum.RELATION_TEMPLATE_ITEM, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(templateEntity), "绑定模板/条款关联关系：模板:" + templateCode + "，条款:" + StringUtils.join(itemCodeList, ","), "");
            });
        }
        return bindResult;
    }

    @Override
    public Boolean removeTemplateItemRelation(String templateCode, String templateItemCode) {

        //1、更新模板-条款关联关系
        templateItemRelationDao.dropTemplateItemRelation(templateCode, templateItemCode);

        //2、更新模板正式版本状态
        TemplateEntity templateEntity = this.updateTemplateMainVersionStatus(templateCode);

        //3、记录新增模板的日志记录
        this.recordTemplateHistoryLog(templateEntity, BasicOperateEnum.RELATION_TEMPLATE_ITEM_REMOVE, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(templateEntity), "解除模板/条款关联关系：模板:" + templateCode + "，条款:" + templateItemCode, "");

        return true;
    }

    /**
     * 更新模板正式版本状态
     *
     * @param templateCode
     * @return
     */
    private TemplateEntity updateTemplateMainVersionStatus(String templateCode) {
        TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(templateCode);
        templateEntity.setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(this.getCurrentUserName());
        templateDao.updateById(templateEntity);
        return templateEntity;
    }

    @Override
    public List<TemplateItemRelationVO> getTemplateItemRelation(String templateCode, String templateItemCode) {
        List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateCode, templateItemCode);
        if (CollectionUtils.isEmpty(itemRelationEntityList)) {
            return new ArrayList<>();
        }
        List<TemplateItemRelationVO> itemRelationVOList = BeanConvertUtils.convert2List(TemplateItemRelationVO.class, itemRelationEntityList);
        if (StringUtils.isNotBlank(templateCode)) {
            List<TemplateGroupRelationEntity> groupRelationList = templateGroupRelationDao.getTemplateByCode("", templateCode);
            //变更的原条款组编码
            List<String> groupCodeList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(groupRelationList)) {
                groupCodeList = groupRelationList.stream().map(TemplateGroupRelationEntity::getTemplateGroupCode).distinct().collect(Collectors.toList());
            }
            final List<String> groupCodeInfoList = groupCodeList;
            itemRelationVOList.forEach(itemRelationEntity -> {
                        List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCode(itemRelationEntity.getTemplateItemCode(), null);

                        if (!CollectionUtils.isEmpty(itemEntityList)) {
                            TemplateItemEntity itemEntity = itemEntityList.get(0);
                            TemplateGroupEntity groupEntity = templateGroupDao.getTemplateGroupByCode(groupCodeInfoList, itemEntity.getTemplateGroupCode());
                            String realGroupCode = null != groupEntity ? groupEntity.getRealGroupCode() : itemEntity.getTemplateGroupCode();
                            String templateGroupCode = null != groupEntity ? groupEntity.getCode() : itemEntity.getTemplateGroupCode();
                            itemRelationEntity
                                    .setCategory1(itemEntity.getCategory1())
                                    .setCategory2(itemEntity.getCategory2())
                                    .setCategory3(itemEntity.getCategory3())
                                    .setCategory1Name(itemEntity.getCategory1Name())
                                    .setCategory2Name(itemEntity.getCategory2Name())
                                    .setCategory3Name(itemEntity.getCategory3Name())
                                    .setTemplateGroupCode(templateGroupCode)
                                    .setRealGroupCode(realGroupCode)
                                    .setHasBindGroup(null != groupEntity);
                            List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode(templateGroupCode, templateCode);
                            itemRelationEntity.setTemplateGroupSort(!CollectionUtils.isEmpty(groupRelationEntityList) ? groupRelationEntityList.get(0).getSort() : 0)
                                    .setSort(itemEntity.getSort());
                            if (StringUtils.isNotBlank(itemEntity.getSalesType())) {
                                List<Integer> salesTypeList = Arrays.stream(itemEntity.getSalesType()
                                        .substring(1, itemEntity.getSalesType().length() - 1)
                                        .split(",")).map(Integer::valueOf).collect(Collectors.toList());
                                itemRelationEntity.setSalesTypeInfo(ContractSalesTypeEnum.getSalesTypeInfo(salesTypeList));
                            }
                            if (StringUtils.isNotBlank(itemEntity.getCustomerCode())) {
                                itemRelationEntity.setCustomerName(StringUtils.isNotBlank(itemEntity.getCustomerName()) ? itemEntity.getCustomerName().substring(0, itemEntity.getCustomerName().length() - 1) : "ALL");
                            }
                            itemRelationEntity.setTemplateItemName(itemEntity.getName())
                                    .setStatus(itemEntity.getStatus());
                            if (StringUtils.isNotBlank(itemEntity.getRuleInfo())) {
                                TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(itemEntity.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
                                if (null != templateRuleEntity) {
                                    itemRelationEntity.setConditionInfo(templateRuleEntity.getConditionInfo());
                                }
                            }
                        } else {
                            itemRelationVOList.remove(itemRelationEntity);
                        }
                    }
            );

            log.info("================条款关联关系数据" + FastJsonUtils.getBeanToJson(itemRelationVOList));
            itemRelationVOList.sort(Comparator.comparing(TemplateItemRelationVO::getTemplateGroupSort).thenComparing(TemplateItemRelationVO::getTemplateGroupCode).thenComparing(TemplateItemRelationVO::getSort));
        }

        if (StringUtils.isNotBlank(templateItemCode)) {
            itemRelationVOList.forEach(itemRelationEntity -> {
                        List<TemplateEntity> templateEntityList = templateDao.getTemplateByCode(itemRelationEntity.getTemplateCode(), null);
                        if (!CollectionUtils.isEmpty(templateEntityList)) {
                            TemplateEntity templateEntity = templateEntityList.get(0);
//                            CompanyEntity companyEntity = companyFacade.getCompanyByCode(templateEntity.getCompanyCode());
                            itemRelationEntity.setBuInfo(BuCodeEnum.getDescByValueList(templateEntity.getBuCode()))
                                    .setCompanyName(StringUtils.isNotBlank(templateEntity.getCompanyCode()) ? templateEntity.getCompanyCode().substring(1, templateEntity.getCompanyCode().length() - 1) : "")
                                    .setCompanyCode(templateEntity.getCompanyCode())
                                    .setCategory1(templateEntity.getCategory1())
                                    .setCategory2(templateEntity.getCategory2())
                                    .setCategory3(templateEntity.getCategory3())
                                    .setCategory1Name(templateEntity.getCategory1Name())
                                    .setCategory2Name(templateEntity.getCategory2Name())
                                    .setCategory3Name(templateEntity.getCategory3Name())
//                                    .setCategoryId(templateEntity.getCategoryId())
//                                    .setCategoryName(GoodsCategoryEnum.getDesc(templateEntity.getCategoryId()))
                                    .setSalesType(templateEntity.getSalesType())
                                    .setSalesTypeInfo(ContractSalesTypeEnum.getDescByValue(templateEntity.getSalesType()))
                                    .setProtocolTypeInfo(ProtocolTypeEnum.getByValue(templateEntity.getProtocolType()).getDesc())
                                    .setContractActionType(templateEntity.getContractActionType())
                                    .setContractActionTypeInfo(ContractTradeTypeEnum.getByValue(templateEntity.getContractActionType()).getDesc())
                                    .setCustomerName(templateEntity.getCustomerName())
                                    .setStatus(templateEntity.getStatus())
                                    .setTemplateName(templateEntity.getName());
                        }
                    }
            );
//            itemRelationVOList.sort(Comparator.comparing(TemplateItemRelationVO::getContractActionType).thenComparing(TemplateItemRelationVO::getCategoryId).thenComparing(TemplateItemRelationVO::getSalesType).reversed());
            itemRelationVOList.sort(Comparator.comparing(TemplateItemRelationVO::getContractActionType).thenComparing(TemplateItemRelationVO::getCategory1).thenComparing(TemplateItemRelationVO::getSalesType).reversed());

        }
        return itemRelationVOList;
    }

    @Override
    public Result exportTemplateJson(QueryTemplateQO templateQO) {
        //根据关联的条款组 过滤模板编码
        List<String> filterCodeListByGroup = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateGroupCode())) {
            List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode(templateQO.getTemplateGroupCode(), "");
            filterCodeListByGroup = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());
        }
        //根据关联的条款 过滤模板编码
        List<String> filterCodeListByItem = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateItemCode())) {
            List<TemplateItemRelationEntity> templateItemRelationList = templateItemRelationDao.getTemplateItemRelation("", templateQO.getTemplateItemCode());
            filterCodeListByItem = templateItemRelationList.stream().map(TemplateItemRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());

        }
        List<TemplateEntity> templateEntityList = templateDao.queryTemplateListByCondition(templateQO, filterCodeListByGroup, filterCodeListByItem);
        List<TemplateInfoJsonDTO> templateJsonList = templateEntityList.stream().map(this::processTemplateDetailJson).collect(Collectors.toList());
        return Result.success(templateJsonList);
    }

    /**
     * 导出模板Json脚本
     *
     * @param templateEntity
     * @return
     */
    private TemplateInfoJsonDTO processTemplateDetailJson(TemplateEntity templateEntity) {
        TemplateInfoJsonDTO templateJsonDTO = this.getTemplateDetailJson(templateEntity);
        this.recordTemplateHistoryLog(templateEntity, BasicOperateEnum.JSON_EXPORT, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(templateJsonDTO), "", "");
        return templateJsonDTO;
    }


    private TemplateInfoJsonDTO getTemplateDetailJson(TemplateEntity templateEntity) {
        if (DisableStatusEnum.DISABLE.getValue().equals(templateEntity.getMainVersionStatus())) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_NOT_MAIN_VERSION, templateEntity.getCode());
        }
        TemplateInfoJsonDTO templateJsonDTO = BeanConvertUtils.convert(TemplateInfoJsonDTO.class, templateEntity);
        templateJsonDTO.setTemplateType(TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue());
        String templateCode = templateJsonDTO.getCode();
        //所绑定的模板（转月、反点价）
        if (DisableStatusEnum.ENABLE.getValue().equals(templateJsonDTO.getBindTemplate())) {
            List<TemplateBindEntity> bindEntityList = templateBindDao.getTemplateBindByCode(templateCode, "");
            templateJsonDTO.setTemplateBindEntityList(bindEntityList);
        }

        //模板-条款组关联关系
        List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode("", templateCode);
        templateJsonDTO.setGroupRelationList(groupRelationEntityList);

        if (!CollectionUtils.isEmpty(groupRelationEntityList)) {
            //模板所绑定的条款组信息
            List<String> groupCodeList = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateGroupCode).distinct().collect(Collectors.toList());
            List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(groupCodeList, null);
            List<String> notMainVersionGroupCodeList = groupEntityList.stream()
                    .filter(groupEntity -> DisableStatusEnum.DISABLE.getValue().equals(groupEntity.getMainVersionStatus()))
                    .map(TemplateGroupEntity::getCode)
                    .distinct()
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notMainVersionGroupCodeList)) {
                throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_EXIST_NOT_MAIN_VERSION, StringUtils.join(notMainVersionGroupCodeList, ","));
            }
            List<TemplateGroupJsonDTO> groupJsonList = BeanConvertUtils.convert2List(TemplateGroupJsonDTO.class, groupEntityList);
            for (TemplateGroupJsonDTO groupJsonDTO : groupJsonList) {
                if (StringUtils.isNotBlank(groupJsonDTO.getRuleInfo())) {
                    TemplateRuleEntity ruleEntity = templateRuleService.getRuleDetailEntityByCode(groupJsonDTO.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
                    if (null != ruleEntity) {
                        groupJsonDTO.setTemplateRule(ruleEntity);
                    }
                }
                groupJsonDTO.setTemplateType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
            }
            templateJsonDTO.setGroupList(groupJsonList);
        }

        //模板-条款关联关系
        List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateCode, "");
        templateJsonDTO.setItemRelationList(itemRelationEntityList);

        if (!CollectionUtils.isEmpty(itemRelationEntityList)) {
            List<String> itemCodeList = itemRelationEntityList.stream()
                    .map(TemplateItemRelationEntity::getTemplateItemCode)
                    .distinct().collect(Collectors.toList());

            List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCodeList(itemCodeList, null);
            List<String> notMainVersionItemCodeList = itemEntityList.stream()
                    .filter(itemEntity -> {
                        return DisableStatusEnum.DISABLE.getValue().equals(itemEntity.getMainVersionStatus());
                    }).map(TemplateItemEntity::getCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notMainVersionItemCodeList)) {
                throw new BusinessException(ResultCodeEnum.TEMPLATE_ITEM_EXIST_NOT_MAIN_VERSION, StringUtils.join(notMainVersionItemCodeList, ","));
            }
            List<TemplateItemJsonDTO> itemJsonList = BeanConvertUtils.convert2List(TemplateItemJsonDTO.class, itemEntityList);
            for (TemplateItemJsonDTO itemJsonDTO : itemJsonList) {
                if (StringUtils.isNotBlank(itemJsonDTO.getRuleInfo())) {
                    TemplateRuleEntity ruleEntity = templateRuleService.getRuleDetailEntityByCode(itemJsonDTO.getCode(), TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
                    if (null != ruleEntity) {
                        itemJsonDTO.setTemplateRule(ruleEntity);
                    }
                }
                itemJsonDTO.setTemplateType(TemplateTypeEnum.ORIGIN_TEMPLATE.getValue());
            }
            templateJsonDTO.setItemList(itemJsonList);
        }
        return templateJsonDTO;
    }


    private TemplateInfoJsonDTO getTemplateDetailInfoLog(TemplateEntity templateEntity) {
        TemplateInfoJsonDTO templateJsonDTO = BeanConvertUtils.convert(TemplateInfoJsonDTO.class, templateEntity);
        String templateCode = templateJsonDTO.getCode();
        //所绑定的模板（转月、反点价）
        if (DisableStatusEnum.ENABLE.getValue().equals(templateJsonDTO.getBindTemplate())) {
            List<TemplateBindEntity> bindEntityList = templateBindDao.getTemplateBindByCode(templateCode, "");
            templateJsonDTO.setTemplateBindEntityList(bindEntityList);
        }

        //模板-条款组关联关系
        List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode("", templateCode);
        templateJsonDTO.setGroupRelationList(groupRelationEntityList);

        if (!CollectionUtils.isEmpty(groupRelationEntityList)) {
            //模板所绑定的条款组信息
            List<String> groupCodeList = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateGroupCode).distinct().collect(Collectors.toList());
            List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(groupCodeList, null);
            List<TemplateGroupJsonDTO> groupJsonList = BeanConvertUtils.convert2List(TemplateGroupJsonDTO.class, groupEntityList);
            templateJsonDTO.setGroupList(groupJsonList);
        }

        //模板-条款关联关系
        List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getTemplateItemRelation(templateCode, "");
        templateJsonDTO.setItemRelationList(itemRelationEntityList);

        if (!CollectionUtils.isEmpty(itemRelationEntityList)) {
            List<String> itemCodeList = itemRelationEntityList.stream()
                    .map(TemplateItemRelationEntity::getTemplateItemCode)
                    .distinct().collect(Collectors.toList());

            List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCodeList(itemCodeList, null);
            List<TemplateItemJsonDTO> itemJsonList = BeanConvertUtils.convert2List(TemplateItemJsonDTO.class, itemEntityList);
            templateJsonDTO.setItemList(itemJsonList);
        }
        return templateJsonDTO;
    }

    @Override
    public Result importTemplateJson(MultipartFile file) {
        //1、解析模板脚本内容（Json格式）
        String jsonString = JsonFileUtil.readJson(file);
        List<TemplateInfoJsonDTO> templateJsonList = FastJsonUtils.getJsonToList(jsonString, TemplateInfoJsonDTO.class);
        if (CollectionUtils.isEmpty(templateJsonList)) {
            return Result.failure("导入数据为空");
        }
        if (!TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue().equals(templateJsonList.get(0).getTemplateType())) {
            return Result.failure("应导入模板脚本，脚本数据类型不匹配！");
        }
        templateJsonList.sort(Comparator.comparing(TemplateInfoJsonDTO::getId));
        for (TemplateInfoJsonDTO newTemplateDTO : templateJsonList) {
            String templateCode = newTemplateDTO.getCode();
            TemplateEntity templateEntity = templateDao.getTemplateEntityByCode(templateCode);
            TemplateEntity newTemplateEntity = BeanConvertUtils.convert(TemplateEntity.class, newTemplateDTO);
            //2、同步模板信息
            if (null == templateEntity) {
                //模板-新增
                newTemplateEntity.setId(null);
                templateDao.save(newTemplateEntity);
            } else {
                //模板-更新
                if (newTemplateDTO.getVersion().equals(templateEntity.getVersion()) &&
                        newTemplateDTO.getMainVersion().equals(templateEntity.getMainVersion())) {
                    this.recordTemplateHistoryLog(newTemplateEntity, BasicOperateEnum.JSON_IMPORT, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(newTemplateDTO), FastJsonUtils.getBeanToJson(newTemplateDTO), "");
                    continue;
                }
                newTemplateEntity.setId(templateEntity.getId());
                templateDao.updateById(newTemplateEntity);
            }

            //3、反点价、转月-套模板关联-同步
            templateBindDao.syncImportBindRelation(templateCode, newTemplateDTO.getTemplateBindEntityList());
            //4、同步条款组基本信息
            templateGroupService.processImportGroupList(newTemplateDTO.getGroupList(), newTemplateEntity.getMainVersion());
            //5、同步模板-条款组关联关系
            templateGroupRelationDao.syncImportTemplateGroupRelation(templateCode, newTemplateDTO.getGroupRelationList());
            //6、同步条款基本信息
            templateItemService.processImportItemList(newTemplateDTO.getItemList(), newTemplateEntity.getMainVersion());
            //7、同步模板-条款关联关系
            templateItemRelationDao.syncImportTemplateItemRelation(templateCode, newTemplateDTO.getItemRelationList());

            this.recordTemplateHistoryLog(newTemplateEntity, BasicOperateEnum.JSON_IMPORT, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(newTemplateDTO), FastJsonUtils.getBeanToJson(newTemplateDTO), "");
        }
        return Result.success(templateJsonList);
    }

    @Override
    public Result syncTemplateRelation(List<TemplateRelationSyncDTO> templateRelationSyncList) {
        return null;
    }

    @Override
    public Result markMainVersion(MarkMainVersionDTO markMainVersionDTO) {
        Integer templateId = markMainVersionDTO.getTemplateId();
        TemplateEntity templateEntity = this.getTemplateById(templateId);
        if (null == templateEntity) {
            return Result.failure(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        if (DisableStatusEnum.DISABLE.getValue().equals(templateEntity.getStatus())) {
            return Result.failure(ResultCodeEnum.TEMPLATE_MARK_MAIN_VERSION, "不能保存正式版本！");
        }
        String mainVersion = generateMainVersion(templateEntity.getMainVersion());
        //2、存储模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        List<String> updateItemCodeList = new ArrayList<>();
        List<String> updateGroupCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(templateEntity.getTemplateGroupList())) {
            for (TemplateGroupRelationVO groupRelationEntity : templateEntity.getTemplateGroupList()) {
//                if (!CollectionUtils.isEmpty(groupRelationEntity.getItemEntityList())) {
//                    for (TemplateItemEntity itemEntity : groupRelationEntity.getItemEntityList()) {
//                        if (DisableStatusEnum.ENABLE.getValue().equals(itemEntity.getStatus()) &&
//                                !DisableStatusEnum.ENABLE.getValue().equals(itemEntity.getMainVersionStatus())) {
//                            templateItemService.markItemMainVersion(itemEntity.setMainVersion(mainVersion));
//                            updateItemCodeList.add(itemEntity.getCode());
//                        }
//                    }
//                }
                TemplateGroupEntity groupEntity = templateGroupDao.getById(groupRelationEntity.getTemplateGroupId());
                if (null != groupEntity) {
                    if (!DisableStatusEnum.ENABLE.getValue().equals(groupEntity.getMainVersionStatus())) {
                        templateGroupService.markGroupMainVersion(groupEntity.setMainVersion(mainVersion));
                        updateGroupCodeList.add(groupEntity.getCode());
                    }
                }
            }
        }
        List<TemplateItemRelationVO> templateItemRelationVOList = this.getTemplateItemRelation(templateEntity.getCode(), "");
        if (!CollectionUtils.isEmpty(templateItemRelationVOList)) {
            for (TemplateItemRelationVO itemRelationVO : templateItemRelationVOList) {
                TemplateItemEntity itemEntity = templateItemDao.getItemEntityByCode(itemRelationVO.getTemplateItemCode());
                if (!DisableStatusEnum.ENABLE.getValue().equals(itemEntity.getMainVersionStatus())) {
                    templateItemService.markItemMainVersion(itemEntity.setMainVersion(mainVersion));
                    updateItemCodeList.add(itemEntity.getCode());
                }
            }
        }
        templateEntity.setMainVersion(mainVersion)
                .setMainVersionStatus(DisableStatusEnum.ENABLE.getValue())
                .setMainVersionDesc(markMainVersionDTO.getMainVersionDesc())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(name);
        templateDao.updateById(templateEntity);

        this.recordTemplateHistoryLog(templateEntity, BasicOperateEnum.MARK_MAIN_VERSION, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(templateEntity), FastJsonUtils.
                getBeanToJson(markMainVersionDTO), "更新的条款组：" + StringUtils.join(updateGroupCodeList, ",") + "；更新的条款：" + StringUtils.join(updateItemCodeList, ","));
        return Result.success("模板标记为正式版本处理成功~");
    }

    @Override
    public HealthCheckVO healthCheck(Integer templateId) {
        TemplateEntity templateEntity = this.getTemplateById(templateId);
        List<TemplateGroupRelationVO> templateGroupList = templateEntity.getTemplateGroupList();

        List<String> groupCodeList = templateGroupList.stream()
                .filter(templateGroup -> {
                    return DisableStatusEnum.ENABLE.getValue().equals(templateGroup.getTemplateGroupStatus()) &&
                            !templateGroup.getHasBindItem();
                })
                .map(TemplateGroupRelationVO::getTemplateGroupCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(groupCodeList)) {
            List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(groupCodeList, null);
            Map<String, List<TemplateGroupEntity>> groupMap = groupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
            groupCodeList = groupCodeList.stream().map(groupCode -> {
                List<TemplateGroupEntity> groupNameList = groupMap.get(groupCode);
                String groupName = CollectionUtils.isEmpty(groupNameList) ? "" : groupNameList.get(0).getName();
                return groupCode + "(" + groupName + ")";
            }).collect(Collectors.toList());
        }
        List<TemplateItemRelationVO> itemRelationEntityList = getTemplateItemRelation(templateEntity.getCode(), "");
        List<String> itemCodeList = itemRelationEntityList.stream()
                .filter(itemRelationEntity -> {
                    return DisableStatusEnum.ENABLE.getValue().equals(itemRelationEntity.getStatus()) &&
                            !itemRelationEntity.getHasBindGroup();
                })
                .map(TemplateItemRelationVO::getTemplateItemCode).collect(Collectors.toList());
        List<TemplateItemEntity> itemEntityList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemCodeList)) {
            itemEntityList = templateItemDao.getTemplateItemByCodeList(itemCodeList, null);
            if (!CollectionUtils.isEmpty(itemEntityList)) {
                List<String> itemGroupCodeList = itemEntityList.stream().map(TemplateItemEntity::getTemplateGroupCode).collect(Collectors.toList());
                List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(itemGroupCodeList, null);
                Map<String, List<TemplateGroupEntity>> groupMap = groupEntityList.stream().collect(Collectors.groupingBy(TemplateGroupEntity::getCode));
                itemEntityList.forEach(itemEntity -> {
                    List<TemplateGroupEntity> groupNameList = groupMap.get(itemEntity.getTemplateGroupCode());
                    String groupName = CollectionUtils.isEmpty(groupNameList) ? "" : groupNameList.get(0).getName();
                    itemEntity.setName(itemEntity.getCode() + "(" + itemEntity.getName() + ")")
                            .setTemplateGroupName(itemEntity.getTemplateGroupCode() + "(" + groupName + ")");
                });
            }
        }
        return new HealthCheckVO().setGroupCodeList(groupCodeList)
                .setItemCodeList(itemEntityList);
    }


    /**
     * 根据上次的主版本号，生成新的正式版本号
     *
     * @param oldMainVersion 上次的正式版本号
     * @return
     */
    private static String generateMainVersion(String oldMainVersion) {
        String newMainVersion = "";
        String nowYear = String.valueOf(DateTime.now().year());
        if (StringUtils.isBlank(oldMainVersion) || !oldMainVersion.contains(nowYear)) {
            return nowYear + "001";
        }
        String oldYear = oldMainVersion.substring(0, 4);
        Integer num = Integer.valueOf(oldMainVersion.substring(4, 7));
        System.out.println("oldYear = " + oldYear + ",num = " + num);
        newMainVersion = oldYear + String.format("%03d", num + 1);
        return newMainVersion;
    }

//    public static void main(String[] args) {
//        System.out.println(generateMainVersion("2022034"));
//        System.out.println(generateMainVersion("2023034"));
//        System.out.println(generateMainVersion(""));
//        System.out.println(generateMainVersion("2023194"));
//    }

    /**
     * 校验模板编码唯一性
     *
     * @param newTemplateEntity 新增/修改，前端传参
     * @param newTemplateEntity 要更新的模板内容
     */
    private void assemblyTemplateCode(TemplateEntity oldTemplateEntity, TemplateEntity newTemplateEntity) {
        String templateCode = oldTemplateEntity.getCode().trim();
        String templateName = oldTemplateEntity.getName();
        //新增或复制模板，重新生成编码
        if (null == oldTemplateEntity.getId()) {
            templateCode = "T_" + StringUtils.join(oldTemplateEntity.getBuCodeList(), "&") + "_" + StringUtils.join(oldTemplateEntity.getCompanyCodeList(), "&") +
                    "_" + GoodsCategoryEnum.getByValue(oldTemplateEntity.getCategory2List().get(0)).getCode()
                    + ContractSalesTypeEnum.getByValue(oldTemplateEntity.getSalesType()).getDirectCode() + "_" +
                    oldTemplateEntity.getContractActionType() + "_" + oldTemplateEntity.getProtocolType();
            if (StringUtils.isNotBlank(oldTemplateEntity.getEnterpriseCode())) {
                templateCode = templateCode + "_" + oldTemplateEntity.getEnterpriseCode();
            }
            List<TemplateEntity> templateList = templateDao.getTemplateByCode(templateCode, null);
            if (null == oldTemplateEntity.getId() && !CollectionUtils.isEmpty(templateList)) {
//            throw new BusinessException(ResultCodeEnum.TEMPLATE_DUPLICATE,",重复模板编码:" + templateList.get(0).getCode());
//            templateCode = templateCode + "_V" + templateList.get(0).getId();
//            templateCode = templateCode + "_V" + DateTimeUtil.formatDateTimeValue();
                TemplateEntity firstTemplate = templateList.get(0);
                Integer newDuplicateTimes = firstTemplate.getDuplicateTimes() + 1;
                templateCode = templateCode + "_" + String.format("%03d", newDuplicateTimes);
                firstTemplate.setDuplicateTimes(newDuplicateTimes);
                templateDao.updateById(firstTemplate);
            }
        }
        newTemplateEntity.setCode(templateCode);
        if (null == oldTemplateEntity.getId() && StringUtils.isBlank(templateName)) {
            templateName = "模板_" + StringUtils.join(
                    oldTemplateEntity.getBuCodeList().stream()
                            .map(buCode -> {
                                return BuCodeEnum.getByValue(oldTemplateEntity.getBuCodeList().get(0)).getDesc();
                            }).collect(Collectors.toList()), "/")
                    + "_" + StringUtils.join(oldTemplateEntity.getCompanyCodeList(), "&") + "_" +
                    GoodsCategoryEnum.getDesc(oldTemplateEntity.getCategory2List().get(0)) +
                    ContractSalesTypeEnum.getByValue(oldTemplateEntity.getSalesType()).getDescription() + "_" +
                    ContractTradeTypeEnum.getByValue(oldTemplateEntity.getContractActionType()).getDesc() + "_" +
                    ProtocolTypeEnum.getByValue(oldTemplateEntity.getProtocolType()).getDesc();
            if (StringUtils.isNotBlank(oldTemplateEntity.getEnterpriseName())) {
                templateName = templateName + "_" + oldTemplateEntity.getEnterpriseName();
            }
            newTemplateEntity.setName(templateName);
        }
        if (!CollectionUtils.isEmpty(oldTemplateEntity.getCompanyCodeList())) {
            newTemplateEntity.setCompanyCode("," + StringUtils.join(oldTemplateEntity.getCompanyCodeList(), ",") + ",");
        }
        if (!CollectionUtils.isEmpty(oldTemplateEntity.getBuCodeList())) {
            newTemplateEntity.setBuCode("," + StringUtils.join(oldTemplateEntity.getBuCodeList(), ",") + ",");
        }
        if (!CollectionUtils.isEmpty(oldTemplateEntity.getCategory1List())) {
            String category1 = oldTemplateEntity.getCategory1List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
            newTemplateEntity.setCategory1("," + category1 + ",");
            newTemplateEntity.setCategory1Name(categoryFacade.assemblyCategoryNames(oldTemplateEntity.getCategory1List()));
        }
        if (!CollectionUtils.isEmpty(oldTemplateEntity.getCategory2List())) {
            String category2 = oldTemplateEntity.getCategory2List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
            newTemplateEntity.setCategory2("," + category2 + ",");
            newTemplateEntity.setCategory2Name(categoryFacade.assemblyCategoryNames(oldTemplateEntity.getCategory2List()));
        }
        if (!CollectionUtils.isEmpty(oldTemplateEntity.getCategory3List())) {
            String category3 = oldTemplateEntity.getCategory3List().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
            newTemplateEntity.setCategory3("," + category3 + ",");
            newTemplateEntity.setCategory3Name(categoryFacade.assemblyCategoryNames(oldTemplateEntity.getCategory3List()));
        }

    }

    /**
     * 处理部分转月、全部反点价、部分反点价的模板自动匹配绑定
     *
     * @param templateEntity 新增的分转月、全部反点价、部分反点价的模板
     */
    private void processTemplateBindInfo(TemplateEntity templateEntity) {
        if (!ContractTradeTypeEnum.needTemplateBind().contains(templateEntity.getContractActionType())) {
            return;
        }
        //1、查询对应的拆分的合同模板
        List<TemplateEntity> templateBindEntityList = new ArrayList<>();
        KeyVariableDTO splitKeyVariableDTO = new KeyVariableDTO()
                .setBuCode(templateEntity.getBuCode())
                .setCompanyCode(templateEntity.getCompanyCode())
                .setCategoryId(templateEntity.getCategoryId())
                .setTemplateCategory1(templateEntity.getCategory1())
                .setTemplateCategory2(templateEntity.getCategory2())
                .setTemplateCategory3(templateEntity.getCategory3())
                .setSalesType(templateEntity.getSalesType())
                .setProtocolType(ProtocolTypeEnum.AGREEMENT.getValue())
                .setContractActionType(ContractTradeTypeEnum.SPLIT_NORMAL.getValue())
                .setEnterpriseCode(templateEntity.getEnterpriseCode());
        TemplateEntity splitTemplateEntity = templateDao.getBindTemplateByKeyVariable(splitKeyVariableDTO);
        if (null == splitTemplateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_MATCH_NOT_EXIST, "匹配不到对应条件的拆分模板！");
        }
        templateBindEntityList.add(splitTemplateEntity);
        //2、查询对应的新增的合同模板
        String protocolType = ContractSalesTypeEnum.PURCHASE.getValue() == templateEntity.getSalesType() ? ProtocolTypeEnum.CONTRACT.getValue() : templateEntity.getProtocolType();
        KeyVariableDTO newKeyVariableDTO = new KeyVariableDTO()
                .setBuCode(templateEntity.getBuCode())
                .setCompanyCode(templateEntity.getCompanyCode())
//                .setCategoryId(templateEntity.getCategoryId())
                .setTemplateCategory1(templateEntity.getCategory1())
                .setTemplateCategory2(templateEntity.getCategory2())
                .setTemplateCategory3(templateEntity.getCategory3())
                .setSalesType(templateEntity.getSalesType())
                .setProtocolType(protocolType)
                .setContractActionType(ContractTradeTypeEnum.NEW.getValue())
                .setEnterpriseCode(templateEntity.getEnterpriseCode());
        TemplateEntity newTemplateEntity = templateDao.getBindTemplateByKeyVariable(newKeyVariableDTO);
        templateBindEntityList.add(newTemplateEntity);
        if (null == newTemplateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_MATCH_NOT_EXIST, "匹配不到对应条件的新增模板！");
        }
        templateBindDao.saveTemplateBind(templateEntity.getCode(), templateBindEntityList);
    }

//    /**
//     * 记录模板新增/修改日志
//     *
//     * @param templateEntity 模板信息
//     * @param operationType  操作动作
//     */
//    private void recordTemplateHistoryLog(TemplateEntity templateEntity, String operationType) {
//        TemplateHistoryLogEntity templateHistoryLogEntity = BeanConvertUtils.convert(TemplateHistoryLogEntity.class, templateEntity);
//        templateHistoryLogEntity.setId(null)
//                .setReferId(templateEntity.getId())
//                .setReferCode(templateEntity.getCode())
//                .setReferType(TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue())
//                .setOperationType(operationType)
//                .setCategoryId(String.valueOf(templateEntity.getCategoryId()))
//                .setSalesType(String.valueOf(templateEntity.getSalesType()))
//                .setContractActionType(String.valueOf(templateEntity.getContractActionType()))
//                .setBindRelationInfo(!CollectionUtils.isEmpty(templateEntity.getTemplateGroupList()) ? FastJsonUtils.getBeanToJson(templateEntity.getTemplateGroupList()) : "");
//        templateHistoryLogDao.save(templateHistoryLogEntity);
//    }

    /**
     * 记录模板新增/修改日志
     *
     * @param templateEntity   模板信息
     * @param basicOperateEnum 操作动作
     */
    private void recordTemplateHistoryLog(TemplateEntity templateEntity, BasicOperateEnum basicOperateEnum,
                                          VersionType versionType, String content, String requestInfo, String memo) {
        TemplateHistoryEntity templateHistoryEntity = new TemplateHistoryEntity();
        templateHistoryEntity.setId(null)
                .setReferId(templateEntity.getId())
                .setReferCode(templateEntity.getCode())
                .setReferName(templateEntity.getName())
                .setReferType(TemplateTypeEnum.SALES_CONTRACT_TEMPLATE.getValue())
                .setOperationType(basicOperateEnum.getValue())
                .setOperationTypeInfo(basicOperateEnum.getDesc())
                .setCategoryId(String.valueOf(templateEntity.getCategoryId()))
                .setSalesType(String.valueOf(templateEntity.getSalesType()))
                .setContractActionType(String.valueOf(templateEntity.getContractActionType()))
                .setVersionType(versionType.getValue())
                .setVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateEntity.getMainVersion() : templateEntity.getVersion())
                .setMainVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateEntity.getVersion() : templateEntity.getMainVersion())
                .setMainVersionStatus(templateEntity.getMainVersionStatus())
                .setMainVersionDesc(BasicOperateEnum.getMainVersionType().contains(basicOperateEnum.getValue()) ? templateEntity.getMainVersionDesc() : "")
                .setStatus(templateEntity.getStatus())
                .setRequestInfo(requestInfo)
                .setContent(content)
                .setMemo(memo)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(templateEntity.getUpdatedBy())
                .setCreatedBy(templateEntity.getCreatedBy());
        if (BasicOperateEnum.needRecordDetailInfoType().contains(basicOperateEnum.getValue())) {
            templateHistoryEntity.setContent(FastJsonUtils.getBeanToJson(this.getTemplateDetailInfoLog(templateEntity)));
        }
        templateHistoryService.saveTemplateHistory(templateHistoryEntity);
    }
}
