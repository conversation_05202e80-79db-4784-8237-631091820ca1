package com.navigator.husky.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.admin.pojo.vo.StructureRuleVO;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.bisiness.enums.ValueTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.rule.RuleVariableBizCodeEnum;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.facade.SpuFacade;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.qo.SpuQO;
import com.navigator.husky.dao.TemplateItemDao;
import com.navigator.husky.dao.VariableDao;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.dto.VariableImportDTO;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.entity.VariableEntity;
import com.navigator.husky.pojo.enums.VariableBizCodeEnum;
import com.navigator.husky.pojo.enums.VariableInputType;
import com.navigator.husky.service.VariableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 14:36
 **/
@Service
@Slf4j
public class VariableServiceImpl implements VariableService {
    @Resource
    private VariableDao variableDao;
    @Resource
    private DictItemFacade dictItemFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private AttributeFacade attributeFacade;
    @Autowired
    private SpuFacade spuFacade;
    @Autowired
    private StructureRuleFacade structureRuleFacade;
    @Autowired
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private TemplateItemDao templateItemDao;
    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public Boolean updateVariable(VariableEntity variableEntity) {
        VariableEntity oldVariableEntity = variableDao.getById(variableEntity.getId());
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        oldVariableEntity.setDisplayName(StringUtils.isNotBlank(variableEntity.getDisplayName()) ? variableEntity.getDisplayName().trim() : oldVariableEntity.getDisplayName().trim())
//                .setTypicalValue(StringUtils.isNotBlank(variableEntity.getTypicalValue()) ? variableEntity.getTypicalValue() : oldVariableEntity.getTypicalValue())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        if (!CollectionUtils.isEmpty(variableEntity.getPatternRelationList())) {
            oldVariableEntity.setPatternRelations(StringUtils.join(variableEntity.getPatternRelationList(), ","));
        }
        return variableDao.updateById(oldVariableEntity);
    }

    @Override
    public Result queryVariableByCondition(QueryDTO<VariableEntity> queryDTO) {
        IPage<VariableEntity> variableEntityIPage = variableDao.queryVariableByCondition(queryDTO);
        if (!CollectionUtils.isEmpty(variableEntityIPage.getRecords())) {
            this.getItemInfoByVariable(variableEntityIPage.getRecords());
        }
        return Result.page(variableEntityIPage);
    }

    @Override
    public List<VariableEntity> queryExportVariableList(VariableEntity variableQO) {
        List<VariableEntity> variableEntityList = variableDao.queryExportVariableList(variableQO);
        if (!CollectionUtils.isEmpty(variableEntityList)) {
            this.getItemInfoByVariable(variableEntityList);
        }
        return variableEntityList;
    }

    @Override
    public VariableEntity getVariableByCode(String code) {
        return variableDao.getVariableByCode(code);
    }

    @Override
    public VariableEntity getVariableByDisplayName(String displayName) {
        return variableDao.getVariableByDisplayName(displayName);
    }

    @Override
    public List<String> getNotUsedVariableList() {
        List<VariableEntity> variableList = this.getAllVariableList(0, 0);
        List<String> variableCodeList = new ArrayList<>();
        for (VariableEntity variableEntity : variableList) {
            List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByVariable(variableEntity.getCode());
            if (CollectionUtils.isEmpty(itemEntityList)) {
                variableCodeList.add(variableEntity.getCode() + "_" + variableEntity.getDisplayName());
            }
        }
        return variableCodeList;
    }

    @Override
    public Result importVariableInfo(MultipartFile uploadFile) {
        List<VariableImportDTO> variableEntityList;
        try {
            variableEntityList = EasyPoiUtils.importExcel(uploadFile, 0, 1, VariableImportDTO.class);
            log.info(FastJsonUtils.getBeanToJson("数字合同-变量导入当前信息：" + variableEntityList));
            List<String> notExistCodeList = new ArrayList<>();
            for (VariableImportDTO importVariableEntity : variableEntityList) {
                VariableEntity variableEntity = variableDao.getVariableByCode(importVariableEntity.getCode());
                if (null == variableEntity) {
                    notExistCodeList.add(importVariableEntity.getCode());
                }
                variableEntity.setName(importVariableEntity.getName())
                        .setDisplayName(importVariableEntity.getDisplayName())
                        .setMemo(StringUtils.isNotBlank(importVariableEntity.getMemo()) ? importVariableEntity.getMemo().trim() : "")
                        .setUpdatedBy("系统");
                variableDao.updateById(variableEntity);
            }
            return Result.success("异常不存在的变量编码:" + FastJsonUtils.getBeanToJson(variableEntityList), variableEntityList);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("变量导入模板错误" + e.toString());
        }
    }

    @Override
    public List<VariableEntity> getAllVariableList(Integer isCondition, Integer isKey) {
        List<VariableEntity> variableEntityList = variableDao.getAllVariableList(isCondition, isKey);
        if (!CollectionUtils.isEmpty(variableEntityList)) {
            this.getItemInfoByVariable(variableEntityList);
        }
        return variableEntityList;
    }

    @Override
    public List<VariableEntity> getAllVariableBasicList() {
        return variableDao.getAllVariableList(null, null);
    }


    private void getItemInfoByVariable(List<VariableEntity> variableEntityList) {
        variableEntityList.forEach(variableEntity -> {
            if (StringUtils.isNotBlank(variableEntity.getPatternRelations())) {
                List<String> patternRelationList = Arrays.stream(variableEntity.getPatternRelations().split(",")).collect(Collectors.toList());
                String patternRelationInfo = patternRelationList.stream().map(PatternRelationEnum::getDescByCode).collect(Collectors.joining(";"));
                variableEntity.setPatternRelationList(patternRelationList)
                        .setPatternRelations(patternRelationInfo);
            }
            if (StringUtils.isNotBlank(variableEntity.getInputType())) {
                variableEntity.setInputTypeInfo(VariableInputType.getByValue(variableEntity.getInputType()).getDesc());
            }
            if (DisableStatusEnum.ENABLE.getValue().equals(variableEntity.getHasDict())) {
                List<DictItemEntity> dictItemEntityList = dictItemFacade.getItemByDictCode(variableEntity.getCode());
                List<EnumValueDTO> enumValueDTOList = dictItemEntityList.stream().map(dictItemEntity -> {
                    return new EnumValueDTO()
                            .setEnumName(dictItemEntity.getDictCode())
                            .setValue(dictItemEntity.getItemValue())
                            .setCode(dictItemEntity.getItemCode())
                            .setMemo(dictItemEntity.getMemo())
                            .setDesc(dictItemEntity.getItemDescription());
                }).collect(Collectors.toList());
                variableEntity.setEnumValueDTOList(enumValueDTOList);
            }
            if (DisableStatusEnum.ENABLE.getValue().equals(variableEntity.getIsEnum())) {
                try {
                    Class<Enum> clazz = (Class<Enum>) Class.forName(variableEntity.getEnumPath());
                    //获取所有枚举实例
                    Enum[] enumConstants = clazz.getEnumConstants();
                    //根据方法名获取方法
                    Method getValue = clazz.getMethod("getValue");
                    Method getDesc = clazz.getMethod("getDesc");
                    List<EnumValueDTO> enumValueDTOList = new ArrayList<>();
                    for (Enum enumInfo : enumConstants) {
                        EnumValueDTO enumValueDTO = new EnumValueDTO()
                                .setEnumName(enumInfo.name())
//                                .setCode(getValue.invoke(enumInfo).toString())
                                .setDesc(getDesc.invoke(enumInfo).toString());
                        if (ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType())) {
                            enumValueDTO.setCode(getValue.invoke(enumInfo).toString());
                        } else {
                            enumValueDTO.setValue((Integer) getValue.invoke(enumInfo));
                            enumValueDTO.setCode(enumValueDTO.getValue().toString());
                        }
                        enumValueDTOList.add(enumValueDTO);
                    }
                    variableEntity.setEnumValueDTOList(enumValueDTOList);
                } catch (Exception e) {
                    log.info(e.toString());
                }
            }
            getBusinessEnumList(variableEntity);
        });
    }

    private void getBusinessEnumList(VariableEntity variableEntity) {
        List<EnumValueDTO> enumValueDTOList = new ArrayList<>();
        if (VariableBizCodeEnum.DELIVERY_FACTORY_CODE.getValue().equals(variableEntity.getCode())) {
            List<FactoryEntity> factoryEntityList = factoryWarehouseFacade.getAllFactory(DisableStatusEnum.ENABLE.getValue());
            if (!CollectionUtils.isEmpty(factoryEntityList)) {
                enumValueDTOList = factoryEntityList.stream().map(factory -> {
                    return new EnumValueDTO()
                            .setEnumName(VariableBizCodeEnum.DELIVERY_FACTORY_CODE.getValue())
                            .setValue(factory.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? factory.getCode() : factory.getId().toString())
                            .setDesc(factory.getCode());
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.SPU_NAME.getValue().equals(variableEntity.getCode())) {
            List<SpuDTO> spuList = spuFacade.querySpuDTOList(new SpuQO());
            if (!CollectionUtils.isEmpty(spuList)) {
                enumValueDTOList = spuList.stream().map(spuInfo -> {
                    return new EnumValueDTO()
                            .setEnumName(spuInfo.getSpuName())
                            .setValue(spuInfo.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? spuInfo.getSpuName() : spuInfo.getId().toString())
                            .setDesc(spuInfo.getSpuName());
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.PACKAGE_NAME.getValue().equals(variableEntity.getCode())) {
            List<AttributeValueEntity> attributeValueEntityList = attributeFacade.getAttributeValueListByType(AttributeType.PACKAGE.getValue());
            List<String> packageValueList = attributeValueEntityList.stream().map(AttributeValueEntity::getName).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(packageValueList)) {
                enumValueDTOList = packageValueList.stream().map(packageValue -> {
                    return new EnumValueDTO()
                            .setEnumName(packageValue)
                            .setCode(packageValue)
                            .setDesc(packageValue);
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.STRUCTURE_TYPE.getValue().equals(variableEntity.getCode())) {
            Result result = structureRuleFacade.queryAvailableStructureList();
            List<StructureRuleVO> structureRuleVOList = JSON.parseArray(JSON.toJSONString(result.getData()), StructureRuleVO.class);
            if (!CollectionUtils.isEmpty(structureRuleVOList)) {
                enumValueDTOList = structureRuleVOList.stream().map(structureRuleVO -> {
                    return new EnumValueDTO()
                            .setEnumName(VariableBizCodeEnum.STRUCTURE_TYPE.getValue())
                            .setValue(structureRuleVO.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? structureRuleVO.getCode() : structureRuleVO.getId().toString())
                            .setDesc(structureRuleVO.getCode() + "-" + structureRuleVO.getStructureName());
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.COMPANY_CODE.getValue().equals(variableEntity.getCode())) {
            List<CompanyEntity> companyEntityList = companyFacade.getAllCompany();
            if (!CollectionUtils.isEmpty(companyEntityList)) {
                enumValueDTOList = companyEntityList.stream().map(company -> {
                    return new EnumValueDTO()
                            .setEnumName(VariableBizCodeEnum.COMPANY_CODE.getValue())
                            .setValue(company.getId())
                            .setCode(ValueTypeEnum.STRING.getValue().equals(variableEntity.getValueType()) ? company.getShortName() : company.getId().toString())
                            .setCode(company.getShortName())
                            .setDesc(company.getName());
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.CATEGORY1.getValue().equals(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(1);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category1 -> {
                    return new EnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY1.getValue())
                            .setValue(category1.getSerialNo())
                            .setCode(category1.getSerialNo().toString())
                            .setDesc(category1.getName());
                }).collect(Collectors.toList());
            }
        } else if (Arrays.asList(VariableBizCodeEnum.CATEGORY2.getValue(), RuleVariableBizCodeEnum.CATEGORY_ID.getValue()).contains(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(2);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category2 -> {
                    return new EnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY2.getValue())
                            .setValue(category2.getSerialNo())
                            .setCode(category2.getSerialNo().toString())
                            .setDesc(category2.getName());
                }).collect(Collectors.toList());
            }
        } else if (VariableBizCodeEnum.CATEGORY3.getValue().equals(variableEntity.getCode())) {
            List<CategoryEntity> firstCategoryList = categoryFacade.getAllCategoryList(3);
            if (!CollectionUtils.isEmpty(firstCategoryList)) {
                enumValueDTOList = firstCategoryList.stream().map(category3 -> {
                    return new EnumValueDTO()
                            .setEnumName(RuleVariableBizCodeEnum.CATEGORY3.getValue())
                            .setValue(category3.getSerialNo())
                            .setCode(category3.getSerialNo().toString())
                            .setDesc(category3.getName());
                }).collect(Collectors.toList());
            }
        }
        if (!CollectionUtils.isEmpty(enumValueDTOList)) {
            variableEntity.setEnumValueDTOList(enumValueDTOList);
        }
    }
}
