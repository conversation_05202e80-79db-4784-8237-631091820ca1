package com.navigator.husky.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.BasicOperateEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.TemplateTypeEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.dao.TemplateHistoryDao;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;
import com.navigator.husky.pojo.enums.VersionType;
import com.navigator.husky.service.TemplateHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-28 16:40
 **/
@Service
@Slf4j
public class TemplateHistoryServiceImpl implements TemplateHistoryService {
    @Resource
    private TemplateHistoryDao templateHistoryDao;

    @Autowired
    private EmployFacade employFacade;

    @Override
    public Result queryHistoryByCondition(QueryDTO<TemplateHistoryEntity> queryDTO) {
        IPage<TemplateHistoryEntity> historyEntityIPage = templateHistoryDao.queryHistoryByCondition(queryDTO);
        if (!CollectionUtils.isEmpty(historyEntityIPage.getRecords())) {
            historyEntityIPage.getRecords().forEach(historyEntity -> {
                historyEntity.setReferTypeInfo(TemplateTypeEnum.getDescByValue(historyEntity.getReferType()))
                        .setVersionTypeInfo(VersionType.getDescByValue(historyEntity.getVersionType()))
                        .setMainVersionStatusInfo(DisableStatusEnum.ENABLE.getValue().equals(historyEntity.getMainVersionStatus()) ? "正式版本" : "临时版本");
            });
        }
        return Result.page(historyEntityIPage);
    }

    @Override
    public void saveTemplateHistory(TemplateHistoryEntity templateHistoryEntity) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateHistoryEntity
                .setCreatedBy(name)
                .setUpdatedBy(name);
        templateHistoryDao.save(templateHistoryEntity);
    }

    @Override
    public List<EnumValueDTO> getOperationTypeList() {
        return Arrays.stream(BasicOperateEnum.values()).map(basicOperateEnum ->
                new EnumValueDTO().setValue(basicOperateEnum.getValue())
                        .setDesc(basicOperateEnum.getDesc())
        ).collect(Collectors.toList());
    }
}
