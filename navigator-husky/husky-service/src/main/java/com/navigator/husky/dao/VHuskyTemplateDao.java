package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.husky.mapper.VHuskyTemplateMapper;
import com.navigator.husky.pojo.entity.VHuskyTemplateEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-11 19:14
 **/
@Dao
public class VHuskyTemplateDao extends BaseDaoImpl<VHuskyTemplateMapper, VHuskyTemplateEntity> {
    /**
     * 根据编号集合，获取有效模板数据集合
     *
     * @param templateCodeList 模板编号集合
     * @return
     */
    public List<VHuskyTemplateEntity> getTemplateByCodeList(List<String> templateCodeList) {
        if (CollectionUtils.isEmpty(templateCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<VHuskyTemplateEntity>()
                .in(VHuskyTemplateEntity::getTemplateCode, templateCodeList)
        );
    }

}
