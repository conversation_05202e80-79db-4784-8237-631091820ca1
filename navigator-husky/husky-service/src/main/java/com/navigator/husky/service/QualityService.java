package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.vo.QualityExportVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 14:06
 **/
public interface QualityService {
    /**
     * 判断质量指标是否存在（TT新增/合同变更）
     *
     * @param qualityInfoDTO 质量指标条件信息
     * @return
     */
    QualityEntity judgeExistQuality(QualityInfoDTO qualityInfoDTO);

    /**
     * 列表查询模板信息
     *
     * @param queryDTO 模板查询条件
     * @return 查询模板分页结果
     */
    Result queryQualityByCondition(QueryDTO<QualityInfoDTO> queryDTO);

    QualityEntity getQualityById(Integer qualityId);

    /**
     * 新增模板信息
     *
     * @param qualityEntity 模板信息
     * @return
     */
    Boolean saveQuality(QualityEntity qualityEntity);

    Boolean updateQuality(QualityEntity qualityEntity);

    Boolean updateQualityStatus(Integer qualityId, Integer status);

    List<QualityExportVO> exportQualityList(QualityInfoDTO qualityInfoDTO);

    Result importQuality(MultipartFile uploadFile);

    /**
     * 处理历史数据
     * @param id
     */
    void processHistoryData(Integer id);
}
