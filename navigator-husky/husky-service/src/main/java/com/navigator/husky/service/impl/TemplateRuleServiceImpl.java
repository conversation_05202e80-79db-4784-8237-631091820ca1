package com.navigator.husky.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.DictItemFacade;
import com.navigator.bisiness.enums.LogicRelationEnum;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.husky.dao.TemplateRuleDao;
import com.navigator.husky.dao.TemplateRuleDetailDao;
import com.navigator.husky.dao.VariableDao;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import com.navigator.husky.pojo.dto.RuleScriptDTO;
import com.navigator.husky.pojo.entity.TemplateRuleDetailEntity;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;
import com.navigator.husky.pojo.entity.VariableEntity;
import com.navigator.husky.service.TemplateRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 15:35
 **/
@Service
@Slf4j
public class TemplateRuleServiceImpl implements TemplateRuleService {
    @Autowired
    private TemplateRuleDao templateRuleDao;
    @Autowired
    private TemplateRuleDetailDao templateRuleDetailDao;
    @Autowired
    private VariableDao variableDao;
    @Autowired
    private DictItemFacade dictItemFacade;
    @Autowired
    private TemplateContentBuilder templateContentBuilder;

    @Override
    public TemplateRuleEntity recordTemplateRule(String referCode, Integer referType, List<ConditionVariableDTO> conditionVariableList) {
        TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(referCode, referType);
        if (CollectionUtils.isEmpty(conditionVariableList)) {
            if (null != templateRuleEntity) {
                templateRuleDao.dropTemplateRule(referCode, referType);
                templateRuleDetailDao.dropTemplateRuleDetail(referCode, referType);
            }
            return new TemplateRuleEntity().setRuleCode("").setRuleInfo("").setConditionInfo("");
        }
        List<String> variableList = conditionVariableList.stream().map(ConditionVariableDTO::getConditionVariable).distinct().collect(Collectors.toList());
//        RuleScriptDTO jointRuleInfo = this.jointConditionInfo(conditionVariableList);
        RuleScriptDTO jointRuleInfo = templateContentBuilder.jointConditionInfo(conditionVariableList);
        String ruleCode = StringUtils.isNotBlank(templateRuleEntity.getRuleCode()) ?
                templateRuleEntity.getRuleCode() : UUID.randomUUID().toString();
        templateRuleEntity
                .setRuleCode(ruleCode)
                .setReferCode(referCode)
                .setReferType(referType)
                .setConditionVariable(StringUtils.join(variableList, ","))
                //加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
                .setConditionInfo(jointRuleInfo.getJointConditionInfo())
                .setRuleInfo(jointRuleInfo.getJointRuleInfo());
        if (null == templateRuleEntity.getId()) {
            templateRuleDao.save(templateRuleEntity);
        } else {
            templateRuleDao.updateById(templateRuleEntity);
        }
        //规则元素信息记录同步
        templateRuleDetailDao.recordRuleDetail(templateRuleEntity, conditionVariableList);

        return templateRuleEntity;
    }

    @Override
    public void syncImportTemplateRule(String referCode, Integer referType, TemplateRuleEntity ruleEntity) {
        TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(referCode, referType);
        if (null != templateRuleEntity) {
            templateRuleDao.dropTemplateRule(referCode, referType);
            templateRuleDetailDao.dropTemplateRuleDetail(referCode, referType);
        }
        if (null == ruleEntity) {
            return;
        }
        ruleEntity.setId(null);
        templateRuleDao.save(ruleEntity);
        if (!CollectionUtils.isEmpty(ruleEntity.getRuleDetailEntityList())) {
            for (TemplateRuleDetailEntity ruleDetailEntity : ruleEntity.getRuleDetailEntityList()) {
                ruleDetailEntity.setId(null);
                templateRuleDetailDao.save(ruleDetailEntity);
            }
        }
    }

    @Override
    public TemplateRuleEntity getRuleByTemplateCode(String referCode, Integer referType) {
        return templateRuleDao.getRuleByTemplateCode(referCode, referType);
    }

    @Override
    public TemplateRuleEntity getRuleDetailEntityByCode(String referCode, Integer referType) {
        TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(referCode, referType);
        if (null == templateRuleEntity) {
            return templateRuleEntity;
        }
        List<TemplateRuleDetailEntity> ruleDetailEntityList = templateRuleDetailDao.getRuleDetailListByRuleCode(templateRuleEntity.getRuleCode());
        return templateRuleEntity.setRuleDetailEntityList(ruleDetailEntityList);
    }

    @Override
    public TemplateRuleEntity getRuleDetailByTemplateCode(String referCode, Integer referType) {
        TemplateRuleEntity templateRuleEntity = templateRuleDao.getRuleByTemplateCode(referCode, referType);
        if (null == templateRuleEntity) {
            return templateRuleEntity;
        }
        List<TemplateRuleDetailEntity> ruleDetailEntityList = templateRuleDetailDao.getRuleDetailListByRuleCode(templateRuleEntity.getRuleCode());
        List<ConditionVariableDTO> conditionVariableList = ruleDetailEntityList.stream()
                .map(ruleDetailEntity -> {
                    ConditionVariableDTO conditionVariableDTO = BeanConvertUtils.convert(ConditionVariableDTO.class, ruleDetailEntity);
//                    List<Integer> conditionValueIdList = Arrays.stream(conditionVariableDTO.getConditionValueIds().split(","))
//                            .map(Integer::valueOf).collect(Collectors.toList());
//                    conditionVariableDTO.setConditionValueIdList(conditionValueIdList);
                    // BUGFIX：Case-1003305-数字合同模板,品名模板触发条件提交报错 Author: nana 2025-06-24 Start
                    List<String> conditionValueList = Arrays.stream(conditionVariableDTO.getConditionValue().split(",")).collect(Collectors.toList());
                    List<String> conditionDescList = Arrays.stream(conditionVariableDTO.getConditionValueInfo().split(",")).collect(Collectors.toList());
                    if (PatternRelationEnum.getContainRelation().contains(ruleDetailEntity.getPatternRelation())
                            && TemplateConstant.TEMPLATE_RULE_SPU.equals(ruleDetailEntity.getConditionVariable())) {
                        if (StringUtils.isNotBlank(ruleDetailEntity.getConditionValue())) {
                            conditionValueList = this.convertSpuValueToList(ruleDetailEntity.getConditionValue());
//                            conditionValueList = Arrays.stream( conditionValue.substring(1,conditionValue.length()-1).split("")).collect(Collectors.toList());
                        }
                        if (StringUtils.isNotBlank(ruleDetailEntity.getConditionValueInfo())) {
                            conditionDescList = this.convertSpuValueToList(ruleDetailEntity.getConditionValueInfo());
                        }
                    }
                    // BUGFIX：Case-1003305-数字合同模板,品名模板触发条件提交报错 Author: nana 2025-06-24 End
                    conditionVariableDTO.setConditionValueList(conditionValueList);
                    // 变量名称
                    VariableEntity variableEntity = variableDao.getById(ruleDetailEntity.getConditionVariableId());
                    if (null != variableEntity) {
                        conditionVariableDTO.setConditionVariableInfo(variableEntity.getName());
                    }
                    //阈值描述信息-前端展示
//                    List<DictItemEntity> dictItemEntityList = dictItemFacade.getDictItemById(conditionValueIdList);
//                    String conditionValueInfo = dictItemEntityList.stream().map(DictItemEntity::getItemName).collect(Collectors.joining(","));
                    conditionVariableDTO.setConditionValueInfo(ruleDetailEntity.getConditionValueInfo())
                            .setConditionDescList(conditionDescList);
                    conditionVariableDTO.setLogicRelationInfo(LogicRelationEnum.getByCode(ruleDetailEntity.getLogicRelation()).getDescription())
                            .setPatternRelationInfo(PatternRelationEnum.getByCode(ruleDetailEntity.getPatternRelation()).getDescription());
                    return conditionVariableDTO;
                })
                .collect(Collectors.toList());
        return templateRuleEntity.setConditionVariableList(conditionVariableList);
    }

    private List<String> convertSpuValueToList(String conditionValue) {
        List<String> conditionValueList = new ArrayList<>();
        // 正则匹配引号内的内容（包括逗号）
        Pattern pattern = Pattern.compile("\"([^\"]*)\"");
        Matcher matcher = pattern.matcher(conditionValue);
        conditionValueList = new ArrayList<>();
        while (matcher.find()) {
            conditionValueList.add(matcher.group(1)); // 提取引号内的内容
        }
        return conditionValueList;
    }
}
