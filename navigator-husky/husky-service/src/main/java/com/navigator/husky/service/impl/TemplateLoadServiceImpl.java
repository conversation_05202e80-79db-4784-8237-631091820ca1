package com.navigator.husky.service.impl;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.dao.TemplateLoadDao;
import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import com.navigator.husky.service.TemplateLoadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-02 16:48
 **/
@Service
@Slf4j
public class TemplateLoadServiceImpl implements TemplateLoadService {
    @Resource
    private TemplateLoadDao templateLoadDao;
    @Autowired
    private EmployFacade employFacade;

    @Override
    public TemplateLoadEntity saveTemplateLoad(TemplateLoadEntity templateLoadEntity) {
        Timestamp now = DateTimeUtil.now();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateLoadEntity.setCreatedAt(now)
                .setUpdatedAt(now)
                .setCreatedBy(name);
        templateLoadDao.save(templateLoadEntity);
        return templateLoadEntity;
    }

    @Override
    public TemplateLoadEntity updateTemplateLoad(TemplateLoadEntity templateLoadEntity) {
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateLoadEntity.setCreatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        templateLoadDao.updateById(templateLoadEntity);
        return templateLoadEntity;
    }
}
