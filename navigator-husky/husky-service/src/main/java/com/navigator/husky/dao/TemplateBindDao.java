package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateBindMapper;
import com.navigator.husky.pojo.entity.TemplateBindEntity;
import com.navigator.husky.pojo.entity.TemplateEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-10-08 15:17
 **/
@Dao
public class TemplateBindDao extends BaseDaoImpl<TemplateBindMapper, TemplateBindEntity> {
    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param templateCode     模板编码
     * @param bindTemplateCode 所属模板
     * @return
     */
    public List<TemplateBindEntity> getTemplateBindByCode(String templateCode, String bindTemplateCode) {
        return this.list(new LambdaQueryWrapper<TemplateBindEntity>()
                .eq(StringUtils.isNotBlank(templateCode), TemplateBindEntity::getTemplateCode, templateCode)
                .eq(StringUtils.isNotBlank(bindTemplateCode), TemplateBindEntity::getBindTemplateCode, bindTemplateCode)
                .eq(TemplateBindEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateBindEntity::getSort)
        );
    }

    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param templateCode     模板编码
     * @param bindTemplateList 模板编码
     * @return
     */
    public void saveTemplateBind(String templateCode, List<TemplateEntity> bindTemplateList) {
        //删除此模板的关联的模板
        this.dropTemplateBind(templateCode);
        for (TemplateEntity bindTemplateEntity : bindTemplateList) {
            TemplateBindEntity bindEntity = new TemplateBindEntity()
                    .setTemplateCode(templateCode)
                    .setBindTemplateCode(bindTemplateEntity.getCode())
                    .setMemo(bindTemplateEntity.getName())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setSort(bindTemplateList.indexOf(bindTemplateEntity) + 1);
            this.save(bindEntity);
        }
    }

    public void syncImportBindRelation(String templateCode, List<TemplateBindEntity> bindEntityList) {
        this.dropTemplateBind(templateCode);
        if (CollectionUtils.isEmpty(bindEntityList)) {
            return;
        }
        bindEntityList.forEach(templateBindEntity -> {
            templateBindEntity.setId(null);
            this.save(templateBindEntity);
        });
    }

    /**
     * 解绑模板-条款组关系
     *
     * @param templateCode
     */
    public void dropTemplateBind(String templateCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateBindEntity::getTemplateCode, templateCode)
                .eq(TemplateBindEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateBindEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(TemplateBindEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
