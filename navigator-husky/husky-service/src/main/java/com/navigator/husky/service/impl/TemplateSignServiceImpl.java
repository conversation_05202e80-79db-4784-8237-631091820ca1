package com.navigator.husky.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.dto.DroolsRuleBizInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.TemplateRenderUtil;
import com.navigator.husky.dao.*;
import com.navigator.husky.pojo.entity.*;
import com.navigator.husky.pojo.enums.TemplateLoadTypeEnum;
import com.navigator.husky.service.TemplateCheckService;
import com.navigator.husky.service.TemplateLoadService;
import com.navigator.husky.service.TemplateSignService;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.dto.contractsign.KeyVariableDTO;
import com.navigator.trade.pojo.dto.contractsign.SignHuskyTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-14 15:25
 **/
@Service
@Slf4j
public class TemplateSignServiceImpl implements TemplateSignService {
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private TemplateDao templateDao;
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private TemplateGroupRelationDao templateGroupRelationDao;
    @Autowired
    private TemplateItemRelationDao templateItemRelationDao;
    @Autowired
    private TemplateBindDao templateBindDao;
    @Autowired
    private TemplateItemDao templateItemDao;
    @Autowired
    private TemplateHistoryLogDao templateHistoryLogDao;
    @Autowired
    private TemplateContentBuilder templateContentBuilder;
    @Autowired
    private TemplateLoadService templateLoadService;
    @Autowired
    private TemplateCheckService templateCheckService;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Value("${template.styleItemCode}")
    private String styleItemCode;
    private static String pageSplitGroupCode = "M1008";


    @Override
    public Result matchTemplateByRule(Integer contractSignId) {
        //1、获取：业务参数信息+关键变量+条件变量
        Result result = contractSignFacade.buildHuskySignTemplateDTOV2(contractSignId);
        if (!result.isSuccess()) {
            return result;
        }
        SignHuskyTemplateDTO templateDTO = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), SignHuskyTemplateDTO.class);
        log.info("1、协议出具模板业务数据：========" + contractSignId);
        //关键变量
        KeyVariableDTO keyVariableDTO = templateDTO.getKeyVariableDTO();

        // 日志：业务基础数据记录
        TemplateLoadEntity templateLoadEntity = templateDTO.getTemplateLoadEntity()
                .setType(TemplateLoadTypeEnum.ACQUIRE_BIX_DATA.getValue())
                .setMemo(TemplateLoadTypeEnum.ACQUIRE_BIX_DATA.getDesc())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.ACQUIRE_BIX_DATA) +
                        "\n关键变量：" + FastJsonUtils.getBeanToJson(templateDTO.getKeyVariableDTO()))
                .setBizData(FastJsonUtils.getBeanToJson(templateDTO));
        templateLoadService.saveTemplateLoad(templateLoadEntity);
        //2、匹配合同模板
        TemplateEntity templateEntity = templateDao.getTemplateByKeyVariable(keyVariableDTO);
        if (null == templateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_MATCH_NOT_EXIT);
        }
        log.info("2、协议出具匹配模板数据：========" + contractSignId + templateLoadEntity.getTtCode());
        //日志--匹配模板
        templateLoadEntity.setId(null)
                .setType(TemplateLoadTypeEnum.TEMPLATE_MATCH.getValue())
                .setMemo(TemplateLoadTypeEnum.TEMPLATE_MATCH.getDesc())
                .setTemplateId(templateEntity.getId())
                .setTemplateCode(templateEntity.getCode())
                .setTemplateName(templateEntity.getName())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_MATCH) + "模板ID:" + templateEntity.getId()
                        + ",\n模板编码：" + templateEntity.getCode() + ",\n模板名称：" + templateEntity.getName()
                        + ",\n模板版本：" + templateEntity.getVersion() + "\n关键变量：" + FastJsonUtils.getBeanToJson(templateDTO.getKeyVariableDTO()));
        templateLoadService.saveTemplateLoad(templateLoadEntity);

        //3、规则引擎的条件变量数据Map
        DroolsRuleBizInfoDTO droolsRuleBizInfoDTO = templateContentBuilder.convertBizParameter(templateDTO);
        //4、规则匹配条款组(templateEntity.setGroupList())
        List<String> resultRealGroupCodeList = this.matchGroupList(droolsRuleBizInfoDTO, templateEntity, templateLoadEntity);
        if (CollectionUtils.isEmpty(resultRealGroupCodeList)) {
            return Result.success(templateEntity);
        }
//        log.info("4、协议出具规则匹配条款组数据：========" + contractSignId + templateLoadEntity.getTtCode() + "关联条款组：" + resultRealGroupCodeList);
        log.info("4、协议出具规则匹配条款组数据：========" + contractSignId + templateLoadEntity.getTtCode());
        //5、规则匹配条款
        List<String> templateCodeList = new ArrayList<>();
        if (DisableStatusEnum.ENABLE.getValue().equals(templateEntity.getBindTemplate())) {
            List<TemplateBindEntity> templateBindEntityList = templateBindDao.getTemplateBindByCode(templateEntity.getCode(), "");
            templateCodeList = templateBindEntityList.stream().map(TemplateBindEntity::getBindTemplateCode).distinct().collect(Collectors.toList());
        } else {
            templateCodeList = Arrays.asList(templateEntity.getCode());
        }
        List<TemplateItemRelationEntity> itemRelationEntityList = templateItemRelationDao.getItemRelationByTemplateCode(templateCodeList);
        List<String> templateItemCodeList = itemRelationEntityList.stream().map(TemplateItemRelationEntity::getTemplateItemCode).distinct().collect(Collectors.toList());
        List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByGroupCodeList(resultRealGroupCodeList);
//        List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByKeyVariable(keyVariableDTO, resultGroupCodeList);
        if (CollectionUtils.isEmpty(itemEntityList) || CollectionUtils.isEmpty(templateItemCodeList)) {
            //日志记录--5、匹配条款组
            templateLoadEntity.setId(null)
                    .setType(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getValue())
                    .setMemo(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getDesc())
                    .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH) + "所有关联条款为空");
            templateLoadService.saveTemplateLoad(templateLoadEntity);
            return Result.success(templateEntity);
        }
//        Map<String, List<TemplateItemEntity>> templateGroupItemMap = itemEntityList.stream().collect(Collectors.groupingBy(TemplateItemEntity::getTemplateGroupCode));
        log.info("5、协议出具条款数据：========" + contractSignId + "TT编码：" + templateLoadEntity.getTtCode());
        //8、条款-规则匹配满足条件的条款
//        List<TemplateItemEntity> resultItemEntityList = this.matchItemRuleList(droolsRuleBizInfoDTO, templateLoadEntity, itemEntityList, itemCustomerEntityList);
        List<TemplateItemEntity> templateItemEntityList = itemEntityList.stream().filter(itemEntity -> templateItemCodeList.contains(itemEntity.getCode())).collect(Collectors.toList());
        List<TemplateItemEntity> resultItemEntityList = this.matchItemRuleList(droolsRuleBizInfoDTO, keyVariableDTO, templateLoadEntity, itemEntityList, templateItemEntityList);
        //9、条款-内容业务数据渲染
        return Result.success(this.assemblyItemListContent(templateDTO, templateLoadEntity, templateEntity, resultItemEntityList));
    }

    /**
     * 组装条款具体内容
     *
     * @param templateDTO          业务基础信息
     * @param templateLoadEntity   加载日志记录
     * @param templateEntity       关联模板数据返回信息
     * @param resultItemEntityList 关联的条款
     */
    private TemplateEntity assemblyItemListContent(SignHuskyTemplateDTO templateDTO, TemplateLoadEntity templateLoadEntity,
                                                   TemplateEntity templateEntity, List<TemplateItemEntity> resultItemEntityList) {
        Map<String, List<TemplateItemEntity>> itemMap = resultItemEntityList.stream().collect(Collectors.groupingBy(TemplateItemEntity::getTemplateGroupCode));
        DroolsRuleBizInfoDTO ruleBizInfoDTO = templateContentBuilder.convertAllParameterMap(templateDTO);
        String errorInfo = "";
        int groupSort = 0;
        for (TemplateGroupEntity templateGroupEntity : templateEntity.getGroupList()) {
            if (DisableStatusEnum.ENABLE.getValue().equals(templateGroupEntity.getNeedNum())) {
                groupSort += 1;
            }
            if (pageSplitGroupCode.equals(templateGroupEntity.getRealGroupCode())) {
                groupSort = 0;
            }
            templateGroupEntity.setSort(groupSort);
            List<TemplateItemEntity> itemGroupEntityList = itemMap.get(templateGroupEntity.getRealGroupCode());
            if (!CollectionUtils.isEmpty(itemGroupEntityList)) {
                itemGroupEntityList.sort(Comparator.comparing(TemplateItemEntity::getSort).thenComparing(TemplateItemEntity::getId));
                int itemSort = 0;
                for (TemplateItemEntity itemEntity : itemGroupEntityList) {
                    if (DisableStatusEnum.ENABLE.getValue().equals(itemEntity.getNeedNum())) {
                        itemSort += 1;
                    }
                    itemEntity.setSort(itemSort);
                    String bizContent = "";
                    try {
                        bizContent = TemplateRenderUtil.templateRender(ruleBizInfoDTO.getMapBizData(), itemEntity.getContent());
                    } catch (Exception e) {
                        errorInfo = errorInfo + "条款Code:" + itemEntity.getCode() + "\n条款Name:" + itemEntity.getName() + "\n条款Content:" + itemEntity.getContent() +
                                "；\n报错信息: " + e.getMessage();
                        log.error(e.getMessage());
                    }
                    itemEntity.setContentInfo("<!-- 模板ID-" + templateEntity.getId() + ",模板Code-" + templateEntity.getCode() + ",模板Name-" + templateEntity.getName() + "-->"
                            + "<!-- 条款组加载规则-" + (StringUtils.isNotBlank(templateGroupEntity.getRuleInfo()) ? templateGroupEntity.getRuleInfo() : "无") + "-->"
                            + "<!-- 条款组ID-" + templateGroupEntity.getId() + ",条款组取值RealCode-" + templateGroupEntity.getRealGroupCode() + ",条款组Code-" + templateGroupEntity.getCode() + ",条款组Name-" + templateGroupEntity.getName() + "-->"
                            + "<!-- 条款加载规则-" + (StringUtils.isNotBlank(itemEntity.getRuleInfo()) ? itemEntity.getRuleInfo() : "无") + "-->"
                            + "<!-- 条款ID-" + itemEntity.getId() + ",条款Code-" + itemEntity.getCode() + ",条款Name-" + itemEntity.getName() + ",条款排序-" + itemSort + "-->"
                            + bizContent);
                }
                templateGroupEntity.setItemList(itemGroupEntityList);
            }
        }
        //转让、分配、回购，都是101新增
        if (Arrays.asList(ContractTradeTypeEnum.NEW.getValue(), ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                        ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(), ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue())
                .contains(templateDTO.getKeyVariableDTO().getContractActionType())) {
            this.processStandardFile(templateDTO.getStandardFileId(), templateEntity);
        }

        //日志记录--4、匹配条款组
        templateLoadEntity.setId(null)
                .setType(TemplateLoadTypeEnum.TEMPLATE_BIZ_DATA.getValue())
                .setMemo(TemplateLoadTypeEnum.TEMPLATE_BIZ_DATA.getDesc())
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_BIZ_DATA) + FastJsonUtils.getBeanToJson(templateEntity))
                .setBizData(FastJsonUtils.getBeanToJson(ruleBizInfoDTO))
                .setErrorInfo(StringUtils.isNotBlank(errorInfo) ? "异常日志：\n" + errorInfo : "");
        templateLoadService.saveTemplateLoad(templateLoadEntity);
        return templateEntity;
    }

    /**
     * 校验企标文件内容
     *
     * @param standardFileId
     * @param templateEntity
     */
    private void processStandardFile(Integer standardFileId, TemplateEntity templateEntity) {
        if (null != standardFileId && standardFileId > 0) {
            SystemRuleItemEntity standardFileItemEntity = systemRuleFacade.getRuleItemById(standardFileId);
            if (null != standardFileItemEntity) {
                TemplateItemEntity itemEntity = new TemplateItemEntity()
                        .setCode("E_standardFile(" + standardFileItemEntity.getRuleKey() + ")_" + standardFileId)
                        .setName("企标文件")
                        .setContent(standardFileItemEntity.getRuleValue())
                        //加分页条款
                        .setContentInfo("<div style=\"page-break-after:always;\"></div>" + standardFileItemEntity.getRuleValue())
                        .setSort(1)
                        .setCanModify(1)
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                TemplateGroupEntity templateGroupEntity = new TemplateGroupEntity()
                        .setCode("M_standardFile(" + standardFileItemEntity.getRuleKey() + ")")
//                        .setTitle("特油_企标文件_"+standardFileItemEntity.getRuleKey())
                        .setName("特油_企标文件(" + standardFileItemEntity.getRuleKey() + ")")
                        .setSort(1000)
                        .setTitle("")
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                        .setNeedNum(0)
                        .setItemList(Arrays.asList(itemEntity));
                templateEntity.getGroupList().add(templateGroupEntity);
            }
        }
    }

    private List<TemplateItemEntity> matchItemRuleList(DroolsRuleBizInfoDTO droolsRuleBizInfoDTO,
                                                       KeyVariableDTO keyVariableDTO,
                                                       TemplateLoadEntity templateLoadEntity,
                                                       List<TemplateItemEntity> allItemEntityList,
                                                       List<TemplateItemEntity> templateItemList) {
        //1、规则匹配条款
        List<String> allItemCodeList = allItemEntityList.stream()
                .map(TemplateItemEntity::getCode)
                .collect(Collectors.toList());
        //2、关联模板的条款
        List<String> templateItemCodeList = templateItemList.stream()
                .map(TemplateItemEntity::getCode)
                .collect(Collectors.toList());
        List<TemplateItemEntity> itemCustomerEntityList = templateItemList;
//        Map<String, List<TemplateItemEntity>> templateGroupItemMap = templateItemList.stream().collect(Collectors.groupingBy(TemplateItemEntity::getTemplateGroupCode));
        //3、满足特殊客户+通用 后的条款（以条款组为基准）
//        List<TemplateItemEntity> itemCustomerEntityList = new ArrayList<>();
//        for (String groupCode : templateGroupItemMap.keySet()) {
//            List<TemplateItemEntity> groupItemList = templateGroupItemMap.get(groupCode);
//            List<TemplateItemEntity> groupEnterpriseItemList = groupItemList.stream()
//                    .filter(itemEntity -> keyVariableDTO.getEnterpriseCode().equals(itemEntity.getEnterpriseCode()) && StringUtils.isNotBlank(itemEntity.getEnterpriseCode()))
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(groupEnterpriseItemList)) {
//                groupItemList = groupItemList.stream().filter(itemEntity -> StringUtils.isBlank(itemEntity.getEnterpriseCode())).collect(Collectors.toList());
//            itemCustomerEntityList.addAll(groupItemList);
//            } else {
//                itemCustomerEntityList.addAll(groupEnterpriseItemList);
//            }
//            List<TemplateItemEntity> groupCustomerItemList = groupItemList.stream()
//                    .filter(itemEntity -> itemEntity.getCustomerCode().contains(keyVariableDTO.getCustomerCode() + ","))
//                    .collect(Collectors.toList());
//            itemCustomerEntityList.addAll(CollectionUtils.isEmpty(groupCustomerItemList) ? groupItemList : groupCustomerItemList);
//        }
        //4、关联模板且匹配客户的条款编码
        List<String> itemCustomerCodeList = itemCustomerEntityList.stream()
                .map(TemplateItemEntity::getCode)
                .collect(Collectors.toList());
        //5、规则匹配条款
        List<TemplateItemEntity> needRuleItemList = itemCustomerEntityList.stream()
                .filter(it -> {
                    return StringUtils.isNotBlank(it.getRuleInfo());
                })
                .collect(Collectors.toList());
        //todo:规则放开
        List<String> needRuleItemCodeList = needRuleItemList.stream().map(TemplateItemEntity::getCode).collect(Collectors.toList());
//        List<String> matchItemCodeList = allItemCodeList;
        List<TemplateItemEntity> resultItemEntityList = new ArrayList<>();
        try {
            List<String> matchItemCodeList = templateContentBuilder.matchItem(needRuleItemList, droolsRuleBizInfoDTO);
            resultItemEntityList = itemCustomerEntityList.stream()
                    .filter(it -> {
                        return matchItemCodeList.contains(it.getCode()) || StringUtils.isBlank(it.getRuleInfo());
                    })
                    .sorted(Comparator.comparing(TemplateItemEntity::getSort).thenComparing(TemplateItemEntity::getId))
                    .collect(Collectors.toList());
            log.info("6、协议出具规则匹配条款数据：========" + templateLoadEntity.getContractSignId() + templateLoadEntity.getTtCode());
            List<String> resultItemCodeList = resultItemEntityList.stream().map(TemplateItemEntity::getCode).collect(Collectors.toList());
            //日志记录--6、匹配条款组
            templateLoadEntity.setId(null)
                    .setType(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getValue())
                    .setMemo(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getDesc())
                    .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH) +
                            "1、所有关联条款：" + this.jointTemplateCodeList(allItemCodeList)
                            + "；\n2、关联模板的条款：" + this.jointTemplateCodeList(templateItemCodeList)
//                            + "；\n3、关联模板且满足特殊集团客户的条款：" + this.jointTemplateCodeList(itemCustomerCodeList)
                            + "；\n4、需要匹配规则条款：" + this.jointTemplateCodeList(needRuleItemCodeList)
                            + "；\n5、匹配规则命中条款：" + this.jointTemplateCodeList(matchItemCodeList) +
                            "；\n6、规则未命中条款：" + this.jointTemplateCodeList(CommonListUtil.getDifferences(needRuleItemCodeList, matchItemCodeList))
                            + "；\n7、最终匹配条款结果：" + this.jointTemplateCodeList(resultItemCodeList));
            templateLoadService.saveTemplateLoad(templateLoadEntity);
            return resultItemEntityList;
        } catch (Exception e) {
            templateLoadEntity.setId(null)
                    .setType(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getValue())
                    .setMemo(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH.getDesc())
                    .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_ITEM_MATCH) +
                            "1、所有关联条款：" + this.jointTemplateCodeList(allItemCodeList)
//                        + "；\n2、满足特殊客户的条款：" + this.jointTemplateCodeList(templateItemCodeList)
                            + "；\n2、绑定模板的条款：" + this.jointTemplateCodeList(templateItemCodeList)
                            + "；\n3、需要匹配规则条款：" + this.jointTemplateCodeList(needRuleItemCodeList))
                    .setErrorInfo("条款规则匹配异常：" + e.toString());
            templateLoadService.saveTemplateLoad(templateLoadEntity);
            return resultItemEntityList;
        }

    }

    @Override
    public Result matchTemplateByRuleV2(Integer contractSignId) {
        //1、业务参数信息+关键变量+条件变量
        Result result = contractSignFacade.buildHuskySignTemplateDTOV2(contractSignId);
        if (!result.isSuccess()) {
            return result;
        }
        SignHuskyTemplateDTO templateDTO = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), SignHuskyTemplateDTO.class);
        log.info("1、协议出具模板业务数据：========" + FastJsonUtils.getBeanToJson(templateDTO));
        //关键变量
        KeyVariableDTO keyVariableDTO = templateDTO.getKeyVariableDTO();
        //2、匹配合同模板
        TemplateEntity templateEntity = templateDao.getTemplateByKeyVariable(keyVariableDTO);
        if (null == templateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_MATCH_NOT_EXIT);
        }
        log.info("2、协议出具匹配模板数据：========" + FastJsonUtils.getBeanToJson(templateEntity));
        //规则引擎的条件变量数据Map
        DroolsRuleBizInfoDTO ruleBizInfoDTO = templateContentBuilder.convertAllParameterMap(templateDTO);
        //3、规则匹配条款组(templateEntity.setGroupList())
        List<String> resultGroupCodeList = this.matchGroupListV2(ruleBizInfoDTO, templateEntity);
        if (CollectionUtils.isEmpty(resultGroupCodeList)) {
            return Result.success(templateEntity);
        }
        log.info("4、协议出具规则匹配条款组数据：========" + FastJsonUtils.getBeanToJson(templateEntity.getGroupList()));
        return Result.success(matchItemList(keyVariableDTO, templateEntity, ruleBizInfoDTO, resultGroupCodeList));
    }

    @Override
    public String provideContractSign(TemplateEntity templateEntity) {
        String templateHtml = templateEntity.getTemplateHtml();
        //todo:001
        String styleItemCodeView = "Clause_0001";
        List<TemplateItemEntity> itemEntityList = templateItemDao.getTemplateItemByCode(styleItemCode, null);
        if (!CollectionUtils.isEmpty(itemEntityList)) {
            templateHtml = templateHtml.replaceAll("#" + styleItemCodeView + "#", Matcher.quoteReplacement(itemEntityList.get(0).getMemo()));
            templateEntity.setTemplateHtml(templateHtml);
        }
        return templateHtml;
    }

    @Override
    public Result checkTemplateByRule(Integer contractSignId) {
        //1、获取：业务参数信息+关键变量+条件变量
        Result result = contractSignFacade.buildHuskySignTemplateDTOV2(contractSignId);
        if (!result.isSuccess()) {
            return Result.success(false);
        }
        SignHuskyTemplateDTO templateDTO = FastJsonUtils.getJsonToBean(JSON.toJSONString(result.getData()), SignHuskyTemplateDTO.class);
        log.info("1、协议出具模板业务数据：========" + contractSignId);
        //关键变量
        KeyVariableDTO keyVariableDTO = templateDTO.getKeyVariableDTO();
        //2、匹配合同模板
        TemplateEntity templateEntity = templateDao.getTemplateByKeyVariable(keyVariableDTO);
        if (null == templateEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_MATCH_NOT_EXIT);
        }
        List<TemplateCheckEntity> checkEntityList = templateCheckService.getCheckListByTemplateCode(templateEntity.getCode());
        if (CollectionUtils.isEmpty(checkEntityList)) {
            return Result.success(false);
        }
        List<TemplateCheckEntity> fullCheckList = checkEntityList.stream().filter(checkEntity -> {
            return StringUtils.isBlank(checkEntity.getRuleInfo());
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(fullCheckList)) {
            return Result.success(true);
        }
        List<TemplateCheckEntity> needRuleCheckList = checkEntityList.stream().filter(checkEntity -> {
            return StringUtils.isNotBlank(checkEntity.getRuleInfo());
        }).collect(Collectors.toList());
        //3、规则引擎的条件变量数据Map
        DroolsRuleBizInfoDTO droolsRuleBizInfoDTO = templateContentBuilder.convertBizParameter(templateDTO);
        List<String> ruleGroupCodeList = templateContentBuilder.matchTemplateCheck(needRuleCheckList, droolsRuleBizInfoDTO);
        if (!CollectionUtils.isEmpty(ruleGroupCodeList)) {
            return Result.success("命中规则ID:" + StringUtils.join(ruleGroupCodeList, ",")
                    + "模板编号：" + templateEntity.getCode() + "模板名称：" + templateEntity.getName(), true);
        }
        return Result.success(false);
    }

    /**
     * 规则匹配条款组
     *
     * @param droolsRuleBizInfoDTO
     * @param templateEntity
     * @return
     */
    private List<String> matchGroupListV2(DroolsRuleBizInfoDTO droolsRuleBizInfoDTO,
                                          TemplateEntity templateEntity) {
        //找关联的条款组
        List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode("", templateEntity.getCode());
        if (CollectionUtils.isEmpty(groupRelationEntityList)) {
            return new ArrayList<>();
        }
        Map<String, List<TemplateGroupRelationEntity>> groupRelationMap = groupRelationEntityList.stream().collect(Collectors.groupingBy(TemplateGroupRelationEntity::getTemplateGroupCode));

        List<String> groupCodeList = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateGroupCode).distinct().collect(Collectors.toList());
        List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(groupCodeList, DisableStatusEnum.ENABLE.getValue());
        log.info("3、协议出具条款组数据：========" + FastJsonUtils.getBeanToJson(groupEntityList));
        //需要匹配规则的条款组
        List<TemplateGroupEntity> needMatchRuleGroupList = groupEntityList.stream().filter(it -> {
            return StringUtils.isNotBlank(it.getRuleInfo());
        }).collect(Collectors.toList());
//        List<String> resultGroupCodeList = needMatchRuleGroupList.stream().map(TemplateGroupEntity::getCode).collect(Collectors.toList());
        List<String> resultGroupCodeList = templateContentBuilder.matchGroup(needMatchRuleGroupList, droolsRuleBizInfoDTO);
        List<TemplateGroupEntity> groupResultList = groupEntityList.stream().filter(it -> {
            //规则匹配命中的 + 无需规则匹配的条款
            return resultGroupCodeList.contains(it.getCode()) || StringUtils.isBlank(it.getRuleInfo());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupResultList)) {
            templateEntity.setGroupList(new ArrayList<>());
            return resultGroupCodeList;
        }
        groupResultList.forEach(it -> {
            it.setSort(groupRelationMap.get(it.getCode()).get(0).getSort());
        });
        groupResultList.sort(Comparator.comparing(TemplateGroupEntity::getSort).thenComparing(TemplateGroupEntity::getId));
        templateEntity.setGroupList(groupResultList);
        return groupResultList.stream().map(TemplateGroupEntity::getCode).collect(Collectors.toList());
    }


    /**
     * 规则匹配条款
     *
     * @param keyVariableDTO
     * @param templateEntity
     * @param droolsRuleBizInfoDTO
     * @param resultGroupCodeList
     * @return
     */
    private TemplateEntity matchItemList(KeyVariableDTO keyVariableDTO, TemplateEntity templateEntity,
                                         DroolsRuleBizInfoDTO droolsRuleBizInfoDTO, List<String> resultGroupCodeList) {
//        List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByKeyVariable(keyVariableDTO, resultGroupCodeList);
        List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByGroupCodeList(resultGroupCodeList);
        if (CollectionUtils.isEmpty(itemEntityList)) {
            return templateEntity;
        }
        log.info("5、协议出具条款数据：========" + FastJsonUtils.getBeanToJson(itemEntityList));
        //规则匹配条款
        List<TemplateItemEntity> needRuleItemList = itemEntityList.stream()
                .filter(it -> {
                    return StringUtils.isNotBlank(it.getRuleInfo());
                })
                .collect(Collectors.toList());
//        List<String> matchItemCodeList = needRuleItemList.stream().map(TemplateItemEntity::getCode).collect(Collectors.toList());
        List<String> matchItemCodeList = templateContentBuilder.matchItem(needRuleItemList, droolsRuleBizInfoDTO);
        List<TemplateItemEntity> resultItemEntityList = itemEntityList.stream()
                .filter(it -> {
                    return matchItemCodeList.contains(it.getCode()) || StringUtils.isBlank(it.getRuleInfo());
                })
                .sorted(Comparator.comparing(TemplateItemEntity::getSort).thenComparing(TemplateItemEntity::getId))
                .collect(Collectors.toList());
        log.info("6、协议出具规则匹配条款数据：========" + FastJsonUtils.getBeanToJson(resultItemEntityList));
        List<String> resultItemCodeList = resultItemEntityList.stream().map(TemplateItemEntity::getCode).collect(Collectors.toList());
        Map<String, List<TemplateItemEntity>> itemMap = resultItemEntityList.stream().collect(Collectors.groupingBy(TemplateItemEntity::getTemplateGroupCode));
        try {
            int groupSort = 0;
            for (TemplateGroupEntity templateGroupEntity : templateEntity.getGroupList()) {
                if (DisableStatusEnum.ENABLE.getValue().equals(templateGroupEntity.getNeedNum())) {
                    groupSort += 1;
                }
                templateGroupEntity.setSort(groupSort);
                List<TemplateItemEntity> itemGroupEntityList = itemMap.get(templateGroupEntity.getCode());
                if (!CollectionUtils.isEmpty(itemGroupEntityList)) {
                    itemGroupEntityList.sort(Comparator.comparing(TemplateItemEntity::getSort).thenComparing(TemplateItemEntity::getId));
                    int itemSort = 0;
                    for (TemplateItemEntity itemEntity : itemGroupEntityList) {
                        String bizContent = TemplateRenderUtil.templateRender(droolsRuleBizInfoDTO.getMapBizData(), itemEntity.getContent());
                        itemEntity.setConditionInfo(bizContent);
                        if (DisableStatusEnum.ENABLE.getValue().equals(itemEntity.getNeedNum())) {
                            itemSort += 1;
                        }
                        itemEntity.setSort(itemSort);
                    }
                    templateGroupEntity.setItemList(itemGroupEntityList);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return templateEntity;
    }

    /**
     * 拼接条款/条款组编码
     *
     * @param templateCodeList
     * @return
     */
    private String jointTemplateCodeList(List<String> templateCodeList) {
        String templateCodeInfo = "无";
        if (CollectionUtils.isEmpty(templateCodeList)) {
            return templateCodeInfo;
        }
        templateCodeInfo = templateCodeList.size() + "条,'" + StringUtils.join(templateCodeList, "','") + "'";
        return templateCodeInfo;
    }

    /**
     * 模板匹配操作信息拼接
     *
     * @param templateLoadTypeEnum
     * @return
     */
    private String getTemplateLoadAct(TemplateLoadTypeEnum templateLoadTypeEnum) {
        return "(" + templateLoadTypeEnum.getValue() + ")" + templateLoadTypeEnum.getDesc() + "\n";
    }

    /**
     * 规则匹配条款组
     *
     * @param droolsRuleBizInfoDTO 规则匹配基本信息
     * @param templateEntity       关联模板
     * @param templateLoadEntity   出具加载日志实体
     * @return 匹配的条款组编码集合
     */
    private List<String> matchGroupList(DroolsRuleBizInfoDTO droolsRuleBizInfoDTO,
                                        TemplateEntity templateEntity,
                                        TemplateLoadEntity templateLoadEntity) {
        //找关联的条款组
        List<TemplateGroupRelationEntity> groupRelationEntityList = getTemplateGroupRelationList(templateEntity);
        if (CollectionUtils.isEmpty(groupRelationEntityList)) {
            //日志记录--2、匹配条款组
            templateLoadEntity.setId(null)
                    .setType(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getValue())
                    .setMemo(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getDesc())
                    .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH) + "：关联条款组为空");
            templateLoadService.saveTemplateLoad(templateLoadEntity);
            return new ArrayList<>();
        }
        Map<String, List<TemplateGroupRelationEntity>> groupRelationMap = groupRelationEntityList.stream().collect(Collectors.groupingBy(TemplateGroupRelationEntity::getTemplateGroupCode));

        List<String> groupCodeList = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateGroupCode).distinct().collect(Collectors.toList());
        List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCodeList(groupCodeList, DisableStatusEnum.ENABLE.getValue());
//        log.info("3、协议出具条款组数据：========" + FastJsonUtils.getBeanToJson(groupEntityList));
        log.info("3、协议出具条款组数据：========");
        //需要匹配规则的条款组
        List<TemplateGroupEntity> needMatchRuleGroupList = groupEntityList.stream().filter(it -> {
            return StringUtils.isNotBlank(it.getRuleInfo());
        }).collect(Collectors.toList());
        //todo:规则放开
        List<String> needRuleGroupCodeList = needMatchRuleGroupList.stream().map(TemplateGroupEntity::getCode).collect(Collectors.toList());
        String errorException = "";
        List<TemplateGroupEntity> groupResultList = new ArrayList<>();
        try {
            List<String> ruleGroupCodeList = templateContentBuilder.matchGroup(needMatchRuleGroupList, droolsRuleBizInfoDTO);
            //加载日志记录
            templateLoadEntity.setContent("1、所有关联条款组：" + this.jointTemplateCodeList(groupCodeList) +
                    "；\n2、需要匹配规则条款组：" + this.jointTemplateCodeList(needRuleGroupCodeList) +
                    "；\n3、匹配规则命中条款组：" + this.jointTemplateCodeList(ruleGroupCodeList) +
                    "；\n4、规则未命中条款组：" + this.jointTemplateCodeList(CommonListUtil.getDifferences(needRuleGroupCodeList, ruleGroupCodeList)));
            groupResultList = groupEntityList.stream().filter(it -> {
                //规则匹配命中的 + 无需规则匹配的条款
                return ruleGroupCodeList.contains(it.getCode()) || StringUtils.isBlank(it.getRuleInfo());
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupResultList)) {
                templateEntity.setGroupList(new ArrayList<>());
                return ruleGroupCodeList;
            }
        } catch (Exception e) {
            errorException = "条款组规则匹配异常：" + e.toString();
            log.info(errorException);
            templateLoadEntity.setId(null)
                    .setType(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getValue())
                    .setMemo(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getDesc())
                    .setBizData(FastJsonUtils.getBeanToJson(droolsRuleBizInfoDTO))
                    .setContent("1、所有关联条款组：" + this.jointTemplateCodeList(groupCodeList) +
                            "；\n2、需要匹配规则条款组：" + this.jointTemplateCodeList(needRuleGroupCodeList))
                    .setErrorInfo(errorException);
            templateLoadService.saveTemplateLoad(templateLoadEntity);
            return new ArrayList<>();
        }

        groupResultList.forEach(it -> {
            it.setSort(groupRelationMap.get(it.getCode()).get(0).getSort());
        });
        groupResultList.sort(Comparator.comparing(TemplateGroupEntity::getSort).thenComparing(TemplateGroupEntity::getId));
        templateEntity.setGroupList(groupResultList);
        List<String> resultGroupCodeList = groupResultList.stream().map(TemplateGroupEntity::getCode).collect(Collectors.toList());
        List<String> realGroupCodeList = groupResultList.stream().map(TemplateGroupEntity::getRealGroupCode).collect(Collectors.toList());

        //日志记录--2、匹配条款组
        templateLoadEntity.setId(null)
                .setType(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getValue())
                .setMemo(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH.getDesc())
                .setBizData(FastJsonUtils.getBeanToJson(droolsRuleBizInfoDTO))
                .setContent(this.getTemplateLoadAct(TemplateLoadTypeEnum.TEMPLATE_GROUP_MATCH) + templateLoadEntity.getContent()
                        + "；\n5、最终匹配条款组结果：" + this.jointTemplateCodeList(resultGroupCodeList)
                        + "；\n5、最终匹配条款组-RealGroupCode结果：" + this.jointTemplateCodeList(realGroupCodeList)
                        + "；\n6、条款组数据详情：" + FastJsonUtils.getBeanToJson(groupResultList));
        templateLoadService.saveTemplateLoad(templateLoadEntity);

        return realGroupCodeList;
    }

    private List<TemplateGroupRelationEntity> getTemplateGroupRelationList(TemplateEntity templateEntity) {
        List<TemplateGroupRelationEntity> groupRelationEntityList = new ArrayList<>();
        if (DisableStatusEnum.ENABLE.getValue().equals(templateEntity.getBindTemplate())) {
            //如果为组装模板绑定
            List<TemplateGroupRelationEntity> bindTemplateGroupList = new ArrayList<>();
            List<TemplateBindEntity> templateBindEntityList = templateBindDao.getTemplateBindByCode(templateEntity.getCode(), "");
            if (!CollectionUtils.isEmpty(templateBindEntityList)) {
                for (TemplateBindEntity templateBindEntity : templateBindEntityList) {
                    List<TemplateGroupRelationEntity> templateGroupList = templateGroupRelationDao.getTemplateByCode("", templateBindEntity.getBindTemplateCode());
                    if (!CollectionUtils.isEmpty(templateGroupList)) {
                        if (templateBindEntityList.indexOf(templateBindEntity) != 0) {
                            // 如果不是第一个模板，则在前面加分页符
                            bindTemplateGroupList.add(new TemplateGroupRelationEntity()
                                    .setTemplateCode(templateEntity.getCode())
                                    .setSort(0)
                                    .setTemplateGroupCode(pageSplitGroupCode)
                                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                            );
                        }
                        bindTemplateGroupList.addAll(templateGroupList);
                    }
                }
                if (!CollectionUtils.isEmpty(bindTemplateGroupList)) {
                    for (TemplateGroupRelationEntity groupRelationEntity : bindTemplateGroupList) {
                        groupRelationEntity.setSort(bindTemplateGroupList.indexOf(groupRelationEntity) + 1);
                        groupRelationEntityList.add(groupRelationEntity);
                    }
                }
            }
        } else {
            groupRelationEntityList = templateGroupRelationDao.getTemplateByCode("", templateEntity.getCode());
        }
        return groupRelationEntityList;
    }
}
