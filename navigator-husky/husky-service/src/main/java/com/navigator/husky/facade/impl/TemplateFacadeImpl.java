package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateFacade;
import com.navigator.husky.pojo.dto.CopyTemplateDTO;
import com.navigator.husky.pojo.dto.MarkMainVersionDTO;
import com.navigator.husky.pojo.dto.TemplateItemRelationDTO;
import com.navigator.husky.pojo.dto.TemplateRelationSyncDTO;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.VHuskyTemplateEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.pojo.vo.HealthCheckVO;
import com.navigator.husky.pojo.vo.TemplateItemRelationVO;
import com.navigator.husky.service.ITemplateService;
import com.navigator.husky.service.VHuskyTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 16:20
 **/
@RestController
public class TemplateFacadeImpl implements TemplateFacade {
    @Autowired
    private ITemplateService templateService;
    @Autowired
    private VHuskyTemplateService huskyTemplateService;

    @Override
    public Result queryByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        return templateService.queryByCondition(queryDTO);
    }

    @Override
    public Result<Boolean> saveTemplate(TemplateEntity templateEntity) {
        return Result.success(templateService.saveTemplate(templateEntity));
    }

    @Override
    public Result<Boolean> updateTemplate(TemplateEntity templateEntity) {
        return Result.success(templateService.updateTemplate(templateEntity));
    }

    @Override
    public Result copyTemplateInfo(CopyTemplateDTO copyTemplateDTO) {
        return templateService.copyTemplateInfo(copyTemplateDTO);
    }

    @Override
    public Result updateTemplateStatus(Integer templateId, Integer status) {
        return templateService.updateStatus(templateId, status);
    }

    @Override
    public TemplateEntity getTemplateById(Integer templateId) {
        return templateService.getTemplateById(templateId);
    }

    @Override
    public List<TemplateEntity> getAllTemplateList(Integer status) {
        return templateService.getAllTemplateList(status);
    }

    @Override
    public List<TemplateEntity> getTemplateListByGroupCode(String templateGroupCode) {
        return templateService.getTemplateListByGroupCode(templateGroupCode);
    }


    @Override
    public Result bindTemplateItemRelation(TemplateItemRelationDTO itemRelationDTO) {
        return Result.success(templateService.bindTemplateItemRelation(itemRelationDTO));
    }

    @Override
    public Result removeTemplateItemRelation(String templateCode, String templateItemCode) {
        return Result.success(templateService.removeTemplateItemRelation(templateCode, templateItemCode));
    }

    @Override
    public List<TemplateItemRelationVO> getTemplateItemRelation(String templateCode, String templateItemCode) {
        return templateService.getTemplateItemRelation(templateCode, templateItemCode);
    }

    @Override
    public Result syncTemplateRelation(List<TemplateRelationSyncDTO> templateRelationSyncList) {
        return templateService.syncTemplateRelation(templateRelationSyncList);
    }

    @Override
    public Result markMainVersion(MarkMainVersionDTO markMainVersionDTO) {
        return templateService.markMainVersion(markMainVersionDTO);
    }

    @Override
    public HealthCheckVO healthCheck(Integer templateId) {
        return templateService.healthCheck(templateId);
    }

    @Override
    public Result exportTemplateExcel(QueryTemplateQO templateQO) {
        return huskyTemplateService.exportTemplateExcel(templateQO);
    }

    @Override
    public Result exportTemplateJson(QueryTemplateQO templateQO) {
        return templateService.exportTemplateJson(templateQO);
    }

    @Override
    public Result importTemplateJson(MultipartFile file) {
        return templateService.importTemplateJson(file);
    }
}
