package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateItemMapper;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateItemDao extends BaseDaoImpl<TemplateItemMapper, TemplateItemEntity> {

    public IPage<TemplateItemEntity> queryByCondition(QueryDTO<QueryTemplateQO> queryDTO, List<String> filterCodeListByTemplate) {
        QueryTemplateQO templateQO = queryDTO.getCondition();
        if (StringUtils.isNotBlank(templateQO.getTemplateCode()) && CollectionUtils.isEmpty(filterCodeListByTemplate)) {
            return this.page(new Page<>(0, 0, 0));
        }
        LambdaQueryWrapper<TemplateItemEntity> queryWrapper = getItemWrapperByCondition(filterCodeListByTemplate, templateQO);
        queryWrapper.orderByDesc(TemplateItemEntity::getId);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<TemplateItemEntity> queryItemList(QueryTemplateQO templateQO, List<String> filterCodeListByTemplate) {
        if (StringUtils.isNotBlank(templateQO.getTemplateCode()) && CollectionUtils.isEmpty(filterCodeListByTemplate)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TemplateItemEntity> queryWrapper = getItemWrapperByCondition(filterCodeListByTemplate, templateQO);
        queryWrapper.orderByDesc(TemplateItemEntity::getId);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<TemplateItemEntity> getItemWrapperByCondition(List<String> filterCodeListByTemplate, QueryTemplateQO templateQO) {
        if (StringUtils.isNotBlank(templateQO.getUpdatedBy())) {
            templateQO.setUpdatedBy(templateQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<TemplateItemEntity> queryWrapper = new LambdaQueryWrapper<TemplateItemEntity>()
                .eq(StringUtils.isNotBlank(templateQO.getTemplateGroupCode()), TemplateItemEntity::getTemplateGroupCode, templateQO.getTemplateGroupCode())
                .eq(null != templateQO.getStatus(), TemplateItemEntity::getStatus, templateQO.getStatus())
                .eq(StringUtils.isNotBlank(templateQO.getEnterpriseCode()), TemplateItemEntity::getEnterpriseCode, templateQO.getEnterpriseCode())
                .eq(null != templateQO.getMainVersionStatus(), TemplateItemEntity::getMainVersionStatus, templateQO.getMainVersionStatus())
                .eq(null != templateQO.getIsFixed(), TemplateItemEntity::getIsFixed, templateQO.getIsFixed())
                .in(StringUtils.isNotBlank(templateQO.getTemplateCode()), TemplateItemEntity::getCode, filterCodeListByTemplate)
                .in(!CollectionUtils.isEmpty(templateQO.getItemIdList()), TemplateItemEntity::getId, templateQO.getItemIdList())
                .like(StringUtils.isNotBlank(templateQO.getContent()), TemplateItemEntity::getContent, templateQO.getContent().trim())
                .like(StringUtils.isNotBlank(templateQO.getUpdatedBy()), TemplateItemEntity::getUpdatedBy, templateQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(templateQO.getStartDay()) && StringUtils.isNotBlank(templateQO.getEndDay()), TemplateItemEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(templateQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(templateQO.getEndDay()))
                .like(StringUtils.isNotBlank(templateQO.getBuCode()), TemplateItemEntity::getBuCode, "," + templateQO.getBuCode() + ",")
                .like(StringUtils.isNotBlank(templateQO.getCompanyCode()), TemplateItemEntity::getCompanyCode, "," + templateQO.getCompanyCode() + ",")
                .like(null != templateQO.getCategoryId(), TemplateItemEntity::getCategory2, templateQO.getCategoryId() + ",")
                .like(null != templateQO.getCategory1(), TemplateItemEntity::getCategory1, "," + templateQO.getCategory1() + ",")
                .like(null != templateQO.getCategory2(), TemplateItemEntity::getCategory2, "," + templateQO.getCategory2() + ",")
                .like(null != templateQO.getCategory3(), TemplateItemEntity::getCategory3, "," + templateQO.getCategory3() + ",")
                .like(null != templateQO.getSalesType(), TemplateItemEntity::getSalesType, "," + templateQO.getSalesType() + ",")
                .like(StringUtils.isNotBlank(templateQO.getProtocolType()), TemplateItemEntity::getProtocolType, "," + templateQO.getProtocolType() + ",")
                .like(null != templateQO.getContractActionType(), TemplateItemEntity::getContractActionType, "," + templateQO.getContractActionType() + ",")
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
//                .like(StringUtils.isNotBlank(templateQO.getCustomerCode()), TemplateItemEntity::getCustomerCode, templateQO.getCustomerCode() + ",");
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(templateQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateItemEntity::getCode, templateQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(templateQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateItemEntity::getName, templateQO.getSearchKey().trim()));
        }
        return queryWrapper;
    }

    public List<TemplateItemEntity> getTemplateItemByCode(String itemCode, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .eq(StringUtils.isNotBlank(itemCode), TemplateItemEntity::getCode, itemCode)
                .eq(null != status, TemplateItemEntity::getStatus, status)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<TemplateItemEntity> getTemplateItemByVariable(String variable) {
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .like(StringUtils.isNotBlank(variable), TemplateItemEntity::getContent, "${" + variable + "!}")
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<TemplateItemEntity> getAllItemList(Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .eq(null != status, TemplateItemEntity::getStatus, status)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public TemplateItemEntity getItemEntityByCode(String itemCode) {
        List<TemplateItemEntity> itemEntityList = this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .eq(TemplateItemEntity::getCode, itemCode)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(itemEntityList) ? null : itemEntityList.get(0);
    }

    public List<TemplateItemEntity> getTemplateItemByCodeList(List<String> itemCodeList, Integer status) {
        if (CollectionUtils.isEmpty(itemCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .in(TemplateItemEntity::getCode, itemCodeList)
                .eq(null != status, TemplateItemEntity::getStatus, status)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }


    public List<TemplateItemEntity> getItemListByGroupCode(String groupCode) {
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .eq(StringUtils.isNotBlank(groupCode), TemplateItemEntity::getTemplateGroupCode, groupCode)
                .eq(TemplateItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateItemEntity::getSort)
        );
    }

    public List<TemplateItemEntity> getItemListByGroupCodeList(List<String> groupCodeList) {
        if (CollectionUtils.isEmpty(groupCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<TemplateItemEntity>()
                .in(TemplateItemEntity::getTemplateGroupCode, groupCodeList)
                .eq(TemplateItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateItemEntity::getSort)
        );
    }

    public void syncItemIsFixed(String groupCode, Integer isFixed) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateItemEntity::getTemplateGroupCode, groupCode)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateItemEntity::getIsFixed, isFixed)
                .update();
    }

    /**
     * 同步更新条款组编码
     *
     * @param oldGroupCode 原条款组编码
     * @param newGroupCode 新条款组编码
     */
    public void syncTemplateGroupCode(String oldGroupCode, String newGroupCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateItemEntity::getTemplateGroupCode, oldGroupCode)
                .eq(TemplateItemEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateItemEntity::getTemplateGroupCode, newGroupCode)
                .set(TemplateItemEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

//    /**
//     * 根据关键变量，获取条款（不判断客户）
//     *
//     * @param keyVariableDTO 关键变量数据
//     * @param groupCodeList  所属条款组
//     * @return 满足条件的条款集合
//     */
//    public List<TemplateItemEntity> getItemListByKeyVariable(KeyVariableDTO keyVariableDTO, List<String> groupCodeList) {
//        LambdaQueryWrapper<TemplateItemEntity> queryWrapper = new LambdaQueryWrapper<TemplateItemEntity>()
//                .eq(TemplateItemEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
//                .in(!CollectionUtils.isEmpty(groupCodeList), TemplateItemEntity::getTemplateGroupCode, groupCodeList);
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getBuCode, keyVariableDTO.getBuCode() + ",")
//                .or().like(TemplateItemEntity::getBuCode, "ALL,")
//                .or().eq(TemplateItemEntity::getBuCode, ""));
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getCompanyCode, keyVariableDTO.getCompanyCode() + ",")
//                .or().like(TemplateItemEntity::getCompanyCode, "ALL,")
//                .or().eq(TemplateItemEntity::getCompanyCode, ""));
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getCategoryId, "" + keyVariableDTO.getCategoryId() + ",")
//                .or().like(TemplateItemEntity::getCategoryId, "0,")
//                .or().eq(TemplateItemEntity::getCategoryId, ""));
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getSalesType, "" + keyVariableDTO.getSalesType() + ",")
//                .or().like(TemplateItemEntity::getSalesType, "0,")
//                .or().eq(TemplateItemEntity::getSalesType, ""));
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getProtocolType, "" + keyVariableDTO.getProtocolType() + ",")
//                .or().like(TemplateItemEntity::getProtocolType, "ALL,")
//                .or().eq(TemplateItemEntity::getProtocolType, ""));
//        queryWrapper.and(QueryWrapper -> QueryWrapper.like(TemplateItemEntity::getContractActionType, "" + keyVariableDTO.getContractActionType() + ",")
//                .or().like(TemplateItemEntity::getContractActionType, "0,")
//                .or().eq(TemplateItemEntity::getContractActionType, ""));
//        return this.list(queryWrapper);
//    }
}
