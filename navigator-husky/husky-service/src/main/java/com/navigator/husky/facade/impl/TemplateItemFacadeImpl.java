package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateItemFacade;
import com.navigator.husky.pojo.dto.TemplateItemJsonDTO;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.service.TemplateItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-07 18:43
 **/
@RestController
public class TemplateItemFacadeImpl implements TemplateItemFacade {
    @Autowired
    private TemplateItemService templateItemService;

    @Override
    public Result queryItemByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        return templateItemService.queryItemByCondition(queryDTO);
    }

    @Override
    public Boolean saveTemplateItem(TemplateItemEntity itemEntity) {
        return templateItemService.saveTemplateItem(itemEntity);
    }

    @Override
    public Result updateTemplateItem(TemplateItemEntity itemEntity) {
        return Result.success(templateItemService.updateTemplateItem(itemEntity));
    }

    @Override
    public TemplateItemEntity getTemplateItemById(Integer itemId) {
        return templateItemService.getTemplateItemById(itemId);
    }

    @Override
    public List<TemplateItemEntity> getAllItemList(Integer status) {
        return templateItemService.getAllItemList(status);
    }

    @Override
    public List<TemplateItemEntity> getItemListByGroupCode(String templateGroupCode) {
        return templateItemService.getItemListByGroupCode(templateGroupCode);
    }

    @Override
    public Result exportItemExcel(QueryTemplateQO templateQO) {
        return templateItemService.exportItemExcel(templateQO);
    }

    @Override
    public Result exportItemJson(QueryTemplateQO queryTemplateQO) {
        return templateItemService.exportItemJson(queryTemplateQO);
    }

    @Override
    public Result importItemJson(MultipartFile file) {
        return templateItemService.importItemJson(file);
    }
}
