package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateGroupMapper;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateGroupDao extends BaseDaoImpl<TemplateGroupMapper, TemplateGroupEntity> {
    /**
     * 条件分页查询条款组数据
     *
     * @param queryDTO
     * @return
     */
    public IPage<TemplateGroupEntity> queryByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        QueryTemplateQO templateQO = queryDTO.getCondition();
        LambdaQueryWrapper<TemplateGroupEntity> queryWrapper = getGroupQueryWrapper(templateQO);
        queryWrapper.orderByDesc(TemplateGroupEntity::getId);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<TemplateGroupEntity> queryGroupList(QueryTemplateQO templateQO) {
        LambdaQueryWrapper<TemplateGroupEntity> queryWrapper = getGroupQueryWrapper(templateQO);
        queryWrapper.orderByDesc(TemplateGroupEntity::getId);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<TemplateGroupEntity> getGroupQueryWrapper(QueryTemplateQO templateQO) {
        if (StringUtils.isNotBlank(templateQO.getUpdatedBy())) {
            templateQO.setUpdatedBy(templateQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<TemplateGroupEntity> queryWrapper = new LambdaQueryWrapper<TemplateGroupEntity>()
                .eq(null != templateQO.getStatus(), TemplateGroupEntity::getStatus, templateQO.getStatus())
                .eq(null != templateQO.getIsFixed(), TemplateGroupEntity::getIsFixed, templateQO.getIsFixed())
                .eq(null != templateQO.getContractActionType(), TemplateGroupEntity::getContractActionType, templateQO.getContractActionType())
                .eq(StringUtils.isNotBlank(templateQO.getRealGroupCode()), TemplateGroupEntity::getRealGroupCode, templateQO.getRealGroupCode())
                .eq(null != templateQO.getMainVersionStatus(), TemplateGroupEntity::getMainVersionStatus, templateQO.getMainVersionStatus())
                .like(StringUtils.isNotBlank(templateQO.getUpdatedBy()), TemplateGroupEntity::getUpdatedBy, templateQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(templateQO.getStartDay()) && StringUtils.isNotBlank(templateQO.getEndDay()), TemplateGroupEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(templateQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(templateQO.getEndDay()))
                .in(!CollectionUtils.isEmpty(templateQO.getGroupIdList()), TemplateGroupEntity::getId, templateQO.getGroupIdList())
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                ;

        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(templateQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateGroupEntity::getCode, templateQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(templateQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateGroupEntity::getName, templateQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(templateQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateGroupEntity::getTitle, templateQO.getSearchKey().trim())
            );
        }
        return queryWrapper;
    }


    /**
     * 根据条款组编码，获取条款组
     *
     * @param templateGroupCode 条款组编码
     * @return
     */
    public List<TemplateGroupEntity> getTemplateGroupByCode(String templateGroupCode, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                .eq(StringUtils.isNotBlank(templateGroupCode), TemplateGroupEntity::getCode, templateGroupCode)
                .eq(null != status, TemplateGroupEntity::getStatus, status)
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<TemplateGroupEntity> getAllGroupList(Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                .eq(null != status, TemplateGroupEntity::getStatus, status)
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 根据条款组编码，获取条款组
     *
     * @param realGroupCode 条款组编码
     * @return
     */
    public List<TemplateGroupEntity> getTemplateGroupByRealCode(String realGroupCode) {
        return this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                .eq(StringUtils.isNotBlank(realGroupCode), TemplateGroupEntity::getRealGroupCode, realGroupCode)
                .eq(TemplateGroupEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public TemplateGroupEntity getGroupEntityByCode(String templateGroupCode) {
        List<TemplateGroupEntity> groupEntityList = this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                .eq(StringUtils.isNotBlank(templateGroupCode), TemplateGroupEntity::getCode, templateGroupCode)
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(groupEntityList) ? null : groupEntityList.get(0);
    }

    public List<TemplateGroupEntity> getTemplateGroupByCodeList(List<String> templateGroupCodeList, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                .in(!CollectionUtils.isEmpty(templateGroupCodeList), TemplateGroupEntity::getCode, templateGroupCodeList)
                .eq(null != status, TemplateGroupEntity::getStatus, status)
                .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 查询条款组
     *
     * @param templateGroupCodeList
     * @param realGroupCode
     * @return
     */
    public TemplateGroupEntity getTemplateGroupByCode(List<String> templateGroupCodeList, String realGroupCode) {
        List<TemplateGroupEntity> groupEntityList = this.list(new LambdaQueryWrapper<TemplateGroupEntity>()
                        .in(!CollectionUtils.isEmpty(templateGroupCodeList), TemplateGroupEntity::getCode, templateGroupCodeList)
                        .eq(StringUtils.isNotBlank(realGroupCode), TemplateGroupEntity::getRealGroupCode, realGroupCode)
                        .eq(TemplateGroupEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
//                .eq(TemplateGroupEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                        .orderByDesc(TemplateGroupEntity::getStatus)
        );
        return CollectionUtils.isEmpty(groupEntityList) ? null : groupEntityList.get(0);

    }
}
