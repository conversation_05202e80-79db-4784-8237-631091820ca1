package com.navigator.husky.service.impl;

import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.bisiness.enums.*;
import com.navigator.common.dto.Result;
import com.navigator.common.util.TemplateUtil;
import com.navigator.husky.dao.TemplateDao;
import com.navigator.husky.dao.TemplateGroupRelationDao;
import com.navigator.husky.dao.TemplateItemRelationDao;
import com.navigator.husky.dao.VHuskyTemplateDao;
import com.navigator.husky.pojo.entity.*;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.service.TemplateItemService;
import com.navigator.husky.service.VHuskyTemplateService;
import com.navigator.husky.service.VariableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-11 19:19
 **/
@Service
@Slf4j
public class VHuskyTemplateServiceImpl implements VHuskyTemplateService {
    @Autowired
    private TemplateDao templateDao;
    @Autowired
    private TemplateGroupRelationDao templateGroupRelationDao;
    @Autowired
    private TemplateItemRelationDao templateItemRelationDao;
    @Autowired
    private VHuskyTemplateDao huskyTemplateDao;
    @Autowired
    private DictItemFacade dictItemFacade;

    @Autowired
    private TemplateItemService templateItemService;
    @Autowired
    private VariableService variableService;

    @Override
    public Result exportTemplateExcel(QueryTemplateQO templateQO) {
        List<VHuskyTemplateEntity> huskyTemplateEntityList = getHuskyTemplateList(templateQO);
//        this.recordTemplateHistoryLog(null, BasicOperateEnum.JSON_IMPORT, "", FastJsonUtils.getBeanToJson(templateQO));
        if (!CollectionUtils.isEmpty(huskyTemplateEntityList)) {
            //获取变量Map，code-displayName
            List<VariableEntity> variableBasicList = variableService.getAllVariableBasicList();
            Map<String, String> variableMap = variableBasicList.stream().collect(Collectors.toMap(VariableEntity::getCode, VariableEntity::getDisplayName, (k1, k2) -> k1));
            Map<String, String> variableInfoMap = new HashMap<>();
            for (Map.Entry<String, String> variableEntry : variableMap.entrySet()) {
                variableInfoMap.put("${" + variableEntry.getKey() + "!}", variableEntry.getValue());
            }

            List<DictItemEntity> dictItemEntityList = dictItemFacade.getItemByDictCode(DictItemType.ENTERPRISE_CODE.getValue());
            Map<String, String> enterpriseMap = dictItemEntityList.stream().collect(Collectors.toMap(DictItemEntity::getItemCode, DictItemEntity::getItemName));


            huskyTemplateEntityList = huskyTemplateEntityList.stream()
                    .filter(vHuskyTemplateEntity -> {
                        return StringUtils.isNotBlank(vHuskyTemplateEntity.getGroupCode()) && StringUtils.isNotBlank(vHuskyTemplateEntity.getItemCode());
                    })
                    .map(vHuskyTemplateEntity -> {
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getTemplateCompanyCode())) {
                            vHuskyTemplateEntity.setTemplateCompanyCodeInfo(vHuskyTemplateEntity.getTemplateCompanyCode().substring(1, vHuskyTemplateEntity.getTemplateCompanyCode().length() - 1));
                        }
                        vHuskyTemplateEntity.setTemplateContractActionTypeInfo(ContractTradeTypeEnum.getByValue(vHuskyTemplateEntity.getTemplateContractActionType()).getDesc())
                                .setGroupContractActionTypeInfo(-1 == vHuskyTemplateEntity.getGroupContractActionType() ? "固定" : ContractTradeTypeEnum.getDescByValue(vHuskyTemplateEntity.getGroupContractActionType()));
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getItemSalesType())) {
                            List<Integer> salesTypeList = Arrays.stream(vHuskyTemplateEntity.getItemSalesType()
                                    .substring(1, vHuskyTemplateEntity.getItemSalesType().length() - 1)
                                    .split(",")).map(Integer::valueOf).collect(Collectors.toList());
                            vHuskyTemplateEntity.setItemSalesType(ContractSalesTypeEnum.getSalesTypeInfo(salesTypeList));
                        }
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getTemplateBuCode())) {
                            vHuskyTemplateEntity.setTemplateBuCode(BuCodeEnum.getDescByValueList(vHuskyTemplateEntity.getTemplateBuCode()));
                        }
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getTemplateEnterpriseCode())) {
                            vHuskyTemplateEntity.setTemplateEnterpriseName(enterpriseMap.get(vHuskyTemplateEntity.getTemplateEnterpriseCode()));
                        }
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getItemEnterpriseCode())) {
                            vHuskyTemplateEntity.setItemEnterpriseName(enterpriseMap.get(vHuskyTemplateEntity.getItemEnterpriseCode()));
                        }
                        if (StringUtils.isBlank(vHuskyTemplateEntity.getBindTemplateName())) {
                            vHuskyTemplateEntity.setBindTemplateName(vHuskyTemplateEntity.getTemplateName());
                        }
                        if (StringUtils.isNotBlank(vHuskyTemplateEntity.getItemContent())) {
                            String nameContent = vHuskyTemplateEntity.getItemContent();
                            List<String> keyList = TemplateUtil.renderKeyVariableList(nameContent);
                            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(keyList)) {
                                for (String keyName : keyList) {
                                    String variableDisplayName = "${" + variableInfoMap.get(keyName) + "!}";
                                    log.info("条款文本替换：" + keyName + ",variableDisplayName:" + variableDisplayName);
                                    nameContent = nameContent.replaceAll(Pattern.quote(keyName), Matcher.quoteReplacement(variableDisplayName));
                                }
                            }
//                            String displayContent = templateItemService.aquireDisplayContentByCode(vHuskyTemplateEntity.getItemContent());
                            vHuskyTemplateEntity.setItemContent(TemplateUtil.removeParentheses(nameContent));
                        }
                        if (null == vHuskyTemplateEntity.getBindSort()) {
                            vHuskyTemplateEntity.setBindSort(0);
                        }
                        return vHuskyTemplateEntity;
                    })
                    .sorted(Comparator.comparing(VHuskyTemplateEntity::getTemplateId).reversed()
//                            .thenComparing(VHuskyTemplateEntity::getTemplateContractActionType)
//                            .thenComparing(VHuskyTemplateEntity::getTemplateCategoryId)
//                            .thenComparing(VHuskyTemplateEntity::getTemplateSalesType).reversed()
//                            .thenComparing(VHuskyTemplateEntity::getTemplateId).reversed()
                            .thenComparing(VHuskyTemplateEntity::getBindSort)
                            .thenComparing(VHuskyTemplateEntity::getGroupSort)
                            .thenComparing(VHuskyTemplateEntity::getItemId))
                    .collect(Collectors.toList());
        }
        return Result.success(huskyTemplateEntityList);
    }

    /**
     * 查询要导出的模板
     *
     * @param templateQO
     * @return
     */
    private List<VHuskyTemplateEntity> getHuskyTemplateList(QueryTemplateQO templateQO) {
        //根据关联的条款组 过滤模板编码
        List<String> filterCodeListByGroup = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateGroupCode())) {
            List<TemplateGroupRelationEntity> groupRelationEntityList = templateGroupRelationDao.getTemplateByCode(templateQO.getTemplateGroupCode(), "");
            filterCodeListByGroup = groupRelationEntityList.stream().map(TemplateGroupRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());
        }
        //根据关联的条款 过滤模板编码
        List<String> filterCodeListByItem = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateItemCode())) {
            List<TemplateItemRelationEntity> templateItemRelationList = templateItemRelationDao.getTemplateItemRelation("", templateQO.getTemplateItemCode());
            filterCodeListByItem = templateItemRelationList.stream().map(TemplateItemRelationEntity::getTemplateCode).distinct().collect(Collectors.toList());

        }
        List<TemplateEntity> templateEntityList = templateDao.queryTemplateListByCondition(templateQO, filterCodeListByGroup, filterCodeListByItem);
        List<String> templateCodeList = templateEntityList.stream().map(TemplateEntity::getCode).collect(Collectors.toList());
        return huskyTemplateDao.getTemplateByCodeList(templateCodeList);
    }
}
