package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.TemplateItemJsonDTO;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-07 17:59
 **/
public interface TemplateItemService {
    Result queryItemByCondition(QueryDTO<QueryTemplateQO> queryDTO);

    Boolean saveTemplateItem(TemplateItemEntity itemEntity);

    Boolean updateTemplateItem(TemplateItemEntity itemEntity);

    TemplateItemEntity getTemplateItemById(Integer templateItemId);

    List<TemplateItemEntity> getAllItemList(Integer status);

    TemplateItemEntity aquireItemContentByCode(TemplateItemEntity itemEntity);

    String aquireDisplayContentByCode(String nameContent);

    List<TemplateItemEntity> getItemListByGroupCode(String templateGroupCode);

    Boolean markItemMainVersion(TemplateItemEntity itemEntity);

    Result exportItemExcel(QueryTemplateQO templateQO);

    Result exportItemJson(QueryTemplateQO queryTemplateQO);

    Result importItemJson(MultipartFile file);

    Result processImportItemList(List<TemplateItemJsonDTO> itemJsonDTOList,String mainVersion);
}
