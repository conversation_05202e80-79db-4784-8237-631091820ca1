package com.navigator.husky.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.*;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.TemplateUtil;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.dao.TemplateGroupDao;
import com.navigator.husky.dao.TemplateGroupRelationDao;
import com.navigator.husky.dao.TemplateHistoryLogDao;
import com.navigator.husky.dao.TemplateItemDao;
import com.navigator.husky.pojo.dto.ItemInfoDTO;
import com.navigator.husky.pojo.dto.TemplateGroupJsonDTO;
import com.navigator.husky.pojo.entity.*;
import com.navigator.husky.pojo.enums.VersionType;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-04 18:24
 **/
@Service
@Slf4j
public class TemplateGroupServiceImpl implements TemplateGroupService {
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private TemplateHistoryLogDao templateHistoryLogDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TemplateRuleService templateRuleService;
    @Autowired
    private TemplateGroupRelationDao templateGroupRelationDao;
    @Autowired
    private TemplateItemDao templateItemDao;
    @Autowired
    private TemplateItemService templateItemService;
    @Autowired
    private TemplateHistoryService templateHistoryService;
    @Autowired
    private VariableService variableService;
    private final static Integer fixContractActionType = -1;

    @Override
    public Result queryGroupByCondition(QueryDTO<QueryTemplateQO> queryDTO) {
        IPage<TemplateGroupEntity> templateGroupEntityIPage = templateGroupDao.queryByCondition(queryDTO);
        for (TemplateGroupEntity templateGroupEntity : templateGroupEntityIPage.getRecords()) {
            TemplateRuleEntity ruleEntity = templateRuleService.getRuleByTemplateCode(templateGroupEntity.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
            if (null != ruleEntity) {
                //加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
                templateGroupEntity.setConditionInfo(ruleEntity.getConditionInfo());
            }
            String contractActionTypeInfo = ContractTradeTypeEnum.getDescByValue(templateGroupEntity.getContractActionType());
            templateGroupEntity.setContractActionTypeInfo(-1 == templateGroupEntity.getContractActionType() ? "无" : contractActionTypeInfo);

        }
        return Result.page(templateGroupEntityIPage);
    }

    @Override
    public Boolean saveTemplateGroup(TemplateGroupEntity templateGroupEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(templateGroupEntity);
        //1、校验条款组编码
        String templateGroupCode = templateGroupEntity.getCode().trim();
        if (StringUtils.isNotBlank(templateGroupCode)) {
            if (CollectionUtils.isNotEmpty(templateGroupDao.getTemplateGroupByCode(templateGroupCode, null))) {
                throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_CODE_DUPLICATE);
            }
            templateGroupEntity.setCode(templateGroupCode);
        }
        //3、保存条款组
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        templateGroupEntity.setRealGroupCode(StringUtils.isNotBlank(templateGroupEntity.getRealGroupCode()) ? templateGroupEntity.getRealGroupCode() : templateGroupEntity.getCode())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setIsFixed(null == templateGroupEntity.getIsFixed() ? DisableStatusEnum.DISABLE.getValue() : templateGroupEntity.getIsFixed())
//                .setIsFixed(fixContractActionType.equals(templateGroupEntity.getContractActionType()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(name)
                .setUpdatedBy(name);
        templateGroupDao.save(templateGroupEntity);
        if (StringUtils.isBlank(templateGroupCode)) {
            templateGroupCode = this.getTemplateGroupCode(templateGroupEntity.getId());
            templateGroupEntity.setCode(templateGroupCode)
                    .setRealGroupCode(StringUtils.isNotBlank(templateGroupEntity.getRealGroupCode()) ? templateGroupEntity.getRealGroupCode() : templateGroupCode);
            templateGroupDao.updateById(templateGroupEntity);
        }
        //4、条款组条件变量规则记录
        TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(templateGroupCode, TemplateTypeEnum.M_TERMS_TEMPLATE.getValue(),
                templateGroupEntity.getConditionVariableList());
        templateGroupEntity.setRuleInfo(templateRuleEntity.getRuleInfo()).setRuleCode(templateRuleEntity.getRuleCode());
        templateGroupDao.updateById(templateGroupEntity);

        //5、记录条款组新增日志
        this.recordGroupHistoryLog(templateGroupEntity, BasicOperateEnum.NEW, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(templateGroupEntity), requestInfo);
        return true;
    }

    @Override
    public Boolean updateTemplateGroup(TemplateGroupEntity templateGroupEntity) {
        String requestInfo = FastJsonUtils.getBeanToJson(templateGroupEntity);
        TemplateGroupEntity groupEntity = templateGroupDao.getById(templateGroupEntity.getId());
        String templateGroupCode = templateGroupEntity.getCode().trim();

        //条款组编码可编辑时
//        if (StringUtils.isNotBlank(templateGroupCode)) {
//            List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCode(templateGroupCode);
//            groupEntityList = groupEntityList.stream().filter(it -> !it.getId().equals(groupEntity.getId())).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(groupEntityList)) {
//                throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_CODE_DUPLICATE);
//            }
//        } else {
//            templateGroupCode = this.getTemplateGroupCode(templateGroupEntity.getId());
//        }
        Integer oldIsFixed = groupEntity.getIsFixed();
//        Integer newIsFixed = fixContractActionType.equals(templateGroupEntity.getContractActionType()) ? DisableStatusEnum.ENABLE.getValue() : DisableStatusEnum.DISABLE.getValue();
        Integer newIsFixed = null == templateGroupEntity.getIsFixed() ? DisableStatusEnum.DISABLE.getValue() : templateGroupEntity.getIsFixed();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        //4、条款组条件变量规则记录
        TemplateRuleEntity templateRuleEntity = templateRuleService.recordTemplateRule(templateGroupCode, TemplateTypeEnum.M_TERMS_TEMPLATE.getValue(),
                templateGroupEntity.getConditionVariableList());
        groupEntity.setCode(templateGroupCode.trim())
                .setName(templateGroupEntity.getName())
                .setTitle(templateGroupEntity.getTitle())
                .setNeedNum(templateGroupEntity.getNeedNum())
                .setMemo(templateGroupEntity.getMemo())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setStatus(templateGroupEntity.getStatus())
                .setMainVersionStatus(DisableStatusEnum.DISABLE.getValue())
                .setContractActionType(templateGroupEntity.getContractActionType())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now())
                .setIsFixed(newIsFixed)
                .setRuleInfo(templateRuleEntity.getRuleInfo())
                .setRuleCode(templateRuleEntity.getRuleCode())
                .setRealGroupCode(StringUtils.isNotBlank(templateGroupEntity.getRealGroupCode()) ? templateGroupEntity.getRealGroupCode() : templateGroupCode)
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
        ;

        templateGroupDao.updateById(groupEntity);

//        if (!templateGroupCode.equals(groupEntity.getCode())) {
//            //同步模板-条款组的编码
//            templateGroupRelationDao.syncTemplateGroupCode(groupEntity.getCode(), templateGroupCode);
//            templateItemDao.syncTemplateGroupCode(groupEntity.getCode(), templateGroupCode);
//        }
        if (!oldIsFixed.equals(newIsFixed) && DisableStatusEnum.ENABLE.getValue().equals(newIsFixed)) {
            templateItemDao.syncItemIsFixed(groupEntity.getCode(), newIsFixed);
        }
        //5、记录条款组新增日志
        groupEntity.setConditionVariableList(templateGroupEntity.getConditionVariableList());
        this.recordGroupHistoryLog(groupEntity, BasicOperateEnum.UPDATE, VersionType.TEMPORARY, "", requestInfo);
        return true;
    }

    @Override
    public Boolean updateGroupStatus(Integer templateGroupId, Integer status) {
        TemplateGroupEntity groupEntity = this.templateGroupDao.getById(templateGroupId);
        if (null == groupEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_NOT_EXIT);
        }
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        groupEntity.setStatus(status)
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setUpdatedBy(name)
                .setUpdatedAt(DateTimeUtil.now());
        templateGroupDao.updateById(groupEntity);
        //4、记录新增模板的日志记录
        BasicOperateEnum basicOperateEnum = DisableStatusEnum.getOperateByStatus(status);
        this.recordGroupHistoryLog(groupEntity, basicOperateEnum, VersionType.TEMPORARY, FastJsonUtils.getBeanToJson(groupEntity), DisableStatusEnum.getDescByValue(status));
        return true;
    }

    @Override
    public TemplateGroupEntity getTemplateGroupById(Integer templateGroupId) {
        TemplateGroupEntity templateGroupEntity = templateGroupDao.getById(templateGroupId);
        if (null == templateGroupEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_NOT_EXIT);
        }
        String contractActionTypeInfo = ContractTradeTypeEnum.getDescByValue(templateGroupEntity.getContractActionType());
        log.info("条款组的操作类型为：" + contractActionTypeInfo);
        templateGroupEntity.setContractActionTypeInfo(-1 == templateGroupEntity.getContractActionType() ? "无" : contractActionTypeInfo);
        TemplateRuleEntity templateRuleEntity = templateRuleService.getRuleDetailByTemplateCode(templateGroupEntity.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
        if (null != templateRuleEntity) {
            templateGroupEntity.setConditionInfo(templateRuleEntity.getConditionInfo())
                    .setConditionVariableList(templateRuleEntity.getConditionVariableList());
        }
        return templateGroupEntity;
    }

    @Override
    public List<TemplateGroupEntity> getTemplateGroupByCodeList(List<String> templateGroupCodeList, Integer status) {
        return templateGroupDao.getTemplateGroupByCodeList(templateGroupCodeList, status);
    }

    @Override
    public List<TemplateGroupEntity> getAllGroupList(Integer status) {
        return templateGroupDao.getAllGroupList(status);
    }

    @Override
    public TemplateGroupEntity getTemplateGroupByCode(String groupCode) {
        List<TemplateGroupEntity> groupEntityList = templateGroupDao.getTemplateGroupByCode(groupCode, null);
        return CollectionUtils.isEmpty(groupEntityList) ? null : groupEntityList.get(0);
    }

    @Override
    public Boolean markGroupMainVersion(TemplateGroupEntity groupEntity) {

        groupEntity.setMainVersionStatus(DisableStatusEnum.ENABLE.getValue())
                .setVersion(DateTimeUtil.formatDateTimeValue())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(this.getCurrentUserName());
        templateGroupDao.updateById(groupEntity);

        this.recordGroupHistoryLog(groupEntity, BasicOperateEnum.MARK_MAIN_VERSION, VersionType.MAIN_VERSION, FastJsonUtils.getBeanToJson(groupEntity), "");
        return true;
    }

    private String getCurrentUserName() {
        //2、更新模板信息
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        return employFacade.getEmployCache(userId);
    }


    @Override
    public Result exportGroupExcel(QueryTemplateQO queryTemplateQO) {
        List<TemplateGroupEntity> groupEntityList = templateGroupDao.queryGroupList(queryTemplateQO);
        if (!CollectionUtils.isEmpty(groupEntityList)) {
            //获取变量Map，code-displayName
            List<VariableEntity> variableBasicList = variableService.getAllVariableBasicList();
            Map<String, String> variableMap = variableBasicList.stream().collect(Collectors.toMap(VariableEntity::getCode, VariableEntity::getDisplayName, (k1, k2) -> k1));
            Map<String, String> variableInfoMap = new HashMap<>();
            for (Map.Entry<String, String> variableEntry : variableMap.entrySet()) {
                variableInfoMap.put("${" + variableEntry.getKey() + "!}", variableEntry.getValue());
            }

            groupEntityList.forEach(groupEntity -> {
                String contractActionTypeInfo = ContractTradeTypeEnum.getDescByValue(groupEntity.getContractActionType());
                groupEntity.setContractActionTypeInfo(-1 == groupEntity.getContractActionType() ? "无" : contractActionTypeInfo);
                //条款组绑定的模板编号（逗号隔开）
                List<TemplateGroupRelationEntity> templateRelationList = templateGroupRelationDao.getTemplateByCode(groupEntity.getCode(), "");
                if (CollectionUtils.isNotEmpty(templateRelationList)) {
                    String templateCodes = templateRelationList.stream()
                            .map(TemplateGroupRelationEntity::getTemplateCode).distinct()
                            .collect(Collectors.joining(","));
                    groupEntity.setBindTemplateCodes(templateCodes);
                }
                //关联的条款及内容
                List<TemplateItemEntity> itemEntityList = templateItemDao.getItemListByGroupCode(groupEntity.getRealGroupCode());
                if (CollectionUtils.isNotEmpty(itemEntityList)) {


                    List<ItemInfoDTO> itemInfoDTOList = itemEntityList.stream()
                            .map(itemEntity -> {
                                String nameContent = "";
                                if (StringUtils.isNotBlank(itemEntity.getContent())) {
                                    nameContent = itemEntity.getContent();
                                    List<String> keyList = TemplateUtil.renderKeyVariableList(nameContent);
                                    if (CollectionUtils.isNotEmpty(keyList)) {
                                        for (String keyName : keyList) {
                                            String variableDisplayName = "${" + variableInfoMap.get(keyName) + "!}";
                                            log.info("条款文本替换：" + keyName + ",variableDisplayName:" + variableDisplayName);
                                            nameContent = nameContent.replaceAll(Pattern.quote(keyName), Matcher.quoteReplacement(variableDisplayName));
                                        }
                                    }
                                }
//                                templateItemService.aquireItemContentByCode(itemEntity);
                                return new ItemInfoDTO()
                                        .setBindItemCode(itemEntity.getCode())
                                        .setBindItemName(itemEntity.getName())
                                        .setBindItemContent(TemplateUtil.removeParentheses(nameContent));
                            })
                            .collect(Collectors.toList());
                    groupEntity.setItemInfoList(itemInfoDTOList);
                }
                //条款组-加载条件
                if (StringUtils.isNotBlank(groupEntity.getRuleInfo())) {
                    TemplateRuleEntity ruleEntity = templateRuleService.getRuleDetailEntityByCode(groupEntity.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
                    if (null != ruleEntity) {
                        groupEntity.setConditionInfo(ruleEntity.getConditionInfo());
                    }
                }

            });
        }
        //3、记录条款excel导出日志
//        TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
//                .setReferType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue())
//                .setOperationType(BasicOperateEnum.EXCEL_EXPORT.getValue())
//                .setOperationTypeInfo(BasicOperateEnum.EXCEL_EXPORT.getDesc())
//                .setContent(FastJsonUtils.getBeanToJson(groupEntityList))
//                .setRequestInfo(FastJsonUtils.getBeanToJson(queryTemplateQO));
//        templateHistoryService.saveTemplateHistory(historyEntity);
        return Result.success(groupEntityList);
    }

    private static List<String> renderKeyVariableList(String content) {
        String regex = "\\$\\{([^}]*)!\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> keyList = new ArrayList<>();
        while (matcher.find()) {
            keyList.add(matcher.group());
        }
        return keyList;
    }

    @Override
    public Result exportGroupJson(QueryTemplateQO queryTemplateQO) {
        List<TemplateGroupEntity> groupEntityList = templateGroupDao.queryGroupList(queryTemplateQO);
        if (CollectionUtils.isEmpty(groupEntityList)) {
            return Result.success(groupEntityList);
        }
        List<String> notMainVersionGroupCodeList = groupEntityList.stream().filter(groupEntity -> {
            return DisableStatusEnum.DISABLE.getValue().equals(groupEntity.getMainVersionStatus());
        }).map(TemplateGroupEntity::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notMainVersionGroupCodeList)) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_GROUP_EXIST_NOT_MAIN_VERSION, StringUtils.join(notMainVersionGroupCodeList, ","));
        }
        List<TemplateGroupJsonDTO> groupJsonDTOList = groupEntityList.stream().map(this::getGroupDetailJson).collect(Collectors.toList());

        List<String> exportGroupCodeList = groupJsonDTOList.stream().map(TemplateGroupJsonDTO::getCode).collect(Collectors.toList());
        String exportGroupCodes = StringUtils.join(exportGroupCodeList, ",");

        Timestamp now = DateTimeUtil.now();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        //3、记录条款脚本导出日志
        for (TemplateGroupJsonDTO groupJsonDTO : groupJsonDTOList) {
            TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
                    .setReferType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue())
                    .setReferCode(groupJsonDTO.getCode())
                    .setReferName(groupJsonDTO.getName())
                    .setOperationType(BasicOperateEnum.JSON_EXPORT.getValue())
                    .setOperationTypeInfo(BasicOperateEnum.JSON_EXPORT.getDesc())
                    .setVersionType(VersionType.MAIN_VERSION.getValue())
                    .setContent(FastJsonUtils.getBeanToJson(groupJsonDTOList))
                    .setMemo(exportGroupCodes)
                    .setMainVersion(groupJsonDTO.getVersion())
                    .setVersion(groupJsonDTO.getVersion())
                    .setRequestInfo(FastJsonUtils.getBeanToJson(queryTemplateQO))
                    .setCreatedAt(now)
                    .setUpdatedAt(now)
                    .setCreatedBy(name)
                    .setUpdatedBy(name);
            ;
            templateHistoryService.saveTemplateHistory(historyEntity);
        }
        return Result.success(groupJsonDTOList);
    }

    private TemplateGroupJsonDTO getGroupDetailJson(TemplateGroupEntity templateGroupEntity) {
        TemplateGroupJsonDTO groupJsonDTO = BeanConvertUtils.convert(TemplateGroupJsonDTO.class, templateGroupEntity);
        if (StringUtils.isNotBlank(groupJsonDTO.getRuleInfo())) {
            TemplateRuleEntity ruleEntity = templateRuleService.getRuleDetailEntityByCode(groupJsonDTO.getCode(), TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
            if (null != ruleEntity) {
                groupJsonDTO.setTemplateRule(ruleEntity);
            }
        }
        groupJsonDTO.setTemplateType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue());
        return groupJsonDTO;
    }

    @Override
    public Result importGroupJson(MultipartFile file) {
        String jsonString = JsonFileUtil.readJson(file);
        //2、条款组脚本信息同步
        List<TemplateGroupJsonDTO> groupJsonDTOList = FastJsonUtils.getJsonToList(jsonString, TemplateGroupJsonDTO.class);
        if (CollectionUtils.isEmpty(groupJsonDTOList)) {
            return Result.failure("条款组脚本数据为空！");
        }
        if (!TemplateTypeEnum.M_TERMS_TEMPLATE.getValue().equals(groupJsonDTOList.get(0).getTemplateType())) {
            return Result.failure("应导入条款组脚本，脚本数据类型不匹配！");
        }
        //1、记录条款组脚本导入日志
        Timestamp now = DateTimeUtil.now();
        Integer userId = Integer.valueOf(JwtUtils.getCurrentUserId());
        String name = employFacade.getEmployCache(userId);
        for (TemplateGroupJsonDTO groupJsonDTO : groupJsonDTOList) {
            TemplateHistoryEntity historyEntity = new TemplateHistoryEntity()
                    .setReferCode(groupJsonDTO.getCode())
                    .setReferName(groupJsonDTO.getName())
                    .setReferType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue())
                    .setOperationType(BasicOperateEnum.JSON_IMPORT.getValue())
                    .setOperationTypeInfo(BasicOperateEnum.JSON_IMPORT.getDesc())
                    .setVersionType(VersionType.MAIN_VERSION.getValue())
                    .setMainVersion(groupJsonDTO.getVersion())
                    .setVersion(groupJsonDTO.getVersion())
                    .setContent(jsonString)
                    .setCreatedAt(now)
                    .setUpdatedAt(now)
                    .setCreatedBy(name)
                    .setUpdatedBy(name);
            templateHistoryService.saveTemplateHistory(historyEntity);
        }

        this.processImportGroupList(groupJsonDTOList, "");
        return Result.success(groupJsonDTOList);
    }

    @Override
    public Result processImportGroupList(List<TemplateGroupJsonDTO> groupJsonDTOList, String mainVersion) {
        groupJsonDTOList.sort(Comparator.comparing(TemplateGroupJsonDTO::getId));
        for (TemplateGroupJsonDTO newGroupDTO : groupJsonDTOList) {
            String groupCode = newGroupDTO.getCode();
            TemplateGroupEntity groupEntity = templateGroupDao.getGroupEntityByCode(groupCode);
            TemplateGroupEntity newGroupEntity = BeanConvertUtils.convert(TemplateGroupEntity.class, newGroupDTO);
            BasicOperateEnum basicOperateEnum;
            if (null == groupEntity) {
                //条款组-新增
                basicOperateEnum = BasicOperateEnum.NEW;
                newGroupEntity = BeanConvertUtils.convert(TemplateGroupEntity.class, newGroupDTO);
                newGroupEntity.setId(null);
                templateGroupDao.save(newGroupEntity);
            } else {
                //条款组-更新
                basicOperateEnum = BasicOperateEnum.UPDATE;
                if (newGroupDTO.getVersion().equals(groupEntity.getVersion())) {
                    continue;
                }
                newGroupEntity.setId(groupEntity.getId());
                templateGroupDao.updateById(newGroupEntity);
            }
            templateRuleService.syncImportTemplateRule(groupCode, TemplateTypeEnum.M_TERMS_TEMPLATE.getValue(),
                    newGroupDTO.getTemplateRule());
            this.recordGroupHistoryLog(newGroupEntity.setMainVersion(mainVersion), basicOperateEnum, VersionType.MAIN_VERSION, "", FastJsonUtils.getBeanToJson(newGroupDTO));
        }
        return Result.success(groupJsonDTOList);
    }

    /**
     * 生成条款组编码
     *
     * @param templateGroupId 条款组ID
     * @return 条款组编码
     */
    private String getTemplateGroupCode(Integer templateGroupId) {
        return "ClauseGroup_" + String.format("%04d", templateGroupId);
    }

    /**
     * 记录条款新增/修改日志
     *
     * @param templateGroupEntity 条款组信息
     * @param basicOperateEnum    操作动作
     */
    private void recordGroupHistoryLog(TemplateGroupEntity templateGroupEntity, BasicOperateEnum basicOperateEnum,
                                       VersionType versionType, String content, String requestInfo) {
        TemplateHistoryEntity templateHistoryEntity = new TemplateHistoryEntity();
        templateHistoryEntity.setId(null)
                .setReferId(templateGroupEntity.getId())
                .setReferCode(templateGroupEntity.getCode())
                .setReferName(templateGroupEntity.getName())
                .setReferType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue())
                .setOperationType(basicOperateEnum.getValue())
                .setOperationTypeInfo(basicOperateEnum.getDesc())
                .setTitle(templateGroupEntity.getTitle())
                .setContractActionType(String.valueOf(templateGroupEntity.getContractActionType()))
                .setVersionType(versionType.getValue())
                .setVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateGroupEntity.getMainVersion() : templateGroupEntity.getVersion())
                .setMainVersionStatus(templateGroupEntity.getMainVersionStatus())
                .setMainVersion(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateGroupEntity.getVersion() : templateGroupEntity.getMainVersion())
//                .setMainVersionDesc(VersionType.MAIN_VERSION.getValue().equals(versionType.getValue()) ? templateGroupEntity.getMainVersionDesc() : "")
                .setStatus(templateGroupEntity.getStatus())
                .setRequestInfo(requestInfo)
                .setContent(content)
                .setCreatedAt(DateTimeUtil.now())
                .setUpdatedAt(DateTimeUtil.now())
                .setUpdatedBy(templateGroupEntity.getUpdatedBy())
                .setCreatedBy(templateGroupEntity.getCreatedBy());
        if (BasicOperateEnum.needRecordDetailInfoType().contains(basicOperateEnum.getValue())) {
            templateHistoryEntity.setContent(FastJsonUtils.getBeanToJson(this.getGroupDetailJson(templateGroupEntity)));
        }
        if (StringUtils.isNotBlank(templateHistoryEntity.getMainVersion())) {
            templateHistoryEntity.setMainVersion(templateGroupEntity.getVersion());
        }
        templateHistoryService.saveTemplateHistory(templateHistoryEntity);
    }

//    /**
//     * 记录模板新增/修改日志
//     *
//     * @param templateGroupEntity 条款组信息
//     * @param operationType       操作动作
//     */
//    private void recordTemplateHistoryLog(TemplateGroupEntity templateGroupEntity, String operationType) {
//        TemplateHistoryLogEntity templateHistoryLogEntity = BeanConvertUtils.convert(TemplateHistoryLogEntity.class, templateGroupEntity);
//        templateHistoryLogEntity.setId(null)
//                .setReferId(templateGroupEntity.getId())
//                .setReferCode(templateGroupEntity.getCode())
//                .setReferType(TemplateTypeEnum.M_TERMS_TEMPLATE.getValue())
//                .setOperationType(operationType)
//                .setConditionVariableInfo(CollectionUtils.isEmpty(templateGroupEntity.getConditionVariableList()) ? "" : FastJsonUtils.getBeanToJson(templateGroupEntity.getConditionVariableList()))
//        ;
//        templateHistoryLogDao.save(templateHistoryLogEntity);
//    }
}
