package com.navigator.husky.service;

import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateEntity;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-14 15:24
 **/
public interface TemplateSignService {
    Result matchTemplateByRule(Integer contractSignId);

    Result matchTemplateByRuleV2(Integer contractSignId);

    String provideContractSign(TemplateEntity templateEntity);

    Result checkTemplateByRule(Integer contractSignId);
}
