package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateItemRelationMapper;
import com.navigator.husky.pojo.dto.TemplateItemRelationDTO;
import com.navigator.husky.pojo.entity.TemplateItemRelationEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateItemRelationDao extends BaseDaoImpl<TemplateItemRelationMapper, TemplateItemRelationEntity> {

    /**
     * 重新绑定模板条款组关系
     *
     * @param itemRelationDTO 模板-条款组绑定关系数组
     */
    public Boolean bindTemplateItemRelation(TemplateItemRelationDTO itemRelationDTO) {
        //true:模板绑定条款 false:条款绑定模板
        Boolean bindTemplate = DisableStatusEnum.ENABLE.getValue().equals(itemRelationDTO.getBindType());
        if (bindTemplate) {
            // 清空模板绑定关系
            this.dropTemplateItemRelation(itemRelationDTO.getBindCode(), "");
        } else {
            // 清空条款绑定关系
            this.dropTemplateItemRelation("", itemRelationDTO.getBindCode());
        }
        if (CollectionUtils.isEmpty(itemRelationDTO.getReferCodeList())) {
            return true;
        }
        //保存模板-条款关系
        itemRelationDTO.getReferCodeList().forEach(referCode -> {
            TemplateItemRelationEntity itemRelationEntity = new TemplateItemRelationEntity();
            itemRelationEntity.setSort(itemRelationDTO.getReferCodeList().indexOf(referCode));
            itemRelationEntity.setTemplateCode(bindTemplate ? itemRelationDTO.getBindCode() : referCode)
                    .setTemplateItemCode(bindTemplate ? referCode : itemRelationDTO.getBindCode())
                    .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now());
            this.save(itemRelationEntity);
        });
        return true;
    }

    /**
     * 同步导入的模板-条款组关联关系
     *
     * @param templateCode
     * @param itemRelationList
     */
    public void syncImportTemplateItemRelation(String templateCode, List<TemplateItemRelationEntity> itemRelationList) {
        this.dropTemplateItemRelation(templateCode, "");
        if (CollectionUtils.isEmpty(itemRelationList)) {
            return;
        }
        itemRelationList.forEach(itemRelationEntity -> {
            itemRelationEntity.setId(null);
            this.save(itemRelationEntity);
        });
    }

    public void copyTemplateItemRelation(String newTemplateCode, List<TemplateItemRelationEntity> itemRelationEntityList) {
        itemRelationEntityList.forEach(itemRelationEntity -> {
            itemRelationEntity.setId(null)
                    .setTemplateCode(newTemplateCode)
                    .setCreatedAt(DateTimeUtil.now())
                    .setUpdatedAt(DateTimeUtil.now());
            this.save(itemRelationEntity);
        });
    }

    /**
     * 根据模板/条款编码查询绑定关系
     *
     * @param templateCode     模板编码
     * @param templateItemCode 条款编码
     * @return 模板-条款绑定关系
     */
    public List<TemplateItemRelationEntity> getTemplateItemRelation(String templateCode, String templateItemCode) {
        return this.list(new LambdaQueryWrapper<TemplateItemRelationEntity>()
                .eq(StringUtils.isNotBlank(templateCode), TemplateItemRelationEntity::getTemplateCode, templateCode)
                .eq(StringUtils.isNotBlank(templateItemCode), TemplateItemRelationEntity::getTemplateItemCode, templateItemCode)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param templateCodeList 模板·编码
     * @return
     */
    public List<TemplateItemRelationEntity> getItemRelationByTemplateCode(List<String> templateCodeList) {
        if (CollectionUtils.isEmpty(templateCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<TemplateItemRelationEntity>()
                .in(TemplateItemRelationEntity::getTemplateCode, templateCodeList)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 根据条款/条款组编码查询绑定关系
     *
     * @param templateItemCodeList 条款编码
     * @param templateCode         条款编码
     * @return
     */
    public List<TemplateItemRelationEntity> getTemplateItemByCode(List<String> templateItemCodeList, String templateCode) {
        return this.list(new LambdaQueryWrapper<TemplateItemRelationEntity>()
                .in(!CollectionUtils.isEmpty(templateItemCodeList), TemplateItemRelationEntity::getTemplateItemCode, templateItemCodeList)
                .eq(StringUtils.isNotBlank(templateCode), TemplateItemRelationEntity::getTemplateCode, templateCode)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 同步更新条款组编码
     *
     * @param oldItemCode
     * @param newItemCode
     */
    public void syncTemplateItemCode(String oldItemCode, String newItemCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateItemRelationEntity::getTemplateItemCode, oldItemCode)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateItemRelationEntity::getTemplateItemCode, newItemCode)
                .set(TemplateItemRelationEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }


    /**
     * 同步更新条款组编码
     *
     * @param oldTemplateCode
     * @param newTemplateCode
     */
    public void syncTemplateCode(String oldTemplateCode, String newTemplateCode) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateItemRelationEntity::getTemplateCode, oldTemplateCode)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateItemRelationEntity::getTemplateCode, newTemplateCode)
                .set(TemplateItemRelationEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }

    /**
     * 解绑模板-条款组关系
     *
     * @param templateCode
     */
    public Boolean dropTemplateItemRelation(String templateCode, String itemCode) {
        return new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(StringUtils.isNotBlank(templateCode), TemplateItemRelationEntity::getTemplateCode, templateCode)
                .eq(StringUtils.isNotBlank(itemCode), TemplateItemRelationEntity::getTemplateItemCode, itemCode)
                .eq(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateItemRelationEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(TemplateItemRelationEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
