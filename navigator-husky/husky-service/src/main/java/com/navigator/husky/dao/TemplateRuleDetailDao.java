package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.bisiness.enums.PatternRelationEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.constant.TemplateConstant;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateRuleDetailMapper;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import com.navigator.husky.pojo.entity.TemplateRuleDetailEntity;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateRuleDetailDao extends BaseDaoImpl<TemplateRuleDetailMapper, TemplateRuleDetailEntity> {

    /**
     * 根据规则编号获取规则信息
     *
     * @param ruleCode
     * @return
     */
    public List<TemplateRuleDetailEntity> getRuleDetailListByRuleCode(String ruleCode) {
        return this.list(new LambdaQueryWrapper<TemplateRuleDetailEntity>()
                .eq(TemplateRuleDetailEntity::getRuleCode, ruleCode)
                .eq(TemplateRuleDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 处理规则详情信息
     *
     * @param templateRuleEntity    条款/条款组加载条件信息表
     * @param conditionVariableList 条款/条款组加载条件信息详情
     */
    public void recordRuleDetail(TemplateRuleEntity templateRuleEntity, List<ConditionVariableDTO> conditionVariableList) {
        this.dropTemplateRuleDetail(templateRuleEntity.getReferCode(), templateRuleEntity.getReferType());
        if (CollectionUtils.isEmpty(conditionVariableList)) {
            return;
        }
        conditionVariableList.forEach(it -> {
            // BUGFIX：Case-1003305-数字合同模板,品名模板触发条件提交报错 Author: nana 2025-06-24 Start
            String conditionValue = it.getConditionValue();
            String conditionValueInfo = StringUtils.join(it.getConditionDescList(), ",");
            if (PatternRelationEnum.getContainRelation().contains(it.getPatternRelation())) {
                conditionValue = StringUtils.join(it.getConditionValueList(), ",");
                if (TemplateConstant.TEMPLATE_RULE_SPU.equals(it.getConditionVariable())) {
                    conditionValue = it.getConditionValueList().stream()
                            .map(s -> "\"" + s + "\"")  // 给每个字符串添加引号
                            .collect(Collectors.joining(","));  // 用逗号连接
                    conditionValueInfo = it.getConditionDescList().stream()
                            .map(s -> "\"" + s + "\"")  // 给每个字符串添加引号
                            .collect(Collectors.joining(","));  // 用逗号连接
                }
            }
            TemplateRuleDetailEntity ruleDetailEntity = new TemplateRuleDetailEntity()
                    .setRuleCode(templateRuleEntity.getRuleCode())
                    .setReferCode(templateRuleEntity.getReferCode())
                    .setReferType(templateRuleEntity.getReferType())
                    //变量
                    .setConditionVariable(it.getConditionVariable())
                    .setConditionVariableId(it.getConditionVariableId())
                    //运算关系
                    .setPatternRelation(it.getPatternRelation())
                    //加载条件阈值
                    .setConditionValue(conditionValue)
                    .setConditionValueInfo(conditionValueInfo)
                    .setRuleDesc(it.getLogicRelationInfo() + it.getConditionVariableInfo() + it.getPatternRelationInfo() + it.getConditionValueInfo())
//                    .setConditionValueIds(StringUtils.join(it.getConditionValueIdList(), ","))
                    .setLevel(it.getLevel())
                    .setSort(it.getSort())
                    .setLogicRelation(it.getLogicRelation())
                    //规则脚本
                    .setRuleInfo(it.getRuleInfo());
            // BUGFIX：Case-1003305-数字合同模板,品名模板触发条件提交报错 Author: nana 2025-06-24 End
            this.save(ruleDetailEntity);
        });
    }

    public void dropTemplateRuleDetail(String referCode, Integer referType) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(TemplateRuleDetailEntity::getReferCode, referCode)
                .eq(TemplateRuleDetailEntity::getReferType, referType)
                .eq(TemplateRuleDetailEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateRuleDetailEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .set(TemplateRuleDetailEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }
}
