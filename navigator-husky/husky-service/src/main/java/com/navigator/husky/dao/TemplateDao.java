package com.navigator.husky.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.mapper.TemplateMapper;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.trade.pojo.dto.contractsign.KeyVariableDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 18:52
 **/
@Dao
public class TemplateDao extends BaseDaoImpl<TemplateMapper, TemplateEntity> {

    /**
     * 条件判断模板数据
     *
     * @param queryDTO              查询条件
     * @param filterCodeListByGroup 条款组-过滤的模板编号条件
     * @param filterCodeListByItem  条款-过滤的模板编号条件
     * @return 分页结果数据
     */
    public IPage<TemplateEntity> pageByCondition(QueryDTO<QueryTemplateQO> queryDTO,
                                                 List<String> filterCodeListByGroup,
                                                 List<String> filterCodeListByItem) {
        QueryTemplateQO templateQO = queryDTO.getCondition();
        if (StringUtils.isNotBlank(templateQO.getTemplateGroupCode()) && CollectionUtils.isEmpty(filterCodeListByGroup)) {
            return this.page(new Page<>(0, 0, 0));
        }
        if (StringUtils.isNotBlank(templateQO.getTemplateItemCode()) && CollectionUtils.isEmpty(filterCodeListByItem)) {
            return this.page(new Page<>(0, 0, 0));
        }
        LambdaQueryWrapper<TemplateEntity> queryWrapper = getTemplateQueryWrapper(templateQO, filterCodeListByGroup, filterCodeListByItem);
        queryWrapper.orderByDesc(TemplateEntity::getId);
        return this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize(), 0), queryWrapper);
    }

    public List<TemplateEntity> queryTemplateListByCondition(QueryTemplateQO templateQO, List<String> filterCodeListByGroup,
                                                             List<String> filterCodeListByItem) {
        List<TemplateEntity> templateEntityList = new ArrayList<>();
        if (StringUtils.isNotBlank(templateQO.getTemplateGroupCode()) && CollectionUtils.isEmpty(filterCodeListByGroup)) {
            return templateEntityList;
        }
        if (StringUtils.isNotBlank(templateQO.getTemplateItemCode()) && CollectionUtils.isEmpty(filterCodeListByItem)) {
            return templateEntityList;
        }
        LambdaQueryWrapper<TemplateEntity> queryWrapper = getTemplateQueryWrapper(templateQO, filterCodeListByGroup, filterCodeListByItem);
        queryWrapper.orderByAsc(TemplateEntity::getId);
        return this.list(queryWrapper);
    }

    private LambdaQueryWrapper<TemplateEntity> getTemplateQueryWrapper(QueryTemplateQO templateQO,
                                                                       List<String> filterCodeListByGroup,
                                                                       List<String> filterCodeListByItem) {
        Integer contractActionType = null;
        if (null != templateQO.getContractActionType()) {
            contractActionType = templateQO.getContractActionType();
            // 修改-变更主体103使用新增101模板
            if (ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue() == contractActionType) {
                contractActionType = ContractTradeTypeEnum.NEW.getValue();
            }
        }
        if (StringUtils.isNotBlank(templateQO.getUpdatedBy())) {
            templateQO.setUpdatedBy(templateQO.getUpdatedBy().trim());
        }
        LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>()
                .in(!CollectionUtils.isEmpty(templateQO.getTemplateIdList()), TemplateEntity::getId, templateQO.getTemplateIdList())
                .in(StringUtils.isNotBlank(templateQO.getTemplateGroupCode()), TemplateEntity::getCode, filterCodeListByGroup)
                .in(StringUtils.isNotBlank(templateQO.getTemplateItemCode()), TemplateEntity::getCode, filterCodeListByItem)
                .eq(null != templateQO.getStatus(), TemplateEntity::getStatus, templateQO.getStatus())
                .like(StringUtils.isNotBlank(templateQO.getBuCode()), TemplateEntity::getBuCode, templateQO.getBuCode())
                .like(StringUtils.isNotBlank(templateQO.getCompanyCode()), TemplateEntity::getCompanyCode, templateQO.getCompanyCode() + ",")
                .like(null != templateQO.getCategory1(), TemplateEntity::getCategory1, "," + templateQO.getCategory1() + ",")
                .like(null != templateQO.getCategory2(), TemplateEntity::getCategory2, "," + templateQO.getCategory2() + ",")
                .like(null != templateQO.getCategoryId(), TemplateEntity::getCategory2, "," + templateQO.getCategoryId() + ",")
                .like(null != templateQO.getCategory3(), TemplateEntity::getCategory3, "," + templateQO.getCategory3() + ",")
                .eq(null != templateQO.getSalesType(), TemplateEntity::getSalesType, templateQO.getSalesType())
                .eq(StringUtils.isNotBlank(templateQO.getProtocolType()), TemplateEntity::getProtocolType, templateQO.getProtocolType())
                .eq(null != contractActionType, TemplateEntity::getContractActionType, contractActionType)
                .eq(StringUtils.isNotBlank(templateQO.getCustomerCode()), TemplateEntity::getEnterpriseCode, templateQO.getCustomerCode())
                .like(StringUtils.isNotBlank(templateQO.getUpdatedBy()), TemplateEntity::getUpdatedBy, templateQO.getUpdatedBy())
                .between(StringUtils.isNotBlank(templateQO.getStartDay()) && StringUtils.isNotBlank(templateQO.getEndDay()), TemplateEntity::getUpdatedAt, DateTimeUtil.parseTimeStamp0000(templateQO.getStartDay()), DateTimeUtil.parseTimeStamp2359(templateQO.getEndDay()))
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        //关键字搜索模板名称/编码
        if (StringUtils.isNotBlank(templateQO.getSearchKey())) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateEntity::getCode, templateQO.getSearchKey().trim())
                    .or(StringUtils.isNotBlank(templateQO.getSearchKey()))
                    .like(StringUtils.isNotBlank(templateQO.getSearchKey()), TemplateEntity::getName, templateQO.getSearchKey().trim()));
        }
        return queryWrapper;
    }

    /**
     * 根据关键变量，出具匹配合同模板
     *
     * @param keyVariableDTO
     * @return
     */
    public TemplateEntity getTemplateByKeyVariable(KeyVariableDTO keyVariableDTO) {
        LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>()
                .like(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
                .like(TemplateEntity::getCompanyCode, "," + keyVariableDTO.getCompanyCode() + ",")
                .like(TemplateEntity::getCategory2, "," + keyVariableDTO.getCategory2() + ",")
                //第一次按3级品种查
                .like(TemplateEntity::getCategory3, "," + keyVariableDTO.getCategory3() + ",")
                .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
                .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
                .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
                .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(TemplateEntity::getId);
        List<TemplateEntity> templateEntityList = new ArrayList<>();
        templateEntityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(templateEntityList)) {
            //三级品种未匹配到模板，则按二级匹配
            LambdaQueryWrapper<TemplateEntity> commonQueryWrapper = new LambdaQueryWrapper<TemplateEntity>()
                    .like(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
                    .like(TemplateEntity::getCompanyCode, "," + keyVariableDTO.getCompanyCode() + ",")
                    .like(TemplateEntity::getCategory2, "," + keyVariableDTO.getCategory2() + ",")
                    .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
                    .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
                    .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
                    .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                    .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
            templateEntityList = this.list(commonQueryWrapper);
            if (CollectionUtils.isEmpty(templateEntityList)) {
                return null;
            }
        }
        List<TemplateEntity> customerTemplateList = templateEntityList.stream()
                .filter(templateEntity -> {
                    return templateEntity.getCustomerCode().contains(keyVariableDTO.getCustomerCode() + ",");
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerTemplateList)) {
            customerTemplateList = templateEntityList.stream()
                    .filter(templateEntity -> {
                        return keyVariableDTO.getEnterpriseCode().equals(templateEntity.getEnterpriseCode());
                    })
                    .collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(customerTemplateList)) {
            return customerTemplateList.get(0);
        } else {
            //过滤掉特殊集团客户的
            templateEntityList = templateEntityList.stream().filter(templateEntity -> {
                return StringUtils.isBlank(templateEntity.getEnterpriseCode());
            }).collect(Collectors.toList());
            return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);

        }
    }

    /**
     * 根据关键变量，匹配合同模板(部分转月、反点价精准匹配，主体多选 必须精准匹配)
     *
     * @param keyVariableDTO
     * @return
     */
    public TemplateEntity getBindTemplateByKeyVariable(KeyVariableDTO keyVariableDTO) {
        LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>()
                .like(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
                .eq(TemplateEntity::getCompanyCode, keyVariableDTO.getCompanyCode())
//                .eq(TemplateEntity::getCategoryId, keyVariableDTO.getCategoryId())
                .eq(TemplateEntity::getCategory1, keyVariableDTO.getTemplateCategory1())
                .eq(TemplateEntity::getCategory2, keyVariableDTO.getTemplateCategory2())
                .eq(TemplateEntity::getCategory3, keyVariableDTO.getTemplateCategory3())
                .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
                .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
                .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
                .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        List<TemplateEntity> templateEntityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(templateEntityList)) {
            return null;
        }
        List<TemplateEntity> customerTemplateList = templateEntityList.stream()
                .filter(templateEntity -> {
                    return keyVariableDTO.getEnterpriseCode().equals(templateEntity.getEnterpriseCode());
                })
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerTemplateList)) {
            return customerTemplateList.get(0);
        } else {
            //过滤掉特殊集团客户的
            templateEntityList = templateEntityList.stream().filter(templateEntity -> {
                return StringUtils.isBlank(templateEntity.getEnterpriseCode());
            }).collect(Collectors.toList());
            return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
        }
    }

    /**
     * 复制模板，判断关键变量是否重复（先不校验主体，主体多选）
     *
     * @param keyVariableDTO
     * @return
     */
    public List<TemplateEntity> judgeCopyTemplate(KeyVariableDTO keyVariableDTO) {
        LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>()
//                .like(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
//                .eq(TemplateEntity::getCategoryId, keyVariableDTO.getCategoryId())
                .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
                .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
                .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
//                .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(StringUtils.isNotBlank(keyVariableDTO.getEnterpriseCode()), TemplateEntity::getEnterpriseCode, keyVariableDTO.getEnterpriseCode())
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateEntity::getId);
        return this.list(queryWrapper);
    }

    /**
     * 根据模板编号，获取模板
     *
     * @param templateCode 模板编号
     * @param status       模板状态（传空，则不判断状态）
     * @return
     */
    public List<TemplateEntity> getTemplateByCode(String templateCode, Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(TemplateEntity::getCode, templateCode)
                .eq(null != status, TemplateEntity::getStatus, status)
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(TemplateEntity::getId)
        );
    }

    public List<TemplateEntity> getAllTemplateList(Integer status) {
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(null != status, TemplateEntity::getStatus, status)
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public TemplateEntity getTemplateEntityByCode(String templateCode) {
        List<TemplateEntity> templateEntityList = this.list(new LambdaQueryWrapper<TemplateEntity>()
                .eq(TemplateEntity::getCode, templateCode)
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
    }

    /**
     * 根据编号集合，获取有效模板数据集合
     *
     * @param templateCodeList 模板编号集合
     * @return
     */
    public List<TemplateEntity> getTemplateByCodeList(List<String> templateCodeList, Integer status) {
        if (CollectionUtils.isEmpty(templateCodeList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapper<TemplateEntity>()
                .in(TemplateEntity::getCode, templateCodeList)
                .eq(null != status, TemplateEntity::getStatus, status)
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public void updateTemplateMainVersionStatus(List<String> templateCodeList) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .in(TemplateEntity::getCode, templateCodeList)
                .eq(TemplateEntity::getMainVersionStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(TemplateEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(TemplateEntity::getMainVersionStatus, DisableStatusEnum.DISABLE.getValue())
                .set(TemplateEntity::getUpdatedAt, DateTimeUtil.now())
                .update();
    }


//    public TemplateEntity getTemplateByKeyVariable(KeyVariableDTO keyVariableDTO) {
//        LambdaQueryWrapper<TemplateEntity> customerQueryWrapper = new LambdaQueryWrapper<TemplateEntity>()
//                .eq(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
//                .eq(TemplateEntity::getCompanyCode, keyVariableDTO.getCompanyCode())
//                .eq(TemplateEntity::getCategoryId, keyVariableDTO.getCategoryId())
//                .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
//                .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
//                .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
//                .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
//                //客户精准匹配
//                .eq(TemplateEntity::getCustomerCode, keyVariableDTO.getCustomerCode());
//        List<TemplateEntity> customerTemplateEntityList = this.list(customerQueryWrapper);
//        if (!CollectionUtils.isEmpty(customerTemplateEntityList)) {
//            return customerTemplateEntityList.get(0);
//        }
//        LambdaQueryWrapper<TemplateEntity> queryWrapper = new LambdaQueryWrapper<TemplateEntity>()
//                .eq(TemplateEntity::getBuCode, keyVariableDTO.getBuCode())
//                .eq(TemplateEntity::getCompanyCode, keyVariableDTO.getCompanyCode())
//                .eq(TemplateEntity::getCategoryId, keyVariableDTO.getCategoryId())
//                .eq(TemplateEntity::getSalesType, keyVariableDTO.getSalesType())
//                .eq(TemplateEntity::getProtocolType, keyVariableDTO.getProtocolType())
//                .eq(TemplateEntity::getContractActionType, keyVariableDTO.getContractActionType())
//                .eq(TemplateEntity::getStatus, DisableStatusEnum.ENABLE.getValue());
//        queryWrapper.and(QueryWrapper -> QueryWrapper.or().eq(TemplateEntity::getCustomerCode, "0")
//                .or().eq(TemplateEntity::getCustomerCode, "")
//                .or().isNull(TemplateEntity::getCustomerCode)
//        );
//        List<TemplateEntity> templateEntityList = this.list(queryWrapper);
//        return CollectionUtils.isEmpty(templateEntityList) ? null : templateEntityList.get(0);
//    }
}
