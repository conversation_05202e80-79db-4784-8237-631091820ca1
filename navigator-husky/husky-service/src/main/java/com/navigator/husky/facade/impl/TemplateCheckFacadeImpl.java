package com.navigator.husky.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateCheckFacade;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import com.navigator.husky.service.TemplateCheckService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 15:52
 **/
@RestController
public class TemplateCheckFacadeImpl implements TemplateCheckFacade {
    @Resource
    private TemplateCheckService templateCheckService;

    @Override
    public Result queryCheckByCondition(QueryDTO<TemplateCheckEntity> queryDTO) {
        return templateCheckService.queryCheckByCondition(queryDTO);
    }

    @Override
    public Boolean saveTemplateCheck(TemplateCheckEntity checkEntity) {
        return templateCheckService.saveTemplateCheck(checkEntity);
    }

    @Override
    public Boolean updateTemplateCheck(TemplateCheckEntity checkEntity) {
        return templateCheckService.updateTemplateCheck(checkEntity);
    }

    @Override
    public TemplateCheckEntity getTemplateCheckById(Integer id) {
        return templateCheckService.getTemplateCheckById(id);
    }

    @Override
    public Boolean updateCheckStatus(Integer id, Integer status) {
        return templateCheckService.updateCheckStatus(id, status);
    }

    @Override
    public List<TemplateCheckEntity> getCheckListByTemplateCode(String templateCode) {
        return templateCheckService.getCheckListByTemplateCode(templateCode);
    }

    @Override
    public Result queryExportTemplateCheckList(TemplateCheckEntity checkQueryDTO) {
        return Result.success(templateCheckService.queryExportTemplateCheckList(checkQueryDTO));
    }
}
