package com.navigator.husky.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-28 16:37
 **/
public interface TemplateHistoryService {

    Result queryHistoryByCondition(QueryDTO<TemplateHistoryEntity> queryDTO);

    /**
     * 记录模板/条款/条款组-操作记录
     *
     * @param templateHistoryEntity
     */
    void saveTemplateHistory(TemplateHistoryEntity templateHistoryEntity);

    List<EnumValueDTO> getOperationTypeList();
}
