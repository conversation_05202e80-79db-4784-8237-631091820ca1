<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>navigator-husky</artifactId>
        <groupId>com.navigator</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>husky-service</artifactId>

    <dependencies>
        <!-- Spring Boot Begin -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!-- Spring Boot End -->

        <!-- Spring Cloud Begin -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- Spring Cloud End -->

        <!-- sqlserver 相关依赖-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>husky-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>trade-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>koala-facade</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <!-- 打包复制jar到指定目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <!--suppress UnresolvedMavenProperty -->
                                <delete dir="../../deploy/${project.artifactId}"/>
                                <mkdir dir="../../deploy/${project.artifactId}"/>
                                <!--<delete>-->
                                <!--<fileset dir="../../deploy/${project.artifactId}" includes="*.jar" />-->
                                <!--</delete>-->
                                <copy overwrite="true"
                                      tofile="../../deploy/${project.artifactId}/${project.artifactId}-${project.version}-${maven.build.timestamp}.jar"
                                      file="${project.build.directory}/${project.artifactId}-${project.version}-${maven.build.timestamp}.jar"/>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}-${project.version}-${maven.build.timestamp}</finalName>
    </build>
</project>
