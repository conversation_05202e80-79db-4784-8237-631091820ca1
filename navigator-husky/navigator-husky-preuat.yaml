apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-husky-preuat
  namespace: preuat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-husky-preuat
  template:
    metadata:
      labels:
        app: ldc-navigator-husky-preuat
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-husky-preuat:#{Build.BuildId}#
        name: ldc-navigator-husky-preuat
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "preuat" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4nnvgsto001-secret  # required
            shareName: logs-preuat  # required
            server: csm4nnvgsto001.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional
---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-husky-preuat
  namespace: preuat
spec:
  type: ClusterIP
  ports:
  - port: 9111
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-husky-preuat