apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-husky-test
  namespace: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-husky-test
  template:
    metadata:
      labels:
        app: ldc-navigator-husky-test
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-husky-test:#{Build.BuildId}#
        name: ldc-navigator-husky-test
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "uat"

---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-husky-test
  namespace: test
spec:
  type: ClusterIP
  ports:
  - port: 9111
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-husky-test