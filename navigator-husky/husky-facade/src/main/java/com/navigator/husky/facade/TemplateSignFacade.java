package com.navigator.husky.facade;

import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 16:28
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateSignFacade {

    @GetMapping("/matchTemplateByRule")
    Result matchTemplateByRule(@RequestParam(value = "contractSignId") Integer contractSignId);

    @GetMapping("/checkTemplateByRule")
    Result checkTemplateByRule(@RequestParam(value = "contractSignId") Integer contractSignId);

    @GetMapping("/matchTemplateByRuleV2")
    Result matchTemplateByRuleV2(@RequestParam(value = "contractSignId") Integer contractSignId);

    @PostMapping("/provideContractSign")
    String provideContractSign(@RequestBody TemplateEntity templateEntity);
}
