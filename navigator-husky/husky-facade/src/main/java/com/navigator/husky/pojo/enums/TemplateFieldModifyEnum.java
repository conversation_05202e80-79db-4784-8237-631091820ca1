package com.navigator.husky.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-15 15:31
 **/
@AllArgsConstructor
@Getter
public enum TemplateFieldModifyEnum {
    /**
     * 合同类型-修改（修改为1 未修改为0）
     */
    contractTypeModify("contractType", "合同类型"),
    /**
     * 开始交货时间-修改
     */
    deliveryStartTimeModify("deliveryStartTime", "开始交货时间"),
    /**
     * 截止交货时间秀-修改
     */
    deliveryEndTimeModify("deliveryEndTime", "截止交货时间"),
    /**
     * 买方客户-修改
     */
    customerCodeModify("customerCode", "买方客户"),
    /**
     * 赊销账期-修改
     */
    creditDaysModify("creditDays", "赊销账期"),
    /**
     * 袋皮扣重-修改
     */
    packageWeightModify("packageWeight", "袋皮扣重"),
    /**
     * 发货库点-修改
     */
    shipWarehouseIdModify("shipWarehouseId", "发货库点"),
    /**
     * 目的地-修改
     */
    destinationModify("destination", "目的地"),
    /**
     * 溢短装-修改
     */
    weightToleranceModify("weightTolerance", "溢短装"),
    /**
     * 重量检验-修改
     */
    weightCheckModify("weightCheck", "重量检验"),
    /**
     * 含税单价-修改
     */
    unitPriceModify("unitPrice", "含税单价"),
    /**
     * 交提货方式-修改
     */
    deliveryTypeModify("deliveryType", "交提货方式"),
    /**
     * 付款方式-修改
     */
    paymentTypeModify("paymentType", "付款方式"),
    /**
     * 交货工厂-修改
     */
    deliveryFactoryCodeModify("deliveryFactoryCode", "交货工厂"),
    /**
     * 点价截止日期-修改
     */
    priceEndTimeModify("priceEndTime", "点价截止日期"),
    /**
     * 点价截止日期类型-修改
     */
    priceEndTypeModify("priceEndType", "点价截止日期类型"),
    /**
     * 履约保证金释放方式-修改
     */
    depositReleaseTypeModify("depositReleaseType", "履约保证金释放方式"),
    /**
     * 履约保证金比例-修改
     */
    depositRateModify("depositRate", "履约保证金比例"),
    /**
     * 履约保证金金额-修改
     */
    depositAmountModify("depositAmount", "履约保证金金额"),
    /**
     * 履约保证金点价后补缴-修改
     */
    addedDepositRateModify("addedDepositRate", "履约保证金点价后补缴比例"),
    /**
     * 追加履约保证金比例-修改
     */
    addedDepositRate2Modify("addedDepositRate2", "追加履约保证金比例"),
    /**
     * 货品包装-修改
     */
    goodsPackageIdModify("goodsPackageId", "货品包装"),
    /**
     * 货品规格-修改
     */
    goodsSpecIdModify("goodsSpecId", "货品规格"),
    /**
     * 货品包装-修改
     */
    goodsIdModify("goodsId", "货物"),
    /**
     * 用途-修改
     */
    usageModify("usage", "用途"),
    /**
     * 用途-修改
     */
    vipModify("vip", "专属客户"),
    ;
    private String value;
    private String desc;

    public static TemplateFieldModifyEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(fieldModifyEnum -> value.equals(fieldModifyEnum.getValue()))
                .findFirst()
                .orElse(contractTypeModify);
    }
}
