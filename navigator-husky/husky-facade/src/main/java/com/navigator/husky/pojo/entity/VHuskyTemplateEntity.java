package com.navigator.husky.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-11 18:27
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_husky_template")
@ApiModel(value = "VHuskyTemplateEntity对象", description = "")
public class VHuskyTemplateEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板ID")
    private Integer templateId;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型+协议类型+[“_”+客户编码]")
    @Excel(name = "模板编号", orderNum = "1", width = 38)
    private String templateCode;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    @Excel(name = "模板名称", orderNum = "2", width = 45)
    private String templateName;

    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @Excel(name = "业务线", replace = {"现货_ST", "期货_FT"}, orderNum = "3", width = 8)
    private String templateBuCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String templateCompanyCode;

//    /**
//     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
//     */
//    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
//    private Integer templateCategoryId;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String templateCategory1;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String templateCategory2;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String templateCategory3;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @Excel(name = "采销", replace = {"采购_1", "销售_2"}, orderNum = "5", width = 6)
    private Integer templateSalesType;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @Excel(name = "协议类型", replace = {"⼤合同_CONTRACT", "订单_ORDER", "补充协议_AGREEMENT"}, orderNum = "12", width = 12)
    private String templateProtocolType;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private Integer templateContractActionType;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String templateEnterpriseCode;

    @Excel(name = "特殊模版客户", orderNum = "13", width = 15)
    private String templateEnterpriseName;

    @ApiModelProperty(value = "布局格式")
    @Excel(name = "模版样式", replace = {"新增_1", "修改_2"}, orderNum = "14", width = 6)
    private String templateLayout;

    @ApiModelProperty(value = "是否绑定组装模板（0 未绑定 1 组装模板）")
    private Integer bindTemplate;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注", orderNum = "17", width = 20)
    private String templateMemo;

    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer templateMainVersionStatus;
    @ApiModelProperty(value = "正式版本备注")
    private String templateMainVersionDesc;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @Excel(name = "模板状态", replace = {"启用_1", "禁用_0"}, orderNum = "15", width = 6)
    private Integer templateStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "模板-更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "16", width = 25)
    private Date templateUpdatedAt;

    private String templateUpdatedBy;

    @Excel(name = "关联模板编号", orderNum = "18", width = 38)
    private String bindTemplateCode;

    @Excel(name = "关联模板名称", orderNum = "19", width = 45)
    private String bindTemplateName;

    private Integer bindSort;

    //======================== 条款组信息 =====================
    private Integer groupSort;

    private Integer groupId;

    @Excel(name = "条款组编号", orderNum = "21", width = 25)
    private String groupCode;

    @Excel(name = "条款组名称", orderNum = "22", width = 30)
    private String groupName;

    @Excel(name = "标题", orderNum = "23", width = 20)
    private String groupTitle;

    @Excel(name = "引用条款组", orderNum = "24", width = 20)
    private String realGroupCode;

    private Integer groupContractActionType;

    private String groupRuleInfo;

    @Excel(name = "条款组-加载条件", orderNum = "26", width = 36)
    private String groupConditionInfo;

    @Excel(name = "条款组状态", replace = {"启用_1", "禁用_0"}, orderNum = "27", width = 6)
    private Integer groupStatus;
    @Excel(name = "备注", orderNum = "28", width = 15)
    private String groupMemo;

    private Integer groupIsFixed;

    private Integer groupMainVersionStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "条款组-更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "29", width = 28)
    private Date groupUpdatedAt;

    private String groupUpdatedBy;

    //======================== 条款信息 =====================

    private Integer itemId;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型+协议类型+[“_”+客户编码]")
    @Excel(name = "条款编号", orderNum = "46", width = 20)
    private String itemCode;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    @Excel(name = "条款名称", orderNum = "47", width = 25)
    private String itemName;

    @ApiModelProperty(value = "所属条款组编号")
    @Excel(name = "所属条款组", orderNum = "48", width = 20)
    private String itemGroupCode;

    //    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
//    @Excel(name = "品类", orderNum = "49", width = 15)
//    private String itemCategoryId;
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String itemCategory1;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String itemCategory2;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String itemCategory3;

    @Excel(name = "一级品类", orderNum = "51", width = 6)
    private String itemCategoryName1;
    @Excel(name = "二级品类", orderNum = "52", width = 12)
    private String itemCategoryName2;
    @Excel(name = "品种", orderNum = "53", width = 10)
    private String itemCategoryName3;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    @Excel(name = "采销", orderNum = "54", width = 10)
    private String itemSalesType;

    private String itemEnterpriseCode;

    @Excel(name = "集团客户名称", orderNum = "55", width = 25)
    private String itemEnterpriseName;

    @Excel(name = "备注", orderNum = "56", width = 20)
    private String itemMemo;
    @ApiModelProperty(value = "规则drools脚本内容")
    @Excel(name = "加载条件", orderNum = "57", width = 40)
    private String itemConditionInfo;
    @ApiModelProperty(value = "条款内容")
    @Excel(name = "条款正文", orderNum = "58", width = 80)
    private String itemContent;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String itemRuleInfo;
    @Excel(name = "条款状态", replace = {"启用_1", "禁用_0"}, orderNum = "50", width = 12)
    private String itemStatus;
    private String itemMainVersionStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "59", width = 28)
    private Date itemUpdatedAt;

    private String itemUpdatedBy;


    //=================== 拼接信息 ======================
    @TableField(exist = false)
    @Excel(name = "所属主体", orderNum = "4", width = 8)
    private String templateCompanyCodeInfo;

    //    /**
//     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
//     */
//    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
//    @TableField(exist = false)
//    @Excel(name = "品类", orderNum = "5", width = 6)
//    private String templateCategoryName;
    @Excel(name = "一级品类", orderNum = "6", width = 6)
    private String templateCategoryName1;
    @Excel(name = "二级品类", orderNum = "7", width = 12)
    private String templateCategoryName2;
    @Excel(name = "品种", orderNum = "8", width = 10)
    private String templateCategoryName3;
    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @TableField(exist = false)
    @Excel(name = "操作类型", orderNum = "11", width = 18)
    private String templateContractActionTypeInfo;

    @Excel(name = "条款组-操作类型", orderNum = "25", width = 18)
    @TableField(exist = false)
    private String groupContractActionTypeInfo;

}