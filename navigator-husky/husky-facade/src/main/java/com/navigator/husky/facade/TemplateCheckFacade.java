package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 15:41
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateCheckFacade {
    /**
     * 出具模板标识配置-列表查询
     *
     * @param queryDTO 出具模板标识配置-查询条件
     * @return 出具模板标识配置-查询结果
     */
    @PostMapping("/queryCheckByCondition")
    Result queryCheckByCondition(@RequestBody QueryDTO<TemplateCheckEntity> queryDTO);

    /**
     * 新增出具模板标识配置
     *
     * @param checkEntity 出具模板标识配置-信息
     * @return 出具模板标识配置新增结果
     */
    @PostMapping("/saveTemplateCheck")
    Boolean saveTemplateCheck(@RequestBody TemplateCheckEntity checkEntity);

    /**
     * 修改出具模板标识配置信息
     *
     * @param checkEntity 出具模板标识配置信息
     * @return 更新结果
     */
    @PostMapping("/updateTemplateCheck")
    Boolean updateTemplateCheck(@RequestBody TemplateCheckEntity checkEntity);

    @GetMapping("/getTemplateCheckById")
    TemplateCheckEntity getTemplateCheckById(@RequestParam(value = "id") Integer id);

    @GetMapping("/updateCheckStatus")
    Boolean updateCheckStatus(@RequestParam(value = "id") Integer id,
                              @RequestParam(value = "status") Integer status);

    @GetMapping("/getCheckListByTemplateCode")
    List<TemplateCheckEntity> getCheckListByTemplateCode(@RequestParam(value = "templateCode") String templateCode);

    @PostMapping("/queryExportTemplateCheckList")
    Result queryExportTemplateCheckList(@RequestBody TemplateCheckEntity checkQueryDTO);
}
