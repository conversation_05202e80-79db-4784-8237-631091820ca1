package com.navigator.husky.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-13 18:18
 **/
@Data
public class QualityImportDTO {

    @Excel(name = "指标主键id")
    private String id;

    @Excel(name = "二级品类")
    private String category2Name;

    @Excel(name = "工厂")
    private String factoryCode;

    @Excel(name = "用途")
    private String usageInfo;

    @Excel(name = "采/销")
    private String salesTypeInfo;

    @Excel(name = "状态")
    private String status;

//    @Excel(name = "规格")
//    private String specInfo;

    @ApiModelProperty(value = "企标/国标")
    @Excel(name = "企标/国标")
    private String standardType;

    @Excel(name = "货品(SPU名称)")
    private String spuNames;

    @Excel(name = "发货库点编码")
    private String warehouseCodes;

    @Excel(name = "专属客户编码")
    private String customerCodes;

    @Excel(name = "集团客户编码")
    private String enterpriseCodes;

    @Excel(name = "质量指标条款内容")
    private String content;

    @Excel(name = "更新/删除/新增")
    private String operationName;
}
