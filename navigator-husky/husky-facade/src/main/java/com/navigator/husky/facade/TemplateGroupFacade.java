package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 19:04
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateGroupFacade {
    /**
     * 列表分页查询条款组信息
     *
     * @param queryDTO 条款组查询条件
     * @return 条款组查询结果
     */
    @PostMapping("/queryGroupByCondition")
    Result queryGroupByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO);

    /**
     * 新增条款组
     *
     * @param groupEntity 条款组信息
     * @return 条款组新增结果
     */
    @PostMapping("/saveTemplateGroup")
    Boolean saveTemplateGroup(@RequestBody TemplateGroupEntity groupEntity);

    /**
     * 修改条款组信息
     *
     * @param groupEntity 条款组信息
     * @return 更新结果
     */
    @PostMapping("/updateTemplateGroup")
    Boolean updateTemplateGroup(@RequestBody TemplateGroupEntity groupEntity);

    @GetMapping("/updateGroupStatus")
    Boolean updateGroupStatus(@RequestParam(value = "templateGroupId") Integer templateGroupId,
                              @RequestParam(value = "status") Integer status);

    /**
     * 根据ID查询条款组
     *
     * @param templateGroupId 条款组ID
     * @return 条款组详情
     */
    @GetMapping("/getTemplateGroupById")
    TemplateGroupEntity getTemplateGroupById(@RequestParam(value = "templateGroupId") Integer templateGroupId);

    /**
     * 根据编码获取条款组信息
     *
     * @param groupCodeList
     * @param status
     * @return
     */
    @GetMapping("/getTemplateGroupByCodeList")
    Result getTemplateGroupByCodeList(@RequestParam(value = "groupCodeList") List<String> groupCodeList,
                                      @RequestParam(value = "status",required = false) Integer status);

    /**
     * 获取所有条款组
     *
     * @param status
     * @return
     */
    @GetMapping("/getAllGroupList")
    List<TemplateGroupEntity> getAllGroupList(@RequestParam(value = "status", required = false) Integer status);

    /**
     * 导出条款组excel信息
     *
     * @param queryTemplateQO
     * @return
     */
    @PostMapping("/exportGroupExcel")
    Result exportGroupExcel(@RequestBody QueryTemplateQO queryTemplateQO);

    /**
     * 导出条款组Json脚本
     *
     * @param queryTemplateQO
     * @return
     */
    @PostMapping("/exportGroupJson")
    Result exportGroupJson(@RequestBody QueryTemplateQO queryTemplateQO);

    /**
     * 导入条款组Json脚本，并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importGroupJson", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importGroupJson(@RequestPart("file") MultipartFile file);
}
