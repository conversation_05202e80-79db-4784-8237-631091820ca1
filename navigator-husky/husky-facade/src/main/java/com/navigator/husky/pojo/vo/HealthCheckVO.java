package com.navigator.husky.pojo.vo;

import com.navigator.husky.pojo.entity.TemplateItemEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-22 15:54
 **/
@Data
@Accessors(chain = true)
public class HealthCheckVO {
    /**
     * 未配置条款的条款组
     */
    private List<String> groupCodeList;
    /**
     * 未关联条款组的条款
     */
    private List<TemplateItemEntity> itemCodeList;
}
