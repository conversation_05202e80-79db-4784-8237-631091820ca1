package com.navigator.husky.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.entity.TemplateBindEntity;
import com.navigator.husky.pojo.entity.TemplateGroupRelationEntity;
import com.navigator.husky.pojo.entity.TemplateItemRelationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-29 13:59
 **/
@Data
@Accessors(chain = true)
public class TemplateInfoJsonDTO {
    @ApiModelProperty(value = "模板自增ID")
    private Integer id;

    @ApiModelProperty(value = "条款/条款组/模板类型，导入做校验")
    private Integer templateType;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型\n" +
            "+协议类型+[“_”+客户编码]")
    private String code;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    private String name;

    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private Integer categoryId;
    @ApiModelProperty(value = "一级品类")
    private String category1;
    @ApiModelProperty(value = "二级品类")
    private String category2;
    @ApiModelProperty(value = "三级品类")
    private String category3;
    @ApiModelProperty(value = "一级品类名称")
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;
    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    private String customerName;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;

    private String enterpriseName;

    @ApiModelProperty(value = "布局格式")
    private String layout;

    @ApiModelProperty(value = "是否绑定组装模板（0 未绑定 1 组装模板）")
    private Integer bindTemplate;

    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "正式版本号（例：2023001,按年份递增）")
    private String mainVersion;
    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer mainVersionStatus;
    @ApiModelProperty(value = "正式版本备注")
    private String mainVersionDesc;
    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String updatedBy;

    private String createdBy;

    /**
     * 规则匹配返回条款组信息
     */
    private List<TemplateGroupJsonDTO> groupList;

    /**
     * 条款组关系
     */
    private List<TemplateGroupRelationEntity> groupRelationList;

    /**
     * 规则匹配返回条款组信息
     */
    private List<TemplateItemJsonDTO> itemList;

    /**
     * 条款组关系
     */
    private List<TemplateItemRelationEntity> itemRelationList;
    /**
     * 所绑定的模板
     */
    private List<TemplateBindEntity> templateBindEntityList;

}
