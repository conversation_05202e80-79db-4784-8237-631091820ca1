package com.navigator.husky.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-21 17:16
 **/
@Data
public class MarkMainVersionDTO {
    /**
     * 模板编号
     */
    @NotNull(message = "模板编号不能为空")
    private Integer templateId;
    /**
     * 标记为主版本号-描述
     */
    @NotBlank(message = "主版本描述不能为空")
    private String mainVersionDesc;
}
