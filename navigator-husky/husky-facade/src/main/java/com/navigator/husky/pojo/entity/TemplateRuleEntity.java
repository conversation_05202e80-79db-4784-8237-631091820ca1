package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_rule")
@ApiModel(value = "TemplateRuleEntity对象", description = "")
public class TemplateRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "模板编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.TemplateTypeEnum}
     */
    @ApiModelProperty(value = "条款类型（1 条款 2 条款组）")
    private Integer referType;

    @ApiModelProperty(value = "条件变量(conditionType、)")
    private String conditionVariable;

    @ApiModelProperty(value = "加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）")
    private String conditionInfo;

    @ApiModelProperty(value = "加载规则信息（drools脚本）")
    private String ruleInfo;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    /**
     * 条件变量规则
     */
    @TableField(exist = false)
    private List<ConditionVariableDTO> conditionVariableList;

    @TableField(exist = false)
    private List<TemplateRuleDetailEntity> ruleDetailEntityList;

}
