package com.navigator.husky.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-29 14:09
 **/
@Data
@Accessors(chain = true)
public class TemplateItemJsonDTO {
    @ApiModelProperty(value = "模板自增ID")
    private Integer id;

    @ApiModelProperty(value = "条款/条款组/模板类型，导入做校验")
    private Integer templateType;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型+协议类型+[“_”+客户编码]")
    private String code;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    private String name;

    @ApiModelProperty(value = "所属条款组编号")
    private String templateGroupCode;

    @ApiModelProperty(value = "条款内容")
    private String content;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String ruleInfo;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String categoryId;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private String salesType;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    @ApiModelProperty(value = "操作类型")
    private String contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    private String customerName;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;

    private String enterpriseName;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "是否需要序号(0不需要 1需要序号)")
    private Integer needNum;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "是否可修改（0不可修改 1可以修改）")
    private Integer canModify;

    @ApiModelProperty(value = "一级品类")
    private String category1;
    @ApiModelProperty(value = "二级品类")
    private String category2;
    @ApiModelProperty(value = "三级品类")
    private String category3;
    @ApiModelProperty(value = "一级品类名称")
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;
    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;

    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer mainVersionStatus;
    /**
     * 条款/条款组-是否为固定
     */
    @ApiModelProperty(value = "是否为固定(0非固定 1 固定)")
    private Integer isFixed;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String createdBy;

    private String updatedBy;

    /**
     * 条款-加载条件-规则
     */
    private TemplateRuleEntity templateRule;
}
