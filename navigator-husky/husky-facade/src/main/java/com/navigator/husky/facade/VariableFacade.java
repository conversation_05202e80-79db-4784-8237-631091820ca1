package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.entity.VariableEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 16:26
 **/
@FeignClient(value = "navigator-husky-service")
public interface VariableFacade {
    @PostMapping("/updateVariable")
    Boolean updateVariable(@RequestBody VariableEntity variableEntity);

    /**
     * 根据条件查询条件/关键变量
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryVariableByCondition")
    Result queryVariableByCondition(@RequestBody QueryDTO<VariableEntity> queryDTO);

    @PostMapping("/queryExportVariableList")
    Result queryExportVariableList(@RequestBody VariableEntity queryDTO);

    /**
     * 获取所有变量信息
     *
     * @return
     */
    @GetMapping("/getAllVariableList")
    List<VariableEntity> getAllVariableList(@RequestParam(value = "isCondition", required = false) Integer isCondition,
                                            @RequestParam(value = "isKey", required = false) Integer isKey);

    @GetMapping("/getAllVariableBasicList")
    List<VariableEntity> getAllVariableBasicList();

    @GetMapping("/getNotUsedVariableList")
    Result getNotUsedVariableList();

    @PostMapping(value = "/importVariableInfo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importVariableInfo(@RequestPart("file") MultipartFile file);

}
