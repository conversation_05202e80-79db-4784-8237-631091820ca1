package com.navigator.husky.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-11 18:42
 **/
@AllArgsConstructor
@Getter
public enum VariableBizCodeEnum {
    CATEGORY1("category1", "一级品类"),
    CATEGORY2("category2", "二级品类"),
    CATEGORY3("category3", "三级品种"),
    CATEGORY_ID("categoryId", "二级品种"),
    DELIVERY_FACTORY_CODE("deliveryFactoryCode", "交货工厂编码"),
    SPEC_ID("specId", "货品规格"),
    STRUCTURE_TYPE("structureType", "结构化定价类型"),
    SPU_NAME("spuName", "spu名称"),
    PACKAGE_NAME("packageName", "货品包装"),

    //================== 质量指标规则 ====================
    WAREHOUSE("warehouse", "发货库点"),
    CUSTOMER("customer", "专属客户"),
    ENTERPRISE("enterprise", "集团客户"),
    COMPANY_CODE("companyCode", "公司主体"),
    SPU("spu", "货品spu"),
    ;
    private String value;
    private String desc;

    public static VariableBizCodeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(variableBizCodeEnum -> value.equals(variableBizCodeEnum.getValue()))
                .findFirst()
                .orElse(DELIVERY_FACTORY_CODE);
    }
}
