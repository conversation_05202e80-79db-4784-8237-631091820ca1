package com.navigator.husky.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-26 18:33
 **/
@Accessors(chain = true)
@Data
public class TemplateRelationSyncDTO {
    /**
     * 需要同步信息的模板编码
     */
    private String templateCode;
    /**
     * 1：部分转月/反点价； 2：暂定价定价/拆分合同(客户主体变化）/变更
     * {@link ContractTradeTypeEnum}
     */
    private Integer syncType;

    private List<TemplateCodeDTO> templateCodeDTOList;


    @Data
    @Accessors(chain = true)
    public static class TemplateCodeDTO{
        /**
         * 补充协议的模板编号
         * 或者需要同步的模板编号（已关联条款组和条款）
         */
        private String syncTemplateCode;

        //========================部分转月、反点价需要传下面三个字段
        /**
         * 分页的条款组编码
         */
        private String splitPageGroupCode;
        /**
         * 分页的条款编码
         */
        private String splitPageItemCode;
        /**
         * 新增的模板编码
         */
        private String addTemplateCode;
    }

}
