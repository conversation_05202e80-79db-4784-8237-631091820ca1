package com.navigator.husky.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-22 11:07
 **/
@Data
public class CopyTemplateDTO {
    /**
     * 需要复制的模板编号
     */
    @NotNull(message = "需要复制的模板ID不能为空")
    private Integer templateId;
    @ApiModelProperty(value = "业务线（ST/WT）")
    private List<String> buCodeList;
    /**
     * 所属主体集合
     */
    @NotEmpty(message = "所属主体不能为空")
    private List<String> companyCodeList;

    @ApiModelProperty(value = "一级品类")
    private List<Integer> category1List;

    @ApiModelProperty(value = "二级品类")
    private List<Integer> category2List;

    @ApiModelProperty(value = "复制模板的品种")
    private List<Integer> category3List;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;
}
