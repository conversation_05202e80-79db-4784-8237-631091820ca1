package com.navigator.husky.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <AUTHOR> NaNa
 * @since : 2023-07-04 15:23
 **/
@Data
@Accessors(chain = true)
public class QueryTemplateQO {
    /**
     * 模板名称/编码
     */
    @ApiModelProperty(value = "搜索关键字:模板名称/编码")
    private String searchKey;

    @ApiModelProperty(value = "条款组编码")
    private String templateGroupCode;

    @ApiModelProperty(value = "条款编码")
    private String templateItemCode;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    private Integer status;

    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private Integer categoryId;
    @ApiModelProperty(value = "二级品种")
    private Integer category2;
    @ApiModelProperty(value = "三级品种")
    private Integer category3;
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private Integer salesType;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "所属客户")
    private String customerCode;
    /**
     * 条款/模板。所属集团客户（单选）
     */
    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;

    //================新增字段=======================
    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer mainVersionStatus;
    /**
     * 条款/条款组-是否为固定
     */
    @ApiModelProperty(value = "是否为固定(0非固定 1 固定)")
    private Integer isFixed;
    /**
     * 条款
     */
    @ApiModelProperty(value = "条款内容")
    private String content;
    /**
     * 条款
     */
    @ApiModelProperty(value = "条款所属模板")
    private String templateCode;
    @ApiModelProperty(value = "更新人")
    private String updatedBy;
    @ApiModelProperty(value = "更新开始时间")
    private String startDay;
    @ApiModelProperty(value = "更新结束时间")
    private String endDay;

    /**
     * 实际取值条款组编码
     */
    @ApiModelProperty(value = "实际取值条款组编码")
    private String realGroupCode;

    private List<Integer> itemIdList;

    private List<Integer> groupIdList;

    private List<Integer> templateIdList;
}
