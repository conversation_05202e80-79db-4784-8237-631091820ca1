package com.navigator.husky.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import com.navigator.husky.pojo.dto.ItemInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_group")
@ApiModel(value = "TemplateGroupEntity对象", description = "")
public class TemplateGroupEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型+协议类型+[“_”+客户编码]")
    @Excel(name = "条款组编码", orderNum = "1", width = 25)
    private String code;
    /**
     * 实际取值条款组编码
     */
    @ApiModelProperty(value = "实际取值条款组编码")
    @Excel(name = "引用条款组", orderNum = "5", width = 25)
    private String realGroupCode;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    @Excel(name = "条款组名称", orderNum = "2", width = 30)
    private String name;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String ruleInfo;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "标题")
    @Excel(name = "标题", orderNum = "3", width = 15)
    private String title;

    @ApiModelProperty(value = "是否需要序号(0不需要 1需要序号)")
    @Excel(name = "是否有序号", replace = {"有_1", "无_0"}, orderNum = "6", width = 6)
    private Integer needNum;

    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    @Excel(name = "正式版本状态", replace = {"正式_1", "非正式_0"}, orderNum = "14", width = 6)
    private Integer mainVersionStatus;
    /**
     * 条款/条款组-是否为固定
     */
    @ApiModelProperty(value = "是否为固定(0非固定 1 固定)")
    @Excel(name = "是否为固定", replace = {"是_1", "否_0"}, orderNum = "15", width = 6)
    private Integer isFixed;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注", orderNum = "7", width = 10)
    private String memo;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    @Excel(name = "状态", replace = {"启用_1", "禁用_0"}, orderNum = "9", width = 6)
    private Integer status;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "17", width = 25)
    private Date updatedAt;

    @Excel(name = "更新人", orderNum = "16", width = 10)
    private String updatedBy;

    private String createdBy;

    @TableField(exist = false)
    private Integer sort;
    @TableField(exist = false)
    private String searchKey;
    /**
     * 加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
     */
    @Excel(name = "加载条件", orderNum = "8", width = 50)
    @TableField(exist = false)
    private String conditionInfo;
    /**
     * 条件变量规则
     */
    @TableField(exist = false)
    private List<ConditionVariableDTO> conditionVariableList;
    /**
     * 规则匹配返回条款信息
     */
    @TableField(exist = false)
    private List<TemplateItemEntity> itemList;

    @TableField(exist = false)
    private String templateHtml;
    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @Excel(name = "操作类型", orderNum = "4", width = 10)
    @TableField(exist = false)
    private String contractActionTypeInfo;
    /**
     * 出具提交，前端传参（新增/修改，传true）
     */
    @TableField(exist = false)
    private Boolean modify;
    @Excel(name = "关联模板", orderNum = "13", width = 30)
    @TableField(exist = false)
    private String bindTemplateCodes;

    @ExcelCollection(name = "条款信息", orderNum = "10")
    @TableField(exist = false)
    private List<ItemInfoDTO> itemInfoList;

    @ApiModelProperty(value = "模板主版本号")
    @TableField(exist = false)
    private String mainVersion;

    @ApiModelProperty(value = "标记为主版本备注信息")
    @TableField(exist = false)
    private String mainVersionDesc;

}
