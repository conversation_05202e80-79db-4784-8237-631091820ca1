package com.navigator.husky.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.TemplateGroupRelationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@Accessors(chain = true)
public class TemplateExportVO {

    private Integer id;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型协议类型+[“_”+客户编码]")
    @Excel(name = "模版编号")
    private String code;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    @Excel(name = "模版名称")
    private String name;
    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @Excel(name = "业务线")
    private String buInfo;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）名称")
    @Excel(name = "所属主体")
    private String companyName;

    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    @Excel(name = "品类")
    private String categoryName;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @Excel(name = "采销")
    private String salesTypeInfo;
    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @Excel(name = "操作类型")
    private String contractActionTypeInfo;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @Excel(name = "协议类型")
    private String protocolTypeInfo;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    @Excel(name = "特殊模版客户")
    private String enterpriseName;

    @ApiModelProperty(value = "布局格式")
    @Excel(name = "模版样式")
    private String layout;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @Excel(name = "状态")
    private String statusInfo;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String memo;

    /**
     * 拼好的模板内容
     */
    @Excel(name = "所属条款组")
    private String groupListInfo;

    /**
     * 协议ID
     */
    @Excel(name = "关联条款")
    private String itemListInfo;

    @Excel(name = "更新人")
    private String updatedBy;

    @Excel(name = "更新时间")
    private String updatedAt;
}
