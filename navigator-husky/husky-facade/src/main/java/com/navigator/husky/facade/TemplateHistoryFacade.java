package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-07 18:42
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateHistoryFacade {
    /**
     * 列表分页检索条款
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryHistoryByCondition")
    Result queryHistoryByCondition(@RequestBody QueryDTO<TemplateHistoryEntity> queryDTO);

    @GetMapping("/getOperationTypeList")
    List<EnumValueDTO> getOperationTypeList();
}
