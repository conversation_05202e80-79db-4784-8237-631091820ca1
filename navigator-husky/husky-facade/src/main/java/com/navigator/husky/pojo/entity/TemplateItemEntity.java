package com.navigator.husky.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_item")
@ApiModel(value = "TemplateItemEntity对象", description = "")
public class TemplateItemEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型\n" +
            "+协议类型+[“_”+客户编码]")
    @Excel(name = "条款编号", orderNum = "1", width = 10)
    private String code;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    @Excel(name = "条款名称", orderNum = "2", width = 15)
    private String name;

    @ApiModelProperty(value = "所属条款组编号")
    @Excel(name = "所属条款组", orderNum = "3", width = 10)
    private String templateGroupCode;

    @ApiModelProperty(value = "条款内容")
    private String content;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String ruleInfo;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String categoryId;

    @ApiModelProperty(value = "一级品类")
    private String category1;
    @ApiModelProperty(value = "二级品类")
    private String category2;
    @ApiModelProperty(value = "品种")
    private String category3;
    @ApiModelProperty(value = "一级品类名称")
    @Excel(name = "一级品类", orderNum = "4", width = 6)
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    @Excel(name = "二级品类", orderNum = "5", width = 16)
    private String category2Name;
    @Excel(name = "品种", orderNum = "6", width = 20)
    @ApiModelProperty(value = "品种名称")
    private String category3Name;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private String salesType;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    @ApiModelProperty(value = "操作类型")
    private String contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    private String customerName;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;

    @Excel(name = "客户集团名称", orderNum = "8", width = 10)
    private String enterpriseName;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "是否需要序号(0不需要 1需要序号)")
    private Integer needNum;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "是否可修改（0不可修改 1可以修改）")
    private Integer canModify;

    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    @Excel(name = "正式版本状态", replace = {"正式_1","非正式_0"}, orderNum = "12", width = 6)
    private Integer mainVersionStatus;
    /**
     * 条款/条款组-是否为固定
     */
    @ApiModelProperty(value = "是否为固定(0非固定 1 固定)")
    @Excel(name = "是否为固定条款", replace = {"是_1","否_0"}, orderNum = "11", width = 6)
    private Integer isFixed;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注", orderNum = "10", width = 12)
    private String memo;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    @Excel(name = "状态", replace = {"启用_1","禁用_0"}, orderNum = "9", width = 6)
    private Integer status;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "16", width = 15)
    private Date updatedAt;

    private String createdBy;

    @Excel(name = "更新人", orderNum = "15", width = 10)
    private String updatedBy;

    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @TableField(exist = false)
    private String buInfo;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）名称")
    @TableField(exist = false)
    private String companyName;

    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
//    @Excel(name = "品类", orderNum = "5", width = 6)
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "一级品类")
    @TableField(exist = false)
    private List<Integer> category1List;

    @ApiModelProperty(value = "二级品类")
    @TableField(exist = false)
    private List<Integer> category2List;

    @ApiModelProperty(value = "三级品类")
    @TableField(exist = false)
    private List<Integer> category3List;
    @ApiModelProperty(value = "多个二级+三级，编辑回显")
    @TableField(exist = false)
    private Map<Integer, List<Integer>> category3Map;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @Excel(name = "采销", orderNum = "7", width = 6)
    @TableField(exist = false)
    private String salesTypeInfo;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @TableField(exist = false)
    private String protocolTypeInfo;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @TableField(exist = false)
    private String contractActionTypeInfo;

    /**
     * 加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
     */
    @Excel(name = "加载条件", orderNum = "12", width = 15)
    @TableField(exist = false)
    private String conditionInfo;
    /**
     * 条件变量规则
     */
    @TableField(exist = false)
    private List<ConditionVariableDTO> conditionVariableList;
    /**
     * 渲染业务数据后的条款内容（出具展示用）
     */
    @Excel(name = "条款正文", orderNum = "13", width = 30)
    @TableField(exist = false)
    private String contentInfo;

    /**
     * 所属条款组名称
     */
    @TableField(exist = false)
    private String templateGroupName;
    /**
     * ===================关键变量多选====================
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @TableField(exist = false)
    private List<String> buCodeList;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    @TableField(exist = false)
    private List<String> companyCodeList;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    @TableField(exist = false)
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    @TableField(exist = false)
    private List<Integer> salesTypeList;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @TableField(exist = false)
    private List<String> protocolTypeList;

    @ApiModelProperty(value = "操作类型")
    @TableField(exist = false)
    private List<Integer> contractActionTypeList;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    @TableField(exist = false)
    private List<String> customerCodeList;

//    @Excel(name = "可修改", orderNum = "9", width = 6)
    @TableField(exist = false)
    private String canModifyInfo;

    @Excel(name = "关联模板", orderNum = "14", width = 20)
    @TableField(exist = false)
    private String bindTemplateCodes;

    @ApiModelProperty(value = "模板主版本号")
    @TableField(exist = false)
    private String mainVersion;

    @ApiModelProperty(value = "标记为主版本备注信息")
    @TableField(exist = false)
    private String mainVersionDesc;

    @TableField(exist = false)
//    @Excel(name = "条款组标题", orderNum = "4", width = 15)
    private String templateGroupTitle;
}
