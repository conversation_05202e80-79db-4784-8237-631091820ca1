package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_a_modify_matrix")
@ApiModel(value = "TemplateModify对象", description = "")
public class TemplateModifyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规则编号")
    private String tcode;

    @ApiModelProperty(value = "模板编号")
    private String mcode;
    private String rcode;

    private  String a;
    private  String b;
    private  String c;
    private  String d;
    private  String e;
    private  String f;
    private  String g;
    private  String h;
    private  String i;
    private  String j;
    private  String k;
    private  String l;
    private  String m;
    private  String n;
    private  String o;
    private  String p;
    private  String q;
    private  String r;
    private  String s;
    private  String t;
    private  String u;
    private  String v;
    private  String w;
    private  String x;
    private  String y;
    private  String content;
    private  String ruleinfo;

}
