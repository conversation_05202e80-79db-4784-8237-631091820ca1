package com.navigator.husky.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.dto.ConditionVariableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-24 18:46
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_check")
@ApiModel(value = "TemplateCheckEntity对象", description = "")
public class TemplateCheckEntity {

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
//    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
////    @NotNull(message = "业务品类不能为空")
//    private Integer categoryId;
    @ApiModelProperty(value = "一级品类")
    @NotNull(message = "一级品类不能为空")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    @NotNull(message = "二级品类不能为空")
    private Integer category2;
    @ApiModelProperty(value = "三级品类")
    @NotNull(message = "品种不能为空")
    private Integer category3;
    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    @NotNull(message = "采销类型不能为空")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "模板编号")
    @Excel(name = "模板编号", orderNum = "6", width = 25)
    private String templateCode;

    @ApiModelProperty(value = "模板名称")
    @Excel(name = "模板名称", orderNum = "7", width = 25)
    private String templateName;

    @ApiModelProperty(value = "规则脚本")
    private String ruleInfo;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    @ApiModelProperty(value = "规则描述")
    @Excel(name = "检查条件", orderNum = "8", width = 50)
    private String conditionInfo;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    @Excel(name = "状态", replace = {"启用_1", "禁用_0"}, orderNum = "9", width = 6)
    private Integer status;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;


    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @Excel(name = "更新人", orderNum = "10", width = 15)
    private String updatedBy;

    private String createdBy;

    /**
     * 条件变量规则
     */
    @TableField(exist = false)
    private List<ConditionVariableDTO> conditionVariableList;

    @ApiModelProperty(value = "一级品类名称")
    @TableField(exist = false)
    @Excel(name = "一级品类", orderNum = "1", width = 15)
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    @TableField(exist = false)
    @Excel(name = "二级品类", orderNum = "2", width = 15)
    private String category2Name;
    @ApiModelProperty(value = "三级品类名称")
    @TableField(exist = false)
    @Excel(name = "品种", orderNum = "3", width = 15)
    private String category3Name;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @Excel(name = "采销", orderNum = "4", width = 15)
    @TableField(exist = false)
    private String salesTypeInfo;
    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @Excel(name = "操作类型", orderNum = "5", width = 15)
    @TableField(exist = false)
    private String contractActionTypeInfo;

    @TableField(exist = false)
    private String statusInfo;

    @Excel(name = "更新时间", orderNum = "11", width = 15)
    @TableField(exist = false)
    private String updateTime;

    @TableField(exist = false)
    private Integer templateId;
}
