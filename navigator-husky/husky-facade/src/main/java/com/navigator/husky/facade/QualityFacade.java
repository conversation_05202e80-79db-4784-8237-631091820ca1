package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.vo.QualityExportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 11:50
 **/
@Api(tags = "质量指标")
@FeignClient(value = "navigator-husky-service")
public interface QualityFacade {
    /**
     * 判断质量指标是否存在（TT新增/合同变更）
     *
     * @param qualityInfoDTO 质量指标条件信息
     * @return
     */
    @PostMapping("/judgeExistQuality")
    Boolean judgeExistQuality(@RequestBody QualityInfoDTO qualityInfoDTO);

    /**
     * 数字合同匹配质量指标条款
     *
     * @param qualityInfoDTO 质量指标规则信息
     * @return 匹配的质量指标
     */
    @PostMapping("/matchQuality")
    QualityEntity matchQuality(@RequestBody QualityInfoDTO qualityInfoDTO);

    /**
     * 列表查询模板信息
     *
     * @param queryDTO 模板查询条件
     * @return 查询模板分页结果
     */
    @PostMapping("/queryQualityByCondition")
    Result queryQualityByCondition(@RequestBody QueryDTO<QualityInfoDTO> queryDTO);

    /**
     * 新增模板信息
     *
     * @param qualityEntity 模板信息
     * @return
     */
    @ApiOperation("新增质量指标")
    @PostMapping("/saveQuality")
    Result<Boolean> saveQuality(@RequestBody QualityEntity qualityEntity);

    @ApiOperation("更新质量指标")
    @PostMapping("/updateQuality")
    Result<Boolean> updateQuality(@RequestBody QualityEntity qualityEntity);

    @GetMapping("/updateQualityStatus")
    Boolean updateQualityStatus(@RequestParam(value = "qualityId") Integer qualityId,
                                @RequestParam(value = "status") Integer status);

    @GetMapping("/getQualityById")
    QualityEntity getQualityById(@RequestParam(value = "qualityId") Integer qualityId);

    @PostMapping("/exportQualityList")
    List<QualityExportVO> exportQualityList(@RequestBody QualityInfoDTO qualityInfoDTO);

    @PostMapping("/importQuality")
    Result importQuality(@RequestParam("file") MultipartFile uploadFile);

    /**
     * 处理历史数据
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "处理历史数据")
    @PostMapping("/quality/processHistoryData")
    Result processHistoryData(@RequestParam(required = false) Integer id);
}
