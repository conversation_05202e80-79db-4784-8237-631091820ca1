package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.*;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.entity.VHuskyTemplateEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.pojo.vo.HealthCheckVO;
import com.navigator.husky.pojo.vo.TemplateItemRelationVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 19:04
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateFacade {
    /**
     * 列表查询模板信息
     *
     * @param queryDTO 模板查询条件
     * @return 查询模板分页结果
     */
    @PostMapping("/queryByCondition")
    Result queryByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO);


    /**
     * 新增模板信息
     *
     * @param templateEntity 模板信息
     * @return
     */
    @PostMapping("/saveTemplate")
    Result<Boolean> saveTemplate(@RequestBody TemplateEntity templateEntity);

    @PostMapping("/updateTemplate")
    Result<Boolean> updateTemplate(@RequestBody TemplateEntity templateEntity);

    /**
     * 复制模板功能
     *
     * @param copyTemplateDTO
     * @return 模板复制结果
     */
    @PostMapping("/copyTemplateInfo")
    Result copyTemplateInfo(@RequestBody CopyTemplateDTO copyTemplateDTO);

    @GetMapping("/updateTemplateStatus")
    Result updateTemplateStatus(@RequestParam(value = "templateId") Integer templateId,
                                 @RequestParam(value = "status") Integer status);

    @GetMapping("/getTemplateById")
    TemplateEntity getTemplateById(@RequestParam(value = "templateId") Integer templateId);
    /**
     * 获取所有模板
     * @param status
     * @return
     */
    @GetMapping("/getAllTemplateList")
    List<TemplateEntity> getAllTemplateList(@RequestParam(value = "status", required = false) Integer status);

    /**
     * 根据条款组编码，获取关联的所有模板
     *
     * @param templateGroupCode 条款组编码
     * @return 关联的模板信息
     */
    @GetMapping("/getTemplateListByGroupCode")
    List<TemplateEntity> getTemplateListByGroupCode(@RequestParam(value = "templateGroupCode") String templateGroupCode);

    /**
     * 绑定模板条款的关系
     *
     * @param itemRelationDTO 模板条款的绑定关系
     * @return 绑定结果
     */
    @PostMapping("/bindTemplateItemRelation")
    Result bindTemplateItemRelation(@RequestBody TemplateItemRelationDTO itemRelationDTO);

    /**
     * 移除模板-条款绑定关系（根据模板编码/条款编码）
     *
     * @param templateCode     模板编码
     * @param templateItemCode 条款编码
     * @return 模板-条款绑定关系
     */
    @GetMapping("/removeTemplateItemRelation")
    Result removeTemplateItemRelation(@RequestParam(value = "templateCode") String templateCode,
                                      @RequestParam(value = "templateItemCode") String templateItemCode);

    /**
     * 获取模板-条款绑定关系（根据模板编码/条款编码）
     *
     * @param templateCode     模板编码
     * @param templateItemCode 条款编码
     * @return 模板-条款绑定关系
     */
    @GetMapping("/getTemplateItemRelation")
    List<TemplateItemRelationVO> getTemplateItemRelation(@RequestParam(value = "templateCode", required = false) String templateCode,
                                                         @RequestParam(value = "templateItemCode", required = false) String templateItemCode);

    /**
     * 页面选择模板套模板的功能（部分转月、反点价）
     *
     * @param templateRelationSyncList
     * @return
     */
    @PostMapping("/syncTemplateRelation")
    Result syncTemplateRelation(@RequestBody List<TemplateRelationSyncDTO> templateRelationSyncList);

    @PostMapping("/markMainVersion")
    Result markMainVersion(@RequestBody MarkMainVersionDTO markMainVersionDTO);

    /**
     * 健康检测
     *
     * @param templateId
     * @return
     */
    @GetMapping("/healthCheck")
    HealthCheckVO healthCheck(@RequestParam(value = "templateId") Integer templateId);

    /**
     * 根据条件查询需要导出的模板数据
     *
     * @param templateQO 模板查询条件
     * @return
     */
    @PostMapping("/exportTemplateExcel")
   Result exportTemplateExcel(@RequestBody QueryTemplateQO templateQO);

    /**
     * 导出模板
     *
     * @param templateQO
     * @return
     */
    @PostMapping("/exportTemplateJson")
    Result exportTemplateJson(@RequestBody QueryTemplateQO templateQO);

    /**
     * 导入模板脚本并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importTemplateJson", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importTemplateJson(@RequestPart("file") MultipartFile file);

}
