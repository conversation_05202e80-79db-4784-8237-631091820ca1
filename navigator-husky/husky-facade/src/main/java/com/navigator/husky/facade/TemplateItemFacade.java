package com.navigator.husky.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.pojo.dto.TemplateItemJsonDTO;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-06-30 19:04
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateItemFacade {
    /**
     * 列表分页检索条款
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryItemByCondition")
    Result queryItemByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO);

    /**
     * 新增条款
     *
     * @param itemEntity 条款信息
     * @return
     */
    @PostMapping("/saveTemplateItem")
    Boolean saveTemplateItem(@RequestBody TemplateItemEntity itemEntity);

    /**
     * 编辑更新条款
     *
     * @param itemEntity 条款信息
     * @return
     */
    @PostMapping("/updateTemplateItem")
    Result updateTemplateItem(@RequestBody TemplateItemEntity itemEntity);

    /**
     * 根据ID查询条款信息
     *
     * @param itemId 条款ID
     * @return 条款信息
     */
    @GetMapping("/getTemplateItemById")
    TemplateItemEntity getTemplateItemById(@RequestParam(value = "itemId") Integer itemId);

    @GetMapping("/getAllItemList")
    List<TemplateItemEntity> getAllItemList(@RequestParam(value = "status", required = false) Integer status);

    /**
     * 根据条款组编码，获取相关联的条款信息
     *
     * @param templateGroupCode 条款组编码
     * @return 相关联的条款信息
     */
    @GetMapping("/getItemListByGroupCode")
    List<TemplateItemEntity> getItemListByGroupCode(@RequestParam(value = "templateGroupCode") String templateGroupCode);

    @PostMapping("/exportItemExcel")
    Result exportItemExcel(@RequestBody QueryTemplateQO queryTemplateQO);

    /**
     * 导出条款Json脚本
     *
     * @param queryTemplateQO
     * @return
     */
    @PostMapping("/exportItemJson")
    Result exportItemJson(@RequestBody QueryTemplateQO queryTemplateQO);

    /**
     * 导入条款Json脚本，并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importItemJson", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = {MediaType.APPLICATION_JSON_VALUE})
    Result importItemJson(@RequestPart("file") MultipartFile file);
}
