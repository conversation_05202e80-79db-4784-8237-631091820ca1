package com.navigator.husky.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-08 14:15
 **/
@AllArgsConstructor
@Getter
public enum VersionType {
    TEMPORARY(1, "临时版本"),
    MAIN_VERSION(2, "正式版本"),
    ;

    private Integer value;
    private String desc;

    public static VersionType getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(versionType -> value.equals(versionType.getValue()))
                .findFirst()
                .orElse(TEMPORARY);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
