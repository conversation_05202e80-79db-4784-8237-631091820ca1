package com.navigator.husky.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-22 17:21
 **/
@Data
@Accessors(chain = true)
public class QualityExportVO {
    @Excel(name = "品类", orderNum = "1", width = 10)
    private String goodsCategoryCode;

    @Excel(name = "交货工厂", orderNum = "2", width = 12)
    private String factoryCode;

    @Excel(name = "发货库点", orderNum = "3", width = 30)
    private String warehouseName;

    @Excel(name = "用途", orderNum = "4", width = 6)
    private String usageInfo;

//    @Excel(name = "规格", orderNum = "5", width = 6)
//    private String specName;
    @Excel(name = "货品SPU", orderNum = "5", width = 20)
    private String spuNames;

//    @Excel(name = "货品", orderNum = "6", width = 6)
//    private String skuNames;

    @Excel(name = "采/销", orderNum = "6", width = 6)
    private String salesTypeInfo;

    @ApiModelProperty(value = "国企标准")
    @Excel(name = "企标/国标", orderNum = "7", width = 6)
    private String standardType;

    @Excel(name = "专属客户", orderNum = "8", width = 15)
    private String customerNames;

    @Excel(name = "专属客户集团", orderNum = "9", width = 15)
    private String enterpriseName;

    @Excel(name = "对应条款内容", orderNum = "10", width = 35)
    private String content;

    @Excel(name = "状态", orderNum = "11", width = 6)
    private String statusInfo;

    @Excel(name = "更新人", orderNum = "12", width = 10)
    private String updatedBy;

    @Excel(name = "更新时间", orderNum = "13", width = 15, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
