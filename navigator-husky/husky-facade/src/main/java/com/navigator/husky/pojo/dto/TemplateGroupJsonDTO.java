package com.navigator.husky.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.entity.TemplateRuleEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-29 14:09
 **/
@Data
@Accessors(chain = true)
public class TemplateGroupJsonDTO {
    @ApiModelProperty(value = "模板自增ID")
    private Integer id;

    @ApiModelProperty(value = "条款/条款组/模板类型，导入做校验")
    private Integer templateType;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型\n" +
            "+协议类型+[“_”+客户编码]")
    private String code;
    /**
     * 实际取值条款组编码
     */
    @ApiModelProperty(value = "实际取值条款组编码")
    private String realGroupCode;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    private String name;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String ruleInfo;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "是否需要序号(0不需要 1需要序号)")
    private Integer needNum;

    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer mainVersionStatus;
    /**
     * 条款/条款组-是否为固定
     */
    @ApiModelProperty(value = "是否为固定(0非固定 1 固定)")
    private Integer isFixed;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String updatedBy;

    private String createdBy;

    /**
     * 条款组-加载条件-规则
     */
    private TemplateRuleEntity templateRule;

}
