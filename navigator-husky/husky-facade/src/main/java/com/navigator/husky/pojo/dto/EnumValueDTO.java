package com.navigator.husky.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-26 11:12
 **/
@Data
@Accessors(chain = true)
public class EnumValueDTO {
    private String enumName;
    private Integer value;
    @Excel(name = "选项值代码", orderNum = "18", width = 15)
    private String code;
    @Excel(name = "选项值名称", orderNum = "17", width = 15)
    private String desc;
    //dbz_dict_item：的memo字段
    private String memo;
}
