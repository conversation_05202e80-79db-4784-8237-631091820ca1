package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.TemplateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_history_log")
@ApiModel(value = "TemplateHistoryLogEntity对象", description = "")
public class TemplateHistoryLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.TemplateTypeEnum}
     */
    @ApiModelProperty(value = "条款类型（1 条款 2 条款组）")
    private Integer referType;

    @ApiModelProperty(value = "模板/条款组/条款ID")
    private Integer referId;

    /**
     * {@link com.navigator.trade.pojo.enums.ContractActionEnum}
     */
    @ApiModelProperty(value = "操作类型（new 新增 2修改）")
    private String operationType;

    @ApiModelProperty(value = "所属条款组编号")
    private String templateGroupCode;

    @ApiModelProperty(value = "条款内容")
    private String content;

    @ApiModelProperty(value = "规则drools脚本内容")
    private String ruleInfo;

    @ApiModelProperty(value = "规则信息")
    private String ruleJson;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String categoryId;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private String salesType;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    @ApiModelProperty(value = "操作类型")
    private String contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    private String customerName;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "是否需要序号(0不需要 1需要序号)")
    private Integer needNum;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "是否可修改（0不可修改 1可以修改）")
    private Integer canModify;

    @ApiModelProperty(value = "布局格式")
    private String layout;

    @ApiModelProperty(value = "模板绑定信息")
    private String bindRelationInfo;

    @ApiModelProperty(value = "条件变量信息")
    private String conditionVariableInfo;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String createdBy;

    private String updatedBy;


}
