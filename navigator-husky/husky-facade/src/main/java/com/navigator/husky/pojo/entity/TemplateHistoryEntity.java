package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.BasicOperateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-24 18:26
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_history")
@ApiModel(value = "TemplateHistoryEntity对象", description = "")
public class TemplateHistoryEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编号")
    private String referCode;

    @ApiModelProperty(value = "模板编号")
    private String referName;
    /**
     * {@link com.navigator.common.enums.TemplateTypeEnum}
     */
    @ApiModelProperty(value = "条款类型（1 条款 2 条款组）")
    private Integer referType;

    @ApiModelProperty(value = "模板/条款组/条款ID")
    private Integer referId;

    /**
     * {@link BasicOperateEnum}
     */
    @ApiModelProperty(value = "操作类型（new 新增 2修改）")
    private Integer operationType;
    /**
     * {@link BasicOperateEnum}
     */
    @ApiModelProperty(value = "操作类型（new 新增 2修改）")
    private String operationTypeInfo;

    @ApiModelProperty(value = "品类")
    private String categoryId;

    private String salesType;

    private String contractActionType;

    private String title;

    private String requestInfo;

    private String content;

    /**
     * {@link com.navigator.husky.pojo.enums.VersionType}
     */
    @ApiModelProperty(value = "版本类型(1 临时版本 2 正式版本)")
    private Integer versionType;

    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;

    @ApiModelProperty(value = "正式版本号（例：2023001,按年份递增）")
    private String mainVersion;

    @ApiModelProperty(value = "正式版本状态（0 非正式版本 1 正式版本）保存、更新、绑定关系变更，则更新为非正式")
    private Integer mainVersionStatus;

    @ApiModelProperty(value = "标记为主版本备注信息")
    private String mainVersionDesc;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    private String memo;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String createdBy;

    private String updatedBy;

    @TableField(exist = false)
    private String searchKey;
    /**
     * {@link com.navigator.common.enums.TemplateTypeEnum}
     */
    @TableField(exist = false)
    private String referTypeInfo;
    /**
     * 版本类型
     */
    @TableField(exist = false)
    private String mainVersionStatusInfo;

    /**
     * {@link com.navigator.husky.pojo.enums.VersionType}
     */
    @ApiModelProperty(value = "版本类型(1 临时版本 2 正式版本)")
    @TableField(exist = false)
    private String versionTypeInfo;

    @ApiModelProperty(value = "更新开始时间")
    @TableField(exist = false)
    private String startDay;
    @ApiModelProperty(value = "更新结束时间")
    @TableField(exist = false)
    private String endDay;
}
