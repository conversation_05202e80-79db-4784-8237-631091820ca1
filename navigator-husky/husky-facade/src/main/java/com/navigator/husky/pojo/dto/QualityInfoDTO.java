package com.navigator.husky.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 11:55
 **/
@Data
@Accessors(chain = true)
public class QualityInfoDTO {
    /**
     * 品类ID
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    private Integer goodsCategoryId;
    /**
     * 交货工厂编码
     */
    private String factoryCode;
    /**
     * 发货库点ID
     */
    private Integer warehouseId;
    /**
     * {@link com.navigator.trade.pojo.enums.UsageEnum}
     */
    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "货品规格")
    private Integer specId;

    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    //============== 分页查询参数 =================
    private Integer status;
    /**
     * 集团客户ID（根据客户关联查询CustomerEntity->parent_id）
     */
    private Integer enterpriseId;
    /**
     * 页面传过来的集团客户
     */
    private Integer viewEnterpriseId;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "国企标准")
    private String standardType;

    @ApiModelProperty(value = "货品spuID")
    private Integer spuId;
}
