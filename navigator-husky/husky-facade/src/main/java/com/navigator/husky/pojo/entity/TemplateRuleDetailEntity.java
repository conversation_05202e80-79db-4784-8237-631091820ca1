package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_rule_detail")
@ApiModel(value = "TemplateRuleDetailEntity对象", description = "")
public class TemplateRuleDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规则编号")
    private String ruleCode;

    @ApiModelProperty(value = "模板编号")
    private String referCode;

    /**
     * {@link com.navigator.common.enums.TemplateTypeEnum}
     */
    @ApiModelProperty(value = "条款类型（1 条款 2 条款组）")
    private Integer referType;

    @ApiModelProperty(value = "条件变量(conditionType、)")
    private String conditionVariable;
    /**
     * 条件变量ID
     */
    @ApiModelProperty(value = "条件变量ID")
    private Integer conditionVariableId;

    @ApiModelProperty(value = "加载条件阈值")
    private String conditionValue;

    @ApiModelProperty(value = "加载条件阈值描述")
    private String conditionValueInfo;

    @ApiModelProperty(value = "运算关系(“等于”、“不等于”、“大于”、“大于等于”、“小于”、“小于等于”、“包含”)")
    private String patternRelation;

    @ApiModelProperty(value = "加载规则信息（drools脚本）")
    private String ruleInfo;

    @ApiModelProperty(value = "加载规则信息描述")
    private String ruleDesc;

    @ApiModelProperty(value = "逻辑关系(&&、||)")
    private String logicRelation;

    @ApiModelProperty(value = "优先级")
    private Integer level;

    @ApiModelProperty(value = "先后顺序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
