package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.enums.TemplateLoadTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template_load")
@ApiModel(value = "TemplateLoadEntity对象", description = "")
public class TemplateLoadEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * {@link TemplateLoadTypeEnum}
     */
    @ApiModelProperty(value = "加载类型（1、组装 2、数据渲染 3、用户提交 4、提交组装）")
    private Integer type;

    @ApiModelProperty(value = "协议ID")
    private Integer contractSignId;

    @ApiModelProperty(value = "TT的ID")
    private Integer ttId;

    @ApiModelProperty(value = "TT编号")
    private String ttCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "处理的内容")
    private String content;

    @ApiModelProperty(value = "业务数据")
    private String bizData;

    @ApiModelProperty(value = "模板ID")
    private Integer templateId;

    @ApiModelProperty(value = "模板名称")
    private String templateCode;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;

    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private Integer categoryId;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    private Integer salesType;

    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolType;

    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    @ApiModelProperty(value = "异常信息")
    private String errorInfo;

    @ApiModelProperty(value = "备注")
    private String memo;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String createdBy;


}
