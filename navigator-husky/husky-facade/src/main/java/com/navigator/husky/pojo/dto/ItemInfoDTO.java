package com.navigator.husky.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-01 14:56
 **/
@Data
@Accessors(chain = true)
@ExcelTarget("itemInfoDTO")
public class ItemInfoDTO {

    @Excel(name = "关联条款编号", orderNum = "10", width = 15)
    private String bindItemCode;

    @Excel(name = "关联条款名称", orderNum = "11", width = 30)
    private String bindItemName;

    @Excel(name = "关联条款内容", orderNum = "12", width = 50)
    private String bindItemContent;
}
