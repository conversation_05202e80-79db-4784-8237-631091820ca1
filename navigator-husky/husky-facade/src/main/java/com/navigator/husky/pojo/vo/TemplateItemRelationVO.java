package com.navigator.husky.pojo.vo;

import com.navigator.husky.pojo.entity.TemplateItemRelationEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-29 14:13
 **/
@Data
@Accessors(chain = true)
public class TemplateItemRelationVO extends TemplateItemRelationEntity {

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "条款名称")
    private String templateItemName;

    @ApiModelProperty(value = "条款内容")
    private String templateItemContent;

    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    private String buInfo;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）名称")
    private String companyName;
    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    private String companyCode;
    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    private String categoryName;
    private Integer categoryId;

    private String category1;

    private String category2;

    private String category3;

    @ApiModelProperty(value = "一级品类名称")
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;
    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private String salesTypeInfo;
    private Integer salesType;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    private String protocolTypeInfo;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private String contractActionTypeInfo;
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;
    /**
     * 加载条件（合同类型=一口价；且交货工厂=TJ、TJIB；且提货方式=自提）
     */
    private String conditionInfo;

    /**
     * 所绑定的客户
     */
    private String customerName;
    /**
     * 模板/条款状态
     */
    private Integer status;
    /**
     * 条款组排序
     */
    private Integer templateGroupSort;
    /**
     * 所属条款组编码
     */
    private String templateGroupCode;
    /**
     * 所属条款组编码
     */
    private String realGroupCode;
}
