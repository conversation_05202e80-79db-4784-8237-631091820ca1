package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_quality")
@ApiModel(value = "QualityEntity对象", description = "")
public class QualityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 品类ID
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "品类ID")
    @NotNull(message = "品类不能为空")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "交货工厂编码")
    @NotBlank(message = "交货工厂不能为空")
    private String factoryCode;

    @ApiModelProperty(value = "发货库点ID逗号隔开")
    private String warehouseIds;

    /**
     * {@link com.navigator.trade.pojo.enums.UsageEnum}
     */
    @ApiModelProperty(value = "用途")
    @NotNull(message = "用途不能为空")
    private Integer usage;

    @ApiModelProperty(value = "货品规格")
    private Integer specId;

    @ApiModelProperty(value = "规格名称")
    private String specName;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    @NotNull(message = "采销类型不能为空")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;

    @ApiModelProperty(value = "是否有配置客户信息")
    private Integer hasCustomer;

    @ApiModelProperty(value = "是否为集团客户  0:否 1:是")
    private Integer enterprise;

    @ApiModelProperty(value = "集团/专属客户ID逗号隔开")
    private String customerIds;

    @ApiModelProperty(value = "集团/专属客户名称逗号隔开")
    private String customerNames;

    @NotBlank(message = "质量指标条款内容")
    private String content;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String updatedBy;

    private String createdBy;

    @ApiModelProperty(value = "货品spuID列表")
    private String spuIds;

    @ApiModelProperty(value = "货品spu名称列表")
    private String spuNames;

    @ApiModelProperty(value = "国企标准")
    private String standardType;

    //===============================扩展字段=================================
    @ApiModelProperty(value = "品类")
    @TableField(exist = false)
    private String goodsCategoryCode;
    /**
     * {@link com.navigator.trade.pojo.enums.UsageEnum}
     */
    @ApiModelProperty(value = "用途")
    @TableField(exist = false)
    private String usageInfo;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @TableField(exist = false)
    private String salesTypeInfo;

    @ApiModelProperty(value = "发货库点信息")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "专属客户信息")
    @TableField(exist = false)
    private String enterpriseName;

    @ApiModelProperty(value = "状态")
    @TableField(exist = false)
    private String statusInfo;

    /**
     * 发货库点
     */
    @TableField(exist = false)
    @NotEmpty(message = "发货库点不能为空")
    private List<QualityRuleEntity> warehouseList;
    /**
     * 客户/集团客户
     */
    @TableField(exist = false)
    private List<QualityRuleEntity> customerList;

    /**
     * 货品
     */
    @TableField(exist = false)
    private List<QualityRuleEntity> spuList;

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<QualityEntity> lqw() {
        return new LambdaQueryWrapper<QualityEntity>();
    }
}
