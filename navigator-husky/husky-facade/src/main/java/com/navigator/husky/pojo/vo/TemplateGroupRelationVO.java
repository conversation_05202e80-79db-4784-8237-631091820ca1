package com.navigator.husky.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.navigator.husky.pojo.entity.TemplateGroupRelationEntity;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-29 14:37
 **/
@Data
@Accessors(chain = true)
public class TemplateGroupRelationVO extends TemplateGroupRelationEntity {

    @TableField(exist = false)
    private Integer templateGroupId;
    @TableField(exist = false)
    private String templateGroupName;
    @TableField(exist = false)
    private Integer templateGroupStatus;
    @TableField(exist = false)
    private String templateGroupMemo;
    @TableField(exist = false)
    private List<TemplateItemEntity> itemEntityList;
    @TableField(exist = false)
    private String conditionInfo;
    @TableField(exist = false)
    private String title;
    /**
     * 对应取值条款组编码
     */
    @TableField(exist = false)
    private String realGroupCode;
    /**
     * 是否綁定条款
     */
    @TableField(exist = false)
    private Boolean hasBindItem;
}
