package com.navigator.husky.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-02 18:20
 **/
@AllArgsConstructor
@Getter
public enum TemplateLoadTypeEnum {
    ACQUIRE_BIX_DATA(1, "业务信息获取"),
    TEMPLATE_MATCH(2, "模板匹配"),
    TEMPLATE_GROUP_MATCH(3, "条款组-规则匹配"),
    TEMPLATE_ITEM_MATCH(4, "条款-规则匹配"),
    TEMPLATE_BIZ_DATA(5, "出具信息组装渲染"),
    USER_PREVIEW(6, "用户预览"),
    USER_SUBMIT(7, "用户提交出具"),
    ;

    private Integer value;
    private String desc;

    public static TemplateLoadTypeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(templateLoadTypeEnum -> value.equals(templateLoadTypeEnum.getValue()))
                .findFirst()
                .orElse(TEMPLATE_GROUP_MATCH);
    }
}
