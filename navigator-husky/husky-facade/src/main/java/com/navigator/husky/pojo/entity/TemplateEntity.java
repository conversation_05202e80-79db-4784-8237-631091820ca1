package com.navigator.husky.pojo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.vo.TemplateGroupRelationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_template")
@ApiModel(value = "TemplateEntity对象", description = "")
public class TemplateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "模板编码=\"T_\"+BU编码+“_”+所属主体+品类编码+采销类型+“_”+操作类型\n" +
            "+协议类型+[“_”+客户编码]")
    private String code;

    @ApiModelProperty(value = "模板名称=\"模板_\"+BU编码名称+“_”+所属主体+品类编码名称+采销类型名称\n" +
            "+“_”+操作类型名称+协议类型名称+[“_”+客户名称]")
    private String name;

    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @NotBlank(message = "业务线不能为空")
    private String buCode;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    @NotBlank(message = "所属主体不能为空")
    private String companyCode;

    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    @NotNull(message = "业务品类不能为空")
    private Integer categoryId;

    @ApiModelProperty(value = "一级品类")
    private String category1;
    @ApiModelProperty(value = "二级品类")
    private String category2;
    @ApiModelProperty(value = "三级品类")
    private String category3;
    @ApiModelProperty(value = "一级品类名称")
    private String category1Name;
    @ApiModelProperty(value = "二级品类名称")
    private String category2Name;
    @ApiModelProperty(value = "三级品类名称")
    private String category3Name;
    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    @NotNull(message = "采销类型不能为空")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    private Integer salesType;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @NotNull(message = "协议类型不能为空")
    private String protocolType;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    private Integer contractActionType;

    @ApiModelProperty(value = "所属客户（空则为通用 非空为某个客户）")
    private String customerCode;

    private String customerName;

    @ApiModelProperty(value = "所属集团客户（空则为通用 非空为某个客户）")
    private String enterpriseCode;

    private String enterpriseName;

    @ApiModelProperty(value = "布局格式")
    private String layout;

    @ApiModelProperty(value = "是否绑定组装模板（0 未绑定 1 组装模板）")
    private Integer bindTemplate;

    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "正式版本号（例：2023001,按年份递增）")
    private String mainVersion;
    @ApiModelProperty(value = "正式版本状态(0非正式 1 正式版本状态)")
    private Integer mainVersionStatus;
    @ApiModelProperty(value = "正式版本备注")
    private String mainVersionDesc;
    @ApiModelProperty(value = "版本号（时间戳格式：yyyyMMddHHmmss）")
    private String version;
    @ApiModelProperty(value = "编码重复次数")
    private Integer duplicateTimes;

    @ApiModelProperty(value = "状态（0禁用 1启用）")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "更新时间", format = "yyyy-MM-dd HH:mm:ss", orderNum = "14", width = 15)
    private Date updatedAt;

    private String updatedBy;

    private String createdBy;

    @ApiModelProperty(value = "业务线（ST/WT）")
    @TableField(exist = false)
    private List<String> buCodeList;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）")
    @TableField(exist = false)
    private List<String> companyCodeList;
    /**
     * {@link com.navigator.bisiness.enums.BuCodeEnum}
     */
    @ApiModelProperty(value = "业务线（现货ST、期货FT）")
    @TableField(exist = false)
    private String buInfo;

    @ApiModelProperty(value = "所属主体（TJIB/FLIB）名称")
    @TableField(exist = false)
    private String companyName;

    /**
     * {@link com.navigator.bisiness.enums.GoodsCategoryEnum}
     */
    @ApiModelProperty(value = "业务品类(11 豆粕M;12 豆油O)")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "一级品类")
    @TableField(exist = false)
    private List<Integer> category1List;

    @ApiModelProperty(value = "二级品类")
    @TableField(exist = false)
    private List<Integer> category2List;

    @ApiModelProperty(value = "三级品类")
    @TableField(exist = false)
    private List<Integer> category3List;
    @ApiModelProperty(value = "多个二级+三级，编辑回显")
    @TableField(exist = false)
    private Map<Integer, List<Integer>> category3Map;

    @ApiModelProperty(value = "采销类型(1 采购P；2 销售S)")
    /**
     * {@link com.navigator.bisiness.enums.ContractSalesTypeEnum}
     */
    @TableField(exist = false)
    private String salesTypeInfo;
    /**
     * {@link com.navigator.bisiness.enums.ProtocolTypeEnum}
     */
    @ApiModelProperty(value = "协议类型（CONTRACT⼤合同 ORDER订单 AGREEMENT补充协议）")
    @TableField(exist = false)
    private String protocolTypeInfo;

    /**
     * {@link com.navigator.bisiness.enums.ContractTradeTypeEnum}
     */
    @ApiModelProperty(value = "操作类型")
    @TableField(exist = false)
    private String contractActionTypeInfo;
    @ApiModelProperty(value = "布局/样式")
    @TableField(exist = false)
    private String layoutInfo;
    /**
     * 规则匹配返回条款组信息
     */
    @TableField(exist = false)
    private List<TemplateGroupEntity> groupList;

    /**
     * 条款组关系
     */
    @TableField(exist = false)
    private List<TemplateGroupRelationVO> templateGroupList;

    /**
     * 拼好的模板内容
     */
    @TableField(exist = false)
    private String templateHtml;

    /**
     * 协议ID
     */
    @TableField(exist = false)
    private Integer contractSignId;
    /**
     * 所绑定的模板
     */
    @TableField(exist = false)
    List<TemplateBindEntity> templateBindEntityList;
    /**
     * 被删除的条款组编号集合
     */
    @TableField(exist = false)
    private List<String> deleteGroupCodeList;
}
