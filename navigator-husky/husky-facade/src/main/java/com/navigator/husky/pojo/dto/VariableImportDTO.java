package com.navigator.husky.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-04 15:51
 **/
@Data
@Accessors(chain = true)
public class VariableImportDTO {
    @ApiModelProperty(value = "字典编码")
    @Excel(name = "变量编码", orderNum = "1", width = 25)
    private String code;

    @ApiModelProperty(value = "名称")
    @Excel(name = "名称", orderNum = "2", width = 25)
    private String name;

    @ApiModelProperty(value = "变量展示名")
    @Excel(name = "变量名称", orderNum = "3", width = 25)
    private String displayName;

    @ApiModelProperty(value = "备注")
    @Excel(name = "取值说明", orderNum = "30", width = 38)
    private String memo;
}
