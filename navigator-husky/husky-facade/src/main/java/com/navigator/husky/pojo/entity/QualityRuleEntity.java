package com.navigator.husky.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.husky.pojo.enums.VariableBizCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbh_quality_rule")
@ApiModel(value = "QualityRuleEntity对象", description = "")
public class QualityRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "质量标准ID")
    private Integer qualityId;

    /**
     * {@link VariableBizCodeEnum}
     */
    @ApiModelProperty(value = "warehouse/customer/enterprise/sku")
    private String qualityRuleCode;

    @ApiModelProperty(value = "发货库点ID/客户ID/集团客户ID/SKU ID")
    private Integer referId;

    @ApiModelProperty(value = "发货库点/客户/集团客户编码/SKU编码")
    private String referCode;

    @ApiModelProperty(value = "发货库点/客户/集团客户名称/SKU名称")
    private String referValue;

    @ApiModelProperty(value = "是否删除（0未删除 1已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private String updatedBy;

    private String createdBy;
}
