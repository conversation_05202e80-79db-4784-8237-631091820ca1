package com.navigator.husky.facade;

import com.navigator.husky.pojo.entity.TemplateLoadEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-02 18:06
 **/
@FeignClient(value = "navigator-husky-service")
public interface TemplateLoadFacade {
    @PostMapping("/saveTemplateLoadLog")
    TemplateLoadEntity saveTemplateLoadLog(@RequestBody TemplateLoadEntity templateLoadEntity);

    @PostMapping("/updateTemplateLoadLog")
    TemplateLoadEntity updateTemplateLoadLog(@RequestBody TemplateLoadEntity templateLoadEntity);
}
