package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

/**
 * <AUTHOR> @date
 */
@Getter
public enum LkgAccountEnum {

    LDZJGZS("29", "路易达孚测试账套",1218763,"ZJG"),
    LDZJGZS2("30", "路易达孚测试账套2",1218763,"DG"),
    ;

    private String siteId;
    private String siteName;
    private int navId;
    private String deliveryFactory;

    LkgAccountEnum(String siteId, String siteName,int navId,String deliveryFactory) {
        this.siteId = siteId;
        this.siteName = siteName;
        this.navId = navId;
        this.siteName = deliveryFactory;
    }

    public static LkgAccountEnum getByNavId(int navId) {
        if (0 == navId){ return LDZJGZS;}
        for (LkgAccountEnum en : LkgAccountEnum.values()) {
            if (en.getNavId() == navId) {
                return en;
            }
        }
        return LDZJGZS;
    }

}
