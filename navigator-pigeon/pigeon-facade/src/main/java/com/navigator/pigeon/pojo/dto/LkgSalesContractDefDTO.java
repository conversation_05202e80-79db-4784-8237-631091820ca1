package com.navigator.pigeon.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class LkgSalesContractDefDTO {
    /**
     * 合同状态,统一传2（合同生效）
     */
    private Integer status;
    /**
     * 定价状态
     * contractType ：2,3,4传0；1传2
     */
    private Integer fixstatus;
    /**
     * 变更次数 新增传0
     */
    private Integer changesTimes;
    @Excel(name = "代签单位", orderNum = "1")
    /**
     * 代签单位 非必传
     * 传11
     */
    private String signingUnit;
    @Excel(name = "合同编号", orderNum = "2")
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 合同补充信息-客户合同号
     */
    private String refContractNumber;
    @Excel(name = "签订地点", orderNum = "3")
    /**
     * 签订地点
     */
    private String signAddress;
    @Excel(name = "签订时间", orderNum = "4")
    /**
     * 签订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;
    /**
     * 有效时间：非必填，默认为空
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveTime;
    @Excel(name = "开始交货日期", orderNum = "5")
    /**
     * 开始交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDeliveryTime;
    @Excel(name = "交(提)货日期", orderNum = "6")
    /**
     * 交(提)货日期：截止交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDeliveryTime;
    /**
     * 延期罚款比 默认空
     */
    private BigDecimal rateOfDelayPenalty;
    /**
     * 基差合同（基差、基差暂定价1，其余传0）
     */
    private Integer isBasic;
    /**
     * 延期定价（暂定价传1）
     */
    private Integer isDelay;
    /**
     * 定价方式（一口价传1，基差及基差暂定价传4，暂定价传8）
     */
    private Integer modeOfPrice;
    /**
     * 定价截止日期
     * 1、若为日期则传日期 2、若为文本则日期传0，将文本传到备注中
     */
    private String endFixPriceTime = "0";
    /**
     * 赊销 1是0否
     */
    private Integer isSellOfCredit;
    /**
     * CNF,是否送货，根据提货方式确认 如果是LDC送货，则打勾
     */
    private Integer isSellOfCNF;
    /**
     * 代加工 1是，0否
     */
    private Integer isSellOfOEM;
    /**
     * BTB 一期全部传0 谷物专用，一期不涉及
     */
    private Integer isSellOfBTB;
    /**
     * 交易类型
     */
    private String transactionType;
    /**
     * 关联合同号 否
     */
    private String fromContractNumber;
    /**
     * 关联合同日期 否 原合同的签订日期
     */
    private String fromContractSignTime;
    @Excel(name = "发票类型", orderNum = "7")
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 是否需要发票:1是，0否 传1
     */
    private Integer isProvideInvoice;
    /**
     * 供应方
     */
    private String supplier;
    /**
     * 合同出具人
     */
    private String exeOfficer;
    /**
     * TT创建者 传商务对应的LKG编码
     */
    @Excel(name = "Trader", orderNum = "8")
    private String trader;
    /**
     * 客户代码
     */
    @Excel(name = "客户代码", orderNum = "9")
    private String customerCode;
    @Excel(name = "客户名称", orderNum = "10")
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 框架协议号
     */
    private String frameAgreementNo;
    /**
     * 品质描述 质量检验
     */
    private String qualityDesc;
    /**
     * 发货库点对应的交货地点
     */
    private String deliveryAddress;
    @Excel(name = "交货方式", orderNum = "11")
    /**
     * 交提货方式
     */
    private Integer modeOfDelivery;
    /**
     * 发货库点（上线前提供）  传LKG编码 例：2.5001
     */
    private String storehouseAddress;
    /**
     * 运费计算方式 1需方承担 2供方承担 默认1
     */
    private Integer freightCalcMode;
    /**
     * 运费暂定价格 如果是LDC送货，需要传值，否则为空
     */
    private BigDecimal freightProvPrice;
    /**
     * 包装方式 默认为50kg（选择linkinage中同步过来的此货品代码的包装） DCE交割豆油默认为：脱胶毛豆油散装
     */
    private String modeOfPack;
    /**
     * 包装是否计算重量 默认选项为是，1
     */
    private Integer isPackCalcWeight;
    /**
     * 袋皮扣重，不扣皮传0，其他传1
     */
    private BigDecimal bagSkinWeight;
    /**
     * 需安排运输 是否送货，根据提货方式确认 与CNF同值
     */
    private Integer isTransport;
    /**
     * 重量验收标准
     */
    private Integer weightCriteria;
    /**
     * 超发限额 溢短装 船提5%，其他1%；传5
     */
    private BigDecimal superHairAmount;
    /**
     * 总金额
     */
    private String tolAmount;
    /**
     * 方向
     */
    private String direction;
    /**
     * 目的港-lkg编码
     */
    private String toHarbor;
    /**
     * 变更单号
     */
    private String changeOrderNo;
    /**
     * 定金比例 以10%为例，传10 履约保证金比例
     */
    private BigDecimal depositRate;
    /**
     * 定金金额 应付履约保证金
     */
    private String depositAmount;
    /**
     * 付定金日期 非必传
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payDepositTime;
    /**
     * 付全款日期 非必传
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payFullTime;
    /**
     * 履约保证金比例
     */
    private BigDecimal bondRate;
    /**
     * 转入单位 默认为空
     */
    private String intoCompany;
    @Excel(name = "付款条件", orderNum = "12")
    /**
     * 付款条件
     * （1）赊销：
     * 基差及基差暂定价：履约保证金比例,履约保证金点价后补缴比例，creditDays赊销账期；
     * 一口价及暂定价：履约保证金比例，creditDays赊销账期；
     * （2）预付款：
     * 基差及基差暂定价：履约保证金比例,履约保证金点价后补缴比例，100%-履约保证金比例-履约保证金点价后补缴比例
     * 一口价及暂定价：履约保证金比例,100%-履约保证金比例
     */
    private String paymentCondition;
    /**
     * 补充条款 传空
     */
    private String suppleProvisions;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 制单人 传商务对应的LKG账号名称
     */
    private String createUser;
    /**
     * 制单日期 createdAt 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 签发人 TT创建人
     */
    private String checkUser;
    /**
     * 签发日期：当前日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;
    /**
     * 合同类型
     * 豆粕销售合同传1,豆油销售合同传2
     */
    private Integer transsequence = 1;
    /**
     * 物品大类
     * 豆粕销售合同传1,豆油销售合同传2
     */
    private String commodityClass = "2";
    /**
     * 合同总数量
     */
    private BigDecimal totalCount = BigDecimal.ZERO;

    private String payConditionCode;

    @ExcelCollection(name = "明细", orderNum = "13")
    private List<LkgSalesContractDetailDTO> detaillist;

}
