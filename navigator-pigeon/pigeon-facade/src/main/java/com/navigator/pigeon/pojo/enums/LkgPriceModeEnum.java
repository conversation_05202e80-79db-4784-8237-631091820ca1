package com.navigator.pigeon.pojo.enums;

import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.Getter;

@Getter
public enum LkgPriceModeEnum {
    DEFAULT_DELIVERY_PRICE(1, "本地车板价"),
    CUSTOMER_APPOINT_PRICE(2, "客户指定地点价格"),
    LEASE_DELIVERY_PRICE(3, "外租库车板价"),
    FUTURES_BASIS_PRICE(4, "期货基差定价"),
    ADVANCE_PAYMENT_PRICE(5, "预付款现销"),
    DCE_SBM_WAREHOUSE_PRICE(6, "DCE豆粕仓单价"),
    DCE_SBM_DELIVERY_PRICE(7, "DCE豆粕提单价"),
    DELAY_BASIS_PRICE(8, "延期定价合同"),
    ;
    /*
        1	本地车板价
        2	客户指定地点价格
        3	外租库车板价
        4	期货基差定价
        5	预付款现销
        6	DCE豆粕仓单价
        7	DCE豆粕提单价
        8	延期定价合同
    */

    private int value;
    private String desc;

    LkgPriceModeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgPriceModeEnum getByValue(Integer value) {
        if (null == value) return DEFAULT_DELIVERY_PRICE;
        for (LkgPriceModeEnum en : LkgPriceModeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return DEFAULT_DELIVERY_PRICE;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static LkgPriceModeEnum getByContractType(Integer contractType) {
        if (null == contractType) return DEFAULT_DELIVERY_PRICE;

        if (contractType == ContractTypeEnum.YI_KOU_JIA.getValue()) {
            return DEFAULT_DELIVERY_PRICE;
        }

        if (contractType == ContractTypeEnum.JI_CHA.getValue()
                || contractType == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
            return FUTURES_BASIS_PRICE;
        }

        if (contractType == ContractTypeEnum.ZAN_DING_JIA.getValue()) {
            return DELAY_BASIS_PRICE;
        }

        return DEFAULT_DELIVERY_PRICE;
    }
}
