package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_sync_record
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_sync_record")
@ApiModel(value = "DbiLkgSyncRecordEntity对象", description = "dbi_lkg_sync_record")
public class LkgSyncRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "请求ID")
    private Integer requestId;

    @ApiModelProperty(value = "lkg合同编号")
    private String lkgContractCode;

    @ApiModelProperty(value = "关联合同编号")
    private String refContractNumber;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "根合同ID")
    private Integer parentContractId;

    @ApiModelProperty(value = "请求场景")
    private Integer tradeType;

    @ApiModelProperty(value = "采销")
    private Integer salesType;

    @ApiModelProperty(value = "客户id")
    private String customerCode;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;

    @ApiModelProperty(value = "处理类型")
    private String processType;

    @ApiModelProperty(value = "处理类型")
    private String contractInfo;

    @ApiModelProperty(value = "同步次数（预警）")
    private Integer tryTimes;

    @ApiModelProperty(value = "1：新增 2：更新 3：定价 4:查询")
    private Integer syncType;

    @ApiModelProperty(value = "同步时间")
    private Date syncTime;

    @ApiModelProperty(value = "同步状态 待完成，已完成，未完成")
    private Integer syncStatus;

    @ApiModelProperty(value = "lkg同步结果反馈信息（json）")
    private String lkgResultsInfo;

    @ApiModelProperty(value = "lkg处理状态（成功，失败）")
    private Integer lkgResultsStatus;

    @ApiModelProperty(value = "过程数据（新增，更新）json形式")
    private String lkgReqestInfo;

    @ApiModelProperty(value = "账套ID（lkg数据库id）factorycode")
    private String factoryCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "唯一标识")
    private String nonce;

    @ApiModelProperty(value = "账套")
    private String siteId;

    @ApiModelProperty(value = "所属主体")
    private Integer belongCustomerId;

    @TableField(exist = false)
    String actionName;

    @TableField(exist = false)
    private Integer ttId;

    public String getActionName() {
        String actionName = LkgInterfaceActionEnum.getByValue(getSyncType()).name();
        return actionName;
    }

}
