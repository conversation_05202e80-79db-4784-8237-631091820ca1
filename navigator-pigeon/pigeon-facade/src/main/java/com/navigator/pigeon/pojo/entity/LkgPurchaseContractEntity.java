package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_purchase_contract")
@ApiModel(value = "LkgPurchaseContractEntity对象", description = "")
public class LkgPurchaseContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "基差定价")
    private String isBasic;

    @ApiModelProperty(value = "延期定价")
    private String isDelay;

    @ApiModelProperty(value = "STF")
    private String isStf;

    @ApiModelProperty(value = "代加工")
    private String isSellOfOem;

    @ApiModelProperty(value = "定价状态")
    private String fixStatus;

    @ApiModelProperty(value = "定价截止日期类型")
    private String endFixPriceType;

    @ApiModelProperty(value = "定价截止日期")
    private String endFixPriceTime;

    @ApiModelProperty(value = "履约保证金释放方式")
    private String depositReleaseType;

    @ApiModelProperty(value = "定价方式")
    private String modeOfPrice;

    @ApiModelProperty(value = "签订时间")
    private Date signTime;

    @ApiModelProperty(value = "签订地点")
    private String signPlace;

    @ApiModelProperty(value = "买方主体名称")
    private String demander;

    @ApiModelProperty(value = "卖方主体名称")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体编码")
    private String supplierCode;

    @ApiModelProperty(value = "超收限额(%)")
    private String overchargeRate;

    @ApiModelProperty(value = "开始交货日期")
    private Date beginDeliveryTime;

    @ApiModelProperty(value = "终止交货日期")
    private Date endDeliveryTime;

    @ApiModelProperty(value = "货品代码")
    private String commodityCode;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "数量")
    private String count;

    @ApiModelProperty(value = "期货合约")
    private String futuresContractNo;

    @ApiModelProperty(value = "Trader")
    private String trader;

    @ApiModelProperty(value = "制单人")
    private String createUser;

    @ApiModelProperty(value = "制单日期")
    private Date createTime;

    @ApiModelProperty(value = "交易类型")
    private String transactionType;

    @ApiModelProperty(value = "开户行")
    private String bankName;

    @ApiModelProperty(value = "开户名称")
    private String bankAccountName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNo;

    @ApiModelProperty(value = "袋皮扣重(kg)")
    private String packUnitWeight;

    @ApiModelProperty(value = "交提货方式编码")
    private String modeOfDeliveryCode;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "发货库点编码")
    private String storeHouseAddress;

    @ApiModelProperty(value = "赊销天数")
    private String creditDays;

    @ApiModelProperty(value = "重量验收标准编码")
    private String weightCriteria;

    @ApiModelProperty(value = "发票类型编码")
    private String invoiceType;

    @ApiModelProperty(value = "履约保证金比例")
    private String depositRate;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private String bondRate;

    @ApiModelProperty(value = "履约保证金金额")
    private String depositAmount;

    /**
     * 价格明细
     */
    @ApiModelProperty(value = "基差价")
    private String extraPrice;

    @ApiModelProperty(value = "期货价格")
    private String futuresPrice;

    @ApiModelProperty(value = "蛋白价差")
    private String proteinDiffPrice;

    @ApiModelProperty(value = "散粕补贴")
    private String compensationPrice;

    @ApiModelProperty(value = "期权费")
    private String optionPrice;

    @ApiModelProperty(value = "运费")
    private String transportPrice;

    @ApiModelProperty(value = "起吊费")
    private String liftingPrice;

    @ApiModelProperty(value = "滞期费")
    private String delayPrice;

    @ApiModelProperty(value = " 高温费")
    private String temperaturePrice;

    @ApiModelProperty(value = " 其他物流费")
    private String otherDeliveryPrice;

    @ApiModelProperty(value = " 回购折价")
    private String buyBackPrice;

    @ApiModelProperty(value = "客诉折价")
    private String complaintDiscountPrice;

    @ApiModelProperty(value = " 转厂补贴")
    private String transferFactoryPrice;

    @ApiModelProperty(value = "其他补贴")
    private String otherPrice;

    @ApiModelProperty(value = "商务补贴")
    private String businessPrice;

    @ApiModelProperty(value = "手续费")
    private String fee;

    @ApiModelProperty(value = "精炼价差")
    private String refineDiffPrice;

    @ApiModelProperty(value = "不含税单价")
    private String cifUnitPrice;

    @ApiModelProperty(value = "Fob单价")
    private String fobUnitPrice;

    @ApiModelProperty(value = "含税单价")
    private String unitPrice;

    @ApiModelProperty(value = "总金额")
    private String totalAmount;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "数据同步标识")
    private Integer isSynced;

    @ApiModelProperty(value = "目的港编码")
    private String toHarborCode;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private String invoicePaymentRate;

    @ApiModelProperty(value = "付款条件编码")
    private String paymentTermCode;

    @ApiModelProperty(value = "发货库点名称")
    private String warehouseName;

    @ApiModelProperty(value = "精炼/分提价差")
    private BigDecimal refineFracDiffPrice;

    @ApiModelProperty(value = "检验费")
    private BigDecimal surveyFees;

    @ApiModelProperty(name = "品种代码")
    private String futureCode;

    @ApiModelProperty(name = "货品全称")
    private String commodityFullName;

    @ApiModelProperty(name = "二级品类")
    private String category2;

    @ApiModelProperty(name = "品种")
    private String category3;

    @ApiModelProperty(name = "账套名称")
    private String siteName;

    @ApiModelProperty(name = "企标类型")
    private String standardType;

    @ApiModelProperty(name = "企标文件编号")
    private String standardFileNo;

    @ApiModelProperty(name = "企标备注")
    private String standardRemark;

    @ApiModelProperty(name = "用途")
    private String usage;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

}
