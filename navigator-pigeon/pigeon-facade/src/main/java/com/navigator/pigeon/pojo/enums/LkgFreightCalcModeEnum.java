package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgFreightCalcModeEnum {
    CUSTOMER_PAY(1, "需方承担"),
    SUPPLIER_PAY(2, "供方承担"),

    ;

    /*
        1	需方承担
        2	供方承担
     */

    private int value;
    private String desc;

    LkgFreightCalcModeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgFreightCalcModeEnum getByValue(Integer value) {
        if (null == value) return CUSTOMER_PAY;
        for (LkgFreightCalcModeEnum en : LkgFreightCalcModeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return CUSTOMER_PAY;
    }


    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
