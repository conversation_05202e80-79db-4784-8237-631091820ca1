package com.navigator.pigeon.pojo.dto;

import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SyncRequestDTO extends LkgSyncRequestEntity {

    List<LkgSyncRecordEntity> lkgSyncRecordEntityList;

    Integer contractType = 0;

    Integer syncType = 0;

    String transactionType = "";

    Integer referContractId = 0;

    Integer requestId;

    Integer recordId;

}
