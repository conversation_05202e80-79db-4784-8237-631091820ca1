package com.navigator.pigeon.facade;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.pojo.dto.LkgQueryRecordDTO;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 合同对外暴露的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/30
 */
@FeignClient(name = "navigator-pigeon-service")
public interface LkgContractFacade {

    /**
     * 发送同步申请
     *
     * @param syncRequestDTO
     * @return
     */
    @PostMapping("/syncContractRequest")
    Result syncContractRequest(@RequestBody SyncRequestDTO syncRequestDTO);

    /**
     * 根据场景重试请求
     *
     * @param syncRequestDTO
     * @return
     */
    @PostMapping("/reSyncContract")
    Result reSyncContract(@RequestBody SyncRequestDTO syncRequestDTO);

    /**
     * 发送同步申请
     *
     * @param reqId
     * @return
     */
    @PostMapping("/reSyncContractRequest")
    Result reSyncContractRequest(@RequestParam("reqId") Integer reqId);

    @PostMapping("/reBuildSyncContractRequest")
    Result reBuildSyncContractRequest(@RequestParam("reqId") Integer reqId);

    /**
     * 获取LKG合同信息
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getLkgContract")
    Result getLkgContract(@RequestParam("contractCode") String contractCode);

    /**
     * 批量获取LKG合同信息
     *
     * @param contractCodeList
     * @return
     */
    @GetMapping("/getBatchLkgContract")
    Result getBatchLkgContract(@RequestParam("contractCodes") List<String> contractCodeList);

    /**
     * 批量获取LKG合同信息
     *
     * @param pageSize 每页条数
     * @param pageNum  页码
     * @return
     */
    @GetMapping("/getBatchLkgContractList")
    Result getBatchLkgContractList(@RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum);

    /**
     * 获取客户的合同点价/转月信息
     *
     * @param customerCode
     * @return
     */
    @GetMapping("/getCustomerContractFutureInfo")
    Result getCustomerContractFutureInfo(@RequestParam("customerCode") String customerCode);

    @GetMapping("/exportTemplate")
    Result exportTemplate(@RequestParam(value = "salesType") int salesType, @RequestParam(value = "contractCode") String contractCode, HttpServletResponse response);


    /**
     * 导入lkg合同数据
     *
     * @param file
     * @param salesType
     * @return
     */
    @PostMapping(value = "/importContract", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importContract(@RequestParam(value = "file") MultipartFile file,
                          @RequestParam(value = "salesType") Integer salesType);


    /**
     * 将导入后的采销合同数据同步到prepareContract
     *
     * @return
     */
    @GetMapping("/syncPrepareContract")
    Result syncPrepareContract();


    /**
     * 将prepareContract数据同步到Contract
     *
     * @return
     */
    @GetMapping("/syncContract")
    Result syncContract();

    /**
     * 数据巡检方案
     * 1.原始数据与结果数据对比
     * 2.根据同步接口结果判断
     *
     * @param startCheckTime 开始校验时间
     * @param endCheckTime   截止校验时间
     * @return
     */
    @GetMapping("/checkLkgRecord")
    Result checkLkgRecord(@RequestParam(value = "startCheckTime", required = false) String startCheckTime, @RequestParam(value = "endCheckTime", required = false) String endCheckTime);

    /**
     * 根据ttId查询request记录
     *
     * @param ttId
     * @return
     */
    @GetMapping("/getLkgRequestByTtId")
    Result getLkgRequestByTtId(@RequestParam(value = "ttId") Integer ttId);

    /**
     * 根据requestId查询record记录
     *
     * @param requestId
     * @return
     */
    @GetMapping("/getLkgRecordByRequestId")
    Result getLkgRecordByRequestId(@RequestParam(value = "requestId") Integer requestId);

    /**
     * 根据contractCode查询本地LKG合同信息
     *
     * @param contractCode 合同编号
     * @return
     */
    @GetMapping("/getLocalLkgContractByContractCode")
    Result getLocalLkgContractByContractCode(@RequestParam("contractCode") String contractCode);

    @PostMapping("/getLocalLkgContractByContractCodeList")
    Result getLocalLkgContractByContractCodeList(@RequestBody LkgQueryRecordDTO queryRecordDTO);

    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 Start
    @PostMapping(value = "/closeContractByFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result closeContractByFile(@RequestParam(value = "checkDate") String checkDate,
                               @RequestParam(value = "file") MultipartFile file);
    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 End
}
