package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_prepare_contract")
@ApiModel(value = "PrepareContractEntity对象", description = "")
public class PrepareContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "唯一编号")
    private String uuid;

    @ApiModelProperty(value = "linkinage合同编码")
    private String linkinageCode;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "合同销售类型（1.采购 2.销售）")
    private Integer salesType;

    @ApiModelProperty(value = "根合同ID")
    private Integer rootId;

    @ApiModelProperty(value = "父合同ID")
    private Integer parentId;

    @ApiModelProperty(value = "合同来源（1合同新增 2合同修改 3合同拆分4合同全部转月 5合同部分转月 6合同全部反点价 7合同部分反点价 8合同定价 9合同主体修改 10合同主体拆分）")
    private Integer contractSource;

    @ApiModelProperty(value = "买方客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "原始买方客户ID（非0则为变更客户主体）")
    private Integer originalCustomerId;

    @ApiModelProperty(value = "卖方客户ID")
    private Integer supplierId;

    @ApiModelProperty(value = "卖方客户名称")
    private String supplierName;

    @ApiModelProperty(value = "卖方主体收款账号信息")
    private String supplierAccount;

    @ApiModelProperty(value = "合同状态（1、未生效 2、生效中  3、修改中 8、已完成 9、已作废）")
    private Integer status;

    @ApiModelProperty(value = "客户看到的合同状态（待定）")
    private Integer customerStatus;

    @ApiModelProperty(value = "是否需要正本（0 不需要 1 需要）")
    private Integer needOriginalPaper;

    @ApiModelProperty(value = "正本状态")
    private Integer originalPaperStatus;

    @ApiModelProperty(value = "签章类型（客户：线下和易企签）")
    private String signatureType;

    @ApiModelProperty(value = "签订地（卖方主体）")
    private String signPlace;

    @ApiModelProperty(value = "交易类型：1、New（新增）2、Revise（修改）3、Split（拆分）")
    private Integer tradeType;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "货品ID")
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * {@link com.navigator.trade.pojo.enums.UnitEnum}
     */
    @ApiModelProperty(value = "计量单位（TT只有吨，涉及物流会有袋件数等）")
    private String weightUnit;

    @ApiModelProperty(value = "品种")
    private Integer goodsCategoryId;

    @ApiModelProperty(value = "默认为50kg（选择linkinage中同步过来的包装） DCE交割豆油默认为：脱胶毛豆油散装")
    private Integer goodsPackageId;

    @ApiModelProperty(value = "规格（默认43%），可选")
    private Integer goodsSpecId;

    @ApiModelProperty(value = "包装是否计算重量 默认选项为否，若选择“是”则根据品种及包装来判断")
    private Integer needPackageWeight;

    @ApiModelProperty(value = "袋皮扣重")
    private String packageWeight = "0";

    @ApiModelProperty(value = "质量检验（根据客户属性定）")
    private String qualityCheck;

    @ApiModelProperty(value = "币种（系统默认为CNY）")
    private String currencyType;

    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal baseDiffPrice;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "合同签订总量（吨）")
    private BigDecimal orderNum;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "总转月次数")
    private Integer totalTransferTimes;

    @ApiModelProperty(value = "可转月次数")
    private Integer ableTransferTimes;

    @ApiModelProperty(value = "总反点价次数")
    private Integer totalReversePriceTimes;

    @ApiModelProperty(value = "可反点价次数")
    private Integer ableReversePriceTimes;

    @ApiModelProperty(value = "已提总量（吨）")
    private BigDecimal totalDeliveryNum;

    @ApiModelProperty(value = "已点总量（吨）")
    private BigDecimal totalPriceNum;

    @ApiModelProperty(value = "已转月量")
    private BigDecimal totalTransferNum;

    @ApiModelProperty(value = "已变更量")
    private BigDecimal totalModifyNum;

    @ApiModelProperty(value = "已回购量")
    private BigDecimal totalBuyBackNum;

    @ApiModelProperty(value = "暂定价")
    private BigDecimal temporaryPrice;

    @ApiModelProperty(value = "成交价")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "赊销账期")
    private Integer creditDays;

    @ApiModelProperty(value = "付款方式(有赊销天数则为赊销；无赊销天数为预付款)")
    private Integer paymentType = 0;

    @ApiModelProperty(value = "应付履约保证金")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "追加履约保证金 一口价默认是5%【下跌】基差150 【下跌】 一口价暂定价5%【上涨也会收】延期定价 默认>0【上涨收】")
    private BigDecimal addedDeposit;

    @ApiModelProperty(value = "履约保证金比例 (系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
    private Integer depositRate;

    @ApiModelProperty(value = "履约保证金释放方式，默认1(1.随车按比例释放 2.抵扣最后一笔)")
    private Integer depositReleaseType;

    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
    private BigDecimal delayPayFine;

//    @ApiModelProperty(value = "迟付款罚金规则")
//    private String delayPayFineRule;

    @ApiModelProperty(value = "是否代加工，默认否（0:否 1:是）")
    private Integer oem;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）")
    private Integer deliveryType;

    @ApiModelProperty(value = "交货工厂")
    private String deliveryFactory;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "发货库点ID，默认为工厂豆粕库，可选")
    private Integer shipWarehouseId;

    @ApiModelProperty(value = "是否安排运输")
    private Integer isArrangeTransport;

    @ApiModelProperty(value = "重量检验（可选，Magellan维护）")
    private String weightCheck;

    @ApiModelProperty(value = "溢短装 (系统默认（可修改）：船提5%，其他1%；)")
    private Integer weightTolerance;

    @ApiModelProperty(value = "作价开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date priceStartTime;

    @ApiModelProperty(value = "作价截止时间")
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String priceEndTime;

    @ApiModelProperty(value = "合同负责人")
    private Integer ownerId;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "作废原因")
    private String invalidReason;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "是否是stf合同（0.否 1.是）")
    private Integer isStf;

    @ApiModelProperty(value = "stf合同号")
    private String customerContractCode;

    @ApiModelProperty(value = "ldc是否需要正本（0.不需要 1.需要）")
    private Integer ldcNeedOriginalPaper;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "点价截止日期类型（1.时间 2.文本）")
    private Integer priceEndType;

    @ApiModelProperty(value = "是否是ldc模板（0.nonframe 1.是）")
    private Integer ldcFrame;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Integer isDeleted;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "履约保证金点价后补缴")
    private Integer addedDepositRate;
    @ApiModelProperty(value = "追加履约保证金比例")
    private Integer addedDepositRate2;

    @ApiModelProperty(value = "卖方主体收款账号Id")
    private Integer supplierAccountId;

    @ApiModelProperty(value = "补充信息标签ID逗号隔开")
    private String tagConfigIds;

    @ApiModelProperty(value = "交货工厂是否发生改变（0.否 1.是）")
    private Integer isChangeFactory;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId = 0;

    @ApiModelProperty(value = "发票类型")
    private Integer invoiceType;

    @ApiModelProperty(value = "版本")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Integer mainVersion = 0;

    @ApiModelProperty(value = "已开单量")
    @TableField(exist = false)
    private BigDecimal totalBillNum;

    @ApiModelProperty(value = "签订总金额")
    private BigDecimal orderAmount;

    /**
     * 含税明细
     */
    @ApiModelProperty(value = "装运费单价")
    private BigDecimal shippingFeePrice;

    @ApiModelProperty(value = "升贴水价格(基差价)")
    private BigDecimal addUnitPrice;

    @ApiModelProperty(value = "已转月次数")
    private Integer transferredTimes;

    @ApiModelProperty(value = "已反点价次数")
    private Integer reversedPriceTimes;

    /**
     * 价格明细
     */
    @ApiModelProperty(value = "基差价")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货价格")
    private BigDecimal futuresPrice;

    @ApiModelProperty(value = "蛋白价差")
    private BigDecimal proteinDiffPrice;

    @ApiModelProperty(value = "散粕补贴")
    private BigDecimal compensationPrice;

    @ApiModelProperty(value = "期权费")
    private BigDecimal optionPrice;

    @ApiModelProperty(value = "运费")
    private BigDecimal transportPrice;

    @ApiModelProperty(value = "起吊费")
    private BigDecimal liftingPrice;

    @ApiModelProperty(value = "滞期费")
    private BigDecimal delayPrice;

    @ApiModelProperty(value = "高温费")
    private BigDecimal temperaturePrice;

    @ApiModelProperty(value = "其他物流费")
    private BigDecimal otherDeliveryPrice;

    @ApiModelProperty(value = "回购折价")
    private BigDecimal buyBackPrice;

    @ApiModelProperty(value = "客诉折价")
    private BigDecimal complaintDiscountPrice;

    @ApiModelProperty(value = "转厂补贴")
    private BigDecimal transferFactoryPrice;

    @ApiModelProperty(value = "其他补贴")
    private BigDecimal otherPrice;

    @ApiModelProperty(value = "商务补贴")
    private BigDecimal businessPrice;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(value = "精炼价差")
    private BigDecimal refineDiffPrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal cifUnitPrice;

    @ApiModelProperty(value = "Fob单价")
    private BigDecimal fobUnitPrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "数据同步标识")
    private Integer isSynced;

    @ApiModelProperty(value = "发票后补缴货款比例")
    private Integer invoicePaymentRate;

    @ApiModelProperty(value = "付款条件编码")
    private String paymentTermCode;

    @ApiModelProperty(value = "付款条件id")
    private Integer payConditionId;

    @ApiModelProperty(value = "用途")
    private Integer usage;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "账套Code")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "精炼/分提价差")
    private BigDecimal refineFracDiffPrice;

    @ApiModelProperty(value = "检验费")
    private BigDecimal surveyFees;

    @ApiModelProperty(name = "品种代码")
    private String futureCode;

    @ApiModelProperty(name = "货品全称")
    private String commodityName;

    @ApiModelProperty(name = "一级品类")
    private Integer category1;

    @ApiModelProperty(name = "二级品类")
    private Integer category2;

    @ApiModelProperty(name = "品种")
    private Integer category3;

    @ApiModelProperty(value = "目的地,值对象新增字段")
    private String destinationValue;

    @ApiModelProperty(value = "交提货方式（卖方车板交货/码头卖方船板自提）,值对象新增字段")
    private String deliveryTypeValue;

    @ApiModelProperty(value = "袋皮扣重,值对象新增字段")
    private String packageWeightValue;

    @ApiModelProperty(value = "发票类型,值对象新增字段")
    private String invoiceTypeValue;

    @ApiModelProperty(value = "重量检验,值对象新增字段")
    private String weightCheckValue;

    @ApiModelProperty(value = "发货库点,值对象新增字段")
    private String shipWarehouseValue;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

    @ApiModelProperty(value = "企标|国标类型")
    private String standardType = "国标";

    @ApiModelProperty(value = "企标文件ID")
    private Integer standardFileId;

    @ApiModelProperty(value = "指标备注")
    private String standardRemark;

}
