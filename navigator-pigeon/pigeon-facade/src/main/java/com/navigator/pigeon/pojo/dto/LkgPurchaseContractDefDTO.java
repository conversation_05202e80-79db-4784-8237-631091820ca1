package com.navigator.pigeon.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class LkgPurchaseContractDefDTO {
    private Integer status;
    private Integer fixstatus;
    private Integer changesTimes;
    @Excel(name = "代签单位", orderNum = "1")
    private String signingUnit;
    @Excel(name = "合同编号", orderNum = "2")
    private String contractNumber;
    private String refContractNumber;
    @Excel(name = "签订时间", orderNum = "4")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;
    @Excel(name = "签订地点", orderNum = "3")
    private String signAddress;
    private Integer tradeNature;
    private Integer modeOfPrice;
    private String endFixPriceTime;
    private Integer isBasic;
    private Integer isDelay;
    private Integer isSellOfBTB;
    private String demander;
    private String exeOfficer;
    @Excel(name = "Trader", orderNum = "8")
    private String trader;
    @Excel(name = "供应商代码", orderNum = "9")
    private String supplierCode;
    @Excel(name = "供应商名称", orderNum = "10")
    private String supplierName;
    private String isSellOfOEM;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveTime;
    private String tolAmount;
    @Excel(name = "发票类型", orderNum = "7")
    private Integer invoiceType;
    private Integer isProvideInvoice;
    private String contractStrategy;
    private BigDecimal overchargeRate;
    private String transactionType;
    private String fromContractNumber;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fromContractSignTime;
    private String changeOrderNo;
    private String deliveryAddress;
    @Excel(name = "开始交货日期", orderNum = "5")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDeliveryTime;
    @Excel(name = "交(提)货日期", orderNum = "6")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDeliveryTime;
    private String qualityCheckExplain;
    @Excel(name = "付款条件", orderNum = "11")
    private String paymentExplain;
    private Integer isPaymentPurchase;
    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String checkUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;
    private String remarks="";
    private String changeReason;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;
    private Integer transsequence = 1;
    private String commodityClass = "2";
    private BigDecimal totalCount = BigDecimal.ZERO;
    private BigDecimal priceOfFOB;
    private String payConditionCode;
    private Integer modeOfDelivery;
    @ExcelCollection(name = "明细", orderNum = "12")
    private List<LkgPurchaseContractDetailDTO> detaillist;

}
