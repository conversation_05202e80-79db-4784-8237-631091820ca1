package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgContractStatusEnum {
    INVALID(-1, "作废"),
    DRAFT(1, "新制合同"),
    EFFECTIVE(2, "合同生效"),
    TAKING(3, "正在执行"),
    LADING_BILL(4, "提单签发完毕"),
    DELIVERYED(5, "进出库完毕"),
    SETTLEED(6, "结算完毕"),
    CLOSED(7, "关闭"),
    ARCHIVED(99, "归档完毕"),

    ;
    /*
        -1	作废
        1	新制合同
        2	合同生效
        3	正在执行
        4	提单签发完毕
        5	进出库完毕
        6	结算完毕
        7	关闭
        99	归档完毕
    */

    private int value;
    private String desc;

    LkgContractStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgContractStatusEnum getByValue(Integer value) {
        if (null == value) return DRAFT;
        for (LkgContractStatusEnum en : LkgContractStatusEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return DRAFT;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static String getNameByValue(int value) {
        return getByValue(value).name();

    }
}
