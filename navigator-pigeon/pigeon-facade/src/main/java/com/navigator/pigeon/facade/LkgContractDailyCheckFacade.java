package com.navigator.pigeon.facade;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: wang tao
 * @Date: 2022/6/27
 * @Time: 15:53
 * @Desception:
 */
@FeignClient(name = "navigator-pigeon-service")
public interface LkgContractDailyCheckFacade {

    /**
     * 定时获取解析文件
     *
     * @param
     * @return
     */
    @GetMapping("/dailyCheck")
    Result dailyCheck();


    /**
     * 改变文件状态(未启动到获取文件)
     *
     * @param
     * @return
     */
    @GetMapping("/changeToPull")
    Result changeToPull();


    /**
     * 改变文件状态(获取文件到合同数据)
     *
     * @param
     * @return
     */
    @GetMapping("/changeToRecord")
    Result changeToRecord();


    /**
     * 重试机制
     * @param
     * @return
     */
    @GetMapping("/resync")
    Result resync();


    /**
     * 保存文件记录
     *
     * @param lkgDailyFileEntity
     * @return
     */
    @PostMapping("saveDailyFile")
    Result saveDailyFile(@RequestBody LkgDailyFileEntity lkgDailyFileEntity);

}
