package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgContractProcessTypeEnum {
    ADD(1, 0, 100, "新增"),
    ADD_CLOSE(1, 0, 100, "新增(并关闭)"),
    UPDATE(0, 1, 200, "更新"),
    UPDATE_ADD(1, 1, 201, "更新并新增"),
    DELETE_ADD(1, 2, 202, "废除并新增"),
    DELETE(0, 2, 203, "废除"),
    PRICE(0, 0, 301, "定价"),
    PRICE_UPDATE(0, 0, 302, "定价更新"),
    QUERY(0, 0, 400, "查询"),
    ;

    private int needAdd;
    private int needUpdate;
    private int actionValue;
    private String desc;

    LkgContractProcessTypeEnum(int needAdd, int needUpdate, int actionValue, String desc) {
        this.needAdd = needAdd;
        this.needUpdate = needUpdate;
        this.actionValue = actionValue;
        this.desc = desc;
    }
}
