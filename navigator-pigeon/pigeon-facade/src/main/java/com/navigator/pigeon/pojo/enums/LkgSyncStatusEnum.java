package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgSyncStatusEnum {
    INIT(0, "新申请"),
    COMPLATE(1, "已同步"),
    SYNCING(2, "同步中"),//实际目前是失败中的含义
    ;

    private int value;
    private String desc;

    LkgSyncStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgSyncStatusEnum getByValue(Integer value) {
        if (null == value) return INIT;
        for (LkgSyncStatusEnum en : LkgSyncStatusEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return INIT;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
