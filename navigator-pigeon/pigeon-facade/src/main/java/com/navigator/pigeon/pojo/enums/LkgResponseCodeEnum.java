package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgResponseCodeEnum {
    UNKNOWN_ERROR("99999", "未知错误"),
    SUCCESS("1", "成功"),
    PARAMETER_ERROR("10100", "接口参数和自定校验错误"),
    BIZ_DATA_CHECK_ERROR("10200", "Data参数中业务数据校验错误"),
    BIZ_LOGIC_ERROR("10300", "业务逻辑校验错误"),
    SYSTEM_ERROR("10400", "系统错误"),
    INTERFACE_ERROR("90001", "接口调用错误"),
    INTERFACE_QUERY_ERROR("90002", "查询接口调用错误"),
    INTERFACE_QUERY_CLOSE("90003", "系统同步已关闭"),
    ;

    private String code;
    private String desc;

    LkgResponseCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LkgResponseCodeEnum getByCode(String code) {
        if (null == code) return UNKNOWN_ERROR;
        for (LkgResponseCodeEnum en : LkgResponseCodeEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return UNKNOWN_ERROR;
    }


    public static String getDescByValue(String code) {
        if (null == code) return "";
        return getByCode(code).getDesc();
    }
}
