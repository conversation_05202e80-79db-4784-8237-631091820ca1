package com.navigator.pigeon.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022--04--23:10:40
 */
@Data
@Accessors(chain = true)
public class LkgContractPriceDefDTO {
    private Integer status;
    private String settleNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date settleTime;
    private String fixPriceNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fixPriceTime;
    private String optUser;
    private String customerCode;
    private String customerName;
    private String customerContacts;
    private String customerFax = "";
    private String contractNumber;
    private String settleBatch;
    private String fixPriceBatch;
    private String commodityCode;
    private String commodityName;
    private String specifications;
    private BigDecimal yetSettleCount;
    private BigDecimal noSettleCount;
    private BigDecimal contractCount;
    private BigDecimal yetFixPriceCount;
    private BigDecimal noFixPriceCount;
    private String currencyType;
    private String futuresContractNo;
    private BigDecimal premiumPrice;
    private BigDecimal futuresPrice;
    private BigDecimal thisSettleCount;
    private BigDecimal thisSettlePrice;
    private BigDecimal thisFixPriceCount;
    private BigDecimal thisFixPricePrice;
    private BigDecimal fixPriceCount;
    private BigDecimal settleCount;
    private String finalAmount;
    private BigDecimal freight;
    private BigDecimal loadingFee;
    private BigDecimal subsidyOfPack;
    private BigDecimal subsidyOfSelf;
    private String describes;
    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String checkUser;
    private String remarks;

}
