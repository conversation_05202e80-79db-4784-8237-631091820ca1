package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgCalculateUnitEnum {
    TON("001", "公吨"),
    KG("002", "公斤"),
    BA("003", "把"),
    BEN("004", "本"),
    BU("005", "部"),
    CENG("006", "层"),
    BAG("007", "袋"),
    DING("008", "顶"),
    KWH("010", "度"),
    FU("011", "付"),
    GE("012", "个"),
    GEN("013", "根"),
    HE("014", "盒"),
    JIAN("015", "件"),
    JUAN("016", "卷"),
    KUAI("017", "块"),
    KUN("018", "捆"),
    M3("019", "立方米"),
    LI("020", "粒"),
    M("021", "米"),
    PIAN("022", "片"),
    M2("023", "平方米"),
    PING("024", "瓶"),
    L("025", "升"),
    SUANG("026", "双"),
    TAI("027", "台"),
    TANG("028", "樘"),
    TAO("029", "套"),
    TIAO("030", "条"),
    TONG("031", "桶"),
    XIANG("032", "箱"),
    ZHAN("033", "盏"),
    ZHANG("034", "张"),
    ZHI("035", "支"),
    ZI("036", "只"),
    PAN("037", "盘"),
    ;
        /*
        *   001	公吨
            002	公斤
            003	把
            004	本
            005	部
            006	层
            007	袋
            008	顶
            010	度
            011	付
            012	个
            013	根
            014	盒
            015	件
            016	卷
            017	块
            018	捆
            019	立方米
            020	粒
            021	米
            022	片
            023	平方米
            024	瓶
            025	升
            026	双
            027	台
            028	樘
            029	套
            030	条
            031	桶
            032	箱
            033	盏
            034	张
            035	支
            036	只
            037	盘
        */

    private String code;
    private String desc;

    LkgCalculateUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LkgCalculateUnitEnum getByCode(String code) {
        if (null == code) return TON;
        for (LkgCalculateUnitEnum en : LkgCalculateUnitEnum.values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return TON;
    }

    public static String getDescByValue(String code) {
        return getByCode(code).getDesc();
    }

    public static String getNameByValue(String code) {
        return getByCode(code).name();
    }
}
