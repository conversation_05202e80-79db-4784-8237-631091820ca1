package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_contract
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_contract")
@ApiModel(value = "DbiLkgContractEntity对象", description = "dbi_lkg_contract")
public class LkgContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "lkg合同编号")
    private String lkgContractCode;

    @ApiModelProperty(value = "nav合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "所属主体ID")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "账套")
    private String siteId;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "关联合同号")
    @TableField("ref_Contract_number")
    private String refcontractNumber;

    @ApiModelProperty(value = "定价方式")
    @TableField("price_Type")
    private Integer priceType;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "客户代码")
    private String customerCode;

    @ApiModelProperty(value = "货品代码")
    private String commodityCode;

    @ApiModelProperty(value = "定价状态")
    private Integer fixStatus;

    @ApiModelProperty(value = "合同状态")
    private Integer status;

    @ApiModelProperty(value = "查询业务类型（采购，销售）")
    private Integer salesType;

    @ApiModelProperty(value = "合同数量")
    private BigDecimal count;

    @ApiModelProperty(value = "合同出库数量")
    private BigDecimal inCount;

    @ApiModelProperty(value = "合同结余数量")
    @TableField("noInt_count")
    private BigDecimal nointCount;

    @ApiModelProperty(value = "合同开单数量")
    @TableField("contract_outCount")
    private BigDecimal contractOutcount;

    @ApiModelProperty(value = "合同未开单结余数量")
    @TableField("contract_noOutCount")
    private BigDecimal contractNooutcount;

    @ApiModelProperty(value = "合同实发数量")
    @TableField("contract_factOutCount")
    private BigDecimal contractFactoutcount;

    @ApiModelProperty(value = "合同未发货数量")
    @TableField("contract_noSendCount")
    private BigDecimal contractNosendcount;

    @ApiModelProperty(value = "总结价数量")
    @TableField("tolSettle_count")
    private BigDecimal tolsettleCount;

    @ApiModelProperty(value = "未结价数量")
    @TableField("tolNoSettle_count")
    private BigDecimal tolnosettleCount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
