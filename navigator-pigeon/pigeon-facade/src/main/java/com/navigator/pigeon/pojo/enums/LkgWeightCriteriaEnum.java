package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgWeightCriteriaEnum {
    DELIVERED(1, "以供方实发数量计算"),
    RECEIVED(2, "以需方实收数量计算"),
    RECEIVED_WITH_LIMIT(3, "以需方实收数量计算，每车货物供方最多承担0.1%损耗"),
    ;

    /*
        1	以供方实发数量计算
        2	以需方实收数量计算
        3	以需方实收数量计算，每车货物供方最多承担0.1%损耗
     */

    private int value;
    private String desc;

    LkgWeightCriteriaEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgWeightCriteriaEnum getByValue(Integer value) {
        if (null == value) return DELIVERED;
        for (LkgWeightCriteriaEnum en : LkgWeightCriteriaEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return DELIVERED;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
