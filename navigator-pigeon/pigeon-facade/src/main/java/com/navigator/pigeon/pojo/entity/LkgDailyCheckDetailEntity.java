package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_daily_file
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_daily_check_detail")
@ApiModel(value="LkgDailyCheckDetailEntity对象", description="dbi_lkg_daily_file")
public class LkgDailyCheckDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "校核日期yyyyMMdd")
    private String checkDay;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "lkg合同编号")
    private String lkgContractCode;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "nav合同量")
    private BigDecimal navCount;

    @ApiModelProperty(value = "nav合同定价量")
    private Double navPriceCount;

    @ApiModelProperty(value = "nav入库量")
    private Double navInCount;

    @ApiModelProperty(value = "nav出库量")
    private Double navOutCount;

    @ApiModelProperty(value = "nav账套")
    private String navSiteId;

    @ApiModelProperty(value = "nav合同状态")
    private Integer navStatus;

    @ApiModelProperty(value = "lkg合同量")
    private BigDecimal lkgCount;

    @ApiModelProperty(value = "lkg合同定价量")
    private Double lkgPriceCount;

    @ApiModelProperty(value = "lkg入库量")
    private Double lkgInCount;

    @ApiModelProperty(value = "lkg出库量")
    private Double lkgOutCount;

    @ApiModelProperty(value = "lkg账套")
    private String lkgSiteId;

    @ApiModelProperty(value = "lkg合同状态")
    private Integer lkgStatus;

    @ApiModelProperty(value = "校核结果 0:未校核 1:校核通过 其他:校核不通过")
    private Integer result;

    @ApiModelProperty(value = "不通过原因")
    private String resultMemo;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;


}
