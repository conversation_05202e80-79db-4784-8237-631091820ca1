package com.navigator.pigeon.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022--04--23:10:35
 */
@Data
@Accessors(chain = true)
public class LkgSalesContractModifyDefDTO {

    /**
     * 状态: 1、NAV要么传合同的状态 2、NAV如果不传，默认状态不变
     */
    private Integer status;

    /**
     * 定价状态
     */
    private Integer fixStatus;

    /**
     * 代签单位: 非必填字段，业务意义已经不存在，建议传空值
     */
    private String signingUnit;

    /**
     * 变更单号: 取TT编号
     */
    private String changeOrderNo;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 相关合同号: 第三方合同编号，取NAV的合同号
     */
    private String refContractNumber;

    /**
     * 签订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;

    /**
     * 签订地点
     */
    private String signAddress;

    /**
     * 变更时间: 取TT创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;

    /**
     * 开始交货日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDeliveryTime;

    /**
     * 交(提)货日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDeliveryTime;

    /**
     * 定价方式
     */
    private Integer modeOfPrice;

    /**
     * 基差定价
     */
    private Integer isBasic;

    /**
     * 定价延期
     */
    private Integer isDelay;

    /**
     * 定价截止至日期
     */
    private String endFixPriceTime;

    /**
     * 赊销: 根据付款方式确定
     */
    private Integer isSellOfCredit;

    /**
     * CNF: 自提0 配送1
     */
    private Integer isSellOfCNF;

    /**
     * 供应方: 取LDC主体
     */
    private String supplier;

    /**
     * 合同出具人
     */
    private String exeOfficer;

    /**
     * TT创建者
     */
    private String trader;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 是否需提供发票 1是，0否
     */
    private Integer isProvideInvoice;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 关联合同号：内部合同号
     */
    private String fromContractNumber;

    /**
     * 关联合同日期：原合同的签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fromContractSignTime;

    /**
     * 变更原因： 取TT"备注"字段
     */
    private String changeReason;

    /**
     * 品质描述
     */
    private String qualityDesc;

    /**
     * 交货地点: deliveryFactory 交货工厂
     */
    private String deliveryAddress;

    /**
     * 交货方式: deliveryType交提货方式（卖方车板交货/码头卖方船板自提）  参见字典
     */
    private Integer modeOfDelivery;

    /**
     * 发货库点
     */
    private String storehouseAddress;

    /**
     * 运费计算方式	1需方承担 2供方承担
     */
    private Integer freightCalcMode;

    /**
     * 运费暂定价格
     */
    private BigDecimal freightProvPrice;

    /**
     * 包装方式
     */
    private String modeOfPack;

    /**
     * 包装计算重量:	1是 0否
     */
    private Integer isPackCalcWeight;

    /**
     * 袋皮扣重(kg)
     */
    private BigDecimal bagSkinWeight;

    /**
     * 需安排运输: 1是0否
     */
    private Integer isTransport;

    /**
     * 重量验收标准
     */
    private Integer weightCriteria;

    /**
     *超发限额
     */
    private BigDecimal superHairAmount;

    /**
     * 履约保证金比例
     */
    private BigDecimal bondRate;

    /**
     * 方向
     */
    private String direction;

    /**
     * 目的港
     */
    private String toHarbor;

    /**
     * 延期罚款比(%/天)
     */
    private BigDecimal rateOfDelayPenalty;

    /**
     * 定金比例
     */
    private BigDecimal depositRate;

    /**
     * 定金金额
     */
    // BUGFIX：case-1002725 基差暂定价合同定价完毕后NAV中含税单价和LKG中含税单价对不上 Author: Mr 2024-10-24 Start
    // private BigDecimal depositAmount;
    private String depositAmount;
    // BUGFIX：case-1002725 基差暂定价合同定价完毕后NAV中含税单价和LKG中含税单价对不上 Author: Mr 2024-10-24 End

    /**
     * 付定金日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payDepositTime;

    /**
     * 付全款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payFullTime;

    /**
     * 付款条件: creditDays赊销账期+depositAmount应付履约保证金
     */
    private String paymentCondition;

    /**
     * 补充条款
     */
    private String suppleProvisions;

    /**
     * 制单人: createdBy发起人 TTAddEntity
     */
    private String createUser;

    /**
     * 制单日期: 合同创建时间 ContractEntity
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 签发人
     */
    private String checkUser;

    /**
     * 签发日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

    /**
     * 备注
     */
    private String remarks;
    private String payConditionCode;
    private ArrayList<LkgSalesContractModifyDetailDTO> detaillist;

}
