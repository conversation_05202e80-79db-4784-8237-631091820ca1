package com.navigator.pigeon.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022--04--22:22:35
 */
@Data
@Accessors(chain = true)
public class SyncQueryRecordDTO {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同ID")
    private Integer contractId;

    @ApiModelProperty(value = "采销类型")
    private int salesType;

    @ApiModelProperty(value = "1：新增 2：更新 3：定价 4:查询")
    private Integer syncType;

    @ApiModelProperty(value = "关联合同号")
    private String refContractNumber;
}
