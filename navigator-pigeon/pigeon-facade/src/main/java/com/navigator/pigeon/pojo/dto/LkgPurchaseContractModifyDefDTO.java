package com.navigator.pigeon.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022--04--23:10:29
 */
@Data
@Accessors(chain = true)
public class LkgPurchaseContractModifyDefDTO {

    private Integer status;
    private String changeOrderNo;
    private String contractNumber;
    private String refContractNumber;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;
    private String signAddress;
    private Integer tradeNature;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;
    private Integer invoiceType;
    private Integer isProvideInvoice;
    private String supplierCode;
    private String supplierName;
    private BigDecimal overchargeRate;
    private String demander;
    private String exeOfficer;
    private String trader;
    private String changeReason;
    private String transactionType;
    private String fromContractNumber;
    private String fromContractSignTime;
    private String deliveryAddress;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDeliveryTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDeliveryTime;
    private String qualityCheckExplain;
    private String paymentExplain;
    private Integer isPaymentPurchase;
    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String checkUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;
    private String remarks;
    private String payConditionCode;
    private Integer modeOfDelivery;
    private ArrayList<LkgPurchaseContractModifyDetailDTO> detaillist;

    private Integer isBasic;    // 基差定价
    private Integer isDelay;    // 定价延期
    private Integer fixStatus;  // 定价状态
    private Integer modeOfPrice;// 定价方式

}
