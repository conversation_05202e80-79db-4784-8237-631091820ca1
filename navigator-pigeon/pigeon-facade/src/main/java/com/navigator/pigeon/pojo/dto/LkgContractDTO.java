package com.navigator.pigeon.pojo.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LkgContractDTO {

    @ApiModelProperty(value = "自增ID")
    private Integer id;
    private String contractCode;
    private String contractNumber;
    private String refContractNumber;
    private Integer priceType;
    private String supplierCode;
    private String customerCode;
    private String commodityCode;
    private Double count;
    private Double inCount;
    private Double noIntCount;
    private Double contractOutCount;
    private Double contractNoOutCount;
    private Double contractFactOutCount;
    private Double contractNoSendCount;
    private Double tolSettleCount;
    private Double tolNoSettleCount;
    private Integer fixStatus;
    private Integer status;
    private Integer queryType;
    private String remark;

    /**
     * 采购订单数量
     */
    private Double orderCount;

    /**
     * 销售调拨数量
     */
    private Double dbOrderCount;
}
