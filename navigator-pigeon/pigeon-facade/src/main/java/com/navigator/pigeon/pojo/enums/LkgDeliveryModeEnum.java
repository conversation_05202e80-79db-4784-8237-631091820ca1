package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgDeliveryModeEnum {
    SHIP_SPORT_TAKE(1, "码头卖方船板 自提"),
    SHIP_CPORT_TAKE(2, "码头买方船板 交货"),
    PORT_TRUCK_TAKE(3, "码头买房汽车板 交货"),
    WAREHOUSE_RENT_TAKE(4, "外租库自提"),
    WAREHOUSE_RENT_SEND(5, "外租库买房汽车板 交货"),
    DONGGUAN_TRUCK_TKAE(6, "东莞工厂汽车板 交货"),
    TRAIN_STATION_TAKE(7, "火车站火车版自提"),
    KAGNDIHUAMEI_TRUCK_TKAE(8, "康地华美工厂交货"),
    SHANGGAO_TRUCK_TKAE(9, "江西上高双胞胎实业工厂买方汽车板 交货"),
    WANNIAN_TRUCK_TKAE(10, "江西万年双胞胎牧业工厂买方汽车板 交货"),
    TANGRENSHEN_TRUCK_TKAE(11, "唐人神集团工厂 交货"),
    TRUCK_SEND(12, "送到买房工厂仓库 卖方汽车板交货"),
    ;

    /*
        1	码头卖方船板 自提
        2	码头买方船板 交货
        3	码头买房汽车板 交货
        4	外租库自提
        5	外租库买房汽车板 交货
        6	东莞工厂汽车板 交货
        7	火车站火车版自提
        8	康地华美工厂交货
        9	江西上高双胞胎实业工厂买方汽车板 交货
        10	江西万年双胞胎牧业工厂买方汽车板 交货
        11	唐人神集团工厂 交货
        12	送到买房工厂仓库 卖方汽车板交货
     */

    private int value;
    private String desc;

    LkgDeliveryModeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgDeliveryModeEnum getByValue(Integer value) {
        if (null == value) return SHIP_SPORT_TAKE;
        for (LkgDeliveryModeEnum en : LkgDeliveryModeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return SHIP_SPORT_TAKE;
    }


    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static String getNameByValue(Integer value) {
        return getByValue(value).name();
    }
}
