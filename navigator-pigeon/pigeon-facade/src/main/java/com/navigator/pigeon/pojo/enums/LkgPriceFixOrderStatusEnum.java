package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgPriceFixOrderStatusEnum {
    UNPRICE(0, "未定价"),
    PRICING(1, "定价中"),
    PRICED(2, "定价完毕"),
    ;
/*    0	未定价
1	定价中
2	定价完毕*/

    private int value;
    private String desc;

    LkgPriceFixOrderStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgPriceFixOrderStatusEnum getByValue(Integer value) {
        for (LkgPriceFixOrderStatusEnum en : LkgPriceFixOrderStatusEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        LkgPriceFixOrderStatusEnum byValue = getByValue(value);
        return byValue == null ? "" : byValue.getDesc();
    }
}
