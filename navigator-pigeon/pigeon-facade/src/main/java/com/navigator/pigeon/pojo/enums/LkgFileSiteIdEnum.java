package com.navigator.pigeon.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LkgFileSiteIdEnum {

    SITES_30("30", "_30_sales_detail.csv"),
    SITEP_30("30", "_30_purchase_detail.csv"),
    SITES_28("28", "_28_sales_detail.csv"),
    SITEP_28("28", "_28_purchase_detail.csv"),
    SITES_29("29", "_29_sales_detail.csv"),
    SITEP_29("29", "_29_purchase_detail.csv"),
    SITES_34("34", "_34_sales_detail.csv"),
    SITEP_34("34", "_34_purchase_detail.csv"),
    SITES_80("80", "_80_sales_detail.csv"),
    SITEP_80("80", "_80_purchase_detail.csv"),
    SITES_05("05", "_05_sales_detail.csv"),
    SITEP_05("05", "_05_purchase_detail.csv"),
    SITES_38("38", "_38_sales_detail.csv"),
    SITEP_38("38", "_38_purchase_detail.csv"),

    ;
    private String siteId;
    private String fileCsv;


}
