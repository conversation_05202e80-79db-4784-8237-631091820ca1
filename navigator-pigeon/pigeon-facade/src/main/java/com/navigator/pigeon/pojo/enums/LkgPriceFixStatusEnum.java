package com.navigator.pigeon.pojo.enums;

import com.navigator.common.util.BigDecimalUtil;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
public enum LkgPriceFixStatusEnum {
    UNPRICE(0, "未定价"),
    PRICING(1, "定价中"),
    PRICED(2, "定价完毕"),
    ;
/*    0	未定价
1	定价中
2	定价完毕*/

    private int value;
    private String desc;

    LkgPriceFixStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgPriceFixStatusEnum getByValue(Integer value) {
        for (LkgPriceFixStatusEnum en : LkgPriceFixStatusEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        LkgPriceFixStatusEnum byValue = getByValue(value);
        return byValue == null ? "" : byValue.getDesc();
    }

    public static LkgPriceFixStatusEnum getByContractType(Integer contractType) {
        if (null == contractType) return UNPRICE;

        if (contractType == ContractTypeEnum.YI_KOU_JIA.getValue()) {
            return PRICED;
        }

        if (contractType == ContractTypeEnum.JI_CHA.getValue()
                || contractType == ContractTypeEnum.ZAN_DING_JIA.getValue()
                || contractType == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
            return UNPRICE;
        }
        return UNPRICE;
    }

    public static LkgPriceFixStatusEnum getByContractNum(Integer contractType, BigDecimal contractNum, BigDecimal pricedNum) {
        if (null == contractNum || null == pricedNum) return UNPRICE;

        if (contractType == ContractTypeEnum.YI_KOU_JIA.getValue()) {
            return PRICED;
        }

        if (contractType == ContractTypeEnum.JI_CHA.getValue()
                || contractType == ContractTypeEnum.ZAN_DING_JIA.getValue()
                || contractType == ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
            return BigDecimalUtil.isEqual(contractNum, pricedNum) ? PRICED : UNPRICE;
        }
        return UNPRICE;
    }
}
