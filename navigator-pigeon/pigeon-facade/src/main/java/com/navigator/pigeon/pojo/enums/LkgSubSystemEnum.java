package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgSubSystemEnum {
    /*
        00，路易达孚(中国)贸易有限公司
        01，路易达孚LDSHGRAIN
        02，路易达孚霸州包销
        04，路易达孚天津包销
        05，路易达孚（中国）糖
        06， 路易达孚天津工厂
    */

    LDC_CN("00", "LDC", "路易达孚(中国)贸易有限公司"),
    LDC_SHGRAIN("01", "SH", "路易达孚LDSHGRAIN"),
    LDC_BZ("02", "BZ", "路易达孚霸州包销"),
    LDC_TJ_SALES("04", "TJB", "路易达孚天津包销"),
    LDC_CN_SUGER("05", "LDCS", "路易达孚（中国）糖"),
    LDC_TJ_FACTORY("06", "TJF", "路易达孚天津工厂"),

    ;

    private String lkgCode;
    private String navCode;
    private String lkgName;

    LkgSubSystemEnum(String lkgCode, String navCode, String lkgName) {
        this.lkgCode = lkgCode;
        this.navCode = navCode;
        this.lkgName = lkgName;
    }

    public static LkgSubSystemEnum getByNavCode(String navCode) {
        if (null == navCode) return LDC_CN;
        for (LkgSubSystemEnum en : LkgSubSystemEnum.values()) {
            if (navCode.equals(en.getNavCode())) {
                return en;
            }
        }
        return LDC_CN;
    }

    public static String getLkgCode(String navCode) {
        return getByNavCode(navCode).getLkgCode();
    }

    public static LkgSubSystemEnum getByLkgCode(String lkgCode) {
        if (null == lkgCode) return LDC_CN;
        for (LkgSubSystemEnum en : LkgSubSystemEnum.values()) {
            if (lkgCode.equals(en.getLkgCode())) {
                return en;
            }
        }
        return LDC_CN;
    }
}
