package com.navigator.pigeon.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class LkgSalesContractDetailDTO {
    /**
     * 货品代码：lkg货品编码
     */
    @Excel(name = "货品代码", orderNum = "13")
    private String commodityCode;
    /**
     * 货品名称
     */
    @Excel(name = "货品名称", orderNum = "14")
    private String commodityName;
    /**
     * 规格：示例传值43%
     */
    @Excel(name = "规格", orderNum = "15")
    private String specifications;
    /**
     * 合同总数量
     */
    @Excel(name = "数量", orderNum = "16")
    private BigDecimal count;
    /**
     * 含税单价
     */
    @Excel(name = "单价", orderNum = "17")
    private BigDecimal price;
    /**
     * 折算FOB价格：含税单价-物流相关费用
     */
    private BigDecimal priceOfFOB;
    /**
     * 计量单位
     */
    @Excel(name = "计量单位", orderNum = "18")
    private String calculateUnit;
    /**
     * 金额
     */
    @Excel(name = "金额", orderNum = "19")
    private String finalAmount;
    /**
     * 期货合约
     */
    @Excel(name = "期货合约", orderNum = "20")
    private String futuresContractNo;
    /**
     * 币种
     */
    private String currencyType;
    /**
     * 期货价格
     */
    @Excel(name = "期货价格", orderNum = "21")
    private BigDecimal futuresPrice;
    /**
     * 升贴水价格
     */
    @Excel(name = "升贴水价格", orderNum = "22")
    private BigDecimal premiumPrice;
    /**
     * 装运费单价
     */
    private BigDecimal shippingPrice;
    /**
     * 损耗率 默认0
     */
    private BigDecimal rateOfLoss;
    /**
     * 人民币汇率 默认1
     */
    private BigDecimal rateOfRMB;
    private String amountOfRMB;
    /**
     * 质量说明
     */
    private String qualityExplain;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 单价含运费，默认1
     */
    private Integer isShippingChargesPrice;
    private String commodityClass;
    /**
     * 单位重量：
     * （1）豆油：传空
     * （2）豆粕：包装为：50KG传50公斤，70KG传70公斤，其他传空值
     */
    private BigDecimal unitWeight;

}
