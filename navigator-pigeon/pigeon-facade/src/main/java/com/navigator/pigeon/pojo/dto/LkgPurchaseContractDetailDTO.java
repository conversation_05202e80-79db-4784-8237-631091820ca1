package com.navigator.pigeon.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class LkgPurchaseContractDetailDTO {
    @Excel(name = "货品代码", orderNum = "12")
    private String commodityCode;
    @Excel(name = "货品名称", orderNum = "13")
    private String commodityName;
    @Excel(name = "规格", orderNum = "14")
    private String specifications;
    private String demandNo;
    @Excel(name = "数量", orderNum = "15")
    private BigDecimal count;
    @Excel(name = "计量单位", orderNum = "17")
    private String calculateUnit;
    @Excel(name = "单价", orderNum = "16")
    private BigDecimal hasTaxPrice;
    private BigDecimal rateOfTax;
    private BigDecimal noTaxPrice;
    private String currencyType;
    private BigDecimal rateOfRMB;
    @Excel(name = "金额", orderNum = "18")
    private String amountOfRMB;
    private BigDecimal giftCount;
    private BigDecimal shippingPrice;
    private String qualityExplain;
    private String batchNo;
    private String remarks;
    private Integer isShippingChargesPrice;
    private String commodityClass;
    private String futuresContractNo;
    private BigDecimal futuresPrice;
    private BigDecimal premiumPrice;
    private BigDecimal priceOfFOB;

}
