package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * lkg_sync_request
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_sync_request")
@ApiModel(value = "DbiLkgSyncRequestEntity对象", description = "lkg_sync_request")
public class LkgSyncRequestEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "TTid")
    private Integer ttId = -1;

    @ApiModelProperty(value = "合同编号")
    private String contractCode = "";

    @ApiModelProperty(value = "合同ID")
    private Integer contractId = -1;

    @ApiModelProperty(value = "根合同ID")
    private Integer parentContractId = 0;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType = 0;

    @ApiModelProperty(value = "客户id")
    private String customerCode = "";

    @ApiModelProperty(value = "定价单")
    private Integer confirmPriceId = -1;

    @ApiModelProperty(value = "交货工厂是否变化")
    private Integer isChangeFactory = 0;

    @ApiModelProperty(value = "请求场景")
    private Integer tradeType = 0;

    @ApiModelProperty(value = "请求信息json")
    private String requestInfo;

    @ApiModelProperty(value = "同步时间")
    private Date syncTime;

    @ApiModelProperty(value = "同步状态 待完成，已完成，未完成")
    private Integer syncStatus;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "备注")
    private String remark = "";

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
