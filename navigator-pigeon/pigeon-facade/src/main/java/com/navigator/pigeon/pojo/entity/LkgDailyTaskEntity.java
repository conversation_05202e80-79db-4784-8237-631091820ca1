package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_daily_file
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_daily_task")
@ApiModel(value="LkgDailyTaskEntity对象", description="dbi_lkg_daily_file")
public class LkgDailyTaskEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "检查日期yyyyMMdd")
    private String checkDate;

    @ApiModelProperty(value = "0:未开始 1:获取文件中 2:落地数据中 3:NAV校核中 4:LKG校核中 5:生成校核报告中 9:已完成")
    private Integer handleStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    private Date updatedAt;


}
