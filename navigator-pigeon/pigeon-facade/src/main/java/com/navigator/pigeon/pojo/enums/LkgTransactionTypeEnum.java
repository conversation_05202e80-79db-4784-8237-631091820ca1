package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgTransactionTypeEnum {
    DEFAULT("", ""),
    TRANSFER("Transfer", "转厂"),
    NEW("NEW", "新增"),
    RESALE("RESAL<PERSON>", "回售"),
    SPLIT("<PERSON>L<PERSON>", "拆分"),
    BUYBACK("BUY<PERSON><PERSON><PERSON>", "回购"),
    REVISE("REVISE", "更新"),
    ;

    /*
        Transfer	Transfer，即交货工厂变化
        NEW	NEW
        RESALE	RESALE，回售，一期不涉及
        SPLIT	SPLIT，拆分，即交货工厂未变化的拆分
     */

    private String code;
    private String desc;

    LkgTransactionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LkgTransactionTypeEnum getByCode(String code) {
        if (null == code) return NEW;
        for (LkgTransactionTypeEnum en : LkgTransactionTypeEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return NEW;
    }


    public static String getDescByValue(String code) {
        return getByCode(code).getDesc();
    }
}
