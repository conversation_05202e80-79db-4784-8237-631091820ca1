package com.navigator.pigeon.pojo.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ImportSalesContract {

    @Excel(name = "合同编号")
    private String contractNumber;

    @Excel(name = "签订时间", databaseFormat = "yyyy-MM-dd")
    private Date signTime;

    @Excel(name = "开始交货时间", databaseFormat = "yyyy-MM-dd")
    private Date beginDeliveryTime;

    @Excel(name = "截止交货时间", databaseFormat = "yyyy-MM-dd")
    private Date endDeliveryTime;

    @Excel(name = "定价方式")
    private String modeOfPrice;

    @Excel(name = "基差定价")
    private String isBasic;

    @Excel(name = "延期定价")
    private String isDelay;

    @Excel(name = "定价状态")
    private String fixStatus;

    @Excel(name = "定价截止日期类型")
    private String endFixPriceType;

    @Excel(name = "定价截止日期")
    private String endFixPriceTime;

    @Excel(name = "赊销天数")
    private String creditDays;

    @Excel(name = "发票类型")
    private String invoiceType;

    @Excel(name = "履约保证金释放方式")
    private String depositReleaseType;

    @Excel(name = "Trader")
    private String trader;

    @Excel(name = "交货工厂")
    private String deliveryFactory;

    @Excel(name = "交提货方式名称")
    private String modeOfDeliveryCode;

    @Excel(name = "发货库点编码")
    private String storeHouseAddress;

    @Excel(name = "发货库点名称")
    private String warehouseName;

    @Excel(name = "包装计算重量")
    private String isPackCalcWeight;

    @Excel(name = "袋皮扣重(kg)")
    private String packUnitWeight;

    @Excel(name = "重量验收名称")
    private String weightCriteria;

    @Excel(name = "超发数限额(%)")
    private String superHairAmount;

    @Excel(name = "目的港编码")
    private String toHarborCode;

    @Excel(name = "数量")
    private String count;

    @Excel(name = "卖方主体名称")
    private String supplierName;

    @Excel(name = "买方主体名称")
    private String customerName;

    @Excel(name = "客户代码")
    private String customerCode;

    @Excel(name = "集团客户代码")
    private String groupCustomerCode;

    @Excel(name = "交易类型")
    private String transactionType;

    @Excel(name = "STF")
    private String isStf;

    @Excel(name = "代加工")
    private String isSellOfOem;

    @Excel(name = "制单人")
    private String createUser;

    @Excel(name = "制单日期", databaseFormat = "yyyy-MM-dd")
    private Date createTime;

    @Excel(name = "签发人")
    private String checkUser;

    @Excel(name = "签发日期", databaseFormat = "yyyy-MM-dd")
    private Date checkTime;

    /**
     * 转月次数
     */
    @Excel(name = "可转月次数")
    private String ableTransferTimes;

    @Excel(name = "已转月次数")
    private String transferredTimes;

    @Excel(name = "可反点价次数")
    private String ableReversePriceTimes;

    @Excel(name = "已反点价次数")
    private String reversedPriceTimes;

    @Excel(name = "履约保证金比例")
    private String depositRate;

    @Excel(name = "履约保证金点价后补缴")
    private String bondRate;

    @Excel(name = "履约保证金金额")
    private String depositAmount;

    /**
     * 价格明细
     */
    @Excel(name = "基差价")
    private String extraPrice;

    @Excel(name = "期货价格")
    private String futuresPrice;

    @Excel(name = "蛋白价差")
    private String proteinDiffPrice;

    @Excel(name = "散粕补贴")
    private String compensationPrice;

    @Excel(name = "期权费")
    private String optionPrice;

    @Excel(name = "运费")
    private String transportPrice;

    @Excel(name = "起吊费")
    private String liftingPrice;

    @Excel(name = "滞期费")
    private String delayPrice;

    @Excel(name = "高温费")
    private String temperaturePrice;

    @Excel(name = "其他物流费")
    private String otherDeliveryPrice;

    @Excel(name = "回购折价")
    private String buyBackPrice;

    @Excel(name = "客诉折价")
    private String complaintDiscountPrice;

    @Excel(name = "转厂补贴")
    private String transferFactoryPrice;

    @Excel(name = "其他补贴")
    private String otherPrice;

    @Excel(name = "商务补贴")
    private String businessPrice;

    @Excel(name = "手续费")
    private String fee;

    @Excel(name = "精炼价差")
    private String refineDiffPrice;

    @Excel(name = "精炼/分提价差")
    private BigDecimal refineFracDiffPrice;

    @Excel(name = "检验费")
    private BigDecimal surveyFees;

    @Excel(name = "不含税单价")
    private String cifUnitPrice;

    @Excel(name = "Fob单价")
    private String fobUnitPrice;

    @Excel(name = "含税单价")
    private String unitPrice;

    @Excel(name = "总金额")
    private String totalAmount;

    @Excel(name = "备注")
    private String billMemo;

    @Excel(name = "发票后补缴货款比例")
    private String invoicePaymentRate;

    @Excel(name = "付款条件编码")
    private String paymentTermCode;

    @Excel(name = "品种代码")
    private String futureCode;

    @Excel(name = "期货合约")
    private String futuresContractNo;

    @Excel(name = "货品代码")
    private String commodityCode;

    @Excel(name = "货品全称")
    private String commodityFullName;

    @Excel(name = "货品名称")
    private String commodityName;

    @Excel(name = "二级品类")
    private String category2;

    @Excel(name = "品种")
    private String category3;

    @Excel(name = "账套名称")
    private String siteName;

    @Excel(name = "企标类型")
    private String standardType;

    @Excel(name = "企标文件编号")
    private String standardFileNo;

    @Excel(name = "企标备注")
    private String standardRemark;

    @Excel(name = "用途")
    private String usage;

    @Excel(name = "业务线")
    private String buCode;

}
