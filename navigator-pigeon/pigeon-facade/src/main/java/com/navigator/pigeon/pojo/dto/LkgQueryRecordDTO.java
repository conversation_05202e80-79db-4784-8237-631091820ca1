package com.navigator.pigeon.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022--04--22:22:42
 */
@Data
@Accessors(chain = true)
public class LkgQueryRecordDTO {

    @ApiModelProperty(value = "nav合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "关联合同号")
    private String refContractNumber;

    @ApiModelProperty(value = "合同code集合")
    private List<String> contractCodeList;


}
