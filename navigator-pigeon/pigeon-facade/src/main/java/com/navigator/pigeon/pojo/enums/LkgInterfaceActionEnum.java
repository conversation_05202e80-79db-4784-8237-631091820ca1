package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgInterfaceActionEnum {
    ADD(100, 1, "ADD", "新增"),
    UPDATE(201, 2, "UPDATE", "更新"),
    DELETE(202, 21, "UPDATE", "废除"),
    PRICE(301, 3, "PRICE", "定价"),
    PRICE_UPDATE(302, 31, "PRICE_UPDATE", "定价更新"),
    SALES_PRICE_UPDATE(302, 32, "SALES_PRICE_UPDATE", "结价更新"),
    QUERY(400, 4, "QUERY", "查询"),

    // 兼容ATLAS LKG接口不存在的动作类型
    CLOSE(500, 5, "CLOSE", "关闭"),
    WARRANT_WITHDRAW(600, 6, "WARRANT_WITHDRAW", "注销撤回"),
    ;


    private int value;
    private int syncType;
    private String actionName;
    private String desc;

    LkgInterfaceActionEnum(int value, int syncType, String actionName, String desc) {
        this.value = value;
        this.syncType = syncType;
        this.actionName = actionName;
        this.desc = desc;
    }

    public static LkgInterfaceActionEnum getByValue(Integer value) {
        if (null == value) return ADD;

        for (LkgInterfaceActionEnum en : LkgInterfaceActionEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }

        return ADD;
    }

    public static LkgInterfaceActionEnum getBySyncType(Integer syncType) {
        if (null == syncType) return ADD;

        for (LkgInterfaceActionEnum en : LkgInterfaceActionEnum.values()) {
            if (en.getSyncType() == syncType) {
                return en;
            }
        }

        return ADD;
    }

}
