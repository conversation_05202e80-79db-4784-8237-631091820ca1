package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("[BI].[Lkg_ContractInfo]")
@ApiModel(value = "LkgContractInfoEntity对象", description = "")
public class LkgContractInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("IdKey")
    private Long IdKey;

    @TableField("contractNumber")
    private String contractNumber;

    @TableField("refContractNumber")
    private String refContractNumber;

    @TableField("priceType")
    private Integer priceType;

    @TableField("customerCode")
    private String customerCode;

    @TableField("commodityCode")
    private String commodityCode;

    private BigDecimal count;

    @TableField("contractOutCount")
    private BigDecimal contractOutCount = BigDecimal.ZERO;

    @TableField("contractNoOutCount")
    private BigDecimal contractNoOutCount;

    @TableField("contractFactOutCount")
    private BigDecimal contractFactOutCount = BigDecimal.ZERO;

    @TableField("contractNoSendCount")
    private BigDecimal contractNoSendCount;

    @TableField("tolSettleCount")
    private BigDecimal tolSettleCount;

    @TableField("tolNoSettleCount")
    private BigDecimal tolNoSettleCount;

    @TableField("fixStatus")
    private Integer fixStatus;

    private Integer status;

    private String remark;

    @TableField("dbOrderCount")
    private BigDecimal dbOrderCount = BigDecimal.ZERO;

    @TableField("InsertDate")
    private Date InsertDate;

}