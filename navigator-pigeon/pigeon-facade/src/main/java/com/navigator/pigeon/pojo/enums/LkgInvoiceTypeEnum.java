package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgInvoiceTypeEnum {
    SPECIAL(1, "SPECIAL", "增值税专用发票"),
    NORMAL(2, "NORMAL", "增值税普通发票"),
    SPECIAL_17(3, "SPECIAL", "增值税专用发票17%"),
    SPECIAL_6(4, "SPECIAL", "增值税专用发票 6%"),
    SPECIAL_3(5, "SPECIAL", "增值税专用发票 3%"),
    SPECIAL_11(6, "SPECIAL", "增值税专用发票11%"),
    NORMAL_11(7, "NORMAL", "增值税普通发票11%"),
    SPECIAL_10(8, "SPECIAL", "增值税专用发票10%"),
    NORMAL_10(9, "NORMAL", "增值税普通发票10%"),
    SPECIAL_16(10, "SPECIAL", "增值税专用发票16%"),
    NORMAL_16(11, "NORMAL", "增值税普通发票16%"),
    ;

    /*
        3	增值税专用发票17%
        4	增值税专用发票 6%
        5	增值税专用发票 3%
        6	增值税专用发票11%
        7	增值税普通发票11%
        8	增值税专用发票10%
        9	增值税普通发票10%
        10	增值税专用发票16%
        11	增值税普通发票16%
     */

    private int value;
    private String type;
    private String desc;

    LkgInvoiceTypeEnum(int value, String type, String desc) {
        this.value = value;
        this.type = type;
        this.desc = desc;
    }

    public static LkgInvoiceTypeEnum getByValue(Integer value) {
        if (null == value) return SPECIAL_17;
        for (LkgInvoiceTypeEnum en : LkgInvoiceTypeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return SPECIAL_17;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
