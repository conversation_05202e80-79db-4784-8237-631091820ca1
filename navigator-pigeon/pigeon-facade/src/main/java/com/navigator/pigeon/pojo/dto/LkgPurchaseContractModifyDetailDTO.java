package com.navigator.pigeon.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022--04--23:10:32
 */
@Data
@Accessors(chain = true)
public class LkgPurchaseContractModifyDetailDTO {

    private String commodityCode;
    private String commodityName;
    private String specifications;
    private BigDecimal count;
    private BigDecimal price;
    private BigDecimal priceOfFOB;
    private String calculateUnit;
    private String finalAmount;
    private String currencyType;
    private String futuresContractNo;
    private BigDecimal futuresPrice;
    private BigDecimal premiumPrice;
    private BigDecimal shippingPrice;
    private BigDecimal rateOfLoss;
    private BigDecimal rateOfRMB;
    private String amountOfRMB;
    private String qualityExplain;
    private String remarks;
    private Integer isShippingChargesPrice;
    private BigDecimal unitWeight;
    private BigDecimal hasTaxPrice;
    private BigDecimal noTaxPrice;
    private BigDecimal rateOfTax;
    private String contractNumber;

}
