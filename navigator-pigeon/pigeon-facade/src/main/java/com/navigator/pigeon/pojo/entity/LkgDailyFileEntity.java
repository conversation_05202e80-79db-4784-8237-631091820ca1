package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_daily_file
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_daily_file")
@ApiModel(value="DbiLkgDailyFileEntity对象", description="dbi_lkg_daily_file")
public class LkgDailyFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "文件名称")
    private String fileName;


    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件来源（nav/lkg）")
    private String fileSource;

    @ApiModelProperty(value = "是否生成（已生成，未生成）预警")
    private Integer isGenerate;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "检查日期")
    private String checkDate;

    @ApiModelProperty(value = "账套id（以上三者确定文件路径）")
    private String siteId;

    @ApiModelProperty(value = "获取状态")
    private Integer pullStatus;

    @ApiModelProperty(value = "总条数")
    private Integer totalCount;

    @ApiModelProperty(value = "当前处理条数")
    private Integer currentCount;

    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    @ApiModelProperty(value = "处理状态")
    private Integer handleStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
