package com.navigator.pigeon.pojo.enums;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import lombok.Getter;

@Getter
public enum LkgInterfaceEnum {
    PURCHASE_ADD("10101", 1, "ADD", "采购合同新增接口"),
    PURCHASE_UPDATE("10102", 1, "UPDATE", "采购合同更新接口"),
    PURCHASE_PRICE("10103", 1, "PRICE", "采购合同定价接口"),
    PURCHASE_QUERY("10104", 1, "QUERY", "采购合同执行情况查询接口"),
    SALES_ADD("10201", 2, "ADD", "销售合同新增接口"),
    SALES_UPDATE("10202", 2, "UPDATE", "销售合同更新接口"),
    SALES_PRICE("10203", 2, "PRICE", "销售合同定价接口"),
    SALES_QUERY("10204", 2, "QUERY", "销售合同执行情况查询接口"),
    PRICE_UPDATE("10301", 3, "PRICE_UPDATE", "定价更新"),
    SALES_PRICE_UPDATE("10302", 3, "SALES_PRICE_UPDATE", "结价更新"),

    ;


    private String code;
    private Integer salesType;
    private String actionName;
    private String desc;

    LkgInterfaceEnum(String code, Integer salesType, String actionName, String desc) {
        this.code = code;
        this.salesType = salesType;
        this.actionName = actionName;
        this.desc = desc;
    }

    public static LkgInterfaceEnum getLkgInterfaceEnum(Integer salesType, String actionName) {
        if (null == salesType) {
            salesType = ContractSalesTypeEnum.SALES.getValue();
        }
        if (null == actionName) {
            actionName = LkgInterfaceActionEnum.ADD.name();
        }
        for (LkgInterfaceEnum en : LkgInterfaceEnum.values()) {
            if (en.getSalesType() == salesType && actionName.equals(en.getActionName())) {
                return en;
            }
        }
        if (actionName == PRICE_UPDATE.getActionName()) {
            return PRICE_UPDATE;
        }else if(actionName == SALES_PRICE_UPDATE.getActionName()){
            return SALES_PRICE_UPDATE;
        }
        return SALES_ADD;
    }

    public static String getTransCode(Integer salesType, String actionName) {
        return getLkgInterfaceEnum(salesType, actionName).getCode();
    }

    public static LkgInterfaceEnum getByCode(String code) {
        if (null == code) return SALES_ADD;
        for (LkgInterfaceEnum en : LkgInterfaceEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return SALES_ADD;
    }


    public static String getDescByValue(String code) {
        if (null == code) return "";
        return getByCode(code).getDesc();
    }
}
