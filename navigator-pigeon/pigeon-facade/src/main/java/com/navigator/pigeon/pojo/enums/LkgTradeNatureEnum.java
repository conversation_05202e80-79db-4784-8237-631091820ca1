package com.navigator.pigeon.pojo.enums;

import lombok.Getter;

@Getter
public enum LkgTradeNatureEnum {
    CHINA(1, "内贸"),
    GLOBAL(2, "外贸"),
    EXCHANGE(3, "内贸收储"),

    ;
    /*
        1	内贸
        2	外贸
        3	内贸收储
    */

    private int value;
    private String desc;

    LkgTradeNatureEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LkgTradeNatureEnum getByValue(Integer value) {
        if (null == value) return CHINA;
        for (LkgTradeNatureEnum en : LkgTradeNatureEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return CHINA;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
