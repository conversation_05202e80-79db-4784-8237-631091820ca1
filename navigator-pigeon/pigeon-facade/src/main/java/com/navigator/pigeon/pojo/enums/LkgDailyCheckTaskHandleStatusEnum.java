package com.navigator.pigeon.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LkgDailyCheckTaskHandleStatusEnum {
    INIT(0, "未启动"),
    PULL_FILE(1, "获取文件"),
    RECORD_CONTRACT_DATA(2, "记录合同数据"),
    CHECK_CONTRACT_NAV(3, "基于NAV数据进行校核"),
    CHECK_CONTRACT_LKG(4, "基于LKG数据进行校核"),
    CREATE_CHECK_REPORT(5, "生成校核报告"),
    CHECK_FINISH(9, "校核完成"),

    ;

    private int value;
    private String desc;
}
