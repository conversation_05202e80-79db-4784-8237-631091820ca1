package com.navigator.pigeon.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dbi_lkg_operation_log
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_lkg_operation_log")
@ApiModel(value="DbiLkgOperationLogEntity对象", description="dbi_lkg_operation_log")
public class LkgOperationLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联记录Id")
    private String referId;

    @ApiModelProperty(value = "调用者模块")
    private String requestSystem;

    @ApiModelProperty(value = "操怍类型")
    private Integer operationType;

    @ApiModelProperty(value = "被调用者信息")
    private String targetSystem;

    @ApiModelProperty(value = "被调用者接口")
    private String requestUrl;

    @ApiModelProperty(value = "参数信息")
    private String requestInfo;

    @ApiModelProperty(value = "调用结果反馈信息")
    private String responseInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发起人ID")
    private Integer createdBy;

    @ApiModelProperty(value = "操作人ID")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
