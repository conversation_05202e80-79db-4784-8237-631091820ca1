package com.navigator.pigeon.service.lkg.remote;

import com.navigator.common.util.SHAUtil;
import org.junit.jupiter.api.Test;

//@SpringBootTest
//@RunWith(MockitoJUnitRunner.class)
//@Transactional
//@Rollback
class LkgRemoteServiceTest {

    @Test
    public void testSHA512() {
        System.out.println(String.format("1111111 %s,%s", 1, 2));

        StringBuilder sbData = new StringBuilder();
        //var paramStr = appKey + appSecret + nonce + timeStamp + data + transCode;
        String appKey = "10001";
        String appSecret = "F78A81FC72B1498898ED023C0217B89FEEEEEEEEEEEERRRRRRRRRRRRRRRR44444444444444";
        String nonce = "0b5f2297f2c64f425df8d72f68d787303463";
        String timeStamp = "1651930428802";
        String jData = "{}";
        String transCode = "10201";
        String siteId = "07";

        sbData.append(appKey).append(appSecret).append(nonce).append(timeStamp).append(jData).append(transCode).append(siteId);
        //System.out.print("测试值16----"+sbData.toString());
        String signKey = SHAUtil.SHA512(sbData.toString());

        System.out.println("==========================");
        System.out.println(signKey);

    }

}