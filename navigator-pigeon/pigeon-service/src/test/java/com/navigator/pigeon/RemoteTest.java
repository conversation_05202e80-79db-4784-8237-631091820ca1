package com.navigator.pigeon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.navigator.common.util.SHAUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.http.OkSslUtil;
import com.navigator.pigeon.dao.LkgDailyFileDao;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.LkgInterfaceRequestDTO;
import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;
import com.navigator.pigeon.pojo.enums.LkgDailyFileHandleStatusEnum;
import com.navigator.pigeon.pojo.enums.LkgDailyFileIsGenerate;
import com.navigator.pigeon.pojo.enums.LkgFileSiteIdEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceEnum;
import com.navigator.pigeon.service.lkg.ILkgContractDailyCheckService;
import com.navigator.pigeon.service.lkg.ISyncContractService;
import com.navigator.pigeon.service.lkg.remote.LkgRemoteFacade;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RemoteTest {

    @Value("${lkgFileUrl.url}")
    private String lkgFileUrl;

    @Value("${syncLkgInfo.open}")
    private String lkgOpen;

    @Resource
    LkgRemoteFacade lkgRemoteFacade;

    @Autowired
    AzureBlobUtil azureBlobUtil;

    @Autowired
    LkgDailyFileDao lkgDailyFileDao;

    @Resource
    LkgContractFacade lkgContractFacade;

    @Resource
    ISyncContractService lSyncContractService;

    @Resource
    ILkgContractDailyCheckService iLkgContractDailyCheckService;

    /**
     * 路易达孚系统服务接口test
     */
    @Test
    public void test() {
        String siteId = "07";
        String appKey = "10001";
        String appSecret = "F78A81FC72B1498898ED023C0217B89FEEEEEEEEEEEERRRRRRRRRRRRRRRR44444444444444";
        String nonce = UUID.fastUUID().toString();
        String timeStamp = new Timestamp(System.currentTimeMillis()).getTime() + "";
        String data = "{\"beginDeliveryTime\":1650424307855,\"changeOrderNo\":\"DZWZPBG100001\",\"changesTimes\":0,\"checkTime\":1650424307855,\"checkUser\":\"LYDF\",\"contractNumber\":\"DZWZPMXF900023\",\"contractStrategy\":\"\",\"createTime\":1650424307855,\"createUser\":\"LYDF\",\"deliveryAddress\":\"ZS/NB+ZJG PORT\",\"demander\":\"04\",\"detaillist\":[{\"amountOfRMB\":782000,\"batchNo\":\"\",\"calculateUnit\":\"145\",\"commodityCode\":\"************.046\",\"commodityName\":\"D款夏季短袖防静电工作服上衣 D款夏季短袖防静电工作服，60%棉，40%聚酯纤维，纱支3232，密度13070，防静电纱卡（网格），上衣有口袋有插笔孔，左肩锁骨处有对讲机挂扣，上衣双口袋，无刺绣编码，洗水唛注明生产年份\",\"count\":36503,\"currencyType\":\"RMB\",\"demandNo\":\"ZSPRMR2010001\",\"giftCount\":3,\"hasTaxPrice\":4560.1,\"isShippingChargesPrice\":1,\"noTaxPrice\":4021,\"qualityExplain\":\"\",\"rateOfRMB\":8.3,\"rateOfTax\":12.5,\"remarks\":\"\",\"shippingPrice\":1240.33,\"specifications\":\"\"}],\"effectiveTime\":1650424307855,\"endDeliveryTime\":1650424307855,\"endFixPriceTime\":1650424307855,\"exeOfficer\":\"\",\"fixstatus\":1,\"fromContractNumber\":\"\",\"fromContractSignTime\":1650424307855,\"invoiceType\":1,\"isBasic\":1,\"isDelay\":1,\"isPaymentPurchase\":1,\"isProvideInvoice\":1,\"isSellOfBTB\":0,\"isSellOfOEM\":0,\"modeOfPrice\":1,\"overchargeRate\":30.2,\"paymentExplain\":\"CAD 100%\",\"qualityCheckExplain\":\"FINAL AT LAODING PORT\",\"refContractNumber\":\"S001326.0000\",\"remarks\":\"\",\"signAddress\":\"北京\",\"signTime\":1650424307855,\"signingUnit\":\"无\",\"status\":1,\"supplierCode\":\"1218763\",\"supplierName\":\"路易达孚（天津）国际贸易有限公司\",\"tolAmount\":68400014,\"tradeNature\":1,\"trader\":\"\",\"transactionType\":1}";
        String transCode = LkgInterfaceEnum.PURCHASE_ADD.getCode();

        /**
         * （1）合成参数字符串：paramStr ,例如
         * var paramStr = appKey + appSecret + nonce + timeStamp + data + transCode + siteId;
         * 注：appSecret 是合作方接口对应 appKey的密钥码，与科吉公司联系。
         * （2）sha512加密,合成字符串
         * var appSign = sha512(paramStr);
         *
         */
        String sign = appKey + appSecret + nonce + timeStamp + data + transCode + siteId;
        log.info("测试服务接口组成{}", sign);
        String appSign = SHAUtil.SHA512(sign);
        log.info("测试服务接口签名{}", appSign);

        LkgInterfaceRequestDTO requestDTO = new LkgInterfaceRequestDTO();
        requestDTO.setData(data)
                .setTransCode(transCode)
                .setAppKey(appKey)
                .setAppSign(appSign)
                .setNonce(nonce)
                .setTimeStamp(timeStamp)
                .setSiteId(siteId);
        log.info("测试服务接口参数{}", requestDTO);
        log.info("测试服务接口结果{}", lkgRemoteFacade.syncLkgInfo(requestDTO));
    }

    @Test
    public void test2() {
        ArrayList<String> records = new ArrayList<>();
        String filePath = "fileRecord/export/contract/2022-07-15/";
        String fileName = "20220715_00_sales_detail_113745148.csv";
        String line = "";
        try {
            System.out.println("读取blob文件路径：" + filePath + fileName);
            InputStreamReader isr = azureBlobUtil.csvFileToStream(filePath, fileName);
            if (null != isr) {
                BufferedReader br = new BufferedReader(isr);
                while ((line = br.readLine()) != null) {
                    records.add(line);
                }
            }
            System.out.println("csv表格读取行数：" + records.size());
        } catch (IOException e) {
            log.info("获取lkg文件路径有误{}", filePath + fileName);
        }
        System.out.println(records.size());
        System.out.println(records);
        System.out.println(CollUtil.isNotEmpty(records));
    }

        /*SyncRequestDTO syncRequestDTO = new SyncRequestDTO();
        syncRequestDTO.setTtId(11111)
                .setContractCode("22222")
                .setContractId(33333)
                .setParentContractId(44444)
                .setConfirmPriceId(55555)
                .setIsChangeFactory(0)
                .setTradeType(101)//19种场景编码
                .setSalesType(1)//采销类型 1/2
                .setCustomerCode("123456");
        lkgContractFacade.syncContractRequest(syncRequestDTO);*/

        /*SyncQueryRecordDTO syncQueryRecordDTO = new SyncQueryRecordDTO();
        syncQueryRecordDTO.setSalesType(1)
                .setContractCode("DZWZP200001")
                .setRefContractNumber("%");*/
        /*ArrayList<LkgContractDTO> lkgContractList = lSyncContractService.getLkgContract(syncQueryRecordDTO);
        System.out.print("查询结果-------"+lkgContractList.toString());*/
    //iLkgContractDailyCheckService.dailyCheck();

    @Test
    public void test3() {
        List<String> records = new ArrayList<>();
        try {

            String line = "";
            OkSslUtil.trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier(OkSslUtil.hv);
            URL url = new URL("https://***********:8001/lkg_contract/20220715_29_sales_detail.csv");
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.connect();
            InputStream inputStream = urlConnection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            while ((line = br.readLine()) != null) {
                records.add(line);
            }
            System.out.println("csv表格读取行数：" + records.size());
            urlConnection.disconnect();
            inputStream.close();
            br.close();
        } catch (Exception e) {
            System.out.println(e.toString());
            System.out.println(records);
        }
        System.out.println(records);

    }

    @Test
    public void testF() {
        /**
         * 20220715_07_sales_detail_113745148.csv
         */
        LkgFileSiteIdEnum[] values = LkgFileSiteIdEnum.values();
        for (LkgFileSiteIdEnum value : values) {

        }
        StringBuilder stringBuilder = new StringBuilder("https://ldcblob.blob.core.chinacloudapi.cn/navigator/dev/fileRecord/export/contract/2022-07-15/sales/");
        stringBuilder.append(new SimpleDateFormat("yyyyMMdd").format(new Date()))
                .append("_")
                .append("00")
                .append("_sales_detail_113745148.csv");
        String line = "";
        List<String> records = new ArrayList<>();
        try {
            URL url = new URL(stringBuilder.toString());
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.connect();
            InputStream inputStream = urlConnection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String readData = br.readLine();
            if (StringUtil.isEmpty(readData)) {
                //如果为空我就认为这个文件不存在
            }
            while ((line = br.readLine()) != null) {
                records.add(line);
            }
            System.out.println("csv表格读取行数：" + records.size());
            urlConnection.disconnect();
            inputStream.close();
            br.close();
        } catch (IOException e) {
            System.out.println("文件路径不存在");
            System.out.println(records);
        }
        System.out.println(records);


    }

    @Test
    public void testD() {
        LkgFileSiteIdEnum[] values = LkgFileSiteIdEnum.values();
        String checkDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        for (LkgFileSiteIdEnum value : values) {
            LkgDailyFileEntity dfEntity = new LkgDailyFileEntity();
            String fileName = checkDate + value.getFileCsv();
            String[] sp = fileName.split("_");
            String filePath = lkgFileUrl + checkDate + value.getFileCsv();
            dfEntity.setFilePath(filePath)
                    .setCheckDate(checkDate)
                    .setFileSource("lkg");
            LkgDailyFileEntity fileEntity = lkgDailyFileDao.getDailyFileEntity(dfEntity);
            if (fileEntity == null) {
                LkgDailyFileEntity lkgDailyFileEntity = new LkgDailyFileEntity();
                lkgDailyFileEntity
                        .setFilePath(filePath)
                        .setFileName(fileName)
                        .setFileSource("lkg")
                        .setIsGenerate(LkgDailyFileIsGenerate.UN_GENERATE.getValue())
                        .setSiteId(sp[1])
                        .setSalesType("purchase".equals(sp[2]) ? 1 : 2)
                        .setCheckDate(checkDate)
                        .setCurrentCount(0)
                        .setPullStatus(0)
                        .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue());
                lkgDailyFileDao.save(lkgDailyFileEntity);
            }
        }

    }

    @Test
    public void  test8(){

        File csv = new File("D:\\spdb\\backup\\20220723_29_sales_detail.csv");
        boolean setReadableFlag = csv.setReadable(true);
        boolean setWritableFlag = csv.setWritable(true);
        if(setReadableFlag&&setWritableFlag){

        }
        InputStreamReader isr = null;
        BufferedReader br = null;
        ArrayList<String> records = null;
        try {
            isr = new InputStreamReader(new FileInputStream(csv), "UTF-8");
            br = new BufferedReader(isr);
            String line = "";
            records = new ArrayList<>();
            if(br != null){
                while ((line = br.readLine()) != null) {
                    System.out.println(line);
                    records.add(line);
                }
            }
            System.out.println("csv表格读取行数：" + records.size());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if(isr != null) {
                    isr.close();
                }
                if(br != null)
                    br.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        for (String record : records) {

            try {
                System.out.println(record.split(",")[13]);
            } catch (Exception e) {
                System.out.println("111");
            }
        }

    }
}
