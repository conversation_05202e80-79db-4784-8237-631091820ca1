package com.navigator.pigeon.service.lkg.impl;

import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.pigeon.dao.LkgSyncRequestDao;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.pigeon.service.lkg.ISyncContractService;
import com.navigator.pigeon.utdata.SyncRequestDataMocker;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
//@Transactional
//@Rollback
class SyncContractLogicServiceTest {

    @Resource
    LkgSyncRequestDao lkgSyncRequestDao;

    @Resource
    ISyncContractService syncContractService;

    @Resource
    SyncContractLogicService syncContractLogicService;

    @MockBean
    ContractFacade contractFacade;
    @MockBean
    TradeRemoteService tradeRemoteService;
    @MockBean
    GoodsFacade goodsFacade;

    @BeforeEach
    void setUP() {
        ContractDetailVO contractDetailVO = new ContractDetailVO();
        Result result = Result.success(contractDetailVO);
        Mockito.when(contractFacade.getContractByContractId(Mockito.anyString())).thenReturn(result);
    }

    @Test
    void saveSyncRecordContractDef() {

        List<SyncRequestDTO> list = SyncRequestDataMocker.genSyncRequestDTO();
        for (SyncRequestDTO requestDTO : list) {
            SyncRequestDTO syncRequestDTO = syncContractService.syncContractRequest(requestDTO);
            //syncContractLogicService.saveSyncRecordContractDef(syncRequestDTO);
        }
    }

    @Test
    public void testSyncContractRequest() {

        List<SyncRequestDTO> list = SyncRequestDataMocker.genSyncRequestDTO();
        for (SyncRequestDTO requestDTO : list) {
            syncContractService.syncContractRequest(requestDTO);
        }
    }

    @Test
    public void reSyncContractRequest() {
        syncContractService.reSyncContractRequest(415);
    }

    @Test
    public void testAddContract() {

        ContractDetailVO c1 = SyncRequestDataMocker.genContractDetailVO(100001);
        ContractDetailVO c2 = SyncRequestDataMocker.genContractDetailVO(100002);
        ConfirmPriceDTO cp = SyncRequestDataMocker.genConfirmPriceDTO();
        List<ConfirmPriceDTO> lictCp = SyncRequestDataMocker.genConfirmPriceDTOList();

        GoodsDTO gd = SyncRequestDataMocker.genGoodsDTO();

        Mockito.when(tradeRemoteService.getTradeContractDetail(100001)).thenReturn(c1);
        Mockito.when(tradeRemoteService.getTradeContractDetail(100002)).thenReturn(c2);
        Mockito.when(tradeRemoteService.getConfirmPriceInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(cp);
        Mockito.when(tradeRemoteService.getConfirmPriceList(Mockito.anyInt())).thenReturn(lictCp);
        Mockito.when(goodsFacade.findGoodsDetail(Mockito.anyInt())).thenReturn(gd);

        SyncRequestDTO requestDTO = new SyncRequestDTO();
        requestDTO.setTtId(0)
                .setContractCode("HT200000001")
                .setContractId(100001)
                .setParentContractId(100002)
                .setSalesType(2)
                .setCustomerCode("C000001")
                .setConfirmPriceId(10001)
                .setIsChangeFactory(0)
                .setTradeType(ContractTradeTypeEnum.SPLIT_NORMAL.getValue());
        requestDTO.setSyncType(1);

        syncContractService.syncContractRequest(requestDTO);
    }

    @Test
    public void testUpdatePrice() {

        ContractDetailVO c1 = SyncRequestDataMocker.genContractDetailVO(100001);
        ContractDetailVO c2 = SyncRequestDataMocker.genContractDetailVO(100002);
        ConfirmPriceDTO cp = SyncRequestDataMocker.genConfirmPriceDTO();
        List<ConfirmPriceDTO> lictCp = SyncRequestDataMocker.genConfirmPriceDTOList();

        GoodsDTO gd = SyncRequestDataMocker.genGoodsDTO();

        Mockito.when(tradeRemoteService.getTradeContractDetail(100001)).thenReturn(c1);
        Mockito.when(tradeRemoteService.getTradeContractDetail(100002)).thenReturn(c2);
        Mockito.when(tradeRemoteService.getConfirmPriceInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(cp);
        Mockito.when(tradeRemoteService.getConfirmPriceList(Mockito.anyInt())).thenReturn(lictCp);
        Mockito.when(goodsFacade.findGoodsDetail(Mockito.anyInt())).thenReturn(gd);

        SyncRequestDTO requestDTO = new SyncRequestDTO();
        requestDTO.setTtId(0)
                .setContractCode("HT200000001")
                .setContractId(100001)
                .setParentContractId(100002)
                .setSalesType(2)
                .setCustomerCode("C000001")
                .setConfirmPriceId(10001)
                .setIsChangeFactory(0)
                .setTradeType(ContractTradeTypeEnum.SPLIT_NORMAL.getValue());

        syncContractService.syncContractRequest(requestDTO);
    }

    @Test
    public void testAddPrice() {

        ContractDetailVO c1 = SyncRequestDataMocker.genContractDetailVO(100001);
        ContractDetailVO c2 = SyncRequestDataMocker.genContractDetailVO(100002);
        ConfirmPriceDTO cp = SyncRequestDataMocker.genConfirmPriceDTO();
        GoodsDTO gd = SyncRequestDataMocker.genGoodsDTO();

        Mockito.when(tradeRemoteService.getTradeContractDetail(100001)).thenReturn(c1);
        Mockito.when(tradeRemoteService.getTradeContractDetail(100002)).thenReturn(c2);
        Mockito.when(tradeRemoteService.getConfirmPriceInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(cp);
        Mockito.when(goodsFacade.findGoodsDetail(Mockito.anyInt())).thenReturn(gd);

        SyncRequestDTO requestDTO = new SyncRequestDTO();
        requestDTO.setTtId(0)
                .setContractCode("HT200000001")
                .setContractId(100001)
                .setParentContractId(100002)
                .setSalesType(2)
                .setCustomerCode("C000001")
                .setConfirmPriceId(10001)
                .setIsChangeFactory(0)
                .setTradeType(ContractTradeTypeEnum.PRICE.getValue());

        syncContractService.syncContractRequest(requestDTO);
    }

    @Test
    public void testQuery(){
        LkgContractDTO lkgContractDTO = syncContractService.getLkgContract("TJIBSBMS2204060");
        System.out.println(lkgContractDTO);
    }


}