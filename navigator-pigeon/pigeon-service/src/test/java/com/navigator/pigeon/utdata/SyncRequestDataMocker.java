package com.navigator.pigeon.utdata;

import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.ContractDetailVO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SyncRequestDataMocker {

    public static List<SyncRequestDTO> genSyncRequestDTO() {
        int salesType = 2;
        int isCF = 0;
        List<SyncRequestDTO> list = new ArrayList<>();

        for (ContractTradeTypeEnum en : ContractTradeTypeEnum.values()) {
            if (en.getValue() == 0) continue;
            list.add(genSyncRequestDTO(salesType, en.getValue(), isCF));
        }

        return list;

    }


    public static SyncRequestDTO genSyncRequestDTO(int salesType, int tradeType, int isCF) {
        SyncRequestDTO syncRequestDTO = new SyncRequestDTO();
        syncRequestDTO.setTtId(1001)
                .setContractCode("HT202202220001")
                .setContractId(20001)
                .setParentContractId(20000)
                .setSalesType(salesType)
                .setTradeType(tradeType)
                .setCustomerCode("YU695")
                .setConfirmPriceId(300001)
                .setIsChangeFactory(isCF);
        return syncRequestDTO;
    }

    public static ContractDetailVO genContractDetailVO(Integer contractId) {
        if (contractId == 100002) {
            ContractDetailVO contractDetailVO = new ContractDetailVO();
            contractDetailVO.setGoodsId(21001)
                    .setLinkinageCode("LCT30202020")
                    .setContractNum(BigDecimal.valueOf(1000))
                    .setContractType(ContractTypeEnum.JI_CHA.getValue())
                    .setDomainCode("2301")
                    .setTradeType(ContractTradeTypeEnum.NEW.getValue())
                    .setPaymentType(0)
                    .setDeliveryType(1)
                    .setDeliveryEndTime(new Date())
                    .setSignDate(new Date())
                    .setCustomerName("上海跳跳糖")
                    .setCustomerCode("SHTTT")
                    .setDeliveryStartTime(new Date())
                    .setTagConfigIds("")
                    .setSupplierName("LDCCHINA")
                    //.setDestination("TJ")
                    .setDepositRate(2)
                    .setNeedPackageWeight(1)
                    .setIsArrangeTransport(1)
                    .setDepositAmount(BigDecimal.valueOf(100000))
                    .setPriceEndTime("2022-01-01")
                    .setId(contractId);
            contractDetailVO.setInvoiceType(1);
            contractDetailVO.setShipWarehouseName("LDC");
            contractDetailVO.setPricedNum(BigDecimal.ZERO);
            contractDetailVO.setNotPricedNum(BigDecimal.valueOf(1000));
            return contractDetailVO;
        } else if (contractId == 100001) {
            ContractDetailVO contractDetailVO = new ContractDetailVO();
            contractDetailVO.setGoodsId(21001)
                    .setLinkinageCode("LCT30202020")
                    .setContractNum(BigDecimal.valueOf(100))
                    .setContractType(ContractTypeEnum.YI_KOU_JIA.getValue())
                    .setDomainCode("2301")
                    .setTradeType(ContractTradeTypeEnum.SPLIT_NORMAL.getValue())
                    .setPaymentType(0)
                    .setDeliveryType(1)
                    .setDeliveryEndTime(new Date())
                    .setSignDate(new Date())
                    .setCustomerName("上海跳跳糖")
                    .setCustomerCode("SHTTT")
                    .setDeliveryStartTime(new Date())
                    .setTagConfigIds("")
                    .setSupplierName("LDCCHINA")
                    //.setDestination("TJ")
                    .setDepositRate(2)
                    .setNeedPackageWeight(1)
                    .setIsArrangeTransport(1)
                    .setDepositAmount(BigDecimal.valueOf(100000))
                    .setPriceEndTime("2022-01-01")
                    .setId(contractId);
            contractDetailVO.setInvoiceType(1);
            contractDetailVO.setShipWarehouseName("LDC");
            contractDetailVO.setPricedNum(BigDecimal.ZERO);
            contractDetailVO.setNotPricedNum(BigDecimal.valueOf(1000));
            return contractDetailVO;
        }
        return null;
    }

    public static ConfirmPriceDTO genConfirmPriceDTO() {
        ConfirmPriceDTO confirmPriceDTO = new ConfirmPriceDTO();
        confirmPriceDTO.setId(300001)
                .setCreatedAt(new Date())
                .setCustomerCode("NCC300001")
                .setCustomerName("上海甜头菜")
                .setTransactionPrice(BigDecimal.valueOf(3390))
                .setDiffPrice(BigDecimal.valueOf(50))
                .setNum(BigDecimal.valueOf(100))
                .setPrice(BigDecimal.valueOf(3440))
                .setContractId(100001);
        return confirmPriceDTO;
    }

    public static GoodsDTO genGoodsDTO() {
        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setId(1001)
                .setLinkageGoodsCode("LC20001")
                .setCode("NC20001")
                .setName("豆粕43%,50KG")
                .setSpecName("43%");
        return goodsDTO;
    }

    public static List<ConfirmPriceDTO> genConfirmPriceDTOList() {
        List<ConfirmPriceDTO> list = new ArrayList<>();
        ConfirmPriceDTO cp1 = genConfirmPriceDTO();
        cp1.setId(3000011);
        list.add(cp1);

        ConfirmPriceDTO cp2 = genConfirmPriceDTO();
        cp1.setId(3000012);
        list.add(cp2);

        ConfirmPriceDTO cp3 = genConfirmPriceDTO();
        cp1.setId(3000013);
        list.add(cp3);

        return list;
    }
}
