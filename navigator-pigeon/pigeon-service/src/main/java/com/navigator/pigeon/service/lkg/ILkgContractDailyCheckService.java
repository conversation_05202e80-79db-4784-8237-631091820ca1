package com.navigator.pigeon.service.lkg;

import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ILkgContractDailyCheckService {


    /**
     * 1、创建每日应处理的任务记录
     * 2、获取文件，并更新
     * 3、对每个获取的文件，落地数据
     * 4、对每条数据进行处理，先以nav为主，再以lkg为主
     * 5、发送每日校核报告
     */

    void dailyCheck();


    /**
     * 改变文件状态(未启动到获取文件)
     *
     * @param
     * @return
     */
    void changeToPull();

    /**
     * 改变文件状态(获取文件到合同数据)
     *
     * @param
     * @return
     */
    void changeToRecord();


    /**
     * 重试机制
     *
     * @param
     * @return
     */
    void resync();

    void createDailyCheckTaskRecord(String checkDate);

    void pullDailyFile(String checkDate);

    void createDailyContractRecord(String checkDate);

    void checkContractByNav(String checkDate);

    void checkContractByLkg(String checkDate);

    void sendDailyCheckReport(String checkDate);

    boolean saveDailyFile(LkgDailyFileEntity lkgDailyFileEntity);

    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 Start
    List<String> closeDailyContract(String checkDate, MultipartFile file);
    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 End
}
