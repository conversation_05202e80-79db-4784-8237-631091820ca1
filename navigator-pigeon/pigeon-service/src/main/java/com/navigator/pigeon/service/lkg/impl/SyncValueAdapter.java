package com.navigator.pigeon.service.lkg.impl;

import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.pigeon.pojo.enums.LkgContractProcessTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.pigeon.pojo.enums.LkgPriceFixStatusEnum;
import com.navigator.pigeon.pojo.enums.LkgTransactionTypeEnum;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SyncValueAdapter {

    public LkgPriceFixStatusEnum lkgPriceFixStatusEnum;
    public LkgInterfaceActionEnum lkgInterfaceActionEnum;
    public LkgTransactionTypeEnum lkgTransactionTypeEnum;
    public LkgContractProcessTypeEnum lkgContractProcessTypeEnum_request;   //整体处理方式
    public LkgContractProcessTypeEnum lkgContractProcessTypeEnum_original;  //原合同处理方式
    public LkgContractProcessTypeEnum lkgContractProcessTypeEnum_new;       //新合同处理方式


    public List<LkgContractProcessTypeEnum> initAdapter(SyncRequestDTO syncRequestDTO) {
        List<LkgContractProcessTypeEnum> listProcessor = new ArrayList<>();
        listProcessor.add(LkgContractProcessTypeEnum.UPDATE);

        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(syncRequestDTO.getTradeType());
        boolean isFactoryChanged = syncRequestDTO.getIsChangeFactory() > 0;

        switch (contractTradeTypeEnum) {
            case UNKNOWN:
                break;
            case NEW:
                //新增
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.ADD;
                lkgContractProcessTypeEnum_original = null;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;

                listProcessor = new ArrayList<>();
                listProcessor.add(LkgContractProcessTypeEnum.ADD);

                break;
            case REVISE_NORMAL:
                //交货工厂变化，废除原合同，新增新合同
                if (isFactoryChanged) {
                    lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                    lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                    lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;

                    listProcessor = new ArrayList<>();
                    listProcessor.add(LkgContractProcessTypeEnum.DELETE);
                    listProcessor.add(LkgContractProcessTypeEnum.ADD);

                } else {
                    //交货工厂不变，更新原合同
                    lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE;
                    lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                    lkgContractProcessTypeEnum_new = null;
                }
                break;
            case REVISE_CHANGE_CUSTOMER:
                //LKG不能变化客户主体，需要废除原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;
                break;
            case SPLIT_NORMAL:
                //更新原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                } else {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                }
                //TODO NEO  基差拆出来一口价的另外处理
                break;
            case SPLIT_CHANGE_CUSTOMER:
                //更新原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                } else {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                }
                //TODO NEO  基差拆出来一口价的另外处理
                break;
            case PRICE:
                //定价
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_new = null;
                break;
            case FIXED:
                //定价
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_new = null;
                break;
            case PRICE_RESULT:
                //定价
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.PRICE;
                lkgContractProcessTypeEnum_new = null;
                break;
            case TRANSFER_PART:
                //更新原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE_ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                break;
            case TRANSFER_ALL:
                //废除原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case TRANSFER_RESULT:
                //更新原合同，新增新合同（其实要分是部分还是全部，一期不涉及）
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case REVERSE_PRICE_PART:
                //更新原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.UPDATE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case REVERSE_PRICE_ALL:
                //废除原合同，新增新合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case STRUCTURE_PRICE:
                break;
            case BUYBACK:
                //废除原（采购）合同，新增新（销售）合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD_CLOSE;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.BUYBACK;
                break;
            case WASHOUT:
                //废除原合同，新增新合同(状态为已完成）,直接废除？
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = null;
                break;
            case PUT_BACK:
                //废除原（销售）合同，新增新（采购）合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE_ADD;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_new = LkgContractProcessTypeEnum.ADD_CLOSE;
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.RESALE;
                break;
            case CLOSED:
                //废除原合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = null;
                break;
            case INVALID:
                //废除原合同
                lkgContractProcessTypeEnum_request = LkgContractProcessTypeEnum.DELETE;
                lkgContractProcessTypeEnum_original = LkgContractProcessTypeEnum.UPDATE;
                lkgContractProcessTypeEnum_new = null;
                break;
        }

        return listProcessor;
    }

    public LkgTransactionTypeEnum getTransactionType(Integer tradeType, boolean isFactoryChanged) {

        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeType);

        switch (contractTradeTypeEnum) {
            case UNKNOWN:
                break;
            case NEW:
                //新增
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;
                break;
            case REVISE_NORMAL:
                //交货工厂变化，废除原合同，新增新合同
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                }
                break;
            case REVISE_CHANGE_CUSTOMER:
                //LKG不能变化客户主体，需要废除原合同，新增新合同
                //lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                }else{
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                }
                break;
            case SPLIT_NORMAL:
                //更新原合同，新增新合同
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                } else {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                }
                break;
            case SPLIT_CHANGE_CUSTOMER:
                //更新原合同，新增新合同
                if (isFactoryChanged) {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.TRANSFER;
                } else {
                    lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                }
                break;
            case PRICE:
                //定价
                break;
            case FIXED:
                //定价
                break;
            case PRICE_RESULT:
                //定价
                break;
            case TRANSFER_PART:
                //更新原合同，新增新合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case TRANSFER_ALL:
                //废除原合同，新增新合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case TRANSFER_RESULT:
                //更新原合同，新增新合同（其实要分是部分还是全部，一期不涉及）
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case REVERSE_PRICE_PART:
                //更新原合同，新增新合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case REVERSE_PRICE_ALL:
                //废除原合同，新增新合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.SPLIT;
                break;
            case STRUCTURE_PRICE:
                break;
            case BUYBACK:
                //废除原（采购）合同，新增新（销售）合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;
                break;
            case WASHOUT:
                //废除原合同，新增新合同(状态为已完成）,直接废除？
                break;
            case PUT_BACK:
                //废除原（销售）合同，新增新（采购）合同
                lkgTransactionTypeEnum = LkgTransactionTypeEnum.RESALE;
                break;
            case CLOSED:
                //废除原合同
                break;
            case INVALID:
                //废除原合同
                break;
        }

        return lkgTransactionTypeEnum == null ? LkgTransactionTypeEnum.DEFAULT : lkgTransactionTypeEnum;
    }
}
