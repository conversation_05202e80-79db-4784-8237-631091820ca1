package com.navigator.pigeon.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.LkgSalesContractMapper;
import com.navigator.pigeon.pojo.entity.LkgSalesContractEntity;

import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/7/5
 * @Time: 16:56
 * @Desception:
 */
@Dao
public class LkgSalesContractDao extends BaseDaoImpl<LkgSalesContractMapper, LkgSalesContractEntity> {


    /**
     * 获取未被同步的销售合同数据
     * @param lkgSalesContractEntity
     * @return
     */
    public List<LkgSalesContractEntity> queryList(LkgSalesContractEntity lkgSalesContractEntity){
        LambdaQueryWrapper<LkgSalesContractEntity> wrapper = new LambdaQueryWrapper<LkgSalesContractEntity>()
                .eq(LkgSalesContractEntity::getIsSynced,lkgSalesContractEntity.getIsSynced());
        return this.baseMapper.selectList(wrapper);
    }
}
