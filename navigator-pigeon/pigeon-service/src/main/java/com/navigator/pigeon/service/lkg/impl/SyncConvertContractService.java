package com.navigator.pigeon.service.lkg.impl;

import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.pigeon.constant.LkgBizConstant;
import com.navigator.pigeon.pojo.dto.*;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.enums.*;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Objects;

import static com.navigator.pigeon.constant.LkgBizConstant.*;

/**
 * <AUTHOR> @date
 */
@Service
@Slf4j
public class SyncConvertContractService {

    @Resource
    TradeRemoteService tradeRemoteService;

    @Resource
    SyncValueAdapter syncValueAdapter;

    public LkgPurchaseContractDefDTO convertPurchaseContractDefDTO(ContractDetailVO contractDetailVO, LkgSyncRecordEntity lkgSyncRecordEntity) {
        //将Navigator合同信息转换为LKG的采购合同
        LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = null;
        try {

            //商品信息
            GoodsDTO goodsDTO = tradeRemoteService.getGoodsDTO(contractDetailVO.getGoodsId());
            GoodsCategoryEnum goodsCategoryEnum = GoodsCategoryEnum.getByValue(goodsDTO.getCategoryId());

            //供应商
            CustomerDTO supplierDTO = tradeRemoteService.getCustomerDTO(contractDetailVO.getSupplierId());

            //TT详情
            TTAddEntity ttAddEntity = tradeRemoteService.getTtAddByTtId(contractDetailVO.getTtId());

            // 操作人
            EmployEntity employEntity = tradeRemoteService.getEmployEntity(ttAddEntity.getCreatedBy());
            // 所属商务
            EmployEntity traderEntity = tradeRemoteService.getEmployEntity(contractDetailVO.getOwnerId());

            String operator = "";
            if (null != employEntity) {
                operator = employEntity.getNickName();
            }
            // 反点价合同的制单人和签发人在调用LKG接口时就用原合同商务
            if (lkgSyncRecordEntity.getTradeType() == ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue()
                    || lkgSyncRecordEntity.getTradeType() == ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue()) {
                operator = traderEntity.getNickName();
            }

            //合同价格详情
            ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceEntity(contractDetailVO.getContractId());

            // Nav-交货方式
            DeliveryTypeEntity deliveryTypeEntity = tradeRemoteService.getDeliveryTypeEntity(contractDetailVO.getDeliveryType());

            // 稅率
            BigDecimal taxRate = contractDetailVO.getTaxRate().multiply(new BigDecimal(100));

            lkgPurchaseContractDefDTO = new LkgPurchaseContractDefDTO();
            //采购合同 详情
            lkgPurchaseContractDefDTO.setStatus(LkgContractStatusEnum.EFFECTIVE.getValue())
                    .setFixstatus(LkgPriceFixStatusEnum.getByContractNum(contractDetailVO.getContractType(), contractDetailVO.getContractNum(), contractDetailVO.getTotalPriceNum()).getValue())
                    .setChangesTimes(0)
                    .setSigningUnit("")
                    .setContractNumber(contractDetailVO.getLinkinageCode())
                    .setRefContractNumber(contractDetailVO.getCustomerContractCode()) //客户方合同编号
                    .setSignTime(contractDetailVO.getSignDate())
                    .setSignAddress(contractDetailVO.getSignPlace())
                    .setTradeNature(LkgTradeNatureEnum.GLOBAL.getValue())
                    .setModeOfPrice(LkgPriceModeEnum.getByContractType(contractDetailVO.getContractType()).getValue())
                    .setIsBasic(convertContractType(contractDetailVO.getContractType()).getIsBasic())
                    .setIsDelay(convertContractType(contractDetailVO.getContractType()).getIsDelay())
                    .setIsSellOfBTB(0)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 start
                    //.setDemander(contractDetailVO.getCompanyName().equals("FL") ? DEFAULT_FL : DEFAULT_LDC)
                    .setDemander(Objects.equals(contractDetailVO.getCompanyId(), DEFAULT_FL_ID) ? DEFAULT_FL : DEFAULT_LDC)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 end
                    .setExeOfficer(DEFAULT_EXE_OFFICER)
                    .setTrader(null == traderEntity ? DEFAULT_TRADER : traderEntity.getNickName())
                    .setSupplierCode(supplierDTO.getLinkageCustomerCode())
                    .setSupplierName(supplierDTO.getName())
                    .setIsSellOfOEM(contractDetailVO.getOem().toString())
                    .setEffectiveTime(contractDetailVO.getSignDate())
                    .setTolAmount(contractDetailVO.getTotalAmount() == null ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setInvoiceType(tradeRemoteService.getInvoiceType(contractDetailVO.getGoodsCategoryId(), contractDetailVO.getInvoiceType(), taxRate.stripTrailingZeros().toPlainString()))
                    .setIsProvideInvoice(IS_PROVIDE_INVOICE)
                    .setContractStrategy("")
                    .setOverchargeRate(new BigDecimal(ttAddEntity.getWeightTolerance() == null ? contractDetailVO.getWeightTolerance() : ttAddEntity.getWeightTolerance()))
                    .setTransactionType(syncValueAdapter.getTransactionType(lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory() == 1 ? true : false).getCode())
                    .setFromContractNumber(getfromContractNumber(lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory()))
                    .setFromContractSignTime(null)
                    .setChangeOrderNo("")
                    .setDeliveryAddress(getDeliveryAddress(contractDetailVO.getShipWarehouseAddress(), contractDetailVO.getSalesType()))
                    .setBeginDeliveryTime(contractDetailVO.getDeliveryStartTime())
                    .setEndDeliveryTime(contractDetailVO.getDeliveryEndTime())
                    .setQualityCheckExplain("详见航海家合同文本")
                    .setPaymentExplain(getPaymentCondition(contractDetailVO, ttAddEntity.getDepositRate(), ttAddEntity.getAddedDepositRate(), ttAddEntity.getCreditDays()))
                    .setIsPaymentPurchase(contractDetailVO.getPaymentType() == PaymentTypeEnum.IMPREST.getType() ? 1 : 0)
                    .setCheckUser(operator)
                    .setCreateUser(operator)
                    .setCreateTime(contractDetailVO.getCreatedAt())
                    .setCheckTime(contractDetailVO.getUpdatedAt())
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setTotalCount(contractDetailVO.getContractNum())
                    .setTranssequence(1)    //采购传1
                    .setPriceOfFOB(contractDetailVO.getFobUnitPrice())
                    .setPayConditionCode(StringUtils.isBlank(contractDetailVO.getPayConditionCode()) ? PAY_CONDITION_CODE : contractDetailVO.getPayConditionCode())
                    .setModeOfDelivery(null == deliveryTypeEntity ? LkgBizConstant.DEFAULT_DELIVERY_TYPE : Integer.parseInt(deliveryTypeEntity.getLkgCode()))
                    .setCommodityClass(goodsCategoryEnum.getLkgCommodityClass());

            if (null == contractDetailVO.getPriceEndType() || contractDetailVO.getPriceEndType() == ContractPriceEndTypeEnum.TEXT.getValue()) {
                lkgPurchaseContractDefDTO.setEndFixPriceTime("");
                //lkgPurchaseContractDefDTO.setRemarks(lkgPurchaseContractDefDTO.getRemarks() + " 截止定价日期：" + contractDetailVO.getPriceEndTime());
            } else {
                if (null == contractDetailVO.getPriceEndTime() || "".equals(contractDetailVO.getPriceEndTime().trim())) {
                    contractDetailVO.setPriceEndTime("");
                }

                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 Start
                String priceEndTime = contractDetailVO.getPriceEndTime();
                // 如果日期格式为yyyy/MM/dd，则转换为yyyy-MM-dd格式
                if (priceEndTime.matches("\\d{4}/\\d{2}/\\d{2}")) {
                    priceEndTime = priceEndTime.replace("/", "-");
                }
                lkgPurchaseContractDefDTO.setEndFixPriceTime(priceEndTime);
                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 End
            }

            //采购商品-价格信息详情
            ArrayList<LkgPurchaseContractDetailDTO> DataDTOList = new ArrayList();
            LkgPurchaseContractDetailDTO lkgPurchaseContractDetailDTO = new LkgPurchaseContractDetailDTO();
            lkgPurchaseContractDetailDTO.setCommodityCode(goodsDTO.getLinkageGoodsCode())
                    .setCommodityName(getCommodityName(goodsDTO))
                    .setSpecifications(getSpecifications(goodsDTO))
                    .setDemandNo("")
                    .setCount(contractDetailVO.getContractNum())
                    .setCalculateUnit(UnitEnum.getByName(contractDetailVO.getWeightUnit()).getLkgCode())
                    .setHasTaxPrice(contractDetailVO.getUnitPrice())
                    .setRateOfTax(taxRate)
                    .setNoTaxPrice(contractDetailVO.getCifUnitPrice())
                    //.setCurrencyType(contractDetailVO.getCurrencyType())
                    .setCurrencyType(CurrencyTypeEnum.RMB.getDesc())
                    .setFuturesContractNo(getFuturesContractNo(contractDetailVO.getDomainCode(), goodsDTO))
                    .setFuturesPrice(contractPriceEntity.getForwardPrice())
                    .setPremiumPrice(getPremiumPrice(contractDetailVO, contractPriceEntity))//含税单价-期货价格 contrucprice
                    .setRateOfRMB(BigDecimal.ONE)
                    .setAmountOfRMB(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setGiftCount(BigDecimal.ZERO)
                    .setShippingPrice(calcShippingPrice(contractPriceEntity))
                    .setQualityExplain("")
                    .setBatchNo("")
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setPriceOfFOB(contractDetailVO.getFobUnitPrice())
                    .setCommodityClass(goodsCategoryEnum.getLkgCommodityClass())
                    .setIsShippingChargesPrice(1);
            DataDTOList.add(lkgPurchaseContractDetailDTO);

            lkgPurchaseContractDefDTO.setDetaillist(DataDTOList);

        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return lkgPurchaseContractDefDTO;
    }

    private String getDeliveryAddress(String shipWarehouseAddress, Integer salesType) {
        // 截取的字符段长度：采购100 销售250
        int splitLength = salesType == ContractSalesTypeEnum.PURCHASE.getValue() ? 100 : 250;

        if (null != shipWarehouseAddress) {
            shipWarehouseAddress = shipWarehouseAddress.replace("\\n", "");
            shipWarehouseAddress = shipWarehouseAddress.replace("\n", "");
            int valueLength = 0;
            for (int i = 0; i < shipWarehouseAddress.length(); i++) {
                String temp = shipWarehouseAddress.substring(i, i + 1);
                if (temp.matches("[\\u0391-\\uFFE5]")) {
                    valueLength += 2;
                } else {
                    valueLength += 1;
                }
                if (valueLength >= splitLength) {
                    shipWarehouseAddress = shipWarehouseAddress.substring(0, i);
                    return shipWarehouseAddress;
                }
            }
        }
        return shipWarehouseAddress;
    }

    public LkgPurchaseContractModifyDefDTO convertPurchaseContractModifyDTO(ContractDetailVO contractDetailVO, LkgSyncRecordEntity lkgSyncRecordEntity) {
        //将Navigator合同信息转换为LKG的销售合同
        LkgPurchaseContractModifyDefDTO lkgPurchaseContractModifyDefDTO = null;
        try {

            //商品信息
            GoodsDTO goodsDTO = tradeRemoteService.getGoodsDTO(contractDetailVO.getGoodsId());

            //TT详情
            TTModifyEntity ttModifyEntity = tradeRemoteService.getTtModifyByTtId(lkgSyncRecordEntity.getTtId());

            // 操作人
            EmployEntity employEntity = tradeRemoteService.getEmployEntity(ttModifyEntity.getCreatedBy());
            // 所属商务
            EmployEntity traderEntity = tradeRemoteService.getEmployEntity(contractDetailVO.getOwnerId());

            //合同价格详情
            ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceEntity(contractDetailVO.getContractId());

            //供应商
            CustomerDTO supplier = tradeRemoteService.getCustomerById(contractDetailVO.getSupplierId());
            //采购商，即LDC所属主体
            // CustomerDTO demander = tradeRemoteService.getCustomerById(contractDetailVO.getBelongCustomerId());

            // Nav-交货方式
            DeliveryTypeEntity deliveryTypeEntity = tradeRemoteService.getDeliveryTypeEntity(contractDetailVO.getDeliveryType());

            // 稅率
            BigDecimal taxRate = contractDetailVO.getTaxRate().multiply(new BigDecimal(100));

            lkgPurchaseContractModifyDefDTO = new LkgPurchaseContractModifyDefDTO();
            lkgPurchaseContractModifyDefDTO
                    .setChangeOrderNo(ttModifyEntity.getTtId() != null ? ttModifyEntity.getTtId().toString() : "")
                    .setContractNumber(contractDetailVO.getLinkinageCode())
                    .setRefContractNumber(contractDetailVO.getCustomerContractCode())
                    .setSignTime(contractDetailVO.getSignDate())
                    .setSignAddress(contractDetailVO.getSignPlace())
                    .setTradeNature(LkgTradeNatureEnum.GLOBAL.getValue())
                    .setChangeTime(contractDetailVO.getUpdatedAt())
                    .setInvoiceType(tradeRemoteService.getInvoiceType(contractDetailVO.getGoodsCategoryId(), contractDetailVO.getInvoiceType(), taxRate.stripTrailingZeros().toPlainString()))
                    .setIsProvideInvoice(1)
                    .setSupplierCode(supplier.getLinkageCustomerCode())
                    .setSupplierName(supplier.getName())
                    .setOverchargeRate(new BigDecimal(ttModifyEntity.getWeightTolerance() == null ? contractDetailVO.getWeightTolerance() : ttModifyEntity.getWeightTolerance()))
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 start
                    //.setDemander(contractDetailVO.getCompanyName().equals("FL") ? DEFAULT_FL : DEFAULT_LDC)
                    .setDemander(Objects.equals(contractDetailVO.getCompanyId(), DEFAULT_FL_ID) ? DEFAULT_FL : DEFAULT_LDC)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 end
                    .setExeOfficer(DEFAULT_EXE_OFFICER)
                    .setTrader(null == traderEntity ? DEFAULT_TRADER : traderEntity.getNickName())
                    .setChangeReason(StringUtils.isBlank(ttModifyEntity.getMemo()) ? "" : ttModifyEntity.getMemo())
                    .setTransactionType("")
//                    .setFromContractNumber(contractDetailVO.getContractCode())
//                    .setFromContractSignTime(DateTimeUtil.formatDateString(contractDetailVO.getSignDate()))
                    .setDeliveryAddress(getDeliveryAddress(contractDetailVO.getShipWarehouseAddress(), contractDetailVO.getSalesType()))
                    .setBeginDeliveryTime(contractDetailVO.getDeliveryStartTime())
                    .setEndDeliveryTime(contractDetailVO.getDeliveryEndTime())
                    .setQualityCheckExplain(QUALITY_CHECK_INFO)
                    .setPaymentExplain(getPaymentCondition(contractDetailVO, ttModifyEntity.getDepositRate(), ttModifyEntity.getAddedDepositRate(), ttModifyEntity.getCreditDays()))
                    .setIsPaymentPurchase(contractDetailVO.getPaymentType() == PaymentTypeEnum.IMPREST.getType() ? 1 : 0)
                    .setCreateUser(employEntity != null ? employEntity.getNickName() : "")
                    .setCreateTime(ttModifyEntity.getCreatedAt())
                    .setCheckUser(employEntity != null ? employEntity.getNickName() : "")
                    .setCheckTime(contractDetailVO.getUpdatedAt())//签发日期
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    // BUGFIX：case-1002949 NAV合同关闭后，LKG合同状态没有更新 Author: Mr 2025-02-19 start
                    //.setStatus(LkgContractStatusEnum.TAKING.getValue())//采购定价单状态 字典
                    .setStatus(Objects.equals(ttModifyEntity.getType(), TTTypeEnum.CLOSED.getType())
                            ? LkgContractStatusEnum.CLOSED.getValue()
                            : LkgContractStatusEnum.TAKING.getValue())
                    // BUGFIX：case-1002949 NAV合同关闭后，LKG合同状态没有更新 Author: Mr 2025-02-19 end
                    .setPayConditionCode(contractDetailVO.getPayConditionCode())
                    .setModeOfDelivery(null == deliveryTypeEntity ? LkgBizConstant.DEFAULT_DELIVERY_TYPE : Integer.parseInt(deliveryTypeEntity.getLkgCode()))
                    .setIsBasic(convertContractType(contractDetailVO.getContractType()).getIsBasic())
                    .setIsDelay(convertContractType(contractDetailVO.getContractType()).getIsDelay())
                    .setFixStatus(LkgPriceFixStatusEnum.getByContractNum(contractDetailVO.getContractType(), contractDetailVO.getContractNum(), contractDetailVO.getTotalPriceNum()).getValue())
                    .setModeOfPrice(LkgPriceModeEnum.getByContractType(contractDetailVO.getContractType()).getValue())
            ;

            /*if (contractDetailVO.getStatus() == ContractStatusEnum.INVALID.getValue()) {
                lkgPurchaseContractModifyDefDTO.setStatus(LkgContractStatusEnum.INVALID.getValue());
            } else if (contractDetailVO.getStatus() == ContractStatusEnum.CLOSED.getValue()) {
                lkgPurchaseContractModifyDefDTO.setStatus(LkgContractStatusEnum.CLOSED.getValue());
            }*/

            LkgPurchaseContractModifyDetailDTO lkgPurchaseContractModifyDetailDTO = new LkgPurchaseContractModifyDetailDTO();
            ArrayList<LkgPurchaseContractModifyDetailDTO> DataDTOList = new ArrayList();
            lkgPurchaseContractModifyDetailDTO
                    .setCommodityCode(goodsDTO.getLinkageGoodsCode())
                    .setCommodityName(getCommodityName(goodsDTO))
                    .setSpecifications(getSpecifications(goodsDTO))
                    .setCount(getCount(contractDetailVO, lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory() == 1 ? true : false))
                    .setPrice(contractDetailVO.getUnitPrice())
                    .setPriceOfFOB(contractDetailVO.getFobUnitPrice())
                    .setCalculateUnit(UnitEnum.getByName(contractDetailVO.getWeightUnit()).getLkgCode())
                    .setFinalAmount(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    //.setCurrencyType(contractDetailVO.getCurrencyType())
                    .setCurrencyType(CurrencyTypeEnum.RMB.getDesc())
                    .setFuturesContractNo(getFuturesContractNo(contractDetailVO.getDomainCode(), goodsDTO))
                    .setFuturesPrice(contractPriceEntity.getForwardPrice())
                    .setPremiumPrice(getPremiumPrice(contractDetailVO, contractPriceEntity))
                    .setShippingPrice(calcShippingPrice(contractPriceEntity))
                    .setRateOfLoss(BigDecimal.ZERO)
                    .setRateOfRMB(BigDecimal.ONE)
                    .setAmountOfRMB(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setQualityExplain("")
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setIsShippingChargesPrice(1)
                    .setUnitWeight(getUnitWeight(goodsDTO))
                    .setHasTaxPrice(contractDetailVO.getUnitPrice())
                    .setNoTaxPrice(contractDetailVO.getCifUnitPrice())
                    .setRateOfTax(taxRate)
                    .setContractNumber(contractDetailVO.getLinkinageCode())
            ;
            DataDTOList.add(lkgPurchaseContractModifyDetailDTO);

            lkgPurchaseContractModifyDefDTO.setDetaillist(DataDTOList);


        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return lkgPurchaseContractModifyDefDTO;
    }

    public LkgSalesContractDefDTO convertSalesContractDefDTO(ContractDetailVO contractDetailVO, LkgSyncRecordEntity lkgSyncRecordEntity) {
        log.info("============contractDetailVO=============={}", contractDetailVO);
        //将Navigator合同信息转换为LKG的销售合同
        LkgSalesContractDefDTO lkgSalesContractDefDTO = null;
        try {

            //商品信息
            GoodsDTO goodsDTO = tradeRemoteService.getGoodsDTO(contractDetailVO.getGoodsId());
            GoodsCategoryEnum goodsCategoryEnum = GoodsCategoryEnum.getByValue(goodsDTO.getCategoryId());

            //TT详情
            TTAddEntity ttAddEntity = tradeRemoteService.getTtAddByTtId(contractDetailVO.getTtId());

            // 操作人
            EmployEntity employEntity = tradeRemoteService.getEmployEntity(ttAddEntity.getCreatedBy());
            // 所属商务
            EmployEntity traderEntity = tradeRemoteService.getEmployEntity(contractDetailVO.getOwnerId());

            String operator = "";
            if (null != employEntity) {
                operator = employEntity.getNickName();
            }
            // 反点价合同的制单人和签发人在调用LKG接口时就用原合同商务
            if (lkgSyncRecordEntity.getTradeType() == ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue()
                    || lkgSyncRecordEntity.getTradeType() == ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue()) {
                operator = traderEntity.getNickName();
            }

            //合同价格信息
            ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceEntity(contractDetailVO.getContractId());

            // 客户详情
            CustomerProtocolEntity customerProtocolEntity = tradeRemoteService.getCustomerDetailEntity(contractDetailVO.getCustomerId(),
                    contractDetailVO.getGoodsCategoryId(),
                    contractDetailVO.getCategory2(),
                    contractDetailVO.getCategory3(),
                    contractDetailVO.getCompanyId(),
                    contractDetailVO.getSalesType());

            // Nav-交货方式
            DeliveryTypeEntity deliveryTypeEntity = tradeRemoteService.getDeliveryTypeEntity(contractDetailVO.getDeliveryType());

            // 特殊处理字段
            int weightCheck = Integer.parseInt(ttAddEntity.getWeightCheck() == null ? contractDetailVO.getWeightCheck() : ttAddEntity.getWeightCheck());
            int weightTolerance = ttAddEntity.getWeightTolerance() == null ? contractDetailVO.getWeightTolerance() : ttAddEntity.getWeightTolerance();

            // 稅率
            BigDecimal taxRate = contractDetailVO.getTaxRate().multiply(new BigDecimal(100));

            lkgSalesContractDefDTO = new LkgSalesContractDefDTO();
            lkgSalesContractDefDTO.setDepositRate(new BigDecimal(contractDetailVO.getDepositRate()))
                    .setChangesTimes(0)
                    .setCustomerName(contractDetailVO.getCustomerName())
                    .setWeightCriteria(tradeRemoteService.getWeightRuleItemById(weightCheck))
                    .setToHarbor(tradeRemoteService.getToHarborRuleItemById(Integer.valueOf(contractDetailVO.getDestination())))
                    .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                    .setFixstatus(LkgPriceFixStatusEnum.getByContractNum(contractDetailVO.getContractType(), contractDetailVO.getContractNum(), contractDetailVO.getTotalPriceNum()).getValue())
                    .setBeginDeliveryTime(contractDetailVO.getDeliveryStartTime())
                    .setSuperHairAmount(new BigDecimal(weightTolerance))
                    .setIsPackCalcWeight(contractDetailVO.getNeedPackageWeight())
                    .setIsSellOfBTB(0)
                    .setBondRate(new BigDecimal(contractDetailVO.getAddedDepositRate()))
                    .setIsDelay(convertContractType(contractDetailVO.getContractType()).getIsDelay())
                    .setIsSellOfCredit(contractDetailVO.getPaymentType() == PaymentTypeEnum.CREDIT.getType() ? 1 : 0)
                    .setIsBasic(convertContractType(contractDetailVO.getContractType()).getIsBasic())
                    .setStorehouseAddress(contractDetailVO.getLkgWarehouseCode())//发货库点 字典
                    .setIsProvideInvoice(1)
                    .setFreightCalcMode(LkgFreightCalcModeEnum.CUSTOMER_PAY.getValue())
                    .setExeOfficer(DEFAULT_EXE_OFFICER)
                    .setSigningUnit("")
                    //.setBagSkinWeight(PACKAGE_WEIGHT_NAME.equals(contractDetailVO.getPackageWeightName()) ? BigDecimal.ZERO : BigDecimal.ONE)
                    .setBagSkinWeight(getBagSkinWeight(contractDetailVO))
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 start
                    //.setSupplier(contractDetailVO.getCompanyName().equals("FL") ? DEFAULT_FL : DEFAULT_LDC)
                    .setSupplier(Objects.equals(contractDetailVO.getCompanyId(), DEFAULT_FL_ID) ? DEFAULT_FL : DEFAULT_LDC)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 end
                    .setDeliveryAddress(getDeliveryAddress(contractDetailVO.getShipWarehouseAddress(), contractDetailVO.getSalesType()))
                    .setRateOfDelayPenalty(BigDecimal.ZERO)
                    .setContractNumber(contractDetailVO.getLinkinageCode())
                    .setPaymentCondition(getPaymentCondition(contractDetailVO, ttAddEntity.getDepositRate(), ttAddEntity.getAddedDepositRate(), ttAddEntity.getCreditDays()))//付款条件
                    .setSuppleProvisions(DEFAULT_SUPPLE_PROVISIONS)
                    .setCreateUser(operator)
                    .setCheckUser(operator)
                    .setEffectiveTime(contractDetailVO.getSignDate())
                    .setTransactionType(syncValueAdapter.getTransactionType(lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory() == 1).getCode())//交易类型字典
                    .setFromContractNumber(getfromContractNumber(lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory()))
                    .setSignAddress(contractDetailVO.getSignPlace())
                    .setQualityDesc(QUALITY_CHECK_INFO)
                    .setCheckTime(contractDetailVO.getUpdatedAt())
                    .setIsSellOfCNF(DeliveryModeEnum.SEND.getValue() == deliveryTypeEntity.getType() ? 1 : 0)
                    .setModeOfDelivery(null == deliveryTypeEntity ? LkgBizConstant.DEFAULT_DELIVERY_TYPE : Integer.parseInt(deliveryTypeEntity.getLkgCode()))
                    .setChangeOrderNo("")
                    .setModeOfPrice(LkgPriceModeEnum.getByContractType(contractDetailVO.getContractType()).getValue())
                    .setEndDeliveryTime(contractDetailVO.getDeliveryEndTime())
                    .setSignTime(contractDetailVO.getSignDate())
                    .setCustomerCode(contractDetailVO.getLkgCustomerCode())
                    .setTrader(null == traderEntity ? DEFAULT_TRADER : traderEntity.getNickName())
                    .setFreightProvPrice(BigDecimal.ZERO)
                    .setTolAmount(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().toPlainString())
                    .setDepositAmount(null == contractDetailVO.getDepositAmount() ? "" : contractDetailVO.getDepositAmount().toPlainString())
                    .setCreateTime(ttAddEntity.getCreatedAt())
                    .setInvoiceType(tradeRemoteService.getInvoiceType(contractDetailVO.getGoodsCategoryId(), contractDetailVO.getInvoiceType(), taxRate.stripTrailingZeros().toPlainString()))
                    .setIsTransport(DeliveryModeEnum.SEND.getValue() == deliveryTypeEntity.getType() ? 1 : 0)
                    //.setIsTransport(DeliveryTypeEnum.getByValue(contractDetailVO.getDeliveryType()).getMode() == DeliveryModeEnum.SEND.getValue() ? 1 : 0)
                    .setIntoCompany("")
                    .setIsSellOfOEM(contractDetailVO.getOem())
                    .setRefContractNumber(contractDetailVO.getCustomerContractCode())
                    .setFrameAgreementNo(null != customerProtocolEntity ? (StringUtils.isBlank(customerProtocolEntity.getProtocolNo()) ? "" : customerProtocolEntity.getProtocolNo()) : "")
                    //.setModeOfPack(GoodsCategoryEnum.OSM_OIL.getValue().equals(contractDetailVO.getGoodsCategoryId()) ? LkgBizConstant.DEFAULT_OIL_PACKAGE : goodsDTO.getPackageName())
                    .setModeOfPack(goodsDTO.getPackageName())
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setTotalCount(contractDetailVO.getContractNum())
                    .setTranssequence(goodsCategoryEnum.getLkgTransSequenc())
                    .setPayConditionCode(StringUtils.isBlank(contractDetailVO.getPayConditionCode()) ? PAY_CONDITION_CODE : contractDetailVO.getPayConditionCode())
                    .setCommodityClass(goodsCategoryEnum.getLkgCommodityClass())
                    .setPayDepositTime(contractDetailVO.getSignDate());

            if (null == contractDetailVO.getPriceEndType() || contractDetailVO.getPriceEndType() == ContractPriceEndTypeEnum.TEXT.getValue()) {
                lkgSalesContractDefDTO.setEndFixPriceTime("");
                // lkgSalesContractDefDTO.setRemarks(lkgSalesContractDefDTO.getRemarks() + "截止定价日期：" + contractDetailVO.getPriceEndTime());
            } else {
                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 Start
                String priceEndTime = StringUtils.isBlank(contractDetailVO.getPriceEndTime()) ? "" : contractDetailVO.getPriceEndTime();
                // 如果日期格式为yyyy/MM/dd，则转换为yyyy-MM-dd格式
                if (priceEndTime.matches("\\d{4}/\\d{2}/\\d{2}")) {
                    priceEndTime = priceEndTime.replace("/", "-");
                }
                lkgSalesContractDefDTO.setEndFixPriceTime(priceEndTime);
                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 End
            }

            LkgSalesContractDetailDTO lkgSalesContractDetailDTO = new LkgSalesContractDetailDTO();
            ArrayList<LkgSalesContractDetailDTO> DataDTOList = new ArrayList();
            lkgSalesContractDetailDTO.setCommodityCode(goodsDTO.getLinkageGoodsCode())
                    .setCommodityName(getCommodityName(goodsDTO))
                    .setSpecifications(getSpecifications(goodsDTO))
                    .setCount(contractDetailVO.getContractNum())
                    .setPrice(contractDetailVO.getUnitPrice())
                    .setPriceOfFOB(contractDetailVO.getFobUnitPrice())
                    .setCalculateUnit(UnitEnum.getByName(contractDetailVO.getWeightUnit()).getLkgCode())
                    .setFinalAmount(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setFuturesContractNo(getFuturesContractNo(contractDetailVO.getDomainCode(), goodsDTO))
                    //.setCurrencyType(contractDetailVO.getCurrencyType())
                    .setCurrencyType(CurrencyTypeEnum.RMB.getDesc())
                    .setFuturesPrice(contractPriceEntity.getForwardPrice())
                    .setPremiumPrice(getPremiumPrice(contractDetailVO, contractPriceEntity))//含税单价-期货价格 contrucprice
                    .setShippingPrice(calcShippingPrice(contractPriceEntity))
                    .setRateOfLoss(BigDecimal.ZERO)
                    .setRateOfRMB(BigDecimal.ONE)
                    .setAmountOfRMB(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setQualityExplain("")
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setIsShippingChargesPrice(1)
                    .setCommodityClass(goodsCategoryEnum.getLkgCommodityClass())
                    .setUnitWeight(getUnitWeight(goodsDTO));
            DataDTOList.add(lkgSalesContractDetailDTO);
            lkgSalesContractDefDTO.setDetaillist(DataDTOList);

        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        log.info("============lkgSalesContractDefDTO=============={}", lkgSalesContractDefDTO);
        return lkgSalesContractDefDTO;
    }

    /**
     * 销售合同:Navigator → LKG
     *
     * @param contractDetailVO
     * @param lkgSyncRecordEntity
     * @return
     */
    public LkgSalesContractModifyDefDTO convertSalesContractModifyDTO(ContractDetailVO contractDetailVO, LkgSyncRecordEntity lkgSyncRecordEntity) {
        log.info("=================2022-05-27合同信息转换=================");

        //将Navigator合同信息转换为LKG的销售合同
        LkgSalesContractModifyDefDTO lkgSalesContractModifyDefDTO = null;
        try {
            //商品信息
            GoodsDTO goodsDTO = tradeRemoteService.getGoodsDTO(contractDetailVO.getGoodsId());

            // tt详情
            TTModifyEntity ttModifyEntity = tradeRemoteService.getTtModifyByTtId(lkgSyncRecordEntity.getTtId());

            // 操作人
            EmployEntity employEntity = tradeRemoteService.getEmployEntity(ttModifyEntity.getCreatedBy());
            // 所属商务
            EmployEntity traderEntity = tradeRemoteService.getEmployEntity(contractDetailVO.getOwnerId());

            //合同价格详情
            ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceEntity(contractDetailVO.getContractId());

            DeliveryTypeEntity deliveryTypeEntity = tradeRemoteService.getDeliveryTypeEntity(contractDetailVO.getDeliveryType());

            lkgSalesContractModifyDefDTO = new LkgSalesContractModifyDefDTO();

            // 特殊处理字段
            int weightCheck = Integer.parseInt(ttModifyEntity.getWeightCheck() == null ? contractDetailVO.getWeightCheck() : ttModifyEntity.getWeightCheck());
            int weightTolerance = ttModifyEntity.getWeightTolerance() == null ? contractDetailVO.getWeightTolerance() : ttModifyEntity.getWeightTolerance();

            // 稅率
            BigDecimal taxRate = contractDetailVO.getTaxRate().multiply(new BigDecimal(100));

            lkgSalesContractModifyDefDTO.setContractNumber(contractDetailVO.getLinkinageCode())
                    .setRefContractNumber(contractDetailVO.getCustomerContractCode())
                    .setBagSkinWeight(getBagSkinWeight(contractDetailVO))
                    .setSignAddress(contractDetailVO.getSignPlace())
                    .setTransactionType("")
                    .setQualityDesc(QUALITY_CHECK_INFO)
                    .setPaymentCondition(getPaymentCondition(contractDetailVO, ttModifyEntity.getDepositRate(), ttModifyEntity.getAddedDepositRate(), ttModifyEntity.getCreditDays()))//付款条件
                    .setRateOfDelayPenalty(BigDecimal.ZERO)
                    .setCreateUser(null == employEntity || StringUtil.isEmpty(employEntity.getNickName()) ? DEFAULT_CHECK_USER : employEntity.getNickName())
                    .setDirection(DEFAULT_DIRECTION)//方向
                    .setCheckTime(contractDetailVO.getUpdatedAt())
                    .setTrader(null == traderEntity ? DEFAULT_TRADER : traderEntity.getNickName())
                    .setExeOfficer(DEFAULT_EXE_OFFICER)
                    .setIsSellOfCredit(contractDetailVO.getPaymentType() == PaymentTypeEnum.CREDIT.getType() ? 1 : 0)
                    .setFixStatus(LkgPriceFixStatusEnum.getByContractNum(contractDetailVO.getContractType(), contractDetailVO.getContractNum(), contractDetailVO.getTotalPriceNum()).getValue())
                    .setIsBasic(convertContractType(contractDetailVO.getContractType()).getIsBasic())
                    .setModeOfDelivery(null == deliveryTypeEntity ? LkgBizConstant.DEFAULT_DELIVERY_TYPE : Integer.parseInt(deliveryTypeEntity.getLkgCode()))
                    .setChangeReason(StringUtils.isBlank(ttModifyEntity.getMemo()) ? "" : ttModifyEntity.getMemo())
                    .setCreateTime(ttModifyEntity.getCreatedAt())
                    .setEndDeliveryTime(contractDetailVO.getDeliveryEndTime())
                    .setSignTime(contractDetailVO.getSignDate())
                    .setStorehouseAddress(contractDetailVO.getLkgWarehouseCode())
                    .setIsProvideInvoice(IS_PROVIDE_INVOICE)
                    .setCustomerName(contractDetailVO.getCustomerName())
                    .setBeginDeliveryTime(contractDetailVO.getDeliveryStartTime())
                    .setCheckUser(null == employEntity || null == employEntity.getNickName() || "".equals(employEntity.getNickName()) ? DEFAULT_CHECK_USER : employEntity.getNickName())
                    .setChangeTime(ttModifyEntity.getCreatedAt())
                    // BUGFIX：case-1002949 NAV合同关闭后，LKG合同状态没有更新 Author: Mr 2025-02-19 start
                    //.setStatus(LkgContractStatusEnum.TAKING.getValue())
                    .setStatus(Objects.equals(ttModifyEntity.getType(), TTTypeEnum.CLOSED.getType())
                            ? LkgContractStatusEnum.CLOSED.getValue()
                            : LkgContractStatusEnum.TAKING.getValue())
                    // BUGFIX：case-1002949 NAV合同关闭后，LKG合同状态没有更新 Author: Mr 2025-02-19 end
                    .setModeOfPack(goodsDTO.getPackageName())
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setSuppleProvisions(DEFAULT_SUPPLE_PROVISIONS)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 start
                    //.setSupplier(contractDetailVO.getCompanyName().equals("FL") ? DEFAULT_FL : DEFAULT_LDC)
                    .setSupplier(Objects.equals(contractDetailVO.getCompanyId(), DEFAULT_FL_ID) ? DEFAULT_FL : DEFAULT_LDC)
                    // BUGFIX：case-1002948 NAV系统传输至LKG卖方主体错误 Author: Mr 2025-02-18 end
                    .setWeightCriteria(tradeRemoteService.getWeightRuleItemById(weightCheck))
                    .setToHarbor(StringUtils.isNotBlank(contractDetailVO.getDestination()) ? tradeRemoteService.getToHarborRuleItemById(Integer.valueOf(contractDetailVO.getDestination())) : "")
                    .setDepositRate(BigDecimal.valueOf(contractDetailVO.getDepositRate()))
                    .setDeliveryAddress(getDeliveryAddress(contractDetailVO.getShipWarehouseAddress(), contractDetailVO.getSalesType()))
                    .setSigningUnit("")
                    .setModeOfPrice(LkgPriceModeEnum.getByContractType(contractDetailVO.getContractType()).getValue())
                    .setPayDepositTime(contractDetailVO.getSignDate())
                    .setIsDelay(convertContractType(contractDetailVO.getContractType()).getIsDelay())
                    .setSuperHairAmount(new BigDecimal(weightTolerance))
                    .setIsPackCalcWeight(contractDetailVO.getNeedPackageWeight())
                    .setIsSellOfCNF(deliveryTypeEntity == null ? 0 : (DeliveryModeEnum.SEND.getValue() == deliveryTypeEntity.getType() ? 1 : 0))
                    .setChangeOrderNo(ttModifyEntity.getTtId() != null ? ttModifyEntity.getTtId().toString() : "")
                    .setFreightCalcMode(LkgFreightCalcModeEnum.CUSTOMER_PAY.getValue())
                    .setBondRate(BigDecimal.valueOf(contractDetailVO.getAddedDepositRate()))
                    .setInvoiceType(tradeRemoteService.getInvoiceType(contractDetailVO.getGoodsCategoryId(), contractDetailVO.getInvoiceType(), taxRate.stripTrailingZeros().toPlainString()))
                    .setCustomerCode(contractDetailVO.getLkgCustomerCode())
                    .setIsTransport(deliveryTypeEntity == null ? 0 : (DeliveryModeEnum.SEND.getValue() == deliveryTypeEntity.getType() ? 1 : 0))
                    //.setIsTransport(DeliveryTypeEnum.getByValue(contractDetailVO.getDeliveryType()).getMode() == DeliveryModeEnum.SEND.getValue() ? 1 : 0)
                    .setPayFullTime(DEFAULT_PAY_FULL_TIME)
                    .setFreightProvPrice(DEFAULT_FREIGHT_PROV_PRICE)
                    .setPayConditionCode(contractDetailVO.getPayConditionCode())
                    // BUGFIX：case-1002725 基差暂定价合同定价完毕后NAV中含税单价和LKG中含税单价对不上 Author: Mr 2024-10-24 Start
                    // .setDepositAmount(contractDetailVO.getDepositAmount());
                    .setDepositAmount(null == contractDetailVO.getDepositAmount() ? "" : contractDetailVO.getDepositAmount().stripTrailingZeros().toPlainString());
                    // BUGFIX：case-1002725 基差暂定价合同定价完毕后NAV中含税单价和LKG中含税单价对不上 Author: Mr 2024-10-24 End

            if (null == contractDetailVO.getPriceEndType() || contractDetailVO.getPriceEndType() == ContractPriceEndTypeEnum.TEXT.getValue()) {
                lkgSalesContractModifyDefDTO.setEndFixPriceTime("");
                // lkgSalesContractModifyDefDTO.setRemarks(lkgSalesContractModifyDefDTO.getRemarks() + " 截止定价日期：" + contractDetailVO.getPriceEndTime());
            } else {
                if (null == contractDetailVO.getPriceEndTime() || "".equals(contractDetailVO.getPriceEndTime().trim())) {
                    contractDetailVO.setPriceEndTime("");
                }
                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 Start
                String priceEndTime = contractDetailVO.getPriceEndTime();
                // 如果日期格式为yyyy/MM/dd，则转换为yyyy-MM-dd格式
                if (priceEndTime.matches("\\d{4}/\\d{2}/\\d{2}")) {
                    priceEndTime = priceEndTime.replace("/", "-");
                }
                lkgSalesContractModifyDefDTO.setEndFixPriceTime(priceEndTime);
                // BUGFIX：case-1002647 航海家原合同已经拆分   但LKG还显示原合同有余量 Author: Mr 2024-06-18 End
            }
            /*if (contractDetailVO.getStatus() == ContractStatusEnum.INVALID.getValue()) {
                lkgSalesContractModifyDefDTO.setStatus(LkgContractStatusEnum.INVALID.getValue());
            } else if (contractDetailVO.getStatus() == ContractStatusEnum.CLOSED.getValue()) {
                lkgSalesContractModifyDefDTO.setStatus(LkgContractStatusEnum.CLOSED.getValue());
            }*/

            LkgSalesContractModifyDetailDTO lkgSalesContractModifyDetailDTO = new LkgSalesContractModifyDetailDTO();
            ArrayList<LkgSalesContractModifyDetailDTO> DataDTOList = new ArrayList();
            lkgSalesContractModifyDetailDTO.setCommodityCode(goodsDTO.getLinkageGoodsCode())
                    .setCommodityName(getCommodityName(goodsDTO))
                    .setSpecifications(getSpecifications(goodsDTO))
                    .setCount(getCount(contractDetailVO, lkgSyncRecordEntity.getTradeType(), contractDetailVO.getIsChangeFactory() == 1 ? true : false))
                    .setPrice(contractDetailVO.getUnitPrice())
                    .setPriceOfFOB(contractDetailVO.getFobUnitPrice())
                    .setCalculateUnit(UnitEnum.getByName(contractDetailVO.getWeightUnit()).getLkgCode())
                    .setFinalAmount(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setFuturesContractNo(getFuturesContractNo(contractDetailVO.getDomainCode(), goodsDTO))
                    //.setCurrencyType(contractDetailVO.getCurrencyType())
                    .setCurrencyType(CurrencyTypeEnum.RMB.getDesc())
                    .setFuturesPrice(contractPriceEntity.getForwardPrice())
                    .setPremiumPrice(getPremiumPrice(contractDetailVO, contractPriceEntity))
                    .setUnitWeight(getUnitWeight(goodsDTO))
                    .setShippingPrice(calcShippingPrice(contractPriceEntity))
                    .setRateOfLoss(BigDecimal.ZERO)
                    .setRateOfRMB(BigDecimal.ONE)
                    .setAmountOfRMB(null == contractDetailVO.getTotalAmount() ? "" : contractDetailVO.getTotalAmount().stripTrailingZeros().toPlainString())
                    .setQualityExplain("")
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo())
                    .setIsShippingChargesPrice(1)
                    .setContractNumber(contractDetailVO.getLinkinageCode());
            DataDTOList.add(lkgSalesContractModifyDetailDTO);

            lkgSalesContractModifyDefDTO.setDetaillist(DataDTOList);

        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return lkgSalesContractModifyDefDTO;
    }

    private LkgContractTypeDTO convertContractType(Integer contractType) {
        // 基差合同 1是，0否
        int isBasic = 0;
        // 定价延期 1是，0否
        int isDelay = 0;

        switch (ContractTypeEnum.getByValue(contractType)) {
            case YI_KOU_JIA:
                isBasic = 0;
                isDelay = 0;
                break;
            case JI_CHA:
                isBasic = 1;
                isDelay = 0;
                break;
            case ZAN_DING_JIA:
                isBasic = 0;
                isDelay = 1;
                break;
            case JI_CHA_ZAN_DING_JIA:
                isBasic = 1;
                isDelay = 0;
                break;
            default:
                break;
        }
        return new LkgContractTypeDTO().setIsBasic(isBasic).setIsDelay(isDelay);
    }

    public LkgQueryRecordDTO convertLkgQueryRecordDTO(String contractNumber, String refContractNumber) {

        LkgQueryRecordDTO LkgQueryRecordDTO = new LkgQueryRecordDTO();
        LkgQueryRecordDTO.setContractNumber(contractNumber)
                .setRefContractNumber(refContractNumber);

        return LkgQueryRecordDTO;
    }

    public LkgContractPriceDefDTO convertLkgContractPriceDefDTO(SyncRequestDTO syncRequestDTO, ConfirmPriceDTO confirmPriceDTO, ContractDetailVO contractDetailVO) {
        LkgContractPriceDefDTO lkgContractPriceDefDTO = null;

        try {
            //商品信息
            GoodsDTO goodsDTO = tradeRemoteService.getGoodsDTO(contractDetailVO.getGoodsId());

            //TT详情
            TradeTicketEntity tradeTicketEntity = tradeRemoteService.getTradeTicketById(syncRequestDTO.getTtId());

            //合同价格详情
            ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceEntity(contractDetailVO.getContractId());

            //合同的Owner
            EmployEntity employEntity = tradeRemoteService.getEmployEntity(tradeTicketEntity.getCreatedBy());

            lkgContractPriceDefDTO = new LkgContractPriceDefDTO();
            lkgContractPriceDefDTO
                    .setOptUser(employEntity != null ? employEntity.getNickName() : "")  //LKG系统ID
                    .setContractNumber(contractDetailVO.getLinkinageCode())
                    .setCommodityCode(goodsDTO.getLinkageGoodsCode())
                    .setCommodityName(getCommodityName(goodsDTO))
                    .setSpecifications(getSpecifications(goodsDTO))
                    .setContractCount(contractDetailVO.getContractNum())
                    .setFuturesContractNo(getFuturesContractNo(contractDetailVO.getDomainCode(), goodsDTO))
                    .setFuturesPrice(confirmPriceDTO.getPrice())
                    .setPremiumPrice(getPremiumPrice(contractDetailVO, contractPriceEntity))
                    //.setFinalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, confirmPriceDTO.getPrice(), confirmPriceDTO.getNum()))
                    .setLoadingFee(BigDecimal.ZERO)
                    .setFreight(BigDecimal.ZERO)
                    .setCurrencyType(CurrencyTypeEnum.RMB.getDesc())
                    .setSubsidyOfPack(BigDecimal.ZERO)
                    .setSubsidyOfSelf(BigDecimal.ZERO)
                    .setDescribes("")
                    .setCheckUser(employEntity != null ? employEntity.getNickName() : "")  //LKG系统ID
                    .setCreateUser(employEntity != null ? employEntity.getNickName() : "")  //LKG系统ID
                    .setCreateTime(confirmPriceDTO.getCreatedAt())
                    .setRemarks(StringUtils.isBlank(contractDetailVO.getMemo()) ? "" : contractDetailVO.getMemo());

            if (syncRequestDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {

                CustomerDTO customerDTO = tradeRemoteService.getCustomerDTO(contractDetailVO.getSupplierId());

                lkgContractPriceDefDTO
                        .setCustomerCode(customerDTO.getLinkageCustomerCode())
                        .setCustomerName(customerDTO.getName())
                        .setCustomerContacts(customerDTO.getContact())
                        .setCustomerFax(customerDTO.getPhone())
                        .setStatus(LkgContractStatusEnum.TAKING.getValue())
                        .setFixPriceNo(confirmPriceDTO.getTtId().toString())
                        .setFixPriceTime(confirmPriceDTO.getCreatedAt())
                        .setFixPriceBatch(tradeTicketEntity.getCode())
                        .setYetFixPriceCount(contractDetailVO.getPricedNum())
                        .setNoFixPriceCount(contractDetailVO.getNotPricedNum())
                        .setThisFixPriceCount(confirmPriceDTO.getNum())
                        .setThisFixPricePrice(lkgContractPriceDefDTO.getFuturesPrice().add(lkgContractPriceDefDTO.getPremiumPrice()))
                        .setFinalAmount(lkgContractPriceDefDTO.getThisFixPricePrice().multiply(lkgContractPriceDefDTO.getThisFixPriceCount()).stripTrailingZeros().toPlainString())
                ;
            } else {
                CustomerDTO customerDTO = tradeRemoteService.getCustomerDTO(contractDetailVO.getCustomerId());

                lkgContractPriceDefDTO
                        .setCustomerCode(customerDTO.getLinkageCustomerCode())
                        .setCustomerName(customerDTO.getName())
                        .setCustomerContacts(customerDTO.getContact())
                        .setCustomerFax(customerDTO.getPhone())
                        .setStatus(LkgContractStatusEnum.EFFECTIVE.getValue())
                        .setSettleNo(confirmPriceDTO.getTtId().toString())
                        .setSettleTime(confirmPriceDTO.getCreatedAt())
                        .setSettleBatch(tradeTicketEntity.getCode())
                        .setYetSettleCount(contractDetailVO.getPricedNum())
                        .setNoSettleCount(contractDetailVO.getNotPricedNum())
                        .setThisSettleCount(confirmPriceDTO.getNum())
                        .setThisSettlePrice(lkgContractPriceDefDTO.getFuturesPrice().add(lkgContractPriceDefDTO.getPremiumPrice()))
                        .setFinalAmount(lkgContractPriceDefDTO.getThisSettlePrice().multiply(lkgContractPriceDefDTO.getThisSettleCount()).stripTrailingZeros().toPlainString())
                ;
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return lkgContractPriceDefDTO;
    }

    private static String getPaymentCondition(ContractDetailVO contractDetailVO, Integer depositRate, Integer addedDepositRate, Integer creditDays) {
        //付款方式(有赊销天数则为赊销；无赊销天数为预付款)--"合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）
        String s = "";
        if (null == depositRate) {
            depositRate = contractDetailVO.getDepositRate();
        }

        if (null == addedDepositRate) {
            addedDepositRate = contractDetailVO.getAddedDepositRate();
        }

        if (null == creditDays) {
            creditDays = contractDetailVO.getCreditDays();
        }

        if (contractDetailVO.getPaymentType() == PaymentTypeEnum.CREDIT.getType()) {
            if (ContractTypeEnum.getBasicTypeList().contains(contractDetailVO.getContractType())) {
                if (100 - depositRate - addedDepositRate < 0) {
                    s = depositRate + "%," + addedDepositRate + "%," + creditDays.toString() + "days";
                } else {
                    s = depositRate + "%," + addedDepositRate + "%," + (100 - depositRate - addedDepositRate) + "%," + creditDays.toString() + "days";
                }
            } else {
                s = depositRate + "%,0%," + (100 - depositRate) + "%," + creditDays.toString() + "days";
            }
        } else {
            if (ContractTypeEnum.getBasicTypeList().contains(contractDetailVO.getContractType())) {
                if (100 - depositRate - addedDepositRate < 0) {
                    s = depositRate + "%," + addedDepositRate + "%";
                } else {
                    s = depositRate + "%," + addedDepositRate + "%," + (100 - depositRate - addedDepositRate) + "%";
                }
            } else {
                s = depositRate + "%,0%," + (100 - depositRate) + "%";
            }
        }
        return s;
    }


    private BigDecimal getUnitWeight(GoodsDTO goodsDTO) {
        BigDecimal UnitWeight = new BigDecimal(0);
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(goodsDTO.getCategoryId()) && StringUtils.isNotBlank(goodsDTO.getPackageName())) {
            if (goodsDTO.getPackageName().contains("50KG")) {
                UnitWeight = new BigDecimal(50);
            } else if (goodsDTO.getPackageName().contains("70KG")) {
                UnitWeight = new BigDecimal(70);
            }
        }
        return UnitWeight;
    }

    private BigDecimal calcShippingPrice(ContractPriceEntity contractPriceEntity) {
        return BigDecimalUtil.initBigDecimal(contractPriceEntity.getTransportPrice())
                .add(BigDecimalUtil.initBigDecimal(contractPriceEntity.getLiftingPrice()))
                .add(BigDecimalUtil.initBigDecimal(contractPriceEntity.getDelayPrice()))
                .add(BigDecimalUtil.initBigDecimal(contractPriceEntity.getTemperaturePrice()))
                .add(BigDecimalUtil.initBigDecimal(contractPriceEntity.getOtherDeliveryPrice()));
    }

    private int getGoodsClass(GoodsDTO goodsDTO) {
        int goodsClass;
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(goodsDTO.getCategoryId())) {
            goodsClass = 2;
        } else {
            goodsClass = 3;
        }
        return goodsClass;
    }

    private String getCommodityName(GoodsDTO goodsDTO) {
        String commodityName = goodsDTO.getName();
        try {
            commodityName = goodsDTO.getLinkageGoodsName();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return commodityName;
    }

    private String getSpecifications(GoodsDTO goodsDTO) {
        String specifications = "";
        try {
            if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(goodsDTO.getCategoryId())) {
                specifications = goodsDTO.getSpecName();
            } else {
                specifications = "";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return specifications;
    }

    private BigDecimal getPremiumPrice(ContractDetailVO contractDetailVO, ContractPriceEntity contractPriceEntity) {

        return BigDecimalUtil.initBigDecimal(contractDetailVO.getUnitPrice())
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getForwardPrice()))
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getTransportPrice()))
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getLiftingPrice()))
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getDelayPrice()))
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getTemperaturePrice()))
                .subtract(BigDecimalUtil.initBigDecimal(contractPriceEntity.getOtherDeliveryPrice()));


    }

    private BigDecimal getBagSkinWeight(ContractDetailVO contractDetailVO) {
        log.info("===================getBagSkinWeight========================={}", contractDetailVO.getPackageWeightName());
        BigDecimal bagSkinWeight = BigDecimal.ZERO;
        try {
            String str = contractDetailVO.getPackageWeightName();
            if (!StringUtils.isBlank(str)) {
                str = str.toLowerCase().replace("kg", "");
                bagSkinWeight = new BigDecimal(str);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("===================return bagSkinWeight========================={}", bagSkinWeight);
        return bagSkinWeight;
    }

    private String getfromContractNumber(Integer tradeType, Integer isChangeFactory) {
        String transactionType = syncValueAdapter.getTransactionType(tradeType, isChangeFactory == 1 ? true : false).getCode();
        String fromContractNumber = "";
        try {
            if ("SPLIT".equalsIgnoreCase(transactionType) || "TRANSFER".equalsIgnoreCase(transactionType)) {
                fromContractNumber = "详见航海家系统";
            }
            if (ContractTradeTypeEnum.BUYBACK == ContractTradeTypeEnum.getByValue(tradeType)) {
                fromContractNumber = "详见航海家系统";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fromContractNumber;
    }

    private String getTransactionType(Integer tradeType, boolean isFactoryChanged) {
        String transactionType = "";
        try {
            ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeType);

            if (ContractTradeTypeEnum.REVISE_NORMAL == contractTradeTypeEnum) {
                if (!isFactoryChanged) {
                    transactionType = LkgTransactionTypeEnum.REVISE.getCode();
                }
            } else if (ContractTradeTypeEnum.TRANSFER_ALL == contractTradeTypeEnum) {
                transactionType = LkgTransactionTypeEnum.REVISE.getCode();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return transactionType;
    }

    //只针对采销合同修改_未变主体 交货工厂改变 更新数量为0
    private BigDecimal getCount(ContractDetailVO contractDetailVO, Integer tradeType, boolean isFactoryChanged) {
        BigDecimal count = contractDetailVO.getContractNum();
        try {
            ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeType);

            if (ContractTradeTypeEnum.REVISE_NORMAL == contractTradeTypeEnum) {
                if (isFactoryChanged) {
                    count = new BigDecimal(0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    private String getFuturesContractNo(String domainCode, GoodsDTO goodsDTO) {
        String futuresContractNo = "";
        try {
            futuresContractNo = goodsDTO.getFuturePrefix() + domainCode + "_" + goodsDTO.getFutureSuffix();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return futuresContractNo;
    }
}
