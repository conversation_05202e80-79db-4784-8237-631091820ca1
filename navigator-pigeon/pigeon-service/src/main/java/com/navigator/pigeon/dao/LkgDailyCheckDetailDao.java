package com.navigator.pigeon.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgDailyCheckDetailMapper;
import com.navigator.pigeon.pojo.entity.LkgDailyCheckDetailEntity;
import com.navigator.pigeon.pojo.enums.LkgCheckResultEnum;

import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/6/13
 * @Time: 18:39
 * @Desception:
 */
@Dao
public class LkgDailyCheckDetailDao extends BaseDaoImpl<DbiLkgDailyCheckDetailMapper, LkgDailyCheckDetailEntity> {


    /**
     * 获取校核未通过的数据
     * @return
     */
    public List<LkgDailyCheckDetailEntity> queryList(){
        LambdaQueryWrapper<LkgDailyCheckDetailEntity> wrapper = new LambdaQueryWrapper<LkgDailyCheckDetailEntity>()
                .eq(LkgDailyCheckDetailEntity::getResult, LkgCheckResultEnum.CHECK_NO_PASS.getStatus())
                ;
        return this.baseMapper.selectList(wrapper);
    }

}
