package com.navigator.pigeon.facade.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.common.dto.Result;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.pigeon.dao.LkgContractDao;
import com.navigator.pigeon.dao.LkgContractInfoDao;
import com.navigator.pigeon.dao.LkgSyncRecordDao;
import com.navigator.pigeon.dao.LkgSyncRequestDao;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.dto.LkgInterfaceResponseDTO;
import com.navigator.pigeon.pojo.dto.LkgQueryRecordDTO;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import com.navigator.pigeon.pojo.enums.LkgSyncStatusEnum;
import com.navigator.pigeon.service.lkg.ILkgContractDailyCheckService;
import com.navigator.pigeon.service.lkg.ISyncContractService;
import com.navigator.pigeon.service.lkg.LkgContractHandleService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RestController
@Slf4j
public class LkgContractFacadeImpl implements LkgContractFacade {
    @Resource
    ISyncContractService syncContractService;
    @Resource
    LkgContractHandleService lkgContractHandleService;
    @Resource
    ContractFacade contractFacade;
    @Resource
    LkgSyncRequestDao lkgSyncRequestDao;
    @Resource
    LkgSyncRecordDao lkgSyncRecordDao;
    @Resource
    LkgContractDao lkgContractDao;
    @Resource
    LkgContractInfoDao contractInfoDao;
    @Resource
    ILkgContractDailyCheckService dailyCheckService;


    @Override
    public Result<SyncRequestDTO> syncContractRequest(SyncRequestDTO syncRequestDTO) {
        log.error("===================LkgContractFacadeImpl.syncContractRequest===================");
        log.error(JSON.toJSONString(syncRequestDTO));

        syncRequestDTO = syncContractService.syncContractRequest(syncRequestDTO);

        return Result.success(syncRequestDTO);
    }

    @Override
    public Result reSyncContract(SyncRequestDTO syncRequestDTO) {
        // 同步场景
        String contractCode = syncRequestDTO.getContractCode();
        if (StringUtils.isNotBlank(contractCode)) {
            Result contractIdResult = contractFacade.getContractIdByCode(contractCode);
            if (contractIdResult.isSuccess() && null != contractIdResult.getData()) {
                ContractEntity contractEntity = contractFacade.getBasicContractById((Integer) contractIdResult.getData());
                syncRequestDTO
                        .setContractType(contractEntity.getContractType())
                        .setReferContractId(syncRequestDTO.getReferContractId() == 0 ? null : syncRequestDTO.getReferContractId())
                        .setSalesType(contractEntity.getSalesType())
                        .setCustomerCode(contractEntity.getCustomerCode())
                        .setContractCode(contractEntity.getContractCode())
                        .setContractId(contractEntity.getId())
                        .setParentContractId(contractEntity.getParentId())
                        .setIsChangeFactory(syncRequestDTO.getIsChangeFactory() == null ? 0 : syncRequestDTO.getIsChangeFactory())
                        .setConfirmPriceId(syncRequestDTO.getConfirmPriceId() == -1 ? null : syncRequestDTO.getConfirmPriceId());
                syncRequestDTO = syncContractService.syncContractRequest(syncRequestDTO);
            }
        }
        return Result.success(syncRequestDTO);
    }

    @Override
    public Result reSyncContractRequest(Integer reqId) {
        System.out.println("================LkgContractFacadeImpl.reSyncContractRequest================");
        //重新同步
        syncContractService.reSyncContractRequest(reqId);
        return Result.success();
    }

    @Override
    public Result reBuildSyncContractRequest(Integer reqId) {
        System.out.println("================LkgContractFacadeImpl.reBuildSyncContractRequest================");
        //重新同步
        syncContractService.reBuildSyncContractRequest(reqId);
        return Result.success();
    }

    @Override
    public Result getLkgContract(String contractCode) {

        LkgContractDTO lkgContractDTO = new LkgContractDTO();
        lkgContractDTO.setContractCode(contractCode)
                .setContractNumber(contractCode);
        lkgContractDTO = syncContractService.getLkgContract(contractCode);
        if (null == lkgContractDTO) {
            lkgContractDTO = new LkgContractDTO();
            lkgContractDTO.setContractNumber(contractCode);
        }

        return Result.success(lkgContractDTO);
    }

    @Override
    public Result getBatchLkgContract(List<String> contractCodeList) {
        return null;
    }

    @Override
    public Result getBatchLkgContractList(Integer pageSize, Integer pageNum) {
        // 获取lkg的合同列表
        List<LkgContractEntity> batchLkgContractList = lkgContractDao.getBatchLkgContractList(pageSize, pageNum);

        CompletableFuture<Void>[] futures = new CompletableFuture[pageNum];

        List<Result> resultList = new ArrayList<>();

        for (int i = 0; i < batchLkgContractList.size(); i++) {
            final int taskIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                // 调用LKG接口
                Result lkgContract = getLkgContract(batchLkgContractList.get(taskIndex).getContractCode());

                log.info("Task " + taskIndex + " is running " + batchLkgContractList.get(taskIndex).getContractCode());
                resultList.add(lkgContract);
            });
        }
        CompletableFuture.allOf(futures).join();

        return Result.success(resultList);
    }

    @Override
    public Result getCustomerContractFutureInfo(String customerCode) {
        return null;
    }

    @Override
    public Result exportTemplate(int salesType, String contractCode, HttpServletResponse response) {
        log.info("salesType=", salesType);
        log.info("contractCode=", contractCode);
        return syncContractService.exportTemplate(salesType, contractCode, response);
    }

    /**
     * 导入lkg合同数据
     *
     * @param file
     * @param salesType
     * @return
     */
    @Override
    public Result importContract(MultipartFile file, Integer salesType) {
        log.info("salesType=", salesType);
        return lkgContractHandleService.importContract(file, salesType);
    }

    /**
     * 将导入后的采销合同数据同步到prepareContract
     *
     * @return
     */
    @Override
    public Result syncPrepareContract() {
        lkgContractHandleService.syncPrepareContract();
        return Result.success();
    }

    @Override
    public Result syncContract() {
        lkgContractHandleService.syncContract();
        return Result.success();
    }

    @Override
    public Result checkLkgRecord(String startCheckTime, String endCheckTime) {
        // 校验批次号
        String checkBatch = "BATCH" + DateTimeUtil.formatDateTimeValue();

        // 1.根据同步接口结果判断
        List<LkgSyncRequestEntity> requestEntityList = lkgSyncRequestDao.queryListByCheckDate(startCheckTime, endCheckTime);

        List<LkgSyncRecordEntity> recordEntityList = lkgSyncRecordDao.queryListByCheckDate(startCheckTime, endCheckTime);

        // 同步总数量
        int totalCount = requestEntityList.size();
        int failCount = 0;

        // 一般存在request缺失record
        if (requestEntityList.size() != recordEntityList.size()) {
            // 查找缺失数据
            for (LkgSyncRequestEntity requestEntity : requestEntityList) {
                List<LkgSyncRecordEntity> recordList = lkgSyncRecordDao.getLkgSyncRecordList(requestEntity.getId());
                if (CollectionUtil.isEmpty(recordList)) {
                    log.info("checkInfo: record数据丢失，requestId:{}", requestEntity.getId());
                    failCount++;
                }
            }

            // 结果插入到check_result
        } else {
            // sync_status:0,2
            for (LkgSyncRecordEntity recordEntity : recordEntityList) {
                if (recordEntity.getSyncStatus() == LkgSyncStatusEnum.INIT.getValue()) {
                    log.info("checkInfo: 转换record异常，requestId:{}", recordEntity.getRequestId());
                    failCount++;
                } else if (recordEntity.getSyncStatus() == LkgSyncStatusEnum.SYNCING.getValue()) {
                    String resultsInfo = recordEntity.getLkgResultsInfo();
                    LkgInterfaceResponseDTO responseDTO = (LkgInterfaceResponseDTO) JSONObject.parse(resultsInfo);
                    log.info("checkInfo: 调用lkg异常，requestId:{},原因:{}", recordEntity.getRequestId(), responseDTO.getMsg());
                    failCount++;
                }
            }
        }

        return Result.success();
    }

    @Override
    public Result getLkgRequestByTtId(Integer ttId) {
        List<LkgSyncRequestEntity> requestEntityList = lkgSyncRequestDao.queryListByTtId(ttId);
        return Result.success(requestEntityList);
    }

    @Override
    public Result getLkgRecordByRequestId(Integer requestId) {
        List<LkgSyncRecordEntity> lkgSyncRecordList = lkgSyncRecordDao.getLkgSyncRecordList(requestId);
        return Result.success(lkgSyncRecordList);
    }

    @Override
    public Result getLocalLkgContractByContractCode(String contractCode) {
        return Result.success(contractInfoDao.getByContractCode(contractCode));
    }

    @Override
    public Result getLocalLkgContractByContractCodeList(LkgQueryRecordDTO queryRecordDTO) {
        return Result.success(contractInfoDao.getByContractCodeList(queryRecordDTO.getContractCodeList()));
    }

    @Override
    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 Start
    public Result closeContractByFile(String checkDate, MultipartFile file) {
        return Result.success(dailyCheckService.closeDailyContract(checkDate, file));
    }
    // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 End
}
