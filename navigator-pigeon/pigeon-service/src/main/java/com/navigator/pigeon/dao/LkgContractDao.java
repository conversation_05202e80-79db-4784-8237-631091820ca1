package com.navigator.pigeon.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgContractMapper;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;

import java.util.List;

@Dao
public class LkgContractDao extends BaseDaoImpl<DbiLkgContractMapper, LkgContractEntity> {

    public LkgContractEntity getLkgContract(Integer contractId) {
        LkgContractEntity lkgContractEntity = null;
        List<LkgContractEntity> contractEntityList = this.baseMapper.selectList(Wrappers.<LkgContractEntity>lambdaQuery()
                .eq(LkgContractEntity::getContractId, contractId)
                .orderByDesc(LkgContractEntity::getId)
        );

        if (null != contractEntityList && contractEntityList.size() > 0) {
            lkgContractEntity = contractEntityList.get(0);
        }

        return lkgContractEntity;
    }

    public LkgContractEntity getLkgContract(String contractCode) {
        LkgContractEntity lkgContractEntity = null;
        List<LkgContractEntity> contractEntityList = this.baseMapper.selectList(Wrappers.<LkgContractEntity>lambdaQuery()
                .eq(LkgContractEntity::getContractCode, contractCode)
                .orderByDesc(LkgContractEntity::getId)
        );

        if (null != contractEntityList && contractEntityList.size() > 0) {
            lkgContractEntity = contractEntityList.get(0);
        }

        return lkgContractEntity;
    }

    public List<LkgContractEntity> getBatchLkgContractList(Integer pageSize, Integer pageNum) {
        return this.page((new Page<>(pageSize, pageNum)), Wrappers.<LkgContractEntity>lambdaQuery()
                .orderByDesc(LkgContractEntity::getId)).getRecords();
    }
}