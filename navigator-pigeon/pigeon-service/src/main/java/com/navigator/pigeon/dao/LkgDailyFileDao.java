package com.navigator.pigeon.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgDailyFileMapper;
import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;

import java.util.List;

/**
 * <p>
 * dbi_lkg_daily_file Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Dao
public class LkgDailyFileDao extends BaseDaoImpl<DbiLkgDailyFileMapper, LkgDailyFileEntity> {
    public List<LkgDailyFileEntity> queryList(LkgDailyFileEntity lkgDailyFileEntity){
        LambdaQueryWrapper<LkgDailyFileEntity> wrapper = new LambdaQueryWrapper<LkgDailyFileEntity>()
                .eq(StrUtil.isNotBlank(lkgDailyFileEntity.getCheckDate()),LkgDailyFileEntity::getCheckDate,lkgDailyFileEntity.getCheckDate())
                .eq(null!=lkgDailyFileEntity.getHandleStatus(),LkgDailyFileEntity::getHandleStatus,lkgDailyFileEntity.getHandleStatus())
                .eq(StrUtil.isNotBlank(lkgDailyFileEntity.getFileName()),LkgDailyFileEntity::getFileName,lkgDailyFileEntity.getFileName())
                .eq(null!=lkgDailyFileEntity.getSalesType(),LkgDailyFileEntity::getSalesType,lkgDailyFileEntity.getSalesType())
                ;
        return this.baseMapper.selectList(wrapper);
    }

    public LkgDailyFileEntity getDailyFileEntity(LkgDailyFileEntity lkgDailyFileEntity){
        LambdaQueryWrapper<LkgDailyFileEntity> wrapper = new LambdaQueryWrapper<LkgDailyFileEntity>()
                .eq(StrUtil.isNotBlank(lkgDailyFileEntity.getCheckDate()),LkgDailyFileEntity::getCheckDate,lkgDailyFileEntity.getCheckDate())
                .eq(StrUtil.isNotBlank(lkgDailyFileEntity.getFileSource()),LkgDailyFileEntity::getFileSource,lkgDailyFileEntity.getFileSource())
                .eq(null!=lkgDailyFileEntity.getHandleStatus(),LkgDailyFileEntity::getHandleStatus,lkgDailyFileEntity.getHandleStatus())
                .eq(StrUtil.isNotBlank(lkgDailyFileEntity.getFilePath()),LkgDailyFileEntity::getFilePath,lkgDailyFileEntity.getFilePath())
                .eq(null!=lkgDailyFileEntity.getSalesType(),LkgDailyFileEntity::getSalesType,lkgDailyFileEntity.getSalesType())
                ;
        LkgDailyFileEntity fileEntity = getOne(wrapper);
        return fileEntity;
    }
}