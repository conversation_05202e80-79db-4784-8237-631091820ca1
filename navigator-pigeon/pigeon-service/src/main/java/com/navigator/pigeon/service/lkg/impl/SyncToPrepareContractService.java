package com.navigator.pigeon.service.lkg.impl;

import cn.hutool.core.util.IdUtil;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.pigeon.dao.LkgContractDao;
import com.navigator.pigeon.dao.LkgPurchaseContractDao;
import com.navigator.pigeon.dao.LkgSalesContractDao;
import com.navigator.pigeon.dao.PrepareContractDao;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;
import com.navigator.pigeon.pojo.entity.LkgPurchaseContractEntity;
import com.navigator.pigeon.pojo.entity.LkgSalesContractEntity;
import com.navigator.pigeon.pojo.entity.PrepareContractEntity;
import com.navigator.pigeon.pojo.enums.LkgImportContractSyncedStatusEnum;
import com.navigator.pigeon.pojo.enums.LkgPriceModeEnum;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR> @date
 */
@Service
@Slf4j
public class SyncToPrepareContractService {

    @Resource
    TradeRemoteService tradeRemoteService;
    @Resource
    LkgPurchaseContractDao lkgPurchaseContractDao;
    @Resource
    LkgSalesContractDao lkgSalesContractDao;
    @Resource
    PrepareContractDao prepareContractDao;
    @Resource
    LkgContractDao lkgContractDao;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private FactoryFacade factoryFacade;

    // 使用Map存储品类、主体和工厂的ID
    private final Map<Integer, String> companyMap = new HashMap<>();
    private final Map<String, String> factoryMap = new HashMap<>();
    private final Map<String, SiteEntity> siteMap = new HashMap<>();
    private final Map<String, DeliveryTypeEntity> deliveryTypeMap = new HashMap<>();
    private final Map<String, CustomerEntity> customerMap = new HashMap<>();
    private final Map<String, CustomerEntity> supplierMap = new HashMap<>();
    private final Map<String, SkuEntity> goodsMap = new HashMap<>();
    private final Map<String, PayConditionEntity> payConditionMap = new HashMap<>();
    private final Map<String, String> weightCheckMap = new HashMap<>();
    private final Map<String, SystemRuleItemEntity> destinationMap = new HashMap<>();
    private final Map<String, WarehouseEntity> warehouseMap = new HashMap<>();
    private final Map<String, EmployEntity> employMap = new HashMap<>();
    private final Map<String, CustomerBankEntity> bankInfoMap = new HashMap<>();
    private final Map<String, CustomerBankDTO> supplierAccountMap = new HashMap<>();

    private static final int BATCH_SIZE = 50; // 每次提交 50 条

    // 初始化主体/工厂/品类/实角色
    private void initBasicImport() {
        // 主体
        if (factoryMap.isEmpty()) {
            List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
            for (CompanyEntity companyEntity : companyEntityList) {
                companyMap.put(companyEntity.getId(), companyEntity.getName());
            }
        }

        // 工厂
        if (factoryMap.isEmpty()) {
            List<FactoryEntity> factoryEntityList = factoryFacade.getAllFactoryList(null);
            for (FactoryEntity factoryEntity : factoryEntityList) {
                factoryMap.put(factoryEntity.getCode(), factoryEntity.getName());
            }
        }
    }

    /**
     * 同步销售合同数据
     *
     * @param lkgSalesContractList 销售合同数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncSalesPrepareContract(List<LkgSalesContractEntity> lkgSalesContractList) {
        int successNum = 0;

        // 初始化数据
        initBasicImport();

        List<PrepareContractEntity> saveList = new ArrayList<>();

        for (LkgSalesContractEntity lkgSalesContract : lkgSalesContractList) {

            PrepareContractEntity prepareContractEntity = BeanConvertUtils.map(PrepareContractEntity.class, lkgSalesContract);

            // 账套信息
            // SiteEntity siteEntity = tradeRemoteService.getSiteEntityByName(lkgSalesContract.getSiteName());
            String siteName = lkgSalesContract.getSiteName();
            SiteEntity siteEntity = siteMap.computeIfAbsent(siteName, name -> tradeRemoteService.getSiteEntityByName(name));
            if (siteEntity == null) {
                log.error("{}导入失败,账套无效)", lkgSalesContract.getContractNumber());
                continue;
            }

            prepareContractEntity
                    .setSiteCode(siteEntity.getCode())
                    .setSiteName(siteEntity.getName())
                    .setBelongCustomerId(siteEntity.getBelongCustomerId())
                    .setDeliveryFactoryCode(siteEntity.getFactoryCode())
                    .setDeliveryFactory(siteEntity.getFactoryCode())
                    .setDeliveryFactoryName(factoryMap.get(siteEntity.getFactoryCode()))
                    .setCompanyId(siteEntity.getCompanyId())
                    .setCompanyName(companyMap.get(siteEntity.getCompanyId()));

            // customer信息
            // CustomerEntity customerDTO = tradeRemoteService.getCustomerDTO(lkgSalesContract.getCustomerCode());
            String customerCode = lkgSalesContract.getCustomerCode();
            CustomerEntity customerDTO = customerMap.computeIfAbsent(customerCode, code -> tradeRemoteService.getCustomerDTO(code));
            if (null == customerDTO) {
                log.error("{}导入失败,customer信息为空)", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setCustomerId(customerDTO.getId())
                    .setCustomerCode(customerDTO.getLinkageCustomerCode())
                    .setCustomerName(customerDTO.getName())
                    .setCustomerStatus(customerDTO.getStatus())
                    .setNeedOriginalPaper(customerDTO.getOriginalPaper())
                    .setSignPlace(customerDTO.getSignPlace())
                    .setSignatureType(String.valueOf(customerDTO.getUseYqq()));

            // supplier信息
            String supplierName = lkgSalesContract.getSupplierName();
            // CustomerEntity supplierEntity = tradeRemoteService.getLdcSupplierByName(supplierName);
            CustomerEntity supplierEntity = supplierMap.computeIfAbsent(supplierName, name -> tradeRemoteService.getLdcSupplierByName(name));
            if (null == supplierEntity) {
                log.error("{}导入失败,{}主体为空)", lkgSalesContract.getContractNumber(), supplierName);
                continue;
            }
            prepareContractEntity
                    .setSupplierId(supplierEntity.getId())
                    .setSupplierName(supplierName);

            // 商品信息
            // SkuEntity goodsInfo = getGoodsInfo(lkgSalesContract.getCommodityFullName());
            SkuEntity goodsInfo = goodsMap.computeIfAbsent(lkgSalesContract.getCommodityFullName(), this::getGoodsInfo);
            if (null == goodsInfo) {
                log.error("{}导入失败,null == goodsInfo)", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setGoodsId(goodsInfo.getId())
                    .setGoodsName(goodsInfo.getFullName())
                    .setCommodityName(goodsInfo.getNickName())
                    .setGoodsCategoryId(goodsInfo.getCategoryId())
                    .setCategory1(goodsInfo.getCategory1())
                    .setCategory2(goodsInfo.getCategory2())
                    .setCategory3(goodsInfo.getCategory3())
                    .setGoodsCategoryId(goodsInfo.getCategoryId())
                    .setGoodsPackageId(goodsInfo.getPackageId())
                    .setGoodsSpecId(goodsInfo.getSpecId())
                    .setFutureCode(lkgSalesContract.getFutureCode());

            // paymentTerm
            // PayConditionEntity payConditionEntity = tradeRemoteService.initPaymentTermEntity(lkgSalesContract.getPaymentTermCode(), ContractSalesTypeEnum.SALES.getValue());
            PayConditionEntity payConditionEntity = payConditionMap.computeIfAbsent(lkgSalesContract.getPaymentTermCode() + "_" + ContractSalesTypeEnum.SALES.getValue(),
                    key -> tradeRemoteService.initPaymentTermEntity(lkgSalesContract.getPaymentTermCode(), ContractSalesTypeEnum.SALES.getValue()));
            if (null == payConditionEntity) {
                log.error("{}导入失败,null == paymentType)", prepareContractEntity.getContractCode());
                continue;
            }
            prepareContractEntity.setPayConditionId(payConditionEntity.getId());

            // 交货方式（先查缓存）
            String deliveryTypeKey = lkgSalesContract.getModeOfDeliveryCode() + "_" + goodsInfo.getCategoryId();
            DeliveryTypeEntity deliveryTypeEntity = deliveryTypeMap.computeIfAbsent(deliveryTypeKey,
                    key -> tradeRemoteService.getDeliveryTypeByName(lkgSalesContract.getModeOfDeliveryCode(), goodsInfo.getCategoryId()));
            if (deliveryTypeEntity == null) {
                log.error("{} 导入失败, null == 交提货方式", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setDeliveryType(deliveryTypeEntity.getId())
                    .setDeliveryTypeValue(deliveryTypeEntity.getName());

            // 处理重量检验（先查缓存）
            String weightCheckKey = goodsInfo.getCategoryId() + "_" + lkgSalesContract.getWeightCriteria();
            String weightCheck = weightCheckMap.computeIfAbsent(
                    weightCheckKey,
                    key -> String.valueOf(tradeRemoteService.getSystemRuleItemIdByRuleKey(
                            SystemCodeConfigEnum.WEIGHT_CHECK_2, goodsInfo.getCategoryId(), lkgSalesContract.getWeightCriteria()))
            );
            if ("0".equals(weightCheck)) {
                log.error("{}导入失败,null == 重量验收)", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setWeightCheck(weightCheck)
                    .setWeightCheckValue(lkgSalesContract.getWeightCriteria());

            // 处理目的地（先查缓存）
            String destinationKey = goodsInfo.getCategoryId() + "_" + lkgSalesContract.getToHarborCode();
            SystemRuleItemEntity destination = destinationMap.computeIfAbsent(
                    destinationKey,
                    key -> tradeRemoteService.findSystemEntityByLkgCode(
                            goodsInfo.getCategoryId(), SystemCodeConfigEnum.DESTINATION.getRuleCode(), lkgSalesContract.getToHarborCode())
            );
            if (destination == null) {
                log.error("{}导入失败,null == 目的地)", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setDestination(String.valueOf(destination.getId()))
                    .setDestinationValue(destination.getRuleValue());

            // 处理发货库点（先查缓存）
            WarehouseEntity warehouseEntity = warehouseMap.computeIfAbsent(
                    lkgSalesContract.getWarehouseName(),
                    key -> tradeRemoteService.getWarehouseByName(lkgSalesContract.getWarehouseName())
            );
            if (null == warehouseEntity) {
                log.error("{}导入失败,null == 发货库点)", lkgSalesContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setShipWarehouseId(warehouseEntity.getId())
                    .setShipWarehouseValue(warehouseEntity.getName());

            // 赊销天数
            prepareContractEntity.setPaymentType(
                    StringUtils.isNotBlank(lkgSalesContract.getCreditDays()) && Integer.parseInt(lkgSalesContract.getCreditDays()) > 0
                            ? PaymentTypeEnum.CREDIT.getType()
                            : PaymentTypeEnum.IMPREST.getType());

            // 生成缓存 Key（确保唯一性）
            String supplierAccountKey = prepareContractEntity.getSupplierId() + "_" +
                    prepareContractEntity.getCompanyId() + "_" +
                    prepareContractEntity.getGoodsCategoryId() + "_" +
                    prepareContractEntity.getDeliveryFactoryCode() + "_" +
                    ContractSalesTypeEnum.SALES.getValue();

            // 先查缓存，避免重复查询
            CustomerBankDTO supplierAccount = supplierAccountMap.computeIfAbsent(
                    supplierAccountKey,
                    key -> tradeRemoteService.getSupplierAccount(
                            prepareContractEntity.getSupplierId(),
                            prepareContractEntity.getCompanyId(),
                            prepareContractEntity.getGoodsCategoryId(),
                            prepareContractEntity.getDeliveryFactoryCode(),
                            ContractSalesTypeEnum.SALES.getValue()
                    )
            );

            if (supplierAccount == null) {
                log.error("{}导入失败,null == supplierAccount)", lkgSalesContract.getContractNumber());
                continue;
            }

            prepareContractEntity
                    .setSupplierAccount(supplierAccount.getBankAccountNo())
                    .setSupplierAccountId(supplierAccount.getId());

            // 发票类型
            prepareContractEntity
                    .setInvoiceType(Integer.valueOf(lkgSalesContract.getInvoiceType()))
                    .setInvoiceTypeValue(getInvoiceTypeValue(lkgSalesContract.getInvoiceType()))
                    .setTaxRate(goodsInfo.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP));

            // 合同类型
            Integer contractType = getContractType(lkgSalesContract.getModeOfPrice(), lkgSalesContract.getIsBasic(), lkgSalesContract.getIsDelay());

            // 业务线
            prepareContractEntity.setBuCode(lkgSalesContract.getBuCode());

            // 企标文件
            if (StringUtil.isNotEmpty(lkgSalesContract.getStandardFileNo())) {
                Integer fileId = tradeRemoteService.getSystemRuleItemIdByRuleKey(SystemCodeConfigEnum.STANDARD_FILE_CONFIG, goodsInfo.getCategoryId(), lkgSalesContract.getStandardFileNo());
                if (null == fileId || fileId == 0) {
                    log.error("{}导入失败,null == 企标文件)", lkgSalesContract.getContractNumber());
                    continue;
                }
                prepareContractEntity
                        .setStandardType("企标")
                        .setStandardFileId(fileId)
                        .setStandardRemark(lkgSalesContract.getStandardRemark());
            }

            prepareContractEntity
                    .setUuid(IdUtil.simpleUUID())
                    .setWeightUnit(UnitEnum.TON.name())
                    .setCurrencyType("CNY")
                    .setCreditDays(parseInteger(lkgSalesContract.getCreditDays()))
                    .setSalesType(ContractSalesTypeEnum.SALES.getValue())
                    .setLinkinageCode(lkgSalesContract.getContractNumber())
                    .setContractCode(lkgSalesContract.getContractNumber())
                    .setParentId(0)
                    .setContractType(contractType)
                    .setContractSource(ContractActionEnum.NEW.getActionValue())
                    .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                    .setDomainCode(lkgSalesContract.getFuturesContractNo())
                    .setContractNum(BigDecimalUtil.initBigDecimal(lkgSalesContract.getCount()))
                    .setOrderNum(BigDecimalUtil.initBigDecimal(lkgSalesContract.getCount()))
                    .setTotalDeliveryNum(BigDecimal.ZERO)
                    .setTotalPriceNum(contractType == ContractTypeEnum.YI_KOU_JIA.getValue() ? BigDecimalUtil.initBigDecimal(lkgSalesContract.getCount()) : BigDecimal.ZERO)
                    .setTotalTransferNum(BigDecimal.ZERO)
                    .setTotalModifyNum(BigDecimal.ZERO)
                    .setTotalBuyBackNum(BigDecimal.ZERO)
                    .setPriceEndType(parseInteger(lkgSalesContract.getEndFixPriceType()))
                    .setPriceEndTime(lkgSalesContract.getEndFixPriceTime())
                    .setWeightTolerance(parseInteger(lkgSalesContract.getSuperHairAmount()))
                    .setAddedDepositRate(parseInteger(lkgSalesContract.getBondRate()))
                    .setInvoicePaymentRate(parseInteger(lkgSalesContract.getInvoicePaymentRate()))
                    .setTradeType(getTradeType(lkgSalesContract.getTransactionType()))
                    .setSignDate(lkgSalesContract.getSignTime())
                    .setDeliveryStartTime(lkgSalesContract.getBeginDeliveryTime())
                    .setDeliveryEndTime(lkgSalesContract.getEndDeliveryTime())
                    .setCreatedAt(lkgSalesContract.getCreateTime())
                    .setOem(parseInteger(lkgSalesContract.getIsSellOfOem()))
                    .setMemo(lkgSalesContract.getBillMemo());

            // 所属商务和制单人
            if (StringUtil.isNotEmpty(lkgSalesContract.getTrader())) {
                EmployEntity employEntity = employMap.computeIfAbsent(lkgSalesContract.getTrader(), key -> tradeRemoteService.getEmployEntity(key));
                if (null == employEntity) {
                    log.error("{}导入失败,null == 所属商务)", lkgSalesContract.getContractNumber());
                    continue;
                }
                prepareContractEntity.setOwnerId(employEntity.getId());
            }
            if (StringUtil.isNotEmpty(lkgSalesContract.getCreateUser())) {
                EmployEntity employEntity = employMap.computeIfAbsent(lkgSalesContract.getCreateUser(), key -> tradeRemoteService.getEmployEntity(key));
                if (null == employEntity) {
                    log.error("{}导入失败,null == 制单人)", lkgSalesContract.getContractNumber());
                    continue;
                }
                prepareContractEntity.setCreatedBy(employEntity.getId())
                        .setUpdatedBy(employEntity.getId());
            }

            // 转月次数
            prepareContractEntity.setTransferredTimes(parseInteger(lkgSalesContract.getTransferredTimes()))
                    .setAbleTransferTimes(parseInteger(lkgSalesContract.getAbleTransferTimes()))
                    .setReversedPriceTimes(parseInteger(lkgSalesContract.getReversedPriceTimes()))
                    .setAbleReversePriceTimes(parseInteger(lkgSalesContract.getAbleReversePriceTimes()));

            prepareContractEntity.setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());

            saveList.add(prepareContractEntity);

            // prepareContractDao.save(prepareContractEntity);
            lkgSalesContract.setIsSynced(LkgImportContractSyncedStatusEnum.SYNCED.getValue());
            lkgSalesContractDao.updateById(lkgSalesContract);

            successNum++;
        }

        // 批量保存
        batchSaveEntities(saveList);

        customerMap.clear();
        supplierMap.clear();

        log.info("SyncPrepareContract:Sales success {}", successNum);
    }

    /**
     * 批量保存
     *
     * @param saveList 保存的数据
     */
    private void batchSaveEntities(List<PrepareContractEntity> saveList) {
        for (int i = 0; i < saveList.size(); i += BATCH_SIZE) {
            List<PrepareContractEntity> batchList = saveList.subList(i, Math.min(i + BATCH_SIZE, saveList.size()));
            batchList.forEach(prepareContractDao::save);
        }
    }

    /**
     * 获取发票类型value
     *
     * @param invoiceType 发票类型
     * @return
     */
    private String getInvoiceTypeValue(String invoiceType) {
        switch (invoiceType) {
            case "1":
                return "增值税普通发票,电子";
            case "2":
                return "增值税普通发票,纸质";
            case "3":
                return "增值税专用发票,电子";
            case "4":
                return "增值税专用发票,纸质";
            case "5":
                return "增值税普通发票,全电";
            case "6":
                return "增值税专用发票,全电";
            default:
                return "";
        }
    }

    /**
     * 同步采购合同数据
     *
     * @param lkgPurchaseContractList 采购合同数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncPurchasePrepareContract(List<LkgPurchaseContractEntity> lkgPurchaseContractList) {
        int successNum = 0;

        // 初始化数据
        initBasicImport();

        List<PrepareContractEntity> saveList = new ArrayList<>();

        for (LkgPurchaseContractEntity lkgPurchaseContract : lkgPurchaseContractList) {

            PrepareContractEntity prepareContractEntity = BeanConvertUtils.map(PrepareContractEntity.class, lkgPurchaseContract);

            // 账套信息
            // SiteEntity siteEntity = tradeRemoteService.getSiteEntityByName(lkgPurchaseContract.getSiteName());
            String siteName = lkgPurchaseContract.getSiteName();
            SiteEntity siteEntity = siteMap.computeIfAbsent(siteName, name -> tradeRemoteService.getSiteEntityByName(siteName));
            if (siteEntity == null) {
                log.error("{}导入失败,账套无效)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setSiteCode(siteEntity.getCode())
                    .setSiteName(siteEntity.getName())
                    .setBelongCustomerId(siteEntity.getBelongCustomerId())
                    .setDeliveryFactoryCode(siteEntity.getFactoryCode())
                    .setDeliveryFactory(siteEntity.getFactoryCode())
                    .setDeliveryFactoryName(factoryMap.get(siteEntity.getFactoryCode()))
                    .setCompanyId(siteEntity.getCompanyId())
                    .setCompanyName(companyMap.get(siteEntity.getCompanyId()));

            // 供应商信息
            // CustomerEntity customerDTO = tradeRemoteService.getCustomerDTO(lkgPurchaseContract.getSupplierCode());
            CustomerEntity customerDTO = supplierMap.computeIfAbsent(lkgPurchaseContract.getSupplierCode(), code -> tradeRemoteService.getCustomerDTO(code));
            if (null == customerDTO) {
                log.error("{}导入失败,null == customerDTO)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setSupplierId(customerDTO.getId())
                    .setSupplierName(customerDTO.getName())
                    .setNeedOriginalPaper(customerDTO.getOriginalPaper())
                    .setOriginalPaperStatus(1)
                    .setLdcFrame(customerDTO.getIsLdc())
                    .setSignatureType(String.valueOf(customerDTO.getUseYqq()));

            // 客户信息
            // CustomerEntity customerEntity = tradeRemoteService.getLdcSupplierByName(lkgPurchaseContract.getDemander());
            CustomerEntity customerEntity = customerMap.computeIfAbsent(lkgPurchaseContract.getDemander(), name -> tradeRemoteService.getLdcSupplierByName(name));
            if (null == customerEntity) {
                log.error("{}导入失败,{}主体为空)", lkgPurchaseContract.getContractNumber(), lkgPurchaseContract.getDemander());
                continue;
            }
            prepareContractEntity
                    .setOriginalCustomerId(0)
                    .setCustomerId(customerEntity.getId())
                    .setCustomerName(customerEntity.getName())
                    .setCustomerCode(customerEntity.getLinkageCustomerCode());

            // 商品信息
            // SkuEntity goodsInfo = getGoodsInfo(lkgPurchaseContract.getCommodityFullName());
            SkuEntity goodsInfo = goodsMap.computeIfAbsent(lkgPurchaseContract.getCommodityFullName(), this::getGoodsInfo);
            if (null == goodsInfo) {
                log.error("{}导入失败,null == goodsInfo)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setGoodsId(goodsInfo.getId())
                    .setGoodsName(goodsInfo.getFullName())
                    .setCommodityName(goodsInfo.getNickName())
                    .setGoodsCategoryId(goodsInfo.getCategoryId())
                    .setCategory1(goodsInfo.getCategory1())
                    .setCategory2(goodsInfo.getCategory2())
                    .setCategory3(goodsInfo.getCategory3())
                    .setGoodsCategoryId(goodsInfo.getCategoryId())
                    .setGoodsPackageId(goodsInfo.getPackageId())
                    .setGoodsSpecId(goodsInfo.getSpecId())
                    .setFutureCode(lkgPurchaseContract.getFutureCode());

            // 处理银行卡信息（先查缓存）
            String bankKey = lkgPurchaseContract.getBankAccountNo() + "_" + goodsInfo.getCategoryId();
            CustomerBankEntity customerBankEntity = bankInfoMap.computeIfAbsent(
                    bankKey,
                    key -> tradeRemoteService.queryBankByBankAccountNo(lkgPurchaseContract.getBankAccountNo(), goodsInfo.getCategoryId())
            );

            if (customerBankEntity == null) {
                log.error("{}导入失败,null == customerBankEntity)", lkgPurchaseContract.getContractNumber());
                continue;
            }

            prepareContractEntity.setSupplierAccountId(customerBankEntity.getId())
                    .setSupplierAccount(lkgPurchaseContract.getBankAccountNo());

            // paymentTerm
            // PayConditionEntity payConditionEntity = tradeRemoteService.initPaymentTermEntity(lkgPurchaseContract.getPaymentTermCode(), ContractSalesTypeEnum.PURCHASE.getValue());
            PayConditionEntity payConditionEntity = payConditionMap.computeIfAbsent(lkgPurchaseContract.getPaymentTermCode() + "_" + ContractSalesTypeEnum.PURCHASE.getValue(),
                    key -> tradeRemoteService.initPaymentTermEntity(lkgPurchaseContract.getPaymentTermCode(), ContractSalesTypeEnum.PURCHASE.getValue()));
            if (null == payConditionEntity) {
                log.error("{}导入失败,null == paymentType)", prepareContractEntity.getContractCode());
                continue;
            }
            prepareContractEntity.setPayConditionId(payConditionEntity.getId());

            // 交货方式（先查缓存）
            String deliveryTypeKey = lkgPurchaseContract.getModeOfDeliveryCode() + "_" + goodsInfo.getCategoryId();
            DeliveryTypeEntity deliveryTypeEntity = deliveryTypeMap.computeIfAbsent(deliveryTypeKey,
                    key -> tradeRemoteService.getDeliveryTypeByName(lkgPurchaseContract.getModeOfDeliveryCode(), goodsInfo.getCategoryId()));
            if (deliveryTypeEntity == null) {
                log.error("{} 导入失败, null == 交提货方式", lkgPurchaseContract.getContractNumber());
                continue;
            }

            prepareContractEntity
                    .setDeliveryType(deliveryTypeEntity.getId())
                    .setDeliveryTypeValue(deliveryTypeEntity.getName());

            // 处理重量检验
            String weightCheckKey = goodsInfo.getCategoryId() + "_" + lkgPurchaseContract.getWeightCriteria();
            String weightCheck = weightCheckMap.computeIfAbsent(
                    weightCheckKey,
                    key -> String.valueOf(tradeRemoteService.getSystemRuleItemIdByRuleKey(
                            SystemCodeConfigEnum.WEIGHT_CHECK_2, goodsInfo.getCategoryId(), lkgPurchaseContract.getWeightCriteria()))
            );
            if ("0".equals(weightCheck)) {
                log.error("{}导入失败,null == 重量验收)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setWeightCheck(weightCheck)
                    .setWeightCheckValue(lkgPurchaseContract.getWeightCriteria());

            // 处理目的地
            String destinationKey = goodsInfo.getCategoryId() + "_" + lkgPurchaseContract.getToHarborCode();
            SystemRuleItemEntity destination = destinationMap.computeIfAbsent(
                    destinationKey,
                    key -> tradeRemoteService.findSystemEntityByLkgCode(
                            goodsInfo.getCategoryId(), SystemCodeConfigEnum.DESTINATION.getRuleCode(), lkgPurchaseContract.getToHarborCode())
            );
            if (destination == null) {
                log.error("{}导入失败,null == 目的地)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setDestination(String.valueOf(destination.getId()))
                    .setDestinationValue(destination.getRuleValue());

            // 处理发货库点
            WarehouseEntity warehouseEntity = warehouseMap.computeIfAbsent(
                    lkgPurchaseContract.getWarehouseName(),
                    key -> tradeRemoteService.getWarehouseByName(lkgPurchaseContract.getWarehouseName())
            );
            if (null == warehouseEntity) {
                log.error("{}导入失败,null == 发货库点)", lkgPurchaseContract.getContractNumber());
                continue;
            }
            prepareContractEntity
                    .setShipWarehouseId(warehouseEntity.getId())
                    .setShipWarehouseValue(warehouseEntity.getName());

            // 赊销天数
            prepareContractEntity.setPaymentType(
                    StringUtils.isNotBlank(lkgPurchaseContract.getCreditDays()) && Integer.parseInt(lkgPurchaseContract.getCreditDays()) > 0
                            ? PaymentTypeEnum.CREDIT.getType()
                            : PaymentTypeEnum.IMPREST.getType());

            //发票类型
            prepareContractEntity.setInvoiceType(Integer.valueOf(lkgPurchaseContract.getInvoiceType()))
                    .setInvoiceTypeValue(getInvoiceTypeValue(lkgPurchaseContract.getInvoiceType()))
                    .setTaxRate(goodsInfo.getTaxRate().divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP));

            // 合同类型
            Integer contractType = getContractType(lkgPurchaseContract.getModeOfPrice(), lkgPurchaseContract.getIsBasic(), lkgPurchaseContract.getIsDelay());

            // 业务线
            prepareContractEntity.setBuCode(lkgPurchaseContract.getBuCode());

            // 企标文件
            if (StringUtil.isNotEmpty(lkgPurchaseContract.getStandardFileNo())) {
                Integer fileId = tradeRemoteService.getSystemRuleItemIdByRuleKey(SystemCodeConfigEnum.STANDARD_FILE_CONFIG, goodsInfo.getCategoryId(), lkgPurchaseContract.getStandardFileNo());
                if (null == fileId || fileId == 0) {
                    log.error("{}导入失败,null == 企标文件)", lkgPurchaseContract.getContractNumber());
                    continue;
                }
                prepareContractEntity
                        .setStandardType("企标")
                        .setStandardFileId(fileId)
                        .setStandardRemark(lkgPurchaseContract.getStandardRemark());
            }

            prepareContractEntity
                    .setUuid(IdUtil.simpleUUID())
                    .setWeightUnit(UnitEnum.TON.name())
                    .setCurrencyType("CNY")
                    .setCreditDays(Integer.valueOf(lkgPurchaseContract.getCreditDays()))
                    .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setLinkinageCode(lkgPurchaseContract.getContractNumber())
                    .setContractCode(lkgPurchaseContract.getContractNumber())
                    .setParentId(0)
                    .setContractType(contractType)
                    .setContractSource(ContractActionEnum.NEW.getActionValue())
                    .setStatus(ContractStatusEnum.EFFECTIVE.getValue())
                    .setDomainCode(lkgPurchaseContract.getFuturesContractNo())
                    .setContractNum(BigDecimalUtil.initBigDecimal(lkgPurchaseContract.getCount()))
                    .setOrderNum(BigDecimalUtil.initBigDecimal(lkgPurchaseContract.getCount()))
                    .setTotalDeliveryNum(BigDecimal.ZERO)
                    .setTotalPriceNum(contractType == ContractTypeEnum.YI_KOU_JIA.getValue() ? BigDecimalUtil.initBigDecimal(lkgPurchaseContract.getCount()) : BigDecimal.ZERO)
                    .setTotalTransferNum(BigDecimal.ZERO)
                    .setTotalModifyNum(BigDecimal.ZERO)
                    .setTotalBuyBackNum(BigDecimal.ZERO)
                    .setOem(Integer.valueOf(lkgPurchaseContract.getIsSellOfOem()))
                    .setPriceEndType(Integer.valueOf(lkgPurchaseContract.getEndFixPriceType()))
                    .setPriceEndTime(lkgPurchaseContract.getEndFixPriceTime())
                    .setAddedDepositRate(Integer.valueOf(lkgPurchaseContract.getBondRate()))
                    .setTradeType(getTradeType(lkgPurchaseContract.getTransactionType()))
                    .setSignDate(lkgPurchaseContract.getSignTime())
                    .setDeliveryStartTime(lkgPurchaseContract.getBeginDeliveryTime())
                    .setDeliveryEndTime(lkgPurchaseContract.getEndDeliveryTime())
                    .setCreatedAt(lkgPurchaseContract.getCreateTime())
                    .setWeightTolerance(Integer.valueOf(lkgPurchaseContract.getOverchargeRate()))
                    .setMemo(lkgPurchaseContract.getRemarks());

            // 所属商务和制单人
            if (StringUtil.isNotEmpty(lkgPurchaseContract.getTrader())) {
                EmployEntity employEntity = employMap.computeIfAbsent(lkgPurchaseContract.getTrader(), key -> tradeRemoteService.getEmployEntity(key));
                if (null == employEntity) {
                    log.error("{}导入失败,null == 所属商务)", lkgPurchaseContract.getContractNumber());
                    continue;
                }
                prepareContractEntity.setOwnerId(employEntity.getId());
            }
            if (StringUtil.isNotEmpty(lkgPurchaseContract.getCreateUser())) {
                EmployEntity employEntity = employMap.computeIfAbsent(lkgPurchaseContract.getCreateUser(), key -> tradeRemoteService.getEmployEntity(key));
                if (null == employEntity) {
                    log.error("{}导入失败,null == 制单人)", lkgPurchaseContract.getContractNumber());
                    continue;
                }
                prepareContractEntity.setCreatedBy(employEntity.getId())
                        .setUpdatedBy(employEntity.getId());
            }

            prepareContractEntity.setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
            // prepareContractDao.save(prepareContractEntity);
            saveList.add(prepareContractEntity);

            lkgPurchaseContract.setIsSynced(LkgImportContractSyncedStatusEnum.SYNCED.getValue());
            lkgPurchaseContractDao.updateById(lkgPurchaseContract);

            successNum++;
        }

        // 批量保存
        batchSaveEntities(saveList);

        customerMap.clear();
        supplierMap.clear();

        log.info("SyncPrepareContract:Purchase success {}", successNum);
    }

    /**
     * 同步合同数据
     *
     * @param prepareContractList
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncContract(List<PrepareContractEntity> prepareContractList) {
        String batch = "BATCH-" + DateTimeUtil.formatDateString();

        for (PrepareContractEntity prepareContractEntity : prepareContractList) {
            ContractEntity contractEntity = BeanConvertUtils.map(ContractEntity.class, prepareContractEntity);

            if (contractEntity.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                contractEntity.setTotalPriceNum(contractEntity.getContractNum());
            }
            // 签订总金额
            contractEntity.setOrderAmount(contractEntity.getTotalAmount());

            // 期货合约
            String domainCode = contractEntity.getDomainCode();
            if (StringUtils.isNotBlank(domainCode)) {
                StringBuilder newCode = new StringBuilder();
                for (int i = 0; i < domainCode.length(); i++) {
                    if (domainCode.charAt(i) >= 48 && domainCode.charAt(i) <= 57) {
                        newCode.append(domainCode.charAt(i));
                    }
                }
                contractEntity.setDomainCode(newCode.toString());
            }

            // 初始化合同点转次数
            processContractEntity(contractEntity, prepareContractEntity, domainCode);

            // 袋皮扣重
            contractEntity.setPackageWeight(prepareContractEntity.getPackageWeight().equals("0") ? "" : prepareContractEntity.getPackageWeight());

            // 同步合同数据
            contractEntity.setId(null)
                    .setRepeatContractCode(contractEntity.getContractCode())
                    .setContractNature(ContractNatureEnum.SPOT_TRADE.getValue())
                    .setIsSoybean2(0)
                    .setCreateBatch(batch)
                    .setCreateSource(SystemEnum.LKG.getValue());

            ContractPriceEntity priceBaseEntity = BeanConvertUtils.map(ContractPriceEntity.class, prepareContractEntity);
            priceBaseEntity.setForwardPrice(prepareContractEntity.getFuturesPrice());
            contractEntity.setContractPriceEntity(priceBaseEntity);

            // 保存contract
            boolean result = tradeRemoteService.prePareToContract(contractEntity);
            if (result) {
                // 保存lkg_contract
                LkgContractEntity lkgContractEntity = new LkgContractEntity();

                BeanUtils.copyProperties(contractEntity, lkgContractEntity);

                // siteId
                // String siteId = tradeRemoteService.getSiteId(contractEntity.getBelongCustomerId());
                String siteId = tradeRemoteService.getSiteIdBySiteCode(contractEntity.getSiteCode());
                lkgContractEntity.setId(null)
                        .setSiteId(siteId);
                lkgContractDao.save(lkgContractEntity);
            }

            //更新数据为已同步
            prepareContractEntity.setIsSynced(LkgImportContractSyncedStatusEnum.SYNCED.getValue());
            prepareContractDao.updateById(prepareContractEntity);
        }
    }

    /**
     * 初始化转月和反点价次数
     *
     * @param contractEntity        合同实体
     * @param prepareContractEntity 预合同实体
     * @param domainCode            期货合约
     */
    public void processContractEntity(ContractEntity contractEntity, PrepareContractEntity prepareContractEntity, String domainCode) {
        Integer ableTransferTimes = Optional.ofNullable(prepareContractEntity.getAbleTransferTimes()).orElse(0);
        Integer transferredTimes = Optional.ofNullable(prepareContractEntity.getTransferredTimes()).orElse(0);
        Integer ableReversePriceTimes = Optional.ofNullable(prepareContractEntity.getAbleReversePriceTimes()).orElse(0);
        Integer reversedPriceTimes = Optional.ofNullable(prepareContractEntity.getReversedPriceTimes()).orElse(0);

        // 如果有可转让次数或可反向定价次数，则直接赋值
        if (ableTransferTimes > 0 || ableReversePriceTimes > 0) {
            contractEntity
                    .setAbleTransferTimes(ableTransferTimes)
                    .setTransferredTimes(transferredTimes)
                    .setTotalTransferTimes(ableTransferTimes + transferredTimes)
                    .setAbleReversePriceTimes(ableReversePriceTimes)
                    .setReversedPriceTimes(reversedPriceTimes)
                    .setTotalReversePriceTimes(ableReversePriceTimes + reversedPriceTimes);
            return; // 直接返回，避免不必要的嵌套
        }

        // 否则，如果是销售类型，需要从远程服务获取数据
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
            ContractTransferCountDTO contractTransferNum = tradeRemoteService.getContractTransferNum(
                    contractEntity.getCustomerId(), contractEntity.getGoodsCategoryId(),
                    domainCode, contractEntity.getDeliveryEndTime(), contractEntity.getCategory2()
            );

            BeanUtils.copyProperties(contractTransferNum, contractEntity);

            contractEntity
                    .setAbleTransferTimes(Math.max(0, contractEntity.getTotalTransferTimes() - transferredTimes))
                    .setTransferredTimes(transferredTimes)
                    .setAbleReversePriceTimes(Math.max(0, contractEntity.getTotalReversePriceTimes() - reversedPriceTimes))
                    .setReversedPriceTimes(reversedPriceTimes);
        }
    }


    /**
     * 获取lkg交易类型
     *
     * @param tradeType
     * @return
     */
    public Integer getTradeType(String tradeType) {
        if ("Split".equals(tradeType)) {
            return ContractTradeTypeEnum.SPLIT_NORMAL.getValue();
        } else {
            return ContractTradeTypeEnum.NEW.getValue();
        }
    }


    /**
     * 获取商品信息
     *
     * @param goodsFullName 商品全称
     * @return
     */
    public SkuEntity getGoodsInfo(String goodsFullName) {
        return tradeRemoteService.getSkuByFullName(goodsFullName);
    }

    /**
     * 根据延期定价+基差定价 定价方式确定合同类型
     * 合同类型1、一口价 2基差 3、一口价暂定价 4延期定价 5、结构化定价 6、结价合同
     *
     * @param modeOfPriceStr
     * @param isBasicStr
     * @param isDelayStr
     * @return
     */
    public Integer getContractType(String modeOfPriceStr, String isBasicStr, String isDelayStr) {
        int modeOfPrice = Integer.parseInt(modeOfPriceStr);
        int isBasic = Integer.parseInt(isBasicStr);
        int isDelay = Integer.parseInt(isDelayStr);

        if (isBasic == 0 && isDelay == 0) {
            if (modeOfPrice == LkgPriceModeEnum.DEFAULT_DELIVERY_PRICE.getValue()) {
                return ContractTypeEnum.YI_KOU_JIA.getValue();
            }
        } else if (isBasic == 1 && isDelay == 0) {
            if (modeOfPrice == LkgPriceModeEnum.FUTURES_BASIS_PRICE.getValue()) {
                return ContractTypeEnum.JI_CHA.getValue();
            }
        } else if (isBasic == 0 && isDelay == 1) {
            if (modeOfPrice == LkgPriceModeEnum.DELAY_BASIS_PRICE.getValue()) {
                return ContractTypeEnum.ZAN_DING_JIA.getValue();
            }
        }
        return ContractTypeEnum.YI_KOU_JIA.getValue();
    }

    /**
     * 字符串转数字
     *
     * @param s
     * @return
     */
    private Integer parseInteger(String s) {
        if (StringUtils.isNotBlank(s)) {
            return Integer.valueOf(s);
        }
        return null;
    }

}
