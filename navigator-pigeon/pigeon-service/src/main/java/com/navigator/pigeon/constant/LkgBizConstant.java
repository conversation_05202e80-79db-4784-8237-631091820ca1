package com.navigator.pigeon.constant;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * lkg业务常量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-03
 */
public interface LkgBizConstant {

    /**
     * 代签单位
     */
    String DEFAULT_SIGNING_UNIT = "11";

    String DEFAULT_EXE_OFFICER = "NAVSYSTEM";
    String DEFAULT_CHECK_USER = "";
    String DEFAULT_TRADER = "";

    String DEFAULT_LDC = "11";

    String DEFAULT_FL = "05";

    Integer DEFAULT_FL_ID = 2;


    /**
     * 带皮扣重文本判断，不扣皮传0，否则传1
     */
    String PACKAGE_WEIGHT_NAME = "不扣皮";
    /**
     * 质量检验文本
     */
    String QUALITY_CHECK_INFO = "详见航海家合同文本";

    /**
     * 是否需提供发票 1是，0否
     */
    Integer IS_PROVIDE_INVOICE = 1;

    /**
     * 方向
     */
    String DEFAULT_DIRECTION = "";

    /**
     * 付定金日期
     */
    Date DEFAULT_PAY_DEPOSIT_TIME = new Date();

    /**
     * 付全款日期
     */
    Date DEFAULT_PAY_FULL_TIME = new Date();

    /**
     * 补充条款
     */
    String DEFAULT_SUPPLE_PROVISIONS = "";

    /**
     * 运费
     */
    BigDecimal DEFAULT_FREIGHT_PROV_PRICE = BigDecimal.ZERO;

    /**
     * 变更单号
     */
    String DEFAULT_CHANGE_ORDER_NO = "";

    Integer DEFAULT_DELIVERY_TYPE = 1101;

    String DEFAULT_OIL_PACKAGE = "脱胶毛豆油散装";

    String PAY_CONDITION_CODE = "Dummy Payment Term";

    Integer MAX_PROCESS_TIME = 240000;

}
