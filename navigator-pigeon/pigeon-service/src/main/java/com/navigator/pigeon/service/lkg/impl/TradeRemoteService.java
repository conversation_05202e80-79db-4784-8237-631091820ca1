package com.navigator.pigeon.service.lkg.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.*;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.*;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.dto.CustomerAllMessageDTO;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerBankEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferCountDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import com.navigator.trade.pojo.vo.TTDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class TradeRemoteService {
    @Resource
    ContractFacade contractFacade;
    @Resource
    TradeTicketFacade tradeTicketFacade;
    @Resource
    ContractPriceFacade contractPriceFacade;
    @Resource
    CustomerProtocolFacade customerProtocolFacade;
    @Resource
    CustomerFacade customerFacade;
    @Resource
    CustomerBankFacade customerBankFacade;
    @Resource
    WarehouseFacade warehouseFacade;
    @Resource
    SystemRuleFacade systemRuleFacade;
    @Resource
    DeliveryTypeFacade deliveryTypeFacade;
    @Resource
    EmployFacade employFacade;
    @Resource
    private PayConditionFacade payConditionFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private SiteFacade siteFacade;
    @Resource
    private SkuFacade skuFacade;
    @Resource
    private FactoryFacade factoryFacade;

    public ContractDetailVO getTradeContractDetail(Integer contractId) {
        log.info("===================TradeRemoteService.getTradeContractDetail===========================");
        log.info("contractId=" + contractId.toString());

        ContractDetailVO contractDetailVO = null;

        try {
            Result rtn = contractFacade.getBasicContractByContractId(contractId.toString());
            if (rtn.isSuccess()) {
                contractDetailVO = JSON.parseObject(JSON.toJSONString(rtn.getData()), ContractDetailVO.class);
            }
        } catch (Exception e) {
            log.error("getTradeContractDetail error", e);
        }

        log.info(JSON.toJSONString(contractDetailVO));

        return contractDetailVO;


    }

    public boolean prePareToContract(ContractEntity contractEntity) {
        log.info("===================TradeRemoteService.getTradeContractDetail===========================");
        log.info(JSON.toJSONString(contractEntity));
        boolean b = false;
        try {
            b = contractFacade.prePareToContract(contractEntity);

        } catch (Exception e) {
            log.error("prePareToContract error", e);
        }
        return b;
    }

    public List<ConfirmPriceDTO> getConfirmPriceList(Integer contractId) {
        log.info("===================TradeRemoteService.getConfirmPriceList===========================");
        List<ConfirmPriceDTO> confirmPriceDTOList = new ArrayList<>();

        try {
            log.info("contractId=" + contractId.toString());

            Result rtn = contractFacade.getConfirmPricedList(contractId);
            if (rtn.isSuccess()) {
//                confirmPriceDTOList = JSON.parseObject(JSON.toJSONString(rtn.getData()), confirmPriceDTOList.getClass());
                confirmPriceDTOList = JSON.parseArray(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
            }

            System.out.println(JSON.toJSONString(confirmPriceDTOList));

        } catch (Exception e) {
            log.error("getConfirmPriceList error", e);
        }

        return confirmPriceDTOList;
    }

    public ConfirmPriceDTO getConfirmPriceInfo(Integer contractId, Integer ttPriceId) {
        log.info("===================TradeRemoteService.getConfirmPriceInfo===========================");
        ConfirmPriceDTO confirmPriceDTO = null;

        try {
            log.info("contractId=" + contractId.toString() + ",ttPriceId=" + ttPriceId.toString());

            Result rtn = contractFacade.getConfirmPricedInfo(contractId, ttPriceId);

            if (rtn.isSuccess()) {
                confirmPriceDTO = JSON.parseObject(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
            }

            log.info(JSON.toJSONString(confirmPriceDTO));

        } catch (Exception e) {
            log.error("getConfirmPriceInfo error", e);
        }

        return confirmPriceDTO;
    }

    public TTDetailVO getTradeTTDetail(Integer ttId) {
        log.info("===================TradeRemoteService.getTradeTTDetail===========================");

        TTDetailVO ttDetailVO = null;
        try {
            log.info("ttId=" + ttId.toString());

            Result rtn = tradeTicketFacade.queryTTDetail(ttId);

            if (rtn.isSuccess()) {
                ttDetailVO = JSON.parseObject(JSON.toJSONString(rtn.getData()), TTDetailVO.class);
            }

            log.info(JSON.toJSONString(ttDetailVO));

        } catch (Exception e) {
            log.error("getTradeTTDetail error", e);
        }
        return ttDetailVO;

    }

    public TradeTicketEntity getTradeTicketById(Integer ttId) {
        log.info("===================TradeRemoteService.getTtInfoById:{}===========================", ttId);

        TradeTicketEntity tradeTicketEntity = new TradeTicketEntity();
        try {

            Result rtn = tradeTicketFacade.getTradeTicketById(ttId);

            if (rtn.isSuccess()) {
                tradeTicketEntity = JSON.parseObject(JSON.toJSONString(rtn.getData()), TradeTicketEntity.class);
            }

            log.info(JSON.toJSONString(tradeTicketEntity));

        } catch (Exception e) {
            log.error("getTtInfoById error", e);
        }
        return tradeTicketEntity == null ? new TradeTicketEntity() : tradeTicketEntity;

    }

    public TTAddEntity getTtAddByTtId(Integer ttId) {
        log.info("===================TradeRemoteService.getTtAddByTtId:{}===========================", ttId);

        TTAddEntity ttAddEntity = new TTAddEntity();
        try {

            Result rtn = tradeTicketFacade.getTtAddByTtId(ttId);

            if (rtn.isSuccess()) {
                ttAddEntity = JSON.parseObject(JSON.toJSONString(rtn.getData()), TTAddEntity.class);
            }

            log.info(JSON.toJSONString(ttAddEntity));

        } catch (Exception e) {
            log.error("getTtInfoById error", e);
        }
        return ttAddEntity == null ? new TTAddEntity() : ttAddEntity;

    }

    public TTModifyEntity getTtModifyByTtId(Integer ttId) {
        log.info("===================TradeRemoteService.getTtModifyByTtId:{}===========================", ttId);

        TTModifyEntity ttModify = new TTModifyEntity();
        try {

            Result rtn = tradeTicketFacade.getTtModifyByTtId(ttId);

            if (rtn.isSuccess()) {
                ttModify = JSON.parseObject(JSON.toJSONString(rtn.getData()), TTModifyEntity.class);
            }

            log.info(JSON.toJSONString(ttModify));

        } catch (Exception e) {
            log.error("getTtModifyByTtId error", e);
        }
        return ttModify == null ? new TTModifyEntity() : ttModify;

    }

    public WarehouseEntity getWarehouseByName(String warehouseName) {
        log.info("===================TradeRemoteService.getWarehouseByName===========================");
        WarehouseEntity warehouseEntity = null;
        try {
            Result<WarehouseEntity> warehouseResult = warehouseFacade.getWarehouseByName(warehouseName);
            if (warehouseResult.isSuccess()) {
                warehouseEntity = warehouseResult.getData();
            }
        } catch (Exception e) {
            log.error("getWarehouseByName error", e);
        }
        return warehouseEntity;
    }

    public GoodsDTO getGoodsDTO(Integer goodsId) {
        log.info("===================TradeRemoteService.getGoodsDTO===========================");
        GoodsDTO goodsDTO = new GoodsDTO();
        try {
            log.info("goodsId=" + goodsId.toString());

            SkuEntity skuEntity = skuFacade.getSkuById(goodsId);
            BeanUtil.copyProperties(skuEntity, goodsDTO);

            // 将字符串解析为 JSON 数组
            JSONArray jsonArray = JSON.parseArray(skuEntity.getKeyAttributeValues());

            String specName = "";
            String packageName = "";

            // 遍历 JSON 数组并查找对应的属性值
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                String attributeName = obj.getString("attributeName");

                if ("蛋白含量".equals(attributeName)) {
                    specName = obj.getString("attributeValueName");
                }

                // 只要包含“包装”字样的属性值，就认为是包装属性
                if (obj.getString("attributeName").contains("包装")) {
                    packageName = obj.getString("attributeValueName");
                }
            }

            goodsDTO.setCategoryId(skuEntity.getCategory2())
                    .setPackageName(packageName)
                    .setSpecName(specName);

            // goodsDTO = goodsFacade.findGoodsDetail(goodsId);

            log.info(JSON.toJSONString(goodsDTO));
        } catch (Exception e) {
            log.error("getGoodsDTO error", e);
        }
        return goodsDTO;
    }

    public SkuEntity getSkuByFullName(String fullName) {
        log.info("===================TradeRemoteService.getSkuByFullName===========================");
        SkuEntity sku = null;
        try {
            Result<SkuEntity> skuResult = skuFacade.getSkuByFullName(fullName);
            if (skuResult.isSuccess()) {
                sku = skuResult.getData();

            }
        } catch (Exception e) {
            log.error("getSkuByFullName error", e);
        }
        return sku;
    }

    public ContractPriceEntity getContractPriceEntity(Integer contractId) {
        log.info("===================TradeRemoteService.getContractPriceEntity===========================");

        ContractPriceEntity contractPriceEntity = null;
        try {
            log.info("contractId=" + contractId.toString());

            contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractId);

            log.info(JSON.toJSONString(contractPriceEntity));
        } catch (Exception e) {
            log.error("getContractPriceEntity error", e);
        }

        return contractPriceEntity;

    }

    public CustomerProtocolEntity getCustomerDetailEntity(Integer customerId, Integer categoryId, Integer category2, Integer category3, Integer companyId, Integer salesType) {
        log.info("===================TradeRemoteService.getCustomerDetailEntity===========================");

        CustomerProtocolEntity customerProtocolEntity = null;
        try {
            log.info("contractId=" + customerId.toString());
            CustomerProtocolDTO customerProtocolDTO = new CustomerProtocolDTO();
            customerProtocolDTO.setCustomerId(customerId)
                    .setCategoryId(categoryId)
                    .setCategory2(String.valueOf(category2))
                    .setCategory3(String.valueOf(category3))
                    .setCompanyId(companyId)
                    .setSaleType(salesType)
            ;
            customerProtocolEntity = customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);

            log.info(JSON.toJSONString(customerProtocolEntity));
        } catch (Exception e) {
            log.error("getCustomerDetailEntity error", e);
        }

        return customerProtocolEntity;

    }


    public DeliveryTypeEntity getDeliveryTypeEntity(Integer id) {
        log.info("===================TradeRemoteService.getDeliveryTypeEntity===========================");

        try {
            log.info("id=" + id.toString());

            DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(id);

            log.info(JSON.toJSONString(deliveryTypeEntity));

            return deliveryTypeEntity;
        } catch (Exception e) {
            log.error("getDeliveryTypeEntity error", e);
            return null;
        }
    }

    public DeliveryTypeEntity getDeliveryTypeByName(String deliveryTypeName, Integer categoryId) {
        log.info("===================TradeRemoteService.getDeliveryTypeByName===========================");

        try {
            log.info("交提货方式名称=" + deliveryTypeName);

            List<DeliveryTypeEntity> allDeliveryTypeList = deliveryTypeFacade.getAllDeliveryTypeList(null, categoryId, null, null, null);
            for (DeliveryTypeEntity deliveryTypeEntity : allDeliveryTypeList) {
                if (deliveryTypeName.equals(deliveryTypeEntity.getName())) {
                    return deliveryTypeEntity;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("getDeliveryTypeByName error", e);
            return null;
        }
    }

    public EmployEntity getEmployEntity(Integer id) {
        log.info("===================TradeRemoteService.getEmployEntity===========================");

        EmployEntity employEntity = null;
        try {
            log.info("id=" + id.toString());

            employEntity = employFacade.getEmployById(id);

            log.info(JSON.toJSONString(employEntity));
        } catch (Exception e) {
            log.error("getEmployEntity error", e);
        }

        return employEntity;
    }

    public EmployEntity getEmployEntity(String nickName) {
        log.info("===================TradeRemoteService.getEmployEntity===========================");

        EmployEntity employEntity = null;
        try {
            log.info("nickName=" + nickName);

            employEntity = employFacade.getEmployNickName(nickName, SystemEnum.MAGELLAN.getValue());

            log.info(JSON.toJSONString(employEntity));
        } catch (Exception e) {
            log.error("getEmployEntity error", e);
        }

        return employEntity;
    }

    public CustomerDTO getCustomerById(Integer customerId) {
        log.info("===================TradeRemoteService.getSiteId===========================");
        try {
            log.info("customerId=" + JSON.toJSONString(customerId));

            CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

            log.info(JSON.toJSONString(customerDTO));
            return customerDTO;
        } catch (Exception e) {
            log.error("getCustomerById error", e);
            return null;
        }
    }

    public CustomerBankEntity queryBankByBankAccountNo(String bankAccountNo, Integer categoryId) {
        log.info("===================TradeRemoteService.getSiteId===========================");
        try {

            CustomerBankEntity customerBankEntity = customerBankFacade.queryBankByBankAccountNo(bankAccountNo);

            if (customerBankEntity != null && customerBankEntity.getCategory2() != null && categoryId != null) {
                if (customerBankEntity.getCategory2().contains(String.valueOf(categoryId))) {
                    return customerBankEntity;
                }
            }

            log.info(JSON.toJSONString(customerBankEntity));
            return customerBankEntity;
        } catch (Exception e) {
            log.error("queryBankByBankAccountNo error", e);
            return null;
        }
    }

    public String getSiteId(Integer customerId) {
        log.info("===================TradeRemoteService.getSiteId===========================");
        try {
            log.info("customerId=" + JSON.toJSONString(customerId));

            CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

            log.info(JSON.toJSONString(customerDTO));
            return customerDTO.getSiteId();
        } catch (Exception e) {
            log.error("getSiteId error", e);
            return "29";
        }
    }

    public String getSiteIdBySiteCode(String siteCode) {
        log.info("===================TradeRemoteService.getSiteIdBySiteCode===========================");
        try {
            SiteEntity siteEntity = siteFacade.getSiteByCode(siteCode);
            if (null != siteEntity) {
                return siteEntity.getLkgCode();
            }
        } catch (Exception e) {
            log.error("getSiteIdBySiteCode error", e);
        }
        return "29";
    }

    public SiteEntity getSiteEntityByName(String siteName) {
        log.info("===================TradeRemoteService.getSiteEntityByName===========================");
        if (StringUtil.isEmpty(siteName)) {
            log.error("getSiteEntityByName error: siteName is empty");
            return null;
        }

        try {
            List<SiteEntity> siteEntityList = siteFacade.querySiteList(new SiteQO().setSiteName(siteName));
            if (CollectionUtil.isNotEmpty(siteEntityList)) {
                return siteEntityList.get(0);
            }
            log.warn("No site found with name: {}", siteName);
            return null;
        } catch (Exception e) {
            log.error("Failed to get site by name: " + siteName, e);
            return null;
        }
    }

    public CustomerDTO getCustomerDTO(Integer customerId) {
        log.info("===================TradeRemoteService.getCustomerDTO===========================");
        try {
            log.info("customerId=" + JSON.toJSONString(customerId));

            CustomerDTO customerDTO = customerFacade.getCustomerById(customerId);

            log.info(JSON.toJSONString(customerDTO));
            return customerDTO;
        } catch (Exception e) {
            log.error("getCustomerDTO error", e);
            return null;
        }
    }

    public int getInvoiceType(Integer categoryId, Integer invoiceType, String taxRate) {
        log.info("===================TradeRemoteService.getInvoiceType===========================");
        try {
            log.info("id=" + categoryId.toString());

            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getInvoiceType(categoryId, invoiceType, taxRate);

            log.info(JSON.toJSONString(systemRuleItemEntity));

            return Integer.valueOf(systemRuleItemEntity.getLkgCode());
        } catch (Exception e) {
            log.error("getInvoiceType error", e);
            return 22;
        }
    }

    public int getWeightRuleItemById(Integer weightCheckId) {
        log.info("===================TradeRemoteService.getWeightRuleItemById===========================");
        try {
            log.info("id=" + weightCheckId.toString());

            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(weightCheckId);

            log.info(JSON.toJSONString(systemRuleItemEntity));

            return Integer.valueOf(systemRuleItemEntity.getLkgCode());
        } catch (Exception e) {
            log.error("getWeightRuleItemById error", e);
            return 10;
        }
    }

    public String getToHarborRuleItemById(Integer destinationId) {
        log.info("===================TradeRemoteService.getToHarborRuleItemById===========================");
        try {
            log.info("id=" + destinationId.toString());

            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.getRuleItemById(destinationId);

            log.info(JSON.toJSONString(systemRuleItemEntity));

            return systemRuleItemEntity.getLkgCode();
        } catch (Exception e) {
            log.error("getToHarborRuleItemById error", e);
            return "SBO00001";
        }

    }

    public String findByLkgCode(Integer categoryId, String ruleCode, String lkgCode) {
        log.info("===================TradeRemoteService.getToHarborRuleItemById===========================");
        try {
            log.info("categoryId=" + categoryId);
            log.info("ruleCode=" + ruleCode);
            log.info("lkgCode=" + lkgCode);
            //过滤一下
            if (StringUtil.isEmpty(lkgCode)) {
                return null;
            }

            SystemRuleItemEntity systemRuleItemEntity = systemRuleFacade.findByLkgCode(categoryId, ruleCode, lkgCode);

            log.info(JSON.toJSONString(systemRuleItemEntity));
            if (null == systemRuleItemEntity) {
                return null;
            }
            return systemRuleItemEntity.getId().toString();
        } catch (Exception e) {
            log.error("getToHarborRuleItemById error", e);
            return null;
        }

    }

    public SystemRuleItemEntity findSystemEntityByLkgCode(Integer categoryId, String ruleCode, String lkgCode) {
        log.info("===================TradeRemoteService.findSystemEntityByLkgCode===========================");
        try {
            //过滤一下
            if (StringUtil.isEmpty(lkgCode)) {
                return null;
            }

            return systemRuleFacade.findByLkgCode(categoryId, ruleCode, lkgCode);
        } catch (Exception e) {
            log.error("findSystemEntityByLkgCode error", e);
            return null;
        }
    }

    public ContractTransferCountDTO getContractTransferNum(Integer customerId, Integer categoryId, String domainCode, Date deliveryEndTime, Integer category2) {
        log.info("===================TradeRemoteService.getToHarborRuleItemById===========================");
        ContractTransferCountDTO contractTransferNum = new ContractTransferCountDTO();
        try {
            log.info("customerId=" + customerId);

            contractTransferNum = contractFacade.getContractTransferNum(customerId, categoryId, domainCode, deliveryEndTime, category2);

            log.info(JSON.toJSONString(contractTransferNum));
            return contractTransferNum;
        } catch (Exception e) {
            log.error("getContractTransferNum error", e);
            return contractTransferNum;
        }


    }


    public CustomerEntity getCustomerDTO(String linkageCustomerCode) {
        log.info("===================TradeRemoteService.getCustomerDTO===========================");
        try {
            log.info("linkageCustomerCode=" + JSON.toJSONString(linkageCustomerCode));

            CustomerEntity customerEntity = customerFacade.queryCustomerByLinkageCode(linkageCustomerCode);

            log.info(JSON.toJSONString(customerEntity));
            return customerEntity;
        } catch (Exception e) {
            log.error("getCustomerDTO error", e);
            return null;
        }
    }

    public CustomerEntity getBelongCustomerId(Integer companyId, Integer factoryId) {
        log.info("===================TradeRemoteService.getCustomerDTO===========================");
        try {

            CustomerEntity customerEntity = customerFacade.queryCustomerByCompanyAndFactory(factoryId, companyId);

            log.info(JSON.toJSONString(customerEntity));
            return customerEntity;
        } catch (Exception e) {
            log.error("getCustomerDTO error", e);
            return null;
        }
    }


    public ContractDetailVO getTradeContractDetailByCode(String contractCode) {
        log.info("===================TradeRemoteService.getTradeContractDetailByCode===========================");
        log.info("contractCode=" + contractCode);

        ContractDetailVO contractDetailVO = null;

        try {
            Result rtn = contractFacade.getContractByContractCode(contractCode);
            if (rtn.isSuccess()) {
                contractDetailVO = JSON.parseObject(JSON.toJSONString(rtn.getData()), ContractDetailVO.class);
            }
        } catch (Exception e) {
            log.error("getTradeContractDetailByCode error", e);
        }

        log.info(JSON.toJSONString(contractDetailVO));

        return contractDetailVO;
    }

    public CustomerBankDTO getSupplierAccount(Integer supplierId, Integer companyId, Integer goodsCategoryId, String deliveryFactoryCode, Integer salesType) {

        //todo 没有多品类
        CustomerAllMessageDTO customerAllMessageDTO = new CustomerAllMessageDTO();
        customerAllMessageDTO.setCustomerId(supplierId)
                .setCompanyId(companyId)
                .setCategoryId(goodsCategoryId)
                .setFactoryCode(deliveryFactoryCode)
                .setSalesType(salesType)
                .setCategory2(String.valueOf(goodsCategoryId))
//                .setCategory3()
        ;
        CustomerDTO customerDTO = customerFacade.queryCustomerAllMessage(customerAllMessageDTO);
        if (null != customerDTO) {
            List<CustomerBankDTO> customerBankDTOS = customerDTO.getCustomerBankDTOS();
            if (CollectionUtil.isNotEmpty(customerBankDTOS)) {
                return customerBankDTOS.get(0);
            }
        }
        return null;
    }

    public CustomerEntity getLdcSupplierByName(String supplierName) {
        log.info("===================TradeRemoteService.getLdcSupplierByName===========================");
        try {
            return customerFacade.getLdcSupplierByName(supplierName);
        } catch (Exception e) {
            return null;
        }
    }

    public PayConditionEntity initPaymentTermEntity(String paymentTermCode, Integer salesType) {
        log.info("===================TradeRemoteService.initPaymentTermEntity===========================");
        Result result = payConditionFacade.queryPayCondition(new PayConditionDTO()
                .setPayConditionCode(paymentTermCode)
                .setStatus(1)
                .setSalesType(salesType));
        if (result.isSuccess()) {
            List<PayConditionEntity> payConditionEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), PayConditionEntity.class);
            if (CollectionUtil.isNotEmpty(payConditionEntityList)) {
                return payConditionEntityList.get(0);
            }
        }
        return null;
    }

    private final Map<String, Integer> systemRuleItemMap = new HashMap<>();

    public Integer getSystemRuleItemIdByRuleKey(SystemCodeConfigEnum configEnum, Integer categoryId, String ruleKey) {

        Integer value = systemRuleItemMap.get(ruleKey);

        if (value != null) {
            return value;
        }

        try {
            SystemRuleDTO systemRuleDTO = new SystemRuleDTO()
                    .setCategoryId(categoryId)
                    .setRuleCode(configEnum.getRuleCode());
            SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);

            if (systemRuleVO != null && CollectionUtil.isNotEmpty(systemRuleVO.getSystemRuleItemVOList())) {
                systemRuleVO.getSystemRuleItemVOList().forEach(systemRuleItemVO ->
                        systemRuleItemMap.put(systemRuleItemVO.getRuleItemKey(), systemRuleItemVO.getRuleItemId())
                );
            }
        } catch (Exception e) {
            log.error("Failed to initialize system rule item map", e);
        }

        // 检查systemRuleItemMap中是否包含指定的ruleKey
        return systemRuleItemMap.getOrDefault(ruleKey, 0);
    }
}

