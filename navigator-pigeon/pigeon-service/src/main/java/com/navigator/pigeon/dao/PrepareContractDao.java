package com.navigator.pigeon.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.PrepareContractMapper;
import com.navigator.pigeon.pojo.entity.PrepareContractEntity;

import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/7/6
 * @Time: 16:54
 * @Desception:
 */
@Dao
public class PrepareContractDao extends BaseDaoImpl<PrepareContractMapper, PrepareContractEntity> {


    /**
     * 获取未同步的数据
     * @param prepareContractEntity
     * @return
     */
    public List<PrepareContractEntity> queryList(PrepareContractEntity prepareContractEntity){
        LambdaQueryWrapper<PrepareContractEntity> wrapper = new LambdaQueryWrapper<PrepareContractEntity>()
                .eq(PrepareContractEntity::getIsSynced,prepareContractEntity.getIsSynced());
        return this.baseMapper.selectList(wrapper);
    }
}
