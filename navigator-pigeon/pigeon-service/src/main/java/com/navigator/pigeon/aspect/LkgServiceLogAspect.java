package com.navigator.pigeon.aspect;

import cn.hutool.core.util.StrUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;


@Aspect
@Component
public class LkgServiceLogAspect {

    private final Logger logger = LoggerFactory.getLogger(LkgServiceLogAspect.class);

    /**
     * 定义切入点，匹配 com.navigator.cuckoo.service.convert 包及其子包下的所有方法
     */
    @Pointcut("execution(* com.navigator.pigeon.service.lkg..*(..))")
    public void servicePointcut() {
    }

    /**
     * 环绕通知，用来记录方法的调用日志
     *
     * @param joinPoint 切点
     * @return 切入点原方法的返回值
     * @throws Throwable 抛出异常
     */
    @Around("servicePointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String typeName = StrUtil.subAfter(joinPoint.getSignature().getDeclaringTypeName(), ".", true);

        logger.info("\n {}#{}===============开始请求方法===============", typeName, joinPoint.getSignature().getName());
        logger.info("{}#{} args : {}", typeName, joinPoint.getSignature().getName(), Arrays.toString(joinPoint.getArgs()));

        try {
            Object result = joinPoint.proceed();
            logger.info("{}#{} result : {} ", typeName, joinPoint.getSignature().getName(), result);
            logger.info("\n {}#{}===============结束请求方法===============", typeName, joinPoint.getSignature().getName());
            return result;
        } catch (Throwable throwable) {
            logger.error("{}#{} exception : {}", typeName, joinPoint.getSignature().getName(), throwable.getMessage());
            throw throwable;
        }
    }
}

