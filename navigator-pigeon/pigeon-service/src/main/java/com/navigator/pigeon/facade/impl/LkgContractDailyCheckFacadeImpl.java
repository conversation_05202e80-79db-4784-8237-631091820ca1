package com.navigator.pigeon.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.facade.LkgContractDailyCheckFacade;
import com.navigator.pigeon.pojo.entity.LkgDailyFileEntity;
import com.navigator.pigeon.service.lkg.ILkgContractDailyCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: wang tao
 * @Date: 2022/6/27
 * @Time: 15:56
 * @Desception:
 */
@RestController
@Slf4j
public class LkgContractDailyCheckFacadeImpl implements LkgContractDailyCheckFacade {

    @Resource
    ILkgContractDailyCheckService iLkgContractDailyCheckService;

    @Override
    public Result dailyCheck() {

        log.info("===================LkgContractDailyCheckFacadeImpl.dailyCheck===================");
        iLkgContractDailyCheckService.dailyCheck();
        return null;
    }


    /**
     * 改变文件状态(未启动到获取文件)
     *
     * @param
     * @return
     */
    @Override
    public Result changeToPull() {
        iLkgContractDailyCheckService.changeToPull();
        return null;
    }


    /**
     * 改变文件状态(获取文件到合同数据)
     *
     * @param
     * @return
     */
    @Override
    public Result changeToRecord() {
        iLkgContractDailyCheckService.changeToRecord();
        return null;
    }

    /**
     * 重试机制
     * @return
     */
    @Override
    public Result resync() {
         iLkgContractDailyCheckService.resync();
         return null;
    }

    @Override
    public Result saveDailyFile(LkgDailyFileEntity lkgDailyFileEntity) {
        return Result.success(iLkgContractDailyCheckService.saveDailyFile(lkgDailyFileEntity));
    }
}
