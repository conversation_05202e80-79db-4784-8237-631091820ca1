package com.navigator.pigeon;

import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.pigeon.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade","com.navigator.pigeon.service.lkg.remote"})
@EnableTransactionManagement
@EnableScheduling
public class PigeonNavigatorApplication {
    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }

    public static void main(String[] args) {
        SpringApplication.run(PigeonNavigatorApplication.class, args);
    }
}
