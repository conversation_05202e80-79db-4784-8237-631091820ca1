package com.navigator.pigeon.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgSyncRecordMapper;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;

import java.util.List;

@Dao
public class LkgSyncRecordDao extends BaseDaoImpl<DbiLkgSyncRecordMapper, LkgSyncRecordEntity> {

    public List<LkgSyncRecordEntity> getLkgSyncRecordList(Integer syncRequestId) {
        return this.baseMapper.selectList(Wrappers.<LkgSyncRecordEntity>lambdaQuery()
                .eq(LkgSyncRecordEntity::getRequestId, syncRequestId)
        );
    }

    public List<LkgSyncRecordEntity> queryListByCheckDate(String startCheckTime, String endCheckTime) {
        return this.baseMapper.selectList(Wrappers.<LkgSyncRecordEntity>lambdaQuery()
                .between(StrUtil.isNotBlank(startCheckTime) && StrUtil.isNotBlank(endCheckTime), LkgSyncRecordEntity::getCreatedAt, startCheckTime, endCheckTime)
                .orderByDesc(LkgSyncRecordEntity::getCreatedAt));
    }
}