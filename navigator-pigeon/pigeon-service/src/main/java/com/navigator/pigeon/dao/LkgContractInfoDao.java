package com.navigator.pigeon.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.LkgContractInfoMapper;
import com.navigator.pigeon.pojo.entity.LkgContractInfoEntity;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Dao
public class LkgContractInfoDao extends BaseDaoImpl<LkgContractInfoMapper, LkgContractInfoEntity> {

    public LkgContractInfoEntity getByContractCode(String contractCode) {
        return this.lambdaQuery().eq(LkgContractInfoEntity::getContractNumber, contractCode).one();
    }

    public Object getByContractCodeList(List<String> contractCodeList) {
        return this.lambdaQuery().in(LkgContractInfoEntity::getContractNumber, contractCodeList).list();
    }
}
