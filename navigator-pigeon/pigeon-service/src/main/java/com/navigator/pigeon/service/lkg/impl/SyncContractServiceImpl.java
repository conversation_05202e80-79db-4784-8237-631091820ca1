package com.navigator.pigeon.service.lkg.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.pigeon.dao.LkgContractDao;
import com.navigator.pigeon.dao.LkgQueryRecordDao;
import com.navigator.pigeon.dao.LkgSyncRecordDao;
import com.navigator.pigeon.dao.LkgSyncRequestDao;
import com.navigator.pigeon.pojo.dto.*;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;
import com.navigator.pigeon.pojo.entity.LkgQueryRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import com.navigator.pigeon.pojo.enums.LkgSyncStatusEnum;
import com.navigator.pigeon.service.lkg.ISyncContractService;
import com.navigator.pigeon.service.lkg.remote.LkgRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@RefreshScope
public class SyncContractServiceImpl implements ISyncContractService {

    @Value("${mockLkgInfo.open}")
    private String lkgOpen;

    @Resource
    LkgSyncRequestDao lkgSyncRequestDao;

    @Resource
    LkgSyncRecordDao lkgSyncRecordDao;

    @Resource
    LkgRemoteService lkgRemoteService;

    @Resource
    LkgContractDao lkgContractDao;

    @Resource
    SyncContractLogicService syncContractLogicService;

    @Resource
    LkgQueryRecordDao lkgQueryRecordDao;

    @Resource
    SyncContractProcessor syncContractProcessor;

    @Override
    public SyncRequestDTO syncContractRequest(SyncRequestDTO syncRequestDTO) {
        /*
        1、接收请求  trade-pigeon  dbi_sync_request
        2、生成需要同步的数据   pigeon自行处理   dbi_sync_record
        3、（逐个处理）sync_record的值，调用同步接口  pigeon-lkg
        */
        //TODO YUYANG
        /**
         * 1、新增dbi_lkg_sync_request记录
         * 2、调用createSyncContractRecord
         */

        LkgSyncRequestEntity lkgSyncRequestEntity = new LkgSyncRequestEntity();

        lkgSyncRequestEntity = BeanConvertUtils.convert(LkgSyncRequestEntity.class, syncRequestDTO);
        lkgSyncRequestEntity.setRequestInfo(JSON.toJSONString(syncRequestDTO))
                .setSyncStatus(LkgSyncStatusEnum.INIT.getValue())
                .setCreatedBy(SystemEnum.MAGELLAN.getEmployId())
                .setCreatedAt(new Date());

        boolean rtn = lkgSyncRequestDao.save(lkgSyncRequestEntity);

        syncRequestDTO.setId(lkgSyncRequestEntity.getId());

        syncContractProcessor.saveSyncRecordContractDef(syncRequestDTO);

        return syncRequestDTO;
    }

    @Override
    public void reSyncContractRequest(Integer reqId) {
        List<LkgSyncRecordEntity> lkgSyncRecordList = lkgSyncRecordDao.getLkgSyncRecordList(reqId);
        for (LkgSyncRecordEntity lkgSyncRecordEntity : lkgSyncRecordList) {
            lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);
        }
    }

    @Override
    public void reBuildSyncContractRequest(Integer reqId) {
        SyncRequestDTO syncRequestDTO = new SyncRequestDTO();

        LkgSyncRequestEntity lkgSyncRequestEntity = lkgSyncRequestDao.getById(reqId);

        syncRequestDTO = JSON.parseObject(lkgSyncRequestEntity.getRequestInfo(), SyncRequestDTO.class);
        syncRequestDTO.setId(lkgSyncRequestEntity.getId());

        //syncRequestDTO = BeanConvertUtils.convert(SyncRequestDTO.class, lkgSyncRequestEntity);

        syncContractProcessor.saveSyncRecordContractDef(syncRequestDTO);
    }

    @Override
    public ArrayList<LkgContractDTO> getLkgContract(LkgSyncRecordEntity lkgSyncRecordEntity) {
        //TODO YUYANG
        /**
         * 1、查询dbi_lkg_contract，获取关联记录
         * 2、调用远程接口，获取详情，更新dbi_lkg_contract
         * 3、聚合信息，返回
         */
        LkgQueryRecordEntity lkgQueryRecordEntity = new LkgQueryRecordEntity();
        lkgQueryRecordEntity.setNavContractCode(lkgSyncRecordEntity.getContractCode())
                .setRequestInfo(JSONObject.toJSONString(lkgSyncRecordEntity))
                .setCreatedAt(new Date())
                .setCreatedBy(SystemEnum.MAGELLAN.getEmployId());
        lkgSyncRecordEntity = syncContractLogicService.buildQueryContractDef(lkgSyncRecordEntity);
        LkgInterfaceResponseDTO responseDTO = lkgRemoteService.sendQueryContract(null);
        ObjectMapper objectMapper = new ObjectMapper();
        ArrayList<LkgContractDTO> lkgContractJsonlist = objectMapper.convertValue(responseDTO.getData(), ArrayList.class);
        lkgQueryRecordEntity.setResponseInfo(JSONObject.toJSONString(responseDTO.getData()));
        lkgQueryRecordDao.save(lkgQueryRecordEntity);

        return lkgContractJsonlist;
    }

    @Override
    public List<LkgContractDTO> getBatchLkgContract(List<String> contractCodes) {
        //TODO YUYANG
        /**
         * 1、批量调用getLkgContract
         */

        List<LkgContractDTO> list = new ArrayList<>();
        for (String contractCode : contractCodes) {
            list.add(getLkgContract(contractCode));
        }
        return list;
    }

    @Override
    public LkgContractDTO getLkgContract(String contractCode) {

        LkgContractEntity lkgContractEntity = lkgContractDao.getLkgContract(contractCode);

        LkgContractDTO lkgContractDTO = BeanConvertUtils.convert(LkgContractDTO.class, lkgContractEntity);

        // dbi_contract是否存在相应的合同
        if (null != lkgContractDTO) {
            // 初始化状态
            lkgContractDTO.setStatus(-1);

            LkgInterfaceResponseDTO lkgInterfaceResponseDTO = lkgRemoteService.sendQueryContract(lkgContractEntity);

            if (lkgInterfaceResponseDTO.getCode() == 1) {
                List<LkgContractDTO> lkgContractList = JSON.parseArray(JSON.toJSONString(lkgInterfaceResponseDTO.getData()), LkgContractDTO.class);
                if (CollectionUtil.isNotEmpty(lkgContractList)) {

                    if (lkgContractList.size() == 1) {
                        lkgContractDTO = lkgContractList.get(0);
                    } else {
                        lkgContractDTO = lkgContractList.get(0);
                        // 出库量累加
                        if (lkgContractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
                            double orderCount = 0d;
                            for (LkgContractDTO contractDTO : lkgContractList) {
                                orderCount = orderCount + contractDTO.getOrderCount();
                            }
                            lkgContractDTO.setOrderCount(orderCount);
                        }
                    }
                }

                // 模拟开单量
                if ("1".equals(lkgOpen)) {
                    if (lkgContractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                        lkgContractDTO
                                // 开单量
                                .setContractOutCount(lkgContractEntity.getContractOutcount().doubleValue())
                                // 出库量
                                .setContractFactOutCount(lkgContractEntity.getContractFactoutcount().doubleValue());
                    } else {
                        lkgContractDTO
                                // 开单量
                                .setOrderCount(lkgContractEntity.getContractOutcount().doubleValue())
                                // 出库量
                                .setInCount(lkgContractEntity.getContractFactoutcount().doubleValue());
                    }
                }
            }
        }

        return lkgContractDTO;
    }


    @Override
    public boolean createSyncContractRecord(SyncRequestDTO syncRequestDTO) {
        syncContractLogicService.saveSyncRecordContractDef(syncRequestDTO);
        return true;
    }

    @Override
    public Result exportTemplate(int salesType, String contractCode, HttpServletResponse response) {
        try {
            System.out.print("salesType=" + salesType + "----contractCode=" + contractCode);
            QueryWrapper<LkgSyncRecordEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("contract_code", contractCode);
            //LkgSyncRecordEntity lkgSyncRecordEntity = lkgSyncRecordDao.getOne(queryWrapper);
            List<LkgSyncRecordEntity> entities = lkgSyncRecordDao.list(queryWrapper);

            if (salesType == 1) {//采销类型 1/2
                ArrayList<LkgPurchaseContractDefDTO> list = new ArrayList<>();
                for (LkgSyncRecordEntity entity : entities) {
                    LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = JSONObject.parseObject(entity.getLkgReqestInfo(), LkgPurchaseContractDefDTO.class);
                    list.add(lkgPurchaseContractDefDTO);
                }
                String fileName = "lkg采购传参信息" + DateTimeUtil.formatDateValue();
                EasyPoiUtils.exportExcel(list, "lkg采购传参信息", "lkg采购传参信息", LkgPurchaseContractDefDTO.class, fileName, response);
                return Result.success(list);
            } else {
                ArrayList<LkgSalesContractDefDTO> list = new ArrayList<>();
                for (LkgSyncRecordEntity entity : entities) {
                    LkgSalesContractDefDTO lkgSalesContractDefDTO = JSONObject.parseObject(entity.getLkgReqestInfo(), LkgSalesContractDefDTO.class);
                    list.add(lkgSalesContractDefDTO);
                }
                String fileName = "lkg销售传参信息" + DateTimeUtil.formatDateValue();
                EasyPoiUtils.exportExcel(list, "lkg销售传参信息", "lkg销售传参信息", LkgSalesContractDefDTO.class, fileName, response);
                return Result.success(list);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        return Result.success("faild", null);

    }


}
