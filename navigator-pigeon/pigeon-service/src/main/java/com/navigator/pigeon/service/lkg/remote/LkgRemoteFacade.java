package com.navigator.pigeon.service.lkg.remote;

import com.navigator.common.config.FeignFormSupportConfig;
import com.navigator.pigeon.pojo.dto.LkgInterfaceRequestDTO;
import com.navigator.pigeon.pojo.dto.LkgInterfaceResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <p>
 * LKG Remote 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@FeignClient(value = "navigator-lkg-service", url = "${lkgService.url}", configuration = FeignFormSupportConfig.class)
public interface LkgRemoteFacade {
    /**
     * 路易达孚系统服务接口
     *
     * @return
     */
    //@PostMapping(value = "/ldzjgzs/tran", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @PostMapping(value = "/lkg/tran", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    LkgInterfaceResponseDTO syncLkgInfo(LkgInterfaceRequestDTO requestDTO);
}
