package com.navigator.pigeon.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.LkgPurchaseContractMapper;
import com.navigator.pigeon.pojo.entity.LkgPurchaseContractEntity;

import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/7/5
 * @Time: 16:52
 * @Desception:
 */
@Dao
public class LkgPurchaseContractDao extends BaseDaoImpl<LkgPurchaseContractMapper, LkgPurchaseContractEntity> {


    /**
     * 获取未被同步的采购合同数据
     * @param lkgPurchaseContractEntity
     * @return
     */
    public List<LkgPurchaseContractEntity> queryList(LkgPurchaseContractEntity lkgPurchaseContractEntity){
        LambdaQueryWrapper<LkgPurchaseContractEntity> wrapper = new LambdaQueryWrapper<LkgPurchaseContractEntity>()
                .eq(LkgPurchaseContractEntity::getIsSynced,lkgPurchaseContractEntity.getIsSynced());
        return this.baseMapper.selectList(wrapper);
    }
}
