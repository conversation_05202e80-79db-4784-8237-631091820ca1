package com.navigator.pigeon.service.lkg;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.pojo.dto.LkgContractDTO;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

public interface ISyncContractService {

    SyncRequestDTO syncContractRequest(SyncRequestDTO syncRequestDTO);

    void reSyncContractRequest(Integer reqId);

    void reBuildSyncContractRequest(Integer reqId);

    ArrayList<LkgContractDTO> getLkgContract(LkgSyncRecordEntity lkgSyncRecordEntity);

    LkgContractDTO getLkgContract(String contractCode);

    List<LkgContractDTO> getBatchLkgContract(List<String> contractCodes);

    boolean createSyncContractRecord(SyncRequestDTO syncRequestDTO);

    Result exportTemplate(int salesType,String contractCode, HttpServletResponse response);



}
