package com.navigator.pigeon.service.lkg.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.pigeon.dao.LkgSyncRecordDao;
import com.navigator.pigeon.pojo.dto.*;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.enums.LkgContractProcessTypeEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.pigeon.pojo.enums.LkgSyncStatusEnum;
import com.navigator.pigeon.service.lkg.remote.LkgRemoteService;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SyncContractLogicService {

    @Resource
    LkgRemoteService lkgRemoteService;

    @Resource
    LkgSyncRecordDao lkgSyncRecordDao;

    @Resource
    SyncValueAdapter syncValueAdapter;

    @Resource
    TradeRemoteService tradeRemoteService;

    @Resource
    SyncConvertContractService syncConvertContractService;

    public List<LkgSyncRecordEntity> saveSyncRecordContractDef(SyncRequestDTO syncRequestDTO) {
        /*
            1、根据业务场景，分别生成记录
            2、每一条记录，获取合同信息
            3、根据获取到的合同信息，生成Lkg的接口定义值对象
            4、更新记录
            5、调用同步接口
        */

        List<LkgSyncRecordEntity> list = new ArrayList<>();

        LkgSyncRecordEntity newLkgSyncRecordEntity = null;
        LkgSyncRecordEntity originalLkgSyncRecordEntity = null;

        //初始化一些值转换
        syncValueAdapter.initAdapter(syncRequestDTO);

        //request处理类型
        LkgContractProcessTypeEnum lkgContractProcessTypeEnum = syncValueAdapter.lkgContractProcessTypeEnum_request;

        if (null == lkgContractProcessTypeEnum) {
            return new ArrayList<>();
        }

        //新增新合同
        if (lkgContractProcessTypeEnum.getNeedAdd() == 1) {
            newLkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
            newLkgSyncRecordEntity.setProcessType(null == syncValueAdapter.lkgContractProcessTypeEnum_new ? "ADD" : syncValueAdapter.lkgContractProcessTypeEnum_new.name())
                    .setSyncType(LkgInterfaceActionEnum.ADD.getValue());

            list.add(newLkgSyncRecordEntity);
            //生成新增记录
            lkgSyncRecordDao.save(newLkgSyncRecordEntity);

            //进行同步处理
            processAddSyncRecord(newLkgSyncRecordEntity);
        }
        //更新原合同
        if (lkgContractProcessTypeEnum.getNeedUpdate() > 0) {
            originalLkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
            originalLkgSyncRecordEntity.setContractId(syncRequestDTO.getParentContractId())
                    .setParentContractId(0)
                    .setProcessType(null == syncValueAdapter.lkgContractProcessTypeEnum_original ? "DEFAULT_UPDATE" : syncValueAdapter.lkgContractProcessTypeEnum_original.name())
                    .setSyncType(LkgInterfaceActionEnum.UPDATE.getValue());

            list.add(originalLkgSyncRecordEntity);

            lkgSyncRecordDao.save(originalLkgSyncRecordEntity);
            //进行同步处理
            //处理更新的记录
            processUpdateSyncRecord(originalLkgSyncRecordEntity);
        }


        //新增定价
        if (lkgContractProcessTypeEnum == LkgContractProcessTypeEnum.PRICE) {
            /*newLkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
            newLkgSyncRecordEntity.setProcessType(null == syncValueAdapter.lkgContractProcessTypeEnum_new ? "ADD" : lkgContractProcessTypeEnum.name())
                    .setSyncType(LkgInterfaceActionEnum.PRICE.getValue());*/
            LkgSyncRecordEntity priceLkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
            priceLkgSyncRecordEntity.setProcessType(null == syncValueAdapter.lkgContractProcessTypeEnum_request ? "PRICE" : syncValueAdapter.lkgContractProcessTypeEnum_request.name())
                    .setSyncType(LkgInterfaceActionEnum.PRICE.getValue());
            //处理新增定价同步记录
            processAddPriceSyncRecord(syncRequestDTO, priceLkgSyncRecordEntity);
        }

        //如果是基差拆一口价，需要作废原合同的点价记录
        boolean b1 = syncRequestDTO.getTradeType() == ContractTradeTypeEnum.SPLIT_NORMAL.getValue()
                || syncRequestDTO.getTradeType() == ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue();
        boolean b2 = null != originalLkgSyncRecordEntity && originalLkgSyncRecordEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue();
        boolean b3 = null != newLkgSyncRecordEntity && newLkgSyncRecordEntity.getContractType() == ContractTypeEnum.YI_KOU_JIA.getValue();

        if (b1 && b2 && b3) {
            //处理原合同的定价记录更新（作废）
            processUpdatePriceSyncRecord(newLkgSyncRecordEntity);
        }

        return list;
    }

    public void processAddSyncRecord(LkgSyncRecordEntity lkgSyncRecordEntity) {
        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = syncConvertContractService.convertPurchaseContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgPurchaseContractDefDTO))
                    .setRefContractNumber(lkgPurchaseContractDefDTO.getRefContractNumber());
        } else {
            LkgSalesContractDefDTO lkgSalesContractDefDTO = syncConvertContractService.convertSalesContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractDefDTO))
                    .setRefContractNumber(lkgSalesContractDefDTO.getRefContractNumber());
        }

        //发送远程调用
        lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        //更新记录
        lkgSyncRecordDao.updateById(lkgSyncRecordEntity);

    }

    public void processUpdateSyncRecord(LkgSyncRecordEntity lkgSyncRecordEntity) {
        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            LkgPurchaseContractModifyDefDTO lkgContractModifyDTO = syncConvertContractService.convertPurchaseContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractModifyDTO))
                    .setRefContractNumber(lkgContractModifyDTO.getRefContractNumber());
        } else {
            LkgSalesContractModifyDefDTO lkgSalesContractModifyDefDTO = syncConvertContractService.convertSalesContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractModifyDefDTO))
                    .setRefContractNumber(lkgSalesContractModifyDefDTO.getRefContractNumber());
        }

        //发送远程调用
        lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        //更新记录
        lkgSyncRecordDao.updateById(lkgSyncRecordEntity);

    }

    public void processAddPriceSyncRecord(SyncRequestDTO syncRequestDTO, LkgSyncRecordEntity lkgSyncRecordEntity) {
        ConfirmPriceDTO confirmPriceDTO = tradeRemoteService.getConfirmPriceInfo(syncRequestDTO.getContractId(), syncRequestDTO.getConfirmPriceId());
        if (null != confirmPriceDTO) {
            ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(syncRequestDTO.getContractId());

            //转换为定价值对象
            LkgContractPriceDefDTO lkgContractPriceDefDTO = syncConvertContractService.convertLkgContractPriceDefDTO(syncRequestDTO, confirmPriceDTO, contractDetailVO);

            //设置请求业务数据
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractPriceDefDTO));

            lkgSyncRecordDao.save(lkgSyncRecordEntity);

            //发送远程调用
            lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

            //更新记录
            lkgSyncRecordDao.updateById(lkgSyncRecordEntity);
        }

    }

    private void processUpdatePriceSyncRecord(LkgSyncRecordEntity lkgSyncRecordEntity) {
        //获取新合同关联的定价单信息
        List<ConfirmPriceDTO> confirmPriceDTOList = tradeRemoteService.getConfirmPriceList(lkgSyncRecordEntity.getContractId());
        if (null != confirmPriceDTOList && confirmPriceDTOList.size() > 0) {
            for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {
                LkgContractPriceDefDTO lkgContractPriceDefDTO = new LkgContractPriceDefDTO();
                lkgContractPriceDefDTO.setFixPriceNo(confirmPriceDTO.getFixPriceNo())
                        .setFixPriceCount(BigDecimal.ZERO);

                LkgSyncRecordEntity syncRecordEntity = new LkgSyncRecordEntity();
                BeanConvertUtils.copy(syncRecordEntity, lkgSyncRecordEntity);
                syncRecordEntity.setId(null)
                        .setLkgReqestInfo(JSON.toJSONString(lkgContractPriceDefDTO))
                        .setProcessType(LkgContractProcessTypeEnum.PRICE_UPDATE.name());

                lkgSyncRecordDao.save(syncRecordEntity);

                //发送远程调用
                lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

                //更新记录
                lkgSyncRecordDao.updateById(lkgSyncRecordEntity);

            }
        }
    }

    /**
     * 根据SyncRequestDTO 生成 SyncRecordEntity
     *
     * @param syncRequestDTO
     * @return
     */
    private LkgSyncRecordEntity createSyncRecord(SyncRequestDTO syncRequestDTO) {
        LkgSyncRecordEntity lkgSyncRecordEntity = new LkgSyncRecordEntity();
        lkgSyncRecordEntity.setRequestId(syncRequestDTO.getId())
                .setLkgContractCode("")
                .setContractCode(syncRequestDTO.getContractCode())
                .setContractId(syncRequestDTO.getContractId())
                .setParentContractId(syncRequestDTO.getParentContractId())
                .setTradeType(syncRequestDTO.getTradeType())
                .setSalesType(syncRequestDTO.getSalesType())
                .setCustomerCode(syncRequestDTO.getCustomerCode())
                .setTryTimes(0)
                //.setSyncType(LkgInterfaceActionEnum.ADD.getValue())
                .setSyncTime(null)
                .setSyncStatus(LkgSyncStatusEnum.INIT.getValue())
                .setLkgReqestInfo("")
                .setLkgResultsInfo("")
                .setLkgResultsStatus(0)
                .setFactoryCode("")
                .setRemark("")
                .setNonce("")
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCreatedBy(SystemEnum.MAGELLAN.getEmployId())
                .setUpdatedBy(SystemEnum.MAGELLAN.getEmployId());

        return lkgSyncRecordEntity;
    }

    public LkgSyncRecordEntity buildQueryContractDef(LkgSyncRecordEntity lkgSyncRecordEntity) {
        //TODO YUYANG

//        ContractSalesTypeEnum contractSalesTypeEnum = null;
//        contractSalesTypeEnum = ContractSalesTypeEnum.getByValue(lkgSyncRecordEntity.getSalesType());

//        if (contractSalesTypeEnum == ContractSalesTypeEnum.PURCHASE) {
//            LkgQueryRecordDTO lkgQueryRecordDTO = syncConvertContractService.convertLkgQueryRecordDTO(lkgSyncRecordEntity.getContractCode(), lkgSyncRecordEntity.getRefContractNumber());
//            lkgSyncRecordEntity.setContractCode(lkgSyncRecordEntity.getContractCode())
//                    .setSalesType(lkgSyncRecordEntity.getSalesType());
//
//            lkgSyncRecordEntity.setLkgReqestInfo(JSONObject.toJSONString(lkgQueryRecordDTO));
//        } else {
        LkgQueryRecordDTO lkgQueryRecordDTO = syncConvertContractService.convertLkgQueryRecordDTO(lkgSyncRecordEntity.getContractCode(), lkgSyncRecordEntity.getRefContractNumber());
        lkgSyncRecordEntity.setContractCode(lkgSyncRecordEntity.getContractCode())
                .setSalesType(lkgSyncRecordEntity.getSalesType());

        lkgSyncRecordEntity.setLkgReqestInfo(JSONObject.toJSONString(lkgQueryRecordDTO));
//        }
        return lkgSyncRecordEntity;
    }


}
