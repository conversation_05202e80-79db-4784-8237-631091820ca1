package com.navigator.pigeon.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.pigeon.mapper.DbiLkgSyncRequestMapper;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import com.navigator.pigeon.pojo.enums.LkgSyncStatusEnum;

import java.text.SimpleDateFormat;
import java.util.List;

@Dao
public class LkgSyncRequestDao extends BaseDaoImpl<DbiLkgSyncRequestMapper, LkgSyncRequestEntity> {

    /**
     * 获取同步失败的lkg同步列表
     *
     * @return
     */
    public List<LkgSyncRequestEntity> querySyncingList() {
        String format = new SimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.addDays(-1));
        String beginTime = format + " 00:00:00";
        String endTime = format + " 23:59:59";
        LambdaQueryWrapper<LkgSyncRequestEntity> wrapper = new LambdaQueryWrapper<LkgSyncRequestEntity>()
                .eq(LkgSyncRequestEntity::getSyncStatus, LkgSyncStatusEnum.SYNCING.getValue())
                .eq(LkgSyncRequestEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .between(LkgSyncRequestEntity::getCreatedAt, beginTime, endTime)
                .orderByDesc(LkgSyncRequestEntity::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 获取同步初始状态的lkg同步列表
     *
     * @return
     */
    public List<LkgSyncRequestEntity> queryInitList() {
        String format = new SimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.addDays(-1));
        String beginTime = format + " 00:00:00";
        String endTime = format + " 23:59:59";
        LambdaQueryWrapper<LkgSyncRequestEntity> wrapper = new LambdaQueryWrapper<LkgSyncRequestEntity>()
                .eq(LkgSyncRequestEntity::getSyncStatus, LkgSyncStatusEnum.INIT.getValue())
                .eq(LkgSyncRequestEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .between(LkgSyncRequestEntity::getCreatedAt, beginTime, endTime)
                .orderByDesc(LkgSyncRequestEntity::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public List<LkgSyncRequestEntity> queryListByTtId(Integer ttId) {
        return this.list(new LambdaQueryWrapper<LkgSyncRequestEntity>()
                .eq(LkgSyncRequestEntity::getTtId, ttId)
                .eq(LkgSyncRequestEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(LkgSyncRequestEntity::getCreatedAt));
    }

    public List<LkgSyncRequestEntity> queryListByCheckDate(String startCheckTime, String endCheckTime) {
        return this.list(new LambdaQueryWrapper<LkgSyncRequestEntity>()
                .between(StrUtil.isNotBlank(startCheckTime) && StrUtil.isNotBlank(endCheckTime), LkgSyncRequestEntity::getCreatedAt, startCheckTime, endCheckTime)
                .eq(LkgSyncRequestEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(LkgSyncRequestEntity::getCreatedAt));
    }
}
