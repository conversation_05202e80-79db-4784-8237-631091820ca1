package com.navigator.pigeon.service.lkg.impl;

import cn.hutool.core.collection.CollUtil;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.pigeon.dao.LkgPurchaseContractDao;
import com.navigator.pigeon.dao.LkgSalesContractDao;
import com.navigator.pigeon.dao.PrepareContractDao;
import com.navigator.pigeon.pojo.dto.ImportPurchaseContract;
import com.navigator.pigeon.pojo.dto.ImportSalesContract;
import com.navigator.pigeon.pojo.entity.LkgPurchaseContractEntity;
import com.navigator.pigeon.pojo.entity.LkgSalesContractEntity;
import com.navigator.pigeon.pojo.entity.PrepareContractEntity;
import com.navigator.pigeon.pojo.enums.LkgImportContractSyncedStatusEnum;
import com.navigator.pigeon.service.lkg.LkgContractHandleService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractPriceFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/6/30
 * @Time: 18:42
 * @Desception:
 */
@Service
@Slf4j
public class LkgContractHandleServiceImpl implements LkgContractHandleService {

    @Resource
    LkgSalesContractDao lkgSalesContractDao;

    @Resource
    LkgPurchaseContractDao lkgPurchaseContractDao;

    @Resource
    PrepareContractDao prepareContractDao;

    @Resource
    SyncToPrepareContractService syncToPrepareContractService;

    @Resource
    ContractFacade contractFacade;

    @Resource
    ContractPriceFacade contractPriceFacade;

    @Override
    public void getSheet(String fileUrl, int salesType) {
        FileInputStream fis = null;

        //得到Excel工作簿对象
        HSSFWorkbook wb = null;

        try {
            fis = new FileInputStream(new File(fileUrl));
            wb = new HSSFWorkbook(fis);

            //得到Excel工作表对象
            HSSFSheet sheet = wb.getSheetAt(0);
            //取得有效的行数
            int rowcount = sheet.getLastRowNum();
            System.out.println("有效行数---- " + rowcount);

            if (salesType == ContractSalesTypeEnum.SALES.getValue()) {
                for (Row row : sheet) {
                    //从数据行开始读取
                    if (row.getRowNum() == 0) {
                        continue;
                    }
                    List<String> list = getSaleRowData(row);
                    LkgSalesContractEntity salesContractEntity = new LkgSalesContractEntity();
                    setBeanFieldValue(salesContractEntity, list);
                    System.out.println("------setBeanFieldValue-----" + salesContractEntity.toString());
                    lkgSalesContractDao.save(salesContractEntity);
                    //break;
                }
            } else {
                for (Row row : sheet) {
                    //从数据行开始读取
                    if (row.getRowNum() == 0) {
                        continue;
                    }
                    List<String> list = getPurchaseRowData(row);
                    LkgPurchaseContractEntity purchaseContractEntity = new LkgPurchaseContractEntity();
                    setBeanFieldValue(purchaseContractEntity, list);
                    System.out.println("------purchaseContractEntity-----" + purchaseContractEntity.toString());
                    lkgPurchaseContractDao.save(purchaseContractEntity);
                    //break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != wb)
                    wb.close();
                if (null != fis)
                    fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            fis = null;
            wb = null;
        }
    }

    public static List<String> getSaleRowData(Row row) {
        String value = "";
        System.out.println("RowNum----" + row.getRowNum());
        System.out.println("LastCellNum----" + row.getLastCellNum());
        List<String> list = new ArrayList<>();
        for (int i = 1; i < row.getLastCellNum(); i++) {

            if (row.getCell(i) != null) {
                if (i == 3 || i == 14 || i == 31 || i == 32 || i == 33 || i == 34 || i == 42 || i == 48 || i == 62 || i == 64) {
                    try {
                        System.out.println("CellNum--" + i + "--" + new SimpleDateFormat("yyyy/MM/dd").format(row.getCell(i).getDateCellValue()));
                        value = new SimpleDateFormat("yyyy/MM/dd").format(row.getCell(i).getDateCellValue());
                        list.add(value);
                    } catch (Exception e) {
                        System.out.println("CellNum--" + i + "--");
                        list.add("");
                    }
                } else {
                    System.out.println("CellNum--" + i + "--" + row.getCell(i).toString());
                    value = row.getCell(i).toString();
                    list.add(value);
                }

            } else {
                System.out.println("CellNum--" + i + "--");
                list.add("");
            }
        }
        return list;
    }

    public static List<String> getPurchaseRowData(Row row) {
        String value = "";
        System.out.println("RowNum----" + row.getRowNum());
        System.out.println("LastCellNum----" + row.getLastCellNum());
        List<String> list = new ArrayList<>();
        for (int i = 1; i < row.getLastCellNum(); i++) {

            if (row.getCell(i) != null) {
                if (i == 8 || i == 12 || i == 18 || i == 19 || i == 36 || i == 38 || i == 42) {
                    try {
                        System.out.println("CellNum--" + i + "--" + new SimpleDateFormat("yyyy/MM/dd").format(row.getCell(i).getDateCellValue()));
                        value = new SimpleDateFormat("yyyy/MM/dd").format(row.getCell(i).getDateCellValue());
                        list.add(value);
                    } catch (Exception e) {
                        System.out.println("CellNum--" + i + "--");
                        list.add("");
                    }
                } else {
                    System.out.println("CellNum--" + i + "--" + row.getCell(i).toString());
                    value = row.getCell(i).toString();
                    list.add(value);
                }

            } else {
                System.out.println("CellNum--" + i + "--");
                list.add("");
            }
        }
        return list;
    }

    public static void setBeanFieldValue(Object bean, List<String> list) {
        Class<?> beanClass = bean.getClass();
        Method[] methods = beanClass.getDeclaredMethods();
        Field[] fields = beanClass.getDeclaredFields();
        //for (Field field : fields) {
        String str = "";
        for (int i = 2; i < fields.length; i++) {
            try {
                Field field = fields[i];
                String fieldSetName = spliceSetMethod(field.getName());
                str = str + "-" + fieldSetName;
                if (!checkPropertyMethodExist(methods, fieldSetName)) continue;
                Method fieldSetMet = beanClass.getMethod(fieldSetName, field.getType());
                //String value = valueMap.get(field.getName());
                String value = list.get(i - 2);
                fieldSetMet.invoke(bean, getFieldTypeValue(field, value));

            } catch (Exception e) {

            }
        }
        System.out.println("----str-----" + str);
    }

    /**
     * return the corresponding type value according to the field property
     *
     * @param field the field
     * @param value original value
     * @return corresponding type value
     */
    public static Object getFieldTypeValue(Field field, Object value) {
        if (null == field || null == value || "".equals(value)) return null;
        String fieldType = field.getType().getSimpleName();
        if ("String".equals(fieldType)) return String.valueOf(value);
        return value;
    }

    public static boolean checkPropertyMethodExist(Method[] methods, String fieldMethod) {
        if (null == fieldMethod || "".equals(fieldMethod)) return false;
        for (Method met : methods) {
            if (fieldMethod.equals(met.getName())) return true;
        }
        return false;
    }

    public static String spliceSetMethod(String fieldName) {
        return splicePropertyMethod(fieldName, "set");
    }

    public static String splicePropertyMethod(String fieldName, String method) {
        if (null == fieldName || "".equals(fieldName)) return null;
        return method + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
    }


    /**
     * 导入lkg合同数据
     *
     * @param file
     * @param salesType
     * @return
     */
    @Override
    public Result importContract(MultipartFile file, Integer salesType) {
        try {
            if (salesType.equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
                List<ImportPurchaseContract> purchaseContractList = EasyPoiUtils.importExcel(file, 0, 1, ImportPurchaseContract.class);
                log.info(FastJsonUtils.getBeanToJson(purchaseContractList));
                importPurchaseContract(purchaseContractList);
            } else {
                List<ImportSalesContract> salesContractList = EasyPoiUtils.importExcel(file, 0, 1, ImportSalesContract.class);
                log.info(FastJsonUtils.getBeanToJson(salesContractList));
                importSalesContract(salesContractList);
            }
            return Result.success();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("导入合同数据失败" + e.toString());
        }
    }

    /**
     * 导入采购合同数据
     *
     * @param purchaseContractList
     */
    public void importPurchaseContract(List<ImportPurchaseContract> purchaseContractList) {
        if (CollUtil.isNotEmpty(purchaseContractList)) {
            purchaseContractList.forEach(purchaseContract -> {
                LkgPurchaseContractEntity lkgPurchaseContractEntity = new LkgPurchaseContractEntity();
                BeanConvertUtils.copy(lkgPurchaseContractEntity, purchaseContract);
                lkgPurchaseContractEntity.setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
                lkgPurchaseContractDao.save(lkgPurchaseContractEntity);
            });
        }
    }

    /**
     * 导入销售合同数据
     *
     * @param salesContractList
     */
    public void importSalesContract(List<ImportSalesContract> salesContractList) {
        if (CollUtil.isNotEmpty(salesContractList)) {
            salesContractList.forEach(salesContract -> {
                LkgSalesContractEntity lkgSalesContractEntity = new LkgSalesContractEntity();
                BeanConvertUtils.copy(lkgSalesContractEntity, salesContract);
                lkgSalesContractEntity.setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
                lkgSalesContractDao.save(lkgSalesContractEntity);
            });
        }
    }

    //@Scheduled(cron = "0 0/3 * * * ? ")
    public void scheduleP() {
        syncPrepareContract();
    }

    //@Scheduled(cron = "0 0/6 * * * ? ")
    public void scheduleT() {
        syncContract();
    }

    /**
     * 将导入后的采销合同数据同步到prepareContract
     *
     * @return
     */
    @Override
    public void syncPrepareContract() {
        // 先同步销售合同数据
        LkgSalesContractEntity lkgSalesContractEntity = new LkgSalesContractEntity()
                .setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
        List<LkgSalesContractEntity> lkgSalesContractList = lkgSalesContractDao.queryList(lkgSalesContractEntity);
        if (CollUtil.isNotEmpty(lkgSalesContractList)) {
            syncToPrepareContractService.syncSalesPrepareContract(lkgSalesContractList);
        }

        // 获取采购销售合同数据
        LkgPurchaseContractEntity lkgPurchaseContractEntity = new LkgPurchaseContractEntity()
                .setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
        List<LkgPurchaseContractEntity> lkgPurchaseContractList = lkgPurchaseContractDao.queryList(lkgPurchaseContractEntity);
        if (CollUtil.isNotEmpty(lkgPurchaseContractList)) {
            syncToPrepareContractService.syncPurchasePrepareContract(lkgPurchaseContractList);
        }
    }

    /**
     * 同步prepare数据到合同
     */
    @Override
    public void syncContract() {
        //获取需要同步的数据
        PrepareContractEntity prepareContract = new PrepareContractEntity()
                .setIsSynced(LkgImportContractSyncedStatusEnum.NO_SYNCED.getValue());
        List<PrepareContractEntity> prepareContractEntities = prepareContractDao.queryList(prepareContract);
        syncToPrepareContractService.syncContract(prepareContractEntities);
    }
}
