package com.navigator.pigeon.service.lkg.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.config.properties.AzureBlobProperties;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.enums.SyncSystemEnum;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.CommonListUtil;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.http.OkSslUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.pigeon.dao.*;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.entity.*;
import com.navigator.pigeon.pojo.enums.*;
import com.navigator.pigeon.service.lkg.ILkgContractDailyCheckService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.navigator.pigeon.constant.LkgBizConstant.MAX_PROCESS_TIME;

@Component
@Service
@Slf4j
public class LkgContractDailyCheckServiceImpl implements ILkgContractDailyCheckService {

    @Value("${lkgFileUrl.url}")
    private String lkgFileUrl;

    @Value("${checkNotice.email}")
    private String checkNotice;

    @Resource
    private MessageFacade messageFacade;

    @Resource
    private ContractFacade contractFacade;

    @Resource
    private LkgContractFacade lkgContractFacade;

    @Autowired
    LkgDailyTaskDao lkgDailyTaskDao;

    @Autowired
    LkgDailyCheckDetailDao lkgDailyCheckDetailDao;

    @Autowired
    LkgDailyFileDao lkgDailyFileDao;

    @Resource
    LkgSyncRequestDao lkgSyncRequestDao;

    @Autowired
    LkgDailyContractDao lkgDailyContractDao;

    @Resource
    private AzureBlobUtil azureBlobUtil;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SiteFacade siteFacade;

    @Resource
    AzureBlobProperties azureBlobProperties;

    //@Scheduled(fixedRate = 5000)
    //@Scheduled(cron = "0 05 14 * * ?")
    public void schedule1() {
        changeToPull();
    }

    //@Scheduled(fixedRate = 5000)
    public void schedule2() {
        changeToRecord();
    }

    //@Scheduled(cron = "0 0/3 * * * ? ")
    public void schedule3() {
        dailyCheck();
    }

    //判断LKG init
    public static final int LKG_REQUEST_INIT_SUB_TIME = 1000 * 60 * 10;

    /**
     * 21:00-22:00新增两条dailyTask状态是获取文件
     */
    @Override
    public void changeToPull() {

/*        log.info("------------定时更新1-------------");
        String checkDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
        tEntity.setCheckDate(checkDate)
                .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.INIT.getValue());
        List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(tEntity);
        if (CollUtil.isNotEmpty(list)) {
            for (LkgDailyTaskEntity taskEntity : list) {
                taskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.PULL_FILE.getValue());
                lkgDailyTaskDao.updateById(taskEntity);
            }
        }*/
        //获取今天的dbi_lkg_daily_task记录，如果没有，则写入
        String checkDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
        tEntity.setCheckDate(checkDate);
        List<LkgDailyTaskEntity> dailyTaskList = lkgDailyTaskDao.queryList(tEntity);
        if (CollUtil.isEmpty(dailyTaskList)) {
            log.info("=====数据库新增两条dailyTask===状态是获取文件");
            LkgDailyTaskEntity lkgDailyTaskEntity = new LkgDailyTaskEntity();
            lkgDailyTaskEntity.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setCheckDate(checkDate)
                    .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.PULL_FILE.getValue())
                    .setRemark("")
                    .setCreatedBy(SystemEnum.MAGELLAN.getEmployId())
                    .setCreatedAt(new Date());
            lkgDailyTaskDao.save(lkgDailyTaskEntity);

            lkgDailyTaskEntity.setSalesType(ContractSalesTypeEnum.SALES.getValue());
            lkgDailyTaskDao.save(lkgDailyTaskEntity);
        }
    }

    /**
     * 22:00-22:30
     * 这里主要获取lkg文件入库(nav的会自动生成)dailyFile
     * 到指定文件夹遍历其中的文件
     * 将获取的文件信息，写入数据库dbi_lkg_daily_file
     * 不要重复写
     * 到了22:00（使用配置），更改状态为RECORD_CONTRACT_DATA
     */
    @Override
    public void changeToRecord() {
        log.info("======开始获取lkg文件======到数据库");
        String checkDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(new LkgDailyTaskEntity()
                .setCheckDate(checkDate)
                .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.PULL_FILE.getValue()));

        if (CollUtil.isNotEmpty(list)) {
            processFiles(checkDate, "lkg");
            processFiles(checkDate, "atlas");

            list.forEach(taskEntity -> {
                taskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.RECORD_CONTRACT_DATA.getValue());
                lkgDailyTaskDao.updateById(taskEntity);
            });
        }
    }

    private void processFiles(String checkDate, String fileSource) {
        String checkDateStr = new SimpleDateFormat("yyyyMMdd").format(DateTimeUtil.addDays(-1));

        if ("lkg".equals(fileSource)) {
            for (LkgFileSiteIdEnum value : LkgFileSiteIdEnum.values()) {
                String fileName = checkDateStr + value.getFileCsv();
                String[] sp = fileName.split("_");
                String filePath = lkgFileUrl + checkDateStr + value.getFileCsv();

                if (lkgDailyFileDao.getDailyFileEntity(new LkgDailyFileEntity()
                        .setFilePath(filePath)
                        .setCheckDate(checkDate)
                        .setFileSource(fileSource)) == null) {
                    lkgDailyFileDao.save(new LkgDailyFileEntity()
                            .setFilePath(filePath)
                            .setFileName(fileName)
                            .setFileSource(fileSource)
                            .setIsGenerate(LkgDailyFileIsGenerate.UN_GENERATE.getValue())
                            .setSiteId(sp[1])
                            .setSalesType("purchase".equals(sp[2]) ? 1 : 2)
                            .setCheckDate(checkDate)
                            .setCurrentCount(0)
                            .setPullStatus(0)
                            .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue()));
                }
            }
        } else if ("atlas".equals(fileSource)) {

            // atlas只有采销两个文件 账套在文件中
            List<String> salesList = Arrays.asList("purchase", "sales");
            salesList.forEach(sales -> {
                // BUGFIX：Case-1003244 -ATLAS合同关闭作业的时间调整到凌晨4点 Author: Mr 2025-06-04 Start
                // String fileName = checkDateStr + "_" + sales + "_detail.csv";
                String fileName = DateUtil.format(new Date(), "yyyyMMdd") + "_" + sales + "_detail.csv";
                // BUGFIX：Case-1003244 -ATLAS合同关闭作业的时间调整到凌晨4点 Author: Mr 2025-06-04 End
                String filePath = azureBlobProperties.getHost() + "/" + azureBlobProperties.getCloseContainName() + "/" + fileName;

                if (lkgDailyFileDao.getDailyFileEntity(new LkgDailyFileEntity()
                        .setFilePath(filePath)
                        .setCheckDate(checkDate)
                        .setFileSource(fileSource)) == null) {
                    lkgDailyFileDao.save(new LkgDailyFileEntity()
                            .setFilePath(filePath)
                            .setFileName(fileName)
                            .setFileSource(fileSource)
                            .setIsGenerate(LkgDailyFileIsGenerate.UN_GENERATE.getValue())
                            .setSiteId("atlas")
                            .setSalesType("purchase".equals(sales) ? 1 : 2)
                            .setCheckDate(checkDate)
                            .setCurrentCount(0)
                            .setPullStatus(0)
                            .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue()));
                }
            });
        }
    }

    @Override
    public void dailyCheck() {
        /**
         * 获取 dbi_lkg_daily_task，如果没有，新增两条记录，状态为初始化
         * 22:00（使用配置）之前，状态一直为PULL_FILE
         * 22:00（使用配置），更改状态为RECORD_CONTRACT_DATA
         * 如果状态为PULL_FILE，调用pullDailyFile方法
         * 如果状态为RECORD_CONTRACT_DATA，调用createDailyCheckTaskRecord方法，全部落地成功后，更新状态为 CHECK_CONTRACT_NAV
         * 如果状态为CHECK_CONTRACT_NAV，调用checkContractByNav方法，如果无数据可更新，更改状态为CHECK_CONTRACT_LKG
         * 如果状态为 CHECK_CONTRACT_LKG，调动checkContractBLkg方法，如果无数据可更新，更改状态为CREATE_CHECK_REPORT
         * 如果状态为CREATE_CHECK_REPORT，调用sendDailyCheckReport方法，完成后更改状态为CHECK_COMPLATE
         *
         */

        //获取今天的dbi_lkg_daily_task记录，如果没有，则写入
        String checkDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        /*LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
        tEntity.setCheckDate(checkDate);
        List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(tEntity);
        if (CollUtil.isNotEmpty(list)) {
            for (LkgDailyTaskEntity taskEntity : list) {
                int handleStatus = taskEntity.getHandleStatus();
                if (handleStatus == LkgDailyCheckTaskHandleStatusEnum.RECORD_CONTRACT_DATA.getValue()) {
                    createDailyContractRecord(checkDate);
                } else if (handleStatus == LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_NAV.getValue()) {
                    checkContractByNav(checkDate);
                } else if (handleStatus == LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_LKG.getValue()) {
                    checkContractByLkg(checkDate);
                } else if (handleStatus == LkgDailyCheckTaskHandleStatusEnum.CREATE_CHECK_REPORT.getValue()) {
                    sendDailyCheckReport(checkDate);
                } else {
                    log.info("======" + checkDate + "合同校核完成");
                }
            }
        }*/

        // 每日合同关闭
        // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 Start
        closeDailyContract(checkDate, null);
        // 优化：case-1002596 BR-TJIBSBMS2303975-001 合同是正本状态，但是N057销售合同汇总导出时显示了关闭状态-测试通过合同关闭接口 Author: Mr 2024-06-04 End
    }

    //@Scheduled(cron = "0 0/3 * * * ? ")
    public void scheduleP() {
        resync();
    }

    /**
     * 重试机制
     */
    @Override
    public void resync() {
        log.info("=======重试机制======");
        //获取未同步成功的数据
        List<LkgSyncRequestEntity> lkgSyncRequestList = lkgSyncRequestDao.querySyncingList();
        if (CollUtil.isNotEmpty(lkgSyncRequestList)) {
            log.info("=====重试总条数=====" + lkgSyncRequestList.size());
            for (int i = 0; i < lkgSyncRequestList.size(); i++) {
                log.info("=====当前重试条数=====" + i);
                LkgSyncRequestEntity lkgSyncRequestEntity = lkgSyncRequestList.get(i);
                log.info("=====重试====" + JSON.toJSONString(lkgSyncRequestEntity));
                lkgContractFacade.reSyncContractRequest(lkgSyncRequestEntity.getId());
                if (i == 100) {
                    break;
                }
            }
        }

        //获取初始状态数据
        List<LkgSyncRequestEntity> lkgInitRequestList = lkgSyncRequestDao.queryInitList();
        Date now = new Date();
        if (CollUtil.isNotEmpty(lkgInitRequestList)) {
            log.info("=====重试INIT总条数=====" + lkgInitRequestList.size());
            for (int i = 0; i < lkgInitRequestList.size(); i++) {
                log.info("=====当前INIT重试条数=====" + i);
                LkgSyncRequestEntity lkgInitRequestEntity = lkgInitRequestList.get(i);
                Date updatedAt = lkgInitRequestEntity.getUpdatedAt();
                //只有超过一定时间的request才能重试，保证相应流程不是在执行中
                if ((now.getTime() - updatedAt.getTime()) > LKG_REQUEST_INIT_SUB_TIME) {
                    log.info("=====INIT重试====" + JSON.toJSONString(lkgInitRequestEntity));
                    lkgContractFacade.reBuildSyncContractRequest(lkgInitRequestEntity.getId());
                }
                if (i == 100) {
                    break;
                }
            }
        }
    }

    @Override
    public void createDailyCheckTaskRecord(String checkDate) {
        //生成dbi_lkg_daily_task记录，每天两条
        LkgDailyTaskEntity lkgDailyTaskEntity = new LkgDailyTaskEntity();
        lkgDailyTaskEntity.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setCheckDate(checkDate)
                .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.INIT.getValue())
                .setRemark("")
                .setCreatedBy(SystemEnum.MAGELLAN.getEmployId())
                .setCreatedAt(new Date());
        lkgDailyTaskDao.save(lkgDailyTaskEntity);

        lkgDailyTaskEntity.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        lkgDailyTaskDao.save(lkgDailyTaskEntity);
    }

    @Override
    public void pullDailyFile(String checkDate) {
        /**
         * 获取lkg文件
         * 到指定文件夹遍历其中的文件
         * 将获取的文件信息，写入数据库dbi_lkg_daily_file
         * 不要重复写
         * 到了22:00（使用配置），更改状态为RECORD_CONTRACT_DATA
         */
        LkgFileSiteIdEnum[] values = LkgFileSiteIdEnum.values();
        for (LkgFileSiteIdEnum value : values) {
            LkgDailyFileEntity dfEntity = new LkgDailyFileEntity();
            String checkDateStr = new SimpleDateFormat("yyyyMMdd").format(DateTimeUtil.addDays(-1));
            String fileName = checkDateStr + value.getFileCsv();
            String[] sp = fileName.split("_");
            String filePath = lkgFileUrl + checkDateStr + value.getFileCsv();
            dfEntity.setFilePath(filePath)
                    .setCheckDate(checkDate)
                    .setFileSource("lkg");
            LkgDailyFileEntity fileEntity = lkgDailyFileDao.getDailyFileEntity(dfEntity);
            if (fileEntity == null) {
                LkgDailyFileEntity lkgDailyFileEntity = new LkgDailyFileEntity();
                lkgDailyFileEntity
                        .setFilePath(filePath)
                        .setFileName(fileName)
                        .setFileSource("lkg")
                        .setIsGenerate(LkgDailyFileIsGenerate.UN_GENERATE.getValue())
                        .setSiteId(sp[1])
                        .setSalesType("purchase".equals(sp[2]) ? 1 : 2)
                        .setCheckDate(checkDate)
                        .setCurrentCount(0)
                        .setPullStatus(0)
                        .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue());
                lkgDailyFileDao.save(lkgDailyFileEntity);
            }
        }


    }

    /**
     * 关闭每日合同
     *
     * @param checkDate 检查日期
     */
    @Override
    public List<String> closeDailyContract(String checkDate, MultipartFile file) {
        // 查询指定日期且状态为 PULL 的每日文件列表
        List<LkgDailyFileEntity> dailyFiles = lkgDailyFileDao.queryList(new LkgDailyFileEntity()
                .setCheckDate(checkDate)
                .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue()));

        // 用于记录合同关闭的日志
        List<String> closeLogList = new ArrayList<>();

        // 只处理 fileSource 为 "lkg" 或 "atlas" 的文件
        dailyFiles.stream()
                .filter(entity -> "lkg".equals(entity.getFileSource()) || "atlas".equals(entity.getFileSource()))
                .forEach(entity -> processDailyFile(entity, file, closeLogList));

        log.info("closeDailyContract closeDate:{}, closeLogList:{}",
                DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), closeLogList);
        return closeLogList;
    }

    /**
     * 处理单个每日文件：
     * 1. 根据传入的 MultipartFile 或文件路径读取 CSV 数据；
     * 2. 遍历 CSV 数据（从第二行开始）解析合同状态及合同号；
     * 3. 针对状态为 7 的合同进行关闭操作（包含 Redis 防重复、合同实际状态检查等）；
     * 4. 更新该每日文件的处理状态。
     */
    private void processDailyFile(LkgDailyFileEntity entity, MultipartFile file, List<String> closeLogList) {
        List<String> csvData = readCsvData(entity, file);
        log.info("======读取的csv文件=======:{}", csvData);
        if (CollUtil.isEmpty(csvData)) {
            return;
        }

        // 从第二行开始遍历（跳过表头）
        for (int i = 1; i < csvData.size(); i++) {
            try {
                String line = csvData.get(i);
                String[] split = line.split(",");

                // 处理csv中的双引号
                split = Arrays.stream(split).map(s -> s.replace("\"", "")).toArray(String[]::new);

                // 提取合同编号 lkg 文件合同号在第一列 atlas 文件合同号在第二列
                String contractNumber = "lkg".equals(entity.getFileSource()) ? split[0] : split[1];

                // 根据文件来源和销售类型解析合同状态
                int status = getContractStatus(entity, split);

                // 只有状态为 7 的合同才进行关闭
                if (status != 7) {
                    continue;
                }

                // 若 Redis 中已记录该合同为关闭，则跳过后续操作
                if (redisUtil.hasKey("lkg:close:contract:" + contractNumber)) {
                    continue;
                }

                // 检查该合同是否已处于关闭状态（正本合同无需重复关闭）
                ContractEntity contractEntity = contractFacade.getBasicContractByCode(contractNumber);
                if (contractEntity == null || contractEntity.getStatus() == ContractStatusEnum.CLOSED.getValue()) {
                    log.warn("Contract is null or already closed: {}", contractNumber);
                    continue;
                }

                // 获取账套 lkg-entity atlas-file
                String fileSource = entity.getFileSource();
                String siteId = "lkg".equals(fileSource) ? entity.getSiteId() : StringUtils.defaultIfBlank(split[0], "");
                if (StringUtils.isBlank(siteId)) {
                    log.warn("siteId is blank for fileSource: {}", fileSource);
                    continue;
                }

                String systemCode = "lkg".equals(fileSource) ? SyncSystemEnum.LINKINAGE.getValue() : SyncSystemEnum.ATLAS.getValue();
                SiteEntity siteEntity = siteFacade.getSiteBySystemCode(systemCode, siteId);
                if (siteEntity == null) {
                    log.warn("No SiteEntity found for systemCode: {}, siteId: {}", systemCode, siteId);
                    continue;
                }

                // 根据账套和合同号进行合同关闭操作
                Result result = contractFacade.closedBySiteCodeAndContractCode(siteEntity.getCode(), contractNumber);
                if (result.isSuccess() && result.getData() != null && (Boolean) result.getData()) {
                    closeLogList.add(contractNumber);
                    // 使用 Redis 记录关闭状态，设置 3 小时过期时间防止重复关闭
                    redisUtil.set("lkg:close:contract:" + contractNumber, "7", 10800);
                }
            } catch (Exception e) {
                log.error("closeDailyContract Error Line:{}, message:{}", i, e.getMessage());
            }
        }

        // 更新当前每日文件的处理状态为 FILE_HANDLING
        entity.setHandleStatus(LkgDailyFileHandleStatusEnum.FILE_HANDLING.getValue());
        lkgDailyFileDao.updateById(entity);
    }

    /**
     * 根据传入的 MultipartFile 或文件路径读取 CSV 数据。
     * <p>
     * 1. 如果 MultipartFile 不为空，则优先通过文件流读取；
     * 2. 否则判断 filePath 是否为 URL 地址，并分别调用 lkg 或 atlas 对应的读取方法；
     * 3. 若非 URL，则按本地文件读取。
     * </p>
     */
    private List<String> readCsvData(LkgDailyFileEntity entity, MultipartFile file) {
        List<String> csvData = new ArrayList<>();
        String filePath = entity.getFilePath();

        if (file != null) {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    csvData.add(line);
                }
            } catch (IOException e) {
                throw new RuntimeException("Error reading CSV from MultipartFile", e);
            }
        } else {
            // 判断 filePath 是否为 URL 地址
            if (filePath.matches("^(http|https)://.*$")) {
                csvData = "lkg".equals(entity.getFileSource()) ? lkgReadCsv(filePath) : atlasReadCsv(filePath);
            } else {
                // 从本地文件系统中读取 CSV 数据
                try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        csvData.add(line);
                    }
                } catch (IOException e) {
                    log.error("closeDailyContract read csv error: {}", e.getMessage());
                }
            }
        }
        return csvData;
    }

    /**
     * 根据文件来源和 CSV 行数据解析合同状态。
     * <p>
     * 对于 lkg 文件：
     * <ul>
     *   <li>若销售类型为采购合同，则从下标 9 获取状态；</li>
     *   <li>否则从下标 13 获取状态。</li>
     * </ul>
     * 对于 atlas 文件，则从下标 2 获取状态。
     * </p>
     */
    private int getContractStatus(LkgDailyFileEntity entity, String[] split) {
        int status = -1;
        if ("lkg".equals(entity.getFileSource())) {
            if (entity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
                status = StringUtils.isBlank(split[9]) ? -1 : Integer.parseInt(split[9]);
            } else {
                status = StringUtils.isBlank(split[13]) ? -1 : Integer.parseInt(split[13]);
            }
        } else if ("atlas".equals(entity.getFileSource())) {
            status = StringUtils.isBlank(split[2]) ? -1 : Integer.parseInt(split[2]);
        }
        return status;
    }


    /**
     * 获取 dbi_lkg_daily_file 记录，每次一条
     * 根据dbi_lkg_daily_file的记录，获取文件并逐条落地数据 dbi_lkg_daily_contract
     * （TBD）每落地一条数据，新增dbi_lkg_daily_contract，并更新dbi_lkg_daily_file
     * 落地完成后，更新dbi_lkg_daily_file的状态
     * 全部落地完成后，更改dbi_lkg_daily_task的状态为 CHECK_CONTRACT_NAV
     */
    @Override
    public void createDailyContractRecord(String checkDate) {
        LkgDailyFileEntity dfEntity = new LkgDailyFileEntity();
        dfEntity.setCheckDate(checkDate)
                .setHandleStatus(LkgDailyFileHandleStatusEnum.PULL.getValue());
        List<LkgDailyFileEntity> lkgDailyFileList = lkgDailyFileDao.queryList(dfEntity);
        if (CollUtil.isNotEmpty(lkgDailyFileList)) {
            for (LkgDailyFileEntity fileEntity : lkgDailyFileList) {
                /**
                 * 这里有nav和lkg的文件路径
                 * nav用的是blob读取文件流的方式
                 * lkg那边是通过url读取文件信息
                 */
                List<String> csvList = new ArrayList<>();
                if ("lkg".equals(fileEntity.getFileSource())) {
                    csvList = lkgReadCsv(fileEntity.getFilePath());
                } else {
                    csvList = navReadCsv(fileEntity.getFilePath(), fileEntity.getFileName());
                }
                if (CollUtil.isNotEmpty(csvList)) {
                    for (int i = 0; i < csvList.size(); i++) {
                        //从第二行开始读
                        if (i > 0) {
                            String[] split = csvList.get(i).split(",");
                            LkgDailyContractEntity lkgDailyContractEntity = createLkgDailyContractEntity(fileEntity, split);
                            if (null == lkgDailyContractEntity) {
                                continue;
                            }

                            // 处理合同关闭 - 根据lkg文件已关闭合同关闭nav合同状态
                            try {
                                if ("lkg".equals(lkgDailyContractEntity.getFileSource()) && lkgDailyContractEntity.getStatus() == 7) {
                                    contractFacade.updateContractByCode(lkgDailyContractEntity.getContractNumber(), ContractStatusEnum.CLOSED.getValue());

                                    lkgDailyContractEntity.setRemark("lkg的合同状态关闭，nav合同更新关闭状态");
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            lkgDailyContractDao.save(lkgDailyContractEntity);
                            fileEntity.setTotalCount(csvList.size())
                                    //.setHandleStatus(LkgDailyFileHandleStatusEnum.FILE_HANDLING.getValue())
                                    .setCurrentCount(fileEntity.getCurrentCount() + 1);
                            lkgDailyFileDao.updateById(fileEntity);
                        }
                    }
                    fileEntity.setHandleStatus(LkgDailyFileHandleStatusEnum.FILE_HANDLED_OVER.getValue());
                    lkgDailyFileDao.updateById(fileEntity);
                }
            }
        }
        changeDailyTaskStatus(checkDate);
        //changeStatus(checkDate, ContractSalesTypeEnum.PURCHASE.getValue());
        //changeStatus(checkDate, ContractSalesTypeEnum.SALES.getValue());
    }


    @Override
    public void checkContractByNav(String checkDate) {
        /**
         * dbi_lkg_daily_contract
         * 先以NAV合同为主进行校核
         * 每次获取100条来自于NAV的合同（contractSource=nav，checkDay=当天，未处理）逐条处理
         *  根据合同号获取来自于LKG的合同（contractSource=lkg，checkDay=当天）
         *  校核：总量、账套、定价量是否一致，记录dbi_lkg_daily_check，并更新dbi_lkg_daily_contract
         * 连续获取4分钟（不精确）
         */
        long t1 = new Timestamp(System.currentTimeMillis()).getTime();


        boolean processing = true;

        while (processing) {
            long t2 = new Timestamp(System.currentTimeMillis()).getTime();
            if (t2 - t1 > MAX_PROCESS_TIME) {
                break;
            }
            //获取销售的数据 dbi_lkg_daily_contract(销售、nav、未处理，100条）
            LkgDailyContractEntity sdcEntity = new LkgDailyContractEntity();
            sdcEntity.setSalesType(ContractSalesTypeEnum.SALES.getValue())
                    .setFileSource("nav")
                    .setCheckDate(checkDate)
                    .setCheckStatus(0);
            List<LkgDailyContractEntity> sList = lkgDailyContractDao.queryList(sdcEntity);


            //List list=new ArrayList();
            //for (Object navContract : list) {
            if (CollUtil.isNotEmpty(sList)) {
                for (int i = 0; i < sList.size(); i++) {
                    //逐条处理 dbi_lkg_daily_contract（合同号，lkg，1条）
                    LkgDailyContractEntity navContract = sList.get(i);
                    sdcEntity.setFileSource("lkg")
                            .setCheckDate(checkDate)
                            .setContractNumber(navContract.getContractNumber());
                    LkgDailyContractEntity lkgContract = lkgDailyContractDao.getDailyContractEntity(sdcEntity);
                    //记录到 dbi_lkg_check_detail
                    checkContract(navContract, lkgContract);
                    //update navContract.check_status=1 两个都要更新
                    //update lkgContract.check_status=1 两个都要更新
                    navContract.setCheckStatus(1);
                    lkgDailyContractDao.updateById(navContract);
                    if (lkgContract != null) {
                        lkgContract.setCheckStatus(1);
                        lkgDailyContractDao.updateById(lkgContract);
                    }
                    if (i == 100) {
                        break;
                    }
                }
            }


            //如果销售数据没有，获取采购的数据
            else {
                //获取销售的数据 dbi_lkg_daily_contract(采购、nav、未处理，100条）
                LkgDailyContractEntity pdcEntity = new LkgDailyContractEntity();
                pdcEntity.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                        .setFileSource("nav")
                        .setCheckDate(checkDate)
                        .setCheckStatus(0);
                List<LkgDailyContractEntity> pList = lkgDailyContractDao.queryList(pdcEntity);

                if (CollUtil.isNotEmpty(pList)) {
                    //同上
                    for (int i = 0; i < pList.size(); i++) {
                        //逐条处理 dbi_lkg_daily_contract（合同号，lkg，1条）
                        LkgDailyContractEntity navContract = pList.get(i);
                        pdcEntity.setFileSource("lkg")
                                .setCheckDate(checkDate)
                                .setContractNumber(navContract.getContractNumber());
                        LkgDailyContractEntity lkgContract = lkgDailyContractDao.getDailyContractEntity(pdcEntity);

                        //Object lkgContract=null;
                        //记录到 dbi_lkg_check_detail
                        checkContract(navContract, lkgContract);
                        //update navContract.check_status=1 两个都要更新
                        //update lkgContract.check_status=1 两个都要更新
                        navContract.setCheckStatus(1);
                        lkgDailyContractDao.updateById(navContract);
                        if (lkgContract != null) {
                            lkgContract.setCheckStatus(1);
                            lkgDailyContractDao.updateById(lkgContract);
                        }
                        if (i == 100) {
                            break;
                        }
                    }
                } else {//如果采购也没有数据了，则更新dbi_lkg_daily_task.handleStatus=CHECK_CONTRACT_LKG
                    LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
                    tEntity.setCheckDate(checkDate)
                            .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_NAV.getValue());
                    List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(tEntity);

                    for (LkgDailyTaskEntity taskEntity : list) {
                        taskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_LKG.getValue());
                        lkgDailyTaskDao.updateById(taskEntity);
                    }
                }

            }
        }


    }

    @Override
    public void checkContractByLkg(String checkDate) {
        /**
         * 然后再以LKG合同为主进行校核
         * 每次获取100条来自于LKG的合同（contractSource=lkg，checkDay=当天，未处理）逐条处理
         *  根据合同号获取来自于LKG的合同（contractSource=nav，checkDay=当天）
         *  校核：账套、总量、定价量是否一致，并更新result
         * 连续获取4分钟（不精确）
         */

        //整体逻辑同上

        long t1 = new Timestamp(System.currentTimeMillis()).getTime();


        boolean processing = true;

        while (processing) {
            long t2 = new Timestamp(System.currentTimeMillis()).getTime();
            if (t2 - t1 > MAX_PROCESS_TIME) {
                break;
            }
            LkgDailyContractEntity sdcEntity = new LkgDailyContractEntity();
            sdcEntity.setSalesType(ContractSalesTypeEnum.SALES.getValue())
                    .setFileSource("lkg")
                    .setCheckDate(checkDate)
                    .setCheckStatus(0);
            List<LkgDailyContractEntity> sList = lkgDailyContractDao.queryList(sdcEntity);


            //List list=new ArrayList();
            //for (Object navContract : list) {
            if (CollUtil.isNotEmpty(sList)) {
                for (int i = 0; i < sList.size(); i++) {
                    //逐条处理 dbi_lkg_daily_contract（合同号，nav，1条）
                    LkgDailyContractEntity lkgContract = sList.get(i);
                    sdcEntity.setFileSource("nav")
                            .setCheckDate(checkDate)
                            .setContractNumber(lkgContract.getContractNumber());
                    LkgDailyContractEntity navContract = lkgDailyContractDao.getDailyContractEntity(sdcEntity);

                    //Object lkgContract=null;
                    //记录到 dbi_lkg_check_detail
                    checkContract(navContract, lkgContract);
                    //update navContract.check_status=1 两个都要更新
                    //update lkgContract.check_status=1 两个都要更新
                    lkgContract.setCheckStatus(1);
                    lkgDailyContractDao.updateById(lkgContract);
                    if (navContract != null) {
                        navContract.setCheckStatus(1);
                        lkgDailyContractDao.updateById(navContract);
                    }
                    if (i == 100) {
                        break;
                    }
                }
            }


            //如果销售数据没有，获取采购的数据
            else {
                //获取销售的数据 dbi_lkg_daily_contract(采购、lkg、未处理，100条）
                LkgDailyContractEntity pdcEntity = new LkgDailyContractEntity();
                pdcEntity.setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                        .setFileSource("lkg")
                        .setCheckDate(checkDate)
                        .setCheckStatus(0);
                List<LkgDailyContractEntity> pList = lkgDailyContractDao.queryList(pdcEntity);

                if (!CommonListUtil.isNullOrEmpty(pList)) {
                    //同上
                    for (int i = 0; i < pList.size(); i++) {
                        //逐条处理 dbi_lkg_daily_contract（合同号，nav，1条）
                        System.out.println("i=" + i + "     cList.size=" + pList.size());
                        LkgDailyContractEntity lkgContract = pList.get(i);
                        pdcEntity.setFileSource("nav")
                                .setCheckDate(checkDate)
                                .setContractNumber(lkgContract.getContractNumber());
                        LkgDailyContractEntity navContract = lkgDailyContractDao.getDailyContractEntity(pdcEntity);

                        //Object lkgContract=null;
                        //记录到 dbi_lkg_check_detail
                        checkContract(navContract, lkgContract);
                        //update navContract.check_status=1 两个都要更新
                        //update lkgContract.check_status=1 两个都要更新
                        lkgContract.setCheckStatus(1);
                        lkgDailyContractDao.updateById(lkgContract);
                        if (navContract != null) {
                            navContract.setCheckStatus(1);
                            lkgDailyContractDao.updateById(navContract);
                        }
                        if (i == 100) {
                            break;
                        }
                    }
                } else {//如果采购也没有数据了，则更新dbi_lkg_daily_task.handleStatus=CREATE_CHECK_REPORT
                    LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
                    tEntity.setCheckDate(checkDate)
                            .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_LKG.getValue());
                    List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(tEntity);

                    for (LkgDailyTaskEntity taskEntity : list) {
                        taskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CREATE_CHECK_REPORT.getValue());
                        lkgDailyTaskDao.updateById(taskEntity);
                    }
                }

            }
        }

    }

    @Override
    public void sendDailyCheckReport(String checkDate) {
        log.info("====是否发送预警====");
        List<LkgDailyCheckDetailEntity> lkgDailyCheckDetailList = lkgDailyCheckDetailDao.queryList();
        if (CollUtil.isNotEmpty(lkgDailyCheckDetailList)) {
            log.info("============需要发送预警消息============");
    /*        SendMessageDTO sendMessageDTO = new SendMessageDTO();
            ReceiverContactVO receiverContactVO = new ReceiverContactVO();
            receiverContactVO.setEmail("<EMAIL>");
            sendMessageDTO
                    .setReceiverContactVO(receiverContactVO)
                    .setTitle("合同校核")
                    .setSendContent("今日合同校核有未通过的数据,请排查");*/
            Result result = messageFacade.sendEmailInfo(checkNotice, "合同校核", "今日合同校核有未通过的数据,请排查");
            if (ResultCodeEnum.OK.getCode() == result.getCode()) {
                log.info("============发送成功============");
                LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
                tEntity.setCheckDate(checkDate)
                        .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CREATE_CHECK_REPORT.getValue());
                List<LkgDailyTaskEntity> list = lkgDailyTaskDao.queryList(tEntity);

                for (LkgDailyTaskEntity taskEntity : list) {
                    taskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_FINISH.getValue());
                    lkgDailyTaskDao.updateById(taskEntity);
                }
            }

        }
    }

    public void checkContract(LkgDailyContractEntity navContract, LkgDailyContractEntity lkgContract) {
        //nav系统中的生效中的状态(2,3,6,8)
        List<Integer> navStatus = Arrays.asList(ContractStatusEnum.EFFECTIVE.getValue(), ContractStatusEnum.MODIFYING.getValue(), ContractStatusEnum.SPLITTING.getValue(), ContractStatusEnum.CLOSING.getValue());
        List<Integer> lkgStatus = Arrays.asList(2, 3, 4, 5, 6);

        StringJoiner resultMemo = new StringJoiner("未通过原因：");
        LkgDailyCheckDetailEntity lkgDailyCheckDetail = new LkgDailyCheckDetailEntity();
        lkgDailyCheckDetail.setResult(LkgCheckResultEnum.CHECK_PASS.getStatus());
        lkgDailyCheckDetail
                .setNavCount(navContract == null ? new BigDecimal(0) : navContract.getCount())
                .setNavSiteId(navContract == null ? "" : navContract.getSiteId())
                .setNavStatus(navContract == null ? -1 : navContract.getStatus())
                .setContractCode(navContract == null ? "" : navContract.getContractNumber())
                .setNavSiteId(navContract == null ? "" : navContract.getSiteId())
                .setLkgCount(lkgContract == null ? new BigDecimal(0) : lkgContract.getCount())
                .setLkgSiteId(lkgContract == null ? "" : lkgContract.getSiteId())
                .setLkgStatus(lkgContract == null ? -1 : lkgContract.getStatus())
                .setLkgContractCode(lkgContract == null ? "" : lkgContract.getContractNumber())
                .setLkgSiteId(lkgContract == null ? "" : lkgContract.getSiteId())

                .setSalesType(navContract == null ? -1 : navContract.getSalesType())
                .setCheckDay(navContract == null ? (lkgContract == null ? "" : lkgContract.getCheckDate()) : navContract.getCheckDate());
        if (!lkgDailyCheckDetail.getNavCount().equals(lkgDailyCheckDetail.getLkgCount())) {
            resultMemo.add("合同量不一致 ");
            lkgDailyCheckDetail.setResultMemo(resultMemo.toString());
            lkgDailyCheckDetail.setResult(LkgCheckResultEnum.CHECK_NO_PASS.getStatus());
        }
        if (!lkgDailyCheckDetail.getNavStatus().equals(lkgDailyCheckDetail.getLkgStatus())) {
     /*         }else if(navContract == null || lkgContract == null ||
                      (!navStatus.contains(navContract.getStatus()) &&
                              !lkgStatus.contains(lkgContract.getStatus()))){*/
            resultMemo.add("合同状态不一致 ");
            lkgDailyCheckDetail.setResultMemo(resultMemo.toString());
            lkgDailyCheckDetail.setResult(LkgCheckResultEnum.CHECK_NO_PASS.getStatus());
            //如果lkg合同状态是关闭
            if (StringUtils.isNotBlank(lkgDailyCheckDetail.getLkgContractCode()) &&
                    ContractStatusEnum.CLOSED.getValue() == lkgDailyCheckDetail.getLkgStatus()) {
                resultMemo.add("lkg的合同状态是已关闭,nav相应的合同需要关闭");
                lkgDailyCheckDetail.setResultMemo(resultMemo.toString());
                log.info("需要关闭合同编号是" + lkgDailyCheckDetail.getLkgContractCode());
                contractFacade.updateContractByCode(lkgDailyCheckDetail.getLkgContractCode(), ContractStatusEnum.CLOSED.getValue());
            }
        }
     /*       if (navContract == null || lkgContract == null
                    || !navContract.getCount().equals(lkgContract.getCount())) {
                resultMemo.add("合同量不一致 ");
                lkgDailyCheckDetail.setResultMemo(resultMemo.toString());
                lkgDailyCheckDetail.setResult(LkgCheckResultEnum.CHECK_NO_PASS.getStatus());
            }
            if (navContract == null || lkgContract == null
                    || !navContract.getStatus().equals(lkgContract.getStatus())) {
                resultMemo.add("合同状态不一致 ");
                lkgDailyCheckDetail.setResultMemo(resultMemo.toString());
                lkgDailyCheckDetail.setResult(LkgCheckResultEnum.CHECK_NO_PASS.getStatus());
            }*/
        lkgDailyCheckDetailDao.save(lkgDailyCheckDetail);
    }


    public LkgDailyContractEntity createLkgDailyContractEntity(LkgDailyFileEntity fileEntity, String[] split) {
        LkgDailyContractEntity lkgDailyContractEntity = new LkgDailyContractEntity();
        //采销类型字段不一样
        try {
            lkgDailyContractEntity
                    .setCheckStatus(LkgDailyContractCheckStatusEnum.UN_CHECK.getValue())
                    .setFileName(fileEntity.getFileName())
                    .setSalesType(fileEntity.getSalesType())
                    .setFileSource(fileEntity.getFileSource())
                    .setHandleTime(new Date())
                    .setSiteId(fileEntity.getSiteId())
                    .setCheckDate(fileEntity.getCheckDate())
                    .setContractNumber(StringUtils.isBlank(split[0]) ? "" : split[0])
                    .setRefcontractNumber(StringUtils.isBlank(split[1]) ? "" : split[1])
                    .setPriceType(StringUtils.isBlank(split[2]) ? -1 : Integer.parseInt(split[2]))
                    .setCommodityCode(StringUtils.isBlank(split[4]) ? "" : split[4])
                    .setCount(BigDecimalUtil.initBigDecimal(split[5]));
            if (ContractSalesTypeEnum.PURCHASE.getValue() == fileEntity.getSalesType()) {
                lkgDailyContractEntity.setSupplierCode(StringUtils.isBlank(split[3]) ? "" : split[3])
                        .setInCount(BigDecimalUtil.initBigDecimal(split[6]))
                        .setNointCount(BigDecimalUtil.initBigDecimal(split[7]))
                        .setFixStatus(StringUtils.isBlank(split[8]) ? -1 : Integer.parseInt(split[8]))
                        .setStatus(StringUtils.isBlank(split[9]) ? -1 : Integer.parseInt(split[9]));
                //.setRemark(StringUtils.isBlank(split[10]) ? "" : split[10]);
            } else {
                lkgDailyContractEntity.setCustomerCode(StringUtils.isBlank(split[3]) ? "" : split[3])
                        .setContractOutcount(BigDecimalUtil.initBigDecimal(split[6]))
                        .setContractNooutcount(BigDecimalUtil.initBigDecimal(split[7]))
                        .setContractFactoutcount(BigDecimalUtil.initBigDecimal(split[8]))
                        .setContractNosendcount(BigDecimalUtil.initBigDecimal(split[9]))
                        .setTolsettleCount(BigDecimalUtil.initBigDecimal(split[10]))
                        .setTolnosettleCount(BigDecimalUtil.initBigDecimal(split[11]))
                        .setFixStatus(StringUtils.isBlank(split[12]) ? -1 : Integer.parseInt(split[12]))
                        .setStatus(StringUtils.isBlank(split[13]) ? -1 : Integer.parseInt(split[13]));
                //.setRemark(StringUtils.isBlank(split[14]) ? "" : split[14]);
            }

            return lkgDailyContractEntity;
        } catch (Exception e) {
            return null;
        }
    }

    public void changeDailyTaskStatus(String checkDate) {
        LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
        tEntity.setCheckDate(checkDate)
                .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.RECORD_CONTRACT_DATA.getValue());
        List<LkgDailyTaskEntity> dailyTaskList = lkgDailyTaskDao.queryList(tEntity);
        for (LkgDailyTaskEntity dailyTaskEntity : dailyTaskList) {
            dailyTaskEntity.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_NAV.getValue());
            lkgDailyTaskDao.updateById(dailyTaskEntity);
        }
    }

    public void changeStatus(String checkDate, Integer salesType) {
        LkgDailyFileEntity dfEntity = new LkgDailyFileEntity();
        dfEntity.setCheckDate(checkDate)
                .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.INIT.getValue())
                .setSalesType(salesType);
        List<LkgDailyFileEntity> list = lkgDailyFileDao.queryList(dfEntity);

        if (CollUtil.isNotEmpty(list)) {
            LkgDailyTaskEntity tEntity = new LkgDailyTaskEntity();
            tEntity.setCheckDate(checkDate)
                    .setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.RECORD_CONTRACT_DATA.getValue())
                    .setSalesType(salesType);
            List<LkgDailyTaskEntity> tlist = lkgDailyTaskDao.queryList(tEntity);
            for (LkgDailyTaskEntity ldte : tlist) {
                ldte.setHandleStatus(LkgDailyCheckTaskHandleStatusEnum.CHECK_CONTRACT_NAV.getValue());
                lkgDailyTaskDao.updateById(ldte);
            }
        }
    }

    public static File[] searchFile(File folder) {// 递归查找包含关键字的文件

        File[] subFolders = folder.listFiles();

        List result = new ArrayList();// 声明一个集合

        for (int i = 0; i < subFolders.length; i++) {// 循环显示文件夹或文件

            if (subFolders[i].isFile()) {// 如果是文件则将文件添加到结果列表中

                result.add(subFolders[i]);

            } else {// 如果是文件夹，则递归调用本方法，然后把所有的文件加到结果列表中

                File[] foldResult = searchFile(subFolders[i]);

                for (int j = 0; j < foldResult.length; j++) {// 循环显示文件

                    result.add(foldResult[j]);// 文件保存到集合中

                }

            }

        }

        File files[] = new File[result.size()];// 声明文件数组，长度为集合的长度

        result.toArray(files);// 集合数组化

        return files;

    }

    public static ArrayList<String> readCsvByBufferedReader(String filePath) {
        File csv = new File(filePath);
        boolean setReadableFlag = csv.setReadable(true);
        boolean setWritableFlag = csv.setWritable(true);
        if (setReadableFlag && setWritableFlag) {

        }
        InputStreamReader isr = null;
        BufferedReader br = null;
        ArrayList<String> records = null;
        try {
            isr = new InputStreamReader(new FileInputStream(csv), "UTF-8");
            br = new BufferedReader(isr);
            String line = "";
            records = new ArrayList<>();
            if (br != null) {
                while ((line = br.readLine()) != null) {
                    System.out.println(line);
                    records.add(line);
                }
            }
            System.out.println("csv表格读取行数：" + records.size());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    isr.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return records;
    }


    /**
     * nav通过blob读取文件信息
     *
     * @param filePath
     * @param fileName
     * @return
     */
    public List<String> navReadCsv(String filePath, String fileName) {
        ArrayList<String> records = new ArrayList<>();
        String line = "";
        try {
            log.info("读取nav文件的路径：" + filePath + fileName);
            InputStreamReader isr = azureBlobUtil.csvFileToStream(filePath, fileName);
            if (isr != null) {
                BufferedReader br = new BufferedReader(isr);
                while ((line = br.readLine()) != null) {
                    records.add(line);
                }
            }
        } catch (IOException e) {
            log.info(filePath + fileName + "该文件路径在blob上不存在");
            return records;
        }
        return records;
    }

    /**
     * lkg通过建立url去读取文件信息
     *
     * @param filePath
     * @return
     */
    public List<String> lkgReadCsv(String filePath) {
        List<String> records = new ArrayList<>();
        try {
            String line = "";
            log.info("获取lkg的文件全路径是：" + filePath);
            URL url = new URL(filePath);
            OkSslUtil.trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier(OkSslUtil.hv);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.connect();
            InputStream inputStream = urlConnection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            while ((line = br.readLine()) != null) {
                records.add(line);
            }
            log.info("csv表格读取行数：" + records.size());
            urlConnection.disconnect();
            inputStream.close();
            br.close();
        } catch (Exception e) {
            log.info(e.toString());
            log.info("获取lkg文件路径有误{}", filePath);
            return records;
        }
        return records;
    }

    /**
     * atlas通过url读取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    public List<String> atlasReadCsv(String filePath) {
        List<String> records = new ArrayList<>();
        try (InputStreamReader isr = azureBlobUtil.closeCsvFileToStream(filePath);
             BufferedReader br = isr != null ? new BufferedReader(isr) : null) {
            log.info("读取atlas文件的路径：" + filePath);
            if (br != null) {
                String line;
                while ((line = br.readLine()) != null) {
                    records.add(line);
                }
            }
        } catch (IOException e) {
            log.info(filePath + "该文件路径在blob上不存在");
        }
        return records;
    }

    @Override
    public boolean saveDailyFile(LkgDailyFileEntity lkgDailyFileEntity) {
        return lkgDailyFileDao.save(lkgDailyFileEntity);
    }
}
