package com.navigator.pigeon.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgDailyTaskMapper;
import com.navigator.pigeon.pojo.entity.LkgDailyTaskEntity;

import java.util.List;

/**
 * @Author: wang tao
 * @Date: 2022/6/13
 * @Time: 18:35
 * @Desception:
 */
@Dao
public class LkgDailyTaskDao extends BaseDaoImpl<DbiLkgDailyTaskMapper, LkgDailyTaskEntity> {
    public List<LkgDailyTaskEntity> queryList(LkgDailyTaskEntity lkgDailyTaskEntity){
        LambdaQueryWrapper<LkgDailyTaskEntity> wrapper = new LambdaQueryWrapper<LkgDailyTaskEntity>()
                .eq(StrUtil.isNotBlank(lkgDailyTaskEntity.getCheckDate()),LkgDailyTaskEntity::getCheckDate,lkgDailyTaskEntity.getCheckDate())
                .eq(null!=lkgDailyTaskEntity.getHandleStatus(),LkgDailyTaskEntity::getHandleStatus,lkgDailyTaskEntity.getHandleStatus())
                .eq(null!=lkgDailyTaskEntity.getSalesType(),LkgDailyTaskEntity::getSalesType,lkgDailyTaskEntity.getSalesType())
                ;

        return this.baseMapper.selectList(wrapper);
    }
}
