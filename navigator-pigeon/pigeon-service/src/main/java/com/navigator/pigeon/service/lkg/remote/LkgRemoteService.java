package com.navigator.pigeon.service.lkg.remote;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.SHAUtil;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.pigeon.dao.LkgOperationLogDao;
import com.navigator.pigeon.dao.LkgSyncRecordDao;
import com.navigator.pigeon.dao.LkgSyncRequestDao;
import com.navigator.pigeon.pojo.dto.LkgInterfaceRequestDTO;
import com.navigator.pigeon.pojo.dto.LkgInterfaceResponseDTO;
import com.navigator.pigeon.pojo.dto.LkgQueryRecordDTO;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;
import com.navigator.pigeon.pojo.entity.LkgOperationLogEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRequestEntity;
import com.navigator.pigeon.pojo.enums.LkgInterfaceActionEnum;
import com.navigator.pigeon.pojo.enums.LkgInterfaceEnum;
import com.navigator.pigeon.pojo.enums.LkgResponseCodeEnum;
import com.navigator.pigeon.pojo.enums.LkgSyncStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@RefreshScope
public class LkgRemoteService {

    @Value("${syncLkgInfo.open}")
    private String lkgOpen;
    @Value("${lkgNotice.email}")
    private String lkgNotice;
    /*@Value("${spring.profiles.active}")
    private String profileActive;*/

    @Resource
    LkgRemoteFacade lkgRemoteFacade;
    @Resource
    LkgSyncRequestDao lkgSyncRequestDao;
    @Resource
    LkgSyncRecordDao lkgSyncRecordDao;
    @Resource
    LkgOperationLogDao lkgOperationLogDao;
    @Resource
    MessageFacade messageFacade;

    private static final String appKey = "10001";
    private static final String appSecret = "F78A81FC72B1498898ED023C0217B89FEEEEEEEEEEEERRRRRRRRRRRRRRRR44444444444444";
    private String timeStamp = new Timestamp(System.currentTimeMillis()).getTime() + "";

    public LkgSyncRecordEntity sendSyncContract(LkgSyncRecordEntity lkgSyncRecordEntity) {
        log.info("==============LkgRemoteService.sendSyncContract===============");
        log.info("lkgSyncRecordEntity:{}", JSON.toJSONString(lkgSyncRecordEntity));

        LkgInterfaceRequestDTO lkgInterfaceRequestDTO = new LkgInterfaceRequestDTO();

        LkgInterfaceActionEnum lkgInterfaceActionEnum = LkgInterfaceActionEnum.getBySyncType(lkgSyncRecordEntity.getSyncType());
        String transCode = getTransCode(lkgSyncRecordEntity.getSalesType(), lkgInterfaceActionEnum.getActionName());

        String siteId = lkgSyncRecordEntity.getSiteId();

        //nonce=id-synctpe-timestamp-随机数
        StringBuilder nonce = new StringBuilder();
        nonce.append(lkgSyncRecordEntity.getId().toString()).append("-")
                .append(siteId).append("-")
                .append(lkgSyncRecordEntity.getSyncType().toString()).append("-")
                .append(timeStamp).append("-")
                .append(RandomUtil.randomNumbers(6));

        //组装Data
        lkgInterfaceRequestDTO.setData(lkgSyncRecordEntity.getLkgReqestInfo())
                .setAppKey(appKey)
                .setNonce(nonce.toString())
                .setTimeStamp(timeStamp)
                .setSiteId(siteId)
                .setTransCode(transCode);

        //加密参数
        String signkey = getAppSign(lkgInterfaceRequestDTO);
        lkgInterfaceRequestDTO.setAppSign(signkey);

        log.info("lkgInterfaceRequestDTO-{}:{}", lkgSyncRecordEntity.getContractCode(), JSON.toJSONString(lkgInterfaceRequestDTO));

        //调用接口并获取返回值
        LkgInterfaceResponseDTO responseDTO = null;

        Date requestStart = new Date();

        try {
            if ("1".equals(lkgOpen)) {
                responseDTO = lkgRemoteFacade.syncLkgInfo(lkgInterfaceRequestDTO);
                log.info("开启调用LKG同步接口返回值:{}", JSONObject.toJSONString(responseDTO));
            } else {
                responseDTO = new LkgInterfaceResponseDTO();
                responseDTO.setCode(0)
                        .setMsg("无需同步")
                        .setFailCode(LkgResponseCodeEnum.INTERFACE_QUERY_CLOSE.getCode());
                log.info("关闭调用LKG同步接口返回值:{}", JSONObject.toJSONString(responseDTO));
            }

        } catch (Exception e) {
            responseDTO = new LkgInterfaceResponseDTO();
            responseDTO.setCode(0)
                    .setMsg("调用LKG接口异常")
                    .setFailCode(LkgResponseCodeEnum.INTERFACE_ERROR.getCode());
            log.error("调用LKG查询接口异常:{}", e.getMessage());

            // 标题
            String title = "【" + SpringContextUtil.getEnv() + "】LKG系统异常";

            // 内容
            String content = "【紧急】合同编号：" + lkgSyncRecordEntity.getContractCode()
                    + "，recordID：" + lkgSyncRecordEntity.getId()
                    + "，nonce：" + nonce
                    + "，errorMsg：" + e.getMessage()
                    + "，调用LKG接口异常,请及时排查原因！！！";

            //  发送邮件通知
            messageFacade.sendEmailInfo(lkgNotice, title, content);
        }

        Date requestEnd = new Date();


        //更新记录
        LkgSyncRequestEntity lkgSyncRequestEntity = lkgSyncRequestDao.getById(lkgSyncRecordEntity.getRequestId());

        lkgSyncRecordEntity.setLkgResultsInfo(JSON.toJSONString(responseDTO))
                .setTryTimes(lkgSyncRecordEntity.getTryTimes() + 1)
                .setNonce(nonce.toString())
                .setSyncTime(new Date());

        if (responseDTO.getCode() == 1) {
            lkgSyncRecordEntity.setSyncStatus(LkgSyncStatusEnum.COMPLATE.getValue());
            lkgSyncRequestEntity.setSyncStatus(LkgSyncStatusEnum.COMPLATE.getValue())
                    .setSyncTime(new Date());

        } else {
            lkgSyncRecordEntity.setSyncStatus(LkgSyncStatusEnum.SYNCING.getValue());
            lkgSyncRequestEntity.setSyncStatus(LkgSyncStatusEnum.SYNCING.getValue())
                    .setSyncTime(new Date());

            // 特殊处理的异常信息
            List<String> msgList = Arrays.asList("本合同对应客户无CNF销售资格，请进行资格认定后再签发",
                    "该客户存信用资格已锁定，请解锁后再签发！",
                    "本合同对应客户无赊销资格，请进行资格认定后再签发");

            // 特殊处理的异常信息-失败后的重试
            String retryMessage = "违反了 PRIMARY KEY 约束";

            if (responseDTO.getFailCode().equals(LkgResponseCodeEnum.SYSTEM_ERROR.getCode())
                    && (msgList.contains(responseDTO.getMsg()) || responseDTO.getMsg().contains(retryMessage))) {
                lkgSyncRecordEntity.setSyncStatus(LkgSyncStatusEnum.COMPLATE.getValue());

                lkgSyncRequestEntity.setSyncStatus(LkgSyncStatusEnum.COMPLATE.getValue())
                        .setSyncTime(new Date());
            }
        }

        lkgSyncRequestDao.updateById(lkgSyncRequestEntity);

        //更新记录
        lkgSyncRecordDao.updateById(lkgSyncRecordEntity);

        //记录日志 记录dbi_lkg_operation_log
        LkgOperationLogEntity lkgOperationLogEntity = new LkgOperationLogEntity();
        lkgOperationLogEntity.setReferId(lkgSyncRecordEntity.getId().toString())
                .setOperationType(lkgSyncRecordEntity.getSyncType())
                .setRequestSystem(SystemEnum.PIGEON.getName())
                .setTargetSystem(SystemEnum.LKG.getName())
                .setRequestUrl("/lkg/tran/" + transCode)
                .setRequestInfo(JSON.toJSONString(lkgInterfaceRequestDTO))
                .setResponseInfo(JSON.toJSONString(responseDTO))
                .setCreatedAt(requestStart)
                .setUpdatedAt(requestEnd)
                .setRemark(lkgSyncRecordEntity.getContractCode());

        lkgOperationLogDao.save(lkgOperationLogEntity);

        return lkgSyncRecordEntity;
    }

    public LkgInterfaceResponseDTO sendQueryContract(LkgContractEntity lkgContractEntity) {
        //调用接口并获取返回值
        LkgInterfaceResponseDTO responseDTO = null;
        if (lkgContractEntity != null) {
            LkgInterfaceRequestDTO lkgInterfaceRequestDTO = new LkgInterfaceRequestDTO();
            Integer salesType = lkgContractEntity.getSalesType();
            String siteId = lkgContractEntity.getSiteId();

            String transCode = getTransCode(salesType, LkgInterfaceActionEnum.QUERY.getActionName());

            //nonce=id-synctpe-timestamp-随机数
            StringBuilder nonce = new StringBuilder();
            nonce.append(LkgInterfaceActionEnum.QUERY.getActionName()).append("-")
                    .append(lkgContractEntity.getContractCode()).append("-")
                    .append(siteId).append("-")
                    .append(timeStamp).append("-")
                    .append(RandomUtil.randomNumbers(6));

            LkgQueryRecordDTO lkgQueryRecordDTO = new LkgQueryRecordDTO();
            lkgQueryRecordDTO.setContractNumber(lkgContractEntity.getContractCode());
            lkgQueryRecordDTO.setRefContractNumber("%");

            //组装Data
            lkgInterfaceRequestDTO.setData(JSON.toJSONString(lkgQueryRecordDTO))
                    .setAppKey(appKey)
                    .setNonce(nonce.toString())
                    .setTimeStamp(timeStamp)
                    .setSiteId(siteId)
                    .setTransCode(transCode);

            //加密参数
            String signkey = getAppSign(lkgInterfaceRequestDTO);
            lkgInterfaceRequestDTO.setAppSign(signkey);

            try {
                if ("1".equals(lkgOpen)) {
                    responseDTO = lkgRemoteFacade.syncLkgInfo(lkgInterfaceRequestDTO);
                    log.info("开启调用LKG查询接口返回值:{}", JSONObject.toJSONString(responseDTO));
                } else {
                    responseDTO = new LkgInterfaceResponseDTO();
                    responseDTO.setCode(0)
                            .setMsg("无需同步")
                            .setFailCode(LkgResponseCodeEnum.INTERFACE_QUERY_CLOSE.getCode());
                    log.info("关闭调用LKG查询接口返回值:{}", JSONObject.toJSONString(responseDTO));
                }
            } catch (Exception e) {
                responseDTO = new LkgInterfaceResponseDTO();
                responseDTO.setCode(0)
                        .setMsg("调用LKG查询接口异常")
                        .setFailCode(LkgResponseCodeEnum.INTERFACE_QUERY_ERROR.getCode());
                log.error("调用LKG查询接口异常:{}", e.getMessage());
                throw new RuntimeException(e);
            }

            // 记录日志 记录dbi_lkg_operation_log
            LkgOperationLogEntity lkgOperationLogEntity = new LkgOperationLogEntity();
            lkgOperationLogEntity.setReferId(lkgContractEntity.getContractCode())
                    .setOperationType(LkgInterfaceActionEnum.QUERY.getSyncType())
                    .setRequestSystem(SystemEnum.PIGEON.getName())
                    .setTargetSystem(SystemEnum.LKG.getName())
                    .setRequestUrl("/lkg/tran/" + transCode)
                    .setRequestInfo(JSON.toJSONString(lkgInterfaceRequestDTO))
                    .setResponseInfo(JSON.toJSONString(responseDTO))
                    .setCreatedAt(new Date());

            lkgOperationLogDao.save(lkgOperationLogEntity);
        }
        return responseDTO;
    }


    private String getTransCode(Integer salesType, String actionName) {
        return LkgInterfaceEnum.getTransCode(salesType, actionName);
    }

    private String getAppSign(LkgInterfaceRequestDTO lkgInterfaceRequestDTO) {
        //String timeStamp = new Timestamp(System.currentTimeMillis()).toString();
        String nonce = lkgInterfaceRequestDTO.getNonce();

        //String jData = JSON.toJSONString(lkgInterfaceRequestDTO.getData());
        String jData = (String) lkgInterfaceRequestDTO.getData();

        StringBuilder sbData = new StringBuilder();
        //var paramStr = appKey + appSecret + nonce + timeStamp + data + transCode;
        sbData.append(appKey).append(appSecret).append(nonce).append(timeStamp).append(jData).append(lkgInterfaceRequestDTO.getTransCode()).append(lkgInterfaceRequestDTO.getSiteId());

        return SHAUtil.SHA512(sbData.toString());

    }

}
