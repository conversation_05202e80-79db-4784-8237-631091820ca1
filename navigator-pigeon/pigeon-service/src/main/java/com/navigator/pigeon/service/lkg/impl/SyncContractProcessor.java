package com.navigator.pigeon.service.lkg.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.pigeon.dao.LkgContractDao;
import com.navigator.pigeon.dao.LkgSyncRecordDao;
import com.navigator.pigeon.pojo.dto.*;
import com.navigator.pigeon.pojo.entity.LkgContractEntity;
import com.navigator.pigeon.pojo.entity.LkgSyncRecordEntity;
import com.navigator.pigeon.pojo.enums.*;
import com.navigator.pigeon.service.lkg.remote.LkgRemoteService;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.vo.ContractDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SyncContractProcessor {

    @Resource
    LkgRemoteService lkgRemoteService;

    @Resource
    LkgSyncRecordDao lkgSyncRecordDao;

    @Resource
    SyncValueAdapter syncValueAdapter;

    @Resource
    TradeRemoteService tradeRemoteService;

    @Resource
    SyncConvertContractService syncConvertContractService;

    @Resource
    LkgContractDao lkgContractDao;

    public List<LkgSyncRecordEntity> saveSyncRecordContractDef(SyncRequestDTO syncRequestDTO) {
        /*
            1、根据业务场景，分别生成记录
            2、每一条记录，获取合同信息
            3、根据获取到的合同信息，生成Lkg的接口定义值对象
            4、更新记录
            5、调用同步接口
        */

        //同步记录列表
        List<LkgSyncRecordEntity> list = new ArrayList<>();

        //新增新合同
        if (syncRequestDTO.getSyncType() == LkgInterfaceActionEnum.ADD.getSyncType()) {
            //进行同步处理
            processAddSyncRecord(syncRequestDTO);

        } else if (syncRequestDTO.getSyncType() == LkgInterfaceActionEnum.UPDATE.getSyncType()) {

            //普通修改 且 变更交货工厂时，NAV仅更新合同，LKG需要废除原合同，新增新合同
            if (syncRequestDTO.getTradeType() == ContractTradeTypeEnum.REVISE_NORMAL.getValue()
                    && syncRequestDTO.getIsChangeFactory() == 1) {
                //删除原合同
                syncRequestDTO.setSyncType(LkgInterfaceActionEnum.DELETE.getSyncType());
                processDeleteSyncRecord(syncRequestDTO);

                //新增新合同
                syncRequestDTO.setSyncType(LkgInterfaceActionEnum.ADD.getSyncType());
                processAddSyncRecord(syncRequestDTO);

            } else {
                //更新原合同
                processUpdateSyncRecord(syncRequestDTO);
            }

        } else if (syncRequestDTO.getSyncType() == LkgInterfaceActionEnum.DELETE.getSyncType()) {

            //废除合同
            processDeleteSyncRecord(syncRequestDTO);

        } else if (syncRequestDTO.getSyncType() == LkgInterfaceActionEnum.PRICE.getSyncType()) {

            //处理新增定价同步记录
            processAddPriceSyncRecord(syncRequestDTO);
        } else if (syncRequestDTO.getSyncType() == LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType()) {

            //更新原合同
//            processPriceUpdateSyncRecord(syncRequestDTO);

            //基差合同拆分 TODO NEO
            //处理原合同的定价记录更新（作废）
            processUpdatePriceSyncRecord(syncRequestDTO);

        }

        return list;
    }


    public LkgSyncRecordEntity processAddSyncRecord(SyncRequestDTO syncRequestDTO) {

        LkgSyncRecordEntity lkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
        lkgSyncRecordEntity.setProcessType(LkgInterfaceActionEnum.ADD.getActionName());

        //生成新增记录
        lkgSyncRecordDao.save(lkgSyncRecordEntity);


        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());

        if (null == contractDetailVO) {
            return lkgSyncRecordEntity;
        }

        LkgTransactionTypeEnum lkgTransactionTypeEnum = syncValueAdapter.getTransactionType(syncRequestDTO.getTradeType(), syncRequestDTO.getIsChangeFactory() == 1);
        if (null == lkgTransactionTypeEnum || lkgTransactionTypeEnum == LkgTransactionTypeEnum.DEFAULT) {
            lkgTransactionTypeEnum = LkgTransactionTypeEnum.NEW;
        }

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO))
                .setBelongCustomerId(contractDetailVO.getBelongCustomerId());

        // String siteId = tradeRemoteService.getSiteId(contractDetailVO.getBelongCustomerId());
        String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());
        lkgSyncRecordEntity.setSiteId(siteId);

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            //转换值对象
            LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = syncConvertContractService.convertPurchaseContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgPurchaseContractDefDTO.setTransactionType(lkgTransactionTypeEnum.getCode());

            //记录到Record中
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgPurchaseContractDefDTO))
                    .setRefContractNumber(lkgPurchaseContractDefDTO.getRefContractNumber());
        } else {
            //转换值对象
            LkgSalesContractDefDTO lkgSalesContractDefDTO = syncConvertContractService.convertSalesContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSalesContractDefDTO.setTransactionType(lkgTransactionTypeEnum.getCode());

            //记录到Record中
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractDefDTO))
                    .setRefContractNumber(lkgSalesContractDefDTO.getRefContractNumber());
        }

        //发送远程调用
        lkgSyncRecordEntity = lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        //保存合同记录
//        if (lkgSyncRecordEntity.getSyncStatus() == LkgSyncStatusEnum.COMPLATE.getValue()) {
        saveLkgContract(lkgSyncRecordEntity);
//        }

        return lkgSyncRecordEntity;

    }

    public LkgSyncRecordEntity processUpdateSyncRecord(SyncRequestDTO syncRequestDTO) {
        //更新原合同
        LkgSyncRecordEntity lkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
        lkgSyncRecordEntity.setProcessType(LkgInterfaceActionEnum.UPDATE.getActionName());

        lkgSyncRecordDao.save(lkgSyncRecordEntity);

        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());

        // String siteId = tradeRemoteService.getSiteId(contractDetailVO.getBelongCustomerId());
        String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());
        lkgSyncRecordEntity.setSiteId(siteId)
                .setBelongCustomerId(contractDetailVO.getBelongCustomerId());

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            //LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = convertPurchaseContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgPurchaseContractModifyDefDTO lkgContractModifyDTO = syncConvertContractService.convertPurchaseContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractModifyDTO))
                    .setRefContractNumber(lkgContractModifyDTO.getRefContractNumber());
        } else {
            //LkgSalesContractDefDTO lkgSalesContractDefDTO = convertSalesContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgSalesContractModifyDefDTO lkgSalesContractModifyDefDTO = syncConvertContractService.convertSalesContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractModifyDefDTO))
                    .setRefContractNumber(lkgSalesContractModifyDefDTO.getRefContractNumber());
        }

        //发送远程调用
        lkgSyncRecordEntity = lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        return lkgSyncRecordEntity;
    }

    public LkgSyncRecordEntity processPriceUpdateSyncRecord(SyncRequestDTO syncRequestDTO) {
        //更新原合同
        LkgSyncRecordEntity lkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
        lkgSyncRecordEntity.setSyncType(LkgInterfaceActionEnum.UPDATE.getSyncType());
        lkgSyncRecordEntity.setProcessType(LkgInterfaceActionEnum.UPDATE.getActionName());

        lkgSyncRecordDao.save(lkgSyncRecordEntity);

        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());
        //ContractDetailVO parentContractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getParentContractId());
        //parentContractDetailVO.setContractNum(contractDetailVO.getContractNum());

        // String siteId = tradeRemoteService.getSiteId(contractDetailVO.getBelongCustomerId());
        String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());
        lkgSyncRecordEntity.setSiteId(siteId)
                .setBelongCustomerId(contractDetailVO.getBelongCustomerId());

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            //LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = convertPurchaseContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgPurchaseContractModifyDefDTO lkgContractModifyDTO = syncConvertContractService.convertPurchaseContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgContractModifyDTO.setChangeOrderNo(syncRequestDTO.getTtId().toString());
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractModifyDTO))
                    .setRefContractNumber(lkgContractModifyDTO.getRefContractNumber());
        } else {
            //LkgSalesContractDefDTO lkgSalesContractDefDTO = convertSalesContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgSalesContractModifyDefDTO lkgSalesContractModifyDefDTO = syncConvertContractService.convertSalesContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSalesContractModifyDefDTO.setChangeOrderNo(syncRequestDTO.getTtId().toString());
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractModifyDefDTO))
                    .setRefContractNumber(lkgSalesContractModifyDefDTO.getRefContractNumber());
        }

        //发送远程调用
        lkgSyncRecordEntity = lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        return lkgSyncRecordEntity;
    }

    public LkgSyncRecordEntity processDeleteSyncRecord(SyncRequestDTO syncRequestDTO) {
        //更新原合同
        LkgSyncRecordEntity lkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
        lkgSyncRecordEntity.setProcessType(LkgInterfaceActionEnum.UPDATE.getActionName());

        lkgSyncRecordDao.save(lkgSyncRecordEntity);

        LkgContractEntity lkgContractEntity = lkgContractDao.getLkgContract(syncRequestDTO.getContractCode());

        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(lkgSyncRecordEntity.getContractId());

        Integer belongCustomerId = contractDetailVO.getBelongCustomerId();
        // String siteId = tradeRemoteService.getSiteId(belongCustomerId);
        String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());

        //获取原合同的siteId
        if (syncRequestDTO.getTradeType() == ContractTradeTypeEnum.REVISE_NORMAL.getValue()
                && syncRequestDTO.getIsChangeFactory() == 1) {
            if (null != lkgContractEntity) {
                belongCustomerId = lkgContractEntity.getBelongCustomerId();
                siteId = lkgContractEntity.getSiteId();
            }
        }

        lkgSyncRecordEntity.setSiteId(siteId)
                .setBelongCustomerId(belongCustomerId);

        lkgSyncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (lkgSyncRecordEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            //LkgPurchaseContractDefDTO lkgPurchaseContractDefDTO = convertPurchaseContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgPurchaseContractModifyDefDTO lkgContractModifyDTO = syncConvertContractService.convertPurchaseContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractModifyDTO))
                    .setRefContractNumber(lkgContractModifyDTO.getRefContractNumber());

            lkgContractModifyDTO.setStatus(LkgContractStatusEnum.INVALID.getValue());

        } else {
            //LkgSalesContractDefDTO lkgSalesContractDefDTO = convertSalesContractDefDTO(contractDetailVO, lkgSyncRecordEntity);
            LkgSalesContractModifyDefDTO lkgSalesContractModifyDefDTO = syncConvertContractService.convertSalesContractModifyDTO(contractDetailVO, lkgSyncRecordEntity);
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgSalesContractModifyDefDTO))
                    .setRefContractNumber(lkgSalesContractModifyDefDTO.getRefContractNumber());

            lkgSalesContractModifyDefDTO.setStatus(LkgContractStatusEnum.INVALID.getValue());
        }

        //发送远程调用
        lkgSyncRecordEntity = lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        //更新合同
        if (null != lkgContractEntity) {
            lkgContractEntity.setStatus(contractDetailVO.getStatus());
            lkgContractEntity.setSiteId(siteId);
            lkgContractDao.updateById(lkgContractEntity);
        }

        return lkgSyncRecordEntity;
    }

    public LkgSyncRecordEntity processAddPriceSyncRecord(SyncRequestDTO syncRequestDTO) {

        LkgSyncRecordEntity lkgSyncRecordEntity = createSyncRecord(syncRequestDTO);
        lkgSyncRecordEntity.setProcessType(LkgInterfaceActionEnum.PRICE.getActionName());

        ConfirmPriceDTO confirmPriceDTO = tradeRemoteService.getConfirmPriceInfo(syncRequestDTO.getContractId(), syncRequestDTO.getConfirmPriceId());
        if (null != confirmPriceDTO) {
            ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(syncRequestDTO.getContractId());

            // String siteId = tradeRemoteService.getSiteId(contractDetailVO.getBelongCustomerId());
            String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());
            lkgSyncRecordEntity.setSiteId(siteId)
                    .setBelongCustomerId(contractDetailVO.getBelongCustomerId())
                    .setLkgContractCode(contractDetailVO.getLinkinageCode())
                    .setContractInfo(JSON.toJSONString(confirmPriceDTO) + " || " + JSON.toJSONString(contractDetailVO));

            //转换为定价值对象
            LkgContractPriceDefDTO lkgContractPriceDefDTO = syncConvertContractService.convertLkgContractPriceDefDTO(syncRequestDTO, confirmPriceDTO, contractDetailVO);

            //设置请求业务数据
            lkgSyncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractPriceDefDTO));

            lkgSyncRecordDao.save(lkgSyncRecordEntity);

            //发送远程调用
            lkgRemoteService.sendSyncContract(lkgSyncRecordEntity);

        }

        return lkgSyncRecordEntity;

    }

    private void processUpdatePriceSyncRecord(SyncRequestDTO syncRequestDTO) {
        //获取新合同关联的定价单信息
        //List<ConfirmPriceDTO> confirmPriceDTOList = tradeRemoteService.getConfirmPriceList(syncRequestDTO.getReferContractId());
        //List<ConfirmPriceDTO> confirmPriceDTOList = tradeRemoteService.getConfirmPriceList(syncRequestDTO.getContractId());
        //if (null != confirmPriceDTOList && confirmPriceDTOList.size() > 0) {
        //for (ConfirmPriceDTO confirmPriceDTO : confirmPriceDTOList) {
        ConfirmPriceDTO confirmPriceDTO = tradeRemoteService.getConfirmPriceInfo(syncRequestDTO.getContractId(), syncRequestDTO.getConfirmPriceId());
        LkgContractPriceDefDTO lkgContractPriceDefDTO = new LkgContractPriceDefDTO();

        LkgSyncRecordEntity syncRecordEntity = createSyncRecord(syncRequestDTO);
        //获取合同详情
        ContractDetailVO contractDetailVO = tradeRemoteService.getTradeContractDetail(syncRecordEntity.getContractId());

        // String siteId = tradeRemoteService.getSiteId(contractDetailVO.getBelongCustomerId());
        String siteId = tradeRemoteService.getSiteIdBySiteCode(contractDetailVO.getSiteCode());
        syncRecordEntity.setSiteId(siteId)
                .setBelongCustomerId(contractDetailVO.getBelongCustomerId());

        syncRecordEntity.setLkgContractCode(contractDetailVO.getLinkinageCode())
                .setContractCode(contractDetailVO.getContractCode())
                .setContractType(contractDetailVO.getContractType())
                .setContractInfo(JSON.toJSONString(contractDetailVO));

        if (syncRequestDTO.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()) {
            lkgContractPriceDefDTO.setFixPriceNo(confirmPriceDTO.getFixPriceNo())
                    .setFixPriceCount(BigDecimal.ZERO);
            syncRecordEntity.setSyncType(LkgInterfaceActionEnum.PRICE_UPDATE.getSyncType());
        } else {
            lkgContractPriceDefDTO.setSettleNo(confirmPriceDTO.getFixPriceNo())
                    .setSettleCount(BigDecimal.ZERO);
            syncRecordEntity.setSyncType(LkgInterfaceActionEnum.SALES_PRICE_UPDATE.getSyncType());
        }


        syncRecordEntity.setLkgReqestInfo(JSON.toJSONString(lkgContractPriceDefDTO))
                .setProcessType(LkgContractProcessTypeEnum.PRICE_UPDATE.name());

        lkgSyncRecordDao.save(syncRecordEntity);

        //发送远程调用
        lkgRemoteService.sendSyncContract(syncRecordEntity);

        //}
        //}
    }

    /**
     * 根据SyncRequestDTO 生成 SyncRecordEntity
     *
     * @param syncRequestDTO
     * @return
     */
    private LkgSyncRecordEntity createSyncRecord(SyncRequestDTO syncRequestDTO) {
        LkgSyncRecordEntity lkgSyncRecordEntity = new LkgSyncRecordEntity();
        lkgSyncRecordEntity.setRequestId(syncRequestDTO.getId())
                .setLkgContractCode("")
                .setContractCode(syncRequestDTO.getContractCode())
                .setContractId(syncRequestDTO.getContractId())
                .setParentContractId(syncRequestDTO.getParentContractId())
                .setContractType(syncRequestDTO.getContractType())
                .setTradeType(syncRequestDTO.getTradeType())
                .setSalesType(syncRequestDTO.getSalesType())
                .setCustomerCode(syncRequestDTO.getCustomerCode())
                .setTryTimes(0)
                .setSyncType(syncRequestDTO.getSyncType())
                .setSyncTime(null)
                .setSyncStatus(LkgSyncStatusEnum.INIT.getValue())
                .setLkgReqestInfo("")
                .setLkgResultsInfo("")
                .setLkgResultsStatus(0)
                .setFactoryCode("")
                .setRemark("")
                .setNonce("")
                .setTtId(syncRequestDTO.getTtId())
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCreatedBy(SystemEnum.MAGELLAN.getEmployId())
                .setUpdatedBy(SystemEnum.MAGELLAN.getEmployId());

        return lkgSyncRecordEntity;
    }

    private void saveLkgContract(LkgSyncRecordEntity lkgSyncRecordEntity) {
        LkgContractEntity lkgContractEntity = new LkgContractEntity();
        lkgContractEntity.setLkgContractCode(lkgSyncRecordEntity.getLkgContractCode())
                .setContractCode(lkgSyncRecordEntity.getContractCode())
                .setBelongCustomerId(lkgSyncRecordEntity.getBelongCustomerId())
                .setSiteId(lkgSyncRecordEntity.getSiteId())
                .setContractId(lkgSyncRecordEntity.getContractId())
                //.setContractStatus()
                //.setContractNumber()
                .setRefcontractNumber(lkgSyncRecordEntity.getRefContractNumber())
                .setSalesType(lkgSyncRecordEntity.getSalesType())
        //.setPriceType()
        //.setStatus()
        //.setCount()
        //.setInCount()
        //.setContractOutcount()
        //.setNointCount()
        //.setContractNooutcount()
        //.setContractFactoutcount()
        //.setContractNosendcount()
        //.setTolsettleCount()
        //.setTolnosettleCount()
        //.setRemark("")
        //.setCreatedAt()
        //.setUpdatedAt()
        ;
        lkgContractDao.save(lkgContractEntity);
    }


}
