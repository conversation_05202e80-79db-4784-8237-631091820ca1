package com.navigator.pigeon.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.pigeon.mapper.DbiLkgDailyContractMapper;
import com.navigator.pigeon.pojo.entity.LkgDailyContractEntity;

import java.util.List;

/**
 * <p>
 * dbi_lkg_daily_contract Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Dao
public class LkgDailyContractDao extends BaseDaoImpl<DbiLkgDailyContractMapper, LkgDailyContractEntity> {
    public List<LkgDailyContractEntity> queryList(LkgDailyContractEntity lkgDailyContractEntity){
        LambdaQueryWrapper<LkgDailyContractEntity> wrapper = new LambdaQueryWrapper<LkgDailyContractEntity>()
                .eq(null!=lkgDailyContractEntity.getSalesType(),LkgDailyContractEntity::getSalesType,lkgDailyContractEntity.getSalesType())
                .eq(StrUtil.isNotBlank(lkgDailyContractEntity.getFileSource()),LkgDailyContractEntity::getFileSource,lkgDailyContractEntity.getFileSource())
                .eq(null!=lkgDailyContractEntity.getCheckStatus(),LkgDailyContractEntity::getCheckStatus,lkgDailyContractEntity.getCheckStatus())
                .eq(StrUtil.isNotBlank(lkgDailyContractEntity.getCheckDate()),LkgDailyContractEntity::getCheckDate,lkgDailyContractEntity.getCheckDate())
                ;
        return this.baseMapper.selectList(wrapper);
    }
    public LkgDailyContractEntity getDailyContractEntity (LkgDailyContractEntity lkgDailyContractEntity){
        LambdaQueryWrapper<LkgDailyContractEntity> wrapper = new LambdaQueryWrapper<LkgDailyContractEntity>()
                .eq(null!=lkgDailyContractEntity.getSalesType(),LkgDailyContractEntity::getSalesType,lkgDailyContractEntity.getSalesType())
                .eq(StrUtil.isNotBlank(lkgDailyContractEntity.getFileSource()),LkgDailyContractEntity::getFileSource,lkgDailyContractEntity.getFileSource())
                .eq(StrUtil.isNotBlank(lkgDailyContractEntity.getCheckDate()),LkgDailyContractEntity::getCheckDate,lkgDailyContractEntity.getCheckDate())
                .eq(null!=lkgDailyContractEntity.getCheckStatus(),LkgDailyContractEntity::getCheckStatus,lkgDailyContractEntity.getCheckStatus())
                .eq(null!=lkgDailyContractEntity.getContractNumber(),LkgDailyContractEntity::getContractNumber,lkgDailyContractEntity.getContractNumber())
                ;
        LkgDailyContractEntity dailyContractEntity = getOne(wrapper);
        return dailyContractEntity;
    }
}
