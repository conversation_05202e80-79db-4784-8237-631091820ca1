<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">

    <!-- 日志编码 -->
    <property name="CHARSET" value="utf-8"/>
    <!-- 日志记录格式 -->
    <property name="INFO_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L-%msg%n" />
    <property name="WARN_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L-%msg%n" />
    <property name="ERROR_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger Line:%-3L-%msg%n" />

    <springProperty scope="context" name="LOG_HOME" source="spring.application.name"/>

    <!-- >>>>>>>>>>>>>>>>>>>>>>>>>配置appender(可以配置多个)>>>>>>>>>>>>>>>>>>>>>>>>> -->

    <!--自定义颜色配置-->
    <conversionRule conversionWord="customColor" converterClass="com.navigator.common.util.LogbackColorUtil"/>
    <!-- 控制台 -->
    <appender name="consoleAppender"
              class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %magenta([%thread]) %customColor(%-5level) %cyan(%logger{15}) - %msg%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <!-- info日志 -->
    <appender name="infoAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>logs/${LOG_HOME}/info.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/${LOG_HOME}/info/log-info-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>6MB</maxFileSize>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder
                class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ERROR_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- warn日志 -->
    <appender name="warnAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>logs/${LOG_HOME}/warn.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/${LOG_HOME}/warn/log-warn-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>6MB</maxFileSize>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder
                class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${WARN_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- error日志 -->
    <appender name="errorAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>logs/${LOG_HOME}/error.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/${LOG_HOME}/error/log-error-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>6MB</maxFileSize>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder
                class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ERROR_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- >>>>>>>>>>>>>>>>>>>>>>>>>>>>>使用appender>>>>>>>>>>>>>>>>>>>>>>>>>>>>> -->
    <!-- 不同的业务逻辑日志打印到指定文件夹-->
    <!--    <logger name="testAppender" additivity="false" level="INFO">
            <appender-ref ref="testAppender"/>
        </logger>-->

    <root level="INFO">
        <!-- 指定使用哪个appender consoleAppender输出到控制台-->
        <appender-ref ref="consoleAppender"/>
        <appender-ref ref="infoAppender" />
        <appender-ref ref="warnAppender" />
        <appender-ref ref="errorAppender" />
    </root>

</configuration>
