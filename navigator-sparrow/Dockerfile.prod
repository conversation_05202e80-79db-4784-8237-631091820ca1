FROM openjdk:11.0
RUN mkdir /config
COPY  navigator-sparrow/sparrow-service/src/main/resources/bootstrap-dev.yml /config
COPY  navigator-sparrow/sparrow-service/src/main/resources/bootstrap.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/sparrow-service/*.jar /navigator-sparrow-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-sparrow-1.0-SNAPSHOT.jar
