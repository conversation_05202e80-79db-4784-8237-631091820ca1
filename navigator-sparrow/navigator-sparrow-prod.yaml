apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-sparrow-prod
  namespace: prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-sparrow-prod
  template:
    metadata:
      labels:
        app: ldc-navigator-sparrow-prod
    spec:
      containers:
      - image: csm4pnvgacr001.azurecr.cn/navigator-sparrow-prod:#{Build.BuildId}#
        name: ldc-navigator-sparrow-prod
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4pnvgsto002-secret  # required
            shareName: logs-prod  # required
            server: csm4pnvgsto002.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optional
