package com.navigator.sparrow.dao;

import cn.signit.sdk.pojo.webhook.response.RevokeSignProcess;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.annotation.Dao;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.sparrow.mapper.YqqTaskRecordMapper;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.pojo.enums.DisposeNodeEnum;
import com.navigator.sparrow.pojo.enums.YqqTaskEnum;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 10:42
 */
@Dao
public class YqqTaskRecordDao extends ServiceImpl<YqqTaskRecordMapper, YqqTaskRecordEntity> {

    public YqqTaskRecordEntity getTaskByBizId(Integer bizId, Integer type) {
        List<YqqTaskRecordEntity> list = this.baseMapper.selectList(Wrappers.<YqqTaskRecordEntity>lambdaQuery()
                .eq(YqqTaskRecordEntity::getBizId, bizId)
                .eq(YqqTaskRecordEntity::getType, type));

        return list.isEmpty() ? null : list.get(0);
    }

    public YqqTaskRecordEntity getTaskByBizUUCode(String UuCode, Integer type) {
        List<YqqTaskRecordEntity> list = this.baseMapper.selectList(Wrappers.<YqqTaskRecordEntity>lambdaQuery()
                .eq(YqqTaskRecordEntity::getBizUuCode, UuCode)
                .eq(YqqTaskRecordEntity::getType, type));

        return list.isEmpty() ? null : list.get(0);
    }

    public List<YqqTaskRecordEntity> unfinishedYqqTaskRecord() {
        return this.baseMapper.selectList(Wrappers.<YqqTaskRecordEntity>lambdaQuery()
                .eq(YqqTaskRecordEntity::getType, YqqTaskEnum.RESPONSE.getValue())
                .ne(YqqTaskRecordEntity::getDisposeNode, DisposeNodeEnum.COMPLATE.getValue())
                .le(YqqTaskRecordEntity::getCreatedAt, DateTimeUtil.formatDateTimeString(new Date(new Date().getTime() - 600000)))
        );
    }

    public YqqTaskRecordEntity saveExist(RevokeSignProcess revokeSignProcess, String eventType) {
        YqqTaskRecordEntity yqqTaskRecordEntity = getTaskByBizUUCode(revokeSignProcess.getCustomTag(), YqqTaskEnum.RESPONSE.getValue());

        //将接收到的数据存入数据库中，防重
        if (null != yqqTaskRecordEntity) {
            return yqqTaskRecordEntity;
        }


        //记录返回数据
        yqqTaskRecordEntity = new YqqTaskRecordEntity()
                .setData(JSON.toJSONString(revokeSignProcess))
                //.setBizId(contractSignEntity.getId())
                .setBizUuCode(revokeSignProcess.getCustomTag())
                .setInvokeNo(revokeSignProcess.getInvokeNo())
                .setDisposeNode(DisposeNodeEnum.SAVE_DATA.getValue())
                .setType(YqqTaskEnum.RESPONSE.getValue())
                .setWebhookEventType(eventType);
        this.save(yqqTaskRecordEntity);

        return yqqTaskRecordEntity;
    }

}
