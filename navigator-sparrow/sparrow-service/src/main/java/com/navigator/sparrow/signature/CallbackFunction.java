package com.navigator.sparrow.signature;

import cn.hutool.core.bean.BeanUtil;
import cn.signit.sdk.pojo.webhook.response.*;
import cn.signit.sdk.type.WebhookEventType;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.TraceLogDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.sparrow.dao.DbtSignatureDao;
import com.navigator.sparrow.dao.YqqTaskRecordDao;
import com.navigator.sparrow.pojo.dto.CallbackFunctionDTO;
import com.navigator.sparrow.pojo.dto.ParticipantDTO;
import com.navigator.sparrow.pojo.entity.DbtSignatureEntity;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.service.IDbtSignatureService;
import com.navigator.sparrow.service.IYqqTaskRecordService;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.dto.contractsign.ContractSignYQQCallbackDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignYQQUrlDTO;
import com.navigator.trade.pojo.dto.notice.CustomerSignNoticeItemDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 10:36
 */
@Slf4j
@Component
public class CallbackFunction {

    @Resource
    private IDbtSignatureService iDbtSignatureService;
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private DbtSignatureDao dbtSignatureDao;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    @Lazy
    private IYqqTaskRecordService yqqTaskRecordService;
    @Resource
    private YqqTaskRecordDao yqqTaskRecordDao;
    @Resource
    private MessageFacade messageFacade;

    public void callbackFunction(WebhookResponse webhookResponse) {
        WebhookResponse ente = webhookResponse;

        // step3: 解析获取rawData
        switch (WebhookEventType.parse(ente.getEvent())) {
            // 企业实名认证提交
            case ENTERPRISE_VERIFICATION_SUBMITTED:
                EnterpriseVerificationSubmitted enterpriseVerificationSubmitted = (EnterpriseVerificationSubmitted) ente.rawDataAsBean();
                log.info("\nEnterpriseVerificationSubmitted rawData is 企业实名认证提交:\n" + JSON.toJSONString(enterpriseVerificationSubmitted, true));
                break;
            // 企业实名认证初级完成
            case ENTERPRISE_VERIFICATION_PRIMARY_COMPLETED:
                EnterpriseVerificationPrimaryCompleted enterpriseVerificationPrimaryCompleted = (EnterpriseVerificationPrimaryCompleted) ente.rawDataAsBean();
                log.info("\nEnterpriseVerificationPrimaryCompleted rawData is 企业实名认证初级完成:\n" + JSON.toJSONString(enterpriseVerificationPrimaryCompleted, true));
                break;
            // 企业实名认证已打款
            case ENTERPRISE_VERIFICATION_PAID:
                EnterpriseVerificationPaid enterpriseVerificationPaid = (EnterpriseVerificationPaid) ente.rawDataAsBean();
                log.info("\nEnterpriseVerificationPaid rawData is 企业实名认证已打款:\n" + JSON.toJSONString(enterpriseVerificationPaid, true));
                break;
            // 企业实名认证完成
            case ENTERPRISE_VERIFICATION_COMPLETED:
                EnterpriseVerificationCompleted enterpriseVerificationCompleted = (EnterpriseVerificationCompleted) ente.rawDataAsBean();
                log.info("\nEnterpriseVerificationCompleted rawData is 企业实名认证完成:\n" + JSON.toJSONString(enterpriseVerificationCompleted, true));
                break;
            // 信封流程完成
            case ENVELOPE_COMPLETED_SUCCEED:
            //case ENVELOPE_COMPLETED:
                RevokeSignProcess revokeSignProcess = ente.rawDataAsBean(RevokeSignProcess.class);
                log.info("\nRevokeSignProcess rawData is 信封流程完成:\n" + JSON.toJSONString(revokeSignProcess, true));
                revokeSignProcessAnalysis(revokeSignProcess, WebhookEventType.ENVELOPE_COMPLETED_SUCCEED.getEvent(),
                        JSON.toJSONString(revokeSignProcess, true));
                break;
            // 信封流程启动
            case ENVELOPE_STARTED:
                EnvelopeStarted envelopeStarted = ente.rawDataAsBean(EnvelopeStarted.class);
                envelopeStartedAnalysis(envelopeStarted, WebhookEventType.ENVELOPE_STARTED.getEvent(),
                        JSON.toJSONString(envelopeStarted, true));
                log.info("\nEnvelopeStarted rawData is 信封流程启动:\n" + JSON.toJSONString(envelopeStarted, true));
                break;
            case ENVELOPE_STARTED_SUCCEED:
                EnvelopeStarted envelopeSucceedStarted = ente.rawDataAsBean(EnvelopeStarted.class);
                envelopeStartedAnalysis(envelopeSucceedStarted, WebhookEventType.ENVELOPE_STARTED_SUCCEED.getEvent(),
                        JSON.toJSONString(envelopeSucceedStarted, true));
                log.info("\nEnvelopeStarted rawData is 启动成功:\n" + JSON.toJSONString(envelopeSucceedStarted, true));
                break;
            case ENVELOPE_STARTED_FAILED:
                EnvelopeStarted envelopeFailedStarted = ente.rawDataAsBean(EnvelopeStarted.class);
                envelopeStartedFailedAnalysis(envelopeFailedStarted, WebhookEventType.ENVELOPE_STARTED_FAILED.getEvent(),
                        JSON.toJSONString(envelopeFailedStarted, true));
                log.info("\nEnvelopeStarted rawData is 启动失败:\n" + JSON.toJSONString(envelopeFailedStarted, true));
                break;
            // 参与者确认
            case PARTICIPANT_CONFIRMED:
                ParticipantConfirmed participantConfirmed = (ParticipantConfirmed) ente.rawDataAsBean();
                log.info("\nParticipantConfirmed rawData is 参与者确认:\n" + JSON.toJSONString(participantConfirmed, true));
                participantConfirmedAnalysis(participantConfirmed, WebhookEventType.PARTICIPANT_CONFIRMED.getEvent(),
                        JSON.toJSONString(participantConfirmed, true));
                break;
            // 参与者拒绝
            case PARTICIPANT_REJECTED:
                ParticipantRejected participantRejected = ente.rawDataAsBean(ParticipantRejected.class);
                participantRejectedAnalysis(participantRejected, WebhookEventType.PARTICIPANT_REJECTED.getEvent(),
                        JSON.toJSONString(participantRejected, true));
                log.info("\nParticipantRejected rawData is 参与者拒绝:\n" + JSON.toJSONString(participantRejected, true));
                break;
            //个人实名认证提交其实
            case PERSON_VERIFICATION_SUBMITTED:
                PersonVerificationSubmitted personVerificationSubmitted1 = ente.rawDataAsBean(PersonVerificationSubmitted.class);
                log.info("\nPersonVerificationSubmitted rawData is 个人实名认证提交:\n" + JSON.toJSONString(personVerificationSubmitted1, true));
                break;
            // 个人实名认证完成
            case PERSON_VERIFICATION_COMPLETED:
                PersonVerificationCompleted personVerificationCompleted1 = ente.rawDataAsBean(PersonVerificationCompleted.class);
                log.info("\nPersonVerificationCompleted rawData is 个人实名认证完成:\n" + JSON.toJSONString(personVerificationCompleted1, true));
                break;
            // 参与者正在处理信封
            case PARTICIPANT_HANDLING:
                ParticipantHandling participantHandling = ente.rawDataAsBean(ParticipantHandling.class);
                log.info("\nParticipantHandling rawData is 参与者正在处理信封:\n" + JSON.toJSONString(participantHandling, true));
                participantHandlingAnalysis(ente, WebhookEventType.PARTICIPANT_HANDLING.getEvent());
                break;
            //快捷签署完成事件
            case QUICK_SIGN_COMPLETED:
                QuickSignCompleted quickSignCompleted = ente.rawDataAsBean(QuickSignCompleted.class);
                log.info("\nQuickSignCompleted rawData is 快捷签署完成事件:\n" + JSON.toJSONString(quickSignCompleted, true));
                break;
            case PARTICIPANT_EXPIRED:
                ParticipantExpired rawDat2 = ente.rawDataAsBean(ParticipantExpired.class);
                log.info("\nParticipantExpired rawData is :\n" + JSON.toJSONString(rawDat2, true));
                break;
            default:
                break;
        }
    }

    /**
     * 参与者处理信封
     *
     * @param webhookResponse
     * @param eventType
     */
    private void participantHandlingAnalysis(WebhookResponse webhookResponse, String eventType) {
        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO();

        //将json转换成Map 取出 senderParticipant 下的wsid
        String rawData = webhookResponse.getRawData();
        Map<String, Object> map = JSON.parseObject(rawData);
        for (String key : map.keySet()) {
            if ("senderParticipant".equals(key)) {
                ParticipantDTO participantDTO = JSON.parseObject(JSON.toJSONString(map.get(key)), ParticipantDTO.class);
                callbackFunctionDTO.setParticipantWsid(participantDTO.getWsid());
            }
        }

        //将回调参数转换成 ParticipantHandling
        ParticipantHandling participantHandling = webhookResponse.rawDataAsBean(ParticipantHandling.class);
        //取出需要的参数
        callbackFunctionDTO.setContractUuId(participantHandling.getCustomTag())
                .setActionUrl(participantHandling.getActionUrl())
                .setName(participantHandling.getReceiverParticipant().getName())
                .setAccount(participantHandling.getAccount())
                .setEnterpriseName(participantHandling.getReceiverParticipant().getEnterpriseName())
                .setEventType(eventType)
                .setAssignedSequence(participantHandling.getReceiverParticipant().getAssignedSequence())
                .setEnvelopeWsid(participantHandling.getBasicEnvelope().getWsid());
        updateContract(callbackFunctionDTO);
        //记录操作
        addSignatureLog(callbackFunctionDTO, JSON.toJSONString(participantHandling, true));
    }

    /**
     * 参与者确认
     *
     * @param participantConfirmed
     * @param eventType
     * @param responseDetail
     */
    private void participantConfirmedAnalysis(ParticipantConfirmed participantConfirmed, String eventType, String responseDetail) {
        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO()
                .setContractUuId(participantConfirmed.getCustomTag())
                .setName(participantConfirmed.getReceiverParticipant().getName())
                .setAccount(participantConfirmed.getAccount())
                .setEnterpriseName(participantConfirmed.getReceiverParticipant().getEnterpriseName())
                .setEventType(eventType)
                .setAssignedSequence(participantConfirmed.getReceiverParticipant().getAssignedSequence());
        updateContract(callbackFunctionDTO);
        addSignatureLog(callbackFunctionDTO, responseDetail);
    }

    /**
     * 信封完成事件
     *
     * @param revokeSignProcess
     * @param eventType
     * @param responseDetail
     */
    private void revokeSignProcessAnalysis(RevokeSignProcess revokeSignProcess, String eventType, String responseDetail) {

        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO()
                .setContractUuId(revokeSignProcess.getCustomTag())
                .setAccount(revokeSignProcess.getAccount())
                .setEventType(eventType);

        //记录签章操作
        addSignatureLog(callbackFunctionDTO, responseDetail);

        //将易企签回调的参数保存
        YqqTaskRecordEntity yqqTaskRecordEntity = yqqTaskRecordDao.saveExist(revokeSignProcess, eventType);
        yqqTaskRecordService.processYqqTaskDispose(yqqTaskRecordEntity);


        /*if(null == contractSignEntity){
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_IS_NOT_EXIST);
        }

        if (ContractSignYQQErrorEnum.MAIL_SEND_SUCCEED.getCode().equals(contractSignEntity.getSignErrorCode())){
            throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_SEND_MAIL);
        }

        String fileCategoryType = null;

        Integer fileCategoryTypeCode = 1002;

        SignatureTypeEnum signatureTypeEnum = SignatureTypeEnum.getByValue(contractSignEntity.getSignatureType());

        switch (signatureTypeEnum) {
            //双签
            case BOTH_SIGNATURE:
                fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getMsg();
                fileCategoryTypeCode = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode();

                //完成时间修改协议签章转台
                contractSignEntity.setSignErrorCode(ContractSignYQQErrorEnum.ACCOMPLISH.getCode())
                        .setSignErrorMessage(ContractSignYQQErrorEnum.ACCOMPLISH.getMessage())
                        .setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue());
                updateContractSignYQQ(contractSignEntity);
                break;

            //LDC单签
            case LDC_ONE_SIGNATURE:

                if (IsCustomerSignatureEnum.AlREADY_SIGN.getValue() == contractSignEntity.getIsCustomerSignature()) {
                    fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getMsg();
                    fileCategoryTypeCode = FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode();
                    break;
                }

                fileCategoryType = FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getMsg();
                fileCategoryTypeCode = FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode();

                noticeCustomer = true;

                break;
            case OFFLINE:
                break;
            case ONE_SIGNATURE:
                break;
        }

        if (noticeCustomer) {
            operationLogFacade.saveTraceLog(uuid, "YQQ.Callback.shouldNoticeCustomerSign", JSON.toJSONString(revokeSignProcess));
        }


        //拼接签章文件名称
        String fileName = FilePathUtil.genContractSignatureFileName(
                contractSignEntity.getContractCode(),
                fileCategoryType,
                FilePathType.PDF.getValue()
        );

        String filePath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT,
                FilePathType.PDF,
                String.valueOf(contractSignEntity.getId()));
        String url = revokeSignProcess.getSignData().getUrl();

        try {
            //将易企签返回的url下载并上传到blob中
            FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByUrl(url, filePath, fileName, BlobFileContextEnum.PDF.getFileType());

            //将下载文件路径信息保存在数据库中
            FileItemDTO fileItemDTO = new FileItemDTO();
            fileItemDTO.setUrl(url)
                    .setFilePath(filePath)
                    .setFileName(fileName)
                    .setId(contractSignEntity.getId())
                    .setCategoryType(fileCategoryTypeCode)
                    .setFileBaseInfoDTO(fileBaseInfoDTO);
            fileProcessFacade.saveFileItem(fileItemDTO);


            if (noticeCustomer) {
                contractSignFacade.sendCustomerNoticeEmail(contractSignEntity.getId());
            }

            //完成时间修改协议签章转台
            contractSignEntity.setSignErrorCode(ContractSignYQQErrorEnum.MAIL_SEND_SUCCEED.getCode())
                    .setSignErrorMessage(ContractSignYQQErrorEnum.MAIL_SEND_SUCCEED.getMessage());

        } catch (Exception e) {
            e.printStackTrace();
            //发送警报
            DbzRedAlarmEntity redAlarmEntity = new DbzRedAlarmEntity();
            redAlarmEntity.setBizModule(BizLogModuleEnum.CUSTOMER_SIGN.name())
                    .setBombScene("saveFileItem")
                    .setReferBizId(contractSignEntity.getId())
                    .setReferBizCode(contractSignEntity.getContractCode())
                    .setMemo("文件上传失败" + e.getMessage());
            operationLogFacade.recordredalarm(redAlarmEntity);
            log.info("文件上传失败" + e.getMessage());
        }

        contractSignEntity.setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue());
        updateContractSignYQQ(contractSignEntity);*/

        /*
            // 下载签章完后的pdf
            fileProcessFacade.downLoadFileByUrl(url,
                    FilePathUtil.getCommonFilePath(ModuleType.CONTRACT,
                            FilePathType.PDF,
                            String.valueOf(contractEntity.getId())),
                    File.separator + FilePathUtil.genContractSignatureFileName(
                            contractEntity.getContractCode(),
                            FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getMsg(),
                            FilePathType.PDF.getValue()
                    ));
            // 上传并且保存 /{文件名称}.pdf
            fileProcessFacade.createFileItem(
                    FilePathUtil.getCommonFilePath(ModuleType.CONTRACT,
                            FilePathType.PDF,
                            String.valueOf(contractEntity.getId())) +
                            FilePathUtil.genContractSignatureFileName(
                                    contractEntity.getContractCode(),
                                    FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getMsg(),
                                    FilePathType.PDF.getValue()),contractEntity.getId(),
                    FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
            //修改合同签章状态*/


    }

    /**
     * 保存签章完成后的合同操作记录
     */
    private void addSignedContractLog(ContractSignEntity contractSignEntity, String bizCode, String operationName, String name) {
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(bizCode)
                .setOperationName(operationName)
                .setReferBizId(contractSignEntity.getId())
                .setReferBizCode(contractSignEntity.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT.getDesc())
                .setLogLevel(OperationSourceEnum.EMPLOYEE.getValue())
                .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                .setOperatorName(name)
                .setTargetRecordId(contractSignEntity.getContractId())
                .setTtCode(contractSignEntity.getTtCode())
                .setData(FastJsonUtils.getPropertyToJson("contractCode", contractSignEntity.getContractCode()))
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription());
        try {
            operationLogFacade.recordOperationLogDetail(operationDetailDTO);
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }
        //operationLogFacade.recordOperationLogOLD(operationDetailDTO);
    }

    /**
     * 信封流程启动
     *
     * @param envelopeStarted
     * @param eventType
     * @param responseDetail
     */
    private void envelopeStartedAnalysis(EnvelopeStarted envelopeStarted, String eventType, String responseDetail) {
        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO()
                .setContractUuId(envelopeStarted.getCustomTag())
                .setActionUrl(envelopeStarted.getActionUrl())
                .setAccount(envelopeStarted.getAccount())
                .setEventType(eventType);
        addSignatureLog(callbackFunctionDTO, responseDetail);
    }

    /**
     * 信封流程启动失败
     *
     * @param envelopeStarted
     * @param eventType
     * @param responseDetail
     */
    private void envelopeStartedFailedAnalysis(EnvelopeStarted envelopeStarted, String eventType, String responseDetail) {
        //根据uuid(自定义表示)查询协议数据
        ContractSignEntity contractSignEntity = contractSignFacade.queryByUUId(envelopeStarted.getCustomTag());
        //将错误编码传入协议中
        contractSignEntity
                .setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue())
                .setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue())
                .setSignErrorCode(envelopeStarted.getCode())
                .setSignErrorMessage(envelopeStarted.getMessage());
        updateContractSignYQQ(contractSignEntity);
        //记录易企签调用记录
        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO()
                .setContractUuId(envelopeStarted.getCustomTag())
                .setActionUrl(envelopeStarted.getActionUrl())
                .setAccount(envelopeStarted.getAccount())
                .setEventType(eventType);
        addSignatureLog(callbackFunctionDTO, responseDetail);
    }

    /**
     * 参与者拒绝
     *
     * @param participantRejected
     * @param eventType
     * @param responseDetail
     */
    private void participantRejectedAnalysis(ParticipantRejected participantRejected, String eventType, String responseDetail) {
        CallbackFunctionDTO callbackFunctionDTO = new CallbackFunctionDTO()
                .setContractUuId(participantRejected.getCustomTag())
                .setActionUrl(participantRejected.getActionUrl())
                .setName(participantRejected.getReceiverParticipant().getName())
                .setAccount(participantRejected.getAccount())
                .setAssignedSequence(participantRejected.getReceiverParticipant().getAssignedSequence())
                .setEventType(eventType);
        updateContract(callbackFunctionDTO);
        addSignatureLog(callbackFunctionDTO, responseDetail);
    }

    /**
     * 记录回调信息
     *
     * @param callbackFunctionDTO
     * @param responseDetail
     */
    private void addSignatureLog(CallbackFunctionDTO callbackFunctionDTO, String responseDetail) {
        try {
            List<DbtSignatureEntity> dbtSignatureEntities = iDbtSignatureService.getDbtSignatureEntityList(callbackFunctionDTO.getContractUuId());
            DbtSignatureEntity dbtSignatureEntity = new DbtSignatureEntity();
            if (!dbtSignatureEntities.isEmpty()) {
                dbtSignatureEntity = dbtSignatureEntities.get(0);
            }
            dbtSignatureEntity.setResponseDetail(responseDetail)
                    .setEventType(callbackFunctionDTO.getEventType())
                    .setSignatureUrl(callbackFunctionDTO.getActionUrl())
                    .setStatus(callbackFunctionDTO.getSuccess())
                    .setEnvelopeWsid(callbackFunctionDTO.getEnvelopeWsid())
                    .setParticipantWsid(callbackFunctionDTO.getParticipantWsid())
                    .setCreatedAt(null)
                    .setUpdatedAt(null);
            dbtSignatureDao.save(dbtSignatureEntity);
        } catch (Exception e) {
            log.error("记录信息错误:{}", e.getMessage());
        }
    }


    private void updateContract(CallbackFunctionDTO callbackFunctionDTO) {
        //根据唯一编号查询信息
        ContractSignEntity contractSignEntity = contractSignFacade.queryByUUId(callbackFunctionDTO.getContractUuId());
        if (WebhookEventType.PARTICIPANT_HANDLING.getEvent().equals(callbackFunctionDTO.getEventType())) {
            //参与者正在处理信封(待签章)
            //修改易企签状态
            if (callbackFunctionDTO.getAssignedSequence() == 1) {
                //保存签章地址
                ContractSignYQQCallbackDTO contractSignYQQCallbackDTO = new ContractSignYQQCallbackDTO();
                contractSignYQQCallbackDTO
                        .setUuid(callbackFunctionDTO.getContractUuId())
                        .setSignatureStatus(SignatureStatusEnum.LDC_AWAIT_SIGNATURE.getType())
                        .setVoluntarilySignType(VoluntarilySignTypeEnum.VOLUNTARILY_SIGN_FAILED.getValue())
                        .setSignatureUrl(callbackFunctionDTO.getActionUrl());
                contractSignFacade.updateContractSignYQQCallback(contractSignYQQCallbackDTO);
            } else {
                //发送邮件补充参数
                contractSignEntity
                        .setSignatureStatus(SignatureStatusEnum.CUSTOMER_AWAIT_SIGNATURE.getType())
                        .setSignErrorMessage("")
                        .setSignatureUrl(callbackFunctionDTO.getActionUrl());

                //保存签章地址
                ContractSignYQQCallbackDTO contractSignYQQCallbackDTO = new ContractSignYQQCallbackDTO();
                contractSignYQQCallbackDTO
                        .setUuid(callbackFunctionDTO.getContractUuId())
                        .setSignatureStatus(SignatureStatusEnum.CUSTOMER_AWAIT_SIGNATURE.getType())
                        .setSignatureUrl(callbackFunctionDTO.getActionUrl())
                        .setStatus(ContractSignStatusEnum.WAIT_BACK.getValue());

                contractSignFacade.updateContractSignYQQCallback(contractSignYQQCallbackDTO);

                //判断是否需要发送邮件
                signSendBothSignatureMail(contractSignEntity);
            }

        } else if (WebhookEventType.PARTICIPANT_CONFIRMED.getEvent().equals(callbackFunctionDTO.getEventType())) {
            //参与者确认(签章完成)
            //修改合同签章状态
            if (callbackFunctionDTO.getAssignedSequence() == 1) {

                LogBizCodeEnum logBizCodeEnum;
                if (StringUtil.isNotEmpty(contractSignEntity.getSignatureUrl())) {
                    logBizCodeEnum = LogBizCodeEnum.COMPLETE_SIGN_SALES_CONTRACT_SIGN;
                } else {
                    logBizCodeEnum = LogBizCodeEnum.COMPLETE_SIGN_VOLUNTARILY_SIGN;
                }
                //判断客户是否已经签章
                ContractSignYQQCallbackDTO contractSignYQQCallbackDTO = new ContractSignYQQCallbackDTO();
                contractSignYQQCallbackDTO
                        .setUuid(callbackFunctionDTO.getContractUuId());
                if (IsCustomerSignatureEnum.AlREADY_SIGN.getValue() == contractSignEntity.getIsCustomerSignature()) {
                    contractSignYQQCallbackDTO.setStatus(ContractSignStatusEnum.WAIT_CONFIRM.getValue());
                    //修改协议
                    contractSignFacade.updateContractSignYQQCallback(contractSignYQQCallbackDTO);
                } else {
                    contractSignYQQCallbackDTO
                            .setStatus(ContractSignStatusEnum.WAIT_BACK.getValue());
                    //修改协议
                    contractSignFacade.updateContractSignYQQCallback(contractSignYQQCallbackDTO);
                }


                addSignedContractLog(contractSignEntity, logBizCodeEnum.getBizCode(), logBizCodeEnum.getMsg(), callbackFunctionDTO.getName());

            } else {

                ContractSignYQQCallbackDTO contractSignYQQCallbackDTO = new ContractSignYQQCallbackDTO();
                contractSignYQQCallbackDTO
                        .setUuid(callbackFunctionDTO.getContractUuId())
                        .setSignatureStatus(SignatureStatusEnum.CUSTOMER_ALREADY_SIGNATURE.getType())
                        .setBackStatus(BackStatusEnum.YQQ.getValue())
                ;

                contractSignEntity.setSignatureStatus(SignatureStatusEnum.CUSTOMER_ALREADY_SIGNATURE.getType());
                //修改协议
                contractSignFacade.updateContractSignYQQCallback(contractSignYQQCallbackDTO);
                addSignedContractLog(contractSignEntity, LogBizCodeEnum.COMPLETE_SIGN_SALES_CUSTOMER_SIGN.getBizCode(), LogBizCodeEnum.COMPLETE_SIGN_SALES_CUSTOMER_SIGN.getMsg(), callbackFunctionDTO.getName());
            }

        } else if (WebhookEventType.ENVELOPE_COMPLETED_SUCCEED.getEvent().equals(callbackFunctionDTO.getEventType())) {
            //流程完成
            contractSignEntity
                    .setSignatureStatus(SignatureStatusEnum.ALREADY_SIGNATURE.getType());
            updateContractSignYQQ(contractSignEntity);
        } else if (WebhookEventType.PARTICIPANT_REJECTED.getEvent().equals(callbackFunctionDTO.getEventType())) {
            //参与者拒绝
            contractSignEntity
                    .setStatus(ContractSignStatusEnum.WAIT_REVIEW.getValue())
                    .setSignatureUrl(null);
            updateContractSignYQQ(contractSignEntity);
        }
    }


    private void updateContractSignYQQ(ContractSignEntity contractSignEntity) {

        ContractSignYQQUrlDTO contractSignYQQUrlDTO = BeanConvertUtils.convert(ContractSignYQQUrlDTO.class, contractSignEntity);

        contractSignFacade.updateContractSignYQQUrl(contractSignYQQUrlDTO);
    }

    /**
     * 客户双签发邮件签章链接
     *
     * @param contractSignEntity
     */
    public void signSendBothSignatureMail(ContractSignEntity contractSignEntity) {

        StringBuilder errorMsg = new StringBuilder();

        try {
            //记录日志
            operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail", JSON.toJSONString(contractSignEntity)));
        } catch (Exception e) {
            log.error("记录日志错误signSendBothSignatureMail:{}", JSON.toJSONString(e));
        }
        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();

        messageInfoDTO.setReferBizId(contractSignEntity.getId().toString());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name());
        messageInfoDTO.setFactoryCode(contractSignEntity.getDeliveryFactoryCode());
        messageInfoDTO.setCategoryId(contractSignEntity.getGoodsCategoryId());
        messageInfoDTO.setSalesType(contractSignEntity.getSalesType());

        Integer customerId;
        if (contractSignEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            customerId = contractSignEntity.getCustomerId();
        } else {
            customerId = Integer.valueOf(contractSignEntity.getSupplierId());
        }

        try {
            List<String> receivers = new ArrayList<>();
            receivers.add(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                    ? contractSignEntity.getSupplierId() : customerId.toString());
            messageInfoDTO.setReceivers(receivers);
        } catch (NumberFormatException e) {
            errorMsg.append("获取客户联系人异常");
        }
        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = getCustomerNoticeEmailData(contractSignEntity);

        //业务数据
        messageInfoDTO.setDataMap(BeanUtil.beanToMap(customerSignNoticeItemDTO));

        //发送消息
        try {
            messageFacade.sendMessage(messageInfoDTO);
        } catch (Exception e) {
            errorMsg.append("消息服务异常:" + e.getMessage());
        }

        try {
            //记录traceLog
            if (StringUtil.isNotEmpty(errorMsg.toString())) {
                //记录日志
                operationLogFacade.saveTraceLog(new TraceLogDTO(contractSignEntity.getId().toString(), "SignService.sendCustomerNoticeEmail.Exception", JSON.toJSONString(errorMsg)));
            }
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }

    }


    public CustomerSignNoticeItemDTO getCustomerNoticeEmailData(ContractSignEntity contractSignEntity) {

        CustomerSignNoticeItemDTO customerSignNoticeItemDTO = new CustomerSignNoticeItemDTO();

        customerSignNoticeItemDTO.setSignId(contractSignEntity.getId())
                .setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_SIGNATURE_NOTICE.name())
                .setFactoryCode(contractSignEntity.getDeliveryFactoryCode())
                .setCategoryId(contractSignEntity.getGoodsCategoryId())
                .setSalesType(contractSignEntity.getSalesType())
                .setCategoryCode(GoodsCategoryEnum.getDescByValue(contractSignEntity.getGoodsCategoryId()))
                .setContractCode(contractSignEntity.getContractCode())
                .setContractFilePath(contractSignEntity.getSignatureUrl())
                .setCustomerId(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? Integer.valueOf(contractSignEntity.getSupplierId()) : contractSignEntity.getCustomerId())
                .setCustomerName(contractSignEntity.getSalesType() == ContractSalesTypeEnum.PURCHASE.getValue()
                        ? contractSignEntity.getSupplierName() : contractSignEntity.getCustomerName());
        //customerSignNoticeItemDTO.setSender(ReceiverTypeEnum.FACTORY_MAIL.getValue());

        return customerSignNoticeItemDTO;
    }
}
