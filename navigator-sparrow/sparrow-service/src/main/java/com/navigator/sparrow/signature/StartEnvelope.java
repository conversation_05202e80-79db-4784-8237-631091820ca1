package com.navigator.sparrow.signature;

import cn.hutool.json.JSONUtil;
import cn.signit.sdk.SignitClient;
import cn.signit.sdk.SignitException;
import cn.signit.sdk.pojo.*;
import cn.signit.sdk.pojo.request.StartEnvelopeRequest;
import cn.signit.sdk.pojo.response.StartEnvelopeResponse;
import cn.signit.sdk.type.*;
import cn.signit.sdk.util.FastjsonDecoder;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.*;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.http.OkHttpUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.LdcFrameEnum;
import com.navigator.customer.pojo.enums.UseYqqEnum;
import com.navigator.sparrow.dao.DbtSignatureDao;
import com.navigator.sparrow.dao.YqqTaskRecordDao;
import com.navigator.sparrow.pojo.dto.*;
import com.navigator.sparrow.pojo.entity.DbtSignatureEntity;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.pojo.enums.YqqTaskEnum;
import com.navigator.sparrow.service.IDbtSignatureService;
import com.navigator.sparrow.service.YqqSignParameterService;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.dto.contractsign.ContractSignYQQUrlDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 10:23
 */
@Slf4j
@Component
public class StartEnvelope {
    @Resource
    private DbtSignatureDao dbtSignatureDao;
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private OkHttpUtil okHttpUtil;
    @Resource
    private IDbtSignatureService signatureService;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private YqqTaskRecordDao yqqTaskRecordDao;
    @Resource
    private YqqSignParameterService yqqSignParameterService;
    @Resource
    private SystemRuleFacade systemRuleFacade;


    @Async
    public ErrorResp envelopesStart(StartEnvelopeDTO startEnvelopeDTO) {

        String uuid = startEnvelopeDTO.getContractSignId() + "_" + startEnvelopeDTO.getUuid();

        //保存发送记录
        YqqTaskRecordEntity yqqTaskRecordEntity = new YqqTaskRecordEntity()
                .setData(JSON.toJSONString(startEnvelopeDTO))
                .setBizId(startEnvelopeDTO.getContractSignId())
                .setBizUuCode(startEnvelopeDTO.getUuid())
                .setType(YqqTaskEnum.REQUEST.getValue());

        //防重
        if (null == yqqTaskRecordDao.getTaskByBizId(startEnvelopeDTO.getContractSignId(), YqqTaskEnum.REQUEST.getValue())) {
            //保存签章参数
            yqqTaskRecordDao.save(yqqTaskRecordEntity);
        } else {
            //保存签章参数
            yqqTaskRecordDao.updateById(yqqTaskRecordEntity);
        }

        YqqSignParameterEntity yqqSignParameterEntity = startEnvelopeDTO.getYqqSignParameterEntity();


        log.info(String.format("yqqSignParameterEntity.uuid:{},{}", startEnvelopeDTO.getUuid(), JSON.toJSONString(yqqSignParameterEntity)));

        // step1: 初始化易企签开放平台客户端
        // 易企签签章地址 yqqSignParameterEntity.getAppSecretKey()
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
        String appUrl = yqqSignParameterEntity.getUrl() + "/v1/open/envelopes/start";
        SignitClient client = new SignitClient(yqqSignParameterEntity.getAppId(), yqqSignParameterEntity.getAppSecretKey(), appUrl);
        client.setOauthUrl(yqqSignParameterEntity.getUrl() + "/v1/oauth/oauth/token");
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end

        //组装请求信息
        StartEnvelopeRequest request = startEnvelopeWithExpire(startEnvelopeDTO, yqqSignParameterEntity);

        //记录易企签请求日志和系统操作日志
        addStartSignContractLog(startEnvelopeDTO, appUrl, JSON.toJSONString(request), JSON.toJSONString(request, true), "组装请求数据");

        log.info(String.format("envelopesStart.{%s}，参数{%s}|{%s}|{%s}|{%s}", uuid, appUrl, yqqSignParameterEntity.getAppId(), yqqSignParameterEntity.getAppSecretKey(), request));
        log.info(String.format("envelopesStart.{%s}，clientinfo={%s}", uuid, JSON.toJSONString(client, true)));

        // step3: 执行请求,获得响应
        StartEnvelopeResponse response = null;
        ErrorResp errorResp = null;
        try {
            //发起并接收请求
            response = client.execute(request);

            //记录易企签请求日志和系统操作日志
            addStartSignContractLog(startEnvelopeDTO, appUrl, JSON.toJSONString(response), JSON.toJSONString(request, true), "发起请求");

        } catch (SignitException e) {
            //记录易企签请求日志和系统操作日志
            addStartSignContractLog(startEnvelopeDTO, appUrl, JSON.toJSONString(e.getMessage()), JSON.toJSONString(request, true), "调用异常");

            log.info(String.format("envelopesStart.{%s}，SignitException={%s}", uuid, JSON.toJSONString(e, true)));

            //解析易企签返回的异常信息（JSON）
            if (FastjsonDecoder.isValidJson(e.getMessage())) {

                //解码异常信息
                errorResp = FastjsonDecoder.decodeAsBean(e.getMessage(), ErrorResp.class);

                log.info(String.format("envelopesStart.{%s}，SignitException={%s}", uuid, JSON.toJSONString(errorResp, true)));

                //记录易企签请求日志和系统操作日志
                addStartSignContractLog(startEnvelopeDTO, appUrl, JSON.toJSONString(response), JSON.toJSONString(request, true), "易企签异常信息处理");
                //更新签章记录
                updateContractSignYQQ(startEnvelopeDTO.getUuid(), errorResp);
            } else {
                //更新签章记录
                updateContractSignYQQ(startEnvelopeDTO.getUuid(), errorResp);
            }

        }

        return null;
    }


    /**
     * 网络问题发起签章失败后,延时调用签章
     *
     * @param startEnvelopeDTO
     * @param num
     */
/*    private void retryExecute(StartEnvelopeDTO startEnvelopeDTO, Integer num) {
        if (10 > num) {
            long delay = 30;
            ++num;
            Integer finalNum = num;
            //创建一个单线程的线程池
            ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
            // 第二个参数为首次执行的延时时间，第三个参数为定时执行的间隔时间
            service.schedule(() -> this.envelopesStart(startEnvelopeDTO, finalNum),
                    delay, TimeUnit.SECONDS);
            addStartSignContractLog(startEnvelopeDTO, "", "", "第" + num + "次重新发起签章", "重新发起调用");
        } else {
            DbtSignatureEntity dbtSignatureEntity = new DbtSignatureEntity()
                    .setContractCode(startEnvelopeDTO.getContractCode())
                    .setUniqueContractCode(startEnvelopeDTO.getUuid())
                    .setRequestType("发起签章失败");
            dbtSignatureDao.save(dbtSignatureEntity);
        }

    }*/


    private void updateContractSignYQQ(String uuid, ErrorResp errorResp) {
        ContractSignEntity contractSignEntity = contractSignFacade.queryByUUId(uuid);
        contractSignEntity.setSignErrorCode(null == errorResp ? ContractSignYQQErrorEnum.SIGN_FAILED.getCode() : errorResp.getCode());
        contractSignEntity.setSignErrorMessage(null == errorResp ? ContractSignYQQErrorEnum.SIGN_FAILED.getMessage() : errorResp.getDeveloperMessage());
        contractSignEntity.setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue());
        contractSignEntity.setVoluntarilySignType(VoluntarilySignTypeEnum.SIGN_FAILED.getValue());
        contractSignEntity.setSignatureType(SignatureTypeEnum.OFFLINE.getValue());
        ContractSignYQQUrlDTO contractSignYQQUrlDTO = BeanConvertUtils.convert(ContractSignYQQUrlDTO.class, contractSignEntity);

        contractSignFacade.updateContractSignYQQUrl(contractSignYQQUrlDTO);

    }


    /**
     * 拼接标准签的json字段
     *
     * @param startEnvelopeDTO
     * @return
     */
    private StartEnvelopeRequest startEnvelopeWithExpire(StartEnvelopeDTO startEnvelopeDTO, YqqSignParameterEntity yqqSignParameterEntity) {

        //根据uuid查询是协议
        ContractSignEntity contractSignEntity = contractSignFacade.queryByUUId(startEnvelopeDTO.getUuid());
        //判断是销售还是采购  签章顺序
        String LDCSignEnterprise = null;
        String CustomerSignEnterprise = null;
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.HUSKY_CONTRACT_PROVIDE.getRuleCode()));

        String buy = "买 方";

        String sell = "卖 方";

        if (null == systemRuleVO) {
            if (!CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList())) {
                if (DisableStatusEnum.DISABLE.getValue().equals(systemRuleVO.getSystemRuleItemVOList().get(0).getStatus())) {
                    buy = "买方";
                    sell = "卖方";
                }
            }
        }

        if (contractSignEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()) {
            startEnvelopeDTO.setLDCSignature(sell + "：" + contractSignEntity.getSupplierName());
            startEnvelopeDTO.setCustomerSignature(buy + "：" + contractSignEntity.getCustomerName());
            startEnvelopeDTO.setCustomerId(contractSignEntity.getCustomerId());

            LDCSignEnterprise = contractSignEntity.getSupplierName();
            CustomerSignEnterprise = contractSignEntity.getCustomerName();

        } else {
            startEnvelopeDTO.setLDCSignature(buy + "：" + contractSignEntity.getCustomerName());
            startEnvelopeDTO.setCustomerSignature(sell + "：" + contractSignEntity.getSupplierName());
            startEnvelopeDTO.setCustomerId(Integer.valueOf(contractSignEntity.getSupplierId()));

            LDCSignEnterprise = contractSignEntity.getCustomerName();
            CustomerSignEnterprise = contractSignEntity.getSupplierName();
        }

        startEnvelopeDTO.setLDCSignEnterprise(LDCSignEnterprise)
                .setCustomerSignEnterprise(CustomerSignEnterprise);
        StartEnvelopeRequest startEnvelopeRequest = new StartEnvelopeRequest();
        //信封基本信息对象
        EnvelopeBasicInfo.Builder builder = startEnvelopeRequest.getBasicInfo().builder()
                //信封标题
                .title(startEnvelopeDTO.getTitle())
                //信封主题
                .subject(contractSignEntity.getContractCode())
                //逾期时间天数
                //todo 逾期时间 测试用一天
                .expire(100);
        //签署文件内容
        EnvelopeContentInfo.Builder envelopeContentInfo = startEnvelopeRequest.getContentInfo().builder()
                .files(EnvelopeFile.builder()
                        .id(startEnvelopeDTO.getFileId().toString())//待签文件唯一标识ID
                        .sequence(1)//该文件排列顺序
                        .data(BaseFileData.builder()//部署文件数据
                                //待签名文件的URL地址
                                .url(startEnvelopeDTO.getUrl()))
                        .isAttached(false));

        //签署参与者信息
        EmployEntity employEntity = employFacade.getEmployById(startEnvelopeDTO.getLoginId());
        EnvelopeParticipantInfo.Builder sender = startEnvelopeRequest.getParticipantInfo().builder()
                .sender(Sender.builder()//发送方信息
                        .name(employEntity.getName())//发送方姓名
                        .contact(Contact.builder()//发送方联系方式
                                .phone(employEntity.getPhone()))//手机号(邮箱)
                        .deleteCompletedEnvelope(false)
                        .enableEmbeddedMode(true));
        //拼接签章人信息
        List<ReceiverDTO> receiverDTOS = new ArrayList<>();
        if (SystemEnum.MAGELLAN.getValue() == startEnvelopeDTO.getSystem()) {

            //LDC发起申请
            CustomerDTO customerDTO = customerFacade.getCustomerById(startEnvelopeDTO.getCustomerId());
            startEnvelopeDTO.setCustomerName(customerDTO.getName())
                    .setIsCustomerSignature(contractSignEntity.getIsCustomerSignature());
            receiverDTOS = customerForms(receiverDTOS, startEnvelopeDTO, 1, yqqSignParameterEntity);

        } else if (SystemEnum.COLUMBUS.getValue() == startEnvelopeDTO.getSystem()) {
            //查询客户信息
            CustomerDTO customerDTO = customerFacade.getCustomerById(startEnvelopeDTO.getCustomerId());
            if (customerDTO.getUseYqq().equals(UseYqqEnum.USE_YQQ.getValue())) {
                receiverDTOS = customerSignature(receiverDTOS, 1, startEnvelopeDTO, yqqSignParameterEntity);
            }
        }

        List<Receiver> receiverList = receiver(receiverDTOS, startEnvelopeDTO.getFileId().toString(),
                startEnvelopeDTO.getPageNo(), startEnvelopeDTO.getCompanyId(), yqqSignParameterEntity);

        /*List<ReceiverDTO> receiverDTOS = customerForms(startEnvelopeDTO.getCustomerId());
        List<Receiver> receiverList = receiver(receiverDTOS, startEnvelopeDTO.getFileId().toString());*/
        //拼接内容
        return StartEnvelopeRequest.builder()
                .basicInfo(builder)
                .contentInfo(envelopeContentInfo)
                .participantInfo(sender.receivers(receiverList))
                //.returnUrl("https://public.inner.efunong.com/12320/marketContract/contractIssueLdc?status=4")//自定义签署完成后跳转的URL
                .acceptDataType(AcceptDataType.URL)//自定义可接收数据类型
                .customTag(startEnvelopeDTO.getUuid())//自定义标识
                .build();
    }

    /**
     * 拼接接收方信息
     *
     * @param receiverDTOS
     * @return
     */
    private List<Receiver> receiver(List<ReceiverDTO> receiverDTOS, String fileId, Integer pageNo, Integer companyId, YqqSignParameterEntity yqqSignParameterEntity) {
        List<Receiver> receiverList = new ArrayList<>();
        for (ReceiverDTO receivers : receiverDTOS) {
            Receiver receiver = new Receiver();
            //设置接收方处理顺序
            receiver.setAssignedSequence(receivers.getAssignedSequence());
            receiver.setSelectedAuthTypes(Arrays.asList(AuthType.SIGN_PIN, AuthType.SMS_CODE, AuthType.EMAIL_CODE));
            receiver.setEnterpriseName(receivers.getEnterpriseName());
            //receiver.setEnterpriseName("路易达孚有限公司-测试");
            receiver.setName(receivers.getReceiverName());
            receiver.setNeedForm(false);
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
            if (SignatureTypeEnum.BOTH_SIGNATURE.getValue() == receivers.getSignatureType() && SignatureTypeEnum.OPEN_SIGN_GROUP.getValue() == Integer.parseInt(yqqSignParameterEntity.getSignGroup())) {
                receiver.setRoleType(EnvelopeRoleType.ENTERPRISE_SIGN_GROUP);
            } else {
                receiver.setRoleType(EnvelopeRoleType.ENTERPRISE_MEMBER);
            }
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
            receiver.setFreeLoginSignTtl(0L);
            receiver.setAllowAddForms(true);
            receiver.setSecureLevel(SecureLevel.DISPOSABLE_CERT);
            receiver.setType(ReceiverType.SIGNER);
            receiver.setEnableEmbeddedMode(true);
            Contact contact = new Contact();
            contact.setPhone(receivers.getReceiverContact());
            receiver.setContact(contact);

            /*//查询系统操作人信息
            EmployEntity employEntity = employFacade.getEmployById(loginId);*/
            //预设表单信息
            List<PresetForm> presetForms = new ArrayList<>();
            //判断是否LdcFrame
            if (receivers.getLdcFrame().equals(LdcFrameEnum.LDC.getValue())) {
                presetForms = this.presetFormsList(receivers.getKeyword(), fileId, pageNo, receiver.getAssignedSequence(), companyId, yqqSignParameterEntity);
                if (1 == receiver.getAssignedSequence()) {
                    //LDC发起静默签
                    receiver.setHandleMode(ParticipantHandleMode.SILENCE);

                }
            }

            receiver.setPresetForms(presetForms);
            /*//签章信息
            InitialValue initialValue = new InitialValue();
            //渲染模式
            initialValue.setRenderingMode(RenderMode.GRAPHIC);
            SealData sealData = new SealData();
            //签章名称
            sealData.setName(envelopesStart.getSealDataName());
            initialValue.setSealData(sealData);*/
            receiverList.add(receiver);
        }
        return receiverList;
    }


    /**
     * 拼接签章人信息(LDC)
     *
     * @param receiverDTOS
     * @param startEnvelopeDTO
     * @param sequence
     * @return
     */
    private List<ReceiverDTO> customerForms(List<ReceiverDTO> receiverDTOS,
                                            StartEnvelopeDTO startEnvelopeDTO,
                                            Integer sequence,
                                            YqqSignParameterEntity yqqSignParameterEntity) {
        //todo 谁登录谁签章 后面查询签章角色进行签章
        EmployEntity employEntity = employFacade.getEmployById(startEnvelopeDTO.getLoginId());
        //达孚签章字段拼接
        ReceiverDTO receiver = new ReceiverDTO()
                .setReceiverName(employEntity.getName())
                .setReceiverContact(employEntity.getPhone())
                //todo 测试先写死 取配置
                //.setEnterpriseName(startEnvelopeDTO.getLDCSignEnterprise())
                .setEnterpriseName(yqqSignParameterEntity.getEnterpriseName())
                .setKeyword(startEnvelopeDTO.getLDCSignature())
                .setIsCustomerSignature(startEnvelopeDTO.getIsCustomerSignature())
                .setLdcFrame(startEnvelopeDTO.getLdcFrame())
                .setAssignedSequence(sequence);
        receiverDTOS.add(receiver);
        //客户签章字段拼接
        if (startEnvelopeDTO.getIsCustomerUseYqq() && startEnvelopeDTO.getCustomerId() != null &&
                IsCustomerSignatureEnum.AlREADY_SIGN.getValue() != startEnvelopeDTO.getIsCustomerSignature()) {
            startEnvelopeDTO.setSystem(SystemEnum.MAGELLAN.getValue());
            this.customerSignature(receiverDTOS, 2, startEnvelopeDTO, yqqSignParameterEntity);
        }
        return receiverDTOS;
    }

    /**
     * 拼接签章人信息(客户)
     *
     * @param receiverDTOS
     * @return
     */
    private List<ReceiverDTO> customerSignature(List<ReceiverDTO> receiverDTOS,
                                                Integer sequence,
                                                StartEnvelopeDTO startEnvelopeDTO,
                                                YqqSignParameterEntity yqqSignParameterEntity) {

        //EmployEntity employEntity = employFacade.queryEmployByCustomerId(startEnvelopeDTO.getCustomerId());
        ReceiverDTO receiverDTO = new ReceiverDTO()
                //企业名称
                .setSignatureType(SignatureTypeEnum.BOTH_SIGNATURE.getValue())
                .setEnterpriseName(startEnvelopeDTO.getCustomerSignEnterprise())
                //.setEnterpriseName(envelopesStart.getSealDataName())
                .setKeyword(startEnvelopeDTO.getCustomerSignature())
                .setLdcFrame(startEnvelopeDTO.getLdcFrame())
                .setAssignedSequence(sequence);

        if (SignatureTypeEnum.OPEN_SIGN_GROUP.getValue() != Integer.parseInt(yqqSignParameterEntity.getSignGroup())) {
            List<CEmployEntity> employEntity = new ArrayList<>();
            Result result = cEmployFacade.getEmployByCustomerId(startEnvelopeDTO.getCustomerId(), CEmployTypeEnum.DEFAULT.getType());
            if (result.isSuccess()) {
                employEntity = JSON.parseArray(JSON.toJSONString(result.getData()), CEmployEntity.class);
            }
            if (employEntity.isEmpty()) {
                return receiverDTOS;
            }
            receiverDTO.setReceiverName(employEntity.get(0).getName())
                    .setReceiverContact(employEntity.get(0).getPhone());
        }

        receiverDTOS.add(receiverDTO);

        if (startEnvelopeDTO.getSystem() == SystemEnum.COLUMBUS.getValue()) {
            this.customerForms(receiverDTOS, null, 2, yqqSignParameterEntity);
        }

        return receiverDTOS;
    }

    /**
     * 预设表单信息(签章位置,根据关键字进行签章)
     *
     * @param keyword
     * @return
     */
    private List<PresetForm> presetFormsList(String keyword,
                                             String fileId,
                                             Integer pageNo,
                                             Integer assignedSequence,
                                             Integer companyId,
                                             YqqSignParameterEntity yqqSignParameterEntity) {
        List<PresetForm> presetForms = new ArrayList<>();
        PresetForm presetForm = new PresetForm();
        presetForm.setFileId(fileId);
        presetForm.setFormType(FormType.SEAL_SIGN);
        presetForm.setRevisable(false);
        presetForm.setScale(1.0f);
        //签署位置
        Signer.Position position = new Signer.Position();
        //关键字定位的数据
        Signer.Position.KeywordPosition keywordPosition = new Signer.Position.KeywordPosition();
        keywordPosition.setWidth(115f);//矩形的宽度
        keywordPosition.setRelativeWidthRatio(1.0f);//矩形框宽度相对于关键字宽度的比率
        keywordPosition.setHeight(115f);//矩形框的高度
        keywordPosition.setRelativeHeightRatio(1.0f);//矩形框高度相对于关键字高度的比率
        keywordPosition.setDirection(Direction.TOP);//偏移方向
        keywordPosition.setOffset(1.0f);//偏移量
        keywordPosition.setRelativeOffsetRatio(0.1f);//在偏移方向上，相对于该方向矩形框大小的比率
        keywordPosition.setxOffset(35f);//x额外偏移量
        keywordPosition.setyOffset(95f);//y额外偏移量
        keywordPosition.setKeyword(keyword);//关键字
        keywordPosition.setPages("all");//关键字需要搜索的页数
        //keywordPosition.setIndex(0);//关键字索引
        keywordPosition.setReverseIndex(true);
        position.setKeywordPosition(keywordPosition);
        presetForm.setPosition(position);

        InitialValue initialValue = new InitialValue();
        //渲染模式
        initialValue.setRenderingMode(RenderMode.GRAPHIC);
        //印章的名称
        SealData sealData = new SealData();

        if (1 == assignedSequence) {
            sealData.setName(yqqSignParameterEntity.getSealName());
        } else {
            sealData.setName("章");
        }

        initialValue.setSealData(sealData);
        presetForm.setInitialValue(initialValue);
        presetForms.add(presetForm);

        //骑缝章字段拼接
        if (1 < pageNo) {
            if (1 == assignedSequence) {
                sealData.setName(yqqSignParameterEntity.getSealName());
                presetForms.add(this.gapSignature(fileId, yqqSignParameterEntity));
            } else if (2 == assignedSequence) {
                presetForms.add(this.customerGapSignature(fileId));
            }
        }

        return presetForms;
    }


    /**
     * 拼接骑缝章参数
     *
     * @return
     */
    public PresetForm gapSignature(String fileId, YqqSignParameterEntity yqqSignParameterEntity) {
        PresetForm presetForm = new PresetForm();
        presetForm.setFormType(FormType.MULTI_CHECK_MARK);//表单类型  MULTI_CHECK_MARK 骑缝章表单域
        presetForm.setFileId(fileId);//当前表单要设置到的文件id
        presetForm.setMargin(0f);//跨页表单距离页面边缘边距
        presetForm.setOffset(100f);//防伪标记中心偏移量
        presetForm.setCertPages("all");//有证书签名的页数
        presetForm.setPixel(10);//单位图片所占像素
        presetForm.setSinglePageMark(true);//单页数是否加盖骑缝章
        presetForm.setScale(1.0f);//缩放比例
        presetForm.setResizable(true);//是否重设骑缝章尺寸
        presetForm.setResizeWidth(115f);//重设骑缝章尺寸宽度
        presetForm.setResizeHeight(115f);//重设骑缝章尺寸高度
        presetForm.setRevisable(true);
        InitialValue initialValue = new InitialValue();
        //渲染模式
        initialValue.setRenderingMode(RenderMode.GRAPHIC);
        //印章的名称
        SealData sealData = new SealData();
        sealData.setName(yqqSignParameterEntity.getSealName());
        initialValue.setSealData(sealData);
        presetForm.setInitialValue(initialValue);
        return presetForm;
    }

    /**
     * 拼接客户骑缝章参数
     *
     * @return
     */
    public PresetForm customerGapSignature(String fileId) {
        PresetForm presetForm = new PresetForm();
        presetForm.setFormType(FormType.MULTI_CHECK_MARK);//表单类型  MULTI_CHECK_MARK 骑缝章表单域
        presetForm.setFileId(fileId);//当前表单要设置到的文件id
        presetForm.setMargin(0f);//跨页表单距离页面边缘边距
        presetForm.setOffset(400f);//防伪标记中心偏移量
        presetForm.setCertPages("all");//有证书签名的页数
        presetForm.setPixel(10);//单位图片所占像素
        presetForm.setSinglePageMark(true);//单页数是否加盖骑缝章
        presetForm.setScale(1.0f);//缩放比例
        presetForm.setResizable(true);//是否重设骑缝章尺寸
        presetForm.setResizeWidth(115f);//重设骑缝章尺寸宽度
        presetForm.setResizeHeight(115f);//重设骑缝章尺寸高度
        presetForm.setRevisable(true);
        InitialValue initialValue = new InitialValue();
        //渲染模式
        initialValue.setRenderingMode(RenderMode.GRAPHIC);
        //印章的名称
        SealData sealData = new SealData();
        sealData.setName("章");
        initialValue.setSealData(sealData);
        presetForm.setInitialValue(initialValue);
        return presetForm;
    }

    /**
     * 撤销易企签签章
     *
     * @param startEnvelopeDTO
     * @return
     * @throws Exception
     */
    public boolean revocation(StartEnvelopeDTO startEnvelopeDTO) {
        //撤销易企签签章
        DbtSignatureEntity dbtSignatureEntity = signatureService.getSignatureEntityBy(startEnvelopeDTO.getUuid());
        try {
            RevokeReasonDTO request = new RevokeReasonDTO();
            request.setRevokeReason(startEnvelopeDTO.getRevokeReason());

            //将实体类转换成Gson格式
            Gson gson = new GsonBuilder().create();
            String reqParams = gson.toJson(request);
            YqqSignParameterEntity yqqSignParameterEntity = yqqSignParameterService.queryYqqSignParameterByCompanyId(startEnvelopeDTO.getCompanyId());
            //获取token拼接链接地址
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
            String urlJoint = yqqSignParameterEntity.getUrl() + "/v1/oauth/oauth/token?client_id=" + yqqSignParameterEntity.getAppId() + "&client_secret=" + yqqSignParameterEntity.getAppSecretKey() + "&grant_type=client_credentials";
            String response = okHttpUtil.get(urlJoint);
            OauthTokenDTO oauthTokenDTO = FastJsonUtils.getJsonToBean(response, OauthTokenDTO.class);

            //拼接撤销地址
            String appUrl = yqqSignParameterEntity.getUrl() + "/v1/open/envelopes/" + dbtSignatureEntity.getEnvelopeWsid() + "/participants/" + dbtSignatureEntity.getParticipantWsid() + "/revoke?access_token=" + oauthTokenDTO.getAccess_token();
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
            //拼接请求头
            Map<String, Object> headers = new HashMap<>();
            headers.put("X-Signit-App-Id", yqqSignParameterEntity.getAppId());

            //验证用户是否实名
            String verificationsCheck = okHttpUtil.sendJsonByPostReq(appUrl, reqParams, headers, "UTF-8");
            AbstractSignatureResponse abstractSignatureResponse = FastJsonUtils.getJsonToBean(verificationsCheck, AbstractSignatureResponse.class);//操作记录

            this.saveSignature(startEnvelopeDTO, appUrl, JSON.toJSONString(response));
            return abstractSignatureResponse.getResult();
        } catch (Exception e) {
            ErrorResp errorResp = null;
            if (FastjsonDecoder.isValidJson(e.getMessage())) {
                errorResp = FastjsonDecoder.decodeAsBean(e.getMessage(), ErrorResp.class);
            }
            log.info("\n撤销易企签签章错误 response is:\n" + JSON.toJSONString(errorResp, true));
        }
        return false;
    }

    /**
     * 操作记录
     *
     * @param startEnvelopeDTO
     * @param appUrl
     * @param response
     */
    private void saveSignature(StartEnvelopeDTO startEnvelopeDTO, String appUrl, String response) {
        DbtSignatureEntity dbtSignatureEntity = new DbtSignatureEntity()
                .setContractCode(startEnvelopeDTO.getContractCode())
                .setUniqueContractCode(startEnvelopeDTO.getUuid())
                .setRequestType("标准签章")
                .setRequestUrl(appUrl)
                .setRequestDetail(response);
        dbtSignatureDao.save(dbtSignatureEntity);
    }


    /**
     * 记录发起签章的记录
     *
     * @param startEnvelopeDTO
     * @param appUrl
     * @param response
     */
    private void addStartSignContractLog(StartEnvelopeDTO startEnvelopeDTO, String appUrl, String response, String request, String status) {
        DbtSignatureEntity dbtSignatureEntity = new DbtSignatureEntity()
                .setContractCode(startEnvelopeDTO.getContractCode())
                .setUniqueContractCode(startEnvelopeDTO.getUuid())
                .setRequestType("标准签章")
                .setRequestUrl(appUrl)
                .setResponseDetail(response)
                .setRequestDetail(request)
                .setStatus(status);
        dbtSignatureDao.save(dbtSignatureEntity);

        int i;
        // 记录合同发起签章记录
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                .setBizCode(LogBizCodeEnum.START_SIGN_SALES_CONTRACT_SIGN.getBizCode())
                .setOperationName(LogBizCodeEnum.START_SIGN_SALES_CONTRACT_SIGN.getMsg())
                .setReferBizId(startEnvelopeDTO.getContractSignId())
                .setReferBizCode(startEnvelopeDTO.getContractCode())
                .setBizModule(ModuleTypeEnum.CONTRACT_SIGN.getDesc())
                .setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.parseInt("1"))
                .setOperatorName("【系统】")
                .setReferOperation("易企签")
                .setReferOperationRecordId(dbtSignatureEntity.getId())
                .setReferOperationData(JSONUtil.toJsonStr(startEnvelopeDTO))
                .setMetaData(JSON.toJSONString(dbtSignatureEntity))
                .setData(JSON.toJSONString(startEnvelopeDTO))
                .setOperationInfo("签章地址：" + startEnvelopeDTO.getUrl())
                //.setData(FastJsonUtils.getPropertyToJson("contractCode", startEnvelopeDTO.getContractCode()))
                .setTriggerSys(SystemEnum.MAGELLAN.getDescription())
                .setTargetRecordId(startEnvelopeDTO.getContractId())
                .setTargetRecordType(LogTargetRecordTypeEnum.CONTRACT.name());
        try {
            operationLogFacade.recordOperationLogOLD(operationDetailDTO);
        } catch (Exception e) {
            log.error("记录日志错误:{}", e.getMessage());
        }
    }
}
