package com.navigator.sparrow.signature;

import cn.signit.sdk.SignitClient;
import cn.signit.sdk.SignitException;
import cn.signit.sdk.pojo.*;
import cn.signit.sdk.pojo.request.EnterpriseVerifyRequest;
import cn.signit.sdk.pojo.request.PersonVerifyRequest;
import cn.signit.sdk.pojo.response.EnterpriseVerifyResponse;
import cn.signit.sdk.pojo.response.PersonVerifyResponse;
import cn.signit.sdk.type.*;
import cn.signit.sdk.util.FastjsonDecoder;
import com.alibaba.fastjson.JSON;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.sparrow.service.YqqSignParameterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 11:39
 */
@Slf4j
@Component
public class Verifications {
    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
    @Resource
    private YqqSignParameterService yqqSignParameterService;
    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
    /**
     * 个人实名认证发起
     */
    public void person() {
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
        YqqSignParameterEntity yqqSignParameterEntity =  yqqSignParameterService.queryYqqSignParameterByCompanyId(1);

        String appUrl = yqqSignParameterEntity.getUrl() + "/v1/open/verifications/person";

        SignitClient client = new SignitClient(yqqSignParameterEntity.getAppId(), yqqSignParameterEntity.getAppSecretKey(), appUrl);
        client.setOauthUrl(yqqSignParameterEntity.getUrl() + "/v1/oauth/oauth/token");
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
        // step2: 使用SDK封装实名认证请求
        PersonVerifyRequest request = verifyPersonParam();
        //不需要上传正反面的照片
        //PersonVerifyRequest request = verifyPersonWithEnableEmbeddedMode();
        //System.out.println("request is :\n" + JSON.toJSONString(request, true));

        /// step3: 执行请求,获得响应
        PersonVerifyResponse response = null;
        try {
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
            client.getOauthData(yqqSignParameterEntity.getAppId(), yqqSignParameterEntity.getAppSecretKey(), TokenType.CLIENT_CREDENTIALS, true);
            // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
            response = client.execute(request);
        } catch (SignitException e) {
            ErrorResp errorResp = null;
            if (FastjsonDecoder.isValidJson(e.getMessage())) {
                errorResp = FastjsonDecoder.decodeAsBean(e.getMessage(), ErrorResp.class);
                log.info("\nerror response is:\n" + JSON.toJSONString(errorResp, true));
            } else {
                log.info("\nerror response is:\n" + e.getMessage());
            }
        }
        log.info("\nresponse is:\n" + JSON.toJSONString(response, true));
    }

    /**
     * 企业实名
     *
     * @return
     */
    private EnterpriseVerifyRequest verifyUseAgentWithLeastParams() {
        return EnterpriseVerifyRequest.builder()
                .name("达孚测试")
                .authType(EnterpriseAuthType.AGENT)
                .authModes(PersonAuthType.FACE_AUTH, PersonAuthType.PHONE_AUTH, PersonAuthType.PHONE_FACE_AUTH,
                        PersonAuthType.ZM_AUTH)
                .agent(EnterpriseAgent.builder()
                        .name("万家霖")
                        .idCardNo("******************")
                        .idCardType(IdCardType.SECOND_GENERATION_IDCARD)
                        .phone("***********")
                        .trustInstrumentImage(IdCardImage.builder()
                                .imageName("委托书照片.docx")
                                .imageCode(ImageCode.AGENT_TRUST)
                                .imageData(IdCardImageData.builder()
                                        .url("http://nxw.so/6CYNj")))
                )
                .unifiedSocialCode("91510700595072782J")
                .businessLicenceImage(IdCardImage.builder()
                        .imageName("营业执照演示图片.jpg")
                        .imageCode(ImageCode.BUSINESS_LICENCE)
                        .imageData(IdCardImageData.builder()
                                .url("http://nxw.so/6CYNj")))
                .bankCardInfo(EnterpriseBankCardInfo.builder()
                        .bankCardNo("6228480489080786572")
                        .bankBranch("绵阳市涪城区西科大支行")
                        .bank("中国农业银行"))
                .returnUrl("http://dealers.erp.efunong.com/")
                .acceptDataType(AcceptDataType.URL)
                .customTag("THIS_IS_A_CLIENT_CUSTOM_REUIRED_PARAMETER_hello_world_legal_People,this_is_test")
                .build();
    }

    /**
     * 个人实名
     *
     * @return
     */
    private PersonVerifyRequest verifyPersonParam() {
        return PersonVerifyRequest.builder()
                .name("高斌")
                .idCardNo("330282199505014030")//身份证号码
                .idCardType(IdCardType.SECOND_GENERATION_IDCARD)
                .phone("***********")
                .authModes(PersonAuthType.PHONE_AUTH)
                .idCardImages(IdCardImage.builder()
                                .imageName("法人身份证反面照")
                                .imageCode(ImageCode.ID_CARD_BACK)
                                .imageData(IdCardImageData.builder()
                                        .url("http://5b0988e595225.cdn.sohucs.com/images/20191231/007525d583ed49c1a23f189e7675f1dd.png")),
                        IdCardImage.builder()
                                .imageName("法人身份证正面照")
                                .imageCode(ImageCode.ID_CARD_FRONT)
                                .imageData(IdCardImageData.builder()
                                        .url("http://5b0988e595225.cdn.sohucs.com/images/20191231/007525d583ed49c1a23f189e7675f1dd.png")),
                        IdCardImage.builder()
                                .imageName("手持身份证人像面")
                                .imageCode(ImageCode.PERSON_HANDHELD_ID_CARD_BACK)
                                .imageData(IdCardImageData.builder()
                                        .url("http://5b0988e595225.cdn.sohucs.com/images/20191231/007525d583ed49c1a23f189e7675f1dd.png")))
                .returnUrl("https://www.baidu.com")
                .acceptDataType(AcceptDataType.URL)
                .customTag("THIS_IS_A_CLIENT_CUSTOM_REUIRED_PARAMETER_1_tag_" + new Date().getTime())
                .build();
    }
}
