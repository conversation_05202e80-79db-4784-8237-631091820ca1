package com.navigator.sparrow.facade.impl;

import cn.signit.sdk.pojo.ErrorResp;
import cn.signit.sdk.pojo.webhook.response.WebhookResponse;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import com.navigator.sparrow.signature.CallbackFunction;
import com.navigator.sparrow.signature.StartEnvelope;
import com.navigator.sparrow.signature.VerificationsCheck;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 15:48
 */

@RestController
public class DbtSignatureFacadeImpl implements DbtSignatureFacade {
    @Resource
    private CallbackFunction callbackFunction;
    @Resource
    private StartEnvelope startEnvelope;
    @Resource
    private VerificationsCheck verificationsCheck;
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public void callbackFunction(WebhookResponse webhookResponse) {

        callbackFunction.callbackFunction(webhookResponse);
    }

    @Override
    public ErrorResp envelopesStart(StartEnvelopeDTO startEnvelopeDTO) {

        return startEnvelope.envelopesStart(startEnvelopeDTO);
    }

    @Override
    public boolean revocation(StartEnvelopeDTO startEnvelopeDTO) {
        return startEnvelope.revocation(startEnvelopeDTO);
    }

    @Override
    public boolean hasAuthentication(CheckRequestDTO checkRequestDTO) throws Exception {
        return verificationsCheck.hasAuthentication(checkRequestDTO);
    }
}
