package com.navigator.sparrow.service.impl;

import com.navigator.sparrow.dao.DbtSignatureDao;
import com.navigator.sparrow.pojo.entity.DbtSignatureEntity;
import com.navigator.sparrow.service.IDbtSignatureService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 易企签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
public class DbtSignatureServiceImpl implements IDbtSignatureService {

    @Resource
    private DbtSignatureDao mapper;

    @Override
    public List<DbtSignatureEntity> getDbtSignatureEntityList(String uniqueContractCode) {
        return mapper.getDbtSignatureEntityList(uniqueContractCode);
    }

    @Override
    public DbtSignatureEntity getSignatureEntityBy(String uuid) {
        return mapper.getSignatureEntityBy(uuid);
    }
}
