package com.navigator.sparrow.signature;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.http.OkHttpsUtil;
import com.navigator.customer.pojo.enums.YqqAuthEnum;
import com.navigator.sparrow.dao.DbtSignatureDao;
import com.navigator.sparrow.pojo.dto.AbstractSignatureResponse;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.sparrow.pojo.dto.OauthTokenDTO;
import com.navigator.sparrow.pojo.entity.DbtSignatureEntity;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.sparrow.service.YqqSignParameterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 14:09
 */
@Slf4j
@Component
public class VerificationsCheck {

    @Resource
    private EmployFacade employFacade;
    @Resource
    private DbtSignatureDao mapper;
    @Resource
    private OkHttpsUtil okHttpsUtil;
    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
    @Resource
    private YqqSignParameterService yqqSignParameterService;

    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
    public boolean hasAuthentication(CheckRequestDTO checkRequestDTO) throws Exception {
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
        YqqSignParameterEntity yqqSignParameterEntity = yqqSignParameterService.queryYqqSignParameterByCompanyId(1);
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
        // step2: 使用SDK封装实名认证请求
        CheckRequestDTO request = new CheckRequestDTO();
        request.setPhone(checkRequestDTO.getPhone());
        request.setCustomTag(checkRequestDTO.getCustomTag());
        //将实体类转换成Gson格式
        Gson gson = new GsonBuilder().create();
        String reqParams = gson.toJson(request);
        // step3: 执行请求,获得响应
        //获取token拼接链接地址
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
        String urlJoint = yqqSignParameterEntity.getUrl() + "/v1/oauth/oauth/token?client_id=" + yqqSignParameterEntity.getAppId() + "&client_secret=" + yqqSignParameterEntity.getAppSecretKey() + "&grant_type=client_credentials";
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
        log.info("===============\n" + urlJoint + "\n=============");
        String response = okHttpsUtil.get(urlJoint);
        OauthTokenDTO oauthTokenDTO = FastJsonUtils.getJsonToBean(response, OauthTokenDTO.class);
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
        //拼接实名请求地址
        String appUrl = yqqSignParameterEntity.getUrl() + "/v1/open/verifications/check?access_token=" + oauthTokenDTO.getAccess_token();
        Map<String, Object> headers = new HashMap<>();
        headers.put("X-Signit-App-Id", yqqSignParameterEntity.getAppId());
        // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 end
        //验证用户是否实名
        String verificationsCheck = okHttpsUtil.sendJsonByGetReq(appUrl, reqParams, headers, "UTF-8");
        AbstractSignatureResponse abstractSignatureResponse = FastJsonUtils.getJsonToBean(verificationsCheck, AbstractSignatureResponse.class);//操作记录
        this.saveSignature(appUrl, JSON.toJSONString(response));
        return abstractSignatureResponse.getResult();
        /*if (abstractSignatureResponse.getResult()) {
            return this.updateYQQAuth(checkRequestDTO.getCustomerId());
        } else {
            return false;
        }*/

    }

    //更改客户实名
    private boolean updateYQQAuth(Integer customerId) {
        EmployEntity employEntity = employFacade.queryEmployByCustomerId(customerId);
        employEntity.setYqqAuth(YqqAuthEnum.AUTH.getValue());
        return employFacade.editEmploy(employEntity) > 0;
    }

    //操作记录听
    private void saveSignature(String appUrl, String response) {
        DbtSignatureEntity dbtSignatureEntity = new DbtSignatureEntity()
                .setRequestType("查询客户是否实名")
                .setRequestUrl(appUrl)
                .setRequestDetail(response);
        mapper.save(dbtSignatureEntity);
    }
}
