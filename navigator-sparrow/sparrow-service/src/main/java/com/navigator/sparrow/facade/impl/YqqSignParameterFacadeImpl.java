package com.navigator.sparrow.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.sparrow.facade.YqqSignParameterFacade;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.sparrow.service.YqqSignParameterService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/21
 */
@RestController
public class YqqSignParameterFacadeImpl implements YqqSignParameterFacade {

    @Resource
    public YqqSignParameterService yqqSignParameterService;

    @Override
    public YqqSignParameterEntity queryYqqSignParameterByCompanyId(Integer companyId) {
        return yqqSignParameterService.queryYqqSignParameterByCompanyId(companyId);
    }

    @Override
    public Result saveYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity) {
        return yqqSignParameterService.saveYqqSignParameter(yqqSignParameterEntity);
    }

    @Override
    public Result updateYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity) {
        return yqqSignParameterService.updateYqqSignParameter(yqqSignParameterEntity);
    }

    @Override
    public Result queryYqqSignParameterList() {
        return yqqSignParameterService.queryYqqSignParameterList();
    }
}
