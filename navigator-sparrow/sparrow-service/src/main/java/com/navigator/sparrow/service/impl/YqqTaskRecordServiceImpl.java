package com.navigator.sparrow.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.signit.sdk.pojo.webhook.response.RevokeSignProcess;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.dto.FileItemDTO;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.FileCategoryType;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.sparrow.dao.YqqTaskRecordDao;
import com.navigator.sparrow.dao.YqqTaskRecordProcDao;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordProcEntity;
import com.navigator.sparrow.pojo.enums.DisposeNodeEnum;
import com.navigator.sparrow.pojo.enums.DisposeStaticEnum;
import com.navigator.sparrow.pojo.enums.YqqTaskEnum;
import com.navigator.sparrow.service.IYqqTaskRecordService;
import com.navigator.sparrow.signature.StartEnvelope;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.dto.contractsign.ContractSignYQQUrlDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 10:42
 */
@Slf4j
@Service
public class YqqTaskRecordServiceImpl implements IYqqTaskRecordService {

    @Resource
    private YqqTaskRecordDao yqqTaskRecordDao;
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private YqqTaskRecordProcDao yqqTaskRecordProcDao;
    @Resource
    private AzureBlobUtil azureBlobUtil;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Resource
    private StartEnvelope startEnvelope;

    @Override
    @Async
    public void processYqqTaskDispose(YqqTaskRecordEntity yqqTaskRecordEntity) {

        if (null != yqqTaskRecordEntity.getBizId()) {
            return;
        }

        //获取签章记录
        ContractSignEntity contractSignEntity = contractSignFacade.queryByUUId(yqqTaskRecordEntity.getBizUuCode());
        //判断协议签章类型
        SignatureTypeEnum signatureTypeEnum = SignatureTypeEnum.getByValue(contractSignEntity.getSignatureType());

        switch (signatureTypeEnum) {
            //双签
            case BOTH_SIGNATURE:
                yqqTaskRecordEntity.setSignFileType(FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
                break;
            //LDC单签
            case LDC_ONE_SIGNATURE:
                //单签 non-frame客户已盖章
                if (IsCustomerSignatureEnum.AlREADY_SIGN.getValue() == contractSignEntity.getIsCustomerSignature()) {
                    yqqTaskRecordEntity.setSignFileType(FileCategoryType.CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
                } else {
                    yqqTaskRecordEntity.setSignFileType(FileCategoryType.CONTRACT_PDF_SIGNATURE_LDC.getCode());
                }
                break;
            case OFFLINE:
                break;
            case ONE_SIGNATURE:
                break;
        }

        //存入协议信息
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setBizId(contractSignEntity.getId()));

        //解析json将jsp文件下载并保存到blob中
        yqqFileSaveBlob(yqqTaskRecordEntity);

        //保存文件和协议关系
        bindingSignFileRelation(yqqTaskRecordEntity);

        //LDC单签发送邮件
        callbackAfterProcess(yqqTaskRecordEntity);
    }

    @Override
    public boolean yqqFileSaveBlob(Integer bizId) {
        YqqTaskRecordEntity yqqTaskRecordEntity = yqqTaskRecordDao.getTaskByBizId(bizId, YqqTaskEnum.RESPONSE.getValue());

        return yqqFileSaveBlob(yqqTaskRecordEntity);
    }


    private boolean yqqFileSaveBlob(YqqTaskRecordEntity yqqTaskRecordEntity) {
        //根据业务id获取任务记录

        //FIXME
        if (DisposeNodeEnum.SAVE_DATA.getValue() != yqqTaskRecordEntity.getDisposeNode()) {
            //如果处理完成，返回true，其他的抛异常“业务处理中”
            return false;
        }

        //将任务节点修改为处理中
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.BEING_PROCESSED.getValue()).setDisposeNode(DisposeNodeEnum.FILE_SAVE_BLOB_EXECUTORY.getValue()));

        RevokeSignProcess revokeSignProcess = JSONObject.parseObject(yqqTaskRecordEntity.getData(), RevokeSignProcess.class);

        ContractSignEntity contractSignEntity = contractSignFacade.getContractSignDetailById(yqqTaskRecordEntity.getBizId());

        //拼接签章文件名称
        String fileName = FilePathUtil.genContractSignatureFileName(contractSignEntity.getContractCode(), FileCategoryType.valueOf(yqqTaskRecordEntity.getSignFileType()).getMsg(), FilePathType.PDF.getValue());

        String filePath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.PDF, String.valueOf(contractSignEntity.getId()));
        //文件下载地址
        String url = revokeSignProcess.getSignData().getUrl();

        try {
            //将易企签返回的url下载并上传到blob中
            FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByUrl(url, filePath, fileName, BlobFileContextEnum.PDF.getFileType());

            if (null == fileBaseInfoDTO) {
                //返回位空时回滚状态
                yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.BEING_PROCESSED.getValue()).setDisposeNode(DisposeNodeEnum.SAVE_DATA.getValue()));
                return false;
            }
            //查询文件参数
            YqqTaskRecordProcEntity yqqTaskRecordProcEntity = yqqTaskRecordProcDao.getYqqTaskProcByTaskId(yqqTaskRecordEntity.getId(), DisposeNodeEnum.FILE_SAVE_BLOB.getValue());
            if (null != yqqTaskRecordProcEntity) {

                yqqTaskRecordProcEntity.setData(JSON.toJSONString(fileBaseInfoDTO));

                yqqTaskRecordProcDao.updateById(yqqTaskRecordProcEntity);
            } else {

                //将返回文件参数保存进入数据库中
                yqqTaskRecordProcEntity = new YqqTaskRecordProcEntity().setData(JSON.toJSONString(fileBaseInfoDTO)).setTaskRecordId(yqqTaskRecordEntity.getId()).setDisposeNode(DisposeNodeEnum.FILE_SAVE_BLOB.getValue());

                yqqTaskRecordProcDao.save(yqqTaskRecordProcEntity);
            }


        } catch (Exception e) {
            log.error("文件上传失败" + e.getMessage());

            yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.BEING_PROCESSED.getValue()).setDisposeNode(DisposeNodeEnum.SAVE_DATA.getValue()));

            return false;
        }
        log.info("文件存储完成");
        //保存文件成功后修改任务记录表状态
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeNode(DisposeNodeEnum.FILE_SAVE_BLOB.getValue()));
        return true;
    }


    @Override
    public boolean bindingSignFileRelation(Integer bizId) {
        YqqTaskRecordEntity yqqTaskRecordEntity = yqqTaskRecordDao.getTaskByBizId(bizId, YqqTaskEnum.RESPONSE.getValue());

        return bindingSignFileRelation(yqqTaskRecordEntity);
    }

    public boolean bindingSignFileRelation(YqqTaskRecordEntity yqqTaskRecordEntity) {


        if (DisposeNodeEnum.FILE_SAVE_BLOB.getValue() != yqqTaskRecordEntity.getDisposeNode()) {
            return false;
        }

        //将任务节点修改为处理中
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.BEING_PROCESSED.getValue()).setDisposeNode(DisposeNodeEnum.BINDING_SIGN_FILE_RELATION_EXECUTORY.getValue()));

        //文件参数
        YqqTaskRecordProcEntity yqqTaskRecordProcEntity = yqqTaskRecordProcDao.getYqqTaskProcByTaskId(yqqTaskRecordEntity.getId(), DisposeNodeEnum.FILE_SAVE_BLOB.getValue());

        //参数为空时回滚状态
        if (null == yqqTaskRecordProcEntity || StrUtil.isBlank(yqqTaskRecordProcEntity.getData())) {
            yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.UNTEATED.getValue()).setDisposeNode(DisposeNodeEnum.SAVE_DATA.getValue()));
        }

        //将下载文件路径信息保存在数据库中
        FileBaseInfoDTO fileBaseInfoDTO = JSONObject.parseObject(yqqTaskRecordProcEntity.getData(), FileBaseInfoDTO.class);
        FileItemDTO fileItemDTO = new FileItemDTO().setId(yqqTaskRecordEntity.getBizId()).setCategoryType(yqqTaskRecordEntity.getSignFileType()).setFileBaseInfoDTO(fileBaseInfoDTO);

        //保存文件
        Result result = fileProcessFacade.saveFileItem(fileItemDTO);
        if (!result.isSuccess()) {
            log.info("文件关系保存失败");
            yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeNode(DisposeNodeEnum.FILE_SAVE_BLOB.getValue()));
            return false;
        }
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeNode(DisposeNodeEnum.BINDING_SIGN_FILE_RELATION.getValue()));

        return true;
    }

    @Override
    public boolean callbackAfterProcess(Integer bizId) {
        YqqTaskRecordEntity yqqTaskRecordEntity = yqqTaskRecordDao.getTaskByBizId(bizId, YqqTaskEnum.RESPONSE.getValue());
        return callbackAfterProcess(yqqTaskRecordEntity);
    }

    private boolean callbackAfterProcess(YqqTaskRecordEntity yqqTaskRecordEntity) {

        if (DisposeNodeEnum.BINDING_SIGN_FILE_RELATION.getValue() != yqqTaskRecordEntity.getDisposeNode()) {
            return false;
        }

        //将任务节点修改为处理中
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.BEING_PROCESSED.getValue()).setDisposeNode(DisposeNodeEnum.SEND_MAIL_EXECUTORY.getValue()));

        //根据业务编码查询出业务数据
        ContractSignEntity contractSignEntity = contractSignFacade.getContractSignDetailById(yqqTaskRecordEntity.getBizId());

        //判断协议是否发送邮件
        if (StrUtil.isNotBlank(contractSignEntity.getSignErrorCode()) && ContractSignYQQErrorEnum.ACCOMPLISH.getCode().equals(contractSignEntity.getSignErrorCode())) {

            //签章任务状态改为完成
            yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.COMPLATE.getValue()).setDisposeNode(DisposeNodeEnum.COMPLATE.getValue()));

            return true;
        }

        //单签发送邮件
        if (SignatureTypeEnum.LDC_ONE_SIGNATURE.getValue() == contractSignEntity.getSignatureType() && IsCustomerSignatureEnum.NOT_SIGN.getValue() == contractSignEntity.getIsCustomerSignature()) {
            //发送邮件
            contractSignFacade.sendCustomerNoticeEmail(contractSignEntity.getId());
            contractSignEntity.setSignErrorCode(ContractSignYQQErrorEnum.MAIL_SEND_SUCCEED.getCode())
                    .setSignErrorMessage(ContractSignYQQErrorEnum.MAIL_SEND_SUCCEED.getMessage());
        } else if (SignatureTypeEnum.BOTH_SIGNATURE.getValue() == contractSignEntity.getSignatureType()) {
            contractSignEntity.setStatus(ContractSignStatusEnum.WAIT_CONFIRM.getValue())
                    .setSignatureStatus(SignatureStatusEnum.ALREADY_SIGNATURE.getType());
        }
        //签章任务状态改为完成
        yqqTaskRecordDao.updateById(yqqTaskRecordEntity.setDisposeStatus(DisposeStaticEnum.COMPLATE.getValue()).setDisposeNode(DisposeNodeEnum.COMPLATE.getValue()));

        //将协议状态修改
        updateContractSignYQQ(contractSignEntity.setSignatureUrl(null).setSignErrorCode(ContractSignYQQErrorEnum.ACCOMPLISH.getCode()).setSignErrorMessage(ContractSignYQQErrorEnum.ACCOMPLISH.getMessage()).setIsOnLineSign(IsOnLineSignEnum.OFFLINE.getValue()));

        return true;
    }

    @Override
    public boolean sendYQQRequest(Integer bizId) {
        //根据BizId获取请求任务记录
        YqqTaskRecordEntity yqqTaskRecordEntity = yqqTaskRecordDao.getTaskByBizId(bizId, YqqTaskEnum.REQUEST.getValue());
        //解析发起签章参数
        StartEnvelopeDTO startEnvelopeDTO = JSONObject.parseObject(yqqTaskRecordEntity.getData(), StartEnvelopeDTO.class);
        //发起签章
        startEnvelope.envelopesStart(startEnvelopeDTO);
        return true;
    }

    @Override
    public List<YqqTaskRecordEntity> unfinishedYqqTaskRecord() {
        return yqqTaskRecordDao.unfinishedYqqTaskRecord();
    }

    /**
     * 修改协议状态
     *
     * @param contractSignEntity
     */
    private void updateContractSignYQQ(ContractSignEntity contractSignEntity) {
        //获取发起签章参数
        ContractSignYQQUrlDTO contractSignYQQUrlDTO = BeanConvertUtils.convert(ContractSignYQQUrlDTO.class, contractSignEntity);
        //发起签章

        contractSignFacade.updateContractSignYQQUrl(contractSignYQQUrlDTO);
    }
}
