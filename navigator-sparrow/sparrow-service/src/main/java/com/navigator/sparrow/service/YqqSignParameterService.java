package com.navigator.sparrow.service;

import com.navigator.common.dto.Result;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/29
 */
public interface YqqSignParameterService {

    YqqSignParameterEntity queryYqqSignParameterByCompanyId(Integer companyId);

    Result saveYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity);

    Result updateYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity);

    Result queryYqqSignParameterList();
}
