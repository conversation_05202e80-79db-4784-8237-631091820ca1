package com.navigator.sparrow.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.annotation.Dao;
import com.navigator.sparrow.mapper.YqqTaskRecordProcMapper;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordProcEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 11:55
 */
@Dao
public class YqqTaskRecordProcDao extends ServiceImpl<YqqTaskRecordProcMapper, YqqTaskRecordProcEntity> {


    public YqqTaskRecordProcEntity getYqqTaskProcByTaskId(Integer tackId, Integer node) {
        List<YqqTaskRecordProcEntity> list = this.baseMapper.selectList(Wrappers.<YqqTaskRecordProcEntity>lambdaQuery()
                .eq(YqqTaskRecordProcEntity::getTaskRecordId, tackId)
                .eq(YqqTaskRecordProcEntity::getDisposeNode, node));
        return list.isEmpty() ? null : list.get(0);
    }
}
