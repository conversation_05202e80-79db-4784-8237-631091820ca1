package com.navigator.sparrow.dao;

import cn.signit.sdk.type.WebhookEventType;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.sparrow.mapper.DbtSignatureMapper;
import com.navigator.sparrow.pojo.entity.DbtSignatureEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 17:40
 */
@Dao
public class DbtSignatureDao extends ServiceImpl<DbtSignatureMapper, DbtSignatureEntity> {


    /**
     * 根据唯一编号查询yqq记录信息
     *
     * @param uniqueContractCode
     * @return
     */
    public List<DbtSignatureEntity> getDbtSignatureEntityList(String uniqueContractCode) {
        return this.baseMapper.selectList(Wrappers.<DbtSignatureEntity>lambdaQuery()
                .eq(DbtSignatureEntity::getUniqueContractCode, uniqueContractCode)
                .eq(DbtSignatureEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据唯一id查询信息
     *
     * @param uuid
     * @return
     */
    public DbtSignatureEntity getSignatureEntityBy(String uuid) {
        return this.baseMapper.selectList(Wrappers.<DbtSignatureEntity>lambdaQuery()
                .eq(DbtSignatureEntity::getUniqueContractCode, uuid)
                .eq(DbtSignatureEntity::getEventType, WebhookEventType.PARTICIPANT_HANDLING.getEvent())
                .orderByDesc(DbtSignatureEntity::getId)).get(0);
    }
}
