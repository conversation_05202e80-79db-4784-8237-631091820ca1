package com.navigator.sparrow.service;

import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 10:42
 */
public interface IYqqTaskRecordService {

    /**
     * 易企签回调任务处理(总)
     *
     * @param yqqTaskRecordEntity
     */
    void processYqqTaskDispose(YqqTaskRecordEntity yqqTaskRecordEntity);

    /**
     * 将文件保存进入blob中
     *
     * @param bizId
     * @return
     */
    boolean yqqFileSaveBlob(Integer bizId);

    /**
     * 绑定协议和文件关系
     *
     * @param bizId
     * @return
     */
    boolean bindingSignFileRelation(Integer bizId);


    /**
     * 事件完成(发送邮件)修改协议状态
     *
     * @param bizId
     * @return
     */
    boolean callbackAfterProcess(Integer bizId);


    /**
     * 根据bizId发起签章
     *
     * @param bizId
     * @return
     */
    boolean sendYQQRequest(Integer bizId);


    List<YqqTaskRecordEntity> unfinishedYqqTaskRecord();
}
