package com.navigator.sparrow.facade.impl;

import com.navigator.sparrow.facade.YqqTaskRecordFacade;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.service.IYqqTaskRecordService;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 9:58
 */
@RestController
public class YqqTaskRecordFacadeImpl implements YqqTaskRecordFacade {

    @Resource
    @Lazy
    private IYqqTaskRecordService yqqTaskRecordService;

    @Override
    public boolean yqqFileSaveBlob(Integer bizId) {
        return yqqTaskRecordService.yqqFileSaveBlob(bizId);
    }

    @Override
    public boolean bindingSignFileRelation(Integer bizId) {
        return yqqTaskRecordService.bindingSignFileRelation(bizId);
    }

    @Override
    public boolean callbackAfterProcess(Integer bizId) {
        return yqqTaskRecordService.callbackAfterProcess(bizId);
    }

    @Override
    public boolean sendYQQRequest(Integer bizId) {
        return yqqTaskRecordService.sendYQQRequest(bizId);
    }

    @Override
    public List<YqqTaskRecordEntity> unfinishedYqqTaskRecord() {
        return yqqTaskRecordService.unfinishedYqqTaskRecord();
    }
}
