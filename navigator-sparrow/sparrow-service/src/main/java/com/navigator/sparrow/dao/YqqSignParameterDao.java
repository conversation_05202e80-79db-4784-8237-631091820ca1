package com.navigator.sparrow.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.sparrow.mapper.YqqSignParameterMapper;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/29
 */
@Dao
public class YqqSignParameterDao extends ServiceImpl<YqqSignParameterMapper, YqqSignParameterEntity> {

    public List<YqqSignParameterEntity> queryYqqSignParameterByCompanyId(Integer companyId) {
        return this.baseMapper.selectList(
                Wrappers.<YqqSignParameterEntity>lambdaQuery()
                        .eq(YqqSignParameterEntity::getCompanyId, companyId)
                        .eq(YqqSignParameterEntity::getContext, SpringContextUtil.getEnv())
                        .eq(YqqSignParameterEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }

    public List<YqqSignParameterEntity> queryYqqSignParameterList() {
        return this.baseMapper.selectList(
                Wrappers.<YqqSignParameterEntity>lambdaQuery()
                        .eq(YqqSignParameterEntity::getContext, SpringContextUtil.getEnv())
        );
    }
}
