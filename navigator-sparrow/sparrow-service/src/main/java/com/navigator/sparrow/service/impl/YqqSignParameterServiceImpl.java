package com.navigator.sparrow.service.impl;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.sparrow.dao.YqqSignParameterDao;
import com.navigator.sparrow.pojo.dto.YqqSignParameterDTO;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import com.navigator.sparrow.service.YqqSignParameterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/29
 */
@Service
public class YqqSignParameterServiceImpl implements YqqSignParameterService {

    @Resource
    private YqqSignParameterDao yqqSignParameterDao;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private EmployFacade employFacade;

    @Override
    public YqqSignParameterEntity queryYqqSignParameterByCompanyId(Integer companyId) {

        List<YqqSignParameterEntity> yqqSignParameterEntities = yqqSignParameterDao.queryYqqSignParameterByCompanyId(companyId);
        return yqqSignParameterEntities.isEmpty() ? null : yqqSignParameterEntities.get(0);
    }

    @Override
    public Result saveYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity) {
        YqqSignParameterEntity yqqSignParameter= queryYqqSignParameterByCompanyId(yqqSignParameterEntity.getCompanyId());

        if(null != yqqSignParameter){
            throw new BusinessException("主体已配置易企签属性,请勿重新配置!");
        }

        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        yqqSignParameterEntity
                .setContext(SpringContextUtil.getEnv())
                .setCreatedBy(employEntity.getName())
                .setCreatedAt(new Date())
                .setUpdateBy(employEntity.getName())
                .setUpdatedAt(new Date());
        return Result.success(yqqSignParameterDao.save(yqqSignParameterEntity));
    }

    @Override
    public Result updateYqqSignParameter(YqqSignParameterEntity yqqSignParameterEntity) {
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
        yqqSignParameterEntity
                .setUpdateBy(employEntity.getName())
                .setUpdatedAt(new Date());
        return Result.success(yqqSignParameterDao.updateById(yqqSignParameterEntity));
    }

    @Override
    public Result queryYqqSignParameterList() {

        List<YqqSignParameterEntity> yqqSignParameterEntities = yqqSignParameterDao.queryYqqSignParameterList();

        List<YqqSignParameterDTO> yqqSignParameterDTOS = BeanConvertUtils.convert2List(YqqSignParameterDTO.class, yqqSignParameterEntities);

        yqqSignParameterDTOS.forEach(i -> {

            CompanyEntity companyEntity = companyFacade.queryCompanyById(i.getCompanyId());

            i.setCompanyName(companyEntity.getShortName());

        });

        return Result.success(yqqSignParameterDTOS);
    }
}
