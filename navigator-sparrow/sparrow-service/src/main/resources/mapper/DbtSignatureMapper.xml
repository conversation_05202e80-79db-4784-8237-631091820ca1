<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.sparrow.mapper.DbtSignatureMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.sparrow.pojo.entity.DbtSignatureEntity">
                    <id column="id" property="id"/>
                    <result column="unique_contract_code" property="uniqueContractCode"/>
                    <result column="contract_code" property="contractCode"/>
                    <result column="request_type" property="requestType"/>
                    <result column="request_url" property="requestUrl"/>
                    <result column="request_detail" property="requestDetail"/>
                    <result column="response_detail" property="responseDetail"/>
                    <result column="event_type" property="eventType"/>
                    <result column="signature_url" property="signatureUrl"/>
                    <result column="status" property="status"/>
                    <result column="is_deleted" property="isDeleted"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, unique_contract_code, contract_code, request_type, request_url, request_detail, response_detail, event_type, signature_url, status, is_deleted, created_at, updated_at
        </sql>
</mapper>
