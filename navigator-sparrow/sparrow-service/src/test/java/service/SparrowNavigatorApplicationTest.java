package service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/3 17:44
 */

import com.navigator.sparrow.SparrowNavigatorApplication;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = SparrowNavigatorApplication.class)
@Transactional
@Rollback
public class SparrowNavigatorApplicationTest {

    @MockBean
    private DbtSignatureFacade dbtSignatureFacade;


    /**
     * 回调接口
     */
    @Test
    public void callbackFunctionTest(){


    }

    /**
     * 发起签署接口
     */
    @Test
    public void envelopesStartTest(){


        dbtSignatureFacade.envelopesStart(this.startEnvelopeDTO());
    }


    public StartEnvelopeDTO startEnvelopeDTO(){
        return new StartEnvelopeDTO()
                .setContractSignId(1)
                .setContractCode("1")
                .setUuid("1")
                .setFileId(1)
                .setUrl("1")
                .setTitle("11")
                .setCustomerId(1)
                .setLDCSignature("1")
                .setCustomerSignature("1")
                .setRevokeReason("1")
                ;
    }

    /**
     * 易企签撤销签署流程
     */
    @Test
    public void revocationTest(){
        dbtSignatureFacade.revocation(this.startEnvelopeDTO());

    }

    /**
     * 查询用户是否实名
     */
    @Test
    public void hasAuthenticationTest() throws Exception{

        CheckRequestDTO checkRequestDTO = new CheckRequestDTO()
                .setPhone("")
                .setCustomTag("1")
                .setCustomerId(1);
        dbtSignatureFacade.hasAuthentication(checkRequestDTO);
    }
}
