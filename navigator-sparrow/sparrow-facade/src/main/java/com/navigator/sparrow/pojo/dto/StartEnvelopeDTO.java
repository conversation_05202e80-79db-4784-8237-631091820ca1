package com.navigator.sparrow.pojo.dto;

import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 11:19
 */
@Data
@Accessors(chain = true)
public class StartEnvelopeDTO {

    //合同id
    private Integer contractId;
    //协议id
    private Integer contractSignId = 0;
    //合同编码
    private String contractCode;
    //协议唯一编号
    private String uuid = "";
    //文件类型
    private String extension;
    //文件页数
    private Integer pageNo;
    //文件id
    private Integer fileId;
    //文件地址
    private String url;
    //签署标题(合同文件标题)
    private String title;
    //客户id
    private Integer customerId;
    //登录人id
    private Integer loginId;
    //LDC签章关键字
    private String LDCSignature;
    //客户签章关键字
    private String customerSignature;
    //LDC签章企业名称
    private String LDCSignEnterprise;
    //客户签章企业名称
    private String CustomerSignEnterprise;
    //撤销原因
    private String revokeReason;
    //发起系统
    private Integer system;
    //客户企业名称
    private String customerName;
    //客户是否已签章
    private Integer isCustomerSignature;
    //合同是否是
    private Integer ldcFrame;

    private Boolean isCustomerUseYqq;
    //是否允许集团签章
    private Integer isEnterpriseSign;
    //客户父id
    private Integer parentId;
    //主体id
    private Integer companyId;

    private YqqSignParameterEntity yqqSignParameterEntity;

}
