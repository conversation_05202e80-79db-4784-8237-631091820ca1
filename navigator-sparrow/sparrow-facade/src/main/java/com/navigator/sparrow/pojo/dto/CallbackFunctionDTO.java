package com.navigator.sparrow.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 12:00
 */
@Data
@Accessors(chain = true)
public class CallbackFunctionDTO {
    //合同唯一编码
    private String contractUuId;
    //返回的处理结果
    private String success;
    //返回的签署地址
    private String actionUrl;
    //返回签署者名称
    private String name;
    //返回签署者电话
    private String account;
    //签署公司名称
    private String enterpriseName;
    //回调事件
    private String eventType;
    //签章顺序
    private Integer assignedSequence;
    //回调消息envelope中的Wsid
    private String envelopeWsid;
    //回调消息participant中的Wsid
    private String participantWsid;

}
