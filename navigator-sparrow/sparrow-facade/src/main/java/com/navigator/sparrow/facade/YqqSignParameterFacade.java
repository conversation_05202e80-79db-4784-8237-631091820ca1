package com.navigator.sparrow.facade;

import com.navigator.common.dto.Result;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/21
 */
@Component
@FeignClient(name = "navigator-sparrow-service")
public interface YqqSignParameterFacade {


    /**
     * 根据主体id查询配置
     *
     * @param companyId
     * @return
     */
    @PostMapping("/queryYqqSignParameterByCompanyId")
    YqqSignParameterEntity queryYqqSignParameterByCompanyId(@RequestParam(value = "companyId") Integer companyId);

    /**
     * 新增易企签配置
     *
     * @param yqqSignParameterEntity
     * @return
     */
    @PostMapping("/saveYqqSignParameter")
    Result saveYqqSignParameter(@RequestBody YqqSignParameterEntity yqqSignParameterEntity);

    /**
     * 修改易企签配置
     *
     * @param yqqSignParameterEntity
     * @return
     */
    @PostMapping("/updateYqqSignParameter")
    Result updateYqqSignParameter(@RequestBody YqqSignParameterEntity yqqSignParameterEntity);

    /**
     * 查询易企签配置列表
     *
     * @return
     */
    @PostMapping("/queryYqqSignParameterList")
    Result queryYqqSignParameterList();
}
