package com.navigator.sparrow.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 10:29
 */

@Data
@Accessors(chain = true)
@ApiModel(value="YqqTaskRecordEntity对象", description="")
@TableName("dbz_yqq_task_record")
public class YqqTaskRecordEntity implements Serializable {

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "业务id")
    private Integer bizId;

    @ApiModelProperty(value = "唯一编码")
    private String bizUuCode;

    @ApiModelProperty(value = "易企签回调回调唯一编码invokeNo")
    private String invokeNo;

    @ApiModelProperty(value = "数据类型 1请求 2:接收")
    private Integer type;

    @ApiModelProperty(value = "回调参数")
    private String data;

    @ApiModelProperty(value = "协议文件类型")
    private Integer signFileType;

    @ApiModelProperty(value = "回调类型 对应易企签(WebhookEventType)类型")
    private String webhookEventType;

    @ApiModelProperty(value = "处理状态 1:未处理 2:处理中 3:处理完成")
    private Integer disposeStatus;

    @ApiModelProperty(value = "当前处理到的节点 接收数据并保存.....邮件发送成功")
    private Integer disposeNode;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
