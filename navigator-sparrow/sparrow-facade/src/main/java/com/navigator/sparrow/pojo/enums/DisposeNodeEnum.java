package com.navigator.sparrow.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 13:56
 */

@Getter
@AllArgsConstructor
public enum DisposeNodeEnum {

    INIT(0,"初始数据"),
    SAVE_DATA(1, "已保存数据"),
    FILE_SAVE_BLOB_EXECUTORY(2, "文件存储中"),
    FILE_SAVE_BLOB(3, "将文件添加进入blob中"),
    BINDING_SIGN_FILE_RELATION_EXECUTORY(4, "绑定文件协议关系中"),
    BINDING_SIGN_FILE_RELATION(5, "绑定文件协议关系"),
    SEND_MAIL_EXECUTORY(6, "发送邮件中"),
    SEND_MAIL(7, "发送邮件成功"),
    COMPLATE(8, "完成"),
    ;

    private Integer value;
    private String desc;

    public static DisposeNodeEnum getByValue(int value) {
        for (DisposeNodeEnum typeEnum : DisposeNodeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return DisposeNodeEnum.INIT;
    }
}
