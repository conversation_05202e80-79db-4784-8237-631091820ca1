package com.navigator.sparrow.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 易企签
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value="DbtSignature对象", description="易企签")
@TableName("dbt_signature")
public class DbtSignatureEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "合同唯一编码")
    private String uniqueContractCode;

    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    @ApiModelProperty(value = "请求类型")
    private String requestType;

    @ApiModelProperty(value = "请求地址")
    private String requestUrl;

    @ApiModelProperty(value = "请求返回json")
    private String requestDetail;

    @ApiModelProperty(value = "回调json")
    private String responseDetail;

    @ApiModelProperty(value = "回调消息envelope中的Wsid")
    private String envelopeWsid;

    @ApiModelProperty(value = "回调消息participant中的Wsid")
    private String participantWsid;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "易企签签名地址")
    private String signatureUrl;

    @ApiModelProperty(value = "请求状态")
    private String status;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;


}
