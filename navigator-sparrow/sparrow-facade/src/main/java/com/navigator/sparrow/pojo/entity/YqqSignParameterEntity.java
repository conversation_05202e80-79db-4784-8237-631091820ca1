package com.navigator.sparrow.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/29
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbz_yqq_sign_parameter")
@ApiModel(value="YqqSignParameterEntity", description="")
public class YqqSignParameterEntity {

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "appSecretKey")
    private String appSecretKey;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "状态: 1:启用 0:禁用")
    private Integer status;

    @ApiModelProperty(value = "所属环境")
    private String context;

    @ApiModelProperty(value = "印章名称")
    private String sealName;
    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
    @ApiModelProperty(value = "签章地址")
    private String url;

    @ApiModelProperty(value = "是否使用签署组")
    private String signGroup;
    // BUGFIX：case-1003143 请把YQQ剩余在nacos的配置， url和signGroup，一起放到db里面 Author: wan 2025-04-18 Start
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
